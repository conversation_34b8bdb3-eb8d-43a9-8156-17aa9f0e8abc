<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.burgeon.r3</groupId>
        <artifactId>r3-oc-oms</artifactId>
        <version>3.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>r3-oc-oms-bll</artifactId>
    <version>3.0.0-SNAPSHOT</version>

    <dependencies>
        <!-- raincloud -->
        <dependency>
            <groupId>org.syman</groupId>
            <artifactId>raincloud-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.syman</groupId>
            <artifactId>raincloud-es</artifactId>
        </dependency>
        <dependency>
            <groupId>org.syman</groupId>
            <artifactId>raincloud-jdbc</artifactId>
        </dependency>

        <!-- r3-framework -->
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>R3-Starter-Dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-common-util-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-model-query</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-model-base</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.burgeon.r3</groupId>-->
<!--            <artifactId>r3-mq</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.burgeon.mq</groupId>
            <artifactId>mq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sysapi</artifactId>
        </dependency>

        <!-- r3-project -->
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-retail-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-share-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-store-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-ip-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-basic-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-service-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-ad-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-ps-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-ps-ext-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-cp-ext-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-cp-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-core-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-st-api</artifactId>
        </dependency>
        <!--唯品会服务-->
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-vip-api</artifactId>
        </dependency>
        <!--促销-->
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-pm-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-fi-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-data-basic-bll</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-splitengine</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-channel-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-stocksync-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>oms-vip-frontinterface-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-sourcing-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-hub-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-sg-interface-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>ac-finance-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>ac-finance-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-zuul-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-system-shutdown</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>r3-mq</artifactId>
                    <groupId>com.burgeon.r3</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>kafka-clients</artifactId>
                    <groupId>org.apache.kafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-elasticsearch-ext</artifactId>
            <version>3.0.0-SNAPSHOT</version>
        </dependency>

        <!-- other -->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-kafka-spring-boot-starter</artifactId>
            <version>3.0.0-SNAPSHOT</version>
        </dependency>

        <!-- test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>tlog-core</artifactId>
            <version>1.5.2-SNAPSHOT</version>
        </dependency>

        <!--log filter-->
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>