/*
package com.jackrain.nea.ps.services;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.OmsDynamicApolloConfig;
import com.jackrain.nea.ps.api.result.ProSkuResult;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.PsRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

*/
/**
 * 商品相关服务
 *
 * @author: 易邵峰
 * @since: 2019-01-28
 * create at : 2019-01-28 09:07
 *//*

@Component
@Slf4j
public class ProductService {

    private PsRpcService psRpcService;

    private OmsDynamicApolloConfig dynamicConfig;

    public ProductService() {

    }

    @Autowired
    public ProductService(PsRpcService psRpcService,
                          OmsDynamicApolloConfig dynamicConfig) {
        this.psRpcService = psRpcService;
        this.dynamicConfig = dynamicConfig;
    }

    */
/**
     * 创建ProductSku内容
     *
     * @param skuResult PsCProSkuResult内容
     * @return ProductSku
     *//*

    private ProductSku buildProductSku(PsCProSkuResult skuResult) {
        ProductSku productSku = new ProductSku();
        // PS_C_SKU.ID
        productSku.setId(skuResult.getId());
        // PS_C_PRO.ENAME
        productSku.setName(skuResult.getPsCProEname());
        // PS_C_PRO.ECODE
        productSku.setSku(skuResult.getPsCProEcode());
        // PS_C_SKU.GBCode
        productSku.setBarcode69(skuResult.getGbcode());
        // PS_C_PRO.pricelist
        productSku.setPrice(skuResult.getPricelist());
        // PS_C_SKU.PS_C_SPEC1OBJ_ID
        productSku.setColorId(skuResult.getPsCSpec1objId() == null ? 0L : skuResult.getPsCSpec1objId());
        // PS_C_SPECOBJ.ECODE(spec1objid)
        productSku.setColorCode(skuResult.getClrsEcode());
        // PS_C_SPECOBJ.ENAME(spec1objid)
        productSku.setColorName(skuResult.getClrsEname());
        // PS_C_SPECOBJ.ECODE(spec2objid)
        productSku.setSizeCode(skuResult.getSizesEcode());
        // PS_C_SPECOBJ.ENAME(spec2objid)
        productSku.setSizeName(skuResult.getSizesEname());
        // PS_C_SKU.PS_C_SPEC2OBJ_ID
        productSku.setSizeId(skuResult.getPsCSpec2objId() == null ? 0L : skuResult.getPsCSpec2objId());
        // PS_C_SKU.WareType
        productSku.setSkuType(skuResult.getWareType() == null ? SkuType.NORMAL_PRODUCT : skuResult.getWareType());
        // PS_C_SKU.Weight
        productSku.setWeight(skuResult.getWeight());
        // SkuSpec 描述内容 = 尺寸 + 颜色
        // PS_C_SPECOBJ.ENAME(spec2objid) + PS_C_SPECOBJ.ENAME(spec1objid)
        if (skuResult.getSizesEname() != null && skuResult.getClrsEname() != null) {
            productSku.setSkuSpec(skuResult.getSizesEname() + "," + skuResult.getClrsEname());
        } else {
            productSku.setSkuSpec("");
        }
        // PS_C_PRODIM_ITEM.ECode
        productSku.setMaterialType(skuResult.getMaterieltype());
        // PS_C_SKU.ECode
        productSku.setEcode(skuResult.getSkuEcode());
        // PS_C_PRO.pricelist
        productSku.setPricelist(skuResult.getPricelist());
        // PS_C_PRO.ECODE
        productSku.setProdCode(skuResult.getPsCProEcode());
        //PS_C_PRO.isGroup
        productSku.setIsGroup(skuResult.getIsGroup());
        // PS_C_SKU.PS_C_PRO_ID
        productSku.setProdId(skuResult.getPsCProId());
        // PS_C_SKU.ECODE
        productSku.setSkuEcode(skuResult.getSkuEcode());

        // 组合商品数量
        productSku.setNum(skuResult.getNum());
        // PS_C_SKU.ECODE
        productSku.setSkuEcode(skuResult.getSkuEcode());
        //性别
        productSku.setSex(skuResult.getSex());

        productSku.setCreateDate(new Date());
        //是否是虚拟商品
        productSku.setIsVirtual(skuResult.getIsVirtual());
        productSku.setSkuName(skuResult.getSkuEname());
        productSku.setPsCBrandId(skuResult.getPsCBrandId());
        // 供应类型 0 正常 1.代销轻供 2.寄售轻供
        productSku.setPsCProSupplyType(skuResult.getSupplyType());
        return productSku;
    }


    */
/**
     * 按照SKUCODE查找商品数据
     *
     * @param sku 商品SKU
     * @return 商品数据
     *//*

    public ProductSku selectProductSku(String sku) {
        String redisKey = BllRedisKeyResources.getProductSkuKey(sku);
        CusRedisTemplate<String, List<ProductSku>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
        try {
            List<ProductSku> productSku = objRedisTemplate.opsForValue().get(redisKey);
            if (log.isDebugEnabled()) {
                log.debug("SelectProductSku From Redis Key={}; Sku={};SelectResult={}", redisKey, sku, productSku);
            }
            if (CollectionUtils.isEmpty(productSku) || productSku.get(0) == null) {
                List<String> skuList = new ArrayList<>();
                skuList.add(sku);

                productSku = new ArrayList<>();
                ProSkuResult proSkuResult = psRpcService.selectProdSkuInfoBySku(skuList);
                if (log.isDebugEnabled()) {
                    log.debug("SelectProductSku From RPC Sku={};SelectResult={}", sku, proSkuResult);
                }
                if (proSkuResult != null) {
                    if (CollectionUtils.isNotEmpty(proSkuResult.getProSkuList())) {
                        ProductSku productSku1 =
                                this.buildProductSku(proSkuResult.getProSkuList().get(0));
                        productSku.add(productSku1);
                        RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, productSku);
                    } else {
                        return null;
                    }
                } else {
                    return null;
                }
            }
            return productSku.get(0);
        } catch (Exception ex) {
            log.error("ProductService.selectProductSku", ex);
        }
        return null;
    }






    */
/***
     * 根据skuCode列表，批量查询商品信息（订单导入时需要使用）
     * @param skuList
     * @return
     *//*

    public List<ProductSku>  selectSkuInfoByeCodes(List<String> skuList) {
        List<ProductSku> list = Lists.newArrayList();
        try {
            ProSkuResult proSkuResult = psRpcService.selectProdSkuInfoBySku(skuList);
            if (log.isDebugEnabled()) {
                log.debug("selectSkuInfoByeCodes From RPC SkuList={};SelectResult={}", JSON.toJSONString(skuList), proSkuResult);
            }
            if (proSkuResult != null && CollectionUtils.isNotEmpty(proSkuResult.getProSkuList())) {
                for(PsCProSkuResult psCProSkuResult: proSkuResult.getProSkuList()){
                    ProductSku productSku =
                            this.buildProductSku(psCProSkuResult);
                    list.add(productSku);
                }
            }

        } catch (Exception e) {
            log.error("查询商品列表异常", e);
            return list;
        }
        return list;
    }
}
*/
