package com.jackrain.nea.ps.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.result.PsProductInfo;
import com.jackrain.nea.psext.request.SkuQueryRequest;
import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 孙俊磊
 * @since :  2019-03-26
 * create at:  2019-03-26 17:21
 * 通过商品或者条码的某个字段，获取相关的条码，商品信息
 */
@Component
public class PsGetCommodityInformationService {

    @Autowired
    private PsRpcService psRpcService;

    public List<JSONObject> getStandardList(SkuQueryRequest queryRequest) {
        Map map = psRpcService.querySku(queryRequest);
        if (map != null) {
            String data = JSON.toJSONString(map.get("data"));
            List<JSONObject> jsonArray = JSONArray.parseArray(data, JSONObject.class);
            if (jsonArray != null && jsonArray.size() != 0) {
                List<JSONObject> list = new ArrayList<>();
                for (JSONObject psJson : jsonArray) {
                    JSONObject objectJson = new JSONObject();
                    objectJson.put("GBCODE", psJson.get("GBCODE"));
                    objectJson.put("SKU_ECODE", psJson.get("ECODE"));
                    objectJson.put("SKU_ID", psJson.get("skuId"));
                    objectJson.put("PS_C_PRO_ID", psJson.get("PS_C_PRO_ID"));
                    objectJson.put("PS_C_PRO_ECODE", psJson.get("PS_C_PRO_ECODE"));
                    objectJson.put("PS_C_PRO_ENAME", psJson.get("PS_C_PRO_ENAME"));
                    objectJson.put("SPEC", psJson.get("SPEC"));
                    objectJson.put("PRICELIST", psJson.get("PRICELIST"));
                    objectJson.put("IS_GIFT", psJson.get("IS_GIFT"));
                    objectJson.put("IS_GROUP", psJson.get("IS_GROUP"));
                    objectJson.put("WareType", psJson.get("WareType"));
                    objectJson.put("SIZE_ID", psJson.get("sizeId"));
                    objectJson.put("SIZE_NAME", psJson.get("sizeName"));
                    objectJson.put("SIZE_CODE", psJson.get("sizeCode"));
                    objectJson.put("COLOR_ID", psJson.get("colorId"));
                    objectJson.put("COLOR_NAME", psJson.get("colorName"));
                    objectJson.put("COLOR_CODE", psJson.get("colorCode"));
                    //20190826 吊牌价 和性别
                    objectJson.put("TAG_PRICE", psJson.get("tagPrice"));
                    objectJson.put("SEX", psJson.get("sex"));
                    //体积
                    objectJson.put("VOLUME", psJson.get("BULK"));
                    list.add(objectJson);
                }

                return list;
            }
        }

        return null;
    }

    public List<PsProductInfo> getProductInfo(SkuQueryRequest queryRequest) {
        Map map = psRpcService.querySku(queryRequest);
        if (map != null) {
            String data = JSON.toJSONString(map.get("data"));
            return JSONArray.parseArray(data, PsProductInfo.class);
        } else {
            return null;
        }
    }

}
