package com.jackrain.nea.ps.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.psext.api.PsCSkuWmsItemMappingCmd;
import com.jackrain.nea.psext.common.PsExtConstantsIF;
import com.jackrain.nea.psext.model.table.PsCSkuWmsItemMapping;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * wms商品映射服务
 *
 * <AUTHOR>
 * @since 2023-04-03 19:29
 */
@Slf4j
@Service
public class PsCSkuWmsItemMappingService {
    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCSkuWmsItemMappingCmd psCSkuWmsItemMappingCmd;

    private static final int TIMEOUT = 10 * 60;


    /**
     * 根据wms商品ID查询oms商品编码，查不到返回空
     *
     * @param customerId wms客户ID
     * @param itemId     wms商品ID
     * @return oms商品编码
     */
    public String queryEcodeByItem(String customerId, String itemId) {
        if (StringUtils.isEmpty(customerId) || StringUtils.isEmpty(itemId)) {
            log.warn(LogUtil.format("平台ID和商品编码必填，平台ID：{},WMS商品ID:{}", "PsCSkuWmsItemMappingService.queryEcodeByItem"),
                    customerId, itemId);
            return null;
        }

        PsCSkuWmsItemMapping itemMapping = getByRedis(PsExtConstantsIF.ITEM_REDIS_KEY_PREFIX, customerId, itemId);
        if (Objects.nonNull(itemMapping)) {
            return itemMapping.getPsCSkuEcode();
        }

        ValueHolderV14<PsCSkuWmsItemMapping> valueHolderV14 = psCSkuWmsItemMappingCmd.queryByWmsItem(customerId, itemId);
        if (Objects.nonNull(valueHolderV14) && valueHolderV14.isOK() && Objects.nonNull(valueHolderV14.getData())) {
            put2Redis(PsExtConstantsIF.ITEM_REDIS_KEY_PREFIX, customerId, itemId, valueHolderV14.getData());
            return valueHolderV14.getData().getPsCSkuEcode();
        }

        log.error(LogUtil.format("根据ECODE查询WMS商品映射结果，customerId:{},itemId:{}，结果：{}",
                "PsCSkuWmsItemMappingService.queryEcodeByItem"), customerId, itemId, JSON.toJSONString(valueHolderV14));
        return null;
    }

    /**
     * 根据oms商品编码查询wms商品ID，查不到返回空
     *
     * @param customerId wms客户ID
     * @param skuECode   oms商品编码
     * @return wms商品ID
     */
    public String queryItemByEcode(String customerId, String skuECode) {
        if (StringUtils.isEmpty(customerId) || StringUtils.isEmpty(skuECode)) {
            log.warn(LogUtil.format("平台ID和商品编码必填，平台ID：{},WMS商品ID:{}", "PsCSkuWmsItemMappingService.queryItemByEcode"),
                    customerId, skuECode);
            return null;
        }

        PsCSkuWmsItemMapping itemMapping = getByRedis(PsExtConstantsIF.ECODE_REDIS_KEY_PREFIX, customerId, skuECode);
        if (Objects.nonNull(itemMapping)) {
            return itemMapping.getWmsItemId();
        }

        ValueHolderV14<PsCSkuWmsItemMapping> valueHolderV14 = psCSkuWmsItemMappingCmd.queryByECode(customerId, skuECode);
        if (Objects.nonNull(valueHolderV14) && valueHolderV14.isOK() && Objects.nonNull(valueHolderV14.getData())) {
            put2Redis(PsExtConstantsIF.ECODE_REDIS_KEY_PREFIX, customerId, skuECode, valueHolderV14.getData());
            return valueHolderV14.getData().getWmsItemId();
        }

        log.error(LogUtil.format("根据ECODE查询WMS商品映射结果，customerId:{},skuECode:{}，结果：{}",
                "PsCSkuWmsItemMappingService.queryItemByEcode"), customerId, skuECode, JSON.toJSONString(valueHolderV14));
        return null;
    }

    /**
     * 从缓存获取
     *
     * @param prefix     缓存前缀，见{@link PsExtConstantsIF#ECODE_REDIS_KEY_PREFIX}
     * @param customerId 客户ID
     * @param key        查询关键字
     * @return 结果对象
     */
    private PsCSkuWmsItemMapping getByRedis(String prefix, String customerId, String key) {
        if (StringUtils.isEmpty(customerId) || StringUtils.isEmpty(key)) {
            log.error(LogUtil.format("平台ID和商品编码必填，平台ID：{},WMS商品ID:{}", "PsCSkuWmsItemMappingService.getByRedis"),
                    customerId, key);
            throw new NDSException("平台ID和商品编码必填");
        }

        CusRedisTemplate<String, PsCSkuWmsItemMapping> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
        return redisTemplate.opsForValue().get(prefix + customerId + ":" + key);
    }

    /**
     * @param prefix     缓存前缀，见{@link PsExtConstantsIF#ITEM_REDIS_KEY_PREFIX}
     * @param customerId 客户ID
     * @param key        查询关键字
     * @param entity     结果对象
     */
    private void put2Redis(String prefix, String customerId, String key, PsCSkuWmsItemMapping entity) {
        CusRedisTemplate<String, PsCSkuWmsItemMapping> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
        String redisKey = prefix + customerId + ":" + key;
        redisTemplate.opsForValue().set(redisKey, entity);
        redisTemplate.expire(redisKey, TIMEOUT, TimeUnit.SECONDS);
    }

}
