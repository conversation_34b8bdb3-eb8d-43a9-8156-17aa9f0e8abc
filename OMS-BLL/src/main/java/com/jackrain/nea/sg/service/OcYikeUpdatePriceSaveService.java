package com.jackrain.nea.sg.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBYikeUpdatePriceMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.yike.OcBYikeUpdatePrice;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;


/**
 * 驿客修改价格接口保存接口
 */
@Slf4j
@Component
public class OcYikeUpdatePriceSaveService {

    @Autowired
    private OcBYikeUpdatePriceMapper mapper;

    /**
     * 库存调整单提交（前端页面提交入口）
     *
     * @param session session
     * @return return
     */
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param")));
        ValueHolder valueHolder = ValueHolderUtils.getFailValueHolder("参数不能为空！");

        if (null == param) {
            return valueHolder;
        }

        if (log.isDebugEnabled()) {
            log.debug("start OcYikeUpdatePriceSaveService.execute.param:{}", param.toJSONString());
        }

        Long id = param.getLong(OcCommonConstant.OBJ_ID);
        JSONObject json = param.getJSONObject(OcCommonConstant.FIX_COLUMN);

        if (json == null) {
            return valueHolder;
        }

        JSONObject data = json.getJSONObject(OcElasticSearchIndexResources.OC_B_YIKE_UPDATE_PRICE.toUpperCase());

        User user = session.getUser();
        Date now = new Date();

        if (null == id || id <= 0L) {
            // 走新增逻辑
            Long objId = ModelUtil.getSequence(OcElasticSearchIndexResources.OC_B_YIKE_UPDATE_PRICE);
            OcBYikeUpdatePrice insert = new OcBYikeUpdatePrice();
            insert.setId(objId);

            insert.setOwnerid(user.getId().longValue());
            insert.setOwnerename(user.getEname());
            insert.setOwnername(user.getName());
            insert.setCreationdate(now);
            setUser(insert, user, now);

            insert.setStatus(1);
            insert.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
            setProperties(insert, data);

            mapper.insert(insert);

            ValueHolder result = ValueHolderUtils.getSuccessValueHolder("保存成功");
            HashMap hashMap = new HashMap();

            JSONObject object = new JSONObject();
            object.put("objid", objId);
            object.put("tablename", OcElasticSearchIndexResources.OC_B_YIKE_UPDATE_PRICE);

            hashMap.put("data", object);
            hashMap.put("code", 0);
            result.setData(hashMap);
            return result;
        }

        // 走修改逻辑
        OcBYikeUpdatePrice obj = mapper.selectById(id);

        if (obj.getStatus() == null || obj.getStatus() != 1 || !OcBOrderConst.IS_ACTIVE_YES.equals(obj.getIsactive())) {
            return ValueHolderUtils.getFailValueHolder("仅可对【未提交】的单据进行修改操作！保存失败！");
        }

        obj.setIsactive(OcBOrderConst.IS_ACTIVE_NO);
        setUser(obj, user, now);
        setProperties(obj, data);

        mapper.updateById(obj);

        return ValueHolderUtils.getSuccessValueHolder("保存成功");
    }

    private void setUser(OcBYikeUpdatePrice obj, User user, Date date) {
        obj.setModifieddate(date);
        obj.setModifierid(user.getId().longValue());
        obj.setModifiername(user.getName());
        obj.setModifierename(user.getEname());
    }

    private void setProperties(OcBYikeUpdatePrice obj, JSONObject json) {
        obj.setShopCode(json.getString("SHOP_CODE"));
        obj.setItemNo(json.getString("ITEM_NO"));
        obj.setIsOnSale(json.getString("IS_ON_SALE"));
        obj.setSalePrice(json.getBigDecimal("SALE_PRICE"));
        obj.setDelivType(json.getInteger("DELIV_TYPE"));
        obj.setBarCode(json.getString("BAR_CODE"));
        obj.setItemSalePrice(json.getBigDecimal("ITEM_SALE_PRICE"));
    }
}
