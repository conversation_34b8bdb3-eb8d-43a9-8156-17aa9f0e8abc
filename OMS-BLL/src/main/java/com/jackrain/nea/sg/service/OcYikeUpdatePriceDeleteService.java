package com.jackrain.nea.sg.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.mapper.OcBYikeUpdatePriceMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.yike.OcBYikeUpdatePrice;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * 驿客修改价格接口保存接口
 */
@Slf4j
@Component
public class OcYikeUpdatePriceDeleteService {

    @Autowired
    private OcBYikeUpdatePriceMapper mapper;

    /**
     * 库存调整单提交（前端页面提交入口）
     *
     * @param session session
     * @return return
     */
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param")));
        ValueHolder valueHolder = ValueHolderUtils.getFailValueHolder("参数不能为空！");
        if (null == param) {
            return valueHolder;
        }
        Long id = param.getLong(OcCommonConstant.OBJ_ID);
        if (null == id) {
            return valueHolder;
        }

//        User user = session.getUser();
//
//        if (Objects.isNull(user)) {
//            return valueHolder;
//        }

        OcBYikeUpdatePrice obj = mapper.selectById(id);

        if (obj.getStatus() == null || obj.getStatus() != 1 || !OcBOrderConst.IS_ACTIVE_YES.equals(obj.getIsactive())) {
            return ValueHolderUtils.getFailValueHolder("仅可对【未提交】的单据进行删除操作！删除失败！");
        }

//        obj.setIsactive(OcBOrderConst.IS_ACTIVE_NO);
//
//        obj.setModifierename(user.getEname());
//        obj.setModifiername(user.getName());
//        obj.setModifierid(user.getId().longValue());
//        obj.setModifieddate(new Date());

        mapper.deleteById(id);

        return ValueHolderUtils.getSuccessValueHolder("删除成功");
    }
}
