package com.jackrain.nea.sg.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutNoticesBillSaveAndWMSRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutNoticesBillSaveRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.request.CpShopQueryRequest;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.jingdong.request.JdDecryptRequest;
import com.jackrain.nea.ip.model.jingdong.response.JdDecryptResponse;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoOrderCycleBuyMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBShipinOrderCodeMapper;
import com.jackrain.nea.oc.oms.model.enums.BackflowStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderCycleBuy;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBShipinOrderCode;
import com.jackrain.nea.oc.oms.services.OcBOrderLinkService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.request.o2o.SaleOrderRequest;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.util.SendMQAsyncUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 新增出库通知单
 * 订单传WMS
 */

@Slf4j
@Component
public class SgOutStockNoticeService {

    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderLinkService ocBOrderLinkService;

    @Autowired
    private SendMQAsyncUtils sendMQAsyncUtils;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsWmsTaskService wmsTaskService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private IpBTaobaoOrderCycleBuyMapper ipBTaobaoOrderCycleBuyMapper;
    @Resource
    private OcBShipinOrderCodeMapper ocBShipinOrderCodeMapper;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    /**
     * 新传wms
     *
     * @param user
     */
    public void addOutStockNoticeToWms(User user, List<OcBOrder> ocBOrders, List<OcBOrderItem> orderItemsList) {
        try {
            //***全链路日志【审核】 新增御城河****
            ocBOrderLinkService.addOrderFinkLogsThread(ocBOrders, BackflowStatus.QIMEN_ERP_CHECK.parseValue());
            List<OcBOrderParam> ocBOrderParams = new ArrayList<>();
            Map<Long, List<OcBOrderItem>> map = new HashMap<>();
            for (OcBOrderItem ocBOrderItem : orderItemsList) {
                Long ocBOrderId = ocBOrderItem.getOcBOrderId();
                List<OcBOrderItem> items = null;
                if (!map.containsKey(ocBOrderId)) {
                    items = new ArrayList<>();
                } else {
                    items = map.get(ocBOrderId);
                }
                items.add(ocBOrderItem);
                map.put(ocBOrderId, items);

            }
            List<Long> warehouseIds = ocBOrders.stream()
                    .filter(o -> o != null).map(OcBOrder::getCpCPhyWarehouseId).distinct().collect(Collectors.toList());
            Map<Long, CpCPhyWarehouse> phyWarehouseMap = cpRpcService.rpcQueryCpCPhyWareHouses(warehouseIds);
            if (phyWarehouseMap == null || phyWarehouseMap.size() < 1) {
                for (OcBOrder ocBOrder : ocBOrders) {
                    reSetOrderStatus2Checked(ocBOrder.getId());
                }
                return;
            }
            Map<Long, SaleOrderRequest> posMap = new HashMap<>();
            for (OcBOrder ocBOrder : ocBOrders) {
                CpCPhyWarehouse phyWarehouse = phyWarehouseMap.get(ocBOrder.getCpCPhyWarehouseId());
                List<OcBOrderItem> orderItems = map.get(ocBOrder.getId());
                if (CollectionUtils.isEmpty(orderItems) || phyWarehouse == null) {
                    reSetOrderStatus2Checked(ocBOrder.getId());
                    continue;
                }
                //用来记录实体仓仓库类型 是不是门店
                OcBOrderParam ocBOrderParam = new OcBOrderParam();
                ocBOrderParam.setOcBOrder(ocBOrder);
                ocBOrderParam.setOrderItemList(orderItems);
                ocBOrderParams.add(ocBOrderParam);
            }
            if (CollectionUtils.isNotEmpty(ocBOrderParams)) {
                this.handleParameter(ocBOrderParams, user, posMap);
            }
        } catch (NDSException e) {
            log.error("{} 异常{}", this.getClass().getName(), e);
        } catch (Exception e) {
            throw new NDSException(e);
        }
    }

    /**
     * 还原订单状态,同时还原传wms中间表状态
     *
     * @param id
     */
    private void reSetOrderStatus2Checked(Long id) {
        OcBOrder order = new OcBOrder();
        order.setId(id);
        order.setOrderStatus(OmsOrderStatus.CHECKED.toInteger());
        if (omsOrderService.updateOrderInfo(order)) {
            wmsTaskService.updateOcBToWmsTaskStatus(id, 0);
        }
    }

    /**
     * 已审核直接调用(不需要加锁)
     *
     * @param orderId
     * @param user
     */
    public void addOutStockNotice(Long orderId, User user) {
        try {
            int i = ocBOrderMapper.updateByIdOrderStatus(orderId);
            if (i <= 0) {
                return;
            }
            List<OcBOrderParam> ocBOrderParams = new ArrayList<>();
            OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
            CpCPhyWarehouse phyWarehouse = cpRpcService.queryByWarehouseId(ocBOrder.getCpCPhyWarehouseId());
            if (phyWarehouse == null) {
                reSetOrderStatus2Checked(ocBOrder.getId());
                return;
            }
            List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
            if (CollectionUtils.isEmpty(orderItems)) {
                reSetOrderStatus2Checked(ocBOrder.getId());
                return;
            }
            //用来记录实体仓仓库类型 是不是门店
            OcBOrderParam ocBOrderParam = new OcBOrderParam();
            ocBOrderParam.setOcBOrder(ocBOrder);
            ocBOrderParam.setOrderItemList(orderItems);
            ocBOrderParams.add(ocBOrderParam);
            Map<Long, SaleOrderRequest> posMap = new HashMap<>();
            this.handleParameter(ocBOrderParams, user, posMap);
        } catch (Exception e) {
            reSetOrderStatus2Checked(orderId);
        }
    }
    public boolean handleParameter(List<OcBOrderParam> ocBOrderParams, User user, Map<Long, SaleOrderRequest> posMap) {
        //天猫周期购传planId
        Map<String, IpBTaobaoOrderCycleBuy> orderIdPlanIdMap = tmallPlanId(ocBOrderParams);
        SgBStoOutNoticesBillSaveAndWMSRequest mqResult = new SgBStoOutNoticesBillSaveAndWMSRequest();
        List<SgBStoOutNoticesBillSaveRequest> saveRequestList = new ArrayList<>();
        try {
            List<Long> orderIds = new ArrayList<>(ocBOrderParams.size());
            ocBOrderParams.forEach(x -> {
                OcBOrder ocBOrder = x.getOcBOrder();
                try {
                    if (log.isDebugEnabled()) {
                        log.debug("xxx : {}", JSONObject.toJSONString(x));
                        log.debug("{} 进入新增出库单,订单id:{}", this.getClass().getName(), ocBOrder.getId());
                    }
                    List<OcBOrderItem> orderItemList = x.getOrderItemList();
                    SgBStoOutNoticesBillSaveRequest billSaveRequest = new SgBStoOutNoticesBillSaveRequest();
                    SgBStoOutNoticesBillSaveRequest.SgBStoOutNoticesSaveRequest noticesSaveRequest = new SgBStoOutNoticesBillSaveRequest.SgBStoOutNoticesSaveRequest();
                    //在生成出库通知单时判断零售发货单是否为“京东自流转订单”，若是则在生成的出库通知单默认“是否传WMS”字段值为“否”；
                    if (ocBOrder.getIsJcorder() != null && ocBOrder.getIsJcorder() == 1) {
                        noticesSaveRequest.setIsPassWms(0);
                        noticesSaveRequest.setIsAutoOut(YesNoEnum.Y.getKey());
                        noticesSaveRequest.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
                        noticesSaveRequest.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
                        noticesSaveRequest.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
                    }
                    // 实体仓id
                    noticesSaveRequest.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
                    // 实体仓编码
                    noticesSaveRequest.setCpCPhyWarehouseEcode(ocBOrder.getCpCPhyWarehouseEcode());
                    // 实体仓名称
                    noticesSaveRequest.setCpCPhyWarehouseEname(ocBOrder.getCpCPhyWarehouseEname());
                    noticesSaveRequest.setPayTime(ocBOrder.getPayTime());
                    noticesSaveRequest.setBillDate(ocBOrder.getOrderDate());
                    // 出库类型
                    noticesSaveRequest.setOutType(SgStoreConstantsIF.OUT_TYPE_ELECTRICITY);
                    noticesSaveRequest.setCpCCustomerId(ocBOrder.getCpCCustomerId());
                    noticesSaveRequest.setSourceBillId(ocBOrder.getId());
                    noticesSaveRequest.setSourceBillNo(ocBOrder.getBillNo());
                    noticesSaveRequest.setSourcecode(ocBOrder.getSourceCode());
                    // 收货人信息加密
                    noticesSaveRequest.setOaid(ocBOrder.getOaid());
                    // 单据类型
                    noticesSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
                    noticesSaveRequest.setIsTocOrder(!OmsBusinessTypeUtil.isToBOrder(ocBOrder));
                    //残次/非残次 orderType orderProType赋值
                    setRequestCcValue(ocBOrder, noticesSaveRequest);
                    //斯凯奇 1. 生成出库通知单时，若该订单为JITX订单且为合单，则取零售发货单中
                    //的"JITX发货平台单号"更新至出库通知单中的"平台单号"中
                    if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
                        if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsMerge())) {
                            noticesSaveRequest.setSourcecode(ocBOrder.getJitxMergedDeliverySn());
                        }
                    }
                    noticesSaveRequest.setCpCShopTitle(ocBOrder.getCpCShopTitle());
                    noticesSaveRequest.setCpCShopId(ocBOrder.getCpCShopId()); //店铺id
                    noticesSaveRequest.setReceiverMobile(StringUtils.isNotBlank(ocBOrder.getReceiverMobile()) ? ocBOrder.getReceiverMobile() : ocBOrder.getReceiverPhone());
                    /**
                     *如果是京东平台  零售发货单生成出库通知单 省市区ename 取值
                     */
                    if (PlatFormEnum.JINGDONG.getCode().equals(ocBOrder.getPlatform())) {
                        noticesSaveRequest.setCpCRegionProvinceEname(StringUtils.isNotEmpty(ocBOrder.getPlatformProvince()) ? ocBOrder.getPlatformProvince() : ocBOrder.getCpCRegionProvinceEname());
                        noticesSaveRequest.setCpCRegionCityEname(StringUtils.isNotEmpty(ocBOrder.getPlatformCity()) ? ocBOrder.getPlatformCity() : ocBOrder.getCpCRegionCityEname());
                        noticesSaveRequest.setCpCRegionAreaEname(StringUtils.isNotEmpty(ocBOrder.getPlatformArea()) ? ocBOrder.getPlatformArea() : ocBOrder.getCpCRegionAreaEname());
                    } else {
                        noticesSaveRequest.setCpCRegionProvinceEname(ocBOrder.getCpCRegionProvinceEname());
                        noticesSaveRequest.setCpCRegionCityEname(ocBOrder.getCpCRegionCityEname());
                        noticesSaveRequest.setCpCRegionAreaEname(ocBOrder.getCpCRegionAreaEname());
                        noticesSaveRequest.setCpCRegionTownEname(ocBOrder.getCpCRegionTownEname());
                    }
                    noticesSaveRequest.setCpCRegionProvinceId(ocBOrder.getCpCRegionProvinceId());
                    noticesSaveRequest.setCpCRegionProvinceEcode(ocBOrder.getCpCRegionProvinceEcode());
                    noticesSaveRequest.setCpCRegionCityId(ocBOrder.getCpCRegionCityId());
                    noticesSaveRequest.setCpCRegionCityEcode(ocBOrder.getCpCRegionCityEcode());
                    noticesSaveRequest.setCpCRegionAreaEcode(ocBOrder.getCpCRegionAreaEcode());
                    noticesSaveRequest.setCpCRegionAreaId(ocBOrder.getCpCRegionAreaId());

                    noticesSaveRequest.setReceiverPhone(StringUtils.isNotBlank(ocBOrder.getReceiverPhone()) ? ocBOrder.getReceiverPhone() : ocBOrder.getReceiverMobile());
                    noticesSaveRequest.setReceiverZip(ocBOrder.getReceiverZip());
                    noticesSaveRequest.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
                    noticesSaveRequest.setIsO2oOrder(ocBOrder.getIsO2oOrder());
                    noticesSaveRequest.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
                    noticesSaveRequest.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
                    noticesSaveRequest.setLogisticNumber(ocBOrder.getExpresscode());
                    noticesSaveRequest.setBuyerRemark(ocBOrder.getBuyerMessage());
                    noticesSaveRequest.setSellerRemark(ocBOrder.getSellerMemo());
                    noticesSaveRequest.setRemark(ocBOrder.getSysremark());
                    noticesSaveRequest.setCpCPlatformId(Long.valueOf(ocBOrder.getPlatform())); //平台
                    noticesSaveRequest.setSplitType(ocBOrder.getSplitReason());
                    noticesSaveRequest.setReceiver(ocBOrder.getReceiverName());
                    noticesSaveRequest.setReceiverName(ocBOrder.getReceiverName());
                    noticesSaveRequest.setReceiverAddress(ocBOrder.getReceiverAddress());
//                    if (PlatFormEnum.JINGDONG_DX.getCode().equals(ocBOrder.getPlatform())) {
//                        decryptOaid(ocBOrder, noticesSaveRequest);
//                    }
                    // 用无用字段标记是否门店
                    noticesSaveRequest.setDeliveryMethod(StringUtils.isEmpty(ocBOrder.getDeliveryMethod()) ? SgConstantsIF.SG_STO_OUT_DELIVERY_WAY_DELIVERY : ocBOrder.getDeliveryMethod());
                    // 下单时间
                    noticesSaveRequest.setOrderTime(ocBOrder.getOrderDate());
                    CpCPlatform cpCPlatform = cpRpcService.queryCpCPlatformByCode(ocBOrder.getPlatform() + "");
                    if (cpCPlatform != null) {
                        noticesSaveRequest.setSourcePlatformEcode(cpCPlatform.getRemark());
                        noticesSaveRequest.setSourcePlatformEname(cpCPlatform.getEname());
                    }
                    /*是否明文，传出库通知单到WMS的时候，如果是为【是】则sourcePlatformEcode会被置为【OTHER】，WMS不解密地址*/
                    noticesSaveRequest.setIsPlainAddr(ocBOrder.getIsPlainAddr());
                    try {
                        if (OcBOrderConst.IS_DELIVERY_URGENT.equals(ocBOrder.getIsDeliveryUrgent())) {
                            noticesSaveRequest.setIsDeliveryUrgent(OcBOrderConst.IS_DELIVERY_URGENT);
                        }
                        if (ocBOrder.getLatestDeliveryTime() != null) {
                            noticesSaveRequest.setDeliveryTime(ocBOrder.getLatestDeliveryTime());
                        }
                    } catch (Exception e) {
                        log.warn(LogUtil.format("加急时间错误:{}",
                                "SgOutStockNoticeService.addOutStockNoticeToWms"), ocBOrder.getId());
                    }

                    // 下单平台
                    noticesSaveRequest.setSourcePlatformId(ocBOrder.getPlatform());
                    noticesSaveRequest.setYlPlatId(ocBOrder.getGwSourceGroup());
                    String orderSourcePlatformEcode = ocBOrder.getOrderSourcePlatformEcode();
                    Long disputeId = ocBOrder.getDisputeId();

                    log.info(LogUtil.format("disputeId:{},platform:{}", "orderSourcePlatformEcode"), disputeId, ocBOrder.getPlatform());

                    if (Objects.nonNull(disputeId) && ocBOrder.getPlatform() == 2) {
                        noticesSaveRequest.setSourcecode(String.valueOf(disputeId));
                    } else if (StringUtils.isNotBlank(orderSourcePlatformEcode) && !(PlatFormEnum.CARD_CODE.getCode().equals(ocBOrder.getPlatform()) ||
                            PlatFormEnum.CREATE_CARD_CODE.getCode().equals(ocBOrder.getPlatform()))) {
                        noticesSaveRequest.setSourcecode(orderSourcePlatformEcode);
                    }
                    if (PlatFormEnum.DOU_YIN.getCode().equals(ocBOrder.getPlatform()) && ocBOrder.getOrderType().equals(OrderTypeEnum.EXCHANGE.getVal())) {
                        // 如果是抖音换货单 需要取平台退款单号
                        Long origReturnOrderId = ocBOrder.getOrigReturnOrderId();
                        if (origReturnOrderId != null){
                            // 根据origReturnOrderId查询退换货单
                           OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByid(origReturnOrderId);
                           if (returnOrder != null && StringUtils.isNotEmpty(returnOrder.getReturnId())){
                               noticesSaveRequest.setSourcecode(returnOrder.getReturnId());
                           }
                        }
                    }
                    CpShopQueryRequest shopQueryRequest = new CpShopQueryRequest();
                    List<String> shopList = new ArrayList<>();
                    shopList.add(ocBOrder.getCpCShopEcode());
                    shopQueryRequest.setShopCodes(shopList);
                    //审核人id
                    noticesSaveRequest.setOwnerid(ocBOrder.getAuditId());
                    //审核人姓名
                    noticesSaveRequest.setOwnername(ocBOrder.getAuditName());
                    //用户昵称user_nick赋值给出库通知单的买家昵称
                    noticesSaveRequest.setUserNick(ocBOrder.getUserNick());
                    //20230511重楼
                    noticesSaveRequest.setDateRange(ocBOrder.getStCCustomLabelEname());
                    noticesSaveRequest.setCarpoolNo(ocBOrder.getCarpoolNo());
                    //天猫周期购传planId
                    if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsCycle())) {
                        IpBTaobaoOrderCycleBuy ipBTaobaoOrderCycleBuy = orderIdPlanIdMap.get(ocBOrder.getTid() + ocBOrder.getCurrentCycleNumber());
                        if (!Objects.isNull(ipBTaobaoOrderCycleBuy) && StringUtils.isNotBlank(ipBTaobaoOrderCycleBuy.getPlanId())) {
                            noticesSaveRequest.setPlanId(ipBTaobaoOrderCycleBuy.getPlanId());
                        }
                    }
                    //视频号订单查询解密编码
                    if (PlatFormEnum.SHIPH.getCode().equals(ocBOrder.getPlatform()) ||
                            (StringUtils.isNotEmpty(ocBOrder.getGwSourceGroup()) && PlatFormEnum.SHIPH.getCode().toString().equals(ocBOrder.getGwSourceGroup()))
                    ) {
                        OcBShipinOrderCode shipinOrderCode = ocBShipinOrderCodeMapper.selectOne(new LambdaQueryWrapper<OcBShipinOrderCode>()
                                .eq(OcBShipinOrderCode::getTid, ocBOrder.getTid())
                                .eq(OcBShipinOrderCode::getIsactive, YesNoEnum.Y.getKey()).last("limit 1"));
                        log.info(LogUtil.format("OcBShipinOrderCode billNo:{},shipinOrderCode:{}",
                                "OcBShipinOrderCode"), ocBOrder.getBillNo(), JSONObject.toJSONString(shipinOrderCode));
                        if (shipinOrderCode != null) {
                            noticesSaveRequest.setEwaybillOrderCode(shipinOrderCode.getEwaybillOrderCode());
                            noticesSaveRequest.setEwaybillOrderAppid(shipinOrderCode.getEwaybillOrderAppid());
                        }
                    } else if (PlatFormEnum.YOUZAN.getCode().equals(ocBOrder.getPlatform())) {
                        OcBShipinOrderCode shipinOrderCode = ocBShipinOrderCodeMapper.selectOne(new LambdaQueryWrapper<OcBShipinOrderCode>()
                                .eq(OcBShipinOrderCode::getTid, ocBOrder.getTid())
                                .eq(OcBShipinOrderCode::getIsactive, YesNoEnum.Y.getKey()).last("limit 1"));
                        if (shipinOrderCode != null) {
                            noticesSaveRequest.setEwaybillOrderAppid(shipinOrderCode.getEwaybillOrderAppid());
                        }
                    }
                    List<SgBStoOutNoticesBillSaveRequest.SgBStoOutNoticesItemSaveRequest> outNoticesItemRequests = new ArrayList<>();
                    orderItemList.forEach(p -> {
                        SgBStoOutNoticesBillSaveRequest.SgBStoOutNoticesItemSaveRequest noticesItem = new SgBStoOutNoticesBillSaveRequest.SgBStoOutNoticesItemSaveRequest();
                        noticesItem.setSourceBillItemId(p.getId());
                        noticesItem.setQty(p.getQty());
                        noticesItem.setPsCProId(p.getPsCProId()); //"商品ID"
                        noticesItem.setPsCProEcode(p.getPsCProEcode()); //"商品编码"
                        noticesItem.setPsCProEname(p.getPsCProEname()); //"商品名称"
                        noticesItem.setPsCSkuId(p.getPsCSkuId()); //"skuID"
                        noticesItem.setPsCSkuEcode(p.getPsCSkuEcode()); //"sku编码"
                        noticesItem.setPsCSpec1Id(p.getPsCClrId()); //"规格1为颜色"
                        noticesItem.setPsCSpec1Ecode(p.getPsCClrEcode());
                        noticesItem.setPsCSpec1Ename(p.getPsCClrEname());
                        noticesItem.setPsCSpec2Id(p.getPsCSizeId());   //规格2为尺寸"
                        noticesItem.setPsCSpec2Ecode(p.getPsCSizeEcode());
                        noticesItem.setPsCSpec2Ename(p.getPsCSizeEname());
                        noticesItem.setGbcode(p.getBarcode()); //国标码
                        // 实际成交价格
                        noticesItem.setPriceActual(p.getPriceActual());
                        noticesItem.setRemark(p.getReserveVarchar02());
                        noticesItem.setOrderLabel(p.getOrderLabel());
                        noticesItem.setLabelingRequirements(p.getLabelingRequirements());
                        outNoticesItemRequests.add(noticesItem);
                    });
                    billSaveRequest.setOutNoticesSaveRequest(noticesSaveRequest);
                    billSaveRequest.setOutNoticesItemSaveRequests(outNoticesItemRequests);
                    saveRequestList.add(billSaveRequest);
                    orderIds.add(ocBOrder.getId());

                } catch (Exception e) {
                    log.error(LogUtil.format("订单传wms组装数据异常:{}", "订单传wms组装数据异常", ocBOrder.getId()), Throwables.getStackTraceAsString(e));
                    reSetOrderStatus2Checked(ocBOrder.getId());
                }
            });
            mqResult.setLoginUser(user);
            mqResult.setNoticesBills(saveRequestList);
            if (log.isDebugEnabled()) {
                log.debug(" 调用出库通知单入参数数据 {}", JSON.toJSONString(mqResult));
            }
            this.sendMQ(mqResult, orderIds);
            return true;
        } catch (Exception e) {
            log.error(LogUtil.format("调用出库通知单并传WMS失败111", "调用出库通知单并传WMS失败111"), Throwables.getStackTraceAsString(e));
            for (OcBOrderParam ocBOrderParam : ocBOrderParams) {
                Long id = ocBOrderParam.getOcBOrder().getId();
                reSetOrderStatus2Checked(id);
            }
            throw new NDSException("调用出库通知单并传WMS失败");
        }
    }

    /**
     * tob/tob-残次/正常 orderType&orderProType set value
     *
     * @param ocBOrder
     * @param noticesSaveRequest
     */
    private void setRequestCcValue(OcBOrder ocBOrder, SgBStoOutNoticesBillSaveRequest.SgBStoOutNoticesSaveRequest noticesSaveRequest) {
        if (OmsBusinessTypeUtil.isToBOrder(ocBOrder)) {
            if (OrderSaleProductAttrEnum.isToBCC(ocBOrder.getSaleProductAttr())) {
                // tob残次销售
                noticesSaveRequest.setOrderProType(0);
                noticesSaveRequest.setOrderType("CCCK");
            } else {
                // tob正常销售
                noticesSaveRequest.setOrderProType(1);
                noticesSaveRequest.setOrderType("B2BCK");
            }
        } else {
            if (OmsOrderUtil.isToCCcOrder(ocBOrder)) {
                // toc残次销售
                noticesSaveRequest.setOrderProType(0);
                noticesSaveRequest.setOrderType("CCXS");
            } else {
                // toc正常销售
                noticesSaveRequest.setOrderProType(1);
                noticesSaveRequest.setOrderType("JYCK");
            }
        }
    }

    /**
     * 计算-最晚发货时间
     * 1、若订单有主表预计发货时间或明细预计发货时间，优先取主表预计发货时间
     * 2、若无主表无预计发货时间，明细表有预计发货时间，则过滤明细中为空的预计发货时间后，取最早的时间
     * 3. 若主表和订单表中都没有则“最晚发货时间”取支付时间+店铺档案 平台时效（平台时效为空则默认按24处理）
     */
//    public Date generateDeliveryTime(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList) {
//        if (Objects.nonNull(ocBOrder.getEstimateConTime())) {
//            return ocBOrder.getEstimateConTime();
//        }
//
//        Optional<Date> dateOptional = ListUtils.emptyIfNull(orderItemList).stream()
//                .map(OcBOrderItem::getEstimateConTime)
//                .filter(Objects::nonNull)
//                .min(Date::compareTo);
//        if (dateOptional.isPresent()) {
//            return dateOptional.get();
//        }
//
//        Date date = ocBOrder.getPayTime();
//        if (Objects.isNull(ocBOrder.getPayTime())) {
//            date = ocBOrder.getCreationdate();
//        }
//
//        int defalutTimeoutPlate = 24;
//        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
//        if (Objects.nonNull(cpShop) && Objects.nonNull(cpShop.getTimeoutPlate())) {
//            defalutTimeoutPlate = cpShop.getTimeoutPlate();
//        }
//        return DateUtils.addHours(date, defalutTimeoutPlate);
//    }

    /**
     * 天猫周期购订单，查询对应的planId
     *
     * @param ocBOrderParams
     * @return
     */
    private Map<String, IpBTaobaoOrderCycleBuy> tmallPlanId(List<OcBOrderParam> ocBOrderParams) {
        Map<String, IpBTaobaoOrderCycleBuy> orderIdPlanIdMap = Maps.newHashMap();

        if (CollectionUtils.isEmpty(ocBOrderParams)) {
            return orderIdPlanIdMap;
        }

        Set<String> orderNos = Sets.newHashSet();
        for (OcBOrderParam ocBOrderParam : ocBOrderParams) {
            OcBOrder ocBOrder = ocBOrderParam.getOcBOrder();
            if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsCycle())) {
                orderNos.add(ocBOrder.getTid());
            }
        }

        if (CollectionUtils.isEmpty(orderNos)) {
            //无周期购类型订单
            return orderIdPlanIdMap;
        }

        List<IpBTaobaoOrderCycleBuy> ipBTaobaoOrderCycleBuys = ipBTaobaoOrderCycleBuyMapper.selectCycleBuyByOrderNos(Lists.newArrayList(orderNos));
        if (CollectionUtils.isEmpty(ipBTaobaoOrderCycleBuys)) {
            return orderIdPlanIdMap;
        }

        orderIdPlanIdMap = ipBTaobaoOrderCycleBuys.stream().collect(Collectors.toMap(x -> x.getOrderId() + x.getCurrPhase(), x -> x, (a, b) -> a));
        return orderIdPlanIdMap;
    }


    private void sendMQ(SgBStoOutNoticesBillSaveAndWMSRequest mqResult, List<Long> orderIds) {
        if (mqResult != null) {
            mqResult.setRequestTime(System.currentTimeMillis());
            String jsonValue = JSONObject.toJSONString(mqResult);
            String messageId = null;
            //获取Topic
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            String topic = config.getProperty("r3.oc.oms.tosgwms.mq.topic", "BJ_DEV_R3_SG_CALLBACK");
            String topic = "R3_SG_CALLBACK";
            String uuid = UUID.randomUUID().toString();
            sendMQAsyncUtils.sendDelayMessage("default", "OMS出库通知SG", jsonValue, topic,
                    "oms_to_sg_out_notices_to_wms", uuid, 2L, ids -> {
                        for (Long id : ids) {
                            reSetOrderStatus2Checked(id);
                        }
                    }, orderIds);

        }
    }


    /**
     * 订单传wms补偿任务
     *
     * @param user
     * @param list
     */
    public void wmsCompensate(User user, List<Long> list) {
        try {
            List<OcBOrderParam> ocBOrderParams = new ArrayList<>();
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsListAndOrderStatus(list);

            List<Long> warehouseIds = ocBOrders.stream()
                    .filter(o -> o != null).map(OcBOrder::getCpCPhyWarehouseId).collect(Collectors.toList());
            Map<Long, CpCPhyWarehouse> phyWarehouseMap = cpRpcService.rpcQueryCpCPhyWareHouses(warehouseIds);
            if (phyWarehouseMap == null || phyWarehouseMap.size() < 1) {
                return;
            }
            Map<Long, SaleOrderRequest> posMap = new HashMap<>();
            for (OcBOrder ocBOrder : ocBOrders) {
                //判断审核时间是否小于当前时间一个小时
                Date auditTime = ocBOrder.getAuditTime();
                if (auditTime == null) {
                    continue;
                }
                Long hourDate = 60 * 1000 * 60L;
                long endDate = System.currentTimeMillis() - hourDate;
                long time = auditTime.getTime();
                if (time > endDate) {
                    continue;
                }
                CpCPhyWarehouse phyWarehouse = phyWarehouseMap.get(ocBOrder.getCpCPhyWarehouseId());
                if (phyWarehouse == null) {
                    continue;
                }
                List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
                if (CollectionUtils.isEmpty(orderItems)) {
                    continue;
                }
                //用来记录实体仓仓库类型 是不是门店
                OcBOrderParam ocBOrderParam = new OcBOrderParam();
                ocBOrderParam.setOcBOrder(ocBOrder);
                ocBOrderParam.setOrderItemList(orderItems);
                ocBOrderParams.add(ocBOrderParam);
            }
            this.handleParameter(ocBOrderParams, user, posMap);
        } catch (Exception e) {
            log.error(LogUtil.format("查询符合条件的数据异常", "查询符合条件的数据异常"), Throwables.getStackTraceAsString(e));
        }

    }


   /* private void convertOrder2SgONBill(OcBOrderParam param, Map<Long, CpCPlatform> map, SgPhyOutNoticesSaveRequest notice) {

        OcBOrder order = param.getOcBOrder();
        BigDecimal productAmt = initBigDecimal(order.getProductAmt());
        BigDecimal orderAmt = initBigDecimal(order.getOrderAmt());
        BigDecimal disAmt = orderAmt.subtract(productAmt);

        // 总平台销售金额:
        notice.setTotPlatformSaleAmt(String.valueOf(productAmt));
        // 折扣金额 :  折扣金额	sum(成交金额)- sum(平台售价)
        notice.setTotDiscountAmt(String.valueOf(disAmt));
        // 支付方式：0-线上支付1-线下支付,	支付方式	固定‘线上支付’
        notice.setPaymethod("0");
        // 店铺编码
        notice.setCpCShopEcode(order.getCpCShopEcode());
        // 来源渠道代码	根据店铺关联查找来源平台，取来源平台的平台编码
        notice.setCpCOrgEcode(getPlatFormCode(map, order.getPlatform()));
        notice.setCpCOrgEname(getPlatFormName(map, order.getPlatform()));
        // 下单门店编码: 下单门店代码	下单门店代码	同城购给平台分配的，非同城购的给默认值default
        int i = initInteger(order.getIsSameCityPurchase());
        String buyOrderShopCode = order.getDeliveryStoreCode();
        if (i == 0) {
            buyOrderShopCode = "default";
        }
        notice.setStoreEcode(buyOrderShopCode);
        // 结算代码: 	结算代码	固定空
        notice.setSettleEcode("");
        //  制单人  固定空
        notice.setCreater("");
        // 制单日期   固定空
        notice.setCreationDate("");
        // 备注  固定空
        notice.setNote("");
        // 快递单号  固定空
        notice.setShippingCode("");
        // 店员代码  固定空
        notice.setSalerEmployeeNo("");
        // 退单  固定false
        notice.setTh_act("false");
        // 来源系统 固定R
        notice.setSystem("R");

        // 扩展属性
        notice.setExtendProps(generateJsonProps(param));

        // 是否完美履约
        notice.setIsPerfecet(getIsPerfectP(order));

        // saleOrderRequest.setCustomerid("");

    }*/


   /* private void convertItem42SgONBill(OcBOrderItem p, SgPhyOutNoticesItemSaveRequest noticesItem) {

        BigDecimal price = initBigDecimal(p.getPrice());
        BigDecimal qty = initBigDecimal(p.getQty());
        BigDecimal realAmt = initBigDecimal(p.getRealAmt());
        BigDecimal referenceAmt = price.multiply(qty);

        // 平台售价
        noticesItem.setPlatformPrice(String.valueOf(p.getPrice()));

        // 折扣金额: 成交金额-平台售价*数量
        BigDecimal disAmt = realAmt.subtract(referenceAmt);
        noticesItem.setDiscountAmt(String.valueOf(disAmt));

        //  参考金额 : 平台售价*数量
        noticesItem.setReferenceAmt(String.valueOf(referenceAmt));

        // 参考价: 平台售价
        noticesItem.setReferencePrice(String.valueOf(p.getPrice()));

        // 状态: 状态	固定0
        noticesItem.setStatus("0");

        // 尺码代码
        noticesItem.setSizeCode(p.getPsCSizeEcode());

        // 颜色代码
        noticesItem.setColorCode(p.getPsCClrEcode());

    }*/

   /* private JSONObject generateJsonProps(OcBOrderParam param) {
        OcBOrder order = param.getOcBOrder();
        JSONObject jsn = new JSONObject();
        jsn.put("is_perfect_p", getIsPerfectP(order));
        jsn.put("tid", order.getTid());
        List<OcBOrderItem> orderItemList = param.getOrderItemList();

        boolean isFind = false;
        for (OcBOrderItem item : orderItemList) {
            String anchorId = item.getAnchorId();
            if (StringUtils.isNotBlank(anchorId)) {
                jsn.put("anchor_id", item.getAnchorId());
                jsn.put("anchor_name", item.getAnchorName());
                isFind = true;
                break;
            }
        }
        if (!isFind) {
            jsn.put("anchor_id", "");
            jsn.put("anchor_name", "");
        }
        return jsn;
    }*/

    private int initInteger(Integer integer) {
        return integer == null ? 0 : integer;
    }

    private BigDecimal initBigDecimal(BigDecimal decimal) {
        return decimal == null ? BigDecimal.ZERO : decimal;
    }

    private int getIsPerfectP(OcBOrder order) {
        int isSameCity = order.getIsSameCityPurchase() == null ? 0 : order.getIsSameCityPurchase();
        return isSameCity == 1 ? 1 : 0;
    }

    /***
     * pos管控仓
     * @param order
     * @param warehouse
     * @return
     */
    private boolean isToPos(OcBOrder order, CpCPhyWarehouse warehouse) {
        String isPos = warehouse.getIsPos();
        return StringUtils.equals("1", isPos);

    }


    /**
     * 平台编码
     *
     * @param map
     * @return
     */
  /*  private String getPlatFormCode(Map<Long, CpCPlatform> map, Integer platId) {

        if (map == null) {
            return null;
        }
        if (platId == null) {
            return null;
        }
        CpCPlatform platform = map.get(Long.valueOf(platId));
        if (platform == null) {
            return null;
        }
        return platform.getEcode();
    }*/

    /**
     * 平台名称
     *
     * @param map
     * @param platId
     * @return
     */
    private String getPlatFormName(Map<Long, CpCPlatform> map, Integer platId) {

        if (map == null) {
            return null;
        }
        if (platId == null) {
            return null;
        }
        CpCPlatform platform = map.get(Long.valueOf(platId));
        if (platform == null) {
            return null;
        }
        return platform.getEname();
    }

    /**
     * 查询所有平台信息
     *
     * @return id, 平台信息
     */
    private Map<Long, CpCPlatform> rpcQueryPlatForm() {

        try {
            List<CpCPlatform> cpCPlatforms = cpRpcService.queryPlatform(null);
            if (cpCPlatforms == null) {
                return null;
            }
            return cpCPlatforms.stream().collect(Collectors.toMap(CpCPlatform::getId, o -> o));
        } catch (Exception ex) {
            log.error(LogUtil.format("组装平台信息异常", "组装平台信息异常"), Throwables.getStackTraceAsString(ex));
            return null;
        }

    }

    private void decryptOaid(OcBOrder ocBOrder, SgBStoOutNoticesBillSaveRequest.SgBStoOutNoticesSaveRequest noticesSaveRequest) {
        log.info("京东供销收货人、收货人地址解密");
        String name = "name";
        String addressDetail = "address_detail";
        JdDecryptRequest jdDecryptRequest = new JdDecryptRequest();
        jdDecryptRequest.setPlatform(ocBOrder.getPlatform().toString());
        jdDecryptRequest.setSeller_nick(ocBOrder.getCpCShopSellerNick());
        List<JdDecryptRequest.JdDecryptData> queryList = new ArrayList<>();
        //收货人
        JdDecryptRequest.JdDecryptData jdDecryptNameData = new JdDecryptRequest.JdDecryptData();
        jdDecryptNameData.setData_type(name);
        jdDecryptNameData.setEncrypted_data(noticesSaveRequest.getReceiver());
        jdDecryptNameData.setTid(ocBOrder.getTid());
        queryList.add(jdDecryptNameData);
        //收货地址
        JdDecryptRequest.JdDecryptData jdDecryptAddressData = new JdDecryptRequest.JdDecryptData();
        jdDecryptAddressData.setData_type(addressDetail);
        jdDecryptAddressData.setEncrypted_data(noticesSaveRequest.getReceiverAddress());
        jdDecryptAddressData.setTid(ocBOrder.getTid());
        queryList.add(jdDecryptAddressData);
        jdDecryptRequest.setQuery_list(queryList);
        ValueHolderV14<List<JdDecryptResponse>> valueHolderV14 = ipRpcService.jdGxDecrypt(jdDecryptRequest);
        if (!valueHolderV14.isOK()) {
            throw new NDSException("调用云枢纽解密接口失败!");
        }
        List<JdDecryptResponse> jdDecryptResponses = valueHolderV14.getData();
        for (JdDecryptResponse jdDecryptResponse : jdDecryptResponses) {
            if (name.equals(jdDecryptResponse.getDataType())) {
                if (String.valueOf(ResultCode.SUCCESS).equals(jdDecryptResponse.getCode())) {
                    noticesSaveRequest.setReceiver(jdDecryptResponse.getEncryptedData());
                    noticesSaveRequest.setReceiverName(jdDecryptResponse.getEncryptedData());
                } else {
                    throw new NDSException("调用云枢纽解密收货人姓名失败!失败原因:" + jdDecryptResponse.getMsg());
                }
            }
            if (addressDetail.equals(jdDecryptResponse.getDataType())) {
                if (String.valueOf(ResultCode.SUCCESS).equals(jdDecryptResponse.getCode())) {
                    noticesSaveRequest.setReceiverAddress(jdDecryptResponse.getEncryptedData());

                } else {
                    throw new NDSException("调用云枢纽解密收货人地址失败!失败原因:" + jdDecryptResponse.getMsg());
                }
            }
        }
        log.info(LogUtil.format("noticesSaveRequest={}", "SgOutStockNoticeService.decryptOaid"),
                JSONObject.toJSONString(noticesSaveRequest));
    }
}
