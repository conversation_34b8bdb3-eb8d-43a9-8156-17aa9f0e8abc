package com.jackrain.nea.sg.service;

import cn.hutool.core.util.ObjectUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCPreOccupyProvincePriority;
import com.jackrain.nea.st.model.table.StCPreOccupyWarehousePriority;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName OmsPreOccupyService
 * @Description 订单预寻源匹配
 * <AUTHOR>
 * @Date 2025/3/4 09:03
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsPreOccupyService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private StRpcService stRpcService;

    public boolean preOccupy(Long orderId) {
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
        if (ObjectUtil.isNull(ocBOrder)) {
            return false;
        }
        String cpCRegionProvinceEcode = ocBOrder.getCpCRegionProvinceEcode();
        if (StringUtils.isBlank(cpCRegionProvinceEcode)) {
            return false;
        }
        StCPreOccupyProvincePriority provincePriority = stRpcService.queryByProvinceEcode(cpCRegionProvinceEcode);
        if (ObjectUtil.isNull(provincePriority)) {
            return false;
        }
        String cpCPhyWarehouseEcode = provincePriority.getCpCPhyWarehouseEcode();
        StCPreOccupyWarehousePriority warehousePriority = stRpcService.queryByWarehouseEcode(cpCPhyWarehouseEcode);
        if (ObjectUtil.isNull(warehousePriority)) {
            return false;
        }
        String provinceCode = warehousePriority.getCpCProvinceCode();
        // 判断cpCRegionProvinceEcode与provinceCode是否一致
        return cpCRegionProvinceEcode.equals(provinceCode);
    }
}
