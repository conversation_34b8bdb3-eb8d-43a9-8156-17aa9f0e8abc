package com.jackrain.nea.sg.service;

import com.alibaba.fastjson.JSON;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsPhyStorageBillOutRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsPhyStorageOutRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.OcbCancelOrderMergeService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.UpdateOrderInfoService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.st.model.request.StStockPriorityRequest;
import com.jackrain.nea.st.service.OmsQueryWareHouseService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-07-02 10:19
 */
@Slf4j
@Component
public class AddAndVoidStockListService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OmsQueryWareHouseService omsQueryWareHouseService;

    @Autowired
    private OmsSyncStockStrategyService syncStockStrategyService;

    @Autowired
    OmsOrderLogService omsOrderLogService;

    @Autowired
    private UpdateOrderInfoService updateOrderInfoService;

    @Autowired
    private OcbCancelOrderMergeService cancelOrderMergeService;

    /**
     * 合单作废新增逻辑发货单
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void mergeAddAndVoidStock(List<OcBOrder> ocBOrderList, OcBOrderRelation ocBOrderRelation, String lgType, User user) {

        try {
            List<Long> ids = ocBOrderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
            if (ocBOrderMapper.updateList(ids) != ids.size()) {
                throw new NDSException("合并订单作废原单失败");
            }
            try {
                for (OcBOrder order : ocBOrderList) {
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(),
                            lgType + "订单合并作废", "", "", user);
                }
            } catch (Exception e) {
                log.error(LogUtil.format("合并原单作废新增日志异常", "合并原单作废新增日志异常"), Throwables.getStackTraceAsString(e));
            }
//            SgSendVoidSaveHandleRequest request = getVoid(ocBOrderList, ocBOrderRelation);
//            request.setLoginUser(user);

//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format("调用批量作废批量新增逻辑发货单入参:{}", "调用批量作废批量新增逻辑发货单入参"), JSON.toJSONString(request));
//            }
            ValueHolderV14 result = null;
            //sgRpcService.mergeVoidAndAddStick(request);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用批量作废批量新增逻辑发货单返回结果:{}", "调用批量作废批量新增逻辑发货单返回结果"), JSON.toJSONString(result));
            }
            if (result.getCode() == ResultCode.FAIL) {
                throw new NDSException(result.getMessage());
            }
            String logType = OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey();
            String logMsg = lgType + "[合单]库存占用成功";
            omsOrderLogService.addUserOrderLog(ocBOrderRelation.getOrderInfo().getId(),
                    ocBOrderRelation.getOrderInfo().getBillNo(), logType, logMsg, "", "", user);
        } catch (Exception e) {
            log.error(LogUtil.format("批量作废新增逻辑发货单异常", "批量作废新增逻辑发货单异常"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        }
    }


    /***
     * 物流拆单作废原逻辑发货单生成新的逻辑发货单
     * @param relation
     * @param childOrderRelations
     * @param user
     * @return
     */
    public Boolean splitOrderVoidAndAddStick(OcBOrderRelation relation,
                                             List<OcBOrderRelation> childOrderRelations, User user) {
        OcBOrder ocBOrderTmp = new OcBOrder();
        ocBOrderTmp.setId(relation.getOrderInfo().getId());
        //订单状态
        ocBOrderTmp.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
        makeModiferField(ocBOrderTmp, user);
        //再次更新订单信息
        ocBOrderMapper.updateById(ocBOrderTmp);

        // 原单
//        SgSendVoidSaveHandleBase oriOrder = new SgSendVoidSaveHandleBase();
//        oriOrder.setSourceBillId(relation.getOrderInfo().getId());
//        oriOrder.setSourceBillNo(relation.getOrderInfo().getBillNo());
//        oriOrder.setSourceBillType(1);
        List<OcBOrderItem> items = relation.getOrderItemList();
//        List<SgSendVoidSaveSkuItemInfoRequest> skuItemInfo = new ArrayList<>();
        for (OcBOrderItem item : items) {
            if (item.getProType() != null && item.getProType() == 4) {
                continue;
            }
            if (OmsOrderRefundStatus.SUCCESS.toInteger()
                    == Optional.ofNullable(item.getRefundStatus()).orElse(0)) {
                continue;
            }
//            SgSendVoidSaveSkuItemInfoRequest sku = new SgSendVoidSaveSkuItemInfoRequest();
//            sku.setItemId(item.getId());
//            sku.setPsCSkuEcode(item.getPsCSkuEcode());
//            sku.setPsCSkuId(item.getPsCSkuId());
//            sku.setQty(item.getQty());
//            skuItemInfo.add(sku);
        }
//        oriOrder.setSkuItemInfo(skuItemInfo);

        //拆出的子单
        //List<SgSendVoidSaveHandleBase> childOrderList = new ArrayList<>();
        for (OcBOrderRelation childRelation : childOrderRelations) {
//            SgSendVoidSaveHandleBase childOrder = new SgSendVoidSaveHandleBase();
//            childOrder.setSourceBillId(childRelation.getOrderInfo().getId());
//            childOrder.setSourceBillNo(childRelation.getOrderInfo().getBillNo());
//            childOrder.setSourceBillType(1);
//            childOrder.setSourceCode(childRelation.getOrderInfo().getSourceCode());
            List<OcBOrderItem> childItems = childRelation.getOrderItemList();
            //List<SgSendVoidSaveSkuItemInfoRequest> childSkuItemInfo = new ArrayList<>();
            for (OcBOrderItem item : childItems) {
                if (item.getProType() != null && item.getProType() == 4) {
                    continue;
                }
                if (OmsOrderRefundStatus.SUCCESS.toInteger()
                        == Optional.ofNullable(item.getRefundStatus()).orElse(0)) {
                    continue;
                }
//                SgSendVoidSaveSkuItemInfoRequest sku = new SgSendVoidSaveSkuItemInfoRequest();
//                sku.setItemId(item.getId());
//                sku.setPsCSkuEcode(item.getPsCSkuEcode());
//                sku.setPsCSkuId(item.getPsCSkuId());
//                sku.setQty(item.getQty());
//                childSkuItemInfo.add(sku);
            }
            //         childOrder.setSkuItemInfo(childSkuItemInfo);
            //           childOrderList.add(childOrder);
        }

//        SgSendVoidSaveHandleRequest params = new SgSendVoidSaveHandleRequest();
//        params.setBillInfo(oriOrder);
//        params.setBillInfoList(childOrderList);
//        params.setLoginUser(user);
        //return sgRpcService.splitOrdeVoidAndAddStick(params);
        return true;

    }

    /**
     * 批量作废新增逻辑发货单
     *
     * @param ocBOrderList
     * @param ocBOrderRelationList
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<List> AddAndVoidStock(
            List<OcBOrder> ocBOrderList, List<OcBOrderRelation> ocBOrderRelationList, User user, boolean isAllowOutStock) {
        //新增是否允许部分成功
        if (log.isDebugEnabled()) {
            log.debug("批量作废新增逻辑发货单入参ocBOrderList:" + JSON.toJSONString(ocBOrderList) + "ocBOrderRelationList:" +
                    JSON.toJSONString(ocBOrderRelationList));
        }

        ValueHolderV14<List> result = new ValueHolderV14<>(ResultCode.SUCCESS, "拆单重新占单成功");
        try {
            //先批量作废
            List<Long> ids = ocBOrderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
            if (ocBOrderMapper.updateList(ids) != ids.size()) {
                throw new NDSException("作废原单失败");
            }

            OcBOrder ocBOrder = ocBOrderList.get(0);

            //不指定实体仓，释放原单库存
            if (!isAllowOutStock) {
                List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemsByOrderIds(ids);
                SgOmsShareOutRequest request = cancelOrderMergeService.buildSgOmsShareOutRequest(ocBOrder, ocBOrderItems, user);
                ValueHolderV14 vh = sgRpcService.voidSgOmsShareOut(request, ocBOrder, ocBOrderItems);
                AssertUtil.assertException(!vh.isOK(), vh.getMessage());
                result.setMessage(vh.getMessage());
                result.setCode(vh.getCode());
                result.setData(new ArrayList<>());
                return result;
            }

//            //组装指定实体仓占单参数
//            SgOmsPhyStorageBillOutRequest request = buildSgPhyStorageOutRequest(ocBOrderList.get(0),ocBOrderRelationList);
//
//            //指定实体仓重新占单
//            ValueHolderV14 sgResult = sgRpcService.phyStorageOut(request);
//            AssertUtil.assertException(!sgResult.isOK(),sgResult.getMessage());
//            result.setCode(sgResult.getCode());
//            result.setMessage(sgResult.getMessage());
//            result.setData(new ArrayList<>());

//        SgSendVoidSaveRequest request = new SgSendVoidSaveRequest();
//            List<SgSendBillVoidRequest> voidList = this.getVoidList(ocBOrderList);
//            List<SgSendSaveWithPriorityRequest> addList = this.getAddPriorityRequests(ocBOrderRelationList, user, isAllowOutStock);
//            if (CollectionUtils.isEmpty(addList)) {
//                throw new NDSException("该实体仓下面逻辑仓为空!");
//            }
//            request.setVoidRequests(voidList);
//            request.setSaveWithPriorityRequests(addList);
//            request.setLoginUser(user);
//            if (log.isDebugEnabled()) {
//                log.debug("调用批量作废批量新增逻辑发货单入参:"
//                        + JSON.toJSONString(request));
//            }
//            result = sgRpcService.voidAndAddStick(request);
//            if (log.isDebugEnabled()) {
//                log.debug("调用批量作废批量新增逻辑发货单返回结果:"
//                        + JSON.toJSONString(result));
//            }
//
//            if (result.getCode() == ResultCode.FAIL) {
//                //缺货或者错误订单 次数加1
//                ocBOrderList.forEach(k -> {
//                    QueryWrapper<OcBOrder> wrapper = new QueryWrapper<>();
//                    wrapper.eq("id", k.getId());
//                    OcBOrder updateOrderInfo = new OcBOrder();
//                    updateOrderInfo.setId(k.getId());
//                    updateOrderInfo.setMergeErrorNum(Objects.isNull(k.getMergeErrorNum()) ? 0 : k.getMergeErrorNum() + 1);
//                    ocBOrderMapper.update(updateOrderInfo, wrapper);
//                });
//                throw new NDSException(result.getMessage());
//            }
//            this.voidOrderPushEs(ocBOrderList, user);
        } catch (Exception e) {
            log.error(LogUtil.format("批量作废新增逻辑发货单异常", "批量作废新增逻辑发货单异常"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        }
        return result;
    }

    /**
     * 组装拆单场景下指定实体仓占用库存model
     *
     * @param origOrder            原单
     * @param ocBOrderRelationList 新单
     * @return model
     */
    private SgOmsPhyStorageBillOutRequest buildSgPhyStorageOutRequest(OcBOrder origOrder, List<OcBOrderRelation> ocBOrderRelationList) {

        AssertUtil.assertException(ObjectUtils.isEmpty(origOrder) || CollectionUtils.isEmpty(ocBOrderRelationList),
                "原单或者拆分后订单为空");

        SgOmsPhyStorageBillOutRequest request = new SgOmsPhyStorageBillOutRequest();
        List<SgOmsPhyStorageOutRequest> outRequestList = new ArrayList<>();
        request.setSourceBillNo(origOrder.getBillNo());
        ocBOrderRelationList.forEach(orderRelation -> {
            OcBOrder ocBOrder = orderRelation.getOrderInfo();
            List<OcBOrderItem> itemList = orderRelation.getOrderItemList();

            //合并重复sku
            if (!CollectionUtils.isEmpty(itemList)) {
                Map<Long, List<OcBOrderItem>> itemMap = itemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuId));
                List<OcBOrderItem> newItemList = new ArrayList<>();
                for (Map.Entry<Long, List<OcBOrderItem>> entry : itemMap.entrySet()) {
                    List<OcBOrderItem> skuList = entry.getValue();
                    if (!CollectionUtils.isEmpty(skuList)) {
                        OcBOrderItem newItem = skuList.get(0);
                        newItem.setQty(skuList.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                        newItemList.add(newItem);
                    }
                }
                if (!CollectionUtils.isEmpty(newItemList)) {
                    itemList = newItemList;
                }
            }

            SgOmsPhyStorageOutRequest outRequest = updateOrderInfoService.buildSgOmsPhyStorageOutRequest(
                    ocBOrder, itemList, orderRelation.getShareStoreId(),
                    orderRelation.getShareStoreEcode(), ocBOrder.getCpCPhyWarehouseId(), ocBOrder.getCpCPhyWarehouseEcode()
            );
            outRequestList.add(outRequest);
        });
        request.setSgOmsPhyStorageOutRequestList(outRequestList);
        return request;
    }

    /**
     * 批量作废新增逻辑发货单后推ES
     *
     * @param ocBOrderList
     */
    public void voidOrderPushEs(List<OcBOrder> ocBOrderList, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("作废原单入参:{}", "作废原单入参"), JSON.toJSONString(ocBOrderList));
        }
        for (OcBOrder ocBOrder : ocBOrderList) {
            try {
                ocBOrder.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
            } catch (Exception e) {
                log.error(LogUtil.format("作废原单主表推ES异常", "作废原单主表推ES异常"), Throwables.getStackTraceAsString(e));
            }
            // 订单操作日志
            /*try {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                        ocBOrder.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(),
                        "作废原单", "", "", user
                );
            } catch (Exception e) {
                log.error("作废原单调用日志服务异常", e);
            }*/
        }
    }

    /**
     * 作废逻辑发货单集合
     *
     * @param ocBOrderList
     * @return
     */
    public List getVoidList(List<OcBOrder> ocBOrderList) {
        if (CollectionUtils.isEmpty(ocBOrderList)) {
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + "参数为空");
            }
            return null;
        }
//        List<SgSendBillVoidRequest> sgSendBillVoidRequestList = new ArrayList<>();
//        for (OcBOrder OcBOrder : ocBOrderList) {
//            SgSendBillVoidRequest sgSendBillVoidRequest = new SgSendBillVoidRequest();
//            sgSendBillVoidRequest.setSourceBillId(OcBOrder.getId());
//            sgSendBillVoidRequest.setSourceBillNo(OcBOrder.getBillNo());
//            sgSendBillVoidRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);//取自库存中心枚举  和作废逻辑发货单的枚举保持一致
//            sgSendBillVoidRequest.setIsExist(true);//传true 没有不处理
//            sgSendBillVoidRequestList.add(sgSendBillVoidRequest);
//        }
//        return sgSendBillVoidRequestList;
        return null;
    }

//    public SgSendVoidSaveHandleRequest getVoid(List<OcBOrder> ocBOrderList, OcBOrderRelation ocBOrderRelation) {
//        if (CollectionUtils.isEmpty(ocBOrderList)
//                || ocBOrderRelation == null
//                || CollectionUtils.isEmpty(ocBOrderRelation.getOrderItemList())) {
//            if (log.isDebugEnabled()) {
//                log.debug(this.getClass().getName() + "参数为空");
//            }
//            return null;
//        }
//
//        SgSendVoidSaveHandleRequest result = new SgSendVoidSaveHandleRequest();
//
//        SgSendVoidSaveHandleBase newOrder = getMergeVoid(ocBOrderRelation.getOrderInfo());
//
//        List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
//        List<SgSendVoidSaveSkuItemInfoRequest> skuItemInfo = new ArrayList<>(orderItemList.size());
//        for (OcBOrderItem ocBOrderItem : orderItemList) {
//            //bug[27120]【1115】【零售发货单】【合并订单】手动合并两个订单，合并失败，提示部分合并成功
//            if (ocBOrderItem.getProType().longValue() == (long) SkuType.NO_SPLIT_COMBINE
//                    || ocBOrderItem.getRefundStatus() == OcOrderRefundStatusEnum.SUCCESS.getVal()) {
//                continue;
//            }
//
//            SgSendVoidSaveSkuItemInfoRequest skuItem = new SgSendVoidSaveSkuItemInfoRequest();
//            skuItem.setItemId(ocBOrderItem.getId());
//            skuItem.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
//            skuItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
//            skuItem.setQty(ocBOrderItem.getQty());
//            skuItemInfo.add(skuItem);
//        }
//
//        newOrder.setSourceCode(buildSourceCode(ocBOrderRelation.getOrderItemList()
//                .stream().map(OcBOrderItem::getTid).distinct().collect(Collectors.toList())));
//        newOrder.setSkuItemInfo(skuItemInfo);
//        result.setBillInfo(newOrder);
//        result.setBillInfoList(getMergeVoidList(ocBOrderList));
//
//        return result;
//    }

    public String buildSourceCode(List<String> sourceCodes) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(sourceCodes)) {
            return null;
        }

        Set<String> set = new HashSet<>();
        StringBuilder buffer = new StringBuilder();
        sourceCodes.forEach(sourceCode -> {
            String[] splitCode = sourceCode.split(",");
            List<String> collect = Arrays.stream(splitCode).distinct().collect(Collectors.toList());
            collect.forEach(s -> {
                if (!set.contains(s)) {
                    set.add(s);
                    buffer.append(s + ",");
                }
            });
        });
        return buffer.length() < 1 ? "" : buffer.deleteCharAt(buffer.length() - 1).toString();
    }

//    public List<SgSendVoidSaveHandleBase> getMergeVoidList(List<OcBOrder> ocBOrderList) {
//        if (CollectionUtils.isEmpty(ocBOrderList)) {
//            if (log.isDebugEnabled()) {
//                log.debug(this.getClass().getName() + "参数为空");
//            }
//            return null;
//        }
//        List<SgSendVoidSaveHandleBase> sgSendBillVoidRequestList = new ArrayList<>();
//
//        for (OcBOrder ocBOrder : ocBOrderList) {
//            sgSendBillVoidRequestList.add(getMergeVoid(ocBOrder));
//        }
//
//        return sgSendBillVoidRequestList;
//    }

//    public SgSendVoidSaveHandleBase getMergeVoid(OcBOrder ocBOrder) {
//        if (Objects.isNull(ocBOrder)) {
//            if (log.isDebugEnabled()) {
//                log.debug(this.getClass().getName() + "参数为空");
//            }
//            return null;
//        }
//
//        SgSendVoidSaveHandleBase sgSendBillVoidRequest = new SgSendVoidSaveHandleBase();
//        sgSendBillVoidRequest.setSourceBillId(ocBOrder.getId());
//        sgSendBillVoidRequest.setSourceBillNo(ocBOrder.getBillNo());
//        sgSendBillVoidRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);//取自库存中心枚举  和作废逻辑发货单的枚举保持一致
//
//        return sgSendBillVoidRequest;
//    }
//
//
//    /**
//     * 新增逻辑发货单集合
//     *
//     * @return
//     */
//    public List<SgSendSaveWithPriorityRequest> getAddPriorityRequests(List<OcBOrderRelation> ocBOrderRelationList, User user, boolean isallowOutStock) {
//        if (CollectionUtils.isEmpty(ocBOrderRelationList)) {
//            return null;
//        }
//        List<SgSendSaveWithPriorityRequest> sgSendSaveWithPriorityRequestList = new ArrayList<>();
//        //组装新增集合
//        for (OcBOrderRelation ocBOrderRelation : ocBOrderRelationList) {
//            SgSendSaveWithPriorityRequest sgSendSaveWithPriorityRequest = new SgSendSaveWithPriorityRequest();
//            try {
//                //第一步:根据实体仓最终查询出虚拟仓优先级
//                //查找实体仓下面对应的逻辑仓
//                if (log.isDebugEnabled()) {
//                    log.debug("根据实体仓查询逻辑仓集合入参:"
//                            + JSON.toJSONString(ocBOrderRelation));
//                }
//
//                List<Long> storeList = omsQueryWareHouseService
//                        .queryStoreList(ocBOrderRelation.getOrderInfo().getCpCPhyWarehouseId());
//                if (CollectionUtils.isEmpty(storeList)) {
//                    if (log.isDebugEnabled()) {
//                        log.debug(this.getClass().getName() + "该实体仓下面逻辑仓为空");
//                    }
//                    return null;
//                }
//                if (log.isDebugEnabled()) {
//                    log.debug("查询逻辑仓优先级入参:"
//                            + JSON.toJSONString(ocBOrderRelation));
//                }
//                List<StStockPriorityRequest> stStockPriorityRequests = syncStockStrategyService
//                        .queryStStockPriority(ocBOrderRelation.getOrderInfo().getCpCShopId(), storeList);
//                if (log.isDebugEnabled()) {
//                    log.debug("查询逻辑仓优先级返回结果:"
//                            + JSON.toJSONString(stStockPriorityRequests));
//                }
//
//                if (CollectionUtils.isEmpty(stStockPriorityRequests)) {
//                    throw new NDSException("请确认当前实体仓下的逻辑仓是否在库存同步策略中配置");
//                }
//                //第二部组装逻辑发货单主表信息
//                SgSendSaveRequest sgSend = new SgSendSaveRequest();
//                sgSend.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
//                sgSend.setSourceBillId(ocBOrderRelation.getOrderInfo().getId());
//                sgSend.setSourceBillNo(ocBOrderRelation.getOrderInfo().getBillNo());
//                sgSend.setCpCShopId(ocBOrderRelation.getOrderInfo().getCpCShopId());
//                sgSend.setCpCShopTitle(ocBOrderRelation.getOrderInfo().getCpCShopTitle());
//                sgSend.setSourcecode(ocBOrderRelation.getOrderInfo().getSourceCode());
//                sgSend.setBillStatus(ocBOrderRelation.getOrderInfo().getOrderStatus());
//                //第三部
//                //组装明细
//                List<SgSendItemSaveRequest> itemList = new ArrayList<>();
//                for (OcBOrderItem ocBOrderItem : ocBOrderRelation.getOrderItemList()) {
//                    if (ocBOrderItem.getProType() != SkuType.NO_SPLIT_COMBINE) {
//                        SgSendItemSaveRequest sgSendItemSaveRequest = new SgSendItemSaveRequest();
//                        sgSendItemSaveRequest.setSourceBillItemId(ocBOrderItem.getId());//来源单据明细Id
//                        sgSendItemSaveRequest.setQty(ocBOrderItem.getQty());//待发货数量
//                        sgSendItemSaveRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());//条码id
//                        sgSendItemSaveRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());//条码
//                        sgSendItemSaveRequest.setPsCProId(ocBOrderItem.getPsCProId());//商品ID
//                        sgSendItemSaveRequest.setPsCProEcode(ocBOrderItem.getPsCProEcode());//商品Ecode
//                        sgSendItemSaveRequest.setPsCProEname(ocBOrderItem.getPsCProEname());//商品名称
//                        sgSendItemSaveRequest.setPsCSpec1Id(ocBOrderItem.getPsCClrId());//颜色信息
//                        sgSendItemSaveRequest.setPsCSpec1Ecode(ocBOrderItem.getPsCClrEcode());
//                        sgSendItemSaveRequest.setPsCSpec1Ename(ocBOrderItem.getPsCClrEname());
//                        sgSendItemSaveRequest.setPsCSpec2Id(ocBOrderItem.getPsCSizeId());
//                        sgSendItemSaveRequest.setPsCSpec2Ecode(ocBOrderItem.getPsCSizeEcode());
//                        sgSendItemSaveRequest.setPsCSpec2Ename(ocBOrderItem.getPsCSizeEname());
//                        itemList.add(sgSendItemSaveRequest);
//                    }
//                }
//                String orderTable = "oc_b_order";
//                sgSendSaveWithPriorityRequest.setTable(orderTable);//表名
//                sgSendSaveWithPriorityRequest.setPriorityList(stStockPriorityRequests);//虚拟仓优先级
//                sgSendSaveWithPriorityRequest.setSgSend(sgSend);//逻辑发货单主表信息
//                sgSendSaveWithPriorityRequest.setItemList(itemList);//逻辑发货单明细信息
//                if (isallowOutStock) {
//                    sgSendSaveWithPriorityRequest.setPreoutWarningType(3);//允许部分占用成功（失败）的情况传缺货（3）
//                    sgSendSaveWithPriorityRequest.setIsSpecChange(false);
//                } else {
//                    sgSendSaveWithPriorityRequest.setPreoutWarningType(2);//允许部分占用成功（失败）的情况传缺货（3）
//                    sgSendSaveWithPriorityRequest.setIsSpecChange(true);
//                }
//                sgSendSaveWithPriorityRequest.setUpdateMethod(1);//修改方式默认全量（1）
//                sgSendSaveWithPriorityRequest.setLoginUser(user);
//            } catch (Exception e) {
//                log.error(this.getClass().getName() + "组装逻辑发货单集合", e);
//                throw new NDSException(e.getMessage());
//
//            }
//            sgSendSaveWithPriorityRequestList.add(sgSendSaveWithPriorityRequest);
//        }
//        return sgSendSaveWithPriorityRequestList;
//    }

    private void makeModiferField(OcBOrder order, User user) {
        Date date = new Date();
        order.setModifierid(Long.valueOf(user.getId()));//修改人id
        order.setModifiername(user.getName());//修改人用户名
        order.setModifierename(user.getEname());
        order.setModifieddate(date);//修改时间
        order.setIsactive("Y");//是否启用
    }
}
