package com.jackrain.nea.sg.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.enums.DrpStoreTypeEnum;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-05-07 15:24
 */
@Slf4j
@Component
public class AddOrderNoticeAndOutService {
    @Autowired
    private BasicCpQueryService basicCpQueryService;

    @Autowired
    protected PsRpcService psRpcService;
    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private CpRpcService cpRpcService;


    public ValueHolderV14 addNoticeAndOutOrderNew(OcBReturnOrderRelation relation, OcBRefundIn refundIn, User user) {
        try {
            ValueHolderV14 result = new ValueHolderV14();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("新增入库通知单新增入库通知单接口入参{}", "新增入库通知单"), JSON.toJSONString(relation), JSON.toJSONString(refundIn));
            }
            if (null == refundIn) {
                return ValueHolderV14Utils.getFailValueHolder("新增入库通知单OcBRefundIn信息为空");
            }
            if (null == refundIn.getCpCPhyWarehouseId()) {
                return ValueHolderV14Utils.getFailValueHolder("退货入库单实体仓Id为空信息为空");
            }
            OcBReturnOrder returnOrderInfo = relation.getReturnOrderInfo();
            if (returnOrderInfo == null) {
                return ValueHolderV14Utils.getFailValueHolder("新增入库通知单returnOrderInfo信息为空");
            }
            SgOmsStoInRequest notices = this.transferParam(relation.getReturnOrderInfo(), relation.getOrderRefundList(), user);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用新增入库通知单接口入参{}", "调用新增入库通知单接口入参"), JSON.toJSONString(notices));
            }
            try {
                result = sgRpcService.addOrderNotice(notices);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("调用新增入库通知单返回数据:{}", "调用新增入库通知单返回数据"), JSON.toJSONString(result));
                }
            } catch (Exception e) {
                log.error(LogUtil.format("调用新增入库通知单异常信息", "调用新增入库通知单异常信息"), Throwables.getStackTraceAsString(e));
                result.setCode(ResultCode.FAIL);
                result.setMessage("调用新增入库通知单SgOmsStoInCmd.saveSgOmsStoIn:异常信息！" + e.getMessage());
            }
            return result;
        } catch (Exception e) {
            log.error(LogUtil.format("新增入库通知单异常", "新增入库通知单异常"), Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.queryStoreList:" + e.getMessage());
        }
    }

    public SgOmsStoInRequest transferParam(OcBReturnOrder returnOrderInfo, List<OcBReturnOrderRefund> refundList, User user) {
        SgOmsStoInRequest notices = new SgOmsStoInRequest();
        List<SgOmsStoInItemRequest> itemList = Lists.newArrayList();
        /****************************************主表参数添加*************************************/
        //来源单据类型
        notices.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);//零售退货
        //来源单据id
        notices.setSourceBillId(returnOrderInfo.getId());
        //来源单据单号
        notices.setSourceBillNo(returnOrderInfo.getBillNo());
        //单据日期
        notices.setBillDate(new Date());
//        //发货方编码
//        notices.setSenderEcode(returnOrderInfo.getReceiveName());
        //发货方名称
        notices.setSenderName(returnOrderInfo.getBuyerNick());
        //业务节点
        notices.setServiceNode(SgConstantsIF.SERVICE_NODE_RETAIL_REF_IN);
//        notices.setLoginUser(user);
        Long storeId = returnOrderInfo.getCpCStoreId();
        String storeCode = returnOrderInfo.getCpCStoreEcode();
        String storeName = returnOrderInfo.getCpCStoreEname();
        if (storeId == null || StringUtils.isEmpty(storeCode) || StringUtils.isEmpty(storeName)) {
            Long cpCPhyWarehouseInId = returnOrderInfo.getCpCPhyWarehouseInId();
            if (cpCPhyWarehouseInId == null) {
                log.debug("当前退换货单：{}入库实体仓为空");
            }

            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseInId);
            AssertUtil.assertException(ObjectUtils.isEmpty(cpCPhyWarehouse),"入库实体仓查询为空");

            StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
            storeInfoQueryRequest.setPhyId(cpCPhyWarehouseInId);
            HashMap<Long, List<CpCStore>> storeInfoByPhyId = basicCpQueryService.getStoreInfoByPhyId(storeInfoQueryRequest);
            List<CpCStore> cpCStores = storeInfoByPhyId.get(cpCPhyWarehouseInId);
            if (CollectionUtils.isEmpty(cpCStores)) {
                throw new NDSException("通过退换货单实体仓未查询到对应逻辑仓信息");
            }
            Optional<CpCStore> returnStore = cpCStores.stream().filter(x -> DrpStoreTypeEnum.TYPE_27.getValue().equals(x.getStoretype())).findFirst();
            if (returnStore.isPresent()) {
                CpCStore cpCStore = returnStore.get();
                storeId = cpCStore.getId();
                storeCode = cpCStore.getEcode();
                storeName = cpCStore.getEname();
            }else if(OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE_STORE_02.equals(cpCPhyWarehouse.getWhType())){
                CpCStore cpCStore = cpCStores.get(0);
                storeId = cpCStore.getId();
                storeCode = cpCStore.getEcode();
                storeName = cpCStore.getEname();
            }
        }
        /****************************************子表表参数添加*************************************/
        for (OcBReturnOrderRefund ocBReturnOrderRefund : refundList) {
            SgOmsStoInItemRequest item = new SgOmsStoInItemRequest();
            item.setCpCStoreId(storeId);
            item.setCpCStoreEcode(storeCode);
            item.setCpCStoreEname(storeName);
            item.setSourceBillItemId(ocBReturnOrderRefund.getId());//
            item.setPsCSkuId(ocBReturnOrderRefund.getPsCSkuId());//条码ID
            item.setPsCSkuEcode(ocBReturnOrderRefund.getPsCSkuEcode());//条码
            item.setPsCProId(ocBReturnOrderRefund.getPsCProId());//商品Id
            item.setPsCProEcode(ocBReturnOrderRefund.getPsCProEcode());//商品编码
            item.setPsCProEname(ocBReturnOrderRefund.getPsCProEname());//商品名称
            item.setPriceList(ocBReturnOrderRefund.getPrice() == null ? BigDecimal.ZERO :
                    ocBReturnOrderRefund.getPrice());//吊牌价
            item.setQty(ocBReturnOrderRefund.getQtyRefund());
            item.setQtyPrein(ocBReturnOrderRefund.getQtyIn());
            // 国标码
            item.setGbcode(ocBReturnOrderRefund.getBarcode());
            if (null != ocBReturnOrderRefund.getPsCSkuId()) {
                ProductSku productSku = psRpcService.selectProductSku(ocBReturnOrderRefund.getPsCSkuEcode() + "");
                //List<SkuQueryListRequest> skuQueryListRequestList = psRpcService.querySkuListByIds(ids);
                if (null == productSku) {
                    throw new NDSException(ocBReturnOrderRefund.getPsCSkuEcode() + "未查询到关联商品信息");
                }
                if (null != productSku) {
                    item.setPsCSpec1Id(productSku.getColorId());//颜色id
                    item.setPsCSpec1Ecode(productSku.getColorCode());
                    item.setPsCSpec1Ename(productSku.getColorName());
                    item.setPsCSpec2Id(productSku.getSizeId());//尺寸id
                    item.setPsCSpec2Ecode(productSku.getSizeCode());
                    item.setPsCSpec2Ename(productSku.getSizeName());
                }
            }
            itemList.add(item);
        }
        notices.setOmsStoInItemRequests(itemList);
        //校验传给sg的参数
        String checkResult = this.checkParam(notices);
        if (StringUtils.isNotEmpty(checkResult)) {
            throw new NDSException(checkResult);
        }
        return notices;
    }

    public String checkParam(SgOmsStoInRequest request) {
        String[] mainTableKey = {"SOURCE_BILL_ID", "SOURCE_BILL_TYPE", "SOURCE_BILL_NO"};
        String[] itemTableKey = {"PS_C_SKU_ECODE", "QTY", "QTY_PREIN", "CP_C_STORE_ID", "CP_C_STORE_ECODE", "CP_C_STORE_ENAME",
                "PS_C_SKU_ID", "PS_C_PRO_ID", "PS_C_PRO_ECODE", "PS_C_PRO_ENAME", "PS_C_SPEC1_ID", "PS_C_SPEC1_ECODE", "PS_C_SPEC2_ID"};

        JSONObject jsObject = JSONObject.parseObject(JSON.toJSONString(request));
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用库存中心生成入库通知单参数SgOmsStoInRequest{}", "调用库存中心生成入库通知单参数"), JSON.toJSONString(request));
        }

        for (String key : mainTableKey) {
            if (jsObject.get(key) == null) {
                return key + "为空";
            }
        }
        JSONArray itemArray = jsObject.getJSONArray("OMSSTOINITEMREQUESTS");
        for (int i = 0; i < itemArray.size(); i++) {
            JSONObject item = itemArray.getJSONObject(i);
            for (String key : itemTableKey) {
                if (item.get(key) == null) {
                    return key + "为空";
                }
            }
        }
        return "";
    }
}
