package com.jackrain.nea.sg.service;

import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-05-21
 * create at : 2019-05-21 5:27 PM
 * 查询订单对应的逻辑仓id集合
 */
@Component
public class QueryOrderItemAvailbleStockService {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsSyncStockStrategyService omsSyncStockStrategyService;

    /**
     * 根据 cpCShopId查询同步店铺策略表明细的cp_c_store_id集合
     *
     * @param cpCShopId 店铺Id
     * @return 同步店铺策略表明细的cp_c_store_id集合
     */
    public List<Long> getStoreIds(Long cpCShopId) {
        List<Long> ids = omsSyncStockStrategyService.queryShopStoreNextList(cpCShopId);
        if (ids == null || ids.size() == 0) {
            return null;
        }
        return ids;
    }

    /**
     * 根据实体仓id查询对应的逻辑仓id集合
     *
     * @param cpCPhyWarehouseId 实体仓id
     * @return 逻辑仓id集合
     */
    public List<Long> getWareIds(Long cpCPhyWarehouseId) {
        List<Long> ids = cpRpcService.queryStoreList(cpCPhyWarehouseId);
        if (ids == null || ids.size() == 0) {
            return null;
        }
        return ids;
    }
}
