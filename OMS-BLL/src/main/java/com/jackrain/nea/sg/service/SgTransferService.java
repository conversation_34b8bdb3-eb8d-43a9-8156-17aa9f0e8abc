package com.jackrain.nea.sg.service;

import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date ： 2020/5/11 11:05
 * description ：退供单业务操作类
 * @ Modified By：
 */

@Service
@Slf4j
public class SgTransferService {
    private static final int DEFAULT_SEARCH_ES_PAGE_SIZE = 100;

    private static final String ES_SEARCH_ROW_COUNT_KEY = "rowcount";

    @Autowired
    private SgRpcService sgRpcService;

    private static final int OMS_ORDER_REDIS_TIMEOUT = 24 * 60 * 60 * 1000;


    /**
     * @param returnOrderRelation 唯品会退供单对象
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2020/6/23 17:08
     * @description：TODO
     */
//    public Boolean queryExistSgTransfer(IpVipReturnOrderRelation returnOrderRelation) {
//        ValueHolderV14<List<ScBTransfer>> valueHolderV14 = sgRpcService.querySgTransferByOrderSn(returnOrderRelation);
//        if (0 == valueHolderV14.getCode()) {
//            List<ScBTransfer> dataList = valueHolderV14.getData();
//            return CollectionUtils.isNotEmpty(dataList);
//        }
//        return false;
//    }
}
