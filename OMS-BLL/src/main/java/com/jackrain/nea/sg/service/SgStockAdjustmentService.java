package com.jackrain.nea.sg.service;


import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoAdjustSaveRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 库存调整单(实体仓)新增
 * 黄世新
 */
@Slf4j
@Component
public class SgStockAdjustmentService {

    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private PsRpcService psRpcService;


    /**
     * 退换货新增库存调整单
     *
     * @param ocBRefundIn  退换货主表信息
     * @param productItems 退货明细
     * @param user         操作人信息
     * @return
     */
    public boolean addStockAdjustmen(OcBRefundIn ocBRefundIn,
                                     List<OcBRefundInProductItem> productItems, User user) {
        try {
            SgOmsStoAdjustSaveRequest adjustSaveRequest = new SgOmsStoAdjustSaveRequest();
//            adjustSaveRequest.setCpCPhyWarehouseId(ocBRefundIn.getCpCPhyWarehouseId());
//            adjustSaveRequest.setCpCPhyWarehouseEcode(ocBRefundIn.getCpCPhyWarehouseEcode());
//            adjustSaveRequest.setCpCPhyWarehouseEname(ocBRefundIn.getCpCPhyWarehouseEname());
            adjustSaveRequest.setSourceBillId(ocBRefundIn.getId()); //来源单据id
            adjustSaveRequest.setSourceBillNo(ocBRefundIn.getId().toString()); //来源单据id
            adjustSaveRequest.setSourceBillType(1); //来源单据类型
           // adjustSaveRequest.setSourceBillType(SgPhyAdjustConstantsIF.SOURCE_BILL_TYPE_REF_IN); //来源单据类型
            adjustSaveRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL); //单据类型 1正常调整 2差异调整
            adjustSaveRequest.setRequestType(SgConstants.REQUEST_TYPE_SAVE_AND_SUBMIT);//请求类型 创建并提交
            adjustSaveRequest.setBillDate(ocBRefundIn.getSubmitDate());//请求类型 创建并提交
            // 调整性质：冲无头件
//            adjustSaveRequest.setSgBAdjustPropId((int)SgConstantsIF.SERVICE_NODE_ADJUST_PROP_FLUSH_NO_SOURCE);
//            adjustSaveRequest.setCpCStoreId(ocBRefundIn.getInStoreId());
            adjustSaveRequest.setCpCStoreEcode(ocBRefundIn.getInStoreEcode());
            adjustSaveRequest.setLoginUser(user);
//            adjustSaveRequest.setCpCStoreEname(ocBRefundIn.getInStoreEname());
//            adjustSaveRequest.setWmsBillNo(ocBRefundIn.getWmsBillNo());

            List<SgOmsStoAdjustItemSaveRequest> items = new ArrayList<>();
            productItems.forEach(p -> {
                SgOmsStoAdjustItemSaveRequest itemSaveRequest = new SgOmsStoAdjustItemSaveRequest();
                itemSaveRequest.setSourceBillItemId(p.getId());
                itemSaveRequest.setQty(p.getQty()); //入库数量
//                itemSaveRequest.setPsCSkuId(p.getPsCSkuId());
                itemSaveRequest.setPsCSkuEcode(p.getPsCSkuEcode());
//                itemSaveRequest.setPsCProId(p.getPsCProId());
//                itemSaveRequest.setPsCProEcode(p.getPsCProEcode());
//                itemSaveRequest.setPsCProEname(p.getPsCProEname());
                //通过条码id查询 颜色及尺寸
//                ProductSku productSku = psRpcService.selectProductById(p.getPsCSkuId() + "");
//                log.debug("SgStockAdjustmentService.addStockAdjustmen根据条码id查询颜色尺码国标码等信息: -> "
//                        + productSku);
//                itemSaveRequest.setPsCSpec1Id(productSku.getColorId());
//                itemSaveRequest.setPsCSpec1Ename(productSku.getColorName());
//                itemSaveRequest.setPsCSpec1Ecode(productSku.getColorCode());
//                itemSaveRequest.setPsCSpec2Id(productSku.getSizeId());
//                itemSaveRequest.setPsCSpec2Ename(productSku.getSizeName());
//                itemSaveRequest.setPsCSpec2Ecode(productSku.getSizeCode());
//                itemSaveRequest.setPsCProId(productSku.getProdId());
//                itemSaveRequest.setPsCProEcode(productSku.getProdCode());
//                itemSaveRequest.setPsCProEname(productSku.getName());
                //20190618 孙俊磊 补全标准价
//                itemSaveRequest.setPriceList(productSku.getPricelist());
                //国标码
//                itemSaveRequest.setGbcode(productSku.getBarcode69());
                items.add(itemSaveRequest);
            });
            adjustSaveRequest.setItems(items);
            log.debug(LogUtil.format("调用接口入参{}", "addStockAdjustmen"), JSONObject.toJSONString(adjustSaveRequest));
            return sgRpcService.addStockAdjustment(adjustSaveRequest).isOK();
        } catch (Exception e) {
            log.error(LogUtil.format("库存调整单(实体仓)新增失败", "库存调整单(实体仓)新增失败"), Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    /**
     * 退换货新增库存调整单
     *
     * @param ocBRefundIn  退换货主表信息
     * @param productItems 退货明细
     * @param user         操作人信息
     * @return
     */
    public ValueHolderV14 addAdjustmen(OcBRefundIn ocBRefundIn,
                                       List<OcBRefundInProductItem> productItems, User user, Long adjustProp) {
        try {
            SgOmsStoAdjustSaveRequest adjustSaveRequest = new SgOmsStoAdjustSaveRequest();
//            adjustSaveRequest.setCpCPhyWarehouseId(ocBRefundIn.getCpCPhyWarehouseId());
//            adjustSaveRequest.setCpCPhyWarehouseEcode(ocBRefundIn.getCpCPhyWarehouseEcode());
//            adjustSaveRequest.setCpCPhyWarehouseEname(ocBRefundIn.getCpCPhyWarehouseEname());
            adjustSaveRequest.setSourceBillId(productItems.get(0).getOcBReturnOrderId()); //来源单据id
            adjustSaveRequest.setSourceBillNo(productItems.get(0).getOcBReturnOrderId().toString()); //来源单据id
            adjustSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF); //来源单据类型 零售退货
            adjustSaveRequest.setRequestType(SgConstants.REQUEST_TYPE_SAVE_AND_SUBMIT);//请求类型
//            adjustSaveRequest.setSourceBillNo();
            adjustSaveRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL); //单据类型 1正常调整 2差异调整
            adjustSaveRequest.setBillDate(ocBRefundIn.getSubmitDate()); //单据类型 1正常调整 2差异调整

//            adjustSaveRequest.setCpCStoreId(ocBRefundIn.getInStoreId());
            adjustSaveRequest.setCpCStoreEcode(ocBRefundIn.getInStoreEcode());
//            adjustSaveRequest.setCpCStoreEname(ocBRefundIn.getInStoreEname());
//            adjustSaveRequest.setWmsBillNo(ocBRefundIn.getWmsBillNo());

            //20190620 增加调整性质 孙俊磊
            adjustSaveRequest.setSgBAdjustPropId(adjustProp.intValue());
            List<SgOmsStoAdjustItemSaveRequest> items = new ArrayList<>();
            productItems.forEach(p -> {
                SgOmsStoAdjustItemSaveRequest itemSaveRequest = new SgOmsStoAdjustItemSaveRequest();
                itemSaveRequest.setSourceBillItemId(p.getId());
                itemSaveRequest.setQty(p.getQty()); //入库数量
//                itemSaveRequest.setPsCSkuId(p.getPsCSkuId());
                itemSaveRequest.setPsCSkuEcode(p.getPsCSkuEcode());
//                itemSaveRequest.setPsCProId(p.getPsCProId());
//                itemSaveRequest.setPsCProEcode(p.getPsCProEcode());
//                itemSaveRequest.setPsCProEname(p.getPsCProEname());
                //通过条码id查询 颜色及尺寸
//                ProductSku productSku = psRpcService.selectProductById(p.getPsCSkuId() + "");
//                log.debug("SgStockAdjustmentService.addAdjustmen根据条码id查询颜色尺码国标码等信息: -> "
//                        + productSku);
//                itemSaveRequest.setPsCSpec1Id(productSku.getColorId());
//                itemSaveRequest.setPsCSpec1Ename(productSku.getColorName());
//                itemSaveRequest.setPsCSpec1Ecode(productSku.getColorCode());
//                itemSaveRequest.setPsCSpec2Id(productSku.getSizeId());
//                itemSaveRequest.setPsCSpec2Ename(productSku.getSizeName());
//                itemSaveRequest.setPsCSpec2Ecode(productSku.getSizeCode());
//                itemSaveRequest.setPsCProId(productSku.getProdId());
//                itemSaveRequest.setPsCProEcode(productSku.getProdCode());
//                itemSaveRequest.setPsCProEname(productSku.getName());
                //20190618 孙俊磊 补全标准价
//                itemSaveRequest.setPriceList(productSku.getPricelist());
//                itemSaveRequest.setGbcode(productSku.getBarcode69());
                items.add(itemSaveRequest);
            });
            adjustSaveRequest.setItems(items);
            adjustSaveRequest.setLoginUser(user);
            log.debug(LogUtil.format("调用接口入参", "addAdjustmen"), JSONObject.toJSONString(adjustSaveRequest));
            return sgRpcService.addStockAdjustment(adjustSaveRequest);
        } catch (Exception e) {
            log.error(LogUtil.format("库存调整单(实体仓)新增失败", "库存调整单(实体仓)新增失败"), Throwables.getStackTraceAsString(e));
            throw new NDSException("库存调整单(实体仓)新增失败" + e.getMessage());
        }
    }

    /**
     * 退换货新增库存调整单
     *
     * @param ocBRefundIn  退换货主表信息
     * @param productItems 退货明细
     * @param user         操作人信息
     * @return
     */
    public boolean addStockAdjustmenByRealSku(OcBRefundIn ocBRefundIn,
                                              List<OcBRefundInProductItem> productItems, User user, Long adjustProp) {
        try {
            SgOmsStoAdjustSaveRequest adjustSaveRequest = new SgOmsStoAdjustSaveRequest();
            adjustSaveRequest.setSourceBillId(ocBRefundIn.getId()); //来源单据id
            adjustSaveRequest.setSourceBillNo(ocBRefundIn.getId().toString()); //来源单据编码
           // adjustSaveRequest.setSourceBillType(SgPhyAdjustConstantsIF.SOURCE_BILL_TYPE_REF_IN); //来源单据类型
            adjustSaveRequest.setSourceBillType(1); //来源单据类型
            adjustSaveRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL); //单据类型 1正常调整 2差异调整
            adjustSaveRequest.setBillDate(ocBRefundIn.getSubmitDate()); //单据类型 1正常调整 2差异调整
            adjustSaveRequest.setRequestType(SgConstants.REQUEST_TYPE_SAVE_AND_SUBMIT);//请求类型 创建并提交
            //20190620 增加调整性质 孙俊磊
            adjustSaveRequest.setSgBAdjustPropId(adjustProp.intValue());
            adjustSaveRequest.setCpCStoreEcode(ocBRefundIn.getInStoreEcode());
            adjustSaveRequest.setLoginUser(user);
            List<SgOmsStoAdjustItemSaveRequest> items = new ArrayList<>();
            productItems.forEach(p -> {
                SgOmsStoAdjustItemSaveRequest itemSaveRequest = new SgOmsStoAdjustItemSaveRequest();
                itemSaveRequest.setSourceBillItemId(p.getId());
                itemSaveRequest.setQty(p.getQty()); //入库数量
                itemSaveRequest.setPsCSkuEcode(p.getPsCSkuEcode());

                items.add(itemSaveRequest);
            });
            adjustSaveRequest.setItems(items);
            return sgRpcService.addStockAdjustment(adjustSaveRequest).isOK();
        } catch (Exception e) {
            log.error(LogUtil.format("库存调整单(实体仓)新增失败", "addStockAdjustmenByRealSku"), Throwables.getStackTraceAsString(e));
        }
        return false;
    }
}
