package com.jackrain.nea.sg.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategy2BRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyCCRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgGiftSkuInfo;
import com.burgeon.r3.sg.sourcing.model.request.SgGroupSkuInfo;
import com.burgeon.r3.sg.sourcing.model.request.SkuItem2B;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemCC;
import com.github.pagehelper.util.StringUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cp.dto.CpCShopProfileExt;
import com.jackrain.nea.cp.services.CpSaleOrganizationService;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCShopProfile;
import com.jackrain.nea.enums.YesOrNoEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryRecordMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipOccupyItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemExtMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBJitxDealerOrderTaskMapper;
import com.jackrain.nea.oc.oms.model.constant.VipConstant;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.JitxOrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OccupancyStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOccupyRelation;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryRecord;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import com.jackrain.nea.oc.oms.model.table.task.OcBJitxDealerOrderTask;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.services.GenerateCyclePurchaseSubOrderService;
import com.jackrain.nea.oc.oms.services.GetAppointDistLevel3Service;
import com.jackrain.nea.oc.oms.services.IpJitxOrderService;
import com.jackrain.nea.oc.oms.services.IpTaobaoExchangeService;
import com.jackrain.nea.oc.oms.services.OcBOrderItemExtService;
import com.jackrain.nea.oc.oms.services.OmsAutoHoldNewService;
import com.jackrain.nea.oc.oms.services.OmsOccupyTaskService;
import com.jackrain.nea.oc.oms.services.OmsOrderAutoSplitByGoodsService;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.SplitOutStockOrderService;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderSpiltUtill;
import com.jackrain.nea.oc.oms.services.audit.OmsOrderAuditService;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitOrderUtils;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.request.StStockPriorityRequest;
import com.jackrain.nea.st.model.result.StCWarehouseQueryResult;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.OmsQueryWareHouseService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.st.service.OrderOccupySplitTypeService;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.util.OrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/4/4 5:28 下午
 * @Version 1.0
 * <p>
 * 占用库存相关的类
 */
@Slf4j
@Component
public class SgOccupiedInventoryService {

    @Autowired
    private OmsQueryWareHouseService omsQueryWareHouseService;
    @Autowired
    private OmsSyncStockStrategyService omsSyncStockStrategyService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsAuditTaskService omsAuditTaskService;
    @Autowired
    private IpBTimeOrderVipMapper mapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBorderItemMapper;
    @Autowired
    private IpBJitxDeliveryRecordMapper jitxDeliveryRecordMapper;
    @Autowired
    private IpTaobaoExchangeService ipTaobaoExchangeService;
    @Autowired
    private SplitOutStockOrderService splitOutStockOrderService;
    @Autowired
    private IpBTimeOrderVipOccupyItemMapper timeOrderVipOccupyItemMapper;
    @Autowired
    private SplitOrderUtils splitOrderUtils;

    @Autowired
    private OmsWmsTaskService wmsTaskService;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private PropertiesConf propertiesConf;

    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;


    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private OmsOrderAutoSplitByGoodsService omsOrderAutoSplitByGoodsService;

    @Autowired
    OmsOrderSpiltUtill omsOrderSpiltUtill;

    @Autowired
    OmsOrderAdvanceParseService omsOrderAdvanceParseService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OcBJitxDealerOrderTaskMapper taskMapper;
    @Autowired
    private OmsAutoHoldNewService omsAutoHoldNewService;
    @Autowired
    private GenerateCyclePurchaseSubOrderService cyclePurchaseSubOrderService;
    @Autowired
    private OcBOccupyTaskMapper ocBOccupyTaskMapper;
    @Autowired
    private OmsOrderAuditService omsOrderAuditService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private VipcomJitxWarehouseService vipcomJitxWarehouseService;
    @Autowired
    private OrderOccupySplitTypeService occupySplitTypeService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private CpSaleOrganizationService cpSaleOrganizationService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OcBOrderItemExtMapper ocBOrderItemExtMapper;
    @Autowired
    private OcBOrderItemExtService ocBOrderItemExtService;
    @Resource
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private GetAppointDistLevel3Service getAppointDistLevel3Service;
    @Resource
    private IpJitxOrderService ipJitxOrderService;

    /**
     * k
     * 占单
     *
     * @param occupyRelation 参数
     * @param user
     * @return key 明细id val 缺货数量
     * 部分占单时返回缺货  并将缺货数量返回
     */
    public ValueHolderV14<Map<Long, Integer>> occupySearchStockWareHouse(OmsOccupyRelation occupyRelation, User user) {

        ValueHolderV14<Map<Long, Integer>> vh = new ValueHolderV14<>();
        OcBOrder ocBOrder = occupyRelation.getOcBOrder();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("occupySearchStockWareHouseOMS占用库存服务开始", "occupySearchStockWareHouseOMS占用库存服务开始", ocBOrder.getId()));
        }
        try {
            if (CollectionUtils.isEmpty(occupyRelation.getOcBOrderItems())) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(ocBOrder.getId() + "订单明细为空!");
                return vh;
            }

            //查找实体仓下面对应的逻辑仓
            List<Long> storeList = omsQueryWareHouseService.queryStoreList(ocBOrder.getCpCPhyWarehouseId());
            if (CollectionUtils.isEmpty(storeList)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(ocBOrder.getId() + "该实体仓下面逻辑仓为空!");
                return vh;
            }
            List<StStockPriorityRequest> stStockPriorityRequests = omsSyncStockStrategyService.queryStStockPriority(ocBOrder.getCpCShopId(), storeList);
            if (CollectionUtils.isEmpty(stStockPriorityRequests)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(ocBOrder.getId() + "该店铺逻辑发货仓为空!");
                return vh;
            }
            // SgSendSaveWithPriorityRequest model = packageLogicSearchModel(occupyRelation, stStockPriorityRequests, user);
            if (log.isDebugEnabled()) {
                // log.debug("sgSendSaveWithPriorityCmd.saveSendWithPriority_OrderId[{}]OMS占用库存服务入参:{}", ocBOrder.getId(), JSONObject.toJSONString(model));
            }
            ValueHolderV14 v14 = null;//sgRpcService.saveSendWithPriority(model);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("sgSendSaveWithPriorityCmd.saveSendWithPriority_OrderId[{}]OMS占用库存服务出参:{}", "OMS占用库存服务出参", ocBOrder.getId()), v14.toJSONObject());
            }
            JSONObject resultJson = v14.toJSONObject();
            vh = handleReturn(ocBOrder, resultJson);
        } catch (Exception e) {
            log.error(LogUtil.format("寻源逻辑仓库存服务异常", "寻源逻辑仓库存服务异常", ocBOrder.getId()), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(ocBOrder.getId() + "订单库存占用失败!" + e.getMessage());
        }
        return vh;
    }

    /**
     * 处理库存中心返回值
     *
     * @param ocBOrder
     * @param resultJson
     * @return key 明细id val 缺货数量
     */
    private ValueHolderV14<Map<Long, Integer>> handleReturn(OcBOrder ocBOrder, JSONObject resultJson) {
        ValueHolderV14<Map<Long, Integer>> vh = new ValueHolderV14<>();
        Map<Long, Integer> map = new HashMap<>();
        if ("0".equalsIgnoreCase(resultJson.getString("code"))) {
            if ("0".equalsIgnoreCase(resultJson.getJSONObject("data").getString("preoutResult"))) {
                vh.setCode(0);
                vh.setMessage(ocBOrder.getId() + "订单占用库存成功!");
            } else if ("3".equalsIgnoreCase(resultJson.getJSONObject("data").getString("preoutResult"))) {
                vh.setCode(3);
                JSONArray jsonArray = resultJson.getJSONObject("data").getJSONArray("outStockItemList");
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    //缺货数量
                    Integer qtyOutOfStock = jsonObject.getInteger("qtyOutOfStock");
                    Long itemId = jsonObject.getLong("sourceItemId");
                    map.put(itemId, qtyOutOfStock);
                }
                vh.setMessage(ocBOrder.getId() + "订单占用库存失败,明细库存缺货!");
            } else {
                vh.setCode(-1);
                vh.setMessage(ocBOrder.getId() + "订单占用库存返回状态异常!");
            }
        } else if ("-2".equalsIgnoreCase(resultJson.getString("code"))) {
            vh.setCode(-2);
            vh.setMessage(ocBOrder.getId() + "订单清空库存失败!" + resultJson.getString("message"));
        } else {
            vh.setCode(-1);
            vh.setMessage(ocBOrder.getId() + "订单库存占用失败!" + resultJson.getString("message"));
        }
        vh.setData(map);
        return vh;
    }


    /**
     * 清空并重新占用库存
     *
     * @param user
     * @param allOccupy false 如果清空失败之则直接返回-1  true返回-2
     * @return
     */
    public ValueHolderV14<Map<Long, Integer>> emptyAgainOccupy(OmsOccupyRelation occupyRelation, boolean allOccupy, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        OcBOrder ocBOrder = occupyRelation.getOcBOrder();
        try {
            if (CollectionUtils.isEmpty(occupyRelation.getOcBOrderItems())) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(ocBOrder.getId() + "_订单明细为空!");
                return vh;
            }
            //查找实体仓下面对应的逻辑仓
            List<Long> storeList = omsQueryWareHouseService.queryStoreList(ocBOrder.getCpCPhyWarehouseId());
            if (CollectionUtils.isEmpty(storeList)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(ocBOrder.getId() + "该实体仓下面逻辑仓为空!");
                return vh;
            }
            //逻辑仓优先级
            List<StStockPriorityRequest> stStockPriorityRequests = omsSyncStockStrategyService.queryStStockPriority(ocBOrder.getCpCShopId(), storeList);
            if (CollectionUtils.isEmpty(stStockPriorityRequests)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(ocBOrder.getId() + "该店铺逻辑发货仓为空!");
                return vh;
            }
//            SgSendBillCleanRequest cleanRequest = new SgSendBillCleanRequest();
//            cleanRequest.setSourceBillId(ocBOrder.getId());
//            //来源单据类型 1、零售发货  2、銷售發貨 3、采購退貨發貨  4、調撥發貨 5、銷售退貨發貨
//            cleanRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
//            //操作用户
//            cleanRequest.setLoginUser(SystemUserResource.getRootUser());
//            //控制没有逻辑发货单不报错
//            cleanRequest.setIsExist(true);
//            SgSendSaveWithPriorityRequest searchRequest = packageLogicSearchModel(occupyRelation, stStockPriorityRequests, user);
            if (log.isDebugEnabled()) {
                log.debug("OrderId[{}],OMS清空并重新占用库存 开始", ocBOrder.getId());
            }
            ValueHolderV14 v14 = null;//sgRpcService.cleanSaveSgBSend(cleanRequest, searchRequest, allOccupy);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OMS清空并重新占用库存 {} 结束", "OMS清空并重新占用库存", ocBOrder.getId()), v14.toJSONObject().toJSONString());
            }
            JSONObject resultJson = v14.toJSONObject();
            vh = handleReturn(ocBOrder, resultJson);
        } catch (Exception e) {
            log.error(LogUtil.format("清空并重新占用库存调整逻辑发货单服务queryChangeWareHouse 异常信息", "清空并重新占用库存调整逻辑发货单服务", ocBOrder.getId()), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单Id" + ocBOrder.getId() + "清空并重新占用库存执行失败!" + e.getMessage());
        }
        return vh;
    }


    /**
     * 重新占用库存
     *
     * @param user
     * @param occupyRelation false 如果清空失败之则直接返回-1  true返回-2
     * @return
     */
    public ValueHolderV14<Map<Long, Integer>> afreshOccupy(OmsOccupyRelation occupyRelation, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        OcBOrder ocBOrder = occupyRelation.getOcBOrder();
        try {
            if (CollectionUtils.isEmpty(occupyRelation.getOcBOrderItems())) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(ocBOrder.getId() + "_订单明细为空!");
                return vh;
            }
            //查找实体仓下面对应的逻辑仓
            List<Long> storeList = omsQueryWareHouseService.queryStoreList(ocBOrder.getCpCPhyWarehouseId());
            if (CollectionUtils.isEmpty(storeList)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(ocBOrder.getId() + "该实体仓下面逻辑仓为空!");
                return vh;
            }
            //逻辑仓优先级
            List<StStockPriorityRequest> stStockPriorityRequests = omsSyncStockStrategyService.queryStStockPriority(ocBOrder.getCpCShopId(), storeList);
            if (CollectionUtils.isEmpty(stStockPriorityRequests)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(ocBOrder.getId() + "该店铺逻辑发货仓为空!");
                return vh;
            }
//            SgSendBillCleanRequest cleanRequest = new SgSendBillCleanRequest();
//            cleanRequest.setSourceBillId(ocBOrder.getId());
//            //来源单据类型 1、零售发货  2、銷售發貨 3、采購退貨發貨  4、調撥發貨 5、銷售退貨發貨
//            cleanRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
//            //操作用户
//            cleanRequest.setLoginUser(SystemUserResource.getRootUser());
//            //控制没有逻辑发货单不报错
//            cleanRequest.setIsExist(true);
//            SgSendSaveWithPriorityRequest searchRequest = packageLogicSearchModel(occupyRelation, stStockPriorityRequests, user);
            if (log.isDebugEnabled()) {
                log.debug("OrderId[{}],OMS清空并重新占用库存 开始", ocBOrder.getId());
            }
            // @20201209 秦俊龙更改，由清空逻辑发货单并重新占单 更新为 直接重新占单
            ValueHolderV14 v14 = null;//sgRpcService.saveSendWithPriority(searchRequest);
            if (log.isDebugEnabled()) {
                log.debug("OrderId[{}],OMS清空并重新占用库存 {} 结束;", ocBOrder.getId(), v14.toJSONObject().toJSONString());
            }
            JSONObject resultJson = v14.toJSONObject();
            vh = handleReturn(ocBOrder, resultJson);
        } catch (Exception e) {
            log.error(LogUtil.format("清空并重新占用库存调整逻辑发货单服务 queryChangeWareHouse 异常信息", "清空并重新占用库存调整逻辑发货单服务", ocBOrder.getId()), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单Id" + ocBOrder.getId() + "清空并重新占用库存执行失败!" + e.getMessage());
        }
        return vh;
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleOccupy(OcBOrder ocBOrder, List<OcBOrderItem> itemList, ValueHolderV14<Map<Long, Integer>> resMap,
                             User user, String remark, List<IpBTimeOrderVip> ipBTimeOrderVipList) {
        for (IpBTimeOrderVip ipBTimeOrderVip : ipBTimeOrderVipList) {
            IpBTimeOrderVip ipBTimeOrderVipNew = new IpBTimeOrderVip();
            ipBTimeOrderVipNew.setStatus(TimeOrderVipStatusEnum.MATCHED.getValue());
            ipBTimeOrderVipNew.setModifierid(Long.valueOf(user.getId()));
            ipBTimeOrderVipNew.setModifiername(user.getName());
            ipBTimeOrderVipNew.setModifierename(user.getEname());
            ipBTimeOrderVipNew.setModifieddate(new Date());
            ipBTimeOrderVipNew.setSysremark(remark);
            QueryWrapper<IpBTimeOrderVip> wrapper = new QueryWrapper();
            wrapper.eq("OCCUPIED_ORDER_SN", ipBTimeOrderVip.getOccupiedOrderSn());

            int i = mapper.update(ipBTimeOrderVipNew, wrapper);
            if (i > 0) {
                timeOrderVipOccupyItemMapper.updateMatchedByOrderId(ipBTimeOrderVip.getId());
            }
        }

        Map<Long, Integer> data = resMap.getData();
        Long orderId = ocBOrder.getId();
        OcBOrder updateOrder = new OcBOrder();
        List<OcBOrderItem> updateItemList = new ArrayList<>();
        updateOrder.setId(orderId);
        if (resMap.getCode() == 0) {
            // 需求：在买家申请换货但是卖家未同意换货时先占单，占单成功同意换货占单失败不同意换货
            // 判断是否换货订单，若是占用库存成功回写中间表 1：占单成功
            ipTaobaoExchangeService.updateIpTaoBaoExchangeDisposeStatusByOcBOrder(ocBOrder, itemList, OccupancyStatusEnum.SUCCESS.getVal());
            //占用库存成功后，将未确认订单加入审核Task中，2020/03/30新增逻辑
            // 当开关开启走仓库拆单 （插入拆单task表），不开启 则不走仓库拆单*（插入传订单审核）
            if (splitOrderUtils.isOpenWareHouseSplitOrder(ocBOrder)) {
                // 插入仓库拆单任务表
                wmsTaskService.saveOrUpdateOcBWarehouseSplitTask(ocBOrder.getId(), user);
            } else {
                // 插入传wms表
                omsAuditTaskService.createOcBAuditTask(ocBOrder, OmsAuditTimeCalculateReason.OCCUPY);
            }

            updateOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            updateOrder.setSysremark("");
            updateOrder.setModifierename(user.getName());
            updateOrder.setModifieddate(new Date());
            for (OcBOrderItem orderItem : itemList) {
                OcBOrderItem item = new OcBOrderItem();
                item.setId(orderItem.getId());
                item.setQtyLost(BigDecimal.ZERO);
                item.setOcBOrderId(orderId);
                updateItemList.add(item);
            }

            omsOrderLogService.addUserOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), "占用库存成功", "", "", user);
        } else if (resMap.getCode() == 3) {
            // 需求：在买家申请换货但是卖家未同意换货时先占单，占单成功同意换货占单失败不同意换货
            // 判断是否换货订单，若是占用库存失败回写中间表 2：缺货
            ipTaobaoExchangeService.updateIpTaoBaoExchangeDisposeStatusByOcBOrder(ocBOrder, itemList, OccupancyStatusEnum.STOCK_OUT.getVal());
            for (OcBOrderItem orderItem : itemList) {
                OcBOrderItem item = new OcBOrderItem();
                Long itemId = orderItem.getId();
                item.setId(orderItem.getId());
                Integer qtyLost = data.get(itemId) == null ? 0 : data.get(itemId);
                item.setQtyLost(BigDecimal.valueOf(qtyLost));
                item.setOcBOrderId(orderId);
                updateItemList.add(item);
            }
            updateOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
//            updateOrder.setIsLackstock(OcBOrderConst.IS_STATUS_IY);
            omsOrderLogService.addUserOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_FAIL.getKey(), "占用库存失败", "", resMap.getMessage(), user);
        } else if (resMap.getCode() == -2) {
            log.error(this.getClass().getName() + " 释放或者占用库存异常", resMap.getMessage());
            throw new NDSException(resMap.getMessage());
        } else {
            // 需求：在买家申请换货但是卖家未同意换货时先占单，占单成功同意换货占单失败不同意换货
            // 判断是否换货订单，若是占用库存失败回写中间表  2：缺货
            ipTaobaoExchangeService.updateIpTaoBaoExchangeDisposeStatusByOcBOrder(ocBOrder, itemList, OccupancyStatusEnum.STOCK_OUT.getVal());
            //-1
            for (OcBOrderItem orderItem : itemList) {
                OcBOrderItem item = new OcBOrderItem();
                Long id = orderItem.getId();
                item.setId(id);
                item.setQtyLost(orderItem.getQty());
                item.setOcBOrderId(orderId);
                updateItemList.add(item);
            }
            updateOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
//            updateOrder.setIsLackstock(OcBOrderConst.IS_STATUS_IY);
            omsOrderLogService.addUserOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_FAIL.getKey(), "占用库存失败", "", resMap.getMessage(), user);
        }
        /* 更新主表信息*/
        BigDecimal countQty = itemList.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        updateOrder.setQtyAll(countQty);
        updateOrder.setSkuKindQty(BigDecimal.valueOf(itemList.size()));
        // 占库存以后重新计算订单所有商品的重量更新到主表（森马分物流需要执行相关物流策略）
//        BigDecimal weight = itemList.stream().map(item -> Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal weight = BigDecimal.ZERO;
        for (OcBOrderItem item : itemList) {
            BigDecimal standardWeight = Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO);
            BigDecimal qty = Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO);
            weight = weight.add(standardWeight.multiply(qty));
        }

        updateOrder.setWeight(weight);
        omsOrderService.updateOrderInfo(updateOrder);
        /* 更新主表明细*/
        if (updateItemList.size() > 0) {
            this.batchOrderItem(updateItemList, orderId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleOccupy(OcBOrder ocBOrder, List<OcBOrderItem> itemList, ValueHolderV14<Map<Long, Integer>> resMap, User user) {
        Map<Long, Integer> data = resMap.getData();
        Long orderId = ocBOrder.getId();
        OcBOrder updateOrder = new OcBOrder();
        List<OcBOrderItem> updateItemList = new ArrayList<>();
        updateOrder.setId(orderId);
        if (resMap.getCode() == 0) {
            // 需求：在买家申请换货但是卖家未同意换货时先占单，占单成功同意换货占单失败不同意换货
            // 判断是否换货订单，若是占用库存成功回写中间表 1：占单成功
            ipTaobaoExchangeService.updateIpTaoBaoExchangeDisposeStatusByOcBOrder(ocBOrder, itemList, OccupancyStatusEnum.SUCCESS.getVal());
            //占用库存成功后，将待审核订单加入审核Task中，2020/03/30新增逻辑

            log.info("进行订单拆单");
            // 当开关开启走仓库拆单 （插入拆单task表），不开启 则不走仓库拆单*（插入传订单审核）
            if (splitOrderUtils.isOpenWareHouseSplitOrder(ocBOrder)) {
                // 插入仓库拆单任务表
                wmsTaskService.saveOrUpdateOcBWarehouseSplitTask(ocBOrder.getId(), user);
            } else {
                // 插入传wms表
                omsAuditTaskService.createOcBAuditTask(ocBOrder, OmsAuditTimeCalculateReason.OCCUPY);
            }

            updateOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
//            updateOrder.setIsLackstock(0); // 占库存成功将缺货标记设置为0
            updateOrder.setSysremark("");
            updateOrder.setModifierename(user.getName());
            updateOrder.setModifieddate(new Date());
            // @20201209 订单合单加密信息
            // 需要传有地址信息的订单参数
            MD5Util.encryptOrderInfo4Merge(ocBOrder);
            updateOrder.setOrderEncryptionCode(ocBOrder.getOrderEncryptionCode());
            for (OcBOrderItem orderItem : itemList) {
                OcBOrderItem item = new OcBOrderItem();
                item.setId(orderItem.getId());
                item.setQtyLost(BigDecimal.ZERO);
                item.setOcBOrderId(orderId);
                updateItemList.add(item);
            }

            omsOrderLogService.addUserOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), "占用库存成功", "", "", user);
        } else if (resMap.getCode() == 3) {
            // 需求：在买家申请换货但是卖家未同意换货时先占单，占单成功同意换货占单失败不同意换货
            // 判断是否换货订单，若是占用库存失败回写中间表 2：缺货
            ipTaobaoExchangeService.updateIpTaoBaoExchangeDisposeStatusByOcBOrder(ocBOrder, itemList, OccupancyStatusEnum.STOCK_OUT.getVal());
            for (OcBOrderItem orderItem : itemList) {
                OcBOrderItem item = new OcBOrderItem();
                Long itemId = orderItem.getId();
                item.setId(orderItem.getId());
                Integer qtyLost = data.get(itemId) == null ? 0 : data.get(itemId);
                item.setQtyLost(BigDecimal.valueOf(qtyLost));
                item.setOcBOrderId(orderId);
                updateItemList.add(item);
            }
            updateOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
//            updateOrder.setIsLackstock(OcBOrderConst.IS_STATUS_IY);
            omsOrderLogService.addUserOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_FAIL.getKey(), "占用库存失败", "", resMap.getMessage(), user);
        } else if (resMap.getCode() == -2) {
            throw new NDSException(resMap.getMessage());
        } else {
            // 需求：在买家申请换货但是卖家未同意换货时先占单，占单成功同意换货占单失败不同意换货
            // 判断是否换货订单，若是占用库存失败回写中间表  2：缺货
            ipTaobaoExchangeService.updateIpTaoBaoExchangeDisposeStatusByOcBOrder(ocBOrder, itemList, OccupancyStatusEnum.STOCK_OUT.getVal());
            //-1
            for (OcBOrderItem orderItem : itemList) {
                OcBOrderItem item = new OcBOrderItem();
                Long id = orderItem.getId();
                item.setId(id);
                item.setQtyLost(orderItem.getQty());
                item.setOcBOrderId(orderId);
                updateItemList.add(item);
            }
            updateOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
//            updateOrder.setIsLackstock(OcBOrderConst.IS_STATUS_IY);
            String message = "占用库存失败";
            if (StringUtil.isNotEmpty(resMap.getMessage())) {
                message += "," + resMap.getMessage();
            }
            omsOrderLogService.addUserOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_FAIL.getKey(), message, "", resMap.getMessage(), user);
        }
        /* 更新主表信息*/
        BigDecimal countQty = itemList.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        updateOrder.setQtyAll(countQty);
        updateOrder.setSkuKindQty(BigDecimal.valueOf(itemList.size()));
        BigDecimal weight = BigDecimal.ZERO;
        for (OcBOrderItem item : itemList) {
            BigDecimal standardWeight = Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO);
            BigDecimal qty = Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO);
            weight = weight.add(standardWeight.multiply(qty));
        }
        updateOrder.setWeight(weight);
        omsOrderService.updateOrderInfo(updateOrder);
        /* 更新主表明细*/
        if (updateItemList.size() > 0) {
            this.batchOrderItem(updateItemList, orderId);
        }
    }

    /**
     * 替换商品后，主表(数量，sku种类数，重量)信息重新计算
     *
     * @param ocBOrder
     * @param itemList
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    public void exchangeSkuBuildOrderInfoHandle(OcBOrder ocBOrder, List<OcBOrderItem> itemList, User user) {
        Long orderId = ocBOrder.getId();
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(orderId);
        updateOrder.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
        updateOrder.setOccupyStatus(OmsParamConstant.INT_TWO);
        updateOrder.setSysremark("");
        updateOrder.setModifierename(user.getName());
        updateOrder.setModifieddate(new Date());
        // 订单合单加密信息，需要传有地址信息的订单参数
        MD5Util.encryptOrderInfo4Merge(ocBOrder);
        updateOrder.setOrderEncryptionCode(ocBOrder.getOrderEncryptionCode());
        omsOrderLogService.addUserOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.RELEASE_STOCK_SUCCESS.getKey(), "释放库存成功", "", "", user);
        /* 更新主表信息*/
        BigDecimal countQty = itemList.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        updateOrder.setQtyAll(countQty);
        updateOrder.setSkuKindQty(BigDecimal.valueOf(itemList.size()));
        //重新计算订单所有商品的重量更新到主表（分物流可能需要根据重量执行相关物流策略）
        BigDecimal weight = BigDecimal.ZERO;
        for (OcBOrderItem item : itemList) {
            BigDecimal standardWeight = Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO);
            BigDecimal qty = Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO);
            weight = weight.add(standardWeight.multiply(qty));
        }
        updateOrder.setWeight(weight);
        omsOrderService.updateOrderInfo(updateOrder);

    }


    private void batchOrderItem(List<OcBOrderItem> itemList, Long orderId) {
        for (OcBOrderItem orderItem : itemList) {
            omsOrderItemService.updateOcBOrderItem(orderItem, orderId);
        }
    }


    /**
     * 占用库存
     *
     * @param borderDto 订单信息
     * @param user      用户信息
     */
    public void occupyWareHouse(OcBOrder borderDto, User user) {
        try {
            //拿到更新后的对象
            OcBOrder order = ocBOrderMapper.selectById(borderDto.getId());
            List<OcBOrderItem> items = ocBorderItemMapper.selectOrderItemListAndReturn(borderDto.getId());
            //调用占用库存服务。占用成功后调用订单日志服务
            OmsOccupyRelation omsOccupyRelation = SgRpcService.getOmsOccupyRelation(order, items);
            SgOccupiedInventoryService sgOccupiedInventoryService = ApplicationContextHandle.getBean(SgOccupiedInventoryService.class);
            ValueHolderV14<Map<Long, Integer>> vh = sgOccupiedInventoryService.occupySearchStockWareHouse(omsOccupyRelation, user);
            sgOccupiedInventoryService.handleOccupy(order, items, vh, user);

            /**
             * 订单为缺货时，处理缺货订单，如果订单不是缺货，方法直接返回不做任何处理。
             */
            splitOutStockOrderService.processOutStockOrder(order.getId(), user);
        } catch (Exception ex) {
            log.error(LogUtil.format("调用占用库存异常", "调用占用库存异常", borderDto.getId()), Throwables.getStackTraceAsString(ex));
            throw new NDSException(ex);
        }
    }


    public ValueHolderV14<Map<Long, Integer>> shortageOccupyWareHouse(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, User user) {
        log.debug(LogUtil.format("执行占用库存服务开始", "执行占用库存服务开始", ocBOrder.getId()));
        try {
            //调用占用库存服务。占用成功后调用订单日志服务
            OmsOccupyRelation omsOccupyRelation = SgRpcService.getOmsOccupyRelation(ocBOrder, ocBOrderItems);
            log.debug(LogUtil.format("orderId={}执行占用库存服务,入参为----->>>>{}", "执行占用库存服务", ocBOrder.getId()), JSON.toJSONString(omsOccupyRelation));

            SgOccupiedInventoryService sgOccupiedInventoryService = ApplicationContextHandle.getBean(SgOccupiedInventoryService.class);
            ValueHolderV14<Map<Long, Integer>> vh = sgOccupiedInventoryService.occupySearchStockWareHouse(omsOccupyRelation, user);
            log.debug(LogUtil.format("orderId={}执行占用库存服务,出参为----->>>>{}", "执行占用库存服务", ocBOrder.getId()), JSON.toJSONString(vh));
            return vh;
        } catch (Exception ex) {
            log.error(LogUtil.format("调用占用库存异常", "调用占用库存异常", ocBOrder.getId()), Throwables.getStackTraceAsString(ex));
            throw new NDSException(ex.getMessage());
        }
    }


    /**
     * 清空并重新占单
     *
     * @param ocBOrder
     * @param itemList
     * @param user
     * @return
     */
    public ValueHolderV14<Map<Long, Integer>> emptyHandleOccupy(OcBOrder ocBOrder, List<OcBOrderItem> itemList, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查询明细数据为:{}", "清空并重新占单", ocBOrder.getId()), JSON.toJSONString(itemList));
        }
        OmsOccupyRelation relation = SgRpcService.getOmsOccupyRelation(ocBOrder, itemList);
        //报缺货
        relation.setWarningType(3);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用清空并占用库存入参:{}", "调用清空并占用库存", ocBOrder.getId()), JSON.toJSONString(relation));
        }
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        ValueHolderV14<Map<Long, Integer>> holderV14 = this.emptyAgainOccupy(relation, true, user);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("清空并占单返回:{}", "清空并占单返回", ocBOrder.getId()), JSON.toJSONString(holderV14));
        }
        return holderV14;
    }

    /**
     * 释放明细占用库存
     *
     * @param ocBOrder
     * @param itemList 注:传入需要释放库存的明细数据
     * @param user
     */
    public void emptyItemOccupyStock(OcBOrder ocBOrder, List<OcBOrderItem> itemList, User user) {
        try {
            for (OcBOrderItem ocBOrderItem : itemList) {
                ocBOrderItem.setQty(BigDecimal.ZERO);
            }
            OmsOccupyRelation omsOccupyRelation = SgRpcService.getOmsOccupyRelation(ocBOrder, itemList);
            SgOccupiedInventoryService sgOccupiedInventoryService = ApplicationContextHandle.getBean(SgOccupiedInventoryService.class);
            ValueHolderV14<Map<Long, Integer>> vh = sgOccupiedInventoryService.occupySearchStockWareHouse(omsOccupyRelation, user);
            if (!vh.isOK()) {
                throw new NDSException("释放库存失败!");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("释放库存失败", "释放库存失败", ocBOrder.getId()), Throwables.getStackTraceAsString(e));
            throw new NDSException("释放库存失败");
        }

    }

    /**
     * <AUTHOR>
     * @Date 15:03 2021/6/29
     * @Description 寻源
     */
    public void occupyOrder(OcBOrder ocBOrder, User operateUser) {
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectOrderItemListOccupy(ocBOrder.getId());
                if (CollectionUtils.isEmpty(orderItemList)) {
                    return;
                }
                //查询一下占单中间表是否有数据 防止定时任务查到后又卡单成功了
                List<Long> orderIds = ocBOccupyTaskMapper.selectOcBOccupyTaskListByOrderId(ocBOrder.getId());
                if (CollectionUtils.isEmpty(orderIds)) {
                    return;
                }
                OcBOrder ocBOrder1 = ocBOrderMapper.selectById(ocBOrder.getId());

                //toc残次订单寻源
                if (OmsOrderUtil.isToCCcOrder(ocBOrder1)) {
                    this.ccOccupy(ocBOrder1, operateUser, orderItemList);
                    return;
                }

                // 不管有没有数据 先删掉
                ocBOrderItemExtService.deleteByOrderId(ocBOrder.getId());
                String appointDistLevel3 = getAppointDistLevel3Service.getAppointDistLevel3(ocBOrder1, orderItemList);
                Map<Long, CpCShopProfileExt> cpCShopProfileMap = new HashMap<>();
                Map<Long, String> errorMap = new HashMap<>();
                if (StringUtils.isEmpty(appointDistLevel3)) {
                    cpCShopProfileMap = specifiedBusinessTypeExecute(ocBOrder, orderItemList, errorMap);
                    if (cpCShopProfileMap == null) return;
                } else {
                    // 等sg增加了字段 把三级分货组织数据塞进去
                    List<OcBOrderItemExt> ocBOrderItemExtList = new ArrayList<>();
                    for (OcBOrderItem ocBOrderItem : orderItemList) {
                        OcBOrderItemExt ocBOrderItemExt = new OcBOrderItemExt();
                        Long id = sequenceUtil.buildOrderItemExtSequenceId();
                        ocBOrderItemExt.setId(id);
                        ocBOrderItemExt.setOcBOrderId(ocBOrder.getId());
                        ocBOrderItemExt.setTid(ocBOrder.getTid());
                        ocBOrderItemExt.setOrderItemId(ocBOrderItem.getId());
                        ocBOrderItemExt.setDistCodeLevelThree(appointDistLevel3);
                        BaseModelUtil.initialBaseModelSystemField(ocBOrderItemExt);
                        ocBOrderItemExtList.add(ocBOrderItemExt);
                    }
                    ocBOrderItemExtService.insertList(ocBOrderItemExtList);
                }
                // 判断
                if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder1.getOrderStatus())) {
                    return;
                }
                // 周期购提货寻源前处理
                if (checkIsCyclePurchasePickUpOrder(ocBOrder)) {
                    if (ocBOrder.getCurrentCycleNumber() != null && ocBOrder.getCurrentCycleNumber() > 1) {
                        // 判断之前订单是否发货，如果未发货 延长卡单时间
                        if (!cyclePurchaseSubOrderService.updatePreDeliveryTimeByOccupy(ocBOrder, operateUser)) {
                            log.info(LogUtil.format("中台周期购提货订单上期未发货 orderId:{} 当前期：{}", "中台周期购提货订单寻源", ocBOrder.getId()), ocBOrder.getId(), ocBOrder.getCurrentCycleNumber());
                            return;
                        }
                    }
                }
                if (ocBOrder.getIsJcorder() == 1) {
                    //京东自流转订单寻源逻辑
                    jdOrderFlow(operateUser, ocBOrder, orderItemList, appointDistLevel3);
                } else {
                    SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest = buildSgRequest(ocBOrder, orderItemList, cpCShopProfileMap);
                    if (StringUtils.isNotEmpty(appointDistLevel3)) {
                        sgFindSourceStrategyC2SRequest.setAppointDistLevel3(appointDistLevel3);
                    }
                    String jsonValue = JSONObject.toJSONString(sgFindSourceStrategyC2SRequest);

                    log.info(LogUtil.format("{}.orderd订单,构建结束参数,{}", "占单入参", ocBOrder.getId()), ocBOrder.getId(), jsonValue);
                    String topic = Mq5Constants.TOPIC_R3_SG_TOBECONFIRM;
                    String tag = Mq5Constants.TAG_R3_SG_TOBECONFIRM;
                    //更新状态为寻源占单中
                    //寻源发送mq单据状态 更新为 "待分配"。
                    OcBOrder order = new OcBOrder();

                    order.setId(ocBOrder.getId());
                    order.setOrderStatus(OmsOrderStatus.OCCUPY_IN.toInteger());
                    order.setSysremark("寻源占单中");
                    order.setDetentionReason("");
                    //寻源后释放
                    if (ocBOrder.getIsDetention() != null && ocBOrder.getIsDetention() == 1) {
                        order.setIsDetention(0);
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                OrderLogTypeEnum.DETENTION_RELEASE.getKey(), "订单卡单释放", "", "", operateUser);
                    }
                    OrderUtil.handleOrderSku(orderItemList, order);
                    // 维护订单拓展表数据
                    ocBOrderMapper.update(order, Wrappers.<OcBOrder>lambdaUpdate()
                            .set(OcBOrder::getDetentionReasonId, null)
                            .eq(OcBOrder::getId, ocBOrder.getId()));

                    MqSendResult result = defaultProducerSend.sendTopic(topic, tag, jsonValue, null);

                    log.info("订单发送mq结果{}", result);
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), "OrderId=" + ocBOrder.getId() + "寻源占单中", "", "", operateUser);
                }
            } else {
                ocBOccupyTaskMapper.updateOcBOccupyTaskListByOrderId(ocBOrder.getId(), new Date());
            }
        } catch (Exception e) {
            log.info(LogUtil.format("寻源占单发送mq异常:{}", "寻源占单发送mq异常", ocBOrder.getId()), Throwables.getStackTraceAsString(e));
            //throw new NDSException(e);
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * toc残次寻源
     *
     * @param ocBOrder
     * @param operateUser
     * @param orderItemList
     */
    private void ccOccupy(OcBOrder ocBOrder, User operateUser, List<OcBOrderItem> orderItemList) {
        if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())) {
            return;
        }

        SgFindSourceStrategyCCRequest ccRequest = buildSgCcRequest(ocBOrder, orderItemList);
        if (ccRequest == null) {
            log.info(LogUtil.format("toc残次寻源明细为空 orderId:{}", "残次寻源明细参数为空", ocBOrder.getId()), ocBOrder.getId());
            return;
        }
        String jsonValue = JSONObject.toJSONString(ccRequest);

        log.info(LogUtil.format("{}.orderd订单,残次构建结束参数,{}", "占单入参", ocBOrder.getId()), ocBOrder.getId(), jsonValue);
        String topic = Mq5Constants.TOPIC_R3_SG_TOBECONFIRM_CC;
        String tag = Mq5Constants.TAG_R3_SG_TOBECONFIRM_CC;
        //更新状态为寻源占单中
        //寻源发送mq单据状态 更新为 "待分配"。
        OcBOrder order = new OcBOrder();

        order.setId(ocBOrder.getId());
        order.setOrderStatus(OmsOrderStatus.OCCUPY_IN.toInteger());
        order.setSysremark("寻源占单中");
        order.setDetentionReason("");
        //寻源后释放
        if (ocBOrder.getIsDetention() != null && ocBOrder.getIsDetention() == 1) {
            order.setIsDetention(0);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.DETENTION_RELEASE.getKey(), "订单卡单释放", "", "", operateUser);
        }
        OrderUtil.handleOrderSku(orderItemList, order);

        ocBOrderMapper.update(order, Wrappers.<OcBOrder>lambdaUpdate()
                .set(OcBOrder::getDetentionReasonId, null)
                .eq(OcBOrder::getId, ocBOrder.getId()));

        MqSendResult result = defaultProducerSend.sendTopic(topic, tag, jsonValue, null);

        log.info("toc残次订单发送mq结果{}", result);
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), "OrderId=" + ocBOrder.getId() + "寻源占单中", "", "", operateUser);
    }

    private Map<Long, CpCShopProfileExt> specifiedBusinessTypeExecute(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList, Map<Long, String> errorMap) {
        Map<Long, CpCShopProfileExt> cpCShopProfileMap = cpSaleOrganizationService.querySaleOrganization(orderItemList, ocBOrder.getCpCShopId(), errorMap);
        // 如果cpCShopProfileMap中存在value为null的话 则报错
        if (cpCShopProfileMap.containsValue(null)) {
            // 根据errorMap中特定的value 找到所有的key
            nullKeyExecute(ocBOrder, errorMap);
            return null;
        } else {
            // 先根据订单id 清空老的数据,然后再新增新查询到的数据
            ocBOrderItemExtService.insertAndDelete(ocBOrder, cpCShopProfileMap, orderItemList);
        }
        return cpCShopProfileMap;
    }

    private void nullKeyExecute(OcBOrder ocBOrder, Map<Long, String> errorMap) {
        List<Long> noneDmi12List = new ArrayList<>();
        List<Long> noneShopProfileList = new ArrayList<>();
        for (Long key : errorMap.keySet()) {
            String value = errorMap.get(key);
            if ("商品零级查询不到".equals(value)) {
                noneDmi12List.add(key);
            } else if ("平台店铺档案品项明细找不到".equals(value)) {
                noneShopProfileList.add(key);
            }
        }
        String message = "";
        if (CollectionUtils.isNotEmpty(noneDmi12List)) {
            List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItemListsByIds(ocBOrder.getId(), noneDmi12List);
            // 获取里面的skucode
            List<String> skuCodeList = ocBOrderItemList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
            message = String.join(",", skuCodeList);
            message = "商品零级查询不到:" + message;
        }
        if (CollectionUtils.isNotEmpty(noneShopProfileList)) {
            List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItemListsByIds(ocBOrder.getId(), noneShopProfileList);
            List<String> skuCodeList = ocBOrderItemList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(message)) {
                message = message + ";平台店铺档案品项明细找不到:" + String.join(",", skuCodeList);
            } else {
                message = "平台店铺档案品项明细找不到:" + String.join(",", skuCodeList);
            }
        }
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(ocBOrder.getId());
        updateOrder.setModifieddate(new Date());
        updateOrder.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IY);
        updateOrder.setIsException(OcBOrderConst.IS_STATUS_IY);
        updateOrder.setExceptionType(OcBOrderConst.ORGANIZATIONAL.toString());
        updateOrder.setExceptionExplain("请检查商品零级和平台店铺档案品项明细");
        updateOrder.setSysremark("请检查商品零级和平台店铺档案品项明细");
        ocBOrderMapper.updateById(updateOrder);
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), message, "", "", SystemUserResource.getRootUser());
        omsOccupyTaskService.addOcBOccupyOutStockTask(ocBOrder, null);
    }

    /**
     * 京东自流转订单寻源指定实体仓+批次逻辑
     *
     * @param operateUser   用户
     * @param ocBOrder      订单
     * @param orderItemList 订单明细
     * @return
     */
    private void jdOrderFlow(User operateUser, OcBOrder ocBOrder, List<OcBOrderItem> orderItemList, String appointDistLevel3) {
        log.info("Start.SgOccupiedInventoryService.jdOrderFlow");
        Long cpCShopId = ocBOrder.getCpCShopId();
        StCShopStrategyDO stShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(cpCShopId);
        String isJdFlowOccupy = stShopStrategyDO.getIsJdFlowOccupy();
        //先判断零售发货单对应的店铺在店铺策略中“京东自流转订单是否占单”的字段值，若为是则调用【指定实体仓占用】服务，反之则不调用；
        if (!YesNoEnum.Y.getKey().equals(isJdFlowOccupy)) {
            OcBOrder order = new OcBOrder();
            order.setId(ocBOrder.getId());
            order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            order.setSysremark("");
            order.setDetentionReason("");
            OrderUtil.handleOrderSku(orderItemList, order);
            ocBOrderMapper.update(order, Wrappers.<OcBOrder>lambdaUpdate()
                    .set(OcBOrder::getDetentionReasonId, null)
                    .eq(OcBOrder::getId, ocBOrder.getId()));

            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.PLATFORM_SEND.getKey(), "京东自流转订单店铺策略是否占单为否", "", "", operateUser);
        }
        SgFindSourceStrategy2BRequest findSourceStrategy2BRequest = buildJdSgRequest(ocBOrder, orderItemList, operateUser);
        if (StringUtils.isNotEmpty(appointDistLevel3)) {
            findSourceStrategy2BRequest.setAppointDistLevel3(appointDistLevel3);
        }
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.TOB_OCCUPY.getKey(), "OrderId=" + ocBOrder.getId() + "TOB开始重新占单", "", "", operateUser);

        //重新占单
        ValueHolderV14 result = sgRpcService.findSource2B(findSourceStrategy2BRequest);

        if (!result.isOK()) {
            //更新状态为TOB寻源占单中 更新为 "待寻源"
            OcBOrder updateModel = new OcBOrder();
            updateModel.setId(ocBOrder.getId());
            updateModel.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            OmsModelUtil.setDefault4Upd(updateModel, operateUser);
            ocBOrderMapper.updateById(updateModel);

            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.TOB_OCCUPY.getKey(), "京东自流转订单重新占单失败:" +
                            result.getMessage(), "", "", operateUser);

        } else {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.TOB_OCCUPY.getKey(), "京东自流转订单重新占单成功", "", "", operateUser);
            OcBOrder updateModel = new OcBOrder();
            updateModel.setId(ocBOrder.getId());
            updateModel.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            OmsModelUtil.setDefault4Upd(updateModel, operateUser);
            ocBOrderMapper.updateById(updateModel);
            //寻源成功自动审核
            OrderICheckRequest orderCheckRequest = new OrderICheckRequest();
            Long[] ids = new Long[1];
            ids[0] = ocBOrder.getId();
            orderCheckRequest.setIds(ids);
            orderCheckRequest.setType(1L);
            try {
                ValueHolderV14 vh = omsOrderAuditService.orderCheck(orderCheckRequest, operateUser);
                if (!vh.isOK()) {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ORDER_EXAMINE.getKey(), "京东自流转订单审核失败:" +
                                    vh.getMessage(), "", "", operateUser);
                }
            } catch (NDSException e) {
                log.error(LogUtil.format("error={}", "SgOccupiedInventoryService.Jcorder.error"),
                        Throwables.getStackTraceAsString(e));
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_EXAMINE.getKey(), "京东自流转订单审核异常!", "", "", operateUser);
            }
        }
    }

    /**
     * <AUTHOR>
     * @Date 15:54 2021/7/5
     * @Description 构建sg参数
     */
    private SgFindSourceStrategyC2SRequest buildSgRequest(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList, Map<Long, CpCShopProfileExt> cpCShopProfileMap) {
        SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest = new SgFindSourceStrategyC2SRequest();
        sgFindSourceStrategyC2SRequest.setSourceBillId(ocBOrder.getId());
        sgFindSourceStrategyC2SRequest.setSourceBillNo(ocBOrder.getBillNo());
        sgFindSourceStrategyC2SRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        sgFindSourceStrategyC2SRequest.setBillDate(ocBOrder.getOrderDate());
        //指定物流寻源
        if (ocBOrder.getAppointLogisticsId() != null) {
            Map<Long, String> logisticsInfo = new HashMap<>();
            logisticsInfo.put(ocBOrder.getAppointLogisticsId(), ocBOrder.getAppointLogisticsEname());
            sgFindSourceStrategyC2SRequest.setLogisticsInfo(logisticsInfo);
        }

        if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
            //查询JITX寻仓结果,存在寻仓结果排除对应仓库寻源
            List<IpBJitxDeliveryRecord> deliveryRecordList = jitxDeliveryRecordMapper
                    .selectList(new LambdaQueryWrapper<IpBJitxDeliveryRecord>()
                            .eq(IpBJitxDeliveryRecord::getTid, ocBOrder.getTid())
                            .eq(IpBJitxDeliveryRecord::getOccupyStatus, VipConstant.JITX_DELIVERY_RECORD_STATUS_OCCUPIED)
                            .eq(IpBJitxDeliveryRecord::getIsactive, R3CommonResultConstants.VALUE_Y));
            if (CollectionUtils.isNotEmpty(deliveryRecordList)) {
                List<OcBJitxDealerOrderTask> dealerOrderTasks = taskMapper
                        .selectList(new LambdaQueryWrapper<OcBJitxDealerOrderTask>()
                                .eq(OcBJitxDealerOrderTask::getTid, ocBOrder.getTid())
                                .eq(OcBJitxDealerOrderTask::getType, JitxDealerTaskTypeEnum.YY_OCCUPY.getCode())
                                .eq(OcBJitxDealerOrderTask::getIsactive, R3CommonResultConstants.VALUE_Y));
                if (CollectionUtils.isNotEmpty(dealerOrderTasks)) {
                    dealerOrderTasks.sort(Comparator.comparing(OcBJitxDealerOrderTask::getCreationdate));
                    Long warehouseId = dealerOrderTasks.get(dealerOrderTasks.size() - 1).getCpCPhyWarehouseId();
                    CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(warehouseId);
                    if (!ObjectUtils.isEmpty(cpCPhyWarehouse)) {
                        sgFindSourceStrategyC2SRequest.setExcludeWarehouseEcode(cpCPhyWarehouse.getEcode());
                    }
                }
            }
            //唯品会JITX 已寻仓 直接指定实体仓优先占用
            if (StringUtils.isNotEmpty(ocBOrder.getCpCPhyWarehouseEcode())) {
                sgFindSourceStrategyC2SRequest.setWarehouseEcode(ocBOrder.getCpCPhyWarehouseEcode());
            } else {
                // 根据唯品会仓库编码 查询有多少个对应的中台实体仓编码
                StCVipcomJitxWarehouse jitxWarehouse = vipcomJitxWarehouseService.queryJitxCapacity(
                        ocBOrder.getCpCShopId(), ocBOrder.getJitxRequiresDeliveryWarehouseId(), null);
                boolean queryAll = true;
                List<StCVipcomJitxWarehouse> vipcomJitxWarehouseList = new ArrayList<>();
                if (jitxWarehouse != null) {
                    vipcomJitxWarehouseList = vipcomJitxWarehouseService.queryByShopIdAndJitxUnShopWarehouseEcode(
                            ocBOrder.getCpCShopId(), jitxWarehouse.getVipcomUnshopWarehouseEcode());
                    if ((CollectionUtils.isNotEmpty(vipcomJitxWarehouseList) && vipcomJitxWarehouseList.size() > 1)) {
                        queryAll = false;
                    } else {
                        //补寄订单也只从唯品会仓库编码对应的仓库发货
                        IpBJitxOrder ipBJitxOrder = ipJitxOrderService.selectJitxOrderOnlyMain(ocBOrder.getTid());
                        if (JitxOrderTypeEnum.REDELIVER_TRANSPORT.getKey().equals(ipBJitxOrder.getOrderType())) {
                            queryAll = false;
                        }
                    }
                }
                if (queryAll) {
                    List<StCVipcomJitxWarehouse> jitxWarehouseList =
                            vipcomJitxWarehouseService.queryVipWarehouseListByShopId(ocBOrder.getCpCShopId());
                    if (CollectionUtils.isEmpty(jitxWarehouseList)) {
                        sgFindSourceStrategyC2SRequest.setWarehouseEcode("唯品会仓库对照表未维护");
                    }
                    sgFindSourceStrategyC2SRequest.setWarehouseEcode(jitxWarehouseList.stream()
                            .map(StCVipcomJitxWarehouse::getCpCPhyWarehouseEcode)
                            .collect(Collectors.joining(",")));
                } else {
                    sgFindSourceStrategyC2SRequest.setWarehouseEcode(vipcomJitxWarehouseList.stream()
                            .map(StCVipcomJitxWarehouse::getCpCPhyWarehouseEcode)
                            .collect(Collectors.joining(",")));
                }

            }
            //JITX订单不允许拆单占用
            sgFindSourceStrategyC2SRequest.setSplitType(StrategyConstants.ORDER_SPLIT_TYPE_NO);
        } else {
            String splitType = occupySplitTypeService.splitType(ocBOrder.getCpCShopId(), ocBOrder.getId());
            log.info("{}.orderd订单占单类型为{}", ocBOrder.getId(), splitType);
            sgFindSourceStrategyC2SRequest.setSplitType(splitType);
            //定金预售的默认仓库不拆
            if (omsOrderAdvanceParseService.checkIsDepositPreSale(ocBOrder)) {
                sgFindSourceStrategyC2SRequest.setSplitType("3");
            }
            if (StringUtils.isNotEmpty(ocBOrder.getCpCPhyWarehouseEcode())) {
                sgFindSourceStrategyC2SRequest.setWarehouseEcode(ocBOrder.getCpCPhyWarehouseEcode());
            }

            // 如果是旺店通下推的订单 不走缺货拆单
            if (OmsOrderUtil.wdtPlatformSend(ocBOrder)) {
                // 设置为缺货不拆单 同时添加操作日志
                sgFindSourceStrategyC2SRequest.setSplitType("3");
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.OCCUPY.getKey(), "旺店通下发订单 不走缺货拆", "", "", SystemUserResource.getRootUser());
            }

            // toc残次订单跳过
            if (OmsOrderUtil.isToCCcOrder(ocBOrder)) {
                // 设置为缺货不拆单 同时添加操作日志
                sgFindSourceStrategyC2SRequest.setSplitType("3");
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.OCCUPY.getKey(), "toc残次订单 不走缺货拆", "", "", SystemUserResource.getRootUser());
            }
        }

        sgFindSourceStrategyC2SRequest.setShopId(ocBOrder.getCpCShopId());
        sgFindSourceStrategyC2SRequest.setShopTitle(ocBOrder.getCpCShopTitle());
        sgFindSourceStrategyC2SRequest.setProvinceId(ocBOrder.getCpCRegionProvinceId());
        sgFindSourceStrategyC2SRequest.setCityId(ocBOrder.getCpCRegionCityId());
        sgFindSourceStrategyC2SRequest.setAreaId(ocBOrder.getCpCRegionAreaId());
        //平台单号
        sgFindSourceStrategyC2SRequest.setTid(ocBOrder.getSourceCode());
        //是否送货上门（送货上门在寻源逻辑里面只会寻到大宝仓）
        sgFindSourceStrategyC2SRequest.setIsDeliveryToDoor(YesOrNoEnum.YES.getCode().equals(ocBOrder.getIsDeliveryToDoor()));
        sgFindSourceStrategyC2SRequest.setIsTobOrder(OmsBusinessTypeUtil.isToBOrder(ocBOrder));
        sgFindSourceStrategyC2SRequest.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
        //构建 明显
        List<SkuItemC2S> skuItems = buildSgSkuItemC2S(orderItemList, cpCShopProfileMap);
        // sgFindSourceStrategyC2SRequest.setSplitType("1");
        sgFindSourceStrategyC2SRequest.setSkuItems(skuItems);
        return sgFindSourceStrategyC2SRequest;
    }

    /**
     * <AUTHOR>
     * @Date 16:17 2021/7/5
     * @Description 构建SgSku 参数
     */
    public List<SkuItemC2S> buildSgSkuItemC2S(List<OcBOrderItem> orderItemList, Map<Long, CpCShopProfileExt> cpCShopProfileMap) {
        //赠品是否允许拆，true 赠品当单独商品传过去 ，false 正常传
        Boolean isSplit = isGiftSplit();
        List<SkuItemC2S> skuItemC2SList = new ArrayList<>();
        List<OcBOrderItem> normalItems = orderItemList.stream().filter(p -> p.getProType() != null && p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        //未拆分的组合商品
        List<OcBOrderItem> groupItems = orderItemList.stream().filter(p -> p.getProType() != null && p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        Map<String, OcBOrderItem> groupItemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(groupItems)) {
            groupItemMap = groupItems.stream().collect(Collectors.toMap(OcBOrderItem::getGroupGoodsMark, Function.identity(), (key1, key2) -> key2));
        }
        Map<String, List<OcBOrderItem>> ooidMap = new HashMap<>();
        List<OcBOrderItem> noGiftItems = normalItems.stream().filter(p -> p.getIsGift() == null || p.getIsGift() == 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noGiftItems)) {
            ooidMap = noGiftItems.stream().filter(p -> StringUtils.isNotEmpty(p.getOoid())).collect(Collectors.groupingBy(OcBOrderItem::getOoid));
        }
        for (OcBOrderItem ocBOrderItem : normalItems) {
            SkuItemC2S skuItemC2S = new SkuItemC2S();
            if (StringUtils.isNotEmpty(ocBOrderItem.getTimeOrderId())) {
                IpBTimeOrderVip timeOrderVip = mapper.selectById(ocBOrderItem.getTimeOrderId());
                if (!ObjectUtils.isEmpty(timeOrderVip)) {
                    skuItemC2S.setTimeOrderId(Long.valueOf(ocBOrderItem.getTimeOrderId()));
                    skuItemC2S.setTimeOrderNo(timeOrderVip.getOccupiedOrderSn());
                }
            }
            if (cpCShopProfileMap != null && cpCShopProfileMap.get(ocBOrderItem.getId()) != null) {
                CpCShopProfile cpCShopProfile = cpCShopProfileMap.get(ocBOrderItem.getId()).getCpCShopProfile();
                skuItemC2S.setSalesDepartmentCode(cpCShopProfile.getSalesDepartmentCode());
                skuItemC2S.setSalesGroupCode(cpCShopProfile.getSalesGroupCode());
                skuItemC2S.setCategoryCode(cpCShopProfileMap.get(ocBOrderItem.getId()).getCategoryCode());
            }
            skuItemC2S.setSourceItemId(ocBOrderItem.getId());
            skuItemC2S.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            skuItemC2S.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            skuItemC2S.setQty(ocBOrderItem.getQty());
            if (ocBOrderItem.getQty() != null && ocBOrderItem.getStandardWeight() != null) {
                skuItemC2S.setItemTotWeight(ocBOrderItem.getStandardWeight().multiply(ocBOrderItem.getQty()));
            }
            skuItemC2S.setProType(ocBOrderItem.getProType().toString());
            // 贴标
            skuItemC2S.setPasteLabel(ocBOrderItem.getReserveVarchar02());
            String giftRelation = ocBOrderItem.getGiftRelation();
            String groupGoodsMark = ocBOrderItem.getGroupGoodsMark();
            if (StringUtils.isNotEmpty(groupGoodsMark)) {
                OcBOrderItem item = groupItemMap.get(groupGoodsMark);
                SgGroupSkuInfo skuInfo = new SgGroupSkuInfo();
                if (item != null) {
                    skuInfo.setGroupSkuId(item.getPsCSkuId());
                    skuInfo.setGroupSkuEcode(item.getPsCSkuEcode());
                }
                String canSplit = ocBOrderItem.getCanSplit();
                if ("Y".equals(canSplit)) {
                    skuInfo.setIsSpilt(Boolean.TRUE);
                }
                skuItemC2S.setGroupSkuInfo(skuInfo);
            }
            Integer isGift = ocBOrderItem.getIsGift();
            if (isGift != null && isGift == 1) {
                SgGiftSkuInfo skuInfo = new SgGiftSkuInfo();
                if (StringUtils.isNotEmpty(ocBOrderItem.getGiftRelation())) {
                    String ooid = ocBOrderItem.getOoid();
                    if (StringUtils.isNotEmpty(ooid)) {
                        List<OcBOrderItem> orderItems = ooidMap.get(ooid);
                        if (CollectionUtils.isNotEmpty(orderItems)) {
                            OcBOrderItem item = orderItems.get(0);
                            skuInfo.setBindingSkuId(item.getPsCSkuId());
                            skuInfo.setBindingSkuEcode(item.getPsCSkuEcode());
                        }
                    }
                    Integer isGiftSplit = ocBOrderItem.getIsGiftSplit();
                    if (isGiftSplit != null && isGiftSplit == 2) {
                        skuInfo.setIsSpilt(Boolean.TRUE);
                    }
                } else {
                    Integer isGiftSplit = ocBOrderItem.getIsGiftSplit();
                    if (isGiftSplit != null && isGiftSplit == 2) {
                        skuInfo.setIsSpilt(Boolean.TRUE);
                    }
                }
                skuItemC2S.setSgGiftSkuInfo(skuInfo);
                skuItemC2S.setProType(SkuType.GIFT_RELATION + "");
            }
            skuItemC2S.setSkuId(ocBOrderItem.getSkuNumiid());
            skuItemC2S.setNumiid(ocBOrderItem.getNumIid());
            skuItemC2S.setPsCProId(ocBOrderItem.getPsCProId());
            skuItemC2S.setPsCProEcode(ocBOrderItem.getPsCProEcode());
            skuItemC2S.setPsCProdim4Id(ocBOrderItem.getMDim4Id());
            skuItemC2S.setPsCProdimId(ocBOrderItem.getMDim6Id());
            buildExpiryDate(ocBOrderItem, skuItemC2S);
            skuItemC2SList.add(skuItemC2S);
        }

        return skuItemC2SList;
    }

    private void buildExpiryDate(OcBOrderItem ocBOrderItem, SkuItemC2S skuItemC2S) {
        Integer expiryDateType = ocBOrderItem.getExpiryDateType();
        if (expiryDateType != null && expiryDateType == 1) {
            String expiryDateRange = ocBOrderItem.getExpiryDateRange();
            if (StringUtils.isNotEmpty(expiryDateRange)) {
                String[] split = expiryDateRange.split("-");
                skuItemC2S.setBeginProduceDate(split[0]);
                skuItemC2S.setEndProduceDate(split[1]);
            }
        }
        if (expiryDateType != null && expiryDateType == 2) {
            String expiryDateRange = ocBOrderItem.getExpiryDateRange();
            if (StringUtils.isNotEmpty(expiryDateRange)) {
                String[] split = expiryDateRange.split("-");
                //计算日期
                skuItemC2S.setBeginProduceDate(computeDate(Integer.parseInt(split[1])));
                skuItemC2S.setEndProduceDate(computeDate(Integer.parseInt(split[0])));
            }
        }
    }

    private String computeDate(Integer day) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(new Date());
        ca.add(Calendar.DATE, 0 - day);
        Date time = ca.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        return format.format(time);
    }


    /**
     * description:赠品是否允许拆
     *
     * @Author: liuwenjin
     * @Date 2021/11/6 1:16 下午
     */
    private boolean isGiftSplit() {
        String isGiftSplit = propertiesConf.getProperty("r3.oms.to.sg.tobeConfirm.giftSplit", "true");
        return StringUtils.equalsIgnoreCase(isGiftSplit, "true");
    }

    /**
     * 判断订单是否周期购订单
     *
     * @param orderInfo 订单信息
     * @return true=周期购
     */
    private boolean checkIsCyclePurchasePickUpOrder(OcBOrder orderInfo) {
        return OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(orderInfo.getBusinessTypeCode())
                || OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(orderInfo.getBusinessTypeCode());
    }

    /**
     * <AUTHOR>
     * @Date 12:14 2021/7/6
     * @Description 分仓后的业务
     */
    public void occupyOtherService(OcBOrderRelation orderInfo, User operateUser) {
        // 未退款成功明细
        List<OcBOrderItem> selectUnSuccessRefundList = omsOrderItemService.selectUnSuccessRefund(orderInfo.getOrderId());
        if (CollectionUtils.isNotEmpty(selectUnSuccessRefundList)) {
            BigDecimal weight = selectUnSuccessRefundList
                    .stream()
                    .map(item -> Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO)
                            .multiply(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO)))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            orderInfo.getOrderInfo().setWeight(weight);
        }
        OcBOrder resultOcBOrder = omsOrderService.selectOrderInfo(orderInfo.getOrderId());
        orderInfo.setOrderInfo(resultOcBOrder);
        if (splitOrderUtils.isOpenWareHouseSplitOrder(resultOcBOrder)) {
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(resultOcBOrder.getOrderStatus())) {
                StCWarehouseQueryResult stCWarehouseQueryResult =
                        stRpcService.getStCWarehouseQueryResultByWareHouseId(resultOcBOrder.getCpCPhyWarehouseId());
                if (stCWarehouseQueryResult != null
                        && ("Y".equals(stCWarehouseQueryResult.getStCWarehouse().getIsGoodsSplit())
                        || "Y".equals(stCWarehouseQueryResult.getStCWarehouse().getIsSkuSplit()))
                        && !orderInfo.getOrderInfo().getPlatform().equals(PlatFormEnum.ALIBABAASCP.getCode())) {
                    // 占单阶段 只做SKU/SPU拆单
                    stCWarehouseQueryResult.getStCWarehouse().setIsSexSplit("N");
                    stCWarehouseQueryResult.getStCWarehouse().setIsBrandSplit("N");
                    Boolean splitResult = omsOrderAutoSplitByGoodsService.doSplitOrder(operateUser,
                            orderInfo, stCWarehouseQueryResult, OmsOrderStatus.UNCONFIRMED.toInteger());
                }
            }
        }
        //  自动执行HOLD单策略
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("占单分物流结束后调用自动执行HOLD单策略", "占单分物流结束后调用自动执行HOLD单策略", orderInfo.getOrderId()));
        }
        OcBOrderParam param = new OcBOrderParam();
        param.setOcBOrder(orderInfo.getOrderInfo());
        param.setOrderItemList(orderInfo.getOrderItemList());
        //自动hold单
        omsAutoHoldNewService.autoHandleHoldOrder(param, operateUser);
    }

    /**
     * description:判断是否o2o
     *
     * @Author: liuwenjin
     * @Date 2021/11/15 11:42 上午
     */
    public boolean isO2OOrder(OcBOrder order) {
        if (null == order) {
            return false;
        }
        Integer platformId = order.getPlatform();
        platformId = Objects.isNull(platformId) ? -1 : platformId;
        if (platformId.equals(PlatFormEnum.POS)) {
            return true;
        } else {
            CpCPhyWarehouse wareHouse = cpRpcService.queryByWarehouseId(order.getCpCPhyWarehouseId());
            if (wareHouse == null) {
                throw new NDSException("通过订单实体仓ID获取实体仓信息获取的对象为NULL");
            } else {
                return StringUtils.equals(wareHouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE_STORE_02);
            }
        }
    }

    private SgFindSourceStrategy2BRequest buildJdSgRequest(OcBOrder order, List<OcBOrderItem> orderItemList, User operateUser) {
        SgFindSourceStrategy2BRequest findSourceStrategy2BRequest = new SgFindSourceStrategy2BRequest();
        findSourceStrategy2BRequest.setWarehouseId(order.getCpCPhyWarehouseId());
        findSourceStrategy2BRequest.setSourceBillId(order.getId());
        findSourceStrategy2BRequest.setSourceBillNo(order.getBillNo());
        findSourceStrategy2BRequest.setTid(order.getTid());
        findSourceStrategy2BRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        findSourceStrategy2BRequest.setBillDate(order.getOrderDate());
        findSourceStrategy2BRequest.setShopId(order.getCpCShopId());
        findSourceStrategy2BRequest.setProvinceId(order.getCpCRegionProvinceId());
        findSourceStrategy2BRequest.setCityId(order.getCpCRegionCityId());
        findSourceStrategy2BRequest.setAreaId(order.getCpCRegionAreaId());
        findSourceStrategy2BRequest.setUser(operateUser);

        List<SkuItem2B> skuItem2BList = new ArrayList<>();

        //条码维度 【效期,占用数量】
        Map<String, BigDecimal> produceDateAndQty = new HashMap<>();

        for (OcBOrderItem ocBOrderItem : orderItemList) {

            Long id = ocBOrderItem.getId();
            String expiryDateRange = ocBOrderItem.getExpiryDateRange();
            if (StringUtils.isNotBlank(expiryDateRange)) {
                String[] split = expiryDateRange.split(",");
                for (int i = 0; i < split.length; i++) {
                    String info = split[i];
                    String produceDate = info.substring(0, info.lastIndexOf("("));
                    BigDecimal qty = new BigDecimal(StringUtils.substringBetween(info, "(", ")"));
                    produceDateAndQty.put(produceDate, qty);
                }
            }
            SkuItem2B skuItem2B = new SkuItem2B();
            skuItem2B.setSourceItemId(id);
            skuItem2B.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            skuItem2B.setSkuId(ocBOrderItem.getSkuNumiid());
            skuItem2B.setNumiid(ocBOrderItem.getNumIid());
            skuItem2B.setPsCProId(ocBOrderItem.getPsCProId());
            skuItem2B.setPsCProdimId(ocBOrderItem.getMDim6Id());
            skuItem2B.setQty(ocBOrderItem.getQty());
            skuItem2B.setLabelingRequirements(ocBOrderItem.getReserveVarchar02());

            skuItem2B.setQtyMap(produceDateAndQty);
            skuItem2BList.add(skuItem2B);
        }

        findSourceStrategy2BRequest.setSkuItems(skuItem2BList);
        return findSourceStrategy2BRequest;
    }

    /**
     * toc残次寻源参数构建
     *
     * @param ocBOrder
     * @param orderItemList
     * @return
     */
    private SgFindSourceStrategyCCRequest buildSgCcRequest(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList) {
        SgFindSourceStrategyCCRequest ccRequest = new SgFindSourceStrategyCCRequest();
        ccRequest.setSourceBillId(ocBOrder.getId());
        ccRequest.setSourceBillNo(ocBOrder.getBillNo());
        ccRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        ccRequest.setBillDate(ocBOrder.getOrderDate());
        ccRequest.setTid(ocBOrder.getSourceCode());
        ccRequest.setSourceBillDate(new Date());
        ccRequest.setShopId(ocBOrder.getCpCShopId());
        ccRequest.setProvinceId(ocBOrder.getCpCRegionProvinceId());

        //指定仓寻源
        if (StringUtils.isNotBlank(ocBOrder.getCpCPhyWarehouseEcode())) {
            ccRequest.setAppointWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        }

        //订单明细过滤未拆分的组合品
        List<OcBOrderItem> normalItems = orderItemList.stream().filter(p -> p.getProType() != null && p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalItems)) {
            return null;
        }

        List<SkuItemCC> skuItemCCList = Lists.newArrayList();
        for (OcBOrderItem ocBOrderItem : normalItems) {
            SkuItemCC skuItemCC = new SkuItemCC();
            skuItemCC.setSourceItemId(ocBOrderItem.getId());
            skuItemCC.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            skuItemCC.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            skuItemCC.setQty(ocBOrderItem.getQty());
            skuItemCC.setPsCProId(ocBOrderItem.getPsCProId());
            skuItemCC.setPsCProEcode(ocBOrderItem.getPsCProEcode());
            skuItemCC.setPsCProdim4Id(ocBOrderItem.getMDim4Id());
            skuItemCC.setPsCProdimId(ocBOrderItem.getMDim6Id());
            //效期
            buildCcExpiryDate(ocBOrderItem, skuItemCC);

            skuItemCCList.add(skuItemCC);
        }

        ccRequest.setSkuItems(skuItemCCList);
        return ccRequest;
    }

    /**
     * 残次效期
     *
     * @param ocBOrderItem
     * @param skuItemCC
     */
    private void buildCcExpiryDate(OcBOrderItem ocBOrderItem, SkuItemCC skuItemCC) {
        Integer expiryDateType = ocBOrderItem.getExpiryDateType();
        if (expiryDateType != null && expiryDateType == 1) {
            String expiryDateRange = ocBOrderItem.getExpiryDateRange();
            if (StringUtils.isNotEmpty(expiryDateRange)) {
                String[] split = expiryDateRange.split("-");
                skuItemCC.setBeginProduceDate(split[0]);
                skuItemCC.setEndProduceDate(split[1]);
            }
        }
        if (expiryDateType != null && expiryDateType == 2) {
            String expiryDateRange = ocBOrderItem.getExpiryDateRange();
            if (StringUtils.isNotEmpty(expiryDateRange)) {
                String[] split = expiryDateRange.split("-");
                //计算日期
                skuItemCC.setBeginProduceDate(computeDate(Integer.parseInt(split[1])));
                skuItemCC.setEndProduceDate(computeDate(Integer.parseInt(split[0])));
            }
        }
    }


}
