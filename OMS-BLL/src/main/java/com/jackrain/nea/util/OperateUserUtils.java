package com.jackrain.nea.util;

import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.web.face.User;

import java.util.Date;

/**
 * @Author: 黄世新
 * @Date: 2020/4/2 1:37 下午
 * @Version 1.0
 */
public class OperateUserUtils {


    public static void saveOperator(BaseModel model, User operateUser) {
        if (operateUser != null) {
            model.setAdOrgId((long) operateUser.getOrgId());
            // @20200729 bug-19637:新增和修改名取值统一逻辑
            model.setOwnername(operateUser.getName());
            model.setAdClientId((long) operateUser.getClientId());
            model.setOwnerid(Long.valueOf(operateUser.getId()));
            model.setCreationdate(new Date());
            model.setModifierid(Long.valueOf(operateUser.getId()));
            model.setModifieddate(new Date());
            model.setModifiername(operateUser.getName());
            model.setIsactive("Y");
        } else {
            User rootUser = SystemUserResource.getRootUser();
            model.setAdOrgId((long) rootUser.getOrgId());
            model.setOwnername(rootUser.getName());
            model.setAdClientId((long) rootUser.getClientId());
            model.setOwnerid(Long.valueOf(rootUser.getId()));
            model.setCreationdate(new Date());
            model.setModifierid(Long.valueOf(rootUser.getId()));
            model.setModifieddate(new Date());
            model.setModifiername(rootUser.getName());
            model.setIsactive("Y");
        }
    }


    /**
     * 赋值默认值(init)
     *
     * @param model
     * @param operateUser
     */
    public static void defaultOperator(BaseModel model, User operateUser) {
        if (operateUser == null) {
            operateUser = SystemUserResource.getRootUser();
        }
        model.setAdOrgId((long) operateUser.getOrgId());
        model.setOwnername(operateUser.getName());
        model.setAdClientId((long) operateUser.getClientId());
        model.setOwnerid(Long.valueOf(operateUser.getId()));
        model.setModifierid(Long.valueOf(operateUser.getId()));
        model.setModifiername(operateUser.getName());
        model.setIsactive(IsActiveEnum.Y.getKey());
        model.setModifieddate(new Date());
        model.setCreationdate(new Date());
    }
}
