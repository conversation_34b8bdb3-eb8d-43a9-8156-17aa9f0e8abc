package com.jackrain.nea.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AcBeanUtils {

    /**
     * 组织中心、所属公司、可用状态
     *
     * @param baseModelDO
     * @param user
     */
    public static void makeCreateAd(BaseModel baseModelDO, User user) {
        //所属公司
        baseModelDO.setAdClientId((long) user.getClientId());
        //所属组织
        baseModelDO.setAdOrgId((long) user.getOrgId());
        //是否启用
        baseModelDO.setIsactive("Y");
    }

    /**
     * @param jsonObject
     * @return void
     * @Descroption 反审-客审人置空
     * @Date 2019/3/26
     */
    public static void makeCancelField(JSONObject jsonObject) {
        jsonObject.put("GUEST_TRIAL_ID", null);
        jsonObject.put("GUEST_TRIAL_TIME", null);
        jsonObject.put("GUEST_TRIAL_NAME", null);
        jsonObject.put("GUEST_TRIAL_ENAME", null);
    }

    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 获取审核的JSON数组
     * @Descroption 生成审核JSONArray
     * @Author: 洪艺安
     * @Date 2019/3/8
     */
    public static JSONArray makeAuditJsonArray(JSONObject param) {
        Long objid = param.getLong("objid");
        JSONArray auditJsonArray = param.getJSONArray("ids");
        if (auditJsonArray == null) {
            auditJsonArray = new JSONArray();
        }
        if (objid == null && auditJsonArray.size() <= 0) {
            throw new NDSException("请至少选择1条记录！");
        }
        //非空单对象加入json数组
        if (objid != null && objid > 0) {
            auditJsonArray = new JSONArray();
            auditJsonArray.add(objid.toString());
        }
        return auditJsonArray;
    }

    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 获取结案的JSON数组
     * @Descroption 生成结案JSONArray
     * @Author: 陈俊明
     * @Date 2019-3-13 13:27
     */
    public static JSONArray makeFinishJsonArray(JSONObject param) {
        return makeAuditJsonArray(param);
    }


    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 获取作废的JSON数组
     * @Descroption 生成作废JSONArray
     * @Author: 洪艺安
     * @Date 2019/3/8
     */
    public static JSONArray makeVoidJsonArray(JSONObject param) {
        Long objid = param.getLong("objid");
        JSONArray auditJsonArray = param.getJSONArray("objids");
        if (auditJsonArray == null) {
            auditJsonArray = new JSONArray();
        }
        if (objid == null && auditJsonArray.size() <= 0) {
            throw new NDSException("请至少选择1条记录！");
        }
        //非空单对象加入json数组
        if (objid != null && objid > 0) {
            auditJsonArray = new JSONArray();
            auditJsonArray.add(objid.toString());
        }
        return auditJsonArray;
    }

    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 生成反审JSONArray
     * @Author: 陈秀楼
     * @Date 2019/3/11
     */
    public static JSONArray makeUnAuditJsonArray(JSONObject param) {
        return makeAuditJsonArray(param);
    }

    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 获取延期的JSON数组
     * @Author: 洪艺安
     * @Date 2019/3/12
     */
    public static JSONArray makeDelayJsonArray(JSONObject param) {
        return makeAuditJsonArray(param);
    }

    /**
     * 列表返回结果封装
     *
     * @param arraySize
     * @param errMap
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/11
     */
    public static ValueHolder getExcuteValueHolder(int arraySize, HashMap<Long, Object> errMap) {
        ValueHolder holder = new ValueHolder();
        int errSize = errMap.size();

        if (arraySize == 1) {
            if (errSize == 0) {
                holder.put("code", ResultCode.SUCCESS);
                holder.put("message", "执行成功！");
            } else {
                //兼容单对象和批量一条数据时直接获取错误信息
                String message = "";
                for (Map.Entry entry : errMap.entrySet()){
                    message=entry.getValue().toString();
                    break;
                }
                holder.put("code", ResultCode.FAIL);
                holder.put("message", message);
            }
        } else {
            if (errSize == 0) {
                holder.put("code", ResultCode.SUCCESS);
                holder.put("message", "执行成功记录数：" + arraySize);
            } else {
                List list =  new ArrayList();
                for (Map.Entry entry : errMap.entrySet()){
                    HashMap<String,Object> map =  new HashMap<String,Object>();
                    map.put("id",entry.getKey());
                    map.put("message",entry.getValue());
                    list.add(map);
                }
                holder.put("code", ResultCode.FAIL);
                holder.put("message", "执行成功记录数：" + (arraySize - errSize) + "，执行失败记录数：" + errSize);
                holder.put("data",list);
            }
        }
        return holder;
    }

    /**
     * @param itemArray  总记录
     * @param errorArray 失败记录
     * @return com.jackrain.nea.util.ValueHolder
     * @Descroption 流程返回结果
     * @Author: 郑小龙
     * @Date 2019/3/13
     */
    public static ValueHolder getProcessValueHolder(JSONArray itemArray, JSONArray errorArray, String typeName) {
        ValueHolder valueHolder = new ValueHolder();
        if (errorArray.size() <= 0) {
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("message", typeName + "执行成功！");
        } else if (itemArray.size() == 1 && errorArray.size() == 1) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", ((JSONObject)errorArray.get(0)).get("message"));
        } else {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", typeName + "执行成功记录数：" + (itemArray.size() - errorArray.size()) + "，" + typeName + "执行失败记录数：" + errorArray.size());
            valueHolder.put("data", errorArray);
        }
        return valueHolder;
    }

    public static ValueHolderV14 getProcessValueHolderV14(int paramSize, JSONArray errorArray, String typeName) {
        if (errorArray.size() <= 0) {
            return ValueHolderV14Utils.getSuccessValueHolder(typeName + "执行成功！");
        } else if (paramSize == 1 && errorArray.size() == 1) {
            Object message = ((JSONObject) errorArray.get(0)).get("message");
            return ValueHolderV14Utils.getFailValueHolder(message==null?"失败":message.toString());
        } else {
            return ValueHolderV14Utils.custom(ResultCode.SUCCESS,
                    typeName + "执行成功记录数：" + (paramSize - errorArray.size()) + "，" + typeName + "执行失败记录数：" + errorArray.size(), errorArray);
        }
    }

    /**
     * @param itemArray  总记录
     * @param errorArray 失败记录
     * @return com.jackrain.nea.util.ValueHolder
     * @Descroption 流程返回结果
     * @Author: 郑小龙
     * @Date 2019/3/13
     */
    public static ValueHolderV14 getProcessValueHolderV14(JSONArray itemArray, JSONArray errorArray, String typeName) {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        if (errorArray.size() <= 0) {
            ValueHolderV14Utils.getSuccessValueHolder(typeName + "执行成功！");
        } else if (itemArray.size() == 1 && errorArray.size() == 1) {
            ValueHolderV14Utils.getFailValueHolder(((JSONObject) errorArray.get(0)).getString("message"));
        } else {
            ValueHolderV14Utils.custom(ResultCode.FAIL,typeName + "执行成功记录数：" + (itemArray.size() - errorArray.size()) + "，" + typeName + "执行失败记录数：" + errorArray.size(),errorArray);
        }
        return valueHolder;
    }

    /**
     * @param errorArray 明细失败记录
     * @return com.jackrain.nea.util.ValueHolder
     * @Descroption 保存删除明细返回结果
     * @Author: 郑小龙
     * @Date 2019/3/13
     */
    public static ValueHolder getExcuteValueHolder(JSONArray errorArray) {
        ValueHolder valueHolder = new ValueHolder();
        if (errorArray.size() <= 0) {
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("message", "保存成功！");
        } else {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "执行失败记录数：" + errorArray.size());
        }
        valueHolder.put("data", errorArray);
        return valueHolder;
    }

    /**
     * @param id
     * @param message
     * @return com.alibaba.fastjson.JSONObject
     * @Descroption 返回一个JsonObject格式的信息
     * @Author: 郑小龙
     * @Date 2019/3/13
     */
    public static JSONObject getJsonObjectInfo(Long id, String message) {
        JSONObject errJo = new JSONObject();
        errJo.put("id", id);
        errJo.put("message", message);
        return errJo;
    }

    /**
     * 对象判空(判断对象各个属性值都为空)
     *
     * @param obj
     * @return
     */
    public static boolean isEmptyField(Object obj) throws Exception{
        //得到类对象的属性集合
        Field[] fs = obj.getClass().getDeclaredFields();
        //遍历属性
        for (Field f : fs) {
            // 设置属性是可以访问的(私有的也可以)
            f.setAccessible(true);
            // 得到此属性的值
            Object val = f.get(obj);
            //判断是否是静态属性值
            boolean isStatic = Modifier.isStatic(f.getModifiers());
            //只要有1个属性不为空,那么就不是所有的属性值都为空
            if(!isStatic&&val!=null) {
                return false;
            }
        }
        return true;
    }

    /**
     * 定时任务用户
     * @param baseModelDO
     */
    public static void makeCreateField_Test(BaseModel baseModelDO) {
        //所属公司
        baseModelDO.setAdClientId(Long.valueOf(37));
        //所属组织
        baseModelDO.setAdOrgId(Long.valueOf(27));
        //创建人id
        baseModelDO.setOwnerid(Long.valueOf(893));
        //创建时间
        baseModelDO.setCreationdate(new Date());
        //创建人名称
        baseModelDO.setOwnername("系统管理员");
        //修改人id
        baseModelDO.setModifierid(Long.valueOf(893));
        //修改人名称
        baseModelDO.setModifiername("系统管理员");
        //修改时间
        baseModelDO.setModifieddate(new Date());
        //是否启用
        baseModelDO.setIsactive("Y");
    }
}