package com.jackrain.nea.util.excel;

import java.lang.annotation.*;

/**
 * excel.annotation.field
 *
 * @author: xiWen.z
 * create at: 2019/8/7 0007
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface XlsAno {

    /**
     * field order number
     */
    int index() default 99999;

    /**
     * field name
     *
     * @return string
     */
    String name() default "";

    /**
     * a way of the field for get value
     * 1. set a field value necessary
     * 2. get value from group when value is 'group'
     *
     * @return string
     */
    XlsSt[] value() default XlsSt.NORMAL;

    /**
     * export excel data type
     *
     * @return XlsTypeEnum
     */
    XlsTyp type() default XlsTyp.STRING;

    /**
     * export excel desc general use for excel name
     *
     * @return
     */
    String desc() default "";

    int ignore() default 0;

}
