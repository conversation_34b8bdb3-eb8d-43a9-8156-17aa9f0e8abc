package com.jackrain.nea.util;

import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkrobot_1_0.Client;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTOHeaders;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTORequest;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTOResponse;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTOResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiV2UserGetbymobileRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiV2UserGetbymobileResponse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @ClassName DingTalkUtil
 * @Description 钉钉机器人
 * <AUTHOR>
 * @Date 2023/6/27 17:38
 * @Version 1.0
 */
@Slf4j
public class DingTalkRobotUtil {

    public final static String OMS_ROBOT_CODE = "dingbmddpgpoauhrgfxe";
    public final static String OMS_ROBOT_APPKEY = "dingbmddpgpoauhrgfxe";
    public final static String OMS_ROBOT_APPSECRET = "1IWTDp56SI-6hGT-849X-XFn2KKfwktqxKp3mPdvH2BfDt_Bb5ocE56dT2f-FjRw";


    /**
     * 使用 Token 初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    public static Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }

    public static String userGetByMobile(String mobile, String token) {
        OapiV2UserGetbymobileResponse response = null;
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getbymobile");
            OapiV2UserGetbymobileRequest request = new OapiV2UserGetbymobileRequest();
            request.setMobile(mobile);
            log.info(LogUtil.format("userGetByMobile req = {}"), JSON.toJSONString(request));
            response = client.execute(request, token);
            log.info(LogUtil.format("userGetByMobile  response = {}"), JSON.toJSONString(response));
            if (response != null && response.isSuccess()) {
                return response.getResult().getUserid();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format("userGetByMobile 请求异常 = {}"), e.getMessage());
            throw new NDSException("userGetByMobile 请求异常");
        }
        return null;
    }

    /**
     * 获取token
     *
     * @param appKey
     * @param appSecret
     * @return
     */
    public static String getToken(String appKey, String appSecret) {
        OapiGettokenResponse response = null;
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
            OapiGettokenRequest request = new OapiGettokenRequest();
            request.setAppkey(appKey);
            request.setAppsecret(appSecret);
            request.setHttpMethod("GET");
            log.info(LogUtil.format("getToken  request = {}"), JSON.toJSONString(request));
            response = client.execute(request);
            log.info(LogUtil.format("getToken  response = {}"), JSON.toJSONString(response));
            if (response != null && org.apache.commons.lang3.StringUtils.isNotBlank(response.getAccessToken())) {
                return response.getAccessToken();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format("getToken 请求异常 = {}"), e.getMessage());
            throw new NDSException("getToken 请求异常");
        }
        return null;
    }


    /**
     * 机器人发送钉钉私聊消息
     *
     * @param appKey
     * @param userIds
     * @param msgKey
     * @param msgParam
     * @param token
     * @return
     */
    public static BatchSendOTOResponseBody batchSend(String robotCode, List<String> userIds, String msgKey, String msgParam, String token) {
        BatchSendOTOResponseBody rspBody = null;
        BatchSendOTOResponse rsp = null;
        try {
            Client client = createClient();
            BatchSendOTOHeaders batchSendOTOHeaders = new BatchSendOTOHeaders();
            batchSendOTOHeaders.xAcsDingtalkAccessToken = token;
            BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest()
                    .setRobotCode(robotCode)
                    .setUserIds(userIds)
                    .setMsgKey(msgKey)
                    .setMsgParam(msgParam);
            log.info(LogUtil.format("batchSend  batchSendOTORequest = {}"), JSON.toJSONString(batchSendOTORequest));
            rsp = client.batchSendOTOWithOptions(batchSendOTORequest, batchSendOTOHeaders, new RuntimeOptions());
            log.info(LogUtil.format("batchSend  batchSendOTOResponse = {}"), JSON.toJSONString(rsp));
            if (rsp != null && rsp.getBody() != null) {
                return rsp.getBody();
            }
            for (int i = 1; i < 4; i++) {
                Thread.sleep(1000);
                log.error(LogUtil.format("batchSend 请求失败 = {},第{} 次"), JSON.toJSONString(rsp), i);
                rsp = client.batchSendOTOWithOptions(batchSendOTORequest, batchSendOTOHeaders, new RuntimeOptions());
                if (rsp != null && rsp.getBody() != null) {
                    return rsp.getBody();
                }
            }
        } catch (TeaException err) {
            err.printStackTrace();
            log.error(LogUtil.format("DingTalkSender batchSend TeaException  err =  {}"), err.getMessage());
            throw new NDSException("DingTalkSender batchSend TeaException");
        } catch (Exception _err) {
            _err.printStackTrace();
            log.error(LogUtil.format("DingTalkSender batchSend Exception  err =  {}"), _err.getMessage());
            throw new NDSException("DingTalkSender batchSend Exception");
        }
        return rspBody;
    }

}
