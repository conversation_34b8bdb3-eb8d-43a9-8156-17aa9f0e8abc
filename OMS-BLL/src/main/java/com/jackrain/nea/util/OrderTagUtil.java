package com.jackrain.nea.util;

import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.tag.TaggerManager;
import com.jackrain.nea.ps.model.SkuType;
import org.springframework.util.CollectionUtils;

/**
 * @Description: 订单标签计算
 * @author: 江家雷
 * @since: 2021/1/5
 * create at : 2021/1/5 14:26
 */
public class OrderTagUtil {

    /***
     *  赠（看拆分后的订单是否存在赠品）、换（看拆分后的订单明细中是否有换标）、组（看拆分后的订单是否有组合福袋商品）、播、轻
     * @param ocBOrderRelation
     */
    public static void orderTags(OcBOrderRelation ocBOrderRelation) {
        if (ocBOrderRelation == null || ocBOrderRelation.getOrderInfo() == null
                || CollectionUtils.isEmpty(ocBOrderRelation.getOrderItemList())) {
            return;
        }
        TaggerManager.get().doTag(ocBOrderRelation.getOrderInfo(), ocBOrderRelation.getOrderItemList());
        ocBOrderRelation.getOrderInfo().setIsCombination(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
        ocBOrderRelation.getOrderInfo().setIsHasgift(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
        //ocBOrderRelation.getOrderInfo().setOrderType(OrderTypeEnum.NORMAL.getVal());
        for (OcBOrderItem item : ocBOrderRelation.getOrderItemList()) {
            if (item.getRefundStatus() != null && item.getRefundStatus() == 6) {
                continue;
            }
            int itemIsGift = item.getIsGift() == null ? 0 : item.getIsGift();
            int proType = item.getProType() == null ? 0 : item.getProType().intValue();
            int isExchangeItem = item.getIsExchangeItem() == null ? 0 : item.getIsExchangeItem();
            if (itemIsGift == OcBorderListEnums.YesOrNoEnum.IS_YES.getVal()) {
                ocBOrderRelation.getOrderInfo().setIsHasgift(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
            }
            //合单拆单取消合标
            ocBOrderRelation.getOrderInfo().setIsMerge(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
            if (proType == SkuType.NO_SPLIT_COMBINE
                    || proType == SkuType.COMBINE_PRODUCT
                    || proType == SkuType.GIFT_PRODUCT) {
                ocBOrderRelation.getOrderInfo().setIsCombination(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
            }
            if (isExchangeItem == OcBorderListEnums.YesOrNoEnum.IS_YES.getVal()) {
                ocBOrderRelation.getOrderInfo().setOrderType(OrderTypeEnum.EXCHANGE.getVal());
            }
        }
    }
}