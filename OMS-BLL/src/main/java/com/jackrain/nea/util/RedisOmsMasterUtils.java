package com.jackrain.nea.util;

import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.R3RedisSlaverUtil;

/**
 * <AUTHOR>
 * @date 2020/10/26 下午6:17
 * @description reidsSlaver 工具类
 **/
public class RedisOmsMasterUtils {
    /**
     * sg-master
     */
    private static final String MASTER = "oms-master";

    /**
     * 默认1分钟
     */
    private static final int LOCK_ORDER_AUTO_TIMEOUT = 0;

    /**
     * 锁定单据的KeyValue值。默认为5
     */
    private static final int LOCKED_ORDER_VALUE = 5;

    /**
     * 获取单据锁定超时时间
     *
     * @return 单据锁定超时时间
     */
    public static long getLockOrderTimeOut() {
        return LOCK_ORDER_AUTO_TIMEOUT;
    }

    /**
     * 获取锁的实例
     *
     * @return
     */
    public static RedisReentrantLock getReentrantLock(String lockId) {
        return new RedisReentrantLock(lockId, MASTER);
    }


    /**
     * 获取oms-master存储业务数据的redis实例
     */
    public static <K, V> CusRedisTemplate<K, V> getObjRedisTemplate() {
        CusRedisTemplate<K, V> cusRedisTemplate = R3RedisSlaverUtil.getObjRedisTemplate(MASTER);
        return cusRedisTemplate;
    }


    /**
     * 获取oms-master存储业务数据的redis实例
     */
    public static <K, V> CusRedisTemplate<K, V> getStrRedisTemplate() {
        CusRedisTemplate<K, V> cusRedisTemplate = R3RedisSlaverUtil.getStrRedisTemplate(MASTER);
        return cusRedisTemplate;
    }
}
