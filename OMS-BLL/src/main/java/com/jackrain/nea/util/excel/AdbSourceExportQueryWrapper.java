package com.jackrain.nea.util.excel;

import cn.hutool.core.util.StrUtil;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.mapper.OcBExcelMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author: yangrui
 * @Date: 2024-10-08 17:58
 */
@Component
@Slf4j
public class AdbSourceExportQueryWrapper {

    @Resource
    private OcBExcelMapper ocBExcelMapper;

    @Resource
    private OmsSystemConfig omsSystemConfig;

    private static final String TABLE_JOIN = "{}.oc_b_order O " +
            "LEFT JOIN {}.oc_b_order_item I ON I.oc_b_order_id = O.id \n" +
            "LEFT JOIN {}.ps_c_pro P ON I.ps_c_sku_ecode = P.ecode ";

    public static final String COLUMN = " O.ID,\n" +
            "\tO.BILL_NO,\n" +
            "\tO.SALES_GROUP_CODE,\n" +
            "\tO.ORDER_TAG,\n" +
            "\tO.ORDER_STATUS,\n" +
            "\tO.CP_C_SHOP_TITLE,\n" +
            "\tO.SOURCE_CODE,\n" +
            "\tO.QTY_ALL,\n" +
            "\tO.USER_NICK,\n" +
            "\tO.RECEIVER_NAME,\n" +
            "\tO.RECEIVER_MOBILE,\n" +
            "\tO.CP_C_REGION_PROVINCE_ENAME,\n" +
            "\tO.CP_C_REGION_CITY_ENAME,\n" +
            "\tO.CP_C_REGION_AREA_ENAME,\n" +
            "\tO.RECEIVER_ADDRESS,\n" +
            "\tO.CP_C_PHY_WAREHOUSE_ENAME,\n" +
            "\tO.CP_C_PHY_WAREHOUSE_ECODE,\n" +
            "\tO.CP_C_LOGISTICS_ENAME,\n" +
            "\tO.EXPRESSCODE,\n" +
            "\tO.CREATIONDATE,\n" +
            "\tO.PRESALE_DEPOSIT_TIME,\n" +
            "\tO.PAY_TIME,\n" +
            "\tO.AUDIT_TIME,\n" +
            "\tO.AUDIT_NAME,\n" +
            "\tO.SALES_DEPARTMENT_NAME,\n" +
            "\tO.SCAN_TIME,\n" +
            "\tO.SELLER_MEMO,\n" +
            "\tO.BUYER_MESSAGE,\n" +
            "\tO.ORDER_TYPE,\n" +
            "\tO.DOUBLE11_PRESALE_STATUS,\n" +
            "\tO.PRODUCT_AMT,\n" +
            "\tO.DISTRIBUTION_TIME,\n" +
            "\tO.WMS_CANCEL_STATUS,\n" +
            "\tO.RETURN_STATUS,\n" +
            "\tO.PRODUCT_DISCOUNT_AMT,\n" +
            "\tO.ADJUST_AMT,\n" +
            "\tO.SHIP_AMT,\n" +
            "\tO.SERVICE_AMT,\n" +
            "\tO.ORDER_AMT,\n" +
            "\tO.RECEIVED_AMT,\n" +
            "\tO.AMT_RECEIVE,\n" +
            "\tO.JD_SETTLE_AMT,\n" +
            "\tO.COD_AMT,\n" +
            "\tO.INVOICE_HEADER,\n" +
            "\tO.INVOICE_CONTENT,\n" +
            "\tO.SUFFIX_INFO,\n" +
            "\tO.SYSREMARK,\n" +
            "\tO.JD_RECEIVE_AMT,\n" +
            "\tO.SALESMAN_NAME,\n" +
            "\tO.OWNERENAME,\n" +
            "\tO.ANCHOR_NAME,\n" +
            "\tO.ANCHOR_ID,\n" +
            "\tI.ANCHOR_NAME ITEM_ANCHOR_NAME,\n" +
            "\tI.ANCHOR_ID ITEM_ANCHOR_ID,\n" +
            "\tO.BUSINESS_TYPE_NAME,\n" +
            "\tcase when O.EXCEPTION_TYPE = 1 then '寻源异常' when O.EXCEPTION_TYPE = 2 then '分物流异常' when O.EXCEPTION_TYPE = 3 then '传wms异常' else '' end EXCEPTION_TYPE,\n" +
            "\tO.GW_SOURCE_GROUP,\n" +
            "\tO.ORDER_SOURCE_PLATFORM_ECODE,\n" +
            "\tO.EXCEPTION_EXPLAIN,\n" +
            "\tO.SG_B_OUT_BILL_NO,\n" +
            "\tO.HOLD_REASON_ID,\n" +
            "\tO.DETENTION_REASON_ID,\n" +
            "\tO.CURRENT_CYCLE_NUMBER,\n" +
            "\tI.OC_B_ORDER_ID,\n" +
            "\tI.PS_C_PRO_ENAME,\n" +
            "\tI.PS_C_PRO_ECODE,\n" +
            "\tI.PT_PRO_NAME,\n" +
            "\tI.GIFTBAG_SKU,\n" +
            "\tI.PS_C_SKU_ECODE,\n" +
            "\tI.PS_C_SKU_ENAME,\n" +
            "\tI.EXPIRY_DATE_RANGE,\n" +
            "\tI.QTY,\n" +
            "\tI.REAL_OUT_NUM,\n" +
            "\tI.QTY_LOST,\n" +
            "\tI.PRICE,\n" +
            "\tI.PRICE_LIST,\n" +
            "\tI.REAL_AMT,\n" +
            "\tI.REFUND_STATUS AS ITEM_REFUND_STATUS,\n" +
            "\tI.AMT_DISCOUNT,\n" +
            "\tI.ADJUST_AMT AS ADJUST_AMT_ITEM,\n" +
            "\tI.ADJUST_AMT,\n" +
            "\tI.ORDER_SPLIT_AMT,\n" +
            "\tO.REFUND_STATUS,\n" +
            "\tI.IS_GIFT,\n" +
            "\tI.NUM_IID,\n" +
            "\tI.SKU_NUMIID,\n" +
            "\tI.LABELING_REQUIREMENTS,\n" +
            "\tO.MERGE_SOURCE_CODE,\n" +
            "\tI.OOID,\n" +
            "\tI.STANDARD_WEIGHT,\n" +
            "\tI.ESTIMATE_CON_TIME,\n" +
            "\tI.RESERVE_BIGINT02,\n" +
            "\tO.ESTIMATE_CON_TIME MAIN_ESTIMATE_CON_TIME,\n" +
            "\tO.INSIDE_REMARK,\n" +
            "\tO.IS_MERGE,\n" +
            "\tO.IS_INTERECEPT,\n" +
            "\tO.IS_INRETURNING,\n" +
            "\tO.IS_HASGIFT,\n" +
            "\tO.IS_SPLIT,\n" +
            "\tO.IS_INVOICE,\n" +
            "\tO.IS_JCORDER,\n" +
            "\tO.IS_OUT_URGENCY,\n" +
            "\tO.IS_HAS_TICKET,\n" +
            "\tO.IS_COMBINATION,\n" +
            "\tO.ORDER_TYPE,\n" +
            "\tO.PRICE_LABEL,\n" +
            "\tO.LOCK_STATUS,\n" +
            "\tO.LIVE_FLAG,\n" +
            "\tO.IS_O2O_ORDER,\n" +
            "\tO.IS_COPY_ORDER,\n" +
            "\tO.IS_RESET_SHIP,\n" +
            "\tO.IS_PROM_ORDER,\n" +
            "\tO.IS_EQUAL_EXCHANGE,\n" +
            "\tO.IS_OUT_STOCK,\n" +
            "\tO.IS_DELIVERY_URGENT,\n" +
            "\tO.IS_MODIFIED_ORDER,\n" +
            "\tO.IS_EXTRA,\n" +
            "\tO.IS_DETENTION,\n" +
            "\tO.IS_FORBIDDEN_DELIVERY,\n" +
            "\tO.IS_VIP_UPDATE_WAREHOUSE,\n" +
            "\tO.REVERSE_AUDIT_TYPE,\n" +
            "\tO.IS_EXCEPTION,\n" +
            "\tO.IS_MEMBER,\n" +
            "\tO.IS_CYCLE,\n" +
            "\tO.IS_DELIVERY_TO_DOOR,\n" +
            "\tO.IS_OVERDUE,\n" +
            "\tO.GENERIC_MARK,\n" +
            "\tO.APPOINT_LOGISTICS_ENAME,\n " +
            "\tO.CARPOOL_NO,\n " +
            "\tO.SALE_PRODUCT_ATTR,\n " +
            "\tO.LATEST_DELIVERY_TIME THE_LATEST_DELIVERY_TIME,\n " +
            "\tO.DETENTION_REASON,\n " +
            "\tP.VOLUME ";


    private String getTable() {
        return StrUtil.format(TABLE_JOIN,
                omsSystemConfig.getRptOrderDbName(),
                omsSystemConfig.getRptOrderDbName(),
                omsSystemConfig.getRptBasicsDbName());
    }

    @TargetDataSource(name = "adb")
    public List<Map<String, Object>> selectOcBAllExcelInfo(XlsSqlModel model) {
        return ocBExcelMapper.selectOcBAllExcelInfo(model, getTable(), COLUMN);
    }
}
