package com.jackrain.nea.util;

import com.google.common.base.Throwables;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: heliu
 * @since: 2019/3/6
 * create at : 2019/3/6 18:39
 */
@Slf4j
@Component
public class BllWebUtil {

    /**
     * IP地址
     *
     * @return
     */
    public String getWebRequestIpAddress() {
        try {
            String ipAddress = BllCommonUtil.getLocalIpAddress();
            return ipAddress;
        } catch (Exception ex) {
            log.error(LogUtil.format("获取服务端IP异常"), Throwables.getStackTraceAsString(ex));
            return "";
        }
    }
}
