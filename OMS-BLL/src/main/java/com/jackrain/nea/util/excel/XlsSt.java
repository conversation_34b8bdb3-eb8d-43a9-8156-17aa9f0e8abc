package com.jackrain.nea.util.excel;

/**
 * excel.field.value get way
 *
 * @author: xiWen.z
 * create at: 2019/8/7 0007
 */
public enum XlsSt {

    /**
     * normal
     * could null
     * level.field
     */
    NORMAL,

    /**
     * necessary
     * level.field
     */
    NOTNULL,

    /**
     * group
     * level.field
     */
    GROUP,

    /**
     *仅供退换货单导出
     */
    FOREIGN,

    /**
     * combination field
     */
    JOIN,

    /**
     * statistics
     */
    STATS,

    /**
     * calculate
     */
    CALC,

    /**
     * es
     * level.table
     */
    ES,

    /**
     * db
     * level.table
     */
    DB,

    /**
     * r3
     * level.table
     */
    R3,

    /**
     * wait for define
     */
    UNDEFINE;

    /**
     * @param e XlsGetWayEnum
     * @return int
     */
    int intVal(XlsSt e) {
        switch (e) {
            case NORMAL:
                return 1;
            case NOTNULL:
                return 2;
            case GROUP:
                return 3;
            case JOIN:
                return 4;
            case STATS:
                return 5;
            case CALC:
                return 6;
            case DB:
                return 10;
            case ES:
                return 20;
            case R3:
                return 30;
            default:
                return 0;
        }
    }

}
