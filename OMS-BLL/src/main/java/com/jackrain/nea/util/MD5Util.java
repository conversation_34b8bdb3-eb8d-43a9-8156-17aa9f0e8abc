package com.jackrain.nea.util;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;

/**
 * @Desc : md5 加密字符串
 * <AUTHOR> xiWen
 * @Date : 2020/12/1
 */
@Slf4j
public class MD5Util {

    /**
     * MD5加密
     *
     * @param var
     * @return
     */
    public static String encryptByMD5(String var) {
        String md5String;
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(var.getBytes("UTF8"));
            byte[] bytes = md5.digest();
            md5String = convertToHexString(bytes);
        } catch (Exception e) {
            throw new RuntimeException(Resources.getMessage("MD5Encrypt Exception"), e);
        }
        return md5String;
    }

    /**
     * 1. userNick
     * 2. cpCShopId
     * 3. platForm
     * 4. payType [去除]
     * 5. cpCPhyWarehouseId
     * 6. orderType
     * 7. receiverName
     * 8. receiverMobile
     * 9. receiverPhone
     * 10. cpCRegionProvinceId
     * 11. cpCRegionCityId
     * 12. cpCRegionAreaId
     * 13. receiverAddress
     *
     * @param order 零售发货单
     */
    public static void encryptOrderInfo4Merge(OcBOrder order) {

        StringBuilder sb = new StringBuilder();
        sb.append(order.getUserNick() == null ? "" : order.getUserNick().trim())
                .append(order.getCpCShopId())
                .append(order.getPlatform())
                .append(order.getCpCPhyWarehouseId())
                .append(order.getOrderType())
                .append(order.getReceiverName() == null ? "" : order.getReceiverName().trim())
                .append(order.getReceiverMobile() == null ? "" : order.getReceiverMobile().trim())
                .append(order.getReceiverPhone() == null ? "" : order.getReceiverPhone().trim())
                .append(order.getCpCRegionProvinceId())
                .append(order.getCpCRegionCityId())
                .append(order.getCpCRegionAreaId())
                .append(order.getReceiverAddress() == null ? "" : order.getReceiverAddress().trim())
                //增加JITX合包码  其他平台合包码为空
                .append(StringUtils.isEmpty(order.getMergedCode()) ? "" : order.getMergedCode().trim())
                .append(order.getBusinessTypeId())
                .append(order.getGwSourceGroup() == null ? "" : order.getReceiverAddress().trim());

        order.setOrderEncryptionCode(encryptByMD5(sb.toString()));
    }

    /**
     * 十六进制转换
     *
     * @param data byte data
     * @return HexString
     */
    private static String convertToHexString(byte[] data) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < data.length; i++) {
            int val = 0xff & data[i];
            if (val < 16) {
                sb.append("0");
            }
            sb.append(Integer.toHexString(val));
        }
        return sb.toString();
    }


}
