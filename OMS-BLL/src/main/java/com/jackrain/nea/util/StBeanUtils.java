package com.jackrain.nea.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.model.base.SubBaseModel;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.sql.Timestamp;
import java.util.*;

/**
 * @Descroption 策略中心对象工具
 * <AUTHOR>
 * @Date 2019/3/8 10:02
 */
public class StBeanUtils {
    /**
     * @param baseModelDO
     * @param user
     * @return void
     * @Descroption 最后更新信息字段修改
     * @Author: 洪艺安
     * @Date 2019/3/8
     */
    public static void makeModifierField(BaseModel baseModelDO, User user) {
        baseModelDO.setModifierid(Long.valueOf(user.getId()));//修改人id
        baseModelDO.setModifiername(user.getName());//修改人用户名
        baseModelDO.setModifieddate(new Date());//修改时间
    }

    /**
     * @param jsonObject
     * @param user
     * @return void
     * @Descroption 最后更新信息字段修改-jsonObject
     * @Author: 郑小龙
     * @Date 2019/3/11
     */
    public static void makeModifierField(JSONObject jsonObject, User user) {
        jsonObject.put("MODIFIERID", Long.valueOf(user.getId()));//修改人id
        jsonObject.put("MODIFIERNAME", user.getName());////修改人名称
        jsonObject.put("MODIFIERENAME", user.getEname());//修改人账号
        jsonObject.put("MODIFIEDDATE", new Date());//修改时间
    }

    /**
     * @param jsonObject
     * @return void
     * @Descroption 反审-审核人置空
     * @Author: 郑小龙
     * @Date 2019/3/11
     */
    public static void makeCancelField(JSONObject jsonObject) {
        jsonObject.put("CHECKID", null);//审核人
        jsonObject.put("CHECKTIME", null);//审核时间
        jsonObject.put("CHECKNAME", null);//审核人姓名
        jsonObject.put("CHECKENAME", null);//审核人账号
    }

    public static void makeCancelField(JSONObject jsonObject, User user) {
        jsonObject.put("REVERSE_ID", Long.valueOf(user.getId()));//反审人id
        jsonObject.put("REVERSE_NAME", user.getName());//反审人名称
        jsonObject.put("REVERSE_ENAME", user.getEname());//反审人账号
        jsonObject.put("REVERSE_TIME", new Date());//反审时间
    }

    /**
     * @param baseModelDO
     * @param user
     * @return void
     * @Descroption 创建信息字段修改
     * @Author: 洪艺安
     * @Date 2019/8
     */
    public static void makeCreateField(BaseModel baseModelDO, User user) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        baseModelDO.setAdClientId((long) user.getClientId());//所属公司
        baseModelDO.setAdOrgId((long) user.getOrgId());//所属组织
        baseModelDO.setOwnerid(Long.valueOf(user.getId()));//创建人id
        baseModelDO.setCreationdate(timestamp);//创建时间
        baseModelDO.setOwnername(user.getName());//创建人用户名
        baseModelDO.setModifierid(Long.valueOf(user.getId()));//修改人id
        baseModelDO.setModifiername(user.getName());//修改人用户名
        baseModelDO.setModifieddate(timestamp);//修改时间
        baseModelDO.setIsactive("Y");//是否启用
    }

    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 生成审核JSONArray
     * @Author: 洪艺安
     * @Date 2019/3/8
     */
    public static JSONArray makeAuditJsonArray(JSONObject param) {
        Long objid = param.getLong("objid");
        JSONArray auditJsonArray = param.getJSONArray("ids");
        if (auditJsonArray == null) {
            auditJsonArray = new JSONArray();
        }
        if (objid == null && auditJsonArray.size() <= 0) {
            throw new NDSException("请至少选择1条记录！");
        }
        //非空单对象加入json数组
        if (objid != null && objid > 0) {
            auditJsonArray = new JSONArray();
            auditJsonArray.add(objid.toString());
        }
        return auditJsonArray;
    }

    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 获取结案的JSON数组
     * @Descroption 生成结案JSONArray
     * @Author: 陈俊明
     * @Date 2019-3-13 13:27
     */
    public static JSONArray makeFinishJsonArray(JSONObject param) {
        return makeAuditJsonArray(param);
    }


    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 获取作废的JSON数组
     * @Descroption 生成作废JSONArray
     * @Author: 洪艺安
     * @Date 2019/3/8
     */
    public static JSONArray makeVoidJsonArray(JSONObject param) {
        Long objid = param.getLong("objid");
        JSONArray auditJsonArray = param.getJSONArray("objids");
        if (auditJsonArray == null) {
            auditJsonArray = new JSONArray();
        }
        if (objid == null && auditJsonArray.size() <= 0) {
            throw new NDSException("请至少选择1条记录！");
        }
        //非空单对象加入json数组
        if (objid != null && objid > 0) {
            auditJsonArray = new JSONArray();
            auditJsonArray.add(objid.toString());
        }
        return auditJsonArray;
    }

    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 校验数据
     * @Author: 胡林洋
     * @Date 2021/05/28
     */
    public static JSONArray checkJsonArray(JSONObject param) {
        JSONArray jsonArray = param.getJSONArray("ids");
        if (jsonArray == null||jsonArray.size() <= 0) {
            throw new NDSException("请至少选择1条记录！");
        }
        return jsonArray;
    }

    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 生成反审JSONArray
     * @Author: 陈秀楼
     * @Date 2019/3/11
     */
    public static JSONArray makeUnAuditJsonArray(JSONObject param) {
        return makeAuditJsonArray(param);
    }

    /**
     * @param param
     * @return com.alibaba.fastjson.JSONArray
     * @Descroption 获取延期的JSON数组
     * @Author: 洪艺安
     * @Date 2019/3/12
     */
    public static JSONArray makeDelayJsonArray(JSONObject param) {
        return makeAuditJsonArray(param);
    }

    /**
     * 列表返回结果封装
     *
     * @param arraySize
     * @param errMap
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/11
     */
    public static ValueHolder getExcuteValueHolder(int arraySize, HashMap<Long, Object> errMap) {
        ValueHolder holder = new ValueHolder();
        int errSize = errMap.size();

        if (arraySize == 1) {
            if (errSize == 0) {
                holder.put("code", ResultCode.SUCCESS);
                holder.put("message", "执行成功！");
            } else {
                String message = "";//兼容单对象和批量一条数据时直接获取错误信息
                List list = new ArrayList();
                for (Map.Entry entry : errMap.entrySet()) {
                    message = entry.getValue().toString();
                    HashMap<String, Object> map = new HashMap<String, Object>();
                    map.put("objid", entry.getKey());
                    map.put("message", entry.getValue());
                    list.add(map);
                }

                holder.put("code", ResultCode.FAIL);
                holder.put("message", message);
                holder.put("data", list);
            }
        } else {
            if (errSize == 0) {
                holder.put("code", ResultCode.SUCCESS);
                holder.put("message", "执行成功记录数：" + arraySize);
            } else {
                List list = new ArrayList();
                for (Map.Entry entry : errMap.entrySet()) {
                    HashMap<String, Object> map = new HashMap<String, Object>();
                    map.put("objid", entry.getKey());
                    map.put("message", entry.getValue());
                    list.add(map);
                }
                holder.put("code", ResultCode.FAIL);
                holder.put("message", "执行成功记录数：" + (arraySize - errSize) + "，执行失败记录数：" + errSize);
                holder.put("data", list);
            }
        }
        return holder;
    }

    /**
     * @param itemArray  总记录
     * @param errorArray 失败记录
     * @return com.jackrain.nea.util.ValueHolder
     * @Descroption 流程返回结果
     * @Author: 郑小龙
     * @Date 2019/3/13
     */
    public static ValueHolder getProcessValueHolder(JSONArray itemArray, JSONArray errorArray, String typeName) {
        ValueHolder valueHolder = new ValueHolder();
        if (errorArray.size() <= 0) {
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("message", typeName + "执行成功！");
        } else if (itemArray.size() == 1 && errorArray.size() == 1) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", ((JSONObject) errorArray.get(0)).get("message"));
        } else {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", typeName + "执行成功记录数：" + (itemArray.size() - errorArray.size()) + "，" + typeName + "执行失败记录数：" + errorArray.size());
            valueHolder.put("data", errorArray);
        }
        return valueHolder;
    }

    /**
     * @param itemArray  总记录
     * @param errorArray 失败记录
     * @return com.jackrain.nea.util.ValueHolder
     * @Descroption 流程返回结果
     * @Author: 秦雄飞
     * @Date 2021/1/4
     */
    public static ValueHolderV14 getProcessValueHolderV14(JSONArray itemArray, JSONArray errorArray, String typeName) {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        if (errorArray.size() <= 0) {
            valueHolder.setCode( ResultCode.SUCCESS);
            valueHolder.setMessage(typeName + "执行成功！");
        } else if (itemArray.size() == 1 && errorArray.size() == 1) {
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage(String.valueOf(((JSONObject) errorArray.get(0)).get("message")));
        } else {
            valueHolder.setCode(ResultCode.FAIL);
            valueHolder.setMessage(typeName + "执行成功记录数：" + (itemArray.size() - errorArray.size()) + "，" + typeName + "执行失败记录数：" + errorArray.size());
            valueHolder.setData(errorArray);
        }
        return valueHolder;
    }

    /**
     * @param errorArray 明细失败记录
     * @return com.jackrain.nea.util.ValueHolder
     * @Descroption 保存删除明细返回结果
     * @Author: 郑小龙
     * @Date 2019/3/13
     */
    public static ValueHolder getExcuteValueHolder(JSONArray errorArray) {
        ValueHolder valueHolder = new ValueHolder();
        if (errorArray.size() <= 0) {
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("message", "保存成功！");
        } else {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", "执行失败记录数：" + errorArray.size());
        }
        valueHolder.put("data", errorArray);
        return valueHolder;
    }

    /**
     * @param id
     * @param message
     * @return com.alibaba.fastjson.JSONObject
     * @Descroption 返回一个JsonObject格式的信息
     * @Author: 郑小龙
     * @Date 2019/3/13
     */
    public static JSONObject getJsonObjectInfo(Long id, String message) {
        JSONObject errJo = new JSONObject();
        errJo.put("id", id);
        errJo.put("message", message);
        return errJo;
    }

    /**
     * 作废功能返回结果
     *
     * @param arraySize
     * @param errMap
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 田钦华
     * @Date 2019/4/15
     */
    public static ValueHolder getExcuteVoidValueHolder(int arraySize, HashMap<Long, Object> errMap) {
        ValueHolder holder = new ValueHolder();
        int errSize = errMap.size();
        if (arraySize == 1) {
            if (errSize == 0) {
                holder.put("code", ResultCode.SUCCESS);
                holder.put("message", "作废成功！");
            } else {
                String message = "";//兼容单对象和批量一条数据时直接获取错误信息
                for (Map.Entry entry : errMap.entrySet()) {
                    message = entry.getValue().toString();
                    break;
                }
                holder.put("code", ResultCode.FAIL);
                holder.put("message", message);
            }
        } else {
            if (errSize == 0) {
                holder.put("code", ResultCode.SUCCESS);
                holder.put("message", "作废成功记录数：" + arraySize);
            } else {
                List list = new ArrayList();
                for (Map.Entry entry : errMap.entrySet()) {
                    HashMap<String, Object> map = new HashMap<String, Object>();
                    map.put("objid", entry.getKey());
                    map.put("message", entry.getValue());
                    list.add(map);
                }
                holder.put("code", ResultCode.FAIL);
                holder.put("message", "作废成功记录数：" + (arraySize - errSize) + "，作废失败记录数：" + errSize);
                holder.put("data", list);
            }
        }
        return holder;
    }

    /**
     * @param baseModel
     * @param user
     * @return void
     * @Descroption 创建信息字段修改
     * @Author: 舒威
     * @Date 2020/11
     */

    public static <T extends BaseModel> T setBModelDefalutData(T baseModel, User user) {

        Date systemDate = new Date();
        Long loginUserId = user.getId() == null ? null : user.getId().longValue();

        baseModel.setAdClientId(Long.valueOf(user.getClientId()));
        baseModel.setAdOrgId(Long.valueOf(user.getOrgId()));
        baseModel.setIsactive("Y");
        baseModel.setOwnerid(loginUserId);
        baseModel.setOwnername(user.getName());
        baseModel.setCreationdate(systemDate);
        baseModel.setModifierid(loginUserId);
        baseModel.setModifiername(user.getName());
        baseModel.setModifieddate(systemDate);

        return baseModel;
    }
}
