package com.jackrain.nea.util;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Desc : 订单收货人地址
 * <AUTHOR> xiWen
 * @Date : 2020/12/26
 */
public class OrderAddressConvertUtil {


    /**
     * 去除空格
     */
    private static final Pattern bp = Pattern.compile("\\s+");

    /**
     * 替换中英文逗号
     */
    private static final Pattern dp = Pattern.compile("[,，]+");

    /**
     * 转换
     *
     * @param var 订单
     * @return 收货地址
     */
    public static String convert(OcBOrder var) {
        String var1 = var.getReceiverAddress();
        if (var1 == null) {
            return null;
        }
        Matcher matcher = bp.matcher(var1);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "");
        }
        matcher.appendTail(sb);
        String var2 = dp.matcher(sb).replaceAll("::::");
        var.setReceiverAddress(var2);
        return var2;
    }

}
