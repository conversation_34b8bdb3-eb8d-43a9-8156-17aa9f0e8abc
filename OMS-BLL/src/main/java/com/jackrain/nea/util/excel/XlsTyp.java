package com.jackrain.nea.util.excel;

/**
 * write to excel data format
 *
 * @author: xiWen.z
 * create at: 2019/8/7 0007
 */
public enum XlsTyp {
    /**
     * String
     */
    STRING,

    /**
     * double
     */
    DOUBLE,

    /**
     * date
     */
    DATE,

    /**
     * int
     */
    INT,

    /**
     * boolean
     */
    BOOL,

    /**
     * text
     */
    RICH_TEXT,

    /**
     * wait for define
     */
    UNDEFINE;

    /**
     * @param e XlsTypeEnum
     * @return int
     */
    int intVal(XlsTyp e) {
        switch (e) {
            case STRING:
                return 1;
            case DOUBLE:
                return 2;
            case DATE:
                return 3;
            case INT:
                return 4;
            case BOOL:
                return 5;
            case RICH_TEXT:
                return 6;
            default:
                return 0;
        }
    }

}
