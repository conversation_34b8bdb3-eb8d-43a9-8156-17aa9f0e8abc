package com.jackrain.nea.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import org.springframework.util.ObjectUtils;

import java.util.Date;

/**
 * className: CommandAdapterUtil
 * description: 框架trigger操作，对象转换工具
 *
 * <AUTHOR>
 * create: 2021-06-19
 * @since JDK 1.8
 */
public class CommandAdapterUtil {


    /**
     * 给基础数据库对象创建人、修改人信息赋值
     * @param model
     * @param operateUser
     */
    public static void defaultOperator(BaseModel model, User operateUser) {
        if(null == model){
            return;
        }

        if (operateUser == null) {
            operateUser = SystemUserResource.getRootUser();
        }

        if(null == model.getId() || model.getId() < 0){
            model.setOwnername(operateUser.getName());
            model.setAdOrgId((long) operateUser.getOrgId());
            model.setAdClientId((long) operateUser.getClientId());
            model.setOwnerid(Long.valueOf(operateUser.getId()));
            model.setCreationdate(new Date());
            model.setIsactive(IsActiveEnum.Y.getKey());
        }
        model.setModifierid(Long.valueOf(operateUser.getId()));
        model.setModifiername(operateUser.getName());
        model.setModifieddate(new Date());
    }

    public static ValueHolder checkSaveSession(QuerySession querySession,String tableName){
        ValueHolder valueHolder = ValueHolderUtils.getFailValueHolder("参数不能为空");
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param")));
        if (null == param) {
            return valueHolder;
        }

        JSONObject fixColumn = param.getJSONObject(OcCommonConstant.FIX_COLUMN);
        Long id = param.getLong(OcCommonConstant.OBJ_ID);
        if (null == fixColumn || null == id) {
            return valueHolder;
        }

        valueHolder = ValueHolderUtils.getSuccessValueHolder(id,tableName);
        valueHolder.put(OcCommonConstant.FIX_COLUMN,fixColumn);
        return valueHolder;
    }

    public static ValueHolder checkDeleteSession(QuerySession querySession,String tableName){
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param")));
        ValueHolder valueHolder = ValueHolderUtils.getFailValueHolder("参数不能为空");
        if (null == param) {
            return valueHolder;
        }
        Long id = param.getLong(OcCommonConstant.OBJ_ID);
        if (null == id) {
            return valueHolder;
        }

        valueHolder = ValueHolderUtils.getSuccessValueHolder(id,tableName);
        JSONObject tabitem = param.getJSONObject("tabitem");
        valueHolder.put("isdelmtable",param.getBoolean("isdelmtable"));
        if(!ObjectUtils.isEmpty(tabitem)){
            valueHolder.put("tabitem",tabitem);
        }
        return valueHolder;
    }

}
