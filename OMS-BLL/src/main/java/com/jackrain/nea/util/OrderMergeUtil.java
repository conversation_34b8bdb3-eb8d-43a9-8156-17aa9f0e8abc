package com.jackrain.nea.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.model.enums.MergeTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 订单合单
 * @date 2021/10/22 17:59
 */
@Slf4j
@Component
public class OrderMergeUtil {

    public static void main(String[] args) {
        long start = System.currentTimeMillis();
        List<OcBOrder> orders = new ArrayList<>();
        for (long i = 1; i < 20; i++) {
            OcBOrder order = new OcBOrder();
            order.setId(i);
            //            order.setPlatform(PlatFormEnum.VIP_JITX.getCode());
            order.setQtyAll(new BigDecimal(i));
            orders.add(order);
        }
        for (long i = 20; i < 300; i++) {
            OcBOrder order = new OcBOrder();
            order.setId(i);
            //            order.setPlatform(PlatFormEnum.VIP_JITX.getCode());
            order.setQtyAll(new BigDecimal(1));
            orders.add(order);
        }
        int eachSize = 20;
        List<List<OcBOrder>> order1 = getMergeLists(orders, eachSize);
        for (int i = 0; i < order1.size(); i++) {
            log.debug("i:::{}", JSON.toJSONString(order1.get(i)));
        }
//        List<OcBOrder> newList=new ArrayList<>();
//        Set<Long> newIdList=new HashSet<>();
//        for (int i = 0; i < list.size(); i++) {
//            String str = "";
//            for (int j = 0; j < list.get(i).size(); j++) {
//                str = str +"["+list.get(i).get(j).getId()+"]"+ list.get(i).get(j).getQtyAll().intValue() + "+";
//
//            }
//            System.out.println("第" + i + "个结果：" + str.substring(0, str.length() - 1));
//        }
//        Map<Long, List<OcBOrder>> map = newList.stream().filter(x->x.getReserveBigint05()!=null).collect(Collectors.groupingBy(OcBOrder::getReserveBigint05));
//        log.debug("map:{}", JSON.toJSONString(map));
//        map.forEach((k,v)->{
//            List<Long> collect = v.stream().map(OcBOrder::getId).distinct().collect(Collectors.toList());
//            log.debug("k:{},v:{}",k, JSON.toJSONString(collect));
//        });

        System.out.println(System.currentTimeMillis() - start);
    }

    /**
     * 循环获取集合数据量最小组合
     */
    public static List<List<OcBOrder>> getMergeLists(List<OcBOrder> orders, int eachSize) {
        boolean flag = true;
        List<List<OcBOrder>> order1 = new ArrayList<>();
        long start = System.currentTimeMillis();
        while (flag) {
            //超时 跳出循环
            if (System.currentTimeMillis() - start > 1000 * 120) {
                log.error("getMergeLists 执行时间超过120s,强制跳出");
                break;
            }
            List<OcBOrder> min = getMinSizeMergeList(orders, eachSize);
            if (CollectionUtils.isEmpty(min)) {
                flag = false;
            } else {
                orders.removeAll(min);
                order1.add(min);
            }
        }
        return order1;
    }


    /**
     * description：获取数据量最小的一组数据 其中必然包含最大值
     *
     * <AUTHOR>
     * @date 2021/10/24
     */
    public static List<OcBOrder> getMinSizeMergeList(List<OcBOrder> orders, int eachSize) {
        List<List<OcBOrder>> list = mergeOrderBySumQty(orders, eachSize);
        Optional<List<OcBOrder>> min = list.stream().min(Comparator.comparing(List<OcBOrder>::size));
        if (min.isPresent()) {
            return min.get();
        } else {
            return null;
        }
    }

    /**
     * 获取一组数据某字段加和为指定数字的全部组合
     */
    public static List<List<OcBOrder>> mergeOrderBySumQty(List<OcBOrder> dtoParam, Integer limitSumNumber) {
        List<List<OcBOrder>> reust = new ArrayList<List<OcBOrder>>();
        int sizeA = 1;
        int sizeC = 1;
        long start1 = System.currentTimeMillis();
        List<OcBOrder> d = null;
        for (int i = 0; i < dtoParam.size(); i++) {
            long second1 = System.currentTimeMillis();
            //超时跳出循环
            if (second1 - start1 > 1000 * 30) {
                log.error("mergeOrderBySumQty,循环[1]执行时间超过30s,跳出循环");
                break;
            }
            OcBOrder order = dtoParam.get(i);
            int s = order.getQtyAll().intValue();
            boolean flag1 = true;
            long start2 = System.currentTimeMillis();
            while (flag1) {
                if (flag1 = false) {
                    break;
                }

                if (dtoParam.size() == sizeA) {
                    flag1 = false;
                    break;
                }
                long second2 = System.currentTimeMillis();
                //超过20s跳出循环
                if (second2 - start2 > 1000 * 20) {
                    log.error("mergeOrderBySumQty,循环[2]执行时间超过20s,跳出循环");
                    break;
                }
                boolean flag2 = true;
                long start3 = System.currentTimeMillis();
                while (flag2) {
                    long second3 = System.currentTimeMillis();
                    //超时 跳出循环
                    if (second3 - start3 > 1000 * 10) {
                        log.error("mergeOrderBySumQty,循环[3]执行时间超过10s,跳出循环");
                        break;
                    }
                    if (dtoParam.size() == sizeC) {
                        sizeA++;
                        flag2 = false;
                        sizeC = sizeA;
                        break;
                    }
                    d = new ArrayList<OcBOrder>();
                    d.add(order);
                    for (int j = sizeC; j < dtoParam.size(); j++) {
                        OcBOrder ocBOrder = dtoParam.get(j);
                        s += ocBOrder.getQtyAll().intValue();
                        d.add(ocBOrder);
                        if (s == limitSumNumber) {
                            reust.add(d);
                            break;
                        }
                        if (dtoParam.size() - j == 1) {
                            s = order.getQtyAll().intValue();
                            sizeC++;
                            break;
                        }
                    }
                }
            }
        }
        return reust;
    }


    public static void test1(List<OcBOrder> orders, int eachSize) {
        List<OcBOrder> sortedQtyOrder = orders.stream().filter(x -> x.getQtyAll() != null).sorted(Comparator.comparing(OcBOrder::getQtyAll)).collect(Collectors.toList());
        log.debug("sortedQtyOrder:{}", JSON.toJSONString(sortedQtyOrder));
        Map<Long, Integer> idAndNeedQtyMap = new HashMap<>(sortedQtyOrder.size());
        Map<Long, OcBOrder> orderMap = new HashMap<>(sortedQtyOrder.size());
        for (OcBOrder order : sortedQtyOrder) {
            idAndNeedQtyMap.put(order.getId(), eachSize - order.getQtyAll().intValue());
            orderMap.put(order.getId(), order);
        }
        for (Long k1 : idAndNeedQtyMap.keySet()) {
            OcBOrder k2 = orderMap.get(k1);
            if (k2.getReserveBigint05() != null) {
                continue;
            }
            k2.setReserveBigint05(k2.getId());
            int needQty = idAndNeedQtyMap.get(k1);
            List<OcBOrder> list1 = new ArrayList<>();
            for (OcBOrder order : sortedQtyOrder) {
                if (order.getReserveBigint05() != null) {
                    continue;
                }
                if (order.getQtyAll().intValue() == needQty) {
                    order.setReserveBigint05(k2.getId());
                    list1.add(k2);
                    list1.add(order);
                    break;
                }
                if (needQty >= 1 || list1.size() == 1) {
                    if (order.getQtyAll().intValue() == needQty) {
                        order.setReserveBigint05(k2.getId());
                        needQty -= order.getQtyAll().intValue();
                    }
                }
            }
        }
        Map<Long, List<OcBOrder>> map = orders.stream().collect(Collectors.groupingBy(OcBOrder::getReserveBigint05));
        log.debug("map:{}", JSON.toJSONString(map));
    }

    private static String batchMergeOrderTest(List<OcBOrder> orders, int eachSize, MergeTypeEnum typeEnum, User user) {

        int size = orders.size(), startIndex = 0;
        int length = size;
        List<OcBOrder> subList;
        StringBuilder sb = new StringBuilder();
        while (size > 0) {
            if (!PlatFormEnum.VIP_JITX.getCode().equals(orders.get(0).getPlatform())) {
                if (size > eachSize) {
                    subList = orders.subList(startIndex, startIndex + eachSize);
                    startIndex += eachSize;
                } else {
                    subList = orders.subList(startIndex, length);
                }
                size -= eachSize;
                String mergeMsg = "不是JitX";
                if (org.apache.commons.lang3.StringUtils.isNotBlank(mergeMsg)) {
                    sb.append(mergeMsg);
                }
            } else {
                Optional<OcBOrder> existHasMergedOrder = orders.stream().filter(x -> x.getQtyAll() != null && x.getQtyAll().intValue() > 1).findAny();
                if (existHasMergedOrder.isPresent()) {
                    List<List<OcBOrder>> groupBySumQty = new ArrayList<>();
                    if (orders.size() < eachSize * 10) {
                        groupBySumQty = OrderMergeUtil.getMergeLists(orders, eachSize);
                    } else {
                        List<List<OcBOrder>> partitionList = Lists.partition(orders, eachSize * 10);
                        for (List<OcBOrder> partition : partitionList) {
                            List<List<OcBOrder>> mergeLists = OrderMergeUtil.getMergeLists(partition, eachSize);
                            if (CollectionUtils.isNotEmpty(mergeLists)) {
                                groupBySumQty.addAll(mergeLists);
                            }
                        }
                    }
                    if (!groupBySumQty.isEmpty()) {
                        log.debug("groupBySumQty:{}", JSON.toJSONString(groupBySumQty));
                    }
                    if (CollectionUtils.isNotEmpty(groupBySumQty)) {
                        for (List<OcBOrder> partition : groupBySumQty) {
                            log.debug("组合数据:{}", JSON.toJSONString(partition));
                            String mergeMsg = "组合数据";

                            if (org.apache.commons.lang3.StringUtils.isNotBlank(mergeMsg)) {
                                sb.append(mergeMsg);
                            }
                            size -= partition.size();
                        }
                        log.debug("剩余 orders：{}，size:{}", JSON.toJSONString(orders), orders.size());
                        length = orders.size();
                        String mergeMsg = "剩余数据";
                        size -= length;
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(mergeMsg)) {
                            sb.append(mergeMsg);
                        }
                    } else {
                        if (size > eachSize) {
                            subList = orders.subList(startIndex, startIndex + eachSize);
                            startIndex += eachSize;
                        } else {
                            subList = orders.subList(startIndex, length);
                        }
                        size -= eachSize;
                        log.debug("未包含组合数据:{}", JSON.toJSONString(subList));
                        String mergeMsg = "未包含组合数据";
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(mergeMsg)) {
                            sb.append(mergeMsg);
                        }
                    }
                } else {
                    if (size > eachSize) {
                        subList = orders.subList(startIndex, startIndex + eachSize);
                        startIndex += eachSize;
                    } else {
                        subList = orders.subList(startIndex, length);
                    }
                    size -= eachSize;
                    log.debug("没有大于1的数据:{}", JSON.toJSONString(subList));
                    String mergeMsg = "没有大于1的数据";
                    if (StringUtils.isNotBlank(mergeMsg)) {
                        sb.append(mergeMsg);
                    }
                }
            }
        }
        return sb.toString();
    }
}
