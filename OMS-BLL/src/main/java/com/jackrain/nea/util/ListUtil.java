package com.jackrain.nea.util;

import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @create 2020-09-18
 * @desc
 **/
public class ListUtil {

    /**
     * 全排列组合
     *
     * @param condition 已","分割的字符串
     * @return
     */
    public static JSONArray permutationAll(String condition) {
        condition = condition.contains(OcBOrderConst.ORDER_COMMA_CN) ?
                condition.replace(OcBOrderConst.ORDER_COMMA_CN, OcBOrderConst.ORDER_COMMA).trim() : condition.trim();
        String[] conditions = condition.split(OcBOrderConst.ORDER_COMMA);
        JSONArray jsonArray = new JSONArray();
        for (String value : conditions) {
            if (value == null) {
                continue;
            }
            String trim = value.trim();
            if (trim.length() < 1) {
                continue;
            }
            jsonArray.add(trim);
        }
        if (conditions.length > 1) {
            jsonArray.add(condition);
        }
        if (conditions.length > 5) {
            return jsonArray;
        }
        // 全排列组合查询，去除当数量大于5时,全排列结果过大
        List<String> list = Arrays.asList(conditions);
        List<String> newList = permutationAll(list);
        if (CollectionUtils.isNotEmpty(newList)) {
            for (String val : newList) {
                if (val == null) {
                    continue;
                }
                String k = val.trim();
                if (k.equals(condition)) {
                    continue;
                }
                if (k.length() < 1) {
                    continue;
                }
                jsonArray.add(k);
            }
        }
        return jsonArray;
    }

    /**
     * 获取list从2位到list.size的全排列组合
     *
     * @param list 待排列组合字符集合
     * @return
     * <AUTHOR>
     */
    public static List<String> permutationAll(List<String> list) {
        List<String> allList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return allList;
        }
        for (int i = 2; i <= list.size(); i++) {
            List<String> noRepeatList = permutationNoRepeat(list, i);
            allList.addAll(noRepeatList);
        }
        return allList;
    }

    /**
     * 排列组合(字符不重复排列)
     *
     * @param list   待排列组合字符集合(忽略重复字符)
     * @param length 排列组合生成长度
     * @return 指定长度的排列组合后的字符串集合
     * <AUTHOR>
     */
    public static List<String> permutationNoRepeat(List<String> list, int length) {
        Stream<String> stream = list.stream().distinct();
        for (int n = 1; n < length; n++) {
            stream = stream.flatMap(
                    str -> list.stream().filter(temp -> !str.contains(temp)).map(a -> {
                        if (org.springframework.util.StringUtils.isEmpty(a)) {
                            return "";
                        }
                        return str.concat(",").concat(a);
                    })
            );
        }
        return stream.collect(Collectors.toList());
    }
}
