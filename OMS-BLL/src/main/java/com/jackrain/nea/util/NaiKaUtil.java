package com.jackrain.nea.util;

/**
 * @ClassName NaiKaUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/31 16:00
 * @Version 1.0
 */
public class NaiKaUtil {

//    public static List<Integer> buildNaiKaType() {
//        List<Integer> businessType = new ArrayList<>();
//        businessType.add(OrderBusinessTypeEnum.ON_LINE_MILK_CARD_SALE.getCode());
//        businessType.add(OrderBusinessTypeEnum.ON_LINE_FREE_MILK_CARD.getCode());
//        businessType.add(OrderBusinessTypeEnum.MILK_CARD_GIVE_OUT_WAREHOUSE.getCode());
//        businessType.add(OrderBusinessTypeEnum.ELECTRONICS_MILK_CARD_SALE.getCode());
//        businessType.add(OrderBusinessTypeEnum.OFFLINE_MILK_CARD_SALE.getCode());
//        businessType.add(OrderBusinessTypeEnum.MILK_CARD_REISSUE.getCode());
//        return businessType;
//    }
}
