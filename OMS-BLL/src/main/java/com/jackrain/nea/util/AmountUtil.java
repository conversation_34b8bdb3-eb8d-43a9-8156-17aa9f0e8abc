package com.jackrain.nea.util;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 金额计算util
 *
 * <AUTHOR>
 */
public class AmountUtil {

    /**
     * 分期金额计算，总金额/期数，最后一期用减法
     *
     * @param size
     * @param currentNum
     * @param amount
     * @return
     */
    public static BigDecimal cycleBuyPartAmount(int size, int currentNum, BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return BigDecimal.ZERO;
        }
        if (size == currentNum) {
            //最后一单用减法
            int i = size - 1;
            BigDecimal multiply = amount.divide(new BigDecimal(size), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(i));
            return amount.subtract(multiply);
        }

        return amount.divide(new BigDecimal(size), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * a/b
     *
     * @param a
     * @param b
     * @return
     */
    public static BigDecimal divide(BigDecimal a, BigDecimal b) {
        if (Objects.isNull(a) || Objects.isNull(b)) {
            return BigDecimal.ZERO;
        }
        return a.divide(b, 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 返回值，为空时返回0
     *
     * @param a
     * @return
     */
    public static BigDecimal getNoDefault(BigDecimal a) {
        if (Objects.isNull(a)) {
            return BigDecimal.ZERO;
        }
        return a;
    }

    public static BigDecimal divideThree(BigDecimal a, BigDecimal b, BigDecimal c) {
        if (Objects.isNull(a) || Objects.isNull(b) || Objects.isNull(c)) {
            return BigDecimal.ZERO;
        }
        BigDecimal divide = a.divide(b, 10, BigDecimal.ROUND_HALF_UP);
        return divide.divide(c, 2, BigDecimal.ROUND_HALF_UP);
    }
}
