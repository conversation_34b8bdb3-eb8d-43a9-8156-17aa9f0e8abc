package com.jackrain.nea.util;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentLogDO;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * description：丢件单ES公共方法
 *
 * <AUTHOR>
 * @date 2020/05/24
 */
@Slf4j
public class AcPayableAdjustmentPushESUtil {

    private static final String ORDER_INDEX = AcConstant.INDEX_AC_F_PAYABLE_ADJUSTMENT;
    private static final String ORDER_TYPE = AcConstant.TYPE_AC_F_PAYABLE_ADJUSTMENT;
    private static final String ITEM_TYPE = AcConstant.TYPE_AC_F_PAYABLE_ADJUSTMENT_ITEM;
    private static final String PARENT_FIELD = "AC_F_PAYABLE_ADJUSTMENT_ID";

    /**
     * description：批量推送主表数据
     *
     * <AUTHOR>
     * @date 2021/1/5
     */
    public static void batchPushOrder(List<AcFPayableAdjustmentDO> payableAdjustmentList) {
        try {
            if (!SpecialElasticSearchUtil.indexExists(ORDER_INDEX)) {
                createOrderIndex();
            }
            SpecialElasticSearchUtil.indexDocuments(ORDER_INDEX, ORDER_TYPE, payableAdjustmentList);
        } catch (Exception e) {
            log.error(LogUtil.format("批量推送丢件单主表数据到ES异常:"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * description：推送主表数据 根据id获取再查一次数据 保证数据最新
     *
     * <AUTHOR>
     * @date 2021/1/5
     */
    public static boolean pushOrder(Long id) {
        AcFPayableAdjustmentMapper mapper = ApplicationContextHandle.getBean(AcFPayableAdjustmentMapper.class);
        return pushOrder(mapper.selectById(id));
    }

    /**
     * description：推送主表和明细表数据 根据id获取再查一次数据 保证数据最新
     *
     * <AUTHOR>
     * @date 2021/1/5
     */
    public static boolean pushOrderAndItem(Long id) {
        AcFPayableAdjustmentMapper mapper = ApplicationContextHandle.getBean(AcFPayableAdjustmentMapper.class);
        AcFPayableAdjustmentItemMapper itemMapper = ApplicationContextHandle.getBean(AcFPayableAdjustmentItemMapper.class);
        AcFPayableAdjustmentDO order = mapper.selectById(id);
        List<AcFPayableAdjustmentItemDO> itemList = itemMapper.selectListByMainId(id);
        return pushData(order, itemList);
    }

    public static boolean pushData(AcFPayableAdjustmentDO order, List<AcFPayableAdjustmentItemDO> itemList) {
        try {
            // 判断索引是否存在
            if (!ElasticSearchUtil.indexExists(ORDER_INDEX)) {
                createOrderIndex();
            }
            // 主表推送es
            if (order != null) {
                Boolean result = ElasticSearchUtil.indexDocument(ORDER_INDEX, ORDER_TYPE, order, order.getId());
            } else {
                return false;
            }
            if (!itemList.isEmpty()) {
                SpecialElasticSearchUtil.indexDocuments(ORDER_INDEX, ITEM_TYPE, itemList, PARENT_FIELD);
                return true;
            } else {
            }
        } catch (Exception e) {

        }
        return false;
    }

    /**
     * description：推送主表数据
     *
     * <AUTHOR>
     * @date 2021/1/5
     */
    public static boolean pushOrder(AcFPayableAdjustmentDO order) {
        try {
            // 判断索引是否存在
            if (!ElasticSearchUtil.indexExists(ORDER_INDEX)) {
                createOrderIndex();
            }
            // 主表推送es
            if (order != null) {
                Boolean result = ElasticSearchUtil.indexDocument(ORDER_INDEX, ORDER_TYPE, order, order.getId());
                return result;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("推送丢件单主表数据ES异常:"), Throwables.getStackTraceAsString(e));

        }
        return false;
    }

    /**
     * description：推送主表数据
     *
     * <AUTHOR>
     * @date 2021/1/5
     */
    public static boolean deleteOrder(AcFPayableAdjustmentDO order) {
        try {
            // 判断索引是否存在
            if (!ElasticSearchUtil.indexExists(ORDER_INDEX)) {
                createOrderIndex();
            }
            // 主表推送es
            if (order != null) {
                Boolean result = ElasticSearchUtil.delDocument(ORDER_INDEX, ORDER_TYPE, order.getId());
                return result;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("推送丢件单主表数据ES异常:"), Throwables.getStackTraceAsString(e));


        }
        return false;
    }

    /**
     * description：批量推送子表数据
     *
     * <AUTHOR>
     * @date 2021/1/5
     */
    public static void pushItemList(List<AcFPayableAdjustmentDO> itemList) {
        try {
            // 判断索引是否存在
            if (!ElasticSearchUtil.indexExists(ORDER_INDEX)) {
                createOrderIndex();
            }
            // 推送es
            if (!itemList.isEmpty()) {
                SpecialElasticSearchUtil.indexDocuments(ORDER_INDEX, ITEM_TYPE, itemList, PARENT_FIELD);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("推送丢件单明细数据到ES异常:"), Throwables.getStackTraceAsString(e));
        }
    }

    private static boolean createOrderIndex() throws IOException {
        List<Class> esChildsClass = new ArrayList<>();
        esChildsClass.add(AcFPayableAdjustmentItemDO.class);
        esChildsClass.add(AcFPayableAdjustmentLogDO.class);
        return SpecialElasticSearchUtil.indexCreate(esChildsClass, AcFPayableAdjustmentDO.class);
    }

}
