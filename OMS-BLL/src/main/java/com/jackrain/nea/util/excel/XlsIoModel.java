package com.jackrain.nea.util.excel;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * excel.export.helper.entity
 *
 * @author: xiWen.z
 * create at: 2019/8/7 0007
 */
@Data
public class XlsIoModel {

    /**
     * static.keys
     */
    static final String PTN = "primaryTable";
    static final String TN = "tableName";
    static final String SN = "sheetName";
    static final String IDX = "index";
    static final String SOT = "sort";
    static final String SQL = "sql";
    static final String CROW = "currentRow";


    static final String ST = "st";
    static final String TP = "tp";
    static final String SERIL = "序号";

    /**
     * level.file
     * excel.name
     */
    private String excelName;
    /**
     * level.sheet
     */
    private String primaryTable;
    /**
     * level.table
     * sheet.information
     * 1. DBTable name
     * 2. excel sheet name
     * 3. sort
     * e.g: {{tableName:oc_b_order, sheetName:oc_b_order_text , index:1}....}
     */
    private List<Map<String, Object>> sheetInfo;

    /**
     * level.field
     * table.header.text
     * 1. DBTable name
     * 2. List: table header display column name
     * e.g:   {oc_b_order: {text1, text2, text3...}...}
     */
    private Map<String, List<String>> headerText;


    /**
     * level.field
     * table.header.key
     * 1. DBTable name
     * 2. List:  table header column entity field
     * e.g: {oc_b_order: {key1,key2,key3...}...}
     */
    private Map<String, List<String>> headerKey;

    /**
     * level.field
     * table.column.field
     * this must be a subset of headerKey
     * 1. DBTable name
     * 2. List:  table header column attention field
     * e.g: {oc_b_order: {key1,key2,key3...}...}
     */
    private Map<String, List<String>> vifField;

    /**
     * level.field
     * table.column.vale
     * 1. DBTable name
     * 2. field name
     * 3. value  Text
     * e.g: {oc_b_order: {type: {st:xlsTyp.Group, key:value...}...}...}
     */
    private Map<String, Map<String, Map<Object, Object>>> convertSource;

}
