package com.jackrain.nea.util;

/**
 * @Desc : oms 同步线程池
 * <AUTHOR> xiWen
 * @Date : 2020/8/14
 */
public class OMSThreadPoolFactory {

    /**
     * 1.1.1 合单.task.mq.生产者.分库查询.
     */
//    private static ThreadPoolExecutor taskMergeOrderPool;
//    private static final String TASK_MERGE_ORDER_POOL_NAME = "R3_OMS_MERGE_ORDER_MQ_PRODUCER_THREAD_POOL_%d";

    /**
     * 1.2.1 合单.多线程处理.consume.消费端.  | oc_b_order
     */
//    private static ThreadPoolExecutor mergeOrderPool;
//    private static final String MERGE_ORDER_POOL_NAME = "R3_OMS_MERGE_ORDER_THREAD_POOL_%d";

//    /**
//     * 1.3.1 转单.京东退款单.task.mq. 查询: ip_b_jingdong_refund
//     */
//    private static ThreadPoolExecutor jdRefundOrderPool;
//    private static final String TASK_JD_REFUND_ORDER_POOL_NAME = "R3_OMS_JD_REFUND_ORDER_TO_MQ_TASK_THREAD_POOL_%d";

    /**
     * 1.4.1 传WMS.订单传wms填充推单延时时间.task 查询: oc_b_to_wms_task
     */
//    private static ThreadPoolExecutor orderToWmsPushDelayPool;
//    private static final String TASK_ORDER_TO_WMS_PUSH_DELAY = "R3_ORDER_TO_WMS_PUSH_DELAY_TASK_THREAD_POOL_%d";

//    /**
//     * 1.5.1 传wms.订单传wms.task. 查询: oc_b_to_wms_task . task.第一个
//     */
//    private static ThreadPoolExecutor orderPreToWmsPool;
//    private static final String TASK_ORDER_PRE_TO_WMS_POOL_NAME = "R3_OMS_PRE_TO_WMS_TASK_THREAD_POOL_%d";
//
//    private static ThreadPoolExecutor naiKaUnFreezePool;
//    private static final String TASK_NAIKA_UNFREEZE_POOL_NAME = "R3_OMS_NAIKA_UNFREEZE_POOL_NAME_TASK_THREAD_POOL_%d";
//
//    private static ThreadPoolExecutor naiKaAccountPool;
//    private static final String TASK_NAIKA_ACCOUNT_POOL_NAME = "R3_OMS_NAIKA_ACCOUNT_POOL_NAME_TASK_THREAD_POOL_%d";

//    private static ThreadPoolExecutor naiKaReturnInPool;
//    private static final String TASK_NAIKA_RETURN_IN_POOL_NAME = "R3_OMS_NAIKA_RETURN_IN_POOL_NAME_TASK_THREAD_POOL_%d";

//    /**
//     * 1.6.1 传wms.订单传wms.task.商品属性拆单 查询: OC_B_WAREHOUSE_SPLIT_TASK. task.第二个
//     */
//    private static ThreadPoolExecutor orderToWmsSplitByGoodsAttrPool;
//    private static final String TASK_ORDER_2_WMS_SPLIT_NAME = "R3_OMS_TO_WMS_SPLIT_GOODS_ATTRIBUTE_TASK_THREAD_POOL_%d";

//    /**
//     * 1.7.1 拆单.京东拆单任务 task 查询：oc_b_order_jingdong_split_task
//     */
//    private static ThreadPoolExecutor jDOrderSplitPool;
//    private static final String TASK_JD_ORDER_SPLIT_POOL_NAME = "R3_OMS_JD_SPLIT_TASK_THREAD_POOL_%d";

//    /**
//     * 1.8.1 拆单.缺货拆单补偿任务 task.mq 查询：oc_b_order_split_task
//     */
//    private static ThreadPoolExecutor orderOutOfStockSplitPool;
//    private static final String TASK_OUT_OF_STOCK_SPLIT_POOL_NAME = "R3_OMS_ORDER_SPLIT_BY_STOCK_TASK_THREAD_POOL_%d";

//    /**
//     * 1.9.1 拆单.发货缺货重新占单补偿任务 task.mq 查询：oc_b_order
//     */
//    private static ThreadPoolExecutor orderSplitPool;
//    private static final String TASK_ORDER_SPLIT_POOL_NAME = "R3_OMS_ORDER_SPLIT_TASK_THREAD_POOL_%d";

//
//    /**
//     * 1.10.1 占单.新占单服务定时任务 task.mq 查询：oc_b_tobeconfirmed_task
//     */
//    private static ThreadPoolExecutor toBeConfirmedOrderPool;
//    private static final String TASK_TO_BE_CONFIRMED_ORDER_POOL_NAME = "R3_OMS_TO_CONFIRMED_ORDER_TASK_THREAD_POOL_%d";
//
//    /**
//     * 1.10.1 待寻源
//     */
//    private static ThreadPoolExecutor occupyOrderPool;
//    private static final String TASK_OCCUPY_ORDER_POOL_NAME = "R3_OMS_OCCUPY_ORDER_TASK_THREAD_POOL_%d";

//
//    /**
//     * 1.11.1 转单.淘宝转单补偿任务 task.mq 查询：ip_b_taobao_order
//     */
//    private static ThreadPoolExecutor tBOrderTransferPool;
//    private static final String TASK_TAO_BAO_ORDER_TRANSFER = "R3_OMS_TAO_BAO_ORDER_TRANSFER_TASK_THREAD_POOL_%d";

//
//    /**
//     * 2.1.1 JIT_X订单.创建改仓工单任务
//     */
//    private static ThreadPoolExecutor jitXCreatePool;
//    private static final String JIT_X_CREATE_POOL_NAME = "R3_OMS_JITX_CREATE_CHANGE_WAREHOUSE_THREAD_POOL_%d";
//
//    /**
//     * 2.2.1 JIT_X订单.获取改仓工单任务
//     */
//    private static ThreadPoolExecutor jitXGetPool;
//    private static final String JIT_X_GET_POOL_NAME = "R3_OMS_JITX_GET_CHANGE_WAREHOUSE_THREAD_POOL_%d";


//    /**
//     * 3.1.1 销售退.同步第三方系统.分库查询. 查询: OC_B_TASK_RETURN_SYNC | oc_b_return_order [功能暂未使用]
//     */
//    private static ThreadPoolExecutor returnSync3ThPool;
//    private static final String TASK_RETURN_SYNC_3TH_SYS_POOL_NAME = "R3_OMS_NEXT_TAO_RETURN_TASK_THREAD_POOL_%d";


//    /**
//     * 3.1.1 销售退.同步第三方系统.分库查询. 查询: OC_B_TASK_RETURN_SYNC | oc_b_return_order [功能暂未使用]
//     */
//    private static ThreadPoolExecutor syncAcPool;
//    private static final String TASK_SYNC_TO_AC_SYS_POOL_NAME = "R3_OMS_TO_AC_TASK_THREAD_POOL_%d";


//    /**
//     * 自动解挂  ==== 自动释放卡单
//     */
//    private static ThreadPoolExecutor autoReleaseHangPool;
//    private static final String TASK_AUTO_RELEASE_HANG_POOL_NAME = "R3_OMS_AUTO_RELEASE_HANG_TASK_THREAD_POOL_%d";

//    /**
//     *
//     */
//    private static ThreadPoolExecutor salesRecordPool;
//    private static final String TASK_SALES_RECORD_POOL_NAME = "R3_OMS_SALES_RECORD_TASK_THREAD_POOL_%d";


//
//    public static ThreadPoolExecutor getSyncAcPool() {
//        return syncAcPool == null
//                ? (syncAcPool = OMSThreadPoolBuilder.build(TASK_SYNC_TO_AC_SYS_POOL_NAME)) : syncAcPool;
//    }

//    /**
//     * 0.1 公共.task.分库查询.
//     *
//     * @return 根据线程池名获取线程池
//     */
//    public static ThreadPoolExecutor getTaskPoolByName(String poolName) {
//        return OMSThreadPoolBuilder.build(poolName);
//    }

//    /**
//     * 1.1.2 合单
//     *
//     * @return 合单分库查询mq生产发送线程池
//     */
//    public static ThreadPoolExecutor getTaskMergeOrderPool() {
//        return taskMergeOrderPool == null
//                ? (taskMergeOrderPool = OMSThreadPoolBuilder.build(TASK_MERGE_ORDER_POOL_NAME)) : taskMergeOrderPool;
//    }

//    /**
//     * 1.2.2 合单
//     *
//     * @return 合单消费端线程池
//     */
//    public static ThreadPoolExecutor getMergeOrderPool() {
//        return mergeOrderPool == null
//                ? (mergeOrderPool = OMSThreadPoolBuilder.build(MERGE_ORDER_POOL_NAME, 68))
//                : mergeOrderPool;
//    }

//    /**
//     * 1.3.2 转单.京东退款单
//     *
//     * @return 京东退款单.转单线
//     */
//    public static ThreadPoolExecutor getJdRefundOrderPool() {
//        return jdRefundOrderPool == null
//                ? (jdRefundOrderPool = OMSThreadPoolBuilder.build(TASK_JD_REFUND_ORDER_POOL_NAME)) : jdRefundOrderPool;
//    }

//    /**
//     * 1.4.2 传WMS.订单传wms填充推单延时时间.task 查询: oc_b_to_wms_task
//     */
//    public static ThreadPoolExecutor getOrderToWmsPushDelayPool() {
//        return orderToWmsPushDelayPool == null
//                ? (orderToWmsPushDelayPool = OMSThreadPoolBuilder.build(TASK_ORDER_TO_WMS_PUSH_DELAY))
//                : orderToWmsPushDelayPool;
//    }

//    /***
//     * 1.5.2 传wms
//     * @return 订单传wms
//     */
//    public static ThreadPoolExecutor getOrderPreToWmsPool() {
//        return orderPreToWmsPool == null
//                ? (orderPreToWmsPool = OMSThreadPoolBuilder.build(TASK_ORDER_PRE_TO_WMS_POOL_NAME))
//                : orderPreToWmsPool;
//    }

//    /***
//     * 传小程序 解冻
//     * @return
//     */
//    public static ThreadPoolExecutor getNaiKaUnFreezePool() {
//        return naiKaUnFreezePool == null
//                ? (naiKaUnFreezePool = OMSThreadPoolBuilder.build(TASK_NAIKA_UNFREEZE_POOL_NAME))
//                : naiKaUnFreezePool;
//    }


//    /***
//     * 传小程序 对账
//     * @return
//     */
//    public static ThreadPoolExecutor getNaiKaAccountPool() {
//        return naiKaAccountPool == null
//                ? (naiKaAccountPool = OMSThreadPoolBuilder.build(TASK_NAIKA_ACCOUNT_POOL_NAME))
//                : naiKaAccountPool;
//    }

//    public static ThreadPoolExecutor getNaiKaReturnInPool() {
//        return naiKaReturnInPool == null ? (naiKaReturnInPool = OMSThreadPoolBuilder.build(TASK_NAIKA_RETURN_IN_POOL_NAME)) : naiKaReturnInPool;
//    }

//    /**
//     * 1.6.2 传wms.订单传wms.task.商品属性拆单 查询: OC_B_WAREHOUSE_SPLIT_TASK
//     *
//     * @return 商品属性拆单
//     */
//    public static ThreadPoolExecutor getOrderToWmsSplitByGoodsAttrPool() {
//        return orderToWmsSplitByGoodsAttrPool == null
//                ? (orderToWmsSplitByGoodsAttrPool = OMSThreadPoolBuilder.build(TASK_ORDER_2_WMS_SPLIT_NAME))
//                : orderToWmsSplitByGoodsAttrPool;
//    }

//    /**
//     * 1.7.2 京东订单拆分任务
//     *
//     * @return 京东订单拆分任务
//     */
//    public static ThreadPoolExecutor getJDOrderSplitPool() {
//        return jDOrderSplitPool == null
//                ? (jDOrderSplitPool = OMSThreadPoolBuilder.build(TASK_JD_ORDER_SPLIT_POOL_NAME)) : jDOrderSplitPool;
//    }

//    /**
//     * 1.8.2 订单缺货拆分补偿
//     *
//     * @return 订单缺货拆分补偿任务
//     */
//    public static ThreadPoolExecutor getOrderOutOfStockSplitPool() {
//        return orderOutOfStockSplitPool == null
//                ? (orderOutOfStockSplitPool = OMSThreadPoolBuilder.build(TASK_OUT_OF_STOCK_SPLIT_POOL_NAME))
//                : orderOutOfStockSplitPool;
//    }

//    /**
//     * 1.9.2 订单拆分补偿
//     *
//     * @return 订单拆分补偿任务
//     */
//    public static ThreadPoolExecutor getOrderSplitPool() {
//        return orderSplitPool == null
//                ? (orderSplitPool = OMSThreadPoolBuilder.build(TASK_ORDER_SPLIT_POOL_NAME)) : orderSplitPool;
//    }

//    /**
//     * 1.10.2 新占单服务
//     *
//     * @return 新占单服务定时任务
//     */
//    public static ThreadPoolExecutor getToBeConfirmedOrderPool() {
//        return toBeConfirmedOrderPool == null
//                ? (toBeConfirmedOrderPool = OMSThreadPoolBuilder.build(TASK_TO_BE_CONFIRMED_ORDER_POOL_NAME))
//                : toBeConfirmedOrderPool;
//    }

//
//    public static ThreadPoolExecutor getOccupyOrderPool() {
//        return occupyOrderPool == null
//                ? (occupyOrderPool = OMSThreadPoolBuilder.build(TASK_OCCUPY_ORDER_POOL_NAME))
//                : occupyOrderPool;
//    }

//    public static ThreadPoolExecutor getSalesRecordPool() {
//        return salesRecordPool == null
//                ? (salesRecordPool = OMSThreadPoolBuilder.build(TASK_SALES_RECORD_POOL_NAME))
//                : salesRecordPool;
//    }
//    /**
//     * 1.11.2 淘宝转单
//     *
//     * @return 淘宝转单生产端线程池
//     */
//    public static ThreadPoolExecutor getTBOrderTransferPool() {
//        return tBOrderTransferPool == null
//                ? (tBOrderTransferPool = OMSThreadPoolBuilder.build(TASK_TAO_BAO_ORDER_TRANSFER)) : tBOrderTransferPool;
//    }

//    /**
//     * 2.1.2 JIT_X订单
//     *
//     * @return 创建改仓工单任务
//     */
//    public static ThreadPoolExecutor getJitXCreatePool() {
//        return jitXCreatePool == null
//                ? (jitXCreatePool = OMSThreadPoolBuilder.build(JIT_X_CREATE_POOL_NAME)) : jitXCreatePool;
//    }
//
//    /**
//     * 2.2.2 JIT_X订单
//     *
//     * @return 获取改仓工单任务
//     */
//    public static ThreadPoolExecutor getJitXGetPool() {
//        return jitXGetPool == null
//                ? (jitXGetPool = OMSThreadPoolBuilder.build(JIT_X_GET_POOL_NAME)) : jitXGetPool;
//    }

//    /**
//     * 3.1.2 同步第三方系统.销售退.分库查询.
//     *
//     * @return 销售退同步线程池
//     */
//    public static ThreadPoolExecutor getReturnSync3ThPool() {
//        return returnSync3ThPool == null
//                ? (returnSync3ThPool = OMSThreadPoolBuilder.build(TASK_RETURN_SYNC_3TH_SYS_POOL_NAME))
//                : returnSync3ThPool;
//    }

//    /**
//     * 审核服务定时任务 分库查询.
//     */
//    private static ThreadPoolExecutor auditOrderPool;
//    private static final String TASK_AUDIT_ORDER_POOL_NAME = "R3_OMS_AUDIT_TASK_THREAD_POOL_%d";

//    public static ThreadPoolExecutor getAuditOrderPool() {
//        return auditOrderPool == null
//                ? (auditOrderPool = OMSThreadPoolBuilder.build(TASK_AUDIT_ORDER_POOL_NAME))
//                : auditOrderPool;
//    }

//    private static ThreadPoolExecutor auditOrderCompensatePool;
//    private static final String TASK_AUDIT_ORDER_COMPENSATE_POOL_NAME = "R3_OMS_AUDIT_COMPENSATE_TASK_THREAD_POOL_%d";

//    /**
//     * 审核补偿服务定时任务 分库查询.
//     */
//    public static ThreadPoolExecutor getAuditOrderCompensatePool() {
//        return auditOrderCompensatePool == null
//                ? (auditOrderCompensatePool = OMSThreadPoolBuilder.build(TASK_AUDIT_ORDER_COMPENSATE_POOL_NAME))
//                : auditOrderCompensatePool;
//    }
//    /**
//     * 补偿审核服务定时任务 分库查询.
//     */
//    private static ThreadPoolExecutor compensateAuditOrderPool;
//    private static final String TASK_COMPENSATE_AUDIT_ORDER_POOL_NAME = "R3_OMS_AUDIT_COMPENSATE_TASK_THREAD_POOL_%d";

//    public static ThreadPoolExecutor getCompensateAuditOrderPool() {
//        return compensateAuditOrderPool == null
//                ? (compensateAuditOrderPool = OMSThreadPoolBuilder.build(TASK_COMPENSATE_AUDIT_ORDER_POOL_NAME))
//                : compensateAuditOrderPool;
//    }

//    /**
//     * 换货转换线城池
//     */
//    private static ThreadPoolExecutor exchangeOrderExecutor;
//    private static String THREAD_POOL_NAME_EXCHANGE_ORDER = "R3_OMS_TOBAO_EXCHANGE_ORDER_TO_MQ_TASK_THREAD_POOL_%d";
//
//    public static ThreadPoolExecutor getExchangeOrderPool() {
//        return exchangeOrderExecutor == null
//                ? (exchangeOrderExecutor = OMSThreadPoolBuilder.build(THREAD_POOL_NAME_EXCHANGE_ORDER))
//                : exchangeOrderExecutor;
//    }

//    /**
//     * 唯品会时效订单转单补偿任务 - 发送mq形式
//     */
//    private static ThreadPoolExecutor vipTimeOrderExecutor;
//    private static String THREAD_POOL_NAME_VIP_TIME_ORDER = "R3_OMS_VIP_TIME_ORDER_TASK_THREAD_POOL_%d";
//
//    public static ThreadPoolExecutor getVipTimeOrderThreadPool() {
//        return vipTimeOrderExecutor == null
//                ? (vipTimeOrderExecutor = OMSThreadPoolBuilder.build(THREAD_POOL_NAME_VIP_TIME_ORDER))
//                : vipTimeOrderExecutor;
//    }

//    /**
//     * 经销商占用 取消占用补偿
//     */
//    private static ThreadPoolExecutor vipJitxDealerOrderExecutor;
//    private static String THREAD_POOL_VIP_JITX_DEALER_ORDER_ORDER = "R3_OMS_DEALER_TASK_MAKE_UP_THREAD_POOL_%d";
//
//    public static ThreadPoolExecutor getVipJitxDealerOrderThreadPool() {
//        return vipJitxDealerOrderExecutor == null
//                ? (vipJitxDealerOrderExecutor = OMSThreadPoolBuilder.build(THREAD_POOL_VIP_JITX_DEALER_ORDER_ORDER))
//                : vipJitxDealerOrderExecutor;
//    }

//    /**
//     * LTS任务调用库存中心接口（生成入库通知单/结果单，逻辑收货单）:补偿任务可补充旧数据
//     */
//    private static ThreadPoolExecutor omsCreatePhynoticsTaskExecutor;
//    private static String THREAD_POOL_OMS_CREATE_PHYNOTICS_TASK = "R3_OMS_CREATE_PHYNOTICS_TASK_THREAD_POOL_%d";
//
//    public static ThreadPoolExecutor getOmsCreatePhynoticsTaskExecutorThreadPool() {
//        return omsCreatePhynoticsTaskExecutor == null
//                ? (omsCreatePhynoticsTaskExecutor = OMSThreadPoolBuilder.build(THREAD_POOL_OMS_CREATE_PHYNOTICS_TASK))
//                : omsCreatePhynoticsTaskExecutor;
//    }

//    /**
//     * 短信任务线程池创建
//     */
//    private static ThreadPoolExecutor omsSendMsgTaskExecutor;
//    private static String THREAD_POOL_OMS_SEND_MSG_TASK = "R3_OMS_SEND_MSG_TASK_THREAD_POOL_%d";
//
//    public static ThreadPoolExecutor getOmsSendMsgTaskThreadPool() {
//        return omsSendMsgTaskExecutor == null
//                ? (omsSendMsgTaskExecutor = OMSThreadPoolBuilder.build(THREAD_POOL_OMS_SEND_MSG_TASK))
//                : omsSendMsgTaskExecutor;
//    }

//    /**
//     * oms调用wing发货任务
//     */
//    private static ThreadPoolExecutor omsWingDeliveryTaskExecutor;
//    public static String THREAD_POOL_WING_DELIVERY_TASK = "R3_OMS_CREATE_AutoToWingDeliveryTask_THREAD_POOL_%d";
//
//    public static ThreadPoolExecutor getWingDeliveryTaskThreadPool() {
//        return omsWingDeliveryTaskExecutor == null
//                ? (omsWingDeliveryTaskExecutor = OMSThreadPoolBuilder.build(THREAD_POOL_WING_DELIVERY_TASK))
//                : omsWingDeliveryTaskExecutor;
//    }

//    /**
//     * 淘宝退单转单传AG取消发货失败重传补偿任务
//     */
//    private static ThreadPoolExecutor omsRefundOrderToAgTaskExecutor;
//    public static String THREAD_POOL_OMS_REFUND_ORDER_TO_AG_TASK = "R3_OMS_REFUND_ORDER_TO_AG_TASK_THREAD_POOL_%d";
//
//    public static ThreadPoolExecutor getOmsRefundOrderToAgTaskThreadPool() {
//        return omsRefundOrderToAgTaskExecutor == null
//                ? (omsRefundOrderToAgTaskExecutor = OMSThreadPoolBuilder.build(THREAD_POOL_OMS_REFUND_ORDER_TO_AG_TASK))
//                : omsRefundOrderToAgTaskExecutor;
//    }

//    /**
//     * 补偿同步结算任务
//     */
//    private static ThreadPoolExecutor omsSettlementOrderExecutor;
//    public static String THREAD_POOL_OMS_SETTLEMENT_ORDER_TASK = "R3_OMS_SETTLEMENT_ORDER_THREAD_POOL_%d";
//
//    public static ThreadPoolExecutor getOmsSettlementOrderThreadPool() {
//        return omsSettlementOrderExecutor == null
//                ? (omsSettlementOrderExecutor = OMSThreadPoolBuilder.build(THREAD_POOL_OMS_SETTLEMENT_ORDER_TASK))
//                : omsSettlementOrderExecutor;
//    }

//    /**
//     * 计算sku库存线程池
//     */
//    private static ExecutorService calculateSkuStockTaskExecutor;
//    public static String THREAD_POOL_NAME_CALCULATE_SKU_STOCK = "R3_OMS_SETTLEMENT_ORDER_THREAD_POOL_%d";
//
//    public static ExecutorService getCalculateSkuStockTaskExecutor() {
//        return calculateSkuStockTaskExecutor == null
//                ? (calculateSkuStockTaskExecutor = OMSThreadPoolBuilder.build(THREAD_POOL_NAME_CALCULATE_SKU_STOCK))
//                : calculateSkuStockTaskExecutor;
//    }

//    /**
//     * 重新寻源临时定时任务（解决17w重新寻源）
//     */
//    private static ExecutorService omsCreateOccupyStockTempExecutor;
//    public static String THREAD_POOL_OMS_CREATE_OCCUPY_STOCK_TEMP = "R3_OMS_CREATE_OCCUPY_STOCK_TEMP_THREAD_POOL_%d";
//
//    public static ExecutorService getOmsCreateOccupyStockTempExecutor() {
//        return omsCreateOccupyStockTempExecutor == null
//                ? (omsCreateOccupyStockTempExecutor = OMSThreadPoolBuilder.build(THREAD_POOL_OMS_CREATE_OCCUPY_STOCK_TEMP))
//                : omsCreateOccupyStockTempExecutor;
//    }

//    /**
//     * 自动释放Hold订单
//     */
//    private static ExecutorService omsOrderHoldItemExecutor;
//    public static String THREAD_POOL_OMS_ORDER_HOLD_ITEM = "R3_OMS_ORDER_HOLD_ITEM_TASK_THREAD_POOL_%d";
//
//    public static ExecutorService getAutoReleaseOmsOrderHoldItemExecutor() {
//        return omsOrderHoldItemExecutor == null
//                ? (omsOrderHoldItemExecutor =
//                OMSThreadPoolBuilder.build(THREAD_POOL_OMS_ORDER_HOLD_ITEM))
//                : omsOrderHoldItemExecutor;
//    }

//    /**
//     * 1.10.2 自动解挂
//     *
//     * @return 自动解挂
//     */
//    public static ThreadPoolExecutor getAutoReleaseHangPool() {
//        return autoReleaseHangPool == null
//                ? (autoReleaseHangPool = OMSThreadPoolBuilder.build(TASK_AUTO_RELEASE_HANG_POOL_NAME))
//                : autoReleaseHangPool;
//    }

//    /**
//     * 奶卡冲抵单汇总 查询：milk_card_amount_offset_order
//     */
//    private static ThreadPoolExecutor milkCardAmountOffsetOrder;
//    private static final String R3_OMS_MILK_CARD_AMOUNT_OFFSET_ORDER_POOL_NAME = "R3_OMS_MILK_CARD_AMOUNT_OFFSET_ORDER_%d";
//
//    /**
//     * @return 奶卡冲抵单汇总
//     */
//    public static ThreadPoolExecutor getMilkCardAmountOffsetOrder() {
//        return milkCardAmountOffsetOrder == null
//                ? (milkCardAmountOffsetOrder = OMSThreadPoolBuilder.build(R3_OMS_MILK_CARD_AMOUNT_OFFSET_ORDER_POOL_NAME))
//                : milkCardAmountOffsetOrder;
//    }
//    /**
//     * 获取发票申请表 查询：AC_F_INVOICE_APPLY
//     */
//    private static ThreadPoolExecutor acFInvoiceApply;
//    private static final String R3_OMS_AC_F_INVOICE_APPLY_NAME = "R3_OMS_AC_F_INVOICE_APPLY_%d";
//
//    public static ThreadPoolExecutor getAcFInvoiceApply() {
//        return acFInvoiceApply == null
//                ? (acFInvoiceApply = OMSThreadPoolBuilder.build(R3_OMS_AC_F_INVOICE_APPLY_NAME))
//                : acFInvoiceApply;
//    }

//    /**
//     * 获取发票开票表 查询：AC_F_ORDER_INVOICE
//     */
//    private static ThreadPoolExecutor acfOrderInvoice;
//    private static final String R3_OMS_AC_F_ORDER_INVOICE_NAME = "R3_OMS_AC_F_ORDER_INVOICE_%d";
//
//    public static ThreadPoolExecutor getAcfOrderInvoice() {
//        return acfOrderInvoice == null
//                ? (acfOrderInvoice = OMSThreadPoolBuilder.build(R3_OMS_AC_F_ORDER_INVOICE_NAME))
//                : acfOrderInvoice;
//    }
//    /**
//     * 出入库插入sap销售记录任务表 查询：oc_b_sap_sales_data_record_add_task
//     */
//    private static ThreadPoolExecutor sapSalesDataRecordAdd;
//    private static final String R3_OMS_SAP_SALES_DATA_RECORD_ADD_POOL_NAME = "R3_OMS_SAP_SALES_DATA_RECORD_ADD_%d";
//
//    /**
//     * @return 出入库插入sap销售记录任务表
//     */
//    public static ThreadPoolExecutor getSapSalesDataRecordAddPool() {
//        return sapSalesDataRecordAdd == null
//                ? (sapSalesDataRecordAdd = OMSThreadPoolBuilder.build(R3_OMS_SAP_SALES_DATA_RECORD_ADD_POOL_NAME))
//                : sapSalesDataRecordAdd;
//    }
}
