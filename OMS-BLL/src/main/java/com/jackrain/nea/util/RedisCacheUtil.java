package com.jackrain.nea.util;

import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.redis.core.BoundHashOperations;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2021/1/6
 */
public class RedisCacheUtil {

    /**
     * 删除redis.指定删除
     *
     * @param id
     * @param prefix
     */
    public static void delete(Long id, String prefix) {
        if (id == null) {
            return;
        }
        String redisKey = prefix + id;
        if (RedisOpsUtil.getObjRedisTemplate().hasKey(redisKey)) {
            RedisOpsUtil.getObjRedisTemplate().delete(redisKey);
        }
    }

    /**
     * 删除redis.指定删除
     *
     * @param id
     * @param prefix
     */
    public static void deleteStr(Long id, String prefix) {
        if (id == null) {
            return;
        }
        String redisKey = prefix + id;
        if (RedisOpsUtil.getStrRedisTemplate().hasKey(redisKey)) {
            RedisOpsUtil.getStrRedisTemplate().delete(redisKey);
        }
    }

    /**
     * 删除redis.批量
     *
     * @param idList
     * @param prefix
     */
    public static void deleteStr(List<Long> idList, String prefix) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<String> redisKeyList = idList.stream().map(p -> prefix + p).collect(Collectors.toList());

        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        redisTemplate.delete(redisKeyList);
    }

    /**
     * hash
     *
     * @param redisKey
     * @param hashKey
     */
    public static void hashDelete(String redisKey, String hashKey) {
        if (hashKey == null) {
            return;
        }
        BoundHashOperations<String, String, Object> hashOperations = ops(redisKey);
        if (hashOperations.hasKey(hashKey)) {
            hashOperations.delete(hashKey);
        }
    }

    /**
     * @param ops
     * @param hashKeys
     * @param v
     */
    public static void batchAdd(BoundHashOperations<String, String, Long> ops, List<String> hashKeys, Long v) {
        for (String hashKey : hashKeys) {
            if (hashKey == null) {
                continue;
            }
            ops.put(hashKey, v);
        }
    }

    /**
     * @param ops
     * @param hashKeys
     */
    public static void batchHashDel(BoundHashOperations<String, String, Long> ops, List<String> hashKeys) {
        for (String hashKey : hashKeys) {
            if (hashKey == null) {
                continue;
            }
            if (ops.hasKey(hashKey)) {
                ops.delete(hashKey);
            }
        }
    }

    /**
     * @param redisKey
     * @param hashKeys
     */
    public static void batchHashDelete(String redisKey, List<String> hashKeys) {
        if (CollectionUtils.isEmpty(hashKeys)) {
            return;
        }
        BoundHashOperations<String, String, Object> hashOperations = ops(redisKey);
        for (String hashKey : hashKeys) {
            if (hashKey == null) {
                continue;
            }
            if (hashOperations.hasKey(hashKey)) {
                hashOperations.delete(hashKey);
            }
        }
    }

    public static <K, V> BoundHashOperations<K, K, V> ops(K k) {
        CusRedisTemplate<K, V> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        BoundHashOperations<K, K, V> ops = objRedisTemplate.boundHashOps(k);
        return ops;
    }


}
