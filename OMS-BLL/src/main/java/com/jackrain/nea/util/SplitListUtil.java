package com.jackrain.nea.util;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR> ruan.gz
 * @Description :
 * @Date : 2020/6/19
 **/
public class SplitListUtil {

    public static <T> List<List<T>> splitList(List<T> collection, int splitSize) {
        if (CollectionUtils.isEmpty(collection)) {
            return null;
        }
        int maxSize = collection.size() / splitSize + 1;
        return Stream.iterate(0, f -> f + 1)
                .limit(maxSize)
                .parallel()
                .map(a -> collection.parallelStream().skip(a * splitSize).limit(splitSize).collect(Collectors.toList()))
                .filter(b -> !b.isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 分割LSIT
     *
     * @param list
     * @param size
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> partition(List<T> list, int size) {
        if (CollectionUtils.isEmpty(list) || size <= 0) {
            return null;
        }
        if (size <= 0) {
            return Lists.partition(list, list.size() + 1);
        }
        return Lists.partition(list, size);
    }
}
