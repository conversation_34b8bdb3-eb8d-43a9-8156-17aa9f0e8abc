package com.jackrain.nea.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultItemMqResult;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultSendMsgResult;
import com.jackrain.nea.cpext.model.UsersDO;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.jackrain.nea.oc.oms.model.constant.OcCommonConstant.AUDIT_SEND_DINGTALK;
import static com.jackrain.nea.oc.oms.model.constant.OcCommonConstant.DELIVERY_SEND_DINGTALK;

/**
 * @ClassName SendToBOrderMessage
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/7/4 13:59
 * @Version 1.0
 */
@Component
@Slf4j
public class SendToBOrderMessageUtil {

    public static final String DELIVERY = "已出库";
    public static final String AUDIT = "已审核";
    public static final String PLACE_ORDER = "已下单";
    private static final Integer TWENTY = 20;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    public static void main(String[] args) {
        StringBuilder sb = new StringBuilder();
        sb.append("## 您有一条订单 平台单号:");
        sb.append("  \n  ");
        sb.append("我是平台单号");
        sb.append(" 单据编号:");
        sb.append("我是单据编号");
        sb.append("  \n  ");
        sb.append(" ");
        sb.append("## 已出库");
        sb.append(",请知悉;");
        sb.append("  \n  ");
        sb.append("客户名称:");
        sb.append("  \n  ");
        sb.append("## 我是测试店铺");
        String token = DingTalkRobotUtil.getToken(DingTalkRobotUtil.OMS_ROBOT_APPKEY, DingTalkRobotUtil.OMS_ROBOT_APPSECRET);
        String id = DingTalkRobotUtil.userGetByMobile("***********", token);
        List<String> userIds = new ArrayList<>();
        if (StringUtils.isNotBlank(id)) {
            userIds.add(id);
        }
        JSONObject msgParam = new JSONObject();
        msgParam.put("title", "123");
        msgParam.put("text", sb.toString());

        DingTalkRobotUtil.batchSend(DingTalkRobotUtil.OMS_ROBOT_CODE, userIds, "sampleMarkdown", JSONUtil.toJsonStr(msgParam), token);
    }

    @Async("commonTaskExecutor")
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 2000L, multiplier = 3))
    public void checkAndSendDingding(OcBOrder ocBOrder, String message, List<SgOutResultSendMsgResult> mqOutOrderList, Integer type) {
        // 是否需要发送钉钉消息
        boolean sendDingTalk = ObjectUtil.equal(type, AUDIT_SEND_DINGTALK) && (OrderBusinessTypeCodeEnum.SAP_CONSIGN_SALE.getCode().equals(ocBOrder.getBusinessTypeCode()) ||
                OrderBusinessTypeCodeEnum.SAP_STANDARD_SALE.getCode().equals(ocBOrder.getBusinessTypeCode()));

        if (ObjectUtil.equal(type, DELIVERY_SEND_DINGTALK) && (OrderBusinessTypeCodeEnum.SAP_CONSIGN_SALE.getCode().equals(ocBOrder.getBusinessTypeCode()) ||
                OrderBusinessTypeCodeEnum.SAP_STANDARD_SALE.getCode().equals(ocBOrder.getBusinessTypeCode()) ||
                OrderBusinessTypeCodeEnum.SAP_FREE.getCode().equals(ocBOrder.getBusinessTypeCode()))) {
            sendDingTalk = true;
        }

        if (sendDingTalk) {
            String token = DingTalkRobotUtil.getToken(DingTalkRobotUtil.OMS_ROBOT_APPKEY, DingTalkRobotUtil.OMS_ROBOT_APPSECRET);
            String userNick = ocBOrder.getUserNick();
            if (StringUtils.isEmpty(userNick)) {
                return;
            }
            // 根据员工姓名 获取员工手机号
            ValueHolder valueHolder = cpRpcService.getByEName(userNick);
            if (valueHolder.isOK()) {
                Map<String, Object> map = (Map<String, Object>) valueHolder.getData().get("data");
                if (map.get("MOBIL") == null){
                    return;
                }
                String mobile = String.valueOf(map.get("MOBIL"));
                JSONObject msgParam = new JSONObject();
                msgParam.put("title", message);

                StringBuilder sb = new StringBuilder();
                sb.append("您有一条订单 平台单号:");
                sb.append(ocBOrder.getTid());
                sb.append(" 单据编号:");
                sb.append(ocBOrder.getBillNo());
                sb.append(" ");
                sb.append(message);
                sb.append(",请知悉;");
                sb.append("  \n  ");
                sb.append("客户名称:");
                sb.append(ocBOrder.getCpCShopTitle());
                sb.append("  \n  ");

                // 构建text信息。 审核后或者发货后的商品信息进行拼接
                if (DELIVERY.equals(message)) {
                    SgOutResultSendMsgResult sendMsgResult = mqOutOrderList.get(0);
                    List<SgOutResultItemMqResult> itemMqResults = sendMsgResult.getMqResultItem();
                    String vehicleNo = sendMsgResult.getMqResult().getVehicleNo();
                    // 如果是出库的话 查询逻辑出库单的商品信息。 商品名称、数量、效期
                    sb.append("出库仓:");
                    sb.append(ocBOrder.getCpCPhyWarehouseEname());
                    sb.append("  \n  ");
                    sb.append("物流承运商:");
                    sb.append(itemMqResults.get(0).getCpCLogisticsEname());
                    sb.append("  \n  ");

                    /*CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.getByCpcPhyWhsId(ocBOrder.getCpCPhyWarehouseId());*/
                    Integer days = stRpcService.queryArrivalDays(ocBOrder.getCpCLogisticsId(), ocBOrder.getCpCPhyWarehouseId(), ocBOrder.getCpCRegionProvinceId());
                    if (Objects.nonNull(days)) {
                        sb.append("预计到货时间:")
                                .append(DateUtil.format(DateUtils.addDays(new Date(), days), "yyyy年MM月dd日"))
                                .append("  \n  ");
                    }

                    if (StringUtils.isNotBlank(vehicleNo)) {
                        sb.append("车牌号:")
                                .append(vehicleNo)
                                .append("  \n  ");
                    }

                    if (CollectionUtils.isNotEmpty(itemMqResults) && itemMqResults.size() <= TWENTY) {
                        for (SgOutResultItemMqResult itemMqResult : itemMqResults) {
                            sb.append("  \n  ");
                            sb.append("商品名称:");
                            sb.append(itemMqResult.getPsCProEname());
                            sb.append("  \n  ");
                            sb.append("数量:");
                            sb.append(itemMqResult.getQty());
                            sb.append("  \n  ");
                            sb.append("生产日期:");
                            sb.append(itemMqResult.getProduceDate());
                            sb.append("  \n  ");
                        }
                    }

                } else if (AUDIT.equals(message)) {
                    sb.append("审核仓:");
                    sb.append(ocBOrder.getCpCPhyWarehouseEname());
                    sb.append("  \n  ");

                    // 找到零售发货单对应的
                    List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItems(ocBOrder.getId());
                    if (CollectionUtils.isNotEmpty(ocBOrderItemList) && ocBOrderItemList.size() <= TWENTY) {
                        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                            sb.append("  \n  ");
                            sb.append("商品名称:");
                            sb.append(ocBOrderItem.getPsCProEname());
                            sb.append("  \n  ");
                            sb.append("数量:");
                            sb.append(ocBOrderItem.getQty());
                            sb.append("  \n  ");
                            sb.append("生产日期:");
                            sb.append(ocBOrderItem.getExpiryDateRange());
                            sb.append("  \n  ");
                        }
                    }

                }


                msgParam.put("text", sb.toString());

                // 如果手机号为空  给雷宏宇发钉钉消息 让他去维护
                if (org.apache.commons.lang3.StringUtils.isEmpty(mobile) || org.apache.commons.lang3.StringUtils.equals("null", mobile)) {
                    String id = DingTalkRobotUtil.userGetByMobile("18814505598", token);
                    List<String> userIds = new ArrayList<>();
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(id)) {
                        userIds.add(id);
                    }
                    msgParam.put("title", "用户档案手机号未维护");
                    msgParam.put("text", "员工:" + userNick + ",用户档案未维护手机号 请及时维护");
                    DingTalkRobotUtil.batchSend(DingTalkRobotUtil.OMS_ROBOT_CODE, userIds, "sampleMarkdown", JSONUtil.toJsonStr(msgParam), token);
                    return;
                }
                String id = DingTalkRobotUtil.userGetByMobile(mobile, token);
                List<String> userIds = new ArrayList<>();
                if (StringUtils.isNotBlank(id)) {
                    userIds.add(id);
                } else {
                    log.warn("获取钉钉用户id失败 手机号:{}", mobile);
                    return;
                }
                DingTalkRobotUtil.batchSend(DingTalkRobotUtil.OMS_ROBOT_CODE, userIds, "sampleMarkdown", JSONUtil.toJsonStr(msgParam), token);
            } else {
                log.error("获取员工信息失败 员工姓名:{}", userNick);
                throw new NDSException("获取员工信息失败");
            }
        }
    }

    @Async("commonTaskExecutor")
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 2000L, multiplier = 3))
    public void sendMsgByPlaceOrder(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList) {
        log.info(LogUtil.format("SendToBOrderMessageUtil.sendMsgByPlaceOrder order:{}",
                "SendToBOrderMessageUtil.sendMsgByPlaceOrder"), JSONObject.toJSONString(ocBOrder));
        Set<String> mobiles = new HashSet<>();
        //获取新零售部门手机号
        getRetailDepartUserMobile(ocBOrder, orderItemList, mobiles);
        List<String> userCodes = new ArrayList<>();
        //获取衍生品部门人员工号
        getDerivativesDepartUserCodes(orderItemList, userCodes);
        log.info("getDerivativesDepartUserCodes userCodes:{}", userCodes);
        //获取奶粉部门人员工号
        getMilkPowderDepartUserCodes(orderItemList, userCodes);
        log.info("getMilkPowderDepartUserCodes userCodes:{}", userCodes);
        //根据用户工号查询用户信息
        List<UsersDO> usersDOList = cpRpcService.queryUserByNames(userCodes);
        if (CollectionUtils.isNotEmpty(usersDOList)) {
            Set<String> userMobiles = usersDOList.stream()
                    .map(UsersDO::getMobil).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(userMobiles)) {
                mobiles.addAll(userMobiles);
            }
        }
        //根据手机号发送已下单格式信息
        sendMsgByMobileByPlaceOrder(ocBOrder, orderItemList, mobiles);
    }

    /**
     * 获取衍生品部门用户工号
     *
     * @param orderItemList
     * @param userCodes
     */
    private void getMilkPowderDepartUserCodes(List<OcBOrderItem> orderItemList, List<String> userCodes) {
        List<String> firstLevelList = businessSystemParamService.getFirstLevelClassificationOfMilkPowder();
        if (CollectionUtils.isEmpty(firstLevelList)) {
            return;
        }
        List<Long> prodimItemIds = new ArrayList<>();
        List<PsCProdimItem> prodimItemList = psRpcService.queryProdimItemByNames(firstLevelList);
        if (CollectionUtils.isNotEmpty(prodimItemList)) {
            prodimItemIds = prodimItemList.stream().map(PsCProdimItem::getId).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(prodimItemIds)) {
            return;
        }
        List<Long> finalProdimItemIds = prodimItemIds;
        List<OcBOrderItem> itemList = orderItemList.stream()
                .filter(s -> finalProdimItemIds.contains(s.getMDim4Id())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemList)) {
            List<String> codes = businessSystemParamService.getFirstLevelClassificationOfMilkPowderUserCodes();
            if (CollectionUtils.isNotEmpty(codes)) {
                userCodes.addAll(codes);
            }
        }
    }

    /**
     * 获取衍生品部门用户工号
     *
     * @param orderItemList
     * @param userCodes
     */
    private void getDerivativesDepartUserCodes(List<OcBOrderItem> orderItemList, List<String> userCodes) {
        List<String> firstLevelList = businessSystemParamService.getFirstLevelClassificationOfDerivatives();
        BigDecimal maxQty = businessSystemParamService.getFirstLevelClassificationOfDerivativesMaxQty();
        if (CollectionUtils.isEmpty(firstLevelList) || maxQty == null) {
            return;
        }
        List<Long> prodimItemIds = new ArrayList<>();
        List<PsCProdimItem> prodimItemList = psRpcService.queryProdimItemByNames(firstLevelList);
        if (CollectionUtils.isNotEmpty(prodimItemList)) {
            prodimItemIds = prodimItemList.stream().map(PsCProdimItem::getId).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(prodimItemIds)) {
            return;
        }
        List<Long> finalProdimItemIds = prodimItemIds;
        BigDecimal totalQty = orderItemList.stream().filter(s -> finalProdimItemIds.contains(s.getMDim4Id()))
                .map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalQty.compareTo(maxQty) >= 0) {
            List<String> codes = businessSystemParamService.getFirstLevelClassificationOfDerivativesUserCodes();
            if (CollectionUtils.isNotEmpty(codes)) {
                userCodes.addAll(codes);
            }
        }
    }

    /**
     * 获取新零售部门手机号
     *
     * @param ocBOrder
     * @param orderItemList
     * @param mobiles
     */
    private void getRetailDepartUserMobile(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList, Set<String> mobiles) {
        List<String> firstLevelList = businessSystemParamService.getFirstLevelClassificationOfLiquidMilk();
        if (CollectionUtils.isEmpty(firstLevelList)) {
            return;
        }
        List<Long> prodimItemIds = new ArrayList<>();
        List<PsCProdimItem> prodimItemList = psRpcService.queryProdimItemByNames(firstLevelList);
        if (CollectionUtils.isNotEmpty(prodimItemList)) {
            prodimItemIds = prodimItemList.stream().map(PsCProdimItem::getId).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(prodimItemIds)) {
            return;
        }
        List<Long> finalProdimItemIds = prodimItemIds;
        List<OcBOrderItem> itemList = orderItemList.stream()
                .filter(s -> finalProdimItemIds.contains(s.getMDim4Id())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        List<UsersDO> usersDOList = cpRpcService.queryUserListByGroupCode(ocBOrder.getSalesGroupCode());
        log.info(LogUtil.format("SendToBOrderMessageUtil.getRetailDepartUserMobile saleGroupCode:{},usersDOList:{}",
                "SendToBOrderMessageUtil.getRetailDepartUserMobile"),
                ocBOrder.getSalesGroupCode(), JSONObject.toJSONString(usersDOList));
        if (CollectionUtils.isEmpty(usersDOList)) {
            return;
        }
        Set<String> userMobiles = usersDOList.stream()
                .filter(s -> StringUtils.isNotEmpty(s.getEname()) && !s.getEname().equals(ocBOrder.getUserNick()))
                .map(UsersDO::getMobil).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(userMobiles)) {
            mobiles.addAll(userMobiles);
        }
    }

    /**
     * 按照已下单的格式发送钉钉消息
     *
     * @param ocBOrder      订单信息
     * @param orderItemList 订单明细信息
     * @param mobiles       手机号集合
     */
    private void sendMsgByMobileByPlaceOrder(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList, Set<String> mobiles) {
        if (CollectionUtils.isEmpty(mobiles)) {
            return;
        }
        String token = DingTalkRobotUtil.getToken(DingTalkRobotUtil.OMS_ROBOT_APPKEY, DingTalkRobotUtil.OMS_ROBOT_APPSECRET);
        List<String> userIds = new ArrayList<>();
        for (String mobile : mobiles) {
            String id = DingTalkRobotUtil.userGetByMobile(mobile, token);
            if (StringUtils.isNotBlank(id)) {
                userIds.add(id);
            } else {
                log.warn("获取钉钉用户id失败 手机号:{}", mobile);
            }
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        JSONObject msgParam = buildMsgTextByPlaceOrder(ocBOrder, orderItemList);
        DingTalkRobotUtil.batchSend(DingTalkRobotUtil.OMS_ROBOT_CODE,
                userIds, "sampleMarkdown", JSONUtil.toJsonStr(msgParam), token);
    }

    /**
     * 构建发送文案（已下单）
     *
     * @param ocBOrder      订单信息
     * @param orderItemList 订单明细信息
     * @return
     */
    private JSONObject buildMsgTextByPlaceOrder(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList) {
        JSONObject msgParam = new JSONObject();
        msgParam.put("title", PLACE_ORDER);

        StringBuilder sb = new StringBuilder();
        sb.append("您有一条订单 平台单号:");
        sb.append(ocBOrder.getTid());
        sb.append(" 单据编号:");
        sb.append(ocBOrder.getBillNo());
        sb.append(" ");
        sb.append(PLACE_ORDER);
        sb.append(",请知悉;");
        sb.append("  \n  ");
        sb.append("客户名称:");
        sb.append(StringUtils.isEmpty(ocBOrder.getCpCShopTitle()) ? "" : ocBOrder.getCpCShopTitle());
        sb.append("  \n  ");
        sb.append("出库仓:");
        sb.append(StringUtils.isEmpty(ocBOrder.getCpCPhyWarehouseEname()) ? "" : ocBOrder.getCpCPhyWarehouseEname());
        sb.append("  \n  ");
        if (CollectionUtils.isNotEmpty(orderItemList) && orderItemList.size() <= TWENTY) {
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                if (SkuType.NO_SPLIT_COMBINE == ocBOrderItem.getProType()) {
                    continue;
                }
                sb.append("  \n  ");
                sb.append("商品名称:");
                sb.append(ocBOrderItem.getPsCProEname());
                sb.append("  \n  ");
                sb.append("数量:");
                sb.append(ocBOrderItem.getQty());
                sb.append("  \n  ");
                sb.append("生产日期:");
                sb.append(StringUtils.isEmpty(ocBOrderItem.getExpiryDateRange()) ? "" : ocBOrderItem.getExpiryDateRange());
                sb.append("  \n  ");
            }
        }
        sb.append("物流承运商:");
        sb.append(StringUtils.isEmpty(ocBOrder.getCpCLogisticsEname()) ? "" : ocBOrder.getCpCLogisticsEname());
        sb.append("  \n  ");
        msgParam.put("text", sb.toString());
        return msgParam;
    }
}
