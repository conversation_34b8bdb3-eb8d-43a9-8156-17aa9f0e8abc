package com.jackrain.nea.util;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version :1.0
 * description ：金额计算
 * @date :2019/7/05 15:00
 */
public class AmountCalcUtils {


    /**
     * 乘法
     *
     * @param dec1         数字1
     * @param dec2         数字2
     * @param scale        位数
     * @param roundingMode 四舍五入规则
     * @return dec1 * dec2
     */
    public static BigDecimal multiplyBigDecimal(final BigDecimal dec1, final BigDecimal dec2,
                                                final int scale, int roundingMode) {
        return (dec1 != null ? dec1 : BigDecimal.ZERO)
                .multiply(dec2 != null ? dec2 : BigDecimal.ZERO).setScale(scale, roundingMode);
    }

    /**
     * 乘法(default roundingMode = BigDecimal.ROUND_HALF_UP)
     *
     * @param dec1  数字1
     * @param dec2  数字2
     * @param scale 位数
     * @return dec1 * dec2
     */
    public static BigDecimal multiplyBigDecimal(final BigDecimal dec1, final BigDecimal dec2,
                                                final int scale) {
        return multiplyBigDecimal(dec1, dec2, scale, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 除法
     *
     * @param dec1         数字1
     * @param dec2         数字2
     * @param scale        位数
     * @param roundingMode 四舍五入规则
     * @return dec1 / dec2
     */
    public static BigDecimal divideBigDecimal(final BigDecimal dec1, final BigDecimal dec2,
                                              final int scale, final int roundingMode) {
        if (dec2 == null || BigDecimal.ZERO.compareTo(dec2) == 0) {
            return BigDecimal.ZERO;
        }
        return (dec1 != null ? dec1 : BigDecimal.ZERO).divide(dec2, scale, roundingMode);
    }

    /**
     * 除法(default roundingMode = BigDecimal.ROUND_HALF_UP)
     *
     * @param dec1  数字1
     * @param dec2  数字2
     * @param scale 位数
     * @return dec1 / dec2
     */
    public static BigDecimal divideBigDecimal(final BigDecimal dec1, final BigDecimal dec2,
                                              final int scale) {
        return divideBigDecimal(dec1, dec2, scale, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 加法
     *
     * @param dec1 数字1
     * @param dec2 数字2
     * @return dec1 + dec2
     */
    public static BigDecimal addBigDecimal(final BigDecimal dec1, final BigDecimal dec2) {
        return (dec1 != null ? dec1 : BigDecimal.ZERO).add(dec2 != null ? dec2 : BigDecimal.ZERO);
    }

    /**
     * 减法
     *
     * @param dec1 数字1
     * @param dec2 数字2
     * @return dec1 - dec2
     */
    public static BigDecimal subtractBigDecimal(final BigDecimal dec1, final BigDecimal dec2) {
        return (dec1 != null ? dec1 : BigDecimal.ZERO)
                .subtract(dec2 != null ? dec2 : BigDecimal.ZERO);
    }

    /**
     * 负数
     *
     * @param dec 数字
     * @return dec
     */
    public static BigDecimal negate(final BigDecimal dec) {
        return (dec != null ? dec.negate() : BigDecimal.ZERO);
    }

    private AmountCalcUtils() {
    }
}
