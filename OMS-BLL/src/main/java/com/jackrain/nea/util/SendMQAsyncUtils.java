package com.jackrain.nea.util;

import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 孙勇生
 * create at:  2019/10/29  09:36
 * @description: 异步发MQ
 */
@Slf4j
@Component
public class SendMQAsyncUtils {
//    @Autowired
//    private R3MqSendHelper r3MqSendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    /**
     * 异步发MQ
     *
     * @param title              使用标题如审核、占单、传wms 方便查日志
     * @param body
     * @param topic
     * @param tag
     * @param msgKey
     * @param delayTime          防疲劳异步间隔时间
     * @param sendMqFallCallBack 失败回调
     * @param callBackIds        回调参数
     */
    @Async
    public void sendDelayMessage(String configName, String title, String body, String topic, String tag, String msgKey
            , Long delayTime, SendMqFallCallBack sendMqFallCallBack, List<Long> callBackIds) {
        try {
//            Thread.sleep(delayTime);
//            String message = r3MqSendHelper.sendMessage(configName, body, topic, tag, msgKey);
            MqSendResult result = defaultProducerSend.sendDelayTopic(topic, tag, body, msgKey, delayTime);

            log.info(LogUtil.format("延时异步发送MQ结果:",title, result));
        } catch (Exception e) {
            if (null != sendMqFallCallBack && CollectionUtils.isNotEmpty(callBackIds)) {
                sendMqFallCallBack.callBackExec(callBackIds);
            }
            log.error(LogUtil.format("延时异步发送MQ异常"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 排除待分配因为异步mq导致
     */
    public void sendDelayMessageToBeConfimed(String configName, String title, String body, String topic, String tag, String msgKey
            , Long delayTime, SendMqFallCallBack sendMqFallCallBack, List<Long> callBackIds) {
        try {
            MqSendResult result = defaultProducerSend.sendDelayTopic(topic, tag, body, msgKey, delayTime);
            log.info("{}延时异步发送MQ结果:{},body:{}", title, result, body);
        } catch (Exception e) {
            if (null != sendMqFallCallBack && CollectionUtils.isNotEmpty(callBackIds)) {
                sendMqFallCallBack.callBackExec(callBackIds);
            }
            log.error("延时异步发送MQ异常!", e);
            throw new NDSException(e);
        }
    }
}