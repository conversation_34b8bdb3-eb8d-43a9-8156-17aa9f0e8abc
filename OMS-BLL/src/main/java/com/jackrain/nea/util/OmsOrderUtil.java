package com.jackrain.nea.util;

import cn.hutool.core.util.ObjectUtil;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName OmsOrderUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/10/10 15:03
 * @Version 1.0
 */
public class OmsOrderUtil {

    /**
     * 判断是否旺店通订单 并且平台下发
     *
     * @param orderInfo
     * @return
     */
    public static boolean wdtPlatformSend(OcBOrder orderInfo) {
        if (ObjectUtil.equal(PlatFormEnum.WANG_DIAN_TONG.getCode(), orderInfo.getPlatform())) {

            // 符合条件的为旺店通平台下发的订单 不能进行拆单或者对等换货等
            // 如果是平台下推的订单 则不能进行拆单
            // 旺店通订单 不允许手动取消平台推送过来的订单(手工、复制、补发除外)
            Integer isCopyOrder = orderInfo.getIsCopyOrder() == null ? 0 : orderInfo.getIsCopyOrder();
            Integer isResetShip = orderInfo.getIsResetShip() == null ? 0 : orderInfo.getIsResetShip();
            Integer isHandleOrder = StringUtils.isEmpty(orderInfo.getOrderSource()) ? 0 : (ObjectUtil.equal("手工新增", orderInfo.getOrderSource()) ? 1 : 0);
            boolean platformSEnd = isCopyOrder == 0 && isResetShip == 0 && isHandleOrder == 0;
            return platformSEnd;
        }
        return false;
    }

    /**
     * 判断是否旺店通订单 并且平台下发 但排除gwSourceGroup为38或39的订单
     *
     * @param orderInfo 订单信息
     * @return true-符合条件（旺店通平台下发且gwSourceGroup不是38或39），false-不符合条件
     */
    public static boolean wdtPlatformSendWithSourceGroup(OcBOrder orderInfo) {
        if (ObjectUtil.equal(PlatFormEnum.WANG_DIAN_TONG.getCode(), orderInfo.getPlatform())) {

            // 符合条件的为旺店通平台下发的订单 不能进行拆单或者对等换货等
            // 如果是平台下推的订单 则不能进行拆单
            // 旺店通订单 不允许手动取消平台推送过来的订单(手工、复制、补发除外)
            Integer isCopyOrder = orderInfo.getIsCopyOrder() == null ? 0 : orderInfo.getIsCopyOrder();
            Integer isResetShip = orderInfo.getIsResetShip() == null ? 0 : orderInfo.getIsResetShip();
            Integer isHandleOrder = StringUtils.isEmpty(orderInfo.getOrderSource()) ? 0 : (ObjectUtil.equal("手工新增", orderInfo.getOrderSource()) ? 1 : 0);
            boolean platformSend = isCopyOrder == 0 && isResetShip == 0 && isHandleOrder == 0;

            // 增加gwSourceGroup校验：如果为"38"或"39"则返回false
            String gwSourceGroup = orderInfo.getGwSourceGroup();
            boolean isSourceGroup38Or39 = gwSourceGroup != null && ("38".equals(gwSourceGroup) || "39".equals(gwSourceGroup));

            // 如果gwSourceGroup是"38"或"39"，则返回false；否则按原逻辑返回
            return platformSend && !isSourceGroup38Or39;
        }
        return false;
    }

    /**
     * 是否toc残次订单
     *
     * @param order
     * @return
     */
    public static boolean isToCCcOrder(OcBOrder order) {
        if (order == null) {
            return false;
        }
        if (!OmsBusinessTypeUtil.isToBOrder(order) && StringUtils.isNotBlank(order.getSaleProductAttr())) {
            return true;
        }
        return false;
    }
}
