package com.jackrain.nea.util;

import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @Author: 黄世新
 * @Date: 2022/8/5 下午1:07
 * @Version 1.0
 */
public class ComputeEqualExchangeQtyUtil {

    public static BigDecimal computeQty(OcBOrderItem ocBOrderItem){
        BigDecimal qty = ocBOrderItem.getQty();
        if (qty == null){
            return BigDecimal.ZERO;
        }
        String equalExchangeRatio = ocBOrderItem.getEqualExchangeRatio();
        if (StringUtils.isNotEmpty(equalExchangeRatio)){
            String[] qtyArray = equalExchangeRatio.split(":");
            BigDecimal a = new BigDecimal(qtyArray[0]);
            BigDecimal b = new BigDecimal(qtyArray[1]);
            //对等换货只能换1  所以肯定能除尽
            BigDecimal divide = a.divide(b, 4, BigDecimal.ROUND_HALF_UP);
            //还原出原数量  这样做的目的是怕拆单
            return qty.multiply(divide);
        }
        return qty;
    }
}
