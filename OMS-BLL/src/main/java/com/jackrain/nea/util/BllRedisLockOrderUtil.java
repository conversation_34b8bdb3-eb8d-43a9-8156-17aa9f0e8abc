package com.jackrain.nea.util;

import org.springframework.stereotype.Component;

/**
 * Redis 锁定订单工具类
 *
 * @author: heliu
 * @since: 2019-03-22
 * create at : 2019-01-28 09:13
 */
@Component
public class BllRedisLockOrderUtil {

    /**
     * 锁定订单的KeyValue值。默认为1
     */
    private static final String LOCKED_ORDER_VALUE = "1";

    /**
     * 默认1分钟
     */
    private static final int LOCK_ORDER_AUTO_TIMEOUT = 0;

    private static final int LOCK_ORDER_AUTO_TIMEOUT2 = 3000;

    /**
     * 默认1分钟
     */
    public static final int LOCK_ORDER_ONE_SECOND = 1000;

    /**
     * 默认锁定消息
     */
    private static final String DEFAULT_LOCK_ORDER_MESSAGE = "SUCCESS";

    /**
     * 获取单据锁定超时时间
     *
     * @return 单据锁定超时时间
     */
    public long getLockOrderTimeOut() {
        return LOCK_ORDER_AUTO_TIMEOUT;
    }

    /**
     * 获取单据锁定超时时间
     *
     * @return 单据锁定超时时间
     */
    public long getLockOrderTimeOut2() {
        return LOCK_ORDER_AUTO_TIMEOUT2;
    }
}
