package com.jackrain.nea.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.function.IFunction;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/10
 */
@Slf4j
public class OmsReturnInStockSupply {

    private static final ThreadLocal<LogInfo> logMessageRecorder = ThreadLocal.withInitial(() -> new LogInfo());

    private static final ThreadLocal<List<RedisReentrantLock>> locks = ThreadLocal.withInitial(() -> new ArrayList<>());

    public static IFunction printLogFunction = () -> {
        if (log.isDebugEnabled()) {
            log.debug(JSON.toJSONString(logMessageRecorder.get().message));
        }
    };

    public static Consumer<String> logs = var -> {
        LogInfo info = logMessageRecorder.get();
        long prevTime = info.pointTime;
        info.pointTime = System.currentTimeMillis();
        info.message.add(info.pointTime - prevTime + "ms-" + var);
    };

    public static Consumer<String> logsWithoutTime = var -> {
        LogInfo info = logMessageRecorder.get();
        info.pointTime = System.currentTimeMillis();
        info.message.add(var);
    };

    public static BiFunction<Long, String, JSONObject> buildWebTipFun =
            (var, msg) -> {
                JSONObject jsn = new JSONObject();
                jsn.put("objid", var);
                jsn.put("message", msg);
                return jsn;
            };

    public static Function<Exception, String> dealExpMessageFunction = e -> {
        if (e == null) {
            return "Null Exception";
        }
        String message = e.getMessage();
        if (message == null) {
            return "Null Exception Message";
        }
        return message.length() > 100 ? message.substring(0, 100) : message;
    };

    public static Function<String, Boolean> lockBill =
            var -> {
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(var);
                try {
                    if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                        locks.get().add(redisLock);
                        logs.accept("lock:" + var);
                        return true;
                    }
                    logs.accept(var + ":锁单失败");
                } catch (Exception ex) {
                    logs.accept(var + ":锁单异常");
                    log.error("lockBill.exp.id:{}, {}", var, Throwables.getStackTraceAsString(ex));
                }
                return false;
            };

    public static IFunction unlockBill =
            () -> {
                for (RedisReentrantLock lock : locks.get()) {
                    try {
                        String lockId = lock.getLockId();
                        lock.unlock();
                        logs.accept("unlock:" + lockId);
                    } catch (Exception ex) {
                        logs.accept(lock.getLockId() + ":解锁异常");
                        log.error("unlockBill.exp.id:{}, {}", lock.getLockId(), Throwables.getStackTraceAsString(ex));
                    }
                }
            };

    public static IFunction releaseFunction = () -> {
        locks.remove();
        logMessageRecorder.remove();
    };

    /**
     * 信息记录
     */
    static class LogInfo {

        /**
         * 时刻
         */
        private long pointTime;

        /**
         * 详情
         */
        private List<String> message;

        LogInfo() {
            message = new ArrayList<>();
            pointTime = System.currentTimeMillis();
        }

    }

}
