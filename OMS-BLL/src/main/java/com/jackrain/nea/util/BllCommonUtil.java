package com.jackrain.nea.util;

import com.burgeon.r3.R3NetworkUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.SocketException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @author: 易邵峰
 * @since: 2019-03-21
 * create at : 2019-03-21 12:18
 */
@Slf4j
public class BllCommonUtil {

    private static String localIpAddress = "";

    private static BllCommonUtil instance;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    private BllCommonUtil() {

    }

    public static BllCommonUtil getInstance() {
        if (instance == null) {
            synchronized (BllCommonUtil.class) {
                instance = new BllCommonUtil();
            }
        }
        return instance;
    }

    public static String getLocalIpAddress() {
        if (StringUtils.isEmpty(localIpAddress)) {
            try {
                localIpAddress = R3NetworkUtil.getLocalIpAddress();
            } catch (SocketException ex) {
                log.error(LogUtil.format("getLocalIpAddress Error"), Throwables.getStackTraceAsString(ex));
            }
        }
        return localIpAddress;
    }

    /**
     * list分页
     *
     * @param sourceList
     * @param pageSize
     * @return
     */
    public static <T> List<List<T>> getBasePageList(List<T> sourceList, int pageSize) {

        List<List<T>> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(sourceList)) {
            return resultList;
        }
        int listSize = sourceList.size();
        int page = listSize / pageSize;
        if (listSize % pageSize != 0) {
            page++;
        }
        for (int i = 0; i < page; i++) {
            List<T> pageList = sourceList.subList(i * pageSize,
                    (((i + 1) * pageSize > listSize ? listSize : pageSize * (i + 1))));
            resultList.add(pageList);
        }
        return resultList;
    }




    /**
     * 返回微妙
     *
     * @return
     */
    public static Long getmicTime() {

        Long cutime = System.currentTimeMillis() * 1000; // 微秒
        Long nanoTime = System.nanoTime(); // 纳秒
        return cutime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
    }


    /**
     * 系统开关默认都是开
     *
     * @param namespace
     * @param key
     * @return
     */
    public static boolean isOpen(String namespace, String key) {
        if (null == namespace) {
            namespace = "application";
        }
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        return config.getPropertyBoolean(key);
    }

    /**
     * 根据订单id列表，批量锁定订单
     *
     * @param orderIdList
     * @return resultMap【锁单失败和成功列表】
     */
    public Map<String, List<Long>> batchLockOrderList(List<Long> orderIdList) {
        Map<String, List<Long>> resultMap = new HashMap<>();
        List<Long> successList = new ArrayList<>();
        List<Long> failList = new ArrayList<>();
        for (Long orderId : orderIdList) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    successList.add(orderId);
                } else {
                    failList.add(orderId);
                }
            } catch (Exception e) {
                failList.add(orderId);
                log.error(LogUtil.format("订单加锁失败",orderId), Throwables.getStackTraceAsString(e));

            }
        }
        resultMap.put("successList", successList);
        resultMap.put("failList", failList);
        return resultMap;
    }

    /**
     * 根据订单id列表，批量解锁订单
     *
     * @param orderIdList
     * @return
     */
    public void batchUnLockOrderList(List<Long> orderIdList) {
        for (Long orderId : orderIdList) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            redisLock.unlock();
        }
    }

}
