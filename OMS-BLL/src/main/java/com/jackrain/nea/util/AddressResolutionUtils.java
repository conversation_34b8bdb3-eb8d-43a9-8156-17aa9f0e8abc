package com.jackrain.nea.util;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCRegionAlias;
import com.jackrain.nea.ip.model.result.AddressAnalyseResp;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 根据详细地址解析 省市区
 *
 * @author: gale.qin
 * @since: 2020/7/1
 * create at : 2020/7/1 19:20
 */
@Component
@Slf4j
public class AddressResolutionUtils {

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private IpRpcService ipRpcService;

    public static String[] ZX_CITYS = new String[]{"北京", "天津", "上海", "重庆"};

    /**
     * 根据详细地址解析省市区数据(必须保证原省市区数据为空)
     *
     * @param address
     * @return
     */
    public Map<String, String> addressResolutionNew(String address) {
        log.info("开始执行详细地址解析 address:{}", address);
        // 先走addressResolution 主要是为了得到 北京、上海、天津、重庆的数据。
        Map<String, String> row = new LinkedHashMap<String, String>();

        // 如果得到的数据为空的 则通过新的规则 获取到区/县 然后查询别名表。再根据别名表中的省、市 匹配下是否
        // "((?<province>[^省]+省|.+自治区|上海|北京|天津|重庆))"
        String regex = "(?<city>[^市]+自治州|.*?红河州|.*?地区|.*?行政单位|市辖区|.*?市|.+盟|.*?县)" +
                "(?<area>[^县]+县|.*?区|.*?市|.*?旗|.*?海域|.*?岛)?" +
                "(?<town>[^区]+区|.+镇)?(?<village>.*)";
        Matcher m = Pattern.compile(regex).matcher(address);
        String city = null, area = null, town = null, village = null;
        while (m.find()) {
            city = m.group("city");
            area = m.group("area");
            ValueHolderV14<List<CpCRegionAlias>> valueHolderV14 = new ValueHolderV14<>();
            try {
                valueHolderV14 = cpRpcService.selectRegionInfo(area, 3);
            } catch (Exception e) {
                log.error(LogUtil.format("AddressResolutionUtils.addressResolutionNew", "订单导入地址解析失败"), Throwables.getStackTraceAsString(e));
            }
            if (CollectionUtils.isNotEmpty(valueHolderV14.getData())) {
                for (CpCRegionAlias cpCRegionAlias : valueHolderV14.getData()) {
                    String aliasProvince = cpCRegionAlias.getCpCRegionProvinceEname();
                    String aliasCity = cpCRegionAlias.getCpCRegionCityEname();
                    String newName = aliasProvince + aliasCity;
                    if (ObjectUtil.equal(newName, city)) {
                        row.put("province", aliasProvince.trim());
                        row.put("city", aliasCity == null ? "" : aliasCity.trim());
                        row.put("area", cpCRegionAlias.getCpCRegionAreaEname() == null ? "" : cpCRegionAlias.getCpCRegionAreaEname().trim());
                        return row;
                    }

                    // 去除掉aliasProvince中的省
                    newName = aliasProvince.replace("省", "") + aliasCity;
                    if (ObjectUtil.equal(newName, city)) {
                        row.put("province", aliasProvince.trim());
                        row.put("city", aliasCity == null ? "" : aliasCity.trim());
                        row.put("area", cpCRegionAlias.getCpCRegionAreaEname() == null ? "" : cpCRegionAlias.getCpCRegionAreaEname().trim());
                        return row;
                    }


                    if (city.startsWith("北京") || city.startsWith("天津") || city.startsWith("上海") || city.startsWith("重庆")) {
                        newName = aliasProvince.replace("市", "") + aliasCity;
                        if (ObjectUtil.equal(newName, city)) {
                            row.put("province", aliasProvince.trim());
                            row.put("city", aliasCity == null ? "" : aliasCity.trim());
                            row.put("area", cpCRegionAlias.getCpCRegionAreaEname() == null ? "" : cpCRegionAlias.getCpCRegionAreaEname().trim());
                            return row;
                        }
                    }
                }
                // 根据市  去别名表里面去查数据
                row.put("city", city == null ? "" : city.trim());
                if (StringUtils.isNotBlank(area) && area.startsWith("市辖区")) {
                    area = area.replace("市辖区", "");
                }
                row.put("area", area == null ? "" : area.trim());
                town = m.group("town");
                row.put("town", town == null ? "" : town.trim());
                village = m.group("village");
                row.put("village", village == null ? "" : village.trim());
            }
        }

        // 如果省市区地址为空 则通过快递100来进行校验
        if (StringUtils.isEmpty(row.get("province")) || StringUtils.isEmpty(row.get("city"))){
            ValueHolderV14<AddressAnalyseResp> valueHolderV14 = ipRpcService.addressAnalyse(address);
            if (!valueHolderV14.isOK()){
                return row;
            }
            AddressAnalyseResp addressAnalyseResp = valueHolderV14.getData();
            AddressAnalyseResp.Xzq xzq = addressAnalyseResp.getData().get(0).getXzq();
            if (xzq != null){
                // 根据市的名称 来查省
                ValueHolderV14<List<CpCRegionAlias>> areaValue = cpRpcService.selectRegionInfo(xzq.getCity(), 2);
                if (CollectionUtils.isEmpty(areaValue.getData())) {
                    row.put("province", "");
                    row.put("city", "");
                    row.put("area", "");
                    return row;
                }
                for (CpCRegionAlias cpCRegionAlias : areaValue.getData()) {
                    // 快递100解析出来了 但是没有填二级地址 直接匹配一下一级地址
                    if (cpCRegionAlias.getCpCRegionProvinceEname().contains(xzq.getProvince())) {
                        row.put("province", cpCRegionAlias.getCpCRegionProvinceEname());
                        row.put("city", cpCRegionAlias.getCpCRegionCityEname());
                        row.put("area", xzq.getDistrict() == null ? "其他区" : xzq.getDistrict());
                        return row;
                    }
                }
            }
        }
        return row;

    }


    /**
     * 解析地址
     *
     * @param address
     * @return
     * <AUTHOR>
     */
    public static Map<String, String> addressResolution(String address) {

        // "((?<province>[^省]+省|.+自治区|上海|北京|天津|重庆))"
        String regex = "(?<province>[^省]+自治区|.*?省|.*?行政区|上海|北京|天津|重庆)" +
                "(?<city>[^市]+自治州|.*?地区|.*?行政单位|市辖区|.*?市|.+盟|.*?县)" +
                "(?<area>[^县]+县|.*?区|.*?市|.*?旗|.*?海域|.*?岛)?" +
                "(?<town>[^区]+区|.+镇)?(?<village>.*)";
        Matcher m = Pattern.compile(regex).matcher(address);
        String province = null, city = null, area = null, town = null, village = null;
        Map<String, String> row = new LinkedHashMap<String, String>();
        while (m.find()) {
            province = m.group("province");
            row.put("province", province == null ? "" : province.trim());
            city = m.group("city");
            // 处理市辖区
            for (String specialName : ZX_CITYS) {
                if (StringUtils.isNotEmpty(province) && StringUtils.isNotEmpty(city)) {
                    if (province.startsWith(specialName) && !city.startsWith(specialName)) {
                        city = province + "市";
                    }
                }
            }
            row.put("city", city == null ? "" : city.trim());
            area = m.group("area");
            if (StringUtils.isNotBlank(area) && area.startsWith("市辖区")) {
                area = area.replace("市辖区", "");
            }
            row.put("area", area == null ? "" : area.trim());
            town = m.group("town");
            row.put("town", town == null ? "" : town.trim());
            village = m.group("village");
            row.put("village", village == null ? "" : village.trim());
        }
        return row;
    }

}
