package com.jackrain.nea.util;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.SkuType;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @Author: 黄世新
 * @Date: 2022/8/17 下午9:16
 * @Version 1.0
 */
public class OrderUtil {


    public static void handleOrderSku(List<OcBOrderItem> orderItemList, OcBOrder order) {
        List<String> skuAll = new ArrayList<>();
        BigDecimal qtyAll = BigDecimal.ZERO;
        for (OcBOrderItem item : orderItemList) {
            String s = item.getPsCSkuEcode() + "(" + item.getQty().intValue() + ")";
            skuAll.add(s);
            if (!String.valueOf(item.getProType()).equals(String.valueOf(SkuType.NO_SPLIT_COMBINE))) {
                qtyAll = qtyAll.add(item.getQty());
            }
        }
        String join = StringUtils.join(skuAll, ",");
        if (join.length() > 500) {
            join = join.substring(0, 495);
        }

        order.setAllSku(join);
        order.setQtyAll(qtyAll);
    }

    /**
     * 检验订单手机号是否为手机号-11位数字
     *
     * @param mobile
     * @return
     */
    public static boolean isMobileCheck(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return false;
        }
        if (mobile.length() != 11) {
            return false;
        }
        return Pattern.matches("^[1]([3-9])[0-9]{9}$", mobile);
    }
}
