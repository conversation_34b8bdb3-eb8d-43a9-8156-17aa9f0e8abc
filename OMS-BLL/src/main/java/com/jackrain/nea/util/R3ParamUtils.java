package com.jackrain.nea.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.constant.OcOmsFrontCommonConstant;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class R3ParamUtils {

    /**
     * 从jsonObj转换到保存该有的入参
     */
    public static <T extends SgR3BaseRequest> T parseSaveObject(QuerySession session, Class<T> clazz) {
        Locale locale = session.getLocale();
        JSONObject param = R3ParamUtils.getParamJo(session);
        if (log.isDebugEnabled()) {
            log.debug("Start parseSaveObject:param{}"
                    , JSONObject.toJSONString(param));
        }
        AssertUtils.notNull(param, "参数不能为空!-param", locale);
        Long objId = param.getLong(OcOmsFrontCommonConstant.OBJID);
        JSONObject fixC = param.getJSONObject(OcOmsFrontCommonConstant.FIXCOLUMN);
        User user = session.getUser();
        AssertUtils.notNull(user, "用户不能为空!", locale);
        T request = null;
        if (fixC == null) {
            JSONObject tabitem = param.getJSONObject(OcOmsFrontCommonConstant.TABITEM);
            //删除的情况
            if (tabitem != null) {
                request = JSONObject.parseObject(tabitem.toString(), clazz);
            } else {
                try {
                    request = clazz.newInstance();
                } catch (Exception e) {

                }
            }

        } else {
            request = JSONObject.parseObject(fixC.toString(), clazz);
        }
        if (request != null) {
            request.setObjId(objId);
            request.setObjids(param.getString(OcOmsFrontCommonConstant.OBJIDS));
            //动作定义列表界面传参转换
            if (CollectionUtils.isNotEmpty(param.getJSONArray(OcOmsFrontCommonConstant.IDS))) {
                request.setIds(JSONObject.parseArray(param.getString(OcOmsFrontCommonConstant.IDS), Long.class));
            }
            request.setLoginUser(user);
        }

        return request;
    }

    /**
     * 返回获取param jsonObj
     *
     * @param session session
     */
    public static JSONObject getParamJo(QuerySession session) {
        AssertUtils.notNull(session, "session is null");
        Locale locale = session.getLocale();
        DefaultWebEvent event = session.getEvent();
        AssertUtils.notNull(event, "参数不能为空!-event", locale);
        return JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue(OcOmsFrontCommonConstant.PARAM),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
    }

    /**
     * 将v14的 holder转化成v13的holder
     * 考虑 date可能即是jsonObj 又是jsonArray的 情况
     */
    public static ValueHolder convertV14WithResult(ValueHolderV14<? extends SgR3BaseResult> holderV14) {
        ValueHolder holder = new ValueHolder();
        if (holderV14 != null) {
            holder.put("code", holderV14.getCode());
            holder.put("message", holderV14.getMessage());
            if (holderV14.getData() != null) {
                if (holderV14.getData().getDataJo() != null) {
                    holder.put("data", holderV14.getData().getDataJo());
                } else if (CollectionUtils.isNotEmpty(holderV14.getData().getDataArr())) {
                    holder.put("data", holderV14.getData().getDataArr());
                }
            }
        }
        return holder;
    }

    /**
     * 将v14的 holder转化成v13的holder
     */
    public static ValueHolder convertV14(ValueHolderV14 holderV14) {
        ValueHolder holder = new ValueHolder();
        if (holderV14 != null) {
            holder.put("code", holderV14.getCode());
            holder.put("message", holderV14.getMessage());
            holder.put("data", holderV14.getData());
        }
        return holder;
    }

    /**
     * 列表返回结果封装
     *
     * @param arraySize 集合长度
     * @param errMap    错误map
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/11
     */
    public static ValueHolder getExcuteValueHolder(int arraySize, Map<Long, Object> errMap) {
        ValueHolder holder = new ValueHolder();
        int errSize = errMap.size();

        if (arraySize == 1) {
            if (errSize == 0) {
                holder.put("code", ResultCode.SUCCESS);
                holder.put("message", "执行成功！");
            } else {
                // 兼容单对象和批量一条数据时直接获取错误信息
                String message = "";
                List list = new ArrayList();
                for (Map.Entry entry : errMap.entrySet()) {
                    message = entry.getValue().toString();
                    Map<String, Object> map = new HashMap<String, Object>(2);
                    map.put("objid", entry.getKey());
                    map.put("message", entry.getValue());
                    list.add(map);
                }

                holder.put("code", ResultCode.FAIL);
                holder.put("message", message);
                holder.put("data", list);
            }
        } else {
            if (errSize == 0) {
                holder.put("code", ResultCode.SUCCESS);
                holder.put("message", "执行成功记录数：" + arraySize);
            } else {
                List list = new ArrayList();
                for (Map.Entry entry : errMap.entrySet()) {
                    HashMap<String, Object> map = new HashMap<String, Object>(2);
                    map.put("objid", entry.getKey());
                    map.put("message", entry.getValue());
                    list.add(map);
                }
                holder.put("code", ResultCode.FAIL);
                holder.put("message", "执行成功记录数：" + (arraySize - errSize) + "，执行失败记录数：" + errSize);
                holder.put("data", list);
            }
        }
        return holder;
    }

    public static <T extends SgR3BaseRequest> List<Long> getBatchObjIds(T request) {
        List<Long> ids = new ArrayList<>();
        Long objId = request.getObjId();
        List<Long> objids = request.getIds();
        if (CollectionUtils.isNotEmpty(objids)){
            ids.addAll(objids);
        }
        if (Objects.nonNull(objId)) {
            ids.add(objId);
        }
        return ids;
    }

}
