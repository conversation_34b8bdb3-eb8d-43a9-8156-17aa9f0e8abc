//package com.jackrain.nea.util;
//
//import com.google.common.util.concurrent.ThreadFactoryBuilder;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//
//import java.util.Map;
//import java.util.concurrent.ArrayBlockingQueue;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.ThreadPoolExecutor;
//import java.util.concurrent.TimeUnit;
//
///**
// * @Desc : 线程池创建
// * <AUTHOR> xiWen
// * @Date : 2021/1/12
// */
//@Slf4j
//public class OMSThreadPoolBuilder {
//
//    //private static AtomicBoolean isLock = new AtomicBoolean(false);
//
//    /**
//     * 分库数量
//     */
//    private static int SIZE;
//
//    private static final String BASE_ODR = "OC_B_ORDER";
//
//    /**
//     * 线程池
//     */
//    private static ConcurrentHashMap<String, ThreadPoolExecutor> threadPoolMap = new ConcurrentHashMap<>();
//
//    /**
//     * 获取线程池
//     *
//     * @param poolName      线程池名称
//     * @param queueCapacity 队列
//     * @return 线程池
//     */
//    protected static ThreadPoolExecutor build(String poolName, int... queueCapacity) {
//
//        AssertUtil.assertException(StringUtils.isBlank(poolName), "ThreadPool Name Can Not Be Empty");
//        ThreadPoolExecutor var = threadPoolMap.get(poolName);
//        if (var == null) {
//            synchronized (OMSThreadPoolBuilder.class) {
//                initSize();
//                int queue = queueCapacity.length > 0 ? (queueCapacity[0] < SIZE ? SIZE : queueCapacity[0]) : SIZE;
//                var = threadPoolMap.get(poolName);
//                if (var == null) {
//                    var = new ThreadPoolExecutor(
//                            SIZE,
//                            SIZE,
//                            0L,
//                            TimeUnit.SECONDS,
//                            new ArrayBlockingQueue(queue),
//                            new ThreadFactoryBuilder().setNameFormat(poolName).build(),
//                            new ThreadPoolExecutor.AbortPolicy()
//                    );
//                    //    executor.allowCoreThreadTimeOut(true);
//                    threadPoolMap.putIfAbsent(poolName, var);
//                }
//            }
//        }
//        return var;
//    }
//
//    /**
//     * init
//     */
//    private static void initSize() {
//        if (SIZE < 2) {
//            if (var.size() > 1) {
//                SIZE = var.size();
//                return;
//            }
//            throw new NDSException("UnCatch DataBase Node Count...");
//        }
//    }
//}
