package com.jackrain.nea.util;

import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.web.face.User;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/7/20
 */
public class ThreadLocalUtil {

    public static final ThreadLocal<User> users = new ThreadLocal<>();

    public static final ThreadLocal<Map<String, BigDecimal>> keyValPair = ThreadLocal.withInitial(() -> new HashMap<>());

    public static final ThreadLocal<Map<Long, RedisReentrantLock>> mapLocks = new ThreadLocal<>();

    public static final ThreadLocal<List<String>> logStepMsg = ThreadLocal.withInitial(() -> new ArrayList<>());

    public static final ThreadLocal<List<RedisReentrantLock>> locks = ThreadLocal.withInitial(() -> new ArrayList<>());

    public static final ThreadLocal<DateTimeFormatter> sdf
            = ThreadLocal.withInitial(() -> DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

}
