package com.jackrain.nea.util;

import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.request.sap.B2cOrderOutStockRequest;
import com.jackrain.nea.oc.oms.model.request.sap.B2cOrderRefundRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.rpc.CpRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 中台数据相关数据转换为SAP模型
 *
 * @author: 王帅
 * @since: 2020-03-23
 * create at : 2020-03-23 13:00
 */
@Component
@Slf4j
public class SapModelTransferUtil {

    @Autowired
    private CpRpcService cpRpcService;

    private static SapModelTransferUtil sapModelTransferUtil;

    @PostConstruct
    public void init() {
        sapModelTransferUtil = this;
        sapModelTransferUtil.cpRpcService = this.cpRpcService;
        //sapModelTransferUtil.postFeeHandleService = this.postFeeHandleService;
    }

    /**
     * 订单类型 转 SAP订单类型
     *  中台 【1：正常 8：虚拟】 转 SAP 【1:正常订单(JYCK）】
     *  中台 【2：换货 3：补发】 转 SAP 【2:补发单（BFCK）】
     * @param orderType
     * @return
     */
    /*private static String orderTypeToSapOrderType(Integer orderType) {
        if (orderType != null && (orderType == OrderTypeEnum.NORMAL.getVal() || orderType == OmsOrderType.DIFFPRICE.toInteger())) {
            return YiShangSapConstants.SALE_ORDER_TYPE_ZC;
        } else if (orderType != null && (orderType == OrderTypeEnum.EXCHANGE.getVal() || orderType == OmsOrderType.REISSUE.toInteger())) {
            return YiShangSapConstants.SALE_ORDER_TYPE_BF;
        }
        return YiShangSapConstants.SALE_ORDER_TYPE_QT;
    }*/

    /**
     * 中台销售订单 转 SAP销售订单
     * @param ocBOrder
     * @param orderItemList
     * @return
     */
//    public static B2cOrderOutStockRequest saleOrderTransfer(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList) {
//        if (log.isDebugEnabled()) {
//            log.debug("saleOrderTransfer params ocBOrder:{}, orderItemList:{}", JSON.toJSONString(ocBOrder), JSON.toJSONString(orderItemList));
//        }
//        B2cOrderOutStockRequest stockRequest = new B2cOrderOutStockRequest();
//        OcBOrder orderModel = stockRequest.getOcBOrder();
//
//        /** 设定SAP销售订单主表信息 */
//        orderModel.setId(ocBOrder.getId());
//        orderModel.setOrderStatus(ocBOrder.getOrderStatus());
//        orderModel.setCpCShopEcode(ocBOrder.getCpCShopEcode());//?
//        orderModel.setBillNo(ocBOrder.getBillNo());
//        orderModel.setOrderDate(ocBOrder.getOrderDate());
//        orderModel.setDeliveryTime(ocBOrder.getScanTime());
//        orderModel.setShopCode(ocBOrder.getCpCShopEcode());
//        orderModel.setSourceNo(ocBOrder.getSourceCode());
//        orderModel.setOrderType(orderTypeToSapOrderType(ocBOrder.getOrderType()));
//        orderModel.setPayMethod(ocBOrder.getPayType().toString());
//        orderModel.setUserNick(ocBOrder.getUserNick());// 买家ID
//        orderModel.setPayTime(ocBOrder.getPayTime());
//        orderModel.setOrderAmt(ocBOrder.getOrderAmt());
//        orderModel.setProductAmt(ocBOrder.getProductAmt());
//        orderModel.setFreight(ocBOrder.getShipAmt());// 买家支付运费
//        orderModel.setWarehouseCode(ocBOrder.getCpCPhyWarehouseEcode());// 仓库
//
//        /** 设定SAP销售订单子表信息 */
//        List<YiShangSapSaleOrderAddRequest.OrderLine> orderLines = new ArrayList<>();
//        for (OcBOrderItem ocBOrderItem : orderItemList) {
//            YiShangSapSaleOrderAddRequest.OrderLine orderLine = new YiShangSapSaleOrderAddRequest.OrderLine();
//            orderLine.setCode(ocBOrderItem.getPsCSkuEcode());// Sku编码
//            orderLine.setBarCode(ocBOrderItem.getPsCSkuEcode());
//            orderLine.setQty(ocBOrderItem.getQty());
//            orderLine.setPrice(ocBOrderItem.getPrice());
//            orderLines.add(orderLine);
//        }
//        returnModel.setOrderLines(orderLines);
//        if (log.isDebugEnabled()) {
//            log.debug("saleOrderTransfer result:"+ JSON.toJSONString(returnModel));
//        }
//        return returnModel;
//    }

    /**
     * 中台销售订单 转 SAP销售订单 (订单出库)
     *
     * @param ocBOrderList
     * @param orderItemList
     * @return
     */
    public static List<B2cOrderOutStockRequest> saleOrderTransfer(List<OcBOrder> ocBOrderList, List<OcBOrderItem> orderItemList) {
        List<B2cOrderOutStockRequest> sapSaleOrderList = new ArrayList<>();
        Map<Long, List<OcBOrderItem>> map = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
        for (OcBOrder ocBOrder : ocBOrderList) {
            B2cOrderOutStockRequest stockRequest = new B2cOrderOutStockRequest();
            List<OcBOrderItem> childList = map.get(ocBOrder.getId());
            /* todo 获取邮费 ,构造一个订单明细
            BigDecimal postFee = sapModelTransferUtil.postFeeHandleService.getPostFee(ocBOrder, childList, false);
            if (BigDecimal.ZERO.compareTo(postFee) != 0) {
                childList.add(sapModelTransferUtil.postFeeHandleService.getPostFeeItemInfo(postFee));
            }*/
            //订单类型
            ocBOrder.setSgBOutBillNo(OrderTypeEnum.getTextByVal(ocBOrder.getOrderType()));
            stockRequest.setOcBOrder(ocBOrder);
            stockRequest.setOrderItemList(childList);
            sapSaleOrderList.add(stockRequest);
        }
        return sapSaleOrderList;
    }

    /**
     * 中台销售退单 转 SAP销售退单
     * @param ocBReturnOrder
     * @param orderRefundList
     * @return
     */
//    public static YiShangSapSaleReturnAddRequest saleReturnTransfer(OcBReturnOrder ocBReturnOrder, List<OcBReturnOrderRefund> orderRefundList) {
//        if (log.isDebugEnabled()) {
//            log.debug("saleReturnTransfer params ocBReturnOrder:{}, orderRefundList:{}", JSON.toJSONString(ocBReturnOrder), JSON.toJSONString(orderRefundList));
//        }
//        YiShangSapSaleReturnAddRequest returnModel = new YiShangSapSaleReturnAddRequest();
//
//        /** 设定SAP销售退单主表信息 */
//        returnModel.setId(ocBReturnOrder.getId());
//        returnModel.setReturnBillNo(ocBReturnOrder.getBillNo());
//        returnModel.setOrigBillNo(ocBReturnOrder.getOrigOrderNo());// 原单的单据编号
//        returnModel.setOrigSourceNo(ocBReturnOrder.getOrigSourceCode());
//        returnModel.setOmsRefundNo(null);
//        returnModel.setInTime(ocBReturnOrder.getInTime());
//        returnModel.setShopId(ocBReturnOrder.getCpCShopId());
//        returnModel.setShopCode(ocBReturnOrder.getCpCShopEcode());
//        returnModel.setReturnAmtActual(ocBReturnOrder.getReturnAmtActual());
//        returnModel.setWarehouseId(ocBReturnOrder.getCpCPhyWarehouseInId());
//        //returnModel.setWarehouseCode(getCpCPhyWarehouseInECode(ocBReturnOrder.getCpCPhyWarehouseInId()));
//        returnModel.setReturnReason("09");
//
//        /** 设定SAP销售退单子表信息 */
//        List<YiShangSapSaleReturnAddRequest.OrderLine> orderLines = new ArrayList<>();
//        for (OcBReturnOrderRefund ocBReturnOrderRefund : orderRefundList) {
//            // 入库数量 > 0 时，才传SAP
//            if (ocBReturnOrderRefund.getQtyIn() != null && ocBReturnOrderRefund.getQtyIn().compareTo(BigDecimal.ZERO) > 0) {
//                YiShangSapSaleReturnAddRequest.OrderLine orderLine = new YiShangSapSaleReturnAddRequest.OrderLine();
//                orderLine.setCode(ocBReturnOrderRefund.getPsCSkuEcode());
//                orderLine.setBarCode(ocBReturnOrderRefund.getPsCSkuEcode());
//                orderLine.setQty(ocBReturnOrderRefund.getQtyIn());// 入库数量
//                orderLine.setDefectQty(ocBReturnOrderRefund.getReserveDecimal02());// 次品数量
//                orderLine.setPrice(ocBReturnOrderRefund.getAmtRefundSingle());// 单件退货金额
//                orderLines.add(orderLine);
//            }
//        }
//        returnModel.setOrderLines(orderLines);
//        if (log.isDebugEnabled()) {
//            log.debug("saleReturnTransfer result:"+ JSON.toJSONString(returnModel));
//        }
//        return returnModel;
//    }

    /**
     * 获取CP_C_PHY_WAREHOUSE_IN_ECODE
     *
     * @param cpCphyWarehouseId
     * @return
     */
    private static String getCpCPhyWarehouseInECode(Long cpCphyWarehouseId) {
        CpCPhyWarehouse rtn = sapModelTransferUtil.cpRpcService.queryByWarehouseId(cpCphyWarehouseId);
        if (rtn != null) {
            return rtn.getEcode();
        }
        return null;
    }

    /**
     * 中台销售退单 转 SAP销售退单
     *
     * @param returnOrderInfoList
     * @param orderRefundList
     * @return
     */
    public static List<B2cOrderRefundRequest> saleReturnTransfer(List<OcBReturnOrder> returnOrderInfoList, List<OcBReturnOrderRefund> orderRefundList) {
        List<B2cOrderRefundRequest> sapSaleReturnList = new ArrayList<>();
        Map<Long, List<OcBReturnOrderRefund>> map = orderRefundList.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getOcBReturnOrderId));
        for (OcBReturnOrder ocBReturnOrder : returnOrderInfoList) {
            B2cOrderRefundRequest returnModel = new B2cOrderRefundRequest();
            List<OcBReturnOrderRefund> childList = map.get(ocBReturnOrder.getId());
            returnModel.setOcBReturnOrder(ocBReturnOrder);
            //returnModel.setReturnOrderItemList(childList);
            sapSaleReturnList.add(returnModel);
        }
        return sapSaleReturnList;
    }

    /**
     * 中台退款单 转 SAP退款单
     * @param ocBReturnAfSend
     * @param ocBReturnAfSendItemList
     * @return
     */
//    public static YiShangSapRefundAddRequest refundTransfer(OcBReturnAfSend ocBReturnAfSend, List<OcBReturnAfSendItem> ocBReturnAfSendItemList) {
//        if (log.isDebugEnabled()) {
//            log.debug("refundTransfer params ocBReturnAfSend:{}, ocBReturnAfSendItemList:{}", JSON.toJSONString(ocBReturnAfSend), JSON.toJSONString(ocBReturnAfSendItemList));
//        }
//        YiShangSapRefundAddRequest returnModel = new YiShangSapRefundAddRequest();
//
//        /** 设定SAP退款单主表信息 */
//        returnModel.setId(ocBReturnAfSend.getId());
//        returnModel.setOrigBillNo(ocBReturnAfSend.getSourceBillNo());// 原单单据编号
//        returnModel.setShopCode(ocBReturnAfSend.getCpCShopEcode());
//        returnModel.setOrigSourceNo(ocBReturnAfSend.getTid());
//        returnModel.setWarehouseCode(null);
//        returnModel.setOmsRefundNo(ocBReturnAfSend.getBillNo());
//      //  returnModel.setReason(ocBReturnAfSend.getReason());
//        Date refundTime = ocBReturnAfSend.getFinancialAuditTime() == null ? ocBReturnAfSend.getModifieddate() : ocBReturnAfSend.getFinancialAuditTime();
//        returnModel.setRefundTime(refundTime);// 财审时间(或修改时间)
//        returnModel.setPayAccount(ocBReturnAfSend.getPayAccount());
//        returnModel.setAmtReturnActual(ocBReturnAfSend.getAmtReturnActual());
//        returnModel.setAmtReturnApply(ocBReturnAfSend.getAmtReturnApply());
//        returnModel.setFreight(null);// TODO 运费退款金额 待定
//        returnModel.setReason("09");
//
//        /** 设定SAP退款单子表信息 */
//        List<YiShangSapRefundAddRequest.OrderLine> orderLines = new ArrayList<>();
//        for (OcBReturnAfSendItem ocBReturnAfSendItem : ocBReturnAfSendItemList) {
//            YiShangSapRefundAddRequest.OrderLine orderLine = new YiShangSapRefundAddRequest.OrderLine();
//            orderLine.setCode(ocBReturnAfSendItem.getPsCSkuEcode());
//            orderLine.setBarCode(ocBReturnAfSendItem.getPsCSkuEcode());
//            orderLine.setQty(BigDecimal.ONE);// 为了迎合SAP，此处数量固定为1，下面price存行总计
//            orderLine.setPrice(ocBReturnAfSendItem.getAmtReturn());// 退款金额
//            orderLines.add(orderLine);
//        }
//        returnModel.setOrderLines(orderLines);
//        if (log.isDebugEnabled()) {
//            log.debug("refundTransfer result:"+ JSON.toJSONString(returnModel));
//        }
//        return returnModel;
//    }

    /**
     * 中台销售退单 转 SAP销售退单
     * @param ocBReturnAfSendList
     * @param ocBReturnAfSendItemList
     * @return
     */
//    public static List<B2cOrderRefundRequest> refundTransfer(List<OcBReturnAfSend> ocBReturnAfSendList, List<OcBReturnAfSendItem> ocBReturnAfSendItemList) {
//        List<B2cOrderRefundRequest> sapSaleReturnList = new ArrayList<>();
//        B2cOrderRefundRequest returnModel = new B2cOrderRefundRequest();
//        Map<Long, List<OcBReturnAfSendItem>> map = ocBReturnAfSendItemList.stream().collect(Collectors.groupingBy(OcBReturnAfSendItem::getOcBReturnAfSendId));
//        for (OcBReturnAfSend ocBReturnAfSend : ocBReturnAfSendList) {
//            List<OcBReturnAfSendItem> childList = map.get(ocBReturnAfSend.getId());
//            returnModel.setOcBReturnOrder(ocBReturnAfSend);
//            returnModel.setReturnOrderItemList(childList);
//            sapSaleReturnList.add(returnModel);
//        }
//        return sapSaleReturnList;
//    }


}
