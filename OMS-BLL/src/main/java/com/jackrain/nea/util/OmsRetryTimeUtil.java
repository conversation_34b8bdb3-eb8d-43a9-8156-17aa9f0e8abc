package com.jackrain.nea.util;

import com.jackrain.nea.config.PropertiesConf;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * @Author: 黄世新
 * @Date: 2020/12/28 下午3:20
 * @Version 1.0
 * 重试时间
 */
@Component
public class OmsRetryTimeUtil {

    private static int[] ladder = new int[]{1, 10, 20, 30, 60, 120, 180, 240, 300, 480, 300, 300, 300, 300, 300, 300};
    private static final int MAX_RETRY_NUMBER = 16;

    private static String timeLadder = "10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h";

    private static final ConcurrentMap<Integer , Long> levelLadderTable = new ConcurrentHashMap<>(32);

    static {
        init();
    }

    public static void init() {
        HashMap<String, Long> timeUnitTable = new HashMap<>(6);
        timeUnitTable.put("s", 1000L);
        timeUnitTable.put("m", 1000L * 60);
        timeUnitTable.put("h", 1000L * 60 * 60);
        timeUnitTable.put("d", 1000L * 60 * 60 * 24);
        String[] levelArray = timeLadder.split(" ");
        for (int i = 0; i < levelArray.length; i++) {
            String value = levelArray[i];
            String ch = value.substring(value.length() - 1);
            Long tu = timeUnitTable.get(ch);
            int level = i + 1;
            long num = Long.parseLong(value.substring(0, value.length() - 1));
            long delayTimeMillis = tu * num;
            levelLadderTable.put(level, delayTimeMillis);
        }
    }

    /**
     * 根据重试次数返回下次执行时间(date)
     *
     * @param retryNumber
     * @return
     */
    public Date getRetryTimeByDate(int retryNumber) {
        Long retryTimeByMillisecond = this.getRetryTimeByMillisecond(retryNumber);
        Date date = new Date();
        date.setTime(retryTimeByMillisecond);
        return date;
    }


    /**
     * 根据重试次数返回下次执行时间(毫秒值)
     *
     * @param retryNumber
     * @return
     */
    public Long getRetryTimeByMillisecond(int retryNumber) {
        if (retryNumber != 0) {
            retryNumber = retryNumber - 1;
        }
        if (retryNumber > MAX_RETRY_NUMBER) {
            retryNumber = MAX_RETRY_NUMBER;
        }
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Long retryTime = config.getProperty("audit_retry_time", 60000L);
        int i = ladder[retryNumber];
        long time = retryTime * i;
        Long nextTime = System.currentTimeMillis() + time;
        return nextTime;
    }


    /**
     * 根据重试次数返回下次执行时间(毫秒值)
     *
     * @param retryNumber
     * @return
     */
    public static Long getNextTime(int retryNumber) {
        if (retryNumber < 1) {
            retryNumber = 1;
        } else {
            if (retryNumber > MAX_RETRY_NUMBER) {
                retryNumber = MAX_RETRY_NUMBER;
            }
        }
        Long ms = levelLadderTable.get(retryNumber);
        Long nextTime = System.currentTimeMillis() + ms;
        return nextTime;
    }



    /**
     * 根据重试次数返回下次执行时间
     *
     * @param retryNumber
     * @return
     */
    public static Date getNextTimeOfDate(int retryNumber) {
        Date date = new Date();
        date.setTime(getNextTime(retryNumber));
        return date;
    }


    public static void main(String[] args) {
        System.out.println(getNextTimeOfDate(2));
    }
}
