package com.jackrain.nea.util;

import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * @Description:
 * @Author: chenb
 * @Date: 2019/3/14 14:30
 */
@Slf4j
public class OmsStorageUtils {

    public static <T extends BaseModel> T setBModelDefalutData(T sourceModel, User loginUser) {
        if (sourceModel == null || loginUser == null) {
            AssertUtils.logAndThrow("参数有误！");
        }
        Date systemDate = new Date();
        Long loginUserId = loginUser.getId() == null ? null : loginUser.getId().longValue();

        sourceModel.setAdClientId(Long.valueOf(loginUser.getClientId()));
        sourceModel.setAdOrgId(Long.valueOf(loginUser.getOrgId()));
        sourceModel.setIsactive("Y");
        sourceModel.setOwnerid(loginUserId);
        sourceModel.setOwnername(loginUser.getName());
        sourceModel.setCreationdate(systemDate);
        sourceModel.setModifierid(loginUserId);
        sourceModel.setModifiername(loginUser.getName());
        sourceModel.setModifieddate(systemDate);

        return sourceModel;
    }

    public static <T extends BaseModel> T setBModelDefalutDataByUpdate(T sourceModel, User loginUser) {
        if (sourceModel == null || loginUser == null) {
            AssertUtils.logAndThrow("参数有误！");
        }
        Date systemDate = new Date();
        Long loginUserId = loginUser.getId() == null ? null : loginUser.getId().longValue();

        sourceModel.setModifierid(loginUserId);
        sourceModel.setModifiername(loginUser.getName());
        sourceModel.setModifieddate(systemDate);

        return sourceModel;
    }
    /**
     * 超长字符串截取
     */
    public static String strSubString(String remark, int size) {
        if (StringUtils.isNotEmpty(remark)) {
            if (remark.length() > size) {
                remark = remark.substring(0, size);
            }
        }
        return remark;
    }
}
