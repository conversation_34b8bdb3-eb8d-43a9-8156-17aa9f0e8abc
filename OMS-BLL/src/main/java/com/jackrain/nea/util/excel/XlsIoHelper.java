package com.jackrain.nea.util.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.OSSClient;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.dto.CStoreDimItemDTO;
import com.jackrain.nea.oc.oms.mapper.OcBExcelMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import com.jackrain.nea.oc.oms.nums.OrderGenericMarkEnum;
import com.jackrain.nea.oc.oms.services.UserQueryListService;
import com.jackrain.nea.oc.oms.util.BigDecimalUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.service.StCHoldOrderReasonQueryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: xiWen.z
 * create at: 2019/8/5 0005
 */
@Slf4j
@Component
public class XlsIoHelper {

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;


    private final int cnsOne = 1;
    private final int cnsZero = 0;
    private final int subIdx = 3;
    private int bSz = 500;
    private final int wbLmt = 100;
    private final int miSec = 1000;
    private final int shtLmt = 999999;
    private final String emps = "";
    private final String comma = ",";
    public static final FastDateFormat sdf = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
    private final String ORDER_TABLE = "oc_b_order";

    @Autowired
    OcBExcelMapper ocBExcelMapper;

    @Autowired
    UserQueryListService userQueryListService;

    @Autowired
    CpRpcService cpRpcService;

    @Autowired
    private StCHoldOrderReasonQueryService holdOrderReasonQueryService;

    @Autowired
    private AdbSourceExportQueryWrapper adbSourceExportQueryWrapper;

    /**
     * export
     *
     * @param n      file name
     * @param l      class list
     * @param m      convert value map
     * @param es     esList
     * @param u      user
     * @param xcd    XlsConvertData
     * @param usrPem UserPermission
     * @return ValueHolderV14
     */
    public ValueHolderV14 export(String n, List<Class> l, XlsIoModel m, List<Long> es, User u, XlsConvertData xcd,
                                 UserPermission usrPem, Boolean withTag) {

        long l1 = System.currentTimeMillis();

        /**
         * 1. init
         */
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            bSz = config.getProperty("exportExcel.sqlSize", 500);
            exportInitModel(l, n, m, usrPem);
        } catch (Exception e) {
            log.error(LogUtil.format("Occurred When invoke exportInitModel"), Throwables.getStackTraceAsString(e));
        }

        // test r3
        //  r3GroupVal(u);

        /**
         * 2. business
         */
        String s = null;
        try {
            s = doExport(m, es, u, xcd, withTag);
        } catch (Exception e) {
            log.error(LogUtil.format("export Error: Occurred When invoke doExport", "零售发货单导出"), e);

        }

        /**
         * 3. result
         */
        ValueHolderV14 v = new ValueHolderV14();
        if (s == null || s.length() < cnsOne) {
            v.setCode(ResultCode.FAIL);
            v.setMessage(Resources.getMessage("导出失败", u.getLocale()));
        } else {
            v.setCode(ResultCode.SUCCESS);
            v.setMessage(Resources.getMessage("导出成功", u.getLocale()));
            v.setData(s);
        }
        long l2 = System.currentTimeMillis();
        return v;
    }


    /**
     * Excel.export.init
     *
     * @param cls    List<Class>
     * @param n      string
     * @param v      XlsIoModel
     * @param usrPem UserPermission
     * @throws NDSException ex
     */
    private void exportInitModel(List<Class> cls, String n, XlsIoModel v, UserPermission usrPem) throws NDSException {
        /**
         * 1. validation
         */
        if (v == null) {
            throw new NDSException("Parameter XlsIoModel Is Null");
        }
        v.setExcelName(n == null ? "" : n);
        List<Map<String, Object>> sht = new ArrayList<>();
        if (cls == null || cls.size() < cnsOne) {
            throw new NDSException("Parameter Class Was Empty");
        }

        Map<String, List<String>> htm = new HashMap<>();
        Map<String, List<String>> hkm = new HashMap<>();
        Map<String, List<String>> ikm = new HashMap<>();
        v.setHeaderKey(hkm);
        v.setHeaderText(htm);
        v.setVifField(ikm);

        /**
         * 1.2 deal user permission
         */
        Set<String> v0 = null;
        if (usrPem != null) {
            v0 = usrPem.getForbiddenColumns();
        }

        for (int i = 0, l = cls.size(); i < l; i++) {
            Class<T> c = cls.get(i);
            if (c == null) {
                throw new NDSException("Class Not Found In Collection");
            }
            /**
             * 1. annotation.judge
             */
            if (!c.isAnnotationPresent(XlsDBAno.class)) {
                throw new NDSException("Class Not Found The XlsDBAno Annotation");
            }
            /**
             *  deal.level.table
             * 2. get information from annotation
             */
            XlsDBAno xlsDb = c.getAnnotation(XlsDBAno.class);
            if (xlsDb.name().trim().length() < cnsOne) {
                throw new NDSException("Name Of Annotation XlsAno Is Null Or Empty");
            }
            Map<String, Object> m = new HashMap<>();
            m.put(XlsIoModel.TN, xlsDb.name());
            m.put(XlsIoModel.SN, xlsDb.desc());
            m.put(XlsIoModel.IDX, xlsDb.index());
            m.put(XlsIoModel.SOT, xlsDb.sort());
            m.put(XlsIoModel.ST, xlsDb.st());
            insertionSort(sht, m);
            v.setSheetInfo(sht);
            if (xlsDb.index() == 0) {
                v.setPrimaryTable(xlsDb.name());
            }

            /**
             * deal.level.Field
             * 3. header text collection
             */
            Field[] fs = c.getDeclaredFields();
            if (fs == null || fs.length < cnsOne) {
                throw new NDSException("Class Reflect Was Wrong Occur When Get Fields");
            }
            /**
             * todo need user support this data at this time- start
             */
            // all
            Map<String, Map<String, Map<Object, Object>>> csm = v.getConvertSource();
            if (csm == null) {
                csm = new HashMap<>();
                v.setConvertSource(csm);
            }
            // field-get table
            Map<String, Map<Object, Object>> m1 = csm.get(xlsDb.name());
            if (m1 == null) {
                m1 = new HashMap<>();
                csm.put(xlsDb.name(), m1);
            }
           /* if (m1.get(xlsAno.name()) == null) { // value -- get fi
                throw new NDSException("Need User Supply Convert Data");
            }*/
            // todo need user support this data at this time- end
            filterSortField(v, xlsDb.name(), m, fs, m1, v0);
        }
    }

    /**
     * field.deal.comprehensive
     *
     * @param v  XlsIoModel
     * @param n  String table name
     * @param m  Map<String, Object>  table info
     * @param fs Field[] fields info
     * @param m1 Map<String, Map<Object, Object>> convert value
     * @param v0 UserPermission
     * @throws NDSException NDSException
     */
    private void filterSortField(XlsIoModel v, String n, Map<String, Object> m, Field[] fs,
                                 Map<String, Map<Object, Object>> m1, Set<String> v0) throws NDSException {
        List<Field> fl = new ArrayList<>();
        for (Field f : fs) {
            // at this time is allow extends parent field
            /*if(!Modifier.isPrivate(f.getModifiers()) ){
                continue;
            }*/
            if (Modifier.isStatic(f.getModifiers())) {
                continue;
            }
            if (!f.isAnnotationPresent(XlsAno.class)) {
                continue;
            }
            fl.add(f);
        }
        if (fl.size() < cnsOne) {
            throw new NDSException("There Is No Field Has XlsAno Annotation");
        }
        if (fl.size() > cnsOne) {
            Collections.sort(fl, (x, y) ->
                    x.getAnnotation(XlsAno.class).index() > y.getAnnotation(XlsAno.class).index() ? 1 : -1);
        }
        int i = 0, l = fl.size();
        StringBuilder sb = new StringBuilder();
        List<String> ht = new ArrayList<>(l);
        List<String> hk = new ArrayList<>(l);
        List<String> ik = new ArrayList<>(l);

        Field f;
        for (; i < l; i++) {
            f = fl.get(i);
            XlsAno a1 = f.getAnnotation(XlsAno.class);
            if (v0 != null) {
                if (v0.contains((a1.name().toUpperCase()))) {
                    continue;
                }
            }
            hk.add(a1.name());
            ht.add(a1.desc());
            List<XlsSt> v1 = Arrays.asList(a1.value());
            if (v1.contains(XlsSt.NOTNULL)) {
                ik.add(a1.name());
            }
            // strategy
            Map<Object, Object> m2 = m1.get(a1.name());
            // todo get from R3
            if (m2 == null) {
                m2 = new HashMap<>();
                //r3GroupVal(); todo  have no time do it at this moment
                m1.put(a1.name(), m2);
            }

            m2.put(XlsIoModel.ST, v1);
            m2.put(XlsIoModel.TP, a1.type());
            if (a1.ignore() > cnsZero) {
                continue;
            }

            sb.append(comma);
            sb.append(a1.name());
        }

        v.getHeaderText().put(n, ht);
        v.getHeaderKey().put(n, hk);
        v.getVifField().put(n, ik);
        m.put(XlsIoModel.SQL, sb.substring(cnsOne));
    }


    /**
     * sheet collection
     * insert sort reordering
     *
     * @param v List<Map<String, Object>>
     * @param m Map<String, Object>
     */
    private void insertionSort(List<Map<String, Object>> v, Map<String, Object> m) {
        int i = 0, l = v.size();
        if (l < cnsOne) {
            v.add(m);
        } else {
            for (; i < l; ) {
                Map<String, Object> m1 = v.get(i);
                int dnc = (int) m1.get(XlsIoModel.IDX);
                int stc = (int) m.get(XlsIoModel.IDX);
                if (stc < dnc) {
                    reOrdering(v, i, m);
                    return;
                }
                i++;
            }
            v.add(m);
        }
    }

    /**
     * insert sort
     * reOrdering
     *
     * @param v List<Map<String, Object>>
     * @param k int
     * @param m Map<String, Object>
     */
    private void reOrdering(List<Map<String, Object>> v, int k, Map<String, Object> m) {
        v.add(null);
        for (int i = v.size() - 1; i > k; i--) {
            Map<String, Object> o = v.get(i - 1);
            v.set(i, o);
        }
        v.set(k, m);
    }

    /**
     * export
     * deal.create.save.push.get
     *
     * @param xlsIoModel XlsIoModel
     * @param esl        es list
     * @param usr        user
     * @param xcd        XlsConvertData
     * @return url
     * @throws Exception exp
     */
    private String doExport(XlsIoModel xlsIoModel, List<Long> esl, User usr, XlsConvertData xcd, Boolean withTag) throws Exception {
        SXSSFWorkbook sxwb = new SXSSFWorkbook(wbLmt);
        Sheet sheet = null;
        Row row = null;
        Map<String, List<String>> headerKey = xlsIoModel.getHeaderKey();
        Map<String, List<String>> headerText = xlsIoModel.getHeaderText();
        Map<String, List<String>> vifField = xlsIoModel.getVifField();
        Map<String, Map<String, Map<Object, Object>>> convertSource = xlsIoModel.getConvertSource();
        //查询来源系统
        List<CpCPlatform> cpCPlatforms = cpRpcService.queryPlatform(null);
        List<Map<String, Object>> storeDimItemList = cpRpcService.findItemByAdStorecolumnName("C_STOREATTRIB3_ID");
        // 组装出来 ID、编码、名称

        Map<String, CStoreDimItemDTO> cStoreDimItemDTOMap = new HashMap<>();
        for (Map<String, Object> map : storeDimItemList) {
            CStoreDimItemDTO cStoreDimItemDTO = new CStoreDimItemDTO();
            cStoreDimItemDTO.setName(String.valueOf(map.get("ENAME")));
            cStoreDimItemDTO.setId((Long) map.get("ID"));
            cStoreDimItemDTO.setCode(String.valueOf(map.get("ECODE")));
            cStoreDimItemDTOMap.put(String.valueOf(map.get("ECODE")), cStoreDimItemDTO);
        }

        Map<Long, String> holdOrderReasonMap = Maps.newHashMap();
        List<StCHoldOrderReason> stCHoldOrderReasonList = holdOrderReasonQueryService.selectAllHoldOrder();
        if (CollectionUtils.isNotEmpty(stCHoldOrderReasonList)) {
            holdOrderReasonMap = stCHoldOrderReasonList.stream().collect(Collectors.toMap(StCHoldOrderReason::getId, StCHoldOrderReason::getReason, (v1, v2) -> v2));
        }

        log.info(LogUtil.format("查询店仓属性.request={}", "CpRpcService.findItemByAdStorecolumnName"), JSONUtil.toJsonStr(cStoreDimItemDTOMap));
        Map<String, String> platformMap = cpCPlatforms.stream().collect(Collectors.toMap(CpCPlatform::getEcode, CpCPlatform::getEname, (v1, v2) -> v2));

        List<Map<String, Object>> v1 = xlsIoModel.getSheetInfo();
        for (Map<String, Object> m : v1) {
            String v2 = m.get(XlsIoModel.TN).toString();
            String v3 = m.get(XlsIoModel.SN).toString();

            sheet = sxwb.createSheet(v3);
            row = sheet.createRow(cnsZero);

            List<String> v4 = headerText.get(v2);
            List<String> v5 = vifField.get(v2);
            List<String> v6 = headerKey.get(v2);
            // todo serial column  default false;
            createHeader(sxwb, row, v4, v5, v6, false);
            m.put(XlsIoModel.CROW, cnsZero);
        }

        int v9;
        int loopCount = esl.size() / bSz + 1;
        for (int loop = 1; loop <= loopCount; loop++) {
            List<Long> v7 = CollUtil.sub(esl, bSz * (loop - 1), bSz * loop);
            if (CollUtil.isNotEmpty(v7)) {
                Map<String, List<Map<String, Object>>> dataMaps = new HashMap<>();

                for (Map<String, Object> m : v1) {
                    String s1 = m.get(XlsIoModel.TN).toString();
                    String es = joinListToString(v7);
                    XlsSqlModel model = prepareSqlCnt(m, s1, es, xlsIoModel.getPrimaryTable());
                    if (ObjectUtil.isNull(withTag)) {
                        withTag = Boolean.FALSE;
                    }
                    List<Map<String, Object>> maps;
                    //区分零售发货单和其他
                    if (ORDER_TABLE.equals(model.getTableName())) {
                        maps = adbSourceExportQueryWrapper.selectOcBAllExcelInfo(model);
                        //匹配来源平台名称
                        Map<Long, String> finalHoldOrderReasonMap = holdOrderReasonMap;
                        Boolean finalWithTag = withTag;
                        maps.forEach(p -> {
                            Object holdReasonId = p.get("HOLD_REASON_ID");
                            Object detentionReasonId = p.get("DETENTION_REASON_ID");
                            if (Objects.nonNull(p.get("GENERIC_MARK"))) {
                                if (OrderGenericMarkEnum.INTERCEPT.getTag().equals(p.get("GENERIC_MARK"))) {
                                    p.put("GENERIC_MARK", OrderGenericMarkEnum.INTERCEPT.getDesc());
                                }
                            }

                            // REFUND_STATUS 如果是6的话 则赋值为"取消" 否则为"未取消"
                            if (Objects.nonNull(p.get("ITEM_REFUND_STATUS")) && "6".equals(p.get("ITEM_REFUND_STATUS").toString())) {
                                p.put("ITEM_REFUND_STATUS", "取消");
                            } else {
                                p.put("ITEM_REFUND_STATUS", "未取消");
                            }

                            if (Objects.nonNull(holdReasonId)) {
                                String reason = finalHoldOrderReasonMap.get((Long) holdReasonId);
                                p.put("hold_reason_name", reason);
                            }
                            if (Objects.nonNull(detentionReasonId)) {
                                String reason = finalHoldOrderReasonMap.get((Long) detentionReasonId);
                                p.put("detention_reason_name", reason);
                            }
                            p.put("GW_SOURCE_GROUP", platformMap.get(p.get("GW_SOURCE_GROUP")));
                            if (p.get("SALES_GROUP_CODE") != null) {
                                p.put("SALES_GROUP_NAME", cStoreDimItemDTOMap.get(p.get("SALES_GROUP_CODE")) == null ? "" :
                                        cStoreDimItemDTOMap.get(p.get("SALES_GROUP_CODE")).getName());
                            }
                            // 处理tag问题 withTag可能为null
                            if (finalWithTag != null && finalWithTag) {
                                // 处理tag问题
                                p.put("tag_name", dealTag(p));
                            }

                            // 总体积
                            Object qty = p.get("QTY");
                            Object volume = p.get("VOLUME");
                            if (Objects.nonNull(qty) && Objects.nonNull(volume)) {
                                String totalVolume = BigDecimalUtil.isNullReturnZero(new BigDecimal(qty.toString()))
                                        .multiply(BigDecimalUtil.isNullReturnZero(new BigDecimal(volume.toString())))
                                        .stripTrailingZeros().toPlainString();
                                p.put("total_volume", totalVolume);
                            }

                            //销售商品属性
                            String saleProductAttr = (String) p.get("SALE_PRODUCT_ATTR");
                            if (StringUtils.isNotBlank(saleProductAttr)) {
                                p.put("SALE_PRODUCT_ATTR", OrderSaleProductAttrEnum.getDescriptionByVal(saleProductAttr));
                            }

                            Long reserveBigint02 = (Long) p.get("RESERVE_BIGINT02");
                            if (Objects.nonNull(reserveBigint02)) {
                                p.put("RESERVE_BIGINT02", reserveBigint02 == 1 ? "是" : "否");
                            }else {
                                p.put("RESERVE_BIGINT02", "否");
                            }
                        });
                    } else {
                        maps = ocBExcelMapper.selectOcBExcelInfo(model);
                    }
                    dataMaps.put(s1, maps);
                }

                if (xcd != null) {
                    xcd.dealData(dataMaps);
                }

                for (Map<String, Object> m : v1) {
                    String s1 = m.get(XlsIoModel.TN).toString();
                    String v2 = m.get(XlsIoModel.SN).toString();
                    sheet = sxwb.getSheet(v2);
                    v9 = (int) m.get(XlsIoModel.CROW);
                    List<String> kl = headerKey.get(s1);
                    Map<String, Map<Object, Object>> vms = convertSource.get(s1);
                    List<Map<String, Object>> dat = dataMaps.get(s1);
                    for (Map<String, Object> stringObjectMap : dat) {
                        v9++;
                        row = sheet.createRow(v9);
                        createCellsWithOutCnt(row, kl, vms, stringObjectMap);
                        m.put(XlsIoModel.CROW, v9);
                    }
                }
            }
        }
        esl.clear();
        String ossFilePath = "OSS-Bucket/EXPORT/" + xlsIoModel.getPrimaryTable().toUpperCase() + "/";
        OutputStream ops = new ByteArrayOutputStream();
        String url = null;
        try {
            sxwb.write(ops);
            ops.flush();
            url = saveAndPushOss(ops, xlsIoModel.getExcelName(), ossFilePath, usr);
        } catch (Exception e) {
            log.error(LogUtil.format("doExport Error: Occurred When Operate Stream", "零售发货单导出"), e);
        } finally {
            try {
                ops.close();
            } catch (IOException e) {
                log.error(LogUtil.format("doExport Error: Occurred When Close Stream", "零售发货单导出"), e);
            }
        }
        printExportStatic(v1);
        return url;

    }


    private Integer getValByKey(Object val) {
        if (ObjectUtil.isNull(val)) {
            return 0;
        }
        if (val.toString().equals("false")) {
            return 0;
        }
        if (val.toString().equals("true")) {
            return 1;
        }
        return Integer.valueOf(String.valueOf(val));
    }

    private String dealTag(Map<String, Object> map) {
        StringBuilder sb = new StringBuilder();
        List<String> tagList = new ArrayList<>(Arrays.asList("IS_MERGE", "IS_INTERECEPT", "IS_INRETURNING", "IS_HASGIFT", "IS_SPLIT", "IS_INVOICE", "IS_JCORDER", "IS_OUT_URGENCY",
                "IS_HAS_TICKET", "IS_COMBINATION", "LOCK_STATUS", "LIVE_FLAG", "IS_O2O_ORDER", "IS_COPY_ORDER", "IS_RESET_SHIP", "IS_PROM_ORDER",
                "IS_EQUAL_EXCHANGE", "IS_OUT_STOCK", "IS_DELIVERY_URGENT", "IS_MODIFIED_ORDER", "IS_EXTRA", "IS_DETENTION", "IS_FORBIDDEN_DELIVERY", "IS_VIP_UPDATE_WAREHOUSE",
                "REVERSE_AUDIT_TYPE", "IS_EXCEPTION", "IS_MEMBER", "IS_CYCLE", "IS_DELIVERY_TO_DOOR", "IS_OVERDUE"));
        for (String tag : tagList) {
            if (OcBOrderConst.IS_STATUS_IY.equals(getValByKey(map.get(tag)))) {
                sb.append(OcOrderTagEum.getTextByKey(tag));
                sb.append(" ");
            }
        }

        if (map.get("PRICE_LABEL") != null && OcOrderTagEum.TAG_DIFFPRICE.getVal().equals(map.get("PRICE_LABEL").toString())) {
            sb.append(OcOrderTagEum.getTextByKey("PRICE_LABEL"));
            sb.append(" ");
        }

        if (map.get("ORDER_SOURCE") != null && OcBOrderConst.IS_STATUS_IN.equals(getValByKey(map.get("IS_COPY_ORDER"))) && OcBOrderConst.IS_STATUS_IN.equals(getValByKey(map.get("IS_RESET_SHIP"))) &&
                OcOrderTagEum.TAG_HAND.getVal().equals(map.get("ORDER_SOURCE").toString())) {
            sb.append(OcOrderTagEum.getTextByKey("ORDER_SOURCE"));
            sb.append(" ");
        }

        if (map.get("ORDER_TYPE") != null && OcOrderTagEum.TAG_CHANGE.getVal().equals(map.get("ORDER_TYPE").toString())) {
            sb.append(OcOrderTagEum.getTextByKey("ORDER_TYPE"));
            sb.append(" ");
        }

        return sb.toString();
    }

    /**
     * statistics
     *
     * @param v list
     */
    private void printExportStatic(List<Map<String, Object>> v) {
        StringBuilder v1 = new StringBuilder();
        v1.append("\nData Export Statistics: -> \n");
        for (Map<String, Object> m : v) {
            String v11 = m.get(XlsIoModel.TN).toString();
            String v12 = m.get(XlsIoModel.SN).toString();
            int v13 = (int) m.get(XlsIoModel.CROW);
            v1.append(v12 + ": " + v11 + ":-> " + v13 + " row\n");
        }
        v1.append("doExport.Finished...");
        recordLog(v1.toString());
    }

    /**
     * create header column
     *
     * @param wb SXSSFWorkbook
     * @param rw Row
     * @param hl header list
     * @param vl attention fields
     * @param kl key list
     * @param g  whether serial
     */
    private void createHeader(SXSSFWorkbook wb, Row rw, List<String> hl, List<String> vl, List<String> kl, boolean g) {
        int i = 0, c = 0, l = hl.size();
        if (g) {
            rw.createCell(cnsZero).setCellValue(XlsIoModel.SERIL);
            c = 1;
        }
        Cell cel;
        for (; i < l; i++, c++) {
            cel = rw.createCell(c);
            cel.setCellValue(hl.get(i));
            if (vl.contains(kl.get(i))) {
                Font fnt = wb.createFont();
                fnt.setColor(HSSFColor.RED.index);
                CellStyle stl = wb.createCellStyle();
                stl.setFont(fnt);
                cel.setCellStyle(stl);
            }
        }
    }

    /**
     * create cell
     *
     * @param row Row
     * @param klt keys list
     * @param vms value mapping value
     * @param rs  ResultSet
     */
    private void createCellsWithOutCnt(Row row, List<String> klt, Map<String, Map<Object, Object>> vms,
                                       Map<String, Object> rs) {
        Cell cel;
        Map<Integer, Cell> cellMap = new HashMap();
        Map<Integer, Long> phyMap = new HashMap();
        for (int i = 0, kl = klt.size(); i < kl; i++) {
            cel = row.createCell(i);
            Object v = rs.get(klt.get(i).toUpperCase()) == null ? rs.get(klt.get(i).toLowerCase()) : rs.get(klt.get(i).toUpperCase());
            if (v == null) {
                v = emps;
            }
            stLabel:
            if (vms.containsKey(klt.get(i))) {

                Map<Object, Object> vm = vms.get(klt.get(i));
                XlsTyp tp = (XlsTyp) vm.get(XlsIoModel.TP);

                if (v != emps && XlsTyp.DATE == tp) {
                    try {
                        cel.setCellValue(sdf.format(new Date(Long.parseLong(v.toString()))));
                        continue;
                    } catch (Exception e) {
                        if (v instanceof String) {
                            String v1 = v.toString();
                            cel.setCellValue(v1);
                            continue;
                        }
                    }
                    cel.setCellValue(sdf.format(v));
                    continue;
                }
                if (XlsTyp.DOUBLE == tp) {
                    Double v1 = v == emps ? cnsZero : Double.valueOf(v.toString());
                    cel.setCellValue(v1);
                    continue;
                }

                List<XlsSt> st = (List<XlsSt>) vm.get(XlsIoModel.ST);
                if (st == null || st.size() < cnsOne) {
                    break stLabel;
                }
                if (st.contains(XlsSt.NORMAL)) {
                    break stLabel;
                }
                if (st.contains(XlsSt.GROUP)) {
                    v = v == emps ? emps : (vm.get(v) == null ? emps : vm.get(v));
                    break stLabel;
                }
                if (st.contains(XlsSt.FOREIGN)) {
                    v = v == emps ? emps : Long.valueOf(v.toString());
                    if (!emps.equals(v)) {
                        CpCPhyWarehouse cPhyWarehouse = cpRpcService.queryByWarehouseId(Long.valueOf(v.toString()));
                        if (cPhyWarehouse != null) {
                            v = cPhyWarehouse.getEname();
                        }
                    }

                    break stLabel;
                }
            }
            cel.setCellValue(v.toString());
        }
    }

    /**
     * create cell withOut condition
     * e.g:  serial
     *
     * @param row Row
     * @param klt list
     * @param vms map
     * @param rs  ResultSet
     * @throws SQLException exp
     */
    private void createCellsWithCnt(Row row, List<String> klt, Map<String, Map<Object, Object>> vms, ResultSet rs)
            throws SQLException {

        Cell cel;
        int i = 0, c = 1, l = klt.size();
        row.createCell(cnsZero).setCellValue(XlsIoModel.SERIL);
        for (; i < l; i++, c++) {
            cel = row.createCell(c);
            Object v = rs.getObject(i);
            if (v == null) {
                v = emps;
            }
            stLabel:
            if (vms.containsKey(klt.get(i))) {
                Map<Object, Object> vm = vms.get(klt.get(i));
                XlsTyp tp = (XlsTyp) vm.get(XlsIoModel.TP);
                if (v != emps && XlsTyp.DATE == tp) {
                    cel.setCellValue(sdf.format(v));
                    continue;
                }
                List<XlsSt> st = (List<XlsSt>) vm.get(XlsIoModel.ST);
                if (st == null || st.size() < cnsOne) {
                    break stLabel;
                }
                if (st.contains(XlsSt.NORMAL)) {
                    break stLabel;
                }
                if (st.contains(XlsSt.GROUP)) {
                    v = v == emps ? emps : vms.get(v);
                    break stLabel;
                }
            }
            cel.setCellValue(v.toString());
        }
    }

    /**
     * create sql
     *
     * @param v  keys Map<String, Object>
     * @param s1 tbName
     * @param es esIds
     * @param pn primary key
     * @return sql.model
     */
    private XlsSqlModel prepareSqlCnt(Map<String, Object> v, String s1, String es, String pn) {

        int v3 = (int) v.get(XlsIoModel.IDX);
        String v4 = v.get(XlsIoModel.SOT).toString();
        String k = v.get(XlsIoModel.SQL).toString();
        StringBuilder sb;
        String hashKey = "id";
        if (v3 > cnsZero) {
            hashKey = pn + "_" + hashKey;
        }

        String sort = emps;
        if (v4 != null && v4.length() > cnsOne) {
            sb = new StringBuilder();
            String[] v5 = v4.split(comma);
            for (int x = 0; x < v5.length; x++) {
                String[] v6 = v5[x].split(":");
                sb.append(", ");
                sb.append(v6[0]);
                sb.append(" ");
                sb.append(v6[1]);
            }
            sort = sb.substring(cnsOne);
        }

        XlsSqlModel m = new XlsSqlModel();
        m.setFields(k);
        m.setTableName(s1);
        m.setHashKey(hashKey);
        m.setEs(es);
        m.setIsActive("Y");
        m.setSort(sort);
        return m;
    }

    /**
     * es
     *
     * @param ls list
     * @return string
     */
    private String joinListToString(List<Long> ls) {
        StringBuilder sb = new StringBuilder();
        for (Long l : ls) {
            sb.append(comma);
            sb.append(l);
        }
        return sb.substring(cnsOne);
    }

    /**
     * save. push. get
     *
     * @param ops stream
     * @param fn  fileName
     * @param fp  oss-file-path
     * @param usr user
     * @return url
     */
    private String saveAndPushOss(OutputStream ops, String fn, String fp, User usr) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String ffn = fp + fn + usr.getName() + sf.format(new Date()) + ".xlsx";
        try {
            ByteArrayOutputStream bos = (ByteArrayOutputStream) ops;
            ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
            OSSClient ossClient = new OSSClient(this.endpoint, this.accessKeyId, this.accessKeySecret);
            boolean isExist = ossClient.doesObjectExist(bucketName, ffn);
            if (isExist) {
                try {
                    Thread.sleep(cnsOne);
                } catch (Exception e) {
                    log.error("###" + this.getClass().getName() + " #saveAndPushOss.File Appeared Same name,Exception"
                            + " Occurred When Thread Sleep", e);
                }
                ffn = fp + fn + usr.getName() + sf.format(new Date()) + ".xlsx";
            }

            if (StringUtils.isBlank(this.timeout)) {
                this.timeout = "1800000";
            }
            Date expiration = new Date(System.currentTimeMillis() + Long.valueOf(this.timeout));
            URL url = ossClient.generatePresignedUrl(this.bucketName, ffn, expiration);
            try {
                ossClient.putObject(this.bucketName, ffn, bis);
            } finally {
                ossClient.shutdown();
            }
            String urlStr = url.toString();
            return urlStr.replace("http", "https");
        } catch (Exception e) {
            log.error(LogUtil.format("#saveAndPushOss.Error:", "零售发货单导出"), e);
            return null;
        }
    }

    /**
     * record debug level log
     *
     * @param s information
     */
    private void recordLog(String s) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(s));
        }
    }


}
