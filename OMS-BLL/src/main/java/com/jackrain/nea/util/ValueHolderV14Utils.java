package com.jackrain.nea.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.*;

/**
 * ValueHolder返回类
 *
 * <AUTHOR>
 */
public class ValueHolderV14Utils {

    /**
     * 异常或者失败
     *
     * @param message 提示信息
     * @return holder
     */
    public static ValueHolderV14 getFailValueHolder(String message) {
        ValueHolderV14 holder = new ValueHolderV14();
        holder.setCode(ResultCode.FAIL);
        holder.setMessage(Resources.getMessage(message, new Locale("zh", "CN")));
        holder.setData(null);
        return holder;
    }

    /**
     * 成功
     *
     * @return hoder
     */
    public static ValueHolderV14 getSuccessValueHolder(String message) {
        ValueHolderV14 holder = new ValueHolderV14();
        holder.setCode(ResultCode.SUCCESS);
        holder.setMessage(Resources.getMessage(message, new Locale("zh", "CN")));
        holder.setData(null);
        return holder;
    }

    /**
     * 删除成功
     *
     * @return hoder
     */
    public static ValueHolderV14 getDeleteSuccessValueHolder() {
        ValueHolderV14 holder = new ValueHolderV14();
        holder.setCode(ResultCode.SUCCESS);
        holder.setMessage(Resources.getMessage("删除成功", new Locale("zh", "CN")));
        holder.setData(null);
        return holder;
    }

    public static ValueHolderV14 custom(Integer code, String message, Object data) {
        ValueHolderV14 holder = new ValueHolderV14();
        holder.setCode(code);
        holder.setMessage(Resources.getMessage(message, new Locale("zh", "CN")));
        holder.setData(data);
        return holder;
    }

    /**
     * 拼接json表示"列表数据左侧红色三角感叹号警告"
     *
     * @param ids     1,2
     * @param message
     * @param message "当前配货单的物流单号修改失败"
     * @return
     */
    public static ValueHolderV14 getExcuteValueHolderByWarnning(List<Long> ids
            , int code
            , String message
            , String subMessage) {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        valueHolder.setCode(code);
        valueHolder.setMessage(Resources.getMessage(message, new Locale("zh", "CN")));
        JSONArray listArray = new JSONArray();
        ids.stream().forEach(obj -> {
            JSONObject retJson = new JSONObject();
            retJson.put("code", -1);
            retJson.put("message", subMessage);
            retJson.put("objid", obj);
            listArray.add(retJson);
        });
        valueHolder.setData(listArray);
        return valueHolder;
    }

    /**
     * 列表返回封装结果
     *
     * @param arraySize 总大小
     * @param errMap    错误集合
     * @return ValueHolderV14
     */
    public static ValueHolderV14 getExcuteValueHolder(int arraySize, Map<Long, Object> errMap) {
        ValueHolderV14 holder = new ValueHolderV14();
        int errSize = errMap.size();
        if (arraySize == 1) {
            if (errSize == 0) {
                holder.setCode(ResultCode.SUCCESS);
                holder.setMessage("执行成功！");
            } else {
                // 兼容单对象和批量一条数据时直接获取错误信息
                String message = "";
                List list = new ArrayList();
                for (Map.Entry entry : errMap.entrySet()) {
                    message = entry.getValue().toString();
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("objid", entry.getKey());
                    map.put("message", entry.getValue());
                    list.add(map);
                }
                holder.setCode(ResultCode.FAIL);
                holder.setMessage(message);
                holder.setData(list);
            }
        } else {
            if (errSize == 0) {
                holder.setCode(ResultCode.SUCCESS);
                holder.setMessage("执行成功记录数：" + arraySize);
            } else {
                List list = new ArrayList();
                for (Map.Entry entry : errMap.entrySet()) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("objid", entry.getKey());
                    map.put("message", entry.getValue());
                    list.add(map);
                }
                holder.setCode(ResultCode.FAIL);
                holder.setMessage("执行成功记录数：" + (arraySize - errSize) + "，执行失败记录数：" + errSize);
                holder.setData(list);
            }
        }
        return holder;
    }
}
