package com.jackrain.nea.util;

import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.web.face.User;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Optional;

/**
 * 基础Model操作对象
 *
 * @author: 易邵峰
 * @since: 2019-03-13
 * create at : 2019-03-13 12:04
 */
public class BaseModelUtil {

    private BaseModelUtil() {

    }

    /**
     * 初始化基础对象。公用字段信息
     *
     * @param model 基础对象
     */
    public static void initialBaseModelSystemField(BaseModel model) {
        model.setCreationdate(new Date());
        model.setModifiername(SystemUserResource.ROOT_USER_NAME);
        model.setModifieddate(new Date());
        model.setModifierid(SystemUserResource.ROOT_USER_ID);

        model.setOwnerid(SystemUserResource.ROOT_USER_ID);
        model.setOwnername(SystemUserResource.ROOT_USER_NAME);
        model.setAdClientId(SystemUserResource.AD_CLIENT_ID);
        model.setAdOrgId(SystemUserResource.AD_ORG_ID);
        model.setIsactive("Y");
    }


    public static void initialBaseModelSystemField(BaseModel model, User user) {
        user = Optional.ofNullable(user).orElse(SystemUserResource.getRootUser());
        model.setCreationdate(new Date());
        model.setModifiername(user.getName());
        model.setModifieddate(new Date());
        model.setModifierid(Long.valueOf(user.getId()));
        model.setOwnerid(Long.valueOf(user.getId()));
        model.setOwnername(user.getName());
        model.setAdClientId((long) user.getClientId());
        model.setAdOrgId((long) user.getOrgId());
        model.setIsactive("Y");
    }

    public static <T extends BaseModel> T setupUpdateParam(T sourceEntity, User operator) {
        Date systemDate = new Date();
        Long operatorId = operator.getId() == null ? null : operator.getId().longValue();

        sourceEntity.setModifierid(operatorId);
        sourceEntity.setModifiername(operator.getName());
        sourceEntity.setModifieddate(systemDate);

        return sourceEntity;
    }

    public static void makeBaseCreateField(BaseModel baseModelDO, User operateUser) {
        if (operateUser != null) {
            baseModelDO.setAdOrgId((long) operateUser.getOrgId());
            if (StringUtils.isNotEmpty(operateUser.getName())) {
                baseModelDO.setOwnername(operateUser.getName());
            } else {
                baseModelDO.setOwnername(operateUser.getEname());
            }
            baseModelDO.setAdClientId((long) operateUser.getClientId());
            baseModelDO.setOwnerid(Long.valueOf(operateUser.getId()));
        }
        baseModelDO.setCreationdate(new Date());
        //是否启用
        baseModelDO.setIsactive("Y");
        //zyj,创建人跟修改人一起保存
        makeBaseModifyField(baseModelDO, operateUser);

    }

    public static void makeBaseModifyField(BaseModel baseModelDO, User operateUser) {
        if (operateUser != null) {
            baseModelDO.setModifierid(Long.valueOf(operateUser.getId()));
            if (StringUtils.isNotEmpty(operateUser.getName())) {
                baseModelDO.setModifiername(operateUser.getName());
            } else {
                baseModelDO.setModifiername(operateUser.getEname());
            }
        }
        //修改时间
        baseModelDO.setModifieddate(new Date());
    }
}
