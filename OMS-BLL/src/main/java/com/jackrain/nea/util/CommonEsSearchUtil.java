package com.jackrain.nea.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName CommonEsSearchUtil
 * @Description: 通用ES查询方法
 * <AUTHOR>
 * @Date 2020/10/22 11:49
 **/
public class CommonEsSearchUtil {

    public static List<Long> commonSearch(JSONObject whereKeys,String fileds,JSONArray orderKeys,String index,String type) {
        String[] returnFileds = {fileds};
        JSONObject search = ElasticSearchUtil.search(index, type, whereKeys, null, orderKeys, 100, 0, returnFileds);
        List<Long> orderIDs = getEsIds(fileds, search);
        return orderIDs;

    }
    public static List<Long> commonSearch(JSONObject whereKeys,String fileds,String index,String type) {
        String[] returnFileds = {fileds};
        JSONObject search = ElasticSearchUtil.search(index, type, whereKeys, null, null, 100, 0, returnFileds);
        List<Long> orderIDs = getEsIds(fileds, search);
        return orderIDs;

    }
    public static List<Long> commonSearchForIndex(JSONObject whereKeys,String fileds,String index,String type,Integer start) {
        String[] returnFileds = {fileds};
        JSONObject search = ElasticSearchUtil.search(index, type, whereKeys, null, null, 100, start, returnFileds);
        List<Long> orderIDs = getEsIds(fileds, search);
        return orderIDs;

    }

    public static List<Long> commonSearchForPage(JSONObject whereKeys, String fileds, String index, String type, JSONObject filter, JSONArray orderKeys, Integer count, int start) {
        String[] returnFileds = {fileds};
        JSONObject search = ElasticSearchUtil.search(index, type,  whereKeys, filter, orderKeys, count, (start - 1) * count, returnFileds);
        List<Long> orderIDs = getEsIds(fileds, search);
        return orderIDs;

    }
    public static List<String> commonSearchFeildsForPage(JSONObject whereKeys, String fileds, String index, String type, Integer count) {
        String[] returnFileds = {fileds};
        JSONObject search = ElasticSearchUtil.search(index, type,  whereKeys, null ,null,count, 0, returnFileds);
        List<String> orderFeilds = getEsReturnFields(fileds, search);
        return orderFeilds;

    }

    public static List<Long> getEsIds(String fileds, JSONObject search) {
        List<Long> orderIDs = new ArrayList<>();
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderIDs.add(jsonObject.getLong(fileds));
            }
        }
        return orderIDs;
    }

    public static List<String> getEsReturnFields(String fileds, JSONObject search) {
        List<String> orderFeilds = new ArrayList<>();
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderFeilds.add(jsonObject.getString(fileds));
            }
        }
        return orderFeilds;
    }
}
