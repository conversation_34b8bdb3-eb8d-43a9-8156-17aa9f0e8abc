package com.jackrain.nea.util;


import com.jackrain.nea.oc.oms.model.enums.ac.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @author:洪艺安
 * @since: 2019/7/12
 * @create at : 2019/7/12 17:20
 */
public class ACEnumUtil {
    /**
     * 应收款调整单-单据状态
     */
    public static Map<Integer, String> billStatusMap = new HashMap<>();
    /**
     * 应收款调整单-单据类型
     */
    public static Map<Integer, String> billTypeMap = new HashMap<>();
    /**
     * 应收款调整单-调整类型
     */
    public static Map<Integer, String> adjustTypeMap = new HashMap<>();
    /**
     * 支付方式
     */
    public static Map<Integer, String> payTypeMap = new HashMap<>();

    /**
     * 应付款调整单-单据状态
     */
    public static Map<Integer, String> payBillStatusMap = new HashMap<>();

    /**
     * 应付款调整单-单据类型
     */
    public static Map<Integer, String> payBillTypeMap = new HashMap<>();

    /**
     * 应付款调整单-渠道类型
     */
    public static Map<Integer, String> channelTypeMap = new HashMap<>();

    static {
        AdjustTypeEnum[] adjustTypeEnum = AdjustTypeEnum.values();
        for (AdjustTypeEnum billType : adjustTypeEnum) {
            adjustTypeMap.put(billType.getVal(), billType.getText());
        }
        BillStatusEnum[] billStatusEnum = BillStatusEnum.values();
        for (BillStatusEnum billStatus : billStatusEnum) {
            billStatusMap.put(billStatus.getVal(), billStatus.getText());
        }
        BillTypeEnum[] billTypeEnum = BillTypeEnum.values();
        for (BillTypeEnum billType : billTypeEnum) {
            billTypeMap.put(billType.getVal(), billType.getText());
        }
        PayTypeEnum[] payTypeEnum = PayTypeEnum.values();
        for (PayTypeEnum payType : payTypeEnum) {
            payTypeMap.put(payType.getVal(), payType.getText());
        }
        PayBillStatusEnum[] payBillStatusEnum = PayBillStatusEnum.values();
        for (PayBillStatusEnum payBillStatus : payBillStatusEnum) {
            payBillStatusMap.put(payBillStatus.getVal(), payBillStatus.getText());
        }
        PayBillTypeEnum[] payBillTypeEnum = PayBillTypeEnum.values();
        for (PayBillTypeEnum payBillType : payBillTypeEnum) {
            payBillTypeMap.put(payBillType.getVal(), payBillType.getText());
        }
        ChannelTypeEnum[] channelTypeEnum = ChannelTypeEnum.values();
        for (ChannelTypeEnum channelType : channelTypeEnum) {
            channelTypeMap.put(channelType.getVal(), channelType.getText());
        }
    }
}
