package com.jackrain.nea.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: hly
 * @create: 2019-09-19 14:38
 */
@Slf4j
@Component
public class JsonUtils {

    public static String json2xml(JSONObject jsonObj) {
        StringBuffer buff = new StringBuffer();
        JSONObject tempObj = null;
        JSONArray tempArr = null;
        for (String temp : jsonObj.keySet()) {
            buff.append("<" + temp.trim() + ">");
            jsonObj.get(temp);
            if (jsonObj.get(temp) instanceof JSONObject) {
                tempObj = (JSONObject) jsonObj.get(temp);
                buff.append(json2xml(tempObj));
            } else if (jsonObj.get(temp) instanceof JSONArray) {
                tempArr = (JSONArray) jsonObj.get(temp);
                if (tempArr.size() > 0) {
                    for (int i = 0; i < tempArr.size(); i++) {
                        tempObj = (JSONObject) tempArr.get(i);
                        buff.append(json2xml(tempObj));
                    }
                }
            } else {
                String tempStr = jsonObj.getString(temp);
                buff.append(tempStr == null ? "" : tempStr.trim());
            }
            buff.append("</" + temp.trim() + ">");
        }
        return buff.toString();
    }

    /**
     * 将json字符串转成对象
     *
     * @param clazz
     * @param jsonObject
     * @param <T>
     * @return
     */
    public static <T> T parseJSON(Class<T> clazz, String jsonObject) {
        T t = null;
        try {
            if (!StringUtils.isEmpty(jsonObject) && !"".equals(jsonObject)) {
                jsonObject = URLDecoder.decode(jsonObject, "utf-8");
                t = JSON.parseObject(jsonObject, clazz);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return t;
    }

    /**
     * 将JSONObject转成对象
     *
     * @param jo
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T jsonParseClass(JSONObject jo, Class<T> clazz) {
        T t = null;
        try {
            if (null != jo && !jo.isEmpty()) {
                t = JSON.toJavaObject(jo, clazz);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return t;

    }

    /**
     * 把JSON字符串解码
     *
     * @param jsonObject
     * @return
     */
    public static String decodeJson(String jsonObject) {

        try {
            return URLDecoder.decode(jsonObject, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 把对象解析成JSON字符串
     *
     * @param obj
     * @return
     */
    public static String toJsonString(Object obj) {
        return JSONObject.toJSONString(obj, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 把json - jsonArray转成list
     *
     * @param tClass
     * @param json
     * @return
     */
    public static List jsonToList(Class<?> tClass, String json) {
        List list = JSONArray.parseArray(json, tClass);
        return list;
    }

    /**
     * @param json
     * @return
     * @Author: 朱宇军
     * @Date 2019/3/27
     */
    public static List<Long> getIds(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        if (jsonObject.containsKey("value")) {
            JSONObject jsonObjectByLists = jsonObject.getJSONObject("value");
            if (jsonObjectByLists.containsKey("IN")) {
                JSONArray jsonArray = jsonObjectByLists.getJSONArray("IN");
                if (!jsonArray.isEmpty()) {
                    List<Long> list = JSONArray.parseArray(jsonArray.toJSONString(), Long.class);// 过时方法
                    return list;
                }
            }
        }
        return new ArrayList<Long>();
    }

    /**
     * String字符串转成List<Long>数据格式
     *
     * @param strArr
     * @return
     * <AUTHOR>
     * @Date 2019/4/28
     */
    public static List<Long> stringToLongList(String strArr) {
        return Arrays.stream(strArr.split(","))
                .map(s -> Long.parseLong(s.trim()))
                .collect(Collectors.toList());
    }

    /**
     * 将json对象中包含的null属性修改成""
     *
     * @param jsonObj
     */
    public static JSONObject filterNull(JSONObject jsonObj, List<String> nullKeyList) {
        Iterator<String> it = jsonObj.keySet().iterator();
        Object obj = null;
        String key = null;
        while (it.hasNext()) {
            key = it.next();
            obj = jsonObj.get(key);
            if (obj instanceof JSONObject) {
                filterNull((JSONObject) obj, nullKeyList);
            }
            if (obj instanceof JSONArray) {
                JSONArray objArr = (JSONArray) obj;
                for (int i = 0; i < objArr.size(); i++) {
                    filterNull(objArr.getJSONObject(i), nullKeyList);
                }
            }
            if (obj == null) {
                jsonObj.put(key, "");
                if (nullKeyList != null) {
                    nullKeyList.add(key);
                }
            }
        }
        return jsonObj;
    }
}
