package com.jackrain.nea.util.excel;

import java.lang.annotation.*;

/**
 * excel.annotation.table
 * setting model strategy
 *
 * @author: xiWen.z
 * create at: 2019/8/9 0009
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface XlsDBAno {

    /**
     * table order number
     */
    int index() default 99999;

    /**
     * table name
     * general same to the db tb name
     *
     * @return string
     */
    String name() default "";

    /**
     * export excel desc general use for excel name
     *
     * @return
     */
    String desc() default "";

    /**
     * sort
     * table search sort by cdt
     * e.g: 'id:desc, name:asc'
     */
    String sort() default "";

    /**
     * Strategy
     *
     * @return
     */
    XlsSt[] st();

}
