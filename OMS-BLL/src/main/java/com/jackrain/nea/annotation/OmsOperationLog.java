package com.jackrain.nea.annotation;


import java.lang.annotation.*;

/**
 * @Descroption 操作日志注解类
 * <AUTHOR>
 * @Date 2020/2/7 16:57
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public  @interface OmsOperationLog {

    /**
     * 是否元数据配置：true-是,false-否(为true时,mainTableName与itemsTableName必需加上)
     * @return
     */
    boolean configurationFlag() default true;

    /**
     * 操作类型：ADD- 新增,MOD- 修改, DEL- 删除
     * @return
     */
    String operationType() default "MOD";

    /**
     * 主表表名
     * @return
     */
    String mainTableName() default "";

    /**
     * 多个子表,表名用逗号(,)隔开
     * @return
     */
    String itemsTableName() default "";

    /**
     * 定制日志唯一识别键:推荐类名+方法名
     * @return
     */
    String customizeLogAopKey() default "";
}
