package com.jackrain.nea.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/1/2 16:06
 * @Description 店铺物流和商品物流优先级
 */
@Data
public class DistributeShopAndProLogisticsDTO implements Serializable {

    /*店铺物流*/
    /**
     * 省+市+仓+商品
     */
    private Map<Long, List<Long>> shopProvinceCityWarehouseProMap;
    /**
     * 省+市+仓+四级分类
     */
    private Map<Long, List<Long>> shopProvinceCityWarehouseDimMap;
    /**
     * 省+仓+商品
     */
    private Map<Long, List<Long>> shopProvinceWarehouseProMap;
    /**
     * 仓+商品
     */
    private Map<Long, List<Long>> shopWarehouseProMap;
    /**
     * 省+仓
     */
    private List<Long> shopProvinceWarehouseList;
    /**
     * 商品
     */
    private Map<Long, List<Long>> proMap;
    /**
     * 四级分类
     */
    private Map<Long, List<Long>> shopDimMap;
    /**
     * 仓
     */
    private List<Long> shopWarehouseList;

    /*商品物流*/

    /**
     * 平台商品ID
     */
    private Map<String, List<Long>> proPlatformProIDMap;
    /**
     * 省市区+仓+商品
     */
    private Map<Long, List<Long>> proProvinceAreaWarehouseProMap;
    /**
     * 省市区+仓+四级
     */
    private Map<Long, List<Long>> proProvinceAreaWarehouseDimMap;
    /**
     * 省+仓+商品
     */
    private Map<Long, List<Long>> proProvinceWarehouseProMap;
    /**
     * 省+仓+四级
     */
    private Map<Long, List<Long>> proProvinceWarehouseDimMap;
    /**
     * 仓+商品
     */
    private Map<Long, List<Long>> proWarehouseProMap;
    /**
     * 仓+四级
     */
    private Map<Long, List<Long>> proWarehouseDimMap;
    /**
     * 省+仓
     */
    private List<Long> proProvinceWarehouseList;
    /**
     * 仓
     */
    private List<Long> proWarehouseIdList;
    /**
     * 省市区+商品
     */
    private Map<Long, List<Long>> proProvinceAreaProMap;
    /**
     * 省市区+四级
     */
    private Map<Long, List<Long>> proProvinceAreaDimMap;
    /**
     * 省+商品
     */
    private Map<Long, List<Long>> proProvinceProMap;
    /**
     * 省+四级
     */
    private Map<Long, List<Long>> proProvinceDimMap;
    /**
     * 商品
     */
    private Map<Long, List<Long>> proProMap;
    /**
     * 四级
     */
    private Map<Long, List<Long>> proDimMap;
    /**
     * 省
     */
    private List<Long> proProvinceIdList;
    /**
     * 都没配置
     */
    private List<Long> proNoAllList;

    public DistributeShopAndProLogisticsDTO() {
        /*店铺物流*/
        shopProvinceCityWarehouseProMap = new HashMap<>();
        shopProvinceCityWarehouseDimMap = new HashMap<>();
        shopProvinceWarehouseProMap = new HashMap<>();
        shopWarehouseProMap = new HashMap<>();
        shopProvinceWarehouseList = new ArrayList<>();
        proMap = new HashMap<>();
        shopDimMap = new HashMap<>();
        shopWarehouseList = new ArrayList<>();
        /*商品物流*/
        proPlatformProIDMap = new HashMap<>();
        proProvinceAreaWarehouseProMap = new HashMap<>();
        proProvinceAreaWarehouseDimMap = new HashMap<>();
        proProvinceWarehouseProMap = new HashMap<>();
        proProvinceWarehouseDimMap = new HashMap<>();
        proWarehouseProMap = new HashMap<>();
        proWarehouseDimMap = new HashMap<>();
        proProvinceWarehouseList = new ArrayList<>();
        proWarehouseIdList = new ArrayList<>();
        proProvinceAreaProMap = new HashMap<>();
        proProvinceAreaDimMap = new HashMap<>();
        proProvinceProMap = new HashMap<>();
        proProvinceDimMap = new HashMap<>();
        proProMap = new HashMap<>();
        proDimMap = new HashMap<>();
        proProvinceIdList = new ArrayList<>();
        proNoAllList = new ArrayList<>();
    }

    /**
     * 按照固定的顺序打印日志，方便排查问题
     *
     * @return 策略优先级日志
     */
    public String getJsonBySort() {
        Map<String, Object> sortMap = new LinkedHashMap<>();
        sortMap.put("shopProvinceCityWarehouseProMap", shopProvinceCityWarehouseProMap);
        sortMap.put("shopProvinceCityWarehouseDimMap", shopProvinceCityWarehouseDimMap);
        sortMap.put("shopProvinceWarehouseProMap", shopProvinceWarehouseProMap);
        sortMap.put("shopWarehouseProMap", shopWarehouseProMap);
        sortMap.put("shopProvinceWarehouseList ", shopProvinceWarehouseList);
        sortMap.put("proMap", proMap);
        sortMap.put("shopDimMap", shopDimMap);
        sortMap.put("shopWarehouseList ", shopWarehouseList);
        sortMap.put("proPlatformProIDMap", proPlatformProIDMap);
        sortMap.put("proProvinceAreaWarehouseProMap", proProvinceAreaWarehouseProMap);
        sortMap.put("proProvinceAreaWarehouseDimMap", proProvinceAreaWarehouseDimMap);
        sortMap.put("proProvinceWarehouseProMap", proProvinceWarehouseProMap);
        sortMap.put("proProvinceWarehouseDimMap", proProvinceWarehouseDimMap);
        sortMap.put("proWarehouseProMap", proWarehouseProMap);
        sortMap.put("proWarehouseDimMap", proWarehouseDimMap);
        sortMap.put("proProvinceWarehouseList ", proProvinceWarehouseList);
        sortMap.put("proWarehouseIdList ", proWarehouseIdList);
        sortMap.put("proProvinceAreaProMap", proProvinceAreaProMap);
        sortMap.put("proProvinceAreaDimMap", proProvinceAreaDimMap);
        sortMap.put("proProvinceProMap", proProvinceProMap);
        sortMap.put("proProvinceDimMap", proProvinceDimMap);
        sortMap.put("proProMap", proProMap);
        sortMap.put("proDimMap", proDimMap);
        sortMap.put("proProvinceIdList ", proProvinceIdList);
        sortMap.put("proNoAllList ", proNoAllList);
        JSONObject jsonObject = new JSONObject(sortMap);
        return jsonObject.toString();
    }
}
