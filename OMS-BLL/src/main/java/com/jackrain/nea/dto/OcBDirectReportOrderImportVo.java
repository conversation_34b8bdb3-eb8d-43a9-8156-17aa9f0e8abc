package com.jackrain.nea.dto;

import lombok.Data;

import java.util.Map;

/**
 * 直发预占保存
 *
 * <AUTHOR>
 * @since 2026-06-06 09:27
 */
@Data
public class OcBDirectReportOrderImportVo {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    /**
     * 平台店铺编码
     */
    private String cpCShopCode;

    /**
     * 分货部门编码
     */
    private String cpCDisOrgLv2Code;

    /**
     * 配销仓编码
     */
    private String sgCSaStoreCode;

    /**
     * 逻辑仓编码
     */
    private String cpCStoreCode;

    /**
     * 预计发货时间
     */
    private String estimateConTimeStr;

    /**
     * 库存释放时间
     */
    private String autoReleaseTimeStr;

    /**
     * 商品编码
     */
    private String psCSkuEcode;

    /**
     * 数量
     */
    private String qty;

    /**
     * 开始生产时间
     */
    private String startProduceDateStr;

    /**
     * 结束生产时间
     */
    private String endProduceDateStr;


    /**
     * 行号
     */
    private int rowNum;

    /**
     * 错误信息描述
     */
    private String errorDesc;


    public static OcBDirectReportOrderImportVo importCreate(int index, Map<String, String> columnMap) {
        OcBDirectReportOrderImportVo importVo = new OcBDirectReportOrderImportVo();
        importVo.setCpCShopCode(columnMap.get("row_" + index + "cell_" + 0));
        importVo.setCpCDisOrgLv2Code(columnMap.get("row_" + index + "cell_" + 1));
        importVo.setSgCSaStoreCode(columnMap.get("row_" + index + "cell_" + 2));
        importVo.setCpCStoreCode(columnMap.get("row_" + index + "cell_" + 3));
        importVo.setEstimateConTimeStr(columnMap.get("row_" + index + "cell_" + 4));
        importVo.setAutoReleaseTimeStr(columnMap.get("row_" + index + "cell_" + 5));

        importVo.setPsCSkuEcode(columnMap.get("row_" + index + "cell_" + 6));
        importVo.setQty(columnMap.get("row_" + index + "cell_" + 7));
        importVo.setStartProduceDateStr(columnMap.get("row_" + index + "cell_" + 8));
        importVo.setEndProduceDateStr(columnMap.get("row_" + index + "cell_" + 9));

        importVo.setRowNum(index + 1);
        return importVo;
    }
}
