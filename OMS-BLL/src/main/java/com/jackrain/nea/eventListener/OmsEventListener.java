package com.jackrain.nea.eventListener;

import com.jackrain.nea.oc.oms.model.SmsSendStrategyInfo;
import com.jackrain.nea.st.service.OmsSmsSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Descroption 事件监听器
 * <AUTHOR>
 * @Date 2020/8/29
 */
@Component
@Slf4j
@EnableAsync
public class OmsEventListener {

    @Autowired
    private final Map<String, OmsSmsSendService> strategyMap = new ConcurrentHashMap<>();

    public OmsEventListener(Map<String, OmsSmsSendService> strategyMap) {
        this.strategyMap.clear();
        strategyMap.forEach((k, v) -> this.strategyMap.put(k, v));
    }

    /**
     * 短信策略发送短信处理
     *
     * @param smsSendEvent
     */
    @EventListener
    @Async("commonTaskExecutor")
    public void onApplicationEvent(SmsSendEvent smsSendEvent) {
        Object data = smsSendEvent.getData();
        if (data instanceof SmsSendStrategyInfo) {
            SmsSendStrategyInfo smsSendStrategyInfo = (SmsSendStrategyInfo) data;
            strategyMap.get(smsSendStrategyInfo.getServiceType()).execute(smsSendStrategyInfo);
        }
    }
}
