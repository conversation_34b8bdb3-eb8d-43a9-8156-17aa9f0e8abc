package com.jackrain.nea.vp.service;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.VpRpcService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2019-07-01 13:18
 * @Version 1.0
 */
@Slf4j
@Component
public class VpAddBlacklistService {

    @Autowired
    private VpRpcService vpRpcService;
    @Autowired
    private CpRpcService cpRpcService;

    public ValueHolder addBlacklist(OcBOrder ocBOrder, User user, Integer type, String remark) {
        ValueHolder holder = new ValueHolder();
        try {
//            VpBBlacklistRequest request = new VpBBlacklistRequest();
//            VpBBlacklistDO vpBBlacklistDO = new VpBBlacklistDO();
//            vpBBlacklistDO.setBuyerMobile(ocBOrder.getReceiverMobile()); //收货人手机号
//            vpBBlacklistDO.setCpCPlatformEcode(ocBOrder.getPlatform() + "");  //平台
//            String platformName = cpRpcService.queryPlatformName(ocBOrder.getPlatform() + "");
//            vpBBlacklistDO.setCpCPlatformEname(platformName);
//            vpBBlacklistDO.setCpCPlatformId(Long.valueOf(ocBOrder.getPlatform()));
//            vpBBlacklistDO.setBuyerNick(ocBOrder.getUserNick()); //用户昵称
//            vpBBlacklistDO.setReceiveName(ocBOrder.getReceiverName()); //收货人
//            vpBBlacklistDO.setCpCRegionCityEname(ocBOrder.getCpCRegionCityEname()); //市
//            vpBBlacklistDO.setCpCRegionCityEcode(ocBOrder.getCpCRegionCityEcode());
//            vpBBlacklistDO.setCpCRegionCityId(ocBOrder.getCpCRegionCityId());
//
//            vpBBlacklistDO.setCpCRegionAreaEcode(ocBOrder.getCpCRegionAreaEcode());//区
//            vpBBlacklistDO.setCpCRegionAreaEname(ocBOrder.getCpCRegionAreaEname());
//            vpBBlacklistDO.setCpCRegionAreaId(ocBOrder.getCpCRegionAreaId());
//
//            vpBBlacklistDO.setCpCRegionProvinceEcode(ocBOrder.getCpCRegionProvinceEcode());//省
//            vpBBlacklistDO.setCpCRegionProvinceEname(ocBOrder.getCpCRegionProvinceEname());
//            vpBBlacklistDO.setCpCRegionProvinceId(ocBOrder.getCpCRegionProvinceId());
//
//            vpBBlacklistDO.setReceiverAddress(ocBOrder.getReceiverAddress()); //详细地址
//
//            vpBBlacklistDO.setSourceCode(ocBOrder.getSourceCode()); //平台单号
//            vpBBlacklistDO.setVpBOrderId(ocBOrder.getId()); //订单id
//            vpBBlacklistDO.setEtype(type);
//            vpBBlacklistDO.setRemark(remark);
//            request.setVpBBlacklistDO(vpBBlacklistDO);
//
//
//            return vpRpcService.insertVpBBlacklist(user, request);
        } catch (NDSException e) {
            holder.put("code", -1);
            holder.put("message", e.getMessage());
            return holder;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用加入黑名单服务出错", e);
            holder.put("code", -1);
            holder.put("message", e.getMessage());
        }
        return holder;


    }

}
