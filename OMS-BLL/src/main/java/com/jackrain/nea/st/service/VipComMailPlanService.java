package com.jackrain.nea.st.service;

import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.MailInfoResult;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 易邵峰
 * @since: 2020-08-13
 * create at : 2020-08-13 00:25
 */
@Component
@Slf4j
public class VipComMailPlanService {
    @Autowired
    private StRpcService stRpcService;

    public MailInfoResult selectVipComMainPlan(long shopId, int taskNode) {
        //1.查询邮件模板
        ValueHolderV14<MailInfoResult> vh14 = stRpcService.querySenderByNodeAndShop(taskNode,
                shopId, null);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("querySenderByNodeAndShop,taskNode:{}, Result:{}", "selectVipComMainPlan", shopId),taskNode, vh14.toDebugString());
        }
        if (vh14.getCode() == ResultCode.FAIL) {
            log.info(LogUtil.format("查询通用JIT邮件模板失败失败信息为:{}", "查询通用JIT邮件模板失败", shopId), vh14.getMessage());
            return null;
        } else {
            return vh14.getData();
        }
    }
}
