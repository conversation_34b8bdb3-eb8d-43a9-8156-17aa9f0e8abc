package com.jackrain.nea.st.service;

/**
 * @Author: 黄世新
 * @Date: 2022/6/22 下午3:41
 * @Version 1.0
 */
public class StRedisKey {
    /**
     * 商品效期策略  指定店铺
     */
    public static final String ST_EXPIRY_DATA_SHOP_ID = "st:expiry:data:shop:id:";
    /**
     * 商品效期策略 公用
     */
    public static final String ST_EXPIRY_DATA_COMMON = "st:expiry:data:common";

    /**
     * 商品效期策略 客户分组
     */
    public static final String ST_EXPIRY_DATA_CUSTOMER_GROUP = "st:expiry:data:customer:group:";

    /**
     * 商品效期策略 会员策略
     */
    public static final String ST_EXPIRY_DATA_MEMBER_MATCH = "st:expiry:data:member:match";

    /**
     * 对等换货策略  指定店铺
     */
    public static final String ST_EQUAL_EXCHANGE_SHOP_ID = "st:equal:exchange:shop:id:";
    /**
     * 对等换货策略 公用
     */
    public static final String ST_EQUAL_EXCHANGE_COMMON = "st:equal:exchange:common";


    /**
     * 订单类型的业务类型策略
     */
    public static final String ST_BUSINESS_TYPE_MATCH_TYPE = "st:business:type:match:type:";
    /**
     * 业务类型档案id
     */
    public static final String ST_BUSINESS_TYPE_ID = "st:business:type:id:";

    /**
     * 业务类型档案id
     */
    public static final String ST_BUSINESS_TYPE_CODE = "st:business:type:code:";

    /**
     * 业务类型档案id
     */
    public static final String ST_BUSINESS_TYPE_BILL_TYPE = "st:business:type:bill:type:";

    /**
     * 订单打标
     */
    public static final String ST_ORDER_LABEL = "st:order:label:";


    public static final String ST_HOLD_ORDER_SHOP_ID = "st:hold:order:shop:id:";





}
