package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.enums.CommStatusEnum;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 单据业务类型服务类
 * @author: caomalai
 * @create: 2022-07-09 13:16
 **/
@Component
@Slf4j
public class StCBusinessTypeService {
    @Autowired
    private StCBusinessTypeService stCBusinessTypeService;
    @Autowired
    private StCBusinessTypeMapper stCBusinessTypeMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;
    /**
     * 单据业务类型保存
     * @param querySession
     * @return
     */
    @OmsOperationLog(mainTableName = "ST_C_BUSINESS_TYPE")
    public ValueHolder save(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCBusinessTypeSaveCmdImpl param：{}","单据业务类型") , param);
        }
        User user = querySession.getUser();
        Long objid = null;
        String tableName = param.getString("table");
        try{
            objid = stCBusinessTypeService.save(param,user);
        }catch (Exception e){
            if(log.isErrorEnabled()){
                log.error(LogUtil.format("新增异常"),e);
            }
            return ValueHolderUtils.fail(e.getMessage());
        }
        return ValueHolderUtils.success("新增成功！",ValueHolderUtils.createAddErrorData(tableName, objid, null));

    }


    /**
     * 保存逻辑
     * @param param
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long save(JSONObject param, User user) {
        JSONObject fixColumn = param.getJSONObject("fixcolumn");

        Long objid = param.getLong("objid");
        String tableName = param.getString("table");
        JSONObject jsonObject = fixColumn.getJSONObject(tableName);
        if (Objects.nonNull(jsonObject)) {
            StCBusinessType stCBusinessType
                    = JSONObject.parseObject(jsonObject.toJSONString(), StCBusinessType.class);
            if(StringUtils.isNotBlank(stCBusinessType.getEcode())
                    || StringUtils.isNotBlank(stCBusinessType.getEname())
                    || StringUtils.isNotBlank(stCBusinessType.getThirdCode())){
                QueryWrapper<StCBusinessType> queryWrapper = new QueryWrapper();
                queryWrapper.lambda().eq(StCBusinessType::getEcode,stCBusinessType.getEcode())
                        .or()
                        .eq(StCBusinessType::getEname,stCBusinessType.getEname())
                        .or()
                        .eq(StCBusinessType::getThirdCode,stCBusinessType.getThirdCode());
                List<StCBusinessType> stCBusinessTypes = stCBusinessTypeMapper.selectList(queryWrapper);
                if(CollectionUtils.isNotEmpty(stCBusinessTypes)){
                    List<String> codesList = stCBusinessTypes.stream()
                            .filter(p-> StringUtils.isNotBlank(p.getEcode()))
                            .map(StCBusinessType::getEcode).collect(Collectors.toList());
                    List<String> nameList = stCBusinessTypes.stream()
                            .filter(p->StringUtils.isNotBlank(p.getEname()))
                            .map(StCBusinessType::getEname).collect(Collectors.toList());
                    List<String> thirdCodeList = stCBusinessTypes.stream()
                            .filter(p->StringUtils.isNotBlank(p.getThirdCode()))
                            .map(StCBusinessType::getThirdCode).collect(Collectors.toList());
                    if(codesList.contains(stCBusinessType.getEcode())){
                        throw new NDSException("业务类型编码重复，无法保存！");
                    }
                    if(nameList.contains(stCBusinessType.getEname())){
                        throw new NDSException("业务类型名称重复，无法保存！");
                    }
                    if(thirdCodeList.contains(stCBusinessType.getThirdCode())){
                        throw new NDSException("第三方编码重复，无法保存！");
                    }
                }
            }


            //新增
            if (Objects.isNull(objid) || objid < 0) {
                Long id = ModelUtil.getSequence(tableName.toLowerCase());
                stCBusinessType.setId(id);
                StBeanUtils.makeCreateField(stCBusinessType,user);
                stCBusinessTypeMapper.insert(stCBusinessType);
                objid = id;
            } else {
                JSONObject aftervalue = param.getJSONObject("aftervalue").getJSONObject(tableName);
                jsonObject.forEach((key,value)->{
                    aftervalue.put(key,value);
                });
                aftervalue.put("ID",objid);
                aftervalue.put("MODIFIERID",Long.valueOf(user.getId()));
                aftervalue.put("MODIFIERNAME",user.getName());
                aftervalue.put("MODIFIEDDATE", DateUtils.formatDate(new Date(),"yyyy-MM-dd hh:mm:ss"));
                stCBusinessTypeMapper.updateWithNull(aftervalue);
                //删除缓存
                StCBusinessType stCBusinessType1 = stCBusinessTypeMapper.selectById(objid);
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_BUSINESS_TYPE_ID+stCBusinessType1.getId());
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_BUSINESS_TYPE_CODE+stCBusinessType1.getEcode());
            }
        }
        return objid;
    }

    /**
     * 业务单据类型删除
     * @param querySession
     * @return
     */
    public ValueHolder delete(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCBusinessTypeDeleteCmdImpl param：{}","单据业务类型") , param);
        }
        try{
            Long objid = param.getLong("objid");
            //删除缓存
            StCBusinessType stCBusinessType1 = selectOneById(objid);
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_BUSINESS_TYPE_ID+stCBusinessType1.getId());
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_BUSINESS_TYPE_CODE+stCBusinessType1.getEcode());

            stCBusinessTypeMapper.delete(new QueryWrapper<StCBusinessType>().lambda().eq(StCBusinessType::getId,objid));
        }catch (Exception e){
            if(log.isErrorEnabled()){
                log.error(LogUtil.format("删除异常"),e);
            }
            return ValueHolderUtils.fail("删除失败！");
        }
        return ValueHolderUtils.success("删除成功！");
    }

    /**
     * 通过ID查询单据业务类型
     * @param id
     * @return
     */
    public StCBusinessType selectOneById(Long id){
        StCBusinessType stCBusinessType = stCBusinessTypeMapper.selectById(id);
        return stCBusinessType;
    }
}
