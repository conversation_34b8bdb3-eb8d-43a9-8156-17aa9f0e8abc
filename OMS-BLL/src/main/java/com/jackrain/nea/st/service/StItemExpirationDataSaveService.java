package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.StItemExpirationDataMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.StItemExpirationData;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * @ClassName StItemExpirationDataSaveService
 * @Description 商品指定效期策略保存
 * <AUTHOR>
 * @Date 2022/6/9 15:40
 * @Version 1.0
 */
@Slf4j
@Component
public class StItemExpirationDataSaveService extends CommandAdapter {

    @Resource
    private StItemExpirationDataMapper stItemExpirationDataMapper;
    @Autowired
    private PsRpcService psRPCservice;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = CommandAdapterUtil.checkSaveSession(querySession, OcCommonConstant.ST_ITEM_EXPIRATION_DATE);
        if (!valueHolder.isOK()) {
            return valueHolder;
        }
        User user = querySession.getUser();
        JSONObject fixColumn = (JSONObject) valueHolder.getData().get(OcCommonConstant.FIX_COLUMN);
        log.info("fixColumn message:{}", JSONObject.toJSONString(fixColumn));
        Long id = (Long) ((HashMap) valueHolder.getData().get("data")).get(OcCommonConstant.OBJ_ID);
        StItemExpirationData stItemExpirationData = ((JSONObject) fixColumn.get(OcCommonConstant.ST_ITEM_EXPIRATION_DATE)).toJavaObject(StItemExpirationData.class);
        log.info("stItemExpirationData message:{}", JSONObject.toJSONString(stItemExpirationData));
        validate(stItemExpirationData);
        if (id < 0) {
            // 新增
            // 1 根据商品编码查询商品数据
            List<PsCPro> psCProList = psRPCservice.queryProByIds(Collections.singletonList(Math.toIntExact(stItemExpirationData.getPsCProId())));
            // 校验商品存不存在
            if (CollectionUtils.isEmpty(psCProList)) {
                throw new NDSException("商品不存在,商品编码:{}" + stItemExpirationData.getPsCProId());
            }
            PsCPro psCPro = psCProList.get(0);
            id = ModelUtil.getSequence(OcCommonConstant.ST_ITEM_EXPIRATION_DATE);
            stItemExpirationData.setId(id);
            stItemExpirationData.setPsCProEname(psCPro.getEname());
            stItemExpirationData.setPsCProId(psCPro.getId());
            stItemExpirationData.setPsCProEcode(psCPro.getEcode());
            stItemExpirationData.setOwnername(user.getEname());
            stItemExpirationData.setModifiername(user.getEname());
            stItemExpirationData.setOwnerid(Long.valueOf(user.getId()));
            stItemExpirationData.setModifierid(Long.valueOf(user.getId()));
            stItemExpirationDataMapper.insert(stItemExpirationData);
        } else {
            // 更新
            StItemExpirationData oldStItemExpirationData = stItemExpirationDataMapper.selectById(id);
            if (StringUtils.equals(oldStItemExpirationData.getIsactive(), "N")) {
                throw new NDSException("当前记录已作废，不允许编辑！");
            }
            stItemExpirationData.setModifiername(user.getEname());
            stItemExpirationData.setModifierid(Long.valueOf(user.getId()));
            stItemExpirationDataMapper.updateById(stItemExpirationData);
        }
        valueHolder = ValueHolderUtils.getSuccessValueHolder(id, OcCommonConstant.ST_ITEM_EXPIRATION_DATE, "保存成功");
        return valueHolder;
    }

    // 校验商品指定效期前端入参
    private void validate(StItemExpirationData itemExpirationData) {
//        a) 记录不存在，则提示：“当前记录已不存在！”确认返回列表页面；
//        b) 单据状态=已作废，则提示：“当前记录已作废，不允许编辑！”
//        c) 如果 结束生产日期/天数 小于 开始生产日期/天数，则提示：“结束生产日期/天数小于开始生产日期/天数，不允许！”
//        d) 如果【开始生产日期/天数】或【结束生产日期/天数】小于0，则提示：“录入的【开始生产日期/天数】或【结束生产日期/天数】不正确，不允许！”
//        e) 如果【品项】和【商品】都为空，则提示：“请正确填写【品项】或【商品】”
//        f) 如果【品项】和【商品】都不为空，则提示：“【品项】和【商品】只允许填写一项！”
//        g) 如果【品项】+【商品】已存在【可用】为是的记录，则提示：“记录已存在，不允许重复录入！”
        // todo 记录不存在，则提示：“当前记录已不存在！”确认返回列表页面；

        if (itemExpirationData.getPsCProId() == null && itemExpirationData.getPx() == null) {
            throw new NDSException("请正确填写【品项】或【商品】");
        }
        if (itemExpirationData.getPsCProId() != null && itemExpirationData.getPx() != null) {
            throw new NDSException("【品项】和【商品】只允许填写一项！");
        }
        if (itemExpirationData.getExpirationType() == null) {
            // 抛出异常
            throw new NDSException("指定类型不能为空");
        }
        // 1表示天数 2表示日期
        if (itemExpirationData.getExpirationType().equals(1)) {
            if (itemExpirationData.getExpirationStartDay() == null || itemExpirationData.getExpirationEndDay() == null) {
                throw new NDSException("指定天数不能为空");
            }
            if (itemExpirationData.getExpirationStartDay() > itemExpirationData.getExpirationEndDay()) {
                throw new NDSException("生产开始天数不能大于生产结束天数");
            }
            if (itemExpirationData.getExpirationStartDay() < 0) {
                throw new NDSException("生产开始天数不能小于0");
            }
        } else if (itemExpirationData.getExpirationType().equals(2)) {
            if (itemExpirationData.getExpirationStartTime() == null || itemExpirationData.getExpirationEndTime() == null) {
                throw new NDSException("指定时间不能为空");
            }
            if (ObjectUtils.compare(itemExpirationData.getExpirationStartTime(), itemExpirationData.getExpirationEndTime()) > 0) {
                throw new NDSException("生产开始时间不能大于生产结束时间");
            }
        }
        List<StItemExpirationData> stItemExpirationDataList = stItemExpirationDataMapper.selectList(new QueryWrapper<StItemExpirationData>().lambda()
                .eq(StItemExpirationData::getPsCProId, itemExpirationData.getPsCProId()).eq(StItemExpirationData::getPx, itemExpirationData.getPx()).eq(StItemExpirationData::getIsactive, "Y"));
        if (!CollectionUtils.isEmpty(stItemExpirationDataList)) {
            throw new NDSException("记录已存在，不允许重复录入！");
        }
    }
}
