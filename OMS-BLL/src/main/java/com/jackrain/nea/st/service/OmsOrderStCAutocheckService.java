package com.jackrain.nea.st.service;

import com.jackrain.nea.rpc.StRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 店铺自动审核策略服务
 *
 * @author: heliu
 * @since: 2019/3/27
 * create at : 2019/3/27 10:28
 */
@Component
@Slf4j
public class OmsOrderStCAutocheckService {

    @Autowired
    private StRpcService stRpcService;

    /**
     * 根据店铺查询订单退单自动审核策略
     *
     * @param shopId 店铺Id
     * @return boolean
     */
    public boolean isExitsRefundOrderStrategy(Long shopId) {
        return stRpcService.isExitsRefundOrderStrategy(shopId);
    }

    /**
     * 根据店铺id查询是否传AG
     *
     * @param id
     * @return
     */
    public boolean isToAgByShopStrategy(Long id) {
        return stRpcService.isToAgByShopStrategy(id);
    }
}

