package com.jackrain.nea.st.service;

import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCSendRuleAddressVipDo;
import com.jackrain.nea.st.model.table.StCSendRuleDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-06-17
 * @desc ST-派单方案-Dubbo服务调用
 **/
@Component
@Slf4j
public class SendPlanRuleQueryService {

    @Autowired
    private StRpcService stRpcService;

    /**
     * 查找派单方案ID集合
     *
     * @param shopId 店铺Id
     * @return
     */
    public List<Long> selectPlanByShopId(Long shopId) {
        return stRpcService.selectPlanByShopId(shopId, new Date());
    }

    /**
     * 查找派单方案的派单规则ID集合
     *
     * @param sendPlanId
     * @return
     */
    public List<Long> selectRuleById(Long sendPlanId) {
        return stRpcService.selectRuleById(sendPlanId);
    }

    /**
     * 查找派单规则
     *
     * @param sendRuleId
     * @return
     */
    public StCSendRuleDO selectSendRuleBySendRuleId(Long sendRuleId) {
        return stRpcService.selectSendRuleBySendRuleId(sendRuleId);
    }

    /**
     * 查找唯品会规则
     *
     * @param sendRuleId
     * @param cpCVipcomWahouseId
     * @return
     */
    public List<StCSendRuleAddressVipDo> selectSendRuleVip(Long sendRuleId, Long cpCVipcomWahouseId) {
        return stRpcService.selectSendRuleVip(sendRuleId, cpCVipcomWahouseId);
    }
}
