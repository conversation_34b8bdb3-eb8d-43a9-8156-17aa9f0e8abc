package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldProvinceItemMapper;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.RedisCacheUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

/**
 * @program: r3-st
 * @description: hold删除service 层
 * @author: liuwj
 * @create: 2021-04-23 14:02
 **/
@Service
public class StCHoldOrderDeleteService extends CommandAdapter {
    @Autowired
    private StCHoldOrderMapper stCHoldOrderMapper;

    @Autowired
    private StCHoldOrderItemMapper stCHoldOrderItemMapper;

    @Autowired
    private StCHoldProvinceItemMapper stCHoldProvinceItemMapper;

    @Override
    public ValueHolder execute(QuerySession session){
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(
                JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        boolean delMainFlag = param.getBoolean("isdelmtable");//是否删除主表
        Long id = param.getLong("objid");

        if (delMainFlag) {
            // 删除主表
            if (Objects.nonNull(id) && id > 0) {
                //主表删除写在这里
            }
        } else {
            // 删除明细
            JSONObject tabitem = param.getJSONObject("tabitem");
            if (!CollectionUtils.isEmpty(tabitem)) {
                JSONArray itemArray = tabitem.getJSONArray("ST_C_HOLD_ORDER_ITEM");
                JSONArray errorArray = new JSONArray();
                if (!CollectionUtils.isEmpty(itemArray)) {
                    deleteItemByID(itemArray, errorArray);
                }
                JSONArray provinceItemArray = tabitem.getJSONArray("ST_C_HOLD_PROVINCE_ITEM");
                if (!CollectionUtils.isEmpty(provinceItemArray)) {
                    deleteItemProvinceByID(provinceItemArray, errorArray);
                }
                //修改主表信息
                updateHoldOrder(id,session);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }

        throw new NDSException("当前记录已不存在！");
    }

    /**
     * <AUTHOR>
     * @Date  2021/4/23
     * @Description  修改主表信息
     */
    private void updateHoldOrder(Long id, QuerySession session) {
        StCHoldOrderDO stCHoldOrderDO =new StCHoldOrderDO();
        stCHoldOrderDO.setId(id);
        StBeanUtils.makeModifierField(stCHoldOrderDO,session.getUser());
        int n = stCHoldOrderMapper.updateById(stCHoldOrderDO);
        if (n<=0){
            throw new NDSException("修改主表的信息失败！");
        }
        StCHoldOrderDO stCHoldOrderDO1 = stCHoldOrderMapper.selectById(id);
        if (stCHoldOrderDO1 != null){
            Long shopId = stCHoldOrderDO1.getCpCShopId() == null ? null : Long.parseLong(stCHoldOrderDO1.getCpCShopId());
            RedisCacheUtil.delete(shopId, StRedisKey.ST_HOLD_ORDER_SHOP_ID);
        }


    }
    /**
     * <AUTHOR>
     * @Date 14:34 2021/4/23
     * @Description  明细删除
     */
    private void deleteItemByID(JSONArray itemArray, JSONArray errorArray) {
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            if ((stCHoldOrderItemMapper.deleteById(itemid)) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "删除明细失败"));
            }
        }
    }

    /**
     * <AUTHOR>
     * @Date 14:34 2021/4/23
     * @Description  省市区明细删除
     */
    private void deleteItemProvinceByID(JSONArray itemArray, JSONArray errorArray) {
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            if ((stCHoldProvinceItemMapper.deleteById(itemid)) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "删除省市区明细失败"));
            }
        }
    }

}
