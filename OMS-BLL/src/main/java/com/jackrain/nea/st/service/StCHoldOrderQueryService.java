package com.jackrain.nea.st.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldProvinceItemMapper;
import com.jackrain.nea.oc.oms.model.request.StCHoldOrderRequest;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderItemDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldProvinceItemDO;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/07/02 13:49
 */
@Component
@Slf4j
@Transactional
public class StCHoldOrderQueryService {

    @Autowired
    private StCHoldOrderMapper stCHoldOrderMapper;

    @Autowired
    private StCHoldOrderItemMapper stCHoldOrderItemMapper;

    @Autowired
    private StCHoldProvinceItemMapper stCHoldProvinceItemMapper;

    /**
     * 根据店铺ID查询有效的Hold策略
     * @param shopId
     * @return
     */
    public List<StCHoldOrderRequest> queryStCHoldOrderByShopId(Long shopId){
        if(shopId == null){
            return null;
        }
        List<StCHoldOrderRequest> stCHoldOrderDOList =stCHoldOrderMapper.queryStCHoldOrderByShopId(shopId);
        if (CollectionUtils.isNotEmpty(stCHoldOrderDOList)){
            for (StCHoldOrderRequest stCHoldOrderRequest : stCHoldOrderDOList) {
                List<StCHoldOrderItemDO> stCHoldOrderItemDOList = stCHoldOrderItemMapper.selectList(new QueryWrapper<StCHoldOrderItemDO>().lambda().eq(StCHoldOrderItemDO:: getHoldOrderId,stCHoldOrderRequest.getId()));
                if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOList)){
                    stCHoldOrderRequest.setStCHoldOrderItemDOList(stCHoldOrderItemDOList);
                }
                List<StCHoldProvinceItemDO> stCHoldProvinceItemDOList = stCHoldProvinceItemMapper.selectList(new QueryWrapper<StCHoldProvinceItemDO>().lambda().eq(StCHoldProvinceItemDO:: getHoldOrderId,stCHoldOrderRequest.getId()));
                if (CollectionUtils.isNotEmpty(stCHoldProvinceItemDOList)){
                    stCHoldOrderRequest.setStCHoldProvinceItemDOList(stCHoldProvinceItemDOList);
                }
            }
        }
        log.info(LogUtil.format("StCHoldOrderQueryService.调用st请求hold策略为{}"), stCHoldOrderDOList);
        return stCHoldOrderDOList;
    }



}
