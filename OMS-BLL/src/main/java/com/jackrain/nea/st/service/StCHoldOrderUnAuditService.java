package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderMapper;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.resource.RedisKeyConst;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.RedisCacheUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @ClassName StCHoldOrderUnAuditService
 * @Description 订单hold策略反审核
 * <AUTHOR>
 * @Date 2023/2/15 11:30
 * @Version 1.0
 */
@Component
@Slf4j
public class StCHoldOrderUnAuditService extends CommandAdapter {

    @Autowired
    private StCHoldOrderMapper mapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start StCHoldOrderUnAuditService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = auditArray.getLong(i);
            try {
                //4.遍历反审核方法
                unAuditStCHoldOrder(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }

    private void unAuditStCHoldOrder(Long id, QuerySession querySession) {
        StCHoldOrderDO stCHoldOrderDO = mapper.selectById(id);
        //主表校验
        if (OmsParamConstant.INT_TWO != stCHoldOrderDO.getEstatus()) {
            throw new NDSException("策略状态非已审核不允许操作！");
        }

        //更新单据状态
        StBeanUtils.makeModifierField(stCHoldOrderDO, querySession.getUser());
        stCHoldOrderDO.setEstatus(OmsParamConstant.INT_ONE);
        StBeanUtils.makeModifierField(stCHoldOrderDO, querySession.getUser());
        int updateNum = mapper.updateById(stCHoldOrderDO);
        if (updateNum < 0) {
            throw new NDSException("反审核失败！");
        }
        // 删除Redis缓存
        Long shopId = stCHoldOrderDO.getCpCShopId() == null ? null : Long.parseLong(stCHoldOrderDO.getCpCShopId());
        RedisCacheUtil.delete(shopId, RedisKeyConst.SHOP_HOLD_ORDER_ST);
        RedisCacheUtil.delete(shopId, StRedisKey.ST_HOLD_ORDER_SHOP_ID);
    }
}
