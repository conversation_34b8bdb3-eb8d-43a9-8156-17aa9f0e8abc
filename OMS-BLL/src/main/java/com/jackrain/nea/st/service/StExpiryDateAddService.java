package com.jackrain.nea.st.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateMapper;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDate;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateItem;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: 黄世新
 * @Date: 2022/6/14 上午10:17
 * @Version 1.0
 */
@Slf4j
@Component
public class StExpiryDateAddService {

    private static final int DateLength = 10;
    private static final int FIVE_HUNDRED = 500;

    @Autowired
    private StCExpiryDateMapper stCExpiryDateMapper;
    @Autowired
    private StCExpiryDateItemMapper stCExpiryDateItemMapper;
    @Autowired
    private StExpiryDateAddService stExpiryDateService;
    @Autowired
    private StExpiryDateAuditService stExpiryDateAuditService;
    @Autowired
    private StCExpiryDateLabelService stCExpiryDateLabelService;

    @OmsOperationLog(mainTableName = "ST_C_EXPIRY_DATE", itemsTableName = "ST_C_EXPIRY_DATE_ITEM")
    public ValueHolder expiryDateService(QuerySession querySession) {
        ValueHolder holder = new ValueHolder();
        try {
            DefaultWebEvent event = querySession.getEvent();
            List<String> nullKeyList = new ArrayList<>();
            JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
            log.info(LogUtil.format("StExpiryDateAddService.expiryDateService param:{}",
                    "StExpiryDateAddService.expiryDateService"), param.toJSONString());
            Long objid = param.getLong("objid");
            User user = querySession.getUser();
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            JSONObject stCExpiryDate = fixColumn.getJSONObject("ST_C_EXPIRY_DATE");
            JSONArray item = fixColumn.getJSONArray("ST_C_EXPIRY_DATE_ITEM");
            StCExpiryDate expiryDate = JsonUtils.jsonParseClass(stCExpiryDate, StCExpiryDate.class);
            List<StCExpiryDateItem> expiryDateItems = null;
            if (CollectionUtils.isNotEmpty(item)) {
                expiryDateItems = JSON.parseArray(item.toJSONString(), StCExpiryDateItem.class);
            }
            // 校验remark字段是否超过500
            if (ObjectUtil.isNotNull(expiryDate) && StringUtils.isNotEmpty(expiryDate.getRemarks()) && expiryDate.getRemarks().length() > FIVE_HUNDRED) {
                String remark = expiryDate.getRemarks();
                remark = remark.substring(0, 499);
                expiryDate.setRemarks(remark);
            }
            if (objid < 0) {
                //新增
                holder = addExpiryDate(expiryDate, expiryDateItems, user);
            } else {
                //更新
                holder = updateExpiryDate(expiryDate, expiryDateItems, objid, user);

            }
        } catch (Exception e) {
            log.error("保存异常", e);
            throw new NDSException("保存异常:" + e.getMessage());

        }
        return holder;

    }

    private ValueHolder updateExpiryDate(StCExpiryDate expiryDate, List<StCExpiryDateItem> expiryDateItems, Long objid, User user) {
        if (expiryDate != null) {
            checkMain(expiryDate, objid);
        }
        if (CollectionUtils.isNotEmpty(expiryDateItems)) {
            checkItem(expiryDateItems, objid, expiryDate);
            for (StCExpiryDateItem expiryDateItem : expiryDateItems) {
                expiryDateItem.setId(ModelUtil.getSequence("ST_C_EXPIRY_DATE_ITEM"));
                expiryDateItem.setStCExpiryDateId(objid);
                expiryDateItem.setIsactive("Y");
                BaseModelUtil.initialBaseModelSystemField(expiryDateItem, user);
            }
            stCExpiryDateItemMapper.batchInsert(expiryDateItems);
        }
        if (expiryDate != null) {
            expiryDate.setId(objid);
        } else {
            expiryDate = new StCExpiryDate();
            expiryDate.setId(objid);
        }
        BaseModelUtil.setupUpdateParam(expiryDate, user);
        stCExpiryDateMapper.updateById(expiryDate);
        return ValueHolderUtils.getSuccessValueHolder(objid, "ST_C_EXPIRY_DATE", "保存成功");
    }

    private ValueHolder addExpiryDate(StCExpiryDate expiryDate, List<StCExpiryDateItem> expiryDateItems, User user) throws ParseException {
        checkMain(expiryDate, null);
        checkItem(expiryDateItems, null, expiryDate);
        Integer expiryType = expiryDate.getExpiryType();
        expiryDate.setSubmitStatus(1);
        if (expiryType == 1) {
            expiryDate.setStartTime(new Date());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date endTime = sdf.parse("9999-12-31 23:59:59");
            expiryDate.setEndTime(endTime);
            expiryDate.setShopId(null);
        }
        Long mainId = ModelUtil.getSequence("ST_C_EXPIRY_DATE");
        expiryDate.setId(mainId);
        BaseModelUtil.initialBaseModelSystemField(expiryDate, user);
        if (CollectionUtils.isNotEmpty(expiryDateItems)) {
            for (StCExpiryDateItem expiryDateItem : expiryDateItems) {
                expiryDateItem.setId(ModelUtil.getSequence("ST_C_EXPIRY_DATE_ITEM"));
                expiryDateItem.setStCExpiryDateId(mainId);
                expiryDateItem.setIsactive("Y");
                BaseModelUtil.initialBaseModelSystemField(expiryDateItem, user);
            }
        }

        stExpiryDateService.saveData(expiryDate, expiryDateItems, false);
        return ValueHolderUtils.getSuccessValueHolder(mainId, "ST_C_EXPIRY_DATE", "保存成功");
    }

    private void checkItem(List<StCExpiryDateItem> expiryDateItems, Long objid, StCExpiryDate expiryDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (CollectionUtils.isEmpty(expiryDateItems)) {
            return;
        }
        if (expiryDate == null) {
            expiryDate = stCExpiryDateMapper.selectById(objid);
            if (expiryDate == null) {
                throw new NDSException("该商品效期策略不存在");
            }
        }
        for (StCExpiryDateItem expiryDateItem : expiryDateItems) {
            Integer appointType = expiryDateItem.getAppointType();
            if (appointType == 1 && (expiryDateItem.getStartDate() != null && expiryDateItem.getStartDate().length() < DateLength)) {
                expiryDateItem.setStartDateDay(expiryDateItem.getStartDate());
                expiryDateItem.setEndDateDay(expiryDateItem.getEndDate());
            }

            Integer appointDimension = expiryDateItem.getAppointDimension();
            if (appointDimension == null) {
                throw new NDSException("指定维度不能为空");
            }
            String appointContent = expiryDateItem.getAppointContent();
            if (StringUtils.isEmpty(appointContent)) {
                throw new NDSException("指定内容不能为空");
            }
            String startDateDay = expiryDateItem.getStartDateDay();
            String endDateDay = expiryDateItem.getEndDateDay();
            if (StringUtils.isEmpty(startDateDay) || StringUtils.isEmpty(endDateDay)) {
                throw new NDSException("【结束生产日期/天数】小于【开始生产日期/天数】，不允许！");
            }
            if (expiryDate == null) {
                expiryDate = stCExpiryDateMapper.selectById(objid);
            }
            //子表没有修改功能 一次只会过来一条
            if (objid != null) {
                int i = stCExpiryDateItemMapper.selectStCExpiryDateItem(appointDimension, appointContent, objid);
                if (i > 0) {
                    throw new NDSException("录入的明细已存在，不允许重复录入！");
                }
            }
            if (appointType == 1) {
                //指定生产日期
                Date startDate, endDate;
                try {
                    startDate = sdf.parse(startDateDay);
                    endDate = sdf.parse(endDateDay);
                } catch (Exception e) {
                    throw new NDSException("指定类型为生产日期,输入日期类型不正确");
                }
                if (endDate.getTime() < startDate.getTime()) {
                    throw new NDSException("【结束生产日期/天数】小于【开始生产日期/天数】，不允许！");
                }
            }
            if (appointType == 2) {
                int startDay, endDay;
                try {
                    startDay = Integer.parseInt(startDateDay);
                    endDay = Integer.parseInt(endDateDay);
                } catch (Exception e) {
                    throw new NDSException("指定类型天数,输入类型不正确");
                }
                if (endDay < startDay) {
                    throw new NDSException("【结束生产日期/天数】小于【开始生产日期/天数】，不允许！");
                }
            }
        }
    }

    private void checkMain(StCExpiryDate expiryDate, Long id) {
        Integer expiryType = expiryDate.getExpiryType();
        if (expiryType == null && id == null) {
            throw new NDSException("类型不能为空");
        }
        if (StringUtils.isNotEmpty(expiryDate.getRemarks()) && expiryDate.getRemarks().length() > FIVE_HUNDRED) {
            expiryDate.setRemarks(StringUtils.substring(expiryDate.getRemarks(), 0, 499));
        }

        if (id != null) {
            StCExpiryDate stCExpiryDate = stCExpiryDateMapper.selectById(id);
            if (stCExpiryDate == null) {
                throw new NDSException("当前记录不存在!");
            }
            if (stCExpiryDate.getSubmitStatus() == null || stCExpiryDate.getSubmitStatus() != 1) {
                throw new NDSException("当前策略的状态不是未审核，不允许修改！");
            }
            if (stCExpiryDate.getExpiryType() == 2) {
                Date startTime = expiryDate.getStartTime();
                Date endTime = expiryDate.getEndTime();
                if (startTime == null && endTime != null) {
                    startTime = stCExpiryDate.getStartTime();
                }
                if (startTime != null && endTime == null) {
                    endTime = stCExpiryDate.getEndTime();
                }
                boolean flag = (startTime != null & endTime != null)
                        && (startTime.getTime() > endTime.getTime()
                        || endTime.getTime() < System.currentTimeMillis());
                if (flag) {
                    throw new NDSException("付款结束时间必须大于等于付款开始时间并且付款结束时间要大于当前时间");
                }
            }
        }
        if (id == null && expiryType == 1) {
            //判断是否有有效的公用类型
            int i = stCExpiryDateMapper.selectStCExpiryDateExpiryType();
            if (i > 0) {
                throw new NDSException("存在有效的公共类型");
            }
        }
        if (id == null && expiryType == 2) {
            Long shopId = expiryDate.getShopId();
            if (shopId == null) {
                throw new NDSException("请选择平台店铺！");
            }
            Date startTime = expiryDate.getStartTime();
            Date endTime = expiryDate.getEndTime();
            if (startTime == null || endTime == null
                    || startTime.getTime() > endTime.getTime()
                    || endTime.getTime() < System.currentTimeMillis()) {
                throw new NDSException("付款结束时间必须大于等于付款开始时间并且付款结束时间要大于当前时间");
            }

        }
        if (id == null && expiryType == 3) {
            Integer customerGrouping = expiryDate.getCustomerGrouping();
            if (customerGrouping == null) {
                throw new NDSException("请输入客户分组！");
            }
            Date startTime = expiryDate.getStartTime();
            Date endTime = expiryDate.getEndTime();
            if (startTime == null || endTime == null
                    || startTime.getTime() > endTime.getTime()
                    || endTime.getTime() < System.currentTimeMillis()) {
                throw new NDSException("付款结束时间必须大于等于付款开始时间并且付款结束时间要大于当前时间");
            }

        }

    }

    @Transactional
    public void saveData(StCExpiryDate expiryDate, List<StCExpiryDateItem> expiryDateItems, boolean autoAudit) {
        if (Objects.nonNull(expiryDate)) {
            stCExpiryDateMapper.insert(expiryDate);
        }
        if (CollectionUtils.isNotEmpty(expiryDateItems)) {
            stCExpiryDateItemMapper.batchInsert(expiryDateItems);
        }

        if (autoAudit) {
            //自动审核
            User rootUser = SystemUserResource.getRootUser();
            String errorMsg = stExpiryDateAuditService.expiryDateAutoExamine(expiryDate.getId(), rootUser.getId(), rootUser.getName());
            if (StringUtils.isNotBlank(errorMsg)) {
                throw new NDSException("自动审核失败:" + errorMsg);
            }
            //发送计算汇波标签策略消息
            stCExpiryDateLabelService.sendCalculateMsg(rootUser);
        }
    }


    public String oaExpiryDatePreCheck(StCExpiryDate expiryDate) {
        try {
            checkMain(expiryDate, null);
        } catch (Exception e) {
            log.error(" oaExpiryDatePreCheck check expiryDate error,message:{}", e.getMessage(), e);
            return e.getMessage();
        }
        return "";
    }

    public Map<String, String> oaExpiryDateItemPreCheck(Map<String, StCExpiryDateItem> map) {
        Map<String, String> resultMap = Maps.newHashMap();
        List<String> repeats = Lists.newArrayList();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

        for (Map.Entry<String, StCExpiryDateItem> entry : map.entrySet()) {
            String serialNo = entry.getKey();
            StCExpiryDateItem item = entry.getValue();

            Integer appointDimension = item.getAppointDimension();
            if (appointDimension == null) {
                resultMap.put(serialNo, "指定维度不能为空");
                continue;
            }
            String appointContent = item.getAppointContent();
            if (StringUtils.isEmpty(appointContent)) {
                resultMap.put(serialNo, "指定内容不能为空");
                continue;
            }
            String startDateDay = item.getStartDateDay();
            String endDateDay = item.getEndDateDay();
            if (StringUtils.isEmpty(startDateDay) || StringUtils.isEmpty(endDateDay)) {
                resultMap.put(serialNo, "【结束生产日期/天数】小于【开始生产日期/天数】，不允许！");
                continue;
            }

            if (repeats.contains(appointDimension + appointContent)) {
                resultMap.put(serialNo, "录入的明细已存在，不允许重复录入！");
                continue;
            }

            Integer appointType = item.getAppointType();
            if (appointType == 1) {
                //指定生产日期
                Date startDate, endDate;
                try {
                    startDate = sdf.parse(startDateDay);
                    endDate = sdf.parse(endDateDay);
                } catch (Exception e) {
                    resultMap.put(serialNo, "指定类型为生产日期,输入日期类型不正确");
                    continue;
                }
                if (endDate.getTime() < startDate.getTime()) {
                    resultMap.put(serialNo, "【结束生产日期/天数】小于【开始生产日期/天数】，不允许！");
                    continue;
                }
            }
            if (appointType == 2) {
                int startDay, endDay;
                try {
                    startDay = Integer.parseInt(startDateDay);
                    endDay = Integer.parseInt(endDateDay);
                } catch (Exception e) {
                    resultMap.put(serialNo, "指定类型天数,输入类型不正确");
                    continue;
                }
                if (startDay < 0 || endDay < 0) {
                    resultMap.put(serialNo, "录入的【开始生产日期/天数】或【结束生产日期/天数】不正确，不允许！");
                    continue;
                }
                if (endDay < startDay) {
                    resultMap.put(serialNo, "【结束生产日期/天数】小于【开始生产日期/天数】，不允许！");
                    continue;
                }
            }

            repeats.add(appointDimension + appointContent);
        }
        return resultMap;
    }

}
