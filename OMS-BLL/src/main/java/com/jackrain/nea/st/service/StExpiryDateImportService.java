package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.model.request.CpCShopQueryByNameRequest;
import com.jackrain.nea.cpext.model.result.CpCShopQueryByNameInfoResult;
import com.jackrain.nea.cpext.model.result.CpCShopQueryByNameResult;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateMapper;
import com.jackrain.nea.oc.oms.model.enums.ExpiryDateTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDate;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateItem;
import com.jackrain.nea.oc.oms.util.DateFormatUtil;
import com.jackrain.nea.oc.oms.vo.StCExpiryDateImpVo;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 效期策略头明细导入
 * @author: caomalai
 * @create: 2022-08-12 13:54
 **/
@Slf4j
@Component
public class StExpiryDateImportService {
    @Reference(group = "cp-ext", version = "1.0")
    private CpShopQueryCmd cpShopQueryCmd;
    @Autowired
    private StCExpiryDateMapper stCExpiryDateMapper;
    @Autowired
    private StCExpiryDateItemMapper stCExpiryDateItemMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    public int batchSaveData(List<StCExpiryDateImpVo> validDataImpVos, User user) {
        if (log.isDebugEnabled()) {
            log.debug("## 商品效期头明细导入数据：" + JSONObject.toJSONString(validDataImpVos));
        }
        //查询仓库信息
        List<String> shopNameList = validDataImpVos.stream().filter(p-> StringUtils.isNotBlank(p.getPlatformShopName()))
                .distinct().map(StCExpiryDateImpVo::getPlatformShopName).collect(Collectors.toList());
        List<CpCShopQueryByNameInfoResult> cpCShops = new ArrayList<>();
        for(String shopName:shopNameList){
            CpCShopQueryByNameRequest request = new CpCShopQueryByNameRequest();
            request.setCpCShopEname(shopName);
            request.setUserId(new Long(user.getId()));
            ValueHolderV14<CpCShopQueryByNameResult> shopV14 = cpShopQueryCmd.queryShopByName(request);
            if(Objects.nonNull(shopV14) && Objects.nonNull(shopV14.getData())){
                List<CpCShopQueryByNameInfoResult> cpCShopList = shopV14.getData().getCpCShopList();
                if(CollectionUtils.isNotEmpty(cpCShopList)){
                    cpCShops.addAll(cpCShopList);
                }
            }
        }
        StringBuilder checkMessage = new StringBuilder();
        //用于判断公用类型开始结束时间是否一致
        String startTimeVo = "";
        String endTimeVo = "";
        List<StCExpiryDateImpVo> commonVoList = new ArrayList<>();
        boolean isCommonTimeSame = true;

        Map<String, CpCShopQueryByNameInfoResult> ShopMap = cpCShops.stream().collect(Collectors.toMap(CpCShopQueryByNameInfoResult::getCpCShopEname, Function.identity(), (key1, key2) -> key2));
        for(StCExpiryDateImpVo impVo:validDataImpVos){
            if(ExpiryDateTypeEnum.APPOINT_SHOP.getKey().equals(impVo.getExpiryType())){
                CpCShopQueryByNameInfoResult shopInfo = ShopMap.get(impVo.getPlatformShopName());
                if(Objects.nonNull(shopInfo)){
                    impVo.setPlatformShopId(shopInfo.getId());
                }else{
                    checkMessage.append("[平台店铺不存在]");
                    if(StringUtils.isNotBlank(impVo.getDesc())){
                        impVo.setDesc(impVo.getDesc()+checkMessage.toString());
                    }else{
                        impVo.setDesc(checkMessage.toString());
                    }
                    checkMessage.setLength(0);
                }
            }
            if(ExpiryDateTypeEnum.COMMUNAL.getKey().equals(impVo.getExpiryType())) {
                commonVoList.add(impVo);
                //校验导入文件中是否有不同的付款开始结束时间
                if (StringUtils.isNotBlank(startTimeVo)) {
                    if (!startTimeVo.equals(impVo.getStartTime())) {
                        isCommonTimeSame = false;
                    }
                }
                if (StringUtils.isNotBlank(endTimeVo)) {
                    if (!endTimeVo.equals(impVo.getEndTime())) {
                        isCommonTimeSame = false;
                    }
                }
                startTimeVo = impVo.getStartTime();
                endTimeVo = impVo.getEndTime();
            }
        }
        if (!isCommonTimeSame) {
            checkMessage.append("[多条公用类型付款开始时间结束时间必须保持一致]");
            for (StCExpiryDateImpVo stCExpiryDateImpVo : commonVoList) {
                if (StringUtils.isNotBlank(stCExpiryDateImpVo.getDesc())) {
                    stCExpiryDateImpVo.setDesc(stCExpiryDateImpVo.getDesc() + checkMessage.toString());
                } else {
                    stCExpiryDateImpVo.setDesc(checkMessage.toString());
                }
            }
            checkMessage.setLength(0);
        }

        Map<String, List<StCExpiryDateImpVo>> collect =
                validDataImpVos.stream().filter(p->StringUtils.isBlank(p.getDesc())).collect(Collectors.groupingBy(
                        p -> p.getExpiryType() + "_" + p.getPlatformShopId() + "_" + p.getStartTime() + "_" + p.getEndTime()));
        if (log.isDebugEnabled()) {
            log.debug("## 商品效期头明细导入数据：" + JSONObject.toJSONString(collect));
        }
        int successNum = 0;
        for (String key : collect.keySet()) {
            List<StCExpiryDate> insertList = new ArrayList<>();
            List<StCExpiryDateItem> insertItemList = new ArrayList<>();

            List<StCExpiryDateImpVo> stCExpiryDateImpVos = collect.get(key);
            String[] split = key.split("_");
            Integer expiryType = Integer.valueOf(split[0]);
            Long shopId = ExpiryDateTypeEnum.COMMUNAL.getKey().equals(expiryType)?null:Long.valueOf(split[1]);
            String startTime = split[2];
            String endTime = split[3];
            Date startDate = null;
            Date endDate = null;
            List<StCExpiryDate> existExpiryDates = stCExpiryDateMapper.selectList(new QueryWrapper<StCExpiryDate>().lambda()
                    .eq(StCExpiryDate::getExpiryType, expiryType)
                    .notIn(StCExpiryDate::getSubmitStatus,3,4)
                    .eq(StCExpiryDate::getIsactive, YesNoEnum.Y.getKey())
                    .eq(ExpiryDateTypeEnum.APPOINT_SHOP.getKey().equals(expiryType), StCExpiryDate::getShopId, shopId)
            );

            //该店铺下按指定内容所有效期策略明细
            Map<String, List<StCExpiryDateItem>> existItemMap  = null;
            if(CollectionUtils.isNotEmpty(existExpiryDates)){
                List<Long> existExpiryIds = existExpiryDates.stream().map(StCExpiryDate::getId).collect(Collectors.toList());
                List<StCExpiryDateItem> existExpiryDateItems = stCExpiryDateItemMapper.selectList(new QueryWrapper<StCExpiryDateItem>().lambda()
                        .in(StCExpiryDateItem::getStCExpiryDateId,existExpiryIds)
                        .eq(StCExpiryDateItem::getIsactive,YesNoEnum.Y.getKey())
                );
                if(CollectionUtils.isNotEmpty(existExpiryDateItems)){
                    existItemMap = existExpiryDateItems.stream().collect(Collectors.groupingBy(p -> p.getAppointContent()));
                }
            }
            if (shopId == null) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_COMMON);
            } else {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_SHOP_ID + shopId);
            }

            StCExpiryDate existMainExpiryDate = null;
            boolean isInsert = false;

            try{
                startDate = DateUtils.parseDate(startTime, DateFormatUtil.YYYY_MM_DD, "yyyy-MM-dd HH:mm:ss");
                endDate = DateUtils.parseDate(endTime, DateFormatUtil.YYYY_MM_DD, "yyyy-MM-dd HH:mm:ss");
            }catch (Exception e){
                log.error("时间转换错误");
                checkMessage.append("[付款时间转换错误]");
                for(StCExpiryDateImpVo impVo:stCExpiryDateImpVos){
                    if(StringUtils.isNotBlank(impVo.getDesc())){
                        impVo.setDesc(impVo.getDesc()+checkMessage.toString());
                    }else{
                        impVo.setDesc(checkMessage.toString());
                    }
                }
                checkMessage.setLength(0);
                continue;
            }

            if(CollectionUtils.isEmpty(existExpiryDates)){
                isInsert = true;
            }else{
                if(ExpiryDateTypeEnum.COMMUNAL.getKey().equals(expiryType)){
                    existMainExpiryDate = existExpiryDates.get(0);
                    //校验指定内容是否重复
                    if (Objects.nonNull(existItemMap) && existItemMap.size() > 0) {
                        for (StCExpiryDateImpVo stCExpiryDateImpVo : stCExpiryDateImpVos) {
                            if (existMainExpiryDate.getStartTime().getTime() != startDate.getTime() || existMainExpiryDate.getEndTime().getTime() != endDate.getTime()) {
                                checkMessage.append("[公用类型付款开始时间结束时间必须与已存在数据一致]");
                            }
                            if (CollectionUtils.isNotEmpty(existItemMap.get(stCExpiryDateImpVo.getAppointContent()))) {
                                checkMessage.append("[指定内容重复]");
                            }
                            if (checkMessage.length() > 0) {
                                if (StringUtils.isNotBlank(stCExpiryDateImpVo.getDesc())) {
                                    stCExpiryDateImpVo.setDesc(stCExpiryDateImpVo.getDesc() + checkMessage.toString());
                                } else {
                                    stCExpiryDateImpVo.setDesc(checkMessage.toString());
                                }
                                checkMessage.setLength(0);
                            }
                        }
                    }
                }else if(ExpiryDateTypeEnum.APPOINT_SHOP.getKey().equals(expiryType)){
                    for (StCExpiryDate existExpiryDate : existExpiryDates) {
                        Date existStartDate = existExpiryDate.getStartTime();
                        Date existEndDate = existExpiryDate.getEndTime();
                        if(startDate.getTime() == existStartDate.getTime()
                                &&endDate.getTime() == existEndDate.getTime()){
                            existMainExpiryDate = existExpiryDate;
                        }else{
                            //如果时间不一样，需要判断品是否时间交叉重复
                            for(StCExpiryDateImpVo stCExpiryDateImpVo:stCExpiryDateImpVos){
                                String appointContent = stCExpiryDateImpVo.getAppointContent();
                                if(Objects.nonNull(existItemMap) && existItemMap.size()>0){
                                    List<StCExpiryDateItem> stCExpiryDateItems = existItemMap.get(appointContent);
                                    if(CollectionUtils.isNotEmpty(stCExpiryDateItems)){
                                        //同品同店铺所有策略
                                        List<Long> sameShopExpiryDateId = stCExpiryDateItems.stream().map(StCExpiryDateItem::getStCExpiryDateId).distinct().collect(Collectors.toList());
                                        List<StCExpiryDate> sameShopExpiryDateList = existExpiryDates.stream().filter(p -> sameShopExpiryDateId.contains(p.getId())).collect(Collectors.toList());
                                        if(CollectionUtils.isNotEmpty(sameShopExpiryDateList)){
                                            for(StCExpiryDate sameDate:sameShopExpiryDateList){
                                                Date sameStartDate = sameDate.getStartTime();
                                                Date sameEndDate = sameDate.getEndTime();
                                                if(startDate.getTime()<sameEndDate.getTime()
                                                        && endDate.getTime()>=sameStartDate.getTime()){
                                                    checkMessage.append("[起止时间交叉重复]");
                                                    if(StringUtils.isNotBlank(stCExpiryDateImpVo.getDesc())){
                                                        stCExpiryDateImpVo.setDesc(stCExpiryDateImpVo.getDesc()+checkMessage.toString());
                                                    }else{
                                                        stCExpiryDateImpVo.setDesc(checkMessage.toString());
                                                    }
                                                    checkMessage.setLength(0);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("## 商品效期头明细导入数据：" + isInsert);
            }
            Long objid = null;
            if(isInsert){
                //插入数据
                StCExpiryDate stCExpiryDate = new StCExpiryDate();
                objid = ModelUtil.getSequence("ST_C_EXPIRY_DATE");
                stCExpiryDate.setId(objid);
                stCExpiryDate.setExpiryType(expiryType);
                stCExpiryDate.setShopId(shopId);
                stCExpiryDate.setStartTime(startDate);
                stCExpiryDate.setEndTime(endDate);
                stCExpiryDate.setSubmitStatus(1);
                StBeanUtils.makeCreateField(stCExpiryDate, user);
                insertList.add(stCExpiryDate);
            }else{
                if(StringUtils.isBlank(checkMessage)){
                    objid = existMainExpiryDate.getId();
                }else{
                    for(StCExpiryDateImpVo impVo:stCExpiryDateImpVos){
                        if(StringUtils.isNotBlank(impVo.getDesc())){
                            impVo.setDesc(impVo.getDesc()+checkMessage.toString());
                        }else{
                            impVo.setDesc(checkMessage.toString());
                        }
                    }
                    checkMessage.setLength(0);
                }

            }
            if (log.isDebugEnabled()) {
                log.debug("## 商品效期头明细导入数据：" + JSONObject.toJSONString(stCExpiryDateImpVos));
            }
            //插入明细表
            for(StCExpiryDateImpVo impVo:stCExpiryDateImpVos){
                if(StringUtils.isBlank(impVo.getDesc())){
                    StCExpiryDateItem item = new StCExpiryDateItem();
                    Long itemId = ModelUtil.getSequence("ST_C_EXPIRY_DATE_ITEM");
                    item.setId(itemId);
                    item.setAppointDimension(impVo.getAppointDimension());
                    item.setAppointContent(impVo.getAppointContent());
                    item.setAppointType(impVo.getAppointType());
                    item.setStCExpiryDateId(objid);
                    item.setEndDateDay(impVo.getEndDateDay());
                    item.setStartDateDay(impVo.getStartDateDay());
                    item.setOrderLabel(impVo.getOrderLabel());
                    // cheapestExpress如果为空则设置为0 如果是"是"则设置为1  如果是"否"则设置为0
                    if(StringUtils.isBlank(impVo.getCheapestExpress())){
                        item.setCheapestExpress(0);
                    }else{
                        if(StringUtils.equals(impVo.getCheapestExpress(),"是")){
                            item.setCheapestExpress(1);
                        }else{
                            item.setCheapestExpress(0);
                        }
                    }
                    StBeanUtils.makeCreateField(item, user);
                    insertItemList.add(item);
                }
            }
            if(CollectionUtils.isNotEmpty(insertList)){
                stCExpiryDateMapper.batchInsert(insertList);
            }
            if(CollectionUtils.isNotEmpty(insertItemList)){
                stCExpiryDateItemMapper.batchInsert(insertItemList);
            }
            successNum += insertItemList.size();
        }
        return successNum;
    }
}
