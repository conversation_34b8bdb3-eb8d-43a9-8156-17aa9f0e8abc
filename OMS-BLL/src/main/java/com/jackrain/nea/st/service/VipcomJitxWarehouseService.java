package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 唯品会JITX产能对照表操作
 *
 * @author: lan.wf
 * @since: 2020/8/17
 * create at : 2020/8/17 10:43
 */
@Component
@Slf4j
public class VipcomJitxWarehouseService {

    @Autowired
    private StRpcService stRpcService;

    /**
     * @param shopId               店铺ID
     * @param phyWarehouseId       实体仓ID
     * @param vipcomWarehouseEcode JITX仓库编码
     * @return
     */
    public StCVipcomJitxWarehouse queryJitxCapacity(Long shopId, Long phyWarehouseId, String vipcomWarehouseEcode) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查询JITX仓库对照表入参", "查询JITX仓库对照表入参", shopId, phyWarehouseId, vipcomWarehouseEcode));
        }
        StCVipcomJitxWarehouse vipcomJitxWarehouse = new StCVipcomJitxWarehouse();
        if (phyWarehouseId == null && vipcomWarehouseEcode == null) {
            return vipcomJitxWarehouse;
        }
        String redisKey = "";
        if (phyWarehouseId != null) {
            redisKey = OmsRedisKeyResources.buildStJitxWarehouseRedisKey(shopId, phyWarehouseId);
        }

        if (vipcomWarehouseEcode != null) {
            redisKey = OmsRedisKeyResources.buildStJitxWarehouseRedisKey(shopId, vipcomWarehouseEcode);
        }
        try {
            CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
            if (strRedisTemplate.hasKey(redisKey)) {
                vipcomJitxWarehouse =
                        JSON.parseObject(strRedisTemplate.opsForValue().get(redisKey),
                                StCVipcomJitxWarehouse.class);
            } else {
                vipcomJitxWarehouse = stRpcService.queryJitxCapacity(shopId, phyWarehouseId, vipcomWarehouseEcode);
                if (vipcomJitxWarehouse != null) {
                    strRedisTemplate.opsForValue().set(redisKey,
                            JSON.toJSONString(vipcomJitxWarehouse), 30L, TimeUnit.MINUTES);
                }
            }

        } catch (Exception e) {
            log.error(LogUtil.format("查询JITX仓库对照表异常", "查询JITX仓库对照表异常", shopId), Throwables.getStackTraceAsString(e));
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查询JITX仓库对照表结果{}", "查询JITX仓库对照表结果", shopId), JSON.toJSONString(vipcomJitxWarehouse));

        }
        return vipcomJitxWarehouse;
    }

    public List<StCVipcomJitxWarehouse> queryVipWarehouseListByShopId(Long shopId) {
        return stRpcService.queryVipWarehouseListByShopId(shopId);
    }

    public List<StCVipcomJitxWarehouse> queryByShopIdAndJitxUnShopWarehouseEcode(Long shopId, String jitxWarehouseEcode) {
        return stRpcService.queryByShopIdAndJitxUnShopWarehouseEcode(shopId, jitxWarehouseEcode);
    }


    public StCVipcomJitxWarehouse queryVipcomWarehouse(Long shopId, String wareCode, String type) {
        if (log.isDebugEnabled()) {
            log.debug("查询JITX仓库对照表入参，shopId：{}，wareCode：{},type:{}", shopId, wareCode, type);
        }
        StCVipcomJitxWarehouse vipcomJitxWarehouse = new StCVipcomJitxWarehouse();

        if (StringUtils.isEmpty(wareCode)) {
            return vipcomJitxWarehouse;
        }
        String redisKey = "";
        if (OmsRedisKeyResources.STORE.equals(type)) {
            redisKey = OmsRedisKeyResources.buildStJitxWarehouseRedisKeyByShopIdAndCode(shopId, wareCode, OmsRedisKeyResources.STORE);
        }

        if (OmsRedisKeyResources.WAREHOUSE.equals(type)) {
            redisKey = OmsRedisKeyResources.buildStJitxWarehouseRedisKeyByShopIdAndCode(shopId, wareCode, OmsRedisKeyResources.WAREHOUSE);
        }
        if (log.isDebugEnabled()) {
            log.debug("JITX仓库对照表 redis key:{}", redisKey);
        }
        try {
            CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
            if (strRedisTemplate.hasKey(redisKey)) {
                vipcomJitxWarehouse =
                        JSON.parseObject(strRedisTemplate.opsForValue().get(redisKey),
                                StCVipcomJitxWarehouse.class);
            } else {
                if (OmsRedisKeyResources.STORE.equals(type)) {
                    vipcomJitxWarehouse = stRpcService.queryVipcomWarehouse(shopId, wareCode, null);
                } else {
                    vipcomJitxWarehouse = stRpcService.queryVipcomWarehouse(shopId, null, wareCode);
                }
                if (vipcomJitxWarehouse != null) {
                    strRedisTemplate.opsForValue().set(redisKey,
                            JSON.toJSONString(vipcomJitxWarehouse), 1, TimeUnit.DAYS);
                }
            }

        } catch (Exception e) {
            log.error("查询JITX仓库对照表异常", Throwables.getStackTraceAsString(e));
        }

        if (log.isDebugEnabled()) {
            log.debug("查询JITX仓库对照表结果：{}", JSON.toJSONString(vipcomJitxWarehouse));
        }
        return vipcomJitxWarehouse;
    }

    /**
     * @param jitxWarehouse   JITX仓库产能配置
     * @param calculationType 操作方式：加产能/减当日产能
     * @return StCVipcomJitxWarehouse 返回产能配置
     */
    public StCVipcomJitxWarehouse updateJitxCapacity(StCVipcomJitxWarehouse jitxWarehouse, Integer calculationType) {
        return stRpcService.updateJitxCapacity(jitxWarehouse, calculationType);
    }
}
