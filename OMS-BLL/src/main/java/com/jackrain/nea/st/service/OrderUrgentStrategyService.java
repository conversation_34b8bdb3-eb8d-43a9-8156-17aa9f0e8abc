package com.jackrain.nea.st.service;

import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCOrderUrgentStrategyDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 易邵峰
 * @since: 2020-08-30
 * create at : 2020-08-30 00:09
 */
@Component
public class OrderUrgentStrategyService {

    @Autowired
    private StRpcService stRpcService;

    public StCOrderUrgentStrategyDO selectOrderUrgentStrategy(Long shopId, Integer vipLevel) {
        String redisKey = OmsRedisKeyResources.buildOrderUrgentStrategy(shopId, vipLevel);
        CusRedisTemplate<String, StCOrderUrgentStrategyDO> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
        Boolean hasKey = objRedisTemplate.hasKey(redisKey);
        if (hasKey != null && hasKey) {
            return objRedisTemplate.opsForValue().get(redisKey);
        }
        ValueHolderV14<StCOrderUrgentStrategyDO> holderV14 = stRpcService.selectOrderUrgentStrategy(shopId, vipLevel);
        if (holderV14 != null && holderV14.isOK()) {
            StCOrderUrgentStrategyDO strategyDO = holderV14.getData();
            if (strategyDO != null) {
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, strategyDO);
            }
            return strategyDO;
        } else {
            return null;
        }
    }

}
