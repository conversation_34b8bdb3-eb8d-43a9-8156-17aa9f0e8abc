package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.cpext.api.CpCPlatformQueryCmd;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMatchStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMatchStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.table.StCBusinessTypeMatchStrategy;
import com.jackrain.nea.oc.oms.model.table.StCBusinessTypeMatchStrategyItem;
import com.jackrain.nea.oc.oms.nums.StCBusinessDiscernTypeEnum;
import com.jackrain.nea.oc.oms.nums.StCBusinessStrategyStatusEnum;
import com.jackrain.nea.ps.api.PsCProdimItemQueryCmd;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.enums.CommStatusEnum;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 业务类型匹配策略服务类
 * @author: caomalai
 * @create: 2022-07-14 14:33
 **/
@Component
@Slf4j
public class StCBusinessTypeMatchStrategyService {
    @Autowired
    private StCBusinessTypeMatchStrategyService stCBusinessTypeMatchStrategyService;
    @Autowired
    private StCBusinessTypeMatchStrategyMapper stCBusinessTypeMatchStrategyMapper;
    @Autowired
    private StCBusinessTypeMatchStrategyItemMapper stCBusinessTypeMatchStrategyItemMapper;
    @Autowired
    private StCBusinessTypeService stCBusinessTypeService;
    @Reference(version = "1.0", group = "cp-ext")
    private CpCPlatformQueryCmd cpCPlatformQueryCmd;
    @Reference(version = "1.0", group = "ps")
    private PsCProdimItemQueryCmd psCProdimItemQueryCmd;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    @OmsOperationLog(mainTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY",itemsTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM")
    public ValueHolder save(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCBusinessTypeMatchStrategySaveCmdImpl param：{}", "业务类型匹配策略"), param);
        }
        User user = querySession.getUser();
        Long objid = null;
        String tableName = param.getString("table");
        try {
            objid = stCBusinessTypeMatchStrategyService.save(param, user);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(LogUtil.format("新增异常"), e);
            }
            return ValueHolderUtils.fail(e.getMessage());
        }
        return ValueHolderUtils.success("新增成功！", ValueHolderUtils.createAddErrorData(tableName, objid, null));
    }

    @Transactional(rollbackFor = Exception.class)
    public Long save(JSONObject param, User user) {
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        Long objid = param.getLong("objid");
        String tableName = param.getString("table");
        JSONObject jsonObject = fixColumn.getJSONObject(tableName);
        if (Objects.nonNull(jsonObject)) {
            StCBusinessTypeMatchStrategy stCBusinessTypeMatchStrategy
                    = JSONObject.parseObject(jsonObject.toJSONString(), StCBusinessTypeMatchStrategy.class);
            //校验业务类型唯一性
            QueryWrapper<StCBusinessTypeMatchStrategy> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StCBusinessTypeMatchStrategy::getBillType, stCBusinessTypeMatchStrategy.getBillType())
                    .eq(StCBusinessTypeMatchStrategy::getStCBusinessTypeId, stCBusinessTypeMatchStrategy.getStCBusinessTypeId())
                    .eq(StCBusinessTypeMatchStrategy::getIsactive, YesNoEnum.Y.getKey())
                    .in(StCBusinessTypeMatchStrategy::getStatus,StCBusinessStrategyStatusEnum.NON_AUDIT.getValue(),StCBusinessStrategyStatusEnum.AUDITED.getValue())
                    .ne(objid > 0, StCBusinessTypeMatchStrategy::getId, stCBusinessTypeMatchStrategy.getId());
            List<StCBusinessTypeMatchStrategy> itemList = stCBusinessTypeMatchStrategyMapper.selectList(queryWrapper);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(itemList)) {
                throw new NDSException("业务类型重复，无法保存！");
            }
            //新增
            if (Objects.isNull(objid) || objid < 0) {
                Long id = ModelUtil.getSequence(tableName.toLowerCase());
                stCBusinessTypeMatchStrategy.setId(id);
                StBeanUtils.makeCreateField(stCBusinessTypeMatchStrategy, user);
                //查询业务类型名称和编码
                StCBusinessType stCBusinessType = stCBusinessTypeService.selectOneById(stCBusinessTypeMatchStrategy.getStCBusinessTypeId());
                if (Objects.isNull(stCBusinessType)) {
                    throw new NDSException("所选业务类型不存在！");
                }
                stCBusinessTypeMatchStrategy.setStCBusinessTypeEcode(stCBusinessType.getEcode());
                stCBusinessTypeMatchStrategy.setStCBusinessTypeEname(stCBusinessType.getEname());
                stCBusinessTypeMatchStrategyMapper.insert(stCBusinessTypeMatchStrategy);
                objid = id;
            } else {
                //更新
                //主表业务校验
                StCBusinessTypeMatchStrategy existOne = stCBusinessTypeMatchStrategyMapper.selectById(objid);
                if (!existOne.getStatus().equals(StCBusinessStrategyStatusEnum.NON_AUDIT.getValue())) {
                    String descByValue = StCBusinessStrategyStatusEnum.getDescByValue(existOne.getStatus());
                    throw new NDSException("当前策略" + descByValue + "，不允许编辑！");
                }

                JSONObject afterValue = param.getJSONObject("aftervalue");
                JSONObject jsonObjectAfter = afterValue.getJSONObject(tableName);
                stCBusinessTypeMatchStrategy
                        = JSONObject.parseObject(jsonObjectAfter.toJSONString(), StCBusinessTypeMatchStrategy.class);
                stCBusinessTypeMatchStrategy.setId(objid);
                StBeanUtils.makeModifierField(stCBusinessTypeMatchStrategy, user);
                if (CommStatusEnum.YES.desc().equals(stCBusinessTypeMatchStrategy.getIsactive())) {
                    stCBusinessTypeMatchStrategy.setIsactive(CommStatusEnum.YES.charVal());
                }
                if (CommStatusEnum.NO.desc().equals(stCBusinessTypeMatchStrategy.getIsactive())) {
                    stCBusinessTypeMatchStrategy.setIsactive(CommStatusEnum.NO.charVal());
                }
                stCBusinessTypeMatchStrategyMapper.updateById(stCBusinessTypeMatchStrategy);

            }
        }

        //子表更新
        String subTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM";
        JSONArray jsonObjectItems = fixColumn.getJSONArray(subTableName);
        if (CollectionUtils.isNotEmpty(jsonObjectItems)) {
            //校验主表审核状态
            StCBusinessTypeMatchStrategy existOne = stCBusinessTypeMatchStrategyMapper.selectById(objid);
            if (!existOne.getStatus().equals(StCBusinessStrategyStatusEnum.NON_AUDIT.getValue())) {
                String descByValue = StCBusinessStrategyStatusEnum.getDescByValue(existOne.getStatus());
                throw new NDSException("当前策略" + descByValue + "，不允许编辑！");
            }

            List<StCBusinessTypeMatchStrategyItem> insertList = new ArrayList<>();
            List<StCBusinessTypeMatchStrategyItem> updateList = new ArrayList<>();
            List<StCBusinessTypeMatchStrategyItem> stCBusinessTypeMatchStrategyItems = JSONArray.parseArray(jsonObjectItems.toJSONString(), StCBusinessTypeMatchStrategyItem.class);
            Long finalObjid = objid;

            List<StCBusinessTypeMatchStrategyItem> existItems =
                    stCBusinessTypeMatchStrategyItemMapper.selectList(
                            new QueryWrapper<StCBusinessTypeMatchStrategyItem>().lambda()
                                    .eq(StCBusinessTypeMatchStrategyItem::getStCBusinessTypeMatchStrategyId, objid)
                                    .eq(StCBusinessTypeMatchStrategyItem::getIsactive, YesNoEnum.Y.getKey()));
            List<String> collect;
            if (CollectionUtils.isNotEmpty(existItems)) {
                collect = existItems.stream().map(p -> p.getDiscernContent() + p.getDiscernType() + p.getDiscernSymbol()).collect(Collectors.toList());
            } else {
                collect = new ArrayList<>();
            }
            stCBusinessTypeMatchStrategyItems.forEach(p -> {
                //业务逻辑判断
                //如果录入的【识别类型】+【运算符】+ 【识别内容】 已存在相同的记录，则提示：“录入的明细已存在，不允许重复录入！
                String content = p.getDiscernContent() + p.getDiscernType() + p.getDiscernSymbol();
                if (collect.contains(content)) {
                    throw new NDSException("录入的明细已存在，不允许重复录入！");
                }
                /**
                 f)	如果【识别类型】为【交易平台】，则判断【指定内容】在【来源平台】中是否存在“可用”状态的记录，则否则提示：“录入的交易平台不存在或不可用！”
                 g)	如果【识别类型】为【物料组】，则判断【指定内容】在商品属性的【物料组】中是否存在“可用”状态的记录，则否则提示：“录入的物料组不存在或不可用！
                 i)	如果【识别类型】为【商品实付金额】，则判断【指定内容】是否为正数或者0，则否则提示：“录入的商品实付金额必须为0或正数！
                 **/
                String discernContent = p.getDiscernContent();
                if(StringUtils.isNotBlank(discernContent)){
                    String[] split = discernContent.split(",");
                    if (Objects.equals(StCBusinessDiscernTypeEnum.PLATFORM.getValue(), p.getDiscernType())) {
                        for (int i = 0; i < split.length; i++) {
                            CpCPlatform cpCPlatform = cpCPlatformQueryCmd.queryCpCPlatformByCode(split[i]);
                            if (Objects.isNull(cpCPlatform)) {
                                throw new NDSException("录入的交易平台不存在或不可用！");
                            }

                        }
                    }
                    if (Objects.equals(StCBusinessDiscernTypeEnum.MATERIAL_GROUP.getValue(), p.getDiscernType())) {
                        List<String> groupCodes = Lists.newArrayList(split);
                        ValueHolderV14<List<PsCProdimItem>> listValueHolderV14 =
                                psCProdimItemQueryCmd.queryPsCProdimItemListByCodes(groupCodes, 2L);
                        List<String> prodimCodeList = new ArrayList<>();
                        if (Objects.nonNull(listValueHolderV14) && CollectionUtils.isNotEmpty(listValueHolderV14.getData())) {
                            List<PsCProdimItem> data = listValueHolderV14.getData();
                            prodimCodeList = data.stream().map(PsCProdimItem::getEcode).collect(Collectors.toList());
                        }
                        for (int i = 0; i < split.length; i++) {
                            if (!prodimCodeList.contains(split[i])) {
                                throw new NDSException("录入的物料组不存在或不可用！");
                            }
                        }
                    }
                    if (Objects.equals(StCBusinessDiscernTypeEnum.PAY_ACTUAL.getValue(), p.getDiscernType())) {
                        for (int i = 0; i < split.length; i++) {
                            BigDecimal bigDecimal = new BigDecimal(split[i]);
                            if (BigDecimal.ZERO.compareTo(bigDecimal) > 0) {
                                throw new NDSException("录入的商品实付金额必须为0或正数！");
                            }
                        }
                    }
                }

                if (p.getId() == null || p.getId() < 0) {
                    p.setStCBusinessTypeMatchStrategyId(finalObjid);
                    Long id = ModelUtil.getSequence(subTableName.toLowerCase());
                    p.setId(id);
                    StBeanUtils.makeCreateField(p, user);
                    insertList.add(p);
                } else {
                    StBeanUtils.makeModifierField(p, user);
                    updateList.add(p);
                }

            });
            if (CollectionUtils.isNotEmpty(insertList)) {
                stCBusinessTypeMatchStrategyItemMapper.batchInsert(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                for (StCBusinessTypeMatchStrategyItem item : updateList) {
                    stCBusinessTypeMatchStrategyItemMapper.updateById(item);
                }
            }
        }
        return objid;
    }

    /**
     * 删除
     *
     * @param querySession
     * @return
     */
    @OmsOperationLog(operationType = "DEL",mainTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY",itemsTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM")
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder delete(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCBusinessTypeMatchStrategyDeleteCmdImpl param：{}", "业务类型匹配策略"), param);
        }
        try {
            Long objid = param.getLong("objid");
            StCBusinessTypeMatchStrategy existOne = stCBusinessTypeMatchStrategyMapper.selectOne(new QueryWrapper<StCBusinessTypeMatchStrategy>()
                    .lambda().eq(StCBusinessTypeMatchStrategy::getId, objid));
            if (Objects.equals(StCBusinessStrategyStatusEnum.AUDITED.getValue(), existOne.getStatus())
                    || Objects.equals(StCBusinessStrategyStatusEnum.VOID.getValue(), existOne.getStatus())
                    || Objects.equals(StCBusinessStrategyStatusEnum.END.getValue(), existOne.getStatus())) {
                String descByValue = StCBusinessStrategyStatusEnum.getDescByValue(existOne.getStatus());
                throw new NDSException("当前策略" + descByValue + "，不允许删除！");
            }

            JSONObject tabItem = param.getJSONObject("tabitem");
            //判断是不是只删除子表
            if (Objects.nonNull(tabItem) && CollectionUtils.isNotEmpty(tabItem.getJSONArray("ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM"))) {
                JSONArray itemIds = tabItem.getJSONArray("ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM");
                List<String> itemIdList = JSONArray.parseArray(itemIds.toJSONString(), String.class);
                List<StCBusinessTypeMatchStrategyItem> itemList = stCBusinessTypeMatchStrategyItemMapper.selectBatchIds(itemIdList);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    Map<Long,String> beforeDelObjMap = new HashMap<>();
                    for (StCBusinessTypeMatchStrategyItem item : itemList) {
                        beforeDelObjMap.put(item.getId(),JSON.toJSONString(item));
                    }
                    querySession.setAttribute("beforeDelObjMap",beforeDelObjMap);
                }
                stCBusinessTypeMatchStrategyItemMapper.deleteBatchIds(itemIdList);
            } else {
                //删除主表
                stCBusinessTypeMatchStrategyMapper.deleteById(objid);
                //删除关联的全部子表
                QueryWrapper<StCBusinessTypeMatchStrategyItem> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(StCBusinessTypeMatchStrategyItem::getStCBusinessTypeMatchStrategyId, objid);
                stCBusinessTypeMatchStrategyItemMapper.delete(queryWrapper);
            }
            Integer billType = existOne.getBillType();
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_BUSINESS_TYPE_MATCH_TYPE+billType);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(LogUtil.format("删除异常"), e);
            }
            return ValueHolderUtils.fail("删除失败！");
        }
        return ValueHolderUtils.success("删除成功！");
    }

    /**
     * 提交
     *
     * @param querySession
     * @return
     */
    @OmsOperationLog(operationType = "AUDIT",mainTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY",itemsTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM")
    public ValueHolder submit(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCBusinessTypeMatchStrategySubmitCmdImpl param：{}", "业务类型匹配策略"), param);
        }
        User user = querySession.getUser();
        Long objid = param.getLong("objid");
        try {
            StCBusinessTypeMatchStrategy existOne = stCBusinessTypeMatchStrategyMapper.selectOne(new QueryWrapper<StCBusinessTypeMatchStrategy>()
                    .lambda().eq(StCBusinessTypeMatchStrategy::getId, objid));
            if (!Objects.equals(StCBusinessStrategyStatusEnum.NON_AUDIT.getValue(), existOne.getStatus())) {
                throw new NDSException("当前策略不是未审核状态，不允许审核！");
            }
            List<StCBusinessTypeMatchStrategyItem> existItems = stCBusinessTypeMatchStrategyItemMapper.selectList(
                    new QueryWrapper<StCBusinessTypeMatchStrategyItem>()
                            .lambda()
                            .eq(StCBusinessTypeMatchStrategyItem::getStCBusinessTypeMatchStrategyId, objid)
                            .eq(StCBusinessTypeMatchStrategyItem::getIsactive, YesNoEnum.Y.getKey()));
            if (CollectionUtils.isEmpty(existItems)) {
                throw new NDSException("请维护策略明细！");
            }
            StCBusinessTypeMatchStrategy p = new StCBusinessTypeMatchStrategy();
            p.setId(objid);
            p.setStatus(StCBusinessStrategyStatusEnum.AUDITED.getValue());
            StBeanUtils.makeModifierField(p, user);
            p.setSubmitId(Long.valueOf(user.getId()));
            p.setSubmitName(user.getName());
            p.setSubmitDate(new Date());
            stCBusinessTypeMatchStrategyMapper.updateById(p);
            Integer billType = existOne.getBillType();
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_BUSINESS_TYPE_MATCH_TYPE+billType);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(LogUtil.format("审核异常"), e);
            }
            return ValueHolderUtils.fail("审核失败！");
        }
        return ValueHolderUtils.success("审核成功！");
    }


    /**
     * 反提交
     *
     * @param querySession
     * @return
     */
    @OmsOperationLog(operationType = "RESERVE_AUDIT",mainTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY",itemsTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM")
    public ValueHolder submitBack(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCBusinessTypeMatchStrategySubmitBackCmdImpl param：{}", "业务类型匹配策略"), param);
        }
        User user = querySession.getUser();
        Long objid = param.getLong("objid");
        try {
            StCBusinessTypeMatchStrategy existOne = stCBusinessTypeMatchStrategyMapper.selectOne(new QueryWrapper<StCBusinessTypeMatchStrategy>()
                    .lambda().eq(StCBusinessTypeMatchStrategy::getId, objid));
            if (!Objects.equals(StCBusinessStrategyStatusEnum.AUDITED.getValue(), existOne.getStatus())) {
                throw new NDSException("当前策略不是已审核状态，不允许反审核！");
            }

            StCBusinessTypeMatchStrategy p = new StCBusinessTypeMatchStrategy();
            p.setId(objid);
            p.setStatus(StCBusinessStrategyStatusEnum.NON_AUDIT.getValue());
            StBeanUtils.makeModifierField(p, user);
            p.setSubmitId(Long.valueOf(user.getId()));
            p.setSubmitName(user.getName());
            p.setSubmitDate(new Date());
            stCBusinessTypeMatchStrategyMapper.updateById(p);
            Integer billType = existOne.getBillType();
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_BUSINESS_TYPE_MATCH_TYPE+billType);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(LogUtil.format("反审核异常"), e);
            }
            return ValueHolderUtils.fail("反审核失败！");
        }
        return ValueHolderUtils.success("反审核成功！");
    }

    /**
     * 作废
     *
     * @param querySession
     * @return
     */
    @OmsOperationLog(operationType = "VOID",mainTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY",itemsTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM")
    public ValueHolder cancel(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCBusinessTypeMatchStrategyVoidCmdImpl param：{}", "业务类型匹配策略"), param);
        }
        User user = querySession.getUser();
        Long objid = param.getLong("objid");
        try {
            StCBusinessTypeMatchStrategy existOne = stCBusinessTypeMatchStrategyMapper.selectOne(new QueryWrapper<StCBusinessTypeMatchStrategy>()
                    .lambda().eq(StCBusinessTypeMatchStrategy::getId, objid));
            if (!Objects.equals(StCBusinessStrategyStatusEnum.NON_AUDIT.getValue(), existOne.getStatus())) {
                throw new NDSException("当前策略不是未审核状态，不允许作废！");
            }

            StCBusinessTypeMatchStrategy p = new StCBusinessTypeMatchStrategy();
            p.setId(objid);
            p.setStatus(StCBusinessStrategyStatusEnum.VOID.getValue());
            p.setIsactive(YesNoEnum.N.getKey());
            StBeanUtils.makeModifierField(p, user);
            stCBusinessTypeMatchStrategyMapper.updateById(p);
            Integer billType = existOne.getBillType();
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_BUSINESS_TYPE_MATCH_TYPE+billType);
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(LogUtil.format("作废异常"), e);
            }
            return ValueHolderUtils.fail("作废失败！");
        }
        return ValueHolderUtils.success("作废成功！");

    }

    /**
     * 结案
     *
     * @param querySession
     * @return
     */
    @OmsOperationLog(operationType = "FINISH",mainTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY",itemsTableName = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM")
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder end(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCBusinessTypeMatchStrategyEndCmdImpl param：{}", "业务类型匹配策略"), param);
        }
        User user = querySession.getUser();
        try {
            JSONArray ids = param.getJSONArray("ids");
            if (CollectionUtils.isEmpty(ids)) {
                throw new NDSException("请选择一条数据！");
            }
            for (int i = 0; i < ids.size(); i++) {
                String objid = (String) ids.get(i);
                StCBusinessTypeMatchStrategy existOne = stCBusinessTypeMatchStrategyMapper.selectOne(new QueryWrapper<StCBusinessTypeMatchStrategy>()
                        .lambda().eq(StCBusinessTypeMatchStrategy::getId, Long.valueOf(objid)));
                if (!Objects.equals(StCBusinessStrategyStatusEnum.AUDITED.getValue(), existOne.getStatus())) {
                    throw new NDSException("当前策略的状态不是已审核，不允许结案！");
                }

                StCBusinessTypeMatchStrategy p = new StCBusinessTypeMatchStrategy();
                p.setId(Long.valueOf(objid));
                p.setStatus(StCBusinessStrategyStatusEnum.END.getValue());
                StBeanUtils.makeModifierField(p, user);
                p.setEndId(Long.valueOf(user.getId()));
                p.setEndName(user.getName());
                p.setEndDate(new Date());
                stCBusinessTypeMatchStrategyMapper.updateById(p);
                Integer billType = existOne.getBillType();
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_BUSINESS_TYPE_MATCH_TYPE+billType);
            }

        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(LogUtil.format("结案异常"), e);
            }
            return ValueHolderUtils.fail(e.getMessage());
        }
        return ValueHolderUtils.success("结案成功！");

    }
}
