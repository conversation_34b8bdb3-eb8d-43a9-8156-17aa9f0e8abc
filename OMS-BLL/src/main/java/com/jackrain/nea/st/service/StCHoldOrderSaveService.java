package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.CommonIdempotentMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderReasonMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldProvinceItemMapper;
import com.jackrain.nea.oc.oms.model.enums.CommonIdempotentTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldEnum;
import com.jackrain.nea.oc.oms.model.request.StCHoldOrderRequest;
import com.jackrain.nea.oc.oms.model.table.CommonIdempotent;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderItemDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import com.jackrain.nea.oc.oms.model.table.StCHoldProvinceItemDO;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.resource.RedisKeyConst;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.RpcPsService;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.util.RedisCacheUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/07/02 13:49
 */
@Component
@Slf4j
@Transactional
public class StCHoldOrderSaveService extends CommandAdapter {

    @Autowired
    private StCHoldOrderMapper stCHoldOrderMapper;

    @Autowired
    private RpcPsService rpcPsService;

    @Autowired
    private StCHoldOrderItemMapper stCHoldOrderItemMapper;

    @Autowired
    private StCHoldProvinceItemMapper stCHoldProvinceItemMapper;

    @Autowired
    private CommonIdempotentMapper commonIdempotentMapper;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private StCHoldOrderReasonMapper stCHoldOrderReasonMapper;


    @Override
    public ValueHolder execute(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        List<String> nullKeyList = new ArrayList<>();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
        if (param != null) {
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            log.debug(LogUtil.format("fixColumn：{}"), JSON.toJSONString(fixColumn));
            log.info(LogUtil.format("StCHoldOrderSaveService.execute querySession:{}"), session);
            StCHoldOrderRequest stCHoldOrderRequest = JsonUtils.jsonParseClass(fixColumn, StCHoldOrderRequest.class);

            String idsStr = param.getString("objids");
            if(StringUtils.isNotBlank(idsStr)){
                String[] split = idsStr.split(",");
                ValueHolder valueHolder = null;
                for(String idObj : split){
                    if(idObj!=null){
                        Long id = Long.parseLong(idObj);
                        valueHolder = this.updateStCHoldOrder(session, stCHoldOrderRequest, id);
                        if(valueHolder==null || !valueHolder.isOK()){
                            return valueHolder;
                        }
                    }
                }
                return valueHolder;
            }else{
                Long id = param.getLong("objid");
                if (fixColumn != null && id != null) {
                    if (id != -1) {
                        return this.updateStCHoldOrder(session, stCHoldOrderRequest, id);
                    } else {
                        return this.insertStCHoldOrder(session, stCHoldOrderRequest);
                    }
                }
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private ValueHolder insertStCHoldOrder(QuerySession session, StCHoldOrderRequest stCHoldOrderRequest) {

        long id = 0;
        //1.HOLD单策略表
        StCHoldOrderDO stCHoldOrderDO = stCHoldOrderRequest.getStCHoldOrder();
        if (stCHoldOrderDO != null) {
            //1.1 判断名称是否已存在
            ValueHolder check = check(-1L, stCHoldOrderRequest, "insert");
            if (check != null) {
                return check;
            }
            //1.2 插入
            id = ModelUtil.getSequence("ST_C_HOLD_ORDER");
            stCHoldOrderDO.setId(id);
            stCHoldOrderDO.setEstatus(1);
            StBeanUtils.makeCreateField(stCHoldOrderDO, session.getUser());
            int insertResult = stCHoldOrderMapper.insert(stCHoldOrderDO);
            if (insertResult < 0) {
                return ValueHolderUtils.getFailValueHolder("保存失败！");
            }
            // 删除Redis缓存
            Long shopId = stCHoldOrderDO.getCpCShopId() == null ? null : Long.parseLong(stCHoldOrderDO.getCpCShopId());
            RedisCacheUtil.delete(shopId, RedisKeyConst.SHOP_HOLD_ORDER_ST);
            RedisCacheUtil.delete(shopId, StRedisKey.ST_HOLD_ORDER_SHOP_ID);
        }
        //新增明细
        List<StCHoldOrderItemDO> stCHoldOrderItemDOList = stCHoldOrderRequest.getStCHoldOrderItemDOList();
        log.info(LogUtil.format("StCHoldOrderSaveService.insertStCHoldOrder param stCHoldOrderItemDOList:{}"),
                stCHoldOrderItemDOList);
        if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOList)){
            //校验
            for (StCHoldOrderItemDO itemDO : stCHoldOrderItemDOList) {
                if (OrderHoldEnum.MOBILE_ORDER_NUM.getKey().equals(itemDO.getRulesRecognition()) || OrderHoldEnum.ADDRESS_ORDER_NUM.getKey().equals(itemDO.getRulesRecognition())) {
                    String content = itemDO.getContent();
                    if (!StringUtils.isNumeric(content) || Integer.parseInt(content) <= 0) {
                        return ValueHolderUtils.getFailValueHolder("请填写正确的识别内容!");
                    }
                }
            }

            List<StCHoldOrderItemDO> updateList = new ArrayList<>();
            List<StCHoldOrderItemDO> insterList = new ArrayList<>();
            long holdOrderId = id;
            stCHoldOrderItemDOList.forEach(i->{
                i.setHoldOrderId(holdOrderId);
                setFieldContent(i);
                if(Objects.nonNull(i.getId()) &&i.getId()>0){
                    StBeanUtils.makeModifierField(i,session.getUser());
                    updateList.add(i);
                }else {
                    i.setId(ModelUtil.getSequence("ST_C_HOLD_ORDER_ITEM"));
                    StBeanUtils.makeCreateField(i,session.getUser());
                    insterList.add(i);
                }
            });
            Boolean flag = holdOrderItemSave(updateList,insterList);
            if (!flag){
                return ValueHolderUtils.getFailValueHolder("保存明细失败！");
            }
        }
        //新增省市区明细
        List<StCHoldProvinceItemDO> stCHoldProvinceItemDOList = stCHoldOrderRequest.getStCHoldProvinceItemDOList();
        log.info(LogUtil.format("StCHoldOrderSaveService.insertStCHoldOrder param stCHoldOrderItemDOList:{}"),
                stCHoldOrderItemDOList);
        //省市区
        if (CollectionUtils.isNotEmpty(stCHoldProvinceItemDOList)){
            List<StCHoldProvinceItemDO> updateList = new ArrayList<>();
            List<StCHoldProvinceItemDO> insterList = new ArrayList<>();
            long holdOrderId = id;
            stCHoldProvinceItemDOList.forEach(i->{
                i.setHoldOrderId(holdOrderId);
                if(Objects.nonNull(i.getId()) &&i.getId()>0){
                    StBeanUtils.makeModifierField(i,session.getUser());
                    updateList.add(i);
                }else {
                    i.setId(ModelUtil.getSequence("ST_C_HOLD_PROVINCE_ITEM"));
                    StBeanUtils.makeCreateField(i,session.getUser());
                    insterList.add(i);
                }
            });
            Boolean flag = holdProvinceItemSave(updateList,insterList);
            if (!flag){
                return ValueHolderUtils.getFailValueHolder("保存明细失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, "ST_C_HOLD_ORDER", "");
    }
    /**
     * <AUTHOR>
     * @Date 15:06 2021/9/3
     * @Description
     */
    private Boolean holdProvinceItemSave(List<StCHoldProvinceItemDO> updateList, List<StCHoldProvinceItemDO> insterList) {
        log.info(LogUtil.format("StCHoldOrderSaveService.holdProvinceItemSave param updateList:{},insterList:{}"),
                updateList, insterList);
        int n=0;
        if (CollectionUtils.isNotEmpty(updateList)){
            for (StCHoldProvinceItemDO stCHoldProvinceItemDO: updateList) {
                    n+= stCHoldProvinceItemMapper.updateById(stCHoldProvinceItemDO);
            }
        }
        if (CollectionUtils.isNotEmpty(insterList)){
            for (StCHoldProvinceItemDO stCHoldProvinceItemDO : insterList) {
                    n+= stCHoldProvinceItemMapper.insert(stCHoldProvinceItemDO);
                }
            }
        if (n>0){
            return true;
        }
        return false;
    }

    /**
     * <AUTHOR>
     * @Date 15:45 2021/4/22
     * @Description  给识别内容赋值 调用rpc
     */
    private void setFieldContent(StCHoldOrderItemDO i) {
        if (Objects.nonNull(i.getCategoriesId()) || Objects.nonNull(i.getInTheClassId()) ||Objects.nonNull(i.getSmallClassId())
                ||Objects.nonNull(i.getGoodsCodeId()) ||Objects.nonNull(i.getGoodsSkuId()) ||Objects.nonNull(i.getPtSkuId()) ||Objects.nonNull(i.getPtSpuId())){
            //查询商品属性大类rpc
            if (Objects.nonNull(i.getCategoriesId())){
                PsCProdimItem psCProdimItem =rpcPsService.queryPsCProDimItem(i.getCategoriesId());
                if (psCProdimItem!=null){
                    i.setContentId(i.getCategoriesId());
//                    i.setContent(psCProdimItem.getEname());
                }
                return;
            }
            //查询商品属性中类rpc
            if (Objects.nonNull(i.getInTheClassId())){
                PsCProdimItem psCProdimItem =rpcPsService.queryPsCProDimItem(i.getInTheClassId());
                if (psCProdimItem!=null){
                    i.setContentId(i.getInTheClassId());
                    i.setContent(psCProdimItem.getEname());
                }
                return;
            }
            //查询商品属性小类rpc
            if (Objects.nonNull(i.getSmallClassId())){
                PsCProdimItem psCProdimItem =rpcPsService.queryPsCProDimItem(i.getSmallClassId());
                if (psCProdimItem!=null){
                    i.setContentId(i.getSmallClassId());
                    i.setContent(psCProdimItem.getEname());
                }
                return;
            }
            //查询商品编码rpc
            if (Objects.nonNull(i.getGoodsCodeId())){
                PsCPro psCPro =rpcPsService.queryProByIds(i.getGoodsCodeId());
                if (psCPro!=null){
                    i.setContentId(i.getGoodsCodeId());
                    i.setContent(psCPro.getEcode());
                }
                return;
            }
            //查询商品skurpc
            if (Objects.nonNull(i.getGoodsSkuId())){
                PsCSku psCSku =rpcPsService.getSkuById(i.getGoodsSkuId());
                if (psCSku!=null){
                    i.setContentId(i.getGoodsSkuId());
                    i.setContent(psCSku.getEcode());
                }
                return;
            }
            //平台skuid
            if (Objects.nonNull(i.getPtSkuId())){
                i.setContent(i.getPtSkuId());
                return;
            }
            //中台skuid
            if (Objects.nonNull(i.getPtSpuId())){
                i.setContent(i.getPtSpuId());
                return;
            }
        }
    }

    /**
     * <AUTHOR>
     * @Date 15:38 2021/4/22
     * @Description  明细新增修改 或者即新增又修改
     */
    private Boolean holdOrderItemSave(List<StCHoldOrderItemDO> updateList, List<StCHoldOrderItemDO> insterList) {
        log.info(LogUtil.format("StCHoldOrderSaveService.HoldOrderItemSave param updateList:{},insterList:{}"),
                updateList, insterList);
        int n=0;
        if (CollectionUtils.isNotEmpty(updateList)){
            for (StCHoldOrderItemDO stCHoldOrderItemDO : updateList) {
                if (checkHoldOrderItem(stCHoldOrderItemDO)){
                    n+= stCHoldOrderItemMapper.updateById(stCHoldOrderItemDO);
                }else {
                    throw new NDSException("已存在相同数据，【识别规则】:"+parsFieldByRule(stCHoldOrderItemDO.getRulesRecognition())+"");
                }
            }

        }
        if (CollectionUtils.isNotEmpty(insterList)){
            for (StCHoldOrderItemDO stCHoldOrderItemDO : insterList) {
                if (checkHoldOrderItem(stCHoldOrderItemDO)){
                    n+= stCHoldOrderItemMapper.insert(stCHoldOrderItemDO);
                }else {
                    throw new NDSException("已存在相同数据，【识别规则】:"+parsFieldByRule(stCHoldOrderItemDO.getRulesRecognition())+"");
                }
            }

        }
        if (n>0){
            return true;
        }
        return false;
    }
    /**
     * <AUTHOR>
     * @Date 18:04 2021/4/22
     * @Description 翻译识别规则
     */
    private String parsFieldByRule(String key) {
       return StConstant.HOLD_ORDER_RULES_RECOGNITION.get(key);
    }

    /**
     * <AUTHOR>
     * @Date 16:11 2021/4/22
     * @Description 校验明细 【识别规则】以及【识别内容】不允许同时相同，否则提示
     */
    private boolean checkHoldOrderItem(StCHoldOrderItemDO stCHoldOrderItemDO) {
        if (Objects.nonNull(stCHoldOrderItemDO) && Objects.nonNull(stCHoldOrderItemDO.getRulesRecognition()) && Objects.nonNull(stCHoldOrderItemDO.getContentId())) {
            if (OrderHoldEnum.MOBILE_ORDER_NUM.getKey().equals(stCHoldOrderItemDO.getRulesRecognition())
                    || OrderHoldEnum.ADDRESS_ORDER_NUM.getKey().equals(stCHoldOrderItemDO.getRulesRecognition())) {
                int n = stCHoldOrderItemMapper.selectCount(new QueryWrapper<StCHoldOrderItemDO>().lambda()
                        .eq(StCHoldOrderItemDO::getRulesRecognition, stCHoldOrderItemDO.getRulesRecognition())
                        .eq(StCHoldOrderItemDO::getContentId, stCHoldOrderItemDO.getContentId())
                        .eq(StCHoldOrderItemDO::getHoldOrderId, stCHoldOrderItemDO.getHoldOrderId()));
                if (n > 0) {
                    return false;
                }
            } else {
                int n = stCHoldOrderItemMapper.selectCount(new QueryWrapper<StCHoldOrderItemDO>().lambda()
                        .eq(StCHoldOrderItemDO::getRulesRecognition, stCHoldOrderItemDO.getRulesRecognition())
                        .eq(StCHoldOrderItemDO::getContent, stCHoldOrderItemDO.getContent())
                        .eq(StCHoldOrderItemDO::getHoldOrderId, stCHoldOrderItemDO.getHoldOrderId()));
                if (n > 0) {
                    return false;
                }
            }
        }
        return true;
    }

    private ValueHolder updateStCHoldOrder(QuerySession session, StCHoldOrderRequest stCHoldOrderRequest, Long id) {
        StCHoldOrderDO stCHoldOrderDO = stCHoldOrderRequest.getStCHoldOrder();
        if (stCHoldOrderDO != null) {
            ValueHolder holder = check(id, stCHoldOrderRequest, "update");
            if (holder != null) {
                return holder;
            }
            //1.订单HOLD单策略主表处理
            stCHoldOrderDO.setId(id);
            StBeanUtils.makeModifierField(stCHoldOrderDO, session.getUser());
            log.debug(LogUtil.format("stCHoldOrderDO：{}"), JSON.toJSONString(stCHoldOrderDO));
            if (stCHoldOrderMapper.updateById(stCHoldOrderDO) < 0) {
                return ValueHolderUtils.getFailValueHolder("更新失败！");
            }
            StCHoldOrderDO holdOrderDO = stCHoldOrderMapper.selectById(id);
            // 删除Redis缓存
            Long shopId = holdOrderDO.getCpCShopId() == null ? null : Long.parseLong(holdOrderDO.getCpCShopId());
            RedisCacheUtil.delete(shopId, RedisKeyConst.SHOP_HOLD_ORDER_ST);
            RedisCacheUtil.delete(shopId, StRedisKey.ST_HOLD_ORDER_SHOP_ID);
        }
        //修改明细
        List<StCHoldOrderItemDO> stCHoldOrderItemDOList = stCHoldOrderRequest.getStCHoldOrderItemDOList();
        log.info(LogUtil.format("StCHoldOrderSaveService.updateStCHoldOrder param stCHoldOrderItemDOList:{}"),
                stCHoldOrderItemDOList);
        if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOList)){
            //校验
            for (StCHoldOrderItemDO itemDO : stCHoldOrderItemDOList) {
                if (OrderHoldEnum.MOBILE_ORDER_NUM.getKey().equals(itemDO.getRulesRecognition()) || OrderHoldEnum.ADDRESS_ORDER_NUM.getKey().equals(itemDO.getRulesRecognition())) {
                    String content = itemDO.getContent();
                    if (!StringUtils.isNumeric(content) || Integer.parseInt(content) <= 0) {
                        return ValueHolderUtils.getFailValueHolder("请填写正确的识别内容!");
                    }
                }
            }

            List<StCHoldOrderItemDO> updateList = new ArrayList<>();
            List<StCHoldOrderItemDO> insterList = new ArrayList<>();
            long holdOrderId = id;
            stCHoldOrderItemDOList.forEach(i->{
                i.setHoldOrderId(holdOrderId);
                setFieldContent(i);
                if(Objects.nonNull(i.getId()) &&i.getId()>0){
                    StBeanUtils.makeModifierField(i,session.getUser());
                    updateList.add(i);
                }else {
                    i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_HOLD_ORDER_ITEM));
                    StBeanUtils.makeCreateField(i,session.getUser());
                    insterList.add(i);
                }
            });
            Boolean flag = holdOrderItemSave(updateList,insterList);
            if (!flag){
                return ValueHolderUtils.getFailValueHolder("保存明细失败！");
            }else {
                StCHoldOrderDO updateStCHoldOrderDO = new StCHoldOrderDO();
                StBeanUtils.makeModifierField(updateStCHoldOrderDO,session.getUser());
                updateStCHoldOrderDO.setId(id);
                stCHoldOrderMapper.updateById(updateStCHoldOrderDO);
            }
        }
        //新增省市区明细
        List<StCHoldProvinceItemDO> stCHoldProvinceItemDOList = stCHoldOrderRequest.getStCHoldProvinceItemDOList();
        log.info(LogUtil.format("StCHoldOrderSaveService.insertStCHoldOrder param stCHoldOrderItemDOList:{}"),
                stCHoldOrderItemDOList);
        //省市区
        if (CollectionUtils.isNotEmpty(stCHoldProvinceItemDOList)){
            List<StCHoldProvinceItemDO> updateList = new ArrayList<>();
            List<StCHoldProvinceItemDO> insterList = new ArrayList<>();
            long holdOrderId = id;
            stCHoldProvinceItemDOList.forEach(i->{
                i.setHoldOrderId(holdOrderId);
                if(Objects.nonNull(i.getId()) &&i.getId()>0){
                    StBeanUtils.makeModifierField(i,session.getUser());
                    updateList.add(i);
                }else {
                    i.setId(ModelUtil.getSequence(StConstant.TAB_ST_C_HOLD_PROVINCE_ITEM));
                    StBeanUtils.makeCreateField(i,session.getUser());
                    insterList.add(i);
                }
            });
            Boolean flag = holdProvinceItemSave(updateList,insterList);
            if (!flag){
                return ValueHolderUtils.getFailValueHolder("保存明细失败！");
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, StConstant.TAB_ST_C_HOLD_ORDER, "");
    }


    private ValueHolder check(Long id, StCHoldOrderRequest stCHoldOrderRequest, String action) {
        StCHoldOrderDO stCHoldOrder = stCHoldOrderRequest.getStCHoldOrder();
        log.info(LogUtil.format("验证HOLD单表单数据{}"), stCHoldOrder);
        Date beginTime = stCHoldOrder.getBeginTime();
        Date endTime = stCHoldOrder.getEndTime();
        String ename = stCHoldOrder.getEname();
        Integer releaseTimeType = stCHoldOrder.getReleaseTimeType();

        //需要考虑编辑情况
        StCHoldOrderDO stCHoldOrderDO = stCHoldOrderMapper.selectById(id);
        if (stCHoldOrderDO != null){
            beginTime = beginTime != null ? beginTime : stCHoldOrderDO.getBeginTime();
            endTime = endTime != null ? endTime : stCHoldOrderDO.getEndTime();
        }

        //时间判断
        if (beginTime != null && endTime != null) {
            if (endTime.before(beginTime)) {
                return ValueHolderUtils.getFailValueHolder("结束日期不能小于开始日期！");
            }
        }

        if(stCHoldOrder.getReleaseTime() != null && stCHoldOrder.getReleaseTime().before(beginTime)){
            return ValueHolderUtils.getFailValueHolder("释放时间不能小于开始日期！");
        }

        // 1:指定时点释放 2:固定时长后释放',
        if(Integer.valueOf(1).equals(releaseTimeType)){
            if(stCHoldOrder.getReleaseTime() == null){
                return ValueHolderUtils.getFailValueHolder("释放时间不能为空");
            }
        } else if(Integer.valueOf(2).equals(releaseTimeType)){
            if(stCHoldOrder.getFixedDuration() == null){
                return ValueHolderUtils.getFailValueHolder("固定时长不能为空");
            }
            if(stCHoldOrder.getTimeUnit() == null){
                return ValueHolderUtils.getFailValueHolder("单位不能为空");
            }
        }
        //具体hold直播场次 校验 HOLD_LIVE_EVENTS
        String regex = "^\\d+(,\\d)*$";
        String  holdLiveEvents = stCHoldOrder.getHoldLiveEvents();
        if (!"".equals(holdLiveEvents) &&holdLiveEvents!=null &&!holdLiveEvents.matches(regex)){
            return ValueHolderUtils.getFailValueHolder("具体hold直播场次格式错误");
        }
        if (beginTime != null && endTime != null) {
            if (endTime.before(beginTime)) {
                return ValueHolderUtils.getFailValueHolder("结束日期不能小于开始日期！");
            }
        }
        switch (action.toLowerCase()) {
            case "insert":
//                HashMap<String, Object> map = new HashMap<>();
//                map.put("ename", ename);
                if (checkEnameRepeat(ename,0L)) {
                    return ValueHolderUtils.getFailValueHolder("订单HOLD单方案名称已存在！");
                }
                break;
            case "update":

                if (!stCHoldOrderDO.getEname().equals(ename)) {
                    if (checkEnameRepeat(ename,id)) {
                        // 重复
                        return ValueHolderUtils.getFailValueHolder("订单HOLD单方案名称已存在！");
                    }
                }
                checkEstatus(stCHoldOrderDO, stCHoldOrderRequest.getIsStrategyTime());
                break;
        }
        return null;
    }

    private boolean checkEnameRepeat(String ename, Long id){
        QueryWrapper<StCHoldOrderDO> wrapper = new QueryWrapper<>();
        wrapper.eq("ename", ename);
        wrapper.eq("ISACTIVE", "Y");
        wrapper.ne("estatus",StConstant.SKUSTOCK_STATUS_03);
        wrapper.ne("estatus",StConstant.SKUSTOCK_STATUS_04);
        if (id >0) {
            wrapper.ne("id", id);
        }
        return !CollectionUtils.isEmpty(stCHoldOrderMapper.selectList(wrapper));
    }

    private void checkEstatus(StCHoldOrderDO stCHoldOrderDO, String isStrategyTime) {
        if (stCHoldOrderDO == null) {
            throw new NDSException("当前记录已不存在！");
        }
        if("Y".equals(isStrategyTime)){
            if (! StConstant.CON_BILL_STATUS_02.equals(stCHoldOrderDO.getEstatus())) {
                throw new NDSException("只能修改已审核方案的结束时间！");
            }
        } else {
            if (StConstant.CON_BILL_STATUS_02.equals(stCHoldOrderDO.getEstatus())) {
                throw new NDSException("当前记录已审核，不允许修改！");
            }else if(StConstant.CON_BILL_STATUS_03.equals(stCHoldOrderDO.getEstatus())){
                throw new NDSException("当前记录已作废，不允许修改！");
            }else if(StConstant.CON_BILL_STATUS_04.equals(stCHoldOrderDO.getEstatus())){
                throw new NDSException("当前记录已结案，不允许修改！");
            }
        }

    }

    /**
     * 系统自动新增卡单策略
     * 黑名单策略卡单
     *
     * @param cpCShopId
     * @param cpCShopTitle
     * @param receiverMobile
     * @return
     */
    public void addBalckStCard(Long cpCShopId, String cpCShopTitle, String receiverMobile) throws ParseException {
        User user = SystemUserResource.getRootUser();
        //查询策略是否存在，存在新增明细，不存在新增策略后再新增明细
        String stName = cpCShopTitle + "疑似黑名单自动卡单";
        QueryWrapper<StCHoldOrderDO> wrapper = new QueryWrapper<>();
        wrapper.eq("ename", stName);
        wrapper.eq("ISACTIVE", "Y");
        wrapper.in("estatus", Lists.newArrayList(StConstant.SKUSTOCK_STATUS_01, StConstant.SKUSTOCK_STATUS_02));
        List<StCHoldOrderDO> stCHoldOrderDOS = stCHoldOrderMapper.selectList(wrapper);

        if (CollectionUtils.isNotEmpty(stCHoldOrderDOS)) {
            //新增明细
            addNewItem(receiverMobile, user, stCHoldOrderDOS.get(0).getId(), true);
            // 删除Redis缓存
            RedisCacheUtil.delete(cpCShopId, RedisKeyConst.SHOP_HOLD_ORDER_ST);
            RedisCacheUtil.delete(cpCShopId, StRedisKey.ST_HOLD_ORDER_SHOP_ID);
        } else {
            //新增策略
            List<StCHoldOrderReason> reasons = stCHoldOrderReasonMapper.selectByTypeAndReason(1, "系统自动识别黑名单");
            if (CollectionUtils.isEmpty(reasons)) {
                throw new NDSException("未找到系统自动识别黑名单原因配置信息");
            }
            StCHoldOrderDO orderDO = getStCHoldOrderDO(cpCShopId, cpCShopTitle, user, stName, reasons.get(0).getId());
            boolean search = false;
            try {
                //新增策略和明细
                addNewStOrderIdempotent(cpCShopId, user, orderDO, receiverMobile);
                // 删除Redis缓存
                Long shopId = orderDO.getCpCShopId() == null ? null : Long.parseLong(orderDO.getCpCShopId());
                RedisCacheUtil.delete(shopId, RedisKeyConst.SHOP_HOLD_ORDER_ST);
                RedisCacheUtil.delete(shopId, StRedisKey.ST_HOLD_ORDER_SHOP_ID);
            } catch (DuplicateKeyException e) {
                DingTalkUtil.notice("黑名单卡单新增策略失败，建议检查是否有配置作废或取消，店铺：" + cpCShopTitle);
                //冲突，查询存在的数据
                search = true;
            }
            if (search) {
                //查询策略
                List<StCHoldOrderDO> stCHoldOrderDOList = stCHoldOrderMapper.selectList(wrapper);
                if (CollectionUtils.isNotEmpty(stCHoldOrderDOList)) {
                    //新增明细
                    addNewItem(receiverMobile, user, stCHoldOrderDOList.get(0).getId(), true);
                }
            }
        }
    }

    private StCHoldOrderDO getStCHoldOrderDO(Long cpCShopId, String cpCShopTitle, User user, String stName, Long holdReasonKey) throws ParseException {
        StCHoldOrderDO stCHoldOrderDO = new StCHoldOrderDO();
        stCHoldOrderDO.setId(ModelUtil.getSequence("ST_C_HOLD_ORDER"));
        stCHoldOrderDO.setEname(stName);
        stCHoldOrderDO.setCpCShopId(cpCShopId.toString());
        stCHoldOrderDO.setCpCShopTitle(cpCShopTitle);
        stCHoldOrderDO.setDayType(1);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        stCHoldOrderDO.setBeginTime(sdf.parse("2023-01-01 00:00:00"));
        stCHoldOrderDO.setEndTime(sdf.parse("2030-01-01 00:00:00"));

        stCHoldOrderDO.setHoldOrderReason(6);
        stCHoldOrderDO.setStrategyType(1);
        stCHoldOrderDO.setOrderFlag("NORMAL");
        stCHoldOrderDO.setIsAutoRelease("N");
        stCHoldOrderDO.setTimeUnit(3);
        stCHoldOrderDO.setEstatus(StConstant.SKUSTOCK_STATUS_02);
        stCHoldOrderDO.setHoldDetentionOrderReason(Math.toIntExact(holdReasonKey));

        StBeanUtils.makeCreateField(stCHoldOrderDO, user);
        return stCHoldOrderDO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addNewStOrderIdempotent(Long cpCShopId, User user, StCHoldOrderDO stCHoldOrderDO, String receiverMobile) {
        stCHoldOrderMapper.insert(stCHoldOrderDO);

        CommonIdempotent commonIdempotent = new CommonIdempotent();
        commonIdempotent.setId(sequenceUtil.buildCommonIdempotentSequenceId());
        commonIdempotent.setBusinessCode(cpCShopId.toString());
        commonIdempotent.setBatchNo(CommonIdempotentTypeEnum.BLACK_ST_CARD.getKey());
        commonIdempotent.setType(CommonIdempotentTypeEnum.BLACK_ST_CARD.getKey());
        StBeanUtils.makeCreateField(commonIdempotent, user);

        //新增明细
        addNewItem(receiverMobile, user, stCHoldOrderDO.getId(), false);

        commonIdempotentMapper.insert(commonIdempotent);
    }

    private void addNewItem(String receiverMobile, User user, Long stId, boolean isCheck) {
        if (!isCheck) {
            //新增明细
            addNewItem(receiverMobile, user, stId);
            return;
        }

        List<StCHoldOrderItemDO> stCHoldOrderItemDOS = stCHoldOrderItemMapper.selectStCHoldOrderItemByMainIds(Lists.newArrayList(stId));
        Map<String, List<StCHoldOrderItemDO>> rulesMap = stCHoldOrderItemDOS.stream().collect(Collectors.groupingBy(StCHoldOrderItemDO::getRulesRecognition));
        List<StCHoldOrderItemDO> stMobiles = rulesMap.get(OrderHoldEnum.MOBILE_NUMBER.getKey());
        List<String> mobiles = stMobiles.stream().map(StCHoldOrderItemDO::getContent).collect(Collectors.toList());
        if (!mobiles.contains(receiverMobile)) {
            //新增明细
            addNewItem(receiverMobile, user, stId);
        }
    }

    private void addNewItem(String receiverMobile, User user, Long stId) {
        StCHoldOrderItemDO itemDO = new StCHoldOrderItemDO();
        itemDO.setId(ModelUtil.getSequence("ST_C_HOLD_ORDER_ITEM"));
        itemDO.setHoldOrderId(stId);
        itemDO.setRulesRecognition(OrderHoldEnum.MOBILE_NUMBER.getKey());
        itemDO.setContent(receiverMobile);
        StBeanUtils.makeCreateField(itemDO, user);
        stCHoldOrderItemMapper.insert(itemDO);
    }

}
