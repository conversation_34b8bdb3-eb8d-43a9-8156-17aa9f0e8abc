package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.util.PrintLogUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.StRedisKeyResources;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCShopStrategyItemDO;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: heliu
 * @since: 2019/3/7
 * create at : 2019/3/7 13:49
 */
@Component
@Slf4j
public class OmsStCShopStrategyService {

    @Autowired
    private StRpcService stRpcService;
    @Resource
    private RedisOpsUtil redisOpsUtil;

    /**
     * 查找店铺查找店铺策略
     *
     * @param shopId 店铺Id
     * @return 店铺策略信息
     */
    public StCShopStrategyDO selectOcStCShopStrategy(Long shopId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OmsStCShopStrategyService", "OmsStCShopStrategyService", shopId));
        }
        try {
            StCShopStrategyDO shopStrategyTemp = null;
            String redisKey = OmsRedisKeyResources.buildLockShopStrategyRedisKey(shopId);
            String redisResult = (String) redisOpsUtil.objRedisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotEmpty(redisResult)) {
                shopStrategyTemp = JSON.parseObject(redisResult, StCShopStrategyDO.class);
            } else {
                shopStrategyTemp = stRpcService.selectOcStCShopStrategyByCpCshopId(shopId);
                // 存redis
                redisOpsUtil.objRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(shopStrategyTemp),
                        StRedisKeyResources.getCacheTimeConf(), TimeUnit.HOURS);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OmsStCShopStrategyService.selectOcStCShopStrategy:{}", "OmsStCShopStrategyService.selectOcStCShopStrategy", shopId), JSON.toJSONString(shopStrategyTemp));
            }
            return shopStrategyTemp;
        } catch (Exception ex) {
            log.error(LogUtil.format("OmsStCShopStrategyService", "OmsStCShopStrategyService", shopId), Throwables.getStackTraceAsString(ex));
            return new StCShopStrategyDO();
        }
    }

    /**
     * 查找所有店铺下的店铺策略
     *
     * @param
     * @return Long
     */
    public List<StCShopStrategyDO> selectAllShopStrategy() {
        List<StCShopStrategyDO> shopStrategyTempList = new ArrayList<>();
        try {

            String redisKey = OmsRedisKeyResources.bulidLockAllStCShopStrategyKey();
            CusRedisTemplate<String, List<StCShopStrategyDO>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            List<StCShopStrategyDO> stCShopStrategyDOList = objRedisTemplate.opsForValue().get(redisKey);

            if (CollectionUtils.isNotEmpty(stCShopStrategyDOList)) {
                shopStrategyTempList = stCShopStrategyDOList;
            } else {
                shopStrategyTempList = stRpcService.queryAllShopStrategy();
                //存放在redis中
                if (CollectionUtils.isNotEmpty(shopStrategyTempList)) {
                    RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, shopStrategyTempList);
                }
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectAllShopStrategy", "selectAllShopStrategy"), JSONObject.toJSONString(shopStrategyTempList));
            }
            return shopStrategyTempList;
        } catch (Exception ex) {
            log.error(LogUtil.format("OmsStCShopStrategyService.selectAllShopStrategy异常", "selectAllShopStrategy异常"),Throwables.getStackTraceAsString(ex));
            return shopStrategyTempList;
        }
    }

    /**
     * 查找店铺明细虚拟条码
     *
     * @param shopId       店铺Id
     * @param skuEcodeList 明细条码ID
     * @return List<StCShopStrategyItem>
     */
    public List<String> selectDiffpriceskuList(Long shopId, List<String> skuEcodeList) {

        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectDiffpriceskuList入参{}", "selectDiffpriceskuList", shopId),skuEcodeList);
            }
            String redisKey = OmsRedisKeyResources.bulidLockStCShopStrategyItemKey(shopId);
            CusRedisTemplate<String, List<StCShopStrategyItemDO>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            List<StCShopStrategyItemDO> shopStrategyItemTempList = Lists.newArrayList();
            List<StCShopStrategyItemDO> shopStrategyItemList = objRedisTemplate.opsForValue().get(redisKey);
            if (CollectionUtils.isNotEmpty(shopStrategyItemList)) {
                shopStrategyItemTempList = shopStrategyItemList;
            } else {
                shopStrategyItemTempList = stRpcService.queryShopStrategyItem(shopId);
                //存放在redis中
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, shopStrategyItemTempList);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectDiffpriceskuList入参{}", "查询虚拟条码出参", shopId),JSONObject.toJSONString(shopStrategyItemTempList));
            }
            List<String> stringList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(shopStrategyItemTempList)) {
                for (StCShopStrategyItemDO strategyItem : shopStrategyItemTempList) {
                    if (StringUtils.isNotEmpty(strategyItem.getDiffpricesku())) {
                        if (skuEcodeList.contains(strategyItem.getDiffpricesku())) {
                            stringList.add(strategyItem.getDiffpricesku());
                        }
                    }
                }
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectDiffpriceskuList入参{}", "过滤虚拟条码之后的出参", shopId),JSONObject.toJSONString(stringList));
            }
            return stringList;
        } catch (Exception ex) {
            log.error(LogUtil.format("selectOcStCShopStrategy入参","selectOcStCShopStrategy入参", shopId),Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

}
