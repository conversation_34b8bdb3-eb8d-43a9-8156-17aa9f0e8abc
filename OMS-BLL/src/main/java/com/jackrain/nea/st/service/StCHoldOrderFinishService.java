package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.BusinessSystem;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderMapper;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.resource.RedisKeyConst;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.RedisCacheUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Descroption 订单HOLD单方案结案
 * <AUTHOR>
 * @Date 2020/07/02 16:42:00
 */
@Component
@Slf4j
public class StCHoldOrderFinishService extends CommandAdapter{

    @Autowired
    private StCHoldOrderMapper mapper;
    @Autowired
    private BusinessSystem businessSystem;

    @Deprecated
    public void holdOrderStrategyAutoFinish(Integer pageSize){
        List<StCHoldOrderDO> list = mapper.queryStCHoldOrderLimit(pageSize);
        if(CollectionUtils.isNotEmpty(list)){
            LocalDateTime now = LocalDateTime.now();
            for(StCHoldOrderDO stCHoldOrder : list){
                if(stCHoldOrder.getEndTime() == null){
                    continue;
                }
                LocalDateTime localEndTime = stCHoldOrder.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().plusDays(7L);
                if(localEndTime.isAfter(now)){
                    continue;
                }
                stCHoldOrder.setModifierid(893L);
                stCHoldOrder.setModifiername("系统管理员");
                stCHoldOrder.setModifierename("root");
                stCHoldOrder.setModifieddate(new Date());
                // 更新单据状态
                stCHoldOrder.setEstatus(OmsParamConstant.INT_FOUR);
                mapper.updateById(stCHoldOrder);
                // 删除Redis缓存
                Long shopId = stCHoldOrder.getCpCShopId() == null ? null : Long.parseLong(stCHoldOrder.getCpCShopId());
                RedisCacheUtil.delete(shopId, RedisKeyConst.SHOP_HOLD_ORDER_ST);
                RedisCacheUtil.delete(shopId, StRedisKey.ST_HOLD_ORDER_SHOP_ID);
            }
        }
    }

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start StCHoldOrderFinishService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray finishArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < finishArray.size(); i++) {
            Long id = finishArray.getLong(i);
            try {
                //4.遍历结案方法
                finishStCHoldOrder(id, querySession.getUser());
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(finishArray.size(), errMap);
    }

    public void finishStCHoldOrder(Long id, User user) {
        StCHoldOrderDO stCHoldOrder = mapper.selectById(id);
        checkEstatus(stCHoldOrder);
        //更新单据状态
        StBeanUtils.makeModifierField(stCHoldOrder, user);
        stCHoldOrder.setEstatus(OmsParamConstant.INT_FOUR);
        int updateNum = mapper.updateById(stCHoldOrder);
        if (updateNum < 0) {
            throw new NDSException("方案:" + stCHoldOrder.getEname() + "结案失败！");
        }
        // 删除Redis缓存
        Long shopId = stCHoldOrder.getCpCShopId() == null ? null : Long.parseLong(stCHoldOrder.getCpCShopId());
        RedisCacheUtil.delete(shopId, RedisKeyConst.SHOP_HOLD_ORDER_ST);
        RedisCacheUtil.delete(shopId, StRedisKey.ST_HOLD_ORDER_SHOP_ID);
    }

    private void checkEstatus(StCHoldOrderDO stCHoldOrder) {
        if (stCHoldOrder == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if(OmsParamConstant.INT_ONE==stCHoldOrder.getEstatus()){
                throw new NDSException("当前记录未审核，不允许结案！");
            }else if (OmsParamConstant.INT_FOUR==stCHoldOrder.getEstatus()) {
                throw new NDSException("当前记录已结案，不允许重复结案！");
            } else if (OmsParamConstant.INT_THREE==stCHoldOrder.getEstatus()) {
                throw new NDSException("当前记录已作废，不允许结案！");
            }
        }
    }


    /**
     * 捞下 HOLD单或卡单已审核，若结束时间+业务系统参数值 是否小于等于 当前时间，若是则自动结案策略。
     * HOLD单或卡单：STRATEGY_TYPE只有HOLD单或卡单，所以这个条件没用
     * 策略状态ESTATUS=已审核 1-未审核，2-已审核，3-已作废，4-已结案
     * 结束时间END_TIME
     *
     * @return 影响行数
     */
    public int autoFinishByBusinessParams() {
        int stCHoldOrderFinishDays = businessSystem.getStCHoldOrderFinishDays();
        Date date = DateUtils.addDays(new Date(), -stCHoldOrderFinishDays);

        List<StCHoldOrderDO> dtoList = mapper.selectList(new QueryWrapper<StCHoldOrderDO>().lambda()
                .eq(StCHoldOrderDO::getEstatus, 2)
                .le(StCHoldOrderDO::getEndTime, date));
        if (CollectionUtils.isEmpty(dtoList)) {
            log.debug("没有符合条件HOLD单或卡单");
            return 0;
        }

        int count = 0;
        for (StCHoldOrderDO dto : dtoList) {
            try {
                finishStCHoldOrder(dto.getId(), R3SystemUserResource.getSystemRootUser());
                count++;
            } catch (Exception e) {
                log.warn("ID:{},HOLD单卡单超过结束间N天自动结案异常：{}", dto.getId(), Throwables.getStackTraceAsString(e));
            }
        }

        return count;
    }
}
