package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.SmsSendStrategyInfo;
import com.jackrain.nea.oc.oms.model.table.OcBMsgSendRecord;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Descroption 短信策略记录录入
 * <AUTHOR>
 * @Date 2020/8/29
 */
@Slf4j
public abstract class AbstractOmsSmsSendService implements OmsSmsSendService {


    //公共变量存储
    private HashMap<String, String> varMap = Maps.newHashMap();

    @Override
    public void execute(SmsSendStrategyInfo smsSendStrategyInfo) {
        log.debug(LogUtil.format("短信任务开始,smsSendStrategyInfo"), JSON.toJSONString(smsSendStrategyInfo));
        try {
            //获取指定的短信策略内容模板
            String smsContent = prepGetContent(smsSendStrategyInfo);
            if (StringUtils.isEmpty(smsContent)) {
                log.debug(LogUtil.format("短信模板为空,smsSendStrategyInfo"), JSON.toJSONString(smsSendStrategyInfo));
                return;
            }
            prepHandler(smsSendStrategyInfo);
            //短信内容变量替换
            smsSendStrategyInfo.setSmsContent(contentMapping(smsContent));
            //插入短信记录
            saveRecord(smsSendStrategyInfo);
        } catch (Exception e) {
            log.error(LogUtil.format("短信信息插入失败,smsSendStrategyInfo",JSON.toJSONString(smsSendStrategyInfo)), Throwables.getStackTraceAsString(e));
        }
    }

    //初始化配置
    public void prepHandler(SmsSendStrategyInfo smsSendStrategyInfo) {
        //获取短信内容自定义变量
        SimpleDateFormat sformat = new SimpleDateFormat("yyyy-MM-dd");

        if (smsSendStrategyInfo.getServiceType().equals("smsSendOutOrder")) {
            //获取短信内容自定义变量
            OcBOrder ocBOrder = smsSendStrategyInfo.getOcBOrder();
            if (ocBOrder != null) {
                varMap.put("$平台单号$", ocBOrder.getBillNo());
                varMap.put("$收货人$", ocBOrder.getReceiverName());
                if (StringUtils.isNotEmpty(ocBOrder.getUserNick())) {
                    varMap.put("$买家昵称$", ocBOrder.getUserNick());
                }
                varMap.put("$本单件数$", String.valueOf(ocBOrder.getQtyAll().intValue()));
                if (StringUtils.isNotEmpty(ocBOrder.getCpCLogisticsEname())) {
                    varMap.put("$快递公司$", ocBOrder.getCpCLogisticsEname());
                }
                if (StringUtils.isNotEmpty(ocBOrder.getExpresscode())) {
                    varMap.put("$物流单号$", ocBOrder.getExpresscode());
                }
                if (Objects.nonNull(ocBOrder.getPayTime())) {
                    varMap.put("$支付时间$", sformat.format(ocBOrder.getPayTime()));
                }
            }
        } else if (smsSendStrategyInfo.getServiceType().equals("smsSendRefundIn")) {
            if (smsSendStrategyInfo.getTaskNode().equals(OmsSmsSendRefundInService.TASKNODE01)) {
                if (Objects.nonNull(smsSendStrategyInfo.getOcBReturnOrder())) {
                    OcBReturnOrder ocBReturnOrder = smsSendStrategyInfo.getOcBReturnOrder();
                    if (Objects.nonNull(ocBReturnOrder)) {
                        varMap.put("$快递公司$", ocBReturnOrder.getCpCLogisticsEname());
                        varMap.put("$物流单号$", ocBReturnOrder.getLogisticsCode());
                        varMap.put("$本单件数$", ocBReturnOrder.getQtyInstore().toString());
                    }
                }
            } else if (smsSendStrategyInfo.getTaskNode().equals(OmsSmsSendRefundInService.TASKNODE02)) {
                OcBRefundIn ocBRefundIn = smsSendStrategyInfo.getOcBRefundIn();
                if (Objects.isNull(ocBRefundIn) || StringUtils.isEmpty(ocBRefundIn.getReceiverMobile())) {
                    return;
                }
                if (StringUtils.isNotEmpty(ocBRefundIn.getCpCLogisticsEname())) {
                    varMap.put("$快递公司$", ocBRefundIn.getCpCLogisticsEname());
                }
                if (StringUtils.isNotEmpty(ocBRefundIn.getLogisticNumber())) {
                    varMap.put("$物流单号$", ocBRefundIn.getLogisticNumber());
                }
                if (StringUtils.isNotEmpty(ocBRefundIn.getReceiverName())) {
                    varMap.put("$买家昵称$", ocBRefundIn.getReceiverName());
                }
            }
        }
    }

    /**
     * 预处理返回一个短信策略模板
     *
     * @return
     */
    public abstract String prepGetContent(SmsSendStrategyInfo smsSendStrategyInfo);

    /**
     * 插入短信记录
     */
    public abstract void saveRecord(SmsSendStrategyInfo smsSendStrategyInfo);

    public void putFieldMap(HashMap<String, String> map) {
        if (null == map || map.size() == 0) {
            return;
        }
        this.varMap.putAll(map);
    }


    /**
     * 生成真正发送的短信内容(替换内容模板中的变量)
     *
     * @param smsContent: 短信模板
     * @return
     */
    public String contentMapping(String smsContent) {
        //去除HTML标签
        String sendContent = delHTMLTag(smsContent);
        if (varMap.size() == 0) {
            return sendContent;
        }
        if (log.isDebugEnabled()){
            log.debug(LogUtil.format("短信contentMapping,smsContent",smsContent, smsContent));
        }
        for (Map.Entry<String, String> entry : varMap.entrySet()) {
            String field = entry.getKey();
            if (null != field && null != varMap.get(field) && sendContent.contains(field)) {
                sendContent = sendContent.replaceAll("\\$" + field.replaceAll("\\$", "") + "\\$", varMap.get(field));
            }
        }
        //去掉无用的（暂时先不用）
        sendContent = sendContent.replaceAll("null", "").
                replaceAll("\\$平台单号\\$", "").
                replaceAll("\\$收货人\\$", "").
                replaceAll("\\$买家昵称\\$", "").
                replaceAll("\\$原单件数\\$", "").
                replaceAll("\\$本单件数\\$", "").
                replaceAll("\\$快递公司\\$", "").
                replaceAll("\\$物流单号\\$", "").
                replaceAll("\\$支付时间\\$", "").
                replaceAll("\\$入库日期\\$", "").
                replaceAll("\\$出库日期\\$", "");
        return sendContent;
    }

    public void makeCreateField(OcBMsgSendRecord item, User user) {
        Long id = Long.valueOf(user.getId());
        Date date = new Date(System.currentTimeMillis());
        item.setAdClientId((long) user.getClientId());//所属公司
        item.setAdOrgId((long) user.getOrgId());//所属组织
        item.setOwnerid(id);//创建人id
        item.setCreationdate(date);//创建时间
        item.setOwnername(user.getName());//创建人用户名
        item.setModifierid(id);//修改人id
        item.setModifiername(user.getName());//修改人用户名
        item.setModifieddate(date);//修改时间
        item.setIsactive("Y");//是否启用
        item.setId(ModelUtil.getSequence("oc_b_msg_send_record"));
        item.setStatus("0");//未发送
    }


    public static String delHTMLTag(String htmlStr) {
        String regExScript = "<script[^>]*?>[\\s\\S]*?<\\/script>"; //定义script的正则表达式
        String regexStyle = "<style[^>]*?>[\\s\\S]*?<\\/style>"; //定义style的正则表达式
        String regExHtml = "<[^>]+>"; //定义HTML标签的正则表达式

        Pattern pScript = Pattern.compile(regExScript, Pattern.CASE_INSENSITIVE);
        Matcher mScript = pScript.matcher(htmlStr);
        htmlStr = mScript.replaceAll(""); //过滤

        Pattern pStyle = Pattern.compile(regexStyle, Pattern.CASE_INSENSITIVE);
        Matcher mStyle = pStyle.matcher(htmlStr);
        htmlStr = mStyle.replaceAll(""); //过滤style标签

        Pattern pHtml = Pattern.compile(regExHtml, Pattern.CASE_INSENSITIVE);
        Matcher mHtml = pHtml.matcher(htmlStr);
        htmlStr = mHtml.replaceAll(""); //过滤html标签
        htmlStr = htmlStr.replaceAll("&nbsp;", " ");
        htmlStr = htmlStr.replaceAll("↵", "");

        return htmlStr.trim(); //返回文本字符串
    }
}
