package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBMsgSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.SmsSendStrategyInfo;
import com.jackrain.nea.oc.oms.model.enums.OmsSendMsgNoticeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsSendMsgTaskNodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.OcBMsgSendRecord;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StcMsgDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Descroption 出库短信策略记录
 * <AUTHOR>
 * @Date 2020/8/29
 */
@Service("smsSendOutOrder")
@Slf4j
public class OmsSmsSendOutOrderService extends AbstractOmsSmsSendService {

    /**
     * 1:物流发货提醒
     */
    private static final String ADVICETYPE = OmsSendMsgNoticeEnum.LOGISTICS_DELIVERY.getVal().toString();
    /**
     * 1:非平台拆分订单完成仓库发货
     * 2:非天猫换货订单完成仓库发货
     */
    private static final String TASKNODE01 = OmsSendMsgTaskNodeEnum.NO_PLATFORM_SPLIT_ORDER_DELIVERY.getVal().toString();
    private static final String TASKNODE02 = OmsSendMsgTaskNodeEnum.NO_TIANMAO_SWAP_ORDER_DELIVERY.getVal().toString();


    /**
     * 判断是否是换货订单完成仓库发货
     */
    private boolean exchangeTag = false;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcBMsgSendMapper ocBMsgSendMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private IpBTaobaoExchangeMapper ipBTaobaoExchangeMapper;

    @Override
    public String prepGetContent(SmsSendStrategyInfo smsSendStrategyInfo) {
        smsSendStrategyInfo.setAdviceType(ADVICETYPE);
        //订单信息不全 重新查询一次 解决物流单号没有
        OcBOrder ocBOrder = ocBOrderMapper.selectById(smsSendStrategyInfo.getOcBOrder().getId());
        smsSendStrategyInfo.setOcBOrder(ocBOrder);
        Long cpCShopId = ocBOrder.getCpCShopId();
        Integer isSplit = ocBOrder.getIsSplit();
        smsSendStrategyInfo.setReceiverMobile(ocBOrder.getReceiverMobile());
        try {
            /*
            拆分单完成仓库发货
             */
            if (isSplit.equals(1)) {
                return getSmsTemplate(smsSendStrategyInfo, cpCShopId, TASKNODE01);
            } else {
                //不是淘宝单 是换货单 不是合并单
                if (ocBOrder.getPlatform().intValue() != PlatFormEnum.TAOBAO.getCode() &&
                        ocBOrder.getOrderType().intValue() == OrderTypeEnum.EXCHANGE.getVal()
                        && ocBOrder.getIsMerge().intValue() == 0) {
                    return getSmsTemplate(smsSendStrategyInfo, cpCShopId, TASKNODE02);
                }

                /*List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItemList(smsSendStrategyInfo.getOcBOrder().getId());
                 *//**
                 * 用作判断是否是：换货订单完成仓库发货-短信通知
                 * 判断订单明细中【平台换货单号】（exchange_bill_no）值为空，且换货标识字段（reserve_bigint04）为“是”
                 *//*
                smsSendStrategyInfo.setOrderItemList(ocBOrderItems);
                if (ocBOrderItems.size() > 0) {
                    for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                        Long exchangeBillNo = ocBOrderItem.getExchangeBillNo(); //平台换货单号
                        Long reserveBigint04 = ocBOrderItem.getReserveBigint04();   //换货标识
                        if (exchangeBillNo == null && reserveBigint04 == 1) {
                            return getSmsTemplate(smsSendStrategyInfo, cpCShopId, TASKNODE02);
                        }
                    }
                }*/
            }
        } catch (Exception e) {
            log.error(LogUtil.format("prepGetContentException,获取短信策略异常", "获取短信策略异常", cpCShopId), Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    @Override
    public void saveRecord(SmsSendStrategyInfo smsSendStrategyInfo) {

        OcBMsgSendRecord ocBMsgSendRecord = new OcBMsgSendRecord();
        ocBMsgSendRecord.setReceiverMobile(smsSendStrategyInfo.getReceiverMobile());
        ocBMsgSendRecord.setContent(smsSendStrategyInfo.getSmsContent());
        ocBMsgSendRecord.setStCMsgId(smsSendStrategyInfo.getMsgId());
        ocBMsgSendRecord.setCount(0L);
        makeCreateField(ocBMsgSendRecord, SystemUserResource.getRootUser());

        //短信记录插入
        ocBMsgSendMapper.insert(ocBMsgSendRecord);
    }

    @Override
    public void prepHandler(SmsSendStrategyInfo smsSendStrategyInfo) {
        log.debug(LogUtil.format("预处理订单出库内容","预处理订单出库内容") ,JSON.toJSONString(smsSendStrategyInfo));
        super.prepHandler(smsSendStrategyInfo);
        //查询出库通知单
        //SgOutQueryResult sgOutQueryResult = selectPhyOutNotice(smsSendStrategyInfo.getOcBOrder().getId());
        HashMap<String, String> map = Maps.newHashMap();
        /**
         * 换货订单完成仓库发货
         */
        if (TASKNODE02.equals(smsSendStrategyInfo.getTaskNode())) {
            List<OcBOrderItem> orderItemList = smsSendStrategyInfo.getOrderItemList();
            if (orderItemList != null && orderItemList.size() > 1) {
                Integer count = 0;
                ArrayList<String> orderIds = Lists.newArrayList();
                Long exchangeBillNo = 0L;
                for (OcBOrderItem ocBOrderItem : orderItemList) {
                    exchangeBillNo = ocBOrderItem.getExchangeBillNo(); //平台换货单号
                    Integer isExchangeItem = ocBOrderItem.getIsExchangeItem();//换货标识
                    if (exchangeBillNo == null && isExchangeItem == 1) {
                        orderIds.add(ocBOrderItem.getTid());
                        count += ocBOrderItem.getQty().intValue();
                    }
                }
                map.put("$平台单号$", String.join("-", orderIds));
                map.put("$本单件数$", String.valueOf(count.intValue()));

                //查询正向订单信息
                Integer num = getOcBorderByExchange(String.valueOf(exchangeBillNo));
                map.put("$原单件数$", String.valueOf(num));
            }
        } else if (TASKNODE01.equals(smsSendStrategyInfo.getTaskNode())) {
            OcBOrder ocBOrder = smsSendStrategyInfo.getOcBOrder();

            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            String outDate = String.valueOf(calendar.getTimeInMillis());

            List<Long> orderIdList = ES4Order.findIdsBySourceCodeAndFilterByOrderDate(
                    Lists.newArrayList(ocBOrder.getSourceCode()), outDate);

            Integer num = getNumQtyByIdList(orderIdList);

            if (Objects.nonNull(num)) {
                map.put("$原单件数$", num.toString());
            }
        }
//        if (Objects.nonNull(sgOutQueryResult)) {
//            SimpleDateFormat sformat = new SimpleDateFormat("yyyy-MM-dd");
//            map.put("$入库日期$", sformat.format(sgOutQueryResult.getNotices().getOutNotices().getOutTime()));
//        }
        putFieldMap(map);
    }

    /**
     * 找到原单数量
     *
     * @param orderIdList
     * @return
     */
    private Integer getNumQtyByIdList(List<Long> orderIdList) {
        log.debug(LogUtil.format("短信内容找到原单数量","短信内容找到原单数量") ,JSON.toJSONString(orderIdList));

        if (!CollectionUtils.isEmpty(orderIdList)) {
            List<OcBOrder> numOrders = ocBOrderMapper.selectByIdsList(orderIdList);
            List<Long> collect = numOrders.stream().filter(this::isSelect).map(OcBOrder::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                //查询明细
                List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItemsByOrderIds(collect);
                if (!CollectionUtils.isEmpty(ocBOrderItems)) {
                    BigDecimal reduce = ocBOrderItems.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    return reduce.intValue();
                }
            }
        }
        return null;
    }

    /**
     * 查询正向订单信息
     *
     * @param orderNo
     * @return
     */
    private Integer getOcBorderByExchange(String orderNo) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("订单短信内容查询","订单短信内容查询", orderNo));
        }

        IpBTaobaoExchange ipBTaobaoExchange = ipBTaobaoExchangeMapper.selectTaobaoExchangeDisputeId(orderNo);
        if (Objects.nonNull(ipBTaobaoExchange)) {
            Long bizOrderId = ipBTaobaoExchange.getBizOrderId();

            List<Long> orderIdList = ES4Order.findIdsByOid(bizOrderId);

            if (org.apache.commons.collections.CollectionUtils.isEmpty(orderIdList)) {
                return null;
            }

            List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(orderIdList);
            Set<String> sourceList = ocBOrders.stream().filter(this::isSelect).map(OcBOrder::getSourceCode).collect(Collectors.toSet());

            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            String outDate = String.valueOf(calendar.getTimeInMillis());

            List<Long> orderIds = ES4Order.findIdsBySourceCodeAndFilterByOrderDate(
                    new ArrayList<>(sourceList), outDate);

            if (CollectionUtils.isEmpty(orderIds)) {
                return null;
            }
            return getNumQtyByIdList(orderIds);
        }
        return null;
    }

    /**
     * 排除所有订单 拆单合单 复制单
     *
     * @param x
     * @return
     */
    private Boolean isSelect(OcBOrder x) {
        if (Objects.nonNull(x.getIsSplit()) && x.getIsSplit() == 1) {
            return true;
        }
        if (Objects.nonNull(x.getIsMerge()) && x.getIsMerge() == 1) {
            return true;
        }
        return !StringUtils.isEmpty(x.getCopyReason());
    }

    /**
     * 查询出库通知单
     *
     * @param id
     */
//    private SgOutQueryResult selectPhyOutNotice(Long id) {
//        SgPhyOutBillQueryRequest request = new SgPhyOutBillQueryRequest();
//        SgPhyOutBillBaseRequest billBaseRequest = new SgPhyOutBillBaseRequest();
//        billBaseRequest.setSourceBillId(id);
//        billBaseRequest.setSourceBillType(1);
//        request.setBaseRequests(Lists.newArrayList(billBaseRequest));
//        ValueHolderV14<List<SgOutQueryResult>> listValueHolderV14 = null;
//        try {
//            log.debug("sms-查询出库通知单入参" + JSON.toJSONString(request));
//            listValueHolderV14 = sgRpcService.queryOutBySource(request);
//            log.debug("sms-查询出库通知单返回" + JSON.toJSONString(listValueHolderV14));
//
//            if (Objects.isNull(listValueHolderV14) || listValueHolderV14.getCode() != 0 || CollectionUtils.isEmpty(listValueHolderV14.getData())) {
//                throw new NDSException("发货短信查询入库通知单返回空信息" + JSON.toJSONString(request));
//            }
//
//            List<SgOutQueryResult> data = listValueHolderV14.getData();
//            return data.get(0);
//        } catch (Exception e) {
//            log.debug("sms-查询出库通知单返回,e={}", e.toString());
//        }
//        return null;
//    }


    /**
     * 拆分单完成仓库发货-短信通知
     *
     * @param smsSendStrategyInfo
     * @param cpCShopId
     * @param taskNode
     * @return
     */
    private String getSmsTemplate(SmsSendStrategyInfo smsSendStrategyInfo, Long cpCShopId, String taskNode) {
        smsSendStrategyInfo.setTaskNode(taskNode);
        ValueHolderV14<StcMsgDO> stringValueHolderV14 = stRpcService.queryMsgInfo(cpCShopId, taskNode, ADVICETYPE);
        if (stringValueHolderV14.isOK()) {
            StcMsgDO data = stringValueHolderV14.getData();
            smsSendStrategyInfo.setMsgId(data.getId()); //设置短信策略ID
            return data.getTemplateContent(); //返回短信模板
        } else {
            log.warn(LogUtil.format("prepGetContentException", "prepGetContentException", cpCShopId, taskNode), ADVICETYPE, stringValueHolderV14.getMessage());
            return null;
        }
    }
}
