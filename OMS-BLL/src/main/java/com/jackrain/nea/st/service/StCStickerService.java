package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.StCStickerItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCStickerMapper;
import com.jackrain.nea.oc.oms.model.enums.StickerAppointDimensionEnum;
import com.jackrain.nea.oc.oms.model.table.StCSticker;
import com.jackrain.nea.oc.oms.model.table.StCStickerItem;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.ps.model.OmsProAttributeInfo;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.StRedisKeyResources;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2023/2/22 17:18
 * @Version 1.0
 */
@Slf4j
@Component
public class StCStickerService {


    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private StCStickerMapper stCStickerMapper;
    @Autowired
    private StCStickerItemMapper stCStickerItemMapper;

    @Autowired
    private BusinessSystemParamService businessSystemParamService;


    /**
     * 保存贴纸策略
     *
     * @param querySession 查询会话
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder saveSticker(QuerySession querySession) {
        ValueHolder holder;
        try {
            DefaultWebEvent event = querySession.getEvent();
            List<String> nullKeyList = new ArrayList<>();
            JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
            log.info(LogUtil.format("StCStickerService.saveSticker param:{}",
                    "StCStickerService.saveSticker"), param.toJSONString());
            Long objid = param.getLong("objid");
            User user = querySession.getUser();
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            JSONObject stickerJson = fixColumn.getJSONObject("ST_C_STICKER");
            JSONArray itemJsonArray = fixColumn.getJSONArray("ST_C_STICKER_ITEM");
            StCSticker stCSticker = JsonUtils.jsonParseClass(stickerJson, StCSticker.class);
            List<StCStickerItem> stCStickerItems = null;
            if (CollectionUtils.isNotEmpty(itemJsonArray)) {
                stCStickerItems = JSON.parseArray(itemJsonArray.toJSONString(), StCStickerItem.class);
            }
            if (objid < 0) {
                //新增
                holder = addSticker(stCSticker, stCStickerItems, user);
            } else {
                //更新
                StCSticker updatedSticker = validateAndMergeSticker(objid, stCSticker, stickerJson);
                holder = updateSticker(updatedSticker, stCStickerItems, objid, user);
            }
        } catch (Exception e) {
            throw new NDSException("保存异常:" + e.getMessage());
        }
        return holder;
    }


    /**
     * 主表校验
     *
     * @param stCSticker 贴纸策略主表
     * @param objid      对象ID
     */
    private void validateMainTable(StCSticker stCSticker, Long objid) {
        // 校验策略名称
        String name = stCSticker.getName();
        QueryWrapper<StCSticker> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StCSticker::getName, name);
        if (objid > 0) {
            queryWrapper.lambda().ne(StCSticker::getId, objid);
        }
        int count = stCStickerMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new NDSException("策略名称[" + name + "]已存在，请重新输入");
        }

        // 校验时间
        Date startTime = stCSticker.getStartTime();
        Date endTime = stCSticker.getEndTime();
        if (startTime == null) {
            throw new NDSException("开始时间不能为空");
        }
        if (endTime == null) {
            throw new NDSException("结束时间不能为空");
        }

        boolean flag = startTime.getTime() > endTime.getTime() || endTime.getTime() < System.currentTimeMillis();
        if (flag) {
            throw new NDSException("结束时间必须大于等于开始时间并且结束时间要大于当前时间");
        }
    }

    /**
     * 明细校验
     *
     * @param stCStickerItems 贴纸策略明细列表
     */
    private void validateItems(List<StCStickerItem> stCStickerItems, Long objid) {
        if (CollectionUtils.isEmpty(stCStickerItems)) {
            return;
        }
        // 获取贴纸四级类目编码
        List<String> stickerCategoryList = businessSystemParamService.getStickerCategoryCodes();
        // 用于检查当前策略明细内部是否有重复
        Map<String, StCStickerItem> dimensionContentMap = new HashMap<>();
        // 如果是更新操作，需要与数据库中当前策略的明细进行比较
        if (objid != null && objid > 0) {
            // 查询数据库中当前策略的明细
            List<StCStickerItem> existingItems = stCStickerItemMapper.selectList(
                    new QueryWrapper<StCStickerItem>().lambda()
                            .eq(StCStickerItem::getStCStickerId, objid));
            // 将数据库中的明细按照appointDimension + "_" + identifyContent分组
            if (CollectionUtils.isNotEmpty(existingItems)) {
                for (StCStickerItem existingItem : existingItems) {
                    String key = existingItem.getAppointDimension() + "_" + existingItem.getIdentifyContent();
                    dimensionContentMap.put(key, existingItem);
                }
            }
        }
        for (StCStickerItem item : stCStickerItems) {
            // 1. 校验指定主播ID+SKU和指定主播ID+四级类目时，识别内容必须存在+号
            Integer appointDimension = item.getAppointDimension();
            String identifyContent = item.getIdentifyContent();

            if ((StickerAppointDimensionEnum.ANCHOR_SKU.getCode().equals(appointDimension) ||
                    StickerAppointDimensionEnum.ANCHOR_CATEGORY.getCode().equals(appointDimension)) &&
                    (StringUtils.isEmpty(identifyContent) || !identifyContent.contains("+"))) {
                String dimensionName = StickerAppointDimensionEnum.getNameByCode(appointDimension);
                throw new NDSException("指定维度[" + dimensionName + "]时，识别内容必须存在+号");
            }
            // 2. 数量得大于0
            BigDecimal qty = item.getQty();
            if (qty == null || qty.compareTo(BigDecimal.ZERO) <= 0) {
                throw new NDSException("数量必须大于0");
            }
            // 3. 校验赠品是否属于贴纸
            Long psCSkuId = item.getPsCSkuId();
            ProductSku productSku = psRpcService.selectProductById(psCSkuId + "");
            if (productSku == null) {
                throw new NDSException("SKU信息不存在");
            }
            // 检查是否开启了效期管理
            String isEnableExpiry = productSku.getIsEnableExpiry();
            if ("Y".equals(isEnableExpiry)) {
                throw new NDSException("[" + productSku.getSkuEcode() + "]开启了效期管理，请检查数据或联系IT");
            }
            // 检查是否属于贴纸四级类目
            boolean isStickerCategory = false;
            Map<String, OmsProAttributeInfo> proAttributeMap = productSku.getProAttributeMap();
            if (proAttributeMap != null && !proAttributeMap.isEmpty()) {
                OmsProAttributeInfo mDim6Info = proAttributeMap.get("M_DIM6_ID");
                if (mDim6Info != null && StringUtils.isNotEmpty(mDim6Info.getEcode()) &&
                        CollectionUtils.isNotEmpty(stickerCategoryList) && stickerCategoryList.contains(mDim6Info.getEcode())) {
                    isStickerCategory = true;
                }
            }
            if (!isStickerCategory) {
                throw new NDSException("[" + productSku.getSkuEcode() + "]不属于贴纸，请检查数据或联系IT");
            }
            // 4. 单个策略不同行"指定维度+识别内容" 不能重复
            String key = appointDimension + "_" + identifyContent;
            if (dimensionContentMap.containsKey(key)) {
                String dimensionName = StickerAppointDimensionEnum.getNameByCode(appointDimension);
                throw new NDSException("指定维度[" + dimensionName + "]、识别内容[" + identifyContent + "]重复");
            }
            dimensionContentMap.put(key, item);
        }
    }

    /**
     * 处理明细数据 - 将识别内容的四级类目转化成id赋值给匹配内容
     *
     * @param stCStickerItems 贴纸策略明细列表
     */
    private void processItems(List<StCStickerItem> stCStickerItems) {
        if (CollectionUtils.isEmpty(stCStickerItems)) {
            return;
        }

        // 先处理无主播ID+SKU和指定主播ID+SKU的情况，直接将识别内容赋值给匹配内容
        List<StCStickerItem> categoryItems = new ArrayList<>();

        for (StCStickerItem item : stCStickerItems) {
            Integer appointDimension = item.getAppointDimension();
            String identifyContent = item.getIdentifyContent();

            // 如果是无主播ID+SKU或指定主播ID+SKU，直接将识别内容赋值给匹配内容
            if (StickerAppointDimensionEnum.NO_ANCHOR_SKU.getCode().equals(appointDimension) ||
                    StickerAppointDimensionEnum.ANCHOR_SKU.getCode().equals(appointDimension)) {
                item.setMatchContent(identifyContent);
            } else {
                // 如果是无主播ID+四级类目或指定主播ID+四级类目，收集起来后续处理
                categoryItems.add(item);
            }
        }

        // 如果没有需要处理的四级类目类型，直接返回
        if (CollectionUtils.isEmpty(categoryItems)) {
            return;
        }

        // 收集需要查询的四级类目编码
        Map<String, List<StCStickerItem>> categoryCodeItemsMap = categoryItems.stream()
                .filter(item -> {
                    Integer appointDimension = item.getAppointDimension();
                    String identifyContent = item.getIdentifyContent();
                    return (StickerAppointDimensionEnum.NO_ANCHOR_CATEGORY.getCode().equals(appointDimension) ||
                            StickerAppointDimensionEnum.ANCHOR_CATEGORY.getCode().equals(appointDimension)) &&
                            StringUtils.isNotEmpty(identifyContent);
                })
                .collect(Collectors.groupingBy(item -> {
                    Integer appointDimension = item.getAppointDimension();
                    String identifyContent = item.getIdentifyContent();
                    String categoryCode = identifyContent;

                    if (StickerAppointDimensionEnum.ANCHOR_CATEGORY.getCode().equals(appointDimension)) {
                        // 如果是指定主播ID+四级类目，需要分离出四级类目编码
                        String[] parts = identifyContent.split("\\+");
                        if (parts.length > 1) {
                            categoryCode = parts[1];
                        }
                    }

                    return categoryCode;
                }));

        // 批量查询四级类目信息
        if (!categoryCodeItemsMap.isEmpty()) {
            try {
                List<String> categoryCodes = categoryCodeItemsMap.keySet().stream().collect(Collectors.toList());
                List<PsCProdimItem> prodimItems = psRpcService.queryFourthCategoryByCodes(categoryCodes);
                // 将四级类目信息转换为Map，以编码为key
                Map<String, PsCProdimItem> prodimItemMap = CollectionUtils.isEmpty(prodimItems) ? new HashMap<>() :
                        prodimItems.stream()
                                .collect(Collectors.toMap(
                                        PsCProdimItem::getEcode,
                                        Function.identity(),
                                        (existing, replacement) -> existing
                                ));

                // 设置匹配内容
                categoryCodeItemsMap.forEach((categoryCode, items) -> {
                    PsCProdimItem prodimItem = prodimItemMap.get(categoryCode);
                    if (prodimItem != null) {
                        // 如果批量查询找到了，设置匹配内容
                        String categoryId = prodimItem.getId().toString();

                        items.forEach(item -> {
                            Integer appointDimension = item.getAppointDimension();
                            String identifyContent = item.getIdentifyContent();

                            if (StickerAppointDimensionEnum.ANCHOR_CATEGORY.getCode().equals(appointDimension)) {
                                // 如果是指定主播ID+四级类目，需要保留主播ID并与四级类目ID拼接
                                String[] parts = identifyContent.split("\\+");
                                if (parts.length > 0) {
                                    // 保留主播ID，并与四级类目ID拼接
                                    item.setMatchContent(parts[0] + "+" + categoryId);
                                    return;
                                }
                            }

                            // 如果是无主播ID+四级类目，直接设置四级类目ID
                            item.setMatchContent(categoryId);
                        });
                    } else {
                        // 如果批量查询没有找到，直接报错
                        throw new NDSException("四级类目[" + categoryCode + "]不存在，请检查数据");
                    }
                });
            } catch (Exception e) {
                log.error("批量获取四级类目信息失败", e);
                throw new NDSException("获取四级类目信息失败，请稍后重试");
            }
        }
    }

    /**
     * 添加贴纸策略
     *
     * @param stCSticker      贴纸策略主表
     * @param stCStickerItems 贴纸策略明细
     * @param user            用户
     * @return 操作结果
     */
    private ValueHolder addSticker(StCSticker stCSticker, List<StCStickerItem> stCStickerItems, User user) {
        // 主表校验
        validateMainTable(stCSticker, -1L);
        // 明细校验
        validateItems(stCStickerItems, -1L);
        // 处理明细数据 - 将识别内容的四级类目转化成id赋值给匹配内容
        processItems(stCStickerItems);
        Long mainId = ModelUtil.getSequence("ST_C_STICKER");
        stCSticker.setId(mainId);
        stCSticker.setStatus(1);
        BaseModelUtil.initialBaseModelSystemField(stCSticker, user);
        if (CollectionUtils.isNotEmpty(stCStickerItems)) {
            for (StCStickerItem stCStickerItem : stCStickerItems) {
                Long psCSkuId = stCStickerItem.getPsCSkuId();
                ProductSku productSku = psRpcService.selectProductById(psCSkuId + "");
                if (productSku == null) {
                    throw new NDSException("SKU信息不存在");
                }
                stCStickerItem.setStCStickerId(mainId);
                stCStickerItem.setId(ModelUtil.getSequence("ST_C_STICKER_ITEM"));
                stCStickerItem.setPsCProEcode(productSku.getProdCode());
                stCStickerItem.setPsCProEname(productSku.getName());
                stCStickerItem.setPsCProId(productSku.getProdId());
                stCStickerItem.setPsCSkuEcode(productSku.getSkuEcode());
                String skuSpec = productSku.getSkuSpec();
                if (StringUtils.isEmpty(skuSpec)) {
                    skuSpec = "";
                }
                String spec = productSku.getSpec();
                if (StringUtils.isEmpty(spec)) {
                    spec = "";
                }
                stCStickerItem.setSkuSpec(skuSpec + ":" + spec);
                BaseModelUtil.initialBaseModelSystemField(stCStickerItem, user);
            }
        }
        stCStickerMapper.insert(stCSticker);
        if (CollectionUtils.isNotEmpty(stCStickerItems)) {
            stCStickerItemMapper.batchInsert(stCStickerItems);
        }
        return ValueHolderUtils.getSuccessValueHolder(mainId, "ST_C_STICKER", "保存成功");
    }


    /**
     * 验证并合并贴纸策略数据
     *
     * @param objid       贴纸策略ID
     * @param stCSticker  新的贴纸策略数据
     * @param stickerJson 贴纸策略JSON数据
     */
    private StCSticker validateAndMergeSticker(Long objid, StCSticker stCSticker, JSONObject stickerJson) {
        StCSticker sticker = stCStickerMapper.selectById(objid);
        if (sticker == null) {
            throw new NDSException("当前记录已不存在!");
        }
        if (sticker.getStatus() == null || sticker.getStatus() != 1) {
            throw new NDSException("当前策略的状态不是未审核，不允许修改！");
        }
        if (stCSticker != null) {
            stCSticker.setId(objid);
        } else {
            stCSticker = new StCSticker();
            stCSticker.setId(objid);
        }
        if (StringUtils.isEmpty(stCSticker.getName())) {
            stCSticker.setName(sticker.getName());
        }
        if (stCSticker.getShopId() == null) {
            stCSticker.setShopId(sticker.getShopId());
        }
        if (stCSticker.getStartTime() == null) {
            stCSticker.setStartTime(sticker.getStartTime());
        }
        if (stCSticker.getEndTime() == null) {
            stCSticker.setEndTime(sticker.getEndTime());
        }
        if (StringUtils.isEmpty(stCSticker.getRemarks())) {
            if (stickerJson != null && stickerJson.containsKey("REMARKS")) {
                stCSticker.setRemarks("");
            } else {
                stCSticker.setRemarks(sticker.getRemarks());
            }
        }
        return stCSticker;
    }

    /**
     * 更新贴纸策略
     *
     * @param stCSticker      贴纸策略主表
     * @param stCStickerItems 贴纸策略明细
     * @param id              贴纸策略ID
     * @param user            用户
     * @return 操作结果
     */
    private ValueHolder updateSticker(StCSticker stCSticker, List<StCStickerItem> stCStickerItems, Long id, User user) {
        validateMainTable(stCSticker, id);
        // 校验明细
        validateItems(stCStickerItems, id);
        // 处理明细数据 - 将识别内容的四级类目转化成id赋值给匹配内容
        processItems(stCStickerItems);
        if (CollectionUtils.isNotEmpty(stCStickerItems)) {
            for (StCStickerItem stCStickerItem : stCStickerItems) {
                Long psCSkuId = stCStickerItem.getPsCSkuId();
                ProductSku productSku = psRpcService.selectProductById(psCSkuId + "");
                if (productSku == null) {
                    throw new NDSException("SKU信息不存在");
                }
                stCStickerItem.setStCStickerId(id);
                stCStickerItem.setId(ModelUtil.getSequence("ST_C_STICKER_ITEM"));
                stCStickerItem.setPsCProEcode(productSku.getProdCode());
                stCStickerItem.setPsCProEname(productSku.getName());
                stCStickerItem.setPsCProId(productSku.getProdId());
                stCStickerItem.setPsCSkuEcode(productSku.getSkuEcode());
                String skuSpec = productSku.getSkuSpec();
                if (StringUtils.isEmpty(skuSpec)) {
                    skuSpec = "";
                }
                String spec = productSku.getSpec();
                if (StringUtils.isEmpty(spec)) {
                    spec = "";
                }
                stCStickerItem.setSkuSpec(skuSpec + ":" + spec);
                BaseModelUtil.initialBaseModelSystemField(stCStickerItem, user);
            }
            stCStickerItemMapper.batchInsert(stCStickerItems);
        }
        BaseModelUtil.setupUpdateParam(stCSticker, user);
        stCStickerMapper.updateById(stCSticker);
        return ValueHolderUtils.getSuccessValueHolder(id, "ST_C_STICKER", "保存成功");
    }


    /**
     * 审核
     *
     * @param querySession 查询会话
     * @return 审核结果
     */
    public ValueHolder auditSticker(QuerySession querySession) {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.info(LogUtil.format("StCStickerService.auditSticker param：{}",
                "StCStickerService.auditSticker"), JSON.toJSONString(param));
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        User user = querySession.getUser();
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        HashMap<Long, Object> errMap = new HashMap();
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = auditArray.getLong(i);
            try {
                auditSticker(id, user);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }


    /**
     * 审核贴纸策略
     *
     * @param id   贴纸策略ID
     * @param user 用户
     */
    private void auditSticker(Long id, User user) {
        StCSticker sticker = stCStickerMapper.selectById(id);
        if (sticker == null) {
            throw new NDSException("当前记录已不存在!");
        }
        if (sticker.getStatus() == null || sticker.getStatus() != 1) {
            throw new NDSException("当前策略的状态不是未审核，不允许审核！");
        }
        Long shopId = sticker.getShopId();
        List<StCSticker> stCStickers = stCStickerMapper.selectStCStickerList(shopId);
        if (CollectionUtils.isNotEmpty(stCStickers)) {
            Date newStartTime = sticker.getStartTime();
            Date newEndTime = sticker.getEndTime();
            for (StCSticker existingSticker : stCStickers) {
                // 跳过当前策略自身
                if (existingSticker.getId().equals(sticker.getId())) {
                    continue;
                }
                Date existingStartTime = existingSticker.getStartTime();
                Date existingEndTime = existingSticker.getEndTime();
                // 检查是否有时间重叠
                boolean hasOverlap = !(newEndTime.before(existingStartTime) || newStartTime.after(existingEndTime));
                // 如果一个策略的结束时间正好是另一个策略的开始时间，也应该被视为重叠
                boolean hasEdgeOverlap = newStartTime.equals(existingEndTime) || newEndTime.equals(existingStartTime);
                if (hasOverlap || hasEdgeOverlap) {
                    throw new NDSException("存在相同店铺时间重叠的已审核数据,不允许审核!");
                }
            }
        }

        List<StCStickerItem> stCStickerItems = stCStickerItemMapper.selectStCStickerItemListByMainId(sticker.getId());
        if (CollectionUtils.isEmpty(stCStickerItems)) {
            throw new NDSException("当前策略不存在有效的明细，不允许审核!");
        }

        StCSticker stCSticker = new StCSticker();
        stCSticker.setId(id);
        stCSticker.setStatus(2);
        stCSticker.setSubmitUserId(Long.valueOf(user.getId()));
        stCSticker.setSubmitTime(new Date());
        BaseModelUtil.makeBaseModifyField(stCSticker, user);
        int i1 = stCStickerMapper.updateById(stCSticker);
        if (i1 <= 0) {
            throw new NDSException("审核失败！");
        }

        // 删除缓存
        if (shopId != null) {
            String cacheKey = StRedisKeyResources.buildStickerCacheKey(shopId);
            RedisOpsUtil.getStrRedisTemplate().delete(cacheKey);
            log.info(LogUtil.format("审核贴纸策略成功，删除缓存 shopId:{}, cacheKey:{}",
                    "审核贴纸策略成功，删除缓存"), shopId, cacheKey);
        }
    }


    /**
     * 结案
     *
     * @param querySession 查询会话
     * @return 结案结果
     */
    public ValueHolder caseSticker(QuerySession querySession) {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.info(LogUtil.format("StCStickerService.caseSticker param：{}",
                "StCStickerService.caseSticker"), JSON.toJSONString(param));
        User user = querySession.getUser();
        JSONArray idsArray = param.getJSONArray("ids");
        List<Long> ids = JSON.parseArray(idsArray.toJSONString(), Long.class);
        HashMap<Long, Object> errMap = new HashMap();
        for (Long id : ids) {
            try {
                caseSticker(id, user);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        return StBeanUtils.getExcuteValueHolder(ids.size(), errMap);
    }


    /**
     * 结案贴纸策略
     *
     * @param id   贴纸策略ID
     * @param user 用户
     */
    private void caseSticker(Long id, User user) {
        StCSticker sticker = stCStickerMapper.selectById(id);
        if (sticker == null) {
            throw new NDSException("当前记录已不存在!");
        }
        if (sticker.getStatus() == null || sticker.getStatus() != 2) {
            throw new NDSException("当前策略的状态不是已审核，不允许结案！");
        }
        StCSticker stCSticker = new StCSticker();
        stCSticker.setId(id);
        stCSticker.setStatus(3);
        stCSticker.setCloseUserId(Long.valueOf(user.getId()));
        stCSticker.setCloseTime(new Date());
        BaseModelUtil.makeBaseModifyField(stCSticker, user);
        int i1 = stCStickerMapper.updateById(stCSticker);
        if (i1 <= 0) {
            throw new NDSException("结案失败！");
        }

        // 删除缓存
        Long shopId = sticker.getShopId();
        if (shopId != null) {
            String cacheKey = StRedisKeyResources.buildStickerCacheKey(shopId);
            RedisOpsUtil.getStrRedisTemplate().delete(cacheKey);
            log.info(LogUtil.format("结案贴纸策略成功，删除缓存 shopId:{}, cacheKey:{}",
                    "结案贴纸策略成功，删除缓存"), shopId, cacheKey);
        }
    }

    public ValueHolder del(QuerySession querySession) {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        Long objid = param.getLong("objid");
        StCSticker stCSticker = stCStickerMapper.selectById(objid);
        if (stCSticker == null) {
            throw new NDSException("记录不存在!");
        }
        Integer billStatus = stCSticker.getStatus();
        if (billStatus != null && billStatus != 1) {
            throw new NDSException("当前状态不是待审核,不允许删除!");
        }
        JSONObject tabItem = param.getJSONObject("tabitem");
        //判断是不是只删除子表
        if (Objects.nonNull(tabItem) && CollectionUtils.isNotEmpty(tabItem.getJSONArray("ST_C_STICKER_ITEM"))) {
            JSONArray itemIds = tabItem.getJSONArray("ST_C_STICKER_ITEM");
            List<String> itemIdList = JSONArray.parseArray(itemIds.toJSONString(), String.class);
            List<StCStickerItem> stCStickerItems = stCStickerItemMapper.selectBatchIds(itemIdList);
            if (!CollectionUtils.isEmpty(stCStickerItems)) {
                Map<Long, String> beforeDelObjMap = new HashMap<>();
                for (StCStickerItem item : stCStickerItems) {
                    beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                }
                querySession.setAttribute("beforeDelObjMap", beforeDelObjMap);
            }
            stCStickerItemMapper.deleteBatchIds(itemIdList);
        } else {
            //删除主表
            stCStickerMapper.deleteById(objid);
            //删除关联的全部子表
            QueryWrapper<StCStickerItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StCStickerItem::getStCStickerId, objid);
            stCStickerItemMapper.delete(queryWrapper);
        }
        return ValueHolderUtils.getDeleteSuccessValueHolder();
    }

}
