package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderReasonMapper;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.resource.RedisKeyConst;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.RedisCacheUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * @author: 秦雄飞
 * @time: 2023/2/15 16:49
 * @description: HOLD单原因保存
 */
@Component
@Slf4j
public class StCHoldOrderReasonSaveService {

    @Autowired
    private StCHoldOrderReasonMapper stCHoldOrderReasonMapper;

    @Transactional
    public ValueHolder saveHoldOrderReason(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("HOLD单原因保存.param={}", "saveHoldOrderReason.param"), JSON.toJSONString(param));
        }
        User user = querySession.getUser();
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        Long objid = param.getLong("objid");
        String tableName = param.getString("table");
        JSONObject jsonObject = fixColumn.getJSONObject(tableName);
        try {
            if (Objects.nonNull(jsonObject)) {
                StCHoldOrderReason stCHoldOrderReason = JSONObject.parseObject(jsonObject.toJSONString(), StCHoldOrderReason.class);
                //新增
                if (Objects.isNull(objid) || objid < 0) {
                    objid = doInsert(stCHoldOrderReason, tableName, user);
                } else {
                    //更新
                    stCHoldOrderReason.setId(objid);
                    doUpdate(stCHoldOrderReason, jsonObject, user);
                }
            } else {
                throw new NDSException("表单不能为空！");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("HOLD单原因保存.error={}", "saveHoldOrderReason.error"), Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.getFailValueHolder(e.getMessage());
        }
        return ValueHolderUtils.getSuccessValueHolder(objid, tableName);
    }

    /**
     * 新增
     *
     * @param stCHoldOrderReason 对象
     * @param tableName          表名
     * @param user               用户
     * @return
     */
    public Long doInsert(StCHoldOrderReason stCHoldOrderReason, String tableName, User user) {
        this.checkAvailable(true, stCHoldOrderReason);
        Long id = ModelUtil.getSequence(tableName);
        stCHoldOrderReason.setId(id);
        OmsModelUtil.setDefault4Add(stCHoldOrderReason, user);
        stCHoldOrderReasonMapper.insert(stCHoldOrderReason);
        return id;
    }

    /**
     * 更新
     *
     * @param stCHoldOrderReason 对象
     * @param jsonObject         页面传参
     * @param user               用户
     */
    public void doUpdate(StCHoldOrderReason stCHoldOrderReason, JSONObject jsonObject, User user) {
        // 判断更新为空
        if (jsonObject.containsKey("REASON") &&
                Objects.isNull(jsonObject.getString("REASON"))) {
            stCHoldOrderReason.setReason(StringUtils.EMPTY);
        }
        // 判断更新为空
        if (jsonObject.containsKey("REMARK") &&
                Objects.isNull(jsonObject.getString("REMARK"))) {
            stCHoldOrderReason.setRemark(StringUtils.EMPTY);
        }
        // 更新
        this.checkAvailable(false, stCHoldOrderReason);
        OmsModelUtil.setDefault4Upd(stCHoldOrderReason, user);
        stCHoldOrderReasonMapper.updateById(stCHoldOrderReason);
        // 删除缓存
        RedisCacheUtil.deleteStr(stCHoldOrderReason.getId(), RedisKeyConst.HOLD_ORDER_REASON_KEY);
    }

    /**
     * 校验数据重复
     *
     * @param isAdd              是否新增
     * @param stCHoldOrderReason 对象
     * @return boolean
     */
    public void checkAvailable(boolean isAdd, StCHoldOrderReason stCHoldOrderReason) {
        Integer type = stCHoldOrderReason.getType();
        String reason = stCHoldOrderReason.getReason();
        if (!isAdd) {
            if (Objects.isNull(type) && StringUtils.isBlank(reason)) {
                return;
            }
            StCHoldOrderReason orderReason = stCHoldOrderReasonMapper.selectById(stCHoldOrderReason.getId());
            if (Objects.isNull(stCHoldOrderReason.getType())) {
                type = orderReason.getType();
            }
            if (StringUtils.isBlank(stCHoldOrderReason.getReason())) {
                reason = orderReason.getReason();
            }
        }
        if (stCHoldOrderReasonMapper.selectExistCount(type, reason) > 0) {
            throw new NDSException("类型+原因说明在记录中已存在，记录重复！");
        }
    }
}
