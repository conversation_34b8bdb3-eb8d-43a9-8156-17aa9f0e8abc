package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldProvinceItemMapper;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderItemDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldProvinceItemDO;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.resource.RedisKeyConst;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.RedisCacheUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;

/**
 * @Descroption 订单HOLD单方案审核
 * <AUTHOR>
 * @Date 2020/07/02 16:42:00
 */
@Component
@Slf4j
public class StCHoldOrderAuditService extends CommandAdapter {
    @Autowired
    private StCHoldOrderMapper mapper;
    @Autowired
    private StCHoldOrderItemMapper itemMapper;
    @Autowired
    private StCHoldProvinceItemMapper stCHoldProvinceItemMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        log.debug(LogUtil.format("Start StCHoldOrderAuditService.execute. ReceiveParams: {}"), param.toJSONString());
        //2.定义错误记录数
        HashMap<Long, Object> errMap = new HashMap();
        //3.生成审核Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        for (int i = 0; i < auditArray.size(); i++) {
            Long id = auditArray.getLong(i);
            try {
                //4.遍历审核方法
                auditStCHoldOrder(id, querySession);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        //5.返回ValueHolder对象
        return StBeanUtils.getExcuteValueHolder(auditArray.size(), errMap);
    }

    public void auditStCHoldOrder(Long id, QuerySession querySession) {
        StCHoldOrderDO stCHoldOrderDO= mapper.selectById(id);
        //主表校验
        checkEstatus(stCHoldOrderDO);

        //更新单据状态
        StBeanUtils.makeModifierField(stCHoldOrderDO, querySession.getUser());
        stCHoldOrderDO.setEstatus(OmsParamConstant.INT_TWO);
        StBeanUtils.makeModifierField(stCHoldOrderDO,querySession.getUser());
        int updateNum = mapper.updateById(stCHoldOrderDO);
        if (updateNum < 0) {
            throw new NDSException("审核失败！");
        }
        // 删除Redis缓存
        Long shopId = stCHoldOrderDO.getCpCShopId() == null ? null : Long.parseLong(stCHoldOrderDO.getCpCShopId());
        RedisCacheUtil.delete(shopId, RedisKeyConst.SHOP_HOLD_ORDER_ST);
        RedisCacheUtil.delete(shopId, StRedisKey.ST_HOLD_ORDER_SHOP_ID);
    }

    private void checkEstatus(StCHoldOrderDO stCHoldOrderDO) {
        if (stCHoldOrderDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (OmsParamConstant.INT_TWO==stCHoldOrderDO.getEstatus()) {
                throw new NDSException("当前记录已审核，不允许重复审核！");
            }else if(OmsParamConstant.INT_THREE==stCHoldOrderDO.getEstatus()){
                throw new NDSException("当前记录已作废，不允许审核！");
            }else if(OmsParamConstant.INT_THREE==stCHoldOrderDO.getEstatus()){
                throw new NDSException("当前记录已结案，不允许审核！");
            }
        }

        //若HOLD单原因为“普通商品”或者“省市区”时，需要校验策略明细是否有值
        if(stCHoldOrderDO.getHoldOrderReason() == 6 ){
            List<StCHoldOrderItemDO> itemList = itemMapper.selectList(new QueryWrapper<StCHoldOrderItemDO>().lambda()
                    .eq(StCHoldOrderItemDO::getIsactive, "Y")
                    .eq(StCHoldOrderItemDO::getHoldOrderId, stCHoldOrderDO.getId()));
            if(CollectionUtils.isEmpty(itemList)){
                throw new NDSException("请先维护策略明细，再审核！");
            }
        }
        if (stCHoldOrderDO.getHoldOrderReason() == 8){
            List<StCHoldProvinceItemDO> itemList = stCHoldProvinceItemMapper.selectList(new QueryWrapper<StCHoldProvinceItemDO>().lambda()
                    .eq(StCHoldProvinceItemDO::getIsactive, "Y")
                    .eq(StCHoldProvinceItemDO::getHoldOrderId, stCHoldOrderDO.getId()));
            if(CollectionUtils.isEmpty(itemList)){
                throw new NDSException("请先维护策略明细，再审核！");
            }
        }
    }
}
