package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldProvinceItemMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderHoldConst;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderItemDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldProvinceItemDO;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.StAutoHoldRelation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/8/6 下午4:39
 * @Version 1.0
 */
@Slf4j
@Component
public class StAutoHoldNewService {
    @Autowired
    private StCHoldOrderMapper stCHoldOrderMapper;
    @Autowired
    private StCHoldOrderItemMapper stCHoldOrderItemMapper;
    @Autowired
    private StCHoldProvinceItemMapper stCHoldProvinceItemMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;


    public Map<Integer, List<StAutoHoldRelation>> selectStAutoHold(Long shopId, OcBOrder ocBOrder) {
        if (shopId == null || shopId <= 0) {
            return Maps.newHashMap();
        }
        String redisKey = StRedisKey.ST_HOLD_ORDER_SHOP_ID + shopId;
        String holdOrderSt = redisOpsUtil.strRedisTemplate.opsForValue().get(redisKey);
        List<StAutoHoldRelation> relation = null;
        if (StringUtils.isNotEmpty(holdOrderSt)) {
            relation = JSON.parseArray(holdOrderSt, StAutoHoldRelation.class);
        } else {
            relation = this.selectAutoHold(shopId);
            if (CollectionUtils.isNotEmpty(relation)) {
                String string = JSONObject.toJSONString(relation);
                redisOpsUtil.strRedisTemplate.opsForValue().set(redisKey, string);
            }
        }
        Map<Integer, List<StAutoHoldRelation>> holdRelationMap = distinguishType(relation, ocBOrder);
        return holdRelationMap;
    }

    private Map<Integer, List<StAutoHoldRelation>> distinguishType(List<StAutoHoldRelation> relations, OcBOrder ocBOrder) {
        //1卡  2Hold
        Map<Integer, List<StAutoHoldRelation>> holdRelationMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(relations)) {
            Date orderDate = ocBOrder.getOrderDate();
            Date payTime = ocBOrder.getPayTime();
            relations = relations.stream()
                    .filter(o -> (OcBOrderHoldConst.ORDER_DATE.equals(o.getStCHoldOrder().getDayType()) && orderDate.before(o.getStCHoldOrder().getEndTime()) && orderDate.after(o.getStCHoldOrder().getBeginTime()))
                            || (OcBOrderHoldConst.PAY_TIME.equals(o.getStCHoldOrder().getDayType()) && payTime.before(o.getStCHoldOrder().getEndTime()) && payTime.after(o.getStCHoldOrder().getBeginTime())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(relations)) {
                List<StAutoHoldRelation> cardSts = relations.stream().filter(p -> p.getStCHoldOrder().getStrategyType() == 1).collect(Collectors.toList());
                List<StAutoHoldRelation> holdSts = relations.stream().filter(p -> p.getStCHoldOrder().getStrategyType() == 2).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(cardSts)) {
                    holdRelationMap.put(1, cardSts);
                }
                if (CollectionUtils.isNotEmpty(holdSts)) {
                    holdRelationMap.put(2, holdSts);
                }
            }
        }
        return holdRelationMap;
    }

    private List<StAutoHoldRelation> selectAutoHold(Long shopId) {
        List<StCHoldOrderDO> stCHoldOrderDOS = stCHoldOrderMapper.selectStCHoldOrderList(shopId);
        if (CollectionUtils.isEmpty(stCHoldOrderDOS)) {
            return null;
        }
        List<Long> holdOrderIds = stCHoldOrderDOS.stream().map(StCHoldOrderDO::getId).collect(Collectors.toList());
        List<StCHoldOrderItemDO> stCHoldOrderItemDOS = stCHoldOrderItemMapper.selectStCHoldOrderItemByMainIds(holdOrderIds);
        List<StCHoldProvinceItemDO> stCHoldProvinceItemDOS = stCHoldProvinceItemMapper.selectStCHoldProvinceItemByMainIds(holdOrderIds);
        Map<Long, List<StCHoldOrderItemDO>> holdOrderItemMap = new HashMap<>();
        Map<Long, List<StCHoldProvinceItemDO>> holdOrderProvinceItemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOS)) {
            holdOrderItemMap = stCHoldOrderItemDOS.stream().collect(Collectors.groupingBy(StCHoldOrderItemDO::getHoldOrderId));
        }
        if (CollectionUtils.isNotEmpty(stCHoldProvinceItemDOS)) {
            holdOrderProvinceItemMap = stCHoldProvinceItemDOS.stream().collect(Collectors.groupingBy(StCHoldProvinceItemDO::getHoldOrderId));
        }
        List<StAutoHoldRelation> relations = new ArrayList<>();
        for (StCHoldOrderDO stCHoldOrderDO : stCHoldOrderDOS) {
            Long id = stCHoldOrderDO.getId();
            StAutoHoldRelation relation = new StAutoHoldRelation();
            relation.setStCHoldOrder(stCHoldOrderDO);
            relation.setStCHoldOrderItems(holdOrderItemMap.get(id));
            relation.setStCHoldProvinceItems(holdOrderProvinceItemMap.get(id));
            relations.add(relation);
        }
        return relations;
    }
}
