package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.StRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.request.StStockPriorityRequest;
import com.jackrain.nea.st.model.result.SyncStockStrategyQueryResult;
import com.jackrain.nea.st.model.vo.StCSyncStockStrategyVo;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 同步库存策略
 *
 * @author: heliu
 * @since: 2019/3/17
 * create at : 2019/3/17 18:43
 */
@Component
@Slf4j
public class OmsSyncStockStrategyService {

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    /**
     * 查找店铺同步策略下面的逻辑供货仓
     *
     * @param cpCShopId 店铺Id
     * @param storeList 取交集的逻辑仓
     * @return List<Long>
     */
    public List<Long> queryShopStoreList(Long cpCShopId, List<Long> storeList) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryShopStoreList入参","queryShopStoreList入参", cpCShopId), "逻辑仓集合" + storeList);
        }
        List<StStockPriorityRequest> shopStoreList = queryStStockPriority(cpCShopId, storeList);
        List<Long> storeTempList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(shopStoreList)) {
            storeTempList = shopStoreList.stream().map(StStockPriorityRequest::getSupplyStoreId).collect(Collectors.toList());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("走缓存过滤queryShopStoreList出参{}","走缓存过滤queryShopStoreList出参", cpCShopId), JSONObject.toJSONString(storeTempList));
        }
        return storeTempList;
    }

    /**
     * 查找店铺同步库存逻辑供货仓的优先级[供需要取逻辑仓优先级交集和不取交集的情况]
     *
     * @param cpCShopId 店铺Id
     * @param storeList 取交集的逻辑仓
     * @return List<Long>
     */
    public List<StStockPriorityRequest> queryStStockPriority(Long cpCShopId, List<Long> storeList) {

        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("queryStStockPriority入参{}","queryStStockPriority入参", cpCShopId), JSONObject.toJSONString(storeList));
            }

            // @20200617 接口从ST迁移到CP，对应的参数对象变更StCChangeStrategyItemDO -> CpCOrgChannelItemEntity
            List<StStockPriorityRequest> stockStrategyItemListTemp = Lists.newArrayList();
            List<CpCOrgChannelItemEntity> stockStrategyItemList = querySyncStockStrategyItemList(cpCShopId);
            if (CollectionUtils.isNotEmpty(stockStrategyItemList)) {
                for (CpCOrgChannelItemEntity syncStockStrategyItem : stockStrategyItemList) {
                    if (syncStockStrategyItem.getCpCStoreId() != null) {
                        if (storeList.contains(syncStockStrategyItem.getCpCStoreId()) && "Y".equals(syncStockStrategyItem.getIsactive())) {
                            StStockPriorityRequest stStockPriorityRequestList = new StStockPriorityRequest();
                            stStockPriorityRequestList.setPriority(stockStrategyItemList.indexOf(syncStockStrategyItem) + 1);
                            stStockPriorityRequestList.setSupplyStoreId(syncStockStrategyItem.getCpCStoreId());
                            stStockPriorityRequestList.setSupplyStoreEcode(syncStockStrategyItem.getCpCStoreEcode());
                            stStockPriorityRequestList.setSupplyStoreEname(syncStockStrategyItem.getCpCStoreEname());
                            stockStrategyItemListTemp.add(stStockPriorityRequestList);
                        }
                    }
                }
            } else {
                for (Long storeId : storeList) {
                    //根据仓id查询逻辑仓信息
                    CpStore cpStore = cpRpcService.selectCpCStoreById(storeId);
                    //is_main_warehouse
                    if (cpStore.getIsMainWarehouse() == 1) {
                        StStockPriorityRequest stStockPriorityRequestList = new StStockPriorityRequest();
                        stStockPriorityRequestList.setPriority(1);
                        stStockPriorityRequestList.setSupplyStoreId(cpStore.getId());
                        stStockPriorityRequestList.setSupplyStoreEcode(cpStore.getEcode());
                        stStockPriorityRequestList.setSupplyStoreEname(cpStore.getEname());
                        stockStrategyItemListTemp.add(stStockPriorityRequestList);
                        break;
                    }
                }
                //如果店铺下逻辑发货仓为空，则找默认发货仓下为主仓的逻辑仓传给库存中心

            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("queryStStockPriority出参{}","queryStStockPriority出参", cpCShopId), JSONObject.toJSONString(stockStrategyItemListTemp));
            }
            return stockStrategyItemListTemp;
        } catch (Exception ex) {
            log.error(LogUtil.format("查找店铺同步库存逻辑供货仓的优先级[供需要取逻辑仓优先级交集和不取交集的情况出参", "查找店铺同步库存逻辑供货仓的优先级异常", cpCShopId), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 查找店铺供货逻辑仓缓存
     *
     * @param shopId 店铺Id
     * @return 供货逻辑仓优先级
     */
    @SuppressWarnings("unchecked")
    public List<CpCOrgChannelItemEntity> querySyncStockStrategyItemList(Long shopId) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查找店铺供货逻辑仓优先级querySyncStockStrategyItemList入参{}","querySyncStockStrategyItemList入参", shopId));
            }
            List<CpCOrgChannelItemEntity> querySyncChannelItemList = Lists.newArrayList();
            //店铺同步库存策略缓存开关
            List<CpCOrgChannelItemEntity> stockStrategyItemList;
            String stockStrategyItemListStr = null;
            Boolean isEnableCached = this.omsSystemConfig.isTobeConfirmedCacheSyncStockStrategyEnabled();
            String redisKey = OmsRedisKeyResources.buildLockSyncStockStrategyItemRedisKey(shopId);
            if (isEnableCached) {
                CusRedisTemplate<String, String> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();

                Boolean isKeyExist = objRedisTemplate.hasKey(redisKey);
                if (isKeyExist != null && isKeyExist) {
                    stockStrategyItemListStr  = objRedisTemplate.opsForValue().get(redisKey);
                }
            }
            if (StringUtils.isEmpty(stockStrategyItemListStr)) {
                querySyncChannelItemList = stRpcService.querySyncStockStrategy(shopId);
                stockStrategyItemList = Lists.newArrayList();
                for (CpCOrgChannelItemEntity channelItemInfo : querySyncChannelItemList) {
                    if ("Y".equals(channelItemInfo.getIsactive())) {
                        CpCOrgChannelItemEntity entity = new CpCOrgChannelItemEntity();
                        BeanUtils.copyProperties(channelItemInfo, entity);
                        stockStrategyItemList.add(entity);
                    }
                }
                // 存放在redis中
                if (isEnableCached) {
                    RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, JSON.toJSONString(stockStrategyItemList), StRedisKeyResources.getCacheTimeConf(), TimeUnit.HOURS);
                }
            } else {
                stockStrategyItemList = JSON.parseArray(stockStrategyItemListStr, CpCOrgChannelItemEntity.class);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查找店铺供货逻辑仓优先级querySyncStockStrategyItemList出参{}","querySyncStockStrategyItemList出参", shopId), JSONObject.toJSONString(stockStrategyItemList), JSONObject.toJSONString(querySyncChannelItemList));
            }
            return stockStrategyItemList;
        } catch (Exception ex) {
            log.error(LogUtil.format("查找店铺同步库存逻辑供货仓的优先级[供需要取逻辑仓优先级交集和不取交集的情况]", "供货优先级列表异常", shopId), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 查所有店铺的店铺同步库存策略
     *
     * @return
     */
    public List<SyncStockStrategyQueryResult> selectAllSyncStockStrategy() {
        try {
            List<SyncStockStrategyQueryResult> syncStockStrategyQueryResultTemp = Lists.newArrayList();
            String redisKey = OmsRedisKeyResources.bulidLockAllStCSyncStockStrategyKey();
            CusRedisTemplate<String, List<SyncStockStrategyQueryResult>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            List<SyncStockStrategyQueryResult> syncStockStrategyQueryResultListTmp = objRedisTemplate.opsForValue().get(redisKey);
            if (CollectionUtils.isNotEmpty(syncStockStrategyQueryResultListTmp)) {
                syncStockStrategyQueryResultTemp = syncStockStrategyQueryResultListTmp;
            } else {
                List<SyncStockStrategyQueryResult> syncStockStrategyQueryResultList = stRpcService.queryAllSyncStockStrategy();
                if (CollectionUtils.isNotEmpty(syncStockStrategyQueryResultList)) {
                    // @20200831 存放在redis中, 设置超时2分钟一次，后续需要优化，更新测试来清理缓存
                    RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, syncStockStrategyQueryResultList, 1000 * 60 * 2, TimeUnit.MILLISECONDS);

                }
                syncStockStrategyQueryResultTemp = syncStockStrategyQueryResultList;
            }

            return syncStockStrategyQueryResultTemp;
        } catch (Exception ex) {
            log.error(LogUtil.format("查所有店铺的店铺同步库存策略selectAllSyncStockStrategy", "查所有店铺的店铺同步库存策略异常"), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 查找店铺同步策略下面的逻辑供货仓[没有交集的情况]
     *
     * @param shopId 店铺Id
     * @return List<Long>
     */
    public List<Long> queryShopStoreNextList(Long shopId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryShopStoreNextList入参","queryShopStoreNextList入参", shopId));
        }
        List<CpCOrgChannelItemEntity> orgChannelItemList = querySyncStockStrategyItemList(shopId);
        List<Long> logicStoreIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(orgChannelItemList)) {
            logicStoreIdList = orgChannelItemList.stream().map(CpCOrgChannelItemEntity::getCpCStoreId).collect(Collectors.toList());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("走缓存过滤queryShopStoreNextList出参","走缓存过滤queryShopStoreNextList出参", shopId),JSONObject.toJSONString(logicStoreIdList));
        }
        return logicStoreIdList;
    }

    /**
     * 查询店铺同步库存策略，获取逻辑仓优先级
     *
     * @param cpCShopId
     * @return
     */
    public List<StCSyncStockStrategyVo> selectSyncStockStrategyByPriority(Long cpCShopId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("querySyncStockStrategyItemByID","querySyncStockStrategyItemByID", cpCShopId));
        }
        List<StCSyncStockStrategyVo> shopStoreList = stRpcService.selectSyncStockStrategyByCpCShopId(cpCShopId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("querySyncStockStrategyItemByID","querySyncStockStrategyItemByID", cpCShopId), JSONObject.toJSONString(shopStoreList));
        }
        return shopStoreList;
    }
}
