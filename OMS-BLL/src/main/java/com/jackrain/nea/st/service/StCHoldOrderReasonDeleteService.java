package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderReasonMapper;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import com.jackrain.nea.resource.RedisKeyConst;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.R3ParamUtils;
import com.jackrain.nea.util.RedisCacheUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: 秦雄飞
 * @time: 2023/2/15 16:49
 * @description: HOLD单原因删除
 */
@Component
@Slf4j
public class StCHoldOrderReasonDeleteService {

    @Autowired
    private StCHoldOrderReasonMapper stCHoldOrderReasonMapper;

    @Transactional
    public ValueHolder deleteHoldOrderReason(SgR3BaseRequest request) {
        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("HOLD单原因删除.param={}",
                    "deleteHoldOrderReason.param"), JSONObject.toJSONString(request));
        }
        List<Long> batchObjIds = R3ParamUtils.getBatchObjIds(request);
        if (CollectionUtils.isEmpty(batchObjIds)) {
            throw new NDSException("请选择记录！");
        }
        List<StCHoldOrderReason> stCHoldOrderReasonList = stCHoldOrderReasonMapper.selectBatchIds(batchObjIds);
        if (CollectionUtils.isEmpty(stCHoldOrderReasonList)) {
            throw new NDSException("所选记录已不存在！");
        }
        Map<Long, Object> errorMap = Maps.newHashMap();
        Map<Long, StCHoldOrderReason> stCHoldOrderReasonMap =
                stCHoldOrderReasonList.stream().collect(Collectors.toMap(StCHoldOrderReason::getId, Function.identity()));
        List<Long> deleteIdList = Lists.newArrayList();
        try {
            for (Long id : batchObjIds) {
                StCHoldOrderReason stCHoldOrderReason = stCHoldOrderReasonMap.get(id);
                if (Objects.isNull(stCHoldOrderReason)) {
                    errorMap.put(id, "当前记录已不存在！");
                    continue;
                }
                Integer dataType = stCHoldOrderReason.getDataType();
                if (Objects.nonNull(dataType) && NumberUtils.INTEGER_ONE.equals(dataType)) {
                    errorMap.put(id, "系统初始化数据，无法删除！");
                    continue;
                }
                deleteIdList.add(id);
            }
            if (CollectionUtils.isNotEmpty(deleteIdList)) {
                stCHoldOrderReasonMapper.deleteBatchIds(deleteIdList);
                RedisCacheUtil.deleteStr(deleteIdList, RedisKeyConst.HOLD_ORDER_REASON_KEY);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("HOLD单原因删除.error={}",
                    "deleteHoldOrderReason.error"), Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.getFailValueHolder(e.getMessage());
        }
        return R3ParamUtils.getExcuteValueHolder(batchObjIds.size(), errorMap);
    }
}