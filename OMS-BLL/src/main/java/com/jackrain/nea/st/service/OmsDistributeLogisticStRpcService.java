package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.request.ExpressAreaRequest;
import com.jackrain.nea.st.model.request.WarehouseLogisticsRankRequest;
import com.jackrain.nea.st.model.table.*;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @author: hulinyang
 * @since: 2019/9/17
 * create at : 2019/9/17 14:14
 */
@Component
@Slf4j
public class OmsDistributeLogisticStRpcService {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private StRpcService stRpcService;

    /**
     * 根据订单中的,“省”,“市”,“区”查询物流区域设置表中可用的物流公司列表
     *
     * @param provinceId 省
     * @param cityId     市
     * @param areaId     区
     * @return List<Long>
     */
    public List<ExpressAreaRequest> selectLogisticsIdInfoByProvinceCityArea(Long provinceId, Long cityId, Long areaId, Long orderId) {

        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询物流区域设置表中可用的物流公司列表入参", "查询物流区域设置表中可用的物流公司列表入参", orderId), "provinceId =" + provinceId + ",cityId =" + cityId + ",areaId =" + areaId);
            }
            List<ExpressAreaRequest> stCExpressAreaInfoList = Lists.newArrayList();
            String redisHashKeyStr = "";
            if (provinceId != null) {
                redisHashKeyStr = provinceId.toString();
            }
            if (cityId != null) {
                redisHashKeyStr = redisHashKeyStr + "_" + cityId.toString();
            }
            if (areaId != null) {
                redisHashKeyStr = redisHashKeyStr + "_" + areaId.toString();
            }
            String redisKey = OmsRedisKeyResources.bulidLockStExpressAreaRequestKey();
            //判断key是否存在
            CusRedisTemplate<String, List<ExpressAreaRequest>> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
            Boolean isExist = redisTemplate.opsForHash().hasKey(redisKey, redisHashKeyStr);
            if (!isExist) {
                log.debug(LogUtil.format("selectLogisticsIdInfoByProvinceCityArea,走redis未获取到当前key的数据，然后走rpc调用接口获取数据", "查询物流区域设置表中可用的物流公司列表入参", orderId), "provinceId =" + provinceId + ",cityId =" + cityId + ",areaId =" + areaId);
                stCExpressAreaInfoList = stRpcService
                        .selectLogisticsIdInfoByProvinceCityArea(provinceId, cityId, areaId);
                if (CollectionUtils.isNotEmpty(stCExpressAreaInfoList)) {
                    redisTemplate.opsForHash().put(redisKey, redisHashKeyStr, stCExpressAreaInfoList);
                }
            } else {
                Object object = redisTemplate.opsForHash().get(redisKey, redisHashKeyStr);
                if (object != null) {
                    stCExpressAreaInfoList = (List<ExpressAreaRequest>) object;
                }
            }
            return stCExpressAreaInfoList;
        } catch (Exception ex) {
            log.error(LogUtil.format("selectLogisticsIdInfoByProvinceCityArea,查询物流区域设置表中可用的物流公司列表异常", "查询物流区域设置表中可用的物流公司列表入参", orderId), "provinceId =" + provinceId + ",cityId =" + cityId + ",areaId =" + areaId, Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 查询所有物流方案（根据下单时间和当前时间过滤）,且根据rank升序,modifieddate降序
     *
     * @param orderDate
     * @param currentDate
     * @param orderId
     * @return
     */
    public List<StCExpressDO> selectStCExpress(Date orderDate, Date currentDate, Long orderId) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询所有已审核物流方案selectStCExpress入参", "查询所有已审核物流方案selectStCExpress入参", orderId), "currentDate =" + currentDate + "订单id =" + orderId);
            }
            List<StCExpressDO> stCExpressList = Lists.newArrayList();
            List<StCExpressDO> stCExpressTmpList = Lists.newArrayList();
            String redisKey = OmsRedisKeyResources.bulidLockStActiveExpressKey();
            // 读
            CusRedisTemplate<Object, Object> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            // 写
            CusRedisTemplate<Object, Object> setObjRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            //判断key是否存在
            stCExpressTmpList = (List<StCExpressDO>) objRedisTemplate.opsForValue().get(redisKey);
            List<StCExpressDO> stCIsActiveExpressList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(stCExpressTmpList)) {
                stCExpressList = stCExpressTmpList;
            } else {
                //根据发货仓库id 和订单商品数量获取物流分配规则明细中的物流公司
                stCIsActiveExpressList = stRpcService.selectStCExpressByCpCShopId(orderDate, currentDate);
                setObjRedisTemplate.opsForValue().set(redisKey, stCIsActiveExpressList);
                return stCIsActiveExpressList;
            }
            List<StCExpressDO> stCIsActiveExpressTmpList = Lists.newArrayList();
            //根据下单时间和当前时间过滤
            if (CollectionUtils.isNotEmpty(stCExpressList)) {
                for (StCExpressDO stCExpressDO : stCExpressList) {
                    if (stCExpressDO.getBeginTime().before(orderDate) && stCExpressDO.getEndTime().after(orderDate)) {
                        stCIsActiveExpressTmpList.add(stCExpressDO);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(stCIsActiveExpressTmpList)) {
                //按照rank升序
                stCIsActiveExpressList = stCIsActiveExpressTmpList.stream().sorted((Comparator.comparing(StCExpressDO::getRank)).thenComparing(StCExpressDO::getModifieddate, Comparator.reverseOrder())).collect(Collectors.toList());
                //stCIsActiveExpressTmpList = stCIsActiveExpressTmpList.stream().sorted(Comparator.comparing(StCExpressDO::getModifieddate).reversed().thenComparing(StCExpressDO::getRank)).collect(Collectors.toList());
/*                List<StCExpressDO> stCIsActiveExpressTmpTwoList = new ArrayList<>();
                for(StCExpressDO stCExpressDOTMP:stCIsActiveExpressTmpList){
                    if(stCIsActiveExpressTmpList.get(0).getRank().equals(stCExpressDOTMP.getRank())){
                        stCIsActiveExpressTmpTwoList.add(stCExpressDOTMP);
                    }
                }
                //按照modifieddate降序
                stCIsActiveExpressList = stCIsActiveExpressTmpTwoList.stream().sorted(Comparator.comparing(StCExpressDO::getModifieddate).reversed()).collect(Collectors.toList());
                log.debug("订单OrderId" + orderId + "分配物流服务,查询所有物流方案，开始根据下单时间和当前时间过滤，根据rank升序后，再根据modifieddate降序后stCIsActiveExpressList的值为："+stCIsActiveExpressList);*/
            }
            return stCIsActiveExpressList;
        } catch (Exception ex) {
            log.error(LogUtil.format("selectStCExpress,查询所有物流方案处理时发生异常", "selectStCExpress,查询所有物流方案处理时发生异常", orderId), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 根据发货仓库id获取物流分配规则明细中的物流公司（过滤商品数量）
     *
     * @param phyWarehouseId
     * @param orderId
     * @return
     */
    public List<StCExpressAllocationItemDO> selectLogisticsIdBycpCPhyWarehouseId(Long phyWarehouseId, BigDecimal qtyAll, Long orderId) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询所有已审核物流方案selectStCExpress入参", "selectLogisticsIdBycpCPhyWarehouseId", orderId), "phyWarehouseId =" + phyWarehouseId);
            }
            List<StCExpressAllocationItemDO> stCExpressAllocationItemList = Lists.newArrayList();
            List<StCExpressAllocationItemDO> stCExpressAllocationItemTmpList = Lists.newArrayList();
            String redisKey = OmsRedisKeyResources.bulidLockStExpressAllocationItemKey(phyWarehouseId);

            CusRedisTemplate<Object, Object> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            printLog("订单OrderId:{},判断查询仓库物流分配规则明细key是否存在:{}", orderId, objRedisTemplate.hasKey(redisKey));
            stCExpressAllocationItemTmpList = (List<StCExpressAllocationItemDO>) objRedisTemplate.opsForValue().get(redisKey);
            log.debug(LogUtil.format("取redis仓库物流分配规则明细中的物流公司数据", "selectLogisticsIdBycpCPhyWarehouseId", orderId), stCExpressAllocationItemTmpList);
            if (CollectionUtils.isNotEmpty(stCExpressAllocationItemTmpList)) {
                stCExpressAllocationItemList = stCExpressAllocationItemTmpList;
            } else {
                //根据发货仓库id 和订单商品数量获取物流分配规则明细中的物流公司
                stCExpressAllocationItemList = stRpcService.selectLogisticsIdBycpCPhyWarehouseId(phyWarehouseId, qtyAll);
                CusRedisTemplate<Object, List<StCExpressAllocationItemDO>> setObjRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
                setObjRedisTemplate.opsForValue().set(redisKey, stCExpressAllocationItemList);
                log.debug(this.getClass().getName() + "分配物流服务,根据发货仓库id 和订单商品数量获取物流分配规则明细中的物流公司为：" + stCExpressAllocationItemList.toString() + "订单id为=" + orderId);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询仓库物流分配规则明细表stCExpressAllocationItemList出参", "selectLogisticsIdBycpCPhyWarehouseId", orderId), JSONObject.toJSONString(stCExpressAllocationItemList));
            }
            //符合商品数量的stCExpressAllocationItemList
            List<StCExpressAllocationItemDO> stCExpressAllocationItemActiveList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(stCExpressAllocationItemList)) {
                for (StCExpressAllocationItemDO stCExpressAllocationItemDO : stCExpressAllocationItemList) {
                    stCExpressAllocationItemActiveList.add(stCExpressAllocationItemDO);
                }
            }
            return stCExpressAllocationItemActiveList;
        } catch (Exception ex) {
            log.error(LogUtil.format("获取仓库物流分配规则明细中的物流公司列表异常", "selectLogisticsIdBycpCPhyWarehouseId", orderId), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 查询物流方案对应的仓库明细数据
     *
     * @param expressId 方案id
     * @param orderId   订单id
     * @return Long
     */
    public List<StCExpressWarehouseItemDO> selectStCExpressWarehouseItemInfo(Long expressId, Long orderId) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectStCExpressWarehouseItemInfo", "selectStCExpressWarehouseItemInfo", orderId), "方案Id" + expressId);
            }
            String redisKey = OmsRedisKeyResources.bulidLockStExpressWarehouseItemKey(expressId);
            List<StCExpressWarehouseItemDO> stCExpressWarehouseItemList = Lists.newArrayList();
            CusRedisTemplate<Object, Object> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            stCExpressWarehouseItemList = (List<StCExpressWarehouseItemDO>) objRedisTemplate.opsForValue().get(redisKey);
            if (CollectionUtils.isNotEmpty(stCExpressWarehouseItemList)) {
                return stCExpressWarehouseItemList;
            } else {
                stCExpressWarehouseItemList = stRpcService.selectStCExpressWarehouseItemInfo(expressId);
                CusRedisTemplate<Object, Object> setObjRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
                setObjRedisTemplate.opsForValue().set(redisKey, stCExpressWarehouseItemList);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询物流方案对应的仓库明细表stCExpressWarehouseItemList出参", "selectStCExpressWarehouseItemInfo", orderId), JSONObject.toJSONString(stCExpressWarehouseItemList));
            }
            return stCExpressWarehouseItemList;
        } catch (Exception ex) {
            log.error(LogUtil.format("查询物流方案对应的仓库明细数据异常", "selectStCExpressWarehouseItemInfo", orderId), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 查询查询物流方案区域明细数据
     *
     * @param expressId 方案id
     * @param orderId   订单id
     * @return Long
     */
    public List<StCExpressPlanAreaItemDO> selectStCExpressPlanAreaItem(Long expressId, Long orderId) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectStCExpressPlanAreaItem", "selectStCExpressPlanAreaItem", orderId), "方案Id" + expressId);
            }
            String redisKey = OmsRedisKeyResources.bulidLockStExpressPlanAreaItemKey(expressId);
            List<StCExpressPlanAreaItemDO> stCExpressPlanAreaItemList = Lists.newArrayList();
            // 读redis数据
            CusRedisTemplate<Object, List<StCExpressPlanAreaItemDO>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            // 打印log
            stCExpressPlanAreaItemList = objRedisTemplate.opsForValue().get(redisKey);
            if (CollectionUtils.isNotEmpty(stCExpressPlanAreaItemList)) {
                return stCExpressPlanAreaItemList;
            } else {
                stCExpressPlanAreaItemList = stRpcService.selectStCExpressPlanAreaItem(expressId);
                RedisMasterUtils.getObjRedisTemplate().opsForValue().set(redisKey, stCExpressPlanAreaItemList);
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, stCExpressPlanAreaItemList);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询查询物流方案区域明细表stCExpressPlanAreaItemList出参", "selectStCExpressPlanAreaItem", orderId), JSONObject.toJSONString(stCExpressPlanAreaItemList));
            }
            return stCExpressPlanAreaItemList;
        } catch (Exception ex) {
            log.error(LogUtil.format("查询查询物流方案区域明细数据异常", "selectStCExpressPlanAreaItem", orderId), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 查询查询物流方案包裹明细数据
     *
     * @param expressId 方案id
     * @param orderId   订单id
     * @return Long
     */
    public List<StCExpressPackageDO> selectStCExpressPackageInfo(Long expressId, Long orderId) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectStCExpressPackageInfo", "selectStCExpressPackageInfo", orderId), "方案Id" + expressId);
            }
            String redisKey = OmsRedisKeyResources.bulidLockStExpressPackageKey(expressId);
            List<StCExpressPackageDO> stCExpressPackageList = Lists.newArrayList();
            // 读redis数据
            CusRedisTemplate<Object, List<StCExpressPackageDO>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            // 打印log
            stCExpressPackageList = objRedisTemplate.opsForValue().get(redisKey);
            if (CollectionUtils.isNotEmpty(stCExpressPackageList)) {
                return stCExpressPackageList;
            } else {
                stCExpressPackageList = stRpcService.selectStCExpressPackageInfo(expressId);
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, stCExpressPackageList);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询物流方案包裹明细表stCExpressPackageList出参", "查询物流方案包裹明细表stCExpressPackageList出参", orderId), JSONObject.toJSONString(stCExpressPackageList));
            }
            return stCExpressPackageList;
        } catch (Exception ex) {
            log.error(LogUtil.format("查询物流方案包裹明细数据异常", "selectStCExpressPackageInfo", orderId), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 查询物流方案对应的商品明细数据
     *
     * @param expressId 方案id
     * @param orderId   订单id
     * @return Long
     */
    public List<StCExpressProItemDO> selectStCExpressProItem(Long expressId, Long orderId) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectStCExpressProItem", "selectStCExpressProItem", orderId), "方案Id" + expressId);
            }
            String redisKey = OmsRedisKeyResources.bulidLockStExpressProItemKey(expressId);
            List<StCExpressProItemDO> stCExpressProItemList = Lists.newArrayList();
            // 读redis数据
            CusRedisTemplate<Object, List<StCExpressProItemDO>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            // 打印log
            stCExpressProItemList = objRedisTemplate.opsForValue().get(redisKey);
            if (CollectionUtils.isNotEmpty(stCExpressProItemList)) {
                return stCExpressProItemList;
            } else {
                stCExpressProItemList = stRpcService.selectStCExpressProItem(expressId);
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, stCExpressProItemList);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询物流方案对应的商品明细表stCExpressProItemList出参", "stCExpressProItemList", orderId), JSONObject.toJSONString(stCExpressProItemList));
            }
            return stCExpressProItemList;
        } catch (Exception ex) {
            log.error(LogUtil.format("查询物流方案对应的商品明细数据异常", "selectStCExpressProItem", orderId), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 查询仓库下物流公司省市及其优先级数据
     *
     * @param orderInfo                     订单id
     * @param wareHouseId                   方案id
     * @param warehouseLogisticsRankRequest
     * @return Long
     */
    public ValueHolderV14<StCWarehouseLogisticsRankDO> queryLogisticsRankInfo(OcBOrder orderInfo, Long wareHouseId,
                                                                              WarehouseLogisticsRankRequest warehouseLogisticsRankRequest) {
        ValueHolderV14<StCWarehouseLogisticsRankDO> vh = new ValueHolderV14();
        try {
            String redisKey = OmsRedisKeyResources.bulidLockStWarehouseLogisticsRankKey();
            List<StCWarehouseLogisticsRankDO> rankList = Lists.newArrayList();
            // 读redis数据
            CusRedisTemplate<Object, List<StCExpressProItemDO>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            // 打印log
            String redisHashKeyStr = "";
            if (wareHouseId != null) {
                redisHashKeyStr = wareHouseId.toString();
            }
            if (orderInfo.getCpCRegionProvinceId() != null) {
                redisHashKeyStr = redisHashKeyStr + "_" + orderInfo.getCpCRegionProvinceId();
            }
            if (orderInfo.getCpCRegionCityId() != null) {
                redisHashKeyStr = redisHashKeyStr + "_" + orderInfo.getCpCRegionCityId();
            }
            //判断key是否存在
            CusRedisTemplate<String, List<StCWarehouseLogisticsRankDO>> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
            Boolean isExist = redisTemplate.opsForHash().hasKey(redisKey, redisHashKeyStr);
            if (!isExist) {
                vh = stRpcService.queryLogisticsRankInfo(warehouseLogisticsRankRequest);
                StCWarehouseLogisticsRankDO rankDo = vh.getData();
                rankList.add(rankDo);
                redisTemplate.opsForHash().put(redisKey, redisHashKeyStr, rankList);
            } else {
                Object object = redisTemplate.opsForHash().get(redisKey, redisHashKeyStr);
                if (object != null) {
                    rankList = (List<StCWarehouseLogisticsRankDO>) object;
                    if (CollectionUtils.isNotEmpty(rankList)) {
                        vh.setCode(ResultCode.SUCCESS);
                        if (CollectionUtils.isNotEmpty(rankList)) {
                            vh.setData(rankList.get(0));
                        } else {
                            vh.setData(null);
                        }
                        return vh;
                    } else {
                        vh = stRpcService.queryLogisticsRankInfo(warehouseLogisticsRankRequest);
                        StCWarehouseLogisticsRankDO rankDo = vh.getData();
                        rankList.add(rankDo);
                        redisTemplate.opsForHash().put(redisKey, redisHashKeyStr, rankList);
                    }
                } else {
                    vh = stRpcService.queryLogisticsRankInfo(warehouseLogisticsRankRequest);
                    StCWarehouseLogisticsRankDO rankDo = vh.getData();
                    rankList.add(rankDo);
                    redisTemplate.opsForHash().put(redisKey, redisHashKeyStr, rankList);
                }
            }

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询仓库下物流公司省市及其优先级vh出参", "queryLogisticsRankInfo", orderInfo.getId()), JSONObject.toJSONString(vh));
            }
            return vh;
        } catch (Exception ex) {
            log.error(LogUtil.format("查询仓库下物流公司省市及其优先级数据异常", "queryLogisticsRankInfo", orderInfo.getId()), Throwables.getStackTraceAsString(ex));
            return vh;
        }
    }

    /**
     * 打印log
     *
     * @param msg  日志描述
     * @param args 参数
     */
    private void printLog(String msg, Object... args) {
        if (log.isDebugEnabled()) {
            log.debug(msg, args);
        }
    }
}