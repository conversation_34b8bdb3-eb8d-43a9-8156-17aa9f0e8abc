package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCOrderPushDelayStrategy;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: ganquan
 * @Date Create In 2020/9/17 10:02
 * @Description: 订单推单延时策略
 */
@Slf4j
@Component
public class OrderPushDelayStrategyService {

    /**
     * 一小时的毫秒数
     */
    private static final BigDecimal HOUR = BigDecimal.valueOf(3600000L);

    @Autowired
    private StRpcService stRpcService;

    /**
     * @param order
     * @return java.util.Date
     * <AUTHOR>
     * @Description 获取订单推单延时后的推单时间
     * @Date 10:10 2020/9/17
     **/
    public Date getOrderPushDelayDate(OcBOrder order) {
        if (order == null) {
            return null;
        }
        Date payTime = order.getPayTime();
        Long cpCShopId = order.getCpCShopId();
        Date now = new Date();
        if (payTime == null || cpCShopId == null) {
            return now;
        }
        StCOrderPushDelayStrategy strategy = stRpcService.queryOrderPushDelayStrategy(cpCShopId);
        if (strategy == null) {
            return now;
        }
        BigDecimal pushOrderDelaySetting = strategy.getPushOrderDelaySetting();
        if (pushOrderDelaySetting == null) {
            return now;
        }
        //Util方法只能传入整数，将小时转换为毫秒数
        int strategyTimeLong = pushOrderDelaySetting.multiply(HOUR).intValue();
        //付款时间+推单延时时间 小于 当前时间时 正常推单
        Date date = DateUtil.addDate(payTime, 14, strategyTimeLong);
        return date;
    }

}
