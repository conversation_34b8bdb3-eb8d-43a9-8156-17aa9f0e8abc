package com.jackrain.nea.st.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.config.BusinessSystem;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateMapper;
import com.jackrain.nea.oc.oms.model.enums.ExpiryDateTypeEnum;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/6/15 下午1:34
 * @Version 1.0
 */
@Slf4j
@Component
public class StExpiryDateCaseService {
    @Autowired
    private StCExpiryDateMapper stCExpiryDateMapper;
    @Autowired
    private StCExpiryDateItemMapper stCExpiryDateItemMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;
    @Autowired
    private StCExpiryDateLabelService stCExpiryDateLabelService;
    @Autowired
    private BusinessSystem businessSystem;
    ;

    @OmsOperationLog(operationType = "FINISH", mainTableName = "ST_C_EXPIRY_DATE", itemsTableName = "ST_C_EXPIRY_DATE_ITEM")
    public ValueHolder expiryDateCaseService(QuerySession querySession) {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        User user = querySession.getUser();
        JSONArray idsArray = param.getJSONArray("ids");
        List<Long> ids = JSON.parseArray(idsArray.toJSONString(), Long.class);
        HashMap<Long, Object> errMap = new HashMap();
        boolean sendMsgFlag = false;
        for (Long id : ids) {
            try {
                expiryDateAudit(id, user);
                sendMsgFlag = true;
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        if (sendMsgFlag) {
            //发送计算汇波标签策略消息
            stCExpiryDateLabelService.sendCalculateMsg(user);
        }
        return StBeanUtils.getExcuteValueHolder(ids.size(), errMap);
    }


    private void expiryDateAudit(Long id, User user) {
        StCExpiryDate expiryDate = stCExpiryDateMapper.selectById(id);
        if (expiryDate == null) {
            throw new NDSException("当前记录已不存在！");
        }
        if (expiryDate.getSubmitStatus() == null || expiryDate.getSubmitStatus() != 2) {
            throw new NDSException("当前策略的状态不是已审核，不允许结案！");
        }
        if (ExpiryDateTypeEnum.COMMUNAL.getKey().equals(expiryDate.getExpiryType())) {
            List<String> accessControl = businessSystem.getStCExpiryDateAccessControl();
            if (CollectionUtils.isEmpty(accessControl) || !accessControl.contains(user.getName())) {
                throw new NDSException("公共不支持结案，若要结案请联系IT");
            }
        }
        StCExpiryDate stCExpiryDate = new StCExpiryDate();
        stCExpiryDate.setId(id);
        stCExpiryDate.setSubmitStatus(4);
        stCExpiryDate.setModifiername(user.getName());
        stCExpiryDate.setModifierid(Long.valueOf(user.getId()));
        stCExpiryDate.setModifieddate(new Date());
        Long shopId = expiryDate.getShopId();
        int i1 = stCExpiryDateMapper.updateById(stCExpiryDate);
        if (i1 <= 0) {
            throw new NDSException("结案失败！");
        }
        if (ObjectUtil.equal(4, expiryDate.getExpiryType())) {
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_MEMBER_MATCH);
        }
        if (shopId == null) {
            Integer customerGrouping = expiryDate.getCustomerGrouping();
            if (customerGrouping == null) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_COMMON);
            } else {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_CUSTOMER_GROUP + customerGrouping);
            }
        } else {
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_SHOP_ID + shopId);
        }
    }

    /**
     * 商品效期策略 已审核且指定类型=店铺指定，若结束时间+业务系统参数值 是否小于等于 当前时间，若是则自动结案策略
     * 策略状态SUBMIT_STATUS： 已审核-2(1-未审核，2-已审核，3-已作废，4-已结案)
     * 类型EXPIRY_TYPE：店铺指定-2(共用1，客户分组3，会员效期4)
     * 付款结束时间-END_TIME
     *
     * @return
     */
    public int autoFinishByBusinessParams() {
        int stCExpiryDateFinishDays = businessSystem.getStCExpiryDateFinishDays();
        Date date = DateUtils.addDays(new Date(), -stCExpiryDateFinishDays);

        List<StCExpiryDate> dtoList = stCExpiryDateMapper.selectList(new QueryWrapper<StCExpiryDate>().lambda()
                .eq(StCExpiryDate::getSubmitStatus, 2)
                .eq(StCExpiryDate::getExpiryType, 2)
                .le(StCExpiryDate::getEndTime, date));

        if (CollectionUtils.isEmpty(dtoList)) {
            log.debug("没有符合条件商品效期策略");
            return 0;
        }

        int count = 0;
        for (StCExpiryDate dto : dtoList) {
            try {
                expiryDateAudit(dto.getId(), R3SystemUserResource.getSystemRootUser());
                count++;
            } catch (Exception e) {
                log.warn("ID:{},商品效期策略超过结束间N天自动结案异常：{}", dto.getId(), Throwables.getStackTraceAsString(e));
            }
        }

        return count;
    }
}
