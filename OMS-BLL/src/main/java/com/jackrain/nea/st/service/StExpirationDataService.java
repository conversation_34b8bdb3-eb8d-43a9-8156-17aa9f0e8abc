package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateMapper;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDate;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateItem;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.StExpiryDateRelation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/6/21 下午2:53
 * @Version 1.0
 */
@Component
public class StExpirationDataService {

    @Autowired
    private StCExpiryDateMapper stCExpiryDateMapper;
    @Autowired
    private StCExpiryDateItemMapper stCExpiryDateItemMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;


    public List<StExpiryDateRelation> selectExpirationData(Long shopId, Date payTime) {
        //先查询redis 使用指定店铺
        String redisKey = StRedisKey.ST_EXPIRY_DATA_SHOP_ID + shopId;
        String expiryDateStr = redisOpsUtil.strRedisTemplate.opsForValue().get(redisKey);
        List<StExpiryDateRelation> relation = null;
        if (StringUtils.isNotEmpty(expiryDateStr)) {
            List<StExpiryDateRelation> relations = JSON.parseArray(expiryDateStr, StExpiryDateRelation.class);
            relation = this.matchingExpiryDate(relations, payTime);
        } else {
            //缓存没有 重新查询
            List<StExpiryDateRelation> relations = this.selectExpirationData(shopId);
            if (CollectionUtils.isNotEmpty(relations)) {
                String string = JSONObject.toJSONString(relations);
                redisOpsUtil.strRedisTemplate.opsForValue().set(redisKey, string);
                relation = this.matchingExpiryDate(relations, payTime);
            }
        }
        return relation;
    }


    /**
     * 查询公共的
     * @return
     */
    public StExpiryDateRelation selectExpirationCommon(){
        //查看是否有公用
        String redisKeyCommon = StRedisKey.ST_EXPIRY_DATA_COMMON;
        String expiryDateStrCommon = redisOpsUtil.strRedisTemplate.opsForValue().get(redisKeyCommon);
        StExpiryDateRelation relation = null;
        if (StringUtils.isNotEmpty(expiryDateStrCommon)) {
            relation = JSON.parseObject(expiryDateStrCommon, StExpiryDateRelation.class);
        } else {
            StExpiryDateRelation dateRelation = selectExpirationDataCommon();
            if (dateRelation != null) {
                String string = JSONObject.toJSONString(dateRelation);
                redisOpsUtil.strRedisTemplate.opsForValue().set(redisKeyCommon, string);
                relation= dateRelation;
            }
        }
        return relation;
    }


    /**
     * 客户分组
     *
     * @return
     */
    public List<StExpiryDateRelation> selectExpirationCustomerGrouping(Integer customerGrouping, Date payTime){
        //查看是否有公用
        String redisKeyCustomerGroup = StRedisKey.ST_EXPIRY_DATA_CUSTOMER_GROUP + customerGrouping;
        String expiryDateStr = redisOpsUtil.strRedisTemplate.opsForValue().get(redisKeyCustomerGroup);
        List<StExpiryDateRelation> relation = null;
        if (StringUtils.isNotEmpty(expiryDateStr)) {
            List<StExpiryDateRelation> relations = JSON.parseArray(expiryDateStr, StExpiryDateRelation.class);
            relation = this.matchingExpiryDate(relations, payTime);
        } else {
            //缓存没有 重新查询
            List<StExpiryDateRelation> relations = this.selectExpirationCustomerGroupData(customerGrouping);
            if (CollectionUtils.isNotEmpty(relations)) {
                String string = JSONObject.toJSONString(relations);
                redisOpsUtil.strRedisTemplate.opsForValue().set(redisKeyCustomerGroup, string);
                relation = this.matchingExpiryDate(relations, payTime);
            }
        }
        return relation;
    }

    public List<StExpiryDateRelation> selectExpirationMemberMatch(Date payTime) {
        String memberMatch = StRedisKey.ST_EXPIRY_DATA_MEMBER_MATCH;
        String expiryDateStr = redisOpsUtil.strRedisTemplate.opsForValue().get(memberMatch);
        List<StExpiryDateRelation> relation = null;
        if (StringUtils.isNotEmpty(expiryDateStr)) {
            List<StExpiryDateRelation> relations = JSON.parseArray(expiryDateStr, StExpiryDateRelation.class);
            relation = this.matchingExpiryDate(relations, payTime);
        } else {
            //缓存没有 重新查询
            List<StExpiryDateRelation> relations = this.selectExpirationMemberMatch();
            if (CollectionUtils.isNotEmpty(relations)) {
                String string = JSONObject.toJSONString(relations);
                redisOpsUtil.strRedisTemplate.opsForValue().set(memberMatch, string);
                relation = this.matchingExpiryDate(relations, payTime);
            }
        }
        return relation;

    }


    private List<StExpiryDateRelation> matchingExpiryDate(List<StExpiryDateRelation> relations, Date payTime) {
        List<StExpiryDateRelation> dateRelations = new ArrayList<>();
        for (StExpiryDateRelation relation : relations) {
            StCExpiryDate expiryDate = relation.getExpiryDate();
            Long startTime = expiryDate.getStartTime().getTime();
            Long endTime = expiryDate.getEndTime().getTime();
            if (payTime.getTime() >= startTime && payTime.getTime() <= endTime) {
                dateRelations.add(relation);
            }
        }
        return dateRelations;
    }


    /**
     * 根据店铺查询数据(已审核)
     *
     * @param shopId
     * @return
     */
    private List<StExpiryDateRelation> selectExpirationData(Long shopId) {
        List<StCExpiryDate> stCExpiryDates = stCExpiryDateMapper.selectStCExpiryDateListByShopId(shopId);
        if (CollectionUtils.isEmpty(stCExpiryDates)) {
            return null;
        }
        List<Long> mainIds = stCExpiryDates.stream().map(StCExpiryDate::getId).collect(Collectors.toList());
        List<StCExpiryDateItem> expiryDateItems = stCExpiryDateItemMapper.selectStCExpiryDateItemByMainIds(mainIds);
        if (CollectionUtils.isEmpty(expiryDateItems)) {
            return null;
        }
        Map<Long, List<StCExpiryDateItem>> expiryDateMap = expiryDateItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getStCExpiryDateId));
        List<StExpiryDateRelation> relations = new ArrayList<>();
        for (StCExpiryDate stCExpiryDate : stCExpiryDates) {
            List<StCExpiryDateItem> expiryDateItems1 = expiryDateMap.get(stCExpiryDate.getId());
            if (CollectionUtils.isNotEmpty(expiryDateItems1)) {
                StExpiryDateRelation relation = new StExpiryDateRelation();
                relation.setExpiryDate(stCExpiryDate);
                relation.setExpiryDateItems(expiryDateItems1);
                relations.add(relation);
            }
        }
        if (CollectionUtils.isEmpty(relations)) {
            return null;
        }
        return relations;
    }


    private List<StExpiryDateRelation> selectExpirationCustomerGroupData(Integer customerGroup) {
        List<StCExpiryDate> stCExpiryDates = stCExpiryDateMapper.selectStCExpiryDateListByCustomerGroup(customerGroup);
        if (CollectionUtils.isEmpty(stCExpiryDates)) {
            return null;
        }
        List<Long> mainIds = stCExpiryDates.stream().map(StCExpiryDate::getId).collect(Collectors.toList());
        List<StCExpiryDateItem> expiryDateItems = stCExpiryDateItemMapper.selectStCExpiryDateItemByMainIds(mainIds);
        if (CollectionUtils.isEmpty(expiryDateItems)) {
            return null;
        }
        Map<Long, List<StCExpiryDateItem>> expiryDateMap = expiryDateItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getStCExpiryDateId));
        List<StExpiryDateRelation> relations = new ArrayList<>();
        for (StCExpiryDate stCExpiryDate : stCExpiryDates) {
            List<StCExpiryDateItem> expiryDateItems1 = expiryDateMap.get(stCExpiryDate.getId());
            if (CollectionUtils.isNotEmpty(expiryDateItems1)) {
                StExpiryDateRelation relation = new StExpiryDateRelation();
                relation.setExpiryDate(stCExpiryDate);
                relation.setExpiryDateItems(expiryDateItems1);
                relations.add(relation);
            }
        }
        if (CollectionUtils.isEmpty(relations)) {
            return null;
        }
        return relations;
    }

    /**
     * 获取会员策略信息
     *
     * @return
     */
    private List<StExpiryDateRelation> selectExpirationMemberMatch() {
        List<StCExpiryDate> stCExpiryDateList = stCExpiryDateMapper.selectStCExpiryDateMemberMatch();
        if (CollectionUtils.isEmpty(stCExpiryDateList)) {
            return null;
        }
        List<Long> mainIds = stCExpiryDateList.stream().map(StCExpiryDate::getId).collect(Collectors.toList());
        List<StCExpiryDateItem> expiryDateItems = stCExpiryDateItemMapper.selectStCExpiryDateItemByMainIds(mainIds);
        if (CollectionUtils.isEmpty(expiryDateItems)) {
            return null;
        }
        Map<Long, List<StCExpiryDateItem>> expiryDateMap = expiryDateItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getStCExpiryDateId));
        List<StExpiryDateRelation> relations = new ArrayList<>();
        for (StCExpiryDate stCExpiryDate : stCExpiryDateList) {
            List<StCExpiryDateItem> expiryDateItems1 = expiryDateMap.get(stCExpiryDate.getId());
            if (CollectionUtils.isNotEmpty(expiryDateItems1)) {
                StExpiryDateRelation relation = new StExpiryDateRelation();
                relation.setExpiryDate(stCExpiryDate);
                relation.setExpiryDateItems(expiryDateItems1);
                relations.add(relation);
            }
        }
        if (CollectionUtils.isEmpty(relations)) {
            return null;
        }
        return relations;

    }

    /**
     * 查询公用的(已审核)
     *
     * @return
     */
    private StExpiryDateRelation selectExpirationDataCommon() {
        List<StCExpiryDate> stCExpiryDates = stCExpiryDateMapper.selectStCExpiryDateCommon();
        if (CollectionUtils.isNotEmpty(stCExpiryDates)) {
            StCExpiryDate stCExpiryDate = stCExpiryDates.get(0);
            List<StCExpiryDateItem> expiryDateItems = stCExpiryDateItemMapper.selectStCExpiryDateItemListByMainId(stCExpiryDate.getId());
            if (CollectionUtils.isNotEmpty(expiryDateItems)) {
                StExpiryDateRelation relation = new StExpiryDateRelation();
                relation.setExpiryDate(stCExpiryDate);
                relation.setExpiryDateItems(expiryDateItems);
                return relation;
            }
        }
        return null;
    }
}
