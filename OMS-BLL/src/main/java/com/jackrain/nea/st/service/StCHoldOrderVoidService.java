package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderMapper;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.resource.RedisKeyConst;
import com.jackrain.nea.util.RedisCacheUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;

/**
 * @Descroption 订单HOLD单方案作废
 * <AUTHOR>
 * @Date 2020/07/02 16:42:00
 */
@Component
@Slf4j
public class StCHoldOrderVoidService {

    @Autowired
    private StCHoldOrderMapper mapper;

    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        HashMap<Long, Object> errMap = new HashMap<>();
        // 生成Json数组
        JSONArray voidArray = StBeanUtils.makeVoidJsonArray(param);
        // 列表批量
        if (!CollectionUtils.isEmpty(voidArray)) {
            for (int i = 0; i < voidArray.size(); i++) {
                // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                Long id = voidArray.getLong(i);
                try {
                    StCHoldOrderDO stCHoldOrder = mapper.selectById(id);
                    voidStCHoldOrder(stCHoldOrder, session);
                    Long shopId = stCHoldOrder.getCpCShopId() != null ? Long.parseLong(stCHoldOrder.getCpCShopId()) :
                            null;
                    RedisCacheUtil.delete(shopId, RedisKeyConst.SHOP_HOLD_ORDER_ST);
                } catch (Exception ex) {
                    errMap.put(id, ex.getMessage());
                }
            }
        }
        //返回日志
        return StBeanUtils.getExcuteValueHolder(voidArray.size(), errMap);
    }

    /**
     * @Descroption 订单HOLD单方案作废
     * <AUTHOR>
     * @Date 2020/07/02 16:42:00
     */
    private void voidStCHoldOrder(StCHoldOrderDO stCHoldOrder, QuerySession session) {
        //验证
        checkEstatus(stCHoldOrder);
        //作废
        StCHoldOrderDO stCHoldOrderDO = new StCHoldOrderDO();
        stCHoldOrderDO.setId(stCHoldOrder.getId());
        stCHoldOrderDO.setEstatus(OmsParamConstant.INT_THREE);
        StBeanUtils.makeModifierField(stCHoldOrderDO, session.getUser());
        int update = mapper.updateById(stCHoldOrderDO);
        if (update < 0) {
            throw new NDSException("作废失败！");
        }
        // 删除Redis缓存
        Long shopId = stCHoldOrderDO.getCpCShopId() == null ? null : Long.parseLong(stCHoldOrderDO.getCpCShopId());
        RedisCacheUtil.delete(shopId, RedisKeyConst.SHOP_HOLD_ORDER_ST);
        RedisCacheUtil.delete(shopId, StRedisKey.ST_HOLD_ORDER_SHOP_ID);
    }


    private void checkEstatus(StCHoldOrderDO stCHoldOrderDO) {
        if (stCHoldOrderDO == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            if (OmsParamConstant.INT_TWO==(stCHoldOrderDO.getEstatus())) {
                throw new NDSException("当前记录已审核，不允许作废！");
            }else if(OmsParamConstant.INT_THREE==(stCHoldOrderDO.getEstatus())){
                throw new NDSException("当前记录已作废，不允许作废！");
            }else if(OmsParamConstant.INT_FOUR==(stCHoldOrderDO.getEstatus())){
                throw new NDSException("当前记录已结案，不允许作废！");
            }
        }
    }

}
