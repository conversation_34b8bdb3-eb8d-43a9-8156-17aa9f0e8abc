package com.jackrain.nea.st.service;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.common.StCConstants;
import com.jackrain.nea.st.model.result.StCPriceResult;
import com.jackrain.nea.st.model.table.StCPriceDO;
import com.jackrain.nea.st.model.table.StCPriceExcludeItemDO;
import com.jackrain.nea.st.model.table.StCPriceItemDO;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: heliu
 * @since: 2019/3/19
 * create at : 2019/3/19 19:44
 */
@Component
@Slf4j
public class OmsOrderPriceSchemeService {

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private PsRpcService psRpcService;

    /***
     *
     * @param cpCphyWarehouseId 发货仓库
     * @param cpCogisticsId 物流公司ID
     * @return 限制数量
     */
    public Long queryPriceList(Long cpCphyWarehouseId, Long cpCogisticsId) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryPriceList入参", "queryPriceList入参"), "发货仓库Id" + cpCphyWarehouseId + "物流公司Id" + cpCogisticsId);
        }
        Long priceList = stRpcService.queryPriceList(cpCphyWarehouseId, cpCogisticsId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryPriceList出参", "queryPriceList出参"), priceList);
        }
        return priceList;
    }

    /**
     * 商品价格服务
     *
     * @param stCPriceList 价格方案
     * @param ocBOrder     订单对象
     * @param skuId        条码Id
     * @param proId        商品ID
     * @param price        成交价
     * @param psCProEcode  商品ecode
     * @return boolean
     */
    public String checkPriceScheme(List<Long> stCPriceList, OcBOrder ocBOrder, String skuId, Long proId,
                                   BigDecimal price, String psCProEcode) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("checkPriceScheme", "checkPriceScheme",ocBOrder.getId(), skuId, price), stCPriceList);
        }
        //查询店铺价格方案是否在有效期内
        if ((CollectionUtils.isEmpty(stCPriceList))) {
            return "success";
        } else {
            //判断价格策略明细是否存在,不存在,直接返回true,存在明细的时候取出第一条
            List<BigDecimal> lowPriceList = stRpcService.queryPriceTotal(stCPriceList, proId);
            if (CollectionUtils.isEmpty(lowPriceList)) {
                return "success";
            } else {
                BigDecimal lowerPrice = (lowPriceList.get(0) == null ? BigDecimal.ZERO : lowPriceList.get(0));
                //判断明细商品成交价是否低于策略明细最低成交价
                if (price.compareTo(lowerPrice) < 0) {
                    String message = " 订单中商品条码" + skuId + "成交价低于策略方案成交价"
                            + lowerPrice + ",其商品编码为" + psCProEcode;
                    return message;
                } else {
                    return "success";
                }
            }
        }
    }

    /**
     * 商品价格服务[手动审核用]
     *
     * @param stCPriceList 价格方案
     * @param ocBorderDto  订单对象
     * @param psCSkuEcode  条码Id
     * @param psCProId     商品ID
     * @param price        成交价
     * @param psCProEcode  商品ecode
     */
    public void checkNoAutoPriceScheme(List<Long> stCPriceList, OcBOrder ocBorderDto, String psCSkuEcode, Long psCProId,
                                       BigDecimal price, String psCProEcode) {

        List<BigDecimal> lowPriceList = stRpcService.queryPriceTotal(stCPriceList, psCProId);
        log.debug(LogUtil.format("最低成交价List", ocBorderDto.getId()), lowPriceList);
        if (CollectionUtils.isNotEmpty(lowPriceList)) {
            BigDecimal lowerPrice = (lowPriceList.get(0) == null ? BigDecimal.ZERO : lowPriceList.get(0));
            //判断明细商品成交价是否低于策略明细最低成交价
            if (price.compareTo(lowerPrice) < 0) {
                String message = "订单OrderId=" + ocBorderDto.getId() + "订单中商品条码" + psCSkuEcode + "成交价为"
                        + price + "低于价格明细策略方" + "案最低成交价" + lowerPrice + ",其商品编码为" + psCProEcode;
                throw new NDSException(Resources.getMessage(message));
            }
        }
    }

    /**
     * 根据商品价格策略排除订单明细
     * @param stCPriceResult
     * @param orderItemList
     */
    public void priceExcludeOrderItem(StCPriceResult stCPriceResult,List<OcBOrderItem> orderItemList){
        if(stCPriceResult == null){
            return;
        }
        if(CollectionUtils.isEmpty(orderItemList)){
            return;
        }
        /**排除商品明细*/
        List<StCPriceExcludeItemDO> stCPriceExcludeItemDOS = stCPriceResult.getStCPriceExcludeItemDOS();
        if(CollectionUtils.isEmpty(stCPriceExcludeItemDOS)){
            return;
        }
        List<OcBOrderItem> excludeItemList = new ArrayList<>();
        for(OcBOrderItem ocBOrderItem:orderItemList){
            String ptProName = ocBOrderItem.getPtProName();
            for(StCPriceExcludeItemDO excludeItemDO:stCPriceExcludeItemDOS){
                Integer discernRule = excludeItemDO.getDiscernRule();
                String discernContent = excludeItemDO.getDiscernContent();
                if(StCConstants.ST_C_PRICE_EXCLUDE_ITEM_1.equals(discernRule)){
                    /**识别规则：商品标题关键字*/
                    if(ptProName.contains(discernContent)){
                        excludeItemList.add(ocBOrderItem);
                        break;
                    }
                }else if(StCConstants.ST_C_PRICE_EXCLUDE_ITEM_2.equals(discernRule)){
                    if(ocBOrderItem.getPsCProEcode() != null && ocBOrderItem.getPsCProEcode().equals(discernContent)){
                        excludeItemList.add(ocBOrderItem);
                        break;
                    }
                }else if(StCConstants.ST_C_PRICE_EXCLUDE_ITEM_3.equals(discernRule)){
                    if(ocBOrderItem.getNumIid() != null && ocBOrderItem.getNumIid().equals(discernContent)){
                        excludeItemList.add(ocBOrderItem);
                        break;
                    }
                }
            }
        }
        if(CollectionUtils.isNotEmpty(excludeItemList)){
            orderItemList.removeAll(excludeItemList);
        }
    }


    /**
     * 订单明细价格校验
     * @param orderId
     * @param orderItemId
     * @param skuId
     * @param proId
     * @param price
     * @param psCProEcode
     * @param stCPriceResult
     * @return
     */
    public String checkOrderItemPrice(Long orderId,Long orderItemId, String skuId, Long proId,
                                   BigDecimal price, String psCProEcode,StCPriceResult stCPriceResult) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("订单明细价格校验！orderId={},orderItemId={},skuId={},price={}",
                    "OmsOrderPriceSchemeService"),orderId,orderItemId,skuId,price);
        }
        if (stCPriceResult == null) {
            return "success";
        }
        StCPriceDO stCPriceDO = stCPriceResult.getStCPriceDO();
         /**商品范围*/
        Integer proRange = stCPriceDO.getProRange();
        if(StCConstants.ST_C_PRICE_PRO_RANGE_1.equals(proRange)){
            //商品范围:全部商品，查询商品基价下限
            ProductSku productSku = psRpcService.selectProductSku(skuId);
            BigDecimal basePriceDown = productSku == null || productSku.getBasePriceDown() == null?BigDecimal.ZERO:productSku.getBasePriceDown();
            if(price.compareTo(basePriceDown) < 0){
                String message = " 订单中商品条码" + skuId + "成交价低于商品基价下限价格"
                        + basePriceDown + ",其商品编码为" + psCProEcode;
                return message;
            }
        }else if(StCConstants.ST_C_PRICE_PRO_RANGE_2.equals(proRange)){
            //商品范围:自定义商品
            List<StCPriceItemDO> stCPriceItemDOS = stCPriceResult.getStCPriceItemDOS();
            if(CollectionUtils.isEmpty(stCPriceItemDOS)){
                return "success";
            }
            //通过商品ID筛选明细
            List<StCPriceItemDO> priceItemList = stCPriceItemDOS.stream()
                    .filter(x -> x.getPsCProId().equals(proId))
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(priceItemList)){
                return "success";
            }
            StCPriceItemDO stCPriceItemDO = priceItemList.get(0);
            if(StCConstants.ST_C_PRICE_ITEM_PRICE_TYPE_1.equals(stCPriceItemDO.getPriceType())){
                //按商品基价下限  查询商品基价下限
                ProductSku productSku = psRpcService.selectProductSku(skuId);
                BigDecimal basePriceDown = productSku == null || productSku.getBasePriceDown() == null?BigDecimal.ZERO:productSku.getBasePriceDown();
                if(price.compareTo(basePriceDown) < 0){
                    String message = " 订单中商品条码" + skuId + "成交价低于商品基价下限价格"
                            + basePriceDown + ",其商品编码为" + psCProEcode;
                    return message;
                }
            }else if(StCConstants.ST_C_PRICE_ITEM_PRICE_TYPE_2.equals(stCPriceItemDO.getPriceType())){
                //自定义商品最低成交价
                BigDecimal qtyLowprice = stCPriceItemDO.getQtyLowprice() == null?BigDecimal.ZERO:stCPriceItemDO.getQtyLowprice();
                if(price.compareTo(qtyLowprice) < 0){
                    String message = " 订单中商品条码" + skuId + "成交价低于商品最低成交价"
                            + qtyLowprice + ",其商品编码为" + psCProEcode;
                    return message;
                }
            }
        }
        return "success";
    }
}