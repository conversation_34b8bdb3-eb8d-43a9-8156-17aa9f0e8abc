package com.jackrain.nea.st.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOperationLogMapper;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.EquityBarterExchangeTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.EquityBarterTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategy;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategyItem;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.vo.StCEquityBarterStrategyImpVo;
import com.jackrain.nea.psext.api.PsCSkuQueryCmd;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jackrain.nea.oc.oms.nums.StConstant.ST_C_EQUITY_BARTER_STRATEGY_ITEM;

/**
 * @author: lijin
 * @create: 2024-06-04
 * @description: 对等换货策略头明细导入
 **/
@Slf4j
@Component
public class StCEquityBarterStrategyImportService {
    @DubboReference(group = "cp-ext", version = "1.0")
    private CpShopQueryCmd cpShopQueryCmd;
    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCSkuQueryCmd psCSkuQueryCmd;
    @Autowired
    private StCEquityBarterStrategyMapper stCEquityBarterStrategyMapper;
    @Autowired
    private StCEquityBarterStrategyItemMapper stCEquityBarterStrategyItemMapper;
    @Resource
    private OcBOperationLogMapper operationLogMapper;
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    private static final String SEPARATOR = ":";

    public ValueHolderV14<String> queryTemplateDownloadUrl() {
        ValueHolderV14<String> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "对等换货策略导入模版下载成功！");
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String titleName = "注意：\n" +
                "1、红色标注项为必填项；\n" +
                "3、表头（1-2行）不允许修改，否则无法识别；\n" +
                "4、输入时，不允许有空隔，不允许有空行，否则无法识别；\n" +
                "5、换货商品和对等商品填商品编码，否则无法识别；\n" +
                "6、平台店铺、商品编码、需与基础档案保持一致，否者无法识别；\n" +
                "7、缺货不还原填是、否或空；";
        String[] mainNames = {"类型", "平台店铺", "换货商品", "换货商品名称", "换货数量", "对等商品", "对等商品名称", "对等数量", "缺货不还原"};
        String[] mustNames = {"类型", "换货商品", "换货数量", "对等商品", "对等数量"};
        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        hssfWorkbook = exportUtil.executeSheet(hssfWorkbook, "对等换货策略头明细导入头明细", titleName, mainList, mustList,
                Lists.newArrayList(), Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "对等换货策略头明细导入模板",
                user, "OSS-Bucket/EXPORT/ST_C_EQUITY_BARTER_STRATEGY/");
        holderV14.setData(putMsg);
        return holderV14;
    }

    /**
     * 头明细覆盖
     *
     * @param strategyImpVos
     * @param user
     * @return
     */
    public ValueHolderV14<String> importCoverDataList(List<StCEquityBarterStrategyImpVo> strategyImpVos, User user) {
        ValueHolderV14<String> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "对等换货策略头明细覆盖成功！");
        ValueHolderV14<String> v14 = exportErrorData(strategyImpVos, user);
        if (!v14.isOK()) {
            return v14;
        }
        //校验数据并分组
        List<String> shopNameList = new ArrayList<>();
        List<String> skuCodeList = new ArrayList<>();
        collectShopNameAndSkuCode(strategyImpVos, shopNameList, skuCodeList);
        //查询店铺信息
        Map<String, CpShop> shopMap = new HashMap<>();
        List<Long> shopIds = queryShopInfo(shopNameList, shopMap);
        //查询sku信息
        Map<String, PsCSku> skuMap = querySkuInfo(skuCodeList);
        checkExcelData(strategyImpVos, shopMap, skuMap);
        ValueHolderV14<String> holder = exportErrorData(strategyImpVos, user);
        if (!holder.isOK()) {
            return holder;
        }
        //根据类型分组
        List<StCEquityBarterStrategyImpVo> commonImpVoList = new ArrayList<>();
        Map<Long, List<StCEquityBarterStrategyImpVo>> shopImpVoMap = new HashMap<>();
        groupData(strategyImpVos, commonImpVoList, shopImpVoMap);
        //构建参数
        List<StCEquityBarterStrategy> strategyUpdateList = new ArrayList<>();
        List<StCEquityBarterStrategy> commonStrategyInsertList = new ArrayList<>();
        List<StCEquityBarterStrategyItem> commonStrategyItemInsertList = new ArrayList<>();
        List<StCEquityBarterStrategy> shopStrategyInsertList = new ArrayList<>();
        List<StCEquityBarterStrategyItem> shopStrategyItemInsertList = new ArrayList<>();
        List<Long> deleteOldStrategyItemIds = new ArrayList<>();
        List<OcBOperationLog> operationLogList = new ArrayList<>();
        buildCoverParam(user, shopIds, commonImpVoList, shopImpVoMap, commonStrategyInsertList, commonStrategyItemInsertList,
                shopStrategyInsertList, shopStrategyItemInsertList, deleteOldStrategyItemIds, operationLogList, strategyUpdateList);
        //保存并双删缓存
        StCEquityBarterStrategyImportService bean = ApplicationContextHandle.getBean(StCEquityBarterStrategyImportService.class);
        bean.delOldAndSaveNew(shopIds, shopImpVoMap, commonStrategyInsertList, commonStrategyItemInsertList, shopStrategyInsertList,
                shopStrategyItemInsertList, deleteOldStrategyItemIds, operationLogList, strategyUpdateList);
        return holderV14;
    }

    /**
     * 头明细追加
     *
     * @param strategyImpVos
     * @param user
     * @return
     */
    public ValueHolderV14<String> importAddDataList(List<StCEquityBarterStrategyImpVo> strategyImpVos, User user) {
        ValueHolderV14<String> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "对等换货策略头明细追加成功！");
        ValueHolderV14<String> v14 = exportErrorData(strategyImpVos, user);
        if (!v14.isOK()) {
            return v14;
        }
        //校验数据并分组
        List<String> shopNameList = new ArrayList<>();
        List<String> skuCodeList = new ArrayList<>();
        collectShopNameAndSkuCode(strategyImpVos, shopNameList, skuCodeList);
        //查询店铺信息
        Map<String, CpShop> shopMap = new HashMap<>();
        List<Long> shopIds = queryShopInfo(shopNameList, shopMap);
        //查询已经存在的策略
        StCEquityBarterStrategy oldCommonStrategy =
                stCEquityBarterStrategyMapper.selectOne(new LambdaQueryWrapper<StCEquityBarterStrategy>()
                        .eq(StCEquityBarterStrategy::getType, EquityBarterTypeEnum.COMMUNAL.getKey())
                        .eq(StCEquityBarterStrategy::getIsactive, YesNoEnum.Y.getKey())
                        .last("limit 1"));
        List<StCEquityBarterStrategyItem> oldCommonStrategyItemList = new ArrayList<>();
        if (oldCommonStrategy != null) {
            oldCommonStrategyItemList =
                    stCEquityBarterStrategyItemMapper.selectList(new LambdaQueryWrapper<StCEquityBarterStrategyItem>()
                            .eq(StCEquityBarterStrategyItem::getStCEquityBarterStrategyId, oldCommonStrategy.getId())
                            .eq(StCEquityBarterStrategyItem::getIsactive, YesNoEnum.Y.getKey()));
        }
        Map<Long, StCEquityBarterStrategy> oldShopStrategyMap = new HashMap<>();
        List<StCEquityBarterStrategyItem> oldShopStrategyItemList = queryOldShopStrategy(shopIds, oldShopStrategyMap);
        //查询sku信息
        Map<String, PsCSku> skuMap = querySkuInfo(skuCodeList);
        //校验表格内数据逻辑
        checkExcelData(strategyImpVos, shopMap, skuMap);
        ValueHolderV14<String> checkExcelDataHolder = exportErrorData(strategyImpVos, user);
        if (!checkExcelDataHolder.isOK()) {
            return checkExcelDataHolder;
        }
        //追加时校验数据是否已存在和主表是否存在
        checkExistData(strategyImpVos, oldCommonStrategy, oldCommonStrategyItemList, oldShopStrategyMap, oldShopStrategyItemList);
        ValueHolderV14<String> checkExistDataHolder = exportErrorData(strategyImpVos, user);
        if (!checkExistDataHolder.isOK()) {
            return checkExistDataHolder;
        }
        //根据类型分组
        List<StCEquityBarterStrategyImpVo> commonImpVoList = new ArrayList<>();
        Map<Long, List<StCEquityBarterStrategyImpVo>> shopImpVoMap = new HashMap<>();
        groupData(strategyImpVos, commonImpVoList, shopImpVoMap);
        //构建参数
        List<StCEquityBarterStrategyItem> commonStrategyItemInsertList = new ArrayList<>();
        List<StCEquityBarterStrategyItem> shopStrategyItemInsertList = new ArrayList<>();
        buildAddParam(user, oldCommonStrategy, oldShopStrategyMap, commonImpVoList, shopImpVoMap,
                commonStrategyItemInsertList, shopStrategyItemInsertList);
        //保存并双删缓存
        StCEquityBarterStrategyImportService bean = ApplicationContextHandle.getBean(StCEquityBarterStrategyImportService.class);
        bean.saveAddNew(shopIds, commonStrategyItemInsertList, shopStrategyItemInsertList);
        return holderV14;
    }

    /**
     * 构建参数（追加）
     *
     * @param user
     * @param oldCommonStrategy
     * @param oldShopStrategyMap
     * @param commonImpVoList
     * @param shopImpVoMap
     * @param commonStrategyItemInsertList
     * @param shopStrategyItemInsertList
     */
    private void buildAddParam(User user, StCEquityBarterStrategy oldCommonStrategy,
                               Map<Long, StCEquityBarterStrategy> oldShopStrategyMap,
                               List<StCEquityBarterStrategyImpVo> commonImpVoList,
                               Map<Long, List<StCEquityBarterStrategyImpVo>> shopImpVoMap,
                               List<StCEquityBarterStrategyItem> commonStrategyItemInsertList,
                               List<StCEquityBarterStrategyItem> shopStrategyItemInsertList) {
        buildCommonAddParam(user, oldCommonStrategy, commonImpVoList, commonStrategyItemInsertList);
        buildShopAddParam(user, oldShopStrategyMap, shopImpVoMap, shopStrategyItemInsertList);
    }

    /**
     * 构建店铺参数（追加）
     *
     * @param user
     * @param oldShopStrategyMap
     * @param shopImpVoMap
     * @param shopStrategyItemInsertList
     */
    private void buildShopAddParam(User user, Map<Long, StCEquityBarterStrategy> oldShopStrategyMap,
                                   Map<Long, List<StCEquityBarterStrategyImpVo>> shopImpVoMap,
                                   List<StCEquityBarterStrategyItem> shopStrategyItemInsertList) {
        if (MapUtils.isNotEmpty(shopImpVoMap)) {
            for (Long shopId : shopImpVoMap.keySet()) {
                StCEquityBarterStrategy stCEquityBarterStrategy = oldShopStrategyMap.get(shopId);
                if (stCEquityBarterStrategy != null) {
                    //追加指定店铺类型策略明细
                    for (StCEquityBarterStrategyImpVo strategyImpVo : shopImpVoMap.get(shopId)) {
                        StCEquityBarterStrategyItem strategyItem = new StCEquityBarterStrategyItem();
                        shopStrategyItemInsertList.add(strategyItem);
                        strategyItem.setId(ModelUtil.getSequence(StConstant.ST_C_EQUITY_BARTER_STRATEGY_ITEM));
                        strategyItem.setStCEquityBarterStrategyId(stCEquityBarterStrategy.getId());
                        strategyItem.setPsCSkuId(strategyImpVo.getSkuId());
                        strategyItem.setPsCSkuCode(strategyImpVo.getSkuCode());
                        strategyItem.setPsCSkuName(strategyImpVo.getSkuName());
                        strategyItem.setQty(strategyImpVo.getSkuQty());
                        strategyItem.setEquitySkuId(strategyImpVo.getEquitySkuId());
                        strategyItem.setEquitySkuCode(strategyImpVo.getEquitySkuCode());
                        strategyItem.setEquitySkuName(strategyImpVo.getEquitySkuName());
                        strategyItem.setEquityQty(strategyImpVo.getEquitySkuQty());
                        strategyItem.setOutStockNoRestore(strategyImpVo.getOutStockNoRestore());
                        if (strategyItem.getEquityQty().compareTo(strategyItem.getQty()) == 0) {
                            strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.EQUITY.getKey());
                        } else {
                            strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.MORE_FOR_LESS.getKey());
                        }
                        strategyItem.setEffectiveStatus("1");
                        strategyItem.setRemark("头明细追加");
                        OmsModelUtil.setDefault4Add(strategyItem, user);
                    }
                }
            }
        }
    }

    /**
     * 构建公用参数（追加）
     *
     * @param user
     * @param oldCommonStrategy
     * @param commonImpVoList
     * @param commonStrategyItemInsertList
     */
    private void buildCommonAddParam(User user, StCEquityBarterStrategy oldCommonStrategy,
                                     List<StCEquityBarterStrategyImpVo> commonImpVoList,
                                     List<StCEquityBarterStrategyItem> commonStrategyItemInsertList) {
        if (CollectionUtils.isNotEmpty(commonImpVoList) && oldCommonStrategy != null) {
            //追加公用类型策略明细
            for (StCEquityBarterStrategyImpVo strategyImpVo : commonImpVoList) {
                StCEquityBarterStrategyItem strategyItem = new StCEquityBarterStrategyItem();
                commonStrategyItemInsertList.add(strategyItem);
                strategyItem.setId(ModelUtil.getSequence(StConstant.ST_C_EQUITY_BARTER_STRATEGY_ITEM));
                strategyItem.setStCEquityBarterStrategyId(oldCommonStrategy.getId());
                strategyItem.setPsCSkuId(strategyImpVo.getSkuId());
                strategyItem.setPsCSkuCode(strategyImpVo.getSkuCode());
                strategyItem.setPsCSkuName(strategyImpVo.getSkuName());
                strategyItem.setQty(strategyImpVo.getSkuQty());
                strategyItem.setEquitySkuId(strategyImpVo.getEquitySkuId());
                strategyItem.setEquitySkuCode(strategyImpVo.getEquitySkuCode());
                strategyItem.setEquitySkuName(strategyImpVo.getEquitySkuName());
                strategyItem.setEquityQty(strategyImpVo.getEquitySkuQty());
                strategyItem.setOutStockNoRestore(strategyImpVo.getOutStockNoRestore());
                if (strategyItem.getEquityQty().compareTo(strategyItem.getQty()) == 0) {
                    strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.EQUITY.getKey());
                } else {
                    strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.MORE_FOR_LESS.getKey());
                }
                strategyItem.setEffectiveStatus("1");
                strategyItem.setRemark("头明细追加");
                OmsModelUtil.setDefault4Add(strategyItem, user);

            }
        }
    }

    /**
     * 保存策略明细（追加）
     *
     * @param shopIds
     * @param commonStrategyItemInsertList
     * @param shopStrategyItemInsertList
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAddNew(List<Long> shopIds, List<StCEquityBarterStrategyItem> commonStrategyItemInsertList,
                           List<StCEquityBarterStrategyItem> shopStrategyItemInsertList) {
        if (CollectionUtils.isNotEmpty(commonStrategyItemInsertList)) {
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_COMMON);
            if (CollectionUtils.isNotEmpty(commonStrategyItemInsertList)) {
                stCEquityBarterStrategyItemMapper.batchInsert(commonStrategyItemInsertList);
            }
        }
        if (CollectionUtils.isNotEmpty(shopStrategyItemInsertList)) {
            for (Long shopId : shopIds) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + shopId);
            }
            if (CollectionUtils.isNotEmpty(shopStrategyItemInsertList)) {
                stCEquityBarterStrategyItemMapper.batchInsert(shopStrategyItemInsertList);
            }
        }
        if (CollectionUtils.isNotEmpty(commonStrategyItemInsertList)) {
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_COMMON);
        }
        if (CollectionUtils.isNotEmpty(shopStrategyItemInsertList)) {
            for (Long shopId : shopIds) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + shopId);
            }
        }
    }

    /**
     * 查询老策略（追加）
     *
     * @param shopIds
     * @param oldShopStrategyMap
     * @return
     */
    private List<StCEquityBarterStrategyItem> queryOldShopStrategy(List<Long> shopIds,
                                                                   Map<Long, StCEquityBarterStrategy> oldShopStrategyMap) {
        List<StCEquityBarterStrategyItem> oldShopStrategyItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(shopIds)) {
            List<StCEquityBarterStrategy> oldShopStrategyList =
                    stCEquityBarterStrategyMapper.selectList(new LambdaQueryWrapper<StCEquityBarterStrategy>()
                            .eq(StCEquityBarterStrategy::getType, EquityBarterTypeEnum.APPOINT_SHOP.getKey())
                            .in(StCEquityBarterStrategy::getCpCShopId, shopIds)
                            .eq(StCEquityBarterStrategy::getIsactive, YesNoEnum.Y.getKey()));
            List<Long> oldShopStrategyMainIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(oldShopStrategyList)) {
                for (StCEquityBarterStrategy strategy : oldShopStrategyList) {
                    oldShopStrategyMap.put(strategy.getCpCShopId(), strategy);
                    oldShopStrategyMainIds.add(strategy.getId());
                }
            }
            if (CollectionUtils.isNotEmpty(oldShopStrategyMainIds)) {
                oldShopStrategyItemList =
                        stCEquityBarterStrategyItemMapper.selectList(new LambdaQueryWrapper<StCEquityBarterStrategyItem>()
                                .in(StCEquityBarterStrategyItem::getStCEquityBarterStrategyId, oldShopStrategyMainIds)
                                .eq(StCEquityBarterStrategyItem::getIsactive, YesNoEnum.Y.getKey()));
            }
        }
        return oldShopStrategyItemList;
    }

    /**
     * 校验主表是否存在和明细是否已存在（追加）
     *
     * @param strategyImpVos
     * @param oldCommonStrategy
     * @param oldCommonStrategyItemList
     * @param oldShopStrategyMap
     * @param oldShopStrategyItemList
     */
    private void checkExistData(List<StCEquityBarterStrategyImpVo> strategyImpVos, StCEquityBarterStrategy oldCommonStrategy,
                                List<StCEquityBarterStrategyItem> oldCommonStrategyItemList,
                                Map<Long, StCEquityBarterStrategy> oldShopStrategyMap,
                                List<StCEquityBarterStrategyItem> oldShopStrategyItemList) {
        List<String> oldCommonExistSkuAndQtyList = new ArrayList<>();
        List<String> oldCommonExistSkuAndEquitySkuList = new ArrayList<>();
        List<String> oldShopExistSkuAndQtyList = new ArrayList<>();
        List<String> oldShopExistSkuAndEquitySkuList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(oldCommonStrategyItemList)) {
            for (StCEquityBarterStrategyItem strategyItem : oldCommonStrategyItemList) {
                oldCommonExistSkuAndQtyList.add(strategyItem.getPsCSkuId() + SEPARATOR +
                        strategyItem.getQty().stripTrailingZeros().toPlainString());
                oldCommonExistSkuAndEquitySkuList.add(strategyItem.getPsCSkuId() + SEPARATOR + strategyItem.getEquitySkuId());
            }
        }
        if (MapUtils.isNotEmpty(oldShopStrategyMap) && CollectionUtils.isNotEmpty(oldShopStrategyItemList)) {
            Map<Long, List<StCEquityBarterStrategyItem>> oldShopStrategyItemMap = oldShopStrategyItemList.stream().
                    collect(Collectors.groupingBy(StCEquityBarterStrategyItem::getStCEquityBarterStrategyId));
            for (Map.Entry<Long, StCEquityBarterStrategy> strategyEntry : oldShopStrategyMap.entrySet()) {
                Long shopId = strategyEntry.getKey();
                StCEquityBarterStrategy strategy = strategyEntry.getValue();
                List<StCEquityBarterStrategyItem> strategyItemList = oldShopStrategyItemMap.get(strategy.getId());
                if (CollectionUtils.isNotEmpty(strategyItemList)) {
                    for (StCEquityBarterStrategyItem strategyItem : strategyItemList) {
                        oldShopExistSkuAndQtyList.add(shopId + SEPARATOR + strategyItem.getPsCSkuId() +
                                SEPARATOR + strategyItem.getQty().stripTrailingZeros().toPlainString());
                        oldShopExistSkuAndEquitySkuList.add(shopId + SEPARATOR + strategyItem.getPsCSkuId() +
                                SEPARATOR + strategyItem.getEquitySkuId());
                    }
                }
            }
        }
        //校验主表是否存在和明细是否重复
        StringBuilder checkMessage = new StringBuilder();
        for (StCEquityBarterStrategyImpVo strategyImpVo : strategyImpVos) {
            if (EquityBarterTypeEnum.COMMUNAL.getKey().equals(strategyImpVo.getType())) {
                if (oldCommonStrategy == null) {
                    checkMessage.append("[当前策略主表信息不存在！]");
                } else {
                    String skuAndQtyKey = strategyImpVo.getSkuId() + SEPARATOR +
                            strategyImpVo.getSkuQty().stripTrailingZeros().toPlainString();
                    String skuAndEquitySkuKey = strategyImpVo.getSkuId() + SEPARATOR + strategyImpVo.getEquitySkuId();
                    if (oldCommonExistSkuAndQtyList.contains(skuAndQtyKey)) {
                        checkMessage.append("[已存在相同换货商品和数量！]");
                    }
                    if (oldCommonExistSkuAndEquitySkuList.contains(skuAndEquitySkuKey)) {
                        checkMessage.append("[已存在相同换货商品和对等商品！]");
                    }
                }
            } else {
                if (!oldShopStrategyMap.containsKey(strategyImpVo.getShopId())) {
                    checkMessage.append("[当前策略主表信息不存在！]");
                } else {
                    String skuAndQtyKey = strategyImpVo.getShopId() + SEPARATOR + strategyImpVo.getSkuId() +
                            SEPARATOR + strategyImpVo.getSkuQty().stripTrailingZeros().toPlainString();
                    String skuAndEquitySkuKey = strategyImpVo.getShopId() + SEPARATOR +
                            strategyImpVo.getSkuId() + SEPARATOR + strategyImpVo.getEquitySkuId();
                    if (oldShopExistSkuAndQtyList.contains(skuAndQtyKey)) {
                        checkMessage.append("[已存在相同换货商品和数量！]");
                    }
                    if (oldShopExistSkuAndEquitySkuList.contains(skuAndEquitySkuKey)) {
                        checkMessage.append("[已存在相同换货商品和对等商品！]");
                    }
                }
            }
            if (StringUtils.isNotEmpty(checkMessage)) {
                if (StringUtils.isNotBlank(strategyImpVo.getDesc())) {
                    strategyImpVo.setDesc(strategyImpVo.getDesc() + checkMessage);
                } else {
                    strategyImpVo.setDesc(checkMessage.toString());
                }
                checkMessage.setLength(0);
            }
        }
    }

    /**
     * 删除老策略，新增新策略（覆盖）
     *
     * @param shopIds
     * @param shopImpVoMap
     * @param commonStrategyInsertList
     * @param commonStrategyItemInsertList
     * @param shopStrategyInsertList
     * @param shopStrategyItemInsertList
     * @param deleteOldStrategyItemIds
     * @param operationLogList
     * @param strategyUpdateList
     */
    @Transactional(rollbackFor = Exception.class)
    public void delOldAndSaveNew(List<Long> shopIds, Map<Long, List<StCEquityBarterStrategyImpVo>> shopImpVoMap,
                                 List<StCEquityBarterStrategy> commonStrategyInsertList,
                                 List<StCEquityBarterStrategyItem> commonStrategyItemInsertList,
                                 List<StCEquityBarterStrategy> shopStrategyInsertList,
                                 List<StCEquityBarterStrategyItem> shopStrategyItemInsertList,
                                 List<Long> deleteOldStrategyItemIds, List<OcBOperationLog> operationLogList,
                                 List<StCEquityBarterStrategy> strategyUpdateList) {
        if (CollectionUtils.isNotEmpty(commonStrategyInsertList) || CollectionUtils.isNotEmpty(commonStrategyItemInsertList)) {
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_COMMON);
            if (CollectionUtils.isNotEmpty(commonStrategyInsertList)) {
                stCEquityBarterStrategyMapper.batchInsert(commonStrategyInsertList);
            }
            if (CollectionUtils.isNotEmpty(commonStrategyItemInsertList)) {
                stCEquityBarterStrategyItemMapper.batchInsert(commonStrategyItemInsertList);
            }
        }
        if (CollectionUtils.isNotEmpty(shopStrategyInsertList) || CollectionUtils.isNotEmpty(shopStrategyItemInsertList)) {
            for (Long shopId : shopIds) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + shopId);
            }
            if (CollectionUtils.isNotEmpty(shopStrategyInsertList)) {
                stCEquityBarterStrategyMapper.batchInsert(shopStrategyInsertList);
            }
            if (CollectionUtils.isNotEmpty(shopStrategyItemInsertList)) {
                stCEquityBarterStrategyItemMapper.batchInsert(shopStrategyItemInsertList);
            }
        }
        if (CollectionUtils.isNotEmpty(strategyUpdateList)) {
            for (StCEquityBarterStrategy strategy : strategyUpdateList) {
                stCEquityBarterStrategyMapper.updateById(strategy);
            }
        }
        if (CollectionUtils.isNotEmpty(deleteOldStrategyItemIds)) {
            stCEquityBarterStrategyItemMapper.deleteBatchIds(deleteOldStrategyItemIds);
        }
        if (CollectionUtils.isNotEmpty(operationLogList)) {
            operationLogMapper.batchInsert(operationLogList);
        }
        if (CollectionUtils.isNotEmpty(commonStrategyInsertList) || CollectionUtils.isNotEmpty(commonStrategyItemInsertList)) {
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_COMMON);
        }
        if (CollectionUtils.isNotEmpty(shopStrategyInsertList) || CollectionUtils.isNotEmpty(shopStrategyItemInsertList)) {
            for (Long shopId : shopImpVoMap.keySet()) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + shopId);
            }
        }
    }

    /**
     * 构建参数（覆盖）
     *
     * @param user
     * @param shopIds
     * @param commonImpVoList
     * @param shopImpVoMap
     * @param commonStrategyInsertList
     * @param commonStrategyItemInsertList
     * @param shopStrategyInsertList
     * @param shopStrategyItemInsertList
     * @param deleteOldStrategyItemIds
     * @param operationLogList
     * @param strategyUpdateList
     */
    private void buildCoverParam(User user, List<Long> shopIds, List<StCEquityBarterStrategyImpVo> commonImpVoList,
                                 Map<Long, List<StCEquityBarterStrategyImpVo>> shopImpVoMap,
                                 List<StCEquityBarterStrategy> commonStrategyInsertList,
                                 List<StCEquityBarterStrategyItem> commonStrategyItemInsertList,
                                 List<StCEquityBarterStrategy> shopStrategyInsertList,
                                 List<StCEquityBarterStrategyItem> shopStrategyItemInsertList,
                                 List<Long> deleteOldStrategyItemIds, List<OcBOperationLog> operationLogList,
                                 List<StCEquityBarterStrategy> strategyUpdateList) {
        buildCommonCoverParam(user, commonImpVoList, commonStrategyInsertList, commonStrategyItemInsertList, deleteOldStrategyItemIds, operationLogList, strategyUpdateList);
        buildShopCoverParam(user, shopIds, shopImpVoMap, shopStrategyInsertList, shopStrategyItemInsertList, deleteOldStrategyItemIds, operationLogList, strategyUpdateList);
    }

    /**
     * 构建店铺参数（覆盖）
     *
     * @param user
     * @param shopIds
     * @param shopImpVoMap
     * @param shopStrategyInsertList
     * @param shopStrategyItemInsertList
     * @param deleteOldStrategyItemIds
     * @param operationLogList
     * @param shopStrategyUpdateList
     */
    private void buildShopCoverParam(User user, List<Long> shopIds,
                                     Map<Long, List<StCEquityBarterStrategyImpVo>> shopImpVoMap,
                                     List<StCEquityBarterStrategy> shopStrategyInsertList,
                                     List<StCEquityBarterStrategyItem> shopStrategyItemInsertList,
                                     List<Long> deleteOldStrategyItemIds, List<OcBOperationLog> operationLogList,
                                     List<StCEquityBarterStrategy> shopStrategyUpdateList) {
        if (MapUtils.isNotEmpty(shopImpVoMap)) {
            //查询已经存在的策略
            Map<Long, StCEquityBarterStrategy> oldShopStrategyMap = new HashMap<>();
            List<Long> oldShopStrategyMainIds = new ArrayList<>();
            List<StCEquityBarterStrategyItem> oldShopStrategyItemList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(shopIds)) {
                List<StCEquityBarterStrategy> oldShopStrategyList =
                        stCEquityBarterStrategyMapper.selectList(new LambdaQueryWrapper<StCEquityBarterStrategy>()
                                .eq(StCEquityBarterStrategy::getType, EquityBarterTypeEnum.APPOINT_SHOP.getKey())
                                .in(StCEquityBarterStrategy::getCpCShopId, shopIds)
                                .eq(StCEquityBarterStrategy::getIsactive, YesNoEnum.Y.getKey()));
                if (CollectionUtils.isNotEmpty(oldShopStrategyList)) {
                    for (StCEquityBarterStrategy strategy : oldShopStrategyList) {
                        oldShopStrategyMap.put(strategy.getCpCShopId(), strategy);
                        oldShopStrategyMainIds.add(strategy.getId());
                    }
                }
                if (CollectionUtils.isNotEmpty(oldShopStrategyMainIds)) {
                    oldShopStrategyItemList =
                            stCEquityBarterStrategyItemMapper.selectList(new LambdaQueryWrapper<StCEquityBarterStrategyItem>()
                                    .in(StCEquityBarterStrategyItem::getStCEquityBarterStrategyId, oldShopStrategyMainIds)
                                    .eq(StCEquityBarterStrategyItem::getIsactive, YesNoEnum.Y.getKey()));
                }
            }
            for (Long shopId : shopImpVoMap.keySet()) {
                StCEquityBarterStrategy stCEquityBarterStrategy = oldShopStrategyMap.get(shopId);
                if (stCEquityBarterStrategy != null) {
                    StCEquityBarterStrategy oldShopStrategyUpdate = new StCEquityBarterStrategy();
                    oldShopStrategyUpdate.setId(stCEquityBarterStrategy.getId());
                    OmsModelUtil.setDefault4Upd(oldShopStrategyUpdate, user);
                    shopStrategyUpdateList.add(oldShopStrategyUpdate);
                    //覆盖指定店铺类型策略明细
                    for (StCEquityBarterStrategyImpVo strategyImpVo : shopImpVoMap.get(shopId)) {
                        StCEquityBarterStrategyItem strategyItem = new StCEquityBarterStrategyItem();
                        shopStrategyItemInsertList.add(strategyItem);
                        strategyItem.setId(ModelUtil.getSequence(ST_C_EQUITY_BARTER_STRATEGY_ITEM));
                        strategyItem.setStCEquityBarterStrategyId(oldShopStrategyMap.get(shopId).getId());
                        strategyItem.setPsCSkuId(strategyImpVo.getSkuId());
                        strategyItem.setPsCSkuCode(strategyImpVo.getSkuCode());
                        strategyItem.setPsCSkuName(strategyImpVo.getSkuName());
                        strategyItem.setQty(strategyImpVo.getSkuQty());
                        strategyItem.setEquitySkuId(strategyImpVo.getEquitySkuId());
                        strategyItem.setEquitySkuCode(strategyImpVo.getEquitySkuCode());
                        strategyItem.setEquitySkuName(strategyImpVo.getEquitySkuName());
                        strategyItem.setEquityQty(strategyImpVo.getEquitySkuQty());
                        strategyItem.setOutStockNoRestore(strategyImpVo.getOutStockNoRestore());
                        if (strategyItem.getEquityQty().compareTo(strategyItem.getQty()) == 0) {
                            strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.EQUITY.getKey());
                        } else {
                            strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.MORE_FOR_LESS.getKey());
                        }
                        strategyItem.setEffectiveStatus("1");
                        strategyItem.setRemark("头明细覆盖");
                        OmsModelUtil.setDefault4Add(strategyItem, user);
                    }
                } else {
                    //新增指定店铺类型策略主表后新增明细
                    StCEquityBarterStrategy strategy = new StCEquityBarterStrategy();
                    shopStrategyInsertList.add(strategy);
                    Long mainId = ModelUtil.getSequence(StConstant.ST_C_EQUITY_BARTER_STRATEGY);
                    strategy.setId(mainId);
                    strategy.setType(EquityBarterTypeEnum.APPOINT_SHOP.getKey());
                    strategy.setCpCShopId(shopId);
                    OmsModelUtil.setDefault4Add(strategy, user);
                    for (StCEquityBarterStrategyImpVo strategyImpVo : shopImpVoMap.get(shopId)) {
                        StCEquityBarterStrategyItem strategyItem = new StCEquityBarterStrategyItem();
                        shopStrategyItemInsertList.add(strategyItem);
                        strategyItem.setId(ModelUtil.getSequence(StConstant.ST_C_EQUITY_BARTER_STRATEGY_ITEM));
                        strategyItem.setStCEquityBarterStrategyId(mainId);
                        strategyItem.setPsCSkuId(strategyImpVo.getSkuId());
                        strategyItem.setPsCSkuCode(strategyImpVo.getSkuCode());
                        strategyItem.setPsCSkuName(strategyImpVo.getSkuName());
                        strategyItem.setQty(strategyImpVo.getSkuQty());
                        strategyItem.setEquitySkuId(strategyImpVo.getEquitySkuId());
                        strategyItem.setEquitySkuCode(strategyImpVo.getEquitySkuCode());
                        strategyItem.setEquitySkuName(strategyImpVo.getEquitySkuName());
                        strategyItem.setEquityQty(strategyImpVo.getEquitySkuQty());
                        strategyItem.setOutStockNoRestore(strategyImpVo.getOutStockNoRestore());
                        if (strategyItem.getEquityQty().compareTo(strategyItem.getQty()) == 0) {
                            strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.EQUITY.getKey());
                        } else {
                            strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.MORE_FOR_LESS.getKey());
                        }
                        strategyItem.setEffectiveStatus("1");
                        strategyItem.setRemark("头明细覆盖");
                        OmsModelUtil.setDefault4Add(strategyItem, user);
                    }
                }
            }
            //店铺的可以直接删除，因为存在就说明这些店铺有新导入的
            collectDelIdsAndBuildLogParam(user, deleteOldStrategyItemIds, operationLogList, oldShopStrategyItemList);
        }
    }

    /**
     * 收集删除明细的id和删除日志参数构建（覆盖）
     *
     * @param user
     * @param deleteOldStrategyItemIds
     * @param operationLogList
     * @param oldStrategyItemList
     */
    private void collectDelIdsAndBuildLogParam(User user, List<Long> deleteOldStrategyItemIds,
                                               List<OcBOperationLog> operationLogList,
                                               List<StCEquityBarterStrategyItem> oldStrategyItemList) {
        if (CollectionUtils.isNotEmpty(oldStrategyItemList)) {
            StringBuilder beforeValue = new StringBuilder();
            for (StCEquityBarterStrategyItem strategyItem : oldStrategyItemList) {
                deleteOldStrategyItemIds.add(strategyItem.getId());
                beforeValue.setLength(0);
                beforeValue.append("[").append(strategyItem.getPsCSkuCode()).append("],[").append(strategyItem.getPsCSkuName())
                        .append("],[").append(strategyItem.getQty()).append("],[").append(strategyItem.getEquitySkuCode())
                        .append("],[").append(strategyItem.getEquitySkuName()).append("],[").append(strategyItem.getEquityQty())
                        .append("],[").append(EquityBarterExchangeTypeEnum.getByKey(strategyItem.getExchangeType()).getDesc()).append("]");
                operationLogList.add(getOperationLog("ST_C_EQUITY_BARTER_STRATEGY_ITEM", "DEL",
                        strategyItem.getStCEquityBarterStrategyId(), "对等换货策略明细",
                        "删除对等换货策略明细", beforeValue.toString(), "头明细覆盖删除", user));
            }
        }
    }

    /**
     * 构建公用参数（覆盖）
     *
     * @param user
     * @param commonImpVoList
     * @param commonStrategyInsertList
     * @param commonStrategyItemInsertList
     * @param deleteOldStrategyItemIds
     * @param operationLogList
     * @param commonStrategyUpdateList
     */
    private void buildCommonCoverParam(User user, List<StCEquityBarterStrategyImpVo> commonImpVoList,
                                       List<StCEquityBarterStrategy> commonStrategyInsertList,
                                       List<StCEquityBarterStrategyItem> commonStrategyItemInsertList,
                                       List<Long> deleteOldStrategyItemIds, List<OcBOperationLog> operationLogList,
                                       List<StCEquityBarterStrategy> commonStrategyUpdateList) {
        if (CollectionUtils.isNotEmpty(commonImpVoList)) {
            StCEquityBarterStrategy oldCommonStrategy =
                    stCEquityBarterStrategyMapper.selectOne(new LambdaQueryWrapper<StCEquityBarterStrategy>()
                            .eq(StCEquityBarterStrategy::getType, EquityBarterTypeEnum.COMMUNAL.getKey())
                            .eq(StCEquityBarterStrategy::getIsactive, YesNoEnum.Y.getKey())
                            .last("limit 1"));
            List<StCEquityBarterStrategyItem> oldCommonStrategyItemList = new ArrayList<>();
            if (oldCommonStrategy != null) {
                oldCommonStrategyItemList =
                        stCEquityBarterStrategyItemMapper.selectList(new LambdaQueryWrapper<StCEquityBarterStrategyItem>()
                                .eq(StCEquityBarterStrategyItem::getStCEquityBarterStrategyId, oldCommonStrategy.getId())
                                .eq(StCEquityBarterStrategyItem::getIsactive, YesNoEnum.Y.getKey()));
            }
            if (oldCommonStrategy != null) {
                StCEquityBarterStrategy oldCommonStrategyUpdate = new StCEquityBarterStrategy();
                oldCommonStrategyUpdate.setId(oldCommonStrategy.getId());
                OmsModelUtil.setDefault4Upd(oldCommonStrategyUpdate, user);
                commonStrategyUpdateList.add(oldCommonStrategyUpdate);
                //覆盖公用类型策略明细
                for (StCEquityBarterStrategyImpVo strategyImpVo : commonImpVoList) {
                    StCEquityBarterStrategyItem strategyItem = new StCEquityBarterStrategyItem();
                    commonStrategyItemInsertList.add(strategyItem);
                    strategyItem.setId(ModelUtil.getSequence(StConstant.ST_C_EQUITY_BARTER_STRATEGY_ITEM));
                    strategyItem.setStCEquityBarterStrategyId(oldCommonStrategy.getId());
                    strategyItem.setPsCSkuId(strategyImpVo.getSkuId());
                    strategyItem.setPsCSkuCode(strategyImpVo.getSkuCode());
                    strategyItem.setPsCSkuName(strategyImpVo.getSkuName());
                    strategyItem.setQty(strategyImpVo.getSkuQty());
                    strategyItem.setEquitySkuId(strategyImpVo.getEquitySkuId());
                    strategyItem.setEquitySkuCode(strategyImpVo.getEquitySkuCode());
                    strategyItem.setEquitySkuName(strategyImpVo.getEquitySkuName());
                    strategyItem.setEquityQty(strategyImpVo.getEquitySkuQty());
                    strategyItem.setOutStockNoRestore(strategyImpVo.getOutStockNoRestore());
                    if (strategyItem.getEquityQty().compareTo(strategyItem.getQty()) == 0) {
                        strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.EQUITY.getKey());
                    } else {
                        strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.MORE_FOR_LESS.getKey());
                    }
                    strategyItem.setEffectiveStatus("1");
                    strategyItem.setRemark("头明细覆盖");
                    OmsModelUtil.setDefault4Add(strategyItem, user);
                }
            } else {
                //新增公用类型策略主表后新增明细
                StCEquityBarterStrategy strategy = new StCEquityBarterStrategy();
                commonStrategyInsertList.add(strategy);
                Long mainId = ModelUtil.getSequence(StConstant.ST_C_EQUITY_BARTER_STRATEGY);
                strategy.setId(mainId);
                strategy.setType(EquityBarterTypeEnum.COMMUNAL.getKey());
                OmsModelUtil.setDefault4Add(strategy, user);
                for (StCEquityBarterStrategyImpVo strategyImpVo : commonImpVoList) {
                    StCEquityBarterStrategyItem strategyItem = new StCEquityBarterStrategyItem();
                    commonStrategyItemInsertList.add(strategyItem);
                    strategyItem.setId(ModelUtil.getSequence(ST_C_EQUITY_BARTER_STRATEGY_ITEM));
                    strategyItem.setStCEquityBarterStrategyId(mainId);
                    strategyItem.setPsCSkuId(strategyImpVo.getSkuId());
                    strategyItem.setPsCSkuCode(strategyImpVo.getSkuCode());
                    strategyItem.setPsCSkuName(strategyImpVo.getSkuName());
                    strategyItem.setQty(strategyImpVo.getSkuQty());
                    strategyItem.setEquitySkuId(strategyImpVo.getEquitySkuId());
                    strategyItem.setEquitySkuCode(strategyImpVo.getEquitySkuCode());
                    strategyItem.setEquitySkuName(strategyImpVo.getEquitySkuName());
                    strategyItem.setEquityQty(strategyImpVo.getEquitySkuQty());
                    strategyItem.setOutStockNoRestore(strategyImpVo.getOutStockNoRestore());
                    if (strategyItem.getEquityQty().compareTo(strategyItem.getQty()) == 0) {
                        strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.EQUITY.getKey());
                    } else {
                        strategyItem.setExchangeType(EquityBarterExchangeTypeEnum.MORE_FOR_LESS.getKey());
                    }
                    strategyItem.setEffectiveStatus("1");
                    strategyItem.setRemark("头明细覆盖");
                    OmsModelUtil.setDefault4Add(strategyItem, user);
                }
            }
            //导入了公共的就可以删除公共之前的明细
            collectDelIdsAndBuildLogParam(user, deleteOldStrategyItemIds, operationLogList, oldCommonStrategyItemList);
        }
    }

    /**
     * 按照策略类型分组（覆盖）
     *
     * @param strategyImpVos
     * @param commonImpVoList
     * @param shopImpVoMap
     */
    private void groupData(List<StCEquityBarterStrategyImpVo> strategyImpVos,
                           List<StCEquityBarterStrategyImpVo> commonImpVoList,
                           Map<Long, List<StCEquityBarterStrategyImpVo>> shopImpVoMap) {
        for (StCEquityBarterStrategyImpVo strategyImpVo : strategyImpVos) {
            if (EquityBarterTypeEnum.COMMUNAL.getKey().equals(strategyImpVo.getType())) {
                commonImpVoList.add(strategyImpVo);
            } else {
                if (shopImpVoMap.containsKey(strategyImpVo.getShopId())) {
                    List<StCEquityBarterStrategyImpVo> impVoList = shopImpVoMap.get(strategyImpVo.getShopId());
                    impVoList.add(strategyImpVo);
                } else {
                    List<StCEquityBarterStrategyImpVo> impVoList = new ArrayList<>();
                    impVoList.add(strategyImpVo);
                    shopImpVoMap.put(strategyImpVo.getShopId(), impVoList);
                }
            }
        }
    }

    /**
     * 导出校验失败的数据
     *
     * @param strategyImpVos
     * @param user
     * @return
     */
    private ValueHolderV14<String> exportErrorData(List<StCEquityBarterStrategyImpVo> strategyImpVos, User user) {
        ValueHolderV14<String> holderV14 = new ValueHolderV14<>();
        List<StCEquityBarterStrategyImpVo> errorImpVoList = strategyImpVos.stream().
                filter(s -> StringUtils.isNotEmpty(s.getDesc())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(errorImpVoList)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setData(exportResult(errorImpVoList, user));
            holderV14.setMessage("对等换货策略头明细导入失败，详情见文件内容");
            return holderV14;
        }
        return holderV14;
    }


    /**
     * 校验逻辑
     *
     * @param strategyImpVos
     * @param shopMap
     * @param skuMap
     */
    private void checkExcelData(List<StCEquityBarterStrategyImpVo> strategyImpVos,
                                Map<String, CpShop> shopMap, Map<String, PsCSku> skuMap) {
        Map<String, Integer> commonUniqueSkuQtyCheck = new HashMap<>();
        Map<String, Integer> commonUniqueSkuEquityCheck = new HashMap<>();
        Map<String, Integer> shopUniqueSkuQtyCheck = new HashMap<>();
        Map<String, Integer> shopUniqueSkuEquityCheck = new HashMap<>();
        StringBuilder checkMessage = new StringBuilder();
        for (StCEquityBarterStrategyImpVo strategyImpVo : strategyImpVos) {
            if (EquityBarterTypeEnum.COMMUNAL.getKey().equals(strategyImpVo.getType())) {
                String uniqueSkuKey = strategyImpVo.getSkuCode() + SEPARATOR + strategyImpVo.getSkuQty().stripTrailingZeros().toPlainString();
                if (commonUniqueSkuQtyCheck.containsKey(uniqueSkuKey)) {
                    checkMessage.append("[已存在相同换货商品和数量,与").append(commonUniqueSkuQtyCheck.get(uniqueSkuKey)).append("行相同！]");
                } else {
                    commonUniqueSkuQtyCheck.put(uniqueSkuKey, strategyImpVo.getRowNum());
                }
                String uniqueEquitySkuKey = strategyImpVo.getSkuCode() + SEPARATOR + strategyImpVo.getEquitySkuCode();
                if (commonUniqueSkuEquityCheck.containsKey(uniqueEquitySkuKey)) {
                    checkMessage.append("[已存在相同换货商品和对等商品,与").append(commonUniqueSkuEquityCheck.get(uniqueEquitySkuKey)).append("行相同！]");
                } else {
                    commonUniqueSkuEquityCheck.put(uniqueSkuKey, strategyImpVo.getRowNum());
                }
            } else {
                CpShop cpShop = shopMap.get(strategyImpVo.getPlatformShopName());
                if (cpShop != null) {
                    strategyImpVo.setShopId(cpShop.getId());
                    String uniqueSkuKey = strategyImpVo.getShopId() + SEPARATOR + strategyImpVo.getSkuCode() +
                            SEPARATOR + strategyImpVo.getSkuQty().stripTrailingZeros().toPlainString();
                    if (shopUniqueSkuQtyCheck.containsKey(uniqueSkuKey)) {
                        checkMessage.append("[已存在相同换货商品和数量,与").append(shopUniqueSkuQtyCheck.get(uniqueSkuKey)).append("行相同！]");
                    } else {
                        shopUniqueSkuQtyCheck.put(uniqueSkuKey, strategyImpVo.getRowNum());
                    }
                    String uniqueEquitySkuKey = strategyImpVo.getShopId() + SEPARATOR + strategyImpVo.getSkuCode() + SEPARATOR + strategyImpVo.getEquitySkuCode();
                    if (shopUniqueSkuEquityCheck.containsKey(uniqueEquitySkuKey)) {
                        checkMessage.append("[已存在相同换货商品和对等商品,与").append(shopUniqueSkuEquityCheck.get(uniqueEquitySkuKey)).append("行相同！]");
                    } else {
                        shopUniqueSkuEquityCheck.put(uniqueSkuKey, strategyImpVo.getRowNum());
                    }
                } else {
                    checkMessage.append("[平台店铺不存在]");
                }
            }
            if (skuMap.containsKey(strategyImpVo.getSkuCode())) {
                strategyImpVo.setSkuId(skuMap.get(strategyImpVo.getSkuCode()).getId());
                strategyImpVo.setSkuName(skuMap.get(strategyImpVo.getSkuCode()).getPsCProEname());
            } else {
                checkMessage.append("[换货商品不存在]");
            }
            if (skuMap.containsKey(strategyImpVo.getEquitySkuCode())) {
                strategyImpVo.setEquitySkuId(skuMap.get(strategyImpVo.getEquitySkuCode()).getId());
                strategyImpVo.setEquitySkuName(skuMap.get(strategyImpVo.getEquitySkuCode()).getPsCProEname());
            } else {
                checkMessage.append("[对等商品不存在]");
            }
            BigDecimal equityQty = strategyImpVo.getEquitySkuQty();
            BigDecimal qty = strategyImpVo.getSkuQty();
            if (equityQty == null || BigDecimal.ONE.compareTo(equityQty) != 0) {
                checkMessage.append("[对等数量不可为空且必须为1]");
            }
            if (qty == null || BigDecimal.ZERO.compareTo(qty) > 0) {
                checkMessage.append("[换货数量不可为空且必须大于0]");
            }
            if (qty.compareTo(equityQty) < 0) {
                checkMessage.append("[换货数量】小于【对等数量】，不允许少换多]");
            }
            if (StringUtils.isNotEmpty(checkMessage)) {
                if (StringUtils.isNotBlank(strategyImpVo.getDesc())) {
                    strategyImpVo.setDesc(strategyImpVo.getDesc() + checkMessage);
                } else {
                    strategyImpVo.setDesc(checkMessage.toString());
                }
                checkMessage.setLength(0);
            }
        }
    }

    /**
     * 查询sku信息
     *
     * @param skuCodeList
     * @return
     */
    private Map<String, PsCSku> querySkuInfo(List<String> skuCodeList) {
        Map<String, PsCSku> skuMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuCodeList)) {
            ValueHolderV14<List<PsCSku>> valueHolderV14 = psCSkuQueryCmd.querySKUByEcodeList(skuCodeList);
            if (valueHolderV14.isOK() && CollectionUtils.isNotEmpty(valueHolderV14.getData())) {
                skuMap = valueHolderV14.getData().stream().collect(Collectors.toMap(PsCSku::getEcode, Function.identity()));
            }
        }
        return skuMap;
    }

    /**
     * 查询店铺信息
     *
     * @param shopNameList
     * @param shopMap
     * @return
     */
    private List<Long> queryShopInfo(List<String> shopNameList, Map<String, CpShop> shopMap) {
        List<Long> shopIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(shopNameList)) {
            List<CpShop> cpShops = cpShopQueryCmd.queryShopByNames(shopNameList);
            if (CollectionUtils.isNotEmpty(cpShops)) {
                for (CpShop cpShop : cpShops) {
                    if (!shopMap.containsKey(cpShop.getCpCShopTitle())) {
                        shopMap.put(cpShop.getCpCShopTitle(), cpShop);
                    }
                    if (!shopIds.contains(cpShop.getId())) {
                        shopIds.add(cpShop.getId());
                    }
                }
            }
        }
        return shopIds;
    }

    /**
     * 收集店铺名称和SKU编码
     *
     * @param strategyImpVos
     * @param shopNameList
     * @param skuCodeList
     */
    private void collectShopNameAndSkuCode(List<StCEquityBarterStrategyImpVo> strategyImpVos,
                                           List<String> shopNameList, List<String> skuCodeList) {
        for (StCEquityBarterStrategyImpVo dataImpVo : strategyImpVos) {
            if (StringUtils.isNotEmpty(dataImpVo.getPlatformShopName()) &&
                    !shopNameList.contains(dataImpVo.getPlatformShopName())) {
                shopNameList.add(dataImpVo.getPlatformShopName());
            }
            if (!skuCodeList.contains(dataImpVo.getSkuCode())) {
                skuCodeList.add(dataImpVo.getSkuCode());
            }
            if (!skuCodeList.contains(dataImpVo.getEquitySkuCode())) {
                skuCodeList.add(dataImpVo.getEquitySkuCode());
            }
        }
    }

    /**
     * @param dataImpVos
     * @param user
     * @return
     */
    private String exportResult(List<StCEquityBarterStrategyImpVo> dataImpVos, User user) {
        List<StCEquityBarterStrategyImpVo> errorList = dataImpVos.parallelStream().filter(x -> x.getDesc() != null).collect(Collectors.toList());
        // 列名
        String[] columnNames = {"主表行号", "错误原因"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"rowNum", "desc"};
        List<String> k = Lists.newArrayList(keys);
        Workbook hssfWorkbook = exportUtil.execute("对等换货策略头明细导入", "对等换货策略头明细导入", c, k, errorList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "对等换货策略头明细导入错误信息", user, "OSS-Bucket/IMPORT/ST_C_EQUITY_BARTER_STRATEGY/");
    }

    /**
     * 获取操作日志对象
     *
     * @param tableName
     * @param operationType
     * @param updateId
     * @param tableDescription
     * @param columnName
     * @param columnBeforeValue
     * @param columnAfterValue
     * @param user
     * @return
     */
    private OcBOperationLog getOperationLog(String tableName, String operationType, Long updateId,
                                            String tableDescription, String columnName, String columnBeforeValue,
                                            String columnAfterValue, User user) {
        OcBOperationLog operationLog = new OcBOperationLog();
        operationLog.setId(ModelUtil.getSequence(StConstant.TAB_OC_B_OPERATION_LOG));
        operationLog.setTableName(tableName);
        operationLog.setOperationType(OperationTypeEnum.getNameByValue(operationType));
        operationLog.setUpdateId(updateId);
        operationLog.setUpdateModelName(tableDescription);
        operationLog.setModContent(columnName);
        operationLog.setBeforeData(columnBeforeValue);
        operationLog.setAfterData(columnAfterValue);
        StBeanUtils.makeCreateField(operationLog, user);
        return operationLog;
    }
}
