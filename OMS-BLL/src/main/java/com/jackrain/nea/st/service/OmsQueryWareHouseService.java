package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.util.PrintLogUtils;
import com.jackrain.nea.psext.api.utils.JsonUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.RedisKeyConst;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.WarehouseRankResult;
import com.jackrain.nea.st.model.table.StCSendPlanDO;
import com.jackrain.nea.st.model.table.StCSendPlanItemDO;
import com.jackrain.nea.st.model.table.StCSendRuleAddressRankDO;
import com.jackrain.nea.st.model.table.StCSendRuleDO;
import com.jackrain.nea.st.model.table.StCSendRuleWarehouseRateDO;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @author: heliu
 * @since: 2019/4/2
 * create at : 2019/4/2 20:14
 */
@Component
@Slf4j
public class OmsQueryWareHouseService {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private StRpcService stRpcService;


    /**
     * 根据实体仓查询逻辑仓
     *
     * @param cphyWarehouseId 逻辑仓集合
     * @return List<Long>
     */
    public List<Long> queryStoreList(Long cphyWarehouseId) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryStoreList参", "queryStoreList入参"), "发货仓库Id" + cphyWarehouseId);
        }
        List<Long> storeList = cpRpcService.queryStoreList(cphyWarehouseId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryStoreList出参", "queryStoreList出参"), storeList);
        }
        return storeList;
    }

    /**
     * 查找派单方案
     *
     * @param orderId     订单ID
     * @param shopId      店铺Id
     * @param currentDate 当前日期
     * @return List<Long>
     */
    @SuppressWarnings("unchecked")
    public List<Long> selectSendPlanList(Long orderId, Long shopId, Date currentDate) {

        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询有效方案selectSendPlanList入参", "selectSendPlanList入参", orderId), "店铺Id" + shopId);
            }
            List<StCSendPlanDO> sendPlanTempList = Lists.newArrayList();
            String redisKey = OmsRedisKeyResources.buildLockShopSendPlanRedisKey(shopId);
            CusRedisTemplate<Object, List<StCSendPlanDO>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            if (objRedisTemplate.hasKey(redisKey)) {
                sendPlanTempList = objRedisTemplate.opsForValue().get(redisKey);
            } else {
                sendPlanTempList = stRpcService.selectSendPlanListByShopId(shopId);
                //存放在redis中
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, sendPlanTempList);
            }
            /*List<StCSendPlanDO> sendPlanList = objRedisTemplate.opsForValue().get(redisKey);
            log.debug("订单OrderId" + orderId + "取redis派单方案里面数据" + sendPlanList);
            if (CollectionUtils.isNotEmpty(sendPlanList)) {
                PrintLogUtils.printDeBugLog("订单OrderId:{},直接从redis获取方案数据", orderId);
                sendPlanTempList = sendPlanList;
            } else {
                PrintLogUtils.printDeBugLog("订单OrderId:{},没有走redis获取方案数据,然后走rpc调用接口获取数据", orderId);
                sendPlanTempList = stRpcService.selectSendPlanListByShopId(shopId);
                //存放在redis中
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, sendPlanTempList);
            }*/
            //过滤符合在日期范围内的方案集合
            List<Long> planList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(sendPlanTempList)) {
                for (StCSendPlanDO sendPlanDO : sendPlanTempList) {
                    //当方案有效开始时间和方案有效结束有效时间都不为空的时候再去判断是否符合时间范围
                    if (sendPlanDO.getBeginTime() != null && sendPlanDO.getEndTime() != null) {
                        if ((sendPlanDO.getBeginTime().before(currentDate) && sendPlanDO.getEndTime().after(currentDate))) {
                            planList.add(sendPlanDO.getId());
                        }
                    }
                }
            }
            log.debug(LogUtil.format("查询有效派单方案id出参", "有效派单方案", orderId), planList);
            return planList;
        } catch (Exception ex) {
            log.error(LogUtil.format("selectSendPlanList查找派单方案异常", "查找派单方案异常", orderId), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 根据方案查找派单规则明细
     *
     * @param orderId 订单id
     * @param planId  派单方案 ID
     * @return 派单规则ID列表
     */
    @SuppressWarnings("unchecked")
    public List<Long> selectSendPlanItemList(Long orderId, Long planId) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectSendPlanItemList入参:派单方案Id", "派单方案Id", orderId), "派单方案Id" + planId);
            }
            String redisKey = OmsRedisKeyResources.buildLockSendPlanRuleRedisKey(planId);
            CusRedisTemplate<String, List<StCSendPlanItemDO>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            List<StCSendPlanItemDO> sendPlanItemTempList = Lists.newArrayList();
            PrintLogUtils.printDeBugLog("订单OrderId:{},判断查询有效派单规则明细表key是否存在:{}", orderId, objRedisTemplate.hasKey(redisKey));
            List<StCSendPlanItemDO> sendPlanItemList = objRedisTemplate.opsForValue().get(redisKey);
            if (CollectionUtils.isNotEmpty(sendPlanItemList)) {
                sendPlanItemTempList = sendPlanItemList;
            } else {
                sendPlanItemTempList = stRpcService.selectSendPlanItemListByPlanId(planId);
                CusRedisTemplate<String, List<StCSendPlanItemDO>> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
                //存放在redis中
                redisTemplate.opsForValue().set(redisKey, sendPlanItemTempList);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询有效派单规则明细表endselectSendPlanItemList出参", "查询有效派单规则明细表", orderId), JSONObject.toJSONString(sendPlanItemTempList));
            }
            //符合规则的id
            List<Long> ruleIdsList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(sendPlanItemTempList)) {
                ruleIdsList = sendPlanItemTempList.stream().map(StCSendPlanItemDO::getStCSendRuleId).collect(Collectors.toList());
            }
            log.debug(LogUtil.format("查询有效派单规则明细表id出参", "查询有效派单规则明细表id出参", orderId), ruleIdsList);
            return ruleIdsList;
        } catch (Exception ex) {
            log.error(LogUtil.format("查找派单规则明细表异常", "查找派单规则明细表异常", orderId), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 根据实体仓库去匹配地址就近的仓库
     *
     * @param orderId          订单ID
     * @param warehouseList    实体发货仓库
     * @param regionProvinceId 省份Id
     * @param sendRuleId       派单规则
     * @return List<OcStCSendRuleAddressRent>
     */
    @SuppressWarnings("unchecked")
    public List<WarehouseRankResult> selectAddressWarehouseList(Long orderId, List<Long> warehouseList,
                                                                Long regionProvinceId, Long sendRuleId) {

        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectAddressWarehouseList入参:实体仓库Id集合{},省份id:{},规则Id:{}", "实体仓库去匹配地址就近的仓库", orderId), warehouseList, regionProvinceId, sendRuleId);
            }

            CusRedisTemplate<String, List<StCSendRuleAddressRankDO>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            String redisKey = OmsRedisKeyResources.buildLockStCSendRuleAddressRankKey(sendRuleId, regionProvinceId);
            List<StCSendRuleAddressRankDO> sendRuleAddressRankTempList = Lists.newArrayList();
           /* List<StCSendRuleAddressRankDO> sendRuleAddressRankList = objRedisTemplate.opsForValue().get(redisKey);
            log.debug("订单OrderId" + orderId + "取redis地址就近规则里面数据" + sendRuleAddressRankList);
            if (CollectionUtils.isNotEmpty(sendRuleAddressRankList)) {
                sendRuleAddressRankTempList = sendRuleAddressRankList;
            } else {
                sendRuleAddressRankTempList = stRpcService.selectSendRuleAddressRankList(regionProvinceId, sendRuleId);
                CusRedisTemplate<String, List<StCSendRuleAddressRankDO>> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
                //存放在redis中
                redisTemplate.opsForValue().set(redisKey, sendRuleAddressRankTempList);
            }*/
            if (objRedisTemplate.hasKey(redisKey)) {
                List<StCSendRuleAddressRankDO> stCSendRuleAddressRankDOS = objRedisTemplate.opsForValue().get(redisKey);
                if (stCSendRuleAddressRankDOS != null) {
                    sendRuleAddressRankTempList = stCSendRuleAddressRankDOS;
                }
            } else {
                sendRuleAddressRankTempList = stRpcService.selectSendRuleAddressRankList(regionProvinceId, sendRuleId);
                objRedisTemplate.opsForValue().set(redisKey, sendRuleAddressRankTempList);
            }

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询地址就近规则sendRuleAddressRankList出参", "查询地址就近规则", orderId), JSONObject.toJSONString(sendRuleAddressRankTempList));
            }
            List<WarehouseRankResult> wareIdsList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(sendRuleAddressRankTempList)) {
                for (StCSendRuleAddressRankDO rank : sendRuleAddressRankTempList) {
                    if (StringUtils.isNotBlank(rank.getWarehouseRank())) {
                        List<WarehouseRankResult> warehouseRankList = JsonUtils.jsonToList(WarehouseRankResult.class, rank.getWarehouseRank());
                        warehouseRankList = warehouseRankList.stream().sorted(Comparator.comparing(WarehouseRankResult::getRank))
                                .collect(Collectors.toList());
                        for (WarehouseRankResult result : warehouseRankList) {
                            if (warehouseList.contains(result.getWarehouseId())) {
                                wareIdsList.add(result);
                            }
                        }
                    }
                }
            }
            log.debug(LogUtil.format("查询有效查询地址就近规则实体仓id出参", "查询有效查询地址就近规则实体仓", orderId), wareIdsList);
            return wareIdsList;
        } catch (Exception ex) {
            log.error(LogUtil.format("查询有效查询地址就近规则实体仓id异常", orderId), Throwables.getStackTraceAsString(ex));
            return Lists.newArrayList();
        }
    }

    /**
     * 根据派单方案ID查找对应的派单规则内容
     * 返回值为地址、比例类型的规则ID
     *
     * @param orderId 订单ID
     * @param planId  派单方案ID
     * @return Map<String, Long> address-地址就近原则；rate-分仓比例原则
     */
    public Map<String, Long> querySendRuleIdByType(Long orderId, Long planId) {
        Map<String, Long> ruleTypeMap = new HashMap<>();
        List<Long> sendRuleIdList = selectSendPlanItemList(orderId, planId);
        log.debug(LogUtil.format("查询查询方案下派单规则querySendRuleIdByType入参", "查询查询方案下派单规则", orderId), sendRuleIdList);

        if (CollectionUtils.isNotEmpty(sendRuleIdList)) {
            //    List<StCSendRuleDO> selectSendRuleByIdList = stRpcService.selectSendRuleByIdList(sendRuleIdList);
            List<StCSendRuleDO> selectSendRuleByIdList = queryStCSendRuleDos(sendRuleIdList);
            PrintLogUtils.printDeBugLog("订单OrderId:{},查询查询方案下派单规则集合数据selectSendRuleByIdList:{};", orderId, selectSendRuleByIdList);
            for (StCSendRuleDO sendRuleDo : selectSendRuleByIdList) {
                if (StringUtils.isNotEmpty(sendRuleDo.getEtype())) {
                    if ("1".equalsIgnoreCase(sendRuleDo.getEtype())) {
                        ruleTypeMap.put("address", sendRuleDo.getId());
                    } else if ("2".equalsIgnoreCase(sendRuleDo.getEtype())) {
                        ruleTypeMap.put("rate", sendRuleDo.getId());
                    }
                }
            }
        }
        log.debug(LogUtil.format("查询查询方案下派单规则querySendRuleIdByType出参", "查询查询方案下派单规则", orderId), sendRuleIdList);
        return ruleTypeMap;
    }

    /**
     * 查找分仓比例的规则
     */
    public List<StCSendRuleDO> queryStCSendRuleDos(List<Long> ids) {

        CusRedisTemplate<String, Object> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
        BoundHashOperations<String, String, StCSendRuleDO> hashOperations = redisTemplate.boundHashOps(RedisKeyConst.ST_SEND_RULE_KEY);

        List<StCSendRuleDO> stCSendRuleDOS = new ArrayList<>(ids.size());
        Map<String, StCSendRuleDO> entries = hashOperations.entries();
        if (entries != null && entries.size() > 0) {
            for (Map.Entry<String, StCSendRuleDO> entry : entries.entrySet()) {
                stCSendRuleDOS.add(entry.getValue());
                ids.remove(Long.valueOf(entry.getKey()));
            }
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            List<StCSendRuleDO> StCSendRuleDos = stRpcService.selectSendRuleByIdList(ids);
            if (CollectionUtils.isNotEmpty(StCSendRuleDos)) {
                for (StCSendRuleDO stCSendRuleDo : StCSendRuleDos) {
                    stCSendRuleDOS.add(stCSendRuleDo);
                    hashOperations.put(String.valueOf(stCSendRuleDo.getId()), stCSendRuleDo);
                    ids.remove(stCSendRuleDo.getId());
                }
            }
            for (Long id : ids) {
                hashOperations.put(String.valueOf(id), null);
            }
        }
        return stCSendRuleDOS;
    }

    /**
     * 根据逻辑仓聚合实体仓
     *
     * @param storeList 逻辑仓集合
     * @return List<Long>
     */
    public List<Long> queryWareHouseIds(List<Long> storeList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryWareHouseIds入参", "逻辑仓Id集合"), storeList);
        }
        List<Long> wareHouseIds = cpRpcService.queryWareHouseIds(storeList);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryWareHouseIds出参", "queryWareHouseIds出参"), wareHouseIds);
        }
        return wareHouseIds;
    }

    /**
     * 根据实体仓库去匹配仓库发货比例
     *
     * @param warehouseList 实体发货仓库
     * @param sendRuleId    派单规则
     * @return List<OcStCSendRuleWarehouseRate>
     */
    public List<StCSendRuleWarehouseRateDO> selectWarehouseRateMapper(List<Long> warehouseList, Long sendRuleId) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("selectWarehouseRateMapper入参{}", "实体仓库Id集合",sendRuleId), "实体仓库Id集合" + warehouseList + "规则id" + sendRuleId);
        }
        List<StCSendRuleWarehouseRateDO> ruleWarehouseRateList = stRpcService.selectWarehouseRateMapper(warehouseList, sendRuleId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("selectWarehouseRateMapper出参{}", "实体仓库Id集合",sendRuleId), ruleWarehouseRateList);
        }
        return ruleWarehouseRateList;
    }

    /**
     * 统计发货仓库发货数量
     *
     * @param warehouseList 实体发货仓库
     * @param sendRuleId    派单规则
     * @return BigDecimal
     */
    public BigDecimal selectQtySendTotal(List<Long> warehouseList, Long sendRuleId) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("selectQtySendTotal入参{}", "实体仓库Id集合",sendRuleId), "实体仓库Id集合" + warehouseList + "规则id" + sendRuleId);
        }
        BigDecimal qtyTotal = stRpcService.selectQtySendTotal(warehouseList, sendRuleId);
        if (log.isDebugEnabled()) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectQtySendTotal出参{}", "实体仓库Id集合",sendRuleId), qtyTotal);
            }
        }
        return qtyTotal;
    }

    /**
     * 更新发货仓库数量
     *
     * @param warehouseId 仓库Id
     * @return Integer
     */
    public Integer updateRuleWarehouseRate(Long warehouseId) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("updateRuleWarehouseRate入参{}", "实体仓库Id",warehouseId), "实体仓库Id" + warehouseId);
        }
        Integer result = stRpcService.updateRuleWarehouseRate(warehouseId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("updateRuleWarehouseRate出参{}", "实体仓库Id",warehouseId), result);
        }
        return result;
    }
}