package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateMapper;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDate;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateItem;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @Author: 黄世新
 * @Date: 2022/6/15 下午1:48
 * @Version 1.0
 */
@Slf4j
@Component
public class StExpiryDateDelItemService {

    @Autowired
    private StCExpiryDateMapper stCExpiryDateMapper;
    @Autowired
    private StCExpiryDateItemMapper stCExpiryDateItemMapper;

    @OmsOperationLog(operationType = "DEL",mainTableName = "ST_C_EXPIRY_DATE",itemsTableName = "ST_C_EXPIRY_DATE_ITEM")
    public ValueHolder expiryDateDelItemService(QuerySession querySession){
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(
                JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        boolean delMainFlag = param.getBoolean("isdelmtable");//是否删除主表
        Long id = param.getLong("objid");
        User user = querySession.getUser();
        if (delMainFlag) {
            // 删除主表
            if (Objects.nonNull(id) && id > 0) {
                //主表删除写在这里
            }
        } else {
            // 删除明细
            JSONObject tabitem = param.getJSONObject("tabitem");
            if (!CollectionUtils.isEmpty(tabitem)) {
                JSONArray itemArray = tabitem.getJSONArray("ST_C_EXPIRY_DATE_ITEM");
                JSONArray errorArray = new JSONArray();
                if (!CollectionUtils.isEmpty(itemArray)) {
                    deleteItemByID(itemArray, errorArray,querySession);
                }
                //修改主表信息
                updateHoldOrder(id,user);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }

        throw new NDSException("当前记录已不存在！");
    }

    private void updateHoldOrder(Long id, User user) {
        StCExpiryDate stCExpiryDate1 = stCExpiryDateMapper.selectById(id);
        if (stCExpiryDate1 == null) {
            throw new NDSException("当前记录已不存在！");
        }
        if (stCExpiryDate1.getSubmitStatus() == null || stCExpiryDate1.getSubmitStatus() != 1) {
            throw new NDSException("当前策略的状态不是未审核，不允许删除明细！");
        }

        StCExpiryDate stCExpiryDate =new StCExpiryDate();
        stCExpiryDate.setId(id);
        stCExpiryDate.setModifierid(Long.valueOf(user.getId()));//修改人id
        stCExpiryDate.setModifiername(user.getName());//修改人用户名
        stCExpiryDate.setModifieddate(new Date());//修改时间
        int n = stCExpiryDateMapper.updateById(stCExpiryDate);
        if (n<=0){
            throw new NDSException("修改主表的信息失败！");
        }
    }

    private void deleteItemByID(JSONArray itemArray, JSONArray errorArray,QuerySession querySession) {
        Map<Long,String> beforeDelObjMap = new HashMap<>();
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemid = itemArray.getLong(i);
            StCExpiryDateItem stCExpiryDateItem = stCExpiryDateItemMapper.selectById(itemid);
            if (stCExpiryDateItem != null) {
                beforeDelObjMap.put(itemid,JSON.toJSONString(stCExpiryDateItem));
            }
            if ((stCExpiryDateItemMapper.deleteById(itemid)) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemid, "删除明细失败"));
            }
        }
        querySession.setAttribute("beforeDelObjMap",beforeDelObjMap);
    }
}
