package com.jackrain.nea.st.service;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.enums.AppointDimensionEnum;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateItem;
import com.jackrain.nea.st.model.StExpiryDateRelation;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 商品效期策略查询服务
 **/
@Slf4j
@Service
public class StExpirationDataQueryService {

    @Autowired
    private StExpirationDataService stExpirationDataService;

    /**
     * 查询公共的商品效期策略
     *
     * @param monthStart 月份起始数
     * @return 效期策略列表
     */
    public ValueHolderV14<List<String>> queryCommonExpirationData(Integer monthStart) {
        ValueHolderV14<List<String>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        if (monthStart == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("月份起始数不能为空");
            return v14;
        }
        try {
            // 根据monthStart计算目标月份的第一天和最后一天
            String[] targetMonthRange = calculateTargetMonthRange(monthStart);
            String firstDay = targetMonthRange[0];
            String lastDay = targetMonthRange[1];
            log.info(LogUtil.format("StExpirationDataQueryService.queryCommonExpirationData firstDay:{},lastDay:{}",
                    "StExpirationDataQueryService.queryCommonExpirationData"), firstDay, lastDay);
            //调用现有的 selectExpirationCommon 方法查询公共效期策略
            StExpiryDateRelation commonExpiration = stExpirationDataService.selectExpirationCommon();
            if (commonExpiration == null || CollectionUtils.isEmpty(commonExpiration.getExpiryDateItems())) {
                return v14;
            }
            //过滤出appointDimension为商品编码的明细
            List<StCExpiryDateItem> productCodeItems = commonExpiration.getExpiryDateItems().stream()
                    .filter(item -> AppointDimensionEnum.PRO_CODE.getKey().equals(item.getAppointDimension()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productCodeItems)) {
                return v14;
            }
            List<String> result = new ArrayList<>();
            //轮循productCodeItems获取符合条件的明细
            for (StCExpiryDateItem item : productCodeItems) {
                if (isItemInTargetRange(item, firstDay, lastDay)) {
                    if (item.getAppointContent() != null) {
                        result.add(item.getAppointContent());
                    }
                }
            }
            v14.setData(result);
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    /**
     * 根据monthStart计算目标月份的第一天和最后一天
     *
     * @param monthStart 月份起始数
     * @return 数组[第一天, 最后一天]，格式为yyyyMMdd
     */
    private String[] calculateTargetMonthRange(Integer monthStart) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());

        // 计算目标月份：当前月份 - monthStart
        calendar.add(Calendar.MONTH, -monthStart);

        // 设置为该月的第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDay = calendar.getTime();

        // 设置为该月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date lastDay = calendar.getTime();

        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        return new String[]{format.format(firstDay), format.format(lastDay)};
    }

    /**
     * 判断明细是否在目标月份范围内
     *
     * @param item     效期策略明细
     * @param firstDay 目标月份第一天
     * @param lastDay  目标月份最后一天
     * @return 是否符合条件
     */
    private boolean isItemInTargetRange(StCExpiryDateItem item, String firstDay, String lastDay) {
        try {
            String dateToCheck = null;

            // 根据appointType判断处理方式
            if (item.getAppointType() != null) {
                if (item.getAppointType() == 1) {
                    // 1-生产日期范围：取startDateDay
                    dateToCheck = item.getStartDateDay();
                } else if (item.getAppointType() == 2) {
                    // 2-生产天数范围：使用computeDate方法转换endDateDay
                    if (item.getEndDateDay() != null) {
                        dateToCheck = computeDate(Integer.parseInt(item.getEndDateDay()));
                    }
                }
            }

            if (dateToCheck == null) {
                return false;
            }

            // 判断日期是否在范围内（包含边界）
            return dateToCheck.compareTo(firstDay) >= 0 && dateToCheck.compareTo(lastDay) <= 0;

        } catch (Exception e) {
            log.error("判断明细是否在目标范围内时发生异常，item: {}", item, e);
            return false;
        }
    }

    /**
     * 根据指定天数转换成日期（参考SgOccupiedInventoryService的实现）
     *
     * @param day 天数
     * @return yyyyMMdd格式的日期字符串
     */
    private String computeDate(Integer day) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(new Date());
        ca.add(Calendar.DATE, 0 - day);
        Date time = ca.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        return format.format(time);
    }
}
