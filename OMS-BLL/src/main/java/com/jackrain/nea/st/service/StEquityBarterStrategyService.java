package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategy;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategyItem;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/6/16 16:02
 */
@Component
@Slf4j
public class StEquityBarterStrategyService {

    // 对等换货策略
    private static final String ST_C_EQUITY_BARTER_STRATEGY = "ST_C_EQUITY_BARTER_STRATEGY";
    private static final String ST_C_EQUITY_BARTER_STRATEGY_ITEM = "ST_C_EQUITY_BARTER_STRATEGY_ITEM";

    @Autowired
    private StCEquityBarterStrategyMapper mapper;
    @Autowired
    private StCEquityBarterStrategyItemMapper itemMapper;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    @OmsOperationLog(mainTableName = "ST_C_EQUITY_BARTER_STRATEGY",itemsTableName = "ST_C_EQUITY_BARTER_STRATEGY_ITEM")
    @Transactional
    public ValueHolder save(QuerySession querySession) {
        //获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), null);
        log.info(LogUtil.format("param:{}", "BansAreaStrategyService.save"), JSONObject.toJSONString(param));
        Long id = param.getLong("objid");
        JSONObject data = param.getJSONObject("fixcolumn");
        String str = data.getString(ST_C_EQUITY_BARTER_STRATEGY);
        String itemStr = data.getString(ST_C_EQUITY_BARTER_STRATEGY_ITEM);
        if (StringUtils.isEmpty(str) && StringUtils.isEmpty(itemStr)) {
            throw new NDSException("保存数据为空！");
        }
        List<StCEquityBarterStrategyItem> items = null;
        User user = querySession.getUser();

        log.debug("jxxxxx {}", JSONObject.toJSONString(user));

        if (!StringUtils.isEmpty(str)) {
            //json转换成对象
            StCEquityBarterStrategy saveModel =
                    JSONObject.parseObject(str, StCEquityBarterStrategy.class);
            //只能有一条公用类型
            if ("1".equals(saveModel.getType())) {
                List<StCEquityBarterStrategy> existList = mapper.selectList(
                        new QueryWrapper<StCEquityBarterStrategy>().lambda()
                                .eq(StCEquityBarterStrategy::getType, saveModel.getType())
                                .ne(id != null && id > 0, StCEquityBarterStrategy::getId, id)
                                .eq(StCEquityBarterStrategy::getIsactive, YesNoEnum.Y.getKey()));
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(existList)) {
                    return ValueHolderUtils.getFailValueHolder("已存在一条可用的公用类型数据!");
                }
            } else if ("2".equals(saveModel.getType())) {
                List<StCEquityBarterStrategy> existList = mapper.selectList(
                        new QueryWrapper<StCEquityBarterStrategy>().lambda()
                                .eq(StCEquityBarterStrategy::getCpCShopId, saveModel.getCpCShopId())
                                .ne(id != null && id > 0, StCEquityBarterStrategy::getId, id)
                                .eq(StCEquityBarterStrategy::getIsactive, YesNoEnum.Y.getKey()));
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(existList)) {
                    return ValueHolderUtils.getFailValueHolder("已存在一条相同平台店铺数据，不允许保存!");
                }
            }

            if (id == null || id < 1) {
                // 新增
                id = ModelUtil.getSequence(ST_C_EQUITY_BARTER_STRATEGY);
                saveModel.setId(id);
                StBeanUtils.makeCreateField(saveModel, user);
                mapper.insert(saveModel);
            } else {
                StCEquityBarterStrategy beforModel = mapper.selectById(id);
                if (beforModel == null) {
                    return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
                }
                if (beforModel.getCpCShopId() == null) {
                    redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_COMMON);
                } else {
                    redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + beforModel.getCpCShopId());
                }
                StBeanUtils.makeModifierField(saveModel, user);
                saveModel.setId(id);
                mapper.updateById(saveModel);
            }
        }

        if (!StringUtils.isEmpty(itemStr)) {
            items = JSONObject.parseArray(itemStr, StCEquityBarterStrategyItem.class);
            saveItem(items, user, id);
            StCEquityBarterStrategy beforModel = mapper.selectById(id);
            if (beforModel.getCpCShopId() == null) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_COMMON);
            } else {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + beforModel.getCpCShopId());
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(id, "保存成功！");
    }

    private void saveItem(List<StCEquityBarterStrategyItem> items, User user, Long objid) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        List<StCEquityBarterStrategyItem> strategyItems =
                itemMapper.selectList(new QueryWrapper<StCEquityBarterStrategyItem>().lambda()
                        .select(StCEquityBarterStrategyItem::getPsCSkuId, StCEquityBarterStrategyItem::getQty,
                                StCEquityBarterStrategyItem::getEquitySkuId)
                        .eq(StCEquityBarterStrategyItem::getStCEquityBarterStrategyId, objid));
        Map<String,BigDecimal> strategyQtyMap = new HashMap<>(16);
        Map<String,Long> strategySkuMap = new HashMap<>(16);
        for (StCEquityBarterStrategyItem strategyItem : strategyItems) {
            strategyQtyMap.put(strategyItem.getPsCSkuId() + ":" + strategyItem.getQty().stripTrailingZeros().toPlainString(), strategyItem.getQty());
            strategySkuMap.put(strategyItem.getPsCSkuId() + ":"  + strategyItem.getEquitySkuId(), strategyItem.getEquitySkuId());
        }

        ArrayList<StCEquityBarterStrategyItem> insertModels = Lists.newArrayList();
        ArrayList<StCEquityBarterStrategyItem> updateModels = Lists.newArrayList();
        for (StCEquityBarterStrategyItem item : items) {
            Long itemId = item.getId();
            BigDecimal equityQty = item.getEquityQty();
            BigDecimal qty = item.getQty();
            Long psCSkuId = item.getPsCSkuId();
            Long equitySkuId = item.getEquitySkuId();
            String outStockNoRestore = item.getOutStockNoRestore();
            if (StringUtils.isEmpty(outStockNoRestore)) {
                outStockNoRestore = "0";
            }
            if (itemId == null || itemId < 1) {
                item.setStCEquityBarterStrategyId(objid);
                StBeanUtils.makeCreateField(item, user);
                item.setId(ModelUtil.getSequence(ST_C_EQUITY_BARTER_STRATEGY_ITEM));
                item.setEffectiveStatus("1");
                item.setStCEquityBarterStrategyId(objid);
                item.setOutStockNoRestore(outStockNoRestore);
                insertModels.add(item);
            } else {
                StCEquityBarterStrategyItem beforItem = itemMapper.selectById(itemId);
                if (beforItem == null) {
                    throw new NDSException("当前明细记录已不存在！");
                }
                equityQty = equityQty == null ? beforItem.getEquityQty() : equityQty;
                qty = qty == null ? beforItem.getQty() : qty;
                psCSkuId = psCSkuId == null ? beforItem.getPsCSkuId() : psCSkuId;
                equitySkuId = equitySkuId == null ? beforItem.getEquitySkuId() : equitySkuId;
                StBeanUtils.makeModifierField(item, user);
                updateModels.add(item);
            }
            if (equityQty == null || BigDecimal.ONE.compareTo(equityQty) != 0) {
                throw new NDSException("对等数量不可为空且必须为1");
            }
            if (qty == null || BigDecimal.ZERO.compareTo(qty) > 0) {
                throw new NDSException("换货数量不可为空且必须大于0");
            }
            if (qty.compareTo(equityQty) < 0) {
                throw new NDSException("【换货数量】小于【对等数量】，不允许少换多！");
            }

            ProductSku sku = psRpcService.selectProductById(psCSkuId + "");
            if (sku == null) {
                throw new NDSException("换货商品SKU已不存在！");
            }
            BigDecimal existQty =  strategyQtyMap.get(item.getPsCSkuId() + ":" + item.getQty());
            if (existQty != null ) {
                throw new NDSException("已存在换货商品:" + sku.getSkuEcode() + ",换货数量:" + existQty + ",不允许重复录入！");
            }else{
                strategyQtyMap.put(item.getPsCSkuId() + ":" + item.getQty(),item.getQty());
            }
            item.setPsCSkuName(sku.getName());
            item.setPsCSkuCode(sku.getSkuEcode());
            ProductSku skuInfo = psRpcService.selectProductById(equitySkuId + "");
            if (skuInfo == null) {
                throw new NDSException("对等商品SKU已不存在！");
            }
            Long exist =  strategySkuMap.get(psCSkuId + ":" + equitySkuId);
            if (exist != null) {
                throw new NDSException("已存在换货商品:" + sku.getSkuEcode() + ",对等商品:" + skuInfo.getSkuEcode() + ",不允许重复录入！");
            }else{
                strategySkuMap.put(psCSkuId + ":" + equitySkuId, equitySkuId);
            }
            item.setEquitySkuName(skuInfo.getName());
            item.setEquitySkuCode(skuInfo.getSkuEcode());
            if (equityQty.compareTo(qty) == 0) {
                item.setExchangeType("2");
            } else {
                item.setExchangeType("1");
            }
        }
        if (!CollectionUtils.isEmpty(insertModels)) {
            itemMapper.batchInsert(insertModels);
        }
        for (StCEquityBarterStrategyItem updateModel : updateModels) {
            itemMapper.updateById(updateModel);
        }
    }

    @OmsOperationLog(operationType = "DEL",mainTableName = "ST_C_EQUITY_BARTER_STRATEGY",itemsTableName = "ST_C_EQUITY_BARTER_STRATEGY_ITEM")
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder del(QuerySession querySession) {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCBusinessTypeDeleteCmdImpl param：{}", "单据业务类型"), param);
        }
        Long objid = param.getLong("objid");
        StCEquityBarterStrategy stCEquityBarterStrategy = mapper.selectById(objid);
        JSONObject tabItem = param.getJSONObject("tabitem");
        //判断是不是只删除子表
        if (Objects.nonNull(tabItem) && org.apache.commons.collections.CollectionUtils.isNotEmpty(tabItem.getJSONArray("ST_C_EQUITY_BARTER_STRATEGY_ITEM"))) {
            JSONArray itemIds = tabItem.getJSONArray("ST_C_EQUITY_BARTER_STRATEGY_ITEM");
            List<String> itemIdList = JSONArray.parseArray(itemIds.toJSONString(), String.class);
            List<StCEquityBarterStrategyItem> itemList = itemMapper.selectBatchIds(itemIdList);
            if (!CollectionUtils.isEmpty(itemList)) {
                Map<Long, String> beforeDelObjMap = new HashMap<>();
                for (StCEquityBarterStrategyItem item : itemList) {
                    beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                }
                querySession.setAttribute("beforeDelObjMap", beforeDelObjMap);
            }
            itemMapper.deleteBatchIds(itemIdList);
        } else {
            //删除主表
            mapper.deleteById(objid);
            //删除关联的全部子表
            QueryWrapper<StCEquityBarterStrategyItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StCEquityBarterStrategyItem::getStCEquityBarterStrategyId, objid);
            List<StCEquityBarterStrategyItem> strategyItemList = itemMapper.selectList(queryWrapper);
            log.info(LogUtil.format("StEquityBarterStrategyService.delMain strategy:{},strategyItemList:{}",
                    "StEquityBarterStrategyService.delMain"),
                    JSONObject.toJSONString(stCEquityBarterStrategy), JSONObject.toJSONString(strategyItemList));
            itemMapper.delete(queryWrapper);
        }

        //删除缓存
        if (stCEquityBarterStrategy != null) {
            if (stCEquityBarterStrategy.getCpCShopId() == null) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_COMMON);
            } else {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + stCEquityBarterStrategy.getCpCShopId());
            }
        }
        return com.jackrain.nea.cpext.utils.ValueHolderUtils.success("删除成功！");
    }
}
