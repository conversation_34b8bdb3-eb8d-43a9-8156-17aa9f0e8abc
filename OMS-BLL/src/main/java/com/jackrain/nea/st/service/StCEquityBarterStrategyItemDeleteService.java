package com.jackrain.nea.st.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.log.service.LogCommonService;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.EquityBarterBatchDelItemShopTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.EquityBarterExchangeTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.EquityBarterTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategy;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategyItem;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略明细删除服务
 **/
@Slf4j
@Service
public class StCEquityBarterStrategyItemDeleteService {

    @Autowired
    private StCEquityBarterStrategyItemMapper itemMapper;

    @Autowired
    private StCEquityBarterStrategyMapper strategyMapper;

    @Autowired
    private LogCommonService logCommonService;

    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    /**
     * 删除对等换货策略明细
     *
     * @param shopType
     * @param shopIds
     * @param exchangeSkuId
     * @param exchangeQty
     * @param equitySkuId
     * @param equityQty
     * @param user
     * @return
     */
    public ValueHolderV14<Void> deleteStrategyItems(Integer shopType, List<Long> shopIds, Long exchangeSkuId,
                                                    BigDecimal exchangeQty, Long equitySkuId, BigDecimal equityQty, User user) {
        ValueHolderV14<Void> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "对等明细删除完毕，请检查数据！");
        try {
            // 第一步：先查询主表，获取符合条件的策略列表
            List<StCEquityBarterStrategy> strategies = findStrategies(shopType, shopIds);
            if (CollectionUtils.isEmpty(strategies)) {
                throw new NDSException("未找到符合条件的对等换货策略！");
            }
            // 提取策略ID列表
            List<Long> strategyIds = strategies.stream()
                    .map(StCEquityBarterStrategy::getId)
                    .collect(Collectors.toList());
            // 第二步：根据策略ID列表和前端入参查询策略明细
            List<StCEquityBarterStrategyItem> itemsToDelete =
                    findStrategyItems(exchangeSkuId, exchangeQty, equitySkuId, equityQty, strategyIds);

            if (CollectionUtils.isEmpty(itemsToDelete)) {
                throw new NDSException("未找到符合条件的对等换货策略明细！");
            }
            StCEquityBarterStrategyItemDeleteService bean =
                    ApplicationContextHandle.getBean(StCEquityBarterStrategyItemDeleteService.class);
            bean.delAndSaveLog(user, itemsToDelete, strategies);
            v14.setMessage("对等明细删除完毕共" + itemsToDelete.size() + "条，请检查数据！");
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    @Transactional(rollbackFor = Exception.class)
    public void delAndSaveLog(User user, List<StCEquityBarterStrategyItem> itemsToDelete,
                              List<StCEquityBarterStrategy> strategies) {
        // 记录删除日志
        List<OcBOperationLog> operationLogs = buildDeleteLogs(itemsToDelete, user);
        // 执行删除
        List<Long> itemIds = itemsToDelete.stream().map(StCEquityBarterStrategyItem::getId).collect(Collectors.toList());
        itemMapper.deleteBatchIds(itemIds);
        // 批量保存日志
        if (CollectionUtils.isNotEmpty(operationLogs)) {
            logCommonService.batchInsertLog(operationLogs);
        }
        // 清除相关缓存（使用主表策略列表）
        clearRelatedCache(strategies);
    }

    /**
     * 查找符合条件的策略列表
     */
    private List<StCEquityBarterStrategy> findStrategies(Integer shopType, List<Long> shopIds) {
        QueryWrapper<StCEquityBarterStrategy> strategyQueryWrapper = new QueryWrapper<>();

        // 限制只查询指定店铺的策略
        strategyQueryWrapper.lambda().eq(StCEquityBarterStrategy::getType, EquityBarterTypeEnum.APPOINT_SHOP.getKey());

        // 根据店铺类型进一步限制
        if (EquityBarterBatchDelItemShopTypeEnum.SPECIFIED_SHOP.getCode().equals(shopType)) {
            // 指定店铺：限制店铺ID在指定列表中
            if (CollectionUtils.isNotEmpty(shopIds)) {
                strategyQueryWrapper.lambda().in(StCEquityBarterStrategy::getCpCShopId, shopIds);
            }
        }
        return strategyMapper.selectList(strategyQueryWrapper);
    }

    /**
     * 根据策略ID列表和前端入参查询策略明细
     */
    private List<StCEquityBarterStrategyItem> findStrategyItems(Long exchangeSkuId, BigDecimal exchangeQty,
                                                                Long equitySkuId, BigDecimal equityQty, List<Long> strategyIds) {
        QueryWrapper<StCEquityBarterStrategyItem> itemQueryWrapper = new QueryWrapper<>();
        itemQueryWrapper.lambda()
                .in(StCEquityBarterStrategyItem::getStCEquityBarterStrategyId, strategyIds)
                .eq(StCEquityBarterStrategyItem::getPsCSkuId, exchangeSkuId)
                .eq(StCEquityBarterStrategyItem::getQty, exchangeQty)
                .eq(StCEquityBarterStrategyItem::getEquitySkuId, equitySkuId)
                .eq(StCEquityBarterStrategyItem::getEquityQty, equityQty);

        return itemMapper.selectList(itemQueryWrapper);
    }

    /**
     * 构建删除日志（优化版本）
     */
    private List<OcBOperationLog> buildDeleteLogs(List<StCEquityBarterStrategyItem> itemsToDelete, User user) {
        List<OcBOperationLog> operationLogs = new ArrayList<>();

        for (StCEquityBarterStrategyItem item : itemsToDelete) {
            // 构建日志内容：[换货商品编码],[换货商品名称],[换货数量],[对等商品编码],[对等商品名称],[对等数量],[换货类型描述]
            StringBuilder logContent = new StringBuilder();
            logContent.append("[").append(item.getPsCSkuCode()).append("]")
                    .append(",[").append(item.getPsCSkuName()).append("]")
                    .append(",[").append(item.getQty().stripTrailingZeros().toPlainString()).append("]")
                    .append(",[").append(item.getEquitySkuCode()).append("]")
                    .append(",[").append(item.getEquitySkuName()).append("]")
                    .append(",[").append(item.getEquityQty().stripTrailingZeros().toPlainString()).append("]")
                    .append(",[").append(EquityBarterExchangeTypeEnum.getByKey(item.getExchangeType()).getDesc()).append("]");

            OcBOperationLog operationLog = logCommonService.getOperationLog(
                    "ST_C_EQUITY_BARTER_STRATEGY_ITEM",
                    "DEL",
                    item.getStCEquityBarterStrategyId(),
                    "对等换货策略明细",
                    "删除对等换货策略明细",
                    logContent.toString(),
                    null,
                    user
            );

            operationLogs.add(operationLog);
        }

        return operationLogs;
    }

    /**
     * 清除相关缓存
     */
    private void clearRelatedCache(List<StCEquityBarterStrategy> strategies) {
        for (StCEquityBarterStrategy strategy : strategies) {
            if (strategy.getCpCShopId() == null) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_COMMON);
            } else {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + strategy.getCpCShopId());
            }
        }
    }
}
