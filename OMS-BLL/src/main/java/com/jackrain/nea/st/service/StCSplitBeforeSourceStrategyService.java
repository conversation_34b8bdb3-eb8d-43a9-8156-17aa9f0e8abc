package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.StCSplitBeforeSourceStrategyCategoryItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCSplitBeforeSourceStrategyMapper;
import com.jackrain.nea.oc.oms.mapper.StCSplitBeforeSourceStrategySkuItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCSplitBeforeSourceStrategyWeightItemMapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCSplitBeforeSourceStrategy;
import com.jackrain.nea.oc.oms.model.table.StCSplitBeforeSourceStrategyCategoryItem;
import com.jackrain.nea.oc.oms.model.table.StCSplitBeforeSourceStrategySkuItem;
import com.jackrain.nea.oc.oms.model.table.StCSplitBeforeSourceStrategyWeightItem;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.model.enums.CommStatusEnum;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 寻源前拆单策略
 * @author: caomalai
 * @create: 2022-07-29 14:01
 **/
@Component
@Slf4j
public class StCSplitBeforeSourceStrategyService {
    @Autowired
    private StCSplitBeforeSourceStrategyMapper stCSplitBeforeSourceStrategyMapper;
    @Autowired
    private StCSplitBeforeSourceStrategyCategoryItemMapper stCSplitBeforeSourceStrategyCategoryItemMapper;
    @Autowired
    private StCSplitBeforeSourceStrategyWeightItemMapper stCSplitBeforeSourceStrategyWeightItemMapper;
    @Autowired
    private StCSplitBeforeSourceStrategySkuItemMapper stCSplitBeforeSourceStrategySkuItemMapper;
    @Autowired
    private PsRpcService psRpcService;


    @Transactional(rollbackFor = Exception.class)
    public Long save(JSONObject param, User user) {
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        Long objid = param.getLong("objid");
        String tableName = param.getString("table");
        JSONObject jsonObject = fixColumn.getJSONObject(tableName);
        if (Objects.nonNull(jsonObject)) {
            StCSplitBeforeSourceStrategy stCSplitBeforeSourceStrategy
                    = JSONObject.parseObject(jsonObject.toJSONString(), StCSplitBeforeSourceStrategy.class);
            //校验业务类型唯一性
            QueryWrapper<StCSplitBeforeSourceStrategy> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StCSplitBeforeSourceStrategy::getEname,stCSplitBeforeSourceStrategy.getEname())
                    .eq(StCSplitBeforeSourceStrategy::getIsactive, YesNoEnum.Y.getKey())
                    .ne(objid>0,StCSplitBeforeSourceStrategy::getId,stCSplitBeforeSourceStrategy.getId());
            List<StCSplitBeforeSourceStrategy> itemList = stCSplitBeforeSourceStrategyMapper.selectList(queryWrapper);
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(itemList)){
                throw new NDSException("策略名称重复，不允许保存");
            }
            //新增
            if (Objects.isNull(objid) || objid < 0) {
                Long id = ModelUtil.getSequence(tableName.toLowerCase());
                stCSplitBeforeSourceStrategy.setId(id);
                StBeanUtils.makeCreateField(stCSplitBeforeSourceStrategy,user);
                stCSplitBeforeSourceStrategyMapper.insert(stCSplitBeforeSourceStrategy);
                objid = id;
            } else {
                //更新
                JSONObject afterValue = param.getJSONObject("aftervalue");
                JSONObject jsonObjectAfter = afterValue.getJSONObject(tableName);
                stCSplitBeforeSourceStrategy
                        = JSONObject.parseObject(jsonObjectAfter.toJSONString(), StCSplitBeforeSourceStrategy.class);
                stCSplitBeforeSourceStrategy.setId(objid);
                StBeanUtils.makeModifierField(stCSplitBeforeSourceStrategy,user);
                if(CommStatusEnum.YES.desc().equals(stCSplitBeforeSourceStrategy.getIsactive())){
                    stCSplitBeforeSourceStrategy.setIsactive(CommStatusEnum.YES.charVal());
                }
                if(CommStatusEnum.NO.desc().equals(stCSplitBeforeSourceStrategy.getIsactive())){
                    stCSplitBeforeSourceStrategy.setIsactive(CommStatusEnum.NO.charVal());
                }
                stCSplitBeforeSourceStrategyMapper.updateById(stCSplitBeforeSourceStrategy);
            }
        }
        //保存子表
        saveCategoryItem(objid,fixColumn,user);
        saveWeightItem(objid,fixColumn,user);
        saveSkuItem(objid,fixColumn,user);
        return objid;
    }

    /**
     * 按sku拆单明细表
     * @param objid
     * @param fixColumn
     * @param user
     */
    private void saveSkuItem(Long objid, JSONObject fixColumn, User user) {
        //子表更新
        String subTableName = "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_SKU_ITEM";
        JSONArray jsonObjectItems = fixColumn.getJSONArray(subTableName);
        if (CollectionUtils.isNotEmpty(jsonObjectItems)) {
            List<StCSplitBeforeSourceStrategySkuItem> insertList = new ArrayList<>();
            List<StCSplitBeforeSourceStrategySkuItem> updateList = new ArrayList<>();
            List<StCSplitBeforeSourceStrategySkuItem> stCSplitBeforeSourceStrategySkuItems =
                    JSONArray.parseArray(jsonObjectItems.toJSONString(), StCSplitBeforeSourceStrategySkuItem.class);
            Long finalObjid = objid;

            List<StCSplitBeforeSourceStrategySkuItem> existItems =
                    stCSplitBeforeSourceStrategySkuItemMapper.selectList(
                            new QueryWrapper<StCSplitBeforeSourceStrategySkuItem>().lambda()
                                    .eq(StCSplitBeforeSourceStrategySkuItem::getStCSplitBeforeSourceStrategyId, objid));
            List<Long> collect;
            if (CollectionUtils.isNotEmpty(existItems)) {
                collect = existItems.stream().map(p -> p.getPsCSkuId()).collect(Collectors.toList());
            } else {
                collect = new ArrayList<>();
            }
            stCSplitBeforeSourceStrategySkuItems.forEach(p -> {
                //业务逻辑判断
                //最大商品数量>0
                BigDecimal maxQty = p.getMaxQty();
                if(Objects.nonNull(maxQty) && BigDecimal.ZERO.compareTo(maxQty)>=0){
                    throw new NDSException("最大商品数量必须大于0");
                }

                //如果录入的SKU重复 已存在相同的记录，则提示：明细SKU重复，不允许保存
                Long content = p.getPsCSkuId();
                if (collect.contains(content)) {
                    throw new NDSException("明细SKU重复，不允许保存");
                }
                if (p.getId() == null || p.getId() < 0) {
                    p.setStCSplitBeforeSourceStrategyId(finalObjid);
                    Long id = ModelUtil.getSequence(subTableName.toLowerCase());
                    p.setId(id);
                    //查询sku
                    ProductSku productSku = psRpcService.selectProductById(String.valueOf(p.getPsCSkuId()));
                    p.setPsCSkuEcode(productSku.getSkuEcode());
                    p.setPsCProName(productSku.getName());
                    StBeanUtils.makeCreateField(p, user);
                    insertList.add(p);
                } else {
                    StBeanUtils.makeModifierField(p, user);
                    updateList.add(p);
                }

            });
            if (CollectionUtils.isNotEmpty(insertList)) {
                stCSplitBeforeSourceStrategySkuItemMapper.batchInsert(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                for (StCSplitBeforeSourceStrategySkuItem item : updateList) {
                    stCSplitBeforeSourceStrategySkuItemMapper.updateById(item);
                }
            }
        }
    }

    /**
     * 按重量拆单明细表
     * @param objid
     * @param fixColumn
     * @param user
     */
    private void saveWeightItem(Long objid, JSONObject fixColumn, User user) {
        //子表更新
        String subTableName = "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_WEIGHT_ITEM";
        JSONArray jsonObjectItems = fixColumn.getJSONArray(subTableName);
        if (CollectionUtils.isNotEmpty(jsonObjectItems)) {
            List<StCSplitBeforeSourceStrategyWeightItem> insertList = new ArrayList<>();
            List<StCSplitBeforeSourceStrategyWeightItem> updateList = new ArrayList<>();
            List<StCSplitBeforeSourceStrategyWeightItem> stCSplitBeforeSourceStrategyWeightItems =
                    JSONArray.parseArray(jsonObjectItems.toJSONString(), StCSplitBeforeSourceStrategyWeightItem.class);
            Long finalObjid = objid;

            List<StCSplitBeforeSourceStrategyWeightItem> existItems =
                    stCSplitBeforeSourceStrategyWeightItemMapper.selectList(
                            new QueryWrapper<StCSplitBeforeSourceStrategyWeightItem>().lambda()
                                    .eq(StCSplitBeforeSourceStrategyWeightItem::getStCSplitBeforeSourceStrategyId, objid));
            List<Long> collect;
            if (CollectionUtils.isNotEmpty(existItems)) {
                collect = existItems.stream().map(p -> p.getPsCProdimItemId()).collect(Collectors.toList());
            } else {
                collect = new ArrayList<>();
            }
            stCSplitBeforeSourceStrategyWeightItems.forEach(p -> {
                //业务逻辑判断
                //重量必须>0
                BigDecimal maxWeight = p.getMaxWeight();
                if(Objects.nonNull(maxWeight) && BigDecimal.ZERO.compareTo(maxWeight)>=0){
                    throw new NDSException("最大商品重量必须大于0");
                }
                //如果录入的品类重复 已存在相同的记录，则提示：明细品类重复，不允许保存
                Long content = p.getPsCProdimItemId();
                if (collect.contains(content)) {
                    throw new NDSException("明细品类重复，不允许保存");
                }
                if (p.getId() == null || p.getId() < 0) {
                    p.setStCSplitBeforeSourceStrategyId(finalObjid);
                    Long id = ModelUtil.getSequence(subTableName.toLowerCase());
                    p.setId(id);
                    StBeanUtils.makeCreateField(p, user);
                    insertList.add(p);
                } else {
                    StBeanUtils.makeModifierField(p, user);
                    updateList.add(p);
                }

            });
            if (CollectionUtils.isNotEmpty(insertList)) {
                stCSplitBeforeSourceStrategyWeightItemMapper.batchInsert(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                for (StCSplitBeforeSourceStrategyWeightItem item : updateList) {
                    stCSplitBeforeSourceStrategyWeightItemMapper.updateById(item);
                }
            }
        }
    }

    /**
     * 保存按品类拆单明细表
     * @param fixColumn
     * @param user
     */
    private void saveCategoryItem(Long objid, JSONObject fixColumn, User user) {
        //子表更新
        String subTableName = "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_CATEGORY_ITEM";
        JSONArray jsonObjectItems = fixColumn.getJSONArray(subTableName);
        if (CollectionUtils.isNotEmpty(jsonObjectItems)) {
            List<StCSplitBeforeSourceStrategyCategoryItem> insertList = new ArrayList<>();
            List<StCSplitBeforeSourceStrategyCategoryItem> updateList = new ArrayList<>();
            List<StCSplitBeforeSourceStrategyCategoryItem> stCSplitBeforeSourceStrategyCategoryItems =
                    JSONArray.parseArray(jsonObjectItems.toJSONString(), StCSplitBeforeSourceStrategyCategoryItem.class);
            Long finalObjid = objid;

            List<StCSplitBeforeSourceStrategyCategoryItem> existItems =
                    stCSplitBeforeSourceStrategyCategoryItemMapper.selectList(
                            new QueryWrapper<StCSplitBeforeSourceStrategyCategoryItem>().lambda()
                                    .eq(StCSplitBeforeSourceStrategyCategoryItem::getStCSplitBeforeSourceStrategyId, objid));
            List<Long> collect;
            if (CollectionUtils.isNotEmpty(existItems)) {
                collect = existItems.stream().map(p -> p.getPsCProdimItemId()).collect(Collectors.toList());
            } else {
                collect = new ArrayList<>();
            }
            stCSplitBeforeSourceStrategyCategoryItems.forEach(p -> {
                //业务逻辑判断
                //如果录入的品类重复 已存在相同的记录，则提示：明细品类重复，不允许保存
                Long content = p.getPsCProdimItemId();
                if (collect.contains(content)) {
                    throw new NDSException("明细品类重复，不允许保存");
                }
                if (p.getId() == null || p.getId() < 0) {
                    p.setStCSplitBeforeSourceStrategyId(finalObjid);
                    Long id = ModelUtil.getSequence(subTableName.toLowerCase());
                    p.setId(id);
                    StBeanUtils.makeCreateField(p, user);
                    insertList.add(p);
                } else {
                    StBeanUtils.makeModifierField(p, user);
                    updateList.add(p);
                }

            });
            if (CollectionUtils.isNotEmpty(insertList)) {
                stCSplitBeforeSourceStrategyCategoryItemMapper.batchInsert(insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                for (StCSplitBeforeSourceStrategyCategoryItem item : updateList) {
                    stCSplitBeforeSourceStrategyCategoryItemMapper.updateById(item);
                }
            }
        }
    }

    /**
     * 删除
     * @param
     * @param param
     * @param querySession
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(JSONObject param, QuerySession querySession) {
        Long objid = param.getLong("objid");
        JSONObject tabItem = param.getJSONObject("tabitem");
        //判断是不是只删除子表
        if (Objects.nonNull(tabItem)
                && (CollectionUtils.isNotEmpty(tabItem.getJSONArray("ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_CATEGORY_ITEM"))
                ||CollectionUtils.isNotEmpty(tabItem.getJSONArray("ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_WEIGHT_ITEM"))
                ||CollectionUtils.isNotEmpty(tabItem.getJSONArray("ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_SKU_ITEM"))
        )) {
            Map<Long,String> beforeDelObjMap = new HashMap<>();
            JSONArray strategyItem = tabItem.getJSONArray("ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_CATEGORY_ITEM");
            if(CollectionUtils.isNotEmpty(strategyItem)){
                List<String> strategyItemList = JSONArray.parseArray(strategyItem.toJSONString(), String.class);
                List<StCSplitBeforeSourceStrategyCategoryItem> itemList =
                        stCSplitBeforeSourceStrategyCategoryItemMapper.selectBatchIds(strategyItemList);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    for (StCSplitBeforeSourceStrategyCategoryItem item : itemList) {
                        beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                    }
                }
                stCSplitBeforeSourceStrategyCategoryItemMapper.deleteBatchIds(strategyItemList);
            }
            JSONArray weightItem = tabItem.getJSONArray("ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_WEIGHT_ITEM");
            if(CollectionUtils.isNotEmpty(weightItem)){
                List<String> weightItemList = JSONArray.parseArray(weightItem.toJSONString(), String.class);
                List<StCSplitBeforeSourceStrategyWeightItem> itemList =
                        stCSplitBeforeSourceStrategyWeightItemMapper.selectBatchIds(weightItemList);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    for (StCSplitBeforeSourceStrategyWeightItem item : itemList) {
                        beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                    }
                }
                stCSplitBeforeSourceStrategyWeightItemMapper.deleteBatchIds(weightItemList);
            }
            JSONArray skuItem = tabItem.getJSONArray("ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_SKU_ITEM");
            if(CollectionUtils.isNotEmpty(skuItem)){
                List<String> skuItemList = JSONArray.parseArray(skuItem.toJSONString(), String.class);
                List<StCSplitBeforeSourceStrategySkuItem> itemList =
                        stCSplitBeforeSourceStrategySkuItemMapper.selectBatchIds(skuItemList);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    for (StCSplitBeforeSourceStrategySkuItem item : itemList) {
                        beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
                    }
                }
                stCSplitBeforeSourceStrategySkuItemMapper.deleteBatchIds(skuItemList);
            }
            querySession.setAttribute("beforeDelObjMap",beforeDelObjMap);
        } else {
            //删除主表
            stCSplitBeforeSourceStrategyMapper.deleteById(objid);
            //删除关联的全部子表
            stCSplitBeforeSourceStrategyCategoryItemMapper.delete(new QueryWrapper<StCSplitBeforeSourceStrategyCategoryItem>()
                    .lambda().eq(StCSplitBeforeSourceStrategyCategoryItem::getStCSplitBeforeSourceStrategyId, objid));
            stCSplitBeforeSourceStrategyWeightItemMapper.delete(new QueryWrapper<StCSplitBeforeSourceStrategyWeightItem>()
                    .lambda().eq(StCSplitBeforeSourceStrategyWeightItem::getStCSplitBeforeSourceStrategyId, objid));
            stCSplitBeforeSourceStrategySkuItemMapper.delete(new QueryWrapper<StCSplitBeforeSourceStrategySkuItem>()
                    .lambda().eq(StCSplitBeforeSourceStrategySkuItem::getStCSplitBeforeSourceStrategyId, objid));


        }
    }

    /**
     * 获取可用且创建时间最近的策略id
     * @return id
     */
    public Long getRecentlySplitBeforeSourceStrategyId() {
        return stCSplitBeforeSourceStrategyMapper.getRecentlySplitBeforeSourceStrategyId();
    }

    /**
     * 查询寻源前策略明细
     * @param mainId 主表id
     * @param itemTableName 明细表名称
     * @return List
     */
    public List querySplitBeforeSourceStrategyItems(Long mainId, String itemTableName) {
        if (mainId == null) {
            return new ArrayList();
        }
        switch (itemTableName) {
            case "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_CATEGORY_ITEM":
                return stCSplitBeforeSourceStrategyCategoryItemMapper
                        .selectList(new LambdaQueryWrapper<StCSplitBeforeSourceStrategyCategoryItem>()
                                .eq(StCSplitBeforeSourceStrategyCategoryItem::getStCSplitBeforeSourceStrategyId, mainId)
                                .eq(BaseModel::getIsactive, YesNoEnum.Y.getKey()));
            case "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_WEIGHT_ITEM":
                return stCSplitBeforeSourceStrategyWeightItemMapper
                        .selectList(new LambdaQueryWrapper<StCSplitBeforeSourceStrategyWeightItem>()
                                .eq(StCSplitBeforeSourceStrategyWeightItem::getStCSplitBeforeSourceStrategyId, mainId)
                                .eq(BaseModel::getIsactive, YesNoEnum.Y.getKey()));
            case "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_SKU_ITEM":
                return stCSplitBeforeSourceStrategySkuItemMapper
                        .selectList(new LambdaQueryWrapper<StCSplitBeforeSourceStrategySkuItem>()
                                .eq(StCSplitBeforeSourceStrategySkuItem::getStCSplitBeforeSourceStrategyId, mainId)
                                .eq(BaseModel::getIsactive, YesNoEnum.Y.getKey()));
            default:
                return new ArrayList();
        }
    }
}
