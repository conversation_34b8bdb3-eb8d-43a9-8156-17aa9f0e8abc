package com.jackrain.nea.st.service;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.burgeon.r3.sg.core.common.SgRedisKeyResources;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateLabelMapper;
import com.jackrain.nea.oc.oms.model.enums.AppointTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateLabel;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.SplitListUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utility.RedisLocker;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: lijin
 * @Date: 2024/09/08
 * @Version 1.0
 */
@Slf4j
@Component
public class StCExpiryDateLabelService extends ServiceImpl<StCExpiryDateLabelMapper, StCExpiryDateLabel> {

    @Autowired
    private StCExpiryDateItemMapper stCExpiryDateItemMapper;
    @Autowired
    private StCExpiryDateLabelMapper stCExpiryDateLabelMapper;
    @Autowired
    private DefaultProducerSend defaultProducerSend;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    /**
     * 计算出库汇波标签(定时任务每天触发一次，其他场景触发需要判断定时任务是否已经跑成功过)
     *
     * @param user        用户信息
     * @param isDailyTask 是否每天任务触发
     */
    public void calculateEffectiveLabel(User user, boolean isDailyTask) {
        //判断日任务是否跑过
        String lockRedisDailyKey = BllRedisKeyResources.buildLockCalculateDateLabelKeyDaily();
        RedisLocker calculateDailyLocker = new RedisLocker(lockRedisDailyKey);
        if (isDailyTask) {
            //日任务
            boolean calculateDailyLockerSuccess = calculateDailyLocker.lock(genExpireMilliseconds());
            if (!calculateDailyLockerSuccess) {
                log.info(LogUtil.format("StCExpiryDateLabelService.calculateEffectiveLabel 日任务已经执行！",
                        "StCExpiryDateLabelService.calculateEffectiveLabel"));
                return;
            }
        } else {
            //非日任务
            boolean exists = calculateDailyLocker.exists();
            if (!exists) {
                log.error(LogUtil.format("StCExpiryDateLabelService.calculateEffectiveLabel 日任务还未完成！",
                        "StCExpiryDateLabelService.calculateEffectiveLabel"));
                throw new NDSException("日任务还未完成！");
            }
        }

        //判断是否正在计算
        String lockRedisKey = BllRedisKeyResources.buildLockCalculateDateLabelKey();
        RedisLocker calculateLocker = new RedisLocker(lockRedisKey);
        boolean calculateLockerSuccess = calculateLocker.lock(1000 * 60 * 10);
        if (!calculateLockerSuccess) {
            log.warn(LogUtil.format("StCExpiryDateLabelService.calculateEffectiveLabel 正在计算中。。。",
                    "StCExpiryDateLabelService.calculateEffectiveLabel"));
            throw new NDSException("出库汇波策略正在计算，请稍后重试！");
        }
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            //计算核心逻辑
            calculate(user);
            stopWatch.stop();
            log.info(LogUtil.format("StCExpiryDateLabelService.calculateEffectiveLabel time:{}s",
                    "StCExpiryDateLabelService.calculateEffectiveLabel"), stopWatch.getTotalTimeSeconds());
        } catch (Exception e) {
            log.warn(LogUtil.format("StCExpiryDateLabelService.calculateEffectiveLabel error:{}",
                    "StCExpiryDateLabelService.calculateEffectiveLabel"), Throwables.getStackTraceAsString(e));
            throw e;
        } finally {
            calculateLocker.unLock();
        }
    }

    /**
     * 计算
     *
     * @param user 用户信息
     */
    private void calculate(User user) {
        log.info(LogUtil.format("StCExpiryDateLabelService.calculate user:{}",
                "StCExpiryDateLabelService.calculate"), JSONObject.toJSONString(user));
        // 查询已审核或者未审核的公共的商品效期明细信息
        List<StCExpiryDateLabel> dateLabelList = stCExpiryDateItemMapper.selectExpiryDateLabel();
        if (CollectionUtils.isEmpty(dateLabelList)) {
            return;
        }
        // a. 收集商品效期明细id(用于后面物理删除使用)
        List<Long> itemIds = dateLabelList.stream()
                .map(StCExpiryDateLabel::getItemId).collect(Collectors.toList());
        // b. 按照指定类型换算新开始/结束生产日期
        convertDateFormat(dateLabelList);
        // c. 排序：按照新开始生产日期倒序、新结束生产日期倒序排序、指定类型（生产日期：1在前，生产天数：2在后）
        // d. 根据新开始生产日期+新结束生产日期分组
        Map<String, List<StCExpiryDateLabel>> groupedByDateRange = sortAndGroup(dateLabelList);
        // f. 查询【出库汇波策略】已经存在记录
        Map<Long, StCExpiryDateLabel> oldLabelMap = queryOldLabel();
        // g. 构建参数
        //     i. 根据分组获取汇波单元并赋值
        //     ii. 根据itemId判断是新增还是更新并更新用户信息
        //     iii. 根据收集的有效数据判断是否可用
        List<StCExpiryDateLabel> insertDateList = new ArrayList<>();
        List<StCExpiryDateLabel> updateDateList = new ArrayList<>();
        buildParam(user, groupedByDateRange, oldLabelMap, insertDateList, updateDateList);
        // h. 保存数据库（开启事务）
        //     i. 物理删除不存在的商品效期明细
        //     ii. 新增和更新
        //      缓存删除更新
        StCExpiryDateLabelService bean = ApplicationContextHandle.getBean(StCExpiryDateLabelService.class);
        bean.saveAndUpdate(itemIds, insertDateList, updateDateList);

    }

    /**
     * 获取过期毫秒数
     *
     * @return 当前时间距离当天最后一毫秒的毫秒数
     */
    private long genExpireMilliseconds() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTimeInMillis() - System.currentTimeMillis();
    }

    /**
     * 增删改
     *
     * @param itemIds
     * @param insertDateList
     * @param updateDateList
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveAndUpdate(List<Long> itemIds, List<StCExpiryDateLabel> insertDateList,
                              List<StCExpiryDateLabel> updateDateList) {
        stCExpiryDateLabelMapper.delete(new LambdaQueryWrapper<StCExpiryDateLabel>()
                .notIn(StCExpiryDateLabel::getItemId, itemIds));
        if (CollectionUtils.isNotEmpty(insertDateList)) {
            stCExpiryDateLabelMapper.batchInsert(insertDateList);
        }

        if (CollectionUtils.isNotEmpty(updateDateList)) {
            List<List<StCExpiryDateLabel>> lists = SplitListUtil.splitList(updateDateList, 500);
            for (List<StCExpiryDateLabel> dateLabels : lists) {
                stCExpiryDateLabelMapper.batchUpdateById(dateLabels);
            }
        }

        // 缓存删除更新
        String redisKey = SgRedisKeyResources.buildStExpiryDateLabel();
        redisOpsUtil.strRedisTemplate.delete(redisKey);
    }

    /**
     * 构建参数
     *
     * @param user
     * @param groupedByDateRange
     * @param oldLabelMap
     * @param insertDateList
     * @param updateDateList
     */
    private void buildParam(User user, Map<String, List<StCExpiryDateLabel>> groupedByDateRange,
                            Map<Long, StCExpiryDateLabel> oldLabelMap,
                            List<StCExpiryDateLabel> insertDateList, List<StCExpiryDateLabel> updateDateList) {
        for (Map.Entry<String, List<StCExpiryDateLabel>> entry : groupedByDateRange.entrySet()) {
            List<StCExpiryDateLabel> dateLabelList = entry.getValue();
            String billNo = getBillNo(user);
            for (int i = 0; i < dateLabelList.size(); i++) {
                StCExpiryDateLabel expiryDateLabel = dateLabelList.get(i);
                expiryDateLabel.setBillNo(billNo);
                StCExpiryDateLabel oldLabel = oldLabelMap.get(expiryDateLabel.getItemId());
                if (oldLabel == null) {
                    //新增
                    expiryDateLabel.setId(ModelUtil.getSequence("ST_C_EXPIRY_DATE_LABEL"));
                    OmsModelUtil.setDefault4Add(expiryDateLabel, user);
                    insertDateList.add(expiryDateLabel);
                } else {
                    //更新
                    expiryDateLabel.setId(oldLabel.getId());
                    OmsModelUtil.setDefault4Upd(expiryDateLabel, user);
                    updateDateList.add(expiryDateLabel);
                }
                if (i == 0) {
                    expiryDateLabel.setIsactive(YesNoEnum.Y.getKey());
                } else {
                    expiryDateLabel.setIsactive(YesNoEnum.N.getKey());
                }
            }
        }
    }

    private String getBillNo(User user) {
        JSONObject sequence = new JSONObject();
        return SequenceGenUtil.generateSquence("ST_C_EXPIRY_DATE_LABEL",
                sequence, user.getLocale(), false);
    }

    /**
     * 查询老数据
     *
     * @return
     */
    private Map<Long, StCExpiryDateLabel> queryOldLabel() {
        List<StCExpiryDateLabel> oldLabelList = stCExpiryDateLabelMapper.selectList(null);
        Map<Long, StCExpiryDateLabel> oldLabelMap = oldLabelList.stream()
                .collect(Collectors.toMap(StCExpiryDateLabel::getItemId, Function.identity()));
        return oldLabelMap;
    }

    /**
     * 收集有效数据
     *
     * @param groupedByDateRange
     * @return
     */
    private List<Long> collectActiveItemId(Map<String, List<StCExpiryDateLabel>> groupedByDateRange) {
        return groupedByDateRange.values().stream()
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0).getItemId())
                .collect(Collectors.toList());
    }

    /**
     * 按照新生产日期倒序、新结束生产日期倒序、指定类型正序后按照
     *
     * @param dateLabelList
     * @return
     */
    private Map<String, List<StCExpiryDateLabel>> sortAndGroup(List<StCExpiryDateLabel> dateLabelList) {
        return dateLabelList.stream()
                .sorted(Comparator.comparing(StCExpiryDateLabel::getNewStartDateDay).reversed()
                        .thenComparing(StCExpiryDateLabel::getNewEndDateDay).reversed()
                        .thenComparing(StCExpiryDateLabel::getAppointType))
                .collect(Collectors.groupingBy(
                        label -> label.getNewStartDateDay() + label.getNewEndDateDay(),
                        Collectors.toList()
                ));
    }

    /**
     * 根据指定类型转换日期格式
     *
     * @param dateLabelList
     */
    private void convertDateFormat(List<StCExpiryDateLabel> dateLabelList) {
        for (StCExpiryDateLabel expiryDateLabel : dateLabelList) {
            Integer appointType = expiryDateLabel.getAppointType();
            if (AppointTypeEnum.DATE_SCOPE.getKey().equals(appointType)) {
                expiryDateLabel.setNewStartDateDay(expiryDateLabel.getStartDateDay());
                expiryDateLabel.setNewEndDateDay(expiryDateLabel.getEndDateDay());
            }
            if (AppointTypeEnum.DAYS_SCOPE.getKey().equals(appointType)) {
                expiryDateLabel.setNewStartDateDay(computeDate(Integer.parseInt(expiryDateLabel.getEndDateDay())));
                expiryDateLabel.setNewEndDateDay(computeDate(Integer.parseInt(expiryDateLabel.getStartDateDay())));
            }
        }
    }

    /**
     * 根据指定天数转换成日期
     */
    private String computeDate(Integer day) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(new Date());
        ca.add(Calendar.DATE, -day);
        Date time = ca.getTime();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        return format.format(time);
    }

    /**
     * 发送异步计算的消息
     *
     * @param user
     */
    public void sendCalculateMsg(User user) {
        String topic = Mq5Constants.TOPIC_R3_CALCULATE_DATE_LABEL;
        String tag = Mq5Constants.TAG_R3_CALCULATE_DATE_LABEL;
        try {
            MqSendResult sendResult = defaultProducerSend.sendTopic(topic, tag, JSON.toJSONString(user), null);
            log.debug(LogUtil.format("发送异步计算的消息 报文:{}，messageId:{}，messageKey:{}",
                    "StCExpiryDateLabelService.sendCalculateMsg"),
                    JSON.toJSONString(user), sendResult.getMessageId(), sendResult.getMessageKey());
        } catch (Exception e) {
            log.error(LogUtil.format("StCExpiryDateLabelService.sendCalculateMsg error:{},messageBody:{}",
                    "StCExpiryDateLabelService.sendCalculateMsg"),
                    Throwables.getStackTraceAsString(e), JSON.toJSONString(user));
        }
    }

    /**
     * 按照新开始生产日期倒序，新结束生产日期倒序查询所有有效汇波策略
     *
     * @return
     */
    public List<StCExpiryDateLabel> queryAllByRule() {
        return stCExpiryDateLabelMapper.selectAllByRule();
    }

}
