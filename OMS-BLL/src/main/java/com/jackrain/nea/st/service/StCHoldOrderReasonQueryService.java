package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderReasonMapper;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.RedisKeyConst;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @author: 秦雄飞
 * @time: 2023/2/21 02:08
 * @description: HOLD原因查询
 */

@Component
@Slf4j
public class StCHoldOrderReasonQueryService {

    @Autowired
    private StCHoldOrderReasonMapper stCHoldOrderReasonMapper;

    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    /**
     * 根据id查询HOLD原因表
     *
     * @param id
     * @return StCHoldOrderReason
     */
    public StCHoldOrderReason selectHoldOrderById(Long id) {
        StCHoldOrderReason holdOrderReason;
        String redisKey = RedisKeyConst.HOLD_ORDER_REASON_KEY + id;
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("StCHoldOrderReasonSaveService.selectHoldOrderById.request:{}",
                        "selectHoldOrderById"), id);
            }
            if (redisOpsUtil.strRedisTemplate.hasKey(redisKey)) {
                String str = redisOpsUtil.strRedisTemplate.opsForValue().get(redisKey);
                return JSONObject.parseObject(str, StCHoldOrderReason.class);
            }
            holdOrderReason = stCHoldOrderReasonMapper.selectById(id);
            if (Objects.isNull(holdOrderReason)) {
                return null;
            }
            redisOpsUtil.strRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(holdOrderReason), 24, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error(LogUtil.format("StCHoldOrderReasonSaveService.selectHoldOrderById",
                    "selectHoldOrderById"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCHoldOrderReasonSaveService.selectHoldOrderById.result:{}",
                    "selectHoldOrderById"), JSONObject.toJSONString(holdOrderReason));
        }
        return holdOrderReason;
    }

    public List<StCHoldOrderReason> selectAllHoldOrder() {
        return stCHoldOrderReasonMapper.selectAllHoldOrder();
    }
}
