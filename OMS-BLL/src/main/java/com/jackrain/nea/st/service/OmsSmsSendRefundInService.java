package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jackrain.nea.oc.oms.mapper.OcBMsgSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.model.SmsSendStrategyInfo;
import com.jackrain.nea.oc.oms.model.enums.OmsSendMsgNoticeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsSendMsgTaskNodeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBMsgSendRecord;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StcMsgDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * <AUTHOR> ruan.gz
 * @Description : 退货入库
 * @Date : 2020/8/31
 **/
@Service("smsSendRefundIn")
@Slf4j
public class OmsSmsSendRefundInService extends AbstractOmsSmsSendService {

    /**
     * 2:
     */
    public static final String TASKNODE01 = OmsSendMsgTaskNodeEnum.REFUND_SWAP_IN_STORAGE.getVal().toString();
    public static final String TASKNODE02 = OmsSendMsgTaskNodeEnum.NO_NAME_IN_STORAGE.getVal().toString();
    /**
     * 1:
     */
    private static final String ADVICETYPE = OmsSendMsgNoticeEnum.RETURN_SWAP.getVal().toString();
    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private OcBMsgSendMapper ocBMsgSendMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBRefundInProductItemMapper ocBRefundInProductItemMapper;

    @Autowired
    private SgRpcService sgRpcService;

    @Override

    public String prepGetContent(SmsSendStrategyInfo smsSendStrategyInfo) {
        smsSendStrategyInfo.setAdviceType(ADVICETYPE);
        Long cpCShopId = null;
        if (smsSendStrategyInfo.getTaskNode().equals(TASKNODE01)) {
            OcBReturnOrder ocBReturnOrder = smsSendStrategyInfo.getOcBReturnOrder();
            cpCShopId = ocBReturnOrder.getCpCShopId();
            smsSendStrategyInfo.setReceiverMobile(ocBReturnOrder.getReceiveMobile());
        } else {
            OcBRefundIn ocBRefundIn = smsSendStrategyInfo.getOcBRefundIn();
            if (Objects.isNull(ocBRefundIn) || StringUtils.isEmpty(ocBRefundIn.getReceiverMobile())) {
                return null;
            }
            smsSendStrategyInfo.setReceiverMobile(ocBRefundIn.getReceiverMobile());
        }
        try {
            log.debug(LogUtil.format("查询退短信策略","查询退短信策略", cpCShopId), smsSendStrategyInfo.getTaskNode());
            ValueHolderV14<StcMsgDO> stringValueHolderV14 = stRpcService.queryMsgInfo(cpCShopId, smsSendStrategyInfo.getTaskNode(), ADVICETYPE);
            log.debug(LogUtil.format("查询退短信策略返回{}","查询退短信策略返回", cpCShopId), JSON.toJSONString(stringValueHolderV14));
            if (stringValueHolderV14.isOK()) {
                if (Objects.isNull(stringValueHolderV14.getData())) {
                    return null;
                }
                StcMsgDO data = stringValueHolderV14.getData();
                Long id = data.getId();
                String templateContent = data.getTemplateContent();
//                    设置短信策略ID
                smsSendStrategyInfo.setMsgId(id);
//                    返回短信模板
                templateContent = templateContent.replaceAll("null", "").
                        replaceAll("\\$支付时间\\$", "").
                        replaceAll("\\$出库日期\\$", "");
                return templateContent;
            } else {
                log.error(LogUtil.format("退货入库,获取短信策略异常","退货入库,获取短信策略异常", cpCShopId), stringValueHolderV14.getMessage());
            }

        } catch (Exception e) {
            log.error(LogUtil.format("退货入库,获取短信策略异常","退货入库,获取短信策略异常", cpCShopId), Throwables.getStackTraceAsString(e));

        }
        return null;
    }

    @Override
    public void saveRecord(SmsSendStrategyInfo smsSendStrategyInfo) {
        OcBMsgSendRecord ocBMsgSendRecord = new OcBMsgSendRecord();
        ocBMsgSendRecord.setReceiverMobile(smsSendStrategyInfo.getReceiverMobile());
        ocBMsgSendRecord.setContent(smsSendStrategyInfo.getSmsContent());
        ocBMsgSendRecord.setStCMsgId(smsSendStrategyInfo.getMsgId());
        ocBMsgSendRecord.setCount(0L);
        makeCreateField(ocBMsgSendRecord, SystemUserResource.getRootUser());

        //短信记录插入
        ocBMsgSendMapper.insert(ocBMsgSendRecord);
    }

    @Override
    public void prepHandler(SmsSendStrategyInfo smsSendStrategyInfo) {
        log.debug(LogUtil.format("预处理订单入库内容","预处理订单入库内容"), JSON.toJSONString(smsSendStrategyInfo));
        super.prepHandler(smsSendStrategyInfo);

        //业务特殊变量存放
        HashMap<String, String> map = Maps.newHashMap();
        if (smsSendStrategyInfo.getTaskNode().equals(TASKNODE01)) {
            OcBReturnOrder ocBReturnOrder = smsSendStrategyInfo.getOcBReturnOrder();
            //原发货单
            OcBOrder order = selectOriOcBorder(ocBReturnOrder.getOrigOrderId());
            if (Objects.nonNull(order)) {
                map.put("$平台单号$", ocBReturnOrder.getOrigSourceCode());
                map.put("$买家昵称$", order.getUserNick());
                //查询明细
                List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItems(order.getId());
                AtomicInteger qtyNum = new AtomicInteger(0);
                Set<String> set = Sets.newHashSet();
                if (!CollectionUtils.isEmpty(ocBOrderItems)) {
                    ocBOrderItems.forEach(k -> {
                        if (!StringUtils.isEmpty(k.getOoid())) {
                            qtyNum.addAndGet(k.getQty().intValue());
                            set.add(k.getOoid());
                        }
                    });
                }
                if (!CollectionUtils.isEmpty(set) && set.size() == 1) {
                    map.put("$原单件数$", qtyNum.toString());
                }
            }
            //查询入库通知单 2
            //SgBPhyInResult sgBPhyInResult = sgRpcService.selectPhyNotices(ocBReturnOrder.getId(), 2);
//            if (Objects.nonNull(sgBPhyInResult) && Objects.nonNull(sgBPhyInResult.getInTime())) {
//                SimpleDateFormat sformat = new SimpleDateFormat("yyyy-MM-dd");
//                map.put("$入库日期$", sformat.format(sgBPhyInResult.getInTime()));
//            }
        } else if (smsSendStrategyInfo.getTaskNode().equals(TASKNODE02)) {
            //无名件
            OcBRefundIn ocBRefundIn = smsSendStrategyInfo.getOcBRefundIn();
            if (Objects.isNull(ocBRefundIn) || StringUtils.isEmpty(ocBRefundIn.getReceiverMobile())) {
                return;
            }
            List<OcBRefundInProductItem> items = ocBRefundInProductItemMapper.selectForItem(ocBRefundIn.getId());
            if (!CollectionUtils.isEmpty(items)) {
                BigDecimal reduce = items.stream().map(OcBRefundInProductItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                map.put("$本单件数$", reduce.toString());
            }
            map.put("$收货人$", ocBRefundIn.getReceiverName());
            map.put("$买家昵称$", ocBRefundIn.getReceiverName());
            //查询调整单入库日期
//            SgPhyAdjustBillQueryResult sgPhyAdjustBillQueryResult = sgRpcService.getPhyAdjust(ocBRefundIn.getId(), 1);
//            if (Objects.nonNull(sgPhyAdjustBillQueryResult) && Objects.nonNull(sgPhyAdjustBillQueryResult.getAdjust().getCheckTime())) {
//                SimpleDateFormat sformat = new SimpleDateFormat("yyyy-MM-dd");
//                map.put("$入库日期$", sformat.format(sgPhyAdjustBillQueryResult.getAdjust().getCheckTime()));
//            }
        } else {
            return;
        }
        log.debug(LogUtil.format("扩展入库短信信息","扩展入库短信信息"), JSON.toJSONString(map));

        putFieldMap(map);
    }

    /**
     * 原发货单
     *
     * @param origOrderId
     * @return
     */
    private OcBOrder selectOriOcBorder(Long origOrderId) {
        if (Objects.nonNull(origOrderId)) {
            return ocBOrderMapper.selectById(origOrderId);
        }
        return null;
    }
}
