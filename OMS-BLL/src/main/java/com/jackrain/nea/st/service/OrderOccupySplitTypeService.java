package com.jackrain.nea.st.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.relation.GetOrderForCancelModel;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNoSplit;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.OmsProAttributeInfo;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyDetailEntity;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyEntity;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName OrderOccupySplitTypeService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/2/27 14:15
 * @Version 1.0
 */
@Component
@Slf4j
public class OrderOccupySplitTypeService {
    private static final String[] PRIORITY = new String[]{"平台商品id", "SKU", "四级", "一级"};

    @Autowired
    private StCShortStockNoSplitStrategyMapper noSplitStrategyMapper;
    @Autowired
    private StCShortStockNoSplitStrategyDetailMapper noSplitStrategyDetailMapper;

    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderNoSplitMapper orderNoSplitMapper;
    @Autowired
    private BuildSequenceUtil buildSequenceUtil;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private PsRpcService psRpcService;

    /**
     * 根据店铺id、 订单id 寻找匹配的拆单类型
     *
     * @param shopId
     * @param orderId
     * @return
     */
    public String splitType(Long shopId, Long orderId) {
        String splitType = null;
        // 查询店铺策略
        StCShopStrategyDO stCShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(shopId);
        splitType = stCShopStrategyDO.getOccupyType();
        OcBOrderNoSplit ocBOrderNoSplit = orderNoSplitMapper.getByOrderId(orderId);
        if (ObjectUtil.isNotNull(ocBOrderNoSplit)) {
            // 判断时间是否还没超过当前时间
            Date noSplitTime = ocBOrderNoSplit.getCanSplitTime();
            if (noSplitTime.after(new Date())) {
                return "3";
            } else {
                return splitType;
            }
        }

        StCShortStockNoSplitStrategyEntity noSplitStrategy = noSplitStrategyMapper.getStrategyByShopId(shopId);
        if (ObjectUtil.isNull(noSplitStrategy)) {
            // 查询公用的策略
            noSplitStrategy = noSplitStrategyMapper.getCommonStrategy();
        }
        if (ObjectUtil.isNotNull(noSplitStrategy)) {
            // 如果不拆策略不为空 则可以进行匹配了 匹配的到的话 就覆盖掉店铺策略上的拆单类型 没匹配到的话 就使用店铺策略上的拆单类型
            List<StCShortStockNoSplitStrategyDetailEntity> noSplitStrategyDetailList = noSplitStrategyDetailMapper.selectByStrategyId(noSplitStrategy.getId());
            if (CollectionUtil.isNotEmpty(noSplitStrategyDetailList)) {
                // 进行匹配 先对策略进行分组 按照顺序 先执行 平台商品ID>SKU>四级 >一级，若同订单多个SKU，统一取当前规则(如 SKU)时效最小时效
                // (1.平台商品id 2.SKU 3.四级 4.一级)
                Map<String, List<StCShortStockNoSplitStrategyDetailEntity>> noSplitStrategyDetailMap = noSplitStrategyDetailList.stream().collect(Collectors.groupingBy(StCShortStockNoSplitStrategyDetailEntity::getMatchRule));
                for (String i : PRIORITY) {
                    List<StCShortStockNoSplitStrategyDetailEntity> detailList = noSplitStrategyDetailMap.get(i);
                    if (CollectionUtil.isEmpty(detailList)) {
                        continue;
                    }
                    List<OcBOrderItem> ocBOrderItemList = orderItemMapper.selectOrderItemListAndReturn(orderId);
                    // 秒
                    String ageing = matchAgeing(i, detailList, ocBOrderItemList);
                    if (StringUtils.isNotEmpty(ageing)) {
                        splitType = "3";
                        OcBOrderNoSplit noSplit = new OcBOrderNoSplit();
                        Long id = buildSequenceUtil.buildOrderNoSplitSequenceId();
                        Date canSplitTime = DateUtil.offset(new Date(), DateField.SECOND, Integer.parseInt(ageing));
                        if (ObjectUtil.equal("-1", ageing)) {
                            // 配置的-1的话 代表永不拆单
                            canSplitTime = DateUtil.offset(new Date(), DateField.YEAR, 100);
                        }
                        noSplit.setId(id);
                        noSplit.setOrderId(orderId);
                        noSplit.setCanSplitTime(canSplitTime);
                        noSplit.setCreationdate(new Date());
                        orderNoSplitMapper.insert(noSplit);
                        GetOrderForCancelModel getOrderForCancelModel = ocBOrderMapper.getOrderForCancelById(orderId);
                        omsOrderLogService.addUserOrderLog(orderId, getOrderForCancelModel.getBillNo(), OrderLogTypeEnum.SHORT_STOCK_NO_SPLIT.getKey(), "订单寻源前命中了缺货不拆策略,策略编码:" + noSplitStrategy.getStrategyCode() + ",命中类型:" + i, null, null, SystemUserResource.getRootUser());
                        log.info(LogUtil.format("{}.orderd订单,匹配的类型,{},匹配的内容:{}", "缺货不拆匹配", orderId, orderId, i, JSONUtil.toJsonStr(detailList)));
                        break;
                    }
                }
            }
        }
        return splitType;
    }

    private String matchAgeing(String rule, List<StCShortStockNoSplitStrategyDetailEntity> detailList, List<OcBOrderItem> ocBOrderItemList) {
        List<String> ageingList = new ArrayList<>();
        Map<String, String> detailMap = detailList.stream().collect(Collectors.toMap(StCShortStockNoSplitStrategyDetailEntity::getMatchContent,
                StCShortStockNoSplitStrategyDetailEntity::getAgeing));
        List<String> skuCodeList = ocBOrderItemList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
        switch (rule) {
            case "平台商品id":
                // 匹配平台商品ID
                List<String> numIidList = ocBOrderItemList.stream().map(OcBOrderItem::getNumIid).collect(Collectors.toList());
                for (String numIid : numIidList) {
                    String ageing = detailMap.get(numIid);
                    if (StringUtils.isNotEmpty(ageing)) {
                        ageingList.add(ageing);
                    }
                }
                break;
            case "SKU":
                // 匹配SKU
                List<String> skuList = ocBOrderItemList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
                for (String sku : skuList) {
                    String ageing = detailMap.get(sku);
                    if (StringUtils.isNotEmpty(ageing)) {
                        ageingList.add(ageing);
                    }
                }
                break;
            case "四级":
                // 匹配四级
                for (String skuCode : skuCodeList) {
                    ProductSku skuInfo = psRpcService.selectProductSku(skuCode);
                    if (skuInfo != null) {
                        Map<String, OmsProAttributeInfo> proAttributeMap = skuInfo.getProAttributeMap();
                        if (proAttributeMap != null && !proAttributeMap.isEmpty()) {
                            OmsProAttributeInfo info = proAttributeMap.get("M_DIM6_ID");
                            if (info != null) {
                                String content = info.getEname();
                                String ageing = detailMap.get(content);
                                if (StringUtils.isNotEmpty(ageing)) {
                                    ageingList.add(ageing);
                                }
                            }
                        }
                    }
                }
                break;
            case "一级":
                // 匹配一级
                for (String skuCode : skuCodeList) {
                    ProductSku skuInfo = psRpcService.selectProductSku(skuCode);
                    if (skuInfo != null) {
                        Map<String, OmsProAttributeInfo> proAttributeMap = skuInfo.getProAttributeMap();
                        if (proAttributeMap != null && !proAttributeMap.isEmpty()) {
                            OmsProAttributeInfo info = proAttributeMap.get("M_DIM4_ID");
                            if (info != null) {
                                String content = info.getEname();
                                String ageing = detailMap.get(content);
                                if (StringUtils.isNotEmpty(ageing)) {
                                    ageingList.add(ageing);
                                }
                            }
                        }
                    }
                }
                break;
            default:
        }

        if (CollectionUtil.isEmpty(ageingList)) {
            return null;
        }
        Collections.sort(ageingList);
        return ageingList.get(0);
    }
}
