package com.jackrain.nea.st.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.config.BusinessSystem;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateMapper;
import com.jackrain.nea.oc.oms.model.enums.ExpiryDateTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDate;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateItem;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/6/15 上午11:23
 * @Version 1.0
 */
@Slf4j
@Component
public class StExpiryDateAuditService {
    @Autowired
    private StCExpiryDateMapper stCExpiryDateMapper;
    @Autowired
    private StCExpiryDateItemMapper stCExpiryDateItemMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;
    @Autowired
    private StCExpiryDateLabelService stCExpiryDateLabelService;
    @Resource
    private BusinessSystem businessSystem;

    @OmsOperationLog(operationType = "AUDIT", mainTableName = "ST_C_EXPIRY_DATE", itemsTableName = "ST_C_EXPIRY_DATE_ITEM")
    public ValueHolder expiryDateAuditService(QuerySession querySession) {
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.info(LogUtil.format("入参：{}", "商品效期策略审核"), JSON.toJSONString(param));
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        User user = querySession.getUser();
        JSONArray idsArray = param.getJSONArray("ids");
        List<Long> ids = JSON.parseArray(idsArray.toJSONString(), Long.class);
        HashMap<Long, Object> errMap = new HashMap();
        boolean sendMsgFlag = false;
        for (Long id : ids) {
            try {
                expiryDateAudit(id, user.getId(), user.getName());
                sendMsgFlag = true;
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        if (sendMsgFlag) {
            //发送计算汇波标签策略消息
            stCExpiryDateLabelService.sendCalculateMsg(user);
        }
        return StBeanUtils.getExcuteValueHolder(ids.size(), errMap);
    }


    private void expiryDateAudit(Long id, Integer userId, String userName) {
        StCExpiryDate expiryDate = stCExpiryDateMapper.selectById(id);
        if (expiryDate == null) {
            throw new NDSException("当前记录已不存在！");
        }
        if (expiryDate.getSubmitStatus() == null || expiryDate.getSubmitStatus() != 1) {
            throw new NDSException("当前策略的状态不是待审核，不允许审核！");
        }
        int i = stCExpiryDateItemMapper.selectStCExpiryDateItemByMainId(expiryDate.getId());
        if (i <= 0) {
            throw new NDSException("请维护策略明细！");
        }

        //判断重复
        checkRepeat(expiryDate);

        // 判断会员策略
        if (ObjectUtil.equal(4, expiryDate.getExpiryType())) {
            // 查询之前存在的所有 会员策略
            List<StCExpiryDate> memberMatchExpiryDateList = stCExpiryDateMapper.selectStCExpiryDateMemberMatch();
            for (StCExpiryDate stCExpiryDate : memberMatchExpiryDateList) {
                // 判断需要审核的时间 是否在已审核的时间区间范围内
                if (((stCExpiryDate.getStartTime().before(expiryDate.getStartTime())) && stCExpiryDate.getEndTime().after(expiryDate.getStartTime())) ||
                        ((stCExpiryDate.getStartTime().before(expiryDate.getEndTime())) && stCExpiryDate.getEndTime().after(expiryDate.getEndTime()))) {
                    throw new NDSException("策略生效时间交叉，不允许保存！");
                }
            }
        }


        StCExpiryDate stCExpiryDate = new StCExpiryDate();
        stCExpiryDate.setId(id);
        stCExpiryDate.setSubmitStatus(2);
        stCExpiryDate.setModifiername(userName);
        stCExpiryDate.setModifierid(Long.valueOf(userId));
        stCExpiryDate.setModifieddate(new Date());
        int i1 = stCExpiryDateMapper.updateById(stCExpiryDate);
        if (i1 <= 0) {
            throw new NDSException("审核失败！");
        }
        if (ObjectUtil.equal(4, expiryDate.getExpiryType())) {
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_MEMBER_MATCH);
        }
        Long shopId = expiryDate.getShopId();
        if (shopId == null) {
            Integer customerGrouping = expiryDate.getCustomerGrouping();
            if (customerGrouping == null) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_COMMON);
            } else {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_CUSTOMER_GROUP + customerGrouping);
            }
        } else {
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_SHOP_ID + shopId);
        }

    }

    /**
     * 校验效期是否重复
     * 类型（指定店铺）+店铺+付款开始时间+付款结束时间+指定纬度+指定内容 唯一。
     * 类型（客户分组）+客户分组+付款开始时间+付款结束时间+指定纬度+指定内容 唯一。
     * 类型（公用）+付款开始时间+付款结束时间+指定纬度+指定内容 唯一。ExpiryDateTypeEnum
     *
     * @param expiryDate
     */
    private void checkRepeat(StCExpiryDate expiryDate) {
        //查询该门店下其他所有效期策略
        List<StCExpiryDate> existShopExpiryList = stCExpiryDateMapper.selectList(new QueryWrapper<StCExpiryDate>().lambda()
                .eq(StCExpiryDate::getShopId, expiryDate.getShopId())
                .eq(StCExpiryDate::getIsactive, YesNoEnum.Y.getKey())
                .eq(StCExpiryDate::getSubmitStatus, 2)
                .ne(StCExpiryDate::getId, expiryDate.getId())
        );
        Map<Long, StCExpiryDate> existShopExpiryMap = existShopExpiryList.stream().collect(Collectors.toMap(StCExpiryDate::getId, Function.identity(), (key1, key2) -> key2));
        List<StCExpiryDateItem> existShopExpiryItemList = null;
        if (CollectionUtils.isNotEmpty(existShopExpiryList)) {
            List<Long> existShopExpiryIds = existShopExpiryList.stream().map(StCExpiryDate::getId).collect(Collectors.toList());
            existShopExpiryItemList = stCExpiryDateItemMapper.selectList(new QueryWrapper<StCExpiryDateItem>().lambda()
                    .in(StCExpiryDateItem::getStCExpiryDateId, existShopExpiryIds)
                    .eq(StCExpiryDateItem::getIsactive, YesNoEnum.Y.getKey()));
        }
        Map<String, List<StCExpiryDateItem>> existShopExpiryItemMap = null;
        if (CollectionUtils.isNotEmpty(existShopExpiryItemList)) {
            existShopExpiryItemMap = existShopExpiryItemList.stream()
                    .collect(Collectors.groupingBy(p -> p.getAppointDimension() + "|" + p.getAppointContent()));
        }

        List<StCExpiryDateItem> expiryDateItems = stCExpiryDateItemMapper.selectList(new QueryWrapper<StCExpiryDateItem>().lambda().eq(StCExpiryDateItem::getStCExpiryDateId, expiryDate.getId()));
        for (StCExpiryDateItem expiryDateItem : expiryDateItems) {
            if (MapUtils.isNotEmpty(existShopExpiryItemMap)
                    && (ExpiryDateTypeEnum.APPOINT_SHOP.getKey().equals(expiryDate.getExpiryType())
                    || ExpiryDateTypeEnum.CUSTOMER_GROUPING.getKey().equals(expiryDate.getExpiryType()))) {
                //查询该指定内容
                for (StCExpiryDateItem existShopExpiryItem : existShopExpiryItemList) {
                    if (existShopExpiryItem.getAppointDimension().equals(expiryDateItem.getAppointDimension())
                            && existShopExpiryItem.getAppointContent().equals(expiryDateItem.getAppointContent())) {
                        List<StCExpiryDateItem> stCExpiryDateItems = existShopExpiryItemMap.get(expiryDateItem.getAppointDimension() + "|" + expiryDateItem.getAppointContent());
                        if (CollectionUtils.isNotEmpty(stCExpiryDateItems)) {
                            for (StCExpiryDateItem stCExpiryDateItem : stCExpiryDateItems) {
                                Long stCExpiryDateId = stCExpiryDateItem.getStCExpiryDateId();
                                StCExpiryDate stCExpiryDate = existShopExpiryMap.get(stCExpiryDateId);
                                Date startDate = expiryDate.getStartTime();
                                Date endDate = expiryDate.getEndTime();

                                Date sameStartDate = stCExpiryDate.getStartTime();
                                Date sameEndDate = stCExpiryDate.getEndTime();
                                if (startDate.getTime() < sameEndDate.getTime()
                                        && endDate.getTime() >= sameStartDate.getTime()) {
                                    throw new NDSException("商品效期主子明细与ID:" + stCExpiryDateItem.getStCExpiryDateId() + "交叉重复,不允许审核");
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 反审核
     *
     * @param session
     * @return
     */
    public ValueHolder expiryDateUnAuditService(QuerySession session) {
        //1.获取传入参数
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        User user = session.getUser();
        JSONArray idsArray = param.getJSONArray("ids");
        List<Long> ids = JSON.parseArray(idsArray.toJSONString(), Long.class);
        HashMap<Long, Object> errMap = new HashMap();
        for (Long id : ids) {
            try {
                expiryDateUnAudit(id, user, true);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        return StBeanUtils.getExcuteValueHolder(ids.size(), errMap);
    }

    public void expiryDateUnAudit(Long id, User user, boolean isClearRedis) {
        log.info(LogUtil.format("StExpiryDateAuditService.expiryDateUnAudit id:{},user:{}",
                "StExpiryDateAuditService.expiryDateUnAudit"), id, JSONObject.toJSONString(user));
        StCExpiryDate expiryDate = stCExpiryDateMapper.selectById(id);
        if (expiryDate == null) {
            throw new NDSException("当前记录已不存在！");
        }
        if (expiryDate.getSubmitStatus() == null || expiryDate.getSubmitStatus() != 2) {
            throw new NDSException("当前策略的状态不是已审核，不允许反审核！");
        }
        if (!ExpiryDateTypeEnum.COMMUNAL.getKey().equals(expiryDate.getExpiryType())) {
            List<String> accessControl = businessSystem.getStCExpiryDateAccessControl();
            if (CollectionUtils.isEmpty(accessControl) || !accessControl.contains(user.getName())) {
                throw new NDSException("不支持反审核，若要反审核请联系IT");
            }
        }
        StCExpiryDate stCExpiryDate = new StCExpiryDate();
        stCExpiryDate.setId(id);
        stCExpiryDate.setSubmitStatus(1);
        stCExpiryDate.setModifiername(user.getName());
        stCExpiryDate.setModifierid(Long.valueOf(user.getId()));
        stCExpiryDate.setModifieddate(new Date());
        int i1 = stCExpiryDateMapper.updateById(stCExpiryDate);
        if (i1 <= 0) {
            throw new NDSException("反审核失败！");
        }
        if (ObjectUtil.equal(4, expiryDate.getExpiryType())) {
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_MEMBER_MATCH);
        }
        if (isClearRedis) {
            Long shopId = expiryDate.getShopId();
            if (shopId == null) {
                Integer customerGrouping = expiryDate.getCustomerGrouping();
                if (customerGrouping == null) {
                    redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_COMMON);
                } else {
                    redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_CUSTOMER_GROUP + customerGrouping);
                }
            } else {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_SHOP_ID + shopId);
            }
        }
    }

    public String expiryDateAutoExamine(Long id, Integer userId, String userName) {
        try {
            expiryDateAudit(id, userId, userName);
        } catch (Exception e) {
            return e.getMessage();
        }
        return "";
    }
}
