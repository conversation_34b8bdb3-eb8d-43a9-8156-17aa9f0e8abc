package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategy;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategyItem;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.StEqualExchangeRelation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/6/30 下午4:37
 * @Version 1.0
 */
@Slf4j
@Component
public class StEqualExchangeService {


    @Autowired
    private StCEquityBarterStrategyMapper stCEquityBarterStrategyMapper;
    @Autowired
    private StCEquityBarterStrategyItemMapper stCEquityBarterStrategyItemMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;


    public StEqualExchangeRelation selectEqualExchange(Long shopId) {
        //通过指定店铺查询有效的对等换货策略
        String redisKey = StRedisKey.ST_EQUAL_EXCHANGE_SHOP_ID + shopId;
        String equalExchangeStr = redisOpsUtil.strRedisTemplate.opsForValue().get(redisKey);
        StEqualExchangeRelation relation = null;
        if (StringUtils.isNotEmpty(equalExchangeStr)) {
            relation = JSON.parseObject(equalExchangeStr, StEqualExchangeRelation.class);
        } else {
            relation = this.selectEqualExchangeData(shopId);
            if (relation != null) {
                String str = JSONObject.toJSONString(relation);
                redisOpsUtil.strRedisTemplate.opsForValue().set(redisKey, str);
            }
        }
        return relation;
    }


    public StEqualExchangeRelation selectEqualExchangeCommon() {
        StEqualExchangeRelation relation;//判断是否有公用的对等换货策略
        String redisKeyCommon = StRedisKey.ST_EQUAL_EXCHANGE_COMMON;
        String equalExchangeCommon = redisOpsUtil.strRedisTemplate.opsForValue().get(redisKeyCommon);
        if (StringUtils.isNotEmpty(equalExchangeCommon)) {
            relation = JSON.parseObject(equalExchangeCommon, StEqualExchangeRelation.class);
        } else {
            relation = this.selectEqualExchangeDataCommon();
            if (relation != null) {
                String str = JSONObject.toJSONString(relation);
                redisOpsUtil.strRedisTemplate.opsForValue().set(redisKeyCommon, str);
            }
        }
        return relation;
    }


    /**
     * 查询公用的
     * @return
     */
    private StEqualExchangeRelation selectEqualExchangeDataCommon(){
        List<StCEquityBarterStrategy> stCEquityBarterStrategies = stCEquityBarterStrategyMapper.selectStCEquityBarterStrategyCommon();
        return getStEqualExchangeRelation(stCEquityBarterStrategies);

    }

    private StEqualExchangeRelation getStEqualExchangeRelation(List<StCEquityBarterStrategy> stCEquityBarterStrategies) {
        if (CollectionUtils.isEmpty(stCEquityBarterStrategies)){
            return null;
        }
        StCEquityBarterStrategy stCEquityBarterStrategy = stCEquityBarterStrategies.get(0);
        Long id = stCEquityBarterStrategy.getId();
        List<StCEquityBarterStrategyItem> stCEquityBarterStrategyItems = stCEquityBarterStrategyItemMapper.selectEquityBarterStrategyItemByMainId(id);
        if (CollectionUtils.isEmpty(stCEquityBarterStrategyItems)){
            return null;
        }
        StEqualExchangeRelation relation = new StEqualExchangeRelation();
        relation.setStCEquityBarterStrategy(stCEquityBarterStrategy);
        relation.setStCEquityBarterStrategyItems(stCEquityBarterStrategyItems);
        return relation;
    }


    private StEqualExchangeRelation selectEqualExchangeData(Long shopId){
        List<StCEquityBarterStrategy> stCEquityBarterStrategies = stCEquityBarterStrategyMapper.selectStCEquityBarterStrategyByShopId(shopId);
        return getStEqualExchangeRelation(stCEquityBarterStrategies);
    }
}
