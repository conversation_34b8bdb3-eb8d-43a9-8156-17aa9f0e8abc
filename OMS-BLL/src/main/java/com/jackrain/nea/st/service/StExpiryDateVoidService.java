package com.jackrain.nea.st.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCExpiryDateMapper;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/6/15 下午1:41
 * @Version 1.0
 */
@Slf4j
@Component
public class StExpiryDateVoidService {
    @Autowired
    private StCExpiryDateMapper stCExpiryDateMapper;
    @Autowired
    private StCExpiryDateItemMapper stCExpiryDateItemMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    @OmsOperationLog(operationType = "VOID",mainTableName = "ST_C_EXPIRY_DATE",itemsTableName = "ST_C_EXPIRY_DATE_ITEM")
    public ValueHolder expiryDateVoidService(QuerySession querySession){
        //1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        User user = querySession.getUser();
        //生成Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        List<Long> ids = JSON.parseArray(auditArray.toJSONString(), Long.class);
        HashMap<Long, Object> errMap = new HashMap<>();
        for (Long id : ids) {
            try {
                expiryDateAudit(id, user);
            } catch (Exception e){
                errMap.put(id, e.getMessage());
            }
        }
        return StBeanUtils.getExcuteValueHolder(ids.size(), errMap);
    }


    private void expiryDateAudit(Long id, User user) {
        StCExpiryDate expiryDate = stCExpiryDateMapper.selectById(id);
        if (expiryDate == null) {
            throw new NDSException("当前记录已不存在！");
        }
        if (expiryDate.getSubmitStatus() == null || expiryDate.getSubmitStatus() != 1) {
            throw new NDSException("当前策略的状态不是未审核，不允许作废！");
        }
        StCExpiryDate stCExpiryDate = new StCExpiryDate();
        stCExpiryDate.setId(id);
        stCExpiryDate.setSubmitStatus(3);
        stCExpiryDate.setIsactive("N");
        stCExpiryDate.setModifiername(user.getName());
        stCExpiryDate.setModifierid(Long.valueOf(user.getId()));
        stCExpiryDate.setModifieddate(new Date());
        Long shopId = expiryDate.getShopId();
        int i1 = stCExpiryDateMapper.updateById(stCExpiryDate);
        if (i1 <= 0) {
            throw new NDSException("作废失败！");
        }
        if (ObjectUtil.equal(4, expiryDate.getExpiryType())) {
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_MEMBER_MATCH);
        }
        if (shopId == null) {
            Integer customerGrouping = expiryDate.getCustomerGrouping();
            if (customerGrouping == null) {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_COMMON);
            } else {
                redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_CUSTOMER_GROUP + customerGrouping);
            }
        } else {
            redisOpsUtil.strRedisTemplate.delete(StRedisKey.ST_EXPIRY_DATA_SHOP_ID + shopId);
        }
    }

}
