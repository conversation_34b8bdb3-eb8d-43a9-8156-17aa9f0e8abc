package com.jackrain.nea.resource;

/**
 * 系统表名称定义
 *
 * @author: 易邵峰
 * @since: 2019-03-07
 * create at : 2019-03-07 18:11
 */
public class SystemTableNames {

    private SystemTableNames() {

    }

    public static final String OC_ORDER_TABLE_NAME = "OC_B_ORDER";

    public static final String OC_ORDER_EXT_TABLE_NAME = "OC_B_ORDER_EXT";

    public static final String OC_TOBECONFIRMED_TASK_NAME = "OC_B_TOBECONFIRMED_TASK";

    public static final String OC_AUDIT_TASK_NAME = "OC_B_AUDIT_TASK";

    public static final String OC_B_TO_WMS_TASK_NAME = "OC_B_TO_WMS_TASK";

    public static final String OC_B_JITX_DEALER_ORDER_TASK = "OC_B_JITX_DEALER_ORDER_TASK";

    public static final String OC_B_WAREHOUSE_SPLIT_TASK_NAME = "OC_B_WAREHOUSE_SPLIT_TASK";

    public static final String OC_B_ORDER_SPLIT_TASK_NAME = "oc_b_order_split_task";

    public static final String OC_B_ORDER_SKU_SPLIT_TASK_NAME = "oc_b_order_sku_split_task";

    public static final String OC_B_ORDER_JINGDONG_SPLIT_TASK_NAME = "OC_B_ORDER_JINGDONG_SPLIT_TASK";

    public static final String OC_ORDER_ITEM_TABLE_NAME = "OC_B_ORDER_ITEM";

    public static final String OC_B_ORDER_EQUAL_EXCHANGE_ITEM = "OC_B_ORDER_EQUAL_EXCHANGE_ITEM";

    public static final String OC_ORDER_NAIKA_TABLE_NAME = "OC_B_ORDER_NAIKA";

    public static final String OC_ORDER_PRE_ORDER_TABLE_NAME = "OC_B_PRE_ORDER";

    public static final String OC_ORDER_PRE_ORDER_ITEM_TABLE_NAME = "OC_B_PRE_ORDER_ITEM";

    public static final String OC_ORDER_NAIKA_UNFREEZE_TABLE_NAME = "oc_b_order_naika_unfreeze";

    public static final String OC_ORDER_PAYMENT_TABLE_NAME = "oc_b_order_payment";

    public static final String OC_ORDER_PROMOTION_TABLE_NAME = "oc_b_order_promotion";

    public static final String OC_ORDER_TAOBAO_TABLE_NAME = "oc_b_order_taobao";

    public static final String OC_ORDER_LINK_TABLE_NAME = "oc_b_order_link";

    public static final String OC_B_DEFICIENCY_TABLE_NAME = "oc_b_deficiency";

    public static final String OC_B_DEFICIENCY_ITEM_TABLE_NAME = "oc_b_deficiency_item";

    public static final String OC_B_DEFICIENCY_TRANSFER_TABLE_NAME = "oc_b_deficiency_transfer";

    public static final String OC_B_INVOICE_NOTICE_TABLE_NAME = "OC_B_INVOICE_NOTICE";

    public static final String OC_B_INVOICE_NOTICE_ITEM_TABLE_NAME = "OC_B_INVOICE_NOTICE_ITEM";

    public static final String OC_B_INVOICE_NOTICE_PRO_TABLE_NAME = "OC_B_INVOICE_NOTICE_PRO";

    public static final String OC_B_ORDER_SEND_LOG = "OC_B_ORDER_SEND_LOG";

    public static final String OC_B_JITX_MODIFY_WAREHOUSE_LOG = "oc_b_jitx_modify_warehouse_log";

    public static final String OC_B_TOWING_DELIVERY_TASK = "OC_B_TOWING_DELIVERY_TASK";

    public static final String IP_B_JITX_RESET_SHIP_WORKFLOW = "ip_b_jitx_reset_ship_workflow";

    public static final String IP_B_STANDPLAT_ORDER = "ip_b_standplat_order";

    public static final String IP_B_STANDPLAT_ORDER_ITEM = "ip_b_standplat_order_item";


    public static final String OC_B_ORDER_LOGISTICS_TRAJECTORY = "oc_b_order_logistics_trajectory";

    public static final String OC_B_AUTO_RELEASE_HANG_TASK_NAME = "OC_B_AUTO_RELEASE_HANG_TASK";

    public static final String CARD_CODE_VOID = "oc_b_order_naika_void";

    public static final String OC_B_ORDER_OUTSTOCK_RECORD = "oc_b_order_outstock_record";

    public static final String OC_B_ORDER_DELIVERY_FAIL = "oc_b_order_delivery_fail";

    public static final String COMMON_IDEMPOTENT = "common_idempotent";

    public static final String OC_B_ORDER_ADDSERVICE_REPORT = "oc_b_order_addservice_report";

    public static final String OC_B_ORDER_NO_SPLIT = "oc_b_order_no_split";

    public static final String ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY = "st_c_short_stock_no_split_strategy";
    public static final String ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY_DETAIL = "st_c_short_stock_no_split_strategy_detail";

    public static final String OC_ORDER_SOURCE_RELATION = "oc_b_order_source_relation";

    public static final String OC_ORDER_ITEM_EXT_TABLE_NAME = "oc_b_order_item_ext";

    public static final String OC_B_TO_B_ORDER = "oc_b_to_b_order";

    public static final String ST_C_CYCLE_STRATEGY = "st_c_cycle_strategy";

    public static final String ST_C_CYCLE_RULE_STRATEGY = "st_c_cycle_rule_strategy";

    public static final String ST_C_CYCLE_ITEM_STRATEGY = "st_c_cycle_item_strategy";

    public static final String OC_B_ORDER_BN_TASK = "oc_b_order_bn_task";

    public static final String ST_C_IMPERFECT_STRATEGY = "st_c_imperfect_strategy";

    public static final String ST_C_IMPERFECT_STRATEGY_ITEM = "st_c_imperfect_strategy_item";

    public static final String ST_C_REMARK_GIFT_STRATEGY = "st_c_remark_gift_strategy";

    public static final String ST_C_DROPSHIP_BASE_PRICE_STRATEGY = "st_c_dropship_base_price_strategy";

    public static final String ST_C_DROPSHIP_BASE_PRICE_STRATEGY_DETAIL = "st_c_dropship_base_price_strategy_detail";


    public static final String OC_B_SHOP_SKU_BATCH_INFO = "oc_b_shop_sku_batch_info";

}
