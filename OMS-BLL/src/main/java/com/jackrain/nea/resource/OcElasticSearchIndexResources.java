package com.jackrain.nea.resource;

import com.jackrain.nea.es.util.ElasticSearchUtil;

/**
 * @author: 易邵峰
 * @since: 2019-03-18
 * create at : 2019-03-18 11:16
 */
public class OcElasticSearchIndexResources {
    public static final String OC_B_ORDER_INDEX_NAME = "oc_b_order";

    public static final String OC_B_ORDER_TYPE_NAME = "oc_b_order";

    public static final String OC_B_PRE_ORDER_INDEX_NAME = "oc_b_pre_order";
    public static final String OC_B_PRE_ORDER_TYPE_NAME = "oc_b_pre_order";

    //********begin 夏继超
    public static final String OC_B_RETURN_ORDER_INDEX_NAME = "oc_b_return_order";

    public static final String OC_B_RETURN_ORDER_TYPE_NAME = "oc_b_return_order";

    public static final String OC_B_RETURN_BF_SEND_INDEX_NAME = "oc_b_return_bf_send";

    public static final String OC_B_RETURN_BF_SEND_TYPE_NAME = "oc_b_return_bf_send";

    public static final String OC_B_RETURN_AF_SEND_INDEX_NAME = "oc_b_return_af_send";

    public static final String OC_B_RETURN_AF_SEND_TYPE_NAME = "oc_b_return_af_send";

    public static final String OC_B_RETURN_AF_SEND_ITEM_INDEX_NAME = "oc_b_return_af_send_item";

    public static final String OC_B_RETURN_AF_SEND_ITEM_TYPE_NAME = "oc_b_return_af_send_item";
    //********end  夏继超
    public static final String OC_B_ORDER_ITEM_TYPE_NAME = "oc_b_order_item";

    public static final String OC_B_ORDER_PARENT_FIELD_NAME = "oc_b_order_id";

    public static final String OC_B_ORDER_PAYMENT_TYPE_NAME = "oc_b_order_payment";

    public static final String OC_B_ORDER_PAYMENT_INDEX_NAME = "oc_b_order_payment";

    public static final String OC_B_ORDER_PROMOTION_TYPE_NAME = "oc_b_order_promotion";

    public static final String OC_B_ORDER_PROMOTION_INDEX_NAME = "oc_b_order_promotion";

    public static final String OC_B_ORDER_TAOBAO_TYPE_NAME = "oc_b_order_taobao";

    public static final String OC_B_DEFICIENCY_TYPE_NAME = "oc_b_deficiency";

    public static final String OC_B_DEFICIENCY_INDEX_NAME = "oc_b_deficiency";

    public static final String OC_B_DEFICIENCY_TRANSFER_TYPE_NAME = "oc_b_deficiency_transfer";

    public static final String OC_B_DEFICIENCY_ITEM_TYPE_NAME = "oc_b_deficiency_item";

    public static final String MILK_CARD_AMOUNT_OFFSET_ORDER_TYPE_NAME = "milk_card_amount_offset_order";

    public static final String MILK_CARD_AMOUNT_OFFSET_ORDER_INDEX_NAME = "milk_card_amount_offset_order";


    public static final String IP_B_TAOBAO_ORDER_INDEX_NAME = "ip_b_taobao_order";

    public static final String IP_B_TAOBAO_ORDER_TYPE_NAME = "ip_b_taobao_order";

    public static final String IP_B_TAOBAO_REfUND_INDEX_NAME = "ip_b_taobao_refund";

    public static final String IP_B_TAOBAO_REfUND_TYPE_NAME = "ip_b_taobao_refund";

    public static final String IP_B_TAOBAO_ORDER_ITEM_TYPE_NAME = "ip_b_taobao_order_item";

    public static final String IP_B_TAOBAO_FX_ORDER_INDEX_NAME = "ip_b_taobao_fx_order";

    public static final String IP_B_TAOBAO_FX_ORDER_TYPE_NAME = "ip_b_taobao_fx_order";

    //********begin 孙俊磊 oc_b_refund_in_product_item
    public static final String OC_B_RETURN_IN_INDEX_NAME = "oc_b_refund_in";

    public static final String OC_B_RETURN_IN_TYPE_NAME = "oc_b_refund_in";

    public static final String OC_B_RETURN_IN_PRODUCT_ITEM_INDEX_NAME = "oc_b_refund_in_product_item";

    public static final String OC_B_RETURN_IN_PRODUCT_ITEM_TYPE_NAME = "oc_b_refund_in_product_item";

    public static final String OC_B_RETURN_BATCH_INDEX_NAME = "oc_b_refund_batch";

    public static final String OC_B_RETURN_BATCH_TYPE_NAME = "oc_b_refund_batch";

    public static final String IP_B_JINGDONG_ORDER_INDEX_NAME = "ip_b_jingdong_order";

    public static final String IP_B_JINGDONG_ORDER_TYPE_NAME = "ip_b_jingdong_order";

    public static final String IP_B_TAOBAO_FX_REFUND_INDEX_NAME = "ip_b_taobao_fx_refund";

    public static final String IP_B_TAOBAO_FX_REFUND_TYPE_NAME = "ip_b_taobao_fx_refund";

    //********end  孙俊磊

    //********begin 明方柱

    public static final String IP_B_STANDPLAT_ORDER_INDEX_NAME = "ip_b_standplat_order";
    public static final String IP_B_STANDPLAT_ORDER_TYPE_NAME = "ip_b_standplat_order";
    public static final String IP_B_TAOBAO_MODIFY_ADDR_INDEX_NAME = "ip_b_taobao_modify_addr";
    public static final String IP_B_TAOBAO_MODIFY_ADDR_TYPE_NAME = "ip_b_taobao_modify_addr";

    //********end 明方柱

    public static final String OC_B_ORDER_LOG = "oc_b_order_log";

    public static final String OC_B_ORDER_LINK = "oc_b_order_link";

    public static final String OC_B_ORDER_LOG_TYPE_NAME = "oc_b_order_log";

    public static final String OC_B_ORDER_LINK_TYPE_NAME = "oc_b_order_link";

    // ************** xiWen.z
    public static final String OC_B_RETURN_ORDER_REFUND_TYPE_NAME = "oc_b_return_order_refund";
    public static final String OC_B_RETURN_ORDER_EXCHANGE_TYPE_NAME = "oc_b_return_order_exchange";
    public static final String OC_B_RETURN_ORDER_LOG_INDEX_NAME = "oc_b_return_order_log";
    public static final String OC_B_RETURN_ORDER_LOG_TYPE_NAME = "oc_b_return_order_log";
    //**************JITX
    public static final String IP_B_JITX_ORDER_INDEX_NAME = "ip_b_jitx_order";

    public static final String IP_B_JITX_ORDER_TYPE_NAME = "ip_b_jitx_order";

    public static final String IP_B_JITX_ORDER_ITEM_TYPE_NAME = "ip_b_jitx_order_item";
    public static final String IP_B_JITX_DELIVERY_INDEX_NAME = "ip_b_jitx_delivery";

    public static final String IP_B_JITX_DELIVERY_TYPE_NAME = "ip_b_jitx_delivery";

    public static final String IP_B_JITX_DELIVERY_ITEM_TYPE_NAME = "ip_b_jitx_delivery_item";

    public static final String OC_B_JITX_MODIFY_WAREHOUSE_LOG = "oc_b_jitx_modify_warehouse_log";

    public static final String IP_B_JITX_RESET_SHIP_WORKFLOW = "ip_b_jitx_reset_ship_workflow";


    //*********淘宝经销
    public static final String IP_B_TAOBAO_JX_ORDER_TYPE_NAME = "ip_b_taobao_jx_order";
    public static final String IP_B_TAOBAO_JX_ORDER_ITEM_TYPE_NAME = "ip_b_taobao_jx_order_item";
    public static final String IP_B_TAOBAO_JX_ORDER_INDEX_NAME = "ip_b_taobao_jx_order";
    public static final String IP_B_TAOBAO_JX_ORDER_ITEM_INDEX_NAME = "ip_b_taobao_jx_order_item";
    //*********开票通知
    public static final String OC_B_INVOICE_NOTICE_INDEX_NAME = "oc_b_invoice_notice";

    public static final String OC_B_INVOICE_NOTICE_TYPE_NAME = "oc_b_invoice_notice";
    public static final String OC_B_INVOICE_NOTICE_ITEM_TYPE_NAME = "oc_b_invoice_notice_item";
    public static final String OC_B_INVOICE_NOTICE_LOG_TYPE_NAME = "oc_b_invoice_notice_log";
    public static final String OC_B_INVOICE_NOTICE_PRO_TYPE_NAME = "oc_b_invoice_notice_pro";
    //*********唯品会时效订单主表
    public static final String IP_TIME_ORDER_VIP_INDEX_NAME = "ip_b_time_order_vip";

    public static final String IP_B_TIME_ORDER_VIP_TYPE_NAME = "ip_b_time_order_vip";
    public static final String IP_B_TIME_ORDER_VIP_ITEM_TYPE_NAME = "ip_b_time_order_vip_item";

    public static final String IP_B_CANCEL_TIME_ORDER_VIP_INDEX_NAME = "ip_b_cancel_time_order_vip";

    public static final String IP_B_CANCEL_TIME_ORDER_VIP_TYPE_NAME = "ip_b_cancel_time_order_vip";
    public static final String IP_B_CANCEL_TIME_ORDER_VIP_ITEM_TYPE_NAME = "ip_b_cancel_time_order_vip_item";

    public static final String IP_B_ORDER_LOCK_TYPE_NAME = "ip_b_order_lock";
    public static final String IP_B_ORDER_LOCK_LOG_TYPE_NAME = "ip_b_order_lock_log";
    public final static String IP_B_WH_INNER_OPERATE = "ip_b_wh_inner_operate";
    public final static String OC_B_ORDER_DELIVERY = "oc_b_order_delivery";

    // ************唯品会退供单中间表 edit by lan 2020/06/18 *************
    public static final String IP_B_VIP_RETURN_ORDER_INDEX_NAME = "ip_b_vip_return_order";

    public static final String IP_B_VIP_RETURN_ORDER_TYPE_NAME = "ip_b_vip_return_order";

    public static final String IP_B_ALIBABA_ASCP_ORDER_INDEX_NAME = "ip_b_alibaba_ascp_order";

    public static final String IP_B_ALIBABA_ASCP_ORDER_TYPE_NAME = "ip_b_alibaba_ascp_order";

    public static final String IP_B_VIP_RETURN_ORDER_SYSTEM = "business_system:ip_b_vip_return_order";

    public static final String OC_B_YIKE_UPDATE_PRICE = "oc_b_yike_update_price";


    public static final String OC_B_JITX_DEALER_ORDER_TASK = "oc_b_jitx_dealer_order_task";

    public static final String IP_B_JD_DIRECT_CANCEL_INDEX = "ip_b_jingdong_direct_refund";

    public static String buildElasticIndexName(String indexName) {
        return ElasticSearchUtil.getIndex(indexName);
    }

}
