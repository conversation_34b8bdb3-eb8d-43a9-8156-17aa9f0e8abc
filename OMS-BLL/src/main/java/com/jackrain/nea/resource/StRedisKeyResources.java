package com.jackrain.nea.resource;

import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.util.ApplicationContextHandle;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/12/22
 */
public class StRedisKeyResources {

    /**
     * 根据店铺查找店铺价格方案
     *
     * @param shopId 店铺id
     * @return key
     */
    public static String buildShopPriceStrategyKey(Long shopId) {
        return RedisKeyConst.SHOP_PRICE_STRATEGY + shopId;
    }

    /**
     * 根据店铺查询店铺价格策略详细信息
     * @param shopId
     * @return
     */
    public static String buildShopPriceStrategyInfoKey(Long shopId) {
        return RedisKeyConst.SHOP_PRICE_STRATEGY_INFO + shopId;
    }


    /**
     * 根据店铺id查找库存同步策略: 查找店铺供货逻辑仓优先级
     *
     * @param shopId 店铺id
     * @return key
     */
    public static String buildSyncStockStrategyKey(Long shopId) {
        return RedisKeyConst.SHOP_SYNC_STOCK_STRATEGY + shopId;
    }


    /**
     * st.查询开启的订单推单延时策略
     *
     * @param shopId
     * @return
     */
    public static String buildShopOrderPushDelayKey(Long shopId) {
        return RedisKeyConst.SHOP_ORDER_PUSH_DELAY + shopId;
    }

    /**
     * st.查询开启的订单推单延时策略
     *
     * @param ecode
     * @return
     */
    public static String buildShopOrderCooperationNoKey(String ecode) {
        return RedisKeyConst.COOPERATION_NO + ecode;
    }

    /**
     * st. 查询仓库拆单策略
     *
     * @param phyWareHouseId
     * @return
     */
    public static String buildPhyWarehouseSplitOrderKey(Long phyWareHouseId) {
        return RedisKeyConst.PHY_WAREHOUSE_SPLIT_ORDER + phyWareHouseId;
    }

    /**
     * 根据店铺ID查询有效的Hold策略
     *
     * @param shopId
     * @return
     */
    public static String buildShopHoldOrderStKey(Long shopId) {
        return RedisKeyConst.SHOP_HOLD_ORDER_ST + shopId;
    }

    /**
     * 直播策略缓存key
     *
     * @param cpCShopId
     * @return
     */
    public static String buildLiveStrategyRedisKey(Long cpCShopId) {
        return RedisKeyConst.ST_LIVE_STRATEGY_KEY + cpCShopId;
    }


    public static int getCacheTimeConf() {
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        return config.getProperty("redis.rpc.cache.time", 5);

    }


    /**
     * 根据店铺查找店铺价格方案
     *
     * @param shopId 店铺id
     * @return key
     */
    public static String buildShopSendPlanAndRuleKey(Long shopId) {
        return RedisKeyConst.SHOP_SEND_PLAN_AND_RULE + shopId;
    }

    /**
     * 根据店铺ID查询有效的卡单策略
     *
     * @param shopId
     * @return
     */
    public static String buildShopDetentionPolicyStKey(Long shopId) {
        return RedisKeyConst.SHOP_DETENTION_ORDER_ST + shopId;
    }
    /**
     * 查询有效的定金预售预下沉策略
     *
     * @param shopId
     * @return
     */
    public static String buildShopDepositPreSaleStKey(Long shopId) {
        return RedisKeyConst.ST_DEPOSIT_PRE_SALE_SINK_KEY + shopId;
    }
    /**
     * 查询有效的订单打标策略
     *
     * @param shopId
     * @return
     */
    public static String buildShopOrderLabelStKey(Long shopId) {
        return RedisKeyConst.ST_C_ORDER_LABEL_KEY + shopId;
    }


    /**
     * 物流策略仓库key
     *
     * @param shopId
     * @return
     */
    public static String buildWarehouseIdStKey(Long warehouseId) {
        return RedisKeyConst.ST_C_ORDER_EXPRESS_ALLOCATION_KEY + warehouseId;
    }

    /**
     * 根据店铺获取贴纸策略缓存key
     *
     * @param shopId 店铺ID
     * @return 缓存key
     */
    public static String buildStickerCacheKey(Long shopId) {
        return RedisKeyConst.STICKER_CACHE_KEY_PREFIX + shopId;
    }
}
