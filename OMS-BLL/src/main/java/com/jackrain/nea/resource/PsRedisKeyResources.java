package com.jackrain.nea.resource;

/**
 * @Author: 黄世新
 * @Date: 2020/12/22 下午9:04
 * @Version 1.0
 * PS缓存的key
 */
public class PsRedisKeyResources {

    public static String getProductSkuKey(String sku) {
        return RedisKeyConst.PS_PRODUCT_SKU + sku;
    }

    public static String getProductSkuShopKey(String sku, Long shopId) {
        return RedisKeyConst.PS_PRODUCT_SKU + "shop:" + shopId + ":" + sku;
    }


    public static String getProductSkuIdKey(String skuId) {
        return RedisKeyConst.PS_PRODUCT_SKUID + skuId;
    }

    public static String getProductSkuForCodeKey(String code) {
        return RedisKeyConst.PS_PRODUCT_SKUFORCODE + code;
    }

    public static String getJdProductSkuShopKey(String wareId) {
        return RedisKeyConst.PS_JD_PRODUCT_SKU + wareId;
    }
}
