package com.jackrain.nea.resource;

import com.jackrain.nea.cpext.model.RegionType;

import static com.jackrain.nea.resource.RedisKeyConst.ARCHIVES_KEY;
import static com.jackrain.nea.resource.RedisKeyConst.CP_CPCWAREHOUSE_BYECODE_ECODE_JSON;
import static com.jackrain.nea.resource.RedisKeyConst.CP_CPREGION_BYID;
import static com.jackrain.nea.resource.RedisKeyConst.CP_CPSTORE_BYWAREHOUSEID;
import static com.jackrain.nea.resource.RedisKeyConst.CP_C_STORE_KEY;
import static com.jackrain.nea.resource.RedisKeyConst.CP_LOGISTICS;
import static com.jackrain.nea.resource.RedisKeyConst.CP_LOGISTICSLIST;
import static com.jackrain.nea.resource.RedisKeyConst.CP_LOGISTICS_BYID;
import static com.jackrain.nea.resource.RedisKeyConst.CP_LOGISTICS_OBJ;
import static com.jackrain.nea.resource.RedisKeyConst.CP_PHYSICAL_WAREHOUSE_ID;
import static com.jackrain.nea.resource.RedisKeyConst.CP_PLATFORM;
import static com.jackrain.nea.resource.RedisKeyConst.CP_SALESROOM_SESSIONKEY;
import static com.jackrain.nea.resource.RedisKeyConst.CP_SHOP;
import static com.jackrain.nea.resource.RedisKeyConst.CP_VIP_ADDRESS_CODE_KEY;

/**
 * Description:组织中心redis key的集中构建类
 *
 * <AUTHOR> sunies
 * @since : 2020-12-22
 * create at : 2020-12-22 20:46
 */
public class CpRedisKeyResources {


    public static String logisticNameKey(String logisticName) {
        return CP_LOGISTICS_OBJ + logisticName;
    }

    /**
     * 根据实体仓code查询实体仓信息
     *
     * @param ecode
     * @return
     */
    public static String buildWarehouseByEcode(String ecode) {
        return CP_CPCWAREHOUSE_BYECODE_ECODE_JSON + ecode;
    }


    public static String buildSalesroomKey(String code) {
        return CP_SALESROOM_SESSIONKEY + code;
    }


    /**
     * 物流公司档案redisKey 默认只是查询主表和明细无关
     *
     * @param logistcsId
     * @return
     */
    public static String buildLogisticsRedisKey(Long logistcsId) {
        return CP_LOGISTICS_BYID + logistcsId;
    }

    public static String getRegionKey(RegionType regionType, String regionName, long parentId) {
        return "cp:region:" + regionType.toKeyword() + ":" + parentId + ":" + regionName.hashCode();
    }


    public static String getShopKey(long shopId) {
        return CP_SHOP+ shopId;
    }

    public static String getLogisticsKey(String code) {
        return CP_LOGISTICS + code;
    }

    /**
     * 通过平台的code获取平台的明细信息
     *
     * @param ecode
     * @return
     */
    public static String buildCpPlatformKey(String ecode) {
        return CP_PLATFORM + ecode;
    }

    /**
     * 实体仓redisKey
     *
     * @param phyWarehouseId
     * @return
     */
    public static String buildCpPhyWarehouseRedisKey(Long phyWarehouseId) {
        return CP_PHYSICAL_WAREHOUSE_ID + phyWarehouseId;
    }

    /**
     * 根据省市区id查询省市区信息
     *
     * @param id
     * @return
     */
    public static String buildCpRegionById(long id) {
        return CP_CPREGION_BYID + id;
    }

    /**
     * 根据实体仓id查询逻辑仓集合
     *
     * @param warehouseId
     * @return
     */
    public static String buildCpStoreByWarehouseId(long warehouseId) {
        return CP_CPSTORE_BYWAREHOUSEID + warehouseId;
    }


    /**
     * 查询有效的物流公司名称redisKey
     *
     * @return
     */
    public static String buildLogicnameListKey() {
        return CP_LOGISTICSLIST;
    }

    /**
     * 根据逻辑仓ID，查询逻辑仓信息
     * @param storeId
     * @return
     */
    public static String buildCpCStoreKey(Long storeId) {
        return CP_C_STORE_KEY + storeId;
    }

    /**
     * 唯品会地址编码，redisKey
     * @param addressCode
     * @return
     */
    public static String buildVipAddressCodeKey(String addressCode) {
        return CP_VIP_ADDRESS_CODE_KEY + addressCode;
    }


    public static String buildArchivesByNameKey(String shopId) {
        return ARCHIVES_KEY+ shopId;
    }
}
