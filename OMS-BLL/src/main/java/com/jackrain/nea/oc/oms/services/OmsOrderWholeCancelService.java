package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: 黄世新
 * @Date: 2022/8/24 下午2:39
 * @Version 1.0
 * <p>
 * 订单整单取消
 */
@Slf4j
@Component
public class OmsOrderWholeCancelService {

    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsReturnUtil omsReturnUtil;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;


    public ValueHolderV14 orderWholeCancelService(Long orderId, User user, String logMeg) {
        ValueHolderV14 holder = new ValueHolderV14();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                cancelOrder(ocBOrder, user, logMeg);
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
            }
            holder.setCode(0);
            holder.setMessage("订单取消成功");
        } catch (Exception ex) {
            if (log.isDebugEnabled()) {
                log.error(LogUtil.format("订单取消异常,error：{}", "订单取消锁单错误"), Throwables.getStackTraceAsString(ex));
            }
            holder.setCode(-1);
            holder.setMessage("订单取消失败" + ex.getMessage());
        } finally {
            redisLock.unlock();
        }
        return holder;
    }


    private void cancelOrder(OcBOrder ocBOrder, User user, String logMeg) {
        if (ocBOrder == null) {
            return;
        }
        Integer orderStatus = ocBOrder.getOrderStatus();
        if (OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)
                || OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)) {
            return;
        }
        if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                || OmsOrderStatus.OCCUPY_IN.toInteger().equals(orderStatus)
                || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus)
                || OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)) {
            throw new NDSException("订单状态不满足,无法取消");
        }
        if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)
                || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
            //调用反审核
            boolean b = omsReturnUtil.toExamineOrder(ocBOrder, user);
            if (!b) {
                throw new NDSException("订单反审核失败,取消失败!");
            }
        }
        orderStatus = ocBOrder.getOrderStatus();
        //调用库存释放
        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)) {
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListOccupy(ocBOrder.getId());
            ValueHolderV14 sgValueHolder = sgRpcService.voidSgStockOccupy(ocBOrder, orderItems, user);
            if (sgValueHolder.getCode() != 0) {
                throw new NDSException("订单释放库存失败,取消失败!");
            }
        }
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setOrderStatus(OmsOrderStatus.CANCELLED.toInteger());
        ocBOrderMapper.updateById(order);
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_CANCLE.getKey(), logMeg + "订单取消", null, null, user);
    }
}
