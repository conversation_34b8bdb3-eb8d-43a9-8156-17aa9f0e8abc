package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.WosWorkOrderTypeEnum;
import com.jackrain.nea.oc.oms.model.result.OcBOrderListResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.request.OcBOrderLogPushRequest;
import com.jackrain.nea.oc.request.OcBOrderRequest;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.AcRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-08-14
 * @desc 零售发货单操作日志推送
 **/
@Slf4j
@Component
public class OrderLogPushService {
    @Autowired
    private QueryOrderService queryOrderService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private AcRpcService acRpcService;

    private static final String LOG_TABLE_NAMAE = "oc_b_order_log";

    /**
     * WOS系统推送操作日志
     *
     * @param ocBOrderLogPushRequest
     * @return
     */
    public ValueHolderV14 wosPushOperationLog(OcBOrderLogPushRequest ocBOrderLogPushRequest) {
        if (null == ocBOrderLogPushRequest
                || StringUtils.isEmpty(ocBOrderLogPushRequest.getBillNo())
                || StringUtils.isEmpty(ocBOrderLogPushRequest.getLogType())
                || StringUtils.isEmpty(ocBOrderLogPushRequest.getLogMessage())
        ) {
            return new ValueHolderV14(ResultCode.FAIL, "参数异常");
        }
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();

        OcBOrderRequest ocBOrderRequest = new OcBOrderRequest();
        ocBOrderRequest.setBillNo(ocBOrderLogPushRequest.getBillNo());
        OcBOrderListResult ocBOrderListResult = queryOrderService.getOrderList(ocBOrderRequest);
        if (CollectionUtils.isEmpty(ocBOrderListResult.getResultList())) {
            valueHolderV14.setMessage("无效单据编号");
            return valueHolderV14;
        }
        if (ocBOrderListResult.getResultList().size() > 1) {
            valueHolderV14.setMessage("根据单据编号查询的订单不唯一");
            return valueHolderV14;
        }
        OcBOrder ocBOrder = ocBOrderListResult.getResultList().get(0);
        OcBOrderResult ocBOrderResult = ocBOrderListResult.getResultList().get(0);
        //新增操作日志日志
        OcBOrderLog ocBOrderLog = new OcBOrderLog();
        BeanUtils.copyProperties(ocBOrderLogPushRequest, ocBOrderLog);
        ocBOrderLog.setOcBOrderId(ocBOrder.getId());
        int row = orderLogInsert(ocBOrderLog);
        if (row == 0) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("WOS系统推送信息失败");
            return valueHolderV14;
        }
        valueHolderV14.setCode(ResultCode.SUCCESS);

        String wosWorkOrderType = ocBOrderLogPushRequest.getWosWorkOrderType();
        //订单打标
        if (StringUtils.isNotEmpty(wosWorkOrderType)) {
            row = orderMarking(wosWorkOrderType, ocBOrderResult);
            if (row == 0) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("订单打标失败!");
            }
            //截回/拒收生成丢件单
            if (WosWorkOrderTypeEnum.INTERCEPTION_REJECTION.getCode().equals(wosWorkOrderType)) {
                ValueHolderV14 payVh = savePayableAdjust(ocBOrderResult);
                if (payVh != null && payVh.getCode() == ResultCode.FAIL) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    String message = valueHolderV14.getMessage() == null ? "" : valueHolderV14.getMessage();
                    valueHolderV14.setMessage(message.concat(payVh.getMessage() == null ? "" : payVh.getMessage()));
                }
            }
        }
        return valueHolderV14;
    }

    /**
     * @param ocBOrderResult
     * @return void
     * <AUTHOR>
     * @Description 截回/拒收生成丢件单
     * @Date 21:47 2020/8/18
     **/
    private ValueHolderV14 savePayableAdjust(OcBOrderResult ocBOrderResult) {
        //wos操作用户
        ValueHolderV14 vh = new ValueHolderV14();
        User operateUser = SystemUserResource.getRootUser();
        Long id = ocBOrderResult.getId();
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().lambda()
                .eq(OcBOrderItem::getOcBOrderId, id));
        if (CollectionUtils.isEmpty(orderItems)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("生成丢件单失败，订单明细信息为空");
            return vh;
        }
        JSONObject var = new JSONObject();
        var.put("ocBorderDto", ocBOrderResult);
        var.put("ocBorderItemDto", orderItems);
        var.put("sourceType", 1);
        return acRpcService.payableAdjustDropCopy(var, operateUser);
    }

    /**
     * 订单操作日志新增
     *
     * @param ocBOrderLog
     * @return
     */
    public int orderLogInsert(OcBOrderLog ocBOrderLog) {
        //wos操作用户
        User operateUser = SystemUserResource.getRootUser();
        ocBOrderLog.setId(ModelUtil.getSequence(LOG_TABLE_NAMAE));
        ocBOrderLog.setOwnerid(Long.valueOf(operateUser.getId()));
        ocBOrderLog.setOwnername("WOS");
        ocBOrderLog.setOwnerename("WOS");
        ocBOrderLog.setUserName("WOS");
        ocBOrderLog.setCreationdate(new Date());
        omsOrderLogService.save(Collections.singletonList(ocBOrderLog));
        return 1;
    }

    /**
     * 订单打标
     *
     * @param wosWorkOrderType
     * @param order
     * @return
     */
    public int orderMarking(String wosWorkOrderType, OcBOrder order) {
        int row = 0;
        OcBOrder ocBOrder = new OcBOrder();
        if (WosWorkOrderTypeEnum.MODIFY_INFORMATION.getCode().equals(wosWorkOrderType)) {
            //修改信息
            if (OcOrderTagEum.TAG_MODIFIED.getVal().equals(order.getIsModifiedOrder().toString())) {
                //已修改
                return 1;
            }
            ocBOrder.setIsModifiedOrder(1);
        } else if (WosWorkOrderTypeEnum.URGE.getCode().equals(wosWorkOrderType)) {
//            //催派
//            if (OcOrderTagEum.TAG_WOS_URGE_LACK.getVal().equals(order.getIsWosUrge().toString())) {
//                //已催派
//                return 1;
//            }
//            ocBOrder.setIsWosUrge(1);
//            order.setIsWosUrge(1);
        } else if (WosWorkOrderTypeEnum.INTERCEPTION_REJECTION.getCode().equals(wosWorkOrderType)) {
            /*//截回或拒收
            if (OcOrderTagEum.TAG_WOS_CUT_LACK.getVal().equals(order.getIsWosCut().toString())) {
                //已截回或已拒收
                return 1;
            }
            ocBOrder.setIsWosCut(1);
            order.setIsWosCut(1);*/
        } else {
            return row;
        }
        ocBOrder.setId(order.getId());
        return ocBOrderMapper.updateById(ocBOrder);
    }
}
