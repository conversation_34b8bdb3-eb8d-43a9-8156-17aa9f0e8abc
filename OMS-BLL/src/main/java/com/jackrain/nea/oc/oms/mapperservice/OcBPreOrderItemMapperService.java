package com.jackrain.nea.oc.oms.mapperservice;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.oc.oms.mapper.OcBPreOrderItemMapper;
import com.jackrain.nea.oc.oms.model.table.OcBPreOrderItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName OcBPreOrderItemMapperService
 * @Description 订单预导入明细
 * <AUTHOR>
 * @Date 2022/10/16 18:00
 * @Version 1.0
 */
@Service
@Slf4j
public class OcBPreOrderItemMapperService extends ServiceImpl<OcBPreOrderItemMapper, OcBPreOrderItem> {

    /**
     * 根据预导入id 获取预导入明细数据
     * @param preOrderId 预导入id
     * @return
     */
    public List<OcBPreOrderItem> getByPreOrderId(Long preOrderId) {
       return baseMapper.selectList(new QueryWrapper<OcBPreOrderItem>().lambda().eq(OcBPreOrderItem::getOcBPreOrderId, preOrderId));
    }
}
