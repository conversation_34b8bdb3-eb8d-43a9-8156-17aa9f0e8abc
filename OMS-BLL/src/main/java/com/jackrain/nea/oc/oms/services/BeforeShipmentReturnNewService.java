package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.service.IpOrderCancelToAgService;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 发货前退款服务
 */

@Slf4j
@Component
public class BeforeShipmentReturnNewService {

    @Autowired
    private IpTaobaoRefundService ipTaobaoRefundService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private MarkRefundService markRefundService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;

    @Autowired
    private IpOrderCancelToAgService ipOrderCancelToAgService;
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    public void beforeShipmentReturn(OcBOrder ocBOrder, IpBTaobaoRefund taobaoRefund,
                                     User operateUser, List<OcBOrderItem> ocBOrderItems) {
        Integer orderStatus = ocBOrder.getOrderStatus();
        //不是仓库发货、平台发货、交易完成、待分配、传WMS
        //2019-08-02 去掉校验待分配及待传wms
        if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                && !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                && !OmsOrderStatus.DEAL_DONE.toInteger().equals(orderStatus)) {
            String status = taobaoRefund.getStatus(); //淘宝退单中间表的退单状态
            String oid = taobaoRefund.getOid() + "";
            OcBOrderItem ocBOrderItem = null;
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                ocBOrderItem = new OcBOrderItem();
                for (OcBOrderItem bOrderItem : ocBOrderItems) {
                    String ooid = bOrderItem.getOoid();
                    Long ocBOrderId = bOrderItem.getOcBOrderId();
                    if (oid.equals(ooid) && ocBOrder.getId().equals(ocBOrderId)) {
                        ocBOrderItem = bOrderItem;
                        break;
                    }
                }
            }
            //更新主表状态(不管什么状态:只要是发货前立马拦截) 2019-08-04修改
            //是否退款中 0:N 1:Y
            OcBOrder order2 = new OcBOrder();
            order2.setIsInreturning(1); //是否退款中
            order2.setId(ocBOrder.getId());
            returnOrderTransferUtil.updateOperator(order2, operateUser);
            omsOrderService.updateOrderInfo(order2);

            order2.setIsInterecept(1);  //是否已经拦截 订单hold 或释放hold单调用HOLD单接口
            ocBOrderHoldService.holdOrUnHoldOrder(order2, OrderHoldReasonEnum.REFUND_HOLD);
            boolean flag = false;
            if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status)) {
                //买家已经申请退款，等待卖家同意
                flag = this.taobaoRefundStatusAgreeNew(ocBOrder, ocBOrderItem, taobaoRefund, operateUser);
            }
            if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
                //退款同意
                flag = this.refundStatusIsSuccessNew(ocBOrder, ocBOrderItem, taobaoRefund,
                        operateUser);
            }
            if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status)) {
                //退款关闭
                flag = this.orderStatusIsClosedNew(ocBOrder, ocBOrderItem, taobaoRefund, operateUser);
            }
            if (!flag) {
                String remark = SysNotesConstant.SYS_REMARK25;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);
            }
        }
    }


    /**
     * 为了适应组合商品 修改原逻辑
     *
     * @param ocBOrder
     * @param ocBOrderItem
     * @param taobaoRefund
     * @param operateUser
     * @return
     */


    private boolean taobaoRefundStatusAgreeNew(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem,
                                               IpBTaobaoRefund taobaoRefund, User operateUser) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("进入发货前退款退单状态为买家已经申请退款: {}", ocBOrder.getId()), ocBOrder.getId());
            }
            Integer orderStatus = ocBOrder.getOrderStatus();
            String refundId = taobaoRefund.getRefundId();
            //通过订单id及ooid  获取当前订单下的所有的商品明细
            //liqb 更改ooid类型从Long类型改成String类型
            String ooid = ocBOrderItem.getOoid();
            List<Long> orderList = new ArrayList();
            orderList.add(ocBOrder.getId());
            List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemListByOoid(ooid, orderList);
            for (OcBOrderItem orderItem : orderItems) {
                orderItem.setRefundStatus(OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal());
                omsOrderItemService.updateOcBOrderItem(orderItem, ocBOrder.getId());
                //插入订单日志
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "条码:" + orderItem.getPsCSkuEcode() + ",买家申请退款，订单挂起", null, null, operateUser);
            }
            if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)) {
                boolean flag = this.toExamineOrder(ocBOrder, operateUser);
                if (flag) {
                    orderStatus = ocBOrder.getOrderStatus();
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核成功", null, null, operateUser);
                } else {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核失败", null, null, operateUser);
                    String remark1 = SysNotesConstant.SYS_REMARK46;
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                            remark1, taobaoRefund);
                    return true;
                }
            }

            if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus)) {
                String remark1 = "订单状态为待分配或者待传wms,等待下次转换";
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        remark1, taobaoRefund);
                return true;
            }
            //判断当前订单店铺是否勾选传AG服务
            Long cpCShopId = ocBOrder.getCpCShopId(); //店铺id
            StCShopStrategyDO stCShopStrategy = omsStCShopStrategyService.selectOcStCShopStrategy(cpCShopId);
            if (stCShopStrategy != null) {
                String isAg = stCShopStrategy.getIsAg();
                if ("N".equals(isAg) || "0".equals(isAg)) {
                    String remark = SysNotesConstant.SYS_REMARK17;
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, taobaoRefund);
                    return true;
                }
            } else {
                String remark = SysNotesConstant.SYS_REMARK17;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);
                //没有对接策略平台 直接退出
                return true;
            }
            //若订单状态是：待审核、缺货、已审核状态时，直接调用【订单传AG取消发货服务】
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                boolean flag = false;
                for (OcBOrderItem orderItem : orderItems) {
                    Integer refundStatus = orderItem.getRefundStatus();
                    if (refundStatus != 1) {
                        continue;
                    }
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("IDS", orderItem.getId());
                    ValueHolderV14 holderV14 = markRefundService.markRefund(jsonObject, operateUser);
                    int code = Tools.getInt(holderV14.getCode(), -1);
                    if (code == 0) {
                        flag = true;

                    } else {
                        flag = false;
                        //WMS撤回失败
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单退款明细id为:" + orderItem.getId() + "条码为:" + orderItem.getPsCSkuEcode() + "标记退款完成失败", null, null, operateUser);
                        break;
                    }
                }
                //全部标记退款完成后  调用AG
                if (flag) {
                    boolean toAg = ipOrderCancelToAgService.orderCancelToAg(ocBOrder,
                            taobaoRefund, operateUser);
                    if (toAg) {
                        //传ag取消发货成功
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "退款单号为:" + refundId + ",订单AG取消发货成功", null, null, operateUser);
                        OcBOrder order = ocBOrderMapper.selectById(ocBOrder.getId());
                        Integer orderStatus1 = order.getOrderStatus();
                        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus1)
                                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus1)
                                || OmsOrderStatus.CHECKED.toInteger().equals(orderStatus1)) {
                            OcBOrder ocBOrder1 = new OcBOrder();
                            //如果是则恢复订单拦截状态，“是否已经拦截”更新为0
                            ocBOrder1.setIsInreturning(0); //是否退款中
                            ocBOrder1.setId(ocBOrder.getId());
                            returnOrderTransferUtil.updateOperator(ocBOrder1, operateUser);
                            omsOrderService.updateOrderInfo(ocBOrder1);
                            //是否已经拦截 订单hold 或释放hold单调用HOLD单接口
                            ocBOrder1.setIsInterecept(0); //订单hold 或释放hold单调用HOLD单接口
                            ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder1, OrderHoldReasonEnum.REFUND_HOLD);
                            String remark = SysNotesConstant.SYS_REMARK17;
                            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                    remark, taobaoRefund);
                        }
                    } else {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "退款单号为:" + refundId + ",订单AG取消发货失败", null, null, operateUser);
                    }
                }
                return true;
            }
            //全渠道订单主表“订单状态”为配货中且“WMS撤回状态”为未撤回或者撤回失败，则自动调用WMS撤回服务
            // 撤回成功，则调用【订单传AG取消发货服务】，撤回失败，则不调用
            if (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
                Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger() == wmsCancelStatus
                        || OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger() == wmsCancelStatus) {
                    // 则自动调用WMS撤回服务// 撤回成功，则调用【订单传AG取消发货服务】
                    this.markRefundIsFailNew(ocBOrder, operateUser, orderItems, taobaoRefund);
                }
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == wmsCancelStatus) {
                    // 撤回成功，则调用【订单传AG取消发货服务】
                    this.markRefundIsFailNew(ocBOrder, operateUser, orderItems, taobaoRefund);
                }
                return true;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("发货前退款退单状态为买家已经申请退款，等待卖家同意装换失败.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
        return false;
    }


    /**
     * 标记退款完成失败或者成功的逻辑
     *
     * @param ocBOrder    主订单
     * @param operateUser 操作人
     */
    public void markRefundIsFailNew(OcBOrder ocBOrder, User operateUser,
                                    List<OcBOrderItem> items, IpBTaobaoRefund taobaoRefund) {
        boolean flag = false;
        this.lockUpBackExamine(ocBOrder, operateUser);
        for (OcBOrderItem item : items) {
            Integer refundStatus = item.getRefundStatus();
            if (refundStatus != OcOrderRefundStatusEnum.SUCCESS.getVal()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("IDS", item.getId());
                //标记退款完成
                ValueHolderV14 holderV14 = markRefundService.markRefund(jsonObject, operateUser);
                int code1 = Tools.getInt(holderV14.getCode(), -1);
                if (code1 == 0) {
                    flag = true;
                } else {
                    flag = false;
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单退款明细id为:" + item.getId() + "条码为:" + item.getPsCSkuEcode() + "标记退款完成失败", null, null, operateUser);
                    break;
                }
            }
        }

        if (flag) {
            //调用AG取消发货
            boolean toAg = ipOrderCancelToAgService.orderCancelToAg(ocBOrder,
                    taobaoRefund, operateUser);
            if (toAg) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "退款单号为:" + taobaoRefund.getRefundId() + ",订单AG取消发货成功", null, null, operateUser);
                String remark = SysNotesConstant.SYS_REMARK17;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);
            } else {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "退款单号为:" + taobaoRefund.getRefundId() + ",订单AG取消发货失败", null, null, operateUser);
            }
        }
    }

    /**
     * 配货中加锁调用反审核
     *
     * @param ocBOrder
     * @param operateUser
     */
    private void lockUpBackExamine(OcBOrder ocBOrder, User operateUser) {
        // 对订单加锁 调用反审核
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                boolean isSuccess = this.toExamineOrder(ocBOrder, operateUser);
                if (!isSuccess) {
                    throw new NDSException("配货中反审核操作失败!");
                }
            } else {
                throw new NDSException("当前订单正在反审核操作!");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("配货中调用反审核出错.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("调用反审核失败!");
        } finally {
            redisLock.unlock();
        }
    }


    /**
     * 处理订单的方法
     *
     * @param ocBOrder
     * @param ocBOrderItems
     * @param taobaoRefund
     * @param operateUser
     */
    private void handleOrderNew(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems,
                                IpBTaobaoRefund taobaoRefund, User operateUser) {
        boolean isReturn = false;
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            Integer refundStatus = ocBOrderItem.getRefundStatus();
            if (OcOrderRefundStatusEnum.SUCCESS.getVal() != refundStatus) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("IDS", ocBOrderItem.getId());
                ValueHolderV14 execute = markRefundService.markRefund(jsonObject, operateUser);
                int code = Tools.getInt(execute.getCode(), -1);
                if (code == 0) {
                    isReturn = true;
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), ocBOrderItem.getPsCSkuEcode() + "条码退款完成, 明细id:" + ocBOrderItem.getId(), null, null, operateUser);
                } else {
                    isReturn = false;
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单退款明细id" + ocBOrderItem.getId() + "标记退款完成失败", null, null, operateUser);
                    break;
                }
            }
        }
        if (isReturn) {
            this.judgeOrder(ocBOrder, taobaoRefund, operateUser);
        }
    }

    /**
     * 对订单明细做相应的判断
     *
     * @param ocBOrder
     * @param taobaoRefund
     * @param operateUser
     */
    private void judgeOrder(OcBOrder ocBOrder, IpBTaobaoRefund taobaoRefund, User operateUser) {
        // 后期考虑赠品问题
        List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemList(ocBOrder.getId());
        //判断当前订单明细是否都为退款成功
        boolean flag = true;
        for (OcBOrderItem orderItem : orderItems) {
            if (OcOrderRefundStatusEnum.SUCCESS.getVal() != orderItem.getRefundStatus()) {
                flag = false;
                break;
            }
        }
        if (flag) {
            //当前退款明细状态都为success 调用订单取消服务(注释掉,标记退款完成已做判断)
            String remark = SysNotesConstant.SYS_REMARK19;
            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, taobaoRefund);
        } else {
            boolean isFlag = false;
            for (OcBOrderItem orderItem : orderItems) {
                if (OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal() ==
                        orderItem.getRefundStatus()) {
                    isFlag = true;
                    break;
                }
            }
            if (isFlag) {
                String remark = SysNotesConstant.SYS_REMARK20;
                ipTaobaoRefundService.updateReturnOrder(
                        TransferOrderStatus.TRANSFERRED.toInteger(), remark, taobaoRefund);
            } else {
                OcBOrder order = new OcBOrder();
                order.setId(ocBOrder.getId());
                this.updateOrder(order, operateUser);
                String remark = SysNotesConstant.SYS_REMARK20;
                ipTaobaoRefundService.updateReturnOrder(
                        TransferOrderStatus.TRANSFERRED.toInteger(), remark, taobaoRefund);
            }
        }
    }

    /**
     * 更新主编是否已经
     *
     * @param ocBOrder
     */

    private void updateOrder(OcBOrder ocBOrder, User operateUser) {
        ocBOrder.setIsInreturning(0);
        returnOrderTransferUtil.updateOperator(ocBOrder, operateUser);
        omsOrderService.updateOrderInfo(ocBOrder);
        // 订单hold 或释放hold单
        ocBOrder.setIsInterecept(0); //是否已经拦截 订单hold 或释放hold单调用HOLD单接口
        ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder, OrderHoldReasonEnum.REFUND_HOLD);
    }


    /**
     * 退款同意（SUCCESS(退款成功)）
     *
     * @param ocBOrder
     * @param ocBOrderItem
     * @param taobaoRefund
     */
    private boolean refundStatusIsSuccessNew(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem,
                                             IpBTaobaoRefund taobaoRefund,
                                             User operateUser) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("进入发货前退款退单状态为退款成功,订单id:{}", ocBOrder.getId()), ocBOrder.getId());
            }
            //通过订单id及ooid  获取当前订单下的所有的商品明细
            //liqb 更改ooid类型从Long类型改成String类型
            String ooid = ocBOrderItem.getOoid();
            List<Long> orderList = new ArrayList();
            orderList.add(ocBOrder.getId());
            List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemListByOoid(ooid, orderList);
            boolean isReturn = true;
            for (OcBOrderItem orderItem : orderItems) {
                Integer refundStatus = orderItem.getRefundStatus();
                if (OcOrderRefundStatusEnum.SUCCESS.getVal() != refundStatus) {
                    isReturn = false;
                }
            }
            //所有ooid相同的明细   全部退款完成
            if (isReturn) {
                //判断订单明细
                this.judgeOrder(ocBOrder, taobaoRefund, operateUser);
                String remark = SysNotesConstant.SYS_REMARK30;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED
                        .toInteger(), remark, taobaoRefund);
                return true;
            }

            Integer orderStatus = ocBOrder.getOrderStatus(); //订单状态
            Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
            if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)) {
                boolean flag = this.toExamineOrder(ocBOrder, operateUser);
                if (flag) {
                    orderStatus = ocBOrder.getOrderStatus();
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核成功", null, null, operateUser);
                } else {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核失败", null, null, operateUser);
                    String remark1 = SysNotesConstant.SYS_REMARK46;
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                            remark1, taobaoRefund);
                    return true;
                }
            }
            if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus)) {
                String remark1 = "订单状态为待分配或者待传wms,等待下次转换";
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        remark1, taobaoRefund);
                return true;
            }
            if (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger() == wmsCancelStatus
                        || OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger() == wmsCancelStatus) {
                    // 调用WMS撤回服务
                    //若服务返回失败，更新【淘宝退单中间表】数据：“转换状态”=0，”系统备注”：订单已转WMS且不可撤回，不允许转换
                    //配货中调用反审核
                    this.lockUpBackExamine(ocBOrder, operateUser);
                    this.handleOrderNew(ocBOrder, orderItems, taobaoRefund, operateUser);
                }
                //状态为配货中 wms状态为已撤回状态
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == wmsCancelStatus) {
                    //配货中调用反审核
                    this.lockUpBackExamine(ocBOrder, operateUser);
                    this.handleOrderNew(ocBOrder, orderItems, taobaoRefund, operateUser);
                }
                return true;
            }

            //b)若“订单状态”为待审核、缺货处理订单
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                this.handleOrderNew(ocBOrder, orderItems, taobaoRefund, operateUser);
                return true;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("发货前退款退单状态为SUCCESS时转换失败.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
        return false;
    }


    /**
     * 退单关闭
     *
     * @param ocBOrder
     * @param ocBOrderItem
     * @param taobaoRefund
     */
    public boolean orderStatusIsClosedNew(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem,
                                          IpBTaobaoRefund taobaoRefund, User operateUser) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("进入发货前退款退单状态为退单关闭,订单id:{}", ocBOrder.getId()), ocBOrder.getId());
            }
            //将当前订单明细改为0
            //通过订单id及ooid  获取当前订单下的所有的商品明细
            //liqb 更改ooid类型从Long类型改成String类型
            String ooid = ocBOrderItem.getOoid();
            List<Long> orderList = new ArrayList();
            orderList.add(ocBOrder.getId());
            List<OcBOrderItem> ocBOrderItemList = orderItemMapper.selectOrderItemListByOoid(ooid, orderList);
            Long id = ocBOrder.getId(); //订单主表id
            for (OcBOrderItem item : ocBOrderItemList) {
                OcBOrderItem orderItem = new OcBOrderItem();
                orderItem.setId(item.getId());
                orderItem.setOcBOrderId(item.getOcBOrderId());
                orderItem.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
                omsOrderItemService.updateOcBOrderItem(orderItem, id);
            }
            //获取所有明细(排除赠品)
            List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemListNotGift(ocBOrder.getId());
            boolean flag = false;
            for (OcBOrderItem item : orderItems) {
                Integer refundStatus = item.getRefundStatus(); //退款状态
                if (OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal() == refundStatus) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                String remark = SysNotesConstant.SYS_REMARK18;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);
            } else {
                //否，则更新全渠道订单“是否已经拦截”=0，则更新【淘宝退单中间表】数据：“转换状态”=2，
                // ”系统备注”：退款关闭，
                // 转换成功，调用订单日志服务
                OcBOrder bOrder = new OcBOrder();
                bOrder.setId(ocBOrder.getId());
                this.updateOrder(bOrder, operateUser);
                String remark = SysNotesConstant.SYS_REMARK18;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);
            }
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "订单退款关闭，取消挂起", null, null, operateUser);
            return true;
        } catch (Exception e) {
            log.error(LogUtil.format("发货前退款退单状态为退款关闭转换失败.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }


    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    private boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            if (isSuccess) {
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(LogUtil.format("调用反审核失败.异常: {}"), Throwables.getStackTraceAsString(e));
            return false;
        }

    }
}

