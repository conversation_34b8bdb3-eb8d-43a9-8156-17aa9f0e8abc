package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.AcFStoreKpiMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.query.QuerySession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * className: AcFStoreKpiCommonService
 * description:门店KPI设置删除服务
 *
 * <AUTHOR>
 * create: 2021-06-19
 * @since JDK 1.8
 */
@Component
public class AcFStoreKpiDeleteService extends CommandAdapter {

    @Autowired
    private AcFStoreKpiMapper kpiMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        ValueHolder valueHolder = CommandAdapterUtil.checkDeleteSession(querySession,OcCommonConstant.AC_F_STORE_KPI);
        if (!valueHolder.isOK()) {
            return valueHolder;
        }
        Long id = (Long)((HashMap)valueHolder.getData().get("data")).get(OcCommonConstant.OBJ_ID);

        //校验门店KPI是否被全渠道报表引用，如果被引用不允许删除
        if(kpiIsReferenced(id)){
            return ValueHolderUtils.getFailValueHolder("记录已被全渠道结算报表引用，不允许删除");
        }

        if (kpiMapper.deleteById(id) > 0){
            return ValueHolderUtils.getSuccessValueHolder("删除成功");
        }

        return ValueHolderUtils.getFailValueHolder("删除失败");
    }

    /**
     * 根据门店编码和月份去全渠道结算报表查询是否被引用
     * @param id 门店KPI  id
     * @return 是否被引用
     */
    private boolean kpiIsReferenced(Long id){
        return id < 0;
    }
}
