package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.service.IpOrderCancelToAgService;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 发货前退款服务
 */

@Slf4j
@Component
public class BeforeShipmentReturnService {

    @Autowired
    private IpTaobaoRefundService ipTaobaoRefundService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private MarkRefundService markRefundService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;
    @Autowired
    private SgRpcService sgRpcervice;
    @Autowired
    private IpOrderCancelToAgService ipOrderCancelToAgService;
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;


    public void beforeShipmentReturn(IpTaobaoRefundRelation ipTaobaoRefundRelation, User operateUser) {
        OcBOrder ocBOrder = ipTaobaoRefundRelation.getOcBOrder().get(0);
        IpBTaobaoRefund taobaoRefund = ipTaobaoRefundRelation.getTaobaoRefund();
        Integer orderStatus = ocBOrder.getOrderStatus();
        //不是仓库发货、平台发货、交易完成、待分配、传WMS
        if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                && !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                && !OmsOrderStatus.DEAL_DONE.toInteger().equals(orderStatus)
                && !OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                && !OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus)) {

            String status = taobaoRefund.getStatus();
            //liqb 更改ooid类型从Long类型改成String类型
            String oid = String.valueOf(taobaoRefund.getOid());
            OcBOrderItem ocBOrderItem = null;
            List<OcBOrderItem> ocBOrderItems = ipTaobaoRefundRelation.getOcBOrderItems();
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                List<OcBOrderItem> collect = ocBOrderItems.stream().filter(p -> p.getOoid().equals(oid)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    ocBOrderItem = collect.get(0);
                }
            }
            boolean flag = false;
            if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status)) {
                //买家已经申请退款，等待卖家同意
                BeforeShipmentReturnService bean = ApplicationContextHandle.getBean(BeforeShipmentReturnService.class);
                flag = bean.taobaoRefundStatusAgree(ocBOrder, ocBOrderItem, taobaoRefund, operateUser);
            }
            if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status) && ocBOrderItem != null) {
                //退款同意
                flag = this.refundStatusIsSuccess(ocBOrder, ocBOrderItem, taobaoRefund, operateUser);
            }
            if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status)) {
                //退款关闭
                flag = this.orderStatusIsClosed(ocBOrder, ocBOrderItem, taobaoRefund, operateUser);
            }
            if (!flag) {
                String remark = SysNotesConstant.SYS_REMARK25;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);

            }
        }
    }

    /**
     * 跟新订单主表及明细表的的数据   推ES
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean taobaoRefundStatusAgree(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem,
                                           IpBTaobaoRefund taobaoRefund, User operateUser) {
        try {
            //订单明细中的“退款状态”更新为wait_seller_agree（买家已经申请退款，等待卖家同意）（值为1）
            log.debug(LogUtil.format("进入发货前退款退单状态为买家已经申请退款,订单id:{}", ocBOrder.getId()), ocBOrder.getId());
            Integer orderStatus = ocBOrder.getOrderStatus();
            String refundId = taobaoRefund.getRefundId();
            if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)) {
                String remark = SysNotesConstant.SYS_REMARK31;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        remark, taobaoRefund);
                return true;
            }
            BigDecimal refundFee = taobaoRefund.getRefundFee();
            ocBOrderItem.setRefundStatus(OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal());
            omsOrderItemService.updateOcBOrderItem(ocBOrderItem, ocBOrder.getId());
            //更新主表状态
            //是否退款中 0:N 1:Y
            OcBOrder order2 = new OcBOrder();
            order2.setIsInreturning(1); //是否退款中
            order2.setId(ocBOrder.getId());
            returnOrderTransferUtil.updateOperator(order2, operateUser);
            omsOrderService.updateOrderInfo(order2);
            order2.setIsInterecept(1); //是否已经拦截 调用HOLD单方法
            ocBOrderHoldService.holdOrUnHoldOrder(order2, OrderHoldReasonEnum.REFUND_HOLD); // 退款拦截
            String remark = SysNotesConstant.SYS_REMARK17;
            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, taobaoRefund);
            //插入订单日志
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款，订单挂起", null, null, operateUser);
            //判断当前订单店铺是否勾选传AG服务
            Long cpCShopId = ocBOrder.getCpCShopId(); //店铺id
            StCShopStrategyDO stCShopStrategy = omsStCShopStrategyService.selectOcStCShopStrategy(cpCShopId);
            if (stCShopStrategy != null) {
                String isAg = stCShopStrategy.getIsAg();
                if ("N".equals(isAg) || "0".equals(isAg)) {
                    log.debug(this.getClass().getName() + " 买家已经申请退款，等待卖家同意,店铺未勾选AG同步,转换结束");
                    return true;
                }
            } else {
                //没有对接策略平台 直接退出
                return true;
            }
            //若订单状态是：待审核、缺货、已审核状态时，直接调用【订单传AG取消发货服务】
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                // 后期考虑赠品问题
                List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemList(ocBOrder.getId());
                orderItems.forEach(p -> {
                    Integer refundStatus = p.getRefundStatus();
                    if (refundStatus != 1) {
                        return;
                    }
                    //调用【标记退款完成服务】成功后，在调用【订单传AG取消发货服务】，
                    //调用标记退款完成服务
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("IDS", p.getId());
                    ValueHolderV14 holderV14 = markRefundService.markRefund(jsonObject, operateUser);
                    int code = Tools.getInt(holderV14.getCode(), -1);
                    log.debug(LogUtil.format("标记退款完成服务返回code:{}", code),code);
                    if (code == 0) {
                        p.setRefundId(refundId);
                        p.setAmtRefund(refundFee);
                        boolean toAg = ipOrderCancelToAgService.orderCancelToAg(ocBOrder,
                                taobaoRefund, operateUser);
                        if (toAg) {
                            //传ag取消发货成功
                            OcBOrder order = ocBOrderMapper.selectById(ocBOrder.getId());
                            Integer orderStatus1 = order.getOrderStatus();
                            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus1)
                                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus1)
                                    || OmsOrderStatus.CHECKED.toInteger().equals(orderStatus1)) {
                                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                        OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "订单AG取消发货成功", null, null, operateUser);
                            }
                        } else {
                            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                    OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "订单AG取消发货失败", null, null, operateUser);
                        }
                    } else {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单退款明细标记退款完成失败", null, null, operateUser);
                    }
                });
                return true;
            }
            //全渠道订单主表“订单状态”为配货中且“WMS撤回状态”为未撤回或者撤回失败，则自动调用WMS撤回服务
            // 撤回成功，则调用【订单传AG取消发货服务】，撤回失败，则不调用
            if (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
                Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger() == wmsCancelStatus
                        || OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger() == wmsCancelStatus) {
                    // 则自动调用WMS撤回服务// 撤回成功，则调用【订单传AG取消发货服务】
                    List<OcBOrder> ocBOrderList = new ArrayList<>();
                    ocBOrderList.add(ocBOrder);
                    ValueHolderV14 execute =
                            sgRpcervice.invoildOutgoingNotice(ocBOrderList, operateUser,true);
                    int code = Tools.getInt(execute.getCode(), -1);
                    if (code == 0) {
                        ocBOrderItem.setRefundId(refundId);
                        ocBOrderItem.setAmtRefund(refundFee);
//                        SgR3BaseResult data = (SgR3BaseResult) execute.getData();
//                        JSONArray dataArr = data.getDataArr();
                        Integer code1 =0; //dataArr.getJSONObject(0).getInteger("code");
                        if (code1 == 0) {
                            this.markRefundIsFail(ocBOrder, taobaoRefund, ocBOrderItem, operateUser);
                        }
                    }
                }
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == wmsCancelStatus) {
                    // 撤回成功，则调用【订单传AG取消发货服务】
                    this.markRefundIsFail(ocBOrder, taobaoRefund, ocBOrderItem, operateUser);
                }
                return true;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("发货前退款退单状态为买家已经申请退款，等待卖家同意装换失败.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);

        }
        return false;
    }

    /**
     * 标记退款完成失败或者成功的逻辑
     *
     * @param ocBOrder     主订单
     * @param ocBOrderItem 退款明细
     * @param operateUser  操作人
     */
    public void markRefundIsFail(OcBOrder ocBOrder, IpBTaobaoRefund taobaoRefund, OcBOrderItem ocBOrderItem, User operateUser) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("IDS", ocBOrderItem.getId());
        //标记退款完成
        ValueHolderV14 holderV14 = markRefundService.markRefund(jsonObject, operateUser);
        int code1 = Tools.getInt(holderV14.getCode(), -1);
        if (code1 == 0) {
            //调用AG取消发货
            boolean toAg = ipOrderCancelToAgService.orderCancelToAg(ocBOrder,
                    taobaoRefund, operateUser);
            if (toAg) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "订单AG取消发货成功", null, null, operateUser);
            } else {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "订单AG取消发货失败", null, null, operateUser);
            }
        } else {
            //WMS撤回失败
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单退款明细标记退款完成失败", null, null, operateUser);
        }
    }


    /**
     * 退单关闭
     *
     * @param ocBOrder
     * @param ocBOrderItem
     * @param taobaoRefund
     */
    public boolean orderStatusIsClosed(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem,
                                       IpBTaobaoRefund taobaoRefund, User operateUser) {
        try {
            log.debug(LogUtil.format("进入发货前退款退单状态为退单关闭:{}", ocBOrder.getId()),ocBOrder.getId());
            //将当前订单明细改为0
            OcBOrderItem orderItem = new OcBOrderItem();
            orderItem.setId(ocBOrderItem.getId());
            orderItem.setOcBOrderId(ocBOrderItem.getOcBOrderId());
            orderItem.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
            omsOrderItemService.updateOcBOrderItem(orderItem, ocBOrderItem.getOcBOrderId());
            //获取所有明细
            List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemList(ocBOrder.getId());
            boolean flag = false;
            for (OcBOrderItem item : orderItems) {
                Integer refundStatus = item.getRefundStatus(); //退款状态
                if (OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal() == refundStatus) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                String remark = SysNotesConstant.SYS_REMARK18;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);
            } else {
                //否，则更新全渠道订单“是否已经拦截”=0，则更新【淘宝退单中间表】数据：“转换状态”=2，
                // ”系统备注”：退款关闭，
                // 转换成功，调用订单日志服务
                OcBOrder bOrder = new OcBOrder();
                bOrder.setId(ocBOrder.getId());
                this.updateOrder(bOrder, operateUser);
                String remark = SysNotesConstant.SYS_REMARK18;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);
            }
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "订单退款关闭，取消挂起", null, null, operateUser);
            return true;
        } catch (Exception e) {
            log.error(LogUtil.format("发货前退款退单状态为退款关闭转换失败.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }

    /**
     * 退款同意（SUCCESS(退款成功)）
     *
     * @param ocBOrder
     * @param ocBOrderItem
     * @param taobaoRefund
     */
    private boolean refundStatusIsSuccess(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem,
                                          IpBTaobaoRefund taobaoRefund, User operateUser) {
        try {
            log.debug(LogUtil.format("进入发货前退款退单状态为退款成功,订单id:{}", ocBOrder.getId()),ocBOrder.getId());
            Integer refundStatus = ocBOrderItem.getRefundStatus();
            if (OcOrderRefundStatusEnum.SUCCESS.getVal() == refundStatus) {
                //判断订单明细
                this.judgeOrder(ocBOrder, taobaoRefund, operateUser);
                String remark = SysNotesConstant.SYS_REMARK30;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED
                        .toInteger(), remark, taobaoRefund);
                return true;
            }
            Integer orderStatus = ocBOrder.getOrderStatus(); //订单状态
            Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
            if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)) {
                String remark = SysNotesConstant.SYS_REMARK31;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        remark, taobaoRefund);
                return true;
            }
            if (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger() == wmsCancelStatus
                        || OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger() == wmsCancelStatus) {
                    // 调用WMS撤回服务
                    // 若服务返回失败，更新【淘宝退单中间表】数据：“转换状态”=0，”系统备注”：订单已转WMS且不可撤回，不允许转换
                    List<OcBOrder> ocBOrderList = new ArrayList<>();
                    ocBOrderList.add(ocBOrder);
                    ValueHolderV14 execute =
                            sgRpcervice.invoildOutgoingNotice(ocBOrderList, operateUser,true);
                    int code = Tools.getInt(execute.getCode(), -1);
                    if (code == 0) {
//                        SgR3BaseResult data = (SgR3BaseResult) execute.getData();
//                        JSONArray dataArr = data.getDataArr();
                        Integer code1 = 0;
                                //dataArr.getJSONObject(0).getInteger("code");
                        if (code1 != 0) {
                            String remark = SysNotesConstant.SYS_REMARK24;
                            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED
                                    .toInteger(), remark, taobaoRefund);
                            return true;
                        } else {
                            this.handleOrder(ocBOrder, ocBOrderItem, taobaoRefund, operateUser);
                        }
                    }
                }
                //状态为配货中 wms状态为已撤回状态
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == wmsCancelStatus) {
                    if (OcOrderRefundStatusEnum.SUCCESS.getVal() != refundStatus) {
                        this.handleOrder(ocBOrder, ocBOrderItem, taobaoRefund, operateUser);
                    } else {
                        String remark = SysNotesConstant.SYS_REMARK30;
                        ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED
                                .toInteger(), remark, taobaoRefund);
                    }
                }
                return true;
            }

            //b)若“订单状态”为待审核、缺货处理订单
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                if (OcOrderRefundStatusEnum.SUCCESS.getVal() != refundStatus) {
                    this.handleOrder(ocBOrder, ocBOrderItem, taobaoRefund, operateUser);
                } else {
                    String remark = SysNotesConstant.SYS_REMARK30;
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED
                            .toInteger(), remark, taobaoRefund);
                }

                return true;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("发货前退款退单状态为SUCCESS时转换失败.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * 处理订单的方法
     *
     * @param ocBOrder
     * @param ocBOrderItem
     * @param taobaoRefund
     * @param operateUser
     */
    private void handleOrder(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem,
                             IpBTaobaoRefund taobaoRefund, User operateUser) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("IDS", ocBOrderItem.getId());
        ValueHolderV14 execute = markRefundService.markRefund(jsonObject, operateUser);
        int code = Tools.getInt(execute.getCode(), -1);
        if (code != 0) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单退款明细标记退款完成失败", null, null, operateUser);
            return;
        }
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), ocBOrderItem.getPsCSkuEcode() + "条码退款完成, 明细id:" + ocBOrderItem.getId(), null, null, operateUser);
        this.judgeOrder(ocBOrder, taobaoRefund, operateUser);

    }

    /**
     * 对订单明细做相应的判断
     *
     * @param ocBOrder
     * @param taobaoRefund
     * @param operateUser
     */
    private void judgeOrder(OcBOrder ocBOrder, IpBTaobaoRefund taobaoRefund, User operateUser) {
        // 后期考虑赠品问题
        List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemList(ocBOrder.getId());

        //判断当前订单明细是否都为退款成功
        boolean flag = true;
        for (OcBOrderItem orderItem : orderItems) {
            if (OcOrderRefundStatusEnum.SUCCESS.getVal() != orderItem.getRefundStatus()) {
                flag = false;
                break;
            }
        }
        if (flag) {
            //当前退款明细状态都为success 调用订单取消服务(注释掉,标记退款完成已做判断)
            String remark = SysNotesConstant.SYS_REMARK19;
            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, taobaoRefund);
        } else {
            boolean isFlag = false;
            for (OcBOrderItem orderItem : orderItems) {
                if (OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal() ==
                        orderItem.getRefundStatus()) {
                    isFlag = true;
                    break;
                }
            }
            if (isFlag) {
                String remark = SysNotesConstant.SYS_REMARK20;
                ipTaobaoRefundService.updateReturnOrder(
                        TransferOrderStatus.TRANSFERRED.toInteger(), remark, taobaoRefund);
            } else {
                OcBOrder order = new OcBOrder();
                order.setId(ocBOrder.getId());
                this.updateOrder(order, operateUser);
                String remark = SysNotesConstant.SYS_REMARK20;
                ipTaobaoRefundService.updateReturnOrder(
                        TransferOrderStatus.TRANSFERRED.toInteger(), remark, taobaoRefund);
            }
        }
    }

    /**
     * 更新主编是否已经
     *
     * @param ocBOrder
     */

    private void updateOrder(OcBOrder ocBOrder, User operateUser) {
        ocBOrder.setIsInreturning(0);
        returnOrderTransferUtil.updateOperator(ocBOrder, operateUser);
        omsOrderService.updateOrderInfo(ocBOrder);

        ocBOrder.setIsInterecept(0); //是否已经拦截 调用HOLD单方法
        ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder, OrderHoldReasonEnum.REFUND_HOLD);
    }

}
