package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.StCMergeOrderInfo;
import com.jackrain.nea.oc.oms.model.enums.InterceptStatus;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.IsForbiddenDeliveryEnum;
import com.jackrain.nea.oc.oms.model.enums.MergeTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsMergeSplitOrderType;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderIsInterceptEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.AutoAuditCheckMergeOrderDto;
import com.jackrain.nea.oc.oms.model.relation.EncryptCodeGSI;
import com.jackrain.nea.oc.oms.model.relation.MergeOderGroups;
import com.jackrain.nea.oc.oms.model.relation.MergeParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.result.MergeOrderEsResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.audit.OmsOrderManualAuditService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.psext.api.SkuLikeQueryCmd;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OrderMergeUtil;
import com.jackrain.nea.util.RedisCacheUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Desc : 合单
 * <AUTHOR> xiWen
 * @Date : 2020/12/10
 */
@Slf4j
@Component
public class OcMergeOrderService {
    @Autowired
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private BasicPsQueryService basicPsQueryService;

    @Autowired
    private BllRedisLockOrderUtil bllRedisLockOrderUtil;

    @Autowired
    private OrderMergeService orderMergeService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OwnMergeOrderService ownMergeOrderService;

    @Autowired
    private PropertiesConf pconf;

//    @Autowired
//    private R3MqSendHelper r3MqSendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    private OmsOrderManualAuditService omsOrderManualAuditService;

    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;

    @Reference(group = "ps-ext", version = "1.0")
    private SkuLikeQueryCmd skuLikeQueryCmd;
    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;

    /**
     * 最小合并单位
     */
    private static final int MIN_UNIT = 2;

    /**
     * 合单redis key
     */
    private static final String redisMergeKey = "oms:mergeorder:encryptcode:";

    private static final String JITX_MERGE_LIMIT_QUANTITY = "business_system:jitx_merge_limit_quantity";

    /**
     * 手动合单
     *
     * @param encryptCode 合单加密串
     * @param orderIds    订单编号集
     * @param user        操作用户
     * @return 合并结果
     */
    public ValueHolderV14 manualMergeHandle(List<OcBOrder> dbList, List<Long> orderIds, User user) {
        OcBOrder order=dbList.get(0);
        String encryptCode=order.getOrderEncryptionCode();
        int limitQty = this.omsSystemConfig.getAutoMergeOrderEachGroupQty();
        ////唯品会JITX需重置限制数量 平台限制 20
        if (PlatFormEnum.VIP_JITX.getCode().equals(order.getPlatform())) {
            int jitxAutoMergeOrderEachGroupQty = businessSystemParamService.getJitxMergedOrderLimit();
            limitQty = jitxAutoMergeOrderEachGroupQty;
            //合单后再次合并 即把20单合并后单子再次合并需做限制
            List<Long> canNotMergedOrderId=new ArrayList<>(orderIds.size());
            for (OcBOrder ocBOrder : dbList) {
                List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItems(ocBOrder.getId());
                if (itemList.size() >= limitQty) {
                    canNotMergedOrderId.add(ocBOrder.getId());
                }
            }
            if (CollectionUtils.isNotEmpty(canNotMergedOrderId)) {
                orderIds.removeAll(canNotMergedOrderId);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.multiFormat("OcMergeOrderService.manualMergeHandle.encryptCode#{}, Ids=", orderIds),
                    encryptCode);
        }

        Set<Integer> orderStatusSet = dbList.stream().map(OcBOrder::getOrderStatus).collect(Collectors.toSet());
        if (orderStatusSet.size() > 1) {
            return new ValueHolderV14(ResultCode.FAIL, "所选单据状态不一致");
        }
        Integer orderStatus = dbList.get(0).getOrderStatus();

        String mergeResult = manualMergeGroup(encryptCode, orderIds, limitQty, user, orderStatus);
        ValueHolderV14 vh = new ValueHolderV14();
        if (StringUtils.isBlank(mergeResult)) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("合单成功");
            return vh;
        }
        vh.setCode(ResultCode.FAIL);
        vh.setMessage(mergeResult);
        return vh;
    }

    /**
     * 验证单据是否可合并
     *
     * @param encryptCodes 单据加密码
     * @return 验证信息
     */
    public ValueHolderV14<AutoAuditCheckMergeOrderDto> checkOrderIsMergeEnable(List<String> encryptCodes) {

        ValueHolderV14<AutoAuditCheckMergeOrderDto> vh = new ValueHolderV14<>();
        long l = System.currentTimeMillis();
        try {

            // 0. 校验参数
            AssertUtil.assertException(CollectionUtils.isEmpty(encryptCodes), "参数不能为空");

            // 1. 店铺合单策略
            Map<Long, StCMergeOrderDO> shopMergeSt = getShopMergeSt();
            AssertUtil.assertException(shopMergeSt == null || shopMergeSt.size() < 1, "未查询到开启自动合单策略的店铺");

            // 2. 构建查询参数
            MergeParam param = buildGsiSearchParam(encryptCodes, shopMergeSt);

            // 3. 查询可合并数据.暂不锁单.忽略此步; 如锁单需要重写此步骤
           /* Map<String, List<Long>> codeKeysMap = searchIdsByEncryptCodesOnGSI(param);
            AssertUtil.assertException(codeKeysMap == null || codeKeysMap.isEmpty(), "未查询到可合并订单组");
            Map<String, List<Long>> codeIdPairs = filterQty(codeKeysMap);
            AssertUtil.assertException(codeIdPairs.isEmpty(), "不存在可合并订单组");*/

            // 4. 过滤数据
            List<OcBOrder> orders = ocBOrderMapper.listOrdersByEncryptCodes(param);
            Map<String, List<OcBOrder>> prevMap = orders.parallelStream()
                    .collect(Collectors.groupingBy(OcBOrder::getOrderEncryptionCode, Collectors.toList()));
            // 5. 校验. 记录数据
            AutoAuditCheckMergeOrderDto dto = getAutoAuditCheckMergeOrderDto(encryptCodes, prevMap, shopMergeSt);
            vh.setData(dto);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("checkOrderIsMergeEnable.TimeConsume.{}ms"), (System.currentTimeMillis() - l));
            }
        } catch (Exception ex) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(ex.getMessage());
        }
        vh.setCode(ResultCode.SUCCESS);
        return vh;


    }

    /**
     * 合单
     * 合单MQ消费
     *
     * @param messageBody 加密码消息
     */
    public void autoMergeConsumeHandle(String messageBody, String messageKey) {
        long l = System.currentTimeMillis();
        List<String> encryptCodes = null;
        try {
            // 1. 解析消息
            encryptCodes = JSON.parseArray(messageBody, String.class);
            AssertUtil.assertException(CollectionUtils.isEmpty(encryptCodes), "消息体为空");
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoMergeOrderMq.Consume, MsgBody: {},MsgId/MsgKey =", messageKey, messageKey),
                        messageBody);
            }

            // 2. 店铺合单策略
            Map<Long, StCMergeOrderDO> shopMergeSt = getShopMergeSt();
            AssertUtil.assertException(shopMergeSt == null || shopMergeSt.size() < 1, "未查询到开启自动合单策略的店铺");

            // 3. 查询可合并订单编号
            MergeParam param = buildGsiSearchParam(encryptCodes, shopMergeSt);
            Map<String, List<Long>> codeKeysMap = searchIdsByEncryptCodesOnGSI(param);
            AssertUtil.assertException(codeKeysMap == null || codeKeysMap.isEmpty(), "未查询到可合并订单组");

            // 4. 多线程合单 已经失效
          //  Map<String, String> multiResult = multiPartWork(codeKeysMap, shopMergeSt, SystemUserResource.getRootUser());
            Map<String, String> multiResult = loopCode4MergeEachGroup(codeKeysMap, shopMergeSt);
            multiResult.put("MQ_CONSUME_TIME", String.valueOf((System.currentTimeMillis() - l)));
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoMergeOrderMq.Consume, Result: {},MsgId/MsgKey={}，{}",
                        messageKey, messageKey), JSON.toJSONString(multiResult),messageKey, messageKey);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("AutoMergeOrderMq.Consume, EncryptCodes: {}, Exp:{},.MsgId/MsgKey=",
                    messageKey, messageKey), encryptCodes, Throwables.getStackTraceAsString(e));
        } finally {
            if (CollectionUtils.isNotEmpty(encryptCodes)) {
                clearRedisMergeKey(encryptCodes);
            }
        }
    }


    /**
     * 循环合并
     *
     * @param dataMap
     * @param mStM
     * @return
     */
    private Map<String, String> loopCode4MergeEachGroup(Map<String, List<Long>> dataMap, Map<Long, StCMergeOrderDO> mStM) {
        User usr = SystemUserResource.getRootUser();
        int limit = this.omsSystemConfig.getAutoMergeOrderEachGroupQty();
        int size = dataMap.size();
        Map<String, String> logMap = new HashMap<>(size);
        int errorNum = 0;
        int successNum = 0;
        for (Map.Entry<String, List<Long>> ele : dataMap.entrySet()) {
            long l = System.currentTimeMillis();
            String msg = autoMergeEachGroup(ele.getKey(), ele.getValue(), mStM, limit, usr);
            if (StringUtils.isNotBlank(msg)) {
                logMap.put(ele.getKey(), "=" + JSON.toJSONString(ele.getValue()) + " :: " + msg);
                errorNum++;
                continue;
            }
            logMap.put(ele.getKey(), "=" + JSON.toJSONString(ele.getValue()) + " CT: "
                    + (System.currentTimeMillis() - l));
            successNum++;
        }
        logMap.put("TASK_INFO", "预处理总量: " + size + " , 处理成功:" + successNum + ", 失败:" + errorNum);
        return logMap;
    }

    /**
     * 单据校验
     *
     * @param orders        预合并单据
     * @param mergeStMap    合并策略
     * @param is2CDetention 是否2C待寻源卡单的订单合并
     * @return 单据校验信息
     */
    private String checkOrderInfo(List<OcBOrder> orders, Map<Long, StCMergeOrderDO> mergeStMap, MergeTypeEnum mgType, boolean is2CDetention) {
        MergeOderGroups group = JSON.parseObject(JSON.toJSONString(orders.get(0)), MergeOderGroups.class);
        Set<Integer> typeSet = null;
        StCMergeOrderInfo mergeStInfo = null;
        if (MergeTypeEnum.AUTO == mgType) {
            StCMergeOrderDO stCMergeOrderDO = mergeStMap.get(group.getCpCShopId());
            mergeStInfo = JSON.parseObject(JSON.toJSONString(stCMergeOrderDO), StCMergeOrderInfo.class);
            typeSet = getSplitMergeSt(mergeStInfo);
        }
        OcBOrder firstOrder = orders.get(0);
        Iterator<OcBOrder> iterator = orders.iterator();
        List<Long> orderIds = new ArrayList<>(orders.size());
        StringBuilder sb = new StringBuilder();
        List<OcBOrderItem> itemAll = new ArrayList<>();
        String oaid = Optional.ofNullable(orders.get(0).getOaid()).orElse("");

        Integer isPlainAddr = Optional.ofNullable(orders.get(0).getIsPlainAddr()).orElse(0);
        String sourceCode = Optional.ofNullable(orders.get(0).getSourceCode()).orElse("");

        /*2C待寻源卡单的时候没有寻源的发货仓*/
        if (!is2CDetention) {
            //判断发货实体仓是否允许合单
            CpCPhyWarehouse warehouse = cpRpcService.queryByWarehouseId(firstOrder.getCpCPhyWarehouseId());
            if (ObjectUtils.isEmpty(warehouse) || R3CommonResultConstants.VALUE_N.equals(warehouse.getIsMergeOrder())) {
                return sb.append("发货实体仓【")
                        .append(Optional.ofNullable(warehouse).orElse(new CpCPhyWarehouse()).getEname())
                        .append("】是否允许合单配置为否,不允许合单").toString();
            }
        }

        Map<String, String> skuAddServiceMap = new HashMap<>();
        while (iterator.hasNext()) {
            OcBOrder next = iterator.next();
            if (PlatFormEnum.VIP_JITX.getCode().equals(firstOrder.getPlatform())) {
                if (!PlatFormEnum.VIP_JITX.getCode().equals(next.getPlatform())) {
                    String erMsg = "订单编号: " + next.getId() + "不是唯品会JITX订单,非JITX订单不可与JITX订单进行合单！";
                    sb.append(erMsg).append(", ");
                    if (MergeTypeEnum.MANUAL == mgType) {
                        return erMsg;
                    }
                    iterator.remove();
                    continue;
                }
                if (!YesNoEnum.ONE.getKey().equals(next.getJitxRequiresMerge())) {
                    String erMsg = "订单编号: " + next.getId() + "的JITX订单“JITX要求合包”为否，不允许合并！";
                    sb.append(erMsg).append(", ");
                    if (MergeTypeEnum.MANUAL == mgType) {
                        return erMsg;
                    }
                    iterator.remove();
                    continue;
                }
                if (IsForbiddenDeliveryEnum.FORBIDDEN.getCode().equals(next.getIsForbiddenDelivery())) {
                    String erMsg = "订单编号: " + next.getId() + "的JITX订单“是否可发货”为否，不允许合并！";
                    sb.append(erMsg).append(", ");
                    if (MergeTypeEnum.MANUAL == mgType) {
                        return erMsg;
                    }
                    iterator.remove();
                    continue;
                }
                if (StringUtils.isEmpty(firstOrder.getMergedCode()) || !firstOrder.getMergedCode().equals(next.getMergedCode())) {
                    String erMsg = "订单编号: " + next.getId() + "与其他JITX订单合包码不一致,不允许合单";
                    sb.append(erMsg).append(", ");
                    if (MergeTypeEnum.MANUAL == mgType) {
                        return erMsg;
                    }
                    iterator.remove();
                    continue;
                }
                if (omsOrderManualAuditService.checkRedisJitxChangeWarehouseFlag(next)) {
                    String erMsg = "订单编号: " + next.getId() + "正在改仓中,不允许合单";
                    sb.append(erMsg).append(", ");
                    if (MergeTypeEnum.MANUAL == mgType) {
                        return erMsg;
                    }
                    iterator.remove();
                    continue;
                }
            }


            List<OcBOrderItem> items = ocBOrderItemMapper.selectList(new LambdaQueryWrapper<OcBOrderItem>()
                    .eq(OcBOrderItem::getOcBOrderId, next.getId())
                    .eq(OcBOrderItem::getIsactive, "Y"));
            if (CollectionUtils.isNotEmpty(items)) {
                itemAll.addAll(items);
            }
            if (CollectionUtils.isNotEmpty(itemAll)) {
                for (OcBOrderItem ocBOrderItem : itemAll) {
                    if (StringUtils.isNotEmpty(ocBOrderItem.getLabelingRequirements())) {
                        String addService = skuAddServiceMap.get(ocBOrderItem.getPsCSkuEcode());
                        if (addService != null && addService == "") {
                            String erMsg = "订单编号: " + next.getId() + "商品编码:" + ocBOrderItem.getPsCSkuEcode() + "存在与其他订单明细同sku不同增值服务类型";
                            sb.append(erMsg).append(", ");
                            if (MergeTypeEnum.MANUAL == mgType) {
                                return erMsg;
                            }
                            iterator.remove();
                            continue;
                        }
                        if (StringUtils.isEmpty(addService)) {
                            skuAddServiceMap.put(ocBOrderItem.getPsCSkuEcode(), ocBOrderItem.getLabelingRequirements());
                        } else if (!ocBOrderItem.getLabelingRequirements().equals(addService)) {
                            String erMsg = "订单编号: " + next.getId() + "商品编码:" + ocBOrderItem.getPsCSkuEcode() + "存在与其他订单明细同sku不同增值服务类型";
                            sb.append(erMsg).append(", ");
                            if (MergeTypeEnum.MANUAL == mgType) {
                                return erMsg;
                            }
                            iterator.remove();
                            continue;
                        }
                    } else {
                        String addService = skuAddServiceMap.get(ocBOrderItem.getPsCSkuEcode());
                        if (StringUtils.isNotEmpty(addService)) {
                            String erMsg = "订单编号: " + next.getId() + "商品编码:" + ocBOrderItem.getPsCSkuEcode() + "存在与其他订单明细同sku不同增值服务类型";
                            sb.append(erMsg).append(", ");
                            if (MergeTypeEnum.MANUAL == mgType) {
                                return erMsg;
                            }
                            iterator.remove();
                            continue;
                        } else {
                            skuAddServiceMap.put(ocBOrderItem.getPsCSkuEcode(), "");
                        }
                    }
                }
            }
            String isPassBaseCheck = checkBaseOrderInfo(next, group, is2CDetention);
            if (StringUtils.isNotBlank(isPassBaseCheck)) {
                String erMsg = "订单编号: " + next.getId() + isPassBaseCheck;
                sb.append(erMsg).append(", ");
                if (MergeTypeEnum.MANUAL == mgType) {
                    return erMsg;
                }
                iterator.remove();
                continue;
            }
            String isPassStCheck = checkMergeStrategy(next, mergeStInfo, typeSet, mgType);
            if (StringUtils.isNotBlank(isPassStCheck)) {
                String erMsg = "订单编号: " + next.getId() + isPassStCheck;
                sb.append(erMsg).append(", ");
                if (MergeTypeEnum.MANUAL == mgType) {
                    return erMsg;
                }
                iterator.remove();
            } else {
                //其他合单条件校验通过后 再进行品类限制校验
                orderIds.add(next.getId());
            }

            //所有单据的oaid必须一样，或者全部为空
            if (!oaid.equals(Optional.ofNullable(next.getOaid()).orElse(""))) {
                return "收货人信息不一致[oaid]";
            }

            //所有单据的「是否明文：is_plain_addr」必须一样，或者全部为空
            if (!isPlainAddr.equals(Optional.ofNullable(next.getIsPlainAddr()).orElse(0))) {
                return "是否明文-信息不一致[is_plain_addr]";
            }

            if (is2CDetention) {
                //2C待寻源卡单 必须同一个平台单号才可以合单
                if (!sourceCode.equals(Optional.ofNullable(next.getSourceCode()).orElse(""))) {
                    return "平台单号-信息不一致[source_code]";
                }
            }

        }

        if (MergeTypeEnum.AUTO == mgType) {
            return sb.toString();
        }
        return null;
    }

    /**
     * 审核.校验单据是否可合并信息
     *
     * @param encryptCodes 加密信息串
     * @param prevMap      预校验单据
     * @param shopMergeSt  自动合并店铺策略
     * @return 校验信息
     */
    private AutoAuditCheckMergeOrderDto getAutoAuditCheckMergeOrderDto(List<String> encryptCodes,
                                                                       Map<String, List<OcBOrder>> prevMap,
                                                                       Map<Long, StCMergeOrderDO> shopMergeSt) {
        AutoAuditCheckMergeOrderDto dto = new AutoAuditCheckMergeOrderDto();
        List<String> enableList = new ArrayList<>(prevMap.size());
        List<String> unableList = new ArrayList<>(prevMap.size());
        Map<String, String> unableMsg = new HashMap<>(prevMap.size());
        for (Map.Entry<String, List<OcBOrder>> entry : prevMap.entrySet()) {
            if (!encryptCodes.contains(entry.getKey())) {
                log.error(LogUtil.format("校验可合并单据,查询到参数中不包含的单据编号=", entry.getKey()));
                continue;
            }
            List<OcBOrder> orders=entry.getValue();
            if (entry.getValue().size() < MIN_UNIT ||(orders.size() >= MIN_UNIT && checkMinNum(orders))) {
                unableList.add(entry.getKey());
                encryptCodes.remove(entry.getKey());
                unableMsg.put(entry.getKey(), "查询可合并单据数量小于2条,或者最小两单擦超过了最大和单数");
                continue;
            }
            String autoCheckMsg = checkOrderInfo(orders, shopMergeSt, MergeTypeEnum.AUTO, false);
            if (StringUtils.isNotBlank(autoCheckMsg)) {
                unableList.add(entry.getKey());
                encryptCodes.remove(entry.getKey());
                unableMsg.put(entry.getKey(), autoCheckMsg);
                continue;
            }
            if (orders.size() < MIN_UNIT || (orders.size() >= MIN_UNIT &&checkMinNum(orders))) {
                unableList.add(entry.getKey());
                encryptCodes.remove(entry.getKey());
                unableMsg.put(entry.getKey(), "单据校验通过数量小于2条,或者最小两单擦超过了最大和单数");
                continue;
            }
            enableList.add(entry.getKey());
            encryptCodes.remove(entry.getKey());
        }

        for (String code : encryptCodes) {
            unableList.add(code);
            unableMsg.put(code, "条件过滤查询,未查询到此单据");
        }

        dto.setEnableMergeCodes(enableList);
        dto.setUnableMergeCodes(unableList);
        dto.setUnableMergeMessages(unableMsg);
        return dto;
    }

    //校验合单 数量最后两个元素数量超过最大数就让审核
    private boolean checkMinNum(List<OcBOrder> orders) {
        try {
            String limitNum = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(JITX_MERGE_LIMIT_QUANTITY);
            Collections.sort(orders, Comparator.comparing(OcBOrder::getQtyAll));
            int count =0;
            for (int i = 0; i < orders.size(); i++) {
                if (i<3){
                    count = count+orders.get(i).getQtyAll().intValue();
                }
            }
            if (count>Integer.valueOf(limitNum)){
                return true;
            }
        }catch (Exception e){
            log.error("校验合单 数量最后两个元素数量超过最大数异常，原因:{}", Throwables.getStackTraceAsString(e));
        }
        return false;
    }


    /**
     * 合单, 拆分类型合并策略
     *
     * @param mergeStInfo 合单策略
     * @return 拆分类型
     */
    public Set<Integer> getSplitMergeSt(StCMergeOrderInfo mergeStInfo) {
        Set<Integer> typeSet = null;
        String splitTypes = mergeStInfo.getMergeSplitOrderType();
        if (StringUtils.isNotBlank(splitTypes)) {
            typeSet = Arrays.stream(splitTypes.split(",")).map(Integer::valueOf).collect(Collectors.toSet());
            typeSet.add(OmsMergeSplitOrderType.DEFAULT_00.getVal());
        }
        return typeSet;
    }

    /**
     * 通用校验
     * 基础数据校验. 部分信息不再判断合法性
     * 去除mq后,基础校验可去掉, 直接比对加密串
     *
     * @param o             订单
     * @param base          合单信息
     * @param is2CDetention 是否2C待寻源卡单的订单合并
     * @return 通过/true
     */
    public String checkBaseOrderInfo(OcBOrder o, MergeOderGroups base, boolean is2CDetention) {
        // 1.0 通用校验. 单据状态
        // 单据状态
        if (is2CDetention) {
            /*必须是待寻源 且 卡单 状态*/
            if (!(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(o.getOrderStatus())
                    && YesNoEnum.Y.getVal().equals(o.getIsDetention()))) {
                return " 非待寻源且卡单状态";
            }
        } else {
            // 单据状态
            if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(o.getOrderStatus())) {
                return " 非待审核状态";
            }
        }
        // 拦截状态
        if (OmsOrderIsInterceptEnum.YES.getVal().equals(o.getIsInterecept())) {
            return " 拦截中";
        }
        // 退款状态
        if (!InreturningStatus.INRETURN_NO.equals(o.getIsInreturning())) {
            return " 退款中";
        }
        // 预售
        boolean isPreSaleOrder = OrderTypeEnum.TBA_PRE_SALE.getVal().equals(o.getOrderType())
                || OcBOrderConst.IS_STATUS_IY.equals(o.getDouble11PresaleStatus());
        if (isPreSaleOrder) {
            if (StringUtils.isNotEmpty(o.getStatusPayStep()) && !TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equals(o.getStatusPayStep())) {
                return " 预售尾款未付";
            }
        }
        // 猫超
        if (PlatFormEnum.ALIBABAASCP.getCode().equals(o.getPlatform())) {
            return " 猫超订单不参与合单";
        }

        // 2.0 基础验证
        // 单据类型
        if (o.getOrderType() == null || bothNe(base.getOrderType(), o.getOrderType())) {
            return " 单据类型不一致/空";
        }
        // 店铺
        if (o.getCpCShopId() == null || bothNe(base.getCpCShopId(), o.getCpCShopId())) {
            return " 平台店铺不一致/空";
        }

        // 平台
        if (o.getPlatform() == null || bothNe(base.getPlatform(), o.getPlatform())) {
            return " 平台不一致/空";
        }

        if (!is2CDetention) {
            // 仓库
            if (o.getCpCPhyWarehouseId() == null || bothNe(base.getCpCPhyWarehouseId(), o.getCpCPhyWarehouseId())) {
                return " 发货仓库不一致/空";
            }
        }

        // 手机
        if (StringUtils.isBlank(o.getReceiverMobile()) || ne(base.getReceiverMobile(), o.getReceiverMobile())) {
            return " 收货人手机不一致/空";
        }

        // 电话
        if (ne(base.getReceiverPhone(), o.getReceiverPhone())) {
            return " 收货人电话不一致";
        }

        // 省
        if (o.getCpCRegionProvinceId() == null || bothNe(base.getCpCregionProvinceId(), o.getCpCRegionProvinceId())) {
            return " 收货人省不一致/空";
        }

        // 市
        if (o.getCpCRegionAreaId() != null && bothNe(base.getCpCregionAreaId(), o.getCpCRegionAreaId())) {
            return " 收货人区不致/空";
        }

        // 区
        if (bothNe(base.getCpCregionCityId(), o.getCpCRegionCityId())) {
            return " 收货人市不一致";
        }
        // 收货人
        if (ne(base.getReceiverName(), o.getReceiverName())) {
            return " 收货人姓名不一致";
        }

        // 地址
        if (StringUtils.isBlank(o.getReceiverAddress()) || ne(base.getReceiverAddress(), o.getReceiverAddress())) {
            return " 收货地址不一致/空";
        }
        // 昵称
        if (ne(base.getUserNick(), o.getUserNick())) {
            return " 买家昵称不一致";
        }

        /*如果是2C待寻源卡单，有可能寻源过，导致仓不一致，最终的加密码也不一致，和产品确认后，不校验加密码*/
        if (!is2CDetention) {
            // 加密码
            if (bothNe(base.getOrderEncryptionCode(), o.getOrderEncryptionCode())) {
                return " 单据信息不一致";
            }
        }
//        if (OcBOrderConst.IS_STATUS_IY.equals(o.getIsSplit())) {
//            return " 已拆单的单据不允许合单";
//        }
        return null;
    }

    /**
     * 合单策略.校验. 自动合单
     *
     * @param o   订单
     * @param mSt 合单策略
     * @return true/通过
     */
    public String checkMergeStrategy(OcBOrder o, StCMergeOrderInfo mSt, Set<Integer> typeSet, MergeTypeEnum mgType) {
        Long businessTypeId = o.getBusinessTypeId();
        if (businessTypeId != null) {
            StCBusinessType stCBusinessType = omsBusinessTypeStService.selectBusinessTypeById(businessTypeId);
            if (stCBusinessType != null) {
                String isMergeOrder = stCBusinessType.getIsMergeOrder();
                if ("N".equals(isMergeOrder) || StringUtils.isEmpty(isMergeOrder)) {
                    return "该订单业务类型不允许合并";
                }
            }
        }
        if (MergeTypeEnum.AUTO == mgType) {
            // 策略开启?
            if (!OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(mSt.getIsAutomerge())) {
                return " 未开启自动合单策略";
            }
            // 已取消
            if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(o.getIsCancelMerge())) {
                return " 已取消合并的订单不允许自动合并";
            }

            // 预售
            boolean isPreSaleOrder = OrderTypeEnum.TBA_PRE_SALE.getVal().equals(o.getOrderType())
                    || OcBOrderConst.IS_STATUS_IY.equals(o.getDouble11PresaleStatus());
            if (isPreSaleOrder) {
                if (!OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(mSt.getIsMergerPresell())) {
                    return " 未开启允许相同预售订单合并";
                }
            }

//            String stCBusinessTypeIds = mSt.getStCBusinessTypeIds();
//            if (StringUtils.isNotEmpty(stCBusinessTypeIds)
//                    && Arrays.asList(stCBusinessTypeIds.split(",")).contains(o.getBusinessTypeId().toString())) {
//                return " 排除订单业务类型不允许自动合并";
//            }
            // 拆单
//            if (!splitType(typeSet, o.getSplitReason())) {
//                return " 拆单类型[" + o.getSplitReason() + "]不允许合单";
//            }
            // 换货
            if (OrderTypeEnum.EXCHANGE.getVal().equals(o.getOrderType())) {
                if (IsActiveEnum.N.getVal().equals(mSt.getIsAutochange())) {
                    return " 自动合单策略未开启换货类型";
                }
            }
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @Date 15:47 2021/6/30
     * @Description 合单校验是否满足品类拆单 满足合 不满足则合
     */
   /* private boolean checkSplitByGoodClass(List<OcBOrderItem> ocBOrderItems,Long warehouseId) {
        log.info(LogUtil.format("合单校验是否满足品类拆单 明细集合{},实体仓为=", warehouseId), ocBOrderItems);
        StCWarehouseQueryResult stCWarehouseQueryResult =
                stRpcService.getStCWarehouseQueryResultByWareHouseId(warehouseId);
        if (stCWarehouseQueryResult != null
                && ("Y".equals(stCWarehouseQueryResult.getStCWarehouse().getIsGoodsSplit()))) {
            List<StCWarehouseGoodsClassDO> stCWarehouseGoodsClassDOList = stCWarehouseQueryResult.getStCWarehouseGoodsClassList();
            if (CollectionUtils.isNotEmpty(stCWarehouseGoodsClassDOList)){
                List<OcBOrderItem> items = ocBOrderItems;
                //总数量
                int num = items.stream().mapToInt(i ->i.getQty().intValue()).sum();
                // 策略中配置的单品类限制数
                Map<Long, Integer> goodClassMap =
                        stCWarehouseGoodsClassDOList.stream().collect(Collectors.toMap(StCWarehouseGoodsClassDO::getPsCProdimId,
                                StCWarehouseGoodsClassDO::getNum, (v1, v2) -> v1));
                // 策略中配置的多品类限制
                Map<Long, Integer> otherGoodClassMap =
                        stCWarehouseGoodsClassDOList.stream().collect(Collectors.toMap(StCWarehouseGoodsClassDO::getPsCProdimId,
                                StCWarehouseGoodsClassDO::getOtherNum, (v1, v2) -> v1));
                //分组求和  key 品类 val 品类的 数量
                Map<Long,Integer> mapItem =  items.stream().filter(i -> getPsCProdimId(i)).collect((Collectors.groupingBy(i->setColumn(i),Collectors.reducing(
                        0,
                        i->i.getQty().intValue(),
                        Integer::sum))));
                for (Long aLong : mapItem.keySet()) {
                    //当前品类数量
                    int qty= mapItem.get(aLong);
                    //单品类数量
                    Integer singleNum = goodClassMap.get(aLong);
                    //单品类
                    if (qty >=singleNum){
                        //多品类
                        if (mapItem.size()>1){
                            //多品类数量
                            Integer moreNum = otherGoodClassMap.get(aLong);
                            //剩余数量
                            int otherQty = num - qty;
                            if (otherQty >=moreNum){
                                return true;
                            }
                        }else {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private boolean getPsCProdimId(OcBOrderItem item) {
        Long prId = item.getPsCProId();
        PsCPro psCPro =this.queryProByIds(prId);
        return psCPro != null && psCPro.getPsCProCategoryId() != null;
    }

    private Long setColumn(OcBOrderItem item) {
        Long prId = item.getPsCProId();
        PsCPro psCPro=this.queryProByIds(prId);
        return  psCPro!=null?psCPro.getPsCProCategoryId():null;
    }*/

    /**
     * <AUTHOR>
     * @Date 13:44 2021/4/29
     * @Description 根据商品编码查询商品档案
     */
    public PsCPro queryProByIds(Long proId) {
        List<Integer> list = new ArrayList<Integer>();
        list.add(proId.intValue());
        List<PsCPro> proList = skuLikeQueryCmd.queryProByIds(list);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(proList)) {
            return proList.get(0);
        }
        return null;
    }

    /**
     * 手动合单
     *
     * @param key      加密串
     * @param value    订单编号
     * @param limitQty 合并数量上限
     * @param user     操作用户
     * @return 合并信息
     */
    private String manualMergeGroup(String key, List<Long> value, int limitQty, User user, Integer orderStatus) {
        // 1. 合并参数
        if (value == null || value.size() < MIN_UNIT) {
            return "查询可合并订单数量小于2条";
        }
        // 2. 0 锁单
        List<RedisReentrantLock> lockList = new ArrayList<>(value.size());
        String sqlIds = batchLockOrder(value, lockList);
        if (lockList.size() < value.size()) {
            batchUnLockOrder(lockList);
            return "部分订单正在操作, 请稍后尝试";
        }
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OcMergeOrderService.manualMergeGroup,Step02-AFLock, Ids#{},encryptCode=",
                        key), value);
            }
            // 3.0 查询数据
            List<OcBOrder> preMergeOrders = getOcBOrders(key, sqlIds, orderStatus);
            //过滤 平台店铺渠道为：一件代发经销平台的订单
            getFilterIssuingOrders(preMergeOrders);

            boolean is2CDetention = false;
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)) {
                // 原逻辑，只能是「待审核」的数据才能合并
            } else if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                boolean isToBOrder = OmsBusinessTypeUtil.isToBOrder(preMergeOrders.get(0));
                if (isToBOrder) {
                    return "仅2C待寻源卡单的订单才可以通过此入口合并";
                }
                Set<Integer> isDetentionSet = ListUtils.emptyIfNull(preMergeOrders).stream()
                        .map(OcBOrder::getIsDetention).collect(Collectors.toSet());
                /*待寻源的数据，只有卡单才可以合并*/
                if (isDetentionSet.contains(YesNoEnum.N.getVal())) {
                    return "待寻源的数据，只有卡单的数据才可以合并";
                }
                is2CDetention = true;
            } else {
                return "当前只有「待寻源的卡单」或「待审核」状态的数据可合并";
            }

            if (preMergeOrders == null || preMergeOrders.size() < MIN_UNIT) {
                return "查询订单,可合并数量小于2条";
            }
            // 3.1 手动查询结果
            if (preMergeOrders.size() < value.size()) {
                return "部分订单无法合并, 请确认所有单据符合合并要求";
            }
            // 4. 合单校验
            String checkMsg = checkOrderInfo(preMergeOrders, null, MergeTypeEnum.MANUAL, is2CDetention);
            if (StringUtils.isNotBlank(checkMsg)) {
                return checkMsg;
            }
            if (preMergeOrders.size() < value.size()) {
                return "部分单据无法合并";
            }
            // 5. 合单
            batchMergeOrder(preMergeOrders, limitQty, MergeTypeEnum.MANUAL, user, is2CDetention);
        } catch (Exception ex) {
            log.error(LogUtil.format("合单异常: ,error:{}"), Throwables.getStackTraceAsString(ex));
            return getSubExceptionMsg(ex, 210);
        } finally {
            // 6. 解锁
            batchUnLockOrder(lockList);
        }
        return null;
    }

    /**
     * 自动
     *
     * @param os   当前合并批次订单
     * @param mSt  自动合单策略
     * @param qty  每组合并数量上限
     * @param user 操作用户
     */
    private String autoMergeEachGroup(String key, List<Long> os, Map<Long, StCMergeOrderDO> mSt, int qty, User user) {
        // 1. 合并参数
        if (os == null || os.size() < MIN_UNIT) {
            return "查询可合并订单数量小于2条";
        }
        // 2. 0 锁单
        List<RedisReentrantLock> lockList = new ArrayList<>(os.size());
        String sqlIds = batchLockOrder(os, lockList);
        if (lockList.size() < MIN_UNIT || os.size() < MIN_UNIT) {
            batchUnLockOrder(lockList);
            return "锁单成功数量小于2条, 部分订单正在操作";
        }
        try {

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("autoMergeEachGroup.AFLockOrder, Ids#{},encryptCode=", key), os);
            }

            // 3.0 查询数据
            List<OcBOrder> preMergeOrders = getOcBOrders(key, sqlIds, null);
            //过滤 平台店铺渠道为：一件代发经销平台的订单
            getFilterIssuingOrders(preMergeOrders);
            if (preMergeOrders == null || preMergeOrders.size() < MIN_UNIT) {
                return "查询订单,可合并数量小于2条";
            }
            // 3.2 自动查询结果过滤
            preMergeOrders = preMergeOrders.parallelStream()
                    .filter(e -> mSt.containsKey(e.getCpCShopId())).collect(Collectors.toList());
            if (preMergeOrders.size() < 2) {
                return "店铺开启自动合并策略的订单小于2条";
            }
            // 4. 合单校验
            String autoCheckMsg = checkOrderInfo(preMergeOrders, mSt, MergeTypeEnum.AUTO, false);
            if (StringUtils.isNotBlank(autoCheckMsg)) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("autoMergeEachGroup, CheckValidMsg:{},EncryptCode=", key), autoCheckMsg);
                }
                return autoCheckMsg;
            }
            //唯品会JITX需重置限制数量
            if (PlatFormEnum.VIP_JITX.getCode().equals(preMergeOrders.get(0).getPlatform())) {
                List<OcBOrder> jitXCanNotMergeOrders = new ArrayList<>();
                int jitxAutoMergeOrderEachGroupQty = businessSystemParamService.getJitxMergedOrderLimit();
                //当设置的参数小于20 即唯品会平台限制数量时 按照设置的参数来即可
                qty = jitxAutoMergeOrderEachGroupQty;
                //合单后再次合并 即把20单合并后单子再次合并需做限制
                for (OcBOrder ocBOrder : preMergeOrders) {
                    //此处可优化为使用主表数量字段进行判断
                    List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItems(ocBOrder.getId());
                    if (itemList.size() >= qty) {
                        jitXCanNotMergeOrders.add(ocBOrder);
                    }
                }
                if (CollectionUtils.isNotEmpty(jitXCanNotMergeOrders)) {
                    preMergeOrders.removeAll(jitXCanNotMergeOrders);
                }
            }

            if (preMergeOrders.size() < MIN_UNIT) {
                return "单据校验通过数量小于2条";
            }

            // 5. 合单
            String mergeResult = batchMergeOrder(preMergeOrders, qty, MergeTypeEnum.AUTO, user, false);
            if (StringUtils.isNotBlank(mergeResult)) {
                return mergeResult;
            }

        } catch (Exception ex) {
            log.error(LogUtil.format("autoMergeEachGroup, Exception: {},EncryptCode=", key),
                    Throwables.getStackTraceAsString(ex));
            return getSubExceptionMsg(ex, 210);
        } finally {
            // 6. 解锁
            batchUnLockOrder(lockList);
        }
        return null;
    }

    /**
     * 查询待合并订单
     *
     * @param encryptCode 单据信息加密串
     * @param value       预查询单据编号
     * @return 预合并订单集
     */
    private List<OcBOrder> getOcBOrders(String encryptCode, String value, Integer orderStatus) {
        OcBOrder searchOrder = new OcBOrder();
//        searchOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
        searchOrder.setOrderStatus(Objects.isNull(orderStatus) ? OmsOrderStatus.UNCONFIRMED.toInteger() : orderStatus);
        searchOrder.setIsInterecept(InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());
        searchOrder.setIsInreturning(InreturningStatus.INRETURN_NO);
//        searchOrder.setPlatform(PlatFormEnum.VIP_JITX.getCode());
        searchOrder.setPayType(OmsPayType.CASH_ON_DELIVERY.toInteger());
        searchOrder.setOrderEncryptionCode(encryptCode);
        searchOrder.setIsSameCityPurchase(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
        searchOrder.setReserveVarchar01(value);
        //是否禁发
//        searchOrder.setIsForbiddenDelivery(IsForbiddenDeliveryEnum.FORBIDDEN.getCode());
        return ocBOrderMapper.listOrder4MergeByIds(searchOrder);
    }

    /**
     * 零售发货单合单 过滤 一件代发经销平台的订单
     * @param orderList
     * @return
     */
    private void getFilterIssuingOrders(List<OcBOrder> orderList){
        if(CollectionUtils.isEmpty(orderList)){
            return;
        }
        List<OcBOrder> issuingOrderList = new ArrayList<>();
        for(OcBOrder ocBOrder:orderList){
            boolean issuing = omsOrderService.checkOrderIssuing(ocBOrder.getId(),ocBOrder.getCpCShopId());
            if(issuing){
                issuingOrderList.add(ocBOrder);
            }
        }
        if(CollectionUtils.isNotEmpty(issuingOrderList)){
            orderList.removeAll(issuingOrderList);
        }
        return;
    }
    /**
     * 分组: 加密串
     *
     * @param mergeOrderEsResult 合单条件加密串
     * @return Map<合单信息串, 订单编号集>
     */
    private Map<String, List<Long>> searchOrderIdByEncryptCode2Map(MergeOrderEsResult mergeOrderEsResult) {

        String[] searchFields = {"ID", "ORDER_ENCRYPTION_CODE"};
        JSONArray resultJsnAay = ES4Order.searchOrderInfoByEncryptCode(mergeOrderEsResult, searchFields);
        if (CollectionUtils.isEmpty(resultJsnAay)) {
            return null;
        }
        List<JSONObject> jsonObjects = JSON.parseArray(resultJsnAay.toJSONString(), JSONObject.class);
        return jsonObjects.parallelStream().collect(Collectors.groupingBy(
                e -> e.getString("ORDER_ENCRYPTION_CODE"),
                Collectors.mapping(e -> e.getLong("ID"), Collectors.toList()))
        );
    }

    /**
     * 锁单
     *
     * @param orderIdList es查询符合合并的订单编号集
     * @return 重入锁集合
     */
    private String batchLockOrder(List<Long> orderIdList, List<RedisReentrantLock> lockList) {
        StringBuilder sb = new StringBuilder();
        Iterator<Long> iterator = orderIdList.iterator();
        while (iterator.hasNext()) {
            Long next = iterator.next();
            try {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(next);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                if (redisLock.tryLock(bllRedisLockOrderUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockList.add(redisLock);
                    sb.append(",").append(next);
                    continue;
                }
            } catch (Exception e) {
                log.error(LogUtil.format("合并订单锁定异常,error:{},Id=", next), Throwables.getStackTraceAsString(e));
            }
            iterator.remove();
        }
        return sb.length() > 1 ? sb.substring(1) : null;
    }

    /**
     * 解锁
     *
     * @param locks 合单锁集合
     */
    private void batchUnLockOrder(List<RedisReentrantLock> locks) {
        for (RedisReentrantLock lock : locks) {
            try {
                lock.unlock();
            } catch (Exception ex) {
                log.error(LogUtil.format("合并订单解锁异常,error:{}.Id=", lock.getLockId()),
                        Throwables.getStackTraceAsString(ex));
            }
        }
    }

    /**
     * 拆分合并
     *
     * @param typeSet     允许合并的拆分类型
     * @param splitReason 拆分类型
     * @return true:允许合并
     */
    private boolean splitType(Set<Integer> typeSet, Integer splitReason) {

        if (splitReason == null || splitReason == OmsMergeSplitOrderType.DEFAULT_00.getVal()) {
            return true;
        }
        return CollectionUtils.isNotEmpty(typeSet) && typeSet.contains(splitReason);
    }

    /**
     * 合单
     *
     * @param orders        预合并单据
     * @param eachSize      单据合并数量上限
     * @param is2CDetention 是否2C待寻源卡单的订单合并
     */
    private String batchMergeOrder(List<OcBOrder> orders, int eachSize, MergeTypeEnum typeEnum, User user, boolean is2CDetention) {
        int size = orders.size(), startIndex = 0;
        int length = size;
        List<OcBOrder> subList;
        StringBuilder sb = new StringBuilder();
        while (size > 0) {
            // 非JitX订单执行原有逻辑
            if (!PlatFormEnum.VIP_JITX.getCode().equals(orders.get(0).getPlatform())) {
                if (size > eachSize) {
                    subList = orders.subList(startIndex, startIndex + eachSize);
                    startIndex += eachSize;
                } else {
                    subList = orders.subList(startIndex, length);
                }
                size -= eachSize;
                String mergeMsg = mergeOrder(subList, eachSize, typeEnum, user, is2CDetention);
                if (StringUtils.isNotBlank(mergeMsg)) {
                    sb.append(mergeMsg);
                }
            }else {
                //判断是否包含数量大于1的订单 门店JitX未合包前为一单一件  如果数量大于1 此时为再次合包 需要在限制20 的前提下组合合包
                Optional<OcBOrder> existHasMergedOrder = orders.stream()
                        .filter(x -> x.getQtyAll() != null && x.getQtyAll().intValue() > 1).findAny();
                if (existHasMergedOrder.isPresent()) {
                    //组合集合
                    List<List<OcBOrder>> groupBySumQty = new ArrayList<>();
                    //当超过限制数量十倍时 分割数量
                    if (orders.size() < eachSize * 10) {
                        //获取所有的最佳组合 PS： 最佳组合指的是该组合中数据量最少 此时必然包含较大值
                        groupBySumQty = OrderMergeUtil.getMergeLists(orders, eachSize);
                    } else {
                        //分割数据量
                        List<List<OcBOrder>> partitionList = Lists.partition(orders, eachSize * 10);
                        //循环获取组合数据
                        for (List<OcBOrder> partition : partitionList) {
                            List<List<OcBOrder>> mergeLists = OrderMergeUtil.getMergeLists(partition, eachSize);
                            if (CollectionUtils.isNotEmpty(mergeLists)) {
                                groupBySumQty.addAll(mergeLists);
                            }
                        }
                    }
                    if (!groupBySumQty.isEmpty()) {
                        log.debug("{},groupBySumQty:{}", this.getClass().getSimpleName(), JSON.toJSONString(groupBySumQty));
                    }
                    if (CollectionUtils.isNotEmpty(groupBySumQty)) {
                        //将所有组合进行合并
                        for (List<OcBOrder> partition : groupBySumQty) {
                            if (log.isDebugEnabled()) {
                                log.debug("组合后需要合并数据:{}", JSON.toJSONString(partition));
                            }
                            String mergeMsg = mergeOrder(partition, eachSize, typeEnum, user, is2CDetention);

                            if (StringUtils.isNotBlank(mergeMsg)) {
                                sb.append(mergeMsg);
                            }
                            size -= partition.size();
                        }
                        if (log.isDebugEnabled()) {
                            log.debug("{},未组合剩余数据size:{},orders:{}",this.getClass().getSimpleName(), orders.size(), JSON.toJSONString(orders));
                        }
                        //剩余未组合的数据
                        length = orders.size();
                        //数据递减 直至处理完毕跳出while
                        size -= length;
                        String mergeMsg = mergeOrder(orders, eachSize, typeEnum, user, is2CDetention);
                        if (StringUtils.isNotBlank(mergeMsg)) {
                            sb.append(mergeMsg);
                        }
                    } else {
                        if (size > eachSize) {
                            subList = orders.subList(startIndex, startIndex + eachSize);
                            startIndex += eachSize;
                        } else {
                            subList = orders.subList(startIndex, length);
                        }
                        size -= eachSize;
                        if(log.isDebugEnabled()) {
                            log.debug("当前未包含组合数据:{}", JSON.toJSONString(subList));
                        }
                        String mergeMsg = mergeOrder(subList, eachSize, typeEnum, user, is2CDetention);
                        if (StringUtils.isNotBlank(mergeMsg)) {
                            sb.append(mergeMsg);
                        }
                    }
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("当前没有数量大于1的数据:{}", JSON.toJSONString(orders));
                    }
                    if (size > eachSize) {
                        subList = orders.subList(startIndex, startIndex + eachSize);
                        startIndex += eachSize;
                    } else {
                        subList = orders.subList(startIndex, length);
                    }
                    size -= eachSize;
                    String mergeMsg = mergeOrder(subList, eachSize, typeEnum, user, is2CDetention);
                    if (StringUtils.isNotBlank(mergeMsg)) {
                        sb.append(mergeMsg);
                    }
                }
            }
        }
        return sb.toString();
    }


    /**
     * 合并操作
     *
     * @param orders        预合并订单
     * @param user          操作用户
     * @param is2CDetention 是否2C待寻源卡单的订单合并
     */
    private String mergeOrder(List<OcBOrder> orders, int limitSize, MergeTypeEnum typeEnum, User user, boolean is2CDetention) {
        String suffixInfo = "";
        List<Long> orderIds = null;
        String cryptCode = orders.get(0).getOrderEncryptionCode();
        String code = "";
        try {
            orderIds = orders.stream().map(OcBOrder::getId).collect(Collectors.toList());
            if (orderIds.size() < MIN_UNIT) {
                return null;
            }
            if (PlatFormEnum.VIP_JITX.getCode().equals(orders.get(0).getPlatform())) {
                int sum = orders.stream().mapToInt(x -> x.getQtyAll().intValue()).sum();
                if (sum > limitSize) {
                    return "JitX订单合并超过合单数量限制";
                }
            }

            // 1. 合并
            code = "构建单据";
            OcBOrderRelation orderRelation = orderMergeService.buildMergeOrderInfo(orders, orderIds, user, is2CDetention);
            suffixInfo = orderRelation.getOrderInfo().getSuffixInfo();

            // 2. 落库
            code = "更新数据";
            ownMergeOrderService.insertOrderAndInvokeSgSrv(orders, orderRelation, typeEnum.code(), user, is2CDetention);

            // 3. 分物流
            if (!is2CDetention) {
                code = "分物流";
                orderMergeService.distributeLogistics(orderRelation, user, orders.get(0));
            }
            code = "";
        } catch (Exception ex) {
            String errorMsg = getSubExceptionMsg(ex, 200);
            suffixInfo = StringUtils.isBlank(suffixInfo) ? "(" + JSON.toJSONString(orderIds) + ")" : suffixInfo;
            String logMsg = typeEnum.code() + "订单编号:" + suffixInfo + errorMsg;

            for (OcBOrder order : orders) {
                try {
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                            OrderLogTypeEnum.ORDER_MERGE.getKey(), logMsg, "", "", user);
                } catch (Exception e) {
                    log.error(LogUtil.format("合单,订单新增日志异常,error:{}"), Throwables.getStackTraceAsString(e));
                }
            }
            log.error(LogUtil.format("MergeOrderExpInfo, 异常:{},cryptCode/标识位/编号集= ", cryptCode, code, orderIds),
                    Throwables.getStackTraceAsString(ex));
            if (MergeTypeEnum.MANUAL == typeEnum) {
                throw new NDSException("合单异常.订单编号:" + JSON.toJSONString(orderIds) + " 信息: " + errorMsg);
            }
            return logMsg;
        }
        return null;
    }

    /**
     * 异常信息截取
     *
     * @param ex 异常信息
     * @return 异常部分信息
     */
    private String getSubExceptionMsg(Exception ex, int len) {
        String expMsg = "";
        if (ex.getMessage() != null) {
            expMsg += ex.getMessage().length() > len ? ex.getMessage().substring(0, len) : ex.getMessage();
        }
        return expMsg;
    }


    /**
     * 等值判断
     *
     * @param o1 参一
     * @param o2 参二
     * @return true: 不相等
     */
    private boolean bothNe(Object o1, Object o2) {
        if (o1 == null) {
            return o2 != null;
        }
        return !o1.equals(o2);
    }

    /**
     * 等值判断
     *
     * @param s1 参一
     * @param s2 参二
     * @return 不相等
     */
    private boolean ne(String s1, String s2) {
        s1 = s1 == null ? "" : s1.trim();
        s2 = s2 == null ? "" : s2.trim();
        return !s1.equalsIgnoreCase(s2);
    }

    /**
     * 合单.Consume
     */
    class MergeOrderCallable implements Callable<Integer> {

        private final Map.Entry<String, List<Long>> ele;
        private final Map<Long, StCMergeOrderDO> mergeStMap;
        private final int limitQty;
        private final User user;
        private final Map<String, String> logMap;

        MergeOrderCallable(Map.Entry<String, List<Long>> entry, Map<Long, StCMergeOrderDO> mgSt, int limit, Map<String,
                String> logMap, User usr) {
            this.ele = entry;
            this.mergeStMap = mgSt;
            this.limitQty = limit;
            this.logMap = logMap;
            this.user = usr;
        }

        @Override
        public Integer call() {
            String name = Thread.currentThread().getName().substring(30);
            long l = System.currentTimeMillis();
            String msg = autoMergeEachGroup(ele.getKey(), ele.getValue(), mergeStMap, limitQty, user);
            if (StringUtils.isNotBlank(msg)) {
                logMap.put(ele.getKey(), name + "=" + JSON.toJSONString(ele.getValue()) + " :: " + msg);
                return ResultCode.FAIL;
            }
            logMap.put(ele.getKey(), name + "=" + JSON.toJSONString(ele.getValue()) + " CT: "
                    + (System.currentTimeMillis() - l));
            return ResultCode.SUCCESS;
        }

    }

    /**
     * MQ发送
     *
     * @param encryptCodes 合单加密串集
     * @param param        mq 发送
     */
    private void sendMqHandle(List<String> encryptCodes, MergeParam param) {

        long expire = 10 * 60 * 1000L;
        long now = System.currentTimeMillis();
        Map<String, Long> filterMap = new HashMap<>();
        try {
            BoundHashOperations<String, String, Long> hashOps = RedisCacheUtil.ops(redisMergeKey);
            Map<String, Long> entries = hashOps.entries();
            if (entries != null) {
                filterMap = entries;
            }
            int tmpNum = 0;
            int sendNum = 0;
            int validNum = 0;
            int size = encryptCodes.size();
            int idx = size - 1;
            List<String> sendData = new ArrayList<>();
            for (int n = 0; n < size; n++) {
                String encryptCode = encryptCodes.get(n);
                if (filterMap.containsKey(encryptCode)) {
                    Long keyTime = filterMap.get(encryptCode);
                    if ((now - keyTime) < expire) {
                        continue;
                    }
                }
                tmpNum++;
                sendData.add(encryptCode);
                if (tmpNum % param.getQtyEachMq() == 0) {
                    boolean sendResult = sendMq(sendData, param);
                    if (sendResult) {
                        RedisCacheUtil.batchAdd(hashOps, sendData, now);
                        sendNum++;
                        validNum += param.getQtyEachMq();
                    }
                    tmpNum = 0;
                    sendData = new ArrayList<>();
                    continue;
                }
                if (n == idx) {
                    boolean sendResult = sendMq(sendData, param);
                    if (sendResult) {
                        RedisCacheUtil.batchAdd(hashOps, sendData, now);
                        validNum = tmpNum + (sendNum * param.getQtyEachMq());
                        sendNum++;
                    }
                }
            }
            param.validQtyRiseOf(validNum).sendQtyRiseOf(sendNum).setCsmTime(System.currentTimeMillis() - now);

        } catch (Exception ex) {
            log.error(LogUtil.format("AutoMergeOrderMq.Producer.SendMQ.Exception: {}"),
                    Throwables.getStackTraceAsString(ex));
            String msg = ex.getMessage().length() > 200 ? ex.getMessage().substring(0, 200) : ex.getMessage();
            param.setMsgId("SendMQExp").setMessage(msg);
        }
    }

    /**
     * mq发送
     *
     * @param sendData 加密串
     * @return 发送结果
     */
    private boolean sendMq(List<String> sendData, MergeParam param) {
        try {
            String message = JSON.toJSONString(sendData);
//            String msgId = r3MqSendHelper.sendMessage(message, param.getTopic(), param.getTag());

            // 15天内未触发
            MqSendResult result = defaultProducerSend.sendTopic(param.getTopic(), param.getTag(), message, null);

            param.setMsgId(result.getMessageId()).setMessage(message);
            return true;
        } catch (Exception e) {
            log.error(LogUtil.format("AutoMergeOrderMq.Producer.SendMQ.SendException: {}, Codes:{},Topic/Tag="
                    , param.getTopic(), param.getTag()), Throwables.getStackTraceAsString(e), sendData);
        }
        return false;
    }

    /**
     * 构建二级索引查询参数
     *
     * @param encryptCodes 加密串
     * @param shopMergeSt  店铺自动合单策略
     * @return 查询参数
     */
    private MergeParam buildGsiSearchParam(List<String> encryptCodes, Map<Long, StCMergeOrderDO> shopMergeSt) {
        MergeParam param = new MergeParam();
        Set<Long> keySet = shopMergeSt.keySet();
        StringBuilder sb = new StringBuilder();
        for (Long id : keySet) {
            sb.append(",").append(id);
        }
        String shopKeys = sb.substring(1);
        param.setShopKeys(shopKeys);

        sb = new StringBuilder();
        for (String code : encryptCodes) {
            sb.append(",").append("'").append(code).append("'");
        }
        String codeKes = sb.substring(1);
        param.setEncryptCodes(codeKes);
        return param;
    }


    /**
     * 清楚Redis待合单key
     *
     * @param encryptCodes 合单加密码串
     */
    private void clearRedisMergeKey(List<String> encryptCodes) {
        BoundHashOperations<String, String, Long> hashOps = RedisCacheUtil.ops(redisMergeKey);
        RedisCacheUtil.batchHashDel(hashOps, encryptCodes);
    }


    /**
     * 查询开启自动合单策略的店铺
     *
     * @return 店铺策略
     */
    private Map<Long, StCMergeOrderDO> getShopMergeSt() {
        List<StCMergeOrderDO> mergeStrategies = stRpcService.queryAllMergeShop();
        if (CollectionUtils.isEmpty(mergeStrategies)) {
            return null;
        }
        Map<Long, StCMergeOrderDO> mergeStMap = mergeStrategies.stream()
                .filter(e -> !(e.getIsAutomerge() == null || e.getIsAutomerge() == 0))
                .collect(Collectors.toMap(StCMergeOrderDO::getCpCShopId, e -> e, (e1, e2) -> e2));
        return mergeStMap;
    }

    /**
     * 索引.查询订单编号
     *
     * @param taskParam 二级索引查询参数
     * @return 加密码, 编号集
     */
    private Map<String, List<Long>> searchIdsByEncryptCodesOnGSI(MergeParam taskParam) {
        List<EncryptCodeGSI> idCodes = ocBOrderMapper.listIdsByEncryptCodes(taskParam);
        if (CollectionUtils.isEmpty(idCodes)) {
            return null;
        }
        return idCodes.parallelStream().collect(Collectors.groupingBy(EncryptCodeGSI::getOrderEncryptionCode,
                Collectors.mapping(EncryptCodeGSI::getId, Collectors.toList()))
        );
    }


}
