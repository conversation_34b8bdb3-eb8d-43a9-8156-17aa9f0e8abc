package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.StandplatSendCallUpdateOrderModel;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Auther: chenhao
 * @Date: 2022-08-19 11:37
 * @Description:
 */

@Slf4j
@Component
public class StandplatSendCallUpdateOrderServcice {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OmsOrderPlatformDeliveryService omsOrderPlatformDeliveryService;

    @Autowired
    private OmsOrderService omsOrderService;


    @Autowired
    private OmsOrderLogService omsOrderLogService;


    public void cosume(StandplatSendCallUpdateOrderModel message) {
        User user = SystemUserResource.getRootUser();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("StandplatSendCallUpdateOrderServcice监控: {}", "StandplatSendCallUpdateOrderServcice"), JSONObject.toJSONString(message));
            }
            boolean result = message.getIsSuccess();
            Long id = message.getId();
            String tid = message.getTid();
            String returnMessage = message.getReturnMessage();
            OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
            //新增日志信息
            String logMsg = "订单" + id + "(平台单号=" + tid + ")平台发货结果" + returnMessage;
            omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, null, ocBOrder.getForceSendFailReason(), user);
            if (result) {
                ocBOrder.setIsForce(1L);
                //更新订单主表状态推ES 并作废退单推es
                omsOrderPlatformDeliveryService.updateMasterOrderStatusPushES(ocBOrder, user);
            } else {
                String errorMessage = StringUtils.isEmpty(message.getErrorMessage()) ? "" : message.getErrorMessage();
                //失败打失败的标记 失败信息
                ocBOrder.setIsForce(0L);
                ocBOrder.setForceSendFailReason(errorMessage);
                omsOrderService.updateOrderInfo(ocBOrder);
                //调用主表更新失败次数接口
                ocBOrderMapper.updateMasterFailTimesById(ocBOrder.getId());
            }

        } catch (Exception e) {
            log.error(LogUtil.format("平台发货错误信息: {}", "StandplatSendCallUpdateOrderServcice"), Throwables.getStackTraceAsString(e));

        }
    }
}
