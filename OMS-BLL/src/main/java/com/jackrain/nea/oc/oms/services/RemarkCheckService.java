package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 李杰
 * @since: 2019/3/15
 * create at : 2019/3/15 10:38
 */
@Slf4j
@Component
public class RemarkCheckService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    /**
     * @param ids ids
     * @return JSONObject
     */
    public JSONObject check(String ids) {
        JSONObject obj = new JSONObject();
        String[] idArrays = ids.split(",");
        for (int i = 0; i < idArrays.length; i++) {
            Long id = Long.valueOf(idArrays[i]);
            OcBOrder ocBorder = ocBOrderMapper.selectById(id); //获取原订单信息
            if (ocBorder == null) {
                obj.put("code", ResultCode.FAIL);
                obj.put("message", "订单不存在");
                return obj;
            }
            Integer orderStatus = ocBorder.getOrderStatus();
            //已取消或系统作废
            if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_CANCELLED.getVal()
                    || orderStatus.intValue() == OcOrderCheckBoxEnum.CHECKBOX_SYSTEM_INVALIDATION.getVal()) {
                obj.put("code", ResultCode.FAIL);
                obj.put("message", id + "订单当前状态异常，不允许修改备注！");
                return obj;
            }
        }
        obj.put("code", ResultCode.SUCCESS);
        obj.put("message", "成功");
        return obj;
    }
}
