package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.service.IpOrderCancelToAgService;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.gsi.GSI4OrderItem;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongCancelRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.refund.JdReturnUtil;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.oms.util.OmsDeliveredRefundFormUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 中间表京东取消订单转单服务
 * @author: 郑小龙
 * @date: 2020-06-01 11:54
 **/
@Component
@Slf4j
public class IpJingdongSaRefundService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private IpBJingdongSaRefundMapper saRefundMapper;
    @Autowired
    private IpBJingdongOrderMapper ipBJingdongOrderMapper;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;
    @Autowired
    private MarkRefundService markRefundService;
    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;
    @Autowired
    private OcBReturnOrderBatchAddService batchAddService;
    @Autowired
    private IpOrderCancelToAgService toAgService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OmsReturnUtil omsReturnUtil;
    @Autowired
    private OcBReturnBfSendMapper ocBReturnBfSendMapper;
    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private OmsDeliveredRefundFormUtil omsDeliveredRefundFormUtil;

    /**
     * 京东取消订单 转单 处理逻辑
     *
     * @param orderInfo
     * @param user
     */
    public void jdCancelOrderRefund(IpJingdongCancelRelation orderInfo, User user) {
        if (log.isDebugEnabled()) {
            log.debug(" IpBJingDongSaRefundService.jdCancelOrderRefund orderInfo={}", JSONObject.toJSONString(orderInfo));
        }
        IpBJingdongSaRefund saRefund = orderInfo.getJingdongSaRefund();
        IpBJingdongOrder jingdongOrder = orderInfo.getJingdongOrder();
        String orderState = jingdongOrder.getOrderState();
        List<Long> ids = orderInfo.getOcBOrder().stream().map(OcBOrder::getId).collect(Collectors.toList());
        //【京东取消订单接口】 状态为 "0未审核"
        if (saRefund.getStatus().equals(JingDongSaRefundStatus.NOAUDIT.toInteger())) {
            //京东订单状态为"锁定"
            //locked(saRefund, ids, user);
            //未审核状态不生成退换货单，更新零售发货单退款中
            noAudit(saRefund, ids, user);
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK17, saRefund);
        }
        //【京东取消订单接口】 状态为 1审核通过、3京东审核通过、5人工审核通过、7青龙拦截成功、9强制关单并退款
        else if (saRefund.getStatus().equals(JingDongSaRefundStatus.AUDITPASS.toInteger())
                || saRefund.getStatus().equals(JingDongSaRefundStatus.JDFINANCEAUDITPASS.toInteger())
                || saRefund.getStatus().equals(JingDongSaRefundStatus.ARTIFICIALAUDITPASS.toInteger())
                || saRefund.getStatus().equals(JingDongSaRefundStatus.QLINTERCEPTSUCC.toInteger())
                || saRefund.getStatus().equals(JingDongSaRefundStatus.FORCIBLYOFFSINGLEREFUND.toInteger())
                || saRefund.getStatus().equals(JingDongSaRefundStatus.INTERCEPTREFUND.toInteger())) {
            tradeCanceled(saRefund, ids, user);
        }else if(saRefund.getStatus().equals(JingDongSaRefundStatus.NOAUDITPASS.toInteger())
                || saRefund.getStatus().equals(JingDongSaRefundStatus.NOJDFINANCEAUDITPASS.toInteger())
                || saRefund.getStatus().equals(JingDongSaRefundStatus.QLINTERCEPTFAIL.toInteger())){
            //审核不通过 2，京东审核不通过4，青龙拦截失败8
            auditFail(saRefund, ids, user);
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK17, saRefund);
        }
        //京东取消订单接口】 状态为 11用户撤销
        else if (saRefund.getStatus().equals(JingDongSaRefundStatus.USERREVOKED.toInteger())) {
            waitSellerStockOut(saRefund, ids, user);
        }
        // //京东取消订单接口】 状态为 其他状态
        else {
            IpJingdongSaRefundService bean = ApplicationContextHandle.getBean(IpJingdongSaRefundService.class);
            //单据状态 其他状态 标记 已转换  备注：买家申请退款转换成功;
            bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK17, saRefund);
        }
    }


    /**
     * 审核状态为未审核
     * @param saRefund
     * @param orderIds
     * @param user
     */
    private void noAudit(IpBJingdongSaRefund saRefund, List<Long> orderIds, User user) {
        String tid = saRefund.getOrderid();
        for (int i = 0; i < orderIds.size(); i++) {
            Long orderId = orderIds.get(i);
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                    Integer orderStatus = ocBOrder.getOrderStatus();
                    //订单状态 为 "已取消"、"系统作废"
                    if (orderStatus.equals(OmsOrderStatus.CANCELLED.toInteger())
                            || orderStatus.equals(OmsOrderStatus.SYS_VOID.toInteger())) {
                        continue;
                    }
                    updateOrderAndItem(tid, orderId, user);
                } else {
                    throw new NDSException("当前订单其他人在操作,请稍后再试！");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("IpBJingDongSaRefundService.noAudit异常，订单id=={}",
                        "IpBJingDongSaRefundService"), orderId);
                throw new NDSException(ex.getMessage());
            } finally {
                redisLock.unlock();
            }
        }
    }

    /**
     * 审核状态为审核不通过
     * @param saRefund
     * @param orderIds
     * @param user
     */
    private void auditFail(IpBJingdongSaRefund saRefund, List<Long> orderIds, User user) {
        String tid = saRefund.getOrderid();
        for (int i = 0; i < orderIds.size(); i++) {
            Long orderId = orderIds.get(i);
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                    Integer orderStatus = ocBOrder.getOrderStatus();
                    //订单状态 为 "已取消"、"系统作废"
                    if (orderStatus.equals(OmsOrderStatus.CANCELLED.toInteger())
                            || orderStatus.equals(OmsOrderStatus.SYS_VOID.toInteger())) {
                        continue;
                    }
                    updateOrderItem(tid, orderId,ocBOrder.getBillNo(), user);
                } else {
                    throw new NDSException("当前订单其他人在操作,请稍后再试！");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("IpBJingDongSaRefundService.auditFail异常，订单id=={}",
                        "IpBJingDongSaRefundService"), orderId);
                throw new NDSException(ex.getMessage());
            } finally {
                redisLock.unlock();
            }
        }
    }



    /**
     * 交易状态为LOCKED的处理方法
     *
     * @param saRefund 接口平台订单表
     * @param orderIds 全渠道订单集
     */
    private void locked(IpBJingdongSaRefund saRefund, List<Long> orderIds, User user) {
        Long shopid = null;
        String tid = saRefund.getOrderid();
        int retractNum = 0;//撤回失败记录
        int statusNum = 0;//"已审核"、"待分配"、"传WMS中" 记录
        int closeNum = 0;//"已取消"、"系统作废" 记录
        int succNum = 0;//"待审核"、"缺货" 或者（配货中且WMS撤回状态为已撤回） 记录
        int redisNum = 0;//其他人操作记录
        int errNum = 0;//转单异常
        int rtnNum = 0;//生成退单 记录
        for (int i = 0; i < orderIds.size(); i++) {
            Long orderId = orderIds.get(i);
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                    shopid = ocBOrder.getCpCShopId();
                    Integer orderStatus = ocBOrder.getOrderStatus();

                    //订单状态 为 "仓库发货"、"平台发货"、"交易完成"、"物流已送达"
                    if (orderStatus.equals(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())
                            || orderStatus.equals(OmsOrderStatus.PLATFORM_DELIVERY.toInteger())
                            || orderStatus.equals(OmsOrderStatus.DEAL_DONE.toInteger())
                            || orderStatus.equals(OmsOrderStatus.DELIVERED.toInteger())) {
                        //京东发货后退款，生成退换货订单服务
                        insertRefundOrder(orderId, ocBOrder.getBillNo(), tid, user, saRefund);
                        rtnNum++;
                    } else {
                        updateOrderAndItem(tid, orderId, user);
                    }
                    //订单状态 为 "已审核"、"待分配"、"传WMS中",不拦截
                    if (log.isDebugEnabled()) {
                        log.debug(" 订单id=" + orderId + ",IpBJingDongSaRefundService.locked ocBOrder=" + JSONObject.toJSONString(ocBOrder));
                    }
                    //订单状态”为配货中且”WMS撤回状态”不是已撤回，则调用WMS撤回服务
                    if (orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger())
                            || orderStatus.equals(OmsOrderStatus.CHECKED.toInteger())) {
                        //调用作废出库通知单并从wms撤回接口
                        boolean flag = this.toExamineOrder(ocBOrder, user);
                        if (log.isDebugEnabled()) {
                            log.debug(" 订单id=" + orderId + ",IpBJingDongSaRefundService.locked 反审核返回flag" + flag + ",订单=" + JSONObject.toJSONString(ocBOrder));
                        }
                        if (flag) {
                            orderStatus = OmsOrderStatus.UNCONFIRMED.toInteger();
                        } else {
                            retractNum++;
                        }
                    }
                    //订单状态 "待审核"、"缺货" 或者（配货中且WMS撤回状态为已撤回）
                    if (orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger())
                            || orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())) {
                        //更新订单明细的退款状态,根据平台单号将明细表中同平台单号的明细退款状态修改为1
                        //订单主表更新,是否退款中”：退款中
                        succNum++;
                    }
                    //订单状态 为 "已审核"、"待分配"、"传WMS中"
                    if (orderStatus.equals(OmsOrderStatus.TO_BE_ASSIGNED.toInteger())
                            || orderStatus.equals(OmsOrderStatus.PENDING_WMS.toInteger())) {
                        statusNum++;
                    }

                    //订单状态 为 "已取消"、"系统作废"
                    if (orderStatus.equals(OmsOrderStatus.CANCELLED.toInteger())
                            || orderStatus.equals(OmsOrderStatus.SYS_VOID.toInteger())) {
                        closeNum++;
                    }
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug(" 订单id=" + orderId + ",IpBJingDongSaRefundService.locked 当前订单其他人在操作!");
                    }
                    redisNum++;
                }
            } catch (Exception ex) {
                errNum++;
                log.error(" 订单id=" + orderId + ",IpBJingDongSaRefundService.locked 异常：" + ex);
            } finally {
                redisLock.unlock();
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(" IpBJingDongSaRefundService.locked errNum=" + errNum + ",redisNum=" + redisNum + ",retractNum="
                    + retractNum + ",statusNum=" + statusNum + ",rtnNum=" + rtnNum + ",closeNum="
                    + closeNum + ",succNum=" + succNum + ",size=" + orderIds.size());
        }
        if (errNum > 0) {
            //转单保存异常，标记 未转换     备注：系统异常,转换失败
            this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK0, saRefund);
            return;
        }
        if (redisNum > 0) {
            //有redis锁，其他人在操作，标记 未转换     备注：全渠道订单其他人正在在操作，请等待下次转换
            this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK61, saRefund);
            return;
        }
        if (retractNum > 0) {
            //wms撤回失败，标记 未转换        备注：WMS撤回失败,请等待下次转换
            this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK45, saRefund);
            return;
        }
        if (statusNum > 0) {
            //有已审核、待分配、传wms中，标记 未转换     备注：订单状态为已审核、待分配或传WMS中，请等待下次转换
            this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK60, saRefund);
            return;
        }
        if (rtnNum > 0) {
            //有仓库发货、平台发货、交易完成、物流已送达，标记 已转换 备注：原订单已出库，需生成退货单，订单退款转换失败
            //调用【京东取消订单更新出库状态接口】，回传出库状态(1:未出库,2:已出库) =2:已出库
            updateWarehouseStatus(saRefund, SysNotesConstant.SYS_REMARK53, 2, shopid);
            return;
        }
        if (succNum > 0) {
            //全部为 "待审核"、"缺货" 或者（配货中且WMS撤回状态为已撤回），标记 已转换  备注：买家申请退款，转换完成
            //调用【京东取消订单更新出库状态接口】，回传出库状态(1:未出库,2:已出库) =1:未出库
            updateWarehouseStatus(saRefund, SysNotesConstant.SYS_REMARK17, 1, shopid);
        }
        if (closeNum > 0 && closeNum == orderIds.size()) {
            //全部为 已取消、系统作废,标记 已转换     备注：原订单已经取消或作废
            //调用【京东取消订单更新出库状态接口】，回传出库状态(1:未出库,2:已出库) =1:未出库
            updateWarehouseStatus(saRefund, SysNotesConstant.SYS_REMARK36, 1, shopid);
        }
    }

    /**
     * 交易状态为TRADE_CANCELED的处理方法
     *
     * @param saRefund 接口平台订单表
     * @param orderIds 全渠道订单集
     * @param user     用户
     */
    private void tradeCanceled(IpBJingdongSaRefund saRefund, List<Long> orderIds, User user) {
        if (log.isDebugEnabled()) {
            log.debug(" IpBJingDongSaRefundService.tradeCanceled orderIds=" + JSONObject.toJSONString(orderIds)
                    + ",saRefund=" + JSONObject.toJSONString(saRefund));
        }
        String tid = saRefund.getOrderid();
        int retractNum = 0;//撤回失败记录
        int statusNum = 0;//"已审核"、"待分配"、"传WMS中" 记录
        int closeNum = 0;//"已取消"、"系统作废" 记录
        int refundErrNum = 0;//标记为退款完成服务失败 记录
        int errNum = 0;//转单异常
        int rtnNum = 0;//生成退单 记录
        int cancelNum = 0;
        OcBOrder ocBOrder = null;
        for (int i = 0; i < orderIds.size(); i++) {
            Long orderId = orderIds.get(i);
            try {
                ocBOrder = ocBOrderMapper.selectById(orderId);
                Integer orderStatus = ocBOrder.getOrderStatus();

                //订单状态 为 "仓库发货"、"平台发货"、"交易完成"、"物流已送达"
                if (orderStatus.equals(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())
                        || orderStatus.equals(OmsOrderStatus.PLATFORM_DELIVERY.toInteger())
                        || orderStatus.equals(OmsOrderStatus.DEAL_DONE.toInteger())
                        || orderStatus.equals(OmsOrderStatus.DELIVERED.toInteger())) {
                    //新增退换货订单服务
                    insertRefundOrder(orderId, ocBOrder.getBillNo(), tid, user, saRefund);
                    rtnNum++;
                } else {
                    updateOrderAndItem(tid, orderId, user);
                }
                if (log.isDebugEnabled()) {
                    log.debug(" 订单id=" + orderId + ",IpBJingDongSaRefundService.tradeCanceled ocBOrder=" + JSONObject.toJSONString(ocBOrder));
                }
                //订单状态”为配货中且”WMS撤回状态”不是已撤回，则调用WMS撤回服务
                if (orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger())
                        || orderStatus.equals(OmsOrderStatus.CHECKED.toInteger())) {
                    //调用作废出库通知单并从wms撤回接口
                    boolean flag = this.lockUpBackExamine(ocBOrder, user);
                    if (log.isDebugEnabled()) {
                        log.debug(" 订单id=" + orderId + ",IpBJingDongSaRefundService.tradeCanceled 反审核返回flag" + flag + ",订单=" + JSONObject.toJSONString(ocBOrder));
                    }
                    if (flag) {
                        orderStatus = OmsOrderStatus.UNCONFIRMED.toInteger();
                    } else {
                        retractNum++;
                    }
                }
                //订单状态 "待审核"、"缺货" 或者（配货中且WMS撤回状态为已撤回）
                if (orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())) {
                    //调用 标记为退款完成服务
                    List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemListOccupy(orderId);
                    ocBOrderItems = ocBOrderItems.stream().filter(p -> (p.getProType() == SkuType.NO_SPLIT_COMBINE || p.getProType() == SkuType.NORMAL_PRODUCT)
                            && p.getTid().equals(tid)).collect(Collectors.toList());
                    List<Long> itemIds = ocBOrderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                    boolean issucc = omsReturnUtil.signRefundNew(ocBOrder.getId(), itemIds, user, OrderHoldReasonEnum.REFUND_HOLD);
                    if (issucc) {
                        //更新主表数据,是否已经拦截”：0、“是否退款中”：0
                        OcBOrder updateOrder = new OcBOrder();
                        updateOrder.setId(orderId);
                        updateOrder.setIsInreturning(InreturningStatus.INRETURN_NO);
                        returnOrderTransferUtil.updateOperator(updateOrder, user);
                        ocBOrderMapper.updateById(updateOrder);

//                        //订单hold 或释放hold单调用HOLD单接口
//                        updateOrder.setIsInterecept(OmsOrderIsInterceptEnum.NO.getVal());
                        ocBOrderHoldService.holdOrUnHoldOrder(updateOrder, OrderHoldReasonEnum.REFUND_HOLD);
//                        orderStatus = OmsOrderStatus.CANCELLED.toInteger();

                        cancelNum++;
                    } else {
                        refundErrNum++;
                    }
                }
                //订单状态 为 "已审核"、"待分配"、"传WMS中"
                if (orderStatus.equals(OmsOrderStatus.TO_BE_ASSIGNED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.PENDING_WMS.toInteger())) {
                    statusNum++;
                }

                //订单状态 为 "已取消"、"系统作废"
                if (orderStatus.equals(OmsOrderStatus.CANCELLED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.SYS_VOID.toInteger())) {
                    closeNum++;
                }
            } catch (Exception ex) {
                errNum++;
                log.error(" 订单id=" + orderId + ",IpBJingDongSaRefundService.tradeCanceled 异常：" + ex);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(" IpBJingDongSaRefundService.tradeCanceled errNum=" + errNum + ",retractNum=" + retractNum
                    + ",statusNum=" + statusNum + ",rtnNum=" + rtnNum + ",closeNum=" + closeNum + ",refundErrNum=" + refundErrNum
                    + ",orderIds=" + orderIds.size());
        }
        if (cancelNum == orderIds.size()) {
            //todo 生成未发货退款单
            if (ocBOrder != null) {
                this.foundRefundFrontRefundOnly(ocBOrder, saRefund, user);
                omsReturnUtil.handleRefundComplete(ocBOrder);
            }
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK65, saRefund);
            return;
        }
        if (errNum > 0) {
            //转单保存异常，标记 未转换     备注：系统异常,转换失败
            this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK0, saRefund);
            return;
        }
        if (retractNum > 0) {
            //wms撤回失败，标记 未转换     备注：WMS撤回失败,请等待下次转换
            this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK45, saRefund);
            return;
        }
        if (statusNum > 0) {
            //有已审核、待分配、传wms中，标记 未转换     备注：订单状态为已审核、待分配或传WMS中，请等待下次转换
            this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK60, saRefund);
            return;
        }
        if (refundErrNum > 0) {
            //执行标记退款服务失败，标记 未转换     备注：标记退款完成服务失败，请等待下次转换
            int markRefundCount = saRefund.getMarkRefundCount() == null ? 0 : saRefund.getMarkRefundCount();
            int istrans = TransferOrderStatus.NOT_TRANSFER.toInteger();
            String sysRemark = SysNotesConstant.SYS_REMARK62;
            if (markRefundCount >= 3) {
                //执行退款服务失败3次，标记 已转换     备注：执行退款完成服务失败3次，需人为操作，标记已转换
                istrans = TransferOrderStatus.TRANSFERRED.toInteger();
                sysRemark = SysNotesConstant.SYS_REMARK66;
            }
            saRefund.setMarkRefundCount(markRefundCount + 1);
            this.updateSaReturnOrder(istrans, sysRemark, saRefund);
            return;
        }
        if (rtnNum > 0) {
            //有仓库发货、平台发货、交易完成、物流已送达，标记 已转换     备注：订单已发货，待退货
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK65, saRefund);
            return;
        }
        if (closeNum > 0 && closeNum == orderIds.size()) {
            //全部为 已取消、系统作废,标记 已转换     备注：买家申请退款，转换完成
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK17, saRefund);
        }
    }

    /**
     * 交易状态为WAIT_SELLER_STOCK_OUT的处理方法
     *
     * @param saRefund 接口平台订单表
     * @param orderIds 全渠道订单集
     * @param user     用户
     */
    private void waitSellerStockOut(IpBJingdongSaRefund saRefund, List<Long> orderIds, User user) {
        if (log.isDebugEnabled()) {
            log.debug(" IpBJingDongSaRefundService.waitSellerStockOut orderIds=" + JSONObject.toJSONString(orderIds)
                    + ",saRefund=" + JSONObject.toJSONString(saRefund));
        }
        String tid = saRefund.getOrderid();
        int statusNum = 0;///"待审核"、"已审核"、"待分配"、"传WMS中"、"缺货" 或者（配货中且WMS撤回状态为已撤回） 记录
        int closeNum = 0;//"已取消"、"系统作废" 记录
        int refundFailNum = 0;//取消退单失败 记录
        int errNum = 0;//转单异常
        int succ = 0;
        for (int i = 0; i < orderIds.size(); i++) {
            Long orderId = orderIds.get(i);
            try {
                OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                Integer orderStatus = ocBOrder.getOrderStatus();
                Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
                if (log.isDebugEnabled()) {
                    log.debug(" 订单id=" + orderId + ",IpBJingDongSaRefundService.waitSellerStockOut ocBOrder=" + JSONObject.toJSONString(ocBOrder));
                }
                //订单状态 为 "仓库发货"、"平台发货"、"交易完成"、"物流已送达"
                if (orderStatus.equals(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())
                        || orderStatus.equals(OmsOrderStatus.PLATFORM_DELIVERY.toInteger())
                        || orderStatus.equals(OmsOrderStatus.DEAL_DONE.toInteger())
                        || orderStatus.equals(OmsOrderStatus.DELIVERED.toInteger())) {
                    boolean flag = this.cancelRefundOrder(tid, orderId, user, saRefund.getPopafsrefundapplyid());
                    succ++;
                    if (log.isDebugEnabled()) {
                        log.debug(" 订单id=" + orderId + ",IpBJingDongSaRefundService.waitSellerStockOut 查询退单并取消返回flag" + flag);
                    }
                    if (!flag) {
                        refundFailNum++;
                    }
                }
                //订单状态 "待审核"、"已审核"、"待分配"、"传WMS中"、"缺货" 或者（配货中且WMS撤回状态为已撤回）
                if (orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.CHECKED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.TO_BE_ASSIGNED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.PENDING_WMS.toInteger())
                        || orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())
                        || orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger())) {
                    //则更新订单管理明细商品退款状态更新为0，记录操作日志，
                    //判断是否存在退款状态为1的商品,不存在 取消拦截，订单退款状态更新为0，
                    updateOrderItem(tid, orderId, ocBOrder.getBillNo(), user);
                    statusNum++;
                }
                //订单状态 为 "已取消"、"系统作废"
                if (orderStatus.equals(OmsOrderStatus.CANCELLED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.SYS_VOID.toInteger())) {
                    closeNum++;
                }
            } catch (Exception ex) {
                errNum++;
                log.error(" 订单id=" + orderId + ",IpBJingDongSaRefundService.waitSellerStockOut 异常：" + ex);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(" IpBJingDongSaRefundService.waitSellerStockOut errNum=" + errNum + ",refundFailNum=" + refundFailNum
                    + ",statusNum=" + statusNum + ",succ=" + succ + ",closeNum=" + closeNum + ",orderIds=" + orderIds.size());
        }
        if (errNum > 0) {
            //转单保存异常，标记 未转换     备注：系统异常,转换失败
            this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK0, saRefund);
            return;
        }
        if (refundFailNum > 0) {
            //有"仓库发货"、"平台发货"、"交易完成"、"物流已送达"  取消退单失败
            //标记 已转换     备注：退货单的状态不满足，不能取消退货单
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK63, saRefund);
            return;
        }
        if (statusNum > 0 || succ > 0) {
            //有"待审核"、"已审核"、"待分配"、"传WMS中"、"缺货" 或者（配货中且WMS撤回状态为已撤回）
            //有"仓库发货"、"平台发货"、"交易完成"、"物流已送达"  无退单或退单取消成功
            //标记 已转换     备注：转换成功
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK67, saRefund);
            return;
        }
        if (closeNum > 0) {
            //全部为 已取消、系统作废,标记 已转换     备注：订单已取消、系统作废，不能撤销
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK64, saRefund);
        }
    }

    /**
     * 调用执行标记退款服务
     *
     * @param tid
     * @param id  全渠道订单id
     */
    private boolean markRefund(String tid, Long id, User user) {
        List<Long> itemIds = ocBOrderItemMapper.queryIdByTidAndOrderId(tid, id);
        if (CollectionUtils.isEmpty(itemIds)) {
            return false;
        } else {
            String join = StringUtils.join(itemIds, ",");
            JSONObject ids = new JSONObject();
            ids.put("IDS", join);
            try {
                ValueHolderV14 v14 = markRefundService.markRefund(ids, user);
                if (log.isDebugEnabled()) {
                    log.debug("订单id=" + id + ",IpBJingDongSaRefundService.markRefund 调用执行标记退款服务返回" + JSONObject.toJSONString(v14));
                }
                if (!v14.isOK()) {
                    return false;
                }
            } catch (Exception e) {
                log.debug("订单id=" + id + ",IpBJingDongSaRefundService.markRefund 异常" + e.getMessage());
                return false;
            }
        }
        return true;
    }

    /**
     * 配货中加锁调用反审核
     *
     * @param ocBOrder
     * @param user
     */
    private Boolean lockUpBackExamine(OcBOrder ocBOrder, User user) {
        boolean isSuccess = true;
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                isSuccess = this.toExamineOrder(ocBOrder, user);
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(" 订单id=" + ocBOrder.getId() + ",IpBJingDongSaRefundService.lockUpBackExamine 当前订单其他人在操作!");
                }
                isSuccess = false;
            }
        } catch (Exception e) {
            log.debug(" 订单id=" + ocBOrder.getId() + ",IpBJingDongSaRefundService.lockUpBackExamine 异常" + e.getMessage());
            isSuccess = false;
        } finally {
            redisLock.unlock();
        }
        return isSuccess;
    }

    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    private boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            if (isSuccess) {
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用反审核失败", e);
            return false;
        }
    }


    /**
     * 调用新增退换货订单
     *
     * @param orderId
     * @param billNo
     * @param tid
     * @param user
     */
    private void insertRefundOrder(Long orderId, String billNo, String tid, User user, IpBJingdongSaRefund saRefund) {
        if (log.isDebugEnabled()) {
            log.debug(" IpBJingDongSaRefundService.insertRefundOrder tid=" + tid + ",orderId=" + orderId + ",billNo=" + billNo);
        }
        try {
            //奶卡订单只生成已发货退款单
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(orderId);
            boolean isMilkCardOrder = omsRefundOrderService.checkIsMilkCardOrder(orderItems);
            if(isMilkCardOrder){
                OcBOrder order = ocBOrderMapper.selectById(orderId);
                OcBReturnAfSendRelation ocBReturnAfSendRelation = omsDeliveredRefundFormUtil.jDCancelOrderAfSendToReturn(order,saRefund,user,orderItems);
                omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation,user);
                return;
            }
            //查询退换货订单是否存在，不存在，调用新增服务
            Long id = isExistReturnOrderByOrderIdAndTid(orderId, tid);
            if (null == id || id <= 0) {
                List<String> listString = new ArrayList<>();
                try {
                    id = batchAddService.addReturnOrder(orderId, 1, listString, user, "京东取消订单新增退换货单",tid,null);
                } catch (Exception e) {
                    log.error(this.getClass().getName() + " 订单id=" + orderId + ",京东取消订单,调用新增退单服务失败！异常:{}", e.getMessage());
                }
                if (listString.size() > 0) {
                    //新增退单添加订单操作日志
                    omsOrderLogService.addUserOrderLog(id, billNo, OrderLogTypeEnum.REFUND_ORDER_ADD.getKey(),
                            listString.get(0), null, null, user);
                }
            }
            if (id != null) {
                OcBOrder order = ocBOrderMapper.selectById(orderId);
                this.foundRefundSlipAfterJdCancel(id, order, saRefund, user);
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 订单id=" + orderId + ",京东取消订单新增退单失败！异常:{}", e.getMessage());
            throw new NDSException(e);
        }

    }

    /**
     * 取消退换货订单
     *
     * @param tid
     * @param orderId
     * @param user
     * @return
     */
    private Boolean cancelRefundOrder(String tid, Long orderId, User user, Long refundId) {
        if (log.isDebugEnabled()) {
            log.debug(" IpBJingDongSaRefundService.checkRefundDoesIsExist tid=" + tid + ",orderid=" + orderId);
        }
        try {
            Long id = isExistReturnOrderByOrderIdAndTid(orderId, tid);
            omsRefundOrderService.closedRefundSlip(refundId + "");
            if (id != null && id > 0) {
                //OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByid(id);
                List<Long> returnOrderIds = new ArrayList<>();
                returnOrderIds.add(id);
                omsRefundOrderService.refundOrderClose(returnOrderIds, null, null, user);

                return true;
            }
        } catch (Exception e) {
            log.debug(this.getClass().getName() + " 取消退换货单失败");
        }
        return false;
    }

    /**
     * 根据oid和tid查询退换货单id
     *
     * @param
     * @return
     */
    private Long isExistReturnOrderByOrderIdAndTid(Long orderid, String tid) {
        return ES4ReturnOrder.findIdByOrderIdAndTid(orderid, tid);
    }

    /**
     * 调用【京东取消订单更新出库状态接口】
     *
     * @param saRefund
     * @param status   回传出库状态(1:未出库,2:已出库)
     */
    private void updateWarehouseStatus(IpBJingdongSaRefund saRefund, String sysRemark, Integer status, Long shopid) {
        ValueHolderV14 v14 = toAgService.jdCancelUpdateWarehouseStatus(shopid, saRefund, status);
        saRefund.setOutboundvalue(status);
        saRefund.setReturnstatus(1);//默认成功
        if (!v14.isOK()) {
            sysRemark = SysNotesConstant.SYS_REMARK68;
            saRefund.setReturnstatus(2);//1：回传成功；2：回传失败
        }
        //调用【京东取消订单更新出库状态接口】 失败，标记 已转换， 备注：调用京东取消订单更新出库状态接口失败！
        this.updateSaReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), sysRemark, saRefund);
    }

    /**
     * 不存在原单
     */
    public boolean noOriginalOrder(IpBJingdongSaRefund saRefund, List<OcBOrder> ocBOrder) {
        IpJingdongSaRefundService bean = ApplicationContextHandle.getBean(IpJingdongSaRefundService.class);
        if (CollectionUtils.isEmpty(ocBOrder)) {
            Date date = new Date();
            //判断退单时间是否超过三天
            Date created = saRefund.getCreationdate();
            int day = Tools.getInt(AdParamUtil.getParam("oms.oc.order.transfer.jd.cancel.refund.day"), 3);
            Long threeDays = day * 24 * 60 * 60 * 1000L + created.getTime();
            if (threeDays < date.getTime()) {
                String remark = SysNotesConstant.SYS_REMARK2;

                bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), remark, saRefund);
                return true;
            }
            //找不到对应的原单,更新转换状态为 0 ,添加系统备注
            String remark = SysNotesConstant.SYS_REMARK1;
            bean.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), remark, saRefund);
            return true;
        }
        return false;
    }

    /**
     * 出现异常时更新状态
     *
     * @param saRefund
     * @return
     */
    public void updateSaRefundIsTransError(IpBJingdongSaRefund saRefund, String error) {
        String sysRemark = SysNotesConstant.SYS_REMARK0;
        //异常信息超过500 截取500
        String str = sysRemark + error;
        if (str.length() > 500) {
            str = str.substring(0, 500);
        }
        IpJingdongSaRefundService bean = ApplicationContextHandle.getBean(IpJingdongSaRefundService.class);
        bean.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), str, saRefund);
    }

    /**
     * 更新订单明细状态为0,并取消拦订单
     *
     * @param tid     接口平台单号
     * @param orderId 订单id
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderItem(String tid, Long orderId, String billno, User user) {
        try {
            //则更新订单管理明细商品退款状态更新为0，判断是否存在退款状态为1的商品,不存在 取消拦截，订单退款状态更新为0，记录操作日志
            ocBOrderItemMapper.updateRefundStatusByTidAndOrderId(OmsOrderRefundStatus.UNREFUND.toInteger(), Long.valueOf(tid), orderId);
            //订单主表更新,是否退款中”：退款中
            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setId(orderId);
            updateOrder.setIsInreturning(InreturningStatus.INRETURN_NO);
            returnOrderTransferUtil.updateOperator(updateOrder, user);
            ocBOrderMapper.updateById(updateOrder);
            //是否已经拦截 订单hold 或释放hold单调用HOLD单接口
            updateOrder.setIsInterecept(OmsOrderIsInterceptEnum.NO.getVal()); //订单hold 或释放hold单调用HOLD单接口
            ocBOrderHoldService.holdOrUnHoldOrder(updateOrder, OrderHoldReasonEnum.REFUND_HOLD);
        } catch (Exception e) {
            log.error(" {}订单更新明细退款状态为0,取消主表拦截失败！异常:{}", orderId, e.getMessage());
            throw new NDSException(e);
        }
    }

    /**
     * 更新订单明细状态为 WAIT_SELLER_AGREE(买家已经申请退款，等待卖家同意)
     *
     * @param tid     接口平台单号
     * @param orderId 订单id
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderAndItem(String tid, Long orderId, User user) {
        try {
            //更新订单明细的退款状态,根据平台单号将明细表中同平台单号的明细退款状态修改为1
            //TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode()
            ocBOrderItemMapper.updateRefundStatusByTidAndOrderId(OmsOrderRefundStatus.WAITSELLERAGREE.toInteger(), Long.valueOf(tid), orderId);
            //订单主表更新,是否退款中”：退款中
            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setId(orderId);
            updateOrder.setIsInreturning(InreturningStatus.INRETURNING);
            returnOrderTransferUtil.updateOperator(updateOrder, user);
            ocBOrderMapper.updateById(updateOrder);
            updateOrder.setIsInterecept(OmsOrderIsInterceptEnum.YES.getVal()); //订单hold 或释放hold单调用HOLD单接口
            ocBOrderHoldService.holdOrUnHoldOrder(updateOrder, OrderHoldReasonEnum.REFUND_HOLD);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 订单id=" + orderId + ",更新明细退款状态和主表是否退款中失败！异常:{}", e.getMessage());
            throw new NDSException(e);
        }
    }

    /**
     * 更新京东取消订单转单状态
     *
     * @param istrans  状态
     * @param remark   备注
     * @param saRefund 京东取消订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateReturnOrder(int istrans, String remark, IpBJingdongSaRefund saRefund) {
        try {
            JSONObject object = new JSONObject();
            object.put("popafsrefundapplyid", saRefund.getPopafsrefundapplyid());
            object.put("istrans", istrans);
            object.put("sysremark", remark);
            object.put("modifieddate", new Date());
            object.put("transdate", new Date());
            saRefundMapper.updateSaRefundOrder(object);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 退款单id=" + saRefund.getPopafsrefundapplyid() + "，京东取消订单更新转单失败,异常:{}", e.getMessage());
            throw new NDSException(e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSaReturnOrder(int istrans, String remark, IpBJingdongSaRefund saRefund) {
        try {
            JSONObject object = new JSONObject();
            object.put("popafsrefundapplyid", saRefund.getPopafsrefundapplyid());
            object.put("istrans", istrans);
            object.put("sysremark", remark);
            object.put("modifieddate", new Date());
            object.put("transdate", new Date());
            object.put("mark_refund_count", saRefund.getMarkRefundCount() == null ? 0 : saRefund.getMarkRefundCount());
            object.put("returnstatus", saRefund.getReturnstatus() == null ? 0 : saRefund.getReturnstatus());
            object.put("outboundvalue", saRefund.getOutboundvalue() == null ? 0 : saRefund.getOutboundvalue());
            saRefundMapper.updateSaRefundOrder(object);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 退款单id=" + saRefund.getPopafsrefundapplyid() + "，京东取消订单更新失败,异常:{}", e.getMessage());
            throw new NDSException(e);
        }
    }

    /**
     * 拦截订单并记录日志
     *
     * @param ocBOrder 全渠道订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void interceptAndLog(OcBOrder ocBOrder, User user) {
        try {
            OcBOrder newOrder = new OcBOrder();
            newOrder.setId(ocBOrder.getId());
            newOrder.setIsInterecept(OmsOrderIsInterceptEnum.YES.getVal()); //订单hold 或释放hold单调用HOLD单接口
            //订单hold 或释放hold单调用HOLD单接口
            ocBOrderHoldService.holdOrUnHoldOrder(newOrder, OrderHoldReasonEnum.REFUND_HOLD);
            /*returnOrderTransferUtil.updateOperator(newOrder, user);
            ocBOrderMapper.updateById(newOrder);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "订单拦截成功", "", "", user);*/
            ocBOrder.setIsInterecept(OmsOrderIsInterceptEnum.YES.getVal());
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 订单id=" + ocBOrder.getId() + ",更新拦截失败！异常:{}", e.getMessage());
            throw new NDSException(e);
        }
    }

    /**
     * 根据退款id查询京东取消订单等信息
     *
     * @param applyid
     * @return
     */
    public IpJingdongCancelRelation getJingdongCancelSaRefund(Long applyid) {
        IpJingdongSaRefundService bean = ApplicationContextHandle.getBean(IpJingdongSaRefundService.class);
        if (log.isDebugEnabled()) {
            log.debug(" IpJingdongSaRefundService.getJingdongCancelSaRefund.applyid:{}", applyid);
        }
        //京东取消订单中间表关系
        IpJingdongCancelRelation cancelRelation = new IpJingdongCancelRelation();
        try {
            IpBJingdongSaRefund saRefund = saRefundMapper.selectJingdongSaRefundByApplyId(applyid);
            if (null == saRefund || StringUtils.isEmpty(saRefund.getOrderid())) {
                return null;
            }

            String orderid = saRefund.getOrderid();
            IpBJingdongOrder jingdongOrder = ipBJingdongOrderMapper.selectJingdongOrderByOrderId(Long.valueOf(orderid));
            if (null == jingdongOrder) {
                Date date = new Date();
                //判断退单时间是否超过三天
                Date created = saRefund.getCreationdate();
                int day = Tools.getInt(AdParamUtil.getParam("oms.oc.order.transfer.jd.cancel.refund.day"), 1);
                Long threeDays = day * 24 * 60 * 60 * 1000L + created.getTime();
                if (threeDays < date.getTime()) {
                    bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK70, saRefund);
                    return null;
                }
                bean.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK69, saRefund);
                return null;
            }

            //    List<Long> orderIds = ES4Order.getIdsBySourceCode(orderid);
            //List<Long> orderIds = GSI4Order.getIdListBySourceCode(orderid);
            //List<Long> orderIds = GSI4OrderItem.selectOcBOrderItemByTid(orderid);
            List<OcBOrder> orderList = ocBOrderMapper.selectOcBOrderByTid(orderid);

            cancelRelation.setJingdongSaRefund(saRefund);
            cancelRelation.setJingdongOrder(jingdongOrder);
            //查询订单
            if (CollectionUtils.isNotEmpty(orderList)) {
                //List<OcBOrder> orderList = ocBOrderMapper.selectByIdsList(orderIds);
                orderList = orderList.stream().distinct().collect(Collectors.toList());
                cancelRelation.setOcBOrder(orderList);
            }
            if (log.isDebugEnabled()) {
                log.debug(" IpJingdongSaRefundService.getJingdongCancelSaRefund.cancelRelation:{}", JSONObject.toJSONString(cancelRelation));
            }
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug(" IpJingdongSaRefundService.error:{}", e.getMessage());
            }
        }
        return cancelRelation;
    }


    /**
     * 生成发货前退款单(仅退款)
     */
    public void foundRefundFrontRefundOnly(OcBOrder ocBOrder, IpBJingdongSaRefund ipBJingdongSaRefund, User user) {
        if (omsRefundOrderService.isRefundSlipBfExist(ipBJingdongSaRefund.getPopafsrefundapplyid() + "")) {
            return;
        }
        OcBReturnBfSend ocBReturnBfSend = new OcBReturnBfSend();
        ocBReturnBfSend.setId(ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCBRETURNBFSEND));
        ocBReturnBfSend.setCpCShopId(ocBOrder.getCpCShopId());
        ocBReturnBfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        ocBReturnBfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        ocBReturnBfSend.setTid(ipBJingdongSaRefund.getOrderid());
        ocBReturnBfSend.setSubBillNo(ipBJingdongSaRefund.getOrderid());
        ocBReturnBfSend.setTReturnId(ipBJingdongSaRefund.getPopafsrefundapplyid() + "");
        ocBReturnBfSend.setBuyerNick(ipBJingdongSaRefund.getBuyerid());
        ocBReturnBfSend.setAmtReturn(ocBOrder.getOrderAmt());
        ocBReturnBfSend.setReason(ipBJingdongSaRefund.getReason());
        ocBReturnBfSend.setTReturnStatus(JdReturnUtil.transTaobaoRefundStatus(ipBJingdongSaRefund.getStatus()));
        ocBReturnBfSend.setReturnApplyTime(ipBJingdongSaRefund.getApplytime());
        //退款说明
        //ocBReturnBfSend.setReturnExplain(ipBJingdongSaRefund.getRefunddesc());
        OperateUserUtils.saveOperator(ocBReturnBfSend, user);

        ocBReturnBfSendMapper.insert(ocBReturnBfSend);
    }


    /**
     * 京东退单生成已发货退款单
     *
     * @param returnId
     * @param ocBOrder
     * @param ipBJingdongSaRefund
     * @param user
     */
    public void foundRefundSlipAfterJdCancel(Long returnId, OcBOrder ocBOrder, IpBJingdongSaRefund ipBJingdongSaRefund,
                                             User user) {
        //获取所有的退单明细数据
        try {
            if (!omsRefundOrderService.isRefundSlipAfExist(ipBJingdongSaRefund.getPopafsrefundapplyid() + "")) {
                return;
            }
            OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByid(returnId);
            if (returnOrder != null) {
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
                List<OcBReturnOrderRefund> orderRefunds =
                        ocBReturnOrderRefundMapper.selectByOcOrderId(returnId);
                //获取发货单主表数据
                OcBReturnAfSendRelation ocBReturnAfSendRelation = omsDeliveredRefundFormUtil.jDCancelRefundAfSendToReturn(orderRefunds, ocBOrder,
                        ipBJingdongSaRefund, user, orderItems, returnOrder);
                omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation,user);
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 生成退款单异常", e);
            throw new NDSException(e);
        }

    }


}