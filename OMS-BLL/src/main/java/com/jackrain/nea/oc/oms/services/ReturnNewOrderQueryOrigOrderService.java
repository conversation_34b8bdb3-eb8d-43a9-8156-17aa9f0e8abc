package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.IsBackEnum;
import com.jackrain.nea.oc.oms.model.enums.ResultOrderReserveBigint03Enum;
import com.jackrain.nea.oc.oms.model.enums.ReturnOrderIsToWmsEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.nums.WmsControlWarehouse;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 退单
 *
 * @date 2019/9/16
 * @author: ming.fz
 */
@Component
@Slf4j
public class ReturnNewOrderQueryOrigOrderService {

    @Autowired
    OcBOrderMapper ocBorderMapper;

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private CpRpcService cpRpcService;

    /**
     * 退单新增勾选是否原退时，调用此服务 返回原始订单物流公司和原始物流单号
     *
     * @param param
     * @param user
     * @return
     */
    public ValueHolderV14<String> queryOrder(JSONObject param, User user) {
        ValueHolderV14<String> returnVh = new ValueHolderV14<>();
        Long id = param.getLong("id");
        if (id == null) {
            throw new NDSException(Resources.getMessage("入参原始订单为空！", user.getLocale()));
        }
        OcBOrder order = ocBorderMapper.selectByID(id);
        if (order == null) {
            return ValueHolderV14Utils.getFailValueHolder("未查询到有效数据");
        }
        JSONObject json = (JSONObject) JSONObject.toJSON(order);
        if (Objects.nonNull(order.getCpCPhyWarehouseId())) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(order.getCpCPhyWarehouseId());
            //判断是否存在原退实体仓ID
            if (Objects.nonNull(cpCPhyWarehouse)) {
                if (cpCPhyWarehouse.getOriginalReturnPhyWarehouseId() == null){
                    json.put("CP_C_PHY_WAREHOUSE_IN_ID", cpCPhyWarehouse.getId());
                    json.put("CP_C_PHY_WAREHOUSE_IN_ID_NAME", cpCPhyWarehouse.getEname());
                }else {
                    json.put("CP_C_PHY_WAREHOUSE_IN_ID", cpCPhyWarehouse.getOriginalReturnPhyWarehouseId());
                    json.put("CP_C_PHY_WAREHOUSE_IN_ID_NAME", cpCPhyWarehouse.getOriginalReturnPhyWarehouseEname());
                }

            }
        }

        returnVh.setData(json.toJSONString());
        returnVh.setCode(ResultCode.SUCCESS);
        returnVh.setMessage("成功");
        return returnVh;
    }

    /**
     * 退单更新为原退
     *
     * @param param
     * @param user
     * @return
     */
    @Transactional
    public ValueHolderV14<List> updateReturnBOrder(JSONObject param, User user) {
        ValueHolderV14<List> returnVh = new ValueHolderV14<>();
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null) {
            throw new NDSException(Resources.getMessage("入参退单为空！", user.getLocale()));
        }
        String substring = null;
        if (ids.size() > 1) {
            StringBuilder sbl = new StringBuilder();
            for (Object id : ids) {
                sbl.append(id);
                sbl.append(",");
            }
            substring = sbl.substring(0, sbl.lastIndexOf(","));
        } else {
            substring = ids.get(0).toString();
        }


        List<OcBReturnOrder> rOrders = ocBReturnOrderMapper.findByJoin(substring);
        int errorCount = 0;
        List<JSONObject> returnList = new ArrayList<>();
        for (OcBReturnOrder rOrder : rOrders) {
            JSONObject resultDate = new JSONObject();
            Long id = rOrder.getId();
            String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    errorCount = getErrorCount(user, errorCount, resultDate, rOrder);
                } else {
                    resultDate.put("id", id);
                    resultDate.put("error", "当前订单其他人在操作，请稍后再试!");
                    ++errorCount;
                }
            } catch (Exception ex){
                log.error("id={}异常={}", id, ex);
                resultDate.put("id", id);
                resultDate.put("error", ex.getMessage());
                ++errorCount;
            } finally {
                redisLock.unlock();
            }
            //去除为空的
            if (resultDate.getLong("id") != null) {
                returnList.add(resultDate);
            }
        }

        returnVh.setData(returnList);
        returnVh.setCode(ResultCode.SUCCESS);
        returnVh.setMessage("成功" + (rOrders.size() - errorCount) + " 失败" + errorCount);
        return returnVh;
    }

    /**
     * 修改退单
     *
     * @param user
     * @param errorCount
     * @param resultDate
     * @param rOrder
     * @return
     */
    private int getErrorCount(User user, int errorCount, JSONObject resultDate, OcBReturnOrder rOrder) {
        //退单id
        Long id = rOrder.getId();
        StringBuilder errorString = new StringBuilder();
        //原单id
        Long ocBOrderId = checkReturnOrder(rOrder, user, errorString);
        if (ocBOrderId == null) {
            resultDate.put("id", id);
            resultDate.put("error", errorString.toString());
            return ++errorCount;
        }

        //查原单
        OcBOrder ocBOrder = ocBorderMapper.selectByID(ocBOrderId);
        if (ocBOrder == null) {
            resultDate.put("id", id);
            resultDate.put("error", "没有原单！");
            return ++errorCount;
        }

        //更新退单
        boolean b = updateRorder(ocBOrder, id, user, resultDate);
        if (!b) {
            return ++errorCount;
        }
        return errorCount;
    }

    /**
     * 更新退单
     *
     * @param ocBOrder
     * @param id
     * @param user
     */
    private boolean updateRorder(OcBOrder ocBOrder, Long id, User user, JSONObject resultDate) {
        //更新状态
        OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
        //原退
        updateOcBReturnOrder.setIsBack(IsBackEnum.Y.getVal());
        //退回物流公司为原单退回物流公司
        updateOcBReturnOrder.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
        updateOcBReturnOrder.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
        updateOcBReturnOrder.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
        //退回物流单号为原单单号
        updateOcBReturnOrder.setLogisticsCode(ocBOrder.getExpresscode());
        //入库实体仓库为原始订单的“发货仓库”
        Long cpCPhyWarehouseId = ocBOrder.getCpCPhyWarehouseId();
        updateOcBReturnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouseId);
        updateOcBReturnOrder.setModifierename(user.getEname());
        updateOcBReturnOrder.setModifieddate(new Date(System.currentTimeMillis()));
        updateOcBReturnOrder.setId(id);
        //判断“入库实体仓库”的“WMS管控”如果为“是”，则更新退换货订单的“是否传WMS”为“是”，反之为“否”。
        if (cpCPhyWarehouseId != null) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseId);
            if (cpCPhyWarehouse != null) {
                int wmsControlWarehouse = cpCPhyWarehouse.getWmsControlWarehouse();
                if (wmsControlWarehouse == WmsControlWarehouse.IS_CONTROL) {
                    //是wms管控仓
                    updateOcBReturnOrder.setIsNeedToWms(ResultOrderReserveBigint03Enum.Y.getVal());
                } else {
                    updateOcBReturnOrder.setIsNeedToWms(ResultOrderReserveBigint03Enum.N.getVal());
                }
            } else {
                resultDate.put("id", id);
                resultDate.put("error", "原单获取实体仓为空！原单单号：" + ocBOrder.getId());
                return false;
            }
        } else {
            resultDate.put("id", id);
            resultDate.put("error", "原单实体仓为空！原单单号：" + ocBOrder.getId());
            return false;
        }
        int record1 = ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
        if (record1 < 1) {
            resultDate.put("id", id);
            resultDate.put("error", "退单更新失败！");
            return false;
        }
        //es推送
        pushEs(user, updateOcBReturnOrder);
        return true;
    }

    /**
     * es虚拟入库推送
     *
     * @param user
     * @param ocBReturnOrder
     */
    private void pushEs(User user, OcBReturnOrder ocBReturnOrder) {
        /*try {
            Boolean aBoolean = SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, ocBReturnOrderMapper.selectById(ocBReturnOrder.getId()), ocBReturnOrder.getId());
            if (!aBoolean) {
                throw new NDSException(Resources.getMessage("mfz - 退货单主表推送ES失败!", user.getLocale()));
            }
        } catch (Exception e) {
            throw new NDSException(Resources.getMessage("mfz - 退货单主表推送ES失败!", user.getLocale()));
        }*/
    }

    /**
     * 校验成功返回原始订单号
     *
     * @param rOrder
     * @param user
     * @return
     */
    private Long checkReturnOrder(OcBReturnOrder rOrder, User user, StringBuilder errorResult) {
        Integer isBack = rOrder.getIsBack();
        if (Objects.nonNull(isBack)) {
            if (IsBackEnum.Y.getVal() == isBack) {
                errorResult.append("非原退单子才可以原退！");
                return null;
            }
        }
        int returnStatus = rOrder.getReturnStatus();
        int isTowms = rOrder.getIsTowms();
        if (!(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal() == returnStatus
                && ReturnOrderIsToWmsEnum.RETURN_ORDER_IS_TO_WMS.getVal() == isTowms)) {
            errorResult.append("订单为“等待退货入库状态”且“传WMS状态”为未传入才允许修为“原退”");
            return null;
        }
        return rOrder.getOrigOrderId();
    }
}