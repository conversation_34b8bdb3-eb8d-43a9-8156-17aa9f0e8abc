package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.service.IpOrderCancelToAgService;
import com.jackrain.nea.oc.oms.es.ES4IpTaoBaoRefund;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.st.service.OmsOrderStCAutocheckService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标记退款完成
 *
 * @author: 夏继超
 * @since: 2020/2/12
 * create at : 2020/2/12 16:43
 */
@Slf4j
@Component
public class MarkRefundCompleteService {
    @Autowired
    private MarkRefundService markRefundService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderStCAutocheckService stCAutocheckService;
    @Autowired
    private IpBTaobaoRefundMapper refundMapper;

    @Autowired
    private IpOrderCancelToAgService toAgService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderRecountAmountService omsOrderRecountAmountService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 markRefund(JSONObject obj, User user) {
        ValueHolderV14 vh;
        //明细ids
        String ids = obj.getString("IDS");
        //转成数组
        String[] idArray = ids.split(",");
        if (idArray == null || idArray.length == 0) {
            throw new NDSException(Resources.getMessage("请选择需要标记退款完成的记录！", user.getLocale()));
        }
        log.debug("标记退款完成入参:" + obj.toJSONString());
        // 查主表id
        Long esOcbOrderId = ES4Order.getIdByItemId(Long.valueOf(idArray[0]));
        if (esOcbOrderId == null){
            throw new NDSException(Resources.getMessage("未查询到需要标记退款完成的记录ID！", user.getLocale()));
        }
        //获取订单信息
        OcBOrder order = ocBOrderMapper.selectById(esOcbOrderId);
        //获取当前选取的明细数据
        String id = getItemIds(idArray, order);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("IDS", id);
        vh = markRefundService.markRefund(jsonObject, user);
        if (vh.getCode() == 0) {
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListNotGift(order.getId());
            orderItems = orderItems.stream().filter(p -> (
                    p.getRefundStatus().equals(OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal())
            )).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderItems)) {
                OcBOrder ocBOrder = new OcBOrder();
                ocBOrder.setIsInreturning(0);
                ocBOrder.setId(order.getId());
                omsOrderService.updateOrderInfo(ocBOrder);
                //是否已经拦截 修改hold单状态 使用HOLD单方法修改
                ocBOrder.setIsInterecept(0);//修改hold单状态 使用HOLD单方法修改
                ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder, OrderHoldReasonEnum.REFUND_HOLD);
            }
        }
        OcBOrder order1 = ocBOrderMapper.selectById(esOcbOrderId);
        if (OmsOrderStatus.SYS_VOID.toInteger().equals(order1.getOrderStatus())) {
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(order1.getId());
            //退单转换时需要
            ocBOrder.setSuffixInfo("REFUND-VOID");
            omsOrderService.updateOrderInfo(ocBOrder);

        }
        return vh;
    }

    public ValueHolder markRefundCancel(Long orderId, List<Long> itemIds, User user) {
        ValueHolder vh = new ValueHolder();
        try {
            markRefundService.markRefundCancel(orderId, itemIds, user);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "标记退款取消成功");
        } catch (Exception e) {
            log.error("MarkRefundCompleteService.markRefundCancel orderId={},异常信息={}", orderId, e);
            vh.put("code", ResultCode.FAIL);
            vh.put("message", e.getMessage());
        }
        return vh;
    }

    /**
     * 处理是否为组合商品
     *
     * @param idArray
     * @param order
     * @return
     */
    public String getItemIds(String[] idArray, OcBOrder order) {
        List<String> items = Arrays.asList(idArray);
        List<Long> itemIds = items.stream().map(Long::valueOf).collect(Collectors.toList());
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListsByIds(order.getId(), itemIds);
        List<String> longs = new ArrayList<>();
        for (OcBOrderItem orderItem : orderItems) {
            if (!OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(orderItem.getIsGift())) {
                //查询当前订单是否有挂靠赠品
                List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItemFullGiftList(order.getId());
                if (CollectionUtils.isNotEmpty(itemList)) {
                    List<OcBOrderItem> giftList = itemList.stream().filter(p -> StringUtils.isNotEmpty(p.getGiftRelation()) && p.getGiftRelation().
                            equals(orderItem.getGiftRelation())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(giftList)) {
                        List<Long> updateItemIds = giftList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                        for (Long updateItemId : updateItemIds) {
                            longs.add(updateItemId + "");
                        }
                    }
                }

            }
            Long proType = orderItem.getProType();
            if (proType != SkuType.NO_SPLIT_COMBINE) {
                longs.add(orderItem.getId() + "");
            } else {

                List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemByBagSku(order.getId(), orderItem.getPsCSkuEcode());
                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                    longs.add(ocBOrderItem.getId() + "");
                }
                OcBOrderItem item = new OcBOrderItem();
                item.setId(orderItem.getId());
                item.setOcBOrderId(orderItem.getOcBOrderId());
                item.setRefundStatus(OcOrderRefundStatusEnum.SUCCESS.getVal());
                item.setPrice(BigDecimal.ZERO);
                item.setOrderSplitAmt(BigDecimal.ZERO);
                item.setAmtDiscount(BigDecimal.ZERO);
                item.setAdjustAmt(BigDecimal.ZERO);
                // 记录实际成交价金额
                item.setReserveDecimal05(orderItem.getRealAmt());
                item.setRealAmt(BigDecimal.ZERO);
                omsOrderItemService.updateOcBOrderItem(item, orderItem.getOcBOrderId());
            }
        }
        //调用标记退款完成
        return String.join(",", longs);
    }

    /**
     * @param obj
     * @param user
     * @param ocBOrder 订单主表
     * @param idArray
     */
    private ValueHolderV14 mainStep(JSONObject obj, User user, OcBOrder ocBOrder, String[] idArray) {
        ValueHolderV14 vh = new ValueHolderV14();
        // 淘宝平台订单，则继续判断订单所属店铺在店铺策略中有没有勾选“是否启用AG”
        if (ocBOrder.getCpCShopId() == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单的店铺不能为空");
            return vh;
        }
        Integer success = 0;
        boolean toAgByShopStrategy = stCAutocheckService.isToAgByShopStrategy(ocBOrder.getCpCShopId());
        log.debug("查询是否启动AG结果:" + toAgByShopStrategy);
        if (toAgByShopStrategy) {
//            则查询订单明细中，退款状态为“买家已申请退款，等待卖家同意”的明细，用明细中的OOID查询淘宝退单中间表中的子订单编号，
//            查询对应的退单中间表的数据，获取退单信息，并判断退单中间表的退款状态
            for (int i = 0; i < idArray.length; i++) {
                Long aLong = Long.valueOf(idArray[i]);
                OcBOrderItem ocBOrderItem = ocBOrderItemMapper.selectOne(new QueryWrapper<OcBOrderItem>().eq("oc_b_order_id", ocBOrder.getId()).eq("id", aLong));
                ValueHolderV14 v14 = nextStep(obj, ocBOrder, ocBOrderItem, user);
                if (ResultCode.SUCCESS == v14.getCode()) {
                    success++;
                }
            }

        } else {
            // 如果未勾选“是否启用AG”，调用标记退款完成服务 程序结束；
            vh = markRefundService.markRefund(obj, user);
            /*vh.setCode(ResultCode.FAIL);
            vh.setMessage("未勾选“是否启用AG,不进行操作");*/
            return vh;
        }
        if (success != idArray.length) {
            throw new NDSException("标记退款失败");
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("标记退款成功");
        return vh;


    }

    private ValueHolderV14 nextStep(JSONObject obj, OcBOrder ocBOrder, OcBOrderItem ocBOrderItem, User user) {
        ValueHolderV14 vh = new ValueHolderV14();

        JSONObject search = ES4IpTaoBaoRefund.getIdByOid(ocBOrderItem.getOoid());

        Long ipRefundId = null;

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            JSONObject jsonObject = (JSONObject) arrayObj.get(0);
            ipRefundId = jsonObject.getLong("ID");
            log.debug("根据明细OOid查询出的中间退单表的数据" + search.toJSONString());
        } else {
            log.debug("进入找不到退单信息分支");
            // 如果未找到退单信息，则对此明细调用【标记退款成功】服务。
            vh = markRefundService.markRefund(obj, user);
            return vh;
        }

        //  查询t退单中间表数据
        IpBTaobaoRefund ipBTaobaoRefund = refundMapper.selectById(ipRefundId);
//        如果退款状态是：退款关闭，则更新明细的退款状态为0“未退款”

        if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(ipBTaobaoRefund.getStatus())) {
            log.debug("进入退款状态是：退款关闭分支");
            OcBOrderItem item = new OcBOrderItem();
            item.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
            item.setModifierename(user.getEname());
            item.setModifieddate(new Date());
            item.setModifiername(user.getName());
            ocBOrderItemMapper.update(item, new QueryWrapper<OcBOrderItem>().eq("oc_b_order_id", ocBOrder.getId()).eq("id", ocBOrderItem.getId()));
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("标记退款退款完成");
            return vh;
        } else if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(ipBTaobaoRefund.getStatus())) {
            log.debug("退款状态是：买家已经申请退款，等待卖家同意分支");
            //        如果退款状态是：买家已经申请退款，等待卖家同意，则对此明细调用【标记退款成功】服务，调用标记退款成功服务成功之后，再调用【订单传AG】服务。
            vh = markRefundService.markRefund(obj, user);
            if (ResultCode.SUCCESS == vh.getCode()) {
                try {
                    log.debug("进入调AG退款服务");
                    boolean b = toAgService.orderCancelToAg(ocBOrder, ipBTaobaoRefund, user);
                    log.debug("调AG退款服务返回的结果:" + b);
                    if (!b) {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), "退款单号为:" + ipBTaobaoRefund.getRefundId() + ",订单AG取消发货失败", null, null, user);
                    }
                } catch (Exception e) {
                    log.error("调用AG服务异常:" + e);
                    throw new NDSException(e.getMessage());
                }
            }
            return vh;
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("标记退款退款完成");
        return vh;
    }

}
