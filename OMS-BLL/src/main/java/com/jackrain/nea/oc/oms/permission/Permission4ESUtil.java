package com.jackrain.nea.oc.oms.permission;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.permission.ColumnPermission;
import com.jackrain.nea.ext.permission.Permissions;
import com.jackrain.nea.oc.oms.model.perm.PermissionAlias;
import com.jackrain.nea.oc.oms.model.perm.PermissionEnum;
import com.jackrain.nea.oc.oms.model.perm.PermissionModel;
import com.jackrain.nea.oc.oms.model.request.OrderQueryRequest;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Permission Filter
 * ElasticSearch Condition
 *
 * @Desc : order search filter by permission
 * <AUTHOR> xiWen
 * @Date : 2020/4/13
 */
@Slf4j
public class Permission4ESUtil {


    private static final String BRAND_KEY = "PS_C_BRAND_ID";
    private static final String STORE_KEY = "CP_C_MAIN_STORE_ID";
    private static final String WAREHOUSE_KEY = "CP_C_WAREHOUSE_ID";
    private static final String SHOP_KEY = "CP_C_SHOP_ID";

    private static final String SHOP_ID = "CP_C_SHOP_ID";
    private static final String STORE_ID = "CP_C_STORE_ID";
    private static final String BRAND_ID = "PS_C_BRAND_ID";
    private static final String WAREHOUSE_ID = "CP_C_PHY_WAREHOUSE_ID";

    private static final String LOGISTICS_KEY = "CP_C_LOGISTICS_ID";
    private static final String LOGISTICS_ID = "CP_C_LOGISTICS_ID";

    private static final String YES = "Y";
    private static String PARENT_KEY = "parentKey";

    private static final Pattern p = Pattern.compile("^[1-9]{1}\\d*$");

    /**
     * Permission Handle
     *
     * @param usr 当前操作用户
     * @return 基础权限处理结果, false : 权限过滤后,无权限查询
     */
    public static boolean permissionHandler(User usr, OrderQueryRequest queryDto, boolean isMulti) {

        // 1. validation parameters
        notNullCsm.accept(usr, "Currently Login User Not Allowed Null");
        if (usr.isAdmin()) {
            return true;
        }

        notNullCsm.accept(queryDto.getWhere(), "ElasticSearch Condition Not Allowed Null");
        notNullCsm.accept(queryDto.getPermissionModel(), "PermissionModel Not Allowed Null");
        PermissionModel permissionModel = queryDto.getPermissionModel();
        if (permissionModel == null) {
            return true;
        }
        List<PermissionEnum> permissions = permissionModel.permissions();
        if (permissions == null || permissions.size() < 1) {
            return true;
        }

        if (isMulti) {
            return dealMultiCondition(permissions, queryDto, usr);
        }
        // 2. loop deal
        return loopHandlePermission(usr, permissions, queryDto.getWhere(), queryDto.getPermissionAlias());
    }

    /**
     * multi relations
     *
     * @param permissions
     * @param queryDto
     * @param usr
     * @return
     */
    private static boolean dealMultiCondition(List<PermissionEnum> permissions, OrderQueryRequest queryDto, User usr) {

        JSONArray whereKeys = queryDto.getWhereKeys();
        for (int i = 0; i < whereKeys.size(); i++) {
            JSONObject cellJsn = whereKeys.getJSONObject(i);
            notNullCsm.accept(cellJsn, "Not Allow Null Value In Multi Where Keys");

            JSONArray primaryAry = cellJsn.getJSONArray(PARENT_KEY);
            if (primaryAry == null) {
                primaryAry = new JSONArray();
                primaryAry.add(queryDto.getWhere());
                cellJsn.put(PARENT_KEY, primaryAry);
            }
            JSONObject whereJsn = primaryAry.getJSONObject(0);
            if (whereJsn == null || whereJsn.size() < 1) {
                whereJsn = queryDto.getWhere();
            }
            boolean eachResult = loopHandlePermission(usr, permissions, whereJsn, queryDto.getPermissionAlias());
            if (!eachResult) {
                return false;
            }
        }
        return true;
    }

    /**
     * 敏感列过滤
     *
     * @param usr 当前操作用户
     * @return not Sensitive Column
     */
    public static void filterSensitiveColumn(User usr, JSONArray jsnAry) {

        notNullCsm.accept(usr, "Currently Login User Not Allowed Null");
        if (usr.isAdmin()) {
            return;
        }

        List<Long> groups = usr.getGroups();
        notEmptyCsm.accept(groups, "Not Found Role In Currently Login User");

        Map<String, ColumnPermission> columns = Permissions.getAllColumns(groups);

        List<String> grantColumn = getGrantColumn(columns);

        columnFilter(grantColumn, jsnAry);
    }

    /**
     * 敏感列获取
     * 默认禁读
     *
     * @param usr             User
     * @param permissionEnums default forbid read
     * @return forbidden cols
     */
    public static List<String> forbiddenCols(User usr, PermissionEnum... permissionEnums) {

        notNullCsm.accept(usr, "Currently Login User Not Allowed Null");
        if (usr.isAdmin()) {
            return new ArrayList<>();
        }

        List<Long> groups = usr.getGroups();
        notEmptyCsm.accept(groups, "Not Found Role In Currently Login User");

        Map<String, ColumnPermission> columns = Permissions.getAllColumns(groups);

        return getGrantColumn(columns, permissionEnums);

    }

    /**
     * loop deal each permission
     *
     * @param usr   operator User
     * @param pEL   current user permission
     * @param jsn   search conditions
     * @param alias current model search key alia
     * @return deal result, false : has no permission to search
     */
    private static boolean loopHandlePermission(User usr, List<PermissionEnum> pEL, JSONObject jsn, PermissionAlias alias) {
        boolean result = false;
        for (PermissionEnum perEnum : pEL) {

            Object aliasKey;
            String permissionKey;
            switch (perEnum) {
                case SHOP:
                    permissionKey = SHOP_KEY;
                    aliasKey = alias == null ? SHOP_ID : determineKeyFun.apply(SHOP_ID, alias.getShop());
                    break;
                case STORE:
                    permissionKey = STORE_KEY;
                    aliasKey = alias == null ? STORE_ID : determineKeyFun.apply(STORE_ID, alias.getStore());
                    break;
                case BRAND:
                    permissionKey = BRAND_KEY;
                    aliasKey = alias == null ? BRAND_ID : determineKeyFun.apply(BRAND_ID, alias.getBrand());
                    break;
                case WAREHOUSE:
                    permissionKey = WAREHOUSE_KEY;
                    aliasKey = alias == null ? WAREHOUSE_ID : determineKeyFun.apply(WAREHOUSE_ID, alias.getWareHouse());
                    break;
                case LOGISTICS:
                    permissionKey = LOGISTICS_KEY;
                    aliasKey = alias == null ? LOGISTICS_ID : determineKeyFun.apply(LOGISTICS_ID, alias.getLogistics());
                    break;
                default:
                    notNullCsm.accept(null, "Unknown Permission Identifier");
                    return false;
            }
            List<Long> permissions = PermissionFun.apply(usr, permissionKey);
            if (permissionKey.equals(WAREHOUSE_KEY)) {
                permissions.add(0L);
            }
            result = loopMultiFields(aliasKey, jsn, permissions);
            if (!result) {
                break;
            }
        }
        return result;
    }


    /**
     * deal each sensitive column
     *
     * @param fieldKeys   current model permission field , key | {key1,key2}
     * @param jsn         search conditions
     * @param permissions current user permission
     * @return bool deal result, false : has no permission case by permission filter
     */
    private static boolean loopMultiFields(Object fieldKeys, JSONObject jsn, List<Long> permissions) {

        if (permissions == null || permissions.size() < 1) {
            return false;
        }

        if (fieldKeys instanceof List) {
            List<String> list = (List<String>) fieldKeys;
            for (String var : list) {
                boolean eachResult = new PermissionHandler(var).handler(permissions, jsn);
                if (!eachResult) {
                    return false;
                }
            }
        } else {
            return new PermissionHandler((String) fieldKeys).handler(permissions, jsn);
        }
        return true;
    }


    /**
     * deal permission class
     */
    static class PermissionHandler {

        /**
         * current permission key that use for search condition
         */
        private String fieldKey;

        PermissionHandler(String key) {
            this.fieldKey = key;
        }

        /**
         * filter
         * deal each field key
         *
         * @param permissions List<Long>
         * @param jsn         JSONObject
         * @return false: no permission
         */
        private boolean handler(List<Long> permissions, JSONObject jsn) {

            // haven't permission key  or contain permission key but no value
            if (!jsn.keySet().contains(fieldKey)) {
                PermissionCsm.accept(jsn, permissions);
                return true;
            }

            String val = jsn.getString(fieldKey);
            if (val == null || val.length() < 1 || StringUtils.equalsIgnoreCase("null", val)) {
                PermissionCsm.accept(jsn, permissions);
                return true;
            }

            try {
                List<Long> searchVal = JSON.parseArray(val, Long.class);
                permissions.retainAll(searchVal);
                if (permissions.size() < 1) {
                    return false;
                }
                PermissionCsm.accept(jsn, permissions);
            } catch (Exception e) {
                Matcher matcher = p.matcher(val);
                if (matcher.matches()) {
                    if (!permissions.contains(Long.valueOf(val))) {
                        return false;
                    }
                } else {
                    return false;
                }
            }
            return true;
        }

        /**
         * haven't search condition , use the permission
         */
        private BiConsumer<JSONObject, List<Long>> PermissionCsm = (jsonObject, longs) -> jsonObject
                .put(this.fieldKey, JSONArray.parseArray(JSON.toJSONString(longs)));

    }

    /***
     *
     * @param forbidCols forbidCols
     * @param columns columns
     */
    private static void columnFilter(List<String> forbidCols, JSONArray columns) {

        if (!notEmptyPdc.test(forbidCols)) {
            return;
        }
        for (int i = 0; i < columns.size(); i++) {
            JSONObject obj = columns.getJSONObject(i);
            Set<Map.Entry<String, Object>> entries = obj.entrySet();
            for (Map.Entry<String, Object> entry : entries) {
                String key = entry.getKey();
                if (forbidCols.contains(key.toUpperCase())) {
                    columns.remove(i--);
                }
            }
        }

    }

    /**
     * 敏感列权限级别
     *
     * @param sensitiveColumns 敏感列
     * @return 返回敏感列, 默认禁读
     */
    private static List<String> getGrantColumn(Map<String, ColumnPermission> sensitiveColumns, PermissionEnum... pms) {

        BiPredicate<String, String> forbidPdc = forbidLevelFn.apply(pms);
        List<String> forbidColumns = new ArrayList<>();
        Set<Map.Entry<String, ColumnPermission>> colEntries = sensitiveColumns.entrySet();
        for (Map.Entry<String, ColumnPermission> colEntry : colEntries) {

            ColumnPermission colPerm = colEntry.getValue();
            if (colPerm == null) {
                continue;
            }
            String isRead = colPerm.getIsRead();
            String isModify = colPerm.getIsModify();
            String eCode = colPerm.getEcode();

            if (forbidPdc.test(isModify, isRead)) {
                forbidColumns.add(eCode.toUpperCase());
            }
        }
        return forbidColumns;
    }

    private static BiFunction<String, String, Integer> levelFun = (m, r) -> YES.equals(m) ? 2 : YES.equals(r) ? 1 : -1;

    private static BiPredicate<String, String> forbidReadPdc = (s1, s2) -> levelFun.apply(s1, s2) < 0;

    private static BiPredicate<String, String> forbidModifyPdc = (s1, s2) -> levelFun.apply(s1, s2) < 2;

    private static Function<PermissionEnum[], BiPredicate<String, String>> forbidLevelFn = x -> (x.length < 1)
            ? forbidReadPdc : (x[0] == PermissionEnum.FORBIDDEN_MODIFY) ? forbidModifyPdc : forbidReadPdc;

    private static BiFunction<User, String, List<Long>> PermissionFun = (user, s) -> {
        List<Long> list = new ArrayList<>();
        list.addAll(Permissions.getReadableDatas(s, user.getId()));
        return list;
    };

    private static BiConsumer<Object, String> notNullCsm = (o, s) -> {
        if (o == null) {
            throw new NDSException(s);
        }
    };

    private static BiConsumer<List, String> notEmptyCsm = (o, s) -> {
        if (o.size() < 1) {
            throw new NDSException(s);
        }
    };

    private static Predicate<List<String>> notEmptyPdc = o -> o != null && o.size() > 0;

    private static BiFunction<String, List<String>, Object> determineKeyFun = (s, keys) -> {
        if (notEmptyPdc.test(keys)) {
            return keys;
        }
        return s;
    };


}
