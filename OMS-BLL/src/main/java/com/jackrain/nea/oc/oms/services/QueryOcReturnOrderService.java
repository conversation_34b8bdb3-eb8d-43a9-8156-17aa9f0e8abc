package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * 根据条件查询退换货订单表数据
 *
 * @author: 夏继超
 * @since: 2019/4/18
 * create at : 2019/4/18 19:35
 */
@Component
@Slf4j
public class QueryOcReturnOrderService {
    @Autowired
    OcBReturnOrderMapper orderMapper;
    @Autowired
    private ElasticSearchUtil elasticSearchUtil;

    /**
     * 字符串转集合
     *
     * @param param 传入的参数
     * @return 返回的信息
     */
    public static List stringToList(String param) {
        List list = new ArrayList();
        String[] split = param.split(",");
        for (String s : split) {
            list.add(Long.valueOf(s));
        }
        return list;
    }

    public ValueHolderV14<OcBReturnOrder> queryOcbReturn(Long cpShopId, List<Long> returnStatus, Long isWriteroff, Long count) {
        ValueHolderV14 vh = new ValueHolderV14();
        JSONObject object = new JSONObject();
        if (returnStatus.size() > 0 && returnStatus != null) {
            object.put("RETURN_STATUS", returnStatus);
        } else {
            vh.setCode(-1);
            vh.setMessage("参数不能为空");
            return vh;
        }
        if (isWriteroff != null) {
             object.put("IS_WRITEOFF", isWriteroff);
        } else {
            vh.setCode(-1);
            vh.setMessage("参数不能为空");
            return vh;
        }
        if (cpShopId != null) {
            object.put("CP_C_SHOP_ID", returnStatus);
        } else {
            vh.setCode(-1);
            vh.setMessage("参数不能为空");
            return vh;
        }
        try {
            //获取分页显示的页数
            Integer start = 1;
            //获取每页显示的数量 默认50条
            Integer pageSize = 1;
            if (count != null) {
                pageSize = Integer.valueOf(count.toString());
            }
            //获取查询条件 判断是否为空 如果不为空则设置到ES
            JSONObject ESObject = findSelect(object);
            if (CollectionUtils.isEmpty(ESObject)) {
               /* ESObject.put("AD_CLIENT_ID", 37);
                ESObject.put("AD_ORG_ID", 27);*/
            }
            JSONObject filter = new JSONObject();
            JSONArray orderKeys = new JSONArray();
            JSONObject order = new JSONObject();
            orderKeys.add(order);
            String[] arr = {"ID"};
            JSONObject search = ES4ReturnOrder.getReturnOrderSearchByUserinfo(ESObject, filter, orderKeys, pageSize, (start - 1) * pageSize, arr);
            JSONArray data = search.getJSONArray("data");
            if (CollectionUtils.isEmpty(data)) {
                vh.setMessage("查询结果为空");
                vh.setCode(-1);
                return vh;
            }
            //获取IDS编号数组
            JSONArray jsonArray = new JSONArray();
            for (int i = 0; i < data.size(); i++) {
                String id = "" + data.getJSONObject(i).getString("ID") + "";
                jsonArray.add(id);
            }
            String join = StringUtils.join(jsonArray, ",");
            List list = stringToList(join);
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.in("ID", list);
            wrapper.orderByAsc("creationdate");
            List ocBreturnOrders = orderMapper.selectList(wrapper);
            if (ocBreturnOrders.size() > 0 && ocBreturnOrders != null) {
                vh.setCode(0);
                vh.setMessage("查询成功");
                vh.setData(ocBreturnOrders);
            } else {
                vh.setCode(-1);
                vh.setMessage("未查询到结果");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            vh.setCode(-1);
            vh.setMessage("查询失败，未检查到数据");
        }
        return vh;
    }

    /**
     * 筛选条件 排除为空的字段
     *
     * @param param 传入的参数
     * @return
     */
    private JSONObject findSelect(JSONObject param) {
        HashMap<String, Object> hashMap = new HashMap<>(param);
        Set<String> keys = hashMap.keySet();
        for (String key : keys) {
            Object value = hashMap.get(key);
            if (StringUtils.isEmpty(value.toString())) {
                hashMap.remove(key);
            } else if (key.equalsIgnoreCase("RETURN_STATUS")) {
                JSONArray array = param.getJSONArray("RETURN_STATUS");
                hashMap.put(key, array);
            } else if (key.equalsIgnoreCase("IS_WRITEOFF")) {
                hashMap.put(key, value);
            } else if (key.equalsIgnoreCase("CP_C_SHOP_ID")) {
                hashMap.put(key, value);
            }
        }
        if (CollectionUtils.isEmpty(hashMap)) {

            return new JSONObject();
        } else {
            return new JSONObject(hashMap);
        }
    }
}
