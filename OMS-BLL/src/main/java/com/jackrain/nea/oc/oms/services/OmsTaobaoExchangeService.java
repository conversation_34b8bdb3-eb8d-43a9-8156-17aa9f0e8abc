package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.ExchangeAgreeModel;
import com.jackrain.nea.ip.model.ExchangeRefuseModel;
import com.jackrain.nea.ip.model.IpCTaobaoProductItem;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.*;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.ExchangeOrderTransferUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author: 黄世新
 * @Date: 2020/11/30 4:04 下午
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsTaobaoExchangeService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private IpBTaobaoExchangeMapper ipBTaobaoExchangeMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderExchangeMapper ocBReturnOrderExchangeMapper;
    @Autowired
    private OmsReturnUtil omsReturnUtil;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private ExchangeOrderTransferUtil exchangeOrderTransferUtil;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;

    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;


    public OmsTaobaoExchangeRelation selectTaobaoExchangeInfo(String disputeId) {

        OmsTaobaoExchangeRelation relation = new OmsTaobaoExchangeRelation();
        IpBTaobaoExchange taoBaoExchange = ipBTaobaoExchangeMapper.selectTaobaoExchangeByDisputeId(disputeId);
        if (taoBaoExchange == null) {
            return null;
        }
        //查询原单 对应订单明细的ooid
        relation.setIpBTaobaoExchange(taoBaoExchange);
        //查询换货sku信息
        ProductSku productSku = this.selectSkuInfo(taoBaoExchange);
        if (productSku == null) {
            return relation;
        }
        relation.setProductSku(productSku);
        //原单的ooid
        Long bizOrderId = taoBaoExchange.getBizOrderId();
        JSONArray returnData = ES4Order.getIdsByItemOoId(bizOrderId);
        if (CollectionUtils.isEmpty(returnData)) {
            return relation;
        }
        Set<Long> orderIdList = new HashSet<>();
        for (int i = 0; i < returnData.size(); i++) {
            Long orderId = returnData.getJSONObject(i).getLong("OC_B_ORDER_ID");
            orderIdList.add(orderId);
        }
        //查询原单信息
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(new ArrayList<>(orderIdList));
        //直接过滤取消和作废的订单
        ocBOrders = ocBOrders.stream().filter(p -> !(OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus())
                || OmsOrderStatus.SYS_VOID.toInteger().equals(p.getOrderStatus()))).collect(Collectors.toList());
        List<OmsOrderExchangeRelation> originalSingleOrders = new ArrayList<>();
        List<OmsOrderExchangeRelation> exchangeOrders = new ArrayList<>();
        for (OcBOrder ocBOrder : ocBOrders) {
            //原单
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemByOoid(ocBOrder.getId(), bizOrderId + "");
            //封装原单信息
            if (CollectionUtils.isNotEmpty(orderItems)) {
                List<OcBOrderItem> ocBOrderItemList = orderItems.stream().filter(p -> p.getIsExchangeItem() == null || p.getIsExchangeItem() == 0).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(ocBOrderItemList)) {
                    OmsOrderExchangeRelation exchangeRelation = new OmsOrderExchangeRelation();
                    exchangeRelation.setOcBOrder(ocBOrder);
                    exchangeRelation.setOcBOrderItems(ocBOrderItemList);
                    originalSingleOrders.add(exchangeRelation);
                }
                List<OcBOrderItem> orderItemList = orderItems.stream().filter(p -> p.getIsExchangeItem() != null && p.getIsExchangeItem() == 1).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(orderItemList)) {
                    OmsOrderExchangeRelation exchangeRelation = new OmsOrderExchangeRelation();
                    //不包含赠品
                    exchangeRelation.setOcBOrder(ocBOrder);
                    exchangeRelation.setOcBOrderItems(orderItemList);
                    exchangeOrders.add(exchangeRelation);
                }
            }
        }
        relation.setOriginalSingleOrder(originalSingleOrders);
        relation.setExchangeOrder(exchangeOrders);
        return relation;
    }


    /**
     * 查询换货sku信息
     *
     * @param taoBaoExchange
     * @return
     */
    private ProductSku selectSkuInfo(IpBTaobaoExchange taoBaoExchange) {
        if (StringUtils.isNotEmpty(taoBaoExchange.getOuterSkuId())) {
            String outerSkuId = taoBaoExchange.getOuterSkuId();
            ProductSku selectProductSku = psRpcService.selectProductSku(outerSkuId);
            return selectProductSku;
        } else {
            //通过换货商品的sku判断换货商品是否存在
            //对应淘宝运枢纽商品表的skuid；由于换货信息下发的换货商品信息是淘宝平台的商品ID，因此需要到商品中间表进行查询对应的R3系统商品SKUID
            String exchangeSku = taoBaoExchange.getExchangeSku();
            IpCTaobaoProductItem taobaoProductItem = cpRpcService.selectIpCTaobaoProductItemBySkuId(exchangeSku);
            if (taobaoProductItem != null) {
                //对应sku表真实的skuid
                String outerId = taobaoProductItem.getOuterId();
                // 查询换货的sku信息
                ProductSku productSku = psRpcService.selectProductSku(outerId);
                taoBaoExchange.setOuterSkuId(outerId);
                taoBaoExchange.setExchangeOuterid(outerId);
                return productSku;
            }
        }
        return null;
    }


    /**
     * 根据换货单号或者
     *
     * @param ipBTaobaoExchange
     * @return
     */
    public ExchangeOrderRelation selectReturnOrderList(IpBTaobaoExchange ipBTaobaoExchange) {
        ExchangeOrderRelation relation = new ExchangeOrderRelation();
        List<Long> returnOrderByDisputeId = ES4ReturnOrder.isExistReturnOrderByDisputeId(ipBTaobaoExchange.getDisputeId() + "");
        List<OcBReturnOrder> list = null;
        if (CollectionUtils.isNotEmpty(returnOrderByDisputeId)) {
            list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(returnOrderByDisputeId);

        }
        if (CollectionUtils.isEmpty(list)) {
            List<Long> refundByoid = ES4ReturnOrder.isExistReturnOrderRefundByoid(ipBTaobaoExchange.getBizOrderId() + "");
            if (CollectionUtils.isNotEmpty(refundByoid)) {
                list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(refundByoid);
                relation.setFlag(2);
            }
        } else {
            relation.setFlag(1);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            relation.setOcBReturnOrders(list);
            return relation;
        }
        return null;
    }

    /**
     * 查询当前退换货单的换货明细
     *
     * @param returnId
     * @return
     */
    public List<OcBReturnOrderExchange> selectReturnOrderExchangeList(Long returnId) {
        return ocBReturnOrderExchangeMapper.selectByReturnIdList(returnId);
    }

    /**
     * 退换货转退货
     *
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    public void exchangeToReturn(OcBReturnOrder returnOrder, User user) {
        Long id = returnOrder.getId();
        Integer returnStatus = returnOrder.getReturnStatus(); //退换货单的状态
        ocBReturnOrderExchangeMapper.deleteByReturnOrderId(id);
        OcBReturnOrder ocBReturnOrder = new OcBReturnOrder();
        ocBReturnOrder.setId(id);
        ocBReturnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());
        if (ReturnStatusEnum.COMPLETION.getVal().equals(returnStatus)) {
            ocBReturnOrder.setReturnStatus(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
        }
        ocBReturnOrder.setExchangeAmt(BigDecimal.ZERO);
        OperateUserUtils.saveOperator(ocBReturnOrder, user);
        ocBReturnOrderMapper.updateById(ocBReturnOrder);
    }

    /**
     * 取消退换货单单及换货订单
     *
     * @param orderInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelExchangeOrderAndReturnOrder(OmsTaobaoExchangeRelation orderInfo, User operateUser) {
        OcBReturnOrder ocBReturnOrder = orderInfo.getOcBReturnOrder();
        Integer returnStatus = ocBReturnOrder.getReturnStatus();
        if (!TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode().equals(returnStatus)) {
            //退换货单无法取消了
            return false;
        }
        //取消退换货单 以及换货订单
        this.cancelExchangeOrder(orderInfo, operateUser);
        List<Long> returnOrderIds = new ArrayList<>();
        returnOrderIds.add(ocBReturnOrder.getId());
        omsRefundOrderService.refundOrderClose(returnOrderIds, null, null, operateUser);
        List<OmsOrderExchangeRelation> originalSingleOrder = orderInfo.getOriginalSingleOrder();
        for (OmsOrderExchangeRelation orderExchangeRelation : originalSingleOrder) {
            OcBOrder ocBOrder = orderExchangeRelation.getOcBOrder();
            List<OcBOrderItem> ocBOrderItems = orderExchangeRelation.getOcBOrderItems();
            //重新查询订单明细信息 此地重新查询主要是取消退换货单的时候 会清空已申请数量 不重新查询会导致后面无法生成新的
            List<Long> longList = ocBOrderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            ocBOrderItems = ocBOrderItemMapper.selectOrderItemListsByIds(ocBOrder.getId(), longList);
            orderExchangeRelation.setOcBOrderItems(ocBOrderItems);
        }

        return true;
    }

    /**
     * 取消换货订单
     *
     * @param orderInfo
     * @param operateUser
     */
    public boolean cancelExchangeOrder(OmsTaobaoExchangeRelation orderInfo, User operateUser) {
        //取消退换货单 以及换货订单
        List<OmsOrderExchangeRelation> exchangeOrder = orderInfo.getExchangeOrder();
        if (CollectionUtils.isNotEmpty(exchangeOrder)) {
            List<OmsOrderExchangeRelation> after = exchangeOrder.stream().filter(p ->
                    OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(p.getOcBOrder().getOrderStatus())
                            || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(p.getOcBOrder().getOrderStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(after)) {
                return false;

            }
            List<OmsOrderExchangeRelation> middleState = exchangeOrder.stream().filter(p ->
                    OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(p.getOcBOrder().getOrderStatus())
                            || OmsOrderStatus.PENDING_WMS.toInteger().equals(p.getOcBOrder().getOrderStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(middleState)) {
                String message = "有换货订单状态为待分配或者待传wms,等待下一次准换";
                throw new NDSException(message);
            }
            //需要反审核
            List<OmsOrderExchangeRelation> needDeAudit = exchangeOrder.stream().filter(p ->
                    OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(p.getOcBOrder().getOrderStatus())
                            || OmsOrderStatus.CHECKED.toInteger().equals(p.getOcBOrder().getOrderStatus())).collect(Collectors.toList());
            for (OmsOrderExchangeRelation orderExchangeRelation : needDeAudit) {
                OcBOrder ocBOrder = orderExchangeRelation.getOcBOrder();
                if (!omsReturnUtil.toExamineOrder(ocBOrder, operateUser)) {
                    String message = "换货关闭,换货订单反审核失败,等待下次继续转换";
                    throw new NDSException(message);
                }
            }
            //此时换货订单的状态 只会是缺货  或者待审核  调用标记退款完成
            for (OmsOrderExchangeRelation orderExchangeRelation : exchangeOrder) {
                Long id = orderExchangeRelation.getOcBOrder().getId();
                List<OcBOrderItem> ocBOrderItems = orderExchangeRelation.getOcBOrderItems();
                List<OcBOrderItem> orderItems = ocBOrderItems.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE || p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
                List<Long> itemIds = orderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                if (!omsReturnUtil.signRefundNew(id, itemIds, operateUser, OrderHoldReasonEnum.ADD_EXCHANGE_ORDER)) {
                    String message = "换货关闭,标记退款完成失败,等待下次继续转换";
                    throw new NDSException(message);
                }
            }
        }
        return true;
    }

    /**
     * 更新换货订单以及退换货单
     *
     * @param orderInfo
     * @param operateUser
     */
    public boolean updateExchangeOrderAndReturnOrder(OmsTaobaoExchangeRelation orderInfo, User operateUser) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        OcBReturnOrder ocBReturnOrder = orderInfo.getOcBReturnOrder();

        OcBReturnOrder returnOrder = new OcBReturnOrder();
        returnOrder.setId(ocBReturnOrder.getId());
        returnOrder.setTbDisputeId(ipBTaobaoExchange.getDisputeId());
        //退款金额
        //returnOrder.setReturnAmtActual(ipBTaobaoExchange.getPrice());
        //卖家昵称
        //returnOrder.setBuyerNick(ipBTaobaoExchange.getBuyerNick());
        //退款创建时间
        // 更新是否换货预留库存   是
        returnOrder.setIsReserved(1);
        returnOrder.setReturnCreateTime(ipBTaobaoExchange.getCreated());
        //最后修改时间
        returnOrder.setLastUpdateTime(ipBTaobaoExchange.getModified());
        //退款说明
        returnOrder.setReturnDesc(ipBTaobaoExchange.getReason());
        if (StringUtils.isEmpty(ocBReturnOrder.getLogisticsCode())) {
            returnOrder.setLogisticsCode(ipBTaobaoExchange.getBuyerLogisticNo());
        }
        //物流公司
        String cpCLogisticsEname = ocBReturnOrder.getCpCLogisticsEname();
        if (StringUtils.isEmpty(cpCLogisticsEname)) {
            returnOrder.setCpCLogisticsEname(ipBTaobaoExchange.getBuyerLogisticName());
            omsReturnUtil.setLogisticInfo(returnOrder, ipBTaobaoExchange.getBuyerLogisticName());
        }
        //加入“空运单号延迟推单有效时间”字段
        returnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder));
        ocBReturnOrderMapper.updateById(returnOrder);
        List<OcBReturnOrderExchange> ocBReturnOrderExchanges = orderInfo.getOcBReturnOrderExchanges();
        if (CollectionUtils.isNotEmpty(ocBReturnOrderExchanges)) {
            for (OcBReturnOrderExchange ocBReturnOrderExchange : ocBReturnOrderExchanges) {
                OcBReturnOrderExchange orderExchange = new OcBReturnOrderExchange();
                orderExchange.setExchangeStatus(ipBTaobaoExchange.getStatus());
                orderExchange.setDisputeId(ipBTaobaoExchange.getDisputeId() + "");
                QueryWrapper<OcBReturnOrderExchange> wrapper = new QueryWrapper<>();
                wrapper.eq("id", ocBReturnOrderExchange.getId());
                wrapper.eq("oc_b_return_order_id", ocBReturnOrderExchange.getOcBReturnOrderId());
                ocBReturnOrderExchangeMapper.update(orderExchange, wrapper);
            }

        }
        List<OmsOrderExchangeRelation> exchangeOrder = orderInfo.getExchangeOrder();
        if (CollectionUtils.isNotEmpty(exchangeOrder)) {
            for (OmsOrderExchangeRelation orderExchangeRelation : exchangeOrder) {
                List<OcBOrderItem> ocBOrderItems = orderExchangeRelation.getOcBOrderItems();
                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                    OcBOrderItem orderItem = new OcBOrderItem();
                    orderItem.setId(ocBOrderItem.getId());
                    orderItem.setExchangeBillNo(ipBTaobaoExchange.getDisputeId());
                    orderItem.setOcBOrderId(ocBOrderItem.getOcBOrderId());
                    omsOrderItemService.updateOcBOrderItem(orderItem, ocBOrderItem.getOcBOrderId());
                }
            }
            //todo 手动生成的换货订单 此时订单可能已经占单成功 更新完数据之后需要将换货中间表置为未转换, 等待下次转换调用策略
            return false;
        } else {
            //则生成换货订单
            OcBReturnOrderRelation orderRelation = new OcBReturnOrderRelation();
            orderRelation.setReturnOrderInfo(ocBReturnOrder);
            exchangeOrderTransferUtil.buildExchangeOrder(orderRelation, orderInfo, operateUser);
            OcBOrder ocBOrder = orderRelation.getOcBOrder();
            List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
            if (ocBOrder != null) {
                ocBOrderMapper.insert(ocBOrder);
                ocBOrderItemMapper.batchInsert(ocBOrderItems);
                OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                toBeConfirmedTask.setOrderId(ocBOrder.getId());
                toBeConfirmedTask.setCreationdate(new Date());
                toBeConfirmedTask.setStatus(0);
                toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
                ocBOrder.setIsInterecept(OmsOrderIsInterceptEnum.YES.getVal());
                ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder, OrderHoldReasonEnum.ADD_EXCHANGE_ORDER);
                orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_ADD.getKey(), "新增换货订单成功", "", "", operateUser);
            }
        }
        return true;
    }

    /**
     * @param orderInfo
     * @param user
     */
    public void agreeExchangeGoods(OmsTaobaoExchangeRelation orderInfo, User user) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        //店铺id
        Long cpCShopId = ipBTaobaoExchange.getCpCShopId();

    }

    /**
     * 同意换货
     *
     * @param exchange
     * @return
     */
    public boolean agreeExchange(IpBTaobaoExchange exchange) {
        boolean result = false;
        try {
            ExchangeAgreeModel model = new ExchangeAgreeModel();
            model.setSellerNick(exchange.getSellerNick());
            model.setDisputeId(exchange.getDisputeId());
            model.setShopId(exchange.getCpCShopId());
            ValueHolderV14 vh = ipRpcService.agree(model);
            int code = Tools.getInt(vh.getCode(), -1);
            if (code == 0) {
                result = true;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " oms同意换货异常信息", e);
            throw new NDSException(e);
        }
        return result;
    }

    /**
     * 拒绝换货
     *
     * @param exchange
     * @param refuseId
     * @param leaveMessage
     * @return
     */
    public boolean refuseExchange(IpBTaobaoExchange exchange, Integer refuseId, String leaveMessage) {
        try {
            ExchangeRefuseModel model = new ExchangeRefuseModel();
            model.setDisputeId(exchange.getDisputeId());
            model.setShopId(exchange.getCpCShopId());
            model.setFields("dispute_id, bizorder_id, modified, status");
            if (StringUtils.isEmpty(leaveMessage)) {
                leaveMessage = "亲您好， 您想要换的款式没有库存了哦，这边只能暂时拒绝下您的换货申请了。建议您换下其他有库存的款式或者转让他人哦，带来不便万分抱歉~";
            }
            model.setLeaveMessage(leaveMessage);
            model.setSellerRefuseReasonId(refuseId.longValue());
            ValueHolderV14 vh = ipRpcService.refuse(model);
            int code = Tools.getInt(vh.getCode(), -1);
            if (code == 0) {
                return true;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " oms拒绝换货异常信息", e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * 判断地址是否发生变化
     *
     * @param orderInfo
     */
    public boolean isAddressChange(OmsTaobaoExchangeRelation orderInfo) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        String buyerAddress = ipBTaobaoExchange.getBuyerAddress();
        //新的地址
        Address address = exchangeOrderTransferUtil.addressResolutionNew(buyerAddress);
        OcBReturnOrder ocBReturnOrder = orderInfo.getOcBReturnOrder();
        //省
        String receiverProvinceName = ocBReturnOrder.getReceiverProvinceName();
        //市
        String receiverCityName = ocBReturnOrder.getReceiverCityName();
        //区
        String receiverAreaName = ocBReturnOrder.getReceiverAreaName();
        String addr = address.getAddr();
        String receiveAddress = ocBReturnOrder.getReceiveAddress();
        boolean flag = (StringUtils.isNotEmpty(addr) && !addr.equals(receiveAddress))
                || (address.getProvince() != null && !address.getProvince().equals(receiverProvinceName))
                || (address.getCity() != null && !address.getCity().equals(receiverCityName))
                || (address.getCounty() != null && !address.getCounty().equals(receiverAreaName));
        return flag;
    }


    /**
     * 更新物流公司名称及单号
     *
     * @param ocBReturnOrder
     * @param taobaoExchange
     */
    public void updateLogisticsCodeAndName(OcBReturnOrder ocBReturnOrder,
                                           IpBTaobaoExchange taobaoExchange,
                                           User operateUser) {
        String logisticsCode = ocBReturnOrder.getLogisticsCode(); //物流单号
        String buyerLogisticNo = taobaoExchange.getBuyerLogisticNo(); //买家物流单号
        if (StringUtils.isEmpty(logisticsCode) || !logisticsCode.equals(buyerLogisticNo)) {
            //更新退换单表的物流单号  物流公司
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            Long id = ocBReturnOrder.getId();
            String buyerLogisticName = taobaoExchange.getBuyerLogisticName(); //买家物流公司名称
            returnOrder.setId(id);
            returnOrder.setLogisticsCode(buyerLogisticNo);
            returnOrder.setCpCLogisticsEname(buyerLogisticName);
            omsReturnUtil.setLogisticInfo(returnOrder, buyerLogisticName);
            OperateUserUtils.saveOperator(returnOrder, operateUser);
            //加入“空运单号延迟推单有效时间”字段
            returnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder));
            ocBReturnOrderMapper.updateById(returnOrder);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateConsigneeAndPhone(OcBReturnOrder ocBReturnOrder,
                                        IpBTaobaoExchange taobaoExchange, List<OmsOrderExchangeRelation> exchangeOrder,
                                        User operateUser) {
        //手机
        String receivePhone = ocBReturnOrder.getReceiveMobile();
        //收货人姓名
        String receiveName = ocBReturnOrder.getReceiveName();
        //平台上的手机号
        String buyerPhone = taobaoExchange.getBuyerPhone();
        //平台上的买家姓名
        String buyerName = taobaoExchange.getBuyerName();
        if (StringUtils.isEmpty(buyerPhone) || !buyerPhone.equals(receivePhone)
                || StringUtils.isEmpty(buyerName) || !buyerName.equals(receiveName)) {
            //更新退换单表的物流单号  物流公司
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            Long id = ocBReturnOrder.getId();
            returnOrder.setId(id);
            returnOrder.setReceiveMobile(buyerPhone);
            returnOrder.setReceiveName(buyerName);
            OperateUserUtils.saveOperator(returnOrder, operateUser);
            ocBReturnOrderMapper.updateById(returnOrder);

        }
        if (CollectionUtils.isNotEmpty(exchangeOrder)) {
            for (OmsOrderExchangeRelation orderExchangeRelation : exchangeOrder) {
                OcBOrder ocBOrder = orderExchangeRelation.getOcBOrder();
                OcBOrder order = new OcBOrder();
                order.setId(ocBOrder.getId());
                order.setReceiverMobile(buyerPhone);
                order.setReceiverName(buyerName);
                ocBOrderMapper.updateById(order);
            }
        }
    }
}
