package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.util.excel.XlsConvertData;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: xiWen.z
 * create at: 2019/8/13 0013
 */
public class OcBorderExportConvert implements XlsConvertData {

    private String tn1 = "oc_b_order";
    private String tn2 = "oc_b_refund_in";
    private String tn3 = "oc_b_return_order";
    private final int digit = 2;

    @Override
    public void dealData(Map<String, List<Map<String, Object>>> dataMaps) {
        Set<String> tbKeys = dataMaps.keySet();
        dispatcherLabel:
        if (tbKeys != null) {
            if (tbKeys.contains(tn1)) {
                dealOcbOrder(dataMaps);
                break dispatcherLabel;
            } else if (tbKeys.contains(tn2)) {
                dealOcbRefundIn(dataMaps);
                break dispatcherLabel;
            } else if (tbKeys.contains(tn3)) {
                dealOcbReturnOrder(dataMaps);
                break dispatcherLabel;
            }
        }
    }

    /**
     * oc_b_order
     *
     * @param dataMaps tn: k,v.list
     */
    private void dealOcbOrder(Map<String, List<Map<String, Object>>> dataMaps) {

        fLabel:
        if (dataMaps.size() > 0) {
            List<Map<String, Object>> pList = dataMaps.get("oc_b_order");
            List<Map<String, Object>> sList = dataMaps.get("oc_b_order_item");
            if (pList == null) {
                break fLabel;
            }
            List<Map<String, Object>> collect = null;
            if (sList != null && sList.size() > 0) {
                collect = sList.stream().sorted(Comparator.comparing(OcBorderExportConvert::compareByPId))
                        .collect(Collectors.toList());
            }
            int i = 0;
            //int l = collect.size();
            Object s1;
            Object s2;
            Object s3;
            Object s0;
            String s;
            for (Map<String, Object> m1 : pList) {
                if (m1 == null) {
                    continue;
                }
                s1 = m1.get("cp_c_region_province_ename");
                s2 = m1.get("cp_c_region_city_ename");
                s3 = m1.get("cp_c_region_area_ename");
                s0 = m1.get("receiver_address");
                s = (s1 == null ? "" : s1.toString()) + (s2 == null ? "" : s2.toString())
                        + (s3 == null ? "" : s3.toString()) + (s0 == null ? "" : s0.toString());
                m1.put("receiver_address", s);
                Object id = m1.get("id");
                BigDecimal b = BigDecimal.ZERO;

                boolean g = false;
                m1.put("totqtylost", b.setScale(digit, BigDecimal.ROUND_DOWN));
            }
        }
    }


    /**
     * @param dataMaps tn: k,v.list
     */
    private void dealOcbRefundIn(Map<String, List<Map<String, Object>>> dataMaps) {
        fLabel:
        if (dataMaps.size() > 1) {
            List<Map<String, Object>> pList = dataMaps.get(tn2);
            List<Map<String, Object>> sList = dataMaps.get("oc_b_refund_in_product_item");
            if (pList == null) {
                break fLabel;
            }
            List<Map<String, Object>> collect = null;
            if (sList != null && sList.size() > 0) {
                collect = sList.stream().sorted(Comparator.comparing(OcBorderExportConvert::refundCompareByFk))
                        .collect(Collectors.toList());
            }
            int i = 0;
            int l = collect != null ? collect.size() : 0;
            for (Map<String, Object> m1 : pList) {
                if (m1 == null) {
                    continue;
                }
                Object id = m1.get("id");
                BigDecimal b = BigDecimal.ZERO;
                // 统计入库数量
                for (; i < l; i++) {
                    Map<String, Object> smp = sList.get(i);
                    if (smp == null) {
                        continue;
                    }
                    Object fk = smp.get("oc_b_refund_in_id");
                    if (fk == null) {
                        continue;
                    }
                    if (!fk.equals(id)) {
                        break;
                    }
                    Object n = smp.get("qty");
                    if (n == null) {
                        continue;
                    }
                    b = b.add(new BigDecimal(n.toString()));
                }
                m1.put("qty_all", b.setScale(digit, BigDecimal.ROUND_DOWN));
            }
        }
    }

    /**
     * @param dataMaps tn: k,v.list
     */
    private void dealOcbReturnOrder(Map<String, List<Map<String, Object>>> dataMaps) {

    }

    /**
     * item sorted by fk
     *
     * @param map map.k.v
     * @return long
     */
    private static Long compareByPId(Map<String, Object> map) {
        return Long.valueOf(map.get("oc_b_order_id").toString());
    }

    /**
     * @param map map.k.v
     * @return long
     */
    private static Long refundCompareByFk(Map<String, Object> map) {
        return Long.valueOf(map.get("oc_b_refund_in_id").toString());
    }

    /**
     * @param map map.k.v
     * @return long
     */
    private static Long returnCompareByFk(Map<String, Object> map) {
        return Long.valueOf(map.get("oc_b_return_order_id").toString());
    }
}
