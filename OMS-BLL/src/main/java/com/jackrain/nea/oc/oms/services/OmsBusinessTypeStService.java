package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.relation.BusinessTypeMatchStRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.service.StRedisKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/7/14 下午3:43
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsBusinessTypeStService {

    @Autowired
    private StCBusinessTypeMatchStrategyMapper stCBusinessTypeMatchStrategyMapper;
    @Autowired
    private StCBusinessTypeMatchStrategyItemMapper stCBusinessTypeMatchStrategyItemMapper;
    @Autowired
    private StCBusinessTypeMapper stCBusinessTypeMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;


    /**
     * 查询业务类型策略
     *
     * @param orderType 1=订单、2=退单、3=退款单
     */
    public List<BusinessTypeMatchStRelation> selectBusinessTypeByOrderType(Integer orderType){
        String redisKey = StRedisKey.ST_BUSINESS_TYPE_MATCH_TYPE + orderType;
        String businessType = redisOpsUtil.strRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotEmpty(businessType)) {
            List<BusinessTypeMatchStRelation> relations = JSON.parseArray(businessType, BusinessTypeMatchStRelation.class);
            return relations;
        }
        List<StCBusinessTypeMatchStrategy> stCBusinessTypeMatchStrategies = stCBusinessTypeMatchStrategyMapper.selectStCBusinessTypeMatchStrategyByType(orderType);
        if (CollectionUtils.isEmpty(stCBusinessTypeMatchStrategies)){
            return null;
        }
        List<Long> ids = stCBusinessTypeMatchStrategies.stream().map(StCBusinessTypeMatchStrategy::getId).collect(Collectors.toList());
        List<StCBusinessTypeMatchStrategyItem> strategyItems = stCBusinessTypeMatchStrategyItemMapper.selectStCBusinessTypeMatchStrategyItemListByMainId(ids);
        if (CollectionUtils.isEmpty(strategyItems)) {
            return null;
        }
        Map<Long, List<StCBusinessTypeMatchStrategyItem>> itemMap =
                strategyItems.stream().collect(Collectors.groupingBy(StCBusinessTypeMatchStrategyItem::getStCBusinessTypeMatchStrategyId));
        List<BusinessTypeMatchStRelation> relations = new ArrayList<>();
        for (StCBusinessTypeMatchStrategy stCBusinessTypeMatchStrategy : stCBusinessTypeMatchStrategies) {
            List<StCBusinessTypeMatchStrategyItem> strategyItems1 = itemMap.get(stCBusinessTypeMatchStrategy.getId());
            if (CollectionUtils.isNotEmpty(strategyItems1)) {
                BusinessTypeMatchStRelation relation = new BusinessTypeMatchStRelation();
                relation.setMatchStrategy(stCBusinessTypeMatchStrategy);
                relation.setMatchStrategyItems(strategyItems1);
                relations.add(relation);
            }
        }
        if (CollectionUtils.isNotEmpty(relations)) {
            String str = JSONObject.toJSONString(relations);
            redisOpsUtil.strRedisTemplate.opsForValue().set(redisKey, str);
        }
        return relations;
    }

    /**
     * 通过档案id查询业务类型档案
     *
     * @param id
     * @return
     */
    public StCBusinessType selectStCBusinessTypeById(Long id){
        String redisKey = StRedisKey.ST_BUSINESS_TYPE_ID + id;
        String businessType = redisOpsUtil.strRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotEmpty(businessType)) {
            StCBusinessType stCBusinessType = JSONObject.parseObject(businessType, StCBusinessType.class);
            return stCBusinessType;
        }
        StCBusinessType stCBusinessType = stCBusinessTypeMapper.selectById(id);
        if (stCBusinessType != null) {
            String str = JSONObject.toJSONString(stCBusinessType);
            redisOpsUtil.strRedisTemplate.opsForValue().set(redisKey, str);
        }
        return stCBusinessType;
    }




    /**
     * 通过档案id查询业务类型档案
     *
     * @param code
     * @return
     */
    public StCBusinessType selectStCBusinessTypeByCode(String code){
        String redisKey = StRedisKey.ST_BUSINESS_TYPE_CODE + code;
        String businessType = redisOpsUtil.strRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotEmpty(businessType)) {
            StCBusinessType stCBusinessType = JSONObject.parseObject(businessType, StCBusinessType.class);
            return stCBusinessType;
        }
        List<StCBusinessType> stCBusinessTypes = stCBusinessTypeMapper.selectStCBusinessTypeByCode(code);
        if (CollectionUtils.isNotEmpty(stCBusinessTypes)) {
            StCBusinessType stCBusinessType = stCBusinessTypes.get(0);
            String str = JSONObject.toJSONString(stCBusinessType);
            redisOpsUtil.strRedisTemplate.opsForValue().set(redisKey, str);
            return stCBusinessType;
        }
        return null;
    }

    public StCBusinessType selectBusinessTypeById(Long id){
        return stCBusinessTypeMapper.selectById(id);
    }

    /**
     * 根根类型及第三方业务编码
     *
     * @param orderType
     * @param thirdCode
     * @return
     */
    public StCBusinessType selectStCBusinessType(Integer orderType, String thirdCode) {
        String redisKey = StRedisKey.ST_BUSINESS_TYPE_ID + orderType +":"+ thirdCode;
        String businessType = redisOpsUtil.strRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotEmpty(businessType)) {
            StCBusinessType stCBusinessType = JSONObject.parseObject(businessType, StCBusinessType.class);
            return stCBusinessType;
        }
        List<StCBusinessType> stCBusinessTypes = stCBusinessTypeMapper.selectStCBusinessTypeByThirdCode(orderType, thirdCode);
        if (CollectionUtils.isNotEmpty(stCBusinessTypes)) {
            StCBusinessType stCBusinessType = stCBusinessTypes.get(0);
            if (stCBusinessType != null) {
                String str = JSONObject.toJSONString(stCBusinessType);
                redisOpsUtil.strRedisTemplate.opsForValue().set(redisKey, str);
            }
            return stCBusinessType;
        }
        return null;
    }

    public boolean isAutoOccupy(OcBOrder order){
        Long businessTypeId = order.getBusinessTypeId();
        if (businessTypeId == null){
            return true;
        }
        StCBusinessType stCBusinessType = this.selectStCBusinessTypeById(businessTypeId);
        if (stCBusinessType == null) {
            return true;
        }
        Integer isSourceOccupy = stCBusinessType.getIsSourceOccupy();
        if (isSourceOccupy == null){
            return true;
        }
        //是否自动寻源占单
        if (1 == isSourceOccupy){
            return true;
        }
        return false;
    }
}
