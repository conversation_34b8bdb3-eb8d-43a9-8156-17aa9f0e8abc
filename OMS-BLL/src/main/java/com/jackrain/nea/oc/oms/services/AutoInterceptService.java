package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderWarningMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderWarning;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName AutoInterceptService
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/11 10:20
 * @Version 1.0
 */
@Component
@Slf4j
public class AutoInterceptService {

    @Autowired
    private ZtoLogisticsInterceptService logisticsInterceptService;
    @Autowired
    private OcBReturnOrderWarningMapper returnOrderWarningMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Async("commonTaskExecutor")
    public void autoIntercept(List<OcBReturnOrderWarning> inserts) {
        List<Long> successIntercept = new ArrayList<>();
        for (OcBReturnOrderWarning ocBReturnOrderWarning : inserts) {
            // 判断warn type
            if (ObjectUtil.notEqual(1, ocBReturnOrderWarning.getWarningType())) {
                continue;
            }
            try {
                ValueHolderV14<Void> valueHolderV14 = logisticsInterceptService.autoIntercept(ocBReturnOrderWarning.getOrderId(), ocBReturnOrderWarning.getTReturnId());
                if (!valueHolderV14.isOK()) {
                    log.info("AutoInterceptService autoIntercept msg {}", valueHolderV14.getMessage());
                }

                // 如果拦截成功的话 标记为已处理
                if (valueHolderV14.isOK()) {
                    successIntercept.add(ocBReturnOrderWarning.getId());
                } else {
                    // 没拦截的话 也要打印操作日志
                    omsOrderLogService.addUserOrderLog(ocBReturnOrderWarning.getOrderId(),
                            ocBReturnOrderWarning.getOrderBillNo(), OrderLogTypeEnum.INTERCEPT_RETURN.getKey(),
                            valueHolderV14.getMessage(), "", "", SystemUserResource.getRootUser());
                    OcBReturnOrderWarning updateWarning = new OcBReturnOrderWarning();
                    updateWarning.setId(ocBReturnOrderWarning.getId());
                    updateWarning.setModifieddate(new Date());
                    updateWarning.setSysremark(valueHolderV14.getMessage());
                    returnOrderWarningMapper.updateById(updateWarning);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("AutoInterceptService autoIntercept error {}", e.getMessage());
            }
        }
        if (CollectionUtils.isNotEmpty(successIntercept)) {
            User user = SystemUserResource.getRootUser();
            returnOrderWarningMapper.batchUpdateNew(successIntercept, user.getId(), user.getName(), OcBOrderConst.IS_ACTIVE_YES);
        }

    }
}
