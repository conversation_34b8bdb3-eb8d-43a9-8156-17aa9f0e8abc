package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCVipcomWahouse;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryRecordMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.constant.VipConstant;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryRecord;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * className: IpBJitxDeliveryRecordService
 * description: JITX寻仓结果表
 *
 * <AUTHOR>
 * create: 2021-12-16
 * @since JDK 1.8
 */
@Component
@Slf4j
public class IpBJitxDeliveryRecordService {

    @Autowired
    private IpBJitxDeliveryRecordMapper deliveryRecordMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OcBJitxDealerOrderTaskService jitxDealerOrderTaskService;

    @Autowired
    private OcBJitxModifyWarehouseLogService jitxModifyWarehouseLogService;

    @Autowired
    private OcBOrderTheAuditService theAuditService;


    public ValueHolderV14<Long> saveRecord(IpBJitxDeliveryRecord record, User user){
        if(log.isDebugEnabled()){
            log.debug(" 保存JITX寻仓结果入参：{}", JSON.toJSONString(record));
        }

        Long id = record.getId();
        CommandAdapterUtil.defaultOperator(record,user);

        //查询中台唯品仓档案
        if(!ObjectUtils.isEmpty(record.getVipStoreCode())){
            CpCVipcomWahouse cpCVipcomWahouse = cpRpcService.selectByCode(record.getVipStoreCode());
            if(!ObjectUtils.isEmpty(cpCVipcomWahouse)){
                record.setVipStoreId(cpCVipcomWahouse.getId());
                if(ObjectUtils.isEmpty(record.getVipStoreName())){
                    record.setVipStoreName(cpCVipcomWahouse.getWarehouseName());
                }
            }
        }

        if (ObjectUtils.isEmpty(id) || id < 0) {
            //新增
            id = ModelUtil.getSequence(OcCommonConstant.IP_B_JITX_DELIVERY_RECORD);
            record.setId(id);
            record.setOwnerename(user.getEname());
            record.setModifierename(user.getEname());
            deliveryRecordMapper.insert(record);
        }else {
            IpBJitxDeliveryRecord old = deliveryRecordMapper.selectById(record.getId());
            if(ObjectUtils.isEmpty(old)){
                return new ValueHolderV14<>(ResultCode.FAIL,"当前记录已不存在！");
            }

            //更新
            record.setModifierename(user.getEname());
            deliveryRecordMapper.updateById(record);
        }
        return new ValueHolderV14<>(id,ResultCode.SUCCESS,"保存成功");
    }

    public ValueHolderV14<Map<String,Long>> batchInsert(List<IpBJitxDeliveryRecord> recordList, User user){
        if(log.isDebugEnabled()){
            log.debug(" 保存JITX寻仓结果入参：{}", JSON.toJSONString(recordList));
        }

        if(CollectionUtils.isEmpty(recordList)){
            return new ValueHolderV14<>(ResultCode.FAIL,"参数为空");
        }
        Map<String,Long> resultMap = new HashMap<>();

        //查询中台唯品仓档案
        Map<String,CpCVipcomWahouse> wareMap = new HashMap<>();
        List<CpCVipcomWahouse> wahouseList = cpRpcService.selectAllVipWarehouse();
        if(!CollectionUtils.isEmpty(wahouseList)){
            wahouseList.forEach(ware -> wareMap.put(ware.getWarehouseCode(),ware));
        }

        recordList.forEach(record ->{

            CommandAdapterUtil.defaultOperator(record,user);

            //查询中台唯品仓档案
            if(!ObjectUtils.isEmpty(record.getVipStoreCode())){
                CpCVipcomWahouse cpCVipcomWahouse = wareMap.get(record.getVipStoreCode());
                if(!ObjectUtils.isEmpty(cpCVipcomWahouse)){
                    record.setVipStoreId(cpCVipcomWahouse.getId());
                    if(ObjectUtils.isEmpty(record.getVipStoreName())){
                        record.setVipStoreName(cpCVipcomWahouse.getWarehouseName());
                    }
                }
            }

            //新增
            Long id = ModelUtil.getSequence(OcCommonConstant.IP_B_JITX_DELIVERY_RECORD);
            record.setId(id);
            record.setOwnerename(user.getEname());
            record.setModifierename(user.getEname());
            if(!ObjectUtils.isEmpty(record.getTid())){
                resultMap.put(record.getTid(),id);
            }
        });


        //根据返回的不同状态做不同的操作
        this.deliveryCallback(recordList,user);

        deliveryRecordMapper.batchInsert(recordList);

        return new ValueHolderV14<>(resultMap,ResultCode.SUCCESS,"保存成功");
    }


    public ValueHolderV14<Map<String,String>> deliveryCallback(List<IpBJitxDeliveryRecord> recordList, User user){

        if(log.isDebugEnabled()){
            log.debug(" 更新经销商任务入参：{}",JSON.toJSONString(recordList));
        }

        ValueHolderV14<Map<String,String>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS,"操作成功");
        if(CollectionUtils.isEmpty(recordList)){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("参数不能为空");
            return v14;
        }
        Map<String,String> resultMap = new HashMap<>();
        recordList.forEach(record -> {
            try {
                IpBJitxDeliveryRecordService recordService = ApplicationContextHandle.getBean(IpBJitxDeliveryRecordService.class);
                ValueHolderV14<String> singlrResult = recordService.singleAction(record,user);
                if(!singlrResult.isOK()){
                    resultMap.put(record.getTid(),singlrResult.getMessage());
                }
            }catch (Exception e){
                log.error(" 更新经销商任务记录异常：{}", Throwables.getStackTraceAsString(e));
                resultMap.put(record.getTid(),e.getMessage());
            }
        });

        if(!resultMap.isEmpty()){
            v14.setMessage("操作成功" + (recordList.size()-resultMap.size()) + "条，失败" + resultMap.size() + "条");
            v14.setData(resultMap);
        }

        if(log.isDebugEnabled()){
            log.debug(" 更新经销商任务结果：{}",JSON.toJSONString(v14));
        }

        return v14;
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<String> singleAction(IpBJitxDeliveryRecord record ,User user){

        ValueHolderV14<String> valueHolderV14 = new ValueHolderV14<>(ResultCode.FAIL,"更新失败，请检查YY寻仓结果状态");
        //换仓状态--申请换仓，新增换仓中间表记录
        if(!ObjectUtils.isEmpty(record.getTransStatus()) &&
                VipConstant.JITX_DELIVERY_RECORD_EXCHANGING.equals(record.getTransStatus())){
            List<OcBOrder> ocBOrderList = ocBOrderMapper.getOrdersUnionGsiBySourceCode(record.getTid());
            ocBOrderList = ocBOrderList.stream().filter(o ->R3CommonResultConstants.VALUE_Y.equals(o.getIsactive())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(ocBOrderList)){
                valueHolderV14.setMessage(record.getTid() + "查询原单为空");
                return valueHolderV14;
            }

            //查询需换仓的实体仓
            List<CpCStore> stores = cpRpcService.selectStoresByCodes(Collections.singletonList(record.getStoreCode()));
            if(CollectionUtils.isEmpty(stores)){
                valueHolderV14.setMessage(record.getStoreCodeName() + "查询逻辑仓为空");
                return valueHolderV14;
            }
            OcBOrder ocBOrder = ocBOrderList.get(0);
            CpCStore store = stores.get(0);
            Long oldWarehouseId = ocBOrder.getCpCPhyWarehouseId();
            String oldWarehouseEcode = ocBOrder.getCpCPhyWarehouseEcode();
            String oldWarehouseEname = ocBOrder.getCpCPhyWarehouseEname();
            ocBOrder.setCpCPhyWarehouseId(store.getCpCPhyWarehouseId());
            ocBOrder.setCpCPhyWarehouseEcode(store.getCpCPhyWarehouseEcode());
            ocBOrder.setCpCPhyWarehouseEname(store.getCpCPhyWarehouseEname());

            jitxModifyWarehouseLogService.createByOrder(ocBOrder,true,oldWarehouseId,oldWarehouseEcode,oldWarehouseEname,
                    record.getVipStoreCode(),user);

            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("操作成功");
            return valueHolderV14;
        }

        //单据状态--已占单，更新经销商任务记录
        if(!ObjectUtils.isEmpty(record.getOccupyStatus()) &&
                VipConstant.JITX_DELIVERY_RECORD_STATUS_OCCUPIED.equals(record.getOccupyStatus())){
            int result = jitxDealerOrderTaskService.updateByTypeAndTid(record.getTid(), JitxDealerTaskTypeEnum.YY_OCCUPY,
                    JitxDealerTaskStatusEnum.SUCCESS,user);
            //更新改仓中间表的发货后仓库
            jitxModifyWarehouseLogService.writeVopWarehouse(record.getTid(),record.getStoreCode(),record.getVipStoreCode());
            return buildValueHolder(result,valueHolderV14);
        }

        //单据状态--取消占单成功/失败，更新JITX经销商任务记录
        if(!ObjectUtils.isEmpty(record.getOccupyStatus()) &&
                (VipConstant.JITX_DELIVERY_RECORD_STATUS_CANCEL_FAIL.equals(record.getOccupyStatus()) ||
                        VipConstant.JITX_DELIVERY_RECORD_STATUS_CANCELED.equals(record.getOccupyStatus()))){

            int result = jitxDealerOrderTaskService.updateByTypeAndTid(record.getTid(), JitxDealerTaskTypeEnum.YY_CANCEL_OCCUPY,
                    VipConstant.JITX_DELIVERY_RECORD_STATUS_CANCELED.equals(record.getOccupyStatus()) ?
                            JitxDealerTaskStatusEnum.SUCCESS : JitxDealerTaskStatusEnum.FAIL,user);

            //取消成功，如果存在反审核中的零售单，反审核该零售单
            //取消失败，如果存在反审核中的零售单，状态改为已审核
            theAuditService.updateJitxDealerOrderAfterBackAudit(record.getTid(),
                    VipConstant.JITX_DELIVERY_RECORD_STATUS_CANCELED.equals(record.getOccupyStatus()));
            return buildValueHolder(result,valueHolderV14);
        }

        return valueHolderV14;
    }


    private ValueHolderV14<String> buildValueHolder(int result,ValueHolderV14<String> valueHolderV14){
        if(result > 0){
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("更新成功");
        }else {
            valueHolderV14.setMessage("更新失败");
        }
        return valueHolderV14;
    }

    //零售单审核校验
    public boolean canAudit(OcBOrderRelation orderInfo){
        if(jitxDealerOrderTaskService.isJitxDealerYYWarehouse(orderInfo.getOrderInfo().getCpCPhyWarehouseId())){
            return !CollectionUtils.isEmpty(selectRecordOccupied(orderInfo.getOrderInfo().getTid()));
        }else {
            return true;
        }
    }

    public List<IpBJitxDeliveryRecord> selectRecordOccupied(String tid){
        return deliveryRecordMapper
                .selectList(new LambdaQueryWrapper<IpBJitxDeliveryRecord>()
                        .eq(IpBJitxDeliveryRecord::getTid,tid)
                        .eq(IpBJitxDeliveryRecord::getIsactive,R3CommonResultConstants.VALUE_Y)
                        .eq(IpBJitxDeliveryRecord::getOccupyStatus,VipConstant.JITX_DELIVERY_RECORD_STATUS_OCCUPIED));
    }
}
