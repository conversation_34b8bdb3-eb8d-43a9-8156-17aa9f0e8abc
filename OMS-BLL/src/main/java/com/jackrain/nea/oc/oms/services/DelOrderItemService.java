package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


/**
 * @Author: wangqiang
 * @Date: 2019-03-04 17:36
 * @Version 1.0
 */
@Slf4j
@Component
public class DelOrderItemService {
    @Autowired
    OcBOrderItemMapper ocBOrderItemFiMapper;

    @Autowired
    SaveBillService saveBillService;

    @Autowired
    OcBOrderMapper ocBOrderMapper;

    public ValueHolder delOrderItem(JSONObject obj, User user) {
        ValueHolder vh = new ValueHolder();

        JSONObject ocBorderDto = obj.getJSONObject("ocBorderDto");
        //订单ID
        Long orderId = ocBorderDto.getLong("ID");
        //订单
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
        if (ocBOrder == null) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("请先保存后再删除!", user.getLocale()));
            return vh;
        }
        JSONArray ocBorderItemArray = obj.getJSONArray("ocBorderItemDto");
        //转成数组
        if (ocBorderItemArray == null || ocBorderItemArray.size() == 0) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("请选择需要删除的明细!", user.getLocale()));
            return vh;
        }
        //将json 数组转为对象
        List<OcBOrderItem> orderItemResults = JSONObject.parseArray(
                ocBorderItemArray.toJSONString(),
                OcBOrderItem.class);
        Integer num = 0;
        Integer flag = 0;
        for (OcBOrderItem orderItemResult : orderItemResults) {
            Long orderItemId = orderItemResult.getId();
            //删除明细
            Integer count = ocBOrderItemFiMapper.deleteByItemId(orderItemId, orderId);
            if (count > 0) {
                //删除明细成功之后,删除ES中明细
                try {
                    SpecialElasticSearchUtil.delDocument(
                            OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                            OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME,
                            orderItemId, orderId);

                    //删除明细之后,重新计算价格
                    List<OcBOrderItem> ocBOrderItems = ocBOrderItemFiMapper.selectOrderItemList(orderId);
                    OcBOrder order = saveBillService.updateOrderInfo(user, ocBOrder, ocBOrderItems);

                    order.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_ALL.getVal());

                    order.setId(orderId);
                    order.setModifierename(user.getName());
                    ocBOrder.setModifieddate(new Date(System.currentTimeMillis()));
                    int update = ocBOrderMapper.updateById(order);
                    if (update > 0) {
                        //计算价格之后更新ES
                        SpecialElasticSearchUtil.indexDocument(
                                OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                                OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME,
                                order, orderItemId,
                                orderId);
                    }

                } catch (Exception e) {
                    log.error(LogUtil.format("删除明细之后更新ES异常: {}"), Throwables.getStackTraceAsString(e));
                }
                num += 1;
                vh.put("code", ResultCode.SUCCESS);
            } else {
                vh.put("code", ResultCode.FAIL);
                flag += 1;
            }

        }

        vh.put("message", Resources.getMessage(String.format("%s条明细删除成功,%s条明细删除失败", num, flag), user.getLocale()));
        return vh;
    }


}
