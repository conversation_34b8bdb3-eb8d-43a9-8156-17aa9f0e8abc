package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.IpBJitxOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Description JITX退款转换服务类
 * @Date 2019-6-26
 **/
@Component
@Slf4j
public class IpJitxRefundService {

    @Autowired
    private IpBJitxOrderMapper ipJitxOrderMapper;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private MarkRefundService markRefundService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新退款状态
     * @Date 2019-6-26
     * @Param [transferOrderStatus, ipBJitxOrder]
     **/
    public boolean updateRefundIsTrans(TransferOrderStatus transferOrderStatus, String sysRemark, IpBJitxOrder ipBJitxOrder) {
        IpJitxRefundService bean = ApplicationContextHandle.getBean(IpJitxRefundService.class);
        boolean flag = bean.updateReturnOrder(transferOrderStatus.toInteger(), sysRemark, ipBJitxOrder);
        return flag;
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新退款状态出现异常
     * @Date 2019-6-26
     * @Param [ipBJitxOrder, error]
     **/
    public boolean updateRefundIsTransError(IpBJitxOrder ipBJitxOrder, String error) {
        IpJitxRefundService bean = ApplicationContextHandle.getBean(IpJitxRefundService.class);
        String sysRemark = SysNotesConstant.SYS_REMARK0;
        //异常信息超过500 截取500
        String str = sysRemark + error;
        if (str.length() > 500) {
            str = str.substring(0, 500);
        }
        boolean flag = bean.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), str, ipBJitxOrder);
        return flag;
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 根据JITX退款单据号查询全渠道订单
     * @Date 2019-6-26
     * @Param [sourceOrderNo]
     **/
    public List<OcBOrder> selectOmsOrder(String sourceOrderNo) {
        return omsOrderService.selectOmsOrderInfoList(sourceOrderNo);
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新全渠道订单主表信息
     * @Date 2019-7-3
     * @Param [ocBOrderDto]
     **/
    public boolean updateOmsOrder(OcBOrder ocBOrderDto) {
        // 订单hold 或释放hold单
        ocBOrderHoldService.holdOrUnHoldOrder(ocBOrderDto, OrderHoldReasonEnum.REFUND_HOLD);
        OcBOrder ocBOrder = new OcBOrder();
        BeanUtils.copyProperties(ocBOrderDto, ocBOrder);
        ocBOrder.setIsInterecept(null);// 拦截标记统一在HOLD单方法里做
        return omsOrderService.updateOrderInfo(ocBOrderDto);
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新退款转换状态方法
     * @Date 2019-6-26
     * @Param [istrans, sysRemark, ipBJitxOrder]
     **/
    @Transactional(rollbackFor = Exception.class)
    public boolean updateReturnOrder(int istrans, String sysRemark, IpBJitxOrder ipBJitxOrder) {
        try {

            int i = ipJitxOrderMapper.updateOrderIsTrans(ipBJitxOrder.getOrderSn(), istrans, true, sysRemark);

            if (i > 0) {
                 /* 推送es
                ipBJitxOrder.setIstrans(istrans);
                ipBJitxOrder.setSysremark(sysRemark);
                SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.IP_B_JITX_ORDER_INDEX_NAME,
                        OcElasticSearchIndexResources.IP_B_JITX_ORDER_TYPE_NAME,
                        ipBJitxOrder, ipBJitxOrder.getId());*/
                return true;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新退货中间表失败,OrderSn:{}", ipBJitxOrder.getOrderSn(), e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 执行标记为退款完成服务
     * @Date 2019-6-27
     * @Param [order, ocBOrder]
     **/
    public ValueHolderV14 markRefund(String tid, OcBOrder ocBOrder, User operateUser) {
        List<Long> itemIds = ocBOrderItemMapper.queryIdByTidAndOrderId(tid, ocBOrder.getId());
        ValueHolderV14 vh14 = new ValueHolderV14();
        if (CollectionUtils.isEmpty(itemIds)) {
            vh14.setCode(ResultCode.FAIL);
            vh14.setMessage("转换失败，全渠道订单没有明细！");
            return vh14;
        } else {
            StringBuilder builder = new StringBuilder();
            for (int j = 0; j < itemIds.size(); j++) {
                Long itemId = itemIds.get(j);
                if (j < itemIds.size() - 1) {
                    builder.append(itemId + ",");
                } else {
                    builder.append(itemId);
                }
            }
            JSONObject ids = new JSONObject();
            ids.put("IDS", builder.toString());
            try {
                vh14 = markRefundService.markRefund(ids, operateUser);
            } catch (Exception e) {
                log.debug(this.getClass().getName() + " 标记退款完成服务：" + e);
                vh14.setCode(ResultCode.FAIL);
                vh14.setMessage("标记退款完成服务异常！");
            }
            return vh14;
        }
    }
}
