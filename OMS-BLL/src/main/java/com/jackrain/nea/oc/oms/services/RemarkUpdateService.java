package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.model.TradeMemoUpdateModel;
import com.jackrain.nea.ip.model.others.OrderRemarkSyncModel;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @author: 李杰
 * @since: 2019/3/11
 * create at : 2019/3/11 11:34
 */
@Slf4j
@Component
public class RemarkUpdateService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private StCRemarkGiftStrategyService stCRemarkGiftStrategyService;

    public ValueHolder remarkUpdate(JSONObject obj, User user) {

        ValueHolder vh = new ValueHolder();
        //ids
        String ids = obj.getString("ids");
        String remark = obj.getString("remark");
        String orderFlag = obj.getString("order_flag");
        //是否覆盖原备注
        String cover = obj.getString("cover");
        //转成数组
        String[] idArray = ids.split(",");
        Integer num = 0;
        Integer flag = 0;
        String res = null;
        for (int i = 0; i < idArray.length; i++) {
            Long id = Long.valueOf(idArray[i]);
            //获取原订单信息
            OcBOrder ocBorder = ocBOrderMapper.selectById(id);
            if (StringUtils.isBlank(orderFlag)) {
                orderFlag = ocBorder.getOrderFlag();
            }
            if (ocBorder == null) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "订单不存在");
                return vh;
            }
            String syncRemark = "";
            Integer platform = ocBorder.getPlatform();
            //原卖家备注
            String sellerMemo = ocBorder.getSellerMemo();
            //覆盖原卖家备注
            if ("true".equals(cover)) {
                OcBOrder order = new OcBOrder();
                order.setId(id);
                order.setOrderFlag(orderFlag);
                order.setSellerMemo(remark);
                order.setModifierid(user.getId().longValue());
                order.setModifiername(user.getName());
                order.setModifierename(user.getEname());
                order.setModifieddate(new Date());
                Integer count = ocBOrderMapper.updateById(order);
                if (count > 0) {
                    num += 1;
                    vh.put("code", ResultCode.SUCCESS);
                } else {
                    vh.put("code", ResultCode.FAIL);
                    flag += 1;
                }
                syncRemark = remark;
                try {
                    // 调用订单日志服务
                    omsOrderLogService.addUserOrderLog(ocBorder.getId(), ocBorder.getBillNo(), OrderLogTypeEnum.SELLERMEMO_UPDATE.getKey(),
                            "卖家备注：修改前：" + sellerMemo + "，修改后：" + remark, null,
                            null, user);
                } catch (Exception e) {
                    log.error(LogUtil.format("修改卖家备注调用订单日志服务失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                }
            }
            //追加原卖家备注
            if ("false".equals(cover)) {
                OcBOrder order = new OcBOrder();
                sellerMemo = sellerMemo == null ? " " : sellerMemo;
                String sellerRemark = sellerMemo + " " + remark;
                order.setId(id);
                order.setOrderFlag(orderFlag);
                order.setSellerMemo(sellerRemark);
                order.setModifierid(user.getId().longValue());
                order.setModifiername(user.getName());
                order.setModifierename(user.getEname());
                order.setModifieddate(new Date());
                // 自动打标：改单
                order.setIsModifiedOrder(1);
                Integer count = ocBOrderMapper.updateById(order);
                if (count > 0) {
                    num += 1;
                    vh.put("code", ResultCode.SUCCESS);
                } else {
                    vh.put("code", ResultCode.FAIL);
                    flag += 1;
                }
                syncRemark = sellerRemark;
                try {
                    // 调用订单日志服务
                    omsOrderLogService.addUserOrderLog(ocBorder.getId(), ocBorder.getBillNo(), OrderLogTypeEnum.SELLERMEMO_UPDATE.getKey(),
                            "卖家备注：修改前：" + sellerMemo + "，修改后：" + remark, null,
                            null, user);
                } catch (Exception e) {
                    log.error(LogUtil.format("修改卖家备注调用订单日志服务失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                }
            }
            //调用淘宝修改卖家备注接口   77 经销   3 分销
            if (platform == 2 || platform == 77 || platform == 3) {
                TradeMemoUpdateModel request = new TradeMemoUpdateModel();
                ValueHolderV14 valueHolderV14 = new ValueHolderV14();
                try {
                    String shopSecretKey = cpRpcService.selectShopById(ocBorder.getCpCShopId()).getShopSecretKey();
                    String[] split = shopSecretKey.split("\\|");
                    String[] split1 = split[2].split(":");
                    String sessionKey = split1[1];
                    request.setOperateUser(user);
                    request.setTid(Long.valueOf(ocBorder.getTid()));
                    request.setFlag(Long.valueOf(ocBOrderMapper.selectById(id).getOrderFlag()));
                    request.setMemo(ocBOrderMapper.selectById(id).getSellerMemo());
                    request.setSessionKey(sessionKey);
                    request.setPlatform(platform);
                    valueHolderV14 = ipRpcService.tradeMemoUpdate(request);
                } catch (Exception e) {
                    log.error(LogUtil.format("调用淘宝修改备注接口异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    OcBOrder order = new OcBOrder();
                    order.setId(id);
                    order.setSysremark("修改备注同步平台异常:");
                    ocBOrderMapper.updateById(order);
                }
                if (valueHolderV14.getCode() == -1) {
                    OcBOrder order = new OcBOrder();
                    order.setId(id);
                    order.setSysremark("淘宝修改备注同步平台失败:" + valueHolderV14.getMessage());
                    ocBOrderMapper.updateById(order);
                }
            }
            if (platform == 4) {
                TradeMemoUpdateModel request = new TradeMemoUpdateModel();
                ValueHolderV14 valueHolderV14 = new ValueHolderV14();
                try {
                    request.setOperateUser(user);
                    request.setTid(Long.valueOf(ocBorder.getTid()));
                    request.setFlag(Long.valueOf(ocBOrderMapper.selectById(id).getOrderFlag()));
                    request.setMemo(ocBOrderMapper.selectById(id).getSellerMemo());
                    request.setSellerNick(ocBorder.getCpCShopSellerNick());
                    request.setPlatform(platform);
                    valueHolderV14 = ipRpcService.tradeMemoUpdate(request);
                } catch (Exception e) {
                    log.error(LogUtil.format("调用京东修改备注接口异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    OcBOrder order = new OcBOrder();
                    order.setId(id);
                    order.setSysremark("修改备注同步平台异常:");
                    ocBOrderMapper.updateById(order);
                }
                if (valueHolderV14.getCode() == -1) {
                    OcBOrder order = new OcBOrder();
                    order.setId(id);
                    order.setSysremark("京东修改备注同步平台失败:" + valueHolderV14.getMessage());
                    ocBOrderMapper.updateById(order);
                }
            }

            //抖音、拼多多修改备注同步平台
            if (PlatFormEnum.DOU_YIN.getCode().equals(platform) || PlatFormEnum.PINDUODUO.getCode().equals(platform) || PlatFormEnum.KUAISHOU.getCode().equals(platform)) {
                ValueHolderV14 valueHolderV14 = new ValueHolderV14();
                try {
                    OrderRemarkSyncModel model = new OrderRemarkSyncModel();
                    model.setPlatform(String.valueOf(platform));
                    model.setSellerNick(ocBorder.getCpCShopSellerNick());
                    model.setRemark(syncRemark);
                    model.setTid(ocBorder.getTid());
                    model.setIsAddStar("true");
                    model.setStar(orderFlag);
                    valueHolderV14 = ipRpcService.orderRemarkSync(model);
                } catch (Exception e) {
                    log.error(LogUtil.format("调用通用订单修改备注接口异常,id:{},异常信息为:{}"), id, Throwables.getStackTraceAsString(e));
                    OcBOrder order = new OcBOrder();
                    order.setId(id);
                    order.setSysremark("修改备注同步平台异常:");
                    ocBOrderMapper.updateById(order);
                }
                if (valueHolderV14.getCode() == -1) {
                    OcBOrder order = new OcBOrder();
                    order.setId(id);
                    order.setSysremark("通用订单修改备注同步平台失败:" + valueHolderV14.getMessage());
                    ocBOrderMapper.updateById(order);
                }
            }

            //卖家备注添加赠品
            stCRemarkGiftStrategyService.addGiftSku(ocBorder.getCpCShopId(), ocBorder.getTid());
        }

        vh.put("message", Resources.getMessage(String.format("%s条备注修改成功,%s条备注修改失败，", num, flag),
                user.getLocale()));
        return vh;
    }
    /**
     * <AUTHOR>
     * @Date 15:53 2021/8/13
     * @Description 批量修改内部
     */
    public ValueHolder bacthUpdateInsideRemark(JSONObject obj, User user) {
        ValueHolder vh = new ValueHolder();
        //ids
        String ids = obj.getString("ids");
        String insideRemark = obj.getString("insideRemark");
        String orderFlag = obj.getString("order_flag");
        //是否覆盖原备注
        String cover = obj.getString("cover");
        //转成数组
        String[] idArray = ids.split(",");
        Integer num = 0;
        Integer flag = 0;
        for (int i = 0; i < idArray.length; i++) {
            Long id = Long.valueOf(idArray[i]);
            //获取原订单信息
            OcBOrder ocBorder = ocBOrderMapper.selectById(id);
            if (StringUtils.isBlank(orderFlag)) {
                orderFlag = ocBorder.getOrderFlag();
            }
            if (ocBorder == null) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "订单不存在");
                return vh;
            }
            //原卖家备注
            String oldInsideRemark = ocBorder.getInsideRemark();
            //覆盖原卖家备注
            if ("true".equals(cover)) {
                OcBOrder order = new OcBOrder();
                order.setId(id);
                order.setOrderFlag(orderFlag);
                order.setInsideRemark(insideRemark);
                order.setModifierid(user.getId().longValue());
                order.setModifiername(user.getName());
                order.setModifierename(user.getEname());
                order.setModifieddate(new Date());
                Integer count = ocBOrderMapper.updateById(order);
                if (count > 0) {
                    num += 1;
                    vh.put("code", ResultCode.SUCCESS);
                } else {
                    vh.put("code", ResultCode.FAIL);
                    flag += 1;
                }
                try {
                    // 调用订单日志服务
                    omsOrderLogService.addUserOrderLog(ocBorder.getId(), ocBorder.getBillNo(), OrderLogTypeEnum.INSIDE_REMARK_UPDATE.getKey(),
                            "内部备注：修改前：" + oldInsideRemark + "，修改后：" + insideRemark, null,
                            null, user);
                } catch (Exception e) {
                    log.debug("修改卖家备注调用订单日志服务失败" + e.getMessage());
                }
            }
            //追加原卖家备注
            if ("false".equals(cover)) {
                OcBOrder order = new OcBOrder();
                oldInsideRemark = oldInsideRemark == null ? " " : oldInsideRemark;
                String sellerRemark = oldInsideRemark + " " + insideRemark;
                order.setId(id);
                order.setOrderFlag(orderFlag);
                order.setInsideRemark(sellerRemark);
                order.setModifierid(user.getId().longValue());
                order.setModifiername(user.getName());
                order.setModifierename(user.getEname());
                order.setModifieddate(new Date());
                Integer count = ocBOrderMapper.updateById(order);
                if (count > 0) {
                    num += 1;
                    vh.put("code", ResultCode.SUCCESS);
                } else {
                    vh.put("code", ResultCode.FAIL);
                    flag += 1;
                }
                try {
                    // 调用订单日志服务
                    omsOrderLogService.addUserOrderLog(ocBorder.getId(), ocBorder.getBillNo(), OrderLogTypeEnum.INSIDE_REMARK_UPDATE.getKey(),
                            "内部备注：修改前：" + oldInsideRemark + "，修改后：" + insideRemark, null,
                            null, user);
                } catch (Exception e) {
                    log.error(LogUtil.format("修改内部备注调用订单日志服务失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                }
            }
            vh.put("message", Resources.getMessage(String.format("%s条内部备注修改成功,%s条内部备注修改失败，", num, flag),
                    user.getLocale()));
        }
        return vh;
    }
}
