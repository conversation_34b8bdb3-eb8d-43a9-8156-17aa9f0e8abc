package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.es.ES4OrderPromotion;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPromotionMapper;
import com.jackrain.nea.oc.oms.model.relation.MergeOrderPromotionQbModel;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: LIQB
 * @create: 2019-07-10 11:50
 */
@Slf4j
@Component
public class QueryMergeOrderPromotionQbService {

    @Autowired
    private OcBOrderPromotionMapper ocBOrderPromotionMapper;

    /**
     * 查询优惠订单信息
     *
     * @param querySession
     * @return
     */
    public ValueHolder queryMergeOrderPromotionQb(QuerySession querySession) {
        ValueHolder result = new ValueHolder();
        JSONObject resultData = new JSONObject();
        try {
            DefaultWebEvent event = querySession.getEvent();
            JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
            JSONObject fixedcolumns = param.getJSONObject("fixedcolumns");
            MergeOrderPromotionQbModel orderParam = checkParam(fixedcolumns);
            //开始条数
            String startindex = param.getString("startindex");
            //每页多少条
            if (StringUtils.isEmpty(startindex)) {
                orderParam.setStartindex(0);
            } else {
                orderParam.setStartindex(Integer.parseInt(startindex));
            }
            List<MergeOrderPromotionQbModel> mergeOrderModelList = this.queryMergeOrderListPromotionQb(orderParam);
            if (CollectionUtils.isEmpty(mergeOrderModelList)) {
                resultData.put("row", new JSONArray());
                resultData.put("totalRowCount", 0);
                resultData.put("disablePagination", true);
                result.put("data", resultData);
                result.put("code", 0);
                result.put("message", "success");
                return result;
            }
            JSONArray jsonArray = (JSONArray) JSONArray.toJSON(mergeOrderModelList);
            List<JSONObject> jsonObjectList = JSONObject.parseArray(
                    JSONObject.toJSONString(jsonArray, SerializerFeature.WriteMapNullValue), JSONObject.class);
            JSONArray getFrameDataFormat = getFrameDataFormat(jsonObjectList);
            resultData.put("start", 0);
            resultData.put("row", getFrameDataFormat);
            resultData.put("totalRowCount", mergeOrderModelList.size());
            resultData.put("disablePagination", true);
            result.put("data", resultData);
            result.put("code", 0);
            result.put("message", "success");
            return result;
        } catch (Exception e) {
            log.error(LogUtil.format("优惠订单列表查询异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            resultData.put("row", new JSONArray());
            resultData.put("totalRowCount", 0);
            resultData.put("disablePagination", true);
            result.put("data", resultData);
            result.put("code", 0);
            result.put("message", "success");
            return result;
        }
    }

    /**
     * 用于前端接口查询使用
     *
     * @param mergeOrderModel
     * @return
     */
    public List<MergeOrderPromotionQbModel> queryMergeOrderListPromotionQb(MergeOrderPromotionQbModel mergeOrderModel) {
        JSONArray aryIds = ES4OrderPromotion.findJSONArrayByWhereKeys(mergeOrderModel);
        List<Integer> ids = Lists.newArrayList();
        for (int i = 0; i < aryIds.size(); i++) {
            Map<String, Integer> map = (Map<String, Integer>) aryIds.get(i);
            ids.add(map.get("ID"));
        }
        List<MergeOrderPromotionQbModel> mergeOrderModelList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(ids)) {
            mergeOrderModelList = ocBOrderPromotionMapper.selectMergeOrderPromotionQbByIds(ids);
        }
        return mergeOrderModelList;
    }

    /**
     * where 条件
     *
     * @param mergeOrderModel
     * @return
     */
    public JSONObject getWhereKey(MergeOrderPromotionQbModel mergeOrderModel) {
        JSONObject whereKeys = new JSONObject();//查询条件
        /*if (null != mergeOrderModel.getGiftItemCode()) {
            whereKeys.put("GIFT_ITEM_CODE", mergeOrderModel.getGiftItemCode());
        }*/
        if (null != mergeOrderModel.getPromotionName()) {
            whereKeys.put("PROMOTION_NAME", mergeOrderModel.getPromotionName());
        }
        return whereKeys;
    }


    /**
     * 过滤条件
     *
     * @param mergeOrderModel
     * @return
     */
    public JSONObject checkFilter(MergeOrderPromotionQbModel mergeOrderModel) {
        JSONObject filterJsonObject = new JSONObject();
        String createDate = this.checkDate(mergeOrderModel.getBeginOrderDate(), mergeOrderModel.getEndOrderDate());
        if (StringUtils.isNotEmpty(createDate)) {
            filterJsonObject.put("CREATIONDATE", createDate);
            return filterJsonObject;
        }
        return null;
    }

    /**
     * 日期校验
     *
     * @return
     */
    public String checkDate(String begindate, String endDate) {
        //type = 1 开始日期和es的下单日期比较
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        if (StringUtils.isEmpty(begindate) || StringUtils.isEmpty(endDate)) {
            return "";
        }
        try {
            String result = sdf.parse(begindate).getTime() + "~" + sdf.parse(endDate).getTime();
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";

    }

    /**
     * 框架格式返回
     *
     * @param dataList
     * @return
     */
    private static JSONArray getFrameDataFormat(List<JSONObject> dataList) {
        JSONArray array = new JSONArray();
        if (dataList != null && dataList.size() > 0) {
            for (JSONObject emp : dataList) {
                Set<String> keySet = emp.keySet();
                JSONObject json = new JSONObject();
                for (String key : keySet) {
                    JSONObject val = new JSONObject();
                    val.put("val", emp.get(key));
                    json.put(key.toUpperCase(), val);
                }
                array.add(json);
            }
        }
        return array;
    }

    /**
     * 参数校验+拼接
     *
     * @param fixedcolumns
     * @return
     */
    public MergeOrderPromotionQbModel checkParam(JSONObject fixedcolumns) {
        MergeOrderPromotionQbModel mergeOrderModel = new MergeOrderPromotionQbModel();
        if (fixedcolumns.containsKey("GIFT_ITEM_CODE")) {
            String giftItemCode = fixedcolumns.getString("GIFT_ITEM_CODE");
            mergeOrderModel.setGiftItemCode(giftItemCode);
        }
        if (fixedcolumns.containsKey("PROMOTION_NAME")) {
            String promotionName = fixedcolumns.getString("PROMOTION_NAME");
            mergeOrderModel.setPromotionName(promotionName);
        }

        return mergeOrderModel;
    }
}
