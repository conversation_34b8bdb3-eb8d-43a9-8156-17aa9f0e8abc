package com.jackrain.nea.oc.oms.services;

import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoOrderMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.IntermediateTableRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.util.TaobaoRefundOrderTransferUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 11:22 上午
 * @Version 1.0
 * 淘宝退单处理
 */
@Slf4j
@Component
public class OmsTaobaoRefundService {
    @Autowired
    private IpBTaobaoRefundMapper ipBTaobaoRefundMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;
    @Autowired
    private IpBTaobaoOrderMapper ipBTaobaoOrderMapper;

    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;
    @Autowired
    private TaobaoRefundOrderTransferUtil taobaoRefundOrderTransferUtil;

    /**
     * 需要提供出去给转单步骤使用
     *
     * @param ocBOrderId
     * @return
     */
    public List<OcBOrderDelivery> findOrderDeliveriesByOcbOrderId(Long ocBOrderId) {
        if (Objects.nonNull(ocBOrderId)) {
            return ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrderId);
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 查询退单所需要的转换信息
     *
     * @param orderNo
     * @return
     */
    public OmsTaobaoRefundRelation selectTaoBaoRefundRelation(String orderNo) {
        //根据退款编号查询退款中间表信息
        OmsTaobaoRefundRelation refundRelation = new OmsTaobaoRefundRelation();
        IpBTaobaoRefund taoBaoRefund = ipBTaobaoRefundMapper.selectTaobaoRefundByRefundId(orderNo);
        if (taoBaoRefund == null) {
            return null;
        }
        refundRelation.setIpBTaobaoRefund(taoBaoRefund);

        //是否天猫周期购售后
        boolean isCycleBuy = taobaoRefundOrderTransferUtil.isTaobaoRefundCycleBuy(taoBaoRefund);
        //天猫周期购需要售后的期数
        List<Integer> cycleRefundNumbers = Lists.newArrayList();
        if (isCycleBuy){
            for (int i = 0; i < taoBaoRefund.getCyclePeriodCount().intValue(); i++) {
                int number = taoBaoRefund.getCyclePeriod().intValue() + i;
                if (number <= taoBaoRefund.getCycleTpc().intValue()) {
                    cycleRefundNumbers.add(number);
                }
            }
        }

        //根据退单中间表的ooid 找到对应的订单明细的oid
        Long oid = taoBaoRefund.getOid();
        Long tid = taoBaoRefund.getTid();

        List<OcBOrder> ocBOrders = ocBOrderMapper.selectNecesaryOcBOrderByTid(tid+"");

        IpBTaobaoOrder ipBTaobaoOrder = ipBTaobaoOrderMapper.selectTaobaoOrderByTid(tid + "");
        refundRelation.setIpBTaobaoOrder(ipBTaobaoOrder);
        if (CollectionUtils.isEmpty(ocBOrders)) {
            return refundRelation;
        }
        ocBOrders = ocBOrders.stream().distinct().collect(Collectors.toList());
        //此处订单已经去重
        List<OmsOrderRelation> omsOrderRelations = new ArrayList<>();

        //赠品订单
        List<OmsOrderRelation> isGiftOrderRelation = new ArrayList<>();
        List<OmsOrderRelation> isGiftItemRelation = new ArrayList<>();

        for (OcBOrder ocBOrder : ocBOrders) {
            //是否为赠品订单
            OmsOrderRelation giftOrderRelation = omsReturnOrderService.checkTbIsGiftOrder(ocBOrder, oid + "");
            if (giftOrderRelation != null) {
                List<OcBOrderDelivery> ocBOrderDeliveries = findOrderDeliveriesByOcbOrderId(ocBOrder.getId());
                giftOrderRelation.setOrderDeliveries(ocBOrderDeliveries);
                isGiftOrderRelation.add(giftOrderRelation);
                continue;
            }
            // 同时也需要带有子单号才行 不能只看赠品
            OmsOrderRelation giftItemRelation = omsReturnOrderService.checkIsGiftItem(ocBOrder, oid);
            if (giftItemRelation != null) {
                isGiftItemRelation.add(giftItemRelation);
            }
            OmsOrderRelation omsOrderRelation = new OmsOrderRelation();

            //根据订单查询需要退货的明细
            List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemByOoid(ocBOrder.getId(), oid + "");
            if (CollectionUtils.isNotEmpty(ocBOrderItems)){
                List<OcBOrderItem> giftItems = ocBOrderItemMapper.selectSystemGiftOrderItem(ocBOrder.getId());
                if (CollectionUtils.isNotEmpty(giftItems)){
                    ocBOrderItems.addAll(giftItems);
                }
            }

            List<OcBOrderItem> orderItemsGift = ocBOrderItemMapper.selectOrderItemFullGiftListWithOid(ocBOrder.getId(), oid + "");
            // 如果主品为空。把赠品塞进去(不然主品为空 赠品不为空的场景 我也不知道咋调整)
            if (CollectionUtils.isEmpty(ocBOrderItems)) {
                ocBOrderItems = orderItemsGift;
            }
            if (CollectionUtils.isEmpty(ocBOrderItems) && CollectionUtils.isEmpty(orderItemsGift)) {
                continue;
            }

            // @20200731 修改查询，调用封装方法
            List<OcBOrderDelivery> ocBOrderDeliveries = findOrderDeliveriesByOcbOrderId(ocBOrder.getId());
            omsOrderRelation.setOrderDeliveries(ocBOrderDeliveries);
            omsOrderRelation.setOcBOrder(ocBOrder);
            omsOrderRelation.setOcBOrderItems(ocBOrderItems);

            if (isCycleBuy && !cycleRefundNumbers.contains(ocBOrder.getCurrentCycleNumber())){
                continue;
            }
            omsOrderRelations.add(omsOrderRelation);
        }
        refundRelation.setCycleBuy(isCycleBuy);
        refundRelation.setCycleBuyCurrPhases(cycleRefundNumbers);
        refundRelation.setOmsOrderRelation(omsOrderRelations);
        refundRelation.setIsGiftOrderRelation(isGiftOrderRelation);
        refundRelation.setGiftItemRelation(isGiftItemRelation);
        IntermediateTableRelation intermediateTableRelation = new IntermediateTableRelation();
        intermediateTableRelation.setIpBTaobaoRefund(taoBaoRefund);
        refundRelation.setIntermediateTableRelation(intermediateTableRelation);
        return refundRelation;
    }


    /**
     * 处理正常赠品 及 挂靠赠品
     *
     * @param ocBOrderItems
     * @param stringListMap
     * @return
     */
    private List<OmsOrderRelation.OcOrderGifts> getOcOrderGifts(List<OcBOrderItem> ocBOrderItems, Map<String, List<OcBOrderItem>> stringListMap) {
        List<OmsOrderRelation.OcOrderGifts> gifts = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            if (ocBOrderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                continue;
            }
            List<OcBOrderItem> orderItems = stringListMap.get("1");
            if (CollectionUtils.isNotEmpty(orderItems)) {
                OmsOrderRelation.OcOrderGifts ocOrderGifts = new OmsOrderRelation.OcOrderGifts();
                ocOrderGifts.setOcBOrderGifts(orderItems);
                //不是挂靠赠品
                ocOrderGifts.setGiftMark(1);
                gifts.add(ocOrderGifts);
                stringListMap.remove("1");
            }
            String giftRelation = ocBOrderItem.getGiftRelation();
            orderItems = stringListMap.get(giftRelation);
            if (CollectionUtils.isNotEmpty(orderItems)) {
                OmsOrderRelation.OcOrderGifts ocOrderGifts = new OmsOrderRelation.OcOrderGifts();
                ocOrderGifts.setOcBOrderGifts(orderItems);
                //挂靠赠品
                ocOrderGifts.setGiftMark(2);
                gifts.add(ocOrderGifts);
                stringListMap.remove(giftRelation);
            }
        }
        return gifts;
    }

    /**
     * 对赠品及下挂赠品的处理
     *
     * @param orderItemsGift
     * @return
     */
    private Map<String, List<OcBOrderItem>> giftHandle(List<OcBOrderItem> orderItemsGift) {
        Map<String, List<OcBOrderItem>> giftList = new HashMap<>(10);
        for (OcBOrderItem orderItemGift : orderItemsGift) {
            //判断明细是否有挂靠赠品及 订单下是否有赠品
            String giftRelation = orderItemGift.getGiftRelation();
            if (StringUtils.isEmpty(giftRelation)) {
                //不为下挂商品
                giftRelation = "1";
            }
            if (!giftList.containsKey(giftRelation)) {
                List<OcBOrderItem> list = new ArrayList<>();
                list.add(orderItemGift);
                giftList.put(giftRelation, list);
            } else {
                List<OcBOrderItem> list = giftList.get(giftRelation);
                list.add(orderItemGift);
                giftList.put(giftRelation, list);
            }
        }
        return giftList;
    }

    /**
     * 获取实例
     *
     * @return
     */
    public static OmsTaobaoRefundService getInstance() {
        return ApplicationContextHandle.getBean(OmsTaobaoRefundService.class);
    }

}
