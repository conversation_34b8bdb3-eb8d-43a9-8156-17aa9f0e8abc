package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 自动分物流服务（临时）
 *
 * @author: hulinyang
 * @since: 2019/9/9
 * create at : 2019/9/9 01:23
 */
@Component
@Slf4j
public class OmsOrderAutoDistributionLogisticsService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    /**
     * 查询待自动分配物流的订单
     *
     * @param pageIndex 初始页面index
     * @param pageSize  页面数据大小
     * @return List<Long>
     */
    public List<Long> selectLogisticsIdIsNull(int pageIndex, int pageSize) {
        JSONObject whereKeys = new JSONObject();
        //筛选“订单状态”为待审核
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.UNCONFIRMED.toInteger());
        //物流公司id为空的订单
        whereKeys.put("CP_C_LOGISTICS_ID", null);
        List<Long> ids = ES4Order.queryEsOrderList(whereKeys, pageIndex, pageSize);
        return ids;
    }

    /**
     * 查询待自动分配物流的订单
     *
     * @param pageIndex 初始页面index
     * @param pageSize  页面数据大小
     * @return List<Long>
     */
    public List<Long> selectLogisticsIdIsZero(int pageIndex, int pageSize) {
        JSONObject whereKeys = new JSONObject();
        //筛选“订单状态”为待审核
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.UNCONFIRMED.toInteger());
        //物流公司id为0的订单
        whereKeys.put("CP_C_LOGISTICS_ID", 0);
        List<Long> ids = ES4Order.queryEsOrderList(whereKeys, pageIndex, pageSize);
        return ids;
    }

    public List<Long> selectWaitAutoDistributionLogisticsOrder(int pageIndex, int pageSize) {
        List<Long> ids = new ArrayList<>();
        List<Long> idsIsNullTmp = selectLogisticsIdIsNull(0, pageSize);
        List<Long> idsIsZeroTmp = selectLogisticsIdIsZero(0, pageSize);
        ids.addAll(idsIsNullTmp);
        ids.addAll(idsIsZeroTmp);
        return ids;
    }

    public List<OcBOrderRelation> packageOcBOrderRelationList(List<Long> orderIdList) {
        List<OcBOrderRelation> ocBOrderRelationList = new ArrayList<>();
        List<OcBOrder> orderInfoList = ocBOrderMapper.selectByIdsList(orderIdList);
        for (OcBOrder ocBOrder : orderInfoList) {
            OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
            ocBOrderRelation.setOrderInfo(ocBOrder);
            ocBOrderRelationList.add(ocBOrderRelation);
        }
        return ocBOrderRelationList;
    }

}