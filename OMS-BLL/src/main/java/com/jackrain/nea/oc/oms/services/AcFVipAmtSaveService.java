package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.AcFVipAmtMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.AcFVipAmt;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;

/**
 * className: AcFVipAmtSaveService
 * description:唯品会商品销售额新增/保存服务
 *
 * <AUTHOR>
 * create: 2021-06-19
 * @since JDK 1.8
 */
@Component
public class AcFVipAmtSaveService extends CommandAdapter {

    @Autowired
    private AcFVipAmtMapper vipAmtMapper;

    @Autowired
    private PsRpcService psRpcService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        User user = querySession.getUser();
        ValueHolder valueHolder = CommandAdapterUtil.checkSaveSession(querySession, OcCommonConstant.AC_F_VIP_AMT);
        if (!valueHolder.isOK()) {
            return valueHolder;
        }

        JSONObject fixColumn = (JSONObject)valueHolder.getData().get(OcCommonConstant.FIX_COLUMN);
        AcFVipAmt vipAmt = ((JSONObject)fixColumn.get(OcCommonConstant.AC_F_VIP_AMT)).toJavaObject(AcFVipAmt.class);
        Long id = (Long)((HashMap)valueHolder.getData().get("data")).get(OcCommonConstant.OBJ_ID);
        //校验订单编号唯一性
        if(!checkUnique(id,vipAmt)){
            valueHolder = ValueHolderUtils.getFailValueHolder("已存在订单商品数据，不允许保存");
            return valueHolder;
        }

        //框架字段赋值
        vipAmt.setId(id);
        CommandAdapterUtil.defaultOperator(vipAmt,user);
//        根据商品id 获取商品名称和编号（新增时必填，修改时没有修改过就不需要）
        if(vipAmt.getPsCProId() != null){
            List<PsCPro> psCPros = psRpcService.queryProByIdList(Lists.newArrayList(vipAmt.getPsCProId()));
            if(CollectionUtils.isEmpty(psCPros) && id<0){
                valueHolder = ValueHolderUtils.getFailValueHolder("请先选择商品");
                return valueHolder;
            }else if(!CollectionUtils.isEmpty(psCPros)){
                PsCPro psCPro = psCPros.get(0);
                vipAmt.setPsCProEcode(psCPro.getEcode());
                vipAmt.setPsCProEname(psCPro.getEname());
            }
        }
        if (id < 0) {
            //新增
            id = ModelUtil.getSequence(OcCommonConstant.AC_F_VIP_AMT);
            vipAmt.setId(id);
            vipAmt.setOwnerename(user.getEname());
            vipAmt.setModifierename(user.getEname());
            vipAmtMapper.insert(vipAmt);
        }else {
            //更新
            vipAmt.setModifierename(user.getEname());
            vipAmtMapper.updateById(vipAmt);
        }

        valueHolder = ValueHolderUtils.getSuccessValueHolder(id,OcCommonConstant.AC_F_VIP_AMT,"保存成功");
        return valueHolder;
    }

    private boolean checkUnique(Long id,AcFVipAmt vipAmt){
        String billNo = vipAmt.getBillNo();

        //如果订单编号为空，直接校验通过
        if(ObjectUtils.isEmpty(billNo)){
            return true;
        }

        return CollectionUtils.isEmpty(vipAmtMapper
                .selectList(new QueryWrapper<AcFVipAmt>()
                .lambda()
                .eq(AcFVipAmt::getBillNo,billNo)
                .ne(AcFVipAmt::getId,id)));
    }
}
