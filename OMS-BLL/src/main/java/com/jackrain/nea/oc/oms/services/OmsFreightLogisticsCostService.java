package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.api.OtherFeesQueryServiceCmd;
import com.jackrain.nea.st.model.table.StCPostfeeDO;
import com.jackrain.nea.st.model.table.StCPostfeeItemDO;
import com.jackrain.nea.st.model.table.StCPostfeeWarehouseDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 物流运费计算后端服务
 *
 * @author: hulinyang
 * @since: 2019/3/21
 * create at : 2019/3/22 15:29
 */
@Component
@Slf4j

public class OmsFreightLogisticsCostService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private StRpcService stRpcService;

    /**
     * 物流运费计算
     *
     * @param ocBOrderRelation 订单关系实体
     * @return boolean
     */
    public ValueHolderV14 freightLogisticsCost(OcBOrderRelation ocBOrderRelation, User user) {
        log.debug(this.getClass().getName() + "计算运费服务,freightLogisticsCost方法开始====" + "订单id为：" + ocBOrderRelation.getOrderInfo().getId());
        ValueHolderV14 vh = new ValueHolderV14();
        BigDecimal result = BigDecimal.ZERO;
        try {
            boolean flag = false;
            OcBOrder ocBOrder = omsOrderService.selectOrderInfo(ocBOrderRelation.getOrderInfo().getId());
            //发货仓库
            Long phyWarehouseId = ocBOrder.getCpCPhyWarehouseId();
            //配送物流
            Long logisticsId = ocBOrder.getCpCLogisticsId();
            //收货省
            Long provinceId = ocBOrder.getCpCRegionProvinceId();
            //收货市
            Long cityId = ocBOrder.getCpCRegionCityId();
            //收货区
            Long areaId = ocBOrder.getCpCRegionAreaId();
            //校验订单状态
            flag = checkBillStatus(ocBOrder, user);
            if (!flag) {
                vh.setCode(-1);
                vh.setMessage("校验订单状态失败");
                return vh;
            }
            //根据当前日期查找在有效期内的运费方案
            List<StCPostfeeDO> stCPostfeeList = stRpcService.searchEffectiveFreightPlan(new Date());
            if (stCPostfeeList.size() == 0) {
                vh.setCode(-1);
                vh.setMessage("无可用的运费方案");
                return vh;
            }
            if (stCPostfeeList.size() > 0 && stCPostfeeList.size() == 1) {
                StCPostfeeDO stCPostfee = stCPostfeeList.get(0);
                //取整数倍
                String multiple = stCPostfee.getMultiple();
                Long stCPostfeeId = stCPostfee.getId();
                Long cityIdTmp = null;
                Long areaIdTmp = null;
                if (stCPostfee.getCpCRegionCityId() != null) {
                    cityIdTmp = cityId;
                }
                if (stCPostfee.getCpCRegionAreaId() != null) {
                    areaIdTmp = areaId;
                }
                List<StCPostfeeItemDO> stCPostfeeItemList
                        = stRpcService.searchStCPostfeeItem(stCPostfeeId, logisticsId, provinceId, cityIdTmp, areaIdTmp);
                StCPostfeeItemDO stCPostfeeItem = stCPostfeeItemList.get(0);
                result = calculateFreight(ocBOrder, stCPostfeeItem, multiple);

            } else {
                boolean effectPostFlag = false;
                for (StCPostfeeDO stCPostfee : stCPostfeeList) {
                    //取整数倍
                    String multiple = stCPostfee.getMultiple();
                    Long stCPostfeeId = stCPostfee.getId();
                    List<StCPostfeeWarehouseDO> stCPostfeeWarehouseList = stRpcService.searchStCPostfeeWarehouse(stCPostfeeId);
                    boolean phyWarehouseFlag = false;
                    if (stCPostfeeWarehouseList.size() > 0) {
                        for (StCPostfeeWarehouseDO stCPostfeeWarehouse : stCPostfeeWarehouseList) {
                            if (phyWarehouseId.equals(stCPostfeeWarehouse.getCpCPhyWarehouseId())) {
                                phyWarehouseFlag = true;
                                break;
                            }
                        }
                        if (!phyWarehouseFlag) {
                            continue;
                        }
                    }
                    Long cityIdTmp = null;
                    Long areaIdTmp = null;
                    if (stCPostfee.getCpCRegionCityId() != null) {
                        cityIdTmp = cityId;
                    }
                    if (stCPostfee.getCpCRegionAreaId() != null) {
                        areaIdTmp = areaId;
                    }
                    List<StCPostfeeItemDO> stCPostfeeItemList
                            = stRpcService.searchStCPostfeeItem(stCPostfeeId, logisticsId, provinceId, cityIdTmp, areaIdTmp);

                    if (stCPostfeeItemList.size() == 0) {
                        stCPostfeeItemList
                                = stRpcService.searchStCPostfeeItem(stCPostfeeId, logisticsId, provinceId, cityIdTmp, null);
                    }
                    if (stCPostfeeItemList.size() == 0) {
                        stCPostfeeItemList
                                = stRpcService.searchStCPostfeeItem(stCPostfeeId, logisticsId, provinceId, null, null);
                    }
                    if (stCPostfeeItemList.size() > 0) {
                        StCPostfeeItemDO stCPostfeeItem = stCPostfeeItemList.get(0);
                        result = calculateFreight(ocBOrder, stCPostfeeItem, multiple);
                        effectPostFlag = true;
                    } else {
                        continue;
                    }
                    if (effectPostFlag) {
                        break;
                    }
                }
            }

        } catch (Exception ex) {
            ex.getMessage();
            log.error(LogUtil.format("计算运费物流成本逻辑异常,error:{}"), Throwables.getStackTraceAsString(ex));
        }
        JSONObject json = new JSONObject();
        json.put("data", result);
        vh.setCode(0);
        vh.setMessage("计算运费方案成功");
        return vh;
    }

    /**
     * 检查订单状态
     *
     * @param ocBOrder 订单实体
     * @return boolean
     */

    public boolean checkBillStatus(OcBOrder ocBOrder, User user) {

        boolean flag;
        try {
            int orderStatus = ocBOrder.getOrderStatus();
            if (orderStatus != OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger()) {
                flag = false;
            } else {
                flag = true;
            }
        } catch (Exception ex) {
            flag = false;
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                    ocBOrder.getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), null, null, ex.getMessage(), user);
            log.error(LogUtil.format("判断订单状态异常,error:{}"), Throwables.getStackTraceAsString(ex));
        }
        return flag;
    }


    /**
     * 根据订单信息按照运费方案标准，计算订单运费
     * ST_C_POSTFEE,ST_C_POSTFEE_ITEM,ST_C_POSTFEE_WAREHOUSE
     *
     * @param ocBOrder       订单信息
     * @param stCPostfeeItem 运费方案明细
     * @param multiple       是否取整数倍
     * @return BigDecimal
     */

    public BigDecimal calculateFreight(OcBOrder ocBOrder, StCPostfeeItemDO stCPostfeeItem, String multiple) {
        log.debug(this.getClass().getName() + "计算运费服务,calculateFreight方法开始，订单id为：" + ocBOrder.getId());
        BigDecimal price = new BigDecimal(0);
        //包裹重量
        BigDecimal pakageWeight = ocBOrder.getWeight();
        //包裹数量
        BigDecimal qtyAll = ocBOrder.getQtyAll();
        if (StringUtils.isNotEmpty(multiple)) {
            String pakageWeightStr = pakageWeight.toString();
            String[] pakage = pakageWeightStr.split("\\.");
            String beforePoint = pakage[0];
            int beforePointTmp = Integer.parseInt(beforePoint);
            BigDecimal bp = new BigDecimal(beforePoint);
            String afterPoint = pakage[1];
            afterPoint = afterPoint.substring(0, 1);
            int afterPointTmp = Integer.parseInt(afterPoint);
            if (afterPointTmp > 5) {
                beforePointTmp = beforePointTmp + 1;
                pakageWeight = new BigDecimal(String.valueOf(beforePointTmp));
            } else if (afterPointTmp < 5) {
                afterPointTmp = 5;
                pakageWeight = new BigDecimal(String.valueOf(beforePointTmp) + "." + String.valueOf(afterPointTmp));
            }
            price = calcPrice(stCPostfeeItem, pakageWeight, qtyAll);
            log.debug(this.getClass().getName() + "计算运费服务,calcPrice方法调用结束，订单id为：" + ocBOrder.getId() + "返回运费为：" + price);
            return price;
        } else {
            price = calcPrice(stCPostfeeItem, pakageWeight, qtyAll);
            log.debug(this.getClass().getName() + "计算运费服务,calcPrice方法调用结束，订单id为：" + ocBOrder.getId() + "返回运费为：" + price);
            return price;
        }

    }

    /**
     * 根据包裹重量运费方案，算出包裹的运费
     *
     * @param stCPostfeeItem 运费方案明细
     * @param pakageWeight   包裹重量
     * @return BigDecimal
     */
    public BigDecimal calcPrice(StCPostfeeItemDO stCPostfeeItem, BigDecimal pakageWeight, BigDecimal qtyAll) {
        log.debug(this.getClass().getName() + "计算运费服务,calcPrice方法调用开始====");
        BigDecimal price = new BigDecimal(0);
        //首重
        BigDecimal firstheavy = new BigDecimal(0);
        if (stCPostfeeItem.getFirstheavy() != null) {
            firstheavy = stCPostfeeItem.getFirstheavy();
        }
        //首费
        BigDecimal firstfee = new BigDecimal(0);
        if (stCPostfeeItem.getFirstfee() != null) {
            firstfee = stCPostfeeItem.getFirstfee();
        }
        //二重
        BigDecimal secondheavy = new BigDecimal(0);
        if (stCPostfeeItem.getSecondheavy() != null) {
            secondheavy = stCPostfeeItem.getSecondheavy();
        }
        //二费
        BigDecimal secondfee = new BigDecimal(0);
        if (stCPostfeeItem.getSecondfee() != null) {
            secondfee = stCPostfeeItem.getSecondfee();
        }
        //三费
        BigDecimal thirdheavy = new BigDecimal(0);
        if (stCPostfeeItem.getThirdheavy() != null) {
            thirdheavy = stCPostfeeItem.getThirdheavy();
        }
        //三重
        BigDecimal thirdfee = new BigDecimal(0);
        if (stCPostfeeItem.getThirdfee() != null) {
            thirdfee = stCPostfeeItem.getThirdfee();
        }
        //续重
        BigDecimal continuedheavy = new BigDecimal(0);
        if (stCPostfeeItem.getContinuedheavy() != null) {
            continuedheavy = stCPostfeeItem.getContinuedheavy();
        }
        //续费
        BigDecimal continuedfee = new BigDecimal(0);
        if (stCPostfeeItem.getContinuedfee() != null) {
            continuedfee = stCPostfeeItem.getContinuedfee();
        }

        //超重部分每kg的费用
        BigDecimal overWeightPrice = continuedfee.divide(continuedheavy, 2, BigDecimal.ROUND_HALF_UP);
        if (firstheavy.compareTo(pakageWeight) >= 0) {
            price = firstheavy.multiply(firstfee);
        } else if (secondheavy.compareTo(pakageWeight) >= 0 && pakageWeight.compareTo(firstheavy) > 0) {
            price = secondheavy.multiply(secondfee);
        } else if (thirdheavy.compareTo(pakageWeight) >= 0 && pakageWeight.compareTo(secondheavy) > 0) {
            price = thirdheavy.multiply(thirdfee);
        } else if (pakageWeight.compareTo(thirdheavy) >= 0) {
            if (firstheavy.compareTo(pakageWeight) != 0 && secondheavy.compareTo(BigDecimal.ZERO) == 0 && thirdheavy.compareTo(BigDecimal.ZERO) == 0) {
                price = firstheavy.multiply(firstfee);
                BigDecimal overHeavy = pakageWeight.subtract(firstheavy);
                BigDecimal overheavyCost = overHeavy.multiply(overWeightPrice);
                price = price.add(overheavyCost);
            } else if (secondheavy.compareTo(BigDecimal.ZERO) != 0 && thirdheavy.compareTo(BigDecimal.ZERO) == 0) {
                price = secondheavy.multiply(secondfee);
                BigDecimal overHeavy = pakageWeight.subtract(secondheavy);
                BigDecimal overheavyCost = overHeavy.multiply(overWeightPrice);
                price = price.add(overheavyCost);

            } else {
                price = thirdheavy.multiply(thirdfee);
                //超重重量
                BigDecimal overWeight = pakageWeight.subtract(thirdheavy);
                //超重部分费用
                BigDecimal overCost = overWeightPrice.multiply(overWeight);
                //三重部分费用
                BigDecimal thirdCost = thirdheavy.multiply(thirdfee);
                //总费用
                price = overCost.add(thirdCost);
            }
        } else {
            price = null;
        }

        if (pakageWeight.compareTo(new BigDecimal(2)) <= 0) {
            //判断商品数量字段是否为空
            BigDecimal proNum = stCPostfeeItem.getWeight();
            if (proNum != null) {
                BigDecimal overNumPrice = new BigDecimal(0);
                if (proNum.compareTo(BigDecimal.ZERO) != 0) {
                    int proNumInt = proNum.intValue();
                    int qtyallInt = qtyAll.intValue();
                    if (qtyallInt - proNumInt > 0) {
                        //取增加商品数量
                        BigDecimal addNum = stCPostfeeItem.getAddWeight();
                        //增加金额
                        BigDecimal addFee = stCPostfeeItem.getAddFee();

                        BigDecimal onePrice = new BigDecimal(0);
                        if (addNum.compareTo(BigDecimal.ZERO) != 0 && addFee.compareTo(BigDecimal.ZERO) != 0) {
                            //超出一件的价格
                            onePrice = addFee.divide(addNum, 2, BigDecimal.ROUND_HALF_UP);
                        }
                        //总价格 = 包裹普通计价金额 + 维护的超出数量金额
                        price = price != null ? price.add(overNumPrice) : overNumPrice;
                        BigDecimal overNum = new BigDecimal(qtyallInt - proNumInt);
                        overNumPrice = onePrice.multiply(overNum);
                    } else {
                        overNumPrice = new BigDecimal(0);
                    }
                    price = price != null ? price.add(overNumPrice) : overNumPrice;
                }
            }
        }
        log.debug(this.getClass().getName() + "计算运费服务,calcPrice方法调用结束====" + "运费金额为：" + price);
        return price;
    }
}
