package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.request.ModifyAddressRequest;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoModifyAddrMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TaoBaoUpdateAddressStatusEnum;
import com.jackrain.nea.oc.oms.model.order.address.ReceiverAddressDto;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongUser;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoModifyAddr;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单地址修改
 *
 * @date 2019/10/12
 * @author: ming.fz
 */
@Component
@Slf4j
public class OcBOrderUpdateAddressService {

    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    private OcBorderUpdateService ocBorderUpdateService;

    @Autowired
    private RegionNewService regionService;

    @Autowired
    IpBTaobaoModifyAddrMapper ipBTaobaoModifyAddrMapper;

    @Autowired
    private IpRpcService ipRpcService;

    /**
     * 判断淘宝预售地址修改
     *
     * @param bl
     * @param tid
     * @param user
     */
    public ValueHolderV14 updateOrderAddress(boolean bl, String tid, User user) {
        return this.updateOrderRegion(bl, tid, user);
    }

    /**
     * 通过sourceCode获取订单
     *
     * @param tid
     * @return
     */
    public List<OcBOrder> getOrderList(String tid) {
        //通过tid获取全渠道所有订单
        return orderService.selectOmsOrderRecord(tid, null, false);
    }

    public OcBOrderRelation getOrderItemList(Long orderId) {
        return orderService.selectOmsOrderInfo(orderId);
    }

    /**
     * 判断淘宝预售地址修改
     *
     * @param bl
     * @param tid
     * @param user
     */
    public ValueHolderV14<String> updateOrderRegion(boolean bl, String tid, User user) {
        ValueHolderV14 result = new ValueHolderV14<String>();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("淘宝预售地址修改开始：", tid));
        }
        IpBTaobaoModifyAddr ipBTaobaoModifyAddr = null;
        List<RedisReentrantLock> lockList =new ArrayList<>();
        try {
            ipBTaobaoModifyAddr = ipBTaobaoModifyAddrMapper.selectSourcecodeByIsUpdate(tid);
            if (Objects.isNull(ipBTaobaoModifyAddr)) {
                result.setCode(ResultCode.FAIL);
                result.setMessage("查询不到单据信息,请稍后重试!");
                return result;
            }
            //判断状态是否是待同步
            Integer isUpdate = ipBTaobaoModifyAddr.getIsUpdate();
            if (isUpdate != TaoBaoUpdateAddressStatusEnum.AWAIT_SYS.getVal() && !bl) {
                result.setCode(ResultCode.FAIL);
                result.setMessage("只有转换状态为待同步的单据才可以单据转换");
                return result;
            }
            //通过tid获取全渠道所有订单
            List<OcBOrder> findOrderInfoList = orderService.selectOmsOrderRecord(tid, null, false);
            if (findOrderInfoList == null || findOrderInfoList.size() == 0) {
                //判断创建时间是否超过三天
                Date creationdate = ipBTaobaoModifyAddr.getCreationdate();
                Date endTime = new Date();
                long l = (endTime.getTime() - creationdate.getTime()) / (24 * 60 * 60 * 1000);
                if (l > 3) {
                    //修改中间表地址更改状态
                    updateAddress(ipBTaobaoModifyAddr, user, TaoBaoUpdateAddressStatusEnum.FAILED_SYS, "超过3天未找到原单，更新失败!");
                } else {
                    updateAddress(ipBTaobaoModifyAddr, user, null, "未找到原单，待下次再更新!");
                }
                result.setCode(ResultCode.FAIL);
                result.setMessage("未找到原单");
                return result;
            }

//            Integer status = douYinCheck(findOrderInfoList, ipBTaobaoModifyAddr, user);
//            if (status == 1) {
//                result.setCode(ResultCode.FAIL);
//                result.setMessage("抖音实效性:15分钟，更新失败!");
//                //因功能需要 嵌入特殊逻辑 下游特殊逻辑:这个data值不同步平台
//                result.setData("douYinSpecialDeal");
//                return result;
//            }

            // 排除掉虚拟订单进行处理
            findOrderInfoList = findOrderInfoList.parallelStream().filter(order -> order.getOrderType() != 8).collect(
                    Collectors.toList());
            if (CollectionUtils.isEmpty(findOrderInfoList)) {
                // 全部为补差价订单
                //修改中间表地址更改状态
                updateAddress(ipBTaobaoModifyAddr, user, TaoBaoUpdateAddressStatusEnum.SUCCESS_SYS, "地址更改成功！");
                result.setCode(ResultCode.SUCCESS);
                result.setMessage("地址修改成功");
                return result;
            }
            if (findOrderInfoList.size() > 1) {
                //记录当前订单地址是否全部更改
                boolean updateAddressBl = true;
                List<OcBOrder> checkBoxAuditedList = findOrderInfoList.parallelStream().filter(ocBOrder ->
                        ocBOrder.getOrderStatus() == OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal()
                                || ocBOrder.getOrderStatus() == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal())
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(checkBoxAuditedList)) {
                    findOrderInfoList.removeAll(checkBoxAuditedList);
                    if (!ocBorderUpdateService.toExamineOrderBatch(checkBoxAuditedList, user, LogTypeEnum.AUTOMATIC_UPDATE_ADDRESS_REVERSE_AUDIT.getType())) {
                        // 有一个反审核不成功 就不修改
                        //修改中间表地址更改状态
                        updateAddress(ipBTaobaoModifyAddr, user, null, "反审核失败，等待下次更新");
                        result.setCode(ResultCode.FAIL);
                        result.setMessage("组合商品反审核失败!");
                        return result;
                    } else {
                        findOrderInfoList.addAll(checkBoxAuditedList);
                    }
                }
                //批量加锁
                batchLock(findOrderInfoList,user,lockList);
                for (OcBOrder ocBOrder : findOrderInfoList) {
                    if (ocBOrder.getOrderStatus() == OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal()
                            || ocBOrder.getOrderStatus() == OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()
                            || ocBOrder.getOrderStatus() == OcOrderCheckBoxEnum.CHECKBOX_PENDING_ALLOCATED.getVal()) {
                        ReceiverAddressDto addressDto = this.buildAddressDto(ocBOrder.getId(), ipBTaobaoModifyAddr);
                        result = ocBorderUpdateService.updateReceiveAddressNoLock(addressDto, user, false, false);
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("淘宝预售地址修改结果：{}"), JSONObject.toJSONString(result));
                        }
                        if (result.isOK()) {
                            //修改中间表地址更改状态
                            updateAddress(ipBTaobaoModifyAddr, user, TaoBaoUpdateAddressStatusEnum.SUCCESS_SYS,
                                    "地址更改成功！");
                        } else {
                            //修改中间表地址更改状态
                            updateAddress(ipBTaobaoModifyAddr, user, null, "修改地址失败，等待下次更新");
                            updateAddressBl = false;
                        }
                    }
                }
                if (updateAddressBl) {
                    //redis KEY删除
                    RedisMasterUtils.getObjRedisTemplate().delete(BllRedisKeyResources
                            .getUpdateOrderAddressKey(findOrderInfoList.get(0).getSourceCode()));
                } else {
                    result.setCode(ResultCode.FAIL);
                    result.setMessage("淘宝预售地址修改失败");
                    return result;
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("淘宝预售地址修改结果返回：{}"), JSONObject.toJSONString(result));
                }
                return result;
            } else {
                OcBOrder ocBOrder = findOrderInfoList.get(0);
                if (ocBOrder.getOrderStatus() != OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal()
                        && ocBOrder.getOrderStatus() != OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal()
                        && ocBOrder.getOrderStatus() != OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()
                        && ocBOrder.getOrderStatus() != OcOrderCheckBoxEnum.CHECKBOX_PENDING_ALLOCATED.getVal()
                        && ocBOrder.getOrderStatus() != OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
                    result.setCode(ResultCode.FAIL);
                    result.setMessage(
                            "订单状态: " + OcOrderCheckBoxEnum.enumToStringByValue(ocBOrder.getOrderStatus()) + " 不允许修改地址!");
                } else {
                    ReceiverAddressDto addressDto = this.buildAddressDto(ocBOrder.getId(), ipBTaobaoModifyAddr);
                    result = ocBorderUpdateService.updateReceiveAddressNew(addressDto, user, false, false);
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("淘宝预售地址修改结果：{}"), JSONObject.toJSONString(result));
                    }
                }
                if (result.isOK()) {
                    //修改中间表地址更改状态
                    updateAddress(ipBTaobaoModifyAddr, user, TaoBaoUpdateAddressStatusEnum.SUCCESS_SYS, "地址更改成功！");
                    //redis KEY删除
                    RedisMasterUtils.getObjRedisTemplate().delete(BllRedisKeyResources
                            .getUpdateOrderAddressKey(findOrderInfoList.get(0).getSourceCode()));
                } else {
                    //修改中间表地址更改状态
                    updateAddress(ipBTaobaoModifyAddr, user, null, "修改地址失败，等待下次更新");
                }
                return result;
            }

        } catch (NDSException e) {
            log.error(LogUtil.format("OcBOrderUpdateAddressService.NDSException,error：{}"),
                    Throwables.getStackTraceAsString(e));
            result.setMessage(e.getMessage());
            updateAddress(ipBTaobaoModifyAddr, user, null, "修改地址异常" + e.getMessage() + "，等待下次更新");
        } catch (Exception e) {
            log.error(LogUtil.format("OcBOrderUpdateAddressService.updateOrderAddress error：{}"),
                    Throwables.getStackTraceAsString(e));
            result.setMessage("修改地址失败! ");
            updateAddress(ipBTaobaoModifyAddr, user, null, "修改地址异常，等待下次更新");
        }finally {
            //批量释放锁
            if (CollectionUtils.isNotEmpty(lockList)) {
                for (RedisReentrantLock redisReentrantLock : lockList) {
                    redisReentrantLock.unlock();
                }
            }

        }
        result.setCode(ResultCode.FAIL);
        return result;
    }
    /**
     * description:批量加锁
     * @Author:  liuwenjin
     * @Date 2023/3/6 19:38
     */
    private void batchLock(List<OcBOrder> findOrderInfoList,User user,List<RedisReentrantLock> lockList) throws InterruptedException {
            for (OcBOrder ocBOrder : findOrderInfoList) {
                Long orderId = ocBOrder.getId();
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                if (redisLock.tryLock(1000, TimeUnit.MILLISECONDS)) {
                    lockList.add(redisLock);
                }else {
                    throw new NDSException(Resources.getMessage(String.format("修改地址加锁失败"), user.getLocale()));
                }
            }
    }

    /**
     * 状态值
     * 0 继续原有逻辑
     * 1 抖音超过15分钟 修改地址表为失败 但不同步平台
     */
    private Integer douYinCheck(List<OcBOrder> findOrderInfoList,
                                IpBTaobaoModifyAddr ipBTaobaoModifyAddr,
                                User user) {
        OcBOrder ocBOrder = findOrderInfoList.get(0);

        if (!PlatFormEnum.DOU_YIN.getCode().equals(ocBOrder.getPlatform())) {
            return 0;
        }

        Date creationdate = ipBTaobaoModifyAddr.getCreationdate();
        Date endTime = new Date();

        long minute = (endTime.getTime() - creationdate.getTime()) / (60 * 1000);

        if (minute > 15) {
            updateAddress(ipBTaobaoModifyAddr, user, TaoBaoUpdateAddressStatusEnum.FAILED_SYS, "抖音实效性:15分钟，更新失败!");
            return 1;
        } else {
            return 0;
        }
    }

    public ReceiverAddressDto buildAddressDto(Long id, IpBTaobaoModifyAddr source) {

        ReceiverAddressDto addressDto = new ReceiverAddressDto();
        addressDto.setReceiverName(source.getReceiveName());
        addressDto.setReceiverMobile(source.getPhoneNum());
        addressDto.setReceiverAddress(source.getAddress());
        addressDto.setReceiverZip(source.getZip());
        addressDto.setOaid(source.getOaid());
        addressDto.setCpCRegionTownEname(source.getReceiverTown());
        setOrderRegoin(source.getProvincecode(), source.getCitycode(), source.getDistrictcode(), addressDto);
        addressDto.setId(id);
        return addressDto;
    }

    public void setOrderRegoin(String provName, String cityName, String areaName, ReceiverAddressDto order) {
        ProvinceCityAreaInfo provinceCityAreaInfo = regionService.selectProvinceCityAreaInfo(provName,
                cityName, areaName);
        if (Objects.isNull(provinceCityAreaInfo) || Objects.isNull(provinceCityAreaInfo.getProvinceInfo())) {
            throw new NDSException("匹配省市区失败");
        }
        if (Objects.isNull(provinceCityAreaInfo.getCityInfo())) {
            throw new NDSException("匹配市区失败");
        }
//        if (Objects.isNull(provinceCityAreaInfo.getAreaInfo())) {
//            throw new NDSException("匹配区失败");
//        }
        order.setCpCRegionProvinceId(provinceCityAreaInfo.getProvinceInfo().getId());
        order.setCpCRegionProvinceEcode(provinceCityAreaInfo.getProvinceInfo().getCode());
        order.setCpCRegionProvinceEname(provinceCityAreaInfo.getProvinceInfo().getName());

        order.setCpCRegionCityId(provinceCityAreaInfo.getCityInfo().getId());
        order.setCpCRegionCityEcode(provinceCityAreaInfo.getCityInfo().getCode());
        order.setCpCRegionCityEname(provinceCityAreaInfo.getCityInfo().getName());

        // 区可不匹配
        if (Objects.nonNull(provinceCityAreaInfo.getAreaInfo())) {
            order.setCpCRegionAreaId(provinceCityAreaInfo.getAreaInfo().getId());
            order.setCpCRegionAreaEcode(provinceCityAreaInfo.getAreaInfo().getCode());
            order.setCpCRegionAreaEname(provinceCityAreaInfo.getAreaInfo().getName());
        } else {
            order.setCpCRegionAreaId(0L);
            order.setCpCRegionAreaEcode("");
            order.setCpCRegionAreaEname("");
        }

    }

    /**
     * 更新中间表
     *
     * @param ipBTaobaoModifyAddr
     * @param user
     * @param statusEnum
     * @param remark
     */
    private void updateAddress(IpBTaobaoModifyAddr ipBTaobaoModifyAddr, User user, TaoBaoUpdateAddressStatusEnum statusEnum,
                               String remark) {
        IpBTaobaoModifyAddr updateInfo = new IpBTaobaoModifyAddr();
        if (statusEnum != null) {
            updateInfo.setIsUpdate(statusEnum.getVal());
        }
        Long id = ipBTaobaoModifyAddr.getId();
        updateInfo.setId(id);
        updateInfo.setRemark(remark);
        updateInfo.setModifieddate(new Date());
        updateInfo.setModifiername(user.getName());
        updateInfo.setModifierename(user.getEname());
        ipBTaobaoModifyAddrMapper.updateById(updateInfo);

    }

    /**
     * 地址修改入参赋值
     *
     * @param ipBTaobaoModifyAddr
     */
    private String setUpdateAddress(IpBTaobaoModifyAddr ipBTaobaoModifyAddr) {
        JSONObject update = new JSONObject();
        //买家所在省
        String provinceName = ipBTaobaoModifyAddr.getProvincecode();
        //买家所在市
        String cityName = ipBTaobaoModifyAddr.getCitycode();
        //买家所在区ID。
        String areaName = ipBTaobaoModifyAddr.getDistrictcode();
        ProvinceCityAreaInfo provinceCityAreaInfo = this.regionService.selectProvinceCityAreaInfo(provinceName,
                cityName, areaName);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("淘宝预售匹配服务地址匹配结果：") + JSONObject.toJSONString(provinceCityAreaInfo));
        }
        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getProvinceInfo() != null) {
            update.put("cp_c_region_province_id", provinceCityAreaInfo.getProvinceInfo().getId());
            update.put("cp_c_region_province_ecode", provinceCityAreaInfo.getProvinceInfo().getCode());
            update.put("cp_c_region_province_ename", provinceCityAreaInfo.getProvinceInfo().getName());
        } else {
            update.put("cp_c_region_province_id", null);
            update.put("cp_c_region_province_ecode", null);
            update.put("cp_c_region_province_ename", null);
        }

        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getCityInfo() != null) {
            update.put("cp_c_region_city_id", provinceCityAreaInfo.getCityInfo().getId());
            update.put("cp_c_region_city_ecode", provinceCityAreaInfo.getCityInfo().getCode());
            update.put("cp_c_region_city_ename", provinceCityAreaInfo.getCityInfo().getName());
        } else {
            update.put("cp_c_region_city_id", null);
            update.put("cp_c_region_city_ecode", null);
            update.put("cp_c_region_city_ename", null);
        }

        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getAreaInfo() != null) {
            update.put("cp_c_region_area_id", provinceCityAreaInfo.getAreaInfo().getId());
            update.put("cp_c_region_area_ecode", provinceCityAreaInfo.getAreaInfo().getCode());
            update.put("cp_c_region_area_ename", provinceCityAreaInfo.getAreaInfo().getName());
        } else {
            update.put("cp_c_region_area_id", null);
            update.put("cp_c_region_area_ecode", null);
            update.put("cp_c_region_area_ename", null);
        }
        update.put("receiver_name", ipBTaobaoModifyAddr.getReceiveName());
        update.put("receiver_mobile", ipBTaobaoModifyAddr.getPhoneNum());
        update.put("receiver_address", ipBTaobaoModifyAddr.getAddress());
        update.put("receiver_zip", ipBTaobaoModifyAddr.getZip());
        update.put("oaid", ipBTaobaoModifyAddr.getOaid());
        // 街道/乡镇
        update.put("cp_c_region_town_ename", ipBTaobaoModifyAddr.getTowncode());
        return update.toJSONString();
    }

    /**
     * 淘宝预售.修改地址
     *
     * @param orderInfo   IpTaobaoOrderRelation
     * @param omsOrder    OcBOrder
     * @param operateUser User
     * <AUTHOR>
     */
    public boolean updateOmsOrderReceiveInfo(IpTaobaoOrderRelation orderInfo, OcBOrder omsOrder, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("淘宝预售.进入修改地址流程"));
        }
        if (orderInfo != null && orderInfo.getTaobaoOrder() != null) {
            IpBTaobaoOrder tabOrder = orderInfo.getTaobaoOrder();
            JSONObject ipJsn = new JSONObject();
            ipJsn.put("cpCRegionProvinceEname", StringUtils.trimToEmpty(tabOrder.getReceiverState()));
            ipJsn.put("cpCRegionCityEname", StringUtils.trimToEmpty(tabOrder.getReceiverCity()));
            ipJsn.put("cpCRegionAreaEname", StringUtils.trimToEmpty(tabOrder.getReceiverDistrict()));
            ipJsn.put("receiverAddress", StringUtils.trimToEmpty(tabOrder.getReceiverAddress()));
            ipJsn.put("omsReceiverName", StringUtils.trimToEmpty(tabOrder.getReceiverName()));
            ipJsn.put("omsReceiverPhone", StringUtils.trimToEmpty(tabOrder.getReceiverPhone()));
            ipJsn.put("omsReceiverMobile", StringUtils.trimToEmpty(tabOrder.getReceiverMobile()));
            ipJsn.put("omsReceiverZip", StringUtils.trimToEmpty(tabOrder.getReceiverZip()));

            JSONObject omsJsn = new JSONObject();
            omsJsn.put("cpCRegionProvinceEname", StringUtils.trimToEmpty(omsOrder.getCpCRegionProvinceEname()));
            omsJsn.put("cpCRegionCityEname", StringUtils.trimToEmpty(omsOrder.getCpCRegionCityEname()));
            omsJsn.put("cpCRegionAreaEname", StringUtils.trimToEmpty(omsOrder.getCpCRegionAreaEname()));
            omsJsn.put("receiverAddress", StringUtils.trimToEmpty(omsOrder.getReceiverAddress()));
            omsJsn.put("omsReceiverName", StringUtils.trimToEmpty(omsOrder.getReceiverName()));
            omsJsn.put("omsReceiverPhone", StringUtils.trimToEmpty(omsOrder.getReceiverPhone()));
            omsJsn.put("omsReceiverMobile", StringUtils.trimToEmpty(omsOrder.getReceiverMobile()));
            omsJsn.put("omsReceiverZip", StringUtils.trimToEmpty(omsOrder.getReceiverZip()));

            boolean gx = false;
            for (String s : ipJsn.keySet()) {
                boolean eqs = StringUtils.equalsIgnoreCase(ipJsn.getString(s), omsJsn.getString(s));
                if (!eqs) {
                    gx = true;
                    break;
                }
            }
            if (gx) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("淘宝预售.修改地址开始"));
                }
                IpBTaobaoModifyAddr ipModifyAddr = new IpBTaobaoModifyAddr();
                ipModifyAddr.setProvincecode(ipJsn.getString("cpCRegionProvinceEname"));
                ipModifyAddr.setCitycode(ipJsn.getString("cpCRegionCityEname"));
                ipModifyAddr.setDistrictcode(ipJsn.getString("cpCRegionAreaEname"));
                ipModifyAddr.setAddress(ipJsn.getString("receiverAddress"));
                ipModifyAddr.setReceiveName(ipJsn.getString("omsReceiverName"));
                ipModifyAddr.setPhoneNum(ipJsn.getString("omsReceiverPhone"));
                ipModifyAddr.setZip(ipJsn.getString("omsReceiverZip"));

                JSONObject copyObject = new JSONObject();
                copyObject.put("id", omsOrder.getId());
                copyObject.put("flag", 1);
                String updateInfoStr = setUpdateAddress(ipModifyAddr);
                copyObject.put("updateInfo", updateInfoStr);
                ValueHolderV14 vh = ocBorderUpdateService.updateReceiveAddress4PreSale(copyObject, operateUser);
                if (vh == null || vh.getCode() == ResultCode.FAIL) {
                    if (log.isDebugEnabled()) {
                        log.debug(this.getClass().getName() + "淘宝预售地址修改失败;渠道订单编号: " + omsOrder.getId()
                                + "; 消息:" + (vh != null ? vh.getMessage() : "null"));
                    }
                    return false;
                }
            }
        } else {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("淘宝预售.修改地址参数异常,出现Null值"));
            }
            return false;
        }
        return true;
    }

    /**
     * 淘宝转单修改地址
     *
     * @param orderInfo
     * @param omsOrder
     * @param operateUser
     * @return
     */
    public ValueHolderV14 updateOmsOrderReceiveInfoByTaoBao(IpTaobaoOrderRelation orderInfo, OcBOrder omsOrder, User operateUser) {
        ValueHolderV14 vh = new ValueHolderV14(0, "修改成功");
        IpBTaobaoOrder taobaoOrder = orderInfo.getTaobaoOrder();
        String oaid = taobaoOrder.getOaid();
        String oaid1 = omsOrder.getOaid();
        if (StringUtils.isNotEmpty(oaid) && !oaid.equals(oaid1)) {
            //oaid不相同  修改地址
            IpBTaobaoModifyAddr ipModifyAddr = new IpBTaobaoModifyAddr();
            ipModifyAddr.setProvincecode(taobaoOrder.getReceiverState());
            ipModifyAddr.setCitycode(taobaoOrder.getReceiverCity());
            ipModifyAddr.setDistrictcode(taobaoOrder.getReceiverDistrict());
            ipModifyAddr.setAddress(taobaoOrder.getReceiverAddress());
            ipModifyAddr.setReceiveName(taobaoOrder.getReceiverName());
            ipModifyAddr.setPhoneNum(taobaoOrder.getReceiverMobile());
            ipModifyAddr.setZip(taobaoOrder.getReceiverZip());
            ipModifyAddr.setOaid(oaid);
            ipModifyAddr.setTowncode(taobaoOrder.getReceiverTown());
            JSONObject copyObject = new JSONObject();
            copyObject.put("id", omsOrder.getId());
            copyObject.put("flag", 1);
            String updateInfoStr = setUpdateAddress(ipModifyAddr);
            copyObject.put("updateInfo", updateInfoStr);
            vh = ocBorderUpdateService.updateReceiveAddressStan(copyObject, operateUser);
        }
        return vh;
    }

    /**
     * 京东转单修改地址
     *
     * @param orderInfo
     * @param omsOrder
     * @param operateUser
     * @return
     */
    public ValueHolderV14 updateOmsOrderReceiveInfoByJD(IpJingdongOrderRelation orderInfo, OcBOrder omsOrder, User operateUser) {
        IpBJingdongOrder jingdongOrder = orderInfo.getJingdongOrder();
        IpBJingdongUser jingdongUser = orderInfo.getJingdongUser();

        IpBTaobaoModifyAddr ipModifyAddr = new IpBTaobaoModifyAddr();
        ipModifyAddr.setProvincecode(jingdongUser.getProvince());
        ipModifyAddr.setCitycode(jingdongUser.getCity());
        ipModifyAddr.setDistrictcode(jingdongUser.getCounty());
        ipModifyAddr.setAddress(jingdongUser.getFullAddress());
        ipModifyAddr.setReceiveName(jingdongUser.getFullname());
        ipModifyAddr.setPhoneNum(jingdongUser.getMobile());

        //京东pop改造 ---> sendpayMap 429=1是抖音 ，429=2 是快手
        boolean isJdPop = false;
        String ctpOrderInfo = jingdongOrder.getCtpOrderInfo();
        String sendPayMap = jingdongOrder.getSendPayMap();
        if (StringUtils.isNotBlank(ctpOrderInfo) && StringUtils.isNotBlank(sendPayMap)) {
            JSONObject sendPayObject = JSONObject.parseObject(sendPayMap);
            if (StringUtils.equals("1", sendPayObject.getString("429")) || StringUtils.equals("2", sendPayObject.getString("429"))) {
                JSONObject jsonObject = JSONObject.parseObject(ctpOrderInfo);

                String ctpWhere = jsonObject.getString("ctpWhere");
                String ctpName = jsonObject.getString("ctpName");
                String ctpMobile = jsonObject.getString("ctpMobile");

                ipModifyAddr.setAddress(StringUtils.isBlank(ctpWhere) ? "" : ctpWhere);
                ipModifyAddr.setReceiveName(StringUtils.isBlank(ctpName) ? "" : ctpName);
                ipModifyAddr.setPhoneNum(StringUtils.isBlank(ctpMobile) ? "" : ctpMobile);

                isJdPop = true;
            }
        }

        JSONObject copyObject = new JSONObject();
        copyObject.put("id", omsOrder.getId());
        copyObject.put("flag", 1);
        String updateInfoStr = setUpdateAddress(ipModifyAddr);

        JSONObject jsonObject = JSON.parseObject(updateInfoStr);
        jsonObject.put("platform_province", jingdongUser.getProvince());
        jsonObject.put("platform_city", jingdongUser.getCity());
        jsonObject.put("platform_area", jingdongUser.getCounty());

        jsonObject.put("oaid", jingdongOrder.getOaid() == null ? "" : jingdongOrder.getOaid());
        //京东pop 不存oaid
        if (isJdPop) {
            jsonObject.put("oaid", "");
        }

        copyObject.put("updateInfo", jsonObject.toJSONString());

        return ocBorderUpdateService.updateReceiveAddressStan(copyObject, operateUser);
    }


    /**
     * 通用平台校验地址是否修改
     *
     * @param orderInfo
     * @param omsOrder
     * @param operateUser
     * @return
     */
    public ValueHolderV14 updateOmsOrderReceiveInfo(IpStandplatOrderRelation orderInfo, OcBOrder omsOrder, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("通用平台校验收货信息是否修改");
        }
        ValueHolderV14 vh = new ValueHolderV14();
        if (orderInfo != null && orderInfo.getStandplatOrder() != null) {
            IpBStandplatOrder tabOrder = orderInfo.getStandplatOrder();
            JSONObject ipJsn = new JSONObject();
            JSONObject omsJsn = new JSONObject();
            boolean gx = false;
            //判断当前通用订单是否为抖音和拼多多
            if ((PlatFormEnum.DOU_YIN.getCode().equals(omsOrder.getPlatform()))) {
                //抖音判断
                if (StringUtils.isNotEmpty(tabOrder.getReceiverMobile()) &&
                        StringUtils.isNotEmpty(omsOrder.getReceiverMobile()) &&
                        StringUtils.isNotEmpty(tabOrder.getReceiverAddress()) &&
                        StringUtils.isNotEmpty(omsOrder.getReceiverAddress()) &&
                        StringUtils.isNotEmpty(tabOrder.getReceiverName()) &&
                        StringUtils.isNotEmpty(omsOrder.getReceiverName())) {
                    //判断是否为密文
                    if (tabOrder.getReceiverMobile().contains("$") || tabOrder.getReceiverMobile().contains("~")) {
                        //中间表和主表收货人手机号码对比
                        Boolean ipReceiverPhoneIndexs = vaildReceiverInfo(tabOrder.getReceiverMobile(), omsOrder.getReceiverMobile(), 1);
                        //收货人地址对比
                        Boolean ipReceiverAddressIndexs = vaildReceiverInfo(tabOrder.getReceiverAddress(), omsOrder.getReceiverAddress(), 1);
                        //收货人名字
                        Boolean ipReceiverNameIndexs = vaildReceiverInfo(tabOrder.getReceiverName(), omsOrder.getReceiverName(), 1);

                        if (ipReceiverPhoneIndexs
                                || ipReceiverAddressIndexs
                                || ipReceiverNameIndexs
                                || !tabOrder.getReceiverProvince().equals(omsOrder.getCpCRegionProvinceEname())
                                || !tabOrder.getReceiverCity().equals(omsOrder.getCpCRegionCityEname())
                                || !tabOrder.getReceiverDistrict().equals(omsOrder.getCpCRegionAreaEname())
                                || !tabOrder.getReceiverTown().equals(omsOrder.getCpCRegionTownEname())) {
                            ipJsn = ipJson(tabOrder, ipJsn);
                            omsJsn = omsJson(omsOrder, omsJsn);
                            gx = true;
                        }
                    } else {
                        ipJsn = ipJson(tabOrder, ipJsn);
                        omsJsn = omsJson(omsOrder, omsJsn);
                        if (Objects.nonNull(ipJsn) && Objects.nonNull(omsJsn)) {
                            for (String s : ipJsn.keySet()) {
                                boolean eqs = StringUtils.equalsIgnoreCase(ipJsn.getString(s), omsJsn.getString(s));
                                if (!eqs) {
                                    gx = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            } else if (PlatFormEnum.PINDUODUO.getCode().equals(omsOrder.getPlatform())) {
                //拼多多判断
                if (StringUtils.isNotEmpty(tabOrder.getReceiverMobile()) &&
                        StringUtils.isNotEmpty(omsOrder.getReceiverMobile()) &&
                        StringUtils.isNotEmpty(tabOrder.getReceiverAddress()) &&
                        StringUtils.isNotEmpty(omsOrder.getReceiverAddress()) &&
                        StringUtils.isNotEmpty(tabOrder.getReceiverName()) &&
                        StringUtils.isNotEmpty(omsOrder.getReceiverName())) {
                    //判断是否为密文
                    if (tabOrder.getReceiverMobile().contains("$") || tabOrder.getReceiverMobile().contains("~")) {
                        //中间表和主表收货人手机号码对比
                        Boolean ipReceiverPhoneIndexs = vaildReceiverInfo(tabOrder.getReceiverMobile(), omsOrder.getReceiverMobile(), 1);
                        //收货人地址对比
                        Boolean ipReceiverAddressIndexs = vaildReceiverInfo(tabOrder.getReceiverAddress(), omsOrder.getReceiverAddress(), 2);
                        //收货人名字
                        Boolean ipReceiverNameIndexs = vaildReceiverInfo(tabOrder.getReceiverName(), omsOrder.getReceiverName(), 2);

                        if (ipReceiverPhoneIndexs
                                || ipReceiverAddressIndexs
                                || ipReceiverNameIndexs
                                || !tabOrder.getReceiverProvince().equals(omsOrder.getCpCRegionProvinceEname())
                                || !tabOrder.getReceiverCity().equals(omsOrder.getCpCRegionCityEname())
                                || !tabOrder.getReceiverDistrict().equals(omsOrder.getCpCRegionAreaEname())) {
                            ipJsn = ipJson(tabOrder, ipJsn);
                            omsJsn = omsJson(omsOrder, omsJsn);
                            gx = true;
                        }
                    } else {
                        ipJsn = ipJson(tabOrder, ipJsn);
                        omsJsn = omsJson(omsOrder, omsJsn);
                        if (Objects.nonNull(ipJsn) && Objects.nonNull(omsJsn)) {
                            for (String s : ipJsn.keySet()) {
                                boolean eqs = StringUtils.equalsIgnoreCase(ipJsn.getString(s), omsJsn.getString(s));
                                if (!eqs) {
                                    gx = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            } else {
                ipJsn = ipJson(tabOrder, ipJsn);
                omsJsn = omsJson(omsOrder, omsJsn);
                if (Objects.nonNull(ipJsn) && Objects.nonNull(omsJsn)) {
                    for (String s : ipJsn.keySet()) {
                        boolean eqs = StringUtils.equalsIgnoreCase(ipJsn.getString(s), omsJsn.getString(s));
                        if (!eqs) {
                            gx = true;
                            break;
                        }
                    }
                }
            }

            if (gx) {
                if (log.isDebugEnabled()) {
                    log.debug("通用平台.修改地址开始");
                }
                //借用淘宝修改地址接口
                IpBTaobaoModifyAddr ipModifyAddr = new IpBTaobaoModifyAddr();
                ipModifyAddr.setProvincecode(ipJsn.getString("cpCRegionProvinceEname"));
                ipModifyAddr.setCitycode(ipJsn.getString("cpCRegionCityEname"));
                ipModifyAddr.setDistrictcode(ipJsn.getString("cpCRegionAreaEname"));
                ipModifyAddr.setTowncode(ipJsn.getString("cpCRegionTownEname"));
                ipModifyAddr.setAddress(ipJsn.getString("receiverAddress"));
                ipModifyAddr.setReceiveName(ipJsn.getString("omsReceiverName"));
                ipModifyAddr.setPhoneNum(ipJsn.getString("omsReceiverMobile"));
                ipModifyAddr.setZip(ipJsn.getString("omsReceiverZip"));
                /*理论上所有有oaid的通用订单修改地址都需要变更oaId，但是本次只修改小红书-开放平台*/
                if (PlatFormEnum.HONGSHU_OPEN.getCode().equals(omsOrder.getPlatform())
                        /*20250716新增京东商仓-佳哥*/
                        || PlatFormEnum.JD_MALL.getCode().equals(omsOrder.getPlatform())) {
                    ipModifyAddr.setOaid(ipJsn.getString("oaid"));
                }

                JSONObject copyObject = new JSONObject();
                copyObject.put("id", omsOrder.getId());
                copyObject.put("flag", 1);
                String updateInfoStr = setUpdateAddress(ipModifyAddr);
                copyObject.put("updateInfo", updateInfoStr);

                vh = ocBorderUpdateService.updateReceiveAddressStan(copyObject, operateUser);
                if (vh == null || vh.getCode() == ResultCode.FAIL) {
                    if (log.isDebugEnabled()) {
                        log.debug(this.getClass().getName() + "通用平台校验收货信息是否修改: " + omsOrder.getId()
                                + "; 消息:" + (vh != null ? vh.getMessage() : "null"));
                    }
                    return vh;
                }
            }
        } else {
            if (log.isDebugEnabled()) {
                log.debug("通用平台校验收货信息是否修改,出现Null值");
            }

        }
        return vh;
    }

    public JSONObject omsJson(OcBOrder omsOrder, JSONObject omsJsn) {
        omsJsn.put("cpCRegionProvinceEname", StringUtils.trimToEmpty(omsOrder.getCpCRegionProvinceEname()));
        omsJsn.put("cpCRegionCityEname", StringUtils.trimToEmpty(omsOrder.getCpCRegionCityEname()));
        omsJsn.put("cpCRegionAreaEname", StringUtils.trimToEmpty(omsOrder.getCpCRegionAreaEname()));
        omsJsn.put("receiverAddress", StringUtils.trimToEmpty(omsOrder.getReceiverAddress()));
        omsJsn.put("omsReceiverName", StringUtils.trimToEmpty(omsOrder.getReceiverName()));
        omsJsn.put("omsReceiverPhone", StringUtils.trimToEmpty(omsOrder.getReceiverPhone()));
        omsJsn.put("omsReceiverMobile", StringUtils.trimToEmpty(omsOrder.getReceiverMobile()));
        omsJsn.put("omsReceiverZip", StringUtils.trimToEmpty(omsOrder.getReceiverZip()));
        omsJsn.put("cpCRegionTownEname", StringUtils.trimToEmpty(omsOrder.getCpCRegionTownEname()));
        return omsJsn;
    }

    public JSONObject ipJson(IpBStandplatOrder tabOrder, JSONObject ipJsn) {
        ipJsn.put("cpCRegionProvinceEname", StringUtils.trimToEmpty(tabOrder.getReceiverProvince()));
        ipJsn.put("cpCRegionCityEname", StringUtils.trimToEmpty(tabOrder.getReceiverCity()));
        ipJsn.put("cpCRegionAreaEname", StringUtils.trimToEmpty(tabOrder.getReceiverDistrict()));
        ipJsn.put("receiverAddress", StringUtils.trimToEmpty(tabOrder.getReceiverAddress()));
        ipJsn.put("omsReceiverName", StringUtils.trimToEmpty(tabOrder.getReceiverName()));
        ipJsn.put("omsReceiverPhone", StringUtils.trimToEmpty(tabOrder.getReceiverPhone()));
        ipJsn.put("omsReceiverMobile", StringUtils.trimToEmpty(tabOrder.getReceiverMobile()));
        ipJsn.put("omsReceiverZip", StringUtils.trimToEmpty(tabOrder.getReceiverZip()));
        ipJsn.put("cpCRegionTownEname", StringUtils.trimToEmpty(tabOrder.getReceiverTown()));
        ipJsn.put("oaid", StringUtils.trimToEmpty(tabOrder.getOaid()));
        return ipJsn;
    }

    public Boolean vaildReceiverInfo(String ipData, String omsData, int index) {
        String[] ipDatas = spiltData(ipData);
        String ipDataindex = ipDatas[index];
        String[] omsDatas = spiltData(omsData);
        String omsDataindex = omsDatas[index];
        if (!Objects.equals(ipDataindex, omsDataindex)) {
            return true;
        } else {
            return false;
        }
    }

    public String[] spiltData(String encData) {
        StringBuffer stringBuffer = new StringBuffer();
        StringBuffer append = stringBuffer.append("\\").append(encData.substring(0, 1));
        String substring = append.toString();
        String[] split = encData.split(substring);
        return split;
    }

    /**
     * @param v14 字段任务结果
     * @param tid 平台单号
     */
    public void invokePushPlatform(ValueHolderV14 v14, String tid) {
        try {
            IpBTaobaoModifyAddr ipBTaobaoModifyAddr = ipBTaobaoModifyAddrMapper.selectSourcecodeByIsUpdate(tid);

            if (v14 != null && "douYinSpecialDeal".equals(v14.getData().toString())) {
                return;
            }

            if (ipBTaobaoModifyAddr == null || !PlatFormEnum.AI_KU_CUN.getLongVal().equals(ipBTaobaoModifyAddr.getPlatformCode())) {
                return;
            }

            if (ipBTaobaoModifyAddr.getIsUpdate() == TaoBaoUpdateAddressStatusEnum.AWAIT_SYS.getVal()) {
                ipBTaobaoModifyAddrMapper.updateR3StatusBySourceCode(tid,TaoBaoUpdateAddressStatusEnum.FAILED_SYS.getVal(),"爱库存平台不进行修改地址补偿!");

            }

            ModifyAddressRequest request = new ModifyAddressRequest();
            request.setTid(ipBTaobaoModifyAddr.getSourcecode());
            request.setPlatform(ipBTaobaoModifyAddr.getPlatformCode());
            request.setSellerNick(ipBTaobaoModifyAddr.getSellerNick());
            request.setOtherData(ipBTaobaoModifyAddr.getOtherData());
            request.setCode(v14.getCode());
            request.setErrorMessage(v14.getMessage());
            ValueHolderV14 result = ipRpcService.standModifyAddrPushPlatform(request);

            ipBTaobaoModifyAddrMapper.updateBySourceCode(ipBTaobaoModifyAddr.getSourcecode(), result.isOK() ? ISUPDATE_STATUS_SYN : ISUPDATE_STATUS_FAIL,
                    StringUtils.substring(result.getMessage(), NumberUtils.INTEGER_ZERO, REMARK_LENGTH));

        } catch (Exception ignored) {

        }
    }

    /**
     * 字段长度限制
     **/

    public static final Integer REMARK_LENGTH = 200;

    /**
     * 同步状态 同步成功
     **/
    public static final Long ISUPDATE_STATUS_SYN = 1L;
    /**
     * 同步状态 同步失败
     **/
    public static final Long ISUPDATE_STATUS_FAIL = 2L;
    /**
     * 同步状态 待同步
     **/
    public static final Long ISUPDATE_STATUS_WAIT = 3L;

}