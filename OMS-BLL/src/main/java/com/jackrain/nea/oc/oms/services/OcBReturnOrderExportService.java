package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrder;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.ChangeEnum;
import com.jackrain.nea.oc.oms.model.result.OcBReturnOrderExportResult;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 13:21
 **/
@Component
@Slf4j
public class OcBReturnOrderExportService {
    public static final String ID = "ID";//退换货单编号
    public static final String OC_B_RETURN_ORDER_ID = "OC_B_RETURN_ORDER_ID";//退换货单编号

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderExchangeMapper exchangeMapper;
    @Autowired
    private OcBReturnOrderRefundMapper refundMapper;
    @Autowired
    CpRpcService cpRpcService;


    public ValueHolderV14<OcBReturnOrderExportResult> exportList(List<Long> idList) {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "退换货订单导出成功！");
        log.debug(LogUtil.multiFormat("[llf]退换货订单导出查询入参：", idList));
        List<ExtOcBReturnOrder> extOcBReturnOrderList = Lists.newArrayList();
        List<ExtOcBReturnOrderExchange> exchangeList = Lists.newArrayList();
        List<ExtOcBReturnOrderRefund> refundList = Lists.newArrayList();

        //查询符合的主表
        QueryWrapper<OcBReturnOrder> orderWrapper = new QueryWrapper<OcBReturnOrder>();
        orderWrapper.in(ID, idList);
        List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectList(orderWrapper);
        List<Long> warehouseInIds = ocBReturnOrders.stream().map(x -> x.getCpCPhyWarehouseInId()).collect(Collectors.toList());
        Map<Long, CpCPhyWarehouse> warehouseMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(warehouseInIds)){
            warehouseMap = cpRpcService.rpcQueryCpCPhyWareHouses(warehouseInIds);
        }

        //转换主表对象返回
        if (CollectionUtils.isNotEmpty(ocBReturnOrders)) {
            for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
                ExtOcBReturnOrder extOcBReturnOrder = new ExtOcBReturnOrder();
                BeanUtils.copyProperties(ocBReturnOrder, extOcBReturnOrder);
                extOcBReturnOrder.setBillTypeName(ChangeEnum.billTypeDatas.get(extOcBReturnOrder.getBillType()));
                extOcBReturnOrder.setIsReservedName(ChangeEnum.yesOrNoDatas.get(extOcBReturnOrder.getIsReserved()));
                extOcBReturnOrder.setIsBackName(ChangeEnum.yesOrNoDatas.get(extOcBReturnOrder.getIsBack()));
                extOcBReturnOrder.setReturnReasonName(ChangeEnum.refundReasonNameDatas.get(extOcBReturnOrder.getReturnReason()));
                extOcBReturnOrder.setReturnStatusName(ChangeEnum.returnStatusNameDatas.get(extOcBReturnOrder.getReturnStatus()));
                CpCPhyWarehouse cPhyWarehouse = warehouseMap.get(ocBReturnOrder.getCpCPhyWarehouseInId());
                if (cPhyWarehouse != null){
                    extOcBReturnOrder.setCpCPhyWarehouseInEname(cPhyWarehouse.getEname());
                }
                if (StringUtils.isEmpty(extOcBReturnOrder.getReturnReason())) {
                    extOcBReturnOrder.setReturnReasonName("暂无退款原因");
                }
                extOcBReturnOrderList.add(extOcBReturnOrder);
            }

            //查询符合的明细表  1、换货明细  2、退货明细
            //1、换货明细
            QueryWrapper<OcBReturnOrderExchange> exchangeWrapper = new QueryWrapper<OcBReturnOrderExchange>();
            exchangeWrapper.in(OC_B_RETURN_ORDER_ID, idList);
            List<OcBReturnOrderExchange> ocBReturnOrderExchangeList = exchangeMapper.selectList(exchangeWrapper);
            if (CollectionUtils.isNotEmpty(ocBReturnOrderExchangeList)) {
                for (OcBReturnOrderExchange exchange : ocBReturnOrderExchangeList) {
                    ExtOcBReturnOrderExchange extOcBReturnOrderExchange = new ExtOcBReturnOrderExchange();
                    BeanUtils.copyProperties(exchange, extOcBReturnOrderExchange);
                    exchangeList.add(extOcBReturnOrderExchange);
                }
            }

            //2、退货明细
            QueryWrapper<OcBReturnOrderRefund> refundWrapper = new QueryWrapper<OcBReturnOrderRefund>();
            refundWrapper.in(OC_B_RETURN_ORDER_ID, idList);
            List<OcBReturnOrderRefund> refundWrapperList = refundMapper.selectList(refundWrapper);
            if (CollectionUtils.isNotEmpty(refundWrapperList)) {
                for (OcBReturnOrderRefund refund : refundWrapperList) {
                    ExtOcBReturnOrderRefund extOcBReturnOrderRefund = new ExtOcBReturnOrderRefund();
                    //BigDecimal allAmt = (refund.getAmtRefund() == null ? BigDecimal.ZERO : refund.getAmtRefund()).multiply(refund.getQtyRefund());
                    BeanUtils.copyProperties(refund, extOcBReturnOrderRefund);
                    extOcBReturnOrderRefund.setAllAmtRefund(refund.getAmtRefund() == null ? BigDecimal.ZERO : refund.getAmtRefund());
                    refundList.add(extOcBReturnOrderRefund);
                }
            }
        }

        //拼接返回对象
        OcBReturnOrderExportResult exportResult = new OcBReturnOrderExportResult();
        exportResult.setOrderList(extOcBReturnOrderList);
        exportResult.setExchangeList(exchangeList);
        exportResult.setRefundList(refundList);
        holderV14.setData(exportResult);
        return holderV14;
    }


}
