package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.dto.Pod2BOrderQueryDTO;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.oc.oms.mapper.OcBOrderByAdbMapper;
import com.jackrain.nea.oc.oms.model.result.Pod2BOrderQueryRequest;
import com.jackrain.nea.oc.oms.nums.EnvEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName Pod2BOrderQueryService
 * @Description 订单查询
 * <AUTHOR>
 * @Date 2024/9/2 11:07
 * @Version 1.0
 */
@Slf4j
@Component
public class Pod2BOrderQueryService {

    @Autowired
    private OcBOrderByAdbMapper ocBOrderByAdbMapper;

    @TargetDataSource(name = "adb")
    public List<Pod2BOrderQueryDTO> queryDTOList(Pod2BOrderQueryRequest request) {
        String db = "test_rpt_basics";
        if (EnvEnum.getEnv().equals(EnvEnum.PROD)) {
            db = "prod_rpt_basics";
        }
        // 计算limit与offset
        Integer limit = request.getPageSize();
        Integer offset = (request.getPageNum() - 1) * request.getPageSize();
        List<Pod2BOrderQueryDTO> pod2BOrderQueryDTOList = ocBOrderByAdbMapper.selectOrders(request.getTid(), request.getBillNo(), request.getOnNo(), request.getWarehouseCode(), request.getLogisticsCode(),
                request.getSkuCode(), request.getBuyerProvince(), request.getBuyerCity(), request.getBuyerArea(), request.getShopCode(), request.getSalesDepartment(),
                request.getAuditor(), request.getStartCreateTime(), request.getEndCreateTime(), request.getStartAuditTime(), request.getEndAuditTime(),
                request.getStartDistributeTime(), request.getEndDistributeTime(), request.getOrderStatusList(), request.getCreateDate(), request.getWarehouseName(),
                limit, offset, db);
        return pod2BOrderQueryDTOList;
    }

    @TargetDataSource(name = "adb")
    public int getTotal(Pod2BOrderQueryRequest request) {
        String db = "test_rpt_basics";
        if (EnvEnum.getEnv().equals(EnvEnum.PROD)) {
            db = "prod_rpt_basics";
        }
        return ocBOrderByAdbMapper.getTotal(request.getTid(), request.getBillNo(), request.getOnNo(), request.getWarehouseCode(), request.getLogisticsCode(),
                request.getSkuCode(), request.getBuyerProvince(), request.getBuyerCity(), request.getBuyerArea(), request.getShopCode(), request.getSalesDepartment(),
                request.getAuditor(), request.getStartCreateTime(), request.getEndCreateTime(), request.getStartAuditTime(), request.getEndAuditTime(), request.getStartDistributeTime(),
                request.getEndDistributeTime(), request.getOrderStatusList(), request.getCreateDate(), db);
    }
}
