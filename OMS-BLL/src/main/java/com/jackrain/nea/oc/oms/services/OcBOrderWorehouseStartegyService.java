package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 线上资金占用服务获取仓库发货需要处理的订单
 *
 * @author: ming.fz
 * @since: 2019-09-3
 * create at : 2019-09-3 10:38
 */
@Component
@Slf4j
public class OcBOrderWorehouseStartegyService {

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    PropertiesConf propertiesConf;

    /**
     * 从ES中查询仓库发货的的单据id
     *
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return 单据编号列表
     */
    public List<Long> selectUnTransferredOrderFromEs(int pageIndex, int pageSize) {
        List<Long> orderIdList = new ArrayList<>();
        try {
            //重试次数
            int num = propertiesConf.getProperty("order.strategy.price.num", 4);
            orderIdList = ES4Order.findIdByOrderStatusAndRetryTimes(pageIndex, pageSize, num);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("线上资金占用服务仓库发货定时任务出错！,error:{}"), Throwables.getStackTraceAsString(ex));
        }
        return orderIdList;
    }

    /**
     * 查询订单数据
     *
     * @param orderIds 订单id
     * @return 订单集合
     */
    public List<OcBOrderParam> selectOrder(List<Long> orderIds) {
        List<OcBOrderParam> ocBOrderParams = new ArrayList<>();
        if (orderIds == null || orderIds.size() == 0) {
            return ocBOrderParams;
        }
        List<OcBOrder> ocBOrders = orderMapper.selectOrderListByIds(orderIds);
        for (OcBOrder ocBOrder : ocBOrders) {
            if (ocBOrder == null) {
                continue;
            }
            int orderStatus = ocBOrder.getOrderStatus();
            if (orderStatus == OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger() || orderStatus == OmsOrderStatus.PLATFORM_DELIVERY.toInteger()) {
                OcBOrderParam ocBOrderParam = new OcBOrderParam();
                ocBOrderParam.setOcBOrder(ocBOrder);
                List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemList(ocBOrder.getId());
                if (orderItems == null) {
                    continue;
                }
                ocBOrderParam.setOrderItemList(orderItems);
                ocBOrderParams.add(ocBOrderParam);
            }
        }
        return ocBOrderParams;
    }


}