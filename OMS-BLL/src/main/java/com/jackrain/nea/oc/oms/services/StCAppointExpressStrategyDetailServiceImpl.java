package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.oc.oms.mapper.StCAppointExpressStrategyDetailMapper;
import com.jackrain.nea.st.model.StCAppointExpressStrategyDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName StCAppointExpressStrategyDetailServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/12 17:17
 * @Version 1.0
 */
@Slf4j
@Service
public class StCAppointExpressStrategyDetailServiceImpl extends ServiceImpl<StCAppointExpressStrategyDetailMapper, StCAppointExpressStrategyDetail> implements StCAppointExpressStrategyDetailService {

    @Override
    public StCAppointExpressStrategyDetail getByMatchRuleAndMatchContent(Long mainId, String matchRule, String matchContent) {
        // 根据匹配规则、匹配内容来查询数据
        return baseMapper.selectOne(new QueryWrapper<StCAppointExpressStrategyDetail>().lambda()
                .eq(StCAppointExpressStrategyDetail::getStrategyId, mainId).eq(StCAppointExpressStrategyDetail::getMatchRule, matchRule)
                .eq(StCAppointExpressStrategyDetail::getMatchContent, matchContent).eq(StCAppointExpressStrategyDetail::getIsactive, "Y"));
    }

    @Override
    public List<StCAppointExpressStrategyDetail> selectByStrategyId(Long strategyId) {
        return baseMapper.selectList(new QueryWrapper<StCAppointExpressStrategyDetail>().lambda()
                .eq(StCAppointExpressStrategyDetail::getStrategyId, strategyId).eq(StCAppointExpressStrategyDetail::getIsactive, "Y"));
    }


}
