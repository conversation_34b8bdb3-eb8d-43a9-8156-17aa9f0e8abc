package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.oc.oms.model.enums.LogisticsServiceTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLogisticsIntercept;
import com.jackrain.nea.oc.oms.nums.LogisticsInterceptStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 京东物流拦截
 *
 * <AUTHOR>
 * @Date 2023/7/6
 */
@Slf4j
@Service
public class LogisticsInterceptService extends ZtoLogisticsInterceptService {

    @Override
    public List<OcBOrderLogisticsIntercept> queryInterceptList(String type) {
        log.info("京东、丹鸟、EMS、、顺丰物流拦截失败重试");
        // 查询失败次数小于6次并且发起拦截失败的数据,更新时间小于当前时间一个小时
        List<Integer> interceptState = Arrays.asList(LogisticsInterceptStatusEnum.SEND_INTERCEPT_FAIL.getKey(),
                LogisticsInterceptStatusEnum.NOT_INTERCEPT.getKey());

        return getOcBOrderLogisticsInterceptMapper().selectList(new LambdaQueryWrapper<OcBOrderLogisticsIntercept>()
                .in(OcBOrderLogisticsIntercept::getLogisticsServiceType,
                        Arrays.asList(LogisticsServiceTypeEnum.JD.getVal(),
                                LogisticsServiceTypeEnum.DN.getVal()),
                        LogisticsServiceTypeEnum.EMS.getVal(),
                        LogisticsServiceTypeEnum.SF.getVal(),
                        LogisticsServiceTypeEnum.YTO.getVal())
                .in(OcBOrderLogisticsIntercept::getInterceptStatus, interceptState)
                .le(OcBOrderLogisticsIntercept::getInterceptFailureNum, 24)
                .le(OcBOrderLogisticsIntercept::getModifieddate, DateUtils.addMinutes(new Date(), -30)));
    }
}
