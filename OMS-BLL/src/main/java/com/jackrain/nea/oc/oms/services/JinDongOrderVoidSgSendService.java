package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Desc : 京东作废发货单
 * <AUTHOR> xiWen
 * @Date : 2020/7/21
 */
@Slf4j
@Component
public class JinDongOrderVoidSgSendService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderJdSplitService omsOrderJdSplitService;

    public ValueHolderV14 voidSgSendService(Long orderId, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        try {
            OcBOrder origOrder = omsOrderService.selectOrderUnConfirmAndStockInfo(orderId);
            if (origOrder == null) {
                log.error("JinDongOrderVoidSgSendService.Order Is Null");
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("作废发货单失败, 未找到原单");
                return vh;
            }


            ValueHolderV14 voidSgBSendRet = omsOrderJdSplitService.voidSgBSend(origOrder);
            if (log.isDebugEnabled()) {
                log.debug("订单OrderId:{}, JinDongOrderVoidSgSendService.voidSgBSend请求返回结果为:{}", origOrder.getId(),
                        voidSgBSendRet.toString());
            }

            if (voidSgBSendRet.isOK()) {
                OcBOrder ocBOrderTmp = new OcBOrder();
                ocBOrderTmp.setId(origOrder.getId());
                ocBOrderTmp.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
                ocBOrderTmp.setModifierename(user.getEname());
                ocBOrderTmp.setModifieddate(new Date());
                omsOrderService.updateOrderInfo(ocBOrderTmp);

                if (log.isDebugEnabled()) {
                    log.debug("订单OrderId:{}, JinDongOrderVoidSgSendService.void原单", origOrder.getId());
                }
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("作废发货单成功");
                return vh;
            }
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("作废发货单失败");
            vh.setData(voidSgBSendRet.getMessage());
        } catch (Exception e) {
            log.error("JinDongOrderVoidSgSendService ", e);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("作废发货单失败");
            vh.setData(e.getMessage());
        }
        return vh;
    }
}
