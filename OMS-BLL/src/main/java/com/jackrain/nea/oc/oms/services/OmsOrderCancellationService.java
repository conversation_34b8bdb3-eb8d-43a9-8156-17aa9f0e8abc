package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.AddAndVoidStockListService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 订单作废服务
 *
 * @author: heliu
 * @since: 2019/3/12
 * create at : 2019/3/12 19:08
 */
@Component
@Slf4j
public class OmsOrderCancellationService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private AddAndVoidStockListService addAndVoidStockListService;

    /**
     * 判断“订单状态”是否为"待分配",“待审核”、“缺货”、“已审核”、“配货中”且配货中,WMS为已撤回的能够作废
     *
     * @param orderInfo 订单信息
     * @return true - 可以作废；False-不可以
     */
    private boolean checkCanVoidOrder(OcBOrder orderInfo) {
        return (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderInfo.getOrderStatus())
                || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderInfo.getOrderStatus())
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderInfo.getOrderStatus())
                || OmsOrderStatus.CHECKED.toInteger().equals(orderInfo.getOrderStatus())
                || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderInfo.getOrderStatus()))
                || (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderInfo.getOrderStatus())
                && OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == orderInfo.getWmsCancelStatus());
    }

    /**
     * 订单作废
     *
     * @param orderInfo 订单对象
     * @return boolean
     */

    @Transactional(rollbackFor = Exception.class)
    public boolean doOrderInvalid(OcBOrder orderInfo, User user) {
        try {
            boolean canVoidOrder = this.checkCanVoidOrder(orderInfo);
            //判断“订单状态”是否为"待分配",“待审核”、“缺货”、“已审核”、“配货中”且配货中,WMS为已撤回的能够作废
            if (canVoidOrder) {
                //非待分配状态需调用取消发货逻辑单服务
                omsOrderService.updateOrderSystemVoid(orderInfo, user);
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("订单作废状态异常,异常信息为:{}",orderInfo.getId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }


    /**
     * 订单作废调用作废逻辑单服务【这个方法调用的前提是已经在库存中心生成逻辑发货单了,如果没有生成逻辑发货单就用上面的方法】
     *
     * @param orderInfo 订单对象
     * @return ValueHolderV14
     */

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<Object> doInvoildOutOrder(OcBOrder orderInfo, User user) {
        ValueHolderV14<Object> valueHolderV14 = new ValueHolderV14<>();
        try {
            boolean canVoidOrder = this.checkCanVoidOrder(orderInfo);
            if (canVoidOrder) {
                boolean isCc = OmsBusinessTypeUtil.isToBOrder(orderInfo) && OrderSaleProductAttrEnum.isToBCC(orderInfo.getSaleProductAttr());
                if(orderInfo.getOrderStatus().equals(OmsOrderStatus.UNCONFIRMED.toInteger())) {
                    //调用取消发货逻辑单服务
                    if (log.isDebugEnabled()) {
                        log.debug("orderID的订单ID为{}开始调用取消发货逻辑单服务", orderInfo.getId());
                    }
                    // 修复作废报错问题 2022-10-06
                    ValueHolderV14 voidSgOutOrderResult = sgRpcService.invoildOutOrder(isCc, orderInfo, user);

                    if (log.isDebugEnabled()) {
                        log.debug("orderID的订单ID为{}开始调用取消发货逻辑单服务出参Result={}", orderInfo.getId(),
                                voidSgOutOrderResult.toJSONObject());
                    }
                    if (voidSgOutOrderResult.getCode() == ResultCode.FAIL) {
                        String message = "orderID的订单ID为" + orderInfo.getId() + "调用取消发货逻辑单服务异常-->" + voidSgOutOrderResult.getMessage();
                        log.error(message);
                        valueHolderV14.setCode(ResultCode.FAIL);
                        valueHolderV14.setMessage(message);
                        return valueHolderV14;
                    }
                if (log.isDebugEnabled()) {
                    log.debug("orderID的订单ID为{}调用取消发货逻辑单服务完成", orderInfo.getId());
                }
                }
                omsOrderService.updateOrderSystemVoid(orderInfo, user);

                valueHolderV14.setCode(ResultCode.SUCCESS);
                valueHolderV14.setMessage("success");
            } else {
                String message = "订单ID为 " + orderInfo.getId() + " 的订单状态非待审核、缺货、已审核、配货中，不允许订单作废!";
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage(message);
            }
            return valueHolderV14;
        } catch (Exception ex) {
            log.error(LogUtil.format("订单作废状态异常,异常信息为:{}", orderInfo.getId()), Throwables.getStackTraceAsString(ex));
            valueHolderV14.setData(ResultCode.FAIL);
            valueHolderV14.setMessage(ex.getMessage());
            return valueHolderV14;
        }
    }

    /**
     * 取消合单专用
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder doInvalidOutOrder(OcBOrderRelation mergeRelation,
                                         List<OcBOrderRelation> mergerOrigOrderRelationList, User user) {
        ValueHolder vh = new ValueHolder();
        try {
            //调用取消发货逻辑单服务
            // 作废原逻辑发货单，生成新逻辑发货单
            boolean result = addAndVoidStockListService.splitOrderVoidAndAddStick(mergeRelation, mergerOrigOrderRelationList, user);
            if (!result) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "调用取消发货逻辑单服务异常");
                return vh;
            }
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "取消合单成功");
            return vh;
        } catch (Exception ex) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", ex.getMessage());
            return vh;
        }
    }
}
