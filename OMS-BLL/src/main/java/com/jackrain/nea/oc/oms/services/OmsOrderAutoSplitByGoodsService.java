package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.OmsOrderSplitEngine;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.constant.OrderSplitStrategyEnum;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.util.*;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.sg.service.AddAndVoidStockListService;
import com.jackrain.nea.st.model.result.BaseResult;
import com.jackrain.nea.st.model.result.StCWarehouseQueryResult;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.OrderPushDelayStrategyService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.OrderTagUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 根据商品属性自动拆单
 */
@Component
@Slf4j
public class OmsOrderAutoSplitByGoodsService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private AddAndVoidStockListService addAndVoidStockListService;

    @Autowired
    private OmsOrderSplitService omsOrderSplitService;

    @Autowired
    private OmsOrderJdSplitService omsOrderJdSplitService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsOrderAutoSplitUtil omsOrderAutoSplitUtil;

    @Autowired
    private OmsOrderSplitEngine omsOrderSplitEngine;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OmsWmsTaskService wmsTaskService;

    @Autowired
    private OrderPushDelayStrategyService orderPushDelayStrategyService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    @Autowired
    private OmsAuditTaskService omsAuditTaskService;

    @Autowired
    private SplitOrderUtils splitOrderUtils;

    @Autowired
    OmsOrderSplitReasonUtil omsOrderSplitReasonUtil;

    /**
     * 处理需要拆单的订单列表
     *
     * @param orderIds
     */
    public void handleSplitMqOrderList(List<Long> orderIds, User user) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        for (Long orderId : orderIds) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    this.splitOrder(user, orderId);
                } else {
                    OcBOrder ocBOrder = this.omsOrderService.selectOrderInfo(orderId);
                    //加入审核中间表
                    omsAuditTaskService.createOcBAuditTask(ocBOrder, OmsAuditTimeCalculateReason.SPLIT);
                    //this.wmsTaskService.saveOrUpdateOcBToWmsTask(ocBOrder, user);
                }
            } catch (Exception e) {
                OcBOrder ocBOrder = this.omsOrderService.selectOrderInfo(orderId);
                omsAuditTaskService.createOcBAuditTask(ocBOrder, OmsAuditTimeCalculateReason.SPLIT);
                //this.wmsTaskService.saveOrUpdateOcBToWmsTask(ocBOrder, user);
            } finally {
                redisLock.unlock();
            }
        }


    }

    /**
     * 按照XSKU/SPU拆单
     *
     * @param ocBOrderId
     */
    public void splitOrderBySkuAndSpu(Long ocBOrderId) {
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(ocBOrderId);
        // 天猫超市订单  货到付款订单不支持拆单
        if (!splitOrderUtils.isOpenWareHouseSplitOrder(ocBOrder)
                || !OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
            return;
        }
        StCWarehouseQueryResult stCWarehouseQueryResult =
                stRpcService.getStCWarehouseQueryResultByWareHouseId(ocBOrder.getCpCPhyWarehouseId());
        if (stCWarehouseQueryResult != null
                && ("Y".equals(stCWarehouseQueryResult.getStCWarehouse().getIsGoodsSplit())
                || "Y".equals(stCWarehouseQueryResult.getStCWarehouse().getIsSkuSplit()))) {
            // 只做SKU/SPU拆单
            stCWarehouseQueryResult.getStCWarehouse().setIsSexSplit("N");
            stCWarehouseQueryResult.getStCWarehouse().setIsBrandSplit("N");
            stCWarehouseQueryResult.getStCWarehouse().setIsGoodsSplit("N");
            OcBOrderRelation relation = new OcBOrderRelation();
            relation.setOrderInfo(ocBOrder);
            this.doSplitOrder(SystemUserResource.getRootUser(), relation,
                    stCWarehouseQueryResult, OmsOrderStatus.UNCONFIRMED.toInteger());
        }
    }

    /**
     * 性别和品牌组拆单拆单
     * @param user
     * @param orderId
     */
    public void
    splitOrder(User user, Long orderId) {
        OcBOrderRelation orderInfo = omsOrderService.selectOmsOrderInfo(orderId);
        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        // 是否支持仓库拆单判断
        // 1.猫超直发，货到付款订单 不支持拆
        // 2.未配置仓库拆单策略，不拆单
        // 3.仓库拆单总开关未开，不拆单
        if (!splitOrderUtils.isOpenWareHouseSplitOrder(ocBOrder)) {
            omsAuditTaskService.createOcBAuditTask(ocBOrder, OmsAuditTimeCalculateReason.SPLIT);
            //this.wmsTaskService.saveOrUpdateOcBToWmsTask(ocBOrder, user);
            return;
        }
        // 订单状态做校验
        if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
            return;
        }
        StCWarehouseQueryResult stCWarehouseQueryResult =
                stRpcService.getStCWarehouseQueryResultByWareHouseId(orderInfo.getOrderInfo().getCpCPhyWarehouseId());
        if (stCWarehouseQueryResult != null
                && ("Y".equals(stCWarehouseQueryResult.getStCWarehouse().getIsSexSplit())
                || "Y".equals(stCWarehouseQueryResult.getStCWarehouse().getIsBrandSplit())
                || "Y".equals(stCWarehouseQueryResult.getStCWarehouse().getIsGoodsClassSplit()))
                && !orderInfo.getOrderInfo().getPlatform().equals(PlatFormEnum.ALIBABAASCP.getCode())) {
            // 推WMS前推 只根据性别和品牌，品类组拆单
            stCWarehouseQueryResult.getStCWarehouse().setIsGoodsSplit("N");
            stCWarehouseQueryResult.getStCWarehouse().setIsSkuSplit("N");
            try {
                boolean result = this.doSplitOrder(user, orderInfo, stCWarehouseQueryResult,
                        orderInfo.getOrderInfo().getOrderStatus());
                if (!result) {
                    omsAuditTaskService.createOcBAuditTask(ocBOrder, OmsAuditTimeCalculateReason.SPLIT);
                    //this.wmsTaskService.saveOrUpdateOcBToWmsTask(orderInfo.getOrderInfo(), user);
                }
            } catch (Exception e) {
                omsAuditTaskService.createOcBAuditTask(ocBOrder, OmsAuditTimeCalculateReason.SPLIT);
                //this.wmsTaskService.saveOrUpdateOcBToWmsTask(ocBOrder, user);
            }
        } else {
            omsAuditTaskService.createOcBAuditTask(ocBOrder, OmsAuditTimeCalculateReason.SPLIT);
            //this.wmsTaskService.saveOrUpdateOcBToWmsTask(ocBOrder, user);
        }
    }

    public boolean doSplitOrder(User user, OcBOrderRelation ocBOrderRelation, BaseResult strategy, Integer orderStatus) {
        OcBOrderRelation relation = new OcBOrderRelation();
        relation.setOrderInfo(ocBOrderRelation.getOrderInfo());
        // 重新查询 orderList
        List<OcBOrderItem> items = ocBOrderItemMapper.selectOrderItemListOccupy(ocBOrderRelation.getOrderInfo().getId());
        relation.setOrderItemList(items);
        // 调用仓库策略拆单
        List<OcBOrderRelation> relationList = omsOrderSplitEngine.startSplit(relation, user, strategy, OrderSplitStrategyEnum.COMMODITY.getCode());
        //自定义拆单赋值
        omsOrderSplitReasonUtil.setCustomReason(relationList);
        if (CollectionUtils.isEmpty(relationList) || relationList.size() <= 1) {
            return false;
        }
        try {
            List<OcBOrderRelation> occupyList = ApplicationContextHandle.getBean(OmsOrderAutoSplitByGoodsService.class).splitOrder(relation, relationList
                    , orderStatus, user);
            if (CollectionUtils.isNotEmpty(occupyList)) {
                for (OcBOrderRelation child : occupyList) {
                    if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(child.getOrderInfo().getOrderStatus())) {
                        try {
//                            // 分物流
//                            omsOrderAutoSplitUtil.distributeLogistics(child.getOrderInfo());
                            // 自动执行HOLD单策略
                            ocBOrderHoldService.autoHoldOrder(child, user);
                        } catch (Exception e) {
                            log.error(LogUtil.format("执行HOLD单策略异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("OmsOrderAutoSplitByGoodsService.doSplitOrder,拆单失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            return false;
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<OcBOrderRelation> splitOrder(OcBOrderRelation orderRelation, List<OcBOrderRelation> relationList, Integer orderStatus, User user) {
        List<OcBOrderRelation> jdOrderRelationList = new ArrayList<>();
        //实例化一个对象，用于批量作废和占用逻辑发货单
        List<OcBOrderRelation> occupyList = new ArrayList<>();
        for (OcBOrderRelation relation : relationList) {
            OrderTagUtil.orderTags(relation);
            OcBOrderRelation ocBOrderRelation = this.saveOrder(relation, orderStatus, jdOrderRelationList);
            occupyList.add(ocBOrderRelation);
        }
        StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(orderRelation.getOrderInfo().getCpCShopId());
        String isPlatformSplit = Optional.ofNullable(shopStrategy.getIsPlatformSplit()).orElse("Y");
        if (PlatFormEnum.JINGDONG.getCode().equals(orderRelation.getOrderInfo().getPlatform())
                && !"手工新增".equals( orderRelation.getOrderInfo().getOrderSource())
                && "Y".equals(isPlatformSplit)) {
            omsOrderJdSplitService.splitOrderByJingdong(orderRelation.getOrderInfo(), jdOrderRelationList, user);
        } else {
            Long oriOrderId = orderRelation.getOrderInfo().getId();
            // 作废原逻辑发货单，生成新逻辑发货单 todo 拆单后库存平移
//            boolean result = addAndVoidStockListService
//                    .splitOrderVoidAndAddStick(orderRelation, occupyList, user);
//            if (log.isDebugEnabled()) {
//                log.debug("OmsOrderAutoSplitByGoodsService.splitOrder方法原订单OrderId={}" +
//                        "订单自动拆单保存调用批量新增逻辑发货单服务出参result={}", oriOrderId, result);
//            }
//            if (!result) {
//                throw new NDSException("占用库存失败，不进行拆单");
//            }

            // 推WMS前
            String logMessage = "";
            for (OcBOrderRelation relation : occupyList) {
                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(relation.getOrderInfo().getOrderStatus())) {
                    logMessage = "按性别或品牌组或者品类拆单,作废原单";
                    omsAuditTaskService.createOcBAuditTask(relation.getOrderInfo(), OmsAuditTimeCalculateReason.SPLIT);
                    //this.wmsTaskService.saveOrUpdateOcBToWmsTask(relation.getOrderInfo(), user);
                } else if(OmsOrderStatus.UNCONFIRMED.toInteger().equals(relation.getOrderInfo().getOrderStatus())){
                    //订单操作日志
                    logMessage = "按SKU或SPU拆单,作废原单";
                    // @20201209 订单合单加密信息
                    if(SplitReason.SPLIT_BY_SKU.equals(relation.getOrderInfo().getSplitReason())){
                        MD5Util.encryptOrderInfo4Merge(relation.getOrderInfo());
                        ocBOrderMapper.updateById(relation.getOrderInfo());
                    }
                    this.omsAuditTaskService.createOcBAuditTask(relation.getOrderInfo(), OmsAuditTimeCalculateReason.SPLIT);
                }
            }
            //订单操作日志
            try {
                omsOrderLogService.addUserOrderLog(orderRelation.getOrderId(),
                        orderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(),
                        logMessage, "", "", user);
            } catch (Exception e) {
            }
        }
        return occupyList;
    }


    private OcBOrderRelation saveOrder(OcBOrderRelation relation, Integer orderStatus, List<OcBOrderRelation> jdOrderRelationList) {
        OcBOrderRelation newRelation = new OcBOrderRelation();
        Long orderId = ModelUtil.getSequence("oc_b_order");
        OcBOrder ocBOrder = bulidOcBOrder(relation.getOrderInfo(), orderId);
        List<OcBOrderItem> ocBOrderItemList = bulidOrderItemList(orderId, relation.getOrderItemList());
        // tagger 查单生成新单，需要打标：在单生成后保存前
        newRelation.setOrderInfo(ocBOrder);
        newRelation.setOrderItemList(ocBOrderItemList);
        OrderTagUtil.orderTags(newRelation);
        ocBOrder.setOrderStatus(orderStatus);
        if (PlatFormEnum.JINGDONG.getCode().equals(relation.getOrderInfo().getPlatform())
                && !relation.getOrderInfo().getPayType().equals(OmsPayType.CASH_ON_DELIVERY.toInteger())) {
            OcBOrderRelation jdOcBOrderRelation = new OcBOrderRelation();
            jdOcBOrderRelation.setOrderItemList(ocBOrderItemList);
            jdOcBOrderRelation.setOrderInfo(ocBOrder);
            jdOrderRelationList.add(jdOcBOrderRelation);
        } else {
            //保存订单对象
            omsOrderService.saveOrderInfo(ocBOrder);
            for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                omsOrderItemService.saveOcBOrderItem(ocBOrderItem, orderId);
            }
            try {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_SPLIT.getKey(), "仓库拆单,自动拆单新生成订单", "", "", SystemUserResource.getRootUser());
            } catch (Exception e) {
            }
        }
        newRelation.setOrderInfo(ocBOrder);
        // 排除 PRO_TYPE = 4，因为占库存不需要4的
        List<OcBOrderItem> items = ocBOrderItemList.stream().filter(item -> item.getProType() != 4).collect(Collectors.toList());
        newRelation.setOrderItemList(items);
        return newRelation;
    }

    private List<OcBOrderItem> bulidOrderItemList(Long orderId, List<OcBOrderItem> orderItemList) {
        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
        for (OcBOrderItem ocbItemDto : orderItemList) {
            OcBOrderItem orderItem = new OcBOrderItem();
            BeanUtils.copyProperties(ocbItemDto, orderItem);
            //重新生成Id
            orderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
            //设置订单Id
            orderItem.setOcBOrderId(orderId);
            //修改人
            orderItem.setModifierename(SystemUserResource.ROOT_USER_NAME);
            //修改时间
            orderItem.setModifieddate(new Date());
            ocBOrderItemList.add(orderItem);
        }
        return ocBOrderItemList;
    }


    /**
     * 构造新订单
     *
     * @param ocBorderDto 原始订单对象
     * @param orderNewId  新订单Id
     * @return 拆分后的订单对象
     */
    private OcBOrder bulidOcBOrder(OcBOrder ocBorderDto, Long orderNewId) {

        OcBOrder ocBOrder = new OcBOrder();
        //复制其他属性
        BeanUtils.copyProperties(ocBorderDto, ocBOrder);
        //设置ID
        ocBOrder.setId(orderNewId);
        //订单编号
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        //是否拆分原单
        ocBOrder.setIsSplit(1);
        //oaid
        ocBOrder.setOaid(ocBorderDto.getOaid());
        //拆分原订单号
        ocBOrder.setSplitOrderId(ocBorderDto.getId());
        //平台单号
        ocBOrder.setSourceCode(ocBorderDto.getSourceCode());
        //设置创建人
        ocBOrder.setOwnername(SystemUserResource.ROOT_USER_NAME);
        //创建时间
        ocBOrder.setCreationdate(new Date());
        //修改人
        ocBOrder.setModifierename(SystemUserResource.ROOT_USER_NAME);
        //修改时间
        ocBOrder.setModifieddate(new Date());
        //系统备注
        ocBOrder.setSysremark("");
        return ocBOrder;
    }

}
