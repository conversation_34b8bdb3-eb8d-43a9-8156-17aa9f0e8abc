package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.es.ES4RefundIn;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.IsGenWroAdjustEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatus;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sg.service.SgStockAdjustmentService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description: 错发调整单补偿任务
 * @author: 郑小龙
 * @date: 2019-09-26 10:19
 */
@Slf4j
@Component
public class ReturnWrongAdjustmentServer {

    @Autowired
    OcBRefundInMapper refundInMapper;
    @Autowired
    OcBRefundInProductItemMapper inProductItem;
    @Autowired
    OcBReturnOrderRefundMapper refundMapper;
    @Autowired
    SgStockAdjustmentService adjustmentService;
    @Autowired
    OcBRefundInLogService refundInLogService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    public ValueHolderV14 execLogic(int pageSize) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "成功");
        try {
            Date d = new Date();
            SimpleDateFormat startSdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(d);
            calendar.add(Calendar.DATE, -1);
            String beginDate = startSdf.format(calendar.getTime());//昨天
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Long startTime = sdf.parse(beginDate + " 22:00:00").getTime();
            Long endTime = System.currentTimeMillis();

            User user = SystemUserResource.getRootUser();
            Integer startIndex = 1;
            JSONObject search = ES4RefundIn.findJSONObjectByPageSizeStartEndTime(pageSize, (startIndex - 1) * pageSize, startTime, endTime);
            int totalCount = search.getInteger("total").intValue();
            if (totalCount <= 0) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("错误调整单补偿任务查询ES无数据！");
                return v14;
            }

            int page = totalCount / pageSize;
            if (totalCount % pageSize != 0) {
                page++;
            }

            for (int i = 0; i < page; i++) {
                search = ES4RefundIn.findJSONObjectByPageSizeStartEndTime(pageSize, i * pageSize, startTime, endTime);
                if (search == null) {
                    continue;
                }
                JSONArray data = search.getJSONArray("data");
                if (CollectionUtils.isEmpty(data)) {
                    continue;
                }

                //获取ID编号数组
                List<Long> longList = new ArrayList<>();
                for (int j = 0; j < data.size(); j++) {
                    long id = data.getJSONObject(j).getLong("ID");
                    longList.add(id);
                }

                QueryWrapper wrapper = new QueryWrapper();
                wrapper.in("id", longList);
                wrapper.orderByAsc("MODIFIEDDATE");
                List<OcBRefundIn> ocBRefundIns = refundInMapper.selectList(wrapper);
                if (CollectionUtils.isEmpty(ocBRefundIns)) {
                    continue;
                }
                //循环处理符合条件的主表数据
                for (OcBRefundIn ocBRefundIn : ocBRefundIns) {
                    long id = ocBRefundIn.getId();
                    String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
                    RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                    try {
                        if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                            processRefund(id, user);
                        }
                    } catch (Exception e) {
                        continue;
                    } finally {
                        redisLock.unlock();
                    }
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("错发补偿任务处理异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
            return v14;
        }
        return v14;
    }

    /**
     * 根据退货入库主表生成错发调整单
     */
    public boolean processRefund(Long id, User user) {
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("id", id);
        wrapper.eq("IN_STATUS", ReturnStatus.WAREHOUSING.toInteger());//2已入库的
        OcBRefundIn ocBRefundIn = refundInMapper.selectOne(wrapper);
        if (null == ocBRefundIn) {
            return false;
        }
        QueryWrapper itemwrapper = new QueryWrapper();
        itemwrapper.eq("oc_b_refund_in_id", ocBRefundIn.getId());
        List<OcBRefundInProductItem> productItems = inProductItem.selectList(itemwrapper);
        if (CollectionUtils.isEmpty(productItems)) {
            return false;
        }

        List<List<OcBRefundInProductItem>> itemList = getRefundInProductItemList(productItems);
        if (CollectionUtils.isEmpty(itemList)) {
            return false;
        }
        //循环退货单号
        for (List<OcBRefundInProductItem> ocBRefundInProductItems : itemList) {
            List<OcBRefundInProductItem> rightItem = new ArrayList<>();
            List<Long> ids = new ArrayList<>(); // 明细的id 集合
            //循环退货单号对应的所有入库明细
            for (OcBRefundInProductItem item : ocBRefundInProductItems) {
                //发出条码（如果存在实收条码则为实收条码信息集合）正向数量
                if (!StringUtils.isEmpty(item.getRealSkuEcode())) {
                    if (null == item.getRealSkuId()) {
                        ProductSku sku = psRpcService.selectProductSku(item.getRealSkuEcode() + "");
                        if (null != sku) {
                            item.setRealSkuId(sku.getId());
                        }
                    }
                    item.setPsCSkuId(item.getRealSkuId());
                    item.setPsCSkuEcode(item.getRealSkuEcode());
                    item.setQty(item.getQty());
                    rightItem.add(item);
                } else {
                    item.setQty(item.getQty());
                    rightItem.add(item);
                }

                //实际发出条码为负向数量的调整单
                QueryWrapper wrapper1 = new QueryWrapper();
                wrapper1.eq("ps_c_sku_ecode", item.getPsCSkuEcodeActual());
                wrapper1.eq("oc_b_return_order_id", item.getOcBReturnOrderId());
                List<OcBReturnOrderRefund> refundList = refundMapper.selectList(wrapper1);
                if (CollectionUtils.isEmpty(refundList)) {
                    if (log.isDebugEnabled()) {
                        log.debug("入库单ID[{}]根据退换货id和条码查不到退换货信息，请检查参数！", ocBRefundIn.getId());
                    }
                    continue;
                }

                OcBReturnOrderRefund refund = refundList.get(0);
                OcBRefundInProductItem productItem = new OcBRefundInProductItem();
                BeanUtils.copyProperties(item, productItem);
                productItem.setPsCSkuId(refund.getPsCSkuId());
                productItem.setPsCSkuEcode(item.getPsCSkuEcodeActual());
                productItem.setPsCProId(refund.getPsCProId());
                productItem.setPsCProEcode(refund.getPsCProEcode());
                productItem.setPsCProEname(refund.getPsCProEname());
                productItem.setQty(new BigDecimal(-item.getQty().intValue()));
                productItem.setOwnerename(user.getEname());
                productItem.setOwnername(user.getName());
                productItem.setIsactive("Y");
                productItem.setCreationdate(new Date());
                productItem.setModifieddate(new Date());
                rightItem.add(productItem);
                ids.add(item.getId());
            }

            if (CollectionUtils.isEmpty(rightItem)) {
                continue;
            }

            ValueHolderV14 holderV14 = new ValueHolderV14();
            try {
                holderV14 = adjustmentService.addAdjustmen(ocBRefundIn, rightItem, user,
                        SgConstantsIF.SERVICE_NODE_ADJUST_PROP_MISTAKE_ADJUSTMENT);
            } catch (Exception e) {
                log.error(LogUtil.format("错发调整单补偿任务调用调整单生成异常,异常信息为:{}", ocBRefundIn.getId()), Throwables.getStackTraceAsString(e));
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage(e.getMessage());
            }
            if (ResultCode.FAIL == holderV14.getCode()) {
                refundInLogService.addLog(ocBRefundIn.getId(), "错发调整单",
                        "退货单号[" + ocBRefundInProductItems.get(0).getOcBReturnOrderId() + "]错发调整单生成失败,原因：" + holderV14.getMessage(), user);
                continue;
            } else {
                //更新入库单明细条码是否生成错发调整单字段值为是
                QueryWrapper uitemwrapper = new QueryWrapper();
                uitemwrapper.in("id", ids);
                uitemwrapper.eq("oc_b_refund_in_id", ocBRefundIn.getId());
                OcBRefundInProductItem productItem = new OcBRefundInProductItem();
                productItem.setModifieddate(new Date());
                productItem.setModifierid(user.getId() + 0L);
                productItem.setModifierename(user.getEname());
                // 更新是否生成错发调整单字段值为是
                productItem.setIsGenWroAdjust(IsGenWroAdjustEnum.YES.integer());
                productItem.setModifiername(user.getName());
                int update = inProductItem.update(productItem, uitemwrapper);
//                try {
//                    List<OcBRefundInProductItem> list = inProductItem.selectList(uitemwrapper);
//                    SpecialElasticSearchUtil.indexDocuments(
//                            OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME,
//                            OcElasticSearchIndexResources.OC_B_RETURN_IN_PRODUCT_ITEM_TYPE_NAME, list,
//                            "OC_B_REFUND_IN_ID");
//                } catch (IOException e) {
//                    log.debug("入库单ID[{}]错发调整单补偿任务退货入库单明细推送es失败", ocBRefundIn.getId(), e);
//                }
            }
        }
        return true;
    }

    /**
     * 根据退货单号进行分组
     */
    private List<List<OcBRefundInProductItem>> getRefundInProductItemList(List<OcBRefundInProductItem> items1) {
        //根据退换货单号进行分组
        HashSet set = new HashSet();
        List<OcBRefundInProductItem> satisfyList = new ArrayList<>();
        for (int i = 0; i < items1.size(); i++) {
            //是否生成错发调整单为否，退货入库单明细存在实际发出条码，存在退单编号的数据
            OcBRefundInProductItem productItem = items1.get(i);
            if (productItem.getIsGenWroAdjust() == 0 &&
                    productItem.getOcBReturnOrderId() != null &&
                    productItem.getPsCSkuEcodeActual() != null) {
                satisfyList.add(productItem);
                set.add(productItem.getOcBReturnOrderId());
            }
        }

        Iterator iterator = set.iterator();
        List<List<OcBRefundInProductItem>> itemList = new ArrayList<>();
        while (iterator.hasNext()) {
            Object next = iterator.next();
            List productItem = new ArrayList();
            for (int i = 0; i < satisfyList.size(); i++) {
                if (next.equals(satisfyList.get(i).getOcBReturnOrderId())) {
                    productItem.add(satisfyList.get(i));
                }
            }
            itemList.add(productItem);
        }
        return itemList;
    }
}
