package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillVoidResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-05-13 10:39
 */
@Slf4j
@Component
public class OrderCancleWmsService {


    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    SgRpcService sgRpcService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    OmsOrderService omsOrderService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OrderExceptionTagService orderExceptionTagService;

    /**
     * 订单撤回wms按钮
     *
     * @return
     */
    public ValueHolderV14 cancleWms(List<Long> ids, User loginUser) {
        ValueHolderV14 result = new ValueHolderV14();
        Map<String, List> lockList = this.getLock(ids);
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectOrderListByIds(ids);
        List<RedisReentrantLock> lockListKey = (List<RedisReentrantLock>) lockList.get("lockList");//锁集合用来解锁
        StringBuffer message = new StringBuffer();
        boolean isSuccess = true;//用来最终返回失败还是成功
        try {
            if (CollectionUtils.isNotEmpty(lockList.get("failIds"))) {
                result.setCode(ResultCode.FAIL);
                result.setMessage("订单编号" + lockList.get("failIds") + ":" + "正在被操作,请稍后重试");
                return result;
            }
            if (CollectionUtils.isEmpty(ocBOrderList)) {
                result.setCode(ResultCode.FAIL);
                result.setMessage("订单编号" + ids + ":" + "找不到记录!!!");
                return result;
            }
            for (OcBOrder ocBOrder : ocBOrderList) {
                if (null != ocBOrder.getWmsCancelStatus() &&
                        ocBOrder.getWmsCancelStatus() == OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()) {
                    result.setCode(ResultCode.FAIL);
                    result.setMessage("订单编号" + ocBOrder.getId() + ":" + "已撤销成功,不允许重复撤销");
                    return result;
                }
            }
            if (ocBOrderList.size() != ids.size()) {
                List<Long> dbIds = ocBOrderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
                List<Long> noRefund = ids.stream().filter(x -> !dbIds.contains(x)).collect(Collectors.toList());
                result.setCode(ResultCode.FAIL);
                result.setMessage("订单编号" + noRefund + ":" + "找不到记录!!!");
                return result;
            }
            String errorMessage = checkParam(ocBOrderList);
            if (StringUtils.isNotEmpty(errorMessage)) {
                result.setCode(ResultCode.FAIL);
                result.setMessage(errorMessage);
                return result;
            }
            //作废出库通知单并从WMS撤回服务
            ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> vhould = new ValueHolderV14<>();
            try {
                vhould = sgRpcService.invoildOutgoingNotice(ocBOrderList, loginUser,true);
                if (log.isDebugEnabled()) {
                    log.debug("调用作废出库通知单返回:" + vhould);
                }
            } catch (Exception e) {
                log.error(LogUtil.format("调用作废出库通知单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                result.setCode(ResultCode.FAIL);
                result.setMessage(e.getMessage());
                return result;
            }
            if (vhould.getCode() == ResultCode.FAIL) {
                vhould.setCode(ResultCode.FAIL);
                vhould.setMessage(vhould.getMessage());
                return vhould;
            }

        } catch (Exception e) {
            log.error(LogUtil.format("wms撤回按钮异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        } finally {
            for (RedisReentrantLock lockItem : lockListKey) {
                lockItem.unlock();
            }
        }
        if (isSuccess) {
            result.setCode(ResultCode.SUCCESS);
        } else {
            result.setCode(ResultCode.FAIL);
        }
        result.setMessage(message.toString());
        return result;
    }

    /**
     * 推ES修改wms状态 i = 1 wms撤回成功  i= 0 wms撤回失败
     *
     * @param flagOrder
     * @return
     */
    public ValueHolderV14 saveOrderWmsStatusAndLog(OcBOrder flagOrder, User loginUser, Integer i, String errorMeaasge) {
        ValueHolderV14 result = new ValueHolderV14();
        StringBuilder message = new StringBuilder();
        if (i == 1) {
            try {
                boolean esResult = omsOrderService.updateOrderInfo(flagOrder);
            } catch (Exception e) {
                log.error(LogUtil.format("从wms撤回成功后,推送es异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            }
            try {
                omsOrderLogService.addUserOrderLog(flagOrder.getId(),
                        flagOrder.getBillNo(), OrderLogTypeEnum.WMS_CANCLE_SUCCESS.getKey(), "WMS撤回成功",
                        "", "", loginUser
                );
            } catch (Exception e) {
                log.error(LogUtil.format(" 从wms撤回成功后,日志调用异常,异常信息为:{}", flagOrder.getId()), Throwables.getStackTraceAsString(e));
            }
            message.append("[订单编号:" + flagOrder.getId() +
                    "从wms撤回成功]" + "\n");
            result.setCode(ResultCode.SUCCESS);
            result.setMessage(message.toString());
        } else {
            try {
                //寻源，异常单据打异常标
                orderExceptionTagService.checkMateException(flagOrder,errorMeaasge, OcBOrderConst.ORDER_TOWMS);
                boolean esResult = omsOrderService.updateOrderInfo(flagOrder);
                if (!esResult) {
                }
            } catch (Exception e) {
                log.error(LogUtil.format("从wms撤回失败,推送es异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            }
            try {
                omsOrderLogService.addUserOrderLog(flagOrder.getId(),
                        flagOrder.getBillNo(), OrderLogTypeEnum.WMS_CANCLE_FAIL.getKey(), errorMeaasge,
                        "", "", loginUser
                );
            } catch (Exception e) {
                log.error(LogUtil.format("从wms撤回失败日志调用异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            }
            message.append("[订单编号:" + flagOrder +
                    "从wms撤回失败]" + "\n");
            result.setCode(ResultCode.FAIL);
            result.setMessage(message.toString());
        }
        return result;
    }

    public String checkParam(List<OcBOrder> ocBOrderList) {
        StringBuffer errormessage = new StringBuffer();
        for (OcBOrder ocBOrder : ocBOrderList) {
            if (!OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(ocBOrder.getOrderStatus())) {
                // b.如果订单状态不是“配货中”，则返回提示信息：““XXX订单非配货中，不允许WMS撤回！”
                errormessage.append("订单编号" + ocBOrder.getId() + ":非配货中，不允许WMS撤回！\n");
                continue;
            }
            if (ocBOrder.getWmsCancelStatus() == OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()) {
                //c.如果订单状态是“配货中”，但是WMS撤回状态为“已撤回”，则返回提示：“XXX订单已从WMS系统撤回，不允许重复撤回！”（XXX为订单编号）
                errormessage.append("订单编号" + ocBOrder.getId() + ":已从WMS系统撤回，不允许重复撤回！\n");
                continue;
            }
        }
        return errormessage.toString();
    }

    /**
     * 锁单
     *
     * @param idList
     * @return
     */
    public Map<String, List> getLock(List<Long> idList) {
        List<RedisReentrantLock> lockList = new ArrayList<>();
        List<Long> ids = Lists.newArrayList();
        List<Long> failIds = Lists.newArrayList();
        Map<String, List> map = new HashMap<>();

        for (Long i : idList) {
            try {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(i);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockList.add(redisLock);
                    ids.add(i);
                } else {
                    failIds.add(i);
                }
            } catch (Exception e) {
                log.error(this.getClass().getName() + "订单锁定异常" + i);
                failIds.add(i);
            }
        }
        map.put("failIds", failIds);
        map.put("lockList", lockList);
        map.put("ids", ids);
        return map;
    }


}
