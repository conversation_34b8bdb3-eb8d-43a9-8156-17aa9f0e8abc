package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.MailInfoResult;
import com.jackrain.nea.st.model.table.StCVipcomMailDO;
import com.jackrain.nea.st.service.OmsOrderPriceSchemeService;
import com.jackrain.nea.st.service.VipComMailPlanService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 转单价格监控
 *
 * <AUTHOR>
 * @date 2020/12/10 10:34
 * @desc
 */
@Slf4j
public class OrderPriceValidate {

    /**
     * 是否发送监控消息(直接配置在阿里云,无具体信息提示,需要开发去看 log)
     *
     * @param orderInfo
     */
    public static void isSendDingTalk(OcBOrderRelation orderInfo) {
        StRpcService omsPriceQueryService = ApplicationContextHandle.getBean(StRpcService.class);
        OmsOrderPriceSchemeService service = ApplicationContextHandle.getBean(OmsOrderPriceSchemeService.class);
        try {
            int unPricrItemCount = 0;
            // 查询有效价格方案
            List<Long> stCPriceList = omsPriceQueryService.queryPriceList(orderInfo.getOrderInfo().getCpCShopId());
            // 根据明细去比对价格方案策略
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
            StringBuilder sbErrorMsg = new StringBuilder("订单OrderId" + orderInfo.getOrderId());
            StringBuilder sbOrderErrorMsg = new StringBuilder("订单OrderId" + orderInfo.getOrderId());
            for (OcBOrderItem orderItem : orderItemList) {
                // 商品价格
                BigDecimal price = orderItem.getRealAmt() == null ? BigDecimal.ZERO : orderItem.getRealAmt();
                String result = service.checkPriceScheme(stCPriceList, orderInfo.getOrderInfo(),
                        orderItem.getPsCSkuEcode(), orderItem.getPsCProId(), price, orderItem.getPsCProEcode());
                // 明细条码不满足最低成交价+1
                if (!StringUtils.equalsIgnoreCase("success", result)) {
                    log.error("OrderId={},低于成交价的数据={}", orderInfo.getOrderInfo().getId(), orderItem.getPsCProId());

                    unPricrItemCount++;
                    sbErrorMsg.append(result);
                    sbErrorMsg.append("\r\n");
                    sbOrderErrorMsg.append(result);
                }
            }
            if (unPricrItemCount > 0) {
                sendPriceErrorMsg(orderInfo, sbErrorMsg.toString());
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("订单执行价格校验异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
    }


    /**
     * 发送钉钉消息
     *
     * @param orderInfo
     * @param errorMsg
     */
    private static void sendPriceErrorMsg(OcBOrderRelation orderInfo, String errorMsg) {
        try {
            // 类型(VIP_TASK_NODE:订单转单:20)
            int type = 20;
            // 价格监控
            int saleType = 3;
            long shopId = orderInfo.getOrderInfo().getCpCShopId();
            VipComMailPlanService planService = ApplicationContextHandle.getBean(VipComMailPlanService.class);
            MailInfoResult mailInfoResult = planService.selectVipComMainPlan(shopId, type);
            if (mailInfoResult != null) {
                List<StCVipcomMailDO> stCVipcomMailDOS = mailInfoResult.getStCVipcomMailDOList();
                if (CollectionUtils.isNotEmpty(stCVipcomMailDOS)) {
                    //发送钉钉
                    List<String> result = Lists.newArrayList();
                    for (StCVipcomMailDO stCVipcomMailDO : stCVipcomMailDOS) {
                        if (!ObjectUtils.isEmpty(stCVipcomMailDO.getIsDingTalk())
                                && stCVipcomMailDO.getSaleType() == saleType
                                && stCVipcomMailDO.getIsDingTalk() == 1
                                && !org.springframework.util.StringUtils.isEmpty(stCVipcomMailDO.getDingTalkNo())) {
                            StringBuffer mailContent = new StringBuffer();
                            mailContent.append(stCVipcomMailDO.getMailTitle() == null ? ""
                                    : stCVipcomMailDO.getMailTitle() + "\n\n" + stCVipcomMailDO.getMailContent() == null ? ""
                                    : stCVipcomMailDO.getMailContent());
                            mailContent.append(errorMsg);
                            DingTalkUtil service = ApplicationContextHandle.getBean(DingTalkUtil.class);
                            String msg = service.sendRobotMsg(mailContent.toString(), stCVipcomMailDO.getDingTalkNo());
                            result.add(msg);
                            return;
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("OrderPriceValidate.sendPriceErrorMsg.Error,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
    }
}
