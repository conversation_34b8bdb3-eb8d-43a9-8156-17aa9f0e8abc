package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemFiMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWrongReceive;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.result.OcBReturnOrderRefundBatchResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.WmsControlWarehouse;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnAfterUtil;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.StCBusinessTypeService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 批量新增退单服务
 *
 * @author: 郑小龙
 * @create: 2020-03-12 09:07
 **/
@Component
@Slf4j
public class OcBReturnOrderBatchAddService {
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemFiMapper ocBorderItemMapper;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderFiMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundFiMapper;
    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;

    @Autowired
    private OmsBusinessTypeDistinguishService omsBusinessTypeDistinguishService;

    @Autowired
    private OmsRefundOrderService omsRefundOrderService;

    @Autowired
    private StCBusinessTypeService stCBusinessTypeService;
    private static final Integer FIVE_HUNDRED = 500;

    public ValueHolderV14 ReturnOrderBatchAdd(List<Long> ids, boolean isBack, User user) throws NDSException {
        OcBReturnOrderBatchAddService bean = ApplicationContextHandle.getBean(OcBReturnOrderBatchAddService.class);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.multiFormat("批量新增退单入参,是否原退:{},ids=", ids), isBack);
        }
        List<String> listString = new ArrayList<>();
        Integer back = isBack ? 1 : 0;
        String logMessage = "手工批量新增退货单";
        for (Long id : ids) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    bean.addReturnOrder(id, back, listString, user, logMessage, null, null);
                } else {
                    log.error(LogUtil.format("新增退单失败,当前订单其他人在操作，请稍后再试,订单id=", id));
                    listString.add("订单id[" + id + "]新增退单失败,当前订单其他人在操作，请稍后再试");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format(",新增退单异常,error:{},订单id=", id), Throwables.getStackTraceAsString(ex));
            } finally {
                redisLock.unlock();
            }
        }
        String success = "处理成功 " + (ids.size() - listString.size()) + ";处理失败 " + listString.size();
        if (listString.size() > 0) {
            success = success + listString;
        }
        ValueHolderV14 v14 = new ValueHolderV14();
        v14.setCode(ResultCode.SUCCESS);
        v14.setMessage(success);
        return v14;
    }

    public ValueHolderV14<Integer> generateByOcBOrder(List<Long> ids, boolean isBack, User user) throws NDSException {
        OcBReturnOrderBatchAddService bean = ApplicationContextHandle.getBean(OcBReturnOrderBatchAddService.class);
        if (log.isDebugEnabled()) {
            log.debug("新增退单入参ids:{},是否原退:{}", ids, isBack);
        }
        List<String> listString = new ArrayList<>();
        Integer back = isBack ? 1 : 0;
        String logMessage = "JitX订单已发货取消新增退货单";
        for (Long id : ids) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    bean.addReturnOrder(id, back, listString, user, logMessage, null, null);
                } else {
                    log.error("订单id:{}新增退单失败,当前订单其他人在操作，请稍后再试", id);
                    listString.add("订单id[" + id + "]新增退单失败,当前订单其他人在操作，请稍后再试");
                }
            } catch (Exception ex) {
                log.error("订单id:{},新增退单异常{}", id, Throwables.getStackTraceAsString(ex));
            } finally {
                redisLock.unlock();
            }
        }
        String success = "处理成功 " + (ids.size() - listString.size()) + ";处理失败 " + listString.size();
        if (listString.size() > 0) {
            success = success + listString;
        }
        ValueHolderV14 v14 = new ValueHolderV14();
        v14.setCode(ResultCode.SUCCESS);
        v14.setMessage(success);
        v14.setData(ids.size() - listString.size());
        return v14;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long addReturnOrder(Long id, Integer isback, List<String> listString, User user,
                               String logMessage, String tid, List<OcBOrderItem> ocBOrderItems) {
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(id);
        try {
            if (ocBOrder == null) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("新增退单失败,未找到原始订单,订单id=", id));
                }
                listString.add("[" + id + "]新增退单失败，未找到原始订单！");
                return null;
            }

            StCBusinessType stCBusinessType = stCBusinessTypeService.selectOneById(ocBOrder.getBusinessTypeId());
            if (Objects.isNull(stCBusinessType)) {
                listString.add("[" + ocBOrder.getBillNo() + "]未查询到该业务类型信息");
                return null;
            }
            if (!YesNoEnum.Y.getVal().equals(stCBusinessType.getIsAllowHandReturn())) {
                listString.add("[" + ocBOrder.getBillNo() + "]"+ocBOrder.getBusinessTypeName() + "类型的设置为不允许手工建退单!");
                return null;
            }

            //验证原单状态
            int orderStatus = ocBOrder.getOrderStatus();
            if (orderStatus != OcOrderCheckBoxEnum.CHECKBOX_PLATFORM_DELIVERY.getVal()
                    && orderStatus != OcOrderCheckBoxEnum.CHECKBOX_WAREHOUSE_DELIVERY.getVal()
                    && orderStatus != OcOrderCheckBoxEnum.CHECKBOX_TRANSACTION_COMPLETED.getVal()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("状态不正确,订单未发货不能进行新增退换货订单操作,订单id=", id));
                }
                listString.add("[" + ocBOrder.getBillNo() + "]新增退单失败，状态不正确！");
                return null;
            }
            //查询明细数据
            if (CollectionUtils.isEmpty(ocBOrderItems)) {
                QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("oc_b_order_id", id);
                queryWrapper.ne("pro_type",4);
                if (StringUtils.isNotEmpty(tid)) {
                    queryWrapper.eq("tid", tid);
                }
                ocBOrderItems = ocBorderItemMapper.selectList(queryWrapper);
            }
            if (CollectionUtils.isEmpty(ocBOrderItems)) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("订单id:{}新增退单失败,原单未查询到明细数据,订单id=", id));
                }
                listString.add("[" + ocBOrder.getBillNo() + "]新增退单失败,原单未查询到明细数据！");
                return null;
            }
            //验证明细是否包含组合商品
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                //验证退单明细是否包含组合商品
                try {
                    checkReEcode(ocBOrderItem);
                } catch (Exception e) {
                    log.error(LogUtil.format("新增退单失败,原单含有组合商品明细,error:{},订单id=", id),
                            Throwables.getStackTraceAsString(e));
                    listString.add("[" + ocBOrder.getBillNo() + "]新增退单失败,原单含有组合商品明细！");
                    return null;
                }
            }

            Long returnId = ModelUtil.getSequence("oc_b_return_order");
            //保存已退数量明细
            List<OcBOrderItem> itemsList = new ArrayList<>();
            //获取退单明细数据
            OcBReturnOrderRefundBatchResult listRefund = getRefundOrder(ocBOrderItems, itemsList, returnId, user);
            //验证总金额
            if (listRefund.getTotamt().compareTo(BigDecimal.ZERO) < 0) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("新增退单失败,退单总金额计算为负数,订单id=", id));
                }
                listString.add("[" + ocBOrder.getBillNo() + "]新增退单失败,退单总金额计算为负数！");
                return null;
            }
            //验证是否有退货明细
            if (CollectionUtils.isEmpty(listRefund.getListRefund())) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("新增退单失败,没有可退数量的明细,订单id=", id));
                }
                listString.add("[" + ocBOrder.getBillNo() + "]新增退单失败,没有可退数量的明细！");
                return null;
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format(" addReturnOrder totamt:{},addqty{},allsku{},订单id=", id), listRefund.getTotamt(),
                        listRefund.getAddQty(),
                        listRefund.getAllSku());
            }
            //新增主表
            OcBReturnOrder returnOrder = genReturnOrder(ocBOrder, returnId, isback, listRefund.getTotamt(),
                    listRefund.getAddQty(), listRefund.getAllSku(), user);
            String jointTid = OmsReturnAfterUtil.getJointTid(listRefund.getListRefund());
            returnOrder.setTid(jointTid);

            if (OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN.getCode().equals(returnOrder.getBusinessTypeCode()) ||
                    OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP_RETURN.getCode().equals(returnOrder.getBusinessTypeCode())) {
                for (OcBReturnOrderRefund returnOrderRefund : listRefund.getListRefund()) {
                    returnOrderRefund.setAmtRefundSingle(new BigDecimal("0.01"));
                    returnOrderRefund.setAmtRefund(returnOrderRefund.getAmtRefundSingle().multiply(returnOrderRefund.getQtyRefund()));
                    returnOrderRefund.setPriceSettle(new BigDecimal("0.01"));
                    returnOrderRefund.setAmtSettleTot(returnOrderRefund.getPriceSettle().multiply(returnOrderRefund.getQtyRefund()));
                }
            }

            ocBReturnOrderFiMapper.insert(returnOrder);
            //新增明细表
            for (OcBReturnOrderRefund refund : listRefund.getListRefund()) {
                ocBReturnOrderRefundFiMapper.insert(refund);
            }
            OcBOrder update = new OcBOrder();
            update.setId(ocBOrder.getId());
//            update.setOrigReturnOrderId(returnOrder.getId());
            //修改原单状态
            update.setReturnStatus(OcBorderListEnums.ReturnStatusEnum.RETURN_ING.getVal());
            ocBOrderMapper.updateById(update);
            //修改原单明细已退数量 TODO 死锁问题待解决 1123
            if (CollectionUtils.isNotEmpty(itemsList)) {
                for (OcBOrderItem item : itemsList) {
                    ocBorderItemMapper.updateOcBOrderItemQty(item);
                }
            }
            //添加新增日志到退单操作日志中
            OcBReturnOrderLog ocBReturnOrderLog = genReturnLog(returnId, user, logMessage);
            ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
            //新增退单成功添加订单日志
            omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.REFUND_ORDER_ADD.getKey(),
                    String.format("%s,退单ID:%d", logMessage, returnOrder.getId()), null, null, user);
            return returnId;
        } catch (Exception e) {
            log.error("{}, 新增退单失败,原因:{}", this.getClass().getName(), Throwables.getStackTraceAsString(e));
            if (ocBOrder == null) {
                listString.add("[" + id + "]新增退单失败，原因:" + e);
            } else {
                listString.add("[" + ocBOrder.getBillNo() + "]新增退单失败，原因:" + e);
            }
            throw new NDSException(e);
        }
    }

    /**
     * 新增退货主表
     */
    public OcBReturnOrder genReturnOrder(OcBOrder ocBOrder, Long returnid, Integer isback, BigDecimal totamt,
                                         BigDecimal addQty, String allSku, User user) {

        OcBReturnOrder returnOrder = new OcBReturnOrder();
        //原平台单号
        returnOrder.setTid(ocBOrder.getSourceCode());
        //单据编号
        returnOrder.setBillNo(sequenceUtil.buildReturnBillNo());
        //原始订单ID
        returnOrder.setOrigOrderId(ocBOrder.getId());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());
        //订单来源1
        returnOrder.setOrigSourceCode(ocBOrder.getSourceCode());
        //单据类型,1退货单，2退换货单
        returnOrder.setBillType(OcReturnBillTypeEnum.RETURN.getVal());
        //买家昵称
        returnOrder.setBuyerNick(ocBOrder.getUserNick());
        //平台店铺名称
        returnOrder.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        //平台店铺id
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        //平台店铺编码
        returnOrder.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        //是否原退
        returnOrder.setIsBack(isback);
        returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        returnOrder.setCpCPhyWarehouseInId(returnOrder.getCpCPhyWarehouseId());
        if (returnOrder.getIsBack() == 1) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(ocBOrder.getCpCPhyWarehouseId());
            if (Objects.nonNull(cpCPhyWarehouse)) {
                if (cpCPhyWarehouse.getOriginalReturnPhyWarehouseId() == null) {
                    returnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouse.getId());
                } else {
                    returnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouse.getOriginalReturnPhyWarehouseId());
                }
            }
            returnOrder.setLogisticsCode(ocBOrder.getExpresscode());//退回物流单号
            returnOrder.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());//退回物流公司id
            returnOrder.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());//退回物流公司编码
            returnOrder.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());//退回物流公司名称
        } else {
            // 非原退，取店铺策略
            if (Objects.nonNull(returnOrder.getCpCShopId())) {
                StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());

                boolean isMultiReturnWarehouse = Objects.nonNull(shopStrategy) &&
                        (Objects.isNull(shopStrategy.getIsMultiReturnWarehouse()) ||
                                shopStrategy.getIsMultiReturnWarehouse() == 0);
                if (isMultiReturnWarehouse) {
                    Long wareId = shopStrategy.getCpCWarehouseDefId() == null ? shopStrategy.getDefaultStoreId() : shopStrategy.getCpCWarehouseDefId();
                    returnOrder.setCpCPhyWarehouseInId(wareId);
                }
            }
        }
        //换货收货人
        returnOrder.setReceiveName(ocBOrder.getReceiverName());
        //换货收货人手机
        returnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());
        //换货收货人电话
        returnOrder.setReceivePhone(ocBOrder.getReceiverPhone());
        //换货收货人省份id
        returnOrder.setReceiverProvinceId(ocBOrder.getCpCRegionProvinceId());
        //换货收货人省份
        returnOrder.setReceiverProvinceName(ocBOrder.getCpCRegionProvinceEname());
        //换货收货人市id
        returnOrder.setReceiverCityId(ocBOrder.getCpCRegionCityId());
        //换货收货人市
        returnOrder.setReceiverCityName(ocBOrder.getCpCRegionCityEname());
        //换货收货人区id
        returnOrder.setReceiverAreaId(ocBOrder.getCpCRegionAreaId());
        //换货收货人区
        returnOrder.setReceiverAreaName(ocBOrder.getCpCRegionAreaEname());
        //地址
        returnOrder.setReceiveAddress(ocBOrder.getReceiverAddress());
        //换货邮费 默认0
        returnOrder.setShipAmt(BigDecimal.ZERO);
        //平台类型
        returnOrder.setPlatform(ocBOrder.getPlatform());
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getSourceCode());
        //是否手工审核
        returnOrder.setIsManualAudit(0);
        //换货金额
        returnOrder.setExchangeAmt(BigDecimal.ZERO);
        //商品应退金额
        returnOrder.setReturnAmtList(totamt);
        //退还运费
        returnOrder.setReturnAmtShip(ocBOrder.getShipAmt() == null ? BigDecimal.ZERO : ocBOrder.getShipAmt());
        //退还其他费用
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);
        //退款金额 = 商品应退金额+退还运费+退还其他费用-换货金额
        returnOrder.setReturnAmtActual(totamt.add(returnOrder.getReturnAmtShip()).add(returnOrder.getReturnAmtOther())
                .subtract(returnOrder.getExchangeAmt()));
        //代销结算金额
        returnOrder.setConsignAmtSettle(BigDecimal.ZERO);
        //退货总数量
        returnOrder.setQtyInstore(addQty);
        if (StringUtils.isNotEmpty(allSku) && allSku.length() > FIVE_HUNDRED) {
            allSku = allSku.substring(0, 500);
        }
        //明细sku+数量 集合
        returnOrder.setAllSku(allSku);
        returnOrder.setId(returnid);
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //是否手工新增  默认 1
        returnOrder.setIsAdd(1);
        //是否传ag,0未传，1已传，2失败，3不传
        returnOrder.setIsToag(0);
        returnOrder.setIsTransfer(0);
        returnOrder.setIsTodrp(0);
        returnOrder.setInventedStatus(0);
        //传WMS状态
        returnOrder.setIsTowms(0);
        //是否确认收货
        returnOrder.setIsReceiveConfirm(0);
        //wms撤回状态 默认未撤回0
        returnOrder.setWmsCancelStatus(0);
        //强制入库 默认0
        returnOrder.setIsForce(0);
        //加入“空运单号延迟推单有效时间”字段
        returnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder));
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        Integer platmform;
        String gwSourceGroup = ocBOrder.getGwSourceGroup();
        if (StringUtils.isNotEmpty(gwSourceGroup)) {
            platmform = Integer.valueOf(gwSourceGroup);
        } else {
            platmform = ocBOrder.getPlatform();
        }
        if (PlatFormEnum.SAP.getCode().equals(platmform) || PlatFormEnum.DMS.getCode().equals(platmform)) {
            QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("oc_b_order_id", ocBOrder.getId());
            List<OcBOrderItem> ocBOrderItems = ocBorderItemMapper.selectList(queryWrapper);
            //匹配业务类型策略
            OmsBusinessTypeDistinguishService.BusinessTypeResult businessTypeResult = omsBusinessTypeDistinguishService.querySapBusinessType("", platmform, ocBOrder.getGwSourceGroup(), ocBOrderItems, user);
            if (businessTypeResult == null) {
                throw new NDSException("SAP业务类型匹配失败！");
            }
            returnOrder.setBusinessTypeId(businessTypeResult.getId());
            returnOrder.setBusinessTypeCode(businessTypeResult.getCode());
            returnOrder.setBusinessTypeName(businessTypeResult.getName());
        }else {
            StCBusinessType stCBusinessType = omsRefundOrderService.queryReturnOrderType(ocBOrder);
            returnOrder.setBusinessTypeId(stCBusinessType.getId());
            returnOrder.setBusinessTypeCode(stCBusinessType.getEcode());
            returnOrder.setBusinessTypeName(stCBusinessType.getEname());
        }
        returnOrder.setOwnerid(user.getId().longValue());
        returnOrder.setOwnername(user.getName());
        returnOrder.setOwnerename(user.getEname());
        returnOrder.setCreationdate(new Date());
        returnOrder.setModifierid(user.getId().longValue());
        returnOrder.setModifiername(user.getName());
        returnOrder.setModifierename(user.getEname());
        returnOrder.setModifieddate(new Date());
        returnOrder.setAdClientId(user.getClientId() + 0L);
        returnOrder.setAdOrgId(user.getOrgId() + 0L);
        returnOrder.setIsWrongReceive(IsWrongReceive.NO.val());
        returnOrder.setPlatformRefundStatus(OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_INIT);
        if (returnOrder.getIsBack() != null
                && returnOrder.getIsBack() == 1
                && StringUtils.isNotEmpty(returnOrder.getCpCLogisticsEcode())
                && returnOrder.getCpCLogisticsEcode().equals(OcCommonConstant.DNKD)
                && StringUtils.isNotEmpty(returnOrder.getLogisticsCode())
                && !returnOrder.getLogisticsCode().contains("T")) {
            returnOrder.setLogisticsCode("T" + returnOrder.getLogisticsCode());
        }
        return returnOrder;
    }

    /**
     * 新增退货明细数据
     */
    public OcBReturnOrderRefundBatchResult getRefundOrder(List<OcBOrderItem> ocBOrderItems, List<OcBOrderItem> itemsList,
                                                           Long returnId, User user) {
        String allSku = "";
        //退单总金额
        BigDecimal totAmt = BigDecimal.ZERO;
        //退单总数量
        BigDecimal addQty = BigDecimal.ZERO;
        //组装退货单明细数据
        List<OcBReturnOrderRefund> listRefund = new ArrayList<>();
        for (OcBOrderItem orderItem : ocBOrderItems) {
            //验证订单数量是否大于已退数量
            if (null == orderItem.getQtyReturnApply()) {
                orderItem.setQtyReturnApply(BigDecimal.ZERO);
            }
            BigDecimal returnQty = orderItem.getQty().subtract(orderItem.getQtyReturnApply());
            if (returnQty.compareTo(BigDecimal.ZERO) < 1) {
                continue;
            }

            // 验证订单明细的退款状态不等于‘退款成功’
            int itemRefundStatus = orderItem.getRefundStatus() == null ? 0 : orderItem.getRefundStatus();
            if (OcOrderRefundStatusEnum.SUCCESS.getVal() == itemRefundStatus) {
                continue;
            }
            //累计退货总数量
            addQty = addQty.add(returnQty);

            OcBReturnOrderRefund refund = new OcBReturnOrderRefund();
            Long reId = ModelUtil.getSequence("oc_b_return_order_refund");
            refund.setOcBReturnOrderId(returnId);
            refund.setId(reId);
            refund.setTid(orderItem.getTid());
            refund.setOwnerid(user.getId().longValue());
            refund.setOwnername(user.getName());
            refund.setOwnerename(user.getEname());
            refund.setCreationdate(new Date());
            refund.setAdClientId((long) user.getClientId());
            refund.setAdOrgId((long) user.getOrgId());
            refund.setModifierid(user.getId().longValue());
            refund.setModifiername(user.getName());
            refund.setModifierename(user.getEname());
            refund.setModifieddate(new Date());
            refund.setOcBOrderId(orderItem.getOcBOrderId());
            refund.setOcBOrderItemId(orderItem.getId());

            BigDecimal realAmt = orderItem.getRealAmt();
            BigDecimal qty = orderItem.getQty();
            BigDecimal sgAmt = BigDecimal.ZERO;

            if (null != realAmt && null != qty && qty.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                sgAmt = realAmt.divide(qty, OcBOrderConst.DECIMAL_QTY_FOUR, BigDecimal.ROUND_HALF_UP);
                refund.setAmtRefundSingle(sgAmt);//单件退货金额
            }

            BigDecimal totPrice = orderItem.getTotPriceSettle();
            BigDecimal settle = orderItem.getPriceSettle();
            refund.setPriceList(orderItem.getPriceTag());//吊牌价
            refund.setPriceSettle(BigDecimal.ZERO);//算单价
            refund.setAmtSettleTot(BigDecimal.ZERO);//结算金额
            //结算单价
            if (null == settle || settle.compareTo(BigDecimal.ZERO) < OcBOrderConst.IS_STATUS_IY) {
                if (sgAmt.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                    refund.setPriceSettle(sgAmt);
                }
            }
            //结算总额,如果没值,则启用成交价格
            if (null == totPrice || totPrice.compareTo(BigDecimal.ZERO) < OcBOrderConst.IS_STATUS_IY) {
                if (realAmt != null && realAmt.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                    refund.setAmtSettleTot(realAmt);
                }
            }

            refund.setProductMark("1");//商品标记 默认1
            refund.setBarcode(orderItem.getBarcode());//国标码
            // 更改为原单明细ooid
            refund.setOid(orderItem.getOoid());//子订单id 原单明细id
            refund.setSkuSpec(orderItem.getSkuSpec());//规格
            refund.setPsCSkuId(orderItem.getPsCSkuId());//skuid
            refund.setPsCSkuEcode(orderItem.getPsCSkuEcode());//SKU编码
            refund.setPsCProId(orderItem.getPsCProId());//商品ID
            refund.setPsCProEcode(orderItem.getPsCProEcode());//商品编码
            refund.setPsCProEname(orderItem.getPsCProEname());//商品名称
            refund.setPrice(orderItem.getPriceTag());//商品单价
            refund.setPsCSizeId(orderItem.getPsCSizeId());//尺寸id
            refund.setPsCSizeEcode(orderItem.getPsCSizeEcode());//尺寸编码
            refund.setPsCSizeEname(orderItem.getPsCSizeEname());//尺寸名称
            refund.setPsCClrId(orderItem.getPsCClrId());//颜色id
            refund.setPsCClrEcode(orderItem.getPsCClrEcode());//颜色编码
            refund.setPsCClrEname(orderItem.getPsCClrEname());//颜色名称
            refund.setIsReturn(0);//退、换货标识  默认0 退
            refund.setQtyIn(BigDecimal.ZERO);//入库数量
            refund.setQtyRefund(returnQty);//申请数量
            refund.setAmtRefund(refund.getQtyRefund().multiply(refund.getAmtRefundSingle()));//单件退货金额*申请数量
            refund.setQtyCanRefund(returnQty);//可退数量
            refund.setSex(orderItem.getSex());//性别
            //是否AG退款，3不传
            refund.setIsToAg(AGStatusEnum.NOT.getVal());
            totAmt = totAmt.add(refund.getAmtRefund());//累加退换货金额
            allSku = allSku + refund.getPsCSkuEcode() + "(" + refund.getQtyRefund().toString() + "),";
            listRefund.add(refund);

            //修改原单的已退数量
            BigDecimal qtyHasReturn = orderItem.getQtyHasReturn() == null ? BigDecimal.ZERO : orderItem.getQtyHasReturn();
            BigDecimal retiredQty = qtyHasReturn.add(refund.getQtyRefund());
            OcBOrderItem item = new OcBOrderItem();
            item.setId(orderItem.getId());
            item.setOcBOrderId(orderItem.getOcBOrderId());
            item.setQtyHasReturn(retiredQty);
            BigDecimal qtyReturnApply = orderItem.getQtyReturnApply() == null ? BigDecimal.ZERO : orderItem.getQtyReturnApply();
            qtyReturnApply = qtyReturnApply.add(refund.getQtyRefund());
            item.setQtyReturnApply(qtyReturnApply);
            itemsList.add(item);
        }
        OcBReturnOrderRefundBatchResult result = new OcBReturnOrderRefundBatchResult();
        result.setAddQty(addQty);
        result.setAllSku(allSku);
        result.setTotamt(totAmt);
        result.setListRefund(listRefund);
        return result;
    }

    /**
     * 新增退单日志表
     */
    public OcBReturnOrderLog genReturnLog(long returnid, User user, String logMessage) {
        Long logId = ModelUtil.getSequence("oc_b_return_order_log");
        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
        ocBReturnOrderLog.setId(logId);
        ocBReturnOrderLog.setLogType("新增退货单");
        ocBReturnOrderLog.setLogMessage(logMessage);
        ocBReturnOrderLog.setUserName(user.getName());
        ocBReturnOrderLog.setOcBReturnOrderId(returnid);
        ocBReturnOrderLog.setIpAddress(user.getLastloginip());
        ocBReturnOrderLog.setAdClientId(user.getClientId() + 0L);
        ocBReturnOrderLog.setAdOrgId(user.getOrgId() + 0L);
        ocBReturnOrderLog.setOwnerid(user.getId() + 0L);
        ocBReturnOrderLog.setOwnername(user.getName());
        ocBReturnOrderLog.setOwnerename(user.getEname());
        ocBReturnOrderLog.setCreationdate(new Date());
        return ocBReturnOrderLog;
    }

    /**
     * 检查退货明细是否有组合商品
     */
    private void checkReEcode(OcBOrderItem item) {
        String ecode = item.getPsCSkuEcode();
        Map map = psRpcService.querySku(ecode);
        List<HashMap> hashMapList = (List<HashMap>) map.get("data");
        if (!hashMapList.isEmpty()) {
            String isGroup = (String) hashMapList.get(0).get("IS_GROUP");
            if ("Y".equals(isGroup)) {
                throw new NDSException("此退换货单含有组合商品明细，请检查后修改为实际出入库条码后重新操作");
            }
        }
    }

    /**
     * 判断新增或者更新的这个是不是wms管控仓
     */
    private OcBReturnOrder checkWmsCtrHouse(OcBReturnOrder returnOrder) {
        /**
         * 如果入库实体仓是WMS管控仓，则将reserve_bigint03此字段赋值为1，如果不是WMS管控仓或者入库实体仓为空，则此字段赋值为0.
         */
        //实体仓id
        Long cpCPhyWarehouseInId = returnOrder.getCpCPhyWarehouseInId();
        if (cpCPhyWarehouseInId == null) {
            returnOrder.setIsNeedToWms(0L);
            return returnOrder;
        }
        // merge return is ext , find is cp ->  cp
        CpCPhyWarehouse wareHouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseInId);
        //判断这个仓是不是wms 管控仓
        if (wareHouse != null) {
            //不是wms 管控仓或者为空，是否传WMS字段（reserve_bigint03）则此字段赋值为0.
            if (wareHouse.getWmsControlWarehouse() == null || WmsControlWarehouse.NOT_CONTROL.equals(wareHouse.getWmsControlWarehouse())) {
                returnOrder.setIsNeedToWms(0L);
            } else {
                //是wms 管控仓，是否传WMS字段（reserve_bigint03）则此字段赋值为0.
                returnOrder.setIsNeedToWms(1L);
            }
        } else {
            throw new NDSException("查询不到实体仓");
        }
        return returnOrder;
    }


}
