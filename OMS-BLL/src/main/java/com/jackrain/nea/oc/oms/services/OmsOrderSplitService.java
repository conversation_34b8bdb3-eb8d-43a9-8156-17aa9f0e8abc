package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.SpiltOrderParam;
import com.jackrain.nea.oc.oms.model.result.QueryOrderItemGroupByResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryProcessor;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OmsOrderSplitReasonUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.util.OrderTagUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 部分虚拟订单拆分服务
 *
 * @author: heliu
 * @since: 2019/3/8
 * create at : 2019/3/8 14:23
 */
@Component
@Slf4j
public class OmsOrderSplitService {

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsOrderCancellationService omsOrderCancellationService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OmsOrderDifferenPriceService omsOrderDifferenPriceService;

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    @Autowired
    private OmsOrderDistributeWarehouseService omsWarehousRuleService;

    @Autowired
    private OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;

    @Autowired
    private OmsToSapTaskService omsToSapTaskService;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Autowired
    private OmsOrderJdSplitService omsOrderJdSplitService;

    @Autowired
    private OrderDeliveryProcessor orderDeliveryProcessor;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OrderAmountUtil orderAmountUtil;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    OmsOrderSplitReasonUtil omsOrderSplitReasonUtil;

    /**
     * 虚拟订单拆分服务
     *
     * @param originalOrderRelation 原始订单对象
     * @return boolean
     */
    public Map<Set<Long>, SpiltOrderParam> startSplitVirtualOrder(OcBOrderRelation originalOrderRelation, User user) {
        Long orderId = originalOrderRelation.getOrderId();

        List<OcBOrderItem> oriOcBOrderItemList = this.orderItemMapper.selectOrderItemListOccupy(orderId);
        // 查找出虚拟的skuCode
        List<String> virtualSkuEcodeList = omsOrderDifferenPriceService.selectVirtualSkuEcodeList(originalOrderRelation);
        if (CollectionUtils.isEmpty(virtualSkuEcodeList)) {
            return null;
        }
        // 正常订单明细
        List<OcBOrderItem> normalProdOrderItemList = oriOcBOrderItemList.stream()
                .filter(item -> !virtualSkuEcodeList.contains(item.getPsCSkuEcode())).collect(Collectors.toList());
        // 虚拟订单明细
        List<OcBOrderItem> virtualProdOrderItemList = oriOcBOrderItemList.stream()
                .filter(item -> virtualSkuEcodeList.contains(item.getPsCSkuEcode())).collect(Collectors.toList());

        Set<Long> normalOrderItemIds = normalProdOrderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toSet());
        Set<Long> virtualOrderItemIds = virtualProdOrderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toSet());
        Map<Set<Long>, SpiltOrderParam> paramMap = new HashMap<>();
        SpiltOrderParam virtualParam = new SpiltOrderParam();
        virtualParam.setFictitiousOrder(true);
        SpiltOrderParam normalParam = new SpiltOrderParam();
        normalParam.setFictitiousOrder(false);
        paramMap.put(normalOrderItemIds, normalParam);
        paramMap.put(virtualOrderItemIds, virtualParam);
        return paramMap;
    }

    /**
     * 构造正常订单对象
     *
     * @param sourceOrderInfo          订单对象
     * @param orderId                  新订单ID
     * @param suffixInfo               订单补充信息
     * @param virtualProdOrderItemList 订单明细
     * @return OcBOrder
     */
    private OcBOrder buildNormalOrderInfo(OcBOrder sourceOrderInfo, Long orderId, String suffixInfo,
                                          List<OcBOrderItem> virtualProdOrderItemList) {
        OcBOrder ocBOrder = new OcBOrder();
        //复制其他属性
        BeanUtils.copyProperties(sourceOrderInfo, ocBOrder);
        //设置ID
        ocBOrder.setId(orderId);
        //订单编号
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        //是否拆分原单
        ocBOrder.setIsSplit(1);
        //订单拆单处理状态
        ocBOrder.setSplitStatus(0);
        //拆单原因
        ocBOrder.setSplitReason(SplitReason.SPLIT_VIRTUAL);
        //拆分原订单号
        ocBOrder.setSplitOrderId(sourceOrderInfo.getId());
        //平台单号
        ocBOrder.setSourceCode(sourceOrderInfo.getSourceCode());
        //订单补充信息
        ocBOrder.setSuffixInfo(suffixInfo);
        //设置创建人
        ocBOrder.setOwnername(SystemUserResource.ROOT_USER_NAME);
        //创建时间
        ocBOrder.setCreationdate(new Date());
        //修改人
        ocBOrder.setModifierename(SystemUserResource.ROOT_USER_NAME);
        //修改时间
        ocBOrder.setModifieddate(new Date());
        // 是否虚拟定订单
        ocBOrder.setIsInvented(0);
        ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
        ocBOrder.setPriceLabel("N");
        //系统备注
        //ocBOrder.setSysremark("订单OrderID" + orderId + "为正常复制订单");
        int giftItemNum = 0;
        //只有明细有赠品,则主表是否有赠品为1
        for (OcBOrderItem orderItem : virtualProdOrderItemList) {
            //发货状态置为已发货
            if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(orderItem.getIsGift())) {
                giftItemNum++;
            }
        }
        if (giftItemNum > 0) {
            ocBOrder.setIsHasgift(1);
        }
        // 重新计算拆分后订单主表金额
        OcBOrderRelation newNormalRelation = new OcBOrderRelation();
        newNormalRelation.setOrderInfo(ocBOrder);
        newNormalRelation.setOrderItemList(virtualProdOrderItemList);
        return orderAmountUtil.recountOrderAmount(newNormalRelation);
    }

    /**
     * 全部为虚拟订单
     *
     * @param orderInfo          原始订单对象
     * @param unNorMalId         虚拟订单ID
     * @param twoSuffixInfo      订单补充信息
     * @param defaultStoreId     缺货默认仓库
     * @param noMalorderItemList 订单明细
     * @return OcBOrder
     */
    private OcBOrder buildVirtualOrderInfo(OcBOrder orderInfo, Long unNorMalId,
                                           String twoSuffixInfo, Long defaultStoreId,
                                           List<OcBOrderItem> noMalorderItemList) {
        OcBOrder ocBOrder = new OcBOrder();
        //复制其他属性
        BeanUtils.copyProperties(orderInfo, ocBOrder);
        //设置ID
        ocBOrder.setId(unNorMalId);
        //订单编号
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        //是否拆分原单
        ocBOrder.setIsSplit(1);
        //是否虚拟订单
        ocBOrder.setIsInvented(1);
        //拆单原因
        ocBOrder.setSplitReason(SplitReason.SPLIT_VIRTUAL);
        //拆分原订单号
        ocBOrder.setSplitOrderId(orderInfo.getId());
        //平台单号
        ocBOrder.setSourceCode(orderInfo.getSourceCode());
        //订单补充信息
        ocBOrder.setSuffixInfo(twoSuffixInfo);
        //缺货状态
        ocBOrder.setOrderStatus(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
        //设置创建人
        ocBOrder.setOwnername(SystemUserResource.ROOT_USER_NAME);
        //创建时间
        ocBOrder.setCreationdate(new Date());
        //修改人
        ocBOrder.setModifierename(SystemUserResource.ROOT_USER_NAME);
        //修改时间
        ocBOrder.setModifieddate(new Date());
        //系统备注
        //ocBOrder.setSysremark("订单OrderID" + unNorMalId + "为虚拟订单，系统自动出库");
        //缺货默认仓库
        ocBOrder.setCpCPhyWarehouseId(defaultStoreId);
        if (defaultStoreId != null) {
            CpCPhyWarehouse cpCPhyWarehouse = omsWarehousRuleService.queryByWarehouseId(defaultStoreId);
            if (cpCPhyWarehouse == null) {
                OcBOrder ocBOrderDto = new OcBOrder();
                String operateMessage = "订单OrderId" + orderInfo.getId() + "的订单未维护店铺缺货默认仓库";
                ocBOrderDto.setId(orderInfo.getId());
                ocBOrderDto.setSysremark(SplitMessageUtil.splitMesssage(operateMessage));
                omsOrderService.updateOrderInfo(ocBOrderDto);
                throw new NDSException(Resources.getMessage("订单OrderId" + orderInfo.getId() + "的订单未维护店铺缺货默认仓库！"));
            }

            //设置ename
            ocBOrder.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
            //设置ecode
            ocBOrder.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
            //如果实体仓是o2o仓库，对订单进行打标
            if (org.apache.commons.lang3.StringUtils.equals(cpCPhyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
                ocBOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
            } else {
                ocBOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
            }

           /* List<Long> storeList = cpRpcService.queryStoreList(defaultStoreId);
            if (CollectionUtils.isNotEmpty(storeList)) {
                CpStore cp = cpRpcService.selectCpCStoreById(storeList.get(0));
                if (cp != null) {
                    ocBOrder.setCpCStoreId(cp.getId());
                    ocBOrder.setCpCStoreEcode(cp.getEcode());
                    ocBOrder.setCpCStoreEname(cp.getEname());
                }
            }*/
        }
        //下单时间
        ocBOrder.setScanTime(new Date());
        //订单类型设置为虚拟，标签为价
        ocBOrder.setOrderType(OrderTypeEnum.DIFFPRICE.getVal());
        //待传结算标志 设置为1（待传）
//        ocBOrder.setToSettleStatus(1L);
        ocBOrder.setPriceLabel(IsActiveEnum.Y.getKey());
        ocBOrder.setIsInvented(1);
        //只有明细有赠品,则主表是否有赠品为1
        int i = 0;
        for (OcBOrderItem orderItem : noMalorderItemList) {
            //发货状态置为已发货
            orderItem.setIsSendout(0);
            if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(orderItem.getIsGift())) {
                i++;
            }
        }
        if (i > 0) {
            ocBOrder.setIsHasgift(1);
        }
        //自定义拆单赋值
        omsOrderSplitReasonUtil.matchReason(ocBOrder);
        // 重新计算拆分后订单主表金额
        OcBOrderRelation newVirtualRelation = new OcBOrderRelation();
        newVirtualRelation.setOrderInfo(ocBOrder);
        newVirtualRelation.setOrderItemList(noMalorderItemList);
        return orderAmountUtil.recountOrderAmount(newVirtualRelation);
    }

    /**
     * 构造虚拟订单明细
     *
     * @param unNorMalId         订单Id
     * @param differPriceSkuList 虚拟明细
     * @return List<OcBOrderItem>
     */
    private List<OcBOrderItem> bulidPriceOcBOrderItem(Long unNorMalId, List<OcBOrderItem> differPriceSkuList) {

        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
        for (OcBOrderItem cBOrderItem : differPriceSkuList) {
            OcBOrderItem orderItem = new OcBOrderItem();
            BeanUtils.copyProperties(cBOrderItem, orderItem);
            //重新生成Id
            orderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
            //设置订单Id
            orderItem.setOcBOrderId(unNorMalId);
            //是否已发货
            orderItem.setIsSendout(0);
            //修改人
            orderItem.setModifierename(SystemUserResource.ROOT_USER_NAME);
            //修改时间
            orderItem.setModifieddate(new Date());
            ocBOrderItemList.add(orderItem);
        }
        return ocBOrderItemList;
    }

    /**
     * 构造全渠道明细表集合
     *
     * @param orderId       订单Id
     * @param orderItemList 订单明细
     * @return List<OcBOrderItem>
     */
    public List<OcBOrderItem> bulidOcBOrderItem(Long orderId, List<OcBOrderItem> orderItemList) {

        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
        for (OcBOrderItem cBOrderItem : orderItemList) {
            OcBOrderItem orderItem = new OcBOrderItem();
            BeanUtils.copyProperties(cBOrderItem, orderItem);
            //重新生成Id
            orderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
            //设置订单Id
            orderItem.setOcBOrderId(orderId);
            //修改人
            orderItem.setModifierename(SystemUserResource.ROOT_USER_NAME);
            //修改时间
            orderItem.setModifieddate(new Date());
            //复制其他属性
            log.debug("_orderItem" + orderItem);
            ocBOrderItemList.add(orderItem);
        }
        return ocBOrderItemList;
    }

    /**
     * 复制订单对象
     *
     * @param orderId         订单Id
     * @param isCopyOrderItem 是否复制明细对象  true复制,false复制
     * @return OcBOrderRelation
     */
    public OcBOrderRelation getOcBOrderRelation(Long orderId, boolean isCopyOrderItem) {

        //未退款明细
        List<OcBOrderItem> orderUnRefundItemList = omsOrderItemService
                .selectUnSuccessRefund(orderId);
        Long orderID = ModelUtil.getSequence("oc_b_order");
        OcBOrderRelation newOcBOrderRelation = new OcBOrderRelation();
        //查询原单对象
        OcBOrder orderInfo = omsOrderService.selectOrderInfo(orderId);
        OcBOrder newOcBOrder = new OcBOrder();
        //复制其他属性
        BeanUtils.copyProperties(orderInfo, newOcBOrder);
        //设置orderID
        newOcBOrder.setId(orderID);
        //订单编号
        newOcBOrder.setBillNo(sequenceUtil.buildBillNo());
        //订单状态--缺货
        newOcBOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        //设置创建人
        newOcBOrder.setOwnername(SystemUserResource.ROOT_USER_NAME);
        //创建时间
        newOcBOrder.setCreationdate(new Date());
        //修改人
        newOcBOrder.setModifierename(SystemUserResource.ROOT_USER_NAME);
        //修改时间
        newOcBOrder.setModifieddate(new Date());
        //系统备注
        //newOcBOrder.setSysremark("订单OrderID" + orderID + "为复制订单");
        //占单状态
        newOcBOrder.setOccupyStatus(0);
        //审核时间
        newOcBOrder.setAuditTime(null);
        //是否拦截
        newOcBOrder.setIsInterecept(0);
        //是否退款中
        newOcBOrder.setIsInreturning(0);
        //是否给物流
        // newOcBOrder.setIsGiveLogistic(0);
        //WMS撤回状态
        newOcBOrder.setWmsCancelStatus(0);
        //退货状态
        newOcBOrder.setReturnStatus(0);
        //退款审核状态AG
        newOcBOrder.setRefundConfirmStatus(0);
        //自动审核状态
        newOcBOrder.setAutoAuditStatus(0);
        //缺货标记
//        newOcBOrder.setIsLackstock(OcBOrderConst.IS_STATUS_IY);
        //配货时间
        newOcBOrder.setDistributionTime(null);
        //是否催发货
        newOcBOrder.setIsOutUrgency(0);
        //是否有工单
        newOcBOrder.setIsHasTicket(0);
        //是否插入核销流水
        // newOcBOrder.setIsWriteoff(0);
        //装入原始订单对象
        newOcBOrderRelation.setOrderInfo(newOcBOrder);
        //不复制明细对象
        if (!isCopyOrderItem) {
            //保存订单对象
            omsOrderService.saveOrderInfo(newOcBOrder);
            return newOcBOrderRelation;
        }
        //保存订单对象 and 保存订单明细
        omsOrderService.saveOrderInfo(newOcBOrder);
        List<OcBOrderItem> ocBOrderItemList = bulidOcBOrderSaveItem(orderUnRefundItemList, newOcBOrder.getId());
        newOcBOrderRelation.setOrderItemList(ocBOrderItemList);
        return newOcBOrderRelation;
    }

    /**
     * 复制完成之后保存到数据表中
     *
     * @param orderItemList 订单明细
     * @param newOrderId    新订单Id
     * @return List<OcBOrderItem>
     */
    private List<OcBOrderItem> bulidOcBOrderSaveItem(List<OcBOrderItem> orderItemList, Long newOrderId) {

        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
        for (OcBOrderItem cBOrderItem : orderItemList) {
            OcBOrderItem orderItem = new OcBOrderItem();
            BeanUtils.copyProperties(cBOrderItem, orderItem);
            //重新生成Id
            orderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
            //设置订单Id
            orderItem.setOcBOrderId(newOrderId);
            //修改人
            orderItem.setModifierename(SystemUserResource.ROOT_USER_NAME);
            //修改时间
            orderItem.setModifieddate(new Date());
            //实缺标记
            orderItem.setIsLackstock(0);
            //是否已经占过库存
            orderItem.setIsAllocatestock(0);
            //买家是否评价
            orderItem.setIsBuyerRate(0);
            //是否已发货
            orderItem.setIsSendout(0);
            //发货失败次数
            orderItem.setOuterrcount(0);
            //复制其他属性
            //保存明细
            omsOrderItemService.saveOcBOrderItem(orderItem, newOrderId);
            ocBOrderItemList.add(orderItem);
        }
        return ocBOrderItemList;
    }

    /**
     * 重新计算明细数据
     *
     * @param orderDto      订单对象
     * @param orderItemList 明细数据
     * @param isflag        是否调用分物流服务
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean reAmount(OcBOrder orderDto, List<OcBOrderItem> orderItemList, boolean isflag) {

        try {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //重新计算商品数量
            BigDecimal normalTotal = BigDecimal.ZERO;
            //重新计算商品总额
            BigDecimal priceListTotal = BigDecimal.ZERO;
            //指定商品优惠金额
            BigDecimal amtDiscountTotal = BigDecimal.ZERO;
            //平摊金额之和
            BigDecimal orderSplitAmtTotal = BigDecimal.ZERO;
            //代销结算金额
            BigDecimal consignmentTotal = BigDecimal.ZERO;
            //应收金额求和
            BigDecimal receivableTotal = BigDecimal.ZERO;
            //调整金额之和
            BigDecimal adjustTotal = BigDecimal.ZERO;
            // SKU条数（排除组合商品的条数）
            int orderItemNum = 0;
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                if (ocBOrderItem.getProType() != SkuType.NO_SPLIT_COMBINE) {
                    orderItemNum++;
                    //商品数量计算
                    normalTotal = normalTotal.add(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty());
                    //商品总额计算
                    priceListTotal = priceListTotal.add((ocBOrderItem.getPrice() == null ? BigDecimal.ZERO : ocBOrderItem.getPrice()).multiply(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty()));
                    //优惠金额计算
                    amtDiscountTotal = amtDiscountTotal.add(ocBOrderItem.getAmtDiscount() == null ? BigDecimal.ZERO : ocBOrderItem.getAmtDiscount());
                    //平摊金额求和
                    orderSplitAmtTotal = orderSplitAmtTotal.add(ocBOrderItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getOrderSplitAmt());
                    //代销结算金额求和
                    consignmentTotal = consignmentTotal.add((ocBOrderItem.getDistributionPrice() == null ? BigDecimal.ZERO : ocBOrderItem.getDistributionPrice()).multiply(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty()));
                    //应收金额求和
                    receivableTotal = receivableTotal.add((ocBOrderItem.getPriceList() == null ? BigDecimal.ZERO : ocBOrderItem.getPriceList()).multiply(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty()));
                    //调整金额之和
                    adjustTotal = adjustTotal.add(ocBOrderItem.getAdjustAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getAdjustAmt());
                }
            }
            //订单总额计算
            BigDecimal orderAmtTotal = priceListTotal.add(orderDto.getShipAmt() == null ? BigDecimal.ZERO : orderDto.getShipAmt()).add(adjustTotal).add(orderDto.getServiceAmt() == null
                    ? BigDecimal.ZERO : orderDto.getServiceAmt()).subtract(amtDiscountTotal).subtract(orderSplitAmtTotal);
            //更新正常订单表数据
            OcBOrder oneOcbOrder = new OcBOrder();
            //将原单对象都复制给新临时对象去调分物流
            oneOcbOrder.setId(orderDto.getId());
            //	【商品数量】：取明细中所有数量的合计；
            oneOcbOrder.setQtyAll(normalTotal);
            oneOcbOrder.setSkuKindQty(new BigDecimal(orderItemNum));
            orderDto.setQtyAll(normalTotal);
            orderDto.setSkuKindQty(new BigDecimal(orderItemNum));
            //	【商品总额】：取明细中所有吊牌金额的合计；
            oneOcbOrder.setProductAmt(priceListTotal);
            orderDto.setProductAmt(priceListTotal);
            //	【商品优惠金额】：取明细中指定商品优惠金额
            oneOcbOrder.setProductDiscountAmt(amtDiscountTotal);
            orderDto.setProductDiscountAmt(amtDiscountTotal);
            //	【订单优惠金额】：整单优惠金额
            oneOcbOrder.setOrderDiscountAmt(orderSplitAmtTotal);
            orderDto.setOrderDiscountAmt(orderSplitAmtTotal);
            //	【订单总额】：商品总额+物流费用+调整金额+服务费-商品优惠金额-订单优惠金额
            oneOcbOrder.setConsignAmt(consignmentTotal);
            orderDto.setConsignAmt(consignmentTotal);
            //	【代销结算金额】：代销结算价*数量
            oneOcbOrder.setAmtReceive(receivableTotal);
            orderDto.setAmtReceive(receivableTotal);
            //	【应收金额】：商品标准价*数量
            oneOcbOrder.setOrderAmt(orderAmtTotal);
            orderDto.setOrderAmt(orderAmtTotal);
            //	【已支付金额】（已收金额）：等于计算后的订单总额
            oneOcbOrder.setReceivedAmt(orderAmtTotal);
            orderDto.setReceivedAmt(orderAmtTotal);
            //	【调整金额之和】：取明细中所有调整金额的合计；
            oneOcbOrder.setAdjustAmt(adjustTotal);
            orderDto.setAdjustAmt(adjustTotal);
            if (isflag) {
                //判断新订单是否有赠品,若有主表加赠品标识
                int i = 0;
                for (OcBOrderItem orderItem : orderItemList) {
                    if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(orderItem.getIsGift())) {
                        i++;
                    }
                }
                //有赠品
                if (i > 0) {
                    oneOcbOrder.setIsHasgift(1);
                    orderDto.setIsHasgift(1);
                } else {
                    //没赠品
                    oneOcbOrder.setIsHasgift(0);
                    orderDto.setIsHasgift(0);
                }
                //然后对于新订单执行分配物流服务
                OcBOrderRelation orderInfo = new OcBOrderRelation();
                orderInfo.setOrderInfo(orderDto);
                orderInfo.setOrderItemList(orderItemList);
               /* Long cpClogisticsId = omsOrderDistributeLogisticsService.orderDistributeLogistics(orderInfo, SystemUserResource.getRootUser());
                //然后再去查询物流公司合法性校验
                CpCLogistics cpCLogistics = omsOrderLogisticsService.queryLogisticsById(cpClogisticsId);
                */
//                CpCLogistics cpCLogistics = omsOrderDistributeLogisticsService.distributeLogistics(orderInfo);
//                if (cpCLogistics != null) {
//                    String message = "订单OrderId" + oneOcbOrder.getId() + "拆分新订单调用分物流服务,订单分配到物流公司.返回物流公司Id[" + cpCLogistics.getId() + "][" + cpCLogistics.getEname() + "],操作时间" + df.format(new Date());
//                    log.debug(message);
//                    oneOcbOrder.setCpCLogisticsId(cpCLogistics.getId());
//                    oneOcbOrder.setCpCLogisticsEname(cpCLogistics.getEname());
//                    oneOcbOrder.setCpCLogisticsEcode(cpCLogistics.getEcode());
//                    //插入日志
//                    omsOrderLogService.addUserOrderLog(oneOcbOrder.getId(), oneOcbOrder.getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), message, null, null, SystemUserResource.getRootUser());
//                } else {
//                    String errorMessage = "订单OrderId" + oneOcbOrder.getId() + "拆分新订单调用分物流服务未匹配到有效物流公司,操作时间" + df.format(new Date());
//                    log.debug(errorMessage);
//                    //为了清除原有的物流公司
//                    oneOcbOrder.setSysremark(errorMessage);
//                    oneOcbOrder.setCpCLogisticsId(0L);
//                    oneOcbOrder.setCpCLogisticsEname("");
//                    oneOcbOrder.setCpCLogisticsEcode("");
//                    //插入日志
//                    omsOrderLogService.addUserOrderLog(oneOcbOrder.getId(), oneOcbOrder.getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), errorMessage, null, null, SystemUserResource.getRootUser());
//                }
            }
            return omsOrderService.updateOrderInfo(oneOcbOrder);
        } catch (Exception ex) {
            log.error(LogUtil.format("执行主表计算金额服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            throw new NDSException(Resources.getMessage("订单OrderId" + orderDto.getId() + "执行主表计算金额服务异常！" + ex.getMessage()));
        }
    }

    /**
     * 均匀计算明细行金额
     *
     * @param ocBorderDto                原始订单对象
     * @param secondOrderderRelationList 新的订单集合对象
     */
    @Transactional(rollbackFor = Exception.class)
    public void reAmountOrderItem(OcBOrder ocBorderDto, List<OcBOrderRelation> secondOrderderRelationList) {

        try {
            //查询原始订单的明细ooid
            List<String> ooidList = omsOrderItemService.queryOoidList(ocBorderDto.getId());
            //取出新单的所有订单Id
            List<Long> orderIdList = new ArrayList<>();
            for (OcBOrderRelation orderRelation : secondOrderderRelationList) {
                orderIdList.add(orderRelation.getOrderInfo().getId());
            }
            //配送费用
            BigDecimal shipAmt = (ocBorderDto.getShipAmt() == null ? BigDecimal.ZERO : ocBorderDto.getShipAmt());
            //服务费
            BigDecimal serviceAmt = (ocBorderDto.getServiceAmt() == null ? BigDecimal.ZERO : ocBorderDto.getServiceAmt());
            //取出新单中的最大Id
            Long newOrderId = Collections.max(orderIdList);
            OcBOrder newOcbOrder = new OcBOrder();
            newOcbOrder.setId(newOrderId);
            //配送费用复制
            newOcbOrder.setShipAmt(shipAmt);
            //服务费复制
            newOcbOrder.setServiceAmt(serviceAmt);
            //更新这两个字段
            omsOrderService.updateOrderInfo(newOcbOrder);
            //存放差值
            BigDecimal lastAdJustAmtAmout = BigDecimal.ZERO;
            BigDecimal lastOrderDiscountAmt = BigDecimal.ZERO;
            BigDecimal lastDistributionPriceAmout = BigDecimal.ZERO;
            BigDecimal lastOrderSplitAmout = BigDecimal.ZERO;
            //根据新单的所有新订单id和原始sku去合计新的sku分组数据
            List<QueryOrderItemGroupByResult> selectUnSuccessRefundGroupByItemList = omsOrderItemService.selectUnSuccessRefundGroupByItemList(orderIdList, ooidList);
            for (QueryOrderItemGroupByResult orderItemGroupByResult : selectUnSuccessRefundGroupByItemList) {
                //当原订单明细匹配上新订单的所有ooid合计时
                if (ooidList.contains(orderItemGroupByResult.getOoid())) {
                    //查询原单的sku的明细金额
                    QueryOrderItemGroupByResult queryOrderItemGroupByResult = omsOrderItemService.queryOrderItemGroupByResult(ocBorderDto.getId(), orderItemGroupByResult.getOoid());
                    //根据skuId和新单的orderId随机查一条明细处理
                    List<OcBOrderItem> orderItemList = omsOrderItemService.queryOrderItemBySkuIdList(orderIdList, orderItemGroupByResult.getOoid());
                    //随机取条明细出来
                    OcBOrderItem ocBOrderItem = orderItemList.get(0);
                    //再次查询新单
                    OcBOrder ocBOrder = omsOrderService.selectOrderInfo(ocBOrderItem.getOcBOrderId());
                    //取出原单的调整金额之和
                    BigDecimal adjustAmt = (queryOrderItemGroupByResult.getAdjustAmtAmount() == null ? BigDecimal.ZERO : queryOrderItemGroupByResult.getAdjustAmtAmount());
                    //取出原单的优惠金额之和
                    BigDecimal productDiscountAmt = (queryOrderItemGroupByResult.getAmtDiscountAmount() == null ? BigDecimal.ZERO : queryOrderItemGroupByResult.getAmtDiscountAmount());
                    //取出原单的分销金额之和
                    BigDecimal consignAmt = (queryOrderItemGroupByResult.getDistributionPriceAmount() == null ? BigDecimal.ZERO : queryOrderItemGroupByResult.getDistributionPriceAmount());
                    //取出明细平摊金额之和
                    BigDecimal orderSplitAmount = (queryOrderItemGroupByResult.getOrderSplitAmount() == null ? BigDecimal.ZERO : queryOrderItemGroupByResult.getOrderSplitAmount());
                    //只有当明细行调整金额和原主表明细行数据对不上时
                    if (adjustAmt.compareTo(orderItemGroupByResult.getAdjustAmtAmount()) != 0) {
                        //当主表调整金额小于明细行调整金额累加金额时
                        if (adjustAmt.compareTo(orderItemGroupByResult.getAdjustAmtAmount()) < 0) {
                            lastAdJustAmtAmout = orderItemGroupByResult.getAdjustAmtAmount().subtract(adjustAmt);
                            //存放结果
                            BigDecimal result = ocBOrderItem.getAdjustAmt().subtract(lastAdJustAmtAmout);
                            ocBOrderItem.setAdjustAmt(result);
                        } else {
                            lastAdJustAmtAmout = adjustAmt.subtract(orderItemGroupByResult.getAdjustAmtAmount());
                            if (lastAdJustAmtAmout.compareTo(BigDecimal.ZERO) > 0) {
                                BigDecimal result = ocBOrderItem.getAdjustAmt().add(lastAdJustAmtAmout);
                                ocBOrderItem.setAdjustAmt(result);
                            }
                        }
                    }
                    //只有当明细商品优惠金额和原主表明细行数据对不上时
                    if (productDiscountAmt.compareTo(orderItemGroupByResult.getAmtDiscountAmount()) != 0) {
                        //当主表商品优惠金额小于明细行累加优惠金额时
                        if (productDiscountAmt.compareTo(orderItemGroupByResult.getAmtDiscountAmount()) < 0) {
                            lastOrderDiscountAmt = orderItemGroupByResult.getAmtDiscountAmount().subtract(productDiscountAmt);
                            //存放结果
                            BigDecimal result = ocBOrderItem.getAmtDiscount().subtract(lastOrderDiscountAmt);
                            ocBOrderItem.setAdjustAmt(result);
                        } else {
                            lastOrderDiscountAmt = productDiscountAmt.subtract(orderItemGroupByResult.getAmtDiscountAmount());
                            log.debug("lastAdJustAmtAmout--> " + lastOrderDiscountAmt);
                            if (lastOrderDiscountAmt.compareTo(BigDecimal.ZERO) > 0) {

                                BigDecimal result = ocBOrderItem.getAmtDiscount().add(lastOrderDiscountAmt);
                                ocBOrderItem.setAmtDiscount(result);
                            }
                        }
                    }
                    //只有当明细分销金额金额和原主表明细行数据对不上时
                    if (consignAmt.compareTo(orderItemGroupByResult.getDistributionPriceAmount()) != 0) {
                        //当主表分销金额小于明细行累加代销金额时
                        if (consignAmt.compareTo(orderItemGroupByResult.getDistributionPriceAmount()) < 0) {
                            lastDistributionPriceAmout = orderItemGroupByResult.getDistributionPriceAmount().subtract(consignAmt);
                            //存放结果
                            BigDecimal result = ocBOrderItem.getDistributionPrice().subtract(lastDistributionPriceAmout);
                            ocBOrderItem.setDistributionPrice(result);
                        } else {
                            log.debug("/*----------------------当主表金额大于明细行代销累加金额时*--------------------*/");
                            lastDistributionPriceAmout = consignAmt.subtract(orderItemGroupByResult.getDistributionPriceAmount());
                            BigDecimal result = ocBOrderItem.getDistributionPrice().add(lastDistributionPriceAmout);
                            ocBOrderItem.setDistributionPrice(result);
                        }
                    }
                    //当主表分摊金额小于明细行累加平摊金额时数据对不上时
                    if (orderSplitAmount.compareTo(orderItemGroupByResult.getOrderSplitAmount()) != 0) {
                        //当主表分摊金额小于明细行累加平摊金额时
                        if (orderSplitAmount.compareTo(orderItemGroupByResult.getOrderSplitAmount()) < 0) {
                            lastOrderSplitAmout = orderItemGroupByResult.getOrderSplitAmount().subtract(orderSplitAmount);
                            //存放结果
                            BigDecimal result = ocBOrderItem.getOrderSplitAmt().subtract(lastOrderSplitAmout);
                            ocBOrderItem.setOrderSplitAmt(result);
                        } else {
                            lastOrderSplitAmout = orderSplitAmount.subtract(orderItemGroupByResult.getOrderSplitAmount());
                            BigDecimal result = ocBOrderItem.getOrderSplitAmt().add(lastOrderSplitAmout);
                            ocBOrderItem.setOrderSplitAmt(result);
                        }
                    }
                    //成交单价
                    BigDecimal price = (ocBOrderItem.getPriceList().multiply(ocBOrderItem.getQty()).subtract(ocBOrderItem.getAmtDiscount()).add(ocBOrderItem.getAdjustAmt())).divide(ocBOrderItem.getQty(), 2, RoundingMode.HALF_EVEN).setScale(2, BigDecimal.ROUND_CEILING);
                    ocBOrderItem.setPrice(price);
                    //【成交金额】=吊牌价*数量-优惠金额+调整金额-明细整单平摊金额
                    BigDecimal realAmt = ocBOrderItem.getPriceList().multiply(ocBOrderItem.getQty()).subtract(ocBOrderItem.getAmtDiscount()).add(ocBOrderItem.getAdjustAmt()).subtract(ocBOrderItem.getOrderSplitAmt()).setScale(2, BigDecimal.ROUND_CEILING);
                    ocBOrderItem.setRealAmt(realAmt);
                    //更新当行明细数据
                    omsOrderItemService.updateOcBOrderItem(ocBOrderItem, ocBOrder.getId());
                    //重新查询明细数据
                    List<OcBOrderItem> orderUnRefundItemList = omsOrderItemService.selectUnSuccessRefund(ocBOrder.getId());
                    //然后再次更新主表金额
                    reAmount(ocBOrder, orderUnRefundItemList, false);
                    log.debug("再次执行重新计算金额成功 订单OrderId" + ocBOrder.getId());
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("执行订单均匀计算明细行金额服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            throw new NDSException(Resources.getMessage("OrderId为" + ocBorderDto.getId() + "执行订单均匀计算明细行金额服务异常！" + ex.getMessage()));
        }
    }
}