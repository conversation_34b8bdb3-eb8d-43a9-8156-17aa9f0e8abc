package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBRefundBatchMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.AdjustProperty;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatus;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.relation.ScanIncomingRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundBatch;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sg.service.SgStockAdjustmentService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-08-22
 * create at : 2019-08-22 1:51 PM
 * 扫描入库 实际逻辑处理类
 */
@Slf4j
@Component
public class ScanIncomingLogicService {

    @Autowired
    private OcBReturnStockInService refundInExtService;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBRefundBatchMapper ocBRefundBatchMapper;

    @Autowired
    private SgStockAdjustmentService sgStockAdjustmentService;

    @Autowired
    private OcBRefundInLogService logService;

    @Autowired
    private OcBRefundInProductItemMapper ocBRefundInProductItemMapper;

    @Autowired
    private OcBRefundInMapper refundInMapper;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    public void toDoScanIncoming(ScanIncomingRelation relation) {
        //进行锁单
        String lockRedisKey = BllRedisKeyResources.buildLockRefundInKey(relation.getRefundInId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                //已经锁单，先查数据库，如果In_status=2,
                OcBRefundIn refundIn = refundInMapper.selectById(relation.getRefundInId());
                if (ReturnStatus.WAREHOUSING.toInteger().equals(refundIn.getInStatus())) {
                    log.debug(this.getClass().getSimpleName() + relation.getRefundInId() + "已入库");
                    return;
                }

                BigDecimal standardQty = BigDecimal.ZERO;
                BigDecimal subStandardQty = BigDecimal.ZERO;
                //未匹配的退货入库单明细（即退换货单号字段为空的明细）
                List<OcBRefundInProductItem> unNormalList = new ArrayList<>();
                List<OcBRefundInProductItem> normalList = new ArrayList<>();
                //明细必不为空，因为已经主子表保存成功了
                for (OcBRefundInProductItem item : relation.getItemList()) {
                    Long ocBReturnOrderId = item.getOcBReturnOrderId();
                    //区分正次品，更新批次表
                    if (StringUtils.isNotBlank(item.getProductMark())) {
                        if ("1".equals(item.getProductMark())) {
                            standardQty = standardQty.add(item.getQty());
                        } else {
                            subStandardQty = subStandardQty.add(item.getQty());
                        }
                    } else {
                        standardQty = standardQty.add(item.getQty());
                    }
                    if (ocBReturnOrderId != null) {
                        normalList.add(item);
                    } else {
                        unNormalList.add(item);
                    }
                }
                //如果入库单明细中存在有退换货单号,则根据明细的退换货单号进行分组调用【退货入库单入库服务】
                if (normalList.size() != 0) {
                    refundStockService(relation.getOcBRefundIn(), relation.getItemList(), relation.getReturnId(), relation.getIsForce(), SystemUserResource.getRootUser(), standardQty, subStandardQty);
                } else if (unNormalList.size() != 0) {
                    stockAdjustment(relation.getOcBRefundIn(), unNormalList, standardQty, subStandardQty, SystemUserResource.getRootUser());
                }
            } else {
                throw new NDSException("当前入库单其他人在操作，请稍后再试!");
            }
        } catch (Exception ex) {
            throw new NDSException(ex.getMessage());
        } finally {
            redisLock.unlock();
        }
    }


    /**
     * @param ocBRefundIn    退货入库
     * @param itemList       退货入库子表
     * @param returnId       退单id
     * @param isForce        是否强制
     * @param rootUser       当前用户
     * @param standardQty    正品数量
     * @param subStandardQty 次品数量
     */
    private void refundStockService(OcBRefundIn ocBRefundIn, List<OcBRefundInProductItem> itemList, Long returnId, Integer isForce, User rootUser, BigDecimal standardQty, BigDecimal subStandardQty) {
        try {

            RefundInRelation refundInRelation = new RefundInRelation();
            refundInRelation.setRefundIn(ocBRefundIn);
            refundInRelation.setItems(itemList);
            ValueHolderV14 holderV14 = refundInExtService.returnWarehousing(refundInRelation, rootUser);
            if (!holderV14.isOK()) {
                throw new NDSException(holderV14.getMessage());
            }

            //批量更新isForce
            updateReturnInfo(itemList, isForce);

        } catch (Exception ex) {
            throw new NDSException(ex.getMessage());
        }
        //更新批次
        updateBatch(ocBRefundIn.getOcBRefundBatchId(), standardQty, subStandardQty);

        //更新in_status=2
        updateInStatus(ocBRefundIn.getId());

    }

    private void updateReturnInfo(List<OcBRefundInProductItem> itemList, Integer isForce) {
        try {
            for (OcBRefundInProductItem item : itemList) {
                Long id = item.getOcBReturnOrderId();
                if (id != null) {
                    OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(id);
                    if (ocBReturnOrder != null) {
                        OcBReturnOrder order = new OcBReturnOrder();
                        order.setId(ocBReturnOrder.getId());
                        if (isForce == null) {
                            isForce = 0;
                        }
                        order.setIsForce(isForce);
                        ocBReturnOrderMapper.updateById(order);
                        // @20201118 去除手推ES代码
                        //ES4ReturnOrder.updateReturnOrderById(ocBReturnOrderMapper.selectById(order.getId()));
                    }
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("批量更新isForce异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));

        }


    }

    private void stockAdjustment(OcBRefundIn bean, List<OcBRefundInProductItem> unNormalList, BigDecimal standardQty, BigDecimal subStandardQty, User loginUser) {
        /*
         * 如果全部明细都不存在退换货单号：
         * 根据未匹配的退货入库单明细（即退换货单号字段为空的明细），
         * 且退货入库单明细条码是否生成调整单为否，
         * 且实收条码 为空/不为空 的明细条码，调用【新增并审核库存调整单服务】，生成一张调整单
         */
        List<OcBRefundInProductItem> isList = new ArrayList<>();
        List<OcBRefundInProductItem> noList = new ArrayList<>();
        List<OcBRefundInProductItem> allList = new ArrayList<>();
        StringBuilder isAllSku = new StringBuilder();
        StringBuilder noAllSku = new StringBuilder();
        StringBuilder allSku = new StringBuilder();
        for (OcBRefundInProductItem item : unNormalList) {
            Integer isGenAdjust = item.getIsGenAdjust();
            if (0 == isGenAdjust) {
                item.setPsCSkuId(item.getRealSkuId() == null ? item.getPsCSkuId() : item.getRealSkuId());
                item.setPsCSkuEcode(item.getRealSkuEcode() == null ? item.getPsCSkuEcode() : item.getRealSkuEcode());
                allSku.append(item.getPsCSkuEcode()).append(",");
                allList.add(item);
            }
        }
        //程序是否ok
        boolean isOk = true;
        if (allList.size() > 0) {
            boolean result = sgStockAdjustmentService.addStockAdjustmenByRealSku(bean, allList, loginUser, AdjustProperty.HEADLESS_STORAGE.toLong());            //生成调整单
            if (result) {
                try {
                    logService.addLog(bean.getId(), "生成调整单", "生成调整单成功，调整条码" + allSku.toString() + "调整数量" + allList.size(), loginUser);
                } catch (Exception ex) {
                    log.error(LogUtil.format("记录退货入库日志异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
                }
            } else {
                isOk = false;
            }
        }

//        if (isList.size() != 0 && noList.size() != 0) {
//            boolean result1 = sgStockAdjustmentService.addStockAdjustmenByRealSku(bean, isList, loginUser, AdjustProperty.HEADLESS_STORAGE.toLong());
//            boolean result2 = sgStockAdjustmentService.addStockAdjustmenByRealSku(bean, noList, loginUser, AdjustProperty.HEADLESS_STORAGE.toLong());
//            if (!result1 || !result2) {
//                isOk = false;
//            }
//            //记录退货入库日志
//            if (isOk) {
//                try {
//                    logService.addLog(bean.getId(), "生成调整单", "生成调整单成功，调整条码" + isAllSku.toString() + "调整数量" + isList.size(), loginUser);
//                    logService.addLog(bean.getId(), "生成调整单", "生成调整单成功，调整条码" + noAllSku.toString() + "调整数量" + noList.size(), loginUser);
//                } catch (Exception ex) {
//                    log.debug(this.getClass().getSimpleName() + "记录退货入库日志异常" + ex);
//                }
//            }
//        } else if (isList.size() != 0) {
//            isOk = sgStockAdjustmentService.addStockAdjustmenByRealSku(bean, isList, loginUser, AdjustProperty.HEADLESS_STORAGE.toLong());
//            //更新入库单明细条码是否生成调整单字段值为是
//            allList = isList;
//            if (isOk) {
//                try {
//                    logService.addLog(bean.getId(), "生成调整单", "生成调整单成功，调整条码" + isAllSku.toString() + "调整数量" + isList.size(), loginUser);
//                } catch (Exception ex) {
//                    log.debug(this.getClass().getSimpleName() + "记录退货入库日志异常" + ex);
//                }
//            }
//        } else if (noList.size() != 0) {
//            isOk = sgStockAdjustmentService.addStockAdjustmenByRealSku(bean, noList, loginUser, AdjustProperty.HEADLESS_STORAGE.toLong());
//            //更新入库单明细条码是否生成调整单字段值为是
//            allList = noList;
//            if (isOk) {
//                try {
//                    logService.addLog(bean.getId(), "生成调整单", "生成调整单成功，调整条码" + noAllSku.toString() + "调整数量" + noList.size(), loginUser);
//                } catch (Exception ex) {
//                    log.debug(this.getClass().getSimpleName() + "记录退货入库日志异常" + ex);
//                }
//            }
//        }
        if (!isOk) {
            throw new NDSException("新增并审核库存调整单服务失败");
        }
        //更新退货入库单明细是否生成调整单字段值为是
        for (OcBRefundInProductItem item : allList) {
            try {
                ocBRefundInProductItemMapper.updateIsGenAdjust(item.getId(), bean.getId(), 1);
                //SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_PRODUCT_ITEM_INDEX_NAME, ocBRefundInProductItemMapper.selectByRefundInId(item.getId(), bean.getId()), item.getId(), bean.getId());
            } catch (Exception ex) {
                throw new NDSException("更新退货入库单明细异常！");
            }
        }

        //整理批次表
        updateBatch(bean.getOcBRefundBatchId(), standardQty, subStandardQty);

        /*
         * 扫描入库更新
         * 1.去除调用自动匹配逻辑
         * 2.无头件调整单成功后 in_status=2 已入库
         */
        updateInStatus(bean.getId());

    }

    private void updateInStatus(Long id) {
        OcBRefundIn refundIn = new OcBRefundIn();
        refundIn.setId(id);
        refundIn.setInStatus(ReturnStatus.WAREHOUSING.toInteger());
        //成功后为空字符串，防止有些成功了，还有失败原因
        refundIn.setRemarkIn("");
        try {
            refundInMapper.updateById(refundIn);
          //  SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, refundInMapper.selectById(id), id);
        } catch (Exception e) {
            log.error(LogUtil.format("更新入库状态数据库或者ES异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
    }

    private void updateBatch(Long id, BigDecimal standardQty, BigDecimal subStandardQty) {
        //库存调整单成功，批次更新异常，不抛出异常
        try {
            //累加num到批次qty
            if (id == null) {
                throw new NDSException("扫描入库异常：批次id不能为空！");
            }
            OcBRefundBatch ocBRefundBatch = ocBRefundBatchMapper.selectById(id);
            if (ocBRefundBatch.getQty() == null) {
                ocBRefundBatch.setQty(new BigDecimal("0"));
            }
            if (ocBRefundBatch.getQtySubstandard() == null) {
                ocBRefundBatch.setQtySubstandard(new BigDecimal("0"));
            }
            OcBRefundBatch batch = new OcBRefundBatch();
            batch.setId(ocBRefundBatch.getId());
            batch.setQty(ocBRefundBatch.getQty().add(standardQty));
            batch.setQtySubstandard(ocBRefundBatch.getQtySubstandard().add(subStandardQty));

            ocBRefundBatchMapper.updateById(batch);
            //推送ES
           // SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_BATCH_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_BATCH_TYPE_NAME, ocBRefundBatchMapper.selectById(batch.getId()), batch.getId());
        } catch (Exception ex) {
            log.error(LogUtil.format("扫描入库异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
    }
}
