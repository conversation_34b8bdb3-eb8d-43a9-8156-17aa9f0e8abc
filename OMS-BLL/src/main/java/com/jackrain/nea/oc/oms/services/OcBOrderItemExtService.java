package com.jackrain.nea.oc.oms.services;

import com.burgeon.r3.sg.sourcing.model.request.SkuItem2B;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyItemDistInfo;
import com.jackrain.nea.cp.dto.CpCShopProfileExt;
import com.jackrain.nea.cpext.model.table.CpCShopProfile;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemExtMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.util.BaseModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName OcBOrderItemExtMapperService
 * @Description 订单明细拓展
 * <AUTHOR>
 * @Date 2024/7/30 11:31
 * @Version 1.0
 */
@Component
@Slf4j
public class OcBOrderItemExtService {

    @Autowired
    private OcBOrderItemExtMapper ocBOrderItemExtMapper;
    @Autowired
    private BuildSequenceUtil sequenceUtil;

    public void deleteByOrderId(Long orderId) {
        ocBOrderItemExtMapper.deleteByOrderId(orderId);
    }

    public void insertAndDelete(OcBOrder order, Map<Long, CpCShopProfileExt> cpCShopProfileMap, List<OcBOrderItem> orderItemList) {
        deleteByOrderId(order.getId());
        Map<Long, List<OcBOrderItem>> ocBOrderItemMap = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getId));
        List<OcBOrderItemExt> ocBOrderItemExtList = new ArrayList<>();
        // 新增
        for (Long key : cpCShopProfileMap.keySet()) {
            List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMap.get(key);
            OcBOrderItem ocBOrderItem = ocBOrderItemList.get(0);
            CpCShopProfileExt cpCShopProfileExt = cpCShopProfileMap.get(key);
            CpCShopProfile cpCShopProfile = cpCShopProfileExt.getCpCShopProfile();
            ProductSku productSku = cpCShopProfileExt.getProductSku();
            Long id = sequenceUtil.buildOrderItemExtSequenceId();
            OcBOrderItemExt ocBOrderItemExt = new OcBOrderItemExt();
            ocBOrderItemExt.setId(id);
            ocBOrderItemExt.setOcBOrderId(order.getId());
            ocBOrderItemExt.setOrderItemId(ocBOrderItem.getId());
            ocBOrderItemExt.setTid(order.getTid());
            ocBOrderItemExt.setMDim4Id(ocBOrderItem.getMDim4Id());
            ocBOrderItemExt.setMDim6Id(ocBOrderItem.getMDim6Id());
            ocBOrderItemExt.setMDim12Id(productSku.getMDim12Id());
            ocBOrderItemExt.setMDim12Code(cpCShopProfile.getCategoryCode());
            ocBOrderItemExt.setMDim12Name(cpCShopProfile.getCategoryName());
            ocBOrderItemExt.setSalesCenterCode(cpCShopProfile.getSalesCenterCode());
            ocBOrderItemExt.setSalesCenterName(cpCShopProfile.getSalesCenterName());
            ocBOrderItemExt.setSalesDepartmentCode(cpCShopProfile.getSalesDepartmentCode());
            ocBOrderItemExt.setSalesDepartmentName(cpCShopProfile.getSalesDepartmentName());
            ocBOrderItemExt.setSalesGroupCode(cpCShopProfile.getSalesGroupCode());
            ocBOrderItemExt.setSalesGroupName(cpCShopProfile.getSalesGroupName());
            BaseModelUtil.initialBaseModelSystemField(ocBOrderItemExt);
            ocBOrderItemExtList.add(ocBOrderItemExt);
        }
        insertList(ocBOrderItemExtList);
    }

    public void insert(OcBOrderItemExt ocBOrderItemExt) {
        ocBOrderItemExtMapper.insert(ocBOrderItemExt);
    }

    public int insertList(List<OcBOrderItemExt> ocBOrderItemExtList) {
        return ocBOrderItemExtMapper.batchInsert(ocBOrderItemExtList);
    }

    public OcBOrderItemExt queryByOrderItemId(Long orderItemId) {
        return ocBOrderItemExtMapper.queryByOrderItemId(orderItemId);
    }

    public List<OcBOrderItemExt> queryByOrderId(Long orderId) {
        return ocBOrderItemExtMapper.queryByOrderId(orderId);
    }

    public List<OcBOrderItemExt> queryByOrderIdList(List<Long> orderIdList) {
        return ocBOrderItemExtMapper.queryByOrderIdList(orderIdList);
    }

    public void updateByItemId(SgOccupyItemDistInfo sgOccupyItemDistInfo, Long orderId) {
        OcBOrderItemExt ocBOrderItemEx = ocBOrderItemExtMapper.queryByOrderAndItemId(sgOccupyItemDistInfo.getSourceItemId(), orderId);
        if (ocBOrderItemEx == null) {
            return;
        }
        OcBOrderItemExt updateItemExt = new OcBOrderItemExt();
        updateItemExt.setId(ocBOrderItemEx.getId());
        updateItemExt.setOcBOrderId(ocBOrderItemEx.getOcBOrderId());
        updateItemExt.setOrderItemId(ocBOrderItemEx.getOrderItemId());
        updateItemExt.setDistCodeLevelOne(sgOccupyItemDistInfo.getDistCodeLevelOne());
        updateItemExt.setDistNameLevelOne(sgOccupyItemDistInfo.getDistNameLevelOne());
        updateItemExt.setDistCodeLevelTwo(sgOccupyItemDistInfo.getDistCodeLevelTwo());
        updateItemExt.setDistNameLevelTwo(sgOccupyItemDistInfo.getDistNameLevelTwo());
        updateItemExt.setDistCodeLevelThree(sgOccupyItemDistInfo.getDistCodeLevelThree());
        updateItemExt.setDistNameLevelThree(sgOccupyItemDistInfo.getDistNameLevelThree());
        updateItemExt.setModifieddate(new Date());
        ocBOrderItemExtMapper.updateItemExtCustom(updateItemExt);
    }

    public void updateByItemId(SkuItem2B skuItem2B) {
        OcBOrderItemExt ocBOrderItemEx = ocBOrderItemExtMapper.queryByOrderItemId(skuItem2B.getSourceItemId());
        if (ocBOrderItemEx == null) {
            return;
        }
        OcBOrderItemExt updateItemExt = new OcBOrderItemExt();
        updateItemExt.setId(ocBOrderItemEx.getId());
        updateItemExt.setOcBOrderId(ocBOrderItemEx.getOcBOrderId());
        updateItemExt.setOrderItemId(ocBOrderItemEx.getOrderItemId());
        updateItemExt.setDistCodeLevelOne(skuItem2B.getDistCodeLevelOne());
        updateItemExt.setDistNameLevelOne(skuItem2B.getDistNameLevelOne());
        updateItemExt.setDistCodeLevelTwo(skuItem2B.getDistCodeLevelTwo());
        updateItemExt.setDistNameLevelTwo(skuItem2B.getDistNameLevelTwo());
        updateItemExt.setDistCodeLevelThree(skuItem2B.getDistCodeLevelThree());
        updateItemExt.setDistNameLevelThree(skuItem2B.getDistNameLevelThree());
        updateItemExt.setModifieddate(new Date());
        ocBOrderItemExtMapper.updateItemExtCustom(updateItemExt);
    }
}
