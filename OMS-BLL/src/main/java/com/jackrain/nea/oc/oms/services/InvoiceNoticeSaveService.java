package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeProMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBInvoiceNoticeRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNotice;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticeItem;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticePro;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OcInvoiceLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.OcInvoiceStatusEnum;
import com.jackrain.nea.oc.oms.nums.OcInvoiceTypeEnum;
import com.jackrain.nea.oc.oms.nums.OrderInvoiceStatusEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.InvoiceNoticeDealUtil;
import com.jackrain.nea.psext.api.utils.JsonUtils;
import com.jackrain.nea.resource.SystemTableNames;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AmountCalcUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * @author: chenxiulou
 * @description: 开票通知单 保存
 * @since: 2019-07-20
 * create at : 2019-07-20 10:49
 */
@Slf4j
@Component
public class InvoiceNoticeSaveService extends CommandAdapter {

    @Autowired
    private OcBInvoiceNoticeMapper invoiceNoticeMapper;
    @Autowired
    private OcBInvoiceNoticeItemMapper invoiceNoticeItemMapper;
    @Autowired
    private OcBInvoiceNoticeProMapper invoiceNoticeProMapper;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private InvoiceNoticeDealUtil dealUtil;
    @Autowired
    private InvoiceNoticeEstatusService estatusService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBOrderMapper orderMapper;

    public ValueHolder saveInvoiceNotice(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);

        Long id = param.getLong("objid");
        //Y/N
        String logFlg = param.getString("logFlg");
        String confirmFlg = param.getString("confirmFlg");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        OcBInvoiceNoticeRelation invoiceNoticeRelation = JsonUtils.jsonParseClass(fixColumn, OcBInvoiceNoticeRelation.class);
        if (invoiceNoticeRelation == null) {
            throw new NDSException("数据异常！");
        }

        if (fixColumn != null && id != null) {
            //将当前订单id赋值
            invoiceNoticeRelation.setId(id);
            if (confirmFlg == null) {
                List<OcBInvoiceNoticeItem> invoiceNoticeItems = invoiceNoticeRelation.getInvoiceNoticeItems();
                List<OcBInvoiceNoticePro> invoiceNoticePros = invoiceNoticeRelation.getInvoiceNoticePros();
                if (CollectionUtils.isEmpty(invoiceNoticeItems) || CollectionUtils.isEmpty(invoiceNoticePros)) {
                    return ValueHolderUtils.getFailValueHolder("请录入明细数据,再保存！");
                }
                BigDecimal amt = BigDecimal.ZERO;
                for (OcBInvoiceNoticeItem invoiceNoticeItem : invoiceNoticeItems) {
                    amt = AmountCalcUtils.addBigDecimal(amt, invoiceNoticeItem.getAmtTaxable());
                }
                if (invoiceNoticeRelation.getInvoiceNotice() != null) {
                    invoiceNoticeRelation.getInvoiceNotice().setAmt(amt);
                }
            }
            if (id != -1) {
                return updateInvoiceNoitce(session.getUser(), invoiceNoticeRelation, logFlg);
            } else {
                return insertInvoiceNoitce(session.getUser(), invoiceNoticeRelation, logFlg);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 开票通知插入
     *
     * @param user
     * @param invoiceNoticeRelation
     * @return com.jackrain.nea.util.ValueHolder
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    public ValueHolder insertInvoiceNoitce(User user,
                                           OcBInvoiceNoticeRelation invoiceNoticeRelation,
                                           String logFlg) {
        if (invoiceNoticeRelation.getInvoiceNotice() == null) {
            return ValueHolderUtils.getFailValueHolder("开票信息为空！");
        }
        if (CollectionUtils.isEmpty(invoiceNoticeRelation.getInvoiceNoticeItems())) {
            return ValueHolderUtils.getFailValueHolder("开票信息开票内容为空！");
        }
        if (CollectionUtils.isEmpty(invoiceNoticeRelation.getInvoiceNoticePros())) {
            return ValueHolderUtils.getFailValueHolder("开票信息订单商品明细为空！");
        }
        if (user == null) {
            return ValueHolderUtils.getFailValueHolder("开票信息用户信息为空！");
        }
        //1.开票信息
        Long id = sequenceUtil.buildSequenceId(SystemTableNames.OC_B_INVOICE_NOTICE_TABLE_NAME);
        ValueHolderV14<OcBInvoiceNotice> v14 = exeInvoiceNotice(user,
                invoiceNoticeRelation.getInvoiceNotice(), id, "insert");
        if (v14 != null && v14.getCode() == ResultCode.SUCCESS) {
            //订单商品明细
            ValueHolderV14<List<OcBInvoiceNoticeItem>> v14Items = exeInvoiceNoticeItem(user,
                    invoiceNoticeRelation.getInvoiceNoticeItems(), id, "insert");
            //开票内容
            ValueHolderV14<List<OcBInvoiceNoticePro>> v14Pros = exeInvoiceNoticePro(user,
                    invoiceNoticeRelation.getInvoiceNoticePros(), id, "insert");
            //更新全渠道订单（开票及票据ID）
            OcBInvoiceNotice esInvoice = v14.getData();
            ValueHolderV14<List<OcBOrder>> v14Orders = dealUtil.updateOcBOrderList(esInvoice.getId(),
                    esInvoice.getOcBOrderId(), "insert", user);
            List<OcBOrder> esOrders = new ArrayList<>();
            if (v14Orders != null && v14Orders.getCode() == ResultCode.SUCCESS) {
                esOrders = v14Orders.getData();
            }
            //推送ES
            dealUtil.pushInvoiceNoticeToEs(esInvoice, v14Items.getData(), v14Pros.getData(), esOrders);
            if (logFlg == null || logFlg.equals("Y")) {
                //操作日志
                dealUtil.addInvoiceNoticeLog(id, OcInvoiceLogTypeEnum.ADD.getKey(), OcInvoiceLogTypeEnum.ADD.getName(), user);
            }
        } else if (v14 != null && v14.getCode() == ResultCode.FAIL) {
            return ValueHolderUtils.getFailValueHolder(v14.getMessage());
        }
        return ValueHolderUtils.getSuccessValueHolder(id, "OC_B_INVOICE_NOTICE");
    }

    public ValueHolder updateInvoiceNoitce(User user, OcBInvoiceNoticeRelation invoiceNoticeRelation, String logFlg) {
        //1.开票信息
        OcBInvoiceNotice esInvoiceNotice = new OcBInvoiceNotice();
        if (invoiceNoticeRelation.getInvoiceNotice() != null) {
            ValueHolderV14<OcBInvoiceNotice> v14 = exeInvoiceNotice(user,
                    invoiceNoticeRelation.getInvoiceNotice(), invoiceNoticeRelation.getId(), "update");
            if (v14 != null && v14.getCode() == ResultCode.SUCCESS) {
                esInvoiceNotice = v14.getData();
            } else if (v14 != null && v14.getCode() == ResultCode.FAIL) {
                return ValueHolderUtils.getFailValueHolder(v14.getMessage());
            }
        }

        //开票内容
        List<OcBInvoiceNoticeItem> esItems = new ArrayList<>();
        List<OcBInvoiceNoticeItem> invoiceNoticeItems = invoiceNoticeRelation.getInvoiceNoticeItems();
        if (!CollectionUtils.isEmpty(invoiceNoticeItems)) {
            invoiceNoticeItemMapper.delete(new QueryWrapper<OcBInvoiceNoticeItem>().eq("OC_B_INVOICE_NOTICE_ID",
                    invoiceNoticeRelation.getId()));
            ValueHolderV14<List<OcBInvoiceNoticeItem>> v14AddItems = exeInvoiceNoticeItem(user,
                    invoiceNoticeItems, invoiceNoticeRelation.getId(), "insert");
            if (v14AddItems != null && CollectionUtils.isEmpty(v14AddItems.getData())) {
                esItems.addAll(v14AddItems.getData());
            }
        }

        //订单商品明细
        List<OcBInvoiceNoticePro> esPros = new ArrayList<>();
        List<OcBInvoiceNoticePro> invoiceNoticePros = invoiceNoticeRelation.getInvoiceNoticePros();
        if (!CollectionUtils.isEmpty(invoiceNoticePros)) {
            invoiceNoticeProMapper.delete(new QueryWrapper<OcBInvoiceNoticePro>().eq("OC_B_INVOICE_NOTICE_ID",
                    invoiceNoticeRelation.getId()));
            ValueHolderV14<List<OcBInvoiceNoticePro>> v14AddPros = exeInvoiceNoticePro(user,
                    invoiceNoticePros, invoiceNoticeRelation.getId(), "insert");
            if (v14AddPros != null && CollectionUtils.isEmpty(v14AddPros.getData())) {
                esPros.addAll(v14AddPros.getData());
            }
        }
        //推送ES
        dealUtil.pushInvoiceNoticeToEs(esInvoiceNotice, esItems, esPros, null);
        if (logFlg == null || logFlg.equals("Y")) {
            //操作日志
            dealUtil.addInvoiceNoticeLog(invoiceNoticeRelation.getId(), OcInvoiceLogTypeEnum.SAVE.getKey(),
                    OcInvoiceLogTypeEnum.SAVE.getName(), user);
        }
        return ValueHolderUtils.getSuccessValueHolder(invoiceNoticeRelation.getId(), "OC_B_INVOICE_NOTICE");
    }

    /**
     * @param user
     * @param invoiceNotice
     * @return
     * @Description 开票信息 插入
     * <AUTHOR>
     * @date 2019-07-20 2019-07-20
     */
    private ValueHolderV14<OcBInvoiceNotice> exeInvoiceNotice(User user, OcBInvoiceNotice invoiceNotice,
                                                              Long id, String action) {
        ValueHolderV14<OcBInvoiceNotice> v14 = new ValueHolderV14<OcBInvoiceNotice>();
        if (StringUtils.isNotBlank(invoiceNotice.getInvoiceNo())) {
            QueryWrapper<OcBInvoiceNotice> wrapper = new QueryWrapper<>();
            wrapper.eq("INVOICE_NO", invoiceNotice.getInvoiceNo());
            wrapper.eq("ISACTIVE", invoiceNotice.getIsactive());
            List<OcBInvoiceNotice> invoiceList = invoiceNoticeMapper.selectList(wrapper);
            for (OcBInvoiceNotice invoice : invoiceList) {
                if (!invoice.getId().equals(id)) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("发票号重复，不允许保存！");
                    return v14;
                }
            }
        }

        if (invoiceNotice.getCpCLogisticsId() != null) {
            CpLogistics cpLogistics = cpRpcService.cpLogisticsInfo(invoiceNotice.getCpCLogisticsId());
            if (cpLogistics != null) {
                invoiceNotice.setCpCLogisticsEcode(cpLogistics.getEcode());
                invoiceNotice.setCpCLogisticsEname(cpLogistics.getEname());
            } else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("快递公司不存在或者已失效，不允许保存！");
                return v14;
            }
        }

        if (StringUtils.isNotBlank(invoiceNotice.getOcBOrderId())) {
            List<String> idList = Arrays.asList(invoiceNotice.getOcBOrderId().split(","));
            if (idList.size() > 50) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("开票通知最大支持50张订单合并开票，当前不允许开票！");
                return v14;
            }
        }

        if (StringUtils.isNotBlank(invoiceNotice.getBuyerRamark()) && invoiceNotice.getBuyerRamark().length() > 100) {
            invoiceNotice.setBuyerRamark(StringUtils.substring(invoiceNotice.getBuyerRamark(), 0, 100));
        }
        if (StringUtils.isNotBlank(invoiceNotice.getSellerRemark()) && invoiceNotice.getSellerRemark().length() > 500) {
            invoiceNotice.setSellerRemark(StringUtils.substring(invoiceNotice.getSellerRemark(), 0, 500));
        }
        switch (action) {
            case "insert":
                invoiceNotice.setId(id);
                invoiceNotice.setBillNo(generateInvoiceNoticeBillNo(user));
                invoiceNotice.setAdOrgId((long) user.getOrgId());
                invoiceNotice.setAdClientId((long) user.getClientId());
                invoiceNotice.setOwnerid(Long.valueOf(user.getId()));
                invoiceNotice.setOwnername(user.getName());
                invoiceNotice.setOwnerename(user.getEname());
                invoiceNotice.setCreationdate(new Date());
                invoiceNotice.setModifierid(Long.valueOf(user.getId()));
                invoiceNotice.setModifiername(user.getName());
                invoiceNotice.setModifierename(user.getEname());
                invoiceNotice.setModifieddate(new Date());
                //是否启用
                invoiceNotice.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
                int insert = invoiceNoticeMapper.insert(invoiceNotice);
                if (insert > 0) {
                    v14.setCode(ResultCode.SUCCESS);
                    v14.setData(invoiceNotice);
                }
                break;
            case "update":
                invoiceNotice.setId(id);
                invoiceNotice.setModifierid(Long.valueOf(user.getId()));
                invoiceNotice.setModifiername(user.getName());
                invoiceNotice.setModifierename(user.getEname());
                invoiceNotice.setModifieddate(new Date());
                int update = invoiceNoticeMapper.updateById(invoiceNotice);
                if (update > 0) {
                    OcBInvoiceNotice esInvoicNotice = invoiceNoticeMapper.selectById(invoiceNotice.getId());
                    v14.setCode(ResultCode.SUCCESS);
                    v14.setData(esInvoicNotice);
                }
                break;
            default:
                v14.setCode(ResultCode.FAIL);
                break;
        }
        return v14;
    }

    /**
     * @param user
     * @param noticeItems
     * @param noticeId
     * @return
     * @Description 开票内容插入
     * <AUTHOR>
     * @date 2019-07-20 2019-07-20
     */
    private ValueHolderV14<List<OcBInvoiceNoticeItem>> exeInvoiceNoticeItem(User user, List<OcBInvoiceNoticeItem> noticeItems,
                                                                            Long noticeId, String action) {
        ValueHolderV14<List<OcBInvoiceNoticeItem>> v14 = new ValueHolderV14<List<OcBInvoiceNoticeItem>>();
        switch (action) {
            case "insert":
                if (!CollectionUtils.isEmpty(noticeItems)) {
                    List<OcBInvoiceNoticeItem> itemList = new ArrayList<>();
                    for (OcBInvoiceNoticeItem invoiceNoticeItem : noticeItems) {
                        invoiceNoticeItem.setId(sequenceUtil.buildSequenceId(SystemTableNames.OC_B_INVOICE_NOTICE_ITEM_TABLE_NAME));
                        invoiceNoticeItem.setOcBInvoiceNoticeId(noticeId);
                        invoiceNoticeItem.setAdOrgId((long) user.getOrgId());
                        invoiceNoticeItem.setAdClientId((long) user.getClientId());
                        invoiceNoticeItem.setOwnerid(Long.valueOf(user.getId()));
                        invoiceNoticeItem.setOwnername(user.getName());
                        invoiceNoticeItem.setOwnerename(user.getEname());
                        invoiceNoticeItem.setCreationdate(new Date());
                        invoiceNoticeItem.setModifierid(Long.valueOf(user.getId()));
                        invoiceNoticeItem.setModifiername(user.getName());
                        invoiceNoticeItem.setModifierename(user.getEname());
                        invoiceNoticeItem.setModifieddate(new Date());
                        //是否启用
                        invoiceNoticeItem.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
                        invoiceNoticeItemMapper.insert(invoiceNoticeItem);
                        itemList.add(invoiceNoticeItemMapper.selectById(invoiceNoticeItem.getId()));
                    }
                    v14.setCode(ResultCode.SUCCESS);
                    v14.setData(itemList);
                }
                break;
            case "update":
                if (!CollectionUtils.isEmpty(noticeItems)) {
                    List<OcBInvoiceNoticeItem> itemList = new ArrayList<>();
                    for (OcBInvoiceNoticeItem invoiceNoticeItem : noticeItems) {
                        invoiceNoticeItem.setModifierid(Long.valueOf(user.getId()));
                        invoiceNoticeItem.setModifiername(user.getName());
                        invoiceNoticeItem.setModifierename(user.getEname());
                        invoiceNoticeItem.setModifieddate(new Date());
                        invoiceNoticeItem.setOcBInvoiceNoticeId(null);
                        invoiceNoticeItemMapper.updateById(invoiceNoticeItem);
                        invoiceNoticeItem.setOcBInvoiceNoticeId(noticeId);
                        itemList.add(invoiceNoticeItemMapper.selectById(invoiceNoticeItem.getId()));
                    }
                    v14.setCode(ResultCode.SUCCESS);
                    v14.setData(itemList);
                }
                break;
        }
        return v14;
    }

    /**
     * @param user
     * @param noticePros
     * @param noticeId
     * @return
     * @Description 开票内容插入
     * <AUTHOR>
     * @date 2019-07-20 2019-07-20
     */
    private ValueHolderV14<List<OcBInvoiceNoticePro>> exeInvoiceNoticePro(User user, List<OcBInvoiceNoticePro> noticePros,
                                                                          Long noticeId, String action) {
        ValueHolderV14<List<OcBInvoiceNoticePro>> v14 = new ValueHolderV14<List<OcBInvoiceNoticePro>>();
        switch (action) {
            case "insert":
                if (!CollectionUtils.isEmpty(noticePros)) {
                    List<OcBInvoiceNoticePro> proList = new ArrayList<>();
                    for (OcBInvoiceNoticePro invoiceNoticePro : noticePros) {
                        invoiceNoticePro.setId(sequenceUtil.buildSequenceId(SystemTableNames.OC_B_INVOICE_NOTICE_PRO_TABLE_NAME));
                        invoiceNoticePro.setOcBInvoiceNoticeId(noticeId);
                        invoiceNoticePro.setAdOrgId((long) user.getOrgId());
                        invoiceNoticePro.setAdClientId((long) user.getClientId());
                        invoiceNoticePro.setOwnerid(Long.valueOf(user.getId()));
                        invoiceNoticePro.setOwnername(user.getName());
                        invoiceNoticePro.setOwnerename(user.getName());
                        invoiceNoticePro.setCreationdate(new Date());
                        invoiceNoticePro.setModifierid(Long.valueOf(user.getId()));
                        invoiceNoticePro.setModifiername(user.getName());
                        invoiceNoticePro.setModifierename(user.getEname());
                        invoiceNoticePro.setModifieddate(new Date());
                        invoiceNoticePro.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
                        invoiceNoticeProMapper.insert(invoiceNoticePro);
                        proList.add(invoiceNoticeProMapper.selectById(invoiceNoticePro.getId()));
                    }
                    v14.setCode(ResultCode.SUCCESS);
                    v14.setData(proList);
                }
                break;
            case "update":
                if (!CollectionUtils.isEmpty(noticePros)) {
                    List<OcBInvoiceNoticePro> proList = new ArrayList<>();
                    for (OcBInvoiceNoticePro invoiceNoticePro : noticePros) {
                        invoiceNoticePro.setModifierid(Long.valueOf(user.getId()));
                        invoiceNoticePro.setModifiername(user.getName());
                        invoiceNoticePro.setModifierename(user.getEname());
                        invoiceNoticePro.setModifieddate(new Date());
                        invoiceNoticePro.setOcBInvoiceNoticeId(null);
                        invoiceNoticeProMapper.updateById(invoiceNoticePro);
                        invoiceNoticePro.setOcBInvoiceNoticeId(noticeId);
                        proList.add(invoiceNoticeProMapper.selectById(invoiceNoticePro.getId()));
                    }
                    v14.setCode(ResultCode.SUCCESS);
                    v14.setData(proList);
                }
                break;
        }
        return v14;
    }


    /**
     * @param user
     * @return
     * @Description 发票单号生成
     * <AUTHOR>
     * @date 2019-07-20 2019-07-20
     */
    private String generateInvoiceNoticeBillNo(User user) {
        JSONObject sequence = new JSONObject();
        sequence.put("OC_B_INVOICE_NOTICE", "");
        return SequenceGenUtil.generateSquence("SEQ_OC_B_INVOICE_NOTICE", sequence, user.getLocale(), false);
    }

    /**
     * 开票确认
     *
     * @param session
     * @return ValueHolder
     */
    public ValueHolder confirmInvoiceNotice(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        param.put("logFlg", "N");
        param.put("confirmFlg", "Y");
        event.put("param", param);
        session.setEvent(event);
        //更新开票信息
        ValueHolder vh1 = this.updateInvoiceNotice(session);
        if (vh1.isOK()) {
            //修改开票状态
            ValueHolder vh2 = estatusService.changeEstatus(session, OcInvoiceStatusEnum.WAIT_INVOICE,
                    OcInvoiceStatusEnum.ALREADY_INVOICE, OcInvoiceLogTypeEnum.CONFIRM_INVOICE);

            Map<String, Object> data = (Map) vh1.getData().get("data");
            if (data.get("objid") != null) {
                OcBInvoiceNotice invoiceNotice = invoiceNoticeMapper.selectById(Long.valueOf(data.get("objid").toString()));
                if (invoiceNotice.getOcBOrderId() != null) {
                    List<String> idList = Arrays.asList(invoiceNotice.getOcBOrderId().split(","));
                    if (!CollectionUtils.isEmpty(idList)) {
                        for (String id : idList) {
                            //修改订单状态
                            dealUtil.updateOcBOderSingle(Long.valueOf(id), null, null, null,
                                    session.getUser(), OrderInvoiceStatusEnum.UNINVOICED);
                        }
                        List<OcBOrder> orders = orderMapper.selectByIdsStrList(idList);
                        //推送ES
                        dealUtil.pushInvoiceNoticeToEs(null, null, null, orders);
                    }
                }
            }
            return vh2;
        }
        if (vh1 != null) {
            return vh1;
        }
        throw new NDSException("开票确认失败！");
    }

    private ValueHolder updateInvoiceNotice(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);

        Long id = param.getLong("objid");
        //Y/N
        String logFlg = param.getString("logFlg");
        String importFlg = param.getString("importFlag");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        OcBInvoiceNoticeRelation invoiceNoticeRelation = JsonUtils.jsonParseClass(fixColumn, OcBInvoiceNoticeRelation.class);
        if (invoiceNoticeRelation == null) {
            throw new NDSException("数据异常！");
        }

        if (fixColumn != null && id != null) {
            //将当前订单id赋值
            invoiceNoticeRelation.setId(id);
            //必输验证
            if (invoiceNoticeRelation.getInvoiceNotice() != null) {
                OcBInvoiceNotice invoiceNotice = invoiceNoticeRelation.getInvoiceNotice();
                if (OcInvoiceTypeEnum.ELE_INVOICE.getKey().equals(invoiceNotice.getInvoiceType())) {
                    if (StringUtils.isBlank(invoiceNotice.getInvoiceNo())) {
                        throw new NDSException("开票号必须填写！");
                    }
                } else {
                    if (StringUtils.isBlank(invoiceNotice.getInvoiceNo()) ||
                            StringUtils.isBlank(invoiceNotice.getLogisticsNo()) ||
                            invoiceNotice.getCpCLogisticsId() == null) {
                        throw new NDSException("开票号、快递公司、快递单号必须填写！");
                    }
                }
                if (importFlg != null) {
                    if (invoiceNotice.getInvoiceEname() == null) {
                        invoiceNotice.setInvoiceId(Long.valueOf(session.getUser().getId()));
                        invoiceNotice.setInvoiceName(session.getUser().getName());
                        invoiceNotice.setInvoiceEname(session.getUser().getEname());
                    }
                    if (invoiceNotice.getInvoiceTime() == null) {
                        invoiceNotice.setInvoiceTime(new Date());
                    }
                }
            }
            return updateInvoiceNoitce(session.getUser(), invoiceNoticeRelation, logFlg);
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 开票审核
     *
     * @param session
     * @return ValueHolder
     */
    public ValueHolder auditInvoiceNotice(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        // 标准ID
        String objid = param.getString("objid");
        ValueHolder vh1 = null;
        if (objid != null) {
            param.put("logFlg", "N");
            event.put("param", param);
            session.setEvent(event);
            //更新开票信息
            vh1 = this.saveInvoiceNotice(session);
        }
        if (vh1 == null || vh1.isOK()) {
            if ("-1".equals(objid) && vh1 != null) {
                Map<String, Object> data = (Map) vh1.getData().get("data");
                param.put("objid", data.get("objid"));
                event.put("param", param);
                session.setEvent(event);
            }
            //修改开票状态
            return estatusService.changeEstatus(session, OcInvoiceStatusEnum.NOT_AUDITED,
                    OcInvoiceStatusEnum.WAIT_INVOICE, OcInvoiceLogTypeEnum.AUDIT);
        }
        if (vh1 != null) {
            return vh1;
        }
        throw new NDSException("开票审核失败！");
    }
}
