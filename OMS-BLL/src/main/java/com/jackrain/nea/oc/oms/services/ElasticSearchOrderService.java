package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.enums.ActualPresinkEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.OmsIsExchangeItemEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderExtendLabelEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.request.OrderQueryRequest;
import com.jackrain.nea.oc.oms.model.result.QueryOrderTagResult;
import com.jackrain.nea.oc.oms.util.DateConversionUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.regex.Pattern;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/4/30
 */
@Slf4j
public class ElasticSearchOrderService {

    private static final String statusKey = "ORDER_STATUS";
    private static final String platStatusKey = "PLATFORM_STATUS";

    /**
     * 订单标签不含
     **/
    private static final String HAS_NO_TAG = "HAS_NO_TAG";
    /**
     * 订单标签
     **/
    private static final String HAS_TAG = "HAS_TAG";
    /**
     * 下拉全选
     **/
    private static final String SELECT_ALL = "all";

    /**
     * 不含实际预下沉状态
     **/
    private static final String NO_ACTUAL_PRESINK_STATUS = "NO_ACTUAL_PRESINK_STATUS";
    /**
     * 实际预下沉状态
     **/
    private static final String ACTUAL_PRESINK_STATUS = "ACTUAL_PRESINK_STATUS";

    /**
     * 订单查询需要根据业务类型查询  区分TOB TOC订单
     **/
    private static final String QUERY_BUSINESS_TYPE_ID = "BUSINESS_TYPE_ID";
    private static final String QUERY_BUSINESS_TYPE_ID_SIGN = "BUSINESS_TYPE_ID_SIGN";
    private static final String QUERY_BUSINESS_TYPE_ID_TOB_SIGN = "TOB";
    private static final String ORDER_SORT = "ORDER_SORT";


    public static void execute(OrderQueryRequest queryDto, JSONObject jsnObj, Boolean useKeyword) {
        checkInitFun.accept(queryDto);
        JSONObject pageJo = jsnObj.getJSONObject("page");
        int size = pageJo.getIntValue("pageSize");
        // current es not support long type
        int index = pageJo.getIntValue("pageNum");
        if (index < 1) {
            index = 1;
        }
        if (size < 1) {
            size = 20;
        }
        int start = (index - 1) * size;
        queryDto.setStart(start);
        queryDto.setIndex(index);
        queryDto.setSize(size);
        paramConditionHandler(jsnObj, queryDto, useKeyword);
    }

    /**
     * check and init search conditions
     */
    private static final Consumer<OrderQueryRequest> checkInitFun = o -> {

        if (o.getWhere() == null) {
            o.setWhere(new JSONObject());
        }
        if (o.getFilter() == null) {
            o.setFilter(new JSONObject());
        }
        if (o.getChild() == null) {
            o.setChild(new JSONObject());
        }
        if (o.getField() == null) {
            o.setField(new String[]{"ID"});
        }
        if (o.getOrder() == null) {
            o.setOrder(new JSONArray());
        }
        if (o.getExistKeys() == null) {
            o.setExistKeys(new JSONObject());
        }
        if (o.getNotExistKeys() == null) {
            o.setNotExistKeys(new JSONObject());
        }
    };


    /**
     * deal search conditions
     */
    static void paramConditionHandler(JSONObject jo, OrderQueryRequest eso, Boolean useKeyword) {
        final String LABEL_SEARCH = "label";
        final String INTELLI_SEARCH = "queryInfo";
        final String STATUS_SEARCH = "status";
        final String HIGH_SEARCH = "highSearch";
        final String CHOICE_SEARCH = "choiceSearch";
        final String SORT_KEY = "sort";

        final String listKey = "list";
        final String typeKey = "type";
        final String valueKey = "value";
        final String queryNameKey = "queryName";

        final String dateKey = "date";
        final String inputKey = "Input";
        final String selectKey = "Select";
        final String LACKSTOCK_OR_AUDIT = "lackstockOrAudit";


        JSONArray labelJas = jo.getJSONArray(LABEL_SEARCH);
        if (CollectionUtils.isEmpty(labelJas)) {
            labelJas = new JSONArray();
        }
        JSONArray queryInfoJo = jo.getJSONArray(INTELLI_SEARCH);
        if (CollectionUtils.isEmpty(queryInfoJo)) {
            queryInfoJo = new JSONArray();
        }
        JSONObject statusJo = jo.getJSONObject(STATUS_SEARCH);
        if (Objects.isNull(statusJo)) {
            statusJo = new JSONObject();
        }
        JSONArray highSearch = jo.getJSONArray(HIGH_SEARCH);
        if (CollectionUtils.isEmpty(highSearch)) {
            highSearch = new JSONArray();
        }
        JSONArray choiceSearch = jo.getJSONArray(CHOICE_SEARCH);
        if (CollectionUtils.isEmpty(choiceSearch)) {
            choiceSearch = new JSONArray();
        }
        JSONArray sortJay = jo.getJSONArray(SORT_KEY);
        if (CollectionUtils.isEmpty(sortJay)) {
            sortJay = new JSONArray();
        }
        eso.getChild().put("oc_b_order_item", new JSONObject());
        eso.getChild().put("oc_b_order_item_ext", new JSONObject());


        // 根据lackstockOrAudit字段值 获取实缺列表或审核失败列表
        String lackstockOrAudit = jo.getString(LACKSTOCK_OR_AUDIT);
        CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        String splitStr = strRedisTemplate.opsForValue().get(
                "business_system:out_stock_recapture_times");
        Integer qtySplit = null;
        try {
            qtySplit = Integer.valueOf(Optional.ofNullable(splitStr).orElse("3"));
        } catch (Exception e) {
            log.error(LogUtil.format("business_system:out_stock_recapture_times: {}"), Throwables.getStackTraceAsString(e));
        }
        if (StringUtils.isNotEmpty(lackstockOrAudit)) {
            if ("LACKSTOCK".equalsIgnoreCase(lackstockOrAudit)) {
                eso.getFilter().put("QTY_SPLIT", Optional.ofNullable(qtySplit).orElse(3) + 1 + "~");
            } else if ("AUDIT".equalsIgnoreCase(lackstockOrAudit)) {
                eso.getWhere().put("AUDIT_FAILED_TYPE", "!=0");
            }
            // 缺货订单才需要根据qty_split字段筛选
        } else if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().toString().equals(statusJo.getString("value"))) {
            eso.getFilter().put("QTY_SPLIT", "~" + (Optional.ofNullable(qtySplit).orElse(3)));
        } else if (OrderExtendLabelEnum.LABEL103.getVal().equals(statusJo.getString("value"))) {
            //换货标签页
            eso.getWhere().put("ORDER_TYPE", OrderTypeEnum.EXCHANGE.getVal());
        } else if (OrderExtendLabelEnum.LABEL102.getVal().equals(statusJo.getString("value"))) {
            //换货标签页
            eso.getWhere().put("ORDER_STATUS", OmsOrderStatus.UNCONFIRMED.toInteger());
            eso.getWhere().put("ORDER_TYPE", OrderTypeEnum.EXCHANGE.getVal());
        }

        // 1.0 part: tag section
        sqlIntelliSearchTag(labelJas, eso);

        // 2.0 part: intelli select section
        if (queryInfoJo != null) {
            int infoLen = queryInfoJo.size();
            for (int i = 0; i < infoLen; i++) {
                JSONObject tmpJo = queryInfoJo.getJSONObject(i);
                String type = tmpJo.getString(typeKey);
                String queryName = tmpJo.getString(queryNameKey);
                if (selectKey.equalsIgnoreCase(type)) {
                    JSONArray selectBox = tmpJo.getJSONArray(listKey);
                    intelliControlHandler(selectBox, queryName, eso.getWhere());
                } else if (dateKey.equalsIgnoreCase(type)) {
                    String value = tmpJo.getString(valueKey);
                    timeControlHandler(value, queryName, eso.getFilter(), eso.getChild());
                } else if (inputKey.equalsIgnoreCase(type)) {
                    String value = tmpJo.getString(valueKey);
                    textControlHandler(value, queryName, eso.getWhere(), eso.getFilter(), eso.getChild(), useKeyword);
                } else if ("DropDownSelectFilter".equalsIgnoreCase(type)) {
                    JSONArray selectDrop = tmpJo.getJSONArray(listKey);
                    intelliDropDownControl(selectDrop, queryName, eso.getWhere());
                }
            }
        }

        // 3.0 part: high search section
        if (highSearch != null) {
            for (int i = 0; i < highSearch.size(); i++) {
                JSONObject tmpJo = highSearch.getJSONObject(i);
                if (tmpJo != null) {
                    String type = tmpJo.getString(typeKey);
                    String queryName = tmpJo.getString(queryNameKey);

                    // 此处找到固定的字段 然后如果有值的话 需要塞到排序的字段里面去
                    if (ORDER_SORT.equalsIgnoreCase(queryName)) {
                        String sort = tmpJo.getString(valueKey);
                        if (StringUtils.isNotEmpty(sort)) {
                            String[] valAry = sort.split(",");
                            for (String val : valAry) {
                                // 空格截取。前面的是字段名 后面的是升序还是降序
                                String[] sortArr = val.split(" ");
                                JSONObject jsn = new JSONObject();
                                jsn.put("key", sortArr[0]);
                                jsn.put("value", sortArr[1]);
                                sortJay.add(jsn);
                            }
                        }
                    }
                    if (selectKey.equalsIgnoreCase(type)) {
                        String sltChk = tmpJo.getString(valueKey);
                        selectControlHandler(sltChk, queryName, eso.getWhere(), eso.getChild(), eso.getExistKeys(), eso.getNotExistKeys());
                    } else if (dateKey.equalsIgnoreCase(type)) {
                        String dateStr = tmpJo.getString(valueKey);
                        //查询明细里面的date类型 加上child
                        timeControlHandler(dateStr, queryName, eso.getFilter(), eso.getChild());
                    } else if (inputKey.equalsIgnoreCase(type)) {
                        String txt = tmpJo.getString(valueKey);
                        textControlHandler(txt, queryName, eso.getWhere(), eso.getFilter(), eso.getChild(), useKeyword);
                    }
                }
            }
        }

        // 0.0 part: sort section
        dealSortCondition(sortJay, eso.getOrder());

        // 4. part: Merge status - status button section
        postButtonStatus(statusJo, eso.getWhere());
        //空实体仓不查询 1123 lwj
        //addDefaultPhyWareHouse(eso.getWhere());

        // yiShang business
        afterArrangeEsHandler(eso.getWhere());

        // 5. choice Search
        JSONArray allSearchAry = dealChoiceSearch(choiceSearch);

        // 6. merge search conditions
        mergeSearchCondition(allSearchAry, eso);

    }

    /**
     * @param whereKeyJo Permission condition
     */
    private static void addDefaultPhyWareHouse(JSONObject whereKeyJo) {
        if (whereKeyJo.containsKey("CP_C_PHY_WAREHOUSE_ID")) {
            JSONArray wareHouse = whereKeyJo.getJSONArray("CP_C_PHY_WAREHOUSE_ID");
            if (wareHouse.size() > 0) {
                wareHouse.add(0);
            }
        }
    }

    private static void dealSortCondition(JSONArray sortJay, JSONArray sortKey) {

        if (sortJay == null || sortJay.size() < 1) {
            JSONObject sortJsn = new JSONObject();
            sortJsn.put("asc", false);
            sortJsn.put("name", "ORDER_DATE");
            sortKey.add(sortJsn);
            return;
        }

        for (int i = 0; i < sortJay.size(); i++) {
            JSONObject sortJsn = sortJay.getJSONObject(i);
            if (sortJsn == null) {
                continue;
            }
            String key = sortJsn.getString("key");
            if (StringUtils.isBlank(key)) {
                continue;
            }
            boolean value = sortJsn.getBooleanValue("value");
            JSONObject jsn = new JSONObject();
            jsn.put("name", key);
            jsn.put("asc", value);
            sortKey.add(jsn);
        }

    }

    /**
     * 智能搜索: 标签
     *
     * @param oA  tagArrays标签集合
     * @param eso OrderQueryRequest
     */
    private static void sqlIntelliSearchTag(JSONArray oA, OrderQueryRequest eso) {

        if (oA != null) {
            int n = oA.size();
            for (int i = 0; i < n; i++) {
                JSONObject o = oA.getJSONObject(i);
                if (o == null) {
                    continue;
                }
                String key = o.getString("key");
                if ("CP_C_LABEL_ID".equalsIgnoreCase(key)) {
                    eso.getWhere().put("CP_C_LABEL_ENAME", "*");
                    continue;
                }

                if (OcOrderTagEum.TAG_CHANGE.getKey().equals(key)) {
                    eso.getChild().put("IS_EXCHANGE_ITEM", OmsIsExchangeItemEnum.YES.integer());
                    continue;
                }

                eso.getWhere().put(key, o.getString("val"));
            }
        }
    }

    /**
     * 智能搜索: 下拉多选
     * 1. 当前: 状态,
     *
     * @param selectBox 下拉多选值集合
     * @param queryName 下拉,查询字段
     * @param es        es
     */
    private static void intelliControlHandler(JSONArray selectBox, String queryName, JSONObject es) {
        if (selectBox == null || selectBox.size() < 1) {
            return;
        }
        int n = selectBox.size();
        JSONArray statusAry = new JSONArray();
        JSONArray platStatusAry = new JSONArray();
        for (int i = 0; i < n; i++) {
            JSONObject o = selectBox.getJSONObject(i);
            if (o != null) {
                if (statusKey.equalsIgnoreCase(queryName)) {
                    statusAry.add(o.getString("value"));
                }
                if (platStatusKey.equalsIgnoreCase(queryName)) {
                    platStatusAry.add(o.getString("value"));
                    es.put(platStatusKey, platStatusAry);
                }
            }
        }
        es.put(statusKey, statusAry);
    }

    /**
     * 智能搜索. 下拉外键,关联查询
     *
     * @param ary 关联数据
     * @param n   字段
     * @param e   es
     */
    private static void intelliDropDownControl(JSONArray ary, String n, JSONObject e) {

        if (ary == null || ary.size() < OcBOrderConst.IS_STATUS_IY) {
            return;
        }
        if ("CP_C_SHOP_TITLE".equals(n)) {
            n = "CP_C_SHOP_ID";
        } else if ("CP_C_PHY_WAREHOUSE_ENAME".equals(n)) {
            n = "CP_C_PHY_WAREHOUSE_ID";
        } else if ("CP_C_LOGISTICS_ENAME".equals(n)) {
            n = "CP_C_LOGISTICS_ID";
        }
        JSONArray fk = new JSONArray();
        for (int i = 0, l = ary.size(); i < l; i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o != null) {
                fk.add(o.getString("ID"));
            }
        }
        e.put(n, fk);
    }

    /**
     * 高级搜索: 下拉多选
     *
     * @param valString 下拉值集合
     * @param queryName 搜索字段
     * @param es        es
     */
    private static void selectControlHandler(String valString, String queryName, JSONObject es, JSONObject childKeys, JSONObject existKeys, JSONObject notExistKeys) {
        if ((valString == null || valString.trim().length() == 0)) {
            return;
        }
        if (ORDER_SORT.equalsIgnoreCase(queryName)) {
            return;
        }

        //解决es分词索引大写变小写问题（terms）
        valString = valString.toLowerCase();
        //区分TOB订单还是TOC订单
        if (QUERY_BUSINESS_TYPE_ID.equalsIgnoreCase(queryName)) {
            JSONArray array = new JSONArray();
            boolean flag = Boolean.FALSE;
            if (valString.contains("!")) {
                flag = Boolean.TRUE;
                valString = valString.substring(1);
            }
            String[] valAry = valString.split(",");
            for (String v : valAry) {
                if (v == null || v.trim().length() < 1) {
                    continue;
                }
                if (!flag) {
                    array.add(v);
                }
                if (flag) {
                    array.add("!=" + v);
                }
            }
            es.put(QUERY_BUSINESS_TYPE_ID, array);
            return;
        }

        /**不含标签处理 订单标签特殊处理**/
        if ((HAS_NO_TAG.equals(queryName) || HAS_TAG.equals(queryName)) && SELECT_ALL.equals(valString)) {
            for (OcOrderTagEum ocOrderTagEum : OcOrderTagEum.values()) {
                /**根据value查找标签枚举类**/
                if (HAS_NO_TAG.equals(queryName)) {
                    if (ocOrderTagEum.getKey().equals("IS_CARPOOL")) {
                        notExistKeys.put(ocOrderTagEum.getKey(), ocOrderTagEum.getVal());
                    } else {
                        JSONArray var = new JSONArray();
                        var.add("!=" + ocOrderTagEum.getVal());
                        es.put(ocOrderTagEum.getKey(), var);
                    }
                } else if (HAS_TAG.equals(queryName)) {
                    if (ocOrderTagEum.getKey().equals("IS_CARPOOL")) {
                        existKeys.put(ocOrderTagEum.getKey(), ocOrderTagEum.getVal());
                    } else {
                        es.put(ocOrderTagEum.getKey(), ocOrderTagEum.getVal());
                    }
                }
            }
            return;
        }

        String[] valAry = valString.split(",");
        JSONArray selectAry = new JSONArray();
        for (String v : valAry) {
            if (v == null || v.trim().length() < 1) {
                continue;
            }
            selectAry.add(v);
        }
        /**不含标签处理*/
        if (HAS_NO_TAG.equals(queryName) || HAS_TAG.equals(queryName)) {
            String value;
            for (int i = 0; i < valAry.length; i++) {
                value = valAry[i];
                QueryOrderTagResult queryOrderTagResult = OcOrderTagEum.getQueryOrderTagResultBySort(Integer.valueOf(value));
                /**根据value查找标签枚举类**/
                if (HAS_NO_TAG.equals(queryName)) {
                    if (queryOrderTagResult.getKey().equals("IS_CARPOOL")) {
                        notExistKeys.put(queryOrderTagResult.getKey(), queryOrderTagResult.getVal());
                    } else {
                        JSONArray var = new JSONArray();
                        var.add("!=" + queryOrderTagResult.getVal());
                        es.put(queryOrderTagResult.getKey(), var);
                    }
                } else if (HAS_TAG.equals(queryName)) {
                    if (queryOrderTagResult.getKey().equals("IS_CARPOOL")) {
                        existKeys.put(queryOrderTagResult.getKey(), queryOrderTagResult.getVal());
                    } else {
                        es.put(queryOrderTagResult.getKey(), queryOrderTagResult.getVal());
                    }
                }
            }
            return;
        }

        /**不含实际预下沉状态 选择全部处理**/
        if ((NO_ACTUAL_PRESINK_STATUS.equals(queryName) || ACTUAL_PRESINK_STATUS.equals(queryName)) && SELECT_ALL.equals(valString)) {
            /**根据value查找标签枚举类**/
            JSONArray array = new JSONArray();
            for (String v : valAry) {
                if (v == null || v.trim().length() < 1) {
                    continue;
                }
                if (NO_ACTUAL_PRESINK_STATUS.equals(queryName)) {
                    String val = ActualPresinkEnum.getValByEname(v);
                    array.add("!=" + val);
                } else {
                    array.add("!=" + v);
                }
            }
            es.put(ACTUAL_PRESINK_STATUS, array);
            return;
        }
        /**包含实际预下沉状态 或者不包含处理*/
        if (NO_ACTUAL_PRESINK_STATUS.equals(queryName) || ACTUAL_PRESINK_STATUS.equals(queryName)) {
            JSONArray array = new JSONArray();
            for (String v : valAry) {
                if (NO_ACTUAL_PRESINK_STATUS.equals(queryName)) {
                    String val = ActualPresinkEnum.getValByEname(v);
                    array.add("!=" + val);
                } else if (ACTUAL_PRESINK_STATUS.equals(queryName)) {
                    array.add(v);
                }
            }
            es.put(ACTUAL_PRESINK_STATUS, array);
            return;
        }

        if ("STATUS_PAY_STEP".equalsIgnoreCase(queryName)) {
            String[] split = OcBOrderConst.preSale.split(",");
            if (selectAry.contains("NOT_PRESALE")) {
                if (selectAry.size() == 1) {
                    es.put(queryName, null);
                } else if (selectAry.size() == 4) {

                } else if (selectAry.size() < 4) {
                    JSONArray var = new JSONArray();
                    for (int i = 0; i < split.length; i++) {
                        if (selectAry.contains(split[i])) {
                            continue;
                        }
                        var.add("!=" + split[i]);
                    }
                    if (var.size() > 0) {
                        es.put(queryName, var);
                    }
                }
                return;
            }
        }
        if ("ST_C_CUSTOM_LABEL_ID".equalsIgnoreCase(queryName)) {
            JSONArray select = new JSONArray();
            for (int i = 0; i < selectAry.size(); i++) {
                select.add(selectAry.get(i) + ",");
            }
            es.put(queryName, select);
            return;
        }

        //child
        if ("M_DIM4_ID".equalsIgnoreCase(queryName)) {
//            childKeys.getJSONObject("oc_b_order_item").put(queryName, valString);
            String[] arr = valString.split(",");
            JSONArray jsonArray = new JSONArray();
            jsonArray.addAll(Arrays.asList(arr));
            childKeys.getJSONObject("oc_b_order_item").put(queryName, jsonArray);
            return;
        }

        if ("RESERVE_BIGINT02".equalsIgnoreCase(queryName)) {
//            childKeys.getJSONObject("oc_b_order_item").put(queryName, valString);
            String[] arr = valString.split(",");
            JSONArray jsonArray = new JSONArray();
            jsonArray.addAll(Arrays.asList(arr));
            childKeys.getJSONObject("oc_b_order_item").put(queryName, jsonArray);
            return;
        }

        if ("STICKER_GIFT".equalsIgnoreCase(queryName)) {
//            childKeys.getJSONObject("oc_b_order_item").put(queryName, valString);
            String[] arr = valString.split(",");
            JSONArray jsonArray = new JSONArray();
            jsonArray.addAll(Arrays.asList(arr));
            childKeys.getJSONObject("oc_b_order_item").put(queryName, jsonArray);
            return;
        }

        if ("SALES_CENTER_ID".equalsIgnoreCase(queryName)) {
            // 由前端控制 如果选择的是id的话 则把编码传过来
//            childKeys.getJSONObject("oc_b_order_item_ext").put("SALES_DEPARTMENT_CODE", valString.toUpperCase());
            String[] arr = valString.toUpperCase().split(",");
            JSONArray jsonArray = new JSONArray();
            jsonArray.addAll(Arrays.asList(arr));
//            childKeys.getJSONObject("oc_b_order_item_ext").put("SALES_DEPARTMENT_CODE", jsonArray);
            childKeys.getJSONObject("oc_b_order_item_ext").put("SALES_DEPARTMENT_CODE.keyword", jsonArray);
            return;
        }

        if ("DIST_CENTER_ID".equalsIgnoreCase(queryName)) {
            // 由前端控制 如果选择的是id的话 则把编码传过来
            childKeys.getJSONObject("oc_b_order_item_ext").put("DIST_CODE_LEVEL_TWO", valString.toUpperCase());
            return;
        }

        es.put(queryName, selectAry);
    }

    /**
     * 高级搜索: 时间控件
     *
     * @param dateStr   时间字符串
     * @param queryName 搜索字段
     * @param es        es
     */
    private static void timeControlHandler(String dateStr, String queryName, JSONObject es, JSONObject childKeys) {

        final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (dateStr == null || dateStr.trim().length() < 1) {
            return;
        }

        String esDateStr = null;
        String[] sAry = dateStr.split("~");
        // 严格格式
        if (sAry[0] == null || sAry[1] == null || sAry[0].length() < OcBOrderConst.DATE_MIN_LENGTH
                || sAry[1].length() < OcBOrderConst.DATE_MIN_LENGTH) {
            return;
        }
        try {
            long startDt = sdf.parse(sAry[0]).getTime();
            long endDt = sdf.parse(sAry[1]).getTime();
            esDateStr = startDt + "~" + endDt;
        } catch (ParseException e) {
            log.error(LogUtil.format("高级搜索时间控件: {}"), Throwables.getStackTraceAsString(e));
        }

        if (esDateStr != null && esDateStr.length() > 0) {
            if (OcBOrderConst.OCB_ORDER_ITEM_ESTIMATE_CON_TIME.equals(queryName)) {
                childKeys.getJSONObject("oc_b_order_item").put(queryName, esDateStr);
                return;
            }
            es.put(queryName, esDateStr);
        }
    }

    private static final Consumer<JSONObject> handleAmbiguousSearch = jsn -> {
        jsn.clear();
        jsn.put("ID", -1);
    };

    /**
     * 高级搜索: 文本控件.批量查询
     *
     * @param c  split c
     * @param n  key
     * @param v  value
     * @param es whereKey
     */
    private static void dealBatchQuery(String c, String n, String v, JSONObject es) {

        JSONArray cnJay = new JSONArray();
        String[] cnAry = v.split(c);
        for (String value : cnAry) {
            if (value == null) {
                continue;
            }
            String s = value.trim();
            if (s.length() < 1) {
                continue;
            }
            cnJay.add(s);
        }

        int num = cnJay.size();
        if (num > 0) {
            boolean isMerge = OcBOrderConst.OCB_ORDER_LIST_BATCH_INCLUDEKEYS_WILDCARD.equals(n);
            if (isMerge && num < 2000) {
                JSONArray scAry = new JSONArray();
                for (Object e : cnJay) {
                    scAry.add("*" + e + "*");
                }
                es.put(n, scAry);
                return;
            }
            es.put(n, cnJay);
        }
    }

    /**
     * 高级搜索: 文本控件_区间查询处理
     *
     * @param v 区间字符串
     * @return 后拼接
     */
    private static String splitAndJoinHighText(String v) {

        String[] ary = v.split("~");
        if (ary.length == 2) {
            if (StringUtils.isNotBlank(ary[0])) {
                BigDecimal v0 = new BigDecimal(ary[0]);
                BigDecimal v1 = new BigDecimal(ary[1]);
                if (v0.compareTo(v1) > 0) {
                    return ary[1] + "~" + ary[0];
                }
            }
        }
        return v;
    }


    /**
     * merge tab button status
     *
     * @param statusJo   button where key
     * @param whereKeyJo whereKeyJo
     */
    private static void postButtonStatus(JSONObject statusJo, JSONObject whereKeyJo) {

        if (statusJo == null) {
            return;
        }
        //扩展标签 已换货完成 待换货
        boolean isExtendLabel = OrderExtendLabelEnum.LABEL102.getVal().equals(statusJo.getString("value"))
                || OrderExtendLabelEnum.LABEL103.getVal().equals(statusJo.getString("value"));
        String statusStr = statusJo.getString("value");
        //扩展标签不需要处理状态
        if (isExtendLabel) {
            return;
        }
        JSONArray tmpStatusList = whereKeyJo.getJSONArray(statusKey);
        JSONArray reSetJa = new JSONArray();
        statusLbl:
        if (null != tmpStatusList && StringUtils.isNotBlank(statusStr)) {
            // intelli select was none, use the tab status value
            if (tmpStatusList.size() < 1) {
                splitComposeStatus(statusStr, reSetJa);
                whereKeyJo.put(statusKey, reSetJa);
                break statusLbl;
            }
            // tab has all status
            if (OcBOrderConst.STATUS_TAB_ALL.equals(statusStr)) {
                if (tmpStatusList.contains(OcBOrderConst.STATUS_TAB_ALL)) {
                    reSetJa.add(OcBOrderConst.STATUS_TAB_ALL);
                    whereKeyJo.put(statusKey, reSetJa);
                } else {
                    whereKeyJo.put(statusKey, tmpStatusList);
                }
            } else {
                // deal Combined tab status
                if (statusStr.contains(OcBOrderConst.ORDER_COMMA)) {
                    splitComposeStatus(statusStr, reSetJa);
                    dealComposeStatus(tmpStatusList, reSetJa, whereKeyJo);
                } else if (tmpStatusList.contains(statusStr)
                        || (tmpStatusList.contains(OcBOrderConst.STATUS_TAB_ALL))) {
                    reSetJa.add(statusStr);
                    whereKeyJo.put(statusKey, reSetJa);
                } else {
                    reSetJa.add(OcBOrderConst.ORDER_STATUS_NONE);
                    whereKeyJo.put(statusKey, reSetJa);
                }
            }
        } else if (tmpStatusList == null && StringUtils.isNotBlank(statusStr)) {
            // intelli select was none, tab has value
            splitComposeStatus(statusStr, reSetJa);
            whereKeyJo.put(statusKey, reSetJa);
        }

        // judge status whether has all status
        JSONArray ary = whereKeyJo.getJSONArray(statusKey);
        if (ary != null) {
            int len = ary.size();
            for (int i = 0; i < len; i++) {
                if (ary.getInteger(i) != null && ary.getIntValue(i) == 0) {
                    whereKeyJo.remove(statusKey);
                    break;
                }
            }
        }
    }

    /**
     * split Combined button status
     *
     * @param stu 状态
     * @param ary jsonArray
     */
    private static void splitComposeStatus(String stu, JSONArray ary) {

        if (stu.contains(OcBOrderConst.ORDER_COMMA)) {
            String[] stuAry = stu.split(OcBOrderConst.ORDER_COMMA);
            for (String s : stuAry) {
                if (StringUtils.isNotBlank(s)) {
                    String realStatus = s;
                    //平台发货失败页签
                    if (OrderExtendLabelEnum.LABEL101.getVal().equals(s)) {
                        realStatus = OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().toString();
                    }
                    ary.add(realStatus);
                }
            }
        } else {
            if (OrderExtendLabelEnum.LABEL101.getVal().equals(stu)) {
                stu = OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().toString();
            }
            ary.add(stu);
        }
    }

    /**
     * 处理组合性tab状态
     *
     * @param tmpStatusList 条件状态
     * @param reSetJa       tab状态
     * @param whereKeyJo    ES
     */
    private static void dealComposeStatus(JSONArray tmpStatusList, JSONArray reSetJa, JSONObject whereKeyJo) {

        if (tmpStatusList.contains(OcBOrderConst.IS_STATUS_SN)) {
            whereKeyJo.put(statusKey, reSetJa);
        } else {
            JSONArray ary = new JSONArray();
            for (int i = 0; i < reSetJa.size(); i++) {
                String stu = reSetJa.getString(i);
                if (tmpStatusList.contains(stu)) {
                    ary.add(stu);
                }
            }
            if (ary.size() > 0) {
                whereKeyJo.put(statusKey, ary);
            } else {
                handleAmbiguousSearch.accept(whereKeyJo);
            }
        }
    }

    /**
     * 一商特有业务查询
     *
     * @param whereKey search conditions
     */
    private static void afterArrangeEsHandler(JSONObject whereKey) {


        final String buyerMsg = "BUYER_MESSAGE";
        final String buyerMsg02 = "BUYER_MESSAGE_02";
        final String sellerMemo = "SELLER_MEMO";
        final String sellerMemo02 = "SELLER_MEMO_02";

        dealYiShDoubleSearchKey(whereKey, buyerMsg, buyerMsg02);
        dealYiShDoubleSearchKey(whereKey, sellerMemo, sellerMemo02);

        final String orderTag = "ORDER_TAG";
        if (whereKey.containsKey(orderTag)) {
            JSONArray tagAry = whereKey.getJSONArray(orderTag);
            for (Object o : tagAry) {
                String[] kv = String.valueOf(o).split("=");
                if (kv.length < 2) {
                    continue;
                }
                if ("CP_C_LABEL_ID".equalsIgnoreCase(kv[0])) {
                    whereKey.put("CP_C_LABEL_ENAME", "*");
                    continue;
                }
                if (whereKey.containsKey(kv[0])) {
                    JSONArray jsnAry = new JSONArray();
                    jsnAry.add(kv[1]);
                    Object o1 = whereKey.get(kv[0]);
                    jsnAry.add(o1);
                    whereKey.put(kv[0], jsnAry);
                    continue;
                }
                whereKey.put(kv[0], kv[1]);
            }
            whereKey.remove(orderTag);
        }
    }

    /**
     * deal double key search
     *
     * @param whereKey   search conditions
     * @param buyerMsg   origin key
     * @param buyerMsg02 repeat key
     */
    private static void dealYiShDoubleSearchKey(JSONObject whereKey, String buyerMsg, String buyerMsg02) {

        final String nullStatus = "1";
        final String notNullStatus = "2";

        buyMsg:
        if (whereKey.containsKey(buyerMsg02)) {
            JSONArray buyMsg = whereKey.getJSONArray(buyerMsg02);
            whereKey.remove(buyerMsg02);
            if (buyMsg.contains("0") || (buyMsg.contains(nullStatus) && buyMsg.contains(notNullStatus))) {
                break buyMsg;
            }
            if (buyMsg.contains(nullStatus)) {
                String buys = whereKey.getString(buyerMsg);
                if (StringUtils.isNotBlank(buys)) {
                    handleAmbiguousSearch.accept(whereKey);
                    break buyMsg;
                }
                whereKey.put(buyerMsg, null);
            }
            if (buyMsg.contains(notNullStatus)) {
                String buys = whereKey.getString(buyerMsg);
                if (StringUtils.isNotBlank(buys)) {
                    break buyMsg;
                }
                whereKey.put(buyerMsg, "*");
            }
        }
    }

    /**
     * deal relations each element
     * split, arrange choice search
     *
     * @param choiceSearch 选择搜索
     */
    private static JSONArray dealChoiceSearch(JSONArray choiceSearch) {
        if (choiceSearch == null) {
            return null;
        }
        // all
        JSONArray wholeAry = new JSONArray();
        // cell
        JSONObject cellJsn = null;
        JSONArray parentJsnAry = null;
        JSONArray childJsnAry = null;
        String nextRlt = "AND";
        for (int i = 0, l = choiceSearch.size(); i < l; i++) {

            JSONObject jsn = choiceSearch.getJSONObject(i);
            if (jsn == null) {
                continue;
            }
            //    String type = jsn.getString("type");
            String value = jsn.getString("value");
            if (StringUtils.isBlank(value)) {
                continue;
            }
            String queryName = jsn.getString("queryName");
            int tableLevel = jsn.getIntValue("tableLevel");
            String sign = jsn.getString("sign");
            String rlt = nextRlt;
            nextRlt = jsn.getString("rlt");

            if ("OR".equalsIgnoreCase(rlt)) {
                cellJsn = new JSONObject();
                wholeAry.add(cellJsn);
                if (tableLevel > 0) {
                    childJsnAry = new JSONArray();
                    dealAndAssignByCalcSign(queryName, value, sign, childJsnAry);
                    cellJsn.put("childKey", childJsnAry);
                    continue;
                }
                parentJsnAry = new JSONArray();
                dealAndAssignByCalcSign(queryName, value, sign, parentJsnAry);
                cellJsn.put("parentKey", parentJsnAry);
                continue;
            }
            if (cellJsn == null) {
                cellJsn = new JSONObject();
                wholeAry.add(cellJsn);
            }
            if (tableLevel > 0) {
                if (childJsnAry == null) {
                    childJsnAry = new JSONArray();
                    dealAndAssignByCalcSign(queryName, value, sign, childJsnAry);
                    cellJsn.put("childKey", childJsnAry);
                    continue;
                }
                dealAndAssignByCalcSign(queryName, value, sign, childJsnAry);
                continue;
            }

            if (parentJsnAry == null) {
                parentJsnAry = new JSONArray();
                dealAndAssignByCalcSign(queryName, value, sign, parentJsnAry);
                cellJsn.put("parentKey", parentJsnAry);
                continue;
            }
            dealAndAssignByCalcSign(queryName, value, sign, parentJsnAry);
        }
        return wholeAry;

    }

    /**
     * @param queryName 查询文本框名称
     * @param value     文本值
     * @param sign      标识
     * @param jsnAry    .
     */
    private static void dealAndAssignByCalcSign(String queryName, String value, String sign, JSONArray jsnAry) {

        JSONObject jsn = new JSONObject();
        if ("ic".equalsIgnoreCase(sign)) {
            jsn.put(queryName, "*" + value + "*");
        } else if ("ec".equalsIgnoreCase(sign)) {
            jsn.put(queryName, "!*" + value + "*");
        } else {
            jsn.put(queryName, value);
        }
        jsnAry.add(jsn);

    }

    /**
     * 高级搜索: 文本控件
     *
     * @param v         文本值
     * @param n         搜索字段
     * @param fKey      搜索字段
     * @param childKeys 搜索字段
     * @param es        es
     */
    private static void textControlHandler(String v, String n, JSONObject es, JSONObject fKey, JSONObject childKeys, Boolean useKeyword) {
        if (StringUtils.isBlank(v)) {
            return;
        }
        v = v.trim();
        if ("~".equals(v)) {
            return;
        }
        // 1. batch
        if (v.contains(OcBOrderConst.ORDER_COMMA)) {
            if (OcBOrderConst.OCB_ORDER_LIST_BATCH_INCLUDEKEYS.contains(n)) {
                dealBatchQuery(OcBOrderConst.ORDER_COMMA, n, v, es);
                return;
            }
        }
        if (v.contains(OcBOrderConst.ORDER_COMMA_CN)) {
            if (OcBOrderConst.OCB_ORDER_LIST_BATCH_INCLUDEKEYS.contains(n)) {
                dealBatchQuery(OcBOrderConst.ORDER_COMMA_CN, n, v, es);
                return;
            }
        }
        if (v.contains(OcBOrderConst.ORDER_COMMA_BLANK)) {
            if (OcBOrderConst.OCB_ORDER_LIST_BATCH_INCLUDEKEYS.contains(n)) {
                dealBatchQuery(OcBOrderConst.ORDER_COMMA_BLANK, n, v, es);
                return;
            }
        }
        // 2. long
        if ("ID".equals(n)) {
            es.put(n, v);
            return;
        }

        if ("CURRENT_CYCLE_NUMBER".equals(n)) {
            es.put(n, v);
            return;
        }

        /*字段类型导致有【-】会搜索失败*/
        if (Boolean.TRUE.equals(useKeyword)
                && StringUtils.equalsIgnoreCase("ORDER_SOURCE_PLATFORM_ECODE", n)) {
            n = "ORDER_SOURCE_PLATFORM_ECODE.keyword";
        }
        if (StringUtils.equalsIgnoreCase("CARPOOL_NO", n)) {
            n = "CARPOOL_NO.keyword";
        }

        // 3. like
       /* if ("SYSREMARK".equals(n) || "BUYER_MESSAGE".equals(n) || "RECEIVER_ADDRESS".equals(n)) {
            es.put(n, "*" + v + "*");
            return true;
        }*/

        // 4. range
        if (OcBOrderConst.OCB_ORDER_QTY_ALL.equals(n) || OcBOrderConst.OCB_ORDER_ORDER_AMT.equals(n)
                || "SKU_KIND_QTY".equals(n)) {
            boolean flag = Pattern.matches(OcBOrderConst.OCB_ORDER_NUMBER_REGES, v);
            if (flag) {
                fKey.put(n, splitAndJoinHighText(v));
                return;
            }
            return;
        }
        //超时时间
        if (OcBOrderConst.OCB_ORDER_TIME_OUT_RANGE.equals(n) && StringUtils.isNotEmpty(v)) {
            boolean flag = Pattern.matches(OcBOrderConst.OCB_ORDER_NUMBER, v);
            if (flag) {
                int hoursNum = Integer.valueOf(v);
                Date data = DateConversionUtil.plusHours(new Date(), -hoursNum);
                long dataLong = data.getTime();
                fKey.put(OcBOrderConst.OCB_ORDER_PAY_TIME, "0~" + dataLong);
            }
            return;
        }
        /// 5. child
        if (OcBOrderConst.OCB_ORDER_ITEM_PSC_SKUECODE.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_PS_C_PRO_ECODE.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_PT_ECODE.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_PT_NAME.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_RESERVE_VARCHAR01.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_SKU_NUMIID.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_NUM_IID.equals(n)) {
            JSONObject childKey = childKeys.getJSONObject("oc_b_order_item");
            if (v.contains(OcBOrderConst.ORDER_COMMA)) {
                dealBatchQuery(OcBOrderConst.ORDER_COMMA, n, v, childKey);
                return;
            }
            if (v.contains(OcBOrderConst.ORDER_COMMA_CN)) {
                dealBatchQuery(OcBOrderConst.ORDER_COMMA_CN, n, v, childKey);
                return;
            }
            childKey.put(n, "*" + v + "*");
            childKeys.put("oc_b_order_item", childKey);
            return;
        }

        if (OcBOrderConst.SOURCE_CODE.equals(n) || OcBOrderConst.BILL_NO.equals(n) || OcBOrderConst.EXPRESSCODE.equals(n) || OcBOrderConst.RECEIVER_MOBILE.equals(n)) {
            es.put(n, v + "*");
            return;
        }
        // 6. other
        es.put(n, "*" + v + "*");
    }

    /**
     * merge all search conditions
     *
     * @param allSearchAry 所有条件
     * @param eso          查询model
     */
    private static void mergeSearchCondition(JSONArray allSearchAry, OrderQueryRequest eso) {

        if (allSearchAry == null || allSearchAry.size() < 1) {
            if (allSearchAry == null) {
                allSearchAry = new JSONArray();
            }
            JSONObject where = eso.getWhere();
            JSONObject child = eso.getChild();
            boolean primary = where != null && where.size() > 0;
            boolean secondary = child != null && child.size() > 0;
            JSONObject cellJson = new JSONObject();
            JSONObject cellJson2 = new JSONObject();
            boolean isHasMerge = false;
            if (primary) {
                JSONArray jsnAry = new JSONArray();
                jsnAry.add(where);
                cellJson.put("parentKey", jsnAry);
                if (where.containsKey("SOURCE_CODE")) {
                    JSONArray jsnAry2 = new JSONArray();
                    JSONObject secJsn = JSON.parseObject(where.toJSONString());
                    String code = where.getString("SOURCE_CODE");
                    secJsn.remove("SOURCE_CODE");
                    secJsn.put("MERGE_SOURCE_CODE", code);
                    jsnAry2.add(secJsn);
                    cellJson2.put("parentKey", jsnAry2);
                    isHasMerge = true;
                }
            }
            if (secondary) {
                JSONArray jsnAry = new JSONArray();
                jsnAry.add(child);
                cellJson.put("childKey", jsnAry);
                cellJson2.put("childKey", jsnAry);
            }
            allSearchAry.add(cellJson);
            if (isHasMerge) {
                allSearchAry.add(cellJson2);
            }
            eso.setWhereKeys(allSearchAry);
            return;
        }

        JSONObject where = eso.getWhere();
        JSONObject child = eso.getChild();
        JSONObject wholeJsn = allSearchAry.getJSONObject(0);
        JSONArray parentKeys = wholeJsn.getJSONArray("parentKey");
        JSONArray childKeys = wholeJsn.getJSONArray("childKey");

        if (where != null && where.size() > 0) {
            if (parentKeys == null) {
                parentKeys = new JSONArray();
            }
            parentKeys.add(where);
            wholeJsn.put("parentKey", parentKeys);
        }

        if (child != null && child.size() > 0) {
            if (childKeys == null) {
                childKeys = new JSONArray();
            }
            childKeys.add(child);
            wholeJsn.put("childKey", childKeys);
        }
        eso.setWhereKeys(allSearchAry);
    }

    /**
     * 封装ToB ToC查询参数
     *
     * @param businessTypeIdList
     * @param jsnObj
     */
    public static void getToBOrToCParam(List<Long> businessTypeIdList, JSONObject jsnObj) {

        //业务类型id为空则不进行过滤
        if (CollectionUtils.isEmpty(businessTypeIdList)) {
            return;
        }

        final String HIGH_SEARCH = "highSearch";
        final String QUERY_NAME = "queryName";
        final String VALUE = "value";
        final String TYPE = "type";
        final String TYPE_SELECT = "Select";

        JSONArray jsonArray = jsnObj.getJSONArray(HIGH_SEARCH);

        Boolean toBFlag = Boolean.FALSE;
        String toBOrToCSign = null;

        for (int i = 0; i < jsonArray.size(); i++) {

            JSONObject tmpJo = jsonArray.getJSONObject(i);

            String type = tmpJo.getString(TYPE);
            String queryName = tmpJo.getString(QUERY_NAME);
            String value = tmpJo.getString(VALUE);

            //如果TOB标识不为空则封装TOB业务类型查询参数
            toBFlag = TYPE_SELECT.equals(type) && QUERY_BUSINESS_TYPE_ID_SIGN.equalsIgnoreCase(queryName)
                    && StringUtils.isNotBlank(value);

            if (toBFlag) {
                toBOrToCSign = value;
                break;
            }
        }

        //如果标识不为空则删除业务类型对象重新封装业务类型对象 赋值TOB或TOC所有业务类型标识
        if (toBFlag) {
            Iterator<Object> o = jsonArray.iterator();
            while (o.hasNext()) {
                JSONObject jo = (JSONObject) o.next();
                if (QUERY_BUSINESS_TYPE_ID.equals(jo.getString(QUERY_NAME))) {
                    o.remove();
                }
            }

            JSONObject businessObject = new JSONObject();
            businessObject.put(TYPE, TYPE_SELECT);
            businessObject.put(QUERY_NAME, QUERY_BUSINESS_TYPE_ID);

            String businessStr = StringUtils.join(businessTypeIdList, ",");

            businessObject.put(VALUE, QUERY_BUSINESS_TYPE_ID_TOB_SIGN.equalsIgnoreCase(toBOrToCSign)
                    ? businessStr
                    : String.format("%s%s", "!", businessStr));

            jsonArray.add(businessObject);
        }

        Iterator<Object> o = jsonArray.iterator();
        while (o.hasNext()) {
            JSONObject jo = (JSONObject) o.next();
            if (QUERY_BUSINESS_TYPE_ID_SIGN.equals(jo.getString(QUERY_NAME))) {
                o.remove();
            }
        }
        jsnObj.put(HIGH_SEARCH, jsonArray);
    }
}
