package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBRefundBatchMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRefundBatch;
import com.jackrain.nea.oc.oms.nums.BatchStatus;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;

/**
 * 退货批次管理作废服务
 *
 * @author: 孙继东
 * @since: 2019-03-26
 * create at : 2019-03-26 11:25
 */
@Slf4j
@Component
public class RefundBatchObsoleteService {
    @Autowired
    private OcBRefundBatchMapper ocBRefundBatchMapper;

    /**
     * 作废服务
     *
     * @param querySession 前端参数
     * @return 返回结果
     */
    public ValueHolder execute(QuerySession querySession) {
        ValueHolder holder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (param != null) {
            JSONArray ids = param.getJSONArray("ids");
            if (!CollectionUtils.isEmpty(ids)) {
                if (ids.size() > 1) {
                    throw new NDSException(Resources.getMessage("不允许批量选择退货批次进行作废"));
                } else if (ids.size() == 1) {
                    OcBRefundBatch refundBatch = ocBRefundBatchMapper.selectById(ids.get(0).toString());
                    if (refundBatch != null) {
                        //已完结(2).已作废(3)，则不允许选择
                        Integer batchStatus = refundBatch.getBatchStatus();
                        if (BatchStatus.BATCHSTATUS_YES.equals(batchStatus)) {
                            throw new NDSException(Resources.getMessage("退货批次已完结，不能作废"));
                        } else if (BatchStatus.BATCHSTATUS_OBSOLETE.equals(batchStatus)) {
                            throw new NDSException(Resources.getMessage("退货批次已作废，不能再次作废"));
                        } else if (BatchStatus.BATCHSTATUS_NO.equals(batchStatus)) {
                            //未完结(1)，则调用退货批次作废服务
                            BigDecimal qty = refundBatch.getQty();
                            //判断退货批次中是否存在退货的商品数量，
                            if (qty == null || qty.compareTo(BigDecimal.ZERO) == 0) {
                                //如果不存在商品数量,则更新退货批次状态为已作废，返回作废成功。
                                refundBatch.setBatchStatus(BatchStatus.BATCHSTATUS_OBSOLETE);
                                ocBRefundBatchMapper.updateById(refundBatch);
                                HashMap map = new HashMap();
                                map.put("objid", ids.get(0));
                                map.put("tablename", "OC_B_REFUND_BATCH");
                                holder.put("data", map);
                                holder.put("code", ResultCode.SUCCESS);
                                holder.put("message", "作废成功");
                            } else {
                                //如果已经有退货的商品数量，则返回提示：“已经存在退货入库明细，不允许作废”；
                                throw new NDSException(Resources.getMessage("已经存在退货入库明细，不允许作废"));
                            }
                        }
                    }
                }
            } else {
                throw new NDSException(Resources.getMessage("请选择一条数据后在操作"));
            }

        }
        return holder;
    }
}
