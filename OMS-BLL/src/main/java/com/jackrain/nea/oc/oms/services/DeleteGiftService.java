package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.result.DeleteGiftResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @author: 夏继超
 * @since: 2019/3/11
 * create at : 2019/3/11 14:53
 */
@Slf4j
@Component
//@Transactional
public class DeleteGiftService {
    @Autowired
    OcBOrderMapper ocOrderMapper;
    @Autowired
    OcBOrderItemMapper ocOrderItemMapper;
    @Autowired
    OcBorderUpdateService ocBorderUpdateService;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBorderUpdateServiceExt ocBorderUpdateServiceExt;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OmsOrderService omsOrderService;

    /**
     * 删除商品的方法（删除商品）
     *
     * @param jsonObject 传入的参数
     * @param loginUser  登陆的用户信息
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<DeleteGiftResult> deleteGift1(JSONObject jsonObject, User loginUser) throws NDSException {
        ValueHolderV14<DeleteGiftResult> vh = new ValueHolderV14<>();
        if (jsonObject != null) {
            Integer orderId;
            Object orderId1 = jsonObject.get("orderId");
            Object ids1 = jsonObject.get("ids");
            if (orderId1 != null || ids1 != null) {
                orderId = jsonObject.getInteger("orderId");
                JSONArray ids = jsonObject.getJSONArray("ids");
                List list = new ArrayList<>();
                //订单状态为1待审核、 52缺货,配货中显示字段
                list.add(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
//                list.add(OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal());
                list.add(OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal());
                OcBOrder ocOrder = ocOrderMapper.selectById(orderId);
                if (ocOrder == null) {
                    throw new NDSException(Resources.getMessage("数据参数有误！", loginUser.getLocale()));
                }

                if (list.contains(ocOrder.getOrderStatus())) {
                    if (ids.isEmpty()) {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(Resources.getMessage("请选择需要删除的商品记录！", loginUser.getLocale()));
                        return vh;
                    }
                    //若订单状态为配货中，则判断WMS撤回状态是否为已撤回
//                    if (OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal() == ocOrder.getOrderStatus()) {
//                        if (OcOrderWmsStatus.WMS_SUCCESS.getVal() != ocOrder.getWmsCancelStatus()) {
//                            vh.setCode(ResultCode.FAIL);
//                            vh.setMessage(Resources.getMessage("订单在WMS中未取消，建议先撤回WMS再进行删除商品", loginUser.getLocale()));
//                            return vh;
//                        }
//                    }
                    String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                    RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                    try {
                        if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                            StringBuilder errorString = new StringBuilder();
                            //判断选择的明细的退款状态是退款已完成 ，这提示当前已经退款完成，不允许操作！
                            for (int i = 0; i < ids.size(); i++) {
                                OcBOrderItem ocOrderItem = ocOrderItemMapper.queryOrderItemByIdPro(Long.valueOf(ids.get(i).toString()), Long.valueOf(orderId.toString()));
                                if (Objects.isNull(ocOrderItem)) {
                                    vh.setCode(ResultCode.FAIL);
                                    vh.setMessage(Resources.getMessage("查询明细失败,请稍后重试！", loginUser.getLocale()));
                                    return vh;
                                }
                                if ((long) SkuType.NO_SPLIT_COMBINE == ocOrderItem.getProType()) {
                                    errorString.append(ocOrderItem.getPsCProEcode()).append(", ");
                                }
                                Integer refundStatus = ocOrderItem.getRefundStatus();
                                if (OcOrderRefundStatusEnum.SUCCESS.getVal() == refundStatus) {
                                    throw new NDSException(Resources.getMessage("存在退款完成的明细，请重新选择", loginUser.getLocale()));
                                }
                            }
                            if (StringUtils.isNotEmpty(errorString)) {
                                vh.setCode(ResultCode.FAIL);
                                vh.setMessage(Resources.getMessage("商品款号[ " + errorString.deleteCharAt(errorString.length() - 2) + "]为未拆分的组合商品不可删除!", loginUser.getLocale()));
                                return vh;
                            }
                            //判断单条删除还是多条删除
                            if (ids.size() == 1) {
                                if (deleteOne(loginUser, vh, orderId, ids, ocOrder)) {
                                    return vh;
                                }
                            } else {

                                batchDelete(loginUser, vh, orderId, ids, ocOrder);
                            }
                        } else {
                            throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", loginUser.getLocale()));
                        }
                    } catch (Exception ex) {
                        log.error(LogUtil.format("删除商品服务异常.异常: {}"), Throwables.getStackTraceAsString(ex));
                        throw new NDSException(ex.getMessage());
                    } finally {
                        redisLock.unlock();
                    }
                } else {
                    throw new NDSException(Resources.getMessage("当前订单状态非待审核、缺货，不允许删除！！", loginUser.getLocale()));
                }
            } else {
                throw new NDSException(Resources.getMessage("参数异常！", loginUser.getLocale()));
            }
        } else {
            throw new NDSException(Resources.getMessage("参数异常！", loginUser.getLocale()));
        }
        return vh;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(User loginUser, ValueHolderV14<DeleteGiftResult> vh, Integer orderId, JSONArray ids, OcBOrder ocOrder) {
        int fail = 0;
        BigDecimal sum = new BigDecimal(0);
        //明细表集合
        List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
        //条码字符串合集
        StringBuilder skuString = new StringBuilder();
        // 存在非赠品的明细行点击删除赠品则提示商品非赠品不允许删除
        StringBuilder giftIsZero = new StringBuilder();
        for (int i = 0; i < ids.size(); i++) {
            Object id = ids.get(i);
            OcBOrderItem ocOrderItem = ocOrderItemMapper.queryOrderItemById(Long.valueOf(id.toString()), Long.valueOf(orderId.toString()));
            if (BigDecimal.ZERO.compareTo(ocOrderItem.getPrice()) != 0) {
                continue;
            }
            ocBOrderItems.add(ocOrderItem);
            //统计删除成功失败的条数
            try {
                if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(ocOrderItem.getIsGift())) {
                    int succeess = ocOrderItemMapper.deleteByItemId(Long.valueOf(id.toString()), ocOrderItem.getOcBOrderId());
                    if (ids.size() > 1) {
                        skuString = skuString.append(",").append(ocOrderItem.getPsCSkuEcode());
                    } else {
                        skuString = skuString.append(ocOrderItem.getPsCSkuEcode());
                    }
                    if (succeess == 0) {
                        fail++;
                        continue;
                    }
                } else {
//                    fail++;
                    giftIsZero.append(ocOrderItem.getPsCProEcode()).append(", ");
                    continue;
                }
            } catch (Exception e) {
                fail++;
            }
        }
        if (StringUtils.isNotEmpty(giftIsZero.toString())) {
            throw new NDSException("商品款号[ " + giftIsZero.deleteCharAt(giftIsZero.length() - 2).toString() + " ]商品非赠品不允许删除!");
        }
        //调用俊磊大保存的服务
        try {
            OcBOrderRelation relation = new OcBOrderRelation();
            //添加头表的信息
            relation.setOrderInfo(ocOrder);
            //添加明细的信息
            relation.setOrderItemList(ocBOrderItems);
            ocBorderUpdateServiceExt.updateOrder(relation, loginUser);

            //判断该订单明细是否还有商品
            List<OcBOrderItem> list = ocOrderItemMapper.selectOrderGiftListByOrderId(ocOrder.getId());
            if (CollectionUtils.isEmpty(list)) {
                OcBOrder order = new OcBOrder();
                order.setId(ocOrder.getId());
                order.setIsHasgift(0);
                omsOrderService.updateOrderInfo(order);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("保存服务失败.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(Resources.getMessage("调用保存服务失败", loginUser.getLocale()));
        }

        try {
            //调用订单得日志服务
            omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(), OrderLogTypeEnum.GIFT_DEL.getKey(), skuString + " 删除赠品成功！", null, null, loginUser);
        } catch (Exception e) {
            log.error(LogUtil.format("服务调用异常.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(Resources.getMessage("服务调用异常!", loginUser.getLocale()));
        }
        Integer success = ids.size() - fail;
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(Resources.getMessage("删除成功" + success + "条，删除失败" + fail + "条数据"));
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean deleteOne(User loginUser, ValueHolderV14<DeleteGiftResult> vh, Integer orderId, JSONArray ids, OcBOrder ocOrder) {
        OcBOrderItem ocOrderItem = ocOrderItemMapper.queryOrderItemById(Long.valueOf(ids.get(0).toString()), Long.valueOf(orderId.toString()));
        if (ocOrderItem == null) {
            throw new NDSException(Resources.getMessage("当前记录异常！", loginUser.getLocale()));
        }
        if (!OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(ocOrderItem.getIsGift())) {
            throw new NDSException(Resources.getMessage("商品非赠品不允许删除! ", loginUser.getLocale()));
        }
        if (BigDecimal.ZERO.compareTo(ocOrderItem.getPrice()) != 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("删除商品失败只能删除成交金额为0 的");
            return true;
        }
        //判断是不是ocOrderItem为空
        ocOrderItemMapper.deleteByItemId(Long.valueOf(ids.get(0).toString()), ocOrderItem.getOcBOrderId());
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("删除赠品成功");
        //调用俊磊大保存的服务
        try {
            OcBOrderRelation relation = new OcBOrderRelation();
            //添加头表的信息
            relation.setOrderInfo(ocOrder);
            //添加明细的信息
            List<OcBOrderItem> orderItemList = new ArrayList<>();
            //把删除的条码的传递数量为0
            ocOrderItem.setQty(new BigDecimal(0));
            orderItemList.add(ocOrderItem);
            relation.setOrderItemList(orderItemList);
            ocBorderUpdateServiceExt.updateOrder(relation, loginUser);
            //判断该订单明细是否还有商品
            List<OcBOrderItem> list = ocOrderItemMapper.selectOrderGiftListByOrderId(ocOrder.getId());
            if (CollectionUtils.isEmpty(list)) {
                OcBOrder order = new OcBOrder();
                order.setId(ocOrder.getId());
                order.setIsHasgift(0);
                omsOrderService.updateOrderInfo(order);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("调用保存服务失败.异常: {}"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage(), loginUser.getLocale()));
            return true;
        }
        try {
            //调用订单得日志服务
            omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(), OrderLogTypeEnum.GIFT_DEL.getKey(), ocOrderItem.getPsCSkuEcode() + " 删除赠品成功！", null, null, loginUser);
        } catch (Exception e) {
            log.error(LogUtil.format("服务调用异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(Resources.getMessage("服务调用异常!", loginUser.getLocale()));
        }
        return false;
    }
}
