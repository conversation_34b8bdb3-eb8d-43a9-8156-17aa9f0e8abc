package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.stocksync.common.OmsConstantsIF;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.BillCopyMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;


/**
 * @Author: wangqiang
 * @Date: 2019-03-04 17:36
 * @Version 1.0
 */
@Slf4j
@Component
public class BillCopyService {
    @Autowired
    BillCopyMapper billCopyMapper;
    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    CpRpcService cpRpcService;
    @Autowired
    private IpRpcService ipRpcService;

    /**
     * 订单复制
     *
     * @param obj  参数
     * @param user 用户
     * @return 订单信息
     */
    public ValueHolder billCopy(JSONObject obj, User user) {

        ValueHolder vh = new ValueHolder();
        JSONObject jsonObject = new JSONObject();
        //ids
        String ids = obj.getString("IDS");
        //转成数组
        JSONArray idArray = JSON.parseArray(ids);

        //type:1单据复制 2丢单复制
        Integer type = obj.getInteger("TYPE");
        if (CollectionUtils.isEmpty(idArray)) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("请选择需要复制的记录", user.getLocale()));
            return vh;
        }
        if (idArray.size() > 1) {

            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("只能选择一条记录进行复制", user.getLocale()));
            return vh;
        }
        int iid = NumberUtils.toInt(idArray.get(0).toString());
        //检查ID是否存在
        OcBOrder ocBOrder1 = ocBOrderMapper.selectById(iid);
        if (ObjectUtils.isEmpty(ocBOrder1)) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("数据库不存在该订单Id", user.getLocale()));
            return vh;
        }

        //纯虚拟商品订单不允许复制
        if(null != ocBOrder1.getIsInvented() && OmsConstantsIF.YES == ocBOrder1.getIsInvented()){
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("此为虚拟订单不支持复制", user.getLocale()));
            return vh;
        }

        //类型为1,走单据复制流程
        if (type == 1) {
            //获取基本信息
            JSONObject orderInfoById = billCopyMapper.queryOrderInfoById(iid);
            //复制付款时间
            orderInfoById.put("PAY_TIME", ocBOrder1.getPayTime());
            //复值组合商品标记
            orderInfoById.put("IS_COMBINATION", ocBOrder1.getIsCombination());

            //主表信息
            OcBOrder ocBOrder = JSON.parseObject(orderInfoById.toJSONString(), OcBOrder.class);
            //跟据店铺id 获取店铺信息
            CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
            ocBOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
            // 自动打标：复制订单
            ocBOrder.setIsCopyOrder(1);
            //获取收货人信息
            JSONObject receiverInfoById = billCopyMapper.queryReceiverInfoById(iid);
            //获取备注信息
            JSONObject messageById = billCopyMapper.queryMessageById(iid);
            //旧平台单号
            String oldTid = orderInfoById.getString("SOURCE_CODE");
            //平台单号 可能为空,不是必填
            orderInfoById.put("TID", oldTid);
            orderInfoById.put("SUFFIX_INFO", iid + "-CP");
            // 自动打标：复制订单
            ocBOrder.setIsCopyOrder(1);
            ocBOrder.setIsLoseCopyOrder(0);

            String copyReason = obj.getString("copy_reason");
            ocBOrder.setCopyReason(copyReason);
            jsonObject.put("baseInfo", ocBOrder);
            jsonObject.put("receivingGoods", receiverInfoById);
            jsonObject.put("remarksInfo", messageById);
            /**
             *  参数 新增订单编号：newTid
             *       用户名称: userName
             *       日志类型:"复制订单"
             *       日志内容:"复制订单成功"
             *       日志参数:空
             *       IP地址:本机Ip
             *       错误信息:空
             */


        }
        //类型为2,走丢单复制流程
        if (type == 2) {
            //获取基本信息
            JSONObject orderInfoById = billCopyMapper.queryOrderInfoById(iid);
            //主表信息
            OcBOrder ocBOrder = JSON.parseObject(orderInfoById.toJSONString(), OcBOrder.class);
            if (!PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
                ocBOrder.setExpresscode(null);
            }
            //复制付款时间
            orderInfoById.put("PAY_TIME", ocBOrder1.getPayTime());
            //复值组合商品标记
            orderInfoById.put("IS_COMBINATION", ocBOrder1.getIsCombination());
            //跟据店铺id 获取店铺信息
            CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
            ocBOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
            //获取收货人信息
            JSONObject receiverInfoById = billCopyMapper.queryReceiverInfoById(iid);
            //获取备注信息
            JSONObject messageById = billCopyMapper.queryMessageById(iid);
            //旧平台单号
            String oldTid = orderInfoById.getString("SOURCE_CODE");
            orderInfoById.put("TID", oldTid);
            orderInfoById.put("SUFFIX_INFO", iid + "-LC");
            //订单类型为丢单
            orderInfoById.put("ORDER_TYPE", OrderTypeEnum.LOST.getVal());
            // 自动打标：复制订单
            orderInfoById.put("IS_COPY_ORDER", 0);
            orderInfoById.put("IS_LOSE_COPY_ORDER", 1);
            orderInfoById.put("copy_reason", "丢单复制");

            jsonObject.put("baseInfo", orderInfoById);
            jsonObject.put("receivingGoods", receiverInfoById);
            jsonObject.put("remarksInfo", messageById);

        }
        //新增退单
        if (type == 3) {
            //获取基本信息
            JSONObject orderInfoById = billCopyMapper.queryOrderInfoById(iid);
            //获取收货人信息
            JSONObject receiverInfoById = billCopyMapper.queryReceiverInfoById(iid);
            //获取备注信息
            JSONObject messageById = billCopyMapper.queryMessageById(iid);
            //旧平台单号
            String oldTid = orderInfoById.getString("SOURCE_CODE");
            //平台单号 可能为空,不是必填
            orderInfoById.put("TID", oldTid);
            StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder1.getCpCShopId());
            if(shopStrategy != null){
                ocBOrder1.setReserveBigint05(shopStrategy.getCpCWarehouseDefId());
                ocBOrder1.setReserveVarchar05(shopStrategy.getCpCWarehouseDefName());
            }
            jsonObject.put("baseInfo", ocBOrder1);
            jsonObject.put("receivingGoods", receiverInfoById);
            jsonObject.put("remarksInfo", messageById);
        }
        //解密收货人信息
        decrypt(jsonObject,ocBOrder1);

        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", Resources.getMessage("成功", user.getLocale()));
        vh.put("data", jsonObject);
        return vh;
    }


    private void decrypt(JSONObject data,OcBOrder decryptOrder){
        JSONObject receiverInfo = data.getJSONObject("receivingGoods");

        ipRpcService.decrypt(decryptOrder);
        receiverInfo.put("RECEIVER_NAME",decryptOrder.getReceiverName());
        receiverInfo.put("RECEIVER_MOBILE",decryptOrder.getReceiverMobile());
        receiverInfo.put("RECEIVER_PHONE",decryptOrder.getReceiverPhone());
        receiverInfo.put("RECEIVER_ADDRESS",decryptOrder.getReceiverAddress());

        data.put("receivingGoods",receiverInfo);
    }

}