package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.mapper.IpBJingdongDirectItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBJingdongDirectMapper;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongDirectOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirect;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirectItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/3/28 下午4:39
 * @Version 1.0
 */
@Slf4j
@Component
public class IpJingdongDirectService {

    @Autowired
    private IpBJingdongDirectMapper ipBJingdongDirectMapper;
    @Autowired
    private IpBJingdongDirectItemMapper ipBJingdongDirectItemMapper;


    public IpJingdongDirectOrderRelation selectDirectOrderRelation(String orderNo){
        IpJingdongDirectOrderRelation relation = new IpJingdongDirectOrderRelation();
        IpBJingdongDirect direct = ipBJingdongDirectMapper.selectIpBJingdongDirectByOrderNo(orderNo);
        if (direct != null) {
            List<IpBJingdongDirectItem> ipBJingdongDirectItems = ipBJingdongDirectItemMapper.selectIpBJingdongDirectItemList(direct.getId());
            relation.setIpBJingdongDirect(direct);
            relation.setIpBJingdongDirectItems(ipBJingdongDirectItems);
            return relation;
        }
        return null;
    }


    public int updateIpBJingdongDirectIstrans(String orderNo, TransferOrderStatus status, String transRemark){
        return ipBJingdongDirectMapper.updateIpBJingdongDirectIstrans(status.toInteger(), transRemark, orderNo);
    }
}
