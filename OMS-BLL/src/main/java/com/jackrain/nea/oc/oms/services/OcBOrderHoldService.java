package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderHoldItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.constant.R3ParamConstants;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderDetentionEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.request.OrderHoldRequest;
import com.jackrain.nea.oc.oms.model.request.StCHoldOrderRequest;
import com.jackrain.nea.oc.oms.model.result.OrderDetentionFutureResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderHoldItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.services.oss.OSSConfig;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.vo.ExecuteErrorVO;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.AssertUtils;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.SplitListUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTask;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderReasonMapper;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @author: 江家雷
 * @since: 2020-07-03
 * create at:  2020-07-03 19:12
 */
@Slf4j
@Component
public class OcBOrderHoldService {

    @Autowired
    private OcBOrderHoldItemMapper ocBOrderHoldItemMapper;

    @Autowired
    private OcBOrderHoldItemService ocBOrderHoldItemService;

    @Autowired
    private OmsOrderLogService orderLogService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private QueryOrderListService queryOrderListService;


    @Autowired
    private AsyncTaskManager asyncTaskManager;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    @Autowired
    private AgainOccupyStockService againOccupyStockService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OcBOccupyTaskMapper ocBOccupyTaskMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    SgRpcService sgRpcService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private ThreadPoolTaskExecutor orderHoldThreadPoolExecutor;
    @Autowired
    private ThreadPoolTaskExecutor orderDetentionPollExecutor;

    @Resource
    private ThreadPoolTaskExecutor batchDetentionReleasePollExecutor;

    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    @Autowired
    private OSSConfig ossConfig;

    @Autowired
    private ExportUtil exportUtil;

    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    @Autowired
    private StCHoldOrderReasonMapper stCHoldOrderReasonMapper;

    /***
     * 自动释放hold单
     */
    public void autoUnHoldOrder(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        List<OcBOrderHoldItem> items = this.ocBOrderHoldItemMapper.selectOrderHoldItemList(orderIds);
        this.unHoldOrder(items);
    }

    public void autoUnHoldJitxOrder(Long orderId) {
        if (orderId == null) {
            return;
        }
        this.ocBOrderHoldItemService.cancelHoldJitxOrder(orderId, OrderHoldReasonEnum.JITX_FORBIDDEN_DELIVERY.getMessage());
    }


    /***
     * 订单自动hold单操作
     * @param orderInfo
     * @param operateUser
     */
    @Transactional(propagation = Propagation.REQUIRED)
    public void autoHoldOrder(OcBOrderRelation orderInfo, User operateUser) {
        this.ocBOrderHoldItemService.autoHoldOrder(orderInfo, operateUser);
    }

    /***
     *  手工HOLD单
     */
    public ValueHolder manualHoldOrder(OrderHoldRequest request, User operateUser) {

        if (CollectionUtils.isEmpty(request.getIds())) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", operateUser.getLocale()));
        }
        ValueHolder vh = new ValueHolder();
        JSONArray errorMessage = new JSONArray();
        // 构建策略
        List<StCHoldOrderRequest> stCHoldOrderList = new ArrayList<>();
        StCHoldOrderRequest stCHoldOrder = new StCHoldOrderRequest();
        BeanUtils.copyProperties(request, stCHoldOrder);
        stCHoldOrderList.add(stCHoldOrder);
        int fail = 0;
        for (Long orderId : request.getIds()) {
            JSONObject jsonObject = new JSONObject();
            try {
                vh = this.ocBOrderHoldItemService.manualHoldOrder(orderId, stCHoldOrderList, operateUser);
            } catch (Exception e) {
                log.error(LogUtil.format("订单HOLDS失败,失败原因={},orderId=", orderId), Throwables.getStackTraceAsString(e));
                jsonObject.put("code", -1);
                jsonObject.put("message", e.getMessage());
                jsonObject.put("objid", orderId);
                errorMessage.add(jsonObject);
                fail++;
                vh.put("code", ResultCode.FAIL);
                vh.put("message", e.getMessage());
                String message = e.getMessage();
                if (e.getMessage() != null && message.length() > 1000) {
                    message = message.substring(0, 1000);
                }
                insertOrderLog(orderId, null, OrderLogTypeEnum.ORDER_HOLD.getKey(), message, null, null, operateUser);
            }
        }
        vh.put("data", errorMessage);
        if (request.getIds().size() == 1) {
            return vh;
        }
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("订单HOLD单成功", operateUser.getLocale()));
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("订单HOLD单成功" + (request.getIds().size() - fail) + "条，失败了" + fail + "条", operateUser.getLocale()));
        }
        return vh;
    }

    /***
     * 手工释放hold单
     */
    @Transactional
    public ValueHolder manualUnHoldOrder(JSONObject obj, User operateUser) {
        if (log.isDebugEnabled()){
            log.debug("manualUnHoldOrder.param{}",obj);
        }
        List<Long> longList = JSON.parseArray(obj.getJSONArray("ids").toJSONString(),Long.class);
        if (longList == null || longList.size() == 0) {
            log.info("手工批量释放hold单查询es");
            //查询全部条件 es查询
            //throw new NDSException(Resources.getMessage("请求参数不能为空!", operateUser.getLocale()));
            String param = obj.getString("param");
            if (Objects.isNull(param)){
                throw new NDSException(Resources.getMessage("请求参数不能为空!", operateUser.getLocale()));
            }
            longList = getEsids(param,operateUser);

        }
        //释放原因
        String holdReleaseReason = obj.getString("holdReleaseReason");

        int failed = 0;
        ValueHolder vh = new ValueHolder();
        JSONArray errorMessage = new JSONArray();
        try {
            if (CollectionUtils.isNotEmpty(longList)) {
                PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
                //分5个线程处理
                int splitNum = config.getProperty("lts.order.manual.UnHoldOrder.split.num", 10);
                int tmpNum = longList.size() / splitNum;//每个小list分的个数
                if (longList.size() % splitNum != 0) {
                    tmpNum = tmpNum + 1;
                }
                List<List<Long>> splitList = SplitListUtil.partition(longList, tmpNum);
                List<Future<List<JSONObject>>> results =
                        new ArrayList<Future<List<JSONObject>>>();
                if (CollectionUtils.isNotEmpty(splitList)) {
                    log.info("manualUnHoldOrder.分组数据为{}条",splitList.size());
                    //线程提交
                    for (int i = 0; i < splitList.size(); i++) {
                        results.add(orderHoldThreadPoolExecutor.submit(new OcBOrderHoldService.CallableTaskWithResult(splitList.get(i), operateUser, holdReleaseReason)));
                    }
                    //线程执行结果获取
                    for (Future<List<JSONObject>> futureResult : results) {
                        if (log.isDebugEnabled()) {
                            log.debug("manualUnHoldOrder------>线程结果:{}", JSON.toJSONString(futureResult));
                        }
                        List<JSONObject> callableMsgList =futureResult.get();
                        for (JSONObject jsonObject : callableMsgList) {
                            if (Objects.nonNull(jsonObject)){
                                errorMessage.add(jsonObject);
                                failed++;
                            }
                        }
                    }
                }
            }else {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", Resources.getMessage("未查询出数据或es超出最大条数", operateUser.getLocale()));
                return vh;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("{} Thread manualUnHoldOrder InterruptedException：{}", "R3_OMS_CREATE_MANUAL_UNHOLD_ORDER_THREAD_POOL_", e);
        } catch (ExecutionException e) {
            log.warn("{} Thread manualUnHoldOrder  ExecutionException：{}", "R3_OMS_CREATE_MANUAL_UNHOLD_ORDER_THREAD_POOL_", e);
        }
        vh.put("data", errorMessage);
        if (failed == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("取消HOLD单成功", operateUser.getLocale()));
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("取消HOLD单成功" + (longList.size() - failed) + "条，失败了" + failed + "条", operateUser.getLocale()));
        }
        return vh;
    }
    /**
     * description:按条件查询ids
     * @Author:  liuwenjin
     * @Date 2021/12/13 2:38 下午
     */
    public List<Long>  getEsids(String param, User operateUser) {
        JSONObject esJsn =queryOrderListService.querySelectList(operateUser,param);
        if (esJsn == null) {
            return null;
        }
        List<Long> list = new ArrayList<>();
        JSONArray ary = esJsn.getJSONArray("data");
        if (ary == null) {
            return null;
        }
        for (int i = 0; i < ary.size(); i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            Long id = o.getLong("ID");
            if (id == null) {
                continue;
            }
            list.add(id);
        }
        return list;
    }

    public ValueHolder holdOrUnHoldOrder(OcBOrder ocBOrder, OrderHoldReasonEnum holdReason) {
        ValueHolder vh = new ValueHolder();
        if (ocBOrder.getIsInterecept() != null && ocBOrder.getIsInterecept() == 1) {
            vh = businessHold(ocBOrder.getId(), holdReason);
        } else if (ocBOrder.getIsInterecept() != null && ocBOrder.getIsInterecept() == 0) {
            vh = businessUnHold(ocBOrder.getId(), holdReason);
        }
        return vh;
    }

    // 流程HOLD单 退款HOLD单/JITX HOLD单
    public ValueHolder businessHold(Long orderId, OrderHoldReasonEnum holdReason) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderHoldService.businessHold start hold单场景={},orderId=", orderId),
                    holdReason.getMessage());
        }
        ValueHolder vh = new ValueHolder();
        try {
            this.ocBOrderHoldItemService.businessHold(orderId, holdReason);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", OrderHoldReasonEnum.getMessageByKey(holdReason.getKey()));
        } catch (Exception e) {
            log.error(LogUtil.format(OrderHoldReasonEnum.getMessageByKey(holdReason.getKey()) + ",exception = {},orderId=",
                    orderId), Throwables.getStackTraceAsString(e));
            vh.put("code", ResultCode.FAIL);
            vh.put("message", e.getMessage());
        }
        return vh;
    }


    // 流程HOLD单 退款HOLD单/JITX HOLD单
    public ValueHolder businessHold(Long orderId, OrderHoldReasonEnum holdReason, Date releaseTime) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderHoldService.businessHold start hold单场景={},orderId=", orderId),
                    holdReason.getMessage());
        }
        ValueHolder vh = new ValueHolder();
        try {
            this.ocBOrderHoldItemService.businessHold(orderId, holdReason, releaseTime, null);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", OrderHoldReasonEnum.getMessageByKey(holdReason.getKey()));
        } catch (Exception e) {
            log.error(LogUtil.format(OrderHoldReasonEnum.getMessageByKey(holdReason.getKey()) + ",exception = {},orderId=",
                    orderId), Throwables.getStackTraceAsString(e));
            vh.put("code", ResultCode.FAIL);
            vh.put("message", e.getMessage());
        }
        return vh;
    }

    // 流程HOLD单 退款取消HOLD单/JITX 取消HOLD单
    public ValueHolder businessUnHold(Long orderId, OrderHoldReasonEnum holdReason) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderHoldService.businessUnHold start ,释放hold单场景={},orderId=", orderId),
                    holdReason.getMessage());
        }
        ValueHolder vh = new ValueHolder();
        try {
            this.ocBOrderHoldItemService.businessUnHold(orderId, holdReason);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", OrderHoldReasonEnum.getMessageByKey(holdReason.getKey()));
        } catch (Exception e) {
            log.error(LogUtil.format(OrderHoldReasonEnum.getMessageByKey(holdReason.getKey()) + " ,exception = {},orderId=",
                    orderId), Throwables.getStackTraceAsString(e));
            vh.put("code", ResultCode.FAIL);
            vh.put("message", e.getMessage());
        }
        return vh;
    }

    private void unHoldOrder(List<OcBOrderHoldItem> items) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderHoldService.autoUnHoldOrder,param{}"), JSONObject.toJSONString(items));
        }
        if (CollectionUtils.isNotEmpty(items)) {
            Map<Long, List<OcBOrderHoldItem>> map = items.stream().collect(Collectors.groupingBy(OcBOrderHoldItem::getOcBOrderId));
            // 修改HOLD单的策略更新时间,让未到释放时间的记录放到后面去
            List<Long> ids = items.stream().map(OcBOrderHoldItem::getOcBOrderId).distinct().collect(Collectors.toList());
            ocBOrderHoldItemMapper.batchUpdateModifieddateByIds(ids);
            int fail = 0;
            for (Long key : map.keySet()) {
                try {
                    this.ocBOrderHoldItemService.autoCancelHoldOrder(key, map.get(key));
                } catch (Exception e) {
                    log.error(LogUtil.format("自动释放订单异常：{}"), Throwables.getStackTraceAsString(e));
                    fail++;
                }
            }
            if (log.isInfoEnabled()) {
                log.info(LogUtil.format(Resources.getMessage("自动释放HOLD单成功" + (map.size() - fail) + "条，失败了" + fail +
                        "条")));
            }
        }
    }

    // 插入订单日志
    public void insertOrderLog(long orderId, String billNo, String logType, String logMessage, String param,
                               String errorMessage, User operateUser) {
        //调用添加订单日志
        try {
            orderLogService.addUserOrderLog(orderId, billNo, logType, logMessage, null, null, operateUser);
        } catch (Exception e) {
            log.error(LogUtil.format("新增订单日志失败，失败原因:{}"), Throwables.getStackTraceAsString(e));
        }
    }
    /**
     * <AUTHOR>
     * @Date 15:52 2021/7/28
     * @Description 手动卡单释放
     */
    public ValueHolder orderDetentionRelease(JSONObject obj, User operateUser) {
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", operateUser.getLocale()));
        }
        ValueHolder vh = new ValueHolder();

        List<List<Object>> batchCancelIdList = Lists.partition(ids, 50);
        List<Future<BatchDetentionReleaseResult>> faildCountList = new ArrayList<>();

        //是否周期购卡单释放
        String cycleOrderRelease = obj.getString("cycleOrderRelease");
        boolean isCycleOrderRelease = YesNoEnum.Y.getKey().equals(cycleOrderRelease);

        for (List<Object> batchCancelIds : batchCancelIdList) {
            faildCountList.add(batchDetentionReleasePollExecutor.submit(new BatchDetentionRelease(operateUser, batchCancelIds, isCycleOrderRelease)));
        }
        AtomicInteger failureCount = new AtomicInteger(0);
        JSONArray errorMessage = new JSONArray();

        for (Future<BatchDetentionReleaseResult> resultFuture : faildCountList) {
            try {
                BatchDetentionReleaseResult detentionReleaseResult = resultFuture.get();
                failureCount.addAndGet(detentionReleaseResult.getFailNum());
                errorMessage.addAll(detentionReleaseResult.getErrorMessage());
            } catch (InterruptedException e) {
                log.error(LogUtil.format("批量释放卡单InterruptedException异常：{}"), Throwables.getStackTraceAsString(e));
            } catch (ExecutionException e) {
                log.error(LogUtil.format("批量释放卡单ExecutionException异常：{}"), Throwables.getStackTraceAsString(e));
            }
        }

        vh.put("data", errorMessage);
        vh.put("failureCount",failureCount.get());
        if (failureCount.get() == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("手动卡单释放成功", operateUser.getLocale()));
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("手动卡单释放成功" + (ids.size() - failureCount.get()) + "条，失败了" + failureCount.get() + "条", operateUser.getLocale()));
        }
        return vh;
    }

    public String exportExecuteErrorResult(List<ExecuteErrorVO> executeErrors, User user, String origFileName) {
        if (log.isDebugEnabled()) {
            log.debug(" exportExecuteErrorResult errorList:{}", JSONObject.toJSONString(executeErrors));
        }
        if (CollectionUtils.isEmpty(executeErrors)) {
            return null;
        }
        // 列名
        String[] columnNames = {"订单id", "错误原因"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"objId", "message"};
        List<String> k = Lists.newArrayList(keys);
        exportUtil.setEndpoint(ossConfig.getEndpoint());
        exportUtil.setAccessKeyId(ossConfig.getAccessKeyId());
        exportUtil.setAccessKeySecret(ossConfig.getAccessKeySecret());
        exportUtil.setBucketName(ossConfig.getBucketName());
        exportUtil.setTimeout(StringUtils.isEmpty(ossConfig.getTimeout()) ? "1800000" : ossConfig.getTimeout());
        Workbook hssfWorkbook = exportUtil.execute("错误结果", "错误结果-"+origFileName, c, k, executeErrors);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "卡单错误信息", user, "OSS-Bucket/IMPORT/OC_B_ORDER/");
    }

    @Data
    class BatchDetentionReleaseResult {
        private JSONArray errorMessage;
        private Integer failNum;
    }

    class BatchDetentionRelease implements Callable<BatchDetentionReleaseResult> {

        User user;
        List<Object> ids;
        boolean cycleOrderRelease;

        public BatchDetentionRelease(User user, List<Object> ids, boolean cycleOrderRelease) {
            this.user = user;
            this.ids = ids;
            this.cycleOrderRelease = cycleOrderRelease;
        }

        @Override
        public BatchDetentionReleaseResult call() throws Exception {
            BatchDetentionReleaseResult releaseResult = new BatchDetentionReleaseResult();
            Integer fail = 0;
            JSONArray errorMessage = new JSONArray();

            // 在批量处理开始前，查询所有卡单原因数据并构建Map
            Map<Integer, String> holdOrderReasonMap = buildHoldOrderReasonMap();

            for (int i = 0; i < ids.size(); i++) {
                JSONObject jsonObject = new JSONObject();
                Long orderId = Long.valueOf(ids.get(i).toString());
                try {
                    detentionRelease(orderId, user, cycleOrderRelease, holdOrderReasonMap);
                } catch (Exception e) {
                    log.info(LogUtil.format("手动卡单释放失败,失败原因：{},orderId=", orderId, Throwables.getStackTraceAsString(e)));
                    jsonObject.put("code", -1);
                    jsonObject.put("message", e.getMessage());
                    jsonObject.put("objid", ids.get(i));
                    errorMessage.add(jsonObject);
                    fail++;
                    String message = e.getMessage();
                    if (e.getMessage() != null && message.length() > 1000) {
                        message = message.substring(0, 1000);
                    }
                    insertOrderLog(orderId, null, OrderLogTypeEnum.DETENTION_RELEASE.getKey(), message, null, null, user);
                }
            }
            releaseResult.setFailNum(fail);
            releaseResult.setErrorMessage(errorMessage);
            return releaseResult;
        }
    }

    /**
     * 构建卡单原因映射Map
     *
     * @return Map<ID, 原因描述>
     */
    private Map<Integer, String> buildHoldOrderReasonMap() {
        Map<Integer, String> holdOrderReasonMap = new HashMap<>();
        try {
            List<StCHoldOrderReason> holdOrderReasons = stCHoldOrderReasonMapper.selectAllHoldOrder();
            if (CollectionUtils.isNotEmpty(holdOrderReasons)) {
                for (StCHoldOrderReason reason : holdOrderReasons) {
                    if (reason.getId() != null && StringUtils.isNotBlank(reason.getReason())) {
                        holdOrderReasonMap.put(Math.toIntExact(reason.getId()), reason.getReason());
                    }
                }
            }
            log.info("成功加载{}条卡单原因数据", holdOrderReasonMap.size());
        } catch (Exception e) {
            log.warn("查询卡单原因数据异常，将跳过原因补充逻辑：{}", e.getMessage());
        }
        return holdOrderReasonMap;
    }

    private ValueHolder detentionRelease(Long orderId, User operateUser, boolean cycleOrderRelease, Map<Integer, String> holdOrderReasonMap) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("detentionRelease 手工卡单释放 start orderId=", orderId));
        }
        ValueHolder vh = new ValueHolder();

        // 给订单加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = this.ocBOrderMapper.selectByID(orderId);
                if (ocBOrder == null) {
                    throw new NDSException("订单不存在");
                }
                if (!AdvanceConstant.DETENTION_STATUS_1.equals(ocBOrder.getIsDetention())) {
                    throw new NDSException("订单非卡单状态,不允许手动释放");
                }
                if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsCycle()) && OrderDetentionEnum.TMALL_CYCLE_BUY_CARD.getVal().equals(ocBOrder.getDetentionReason())) {
                    throw new NDSException("天猫周期购订单卡单不允许手动释放");
                }
                //业务类型=中台周期购提货，不支持批量取消卡单
                if (!cycleOrderRelease && OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(ocBOrder.getBusinessTypeCode())) {
                    throw new NDSException("中台周期购提货订单不支持批量取消卡单");
                }
                if (cycleOrderRelease && !OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(ocBOrder.getBusinessTypeCode())) {
                    throw new NDSException("周期购解卡只能释放中台周期购提货订单");
                }
                // 保存原卡单原因，用于日志记录
                String originalDetentionReason = ocBOrder.getDetentionReason();

                // 如果卡单原因为空但卡单原因ID不为空，则从Map中查找对应的原因描述
                if (StringUtils.isBlank(originalDetentionReason) && ocBOrder.getDetentionReasonId() != null) {
                    String reasonFromMap = holdOrderReasonMap.get(ocBOrder.getDetentionReasonId());
                    if (StringUtils.isNotBlank(reasonFromMap)) {
                        originalDetentionReason = reasonFromMap;
                        log.info("通过卡单原因ID={}补充了卡单原因描述：{}", ocBOrder.getDetentionReasonId(), reasonFromMap);
                    } else {
                        log.warn("未找到卡单原因ID={}对应的原因描述", ocBOrder.getDetentionReasonId());
                    }
                }

                // 检查用户是否有权限释放此卡单原因的订单
                checkDetentionReleasePermission(operateUser, originalDetentionReason);

                ocBOrder.setIsDetention(AdvanceConstant.DETENTION_STATUS_2);
                //更新状态为分仓
                ocBOrder.setSysremark("卡单释放成功");
                ocBOrder.setDetentionReason("");
                //最后一次卡单释放埋点
                ocBOrder.setDetentionReleaseDate(new Date());



                int num = ocBOrderMapper.update(ocBOrder, Wrappers.<OcBOrder>lambdaUpdate()
                        .set(OcBOrder::getDetentionReasonId, null)
                        .eq(OcBOrder::getId, ocBOrder.getId()));
                if (num > 0) {
                    ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.DETENTION_RELEASE_DATE,new Date(),ocBOrder.getId(),operateUser);
                    String logMsg = "OrderId="+ocBOrder.getId()+"手动释放卡单成功。原卡单原因：" +
                            (StringUtils.isNotBlank(originalDetentionReason) ? originalDetentionReason : "无，卡单ID：" + ocBOrder.getId());
                    //加入占单表
                    int i = 5 * 60 * 1000;
                    long l = System.currentTimeMillis() + i;
                    omsOccupyTaskService.addOcBOccupyTask(ocBOrder, new Date(l));
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.DETENTION_RELEASE.getKey(), logMsg, "", null, operateUser);
                    vh.put("code", ResultCode.SUCCESS);
                    vh.put("message", "卡单手动释放成功");
                }else {
                    String logMsg = "OrderId="+ocBOrder.getId()+"手动释放卡单失败。原卡单原因：" +
                            (StringUtils.isNotBlank(originalDetentionReason) ? originalDetentionReason : "无，卡单ID：" + ocBOrder.getId());
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.DETENTION_RELEASE.getKey(), logMsg, "", null, operateUser);
                    vh.put("code", ResultCode.FAIL);
                    vh.put("message", "手动释放卡单失败");
                }
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", operateUser.getLocale()));
            }
        } catch (Exception e) {
            log.info(LogUtil.format("取消HOLD单失败,异常信息={},OrderId=", orderId), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        } finally {
            redisLock.unlock();
        }
        return vh;
    }

    public ValueHolder orderDetentionSync(JSONObject obj, User operateUser, boolean isLockOrder) {
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", operateUser.getLocale()));
        }
        ValueHolder vh = new ValueHolder();
        JSONArray errorMessage = new JSONArray();
        int fail = 0;
        for (int i = 0; i < ids.size(); i++) {
            JSONObject jsonObject = new JSONObject();
            Long orderId = ids.getLong(i);
            try {
                vh = ocBOrderHoldService.detention(orderId, obj, operateUser, isLockOrder);
            } catch (Exception e) {
                log.info("手动卡单失败，orderId:{},失败原因：{}", orderId, e.getMessage());
                jsonObject.put("code", -1);
                jsonObject.put("message", e.getMessage());
                jsonObject.put("objid", ids.get(i));
                errorMessage.add(jsonObject);
                fail++;
                vh.put("code", ResultCode.FAIL);
                vh.put("message", e.getMessage());
                String message = e.getMessage();
                if (e.getMessage() != null && message.length() > 1000) {
                    message = message.substring(0, 1000);
                }
                insertOrderLog(orderId, null, OrderLogTypeEnum.DETENTION.getKey(), message, null, null, operateUser);
            }
        }
        vh.put("data", errorMessage);
        if (ids.size() == 1) {
            return vh;
        }
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("手动卡单成功", operateUser.getLocale()));
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("手动卡单成功" + (ids.size() - fail) + "条，失败了" + fail + "条", operateUser.getLocale()));
        }
        return vh;
    }

    public ValueHolder syncTaskUnHold(JSONObject obj, User operateUser){
        ValueHolder vh = new ValueHolder();
        //插入我的任务里
        AsyncTaskBody asyncTaskBody =new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("异步释放hold单");
        asyncTaskBody.setTaskType("释放hold单");
        //任务开始
        asyncTaskManager.beforeExecute(operateUser,asyncTaskBody);
        commonTaskExecutor.submit(() -> {
            //主线任务
            ValueHolder valueHolder = manualUnHoldOrder(obj, operateUser);
            HashMap hashMap = valueHolder.getData();
            Map<Object, Object> retMap = new LinkedHashMap<>();
            if (valueHolder.isOK()) {
                retMap.put("code", ResultCode.SUCCESS);
                Map<Object, Object> map = new LinkedHashMap<>();
                map.put("入参", parseParam(obj));
                map.put("出参", JSON.toJSONString(hashMap));
                retMap.put("message", map);
            }else {
                retMap.put("code",ResultCode.FAIL);
                retMap.put("入参",parseParam(obj));
                retMap.put("出参",JSON.toJSONString(hashMap));
            }
            log.info("主线任务返回结果为:{}",valueHolder);
            //任务完成
            asyncTaskManager.afterExecute(operateUser,asyncTaskBody,JSON.parseObject(JSON.toJSONString(retMap)));
        });
        vh.put("message", Resources.getMessage("执行成功，取消HOLD单异步释放任务开始！", operateUser.getLocale()));
        return vh;
    }

    private static String parseParam(JSONObject object){
        Map<String, String> map = new LinkedHashMap<>();
        String param = object.getString("param");
        try {
            if (Objects.nonNull(param) && !"".equals(param)) {
                String str = param.replace("[]", "null");
                JSONObject paramObj = (JSONObject) JSONObject.parse(str);
//                JSONObject page = paramObj.getJSONObject("page");
//                if (Objects.nonNull(page)) {
//                    String pageSize = page.getString("pageSize");
//                    map.put("每页数", pageSize);
//                    String pageNum = page.getString("pageNum");
//                    map.put("当前页", pageNum);
//                }
                JSONObject status = paramObj.getJSONObject("status");
                if (Objects.nonNull(status)) {
                    map.put("单据状态", status.getString("label"));
                }
                JSONArray highSearch = paramObj.getJSONArray("highSearch");
                if (CollectionUtils.isNotEmpty(highSearch)){
                    //获取OcBOrder里注解的集合
                    Map<String, String> annotationMap = getDeclaredFieldsInfo();
                    for (Object o : highSearch) {
                        JSONObject object2 = (JSONObject) JSON.toJSON(o);
                        if (!Objects.isNull(object2.getString("value")) && !"".equals(object2.getString("value")) && !object2.getString("value").contains("undefined")) {
                            String name = annotationMap.get(object2.getString("queryName"));
                            String val = object2.getString("value");
                            map.put(name, val);
                        }
                    }
                }
            }else {
                map.put("订单id", JSON.toJSONString(object.getJSONArray("ids")));
            }
        }catch (Exception e){
            log.error("解析释放hold单入参信息报错，输出原字符串！原因：{}", Throwables.getStackTraceAsString(e));
            return JSON.toJSONString(object);
        }
        return JSON.toJSONString(map);
    }
    //获取订单表全部字段的注解
    public static Map<String, String> getDeclaredFieldsInfo(){
        Map<String, String> map = new HashMap();
        Field[] fields=OcBOrder.class.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            boolean annotationPresent = fields[i].isAnnotationPresent(ApiModelProperty.class);
            if (annotationPresent) {
                // 获取注解值
                String name = fields[i].getAnnotation(ApiModelProperty.class).value();
                String key = fields[i].getAnnotation(JSONField.class).name();
                map.put(key,name);
            }
        }
        return map;
    }

    @AsyncTask("手动卡单")
    public JSONObject orderDetentionAsync(QuerySession session, JSONObject obj, User operateUser, boolean isLockOrder) {
        log.info(LogUtil.format("入参:{}", "开始手工异步卡单"), obj.toJSONString());
        JSONArray ids = obj.getJSONArray("ids");
        List<List<Object>> idPartitionList = Lists.partition(ids, 50);
//        ExecutorService executor = ThreadPoolConstantUtil.getOrderDetentionPollExecutor();
        List<Future<JSONArray>> results = new ArrayList<>();
        for (List<Object> idPartition : idPartitionList) {
            results.add(orderDetentionPollExecutor.submit(new CallableOrderDetention(idPartition, operateUser, obj, isLockOrder)));
        }
        ValueHolder vh = new ValueHolder();
        StringBuilder errorMessage = new StringBuilder();
        int fail = 0;
        for (Future<JSONArray> futureResult : results) {
            try {
                JSONArray array = futureResult.get();
                fail += array.size();
                for (int i = 0; i < array.size(); i++) {
                    JSONObject object = array.getJSONObject(i);
                    errorMessage.append("订单ID：")
                            .append(object.getLong("objid"))
                            .append("卡单失败,")
                            .append(object.getString("message"))
                            .append(";");
                }
            } catch (InterruptedException | ExecutionException e) {
                errorMessage.append("获取线程执行结果失败！");
            }
        }
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("手动卡单成功", operateUser.getLocale()));
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "手动卡单成功" + (ids.size() - fail) + "条，失败了" + fail + "条！" + errorMessage);
        }
        return vh.toJSONObject();
    }

    public OrderDetentionFutureResult orderDetentionFuture(JSONObject obj, User operateUser, boolean isLockOrder) {
        log.info(LogUtil.format("入参:{}", "开始手工同步卡单"), obj.toJSONString());
        JSONArray ids = obj.getJSONArray("ids");
        List<List<Object>> idPartitionList = Lists.partition(ids, 50);

        List<Future<JSONArray>> results = new ArrayList<>();
        for (List<Object> idPartition : idPartitionList) {
            results.add(orderDetentionPollExecutor.submit(new CallableOrderDetention(idPartition, operateUser, obj,isLockOrder)));
        }

        int fail = 0;
        List<ExecuteErrorVO> infoHolderList = Lists.newArrayListWithExpectedSize(1000);
        for (Future<JSONArray> futureResult : results) {
            try {
                JSONArray array = futureResult.get();
                fail += array.size();
                for (int i = 0; i < array.size(); i++) {
                    JSONObject object = array.getJSONObject(i);
                    Long objid = object.getLong("objid");
                    String message = object.getString("message");
                    if (objid != null) {
                        ExecuteErrorVO infoHolder = new ExecuteErrorVO();
                        infoHolder.setObjId(objid);
                        infoHolder.setMessage(message);
                        infoHolderList.add(infoHolder);
                    }
                }
            } catch (InterruptedException | ExecutionException e) {
            }
        }
        return new OrderDetentionFutureResult(infoHolderList,ids.size()-fail);
    }


    /**
     * description:手动卡单
     * @Author:  liuwenjin
     * @Date 2022/9/17 14:55
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ValueHolder detention(Long orderId, JSONObject obj, User operateUser, boolean isLockOrder) {
        if (log.isDebugEnabled()) {
            log.debug("detention 手工卡单 start orderId={}", orderId);
        }
        ValueHolder vh = new ValueHolder();
        // 给订单加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            boolean b = redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS);
            if (!isLockOrder || b) {
                detainWithRedisLock(orderId, obj, operateUser, vh);
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", operateUser.getLocale()));
            }
        } catch (Exception e) {
            log.info("手动卡单失败,OrderId={},异常信息={}", orderId, e);
            throw new NDSException(e.getMessage());
        } finally {
            redisLock.unlock();
        }
        return vh;
    }

    public void detainWithRedisLock(Long orderId, JSONObject obj, User operateUser, ValueHolder vh) {
        OcBOrder ocBOrder = this.ocBOrderMapper.selectByID(orderId);
        if (ocBOrder == null) {
            throw new NDSException("订单不存在");
        }
        if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())
                && !OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
            throw new NDSException("订单非待寻源/待审核状态,不允许手动卡单");
        }
        if (AdvanceConstant.DETENTION_STATUS_1.equals(ocBOrder.getIsDetention())) {
            throw new NDSException("此订单已经卡单,不允许重复操作");
        }

        Date releaseDate = null;
        String autoRelease = obj.getString("IS_AUTO_RELEASE");
        //卡单原因
        String holdOrderReason = obj.getString("HOLD_DETENTION_ORDER_REASON");
        if ("Y".equals(autoRelease)) {
            log.info("orderDetention.autoRelease :{}", obj.toJSONString());
            String releaseType = obj.getString("RELEASE_TIME_TYPE");
            if ("1".equals(releaseType)) {
                String releaseTime = obj.getString("RELEASE_TIME");
                if (StringUtils.isNotEmpty(releaseTime)) {
                    try {
                        releaseDate = DateUtil.stringToDate(releaseTime);
                    } catch (Exception e) {
                        log.error("手动卡单失败，失败原因：获取指定时间释放异常");
                        throw new NDSException("手动卡单失败，失败原因：获取指定时间释放异常");
                    }
                } else {
                    throw new NDSException("手动卡单失败，失败原因：获取指定时间释放不能为空");
                }
            } else if ("2".equals(releaseType)) {
                String dateType = obj.getString("DAY_TYPE");
                Date orderDate = null;
                if ("1".equals(dateType)) {
                    orderDate = ocBOrder.getOrderDate();
                } else if ("2".equals(dateType)) {
                    orderDate = ocBOrder.getPayTime();
                } else if ("3".equals(dateType)) {
                    orderDate = ocBOrder.getCreationdate();
                } else {
                    throw new NDSException("手动卡单失败，失败原因：释放时点类型未知");
                }
                if (orderDate == null) {
                    throw new NDSException("手动卡单失败，失败原因：订单时间不能为空");
                }
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(orderDate);

                String timeUnit = obj.getString("TIME_UNIT");
                int type = 0;
                if ("1".equals(timeUnit)) {
                    type = Calendar.MINUTE;
                } else if ("2".equals(timeUnit)) {
                    type = Calendar.HOUR;
                } else if ("3".equals(timeUnit)) {
                    type = Calendar.DAY_OF_MONTH;
                } else {
                    throw new NDSException("手动卡单失败，失败原因：未知的时间单位");
                }
                Integer fixedDuration = obj.getInteger("FIXED_DURATION");
                if (fixedDuration == null) {
                    throw new NDSException("手动卡单失败，失败原因：指定时长不能为空");
                }

                calendar.add(type, fixedDuration);
                releaseDate = calendar.getTime();
                log.info("autoRelease.time:{}, {}", releaseDate, orderDate);
            } else {
                throw new NDSException("手动卡单失败，失败原因：未知的释放时点");
            }
        }

        OcBOrder newOcBOrder = new OcBOrder();
        newOcBOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        newOcBOrder.setId(orderId);
        newOcBOrder.setIsDetention(AdvanceConstant.DETENTION_STATUS_1);
        Map<Integer, String> OrderDetentionMap = OrderDetentionEnum.toMap();
        newOcBOrder.setSysremark(OrderDetentionMap.get(Integer.valueOf(holdOrderReason)));
        newOcBOrder.setDetentionReason(OrderDetentionMap.get(Integer.valueOf(holdOrderReason)));
        newOcBOrder.setDetentionReasonId(Integer.valueOf(holdOrderReason));
        //最后一次卡单释放埋点
        newOcBOrder.setDetentionReleaseDate(releaseDate);

        // 卡单清空仓库
        JSONObject updateObj = JSON.parseObject(JSON.toJSONStringWithDateFormat(newOcBOrder, "yyyy-MM-dd HH:mm:ss", SerializerFeature.EMPTY));
        updateObj.put("CP_C_PHY_WAREHOUSE_ID", null);
        updateObj.put("CP_C_PHY_WAREHOUSE_ECODE", null);
        updateObj.put("CP_C_PHY_WAREHOUSE_ENAME", null);
        ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.DETENTION_DATE, new Date(), ocBOrder.getId(), operateUser);
        if (ocBOrderMapper.updateRecord(updateObj) > 0) {
            //待审核需要释放库存
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
                //查询原单明细
                List<OcBOrderItem> ocBOrderItemList = orderItemMapper.selectOrderItemListOccupy(orderId);
                log.info("order{}.开始调用库存取消接口", orderId);
                //封装数据
                SgOmsShareOutRequest request = buildSgOmsShareOutRequest(ocBOrder, ocBOrderItemList, operateUser);
                log.info("调用sg取消库存封装数据为：{}", JSON.toJSONString(request));
                ValueHolderV14 sgValueHolder = sgRpcService.voidSgOmsShareOut(request, ocBOrder, ocBOrderItemList);
                AssertUtil.assertException(!sgValueHolder.isOK(), "释放库存失败");
                log.info("调用sg取消库存返回接口数据为：{}", JSON.toJSONString(sgValueHolder));

            }
            //删除占单中间表
            ocBOccupyTaskMapper.deleteOcBOccupyTaskByOrderId(orderId);
            String logMsg = "OrderId=" + ocBOrder.getId() + "手动卡单成功。";
            if (StringUtils.isNotEmpty(obj.getString("HOLD_ORDER_REASON_MSG"))) {
                logMsg = obj.getString("HOLD_ORDER_REASON_MSG");
            }
            //自动释放才会加人
            if ("Y".equals(autoRelease)) {
                // 加入中间表
                omsOccupyTaskService.addOcBOccupyTask(ocBOrder, releaseDate);
            }
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.DETENTION.getKey(), logMsg, "", null, operateUser);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "手动卡单成功");
        } else {
            String logMsg = "OrderId=" + ocBOrder.getId() + "手动卡单失败。";
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.DETENTION.getKey(), logMsg, "", null, operateUser);
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "手动卡单失败");
        }
    }

    /**
     * description:手动卡单批量
     *
     * @Author: liuwenjin
     * @Date 2022/9/17 14:55
     */
    public ValueHolder orderDetention(JSONObject obj, User operateUser, boolean isLockOrder) {
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", operateUser.getLocale()));
        }
        OcBOrderHoldService service = ApplicationContextHandle.getBean(OcBOrderHoldService.class);
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        int limitSize = config.getProperty("oms.oc.order.detention.async.limit.size", 100);
        if (ids.size() >= limitSize) {
            QuerySession session = new QuerySessionImpl(operateUser);
            DefaultWebEvent event = new DefaultWebEvent("orderDetention", new HashMap(16));
            event.put("menu", "零售发货单");
            session.setEvent(event);
            JSONObject result = service.orderDetentionAsync(session, obj, operateUser, isLockOrder);
            ValueHolder vh = new ValueHolder();
            if (result.getIntValue(R3ParamConstants.CODE) == 0) {
                vh.put(R3ParamConstants.CODE, ResultCode.SUCCESS);
                vh.put(R3ParamConstants.MESSAGE, "当前数据量较大，请在我的任务查看执行情况");
            } else {
                vh.put(R3ParamConstants.CODE, ResultCode.FAIL);
                vh.put(R3ParamConstants.MESSAGE, result.getString(R3ParamConstants.MESSAGE));
            }
            return vh;
        }
        return service.orderDetentionSync(obj, operateUser, isLockOrder);
    }

    /**
     * description:多线程取消hold单
     *
     * @Author: liuwenjin
     * @Date 2021/12/13 3:27 下午
     */
    public class CallableTaskWithResult implements Callable<List<JSONObject>> {
        private final List<Long> longs;
        private final User operateUser;
        private final String holdReleaseReason;

        public CallableTaskWithResult(List<Long> longs, User operateUser, String holdReleaseReason) {
            this.longs = longs;
            this.operateUser = operateUser;
            this.holdReleaseReason = holdReleaseReason;
        }

        @Override
        public List<JSONObject> call() throws Exception {
            ValueHolder vh = new ValueHolder();
            List<JSONObject> jsonObjectList = new ArrayList<>(longs.size());
            for (int i = 0; i < longs.size(); i++) {
                JSONObject jsonObject = new JSONObject();
                Long orderId = longs.get(i);
                try {
                    vh = ocBOrderHoldItemService.cancelHoldOrder(orderId, operateUser, holdReleaseReason);
                } catch (Exception e) {
                    log.error("释放订单HOLD单失败，orderId:{},失败原因：{}", orderId, e.getMessage());
                    jsonObject.put("code", -1);
                    jsonObject.put("message", e.getMessage());
                    jsonObject.put("objid", longs.get(i));
                    jsonObjectList.add(jsonObject);
                    vh.put("code", ResultCode.FAIL);
                    vh.put("message", e.getMessage());
                    String message = e.getMessage();
                    if (e.getMessage() != null && message.length() > 1000) {
                        message = message.substring(0, 1000);
                    }
                    insertOrderLog(orderId, null, OrderLogTypeEnum.ORDER_HOLD_CANCEL.getKey(), message, null, null, operateUser);
                }
            }
            return jsonObjectList;
        }
    }

    class CallableOrderDetention implements Callable<JSONArray> {
        private final List<Object> ids;
        private final User user;
        private final JSONObject obj;
        private final boolean isLockOrder;

        public CallableOrderDetention(List<Object> ids, User user, JSONObject obj, boolean isLockOrder) {
            this.ids = ids;
            this.user = user;
            this.obj = obj;
            this.isLockOrder = isLockOrder;
        }

        @Override
        public JSONArray call() throws Exception {
            JSONArray errorMessage = new JSONArray();
            for (Object id : ids) {
                JSONObject jsonObject = new JSONObject();
                Long orderId = Long.valueOf(id.toString());
                try {
                    OcBOrderHoldService service = ApplicationContextHandle.getBean(OcBOrderHoldService.class);
                    ValueHolder vh = service.detention(orderId, obj, user, isLockOrder);
                    AssertUtils.isTrue(vh.isOK(), (String) vh.get(R3ParamConstants.MESSAGE));
                } catch (Exception e) {
                    log.info("手动卡单失败，orderId:{},失败原因：{}", orderId, Throwables.getStackTraceAsString(e));
                    jsonObject.put("code", -1);
                    jsonObject.put("message", e.getMessage());
                    jsonObject.put("objid", orderId);
                    errorMessage.add(jsonObject);
                    String message = e.getMessage();
                    if (e.getMessage() != null && message.length() > 1000) {
                        message = message.substring(0, 1000);
                    }
                    insertOrderLog(orderId, null, OrderLogTypeEnum.DETENTION.getKey(), message, null, null, user);
                }
            }
            return errorMessage;
        }
    }

    /**
     * <AUTHOR>
     * @Date 14:31 2021/7/30
     * @Description 封装sg、所用的数据
     */
    public SgOmsShareOutRequest buildSgOmsShareOutRequest(OcBOrder orderInfo, List<OcBOrderItem> ocBOrderItemList, User user) {
        SgOmsShareOutRequest request = new SgOmsShareOutRequest();
        request.setSourceBillId(orderInfo.getId());
        request.setSourceBillNo(orderInfo.getBillNo());
        request.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        request.setLoginUser(user);
        List<SgOmsShareOutItemRequest> itemRequestList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            SgOmsShareOutItemRequest sgOmsShareOutItemRequest = new SgOmsShareOutItemRequest();
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            sgOmsShareOutItemRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            sgOmsShareOutItemRequest.setQtyPreout(ocBOrderItem.getQty());
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            itemRequestList.add(sgOmsShareOutItemRequest);
        }
        request.setItemRequestList(itemRequestList);

        return request;
    }
    /**
     * description:
     * 1  客服卡单-疫情停发
     * 2  客服卡单-延期等通知
     * 3  审单卡单-疫情停发
     * 0 客服卡单-运营要求
     * 4  销管卡单-运营要求
     * 5 审单卡单-运营要求
     * @Author:  liuwenjin
     * @Date 2022/11/4 14:15
     */
    /**
     * 检查用户是否有权限释放指定卡单原因的订单
     *
     * @param operateUser 操作用户
     * @param detentionReason 卡单原因
     * @throws NDSException 当用户无权限时抛出异常
     */
    private void checkDetentionReleasePermission(User operateUser, String detentionReason) {
        if (operateUser == null || StringUtils.isBlank(operateUser.getName())) {
            return; // 用户信息不完整，跳过检查
        }

        if (StringUtils.isBlank(detentionReason)) {
            return; // 卡单原因为空，跳过检查
        }

        try {
            // 调用BusinessSystemParamService中的权限检查方法
            boolean isRestricted = businessSystemParamService.isUserRestrictedForDetentionRelease(
                    operateUser.getName(), detentionReason);

            if (isRestricted) {
                throw new NDSException("当前用户无权限释放此卡单原因的订单：" + detentionReason);
            }
        } catch (NDSException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            // 其他异常，记录日志但不阻止操作
            log.warn("检查卡单释放权限异常，用户：{}，卡单原因：{}，异常信息：{}",
                    operateUser.getName(), detentionReason, e.getMessage());
        }
    }

}

