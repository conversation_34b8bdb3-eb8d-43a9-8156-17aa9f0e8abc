package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.request.OrderMarkingRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单打标
 *
 * @date 2022/6/14
 * @author: ming.fz
 */
@Component
@Slf4j
public class OcBOrderMarkingService {

    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBOrderMapper orderMapper;

    /**
     * 打标签
     *
     * @param param
     * @param user
     * @return
     */
    public ValueHolderV14 orderMarking(OrderMarkingRequest param, User user) {
        if (param == null || param.getIds() == null || param.getIds().length < 1) {
            throw new NDSException(Resources.getMessage("请选择需要打标的订单记录！", user.getLocale()));
        }
        if (StringUtils.isEmpty(param.getStCCustomLabelId())) {
            throw new NDSException(Resources.getMessage("请选择标签！", user.getLocale()));
        }
        Long[] ids = param.getIds();
        Map<Long, String> errorMap = Maps.newHashMap();
        for (Long id : ids) {
            orderMarking(user, id, errorMap, param);
        }
        return getValueHolderV14(ids, errorMap);
    }


    private void orderMarking(User user, Long id, Map<Long, String> errorMap, OrderMarkingRequest param) {
        //猫超订单不能取消订单
        OcBOrder ocBOrder = orderMapper.selectById(id);
        if (ocBOrder == null) {
            errorMap.put(id, "当前记录已不存在！");
            return;
        }
        Integer status = ocBOrder.getOrderStatus();
        if (OmsOrderStatus.CANCELLED.toInteger().equals(status) || OmsOrderStatus.SYS_VOID.toInteger().equals(status)) {
            errorMap.put(id, "状态已取消或作废，不能打标！");
            return;
        }
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder updateModel = new OcBOrder();
                updateModel.setId(id);
                updateModel.setStCCustomLabelId(getJoinStr(param.getStCCustomLabelId(), ocBOrder.getStCCustomLabelId()));
                updateModel.setStCCustomLabelEname(getJoinStr(param.getStCCustomLabelEname(), ocBOrder.getStCCustomLabelEname()));
                OmsModelUtil.setDefault4Upd(updateModel, user);
                if (orderMapper.updateById(updateModel) > 0) {
                    logSave(ocBOrder, user, "手工打标成功!");
                }
            } else {
                errorMap.put(id, "当前订单其他人在操作，请稍后再试!");
            }
        } catch (Exception ex) {
            if (log.isDebugEnabled()) {
                log.error(LogUtil.format("订单取消锁单错误,error：{}"), Throwables.getStackTraceAsString(ex));
            }
            errorMap.put(id, "程序内部错误！");
        } finally {
            redisLock.unlock();
        }
    }

    private void logSave(OcBOrder orderInfo, User user, String logContent) {
        String logType = OrderLogTypeEnum.ORDER_LABEL.getKey();
        try {
            /*omsOrderLogService.addOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), logType,
                    logContent, "", "", user.getLastloginip());*/

            omsOrderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(), logType,
                    logContent, "", "", user);
        } catch (Exception e) {
            log.error(LogUtil.format("调用日志服务异常,error：{},id = ", orderInfo.getId()), Throwables.getStackTraceAsString(e));
            omsOrderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(), logType,
                    logContent, JSONObject.toJSONString(orderInfo), e.toString(), user);
        }

    }

    private String getJoinStr(String joinStr, String str) {
        Set<String> set = new HashSet<>();
        if (StringUtils.isEmpty(str)) {
            return joinStr;
        }
        String[] split = str.split(",");
        for (String s : split) {
            set.add(s);
        }
        set.add(joinStr);
        return set.stream()
                .map(String::toString)
                .collect(Collectors.joining(","));
    }

    /**
     * 取消标签
     *
     * @param param
     * @param user
     * @return
     */
    public ValueHolderV14 clearMarking(OrderMarkingRequest param, User user) {
        if (param == null || param.getIds() == null || param.getIds().length < 1) {
            throw new NDSException(Resources.getMessage("请选择需要取消打标订单的记录！", user.getLocale()));
        }
        if (StringUtils.isEmpty(param.getStCCustomLabelId())) {
            throw new NDSException(Resources.getMessage("请选需要择取消的标签！", user.getLocale()));
        }
        Long[] ids = param.getIds();
        Map<Long, String> errorMap = Maps.newHashMap();
        for (Long id : ids) {
            clearMarking(user, id, errorMap, param);
        }
        return getValueHolderV14(ids, errorMap);
    }

    private void clearMarking(User user, Long id, Map<Long, String> errorMap, OrderMarkingRequest param) {
        //猫超订单不能取消订单
        OcBOrder ocBOrder = orderMapper.selectById(id);
        if (ocBOrder == null) {
            errorMap.put(id, "当前记录已不存在！");
            return;
        }
        Integer status = ocBOrder.getOrderStatus();
        if (OmsOrderStatus.CANCELLED.toInteger().equals(status) || OmsOrderStatus.SYS_VOID.toInteger().equals(status)) {
            errorMap.put(id, "状态已取消或作废，不能取消打标！");
            return;
        }
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder updateModel = new OcBOrder();
                updateModel.setId(id);
                updateModel.setStCCustomLabelId(clearStr(param.getStCCustomLabelId(), ocBOrder.getStCCustomLabelId()));
                updateModel.setStCCustomLabelEname(clearStr(param.getStCCustomLabelEname(), ocBOrder.getStCCustomLabelEname()));
                OmsModelUtil.setDefault4Upd(updateModel, user);
                if (orderMapper.updateById(updateModel) > 0) {
                    logSave(ocBOrder, user, "手工取消标签成功！标签：" + param.getStCCustomLabelEname());
                }
            } else {
                errorMap.put(id, "当前订单其他人在操作，请稍后再试!");
            }
        } catch (Exception ex) {
            if (log.isDebugEnabled()) {
                log.error(LogUtil.format("订单取消锁单错误,error：{}"), Throwables.getStackTraceAsString(ex));
            }
            errorMap.put(id, "程序内部错误！");
        } finally {
            redisLock.unlock();
        }
    }

    private String clearStr(String clearStr, String str) {
        Set<String> set = new HashSet<>();
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        String[] split = str.split(",");
        for (String s : split) {
            if (s.equals(clearStr)) {
                continue;
            }
            set.add(s);
        }
        return set.stream()
                .map(String::toString)
                .collect(Collectors.joining(","));
    }

    private ValueHolderV14 getValueHolderV14(Long[] ids, Map<Long, String> errorMap) {
        int size = errorMap.size();
        ValueHolderV14 vh = ValueHolderV14Utils.getSuccessValueHolder("成功" + (ids.length - size) + "条！");
        if (size > 0) {
            vh.setMessage(vh.getMessage() + "失败" + size + "条！");
            vh.setData(JSONObject.parseObject(JSONObject.toJSONString(errorMap)));
        }
        return vh;
    }
}