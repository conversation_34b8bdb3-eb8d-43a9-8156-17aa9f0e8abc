package com.jackrain.nea.oc.oms.services.directreport;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBDirectReportOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBDirectReportOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBDirectReportOrderStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrder;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrderItem;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 直发预占保存
 *
 * <AUTHOR>
 * @since 2024-11-29 11:07
 */
@Slf4j
@Service
public class OcBDirectReportOrderDeleteService {
    @Resource
    private OcBDirectReportOrderMapper ocBDirectReportOrderMapper;
    @Resource
    private OcBDirectReportOrderItemMapper ocBDirectReportOrderItemMapper;

    /**
     * 删除明细-加锁
     */
    public void delete(Long objId, JSONObject param, QuerySession querySession) {
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(BllRedisKeyResources.buildDirectReportOptLockKey(objId));
        try {
            if (redisLock.tryLock(1, TimeUnit.MINUTES)) {
                doDelete(objId, param, querySession);
            } else {
                throw new NDSException("请勿同时操作，建议稍后再试");
            }
        } catch (InterruptedException e) {
            log.warn(LogUtil.format("直发预占加锁失败，请联系值班人员,异常信息:{}",
                    "DirectReportOptLockKey.error"), Throwables.getStackTraceAsString(e));
            throw new NDSException("加锁失败，请联系值班人员");
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 删除明细
     */
    private void doDelete(Long objId, JSONObject param, QuerySession querySession) {
        JSONObject tabItem = param.getJSONObject("tabitem");
        JSONArray itemArray = tabItem.getJSONArray("OC_B_DIRECT_REPORT_ORDER_ITEM");
        if (Objects.isNull(itemArray) || itemArray.size() == 0) {
            throw new NDSException("请选择要删除的明细");
        }

        //判断主表是否存在
        OcBDirectReportOrder main = ocBDirectReportOrderMapper.selectById(objId);
        if (main == null || YesNoEnum.N.getKey().equals(main.getIsactive())) {
            throw new NDSException("未找到有效记录");
        }
        //状态数据检查
        if (!OcBDirectReportOrderStatusEnum.UN_AUDITED.getValue().equals(main.getStatus())) {
            log.info(LogUtil.format("当前只支持删除未审核的单据：{}",
                    "OcBDirectReportOrderDeleteService.deleteByFi"), objId);
            throw new NDSException("当前只支持删除未审核的单据");
        }

        Boolean isDelete = false;
        Boolean isDel = param.getBoolean("isdelmtable");
        //判断是删除主表还是明细表单独删除
        if (isDelete.equals(isDel)) {
            //单独删除明细
            deleteItem(itemArray, main, querySession);
        } else {
            //删除主表
            throw new NDSException("暂不支持删除主表");
        }
    }

    /**
     * 删除明细
     */
    private void deleteItem(JSONArray itemArray, OcBDirectReportOrder main, QuerySession querySession) {
        List<Long> itemIds = itemArray.toJavaList(Long.class);
        List<OcBDirectReportOrderItem> itemList = ocBDirectReportOrderItemMapper.selectList(new QueryWrapper<OcBDirectReportOrderItem>().lambda()
                .eq(OcBDirectReportOrderItem::getOcBDirectReportOrderId, main.getId())
                .in(OcBDirectReportOrderItem::getId, itemIds)
                .eq(BaseModel::getIsactive, YesNoEnum.Y.getKey()));
        if (CollectionUtils.isEmpty(itemList)) {
            throw new NDSException("未找到对应明细");
        }
        //用于后面删除
        Map<Long, String> beforeDelObjMap = new HashMap<>();
        for (OcBDirectReportOrderItem item : itemList) {
            beforeDelObjMap.put(item.getId(), JSON.toJSONString(item));
        }
        querySession.setAttribute("beforeDelObjMap", beforeDelObjMap);
        List<Long> ids = itemList.stream().map(OcBDirectReportOrderItem::getId).collect(Collectors.toList());
        ocBDirectReportOrderItemMapper.deleteBatchIds(ids);

        List<OcBDirectReportOrderItem> itemList2 = ocBDirectReportOrderItemMapper.selectList(new QueryWrapper<OcBDirectReportOrderItem>().lambda()
                .eq(OcBDirectReportOrderItem::getOcBDirectReportOrderId, main.getId())
                .eq(BaseModel::getIsactive, YesNoEnum.Y.getKey()));
        main.setTotalQty(ListUtils.emptyIfNull(itemList2).stream()
                .map(OcBDirectReportOrderItem::getQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        main.setTotWeight(ListUtils.emptyIfNull(itemList2).stream()
                .map(OcBDirectReportOrderItem::getTotWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        OmsModelUtil.setDefault4Upd(main, querySession.getUser());
        ocBDirectReportOrderMapper.updateById(main);
    }
}
