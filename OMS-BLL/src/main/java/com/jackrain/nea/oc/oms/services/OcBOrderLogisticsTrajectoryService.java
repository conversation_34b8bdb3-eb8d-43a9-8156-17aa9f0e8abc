package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.ip.model.tms.TmsLogisticsTrajectoryXmlModel;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogisticsTrajectoryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.enums.LogisticsStatusEnum;
import com.jackrain.nea.oc.oms.model.request.kdzs.KdzsCallBackRequest;
import com.jackrain.nea.oc.oms.model.request.kdzs.LogisticsFullTrace;
import com.jackrain.nea.oc.oms.model.result.kdzs.KdzsCallBackResponse;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLogisticsTrajectory;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.ReturnOrderNodeEnum;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OcBOrderLogisticsTrajectoryService {

    @Autowired
    OcBOrderLogisticsTrajectoryMapper mapper;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBReturnOrderNodeRecordService nodeRecordService;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    /**
     * description:插入物流轨迹
     * @Author:  liuwenjin
     * @Date 2021/12/7 4:50 下午
     */
    @Transactional(rollbackFor = {Exception.class})
    public ValueHolderV14 insterLogisticsTrajectory(JSONObject jsonObject) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (log.isDebugEnabled()){
            log.debug(this.getClass().getName()+"param,jsonObject:{}",jsonObject);
        }
        if (jsonObject ==null){
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("物流轨迹json对象为空！");
            return vh;
        }
        try {
            TmsLogisticsTrajectoryXmlModel tmsLogisticsTrajectoryXmlModel = JSON.toJavaObject(jsonObject,TmsLogisticsTrajectoryXmlModel.class);
            if (log.isDebugEnabled()){
                log.debug(this.getClass().getName()+"param,tmsLogisticsTrajectoryXmlModel:{}",JSON.toJSONString(tmsLogisticsTrajectoryXmlModel));
            }
            OcBOrderLogisticsTrajectory logisticsTrajectory =new OcBOrderLogisticsTrajectory();
            if (Objects.nonNull(tmsLogisticsTrajectoryXmlModel)){
                BeanUtils.copyProperties(tmsLogisticsTrajectoryXmlModel.getXmlData(), logisticsTrajectory);
                Long id = ModelUtil.getSequence("oc_b_order_logistics_trajectory");
                logisticsTrajectory.setId(id);
                //合单相关逻辑
                if (Objects.nonNull((tmsLogisticsTrajectoryXmlModel.getXmlData().getCombinationOrders()))){
                    logisticsTrajectory.setMergeOriginalLegno(tmsLogisticsTrajectoryXmlModel.getXmlData().getCombinationOrders().getMergeOriginalLegno());
                    logisticsTrajectory.setMergeOriginalOrderCode(tmsLogisticsTrajectoryXmlModel.getXmlData().getCombinationOrders().getMergeOriginalOrderCode());
                    logisticsTrajectory.setQuantity(tmsLogisticsTrajectoryXmlModel.getXmlData().getCombinationOrders().getQuantity());
                    logisticsTrajectory.setWeight(tmsLogisticsTrajectoryXmlModel.getXmlData().getCombinationOrders().getWeight());
                    logisticsTrajectory.setVolume(tmsLogisticsTrajectoryXmlModel.getXmlData().getCombinationOrders().getVolume());
                    logisticsTrajectory.setCarton(tmsLogisticsTrajectoryXmlModel.getXmlData().getCombinationOrders().getCarton());
                }
                //赋值head的属性
                if (Objects.nonNull((tmsLogisticsTrajectoryXmlModel.getHead()))){
                    logisticsTrajectory.setType(tmsLogisticsTrajectoryXmlModel.getHead().getType());
                    logisticsTrajectory.setSender(tmsLogisticsTrajectoryXmlModel.getHead().getSender());
                    logisticsTrajectory.setCode(tmsLogisticsTrajectoryXmlModel.getHead().getCode());
                    logisticsTrajectory.setDate(tmsLogisticsTrajectoryXmlModel.getHead().getDate());
                    logisticsTrajectory.setBusicode(tmsLogisticsTrajectoryXmlModel.getHead().getBusicode());
                    logisticsTrajectory.setInvokeUnikey(tmsLogisticsTrajectoryXmlModel.getHead().getInvokeUnikey());
                }
                logisticsTrajectory.setLogisticStatus(logisticsTrajectory.getStatus());
                Date onroadDate=null,onroadTransferDate=null,arrivedDate=null;
                //根据原单编号查询原单，赋值原单id、原单类型
                String billNo = Optional.ofNullable(logisticsTrajectory.getLegno()).orElse("");
                if(billNo.startsWith(OcCommonConstant.ORDER_PRIFIX)){
                    OcBOrder order= orderMapper.selectBySgBOutBillNo(billNo);
                    logisticsTrajectory.setOrigOrderId(order.getId());
                    logisticsTrajectory.setOrigOrderType(OcCommonConstant.BILL_TYPE_RETAIL);
                   // onroadDate=order.getOnroadDate();
                    //onroadTransferDate=order.getOnroadDate();
                    //arrivedDate=order.getArrivedDate();
                }else if(billNo.startsWith(OcCommonConstant.RETURN_ORDER_PRIFIX)){
                    Long origOrderId= ocBReturnOrderMapper.selectBySgBOutBillNo(billNo);
                    logisticsTrajectory.setOrigOrderId(origOrderId);
                    logisticsTrajectory.setOrigOrderType(OcCommonConstant.BILL_TYPE_RETURN_ORDER);
                }
                makeCreateField(logisticsTrajectory, SystemUserResource.getRootUser());
                String logisticsStatus =logisticsTrajectory.getLogisticStatus();
                String status =logisticsTrajectory.getStatus();
                //物流-揽收时间
                if (billNo.startsWith(OcCommonConstant.ORDER_PRIFIX) && LogisticsStatusEnum.ONROAD.getCode().equals(status) && Objects.isNull(onroadDate)){
                    onroadDate=new Date();
                    ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.ONROAD_DATE,new Date(),logisticsTrajectory.getOrigOrderId(),SystemUserResource.getRootUser());
                }
                //物流-第一次中转时间
                if (billNo.startsWith(OcCommonConstant.ORDER_PRIFIX) && LogisticsStatusEnum.ONROAD.getCode().equals(status) && !Objects.isNull(onroadTransferDate)){
                    onroadTransferDate=new Date();
                    ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.ONROAD_TRANSFER_DATE,new Date(),logisticsTrajectory.getOrigOrderId(),SystemUserResource.getRootUser());
                }
                //物流-签收时间
                if (billNo.startsWith(OcCommonConstant.ORDER_PRIFIX) && LogisticsStatusEnum.ARRIVED.getCode().equals(status) && Objects.isNull(arrivedDate)){
                    arrivedDate=new Date();
                    ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.ARRIVED_DATE,new Date(),logisticsTrajectory.getOrigOrderId(),SystemUserResource.getRootUser());
                }
                int n = mapper.insert(logisticsTrajectory);
                //修改订单主表的物流轨状态字段 todo//等拿到报文 在分析
                if (n>0){
                    int m =0;
                    if (billNo.startsWith(OcCommonConstant.ORDER_PRIFIX)){
                        //回写到零售发货单上 物流状态
                        m =orderMapper.updateByOnId(billNo,logisticsStatus,onroadDate,onroadTransferDate,arrivedDate);
                    }else if (billNo.startsWith(OcCommonConstant.RETURN_ORDER_PRIFIX)){
                        //回写到退单上 物流状态
                        m =ocBReturnOrderMapper.updateByThId(billNo,logisticsStatus);

                        ReturnOrderNodeEnum nodeEnum =  null;
                        if(OcCommonConstant.LOGISTICS_STATUS_ONROAD.equals(logisticsStatus)){
                            nodeEnum = ReturnOrderNodeEnum.EXPRESS_RECEIVE_TIME;
                        }else if(OcCommonConstant.LOGISTICS_STATUS_ARRIVED.equals(logisticsStatus)){
                            nodeEnum = ReturnOrderNodeEnum.STORE_RECEIVE_TIME;
                        }

                        if(!ObjectUtils.isEmpty(nodeEnum)){
                            //揽收时间、签收时间埋点
                            nodeRecordService.batchInsertNotExist(nodeEnum, DateUtil.dateTimeMinisFormatter.parse(logisticsTrajectory.getRecordTime()),
                                    Collections.singletonList(logisticsTrajectory.getOrigOrderId()), SystemUserResource.getRootUser());
                        }
                    }
                    if (m>0){
                        vh.setCode(ResultCode.SUCCESS);
                        vh.setMessage("oms订单新增物流轨迹成功！,回写物流状态成功！");
                    }else {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage("oms订单新增物流轨迹成功！,回写物流状态失败！");
                    }
                }else {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("oms订单新增物流轨迹失败！");
                }
            }
        }catch (Exception e){
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("oms订单新增物流轨迹异常！");
            vh.setMessage("oms订单新增物流轨迹异常,信息为："+ Throwables.getStackTraceAsString(e));
            log.error("oms订单新增物流轨迹异常,信息为：{}", Throwables.getStackTraceAsString(e));
            return vh;
        }
        if (log.isDebugEnabled()){
            log.debug(this.getClass().getName()+"param,vh:{}",vh);
        }
        return vh;
    }

    /**
     * description:赋值基础信息
     * @Author:  liuwenjin
     * @Date 2021/12/7 8:47 下午
     */
    private void makeCreateField(BaseModel model, User user) {
        Date date = new Date();
        model.setAdClientId((long) user.getClientId());//所属公司
        model.setAdOrgId((long) user.getOrgId());//所属组织
        model.setOwnerid(Long.valueOf(user.getId()));//创建人id
        model.setCreationdate(date);//创建时间
        model.setOwnername(user.getName());//创建人用户名
        model.setModifierid(Long.valueOf(user.getId()));//修改人id
        model.setModifiername(user.getName());//修改人用户名
        model.setModifieddate(date);//修改时间
        model.setIsactive("Y");//是否启用
    }

}
