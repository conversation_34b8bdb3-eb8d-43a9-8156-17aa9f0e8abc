package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderSplitRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * description：jitx发货异常补发
 *
 * <AUTHOR>
 * @date 2021/11/24
 */
@Component
@Slf4j
public class OmsOrderVopResetShipService {
    @Autowired
    private SaveBillService saveBillService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private BuildSequenceUtil sequenceUtil;


    @Autowired
    private OmsOrderItemService omsOrderItemService;


    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private IpBJitxOrderMapper ipBJitxOrderMapper;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    private IpBJitxResetShipWorkflowService ipBJitxResetShipWorkflowService;

    @Autowired
    private OmsOrderLockStockAndReOccupyStockService omsOrderLockStockAndReOccupyStockService;

    public ValueHolderV14 resetShip(String param, User user) {
        log.info("{}.resetShip start:{}", this.getClass().getSimpleName(), param);
        JSONObject jsonObject = JSONObject.parseObject(param);
        JSONArray jsonArray = jsonObject.getJSONArray("ids");
        if (jsonArray == null || jsonArray.isEmpty()) {
            return ValueHolderV14Utils.getFailValueHolder("请选择数据");
        }
        List<Long> idList = new ArrayList<>(jsonArray.size());
        for (int i = 0; i < jsonArray.size(); i++) {
            if (jsonArray.getLong(i) != null) {
                idList.add(jsonArray.getLong(i));
            }
        }
        if (CollectionUtils.isEmpty(idList)) {
            return ValueHolderV14Utils.getFailValueHolder("参数有误");
        }
        int fail = 0;
        if (idList.size() == 1) {
            ValueHolderV14 v14 = this.doFunction(idList.get(0), user);
            return v14;
        } else {
            for (int i = 0; i < idList.size(); i++) {
                try {
                    ValueHolderV14 v14 = this.doFunction(idList.get(i), user);
                    if (!v14.isOK()) {
                        fail++;
                    }
                } catch (Exception e) {
                    fail++;
                }
            }
            return ValueHolderV14Utils.getSuccessValueHolder("选择" + idList.size() + "条," + "成功" + (idList.size() - fail) + "条," + "失败" + fail + "条");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 doFunction(Long id, User user) {
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(id);
                List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectUnSuccessRefund(id);
                ValueHolderV14 v141 = this.checkOrder(ocBOrder, orderItemList, user);
                if (!v141.isOK()) {
                    log.error("错误信息为：" + v141.getMessage());
                    return v141;
                }
                IpBJitxOrder ipBJitxOrder = ipBJitxOrderMapper.selectJitxOrderByOrderSn(ocBOrder.getTid());
                ValueHolderV14 v14 = this.checkJitxOrder(ipBJitxOrder);
                if (v14.isOK()) {
                    boolean jitxOrderShippedFlag =
                            JitxOrderStatus.ORDER_ALREADY_SEND.equals(ipBJitxOrder.getOrderStatus());
                    if (StringUtils.isNotEmpty(ocBOrder.getMergedCode()) && ocBOrder.getQtyAll().compareTo(BigDecimal.ONE) > 0 && orderItemList.size() > 1) {
                        for (OcBOrderItem item : orderItemList) {
                            String resetShipFlag = ipBJitxResetShipWorkflowService.getJITXRedisResetShipFlag(item.getTid());
                            if (StringUtils.isNotEmpty(resetShipFlag)) {
                                return ValueHolderV14Utils.getFailValueHolder("存在未完成的发货重置工单,请稍后再试");
                            }
                        }

                        OcBOrderSplitRelation splitRelation = new OcBOrderSplitRelation();
                        splitRelation.setOrderInfo(ocBOrder);
                        splitRelation.setOrderItemList(orderItemList);
                        splitRelation.setUser(user);
                        splitRelation.setOriginOrderId(ocBOrder.getId());
                        splitRelation.setLogType(OrderLogTypeEnum.JITX_REDELIVERY_SPLIT.getKey());
                        splitRelation.setSplitReason(SplitReason.SPLIT_REDELIVERY);
                        ValueHolderV14 splitResult = omsOrderLockStockAndReOccupyStockService.splitOrigOrder(splitRelation, null);
                        if (splitResult.isOK()) {
                            if (jitxOrderShippedFlag) {
                                ipBJitxResetShipWorkflowService.createByOcBOrder(ocBOrder, orderItemList, user, IpBJitxResetShipWorkflowService.RE_DELIVERY);
                            }
                        }
                    } else {
                        JSONObject object = new JSONObject();
                        object.put("ocBorderDto", ocBOrder);
                        object.put("ocBorderItemDto", orderItemList);
                        object.put("orderId", ocBOrder.getId());
                        //漏发复制
                        object.put("type", 3);
                        object.put("jitx_redelivery", true);
                        ValueHolder valueHolder = saveBillService.saveBill(object, user, Boolean.FALSE);
                        if (valueHolder.isOK()) {
                            if (jitxOrderShippedFlag) {
                                ipBJitxResetShipWorkflowService.createByOcBOrder(ocBOrder, orderItemList, user, IpBJitxResetShipWorkflowService.RE_DELIVERY);
                            }
                            return ValueHolderV14Utils.getSuccessValueHolder("异常发货补发创建成功");
                        } else {
                            return ValueHolderV14Utils.getFailValueHolder(String.format("异常发货补发创建失败：%s", valueHolder.get("message")));
                        }
                    }
                    return ValueHolderV14Utils.getSuccessValueHolder("异常发货补发创建成功");
                } else {
                    return v14;
                }
            } else {
                log.error("订单id:{}插入重置发货数据失败,当前订单其他人在操作，请稍后再试", id);
                return ValueHolderV14Utils.getFailValueHolder("当前订单其他人在操作，请稍后再试");
            }
        } catch (Exception ex) {
            log.error("{},订单id:{},异常{}", this.getClass().getSimpleName(), id, Throwables.getStackTraceAsString(ex));
            throw new NDSException(ex.getMessage());
        } finally {
            redisLock.unlock();
        }
    }

    public ValueHolderV14 checkJitxOrder(IpBJitxOrder ipBJitxOrder) {
        if (ipBJitxOrder == null) {
            return ValueHolderV14Utils.getFailValueHolder("JITX订单不存在");
        }
        if (!OcBOrderConst.IS_ACTIVE_YES.equals(ipBJitxOrder.getIsactive())) {
            return ValueHolderV14Utils.getFailValueHolder("JITX订单已作废");
        }
        boolean canDevelReplenishmentShipment =
                JitxOrderStatus.ORDER_ALREADY_AUDITED.equals(ipBJitxOrder.getOrderStatus())
                        || JitxOrderStatus.ORDER_ALREADY_SEND.equals(ipBJitxOrder.getOrderStatus());
        if (!canDevelReplenishmentShipment) {
            return ValueHolderV14Utils.getFailValueHolder("当前仅支持JITX订单【已发货】或者【已审核】进行发货异常补发！");
        }
        return ValueHolderV14Utils.getSuccessValueHolder("校验jitx订单成功");
    }

    /**
     * description：校验订单数据
     *
     * <AUTHOR>
     * @date 2021/11/26
     */
    public ValueHolderV14 checkOrder(OcBOrder ocBOrder, List<OcBOrderItem> itemList, User user) {
        log.info("{},jitx发货异常补发校验开始,orderId:{}", this.getClass().getSimpleName(), ocBOrder.getId());
        if (ocBOrder == null) {
            log.error("{} 订单ID：{} 订单不存在", this.getClass().getSimpleName(), ocBOrder.getId());
            return ValueHolderV14Utils.getFailValueHolder("订单不存在");
        }

        if (CollectionUtils.isEmpty(itemList)) {
            return ValueHolderV14Utils.getFailValueHolder("订单不存在有效明细");
        }
        String tid = ocBOrder.getTid();
        if (StringUtils.isBlank(tid)) {
            return ValueHolderV14Utils.getFailValueHolder("订单平台单号为空");
        }

        if (!PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
            return ValueHolderV14Utils.getFailValueHolder("当前仅支持JITX订单进行发货异常补发！");
        }
        if (!(OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus()) || OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus()))) {
            return ValueHolderV14Utils.getFailValueHolder("当前仅支持状态为“仓库发货”或“平台发货”的JITX单据进行异常补发！");
        }

        List<IpBJitxResetShipWorkflow> shipWorkflows = ipBJitxResetShipWorkflowService.existReDeliveryWorkflow(ocBOrder.getId());
        if (CollectionUtils.isNotEmpty(shipWorkflows)) {
            return ValueHolderV14Utils.getFailValueHolder("该笔订单已操作过异常补发,请勿重复补发！");
        }
        //当前单据的退换货单未完成，不可进行门店补发！
        LambdaQueryWrapper<OcBReturnOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OcBReturnOrder::getTid, StringUtils.isEmpty(ocBOrder.getMergeSourceCode()) ? tid : ocBOrder.getMergeSourceCode());
        queryWrapper.eq(OcBReturnOrder::getOrigOrderId, ocBOrder.getId());
        queryWrapper.eq(OcBReturnOrder::getReturnStatus, ReturnStatusEnum.COMPLETION.getVal());
        List<OcBReturnOrder> returnOrderList = ocBReturnOrderMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(returnOrderList)) {
            return ValueHolderV14Utils.getFailValueHolder("当前单据的退换货单未完成，不可进行门店补发！");
        }
        //当前单据的退换货单未将原单的退货明细及数量进行全退，不可进行门店补发！
        List<Long> returnOrderIdList = returnOrderList.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
        String join = StringUtils.join(returnOrderIdList, ",");
        List<OcBReturnOrderRefund> rfnList = ocBReturnOrderRefundMapper.selectReturnRefundByPIds(join);
        Map<String, BigDecimal> refundSkuQtyMap = new HashMap<>(rfnList.size());
        //退换货单明细sku及入库数量
        for (OcBReturnOrderRefund refund : rfnList) {
            BigDecimal existQty = refundSkuQtyMap.get(refund.getPsCSkuEcode());
            BigDecimal qtyIn = refund.getQtyIn() == null ? BigDecimal.ZERO : refund.getQtyIn();
            if (existQty == null) {
                refundSkuQtyMap.put(refund.getPsCSkuEcode(), qtyIn);
            } else {
                refundSkuQtyMap.put(refund.getPsCSkuEcode(), existQty.add(refund.getQtyIn()));
            }
        }
        //发货单明细sku及数量
        Map<String, BigDecimal> orderSkuQtyMap = new HashMap<>(itemList.size());
        for (OcBOrderItem item : itemList) {
            BigDecimal existQty = orderSkuQtyMap.get(item.getPsCSkuEcode());
            BigDecimal qty = item.getQty() == null ? BigDecimal.ZERO : item.getQty();
            if (existQty == null) {
                orderSkuQtyMap.put(item.getPsCSkuEcode(), qty);
            } else {
                orderSkuQtyMap.put(item.getPsCSkuEcode(), existQty.add(item.getQty()));
            }

        }
        boolean isDifferentSkuOrQty = false;
        for (String k : orderSkuQtyMap.keySet()) {
            BigDecimal qtyIn = refundSkuQtyMap.get(k);
            BigDecimal qty = orderSkuQtyMap.get(k);
            if (qtyIn == null || qtyIn.compareTo(qty) != 0) {
                isDifferentSkuOrQty = true;
                break;
            }
        }
        if (isDifferentSkuOrQty) {
            return ValueHolderV14Utils.getFailValueHolder("当前单据的退换货单未将原单的退货明细及数量进行全退，不可进行门店补发！");
        }
        return ValueHolderV14Utils.getSuccessValueHolder("校验成功");
    }

    public ValueHolderV14 checkJitxMergedOrder(OcBOrder ocBOrder, List<OcBOrderItem> itemList) {
        if (StringUtils.isNotEmpty(ocBOrder.getMergedCode()) && ocBOrder.getQtyAll().compareTo(BigDecimal.ONE) > 0 && itemList.size() > 1) {
            for (OcBOrderItem item : itemList) {
                String resetShipFlag = ipBJitxResetShipWorkflowService.getJITXRedisResetShipFlag(item.getTid());
                if (StringUtils.isNotEmpty(resetShipFlag)) {
                    return ValueHolderV14Utils.getFailValueHolder("存在未完成的发货重置工单,无法重置发货");
                }
            }
            return ValueHolderV14Utils.getSuccessValueHolder("当前订单是合包订单,需重置发货");
        } else {
            return ValueHolderV14Utils.getFailValueHolder("当前订单非合包订单,不需重置发货");
        }
    }
}
