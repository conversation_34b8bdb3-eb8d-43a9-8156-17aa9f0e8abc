package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderInvoiceInformMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.request.SaveRecordInvoiceRequest;
import com.jackrain.nea.oc.oms.model.result.QueryOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrderInvoiceInform;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 记录开票.全渠道订单
 *
 * @author: xiWen.z
 * create at: 2019/7/24 0024
 */
@Component
@Slf4j
public class OcBOrderRecordInvoicingService {

    @Autowired
    private OcBOrderInvoiceInformMapper invoiceInformMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    /**
     * @param requestModel SaveRecordInvoiceRequest
     * @param usr          User
     * @return vh14
     */
    public ValueHolderV14 saveRecordInvoicing(SaveRecordInvoiceRequest requestModel, User usr) {

        // 1. 参数校验
        ValueHolderV14<SaveRecordInvoiceRequest> v1 = validateParams(requestModel, usr);
        if (ResultCode.FAIL == v1.getCode()) {
            return v1;
        }

        // 2. 新增
        try {
            Set<Long> smOs = dealSameSourceCodeOrder(v1.getData().getQueryOrderResult().getId());
            OcBOrderRecordInvoicingService bean
                    = ApplicationContextHandle.getBean(OcBOrderRecordInvoicingService.class);
            bean.saveOcBOrderInvoiceInform(v1.getData(), usr, smOs);
        } catch (Exception e) {
            log.error(LogUtil.format("###saveRecordInvoicing. Error：{} "), Throwables.getStackTraceAsString(e));
            return valueResult("记录失败", false, usr);
        }
        return valueResult("记录成功", true, usr);

    }

    /**
     * 参数.校验
     *
     * @param invoiceInform SaveRecordInvoiceRequest
     * @param usr           User
     * @return vh14
     */
    private ValueHolderV14<SaveRecordInvoiceRequest> validateParams(SaveRecordInvoiceRequest invoiceInform, User usr) {
        if (usr == null || invoiceInform == null) {
            ValueHolderV14 v = new ValueHolderV14();
            v.setMessage("必填信息不能为空");
            v.setCode(ResultCode.FAIL);
            recordDebugLog("###" + this.getClass().getName() + "validateParams. SaveRecordInvoiceRequest Is Null"
                    + "Or User Is Null");
            return v;
        }

        QueryOrderResult qor = invoiceInform.getQueryOrderResult();
        if (qor == null || qor.getId() == null) {
            recordDebugLog("###" + this.getClass().getName() + "validateParams. QueryOrderResult Or ID Is Null ");
            return valueResult("订单信息缺失", false, usr);
        }

        OcBOrderInvoiceInform oif = invoiceInform.getOcBOrderInvoiceInform();
        if (oif == null) {
            recordDebugLog("###" + this.getClass().getName() + "validateParams. Model:OcBOrderInvoiceInform "
                    + "In SaveRecordInvoiceRequest is Null");
            return valueResult("必填信息不能为空", false, usr);
        }
        // 1. 抬头
        String headerName = oif.getHeaderName();
        if (StringUtils.isBlank(headerName)) {
            return valueResult("发票抬头信息不能为空", false, usr);
        }
        // 2.1 发票类型
        Integer invoiceType = oif.getInvoiceType();
        Integer headerType = oif.getHeaderType();
        if (invoiceType == null) {
            return valueResult("发票类型不能为空", false, usr);
        } else {
            Set<Integer> allEnumValueToSet = OcBorderListEnums.InvoiceTypeEnum.getAllEnumValueToSet();
            if (allEnumValueToSet.add(invoiceType)) {
                return valueResult("未知发票类型", false, usr);
            }
        }

        // 2.2 抬头类型
        if (headerType != null && OcBorderListEnums.HeaderTypeEnum.COMPANY.getVal().equals(headerType)) {
            Set<Integer> allEnumValueToSet = OcBorderListEnums.HeaderTypeEnum.getAllEnumValueToSet();
            if (allEnumValueToSet.add(headerType)) {
                return valueResult("未知抬头类型", false, usr);
            }
            String taxpayerNo = oif.getTaxpayerNo();
            if (StringUtils.isBlank(taxpayerNo)) {
                return valueResult("抬头类型为企业,识别号不能为空", false, usr);
            }
        }

        // 专用发票
        String msg = null;
        specialLabel:
        if (OcBorderListEnums.InvoiceTypeEnum.SPECIAL_INVOICE.getVal().equals(invoiceType)) {
            if (StringUtils.isBlank(oif.getTaxpayerNo())) {
                msg = "专用发票,识别号不能为空";
                break specialLabel;
            }
            if (StringUtils.isBlank(oif.getPhoneNo())) {
                msg = "专用发票,电话号码不能为空";
                break specialLabel;
            }
            if (StringUtils.isBlank(oif.getOpeningBank())) {
                msg = "专用发票,开户银行不能为空";
                break specialLabel;

            }
            if (StringUtils.isBlank(oif.getOpeningBankAccount())) {
                msg = "专用发票,开户行账号不能为空";
                break specialLabel;

            }
            if (StringUtils.isBlank(oif.getCompany())) {
                msg = "专用发票,公司地址不能为空";
            }
        }
        if (StringUtils.isNotBlank(msg)) {
            return valueResult(msg, false, usr);
        }

        ValueHolderV14 vh = valueResult("success", true, usr);
        vh.setData(invoiceInform);
        return vh;
    }

    /**
     * 数据操作
     *
     * @param requestModel SaveRecordInvoiceRequest
     * @param usr          User
     */
    @Transactional(rollbackFor = NDSException.class)
    public void saveOcBOrderInvoiceInform(SaveRecordInvoiceRequest requestModel, User usr,
                                          Set<Long> smOs) {
        recordDebugLog("###" + this.getClass().getName() + "###saveOcBOrderInvoiceInform.开始数据更新");
        if (smOs == null) {
            throw new NDSException("数据异常, 该平台单号未找到相关数据");
        }
        // 1. 子处理
        Long ocBOrderId = requestModel.getQueryOrderResult().getId();
        if (ocBOrderId == null) {
            throw new NDSException("更新数据时,发生异常:OcBOrderId Is Null");
        }
        OcBOrderInvoiceInform invoice = requestModel.getOcBOrderInvoiceInform();
        Long id = invoice.getId();
        boolean flag = false;
        // 修改
        if (id != null && id > OcBOrderConst.IS_STATUS_IN) {
            invoice.setId(null);
            invoice.setOcBOrderId(null);
            invoice.setModifierename(usr.getEname());
            invoice.setModifieddate(new Date());
            invoice.setModifierid(Long.valueOf(usr.getId()));
            invoice.setModifiername(usr.getName());
            flag = true;
        } else {
            invoice.setCreationdate(new Date());
            invoice.setOwnerid(Long.valueOf(usr.getId()));
            invoice.setOwnername(usr.getName());
            invoice.setOwnerename(usr.getEname());
            invoice.setModifieddate(new Date());
            invoice.setModifiername(usr.getName());
            invoice.setModifierename(usr.getEname());
            invoice.setModifierid(Long.valueOf(usr.getId()));
            invoice.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
            invoice.setAdClientId(Long.valueOf(usr.getClientId()));
            invoice.setAdOrgId(Long.valueOf(usr.getOrgId()));
        }

        // 3.0 日志
        OcBOrderLog orderLog = new OcBOrderLog();
        orderLog.setBillNo(requestModel.getQueryOrderResult().getBillNo());
        orderLog.setUserName(usr.getName());
        orderLog.setLogType(OrderLogTypeEnum.RECORD_INVOICE_TYPE.getKey());
        String msg = "记录开票信息成功";
        if (flag) {
            msg = "记录开票信息修改成功";
        }
        orderLog.setLogMessage(msg);
        orderLog.setIpAddress(usr.getLastloginip());
        orderLog.setOwnerid(Long.valueOf(usr.getId()));
        orderLog.setOwnerename(usr.getEname());
        orderLog.setOwnername(usr.getName());
        orderLog.setCreationdate(new Date());
        orderLog.setModifieddate(new Date());
        orderLog.setModifierename(usr.getEname());
        orderLog.setModifiername(usr.getName());
        orderLog.setModifierid(Long.valueOf(usr.getId()));
        orderLog.setAdClientId(Long.valueOf(usr.getClientId()));
        orderLog.setAdOrgId(Long.valueOf(usr.getOrgId()));
        orderLog.setIsactive(OcBOrderConst.IS_ACTIVE_YES);

        recordDebugLog("###" + this.getClass().getName() + "###saveOcBOrderInvoiceInform.参数备参结束");
        try {
            if (flag) {
                StringBuilder sb = new StringBuilder();
                for (Long smO : smOs) {
                    sb.append(",");
                    sb.append(smO);
                }
                String hashKeys = sb.substring(OcBOrderConst.IS_STATUS_IY);
                String voiceString = JSON.toJSONStringWithDateFormat(invoice,
                        "yyyy-MM-dd HH:mm:ss");
                JSONObject ivcJsn = JSONObject.parseObject(voiceString);

                ivcJsn.put("refId", hashKeys);
                invoiceInformMapper.updateInvoiceInformByFids(ivcJsn);
            } else {
                for (Long oid : smOs) {
                    Long mulId = Tools.getSequence("oc_b_order_invoice_inform");
                    invoice.setId(mulId);
                    invoice.setOcBOrderId(oid);
                    invoiceInformMapper.insert(invoice);
                    ocBOrderMapper.updateOcBorderCaseByInvoice(OcBOrderConst.IS_STATUS_IY, oid);
                }
            }
            for (Long oid : smOs) {
                Long logId = Tools.getSequence("oc_b_order_log");
                orderLog.setId(logId);
                orderLog.setOcBOrderId(oid);
                omsOrderLogService.save(Collections.singletonList(orderLog));
            }
            recordDebugLog("###" + this.getClass().getName() + "###saveOcBOrderInvoiceInform.数据更新结束");
/*
            if (!flag) {
                JSONObject jo = new JSONObject();
                jo.put("IS_INVOICE", OcBOrderConst.IS_STATUS_IY);
                SpecialElasticSearchUtil.updateDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                        OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, jo, ocBOrderId);
            }*/
//            SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
//                    OcElasticSearchIndexResources.OC_B_ORDER_LOG_TYPE_NAME, orderLog, orderLog.getId(), ocBOrderId);
            recordDebugLog("###" + this.getClass().getName() + "###saveOcBOrderInvoiceInform.ES更新结束");
        } catch (Exception e) {
            log.error(LogUtil.format("###saveOcBOrderInvoiceInform. Error: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("更新数据时,发生异常: " + e);
        }

    }

    /**
     * @param msg       String
     * @param flag      bool
     * @param loginUser User
     * @return vh14
     */
    private ValueHolderV14 valueResult(String msg, boolean flag, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (flag) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        }
        return vh;
    }

    /**
     * 记录debug级别日志
     *
     * @param msg String
     */
    private void recordDebugLog(String msg) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(msg));
        }
    }

    /**
     * 处理. 容错. 旧数据
     *
     * @param pId
     * @return
     */
    private Set<Long> dealSameSourceCodeOrder(Long pId) {

        String s = ocBOrderMapper.selectSourceCodeById(pId, OcBOrderConst.IS_ACTIVE_YES);
        if (StringUtils.isBlank(s)) {
            return null;
        }

        /*JSONArray esRst = ES4Order.queryIdBySourceCode(s);
        String ids = joinEsSearchResult(esRst);*/

        List<Long> idsList = GSI4Order.getIdListBySourceCode(s);
        String ids = StringUtils.join(idsList, ",");
        if (StringUtils.isBlank(ids)) {
            return null;
        }
        List<QueryOrderResult> orderResult = ocBOrderMapper.orderListQueryByIds(ids, OcBOrderConst.IS_ACTIVE_YES);
        // 不处理.旧数据
        Set<Long> pIdSet = new HashSet<>();
        // StringBuilder sb = new StringBuilder();
        for (QueryOrderResult o : orderResult) {
            if (o == null) {
                continue;
            }
            if (judgeOrderStatus(o)) {
                pIdSet.add(o.getId());
            /*    sb.append(",");
                sb.append(o.getId());*/
            }
        }
        if (pIdSet.size() < OcBOrderConst.IS_STATUS_IY) {
            return null;
        }
        return pIdSet;
       /*    String s1 = sb.toString();
        if (StringUtils.isBlank(s1)) {
            return null;
        }
        Map<Long, List<OcBOrderInvoiceInform>> iMp = getOcBOrderInvoiceInform(s1.substring(OcBOrderConst.IS_STATUS_IY));
        if (iMp == null) {
            return null;
        }
        Map<String, Set<Long>> prevMap = new HashMap<>();
        Set<Long> existOid = iMp.keySet();
        for (Long oId : existOid) {
            if (pIdSet.contains(oId)) {
                pIdSet.remove(oId);
            }
        }
        //  prevMap.put("update", existOid); 只插入,更新,
        prevMap.put("insert", pIdSet);

        return prevMap;*/
    }

    /**
     * get ids
     *
     * @param ary
     * @return
     */
    private String joinEsSearchResult(JSONArray ary) {

        if (ary == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < ary.size(); i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            Long id = o.getLong("ID");
            if (id == null) {
                continue;
            }
            sb.append(",");
            sb.append(id);
        }
        String s = sb.toString();
        if (StringUtils.isBlank(s)) {
            return null;
        }
        return s.substring(OcBOrderConst.IS_STATUS_IY);
    }

    /**
     * 未登记
     *
     * @param qor
     * @return
     */
    private boolean judgeOrderStatus(QueryOrderResult qor) {
        Integer orderStatus = qor.getOrderStatus();
        if (orderStatus == null) {
            return false;
        }
        if (OcOrderCheckBoxEnum.CHECKBOX_SYSTEM_INVALIDATION.getVal() == orderStatus) {
            return false;
        }
        Integer invoiceStatus = qor.getInvoiceStatus();
        if (invoiceStatus != null) {
            return OcBorderListEnums.InvoiceStatusEnum.UN_REGISTER.getVal().equals(invoiceStatus);
        }
        return true;
    }

    /**
     * 查询.记录信息
     *
     * @param refIds refIds
     * @return Map
     */
    private Map<Long, List<OcBOrderInvoiceInform>> getOcBOrderInvoiceInform(String refIds) {
        Map<Long, List<OcBOrderInvoiceInform>> m;
        List<OcBOrderInvoiceInform> invoiceList = invoiceInformMapper.selectInvoiceInformByRids(refIds,
                OcBOrderConst.IS_ACTIVE_YES);

        if (invoiceList == null || invoiceList.size() < OcBOrderConst.IS_STATUS_IY) {
            return null;
        }
        m = new HashMap<>();
        Set<Long> set = new HashSet<>();
        for (OcBOrderInvoiceInform o : invoiceList) {
            Long oId = o.getOcBOrderId();
            if (oId == null) {
                continue;
            }
            if (set.add(oId)) {
                List<OcBOrderInvoiceInform> lt = new ArrayList<>();
                lt.add(o);
                m.put(oId, lt);
                continue;
            }
            List<OcBOrderInvoiceInform> lt1 = m.get(oId);
            if (lt1 != null) {
                lt1.add(o);
            }
        }
        if (m != null && m.size() > OcBOrderConst.IS_STATUS_IN) {
            return m;
        }
        return null;

    }
}
