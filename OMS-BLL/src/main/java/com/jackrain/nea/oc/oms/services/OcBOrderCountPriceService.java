package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 淘宝预售订单金额计算
 *
 * @date 2019/10/23
 * @author: ming.fz
 */
@Component
@Slf4j
public class OcBOrderCountPriceService {

    /**
     * 订单明细金额计算
     *
     * @param orderItems
     * @param omsOdr
     * @return
     */
    public List<OcBOrderItem> encapsulationParameter(List<OcBOrderItem> orderItems, OcBOrder omsOdr) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("预售金额计算开始"));
        }
        for (OcBOrderItem item : orderItems) {
            //成交价格
            item.setPrice(calcOrderItemPrice(item));
        }
        setItemOrderSplitMat(omsOdr, orderItems);
        return orderItems;
    }

    /**
     * 【成交价格】： 订单明细表的（“标准价”*“数量”-“优惠费用”+“调整费用”）/“数量”
     *
     * @param item 订单明细
     * @return 成交价格
     */
    private BigDecimal calcOrderItemPrice(OcBOrderItem item) {
        BigDecimal bigOriginalPrice = item.getPriceList() == null ? BigDecimal.ZERO : item.getPriceList();
        BigDecimal bigPrice = bigOriginalPrice.multiply(item.getQty());
        bigPrice = bigPrice.subtract(
                item.getAmtDiscount() == null ? BigDecimal.ZERO : item.getAmtDiscount()
        );
        bigPrice = bigPrice.add(
                item.getAdjustAmt() == null ? BigDecimal.ZERO : item.getAdjustAmt());
        bigPrice = bigPrice.divide(item.getQty(), 2, RoundingMode.HALF_EVEN);
        return bigPrice;
    }


    /**
     * 计算和赋值订单明细整单平摊金额 并赋值单行实际成交金额
     *
     * @param orderInfo
     * @param orderItemList
     */
    private void setItemOrderSplitMat(OcBOrder orderInfo, List<OcBOrderItem> orderItemList) {
        //去除最后一条明细所有明细优惠金额总和
        BigDecimal countPrice = BigDecimal.ZERO;
        //所有明细实际成交金额总和
        BigDecimal discountCountPrice = BigDecimal.ZERO;
        //主表的订单优惠金额
        BigDecimal orderDiscountAmt = orderInfo.getOrderDiscountAmt() == null ? BigDecimal.ZERO : orderInfo.getOrderDiscountAmt();
        for (int i = 0, length = orderItemList.size(); i < length; i++) {
            OcBOrderItem ocBOrderItem = orderItemList.get(i);
            if (length - 1 == i) {
                //最后一条商品的【优惠平摊】= 【主表的订单优惠金额 - 其余所有优惠平摊之和】
                //赋值明细整单平摊金额
                ocBOrderItem.setOrderSplitAmt(orderDiscountAmt.subtract(countPrice));
            } else {
                //【优惠平摊】 = 主表的订单优惠金额 * （成交金额 / 所有商品明细的成交金额之和）
                //【优惠平摊】 = 主表的订单优惠金额 * （标准价*数量 / 所有标准价*数量之和）
                if (i == 0) {
                    for (OcBOrderItem standItem : orderItemList) {
                        BigDecimal price = standItem.getPriceList() == null ? BigDecimal.ZERO : standItem.getPriceList();
                        BigDecimal num = standItem.getQty() == null ? BigDecimal.ZERO : standItem.getQty();
                        discountCountPrice = discountCountPrice.add(price.multiply(num));
                    }
                }

                BigDecimal price = ocBOrderItem.getPriceList() == null ? BigDecimal.ZERO : ocBOrderItem.getPriceList();
                BigDecimal num = ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty();
                //标准价乘以数量
                BigDecimal realAmt = price.multiply(num);
                //计算当前明细所占总优惠金额的比例金额保留两位小数
                BigDecimal ratio = realAmt.divide(discountCountPrice, 2, RoundingMode.HALF_EVEN);
                //计算当前明细优惠整单平摊金额
                BigDecimal multiply = orderDiscountAmt.multiply(ratio);
                //赋值明细整单平摊金额
                ocBOrderItem.setOrderSplitAmt(multiply);

                //计算除集中最后一条明细优惠金额之和
                countPrice = countPrice.add(multiply);
            }
            //单行实际成交金额.
            ocBOrderItem.setRealAmt(this.calcOrderItemRealAmount(ocBOrderItem));
        }
    }

    /**
     * 单行实际成交金额. 成交价格*数量-平摊金额
     *
     * @param
     * @return
     */
    private BigDecimal calcOrderItemRealAmount(OcBOrderItem orderItem) {
        BigDecimal price = orderItem.getPrice() == null ? BigDecimal.ZERO : orderItem.getPrice();
        BigDecimal multiply = price.multiply(orderItem.getQty());
        BigDecimal subtract = multiply.subtract(orderItem.getOrderSplitAmt());
        return subtract;
    }
}