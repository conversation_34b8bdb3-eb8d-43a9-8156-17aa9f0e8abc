package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBLowTemperatureOccupyTaskMapper;
import com.jackrain.nea.oc.oms.model.table.OcBLowTemperatureOccupyTask;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ClassName OcBLowTemperatureOccupyTaskService
 * @Description 低温寻源
 * <AUTHOR>
 * @Date 2024/5/10 14:17
 * @Version 1.0
 */
@Component
public class OcBLowTemperatureOccupyTaskService {

    @Autowired
    private OcBLowTemperatureOccupyTaskMapper lowTemperatureOccupyTaskMapper;

    public void addLowTemperaturOcBOccupyTask(OcBOrder order, Date nextTime, Integer itemNum) {
        OcBLowTemperatureOccupyTask occupyTask = new OcBLowTemperatureOccupyTask();
        occupyTask.setId(ModelUtil.getSequence("oc_b_low_temperature_occupy_task"));
        occupyTask.setOrderId(order.getId());
        occupyTask.setBusinessTypeId(order.getBusinessTypeId());
        occupyTask.setBusinessTypeCode(order.getBusinessTypeCode());
        occupyTask.setBusinessTypeName(order.getBusinessTypeName());
        occupyTask.setIsCycle(order.getIsCycle());
        occupyTask.setTid(order.getTid());
        // 基数明细行数
        occupyTask.setItemNum(itemNum);
        occupyTask.setBillNo(order.getBillNo());
        occupyTask.setBillNo(order.getBillNo());
        occupyTask.setNextTime(new Date());
        occupyTask.setCreationdate(new Date());
        lowTemperatureOccupyTaskMapper.insert(occupyTask);

    }

}
