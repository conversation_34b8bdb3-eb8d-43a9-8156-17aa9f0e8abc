package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.sap.ReturnSyncTask;
import com.jackrain.nea.rpc.CpRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * oms  同步第三方系统
 *
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/25
 */
@Slf4j
@Component
public class OmsSyncTh3SysTaskService {


    @Autowired
    private CpRpcService cpRpcService;
    /**
     * 参数
     *
     * @param order
     * @return
     */
    private ReturnSyncTask isSyncTask(OcBReturnOrder order) {

        ReturnSyncTask task = null;
        if (isPosCtrlWarehouse(order)) {
            task = new ReturnSyncTask();
            task.setIsNextTao(1);
        }
        /*if (isSapCtrlWarehouse(order)) {
            task = task == null ? new ReturnSyncTask() : task;
        }*/

        return task;
    }

    /**
     * 是否是pos管控仓
     *
     * @param order
     * @return
     */
    private boolean isPosCtrlWarehouse(OcBReturnOrder order) {

        String storeCode = order.getStoreCode();
        if (StringUtils.isBlank(storeCode)) {
            return false;
        }
        CpCPhyWarehouse cpCPhyWarehouse =
                cpRpcService.queryByWarehouseId(order.getCpCPhyWarehouseInId());
        return cpCPhyWarehouse != null && StringUtils.equals("1", cpCPhyWarehouse.getIsPos());

    }


}
