package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

//import com.jackrain.nea.st.api.StOrderAutoRefundCmd;

@Slf4j
@Component
public abstract class AbstractOmsOrderPolicyService {

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private StRpcService stRpcService;

    /**
     * 发货前的状态
     *
     * @param orderStatus
     * @return
     */
    public boolean checkOrderStatusFront(Integer orderStatus) {
        /*【待分配】50,【待审核】1,【已审核】3,【缺货(待寻源)】2，【配货中】4，【传WMS中】21*/
        return OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                || OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)
                || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)
                || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus);
    }

    /**
     * 添加日志
     *
     * @param orderId
     * @param logType
     * @param msg
     */
    public void addReturnOrderAutoRefundLog(Long orderId, OrderLogTypeEnum logType, String msg, User user) {
        omsOrderLogService.addUserOrderLog(orderId, null, logType.getKey(), msg, null, null,
                user);
    }

    /**
     * 检查ag
     *
     * @param shopId
     * @return
     */
    public boolean isToAgByShopStrategy(Long shopId) {
        return stRpcService.isToAgByShopStrategy(shopId);
    }
}
