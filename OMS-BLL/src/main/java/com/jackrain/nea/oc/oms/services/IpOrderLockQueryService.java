package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.oc.oms.es.ES4IpOrderLock;
import com.jackrain.nea.oc.oms.mapper.IpBOrderLockMapper;
import com.jackrain.nea.oc.oms.model.resources.LockOrderConstant;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLock;
import com.jackrain.nea.oc.oms.nums.OrderLockLogTypeEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 淘宝锁单服务类
 * @Date 2019-10-10
 **/
@Component
@Slf4j
public class IpOrderLockQueryService {

    @Autowired
    private IpBOrderLockMapper lockMapper;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private IpOrderLockService ipOrderLockService;

    /**
     * @param pageIndex
     * @param pageSize
     * @return java.util.List<java.lang.String>
     * @Description 获取淘宝中间表分库键
     * <AUTHOR>
     * @date 2019/10/9 15:10
     */
    public List<Long> selectLockKey(int pageIndex, int pageSize) {
        String indexName = OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME;
//        if (!SpecialElasticSearchUtil.indexExists(indexName)) {
//            try {
//                SpecialElasticSearchUtil.indexCreate(IpBOrderLock.class);
//            } catch (IOException e) {
//                log.debug("创建淘宝锁单中间表索引失败！" + e.getMessage());
//            }
//        }
        return ES4IpOrderLock.selectLockKeyBy(pageIndex, pageSize);
    }

    /**
     * @param total 单程处理数
     * @return
     * @Description 处理退货入库超时未入库 设置异常状态
     * <AUTHOR>
     * @date 2019-09-25 2019-09-25
     */
    public ValueHolderV14 dealLockOrderLockTime(int total) {
        log.debug("dealLockOrderLockTime定时任务开始执行》》》" + "设置的数据量为" + total + "条；");
        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            List<Long> idList = ES4IpOrderLock.selectLockOrderIdListFromES(0, total);
            log.info(this.getClass().getName() + " 定时任务拉取待初始化解锁时间锁单数量：" + idList.size());
            if (CollectionUtils.isEmpty(idList)) {
                log.info(this.getClass().getName() + " 锁单预计解锁时间初始化任务-查询待初始化预计解锁时间列表为空；任务结束！");
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("自动化任务完成,查询待初始化预计解锁时间列表为空！");
                return v14;
            }

            List<IpBOrderLock> lockList = lockMapper.selectBatchIds(idList);
            Map<Long, List<IpBOrderLock>> shopLockOrders = lockList.stream()
                    .collect(Collectors.groupingBy(x -> x.getCpCShopId()));
            for (Map.Entry<Long, List<IpBOrderLock>> entry : shopLockOrders.entrySet()) {
                List<IpBOrderLock> curLockList = entry.getValue();
                Long shopId = entry.getKey();
                // 调用店铺策略获取参数
                StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(shopId);
                if (shopStrategy == null) {
                    log.info(this.getClass().getName() + " 锁单预计解锁时间初始化任务-店铺Id" + shopId + "店铺策略为空；该店铺不初始化预计解锁时间！");
                    continue;
                }

                String isEnableUnlock = shopStrategy.getIsEnableUnlock();
                if ("Y".equals(isEnableUnlock) && shopStrategy.getLockDays() != null) {
                    int days = shopStrategy.getLockDays().intValue();
                    List<String> curIdList = curLockList.stream().map(x -> x.getId().toString()).collect(Collectors.toList());
                    String ids = curIdList.stream().collect(Collectors.joining(","));
                    int update = lockMapper.updateLockOrderByBatchSQL(ids, days, LockOrderConstant.ABLE_UNLOCK);
                    if (update < 0) {
                        log.error(this.getClass().getName() + " 锁单ids：" + ids + "更新数据库解锁时间失败！");
                    } else {
                        List<IpBOrderLock> esLockList = lockMapper.selectBatchIds(curIdList);
                        //重推ES
//                        SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
//                                OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME, esLockList);
                        for (String id : curIdList) {
                            //记录日志
                            String errMsg = "预计解锁时间初始化成功";
                            ipOrderLockService.insetIpOrderLockLog(OrderLockLogTypeEnum.INITIALIZE.getKey(), errMsg, Long.valueOf(id), null, SystemUserResource.getRootUser());

                        }

                    }
                } else {
                    log.info(this.getClass().getName() + " 锁单预计解锁时间初始化任务-店铺Id" + shopId + "店铺策略未启用自动解锁");
                }
            }


            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("自动化任务完成！");
        } catch (Exception ex) {
            log.error(this.getClass().getName() + " 锁单预计解锁时间初始化任务：" + ex.toString());
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("自动化任务异常！");
        }
        return v14;
    }


}
