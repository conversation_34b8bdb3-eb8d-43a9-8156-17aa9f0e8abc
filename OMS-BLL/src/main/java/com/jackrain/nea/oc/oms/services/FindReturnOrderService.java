package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.constant.OcOmsConstant;
import com.jackrain.nea.oc.oms.model.enums.InterceptStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnProTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: 郑立轩
 * @since: 2019/4/30
 * create at : 2019/4/30 14:04
 */
@Slf4j
@Component
public class FindReturnOrderService {
    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsReturnOrderService omsReturnOrderService;

    @Autowired
    private IpRpcService service;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;


    public List<OcBReturnOrder> findReturnOrderService(Integer range) {
        JSONArray esSearchResult = ES4ReturnOrder.QueryOrderReturnIds(range);
        String join = StringUtils.join(esSearchResult, ",");
        // @20200721 如果为空，不要再查数据库，避免报错
        if (StringUtils.isEmpty(join)) {
            return null;
        }
        List<OcBReturnOrder> returnOrders = returnOrderMapper.findByJoin(join);

        // @20200706 加系统参数控制传WMS是否需要校验物流单号非空
        boolean logisticsStrict = isLogisticsStrict();
        //2.获取实体仓id
        List<Long> preUpdateReturnIds = new ArrayList<>();
        for (int i = returnOrders.size() - 1; i >= 0; i--) {
            OcBReturnOrder ocBReturnOrder = returnOrders.get(i);
            //去除CP_C_LOGISTICS_ECODE的非空校验，增加对is_towms=2的校验，为了防止ES同步太慢，把传WMS成功的重新传一遍
            if (!checkRemove(logisticsStrict, ocBReturnOrder) || WmsWithdrawalState.YES.toInteger().equals(ocBReturnOrder.getIsTowms())) {
                preUpdateReturnIds.add(ocBReturnOrder.getId());
                returnOrders.remove(i);
            }
        }
        if (CollectionUtils.isNotEmpty(preUpdateReturnIds)) {
            updateReturnOrderModifiedDate(preUpdateReturnIds);
        }
        return returnOrders;
    }

    /**
     * <AUTHOR>
     * @Date 16:43 2021/5/20
     * @Description 验证不传wms的条件
     */
    private boolean checkRemove(boolean logisticsStrict, OcBReturnOrder ocBReturnOrder) {
        if (logisticsStrict) {
            if (StringUtils.isNotEmpty(ocBReturnOrder.getLogisticsCode())) {
                return true;
            }
            return false;
        } else {
            if (StringUtils.isNotEmpty(ocBReturnOrder.getLogisticsCode())) {
                return true;
            } else {
                if (checkPushDelayTime(ocBReturnOrder)) {
                    return true;
                }
                return false;
            }
        }
    }

    /**
     * <AUTHOR>
     * @Date 16:55 2021/5/19
     * @Description 判断当前空运单号延迟推单有效时间
     */
    private boolean checkPushDelayTime(OcBReturnOrder ocBReturnOrder) {
        Date pushDelayTime = ocBReturnOrder.getPushDelayTime();
        if (pushDelayTime == null) {
            return false;
        }
        if (pushDelayTime.before(new Date()) || pushDelayTime == new Date()) {
            return true;
        }
        return false;
    }

    /**
     * 批量更新退单修改时间
     *
     * @param preList
     * @return
     */
    private void updateReturnOrderModifiedDate(List<Long> preList) {
        int size = preList.size(), startIndex = 0;
        int length = size, eachSize = 50;
        List<Long> subList;
        while (size > 0) {
            if (size > eachSize) {
                subList = preList.subList(startIndex, startIndex + eachSize);
                startIndex += eachSize;
            } else {
                subList = preList.subList(startIndex, length);
            }
            updateReturnOrder(subList);
            size -= eachSize;
        }
    }

    /**
     * 更新修改时间
     *
     * @param list 待更新退单
     */
    private void updateReturnOrder(List<Long> list) {

        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> preUpdateIds = new ArrayList<>(50);
        List<RedisReentrantLock> locks = new ArrayList<>(50);
        try {

            for (Long id : list) {
                String lockRedisKey = BllRedisKeyResources.buildLockReturnInKey(id);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    locks.add(redisLock);
                    preUpdateIds.add(id);
                }
            }
            if (CollectionUtils.isEmpty(preUpdateIds)) {
                return;
            }
            int updateResult = returnOrderMapper.updateModifiedDateByIds(preUpdateIds);

        } catch (Exception ex) {
            log.error(LogUtil.format("FindReturnOrderService.退单传WMS解锁异常: {}"), Throwables.getStackTraceAsString(ex));
        } finally {
            for (RedisReentrantLock lock : locks) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    String key = "";
                    if (lock != null) {
                        key = lock.getLockId();
                    }
                    log.error(LogUtil.format("updateReturnOrder.ReturnOrderId#{},退单传WMS解锁异常: {}"),
                            key, Throwables.getStackTraceAsString(e));
                    continue;
                }
            }
        }

    }

    /**
     * 是否需要校验物流单号
     *
     * @return
     */
    private boolean isLogisticsStrict() {
        String logisticsStrict = AdParamUtil.getParam(OcOmsConstant.KEY_SYS_PARAM_RETURN_ORDER_2_WMS_LOGISTICS_STRICT);
        return !OcOmsConstant.FALSE_STRING.equalsIgnoreCase(logisticsStrict);
    }

    //去掉整体事务控制，放置待传wms中再次出现 liqb
//    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 findReturnOrderByWareHouse(List<OcBReturnOrder> data) throws IOException {
        ValueHolderV14 vh = new ValueHolderV14<>();
        List<OcBReturnOrder> returnOrders = new ArrayList<>();
        //筛选数据，传wms失败次数<=5次的数据
        for (OcBReturnOrder bean : data) {
            if (null == bean.getQtyWmsFail() || bean.getQtyWmsFail() <= 5) {
                returnOrders.add(bean);
            }
        }
        try {
            //1)增加一个集合传wms收集
            List<OcBReturnOrder> returnOrders2wms = new ArrayList<>();
            for (int i = returnOrders.size() - 1; i >= 0; i--) {
                //定时任务添加锁单操作
                String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(returnOrders.get(i).getId());
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    continue;
                }
                try {
                    //去掉查询管控仓
                    //20190709  getCpCPhyWarehouseId改为getCpCPhyWarehouseInId
                    Long warehouseId = returnOrders.get(i).getCpCPhyWarehouseInId();
                    if (null == warehouseId) {
                        this.setFailRemarkMessage(returnOrders.get(i), SystemUserResource.getRootUser(), "传WMS的入库实体仓不能为空");
                        continue;
                    }
//                    CpCPhyWarehouse wareHouse = findWareHouse(warehouseId);
//                    if (wareHouse == null || null == wareHouse.getWmsControlWarehouse() || WmsControlWarehouse.NOT_CONTROL.equals(wareHouse.getWmsControlWarehouse())) {
//                        this.setFailRemarkMessage(returnOrders.get(i), SystemUserResource.getRootUser(), "退单所属实体仓未勾选WMS管控仓，退单无法传给WMS");
//                        continue;
//                    }
                    //todo 默认都传wms(wing)
//                    Long is_to_wms = returnOrders.get(i).getIsNeedToWms();  // 是否传wms
//                    if (null == is_to_wms || is_to_wms != 1) {
//                        this.setFailRemarkMessage(returnOrders.get(i), SystemUserResource.getRootUser(), "是否传wms为是才能传WMS");
//                        continue;
//                    }
                    //更改零售退货单【传WMS状态】为【传WMS中】
                    OcBReturnOrder ocBReturnOrder2wms = new OcBReturnOrder();
                    ocBReturnOrder2wms.setId(returnOrders.get(i).getId());
                    ocBReturnOrder2wms.setIsTowms(WmsWithdrawalState.PASS.toInteger());
                    ocBReturnOrder2wms.setModifieddate(new Date());
                    omsReturnOrderService.updateOcBReturnOrder(ocBReturnOrder2wms);
                    returnOrders2wms.add(returnOrders.get(i));
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    redisLock.unlock();
                }
            }

            // @20200820 过滤为空，则不调用接口
            if (CollectionUtils.isNotEmpty(returnOrders2wms)) {
//                return service.qiMenEntryOrderCreate(returnOrders2wms);
                //ValueHolderV14 v14 = service.pushReturnOrderToWms(returnOrders2wms);
                ValueHolderV14 v14 = null;
                if (log.isDebugEnabled()) {
                    log.debug("退单传wms结果：{}", v14);
                }
                return v14;
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("数据为空");
                return vh;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("退单传wms异常，原因: {}"), Throwables.getStackTraceAsString(e));
            returnOrders.forEach(s -> {
                s.setIsTowms(0);
                try {
                    omsReturnOrderService.updateOcBReturnOrder(s);
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            });
        }
        vh.setCode(ResultCode.FAIL);
        vh.setMessage("fail");
        return vh;

    }

    /**
     * 第4步判断 退货类型
     *
     * @param ocBReturnOrder
     * @param rootUser
     */
    private Boolean step04AjustReturnProType(OcBReturnOrder ocBReturnOrder, User rootUser) {
        Boolean isNextStep = true;
        if (ReturnProTypeEnum.INTERCEPT.getVal().equals(ocBReturnOrder.getReturnProType())) {
            //  拦截的处理（判断拦截状态，如果是配送拦截成功，则调用【调用奇门退单传WMS接口】；如果为其他，则不进行处理，程序结束；）
            Integer intercerptStatus = ocBReturnOrder.getIntercerptStatus();
            isNextStep = intercerptStatus != null && intercerptStatus.equals(InterceptStatus.DELIVERY_INTERCEPT_SUCCESS.getCode());

        } else if (ReturnProTypeEnum.REJECTION.getVal().equals(ocBReturnOrder.getReturnProType())) {
            //  拒收的处理 （调用【调用奇门退单传WMS接口】）
            isNextStep = true;
        } else {
            // 客退的 处理（判断退回物流单号是否为空，如果为空，则不进行处理，程序结束。若非空；调用【调用奇门退单传WMS接口】 ）
            if (StringUtils.isBlank(ocBReturnOrder.getLogisticsCode())) {
                isNextStep = false;
            }
        }
        return isNextStep;
    }


    /**
     * 跨库查询实体仓信息
     *
     * @param warehouseId 实体仓ID
     * @return 实体仓
     */
    public CpCPhyWarehouse findWareHouse(Long warehouseId) {

        return cpRpcService.queryByWarehouseId(warehouseId);
    }

    /**
     * 设置失败备注信息
     *
     * @param returnOrder
     * @param user
     * @param remark
     */
    public void setFailRemarkMessage(OcBReturnOrder returnOrder, User user, String remark) throws Exception {
        OcBReturnOrder returnOrderExt = new OcBReturnOrder();
        returnOrderExt.setId(returnOrder.getId());
        returnOrderExt.setRemark(remark);
        returnOrderExt.setModifieddate(new Date());
        returnOrderExt.setModifierid(user.getId() + 0L);
        returnOrderExt.setModifiername(user.getName());
        returnOrderExt.setModifierename(user.getEname());
        omsReturnOrderService.updateOcBReturnOrder(returnOrderExt);
    }

}

