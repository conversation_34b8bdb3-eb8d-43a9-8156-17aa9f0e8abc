package com.jackrain.nea.oc.oms.services.returnorder;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.basic.model.result.SgBStoInNoticesResult;
import com.burgeon.r3.sg.store.api.in.SgBStoInNoticesQueryCmd;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendInStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.request.TerminateWarehousingItemRequest;
import com.jackrain.nea.oc.oms.model.request.TerminateWarehousingRequest;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/24 下午1:48
 * @describe :终止入库
 */

@Slf4j
@Component
public class OcBRefundOrderTerminateWarehousingService {

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;

    @Autowired
    private OcBReturnOrderRefundMapper returnOrderRefundMapper;

    @Autowired
    private OcBReturnOrderLogMapper returnOrderLogMapper;

    @DubboReference(group = "sg", version = "1.0")
    private SgBStoInNoticesQueryCmd stoInNoticesQueryCmd;

    public ValueHolderV14<String> terminateWarehousing(TerminateWarehousingRequest request) {

        ValueHolderV14<String> resultHolder = new ValueHolderV14<>(ResultCode.SUCCESS, "退换货单终止入库执行成功");


        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBRefundOrderTerminateWarehousingService.terminateWarehousing request:{}",
                    "终止入库入参", JSONObject.toJSONString(request)));
        }

        Long id;
        try {

            paramCheck(request);

            id = queryBillId(request);

        } catch (Exception e) {

            log.error(LogUtil.format("OcBRefundOrderTerminateWarehousingService.terminateWarehousing error:{}",
                    "终止入库异常", Throwables.getStackTraceAsString(e), e));

            resultHolder.setCode(ResultCode.FAIL);
            resultHolder.setMessage(e.getMessage());
            return resultHolder;
        }

        String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);

        try {

            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {

                dealSingle(request, id);

            } else {

                throw new NDSException("当前单据正在被操作中，不允许重复操作");

            }

        } catch (Exception e) {

            buildReturnOderLog("退换货单终止入库", "退换货单终止入库执行失败:" + e.getMessage(), id, request.getUser());

            log.error(LogUtil.format("OcBRefundOrderTerminateWarehousingService.terminateWarehousing error:{}",
                    "终止入库异常", Throwables.getStackTraceAsString(e), e));

            resultHolder.setCode(ResultCode.FAIL);
            resultHolder.setMessage(e.getMessage());

        } finally {

            redisLock.unlock();
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBRefundOrderTerminateWarehousingService.terminateWarehousing result:{}",
                    "终止入库出参", JSONObject.toJSONString(resultHolder)));
        }

        return resultHolder;
    }

    private Long queryBillId(TerminateWarehousingRequest request) {

        String billNo = request.getBillNo();

        ValueHolderV14<SgBStoInNoticesResult> resultValueHolderV14 = stoInNoticesQueryCmd.selectSgBStoInNoticesByBillNo(billNo);
        if (!resultValueHolderV14.isOK()) {
            throw new NDSException(resultValueHolderV14.getMessage());
        }

        if (resultValueHolderV14.getData() == null
                || resultValueHolderV14.getData().getNotices() == null
                || resultValueHolderV14.getData().getNotices().getSourceBillId() == null) {
            throw new NDSException("根据入库通知单未查到退单ID");
        }

        Long sourceBillId = resultValueHolderV14.getData().getNotices().getSourceBillId();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBRefundOrderTerminateWarehousingService.selectSgBStoInNoticesByBillNo result:{}",
                    "根据通知单号查退单ID", JSONObject.toJSONString(sourceBillId)));
        }

        return sourceBillId;
    }

    private void dealSingle(TerminateWarehousingRequest request,
                            Long id) {

        User user = request.getUser();

        List<TerminateWarehousingItemRequest> terminateWarehousingItemList = request.getItem();

        List<OcBReturnOrder> returnOrderList = returnOrderMapper.selectList(new QueryWrapper<OcBReturnOrder>()
                .lambda()
                .eq(OcBReturnOrder::getId, id)
                .eq(OcBReturnOrder::getIsactive, R3CommonResultConstants.VALUE_Y));

        if (CollectionUtils.isEmpty(returnOrderList)) {
            throw new NDSException("当前退单记录已经不存在");
        }

        List<OcBReturnOrderRefund> returnOrderRefundList = returnOrderRefundMapper.selectList(new QueryWrapper<OcBReturnOrderRefund>()
                .lambda()
                .eq(OcBReturnOrderRefund::getOcBReturnOrderId, id)
                .eq(OcBReturnOrderRefund::getIsactive, R3CommonResultConstants.VALUE_Y));

        if (CollectionUtils.isEmpty(returnOrderList)) {
            throw new NDSException("当前所有的单据明细记录已经不存在");
        }

        OcBReturnOrder returnOrder = returnOrderList.get(0);

        if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnOrder.getReturnStatus())) {

            //校验条码和数量
            skuAndQtyCheck(returnOrderRefundList, terminateWarehousingItemList);

            //判断是否入库数量全部为0
//            boolean isAllZero = isAllZero(terminateWarehousingItemList);

            //更改原单状态
            updateBillInfo(returnOrder, returnOrderRefundList, user, false);

            //日志记录
            buildReturnOderLog("退换货单终止入库", "退换货单终止入库执行完成", id, user);

        } else if (ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal().equals(returnOrder.getReturnStatus()) ||
                ReturnStatusEnum.COMPLETION.getVal().equals(returnOrder.getReturnStatus())) {

            if (ProReturnStatusEnum.WHOLE.getVal().equals(returnOrder.getProReturnStatus())) {

                throw new NDSException("退换货单已全部入库，不能进行终止入库");

            } else if (ProReturnStatusEnum.PORTION.getVal().equals(returnOrder.getProReturnStatus())) {

                //校验条码和数量
                skuAndQtyCheck(returnOrderRefundList, terminateWarehousingItemList);

                //判断是否入库数量全部为0
//                boolean isAllZero = isAllZero(terminateWarehousingItemList);

                //更改原单状态
                updateBillInfo(returnOrder, returnOrderRefundList, user, true);

                //日志记录
                buildReturnOderLog("退换货单终止入库", "退换货单终止入库执行完成", id, user);
            }

        } else if (/*ReturnStatusEnum.COMPLETION.getVal().equals(returnOrder.getReturnStatus())
                || */ReturnStatusEnum.CANCLE.getVal().equals(returnOrder.getReturnStatus())) {

            throw new NDSException("退换货单已取消，不能进行终止入库");
        }

    }

    /**
     * 判断入库数量是否都为0
     *
     * @param terminateWarehousingItemList
     * @return true:是；false:否
     */
//    private boolean isAllZero(List<TerminateWarehousingItemRequest> terminateWarehousingItemList) {
//        if (CollectionUtils.isEmpty(terminateWarehousingItemList)) {
//            return true;
//        }
//
//        boolean isAllZero = true;
//        for (TerminateWarehousingItemRequest terminateWarehousingItemRequest : terminateWarehousingItemList) {
//            BigDecimal qtyIn = terminateWarehousingItemRequest.getQtyIn() == null ? BigDecimal.ZERO : terminateWarehousingItemRequest.getQtyIn();
//            if (qtyIn.compareTo(BigDecimal.ZERO) != 0) {
//                isAllZero = false;
//                break;
//            }
//        }
//        return isAllZero;
//    }

    private void skuAndQtyCheck(List<OcBReturnOrderRefund> returnOrderRefundList,
                                List<TerminateWarehousingItemRequest> terminateWarehousingItemList) {

        if (CollectionUtils.isEmpty(terminateWarehousingItemList)) {
            throw new NDSException("WMS明细不能为空");
        }

        Map<String, BigDecimal> orgSkuAndQtyMap = new HashMap<>(16);
        Map<String, BigDecimal> terminateSkuAndQtyMap = new HashMap<>(16);

        for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefundList) {

            BigDecimal qtyMatch = returnOrderRefund.getQtyMatch() == null ? BigDecimal.ZERO : new BigDecimal(returnOrderRefund.getQtyMatch());
            String skuEcode = returnOrderRefund.getPsCSkuEcode();

            if (orgSkuAndQtyMap.containsKey(skuEcode)) {

                orgSkuAndQtyMap.put(skuEcode, orgSkuAndQtyMap.get(skuEcode).add(qtyMatch));

            } else {

                orgSkuAndQtyMap.put(skuEcode, qtyMatch);
            }
        }

        for (TerminateWarehousingItemRequest terminateWarehousingItemRequest : terminateWarehousingItemList) {

            BigDecimal qtyIn = terminateWarehousingItemRequest.getQtyIn() == null ? BigDecimal.ZERO : terminateWarehousingItemRequest.getQtyIn();
            String skuEcode = terminateWarehousingItemRequest.getSkuEcode();

            if (terminateSkuAndQtyMap.containsKey(skuEcode)) {

                terminateSkuAndQtyMap.put(skuEcode, terminateSkuAndQtyMap.get(skuEcode).add(qtyIn));

            } else {

                terminateSkuAndQtyMap.put(skuEcode, qtyIn);
            }
        }

        for (String skuEcode : terminateSkuAndQtyMap.keySet()) {

            if (orgSkuAndQtyMap.containsKey(skuEcode)) {

                BigDecimal orgQty = orgSkuAndQtyMap.get(skuEcode);
                BigDecimal terminateQty = terminateSkuAndQtyMap.get(skuEcode);

                if (orgQty.compareTo(terminateQty) != 0) {

                    throw new NDSException("退单明细中条码:" + skuEcode + ",原单入库数量:" + orgQty + ",WMS入库数量:" + terminateQty + ",数量不匹配");
                }


            } else {

                throw new NDSException("退单明细中不存在条码:" + skuEcode);
            }
        }

        for (String skuEcode : orgSkuAndQtyMap.keySet()) {

            if (!terminateSkuAndQtyMap.containsKey(skuEcode)) {

                BigDecimal orgQty = orgSkuAndQtyMap.get(skuEcode);
                BigDecimal terminateQty = BigDecimal.ZERO;

                if (orgQty.compareTo(terminateQty) != 0) {

                    throw new NDSException("退单明细中条码:" + skuEcode + ",原单入库数量:" + orgQty + "WMS入库无此条码,数量默认为0,数量不匹配");
                }
            }
        }

    }

    private void paramCheck(TerminateWarehousingRequest request) {

        if (request == null) {
            throw new NDSException("请求参数不能为空");
        }

        if (StringUtils.isEmpty(request.getBillNo())) {
            throw new NDSException("单据编号不能为空");
        }

        if (request.getUser() == null) {
            throw new NDSException("用户不能为空");
        }

    }


    private List<OcBReturnOrderRefund> buildUpdateItemTableList(List<OcBReturnOrderRefund> returnOrderRefundList, User user, boolean isPartIn) {

        List<OcBReturnOrderRefund> updateItemTableList = new ArrayList<>();

        for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefundList) {

            OcBReturnOrderRefund updateItemTable = new OcBReturnOrderRefund();
            updateItemTable.setId(returnOrderRefund.getId());

            if (isPartIn) {

                BigDecimal qtyRefund = returnOrderRefund.getQtyRefund() == null ? BigDecimal.ZERO : returnOrderRefund.getQtyRefund();
                BigDecimal qtyIn = returnOrderRefund.getQtyIn() == null ? BigDecimal.ZERO : returnOrderRefund.getQtyIn();

                updateItemTable.setTerminationQty(qtyRefund.subtract(qtyIn));
            } else {

                updateItemTable.setTerminationQty(returnOrderRefund.getQtyRefund());
            }

            OmsModelUtil.setDefault4Upd(updateItemTable, user);

            updateItemTableList.add(returnOrderRefund);
        }

        return updateItemTableList;
    }

    /**
     * 更新退换货单
     * @param returnOrder
     * @param user
     * @param isPartIn
     * @return
     */
    private OcBReturnOrder buildUpdateMainTable(OcBReturnOrder returnOrder, User user, boolean isPartIn) {

        //退换货单主表的【终止类型】字段赋值为“全部”；【退换货单】单据状态变更为“完成”

        OcBReturnOrder updateMainTable = new OcBReturnOrder();

        updateMainTable.setId(returnOrder.getId());
        if (isPartIn) {
            updateMainTable.setTerminationType("2");
            //updateMainTable.setReturnStatus(ReturnStatusEnum.COMPLETION.getVal());
        } else {
            updateMainTable.setTerminationType("1");
            updateMainTable.setReturnStatus(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
            updateMainTable.setProReturnStatus(ReturnAfSendInStatusEnum.ALLINSTORAGE.getVal());
        }

        //所有商品入库数量都为0，变成待入库
//        if (isAllZero){
//            updateMainTable.setProReturnStatus(ReturnAfSendInStatusEnum.PENDINGSTORAGE.getVal());
//        }

        updateMainTable.setTerminationDate(new Date());
        OmsModelUtil.setDefault4Upd(updateMainTable, user);

        return updateMainTable;
    }

    /**
     * 更新
     *
     * @param returnOrder
     * @param returnOrderRefundList
     * @param user
     * @param isPartIn
     */
    public void updateBillInfo(OcBReturnOrder returnOrder,
                               List<OcBReturnOrderRefund> returnOrderRefundList,
                               User user,
                               boolean isPartIn) {

        OcBReturnOrder updateMainTable = buildUpdateMainTable(returnOrder, user, isPartIn);

        OcBRefundOrderTerminateWarehousingService service = ApplicationContextHandle.getBean(OcBRefundOrderTerminateWarehousingService.class);
        service.doUpdateBillInfo(returnOrder.getId(), updateMainTable, returnOrderRefundList, isPartIn, user);
    }

    @Transactional(rollbackFor = Exception.class)
    public void doUpdateBillInfo(Long id,
                                 OcBReturnOrder updateMainTable,
                                 List<OcBReturnOrderRefund> returnOrderRefundList,
                                 boolean isPartIn,
                                 User user) {

        returnOrderMapper.updateById(updateMainTable);

        for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefundList) {

            Long itemId = returnOrderRefund.getId();
            OcBReturnOrderRefund updateItemTable = new OcBReturnOrderRefund();

            BigDecimal qtyRefund = returnOrderRefund.getQtyRefund() == null ? BigDecimal.ZERO : returnOrderRefund.getQtyRefund();
            BigDecimal qtyMatch = returnOrderRefund.getQtyMatch() == null ? BigDecimal.ZERO : new BigDecimal(returnOrderRefund.getQtyMatch());
            if (isPartIn) {

                updateItemTable.setTerminationQty(qtyRefund.subtract(qtyMatch));
            } else {

                updateItemTable.setTerminationQty(qtyRefund);
            }

            OmsModelUtil.setDefault4Upd(updateItemTable, user);

            returnOrderRefundMapper.update(updateItemTable, new UpdateWrapper<OcBReturnOrderRefund>()
                    .lambda()
                    .eq(OcBReturnOrderRefund::getOcBReturnOrderId, id)
                    .eq(OcBReturnOrderRefund::getId, itemId));
        }
    }


    public void buildReturnOderLog(String type, String message, Long key, User usr) {
        OcBReturnOrderLog returnLog = new OcBReturnOrderLog();
        returnLog.setLogType(type);
        returnLog.setUserName(usr.getName());
        returnLog.setOwnername(usr.getName());
        returnLog.setOwnerename(usr.getEname());
        returnLog.setCreationdate(new Date());
        returnLog.setOwnerid(Long.valueOf(usr.getId()));
        returnLog.setModifierid(Long.valueOf(usr.getId()));
        returnLog.setModifiername(usr.getName());
        returnLog.setModifierename(usr.getEname());
        returnLog.setModifieddate(new Date());
        returnLog.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
        returnLog.setIpAddress(usr.getLastloginip());
        returnLog.setAdOrgId((long) usr.getOrgId());
        returnLog.setAdClientId((long) usr.getClientId());
        Long id = Tools.getSequence(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_LOG_TYPE_NAME);
        returnLog.setId(id);
        returnLog.setLogMessage(message);
        returnLog.setOcBReturnOrderId(key);

        returnOrderLogMapper.insert(returnLog);
    }
}
