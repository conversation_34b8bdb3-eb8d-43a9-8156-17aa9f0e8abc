package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPromItemMapper;
import com.jackrain.nea.oc.oms.model.PromotionDetailInfo;
import com.jackrain.nea.oc.oms.model.PromotionInfo;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.pm.service.PmPreExecService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/8/27 4:46 下午
 * @Version 1.0
 * <p>
 * 促销预执行
 */
@Slf4j
@Component
public class OmsPromotionPreService {


    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private PmPreExecService pmPreExecService;

    @Autowired
    private OcBOrderPromItemMapper ocBOrderPromItemMapper;

    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsToBeConfirmedTaskService omsToBeConfirmedTaskService;




    public ValueHolder executePromotionPre(OcBOrder ocBOrder, List<OcBOrderItem> items) {
        JSONObject preExecParam = new JSONObject();
        PromotionInfo promotionInfo = this.getPromotionInfo(ocBOrder, items);
        String jsonString = JSONObject.toJSONString(promotionInfo);
        preExecParam.put("param", jsonString);
        ValueHolder vh = pmPreExecService.callPreExecRpc(preExecParam);
        return vh;
    }


    /**
     * 封装返回参数处理
     *
     * @param holder true 无可执行的促销   false 有促销或者异常 订单挂起
     */
    public boolean handleResult(ValueHolder holder, OcBOrder ocBOrder) {
        int code = Tools.getInt(holder.get("code"), -1);
        if (code == 0) {
            Object data = holder.get("data");
            if (data == null) {
                //促销返回 的data为空时 暂不处理
                return true;
            }
            JSONObject dataJson = JSONObject.parseObject(holder.get("data") + "");
            Long orderId = dataJson.getLong("orderNo");
            boolean isHoldFlag = dataJson.getBoolean("isHold");
            if (isHoldFlag) {
                JSONArray pmInfo = dataJson.getJSONArray("pmInfo");
                if (CollectionUtils.isNotEmpty(pmInfo)) {
                    List<OcBOrderPromItem> promItems = new ArrayList<>();
                    for (int i = 0; i < pmInfo.size(); i++) {
                        OcBOrderPromItem ocBOrderPromItem = new OcBOrderPromItem();
                        ocBOrderPromItem.setId(ModelUtil.getSequence("oc_b_order_prom_item"));
                        ocBOrderPromItem.setOcBOrderId(orderId);
                        ocBOrderPromItem.setIsHold("Y");
                        JSONObject pmJson = pmInfo.getJSONObject(i);
                        Long pmID = pmJson.getLong("pmID");
                        ocBOrderPromItem.setPromId(pmID);
                        String pmName = pmJson.getString("pmName");
                        ocBOrderPromItem.setPromName(pmName);
                        String groupNo = pmJson.getString("groupNo");
                        ocBOrderPromItem.setGroupCode(groupNo);
                        ocBOrderPromItem.setCreationdate(new Date());
                        ocBOrderPromItem.setModifieddate(new Date());
                        ocBOrderPromItem.setIsactive("Y");
                        ocBOrderPromItem.setIsExcute("N");
                        promItems.add(ocBOrderPromItem);
                    }
                    ocBOrderPromItemMapper.batchInsert(promItems);
                    OcBOrder ocBOrderDto = new OcBOrder();
                    ocBOrderDto.setModifierename(SystemUserResource.ROOT_USER_NAME);
                    ocBOrderDto.setIsPromOrder(OcBOrderConst.IS_STATUS_IY);
                    //促销释放后不在执行这步  直接跳转促销预执行下一步
                    ocBOrderDto.setModifieddate(new Date());
                    ocBOrderDto.setId(ocBOrder.getId());
                    omsOrderService.updateOrderInfo(ocBOrderDto);
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ORDER_PROM.getKey(), "订单正在执行促销中......", null, null, SystemUserResource.getRootUser());
                    return false;
                } else {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ORDER_PROM.getKey(), "调用促销返回数据异常", null, null, SystemUserResource.getRootUser());
                    throw new NDSException("调用促销返回数据异常");
                }
            } else {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_PROM.getKey(), "无可执行的促销活动！促销返回信息：" + holder.get("message"), null, null, SystemUserResource.getRootUser());
                return true;
            }
        } else {
            throw new NDSException("调用促销接口异常" + holder.get("message"));
        }
    }


    /**
     * 插入占单任务表
     *
     * @param orderId
     */
    private void insertToBeConfirmedTask(Long orderId) {
        OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
        toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
        toBeConfirmedTask.setOrderId(orderId);
        toBeConfirmedTask.setCreationdate(new Date());
        toBeConfirmedTask.setStatus(0);
        omsToBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
    }


    /**
     * 封装入参
     *
     * @param
     * @param order
     * @return
     */
    private PromotionInfo getPromotionInfo(OcBOrder order, List<OcBOrderItem> items) {
        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setOrderId(order.getId());
        promotionInfo.setOrderNo(order.getSourceCode());
        promotionInfo.setOrderTime(order.getOrderDate());
        promotionInfo.setOrderType(order.getOrderType());
        String statusPayStep = order.getStatusPayStep();
        if (order.getPresaleDepositTime() != null && StringUtils.isNotEmpty(statusPayStep)) {
            promotionInfo.setEarnestMoneyTime(order.getPresaleDepositTime());
        }
        if (order.getPayTime() != null) {
            promotionInfo.setPayTime(order.getPayTime());
            promotionInfo.setHoldTime(order.getPayTime());
        } else {
            promotionInfo.setHoldTime(order.getPresaleDepositTime());
        }

        if (TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equals(statusPayStep)) {
            promotionInfo.setOrderMark("1");
        } else {
            promotionInfo.setOrderMark("0");
        }
        promotionInfo.setProvinceIdD(order.getCpCRegionProvinceId());
        promotionInfo.setShopId(order.getCpCShopId());
        promotionInfo.setShopEcode(order.getCpCShopEcode());
//        promotionInfo.setShopEname(order.getCpCStoreEname());
        promotionInfo.setSellerRemark(order.getSellerMemo());
        promotionInfo.setBuyerRemark(order.getBuyerMessage());
        promotionInfo.setEnumdataFlatfopm(order.getOrderSource() == null ? "" : order.getOrderSource());
        promotionInfo.setMemberName(order.getUserNick());
        List<PromotionDetailInfo> detailInfos = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : items) {
            // 和产品确认后，赠品（包含中台赠品，系统赠品）不调用促销。bug编号：31251
            if (ocBOrderItem.getIsGift() != 1) {
                PromotionDetailInfo detailInfo = new PromotionDetailInfo();
                //吊牌价
                detailInfo.setAmtList(ocBOrderItem.getPriceTag() == null ? BigDecimal.ZERO : ocBOrderItem.getPriceTag());
                //标准价
                detailInfo.setAmtRetail(ocBOrderItem.getPriceList() == null ? BigDecimal.ZERO : ocBOrderItem.getPriceList());
                //成交价
                detailInfo.setAmtReceivable(ocBOrderItem.getRealAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getRealAmt().divide(ocBOrderItem.getQty(), 2, BigDecimal.ROUND_HALF_UP));
                detailInfo.setQtty(ocBOrderItem.getQty().intValue());
                //促销中心确认后，sku的value也设置为商品编码
                detailInfo.setSku(ocBOrderItem.getPsCSkuEcode());
                detailInfo.setEcode(ocBOrderItem.getPsCProEcode());
                detailInfo.setPlatformEcode(ocBOrderItem.getNumIid());
                detailInfo.setPlatformSku(ocBOrderItem.getSkuNumiid());
                detailInfos.add(detailInfo);
            }
        }
        promotionInfo.setProductList(detailInfos);
        return promotionInfo;
    }
}
