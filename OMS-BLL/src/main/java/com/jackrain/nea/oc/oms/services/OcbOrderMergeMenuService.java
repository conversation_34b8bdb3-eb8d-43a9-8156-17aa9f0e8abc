package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-07-04 09:49
 */
@Slf4j
@Component
public class OcbOrderMergeMenuService {


    @Autowired
    private OrderMergeService orderMergeService;

    /**
     * 合并订单按钮
     *
     * @param querySession
     * @return
     */
    public ValueHolder mergeOrder(QuerySession querySession) {
        ValueHolder result = new ValueHolder();
        User user = querySession.getUser();
        HashMap map = new HashMap();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss",
                SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("框架param:{}"), param.toJSONString());
        }
        List<Long> list = (List) param.get("ids");
        if (CollectionUtils.isEmpty(list) || list.size() < 2) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "请选择至少两条单据,才能进行合并");
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }

        try {
            ValueHolder valueHolder = orderMergeService.mergeOrderMenu(list, user);
            return valueHolder;
        } catch (Exception e) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "手动合单异常：" + e.getMessage());
            map.put("data", null);
            result.setData(map);
            return result;
        }
    }


    public ValueHolder mergeOrderOne(QuerySession querySession) {
        ValueHolder result = new ValueHolder();
        User user = querySession.getUser();
        HashMap map = new HashMap();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("框架param{}"), param.toJSONString());
        }

        List<Long> list = JSON.parseArray(param.getString("ids"),Long.class);

        if (CollectionUtils.isEmpty(list) || list.size() < 2) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "请选择至少两条单据,才能进行合并");
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }
        try {
            ValueHolder valueHolder = orderMergeService.mergeOrderMenuOne(list, user);
            return valueHolder;
        } catch (Exception e) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "手动合单异常：" + e.getMessage());
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }
    }
}
