package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.FormatDateUtil;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Author: ganquan
 * @Date Create In 2020/7/7 10:34
 * @Description: wms实缺服务
 */
@Slf4j
@Component
public class LackOrderToWmsBackService {

    private static final int LOG_MESSAGE_MAX_LENGHT = 7900;

    @Autowired
    private SgRpcService sgRpcPhyOutQueryService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Transactional(rollbackFor = Exception.class)
    public void handle(String message, User user) {
        log.debug("LackOrderToWmsBackService回传任务开始执行》》》" + JSON.toJSONString(message));
        JSONObject object = JSONObject.parseObject(message);
        JSONObject request = object.getJSONObject("request");
        JSONArray items = request.getJSONArray("items");
        //出库通知单编码
        String deliveryOrderCode = request.getString("deliveryOrderCode");
        if (StringUtils.isEmpty(deliveryOrderCode)) {
            log.error("日志服务:wms回传缺货服务RLackOrderToWmsBackService.handle deliveryOrderCode获取发货通知单编码为空！");
            return;
        }
        ValueHolderV14<Long> hv = sgRpcPhyOutQueryService.querySourceIdByBillNo(deliveryOrderCode);
        if (hv == null) {
            log.error("日志服务:wms回传缺货服务RLackOrderToWmsBackService.handle 获取回传失败:hv为空！");
            return;
        }
        if (hv.getCode() == ResultCode.FAIL) {
            log.error("日志服务:wms回传缺货服务RLackOrderToWmsBackService.handle 获取订单id失败:{}！", hv.getMessage());
            return;
        }
        Long orderId = hv.getData();
        if (orderId == null) {
            log.error("日志服务:wms回传缺货服务RLackOrderToWmsBackService.handle 获取订单id为空！");
            return;
        }
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
        if (ocBOrder == null) {
            log.error("日志服务:wms回传缺货服务RLackOrderToWmsBackService.handle 获取订单信息为空！");
            return;
        }
        //实缺
       // ocBOrder.setIsRealLackstock(OcBOrderConst.IS_STATUS_IY);
        ocBOrder.setModifierename(user.getName());
        ocBOrder.setModifieddate(new Date(System.currentTimeMillis()));
        if (ocBOrderMapper.updateById(ocBOrder) > 0) {
            String logMessage = getOrderLogMessage(items);
            omsOrderLogService.addUserOrderLog(orderId, ocBOrder.getBillNo(),
                    OrderLogTypeEnum.WMS_LACK_GIFT.getKey(),
                    logMessage, null, "", user);
            log.debug("LackOrderToWmsBackService,WMS实缺商品打标成功，mq消费结束，订单id为：" + orderId);
        }
    }

    /**
     * @param items
     * @return java.lang.String
     * <AUTHOR>
     * @Description 实缺商品获取日志内容
     * @Date 14:30 2020/7/7
     **/
    private String getOrderLogMessage(JSONArray items) {
        Date date = new Date();
        String dateStr = FormatDateUtil.formatDate(date, "yyyy-MM-dd HH:mm:ss");
        StringBuffer message = new StringBuffer();
        if (CollectionUtils.isEmpty(items)) {
            return message.toString();
        }
        for (Object item : items) {
            JSONObject itemObj = ((JSONObject) item).getJSONObject("item");
            //条码
            String itemCode = itemObj.getString("itemCode");
            //缺货商品数量
            String lackQty = itemObj.getString("lackQty");
            //应发货商品数量
            String planQty = itemObj.getString("planQty");
            //缺货原因
            String reason = itemObj.getString("reason");
            message.append("[回传时间: ").append(dateStr).append("]")
                    .append("[条码: ").append(itemCode).append("]")
                    .append("[实缺商品数量: ").append(lackQty).append("]")
                    .append("[应发商品数量: ").append(planQty).append("]")
                    .append("[缺货原因: ").append(reason).append("]");
        }
        String s = message.toString();
        if (message.length() > LOG_MESSAGE_MAX_LENGHT) {
            s = message.substring(0, LOG_MESSAGE_MAX_LENGHT);
        }
        return s;
    }

}
