package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.jingdong.ReceiveRegisterRequest;
import com.jackrain.nea.oc.oms.mapper.IpBJingdongRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.IsInStorageEnum;
import com.jackrain.nea.oc.oms.model.enums.IsOpenAGEnum;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongRefund;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 京东自动退款SA-调用京东拆包登记服务
 * @author: 郑小龙
 * @date: 2020-06-04 17:25
 **/
@Component
@Slf4j
public class OcBRefundOrderToSAService {

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private ReturnOrderLogService logService;
    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;
    @Autowired
    private IpBJingdongRefundMapper jingdongRefundMapper;
    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private StRpcService stRpcService;

    public ValueHolderV14 refundOrderToSa(String param, User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        JSONObject jsonObject = JSON.parseObject(param);
        if (jsonObject == null || jsonObject.size() == 0) {
            holderV14.setCode(-1);
            holderV14.setMessage("参数为空！");
            return holderV14;
        }
        //退换货Id
        Long id = jsonObject.getLong("ID");
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(id);
        List<OcBReturnOrderRefund> refundList = ocBReturnOrderRefundMapper.selectByOcOrderId(id);
        if (ocBReturnOrder == null) {
            holderV14.setCode(-1);
            holderV14.setMessage("退换货单已不存在！");
            return holderV14;
        }

        if (CollectionUtils.isEmpty(refundList)) {
            holderV14.setCode(-1);
            holderV14.setMessage("退换货单明细已不存在！");
            return holderV14;
        }
        //如果退换货单状态为完成状态，则进入下一步流程判断；如果为其他状态，则不进行处理
        Integer returnStatus = ocBReturnOrder.getReturnStatus();
        if (returnStatus == null || !(returnStatus.equals(ReturnStatusEnum.COMPLETION.getVal()))) {
            holderV14.setCode(-1);
            holderV14.setMessage("退单传SA服务失败：退换货状态不匹配！");
            return holderV14;
        }

        //判断退货单平台类型如果为淘宝平台的退单，进行下一步判断,否则不判断
        Integer platform = ocBReturnOrder.getPlatform();
        if (!PlatFormEnum.JINGDONG.getCode().equals(platform)) {
            holderV14.setCode(-1);
            holderV14.setMessage("退单传SA服务失败：平台类型不匹配！");
            return holderV14;
        }

        /*单据类型,1退货单，2退换货单',
         *退单的类型，如果为退换货单，则不走SA程序，不进行处理，更新传SA状态为不传AG（3）
         *添加退换货订单操作日志中；如果类型为退货单时进行下一步判断
         */
        Integer billType = ocBReturnOrder.getBillType();
        if (billType == null || billType.equals(OcReturnBillTypeEnum.EXCHANGE.getVal())) {
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            returnOrder.setId(ocBReturnOrder.getId());
            returnOrder.setIsToag(AGStatusEnum.NOT.getVal());
            return updateRefundOrderAgAndLog(returnOrder, loginUser, "单据类型不匹配！");
        }

        StCShopStrategyDO ocStCShopStrategy = stRpcService.selectOcStCShopStrategyByCpCshopId(ocBReturnOrder.getCpCShopId());
        if (ocStCShopStrategy != null && IsOpenAGEnum.OPEN.getVal().equals(ocStCShopStrategy.getIsAg())) {
            return gotoAg(ocBReturnOrder, refundList, loginUser);
        } else {
            holderV14.setCode(-1);
            holderV14.setMessage("退单传AG服务失败：店铺策略为空或店铺策略未勾选AG状态！");
            return holderV14;
        }
    }

    /**
     * 调用Sa拆包接口判断逻辑
     *
     * @param ocBReturnOrder
     * @param refundList
     * @param loginUser
     * @return
     */
    private ValueHolderV14 gotoAg(OcBReturnOrder ocBReturnOrder, List<OcBReturnOrderRefund> refundList, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug(" OcBRefundOrderToSAService.gotoAg.ocBReturnOrder {}",JSON.toJSONString(ocBReturnOrder));
        }
        ValueHolderV14 holderV14 = new ValueHolderV14();
        /*
         * 判断退货单明细的平台退款单号是否为空 ，
         * 如果所有明细为空，则不走SA程序，不进行处理，更新传SA状态为不传SA
         * 添加退换货订单操作日志中；不为空进行下一步判断
         */
        Long id = ocBReturnOrder.getId();

        for (OcBReturnOrderRefund refund : refundList) {
            OcBReturnOrderRefund newRefund = new OcBReturnOrderRefund();
            newRefund.setId(refund.getId());
            newRefund.setPsCSkuEcode(refund.getPsCSkuEcode());
            returnOrderTransferUtil.updateOperator(newRefund, loginUser);

            // 平台退款单号, 替换字段  reserveVarchar01-> refund_bill_no
            String returnId = refund.getRefundBillNo();
            //是否ag退款
            Integer isAg = refund.getIsToAg();
            if (null == isAg) {
                newRefund.setIsToAg(AGStatusEnum.FAIL.getVal());
                updateRefundOrderAgAndLog(id, newRefund, loginUser, "退单传AG服务失败：SA状态异常！");
                continue;
            }

            if (StringUtils.isNotEmpty(returnId)) {
                if (isAg.equals(AGStatusEnum.INIT.getVal())
                        || isAg.equals(AGStatusEnum.FAIL.getVal())) {
                    QueryWrapper<IpBJingdongRefund> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("afsserviceid", returnId);
                    IpBJingdongRefund jingdongRefund = jingdongRefundMapper.selectOne(queryWrapper);
                    if (null == jingdongRefund) {
                        newRefund.setIsToAg(AGStatusEnum.NOT.getVal());
                        updateRefundOrderAgAndLog(id, newRefund, loginUser, "京东退款单中间表不存在此退单！");
                        continue;
                    }

                    //主商品功能状况（qualityState）正品=10 好，次品=30 坏
                    int qstate = refund.getProductMark() == null ? 10 : refund.getProductMark().equals("1") ? 10 : 30;
                    ValueHolderV14 v14 = agIncomingStock(ocBReturnOrder, returnId, qstate, loginUser);
                    if (v14.isOK()) {
                        newRefund.setIsToAg(AGStatusEnum.SUCCESS.getVal());
                        updateRefundOrderAgAndLog(id, newRefund, loginUser, "");
                        continue;
                    } else {
                        newRefund.setIsToAg(AGStatusEnum.FAIL.getVal());
                        updateRefundOrderAgAndLog(id, newRefund, loginUser, v14.getMessage());
                        continue;
                    }
                } else {
                    newRefund.setIsToAg(refund.getIsToAg());
                    updateRefundOrderAgAndLog(id, newRefund, loginUser, "退单SA拆包登记接口失败：明细SA状态不匹配！");
                    continue;
                }
            } else {
                newRefund.setIsToAg(AGStatusEnum.NOT.getVal());
                updateRefundOrderAgAndLog(id, newRefund, loginUser, "平台退款单号为空！");
                continue;
            }
        }
        List<OcBReturnOrderRefund> returnOrderRefunds = ocBReturnOrderRefundMapper.selectByOcOrderId(ocBReturnOrder.getId());

        holderV14.setCode(-1);
        holderV14.setMessage("退单SA拆包登记接口失败！部分明细未调用SA拆包登记服务！");

        //此实体仅用来更新
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        returnOrder.setId(ocBReturnOrder.getId());
        returnOrderTransferUtil.updateOperator(returnOrder, loginUser);

        //已传数量
        int ALREADY_TO_AG = 0;
        //不传数量
        int DO_NOT_TO_AG = 0;
        //失败数量
        int FAil_TO_AG = 0;

        for (OcBReturnOrderRefund orderRefund : returnOrderRefunds) {
            if (orderRefund.getIsToAg().equals(AGStatusEnum.SUCCESS.getVal())) {
                ALREADY_TO_AG++;
                continue;
            } else if (orderRefund.getIsToAg().equals(AGStatusEnum.FAIL.getVal())) {
                FAil_TO_AG++;
                continue;
            } else if (orderRefund.getIsToAg().equals(AGStatusEnum.NOT.getVal())) {
                DO_NOT_TO_AG++;
                continue;
            }
        }

        if (FAil_TO_AG == returnOrderRefunds.size()) {
            returnOrder.setIsToag(AGStatusEnum.FAIL.getVal());
        }
        if (DO_NOT_TO_AG == returnOrderRefunds.size()) {
            returnOrder.setIsToag(AGStatusEnum.NOT.getVal());
        }
        if (ALREADY_TO_AG == returnOrderRefunds.size()) {
            returnOrder.setIsToag(AGStatusEnum.SUCCESS.getVal());
            returnOrder.setIsInstorage(IsInStorageEnum.IS_INSTORAGE.getVal());
            holderV14.setCode(0);
            holderV14.setMessage("退单传SA服务成功！");
        }
        ocBReturnOrderMapper.updateById(returnOrder);
        return holderV14;
    }

    /**
     * SA拆包登记接口
     *
     * @param returnOrder
     * @param returnId
     * @param qstate
     * @param loginUser
     * @return
     */
    private ValueHolderV14 agIncomingStock(OcBReturnOrder returnOrder, String returnId, Integer qstate, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getSimpleName() + " 京东调用SA拆包登记,returnOrder=" + JSONObject.toJSONString(returnOrder)
                    + ",returnId=" + returnId + ",qstate=" + qstate);
        }
        ValueHolderV14 v14 = new ValueHolderV14(0, "");
        try {
            CpShop cpShop = cpRpcService.selectShopById(returnOrder.getCpCShopId());
            if (null == cpShop) {
                log.debug(this.getClass().getSimpleName() + " 京东调用SA拆包登记,查询店铺为空cpShop=" + cpShop);
                v14.setMessage("京东调用SA拆包登记,查询店铺为空!");
                v14.setCode(-1);
                return v14;
            }
            ReceiveRegisterRequest request = new ReceiveRegisterRequest();
            //商家ID（最长50）
            request.setBuId(cpRpcService.getUserId(cpShop));
            //操作人账号（最长50）
            request.setOperatePin("semir");
            //操作人姓名（最长50）
            request.setOperateNick("semir");
            //服务单号
            request.setServiceId(Long.valueOf(returnId));
            //订单号
            request.setOrderId(Long.valueOf(returnOrder.getOrigSourceCode()));
            //收货人账号
            request.setReceivePin(returnOrder.getReceiveName());
            //收货人姓名
            request.setReceiveName(returnOrder.getReceiveName());
            //产品包装状况
            request.setPackingState(1);
            //主商品功能状况
            request.setQualityState(qstate);
            //发票登记状况
            request.setInvoiceRecord(1);
            //收货登记原因
            request.setJudgmentReason(1);
            //附件/赠品
            request.setAccessoryOrGift(1);
            //主商品外观
            request.setAppearanceState(1);
            //收货备注
            request.setReceiveRemark(StringUtils.isEmpty(returnOrder.getBackMessage()) ? "无备注" : returnOrder.getBackMessage());

            log.debug(this.getClass().getSimpleName() + " 京东调用SA拆包登记服务入参=" + JSONObject.toJSONString(request));
            v14 = ipRpcService.receiveRegister(request, cpShop.getSellerNick());
            log.debug(this.getClass().getSimpleName() + " 京东调用SA拆包登记服务出参=" + v14);
        } catch (Exception ex) {
            log.debug(this.getClass().getSimpleName() + " 京东调用SA拆包登记服务异常=" + ex.getMessage());
            v14.setMessage("京东调用SA拆包登记异常!");
            v14.setCode(-1);
        }
        return v14;
    }

    /**
     * 更新传SA状态，添加退换货订单操作日志中
     *
     * @param ocBReturnOrder 退换货实体
     * @param loginUser      loginUser
     * @param logContent     日志内容
     */
    private ValueHolderV14 updateRefundOrderAgAndLog(OcBReturnOrder ocBReturnOrder, User loginUser, String logContent) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            ocBReturnOrderMapper.updateById(ocBReturnOrder);
        } catch (Exception ex) {
            log.error(LogUtil.format("日志服务：SA拆包登记接口异常,error:{}"), Throwables.getStackTraceAsString(ex));
            logContent = "同步异常：" + ex;
            logService.addRefundOrderLog(ocBReturnOrder.getId(), "同步AG入仓", logContent, loginUser);
            throw new NDSException("退单SA拆包登记接口失败！" + ex);
        }

        if (ocBReturnOrder.getIsToag().equals(AGStatusEnum.SUCCESS.getVal())) {
            logContent = "同步成功：同步AG入仓成功";
            holderV14.setCode(0);
            holderV14.setMessage("退单SA拆包登记接口成功！");
        } else {
            holderV14.setCode(-1);
            logContent = "同步失败：" + logContent;
            holderV14.setMessage("退单SA拆包登记接口失败！" + logContent);
        }
        logService.addRefundOrderLog(ocBReturnOrder.getId(), "同步AG入仓", logContent, loginUser);
        return holderV14;
    }

    /**
     * 更新传SA状态，添加退换货订单操作日志中
     *
     * @param id
     * @param refund
     * @param loginUser
     * @param logContent
     * @return
     */
    private ValueHolderV14 updateRefundOrderAgAndLog(Long id, OcBReturnOrderRefund refund, User loginUser, String logContent) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            ocBReturnOrderRefundMapper.updateById(refund);
        } catch (Exception ex) {
            log.error(LogUtil.format("日志服务：SA拆包登记接口异常,error:{}"), Throwables.getStackTraceAsString(ex));
            logContent = "明细sku[" + refund.getPsCSkuEcode() + "],同步异常：" + ex;
            logService.addRefundOrderLog(id, "同步AG入仓", logContent, loginUser);
            throw new NDSException("退单SA拆包登记接口失败！" + ex);
        }

        if (refund.getIsToAg().equals(AGStatusEnum.SUCCESS.getVal())) {
            logContent = "明细sku[" + refund.getPsCSkuEcode() + "],同步成功：同步AG入仓成功";
            holderV14.setCode(0);
            holderV14.setMessage("退单SA拆包登记接口成功！");
        } else {
            holderV14.setCode(-1);
            logContent = "明细sku[" + refund.getPsCSkuEcode() + "],同步失败：" + logContent;
            holderV14.setMessage("退单SA拆包登记接口失败！" + logContent);
        }
        logService.addRefundOrderLog(id, "同步AG入仓", logContent, loginUser);
        return holderV14;
    }

}
