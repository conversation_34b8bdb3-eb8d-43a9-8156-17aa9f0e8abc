package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsPhyStorageOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsReleaseOutRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemFiMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.matcher.live.LiveFlagEnum;
import com.jackrain.nea.oc.oms.matcher.live.LiveMatchManager;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BigDecimalUtil;
import com.jackrain.nea.oc.oms.util.PrintLogUtils;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-05-05
 * create at : 2019-05-05 4:49 PM
 * PRD-R3-订单管理-31-修改订单商品服务PRD后端-程诗迎-20190227 V1.0
 */
@Slf4j
@Component
public class OcBorderUpdateServiceExt {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemFiMapper ocBorderItemMapper;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsOrderCancellationService omsOrderCancellationService;

    @Autowired
    private OmsOrderCheckAndUpdateService omsOrderCheckAndUpdateService;

    @Autowired
    private OmsOrderSplitService omsOrderSplitService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OmsOrderRecountAmountService omsOrderRecountAmountService;
    @Autowired
    private OcbCancelOrderMergeService cancelOrderMergeService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private AgainOccupyStockService againOccupyStockService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 updateOrder(OcBOrderRelation relation, User loginUser) {
        log.debug(this.getClass().getSimpleName() + "修改订单入参=" + JSON.toJSONString(relation));
        //redis锁单
        OcBOrder order = relation.getOrderInfo();
        //重新查询一下主表信息
        OcBOrder ocBOrder = ocBOrderMapper.selectById(order.getId());
        if (ocBOrder == null) {
            throw new NDSException("该订单已不存在！");
        }
        relation.setOrderInfo(ocBOrder);
        //无明细直接判断 订单状态为待审核、缺货状态，则执行占用库存服务
        Integer orderStatus = ocBOrder.getOrderStatus();
        Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();

        List<OcBOrderItem> orderItemList = orderItemMapper.selectUnSuccessRefundAndNoSplit(ocBOrder.getId());
        List<OcBOrderItem> itemList = orderItemList.stream().filter(p -> p.getProType() != SkuType.COMBINE_PRODUCT && p.getProType() != SkuType.GIFT_PRODUCT).collect(Collectors.toList());

        // @20200704 直播打标-退款完成重整（这里只是赋值，更新在后面updateMainTable）
        LiveMatchManager.getInstance().cleanUp(order, orderItemList);

        //明细里is_gift有一个=1，就更新主表is_hasgift=1
        boolean isGift = false;
        //存在明细整理主表
        if (itemList != null && itemList.size() != 0) {
            OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
            ocBOrderRelation.setOrderInfo(ocBOrder);
            List<OcBOrderItem> list = new ArrayList<>();
            omsOrderRecountAmountService.doRecountAmount(ocBOrderRelation, list, itemList);
            List<OcBOrderItem> collect = orderItemList.stream().filter(p -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(p.getIsGift())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                ocBOrder.setIsHasgift(1);
            } else {
                ocBOrder.setIsHasgift(0);
            }

            log.debug(this.getClass().getSimpleName() + "整理主表后数据：" + JSON.toJSONString(relation));

            //更新主表
            return updateMainTable(relation, orderStatus, wmsCancelStatus, loginUser);
        } else {
            throw new NDSException("订单更新异常");
        }
    }

    /**
     * 复制直播信息
     *
     * @param src
     * @param dest
     * @20200704 直播打标-退款完成节点重整标记
     */
    private void copyLiveInfo(OcBOrder src, OcBOrder dest) {
        if (Objects.nonNull(src) && Objects.nonNull(dest)) {
            //  mybatis.updateById无法将一个字段更新成null，但是可以更新成空""
            dest.setLiveFlag(Objects.isNull(src.getLiveFlag()) ? LiveFlagEnum.N.getValue() : src.getLiveFlag());
           // dest.setLivePlatform(Objects.isNull(src.getLivePlatform()) ? "-1" : src.getLivePlatform());
            dest.setAnchorName(Objects.isNull(src.getAnchorName()) ? "" : src.getAnchorName());
            dest.setAnchorId(Objects.isNull(src.getAnchorId()) ? "" : src.getAnchorId());
        }
    }

    private ValueHolderV14 updateMainTable(OcBOrderRelation relation, Integer orderStatus, Integer wmsCancelStatus, User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        OcBOrder bOrder = ocBOrderMapper.selectById(relation.getOrderId());
        bOrder.setIsHasgift(relation.getOrderInfo().getIsHasgift());

        // @20200704 直播打标-退款完成节点重整 by wu.lb
        copyLiveInfo(relation.getOrderInfo(), bOrder);

        relation.setOrderInfo(bOrder);
        BigDecimal orderDiscountAmt = bOrder.getOrderDiscountAmt();
        try {

            if (ocBOrderMapper.updateById(relation.getOrderInfo()) > 0) {
                SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocBOrderMapper.selectById(relation.getOrderId()), relation.getOrderId());
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("日志服务 更新异常,error:{}"), Throwables.getStackTraceAsString(ex));
            throw new NDSException("更新异常");
        }

        /*
         * 若订单状态为待审核、缺货状态，则执行占用库存服务
         */
        OcBOrder ocBOrder = relation.getOrderInfo();
        List<OcBOrderItem> orderItemList = relation.getOrderItemList();
        log.info(LogUtil.format("进入修改规格业务处理,orderId =", ocBOrder.getId()));
        if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal()) {
            //dealOrder(relation, loginUser);
            //调用指定商品释放库存服务
            SgOmsShareOutRequest request = cancelOrderMergeService.buildSgOmsShareOutRequest(ocBOrder, orderItemList, loginUser);
            ValueHolderV14  v14=sgRpcService.voidSgOmsShareOut(request,ocBOrder, orderItemList);
            if (v14.isOK()){
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.RELEASE_STOCK_SUCCESS.getKey(), "释放库存成功", "", "", loginUser);
                updateOrderStatus(ocBOrder,loginUser);
                //加入占单表
                omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
            }else {
                throw new NDSException("释放库存失败！");
            }
        }else if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()){
            updateOrderStatus(ocBOrder,loginUser);
            //加入占单表
            omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
        }
//        else if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
//            //若订单状态为配货中且WMS撤回状态为已撤回，则复制新单，作废原单。
//            if (wmsCancelStatus != null && wmsCancelStatus == OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()) {
//                relation = copyOrderAndLogRecord(relation, loginUser);
//            } else {
//                throw new NDSException("WMS撤回状态错误！");
//            }
//        }
        else {
            throw new NDSException("订单状态异常！");
        }

        if (orderItemList.size() > 1) {
            //分出新的老的，进行日志打印
            OcBOrderItem item = orderItemList.get(0);
            OcBOrderItem item2 = orderItemList.get(1);
            String content;
            if (item.getQty().compareTo(item2.getQty()) > 0) {
                //1新2旧单
                content = item2.getPsCSkuEcode() + "条码修改为" + item.getPsCSkuEcode() + "条码！";
            } else {
                content = item.getPsCSkuEcode() + "条码修改为" + item2.getPsCSkuEcode() + "条码！";
            }
            try {
                omsOrderLogService.addUserOrderLog(relation.getOrderInfo().getId(), relation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SAVE.getKey(), content, "", "", loginUser);
            } catch (Exception ex) {
                log.debug("日志服务：调用日志服务异常" + ex);
            }
        }
        //判断平摊金额的总额是否与头表订单优惠金额是否一致，若一致，不处理，若不一致 则调用订单平摊金额服务
        doCheckAndUpdateBlanceMoney(relation.getOrderInfo(), orderDiscountAmt);
        //走完没有异常，就成功
        holderV14.setMessage("保存成功！");
        holderV14.setCode(0);
        return holderV14;
    }

    /**
     * <AUTHOR>
     * @Date 20:39 2021/8/13
     * @Description 更新状态
     */
    void updateOrderStatus(OcBOrder ocBOrder,User loginUser){
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(ocBOrder.getId());
        updateOrder.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
        updateOrder.setOccupyStatus(OmsParamConstant.INT_TWO);
        updateOrder.setSysremark("");
        updateOrder.setModifierename(loginUser.getName());
        updateOrder.setModifieddate(new Date());
        // 订单合单加密信息，需要传有地址信息的订单参数
        MD5Util.encryptOrderInfo4Merge(ocBOrder);
        updateOrder.setOrderEncryptionCode(ocBOrder.getOrderEncryptionCode());
        omsOrderService.updateOrderInfo(updateOrder);
    }
    /**
     * 调用成功，则调用订单日志服务，如果是新增商品则传输以下参数
     * 如果是减少商品则传输以下参数
     * 如果是既新增商品又有减少商品则以上2个日志同时写入
     * 调用服务失败，则回滚数据，不允许修改商品明细信息
     * qty==0,减少商品库存。
     * qty>0,增加商品库存。
     *
     * @param relation  relation
     * @param loginUser loginUser
     */
    private void dealOrder(OcBOrderRelation relation, User loginUser) {
        List<OcBOrderItem> orderItemList = relation.getOrderItemList();
        boolean addStock = false;
        boolean reduceStock = false;
        if (orderItemList == null || orderItemList.size() == 0) {
            throw new NDSException("变动的明细信息不能为空");
        }
        for (OcBOrderItem item : orderItemList) {
            if (BigDecimal.ZERO.equals(item.getQty())) {
                reduceStock = true;
            } else if (item.getQty().compareTo(BigDecimal.ZERO) > 0) {
                addStock = true;
            }
        }

        log.debug(LogUtil.format("日志服务：占用库存传输数据SgRpcService.querySearchStockAndModifyGoodsInfo(relation)==") + JSON.toJSONString(relation));
        ValueHolderV14 sgValueHolder = sgRpcService.querySearchStockAndModifyGoodsInfo(relation, loginUser);

        if (sgValueHolder.isOK()) {
            if (addStock) {
                String type = "占用库存";
                String content = "占用库存成功";
                try {
                    omsOrderLogService.addUserOrderLog(relation.getOrderId(), relation.getOrderInfo().getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), content, "", "", loginUser);
                } catch (Exception ex) {
                    log.error(LogUtil.format("日志服务：调用日志服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
                }
            }
            if (reduceStock) {
                String type = "释放库存";
                String content = "释放库存成功";
                try {
                    omsOrderLogService.addUserOrderLog(relation.getOrderId(), relation.getOrderInfo().getBillNo(), OrderLogTypeEnum.RELEASE_STOCK_SUCCESS.getKey(), content, "", "", loginUser);
                } catch (Exception ex) {
                    log.debug(LogUtil.format("日志服务：调用日志服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
                }
            }
            //更换商品或者修改规格了，打印日志
            if (addStock && reduceStock) {
                String type = "保存（明细修改规格）";
                //分出新的老的，进行日志打印
                OcBOrderItem item = orderItemList.get(0);
                OcBOrderItem item2 = orderItemList.get(1);
                String content;
                if (item.getQty().compareTo(item2.getQty()) > 0) {
                    //1新2旧单
                    content = item2.getPsCSkuEcode() + "条码修改为" + item.getPsCSkuEcode() + "条码！";
                } else {
                    content = item.getPsCSkuEcode() + "条码修改为" + item2.getPsCSkuEcode() + "条码！";
                }
                try {
                    omsOrderLogService.addUserOrderLog(relation.getOrderInfo().getId(), relation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SAVE.getKey(), content, "", "", loginUser);
                } catch (Exception ex) {
                    log.error(LogUtil.format("日志服务：调用日志服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
                }
            }
        } else if (sgValueHolder.getCode() == 3) {
            //分出新的老的，进行日志打印
            if (addStock && reduceStock) {
                OcBOrderItem item = orderItemList.get(0);
                OcBOrderItem item2 = orderItemList.get(1);
                String content;
                if (item.getQty().compareTo(item2.getQty()) > 0) {
                    //1新2旧单
                    content = item2.getPsCSkuEcode() + "条码修改为" + item.getPsCSkuEcode() + "条码！";
                } else {
                    content = item.getPsCSkuEcode() + "条码修改为" + item2.getPsCSkuEcode() + "条码！";
                }
                try {
                    omsOrderLogService.addUserOrderLog(relation.getOrderInfo().getId(), relation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SAVE.getKey(), content, "", "", loginUser);
                } catch (Exception ex) {
                    log.error(LogUtil.format("日志服务：调用日志服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
                }
                omsOrderLogService.addUserOrderLog(relation.getOrderId(), relation.getOrderInfo().getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_FAIL.getKey(), "因缺少库存，占用库存失败", "", "", loginUser);
                log.debug(LogUtil.format("日志服务：因缺少库存，占用库存失败") + sgValueHolder.getMessage());
            }
            throw new NDSException("因缺少库存，占用库存失败");
        } else {
            Long orderItemId = 0L;
            BigDecimal outOfStockNum = BigDecimal.ZERO;
            if (addStock && reduceStock) {
                OcBOrderItem item = orderItemList.get(0);
                OcBOrderItem item2 = orderItemList.get(1);
                String content;
                if (item.getQty().compareTo(item2.getQty()) > 0) {
                    //1新2旧单
                    outOfStockNum = item.getQty();
                    orderItemId = item.getId();
                    content = item2.getPsCSkuEcode() + "条码修改为" + item.getPsCSkuEcode() + "条码！";
                } else {
                    outOfStockNum = item2.getQty();
                    orderItemId = item2.getId();
                    content = item.getPsCSkuEcode() + "条码修改为" + item2.getPsCSkuEcode() + "条码！";
                }
                //修改明细缺货数量
                UpdateWrapper<OcBOrderItem> wrapper = new UpdateWrapper<>();
                wrapper.eq("oc_b_order_id", orderItemId).eq("id", relation.getOrderInfo().getId());
                OcBOrderItem qtyItem = new OcBOrderItem();
                qtyItem.setQtyLost(outOfStockNum);
                ocBorderItemMapper.update(qtyItem, wrapper);
                log.debug(LogUtil.format("修改明细缺货数量setQtyLost{},orderItemId/orderId=", orderItemId,
                        relation.getOrderInfo().getId()), outOfStockNum);
                try {
                    omsOrderLogService.addUserOrderLog(relation.getOrderInfo().getId(), relation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SAVE.getKey(), content, "", "", loginUser);
                } catch (Exception ex) {
                    log.debug(LogUtil.format("日志服务：调用日志服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
                }
                omsOrderLogService.addUserOrderLog(relation.getOrderId(), relation.getOrderInfo().getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_FAIL.getKey(), "因缺少库存，占用库存失败", "", "", loginUser);
                log.debug(LogUtil.format("日志服务：因缺少库存，占用库存失败") + sgValueHolder.getMessage());
            }
//            throw new NDSException("占用库存服务失败,异常信息-->" + sgValueHolder.getMessage());
            //addStock代表增加赠品、更换商品、修改规格，将主表改为缺货，子表信息替换并更新数据库，不报异常
            if (addStock) {
                doOutOfStockUpdate(relation);
            }
        }
    }

    private void doCheckAndUpdateBlanceMoney(OcBOrder ocBorderDto, BigDecimal orderDiscountAmt) {
        if (!orderDiscountAmt.equals(ocBorderDto.getOrderDiscountAmt())) {
//            OcBOrderRelation relation = new OcBOrderRelation();
//            relation.setOrderInfo(ocBorderDto);
            try {
                OcBOrderRelation relation = new OcBOrderRelation();
                relation.setOrderInfo(ocBorderDto);
                List<OcBOrderItem> list = new ArrayList<>();
                List<OcBOrderItem> items = orderItemMapper.selectOrderItemListOccupy(ocBorderDto.getId());

                List<OcBOrderItem> itemList = items.stream().filter(p -> p.getProType() != SkuType.COMBINE_PRODUCT && p.getProType() != SkuType.GIFT_PRODUCT).collect(Collectors.toList());
                omsOrderRecountAmountService.doRecountAmount(relation, list, itemList);
            } catch (Exception ex) {
                log.error(LogUtil.format("日志服务：订单平摊金额服务失败！,error:{}"), Throwables.getStackTraceAsString(ex));
                throw new NDSException("保存失败:订单平摊金额服务失败！" + ex.getMessage());
            }
        }
    }

    /**
     * 则将原订单复制生成一张新订单，订单状态为缺货（2），
     * 新单调用【占用库存服务】，如果调用失败，则回滚数据，不允许修改商品明细信息
     * 调用成功，则新单调用日志服务
     *
     * @param relation  relation
     * @param loginUser loginUser
     * @return 新的订单信息
     */
    private OcBOrderRelation copyOrderAndLogRecord(OcBOrderRelation relation, User loginUser) {
        OcBOrderRelation newRelation;
        OcBOrder newOrder;
        //复制订单
        try {
            newRelation = omsOrderSplitService.getOcBOrderRelation(relation.getOrderId(), true);
            if (newRelation.getOrderInfo() == null) {
                throw new NDSException("复制订单失败！");
            }
            newOrder = newRelation.getOrderInfo();
            try {
                omsOrderLogService.addUserOrderLog(newOrder.getId(), newOrder.getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(), "复制订单成功", "", "", loginUser);
            } catch (Exception ex) {
                log.error(LogUtil.format("日志服务：调用日志服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("日志服务：复制订单异常,error:{}"), Throwables.getStackTraceAsString(ex));
            throw new NDSException("复制订单失败！" + ex.getMessage());
        }

        log.debug(LogUtil.format("日志服务：占用库存传输数据SgRpcService.querySearchStockAndModifyAddress(newRelation)==") + JSON.toJSONString(newRelation));
        ValueHolderV14 holderV14 = sgRpcService.querySearchStockAndModifyAddress(newRelation, loginUser);

        if (holderV14.isOK()) {
            try {
                omsOrderLogService.addUserOrderLog(newOrder.getId(), newOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), "占用库存成功", "", "", loginUser);
            } catch (Exception ex) {
                log.error(LogUtil.format("日志服务：调用日志服务成功,error:{}"), Throwables.getStackTraceAsString(ex));
            }
        } else if (holderV14.getCode() == 3) {
            try {
                omsOrderLogService.addUserOrderLog(newOrder.getId(), newOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_FAIL.getKey(), "因缺少库存，占用库存失败", "", "", loginUser);
            } catch (Exception ex) {
                log.error(LogUtil.format("日志服务：调用日志服务成功,error:{}"), Throwables.getStackTraceAsString(ex));
            }
        } else {
            log.debug(LogUtil.format("日志服务：缺货占用库存服务失败") + holderV14.getMessage());
//            throw new NDSException("占用库存服务失败,异常信息-->" + holderV14.getMessage());
            doOutOfStockUpdate(relation);
        }

        //作废订单服务
        try {
            ValueHolderV14 result = omsOrderCancellationService.doInvoildOutOrder(relation.getOrderInfo(), loginUser);
            if (!result.isOK()) {
                throw new NDSException();
            }
            try {
                omsOrderLogService.addUserOrderLog(relation.getOrderId(), relation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(), "配货中修改商品作废原订单", "", "", loginUser);
            } catch (Exception ex) {
                log.error(LogUtil.format("日志服务：调用日志服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("调用日志doOrderInvalid失败,error:{}"), Throwables.getStackTraceAsString(ex));
            throw new NDSException("调用作废订单服务失败,异常信息-->" + ex.getMessage());
        }
        return newRelation;
    }

    private void doOutOfStockUpdate(OcBOrderRelation relation) {
        //主表更新为缺货
        relation.getOrderInfo().setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal());
        try {
            ocBOrderMapper.updateById(relation.getOrderInfo());
            SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocBOrderMapper.selectById(relation.getOrderId()), relation.getOrderId());
        } catch (Exception ex) {
            log.debug(this.getClass().getSimpleName() + "更新主表异常,主表Id=" + relation.getOrderId() + ex);
            throw new NDSException(this.getClass().getSimpleName() + "更新主表异常,主表Id=" + relation.getOrderId());
        }
        //此处不更新主子表，因为上游已经更新或者新增过了
//        List<OcBOrderItem> itemList = relation.getOrderItemList();
//        if (itemList != null && itemList.size() != 0) {
//            //代表可以更新
//            if (itemList.size() != 1) {
//                OcBOrderItem updateItem;
//                if (itemList.get(0).getQty().compareTo(itemList.get(1).getQty()) > 0) {
//                    updateItem = itemList.get(0);
//                } else {
//                    updateItem = itemList.get(1);
//                }
//                log.debug(this.getClass().getSimpleName() + "更新主表Id=" + relation.getOrderId() + "的明细，明细=" + JSON.toJSONString(updateItem));
//                ocBorderItemMapper.updateById(updateItem);
//                try {
//                    SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, updateItem, updateItem.getId(), relation.getOrderId().toString());
//                } catch (Exception ex) {
//                    log.debug(this.getClass().getSimpleName() + "子表推送es异常");
//                }
//            }
//
//        }
        log.debug(this.getClass().getSimpleName() + "更新主子表成功！");
    }

}
