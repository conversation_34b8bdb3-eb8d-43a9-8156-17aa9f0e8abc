package com.jackrain.nea.oc.oms.services;

import com.burgeon.r3.sg.core.model.ext.SgBStorageInclShare;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderIsInterceptEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.request.OrderSerarchCheckRequest;
import com.jackrain.nea.oc.oms.model.result.CheckOrderResult;
import com.jackrain.nea.oc.oms.model.result.WareHouseResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.st.service.OmsQueryWareHouseService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>缺货订单自动寻仓占单任务</>
 *
 * @author: heliu
 * @since: 2019/3/22
 * create at : 2019/3/22 17:09
 */
@Component
@Slf4j
public class OmsOrderAutoSearchWarehouseService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsQueryWareHouseService omsQueryWareHouseService;

    @Autowired
    private OmsSyncStockStrategyService omsSyncStockStrategyService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsOrderDistributeWarehouseService orderDistributeWarehouseService;

    @Autowired
    private OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;

    @Autowired
    private OmsFortuneBagAgainSmokeService omsFortuneBagAgainSmokeService;
    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;


    /**
     * 缺货自动寻仓服务
     *
     * @param param 传进来参数
     * @param user  user对象
     * @return ValueHolderV14
     */
    public ValueHolderV14 doAutoSearchWarehouse(OrderSerarchCheckRequest param, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (param == null || param.getIds().length < 1) {
            throw new NDSException(Resources.getMessage("请选择需要缺货自动寻仓的订单！"));
        }
        Long[] ids = param.getIds();
        Boolean isFortuneBag = param.getIsFortuneBag();
        if (ids.length > 1) {
            int success = 0;
            int failed = 0;
            //列表界面审核
            for (int i = 0; i < ids.length; i++) {
                Long orderId = ids[i];
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        if (checkOrderInfo(user, orderId, isFortuneBag)) {
                            success++;
                        } else {
                            failed++;
                        }
                    } else {
                        throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!"));
                    }
                } catch (Exception ex) {
                    failed++;
                } finally {
                    redisLock.unlock();
                }
            }
            vh.setCode(0);
            vh.setMessage("缺货自动寻仓执行成功 " + success + "，执行失败 " + failed);
            return vh;
        } else {
            //单个订单操作
            Long orderId = ids[0];
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    if (checkOrderInfo(user, ids[0], isFortuneBag)) {
                        vh.setCode(0);
                        if (isFortuneBag) {
                            vh.setMessage("订单OrderId" + ids[0] + "的福袋重新抽取成功! ");
                        } else {
                            vh.setMessage("订单OrderId" + ids[0] + "的订单缺货寻仓执行成功! ");
                        }
                        return vh;
                    } else {
                        vh.setCode(-1);
                        vh.setMessage("订单OrderId" + ids[0] + "的订单缺货重新占单依旧缺货,需要调整库存之后再执行! ");
                        return vh;
                    }
                } else {
                    throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!"));
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("缺货重新占单异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
                throw new NDSException(Resources.getMessage(ex.getMessage()));
            } finally {
                redisLock.unlock();
            }
        }
    }

    /**
     * 判断寻仓入口
     *
     * @param user         用户对象
     * @param orderId      订单Id
     * @param isFortuneBag 福袋
     * @return
     */
    public boolean checkOrderInfo(User user, Long orderId, boolean isFortuneBag) {
        //先查找订单缺货对象
        OcBOrder ocBorderDto = omsOrderService.selectOrderUnStock(orderId);
        if (ocBorderDto == null) {
            throw new NDSException(Resources.getMessage("只允许缺货订单进行缺货重新占单操作!"));
        }
        //该订单为挂起订单，缺货重新占单失败！
        /*if (ocBorderDto.getIsInterecept() == 1) {
            throw new NDSException(Resources.getMessage("该订单为挂起订单，缺货重新占单失败！"));
        }*/
        // HOLD单订单也可以缺货重新占单，但是退款原因HOLD单的订单不能缺货重新占单
        if (InreturningStatus.INRETURNING.equals(ocBorderDto.getIsInreturning())
                && OmsOrderIsInterceptEnum.YES.getVal().equals(ocBorderDto.getIsInterecept())) {
            throw new NDSException("订单退款Hold单,不能进行缺货重新占单！");
        }
        //在查找未退款明细
        List<OcBOrderItem> orderItemList = omsOrderItemService.selectUnSuccessRefund(ocBorderDto.getId());

        //单个订单缺货重新寻找仓库
        CheckOrderResult orderResult = checkOrder(ocBorderDto, orderItemList, user, isFortuneBag);
        if (orderResult.getId() == null) {
            //缺货重新占单成功后 缺货标记去除
            omsOrderService.updateOrderIsLackstock(orderId, user);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 执行缺货寻仓逻辑
     *
     * @param ocBorderDto   订单对象
     * @param orderItemList 未退款明细对象
     * @param user          用户对象
     * @param isFortuneBag  福袋
     * @return
     */
    private CheckOrderResult checkOrder(OcBOrder ocBorderDto, List<OcBOrderItem> orderItemList, User user, boolean isFortuneBag) {

        Integer orderStatus = ocBorderDto.getOrderStatus();
        Long orderId = ocBorderDto.getId();

        //判断明细是否为空
        if (CollectionUtils.isEmpty(orderItemList)) {
            throw new NDSException(Resources.getMessage("订单OrderId" + orderId + "的订单明细不能为空！"));
        }


        if (isFortuneBag) {
            boolean fortuneItemFlg = false;
            for (OcBOrderItem orderItem : orderItemList) {
                //商品类型
                Long proType = orderItem.getProType();
                if (proType == null) {
                    continue;
                }
                if (SkuType.GIFT_PRODUCT == proType) {
                    fortuneItemFlg = true;
                }
            }
            if (!fortuneItemFlg) {
                throw new NDSException(Resources.getMessage("订单Id为" + ocBorderDto.getId() + "的订单不是福袋订单！"));
            }
        }

        //订单状态
        if (orderStatus == null) {
            throw new NDSException(Resources.getMessage("订单OrderId" + orderId + "的订单状态不能为空！"));
        }
        if (orderStatus != OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal()) {
            throw new NDSException(Resources.getMessage("订单OrderId" + orderId + "的订单状态非缺货状态！"));
        }
        //订单是否被拦截
        /*if (ocBorderDto.getIsInterecept() == 1) {
            throw new NDSException(Resources.getMessage("订单OrderId" + orderId + "的订单已经被挂起！"));
        }*/
        //判断发货仓库
        if (ocBorderDto.getCpCPhyWarehouseId() == null) {
            throw new NDSException(Resources.getMessage("订单OrderId" + orderId + "的订单发货仓库不能为空！"));

        }
        //判断店铺是否为空
        if (ocBorderDto.getCpCShopId() == null) {
            throw new NDSException(Resources.getMessage("订单OrderId" + orderId + "的订单下单店铺不能为空！"));
        }


        //执行重新寻仓逻辑
        checkOrderItemStock(ocBorderDto, orderItemList, user, isFortuneBag);

        /**
         * 【缺货拆单按钮】前置调用缺货重新占单时，返回专用。 后续有时间进行重构
         * 目的是为了前置，后置的代码减少逻辑改动，特此加特殊代码。
         */
        return new CheckOrderResult();
    }


    /**
     * 检查订单明细是否满足库存
     *
     * @param ocBorderDto   订单对象
     * @param orderItemList 未退款明细对象
     * @param user          用户对象
     * @param isFortuneBag  福袋
     */
    public void checkOrderItemStock(OcBOrder ocBorderDto, List<OcBOrderItem> orderItemList, User user, boolean isFortuneBag) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            //拿到更新后的对象
            OcBOrder order = ocBOrderMapper.selectById(ocBorderDto.getId());
            List<OcBOrderItem> items = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBorderDto.getId());
            ValueHolderV14<Map<Long, Integer>> holder = sgOccupiedInventoryService.shortageOccupyWareHouse(order, items, user);
            if (holder.getCode() == 0) {
                String message = " 订单OrderId" + ocBorderDto.getId() + "缺货重新占单执行成功,操作时间" + df.format(new Date());
                if (log.isDebugEnabled()) {
                    log.debug("缺货重新占单执行时间{}", message);
                }
                sgOccupiedInventoryService.handleOccupy(order, items, holder, user);
                //调用满足库存日志
                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.LACK_RE_OCCUPANCY.getKey(), "订单OrderId" + order.getId() + "缺货重新占单执行成功,操作时间" + df.format(new Date()),
                        null, null, user);
            } else {
                //重试占不上的情况下走分仓,看分到的仓库是否和缺货发货仓库一致,若一致结束流程,不一致,去查库存,满足的情况下去调更新实体仓并更新逻辑发货单服务
                Long wareHouseId = order.getCpCPhyWarehouseId();
                //如果是唯品会订单不需要分仓直接取实体仓
                if (!order.getPlatform().equals(PlatFormEnum.VIP_JITX.getCode())) {
                    //非唯品会订单则重新获取分仓服务的ID
                    OcBOrderRelation orderRelation = new OcBOrderRelation();
                    orderRelation.setOrderInfo(order);
                    orderRelation.setOrderItemList(items);
                    wareHouseId = orderDistributeWarehouseService.doCallDistributeWarehouse(orderRelation, user);
                    if (log.isDebugEnabled()) {
                        log.debug(" 缺货重新占单旧仓库id:{},新仓库id:{}", order.getCpCPhyWarehouseId(), wareHouseId);
                    }
                }
                // 重新分仓: 1.没有找到实体仓库
                if (wareHouseId == null) {
                    String erroreMessage = "订单OrderId" + order.getId() + "重新调用分仓服务未分配到有效实体发货发货仓库,自动寻仓结束！";
                    OcBOrder newOrder = new OcBOrder();
                    newOrder.setId(order.getId());
                    newOrder.setSysremark(erroreMessage);
                    omsOrderService.updateOrderInfo(newOrder);
                    throw new NDSException(Resources.getMessage(erroreMessage));
                } else {
//                    // 重新分仓: 2.和原缺货发货仓库一致
                    if (order.getCpCPhyWarehouseId().compareTo(wareHouseId) == 0) {
                        // @20210114 缺货重新占单,更新缺货明细的缺货数量
                        if(holder.getCode() == 3){
                            Map<Long, Integer> data = holder.getData();
                            for (OcBOrderItem orderItem : items) {
                                OcBOrderItem item = new OcBOrderItem();
                                Long itemId = orderItem.getId();
                                item.setId(orderItem.getId());
                                item.setOcBOrderId(orderItem.getOcBOrderId());
                                Integer qtyLost = data.get(itemId) == null ? 0 : data.get(itemId);
                                item.setQtyLost(BigDecimal.valueOf(qtyLost));
                                omsOrderItemService.updateOcBOrderItem(item, ocBorderDto.getId());
                            }
                        }
                        // 福袋商品   缺货重新抽取
                        if (isFortuneBag) {
                            //ValueHolderV14 vh = omsFortuneBagAgainSmokeService.againFortuneBagSmoke(ocBorderDto, orderItemList, user);
                            ValueHolderV14 vh = omsFortuneBagAgainSmokeService.againFortuneBagDraw(ocBorderDto.getId(),user);
                            if (!vh.isOK()) {
                                throw new NDSException(Resources.getMessage(vh.getMessage()));
                            }
                        } else {
                            String erroreMessage = "订单OrderId" + order.getId() + "重新调用分仓服务,寻仓结果和原发货仓一致.占用库存失败，实体仓暂时缺货，请调整相应商品库存！";
                            OcBOrder newOrder = new OcBOrder();
                            newOrder.setId(order.getId());
                            newOrder.setSysremark(SplitMessageUtil.splitMesssage(erroreMessage));
                            omsOrderService.updateOrderInfo(newOrder);
                            throw new NDSException(Resources.getMessage(erroreMessage));
                        }
                    } else {

                        // 重新分仓: 3.和原缺货发货仓库不一致,->: 重新set值并且依据这个新发货仓库去查库存
                        order.setCpCPhyWarehouseId(wareHouseId);
                        OcBOrder updateOrder = new OcBOrder();
                        CpCPhyWarehouse cpCPhyWarehouse = orderDistributeWarehouseService.queryByWarehouseId(wareHouseId);
                        //set实体仓Id
                        updateOrder.setId(order.getId());
                        updateOrder.setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
                        // 设置ename
                        updateOrder.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                        // 设置ecode
                        updateOrder.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                        // 如果实体仓是o2o仓库，对订单进行打标
                        if (StringUtils.equals(cpCPhyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
                            updateOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
                        } else {
                            updateOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
                        }
                        // updateOrder.setThirdWarehouseType(cpCPhyWarehouse.getThirdWarehouseType());
                        updateOrder.setCpCLogisticsId(0L);
                        updateOrder.setCpCLogisticsEcode("");
                        updateOrder.setCpCLogisticsEname("");
                        // 分物流
                        OcBOrderRelation orderInfo = new OcBOrderRelation();
                        order.setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
                        order.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                        order.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                        order.setIsO2oOrder(updateOrder.getIsO2oOrder());
                        // order.setThirdWarehouseType(cpCPhyWarehouse.getThirdWarehouseType());
                        orderInfo.setOrderItemList(items);
                        orderInfo.setOrderInfo(order);
                        CpCLogistics cpCLogistics = omsOrderDistributeLogisticsService.distributeLogistics(orderInfo);
                        if (cpCLogistics != null) {
                            updateOrder.setCpCLogisticsId(cpCLogistics.getId());
                            updateOrder.setCpCLogisticsEcode(cpCLogistics.getEcode());
                            updateOrder.setCpCLogisticsEname(cpCLogistics.getEname());
                        }

                        omsOrderService.updateOrderInfo(updateOrder);
                        String messageInfo = "OrderId" + order.getId() + "订单重新缺货占单分配到发货仓.返回实体发货仓Id[" + wareHouseId + "][" + cpCPhyWarehouse.getEname() + "],操作时间" + df.format(new Date());
                        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.WAREHOUSE_SERVICE.getKey(), messageInfo, null, null, SystemUserResource.getRootUser());

                        //利用新仓库去调用新增调用寻源逻辑实体仓.看是否能够占用成功
                        ValueHolderV14<Map<Long, Integer>> occupy = sgOccupiedInventoryService.emptyHandleOccupy(order, items, user);
                        if (occupy.getCode() == 0) {
                            this.handleOrder(order, items, wareHouseId);
                            sgOccupiedInventoryService.handleOccupy(order, items, occupy, user);
                            //调用实体仓变更调整逻辑发货单日志
                            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.WAREHOUSE_UPDATE.getKey(),
                                    "订单OrderID" + order.getId() + "的订单实体仓变更调整逻辑发货单执行成功,操作时间" + df.format(new Date()), null, null, user);
                        } else {
                            // 福袋商品   缺货重新抽取
                            if (isFortuneBag) {
                                //ValueHolderV14 vh = omsFortuneBagAgainSmokeService.againFortuneBagSmoke(ocBorderDto, orderItemList, user);
                                ValueHolderV14 vh = omsFortuneBagAgainSmokeService.againFortuneBagDraw(ocBorderDto.getId(),user);
                                if (!vh.isOK()) {
                                    throw new NDSException(Resources.getMessage(vh.getMessage()));
                                }
                            } else {
                                OcBOrder newOrder = new OcBOrder();
                                String erroreMessage = "订单OrderID" + ocBorderDto.getId() + "的订单缺货重新占单继续缺货或者重试占用库存调用异常" + occupy.getMessage();
                                newOrder.setId(ocBorderDto.getId());
                                newOrder.setSysremark(SplitMessageUtil.splitMesssage(erroreMessage));
                                omsOrderService.updateOrderInfo(newOrder);
                                sgOccupiedInventoryService.handleOccupy(order, items, occupy, user);
                                throw new NDSException(Resources.getMessage(erroreMessage));
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("缺货重新占单异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            OcBOrder newOrder = new OcBOrder();
            String erroreMessage = "订单OrderId" + ocBorderDto.getId() + "异常信息-->" + ex.getMessage();
            newOrder.setId(ocBorderDto.getId());
            newOrder.setSysremark(SplitMessageUtil.splitMesssage(erroreMessage));
            omsOrderService.updateOrderInfo(newOrder);
            throw new NDSException(Resources.getMessage("订单OrderId" + ocBorderDto.getId() + "异常信息-->", ex.getMessage()));
        }
    }


    private void handleOrder(OcBOrder order, List<OcBOrderItem> items, Long wareHouseId) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        OcBOrder newOrder = new OcBOrder();
        newOrder.setId(order.getId());
//        //查询实体仓
//        CpCPhyWarehouse cpCPhyWarehouse = orderDistributeWarehouseService.queryByWarehouseId(wareHouseId);
//        //set实体仓Id
//        newOrder.setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
//        //设置ename
//        newOrder.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
//        //设置ecode
//        newOrder.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
//
        //然后对于新订单执行分配物流服务
        OcBOrderRelation orderRelation = new OcBOrderRelation();
        orderRelation.setOrderInfo(order);
        orderRelation.setOrderItemList(items);


        // Long cpClogisticsId = omsOrderDistributeLogisticsService.orderDistributeLogistics(orderRelation, SystemUserResource.getRootUser());
        // 然后再去查询物流公司合法性校验
        // CpCLogistics cpCLogistics = omsOrderLogisticsService.queryLogisticsById(cpClogisticsId);
        CpCLogistics cpCLogistics = omsOrderDistributeLogisticsService.distributeLogistics(orderRelation);
        if (cpCLogistics != null) {
            String message = "订单OrderId" + order.getId() + "订单重新缺货占单调用分物流服务,订单分配到物流公司.返回物流公司Id[" + cpCLogistics.getId() + "][" + cpCLogistics.getEname() + "],操作时间" + df.format(new Date());
            newOrder.setCpCLogisticsId(cpCLogistics.getId());
            newOrder.setCpCLogisticsEname(cpCLogistics.getEname());
            newOrder.setCpCLogisticsEcode(cpCLogistics.getEcode());
            //20190909去掉正常系统备注
            //newOrder.setSysremark(SplitMessageUtil.splitMesssage(message));
            //插入分物流日志
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), message, null, null, SystemUserResource.getRootUser());
        } else {
            String errorMessage = "订单OrderId" + order.getId() + "订单重新缺货占单调用分物流服务未匹配到有效物流公司,操作时间" + df.format(new Date());
            newOrder.setSysremark(SplitMessageUtil.splitMesssage(errorMessage));
            //为了清除原有的物流公司
            newOrder.setCpCLogisticsId(0L);
            newOrder.setCpCLogisticsEname("");
            newOrder.setCpCLogisticsEcode("");
            //插入日志
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), errorMessage, null, null, SystemUserResource.getRootUser());
        }
        omsOrderService.updateOrderInfo(newOrder);

    }


    /**
     * 查询全部满足明细库存的实体仓
     *
     * @param ocBorderDto   订单对象
     * @param orderItemList 未退款明细
     * @return List<Long>
     */
    public List<Long> queryCpwareHouseIdList(OcBOrder ocBorderDto, List<OcBOrderItem> orderItemList) {

        List<SgBStorageInclShare> storageQueryResultList = new ArrayList<>();
        //查找缺货订单下的对应逻辑仓
        List<Long> storeList = omsQueryWareHouseService.queryStoreList(ocBorderDto.getCpCPhyWarehouseId());
        if (CollectionUtils.isEmpty(storeList)) {
            throw new NDSException(Resources.getMessage("订单OrderId" + ocBorderDto.getId()
                    + "的订单实体仓下面无逻辑仓！"));
        }
        //查找店铺下面的逻辑供货仓
        List<Long> shopStoreList = omsSyncStockStrategyService.queryShopStoreList(ocBorderDto.getCpCShopId(), storeList);
        if (CollectionUtils.isEmpty(shopStoreList)) {
            throw new NDSException(Resources.getMessage("订单OrderId" + ocBorderDto.getId()
                    + "的订单店铺下面无逻辑供货仓！"));
        }
        //取出订单明细条码Code
        Set<Long> skuIdSet = new HashSet<>();
        for (OcBOrderItem orderItem : orderItemList) {
            skuIdSet.add(orderItem.getPsCSkuId());
        }
        ArrayList<Long> skuIds = new ArrayList<>(skuIdSet);
        try {
            ValueHolderV14<List<SgBStorageInclShare>> holderV14 = sgRpcService
                    .querySumStorageGrpPhyByRedis(skuIds, shopStoreList, ocBorderDto.getCpCPhyWarehouseId(), SystemUserResource.getRootUser());
            storageQueryResultList = holderV14.getData();
        } catch (NDSException ex) {
            throw new NDSException(Resources.getMessage("订单OrderId[" + ocBorderDto.getId() + "],寻找发货仓库库存失败！" + ex.getMessage()));
        }
        //将skuCode 和购买数量存在Map集合里面
        Map<Long, BigDecimal> stringListMap = new HashMap<>();
        for (OcBOrderItem orderItem : orderItemList) {
            //取出明细购买数量 相同skuCode  数量累加
            BigDecimal qty = (orderItem.getQty() == null ? BigDecimal.ZERO : orderItem.getQty());
            //避免出现不同明细相同sku,若出现,计算累加和
            if (stringListMap.containsKey(orderItem.getPsCSkuId())) {
                stringListMap.put(orderItem.getPsCSkuId(), qty.add(stringListMap.get(orderItem.getPsCSkuId())));
            } else {
                stringListMap.put(orderItem.getPsCSkuId(), qty);
            }
        }
        //满足库存的临时对象
        List<WareHouseResult> wareHouseResultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(storageQueryResultList)) {
            //逻辑仓对应一个skuCode和可用数量
            for (SgBStorageInclShare sgBStorage : storageQueryResultList) {
                BigDecimal qtyAvailable = (sgBStorage.getQtyAvailable() == null ? BigDecimal.ZERO : sgBStorage.getQtyAvailable());
                //将符合条件的逻辑仓都存到集合中  [sku相同  购买数量<库存数量]
                if ((stringListMap.containsKey(sgBStorage.getPsCSkuId())) && (stringListMap.get(sgBStorage.getPsCSkuId()).compareTo(qtyAvailable) <= 0)) {
                    wareHouseResultList.add(new WareHouseResult(sgBStorage.getPsCSkuId(), sgBStorage.getCpCPhyWarehouseId()));
                }
            }
        }
        Map<Long, StringBuffer> wareHouseListMap = new HashMap<>();
        for (WareHouseResult wareHouseResult : wareHouseResultList) {
            StringBuffer stringBuffer = new StringBuffer();
            if (wareHouseListMap.containsKey(wareHouseResult.getWareHosueId())) {
                wareHouseListMap.put(wareHouseResult.getWareHosueId(), wareHouseListMap.get(wareHouseResult.getWareHosueId()).append(",").append(wareHouseResult.getSkuId()));
            } else {
                wareHouseListMap.put(wareHouseResult.getWareHosueId(), stringBuffer.append(wareHouseResult.getSkuId()));
            }
        }
        List<Long> wareHouseIds = new ArrayList<>();
        //得到key集合
        for (Long key : wareHouseListMap.keySet()) {
            StringBuffer skuCodeString = wareHouseListMap.get(key);
            //判断当前实体发货仓库的明细sku是否都满足库存
            boolean reulst = orderDistributeWarehouseService.checkContainSkuId(skuCodeString, skuIds);
            if (reulst) {
                wareHouseIds.add(key);
            }
        }
        //实体仓如果没有找到返回空
        if (CollectionUtils.isEmpty(wareHouseIds)) {
            return new ArrayList<>();
        }
        //聚合过得实体仓去重
        List<Long> secondWareHouseIdTempList = wareHouseIds.stream().distinct().collect(Collectors.toList());
        //过滤京东货到付款不发的仓库
        List<Long> secondWareHouseIds = Lists.newArrayList();
        //京东货到付款的订单不发仓库[不支持的仓库包含：菜鸟华南仓、菜鸟华北仓、菜鸟华东仓、菜鸟西南仓、菜鸟华中仓]
        if (PlatFormEnum.JINGDONG.getCode().equals(ocBorderDto.getPlatform()) && OmsPayType.CASH_ON_DELIVERY.toInteger() == ocBorderDto.getPayType()) {
            OcBOrderRelation orderRelation = new OcBOrderRelation();
            orderRelation.setOrderInfo(ocBorderDto);
            secondWareHouseIds = orderDistributeWarehouseService.getJdNoSendWareHouseList(secondWareHouseIdTempList, orderRelation);
        } else {
            secondWareHouseIds = secondWareHouseIdTempList;
        }
        return secondWareHouseIds;
    }

    /**
     * 自动任务执行入口
     *
     * @param user    用户对象
     * @param orderId 订单ID
     * @return ValueHolderV14
     */
    //@Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 checkAutoSerachOrderInfo(User user, Long orderId, boolean isThrowFlag) {

        ValueHolderV14 vh = new ValueHolderV14();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                if (checkOrderInfo(user, orderId, isThrowFlag)) {
                    vh.setCode(0);
                    vh.setMessage("订单OrderId" + orderId + "的订单缺货自动寻仓任务执行成功! ");
                    return vh;
                } else {
                    vh.setCode(-1);
                    vh.setMessage("订单OrderId" + orderId + "的订单缺货重新占单依旧缺货,需要调整库存之后再执行! ");
                    return vh;
                }
            } else {
                vh.setCode(-1);
                vh.setMessage("订单OrderId" + orderId + "当前订单其他人在操作，请稍后再试! ");
                return vh;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("订单缺货自动寻仓任务执行异常,异常信息为:{}", orderId), Throwables.getStackTraceAsString(ex));
            vh.setCode(-1);
            vh.setMessage("订单OrderId" + orderId + "的订单缺货自动寻仓任务执行异常!异常信息--> " + ex.getMessage());
            return vh;
        } finally {
            redisLock.unlock();
        }
    }
}