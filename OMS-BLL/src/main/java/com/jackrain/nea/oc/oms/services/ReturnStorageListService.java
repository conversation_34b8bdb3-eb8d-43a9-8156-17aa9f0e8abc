package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4RefundIn;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundBatchMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.oc.oms.model.constant.RefundInConstant;
import com.jackrain.nea.oc.oms.model.enums.IsGenAdjustEnum;
import com.jackrain.nea.oc.oms.model.enums.IsGenMinusAdjustEnum;
import com.jackrain.nea.oc.oms.model.enums.IsOffMatchEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWithoutOrigEnum;
import com.jackrain.nea.oc.oms.model.enums.MatchingSate;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.relation.ReturnOrderRollbackRelation;
import com.jackrain.nea.oc.oms.model.request.OmsRefundInRequest;
import com.jackrain.nea.oc.oms.model.result.QueryOcRefundInResult;
import com.jackrain.nea.oc.oms.model.result.QueryOcbReturnAndRefundItemResult;
import com.jackrain.nea.oc.oms.model.result.QueryRefundInResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBRefundBatch;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInExt;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderEtx;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.permission.OcOrderAuthorityMgtService;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sg.service.AddOrderNoticeAndOutService;
import com.jackrain.nea.sg.service.SgStockAdjustmentService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: 夏继超
 * @since: 2019/4/1
 * create at : 2019/4/1 11:13
 */
@Slf4j
@Component
public class ReturnStorageListService {
    @Autowired
    OcBRefundInMapper refundInMapper;
    @Autowired
    OcBRefundInProductItemMapper inProductItem;
    @Autowired
    OcBReturnOrderMapper returnOrderMapper;
    @Autowired
    OcBReturnOrderRefundMapper refundMapper;
    @Autowired
    ReturnReceiptMatchService returnReceiptMatchService;
    @Autowired
    ScanIncomingService scanIncomingService;
    @Autowired
    OcBRefundBatchMapper batchMapper;
    @Autowired
    OcBRefundInLogService refundInLogService;
    @Autowired
    PsRpcService psRpcService;
    @Autowired
    AddOrderNoticeAndOutService addOrderNoticeAndOutService;
    @Autowired
    SgStockAdjustmentService adjustmentService;
    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    OcCancelChangingOrRefundService cancelChangingOrRefundService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBReturnOrderExchangeMapper exchangeMapper;
    @Autowired
    private OcOrderAuthorityMgtService ocOrderAuthorityMgtService;

    @Autowired
    private OcBReturnStockInService ocBReturnStockInService;

/*    @Autowired
    private OcRefundInMatchService ocRefundInMatchService;*/

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private ReturnOrderAuditService returnOrderAuditService;

    /**
     * 字符串转集合
     *
     * @param param 传入的参数
     * @return 返回的信息
     */
    public static List stringToList(String param) {
        List list = new ArrayList();
        String[] split = param.split(",");
        for (String s : split) {
            list.add(Long.valueOf(s));
        }
        return list;
    }

    /**
     * 退货入库单列表
     *
     * @param param 传入的参数
     * @param user  当前的用户
     * @return 返回的值
     */
    public ValueHolderV14 returnStorageList(String param, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        JSONObject object = JSONObject.parseObject(param);
        QueryOcRefundInResult result = new QueryOcRefundInResult();
        //设置明细查询字段
        JSONObject childKeys = new JSONObject();
        try {
            //获取分页显示的页数
            Integer start = object.getInteger("currentPage");
            // export get es data 1.1 author by xiWen. start
            boolean flag = false;
            final int resultCode = 3;
            if (object != null && object.containsKey("esExport")) {
                flag = true;
                object.remove("esExport");
                if (start == null) {
                    start = 1;
                }
            }
            // export get es data 1.2 author by xiWen. end
            //获取每页显示的数量 默认50条
            Integer count = object.getInteger("pageSize") == null ? 50 : object.getInteger("pageSize");
            //转换实体
            object.remove("currentPage");
            object.remove("pageSize");

            //获取查询条件 判断是否为空 如果不为空则设置到ES
            JSONObject eSObject = findSelect(object, childKeys);

            if (CollectionUtils.isEmpty(eSObject)) {
            }
            JSONObject filter = new JSONObject();

            if (object.containsKey("beginDate") && object.containsKey("endDate")) {
                if (!StringUtils.isBlank(object.getString("beginDate")) && !StringUtils.isBlank(object.getString(
                        "endDate"))) {
                    String beginDate = object.getString("beginDate");
                    String endDate = object.getString("endDate");
                    Long dateBegin = null;
                    Long dateEnd = null;
                    dateBegin = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(beginDate).getTime();
                    dateEnd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endDate).getTime();
                    if (dateBegin != null && dateEnd != null) {
                        filter.put("CREATIONDATE", dateBegin + "~" + dateEnd);
                    }
                }
            }
            //增加匹配时间查询
            if (object.containsKey("MATCHED_TIME")) {
                JSONArray reservevarchar03 = object.getJSONArray("MATCHED_TIME");
                if (reservevarchar03 != null && reservevarchar03.size() > 0) {
                    Date beginTime = reservevarchar03.getDate(0);
                    //String format6 = DateFormatUtils.format(beginTime, "yyyy-MM-dd HH:mm:ss");
                    Date endTime = reservevarchar03.getDate(1);
                    //String format7 = DateFormatUtils.format(endTime, "yyyy-MM-dd HH:mm:ss");
                    if (beginTime != null && endTime != null) {

                        filter.put("MATCHED_TIME", beginTime.getTime() + "~" + endTime.getTime());
                    }
                }
            }
            if (eSObject.getString("BEGINDATE") != null && eSObject.getString("ENDDATE") != null && eSObject.containsKey("MATCHED_TIME")) {
                eSObject.remove("BEGINDATE");
                eSObject.remove("ENDDATE");
            }
            //如果关闭匹配查全部就直接去除
            if (eSObject.containsKey("IS_OFF_MATCH")) {
                JSONArray is_off_match = eSObject.getJSONArray("IS_OFF_MATCH");
                if (is_off_match.contains("bSelect-all")) {
                    eSObject.remove("IS_OFF_MATCH");
                }
            }
            //如果是否正品查全部就直接去除
            if (childKeys.containsKey("PRODUCT_MARK")) {
                JSONArray productMark = childKeys.getJSONArray("PRODUCT_MARK");
                if (productMark.contains("bSelect-all")) {
                    childKeys.remove("PRODUCT_MARK");
                }
            }
            eSObject.remove("MATCHED_TIME");
            JSONArray orderKeys = new JSONArray();
            JSONObject order = new JSONObject();
            orderKeys.add(order);

            // xiwen.start.permission
            //   filterSearchCondition(usrPem, eSObject);
            // xiwen.end
            JSONObject search = ES4RefundIn.findJSONObjectByeSObjectFilterChildKeys(eSObject, filter, childKeys,
                    count, (start - 1) * count);
            if (search == null) {
                result.setQueryResult(null);
                result.setPageNum(0);
                result.setPageSize(0);
                result.setTotalNum(0);
                result.setTotalSize(0);
                vh.setMessage("成功");
                vh.setCode(0);
                vh.setData(result);
                return vh;
            }
            JSONArray data = search.getJSONArray("data");
            if (CollectionUtils.isEmpty(data)) {
                result.setQueryResult(null);
                result.setPageNum(0);
                result.setPageSize(0);
                result.setTotalNum(0);
                result.setTotalSize(0);
                vh.setMessage("成功");
                vh.setCode(0);
                vh.setData(result);
                return vh;
            }
            //获取IDS编号数组
            JSONArray jsonArray = new JSONArray();
            for (int i = 0; i < data.size(); i++) {
                String id = "" + data.getJSONObject(i).getString("ID") + "";
                jsonArray.add(id);
            }

            // export get es data 1.2 author by xiWen. start
            if (flag) {
                vh.setData(jsonArray);
                vh.setCode(resultCode);
                return vh;
            }
            // export get es data 1.2 author by xiWen. end

            Integer totalCount = search.getInteger("total");
            String join = StringUtils.join(jsonArray, ",");
            List list = stringToList(join);
            //通过数据库中获取列表数据
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.in("id", list);
            wrapper.orderByDesc("creationdate");
            List<OcBRefundIn> ocBRefundIns = refundInMapper.selectList(wrapper);
            List<OcBRefundInExt> ocBRefundInExts = new ArrayList<>();
            // 组装主子表数据返回
            assembleData(ocBRefundIns, ocBRefundInExts);
            result.setQueryResult(ocBRefundInExts);
            result.setPageNum(start);
            result.setPageSize(count);
            result.setTotalNum(totalCount % count == 0 ? totalCount / count : totalCount / count + 1);
            result.setTotalSize(totalCount);
            vh.setData(result);
            vh.setCode(0);
            vh.setMessage("成功");
        } catch (Exception e) {
            log.error(LogUtil.format("查询退货入库信息有误,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("查询退货入库信息有误，请检查搜索条件"));
            vh.setData(e.getMessage());
        }
        return vh;
    }

    /**
     * 组装数据
     *
     * @param ocBRefundIns    主表集合
     * @param ocBRefundInExts 扩展类集合
     * @throws ParseException
     */
    private void assembleData(List<OcBRefundIn> ocBRefundIns, List<OcBRefundInExt> ocBRefundInExts) throws ParseException {
        for (OcBRefundIn record : ocBRefundIns) {
            QueryWrapper query = new QueryWrapper();
            query.eq("oc_b_refund_in_id", record.getId());
            BigDecimal qtyAll = new BigDecimal(0);
            List<OcBRefundInProductItem> list1 = inProductItem.selectList(query);
            for (OcBRefundInProductItem productItem : list1) {
                BigDecimal qty = productItem.getQty();
                if (qty == null) {
                    qty = BigDecimal.ZERO;
                }
                qtyAll = qtyAll.add(qty);
            }
            OcBRefundInExt ocBRefundInExt = new OcBRefundInExt();
            ocBRefundInExt.setQtyAll(qtyAll);
            ocBRefundInExt.setItemList(Lists.newArrayList());
            if (record.getInStatus() != null) {
                if (ReturnStatus.WAREHOUSING_AND_SCRAP.toInteger().equals(record.getInStatus())) {
                    ocBRefundInExt.setInvalidState("已作废");
                } else {
                    ocBRefundInExt.setInvalidState("未作废");
                }
            } else {
                ocBRefundInExt.setInvalidState("未作废");
            }
            if (record.getInStatus() != null) {
                if (ReturnStatus.WAITING_FOR_STORAGE.toInteger().equals(record.getInStatus())) {
                    ocBRefundInExt.setWarehousingStatus("等待入库");
                } else if (ReturnStatus.WAREHOUSING.toInteger().equals(record.getInStatus())) {
                    ocBRefundInExt.setWarehousingStatus("已入库");
                } else if (ReturnStatus.WAREHOUSING_AND_SCRAP.toInteger().equals(record.getInStatus())) {
                    ocBRefundInExt.setWarehousingStatus("入库作废");
                } else if (ReturnStatus.UNCORRELATED_RETURN_BILL.toInteger().equals(record.getInStatus())) {
                    ocBRefundInExt.setWarehousingStatus("未关联退货单");
                } else {
                    ocBRefundInExt.setWarehousingStatus("异常");
                }
            }
            if (record.getSpecialType() != null) {
                if (OcBOrderConst.ORDER_SPECIAL_TYPE_NORMAL.equals(record.getSpecialType())) {
                    ocBRefundInExt.setSpecialtypeName("正常");
                } else if (OcBOrderConst.ORDER_SPECIAL_TYPE_WRONG_DEAL.equals(record.getSpecialType())) {
                    ocBRefundInExt.setSpecialtypeName("错发扫描处理");
                } else {
                    ocBRefundInExt.setSpecialtypeName("鞋盒条码与实物条码不符处理");
                }
            }
            if (record.getMatchStatus() != null) {
                if (MatchingSate.PARTIAL_MATCHING.toInteger() == record.getMatchStatus()) {
                    ocBRefundInExt.setMatchstatusname("部分匹配");
                } else if (MatchingSate.UNMATCHED.toInteger() == record.getMatchStatus()) {
                    ocBRefundInExt.setMatchstatusname("未匹配");
                } else if (MatchingSate.NOT_MATCH.toInteger() == record.getMatchStatus()) {
                    ocBRefundInExt.setMatchstatusname("无需匹配");
                } else {
                    ocBRefundInExt.setMatchstatusname("全部匹配");
                }
            } else {
                ocBRefundInExt.setMatchstatusname("状态值为空");
            }

            if (record.getIsOffMatch() != null) {
                if (IsOffMatchEnum.OFF_MATCH.toInteger() == record.getIsOffMatch()) {
                    ocBRefundInExt.setIsoffmatchname("否");
                } else {
                    ocBRefundInExt.setIsoffmatchname("是");
                }
            }
            ocBRefundInExt.setId(record.getId());
            ocBRefundInExt.setBatchNo(record.getBatchNo());
            ocBRefundInExt.setOcBRefundBatchId(record.getOcBRefundBatchId());
            ocBRefundInExt.setAllSku(record.getAllSku());
            ocBRefundInExt.setUserId(record.getUserId());
            ocBRefundInExt.setSourceCode(record.getSourceCode());
            ocBRefundInExt.setUserNick(record.getUserNick());
            ocBRefundInExt.setInStoreId(record.getInStoreId());
            ocBRefundInExt.setInStoreEcode(record.getInStoreEcode());
            ocBRefundInExt.setInStoreEname(record.getInStoreEname());
            ocBRefundInExt.setRemark(record.getRemark());
            ocBRefundInExt.setLogisticNumber(record.getLogisticNumber());
            ocBRefundInExt.setReceiverName(record.getReceiverName());
            ocBRefundInExt.setReceiverMobile(record.getReceiverMobile());
            ocBRefundInExt.setReceiverAddress(record.getReceiverAddress());
            ocBRefundInExt.setCpCLogisticsId(record.getCpCLogisticsId());
            ocBRefundInExt.setCpCLogisticsEcode(record.getCpCLogisticsEcode());
            ocBRefundInExt.setCpCLogisticsEname(record.getCpCLogisticsEname());
            ocBRefundInExt.setOrigOrderNo(record.getOrigOrderNo());
            ocBRefundInExt.setInStatus(record.getInStatus());
            ocBRefundInExt.setMatchStatus(record.getMatchStatus());
            ocBRefundInExt.setSpecialType(record.getSpecialType());
            ocBRefundInExt.setVersion(record.getVersion());
            ocBRefundInExt.setOwnerename(record.getOwnerename());
            ocBRefundInExt.setModifierename(record.getModifierename());
            ocBRefundInExt.setAdClientId(record.getAdClientId());
            ocBRefundInExt.setAdOrgId(record.getOwnerid());
            String format = DateFormatUtils.format(record.getCreationdate(), "yyyy-MM-dd HH:mm:ss");
            ocBRefundInExt.setCreateTime(format);
            ocBRefundInExt.setCreationdate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(format));
            String format1 = DateFormatUtils.format(record.getModifieddate(), "yyyy-MM-dd HH:mm:ss");
            ocBRefundInExt.setUpdateTime(format1);
            ocBRefundInExt.setModifieddate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(format1));
            ocBRefundInExt.setOwnerid(record.getOwnerid());
            ocBRefundInExt.setOwnername(record.getOwnername());
            ocBRefundInExt.setModifierid(record.getModifierid());
            ocBRefundInExt.setModifiername(record.getModifiername());
            ocBRefundInExt.setIsactive(record.getIsactive());
            ocBRefundInExt.setMatcher(record.getMatcher());
            ocBRefundInExt.setRemarkHandle(record.getRemarkHandle());
            ocBRefundInExt.setMatchedTime(record.getMatchedTime());
            ocBRefundInExt.setHandler(record.getHandler());
            ocBRefundInExt.setRemarkIn(record.getRemarkIn()); //  添加异常备注
            ocBRefundInExt.setCpCPhyWarehouseId(record.getCpCPhyWarehouseId());
            ocBRefundInExt.setCpCPhyWarehouseEcode(record.getCpCPhyWarehouseEcode());
            ocBRefundInExt.setCpCPhyWarehouseEname(record.getCpCPhyWarehouseEname());
            ocBRefundInExt.setIsOffMatch(record.getIsOffMatch());
            //新增WMS编号
            ocBRefundInExt.setWmsBillNo(record.getWmsBillNo());
            ocBRefundInExt.setVirtualInStatus(record.getVirtualInStatus());

            ocBRefundInExt.setNumLess(record.getNumLess());
            if (StringUtils.isNotBlank(record.getNumLess())) {
                if (AcConstant.IS_YES.equals(record.getNumLess())) {
                    ocBRefundInExt.setNumLessName("是");
                }
                else if (AcConstant.IS_NO.equals(record.getNumLess())) {
                    ocBRefundInExt.setNumLessName("否");
                }
            }
            ocBRefundInExt.setNumMore(record.getNumMore());
            if (StringUtils.isNotBlank(record.getNumMore())) {
                if (AcConstant.IS_YES.equals(record.getNumMore())) {
                    ocBRefundInExt.setNumMoreName("是");
                }
                else if (AcConstant.IS_NO.equals(record.getNumMore())) {
                    ocBRefundInExt.setNumMoreName("否");
                }
            }
            ocBRefundInExt.setProductDiff(record.getProductDiff());
            if (StringUtils.isNotBlank(record.getProductDiff())) {
                if (AcConstant.IS_YES.equals(record.getProductDiff())) {
                    ocBRefundInExt.setProductDiffName("是");
                }
                else if (AcConstant.IS_NO.equals(record.getProductDiff())) {
                    ocBRefundInExt.setProductDiffName("否");
                }
            }
            //查询明细数据
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("oc_b_refund_in_id", ocBRefundInExt.getId());
            List<OcBRefundInProductItem> productItemList = inProductItem.selectList(queryWrapper);
            ocBRefundInExt.setItemList(productItemList);
            ocBRefundInExts.add(ocBRefundInExt);
        }
    }

    /**
     * 退货入库单的新增编辑服务
     *
     * @param obj  传入的参数
     * @param user 当前的用户
     * @return 返回的数据
     */

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 returnStorageSave(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        JSONObject object = obj.getJSONObject("OcBRefundIn");
        OcBRefundIn ocBRefundIn1 = JSONObject.parseObject(object.toJSONString(), OcBRefundIn.class);
        OcBRefundBatch ocBRefundBatch = null;
        //查询批次对象
        if (ocBRefundIn1.getOcBRefundBatchId() != null) {
            ocBRefundBatch = batchMapper.selectById(ocBRefundIn1.getOcBRefundBatchId());
        }
        OcBRefundIn ocBRefundIn = refundInMapper.selectById(ocBRefundIn1.getId());
        boolean isoffMuatchFlag = false;//是否有做【是否关闭匹配】的修改,用于是否更新日志处使用
        //退货入库的编辑
        if (ocBRefundIn1.getId() != null) {
            OcBRefundIn in = new OcBRefundIn();
            in.setId(ocBRefundIn1.getId());
            //修改是否 匹配
            in.setIsOffMatch(ocBRefundIn1.getIsOffMatch());
            //判断 是否关闭匹配是否有修改，若有修改则走逻辑
            if (ocBRefundIn1.getIsOffMatch() != null && !ocBRefundIn1.getIsOffMatch().equals(ocBRefundIn.getIsOffMatch())) {
                isoffMuatchFlag = true;
                if (ocBRefundIn1.getIsOffMatch() == 0) {// 没勾选【是否关闭匹配】，是->否
                    setIsOffMatchFun(ocBRefundIn1.getId(), in);
                } else {//勾选【是否关闭匹配】，否-》是
                    in.setMatchStatus(MatchingSate.NOT_MATCH.toInteger());//勾选了是否关闭匹配，则主表匹配状态为：无需匹配
                }
            }
            //加入匹配人和处理备注
            in.setRemarkHandle(ocBRefundIn1.getRemarkHandle()); //处理人备注
            in.setHandler(ocBRefundIn1.getHandler());  // 处理人
            in.setRemark(ocBRefundIn1.getRemark());
            in.setModifieddate(new Date());
            in.setModifierid(Long.valueOf(user.getId()));
            in.setModifiername(user.getName());
            in.setModifierename(user.getEname());
            int i = refundInMapper.updateById(in);
            ocBRefundIn = refundInMapper.selectById(ocBRefundIn1.getId());
            ocBRefundIn.setRemark(ocBRefundIn1.getRemark());
            ocBRefundIn.setMatcher(ocBRefundIn1.getMatcher());
            ocBRefundIn.setRemarkHandle(ocBRefundIn1.getRemarkHandle());
            ocBRefundIn.setHandler(ocBRefundIn1.getHandler()); //处理人
            //修改是否 匹配
            ocBRefundIn.setIsOffMatch(ocBRefundIn1.getIsOffMatch());
            ocBRefundIn.setModifieddate(new Date());
            ocBRefundIn.setModifierid(Long.valueOf(user.getId()));
            ocBRefundIn.setModifiername(user.getName());
            ocBRefundIn.setModifierename(user.getEname());

            //修改明细
            updateItems(obj, ocBRefundIn1, user);
            if (i > 0) {
                //推送主表的es
                try {
                   /* Boolean aBoolean = SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources
                   .OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, ocBRefundIn,in
                   .getId());
                    if (!aBoolean) {
                        throw new NDSException(Resources.getMessage("更新退货入库单主表推送ES失败!", user.getLocale()));
                    }*/
                    //插入更新日志
                    if (isoffMuatchFlag) {//有做更新变更
                        if (ocBRefundIn1.getIsOffMatch() == 0) {// 没勾选【是否关闭匹配】,是->否
                            refundInLogService.addLog(ocBRefundIn1.getId(), "修改【是否关闭匹配】", "修改【是否关闭匹配】成功,由“是”修改为“否”",
                                    user);
                        } else {//勾选【是否关闭匹配】,否-》是
                            refundInLogService.addLog(ocBRefundIn1.getId(), "修改【是否关闭匹配】", "修改【是否关闭匹配】成功,由“否”修改为“是”",
                                    user);
                        }
                    } else {
                        refundInLogService.addLog(ocBRefundIn1.getId(), "修改退货入库单", "修改退货入库单成功", user);
                    }
                } catch (Exception e) {
                    throw new NDSException(Resources.getMessage("更新退货入库单主表推送ES失败!", user.getLocale()));
                }
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage(Resources.getMessage("更新成功", user.getLocale()));
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("更新失败", user.getLocale()));
            }
        } else {
            try {
                ValueHolderV14 v14 = setParams(obj, user, vh, object, ocBRefundBatch);
                if (!v14.isOK()) {
                    throw new NDSException(Resources.getMessage("退货新增失败-》" + v14.getMessage(), user.getLocale()));
                }
            } catch (Exception e) {
                throw new NDSException(Resources.getMessage("退货新增失败-》" + e.getMessage(), user.getLocale()));
            }
        }
        return vh;
    }

    /**
     * 根据退货入库id，查询判断明细是否全部匹配
     *
     * @param id 退货入库id
     */
    private void setIsOffMatchFun(Long id, OcBRefundIn in) {
        QueryWrapper query = new QueryWrapper();
        query.eq("oc_b_refund_in_id", id);
        query.eq("ISACTIVE", "Y");
        List<OcBRefundInProductItem> itemsAll = inProductItem.selectList(query);
        query.eq("is_match", 1);//已匹配
        List<OcBRefundInProductItem> items = inProductItem.selectList(query);
        if (itemsAll != null && items != null) {
            if (itemsAll.size() == items.size()) {//明细全匹配
                in.setMatchStatus(MatchingSate.MATCH_ALL.toInteger());
            } else if (items.size() > 0 && items.size() < itemsAll.size()) {//部分匹配
                in.setMatchStatus(MatchingSate.PARTIAL_MATCHING.toInteger());
            } else if (items.size() == 0) {//未匹配
                in.setMatchStatus(MatchingSate.UNMATCHED.toInteger());
            }
        }
    }

    /**
     * 更新退单明细表的数据
     *
     * @param obj
     * @param user
     */
    private void updateItems(JSONObject obj, OcBRefundIn ocBRefundIn, User user) {
        JSONArray ocBRefundInProductItem = obj.getJSONArray("ocBRefundInProductItem");
        List<OcBRefundInProductItem> productItems1 = JSONObject.parseArray(ocBRefundInProductItem.toJSONString(),
                OcBRefundInProductItem.class);

        List<OcBRefundInProductItem> productItems = new ArrayList<>();
        BigDecimal realQty = new BigDecimal(0);
        BigDecimal falseQty = new BigDecimal(0);
        for (int i1 = 0; i1 < productItems1.size(); i1++) {
            OcBRefundInProductItem item1 = productItems1.get(i1);
            OcBRefundInProductItem refundItem = inProductItem.selectById(item1.getId());
            if (item1.getProductMark() != null && !item1.getProductMark().equals(refundItem.getProductMark())) {
                if (item1.getProductMark().equals("1")) {
                    realQty = realQty.add(item1.getQty());
                } else {
                    falseQty = falseQty.add(item1.getQty());
                }
            }
            OcBRefundInProductItem item = new OcBRefundInProductItem();
            item.setProductMark(item1.getProductMark());
            item.setId(item1.getId());
            item.setModifierename(user.getEname());
            item.setModifiername(user.getName());
            item.setModifieddate(new Date());
            inProductItem.updateById(item);
            OcBRefundInProductItem item2 = inProductItem.selectById(item1.getId());
            item2.setProductMark(item1.getProductMark());
            item2.setModifierename(user.getEname());
            item2.setModifiername(user.getName());
            item2.setModifieddate(new Date());
            productItems.add(item2);
        }
        //推送明细表
//        try {
//            SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME,
//            OcElasticSearchIndexResources.OC_B_RETURN_IN_PRODUCT_ITEM_TYPE_NAME, productItems, "OC_B_REFUND_IN_ID");
//        } catch (Exception e) {
//            log.debug("更新退货入库单明细推送ES失败:" + e.getMessage());
//            throw new NDSException(Resources.getMessage("更新退货入库单明细推送ES失败!", user.getLocale()));
//        }
        //更新批次表的  正品和次品数量
        OcBRefundBatch ocBRefundBatch = batchMapper.selectById(ocBRefundIn.getOcBRefundBatchId());
        //判断是否修改了明细中的正品或者是次品，
        // 如果修改了正品就把正品数量减少  次品数量增加
        // 如果修改了次品，正品数量增加 次品数量减少
        if (ocBRefundBatch == null) {
            return;
        }
        int i = realQty.subtract(falseQty).intValue();

        if (ocBRefundBatch.getQty() != null) {
            ocBRefundBatch.setQty(ocBRefundBatch.getQty().add(new BigDecimal(i)));
        } else {
            ocBRefundBatch.setQty(BigDecimal.ZERO.add(new BigDecimal(i)));
        }
        if (ocBRefundBatch.getQtySubstandard() != null) {
            ocBRefundBatch.setQtySubstandard(ocBRefundBatch.getQtySubstandard().add(new BigDecimal(-i)));
        } else {
            ocBRefundBatch.setQtySubstandard(BigDecimal.ZERO.add(new BigDecimal(-i)));
        }

        batchMapper.updateById(ocBRefundBatch);
    }

    private ValueHolderV14 setParams(JSONObject obj, User user, ValueHolderV14 vh, JSONObject object,
                                     OcBRefundBatch ocBRefundBatch) {
        OcBRefundIn ocBRefundInTmp = JSONObject.parseObject(object.toJSONString(), OcBRefundIn.class);
        //补全主表的信息
        object.put("MATCH_STATUS", 0);
        if (ocBRefundInTmp != null) {
            if (ocBRefundInTmp.getIsOffMatch() != null) {
                if (ocBRefundInTmp.getIsOffMatch() == 1) {//勾选【是否关闭匹配】，主表匹配状态为:3 无需匹配
                    object.put("MATCH_STATUS", MatchingSate.NOT_MATCH.toInteger());
                }
            }
        }
        object.put("IN_STATUS", ReturnStatus.WAREHOUSING.toInteger());
        if (ocBRefundBatch != null) {
            object.put("IN_STORE_ECODE", ocBRefundBatch.getInStoreEcode());
            object.put("IN_STORE_ENAME", ocBRefundBatch.getInStoreEname());
            object.put("BATCH_NO", ocBRefundBatch.getBatchNo());
        }
        object.put("SPECIAL_TYPE", "0");
        object.put("VERSION", 0L);
        object.put("OWNERENAME", user.getEname());
        object.put("MODIFIERENAME", user.getEname());
        object.put("AD_CLIENT_ID", 37L);
        object.put("AD_ORG_ID", 27L);
        object.put("OWNERID", user.getId());
        object.put("OWNERNAME", user.getClientName());
        object.put("MODIFIEDDATE", new Date());
        object.put("CREATIONDATE", new Date());
        object.put("ISACTIVE", "Y");
        Object id = obj.get("ID");
        object.remove("ID");
        //根据id 查询退货主表信息
        //补全实体仓信息
        CpStore cpStore = cpRpcService.selectCpCStoreById(object.getLong("IN_STORE_ID"));
        if (cpStore != null) {
            object.put("CP_C_PHY_WAREHOUSE_ECODE", cpStore.getCpCPhyWarehouseEcode());
            object.put("CP_C_PHY_WAREHOUSE_ID", cpStore.getCpCPhyWarehouseId());
            object.put("CP_C_PHY_WAREHOUSE_ENAME", cpStore.getCpCPhyWarehouseEname());
        }
        OcBReturnOrder order = returnOrderMapper.selectById(Long.valueOf(id.toString()));
        object.put("ALL_SKU", order.getAllSku());
        /*object.put("CP_C_PHY_WAREHOUSE_ID", order.getCpCPhyWarehouseId());
        object.put("CP_C_PHY_WAREHOUSE_ECODE", order.getcp)*/
        //退货入库的新增
        JSONArray ocBRefundInProductItem = obj.getJSONArray("ocBRefundInProductItem");
        /*List<OcBRefundInProductItem> productItems = JSON.parseArray(JSON.toJSONString(ocBRefundInProductItem),
        OcBRefundInProductItem.class);
        List<OcBRefundInProductItem> items = new ArrayList<>();
        for (int i = 0; i < productItems.size(); i++) {
            OcBRefundInProductItem productItem = productItems.get(i);
            List<Integer> ids = new ArrayList<>();
            ProductSku productSku = productService.selectProductById(productItem.getPsCSkuId().toString());
            //明细新加二个字段 颜色和尺码
            productItem.setReserveVarchar01(productSku.getColorName());
            productItem.setReserveVarchar02(productSku.getSizeName());
            items.add(productItem);
        }*/
        JSONObject param = new JSONObject();
        param.put("IS_AFTER", 0);
        param.put("DATA", object);
        param.put("ITEM_LIST", ocBRefundInProductItem);
        param.put("ID", id);
        param.put("IS_FORCE", 0);
        ValueHolderV14 v14 = null;
        try {
            v14 = scanIncomingService.saveRefundOrder(param.toJSONString(), user);
            if (v14.getCode() != -1) {
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage(Resources.getMessage("退货入库新增成功", user.getLocale()));
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("退货入库新增失败-->" + v14.getMessage(), user.getLocale()));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("退货入库新增异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("退货入库新增失败-->" + e.getMessage(), user.getLocale()));
        }
        return vh;
    }

    /**
     * 退货入库单手动匹配检查
     *
     * @param obj  传入的参数
     * @param user 当前用户
     * @return
     */
    public ValueHolderV14 manualMatchingCheck(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        //判断退货入库单匹配状态，如果为“全部匹配”，则不允许选择，提示：“此退货入库单已经全部匹配入库”，
        Object id = obj.get("id");
        if (id != null) {
            OcBRefundIn ocBRefundIn = refundInMapper.selectById(Long.valueOf(id.toString()));
            if (ocBRefundIn != null) {
                if (ocBRefundIn.getIsOffMatch() == null) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("是否关闭匹配字段不能为空");
                    return vh;
                }
                //如果是否关闭匹配 字段为是 就直接  “此退货入库单已关闭匹配，不允许选择”
                if (1 == ocBRefundIn.getIsOffMatch()) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("此退货入库单已关闭匹配，不允许选择");
                    return vh;
                }
                if (ocBRefundIn.getMatchStatus() == null) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("匹配状态字段不能为空");
                    return vh;
                }
                if (MatchingSate.MATCH_ALL.toInteger() == ocBRefundIn.getMatchStatus()) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("此退货入库单已经全部匹配入库");
                } else {
                    //2.入库状态如果为：未入库，已作废，未关联退换货单，则不允许点击手工匹配按钮
                    if (!ReturnStatus.WAREHOUSING.toInteger().equals(ocBRefundIn.getInStatus())) {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage("入库状态不满足，不允许匹配");
                    } else {
                        //如果为其他状态：“未匹配”、“部分匹配”，则弹出手工匹配页面；
                        vh.setCode(ResultCode.SUCCESS);
                        vh.setMessage("校验成功");
                    }
                }
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("所选的记录不存在");
            }
        }
        return vh;
    }

    /**
     * 手工匹配的列表
     *
     * @param obj
     * @param user
     * @return
     */
    public ValueHolderV14 manualMatchingList(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        Object id = obj.get("id");
        QueryRefundInResult refundInResult = new QueryRefundInResult();
        if (id != null) {
            OcBRefundIn ocBRefundIn = refundInMapper.selectById(Long.valueOf(id.toString()));
            if (ocBRefundIn != null) {
                refundInResult.setOcBRefundIn(ocBRefundIn);
                QueryWrapper<OcBRefundInProductItem> wrapper = new QueryWrapper<>();
                wrapper.eq("oc_b_refund_in_id", Long.valueOf(id.toString()));
                List<OcBRefundInProductItem> items = inProductItem.selectList(wrapper);
                //转化商品标记的名称
                if (ocBRefundIn.getOrigOrderNo() != null) {
                    OcBOrder order = ocBOrderMapper.selectById(ocBRefundIn.getOrigOrderNo());//ljp add 客户需要详情展示店铺信息
                    refundInResult.setOcBOrder(order);
                }
                refundInResult.setInProductItem(items);
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("查询 成功");
                vh.setData(refundInResult);
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("传入数据有误");
            }
        }
        return vh;
    }

    /**
     * 退货入库单明细搜索按钮服务
     *
     * @param param 传入的参数
     * @param user  当前用户
     * @return
     */
    public ValueHolderV14 searchButtonsInDetail(String param, User user) {
        JSONObject object = JSON.parseObject(param);
        // es 查询条件
        JSONObject whereKeys = queryCriteria(object);
        ValueHolderV14 vh = new ValueHolderV14();
        JSONObject filterKeys = new JSONObject();
        JSONArray orderKeys = new JSONArray();
        Integer start = 0;
        Integer ranger = 10000;
        String[] fileds = {"ID"};
        JSONObject search = ES4ReturnOrder.getReturnOrderSearchByUserinfo(whereKeys, filterKeys, orderKeys, start,
                ranger, fileds);
        log.debug(this.getClass().getName() + "->searchButtonsInDetail(): es的出参" + search.toJSONString());
        JSONArray data = search.getJSONArray("data");
        if (data == null || data.size() == 0) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("暂无数据");
            return vh;
        }
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            ids.add(data.getJSONObject(i).getLong("ID"));
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("id", ids);
        //退单状态为：等待退货入库且是否原退字段值为否，或者退单虚拟入库状态为：“虚拟入库未入库状态
        //退单状态为等待退货入库且是否传wms字段值为否且是否生成零售字段为否，
        // 或者退单虚拟入库状态为虚拟入库未入库状态且是否生成零售字段为否切退货状态不是取消的:: reserve_bigint03 = 0 AND
        queryWrapper.last("AND ( (return_status = 20 AND is_todrp = 0) OR (invented_status = 1 AND is_todrp = 0 AND " +
                "return_status <> 60))");
        List<OcBReturnOrder> ocBReturnOrders = returnOrderMapper.selectList(queryWrapper);
        if (ocBReturnOrders == null || ocBReturnOrders.size() == 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("查询无数据", user.getLocale()));
            return vh;
        }
        //在根据主表数据查询出的对应的退货明细的数据

        List<OcBReturnOrderEtx> list = new ArrayList<>();
        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
            if (ocBReturnOrder != null) {
                //判断主表是不是有原单
                if (ocBReturnOrder.getOrigOrderId() != null) {
                    //设置实体仓的名字
                    // QueryOcbReturnAndRefundItemResult refundItemResult = new QueryOcbReturnAndRefundItemResult();
                    OcBReturnOrderEtx etx = new OcBReturnOrderEtx();
                    Long cpCPhyWarehouseId = ocBReturnOrder.getCpCPhyWarehouseId();
                    if (cpCPhyWarehouseId == null) {
                        etx.setShopName("暂无实体仓信息");
                    } else {
                        try {
                            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseId);
                            etx.setShopName(cpCPhyWarehouse.getEname());
                        } catch (Exception e) {
                            log.error(LogUtil.format("实体仓调用异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                            vh.setCode(ResultCode.FAIL);
                            vh.setMessage(Resources.getMessage("实体仓调用失败啊"));
                            return vh;
                        }
                    }
                    //查询退换货订单对应的退货入库明细
                    List<OcBReturnOrderRefund> returnProduct = refundMapper.selectByOcOrderId(ocBReturnOrder.getId());
                    for (OcBReturnOrderRefund ocBReturnOrderRefund : returnProduct) {
                        if ("1".equals(ocBReturnOrderRefund.getProductMark())) {
                            ocBReturnOrderRefund.setProductMark("正品");
                        } else if ("0".equals(ocBReturnOrderRefund.getProductMark())) {
                            ocBReturnOrderRefund.setProductMark("次品");
                        }
                    }
                    setParams(list, ocBReturnOrder, etx, returnProduct);
                } else {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("查询无符合的数据", user.getLocale()));
                    continue;
                }
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("当前记录不存在");
            }

        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(Resources.getMessage("查询成功!", user.getLocale()));
        vh.setData(list);
        return vh;
    }

    private JSONObject queryCriteria(JSONObject object) {
        JSONObject esSearch = new JSONObject();
        HashMap<String, Object> hashMap = new HashMap<>(object);
        Iterator<Map.Entry<String, Object>> iterator = hashMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> next = iterator.next();
            String key = next.getKey();
            Object value = next.getValue();
            String s = String.valueOf(value);
            if (StringUtils.isBlank(s)) {
                continue;

            }
            if ("buyer_nick".equalsIgnoreCase(key) || "receive_name".equalsIgnoreCase(key)) {
                esSearch.put(key.toUpperCase(), "*" + value + "*");
            } else if ("tid".equalsIgnoreCase(key) || "logistics_code".equalsIgnoreCase(key) || "receive_mobile".equalsIgnoreCase(key) || "id".equalsIgnoreCase(key)) {
                esSearch.put(key.toUpperCase(), value);
            }
        }
        return esSearch;
    }

    private void setParams(List<OcBReturnOrderEtx> list, OcBReturnOrder ocBReturnOrder, OcBReturnOrderEtx etx,
                           List<OcBReturnOrderRefund> returnProduct) {
        etx.setId(ocBReturnOrder.getId());
        etx.setAdOrgId(ocBReturnOrder.getAdOrgId());
        etx.setTid(ocBReturnOrder.getTid());
        etx.setIsForce(ocBReturnOrder.getIsForce());
        etx.setCpCStoreId(ocBReturnOrder.getCpCStoreId());
        etx.setCpCStoreEcode(ocBReturnOrder.getCpCStoreEcode());
        etx.setCpCStoreEname(ocBReturnOrder.getCpCStoreEname());
        etx.setOrdeSource(ocBReturnOrder.getOrdeSource());
        etx.setWmsCancelStatus(ocBReturnOrder.getWmsCancelStatus());
        etx.setReturnFlag(ocBReturnOrder.getReturnFlag());
        etx.setSysMsg(ocBReturnOrder.getSysMsg());
        etx.setIsReceiveConfirm(ocBReturnOrder.getIsReceiveConfirm());
        etx.setTbDisputeId(ocBReturnOrder.getTbDisputeId());
        etx.setWmsFailreason(ocBReturnOrder.getWmsFailreason());
        etx.setIsTowms(ocBReturnOrder.getIsTowms());
        etx.setIsNotlogmber(ocBReturnOrder.getIsNotlogmber());
        etx.setIsInstorage(ocBReturnOrder.getIsInstorage());
        etx.setIsExamine(ocBReturnOrder.getIsExamine());
        etx.setCheckFaileInfo(ocBReturnOrder.getCheckFaileInfo());
        etx.setIsCheck(ocBReturnOrder.getIsCheck());
        etx.setIsRefund(ocBReturnOrder.getIsRefund());
        etx.setIsTransfer(ocBReturnOrder.getIsTransfer());
        etx.setInerId(ocBReturnOrder.getInerId());
        etx.setInerEname(ocBReturnOrder.getInerEname());
        etx.setInerName(ocBReturnOrder.getInerName());
        etx.setCheckerId(ocBReturnOrder.getCheckerId());
        etx.setCheckerEname(ocBReturnOrder.getCheckerEname());
        etx.setCheckerName(ocBReturnOrder.getCheckerName());
        etx.setInTime(ocBReturnOrder.getInTime());
        etx.setAuditTime(ocBReturnOrder.getInTime());
        etx.setQtyInstore(ocBReturnOrder.getQtyInstore());
        etx.setReturnPhase(ocBReturnOrder.getReturnPhase());
        etx.setAllSku(ocBReturnOrder.getAllSku());
        etx.setOvertimeInterval(ocBReturnOrder.getOvertimeInterval());
        etx.setOrderflag(ocBReturnOrder.getOrderflag());
        etx.setIsReserved(ocBReturnOrder.getIsReserved());
        etx.setDistributorId(ocBReturnOrder.getDistributorId());
        etx.setCpCPhyWarehouseId(ocBReturnOrder.getCpCPhyWarehouseId());
        etx.setSellerNick(ocBReturnOrder.getSellerNick());
        etx.setUserid(ocBReturnOrder.getUserid());
        etx.setInventedStatus(ocBReturnOrder.getInventedStatus());
        etx.setProReturnStatus(ocBReturnOrder.getProReturnStatus());
        etx.setPlatform(ocBReturnOrder.getPlatform());
        etx.setShipAmt(ocBReturnOrder.getShipAmt());
        etx.setOrigOrderId(ocBReturnOrder.getOrigOrderId());
        etx.setReceiveName(ocBReturnOrder.getReceiveName());
        etx.setReceiverAreaName(ocBReturnOrder.getReceiverAreaName());
        etx.setReceiverCityName(ocBReturnOrder.getReceiverCityName());
        etx.setReceiverProvinceName(ocBReturnOrder.getReceiverProvinceName());
        etx.setReturnId(ocBReturnOrder.getReturnId());
        etx.setExchangeAmt(ocBReturnOrder.getExchangeAmt());
        etx.setReceiverAreaId(ocBReturnOrder.getReceiverAreaId());
        etx.setReceiverCityId(ocBReturnOrder.getReceiverCityId());
        etx.setReceiverProvinceId(ocBReturnOrder.getReceiverProvinceId());
        etx.setRemark(ocBReturnOrder.getRemark());
        etx.setLogisticsCode(ocBReturnOrder.getLogisticsCode());
        etx.setReceivePhone(ocBReturnOrder.getReceivePhone());
        etx.setReceiveMobile(ocBReturnOrder.getReceiveMobile());
        etx.setReceiveZip(ocBReturnOrder.getReceiveZip());
        etx.setReceiveAddress(ocBReturnOrder.getReceiveAddress());
        etx.setCpCLogisticsId(ocBReturnOrder.getCpCLogisticsId());
        etx.setCpCLogisticsEcode(ocBReturnOrder.getCpCLogisticsEcode());
        etx.setCpCLogisticsEname(ocBReturnOrder.getCpCLogisticsEname());
        etx.setReturnDesc(ocBReturnOrder.getReturnDesc());
        etx.setReturnReason(ocBReturnOrder.getReturnReason());
        etx.setBuyerNick(ocBReturnOrder.getBuyerNick());
        etx.setOrigSourceCode(ocBReturnOrder.getOrigSourceCode());
        etx.setReturnAmtOther(ocBReturnOrder.getReturnAmtOther());
        etx.setReturnAmtShip(ocBReturnOrder.getReturnAmtShip());
        etx.setReturnAmtList(ocBReturnOrder.getReturnAmtList());
        etx.setReturnAmtActual(ocBReturnOrder.getReturnAmtActual());
        etx.setReturnTime(ocBReturnOrder.getReturnTime());
        etx.setLastUpdateTime(ocBReturnOrder.getLastUpdateTime());
        etx.setReturnCreateTime(ocBReturnOrder.getReturnCreateTime());
        etx.setReturnStatus(ocBReturnOrder.getReturnStatus());
        etx.setOrigOrderStatus(ocBReturnOrder.getOrigOrderStatus());
        etx.setBillType(ocBReturnOrder.getBillType());
        etx.setIsAdd(ocBReturnOrder.getIsAdd());
        etx.setIsTodrp(ocBReturnOrder.getIsTodrp());
        etx.setIsToag(ocBReturnOrder.getIsToag());
        etx.setIsBack(ocBReturnOrder.getIsBack());
        etx.setCpCShopId(ocBReturnOrder.getCpCShopId());
        etx.setCpCShopTitle(ocBReturnOrder.getCpCShopTitle());
        etx.setConsignAmtSettle(ocBReturnOrder.getConsignAmtSettle());
        etx.setReturnType(ocBReturnOrder.getReturnType());
        etx.setIsManualAudit(ocBReturnOrder.getIsManualAudit());
        etx.setVersion(ocBReturnOrder.getVersion());
        etx.setAdClientId(ocBReturnOrder.getAdClientId());
        etx.setOwnerid(ocBReturnOrder.getOwnerid());
        etx.setOwnerename(ocBReturnOrder.getOwnerename());
        etx.setOwnername(ocBReturnOrder.getOwnername());
        etx.setCreationdate(ocBReturnOrder.getCreationdate());
        etx.setModifierid(ocBReturnOrder.getModifierid());
        etx.setModifierename(ocBReturnOrder.getModifierename());
        etx.setModifiername(ocBReturnOrder.getModifiername());
        etx.setModifieddate(ocBReturnOrder.getModifieddate());
        etx.setIsactive(ocBReturnOrder.getIsactive());
        etx.setAdClientId(ocBReturnOrder.getAdClientId());
        etx.setAdOrgId(ocBReturnOrder.getAdOrgId());
        etx.setCreationdate(ocBReturnOrder.getCreationdate());
        etx.setModifieddate(ocBReturnOrder.getModifieddate());
        etx.setOwnerid(ocBReturnOrder.getOwnerid());
        etx.setOwnername(ocBReturnOrder.getOwnername());
        etx.setModifierid(ocBReturnOrder.getModifierid());
        etx.setModifiername(ocBReturnOrder.getModifiername());
        etx.setIsactive(ocBReturnOrder.getIsactive());
        etx.setProductItems(returnProduct);
        list.add(etx);
    }

    /**
     * 手工匹配的弹出确认按钮
     * 测试
     *
     * @param param 传入的参数
     * @param user  当前用户
     * @return 返回的数据
     */
    public ValueHolderV14<JSONObject> manualMatchingConfirmationButton(JSONObject param, User user) {
        ValueHolderV14<JSONObject> vh = new ValueHolderV14<>();
        //退货入库主表id
        Long refundInId = param.getLong("refundInId");
        //退货入库明细的id
        JSONArray array = param.getJSONArray("refundId");
        List<Long> refundIds = JSONObject.parseArray(array.toJSONString(), Long.class);
        //退换货订单表的id
        Long id = param.getLong("id");
        if (CollectionUtils.isEmpty(refundIds) && id == null && refundInId == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("参数异常,请稍后再试！"));
            return vh;
        }
        // 可退数量校验
        this.checkRequestParams(refundInId, refundIds, id);

        QueryWrapper<OcBRefundInProductItem> wrapper = new QueryWrapper<>();
        wrapper.in("id", refundIds);
        wrapper.eq("oc_b_refund_in_id", refundInId);
        List<OcBRefundInProductItem> refundInProductItems = inProductItem.selectList(wrapper);
        List<OcBReturnOrderRefund> returnOrderRefunds = refundInMapper.selectByOcBreturnOrderId(id);

        if (CollectionUtils.isEmpty(refundInProductItems) && CollectionUtils.isEmpty(returnOrderRefunds)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("查询的数据不存在，请检查后重试！", user.getLocale()));
            return vh;
        }
        for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefunds) {
            if (returnOrderRefund.getPsCSkuId() == null || returnOrderRefund.getQtyRefund() == null) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("条码数量不能为空，数据异常", user.getLocale()));
                return vh;
            }
        }
        for (OcBRefundInProductItem refundInProductItem : refundInProductItems) {
            if (refundInProductItem.getPsCSkuId() == null || refundInProductItem.getQty() == null) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("条码数量不能为空，数据异常", user.getLocale()));
                return vh;
            }
            QueryWrapper<OcBReturnOrderRefund> queryWrapper = new QueryWrapper<>();
            if (refundInProductItem.getRealSkuId() != null) {
                queryWrapper.eq("ps_c_sku_id", refundInProductItem.getRealSkuId());
            } else {
                queryWrapper.eq("ps_c_sku_id", refundInProductItem.getPsCSkuId());
            }
            queryWrapper.ge("qty_refund", refundInProductItem.getQty());
            queryWrapper.eq("oc_b_return_order_id", id);
            List<OcBReturnOrderRefund> bReturnOrderRefunds = refundMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(bReturnOrderRefunds)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("条码数量不一致，请走强制匹配", user.getLocale()));
                return vh;
            }
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(Resources.getMessage("操作成功", user.getLocale()));
        JSONObject data = new JSONObject();
        data.put("oc_b_refund_in_id", id);
        vh.setData(data);
        return vh;
    }

    /**
     * 检查手工匹配请求参数
     *
     * @param refundInId    退货入库单ID
     * @param refundIds     退货入库单商品明细ids
     * @param returnOrderId 退换货单ID
     */
    private void checkRequestParams(Long refundInId, List<Long> refundIds, Long returnOrderId) {
        // 退货入库单商品明细
        List<OcBRefundInProductItem> ocBRefundInProductItems = inProductItem.selectForItem(refundInId);
        // 退货商品明细
        List<OcBReturnOrderRefund> ocBReturnOrderRefunds = refundInMapper.selectByOcBreturnOrderId(returnOrderId);
        // 退换货单
        OcBReturnOrder returnOrder = returnOrderMapper.selectById(returnOrderId);
        if (Objects.isNull(returnOrder)) {
            throw new NDSException("未查询到退换单！");
        }

        Long ocBRefundInId = returnOrder.getOcBRefundInId();

        if (Objects.isNull(ocBRefundInId) || ocBRefundInId.equals(refundInId)) {
            if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnOrder.getReturnStatus()) ||
                    (OcBOrderConst.INVENTED_STATUS_UN.equals(returnOrder.getInventedStatus()) &&
                            ReturnStatusEnum.CANCLE.getVal().equals(returnOrder.getReturnStatus()))) {
                // 查询确定时过滤
                ocBRefundInProductItems = ocBRefundInProductItems.stream().filter(
                        o -> refundIds.contains(o.getId()) && Objects.isNull(o.getOcBReturnOrderId())
                ).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(ocBRefundInProductItems)) {
                    throw new NDSException("手工匹配失败: 没有匹配到入库单商品明细！");
                }

                // 条码、数量
                Map<String, List<OcBRefundInProductItem>> prodInItemMap = ocBRefundInProductItems.stream()
                        .collect(Collectors.groupingBy(OcBRefundInProductItem::getPsCSkuEcode));

                Map<String, List<OcBReturnOrderRefund>> orderRefundMap = ocBReturnOrderRefunds.stream()
                        .collect(Collectors.groupingBy(OcBReturnOrderRefund::getPsCSkuEcode));

                for (Map.Entry<String, List<OcBRefundInProductItem>> prodInItemEty : prodInItemMap.entrySet()) {
                    String key = prodInItemEty.getKey();

                    List<OcBReturnOrderRefund> returnOrderRefunds = orderRefundMap.get(key);
                    if (CollectionUtils.isEmpty(returnOrderRefunds)) {
                        String msg = String.format("手工匹配失败: 退货明细没有匹配到入库明细SKU编码【%s】", key);
                        throw new NDSException(msg);
                    }

                    BigDecimal qtyRefund = BigDecimal.ZERO;
                    long match = 0;
                    for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefunds) {
                        qtyRefund = qtyRefund.add(returnOrderRefund.getQtyRefund());
                        match = match + returnOrderRefund.getQtyMatch();
                    }
                    BigDecimal qtyMatch = new BigDecimal(match);
                    BigDecimal inTotalQty = BigDecimal.ZERO;
                    List<OcBRefundInProductItem> refundInProductItems = prodInItemEty.getValue();
                    for (OcBRefundInProductItem inProductItem : refundInProductItems) {
                        inTotalQty = inTotalQty.add(inProductItem.getQty());
                    }

                    // 入库数量 compare 剩余匹配数量(申请数量 - 匹配数量)
                    BigDecimal canMatchQty = qtyRefund.subtract(qtyMatch);
                    if (inTotalQty.compareTo(canMatchQty) > 0) {
                        throw new NDSException("手工匹配失败: 同SKU入库数量大于申请数量！");
                    }
                }
            }
        } else {
            throw new NDSException("手工匹配失败: 退货入库单编号不一致！");
        }
    }

    /**
     * 筛选条件 排除为空的字段
     *
     * @param param     传入的参数
     * @param childKeys 查询字表的参数
     * @return
     */
    private JSONObject findSelect(JSONObject param, JSONObject childKeys) {
        HashMap<String, Object> hashMap = new HashMap<>(param);
        Iterator<Map.Entry<String, Object>> iterator = hashMap.entrySet().iterator();
        HashMap<String, Object> need = new HashMap<>();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> map = iterator.next();
            String key = map.getKey();
            Object value = map.getValue();
            if (StringUtils.isEmpty(value.toString())) {
                iterator.remove();
            } else if ("IN_STORE_ID".equalsIgnoreCase(key) || "CP_C_LOGISTICS_ID".equalsIgnoreCase(key) ||
                    "OC_B_REFUND_BATCH_ID".equalsIgnoreCase(key) || "ID".equalsIgnoreCase(key)/*|| "LOGISTIC_NUMBER"
            .equalsIgnoreCase(key)
            */ || "ORIG_ORDER_NO".equalsIgnoreCase(key) || "RECEIVER_MOBILE".equalsIgnoreCase(key) || "SOURCE_CODE".equalsIgnoreCase(key)) {
                String storeId = param.getString(key);
                //去空格，去中文逗号
                String replace1 = storeId.replace(" ", "");
                String replace = replace1.replace("，", ",");
                String[] split = replace.split(",");
                JSONArray array = new JSONArray();
                for (int i = 0; i < split.length; i++) {
                    array.set(i, split[i]);
                }
                need.put(key.toUpperCase(), array);
            } else if ("ID".equalsIgnoreCase(key)) {
                need.put(key.toUpperCase(), value);
            } else if (RefundInConstant.RECEIVER_NAME.equalsIgnoreCase(key) || RefundInConstant.REMARK.equalsIgnoreCase(key)
                    || RefundInConstant.RECEIVER_ADDRESS.equalsIgnoreCase(key) || RefundInConstant.MATCHER.equalsIgnoreCase(key)
                    || RefundInConstant.REMARK_HANDLE.equalsIgnoreCase(key) || RefundInConstant.OWNER_ENAME.equalsIgnoreCase(key)
                    || RefundInConstant.OWNER_NAME.equalsIgnoreCase(key) || RefundInConstant.USER_NICK.equalsIgnoreCase(key)
                    || RefundInConstant.ALL_SKU.equalsIgnoreCase(key)) {
                need.put(key.toUpperCase(), "*" + value + "*");
            } else if ("PS_C_PRO_ECODE".equalsIgnoreCase(key) || "GBCODE".equalsIgnoreCase(key) || "REAL_SKU_ECODE".equalsIgnoreCase(key)
                    || "REAL_GBCODE".equalsIgnoreCase(key) || "OC_B_RETURN_ORDER_ID".equalsIgnoreCase(key)) {
                String storeId = param.getString(key);
                //去空格，去中文逗号
                String replace1 = storeId.replace(" ", "");
                String replace = replace1.replace("，", ",");
                String[] split = replace.split(",");
                JSONArray array = new JSONArray();
                for (int i = 0; i < split.length; i++) {
                    array.set(i, split[i]);
                }
                // 用来解决实收国标码与主表字段相同的情况
                if ("REAL_GBCODE".equalsIgnoreCase(key)) {
                    childKeys.put("RESERVE_VARCHAR01", array);
                } else {
                    childKeys.put(key.toUpperCase(), array);
                    if ("OC_B_RETURN_ORDER_ID".equalsIgnoreCase(key)) {
                        need.remove(key);
                    }
                }
            } else if ("PRODUCT_MARK".equalsIgnoreCase(key)) {
                childKeys.put(key.toUpperCase(), value);
            } else if ("LOGISTIC_NUMBER".equalsIgnoreCase(key)) {//物流单号支持模糊查询 liqb
                String storeId = param.getString(key);
                //去空格，去中文逗号
                String replace1 = storeId.replace(" ", ",");
                String replace = replace1.replace("，", ",");
                String[] split = replace.split(",");
                JSONArray array = new JSONArray();
                for (int i = 0; i < split.length; i++) {
                    array.set(i, String.format("%s*", split[i]));
                }
                need.put(key.toUpperCase(), array);
            } else {
                need.put(key.toUpperCase(), value);
            }
        }
        if (CollectionUtils.isEmpty(need)) {

            return new JSONObject();
        } else {
            return new JSONObject(need);
        }
    }

    /**
     * 手工匹配的列表保存按钮
     *
     * @param param 入库信息
     * @param user  匹配用户
     * @return 匹配结果
     */
    public ValueHolderV14 markSureButton(JSONObject param, User user) {
        OmsRefundInRequest refundInRequest = initValidateParam(param, user);
        OcBRefundIn refundIn = refundInRequest.getRefundIn();
        Map<Long, List<OcBRefundInProductItem>> inProdItemMap = doFilterRefundInRequest(refundInRequest);

        // 未入库的退单编号
        Set<Long> rtnOrderIds = inProdItemMap.keySet();

        AssertUtil.assertException(CollectionUtils.isEmpty(rtnOrderIds), "不存在匹配到退单的商品明细");

        Map<Long, ReturnOrderRollbackRelation> rollbackData = new HashMap<>();

        String lockRedisKey = BllRedisKeyResources.buildLockReturnInKey(refundIn.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {

                StringBuilder skuSb = new StringBuilder();

                // 退单匹配校验以及赋值处理
                ReturnStorageListService service = ApplicationContextHandle.getBean(ReturnStorageListService.class);
                service.checkMatchRtnOrderProcess(refundIn.getId(), rtnOrderIds, rollbackData, inProdItemMap, skuSb);

                // 1. 退货入库单
                updateRefundInOrder(refundIn, user);

                // 2. 更新明细
                //Set<Long> returnOrderIdSet = updateProductItems(refundInRequest, skuSb);

                // 3. wms撤回
                wmsReCallBackReturnOrder(rtnOrderIds, user);

                // 4. 调用退货入库单匹配服务。
                ValueHolderV14 vh = invokeMatchService(refundIn.getId(), skuSb, user);

                if (ResultCode.FAIL == vh.getCode()) {
                    Set<Long> orderIds = rollbackData.keySet();
                    Set<Long> successIds = (Set<Long>) vh.getData();
                    if (CollectionUtils.isEmpty(successIds)) {
                        // 全部反向更新
                        this.rollbackData(rollbackData, orderIds);
                    } else {
                        // 部分反向更新
                        Set<Long> failIds = orderIds.stream().filter(
                                o -> !successIds.contains(o)).collect(Collectors.toSet());
                        this.rollbackData(rollbackData, failIds);
                    }
                }
                return vh;
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单正在操作，请稍后再试!", user.getLocale()));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("手工匹配失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("手工匹配失败: " + ExceptionUtil.getMessage(e));
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 手工匹配.匹配失败回滚数据
     *
     * @param relationMap 数据
     * @param ids         退单编号
     */
    private void rollbackData(Map<Long, ReturnOrderRollbackRelation> relationMap, Set<Long> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            for (Long id : ids) {
                String rtnLockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
                RedisReentrantLock rtnRedisLock = new RedisReentrantLock(rtnLockRedisKey);
                try {
                    if (rtnRedisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        ReturnOrderRollbackRelation rollBackRelation = relationMap.get(id);
                        OcBReturnOrder returnOrder = rollBackRelation.getReturnOrder();
                        List<OcBRefundInProductItem> inProductItems = rollBackRelation.getRefundInProductItems();
                        Set<OcBReturnOrderRefund> orderRefunds = rollBackRelation.getReturnOrderRefunds();

                        this.updateMatchData(null, inProductItems, orderRefunds, returnOrder);
                    }
                } catch (Exception ex) {
                    log.error(LogUtil.format("手工匹配.匹配失败回滚数据,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
                } finally {
                    rtnRedisLock.unlock();
                }
            }
        }
    }

    /**
     * 手工匹配. 参数据校验,转换
     *
     * @param param 入库单,入库单明细
     * @param user  用户
     * @return 入库单关系
     */
    private OmsRefundInRequest initValidateParam(JSONObject param, User user) {

        AssertUtil.notNull(user, "操作用户不存在");

        AssertUtil.notEmpty(param, "手工匹配,参数缺失");

        JSONObject refundJsn = param.getJSONObject("OC_B_REFUND_IN");
        AssertUtil.notNull(refundJsn, "入库单参数缺失");

        JSONArray items = param.getJSONArray("OC_B_REFUND_IN_PRODUCT_ITEM");
        AssertUtil.notEmpty(items, "商品明细参数缺失");

        OmsRefundInRequest request = JSONObject.parseObject(param.toJSONString(), OmsRefundInRequest.class);
        AssertUtil.notNull(request, "入库单转换为空");

        AssertUtil.notNull(request.getRefundIn().getId() == null, "入库单编号不能为空");

        return request;
    }

    /**
     * 手工匹配.过滤请求的数据以及退单编号分组
     *
     * @param refundInRequest 保存的请求数据
     * @return Map<Long, List < OcBRefundInProductItem>>
     */
    public Map<Long, List<OcBRefundInProductItem>> doFilterRefundInRequest(OmsRefundInRequest refundInRequest) {
        OcBRefundIn refundIn = refundInRequest.getRefundIn();
        // 保存时前端传递过来的入库明细
        List<OcBRefundInProductItem> inProductItemReq = refundInRequest.getProductItems();
        // 退货入库单商品明细
        List<OcBRefundInProductItem> inProductItems = inProductItem.selectForItem(refundIn.getId());
        // 过滤已经匹配入库明细id
        Set<Long> refInProdIds = inProductItems.stream().filter(
                o -> Objects.nonNull(o.getOcBReturnOrderId())
        ).map(OcBRefundInProductItem::getId).collect(Collectors.toSet());

        // 过滤请求入库明细里已经匹配过的ID以及填写了退单编号的明细
        inProductItemReq = inProductItemReq.stream().filter(
                o -> !refInProdIds.contains(o.getId()) && Objects.nonNull(o.getOcBReturnOrderId())
        ).collect(Collectors.toList());

        // 退单编号 分组
        Map<Long, List<OcBRefundInProductItem>> inProdMap = inProductItemReq.stream().collect(
                Collectors.groupingBy(OcBRefundInProductItem::getOcBReturnOrderId));
        return inProdMap;
    }

    /**
     * 手工匹配.检查匹配退单处理
     *
     * @param refundInId    退货入库ID
     * @param rtnOrderIds   退单ID
     * @param rollbackMap   回滚数据
     * @param inProdItemMap 入库明细
     * @param skuSb         sku 拼接信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void checkMatchRtnOrderProcess(Long refundInId, Set<Long> rtnOrderIds,
                                          Map<Long, ReturnOrderRollbackRelation> rollbackMap,
                                          Map<Long, List<OcBRefundInProductItem>> inProdItemMap,
                                          StringBuilder skuSb) {
        for (Long rtnOrderId : rtnOrderIds) {
            List<OcBRefundInProductItem> inProductItems = inProdItemMap.get(rtnOrderId);
            ReturnOrderRollbackRelation rtnOrderCallBack = new ReturnOrderRollbackRelation();
            // 入库明细、退货明细数量计算
            Set<OcBReturnOrderRefund> orderRefunds = new HashSet<>();
            List<OcBRefundInProductItem> inProdItems = new ArrayList<>();

            Set<OcBReturnOrderRefund> orderRefundsBak = new HashSet<>();
            List<OcBRefundInProductItem> inProdItemsBack = new ArrayList<>();

            String rtnLockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(rtnOrderId);
            RedisReentrantLock rtnRedisLock = new RedisReentrantLock(rtnLockRedisKey);
            try {
                if (rtnRedisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    // 更新退单 退货入库单编号
                    OcBReturnOrder rtnOrder = returnOrderMapper.selectById(rtnOrderId);
                    if (Objects.nonNull(rtnOrder.getOcBRefundInId()) && !refundInId.equals(rtnOrder.getOcBRefundInId())) {
                        throw new NDSException("退货入库单编号不一致！");
                    }
                    if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(rtnOrder.getReturnStatus()) ||
                            (OcBOrderConst.INVENTED_STATUS_UN.equals(rtnOrder.getInventedStatus()) &&
                                    ReturnStatusEnum.CANCLE.getVal().equals(rtnOrder.getReturnStatus()))) {

                        rtnOrderCallBack.setReturnOrder(rtnOrder);
                        // 退货商品明细
                        List<OcBReturnOrderRefund> rtnItems = refundInMapper.selectByOcBreturnOrderId(rtnOrder.getId());
                        for (OcBReturnOrderRefund rtnItem : rtnItems) {
                            int canMatchQty =
                                    initBigDecimal2Int(rtnItem.getQtyRefund()) - initLong(rtnItem.getQtyMatch());
                            int qtyIn = 0;
                            for (OcBRefundInProductItem productItem : inProductItems) {
                                AssertUtil.notNull(productItem.getPsCSkuEcode(), "请求入库明细的SKU不存在！");

                                if (productItem.getPsCSkuEcode().equals(rtnItem.getPsCSkuEcode())) {
                                    OcBRefundInProductItem inProductItem = new OcBRefundInProductItem();
                                    inProductItem.setId(productItem.getId());
                                    inProductItem.setOcBReturnOrderId(null);
                                    inProductItem.setOcBRefundInId(productItem.getOcBRefundInId());
                                    inProductItem.setIsWithoutOrig(IsWithoutOrigEnum.IS_WITHOUT_ORIG.getVal());
                                    inProdItemsBack.add(inProductItem);

                                    qtyIn = qtyIn + initBigDecimal2Int(productItem.getQty());
                                    productItem.setOcBReturnOrderId(rtnOrder.getId());
                                    productItem.setIsWithoutOrig(IsWithoutOrigEnum.NOT_WITHOUT_ORIG.getVal());
                                    // 条码信息拼接
                                    skuSb.append("条码: ").append(productItem.getPsCSkuEcode()).append(", ")
                                            .append("退单编号: ").append(productItem.getOcBReturnOrderId()).append(", ");
                                    inProdItems.add(productItem);
                                }
                            }
                            if (qtyIn < 1) {
                                continue;
                            }
                            if (qtyIn > canMatchQty) {
                                throw new NDSException("同SKU入库数量大于可匹配数量！");
                            }

                            OcBReturnOrderRefund orderRefund = new OcBReturnOrderRefund();
                            orderRefund.setId(rtnItem.getId());
                            orderRefund.setQtyMatch(rtnItem.getQtyMatch());
                            orderRefund.setOcBReturnOrderId(rtnItem.getOcBReturnOrderId());
                            orderRefundsBak.add(orderRefund);

                            rtnItem.setQtyMatch(initLong(rtnItem.getQtyMatch()) + (long) qtyIn);
                            orderRefunds.add(rtnItem);
                        }
                        if (!CollectionUtils.isEmpty(inProdItems) && !CollectionUtils.isEmpty(orderRefunds)) {
                            // 更新的数据
                            this.updateMatchData(refundInId, inProdItems, orderRefunds, rtnOrder);
                            // 回滚数据保存
                            rtnOrderCallBack.setRefundInProductItems(inProdItemsBack);
                            rtnOrderCallBack.setReturnOrderRefunds(orderRefundsBak);
                            rollbackMap.put(rtnOrderId, rtnOrderCallBack);
                        }
                    } else {
                        throw new NDSException("退单状态非待退货入库或取消状态，不满足匹配条件！");
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("手工匹配.检查匹配退单处理,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
                throw new NDSException(ExceptionUtil.getMessage(ex));
            } finally {
                rtnRedisLock.unlock();
            }
        }
    }

    /**
     * 手工匹配.更新匹配数据
     *
     * @param refundInId   退货入库ID
     * @param inProdItems  入库明细
     * @param orderRefunds 退货明细
     * @param rtnOrder     退单
     */
    private void updateMatchData(Long refundInId, List<OcBRefundInProductItem> inProdItems,
                                 Set<OcBReturnOrderRefund> orderRefunds, OcBReturnOrder rtnOrder) {
        try {
            // 更新入库明细
            for (OcBRefundInProductItem item : inProdItems) {
                inProductItem.updateRefundInItem4Match(item.getOcBReturnOrderId(), item.getIsWithoutOrig(),
                        item.getId(), item.getOcBRefundInId());
            }
            // 更新退单明细
            for (OcBReturnOrderRefund item : orderRefunds) {
                refundMapper.updateMatchNum(item.getQtyMatch(), rtnOrder.getId(), item.getId());
            }
            // 更新退单
            if (Objects.isNull(rtnOrder.getOcBRefundInId())) {
                returnOrderMapper.updateReturnOrderRefundInId(refundInId, rtnOrder.getId());
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("手工匹配,更新明细数据异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
    }

    /**
     * BigDecimal类型转Int类型
     *
     * @param val 输入值
     * @return int
     */
    private int initBigDecimal2Int(BigDecimal val) {
        return val == null ? 0 : val.intValue();
    }

    /**
     * Long类型转Int类型
     *
     * @param val 输入值
     * @return int
     */
    private int initLong(Long val) {
        return val == null ? 0 : val.intValue();
    }

    /**
     * 手工匹配.更新退货入库单匹配信息
     *
     * @param refundIn 退货入库单,入参
     * @param usr      匹配,操作用户
     */
    private void updateRefundInOrder(OcBRefundIn refundIn, User usr) {

        OcBRefundIn in = new OcBRefundIn();
        in.setId(refundIn.getId());
        in.setMatcher(usr.getEname());
        in.setMatchedTime(new Date());
        if (StringUtils.isNotBlank(refundIn.getRemarkHandle())) {
            in.setRemarkHandle(refundIn.getRemarkHandle());
        }
        if (StringUtils.isNotBlank(refundIn.getRemark())) {
            in.setRemark(refundIn.getRemark());
        }
        if (StringUtils.isNotBlank(refundIn.getHandler())) {
            in.setHandler(refundIn.getHandler());
        }

        int result = refundInMapper.updateById(in);

        AssertUtil.assertException(result < 1, "更新退货入库单失败");

    }

    /**
     * 手工匹配.更新入库商品明细
     *
     * @param refundInRequest 入库单关系类
     * @param skuSb           匹配记录条码:退单编号
     * @return 匹配退单
     */
    private Set<Long> updateProductItems(OmsRefundInRequest refundInRequest, StringBuilder skuSb) {

        Set<Long> returnOrderIdSet = new HashSet<>();
        List<OcBRefundInProductItem> productItems = refundInRequest.getProductItems();
        for (OcBRefundInProductItem productItem : productItems) {

            if (productItem.getOcBReturnOrderId() == null) {
                continue;
            }
            OcBRefundInProductItem item = new OcBRefundInProductItem();
            item.setOcBReturnOrderId(productItem.getOcBReturnOrderId());
            skuSb.append("条码: ").append(productItem.getPsCSkuEcode()).append(", ").append("退单编号: ")
                    .append(productItem.getOcBReturnOrderId()).append(", ");

            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("oc_b_refund_in_id", refundInRequest.getRefundIn().getId());
            wrapper.eq("id", productItem.getId());
            int eachResult = inProductItem.update(item, wrapper);

            AssertUtil.assertException(eachResult < 1, "更新退货入库单商品明细失败. 商品明细编号: " + productItem.getId());
            returnOrderIdSet.add(productItem.getOcBReturnOrderId());
        }
        return returnOrderIdSet;

    }

    /**
     * wms撤回
     *
     * @param sets 退单编号
     * @param usr  操作用户
     */
    private void wmsReCallBackReturnOrder(Set<Long> sets, User usr) {

        if (CollectionUtils.isEmpty(sets)) {
            return;
        }
        for (Long set : sets) {
            OcBReturnOrder returnOrder = returnOrderMapper.selectByid(set);
            if (returnOrder == null) {
                continue;
            }
    //        ocRefundInMatchService.wmsRecallMatchedReturnOrder(returnOrder, usr);
        }
    }

    /**
     * 调用自动匹配
     *
     * @param id   入库单编号
     * @param user 操作用户
     */
    private ValueHolderV14 invokeMatchService(Long id, StringBuilder skuSb, User user) {

        try {
            JSONObject object = new JSONObject();
            object.put("ID", id);
        //    ValueHolderV14 v14 = ocRefundInMatchService.warehousingMatching(object, user);
            ValueHolderV14 v14 = null;
            AssertUtil.assertException(v14 == null, "手工匹配, 入库失败");
            if (v14.isOK()) {
                refundInLogService.addLog(id, "手工匹配", "手工匹配成功: " + skuSb.toString(), user);
                v14.setMessage(Resources.getMessage("匹配成功", user.getLocale()));
                return v14;
            }
            v14.setMessage(Resources.getMessage("匹配失败: " + v14.getMessage(), user.getLocale()));
            refundInLogService.addLog(id, "手工匹配", "手工匹配失败: " + skuSb.toString(), user);
            return v14;
        } catch (Exception e) {
            log.error(LogUtil.format("调用退货入库单匹配服务失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            refundInLogService.addLog(id, "手工匹配", "手工匹配失败" + skuSb.toString(), user);
            throw new NDSException("调用退货入库单匹配服务失败: " + ExceptionUtil.getMessage(e));
        }
    }

    /**
     * @param param
     * @param user
     * @return
     */
    public ValueHolderV14 saveButton(JSONObject param, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        //根据主表的id查询主表及其明细表
        Long id = Long.valueOf(param.get("id").toString());
        QueryWrapper<OcBReturnOrderRefund> wrapper = new QueryWrapper<>();
        OcBReturnOrder ocBReturnOrder = returnOrderMapper.selectById(id);
        //在根据主表数据查询出的对应的退货明细的数据
        wrapper.eq("oc_b_return_order_id", id);
        List<OcBReturnOrderRefund> ocBReturnOrderRefunds = refundMapper.selectList(wrapper);
        QueryOcbReturnAndRefundItemResult refundItemResult = new QueryOcbReturnAndRefundItemResult();
        refundItemResult.setOcBReturnOrder(ocBReturnOrder);
        //查询退换货订单对应的退货入库明细
        refundItemResult.setProductItems(ocBReturnOrderRefunds);
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(Resources.getMessage("查询成功!", user.getLocale()));
        vh.setData(refundItemResult);
        return vh;
    }

    /**
     * 强制匹配服务
     *
     * @param param
     * @param user
     * @return
     */
    public ValueHolderV14 forcedMatching(JSONObject param, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        JSONArray ocBRefundInProductItem = param.getJSONArray("OC_B_REFUND_IN_PRODUCT_ITEM");
        //退货入库单 明细
        List<OcBRefundInProductItem> items1 = JSONObject.parseArray(ocBRefundInProductItem.toJSONString(),
                OcBRefundInProductItem.class);
        //入库单主表
        JSONObject ocBRefundIn = param.getJSONObject("OC_B_REFUND_IN");
        Long id = ocBRefundIn.getLong("ID");
        OcBRefundIn ocBRefundIn1 = refundInMapper.selectById(id);
        //筛选看看有没有选择退单编号进行强制匹配 ()
        List<OcBRefundInProductItem> filterItem =
                items1.stream().filter(p -> p.getIsMatch() == 0 && p.getPsCSkuEcodeActual() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterItem)) {
            throw new NDSException("实际发出条码不能为空!!!");
        }
        // 判断入库单明细中是否无原单条码为是且存在实际发出条码的明细是否存在退单编号是否匹配为 否 的 过滤后的集合
        List<OcBRefundInProductItem> satisfyList =
                items1.stream().filter(p -> p.getIsWithoutOrig() == 1 && p.getOcBReturnOrderId() != null && p.getIsMatch() == 0).collect(Collectors.toList());
        List<OcBRefundInProductItem> packegerItem = new ArrayList<>();
        try {
            for (int i = 0; i < satisfyList.size(); i++) {
                OcBRefundInProductItem productItem = satisfyList.get(i);
                OcBRefundInProductItem productItem1 = new OcBRefundInProductItem();
                String reserveVarchar04 = productItem.getPsCSkuEcodeActual();
                ProductSku productSku = psRpcService.selectProductSku(reserveVarchar04);
                if (productSku != null) {
                    BeanUtils.copyProperties(productItem, productItem1);
                    productItem1.setPsCSkuId(productSku.getId());
                    productItem1.setPsCSkuEcode(reserveVarchar04);
                    packegerItem.add(productItem1);
                } else {
                    throw new NDSException("未查询到实际发出条码信息");
                }
            }
            //筛选的id集合
            List<Long> serachIds = satisfyList.stream().map(p -> p.getId()).collect(Collectors.toList());
            for (int i = 0; i < items1.size(); i++) {
                OcBRefundInProductItem productItem = items1.get(i);
                if (serachIds.contains(productItem.getId())) {
                    continue;
                } else {
                    packegerItem.add(productItem);
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("根据条码查询异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("根据条码查询异常");
        }
        // 调用则调用【退货入库单入库服务】，（将实际发出条码放在发出条码列传参 ）
        //调用失败，则回滚数据，返回失败原因,记录在操作日志中
          try {
            long beginTime = System.currentTimeMillis();
            RefundInRelation refundInRelation = new RefundInRelation();
            refundInRelation.setRefundIn(ocBRefundIn1);
            refundInRelation.setItems(packegerItem);
            ValueHolderV14 holderV14 = ocBReturnStockInService.returnWarehousing(refundInRelation, user);
            long endTime = System.currentTimeMillis();
            if (!holderV14.isOK()) {
                throw new NDSException(holderV14.getMessage());
            }
        } catch (Exception e) {
            // 插入失败日志
            refundInLogService.addLog(ocBRefundIn1.getId(), "退单强制匹配失败", "调用入库服务失败，失败原因:" + e.getMessage(), user);
            throw new NDSException("调用入库服务失败,详情请看操作日志");
        }
        // 调用成功，则将传输的条码信息按退单编号分组调用【新增并审核库存调整单服务】，
        Map<Long, List<OcBRefundInProductItem>> groupByOcbReturnIdCollect =
                satisfyList.stream().collect(Collectors.groupingBy(OcBRefundInProductItem::getOcBReturnOrderId));
        try {
            //3、判断调整单的生成
            ValueHolderV14 v14 = checkAjust(ocBRefundIn1, user, groupByOcbReturnIdCollect);
            if (ResultCode.FAIL == v14.getCode()) {
                throw new NDSException("新增调整单失败" + v14.getMessage());
            }
        } catch (Exception e) {
            throw new NDSException("调用调整单失败>>" + e.getMessage());
        }
        //4.更新明细需要更新的字段 和主表
        try {
            updateProductItemsAndToEs(ocBRefundIn1, user, satisfyList);
        } catch (Exception e) {
            throw new NDSException(this.getClass().getName() + "更新明细字段并推送es失败" + e.getMessage());
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("匹配成功");
        return vh;
    }

    /**
     * 更新明细表并 推送es
     *
     * @param ocBRefundIn1
     * @param user
     * @param satisfyList  满足条件的明细
     */
    private void updateProductItemsAndToEs(OcBRefundIn ocBRefundIn1, User user,
                                           List<OcBRefundInProductItem> satisfyList) {
        // 用来记录日志的map 集合
        List<HashMap<String, Object>> logMap = new ArrayList<>();
        // 更新明细 是否匹配状态为 是  更新 是否生成调整单为是 更新 退单编号
        for (int i = 0; i < satisfyList.size(); i++) {
            OcBRefundInProductItem productItem = satisfyList.get(i);
            OcBRefundInProductItem needUpdateItem = new OcBRefundInProductItem();
            needUpdateItem.setOcBReturnOrderId(productItem.getOcBReturnOrderId());
            // needUpdateItem.setIsMatch(1);
            //        needUpdateItem.setIsGenAdjust(1);
            needUpdateItem.setModifiername(user.getName());
            needUpdateItem.setModifierename(user.getEname());
            needUpdateItem.setModifierid(user.getId() + 0L);
            needUpdateItem.setPsCSkuEcodeActual(productItem.getPsCSkuEcodeActual());
            QueryWrapper wrapper = new QueryWrapper();
            HashMap<String, Object> map = new HashMap<>();
            map.put("returnId", productItem.getOcBReturnOrderId());
            map.put("sku", productItem.getPsCSkuEcodeActual());
            logMap.add(map);
            wrapper.eq("oc_b_refund_in_id", productItem.getOcBRefundInId());
            wrapper.eq("id", productItem.getId());
            int update = inProductItem.update(needUpdateItem, wrapper);
        }
        // 更新主表的匹配状态 如果明细不完全匹配改成部分匹配 如果明细全部匹配改为 全部匹配
       /* QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("oc_b_refund_in_id", ocBRefundIn1.getId());
        // 查询这个退单总共有多少明细
        Integer integer = inProductItem.selectCount(wrapper);
        wrapper.eq("is_match", 1);
        // 查询匹配 的明细的条数
        Integer integer1 = inProductItem.selectCount(wrapper);
        OcBRefundIn in = new OcBRefundIn();
        in.setModifieddate(new Date());
        in.setModifierename(user.getEname());
        in.setModifiername(user.getName());
        in.setModifierid(user.getId() + 0L);
        if (integer1 > 0 && integer1.equals(integer)) {
            in.setMatchStatus(MatchingSate.MATCH_ALL.toInteger());
        } else if (integer1 > 0 && !integer1.equals(integer)) {
            in.setMatchStatus(MatchingSate.PARTIAL_MATCHING.toInteger());
        }
        QueryWrapper wrapper1 = new QueryWrapper();
        wrapper1.eq("id", ocBRefundIn1.getId());
        refundInMapper.update(in, wrapper1);*/

        //推送退单明细表的es
        // 查询后更新的 字表 推送es
        QueryWrapper wrapper2 = new QueryWrapper();
        wrapper2.eq("oc_b_refund_in_id", ocBRefundIn1.getId());
        List<OcBRefundInProductItem> list = inProductItem.selectList(wrapper2);
//        try {
//            SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME,
//            OcElasticSearchIndexResources.OC_B_RETURN_IN_PRODUCT_ITEM_TYPE_NAME, list, "OC_B_RETURN_ORDER_ID");
//        } catch (IOException e) {
//            log.debug(this.getClass().getName() + "退货入库单明细推送es失败" + e.getMessage() + ",入库单的id是:" + ocBRefundIn1
//            .getId());
//            throw new NDSException(this.getClass().getName() + "退货入库单明细推送es失败" + e.getMessage());
//        }
        //推送主表的es
        //查询后更新的主表
        OcBRefundIn lastOcBRefundIn = refundInMapper.selectById(ocBRefundIn1.getId());
       /* try {
            SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME,
            OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, lastOcBRefundIn, lastOcBRefundIn.getId());
        } catch (IOException e) {
            log.debug(this.getClass().getName() + "退货入库单明细推送es失败" + e.getMessage() + ",入库单的id是:" + ocBRefundIn1.getId
            ());
            throw new NDSException(this.getClass().getName() + "退货入库单明细推送es失败" + e.getMessage());
        }*/
        StringBuilder logMessage = new StringBuilder("错发强制匹配成功,");
        //插入日志
        for (int i = 0; i < logMap.size(); i++) {
            HashMap<String, Object> map = logMap.get(i);
            Object returnId = map.get("returnId");
            Object sku = map.get("sku");
            logMessage.append("匹配退单编号" + returnId + "，匹配条码" + sku + ",");
        }
        refundInLogService.addLog(ocBRefundIn1.getId(), "错发强制匹配", logMessage.toString(), user);
    }

    /**
     * 调用调整单服务
     *
     * @param ocBRefundIn1              入库单主表
     * @param user                      当前用户
     * @param groupByOcbReturnIdCollect 按照退货单编号分组的集合
     * @return
     */
    private ValueHolderV14 checkAjust(OcBRefundIn ocBRefundIn1, User user,
                                      Map<Long, List<OcBRefundInProductItem>> groupByOcbReturnIdCollect) {
        ValueHolderV14 vh = new ValueHolderV14();
        //OcBRefundIn ocBRefundIn = refundInMapper.selectById(ocBRefundIn1.getId());
        //遍历分组
        for (Map.Entry<Long, List<OcBRefundInProductItem>> entryUser : groupByOcbReturnIdCollect.entrySet()) {
            Long key = entryUser.getKey();
            List<OcBRefundInProductItem> matchItems = entryUser.getValue();
            // 调用调整单 入库单明细的发出条码为正向向数量 的调整单(如果存在实收条码就按照实收条码来生成正向数量)
            List<OcBRefundInProductItem> rightItem = new ArrayList<>();
            for (int i = 0; i < matchItems.size(); i++) {
                OcBRefundInProductItem item = matchItems.get(i);
                if (item.getRealSkuEcode() != null) {
                    item.setPsCSkuId(item.getRealSkuId());
                    item.setPsCSkuEcode(item.getRealSkuEcode());
                    item.setQty(item.getQty());
                    rightItem.add(item);
                } else {
                    item.setQty(item.getQty());
                    rightItem.add(item);
                }
            }
            try {
                // 调用调整单 ，?实际发出条码为负向数量的调整单
                // List<OcBRefundInProductItem> rightItems = new ArrayList<>();
                for (int i = 0; i < matchItems.size(); i++) {
                    OcBRefundInProductItem item = matchItems.get(i);
                    // 根据入库单信息查询退换货单进行组装数据
                    QueryWrapper wrapper1 = new QueryWrapper();
                    wrapper1.eq("ps_c_sku_ecode", item.getPsCSkuEcodeActual());
                    wrapper1.eq("oc_b_return_order_id", item.getOcBReturnOrderId());
                    List<OcBReturnOrderRefund> refundList = refundMapper.selectList(wrapper1);
                    if (CollectionUtils.isEmpty(refundList)) {
                        throw new NDSException("根据退换货id和条码查不到退换货信息，请检查参数");
                    }
                    OcBReturnOrderRefund refund = refundList.get(0);
                    OcBRefundInProductItem productItem = new OcBRefundInProductItem();
                    productItem.setId(item.getId());
                    productItem.setOcBRefundInId(item.getOcBRefundInId());
                    productItem.setGbcode(item.getGbcode());
                    productItem.setPsCSkuId(refund.getPsCSkuId());
                    productItem.setPsCSkuEcode(item.getPsCSkuEcodeActual());
                    productItem.setPsCProId(refund.getPsCProId());
                    productItem.setPsCProEcode(refund.getPsCProEcode());
                    productItem.setPsCProEname(refund.getPsCProEname());
                    productItem.setScBInId(item.getScBInId());
                    productItem.setProductMark(item.getProductMark());
                    productItem.setIsWithoutOrig(item.getIsWithoutOrig());
                    productItem.setQty(new BigDecimal(-item.getQty().intValue()));
                    productItem.setOcBReturnOrderId(item.getOcBReturnOrderId());
                    productItem.setIsMatch(item.getIsMatch());
                    productItem.setIsGenAdjust(item.getIsGenAdjust());
                    productItem.setOwnerename(user.getEname());
                    productItem.setOwnername(user.getName());
                    productItem.setIsactive("Y");
                    productItem.setPsCClrId(item.getPsCClrId());
                    productItem.setPsCClrEcode(item.getPsCClrEcode());
                    productItem.setPsCClrEname(item.getPsCClrEname());
                    productItem.setPsCSizeId(item.getPsCSizeId());
                    productItem.setPsCSizeEcode(item.getPsCSizeEcode());
                    productItem.setPsCSizeEname(item.getPsCSizeEname());
                    productItem.setCreationdate(new Date());
                    productItem.setModifieddate(new Date());
                    rightItem.add(productItem);
                }
                ValueHolderV14 holderV14 = adjustmentService.addAdjustmen(ocBRefundIn1, rightItem, user,
                        SgConstantsIF.SERVICE_NODE_ADJUST_PROP_MISTAKE_ADJUSTMENT);
                if (ResultCode.FAIL == holderV14.getCode()) {
                    refundInLogService.addLog(ocBRefundIn1.getId(), "生成调整单",
                            "生成冲错发调整单失败,失败原因：" + holderV14.getMessage(), user);
                } else {
                    //更新入库单明细条码是否生成错发调整单字段值为是
                    List<Long> ids = new ArrayList<>(); // 明细的id 集合
                    for (int i = 0; i < matchItems.size(); i++) {
                        OcBRefundInProductItem productItem = matchItems.get(i);
                        ids.add(productItem.getId());
                    }
                    QueryWrapper wrapper = new QueryWrapper();
                    wrapper.in("id", ids);
                    wrapper.eq("oc_b_refund_in_id", ocBRefundIn1.getId());
                    OcBRefundInProductItem productItem = new OcBRefundInProductItem();
                    productItem.setModifieddate(new Date());
                    productItem.setModifierid(user.getId() + 0L);
                    productItem.setModifierename(user.getEname());
                    //更新是否生成错发调整单字段值为是
                    productItem.setIsGenWroAdjust(IsGenAdjustEnum.YES.integer());
                    productItem.setIsWithoutOrig(0); // 更新是否无原单条码字段值为否
                    productItem.setModifiername(user.getName());
                    int update = inProductItem.update(productItem, wrapper);
                }
                List<OcBRefundInProductItem> falseItem = new ArrayList<>();
                for (int i = 0; i < matchItems.size(); i++) {
                    OcBRefundInProductItem item = matchItems.get(i);
                    Integer isGenAdjust = item.getIsGenAdjust();
                    boolean isGenAdjusted = IsGenAdjustEnum.YES.integer().equals(isGenAdjust);
                    if (!isGenAdjusted) {
                        continue;
                    }
                    if (item.getRealSkuEcode() != null) {
                        item.setPsCSkuId(item.getRealSkuId());
                        item.setPsCSkuEcode(item.getRealSkuEcode());
                        item.setQty(new BigDecimal(-item.getQty().intValue()));
                        falseItem.add(item);
                    } else {
                        item.setQty(new BigDecimal(-item.getQty().intValue()));
                        falseItem.add(item);
                    }
                }
                if (falseItem.size() > 0) {
                    ValueHolderV14 v14 = adjustmentService.addAdjustmen(ocBRefundIn1, falseItem, user,
                            SgConstantsIF.SERVICE_NODE_ADJUST_PROP_FLUSH_NO_SOURCE);
                    if (ResultCode.FAIL == v14.getCode()) {
                        refundInLogService.addLog(ocBRefundIn1.getId(), "生成调整单",
                                "生成冲无头件调整单失败,失败原因：" + holderV14.getMessage(), user);
                    } else {
                        //调用调整单成功则更新入库单明细条码是否生成冲无头件调整单字段值为是 ，更新入库单明细条码是否无原单条码字段值为否
                        List<Long> ids = new ArrayList<>(); // 明细的id 集合
                        for (int i = 0; i < matchItems.size(); i++) {
                            OcBRefundInProductItem productItem = matchItems.get(i);
                            ids.add(productItem.getId());
                        }
                        QueryWrapper wrapper = new QueryWrapper();
                        wrapper.in("id", ids);
                        wrapper.eq("oc_b_refund_in_id", ocBRefundIn1.getId());
                        OcBRefundInProductItem productItem = new OcBRefundInProductItem();
                        productItem.setModifieddate(new Date());
                        productItem.setModifierid(user.getId() + 0L);
                        productItem.setModifierename(user.getEname());
                        // 更新是否生成冲调整单字段值为是
                        productItem.setIsGenMinusAdjust(IsGenMinusAdjustEnum.YES.integer());
                        productItem.setModifiername(user.getName());
                        int update = inProductItem.update(productItem, wrapper);
                        StringBuilder meassage = new StringBuilder("生成冲无头件调整单成功,");
                        //记录冲无头件日志
                        for (int i = 0; i < falseItem.size(); i++) {
                            OcBRefundInProductItem productItem1 = falseItem.get(i);
                            meassage.append("调整条码：" + productItem1.getPsCSkuEcode()).append("调整数量：").append(productItem1.getQty());
                        }
                        refundInLogService.addLog(ocBRefundIn1.getId(), "生成调整单", meassage.toString(), user);
                    }
                }
            } catch (Exception e) {
                throw new NDSException(this.getClass().getName() + "调用调整单失败》》" + e.getMessage());
            }
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("调用调整单成功");
        return vh;
    }

    /**
     * 判断明细表的匹配状态
     *
     * @param ocBRefundIn1
     * @param user
     */
    private void checkMatchStatu(OcBRefundIn ocBRefundIn1, User user) throws Exception {
        // a.如果已经全部匹配，则更新退货入库单匹配状态为全部匹配，如果存在明细匹配状态为未匹配，则更新退货入库单匹配状态为部分匹配
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("oc_b_refund_in_id", ocBRefundIn1.getId());
        wrapper.eq("is_match", 1);
        List list = inProductItem.selectList(wrapper);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("oc_b_refund_in_id", ocBRefundIn1.getId());
        Integer integer = inProductItem.selectCount(queryWrapper);
        if (integer.equals(list.size())) {
            ocBRefundIn1.setMatchStatus(MatchingSate.MATCH_ALL.toInteger());
            ocBRefundIn1.setModifierename(user.getEname());
            ocBRefundIn1.setModifiername(user.getName());
            ocBRefundIn1.setModifieddate(new Date());
            OcBRefundIn in = new OcBRefundIn();
            in.setId(ocBRefundIn1.getId());
            in.setMatchStatus(MatchingSate.MATCH_ALL.toInteger());
            in.setModifierename(user.getEname());
            in.setModifiername(user.getName());
            in.setModifieddate(new Date());
            refundInMapper.updateById(in);
            //SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME,
            // OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, ocBRefundIn1, ocBRefundIn1.getId());
        } else if (list.size() > 0) {
            ocBRefundIn1.setMatchStatus(MatchingSate.PARTIAL_MATCHING.toInteger());
            ocBRefundIn1.setModifierename(user.getEname());
            ocBRefundIn1.setModifiername(user.getName());
            ocBRefundIn1.setModifieddate(new Date());
            OcBRefundIn in = new OcBRefundIn();
            in.setId(ocBRefundIn1.getId());
            in.setMatchStatus(MatchingSate.PARTIAL_MATCHING.toInteger());
            in.setModifierename(user.getEname());
            in.setModifiername(user.getName());
            in.setModifieddate(new Date());
            refundInMapper.updateById(in);
            //  SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME,
            //  OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, ocBRefundIn1, ocBRefundIn1.getId());
        }
    }

    //更新退换货主表
    private void updateReturnAndRefund(List<List<OcBRefundInProductItem>> itemList, User user) throws Exception {
        //则更新生成退货入库单的条码的匹配状态为已匹配1，更新退换货单号的是否生成零售（is_todrp）的值为是1,
        for (int i = 0; i < itemList.size(); i++) {
            List<OcBRefundInProductItem> items = itemList.get(i);
            //更改入库单字表
            for (int i1 = 0; i1 < items.size(); i1++) {
                OcBRefundInProductItem item1 = items.get(i1);
                OcBRefundInProductItem item = new OcBRefundInProductItem();
                item.setId(item1.getId());
                item.setIsMatch(1);
                item.setPsCSkuEcodeActual(item1.getPsCSkuEcodeActual());
                item.setOcBReturnOrderId(item1.getOcBReturnOrderId());
                //暂时保存到数据库来进行后面的拆分明细（存放的是退货明细的id）
                item.setIsGenInOrder(item1.getIsGenInOrder());
                item.setModifieddate(new Date());
                item.setModifiername(user.getName());
                item.setModifierename(user.getEname());
                QueryWrapper wrapper = new QueryWrapper();
                wrapper.eq("oc_b_refund_in_id", item1.getOcBRefundInId());
                wrapper.eq("id", item1.getId());
                inProductItem.update(item, wrapper);
            }
            //更改 退换货主表
            OcBRefundInProductItem item = items.get(0);
            OcBReturnOrder order1 = returnOrderMapper.selectById(item.getOcBReturnOrderId());
            order1.setModifieddate(new Date());
            order1.setModifierename(user.getEname());
            order1.setModifiername(user.getName());
            order1.setIsTodrp(1);
            OcBReturnOrder order = new OcBReturnOrder();
            order.setId(order1.getId());
            order.setModifieddate(new Date());
            order.setModifierename(user.getEname());
            order.setModifiername(user.getName());
            order.setIsTodrp(1);
            returnOrderMapper.updateById(order);
            //推送退换货主表的es
            //  SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
            //  OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, order1, order1.getId());
        }
    }

    /**
     * 调用新增入库通知单服务
     *
     * @param itemList
     * @param ocBRefundIn1
     * @param user
     */
    private ValueHolderV14 callWarehousingNotification(List<List<OcBRefundInProductItem>> itemList,
                                                       OcBRefundIn ocBRefundIn1, User user) {
        try {
            ValueHolderV14 vh = new ValueHolderV14();
            //补充 调用 新增入库通知单的数据
            if (itemList == null || itemList.size() == 0) {
                throw new NDSException("无满足数据,请检查后重试");
            }
            for (int i = 0; i < itemList.size(); i++) {
                List<OcBRefundInProductItem> items1 = itemList.get(i);
                for (int j = 0; j < items1.size(); j++) {
                    //补充 调用 新增入库通知单的数据
                    OcBReturnOrderRelation ocBReturnOrderRelation = new OcBReturnOrderRelation();
                    OcBRefundInProductItem item = items1.get(j);
                    Long ocBReturnOrderId = item.getOcBReturnOrderId();
                    //查询 退换货主表
                    OcBReturnOrder order = returnOrderMapper.selectById(ocBReturnOrderId);
                    //查询换货明细
                    QueryWrapper wrapper1 = new QueryWrapper();
                    wrapper1.eq("oc_b_return_order_id", ocBReturnOrderId);
                    List<OcBReturnOrderExchange> list = exchangeMapper.selectList(wrapper1);
                    //查询退货明细
                    QueryWrapper wrapper2 = new QueryWrapper();
                    wrapper2.eq("oc_b_return_order_id", ocBReturnOrderId);
                    List<OcBReturnOrderRefund> refundList = refundMapper.selectList(wrapper2);
                    //筛选数量
                    for (int k = 0; k < refundList.size(); k++) {
                        //最终传入的数量
                        BigDecimal qty = BigDecimal.ZERO;
                        //退换货明细 表
                        OcBReturnOrderRefund refund = refundList.get(k);
                        for (int z = 0; z < items1.size(); z++) {
                            // 入库单明细表
                            OcBRefundInProductItem productItem = items1.get(z);
                            if (refund.getPsCSkuEcode().equals(productItem.getPsCSkuEcodeActual())) {
                                qty = qty.add(productItem.getQty());
                            }
                        }
                        if (refund.getQtyRefund().compareTo(qty) < 0) {
                            throw new NDSException("申请数量小于入库数量，数量异常");
                        }
                        refund.setQtyIn(qty);
                    }
                    ocBReturnOrderRelation.setReturnOrderInfo(order);
                    ocBReturnOrderRelation.setOrderRefundList(refundList);
                    ocBReturnOrderRelation.setOrderExchangeList(list);
                    if (j == 0) {
                        //调用新增入库通知单服务
                        vh = addOrderNoticeAndOutService.addNoticeAndOutOrderNew(ocBReturnOrderRelation, ocBRefundIn1
                                , user);
                        break;
                    }
                }
            }
            return vh;
        } catch (Exception e) {
            log.error(LogUtil.format("调用新增入库通知单服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException( "调用新增入库通知单服务异常>>>" + e.getMessage());
        }
    }

    /**
     * 强制匹配s搜索按钮确定按钮
     *
     * @param param
     * @param user
     * @return
     */
    public ValueHolderV14 seachForced(JSONObject param, User user) {
        ValueHolderV14 v14 = new ValueHolderV14();
        //通过搜索条件查询到指定退换货订单，选中一条退单，点击确认，如果此退单中只有一条明细，则判断此明细和入库单明细是否一致，
        // 如果一致则提示条码数量一致，请走手工匹配。当条码不一致时则将此入库单sku存到实际发出条码上
        //获取明细表数据
        Long item = 0L;
        try {
            item = param.getLong("refundId");
        } catch (Exception e) {
            JSONArray jsnAry = param.getJSONArray("refundId");
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(jsnAry)) {
                item = jsnAry.getLong(0);
            }
        }
        //主表的id
        Long refundId = param.getLong("refundInId");
        //退换货主表
        Long returnOrderId = param.getLong("returnOrderId");
        //退换货明细表
        Long returnItem = param.getLong("returnItem");
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("id", item);
        wrapper.eq("oc_b_refund_in_id", refundId);
        //退货入库 明细对象的集合
        OcBRefundInProductItem productItem = inProductItem.selectOne(wrapper);
        // 退货入库主表对象
        OcBRefundIn ocBRefundIn = refundInMapper.selectById(refundId);
        // 退换货明细
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", returnItem);
        queryWrapper.eq("oc_b_return_order_id", returnOrderId);
        OcBReturnOrderRefund refund = refundMapper.selectOne(queryWrapper);
        //如果存在实收条码则选择的退单和实收条码匹配
        if (productItem.getRealSkuEcode() != null) {
            if (refund.getPsCSkuEcode().equals(productItem.getRealSkuEcode())) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("条码一致，请走手工匹配");
            } else {
                v14.setCode(ResultCode.SUCCESS);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("returnId", returnOrderId);
                jsonObject.put("PS_C_SKU_ECODE_ACTUAL", refund.getPsCSkuEcode());
                jsonObject.put("returnItemId", refund.getId());
                v14.setData(jsonObject);
                return v14;
            }
        } else {
            //不存在实际条码 就跟原来的发出条码进行比较
            if (refund.getPsCSkuEcode().equals(productItem.getPsCSkuEcode())) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("条码一致，请走手工匹配");
            } else {
                v14.setCode(ResultCode.SUCCESS);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("returnId", returnOrderId);
                jsonObject.put("PS_C_SKU_ECODE_ACTUAL", refund.getPsCSkuEcode());
                jsonObject.put("returnItemId", refund.getId());
                v14.setData(jsonObject);
                return v14;
            }
        }
        return v14;
    }

    /**
     * 检查条码是否匹配
     *
     * @param items         退货入库明细id 集合
     * @param refundIds     退货入库主表 id
     * @param returnOrderId 退换货主表id
     * @param returnItem    退换货明细表id
     * @return 返回满足条件的条码
     */
    private ValueHolderV14 checkSkuMatch(JSONArray items, Object refundIds, Long returnOrderId, Long returnItem) {
        return null;
    }

    public ValueHolderV14 cutInLine(JSONObject param, User user) {
        JSONArray jsonArray = param.getJSONArray("ids");
        if (jsonArray == null || jsonArray.isEmpty()) {
            return new ValueHolderV14(ResultCode.FAIL, "请选择要手动匹配的单据");
        }
        // 转成List<Long>
        List<Long> list = JSONArray.parseArray(jsonArray.toJSONString(), Long.class);
        for (Long id : list) {
            OcBRefundIn refundIn = refundInMapper.getOcBrefundInById(id, "Y");
            if (refundIn == null) {
                return new ValueHolderV14(ResultCode.FAIL, "未查询到该单据");
            }
            OcBRefundIn newRefundIn = new OcBRefundIn();
            newRefundIn.setQtyMatch(0L);
            newRefundIn.setId(id);
            newRefundIn.setModifieddate(new Date());
            refundInMapper.updateById(newRefundIn);
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "手动匹配成功");
    }
}
