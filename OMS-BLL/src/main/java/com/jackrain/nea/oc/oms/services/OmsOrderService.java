package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.gsi.GSI4OrderItem;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPaymentMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPromotionMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderTaobaoMapper;
import com.jackrain.nea.oc.oms.model.enums.BackflowStatus;
import com.jackrain.nea.oc.oms.model.enums.IsForbiddenDeliveryEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderWmsCancelNumber;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.order.address.ReceiverAddressDto;
import com.jackrain.nea.oc.oms.model.order.address.WarehouseAndLogisticsDto;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrderItemExt;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromotion;
import com.jackrain.nea.oc.oms.model.table.OcBOrderTaobao;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.tag.consts.SupplyTypeEnum;
import com.jackrain.nea.oc.oms.util.AlibabaAscpOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.JingdongOrderTransferUtils;
import com.jackrain.nea.oc.oms.util.JitxOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.oc.oms.util.StandplatOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.TaobaoOrderTransferUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.BllSystemParameterKeyResources;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 全渠道订单处理服务
 *
 * @author: 易邵峰
 * @since: 2019-01-22
 * create at : 2019-01-22 22:52
 */
@Component
@Slf4j
public class OmsOrderService {

    private static final int DEFAULT_SEARCH_ES_PAGE_SIZE = 100;
    private static final int OMS_ORDER_REDIS_TIMEOUT = 24 * 60 * 60 * 1000;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OcBOrderPromotionMapper orderPromotionMapper;
    @Autowired
    private OcBOrderPaymentMapper orderPaymentMapper;
    @Autowired
    private OcBOrderTaobaoMapper orderTaobaoMapper;
    @Autowired
    private TaobaoOrderTransferUtil transferUtil;
    @Autowired
    private JitxOrderTransferUtil jitxTransferUtil;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private OcBOrderLinkService orderLinkService;
    @Autowired
    private JingdongOrderTransferUtils jingdongOrderTransferUtils;
    @Autowired
    private OmsOrderCheckAndUpdateService omsOrderCheckAndUpdateService;
    @Autowired
    private StandplatOrderTransferUtil standplatOrderTransferUtil;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;
    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OmsOrderDistributeWarehouseService omsWarehouseRuleService;
    @Autowired
    private AlibabaAscpOrderTransferUtil alibabaAscpOrderTransferUtil;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Autowired
    private IpJitxOrderService ipJitxOrderService;

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsAuditTaskService omsAuditTaskService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OmsReturnUtil omsReturnUtil;
    @Autowired
    private OmsOrderCancellationService omsOrderCancellationService;


    /**
     * 换货单设置orderId
     *
     * @param disputeId
     * @param orderId
     */
    public static void setOmsOrderToRedisForExchange(Long disputeId, Long orderId) {
        if (Objects.nonNull(disputeId) && Objects.nonNull(orderId)) {
            String redisKey = BllRedisKeyResources.getOmsOrderKey(disputeId.toString());
            RedisMasterUtils.getObjRedisTemplate().opsForValue().set(redisKey, orderId, OMS_ORDER_REDIS_TIMEOUT,
                    TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 换货单取orderId
     *
     * @param disputeId
     * @return
     */
    public static Long getOmsOrderToRedisForExchange(Long disputeId) {
        if (Objects.nonNull(disputeId)) {
            String redisKey = BllRedisKeyResources.getOmsOrderKey(disputeId.toString());
            Boolean hasKey = RedisMasterUtils.getObjRedisTemplate().hasKey(redisKey);

            if (Objects.nonNull(hasKey) && hasKey) {
                Object orderId = RedisMasterUtils.getObjRedisTemplate().opsForValue().get(redisKey);

                if (Objects.nonNull(orderId)) {
                    return Long.valueOf(orderId.toString());
                }
            }
        }


        return 0L;
    }

    /**
     * 返回默认的系统Root用户
     *
     * @return
     */
    public static User getRootUser() {
        User user = new UserImpl();
        ((UserImpl) user).setId(SystemUserResource.ROOT_USER_ID.intValue());
        ((UserImpl) user).setEname(SystemUserResource.ROOT_ENAME);
        ((UserImpl) user).setTruename(SystemUserResource.ROOT_USER_NAME);
        ((UserImpl) user).setClientId(SystemUserResource.AD_CLIENT_ID.intValue());
        ((UserImpl) user).setOrgId(SystemUserResource.AD_ORG_ID.intValue());
        ((UserImpl) user).setName(SystemUserResource.ROOT_USER_NAME);
        return user;
    }

    /**
     * 查询订单是否存在Redis中
     *
     * @param sourceCode 原始订单号
     * @return
     */
    private long selectOmsOrderFromRedis(String sourceCode) {
        String redisKey = BllRedisKeyResources.getOmsOrderKey(sourceCode);
        CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        Boolean hasKey = objRedisTemplate.hasKey(redisKey);
        if (hasKey != null && hasKey) {
            Long value = objRedisTemplate.opsForValue().get(redisKey);
            if (value == null) {
                return 0L;
            }
            return value;
        } else {
            return 0L;
        }
    }

    /**
     * 依据SourceCode查询订单信息
     *
     * @param sourceCode 平台单号
     * @return List<OcBOrder>
     */
    public List<OcBOrder> selectOmsOrderInfo(String sourceCode) {
        //2019-08-09 由于存在拆单更新卖家备注的的影响  先查ES 然后查redis
        //    List<Long> esIdList = ES4Order.getIdsBySourceCode(sourceCode);
        List<Long> esIdList = GSI4Order.getIdListBySourceCode(sourceCode);

        List<OcBOrder> orderList = new ArrayList<>();

//        if (CollectionUtils.isEmpty(esIdList)) {
//            long id = this.selectOmsOrderFromRedis(sourceCode);
//            if (log.isDebugEnabled()) {
//                log.debug("OrderId={},###OmsOrderService.selectOmsOrderInfo.FromRedis.ID,SourceCode={}", id,
//                        sourceCode);
//            }
//            if (id > 0) {
//                esIdList = new ArrayList<>();
//                esIdList.add(id);
//            }
//        }
        if (CollectionUtils.isNotEmpty(esIdList)) {
            for (Long tempId : esIdList) {
                OcBOrder order = orderMapper.selectById(tempId);
                if (order != null) {
                    orderList.add(order);
                } else {
                    log.error("OrderId={},OmsOrderService.SelectOmsOrder.Database.Not.Exist.ES.;SourceCode={}", tempId,
                            sourceCode);
                }
            }
        }
        return orderList;
    }

    /**
     * 通过
     *
     * @param sourceCode
     * @return
     */
    public List<OcBOrder> selectOmsOrderList(String sourceCode) {
        //2019-08-09 由于存在拆单更新卖家备注的的影响  先查ES 然后查redis
        //    List<Long> esIdList = ES4Order.getIdsBySourceCode(sourceCode);
        List<Long> esIdList = GSI4OrderItem.selectOcBOrderItemByTid(sourceCode);

        List<OcBOrder> orderList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(esIdList)) {
            for (Long tempId : esIdList) {
                OcBOrder order = orderMapper.selectById(tempId);
                if (order != null) {
                    orderList.add(order);
                } else {
                    log.error("OrderId={},OmsOrderService.SelectOmsOrder.Database.Not.Exist.ES.;SourceCode={}", tempId,
                            sourceCode);
                }
            }
        }
        return orderList;
    }


    /**
     * 依据SourceCode查询订单信息
     *
     * @param sourceCode 平台单号
     * @return List<OcBOrder>
     */
    public List<OcBOrder> selectOmsOrderInfoList(String sourceCode) {

        //    List<Long> list = ES4Order.getIdsBySourceCode(sourceCode);
        List<Long> list = GSI4OrderItem.selectOcBOrderItemByTid(sourceCode);
//        List<Long> list = GSI4Order.getIdListBySourceCode(sourceCode);
//        long id = 0;
//        // 先查询ES，再查询Redis。防止重复转单。因为ES有可能延迟100MS
//        if (CollectionUtils.isEmpty(list)) {
//            id = this.selectOmsOrderFromRedis(sourceCode);
//            list = new ArrayList<>();
//            list.add(id);
//            log.warn("Id={},###OmsOrderService.selectOmsOrderInfo.FromRedis.ID", id);
//        }
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<OcBOrder> orderList = orderMapper.selectByIdsList(list);
        return orderList;
    }

    /**
     * 查询已审核订单(传wms)
     *
     * @param pageIndex 初始页面index
     * @param pageSize  页面数据大小
     * @return List<IpOrderReturnRelation>
     */
    public List<Long> selectAuditedOrderList(int pageIndex, int pageSize) {
        JSONObject whereKeys = new JSONObject();
        //todo 目前为只查订单状态为审核的
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.CHECKED.toInteger());
        whereKeys.put("IS_INTERECEPT", 0);
        whereKeys.put("IS_FORCE", 0);
        List<Long> ids = ES4Order.queryEsOrderList(whereKeys, pageIndex, pageSize);
//        List<OcBOrderParam> list = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(ids)) {
//            List<OcBOrder> ocBOrders = orderMapper.selectByIdsListAndInterecept(ids);
//            ocBOrders.forEach(p -> {
//                // String cpCLogisticsEcode = p.getCpCLogisticsEcode();
////                //物流编码  2019-07-30 上线优化  将此逻辑去掉
////                String expresscode = p.getExpresscode();
////                if (LogisticsEcodeEnum.LOGISTICS_JD.getCode().equals(cpCLogisticsEcode)
////                        && StringUtils.isEmpty(expresscode)) {
////                    //物流名称是JD  物流编码为空的过滤掉
////                    return;
////                }
//                List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemListAndReturn(p.getId());
//                if (CollectionUtils.isEmpty(orderItems)) {
//                    return;
//                }
//                OcBOrderParam ocBOrderParam = new OcBOrderParam();
//                ocBOrderParam.setOcBOrder(p);
//                ocBOrderParam.setOrderItemList(orderItems);
//                list.add(ocBOrderParam);
//            });
//        }
        return ids;
    }

    /**
     * wms撤回
     *
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public List<OcBOrderParam> selectOrderWmsWithdrawList(int pageIndex, int pageSize) {
        JSONObject whereKeys = new JSONObject();
        //todo 目前为只查订单状态为审核的
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.IN_DISTRIBUTION.toInteger());
        whereKeys.put("IS_INTERECEPT", 1); //拦截
        JSONObject filterKeys = new JSONObject();
        filterKeys.put("WMS_CANCEL_NUMBER", "~" + OcOrderWmsCancelNumber.UNOVER.toInteger());
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger());
        jsonArray.add(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger());
        whereKeys.put("WMS_CANCEL_STATUS", jsonArray);
        List<Long> ids = ES4Order.queryEsOrderList(whereKeys, filterKeys, pageIndex, pageSize);

        // 2. 已审核 & 拦截
        JSONObject whereKey = new JSONObject();
        whereKey.put("ORDER_STATUS", OmsOrderStatus.CHECKED.toInteger());
        whereKey.put("IS_INTERECEPT", 1);
        filterKeys = new JSONObject();
        filterKeys.put("WMS_CANCEL_NUMBER", "~" + OcOrderWmsCancelNumber.UNOVER.toInteger());
        List<Long> checkList = ES4Order.queryEsOrderList(whereKey, filterKeys, pageIndex, pageSize);
        if (checkList.size() > 0) {
            ids.removeAll(checkList);
            ids.addAll(checkList);
        }

        List<OcBOrderParam> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<OcBOrder> ocBOrders = orderMapper.selectByIdsList(ids);
            ocBOrders.forEach(p -> {
                List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemList(p.getId());
                OcBOrderParam ocBOrderParam = new OcBOrderParam();
                ocBOrderParam.setOcBOrder(p);
                ocBOrderParam.setOrderItemList(orderItems);
                list.add(ocBOrderParam);
            });
        }
        return list;
    }

    /**
     * @param orderId 订单编号
     * @return OcBOrderRelation
     */
    public OcBOrderRelation selectOmsOrderInfo(long orderId) {

        OcBOrder orderInfo = this.orderMapper.selectById(orderId);
        if (orderInfo == null) {
            return null;
        }

        OcBOrderRelation orderRelation = new OcBOrderRelation();
        List<OcBOrderItem> orderItemList = this.orderItemMapper.selectOrderItemList(orderId);
//        List<OcBOrderPayment> ocBOrderPayments = this.orderPaymentMapper.selectOrderPaymentItemList(orderId);

        orderRelation.setOrderInfo(orderInfo);
        orderRelation.setOrderItemList(orderItemList);

        return orderRelation;
    }

    /**
     * @param orderId 订单编号
     * @return OcBOrderRelation
     */
    public OcBOrderRelation selectOcBOrderRelation(long orderId) {

        OcBOrder orderInfo = this.orderMapper.selectById(orderId);
        if (orderInfo == null) {
            return null;
        }

        OcBOrderRelation orderRelation = new OcBOrderRelation();
        List<OcBOrderItem> orderItemList = this.orderItemMapper.selectOrderItemList(orderId);
        List<OcBOrderPayment> ocBOrderPaymentList = this.orderPaymentMapper.selectOrderPaymentItemList(orderId);
        List<OcBOrderPromotion> ocBOrderPromotionList = this.orderPromotionMapper.selectOcBOrderPromotionList(orderId);

        orderRelation.setOrderPaymentList(ocBOrderPaymentList);
        orderRelation.setOrderPromotionList(ocBOrderPromotionList);
        orderRelation.setOrderInfo(orderInfo);
        orderRelation.setOrderItemList(orderItemList);

        return orderRelation;
    }

    /**
     * @param orderId 订单编号
     * @return OcBOrderRelation
     */
    public OcBOrderRelation selectOmsOrderInfoOccupy(long orderId) {

        OcBOrder orderInfo = this.orderMapper.selectById(orderId);
        if (orderInfo == null) {
            return null;
        }

        OcBOrderRelation orderRelation = new OcBOrderRelation();
        List<OcBOrderItem> orderItemList = this.orderItemMapper.selectOrderItemListOccupy(orderId);

        orderRelation.setOrderInfo(orderInfo);
        orderRelation.setOrderItemList(orderItemList);


        if (log.isDebugEnabled()) {
            log.debug("OrderId={},start获取订单信息={}", orderId, JSON.toJSONString(orderRelation));
        }
        return orderRelation;
    }

    /**
     * @param tbOrderRelation OcBOrderRelation对象
     * @param isHistoryOrder  是否为历史单据信息
     * @return OcBOrderRelation
     */
    public OcBOrderRelation convertTaobaoOrderToOrder(IpTaobaoOrderRelation tbOrderRelation,
                                                      boolean isHistoryOrder) {
        // 判断是否部分发货，部分发货需要拆单
        String orderStatus = tbOrderRelation.getTaobaoOrder().getStatus();
        if (isHistoryOrder || !TaoBaoOrderStatus.SELLER_CONSIGNED_PART.equalsIgnoreCase(orderStatus)) {
            OcBOrderRelation ocBOrderRelation = transferUtil.taobaoOrderToOrder(tbOrderRelation, isHistoryOrder);
            // 历史订单 包含轻供商品，订单仓库字段不给默认仓库
            if (isHistoryOrder) {
                buildWarehouse(tbOrderRelation.getTaobaoOrderItemList(), ocBOrderRelation);
            }
            return ocBOrderRelation;
        } else {
            List<IpBTaobaoOrderItemEx> deliveryItems = tbOrderRelation
                    .getTaobaoOrderItemList().stream()
                    .filter(item -> !TaoBaoOrderStatus.WAIT_SELLER_SEND_GOODS.equalsIgnoreCase(item.getStatus())).collect(Collectors.toList());
            // 将订单已发货明细拆为一单，作为历史订单处理
            IpTaobaoOrderRelation taobaoOrderDelivery = new IpTaobaoOrderRelation();
            BeanUtils.copyProperties(tbOrderRelation, taobaoOrderDelivery);
            taobaoOrderDelivery.getTaobaoOrder().setNum(null);
            taobaoOrderDelivery.setTaobaoOrderItemList(deliveryItems);
            OcBOrderRelation orderInfo = transferUtil.taobaoOrderToOrder(taobaoOrderDelivery, true);
            orderInfo.getOrderInfo().setIsSplit(1);
            orderInfo.getOrderInfo().setSplitReason(SplitReason.SPLIT_PART_DELIVERY);
            orderInfo.getOrderInfo().setSuffixInfo("平台部分发货-已发货部分"); // 审核时有校验
            orderInfo.getOrderInfo().setAmtReceive(orderInfo.getOrderInfo().getOrderAmt());
            orderInfo.getOrderInfo().setReceivedAmt(orderInfo.getOrderInfo().getOrderAmt());
            // @历史订单出库时间赋值 任务ID 29976
            orderInfo.getOrderInfo().setScanTime(new Date());
            // 包含轻供商品，订单仓库字段不给默认仓库
            buildWarehouse(deliveryItems, orderInfo);
            ApplicationContextHandle.getBean(OmsOrderService.class)
                    .saveOmsOrderInfo(orderInfo, taobaoOrderDelivery, true, SystemUserResource.getRootUser());

            // 将订单未发货明细拆为一单，作为新单处理
            List<IpBTaobaoOrderItemEx> unDeliveryItems = tbOrderRelation
                    .getTaobaoOrderItemList().stream()
                    .filter(item -> TaoBaoOrderStatus.WAIT_SELLER_SEND_GOODS.equalsIgnoreCase(item.getStatus())).collect(Collectors.toList());
            IpTaobaoOrderRelation taobaoOrderUnDelivery = new IpTaobaoOrderRelation();
            BeanUtils.copyProperties(tbOrderRelation, taobaoOrderUnDelivery);
            taobaoOrderUnDelivery.getTaobaoOrder().setNum(null);
            taobaoOrderUnDelivery.setTaobaoOrderItemList(unDeliveryItems);
            OcBOrderRelation newOcBOrderRelation = transferUtil.taobaoOrderToOrder(taobaoOrderUnDelivery, false);
            newOcBOrderRelation.getOrderInfo().setIsSplit(1);
            newOcBOrderRelation.getOrderInfo().setSplitReason(SplitReason.SPLIT_PART_DELIVERY);
            newOcBOrderRelation.getOrderInfo().setSplitStatus(0);
            newOcBOrderRelation.getOrderInfo().setSuffixInfo("平台部分发货-未发货部分");
            newOcBOrderRelation.getOrderInfo().setAmtReceive(newOcBOrderRelation.getOrderInfo().getOrderAmt());
            newOcBOrderRelation.getOrderInfo().setReceivedAmt(newOcBOrderRelation.getOrderInfo().getOrderAmt());
            for (IpBTaobaoOrderItemEx item : unDeliveryItems) {
                ProductSku productSku = item.getProdSku();
                if (productSku == null) {
                    continue;
                }
                if ("Y".equals(productSku.getIsVirtual())) {
                    orderInfo.getOrderInfo().setIsInvented(1);
                    orderInfo.getOrderInfo().setPriceLabel("Y");
                    orderInfo.getOrderInfo().setOrderType(OrderTypeEnum.DIFFPRICE.getVal());
                    break;
                }
            }
            return newOcBOrderRelation;
        }

    }

    private void buildWarehouse(List<IpBTaobaoOrderItemEx> deliveryItems, OcBOrderRelation ocBOrderRelation) {
        // 包含轻供商品，订单仓库字段不给默认仓库
        boolean hasLightGoods = false;
        for (IpBTaobaoOrderItemEx item : deliveryItems) {
            ProductSku productSku = item.getProdSku();
            if (productSku == null) {
                continue;
            }
            Integer supplyType =
                    productSku.getPsCProSupplyType() == null ? 0 : productSku.getPsCProSupplyType().intValue();
            if (SupplyTypeEnum.PROXY.getValue().equals(supplyType)
                    || SupplyTypeEnum.CONSIGN.getValue().equals(supplyType)) {
//                ocBOrderRelation.getOrderInfo().setHasLightSupplyProd(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
                hasLightGoods = true;
            }
            if ("Y".equals(productSku.getIsVirtual())) {
                ocBOrderRelation.getOrderInfo().setIsInvented(1);
                ocBOrderRelation.getOrderInfo().setPriceLabel("Y");
                ocBOrderRelation.getOrderInfo().setOrderType(OrderTypeEnum.DIFFPRICE.getVal());
            }
        }
        if (!hasLightGoods) {
            Long defautId = omsWarehouseRuleService.queryDefaultWarehouse(ocBOrderRelation.getOrderInfo().getCpCShopId());
            ocBOrderRelation.getOrderInfo().setCpCPhyWarehouseId(defautId);
            if (defautId != null) {
                CpCPhyWarehouse physicalWarehouseInfo = omsWarehouseRuleService.queryByWarehouseId(defautId);
                if (physicalWarehouseInfo != null) {
                    ocBOrderRelation.getOrderInfo().setCpCPhyWarehouseEname(physicalWarehouseInfo.getEname());
                    ocBOrderRelation.getOrderInfo().setCpCPhyWarehouseEcode(physicalWarehouseInfo.getEcode());
                }
            }
        }
    }


    /**
     * 保存订单
     *
     * @param orderInfo       订单对象
     * @param tbOrderRelation 淘宝中间表关联关系
     * @param isHistoryOrder  是否为历史单据信息
     * @param operateUser     用户对象
     * @return true - 成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOmsOrderInfo(OcBOrderRelation orderInfo, IpTaobaoOrderRelation tbOrderRelation,
                                    boolean isHistoryOrder, User operateUser) {
        if (orderInfo == null) {
            return false;
        }

        if (orderInfo.getOrderInfo() == null) {
            return false;
        }
        if (log.isDebugEnabled()) {
            log.debug("OrderId={}，Start SaveOrder", orderInfo.getOrderInfo().getId());
        }

        // @20200913 地址逗号替换
        //    OmsOrderSaveUtil.getInstance().addressReplaceDot(orderInfo.getOrderInfo());
        OrderAddressConvertUtil.convert(orderInfo.getOrderInfo());
        int result = orderMapper.insert(orderInfo.getOrderInfo());

        if (orderInfo.getOrderItemList() != null) {
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
            if (isHistoryOrder) {
                //为历史订单 组合商品进行拆分
                List<OcBOrderItem> itemList =
                        orderItemList.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(itemList)) {
                    List<OcBOrderItem> items = omsConstituteSplitService.encapsulationParameter(itemList,
                            orderInfo.getOrderInfo(), operateUser, 0);
                    orderItemList.addAll(items);
                }
            }
            for (OcBOrderItem orderItem : orderItemList) {
                try {
                    orderItem.setOcBOrderId(orderInfo.getOrderInfo().getId());
                    orderItemMapper.insert(orderItem);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new NDSException(e);
                }
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start OcBOrderPromotion Item", orderInfo.getOrderInfo().getId());
        }
        if (orderInfo.getOrderPromotionList() != null) {
            for (OcBOrderPromotion promotion : orderInfo.getOrderPromotionList()) {
                promotion.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPromotionMapper.insert(promotion);
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder Payment", orderInfo.getOrderInfo().getId());
        }
        if (orderInfo.getOrderPaymentList() != null) {
            for (OcBOrderPayment payment : orderInfo.getOrderPaymentList()) {
                payment.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPaymentMapper.insert(payment);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder TaobaoOrder", orderInfo.getOrderInfo().getId());
        }
        if (orderInfo.getOrderTaobao() != null) {
            this.orderTaobaoMapper.insert(orderInfo.getOrderTaobao());
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder OrderFinkLog", orderInfo.getOrderInfo().getId());
        }
        if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
            List<OcBOrder> ocBOrders = new ArrayList<>();
            ocBOrders.add(orderInfo.getOrderInfo());
            this.orderLinkService.addOrderFinkLogsThread(ocBOrders, BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder OrderLog", orderInfo.getOrderInfo().getId());
        }
        if (isHistoryOrder) {

            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "历史单据新增订单成功", "", "", operateUser);
        } else {
            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "新增订单成功", "", "", operateUser);
        }


        if (result > 0) {
            /**
             * 单据转单或新增后插入占单任务表
             */
            if (!isHistoryOrder) {
                OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                toBeConfirmedTask.setOrderId(orderInfo.getOrderInfo().getId());
                toBeConfirmedTask.setCreationdate(new Date());
                toBeConfirmedTask.setStatus(0);
                toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
            }

        }
        return (result > 0);
    }
    /**
     * 保存订单
     *
     * @param orderInfo       订单对象
     * @param operateUser     用户对象
     * @return true - 成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOmsOrderInfoByCopy(OcBOrderRelation orderInfo, User operateUser) {
        if (orderInfo == null) {
            return false;
        }

        if (orderInfo.getOrderInfo() == null) {
            return false;
        }
        if (log.isDebugEnabled()) {
            log.debug("OrderId={}，Start SaveOrder", orderInfo.getOrderInfo().getId());
        }


        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        // @20200913 地址逗号替换
        //    OmsOrderSaveUtil.getInstance().addressReplaceDot(orderInfo.getOrderInfo());
        OrderAddressConvertUtil.convert(ocBOrder);
        ocBOrder.setId(sequenceUtil.buildOrderSequenceId());
        OmsModelUtil.setDefault4Add(ocBOrder,operateUser);

        int result = orderMapper.insert(ocBOrder);

        if (orderInfo.getOrderItemList() != null) {
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
            for (OcBOrderItem orderItem : orderItemList) {
                try {
                    orderItem.setId(sequenceUtil.buildOrderItemSequenceId());
                    OmsModelUtil.setDefault4Add(orderItem,operateUser);
                    orderItem.setOcBOrderId(orderInfo.getOrderInfo().getId());
                    orderItemMapper.insert(orderItem);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new NDSException(e);
                }
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start OcBOrderPromotion Item", orderInfo.getOrderInfo().getId());
        }
        if (orderInfo.getOrderPromotionList() != null) {
            for (OcBOrderPromotion promotion : orderInfo.getOrderPromotionList()) {
                promotion.setId(sequenceUtil.buildOrderPromotionSequenceId());
                OmsModelUtil.setDefault4Add(promotion,operateUser);
                promotion.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPromotionMapper.insert(promotion);
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder Payment", orderInfo.getOrderInfo().getId());
        }
        if (orderInfo.getOrderPaymentList() != null) {
            for (OcBOrderPayment payment : orderInfo.getOrderPaymentList()) {
                payment.setId(sequenceUtil.buildOrderPaymentSequenceId());
                OmsModelUtil.setDefault4Add(payment,operateUser);
                payment.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPaymentMapper.insert(payment);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder TaobaoOrder", orderInfo.getOrderInfo().getId());
        }
        OcBOrderTaobao orderTaobao = orderInfo.getOrderTaobao();
        if (Objects.nonNull(orderTaobao)) {
            orderTaobao.setId(sequenceUtil.buildOrderTaobaoSequenceId());
            OmsModelUtil.setDefault4Add(orderTaobao,operateUser);
            this.orderTaobaoMapper.insert(orderTaobao);
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder OrderFinkLog", orderInfo.getOrderInfo().getId());
        }
        if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
            List<OcBOrder> ocBOrders = new ArrayList<>();
            ocBOrders.add(orderInfo.getOrderInfo());
            this.orderLinkService.addOrderFinkLogsThread(ocBOrders, BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder OrderLog", orderInfo.getOrderInfo().getId());
        }
        orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                OrderLogTypeEnum.ORDER_COPY.getKey(), "复制订单新增成功！", "", "", operateUser);


        if (result > 0) {
            /**
             * 单据转单或新增后插入占单任务表
             */
            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(orderInfo.getOrderInfo().getId());
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);

            //omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);


        }
        return (result > 0);
    }

    /**
     * 保存订单
     *
     * @param orderInfo       订单对象
     * @param isHistoryOrder  是否为历史单据信息
     * @param operateUser     用户对象
     * @return true - 成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOmsOrderInfo(OcBOrderRelation orderInfo,
                                    boolean isHistoryOrder, User operateUser) {
        if (orderInfo == null) {
            return false;
        }

        if (orderInfo.getOrderInfo() == null) {
            return false;
        }
        if (log.isDebugEnabled()) {
            log.debug("OrderId={}，Start SaveOrder", orderInfo.getOrderInfo().getId());
        }

        // @20200913 地址逗号替换
        //    OmsOrderSaveUtil.getInstance().addressReplaceDot(orderInfo.getOrderInfo());
        OrderAddressConvertUtil.convert(orderInfo.getOrderInfo());
        int result = orderMapper.insert(orderInfo.getOrderInfo());

        if (orderInfo.getOrderItemList() != null) {
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
            if (isHistoryOrder) {
                //为历史订单 组合商品进行拆分
                List<OcBOrderItem> itemList =
                        orderItemList.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(itemList)) {
                    List<OcBOrderItem> items = omsConstituteSplitService.encapsulationParameter(itemList,
                            orderInfo.getOrderInfo(), operateUser, 0);
                    orderItemList.addAll(items);
                }
            }
            for (OcBOrderItem orderItem : orderItemList) {
                try {
                    orderItem.setOcBOrderId(orderInfo.getOrderInfo().getId());
                    orderItemMapper.insert(orderItem);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new NDSException(e);
                }
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start OcBOrderPromotion Item", orderInfo.getOrderInfo().getId());
        }
        if (orderInfo.getOrderPromotionList() != null) {
            for (OcBOrderPromotion promotion : orderInfo.getOrderPromotionList()) {
                promotion.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPromotionMapper.insert(promotion);
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder Payment", orderInfo.getOrderInfo().getId());
        }
        if (orderInfo.getOrderPaymentList() != null) {
            for (OcBOrderPayment payment : orderInfo.getOrderPaymentList()) {
                payment.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPaymentMapper.insert(payment);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder TaobaoOrder", orderInfo.getOrderInfo().getId());
        }
        if (orderInfo.getOrderTaobao() != null) {
            this.orderTaobaoMapper.insert(orderInfo.getOrderTaobao());
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder OrderFinkLog", orderInfo.getOrderInfo().getId());
        }
        if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
            List<OcBOrder> ocBOrders = new ArrayList<>();
            ocBOrders.add(orderInfo.getOrderInfo());
            this.orderLinkService.addOrderFinkLogsThread(ocBOrders, BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder OrderLog", orderInfo.getOrderInfo().getId());
        }
        if (isHistoryOrder) {

            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "历史单据新增订单成功", "", "", operateUser);
        } else {
            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "新增订单成功", "", "", operateUser);
        }


        if (result > 0) {
            /**
             * 单据转单或新增后插入占单任务表
             */
            if (!isHistoryOrder) {
                OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                toBeConfirmedTask.setOrderId(orderInfo.getOrderInfo().getId());
                toBeConfirmedTask.setCreationdate(new Date());
                toBeConfirmedTask.setStatus(0);
                toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
            }

        }
        return (result > 0);
    }

    /**
     * 保存订单
     *
     * @param orderInfo                订单对象
     * @param alibabaAscpOrderRelation 猫超直发中间表关联关系
     * @param operateUser              用户对象
     * @return true - 成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOmsOrderInfo(OcBOrderRelation orderInfo, IpAlibabaAscpOrderRelation alibabaAscpOrderRelation,
                                    User operateUser) {
        if (orderInfo == null) {
            return false;
        }

        if (orderInfo.getOrderInfo() == null) {
            return false;
        }
        if (log.isDebugEnabled()) {
            log.debug("Start SaveOrder");
        }
        int result = orderMapper.insert(orderInfo.getOrderInfo());

        if (orderInfo.getOrderItemList() != null) {
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
//            if (isHistoryOrder) {
//                //为历史订单 组合商品进行拆分
//                List<OcBOrderItem> itemList = orderItemList.stream().filter(p -> p.getProType() == SkuType
//                .NO_SPLIT_COMBINE).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(itemList)) {
//                    List<OcBOrderItem> items = omsConstituteSplitService.encapsulationParameter(itemList, orderInfo
//                    .getOrderInfo(), operateUser);
//                    orderItemList.addAll(items);
//                }
//            }
            for (OcBOrderItem orderItem : orderItemList) {
                try {
                    orderItem.setOcBOrderId(orderInfo.getOrderInfo().getId());
                    orderItemMapper.insert(orderItem);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new NDSException(e);
                }
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("Start OcBOrderPromotion Item");
        }
        if (orderInfo.getOrderPromotionList() != null) {
            for (OcBOrderPromotion promotion : orderInfo.getOrderPromotionList()) {
                promotion.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPromotionMapper.insert(promotion);
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("Start SaveOrder Payment");
        }
        if (orderInfo.getOrderPaymentList() != null) {
            for (OcBOrderPayment payment : orderInfo.getOrderPaymentList()) {
                payment.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPaymentMapper.insert(payment);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("Start SaveOrder TaobaoOrder");
        }
        this.orderTaobaoMapper.insert(orderInfo.getOrderTaobao());

        if (log.isDebugEnabled()) {
            log.debug("Start SaveOrder OrderFinkLog");
        }
        if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
            List<OcBOrder> ocBOrders = new ArrayList<>();
            ocBOrders.add(orderInfo.getOrderInfo());
            this.orderLinkService.addOrderFinkLogsThread(ocBOrders, BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
        }

        if (log.isDebugEnabled()) {
            log.debug("Start SaveOrder OrderLog");
        }
//        if (isHistoryOrder) {
//
//            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
//                    OrderLogTypeEnum.ORDER_ADD.getKey(), "历史单据新增订单成功", "", "", operateUser);
//        } else {
        orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                OrderLogTypeEnum.ORDER_ADD.getKey(), "新增订单成功", "", "", operateUser);
        //    }


        if (result > 0) {

            // 2019-08-04 易邵峰修改：将Redis 和 ES的推送 调换位置。目的是为了防止ES操作时间过长
            // Redis推送。主要是为了防止ES在存储数据时，会延迟100ms左右。为了保障不重复转单，在Redis进行存储。
            // 在进行查询时，先查询ES，若ES不存在，再查询Redis。
            String redisKey = BllRedisKeyResources.getOmsOrderKey(orderInfo.getOrderInfo().getSourceCode());
            this.getRedisTemplate().opsForValue().set(redisKey, orderInfo.getOrderId(), OMS_ORDER_REDIS_TIMEOUT,
                    TimeUnit.MILLISECONDS);
            /**
             * 单据转单或新增后插入占单任务表
             */
            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(orderInfo.getOrderInfo().getId());
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);

        }
        return (result > 0);
    }

    /**
     * 查找订单对象
     *
     * @param orderId 订单Id
     * @return OcBOrder
     */
    public OcBOrder selectOrderInfo(Long orderId) {
        return orderMapper.selectByID(orderId);
    }

    /**
     * 判断订单id是否存在
     *
     * @param orderId 订单Id
     * @return int
     */
    public int selectIdByCount(Long orderId) {
        return orderMapper.selectIdByCount(orderId);
    }

    /**
     * 作废订单Update数据库
     *
     * @param orderInfo 订单信息 @param operateUser 操作用户
     * @return 作废是否成功
     */
    public boolean updateOrderSystemVoid(OcBOrder orderInfo, User operateUser) {
        //非待分配状态需调用取消发货逻辑单服务
        orderInfo.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
        orderInfo.setSuffixInfo(orderInfo.getId() + "–IV");
        orderInfo.setModifierename(operateUser.getName());
        orderInfo.setModifieddate(new Date());
        orderInfo.setSysremark("订单ID为 " + orderInfo.getId() + " 的订单作废成功");
        boolean updateResult = this.updateOrderInfo(orderInfo);
        if (updateResult) {
            orderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(),
                    OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(),
                    "订单编号为" + orderInfo.getBillNo() + " 的订单作废成功", null, null,
                    operateUser);
        }
        return updateResult;
    }

    public boolean updateOrderSystemCancelled(OcBOrder orderInfo, User operateUser) {
        //非待分配状态需调用取消发货逻辑单服务
        orderInfo.setOrderStatus(OmsOrderStatus.CANCELLED.toInteger());
        orderInfo.setSuffixInfo(orderInfo.getId() + "–IV");
        orderInfo.setModifierename(operateUser.getName());
        orderInfo.setModifieddate(new Date());
        orderInfo.setSysremark("订单ID为 " + orderInfo.getId() + " 的订单作废成功");
        boolean updateResult = this.updateOrderInfo(orderInfo);
        if (updateResult) {
            orderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(),
                    OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(),
                    "订单编号为" + orderInfo.getBillNo() + " 的订单作废成功", null, null,
                    operateUser);
        }
        return updateResult;
    }

    /**
     * 更新订单主表
     *
     * @param orderInfo 订单修改对象  [Mapper]
     * @return boolean
     */
    public boolean updateOrderInfo(OcBOrder orderInfo) {
        return orderMapper.updateById(orderInfo) > 0;
    }

    /**
     * 审核成功更新订单主表
     *
     * @param orderId 订单ID
     * @param name    修改人
     * @return boolean
     */
    public boolean updateAuditSuccess(Long orderId, String name) {
        return orderMapper.updateAuditSuccess(orderId, name) > 0;
    }

    /**
     * 批量更新订单主表
     *
     * @param ids [Mapper]
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderList(List<Long> ids) {
        return orderMapper.updateOrderIsOverfiveSysremarkList(ids) > 0;
    }


    /**
     * 新增订单对象
     *
     * @param ocBOrderDto 订单对象
     * @return boolean
     */
    public boolean saveOrderInfo(OcBOrder ocBOrderDto) {
        // @20200913 地址逗号替换
        OmsOrderSaveUtil.getInstance().addressReplaceDot(ocBOrderDto);
        return orderMapper.insert(ocBOrderDto) > 0;
    }

    /**
     * 孙俊磊0425
     * 执行全渠道订单数据新增
     *
     * @param relation       包含转换好的，订单主子表数据
     * @param isHistoryOrder 是否为历史单据信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderInfo(IpJingdongOrderRelation relation, boolean isHistoryOrder) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("京东转换数据relation={},{}", JSON.toJSONString(relation), this.getClass().getSimpleName());
            }
            OcBOrder ocBOrder = relation.getOcBOrder();
            // @20200913 地址逗号替换
            //    OmsOrderSaveUtil.getInstance().addressReplaceDot(relation.getOcBOrder());
            OrderAddressConvertUtil.convert(relation.getOcBOrder());
            if (orderMapper.insert(ocBOrder) > 0) {
                orderItemMapper.batchInsert(relation.getOcBOrderItems());
                if (!OrderTypeEnum.EXCHANGE.getVal().equals(ocBOrder.getOrderType())) {
                    //明细插入成功后，调用金额平摊服务（详情请查看金额平摊服务PRD）
                    doCheckAndUpdateBlanceMoney(relation);
                    //明细插入成功后，调用平台优惠金额平摊服务（详情请参照金额平摊服务PRD）
                    doJdCheckAndUpdateBlanceMoney(relation);
                }
                OcBOrderRelation orderInfo = new OcBOrderRelation();
                orderInfo.setOrderInfo(ocBOrder);
                orderInfo.setOrderItemList(relation.getOcBOrderItems());
                OrderPriceValidate.isSendDingTalk(orderInfo);
                orderPaymentMapper.insert(relation.getOcBOrderPayment());

                if (OrderTypeEnum.EXCHANGE.getVal().equals(ocBOrder.getOrderType()) && Objects.isNull(ocBOrder.getOrigReturnOrderId())) {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.JINGDONG_EXCHANGEORDER_TRANSFER.getKey(),
                            "京东换货单转换关联退单失败", ocBOrder.getTid(), "京东换货单转换关联退单失败", SystemUserResource.getRootUser());
                }
                if (log.isDebugEnabled()) {
                    log.debug("OrderId={},日志服务：订单新增成功", ocBOrder.getId());
                }


                if (relation.getOcBOrderPromotionList() != null && relation.getOcBOrderPromotionList().size() != 0) {
                    orderPromotionMapper.batchInsert(relation.getOcBOrderPromotionList());
                    if (log.isDebugEnabled()) {
                        log.debug("OrderId={},日志服务：订单优惠信息成功", ocBOrder.getId());
                    }
                }


                try {
                    if (isHistoryOrder) {
                        orderLogService.addUserOrderLog(relation.getOcBOrder().getId(),
                                relation.getOcBOrder().getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(),
                                "历史订单转换新增订单成功", "", "", SystemUserResource.getRootUser());
                    } else {
                        orderLogService.addUserOrderLog(relation.getOcBOrder().getId(),
                                relation.getOcBOrder().getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(), "新增订单成功", ""
                                , "", SystemUserResource.getRootUser());
                    }
                } catch (Exception ex) {
                    log.error("OrderId={},日志服务：异常={}", ocBOrder.getId(), ex.getMessage());
                }
                // Redis推送。主要是为了防止ES在存储数据时，会延迟100ms左右。为了保障不重复转单，在Redis进行存储。
                // 在进行查询时，先查询ES，若ES不存在，再查询Redis。
                String redisKey = BllRedisKeyResources.getOmsOrderKey(relation.getOrderNo().toString());
                this.getRedisTemplate().opsForValue().set(redisKey, relation.getOcBOrder().getId(),
                        OMS_ORDER_REDIS_TIMEOUT,
                        TimeUnit.MILLISECONDS);
                if (!isHistoryOrder) {
                    /**
                     * 单据转单或新增后插入占单任务表
                     */
                    OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                    toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                    toBeConfirmedTask.setOrderId(relation.getOcBOrder().getId());
                    toBeConfirmedTask.setCreationdate(new Date());
                    toBeConfirmedTask.setStatus(0);
                    toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
                }
            }
        } catch (Exception ex) {
            if (log.isDebugEnabled()) {
                log.debug("OrderId={},日志服务：新增全渠道订单失败={}", relation.getOcBOrder().getId(), ex);
            }
            throw new NDSException("新增全渠道订单失败" + ex);
        }
    }

    /**
     * 判断子表sum（平摊金额）是否与头表订单优惠金额是否一致，若一致，不处理，若不一致 则调用订单平摊金额服务
     *
     * @param relation 包含所有信息的relation
     */
    private void doCheckAndUpdateBlanceMoney(IpJingdongOrderRelation relation) {
        BigDecimal orderDiscountAmt = getOrderItemsDiscountAmt(relation);
        if (!orderDiscountAmt.equals(relation.getOcBOrder().getOrderDiscountAmt())) {
            OcBOrderRelation ocRelation = new OcBOrderRelation();
            ocRelation.setOrderInfo(relation.getOcBOrder());
            try {
                boolean checkAndUpdateBlanceMoney =
                        omsOrderCheckAndUpdateService.doCheckAndUpdateBlanceMoney(ocRelation);
                if (!checkAndUpdateBlanceMoney) {
                    throw new NDSException();
                }
            } catch (Exception ex) {
                if (log.isDebugEnabled()) {
                    log.debug("OrderId={},日志服务：订单平摊金额服务失败={}", relation.getOcBOrder().getId(), ex);
                }
                throw new NDSException("保存失败:订单平摊金额服务失败！");
            }
        }
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 京东平台优惠金额平摊  判断子表sum（平摊金额）是否与头表订单优惠金额是否一致，若一致，不处理，若不一致 则调用订单平摊金额服务
     * @Date 2019-11-27
     * @Param [relation]
     **/
    private void doJdCheckAndUpdateBlanceMoney(IpJingdongOrderRelation relation) {
//        BigDecimal platformDiscountAmt = getPlatformDiscountAmt(relation);
//        if (!platformDiscountAmt.equals(relation.getOcBOrder().getReserveDecimal01())
//                && !platformDiscountAmt.equals(BigDecimal.ZERO)) {
//            OcBOrderRelation ocRelation = new OcBOrderRelation();
//            ocRelation.setOrderInfo(relation.getOcBOrder());
//            try {
//                boolean flag = doJdPlatformBlanceMoney(ocRelation);
//                if (!flag) {
//                    throw new NDSException();
//                }
//            } catch (Exception ex) {
//                log.debug("日志服务：京东订单平台优惠平摊金额服务失败！");
//                throw new NDSException("保存失败:京东订单平台优惠平摊金额服务失败！");
//            }
//        }

        OcBOrderRelation ocRelation = new OcBOrderRelation();
        ocRelation.setOrderInfo(relation.getOcBOrder());
        try {
            boolean flag = doJdPlatformBlanceMoney(ocRelation);
            if (!flag) {
                throw new NDSException();
            }
        } catch (Exception ex) {
            if (log.isDebugEnabled()) {
                log.debug("OrderId={},日志服务：京东订单平台优惠平摊金额服务失败={}", relation.getOcBOrder().getId(), ex);
            }
            throw new NDSException("保存失败:京东订单平台优惠平摊金额服务失败！");
        }

    }

    /**
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @Description 获取平台优惠金额
     * @Date 2019-11-27
     * @Param [relation]
     **/
    private BigDecimal getPlatformDiscountAmt(IpJingdongOrderRelation relation) {
        //平台优惠金额
        BigDecimal platformDiscountAmt = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(relation.getOcBOrderItems())) {
            for (OcBOrderItem item : relation.getOcBOrderItems()) {
                if (null != item.getReserveDecimal04()) {
                    platformDiscountAmt = platformDiscountAmt.add(item.getReserveDecimal04());
                }
            }
        }
        return platformDiscountAmt;
    }

    /**
     * 京东平台优惠平摊金额为未退款明细
     *
     * @param orderInfo OcBOrderRelation对象
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean doJdPlatformBlanceMoney(OcBOrderRelation orderInfo) {

        //执行内容不包含已退款明细
        List<OcBOrderItem> orderItemList = omsOrderItemService.selectUnSuccessRefund(orderInfo.getOrderInfo().getId());
        //去除成交单价为0的明细
        List<OcBOrderItem> unSuccessRefundList =
                orderItemList.stream().filter(o -> o.getPrice().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (log.isDebugEnabled()) {
            log.debug("OrderId={},{},平摊金额对象服务明细入参={}", orderInfo.getOrderInfo().getId(), this.getClass().getName(),
                    JSON.toJSONString(unSuccessRefundList));
        }
        //主表平台优惠金额
        BigDecimal platformDisAmt = BigDecimal.ZERO;
        try {
            if (CollectionUtils.isNotEmpty(unSuccessRefundList)) {
                if (log.isDebugEnabled()) {
                    log.debug("OrderId={},{},订单明细数据print size={}", orderInfo.getOrderInfo().getId(),
                            this.getClass().getName(), unSuccessRefundList);
                }
                if (unSuccessRefundList.size() == 1) {
                    if (log.isDebugEnabled()) {
                        log.debug("OrderId={},{},订单只有一条明细的数据情况执行平摊金额!", orderInfo.getOrderInfo().getId(),
                                this.getClass().getName());
                    }
                    //若订单明细中只有一条数据，则更新订单明细中“整单平摊金额”=主表（全渠道订单）的“订单优惠金额”
                    OcBOrderItem orderItemDto = unSuccessRefundList.get(0);
                    //更新明细表的明细平摊金额
                    //加上京东平台优惠券
                    BigDecimal platformDiscountAmt = BigDecimal.ZERO;
                    platformDiscountAmt = platformDiscountAmt.add(orderInfo.getOrderInfo().getAmtPlatDiscount() == null
                            ? BigDecimal.ZERO : orderInfo.getOrderInfo().getAmtPlatDiscount());
                    platformDiscountAmt = platformDiscountAmt.add(orderItemDto.getAmtJingdongCoupon() == null
                            ? BigDecimal.ZERO : orderItemDto.getAmtJingdongCoupon());
                    orderItemDto.setReserveDecimal04(platformDiscountAmt);
                    omsOrderItemService.updateOcBOrderItem(orderItemDto, orderInfo.getOrderId());
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("OrderId={},{},订单明细有多条的数据情况执行平摊金额!", orderInfo.getOrderInfo().getId(),
                                this.getClass().getName());
                    }
                    int j = 0;
                    for (OcBOrderItem orderItem : unSuccessRefundList) {
                        //判断订单明细中的成交价格是否大于0
                        if (orderItem.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                            j++;
                        }
                    }
                    //只要有一条满足都执行平摊金额
                    if (j > 0) {
                        //除末尾行其他明细整单平摊金额之和
                        BigDecimal totalItemAmt = BigDecimal.ZERO;
                        //最后一行明细平摊金额
                        BigDecimal lastOrderSplitAmt = BigDecimal.ZERO;
                        //主表的平台优惠金额
                        BigDecimal platformDiscountAmt = BigDecimal.ZERO;
                        platformDiscountAmt = (orderInfo.getOrderInfo().getAmtPlatDiscount() == null ? BigDecimal.ZERO
                                : orderInfo.getOrderInfo().getAmtPlatDiscount());
                        if (log.isDebugEnabled()) {
                            log.debug("OrderId={},{},平台优惠金额={}", orderInfo.getOrderInfo().getId(),
                                    this.getClass().getName(), platformDiscountAmt);
                        }//非退款明细求和
                        BigDecimal sumPrice = BigDecimal.ZERO;
                        sumPrice = sumPrice(orderInfo.getOrderInfo(), unSuccessRefundList);
                        if (log.isDebugEnabled()) {
                            log.debug("OrderId={},{},订单非退款明细求和={}", orderInfo.getOrderInfo().getId(),
                                    this.getClass().getName(), sumPrice);
                        }
                        //避免明细求和为0
                        if (sumPrice.compareTo(BigDecimal.ZERO) != 0) {
                            for (int i = 0; i < unSuccessRefundList.size(); i++) {
                                OcBOrderItem newOderItem = unSuccessRefundList.get(i);
                                if (i < unSuccessRefundList.size() - 1) {
                                    //“整单平摊金额”= 【（当前明细“成交价格”*“数量”/sum（明细“成交价格”*“数量”）】*主表的平台优惠金额
                                    BigDecimal platformSplitAmt = BigDecimal.ZERO;
                                    platformSplitAmt =
                                            ((newOderItem.getPrice().multiply(newOderItem.getQty()).divide(sumPrice,
                                                    2, RoundingMode.HALF_EVEN))
                                                    .multiply(platformDiscountAmt)).setScale(2,
                                                    BigDecimal.ROUND_CEILING);
                                    if (log.isDebugEnabled()) {
                                        log.debug("OrderId={},{},platformSplitAmt={}", orderInfo.getOrderId(),
                                                this.getClass().getName(), platformSplitAmt);
                                    }
                                    //除去最后一行的整体平摊金额之和
                                    totalItemAmt = totalItemAmt.add(platformSplitAmt);
                                    //更新明细平摊金额
                                    //加上京东平台优惠金额
                                    platformSplitAmt = platformSplitAmt.add(newOderItem.getAmtJingdongCoupon() == null
                                            ? BigDecimal.ZERO : newOderItem.getAmtJingdongCoupon());
                                    newOderItem.setReserveDecimal04(platformSplitAmt);
                                    omsOrderItemService.updateOcBOrderItem(newOderItem, orderInfo.getOrderId());
                                } else if (i == unSuccessRefundList.size() - 1) {
                                    //减去平摊金额
                                    lastOrderSplitAmt = platformDiscountAmt.subtract(totalItemAmt);
                                    if (log.isDebugEnabled()) {
                                        log.debug("OrderId={},{},lastOrderSplitAmt={}",
                                                orderInfo.getOrderInfo().getId(), this.getClass().getName(),
                                                lastOrderSplitAmt);
                                    }
                                    //加上京东平台优惠金额
                                    lastOrderSplitAmt = lastOrderSplitAmt.add(newOderItem.getAmtJingdongCoupon() == null
                                            ? BigDecimal.ZERO : newOderItem.getAmtJingdongCoupon());
                                    newOderItem.setReserveDecimal04(lastOrderSplitAmt);
                                    omsOrderItemService.updateOcBOrderItem(newOderItem, orderInfo.getOrderId());
                                }
                            }
                        }
                        if (log.isDebugEnabled()) {
                            log.debug("OrderId={},{},订单除去末尾行的totalItemAmt={}", orderInfo.getOrderInfo().getId(),
                                    this.getClass().getName(), totalItemAmt);

                            log.debug("OrderId={},{},订单最后一行的lastOrderSplitAmt={}", orderInfo.getOrderInfo().getId(),
                                    this.getClass().getName(), lastOrderSplitAmt);
                        }
                    }
                }
                //重新计算平台优惠金额，明细平摊之和
                List<OcBOrderItem> unNewSuccessRefundList =
                        omsOrderItemService.selectUnSuccessRefund(orderInfo.getOrderInfo().getId());
                for (OcBOrderItem ocBOrderItem : unNewSuccessRefundList) {
                    platformDisAmt = platformDisAmt.add(ocBOrderItem.getReserveDecimal04() == null
                            ? BigDecimal.ZERO : ocBOrderItem.getReserveDecimal04());
                }
            }
            //非淘宝订单,清除流程系统备注
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setSysremark("");
            ocBOrder.setId(orderInfo.getOrderId());
            //平台优惠金额
            ocBOrder.setAmtPlatDiscount(platformDisAmt);
            updateOrderInfo(ocBOrder);
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("OrderId={},{}, 订单执行京东平台优惠金额平摊逻辑服务异常={}" + orderInfo.getOrderInfo().getId(),
                    this.getClass().getName(), ex.getMessage());
            return false;
        }
    }

    /**
     * 非退款明细求和
     *
     * @param orderInfo
     * @param unRefundItemList
     * @return BigDecimal
     */
    private BigDecimal sumPrice(OcBOrder orderInfo, List<OcBOrderItem> unRefundItemList) {

        BigDecimal priceTotal = BigDecimal.ZERO;
        for (OcBOrderItem ocBOrderItem : unRefundItemList) {
            BigDecimal price = (ocBOrderItem.getPrice() == null ? BigDecimal.ZERO : ocBOrderItem.getPrice());
            BigDecimal qty = (ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty());
            priceTotal = priceTotal.add(price.multiply(qty));
        }
        if (log.isDebugEnabled()) {
            log.debug("OrderId={},非退款明细金额求和={}", orderInfo.getId(), priceTotal);
        }
        return priceTotal;
    }

    /**
     * 获取订单明细的折扣金额
     *
     * @param relation 京东单据对象
     * @return 订单明细折扣金额
     */
    private BigDecimal getOrderItemsDiscountAmt(IpJingdongOrderRelation relation) {
        //订单优惠金额
        BigDecimal orderDiscountAmt = new BigDecimal("0");
        if (relation.getOcBOrderItems() != null && relation.getOcBOrderItems().size() != 0) {
            for (OcBOrderItem item : relation.getOcBOrderItems()) {
                if (item.getOrderSplitAmt() == null) {
                    item.setOrderSplitAmt(new BigDecimal("0"));
                }
                orderDiscountAmt = orderDiscountAmt.add(item.getOrderSplitAmt());
            }
        }
        return orderDiscountAmt;
    }

    /**
     * @param orderInfo      带有全部京东信息的orderInfo
     * @param isHistoryOrder 是否为历史单据信息
     * @return 包含全渠道所有信息的relation, 子表分库键已经设置好
     */
    public IpJingdongOrderRelation convertJdOrderToOrder(IpJingdongOrderRelation orderInfo, boolean isHistoryOrder) throws Exception {

        OcBOrder ocBOrder = jingdongOrderTransferUtils.convertJdOrderToOmsOrder(orderInfo, isHistoryOrder);
        orderInfo.setOcBOrderItems(jingdongOrderTransferUtils.convertJdOrderItemToOmsOrderItem(orderInfo));
        orderInfo.setOcBOrderPayment(jingdongOrderTransferUtils.convertJingdongOrderPayToOmsOrderPay(ocBOrder,
                orderInfo.getJingdongOrder()));
        orderInfo.setOcBOrderPromotionList(jingdongOrderTransferUtils.convertJingdongOrderCoupondListToOmsOrderPromotionList(orderInfo.getJingdongCoupondtaiList(), ocBOrder));
        if (isHistoryOrder) {
            // 包含轻供商品，订单仓库字段不给默认仓库
            boolean hasLightGoods = false;
            for (IpBJingdongOrderItemExt item : orderInfo.getJingdongOrderItems()) {
                ProductSku productSku = item.getProdSku();
                if (productSku == null) {
                    continue;
                }
                Integer supplyType =
                        productSku.getPsCProSupplyType() == null ? 0 : productSku.getPsCProSupplyType().intValue();
                if (SupplyTypeEnum.PROXY.getValue().equals(supplyType)
                        || SupplyTypeEnum.CONSIGN.getValue().equals(supplyType)) {
//                    orderInfo.getOcBOrder().setHasLightSupplyProd(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
                    hasLightGoods = true;
                }
                if ("Y".equals(productSku.getIsVirtual())) {
                    orderInfo.getOcBOrder().setIsInvented(1);
                    orderInfo.getOcBOrder().setPriceLabel("Y");
                    orderInfo.getOcBOrder().setOrderType(OrderTypeEnum.DIFFPRICE.getVal());
                }
            }
            if (!hasLightGoods) {
                Long defautId = omsWarehouseRuleService.queryDefaultWarehouse(orderInfo.getOcBOrder().getCpCShopId());
                orderInfo.getOcBOrder().setCpCPhyWarehouseId(defautId);
                if (defautId != null) {
                    CpCPhyWarehouse physicalWarehouseInfo = omsWarehouseRuleService.queryByWarehouseId(defautId);
                    if (physicalWarehouseInfo != null) {
                        orderInfo.getOcBOrder().setCpCPhyWarehouseEname(physicalWarehouseInfo.getEname());
                        orderInfo.getOcBOrder().setCpCPhyWarehouseEcode(physicalWarehouseInfo.getEcode());
                    }
                }
            }
        }
        return orderInfo;

    }

    /**
     * 审核. 重单检查
     * 排除已取消, 作废
     *
     * @param orderId 订单Id
     * @return OcBOrder
     */
    public OcBOrder searchDuplicateOrder(Long orderId) {
        return orderMapper.selectOrderCancelAndCancelInfo(orderId);
    }

    /**
     * @param jitxOrderRelation OcBOrderRelation对象
     * @param isHistoryOrder    是否为历史单据信息
     * @return OcBOrderRelation
     */
    public OcBOrderRelation convertJitxOrderToOrder(IpJitxOrderRelation jitxOrderRelation,
                                                    boolean isHistoryOrder) {
        OcBOrderRelation orderInfo = jitxTransferUtil.jitxOrderToOrder(jitxOrderRelation, isHistoryOrder);
        return orderInfo;
    }

    /**
     * 保存订单
     *
     * @param orderInfo         订单对象
     * @param jitxOrderRelation 中间表关联关系
     * @param isHistoryOrder    是否为历史单据信息
     * @param operateUser       用户对象
     * @return true - 成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOmsOrderInfoForJitx(OcBOrderRelation orderInfo, IpJitxOrderRelation jitxOrderRelation,
                                           boolean isHistoryOrder, User operateUser) {
        if (orderInfo == null) {
            return false;
        }
        if (orderInfo.getOrderInfo() == null) {
            return false;
        }
        if (CollectionUtils.isEmpty(orderInfo.getOrderItemList())) {
            throw new NDSException("订单明细不存在，转单失败！");
        }

        if (log.isDebugEnabled()) {
            log.debug("OrderId={},Start SaveOrder", orderInfo.getOrderInfo().getId());
        }

        // @20200913 地址逗号替换
        //    OmsOrderSaveUtil.getInstance().addressReplaceDot(orderInfo.getOrderInfo());
        OrderAddressConvertUtil.convert(orderInfo.getOrderInfo());
        int result = orderMapper.insert(orderInfo.getOrderInfo());

        if (orderInfo.getOrderItemList() != null) {
            for (OcBOrderItem orderItem : orderInfo.getOrderItemList()) {
                orderItem.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderItemMapper.insert(orderItem);
            }
        }

        if (isHistoryOrder) {
            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "历史单据新增订单成功", "", "", operateUser);
        } else {
            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "新增订单成功", "", "", operateUser);
            jitxOrderRelation.setOcBOrderId(orderInfo.getOrderId());
            if (jitxOrderRelation.getSameWarehouse() != null) {
                //进行了仓库是否一致判断才需要进行取消占用或平移操作
                this.handleStockOccupy(jitxOrderRelation,orderInfo);
            }else{
                if (jitxOrderRelation.isNeedCancelStockOccupy()) {
                    //时效订单部分缺货  需要取消占用 重新占库存
                    ValueHolderV14 v14 = ipJitxOrderService.cancelStockOccupy(jitxOrderRelation);
                    if (v14.isOK()) {
                        if (log.isDebugEnabled()) {
                            log.debug("Step85JitxStockOccupy 时效订单部分缺货,取消占用结果信息：{}", v14.getMessage());
                        }
                    } else {
                        throw new NDSException("时效订单部分缺货,取消占用异常:" + SplitMessageUtil.splitErrMsgBySize(v14.getMessage(), SplitMessageUtil.SIZE_500));
                    }
                }
            }
            //需要更新时效订单为完成 占用明细为已匹配成功
            if (jitxOrderRelation.isNeedUpdateTimeOrder()) {
                if (log.isDebugEnabled()) {
                    log.debug("{},更新时效订单为完成", this.getClass().getSimpleName());
                }
                IpBTimeOrderVip update = new IpBTimeOrderVip();
                update.setOrderSn(jitxOrderRelation.getOrderNo());
                update.setStatus(TimeOrderVipStatusEnum.MATCHED.getValue());
                ipVipTimeOrderService.updateOrderByOrderSn(update);
            }
            if(IsForbiddenDeliveryEnum.FORBIDDEN.getCode().equals(orderInfo.getOrderInfo().getIsForbiddenDelivery())){
                ocBOrderHoldService.holdOrUnHoldOrder(orderInfo.getOrderInfo(), OrderHoldReasonEnum.JITX_FORBIDDEN_DELIVERY);
            }
        }

        if (result > 0 && (jitxOrderRelation.getSameWarehouse() == null || !jitxOrderRelation.getSameWarehouse())) {
            /**
             * 单据转单或新增后插入占单任务表
             */
            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(orderInfo.getOrderInfo().getId());
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);

            String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;
//            try {
//                SpecialElasticSearchUtil.indexDocument(indexName, OC_B_ORDER_TYPE_NAME,
//                        orderInfo.getOrderInfo(), orderInfo.getOrderInfo().getId());
//                if (orderInfo.getOrderItemList() != null && orderInfo.getOrderItemList().size() > 0) {
//                    SpecialElasticSearchUtil.indexDocuments(indexName,
//                            OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME,
//                            orderInfo.getOrderItemList(),
//                            "OC_B_ORDER_ID");
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }

            // Redis推送。主要是为了防止ES在存储数据时，会延迟100ms左右。为了保障不重复转单，在Redis进行存储。
            // 在进行查询时，先查询ES，若ES不存在，再查询Redis。
            String redisKey = BllRedisKeyResources.getOmsOrderKey(orderInfo.getOrderInfo().getSourceCode());
            this.getRedisTemplate().opsForValue().set(redisKey, orderInfo.getOrderId(), OMS_ORDER_REDIS_TIMEOUT,
                    TimeUnit.MILLISECONDS);
        }
        return (result > 0);
    }

    private void handleStockOccupy(IpJitxOrderRelation jitxOrderRelation,OcBOrderRelation orderInfo) {
        //jitx仓库与时效订单一致  库存平移
        if (jitxOrderRelation.getSameWarehouse()) {
            if (jitxOrderRelation.getOcBOrderId() != null) {
                ValueHolderV14 v14 = ipJitxOrderService.shiftStockOccupy(jitxOrderRelation.getOcBOrderId(), jitxOrderRelation.getOrderNo());
                if (v14.isOK()) {
                    //库存平移成功 更新发货单为待审核
                    ipJitxOrderService.updateOcBOrder(jitxOrderRelation.getOcBOrderId());
                    orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                            OrderLogTypeEnum.JITX_SHIFT_STOCK_OCCUPY.getKey(), OrderLogTypeEnum.JITX_SHIFT_STOCK_OCCUPY.getName(), "", "", SystemUserResource.getRootUser());

                    //加入审核任务表
                    omsAuditTaskService.createOcBAuditTask(orderInfo.getOrderInfo(), OmsAuditTimeCalculateReason.OCCUPY);
                } else {
                    throw new NDSException("时效订单与JITX仓库一致,库存平移异常:" + SplitMessageUtil.splitErrMsgBySize(v14.getMessage(), SplitMessageUtil.SIZE_500));
                }
            }
        } else {
            //仓库不一致  取消原先占用 重新占库存
            ValueHolderV14 v14 = ipJitxOrderService.cancelStockOccupy(jitxOrderRelation);
            if (v14.isOK()) {
                if (log.isDebugEnabled()) {
                    log.debug("Step85JitxStockOccupy JITX仓库与时效单仓库不一致,取消占用结果信息：{}", v14.getMessage());
                }
//                    ipJitxOrderService.occupyStockAgain(orderInfo.getOrderId());
            } else {
                throw new NDSException("时效订单与JITX仓库不一致,取消占用异常:" + SplitMessageUtil.splitErrMsgBySize(v14.getMessage(), SplitMessageUtil.SIZE_500));
            }
        }
    }

    /**
     * @param standplatOrderRelation OcBOrderRelation对象
     * @param isHistoryOrder         是否为历史单据信息
     * @return OcBOrderRelation
     */
    public OcBOrderRelation convertStandplatOrderToOrder(IpStandplatOrderRelation standplatOrderRelation,
                                                         boolean isHistoryOrder) {
        OcBOrderRelation orderInfo = standplatOrderTransferUtil.standplatOrderToOrder(standplatOrderRelation,
                isHistoryOrder);
        if (isHistoryOrder) {
            // 包含轻供商品，订单仓库字段不给默认仓库
            boolean hasLightGoods = false;
            for (IpBStandplatOrderItemEx item : standplatOrderRelation.getStandPlatOrderItemList()) {
                ProductSku productSku = item.getProdSku();
                if (productSku == null) {
                    continue;
                }
                Integer supplyType =
                        productSku.getPsCProSupplyType() == null ? 0 : productSku.getPsCProSupplyType().intValue();
                if (SupplyTypeEnum.PROXY.getValue().equals(supplyType)
                        || SupplyTypeEnum.CONSIGN.getValue().equals(supplyType)) {
//                    orderInfo.getOrderInfo().setHasLightSupplyProd(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
                    hasLightGoods = true;
                }
                if ("Y".equals(productSku.getIsVirtual())) {
                    orderInfo.getOrderInfo().setIsInvented(1);
                    orderInfo.getOrderInfo().setPriceLabel("Y");
                    orderInfo.getOrderInfo().setOrderType(OrderTypeEnum.DIFFPRICE.getVal());
                }
            }
            if (!hasLightGoods) {
                Long defautId = omsWarehouseRuleService.queryDefaultWarehouse(orderInfo.getOrderInfo().getCpCShopId());
                orderInfo.getOrderInfo().setCpCPhyWarehouseId(defautId);
                if (defautId != null) {
                    CpCPhyWarehouse physicalWarehouseInfo = omsWarehouseRuleService.queryByWarehouseId(defautId);
                    if (physicalWarehouseInfo != null) {
                        orderInfo.getOrderInfo().setCpCPhyWarehouseEname(physicalWarehouseInfo.getEname());
                        orderInfo.getOrderInfo().setCpCPhyWarehouseEcode(physicalWarehouseInfo.getEcode());
                    }
                }
            }
        }
        return orderInfo;
    }

    /**
     * @param alibabaAscpOrderRelation OcBOrderRelation对象
     * @return OcBOrderRelation
     */
    public OcBOrderRelation convertAlibabaAscpOrderToOrder(IpAlibabaAscpOrderRelation alibabaAscpOrderRelation) {
        OcBOrderRelation orderInfo = alibabaAscpOrderTransferUtil.AlibabaAscpOrderToOrder(alibabaAscpOrderRelation);
        return orderInfo;
    }

    /**
     * 查询列表
     *
     * @param standplatOrderRelation
     * @return
     */
    public List<OmsOrderRelation> selectOmsOrderRelation(IpStandplatOrderRelation standplatOrderRelation) {
        // 提取查询条件
        String tid = null;
        List<String> oids = null;
        Long shopId = null;

        if (Objects.nonNull(standplatOrderRelation)) {
            if (Objects.nonNull(standplatOrderRelation.getStandplatOrder())) {
                tid = standplatOrderRelation.getStandplatOrder().getTid();
                shopId = standplatOrderRelation.getStandplatOrder().getCpCShopId();
            }

            if (CollectionUtils.isNotEmpty(standplatOrderRelation.getStandPlatOrderItemList())) {
                oids = standplatOrderRelation.getStandPlatOrderItemList().stream()
                        .filter(i -> Objects.nonNull(i.getOid())).map(IpBStandplatOrderItemEx::getOid).collect(Collectors.toList());
            }
        }

        return selectOmsOrderRelation(tid, shopId, oids);
    }

    /**
     * 通过tid/oids查找已经存在的订单信息
     *
     * @param tid
     * @param oids
     * @return
     */
    public List<OmsOrderRelation> selectOmsOrderRelation(String tid, Long shopId, List<String> oids) {
        List<OmsOrderRelation> relations = null;

        // oid优先查，如果oid为空，则通过tid查
        if (CollectionUtils.isNotEmpty(oids)) {
            relations = selectOmsOrderRelationByOid(oids);
        }

        if ((CollectionUtils.isEmpty(oids) || CollectionUtils.isEmpty(relations)) && StringUtils.isNotEmpty(tid)) {
            relations = selectOmsOrderRelationByTid(tid, shopId);
        }

        if (CollectionUtils.isEmpty(relations)) {
            relations = new ArrayList<>();
        }

        return relations;
    }

    /**
     * 通过oid查询
     *
     * @param oids
     * @return
     */
    public List<OmsOrderRelation> selectOmsOrderRelationByOid(List<String> oids) {
        List<OmsOrderRelation> relations = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(oids)) {
            // 查询出所有的item
            List<OcBOrderItem> allItems = new ArrayList<>();

            oids.forEach(oid -> {
                // Set<Long> itemIds = selectOrderItemIdsByOid(oid);
                List<OcBOrderItem> items = orderItemMapper.selectList(new LambdaQueryWrapper<OcBOrderItem>()
                        .eq(OcBOrderItem::getOoid, oid)
                        .eq(OcBOrderItem::getIsactive, "Y"));

                allItems.addAll(items);
            });

            // 按orderId归类
            Map<Long, List<OcBOrderItem>> itemMap =
                    allItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));

            if (Objects.nonNull(itemMap) && !itemMap.isEmpty()) {
                itemMap.forEach((key, value) -> {
                    OmsOrderRelation relation = new OmsOrderRelation();

                    // 查订单
                    OcBOrder order = orderMapper.selectByID(key);
                    relation.setOcBOrder(order);
                    relation.setOcBOrderItems(value);

                    relations.add(relation);
                });
            }
        }

        return relations;
    }

    /**
     * 通过TID查询
     *
     * @param tid
     * @param shopId
     * @return
     */
    public List<OmsOrderRelation> selectOmsOrderRelationByTid(String tid, Long shopId) {
        // 通过tid查询原单
        List<OcBOrder> ocBOrders = selectOmsOrderRecord(tid, shopId, true);
        List<OmsOrderRelation> omsOrderRelations = new ArrayList<>();

        for (OcBOrder ocBOrder : ocBOrders) {
            OmsOrderRelation omsOrderRelation = new OmsOrderRelation();
            //根据订单查询需要退货的明细
            List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItems(ocBOrder.getId());
//            List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItemByOoid(ocBOrder.getId(), ooid + "");
//            List<OcBOrderItem> orderItemsGift = orderItemMapper.selectOrderItemFullGiftList(ocBOrder.getId());

            omsOrderRelation.setOcBOrder(ocBOrder);
            omsOrderRelation.setOcBOrderItems(ocBOrderItems);
//            //循环明细数据 暂时不考虑换货
//            if (CollectionUtils.isNotEmpty(orderItemsGift)) {
//                Map<String, List<OcBOrderItem>> stringListMap = this.giftHandle(orderItemsGift);
//                List<OmsOrderRelation.OcOrderGifts> gifts = this.getOcOrderGifts(ocBOrderItems, stringListMap);
//                omsOrderRelation.setOcOrderGifts(gifts);
//            }

            omsOrderRelations.add(omsOrderRelation);
        }

        return omsOrderRelations;
    }


    //-------------ming.fz 通用转单 end

    /**
     * 依据SourceCode查询所有订单信息
     *
     * @param sourceCode 平台单号
     * @return List<OcBOrder>
     */
    public List<OcBOrder> selectOmsOrderRecord(String sourceCode, Long shopId, boolean isStanPlat) {
        // 先查询ES，再查询Redis。防止重复转单。因为ES有可能延迟100MS
        //List<Long> ids = ES4Order.findBySourceCodeOrShopId(sourceCode, shopId, isStanPlat);
        List<Long> ids = GSI4Order.getIdListBySourceCode(sourceCode);
        List<OcBOrder> orderList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                OcBOrder order = orderMapper.selectById(id);
                if (order != null) {
                    orderList.add(order);
                }
            }
        } else {
            long id = this.selectOmsOrderFromRedis(sourceCode);
            OcBOrder order = orderMapper.selectById(id);
            if (order != null) {
                orderList.add(order);
            }
            log.warn("OrderId={},###OmsOrderService.selectOmsOrderInfo.FromRedis.ID", id);
        }


        return orderList;
    }
    //-------------ming.fz 通用转单 end

    public void setDefaultValueOrder(OcBOrder order) {
        order.setModifiername(SystemUserResource.ROOT_USER_NAME);
        order.setModifieddate(new Date());
        order.setModifierid(SystemUserResource.ROOT_USER_ID);
    }

    /**
     * 保存订单
     *
     * @param orderInfo      订单对象
     * @param isHistoryOrder 是否为历史单据信息
     * @param operateUser    用户对象
     * @return true - 成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOmsOrderInfoStandplat(OcBOrderRelation orderInfo,
                                             boolean isHistoryOrder, User operateUser,
                                             IpStandplatOrderRelation ipStandplatOrderRelation) {
        if (orderInfo == null) {
            return false;
        }

        if (orderInfo.getOrderInfo() == null) {
            return false;
        }
        // @20200913 地址逗号替换
        //    OmsOrderSaveUtil.getInstance().addressReplaceDot(orderInfo.getOrderInfo());
        OrderAddressConvertUtil.convert(orderInfo.getOrderInfo());
        int result = orderMapper.insert(orderInfo.getOrderInfo());

        if (orderInfo.getOrderItemList() != null) {
            // 快手平台预计发货时间
            Integer platform = orderInfo.getOrderInfo().getPlatform();
            if(platform.equals(PlatFormEnum.KUAISHOU.getCode())) {
                for (OcBOrderItem orderItem : orderInfo.getOrderItemList()) {
                    orderItem.setEstimateConTime(orderInfo.getOrderInfo().getEstimateConTime());
                }
            }

            for (OcBOrderItem orderItem : orderInfo.getOrderItemList()) {
                try {
                    orderItem.setOcBOrderId(orderInfo.getOrderInfo().getId());

                    truncationPtProName(orderItem);
                    orderItemMapper.insert(orderItem);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new NDSException(e);
                }
            }
        }

        // 插入奶卡表数据
        if (CollectionUtils.isNotEmpty(orderInfo.getOrderNaiKaList())) {
            for (OcBOrderNaiKa ocBOrderNaiKa : orderInfo.getOrderNaiKaList()) {
                try {
                    ocBOrderNaiKaMapper.insert(ocBOrderNaiKa);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new NDSException(e);
                }
            }
        }
        if (orderInfo.getOrderPaymentList() != null) {
            for (OcBOrderPayment payment : orderInfo.getOrderPaymentList()) {
                payment.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPaymentMapper.insert(payment);
            }
        }

        List<OcBOrder> ocBOrders = new ArrayList<>();
        ocBOrders.add(orderInfo.getOrderInfo());
        this.orderLinkService.addOrderFinkLogsThread(ocBOrders, BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
        if (isHistoryOrder) {
            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "历史单据新增订单成功 ", "", "", operateUser);
        } else {
            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "新增订单成功 ", "", "", operateUser);
        }

        if (result > 0) {

            // Redis推送。主要是为了防止ES在存储数据时，会延迟100ms左右。为了保障不重复转单，在Redis进行存储。
            // 在进行查询时，先查询ES，若ES不存在，再查询Redis。
            String redisKey = BllRedisKeyResources.getOmsOrderKey(orderInfo.getOrderInfo().getSourceCode());
            this.getRedisTemplate().opsForValue().set(redisKey, orderInfo.getOrderId(), OMS_ORDER_REDIS_TIMEOUT,
                    TimeUnit.MILLISECONDS);
            if (!isHistoryOrder) {
                /**
                 * 单据转单或新增后插入占单任务表
                 */
                OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                toBeConfirmedTask.setOrderId(orderInfo.getOrderInfo().getId());
                toBeConfirmedTask.setCreationdate(new Date());
                toBeConfirmedTask.setStatus(0);
                toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
            }
        }
        return (result > 0);
    }

    private void truncationPtProName(OcBOrderItem orderItem) {
        if (StringUtils.isEmpty(orderItem.getPtProName()) || orderItem.getPtProName().length() <= 100) {
            return;
        }

        orderItem.setPtProName(orderItem.getPtProName().substring(0, 97).concat("..."));
    }


    /**
     * 保存订单
     *
     * @param orderInfo      订单对象
     * @param isHistoryOrder 是否为历史单据信息
     * @param operateUser    用户对象
     * @return true - 成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOmsOrderInfoAlibabaAscp(OcBOrderRelation orderInfo,
                                               boolean isHistoryOrder, User operateUser) {
        if (orderInfo == null) {
            return false;
        }

        if (orderInfo.getOrderInfo() == null) {
            return false;
        }
        int result = orderMapper.insert(orderInfo.getOrderInfo());

        if (orderInfo.getOrderItemList() != null) {
            for (OcBOrderItem orderItem : orderInfo.getOrderItemList()) {
                try {
                    orderItem.setOcBOrderId(orderInfo.getOrderInfo().getId());
                    orderItemMapper.insert(orderItem);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new NDSException(e);
                }
            }
        }
        if (orderInfo.getOrderPaymentList() != null) {
            for (OcBOrderPayment payment : orderInfo.getOrderPaymentList()) {
                payment.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPaymentMapper.insert(payment);
            }
        }

        List<OcBOrder> ocBOrders = new ArrayList<>();
        ocBOrders.add(orderInfo.getOrderInfo());
        this.orderLinkService.addOrderFinkLogsThread(ocBOrders, BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
        if (isHistoryOrder) {
            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "历史单据新增订单成功 ", "", "", operateUser);
        } else {
            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "新增订单成功 ", "", "", operateUser);
        }

        if (result > 0) {

            // Redis推送。主要是为了防止ES在存储数据时，会延迟100ms左右。为了保障不重复转单，在Redis进行存储。
            // 在进行查询时，先查询ES，若ES不存在，再查询Redis。
            String redisKey = BllRedisKeyResources.getOmsOrderKey(orderInfo.getOrderInfo().getSourceCode());
            this.getRedisTemplate().opsForValue().set(redisKey, orderInfo.getOrderId(), OMS_ORDER_REDIS_TIMEOUT,
                    TimeUnit.MILLISECONDS);

            /**
             * 单据转单或新增后插入占单任务表
             */
            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(orderInfo.getOrderInfo().getId());
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
        }
        return (result > 0);
    }

    /**
     * 更新订单审核状态
     *
     * @param orderIds    订单id集合
     * @param auditStatus 审核状态
     */
    public void updateAuditOrderList(List<Long> orderIds, Integer auditStatus) {
        orderMapper.updateAuditOrderList(orderIds, auditStatus);
    }

    /**
     * 更新订占单状态
     *
     * @param orderIds    订单id集合
     * @param occpyStatus 审核状态
     * @param occpyStatus 审核状态
     */
    public void updateOccpyOrderList(List<Long> orderIds, Integer occpyStatus, Integer whereOccupyStatus) {
        orderMapper.updateOccpyOrderList(orderIds, occpyStatus, whereOccupyStatus);
    }

    /**
     * 根据订单idList获得订单订单集合（无状态控制）
     *
     * @param ids
     * @return
     */
    public List<OcBOrder> selectOrderListByIdsList(List<Long> ids) {
        return orderMapper.selectOrderListByIdsList(ids);
    }

    /**
     * 根据订单idList获得订单订单集合(占单状态是占单中7）
     * 20200406有了任务中间表不用打标占单状态了
     *
     * @param ids
     * @return
     */
    public List<OcBOrder> selectOrderListByIdsListOccpy7(List<Long> ids) {
        return orderMapper.selectOrderListByIdsListOccpy7(ids);
    }

    /**
     * 查询待审核数据
     *
     * @param orderId
     * @return OcBOrder
     */
    public OcBOrder selectAuditOrderInfo(Long orderId) {
        return orderMapper.selectAuditOrderInfo(orderId);
    }

    /**
     * 查询配货中数据
     *
     * @param orderId
     * @return OcBOrder
     */
    public OcBOrder selectInDistributionOrder(Long orderId) {
        return orderMapper.selectInDistributionOrder(orderId);
    }

    /**
     * 查询待审核数据
     *
     * @param orderId
     * @return OcBOrder
     */
    public OcBOrder selectUnConfirmedOrderInfo(Long orderId) {
        return orderMapper.selectUnConfirmedOrderInfo(orderId);
    }

    public List<OcBOrder> selectAuditOrderInfoList(List<Long> ids) {
        return orderMapper.selectAuditOrderInfoList(ids);
    }

    /**
     * 查询缺货数据
     *
     * @param orderId
     * @return OcBOrder
     */

    public OcBOrder selectOrderUnStock(Long orderId) {
        return orderMapper.selectOrderUnStock(orderId);
    }

    /**
     * 根据订单ID列表查询未HOLD单的所有订单
     *
     * @param ids
     * @return List<OcBOrder>
     */
    public List<OcBOrder> selectNotHoldOrderList(List<Long> ids) {
        return orderMapper.selectNotHoldOrderList(ids);
    }

    /**
     * 查询待分配数据
     *
     * @param orderId
     * @return OcBOrder
     */
    public OcBOrder selectWaitDistributeInfo(Long orderId) {
        return orderMapper.selectWaitDistributeInfo(orderId);
    }


    /**
     * 查询待分配和缺货数据
     *
     * @param orderId
     * @return OcBOrder
     */
    public OcBOrder selectWaitDistributeInfoAndOutStock(Long orderId) {
        return orderMapper.selectWaitDistributeInfoAndOutStock(orderId);
    }
    /**
     * 查询数据.淘宝预售
     *
     * @param orderId id
     * @return OcBOrder
     */
    public OcBOrder selectTaoBaoPreSaleInfo(Long orderId) {
        return orderMapper.selectTaoBaoPreSaleInfo(orderId);
    }

    /**
     * 查询待确认和待缺货数据
     *
     * @param orderId
     * @return OcBOrder
     */
    public OcBOrder selectOrderUnConfirmAndStockInfo(Long orderId) {
        return orderMapper.selectOrderUnConfirmAndStockInfo(orderId);
    }

    /**
     * 补偿同步结算日志
     */
    public void synOrderSendGoodsLog(Integer countSize) {
        Long startTime = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug("仓库发货批量补偿任务开始执行synOrderSendGoodsLog,执行条数={}", countSize);
        }
        JSONObject whereKey = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(ToACStatusEnum.PENDING.getValue().longValue());
        jsonArray.add(ToACStatusEnum.FAILED.getValue().longValue());
        whereKey.put("TO_SETTLE_STATUS", jsonArray);
        // @20200820 固定店铺数据
        // whereKey.put("CP_C_SHOP_ID", 48);
        JSONArray statusJsonArr = new JSONArray();
        statusJsonArr.add(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
        statusJsonArr.add(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        whereKey.put("ORDER_STATUS", statusJsonArr);
        List orderIdList = ES4Order.queryEsOrderList(whereKey, 0, countSize);
        if (!CollectionUtils.isEmpty(orderIdList)) {
            List<OcBOrder> ocBOrders = orderMapper.selectOrderListByIds(orderIdList);
            if (!CollectionUtils.isEmpty(ocBOrders)) {
                omsOrderItemService.sendBatchSettlementLog(ocBOrders);
            }
        }
        Long endTime = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug("仓库发货批量补偿任务执行结束synOrderSendGoodsLog,执行条数{},执行时间:{}", countSize, (endTime - startTime) / 1000);
        }
    }

    /**
     * 修改订单缺货标记为不缺货
     *
     * @param orderId
     * @param loginUser 用户
     */
    public void updateOrderIsLackstock(Long orderId, User loginUser) {
        try {
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setModifierename(loginUser.getEname());
            ocBOrder.setModifiername(loginUser.getName());
            ocBOrder.setModifieddate(new Date());
            ocBOrder.setModifierid(loginUser.getId().longValue());
            ocBOrder.setId(orderId);
//            ocBOrder.setIsLackstock(OcBOrderConst.IS_STATUS_IN);
            //更新订单主表
            orderMapper.updateById(ocBOrder);
            // 更新子单缺货数量
            List<OcBOrderItem> items = orderItemMapper.selectOrderItemListAndReturn(orderId);
            for (OcBOrderItem orderItem : items) {
                OcBOrderItem item = new OcBOrderItem();
                Long id = orderItem.getId();
                item.setId(id);
                item.setQtyLost(BigDecimal.ZERO);
                item.setOcBOrderId(orderId);
                omsOrderItemService.updateOcBOrderItem(item, orderId);
            }

        } catch (Exception e) {
            log.error("修改订单缺货标记为不缺货异常,异常信息为{}", e);
        }
    }

    /**
     * 查询指定快递且缺货的订单
     *
     * @param nodeName
     * @param tableName
     * @return
     */
    public List<Long> selectOrderListByAppointLogistics(String tableName) {
        return this.orderMapper.selectOrderListByAppointLogistics(tableName);
    }

    private CusRedisTemplate<String, Long> getRedisTemplate() {
        return RedisMasterUtils.getObjRedisTemplate();
    }


    public int updateForceAddress(Integer platform, ReceiverAddressDto addressDto, WarehouseAndLogisticsDto houseDto, User user) {
        UpdateWrapper<OcBOrder> wrapper = new UpdateWrapper<>();
        if (Objects.nonNull(addressDto.getCpCRegionProvinceId())) {
            wrapper.set("CP_C_REGION_PROVINCE_ID", addressDto.getCpCRegionProvinceId());
        }
        if (StringUtils.isNotEmpty(addressDto.getCpCRegionProvinceEcode())) {
            wrapper.set("CP_C_REGION_PROVINCE_ECODE", addressDto.getCpCRegionProvinceEcode());
        }
        if (StringUtils.isNotEmpty(addressDto.getCpCRegionProvinceEname())){
            wrapper.set("CP_C_REGION_PROVINCE_ENAME", addressDto.getCpCRegionProvinceEname());
        }

        if (Objects.nonNull(addressDto.getCpCRegionCityId())){
            wrapper.set("CP_C_REGION_CITY_ID", addressDto.getCpCRegionCityId());
        }
        if (StringUtils.isNotEmpty(addressDto.getCpCRegionCityEcode())){
            wrapper.set("CP_C_REGION_CITY_ECODE", addressDto.getCpCRegionCityEcode());
        }
        if (StringUtils.isNotEmpty(addressDto.getCpCRegionCityEname())){
            wrapper.set("CP_C_REGION_CITY_ENAME", addressDto.getCpCRegionCityEname());
        }
        if (Objects.nonNull(addressDto.getCpCRegionAreaId())){
            wrapper.set("CP_C_REGION_AREA_ID", addressDto.getCpCRegionAreaId());
        }
        if (StringUtils.isNotEmpty(addressDto.getCpCRegionAreaEcode())){
            wrapper.set("CP_C_REGION_AREA_ECODE", addressDto.getCpCRegionAreaEcode());
        }
        if (StringUtils.isNotEmpty(addressDto.getCpCRegionAreaEname())){
            wrapper.set("CP_C_REGION_AREA_ENAME", addressDto.getCpCRegionAreaEname());
        }
        if (StringUtils.isNotEmpty(addressDto.getCpCRegionTownEname())){
            wrapper.set("CP_C_REGION_TOWN_ENAME", addressDto.getCpCRegionTownEname());
        }
        if (StringUtils.isNotEmpty(addressDto.getReceiverAddress())){
            wrapper.set("RECEIVER_ADDRESS", addressDto.getReceiverAddress());
        }
        if (StringUtils.isNotEmpty(addressDto.getPlatformProvince())){
            wrapper.set("PLATFORM_PROVINCE", addressDto.getPlatformProvince());
        }
        if (StringUtils.isNotEmpty(addressDto.getPlatformCity())){
            wrapper.set("PLATFORM_CITY", addressDto.getPlatformCity());
        }
        if (StringUtils.isNotEmpty(addressDto.getPlatformArea())){
            wrapper.set("PLATFORM_AREA", addressDto.getPlatformArea());
        }
        if (StringUtils.isNotEmpty(addressDto.getReceiverName())){
            wrapper.set("RECEIVER_NAME", addressDto.getReceiverName());
        }
        if (StringUtils.isNotEmpty(addressDto.getReceiverMobile())) {
            wrapper.set("RECEIVER_MOBILE", addressDto.getReceiverMobile());
        }
        if (StringUtils.isNotEmpty(addressDto.getReceiverPhone())) {
            wrapper.set("RECEIVER_PHONE", addressDto.getReceiverPhone());
        }
        if (StringUtils.isNotEmpty(addressDto.getReceiverZip())) {
            wrapper.set("RECEIVER_ZIP", addressDto.getReceiverZip());
        }
        if (Objects.nonNull(addressDto.getIsPlainAddr())) {
            wrapper.set("IS_PLAIN_ADDR", addressDto.getIsPlainAddr());
        }
        if (houseDto != null) {
            wrapper.set("CP_C_STORE_ID", houseDto.getCpCStoreId())
                    .set("CP_C_STORE_ECODE", houseDto.getCpCStoreEcode())
                    .set("CP_C_STORE_ENAME", houseDto.getCpCStoreEname())
                    .set("CP_C_PHY_WAREHOUSE_ID", houseDto.getCpCPhyWarehouseId())
                    .set("CP_C_PHY_WAREHOUSE_ECODE", houseDto.getCpCPhyWarehouseEcode())

                    .set("CP_C_PHY_WAREHOUSE_ENAME", houseDto.getCpCPhyWarehouseEname())
                    .set("CP_C_LOGISTICS_ID", houseDto.getCpCLogisticsId())
                    .set("CP_C_LOGISTICS_ECODE", houseDto.getCpCLogisticsEcode())
                    .set("CP_C_LOGISTICS_ENAME", houseDto.getCpCLogisticsEname());
        }
        wrapper.eq("ID", addressDto.getId());
        OcBOrder order = new OcBOrder();
        BaseModelUtil.setupUpdateParam(order, user);
        order.setModifierename(user.getEname());
        //有赞不要oaid
        if (!PlatFormEnum.YOUZAN.getCode().equals(platform)) {
            order.setOaid(addressDto.getOaid());
        }
        return orderMapper.update(order, wrapper);
    }

    /**
     * 未退款明细(用proType in(0,4))
     *
     * @param orderItemList
     * @return
     */
    public List<OcBOrderItem> getUnSuccessRefundAudit(List<OcBOrderItem> orderItemList) {

        //未退款的只要protype(0,4)
        List<OcBOrderItem> norMalAndNoSplitCombineList = orderItemList.stream()
                .filter(e -> Objects.nonNull(e.getProType())
                        && (e.getProType().intValue() == SkuType.NORMAL_PRODUCT
                        || e.getProType().intValue() == SkuType.NO_SPLIT_COMBINE)).collect(Collectors.toList());
        return norMalAndNoSplitCombineList;
    }

    /**
     * 未退款明细(排除 proType <> 4 >)
     *
     * @param orderItemList
     * @return
     */
    public List<OcBOrderItem> getUnSuccessRefundAuditExcludeNoSplitCombine(List<OcBOrderItem> orderItemList) {

        List<OcBOrderItem> norMalAndNoSplitCombineList = orderItemList.stream()
                .filter(e -> Objects.nonNull(e.getProType())
                        && (e.getProType().intValue() != SkuType.NO_SPLIT_COMBINE)).collect(Collectors.toList());
        return norMalAndNoSplitCombineList;
    }

    /**
     * 判断订单 对应平台店铺的店铺渠道是否为：一件代发经销平台
     * @param orderId
     * @param shopId
     * @return
     */
    public Boolean checkOrderIssuing(Long orderId,Long shopId){
        boolean isCheckIssuing = false;
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("订单id为：{},店铺id为：{}",
                    "OmsOrderService.checkOrderIssuing",
                    orderId),orderId,shopId);
        }
        CpShop cpShop = cpRpcService.selectShopById(shopId);
        if(cpShop == null){
            log.error(LogUtil.format("根据订单店铺ID查询平台店铺为空，订单ID:{},店铺ID{}","OmsOrderService.checkOrderIssuing"),
                    orderId,shopId);
        }else{
            String storeChannel = cpShop.getStoreChannel();
            if(storeChannel != null && "21".equals(storeChannel)){
                return true;
            }
        }
        return isCheckIssuing;
    }

    /**
     * 交易关闭时对订单的处理
     *
     * @param findOrderInfoList 若订单 中间表订单关闭状态 并 订单是付定金未付尾款的单子
     *                          判断订单状态   （已审核或配货中或传wms中）调用反审核和取消服务，
     *                          待审核或缺货）直接调用取消服务，（仓库发货或平台发货）不做处理
     */
    public boolean preSaleTransactionClosure(List<OcBOrder> findOrderInfoList, String stepTradeStatus, User operateUser) {
        boolean flag = true;
        if (CollectionUtils.isNotEmpty(findOrderInfoList)) {
            for (OcBOrder ocBOrder : findOrderInfoList) {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        Integer orderStatus = ocBOrder.getOrderStatus();
                        if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equalsIgnoreCase(stepTradeStatus)) {
                            if (OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(orderStatus)
                                    || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus)) {
                                flag = false;
                            }
                            if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)
                                    || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
                                //调用反审核接口
                                boolean isSuccess = omsReturnUtil.toExamineOrder(ocBOrder, operateUser);
                                //反审核成功
                                if (isSuccess) {
                                    orderStatus = ocBOrder.getOrderStatus();
                                } else {
                                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(), "订单编号为"
                                            + ocBOrder.getBillNo() + " 交易关闭,取消订单时反审核失败", null, null, operateUser);
                                }
                            }
                            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                                //调用取消订单
                                //ocBOrder.setIsInreturning(1);
                                ocBOrder.setPlatformStatus(TaoBaoOrderStatus.TRADE_CLOSED);
                                ValueHolderV14 holderV14 = omsOrderCancellationService.doInvoildOutOrder(ocBOrder, operateUser);
                                if (holderV14.getCode() == 0) {
                                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(), "订单编号为"
                                            + ocBOrder.getBillNo() + " 交易关闭,取消订单", null, null, operateUser);
                                } else {
                                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(), "订单编号为"
                                            + ocBOrder.getBillNo() + " 交易关闭,取消订单失败", null, null, operateUser);
                                    flag = false;
                                }
                            }
                        }
                    } else {
                        log.error(this.getClass().getName() + " 订单id" + ocBOrder.getId() + " 尾款未付交易关闭时处理时加锁失败");
                        flag = false;
                    }
                } catch (Exception e) {
                    log.error(this.getClass().getName() + " 订单id" + ocBOrder.getId() + " 预售订单,尾款未付交易关闭时处理失败", e);
                    flag = false;
                } finally {
                    redisLock.unlock();
                }
                if (!flag) {
                    log.error(this.getClass().getName() + " 订单id" + ocBOrder.getId() + " 预售未付尾款交易关闭关闭处理失败");
                    return false;
                }
            }
        }
        return true;
    }


    /**
     * 处理主品退款完成关联的订单明细为平台赠品的逻辑
     */
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private OmsMarkCancelService markCancelService;

    public boolean handlePlatformGift(OcBOrder ocBOrder, String oid, User operateUser) {
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            //查询该订单下的明细数据 判断是否有平台赠品
            List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectAllOrderItemListByOrderIdAndOid(ocBOrder.getId(), oid);
            if (CollectionUtils.isEmpty(ocBOrderItems)) {
                //没有  结束程序
                return true;
            }
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                Integer orderStatus = ocBOrder.getOrderStatus();
                if (OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(orderStatus)
                        || OmsOrderStatus.PENDING_WMS.toInteger() .equals(orderStatus)
                        || OmsOrderStatus.OCCUPY_IN.toInteger() .equals(orderStatus)) {
                    return false;
                }
                if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)
                        ||OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
                    //调用反审核接口
                    boolean isSuccess = ocBOrderTheAuditService.toExamineOrder(ocBOrder, operateUser);
                    //反审核成功
                    if (isSuccess) {
                        orderStatus = ocBOrder.getOrderStatus();
                    } else {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单编号为"
                                + ocBOrder.getBillNo() + " 主品退款完成关联的订单明细为平台赠品反审核失败", null, null, operateUser);
                    }
                }
                if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                        || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                        || OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                        || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) {
                    return true;

                }
                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                        || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                    //调用标记取消服务
                    List<Long> itemIds = ocBOrderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                    ValueHolderV14 holderV14 = markCancelService.markCancel(ocBOrder.getId(), itemIds, operateUser, OrderHoldReasonEnum.REFUND_HOLD, false);

                    if (holderV14.isOK()) {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单编号为"
                                + ocBOrder.getBillNo() + " 主品退款完成关联的订单明细为平台赠品取消成功", null, null, operateUser);
                    } else {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单编号为"
                                + ocBOrder.getBillNo() + " 主品退款完成关联的订单明细为平台赠品取消失败", null, null, operateUser);
                    }
                    return holderV14.isOK();
                }
            } else {
                log.error(this.getClass().getName() + " 订单id" + ocBOrder.getId() + " 主品退款完成关联的订单明细为平台赠品,此订单正在操作");
                return false;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 订单id" + ocBOrder.getId() + " 主品退款完成关联的订单明细为平台赠品处理取消失败!", e);
        } finally {
            redisLock.unlock();
        }
        return false;
    }

    public List<OcBOrder> getOrdersForOaBySourceCodes(List<String> sourceCodes) {
        return orderMapper.getOrdersForOaBySourceCodes(sourceCodes);
    }

    public List<OcBOrderItem> getGiftOrderDetailByTidAndOrderId(List<Map<String, Object>> queryMapList) {
        if (CollectionUtils.isEmpty(queryMapList)) return Collections.emptyList();
        return orderMapper.getGiftOrderDetailByTidAndOrderId(queryMapList);
    }

    public List<OcBOrder> getOrdersForOaRegexSourceCodes(List<String> sourceCodes) {
        if (CollectionUtils.isEmpty(sourceCodes)) return Collections.emptyList();
        return orderMapper.getOrdersForOaRegexSourceCodes(sourceCodes);
    }
}
