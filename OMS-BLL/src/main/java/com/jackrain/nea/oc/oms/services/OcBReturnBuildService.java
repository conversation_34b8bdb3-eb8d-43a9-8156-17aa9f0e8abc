package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCRegion;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.constant.NaiKaTypeConstant;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.IpOrderReturnRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-03-28 14:39
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class OcBReturnBuildService {
    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OmsOrderLogService omsOrderLogService;


    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Autowired
    protected PsRpcService psRpcService;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;

    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;

    /**
     * 参数为对象改为IpTaobaoExchangeRelation
     * IpTaobaoExchangeRelation.getOcBReturnOrder() 退换货表 传退换货订单ID和dispute_id/tid
     * 如果dispute_id为空  传tid
     * <p>
     * IpTaobaoExchangeRelation.getOcBOrder() 参数参考文档
     *
     * <p>文档解释
     * 2.	source_code赋值规则：判断淘宝换货单号字段是否为空
     * 1)	如果为空，则source_code=tid(退单表)
     * 2)	不为空，则source_code= tb_dispute_id（退单表）
     * 3.	订单补充信息生成规则：
     * 1)	判断淘宝换货单号字段是否为空
     * a)	若是为空，则订单补充信息suffix_info字段赋值为：退单编号（ID）-EC
     * b)	若不为空，则订单补充信息suffix_info字段赋值为：退单编号（ID）-TC
     *
     * @param ocBOrderRelationCheck
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 buildExchange(IpOrderReturnRelation ocBOrderRelationCheck, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("buildExchange 接收入参:{}"), JSONObject.toJSONString(ocBOrderRelationCheck));
        }
        ValueHolderV14 result = new ValueHolderV14();
        OrderTypeEnum orderType = ocBOrderRelationCheck.getOrderType();
        boolean isReissue = OrderTypeEnum.REISSUE == orderType;
        Map map;
        if (isReissue) {
            map = new HashMap();
            map.put("errormassage", "");
            map.put("ocBOrderRelation", ocBOrderRelationCheck);
        } else {
            map = checkOrder(ocBOrderRelationCheck, operateUser);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("buildExchange.checkOrder.参数校验后:{}"), JSONObject.toJSONString(map));
        }
        if (StringUtils.isNotEmpty(map.get("errormassage").toString())) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(map.get("errormassage").toString());
            log.debug(LogUtil.format("生成换货订单服务参数校验后:") + map.get("errormassage").toString());
            return result;
        }
        IpOrderReturnRelation ocBOrderRelation = (IpOrderReturnRelation) map.get("ocBOrderRelation");
        try {
            OcBOrder ocBOrder = ocBOrderRelation.getOcBOrder();
            OcBOrderNaiKa ocBOrderNaiKa = ocBOrderRelation.getOrderNaiKa();
            ocBOrder.setId(ModelUtil.getSequence("oc_b_order"));
            ocBOrder.setBillNo(sequenceUtil.buildBillNo());//获取订单号
            Long origOrderId = ocBOrder.getOrigOrderId();
            if (origOrderId != null) {
                OcBOrder order = ocBOrderMapper.selectById(origOrderId);
                if (order != null) {
                    ocBOrder.setGwSourceGroup(order.getGwSourceGroup());
                    ocBOrder.setOrderSourcePlatformEcode(order.getOrderSourcePlatformEcode());
                }
            }
            if (ocBOrderMapper.insert(ocBOrder) > 0) {
                List<OcBOrderNaiKa> ocBOrderNaiKaList = new ArrayList<>();
                //全渠道订单主表插入成功 开始插入子表  明细id每次生成一个 单条插入
                for (OcBOrderItem ocBOrderItem : ocBOrderRelation.getOcBOrderItems()) {
                    //id未自动生成
                    ocBOrderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
                    ocBOrderItem.setOcBOrderId(ocBOrder.getId());//主表的id
                    if (isReissue) {
                        ocBOrderItem.setIsExchangeItem(OmsIsExchangeItemEnum.NO.integer());
                    } else {
                        ocBOrderItem.setIsExchangeItem(OmsIsExchangeItemEnum.YES.integer());
                    }
                    // 奶卡信息补充
                    if(null != ocBOrderNaiKa){
                        buildOrderNaikaList(ocBOrder, ocBOrderItem, ocBOrderNaiKa, ocBOrderNaiKaList);
                    }
                }
                ocBOrderItemMapper.batchInsert(ocBOrderRelation.getOcBOrderItems());

                /**
                 * 奶卡信息
                 */
                if(CollectionUtils.isNotEmpty(ocBOrderNaiKaList)){
                    ocBOrderNaiKaMapper.batchInsert(ocBOrderNaiKaList);
                }

                // hold单
                if (!ocBOrderRelationCheck.isForbidHoldExchangeOrder()) {
                    OcBOrder newOrder = new OcBOrder();
                    newOrder.setId(ocBOrder.getId());
                    newOrder.setIsInterecept(OmsOrderIsInterceptEnum.YES.getVal());
                    ocBOrderHoldService.holdOrUnHoldOrder(newOrder, OrderHoldReasonEnum.ADD_EXCHANGE_ORDER);
                }

                /**
                 * 单据转单或新增后插入占单任务表
                 */
                OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                toBeConfirmedTask.setOrderId(ocBOrder.getId());
                toBeConfirmedTask.setCreationdate(new Date());
                toBeConfirmedTask.setStatus(0);
                toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
            }
            pushEsAndLog(ocBOrderRelation, operateUser);//最后调用日志服务并推送es 不影响主流程
            result.setCode(ResultCode.SUCCESS);
            result.setMessage("服务调用成功");
        } catch (Exception e) {
            String tip = isReissue ? "补寄" : "换货";
            log.error(LogUtil.format("生成" + tip + "订单服务,error:{}"), Throwables.getStackTraceAsString(e));
            result.setCode(ResultCode.FAIL);
            result.setMessage(e.getMessage());
            throw new NDSException(e.getMessage());
        }
        return result;
    }

    public void buildOrderNaikaFromOrigOrder(OcBOrder ocBOrder, IpOrderReturnRelation ocBOrderRelation) {
        // 以下业务类型需要原单奶卡
        OrderBusinessTypeCodeEnum businessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(ocBOrder.getBusinessTypeCode());
        if(businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.CYCLE_PICK_UP
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER_PICK_UP
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.CYCLE_PICK_UP_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_CYCLE_PICK_UP_REISSUE){
            List<OcBOrderNaiKa> ocBOrderNaiKas = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(ocBOrder.getId());
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(ocBOrderNaiKas)){
                OcBOrderNaiKa origOrderNaika = ocBOrderNaiKas.get(0);
                OcBOrderNaiKa orderNaiKa = new OcBOrderNaiKa();
                orderNaiKa.setBusinessTypeCode(origOrderNaika.getBusinessTypeCode());
                orderNaiKa.setBusinessTypeName(origOrderNaika.getBusinessTypeName());
                orderNaiKa.setBusinessTypeId(origOrderNaika.getBusinessTypeId());
                orderNaiKa.setBusinessType(NaiKaTypeConstant.PICK_UP);
                orderNaiKa.setCardCode(origOrderNaika.getCardCode());
                orderNaiKa.setNaikaStatus(origOrderNaika.getNaikaStatus());
                orderNaiKa.setOperateTime(origOrderNaika.getOperateTime());
                orderNaiKa.setWipeAmt(origOrderNaika.getWipeAmt());
                orderNaiKa.setUnfreezeErrorMsg(origOrderNaika.getUnfreezeErrorMsg());
                orderNaiKa.setAccountToNaika(origOrderNaika.getAccountToNaika());
                orderNaiKa.setAccountToNaikaTimes(origOrderNaika.getAccountToNaikaTimes());
                orderNaiKa.setAccountErrorMsg(origOrderNaika.getAccountErrorMsg());
                orderNaiKa.setUpToBillDate(origOrderNaika.getUpToBillDate());
                ocBOrderRelation.setOrderNaiKa(orderNaiKa);
            }
        }
    }

    private void buildOrderNaikaList(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem, OcBOrderNaiKa ocBOrderNaiKa, List<OcBOrderNaiKa> ocBOrderNaiKaList) {
        OcBOrderNaiKa orderNaiKa = new OcBOrderNaiKa();
        BaseModelUtil.initialBaseModelSystemField(orderNaiKa);
        orderNaiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
        orderNaiKa.setOcBOrderId(ocBOrder.getId());
        orderNaiKa.setOcBOrderItemId(ocBOrderItem.getId());
        orderNaiKa.setTid(ocBOrderItem.getTid());
        orderNaiKa.setBusinessType(ocBOrderNaiKa.getBusinessType());
        orderNaiKa.setBusinessTypeCode(ocBOrderNaiKa.getBusinessTypeCode());
        orderNaiKa.setBusinessTypeName(ocBOrderNaiKa.getBusinessTypeName());
        orderNaiKa.setBusinessTypeId(ocBOrderNaiKa.getBusinessTypeId());
        orderNaiKa.setNaikaStatus(ocBOrderNaiKa.getNaikaStatus());
        orderNaiKa.setPsCProEcode(ocBOrderItem.getPsCProEcode());
        orderNaiKa.setPsCProId(ocBOrderItem.getPsCProId());
        orderNaiKa.setPsCProEname(ocBOrderItem.getPsCProEname());
        orderNaiKa.setPsCSkuId(ocBOrderItem.getPsCSkuId());
        orderNaiKa.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
        orderNaiKa.setCardCode(ocBOrderNaiKa.getCardCode());
        orderNaiKa.setPsCSkuEname(ocBOrderItem.getPsCSkuEname());
        orderNaiKa.setSkuSpec(ocBOrderItem.getSkuSpec());
        orderNaiKa.setOperateTime(ocBOrderNaiKa.getOperateTime());
        orderNaiKa.setWipeAmt(ocBOrderNaiKa.getWipeAmt());
        orderNaiKa.setUnfreezeErrorMsg(ocBOrderNaiKa.getUnfreezeErrorMsg());
        orderNaiKa.setAccountToNaika(ocBOrderNaiKa.getAccountToNaika());
        orderNaiKa.setAccountToNaikaTimes(ocBOrderNaiKa.getAccountToNaikaTimes());
        orderNaiKa.setAccountErrorMsg(ocBOrderNaiKa.getAccountErrorMsg());
        orderNaiKa.setUpToBillDate(ocBOrderNaiKa.getUpToBillDate());
        ocBOrderNaiKaList.add(orderNaiKa);
    }

    /**
     * 生成换货订单服务推es 调用日志服务单独独立出来
     *
     * @param ocBOrderRelation
     * @param operateUser
     */
    public void pushEsAndLog(IpOrderReturnRelation ocBOrderRelation, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用日志服务单独独立出来入参") + JSONObject.toJSONString(ocBOrderRelation));
        }
        try {
            //调用订单日志服务
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用日志服务入参") + JSONObject.toJSONString(ocBOrderRelation.getOcBOrder()));
            }

            String logMmessage = "由退换货" + ocBOrderRelation.getOcBOrder().getOrigReturnOrderId() + "新增订单成功";
            String logType = "新增订单";
            omsOrderLogService.addUserOrderLog(ocBOrderRelation.getOcBOrder().getId(), ocBOrderRelation.getOcBOrder().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), logMmessage, "", "", operateUser);
        } catch (Exception e) {
            log.error(LogUtil.format("生成换货订单调用日志服务异常,error:{}"), Throwables.getStackTraceAsString(e));
        }

    }

    /**
     * 生成换货订单服务主表校验
     * 只校验部分参数
     *
     * @param ocBOrderRelation
     * @return
     */
    public Map checkOrder(IpOrderReturnRelation ocBOrderRelation, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("生成换货订单服务校验入参") + JSONObject.toJSONString(ocBOrderRelation));
        }
        StringBuffer result = new StringBuffer();
        Map map = new HashMap();
        OcBOrder ocBOrder = ocBOrderRelation.getOcBOrder();
        if (null == ocBOrder) {
            result.append("订单信息为空");
            map.put("errormassage", result.toString());
            return map;
        }
        if (null == ocBOrderRelation.getOcBReturnOrder()) {
            result.append("退单信息为空");
            map.put("errormassage", result.toString());
            return map;
        }
        if (null == ocBOrderRelation.getOcBReturnOrder().getId()) {
            result.append("退单编号不能为空");
        }
        if (null == ocBOrderRelation.getOcBReturnOrder().getTbDisputeId()) {
            ocBOrder.setSuffixInfo(ocBOrderRelation.getOcBReturnOrder().getId() + "-EC");
        } else {
            ocBOrder.setSuffixInfo(ocBOrderRelation.getOcBReturnOrder().getId() + "-TC");
        }
        //20190805纲上线后修复sourceCode经过确认改为改为退单的平台单号 Tid  不再取换货平台单号
        ocBOrder.setSourceCode(ocBOrderRelation.getOcBReturnOrder().getTid());
        if (StringUtils.isEmpty(ocBOrder.getSourceCode())) {
            result.append("[平台单号信息不能为空]");
        }
        ocBOrder.setMergeSourceCode(ocBOrderRelation.getOcBReturnOrder().getTid());
        if (null == ocBOrder.getCpCShopId()) {
            result.append("[店铺ID不能为空]");
        } else {
            CpShop cpShop = new CpShop();
            try {
                cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
                log.debug(this.getClass().getName() + " 根据店铺id:" + ocBOrder.getCpCShopId() + " 查询销售渠道档案结果:" + cpShop);
                if (null != cpShop) {
                    ocBOrder.setCpCShopSellerNick(cpShop.getSellerNick());
                    ocBOrder.setCpCShopEcode(cpShop.getEcode());
                    ocBOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
                    /*ocBOrder.setCpCStoreId(cpShop.getCpCStoreId());
                    ocBOrder.setCpCStoreEcode(cpShop.getCpCStoreEcode());
                    ocBOrder.setCpCStoreEname(cpShop.getCpCStoreEname());*/
                } else {
                    result.append("[根据店铺id获取不到店铺信息]");
                }
            } catch (Exception e) {
                result.append("[根据店铺id获取店铺信息异常" + e.getMessage() + "]");
            }
        }
        if (null == ocBOrder.getProductAmt()) {
            result.append("[商品总额不能为空]");
        }
        if (null == ocBOrder.getOrderAmt()) {
            result.append("[订单总额不能为空]");
        }
        if (null == ocBOrder.getReceivedAmt()) {
            result.append("[已收金额不能为空]");
        }
        if (null == ocBOrder.getAmtReceive()) {
            result.append("[应收金额不能为空]");
        }
        if (null == ocBOrder.getCpCRegionProvinceId()) {
            result.append("[买家省id不能为空]");
        } else {
            CpCRegion cpCRegion = new CpCRegion();
            try {
                cpCRegion = cpRpcService.queryRegionById(ocBOrder.getCpCRegionProvinceId());
                if (null != cpCRegion) {
                    ocBOrder.setCpCRegionProvinceEcode(cpCRegion.getEcode());
                    ocBOrder.setCpCRegionProvinceEname(cpCRegion.getEname());
                } else {
                    result.append("根据省id获取不到省的信息");
                }
            } catch (Exception e) {
                log.error(LogUtil.format("换货订单服务异常,error:{}"), Throwables.getStackTraceAsString(e));
                result.append("[根据省id获取省信息异常" + e.getMessage() + "]");
            }
        }
        if (null == ocBOrder.getCpCRegionCityId()) {
            result.append("[买家市id不能为空]");
        } else {
            try {
                CpCRegion cpCRegion = cpRpcService.queryRegionById(ocBOrder.getCpCRegionCityId());
                if (null != cpCRegion) {
                    ocBOrder.setCpCRegionCityEcode(cpCRegion.getEcode());
                    ocBOrder.setCpCRegionCityEname(cpCRegion.getEname());
                } else {
                    result.append("[根据市id获取不到市的信息]");
                }
            } catch (Exception e) {
                log.error(LogUtil.format("换货订单服务异常,error:{}"), Throwables.getStackTraceAsString(e));
                result.append("[根据市id获取市的信息异常").append(e.getMessage()).append("]");
            }


        }
        if (null != ocBOrder.getCpCRegionAreaId()) {
            try {
                CpCRegion cpCRegion = cpRpcService.queryRegionById(ocBOrder.getCpCRegionAreaId());
                if (null != cpCRegion) {
                    ocBOrder.setCpCRegionAreaEcode(cpCRegion.getEcode());
                    ocBOrder.setCpCRegionAreaEname(cpCRegion.getEname());
                } else {
                    result.append("[根据区id获取不到区的信息]");
                }
            } catch (Exception e) {
                log.error(LogUtil.format("换货订单服务异常,error:{}"), Throwables.getStackTraceAsString(e));
                result.append("[根据区id获取区的信息异常").append(e.getMessage()).append("]");
            }
        }
        if (null == ocBOrder.getOrigReturnOrderId()) {
            result.append("[原始退货单号不能为空]");
        }
        if (StringUtils.isEmpty(ocBOrder.getSysremark())) {
            ocBOrder.setSysremark("由退换货单" +
                    ocBOrder.getOrigReturnOrderId() + "生成新订单");
        }
        if (StringUtils.isEmpty(ocBOrder.getSellerMemo())) {
            ocBOrder.setSellerMemo("");
        }
        if (null == ocBOrder.getPlatform()) {
            result.append("[平台不能为空]");
        }
        if (null == ocBOrder.getTid()) {
            result.append("[初始平台单号不能为空]");
        }
        //主表不为空校验结束
        //开始主表加默认值
        ocBOrder.setIsExchangeNoIn(1L);//是否是换货未入库 默认是
        ocBOrder.setIsGeninvoiceNotice(0);//是否生成开票通知
        ocBOrder.setPayType(OmsPayType.ON_LINE_PAY.toInteger());//默认1 在线支付
        ocBOrder.setProductDiscountAmt(BigDecimal.ZERO);
        ocBOrder.setReturnStatus(0);
        ocBOrder.setRefundConfirmStatus(0);
        ocBOrder.setAutoAuditStatus(0);
        // ocBOrder.setSysPresaleStatus(0);
        // ocBOrder.setIsHaspresalesku(0);
        ocBOrder.setDouble11PresaleStatus(0);//双十一预售状态

        ocBOrder.setOrderDiscountAmt(BigDecimal.ZERO);
        ocBOrder.setShipAmt(BigDecimal.ZERO);
        ocBOrder.setServiceAmt(BigDecimal.ZERO);
        ocBOrder.setConsignAmt(BigDecimal.ZERO);
        ocBOrder.setConsignShipAmt(BigDecimal.ZERO);
        ocBOrder.setCodAmt(BigDecimal.ZERO);
        // ocBOrder.setOperateAmt(BigDecimal.ZERO);
        ocBOrder.setJdReceiveAmt(BigDecimal.ZERO);
        ocBOrder.setJdSettleAmt(BigDecimal.ZERO);
        ocBOrder.setLogisticsCost(BigDecimal.ZERO);
        ocBOrder.setOrderType(2);//换货
        ocBOrder.setOrderStatus(50);//待分配
        ocBOrder.setOccupyStatus(0);//未占单
        ocBOrder.setOrderDate(new Date());//订单日期  系统时间
        ocBOrder.setPayTime(new Date());//付款时间 系统时间
        ocBOrder.setIsMerge(0);//是否合并订单  默认否
        ocBOrder.setIsSplit(0);//是否拆分订单（默认否）
        ocBOrder.setIsInterecept(0);//是否已经拦截（默认否）
        ocBOrder.setIsInreturning(0);//是否退款中（默认否）
        // ocBOrder.setIsGiveLogistic(0);//是否已给物流（默认否）
        ocBOrder.setWmsCancelStatus(0);//wms撤回状态（默认未撤回）
//        ocBOrder.setIsLackstock(0);//实缺标记（默认否）
        ocBOrder.setIsInvented(0);//是否虚拟订单（默认否）
        ocBOrder.setIsCombination(0);//是否组合订单（默认否）
        ocBOrder.setIsOutUrgency(0);//是否催发货（默认否）
        ocBOrder.setIsHasTicket(0);//是否有工单（默认否）
        ocBOrder.setIsactive("Y");//是否虚拟订单（默认否）
        // ocBOrder.setIsTodrp(0);//是否生成调拨零售 默认否
        //  ocBOrder.setSysPresaleStatus(0);
        //ocBOrder.setIsShopCommission(0);
        BaseModelUtil.initialBaseModelSystemField(ocBOrder, user);
        ocBOrder.setOwnerename(user.getEname());
        //开始子表校验
        BigDecimal goodsSum = BigDecimal.ZERO;//商品总额
        BigDecimal adjustAmtSum = BigDecimal.ZERO;//调整今天之和
        BigDecimal goodsNum = BigDecimal.ZERO;//商品总数量 统计明细数量之和
        int i = 1;
        StringBuffer itemMassage = new StringBuffer();
        List<OcBOrderItem> ocBOrderItems = ocBOrderRelation.getOcBOrderItems();
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
//            if (null == ocBOrderItem.getStoreSite()) {
//                itemMassage.append("第" + i + "条明细库位不能为空");
//            }
            /*if (StringUtils.isEmpty(ocBOrderItem.getBarcode())) {
                itemMassage.append("第" + i + "条明细国标码不能为空");
            }*/
            if (null == ocBOrderItem.getPsCProId()) {
                itemMassage.append("第").append(i).append("条明细商品id不能为空");
            }
            if (StringUtils.isEmpty(ocBOrderItem.getPsCProEcode())) {
                itemMassage.append("第").append(i).append("条明细商品编码不能为空");
            }
            if (StringUtils.isEmpty(ocBOrderItem.getPsCProEname())) {
                itemMassage.append("第").append(i).append("条明细商品名称不能为空");
            }
            if (null == ocBOrderItem.getPsCSkuId()) {
                itemMassage.append("第").append(i).append("条明细条码id不能为空");
                continue;
            }
            if (StringUtils.isEmpty(ocBOrderItem.getPsCProEcode())) {
                itemMassage.append("第").append(i).append("条明细条码编码不能为空");
            }
            if (null == ocBOrderItem.getPriceList()) {
                itemMassage.append("第").append(i).append("条明细标准价不能为空");
            }


            if (null == ocBOrderItem.getPrice()) {//成交价格不作计算 调用者传入
                itemMassage.append("第").append(i).append("条明细成交价格不能为空");
                continue;
            }

            if (null == ocBOrderItem.getQty()) {
                itemMassage.append("第").append(i).append("条明细数量不能为空");
                continue;
            }

            if (null == ocBOrderItem.getTid()) {
                itemMassage.append("第").append(i).append("条明细平台单号不能为空");
            }
            if (null == ocBOrderItem.getAdjustAmt()) {
                itemMassage.append("第").append(i).append("条明细调整金额不能为空");
            }
            List<Integer> list = Lists.newArrayList(ocBOrderItem.getPsCSkuId().intValue());
            ProductSku productSku = new ProductSku();
            try {
                productSku = psRpcService.selectProductById(ocBOrderItem.getPsCSkuId() + "");
                if (null == productSku) {
                    itemMassage.append("第").append(i).append("条根据条码id获取不到商品信息");
                    continue;
                }
                ocBOrderItem.setProType((long) productSku.getSkuType());
                String isEnableExpiry = productSku.getIsEnableExpiry();
                if ("Y".equals(isEnableExpiry)) {
                    ocBOrderItem.setIsEnableExpiry(1);
                } else {
                    ocBOrderItem.setIsEnableExpiry(0);
                }
                // 补充品类信息
                ocBOrderItem.setMDim4Id(productSku.getMDim4Id());
                ocBOrderItem.setMDim6Id(productSku.getMDim6Id());
                ocBOrderItem.setStandardWeight(productSku.getWeight());
            } catch (Exception e) {
                log.error(LogUtil.format("换货订单服务异常,error:{}"), Throwables.getStackTraceAsString(e));
                itemMassage.append("第").append(i).append("条明细调异常+").append(e.getMessage());
                continue;
            }
            if (null == productSku) {
                itemMassage.append("第").append(i).append("条明细根据条码id获取不到规格信息");
                continue;
            }
            if (StringUtils.isNotEmpty(ocBOrderItem.getPsCSkuEcode())) {
                ocBOrderItem.setPsCSkuEcode(StringUtils.upperCase(ocBOrderItem.getPsCSkuEcode()));
            }
            ocBOrderItem.setSkuSpec(productSku.getSkuSpec());
            ocBOrderItem.setPriceTag(productSku.getPricelist());
            ocBOrderItem.setSex(productSku.getSex());
            ocBOrderItem.setAdOrgId((long) user.getOrgId());
            ocBOrderItem.setAdClientId((long) user.getClientId());
            ocBOrderItem.setOwnername(user.getName());
            ocBOrderItem.setOwnerename(user.getEname());
            ocBOrderItem.setOwnerid((long) user.getId());
            ocBOrderItem.setModifiername(user.getName());
            ocBOrderItem.setModifierename(user.getEname());
            ocBOrderItem.setCreationdate(new Date());
            ocBOrderItem.setModifierid((long) user.getId());
            ocBOrderItem.setModifieddate(new Date());

            ocBOrderItem.setPsCClrId(productSku.getColorId());
            ocBOrderItem.setPsCClrEcode(productSku.getColorCode());
            ocBOrderItem.setPsCClrEname(productSku.getColorName());

            ocBOrderItem.setPsCSizeId(productSku.getSizeId());
            ocBOrderItem.setPsCSizeEcode(productSku.getSizeCode());
            ocBOrderItem.setPsCSizeEname(productSku.getSizeName());

            //标准价*数量求和
            // @20200728 空指针问题处理
            if (Objects.isNull(ocBOrderItem.getPriceList())) {
                ocBOrderItem.setPriceList(BigDecimal.ZERO);
            }

            if (Objects.isNull(ocBOrderItem.getQty())) {
                ocBOrderItem.setQty(BigDecimal.ZERO);
            }

            BigDecimal priceQty = Optional.ofNullable(ocBOrderItem.getPrice()).orElse(BigDecimal.ZERO).multiply(ocBOrderItem.getQty());
            goodsSum = goodsSum.add(priceQty);

            // @20200810 bug-20535-【用户】订单289，手工新增换货订单报异常
            // adjustAmtSum = adjustAmtSum.add(ocBOrderItem.getAdjustAmt());
            adjustAmtSum = adjustAmtSum.add(Objects.isNull(ocBOrderItem.getAdjustAmt()) ? BigDecimal.ZERO : ocBOrderItem.getAdjustAmt());

            ocBOrderItem.setRefundStatus(0);
            ocBOrderItem.setOrderSplitAmt(new BigDecimal(0));
            ocBOrderItem.setAmtDiscount(new BigDecimal(0));
            //ocBOrderItem.setAdjustAmt(new BigDecimal("0")); 去除默认值 调用者传入
            //ocBOrderItem.setRealAmt(new BigDecimal("0")); 去除默认值 调用者传入
            ocBOrderItem.setIsAllocatestock(0);
            ocBOrderItem.setIsBuyerRate(0);
            ocBOrderItem.setQtyRefund(new BigDecimal(0));
            ocBOrderItem.setIsPresalesku(0);
            ocBOrderItem.setIsSendout(0);
            ocBOrderItem.setIsLackstock(0);//实缺标记（默认否）
            ocBOrderItem.setRefundStatus(0);//退款状态
            goodsNum = goodsNum.add(ocBOrderItem.getQty());
        }
        ocBOrder.setAdjustAmt(adjustAmtSum);
        ocBOrder.setProductAmt(goodsSum);
        ocBOrder.setQtyAll(goodsNum);
        int count = ocBOrderItemMapper.selectCountForOrder(ocBOrder.getId());
        ocBOrder.setSkuKindQty(new BigDecimal(count));
        if (StringUtils.isNotEmpty(itemMassage)) {
            map.put("errormassage", itemMassage.toString());
        } else {
            if (StringUtils.isNotEmpty(result)) {
                map.put("errormassage", result.toString());
            } else {
                map.put("errormassage", "");
            }
        }
        ocBOrderRelation.setOcBOrder(ocBOrder);
        ocBOrderRelation.setOcBOrderItems(ocBOrderItems);
        map.put("ocBOrderRelation", ocBOrderRelation);
        return map;
    }


}
