package com.jackrain.nea.oc.oms.services.directreport;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCDistributionOrganization;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBDirectReportOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBDirectReportOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBDirectReportOrderStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrder;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrderItem;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.RpcPsService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 直发预占保存
 *
 * <AUTHOR>
 * @since 2024-11-29 11:07
 */
@Slf4j
@Service
public class OcBDirectReportOrderSaveService {
    private static final String MAIN_TABLE = "OC_B_DIRECT_REPORT_ORDER";
    private static final String ITEM_TABLE = "OC_B_DIRECT_REPORT_ORDER_ITEM";


    @Resource
    private OcBDirectReportOrderMapper ocBDirectReportOrderMapper;

    @Resource
    private OcBDirectReportOrderItemMapper ocBDirectReportOrderItemMapper;

    @Resource
    private CpRpcService cpRpcService;

    @Resource
    private RpcPsService rpcPsService;

    /**
     * 创建
     */
    @Transactional(rollbackFor = {Exception.class})
    public ValueHolder create(JSONObject param, User user) {
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject obj = fixColumn.getJSONObject("OC_B_DIRECT_REPORT_ORDER");
        JSONArray array = fixColumn.getJSONArray("OC_B_DIRECT_REPORT_ORDER_ITEM");
        try {
            if (CollectionUtils.isEmpty(array)) {
                return ValueHolderUtils.getFailValueHolder("明细不能为空");
            }
            OcBDirectReportOrder main = JSONObject.parseObject(obj.toJSONString(), OcBDirectReportOrder.class);
            int existsBillNo = ocBDirectReportOrderMapper.selectCount(new QueryWrapper<OcBDirectReportOrder>().lambda()
                    .eq(OcBDirectReportOrder::getBillNo, main.getBillNo())
                    .eq(BaseModel::getIsactive, YesNoEnum.Y.getKey()));
            if (existsBillNo > 0) {
                log.warn(LogUtil.format("直发单号已存在，请勿重复提交：{}",
                        "OcBDirectReportOrderSaveService.create"), main.getBillNo());
                return ValueHolderUtils.getFailValueHolder("直发单号已存在，请勿重复提交");
            }

            CpCDistributionOrganization disrtibutionOrg = cpRpcService.queryDisrtibutionOrgById(main.getCpCDisOrgLv2Id());
            if (Objects.isNull(disrtibutionOrg)) {
                log.warn(LogUtil.format("直发机构不存在，请检查直发机构编码是否正确：{}",
                        "OcBDirectReportOrderSaveService.create"), main.getCpCDisOrgLv2Id());
                return ValueHolderUtils.getFailValueHolder("直发机构不存在，请检查直发机构编码是否正确");
            }
            List<OcBDirectReportOrderItem> items = JSONObject.parseArray(array.toJSONString(), OcBDirectReportOrderItem.class);
            BigDecimal totalQty = items.stream().map(OcBDirectReportOrderItem::getQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            main.setTotalQty(totalQty);
            main.setTotalFulfillQty(BigDecimal.ZERO);
            main.setBillNo(SequenceGenUtil.generateSquence(MAIN_TABLE,
                    new JSONObject(), user.getLocale(), false));
            main.setBillDate(new Date());
            main.setStatus(OcBDirectReportOrderStatusEnum.UN_AUDITED.getValue());
            main.setCpCDisOrgLv2Code(disrtibutionOrg.getEcode());
            main.setCpCDisOrgLv2Name(disrtibutionOrg.getEname());

            if (Objects.nonNull(main.getEstimateConTime())
                    && main.getEstimateConTime().before(new Date())) {
                throw new NDSException("预计发货时间不能早于当前时间");
            }
            if (Objects.nonNull(main.getAutoReleaseTime())
                    && main.getAutoReleaseTime().before(new Date())) {
                throw new NDSException("库存释放时间不能早于当前时间");
            }

            main.setId(ModelUtil.getSequence("OC_B_DIRECT_REPORT_ORDER"));
            OmsModelUtil.setDefault4Add(main, user);

            createItem(items, main.getId(), user);

            main.setTotWeight(items.stream()
                    .map(OcBDirectReportOrderItem::getTotWeight)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            ocBDirectReportOrderMapper.insert(main);

            return ValueHolderUtils.getSuccessValueHolder(main.getId(), MAIN_TABLE);
        } catch (Exception e) {
            log.warn(LogUtil.format("直发预占创建失败，异常信息：{}",
                    "OcBDirectReportOrderSaveService.create"), Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.getFailValueHolder("直发预占创建失败:" + e.getMessage());
        }
    }

    /**
     * 修改-加锁
     * 主表字段有before/after，新增明细时有新的值，删除明细有对应的接口
     */
    @Transactional(rollbackFor = {Exception.class})
    public ValueHolder modify(Long objId, JSONObject param, User user) {
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(BllRedisKeyResources.buildDirectReportOptLockKey(objId));
        try {
            if (redisLock.tryLock(1, TimeUnit.MINUTES)) {
                return doModify(objId, param, user);
            } else {
                throw new NDSException("请勿同时操作，建议稍后再试");
            }
        } catch (InterruptedException e) {
            log.warn(LogUtil.format("直发预占加锁失败，请联系值班人员,异常信息:{}",
                    "DirectReportOptLockKey.error"), Throwables.getStackTraceAsString(e));
            throw new NDSException("加锁失败，请联系值班人员");
        } finally {
            redisLock.unlock();
        }
    }


    /**
     * 执行修改
     */
    private ValueHolder doModify(Long objId, JSONObject param, User user) {
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject obj = fixColumn.getJSONObject(MAIN_TABLE);
        try {
            OcBDirectReportOrder exists = ocBDirectReportOrderMapper.selectById(objId);
            if (Objects.isNull(exists)) {
                log.info(LogUtil.format("直发预占不存在，请检查直发预占ID是否正确：{}",
                        "OcBDirectReportOrderSaveService.modify"), objId);
                return ValueHolderUtils.getFailValueHolder("直发预占不存在");
            }

            if (YesNoEnum.N.getKey().equals(exists.getIsactive())) {
                throw new NDSException("未找到有效记录");
            }

            if (!OcBDirectReportOrderStatusEnum.UN_AUDITED.getValue().equals(exists.getStatus())) {
                log.info(LogUtil.format("当前只支持修改未审核的单据：{}",
                        "OcBDirectReportOrderSaveService.modify"), objId);
                return ValueHolderUtils.getFailValueHolder("当前只支持修改未审核的单据");
            }

            JSONArray array = fixColumn.getJSONArray(ITEM_TABLE);
            /*明细是否被修改*/
            boolean isItemMod = false;
            if (CollectionUtils.isNotEmpty(array)) {
                List<OcBDirectReportOrderItem> items = JSONObject.parseArray(array.toJSONString(), OcBDirectReportOrderItem.class);
                isItemMod = modifyItem(exists, items, user);
            }

            /*明细被修改，需要重新查询明细用于计算总数量和总重量*/
            modifyMainInfo(param, user, obj, exists, isItemMod);
        } catch (Exception e) {
            log.warn(LogUtil.format("直发预占保存失败，异常信息：{}",
                    "OcBDirectReportOrderSaveService.modify"), Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.getFailValueHolder("直发预占保存失败:" + e.getMessage());
        }

        return ValueHolderUtils.getSuccessValueHolder(objId, MAIN_TABLE);
    }

    private boolean modifyItem(OcBDirectReportOrder exists, List<OcBDirectReportOrderItem> items, User user) {
        if (CollectionUtils.isEmpty(items)) {
            return false;
        }
        List<OcBDirectReportOrderItem> newItems = ListUtils.emptyIfNull(items).stream()
                .filter(i -> Objects.isNull(i.getId())
                        || i.getId() < 0).collect(Collectors.toList());
        /*新增的明细*/
        if (CollectionUtils.isNotEmpty(newItems)) {
            createItem(newItems, exists.getId(), user);
        }

        /*修改的明细*/
        List<OcBDirectReportOrderItem> updateItemList = ListUtils.emptyIfNull(items).stream()
                .filter(i -> Objects.nonNull(i.getId())
                        && i.getId() > 0).collect(Collectors.toList());
        List<Long> updateItemIds = updateItemList.stream()
                .map(OcBDirectReportOrderItem::getId).collect(Collectors.toList());
        List<OcBDirectReportOrderItem> existsItemList = ocBDirectReportOrderItemMapper.selectList(new QueryWrapper<OcBDirectReportOrderItem>().lambda()
                .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                .eq(OcBDirectReportOrderItem::getOcBDirectReportOrderId, exists.getId())
                .in(OcBDirectReportOrderItem::getId, updateItemIds));
        Map<Long, OcBDirectReportOrderItem> existsItemMap = ListUtils.emptyIfNull(existsItemList).stream()
                .collect(Collectors.toMap(OcBDirectReportOrderItem::getId, Function.identity()));
        for (OcBDirectReportOrderItem update : updateItemList) {
            OcBDirectReportOrderItem existsItem = existsItemMap.get(update.getId());
            if (Objects.isNull(existsItem)) {
                log.warn(LogUtil.format("直发预占明细不存在，请检查明细ID是否正确：{}",
                        "OcBDirectReportOrderSaveService.modify"), update.getId());
                throw new NDSException("直发预占明细不存在:" + update.getId());
            }
            if (Objects.nonNull(update.getQty())) {
                if (BigDecimal.ZERO.compareTo(update.getQty()) >= 0) {
                    throw new NDSException("明细行数量必须大于0");
                }

                existsItem.setQty(update.getQty());
                existsItem.setTotWeight(update.getQty()
                        .multiply(Optional.ofNullable(existsItem.getWeight()).orElse(BigDecimal.ZERO)));
            }
            if (Objects.nonNull(update.getStartProduceDate())) {
                existsItem.setStartProduceDate(update.getStartProduceDate());
            }
            if (Objects.nonNull(update.getEndProduceDate())) {
                existsItem.setEndProduceDate(update.getEndProduceDate());
            }

            if (existsItem.getStartProduceDate().after(existsItem.getEndProduceDate())) {
                throw new NDSException("开始生产时间必须在结束生产时间之前");
            }

            OmsModelUtil.setDefault4Upd(existsItem, user);
            ocBDirectReportOrderItemMapper.updateById(existsItem);
        }

        return true;
    }

    /**
     * 修改主表信息
     */
    private void modifyMainInfo(JSONObject param, User user, JSONObject obj, OcBDirectReportOrder exists, boolean isItemMod) {
        if (isItemMod) {
            List<OcBDirectReportOrderItem> orderItems =
                    ocBDirectReportOrderItemMapper.selectList(new QueryWrapper<OcBDirectReportOrderItem>().lambda()
                            .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y)
                            .eq(OcBDirectReportOrderItem::getOcBDirectReportOrderId, exists.getId()));
            BigDecimal totalQty = ListUtils.emptyIfNull(orderItems).stream()
                    .map(OcBDirectReportOrderItem::getQty).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            exists.setTotalQty(totalQty);
            exists.setTotWeight(orderItems.stream()
                    .map(OcBDirectReportOrderItem::getTotWeight)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        JSONObject aftervalue = param.getJSONObject("aftervalue");
        JSONObject jsonObject = Objects.isNull(aftervalue) ? null : aftervalue.getJSONObject("OC_B_DIRECT_REPORT_ORDER");
        if (Objects.isNull(jsonObject)) {
            if (isItemMod) {
                OmsModelUtil.setDefault4Upd(exists, user);
                ocBDirectReportOrderMapper.updateById(exists);
            }
            return;
        }

        Set<String> modifyKey = aftervalue.getJSONObject(MAIN_TABLE).keySet();
        if (CollectionUtils.isNotEmpty(modifyKey)) {
            OcBDirectReportOrder main = JSONObject.parseObject(obj.toJSONString(), OcBDirectReportOrder.class);
            for (String key : modifyKey) {
                if ("CP_C_SHOP_ID".equals(key)) {
                    exists.setCpCShopId(main.getCpCShopId());
                }
                if ("CP_C_DIS_ORG_LV2_ID".equals(key)) {
                    CpCDistributionOrganization distributionOrg = cpRpcService.queryDisrtibutionOrgById(main.getCpCDisOrgLv2Id());
                    if (Objects.isNull(distributionOrg)) {
                        log.warn(LogUtil.format("直发机构不存在，请检查直发机构编码是否正确：{}",
                                "OcBDirectReportOrderSaveService.modify"), main.getCpCDisOrgLv2Id());
                        throw new NDSException("直发机构不存在，请检查直发机构编码是否正确");
                    }
                    exists.setCpCDisOrgLv2Id(main.getCpCDisOrgLv2Id());
                    exists.setCpCDisOrgLv2Code(distributionOrg.getEcode());
                    exists.setCpCDisOrgLv2Name(distributionOrg.getEname());
                }
                if ("SG_C_SA_STORE_ID".equals(key)) {
                    exists.setSgCSaStoreId(main.getSgCSaStoreId());
                }
                if ("CP_C_STORE_ID".equals(key)) {
                    exists.setCpCStoreId(main.getCpCStoreId());
                }
                if ("ESTIMATE_CON_TIME".equals(key)) {
                    exists.setEstimateConTime(main.getEstimateConTime());
                }
                if ("AUTO_RELEASE_TIME".equals(key)) {
                    exists.setAutoReleaseTime(main.getAutoReleaseTime());
                }
                if ("REMARK".equals(key)) {
                    exists.setRemark(StringUtils.isEmpty(main.getRemark()) ? "-" : main.getRemark());
                }
            }
        }

        if (Objects.nonNull(exists.getEstimateConTime())
                && exists.getEstimateConTime().before(new Date())) {
            throw new NDSException("预计发货时间不能早于当前时间");
        }
        if (Objects.nonNull(exists.getAutoReleaseTime())
                && exists.getAutoReleaseTime().before(new Date())) {
            throw new NDSException("存释放时间不能早于当前时间");
        }
        OmsModelUtil.setDefault4Upd(exists, user);
        ocBDirectReportOrderMapper.updateById(exists);
    }

    /**
     * 新增明细
     */
    private void createItem(List<OcBDirectReportOrderItem> items, Long mainId, User user) {
        Set<Long> psCSkuIds = items.stream().map(OcBDirectReportOrderItem::getPsCSkuId).collect(Collectors.toSet());
        Map<Long, PsCSku> skuMap = rpcPsService.querySkuByIds(psCSkuIds);
        for (OcBDirectReportOrderItem item : items) {
            if ((Objects.isNull(item.getStartProduceDate()) && Objects.nonNull(item.getEndProduceDate()))
                    || (Objects.nonNull(item.getStartProduceDate()) && Objects.isNull(item.getEndProduceDate())
                    || (item.getStartProduceDate().after(item.getEndProduceDate())))) {
                throw new NDSException("开始与结束日期不能只有一个值且开始日期不能在结束日期之前");
            }
            if (BigDecimal.ZERO.compareTo(item.getQty()) >= 0) {
                throw new NDSException("明细行数量必须大于0");
            }
            PsCSku psCSku = skuMap.get(item.getPsCSkuId());
            if (Objects.isNull(psCSku)) {
                throw new NDSException("SKU不存在" + item.getPsCSkuId());
            }
            item.setPsCProEname(psCSku.getPsCProEname());
            item.setPsCSkuEcode(psCSku.getEcode());
            item.setWeight(psCSku.getWeight());
            item.setTotWeight(item.getQty()
                    .multiply(Optional.ofNullable(item.getWeight()).orElse(BigDecimal.ZERO)));

            item.setOcBDirectReportOrderId(mainId);
            item.setId(ModelUtil.getSequence(ITEM_TABLE));
            OmsModelUtil.setDefault4Add(item, user);
        }

        ocBDirectReportOrderItemMapper.batchInsert(items);
    }
}
