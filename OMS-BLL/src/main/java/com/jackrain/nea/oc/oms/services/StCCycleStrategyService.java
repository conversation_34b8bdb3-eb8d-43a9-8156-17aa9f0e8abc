package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.model.StCCycleStrategyRelation;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @ClassName StCCycleStrategyService
 * @Description 周期购促销
 * <AUTHOR>
 * @Date 2024/8/19 11:42
 * @Version 1.0
 */
public interface StCCycleStrategyService {

    /**
     * 新增周期购促销策略
     *
     * @param strategyRelation 周期购促销
     * @return
     * @throws NDSException
     */
    ValueHolderV14<Long> save(StCCycleStrategyRelation strategyRelation, User user) throws NDSException;

    /**
     * 修改周期购促销策略
     *
     * @param strategyRelation
     * @return
     * @throws NDSException
     */
    ValueHolderV14<Void> update(StCCycleStrategyRelation strategyRelation, JSONObject before, JSONObject after, User user, Long id) throws NDSException;
}
