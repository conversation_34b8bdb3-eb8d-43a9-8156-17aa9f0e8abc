package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderSourceRelationMapper;
import com.jackrain.nea.oc.oms.mapper.StCHoldOrderReasonMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBOrderSourceRelationTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderSourceRelation;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import com.jackrain.nea.oc.oms.model.table.StCSplitBeforeSourceStrategyCategoryItem;
import com.jackrain.nea.oc.oms.model.table.StCSplitBeforeSourceStrategySkuItem;
import com.jackrain.nea.oc.oms.model.table.StCSplitBeforeSourceStrategyWeightItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.spiltorder.OmsSplitWeightService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.api.PsCProdimItemQueryCmd;
import com.jackrain.nea.ps.api.request.PsCProdimItemRequest;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.StCSplitBeforeSourceStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AssertUtils;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SplitBeforeSourcingStService {

    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private StCSplitBeforeSourceStrategyService stSplitBeforeSourceStrategyService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OcBOrderMapper ocOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocOrderItemMapper;
    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OcBLowTemperatureOccupyTaskService lowTemperatureOccupyTaskService;
    @Autowired
    private SplitBeforeSourcingStService splitBeforeSourcingStService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OrderAmountUtil orderAmountUtil;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private OmsSplitWeightService omsSplitWeightService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;
    @Autowired
    private BoxStrategyService boxStrategyService;
    @Autowired
    private OcBOrderSourceRelationMapper sourceRelationMapper;
    @Autowired
    private AppointExpressStrategyService appointExpressStrategyService;
    @Autowired
    private StCHoldOrderReasonMapper stCHoldOrderReasonMapper;
    @Autowired
    private CycleBuyInfoService cycleBuyInfoService;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    @DubboReference(version = "1.0", group = "ps")
    private PsCProdimItemQueryCmd cProdimItemQueryCmd;

    private static final String OC_B_ORDER_EXPRESS_STARTING_WEIGHT = "business_system:oc_b_order_express_starting_weight";

    /**
     * @param orderParam 订单信息
     * @param user
     */
    public void splitBeforeSourcingStrategy(OcBOrderParam orderParam, User user, List<Long> cardByGiftNotSplit, List<Long> cardByGiftRelation, boolean isDelay, boolean canAddTemperature) {
        log.info(LogUtil.format("SplitBeforeSourcingStService.splitBeforeSourcingStrategy orderParam:{},cardByGiftNotSplit:{},cardByGiftRelation:{}",
                "SplitBeforeSourcingStService.splitBeforeSourcingStrategy"),
                JSONObject.toJSONString(orderParam), JSONObject.toJSONString(cardByGiftNotSplit), JSONObject.toJSONString(cardByGiftRelation));
        //快运起始重量(KG)
        BigDecimal expressStartingWeight = getExpressStartingWeight();
        log.info("SplitBeforeSourcingStService.splitBeforeSourcingStrategy.expressStartingWeight={}", expressStartingWeight);
        OcBOrder ocOrder = orderParam.getOcBOrder();
        List<OcBOrderItem> orderItems = orderParam.getOrderItemList();
        List<OcBOrderItem> orderItemList = orderItems.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        Integer platform = ocOrder.getPlatform();
        //快运和业务类型判断是否拆单
        if (checkExpressAndBusiness(expressStartingWeight, ocOrder, orderItemList, user)) {
            matchAppointExpress(ocOrder, orderItemList);
            this.insertOccupy(ocOrder, orderItemList, isDelay, canAddTemperature);
            return;
        }
        if (PlatFormEnum.VIP_JITX.getCode().equals(platform)) {
            matchAppointExpress(ocOrder, orderItemList);
            this.addOccupy(ocOrder, isDelay);
            return;
        }
        //获取策略
        Long splitBeforeSourceStrategyId =
                stSplitBeforeSourceStrategyService.getRecentlySplitBeforeSourceStrategyId();
        if (splitBeforeSourceStrategyId == null) {
            omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                    OrderLogTypeEnum.STRATEGY_SPLIT.getKey(), "未匹配到寻源前拆单策略", "", "", user);
            this.addOccupy(ocOrder, isDelay);
            return;
        }
        Collection<List<OcBOrderItem>> splitOrderItemsPool = null;
        Collection<List<OcBOrderItem>> splitOrderItemsByCategory = new ArrayList<>();
        Collection<List<OcBOrderItem>> splitOrderItemsByWeight = new ArrayList<>();

        List<StCSplitBeforeSourceStrategyCategoryItem> categoryStrategyItems =
                stSplitBeforeSourceStrategyService.querySplitBeforeSourceStrategyItems(splitBeforeSourceStrategyId,
                        "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_CATEGORY_ITEM");
        List<StCSplitBeforeSourceStrategyWeightItem> weightStrategyItems =
                stSplitBeforeSourceStrategyService.querySplitBeforeSourceStrategyItems(splitBeforeSourceStrategyId,
                        "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_WEIGHT_ITEM");
        List<StCSplitBeforeSourceStrategySkuItem> skuStrategyItems =
                stSplitBeforeSourceStrategyService.querySplitBeforeSourceStrategyItems(splitBeforeSourceStrategyId,
                        "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_SKU_ITEM");
        //品类拆单策略明细
        Map<Long, StCSplitBeforeSourceStrategyCategoryItem> categoryStrategyMap = categoryStrategyItems.stream()
                .collect(Collectors.toMap(StCSplitBeforeSourceStrategyCategoryItem::getPsCProdimItemId,
                        Function.identity()));
        //重量拆单策略明细
        Map<Long, BigDecimal> weightStrategyMap = weightStrategyItems.stream()
                .collect(Collectors.toMap(StCSplitBeforeSourceStrategyWeightItem::getPsCProdimItemId,
                        StCSplitBeforeSourceStrategyWeightItem::getMaxWeight));
        //条码拆单策略明细
        Map<Long, BigDecimal> skuStrategyMap = skuStrategyItems.stream()
                .collect(Collectors.toMap(StCSplitBeforeSourceStrategySkuItem::getPsCSkuId,
                        StCSplitBeforeSourceStrategySkuItem::getMaxQty));

        StringBuilder strategyLog = new StringBuilder();
        if (CollectionUtils.isNotEmpty(skuStrategyItems)) {
            splitOrderItemsPool = splitStrategyBySku(orderItemList, skuStrategyMap, strategyLog);
            log.info(LogUtil.format("SplitBeforeSourcingStService.splitBeforeSourcingStrategy orderId:{},splitOrderByItem:{}",
                    "SplitBeforeSourcingStService.splitBeforeSourcingStrategy"),
                    ocOrder.getId(), JSONObject.toJSONString(splitOrderItemsPool));
        }
        if (CollectionUtils.isNotEmpty(categoryStrategyItems)) {
            if (strategyLog.toString().endsWith("/")){
                strategyLog.replace(strategyLog.length() -1, strategyLog.length(), "");
            }
            if (CollectionUtils.isNotEmpty(splitOrderItemsPool)) {
                for (List<OcBOrderItem> splitOrderItem : splitOrderItemsPool) {
                    Collection<List<OcBOrderItem>> splitStrategyByCategory = splitStrategyByCategory(splitOrderItem,
                            categoryStrategyMap, strategyLog);
                    splitOrderItemsByCategory.addAll(splitStrategyByCategory);
                }
                if (CollectionUtils.isNotEmpty(splitOrderItemsByCategory)) {
                    splitOrderItemsPool = splitOrderItemsByCategory;
                }
            } else {
                splitOrderItemsPool = splitStrategyByCategory(orderItemList, categoryStrategyMap, strategyLog);
            }
            log.info(LogUtil.format("SplitBeforeSourcingStService.splitBeforeSourcingStrategy orderId:{},splitOrderByDim:{}",
                    "SplitBeforeSourcingStService.splitBeforeSourcingStrategy"),
                    ocOrder.getId(), JSONObject.toJSONString(splitOrderItemsPool));
        }
        if (CollectionUtils.isNotEmpty(weightStrategyItems)) {
            if (CollectionUtils.isNotEmpty(splitOrderItemsPool)) {
                for (List<OcBOrderItem> splitOrderItem : splitOrderItemsPool) {
                    List<List<OcBOrderItem>> lists = omsSplitWeightService.splitWeightService(splitOrderItem, weightStrategyMap);
                    if (CollectionUtils.isNotEmpty(lists)) {
                        splitOrderItemsByWeight.addAll(lists);
                    }
                }
                if (CollectionUtils.isNotEmpty(splitOrderItemsByWeight)) {
                    splitOrderItemsPool = splitOrderItemsByWeight;
                }
            } else {
                splitOrderItemsPool = omsSplitWeightService.splitWeightService(orderItemList, weightStrategyMap);
            }
        }
        log.info(LogUtil.format("SplitBeforeSourcingStService.splitBeforeSourcingStrategy orderId:{},splitOrdeByWeight:{}",
                "SplitBeforeSourcingStService.splitBeforeSourcingStrategy"),
                ocOrder.getId(), JSONObject.toJSONString(splitOrderItemsPool));

        Collection<List<OcBOrderItem>> addSplitOrderItemsPool = new ArrayList<>();
        String boxStrategyLogMessage = "";
        StCShopStrategyDO stCShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(ocOrder.getCpCShopId());
        // 走一遍箱型拆单
        if (CollectionUtils.isNotEmpty(splitOrderItemsPool)) {
            if (StringUtils.isNotEmpty(stCShopStrategyDO.getIsBoxSplit()) && ObjectUtil.equal("1", stCShopStrategyDO.getIsBoxSplit())) {
                for (List<OcBOrderItem> ocBOrderItemList : splitOrderItemsPool) {
                    Collection<List<OcBOrderItem>> addOrderItemList = new ArrayList<>();
                    // 针对ocBOrderItemList 查询是否有命中到策略
                    boxStrategyLogMessage = boxStrategyService.boxStrategyMatch(addOrderItemList, ocBOrderItemList, boxStrategyLogMessage);
                    addSplitOrderItemsPool.addAll(addOrderItemList);
                }
            } else {
                addSplitOrderItemsPool.addAll(splitOrderItemsPool);
            }
        }

        String strategyLogStr = "";
        if (strategyLog.length() > 0) {
            strategyLogStr = " 命中规则：" + strategyLog;
        }
        log.info("寻源前拆单日志命中规则：" + strategyLogStr);
        if (CollectionUtils.isNotEmpty(addSplitOrderItemsPool) && addSplitOrderItemsPool.size() > 1) {
            String logMessage = "订单寻源前策略拆单作废" + strategyLogStr;
            if (StringUtils.isNotEmpty(boxStrategyLogMessage) && CollectionUtils.isNotEmpty(splitOrderItemsPool) && splitOrderItemsPool.size() > 1) {
                logMessage = "订单寻源前策略拆单" + strategyLogStr + "&箱型拆单均匹配到 原单作废" + "," + boxStrategyLogMessage;
            }
            if (StringUtils.isNotEmpty(boxStrategyLogMessage) && (CollectionUtils.isEmpty(splitOrderItemsPool) || splitOrderItemsPool.size() <= 1)) {
                logMessage = boxStrategyLogMessage;
            }
            splitBeforeSourcingStService.insertNewOrders(addSplitOrderItemsPool, ocOrder, orderItems, user, null,
                    logMessage, cardByGiftNotSplit, cardByGiftRelation, isDelay, Lists.newArrayList(), Maps.newHashMap());
        } else {
            omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                    OrderLogTypeEnum.STRATEGY_SPLIT.getKey(), "订单明细未匹配到拆单策略", null, null, user);
            matchAppointExpress(ocOrder, orderItemList);
            insertOccupy(ocOrder, orderItemList, isDelay, canAddTemperature);
        }
    }

    private void insertOccupy(OcBOrder ocOrder, List<OcBOrderItem> orderItemList, boolean isDelay, boolean canAddTemperature) {
        OcBOrder order = new OcBOrder();
        order.setId(ocOrder.getId());
        order.setSysremark("");
        order.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        ocOrderMapper.updateById(order);
        String businessTyCode = ocOrder.getBusinessTypeCode();
        String naiKaType = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(businessTyCode).getNaiKaType();
        boolean isCycle = ocOrder.getIsCycle() != null && ocOrder.getIsCycle() == 1;
        boolean isPickUp = "pickup".equals(naiKaType);
        // 判断商品的一级类目是否有包含低温白奶
        // 获取ocBOrderItems里面的商品编码
        boolean containsLowTemperature = false;
        List<String> skuCodeList = orderItemList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
        if (!isCycle) {
            for (String skuCode : skuCodeList) {
                ProductSku productSku = psRpcService.selectProductSku(skuCode);
                //productSku 中的proAttributeMap 中M_DIM4_ID的ename为"低温白奶"
                if (productSku != null && productSku.getProAttributeMap() != null && productSku.getProAttributeMap().get("M_DIM4_ID") != null) {
                    String ename = productSku.getProAttributeMap().get("M_DIM4_ID").getEname();
                    if ("低温白奶".equals(ename)) {
                        containsLowTemperature = true;
                        break;
                    }
                }
            }
        }
        if (!canAddTemperature) {
            containsLowTemperature = false;
        }
        if (!containsLowTemperature) {
            this.addOccupy(ocOrder, isDelay);
            return;
        }
        // 走到这里的都是低温的
        // 低温&散单(包含周期购)
        if (containsLowTemperature && !isPickUp) {
            List<StCHoldOrderReason> stCHoldOrderReasonList = stCHoldOrderReasonMapper.selectByTypeAndReason(1, "低温白奶散单卡单");
            if (CollectionUtils.isNotEmpty(stCHoldOrderReasonList)) {
                StCHoldOrderReason stCHoldOrderReason = stCHoldOrderReasonList.get(0);
                order.setDetentionReason(stCHoldOrderReason.getReason());
                order.setDetentionReasonId(Math.toIntExact(stCHoldOrderReason.getId()));
                order.setIsDetention(1);
            } else {
                order.setDetentionReason("低温白奶散单卡单");
            }
            order.setModifieddate(new Date());
            ocOrderMapper.updateById(order);
            return;
        }
        // 走到这里的都是低温&提奶的
        Date detentionReleaseDate = new Date();
        omsOccupyTaskService.addOcBOccupyTaskNew(ocOrder, detentionReleaseDate, orderItemList.size(), containsLowTemperature, isPickUp);
    }

    // 匹配指定快递
    private void matchAppointExpress(OcBOrder ocOrder, List<OcBOrderItem> orderItemList) {
        //toc残次订单跳过
        if (OmsOrderUtil.isToCCcOrder(ocOrder)){
            return;
        }

        for (OcBOrderItem ocBOrderItem : orderItemList) {
            if (ocOrder.getAppointLogisticsId() != null) {
                continue;
            }
            appointExpressStrategyService.getAppointExpressStrategyRelation(ocOrder, ocBOrderItem);
        }
    }

    /**
     * 快运和业务类型判断是否拆单
     *
     * @param expressStartingWeight 快运起始重量
     * @param ocOrder               订单
     * @param orderItemList         订单明细
     * @return boolean
     */
    private boolean checkExpressAndBusiness(BigDecimal expressStartingWeight, OcBOrder ocOrder,
                                            List<OcBOrderItem> orderItemList, User user) {
        //快运起始重量(KG) 大于0 判断订单重量是否满足
        boolean isExpress = false;
        if (expressStartingWeight.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal orderWeight = BigDecimal.ZERO;
            for (OcBOrderItem orderItem : orderItemList) {
                BigDecimal weight = Optional.ofNullable(orderItem.getStandardWeight()).orElse(BigDecimal.ZERO);
                orderWeight = orderWeight.add(weight.multiply(orderItem.getQty()));
            }

            if (orderWeight.compareTo(expressStartingWeight) >= 0) {
                ocOrder.setIsExpress(YesNoEnum.Y.getKey());
                isExpress = true;
                omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                        OrderLogTypeEnum.STRATEGY_SPLIT.getKey(), "为快运订单,不拆单", null, null, user);

            } else {
                ocOrder.setIsExpress(YesNoEnum.N.getKey());
            }
        } else {
            ocOrder.setIsExpress(YesNoEnum.N.getKey());
        }
        ocOrderMapper.updateById(ocOrder);
        //(4)如果订单的“订单业务类型”非线上奶卡销售、线上免费奶卡。电子奶卡销售时，则继续判断【店铺策略】下“是否拆单”；
        //如果店铺拆单=否，则订单不拆单；
        //如果店铺拆单=是，则订单继续下面寻源前拆单策略逻辑；；
        boolean unSplit =
                OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode().equals(ocOrder.getBusinessTypeCode()) ||
                        OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD.getCode().equals(ocOrder.getBusinessTypeCode()) ||
                        OrderBusinessTypeCodeEnum.VIRTUAL_MILK_CARD.getCode().equals(ocOrder.getBusinessTypeCode());
        boolean toBOrder = OmsBusinessTypeUtil.isToBOrder(ocOrder);
        if (unSplit || toBOrder) {
            omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                    OrderLogTypeEnum.STRATEGY_SPLIT.getKey(), "该业务类型订单不拆单", null, null, user);
            return true;
        }
        StCShopStrategyDO strategyByCpCshopId =
                stRpcService.selectOcStCShopStrategyByCpCshopId(ocOrder.getCpCShopId());
        if (strategyByCpCshopId == null) {
            return false;
        }
        if (orderItemList.size() == 1 && orderItemList.get(0).getQty().compareTo(BigDecimal.ONE) == 0) {
            return true;
        }
        if (!YesNoEnum.Y.getKey().equals(strategyByCpCshopId.getCanSplit())) {
            omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                    OrderLogTypeEnum.STRATEGY_SPLIT.getKey(), "店铺策略未开启拆单,不拆单", null, null, user);
            return true;
        }
        return isExpress;
    }

    public boolean insertNewOrders(Collection<List<OcBOrderItem>> splitOrderItemsPool,
                                   OcBOrder ocOrder, List<OcBOrderItem> orderItemList, User user, Map<String, Long> warehouseIdMap, String logMessage,
                                   List<Long> cardByGiftNotSplit, List<Long> cardByGiftRelation, boolean isDelay, List<Long> splitItemIds, Map<Long, String> matchItemMap) {
        Map<Long, OcBOrderItem> orderItemMap =
                orderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity()));
        List<OcBOrderItem> oldItem = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : orderItemList) {
            OcBOrderItem item = new OcBOrderItem();
            BeanUtils.copyProperties(ocBOrderItem, item);
            item.setCreationdate(new Date());
            item.setModifieddate(new Date());
            oldItem.add(item);
        }
        List<OcBOrder> insertOrders = new ArrayList<>();
        List<OcBOrderItem> insertOrderItems = new ArrayList<>();
        List<OcBOrderEqualExchangeItem> exchangeItems =
                ocBOrderEqualExchangeItemMapper.selectOcBOrderEqualExchangeItemList(Collections.singletonList(ocOrder.getId()));
        // 查询对应的奶卡信息
        List<OcBOrderNaiKa> ocBOrderNaiKas = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(ocOrder.getId());
        List<OcBOrderNaiKa> ocBOrderNaiKaList = new ArrayList<>();
        List<OcBOrderEqualExchangeItem> exchangeItems1 = new ArrayList<>();
        Map<Long, List<Long>> itemMapping = new HashMap<>();
        // 运费放第一单上
        Map<Long, List<Long>> oldItemMap = new HashMap<>();
        //运费放第一单上
        boolean spitAmtFlag = true;
        for (List<OcBOrderItem> ocOrderItems : splitOrderItemsPool) {
            // 计算明细金额
            computeAmt(orderItemMap, ocOrderItems);
            OcBOrder newOrder = new OcBOrder();
            BeanUtils.copyProperties(ocOrder, newOrder);
            long mainId = sequenceUtil.buildOrderSequenceId();

            try {
                if (CollectionUtils.isNotEmpty(ocOrderItems)) {
                    List<Long> itemIds = ocOrderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                    if (splitItemIds.containsAll(itemIds)) {
                        String redisKey = BllRedisKeyResources.getCOrderCancelMark(ocOrder.getTid());
                        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
                        objRedisTemplate.opsForValue().set(redisKey, String.valueOf(mainId), 30, TimeUnit.MINUTES);
                    }
                }
            } catch (Exception e) {
                log.warn("insertNewOrders canci redis error ocOrderItems:{}", JSON.toJSONString(ocOrderItems), e);
                DingTalkUtil.notice("insertNewOrders canci redis error");
            }

            newOrder.setCreationdate(new Date());
            newOrder.setModifieddate(new Date());
            newOrder.setId(mainId);
            newOrder.setIsSplit(1);
            newOrder.setBillNo(sequenceUtil.buildBillNo());
            newOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            if (MapUtils.isNotEmpty(matchItemMap)){
                newOrder.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
            }
            newOrder.setSuffixInfo("SP-" + mainId);
            newOrder.setCpCPhyWarehouseId(null);
            newOrder.setCpCPhyWarehouseEname(null);
            newOrder.setCpCPhyWarehouseEcode(null);

            if (CollectionUtils.isNotEmpty(ocOrderItems)) {
                String saleProductAttr = matchItemMap.get(ocOrderItems.get(0).getId());
                if (StringUtils.isNotBlank(saleProductAttr)) {
                    newOrder.setSaleProductAttr(saleProductAttr);
                }
            }

            List<OcBOrderItem> combineSplit = isCombineSplit(oldItem, ocOrderItems);
            if (CollectionUtils.isNotEmpty(combineSplit)) {
                this.groupGoodsType(ocOrderItems, combineSplit);
                ocOrderItems.addAll(combineSplit);
            } else {
                newOrder.setIsCombination(0);
                for (OcBOrderItem ocOrderItem : ocOrderItems) {
                    ocOrderItem.setProType((long) SkuType.NORMAL_PRODUCT);
                }
            }
            Map<Long, List<OcBOrderNaiKa>> naikaMap = new HashMap<>();
            List<OcBOrderNaiKa> orderNaiKas = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(ocBOrderNaiKas)) {
                List<OcBOrderNaiKa> naiKas = ocBOrderNaiKas.stream().filter(p -> p.getOcBOrderItemId() == null).collect(Collectors.toList());
                List<OcBOrderNaiKa> naiKaList = ocBOrderNaiKas.stream().filter(p -> p.getOcBOrderItemId() != null).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(naiKaList)) {
                    naikaMap = naiKaList.stream().collect(Collectors.groupingBy(OcBOrderNaiKa::getOcBOrderItemId));
                }
                if (CollectionUtils.isNotEmpty(naiKas)) {
                    for (OcBOrderNaiKa naiKa : naiKas) {
                        OcBOrderNaiKa orderNaiKa = new OcBOrderNaiKa();
                        BeanUtils.copyProperties(naiKa, orderNaiKa);
                        orderNaiKa.setCreationdate(new Date());
                        orderNaiKa.setModifieddate(new Date());
                        orderNaiKas.add(orderNaiKa);
                    }
                }
            }
            List<Long> oldItemIdList = new ArrayList<>();
            // 处理奶卡信息
            for (OcBOrderItem newItem : ocOrderItems) {
                Long id = newItem.getId();
                this.matchWarehouse(warehouseIdMap, newOrder, newItem);
                long itemSequenceId = sequenceUtil.buildOrderItemSequenceId();
                if (!itemMapping.containsKey(id)) {
                    List<Long> itemIds = new ArrayList<>();
                    itemIds.add(itemSequenceId);
                    itemMapping.put(id, itemIds);
                } else {
                    List<Long> itemIds = itemMapping.get(id);
                    itemIds.add(itemSequenceId);
                    itemMapping.put(id, itemIds);
                }
                List<OcBOrderNaiKa> orderNaiKas1 = handleNaika(naikaMap, newItem, itemSequenceId);
                if (CollectionUtils.isNotEmpty(orderNaiKas1)) {
                    orderNaiKas.addAll(orderNaiKas1);
                }
                newItem.setId(itemSequenceId);
                newItem.setOcBOrderId(mainId);
                oldItemIdList.add(id);
                newItem.setCreationdate(new Date());
                newItem.setModifieddate(new Date());
            }
            oldItemMap.put(mainId, oldItemIdList);

            List<OcBOrderItem> collect =
                    ocOrderItems.stream().filter(p -> p.getIsGift() != null && p.getIsGift() == 1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                newOrder.setIsHasgift(1);
            } else {
                newOrder.setIsHasgift(0);
            }
            List<OcBOrderItem> equalExchangeItems =
                    ocOrderItems.stream().filter(p -> p.getIsEqualExchange() != null && p.getIsEqualExchange() == 1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(equalExchangeItems)) {
                newOrder.setIsEqualExchange(1);
            } else {
                newOrder.setIsEqualExchange(0);
            }

            List<OcBOrderItem> returnItems =
                    ocOrderItems.stream().filter(p -> p.getPtReturnStatus() != null
                            && OcOrderRefundStatusEnum.CLOSED.getVal() != p.getRefundStatus()).collect(Collectors.toList());

            // 根据明细的退款状态 判断主表的退款标记 1205
            if (CollectionUtils.isEmpty(returnItems)) {
                //是否已经拦截
                newOrder.setIsInterecept(0);
                //是否已经退款中
                newOrder.setIsInreturning(0);
            }

            // 标记处理
            List<OcBOrderItem> giftList =
                    ocOrderItems.stream().filter(p -> p.getIsGiftSplit() != null && p.getIsGiftSplit() == 3).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(giftList)) {
                //hold 单
                newOrder.setIsInterecept(1);
                ocBOrderHoldService.holdOrUnHoldOrder(newOrder, OrderHoldReasonEnum.GIFT_AFTER);
            }

            //衍生品卡单拆单后无衍生品订单释放卡单
            if ("免费订单衍生品自动拆单".equals(logMessage)){
                List<String> dmsFreeOrder = businessSystemParamService.getDmsFreeOrder();
                OcBOrderItem ocBOrderItem = ocOrderItems.get(0);
                if (!dmsFreeOrder.contains(ocBOrderItem.getPsCSkuEcode())){
                    //不卡单
                    newOrder.setDetentionReason("");
                    newOrder.setDetentionReasonId(null);
                    newOrder.setIsDetention(0);
                }
            }

            omsOrderLogService.addUserOrderLog(newOrder.getId(), newOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_SPLIT.getKey(), "由订单id:" + ocOrder.getId() + "拆分", null, null, user);

            //拆单维护补发关系
            if (YesNoEnum.Y.getVal().equals(newOrder.getIsResetShip())) {
                //补发单，维护来源关系
                List<Long> sourceIds = sourceRelationMapper.querySourceOrderId(ocOrder.getId(), OcBOrderSourceRelationTypeEnum.REISSUE.getKey());
                if (CollectionUtils.isNotEmpty(sourceIds)) {
                    OcBOrderSourceRelation sourceRelation = new OcBOrderSourceRelation();
                    sourceRelation.setId(sequenceUtil.buildOrderSourceRelationSequenceId());
                    sourceRelation.setOrderId(newOrder.getId());
                    sourceRelation.setSourceOrderId(sourceIds.get(0));
                    sourceRelation.setType(OcBOrderSourceRelationTypeEnum.REISSUE.getKey());
                    sourceRelation.setCreationdate(new Date());
                    sourceRelation.setModifieddate(new Date());
                    sourceRelation.setOwnerid(user != null ? user.getId() : 0L);
                    sourceRelation.setOwnername(user != null ? user.getEname() : "");
                    sourceRelation.setIsactive("Y");
                    sourceRelationMapper.insert(sourceRelation);
                }
            }

            if (CollectionUtils.isNotEmpty(exchangeItems)) {
                for (OcBOrderEqualExchangeItem exchangeItem : exchangeItems) {
                    OcBOrderEqualExchangeItem item = new OcBOrderEqualExchangeItem();
                    BeanUtils.copyProperties(exchangeItem, item);
                    long l = sequenceUtil.buildEqualExchangeItemSequenceId();
                    item.setId(l);
                    item.setOcBOrderId(newOrder.getId());
                    exchangeItems1.add(item);
                }
            }
            if (CollectionUtils.isNotEmpty(orderNaiKas)) {
                for (OcBOrderNaiKa orderNaiKa : orderNaiKas) {
                    orderNaiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
                    orderNaiKa.setOcBOrderId(newOrder.getId());
                    ocBOrderNaiKaList.add(orderNaiKa);
                }
            }
            if (spitAmtFlag && newOrder.getShipAmt().compareTo(BigDecimal.ZERO) > 0) {
                spitAmtFlag = false;
            } else {
                newOrder.setShipAmt(BigDecimal.ZERO);
            }

            insertOrders.add(newOrder);
            insertOrderItems.addAll(ocOrderItems);
        }
        // 检验数据是否完整 不完整则直接退出
        List<OcBOrderItem> orderItems = orderItemList.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        BigDecimal qtyCountOriginal = orderItems.stream().map(OcBOrderItem::getQty).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        List<OcBOrderItem> itemList = insertOrderItems.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        BigDecimal qtyCountNew = itemList.stream().map(OcBOrderItem::getQty).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        if (qtyCountOriginal.compareTo(qtyCountNew) != 0) {
            log.info(LogUtil.format("寻源前拆单数量检验异常", "寻源前拆单数量检验异常", ocOrder.getId(), ocOrder.getOrderStatus()));
            omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                    OrderLogTypeEnum.STRATEGY_SPLIT.getKey(), "拆单数量检验不通过!!!", null, null, user);
            return false;
        }
        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocOrder.getOrderStatus())) {
            // 释放库存
            ValueHolderV14 sgValueHolder = sgRpcService.voidSgStockOccupy(ocOrder, orderItemList, user);
            if (sgValueHolder.getCode() != 0) {
                throw new NDSException("释放占用库存失败!");
            }
        }
        this.computeComputeAmt(itemMapping, insertOrderItems, orderItemList);
        Map<Long, List<OcBOrderItem>> itemMap = insertOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
        for (OcBOrder insertOrder : insertOrders) {
            boolean isPickUp = false;

            boolean isCycle = insertOrder.getIsCycle() != null && insertOrder.getIsCycle() == 1;
            List<OcBOrderItem> ocBOrderItems = itemMap.get(insertOrder.getId());
            List<OcBOrderItem> bOrderItems = ocBOrderItems.stream().filter(p -> p.getEstimateConTime() != null).collect(Collectors.toList());
            if (insertOrder.getEstimateConTime() == null && CollectionUtils.isEmpty(bOrderItems)) {
                insertOrder.setDouble11PresaleStatus(0);
            }

            String businessTyCode = insertOrder.getBusinessTypeCode();
            String naiKaType = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(businessTyCode).getNaiKaType();
            if ("pickup".equals(naiKaType)) {
                isPickUp = true;
            }
            // 判断商品的一级类目是否有包含低温白奶
            // 获取ocBOrderItems里面的商品编码
            boolean containsLowTemperature = false;
            List<String> skuCodeList = ocBOrderItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
            if (!isCycle && !OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(businessTyCode) && !OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP_REISSUE.getCode().equals(businessTyCode)) {
                for (String skuCode : skuCodeList) {
                    ProductSku productSku = psRpcService.selectProductSku(skuCode);
                    //productSku 中的proAttributeMap 中M_DIM4_ID的ename为"低温白奶"
                    if (productSku != null && productSku.getProAttributeMap() != null && productSku.getProAttributeMap().get("M_DIM4_ID") != null) {
                        String ename = productSku.getProAttributeMap().get("M_DIM4_ID").getEname();
                        if ("低温白奶".equals(ename)) {
                            containsLowTemperature = true;
                            break;
                        }
                    }
                }
            }
            // 如果是低温白奶 但是不是提奶订单的话 就直接卡单 并且不设置卡单时间(如果这个sku之前配置了卡单策略呢)
            if (containsLowTemperature && !isPickUp) {
                List<StCHoldOrderReason> stCHoldOrderReasonList = stCHoldOrderReasonMapper.selectByTypeAndReason(1, "低温白奶散单卡单");
                if (CollectionUtils.isNotEmpty(stCHoldOrderReasonList)) {
                    StCHoldOrderReason stCHoldOrderReason = stCHoldOrderReasonList.get(0);
                    insertOrder.setDetentionReason(stCHoldOrderReason.getReason());
                    insertOrder.setDetentionReasonId(Math.toIntExact(stCHoldOrderReason.getId()));
                    insertOrder.setIsDetention(1);
                } else {
                    insertOrder.setDetentionReason("低温白奶散单卡单");
                }
                insertOrder.setDetentionReleaseDate(null);
            }
            List<Long> oldItemIdList = oldItemMap.get(insertOrder.getId());

            //残次拆单不插寻源表
            if (MapUtils.isEmpty(matchItemMap)){
                if (insertOrder.getIsDetention() != null && insertOrder.getIsDetention() == 1) {
                    if (CollectionUtils.isNotEmpty(cardByGiftNotSplit) && cardByGiftNotSplit.containsAll(oldItemIdList)) {
                        insertOrder.setIsDetention(0);
                        insertOrder.setDetentionReleaseDate(null);
                    }
                    if (CollectionUtils.isNotEmpty(cardByGiftRelation) && cardByGiftRelation.containsAll(oldItemIdList)) {
                        insertOrder.setIsDetention(0);
                        insertOrder.setDetentionReleaseDate(null);
                    }
                    if (insertOrder.getIsDetention() == 1) {
                        Date detentionReleaseDate = insertOrder.getDetentionReleaseDate();
                        if (containsLowTemperature) {
                            omsOccupyTaskService.addOcBOccupyTaskNew(insertOrder, detentionReleaseDate, ocBOrderItems.size(), containsLowTemperature, isPickUp);
                        } else if (detentionReleaseDate != null) {
                            omsOccupyTaskService.addOcBOccupyTask(insertOrder, detentionReleaseDate);
                        }
                    }
                } else {
                    if (isDelay) {
                        //延迟转单（寻源）
                        newDelayTrans(insertOrder, containsLowTemperature, ocBOrderItems.size());
                    } else {
                        Calendar calendar = Calendar.getInstance();
                        calendar.add(Calendar.SECOND, 5);
                        omsOccupyTaskService.addOcBOccupyTaskNew(insertOrder, calendar.getTime(), ocBOrderItems.size(), containsLowTemperature, isPickUp);
                    }
                }
            }

            OcBOrderParam param = new OcBOrderParam();
            param.setOcBOrder(insertOrder);
            param.setOrderItemList(ocBOrderItems);
            orderAmountUtil.recountOrderAmount(param);

            // 如果订单的物流公司已经存在，则不匹配
            Long appointLogisticsId = insertOrder.getAppointLogisticsId();
            if (appointLogisticsId != null) {
                continue;
            }
            matchAppointExpress(insertOrder, ocBOrderItems);
        }

        applicationContext.getBean(SplitBeforeSourcingStService.class).extracted(ocOrder, logMessage, insertOrders, insertOrderItems, ocBOrderNaiKaList, exchangeItems1);
        omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                OrderLogTypeEnum.ORDER_SPLIT.getKey(), logMessage, null, null, user);

        //中台周期购信息维护
        if (OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(ocOrder.getBusinessTypeCode())) {
            try {
                cycleBuyInfoService.fullCycleBuyInfo(ocOrder, insertOrders);
            } catch (Exception e) {
                log.warn("中台周期购信息维护失败 insertNewOrders billNo:{}", ocOrder.getBillNo(), e);
                DingTalkUtil.notice("中台周期购信息维护失败:" + ocOrder.getBillNo());
            }
        }

        //残次拆单待分配
        if (MapUtils.isNotEmpty(matchItemMap)){
            for (OcBOrder insertOrder : insertOrders) {
                OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                toBeConfirmedTask.setOrderId(insertOrder.getId());
                toBeConfirmedTask.setCreationdate(new Date());
                toBeConfirmedTask.setStatus(0);
                toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
            }
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void extracted(OcBOrder ocOrder, String logMessage, List<OcBOrder> insertOrders, List<OcBOrderItem> insertOrderItems, List<OcBOrderNaiKa> ocBOrderNaiKaList, List<OcBOrderEqualExchangeItem> exchangeItems1) {
        if (CollectionUtils.isNotEmpty(exchangeItems1)) {
            ocBOrderEqualExchangeItemMapper.batchInsert(exchangeItems1);
        }
        if (CollectionUtils.isNotEmpty(ocBOrderNaiKaList)) {
            ocBOrderNaiKaMapper.batchInsert(ocBOrderNaiKaList);
        }
        ocOrderMapper.batchInsert(insertOrders);
        ocOrderItemMapper.batchInsert(insertOrderItems);

        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(ocOrder.getId());
        updateOrder.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
        updateOrder.setSysremark(logMessage);
        ocOrderMapper.updateById(updateOrder);
    }

    public void groupGoodsType(List<OcBOrderItem> ocOrderItems, List<OcBOrderItem> combineSplit) {
        List<String> groupMarkList = combineSplit.stream().filter(p -> StringUtils.isNotEmpty(p.getGroupGoodsMark())).map(OcBOrderItem::getGroupGoodsMark).collect(Collectors.toList());
        for (OcBOrderItem ocOrderItem : ocOrderItems) {
            String groupGoodsMark = ocOrderItem.getGroupGoodsMark();
            if (StringUtils.isEmpty(groupGoodsMark)) {
                continue;
            }
            if (groupMarkList.contains(groupGoodsMark)) {
                ocOrderItem.setProType((long) SkuType.COMBINE_PRODUCT);
            } else {
                ocOrderItem.setProType((long) SkuType.NORMAL_PRODUCT);
            }

        }
    }


    private List<OcBOrderNaiKa> handleNaika(Map<Long, List<OcBOrderNaiKa>> naikaMap, OcBOrderItem ocBOrderItem, Long newItemId) {
        if (naikaMap.isEmpty()) {
            return null;
        }
        List<OcBOrderNaiKa> orderNaiKaList = new ArrayList<>();
        List<OcBOrderNaiKa> orderNaiKas = naikaMap.get(ocBOrderItem.getId());
        if (CollectionUtils.isNotEmpty(orderNaiKas)) {
            for (OcBOrderNaiKa orderNaiKa : orderNaiKas) {
                OcBOrderNaiKa naiKa = new OcBOrderNaiKa();
                BeanUtils.copyProperties(orderNaiKa, naiKa);
                naiKa.setOcBOrderItemId(newItemId);
                orderNaiKaList.add(naiKa);
            }
        }

        return orderNaiKaList;
    }


    private void matchWarehouse(Map<String, Long> warehouseIdMap, OcBOrder newOrder, OcBOrderItem newItem) {
        String id = newItem.getReserveVarchar05();
        if (warehouseIdMap != null && !warehouseIdMap.isEmpty()) {
            Long warehouseId = warehouseIdMap.get(id);
            if (warehouseId != null) {
                CpCPhyWarehouse warehouse = cpRpcService.queryByWarehouseId(warehouseId);
                if (warehouse != null) {
                    newOrder.setCpCPhyWarehouseId(warehouseId);
                    newOrder.setCpCPhyWarehouseEname(warehouse.getEname());
                    newOrder.setCpCPhyWarehouseEcode(warehouse.getEcode());
                    newOrder.setReserveVarchar05("");
                }
            }
        }
    }

    private void computeComputeAmt(Map<Long, List<Long>> itemMapping, List<OcBOrderItem> newOrderItems, List<OcBOrderItem> orderItemList) {
        if (itemMapping.isEmpty()) {
            return;
        }
        Map<Long, OcBOrderItem> oldOrderItemMap = orderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
        Map<Long, OcBOrderItem> newOrderItemMap = newOrderItems.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
        for (Long id : itemMapping.keySet()) {
            OcBOrderItem oldOrderItem = oldOrderItemMap.get(id);
            List<OcBOrderItem> itemList = new ArrayList<>();
            List<Long> newItemIds = itemMapping.get(id);
            if (CollectionUtils.isNotEmpty(newItemIds) && newItemIds.size() == 1) {
                continue;
            }
            BigDecimal realAmtCount = oldOrderItem.getRealAmt() == null ? BigDecimal.ZERO : oldOrderItem.getRealAmt();
            if (realAmtCount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            for (Long newItemId : newItemIds) {
                itemList.add(newOrderItemMap.get(newItemId));
            }
            //单行交易明细组合商品总的优惠金额
            BigDecimal amtDiscountTotal = oldOrderItem.getAmtDiscount() == null ? BigDecimal.ZERO : oldOrderItem.getAmtDiscount();
            //单行交易明细组合商品总调整金额
            BigDecimal adjustAmtCount = oldOrderItem.getAdjustAmt() == null ? BigDecimal.ZERO : oldOrderItem.getAdjustAmt();
            //单行交易明细组合商品总平摊金额
            BigDecimal orderSplitAmtCount = oldOrderItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : oldOrderItem.getOrderSplitAmt();
            //原价
            Collections.sort(itemList, (o1, o2) -> o2.getRealAmt().compareTo(o1.getRealAmt()));
            OcBOrderItem orderItem = itemList.get(0);
            //尾差
            BigDecimal realAmt = itemList.stream().map(OcBOrderItem::getRealAmt).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal orderSplitAmt = itemList.stream().map(OcBOrderItem::getOrderSplitAmt).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal amtDiscount = itemList.stream().map(OcBOrderItem::getAmtDiscount).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal adjustAmt = itemList.stream().map(OcBOrderItem::getAdjustAmt).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal priceQ = itemList.stream().map(OcBOrderItem::getPrice).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            orderItem.setRealAmt(orderItem.getRealAmt().add(realAmtCount.subtract(realAmt)));
            orderItem.setOrderSplitAmt(orderItem.getOrderSplitAmt().add(orderSplitAmtCount.subtract(orderSplitAmt)));
            orderItem.setAmtDiscount(orderItem.getAmtDiscount().add(amtDiscountTotal.subtract(amtDiscount)));
            orderItem.setPriceActual(orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
            orderItem.setAdjustAmt(orderItem.getAdjustAmt().add(adjustAmtCount.subtract(adjustAmt)));
        }
    }

    /**
     * 判断组合商品是否被拆分
     *
     * @param orderItemList
     * @param ocOrderItems
     */
    public List<OcBOrderItem> isCombineSplit(List<OcBOrderItem> orderItemList,
                                             List<OcBOrderItem> ocOrderItems) {
        Map<String, OcBOrderItem> group4Map = new HashMap<>();
        Map<String, List<OcBOrderItem>> group2Map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderItemList)) {
            List<OcBOrderItem> group4 = orderItemList.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
            List<OcBOrderItem> group2 = orderItemList.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT || p.getProType() == SkuType.GIFT_PRODUCT).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(group4)) {
                group4Map = group4.stream().collect(Collectors.toMap(OcBOrderItem::getGroupGoodsMark, Function.identity(), (key1, key2) -> key2));
            }
            if (CollectionUtils.isNotEmpty(group2)) {
                group2Map = group2.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
            }
        }
        if (group2Map.isEmpty()) {
            return null;
        }
        List<OcBOrderItem> orderItems = ocOrderItems.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT && StringUtils.isNotEmpty(p.getGroupGoodsMark())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItems)) {
            return null;
        }
        Map<String, List<OcBOrderItem>> stringListMap = orderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
        List<OcBOrderItem> groupItem = new ArrayList<>();
        for (String s : stringListMap.keySet()) {
            List<OcBOrderItem> itemList = group2Map.get(s);
            List<OcBOrderItem> itemList1 = stringListMap.get(s);
            //判断是否一样 数量一样  就判断数量是否一样
            boolean flag = true;
            if (itemList.size() == itemList1.size()) {
                Map<Long, OcBOrderItem> orderItemMap = itemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
                for (OcBOrderItem item : itemList1) {
                    OcBOrderItem item1 = orderItemMap.get(item.getId());
                    if (item1 == null) {
                        flag = false;
                        break;
                    }
                    BigDecimal qty = item.getQty();
                    BigDecimal qty1 = item1.getQty();
                    if (qty.compareTo(qty1) != 0) {
                        flag = false;
                        break;
                    }
                }
            } else {
                flag = false;
            }
            if (flag) {
                OcBOrderItem item = group4Map.get(s);
                if (item != null) {
                    groupItem.add(item);
                }
            }
        }
        return groupItem;
    }

    private Collection<List<OcBOrderItem>> splitStrategyByCategory(List<OcBOrderItem> ocOrderItemList,
                                                                   Map<Long, StCSplitBeforeSourceStrategyCategoryItem> categoryStrategyMap, StringBuilder logBuilder) {
        log.info("Start.SplitBeforeSourcingStService.splitStrategyByCategory");
        //收集拆单和未拆单的的
        Table<List<String>, Boolean, List<OcBOrderItem>> employeeTable = HashBasedTable.create();
        //不满足拆单的
        List<OcBOrderItem> restList = new ArrayList<>();
        //其他可拆单的
        List<OcBOrderItem> canSplitList = new ArrayList<>();

        //不允许拆单的赠品
        List<OcBOrderItem> unSplitGiftList = new ArrayList<>();
        //所有赠品挂靠关系
        List<String> allSplitGiftRelation = new ArrayList<>();

        List<OcBOrderItem> isCombineProductList = new ArrayList<>();


        //数据分组  不可拆单赠品 、可拆单商品、不可拆单(不包含赠品)
        groupBeforeSplitCategory(ocOrderItemList, canSplitList, isCombineProductList, unSplitGiftList);

        if (CollectionUtils.isNotEmpty(isCombineProductList)) {
            Map<String, List<OcBOrderItem>> isCombineMap =
                    isCombineProductList.stream().collect(Collectors.groupingBy(x -> Optional.ofNullable(x.getGroupGoodsMark()).orElse(
                            String.valueOf(Math.random()))));
            Set<String> groupGoodsMark = isCombineMap.keySet();
            for (String mark : groupGoodsMark) {
                List<OcBOrderItem> ocOrderItems = isCombineMap.get(mark);
                //组合商品是否允许拆
                boolean b = ocOrderItems.stream().anyMatch(o -> "N".equals(o.getCanSplit()));
                if (b) {
                    //不允许判断品类是否一致
                    boolean noDim = ocOrderItems.stream().anyMatch(o -> o.getMDim4Id() == null);
                    if (noDim) {
                        restList.addAll(ocOrderItems);
                        continue;
                    }
                    Map<Long, List<OcBOrderItem>> dimMap =
                            ocOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getMDim4Id));
                    Set<Long> dimIds = dimMap.keySet();
                    if (dimIds.size() > 1) {
                        //品类不一致 不按品类拆
                        restList.addAll(ocOrderItems);
                        continue;
                    }
                    for (Long dim : dimIds) {
                        boolean isSplit = categoryStrategyMap.containsKey(dim);
                        if (isSplit) {
                            canSplitList.addAll(dimMap.get(dim));
                        } else {
                            restList.addAll(dimMap.get(dim));
                        }
                    }

                } else {
                    canSplitList.addAll(ocOrderItems);
                }
            }
        }
        //品类分组 匹配品类的
        Map<Long, List<OcBOrderItem>> mDimMap =
                canSplitList.stream().filter(o -> o.getMDim4Id() != null).collect(Collectors.groupingBy(OcBOrderItem::getMDim4Id));

        List<OcBOrderItem> noMDimList =
                canSplitList.stream().filter(o -> o.getMDim4Id() == null).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(noMDimList)) {
            restList.addAll(noMDimList);
        }

        Map<Long, PsCProdimItem> dimItemMap = new HashMap<>();
        try {
            PsCProdimItemRequest psCProdimItemRequest = new PsCProdimItemRequest();
            psCProdimItemRequest.setIdList(new ArrayList<>(categoryStrategyMap.keySet()));
            ValueHolderV14<Map<Long, PsCProdimItem>> mapValueHolderV14 = cProdimItemQueryCmd.queryPsCProdimItem(psCProdimItemRequest);
            if (mapValueHolderV14.isOK()){
                dimItemMap = mapValueHolderV14.getData();
            }
        }
        catch (Exception e) {
            log.warn("splitStrategyByCategory查询主数据信息异常={}", Throwables.getStackTraceAsString(e));
        }

        Set<Map.Entry<Long, List<OcBOrderItem>>> entries = mDimMap.entrySet();
        for (Map.Entry<Long, List<OcBOrderItem>> entry : entries) {
            Long mDimId = entry.getKey();
            StCSplitBeforeSourceStrategyCategoryItem strategy = categoryStrategyMap.get(mDimId);
            List<OcBOrderItem> value = entry.getValue();
            if (strategy != null) {
                //表示命中过，且存在多种一级分类，打印命中的那个分类
                if (mDimMap.keySet().size() > 1) {
                    PsCProdimItem psCProdimItem = dimItemMap.get(mDimId);
                    if (psCProdimItem != null) {
                        logBuilder.append(" ").append(psCProdimItem.getEname()).append(" ");
                    }
                }

                //匹配拆单策略的
                List<String> giftRelations =
                        value.stream().map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
                //含有主品
                boolean haveMain = value.stream().anyMatch(o -> o.getIsGift() == null || o.getIsGift() != 1);
                if (haveMain) {
                    allSplitGiftRelation.addAll(giftRelations);
                }
                boolean contains = employeeTable.contains(giftRelations, haveMain);
                //存在多key相同会覆盖
                if (contains) {
                    double random = Math.random();
                    giftRelations.add(String.valueOf(random));
                }

                employeeTable.put(giftRelations, haveMain, value);
                continue;
            }
            //可拆单情况且未匹配策略的集合(包含赠品)放到rest
            restList.addAll(value);
        }

        //不可拆单赠品挂靠分组
        Map<String, List<OcBOrderItem>> giftRelationMap =
                unSplitGiftList.stream().collect(Collectors.groupingBy((x -> Optional.ofNullable(x.getGiftRelation()).orElse("默认"))));

        Map<List<String>, List<OcBOrderItem>> splitMainMap = employeeTable.column(true);
        if (MapUtils.isNotEmpty(splitMainMap)) {
            Set<List<String>> giftRelationSet = splitMainMap.keySet();
            Set<Map.Entry<String, List<OcBOrderItem>>> giftEntries = giftRelationMap.entrySet();
            //没有绑定主品的赠品
            for (Map.Entry<String, List<OcBOrderItem>> giftEntry : giftEntries) {
                String giftRelation = giftEntry.getKey();
                List<OcBOrderItem> gifts = giftEntry.getValue();
                for (List<String> stringList : giftRelationSet) {
                    if (allSplitGiftRelation.contains(giftRelation)) {
                        //赠品需随主品组成包裹
                        if (stringList.contains(giftRelation)) {
                            List<OcBOrderItem> orderItems = splitMainMap.get(stringList);
                            orderItems.addAll(gifts);
                            splitMainMap.put(stringList, orderItems);
                            break;
                        }
                    } else {
                        List<OcBOrderItem> orderItems = splitMainMap.get(stringList);
                        orderItems.addAll(gifts);
                        splitMainMap.put(stringList, orderItems);
                        break;
                    }
                }
            }
        } else {
            restList.addAll(unSplitGiftList);
        }

        List<List<OcBOrderItem>> lists = new ArrayList<>();
        Collection<List<OcBOrderItem>> values = employeeTable.values();
        if (CollectionUtils.isNotEmpty(values)) {
            lists.addAll(values);
        }
        if (CollectionUtils.isNotEmpty(restList)) {
            lists.add(restList);
        }
        log.info("Finish.SplitBeforeSourcingStService.splitStrategyByCategory,value.size={}", lists.size());
        return lists;
    }

    private void groupBeforeSplitCategory(List<OcBOrderItem> ocOrderItemList, List<OcBOrderItem> canSplitList,
                                          List<OcBOrderItem> isCombineProductList,
                                          List<OcBOrderItem> unSplitGiftList) {
        for (OcBOrderItem orderItem : ocOrderItemList) {
            //为组合商品
            if (orderItem.getProType() == 2) {
                isCombineProductList.add(orderItem);
                continue;
                //为赠品且不允许拆单
            } else if (orderItem.getIsGift() != null && orderItem.getIsGift() == 1 && (orderItem.getIsGiftSplit() == null || orderItem.getIsGiftSplit() != 2)) {
                unSplitGiftList.add(orderItem);
                continue;
            }
            //允许拆单的集合
            canSplitList.add(orderItem);

        }

    }

    private Collection<List<OcBOrderItem>> splitStrategyByWeight(List<OcBOrderItem> ocOrderItemList,
                                                                 Map<Long, BigDecimal> weightStrategyMap) {
        log.info("Start.SplitBeforeSourcingStService.splitStrategyByWeight");
        BigDecimal totWeight = BigDecimal.ZERO;
        for (OcBOrderItem item : ocOrderItemList) {
            BigDecimal weight = Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO);
            BigDecimal qty = item.getQty();
            totWeight = totWeight.add(weight.multiply(qty));
        }
        //不满足拆单的
        List<OcBOrderItem> restList = new ArrayList<>();
        //赠品
        List<OcBOrderItem> giftList = new ArrayList<>();
        //收集拆单和未拆单的的
        Table<String, BigDecimal, List<OcBOrderItem>> employeeTable = HashBasedTable.create();
        Map<String, OcBOrderItem> goodsMarkMap =
                ocOrderItemList.stream().filter(i -> i.getProType() == 4).collect(Collectors.toMap(OcBOrderItem::getGroupGoodsMark, Function.identity()));
        //商品重量有null值 排序
        ocOrderItemList.sort((o1, o2) -> {
            if (o1.getStandardWeight() == null || o2.getStandardWeight() == null) {
                return 0;
            }
            return o1.getStandardWeight().compareTo(o2.getStandardWeight());
        });
        for (OcBOrderItem orderItem : ocOrderItemList) {
            if (orderItem.getProType() == 4) {
                continue;
            }
            if (orderItem.getProType() == 2) {
                if ("Y".equals(orderItem.getCanSplit()) && weightStrategyMap.containsKey(orderItem.getMDim4Id())) {
                    splitWeight(weightStrategyMap, restList, employeeTable, orderItem);
                } else {
                    restList.add(orderItem);
                }
            } else if (orderItem.getIsGift() != null && orderItem.getIsGift() == 1) {
                if (orderItem.getIsGiftSplit() != null && orderItem.getIsGiftSplit() == 2 && weightStrategyMap.containsKey(orderItem.getMDim4Id())) {
                    splitWeight(weightStrategyMap, restList, employeeTable, orderItem);
                } else {
                    giftList.add(orderItem);
                }
            } else {
                if (weightStrategyMap.containsKey(orderItem.getMDim4Id())) {
                    splitWeight(weightStrategyMap, restList, employeeTable, orderItem);
                } else {
                    restList.add(orderItem);
                }
            }
        }
        List<List<OcBOrderItem>> collect = new ArrayList<>(employeeTable.values());
        if (CollectionUtils.isNotEmpty(giftList)) {
            for (OcBOrderItem gift : giftList) {
                boolean notBlankGift = true;

                if (StringUtils.isNotBlank(gift.getGiftRelation())) {
                    for (List<OcBOrderItem> items : collect) {
                        List<String> giftRelations =
                                items.stream().filter(o -> o.getGiftRelation() != null).map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(giftRelations)) {
                            if (giftRelations.contains(gift.getGiftRelation())) {
                                items.add(gift);
                                notBlankGift = false;
                                break;
                            }
                        }
                    }
                    if (notBlankGift) {
                        if (restList.size() > 0) {
                            restList.add(gift);
                        } else {
                            collect.get(0).add(gift);
                        }
                    }
                } else {
                    boolean restHaveMain = restList.stream().anyMatch(o -> o.getIsGift() == null || o.getIsGift() != 1);
                    boolean anchored = true;

                    if (restHaveMain) {
                        restList.add(gift);
                        anchored = false;
                    }
                    if (anchored) {
                        for (List<OcBOrderItem> items : collect) {
                            boolean haveMain = items.stream().anyMatch(o -> o.getIsGift() == null || o.getIsGift() != 1);
                            if (haveMain) {
                                items.add(gift);
                                anchored = false;
                                break;
                            }
                        }
                    }
                    if (anchored) {
                        collect.get(0).add(gift);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(restList)) {
            collect.add(restList);
        }
        log.info("Finish.SplitBeforeSourcingStService.splitStrategyByWeight,value.size={}", collect.size());
        BigDecimal reduce = BigDecimal.ZERO;
        for (List<OcBOrderItem> orderItems : collect) {
            for (OcBOrderItem orderItem : orderItems) {
                BigDecimal weight = Optional.ofNullable(orderItem.getStandardWeight()).orElse(BigDecimal.ZERO);
                BigDecimal qty = orderItem.getQty();
                reduce = reduce.add(weight.multiply(qty));
            }
            if (MapUtils.isNotEmpty(goodsMarkMap)) {
                List<String> goodsMarks = orderItems.stream().filter(o -> o.getProType() == 2)
                        .map(OcBOrderItem::getGroupGoodsMark).distinct().collect(Collectors.toList());
                for (String goodsMark : goodsMarks) {
                    OcBOrderItem item = goodsMarkMap.get(goodsMark);
                    if (item != null) {
                        OcBOrderItem ocBOrderItem = new OcBOrderItem();
                        BeanUtils.copyProperties(item, ocBOrderItem);
                        orderItems.add(ocBOrderItem);
                    }
                }
            }
        }
        if (reduce.compareTo(totWeight) != 0) {
            log.error(LogUtil.format("SplitBeforeSourcingStService.splitStrategyByWeight.error",
                    "ocOrderItemList={}", "weightStrategyMap={}", "splitLists={}",
                    JSONObject.toJSONString(ocOrderItemList), JSONObject.toJSONString(weightStrategyMap),
                    JSONObject.toJSONString(collect)));
            AssertUtils.logAndThrow("按重量拆单异常!拆分集合重量之和" + reduce + ",拆单前重量" + totWeight);
        }
        return collect;
    }

    private Collection<List<OcBOrderItem>> splitStrategyBySku(List<OcBOrderItem> ocOrderItemList,
                                                              Map<Long, BigDecimal> skuStrategyMap, StringBuilder logBuilder) {
        log.info("Start.SplitBeforeSourcingStService.splitStrategyBySku");
        //不满足拆单的
        List<OcBOrderItem> restList = new ArrayList<>();
        //赠品不可拆
        List<OcBOrderItem> giftList = new ArrayList<>();

        List<List<OcBOrderItem>> collect = new ArrayList<>();

        for (OcBOrderItem orderItem : ocOrderItemList) {
            if (orderItem.getProType() == 4) {
                continue;
            }
            if (orderItem.getProType() == 2) {
                if ("Y".equals(orderItem.getCanSplit()) && skuStrategyMap.containsKey(orderItem.getPsCSkuId())) {
                    splitQty(skuStrategyMap, restList, collect, orderItem);
                    if (orderItem.getQty().compareTo(skuStrategyMap.get(orderItem.getPsCSkuId())) >= 0) {
                        //增加操作日志
                        BigDecimal bigDecimal = skuStrategyMap.get(orderItem.getPsCSkuId());
                        logBuilder.append(orderItem.getPsCSkuEcode()).append("最大").append(bigDecimal.stripTrailingZeros().toPlainString()).append("/");
                    }
                } else {
                    restList.add(orderItem);
                }
            } else if (orderItem.getIsGift() != null && orderItem.getIsGift() == 1) {
                if (orderItem.getIsGiftSplit() == null || orderItem.getIsGiftSplit() == 2 || orderItem.getIsGiftSplit() == 3) {
                    if (skuStrategyMap.containsKey(orderItem.getPsCSkuId())) {
                        splitQty(skuStrategyMap, restList, collect, orderItem);
                        if (orderItem.getQty().compareTo(skuStrategyMap.get(orderItem.getPsCSkuId())) >= 0) {
                            //增加操作日志
                            BigDecimal bigDecimal = skuStrategyMap.get(orderItem.getPsCSkuId());
                            logBuilder.append(orderItem.getPsCSkuEcode()).append("最大").append(bigDecimal.stripTrailingZeros().toPlainString()).append("/");
                        }
                    } else {
                        restList.add(orderItem);
                    }
                } else {
                    giftList.add(orderItem);
                }
            } else {
                if (skuStrategyMap.containsKey(orderItem.getPsCSkuId())) {
                    splitQty(skuStrategyMap, restList, collect, orderItem);
                    if (orderItem.getQty().compareTo(skuStrategyMap.get(orderItem.getPsCSkuId())) >= 0) {
                        //增加操作日志
                        BigDecimal bigDecimal = skuStrategyMap.get(orderItem.getPsCSkuId());
                        logBuilder.append(orderItem.getPsCSkuEcode()).append("最大").append(bigDecimal.stripTrailingZeros().toPlainString()).append("/");
                    }
                } else {
                    restList.add(orderItem);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(giftList)) {
            for (OcBOrderItem gift : giftList) {
                boolean relationFlag = true;
                for (List<OcBOrderItem> items : collect) {
                    List<String> giftRelations =
                            items.stream().filter(o -> o.getGiftRelation() != null).map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(giftRelations)) {
                        if (giftRelations.contains(gift.getGiftRelation())) {
                            items.add(gift);
                            relationFlag = false;
                            break;
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(restList) && relationFlag) {
                    for (OcBOrderItem item : restList) {
                        if (StringUtils.isNotEmpty(item.getGiftRelation()) && StringUtils.isNotEmpty(gift.getGiftRelation()) && item.getGiftRelation().equals(gift.getGiftRelation())) {
                            restList.add(gift);
                            relationFlag = false;
                            break;
                        }
                    }
                }

                if (relationFlag) {
                    for (List<OcBOrderItem> items : collect) {
                        boolean haveMain = items.stream().anyMatch(o -> o.getIsGift() == null || o.getIsGift() != 1);
                        if (haveMain) {
                            items.add(gift);
                            relationFlag = false;
                            break;
                        }
                    }
                }
                if (relationFlag) {
                    restList.add(gift);
                }

            }
        }

        if (CollectionUtils.isNotEmpty(restList)) {
            collect.add(restList);
        }
        log.info("Finish.SplitBeforeSourcingStService.splitStrategyBySku,value.size={}", collect.size());

        return collect;
    }

    private void splitWeight(Map<Long, BigDecimal> weightStrategyMap, List<OcBOrderItem> restList,
                             Table<String, BigDecimal, List<OcBOrderItem>> splitMap, OcBOrderItem orderItem) {
        BigDecimal splitWeight = weightStrategyMap.get(orderItem.getMDim4Id());
        if (splitWeight == null) {
            return;
        }
        BigDecimal standardWeight = Optional.ofNullable(orderItem.getStandardWeight()).orElse(BigDecimal.ZERO);
        if (standardWeight.compareTo(splitWeight) > 0) {
            restList.add(orderItem);
            return;
        }
        BigDecimal qty = orderItem.getQty();

        while (qty.compareTo(BigDecimal.ZERO) > 0) {
            List<OcBOrderItem> items = splitMap.get(String.valueOf(orderItem.getMDim4Id()), splitWeight);
            if (CollectionUtils.isEmpty(items)) {
                items = new ArrayList<>();
            }
            //未拆分商品总重量
            BigDecimal totWeight = qty.multiply(standardWeight);
            //当前包裹重量
            BigDecimal itemsWeight = getItemsWeight(items);
            //重量为0
            if (splitWeight.compareTo(totWeight.add(itemsWeight)) > 0) {
                OcBOrderItem addOldItem = new OcBOrderItem();
                BeanUtils.copyProperties(orderItem, addOldItem);
                addOldItem.setQty(qty);
                items.add(addOldItem);
                qty = BigDecimal.ZERO;
                splitMap.put(String.valueOf(orderItem.getMDim4Id()), splitWeight, items);
            } else {
                //分割包裹剩余重量
                BigDecimal subtract = splitWeight.subtract(itemsWeight);
                BigDecimal divide = subtract.divide(standardWeight, 4, BigDecimal.ROUND_HALF_UP);
                if (divide.compareTo(BigDecimal.ONE) >= 0) {
                    BigDecimal integerPart = divide.setScale(0, RoundingMode.DOWN);
                    OcBOrderItem addOldItem = new OcBOrderItem();
                    BeanUtils.copyProperties(orderItem, addOldItem);
                    addOldItem.setQty(integerPart);
                    qty = qty.subtract(integerPart);
                    items.add(addOldItem);
                    splitMap.remove(String.valueOf(orderItem.getMDim4Id()), splitWeight);
                    splitMap.put(String.valueOf(orderItem.getMDim4Id()) + Math.random(), splitWeight, items);
                } else {
                    splitMap.remove(String.valueOf(orderItem.getMDim4Id()), splitWeight);
                    splitMap.put(String.valueOf(orderItem.getMDim4Id()) + Math.random(), splitWeight, items);
                }
            }

        }
    }

    private void splitQty(Map<Long, BigDecimal> skuStrategyMap, List<OcBOrderItem> restList,
                          List<List<OcBOrderItem>> collect, OcBOrderItem orderItem) {
        BigDecimal splitQty = skuStrategyMap.get(orderItem.getPsCSkuId());
        BigDecimal qty = orderItem.getQty();
        BigDecimal divide = qty.divide(splitQty, 4, BigDecimal.ROUND_HALF_UP);
        if (divide.compareTo(BigDecimal.ONE) >= 0) {
            BigDecimal integerPart = divide.setScale(0, RoundingMode.DOWN);
            Integer splitCount = Integer.valueOf(String.valueOf(integerPart));
            for (Integer i = 0; i < splitCount; i++) {
                List<OcBOrderItem> orderItems = new ArrayList<>();
                OcBOrderItem splitItem = new OcBOrderItem();
                BeanUtils.copyProperties(orderItem, splitItem);
                splitItem.setQty(splitQty);
                orderItems.add(splitItem);
                collect.add(orderItems);
            }
            BigDecimal subtractQty = qty.subtract(integerPart.multiply(splitQty));
            if (subtractQty.compareTo(BigDecimal.ZERO) > 0) {
                OcBOrderItem splitItem = new OcBOrderItem();
                BeanUtils.copyProperties(orderItem, splitItem);
                splitItem.setQty(subtractQty);
                restList.add(splitItem);
            }
        } else {
            restList.add(orderItem);
        }
    }

    /**
     * 计算金额
     *
     * @param itemMap    原明细
     * @param orderItems 寻源前拆单逻辑后的明细
     */
    private void computeAmt(Map<Long, OcBOrderItem> itemMap, List<OcBOrderItem> orderItems) {
        for (OcBOrderItem orderItem : orderItems) {
            OcBOrderItem oldItem = itemMap.get(orderItem.getId());
            this.computeAmtItem(orderItem, oldItem);
        }
    }



    public void computeAmtItem(OcBOrderItem orderItem, OcBOrderItem oldItem) {
        if (oldItem == null) {
            return;
        }
        // 成交金额
        BigDecimal qty = oldItem.getQty();
        String oldEqualExchangeRatio = oldItem.getEqualExchangeRatio();
        if (StringUtils.isNotEmpty(oldEqualExchangeRatio)) {
            if (StringUtils.isNotEmpty(oldEqualExchangeRatio)) {
                String[] split = oldEqualExchangeRatio.split(":");
                qty = new BigDecimal(split[0]);
            }
        }
        BigDecimal realAmtSingle = oldItem.getRealAmt().divide(qty, 10, BigDecimal.ROUND_HALF_UP);
        //调整金额
        BigDecimal adjustAmtSingle = oldItem.getAdjustAmt().divide(qty, 10, BigDecimal.ROUND_HALF_UP);
        //优惠金额
        BigDecimal amtDiscountSingle = oldItem.getAmtDiscount().divide(qty, 10, BigDecimal.ROUND_HALF_UP);
        //平摊金额
        BigDecimal orderSplitAmtSingle = oldItem.getOrderSplitAmt().divide(qty, 10, BigDecimal.ROUND_HALF_UP);

        String equalExchangeRatio = orderItem.getEqualExchangeRatio();
        BigDecimal qty1 = orderItem.getQty();
        if (StringUtils.isNotEmpty(equalExchangeRatio)) {
            String[] split = equalExchangeRatio.split(":");
            BigDecimal ratio1 = new BigDecimal(split[0]);
            BigDecimal ratio2 = new BigDecimal(split[1]);
            BigDecimal ratioQty = ratio1.divide(ratio2);
            qty1 = qty1.multiply(ratioQty);
        }
        orderItem.setRealAmt(realAmtSingle.multiply(qty1).setScale(4, BigDecimal.ROUND_HALF_UP));
        orderItem.setAdjustAmt(adjustAmtSingle.multiply(qty1).setScale(4, BigDecimal.ROUND_HALF_UP));
        orderItem.setAmtDiscount(amtDiscountSingle.multiply(qty1).setScale(4,
                BigDecimal.ROUND_HALF_UP));
        orderItem.setOrderSplitAmt(orderSplitAmtSingle.multiply(qty1).setScale(4,
                BigDecimal.ROUND_HALF_UP));
        orderItem.setPriceActual(orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
    }


    public void addOccupy(OcBOrder ocBOrder, boolean isDelay) {
        //无拆单规则  插入待寻源中间表
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setSysremark("");
        order.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        ocOrderMapper.updateById(order);
        if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsCycle())){
            omsOccupyTaskService.addOcBOccupyTask(ocBOrder, ocBOrder.getEstimateConTime());
            return;
        }
        if (ocBOrder.getIsDetention() != null && ocBOrder.getIsDetention() == 1) {
            Date detentionReleaseDate = ocBOrder.getDetentionReleaseDate();
            if (detentionReleaseDate != null) {
                omsOccupyTaskService.addOcBOccupyTask(ocBOrder, detentionReleaseDate);
            }
        } else {
            if (isDelay) {
                //延迟转单（寻源）
                delayTrans(ocBOrder);
            } else {
                omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
            }
        }
    }

    /**
     * 根据店铺策略，延迟转单（寻源）
     *
     * @param ocBOrder
     */
    private void delayTrans(OcBOrder ocBOrder) {
        StCShopStrategyDO stCShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
        if (!Objects.isNull(stCShopStrategyDO) && YesNoEnum.Y.getKey().equals(stCShopStrategyDO.getIsDelayTransfer())) {
            //计算延迟时间
            Calendar calendar = Calendar.getInstance();
            Long delayTime = stCShopStrategyDO.getDelayTransferTime() == null ? 0 : stCShopStrategyDO.getDelayTransferTime();
            calendar.add(Calendar.SECOND, delayTime.intValue());
            omsOccupyTaskService.addOcBOccupyTask(ocBOrder, calendar.getTime());
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.SECOND, 5);
            omsOccupyTaskService.addOcBOccupyTask(ocBOrder, calendar.getTime());
        }
    }

    private void newDelayTrans(OcBOrder ocBOrder, boolean containsLowTemperature, Integer itemNum) {
        StCShopStrategyDO stCShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
        if (!Objects.isNull(stCShopStrategyDO) && YesNoEnum.Y.getKey().equals(stCShopStrategyDO.getIsDelayTransfer())) {
            //计算延迟时间
            Calendar calendar = Calendar.getInstance();
            Long delayTime = stCShopStrategyDO.getDelayTransferTime() == null ? 0 : stCShopStrategyDO.getDelayTransferTime();
            calendar.add(Calendar.SECOND, delayTime.intValue());
            if (containsLowTemperature) {
                // 往新的表中写数据
                lowTemperatureOccupyTaskService.addLowTemperaturOcBOccupyTask(ocBOrder, calendar.getTime(), itemNum);
            } else {
                omsOccupyTaskService.addOcBOccupyTask(ocBOrder, calendar.getTime());
            }
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.SECOND, 5);
            if (containsLowTemperature) {
                // 往新的表中写数据
                lowTemperatureOccupyTaskService.addLowTemperaturOcBOccupyTask(ocBOrder, calendar.getTime(), itemNum);
            } else {
                omsOccupyTaskService.addOcBOccupyTask(ocBOrder, calendar.getTime());
            }
        }
    }

    private BigDecimal getExpressStartingWeight() {
        try {
            String value =
                    (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(OC_B_ORDER_EXPRESS_STARTING_WEIGHT);
            if (StringUtils.isEmpty(value)) {
                return BigDecimal.ZERO;
            }
            return new BigDecimal(value);
        } catch (Exception e) {
            log.error("Step150SourcingBeforeDisassemble.getExpressStartingWeight.error={}",
                    Throwables.getStackTraceAsString(e));
            return BigDecimal.ZERO;
        }
    }

    private BigDecimal getItemsWeight(List<OcBOrderItem> ocOrderItems) {
        if (CollectionUtils.isEmpty(ocOrderItems)) {
            return BigDecimal.ZERO;
        }
        BigDecimal itemsWeight = BigDecimal.ZERO;
        for (OcBOrderItem orderItem : ocOrderItems) {
            BigDecimal standardWeight = Optional.ofNullable(orderItem.getStandardWeight()).orElse(BigDecimal.ZERO);
            BigDecimal qty = orderItem.getQty();
            itemsWeight = itemsWeight.add(qty.multiply(standardWeight));
        }
        return itemsWeight;
    }

}
