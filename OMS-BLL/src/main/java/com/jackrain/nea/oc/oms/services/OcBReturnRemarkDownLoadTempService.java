package com.jackrain.nea.oc.oms.services;

import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: xiWen.z
 * create at: 2019/9/17 0017
 */
@Slf4j
@Component
public class OcBReturnRemarkDownLoadTempService {

    @Autowired
    private ExportUtil exportUtil;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    public ValueHolderV14 downLoadRemarkTemp(User usr) {
        ValueHolderV14 vh = new ValueHolderV14();
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            timeout = "1800000";
        }
        exportUtil.setTimeout(timeout);
        /**
         * 表头字段
         */
        String[] header = {"平台单号", "备注", "旗帜", "旗帜颜色:0, 无1, 红2, 橙 3, 绿4,蓝5, 紫"};
        List headerList = Lists.newArrayList(header);
        //生成Excel
        XSSFWorkbook hasWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hasWorkbook, "退换货订单备注", "", headerList, Lists.newArrayList(), Lists.newArrayList(), false);
        String url = exportUtil.saveFileAndPutOss(hasWorkbook, "退换货订单备注修改数据导入模板", usr, "OSS-Bucket/EXPORT/OC_B_RETURN_ORDER/");
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("退换货备注导入模板下载成功！");
        vh.setData(url);
        return vh;
    }
}
