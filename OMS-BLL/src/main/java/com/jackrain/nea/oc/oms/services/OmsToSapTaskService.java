package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
import com.jackrain.nea.oc.oms.sap.SapTaskTableEnum;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: oms传Sap中间task表服务（B2C、互道）
 * @author: zhu<PERSON>ofan
 * @create: 2020-08-22 10:16
 */
@Component
@Slf4j
public class OmsToSapTaskService {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private Oms2SapMapper oms2SapMapper;


    /**
     * 订单插入sap task中间表
     * 通过判断实体仓中SAP是否是SAP管控仓传SAP中间表
     *
     * @param ocBOrder 订单信息
     */
    public void isToSapOrder(OcBOrder ocBOrder) {
        if (null != ocBOrder.getCpCPhyWarehouseId()) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(ocBOrder.getCpCPhyWarehouseId());
            if (null != cpCPhyWarehouse) {
                if (cpCPhyWarehouse.getIsSap() == 1) {
                    Long sapId = ModelUtil.getSequence(SapTaskTableEnum.ORDER.txt());
                    oms2SapMapper.insertSingleTaskOrder(SapTaskTableEnum.ORDER.txt(), ocBOrder.getId(), sapId);
                }
            }
        }
    }

    /**
     * 退单插入sap task中间表
     * 通过判断实体仓中SAP是否是SAP管控仓传SAP中间表
     *
     * @param user
     * @param ocBReturnOrder
     */
    public void isToSapReturnOrder(User user, OcBReturnOrder ocBReturnOrder) {
        try {
            if (null != ocBReturnOrder.getCpCPhyWarehouseInId()) {
                CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(ocBReturnOrder.getCpCPhyWarehouseInId());
                if (log.isDebugEnabled()) {
                    log.debug(this.getClass().getName() + "CpRpcExtService.isToSapReturnOrder.cpCPhyWarehouse.实体仓信息:" + JSONObject.toJSONString(cpCPhyWarehouse));
                }
                if (null != cpCPhyWarehouse) {
                    if (cpCPhyWarehouse.getIsSap() == 1) {
                        Integer billType = ocBReturnOrder.getBillType();
                        if (billType != null) {
                            Long sapId = ModelUtil.getSequence(SapTaskTableEnum.RETURN.txt());
                            oms2SapMapper.insertSingleTaskOrder(SapTaskTableEnum.RETURN.txt(), ocBReturnOrder.getId(), sapId);
                            log.debug(this.getClass().getName() + "CpRpcExtService.isToSapReturnOrder.Insert TaskOrder主键ID: {},退单id：{}", sapId, ocBReturnOrder.getId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.isToSapReturnOrder.error,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
    }

}