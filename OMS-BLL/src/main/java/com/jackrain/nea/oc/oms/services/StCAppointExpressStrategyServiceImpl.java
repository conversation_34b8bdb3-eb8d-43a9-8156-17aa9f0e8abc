package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.oc.oms.mapper.StCAppointExpressStrategyMapper;
import com.jackrain.nea.st.model.StCAppointExpressStrategy;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * @ClassName StCAppointExpressStrategyServiceImpl
 * @Description 指定快递
 * <AUTHOR>
 * @Date 2024/4/12 15:45
 * @Version 1.0
 */
@Slf4j
@Service
public class StCAppointExpressStrategyServiceImpl extends ServiceImpl<StCAppointExpressStrategyMapper, StCAppointExpressStrategy> implements StCAppointExpressStrategyService {

    @Override
    public Boolean updateActive(User user, List<Long> objIds, String isActive) {
        Collection<StCAppointExpressStrategy> collections = listByIds(objIds);
        if (CollectionUtils.isEmpty(collections)) {
            return true;
        }
        collections.forEach(o -> {
            o.setIsactive(isActive);
            o.setModifieddate(new Date());
            o.setModifierid(user.getId().longValue());
            o.setModifierename(user.getEname());
            o.setModifiername(user.getName());
        });
        updateBatchById(collections);
        return true;
    }

    @Override
    public StCAppointExpressStrategy getByShopId(Long shopId) {
        return baseMapper.selectOne(new QueryWrapper<StCAppointExpressStrategy>().lambda()
                .eq(StCAppointExpressStrategy::getShopId, shopId).eq(StCAppointExpressStrategy::getIsactive, "Y"));
    }

    @Override
    public StCAppointExpressStrategy getCommonStrategy() {
        return baseMapper.selectOne(new QueryWrapper<StCAppointExpressStrategy>().lambda()
                .eq(StCAppointExpressStrategy::getStrategyType, 1).eq(StCAppointExpressStrategy::getIsactive, "Y"));
    }
}
