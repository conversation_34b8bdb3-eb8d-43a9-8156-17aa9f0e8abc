package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 中间表淘宝订单处理服务
 *
 * @author: 易邵峰
 * @since: 2019-01-22
 * create at : 2019-01-22 15:23
 */
@Component
@Slf4j
public class IpTaobaoOrderService {

    /**
     * 周期购
     */
    private final static String CYCLE_BUY = "CYCLE_BUY";

    @Autowired
    private IpBTaobaoOrderMapper ipTaobaoOrderMapper;

    @Autowired
    private IpBTaobaoOrderItemMapper orderItemMapper;

    @Autowired
    private IpBTaobaoOrderPromotionMapper orderPromotionMapper;

    @Autowired
    private IpBTaobaoModifyAddrMapper ipBTaobaoModifyAddrMapper;

    @Autowired
    private IpBTaobaoOrderCycleBuyMapper ipBTaobaoOrderCycleBuyMapper;


    /**
     * 依据OrderNo(即TID)进行查询中间表淘宝信息数据
     *
     * @param orderNo 淘宝订单号
     * @return 中间表淘宝信息数据
     */
    public IpTaobaoOrderRelation selectTaobaoOrderByTid(String orderNo) {
        IpBTaobaoOrder orderInfo = this.ipTaobaoOrderMapper.selectTaobaoOrderByTid(orderNo);

        if (orderInfo == null) {
            return null;
        }

        IpTaobaoOrderRelation taoBaoOrderRelation = new IpTaobaoOrderRelation();
        long taobaoOrderId = orderInfo.getId();
        List<IpBTaobaoOrderItemEx> orderItemList = this.orderItemMapper.selectOrderItemList(taobaoOrderId);

        List<IpBTaobaoOrderPromotion> orderPromotionList = this.orderPromotionMapper
                .selectOrderPromotionList(taobaoOrderId);

        //查询周期购信息
        if (CYCLE_BUY.equals(orderInfo.getBusinessType())) {
            List<IpBTaobaoOrderCycleBuy> ipBTaobaoOrderCycleBuys = ipBTaobaoOrderCycleBuyMapper.selectOrderCycleBuyList(taobaoOrderId);
            taoBaoOrderRelation.setTaobaoOrderCycleBuyList(ipBTaobaoOrderCycleBuys);
        }

        taoBaoOrderRelation.setTaobaoOrderItemList(orderItemList);
        taoBaoOrderRelation.setTaobaoOrderPromotionList(orderPromotionList);
        taoBaoOrderRelation.setTaobaoOrder(orderInfo);

        // 由于在订单合单处理过程中，会对订单进行ES查询操作，而ES查询框架采用英文逗号【,】作为系统特殊符号，
        // 如果地址带有英文逗号【,】则会导致ES查询出现异常。
        // 因此需要进行替换操作
        if (StringUtils.isNotEmpty(orderInfo.getReceiverAddress())) {
            String receiverAddress = StringUtils.replace(orderInfo.getReceiverAddress(), ",", "::::");
            orderInfo.setReceiverAddress(receiverAddress);
        }

        return taoBaoOrderRelation;
    }

    /**
     * 更新淘宝中间表订单状态值
     *
     * @param orderNo             订单编号
     * @param transferOrderStatus 转换订单状态
     * @param remarks             备注信息
     * @return 更新是否成功。true-成功；false-失败
     */
    public boolean updateTaobaoOrderTransStatus(String orderNo, TransferOrderStatus transferOrderStatus,
                                                String remarks) {
        boolean isUpdateTransNum = transferOrderStatus == TransferOrderStatus.TRANSFERRED;
        if (remarks != null && remarks.length() > IpBTaobaoOrderMapper.MAX_REMARK_LENGTH) {
            remarks = remarks.substring(0, IpBTaobaoOrderMapper.MAX_REMARK_LENGTH - 1);
        }

        int result = this.ipTaobaoOrderMapper.updateOrderIsTrans(orderNo, transferOrderStatus.toInteger(),
                isUpdateTransNum, remarks);
        return result > 0;
    }


    public boolean updateTaobaoOrderTransAndAbnormal(String orderNo, TransferOrderStatus transferOrderStatus,
                                                     String remarks, Integer abnormalType) {
        if (remarks != null && remarks.length() > IpBTaobaoOrderMapper.MAX_REMARK_LENGTH) {
            remarks = remarks.substring(0, IpBTaobaoOrderMapper.MAX_REMARK_LENGTH - 1);
        }

       return this.ipTaobaoOrderMapper.updateOrderIsTransAndAbnormal(orderNo, transferOrderStatus.toInteger(), remarks, abnormalType)>0;
    }

    /**
     * 根据平台单号查询淘宝修改地址表是否更新字段
     *
     * @param sourcecode 平台单号
     * @return 是否更新
     */
    public IpBTaobaoModifyAddr selectSourcecodeByIsUpdate(String sourcecode) {
        return ipBTaobaoModifyAddrMapper.selectSourcecodeByIsUpdate(sourcecode);
    }

    /**
     * 根据node查询OrderNo
     *
     * @param node    分库名
     * @param name    分库表名
     * @param size    查询数量
     * @param isTrans 转换状态
     * @param remarks 备注信息(值为null时不作为查询条件)
     * @param minutes 查询n分钟之前的数据(值为0时不作为查询条件)
     * @return OrderNo集合
     */
    public List<String> selectDynamicTaskOrder(String node, String name, int size, int isTrans, String remarks, int minutes) {
        return ipTaobaoOrderMapper.selectDynamicTaskOrder(node, name, size, isTrans, remarks, minutes);
    }

    /**
     * 更新淘宝订单的备注信息
     *
     * @param orderNoList         订单编号集合
     * @param remarks             转换备注信息
     * @param whenSysRemarkIsNull 更新时使用SysRemark=Null作为条件
     * @return int
     */
    public int updateSysRemark(List<String> orderNoList, String remarks, boolean whenSysRemarkIsNull) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0, l = orderNoList.size(); i < l; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(orderNoList.get(i));
        }

        if (whenSysRemarkIsNull) {
            return ipTaobaoOrderMapper.updateSysRemarkWhenItIsNull(sb.toString(), remarks);
        }
        return ipTaobaoOrderMapper.updateSysRemark(sb.toString(), remarks);
    }


}
