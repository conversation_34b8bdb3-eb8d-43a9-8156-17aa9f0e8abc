package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.es.ES4RefundIn;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatus;
import com.jackrain.nea.oc.oms.model.relation.ScanIncomingRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-08-26
 * create at : 2019-08-26 3:43 PM
 * 扫描入库补偿任务处理类
 */
@Slf4j
@Component
public class ScanIncomingAutoService {

    @Autowired
    OcBRefundInMapper refundInMapper;

    @Autowired
    OcBRefundInProductItemMapper itemMapper;

    @Autowired
    ScanIncomingLogicService scanIncomingUpdateService;

    public void doScanIncoming(Integer range) {
        List<Long> ids = ES4RefundIn.findRefundInIdsByRange(range);
        List<OcBRefundIn> ocBRefundIns = refundInMapper.selectBatchIds(ids);
        //筛选数据
        List<ScanIncomingRelation> relations = new ArrayList<>();
        ScanIncomingRelation relation;
        QueryWrapper<OcBRefundInProductItem> queryWrapper;
        for (OcBRefundIn bean : ocBRefundIns) {
            relation = new ScanIncomingRelation();
            queryWrapper = new QueryWrapper<>();
            if (bean.getQtyFail() == null || bean.getQtyFail() <= 5) {
                queryWrapper.eq("oc_b_refund_in_id", bean.getId());
                List<OcBRefundInProductItem> items = itemMapper.selectList(queryWrapper);
                if (items != null && items.size() != 0) {
                    relation.setOcBRefundIn(bean);
                    relation.setItemList(items);
                    relations.add(relation);
                }
            }
        }

        for (ScanIncomingRelation relationData : relations) {
            try {
                scanIncomingUpdateService.toDoScanIncoming(relationData);
            } catch (Exception ex) {
                log.error(LogUtil.format("扫描入库异常id:{},异常信息为:{}",relationData.getRefundInId()), relationData.getRefundInId(),Throwables.getStackTraceAsString(ex));
                OcBRefundIn refundIn = new OcBRefundIn();
                refundIn.setId(relationData.getRefundInId());
                Long failCount = relationData.getOcBRefundIn().getQtyFail();
                if (failCount == null) {
                    failCount = 1L;
                } else {
                    failCount += 1;
                }
                refundIn.setQtyFail(failCount);
                refundIn.setRemarkIn(ex.getMessage());
                //入库状态改为异常
                refundIn.setInStatus(ReturnStatus.REFUND_EXCEPTION.toInteger());
                refundInMapper.updateById(refundIn);
               /* try {
                    SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME,
                            OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME,
                            refundInMapper.selectById(relationData.getOcBRefundIn()), relationData.getOcBRefundIn());
                } catch (IOException e) {
                    e.printStackTrace();
                }*/
            }
        }
    }
}
