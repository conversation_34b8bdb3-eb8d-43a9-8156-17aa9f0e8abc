package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBRefundBatchMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRefundBatch;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.nums.BatchStatus;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.psext.model.table.PsCSpecobj;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 退货批次管理完结服务
 *
 * @author: 孙继东
 * @since: 2019-03-26
 * create at : 2019-03-26 10:21
 */
@Slf4j
@Component
public class RefundBatchEndService {
    @Autowired
    private OcBRefundBatchMapper ocBRefundBatchMapper;
    @Autowired
    private OcBRefundInMapper ocBRefundInMapper;
    @Autowired
    private OcBRefundInProductItemMapper ocBRefundInProductItemMapper;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    /**
     * 完结服务
     *
     * @param querySession 前端数据
     * @return 返回结果
     */
    public ValueHolder execute(QuerySession querySession) {
        ValueHolder holder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (param != null) {
            JSONArray ids = param.getJSONArray("ids");
            JSONArray data = new JSONArray();
            int fail = 0;
            if (CollectionUtils.isNotEmpty(ids)) {
                a:
                for (int k = 0; k < ids.size(); k++) {
                    JSONObject jj = new JSONObject();
                    String lockRedisKey = BllRedisKeyResources.buildLockReturnInKey(NumberUtils.toLong(ids.get(k).toString()));
                    RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                    try {
                        if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                            OcBRefundBatch refundBatch = ocBRefundBatchMapper.selectById(ids.get(k).toString());
                            //判断退货批次中的物流公司、收货人、收货人手机、收货人省、市、区、收货人详细地址信息是否存在为空，
                            // 若有为空的字段，则返回提示：“退货批次信息需要填写完整才能完结”，若信息不为空
                            if (refundBatch.getCpCLogisticsId() == null || refundBatch.getReceiverName() == null
                                    || refundBatch.getReceiverMobile() == null || refundBatch.getReceiverProvinceId() == null
                                    || refundBatch.getReceiverCityId() == null || refundBatch.getReceiverDistrictId() == null
                                    || refundBatch.getReceiverAddress() == null) {
                                jj.put("code", -1);
                                jj.put("message", Resources.getMessage("批次号" + refundBatch.getBatchNo() + ":退货批次信息需要填写完整才能完结"));
                                jj.put("objid", ids.get(k));
                                fail++;
                                data.add(jj);
                                continue;
                            }
                            if (refundBatch.getBatchType() != null && refundBatch.getBatchType() == 2) {
                                jj.put("code", -1);
                                jj.put("message", Resources.getMessage("批次号" + refundBatch.getBatchNo() + ":类型为门店,不允许完结"));
                                jj.put("objid", ids.get(k));
                                fail++;
                                data.add(jj);
                                continue;
                            }
                            if (refundBatch != null) {
                                Integer batchStatus = refundBatch.getBatchStatus();
                                //已完结(2)、已作废(3)，提示：“此退货批次单状态不正确，不能完结”；
                                if (BatchStatus.BATCHSTATUS_YES.equals(batchStatus) || BatchStatus.BATCHSTATUS_OBSOLETE.equals(batchStatus)) {
                                    jj.put("code", -1);
                                    jj.put("message", Resources.getMessage("批次号" + refundBatch.getBatchNo() + ":此退货批次单状态不正确，不能完结"));
                                    jj.put("objid", ids.get(k));
                                    fail++;
                                    data.add(jj);
                                    continue;
                                } else if (BatchStatus.BATCHSTATUS_NO.equals(batchStatus)) {
                                    //未完结(1)，根据传入的退换货批次，查找所有此退货批次的退货入库单，
                                    // 若查找不到任何数据，则提示：“此退货批次未生成退货入库单，不能完结”；
                                    List<OcBRefundIn> refundIns = ocBRefundInMapper.selectList(new QueryWrapper<OcBRefundIn>().eq("batch_no", refundBatch.getBatchNo()));
                                    if (CollectionUtils.isEmpty(refundIns)) {
                                        jj.put("code", -1);
                                        jj.put("message", Resources.getMessage("批次号" + refundBatch.getBatchNo() + ":此退货批次未生成退货入库单，不能完结"));
                                        jj.put("objid", ids.get(k));
                                        fail++;
                                        data.add(jj);
                                        continue;
                                    } else {
                                        //若查找到退货入库单数据，则根据退货入库单的入库明细，生成一张调拨单
                                        //调拨单自动出库
//                                        SgTransferBillSaveRequest sgTransferBillSaveRequest = new SgTransferBillSaveRequest();
//                                        sgTransferBillSaveRequest.setLoginUser(querySession.getUser());
//                                        sgTransferBillSaveRequest.setObjId(-1L);
//
//                                        SgTransferSaveRequest transferSaveRequest = getSgTransferSaveRequest(refundBatch);
//                                        sgTransferBillSaveRequest.setTransfer(transferSaveRequest);
//                                        List<SgTransferItemSaveRequest> list_z = new ArrayList<>();
//                                        List<SgTransferItemSaveRequest> list_c = new ArrayList<>();
                                        for (int i = 0; i < refundIns.size(); i++) {
                                            OcBRefundIn ocBRefundIn = refundIns.get(i);
                                            List<OcBRefundInProductItem> refundInProductItems = ocBRefundInProductItemMapper.selectList(new QueryWrapper<OcBRefundInProductItem>().eq("oc_b_refund_in_id", ocBRefundIn.getId()));
                                            log.debug("退货入库商品数据" + refundInProductItems);
                                            if (CollectionUtils.isNotEmpty(refundInProductItems)) {
                                                for (int j = 0; j < refundInProductItems.size(); j++) {
                                                    OcBRefundInProductItem refundInProductItem = refundInProductItems.get(j);
                                                    Long psCProId = refundInProductItem.getPsCProId();
//                                                    SgTransferItemSaveRequest item = new SgTransferItemSaveRequest();
//                                                    //调拨数量为入库单明细的数量,
//                                                    item.setGbcode(refundInProductItem.getGbcode());
//                                                    item.setQty(refundInProductItem.getQty());
//                                                    item.setId(-1L);
//                                                    item.setPsCProId(psCProId);
//                                                    item.setPsCProEcode(refundInProductItem.getPsCProEcode());
//                                                    item.setPsCProEname(refundInProductItem.getPsCProEname());
                                                    //调拨条码为入库单明细发出条码（如果有实收条码则为实收条码）
                                                    //实收条码
                                                    String realSkuEcode = refundInProductItem.getRealSkuEcode();
                                                    //条码
                                                    String psCSkuEcode = refundInProductItem.getPsCSkuEcode();
                                                    JSONObject jsonObject = new JSONObject();
                                                    if (realSkuEcode != null) {
//                                                        item.setPsCSkuEcode(realSkuEcode);
//                                                        item.setPsCSkuId(refundInProductItem.getRealSkuId());
                                                        jsonObject = psRpcService.querySkuinfoByProIdAndEcode(psCProId, realSkuEcode);


                                                    } else {
//                                                        item.setPsCSkuEcode(psCSkuEcode);
//                                                        item.setPsCSkuId(refundInProductItem.getPsCSkuId());
                                                        jsonObject = psRpcService.querySkuinfoByProIdAndEcode(psCProId, psCSkuEcode);
                                                    }
                                                    //item.setPriceList(jsonObject.getBigDecimal("PRICE_LIST"));
                                                    String ps_c_specobj1 = jsonObject.getString("PS_C_SPECOBJ1");
                                                    if (StringUtils.isNotEmpty(ps_c_specobj1)) {
                                                        PsCSpecobj psCSpecobj1 = JSON.parseObject(ps_c_specobj1, new TypeReference<PsCSpecobj>() {
                                                        });
//                                                        item.setPsCClrEname(psCSpecobj1.getEname());
//                                                        item.setPsCClrEcode(psCSpecobj1.getEcode());
//                                                        item.setPsCClrId(psCSpecobj1.getId());
                                                    } else {
                                                        if (log.isDebugEnabled()) {
                                                            log.debug("颜色为空，商品明细id=" + refundInProductItem.getId() + ",商品id=" + psCProId + ",条码=" + psCSkuEcode);
                                                        }
                                                        jj.put("code", -1);
                                                        jj.put("message", Resources.getMessage("批次号" + refundBatch.getBatchNo() + ":入库商品颜色参数为空"));
                                                        jj.put("objid", ids.get(k));
                                                        fail++;
                                                        data.add(jj);
                                                        continue a;
                                                    }
                                                    String ps_c_specobj2 = jsonObject.getString("PS_C_SPECOBJ2");
                                                    if (StringUtils.isNotEmpty(ps_c_specobj2)) {
                                                        PsCSpecobj psCSpecobj2 = JSON.parseObject(ps_c_specobj2, new TypeReference<PsCSpecobj>() {
                                                        });
//                                                        item.setPsCSizeId(psCSpecobj2.getId());
//                                                        item.setPsCSizeEcode(psCSpecobj2.getEcode());
//                                                        item.setPsCSizeEname(psCSpecobj2.getEname());
                                                    } else {
                                                        if (log.isDebugEnabled()) {
                                                            log.debug("尺寸为空，商品明细id=" + refundInProductItem.getId() + ",商品id=" + psCProId + ",条码=" + psCSkuEcode);
                                                        }
                                                        jj.put("code", -1);
                                                        jj.put("message", Resources.getMessage("批次号" + refundBatch.getBatchNo() + ":入库商品尺寸参数为空"));
                                                        jj.put("objid", ids.get(k));
                                                        fail++;
                                                        data.add(jj);
                                                        continue a;
                                                    }
                                                    if ("1".equals(refundInProductItem.getProductMark())) {
                                                        //正品
                                                        //list_z.add(item);
                                                    } else {
                                                        //次品
                                                        //list_c.add(item);
                                                    }
                                                }
                                            } else {
                                                jj.put("code", -1);
                                                jj.put("message", Resources.getMessage("批次号" + refundBatch.getBatchNo() + ":未找到对应的退货入库商品明细"));
                                                jj.put("objid", ids.get(k));
                                                fail++;
                                                data.add(jj);
                                                continue a;
                                            }
                                        }
                                        refundBatch.setBatchStatus(BatchStatus.BATCHSTATUS_ING);
                                        refundBatch.setRemark("当前完结数量较多，正在完结中，请等待");
                                        ocBRefundBatchMapper.updateById(refundBatch);
                                        //完结链路长导致超时，先给前端响应，后面再同步状态
                                        //autoResult(refundBatch, sgTransferBillSaveRequest, transferSaveRequest, list_z, list_c);
                                    }
                                }
                            }
                        } else {
                            //失败
                            jj.put("code", -1);
                            jj.put("message", Resources.getMessage("当前订单其他人在操作，请稍后再试!"));
                            jj.put("objid", ids.get(k));
                            fail++;
                            data.add(jj);
                        }
                    } catch (Exception e) {
                        log.error(LogUtil.format("完结异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    } finally {
                        redisLock.unlock();
                    }
                }
            } else {
                throw new NDSException(Resources.getMessage("请选择一条数据后在操作"));
            }
            holder.put("data", data);
            if (fail > 0) {
                holder.put("code", -1);
                holder.put("message", "正在完结记录数：" + (ids.size() - fail) + "，完结失败记录数：" + fail);
            } else {
                holder.put("code", 0);
                holder.put("message", "正在完结记录数：" + ids.size());
            }
        }
        return holder;
    }

    /**
     * 调拨单自动出库服务
     *
     * @param refundBatch
     * @param sgTransferBillSaveRequest
     * @param transferSaveRequest
     * @param list_z
     * @param list_c
     */
//    private void autoResult(OcBRefundBatch refundBatch, SgTransferBillSaveRequest sgTransferBillSaveRequest, SgTransferSaveRequest transferSaveRequest, List<SgTransferItemSaveRequest> list_z, List<SgTransferItemSaveRequest> list_c) {
//        Executors.newSingleThreadExecutor().execute(() -> {
//            //完结时，根据退货入库单中商品明细正次品生成不同的调拨单。
//            int Z = 0, C = 0;
//            // 当商品标记为正品时，则生成调出仓库为销退入库正品仓，调入仓库为调拨入库仓库的调拨单。
//            ValueHolderV14 holderV14 = new ValueHolderV14();
//            if (CollectionUtils.isNotEmpty(list_z)) {
//                sgTransferBillSaveRequest.setItems(list_z);
//                //发货店仓为退货批次的销退入库正品仓
//                transferSaveRequest.setCpCOrigEcode(refundBatch.getInStoreEcode());
//                transferSaveRequest.setCpCOrigEname(refundBatch.getInStoreEname());
//                transferSaveRequest.setCpCOrigId(refundBatch.getInStoreId());
//
//                //收货店仓为退货批次的调拨入库仓库
//                transferSaveRequest.setCpCDestEcode(refundBatch.getTransferStoreEcode());
//                transferSaveRequest.setCpCDestEname(refundBatch.getTransferStoreEname());
//                transferSaveRequest.setCpCDestId(refundBatch.getTransferStoreId());
//
//                holderV14 = sgRpcService.autoOut(sgTransferBillSaveRequest);
//                if (log.isDebugEnabled()) {
//                    log.debug("正品调用调拨单自动出库,入参：" + sgTransferBillSaveRequest.toString() + ",出参：" + holderV14.toString());
//                }
//                Z = holderV14.getCode() == 0 ? 1 : -2;
//            }
//            // 当商品标记为次品时，则生成调出仓库为销退入库正品仓，调入仓库为销退入库次品仓的调拨单。
//            ValueHolderV14 valueHolderV14 = new ValueHolderV14();
//            if (CollectionUtils.isNotEmpty(list_c) && Z >= 0) {
//                sgTransferBillSaveRequest.setItems(list_c);
//                //发货店仓为退货批次的销退入库正品仓
//                transferSaveRequest.setCpCOrigEcode(refundBatch.getInStoreEcode());
//                transferSaveRequest.setCpCOrigEname(refundBatch.getInStoreEname());
//                transferSaveRequest.setCpCOrigId(refundBatch.getInStoreId());
//
//                //收货店仓为退货批次的销退入库次品仓
//                transferSaveRequest.setCpCDestEcode(refundBatch.getReturnPhyWarehouseEcode());
//                transferSaveRequest.setCpCDestEname(refundBatch.getReturnPhyWarehouseEname());
//                transferSaveRequest.setCpCDestId(refundBatch.getReturnPhyWarehouseId());
//
//                valueHolderV14 = sgRpcService.autoOut(sgTransferBillSaveRequest);
//                if (log.isDebugEnabled()) {
//                    log.debug("次品调用调拨单自动出库,入参：" + sgTransferBillSaveRequest.toString() + ",出参：" + valueHolderV14.toString());
//                }
//                C = valueHolderV14.getCode() == 0 ? 1 : -2;
//            }
//            if (Z + C > 0) {
//                //调拨成功，更新退货批次的状态为完结,更新正次品数量
//                refundBatch.setBatchStatus(BatchStatus.BATCHSTATUS_YES);
//                refundBatch.setQty(BigDecimal.valueOf(list_z.size()));
//                refundBatch.setQtySubstandard(BigDecimal.valueOf(list_c.size()));
//                refundBatch.setRemark("完结成功");
//                ocBRefundBatchMapper.updateById(refundBatch);
//            } else {
//                //失败
//                refundBatch.setBatchStatus(BatchStatus.BATCHSTATUS_NO);
//                refundBatch.setRemark("完结失败,调拨单自动出库失败,正品失败原因:" + holderV14.getMessage() + ",次品失败原因：" + valueHolderV14.getMessage());
//                ocBRefundBatchMapper.updateById(refundBatch);
//            }
//        });
//    }

    /**
     * 设置参数
     *
     * @param refundBatch
     * @return
     */
//    private SgTransferSaveRequest getSgTransferSaveRequest(OcBRefundBatch refundBatch) {
//        SgTransferSaveRequest transferSaveRequest = new SgTransferSaveRequest();
//        //物流公司信息
//        transferSaveRequest.setCpCLogisticsId(refundBatch.getCpCLogisticsId());
//        transferSaveRequest.setCpCLogisticsEcode(refundBatch.getCpCLogisticsEcode());
//        transferSaveRequest.setCpCLogisticsEname(refundBatch.getCpCLogisticsEname());
//
//        //收货人信息
//        transferSaveRequest.setReceiverName(refundBatch.getReceiverName());
//        transferSaveRequest.setReceiverMobile(refundBatch.getReceiverMobile());
//        transferSaveRequest.setReceiverProvinceId(refundBatch.getReceiverProvinceId());
//        transferSaveRequest.setReceiverProvinceEname(refundBatch.getReceiverProvinceEname());
//        transferSaveRequest.setReceiverCityId(refundBatch.getReceiverCityId());
//        transferSaveRequest.setReceiverCityEname(refundBatch.getReceiverCityEname());
//        transferSaveRequest.setReceiverDistrictId(refundBatch.getReceiverDistrictId());
//        transferSaveRequest.setReceiverDistrictEname(refundBatch.getReceiverDistrictEname());
//        transferSaveRequest.setReceiverAddress(refundBatch.getReceiverAddress());
//        //正常调拨
//        transferSaveRequest.setTransferType(SgTransferConstantsIF.TRANSFER_TYPE_NORMAL);
//        //发货类型 自提
//        transferSaveRequest.setSendType(SgTransferConstantsIF.SEND_TYPE_ZT);
//
//        //批次号
//        transferSaveRequest.setBatchNo(refundBatch.getBatchNo());
//        return transferSaveRequest;
//    }
}
