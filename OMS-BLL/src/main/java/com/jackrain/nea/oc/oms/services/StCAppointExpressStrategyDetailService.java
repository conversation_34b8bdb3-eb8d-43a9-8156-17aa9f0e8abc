package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jackrain.nea.st.model.StCAppointExpressStrategyDetail;

import java.util.List;

/**
 * @ClassName StCAppointExpressStrategyDetailService
 * @Description 指定快递明细
 * <AUTHOR>
 * @Date 2024/4/12 17:16
 * @Version 1.0
 */
public interface StCAppointExpressStrategyDetailService extends IService<StCAppointExpressStrategyDetail> {


    /**
     * 根据匹配规则+匹配内容 来查询是否存在数据
     *
     * @param matchRule    匹配规则
     * @param matchContent 匹配内容
     * @return
     */
    StCAppointExpressStrategyDetail getByMatchRuleAndMatchContent(Long mainId, String matchRule, String matchContent);

    /**
     * 根据策略id 获取策略信息
     *
     * @param strategyId
     * @return
     */
    List<StCAppointExpressStrategyDetail> selectByStrategyId(Long strategyId);
}
