package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.services.refund.OcBReturnOrderLogService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * description：
 *
 * <AUTHOR>
 * @date 2021/11/9
 */
@Slf4j
@Component
public class ReturnOrderPushWmsService {

    public static final int SPLIT_SIZE = 200;

    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;

    @Autowired
    private OcBReturnOrderRefundMapper returnOrderRefundMapper;

    @Autowired
    private IpRpcService service;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OmsReturnOrderService omsReturnOrderService;

    @Autowired
    private OcBReturnOrderLogService returnOrderLogService;

    public void pushData(Integer range) {

        boolean ifControl = businessSystemParamService.getReturnOrderToWmsControl();
        List<OcBReturnOrder> ocBReturnOrders = returnOrderMapper.selectReturnOrderToWmsTask(range, ifControl, 6);
        List<List<OcBReturnOrder>> partitionList = Lists.partition(ocBReturnOrders, businessSystemParamService.getReturnOrderTransferWmsNum());

        for (List<OcBReturnOrder> subList : partitionList) {
            List<OcBReturnOrder> dataList = Lists.newArrayList();
            List<RedisReentrantLock> lockList = Lists.newArrayList();
            try {
                for (OcBReturnOrder returnOrder : subList) {
                    String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(returnOrder.getId());
                    RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        dataList.add(returnOrder);
                        lockList.add(redisLock);
                    } else {
                        log.error(LogUtil.format("退单传WMS定时任务上锁失败，单据正在操作中={}", returnOrder.getBillNo()), returnOrder.getBillNo());
                    }
                }
                doReturnOrderToWms(dataList);
            } catch (Exception e) {
                log.error("退单传WMS任务异常", e);
            } finally {
                //批量解锁
                try {
                    lockList.forEach(lock -> lock.unlock());
                } catch (Exception e) {
                    log.error(LogUtil.format("退单传WMS定时任务释放锁失败:{}"), Throwables.getStackTraceAsString(e));
                }
            }
        }
    }

    /**
     * wms新建退单
     *
     * @param returnOrders 退单集合
     */
    public void doReturnOrderToWms(List<OcBReturnOrder> returnOrders) {
        List<Long> returnIdList = returnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
        String ids = StringUtils.join(returnIdList, ",");
        try {
            List<OcBReturnOrderRefund> ocBReturnOrderRefunds = new ArrayList<>();
            List<OcBReturnOrderRefund> list = returnOrderRefundMapper.selectByOcOrderIdsStr(ids);
            if (CollectionUtils.isNotEmpty(list)) {
                ocBReturnOrderRefunds.addAll(list);
            }
            // 批量更新退单状态为传中
            returnOrderMapper.updateWmsStatusByIds(ids,
                    WmsWithdrawalState.PASS.toInteger(), 0, "");
            // 批量新增日志
            returnOrderLogService.batchSaveOrderLogInfo(returnIdList,
                    "退单传WMS", "传WMS处理中", SystemUserResource.getRootUser());

            service.pushReturnOrderToWms(returnOrders, ocBReturnOrderRefunds);
        } catch (Exception e) {
            returnOrderMapper.updateWmsStatusByIds(ids,
                    WmsWithdrawalState.NO.toInteger(), 0, e.getMessage());
            returnOrderLogService.batchSaveOrderLogInfo(returnIdList,
                    "退单传WMS", String.format("传WMS失败，原因：%s", e.getMessage()), SystemUserResource.getRootUser());
        }
    }


    /**
     * 设置失败备注信息
     *
     * @param returnOrder
     * @param user
     * @param remark
     */
    public void setFailRemarkMessage(OcBReturnOrder returnOrder, User user, String remark) throws Exception {
        OcBReturnOrder returnOrderExt = new OcBReturnOrder();
        returnOrderExt.setId(returnOrder.getId());
        returnOrderExt.setRemark(remark);
        returnOrderExt.setModifieddate(new Date());
        returnOrderExt.setModifierid(user.getId() + 0L);
        returnOrderExt.setModifiername(user.getName());
        returnOrderExt.setModifierename(user.getEname());
        returnOrderMapper.updateById(returnOrderExt);
    }

    /**
     * <AUTHOR>
     * @Date 16:43 2021/5/20
     * @Description 验证不传wms的条件
     */
    private boolean checkLogisticsCodeAndPushDelayTime(boolean logisticsStrict, OcBReturnOrder ocBReturnOrder) {
        if (logisticsStrict) {
            return StringUtils.isNotEmpty(ocBReturnOrder.getLogisticsCode());
        } else {
            if (StringUtils.isNotEmpty(ocBReturnOrder.getLogisticsCode())) {
                return true;
            } else {
                return checkPushDelayTime(ocBReturnOrder);
            }
        }
    }

    /**
     * <AUTHOR>
     * @Date 16:55 2021/5/19
     * @Description 判断当前空运单号延迟推单有效时间
     */
    private boolean checkPushDelayTime(OcBReturnOrder ocBReturnOrder) {
        Date pushDelayTime = ocBReturnOrder.getPushDelayTime();
        if (pushDelayTime == null) {
            return false;
        }
        return pushDelayTime.before(new Date()) || pushDelayTime == new Date();
    }

    private void batchUpdateModifiedDate(List<Long> preList) {
        int size = preList.size(), startIndex = 0;
        int length = size, eachSize = 200;
        List<Long> subList;
        while (size > 0) {
            if (size > eachSize) {
                subList = preList.subList(startIndex, startIndex + eachSize);
                startIndex += eachSize;
            } else {
                subList = preList.subList(startIndex, length);
            }
            returnOrderMapper.updateModifiedDateByIds(subList);
            size -= eachSize;
        }
    }
}

