package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.basic.model.result.SgSumRedisStorageQueryResult;
import com.burgeon.r3.sg.core.model.ext.SgBStorageInclShare;
import com.github.pagehelper.util.StringUtil;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderSkuSplitTaskMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderSplitTaskMapper;
import com.jackrain.nea.oc.oms.model.SplitOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderSource;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSkuSplitTask;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSplitTask;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.util.BigDecimalUtil;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.SendMQAsyncUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 拆分缺货订单服务
 *
 * @author: WangShuai
 * create at: 2020/12/04
 */
@Slf4j
@Component
public class SplitOutStockOrderService {


    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OmsSyncStockStrategyService syncStockStrategyService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OcBOrderSplitTaskMapper ocBOrderSplitTaskMapper;
    @Autowired
    private OcBOrderSkuSplitTaskMapper ocBOrderSkuSplitTaskMapper;
    @Autowired
    private SendMQAsyncUtils sendMQAsyncUtils;

    private final static String GIFT_TYPE_ONE = "1";
    private final static Integer REFUND_STATUS_SIX = 6;


    /**
     * 计算订单全部逻辑仓是否缺货
     *
     * @return true:全逻辑仓缺货 false:部分逻辑仓缺货
     */
    public boolean calculateOrderAllStoreIsOutStock(Long orderId, OcBOrder orderDB, List<OcBOrderItem> orderItemListDB) throws NDSException {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        long startTime = System.currentTimeMillis();
        // 是否全仓缺货
        boolean isAllStoreOutStock = false;
        /**
         * 校验数据
         */
        if (orderDB == null) {
            this.endLog(prefix, orderId, true, startTime);
            return true;
        }
        if (CollectionUtils.isEmpty(orderItemListDB)) {
            this.endLog(prefix, orderId, true, startTime);
            return true;
        }

        // 获取有效是的订单明细数据,描述：剔除组合（福袋）商品（protype = 4），剔除掉已取消（RefundStatus=6）的
        List<OcBOrderItem> newOrderItemList = orderItemListDB.stream().filter(obj -> (obj.getProType() != SkuType.NO_SPLIT_COMBINE
                && !REFUND_STATUS_SIX.equals(obj.getRefundStatus()))).collect(Collectors.toList());

        // 获取有效是的skuId列表
        List<Long> skuIdList = newOrderItemList.stream().map(OcBOrderItem::getPsCSkuId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuIdList)) {
            this.endLog(prefix, orderId, true, startTime);
            return true;
        }
        // 当前店铺下的供货逻辑仓
        List<Long> storeIdList = syncStockStrategyService.queryShopStoreNextList(orderDB.getCpCShopId());
        if (CollectionUtils.isEmpty(storeIdList)) {
            this.endLog(prefix, orderId, true, startTime);
            return true;
        }
        // 查询有效库存
        List<SgBStorageInclShare> sgSumRedisStorageQueryResultList = sgRpcService.queryAllStorageStockByRedis(orderId, storeIdList, skuIdList);
        if (CollectionUtils.isEmpty(sgSumRedisStorageQueryResultList)) {
            this.endLog(prefix, orderId, true, startTime);
            return true;
        }
        // 根据实体仓Id聚合
        Map<Long, List<SgBStorageInclShare>> sgSumRedisStorageQueryResultMap = sgSumRedisStorageQueryResultList.stream().collect(Collectors.groupingBy(SgBStorageInclShare::getCpCPhyWarehouseId));
        /**
         * 重新组装订单明细
         */
        List<OrderItemRel> orderItemRelList = this.reassembleOrderItemList(orderId, orderItemListDB);

        /**
         * 为OrderItemRel添加有库存的实体仓列表
         */
        this.addHasStockPhyWarehouseIdForOrderItemRel(orderId, orderItemRelList, sgSumRedisStorageQueryResultMap);
        // 除【非挂靠关系赠品】以外，订单明细库存情况  1：全部缺货 2：全部有货 3：部分有货或部分缺货
        int retCode = this.judgeOrderItemRelStockResult(orderId, orderItemRelList);
        if (retCode == 1) {
            this.endLog(prefix, orderId, true, startTime);
            return true;
        }
        // 非挂靠关系的系统赠品
        List<OcBOrderItem> systemGiftListNotGiftRelation = orderItemListDB.stream().filter(obj -> (GIFT_TYPE_ONE.equals(obj.getGiftType()) && StringUtil.isEmpty(obj.getGiftRelation()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(systemGiftListNotGiftRelation)) {

            /**
             * 计算非挂靠关系赠品库存情况
             */
            List<OrderItemRel> notGiftRelationForOrderItemRelList = this.assembleSystemGiftListForNotGiftRelation(orderId, systemGiftListNotGiftRelation);
            this.addHasStockPhyWarehouseIdForOrderItemRel(orderId, notGiftRelationForOrderItemRelList, sgSumRedisStorageQueryResultMap);
            /**
             * 对OrderItemRel中 pro_type是4和0 且在同一实体仓下的 增加非挂靠关系赠品
             */
            for (OrderItemRel orderItemRel : orderItemRelList) {
                List<OcBOrderItem> giftList = orderItemRel.getGiftList() == null ? new ArrayList<>() : orderItemRel.getGiftList();
                List<Long> haveStockPhyWarehouseIdList = orderItemRel.getHaveStockPhyWarehouseIdList();
                if (CollectionUtils.isNotEmpty(haveStockPhyWarehouseIdList)) {
                    Iterator<OrderItemRel> iterator = notGiftRelationForOrderItemRelList.iterator();
                    while(iterator.hasNext()){
                        OrderItemRel notGiftRelationOrderItemRel = iterator.next();
                        List<Long> haveStockPhyWarehouseIdListByNotGiftRelation = notGiftRelationOrderItemRel.getHaveStockPhyWarehouseIdList();
                        if (CollectionUtils.isNotEmpty(haveStockPhyWarehouseIdListByNotGiftRelation)) {
                            // 取haveStockPhyWarehouseIdList 和 haveStockPhyWarehouseIdListByNotGiftRelation 交集
                            List<Long> intersectionList = haveStockPhyWarehouseIdList.stream().filter(obj -> haveStockPhyWarehouseIdListByNotGiftRelation.contains(obj)).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(intersectionList)) {
                                giftList.add(notGiftRelationOrderItemRel.getHeader());
                                orderItemRel.setHaveStockPhyWarehouseIdList(intersectionList);
                                iterator.remove();
                            }
                        }
                    }
                }
            }

            /**
             * 剩余的非挂靠关系的赠品明细存在时，随机挂靠到一个商品上
             */
            if (CollectionUtils.isNotEmpty(notGiftRelationForOrderItemRelList)) {
                if(CollectionUtils.isEmpty(orderItemRelList.get(0).getGiftList())) {
                    orderItemRelList.get(0).setGiftList(this.getGiftList(notGiftRelationForOrderItemRelList));
                } else {
                    orderItemRelList.get(0).getGiftList().addAll(this.getGiftList(notGiftRelationForOrderItemRelList));
                }
                notGiftRelationForOrderItemRelList.clear();
            }
            /**
             * 重新计算添加非挂靠关系赠品后，库存情况
             */
            this.addHasStockPhyWarehouseIdForOrderItemRel(orderId, orderItemRelList, sgSumRedisStorageQueryResultMap);
            int returnCode = this.judgeOrderItemRelStockResult(orderId, orderItemRelList);
            if (returnCode == 1) {
                isAllStoreOutStock = true;
            }
            if (log.isDebugEnabled()) {
                log.debug("{} 订单ID:{} 重新计算添加非挂靠关系赠品后，库存情况 结束]", prefix, orderId, JSON.toJSONString(orderItemRelList));
            }
        }
        this.endLog(prefix, orderId, isAllStoreOutStock, startTime);
        return isAllStoreOutStock;
    }

    private void endLog(String prefix, Long orderId, boolean isAllStoreOutStock, Long startTime) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("订单ID:{} 计算订单全部逻辑仓是否缺货返回值：{} 耗时：{}ms", orderId), orderId, isAllStoreOutStock, (System.currentTimeMillis() - startTime));
        }
    }

    private List<OcBOrderItem> getGiftList(List<OrderItemRel> notGiftRelationForOrderItemRelList) {
        List<OcBOrderItem> giftList = new ArrayList<>(notGiftRelationForOrderItemRelList.size());
       for (OrderItemRel orderItemRel : notGiftRelationForOrderItemRelList) {
           OcBOrderItem header = new OcBOrderItem();
           BeanUtils.copyProperties(orderItemRel.getHeader(), header);
           giftList.add(header);
       }
       return giftList;
    }

    /**
     * 判断库存情况
     * @param orderItemRelList
     * @return 1：全部缺货 2：全部有货 3：部分有货或部分缺货
     */
    private int judgeOrderItemRelStockResult(@NotNull Long orderId, @NotEmpty List<OrderItemRel> orderItemRelList) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        int retCode;
        // 缺货明细
        List<OrderItemRel> outOfStockOrderItemRelList = new ArrayList<>();
        // 有货明细
        List<OrderItemRel> haveStockOrderItemRelList = new ArrayList<>();

        for (OrderItemRel orderItemRel : orderItemRelList) {
            List<Long> haveStockPhyWarehouseIdList = orderItemRel.getHaveStockPhyWarehouseIdList();
            if (CollectionUtils.isEmpty(haveStockPhyWarehouseIdList)) {
                outOfStockOrderItemRelList.add(orderItemRel);
            } else {
                haveStockOrderItemRelList.add(orderItemRel);
            }
        }
        String message;
        int size = orderItemRelList.size();
        if (outOfStockOrderItemRelList.size() == size) {
            retCode = 1;
            message = "全部缺货";
        } else if (haveStockOrderItemRelList.size() == size) {
            retCode = 2;
            message = "全部有货";
        } else {
            retCode = 3;
            message = "部分有货或部分缺货";
        }
        return retCode;
    }

    /**
     * 添加有库存的实体仓列表为OrderItemRel
     * @param orderId
     * @param orderItemRel
     * @param sgSumRedisStorageQueryResultMap
     */
    private void addHasStockPhyWarehouseIdForOrderItemRel(Long orderId, OrderItemRel orderItemRel, Map<Long, List<SgBStorageInclShare>> sgSumRedisStorageQueryResultMap) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        long startTime = System.currentTimeMillis();
        OcBOrderItem header = orderItemRel.getHeader();
        List<OcBOrderItem> giftList = orderItemRel.getGiftList();
        // 商品类型(0:正常,1:福袋,2:组合,3:预售;4:未拆分的组合商品;5:轻供商品)
        Long proType = header.getProType();
        if (proType == SkuType.NO_SPLIT_COMBINE) {
            List<OcBOrderItem> detailList = orderItemRel.getDetailList();
            List<Long> haveStockPhyWarehouseIdList = this.getHaveStockPhyWarehouseIdList(orderId, sgSumRedisStorageQueryResultMap, detailList, giftList);
            orderItemRel.setHaveStockPhyWarehouseIdList(haveStockPhyWarehouseIdList);
        } else if (proType == SkuType.NORMAL_PRODUCT) {
            List<OcBOrderItem> detailList = new ArrayList<>();
            detailList.add(header);
            List<Long> haveStockPhyWarehouseIdList = this.getHaveStockPhyWarehouseIdList(orderId, sgSumRedisStorageQueryResultMap, detailList, giftList);
            orderItemRel.setHaveStockPhyWarehouseIdList(haveStockPhyWarehouseIdList);
        }
    }

    /**
     * 添加有库存的实体仓列表为OrderItemRel
     * @param orderId
     * @param orderItemRelList
     * @param sgSumRedisStorageQueryResultMap
     */
    private void addHasStockPhyWarehouseIdForOrderItemRel(Long orderId, List<OrderItemRel> orderItemRelList, Map<Long, List<SgBStorageInclShare>> sgSumRedisStorageQueryResultMap) {
        for (OrderItemRel orderItemRel : orderItemRelList) {
            this.addHasStockPhyWarehouseIdForOrderItemRel(orderId, orderItemRel, sgSumRedisStorageQueryResultMap);
        }
    }


    /**
     * 有库存的实体ID列表
     * @param orderId
     * @param sgSumRedisStorageQueryResultMap
     * @param detailList
     * @param giftList
     * @return
     */
    private List<Long> getHaveStockPhyWarehouseIdList(Long orderId, Map<Long, List<SgBStorageInclShare>> sgSumRedisStorageQueryResultMap, List<OcBOrderItem> detailList, List<OcBOrderItem> giftList) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        // 有库存的实体ID列表
        List<Long> haveStockPhyWarehouseIdList = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        /**
         * 循环同一实体仓下，订单明细是否有库存
         */
        for (Long cpCPhyWarehouseId : sgSumRedisStorageQueryResultMap.keySet()) {
            List<SgBStorageInclShare> redisStorageList = sgSumRedisStorageQueryResultMap.get(cpCPhyWarehouseId);
            // 明细是否有库存 true：有 false:无
            boolean isHaveStockByDetailList = false;
            // 赠品是否有库存 true：有 false:无
            boolean isHaveStockByGiftList = false;
            if (CollectionUtils.isNotEmpty(detailList)) {
                // 同一实体仓下，有库存的列表
                List<SkuStock> haveStockList = this.getHaveStockList(detailList, cpCPhyWarehouseId, redisStorageList);
                // 如果不缺货明细（haveStockList）条数和明细（detailList）条数相等，说明当前实体仓下的sku库存满足所有明细（detailList）库存
                if (haveStockList.size() == detailList.size()) {
                    isHaveStockByDetailList = true;
                }
            } else {
                isHaveStockByDetailList = true;
            }
            if (CollectionUtils.isNotEmpty(giftList)) {
                // 同一实体仓下，有库存的列表
                List<SkuStock> haveStockList = this.getHaveStockList(giftList, cpCPhyWarehouseId, redisStorageList);
                // 如果不缺货明细（haveStockList）条数和赠品明细（giftList）条数相等，说明当前实体仓下的sku库存满足所有赠品明细（giftList）库存
                if (haveStockList.size() == giftList.size()) {
                    isHaveStockByGiftList = true;
                }
            } else {
                isHaveStockByGiftList = true;
            }
            if (isHaveStockByDetailList && isHaveStockByGiftList) {
                haveStockPhyWarehouseIdList.add(cpCPhyWarehouseId);
            }
        }
        return haveStockPhyWarehouseIdList;
    }

    /**
     * 同一实体仓下，有库存的列表
     * @param detailList
     * @param cpCPhyWarehouseId
     * @param redisStorageList
     * @return
     */
    private List<SkuStock> getHaveStockList(List<OcBOrderItem> detailList, Long cpCPhyWarehouseId, List<SgBStorageInclShare> redisStorageList) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        long startTime = System.currentTimeMillis();
        // 不缺货明细
        List<SkuStock> haveStockList = new ArrayList<>();
        for (OcBOrderItem orderItem : detailList) {
            Long psCSkuId = orderItem.getPsCSkuId();
            BigDecimal qty = BigDecimalUtil.isNullReturnZero(orderItem.getQty());
            for (SgBStorageInclShare sgSumRedisStorageQueryResult : redisStorageList) {
                Long sgPsCSkuId = sgSumRedisStorageQueryResult.getPsCSkuId();
                // 可用库存
                BigDecimal qtyAvailable = BigDecimalUtil.isNullReturnZero(sgSumRedisStorageQueryResult.getQtyAvailable());
                // 同一个SKU, 可用库存 >= sku购买数量
                if (psCSkuId.equals(sgPsCSkuId) && qtyAvailable.compareTo(qty) >= 0) {
                    SkuStock skuStock = new SkuStock();
                    skuStock.setPsCSkuId(psCSkuId);
                    skuStock.setHaveStock(true);
                    skuStock.setPhyWarehouseId(cpCPhyWarehouseId);
                    skuStock.setQtyAvailable(qtyAvailable);
                    haveStockList.add(skuStock);
                    break;
                }
            }
        }
        return haveStockList;
    }

    /**
     * 判断是否缺货
     *
     * @param detailList
     * @return true:缺货 false：不缺
     */
    private boolean isOutOfStock(List<OcBOrderItem> detailList, Map<Long, List<SgSumRedisStorageQueryResult>> sgSumRedisStorageQueryResultMap) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        boolean retFlag = true;
        long startTime = System.currentTimeMillis();
        /**
         * 循环同一实体仓下，订单明细是否有库存
         */
        for (Long cpCPhyWarehouseId : sgSumRedisStorageQueryResultMap.keySet()) {
            List<SgSumRedisStorageQueryResult> list = sgSumRedisStorageQueryResultMap.get(cpCPhyWarehouseId);

            // 不缺货明细
            List<SkuStock> haveStockList = new ArrayList<>();
            SkuStock skuStock;
            for (OcBOrderItem orderItem : detailList) {
                skuStock = new SkuStock();
                Long psCSkuId = orderItem.getPsCSkuId();
                BigDecimal qty = BigDecimalUtil.isNullReturnZero(orderItem.getQty());
                // 是否sku有库存 true:有 false：无
                boolean isSkuHaveStock = false;
                for (SgSumRedisStorageQueryResult sgSumRedisStorageQueryResult : list) {
                    Long sgPsCSkuId = sgSumRedisStorageQueryResult.getPsCSkuId();
                    // 可用库存
                    BigDecimal qtyAvailable = BigDecimalUtil.isNullReturnZero(sgSumRedisStorageQueryResult.getQtyAvailable());
                    // 同一个SKU, 可用库存 >= sku购买数量
                    if (psCSkuId.equals(sgPsCSkuId) && qtyAvailable.compareTo(qty) >= 0) {
                        isSkuHaveStock = true;
                        break;
                    }
                }
                skuStock.setPsCSkuId(psCSkuId);
                skuStock.setHaveStock(isSkuHaveStock);
                haveStockList.add(skuStock);
            }
            int haveStockNum = 0;
            for (SkuStock skustock : haveStockList) {
                if (skustock.isHaveStock()) {
                    haveStockNum++;
                }
            }
            // 如果相等，说明同一实体仓下，sku都有库存
            if (haveStockNum == haveStockList.size()) {
                retFlag = false;
                break;
            }
        }
        return retFlag;
    }


    class SkuStock {
        private Long psCSkuId;
        private BigDecimal qty;
        private boolean haveStock;
        private Long phyWarehouseId;
        private BigDecimal qtyAvailable;

        public Long getPsCSkuId() {
            return psCSkuId;
        }

        public void setPsCSkuId(Long psCSkuId) {
            this.psCSkuId = psCSkuId;
        }

        public BigDecimal getQty() {
            return qty;
        }

        public void setQty(BigDecimal qty) {
            this.qty = qty;
        }

        public boolean isHaveStock() {
            return haveStock;
        }

        public void setHaveStock(boolean haveStock) {
            this.haveStock = haveStock;
        }

        public Long getPhyWarehouseId() {
            return phyWarehouseId;
        }

        public void setPhyWarehouseId(Long phyWarehouseId) {
            this.phyWarehouseId = phyWarehouseId;
        }

        public BigDecimal getQtyAvailable() {
            return qtyAvailable;
        }

        public void setQtyAvailable(BigDecimal qtyAvailable) {
            this.qtyAvailable = qtyAvailable;
        }
    }


    class OrderItemRel {
        private OcBOrderItem header;
        private List<OcBOrderItem> detailList;
        private List<OcBOrderItem> giftList;
        private List<Long> haveStockPhyWarehouseIdList;

        public OcBOrderItem getHeader() {
            return header;
        }

        public void setHeader(OcBOrderItem header) {
            this.header = header;
        }

        public List<OcBOrderItem> getDetailList() {
            return detailList;
        }

        public void setDetailList(List<OcBOrderItem> detailList) {
            this.detailList = detailList;
        }

        public List<OcBOrderItem> getGiftList() {
            return giftList;
        }

        public void setGiftList(List<OcBOrderItem> giftList) {
            this.giftList = giftList;
        }

        public List<Long> getHaveStockPhyWarehouseIdList() {
            return haveStockPhyWarehouseIdList;
        }

        public void setHaveStockPhyWarehouseIdList(List<Long> haveStockPhyWarehouseIdList) {
            this.haveStockPhyWarehouseIdList = haveStockPhyWarehouseIdList;
        }
    }

    /**
     * 重新组装订单明细
     * 重要：调用方保证订单明细中不包含退款明细，删除明细
     *
     * @param inputOrderItemList
     * @return
     */
    public List<OrderItemRel> reassembleOrderItemList(Long orderId, List<OcBOrderItem> inputOrderItemList) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        List<OrderItemRel> orderItemRelList = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(inputOrderItemList)) {
            return orderItemRelList;
        }
        // 排除掉退款的明细
        inputOrderItemList = inputOrderItemList.stream().filter(obj -> !REFUND_STATUS_SIX.equals(obj.getRefundStatus())).collect(Collectors.toList());
        // 组合或福袋未拆分的订单明细
        List<OcBOrderItem> noSplitCombineList = inputOrderItemList.stream().filter(obj -> obj.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        // 正常商品明细（不包含系统赠品）
        List<OcBOrderItem> normalProductList = inputOrderItemList.stream().filter(obj -> (obj.getProType() == null || obj.getProType() == SkuType.NORMAL_PRODUCT) && !GIFT_TYPE_ONE.equals(obj.getGiftType())).collect(Collectors.toList());
        // 系统赠品（不包含非挂靠关系赠品）
        List<OcBOrderItem> systemGiftList = inputOrderItemList.stream().filter(obj -> (GIFT_TYPE_ONE.equals(obj.getGiftType()) && obj.getGiftRelation() != null)).collect(Collectors.toList());
        /**
         * 组装组合商品或福袋
         */
        if (CollectionUtils.isNotEmpty(noSplitCombineList)) {
            // 拆分后的的明细(只包含 protype = 1 和 protype = 2)
            List<OcBOrderItem> splitList = inputOrderItemList.stream().filter(obj -> obj.getProType() == SkuType.COMBINE_PRODUCT || obj.getProType() == SkuType.GIFT_PRODUCT).collect(Collectors.toList());
            this.assembleCombine(orderId, orderItemRelList, noSplitCombineList, splitList, systemGiftList);
        }
        /**
         * 组装正常商品
         */
        if (CollectionUtils.isNotEmpty(normalProductList)) {
            this.assembleNormalProduct(orderId, orderItemRelList, normalProductList, systemGiftList);
        }
        return orderItemRelList;

    }

    /**
     * 组装组合商品或福袋
     *
     * @param orderItemRelList
     * @param noSplitCombineList
     * @param splitList
     * @param systemGiftList
     */
    private void assembleCombine(Long orderId, List<OrderItemRel> orderItemRelList, List<OcBOrderItem> noSplitCombineList, List<OcBOrderItem> splitList, List<OcBOrderItem> systemGiftList) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        long startTime = System.currentTimeMillis();
        OrderItemRel orderItemRel;
        for (OcBOrderItem orderItem : noSplitCombineList) {
            orderItemRel = new OrderItemRel();
            /**
             * 组装头部信息
             */
            OcBOrderItem header = new OcBOrderItem();
            BeanUtils.copyProperties(orderItem, header);
            /**
             * 组装头部-拆分后明细
             */
            List<OcBOrderItem> detailList = null;
            String groupGoodsMark = orderItem.getGroupGoodsMark();
            if (groupGoodsMark != null) {
               detailList = splitList.stream().filter(obj -> groupGoodsMark.equals(obj.getGroupGoodsMark())).collect(Collectors.toList());
            }
            /**
             * 组装头部-赠品明细
             */
            if (CollectionUtils.isNotEmpty(systemGiftList)) {
                String headerGiftRelation = orderItem.getGiftRelation() == null ? orderItem.getNumIid() : orderItem.getGiftRelation();
                if (headerGiftRelation != null) {
                    List<OcBOrderItem> giftList = systemGiftList.stream().filter(obj -> headerGiftRelation.equals(obj.getGiftRelation())).collect(Collectors.toList());
                    orderItemRel.setGiftList(giftList);
                }
            }
            orderItemRel.setHeader(header);
            orderItemRel.setDetailList(detailList);
            orderItemRelList.add(orderItemRel);
        }
    }

    /**
     * 组装组合正常商品
     *
     * @param orderItemRelList
     * @param normalProductList
     * @param systemGiftList
     */
    private void assembleNormalProduct(Long orderId, List<OrderItemRel> orderItemRelList, List<OcBOrderItem> normalProductList, List<OcBOrderItem> systemGiftList) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        long startTime = System.currentTimeMillis();
        OrderItemRel orderItemRel;
        for (OcBOrderItem orderItem : normalProductList) {
            orderItemRel = new OrderItemRel();
            /**
             * 组装正常商品头部信息
             */
            OcBOrderItem header = new OcBOrderItem();
            BeanUtils.copyProperties(orderItem, header);

            /**
             * 组装正常商品-赠品明细
             */
            if (CollectionUtils.isNotEmpty(systemGiftList)) {
                String headerGiftRelation = orderItem.getGiftRelation() == null ? orderItem.getNumIid() : orderItem.getGiftRelation();
                if (headerGiftRelation != null) {
                    List<OcBOrderItem> giftList = systemGiftList.stream().filter(obj -> headerGiftRelation.equals(obj.getGiftRelation())).collect(Collectors.toList());
                    orderItemRel.setGiftList(giftList);
                }
            }
            orderItemRel.setHeader(header);
            orderItemRel.setDetailList(null);
            orderItemRelList.add(orderItemRel);
        }
    }


    /**
     * 组装系统赠品非挂靠关系
     * @param orderId
     * @param notGiftRelationList
     */
    private List<OrderItemRel>  assembleSystemGiftListForNotGiftRelation(Long orderId, List<OcBOrderItem> notGiftRelationList) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        List<OrderItemRel> orderItemRelList = new ArrayList<>();
        long startTime = System.currentTimeMillis();

        for (OcBOrderItem orderItem : notGiftRelationList) {
            OrderItemRel orderItemRel = new OrderItemRel();
            /**
             * 组装系统赠品非挂靠关系头部信息
             */
            OcBOrderItem header = new OcBOrderItem();
            BeanUtils.copyProperties(orderItem, header);
            orderItemRel.setHeader(header);
            orderItemRel.setGiftList(null);
            orderItemRel.setDetailList(null);
            orderItemRel.setHaveStockPhyWarehouseIdList(null);
            orderItemRelList.add(orderItemRel);
        }
        return orderItemRelList;
    }

    /**
     * 获取有效是的订单数据（剔除组合（福袋）商品（protype = 4），剔除掉已取消（RefundStatus=6）的, 剔除中台赠品（GiftType = 1））
     *
     * @param orderItemList
     * @return
     */
    public List<OcBOrderItem> getEffectiveOrderItemList(List<OcBOrderItem> orderItemList) {
        List<OcBOrderItem> newOrderItemList = orderItemList.stream().filter(obj -> (obj.getProType() != SkuType.NO_SPLIT_COMBINE
                && obj.getRefundStatus() != OcOrderRefundStatusEnum.SUCCESS.getVal()
                && !"1".equals(obj.getGiftType()))).collect(Collectors.toList());
        return newOrderItemList;
    }


    /**
     * 处理缺货订单
     *
     * @param orderId
     */
    public void processOutStockOrder(Long orderId, User user) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        long startTime = System.currentTimeMillis();
        OcBOrder orderDB = ocBOrderMapper.selectByID(orderId);
        if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderDB.getOrderStatus())) {
            return;
        }
        List<OcBOrderItem> orderItemListDB = orderItemMapper.selectOrderItemListOccupy(orderId);
        // true:全部供货逻辑仓缺货 false:部分供货逻辑仓缺货
        boolean allStoreIsOutStock;
        if (OmsOrderSource.MANUAL_ADD.getEcode().equals(orderDB.getOrderSource())) {
            // 手工新增的订单为全缺
            allStoreIsOutStock = true;
        } else {
            // 判断是否全部供货逻辑仓缺货或部分缺货
            allStoreIsOutStock = this.calculateOrderAllStoreIsOutStock(orderId, orderDB, orderItemListDB);
        }

        // 获取有效是的订单明细数据,描述：剔除组合（福袋）商品（protype = 4），剔除掉已取消（RefundStatus=6）的, 剔除中台赠品（GiftType = 1）
        List<OcBOrderItem> newOrderItemList = this.getEffectiveOrderItemList(orderItemListDB);
        // 获取有效是的skuId列表
        List<Long> skuIdList = newOrderItemList.stream().map(OcBOrderItem::getPsCSkuId).collect(Collectors.toList());
        /**
         * 手工拆单可以单独拆出一条赠品，故加此逻辑
         */
        if (CollectionUtils.isNotEmpty(orderItemListDB) && orderItemListDB.size() == 1 && CollectionUtils.isEmpty(skuIdList)) {
            if(orderItemListDB.get(0).getIsGift() == 1) {
                skuIdList.add(orderItemListDB.get(0).getPsCSkuId());
            }
        }
        // 实缺标记:0否 1是
        int lackStock = 0;
        if (allStoreIsOutStock) {
            lackStock = 1;
            this.insertOrderSplit(orderDB, lackStock, null, skuIdList, user);
        } else {
            // 如果订单明细只有一条,不发拆单MQ。
            if (newOrderItemList.size() == 1) {
                this.insertOrderSplit(orderDB, lackStock, null, skuIdList, user);
            } else {
                // 新增缺货拆单表
                this.insertOrderSplitTask(orderDB, lackStock, null, 1, user);
                // 发送缺货拆单MQ
                List<Long> orderIdList = new ArrayList<>();
                orderIdList.add(orderId);
                this.sendSplitOrderMQ(orderIdList, false);
            }
        }
    }

    private void insertOrderSplit(OcBOrder orderDB, int lackStock, String remark, List<Long> skuIdList, User user) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        if (CollectionUtils.isNotEmpty(skuIdList)) {
            // 新增缺货拆单表
            this.insertOrderSplitTask(orderDB, lackStock, remark, 0, user);
            // 新增SKU缺货拆单表
            this.insertOrderSkuSplitTaskMapper(orderDB.getId(), 0, skuIdList, user);
        }

    }

    public void sendSplitOrderMQ(List<Long> orderIdList, boolean againExecOccupy) {
        List<SplitOrderMqInfo> splitOrderMqInfoList = new ArrayList<>();
        for (Long orderId : orderIdList) {
            SplitOrderMqInfo relation = new SplitOrderMqInfo();
            relation.setOrderId(orderId);
            relation.setAgainExecOccupy(againExecOccupy);
            splitOrderMqInfoList.add(relation);
        }
        String str = JSON.toJSONString(splitOrderMqInfoList);
        PropertiesConf propertiesConf = ApplicationContextHandle.getBean(PropertiesConf.class);
//        String topic = propertiesConf.getProperty("r3.oc.oms.split.mq.topic");
        String topic = "R3_OC_OMS_CALL_AUTOSPLIT";
//        String tag = propertiesConf.getProperty("r3.oc.oms.split.mq.tag");
        String tag = "OperateSplit";
        String msgKey = UUID.randomUUID().toString();
        sendMQAsyncUtils.sendDelayMessage("default", "缺货拆单", str, topic, tag, msgKey, 2L, SendMqFallCallBackImpl -> this.sendMqFailCallBack(orderIdList), orderIdList);
    }

    /**
     * 发送MQ失败回滚数据状态
     * @param orderIdList
     */
    private void sendMqFailCallBack(List<Long> orderIdList) {
        ocBOrderSplitTaskMapper.batchUpdateOne(0, orderIdList, 1);
    }

    public void insertOrderSplitTask(OcBOrder order, int lackStock, String remark, int status, User user) {
        OcBOrderSplitTask entity = new OcBOrderSplitTask();
        Date date = new Date();
        entity.setId(BuildSequenceUtil.getInstance().buildSplitOrderTaskId());
        entity.setOcBOrderId(order.getId());
        entity.setPayTime(order.getPayTime());
        entity.setStatus(status);
        entity.setIsLackStock(lackStock);
        entity.setSplitTimes(0);
        entity.setNextTime(new Date());
        entity.setRemark(remark);
        //所属公司
        entity.setAdClientId((long) user.getClientId());
        //所属组织
        entity.setAdOrgId((long) user.getOrgId());
        //创建人id
        entity.setOwnerid(Long.valueOf(user.getId()));
        //创建时间
        entity.setCreationdate(date);
        //创建人用户名
        entity.setOwnername(user.getName());
        entity.setOwnerename(user.getEname());
        //修改人id
        entity.setModifierid(Long.valueOf(user.getId()));
        //修改人用户名
        entity.setModifiername(user.getName());
        entity.setModifierename(user.getEname());
        //修改时间
        entity.setModifieddate(date);
        entity.setIsactive("Y");
        //ocBOrderSplitTaskMapper.insert(entity);
        List<OcBOrderSplitTask> list = new ArrayList<>();
        list.add(entity);
        ocBOrderSplitTaskMapper.batchInsertOrderSplitTask(list);
    }

    public void insertOrderSkuSplitTaskMapper(Long orderId, int status, List<Long> skuIdList, User user) {
        Date date = new Date();
        List<OcBOrderSkuSplitTask> list = new ArrayList<>();
        for (Long skuId : skuIdList) {
            OcBOrderSkuSplitTask entity = new OcBOrderSkuSplitTask();
            entity.setId(BuildSequenceUtil.getInstance().buildSplitSkuOrderTaskId());
            entity.setOcBOrderId(orderId);
            entity.setPsCSkuId(skuId);
            entity.setStatus(status);
            //所属公司
            entity.setAdClientId((long) user.getClientId());
            //所属组织
            entity.setAdOrgId((long) user.getOrgId());
            //创建人id
            entity.setOwnerid(Long.valueOf(user.getId()));
            //创建时间
            entity.setCreationdate(date);
            //创建人用户名
            entity.setOwnername(user.getName());
            entity.setOwnerename(user.getEname());
            //修改人id
            entity.setModifierid(Long.valueOf(user.getId()));
            //修改人用户名
            entity.setModifiername(user.getName());
            entity.setModifierename(user.getEname());
            //修改时间
            entity.setModifieddate(date);
            entity.setIsactive("Y");
            entity.setNextTime(System.currentTimeMillis());
            list.add(entity);
        }
        ocBOrderSkuSplitTaskMapper.batchInsertOcBOrderSkuSplitTask(list);
    }


    /**
     * 由于订单状态或明细变更，更新缺货拆单数据
     *
     * @param orderId
     * @param user
     */
    public void handleOutStockOrderForOrderSplit(Long orderId, User user) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        long begin = System.currentTimeMillis();
        OcBOrder orderDB = ocBOrderMapper.selectByID(orderId);
        /**
         * 订单为缺货时，处理缺货订单
         */
        if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderDB.getOrderStatus())) {

            OcBOrderSplitTask ocBOrderSplitTaskDB = this.selectOcBOrderSplitTask(orderId);
            if (ocBOrderSplitTaskDB != null) {
                List<OcBOrderSkuSplitTask> orderSkuSplitTaskList = this.selectOcBOrderSkuSplitTask(orderId);
                if (CollectionUtils.isNotEmpty(orderSkuSplitTaskList)) {
                    List<Long> orderSkuSplitTaskSkuIdList = orderSkuSplitTaskList.stream().map(OcBOrderSkuSplitTask::getPsCSkuId).collect(Collectors.toList());
                    // 获取有效是的skuId列表
                    List<Long> orderItemSkuIdList = this.getSkuIdListFormDB(orderId);
                    List<Long> noContainsSkuIdList = new ArrayList<>();
                    for (Long skuId : orderItemSkuIdList) {
                        if (!orderSkuSplitTaskSkuIdList.contains(skuId)) {
                            noContainsSkuIdList.add(skuId);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(noContainsSkuIdList)) {
                        this.insertOrderSkuSplitTaskMapper(orderId, 0, noContainsSkuIdList, user);
                    }
                }
            } else {
                // 新增缺货拆单表
                this.insertOrderSplitTask(orderDB, 1, null, 0, user);
                List<Long> skuIdList = this.getSkuIdListFormDB(orderId);
                if (CollectionUtils.isNotEmpty(skuIdList)) {
                    this.insertOrderSkuSplitTaskMapper(orderId, 0, skuIdList, user);
                }
            }

            this.updateOrderSplitTask(orderId, 0, "由于订单状态为缺货，更新缺货拆单数据", user);
        } else {
            this.updateOrderSplitTask(orderId, 2, "由于订单状态或明细变更，更新缺货拆单数据", user);
        }
    }

    private List<Long> getSkuIdListFormDB(Long orderId) {
        List<OcBOrderItem> orderItemListDB = orderItemMapper.selectOrderItemListOccupy(orderId);
        /**
         * 新增Sku缺货拆单表
         */
        // 获取有效是的订单明细数据,描述：剔除组合（福袋）商品（protype = 4），剔除掉已取消（RefundStatus=6）的, 剔除中台赠品（GiftType = 1）
        List<OcBOrderItem> newOrderItemList = this.getEffectiveOrderItemList(orderItemListDB);
        // 获取有效是的skuId列表
        List<Long> skuIdList = newOrderItemList.stream().map(OcBOrderItem::getPsCSkuId).collect(Collectors.toList());
        return skuIdList;
    }

    private void updateOrderSplitTask(Long orderId, int status, String remark, User user) {
        OcBOrderSplitTask orderSplitTask = this.selectOcBOrderSplitTask(orderId);
        if (orderSplitTask != null) {
            Date now = new Date();
            /**
             * 更新oc_b_order_split_task
             */
            UpdateWrapper<OcBOrderSplitTask> whereConditionOrderSplitTask = new UpdateWrapper<>();
            whereConditionOrderSplitTask.eq("oc_b_order_id", orderId);

            OcBOrderSplitTask orderSplitTaskDB = new OcBOrderSplitTask();
            orderSplitTaskDB.setModifieddate(now);
            orderSplitTaskDB.setModifierename(user.getEname());
            orderSplitTaskDB.setModifiername(user.getName());
            orderSplitTaskDB.setModifierid(Long.valueOf(user.getId()));
            orderSplitTaskDB.setStatus(status);
            orderSplitTaskDB.setRemark(remark);

            ocBOrderSplitTaskMapper.update(orderSplitTaskDB, whereConditionOrderSplitTask);

            /**
             * 更新oc_b_order_sku_split_task
             */
            UpdateWrapper<OcBOrderSkuSplitTask> whereConditionForOrderSkuSplitTask = new UpdateWrapper<>();
            whereConditionForOrderSkuSplitTask.eq("oc_b_order_id", orderId);

            OcBOrderSkuSplitTask ocBOrderSkuSplitTask = new OcBOrderSkuSplitTask();
            ocBOrderSkuSplitTask.setStatus(status);
            ocBOrderSkuSplitTask.setModifieddate(now);
            ocBOrderSkuSplitTask.setModifierename(user.getEname());
            ocBOrderSkuSplitTask.setModifiername(user.getName());
            ocBOrderSkuSplitTask.setModifierid(Long.valueOf(user.getId()));
            ocBOrderSkuSplitTaskMapper.update(ocBOrderSkuSplitTask, whereConditionForOrderSkuSplitTask);
        }
    }

    private OcBOrderSplitTask selectOcBOrderSplitTask(Long orderId) {
        OcBOrderSplitTask orderSplitTask = null;
        QueryWrapper<OcBOrderSplitTask> whereConditionOrderSplitTask = new QueryWrapper<>();
        whereConditionOrderSplitTask.eq("oc_b_order_id", orderId);
        List<OcBOrderSplitTask> orderSplitTaskList = ocBOrderSplitTaskMapper.selectList(whereConditionOrderSplitTask);
        if (CollectionUtils.isNotEmpty(orderSplitTaskList)) {
            orderSplitTask = orderSplitTaskList.get(0);
            if (orderSplitTaskList.size() > 1) {
            }
        }
        return orderSplitTask;
    }

    private List<OcBOrderSkuSplitTask> selectOcBOrderSkuSplitTask(Long orderId) {
        QueryWrapper<OcBOrderSkuSplitTask> whereConditionOrderSplitTask = new QueryWrapper<>();
        whereConditionOrderSplitTask.eq("oc_b_order_id", orderId);
        List<OcBOrderSkuSplitTask> orderSkuSplitTaskList = ocBOrderSkuSplitTaskMapper.selectList(whereConditionOrderSplitTask);
        return orderSkuSplitTaskList;
    }
}
