package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderItemProType;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.qimen.PosOrderStatusCallBackService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.request.o2o.SplitOrderRequest;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.web.face.User;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Desc : o2o split order
 * <AUTHOR> xiWen
 * @Date : 2020/8/13
 */
@Slf4j
@Component
public class SplitSaleOrderService {

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;

    @Autowired
    private OmsOrderManualSplitService omsOrderManualSplitService;

    @Autowired
    private PosOrderStatusCallBackService posOrderStatusCallBackService;


    public ValueHolderV14<JSON> split(SplitOrderRequest request, User user) {
        ValueHolderV14<JSON> splitResult = mainProcedure(request, user);
        return splitResult;
    }

    public ValueHolderV14<JSON> split(List<SplitOrderRequest> requestList, User user) {

        JSONArray jsnAry = new JSONArray();
        JSONObject jsn;
        for (int i = 0; i < requestList.size(); i++) {
            ValueHolderV14<JSON> splitResult = mainProcedure(requestList.get(i), user);
            if (splitResult != null) {
                jsn = new JSONObject();
                jsn.put("code", splitResult.getCode());
                jsn.put("msg", splitResult.getMessage());
                jsnAry.add(jsn);
            }
        }
        ValueHolderV14<JSON> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("success");
        if (jsnAry.size() < 1) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("failed");
        }
        vh.setData(jsnAry);
        return vh;
    }


    /**
     * split order main process
     *
     * @param request split order params
     * @param user    operator
     * @return
     */
    private ValueHolderV14 mainProcedure(SplitOrderRequest request, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        RedisReentrantLock redisLock = null;
        try {

            // 1. verify
            verifyParams(request, user);

            // 2. search origin shard key
            Long sourceOrderId = posOrderStatusCallBackService.selectOmsOrderId(request.getOrderId());

            // 3. lock order
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(sourceOrderId);
            redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {

                // 4. query oms origin order
                OcBOrderParam ocBOrderParam = queryOriginOrder(sourceOrderId);

                // 5. de audit
                boolean deAuditVh = ocBOrderTheAuditService.updateOrderInfo(user, vh, sourceOrderId, true, LogTypeEnum.BILL_REVERSE_AUDIT.getType(),true);
                assertException(!deAuditVh, vh.getMessage());

                // 6. split handler
                List<OcBOrderParam> newOrders = splitHandle(ocBOrderParam, request);

                // 7. invoke void sg sendOrder
                String paramString = convertOrderParam4Split(newOrders);
                ValueHolderV14 splitVh = omsOrderManualSplitService.splitO2Order(paramString, user);
                assertException((splitVh == null || !splitVh.isOK()), (splitVh == null ? "调用拆单服务结果null" : splitVh.getMessage()));

            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("当前订单正在操作，请稍后再试!");
                return vh;
            }
            // 8. void origin order, add new orders
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("success");
        } catch (Exception ex) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            String msg = "异常";
            if (ex != null && ex.getMessage() != null) {
                msg = ex.getMessage().length() > 500 ? ex.getMessage().substring(0, 500) : ex.getMessage();
            }
            vh.setMessage(msg);
        } finally {
            if (redisLock != null) {
                redisLock.unlock();
            }
        }
        return vh;
    }


    /**
     * 1. verify param
     *
     * @param request
     * @param user
     */
    private void verifyParams(SplitOrderRequest request, User user) {

        assertData(user, "Current Operate User");

        assertData(request, "拆分参数");

        assertData(request.getOrderId(), "拆分参数: 出库通知单号");

        List<SplitOrderRequest.SplitOrderItem> splitOrders = request.getSplitOrders();
        assertException((splitOrders == null || splitOrders.size() < 2), "拆分参数:明细需不为空且大于一条");

        Set<String> groupSet = new HashSet<>();
        for (int i = 0; i < splitOrders.size(); i++) {
            String indexFlag = new StringBuilder("SplitOrderItem.").append("[").append(i + 1).append("].").toString();
            assertData(splitOrders.get(i), indexFlag);
            assertData(splitOrders.get(i).getSubOrderId(), indexFlag + "subOrderId");
            assertData(splitOrders.get(i).getSplitGroup(), indexFlag + "splitGroup");
            assertData(splitOrders.get(i).getSubOrderId(), indexFlag + "subOrderId");
            assertData(splitOrders.get(i).getSplitGroup(), indexFlag + "splitGroup");
            groupSet.add(splitOrders.get(i).getSplitGroup());
            assertData(splitOrders.get(i).getGoodsCode(), indexFlag + "goodsCode");
            assertData(splitOrders.get(i).getColorCode(), indexFlag + "colorCode");
            assertData(splitOrders.get(i).getSizeCode(), indexFlag + "sizeCode");
            assertData(splitOrders.get(i).getNum(), indexFlag + "num");
            boolean gtZero = isGtZero(splitOrders.get(i).getNum());
            assertException(!gtZero, "num :Illegal Parameter");

        }
        assertException(groupSet.size() < 2, "明细的SplitGroup都相同,无须拆单");

    }


    /**
     * 2. query origin order
     *
     * @param orderId
     * @return
     */
    private OcBOrderParam queryOriginOrder(Long orderId) {

        OcBOrder order = ocBOrderMapper.selectByID(orderId);
        assertData(order, "订单");
        // todo 是否校验 单据状态
        assertData(order.getOrderStatus(), "订单状态");

        boolean isAllowSplit = OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(order.getOrderStatus());
        assertException(!isAllowSplit, "订单状态不为配货中，无法执行订单拆分");

        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectAllOrderItem(orderId);
        assertData(orderItems, "Origin OrderItems");
        // todo  是否未退款,组合等判断
        List<OcBOrderItem> collect = orderItems.stream()
                .filter(o -> o.getQty() == null || o.getPsCProEcode() == null || o.getPsCClrEcode() == null
                        || o.getPsCSizeEcode() == null).collect(Collectors.toList());
        assertException(CollectionUtils.isNotEmpty(collect), "Origin OrderItems Has Illegal Item");

        OcBOrderParam ocBOrderParam = new OcBOrderParam();
        ocBOrderParam.setOcBOrder(order);
        ocBOrderParam.setOrderItemList(orderItems);

        return ocBOrderParam;
    }

    /**
     * 3. split order
     *
     * @param ocBOrderParam
     * @param request
     * @return
     */
    private List<OcBOrderParam> splitHandle(OcBOrderParam ocBOrderParam, SplitOrderRequest request) {

        // 1. 原单明细转换:  1. map<明细ID, list<item> >
        Map<Long, OcBOrderItem> originItemMap = ocBOrderParam
                .getOrderItemList().stream().collect(Collectors.toMap(OcBOrderItem::getId, o -> o, (x, y) -> y));


        // 2. 参数明细分组：　1. map<子明细Id, list<拆分明细> >
        List<SplitOrderRequest.SplitOrderItem> splitOrders = request.getSplitOrders();
        Map<String, List<SplitOrderRequest.SplitOrderItem>> splitItemsMap = splitOrders.stream()
                .collect(Collectors.groupingBy(SplitOrderRequest.SplitOrderItem::getSubOrderId, Collectors.toList()));

        // 3. 比对,校验拆分信息
        reVerifySplitParam(splitItemsMap, originItemMap);

        // 4. 参数分组-- groupId
        List<OcBOrderParam> afterSplit = new ArrayList<>();
        Map<String, List<SplitOrderRequest.SplitOrderItem>> splitGroupMap = splitOrders.stream()
                .collect(Collectors.groupingBy(SplitOrderRequest.SplitOrderItem::getSplitGroup, Collectors.toList()));

        for (Map.Entry<String, List<SplitOrderRequest.SplitOrderItem>> entry : splitGroupMap.entrySet()) {
            OcBOrderParam newOrderParam = new OcBOrderParam();

            // 暂时不用此方法
           /* OcBOrder newOrder = new OcBOrder();
            BeanUtils.copyProperties(ocBOrderParam.getOcBOrder(), newOrder);
            Long newOrderId = ModelUtil.getSequence("oc_b_order");
            newOrder.setId(newOrderId);
            newOrder.setIsSplit(OcBOrderConst.IS_STATUS_IY);*/

            newOrderParam.setOcBOrder(ocBOrderParam.getOcBOrder());

            List<OcBOrderItem> newItemList = new ArrayList<>();
            List<SplitOrderRequest.SplitOrderItem> splitItems = entry.getValue();
            for (SplitOrderRequest.SplitOrderItem splitItem : splitItems) {

              /*  String subOrderId = splitItem.getSubOrderId();
                OcBOrderItem item = originItemMap.get(subOrderId);
                BeanUtils.copyProperties(item, newItem);
                Long newItemId = ModelUtil.getSequence("oc_b_order_item");
                newItem.setId(newItemId);
                newItem.setQty(splitItem.getNum());
                newItem.setOcBOrderId(newOrderId);*/

                // 启用,改造已有拆分订单功能
                OcBOrderItem newItem = new OcBOrderItem();
                newItem.setOcBOrderId(ocBOrderParam.getOcBOrder().getId());
                newItem.setId(Long.valueOf(splitItem.getSubOrderId()));
                newItem.setQty(splitItem.getNum());

                newItemList.add(newItem);

            }
            newOrderParam.setOrderItemList(newItemList);
            afterSplit.add(newOrderParam);
        }
        // 5. 重新计算主,子金额,数量等
      /*  for (OcBOrderParam newParam : afterSplit) {
            reCalcNewOrderData(newParam);
        }*/


        //添加未拆分的组合商品(选择第一单)
        for (Long orderId : originItemMap.keySet()) {
            OcBOrderItem ocBOrderItem = originItemMap.get(orderId);
            if (OmsOrderItemProType.UNDIVIDED_PORTFOLIO_GOODS.getVal() == ocBOrderItem.getProType()) {
                OcBOrderParam ocBOrderParam1 = afterSplit.get(0);
                List<OcBOrderItem> orderItemList = ocBOrderParam1.getOrderItemList();
                orderItemList.add(ocBOrderItem);
            }
        }
        return afterSplit;

    }

    /**
     * check split param again
     *
     * @param splitItemsMap
     * @param originItemMap
     */
    private void reVerifySplitParam(Map<String, List<SplitOrderRequest.SplitOrderItem>> splitItemsMap,
                                    Map<Long, OcBOrderItem> originItemMap) {

        for (Map.Entry<String, List<SplitOrderRequest.SplitOrderItem>> entry : splitItemsMap.entrySet()) {

            //   BigDecimal splitQty = entry.getValue().stream().map(x -> x.getNum()).reduce(BigDecimal.ZERO, BigDecimal::add);
            OcBOrderItem origItem = originItemMap.get(Long.valueOf(entry.getKey()));
            assertException(origItem == null, "未找到拆分参数SubId对应的原单明细" + entry.getKey());
            String origProCode = origItem.getPsCProEcode();
            String origPClrCode = origItem.getPsCClrEcode();
            String origPSizeCode = origItem.getPsCSizeEcode();

            boolean hasError = true;
            StringBuilder expSb = new StringBuilder();
            BigDecimal statisticsSplitQty = BigDecimal.ZERO;
            for (SplitOrderRequest.SplitOrderItem splitItem : entry.getValue()) {

                if (!StringUtils.equals(origProCode, splitItem.getGoodsCode())) {
                    expSb.append("参数商品编码: ").append(splitItem.getGoodsCode()).append(", 原商品编码:").append(origProCode);
                    break;
                }

                if (!StringUtils.equals(origPClrCode, splitItem.getColorCode())) {
                    expSb.append("参数商品颜色编码: ").append(splitItem.getGoodsCode()).append(", 原商品颜色编码:").append(origProCode);
                    break;
                }

                if (!StringUtils.equals(origPSizeCode, splitItem.getSizeCode())) {
                    expSb.append("参数商品尺寸编码: ").append(splitItem.getGoodsCode()).append(", 原商品尺寸编码:").append(origProCode);
                    break;
                }
                hasError = false;
                statisticsSplitQty = statisticsSplitQty.add(splitItem.getNum());
            }
            if (!hasError) {
                hasError = statisticsSplitQty.compareTo(origItem.getQty()) == 0 ? false : true;
                if (hasError) {
                    expSb.append("参数商品总数量: ").append(statisticsSplitQty).append(", 原明细数量:").append(origItem.getQty());
                }
            }
            if (hasError) {
                assertException(true, expSb.append("参数明细有异ID: ").append(entry.getKey()).toString());
            }
        }
    }

    private void reCalcNewOrderData(OcBOrderParam newParam) {


    }

    /**
     * convert model to jsonString
     *
     * @param ocBOrderParams
     * @return
     */
    private String convertOrderParam4Split(List<OcBOrderParam> ocBOrderParams) {

        JSONObject jsn = new JSONObject();
        JSONArray ttlAry = new JSONArray();
        for (OcBOrderParam orderParam : ocBOrderParams) {
            OcBOrder order = orderParam.getOcBOrder();
            JSONArray eachOrder = new JSONArray();
            List<OcBOrderItem> items = orderParam.getOrderItemList();
            for (OcBOrderItem item : items) {
                JSONObject eachItem = new JSONObject();
                eachItem.put("orig_order_id", order.getId());
                eachItem.put("orig_order_item_id", item.getId());
                eachItem.put("split_num", item.getQty());
                eachItem.put("cp_c_phy_warehouse_id", order.getCpCPhyWarehouseId());
                eachItem.put("cp_c_phy_warehouse_ecode", order.getCpCPhyWarehouseEcode());
                eachItem.put("cp_c_phy_warehouse_ename", order.getCpCPhyWarehouseEname());
                eachOrder.add(eachItem);
            }
            ttlAry.add(eachOrder);
        }

        jsn.put("data", ttlAry);
        String resultParam = jsn.toJSONString();
        return resultParam;

    }

    /**
     * verify param assert
     *
     * @param obj
     * @param tag
     */
    private void assertData(Object obj, String tag) {

        if (obj == null) {
            throw new NDSException(tag + ":Null");
        }
        if (obj instanceof String) {
            if (StringUtils.isEmpty((CharSequence) obj)) {
                throw new NDSException(tag + ":Empty");
            }
        } else if (obj instanceof List) {
            if (CollectionUtils.isEmpty((Collection) obj)) {
                throw new NDSException(tag + " :Empty");
            }
        }
    }

    private void assertException(boolean isThrow, String msg) {
        if (isThrow) {
            throw new NDSException(msg);
        }
    }

    /**
     * @param bigDecimal
     * @return
     */
    private boolean isGtZero(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return false;
        }
        return bigDecimal.compareTo(BigDecimal.ZERO) > 0 ? true : false;
    }

    /**
     * level debug recorder
     *
     * @param msg log message
     */
    private void logDebug(String msg, Object... params) {

        if (log.isDebugEnabled()) {
            if (params.length == 0) {
                log.debug("SplitSaleOrderService.".concat(msg));
            } else {
                log.debug("SplitSaleOrderService.".concat(msg), params);
            }
        }
    }

    /**
     * level error recorder
     *
     * @param msg    message or format string
     * @param params print values
     */
    private void logError(String msg, Object... params) {

        if (params.length == 0) {
            log.error("SplitSaleOrderService.".concat(msg));
        } else {
            log.error("SplitSaleOrderService.".concat(msg), params);
        }
    }

}
