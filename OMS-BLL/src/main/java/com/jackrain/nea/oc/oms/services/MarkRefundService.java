package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPaymentFiMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBJitxDealerOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.oc.oms.util.OrderPrevDealUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @author: 周琳胜
 * @since: 2019/3/11
 * create at : 2019/3/11 11:24
 */
@Slf4j
@Component
@Deprecated
public class MarkRefundService {
    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    OcBOrderPaymentFiMapper ocBOrderPaymentFiMapper;
    @Autowired
    OmsOrderCancellationService omsOrderCancellationService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBorderUpdateServiceExt ocBorderUpdateServiceExt;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OmsOrderRecountAmountService omsOrderRecountAmountService;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private IpJitxOrderService ipJitxOrderService;

    @Autowired
    private OcBJitxDealerOrderTaskService ocBJitxDealerOrderTaskService;

    /**
     * 标记退款完成
     *
     * @param obj  入参id
     * @param user 用户信息
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 markRefund(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        //明细ids
        String ids = obj.getString("IDS");
        //转成数组
        String[] idArray = ids.split(",");
        if (idArray == null || idArray.length == 0) {
            throw new NDSException(Resources.getMessage("请选择需要标记退款完成的记录！", user.getLocale()));
        }
        // 查主表id
        Long esOcbOrderId = ES4Order.getIdByItemId(Long.valueOf(idArray[0]));

        if (esOcbOrderId == null) {
            throw new NDSException(Resources.getMessage("未从ES查找到相应主表ID！", user.getLocale()));
        }
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 标记退款完成订单id{}", esOcbOrderId);
        }
        //            //redis锁单
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(esOcbOrderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                for (int i = 0; i < idArray.length; i++) {
                    Long orderItemId = Long.valueOf(idArray[i]);


                    QueryWrapper<OcBOrderItem> wrapper = new QueryWrapper<>();
                    wrapper.eq("id", orderItemId).eq("oc_b_order_id", esOcbOrderId);
                    wrapper.ne("pro_type", SkuType.NO_SPLIT_COMBINE);

                    //获取明细信息
                    OcBOrderItem ocBOrderItem = ocBOrderItemMapper.selectOne(wrapper);
                    if (ocBOrderItem == null) {
                        throw new NDSException(Resources.getMessage("请选择有效的明细记录！", user.getLocale()));
                    }
                    //获取订单信息
                    OcBOrder order = ocBOrderMapper.selectById(ocBOrderItem.getOcBOrderId());
                    // JITX平台.拦截   isJitX  jitx订单
                    Long isJitX = obj.getLong("ISJITX");
                    if (PlatFormEnum.VIP_JITX.getLongVal().equals(isJitX)) {
                        OrderPrevDealUtil.platformIntercept(order, user);
                    }

                    //获取订单编号
                    String billNo = order.getBillNo();

                    Integer orderStatus = null;
                    Integer wmsCancelStatus = null;
                    Integer refundStatus = ocBOrderItem.getRefundStatus();
                    if (order != null) {
                        //获得订单状态
                        orderStatus = order.getOrderStatus();
                    }
                    //  1, 待审核 2, 缺货3, 已审核4, 配货中
                    if (orderStatus == null) {
                        throw new NDSException(Resources.getMessage("订单状态异常，不允许退款完成！", user.getLocale()));
                    } else if (orderStatus != OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal() && orderStatus
                            != OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal() && orderStatus
                            != OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
                        throw new NDSException(Resources.getMessage("订单异常，不允许退款完成！", user.getLocale()));
                    }
                    if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
                        wmsCancelStatus = order.getWmsCancelStatus();
                        log.debug(this.getClass().getName() + " 标记退款完成wms状态:{}", wmsCancelStatus);
                        if (wmsCancelStatus == null) {
                            throw new NDSException(Resources.getMessage("wms撤回状态异常，不允许退款完成！", user.getLocale()));
                            //  0未撤回1 撤回成功2撤回失败
                        } else if (wmsCancelStatus != OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()) {
                            throw new NDSException(Resources.getMessage("此订单在WMS中未取消，不允许标记为退款完成，建议先"
                                    + "撤回WMS再进行标记！", user.getLocale()));
                        }
                    }
                    if (refundStatus == null) {
                        throw new NDSException(Resources.getMessage("订单明细状态异常！", user.getLocale()));
                    }
                    if (refundStatus == OcOrderRefundStatusEnum.SUCCESS.getVal()) {
                        throw new NDSException(Resources.getMessage("当前已经退款完成，不允许重复操作！", user.getLocale()));
                    }

                    //更新明细条码数据
                    OcBOrderItem newOcBorderItem = new OcBOrderItem();
                    newOcBorderItem.setId(orderItemId);
                    newOcBorderItem.setRefundStatus(OcOrderRefundStatusEnum.SUCCESS.getVal());
                    //获得商品明细 数量
                    BigDecimal qty = ocBOrderItem.getQty();
                    newOcBorderItem.setQtyRefund(qty);
                    //if (ocBOrderItem.getQtyRefund().compareTo(ocBOrderItem.getQty()) == 0) {
                    newOcBorderItem.setPrice(BigDecimal.ZERO);
                    //}
                    newOcBorderItem.setOrderSplitAmt(BigDecimal.ZERO);
                    newOcBorderItem.setAmtDiscount(BigDecimal.ZERO);
                    newOcBorderItem.setAdjustAmt(BigDecimal.ZERO);
                    // 记录实际成交价金额
                    newOcBorderItem.setReserveDecimal05(ocBOrderItem.getRealAmt());
                    newOcBorderItem.setRealAmt(BigDecimal.ZERO);
                    newOcBorderItem.setPriceActual(BigDecimal.ZERO); // 成交单价清零
                    newOcBorderItem.setPriceList(BigDecimal.ZERO); // 吊牌价格清零
                    newOcBorderItem.setPriceTag(BigDecimal.ZERO);// 吊牌价格清零
                    QueryWrapper<OcBOrderItem> updateWrapper = new QueryWrapper<>();
                    updateWrapper.eq("id", orderItemId).eq("oc_b_order_id", order.getId());
                    updateWrapper.ne("pro_type", SkuType.NO_SPLIT_COMBINE);
                    ocBOrderItemMapper.update(newOcBorderItem, updateWrapper);

                    //获取新的明细信息
                    QueryWrapper<OcBOrderItem> newWrapper = new QueryWrapper<>();
                    newWrapper.eq("id", orderItemId).eq("oc_b_order_id", esOcbOrderId);
                    newWrapper.ne("pro_type", SkuType.NO_SPLIT_COMBINE);
                    OcBOrderItem eSocBOrderItem = ocBOrderItemMapper.selectOne(newWrapper);
                  /*  try {
                        Boolean flag = SpecialElasticSearchUtil.indexDocument(OC_B_ORDER_INDEX_NAME, OC_B_ORDER_ITEM_TYPE_NAME,
                                eSocBOrderItem, orderItemId, order.getId());
                        if (!flag) {
                            throw new NDSException("推送es失败");
                        }
                    } catch (Exception e) {
                        throw new NDSException("推送es失败");
                    }*/

                    // 插入【订单付款信息表】，根据退款条码金额产生一条负向的支付明细
                    // 若订单“已收金额”大于0，则需要生成负向付款信息表。
                    //BigDecimal money = order.getReceivedAmt(); // 已收金额
                    // 明细的成交金额
                    BigDecimal money = ocBOrderItem.getRealAmt();
                    if (money != null && new BigDecimal(0).compareTo(money) < 0) {
                        OcBOrderPayment ocBorderPaymentDto = new OcBOrderPayment();
                        ocBorderPaymentDto.setModifierename(user.getEname());
                        ocBorderPaymentDto.setModifieddate(new Date(System.currentTimeMillis()));
                        ocBorderPaymentDto.setOcBOrderId(order.getId());
                        ocBorderPaymentDto.setPaymentAmt(money.multiply(new BigDecimal(-1)));
                        ocBorderPaymentDto.setId(ModelUtil.getSequence("oc_b_order_payment"));
                        ocBorderPaymentDto.setPayTime(new Date());
                        ocBorderPaymentDto.setEndTime(new Date());
                        ocBOrderPaymentFiMapper.insert(ocBorderPaymentDto);
                    }
                    // 插入订单日志服务
                    try {
                        String logType = "标记退款完成";
                        String logMessage = ocBOrderItem.getPsCSkuEcode() + "条码退款完成";
                        String param = "";
                        String errorMessage = "";
                        omsOrderLogService.addUserOrderLog(order.getId(), billNo, OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), logMessage, param, errorMessage, user);
                    } catch (Exception e) {
                        log.error("调用订单日志服务出错,异常信息", e);
                    }
                }
                //判断是否需要更改主表订单状态
                Long orderItemId = Long.valueOf(idArray[0]);
                QueryWrapper<OcBOrderItem> qw = new QueryWrapper<>();
                qw.eq("id", orderItemId).eq("oc_b_order_id", esOcbOrderId);
                qw.ne("pro_type", SkuType.NO_SPLIT_COMBINE);
                //获取明细信息
                OcBOrderItem ocBOrderItem = ocBOrderItemMapper.selectOne(qw);
                Long orderId = Long.valueOf(ocBOrderItem.getOcBOrderId().toString());
                QueryWrapper<OcBOrderItem> wrapper = new QueryWrapper<>();
                wrapper.eq("oc_b_order_id", orderId);
                wrapper.ne("pro_type", SkuType.NO_SPLIT_COMBINE);
                List<OcBOrderItem> items = ocBOrderItemMapper.selectList(wrapper);

                int temp = 0;
                int temp2 = 0;

                List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
                //遍历明细退款状态
                for (int i = 0; i < items.size(); i++) {
                    Integer refundStatus = items.get(i).getRefundStatus();
                    if (refundStatus == null) {
                        log.debug("标记为退款完成遍历明细状态为空");
                        throw new NDSException(Resources.getMessage("当前退款状态异常，订单状态修改失败！", user.getLocale()));
                    } else if (items.get(i).getRefundStatus() != OcOrderRefundStatusEnum.SUCCESS.getVal()) {
                        ocBOrderItems.add(items.get(i));
                        temp--;
                    }
                }
                if (log.isDebugEnabled()) {
                    log.debug("MarkRefundService.markRefund orderId={},temp={}", ocBOrderItem.getOcBOrderId(), temp);
                }
                if (temp == 0) {
                    // 如果全部为退款完成
                    try {
                        // 调用订单作废服务
                        OcBOrder order = ocBOrderMapper.selectById(orderId);
                        log.debug("调用订单作废服务入参" + order);
                        ValueHolderV14 f = omsOrderCancellationService.doInvoildOutOrder(order, user);
                        log.debug("调用订单作废服务出参" + f.toString());
                        if (!f.isOK()) {
                            throw new NDSException("调用订单作废服务出错" + f.getMessage());
                        }
                        // 调用作废服务成功，更新主表数据
                        // “调整金额”：订单明细中“调整金额”合计
                        order.setAdjustAmt(BigDecimal.ZERO);
                        // 商品总额”：明细中“标准价”*（“商品数量”-“已退数量”）的绝对值合计
                        order.setProductAmt(BigDecimal.ZERO);
                        // 商品数量”：明细“商品数量”-“已退数量”合计
                        order.setQtyAll(BigDecimal.ZERO);
                        // 优惠金额”：明细“优惠金额”合计
                        order.setProductDiscountAmt(BigDecimal.ZERO);
                        //订单优惠金额”：明细“整单平摊金额”合计
                        order.setOrderDiscountAmt(BigDecimal.ZERO);
                        // 配送费用：更新为0
                        order.setShipAmt(BigDecimal.ZERO);
                        // 服务费
                        order.setServiceAmt(BigDecimal.ZERO);
                        // 已付金额 :订单总额：商品总额+配送费用+调整金额+服务费-订单优惠金额-商品优惠金额
                        order.setOrderAmt(BigDecimal.ZERO);
                        // 已收金额
                        order.setReceivedAmt(BigDecimal.ZERO);
                        // 更新主表信息
                        int num = ocBOrderMapper.updateById(order);
                        log.debug("作废订单后更新主表" + num);
                        //YY经销商订单作废后需调用接口通知
                        if (PlatFormEnum.VIP_JIT.getCode().equals(order.getPlatform())) {
                            OcBJitxDealerOrderRelation relation=new OcBJitxDealerOrderRelation();
                            relation.setOcBOrder(order);
                            relation.setWarehouseId(order.getCpCPhyWarehouseId());
                            relation.setUser(user);
                            ocBJitxDealerOrderTaskService.orderCancel(relation);
                        }

                        // 插入订单日志服务
                        try {
                            String logType = "系统作废";
                            String logMessage = "标记退款完成作废原订单";
                            String param = "";
                            String errorMessage = "";
                            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(), logMessage, param, errorMessage, user);
                        } catch (Exception e) {
                            log.debug("调用订单日志服务出错:" + e.getMessage());
                        }
                      /*  try {
                            Boolean flag = SpecialElasticSearchUtil.indexDocument(OC_B_ORDER_INDEX_NAME, OC_B_ORDER_TYPE_NAME,
                                    ocBOrderMapper.selectById(orderId), orderId);
                            if (!flag) {
                                throw new NDSException("推送ES失败 ");
                            }
                        } catch (Exception e) {
                            throw new NDSException("推送ES失败");
                        }*/
                        vh.setCode(ResultCode.SUCCESS);
                        vh.setMessage("标记成功，订单已作废！");
                        return vh;
                    } catch (Exception e) {
                        log.debug("调用订单作废服务异常:" + e);
                        throw new NDSException("调用订单作废服务异常" + e.getMessage());
                    }
                } else { // 如果没有全部退款完成
                    // 判断未退款完成的明细是否为赠品
                    for (int i = 0; i < ocBOrderItems.size(); i++) {
                        Integer isGift = ocBOrderItems.get(i).getIsGift();
                        if (isGift == null) {
                            throw new NDSException(Resources.getMessage("当前是否为赠品状态异常，订单状态修改失败！", user.getLocale()));
                        } else if (ocBOrderItems.get(i).getIsGift() != 1) {
                            temp2--;
                        }
                    }
                    if (temp2 == 0) {
                        try {
                            // 调用订单作废服务
                            OcBOrder order = ocBOrderMapper.selectById(orderId);
                            log.debug("调用订单作废服务入参" + order);
                            ValueHolderV14 f = omsOrderCancellationService.doInvoildOutOrder(order, user);
                            log.debug("调用订单作废服务出参" + f.toString());
                            if (f.isOK()) {
                                // 调用作废服务成功，更新主表数据
                                // “调整金额”：订单明细中“调整金额”合计
                                order.setAdjustAmt(BigDecimal.ZERO);
                                // 商品总额”：明细中“标准价”*（“商品数量”-“已退数量”）的绝对值合计
                                order.setProductAmt(BigDecimal.ZERO);
                                // 商品数量”：明细“商品数量”-“已退数量”合计
                                order.setQtyAll(BigDecimal.ZERO);
                                // 优惠金额”：明细“优惠金额”合计
                                order.setProductDiscountAmt(BigDecimal.ZERO);
                                //订单优惠金额”：明细“整单平摊金额”合计
                                order.setOrderDiscountAmt(BigDecimal.ZERO);
                                // 配送费用：更新为0
                                order.setShipAmt(BigDecimal.ZERO);
                                // 服务费
                                order.setServiceAmt(BigDecimal.ZERO);
                                // 已付金额 :订单总额：商品总额+配送费用+调整金额+服务费-订单优惠金额-商品优惠金额
                                order.setOrderAmt(BigDecimal.ZERO);
                                // 已收金额
                                order.setReceivedAmt(BigDecimal.ZERO);
                                // 更新主表信息
                                int num = ocBOrderMapper.updateById(order);
                                log.debug("作废订单后更新主表" + num);
                                //YY经销商订单作废后需调用接口通知
                                if (PlatFormEnum.VIP_JIT.getCode().equals(order.getPlatform())) {
                                    OcBJitxDealerOrderRelation relation=new OcBJitxDealerOrderRelation();
                                    relation.setOcBOrder(order);
                                    relation.setWarehouseId(order.getCpCPhyWarehouseId());
                                    relation.setUser(user);
                                    ocBJitxDealerOrderTaskService.orderCancel(relation);
                                }
                            } else {
                                throw new NDSException("调用订单作废服务出错:" + f.getMessage());
                            }
                            // 插入订单日志服务
                            try {
                                String logType = "系统作废";
                                String logMessage = "标记退款完成作废原订单 ";
                                String param = "";
                                String errorMessage = "";
                                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(), logMessage, param, errorMessage, user);
                            } catch (Exception e) {
                                log.debug("调用订单日志服务出错:" + e);
                            }
                          /*  try {
                                Boolean flag = SpecialElasticSearchUtil.indexDocument(OC_B_ORDER_INDEX_NAME, OC_B_ORDER_TYPE_NAME,
                                        ocBOrderMapper.selectById(orderId), orderId);
                                if (!flag) {
                                    throw new NDSException("推送ES失败 ");
                                }
                            } catch (Exception e) {
                                throw new NDSException("推送ES失败");
                            }*/
                            vh.setCode(ResultCode.SUCCESS);
                            vh.setMessage("标记成功，订单已作废！");
                            return vh;
                        } catch (Exception e) {
                            log.debug("调用订单作废服务异常:" + e);
                            throw new NDSException("调用订单作废服务异常" + e.getMessage());
                        }
                    } else {
                        // 调用保存服务

                        try {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("id", orderId);
                            //  编辑保存服务
                            OcBOrderRelation relation = new OcBOrderRelation();
                            OcBOrder order = ocBOrderMapper.selectById(orderId);
                            relation.setOrderInfo(order);
                            List<OcBOrderItem> itemList = new ArrayList<>();
                            String originTid = StringUtils.isNotEmpty(order.getTid()) ? order.getTid() : order.getSourceCode();
                            boolean isNeedChangeTidFlag=false;
                            for (int i = 0; i < idArray.length; i++) {
                                //获取新的明细信息
                                QueryWrapper<OcBOrderItem> newWrapper = new QueryWrapper<>();
                                newWrapper.eq("id", idArray[i]).eq("oc_b_order_id", esOcbOrderId);
                                newWrapper.ne("pro_type", SkuType.NO_SPLIT_COMBINE);
                                OcBOrderItem eSocBOrderItem = ocBOrderItemMapper.selectOne(newWrapper);
                                eSocBOrderItem.setQty(BigDecimal.ZERO);
                                itemList.add(eSocBOrderItem);
                                //主表记录的平台单号对应明细是否退款了
                                isNeedChangeTidFlag = originTid.equals(eSocBOrderItem.getTid());
                            }

                            relation.setOrderItemList(itemList);
                            if(isNeedChangeTidFlag) {
                                if (PlatFormEnum.VIP_JITX.getCode().equals(order.getPlatform())) {
                                    log.info("{},唯品会标记退款完成后更新主表订单号及物流单号", this.getClass().getSimpleName());
                                    List<OcBOrderItem> orderItemList = orderItemMapper.selectUnSuccessRefundAndNoSplit(order.getId());
                                    if (CollectionUtils.isNotEmpty(orderItemList)) {
                                        List<String> tidList = orderItemList.stream().filter(x -> StringUtils.isNotEmpty(x.getTid())).map(OcBOrderItem::getTid).distinct().collect(Collectors.toList());
                                        if (!tidList.contains(originTid)) {
                                            List<IpBJitxOrder> jitxOrders = ipJitxOrderService.queryAuditOrder(tidList);
                                            if(CollectionUtils.isNotEmpty(jitxOrders)){
                                                IpBJitxOrder ipBJitxOrder = jitxOrders.get(0);
                                                OcBOrder update = new OcBOrder();
                                                update.setId(order.getId());
                                                update.setTid(ipBJitxOrder.getOrderSn());
                                                update.setSourceCode(ipBJitxOrder.getOrderSn());
                                                update.setJitxMergedDeliverySn(ipBJitxOrder.getOrderSn());
                                                update.setExpresscode(ipBJitxOrder.getTransportNo());
                                                ocBOrderMapper.updateById(update);
                                            }
                                        }
                                    }
                                }
                            }
                            log.debug("标记为退款完成调用保存服务start:{}" , relation);
                            //订单明细处理
                            vh = ocBorderUpdateServiceExt.updateOrder(relation, user);
                            log.debug("标记为退款完成调用保存服务end:{}" , vh);
                            if (!vh.isOK()) {
                                throw new NDSException("调用保存服务出错" + vh.getMessage());
                            }
                        } catch (Exception e) {
                            log.error("标记为退款完成调用保存服务出错,异常信息:{}", Throwables.getStackTraceAsString(e));
                            throw new NDSException("调用保存服务出错:" + e.getMessage());
                        }
                    }
                }

                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("标记成功！");
                return vh;
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
            }
        } catch (Exception ex) {
            log.error("{},标记退款完成异常,orderId:{},异常信息：{}", this.getClass().getSimpleName(), esOcbOrderId,Throwables.getStackTraceAsString(ex));
            throw new NDSException(Resources.getMessage("订单更新错误！" + ex.getMessage(), user.getLocale()));

        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 标记退款取消
     *
     * @param orderId
     * @param itemIds
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder markRefundCancel(Long orderId, List<Long> itemIds, User user) {
        ValueHolder vh = new ValueHolder();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = this.ocBOrderMapper.selectByID(orderId);
                if (ocBOrder == null) {
                    throw new NDSException("订单ID " + orderId + "对应的订单不存在");
                }
//                if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
//                    throw new NDSException("当前订单为JITX订单, 不允许标记退款取消");
//                }
                if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())
                        && !OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())) {
                    throw new NDSException("当前订单状态非待审核或者缺货状态，不允许操作");
                }
                List<OcBOrderItem> items = ocBOrderItemMapper.selectOrderItemListsByIds(orderId, itemIds);
                for (OcBOrderItem item : items) {
                    if (!InreturningStatus.INRETURNING.equals(item.getRefundStatus())) {
                        throw new NDSException(Resources.getMessage("勾选的明细中有非《退款中》的明细", user.getLocale()));
                    }
                    OcBOrderItem updateItem = new OcBOrderItem();
                    updateItem.setRefundStatus(InreturningStatus.INRETURN_NO);
                    QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("oc_b_order_id", item.getOcBOrderId());
                    queryWrapper.eq("id", item.getId());
                    ocBOrderItemMapper.update(updateItem, queryWrapper);
                }
                // 插入订单日志服务
                try {
                    String str = items.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.joining(","));
                    String logMessage = str + "条码标记退款取消";
                    String param = "";
                    String errorMessage = "";
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.MARK_REFUND_CANCEL.getKey(), logMessage, param, errorMessage, user);
                } catch (Exception e) {
                    log.error("调用订单日志服务出错:", e);
                }

                List<OcBOrderItem> itemList = ocBOrderItemMapper.selectUnSuccessRefund(orderId);
                if (CollectionUtils.isNotEmpty(itemList)) {
                    List<OcBOrderItem> results =
                            itemList.stream().filter(item -> item.getRefundStatus() != null && item.getRefundStatus() == 1)
                                    .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(results)) {
                        OcBOrder order = new OcBOrder();
                        order.setIsInreturning(0);
                        order.setId(ocBOrder.getId());
                        omsOrderService.updateOrderInfo(order);
                        //是否已经拦截 Hold单统一调用 HOLD单方法
                        order.setIsInterecept(0);
                        ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
                    }
                }

                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "标记退款取消成功！");
                return vh;
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
            }
        } catch (Exception ex) {
            throw new NDSException(ex.getMessage());
        } finally {
            redisLock.unlock();
        }
    }
}
