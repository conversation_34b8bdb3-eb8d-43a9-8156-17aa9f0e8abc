package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.SelectOrderUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/3/18 1:58 下午
 * @Version 1.0
 * 批量替换下挂组合商品
 */
@Slf4j
@Component
public class BatchReplaceHangDownGoodsService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private SelectOrderUtil selectOrderUtil;
    @Autowired
    private OmsReplaceComposeService omsReplaceComposeService;


    public ValueHolderV14 batchReplaceHangDownGoods(JSONObject param, User user, UserPermission usrPem) {
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            if (param.isEmpty()) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("传入的参数不能为空");
                return vh;
            }
            String changeGoodsSKu = param.getString("changeGoodsSku");
            if (StringUtils.isEmpty(changeGoodsSKu)) {
                vh.setMessage("请输入需要替换的条码");
                vh.setCode(ResultCode.FAIL);
                return vh;
            }
            // step01 根据传入得参数来确定用不用查询id

            JSONArray ids = param.getJSONArray("ids");
            if (CollectionUtils.isNotEmpty(ids)) {
                List<Long> orders = ids.stream().map(p -> (Long) p).collect(Collectors.toList());
                this.handleOrder(orders, changeGoodsSKu, user);
            } else {
                List<Long> orders = selectOrderUtil.checkParamForIds(param, user, usrPem);
                this.handleOrder(orders, changeGoodsSKu, user);
            }
            vh.setMessage("处理成功");
            vh.setCode(ResultCode.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("批量替换下挂商品异常: {}"), Throwables.getStackTraceAsString(e));
            vh.setMessage("程序异常");
            vh.setCode(ResultCode.FAIL);
        }
        return vh;
    }


    /**
     * @param ids
     * @param changeGoodsSKu
     */
    private void handleOrder(List<Long> ids, String changeGoodsSKu, User operateUser) {
        //批量查询订单(只查询缺货或者待审核的)
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsByStatus(ids);
        for (OcBOrder ocBOrder : ocBOrders) {
            OcBOrderItem ocBOrderItem = ocBOrderItemMapper.queryOrderItemBySku(changeGoodsSKu, ocBOrder.getId());
            if (ocBOrderItem == null) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.COMBINATION_SPLIT.getKey(), "订单已被拆分,无法替换下挂商品", null, null, operateUser);
                continue;
            } else {
                if (selectOrderUtil.selectOrder(ocBOrderItem.getOoid())) {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.COMBINATION_SPLIT.getKey(), "订单已被拆分,无法替换下挂商品", null, null, operateUser);
                    continue;
                }
            }
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemByBagSku(ocBOrder.getId(), changeGoodsSKu);
            if (CollectionUtils.isEmpty(orderItems)) {
                continue;
            }
            omsReplaceComposeService.getOcBOrderItems(changeGoodsSKu, ocBOrder, ocBOrderItem, orderItems, operateUser, false);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.COMBINATION_SPLIT.getKey(), "替换条码:" + changeGoodsSKu + "成功", null, null, operateUser);
        }
    }
}



