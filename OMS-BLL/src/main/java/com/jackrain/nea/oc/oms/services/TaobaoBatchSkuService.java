package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.IpBOrderTjxtLogMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoOrderMapper;
import com.jackrain.nea.oc.oms.model.table.IpBOrderTjxtLog;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName : TaobaoBatchSkuService
 * @Description : 淘宝中间表批量修改sku异常信息
 * <AUTHOR>  YCH
 * @Date: 2021-12-08 15:11
 */
@Slf4j
@Component
public class TaobaoBatchSkuService {

    @Autowired
    private IpBTaobaoOrderMapper ipBTaobaoOrderMapper;
    @Autowired
    private IpBTaobaoOrderItemMapper ipBTaobaoOrderItemMapper;
    @Autowired
    private IpBOrderTjxtLogMapper ipBOrderTjxtLogMapper;

    @Transactional
    public ValueHolderV14 handle(JSONObject jsonObject, User user) {

        ValueHolderV14 vhResult = new ValueHolderV14(ResultCode.SUCCESS, "处理完毕");

        //获取入参
        String cpCShop = jsonObject.getString("cp_c_shop_id");
        Date beforDate = jsonObject.getDate("beforDate");
        Date afterDate = jsonObject.getDate("afterDate");
        Integer isBehind = jsonObject.getInteger("isBehind");
        String content = jsonObject.getString("content");
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        String property = config.getProperty("r3.oc.oms.skuerror", "*,%");
        if (!property.contains(content)){
            vhResult.setCode(ResultCode.FAIL);
            vhResult.setMessage("该特殊字符没有维护！不符合规范，请检查");
            return vhResult;
        }
        String[] split1 = cpCShop.split(",");
        List<String> cpList = Arrays.asList(split1);
        //根据店铺和付款时间查询
        List<IpBTaobaoOrder> ipBTaobaoOrders = ipBTaobaoOrderMapper.selectList(new QueryWrapper<IpBTaobaoOrder>().lambda()
                .in(IpBTaobaoOrder::getCpCShopId, cpList)
                .between(IpBTaobaoOrder::getPayTime, beforDate, afterDate));
        if (CollectionUtils.isEmpty(ipBTaobaoOrders)) {
            return vhResult;
        }
        List<Long> idList = ipBTaobaoOrders.stream().map(s -> s.getId()).collect(Collectors.toList());
        List<IpBTaobaoOrderItem> ipBTaobaoOrderItems = ipBTaobaoOrderItemMapper.selectList(new QueryWrapper<IpBTaobaoOrderItem>().lambda()
                .in(IpBTaobaoOrderItem::getIpBTaobaoOrderId, idList));
        if (CollectionUtils.isEmpty(ipBTaobaoOrderItems)) {
            return vhResult;
        }
        List<IpBOrderTjxtLog> ipBOrderTjxtLogs = new ArrayList<>();
        //匹配，处理业务
        for (IpBTaobaoOrderItem ipBTaobaoOrderItem : ipBTaobaoOrderItems) {
            //自定义sku
            String outerSkuId = ipBTaobaoOrderItem.getOuterSkuId();
            Long ipBTaobaoOrderId = ipBTaobaoOrderItem.getIpBTaobaoOrderId();
            //自定义sku长度小于敏感字符长度跳过
            if (StringUtils.isEmpty(outerSkuId) || outerSkuId.length() < content.length()) {
                continue;
            }
            if (isBehind == 0) {
                //sku去除最前面特殊字段
                if (outerSkuId.substring(0, content.length()).equals(content)) {
                    IpBTaobaoOrder ipBTaobaoOrder = new IpBTaobaoOrder();
                    ipBTaobaoOrder.setId(ipBTaobaoOrderId);
                    ipBTaobaoOrder.setIstrans(0);
                    ipBTaobaoOrderMapper.updateById(ipBTaobaoOrder);
                    ipBTaobaoOrderItem.setOuterSkuId(outerSkuId.substring(content.length()));
                    ipBTaobaoOrderItemMapper.updateById(ipBTaobaoOrderItem);
                    ipBOrderTjxtLogs.add(addLog(ipBTaobaoOrderId, outerSkuId, ipBTaobaoOrderItem.getOuterSkuId(), user));
                }
            } else {
                //sku去除最后的特殊字段
                if (outerSkuId.substring(outerSkuId.length() - content.length()).equals(content)) {
                    IpBTaobaoOrder ipBTaobaoOrder = new IpBTaobaoOrder();
                    ipBTaobaoOrder.setId(ipBTaobaoOrderId);
                    ipBTaobaoOrder.setIstrans(0);
                    ipBTaobaoOrderMapper.updateById(ipBTaobaoOrder);
                    ipBTaobaoOrderItem.setOuterSkuId(outerSkuId.substring(0, outerSkuId.length() - content.length()));
                    ipBTaobaoOrderItemMapper.updateById(ipBTaobaoOrderItem);
                    ipBOrderTjxtLogs.add(addLog(ipBTaobaoOrderId, outerSkuId, ipBTaobaoOrderItem.getOuterSkuId(), user));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(ipBOrderTjxtLogs)){
            ipBOrderTjxtLogMapper.batchInsert(ipBOrderTjxtLogs);
        }
        return vhResult;
    }

    public IpBOrderTjxtLog addLog(Long ipBTaobaoOrderId, String bmod, String amod, User user) {
        IpBOrderTjxtLog ipBOrderTjxtLog = new IpBOrderTjxtLog();
        ipBOrderTjxtLog.setId(ModelUtil.getSequence("ip_b_order_tjxt_log"));
        ipBOrderTjxtLog.setIpBTaobaoOrderId(ipBTaobaoOrderId);
        ipBOrderTjxtLog.setModcontent("SKU异常处理");
        ipBOrderTjxtLog.setAmod(amod);
        ipBOrderTjxtLog.setBmod(bmod);
        BaseModelUtil.makeBaseModifyField(ipBOrderTjxtLog, user);
        return ipBOrderTjxtLog;
    }
}
