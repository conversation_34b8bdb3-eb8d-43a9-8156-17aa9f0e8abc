package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.hub.api.minipt.HubOrderCmd;
import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
import com.jackrain.nea.hub.model.HXInvoicingModel.ResultType;
import com.jackrain.nea.hub.model.minipt.ReturnToDouChaoReq;
import com.jackrain.nea.hub.model.minipt.ReturnToTmallDddReq;
import com.jackrain.nea.hub.request.naika.NaiKaRefundStockInItemRequest;
import com.jackrain.nea.hub.request.naika.NaiKaRefundStockInRequest;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.IsWrongReceive;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnOrderDealTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.request.CancelOrderModel;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 退单处理服务
 *
 * @author: 周琳胜
 * @since: 2019/4/3
 * create at : 2019/4/3 10:40
 */
@Slf4j
@Component
public class OcBReturnOrderService {

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;
    @Reference(group = "hub", version = "1.0")
    private NaiKaOrderCmd naiKaOrderCmd;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private ReturnOrderLogService returnOrderLogService;

    @Reference(group = "hub", version = "1.0")
    private HubOrderCmd hubOrderCmd;

    /**
     * 查找符合条件的退单审核的订单
     *
     * @param pageIndex 初始页面index
     * @param pageSize  页面数据大小
     * @return List<Long>
     */
    public List<Long> selectChargebackToCheck(int pageIndex, int pageSize) {
        JSONObject whereKeys = new JSONObject();
        //退单状态”为等待售后确认
        whereKeys.put("RETURN_STATUS", ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
        whereKeys.put("IS_WRONG_RECEIVE",IsWrongReceive.NO.val());
        return ES4ReturnOrder.queryEsOrderList(whereKeys, pageIndex, pageSize);
    }

    /**
     * 根据原单查询换货单类型
     *
     * @param origRetunOrderId 原始退货单
     * @return OcBReturnOrder
     */
    public OcBReturnOrder queryOcBReturnOrder(Long origRetunOrderId) {
        return ocBReturnOrderMapper.queryOcBReturnOrder(origRetunOrderId);
    }

    public ValueHolderV14 cancelReturnOrder(CancelOrderModel cancelOrderModel){
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBReturnOrderService.cancelReturnOrder.request={}",
                    "OcBOrderCancelService.cancelReturnOrder"), JSON.toJSONString(cancelOrderModel));
        }
        ValueHolderV14 vh = ValueHolderV14Utils.getSuccessValueHolder("取消成功！");
        try{
            String tid = cancelOrderModel.getTid();
            //平台退款单号
            String returnId = cancelOrderModel.getReturnId();
            //查询订单是否存在
            List<Long> idList = ocBReturnOrderMapper.listIdFromGsiByReturnId(returnId);
            if(CollectionUtils.isEmpty(idList)){
                return ValueHolderV14Utils.custom(1,"订单不存在,请稍后再试！",null);
            }
            Long id = idList.get(0);
            Integer orderStatus = null;
            //根据订单状态
            JSONObject jsonObject = ES4ReturnOrder.findIdAndStatusByTid(id);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("ID:{},ES4ReturnOrder.findIdAndStatusByTid.jsonObject={}",
                        "OcBOrderCancelService.cancelReturnOrder",id),id,jsonObject.toJSONString());
            }
            if(jsonObject.isEmpty()){
                //查询数据库
                OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectOne(Wrappers.<OcBReturnOrder>lambdaQuery()
                        .select(OcBReturnOrder::getReturnStatus)
                        .eq(OcBReturnOrder::getId,id));
                orderStatus = ocBReturnOrder.getReturnStatus();
            }else{
                orderStatus = jsonObject.getInteger("RETURN_STATUS");
            }
            if(ReturnStatusEnum.CANCLE.getVal().equals(orderStatus)){
                return ValueHolderV14Utils.getSuccessValueHolder("当前订单已为取消状态，不再进行取消操作！");
            }
            User user = SystemUserResource.getRootUser();
            //取消逻辑
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(id);
            ValueHolderV14 v14 = ocCancelChangingOrRefundService.oneOcCancle(user,new ValueHolderV14(),jsonArray);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("ID:{},OcCancelChangingOrRefundService.oneOcCancle.v14={}",
                        "OcBOrderCancelService.cancelReturnOrder",id),id,v14.toJSONObject().toJSONString());
            }
            if(v14.isOK()){
                return ValueHolderV14Utils.getSuccessValueHolder("取消成功！");
            } else {
                return ValueHolderV14Utils.getSuccessValueHolder("取消失败！");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("OcBReturnOrderService.cancelReturnOrder.error={}", "OcBReturnOrderService.cancelReturnOrder"),
                    Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder("取消失败！");
        }
    }

    public ValueHolderV14 toNaiKa(String param, User user) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.FAIL);
        if (StringUtils.isEmpty(param)) {
            valueHolderV14.setMessage("参数不能为空");
            return valueHolderV14;
        }
        String[] idArr = param.split(",");
        List<String> errorIds = new ArrayList<>();
        StringBuilder sb = new StringBuilder();

        for (String id : idArr) {
            // 根据提奶查询退换货单
            OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(Long.valueOf(id));
            // 判断是否需要传奶卡系统
            if (ObjectUtil.notEqual(PlatFormEnum.CARD_CODE.getCode(), ocBReturnOrder.getPlatform()) &&
                    ObjectUtil.notEqual(PlatFormEnum.CREATE_CARD_CODE.getCode(), ocBReturnOrder.getPlatform())) {
                continue;
            }
            if (ObjectUtil.notEqual(ocBReturnOrder.getToNaikaStatus(), ReturnNaiKaStatusEnum.UN_PUSH.getStatus())
                    && ObjectUtil.notEqual(ocBReturnOrder.getToNaikaStatus(), ReturnNaiKaStatusEnum.PUSH_FAIL.getStatus())) {
                errorIds.add(ocBReturnOrder.getBillNo());
                continue;
            }
            if (ObjectUtil.isNotNull(ocBReturnOrder.getIsWrongReceive()) &&
                    ObjectUtil.notEqual(ocBReturnOrder.getIsWrongReceive(), IsWrongReceive.NO)) {
                continue;
            }
            CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(Long.valueOf(ocBReturnOrder.getPlatform()));
            NaiKaRefundStockInRequest request = new NaiKaRefundStockInRequest();
            request.setTid(ocBReturnOrder.getTid());
            request.setRefundNo(ocBReturnOrder.getReturnId());
            request.setPlatformCode(cpCPlatform.getEcode());
            List<OcBReturnOrderRefund> ocBReturnOrderRefundList =
                    ocBReturnOrderRefundMapper.selectByOcOrderId(ocBReturnOrder.getId());
            List<NaiKaRefundStockInItemRequest> itemRequestList = new ArrayList<>();
            for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefundList) {
                NaiKaRefundStockInItemRequest itemRequest = new NaiKaRefundStockInItemRequest();
                itemRequest.setSubOrderId(ocBReturnOrderRefund.getOid());
                itemRequest.setNum(ocBReturnOrderRefund.getQtyIn());
                itemRequest.setActNum(ocBReturnOrderRefund.getQtyRefund());
                itemRequest.setSkuCode(ocBReturnOrderRefund.getPsCSkuEcode());
                itemRequestList.add(itemRequest);
            }
            request.setRefundStockInItemRequestList(itemRequestList);
            OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
            String logContent = "";
            try {
                ValueHolderV14 holderV14 = naiKaOrderCmd.orderRefundStockIn(request);
                // 增加日志
                if (holderV14.isOK()) {
                    updateOcBReturnOrder.setOrigSourceCode(ocBReturnOrder.getOrigSourceCode());
                    updateOcBReturnOrder.setId(ocBReturnOrder.getId());
                    updateOcBReturnOrder.setModifieddate(new Date());
                    updateOcBReturnOrder.setToNaikaStatus(ReturnNaiKaStatusEnum.PUSHED.getStatus());
                    ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                    logContent = "同步奶卡系统成功";
                } else {
                    updateOcBReturnOrder.setOrigSourceCode(ocBReturnOrder.getOrigSourceCode());
                    updateOcBReturnOrder.setId(ocBReturnOrder.getId());
                    updateOcBReturnOrder.setModifieddate(new Date());
                    updateOcBReturnOrder.setToNaikaStatus(ReturnNaiKaStatusEnum.PUSH_FAIL.getStatus());
                    ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                    sb.append(holderV14.getMessage());
                    errorIds.add(ocBReturnOrder.getBillNo());
                    logContent = "同步奶卡系统失败,原因:" + (ObjectUtil.isEmpty(holderV14.getMessage()) ? "" : holderV14.getMessage());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("OcBReturnOrderService.toNaiKa.error={}", "OcBReturnOrderService.toNaiKa"),
                        Throwables.getStackTraceAsString(e));
                updateOcBReturnOrder.setOrigSourceCode(ocBReturnOrder.getOrigSourceCode());
                updateOcBReturnOrder.setId(ocBReturnOrder.getId());
                updateOcBReturnOrder.setModifieddate(new Date());
                updateOcBReturnOrder.setToNaikaStatus(ReturnNaiKaStatusEnum.PUSH_FAIL.getStatus());
                ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                errorIds.add(ocBReturnOrder.getBillNo());
                logContent = "同步奶卡系统失败,原因:" + (ObjectUtil.isEmpty(e.getMessage()) ? "" : e.getMessage());
            }
            returnOrderLogService.addRefundOrderLog(ocBReturnOrder.getId(), "同步奶卡系统", logContent, user);
        }
        if (CollectionUtils.isNotEmpty(errorIds)) {
            sb.append("退单:");
            sb.append(JSONUtil.toJsonStr(errorIds));
            sb.append("。");
            sb.append("推送小程序失败");
            valueHolderV14.setMessage(sb.toString());
        } else {
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("success");
        }
        return valueHolderV14;
    }

    public ValueHolderV14 markFinish(String param) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.FAIL);
        if (StringUtils.isEmpty(param)) {
            valueHolderV14.setMessage("参数不能为空");
            return valueHolderV14;
        }
        String[] idArr = param.split(",");

        List<String> errIds = new ArrayList<>();
        for (String id : idArr) {
            // 更新已发货退款单 将状态调整为 标记完成
            Long ocBReturnOrderId = Long.valueOf(id);
            OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(ocBReturnOrderId);

            // 进行状态校验
            if (ObjectUtil.notEqual(ReturnNaiKaStatusEnum.PUSH_FAIL.getStatus(), ocBReturnOrder.getToNaikaStatus()) &&
                    ObjectUtil.notEqual(ReturnNaiKaStatusEnum.UN_PUSH.getStatus(), ocBReturnOrder.getToNaikaStatus())) {
                errIds.add(ocBReturnOrder.getBillNo());
                continue;
            }
            OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
            updateOcBReturnOrder.setId(ocBReturnOrderId);
            updateOcBReturnOrder.setOrigSourceCode(ocBReturnOrder.getOrigSourceCode());
            updateOcBReturnOrder.setModifieddate(new Date());
            updateOcBReturnOrder.setToNaikaStatus(ReturnNaiKaStatusEnum.OFFLINE_FINISH.getStatus());
            ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
        }
        if (CollectionUtils.isNotEmpty(errIds)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(JSONUtil.toJsonStr(errIds) + "订单因订单状态不合法处理失败，其余均标记成功");
            return valueHolderV14;
        }
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");
        return valueHolderV14;
    }

    /**
     * 处理tmall ddd 退货回调平台
     */
    public void tmallDdd() {
        //查询完成，未处理的数据
        List<OcBReturnOrder> returnOrders = ocBReturnOrderMapper.selectPlatformNoDealOrders(ReturnStatusEnum.COMPLETION.getVal(), PlatFormEnum.TMALL_DDD.getCode());
        if (CollectionUtils.isEmpty(returnOrders)) {
            return;
        }

        List<Long> ids = returnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
        List<OcBReturnOrderRefund> returnOrderRefunds = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(ids);
        Map<Long, List<OcBReturnOrderRefund>> detailMap = returnOrderRefunds.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getOcBReturnOrderId));

        for (OcBReturnOrder returnOrder : returnOrders) {
            try {
                ReturnToTmallDddReq req = new ReturnToTmallDddReq();
                req.setReturnNo(returnOrder.getReturnId());
                req.setShopId(returnOrder.getCpCShopId());
                req.setReturnNoConfirmTime(returnOrder.getConfirmDate());

                List<ReturnToTmallDddReq.Item> itemList = Lists.newArrayList();
                List<OcBReturnOrderRefund> refunds = detailMap.get(returnOrder.getId());
                if (CollectionUtils.isEmpty(refunds)) {
                    continue;
                }

                for (OcBReturnOrderRefund refund : refunds) {
                    ReturnToTmallDddReq.Item item = new ReturnToTmallDddReq.Item();
                    item.setSubOrderId(refund.getOid());
                    item.setSkuId(refund.getPsCSkuEcode());
                    item.setReturnQuantity(refund.getQtyIn() == null ? 0 : refund.getQtyIn().intValue());
                    itemList.add(item);
                }
                req.setItemList(itemList);

                ResultType resultType = hubOrderCmd.returnToTmallDdd(req);
                log.info(LogUtil.format("tmallDdd req:{},resultType:{}", "OcBReturnOrderService.tmallDdd"), JSON.toJSONString(req), JSON.toJSONString(resultType));

                if (resultType == null){
                    continue;
                }

                if (String.valueOf(ResultCode.SUCCESS).equals(resultType.getCode())) {
                    //更新退换货单处理完成
                    OcBReturnOrder update = new OcBReturnOrder();
                    update.setId(returnOrder.getId());
                    update.setModifieddate(new Date());
                    update.setDealType(ReturnOrderDealTypeEnum.TMALL_DDD_RETURN.getKey());
                    ocBReturnOrderMapper.updateById(update);
                }

                log.info(LogUtil.format("tmallDdd done billNo:{}", "OcBReturnOrderService.tmallDdd"), returnOrder.getBillNo());
            } catch (Exception e) {
                log.error(LogUtil.format("OcBReturnOrderService.tmallDdd.error={}", "OcBReturnOrderService.tmallDdd"), Throwables.getStackTraceAsString(e));
            }
        }
    }

    /**
     * 处理抖超退货回调平台
     */
    public void douchaoReturnBack() {
        //查询完成，未处理的数据
        List<OcBReturnOrder> returnOrders = ocBReturnOrderMapper.selectPlatformNoDealOrders(ReturnStatusEnum.COMPLETION.getVal(), PlatFormEnum.DOUCHAO.getCode());
        if (CollectionUtils.isEmpty(returnOrders)) {
            return;
        }

        List<Long> ids = returnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
        List<OcBReturnOrderRefund> returnOrderRefunds = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(ids);
        Map<Long, List<OcBReturnOrderRefund>> detailMap = returnOrderRefunds.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getOcBReturnOrderId));

        for (OcBReturnOrder returnOrder : returnOrders) {
            try {
                ReturnToDouChaoReq req = new ReturnToDouChaoReq();
                req.setReturnNo(returnOrder.getReturnId());
                req.setShopId(returnOrder.getCpCShopId());
                req.setOperateTime(returnOrder.getInTime() == null ? new Date() : returnOrder.getInTime());

                List<ReturnToDouChaoReq.Item> itemList = Lists.newArrayList();
                List<OcBReturnOrderRefund> refunds = detailMap.get(returnOrder.getId());
                if (CollectionUtils.isEmpty(refunds)) {
                    continue;
                }

                for (OcBReturnOrderRefund refund : refunds) {
                    ReturnToDouChaoReq.Item item = new ReturnToDouChaoReq.Item();
                    item.setSubOrderId(refund.getOid());
                    item.setReturnQuantity(refund.getQtyIn() == null ? 0 : refund.getQtyIn().intValue());
                    itemList.add(item);
                }
                req.setItemList(itemList);

                ResultType resultType = hubOrderCmd.returnToDouchao(req);
                log.info(LogUtil.format("douchao req:{},resultType:{}", "OcBReturnOrderService.douchaoReturnBack"), JSON.toJSONString(req), JSON.toJSONString(resultType));

                if (resultType == null){
                    continue;
                }

                if (String.valueOf(ResultCode.SUCCESS).equals(resultType.getCode())) {
                    //更新退换货单处理完成
                    OcBReturnOrder update = new OcBReturnOrder();
                    update.setId(returnOrder.getId());
                    update.setModifieddate(new Date());
                    update.setDealType(ReturnOrderDealTypeEnum.TMALL_DDD_RETURN.getKey());
                    ocBReturnOrderMapper.updateById(update);
                }

                log.info(LogUtil.format("douchao done billNo:{}", "OcBReturnOrderService.douchaoReturnBack"), returnOrder.getBillNo());
            } catch (Exception e) {
                log.error(LogUtil.format("OcBReturnOrderService.douchao.error={}", "OcBReturnOrderService.douchaoReturnBack"), Throwables.getStackTraceAsString(e));
            }
        }
    }
}
