package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.util.TypeUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderWmsStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.request.CancelOrderModel;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * date ：Created in 10:42 2019/12/5
 * description ：全渠道订单取消业务
 * @ Modified By：
 */
@Slf4j
@Service
public class OcBOrderCancelService {
    @Autowired
    private OcBOrderOffService ocBOrderOffService;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;


    public ValueHolder cancelOrder(QuerySession querySession) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("全渠道订单取消服务begin"));
        }
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        if (null == param) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("全渠道订单取消服务param：") + param);
        }

        boolean isIDS = param.containsKey("ids");
        boolean isObjId = param.containsKey("objid");

        if (!(isIDS || isObjId)) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        if (isIDS && isObjId) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        if (isIDS && (param.getJSONArray("ids").size() <= 0)) {
            throw new NDSException(Resources.getMessage("请选择需要取消的单据记录！", querySession.getLocale()));
        }

        if (isIDS) {
            //列表批量处理
            vh = batchMarked(param, querySession);
        } else {
            //单对象界面处理
            Long objid = param.getLongValue("objid");
            JSONObject jsonObject;
            try {
                jsonObject = separateMarked(objid, querySession);
                vh.put("code", TypeUtils.castToInt(jsonObject.get("code")));
                vh.put("message", Resources.getMessage(jsonObject.getString("message"), querySession.getLocale()));
            } catch (Exception e) {
                e.printStackTrace();
                vh.put("code", -1);
                vh.put("message", Resources.getMessage(e.getMessage(), querySession.getLocale()));
            }
        }
        return vh;
    }


    public JSONObject separateMarked(Long objid, QuerySession querySession) {
        JSONObject jsonObject = new JSONObject();
        String logContent = "全渠道订单取消成功";
        try {
            boolean b = ocBOrderOffService.startCancelOrderByLock(querySession.getUser(), objid, OrderLogTypeEnum.ORDER_CANCLE.getKey(), logContent);
            if (b) {
                jsonObject.put("message", Resources.getMessage("订单取消成功", querySession.getLocale()));
                jsonObject.put("code", 0);
            } else {
                jsonObject.put("message", Resources.getMessage("订单取消失败", querySession.getLocale()));
                jsonObject.put("code", -1);
            }
        } catch (Exception e) {
            e.printStackTrace();
            jsonObject.put("message", Resources.getMessage("订单取消失败，" + e.getMessage(), querySession.getLocale()));
            jsonObject.put("code", -1);
        }
        return jsonObject;
    }

    private ValueHolder batchMarked(JSONObject param, QuerySession querySession) {
        ValueHolder valueHolder = new ValueHolder();
        int success = 0;
        int fail = 0;
        Object[] ids = param.getJSONArray("ids").toArray();
        JSONArray listArray = new JSONArray();
        for (Object id : ids) {
            Long objid = TypeUtils.castToLong(id);
            JSONObject retJson;
            try {
                retJson = separateMarked(objid, querySession);
            } catch (Exception e) {
                e.printStackTrace();
                retJson = new JSONObject();
                retJson.put("message", Resources.getMessage(e.getMessage(), querySession.getLocale()));
                retJson.put("code", -1);
                retJson.put("objid", objid);
                listArray.add(retJson);
                fail++;
                continue;
            }
            if (retJson.containsKey("code") && retJson.getInteger("code") == 0) {
                success++;
            } else {
                fail++;
                listArray.add(retJson);
            }
        }

        valueHolder.put("data", listArray);
        if (0 == fail) {
            valueHolder.put("code", 0);
        } else {
            valueHolder.put("code", -1);
        }
        valueHolder.put("message", Resources.getMessage("取消成功的记录数：" + success + ",失败的记录数：" + fail, querySession.getLocale()));
        return valueHolder;
    }

    /**
     * 通过平台单号取消零售发货单
     * @param cancelOrderModel
     * @return
     */
    public ValueHolderV14 cancelOrderByTid(CancelOrderModel cancelOrderModel){
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderCancelService.cancelOrderByTid.request={}",
                    "OcBOrderCancelService.cancelOrderByTid"),JSON.toJSONString(cancelOrderModel));
        }
        ValueHolderV14 vh = ValueHolderV14Utils.getSuccessValueHolder("取消成功！");
        try{
            String tid = cancelOrderModel.getTid();
            List<Long> idList = orderMapper.listIdFromGsiBySourceCode(tid);
            if(CollectionUtils.isEmpty(idList)){
                return ValueHolderV14Utils.custom(1,"订单不存在,请稍后再试！",null);
            }
            Long id = idList.get(0);
            User user = SystemUserResource.getRootUser();
            //查询订单状态
            Integer status = null;
            Integer wmsCancelStatus = null;
            //查询ES 根据订单ID查询订单状态
            JSONObject jsonObject = ES4Order.getOrderStatusById(id);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("ID:{},ES4Order.getOrderStatusById.jsonObject={}",
                        "OcBOrderCancelService.cancelOrderByTid",id),id,jsonObject.toJSONString());
            }
            if(jsonObject.isEmpty()){
                //查询数据库
                OcBOrder ocBOrder = orderMapper.selectOne(Wrappers.<OcBOrder>lambdaQuery()
                        .select(OcBOrder::getOrderStatus,OcBOrder::getWmsCancelStatus)
                        .eq(OcBOrder::getId,id));
                status = ocBOrder.getOrderStatus();
                wmsCancelStatus = ocBOrder.getWmsCancelStatus();
            }else{
                status = jsonObject.getInteger("ORDER_STATUS");
                wmsCancelStatus = jsonObject.getInteger("WMS_CANCEL_STATUS");
            }
            if(OmsOrderStatus.CANCELLED.toInteger().equals(status)){
                return ValueHolderV14Utils.getSuccessValueHolder("当前订单已为取消状态，不再进行取消操作！");
            }
            boolean isAuditSuccess = true;
            //如果订单为配货中状态，则先进行反审核，再进行订单取消
            if(OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(status)
                    && wmsCancelStatus.intValue() != OcOrderWmsStatus.WMS_SUCCESS.getVal()){
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("取消零售发货单ID:{} 订单状态为配货单，WMS撤回状态非撤回成功，开始进行订单反审核",
                            "OcBOrderCancelService.cancelOrderByTid",id,tid),id);
                }
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        isAuditSuccess = ocBOrderTheAuditService.updateOrderInfo(user,new ValueHolderV14(),id,false,1L,true);
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("取消零售发货单ID:{} 订单状态为配货单，WMS撤回状态非撤回成功，订单反审核结束。result:{}",
                                    "OcBOrderCancelService.cancelOrderByTid",id,tid),id,isAuditSuccess);
                        }
                    } else {
                        throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
                    }
                } catch (Exception ex) {
                    log.error(LogUtil.format("取消零售发货单ID:{},反审核失败:{}","OcBOrderCancelService.cancelOrderByTid",id,tid),
                            id,Throwables.getStackTraceAsString(ex));
                    isAuditSuccess = false;
                } finally {
                    redisLock.unlock();
                }
            }
            if(isAuditSuccess){
                String logContent = "全渠道订单取消成功";
                //取消零售发货单
                boolean isCancel = ocBOrderOffService.startCancelOrderByLock(user, id, OrderLogTypeEnum.ORDER_CANCLE.getKey(), logContent);
                if(!isCancel){
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("取消零售发货单ID:{} 订单取消失败",
                                "OcBOrderCancelService.cancelOrderByTid",id,tid),id);
                    }
                    return ValueHolderV14Utils.getFailValueHolder("取消失败！");
                }
            }else{
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("取消零售发货单ID:{} 订单反审核失败",
                            "OcBOrderCancelService.cancelOrderByTid",id,tid),id);
                }
                return ValueHolderV14Utils.getFailValueHolder("取消失败！");
            }
        }catch (Exception e){
            log.error(LogUtil.format("OcBOrderCancelService.cancelOrderByTid.error={}","OcBOrderCancelService.cancelOrderByTid"),
                    Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder("取消失败！");
        }
        return vh;
    }
}
