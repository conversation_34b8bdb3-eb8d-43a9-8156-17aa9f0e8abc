package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.qimen.QimenOrderCallbackModel;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.InterceptStatus;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: 夏继超
 * @since: 2019/3/11
 * create at : 2019/3/11 11:05
 */
@Slf4j
@Component
//@Transactional
public class OrderInterceptionService {
    @Autowired
    OcBOrderMapper ocOrderMapper;
    @Autowired
    OcBorderUpdateService ocBorderUpdateService;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcCancelChangingOrRefundService refundService;

    @Autowired
    IpRpcService ipRpcService;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    /**
     * 订单挂起
     *
     * @param object1   订单的id
     * @param loginUser 当前登录用户
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder orderInterception(JSONObject object1, User loginUser) throws NDSException {
        ValueHolder vh = new ValueHolder();
        //Long id = Long.valueOf(object1.get("ids").toString());
        JSONArray ids = object1.getJSONArray("ids");
        if (ids.isEmpty()) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", loginUser.getLocale()));
        } else {

            if (ids.size() == 1) {

                //id订单的id
                OcBOrder ocOrder = ocOrderMapper.selectById(Long.valueOf(ids.get(0).toString()));
                List list = new ArrayList();
                list.add(OcOrderCheckBoxEnum.CHECKBOX_WAREHOUSE_DELIVERY.getVal());
                list.add(OcOrderCheckBoxEnum.CHECKBOX_PLATFORM_DELIVERY.getVal());
                list.add(OcOrderCheckBoxEnum.CHECKBOX_CANCELLED.getVal());
                list.add(OcOrderCheckBoxEnum.CHECKBOX_SYSTEM_INVALIDATION.getVal());
                list.add(OcOrderCheckBoxEnum.CHECKBOX_LOGISTICS_DELIVERED.getVal());
                list.add(OcOrderCheckBoxEnum.CHECKBOX_TRANSACTION_COMPLETED.getVal());
                //给订单加锁
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(Long.valueOf(ids.get(0).toString()));
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        if (ocOrder != null) {
                            if (1 == ocOrder.getIsInterecept()) {
                                vh.put("code", ResultCode.FAIL);
                                vh.put("message", Resources.getMessage("当前订单已挂起，不允许重复挂起！", loginUser.getLocale()));
                                return vh;
                                //仓库发货”“平台发货”、“已取消”、“系统作废”、“物流已送达”、“交易完成”
                            } else if (list.contains(ocOrder.getOrderStatus())) {
                                vh.put("code", ResultCode.FAIL);
                                vh.put("message", Resources.getMessage(" 当前订单已发货，不允许挂起！", loginUser.getLocale()));
                                return vh;
                            } else {
                                ocOrder.setIsInterecept(1);//是否已经拦截 使用HOLD单方法修改
                                vh = ocBOrderHoldService.holdOrUnHoldOrder(ocOrder, OrderHoldReasonEnum.REFUND_HOLD);
                                //修改人待修改
                                /*ocOrder.setModifierid(Long.valueOf(loginUser.getId()));
                                ocOrder.setModifieddate(new Date());
                                int i = ocOrderMapper.updateById(ocOrder);
                                if (i > 0) {
                                    vh.put("code", ResultCode.SUCCESS);
                                    vh.put("message", Resources.getMessage(" 订单挂起成功", loginUser.getLocale()));
                                } else {
                                    vh.put("code", ResultCode.FAIL);
                                    vh.put("message", Resources.getMessage(" 订单挂起失败", loginUser.getLocale()));
                                    return vh;
                                }
                                try {
                                    Boolean aBoolean = SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocOrder, ocOrder.getId());
                                    if (!aBoolean) {
                                        throw new NDSException(Resources.getMessage("订单挂起推送ES失败!", loginUser.getLocale()));
                                    }
                                } catch (Exception e) {
                                    throw new NDSException(Resources.getMessage("订单挂起推送ES失败!", loginUser.getLocale()));
                                }
                                //调用添加订单日志
                                try {
                                    omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "订单挂起成功", null, null, loginUser);
                                } catch (Exception e) {
                                    vh.put("code", ResultCode.FAIL);
                                    vh.put("message", Resources.getMessage("新增订单日志失败"));
                                }*/
                            }
                        } else {
                            throw new NDSException("当前记录已不存在！");
                        }
                    } else {
                        throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", loginUser.getLocale()));
                    }
                } catch (Exception ex) {
                    throw new NDSException(Resources.getMessage("订单更新锁单错误！", loginUser.getLocale()));
                } finally {
                    redisLock.unlock();
                }
            } else {
                int fail = 0;
                for (Object o : ids) {
                    //id订单的id
                    OcBOrder ocOrder = ocOrderMapper.selectById(Long.valueOf(o.toString()));
                    List list = new ArrayList();
                    list.add(OcOrderCheckBoxEnum.CHECKBOX_WAREHOUSE_DELIVERY.getVal());
                    list.add(OcOrderCheckBoxEnum.CHECKBOX_PLATFORM_DELIVERY.getVal());
                    list.add(OcOrderCheckBoxEnum.CHECKBOX_CANCELLED.getVal());
                    list.add(OcOrderCheckBoxEnum.CHECKBOX_SYSTEM_INVALIDATION.getVal());
                    list.add(OcOrderCheckBoxEnum.CHECKBOX_LOGISTICS_DELIVERED.getVal());
                    list.add(OcOrderCheckBoxEnum.CHECKBOX_TRANSACTION_COMPLETED.getVal());
                    try {
                        //给订单加锁
                        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(Long.valueOf(o.toString()));
                        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                        try {
                            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                                if (ocOrder != null) {
                                    if (1 == ocOrder.getIsInterecept()) {
                                        throw new NDSException(Resources.getMessage("当前订单已挂起，不允许重复挂起！", loginUser.getLocale()));
                                        //仓库发货”“平台发货”、“已取消”、“系统作废”、“物流已送达”、“交易完成”
                                    } else if (list.contains(ocOrder.getOrderStatus())) {
                                        throw new NDSException(Resources.getMessage("当前订单已发货，不允许挂起！！", loginUser.getLocale()));
                                    } else {
                                        ocOrder.setIsInterecept(1);//是否已经拦截 使用HOLD单方法修改
                                        vh = ocBOrderHoldService.holdOrUnHoldOrder(ocOrder, OrderHoldReasonEnum.REFUND_HOLD);
                                        /*//修改人待修改
                                        ocOrder.setModifierid(Long.valueOf(loginUser.getId()));
                                        ocOrder.setModifieddate(new Date());
                                        int i = ocOrderMapper.updateById(ocOrder);
                                        if (i > 0) {
                                            vh.put("code", ResultCode.SUCCESS);
                                            vh.put("message", Resources.getMessage(" 订单挂起成功", loginUser.getLocale()));
                                        } else {
                                            throw new NDSException(Resources.getMessage("挂起失败！！", loginUser.getLocale()));
                                        }
                                        Boolean aBoolean = SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocOrder, ocOrder.getId());
                                        if (!aBoolean) {
                                            throw new NDSException(Resources.getMessage("订单挂起推送ES失败!", loginUser.getLocale()));
                                        }
                                        //调用添加订单日志
                                        try {
                                            omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "订单挂起成功", null, null, loginUser);
                                        } catch (Exception e) {
                                            vh.put("code", ResultCode.FAIL);
                                            vh.put("message", Resources.getMessage("新增订单日志失败"));
                                        }*/
                                    }
                                } else {
                                    throw new NDSException("当前记录已不存在！");
                                }
                            } else {
                                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", loginUser.getLocale()));
                            }
                        } catch (Exception e) {
                            throw new NDSException(Resources.getMessage("订单更新锁单错误！", loginUser.getLocale()));
                        } finally {
                            redisLock.unlock();
                        }

                    } catch (Exception e) {
                        fail++;
                    }
                }
                vh.put("code", ResultCode.FAIL);
                vh.put("message", Resources.getMessage("挂起成功" + (ids.size() - fail) + "条，失败了" + fail + "条", loginUser.getLocale()));
                return vh;
            }
            return vh;
        }
    }

    /**
     * 批量订单解挂
     *
     * @param object
     * @param loginUser
     * @return
     */
    public ValueHolder mainCancelInterception(JSONObject object, User loginUser) {
        JSONArray ids = object.getJSONArray("ids");
        ValueHolder vh = new ValueHolder();
        OrderInterceptionService bean = ApplicationContextHandle.getBean(OrderInterceptionService.class);
        Integer success = 0;
        for (int i = 0; i < ids.size(); i++) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", ids.getLong(i));
            OcBOrder ocBOrder = ocOrderMapper.selectById(ids.getLong(i));
            try {
                ValueHolder valueHolder = bean.cancelInterception(jsonObject, loginUser);

                if (ResultCode.SUCCESS == (Integer) valueHolder.get("code")) {
                    success++;
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "取消订单挂起成功", null, null, loginUser);
                } else {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "取消订单挂起失败" + valueHolder.get("message"), null, null, loginUser);
                }

            } catch (Exception e) {
                log.error(LogUtil.format("订单解挂异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "取消订单挂起失败" + e.getMessage(), null, null, loginUser);
            }
        }
        if (success == ids.size()) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "订单解挂成功" + success + "条数据");
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "订单解挂成功" + success + "条数据,失败" + (ids.size() - success) + "条数据");
        }
        return vh;
    }

    /**
     * 订单取消挂起
     *
     * @param object    传入的参数
     * @param loginUser 当前登录用户
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder cancelInterception(JSONObject object, User loginUser) throws NDSException {
        ValueHolder vh = new ValueHolder();
        Object id = object.get("id");
        // 1)若【是否已经挂起】=是，则显示【取消挂起】按钮，影响【订单挂起】按钮。
        OcBOrder ocBOrder = ocOrderMapper.selectByID(Long.valueOf(id.toString()));
        //给订单加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(Long.valueOf(id.toString()));
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                try {
                    if (ocBOrder != null) {
                        if (1 == ocBOrder.getIsInterecept()) {
                            // 3)但【订单状态】=“订单作废”或“已取消”，则提示是“当前订单已作废或已取消，不允许取消挂起！”
                            if (OmsOrderStatus.CANCELLED.toInteger().equals(ocBOrder.getOrderStatus())
                                    || OmsOrderStatus.SYS_VOID.toInteger().equals(ocBOrder.getOrderStatus())) {
                                vh.put("code", ResultCode.FAIL);
                                vh.put("message", Resources.getMessage("当前订单已作废或已取消，不允许取消挂起！"));
                            }
                  /*  4)更新全渠道订单主表数据
                    a)【是否已经挂起】：否
                    b)【修改人】：当前操作人
                    【修改时间】：当前操作时间*/
                            ocBOrder.setIsInterecept(0);//是否已经拦截 使用HOLD单方法修改
                            vh = ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder, OrderHoldReasonEnum.REFUND_HOLD);
                            /*ocBOrder.setModifierename(loginUser.getName());
                            ocBOrder.setModifieddate(new Date());
                            int i = ocOrderMapper.updateById(ocBOrder);
                            if (i == 0) {
                                throw new NDSException("更新主表异常！");
                            }
                            //推送主表更新信息到es
                            Boolean aBoolean = SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocBOrder, ocBOrder.getId());
                            if (!aBoolean) {
                                throw new NDSException(Resources.getMessage("取消订单挂起推送ES失败!", loginUser.getLocale()));
                            }
                           *//* //e)调用订单日志服务
                            //调用添加订单日志
                            try {
                                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "取消订单挂起成功", null, null, loginUser);
                            } catch (Exception e) {
                                vh.put("code", ResultCode.FAIL);
                                vh.put("message", Resources.getMessage("新增订单日志失败"));
                            }*//*
                            vh.put("code", ResultCode.SUCCESS);
                            vh.put("message", Resources.getMessage("取消挂起成功"));*/
                        } else {
                            // 2)若【是否已经挂起】=否，在提示“当前订单未挂起，不允许取消挂起！”
                            vh.put("code", ResultCode.FAIL);
                            vh.put("message", Resources.getMessage("当前订单未挂起，不允许取消挂起！"));
                        }
                    } else {
                        vh.put("code", ResultCode.FAIL);
                        vh.put("message", Resources.getMessage("当前订单不存在，请检查后重试！"));
                    }
                } catch (Exception e) {
                    vh.put("code", ResultCode.FAIL);
                    vh.put("message", Resources.getMessage("取消订单挂起失败", loginUser.getLocale()));
                }
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", loginUser.getLocale()));
            }
        } catch (Exception ex) {
            throw new NDSException(Resources.getMessage("订单更新锁单错误！", loginUser.getLocale()));
        } finally {
            redisLock.unlock();
        }
        return vh;
    }

    /**
     * 配送拦截服务
     *
     * @param ids  退单的id 集合
     * @param user 当前用户
     * @return 返回的成功失败信息
     */
    public ValueHolderV14 distributionInterception(JSONArray ids, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        // 1.判断拦截状态，若拦截状态为发起拦截成功 或 配送拦截成功，则返回提示：“配送拦截成功，不允许配送拦截”，
        OrderInterceptionService bean = ApplicationContextHandle.getBean(OrderInterceptionService.class);
        if (ids.size() == 1) {
            Long aLong = ids.getLong(0);
            try {
                vh = bean.distributionInterceptionStep(aLong, user);
                if (ResultCode.SUCCESS == vh.getCode()) {
                    refundService.insertReturnOrederLog("配送拦截", "配送拦截成功", null, user, aLong);
                } else {
                    refundService.insertReturnOrederLog("配送拦截", vh.getMessage(), null, user, aLong);
                }
            } catch (Exception e) {
                log.error(LogUtil.format("调用配送拦截服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                refundService.insertReturnOrederLog("配送拦截", e.getMessage(), null, user, aLong);
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(e.getMessage());
            }
            return vh;
        } else {
            Integer fail = 0;
            for (int i = 0; i < ids.size(); i++) {
                Long aLong = ids.getLong(i);
                try {
                    ValueHolderV14 vh14 = bean.distributionInterceptionStep(aLong, user);
                    if (ResultCode.SUCCESS == vh14.getCode()) {
                        refundService.insertReturnOrederLog("配送拦截", "配送拦截成功", null, user, aLong);
                    } else {
                        fail++;
                        refundService.insertReturnOrederLog("配送拦截", vh14.getMessage(), null, user, aLong);
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("调用配送拦截服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    refundService.insertReturnOrederLog("配送拦截", e.getMessage(), null, user, aLong);
                    fail++;
                }
            }
            if (fail == 0) {
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("配送拦截成功" + ids.size() + "条数据，失败0条数据");
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("配送拦截成功" + (ids.size() - fail) + "条数据，失败" + fail + "条数据");
            }
            return vh;
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 distributionInterceptionStep(Long aLong, User user) {
        // todo 413 后面要改到 订单上面进行配货拦截   （逻辑进行修改）
        ValueHolderV14 vh = new ValueHolderV14();
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(aLong);
        if (InterceptStatus.LAUNCH_INTERCEPT_SUCCESS.getCode().equals(ocBReturnOrder.getIntercerptStatus())) {
//                若拦截状态为未发起拦截 或 发起拦截失败，则调用配送拦截接口：taobao.qimen.order.callback
            QimenOrderCallbackModel needParam = packageParam(ocBReturnOrder, user);

            vh = ipRpcService.distributionInterception(needParam);
            if (ResultCode.SUCCESS == vh.getCode()) {
                updateInterStatus(ocBReturnOrder, user, InterceptStatus.LAUNCH_INTERCEPT_SUCCESS.getCode());
            } else {
                updateInterStatus(ocBReturnOrder, user, InterceptStatus.LAUNCH_INTERCEPT_FAIL.getCode());
            }
        } else {
            if ("0".equals(ocBReturnOrder.getIntercerptStatus().toString())) {
//                若拦截状态为未发起拦截 或 发起拦截失败，则调用配送拦截接口：taobao.qimen.order.callback
                QimenOrderCallbackModel needParam = packageParam(ocBReturnOrder, user);
                //  调用ip 中心的配送拦截服务
                vh = ipRpcService.distributionInterception(needParam);
                if (ResultCode.SUCCESS == vh.getCode()) {
                    updateInterStatus(ocBReturnOrder, user, 1);
                } else {
                    updateInterStatus(ocBReturnOrder, user, 0);
                }
            } else {
                // 1.判断拦截状态，若拦截状态为发起拦截成功 或 配送拦截成功，则返回提示：“配送拦截成功，不允许配送拦截”，
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("配送拦截成功，不允许配送拦截");
                return vh;
            }
        }

        return vh;
    }

    /**
     * 更新拦截状态方法
     *
     * @param ocBReturnOrder 退单主表
     * @param user           当前用户
     * @param status         拦截状态 1  是成功 0 是失败
     */
    private void updateInterStatus(OcBReturnOrder ocBReturnOrder, User user, Integer status) {
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        returnOrder.setIntercerptStatus(status);
        returnOrder.setModifiername(user.getName());
        returnOrder.setModifierename(user.getEname());
        returnOrder.setModifierid(user.getId() + 0L);
        ocBReturnOrderMapper.updateById(returnOrder);
    }

    private QimenOrderCallbackModel packageParam(OcBReturnOrder ocBReturnOrder, User user) {
        if (StringUtils.isBlank(ocBReturnOrder.getLogisticsCode())) {
            throw new NDSException("物流单号不能为空");
        }
        QimenOrderCallbackModel param = new QimenOrderCallbackModel();
        param.setOperateUser(user);
        param.setExpressCode(ocBReturnOrder.getLogisticsCode());  // 物流单号
        return param;
    }
}
