package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.ascp.InStorageFeedbackModel;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBAlibabaAscpOrderRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBAlibabaAscpOrderRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnBfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBReturnAfSendListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.RefundStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.AlibabaAscpOrderExt;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefund;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnBfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.RefundOrderSourceTypeEnum;
import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
import com.jackrain.nea.oc.oms.services.calculate.qty.OmsOrderQtyCalculateService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Descroption 猫超直发退货转换
 * <AUTHOR>
 * @Date 2020/09/05 20:47
 */
@Slf4j
@Component
public class IpBAlibabaAscpOrderRefundService {
    @Autowired
    protected CpRpcService cpRpcService;
    @Autowired
    private IpBAlibabaAscpOrderRefundMapper ipBAlibabaAscpOrderRefundMapper;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private OcBReturnBfSendMapper ocBReturnBfSendMapper;
    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
    @Autowired
    private Oms2SapMapper oms2SapMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private MarkRefundService markRefundService;
    @Autowired
    private OmsOrderRecountAmountService omsOrderRecountAmountService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private OmsRefundOrderService refundOrderService;
    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;
    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;
    @Autowired
    private IpBAlibabaAscpOrderRefundItemMapper orderRefundItemMapper;
    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;
    @Autowired
    private IpRpcService ipRpcService;

    /**
     * @param bizOrderCode
     * @return
     * @Descroption 获取猫超直发退换货关系数据对象
     * @Author: 洪艺安
     * @Date 2019/4/25
     */
    public IpBAlibabaAscpOrderRefundRelation getAlibabaAscpRefundRelation(String bizOrderCode) {
        IpBAlibabaAscpOrderRefundRelation refundRelation = null;
        String forwardOrderCode = null;
        try {
            IpBAlibabaAscpOrderRefund orderRefund = ipBAlibabaAscpOrderRefundMapper.selectAlibabaAscpOrderRefundByBizOrderCode(bizOrderCode);
            if (null == orderRefund) {
                return null;
            }
            refundRelation = new IpBAlibabaAscpOrderRefundRelation();
            refundRelation.setOrderRefund(orderRefund);
            long orderRefundId = orderRefund.getId();
            forwardOrderCode = orderRefund.getForwardOrderCode();
            List<IpBAlibabaAscpOrderRefundItem> orderRefundItems = orderRefundItemMapper.selectAlibabaOrderRefundItemList(orderRefundId);
            refundRelation.setIpBAlibabaAscpOrderRefundItems(orderRefundItems);

//        Set<String> subOrderIds = orderRefundItems.stream().filter(refundItem -> refundItem.getSubOrderCode() != null).
//                map(IpBAlibabaAscpOrderRefundItem::getSubOrderCode).collect(Collectors.toSet());
//        String ooIds = String.join(",", subOrderIds);
//        log.debug(subOrderIds.toString());

            Set<Long> ids = ES4Order.findIdsByTid(orderRefund.getForwardOrderCode());

            if (CollectionUtils.isEmpty(ids)) {
                return refundRelation;
            }

            List<Long> idList = new ArrayList<>(ids);

            //此处订单已经去重
            List<OmsOrderRelation> omsOrderRelations = new ArrayList<>();
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(idList);
            for (OcBOrder ocBOrder : ocBOrders) {
                Long orderId = ocBOrder.getId();
                OmsOrderRelation omsOrderRelation = new OmsOrderRelation();
                String tid = "";
                if (StringUtils.isNotEmpty(orderRefund.getForwardOrderCode())) {
                    tid = orderRefund.getForwardOrderCode();
                }
                //根据订单查询需要退货的明细
                QueryWrapper refundWrapper = new QueryWrapper<>()
                        .eq("isactive", "Y")
                        .eq("oc_b_order_id", orderId)
                        .eq("tid", tid);
                List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectList(refundWrapper);

                QueryWrapper giftWrapper = new QueryWrapper<>()
                        .eq("is_gift", 1)
                        .eq("isactive", "Y")
                        // 不等于
                        .ne("pro_type", "4")
                        .eq("oc_b_order_id", orderId)
                        .eq("tid", tid);
                List<OcBOrderItem> orderItemsGift = ocBOrderItemMapper.selectList(giftWrapper);
                if (log.isDebugEnabled()) {
                    log.debug("{} selected order No.{} gift goods on generate return order relation DO process. gift goods list:{}", this.getClass().getName(), orderId, JSON.toJSONString(orderItemsGift));
                }
                List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(orderId);
                omsOrderRelation.setOrderDeliveries(ocBOrderDeliveries);
                omsOrderRelation.setOcBOrder(ocBOrder);
                omsOrderRelation.setOcBOrderItems(ocBOrderItems);
                //循环明细数据 todo 暂时不考虑换货
                if (CollectionUtils.isNotEmpty(orderItemsGift)) {
                    Map<String, List<OcBOrderItem>> stringListMap = this.giftHandle(orderItemsGift);
                    List<OmsOrderRelation.OcOrderGifts> gifts = this.getOcOrderGifts(ocBOrderItems, stringListMap);
                    omsOrderRelation.setOcOrderGifts(gifts);
                    log.info("{} filter order NO.{} not return gift goods, mapping gift goods list:{}.", this.getClass().getName(), orderId, JSON.toJSONString(gifts));
                }
                omsOrderRelations.add(omsOrderRelation);
            }
            refundRelation.setOmsOrderRelation(omsOrderRelations);
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + "  通用退单查询数据:{}", JSONObject.toJSONString(refundRelation));
            }
        } catch (Exception e) {
            log.error("猫超直发退换货error:bizOrderCode:{},forwardOrderCode:{},error:{}", bizOrderCode, forwardOrderCode, e.getMessage());
            System.out.println(String.format("猫超直发退换货error:bizOrderCode:%s,forwardOrderCode:%s,error:%s", bizOrderCode, forwardOrderCode, e.getMessage()));
        }
        return refundRelation;
    }


    /**
     * 对赠品及下挂赠品的处理
     *
     * @param orderItemsGift
     * @return
     */
    private Map<String, List<OcBOrderItem>> giftHandle(List<OcBOrderItem> orderItemsGift) {
        Map<String, List<OcBOrderItem>> giftList = new HashMap<>(10);
        for (OcBOrderItem orderItemGift : orderItemsGift) {
            //判断明细是否有挂靠赠品及 订单下是否有赠品
            String giftRelation = orderItemGift.getGiftRelation();
            if (StringUtils.isEmpty(giftRelation)) {
                //不为下挂商品
                giftRelation = "1";
            }
            if (!giftList.containsKey(giftRelation)) {
                List<OcBOrderItem> list = new ArrayList<>();
                list.add(orderItemGift);
                giftList.put(giftRelation, list);
            } else {
                List<OcBOrderItem> list = giftList.get(giftRelation);
                list.add(orderItemGift);
                giftList.put(giftRelation, list);
            }
        }
        return giftList;
    }

    /**
     * 处理正常赠品 及 挂靠赠品
     *
     * @param ocBOrderItems
     * @param stringListMap
     * @return
     */
    private List<OmsOrderRelation.OcOrderGifts> getOcOrderGifts(List<OcBOrderItem> ocBOrderItems, Map<String, List<OcBOrderItem>> stringListMap) {
        boolean flag = true;
        List<OmsOrderRelation.OcOrderGifts> gifts = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            if (ocBOrderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                continue;
            }
            OmsOrderRelation.OcOrderGifts ocOrderGifts = new OmsOrderRelation.OcOrderGifts();
            String giftRelation = ocBOrderItem.getGiftRelation();
            if (StringUtils.isEmpty(giftRelation) && flag) {
                List<OcBOrderItem> orderItems = stringListMap.get("1");
                if (CollectionUtils.isNotEmpty(orderItems)) {
                    ocOrderGifts.setOcBOrderGifts(orderItems);
                    //不是挂靠赠品
                    ocOrderGifts.setGiftMark(1);
                    gifts.add(ocOrderGifts);
                    flag = false;
                }
            } else {
                if (StringUtils.isNotEmpty(giftRelation)) {
                    List<OcBOrderItem> orderItems = stringListMap.get(giftRelation);
                    if (CollectionUtils.isNotEmpty(orderItems)) {
                        ocOrderGifts.setOcBOrderGifts(orderItems);
                        //挂靠赠品
                        ocOrderGifts.setGiftMark(2);
                        gifts.add(ocOrderGifts);
                    }
                }
            }
        }
        return gifts;
    }

    public List<OcBOrderItem> getOcBOrderItems(String orderId) {
        //生成所有订单明细List
        List<OcBOrderItem> orderItemAll = new ArrayList<>();

        Set<Long> ids = ES4Order.findIdsByTid(orderId);

        if (CollectionUtils.isEmpty(ids)) {
            return orderItemAll;
        }

        for (Long id : ids) {
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemList(id);
            orderItemAll.addAll(orderItems);
        }
        return orderItemAll;
    }

    /**
     * @param orderRefund
     * @return boolean
     * @Descroption 异常修改退单中间表备注及推送ES并且记录日志
     * @Author: 洪艺安
     * @Date 2019/4/26
     */
    public boolean updateRefundIsTransError(IpBAlibabaAscpOrderRefund orderRefund, String errorMessage) {
        String sysRemark = SysNotesConstant.SYS_REMARK0;
        //异常信息超过500 截取500
        String str = sysRemark + errorMessage;
        if (str.length() > 500) {
            str = str.substring(0, 500);
        }
        boolean flag = this.updateAlibabaAscpOrderRefundAndTransCount(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                str, orderRefund);
        return flag;
    }

    /**
     * 更新猫超直发退单订单转单状态
     *
     * @param istrans     状态
     * @param remark      备注
     * @param orderRefund
     */
    private boolean updateAlibabaAscpOrderRefundAndTransCount(int istrans, String remark, IpBAlibabaAscpOrderRefund orderRefund) {
        String bizOrderCode = null;
        try {
            bizOrderCode = orderRefund.getBizOrderCode();
            JSONObject object = new JSONObject();
            object.put("biz_order_code", bizOrderCode);
            object.put("istrans", istrans);
            if (StringUtils.isNotBlank(remark) && remark.length() > 200) {
                remark = remark.substring(0, 200);
            }
            object.put("sysremark", remark);
            object.put("modifieddate", new Date());
            object.put("transdate", new Date());
            int i = ipBAlibabaAscpOrderRefundMapper.updateAlibabaAscpOrderRefundAndTransCount(object);
            //推送es
            return i > 0;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 履约单号=" + bizOrderCode + "，猫超直发退单订单更新转单失败,异常:{}", e.getMessage());
            throw new NDSException(e);
        }
    }

    /**
     * 更新猫超直发退单订单转单状态
     *
     * @param istrans     状态
     * @param remark      备注
     * @param orderRefund
     */
    public boolean updateAlibabaAscpOrderRefund(int istrans, String remark, IpBAlibabaAscpOrderRefund orderRefund) {
        String bizOrderCode = null;
        try {
            bizOrderCode = orderRefund.getBizOrderCode();
            JSONObject object = new JSONObject();
            object.put("biz_order_code", bizOrderCode);
            object.put("istrans", istrans);
            if (StringUtils.isNotBlank(remark) && remark.length() > 200) {
                remark = remark.substring(0, 200);
            }
            object.put("sysremark", remark);
            object.put("modifieddate", new Date());
            object.put("transdate", new Date());
            int i = ipBAlibabaAscpOrderRefundMapper.updateAlibabaAscpOrderRefund(object);
            //推送es
            return i > 0;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 履约单号=" + bizOrderCode + "，猫超直发退单订单更新转单失败,异常:{}", e.getMessage());
            throw new NDSException(e);
        }
    }

    /**
     * 判断仓库控制是否需要发送WMS
     *
     * @param warehouseId
     * @return
     */
    private boolean isNeedToWmsByWarehouseId(Long warehouseId) {
        boolean isNeed = false;

        if (Objects.nonNull(warehouseId)) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(warehouseId);
            isNeed = Objects.nonNull(cpCPhyWarehouse) && Objects.nonNull(cpCPhyWarehouse.getWmsControlWarehouse()) && cpCPhyWarehouse.getWmsControlWarehouse() == 1;
        }

        return isNeed;
    }

    /**
     * @param id
     * @return com.jackrain.nea.cpext.model.table.CpShop
     * @Descroption 根据店铺id查询店铺信息
     * @Author: 洪艺安
     * @Date 2019/5/23
     */
    public CpShop queryShopById(Long id) {
        CpShop cpShop = null;
        try {
            cpShop = cpRpcService.selectShopById(id);
        } catch (Exception e) {
            String errMsg = "调用根据店铺id查询店铺信息RPC接口失败";
            log.error(errMsg);
            throw new NDSException(errMsg);
        }
        return cpShop;
    }

    /**
     * 更新物流信息
     *
     * @param logisticCode 物流编码
     * @param returnId     退单编号
     * @param user         更新用户
     */
    public void updateRefundLogicNumber(String logisticCode, String returnId, User user, String storeCode) {
        if (log.isDebugEnabled()) {
            log.debug("updateRefundLogicNumber Params logisticCode:{},returnId:{}", logisticCode, returnId);
        }
        OcBReturnOrder ocBReturnOrder = this.selectReturnOrderByReturnId(returnId);
        if (ocBReturnOrder != null) {
            // 假如中间表有物流单号 && 假如退货单有物流信息并且不去更新
            if (StringUtils.isBlank(logisticCode)
                    || (StringUtils.isNotBlank(ocBReturnOrder.getLogisticsCode()) && Objects.equals(ocBReturnOrder.getLogisticsCode(), logisticCode))) {
                return;
            }
            List<CpCStore> cpStoreList = cpRpcService.queryStoreByTmallStoreCode(storeCode);

            if (CollectionUtils.isNotEmpty(cpStoreList)) {
                CpCStore cpCStore = cpStoreList.get(0);
                ocBReturnOrder.setCpCPhyWarehouseInId(cpCStore.getCpCPhyWarehouseId());
            } else {
                throw new NDSException("仓库编码" + storeCode + "找不到逻辑仓");
            }
            ocBReturnOrder.setLogisticsCode(logisticCode);
            ocBReturnOrder.setModifieddate(new Date());
            ocBReturnOrder.setModifierid(Long.valueOf(user.getId()));
            ocBReturnOrder.setModifieddate(new Date());
            ocBReturnOrder.setModifiername(user.getName());
            log.info(" ============= update logisticInfo success ============= ");
            ocBReturnOrderMapper.update(ocBReturnOrder, new LambdaQueryWrapper<OcBReturnOrder>().eq(OcBReturnOrder::getId, ocBReturnOrder.getId()));
        }
    }

    /**
     * 根据refundId去获取退换货订单
     *
     * @return 退换货订单
     */
    private OcBReturnOrder selectReturnOrderByReturnId(String refundId) {
        Set<Long> ids = ES4ReturnOrder.findIdByReturnId(refundId);
        if (CollectionUtils.isNotEmpty(ids)) {
            LambdaQueryWrapper<OcBReturnOrder> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(OcBReturnOrder::getId, ids);
            return ocBReturnOrderMapper.selectList(wrapper).stream().findFirst().orElse(null);
        }
        return null;
    }

    /**
     * 根据平台单号校验 退货单  发货前/后退款单 是否存在
     *
     * @param tid
     */
    public boolean isExistReturnRelevantInfo(String tid) {
        if (StringUtils.isBlank(tid)) {
            return false;
        }

        List<OcBReturnBfSend> ocBReturnBfSends = ocBReturnBfSendMapper.selectList(new LambdaQueryWrapper<OcBReturnBfSend>().
                eq(OcBReturnBfSend::getTid, tid));
        if (!ocBReturnBfSends.isEmpty()) {
            return true;
        }

        List<OcBReturnAfSend> ocBReturnAfSends = ocBReturnAfSendMapper.selectList(new LambdaQueryWrapper<OcBReturnAfSend>().
                eq(OcBReturnAfSend::getTid, tid));
        if (!ocBReturnAfSends.isEmpty()) {
            return true;
        }
        List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectList(new LambdaQueryWrapper<OcBReturnOrder>()
                .eq(OcBReturnOrder::getTid, tid));
        return !ocBReturnOrders.isEmpty();
    }

    /**
     * 更新仅退款单状态
     *
     * @param refundId 退款单单号
     * @param status   状态
     * @return 是否更新
     */
    public boolean updateRefundSlip(String refundId, String status) {
        boolean hasSendOrder = false;
        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(refundId);
        if (ocBReturnAfSend != null) {
            if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
                ocBReturnAfSendMapper.updateOcBReturnAfSendRefundStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal(), status, refundId);
                oms2SapMapper.insertSingleTaskOrder("oc_b_task_refund_sap", ocBReturnAfSend.getId(), ModelUtil.getSequence("oc_b_task_refund_sap"));
            } else {
                ocBReturnAfSendMapper.updateOcBReturnAfSend(status, refundId);
            }
            log.info("{} update a only refund order to {} status, refund NO.{}", this.getClass().getName(), status, refundId);
            hasSendOrder = true;
        }
        // 是否存在发货前退款单
        if (isRefundSlipBfExist(refundId)) {
            ocBReturnBfSendMapper.updateOcBReturnBfSend(status, refundId);
            log.info("{} update a delivering refund order to {} status, refund NO.{}", this.getClass().getName(), status, refundId);
            hasSendOrder = true;
        }
        return hasSendOrder;
    }

    /**
     * 查询发货前退款单是否存在
     *
     * @param refundId
     * @return
     */
    private boolean isRefundSlipBfExist(String refundId) {
        List<OcBReturnBfSend> ocBReturnBfSend = ocBReturnBfSendMapper.selectOcBReturnBfSendsByRefundId(refundId);
        return !ocBReturnBfSend.isEmpty();
    }

    /**
     * 查询发货前退款单是否存在
     *
     * @param refundId
     * @return
     */
    private OcBReturnBfSend getOcBReturnBfSend(String refundId) {
        List<OcBReturnBfSend> ocBReturnBfSend = ocBReturnBfSendMapper.selectOcBReturnBfSendsByRefundId(refundId);
        if (CollectionUtils.isNotEmpty(ocBReturnBfSend)) {
            return ocBReturnBfSend.get(0);
        }
        return null;
    }

    public boolean updateReturnOrderItem(String returnId, List<String> oid, String status) {
        boolean hasReturnOrderItem = false;
        List<Long> existReturnOrderByOid = this.selectReturnOrderByOidOrReturnId(returnId, oid);
        log.info("===============existReturnOrderByOid:{},returnId:{},status:{}",
                JSONObject.toJSONString(existReturnOrderByOid), returnId, status);
        if (CollectionUtils.isNotEmpty(existReturnOrderByOid)) {
            log.info("=======进入existReturnOrderByOid is not null");
            ocBReturnOrderRefundMapper.updateReturnOrderRefund(existReturnOrderByOid, status, returnId);
            hasReturnOrderItem = true;
        }
        return hasReturnOrderItem;
    }

    /**
     * 先根据退款单号查  如果未查询到则用(oid)子订单号查询
     *
     * @return 退货单ID列表
     */
    private List<Long> selectReturnOrderByOidOrReturnId(String refundId, List<String> oid) {
        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByRefundId(refundId);

        if (CollectionUtils.isEmpty(ids)) {
            if (CollectionUtils.isNotEmpty(oid)) {
                ids = ES4ReturnOrder.findReturnOrderIdByOid(StringUtils.join(oid, ","));
            }
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            return new ArrayList<>(ids);
        } else {
            // @20200720 增加查reids逻辑
            Long returnOrderId = OmsReturnOrderService.selectOmsReturnOrderFromRedisByReturnId(refundId);
            // redis返回的默认是0
            if (Objects.nonNull(returnOrderId) && returnOrderId.longValue() > 0) {
                List<Long> rids = new ArrayList<>();
                rids.add(returnOrderId);
                return rids;
            }
        }
        return null;
    }

    /**
     * 根据订单明细退款状态
     *
     * @param orderItems
     * @return
     */
    private Integer judgeReturnStatus(List<OcBOrderItem> orderItems) {
        int noRefund = 0;
        int partRefund = 0;
        for (OcBOrderItem orderItem : orderItems) {
            String ptReturnStatus = orderItem.getPtReturnStatus();
            if (StringUtils.isEmpty(ptReturnStatus)
                    || TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(ptReturnStatus)) {
                noRefund++;
                continue;
            }
            boolean flag = TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(ptReturnStatus)
                    || TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode().equals(ptReturnStatus)
                    || TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode().equals(ptReturnStatus)
                    || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(ptReturnStatus)
                    || TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(ptReturnStatus);
            if (flag) {
                partRefund++;
            }
        }
        if (orderItems.size() == noRefund) {
            return RefundStatusEnum.NO_PAY.getKey();
        } else if (partRefund == orderItems.size()) {
            return RefundStatusEnum.ALL_PAY.getKey();
        } else {
            return RefundStatusEnum.PART_PAY.getKey();
        }
    }

    /**
     * 更新 中间表主表状态 和 备注
     *
     * @param toInteger   装换状态
     * @param sysRemark22 备注
     * @param orderRefund 通用退单中间表
     */
    public void updateReturnOrder(int toInteger, String sysRemark22, IpBAlibabaAscpOrderRefund orderRefund) {
        IpBAlibabaAscpOrderRefund refund = new IpBAlibabaAscpOrderRefund();
        refund.setSysremark(sysRemark22);
        refund.setIstrans(toInteger);
        refund.setId(orderRefund.getId());
        refund.setModifieddate(new Date());
        if (StringUtils.isNotBlank(sysRemark22) && sysRemark22.length() > 200) {
            sysRemark22 = sysRemark22.substring(0, 200);
        }
        orderRefund.setSysremark(sysRemark22);
        orderRefund.setIstrans(toInteger);
        LambdaQueryWrapper<IpBAlibabaAscpOrderRefund> wrapper = new LambdaQueryWrapper<IpBAlibabaAscpOrderRefund>();
        List<Integer> status = new ArrayList<>();
//        status.add(TransferOrderStatus.NOT_TRANSFER.toInteger());
//        status.add(TransferOrderStatus.TRANSFERRING.toInteger());
//        wrapper.in(IpBAlibabaAscpOrderRefund::getIstrans, status.toArray());
        wrapper.eq(IpBAlibabaAscpOrderRefund::getBizOrderCode, orderRefund.getBizOrderCode());
        ipBAlibabaAscpOrderRefundMapper.update(refund, wrapper);
        //跟新退单中间表的es
        try {
            Boolean aBoolean = SpecialElasticSearchUtil.indexDocument(AlibabaAscpOrderExt.TABLE_NAME_IP_B_ALIBABA_ASCP_ORDER_REFUND, AlibabaAscpOrderExt.TABLE_NAME_IP_B_ALIBABA_ASCP_ORDER_REFUND, orderRefund, orderRefund.getId());
            if (!aBoolean) {
                throw new NDSException("推送退货z中间表主表到es 失败");
            }
        } catch (IOException e) {
            log.debug(this.getClass().getName() + "推送退货z中间表主表到es 失败" + e.getMessage());
            throw new NDSException("推送退货z中间表主表到es 失败" + e.getMessage());
        }
    }

    /**
     * 生成发货前退款单(仅退款)
     */
    public void foundRefundFrontRefundOnly(OcBOrder ocBOrder, IpBAlibabaAscpOrderRefund orderRefund, User user) {

        OcBReturnBfSend ocBReturnBfSend = new OcBReturnBfSend();
        ocBReturnBfSend.setId(ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCBRETURNBFSEND));
        ocBReturnBfSend.setCpCShopId(ocBOrder.getCpCShopId());
        ocBReturnBfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        ocBReturnBfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        ocBReturnBfSend.setTid(orderRefund.getBizOrderCode());
//        ocBReturnBfSend.setSubBillNo(orderRefund.getSubOrderId());
        ocBReturnBfSend.setTReturnId(orderRefund.getBizOrderCode());
//        ocBReturnBfSend.setBuyerNick(orderRefund.getBuyerNick());
//        ocBReturnBfSend.setAmtReturn(orderRefund.getRefundAmount());
//        ocBReturnBfSend.setReason(orderRefund.getReturnReason());
        ocBReturnBfSend.setReturnApplyTime(orderRefund.getCreated());
//        Integer returnStatus = orderRefund.getReturnStatus();
//        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
//        ocBReturnBfSend.setTReturnStatus(status);
        ocBReturnBfSend.setReturnApplyTime(orderRefund.getCreated());
        //退款说明
        //ocBReturnBfSend.setReturnExplain(orderRefund.get);
        OcBReturnBfSend oldReturnBfSend = getOcBReturnBfSend(orderRefund.getBizOrderCode());
        if (oldReturnBfSend != null && oldReturnBfSend.getCreationdate().getTime() < orderRefund.getCreated().getTime()) {
            LambdaQueryWrapper<OcBReturnBfSend> returnSendWarpper = new LambdaQueryWrapper<OcBReturnBfSend>();
            returnSendWarpper.eq(OcBReturnBfSend::getId, oldReturnBfSend.getId());
            ocBReturnBfSend.setId(null);
            ocBReturnBfSendMapper.update(ocBReturnBfSend, returnSendWarpper);
        } else {
            OperateUserUtils.saveOperator(ocBReturnBfSend, user);
            ocBReturnBfSendMapper.insert(ocBReturnBfSend);
        }
    }

    /**
     * 买家申请退款,等待卖家同意
     *
     * @param omsOrderRelation 订单对象
     * @param operateUser      操作人
     * @return
     */
    public boolean taobaoRefundStatusAgree(OmsOrderRelation omsOrderRelation,
                                           IpBAlibabaAscpOrderRefund orderRefund, User operateUser) {
        try {
            List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
            //判断明细是否全部退款成功(一个事务要么全部成功)
            boolean flag = true;
            for (int i = 0; i < ocBOrderItems.size(); i++) {
                OcBOrderItem item = ocBOrderItems.get(i);
                if (OcOrderRefundStatusEnum.SUCCESS.getVal() != item.getRefundStatus()) {
                    flag = false;
                }
            }
            if (flag) {
                return true;
            }
            //将订单明细的退款状态更新为 1
            OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
            //未拆分的组合商品信息
            List<OcBOrderItem> items = ocBOrderItems.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
            List<OcBOrderItem> itemList = ocBOrderItems.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
            List<Long> updateItemIds = ocBOrderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            ocBOrderItemMapper.updateOcBOrderItemById(ocBOrder.getId(), updateItemIds, OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal());
            boolean handle = this.orderHandle(orderRefund, operateUser, ocBOrder);
            if (!handle) {
                return false;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 买家申请退款,等待卖家同意处理异常", e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * 订单状态处理
     *
     * @param operateUser
     * @param ocBOrder
     */
    private boolean orderHandle(IpBAlibabaAscpOrderRefund orderRefund, User operateUser, OcBOrder ocBOrder) {
        Integer orderStatus = ocBOrder.getOrderStatus();
        if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus)) {
            String remark1 = "订单状态为待分配或者待传wms,等待下次转换";
            this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                    remark1, orderRefund);
            return false;
        } else if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)
                || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
            //订单状态为已审核或者配货中 调用反审核
            if (this.toExamineOrder(ocBOrder, operateUser)) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核成功", null, null, operateUser);
            } else {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核失败", null, null, operateUser);
                String remark1 = SysNotesConstant.SYS_REMARK46;
                this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        remark1, orderRefund);
                return false;
            }
        }
        return true;
    }

    private void orderHandle(IpBAlibabaAscpOrderRefund orderRefund, OcBOrder ocBOrder) {
        //判断订单是否全部退款完成
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        if (CollectionUtils.isEmpty(orderItems)) {
            this.cancelOrder(ocBOrder.getId());
            String remark = SysNotesConstant.SYS_REMARK19;
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, orderRefund);
            return;
        }
        //判断订单时候还有申请退款的明细
        orderItems = orderItems.stream().filter(p -> (
                        p.getRefundStatus().equals(OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal())
                )
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderItems)) {
            // this.cancelOrder(ocBOrder.getId());
            String remark = SysNotesConstant.SYS_REMARK19;
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, orderRefund);
        } else {
            OcBOrder order = new OcBOrder();
            order.setIsInreturning(0);
            order.setId(ocBOrder.getId());
            omsOrderService.updateOrderInfo(order);
            //是否已经拦截 hold单或释放hold单调用 HOLD单方法
            order.setIsInterecept(0);//修改hold单状态 使用HOLD单方法修改
            ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
            String remark = SysNotesConstant.SYS_REMARK20;
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, orderRefund);
        }
    }

    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    private boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            if (isSuccess) {
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用反审核失败", e);
            return false;
        }

    }

    /**
     * 退款成功
     *
     * @param omsOrderRelation 订单对象
     * @param operateUser      用户
     * @return
     */
    public void refundStatusIsSuccess(OmsOrderRelation omsOrderRelation,
                                      IpBAlibabaAscpOrderRefund orderRefund, User operateUser) {
        try {
            List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
            OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
            //判断是否已退款完成
            boolean flag = true;
            for (int i = 0; i < ocBOrderItems.size(); i++) {
                OcBOrderItem item = ocBOrderItems.get(i);
                if (OcOrderRefundStatusEnum.SUCCESS.getVal() == item.getRefundStatus()) {
                    ocBOrderItems.remove(item);
                    i--;
                } else {
                    flag = false;
                }
            }
            if (flag) {
                orderHandle(orderRefund, ocBOrder);
                String remark = SysNotesConstant.SYS_REMARK19;
                this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, orderRefund);
                return;
            }
            boolean handle = this.orderHandle(orderRefund, operateUser, ocBOrder);
            if (!handle) {
                return;
            }
            Integer orderStatus = ocBOrder.getOrderStatus();
            //未拆分的组合商品信息
            List<OcBOrderItem> items = ocBOrderItems.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
            List<OcBOrderItem> itemList = ocBOrderItems.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
            List<Long> itemIds = itemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            //若订单状态是：待审核、缺货、已审核状态时，直接调用【订单传AG取消发货服务】
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                if (signRefund(itemIds, operateUser)) {
                    //标记退款完成之后将 更新订单补充信息 区分此订单有退款作废
                    OcBOrder orderInfo = ocBOrderMapper.selectById(ocBOrder.getId());
                    if (OmsOrderStatus.SYS_VOID.toInteger().equals(orderInfo.getOrderStatus())) {
                        OcBOrder bOrder = new OcBOrder();
                        bOrder.setId(orderInfo.getId());
                        bOrder.setSuffixInfo("REFUND-VOID");
                        omsOrderService.updateOrderInfo(bOrder);

                    }
                    if (CollectionUtils.isNotEmpty(items)) {
                        for (OcBOrderItem orderItem : items) {
                            OcBOrderItem item = new OcBOrderItem();
                            item.setId(orderItem.getId());
                            item.setOcBOrderId(orderItem.getOcBOrderId());
                            item.setRefundStatus(OcOrderRefundStatusEnum.SUCCESS.getVal());
                            item.setPrice(BigDecimal.ZERO);
                            item.setOrderSplitAmt(BigDecimal.ZERO);
                            item.setAmtDiscount(BigDecimal.ZERO);
                            item.setAdjustAmt(BigDecimal.ZERO);
                            // 记录实际成交价金额
                            item.setReserveDecimal05(orderItem.getRealAmt());
                            item.setRealAmt(BigDecimal.ZERO);
                            omsOrderItemService.updateOcBOrderItem(item, orderItem.getOcBOrderId());
                        }
                    }
                    orderHandle(orderRefund, ocBOrder);
                    List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectUnSuccessRefundAndNoSplit(orderInfo.getId());
                    List<OcBOrderItem> orderItems = orderItemList.stream().filter(p -> p.getProType() != SkuType.COMBINE_PRODUCT && p.getProType() != SkuType.GIFT_PRODUCT).collect(Collectors.toList());
                    OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                    ocBOrderRelation.setOrderInfo(orderInfo);
                    List<OcBOrderItem> list = new ArrayList<>();
                    omsOrderRecountAmountService.doRecountAmount(ocBOrderRelation, list, orderItems);
                }
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 退款成功处理异常", e);
            throw new NDSException(e);
        }
    }

    /**
     * 调用标记退款完成
     *
     * @param itemIds
     * @param user
     * @return
     */
    private boolean signRefund(List<Long> itemIds, User user) {
        List<String> itemIdstr = itemIds.stream().map(p -> p + "").collect(Collectors.toList());
        String join = String.join(",", itemIdstr);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("IDS", join);
        ValueHolderV14 holderV14 = markRefundService.markRefund(jsonObject, user);
        int code = Tools.getInt(holderV14.getCode(), -1);
        return code == 0;
    }

    /**
     * 发货前全部退款完成之后,取消原单
     *
     * @param id
     */
    void cancelOrder(Long id) {
        OcBOrder order = new OcBOrder();
        order.setId(id);
        // 全部退款完成之后取消原单
        order.setOrderStatus(OmsOrderStatus.CANCELLED.toInteger());
        order.setModifieddate(new Date());
        omsOrderService.updateOrderInfo(order);
    }

    /**
     * @param omsOrderRelation 订单对象
     * @param operateUser
     * @return
     */
    public void orderStatusIsClosed(OmsOrderRelation omsOrderRelation,
                                    IpBAlibabaAscpOrderRefund orderRefund, User operateUser) {
        OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
        List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
        //将订单明细的对象的退款状态改为未退款
        for (OcBOrderItem item : ocBOrderItems) {
            OcBOrderItem orderItem = new OcBOrderItem();
            orderItem.setId(item.getId());
            orderItem.setOcBOrderId(item.getOcBOrderId());
            orderItem.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
            omsOrderItemService.updateOcBOrderItem(orderItem, ocBOrder.getId());
        }
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListNotGift(ocBOrder.getId());
        orderItems = orderItems.stream().filter(p -> (
                        p.getRefundStatus().equals(OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal())
                )
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderItems)) {
            String remark = SysNotesConstant.SYS_REMARK18;
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, orderRefund);
            return;
        } else {
            OcBOrder order = new OcBOrder();
            order.setIsInreturning(0);
            order.setId(ocBOrder.getId());
            omsOrderService.updateOrderInfo(order);
            //是否已经拦截 hold单或释放hold单调用 HOLD单方法
            order.setIsInterecept(0);//修改hold单状态 使用HOLD单方法修改
            ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    "退款关闭,转换完成!", orderRefund);
        }
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "订单退款关闭，取消拦截", null, null, operateUser);
    }

    /**
     * 生成发货后退款单(仅退款)
     *
     * @param ocBOrderItems
     * @param ocBOrder
     * @param orderRefund
     */
    public void foundRefundSlipAfterRefundOnly(List<OcBOrderItem> ocBOrderItems, OcBOrder ocBOrder,
                                               IpBAlibabaAscpOrderRefund orderRefund, User user) {
        //获取所有的退单明细数据
        try {
            if (!isRefundSlipAfExist(orderRefund.getBizOrderCode())) {
                return;
            }
            OcBReturnAfSendRelation ocBReturnAfSendRelation =
                    this.standplatRefundAfSendToRefundOnly(ocBOrderItems, ocBOrder, orderRefund, user);
            //获取发货单主表数据
            omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation,user);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 生成退款单异常", e);
        }


    }

    /**
     * 处理发货后退款单的数据(仅退款)
     *
     * @param ocBOrderItems
     * @param ocBOrder
     * @param orderRefund
     * @return
     */
    public OcBReturnAfSendRelation standplatRefundAfSendToRefundOnly(List<OcBOrderItem> ocBOrderItems,
                                                                     OcBOrder ocBOrder,
                                                                     IpBAlibabaAscpOrderRefund orderRefund,
                                                                     User user) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, orderRefund,
                TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode(), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItem(ocBOrderItems, orderRefund, user, ocBOrder);
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }


    /**
     * 查询发货后退款单是否存在
     *
     * @param refundId
     * @return
     */
    public boolean isRefundSlipAfExist(String refundId) {
        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(refundId);
        return ocBReturnAfSend == null;
    }

    /**
     * 生成发货后退款单
     *
     * @return
     */
    private OcBReturnAfSend buildOcBReturnAfSend(OcBOrder ocBOrder,
                                                 IpBAlibabaAscpOrderRefund orderRefund,
                                                 Integer billType, User user) {
        OcBReturnAfSend ocBReturnAfSend = new OcBReturnAfSend();
        ocBReturnAfSend.setRefundOrderSourceType(2);
        ocBReturnAfSend.setCpCShopId(ocBOrder.getCpCShopId());
        ocBReturnAfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        ocBReturnAfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        ocBReturnAfSend.setTid(ocBOrder.getTid());
        ocBReturnAfSend.setBillNo(sequenceUtil.aFbuildBillNo());
        //退款状态 0 待审核
        ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal());
        ocBReturnAfSend.setTReturnId(orderRefund.getBizOrderCode());
        //单据类型 0 退货退款 1仅退款',
        ocBReturnAfSend.setBillType(billType);
//        ocBReturnAfSend.setVipNick(orderRefund.getBuyerNick());
//        ocBReturnAfSend.setReason(orderRefund.getReturnReason());
        //支付方式
        ocBReturnAfSend.setPayMode(ocBOrder.getPayType() + "");
        //支付宝账号
        ocBReturnAfSend.setPayAccount(ocBOrder.getBuyerAlipayNo());
        //申请退款金额
//        ocBReturnAfSend.setAmtReturnApply(orderRefund.getRefundAmount());

        // @******** 赋值单据来源为自动
        ocBReturnAfSend.setRefundOrderSourceType(RefundOrderSourceTypeEnum.AUTO.getValue());

        ocBReturnAfSend.setSourceBillNo(ocBOrder.getId() + "");
        ocBReturnAfSend.setCpCPlatformId(Long.valueOf(ocBOrder.getPlatform()));
        ocBReturnAfSend.setPayMode(OcBReturnAfSendListEnums.PayTypeEnum.Alipay.getVal());
        //todo 实际退款金额
//        ocBReturnAfSend.setAmtReturnActual(orderRefund.getRefundAmount());
        //申请退款时间
        ocBReturnAfSend.setReturnApplyTime(new Date());
        ocBReturnAfSend.setAgStatus(AGStatusEnum.INIT.getVal() + "");
//        ocBReturnAfSend.setReturnExplain(orderRefund.getReturnReason());
        //收货人姓名
        ocBReturnAfSend.setReceiverName(ocBOrder.getReceiverName());
        //收货人手机
        ocBReturnAfSend.setReceiverMobile(ocBOrder.getReceiverMobile());
        Integer returnStatus = orderRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        ocBReturnAfSend.setTReturnStatus(status);
        if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
            ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
        }
        OperateUserUtils.saveOperator(ocBReturnAfSend, user);

        return ocBReturnAfSend;
    }

    private List<OcBReturnAfSendItem> buildOcBReturnAfSendItem(List<OcBOrderItem> ocBOrderItems,
                                                               IpBAlibabaAscpOrderRefund orderRefund,
                                                               User user, OcBOrder ocBOrder) {
        //退款金额
//        BigDecimal refundFee = orderRefund.getRefundAmount();
        BigDecimal refundFee = BigDecimal.ZERO;// TODO 退款金额暂时为零
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = new ArrayList<>();
        //存在拆数量的情况   将金额放在第一个明细上
        int i = 0;
        if (log.isDebugEnabled()) {
            log.debug("{} build delivering refund order item, all order item :{}", this.getClass().getName(), ocBOrderItems);
        }
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(1L);
            ocBReturnAfSendItem.setRelationBillId(ocBOrderItem.getOcBOrderId());
            //'单据类型  客退 0，拦截 1，拒收 2 ',
            //ocBReturnAfSendItem.setBillType(1L);
            //todo 拦截状态
            //ocBReturnAfSendItem.setInterceptStatus();
            //赠品
            ocBReturnAfSendItem.setRelationBillNo(ocBOrder.getBillNo());
            ocBReturnAfSendItem.setGift(ocBOrderItem.getIsGift() + "");
            ocBReturnAfSendItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(ocBOrderItem.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(ocBOrderItem.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(ocBOrderItem.getPsCProId());
            ocBReturnAfSendItem.setPtProName(ocBOrderItem.getPtProName());
            ocBReturnAfSendItem.setPurchaseQty(ocBOrderItem.getQty());
            ocBReturnAfSendItem.setAmtActual(ocBOrderItem.getRealAmt());
            if (i == 0) {
                ocBReturnAfSendItem.setAmtHasReturn(refundFee);
                ocBReturnAfSendItem.setAmtReturn(refundFee);
                i++;
            } else {
                ocBReturnAfSendItem.setAmtHasReturn(BigDecimal.ZERO);
                ocBReturnAfSendItem.setAmtReturn(BigDecimal.ZERO);
            }
            ocBReturnAfSendItem.setPsCSkuEname(ocBOrderItem.getPsCSkuEname());
            ocBReturnAfSendItem.setFreight(BigDecimal.ZERO);
            //todo 规格id
            //ocBReturnAfSendItem.setPsCSpecId(ocBReturnOrderRefund.);
            //todo 规格名称
            //ocBReturnAfSendItem.setPsCSpecEname();
            //申请退货数量
            ocBReturnAfSendItem.setRelationBillItemId(ocBOrderItem.getId());
            ocBReturnAfSendItem.setPsCSkuPtEcode(ocBOrderItem.getPsCSkuPtEcode());
            ocBReturnAfSendItem.setGift(ocBOrderItem.getGiftType());
            ocBReturnAfSendItem.setQtyReturnApply(ocBOrderItem.getQty());

            ocBReturnAfSendItem.setOcBOrderItemId(ocBOrderItem.getId());
            if (ObjectUtil.isNotNull(ocBOrderItem.getOcBOrderId())) {
                OcBOrder order = ocBOrderMapper.get4AfReturn(ocBOrderItem.getOcBOrderId());
                if (ObjectUtil.isNotNull(order)) {
                    ocBReturnAfSendItem.setOcBOrderId(order.getId());
                    ocBReturnAfSendItem.setBusinessTypeCode(order.getBusinessTypeCode());
                    ocBReturnAfSendItem.setBusinessTypeId(order.getBusinessTypeId());
                    ocBReturnAfSendItem.setBusinessTypeName(order.getBusinessTypeName());
                }
            }
            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItems.add(ocBReturnAfSendItem);
        }
        return ocBReturnAfSendItems;
    }

    private Long selectOmsReturnOrderFromRedis(String redisKey) {
        CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        Boolean hasKey = objRedisTemplate.hasKey(redisKey);
        if (hasKey != null && hasKey) {
            Long value = objRedisTemplate.opsForValue().get(redisKey);
            return value;
        } else {
            return null;
        }
    }

    public void updateOcBReturnOrder(IpBAlibabaAscpOrderRefund ascpOrderRefund, List<OcBReturnOrder> ocBReturnOrders,
                                     List<OcBReturnOrderRefund> orderRefunds) {
        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
            if (StringUtils.isNotEmpty(ocBReturnOrder.getReturnId())) {
                continue;
            }
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            returnOrder.setId(ocBReturnOrder.getId());
            //平台退款单号
            returnOrder.setReturnId(ascpOrderRefund.getBizOrderCode());
            //卖家昵称
//            returnOrder.setBuyerNick(ascpOrderRefund.getBuyerNick());
            //申请退款时间
            returnOrder.setReturnCreateTime(ascpOrderRefund.getCreated());
            //最后修改时间
            returnOrder.setLastUpdateTime(ascpOrderRefund.getModified());
            //货物退回时间
            //returnOrder.setReturnTime(ascpOrderRefund.getGoodReturnTime());
            //退款说明
//            returnOrder.setReturnDesc(ascpOrderRefund.getReturnReason());
            //商品应退金额(
//            returnOrder.setReturnAmtList(ascpOrderRefund.getRefundAmount());
            //售后/售中
            //returnOrder.setReturnPhase(ascpOrderRefund.getRefundPhase());
            //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
//            returnOrder.setReturnAmtActual(ascpOrderRefund.getRefundAmount());
            //卖家昵称
            returnOrder.setSellerNick(ascpOrderRefund.getSellerNick());
            //物流公司名称
//            String companyName = ascpOrderRefund.getCompanyName();
            //退回物流单号
            returnOrder.setLogisticsCode(ascpOrderRefund.getTmsOrderCode());
//            returnOrder.setCpCLogisticsEname(companyName);
            // 运费 by 秦俊龙
//            returnOrder.setReturnAmtShip(ascpOrderRefund.getReturnShipamount());
            ocBReturnOrderMapper.updateById(returnOrder);
        }
        for (OcBReturnOrderRefund returnOrderRefund : orderRefunds) {
            if (returnOrderRefund.getOid() != null && returnOrderRefund.getOid().equals(ascpOrderRefund.getBizOrderCode())) {
                OcBReturnOrderRefund orderRefund = new OcBReturnOrderRefund();
                orderRefund.setId(returnOrderRefund.getId());
                Integer returnStatus = ascpOrderRefund.getReturnStatus();
                String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
                orderRefund.setRefundStatus(status);
                orderRefund.setRefundBillNo(ascpOrderRefund.getBizOrderCode());
//                orderRefund.setAmtPtRefund(ascpOrderRefund.getRefundAmount());
                returnOrderRefund.setRefundStatus(status);
                QueryWrapper<OcBReturnOrderRefund> wrapper = new QueryWrapper<>();
                wrapper.eq("id", returnOrderRefund.getId());
                wrapper.eq("oc_b_return_order_id", returnOrderRefund.getOcBReturnOrderId());
                //更新之前分库建必须设置为空
                ocBReturnOrderRefundMapper.update(orderRefund, wrapper);
            }
        }

    }

    /**
     * 拦截(对一个包裹进行处理)
     *
     * @param ocBOrder
     * @param ocBOrderItems 当前订单下所有的明细信息
     * @param deliveries
     */
    private OcBReturnOrderRelation logisticsIntercept(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems,
                                                      List<OcBOrderDelivery> deliveries,
                                                      IpBAlibabaAscpOrderRefund orderRefund, Integer interceptMark,
                                                      TaobaoReturnOrderExt.ReturnBillsStatus billsStatus,
                                                      User user) {
        //判断包裹是否有多个sku 获取sku的数量
        Map<String, BigDecimal> skuCount = new HashMap<>();
        for (OcBOrderDelivery delivery : deliveries) {
            String psCSkuEcode = delivery.getPsCSkuEcode();
            if (!skuCount.containsKey(psCSkuEcode)) {
                skuCount.put(psCSkuEcode, delivery.getQty());
            } else {
                BigDecimal qty = skuCount.get(psCSkuEcode);
                skuCount.put(psCSkuEcode, delivery.getQty().add(qty));
            }
        }
        OmsOrderRelation orderRelation = new OmsOrderRelation();
        orderRelation.setOcBOrder(ocBOrder);
        orderRelation.setOcBOrderItems(ocBOrderItems);
        orderRelation.setInterceptMark(interceptMark);
        return this.standplatRefundOrderToReturnOrder(orderRelation,
                orderRefund, skuCount, deliveries.get(0), billsStatus, user);
    }

    /**
     * 按包裹生成退换货订单的关系处理
     *
     * @param orderRelation
     * @param orderRefund
     * @param skuCount
     * @param ocBOrderDelivery
     * @param billsStatus      单据类型(拒收 , 拦截)
     * @return
     */
    public OcBReturnOrderRelation standplatRefundOrderToReturnOrder(OmsOrderRelation orderRelation,
                                                                    IpBAlibabaAscpOrderRefund orderRefund,
                                                                    Map<String, BigDecimal> skuCount,
                                                                    OcBOrderDelivery ocBOrderDelivery,
                                                                    TaobaoReturnOrderExt.ReturnBillsStatus billsStatus,
                                                                    User user) {
        OcBReturnOrderRelation returnOrderRelation = new OcBReturnOrderRelation();
        OcBReturnOrder ocBReturnOrder = this.buildOcBReturnOrderFromStandplatRefund(orderRelation,
                orderRefund, ocBOrderDelivery, billsStatus.getCode(), user);
        List<OcBReturnOrderRefund> orderRefunds = buildReturnOrderItemFromRefund(orderRelation,
                orderRefund, skuCount, user);

        // @20200818 bug#21336 通用平台退单可退数量校验--拦截场景
        boolean qtyFlag = OmsOrderQtyCalculateService.getInstance().checkQtyCanReturn(orderRefunds, orderRelation.getOcBOrderItems());

        if (!qtyFlag) {
            throw new NDSException("申请数量大于可退数量");
        }

        this.getAllSku(orderRefunds, ocBReturnOrder);
        returnOrderRelation.setReturnOrderInfo(ocBReturnOrder);
        returnOrderRelation.setOrderRefundList(orderRefunds);
        return returnOrderRelation;
    }

    /**
     * 封装主表的all_sku以及 商品数量
     *
     * @param returnOrderItems
     * @return
     */
    private void getAllSku(List<OcBReturnOrderRefund> returnOrderItems, OcBReturnOrder returnOrder) {
        //拼接退货sku加数量
        String skuQyt = "";
        BigDecimal qtyInstore = BigDecimal.ZERO;
        int i = 0;
        for (OcBReturnOrderRefund returnOrderItem : returnOrderItems) {
            qtyInstore = qtyInstore.add(returnOrderItem.getQtyRefund());
            if (i == 5) {
                continue;
            }
            String str = returnOrderItem.getPsCSkuEcode() + "(" + returnOrderItem.getQtyRefund().intValue() + "),";
            skuQyt = skuQyt + str;
            i++;

        }
        if (StringUtils.isNotEmpty(skuQyt)) {
            //去掉最后一个,号
            skuQyt = skuQyt.substring(0, skuQyt.length() - 1);
        }
        returnOrder.setAllSku(skuQyt);
        returnOrder.setQtyInstore(qtyInstore);
    }


    /**
     * 退单明细表数量
     *
     * @param orderRefund
     * @return
     */
    private List<OcBReturnOrderRefund> buildReturnOrderItemFromRefund(OmsOrderRelation orderRelation,
                                                                      IpBAlibabaAscpOrderRefund orderRefund,
                                                                      Map<String, BigDecimal> skuCount,
                                                                      User user) {
        List<OcBReturnOrderRefund> result = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        for (OcBOrderItem orderItem : ocBOrderItems) {
            BigDecimal bigDecimal = skuCount.get(orderItem.getPsCSkuEcode());
            if (bigDecimal == null) {
                continue;
            }
            if (bigDecimal.compareTo(orderItem.getQty()) > 0) {
                bigDecimal = orderItem.getQty();
            }
            OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
            BigDecimal refundAmt = orderItem.getRealAmt().divide(orderItem.getQty(),
                    4, BigDecimal.ROUND_HALF_DOWN).multiply(bigDecimal);
            returnOrderRefund.setAmtRefund(refundAmt);
            //申请数量
            returnOrderRefund.setQtyRefund(bigDecimal);
            //商品名称
            returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());

            returnOrderRefund.setPrice(orderItem.getPriceList());
            //1 qty_can_refund 购买数量 合计所有明细qty
            returnOrderRefund.setQtyCanRefund(orderItem.getQty());
            //商品单价
            returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
            //国标码
            returnOrderRefund.setBarcode(orderItem.getBarcode());
            //修改人用户名
            returnOrderRefund.setModifierename(orderItem.getModifierename());
            //商品规格
            returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
            returnOrderRefund.setOid(orderItem.getOoid());
            //条码id
            returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
            returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
            returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
            returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
            returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
            returnOrderRefund.setPsCProId(orderItem.getPsCProId());
            //颜色尺寸
            returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
            returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
            returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

            returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
            returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
            returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
            returnOrderRefund.setSex(orderItem.getSex());
            returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
            returnOrderRefund.setOcBOrderItemId(orderItem.getId());
            returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
            //returnOrderRefund.setAmtPtRefund(ipBTaobaoRefund.getRefundFee());
            returnOrderRefund.setGiftType(orderItem.getGiftType());
            returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
            //returnOrderRefund.setRefundStatus(ipBTaobaoRefund.getStatus());
            //returnOrderRefund.setRefundBillNo(ocBOrder.getBillNo());
            returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                    4, BigDecimal.ROUND_HALF_UP));
            returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");


            if (orderRefund != null && orderItem.getOoid() != null &&
                    orderItem.getOoid().equals(orderRefund.getBizOrderCode())) {
                Integer returnStatus = orderRefund.getReturnStatus();
                String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
                returnOrderRefund.setRefundStatus(status);
                returnOrderRefund.setRefundBillNo(orderRefund.getForwardOrderCode());
//                returnOrderRefund.setAmtPtRefund(orderRefund.getRefundAmount());
            }
            OperateUserUtils.saveOperator(returnOrderRefund, user);

            result.add(returnOrderRefund);
        }
        return result;
    }

    /**
     * 退单主表数据创建
     *
     * @return
     */
    private OcBReturnOrder buildOcBReturnOrderFromStandplatRefund(OmsOrderRelation orderRelation,
                                                                  IpBAlibabaAscpOrderRefund orderRefund,
                                                                  OcBOrderDelivery ocBOrderDelivery,
                                                                  Integer proType, User user) {
        OcBOrder ocBOrder = orderRelation.getOcBOrder();
        OcBReturnOrder returnOrder = new OcBReturnOrder();

        if (orderRefund != null) {
            //平台退款单号
            returnOrder.setReturnId(orderRefund.getBizOrderCode());
            //卖家呢城
            returnOrder.setSellerNick(orderRefund.getSellerNick());
            //申请退款时间
            returnOrder.setReturnCreateTime(orderRefund.getCreated());
            //最后修改时间
            returnOrder.setLastUpdateTime(orderRefund.getModified());

            //货物退回时间
            //returnOrder.setReturnTime(orderRefund.getGoodReturnTime());
            //退款说明
//            returnOrder.setReturnDesc(orderRefund.getReturnReason());
            //商品应退金额(
            returnOrder.setReturnAmtList(BigDecimal.ZERO);
            //售后/售中
            // returnOrder.setReturnPhase(orderRefund.getRefundPhase());
            //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
            returnOrder.setReturnAmtActual(BigDecimal.ZERO);

//            String companyName = orderRefund.getCompanyName();
            //退回物流单号
            returnOrder.setLogisticsCode(orderRefund.getTmsOrderCode());

//            returnOrder.setCpCLogisticsEname(companyName);
            //退回说明
            //returnOrder.setReserveVarchar02(orderRefund.getRefunddesc());
            this.setLogisticInfo(returnOrder, null);

        }
        if (null != ocBOrder) {
            //region 物流信息
            // 物流公司id
            returnOrder.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
            // 物流公司编码
            returnOrder.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
            // 物流公司名称
            returnOrder.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
            //endregion

            returnOrder.setCreationdate(new Date());
            returnOrder.setBillNo(sequenceUtil.buildReturnBillNo());
            //等待退货入库(PRD数据对象)
            returnOrder.setReturnStatus(TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode());
            //原始订单编号
            returnOrder.setOrigOrderId(ocBOrder.getId());
            //退还运费，默认0
            returnOrder.setReturnAmtShip(BigDecimal.ZERO);
            //退还其他费用，默认0
            returnOrder.setReturnAmtOther(BigDecimal.ZERO);
            //换货人姓名
            returnOrder.setReceiveName(ocBOrder.getReceiverName());
            //换货人手机
            returnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());
            //订单来源
            returnOrder.setOrdeSource(ocBOrder.getOrderSource());
            //邮编
            returnOrder.setReceiveZip(ocBOrder.getReceiverZip());
            //发货仓库
            returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
            //平台类型
            returnOrder.setPlatform(ocBOrder.getPlatform());
            // returnOrder.setThirdWarehouseType(ocBOrder.getThirdWarehouseType());
            returnOrder.setOrigOrderNo(ocBOrder.getBillNo());

            //换货金额
            returnOrder.setExchangeAmt(BigDecimal.ZERO);
            returnOrder.setTid(ocBOrder.getTid());
            //是否传AG默认否
            returnOrder.setIsToag(AGStatusEnum.INIT.getVal());
            //是否生成调拨单，默认0
            returnOrder.setIsTransfer(0);
            //是否生成零售，默认0
            returnOrder.setIsTodrp(0);
            //退单状态，默认20
            returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
            //是否手工新增，默认0
            returnOrder.setIsAdd(0);
            //虚拟入库状态，默认0
            returnOrder.setInventedStatus(0);
            //是否原退，默认0
            returnOrder.setIsRefund(0);
            //是否确认收货，默认0
            returnOrder.setIsReceiveConfirm(0);
            //WMS撤回状态，默认0
            returnOrder.setWmsCancelStatus(0);
            //强制入库，默认0
            returnOrder.setIsForce(0);
            //是否手工审核，默认0
            returnOrder.setIsManualAudit(0);
            //是否传WMS
            returnOrder.setIsTowms(0);
            //是否入仓成功
            returnOrder.setIsInstorage(0);
            returnOrder.setOrigSourceCode(ocBOrder.getSourceCode());
            returnOrder.setOrigOrderNo(ocBOrder.getBillNo());
            //退款原因
            returnOrder.setRemark("猫超默认退款无");
            returnOrder.setReturnReason("猫超默认退款理由无");
            returnOrder.setReceiveAddress(ocBOrder.getReceiverAddress());
            //region 商铺信息
            // 卖家昵称
            returnOrder.setBuyerNick(ocBOrder.getCpCShopSellerNick());
            returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
            returnOrder.setCpCShopEcode(ocBOrder.getCpCShopEcode());
            returnOrder.setCpCShopTitle(ocBOrder.getCpCShopTitle());
            //endregion

            returnOrder.setReceiverProvinceName(ocBOrder.getCpCRegionProvinceEname());
            returnOrder.setReceiverCityName(ocBOrder.getCpCRegionCityEname());
            returnOrder.setReceiverAreaName(ocBOrder.getCpCRegionAreaEname());
            returnOrder.setReceiverProvinceId(ocBOrder.getCpCRegionProvinceId());
            returnOrder.setReceiverCityId(ocBOrder.getCpCRegionCityId());
            returnOrder.setReceiverAreaId(ocBOrder.getCpCRegionAreaId());
            //原单卖家备注
            returnOrder.setOrigSellerRemark(ocBOrder.getSellerMemo());
            //原单买家留言
            returnOrder.setOrigBuyerMessage(ocBOrder.getBuyerMessage());
            //取值为发货实体仓档案中关联的退货待检实体仓仓库
            this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder);
            if (proType.equals(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode())
                    || TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_REJECTION.getCode().equals(proType)) {
                returnOrder.setCpCPhyWarehouseInId(ocBOrder.getCpCPhyWarehouseId());
                this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder);
            } else {
                StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
                //todo
                Integer isMultiReturnWarehouse = shopStrategy.getIsMultiReturnWarehouse();
                if (isMultiReturnWarehouse == null || isMultiReturnWarehouse == 0) {
                    returnOrder.setCpCPhyWarehouseInId(shopStrategy.getCpCWarehouseDefId());
                    this.selectReturnCPhyWarehouse(shopStrategy.getCpCWarehouseDefId(), returnOrder);
                }
            }
        }
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());

        returnOrder.setReturnProType(proType);
        // 从中间表取物流信息 退款类型等于 退货退款时
        if (Objects.nonNull(orderRefund)) {
            // 假如退货物流单号不为空！ 并且和之前的物流单号不同
            if (StringUtils.isNotBlank(orderRefund.getTmsOrderCode())) {
                try {
                    LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByCarrierCode(orderRefund.getTmsServiceCode());
                    if (Objects.nonNull(logisticsInfo)) {
                        returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                        returnOrder.setCpCLogisticsId(logisticsInfo.getId());
                    }
                } catch (Exception e) {
                    log.error("错误信息!", e);
                }
                returnOrder.setLogisticsCode(orderRefund.getTmsOrderCode());
//                returnOrder.setCpCLogisticsEname(orderRefund.getCompanyName());
            }
        } else if (ocBOrderDelivery != null) {
            returnOrder.setLogisticsCode(ocBOrderDelivery.getLogisticNumber());
            returnOrder.setCpCLogisticsId(ocBOrderDelivery.getCpCLogisticsId());
            returnOrder.setCpCLogisticsEcode(ocBOrderDelivery.getCpCLogisticsEcode());
            returnOrder.setCpCLogisticsEname(ocBOrderDelivery.getCpCLogisticsEname());
        }
        returnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());
        returnOrder.setInventedStatus(orderRelation.getInterceptMark()); //未发起拦截

        ocSaveChangingOrRefundingService.checkBillType(returnOrder);
//        //加入“空运单号延迟推单有效时间”字段  在保存的方法里统一处理 2021-11-10
//        returnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder));
        OperateUserUtils.saveOperator(returnOrder, user);
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 构建退货单主表数据:{}", JSONObject.toJSONString(returnOrder));
        }
        return returnOrder;
    }

    /**
     * 判断当前需要拦截的全是赠品的包裹
     */
    private boolean checkAllGift(List<OcBOrderItem> ocBOrderItems, List<OcBOrderDelivery> deliveries) {
        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            return false;
        }
        List<String> skuList = ocBOrderItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
        List<String> stringList = deliveries.stream().map(OcBOrderDelivery::getPsCSkuEcode).collect(Collectors.toList());
        return skuList.containsAll(stringList);
    }

    /**
     * 通过实体仓id查询该实体仓的退货仓id
     */
    private void selectReturnCPhyWarehouse(Long cPhyWarehouseId, OcBReturnOrder returnOrder) {
        if (cPhyWarehouseId != null) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cPhyWarehouseId);
            if (cpCPhyWarehouse != null) {
                Integer wmsControlWarehouse = cpCPhyWarehouse.getWmsControlWarehouse();
                if (wmsControlWarehouse != null && wmsControlWarehouse == 1) {
                    returnOrder.setIsNeedToWms(1L);
                }
            }
        }
    }

    public void setLogisticInfo(OcBReturnOrder returnOrder, String buyerLogisticName) {
        if (StringUtils.isNotEmpty(buyerLogisticName)) {
            LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(buyerLogisticName);
            if (logisticsInfo != null) {
                returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                returnOrder.setCpCLogisticsId(logisticsInfo.getId());
            }
        }
    }

    /**
     * 拦截生成发货后退款单(退货退款)
     */
    public void foundRefundSlipAfter(List<Long> returnId, OcBOrder ocBOrder, IpBAlibabaAscpOrderRefund orderRefund,
                                     User user) {
        //获取所有的退单明细数据
        try {
            List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectReturnOrderListByOrderIds(returnId);
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
            List<OcBReturnOrderRefund> orderRefunds = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(returnId);
            this.updateOcBReturnOrder(orderRefund, ocBReturnOrders, orderRefunds);
            //获取发货单主表数据
            if (!isRefundSlipAfExist(orderRefund.getBizOrderCode())) {
                return;
            }
            OcBReturnAfSendRelation ocBReturnAfSendRelation =
                    this.alibabaAscpRefundAfSendToReturn(orderRefunds, ocBOrder,
                            orderRefund, user, orderItems, ocBReturnOrders);
            omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation,user);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 生成退款单异常", e);
            throw new NDSException(e);
        }
    }

    /**
     * 处理发货后退款单的数据(退货退款)
     *
     * @param ocBReturnOrderRefunds
     * @param ocBOrder
     * @param orderRefund
     * @return
     */
    public OcBReturnAfSendRelation alibabaAscpRefundAfSendToReturn(List<OcBReturnOrderRefund> ocBReturnOrderRefunds,
                                                                   OcBOrder ocBOrder,
                                                                   IpBAlibabaAscpOrderRefund orderRefund,
                                                                   User user, List<OcBOrderItem> orderItems,
                                                                   List<OcBReturnOrder> ocBReturnOrders) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, orderRefund,
                getRefundType(orderRefund.getRefundType()), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItemRelation(ocBReturnOrderRefunds,
                orderRefund, user, orderItems, ocBReturnOrders);
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }

    /**
     * 通用平台退单状态转换为中台类型
     *
     * @param refundType
     * @return
     */
    int getRefundType(Integer refundType) {
        if (refundType == null) {
            return 0;
        }
        if (refundType == 2) {
            return TaobaoReturnOrderExt.SendBillType.RETURN_REFUND.getCode();
        }
        return refundType;
    }

    /**
     * 生成发货后退款单明细(关联退换货单)
     *
     * @param ocBReturnOrderRefunds
     * @return
     */
    private List<OcBReturnAfSendItem> buildOcBReturnAfSendItemRelation(List<OcBReturnOrderRefund> ocBReturnOrderRefunds,
                                                                       IpBAlibabaAscpOrderRefund ipBStandplatRefund,
                                                                       User user, List<OcBOrderItem> orderItems,
                                                                       List<OcBReturnOrder> ocBReturnOrders) {
        if (log.isDebugEnabled()) {
            log.debug("{} build delivering refund order item by relation, all order item :{}", this.getClass().getName(), ocBReturnOrderRefunds.toString());
        }
        Map<Long, OcBOrderItem> map = new HashMap<>(10);
        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OcBOrderItem item : orderItems) {
                map.put(item.getId(), item);
            }
        }
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = new ArrayList<>();
        for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefunds) {
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(0L);
            ocBReturnAfSendItem.setRelationBillId(ocBReturnOrderRefund.getOcBReturnOrderId());
            List<OcBReturnOrder> list = ocBReturnOrders.stream().filter(p -> p.getId().equals(ocBReturnOrderRefund.getOcBReturnOrderId())).collect(Collectors.toList());
            ocBReturnAfSendItem.setRelationBillNo(list.get(0).getBillNo());
            //'单据类型  客退 0，拦截 1，拒收 2 ',
            ocBReturnAfSendItem.setBillType(1);
            //todo 拦截状态
            //ocBReturnAfSendItem.setInterceptStatus();
            //赠品
            ocBReturnAfSendItem.setGift(ocBReturnOrderRefund.getGiftType());
            ocBReturnAfSendItem.setPsCSkuId(ocBReturnOrderRefund.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(ocBReturnOrderRefund.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(ocBReturnOrderRefund.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(ocBReturnOrderRefund.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(ocBReturnOrderRefund.getPsCProId());
            // 邮费
//            ocBReturnAfSendItem.setFreight(ipBStandplatRefund.getReturnShipamount());

            //todo 规格id
            //ocBReturnAfSendItem.setPsCSpecId(ocBReturnOrderRefund.);
            //todo 规格名称
            //ocBReturnAfSendItem.setPsCSpecEname();
            //申请退货数量
            ocBReturnAfSendItem.setQtyReturnApply(ocBReturnOrderRefund.getQtyRefund());
            ocBReturnAfSendItem.setAmtReturn(ocBReturnOrderRefund.getAmtRefund());
            ocBReturnAfSendItem.setPurchaseQty(ocBReturnOrderRefund.getQtyRefund());
            OcBOrderItem orderItem = map.get(ocBReturnOrderRefund.getOcBOrderItemId());
            if (orderItem != null) {
                if (refundOrderService.isNullOrZero(ocBReturnAfSendItem.getQtyReturnApply())) {
                    ocBReturnAfSendItem.setQtyReturnApply(orderItem.getQty());
                }
                if (refundOrderService.isNullOrZero(ocBReturnAfSendItem.getAmtReturn())) {
                    ocBReturnAfSendItem.setAmtReturn(orderItem.getRealAmt());
                }
                ocBReturnAfSendItem.setPurchaseQty(orderItem.getQty());
                ocBReturnAfSendItem.setPtProName(orderItem.getPtProName());
                ocBReturnAfSendItem.setPsCSkuEname(orderItem.getPsCSkuEname());
                ocBReturnAfSendItem.setPsCSkuPtEcode(orderItem.getPsCSkuPtEcode());
                // BigDecimal realAmt = orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP).multiply(ocBReturnOrderRefund.getQtyRefund());
                ocBReturnAfSendItem.setAmtActual(orderItem.getRealAmt());

                ocBReturnAfSendItem.setOcBOrderItemId(orderItem.getId());
                if (ObjectUtil.isNotNull(orderItem.getOcBOrderId())) {
                    OcBOrder order = ocBOrderMapper.get4AfReturn(orderItem.getOcBOrderId());
                    if (ObjectUtil.isNotNull(order)) {
                        ocBReturnAfSendItem.setOcBOrderId(order.getId());
                        ocBReturnAfSendItem.setBusinessTypeCode(order.getBusinessTypeCode());
                        ocBReturnAfSendItem.setBusinessTypeId(order.getBusinessTypeId());
                        ocBReturnAfSendItem.setBusinessTypeName(order.getBusinessTypeName());
                    }
                }
            }
            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItems.add(ocBReturnAfSendItem);
        }
        return ocBReturnAfSendItems;
    }

    /**
     * 生成发货后退款单(仅退款)
     *
     * @param ocBOrderItems
     * @param ocBOrder
     * @param refundRelation
     */
    public void foundRefundSlipAfterRefundOnly(List<OcBOrderItem> ocBOrderItems, OcBOrder ocBOrder,
                                               IpBAlibabaAscpOrderRefundRelation refundRelation, User user) {
        IpBAlibabaAscpOrderRefund orderRefund = refundRelation.getOrderRefund();
        //获取所有的退单明细数据
        try {
            if (!isRefundSlipAfExist(orderRefund.getBizOrderCode())) {
                return;
            }
            OcBReturnAfSendRelation ocBReturnAfSendRelation =
                    this.refundAfSendToRefundOnlys(ocBOrderItems, ocBOrder, refundRelation, user);
            //获取发货单主表数据
            omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation,user);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 生成退款单异常", e);
        }
    }

    /**
     * 处理发货后退款单的数据(仅退款)
     *
     * @param ocBOrderItems
     * @param ocBOrder
     * @param orderRefundRelation
     * @return
     */
    public OcBReturnAfSendRelation refundAfSendToRefundOnly(List<OcBOrderItem> ocBOrderItems,
                                                            OcBOrder ocBOrder,
                                                            IpBAlibabaAscpOrderRefundRelation orderRefundRelation,
                                                            User user) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        // 退款金额
        BigDecimal refundFee = BigDecimal.ZERO;
        Map<String, IpBAlibabaAscpOrderRefundItem> ooidMap = Maps.newHashMap();
        for (IpBAlibabaAscpOrderRefundItem current : orderRefundRelation.getIpBAlibabaAscpOrderRefundItems()) {
            ooidMap.put(current.getSubOrderCode(), current);
//            BigDecimal bigDecimal = Optional.ofNullable(current.getRefundFee()).orElse(BigDecimal.ZERO);
            BigDecimal bigDecimal = BigDecimal.ZERO;
            refundFee = refundFee.add(bigDecimal);
        }
        if (log.isDebugEnabled()) {
            log.debug(" before refundAfSendToRefundOnly orderNo:{}, refundFee:{},", orderRefundRelation.getOrderNo(), refundFee);
        }
        // 假如子表金额之和等于0 ,就从头表上取
        if (BigDecimal.ZERO.compareTo(refundFee) == 0) {
//            refundFee = orderRefundRelation.getOrderRefund().getRefundAmount();
            refundFee = BigDecimal.ZERO;
        }
        if (log.isDebugEnabled()) {
            log.debug("after refundAfSendToRefundOnly orderNo:{}, refundFee:{},", orderRefundRelation.getOrderNo(), refundFee);
        }
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, orderRefundRelation, refundFee,
                TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode(), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItems(ocBOrderItems, ooidMap, user, ocBOrder);
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }

    /**
     * 处理发货后退款单的数据(仅退款)
     *
     * @param ocBOrderItems
     * @param ocBOrder
     * @param refundRelation
     * @return
     */
    public OcBReturnAfSendRelation refundAfSendToRefundOnlys(List<OcBOrderItem> ocBOrderItems,
                                                             OcBOrder ocBOrder,
                                                             IpBAlibabaAscpOrderRefundRelation refundRelation,
                                                             User user) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        // 退款金额
        BigDecimal refundFee = BigDecimal.ZERO;
        Map<String, IpBAlibabaAscpOrderRefundItem> ooidMap = Maps.newHashMap();
        for (IpBAlibabaAscpOrderRefundItem current : refundRelation.getIpBAlibabaAscpOrderRefundItems()) {
            ooidMap.put(current.getSubOrderCode(), current);
//            BigDecimal bigDecimal = Optional.ofNullable(current.getRefundFee()).orElse(BigDecimal.ZERO);
//            refundFee = refundFee.add(bigDecimal);
        }
        if (log.isDebugEnabled()) {
            log.debug(" before refundAfSendToRefundOnly orderNo:{}, refundFee:{},", refundRelation.getOrderNo(), refundFee);
        }
        // 假如子表金额之和等于0 ,就从头表上取
        if (BigDecimal.ZERO.compareTo(refundFee) == 0) {
//            refundFee = refundRelation.getIpBStandplatRefund().getRefundAmount();
        }
        if (log.isDebugEnabled()) {
            log.debug("after refundAfSendToRefundOnly orderNo:{}, refundFee:{},", refundRelation.getOrderNo(), refundFee);
        }
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, refundRelation, refundFee,
                TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode(), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItems(ocBOrderItems, ooidMap, user, ocBOrder);
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }

    private List<OcBReturnAfSendItem> buildOcBReturnAfSendItems(List<OcBOrderItem> ocBOrderItems,
                                                                Map<String, IpBAlibabaAscpOrderRefundItem> itemMap,
                                                                User user, OcBOrder ocBOrder) {
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(1L);
            ocBReturnAfSendItem.setRelationBillId(ocBOrderItem.getOcBOrderId());
            ocBReturnAfSendItem.setRelationBillNo(ocBOrder.getBillNo());
            //赠品
            ocBReturnAfSendItem.setGift(ocBOrderItem.getIsGift() + "");
            ocBReturnAfSendItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(ocBOrderItem.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(ocBOrderItem.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(ocBOrderItem.getPsCProId());
            ocBReturnAfSendItem.setPtProName(ocBOrderItem.getPtProName());

            ocBReturnAfSendItem.setPurchaseQty(ocBOrderItem.getQty());
            ocBReturnAfSendItem.setAmtActual(ocBOrderItem.getRealAmt());
            IpBAlibabaAscpOrderRefundItem refundItem = Optional.ofNullable(itemMap.get(ocBOrderItem.getOoid())).orElse(new IpBAlibabaAscpOrderRefundItem());
//            BigDecimal refundFee = refundItem.getRefundFee() == null ? ocBOrderItem.getRealAmt() : refundItem.getRefundFee();

//            ocBReturnAfSendItem.setAmtHasReturn(refundFee);
//            ocBReturnAfSendItem.setAmtReturn(refundFee);
            ocBReturnAfSendItem.setPsCSkuEname(ocBOrderItem.getPsCSkuEname());
            ocBReturnAfSendItem.setFreight(BigDecimal.ZERO);
            ocBReturnAfSendItem.setRelationBillItemId(ocBOrderItem.getId());
            ocBReturnAfSendItem.setPsCSkuPtEcode(ocBOrderItem.getPsCSkuPtEcode());
            ocBReturnAfSendItem.setGift(ocBOrderItem.getGiftType());
            if (Objects.isNull(refundItem.getPlanReturnQuantity()) && !isNullOrZero(ocBOrderItem.getPriceActual())) {
                // 申请退货数量 余数进1
//                ocBReturnAfSendItem.setQtyReturnApply(refundFee.divide(ocBOrderItem.getPriceActual(), 0, BigDecimal.ROUND_UP));
            } else {
                // 申请退货数量
                ocBReturnAfSendItem.setQtyReturnApply(new BigDecimal(refundItem.getPlanReturnQuantity()));
            }

            ocBReturnAfSendItem.setOcBOrderItemId(ocBOrderItem.getId());
            if (ObjectUtil.isNotNull(ocBOrderItem.getOcBOrderId())) {
                OcBOrder order = ocBOrderMapper.get4AfReturn(ocBOrderItem.getOcBOrderId());
                if (ObjectUtil.isNotNull(order)) {
                    ocBReturnAfSendItem.setOcBOrderId(order.getId());
                    ocBReturnAfSendItem.setBusinessTypeCode(order.getBusinessTypeCode());
                    ocBReturnAfSendItem.setBusinessTypeId(order.getBusinessTypeId());
                    ocBReturnAfSendItem.setBusinessTypeName(order.getBusinessTypeName());
                }
            }

            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItems.add(ocBReturnAfSendItem);
        }
        return ocBReturnAfSendItems;
    }

    /**
     * 是空的或者是0
     *
     * @param arg
     * @return
     */
    public boolean isNullOrZero(BigDecimal arg) {
        return arg == null || BigDecimal.ZERO.compareTo(arg) == 0;
    }

    /**
     * 生成发货后退款单
     *
     * @return
     */
    private OcBReturnAfSend buildOcBReturnAfSend(OcBOrder ocBOrder,
                                                 IpBAlibabaAscpOrderRefundRelation refundRelation,
                                                 BigDecimal refundFee,
                                                 Integer billType, User user) {
        IpBAlibabaAscpOrderRefund orderRefund = refundRelation.getOrderRefund();
        OcBReturnAfSend ocBReturnAfSend = new OcBReturnAfSend();
        ocBReturnAfSend.setCpCShopId(ocBOrder.getCpCShopId());
        ocBReturnAfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        ocBReturnAfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        ocBReturnAfSend.setTid(ocBOrder.getTid());
        ocBReturnAfSend.setBillNo(sequenceUtil.aFbuildBillNo());
        //退款状态 0 待审核
        ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal());
        ocBReturnAfSend.setTReturnId(orderRefund.getBizOrderCode());
        ocBReturnAfSend.setTReturnStatus(String.valueOf(orderRefund.getReturnStatus()));
        //单据类型 0 退货退款 1仅退款',
        ocBReturnAfSend.setBillType(billType);
//        ocBReturnAfSend.setVipNick(orderRefund.getBuyerNick());
        // @******** 加手机
        ocBReturnAfSend.setVipPhone(ocBOrder.getReceiverPhone());
//        ocBReturnAfSend.setReason(orderRefund.getReturnReason());
        //单据来源设置默认值为2 自动
        ocBReturnAfSend.setRefundOrderSourceType(RefundOrderSourceTypeEnum.AUTO.getValue());
        //支付方式
        ocBReturnAfSend.setPayMode(ocBOrder.getPayType() + "");
        //支付宝账号
        ocBReturnAfSend.setPayAccount(ocBOrder.getBuyerAlipayNo());
        // 通用平台退款状态
        ocBReturnAfSend.setTReturnStatus(TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(orderRefund.getReturnStatus()));
        ocBReturnAfSend.setSourceBillNo(ocBOrder.getId() + "");
        ocBReturnAfSend.setCpCPlatformId(Long.valueOf(ocBOrder.getPlatform()));
        ocBReturnAfSend.setPayMode(OcBReturnAfSendListEnums.PayTypeEnum.Alipay.getVal());
        // 申请退款金额
        ocBReturnAfSend.setAmtReturnApply(refundFee);
        //todo 实际退款金额
        ocBReturnAfSend.setAmtReturnActual(refundFee);
        //申请退款时间
        ocBReturnAfSend.setReturnApplyTime(new Date());
        ocBReturnAfSend.setAgStatus(AGStatusEnum.INIT.getVal() + "");
//        ocBReturnAfSend.setReturnExplain(orderRefund.getReturnReason());
        Integer status = orderRefund.getReturnStatus();
        if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.toLong().intValue() == status) {
            ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
        }
        OperateUserUtils.saveOperator(ocBReturnAfSend, user);

        return ocBReturnAfSend;
    }

    /**
     * 拦截关闭时查询退款单是否存在的处理
     *
     * @param orderRefund
     * @return
     */
    public List<Long> interceptOrderIsExist(IpBAlibabaAscpOrderRefund orderRefund, List<IpBAlibabaAscpOrderRefundItem> orderRefundItems) {
        List<Long> existReturnOrder = this.isExistReturnOrderRefundByReturnId(orderRefund.getBizOrderCode());
        // 假如退款单为空
        if (CollectionUtils.isEmpty(existReturnOrder)) {
            // 中间表的平台单号 String tid = orderRefund.getOrderNo();
            List<String> ooids = orderRefundItems.stream().filter(item -> item.getSubOrderCode() != null).map(IpBAlibabaAscpOrderRefundItem::getSubOrderCode).collect(Collectors.toList());
            if (ooids.isEmpty()) {
                return null;
            }
            // 根据
            List<Long> refundByoid = this.isExistReturnOrderRefundByoid(StringUtils.join(ooids, ","));
            //将必要的退款数据更新到对应的退货单
            if (CollectionUtils.isNotEmpty(refundByoid)) {
                List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectReturnOrderListByOrderIds(refundByoid);
                List<OcBReturnOrderRefund> orderRefunds = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(refundByoid);
                this.updateOcBReturnOrder(orderRefund, ocBReturnOrders, orderRefunds);
                List<OcBReturnOrderRefund> orderRefundList = orderRefunds.stream().filter(p -> p.getRefundStatus() != null
                        && (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(p.getRefundStatus())
                        || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(p.getRefundStatus()))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(orderRefundList) && orderRefunds.size() == orderRefundList.size()) {
                    return refundByoid;
                } else {
                    return null;
                }
            }
        } else {
            // 根据 oc_b_return_order_id 找到退货单明细
            List<OcBReturnOrderRefund> orderRefunds = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(existReturnOrder);
            //  过滤出 退款状态不是空 && (订单状态是关闭 || 卖家拒绝退款 )
            List<OcBReturnOrderRefund> orderRefundList = orderRefunds.stream().filter(p -> p.getRefundStatus() != null
                    && (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(p.getRefundStatus())
                    || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(p.getRefundStatus()))).collect(Collectors.toList());
            // 返回 OC_B_RETURN_ORDER_ID 退款单id
            if (CollectionUtils.isNotEmpty(orderRefundList) && orderRefunds.size() == orderRefundList.size()) {
                return existReturnOrder;
            } else {
                return null;
            }
        }
        return existReturnOrder;
    }

    /**
     * 根据明细表的退款单号查询退单是否存在
     *
     * @param refundNo
     * @return
     */
    public List<Long> isExistReturnOrderRefundByReturnId(String refundNo) {
        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByRefundIdAndStatus(refundNo);
        if (CollectionUtils.isNotEmpty(ids)) {
            return new ArrayList<>(ids);
        }
        return null;
    }

    /**
     * 根据明细表的oid查询退货单是否存在
     *
     * @param oid
     * @return
     */
    public List<Long> isExistReturnOrderRefundByoid(String oid) {
        if (oid == null) {
            return null;
        }
        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByOid(oid);
        if (CollectionUtils.isNotEmpty(ids)) {
            //排除已经取消的退换货订单
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(new ArrayList<>(ids));
            if (CollectionUtils.isNotEmpty(list)) {
                return list.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void refundOrderClose(List<Long> refundOrderIds, List<OmsOrderRelation> omsOrderRelation,
                                 IpBAlibabaAscpOrderRefund orderRefund, User user) {
        List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectOcBReturnOrderByOrderIds(refundOrderIds);
        //关闭发货后退款单
        omsRefundOrderService.closedRefundSlip(orderRefund.getBizOrderCode());
        if (CollectionUtils.isNotEmpty(ocBReturnOrders)) {
            JSONObject jsonObject = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            List<OcBReturnOrderRefund> ocBReturnOrderRefunds = new ArrayList<>();
            for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
                //
                List<OcBReturnOrderRefund> refundList = ocBReturnOrderRefundMapper.selectByOcOrderId(ocBReturnOrder.getId());
                ocBReturnOrderRefunds.addAll(refundList);
                Integer returnStatus = ocBReturnOrder.getReturnStatus();
                if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus)) {
                    jsonArray.add(ocBReturnOrder.getId());
                    String logisticsCode = ocBReturnOrder.getLogisticsCode();
                    String returnId = ocBReturnOrder.getReturnId();
                    if (StringUtils.isNotEmpty(logisticsCode)) {
                        String redisKey = BllRedisKeyResources.getOmsReturnOrderLogisticsKey(logisticsCode);
                        RedisMasterUtils.getObjRedisTemplate().delete(redisKey);
                    }
                    if (StringUtils.isNotEmpty(returnId)) {
                        String redisKey = BllRedisKeyResources.getOmsReturnOrderReturnIdKey(returnId);
                        RedisMasterUtils.getObjRedisTemplate().delete(redisKey);
                    }

                }
            }
            if (CollectionUtils.isNotEmpty(jsonArray)) {
                jsonObject.put("ids", jsonArray);
                ValueHolderV14 holderV14 = ocCancelChangingOrRefundService.orRefundService(jsonObject, user, Boolean.FALSE);
                int code = Tools.getInt(holderV14.getCode(), -1);
                if (code == 0) {
                    this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            "", orderRefund);
                    if (CollectionUtils.isNotEmpty(ocBReturnOrderRefunds)) {
                        for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefunds) {
                            OcBOrderItem item = new OcBOrderItem();
                            item.setId(ocBReturnOrderRefund.getOcBOrderItemId());
                            item.setOcBOrderId(ocBReturnOrderRefund.getOcBOrderId());
                            item.setQtyReturnApply(BigDecimal.ZERO);
                            omsOrderItemService.updateOcBOrderItem(item, ocBReturnOrderRefund.getOcBOrderId());
                        }
                    }
                    //更新中间表转换转状态
                    this.updateOrder(omsOrderRelation);
                    //插入日志
                    //this.insertLogs(jsonArray);
                } else {
                    String remark = holderV14.getMessage();
                    if (checkReturnOrderData(orderRefund)) {
                        this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                remark, orderRefund);
                    } else {
                        this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                                remark, orderRefund);
                    }
                }
            } else {
                String remark = SysNotesConstant.SYS_REMARK33;
                this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, orderRefund);
            }
        }
    }

    private void updateOrder(List<OmsOrderRelation> omsOrderRelation) {
        for (OmsOrderRelation orderRelation : omsOrderRelation) {
            OcBOrder ocBOrder1 = orderRelation.getOcBOrder();
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(ocBOrder1.getId());
            //无退货
            ocBOrder.setReturnStatus(0);
            omsOrderService.updateOrderInfo(ocBOrder);
        }
    }

    /**
     * 检验原单不存在的时间
     *
     * @return
     */
    private boolean checkReturnOrderData(IpBAlibabaAscpOrderRefund orderRefund) {
        Date date = new Date();
        //判断退单时间是否超过三天
        Date created = orderRefund.getCreated();
        Long threeDays = 3 * 24 * 60 * 60 * 1000L + created.getTime();
        return threeDays < date.getTime();
    }

    /**
     * 通过退单明细计算金额
     *
     * @param orderInfo
     * @param orderRefund
     */
    public void setAfOrderAmount(IpBAlibabaAscpOrderRefundRelation orderInfo, IpBAlibabaAscpOrderRefund orderRefund) {
        // 退款金额
        BigDecimal refundFee = BigDecimal.ZERO;
        // 假如不是整单退
        if (!orderInfo.isFullRefund()) {
            for (IpBAlibabaAscpOrderRefundItem current : orderInfo.getIpBAlibabaAscpOrderRefundItems()) {
//                BigDecimal bigDecimal = Optional.ofNullable(current.getRefundFee()).orElse(BigDecimal.ZERO);
//                refundFee = refundFee.add(bigDecimal);
            }
            if (BigDecimal.ZERO.compareTo(refundFee) != 0) {
//                orderRefund.setRefundAmount(refundFee);
            }
        } else {
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            for (OmsOrderRelation orderRelation : omsOrderRelation) {
                if (orderRelation.getOcBOrder() != null) {
                    if (!omsRefundOrderService.isNullOrZero(orderRelation.getOcBOrder().getOrderAmt())) {
                        refundFee = refundFee.add(orderRelation.getOcBOrder().getOrderAmt());
                    }
                }
            }
//            orderRefund.setRefundAmount(refundFee);
        }
    }

    /**
     * 按子订单生成退换货订单的关系处理
     *
     * @param orderRelation
     * @param orderRefund
     * @return
     */
    public List<OcBReturnOrderRelation> standplatRefundOrderToReturnOid(IpBAlibabaAscpOrderRefundRelation orderInfo,
                                                                        List<OmsOrderRelation> orderRelation,
                                                                        IpBAlibabaAscpOrderRefund orderRefund,
                                                                        List<IpBAlibabaAscpOrderRefundItem> orderRefundItems,
                                                                        User user) {
        if (log.isDebugEnabled()) {
            log.debug("{} start generate return order relation process action [standplatRefundOrderToReturnOid], order NO.{}. into order relation list:{}", this.getClass().getName(), orderInfo.getOrderId(), JSON.toJSONString(orderRelation));
        }
        //生成主表数据
        List<OcBReturnOrderRelation> orderRelations = new ArrayList<>();
        OmsOrderRelation omsOrderRelation = orderRelation.get(0);
        OcBReturnOrder ocBReturnOrder = this.buildOcBReturnOrderFromStandplatRefund(omsOrderRelation, orderRefund,
                null, TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode(), user);
        List<OcBOrderItem> orderItemList = new ArrayList<>();
        for (OmsOrderRelation relation : orderRelation) {
            orderItemList.addAll(relation.getOcBOrderItems());
            //判断赠品(是否有挂靠赠品)
            List<OmsOrderRelation.OcOrderGifts> ocOrderGifts = relation.getOcOrderGifts();
            if (CollectionUtils.isNotEmpty(ocOrderGifts)) {
                for (OmsOrderRelation.OcOrderGifts ocOrderGift : ocOrderGifts) {
                    if (ocOrderGift.getGiftMark() == 2) {
                        List<OcBOrderItem> ocBOrderGifts = ocOrderGift.getOcBOrderGifts();
                        //将挂靠赠品加入明细
                        orderItemList.addAll(ocBOrderGifts);
                    } else {
                        String orderNo = orderRefund.getBizOrderCode();
                        if (!isGift(orderNo)) {
                            List<OcBOrderItem> ocBOrderGifts = ocOrderGift.getOcBOrderGifts();
                            //将挂靠赠品加入明细
                            orderItemList.addAll(ocBOrderGifts);
                        }
                    }
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("{} order {} [standplatRefundOrderToReturnOid] action mapping goods item end, mapping goods list:{}", this.getClass().getName(), orderInfo.getOrderId(), JSON.toJSONString(orderItemList));
        }
        OmsOrderRelation omsRelation = new OmsOrderRelation();
        omsRelation.setOcBOrder(omsOrderRelation.getOcBOrder());
        omsRelation.setOcBOrderItems(orderItemList);
        List<OcBReturnOrderRefund> orderRefunds = this.buildReturnOrderItemFromOid(true, omsRelation, orderRefund, orderRefundItems, user);
        if (CollectionUtils.isEmpty(orderRefunds)) {
            return null;
        }

        // @20200813 bug#21336 通用平台退单可退数量校验
        boolean qtyFlag = OmsOrderQtyCalculateService.getInstance().checkQtyCanReturn(orderRefunds, omsOrderRelation.getOcBOrderItems());

        if (!qtyFlag) {
            throw new NDSException("申请数量大于可退数量");
        }

        this.getAllSku(orderRefunds, ocBReturnOrder);
        OcBReturnOrderRelation relation = new OcBReturnOrderRelation();
        ocBReturnOrder.setReturnAmtActual(orderRefunds.stream().map(OcBReturnOrderRefund::getAmtRefund)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        ocBReturnOrder.setReturnAmtList(ocBReturnOrder.getReturnAmtActual());
        relation.setReturnOrderInfo(ocBReturnOrder);
        relation.setOrderRefundList(orderRefunds);
        orderRelations.add(relation);

        // @20200825 重算金额
        OmsReturnOrderService.getInstance().reCalculateReturnAmt(orderRelations);

        return orderRelations;
    }

    /**
     * 按子订单生成退单明细数据
     *
     * @param orderRelation
     * @return
     */
    private List<OcBReturnOrderRefund> buildReturnOrderItemFromOid(boolean isFullRefund, OmsOrderRelation orderRelation,
                                                                   IpBAlibabaAscpOrderRefund orderRefund,
                                                                   List<IpBAlibabaAscpOrderRefundItem> orderRefundItems,
                                                                   User user) {
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        if (log.isDebugEnabled()) {
            log.debug("=======isFullRefund:{} ,no :{}========", isFullRefund, orderRefund.getBizOrderCode());
        }
        if (isFullRefund) {
            return buildReturnOrderItemFromOidBak(orderRelation, orderRefund, orderRefundItems, user);
        }
        return this.getReturnRefunds(orderRefund, orderRefundItems, ocBOrderItems, user);
    }

    private List<OcBReturnOrderRefund> buildReturnOrderItemFromOidBak(OmsOrderRelation orderRelation,
                                                                      IpBAlibabaAscpOrderRefund orderRefund,
                                                                      List<IpBAlibabaAscpOrderRefundItem> orderRefundItems,
                                                                      User user) {
        List<OcBReturnOrderRefund> orderRefunds = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        OcBOrder ocBOrder = orderRelation.getOcBOrder();
        //退货金额
        for (OcBOrderItem orderItem : ocBOrderItems) {
            //已申请退货数量
            BigDecimal qtyReturnApply = orderItem.getQtyReturnApply();
            if (qtyReturnApply == null) {
                qtyReturnApply = BigDecimal.ZERO;
            }
            orderRefunds.add(this.geFullOcBReturnOrderRefund(orderRefund, user, ocBOrder, orderItem, orderItem.getQty()));
        }
        return orderRefunds;
    }

    /**
     * 获取退单明细
     *
     * @param orderRefund      中间表主表
     * @param orderRefundItems 中间表明细
     * @param ocBOrderItems    订单明细
     * @param user             操作员
     * @return
     */
    private List<OcBReturnOrderRefund> getReturnRefunds(IpBAlibabaAscpOrderRefund orderRefund,
                                                        List<IpBAlibabaAscpOrderRefundItem> orderRefundItems,
                                                        List<OcBOrderItem> ocBOrderItems,
                                                        User user) {
        ArrayList<OcBReturnOrderRefund> refunds = Lists.newArrayList();
        if (log.isDebugEnabled()) {
            log.debug(" ========== getReturnRefunds  ocBOrderItems:{},orderRefundItems:{}",
                    JSONObject.toJSONString(ocBOrderItems), JSONObject.toJSONString(orderRefundItems));
        }
        // 订单详情item
        Map<String, OcBOrderItem> itemMap = ocBOrderItems.stream().distinct().collect(Collectors.toMap(OcBOrderItem::getOoid, Function.identity()));
        if (log.isDebugEnabled()) {
            log.debug(" ========== getReturnRefunds itemMap:{}", JSONObject.toJSONString(itemMap));
        }
        for (IpBAlibabaAscpOrderRefundItem refundItem : orderRefundItems) {
            OcBOrderItem ocBOrderItem = itemMap.get(refundItem.getSubOrderCode());
            if (Objects.isNull(ocBOrderItem)) {
                log.info(" in to ocBOrderItem is null ");
                continue;
            }
            BigDecimal price = this.getOcBorderItemSinglePrice(refundItem, ocBOrderItem);
            if (BigDecimal.ZERO.compareTo(price) == 0) {
                log.info(" in to price is zero refundItemId is :{}   ", refundItem.getId());
                continue;
            }
            // 退货单申请数量
            BigDecimal qty = BigDecimal.ZERO;
            // 退货单申请金额
            BigDecimal applyPrice = BigDecimal.ZERO;
            // 退货中间表明细,refundFee不为空
//            if (refundItem.getRefundFee() != null && BigDecimal.ZERO.compareTo(refundItem.getRefundFee()) != 0) {
//                qty = refundItem.getRefundFee().divide(price, 0, BigDecimal.ROUND_UP);
//                applyPrice = refundItem.getRefundFee();
//            }
//            // 退货中间表明细 数量 不为空
//            else if (refundItem.getReturnQuantity() != null && BigDecimal.ZERO.compareTo(refundItem.getReturnQuantity()) != 0) {
//                // 退货单申请金额
//                applyPrice = refundItem.getReturnQuantity().multiply(price);
//                qty = refundItem.getReturnQuantity();
//            } else {
//                // 退货单申请金额
//                applyPrice = ocBOrderItem.getRealAmt();
//                qty = ocBOrderItem.getQty();
//            }
            refunds.add(this.getRefundItem(ocBOrderItem, qty, applyPrice, orderRefund, refundItem, user));
        }
        if (refunds.size() != ocBOrderItems.size()) {
            List<OcBOrderItem> gift = this.getGift(ocBOrderItems);
            if (gift.size() == (ocBOrderItems.size() - refunds.size())) {
                for (OcBOrderItem ocBOrderItem : gift) {
                    refunds.add(this.getRefundItem(ocBOrderItem, ocBOrderItem.getPrice(), BigDecimal.ZERO, orderRefund, null, user));
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(" ========== getReturnRefunds refunds:{}", JSONObject.toJSONString(refunds));
        }
        return refunds;
    }

    private OcBReturnOrderRefund geFullOcBReturnOrderRefund(IpBAlibabaAscpOrderRefund orderRefund, User user,
                                                            OcBOrder ocBOrder, OcBOrderItem orderItem, BigDecimal qty) {
        //发货信息
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        BigDecimal refundAmt = orderItem.getRealAmt().divide(orderItem.getQty(),
                4, BigDecimal.ROUND_HALF_UP).multiply(qty);
        //
        returnOrderRefund.setAmtRefund(refundAmt);
        returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        //申请数量
        returnOrderRefund.setQtyRefund(qty);
        Integer returnStatus = orderRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        returnOrderRefund.setRefundStatus(status);
//        returnOrderRefund.setAmtPtRefund(orderRefund.getRefundAmount());
        //商品名称
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPrice(orderItem.getPriceList());
        //1 qty_can_refund 购买数量 合计所有明细qty
        returnOrderRefund.setQtyCanRefund(orderItem.getQty());
        //商品单价
        returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
        //国标码
        returnOrderRefund.setBarcode(orderItem.getBarcode());
        //修改人用户名
        returnOrderRefund.setModifierename(orderItem.getModifierename());
        //商品规格
        returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setOid(orderItem.getOoid());
        //条码id
        returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
        returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
        returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
        returnOrderRefund.setGiftType(orderItem.getGiftType());
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCProId(orderItem.getPsCProId());
        //颜色尺寸
        returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
        returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

        returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
        returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
        returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
        returnOrderRefund.setSex(orderItem.getSex());
        returnOrderRefund.setRefundBillNo(orderRefund.getBizOrderCode());
        returnOrderRefund.setOcBOrderId(ocBOrder.getId());
        returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                4, BigDecimal.ROUND_HALF_UP));
        returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");
        OperateUserUtils.saveOperator(returnOrderRefund, user);
        return returnOrderRefund;
    }

    /**
     * 过滤出赠品
     *
     * @return
     */
    private List<OcBOrderItem> getGift(List<OcBOrderItem> items) {
        List<OcBOrderItem> gifts = Lists.newArrayList();
        if (CollectionUtils.isEmpty(items)) {
            return gifts;
        }
        for (OcBOrderItem item : items) {
            if (Objects.equals(item.getIsGift(), 1) && item.getProType() != SkuType.NO_SPLIT_COMBINE) {
                gifts.add(item);
            }
        }
        return gifts;
    }

    /**
     * 获取单价
     *
     * @param refundItem
     * @return
     */
    private BigDecimal getOcBorderItemSinglePrice(IpBAlibabaAscpOrderRefundItem refundItem, OcBOrderItem ocBOrderItem) {
        if (Objects.equals(refundItem.getSubOrderCode(), ocBOrderItem.getOoid())) {
            return ocBOrderItem.getRealAmt().divide(ocBOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    /**
     * @param orderItem 订单详情
     * @param qty       申请数量
     * @param price     退单金额
     * @param user      操作人
     * @return
     */
    private OcBReturnOrderRefund getRefundItem(OcBOrderItem orderItem, BigDecimal qty, BigDecimal price,
                                               IpBAlibabaAscpOrderRefund orderRefund, IpBAlibabaAscpOrderRefundItem refundItem,
                                               User user) {
        log.info("===== qty:{},price:{},", qty, price);
        if (qty.compareTo(orderItem.getQty()) > 0) {
            qty = orderItem.getQty();
        }
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        // 退单金额
        returnOrderRefund.setAmtRefund(price);
        // 申请数量
        returnOrderRefund.setQtyRefund(qty);
        //商品名称
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());

        returnOrderRefund.setPrice(orderItem.getPriceList());
        //1 qty_can_refund 购买数量 合计所有明细qty
        returnOrderRefund.setQtyCanRefund(orderItem.getQty());
        //商品单价
        returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
        //国标码
        returnOrderRefund.setBarcode(orderItem.getBarcode());
        //修改人用户名
        returnOrderRefund.setModifierename(orderItem.getModifierename());
        //商品规格
        returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setOid(orderItem.getOoid());
        //条码id
        returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCProId(orderItem.getPsCProId());
        //颜色尺寸
        returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
        returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

        returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
        returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
        returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
        returnOrderRefund.setSex(orderItem.getSex());
        returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
        returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
        returnOrderRefund.setGiftType(orderItem.getGiftType());
        returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
        returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                4, BigDecimal.ROUND_HALF_UP));
        returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");

        if (Objects.nonNull(refundItem) && Objects.nonNull(refundItem.getSubOrderCode()) && Objects.equals(orderItem.getOoid(), refundItem.getSubOrderCode())) {
            Integer returnStatus = orderRefund.getReturnStatus();
            String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
            returnOrderRefund.setRefundStatus(status);
            returnOrderRefund.setRefundBillNo(orderRefund.getBizOrderCode());
//            returnOrderRefund.setAmtPtRefund(orderRefund.getRefundAmount());
        }
        OperateUserUtils.saveOperator(returnOrderRefund, user);
        return returnOrderRefund;
    }

    /**
     * 通过平台单号查询是否生成过退换货单
     *
     * @param tid
     * @return
     */
    private boolean isGift(String tid) {
        //通过平台单号查询ES
        Set<Long> ids = ES4ReturnOrder.findIdByTid(tid);
        //是否存在退换货订单
        return CollectionUtils.isNotEmpty(ids);
    }

    public void invokeAscpInStorageFeedbackCmdService(OcBReturnOrder rtn, List<OcBReturnOrderRefund> rtnORfnList, OcBOrder ocBOrder) {
        if (PlatFormEnum.ALIBABAASCP.getCode().equals(ocBOrder.getPlatform())) {
            // 调用猫超回告接口
            ValueHolderV14 vh;
            try {
                IpBAlibabaAscpOrderRefund ipBAlibabaAscpOrderRefund = ipBAlibabaAscpOrderRefundMapper.selectAlibabaAscpOrderRefundByBizOrderCode(rtn.getOrigSourceCode());

                InStorageFeedbackModel model = new InStorageFeedbackModel();
                model.setSupplierId(rtn.getSettleSupplierCode());
                model.setBizOrderCode(rtn.getOrigSourceCode());
//                ERP业务编码
                model.setOutBizId(ipBAlibabaAscpOrderRefund.getOutBizId());
                model.setInstorageTime(rtn.getReturnTime());
                model.setTmsOrderCode(rtn.getLogisticsCode());
                model.setTmsServiceCode(rtn.getCpCLogisticsEcode());
                model.setStoreCode(rtn.getStoreCode());

                List<InStorageFeedbackModel.Orderitems> orderitemsList = new ArrayList<>();
                for (OcBReturnOrderRefund item : rtnORfnList) {
                    InStorageFeedbackModel.Orderitems orderitem = new InStorageFeedbackModel.Orderitems();
                    // 逆向履约子单号  子订单Id
                    orderitem.setSubOrderCode(item.getOid());
                    // 货品id
                    orderitem.setScItemId(item.getPsCProEcode());
                    // 入库数量
                    long qtyIn = item.getQtyIn() == null ? 0L : Long.valueOf(item.getQtyIn().toString());
                    orderitem.setActualReceivedQuantity(qtyIn);
                    // 货品未收货总数量
                    orderitem.setActualLackQuantity(0L);

                    List<InStorageFeedbackModel.Instoragedetails> instoragedetailList = new ArrayList<>();
                    InStorageFeedbackModel.Instoragedetails instoragedetails = new InStorageFeedbackModel.Instoragedetails();
                    instoragedetails.setReceivedQuantity(qtyIn);
                    // 库存类型:101=残次品;1=正品  业务 默认正品
                    instoragedetails.setStorageType("1");
                    instoragedetailList.add(instoragedetails);
                    orderitem.setInstorageDetails(instoragedetailList);
                    orderitemsList.add(orderitem);
                }

                model.setOrderItems(orderitemsList);
                //店铺id
                Long cpCShopId = ocBOrder.getCpCShopId();
                CpShop cpShop = cpRpcService.selectShopById(cpCShopId);
                // 商家 信息
                InStorageFeedbackModel.Receiverinfo receiverinfo = new InStorageFeedbackModel.Receiverinfo();
                receiverinfo.setReceiverPhone(cpShop.getSellerPhone());
                receiverinfo.setReceiverMobile(cpShop.getSellerPhone());
                receiverinfo.setReceiverName(cpShop.getSellerName());
                receiverinfo.setReceiverAddress(cpShop.getSellerAddress());
                String receiveTown = StringUtils.isBlank(ipBAlibabaAscpOrderRefund.getReceiveTown()) ? "无" : ipBAlibabaAscpOrderRefund.getReceiveTown();
                receiverinfo.setReceiveTown(receiveTown);
                receiverinfo.setReceiverArea(cpShop.getSellerArea());
                receiverinfo.setReceiverCity(cpShop.getSellerCity());
                receiverinfo.setReceiverProvince(cpShop.getSellerProvince());
                String receiveCountry = StringUtils.isBlank(ipBAlibabaAscpOrderRefund.getReceiverCountry()) ? "中国" : ipBAlibabaAscpOrderRefund.getReceiverCountry();
                receiverinfo.setReceiverCountry(receiveCountry);
                String receiverZipCode = StringUtils.isBlank(cpShop.getSellerZip()) ? "无" : cpShop.getSellerZip();
                receiverinfo.setReceiverZipCode(receiverZipCode);
                model.setReceiverInfo(receiverinfo);

                // 消费者 信息
                InStorageFeedbackModel.Senderinfo senderinfo = new InStorageFeedbackModel.Senderinfo();
                senderinfo.setSenderPhone(rtn.getReceivePhone());
                senderinfo.setSenderMobile(rtn.getReceiveMobile());
                senderinfo.setSenderName(rtn.getReceiveName());
                senderinfo.setSenderAddress(rtn.getReceiveName());
                // 收件方镇
                String senderTown = StringUtils.isBlank(ipBAlibabaAscpOrderRefund.getSenderTown()) ? "无" : ipBAlibabaAscpOrderRefund.getSenderTown();
                senderinfo.setSenderTown(senderTown);
                senderinfo.setSenderArea(rtn.getReceiverAreaName());
                senderinfo.setSenderCity(rtn.getReceiverCityName());
                senderinfo.setSenderProvince(rtn.getReceiverProvinceName());
                String senderCountry = StringUtils.isBlank(ipBAlibabaAscpOrderRefund.getSenderTown()) ? "中国" : ipBAlibabaAscpOrderRefund.getSenderTown();
                senderinfo.setSenderCountry(senderCountry);
                String sellerZip = StringUtils.isBlank(rtn.getReceiveZip()) ? "无" : rtn.getReceiveZip();
                senderinfo.setSenderZipCode(sellerZip);
                model.setSenderInfo(senderinfo);

                vh = ipRpcService.inStorageFeedback(model, rtn.getSellerNick());
                //记录日志信息。Finish 标记结束
                if (log.isDebugEnabled()) {
                    log.debug(this.getClass().getName() + ".invokeAscpInStorageFeedbackCmdService bizOrderCode:{},Return Result={}", rtn.getOrigOrderId(), JSONObject.parseObject(JSONObject.toJSONString(vh)));
                }
            } catch (NDSException e) {
                vh = new ValueHolderV14();
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage(e.getMessage()));
            }
        }
    }

}
