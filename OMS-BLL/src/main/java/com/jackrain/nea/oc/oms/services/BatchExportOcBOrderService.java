package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.BasePermission;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.QueryOrderListResult;
import com.jackrain.nea.oc.oms.nums.excel.OcBOrderAllModel;
import com.jackrain.nea.oc.oms.permission.OcOrderAuthorityMgtService;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.excel.XlsIoHelper;
import com.jackrain.nea.util.excel.XlsIoModel;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * 订单导出
 *
 * @author: xiWen.z
 * create at: 2019/7/31 0031
 */
@Slf4j
@Component
public class BatchExportOcBOrderService {

    /**
     * 最大导出数量限制
     */
    private final static String EXPORT_MAX_ROW_NUM_LIMIT = "export_max_row_num_limit";

    /**
     * 最大导出数量默认值
     */
    private final static int DEF_EXPORT_MAX_ROW_NUM_LIMIT = 1000000;

    @Autowired
    private XlsIoHelper xlsIoHelper;

    @Autowired
    private OcOrderAuthorityMgtService ocOrderAuthorityMgtService;

    @Autowired
    private ChannelTypeService channelTypeService;

    @Autowired
    private OcBOrderListQueryService ocBOrderListQueryService;

    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    private QueryOrderListService queryOrderListService;

    /**
     * 导出.主程
     */
    public ValueHolderV14 mainProgram(String param, User usr, UserPermission usrPem, Boolean withTag) {
        /**
         * 0. 出参
         */
        ValueHolderV14<QueryOrderListResult> vh = new ValueHolderV14<>();
        QueryOrderListResult qolr = new QueryOrderListResult();
        vh.setData(qolr);

        /**
         * 1. 校验
         */
        ValueHolderV14<JSONObject> v1 = validateParam(param, usr, vh);
        if (v1.getCode() == ResultCode.FAIL) {
            return v1;
        }

        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("零售发货单导出");
        asyncTaskBody.setTaskType("导出");
        //任务开始
        asyncTaskManager.beforeExecute(usr, asyncTaskBody);
        return asyncExport(asyncTaskBody, "零售发货单", v1, usr, usrPem, withTag);
//        return xlsIoHelper.export("全渠道订单", list, xlsIoModel, esIdList, usr, oec, usrPem);

    }


    /**
     * @param v
     */
    private void prepareConvertData(XlsIoModel v) {
        Map<String, Map<String, Map<Object, Object>>> csm = v.getConvertSource();
        if (csm == null) {
            csm = new HashMap<>();
            v.setConvertSource(csm);
        }
        // field-get table.oc_b_order
        Map<String, Map<Object, Object>> m1 = csm.get("oc_b_order");
        if (m1 == null) {
            m1 = new HashMap<>();
            csm.put("oc_b_order", m1);
        }

        Map<Object, Object> orderType = OrderTypeEnum.convertAllToHashVal();
        Map<Object, Object> platform = PlatFormEnum.convertAllToHashVal();
        Map<Object, Object> payType = OcBorderListEnums.PayTypeEnum.convertAllToHashVal();
        Map<Object, Object> wmsCancel = OcBorderListEnums.WmsCanceStatusEnum.convertAllToHashVal();
        Map<Object, Object> returnStatus = OcBorderListEnums.ReturnStatusEnum.convertAllToHashVal();
        Map<Object, Object> orderStatus = OcOrderCheckBoxEnum.convertAllToHashVal();

        m1.put("return_status", returnStatus);
        m1.put("wms_cancel_status", wmsCancel);
        m1.put("order_type", orderType);
        m1.put("pay_type", payType);
        m1.put("platform", platform);
        m1.put("order_status", orderStatus);

        // field-get table.oc_b_order
        Map<String, Map<Object, Object>> m2 = csm.get("oc_b_order_item");
        /*if (m2 == null) {
            m2 = new HashMap<>();
            csm.put("oc_b_order_item", m2);
        }*/

        Map<Object, Object> refundStatus = OcBorderListEnums.OcBOrderRefundStatus.convertAllToHashVal();

        Map<Object, Object> isGift = new HashMap<>();
        isGift.put(0, "否");
        isGift.put(1, "是");
        Map<Object, Object> isLackStock = new HashMap<>();
        isLackStock.put(0, "否");
        isLackStock.put(1, "是");

        m1.put("refund_status", refundStatus);
        m1.put("is_gift", isGift);
        m1.put("is_lackstock", isLackStock);

    }

    /**
     * 1.1 校验
     *
     * @param searchJSONString
     * @return
     */
    public ValueHolderV14 validateParam(String searchJSONString, User usr, ValueHolderV14<QueryOrderListResult> vh) {
        if (usr == null || searchJSONString == null || searchJSONString.trim().length() == OcBOrderConst.IS_STATUS_IN) {
            return reBuildVh("参数丢失", usr, false);
        }
        JSONObject jsnObj;
        try {
            jsnObj = JSON.parseObject(searchJSONString);
        } catch (Exception e) {
            return reBuildVh("参数解析异常", usr, false);
        }
        String slcId = "idList";
        if (jsnObj.containsKey(slcId)) {
            ValueHolderV14<JSONObject> v = new ValueHolderV14();
            v.setCode(ResultCode.SUCCESS);
            v.setData(jsnObj);
            return v;
        }
        final String pageKey = "page";
        JSONObject o = jsnObj.getJSONObject(pageKey);
        if (o == null) {
            return reBuildVh("分页信息丢失", usr, false);
        }
        final String sizeKey = "pageSize";
        final String numKey = "pageNum";
        Integer num = o.getInteger(numKey);
        Integer size = o.getInteger(sizeKey);
        if (num == null || size == null || num < OcBOrderConst.IS_STATUS_IY || size < OcBOrderConst.IS_STATUS_IY) {
            return reBuildVh("分页信息不正确", usr, false);
        }
        if (num > OcBOrderConst.IS_STATUS_IN) {
            vh.getData().setPageNum(num);
        }
        int exportSize;
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        exportSize = config.getProperty("exportExcel.exportSize", 100000);
        if (exportSize == 0) {
            exportSize = 100000;
        }
        if (size > OcBOrderConst.IS_STATUS_IN) {
            if (size > exportSize) {
                size = exportSize;
            }
            vh.getData().setPageSize(size);
        }


        queryOrderListService.convertSalesCenterToSalesDept(jsnObj);

        ValueHolderV14<JSONObject> v = new ValueHolderV14();
        v.setCode(ResultCode.SUCCESS);
        jsnObj.put("SearchForId", true);
        v.setData(jsnObj);
        return v;

    }

    /**
     * 调用订单列表查询
     *
     * @param o
     * @param loginUser
     * @param pem
     * @return
     */
    private List<Long> invokeListQuery4Es(JSONObject o, User loginUser, UserPermission pem) {
        if (o.containsKey("idList")) {
            JSONArray idList = o.getJSONArray("idList");
            if (idList != null && idList.size() > OcBOrderConst.IS_STATUS_IN) {
                PropertiesConf pconf = ApplicationContextHandle.getBean(PropertiesConf.class);
                int exportMaxRowNumLimit = pconf.getProperty(EXPORT_MAX_ROW_NUM_LIMIT, DEF_EXPORT_MAX_ROW_NUM_LIMIT);
                if (idList.size() > exportMaxRowNumLimit) {
                    log.error(LogUtil.format("BatchExportOcBOrderService.invokeListQuery4Es Error: 导出数据超出最大行数限制"));
                    throw new NDSException("导出数据超出最大行数限制,最大行数：" + exportMaxRowNumLimit);
                }
                return JSON.parseArray(idList.toJSONString(), Long.class);
            }
            return new ArrayList<>();
        }
        ValueHolderV14<QueryOrderListResult> vh = ocBOrderListQueryService.queryOrderList(o.toString(), loginUser, pem, true);
        if (!vh.isOK()) {
            return new ArrayList<>();
        }
        QueryOrderListResult data = vh.getData();
        if (data == null) {
            return new ArrayList<>();
        }
        List<Long> ids = data.getIds();
        if (CollectionUtils.isNotEmpty(ids)) {
            return ids;
        }
        return new ArrayList<>();
    }

    /**
     * 1.2 初始结果信息
     */

    private ValueHolderV14 reBuildVh(String s, User usr, boolean b) {
        ValueHolderV14 v = new ValueHolderV14();
        if (b) {
            v.setCode(ResultCode.SUCCESS);
        } else {
            v.setCode(ResultCode.FAIL);
        }
        if (usr != null) {
            s = Resources.getMessage(s, usr.getLocale());
        }
        v.setMessage(s);
        QueryOrderListResult queryOrderListResult = new QueryOrderListResult();
        queryOrderListResult.setPageNum(OcBOrderConst.PAGE_NUM);
        queryOrderListResult.setPageSize(OcBOrderConst.PAGE_SIZE);
        queryOrderListResult.setTotalNum(OcBOrderConst.IS_STATUS_IN);
        queryOrderListResult.setTotalSize(OcBOrderConst.ORDER_ID);
        v.setData(queryOrderListResult);
        return v;
    }

    /**
     * es.ids
     * 2.0 merge
     */
    public List<Long> mainSearchIdsOnEs(JSONObject o, ValueHolderV14<QueryOrderListResult> vh, UserPermission usrPem) {

        if (o.containsKey("idList")) {
            JSONArray idList = o.getJSONArray("idList");
            if (idList != null && idList.size() > OcBOrderConst.IS_STATUS_IN) {
                return JSON.parseArray(idList.toJSONString(), Long.class);
            }
            return new ArrayList<>();
        }

        final String forwardSort = "asc";
        final String odrFldKey = "name";
        final String odrFldV1 = "ORDER_DATE";


        // 1.0 排序.备参
        JSONArray odrJay = new JSONArray();
        JSONObject odrJo = new JSONObject();
        odrJo.put(odrFldKey, odrFldV1);
        odrJo.put(forwardSort, false);
        odrJay.add(odrJo);

        JSONObject cdtKey = new JSONObject();
        JSONObject chdKey = new JSONObject();
        JSONObject fltKey = new JSONObject();

        // 2.0 分页.查询.起始获取
        int startIndex = getSplitPageStartIndex(vh);
        int size = vh.getData().getPageSize();

        JSONObject statusJo = o.getJSONObject("status");
        JSONArray labelJas = o.getJSONArray("label");
        JSONArray queryInfoJo = o.getJSONArray("queryInfo");
        JSONArray highSearch = o.getJSONArray("highSearch");

        // 2.0 标签部分
        sqlIntelliSearchTag(labelJas, cdtKey);

        // 3.0 多下拉智能查询部分
        if (queryInfoJo != null) {
            int infoLen = queryInfoJo.size();
            for (int i = 0; i < infoLen; i++) {
                JSONObject tmpJo = queryInfoJo.getJSONObject(i);
                String type = tmpJo.getString("type");
                String queryName = tmpJo.getString("queryName");
                if (type != null && "Select".equals(type)) {
                    JSONArray selectBox = tmpJo.getJSONArray("list");
                    sqlIntelliSearchCtrHandler(selectBox, queryName, cdtKey);
                } else if (type != null && "date".equals(type)) {
                    String value = tmpJo.getString("value");
                    sqlHighSearchDateCtrHandler(value, queryName, fltKey);
                } else if (type != null && "Input".equals(type)) {
                    String value = tmpJo.getString("value");
                    sqlHighSearchTextCtrHandler(value, queryName, cdtKey, fltKey, chdKey);
                } else if (type != null && "DropDownSelectFilter".equals(type)) {
                    JSONArray selectDrop = tmpJo.getJSONArray("list");
                    intelliSearchDropDownRef(selectDrop, queryName, cdtKey);
                }
            }
        }

        // 4.0 高级搜索部分
        if (highSearch != null) {
            for (int i = 0; i < highSearch.size(); i++) {
                JSONObject tmpJo = highSearch.getJSONObject(i);
                if (tmpJo != null) {
                    String type = tmpJo.getString("type");
                    String queryName = tmpJo.getString("queryName");
                    if ("Select".equalsIgnoreCase(type)) {
                        String sltChk = tmpJo.getString("value");
                        highSearchSelectCtrlHandler(sltChk, queryName, cdtKey);
                    } else if ("date".equalsIgnoreCase(type)) {
                        String dateStr = tmpJo.getString("value");
                        sqlHighSearchDateCtrHandler(dateStr, queryName, fltKey);
                    } else if ("Input".equalsIgnoreCase(type)) {
                        String txt = tmpJo.getString("value");
                        sqlHighSearchTextCtrHandler(txt, queryName, cdtKey, fltKey, chdKey);
                    }
                }
            }
        }
        // 5.0 ES:合并状态- 状态按钮部分
        buttonStatuJoinList(statusJo, cdtKey);
        allStatuHandler(cdtKey);

        filterSearchCondition(usrPem, cdtKey);
        // 6.0 es.search
        JSONObject esOrderJo = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, cdtKey, fltKey, odrJay,
                chdKey, size, startIndex, new String[]{"ID"});
        return splitOrderIds(esOrderJo, vh);
    }

    /**
     * 2.1 es.splitPage.startIndex
     */
    private int getSplitPageStartIndex(ValueHolderV14<QueryOrderListResult> vh) {
        return (vh.getData().getPageNum() - OcBOrderConst.IS_STATUS_IY) * (vh.getData().getPageSize());
    }

    /**
     * 2.2 es.cdt.tag
     */
    private void sqlIntelliSearchTag(JSONArray oA, JSONObject es) {
        if (oA != null) {
            int n = oA.size();
            for (int i = 0; i < n; i++) {
                JSONObject o = oA.getJSONObject(i);
                if (o != null) {
                    es.put(o.getString("key"), o.getString("val"));
                }
            }
        }
    }

    /**
     * 2.3.0 es.cdt.select
     */
    private void sqlIntelliSearchCtrHandler(JSONArray selectBoxs, String queryName, JSONObject es) {
        if (selectBoxs == null || selectBoxs.size() < OcBOrderConst.IS_STATUS_IY) {
            return;
        }
        int n = selectBoxs.size();
        JSONArray statuAry = new JSONArray();
        for (int i = 0; i < n; i++) {
            JSONObject o = selectBoxs.getJSONObject(i);
            if (o != null && "ORDER_STATUS".equalsIgnoreCase(queryName)) {
                statuAry.add(o.getString("value"));
            }
        }
        es.put("ORDER_STATUS", statuAry);
    }

    /**
     * 2.3.1 es.cdt.select.dropDown
     */
    private void intelliSearchDropDownRef(JSONArray ary, String n, JSONObject e) {
        if (ary == null || ary.size() < OcBOrderConst.IS_STATUS_IY) {
            return;
        }
        if (n.equals("CP_C_SHOP_TITLE")) {
            n = "CP_C_SHOP_ID";
        } else if (n.equals("CP_C_PHY_WAREHOUSE_ENAME")) {
            n = "CP_C_PHY_WAREHOUSE_ID";
        } else if (n.equals("CP_C_LOGISTICS_ENAME")) {
            n = "CP_C_LOGISTICS_ID";
        }
        JSONArray fk = new JSONArray();
        int l = ary.size();
        for (int i = 0; i < l; i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o != null) {
                fk.add(o.getString("ID"));
            }
        }
        e.put(n, fk);
    }

    /**
     * 2.3.2 es.cdt.highSelect.ref | group
     */
    private void highSearchSelectCtrlHandler(String valString, String queryName, JSONObject es) {
        if (valString == null || valString.trim().length() == 0) {
            return;
        }
        //ES:渠道类型条件
        if ("CHANNEL_TYPE_ID".equalsIgnoreCase(queryName) && StringUtils.isNotEmpty(valString)) {
            channelTypeService.channelTypeSelectHandler(valString, es);
            return;
        }
        String[] valArys = valString.split(",");
        int len = valArys.length;
        JSONArray selectAry = new JSONArray();
        for (int i = 0; i < len; i++) {
            String v = valArys[i];
            if (v == null || v.trim().length() < 1) {
                continue;
            }
            selectAry.add(v);
        }
        es.put(queryName, selectAry);
    }

    /**
     * 2.4.0 es.cdt.filter.range.time
     */
    private void sqlHighSearchDateCtrHandler(String dateStr, String queryName, JSONObject es) {
        if (dateStr == null || dateStr.trim().length() < 1) {
            return;
        }
        String esDateStr = dealStringConvertToTime(dateStr);
        if (esDateStr != null && esDateStr.length() > 0) {
            es.put(queryName, esDateStr);
        }
    }

    /**
     * 2.4.1 es.cdt.filter.range.time.convert
     */
    private String dealStringConvertToTime(String str) {
        String dtStr = null;
        String[] sAry = str.split("~");
        // 严格格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (sAry[0] == null || sAry[1] == null || sAry[0].length() < OcBOrderConst.DATE_MIN_LENGTH
                || sAry[1].length() < OcBOrderConst.DATE_MIN_LENGTH) {
            return null;
        }
        try {
            Long startDt = sdf.parse(sAry[0]).getTime();
            Long endDt = sdf.parse(sAry[1]).getTime();
            dtStr = startDt + "~" + endDt;
        } catch (ParseException e) {
            log.error("OcBOrderListQueryServer.dealStringConvertToTIme Error " + e.getMessage());
        }
        return dtStr;
    }

    /**
     * 2.5.0 es.cdt.highSearch.text
     */
    private boolean sqlHighSearchTextCtrHandler(String v, String n, JSONObject es, JSONObject fKey,
                                                JSONObject childKeys) {
        if (StringUtils.isBlank(v) || "~".equals(v.trim())) {
            return false;
        }

        v = v.trim();
        if (v.contains(OcBOrderConst.ORDER_COMMA) && OcBOrderConst.OCB_ORDER_LIST_BATCH_INCLUDEKEYS.contains(n)) {
            dealBatchQuery(OcBOrderConst.ORDER_COMMA, n, v, es);
            return true;
        }
        if (v.contains(OcBOrderConst.ORDER_COMMA_CN) && OcBOrderConst.OCB_ORDER_LIST_BATCH_INCLUDEKEYS.contains(n)) {
            dealBatchQuery(OcBOrderConst.ORDER_COMMA_CN, n, v, es);
            return true;
        }

        if ("ID".equals(n)) {
            es.put(n, v);
            return true;
        }
        if (OcBOrderConst.OCB_ORDER_QTY_ALL.equals(n) || OcBOrderConst.OCB_ORDER_ORDER_AMT.equals(n)) {
            boolean flag = Pattern.matches(OcBOrderConst.OCB_ORDER_NUMBER_REGES, v);
            if (flag) {
                fKey.put(n, splitAndJoinHighText(v));
                return true;
            }
            return false;
        }
        if (OcBOrderConst.OCB_ORDER_ITEM_PSC_SKUECODE.equals(n)) {
            childKeys.put(n, v + "*");
            return true;
        }
        es.put(n, v + "*");
        return true;
    }

    /**
     * 2.5.1 es.cdt.highSearch.text.batch
     */
    private void dealBatchQuery(String c, String n, String v, JSONObject es) {
        JSONArray cnJay = new JSONArray();
        String[] cnAry = v.split(c);
        int l = cnAry.length;
        for (int i = 0; i < l; i++) {
            if (cnAry[i] == null) {
                continue;
            }
            String s = cnAry[i].trim();
            if (s.length() < OcBOrderConst.IS_STATUS_IY) {
                continue;
            }
            cnJay.add(s);
        }
        if (cnJay.size() > OcBOrderConst.IS_STATUS_IN) {
            es.put(n, cnJay);
        }
    }

    /**
     * 2.5.3 es.cdt.highSearch.filter.text.range
     */
    private String splitAndJoinHighText(String v) {
        String[] ary = v.split("~");
        if (ary.length > 1 && StringUtils.isNotBlank(ary[0]) && StringUtils.isNotBlank(ary[1])) {
            BigDecimal v0 = new BigDecimal(ary[0]);
            BigDecimal v1 = new BigDecimal(ary[1]);
            if (v0.compareTo(v1) > 0) {
                return ary[1] + "~" + ary[0];
            }
        }
        return v;
    }

    /**
     * 2.6.0 es.cdt.status
     */
    private void buttonStatuJoinList(JSONObject buttonWherekey, JSONObject whereKeyJo) {
        if (buttonWherekey == null) {
            return;
        }
        String statusStr = buttonWherekey.getString("value");
        JSONArray tmpStatusList = whereKeyJo.getJSONArray("ORDER_STATUS");
        JSONArray reSetJa = new JSONArray();

        statusLbl:
        if (null != tmpStatusList && StringUtils.isNotBlank(statusStr)) {
            // 下拉没值, tab值 加入
            if (tmpStatusList.size() < OcBOrderConst.IS_STATUS_IY) {
                splitComposeStatus(statusStr, reSetJa);
                whereKeyJo.put("ORDER_STATUS", reSetJa);
                break statusLbl;
            }

            // tab 包含全部
            if (OcBOrderConst.STATUS_TAB_ALL.equals(statusStr)) {
                if (tmpStatusList.contains(OcBOrderConst.STATUS_TAB_ALL)) {
                    reSetJa.add(OcBOrderConst.STATUS_TAB_ALL);
                    whereKeyJo.put("ORDER_STATUS", reSetJa);
                } else {
                    whereKeyJo.put("ORDER_STATUS", tmpStatusList);
                }
            } else {
                // 组合性tab
                if (statusStr.contains(OcBOrderConst.ORDER_COMMA)) {
                    splitComposeStatus(statusStr, reSetJa);
                    dealComposeStatus(tmpStatusList, reSetJa, whereKeyJo);
                } else if (tmpStatusList.contains(statusStr)
                        || (tmpStatusList.contains(OcBOrderConst.STATUS_TAB_ALL))) {
                    reSetJa.add(statusStr);
                    whereKeyJo.put("ORDER_STATUS", reSetJa);
                } else {
                    reSetJa.add(OcBOrderConst.ORDER_STATUS_NONE);
                    whereKeyJo.put("ORDER_STATUS", reSetJa);
                }
            }
        } else if (tmpStatusList == null && StringUtils.isNotBlank(statusStr)) {
            // 下拉为null, tab有值
            splitComposeStatus(statusStr, reSetJa);
            whereKeyJo.put("ORDER_STATUS", reSetJa);
        }
    }

    /**
     * 2.6.1 es.cdt.status.compose
     */
    private void splitComposeStatus(String stu, JSONArray ary) {
        if (stu.contains(OcBOrderConst.ORDER_COMMA)) {
            String[] stuAry = stu.split(OcBOrderConst.ORDER_COMMA);
            for (String s : stuAry) {
                if (StringUtils.isNotBlank(s)) {
                    ary.add(s);
                }
            }
        } else {
            ary.add(stu);
        }
    }

    /**
     * 2.6.2 es.cdt.status.compose.deal
     */
    private void dealComposeStatus(JSONArray tmpStatusList, JSONArray reSetJa, JSONObject whereKeyJo) {
        if (tmpStatusList.contains(OcBOrderConst.IS_STATUS_SN)) {
            whereKeyJo.put("ORDER_STATUS", reSetJa);
        } else {
            JSONArray ary = new JSONArray();
            for (int i = 0; i < reSetJa.size(); i++) {
                String stu = reSetJa.getString(i);
                if (tmpStatusList.contains(stu)) {
                    ary.add(stu);
                }
            }
            if (ary.size() > OcBOrderConst.IS_STATUS_IN) {
                whereKeyJo.put("ORDER_STATUS", ary);
            } else {
                ary.add(OcBOrderConst.ORDER_STATUS_NONE);
                whereKeyJo.put("ORDER_STATUS", ary);
            }
        }
    }

    /**
     * 2.6.3 es.cdt.status.allStatus.judge
     */
    private void allStatuHandler(JSONObject es) {
        if (es != null) {
            JSONArray arys = es.getJSONArray("ORDER_STATUS");
            if (arys != null) {
                int len = arys.size();
                for (int i = 0; i < len; i++) {
                    if (arys.getInteger(i) != null && OcBOrderConst.ORDER_STATUS_ALL.equals(arys.getInteger(i))) {
                        es.put("ORDER_STATUS", OcOrderCheckBoxEnum.joinAllStatusVal());
                        break;
                    }
                }
            }
        }
    }

    /**
     * 2.7.0 es.result.ids
     */
    private List<Long> splitOrderIds(JSONObject esJo, ValueHolderV14<QueryOrderListResult> vh) {
        recordLog(" #splitOrderIds.esJo==>: " + JSON.toJSONString(esJo));
        List<Long> list = new ArrayList<>();
        if (esJo == null) {
            return new ArrayList<>();
        }
        final String esRstTotKey = "total";
        final String esRstDatKey = "data";
        final String esRstIdKey = "ID";
        Long totalCount = esJo.getLong(esRstTotKey);
        Long totalPage = 0L;
        if (totalCount > OcBOrderConst.IS_STATUS_IN) {
            long l = totalCount % (vh.getData().getPageSize());
            if (l == OcBOrderConst.IS_STATUS_IN) {
                totalPage = totalCount / (vh.getData().getPageSize());
            } else {
                totalPage = (totalCount / (vh.getData().getPageSize())) + OcBOrderConst.IS_STATUS_IY;
            }
        }
        vh.getData().setTotalSize(totalCount);
        vh.getData().setTotalNum(totalPage.intValue());

        JSONArray ary = esJo.getJSONArray(esRstDatKey);
        if (ary == null) {
            return new ArrayList<>();
        }
        for (int i = 0; i < ary.size(); i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            Long id = o.getLong(esRstIdKey);
            if (id == null) {
                continue;
            }
            list.add(id);
        }
        return list;
    }

    /**
     * debug级别日志
     *
     * @param msg String
     */
    private void recordLog(String msg) {
        if (log.isDebugEnabled()) {
            log.debug("###" + this.getClass().getName() + msg);
        }
    }

    /**
     * sd
     *
     * @param pem
     * @param wKey
     */
    private void filterSearchCondition(UserPermission pem, JSONObject wKey) {
        keyLabel:
        if (pem != null) {
            Map<String, BasePermission> baseMap = pem.getBasePermission();
            if (baseMap == null) {
                break keyLabel;
            }
            BasePermission basePem = baseMap.get("OC_B_ORDER");
            // 表没配
            if (basePem == null) {
                return;
            }
            List<String> cdtList = new ArrayList<>();
            cdtList.add("CP_C_SHOP_ID");
            cdtList.add("CP_C_PHY_WAREHOUSE_ID");
            ocOrderAuthorityMgtService.recombinationSearchCdt(cdtList, basePem, wKey);
        }
    }

    //异步处理导出
    public ValueHolderV14 asyncExport(AsyncTaskBody asyncTaskBody, String msg, ValueHolderV14<JSONObject> v1, User user, UserPermission usrPem, Boolean withTag) {
        ValueHolderV14 vh = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {
            try {
                if (log.isDebugEnabled()) {
                    log.debug(" asyncExport,finalIdList v1:{}", JSONObject.toJSONString(v1));
                }
                List<Long> finalIdList = esSearch(v1, user, usrPem);
                if (CollectionUtils.isEmpty(finalIdList)) {
                    throw new NDSException(Resources.getMessage("未查询到数据"));
                }
                if (log.isDebugEnabled()) {
                    log.debug(" asyncExport,finalIdList finalIdList:{}", JSONObject.toJSONString(finalIdList));
                }
                OcBorderExportConvert oec = new OcBorderExportConvert();
                XlsIoModel xlsIoModel = new XlsIoModel();
                prepareConvertData(xlsIoModel);
                List<Class> list = new ArrayList<>();
                list.add(OcBOrderAllModel.class);
                //主线任务
                ValueHolderV14 valueHolder = xlsIoHelper.export(msg, list, xlsIoModel, finalIdList, user, oec, usrPem, withTag);
                retMap.put("code", valueHolder.getCode());
                retMap.put("data", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());
                //任务完成
                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception e) {
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导出异常：" + e.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        vh.setCode(ResultCode.SUCCESS);
        vh.setData(asyncTaskBody.getId());
        vh.setMessage(Resources.getMessage("零售发货导出任务开始！详情请在我的任务查看！"));
        return vh;
    }


    /**
     * @param v1
     * @param usr
     * @param usrPem
     */
    private List<Long> esSearch(ValueHolderV14<JSONObject> v1, User usr, UserPermission usrPem) {
        long l0 = System.currentTimeMillis();

        long l1 = System.currentTimeMillis();

        List<Long> esIdList = invokeListQuery4Es(v1.getData(), usr, usrPem);
        long l2 = System.currentTimeMillis();
        recordLog("###: Validate Param Time:==> " + (l1 - l0) + " ###: Es Search Param Time:=====>" + (l2 - l1));
        return esIdList;
    }
}
