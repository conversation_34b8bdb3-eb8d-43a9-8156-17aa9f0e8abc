package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.StCDropshipBasePriceStrategyDetailMapper;
import com.jackrain.nea.oc.oms.mapper.StCDropshipBasePriceStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.StCDropshipBasePriceStrategy;
import com.jackrain.nea.oc.oms.model.table.StCDropshipBasePriceStrategyDetail;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 一件代发客户基价策略查询服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class StCDropshipBasePriceQueryServiceImpl implements StCDropshipBasePriceQueryService {

    @Autowired
    private StCDropshipBasePriceStrategyMapper strategyMapper;

    @Autowired
    private StCDropshipBasePriceStrategyDetailMapper detailMapper;

    @Override
    public ValueHolder getBasePrice(JSONObject obj, User user) {
        ValueHolder vh = new ValueHolder();
        
        try {
            // 参数校验
            Long shopId = obj.getLong("CP_C_SHOP_ID");
            String skuCode = obj.getString("PS_C_SKU_ECODE");
            
            if (shopId == null) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", Resources.getMessage("店铺ID不能为空!", user.getLocale()));
                return vh;
            }
            
            if (StringUtils.isBlank(skuCode)) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", Resources.getMessage("SKU编码不能为空!", user.getLocale()));
                return vh;
            }
            
            // 查询店铺的已审核策略
            StCDropshipBasePriceStrategy strategy = strategyMapper.selectByShopId(shopId);
            if (strategy == null) {
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "该店铺未配置一件代发基价策略");
                vh.put("data", createEmptyResult(shopId, skuCode));
                return vh;
            }
            
            if (strategy.getAuditStatus() == null || strategy.getAuditStatus() != 1) {
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "该店铺的一件代发基价策略未审核");
                vh.put("data", createEmptyResult(shopId, skuCode));
                return vh;
            }
            
            // 查询SKU基价
            StCDropshipBasePriceStrategyDetail detail = detailMapper.selectByStrategyIdAndSkuCode(strategy.getId(), skuCode);
            
            JSONObject result = new JSONObject();
            result.put("CP_C_SHOP_ID", shopId);
            result.put("PS_C_SKU_ECODE", skuCode);
            result.put("STRATEGY_ID", strategy.getId());
            result.put("AUDIT_STATUS", strategy.getAuditStatus());
            
            if (detail != null && detail.getBasePrice() != null) {
                result.put("BASE_PRICE", detail.getBasePrice());
                result.put("HAS_BASE_PRICE", true);
                vh.put("message", "查询成功");
            } else {
                result.put("BASE_PRICE", BigDecimal.ZERO);
                result.put("HAS_BASE_PRICE", false);
                vh.put("message", "该SKU未配置基价");
            }
            
            vh.put("code", ResultCode.SUCCESS);
            vh.put("data", result);
            
            if (log.isDebugEnabled()) {
                log.debug("查询一件代发基价成功，店铺ID={}，SKU={}，基价={}", 
                        shopId, skuCode, result.get("BASE_PRICE"));
            }
            
        } catch (Exception e) {
            log.error("查询一件代发基价失败", e);
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("查询失败：" + e.getMessage(), user.getLocale()));
        }
        
        return vh;
    }
    
    /**
     * 创建空结果
     */
    private JSONObject createEmptyResult(Long shopId, String skuCode) {
        JSONObject result = new JSONObject();
        result.put("CP_C_SHOP_ID", shopId);
        result.put("PS_C_SKU_ECODE", skuCode);
        result.put("STRATEGY_ID", null);
        result.put("STRATEGY_NAME", null);
        result.put("AUDIT_STATUS", null);
        result.put("BASE_PRICE", BigDecimal.ZERO);
        result.put("HAS_BASE_PRICE", false);
        return result;
    }
}
