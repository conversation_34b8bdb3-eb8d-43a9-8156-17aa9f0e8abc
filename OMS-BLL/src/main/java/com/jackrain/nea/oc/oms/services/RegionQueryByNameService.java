package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCRegionRelation;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-03-25 16:48
 */
@Slf4j
@Component
public class RegionQueryByNameService {

    @Autowired
    private CpRpcService cpRpcService;

    public ValueHolderV14<CpCRegionRelation> queryByName(JSONObject obj) {
        ValueHolderV14<CpCRegionRelation> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        if (!obj.containsKey("provinceName") || !obj.containsKey("cityName") || !obj.containsKey("areaName")) {
            vh.setMessage("省/市/区的名称不能为空");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        String provinceName = obj.get("provinceName").toString();
        String cityName = obj.get("cityName").toString();
        String areaName = obj.get("areaName").toString();
        CpCRegionRelation cpCRegionRelation = cpRpcService.selectRegionRelationByProvinceCityArea(provinceName,
                cityName, areaName);
        vh.setData(cpCRegionRelation);
        return vh;
    }
}
