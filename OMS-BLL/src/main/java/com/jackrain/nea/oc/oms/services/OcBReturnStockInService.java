package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoAdjustSaveRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.eventListener.SmsSendEvent;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.SmsSendStrategyInfo;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.LogStepRecord;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.AddOrderNoticeAndOutService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 退货入库单.入库服务.入库结果单生成结束
 *
 * @author: xiWen.z
 * create at: 2019/7/27 0027
 */
@Slf4j
@Component
public class OcBReturnStockInService {

    @Autowired
    private OcBRefundInMapper ocBRefundInMapper;

    @Autowired
    private OcBRefundInProductItemMapper ocBRefundInProItemMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    private OcBRefundInLogMapper ocBRefundInLogMapper;

    @Autowired
    AddOrderNoticeAndOutService addOrderNoticeAndOutService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 正常流程标识
     */
    private final int normalProcess = 3;

    /**
     * 入库
     *
     * @param relation 退货入库单DRM
     * @param usr      当前操作用户
     * @return 入库结果
     * @throws NDSException NDS
     */
    public ValueHolderV14<Set<Long>> returnWarehousing(RefundInRelation relation, User usr) throws NDSException {


        // 1. 校验
        validateParams(relation, usr);

        // 2. 无名件处理
        ValueHolderV14 vh = namelessItemProcess(relation, usr);
        if (vh.getCode() != normalProcess) {
            return vh;
        }

        // 3. 匹配
        List<OcBReturnOrderRelation> rtnRltList = new ArrayList<>();
        Map<Long, List<OcBRefundInProductItem>> rfnItemMap = new HashMap<>();
        boolean dealResult = matchHandler(relation, rtnRltList, rfnItemMap);
        if (!dealResult || rtnRltList.size() < OcBOrderConst.IS_STATUS_IY) {
            return valueHolderResult("未匹配到有效商品明细, 不允许入库", false, usr);
        }

        // 4.0 生成入库通知单
        ValueHolderV14 noticeVh = invokeAddInNoticeService(rfnItemMap, rtnRltList, relation.getRefundIn(), usr);
        if (noticeVh.getCode() < ResultCode.SUCCESS) {
            if (rfnItemMap.size() > ResultCode.SUCCESS) {
                portionUpdateStatus(rfnItemMap, relation.getRefundIn(), usr);
            }
            ValueHolderV14 portionResult = valueHolderResult("生成入库通知单失败[部分]: " + noticeVh.getMessage(),
                    false, usr);
            portionResult.setData(noticeVh.getData());
            return portionResult;
        }

        // 5.1 匹配状态, 调整单.备参
        List<OcBRefundInProductItem> realSkuMatchList = new ArrayList<>();
        List<OcBRefundInProductItem> realSkuUnMatchList = new ArrayList<>();
        List<OcBRefundInProductItem> noneRealSKuUnMatchList = new ArrayList<>();
        statisticsMatchCount(relation, realSkuMatchList, realSkuUnMatchList, noneRealSKuUnMatchList);

        // 5.2 调用调整单
        invokeAdjustmentService(relation.getRefundIn(), realSkuMatchList, realSkuUnMatchList, noneRealSKuUnMatchList, usr);

        // 6.0 更新
        updateDataMethods(relation.getRefundIn(), relation.getItems(), usr);
        vh = valueHolderResult("入库成功", true, usr);
        vh.setData(noticeVh.getData());
        return vh;
    }


    /**
     * 返回信息
     *
     * @param msg       输出信息
     * @param flag      结果
     * @param loginUser 登录用户
     * @return vh
     */
    private ValueHolderV14 valueHolderResult(String msg, boolean flag, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (flag) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        }
        return vh;
    }

    /**
     * sql in查询条件拼接
     *
     * @param set set
     * @return String
     */
    private String sqlHandler(Set<Long> set) {

        StringBuilder sb = new StringBuilder();
        boolean i = false;
        for (Long e : set) {
            if (i) {
                sb.append(",");
            }
            sb.append("'");
            sb.append(e);
            sb.append("'");
            if (!i) {
                i = true;
            }
        }
        return sb.toString();
    }

    /**
     * 查询退货单信息
     * table3: return_order
     *
     * @param sqlReturnOrderIds 退货单id集合
     * @return List<OcBOrderReturnRefundProductRelation>
     */
    private List<OcBReturnOrder> searchReturnOrder(String sqlReturnOrderIds) {

        List<OcBReturnOrder> returnOrderRltList = null;
        if (StringUtils.isNotBlank(sqlReturnOrderIds)) {
            try {
                returnOrderRltList = ocBReturnOrderMapper.queryOcBReturnOrderListByIds(sqlReturnOrderIds,
                        OcBOrderConst.IS_ACTIVE_YES);
            } catch (Exception e) {
                log.error(LogUtil.format("OcBReturnStockInService.查询退单.error.Ids-{}, ex-{} "), sqlReturnOrderIds,
                        Throwables.getStackTraceAsString(e));
            }
        } else {
            logDebug(LogUtil.format("OcBReturnStockInService.查询退单.SQLParameters isBlank~! Cannot Search ReturnOrder " +
                    "Info"));
        }
        return returnOrderRltList;
    }


    /**
     * 退货商品查询结果
     *
     * @param sqlReturnOrderIds 退货主表id集合
     * @return List<OcBReturnOrderRefund>
     */
    private List<OcBReturnOrderRefund> searchReturnOrderRefund(String sqlReturnOrderIds) {

        try {
            return ocBReturnOrderRefundMapper.queryReturnOrderRefundByoIds(
                    sqlReturnOrderIds, OcBOrderConst.IS_ACTIVE_YES);
        } catch (Exception e) {
            log.error(LogUtil.format("OcBReturnStockInService.退货商品查询.error.FK-{}.ex-{}"), sqlReturnOrderIds,
                    Throwables.getStackTraceAsString(e));
            return null;
        }

    }

    /**
     * 入库通知单部分成功.更新状态
     *
     * @param rfnItmMap 部分入库明细
     * @param inRfnIn   入库单.部分
     * @param usr       User
     */
    private void portionUpdateStatus(Map<Long, List<OcBRefundInProductItem>> rfnItmMap, OcBRefundIn inRfnIn, User usr) {
        Set<Long> keys = rfnItmMap.keySet();
        List<OcBRefundInProductItem> items = new ArrayList<>();
        for (Long k : keys) {
            List<OcBRefundInProductItem> tmpList = rfnItmMap.get(k);
            if (tmpList != null) {
                items.addAll(tmpList);
            }
        }
        if (items.size() > ResultCode.SUCCESS) {
            inRfnIn.setMatchStatus(OcBOrderConst.REFUND_IN_MATCHSTATUS_PORTION);
            updateDataMethods(inRfnIn, items, usr);
        }
    }

    /**
     * 数据操作
     *
     * @param ocBRefundIn  入库单
     * @param inRfnProList 入库明细
     * @param usr          用户
     */
    private void updateDataMethods(OcBRefundIn ocBRefundIn, List<OcBRefundInProductItem> inRfnProList, User usr) {

        logDebug(LogUtil.format("updateDataMethods-: OcBRefundIn-{} . OcBRefundInProductItem-{}"),
                JSON.toJSONString(ocBRefundIn),
                JSON.toJSONString(inRfnProList));
        try {
            for (OcBRefundInProductItem x : inRfnProList) {
                ocBRefundInProItemMapper.updateRefundIsMatchSecondary(x);
            }
            ocBRefundInMapper.updateOcBRefundInMatchStatus(ocBRefundIn.getMatchStatus(), ocBRefundIn.getId());
            writeReturnOderLog(ocBRefundIn, inRfnProList, usr);
        } catch (Exception e) {
            log.error(LogUtil.format("updateDataMethods.更新异常{}"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 日志.退货入库
     *
     * @param ocBRefundIn  入库单
     * @param inRfnProList 入库明细
     * @param usr          用户
     */
    private void writeReturnOderLog(OcBRefundIn ocBRefundIn, List<OcBRefundInProductItem> inRfnProList, User usr) {
        logDebug(LogUtil.format("writeReturnOderLog: 入库结果单,日志记录->: 开始记录"));
        OcBRefundInLog rfnLog = new OcBRefundInLog();
        rfnLog.setLogtype("退货单入库");
        setLogMsg(usr, rfnLog);

        Map<String, BigDecimal> cMap = new HashMap<>();
        for (OcBRefundInProductItem o : inRfnProList) {
            BigDecimal qty = o.getQty();
            if (qty == null || qty.compareTo(BigDecimal.ZERO) < OcBOrderConst.IS_STATUS_IY) {
                continue;
            }
            if (IsMatchEnum.MATCHED.getVal().equals(o.getIsMatch())) {
                statisticInQty(cMap, o.getPsCSkuEcode(), o.getQty());
            }
        }
        StringBuilder sb = new StringBuilder();
        Set<Map.Entry<String, BigDecimal>> es = cMap.entrySet();
        for (Map.Entry<String, BigDecimal> e : es) {
            sb.append(",").append(e.getKey()).append("(")
                    .append(e.getValue().setScale(0, BigDecimal.ROUND_DOWN)).append(")");
        }
        String contentString = "入库结果单入库完成[入库]，入库条码: " + (sb.substring(OcBOrderConst.IS_STATUS_IY));
        Long id = Tools.getSequence("oc_b_refund_in_log");
        rfnLog.setId(id);
        rfnLog.setLogmessage(contentString);
        rfnLog.setOmsonlineorderid(ocBRefundIn.getId());
        ocBRefundInLogMapper.insert(rfnLog);
    }

    /**
     * log日志设置
     *
     * @param usr    操作用户
     * @param rfnLog 日志Model
     */
    private void setLogMsg(User usr, OcBRefundInLog rfnLog) {
        rfnLog.setUsername(usr.getName());
        rfnLog.setOwnerid(Long.valueOf(usr.getId()));
        rfnLog.setOwnername(usr.getName());
        rfnLog.setOwnerename(usr.getEname());
        rfnLog.setModifiername(usr.getName());
        rfnLog.setModifierename(usr.getEname());
        rfnLog.setCreationdate(new Date());
        rfnLog.setModifierid(Long.valueOf(usr.getId()));
        rfnLog.setModifieddate(new Date());
        rfnLog.setAdOrgId(Long.valueOf(usr.getOrgId()));
        rfnLog.setAdClientId(Long.valueOf(usr.getClientId()));
        rfnLog.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
        rfnLog.setIpaddress(usr.getLastloginip());
    }

    /**
     * @param map   Map
     * @param ecode String
     * @param qty   BigDecimal
     */
    private void statisticInQty(Map<String, BigDecimal> map, String ecode, BigDecimal qty) {
        Set<String> keys = map.keySet();
        if (keys.contains(ecode)) {
            BigDecimal inQty = map.get(ecode);
            BigDecimal tmpQty = inQty.add(qty);
            map.put(ecode, tmpQty);
        } else {
            map.put(ecode, qty);
        }
    }

    /**
     * 参数校验
     *
     * @param relation 退货入库单DM
     * @param usr      User
     */
    private void validateParams(RefundInRelation relation, User usr) {

        AssertUtil.notNull(usr, "用户信息丢失, 不允许入库");

        AssertUtil.notNull(relation, "入库参数为空,不允许入库");

        AssertUtil.assertException(!relation.isCurrentParamValid(), "入库单或入库商品明细为空, 不允许入库");

        AssertUtil.notContainNullVal(relation.getItems(), "入库商品中,数据出现空值, 不允许入库");

        AssertUtil.notNull(relation.getRefundIn().getInStoreId(), "入库单逻辑仓不能为空");

        logDebug(LogUtil.format("OcBReturnStockInService.validateParams.param#{}"), JSON.toJSONString(relation));

    }

    /**
     * 查询退单,明细信息
     *
     * @param relation   入库单
     * @param rtnRltList 退换货关系
     * @param fnItmMp    记录生成入库通知单入库明细
     * @return T?F
     */
    private boolean matchHandler(RefundInRelation relation, List<OcBReturnOrderRelation> rtnRltList,
                                 Map<Long, List<OcBRefundInProductItem>> fnItmMp) {

        // 1.0 根据退单号分组明细
        Set<Long> rtnIdSet = new HashSet<>();
        List<OcBRefundInProductItem> unMatchItems = new ArrayList<>();
        for (OcBRefundInProductItem item : relation.getItems()) {
            boolean isValid = item.getOcBReturnOrderId() != null
                    && OcBOrderConst.REFUND_IN_MATCHSTATUS_UN == item.getIsMatch()
                    && NumUtil.gtZero(item.getQty());
            if (isValid) {
                unMatchItems.add(item);
                rtnIdSet.add(item.getOcBReturnOrderId());
            }
        }
        AssertUtil.assertException(CollectionUtils.isEmpty(rtnIdSet), "入库商品根据退单编号分组, 未筛选到有效[未匹配]数据");

        // 2.0 查询退单信息
        String rtnIds = sqlHandler(rtnIdSet);
        List<OcBReturnOrder> shRtnOdrList = searchReturnOrder(rtnIds);
        AssertUtil.notEmpty(shRtnOdrList, "未查询到退换货单信息");

        // 2.2 查询退换货明细
        List<OcBReturnOrderRefund> rtnOdrRfnItems = searchReturnOrderRefund(rtnIds);
        AssertUtil.notEmpty(rtnOdrRfnItems, "未查询退换货明细");

        // 3.0 校验入库数量
        if (rtnOdrRfnItems.size() > 0) {
            boolean vli = validateQtyInSameOrder(rtnOdrRfnItems, unMatchItems);
            AssertUtil.assertException(!vli, "同单号入库数量大于申请数量");
        }

        // 4.0 根据单号,匹配明细
        return match4RefundIn(shRtnOdrList, rtnOdrRfnItems, unMatchItems, rtnRltList, fnItmMp);
    }

    /**
     * @param shRtnOdrList      OcBReturnOrder
     * @param rtnOdrRfnItems    OcBReturnOrderRefund
     * @param filterRfnProItems OcBRefundInProductItem
     * @param rtnRltList        rtnRltList
     * @param fnItmMp           OcBRefundInProductItem map key: rtnId , value: list rfnItem
     * @return bool t/f
     */
    private boolean match4RefundIn(List<OcBReturnOrder> shRtnOdrList, List<OcBReturnOrderRefund> rtnOdrRfnItems,
                                   List<OcBRefundInProductItem> filterRfnProItems,
                                   List<OcBReturnOrderRelation> rtnRltList,
                                   Map<Long, List<OcBRefundInProductItem>> fnItmMp) {
//        退单项先根据退单id分组，再根据sku合并退单项
        Map<Long, List<OcBReturnOrderRefund>> rtnOdrRfnItemsMap = rtnOdrRfnItems.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getOcBReturnOrderId));
        rtnOdrRfnItemsMap.keySet().forEach(e->{
            List<OcBReturnOrderRefund> collect = rtnOdrRfnItemsMap.get(e).stream().collect(Collectors.toMap(OcBReturnOrderRefund::getPsCSkuId, o -> o, (o1, o2) -> {
                int total = initBigDecimal2Int(o1.getQtyRefund()) + initBigDecimal2Int(o2.getQtyRefund());
                o1.setQtyRefund(BigDecimal.valueOf(total));
                return o1;
            })).values().stream().collect(Collectors.toList());
            rtnOdrRfnItemsMap.put(e,collect);
        });
//        根据退单入库id和skuid 分组
        Map<String, List<OcBRefundInProductItem>> filterRfnProItemsMap = filterRfnProItems.stream().collect(Collectors.groupingBy(e -> e.getOcBReturnOrderId() +"_"+ e.getPsCSkuId()));
//        Collections.sort(rtnOdrRfnItems, (s1, s2) -> s2.getQtyRefund().compareTo(s1.getQtyRefund()));
//        Collections.sort(filterRfnProItems, (s1, s2) -> s2.getQty().compareTo(s1.getQty()));
        for (OcBReturnOrder x : shRtnOdrList) {
            if (x == null) {
                logDebug(LogUtil.format("match4RefundIn: 根据单号,整合明细->无数据"));
                continue;
            }
            boolean xg = false;
            List<OcBReturnOrderRefund> item = new ArrayList<>();
            List<OcBRefundInProductItem> rfnItem = new ArrayList<>();
            List<OcBReturnOrderRefund> ocBReturnOrderRefunds = rtnOdrRfnItemsMap.get(x.getId());
            if(CollectionUtils.isEmpty(ocBReturnOrderRefunds)){
                continue;
            }
            for (OcBReturnOrderRefund r : ocBReturnOrderRefunds) {
                if (r == null) {
                    continue;
                }
                Long rSKu = r.getPsCSkuId();
                if (rSKu == null) {
                    continue;
                }
//                if (!x.getId().equals(r.getOcBReturnOrderId())) {
//                    continue;
//                }
                boolean g = false;
                BigDecimal tQty = r.getQtyRefund();
                if (tQty == null || tQty.compareTo(BigDecimal.ZERO) < OcBOrderConst.IS_STATUS_IY) {
                    continue;
                }
                List<OcBRefundInProductItem> ocBRefundInProductItems = filterRfnProItemsMap.get(x.getId() +"_"+ rSKu);
                if(CollectionUtils.isEmpty(ocBRefundInProductItems)){
                    continue;
                }
                for (OcBRefundInProductItem p : ocBRefundInProductItems) {
//                    if (!x.getId().equals(p.getOcBReturnOrderId())) {
//                        continue;
//                    }
                    if (IsMatchEnum.MATCHED.getVal().equals(p.getIsMatch())) {
                        continue;
                    }
                    BigDecimal qty = p.getQty();
                    if (qty == null || qty.compareTo(BigDecimal.ZERO) < OcBOrderConst.IS_STATUS_IY) {
                        continue;
                    }
//                    if (!rSKu.equals(p.getPsCSkuId())) {
//                        continue;
//                    }
                    BigDecimal qtyIn = r.getQtyIn() == null ? BigDecimal.ZERO : r.getQtyIn();
                    BigDecimal dynQty = qtyIn.add(qty);
                    if (tQty.compareTo(dynQty) < OcBOrderConst.IS_STATUS_IN) {
                        continue;
                    }
                    r.setQtyIn(dynQty);
                    p.setIsMatch(IsMatchEnum.MATCHED.getVal());
                    p.setIsGenInOrder(IsGenInEnum.YES.integer());
                    rfnItem.add(p);
                    g = true;
                }
                if (g) {
                    item.add(r);
                    xg = true;
                }
            }

            if (xg) {
                OcBReturnOrderRelation rtnOdrRlt = new OcBReturnOrderRelation();
                rtnOdrRlt.setReturnOrderInfo(x);
                rtnOdrRlt.setOrderRefundList(item);
                rtnRltList.add(rtnOdrRlt);
                // record rfn item which was matched
                fnItmMp.put(x.getId(), rfnItem);
            }
        }
        if (rtnRltList != null && rtnRltList.size() > 0) {
            logDebug(LogUtil.format("match4RefundIn: 同单号,匹配明细-{}"), JSON.toJSONString(rtnRltList));
            return true;
        }
        logDebug(LogUtil.format("match4RefundIn: 同单号,未匹配到明细"));
        return false;
    }

    /**
     * 同单号入库数量验证
     *
     * @param rtnItems 退单商品
     * @param rfnItems 入库商品
     * @return 是否合法
     */
    private boolean validateQtyInSameOrder(List<OcBReturnOrderRefund> rtnItems, List<OcBRefundInProductItem> rfnItems) {

        Map<Long, List<OcBReturnOrderRefund>> returnItemMap = rtnItems.stream()
                .collect(Collectors.groupingBy(OcBReturnOrderRefund::getOcBReturnOrderId, Collectors.toList()));
        Map<Long, List<OcBRefundInProductItem>> proItemMap = rfnItems.stream()
                .collect(Collectors.groupingBy(OcBRefundInProductItem::getOcBReturnOrderId, Collectors.toList()));
        if (returnItemMap != null) {
            Set<Long> rtnIds = returnItemMap.keySet();
            for (Long id : rtnIds) {
                Map<Long, BigDecimal> rtnMap = new HashMap<>();
                Map<Long, BigDecimal> rfnMap = new HashMap<>();
                List<OcBReturnOrderRefund> ocBReturnOrderRefunds = returnItemMap.get(id);
                List<OcBRefundInProductItem> ocBRefundInProductItems = proItemMap.get(id);
                calRtnSameSkuQty(ocBReturnOrderRefunds, rtnMap);
                calRfnSameSkuQty(ocBRefundInProductItems, rfnMap);
                boolean b = rtnCntCompare2RfnCnt(rtnMap, rfnMap);
                if (!b) {
                    logDebug(LogUtil.format("OcBReturnStockInService.同退单号,验证入库数量是否大于申请数量: False. RtnOdrId=", id));
                    return false;
                }
            }
        } else {
            logDebug(LogUtil.format("OcBReturnStockInService.同退单号,验证入库数量是否大于申请数量: 分组退单明细时为Null"));
            return false;
        }
        return true;
    }

    /**
     * @param rtnItems 退单明细
     * @param rtnMap   sku数量记录
     */
    private void calRtnSameSkuQty(List<OcBReturnOrderRefund> rtnItems, Map<Long, BigDecimal> rtnMap) {

        for (OcBReturnOrderRefund rtnItem : rtnItems) {
            if (rtnItem != null) {
                if (rtnItem.getPsCSkuId() == null) {
                    continue;
                }
                BigDecimal refund = rtnItem.getQtyRefund();
                if (refund == null) {
                    refund = BigDecimal.ZERO;
                }
                calAddSameSkuQty(rtnMap, rtnItem.getPsCSkuId(), refund);
            }
        }
    }

    /**
     * @param rfnItems 入库明细
     * @param rfnMap   sku数量
     */
    private void calRfnSameSkuQty(List<OcBRefundInProductItem> rfnItems, Map<Long, BigDecimal> rfnMap) {
        for (OcBRefundInProductItem rfnItem : rfnItems) {
            if (rfnItem != null) {
                if (IsMatchEnum.MATCHED.getVal().equals(rfnItem.getIsMatch())) {
                    continue;
                }
                if (rfnItem.getPsCSkuId() == null) {
                    continue;
                }
                BigDecimal inQty = rfnItem.getQty();
                if (inQty == null) {
                    continue;
                }
                calAddSameSkuQty(rfnMap, rfnItem.getPsCSkuId(), inQty);
            }
        }
    }


    /**
     * @param m sku map
     * @param k sku
     * @param q qty
     */
    private void calAddSameSkuQty(Map<Long, BigDecimal> m, Long k, BigDecimal q) {
        Set<Long> skuKs = m.keySet();
        if (skuKs.contains(k)) {
            BigDecimal n = m.get(k).add(q);
            m.put(k, n);
        } else {
            m.put(k, q);
        }
    }

    /**
     * @param rtnSku return refund item map
     * @param rfnSku refund in item map
     * @return bool
     */
    private boolean rtnCntCompare2RfnCnt(Map<Long, BigDecimal> rtnSku, Map<Long, BigDecimal> rfnSku) {
        Set<Long> rtnKeys = rtnSku.keySet();
        for (Long k : rtnKeys) {
            BigDecimal rtnCnt = rtnSku.get(k);
            BigDecimal rfnCnt = rfnSku.get(k);
            if (rfnCnt == null) {
                continue;
            }
            if (rtnCnt.compareTo(rfnCnt) < 0) {
                logDebug(LogUtil.format("OcBReturnStockInService.数量异常.RtnQty-{}.RfnQty-{},SkuId=", k), rtnCnt, rfnCnt);
                return false;
            }
        }
        return true;
    }

    /**
     * 调用新增入库通知单服务
     *
     * @param rfnItemMap 记录匹配入库
     * @param rtnRltList 退换货关系
     * @param inRfnIn    入库单
     * @param usr        user
     * @return T?F
     */
    private ValueHolderV14 invokeAddInNoticeService(Map<Long, List<OcBRefundInProductItem>> rfnItemMap,
                                                    List<OcBReturnOrderRelation> rtnRltList,
                                                    OcBRefundIn inRfnIn, User usr) {
        ValueHolderV14 vh;
        boolean g = true;
        Set<Long> successList = new HashSet<>();
        StringBuilder sb = new StringBuilder();
        for (OcBReturnOrderRelation e : rtnRltList) {
            Long id = e.getReturnOrderInfo().getId();
            try {
                vh = addOrderNoticeAndOutService.addNoticeAndOutOrderNew(e, inRfnIn, usr);
                if (vh != null) {
                    if (ResultCode.SUCCESS == vh.getCode()) {
                        logDebug(LogUtil.format("OcBReturnStockInService.调用新增入库通知单服. Result Success-"
                                + "Code-{}. MSG-{},ReturnOrderId=", id), vh.getCode(), vh.getMessage());
                        successList.add(id);
                    } else {
                        g = false;
                        rfnItemMap.remove(id);
                        sb.append("; 退单编号: ").append(id).append(", 新增入库通知单失败; 状态码: ").append(vh.getCode())
                                .append(", 失败信息: ").append(vh.getMessage());
                        log.error(LogUtil.format("OcBReturnStockInService.调用新增入库通知单服务. Result Fail-"
                                + "Code-{}. MSG-{},ReturnOrderId=", id), vh.getCode(), vh.getMessage());
                    }
                } else {
                    g = false;
                    rfnItemMap.remove(id);
                    sb.append("; 退单编号: ").append(id).append(", 新增入库通知单失败; 失败信息: 返回值异常,返回值为: Null");
                    log.error(LogUtil.format("OcBReturnStockInService.invokeAddOrderNoticeAndOutService Error-调用新增入库通知单"
                            + ",返回值异常, 返回值为: Null,ReturnOrderId=", id));
                }
            } catch (Exception ex) {
                g = false;
                rfnItemMap.remove(id);
                sb.append("; 退单编号: ").append(id).append(", 新增入库通知单异常; 异常信息: ")
                        .append(ExceptionUtil.getMessage(ex));
                log.error(LogUtil.format("OcBReturnStockInService. 调用新增入库通知单服务发生异常,error：{}, ReturnOrderId=", id),
                        Throwables.getStackTraceAsString(ex));
            }
        }
        vh = new ValueHolderV14();
        if (!g) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(sb.substring(1));
        } else {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("Success");
        }
        vh.setData(successList);
        return vh;
    }

    /**
     * 调整单统计
     *
     * @param relation               RefundInRelation
     * @param realSkuMatchList       OcBRefundInProductItem
     * @param realSkuUnMatchList     OcBRefundInProductItem
     * @param noneRealSKuUnMatchList OcBRefundInProductItem
     */
    private void statisticsMatchCount(RefundInRelation relation,
                                      List<OcBRefundInProductItem> realSkuMatchList,
                                      List<OcBRefundInProductItem> realSkuUnMatchList,
                                      List<OcBRefundInProductItem> noneRealSKuUnMatchList) {
        int mahStuCt = 0;
        for (OcBRefundInProductItem p : relation.getItems()) {

            boolean isValidQty = NumUtil.gtZero(p.getQty());
            if (!isValidQty) {
                continue;
            }
            if (IsMatchEnum.MATCHED.getVal().equals(p.getIsMatch())) {
                mahStuCt++;
                if (p.getRealSkuId() != null && IsGenAdjustEnum.NO.integer().equals(p.getIsGenAdjust())) {
                    realSkuMatchList.add(p);
                }
            } else {
                Long refRtnId = p.getOcBReturnOrderId();
                if (refRtnId != null && refRtnId > OcBOrderConst.IS_STATUS_IN) {
                    continue;
                }
                if (!(IsGenAdjustEnum.NO.integer().equals(p.getIsGenAdjust()))) {
                    continue;
                }
                if (p.getRealSkuId() != null) {
                    realSkuUnMatchList.add(p);
                } else {
                    noneRealSKuUnMatchList.add(p);
                }

            }
        }
        if (mahStuCt == relation.getItems().size()) {
            relation.getRefundIn().setMatchStatus(OcBOrderConst.REFUND_IN_MATCHSTATUS_ALL);
        } else if (mahStuCt > OcBOrderConst.IS_STATUS_IN && mahStuCt < relation.getItems().size()) {
            relation.getRefundIn().setMatchStatus(OcBOrderConst.REFUND_IN_MATCHSTATUS_PORTION);
        }

    }

    /**
     * 调用调整单服务
     *
     * @param inRfnIn                OcBRefundIn
     * @param realSkuMatchList       List<OcBRefundInProductItem>
     * @param realSkuUnMatchList     List<OcBRefundInProductItem>
     * @param noneRealSKuUnMatchList List<OcBRefundInProductItem>
     * @param usr                    User
     */
    private void invokeAdjustmentService(OcBRefundIn inRfnIn, List<OcBRefundInProductItem> realSkuMatchList,
                                         List<OcBRefundInProductItem> realSkuUnMatchList,
                                         List<OcBRefundInProductItem> noneRealSKuUnMatchList, User usr) {
        if (!realSkuMatchList.isEmpty()) {
            revokeAdjustmentSrv(inRfnIn, realSkuMatchList, usr);
            logDebug(LogUtil.format("错发调整, 调用结束"));
        }

        if (!noneRealSKuUnMatchList.isEmpty() || !realSkuUnMatchList.isEmpty()) {
            boolean b = revokeUnMatchAdjustment(inRfnIn, noneRealSKuUnMatchList, realSkuUnMatchList, usr);
            if (b) {
                if (!noneRealSKuUnMatchList.isEmpty()) {
                    noneRealSKuUnMatchList.forEach(x -> x.setIsGenAdjust(IsGenAdjustEnum.YES.integer()));
                }
                if (!realSkuUnMatchList.isEmpty()) {
                    realSkuUnMatchList.forEach(x -> x.setIsGenAdjust(IsGenAdjustEnum.YES.integer()));
                }
            }
        }

    }

    /**
     * RPC调用新增调整单服务. 正负调整
     *
     * @param rfn      入库单
     * @param proItems 入库明细
     * @param user     用户
     */
    private void revokeAdjustmentSrv(OcBRefundIn rfn, List<OcBRefundInProductItem> proItems, User user) {

        try {
            SgOmsStoAdjustSaveRequest sgAdjustReq = new SgOmsStoAdjustSaveRequest();
            //单据类型（1 正常调整 2 异常调整）
            sgAdjustReq.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
            //调整性质 错发调整
            sgAdjustReq.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_MISTAKE_ADJUSTMENT);
            //来源单据类型 零售退货
            sgAdjustReq.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);
            sgAdjustReq.setCpCStoreEcode(rfn.getInStoreEcode());
            sgAdjustReq.setRequestType(SgConstants.REQUEST_TYPE_SAVE_AND_SUBMIT);//请求类型
            sgAdjustReq.setBillDate(rfn.getSubmitDate());
            Map<Long, List<SgOmsStoAdjustItemSaveRequest>> sgMap = new HashMap<>();
            Map<Long, List<OcBRefundInProductItem>> spliceMap = proItems.stream()
                    .collect(Collectors.groupingBy(OcBRefundInProductItem::getOcBReturnOrderId, Collectors.toList()));
            if (spliceMap == null) {
                return;
            }
            Set<Long> spliceKeys = spliceMap.keySet();
            for (Long key : spliceKeys) {
                List<OcBRefundInProductItem> proItemList = spliceMap.get(key);
                for (OcBRefundInProductItem x : proItemList) {
                    SgOmsStoAdjustItemSaveRequest itm = dealProSkuAndPro(x, x.getPsCSkuId(), x.getPsCSkuEcode(), false);
                    itm.setQty(OcBOrderConst.CONST_MINUS_ONE.multiply(x.getQty()));
                    SgOmsStoAdjustItemSaveRequest itm2 = dealProSkuAndPro(x, x.getRealSkuId(), x.getRealSkuEcode(), true);
                    spliceAdjustByRtnOdrId(sgMap, x, itm);
                    spliceAdjustByRtnOdrId(sgMap, x, itm2);
                }
                sgAdjustReq.setSourceBillId(key);
                sgAdjustReq.setSourceBillNo(key.toString());
                sgAdjustReq.setItems(sgMap.get(key));
                sgAdjustReq.setLoginUser(user);
                boolean b = sgRpcService.addStockAdjustment(sgAdjustReq).isOK();
                if (b) {
                    spliceMap.get(key).forEach(c -> c.setIsGenWroAdjust(IsGenWroAdjustEnum.YES.integer()));
                } else {
                    log.error(LogUtil.format("错发调整.调用失败, 入库单id/退单id=", rfn.getId(), key));
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("错发调整.调用生成调整单服务异常-{}"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * RPC调用新增调整单服务.无头件调整
     *
     * @param rfn                    OcBRefundIn
     * @param noneRealSKuUnMatchList OcBRefundInProductItem
     * @param realSkuUnMatchList     OcBRefundInProductItem
     * @param user                   User
     * @return bool
     */
    private boolean revokeUnMatchAdjustment(OcBRefundIn rfn, List<OcBRefundInProductItem> noneRealSKuUnMatchList,
                                            List<OcBRefundInProductItem> realSkuUnMatchList, User user) {

        try {
            SgOmsStoAdjustSaveRequest sgAdjustReq = new SgOmsStoAdjustSaveRequest();
            sgAdjustReq.setSourceBillId(rfn.getId());
            sgAdjustReq.setSourceBillNo(rfn.getId().toString());
            //sgAdjustReq.setSourceBillType(SgPhyAdjustConstantsIF.SOURCE_BILL_TYPE_REF_IN);
            sgAdjustReq.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
            sgAdjustReq.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_NO_SOURCE_IN);
            sgAdjustReq.setRequestType(SgConstants.REQUEST_TYPE_SAVE_AND_SUBMIT);//请求类型 创建并提交
            sgAdjustReq.setCpCStoreEcode(rfn.getInStoreEcode());
            sgAdjustReq.setBillDate(rfn.getSubmitDate());
            List<SgOmsStoAdjustItemSaveRequest> items = new ArrayList<>();
            List<Long> preList = new ArrayList<>();
            for (OcBRefundInProductItem p1 : noneRealSKuUnMatchList) {
                SgOmsStoAdjustItemSaveRequest itm = dealProSkuAndPro(p1, p1.getPsCSkuId(), p1.getPsCSkuEcode(), false);
                items.add(itm);
                preList.add(p1.getId());
            }
            for (OcBRefundInProductItem p2 : realSkuUnMatchList) {
                SgOmsStoAdjustItemSaveRequest itm = dealProSkuAndPro(p2, p2.getRealSkuId(), p2.getRealSkuEcode(), true);
                items.add(itm);
                preList.add(p2.getId());
            }
            sgAdjustReq.setItems(items);
            sgAdjustReq.setLoginUser(user);
            return sgRpcService.addStockAdjustment(sgAdjustReq).isOK();
        } catch (Exception e) {
            log.error(LogUtil.format("无头件调整.生成调整单服务.异常-{} "), Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    /**
     * 预.调用调整单服务.明细参数配置
     *
     * @param x        入库单明细
     * @param skuId    预用skuId
     * @param skuEcode skuEcode
     * @param flag     bool
     * @return SgPhyAdjustItemSaveRequest
     */
    private SgOmsStoAdjustItemSaveRequest dealProSkuAndPro(OcBRefundInProductItem x, Long skuId, String skuEcode,
                                                           boolean flag) {
        SgOmsStoAdjustItemSaveRequest itm = new SgOmsStoAdjustItemSaveRequest();
        itm.setSourceBillItemId(x.getId());
        itm.setPsCSkuEcode(skuEcode);
        itm.setQty(x.getQty());

        return itm;
    }

    /**
     * 分组明细
     *
     * @param pm  分组调整入参
     * @param x   OcBRefundInProductItem
     * @param itm SgPhyAdjustItemSaveRequest
     */
    private void spliceAdjustByRtnOdrId(Map<Long, List<SgOmsStoAdjustItemSaveRequest>> pm, OcBRefundInProductItem x,
                                        SgOmsStoAdjustItemSaveRequest itm) {
        Set<Long> rKeys = pm.keySet();
        if (rKeys.contains(x.getOcBReturnOrderId())) {
            List<SgOmsStoAdjustItemSaveRequest> sgItems = pm.get(x.getOcBReturnOrderId());
            sgItems.add(itm);
        } else {
            List<SgOmsStoAdjustItemSaveRequest> sgItems = new ArrayList<>();
            sgItems.add(itm);
            pm.put(x.getOcBReturnOrderId(), sgItems);
        }
    }

    /**
     * 无名件处理
     *
     * @param relation DRM
     * @param usr      User
     * @return vh
     */
    private ValueHolderV14 namelessItemProcess(RefundInRelation relation, User usr) {

        ValueHolderV14 vh = new ValueHolderV14();
        if (StringUtils.isBlank(relation.getRefundIn().getWmsBillNo())) {
            vh.setCode(normalProcess);
            return vh;
        }

        for (OcBRefundInProductItem item : relation.getItems()) {

            int isMatch = item.getIsMatch() == null ? 0 : item.getIsMatch();
            int isGenAdjust = item.getIsGenAdjust() == null ? 0 : item.getIsGenAdjust();
            boolean isNotNeed = isMatch != 0 || isGenAdjust != 0 || item.getOcBReturnOrderId() != null;
            if (isNotNeed) {
                vh.setCode(normalProcess);
                return vh;
            }
        }

        LogStepRecord recorder = LogStepRecord.build().record("无名件调整-Start: ");
        try {

            JSONObject jsn = new JSONObject();
            SgOmsStoAdjustSaveRequest sgPhyAdjustBillSaveRequest = setAndExtractParamVal4Sg(jsn, relation, usr);
            boolean isSuccess = sgRpcService.addStockAdjustment(sgPhyAdjustBillSaveRequest).isOK();
            recorder.record(" SgPhyAdjustBillSaveRequest.Result-" + isSuccess);

            if (isSuccess) {
                try {
                    //发送短信
                    SmsSendStrategyInfo smsSendStrategyInfo = new SmsSendStrategyInfo();
                    smsSendStrategyInfo.setOcBRefundIn(relation.getRefundIn());
                    smsSendStrategyInfo.setServiceType("smsSendRefundIn");
                    smsSendStrategyInfo.setTaskNode(OmsSendMsgTaskNodeEnum.NO_NAME_IN_STORAGE.getVal().toString());
                    applicationContext.publishEvent(new SmsSendEvent<>(this, smsSendStrategyInfo));
                } catch (Exception e) {
                    log.error(LogUtil.format("无名件异步短信监听处理异常->:{} "), Throwables.getStackTraceAsString(e));
                }

                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("无名件生成调整单成功");
                updateNamelessItem(jsn, relation.getItems(), usr);
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("无名件生成调整单失败");
            }

        } catch (Exception e) {
            log.error(LogUtil.format("无名件调整.调用生成调整单服务. 异常信息->: {}"), Throwables.getStackTraceAsString(e));
            vh.setMessage("无名件生成调整单异常");
            vh.setCode(ResultCode.FAIL);
            logDebug(recorder.content());
        }
        logDebug(recorder.content());
        return vh;

    }


    /**
     * 无名件组装调用参数明细
     *
     * @param jsn      条码记录
     * @param relation 入库单关系类
     * @param usr      操作用户
     * @return 调整单参数
     */
    private SgOmsStoAdjustSaveRequest setAndExtractParamVal4Sg(JSONObject jsn, RefundInRelation relation, User usr) {

        OcBRefundIn inRfnIn = relation.getRefundIn();
        List<OcBRefundInProductItem> rfnProList = relation.getItems();

        SgOmsStoAdjustSaveRequest sgAdjustReq = new SgOmsStoAdjustSaveRequest();
        sgAdjustReq.setSourceBillId(inRfnIn.getId());
        sgAdjustReq.setSourceBillNo(inRfnIn.getId().toString());
        sgAdjustReq.setSourceBillType(1);
        sgAdjustReq.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL);
        sgAdjustReq.setBillDate(inRfnIn.getSubmitDate());
        sgAdjustReq.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_NO_SOURCE_IN);
        sgAdjustReq.setCpCStoreEcode(inRfnIn.getInStoreEcode());
        sgAdjustReq.setLoginUser(usr);
        sgAdjustReq.setRequestType(SgConstants.REQUEST_TYPE_SAVE_AND_SUBMIT);//请求类型 创建并提交

        List<SgOmsStoAdjustItemSaveRequest> items = new ArrayList<>();

        // prepare for update
        StringBuilder sbId = new StringBuilder();
        Map<String, BigDecimal> staticsSkuCount = new HashMap<>();
        for (OcBRefundInProductItem x : rfnProList) {

            SgOmsStoAdjustItemSaveRequest itm = new SgOmsStoAdjustItemSaveRequest();
            itm.setSourceBillItemId(x.getId());
            itm.setPsCSkuEcode(x.getPsCSkuEcode());
            itm.setQty(x.getQty());
            items.add(itm);

            // pre set
            x.setIsGenAdjust(1);
            x.setModifierid(Long.valueOf(usr.getId()));
            x.setModifiername(usr.getName());
            x.setModifierename(usr.getEname());
            x.setModifieddate(new Date());

            sbId.append(",").append(x.getId());
            boolean hasSkuCode = staticsSkuCount.containsKey(x.getPsCSkuEcode());
            if (hasSkuCode) {
                BigDecimal qty = staticsSkuCount.get(x.getPsCSkuEcode());
                qty = qty.add(initBigDecimal(x.getQty()));
                staticsSkuCount.put(x.getPsCSkuEcode(), qty);
            } else {
                staticsSkuCount.put(x.getPsCSkuEcode(), initBigDecimal(x.getQty()));
            }
        }
        sbId.substring(1);
        jsn.put("IDS", sbId.substring(1));
        StringBuilder sbCode = new StringBuilder();
        for (Map.Entry<String, BigDecimal> entry : staticsSkuCount.entrySet()) {
            BigDecimal commonQty = entry.getValue().setScale(0, RoundingMode.HALF_DOWN);
            sbCode.append(",").append(entry.getKey()).append("(").append(commonQty).append(")");
        }
        jsn.put("CODE_CONTENT", sbCode.substring(1));

        sgAdjustReq.setItems(items);

        return sgAdjustReq;
    }


    /**
     * 数据操作. 无名件
     * <p>
     * 入库单
     *
     * @param inRfnProList 入库明细
     * @param usr          用户
     */
    private void updateNamelessItem(JSONObject jsn, List<OcBRefundInProductItem> inRfnProList, User usr) {

        logDebug(LogUtil.format("updateNamelessItem-> Start Update Param-{}"), JSON.toJSONString(inRfnProList));
        try {

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            OcBRefundInProductItem item = inRfnProList.get(0);
            jsn.put("MODIFIERID", item.getModifierid());
            jsn.put("MODIFIERNAME", item.getModifiername());
            jsn.put("MODIFIERENAME", item.getModifierename());
            jsn.put("MODIFIEDDATE", dateFormat.format(item.getModifieddate()));
            jsn.put("OC_B_REFUND_IN_ID", item.getOcBRefundInId());

            int itemResult = ocBRefundInProItemMapper.updateRefundItemIsGenAdjust(jsn);
            int logResult = recordNameLessLog(jsn.getString("CODE_CONTENT"), item.getOcBRefundInId(), usr);
            logDebug(LogUtil.format("updateNamelessItem. 更新数据结束.明细-{}.日志-{}"), itemResult, logResult);

        } catch (Exception e) {
            log.error(LogUtil.format("updateNamelessItem. 添加日志时异常"), Throwables.getStackTraceAsString(e));
        }
    }


    /**
     * 记录日志. 无名件
     *
     * @param logMsg
     * @param rfnId
     * @param usr
     * @return
     */
    private int recordNameLessLog(String logMsg, Long rfnId, User usr) {

        logDebug(LogUtil.format("writeRefundNameLessLog-无名件入库,日志记录->: 开始记录"));
        OcBRefundInLog rfnLog = new OcBRefundInLog();
        rfnLog.setLogtype("生成调整单(正)");
        setLogMsg(usr, rfnLog);
        Long id = Tools.getSequence("oc_b_refund_in_log");
        rfnLog.setId(id);
        rfnLog.setLogmessage("无名件生成调整单成功, 调整条码: " + logMsg);
        rfnLog.setOmsonlineorderid(rfnId);
        return ocBRefundInLogMapper.insert(rfnLog);
    }

    /**
     * 初始化
     *
     * @param num
     * @return
     */
    private BigDecimal initBigDecimal(BigDecimal num) {
        return num == null ? BigDecimal.ZERO : num;
    }

    /**
     * level debug recorder
     *
     * @param msg log message
     */
    private void logDebug(String msg, Object... params) {
        if (log.isDebugEnabled()) {
            if (params.length == 0) {
                log.debug(msg);
            } else {
                log.debug(msg, params);
            }
        }
    }


    /**
     * init and convert to int
     *
     * @param val BigDecimal
     * @return null return 0
     */
    private int initBigDecimal2Int(BigDecimal val) {
        return val == null ? 0 : val.intValue();
    }

}
