package com.jackrain.nea.oc.oms.carpoolOrder;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.cp.enums.CpCLogisticsTypeEnum;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024/12/26 11:13
 * @Description
 */
@Slf4j
@Component
public class OmsOrderCarpoolOrderService {

    @Resource
    private OcBOrderMapper ocBOrderMapper;
    @Resource
    private CpRpcService cpRpcService;
    @Resource
    private BllRedisLockOrderUtil redisUtil;
    @Resource
    private OmsOrderLogService omsOrderLogService;
    private static final String CARPOOL_NO_NAME = "OC_B_ORDER_CARPOOL_NO";


    /**
     * 校验是否满足拼车条件并获取拼车单号
     *
     * @param orderIds 订单id集合
     * @param user     用户信息
     * @return
     */
    public String carpoolOrderCheckAndGetNumber(List<Long> orderIds, User user) {
        log.info(LogUtil.format("OmsOrderCarpoolOrderService.carpoolOrderCheckAndGetNumber orderIds:{},user:{}",
                        "OmsOrderCarpoolOrderService.carpoolOrderCheckAndGetNumber"),
                JSONObject.toJSONString(orderIds), JSONObject.toJSONString(user));
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new NDSException("订单id不能为空！");
        }
        if (user == null) {
            throw new NDSException("用户信息不能为空！");
        }
        //check
        checkCarpoolOrder(orderIds);
        //生成流水拼车单号
        return SequenceGenUtil.generateSquence(CARPOOL_NO_NAME,
                new JSONObject(), user.getLocale(), false);
    }

    /**
     * 整车拼车确认
     *
     * @param orderIds  订单id集合
     * @param carpoolNo 拼车单号
     * @param user      用户信息
     */
    public void carpoolOrderConfirm(List<Long> orderIds, String carpoolNo, User user) {
        log.info(LogUtil.format("OmsOrderCarpoolOrderService.carpoolOrderConfirm orderIds:{},carpoolNo:{},user:{}",
                        "OmsOrderCarpoolOrderService.carpoolOrderConfirm"),
                JSONObject.toJSONString(orderIds), carpoolNo, JSONObject.toJSONString(user));
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new NDSException("订单id不能为空！");
        }
        if (StringUtils.isEmpty(carpoolNo)) {
            throw new NDSException("拼车单号不能为空！");
        }
        if (user == null) {
            throw new NDSException("用户信息不能为空！");
        }
        //加锁
        List<RedisReentrantLock> lockList = new ArrayList<>();
        try {
            batchLock(orderIds, lockList);
            //调用上面check
            List<OcBOrder> orderList = checkCarpoolOrder(orderIds);
            //校验拼车单号是否重复（走ES,可能存在延迟） -- 当前拼车单号已存在！
            List<Long> ids = ES4Order.getIdsByCarpoolNo(carpoolNo);
            if (CollectionUtils.isNotEmpty(ids)) {
                throw new NDSException("当前拼车单号已存在！");
            }
            //写入拼车单号
            OcBOrder updateModel = new OcBOrder();
            updateModel.setCarpoolNo(carpoolNo);
            ocBOrderMapper.update(updateModel, new LambdaQueryWrapper<OcBOrder>().in(OcBOrder::getId, orderIds));
            //记录日志 -- [PC202412140001]整车拼车成功！
            List<OcBOrderLog> orderLogs = new ArrayList<>();
            StringBuilder sb = new StringBuilder();
            for (OcBOrder order : orderList) {
                sb.setLength(0);
                sb.append("[").append(carpoolNo).append("]整车拼车成功！");
                OcBOrderLog orderLog = omsOrderLogService.getOcBOrderLog(order.getId(), order.getBillNo(),
                        OrderLogTypeEnum.CARPOOL_ORDER.getKey(), sb.toString(), "", "", user);
                orderLogs.add(orderLog);
            }
            omsOrderLogService.save(orderLogs);
        } catch (Exception e) {
            throw new NDSException(e.getMessage());
        } finally {
            //批量释放锁
            batchUnLock(lockList);
        }
    }

    /**
     * 取消整车拼车
     *
     * @param orderIds
     * @param user
     * @return
     */
    public void carpoolOrderCancel(List<Long> orderIds, User user) {
        log.info(LogUtil.format("OmsOrderCarpoolOrderService.carpoolOrderCancel orderIds:{},user:{}",
                        "OmsOrderCarpoolOrderService.carpoolOrderCancel"),
                JSONObject.toJSONString(orderIds), JSONObject.toJSONString(user));
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new NDSException("订单id不能为空！");
        }
        if (user == null) {
            throw new NDSException("用户信息不能为空！");
        }
        List<OcBOrder> orderList = ocBOrderMapper.selectOrderListByIds(orderIds);
        if (CollectionUtils.isEmpty(orderList)) {
            throw new NDSException("订单不存在！");
        }
        List<Integer> orderStatus = new ArrayList<>();
        List<String> carpoolNos = new ArrayList<>();
        for (OcBOrder order : orderList) {
            if (!orderStatus.contains(order.getOrderStatus())) {
                orderStatus.add(order.getOrderStatus());
            }
            if (StringUtils.isNotEmpty(order.getCarpoolNo()) && !carpoolNos.contains(order.getCarpoolNo())) {
                carpoolNos.add(order.getCarpoolNo());
            }
        }
        if (orderStatus.size() != 1 || !OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus.get(0))) {
            throw new NDSException("所有订单必须是待审核！");
        }
        if (CollectionUtils.isEmpty(carpoolNos)) {
            throw new NDSException("请勾选拼车订单进行取消拼车！");
        }
        cancelCarpoolNo(carpoolNos, user, true);
    }

    /**
     * 清空拼车单号
     *
     * @param carpoolNos    拼车单号
     * @param user          用户信息
     * @param isReturnError 是否抛异常报错
     */
    public void cancelCarpoolNo(List<String> carpoolNos, User user, boolean isReturnError) {
        log.info(LogUtil.format("OmsOrderCarpoolOrderService.cancelCarpoolNo carpoolNos:{},user:{},isReturnError:{}",
                        "OmsOrderCarpoolOrderService.cancelCarpoolNo"),
                JSONObject.toJSONString(carpoolNos), JSONObject.toJSONString(user), isReturnError);
        if (user == null) {
            user = SystemUserResource.getRootUser();
        }
        try {
            if (CollectionUtils.isEmpty(carpoolNos)) {
                throw new NDSException("拼车单号不能为空！");
            }
            for (String carpoolNo : carpoolNos) {
                List<Long> ids = ES4Order.getIdsByCarpoolNo(carpoolNo);
                if (CollectionUtils.isEmpty(ids)) {
                    throw new NDSException("拼车单号[" + carpoolNo + "]查询ES订单为空");
                }
                List<OcBOrder> orderList = ocBOrderMapper.selectOrderListByIds(ids);
                if (CollectionUtils.isEmpty(orderList)) {
                    throw new NDSException("拼车单号[" + carpoolNo + "]查询DB订单为空");
                }
                ocBOrderMapper.updateOrderCarpoolNoToNull(ids);
                List<OcBOrderLog> orderLogs = new ArrayList<>();
                StringBuilder sb = new StringBuilder();
                for (OcBOrder order : orderList) {
                    sb.setLength(0);
                    sb.append("[").append(carpoolNo).append("]取消拼车成功！");
                    OcBOrderLog orderLog = omsOrderLogService.getOcBOrderLog(order.getId(), order.getBillNo(),
                            OrderLogTypeEnum.CANCEL_CARPOOL_ORDER.getKey(), sb.toString(), "", "", user);
                    orderLogs.add(orderLog);
                }
                omsOrderLogService.save(orderLogs);
            }
        } catch (Exception e) {
            if (isReturnError) {
                throw new NDSException(e.getMessage());
            } else {
                log.error(LogUtil.format("OmsOrderCarpoolOrderService.cancelCarpoolNo error:{}",
                        "OmsOrderCarpoolOrderService.cancelCarpoolNo"), e.getMessage());
            }
        }
    }

    /**
     * 批量解锁
     *
     * @param lockList 锁集合
     */
    private void batchUnLock(List<RedisReentrantLock> lockList) {
        if (CollectionUtils.isNotEmpty(lockList)) {
            for (RedisReentrantLock redisReentrantLock : lockList) {
                redisReentrantLock.unlock();
            }
        }
    }

    /**
     * 批量加锁
     *
     * @param orderIds 订单id
     * @param lockList 锁集合
     * @throws InterruptedException
     */
    private void batchLock(List<Long> orderIds, List<RedisReentrantLock> lockList) throws InterruptedException {
        for (Long orderId : orderIds) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                lockList.add(redisLock);
            } else {
                throw new NDSException("整车拼车加锁失败，请稍后重试！");
            }
        }
    }

    /**
     * 校验订单信息
     *
     * @param orderIds 订单id
     * @return
     */
    private List<OcBOrder> checkCarpoolOrder(List<Long> orderIds) {
        //根据id查询订单集合
        List<OcBOrder> orderList = ocBOrderMapper.selectOrderListByIds(orderIds);
        if (CollectionUtils.isEmpty(orderList)) {
            throw new NDSException("订单不存在！");
        }
        //收集订单状态、仓库、物流公司、拼车单号
        List<Integer> orderStatus = new ArrayList<>();
        List<Long> warehouseList = new ArrayList<>();
        List<Long> logisticsList = new ArrayList<>();
        for (OcBOrder order : orderList) {
            //校验拼车单号是否为空 -- 拼车单号不为空！
            if (StringUtils.isNotEmpty(order.getCarpoolNo())) {
                throw new NDSException(order.getBillNo() + "拼车单号不为空！");
            }
            if (!orderStatus.contains(order.getOrderStatus())) {
                orderStatus.add(order.getOrderStatus());
            }
            if (order.getCpCPhyWarehouseId() == null) {
                throw new NDSException(order.getBillNo() + "仓库为空！");
            } else if (!warehouseList.contains(order.getCpCPhyWarehouseId())) {
                warehouseList.add(order.getCpCPhyWarehouseId());
            }
            if (order.getCpCLogisticsId() == null) {
                throw new NDSException(order.getBillNo() + "物流公司为空！");
            } else if (!logisticsList.contains(order.getCpCLogisticsId())) {
                logisticsList.add(order.getCpCLogisticsId());
            }
        }
        //校验订单状态是否唯一且为待审核 -- 所有订单必须是待审核
        if (orderStatus.size() != 1 || !OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus.get(0))) {
            throw new NDSException("所有订单必须是待审核！");
        }
        //校验仓库、物流公司是否唯一 -- 发货仓库不一致！||物流公司不一致！
        if (warehouseList.size() != 1) {
            throw new NDSException("发货仓库不一致！");
        }
        if (logisticsList.size() != 1) {
            throw new NDSException("物流公司不一致！");
        }
        //校验物流公司为整车类型
        CpLogistics logistics = cpRpcService.queryLogisticsById(logisticsList.get(0));
        if (CpCLogisticsTypeEnum.FULL_CAR.getKey() != logistics.getType() &&
                CpCLogisticsTypeEnum.LARGE_CARGO_LOGISTICS.getKey() != logistics.getType()) {
            throw new NDSException("物流公司必须为整车或者大货物流类型！");
        }
        return orderList;
    }
}
