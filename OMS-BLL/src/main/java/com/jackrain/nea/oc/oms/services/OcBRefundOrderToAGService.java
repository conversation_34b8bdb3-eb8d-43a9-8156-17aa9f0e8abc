package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * <AUTHOR> 孙俊磊
 * @since :  2019-04-02
 * create at:  2019-04-02 10:06
 */
@Component
@Slf4j
public class OcBRefundOrderToAGService {

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private IpBTaobaoRefundMapper ipBTaobaoRefundMapper;

    @Autowired
    private ReturnOrderLogService logService;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private IpRpcService ipRpcService;

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 refundOrderToAg(String param, User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        JSONObject jsonObject = JSON.parseObject(param);
        if (jsonObject == null || jsonObject.size() == 0) {
            holderV14.setCode(-1);
            holderV14.setMessage("参数为空！");
            return holderV14;
        }
        //退换货Id
        Long id = jsonObject.getLong("ID");
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(id);
        if (ocBReturnOrder == null) {
            holderV14.setCode(-1);
            holderV14.setMessage("退换货单已不存在！");
            return holderV14;
        }

        //如果退换货单状态为完成状态，则进入下一步流程判断；如果为其他状态，则不进行处理
        Integer returnStatus = ocBReturnOrder.getReturnStatus();
        if (returnStatus == null || !returnStatus.equals(ReturnStatusEnum.COMPLETION.getVal())) {
            holderV14.setCode(-1);
            holderV14.setMessage("退单传AG服务失败：退换货状态不匹配！");
            return holderV14;
        }

        //判断退货单平台类型如果为淘宝平台的退单，进行下一步判断,否则不判断
        Integer platform = ocBReturnOrder.getPlatform();
        if (!PlatFormEnum.TAOBAO.getCode().equals(platform)) {
            holderV14.setCode(-1);
            holderV14.setMessage("退单传AG服务失败：平台类型不匹配！");
            return holderV14;
        }

        /*单据类型,1退货单，2退换货单',
         *退单的类型，如果为退换货单，则不走AG程序，不进行处理，更新传AG状态为不传AG（3）
         *添加退换货订单操作日志中；如果类型为退货单时进行下一步判断
         */
        Integer billType = ocBReturnOrder.getBillType();
        Integer isToAg = ocBReturnOrder.getIsToag();
        if (billType == null || billType.equals(OcReturnBillTypeEnum.EXCHANGE.getVal())) {
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            returnOrder.setId(ocBReturnOrder.getId());
            returnOrder.setIsToag(AGStatusEnum.NOT.getVal());
            return updateRefundOrderAgAndLog(returnOrder, loginUser, "单据类型不匹配！");
        }

        //如果退单为退货单类型，则继续判断退单传AG状态
        //如果退单状态为已传AG（1），或者不传AG（3），则不进行处理
        if (isToAg == null) {
            holderV14.setCode(-1);
            holderV14.setMessage("退单传AG服务失败：AG状态异常！");
            return holderV14;
        } else if (isToAg.equals(AGStatusEnum.INIT.getVal()) || isToAg.equals(AGStatusEnum.FAIL.getVal())) {
            StCShopStrategyDO ocStCShopStrategy = stRpcService.selectOcStCShopStrategyByCpCshopId(ocBReturnOrder.getCpCShopId());
            if (ocStCShopStrategy != null && IsOpenAGEnum.OPEN.getVal().equals(ocStCShopStrategy.getIsAg())) {
                return gotoAg(ocBReturnOrder, loginUser);
            } else {
                holderV14.setCode(-1);
                holderV14.setMessage("退单传AG服务失败：店铺策略为空或店铺策略未勾选AG状态！");
                return holderV14;
            }
        } else {
            holderV14.setCode(-1);
            holderV14.setMessage("退单传AG服务失败：AG状态不匹配！");
            return holderV14;
        }
    }

    private ValueHolderV14 gotoAg(OcBReturnOrder ocBReturnOrder, User loginUser) {
        /*
         * 判断退货单的平台退款单号是否为空 ，
         * 如果为空，则不走AG程序，不进行处理，更新传AG状态为不传AG
         * 添加退换货订单操作日志中；不为空进行下一步判断
         */
        String returnId = ocBReturnOrder.getReturnId();
        //此实体仅用来更新
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        returnOrder.setId(ocBReturnOrder.getId());

        if (StringUtils.isNotEmpty(returnId)) {

            QueryWrapper<IpBTaobaoRefund> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("refund_id", returnId);
            IpBTaobaoRefund ipBTaobaoRefund = ipBTaobaoRefundMapper.selectOne(queryWrapper);

            if (ipBTaobaoRefund != null) {
                String status = ipBTaobaoRefund.getStatus();
                /*若查找到退单记录，则判断退单中间表的退单状态，
                 *当状态为：WAIT_BUYER_RETURN_GOODS，WAIT_SELLER_CONFIRM_GOODS，
                 *则调用AG入仓接口
                 */
                log.debug(this.getClass().getSimpleName() + "退单的中间表数据=" + JSON.toJSONString(ipBTaobaoRefund));

                log.debug(this.getClass().getSimpleName() + "退单的平台退款单号查找淘宝退款单中间表退单状态=" + status);

                if (OcOrderRefundStatusEnum.WAIT_BUYER_RETURN_GOODS.name().equals(status) || OcOrderRefundStatusEnum.WAIT_SELLER_CONFIRM_GOODS.name().equals(status)) {
                    BigDecimal returnAmtActual = ocBReturnOrder.getReturnAmtActual();
                    BigDecimal refundFee = ipBTaobaoRefund.getRefundFee();
                    //有任意值为空，不能调用
                    if (refundFee == null || returnAmtActual == null) {
                        returnOrder.setIsToag(AGStatusEnum.NOT.getVal());
                        return updateRefundOrderAgAndLog(returnOrder, loginUser, "退换货订单的退货单总金额和退单中间表的退还金额不一致，不调AG入仓接口！");
                    }
                    //金额相等调用ag入仓
                    if (refundFee.compareTo(returnAmtActual) == 0) {
                        boolean success = ipRpcService.updateLogisticsWarehouse(returnId, ocBReturnOrder.getCpCShopId(), loginUser);
                        if (success) {
                            returnOrder.setIsToag(AGStatusEnum.SUCCESS.getVal());
                            returnOrder.setIsInstorage(IsInStorageEnum.IS_INSTORAGE.getVal());
                            return updateRefundOrderAgAndLog(returnOrder, loginUser, "单据类型不匹配！");
                        } else {
                            returnOrder.setIsToag(AGStatusEnum.FAIL.getVal());
                            return updateRefundOrderAgAndLog(returnOrder, loginUser, "调用AG入仓接口失败！");
                        }
                    } else {
                        returnOrder.setIsToag(AGStatusEnum.NOT.getVal());
                        return updateRefundOrderAgAndLog(returnOrder, loginUser, "退换货订单的退货单总金额和退单中间表的退还金额不一致，不调AG入仓接口！");
                    }
                } else {
                    returnOrder.setIsToag(AGStatusEnum.NOT.getVal());
                    return updateRefundOrderAgAndLog(returnOrder, loginUser, "判断退单中间表的退单状态不匹配！");
                }

            } else {
                returnOrder.setIsToag(AGStatusEnum.NOT.getVal());
                return updateRefundOrderAgAndLog(returnOrder, loginUser, "淘宝退款单中间表不存在此退单！");
            }
        } else {
            returnOrder.setIsToag(AGStatusEnum.NOT.getVal());
            return updateRefundOrderAgAndLog(returnOrder, loginUser, "平台退款单号为空！");
        }
    }

    /**
     * 更新传AG状态，添加退换货订单操作日志中
     *
     * @param ocBReturnOrder 退换货实体
     * @param loginUser      loginUser
     * @param logContent     日志内容
     */
    private ValueHolderV14 updateRefundOrderAgAndLog(OcBReturnOrder ocBReturnOrder, User loginUser, String logContent) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            ocBReturnOrderMapper.updateById(ocBReturnOrder);
        } catch (Exception ex) {
            log.error(LogUtil.format("日志服务：更新AG状态异常,error:{}"), Throwables.getStackTraceAsString(ex));
            logContent = "同步异常：" + ex;
            logService.addRefundOrderLog(ocBReturnOrder.getId(), "同步AG入仓", logContent, loginUser);
            throw new NDSException("退单传AG服务失败！" + ex);
        }

        if (ocBReturnOrder.getIsToag().equals(AGStatusEnum.SUCCESS.getVal())) {
            logContent = "同步成功：同步AG入仓成功";
            holderV14.setCode(0);
            holderV14.setMessage("退单传AG服务成功！");
        } else {
            holderV14.setCode(-1);
            logContent = "同步失败：" + logContent;
            holderV14.setMessage("退单传AG服务失败！" + logContent);
        }
        logService.addRefundOrderLog(ocBReturnOrder.getId(), "同步AG入仓", logContent, loginUser);
        return holderV14;
    }

}

