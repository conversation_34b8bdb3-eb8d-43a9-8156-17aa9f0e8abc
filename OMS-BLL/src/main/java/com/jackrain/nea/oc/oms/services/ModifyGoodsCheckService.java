package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.util.OrderPrevDealUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 周琳胜
 * @since: 2019/3/12
 * create at : 2019/3/12 11:45
 */
@Slf4j
@Component
public class ModifyGoodsCheckService {
    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;

    /**
     * 更换商品前置判断
     *
     * @param obj  入参orderId
     * @param user 用户信息
     * @return
     */
    public ValueHolderV14 modifyGoodsCheck(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (null == obj) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        //获得订单明细id
        Long orderItemId = obj.getLong("ID");
        QueryWrapper<OcBOrderItem> wrapper = new QueryWrapper<>();
        wrapper.eq("id", orderItemId);
        OcBOrderItem ocBOrderItem = ocBOrderItemMapper.selectOne(wrapper); //获取明细信息
        if (ocBOrderItem.getRefundStatus() == OcOrderRefundStatusEnum.SUCCESS.getVal()) {
            throw new NDSException(Resources.getMessage("当前已经退款完成，不允许操作！", user.getLocale()));
        }
        OcBOrder order = ocBOrderMapper.selectById(ocBOrderItem.getOcBOrderId()); //获取订单信息

        // JITX平台.拦截
        OrderPrevDealUtil.platformIntercept(order, user);

        Integer orderStatus = order.getOrderStatus(); //获得订单状态
        //  1, 待审核 2, 缺货3, 已审核4, 配货中
        if (null == orderStatus) {
            throw new NDSException(Resources.getMessage("订单状态异常，不允许更换商品！", user.getLocale()));
        } else if (orderStatus != OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal() && orderStatus
                != OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal() && orderStatus
                != OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
            throw new NDSException(Resources.getMessage("订单状态不符合，不允许更换商品！", user.getLocale()));
        }
        if (orderStatus == OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal()) {
            Integer wmsCancelStatus = order.getWmsCancelStatus();
            if (null == wmsCancelStatus) {
                throw new NDSException(Resources.getMessage("wms撤回状态异常，不允许更换商品！", user.getLocale()));
            } else if (wmsCancelStatus != OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()) {    //  0未撤回1 撤回成功2撤回失败
                throw new NDSException(Resources.getMessage("此订单在WMS中未取消，不允许更换商品，建议先撤回WMS再进"
                        + "行更换！", user.getLocale()));
            }
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("OK");
        return vh;
    }
}
