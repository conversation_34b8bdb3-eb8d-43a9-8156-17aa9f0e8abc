package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.burgeon.r3.sg.inf.model.result.oms.product.SgStoreWithPrioritySearchItemResult;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutBillVoidRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutVoidRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.VipcomTimeOrderConfing;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderOccupyItemStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBJitxDealerOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOccupyRelation;
import com.jackrain.nea.oc.oms.model.request.TimeOrderVoidSgSendRequest;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.AddAndVoidStockListService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :1.0
 * description ： 时效订单取消占单服务
 * @date :2019/8/16 15:30
 */
@Component
@Slf4j
public class TimeOrderVoidSgSendService {

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private IpBTimeOrderVipMapper mapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBorderItemMapper;

    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;

    private final String orderTable = "oc_b_order";

    @Autowired
    private AddAndVoidStockListService addAndVoidStockListService;

    @Autowired
    private VipcomTimeOrderConfing vipcomTimeOrderConfing;

    @Autowired
    private IpBTimeOrderVipOccupyItemMapper timeOrderVipOccupyItemMapper;

    @Autowired
    private IpBJitxOrderMapper jitxOrderMapper;

    @Autowired
    private OcBJitxDealerOrderTaskService taskService;

    @Autowired
    protected IpJitxDeliveryService ipJitxDeliveryService;

    @Autowired
    private IpVipTimeOrderService ipVipTimeOrderService;

    /**
     * @param borderDto 订单
     * @param request   请求
     * @return com.jackrain.nea.sys.domain.ValueHolderV14 :注释
     * <AUTHOR>
     * @date 2020/7/22 16:21
     * @description：批量作废新增逻辑发货单
     */
    public ValueHolderV14 voidSgSendV14(OcBOrder borderDto, TimeOrderVoidSgSendRequest request) {

        ValueHolderV14 vhResult = new ValueHolderV14();

        //查询时效订单信息
        List<IpBTimeOrderVip> ipBTimeOrderVipList = mapper.selectTimeOrderByOrderSn(request.getOrderSn());

        if (CollectionUtils.isEmpty(ipBTimeOrderVipList)) {
            //拿到更新后的对象
            OcBOrder order = ocBOrderMapper.selectById(borderDto.getId());
            List<OcBOrderItem> items = ocBorderItemMapper.selectOrderItemListAndReturn(borderDto.getId());
            return jitxOrderOnlyOccupyStock(order, items, request.getUser());
        }
        List<IpBTimeOrderVip> occupiedTimeOrderVipList = ipBTimeOrderVipList.stream()
                .filter(t -> TimeOrderVipStatusEnum.OCCUPIED.getValue().equals(t.getStatus()) ||
                        TimeOrderVipStatusEnum.OUT_STOCK.getValue().equals(t.getStatus())).collect(Collectors.toList());
        //拿到更新后的对象
        OcBOrder order = ocBOrderMapper.selectById(borderDto.getId());
        List<OcBOrderItem> items = ocBorderItemMapper.selectOrderItemListAndReturn(borderDto.getId());

        // 无占用库存的时效订单--->只是订单占库存，不再做释放库存操作
        if (CollectionUtils.isEmpty(occupiedTimeOrderVipList)) {
            return jitxOrderOnlyOccupyStock(order, items, request.getUser());
        }

        List<Long> orderIdList = ipBTimeOrderVipList.stream().map(IpBTimeOrderVip::getId).collect(Collectors.toList());

        List<IpBTimeOrderVipOccupyItem> timeOrderVipOccupyItems = timeOrderVipOccupyItemMapper.selectOrderOccupyItemListByOrderIds(orderIdList);
        if (CollectionUtils.isEmpty(timeOrderVipOccupyItems)) {
            return jitxOrderOnlyOccupyStock(order, items, request.getUser());
        }

        IpBJitxOrder jitxOrder = jitxOrderMapper.selectJitxOrderByOrderSn(order.getSourceCode());

        List<String> warehouseCodes = timeOrderVipOccupyItems.stream().map(IpBTimeOrderVipOccupyItem::getWarehouseCode).distinct().collect(Collectors.toList());
        if (warehouseCodes.size() != 1 || !warehouseCodes.get(0).equalsIgnoreCase(jitxOrder.getDeliveryWarehouse())) {
            voidSgSend(request);
            return jitxOrderOnlyOccupyStock(order, items, request.getUser());
        }

//        SgSendVoidSaveHandleRequest sendVoidSaveHandleRequest = new SgSendVoidSaveHandleRequest();
//        SgSendVoidSaveHandleBase newOrder = createNewOrder(order, items);
//        List<SgSendVoidSaveHandleBase> oldOrderList = createOldOrderList(occupiedTimeOrderVipList);
//        sendVoidSaveHandleRequest.setBillInfo(newOrder);
//        sendVoidSaveHandleRequest.setBillInfoList(oldOrderList);
//        sendVoidSaveHandleRequest.setLoginUser(request.getUser());
        if (log.isDebugEnabled()) {
            //log.debug("调用批量作废批量新增逻辑发货单入参:{}", JSON.toJSONString(sendVoidSaveHandleRequest));
        }
        try {
            ValueHolderV14<List<SgStoreWithPrioritySearchItemResult>> result = null;
                    //sgRpcService.jitxMerge(sendVoidSaveHandleRequest);

            if (log.isDebugEnabled()) {
                log.debug("调用批量作废批量新增逻辑发货单返回结果:{}", JSON.toJSONString(result));
            }

            ValueHolderV14<Map<Long, Integer>> vh = new ValueHolderV14<>();
            Map<Long, Integer> map = new HashMap<>();
            if (result.getCode() == ResultCode.FAIL) {
                vhResult.setCode(ResultCode.FAIL);
                vhResult.setMessage("时效订单批量作废新增逻辑发货单失败: " + result.getMessage());
                for (OcBOrderItem ocBOrderItem : items) {
                    //缺货数量  全缺
                    BigDecimal qty = ocBOrderItem.getQty();
                    Long ocBOrderId = ocBOrderItem.getOcBOrderId();
                    map.put(ocBOrderId, qty.intValue());
                    vh.setCode(3);
                    vh.setMessage("订单占用库存失败,明细库存缺货!");
                    log.debug("订单{}占用库存失败,明细库存缺货!", order.getId());
                }
            } else {
                List<SgStoreWithPrioritySearchItemResult> resultList = result.getData();
                if (CollectionUtils.isNotEmpty(resultList)) {
                    for (SgStoreWithPrioritySearchItemResult itemResult : resultList) {
                        //缺货数量
                        BigDecimal qtyOutOfStock = itemResult.getQtyOutOfStock();
                        Long itemId = itemResult.getSourceItemId();
                        map.put(itemId, qtyOutOfStock.intValue());
                    }
                    vh.setCode(3);
                    vh.setMessage("订单占用库存失败,明细库存缺货!");
                    log.debug("订单{}占用库存失败,明细库存缺货!", order.getId());
                } else {
                    vh.setCode(0);
                    vh.setMessage(order.getId() + "订单占用库存成功!");
                    log.debug("订单{}占用库存成功!", order.getId());
                }
                vhResult.setCode(ResultCode.SUCCESS);
                vhResult.setMessage("时效订单批量作废新增逻辑发货单成功！");
            }
            vh.setData(map);
            SgOccupiedInventoryService sgOccupiedInventoryService = ApplicationContextHandle.getBean(SgOccupiedInventoryService.class);
            sgOccupiedInventoryService.handleOccupy(order, items, vh, request.getUser(), request.getRemark(), ipBTimeOrderVipList);

        } catch (Exception e) {
            log.error("订单{}时效订单批量作废新增逻辑发货单异常", order.getId(), e);
            vhResult.setCode(ResultCode.FAIL);
            vhResult.setMessage("时效订单批量作废新增逻辑发货单异常！");
        }
        return vhResult;
    }

    private ValueHolderV14 jitxOrderOnlyOccupyStock(OcBOrder order, List<OcBOrderItem> items, User user) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        try {
            //调用占用库存服务。占用成功后调用订单日志服务
            OmsOccupyRelation omsOccupyRelation = SgRpcService.getOmsOccupyRelation(order, items);
            SgOccupiedInventoryService transactionService = ApplicationContextHandle.getBean(SgOccupiedInventoryService.class);
            ValueHolderV14<Map<Long, Integer>> vh = transactionService.occupySearchStockWareHouse(omsOccupyRelation, user);
            sgOccupiedInventoryService.handleOccupy(order, items, vh, user);
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("JITX订单仅占用库存成功！");
        } catch (Exception ex) {
            log.error(LogUtil.format("调用占用库存异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            throw new NDSException(ex.getMessage());
        }
        return valueHolderV14;
    }

//    private List<SgSendVoidSaveHandleBase> createOldOrderList(List<IpBTimeOrderVip> occupiedTimeOrderVipList) {
//        List<SgSendVoidSaveHandleBase> oldOrderList = new ArrayList<>();
//        occupiedTimeOrderVipList.forEach(o -> {
//            SgSendVoidSaveHandleBase oldOrder = new SgSendVoidSaveHandleBase();
//            oldOrder.setSourceBillId(o.getId());
//            oldOrder.setSourceBillNo(o.getBillNo());
//            oldOrder.setSourceBillType(SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
//            oldOrderList.add(oldOrder);
//        });
//        return oldOrderList;
//    }

//    private SgSendVoidSaveHandleBase createNewOrder(OcBOrder order, List<OcBOrderItem> items) {
//        SgSendVoidSaveHandleBase newOrder = new SgSendVoidSaveHandleBase();
//        if (null == order) {
//            if (log.isDebugEnabled()) {
//                log.debug(this.getClass().getName() + "时效订单释放库存给jitx订单参数为空");
//            }
//        } else {
//
//            newOrder.setSourceBillId(order.getId());
//            newOrder.setSourceBillNo(order.getBillNo());
//            //取自库存中心枚举  和作废逻辑发货单的枚举保持一致
//            newOrder.setSourceBillType(SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
//        }
//        List<SgSendVoidSaveSkuItemInfoRequest> skuItemInfo = new ArrayList<>(items.size());
//        items.forEach(item -> {
//            if (item.getProType() == (long) SkuType.NO_SPLIT_COMBINE
//                    || item.getRefundStatus() == OcOrderRefundStatusEnum.SUCCESS.getVal()) {
//                return;
//            }
//            SgSendVoidSaveSkuItemInfoRequest skuItem = new SgSendVoidSaveSkuItemInfoRequest();
//            skuItem.setItemId(item.getId());
//            skuItem.setPsCSkuEcode(item.getPsCSkuEcode());
//            skuItem.setPsCSkuId(item.getPsCSkuId());
//            skuItem.setQty(item.getQty());
//            skuItemInfo.add(skuItem);
//        });
//        newOrder.setSourceCode(addAndVoidStockListService.buildSourceCode(
//                items.stream().map(OcBOrderItem::getTid).distinct().collect(Collectors.toList())));
//        newOrder.setSkuItemInfo(skuItemInfo);
//        return newOrder;
//    }

    /**
     * @param request 请求对象
     * @return com.jackrain.nea.sys.domain.ValueHolderV14 :注释
     * <AUTHOR>
     * @date 2020/7/22 10:52
     * @description：时效订单仅释放库存
     */
    public ValueHolderV14 voidSgSendV14(@NotNull TimeOrderVoidSgSendRequest request) {
        ValueHolderV14 result = new ValueHolderV14<>();
        if (StringUtils.isBlank(request.getOrderSn())
                && StringUtils.isBlank(request.getPickNo())
                && StringUtils.isBlank(request.getOccupiedOrderSn())) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("输入参数为空！");
        }
        try {
            result = voidSgSend(request);
        } catch (Exception e) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("订单释放库存失败！");
        }
        return result;
    }

    /**
     * @param request 请求体
     * @return void : 注释
     * <AUTHOR>
     * @date 2020/7/22 10:53
     * @description：时效订单仅释放库存
     */
    private ValueHolderV14 voidSgSend(TimeOrderVoidSgSendRequest request) {
        User user = request.getUser();
        QueryWrapper<GsiIpBTimeOrderVipOrderSn> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_sn", request.getOrderSn());

        List<String> occupiedOrderSnList =  new ArrayList<>();
        List<IpBTimeOrderVip> timeOrderVips = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getOrderSn())) {
            timeOrderVips = mapper.selectTimeOrderOccupiedOrderSnListByOrderSn(request.getOrderSn());
            if(!CollectionUtils.isEmpty(timeOrderVips)){
                List<String> occupiedOrderSns = timeOrderVips.stream().map(IpBTimeOrderVip::getOccupiedOrderSn).collect(Collectors.toList());
                occupiedOrderSnList.addAll(occupiedOrderSns);
            }
        } else if (StringUtils.isNotBlank(request.getOccupiedOrderSn())) {
            //timeOrderVips.add(mapper.selectTimeOrderByOccupiedOrderSn(request.getOccupiedOrderSn()));
            occupiedOrderSnList.add(request.getOccupiedOrderSn());
        }
        //Map<Long, List<IpBTimeOrderVip>> collect = timeOrderVips.stream().collect(Collectors.groupingBy(IpBTimeOrderVip::getId));

        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS,"取消占用成功");
        if (CollectionUtils.isEmpty(occupiedOrderSnList)) {
            log.error("TimeOrderVoidSgSendService.voidSgSend:时效订单释放库存,查询时效订单编号集合为空 request:{}",JSON.toJSONString(request));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("时效单不存在");
            return v14;
        }
        List<Integer> timeOrderStatus = new ArrayList<>();
        timeOrderStatus.add(TimeOrderVipStatusEnum.OCCUPIED.getValue());
        timeOrderStatus.add(TimeOrderVipStatusEnum.OUT_STOCK.getValue());
        timeOrderStatus.add(TimeOrderVipStatusEnum.SEEKING_STORE_SUCCESS.getValue());
        timeOrderStatus.add(TimeOrderVipStatusEnum.CREATED.getValue());
        for (String occupiedOrderSn : occupiedOrderSnList) {
            //查询时效订单
            IpVipTimeOrderRelation timeOrderInfo = ipVipTimeOrderService.selectTimeOrder(occupiedOrderSn);
            if(timeOrderInfo == null || timeOrderInfo.getIpBTimeOrderVip() == null){
                log.error("TimeOrderVoidSgSendService.voidSgSend:时效订单 occupiedOrderSn：{} 取消库存占用失败，时效订单查询为空", occupiedOrderSn);
                continue;
            }
            IpBTimeOrderVip timeOrderVip = timeOrderInfo.getIpBTimeOrderVip();
            String orderSn = timeOrderVip.getOrderSn();
            Integer status = timeOrderVip.getStatus();
            if(!timeOrderStatus.contains(status)){
                log.info("TimeOrderVoidSgSendService.voidSgSend:时效订单 occupiedOrderSn：{} 取消库存占用失败，状态不满足", occupiedOrderSn);
                continue;
            }
            /**判断是否全部缺货，如果全部缺货，则不调用库存释放接口*/
            boolean isAllStock = ipVipTimeOrderService.isAllStock(timeOrderInfo);
            if(!isAllStock){
                if(log.isDebugEnabled()){
                    log.debug(" 时效订单取消，时效单：{}", JSON.toJSONString(timeOrderInfo));
                }

                try {
                    //插入取消YY时效单任务
                    OcBJitxDealerOrderRelation relation=new OcBJitxDealerOrderRelation();
                    relation.setTimeOrderVip(timeOrderInfo.getIpBTimeOrderVip());
                    relation.setWarehouseId(timeOrderInfo.getIpBTimeOrderVipOccupyItemList().get(0).getCpCPhyWarehouseId());
                    relation.setUser(user);
                    taskService.orderCancel(relation);
                }catch (Exception e){
                    log.error(" 取消时效订单转换，YY取消占单异常：{}", Throwables.getStackTraceAsString(e));
                }

                /**如果已经寻仓 则调用逻辑仓释放接口，否则调用共享层释放接口*/
                if(TimeOrderVipStatusEnum.SEEKING_STORE_SUCCESS.getValue().equals(status)){
                    //查询寻仓单
                    IpJitxDeliveryRelation orderInfo = ipJitxDeliveryService.selectJitxDelivery(orderSn);
                    if(orderInfo == null || orderInfo.getJitxDelivery() == null){
                        log.error("TimeOrderVoidSgSendService.voidSgSend:时效订单 occupiedOrderSn：{} 取消库存占用失败，寻仓单查询为空", occupiedOrderSn);
                        continue;
                    }
                    //取消逻辑层占用
                    v14 = cancelWarehouseOccupy(orderInfo,timeOrderInfo,request.getUser());
                }else if(TimeOrderVipStatusEnum.CREATED.getValue().equals(status)){
                    //未占单 则直接更新时效订单为：已取消
                }else{
                    //取消共享层占用
                    v14 = cancelShareOccupy(timeOrderVip,request.getUser());
                }
            }
            if(v14.isOK()){
                //更新时效订单为：已取消
                ipVipTimeOrderService.updateTimeOrderIsactiveN(occupiedOrderSn,request.getUser(),request.getRemark());
            }else{
                IpBTimeOrderVip updateTimeOrder = new IpBTimeOrderVip();
                updateTimeOrder.setModifieddate(new Date());
                updateTimeOrder.setModifierid(Long.valueOf(user.getId()));
                updateTimeOrder.setModifiername(user.getName());
                updateTimeOrder.setModifierename(user.getEname());
                updateTimeOrder.setSysremark(v14.getMessage());
                mapper.update(updateTimeOrder,
                        new QueryWrapper<IpBTimeOrderVip>()
                                .in("occupied_order_sn",occupiedOrderSn));
            }
        }

        return v14;
        /*// 入参list
        List<SgSendBillVoidRequest> requestSgList = new ArrayList<>();
        for (IpBTimeOrderVip ipBTimeOrderVipOrderSn : timeOrderVips) {
            SgSendBillVoidRequest sgSendBillVoidRequest = new SgSendBillVoidRequest();
            sgSendBillVoidRequest.setSourceBillId(ipBTimeOrderVipOrderSn.getId());
            sgSendBillVoidRequest.setSourceBillNo(ipBTimeOrderVipOrderSn.getBillNo());
            sgSendBillVoidRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
            sgSendBillVoidRequest.setIsExist(true);
            requestSgList.add(sgSendBillVoidRequest);
        }
        if (CollectionUtils.isEmpty(requestSgList)) {
            return;
        }
        List<List<SgSendBillVoidRequest>> lists = ListSplitUtil.splitList(requestSgList,
                vipcomTimeOrderConfing.getTimeOrderReleaseStockNum());
        for (List<SgSendBillVoidRequest> list : lists) {
            if (log.isDebugEnabled()) {
                log.debug("批量释放SG时效订单库存，当前批次条数：{}", list.size());
            }
            ValueHolderV14<SgSendBatchVoidResult> vh = sgRpcService.batchVoidSgSend(list, user);
            if (vh.getCode() == ResultCode.FAIL) {
                if (log.isDebugEnabled()) {
                    log.debug("释放时效订单当前批次失败：{}", JSON.toJSONString(vh));
                }
                list.forEach(sgSendBillVoidRequest -> {
                    try {
                        Long sourceBillId = sgSendBillVoidRequest.getSourceBillId();
                        IpBTimeOrderVip timeOrderVip = collect.get(sourceBillId).get(0);
                        IpBTimeOrderVip ipbTimeOrderVipNew = new IpBTimeOrderVip();
                        ipbTimeOrderVipNew.setModifierid(Long.valueOf(user.getId()));
                        ipbTimeOrderVipNew.setModifiername(user.getName());
                        ipbTimeOrderVipNew.setModifierename(user.getEname());
                        ipbTimeOrderVipNew.setModifieddate(new Date());
                        ipbTimeOrderVipNew.setSysremark(vh.getMessage());
                        QueryWrapper<IpBTimeOrderVip> wrapper = new QueryWrapper<>();
                        wrapper.eq("OCCUPIED_ORDER_SN", timeOrderVip.getOccupiedOrderSn());
                        mapper.update(ipbTimeOrderVipNew, wrapper);
                    } catch (Exception e) {
                        log.error("时效订单释放库存失败更新备注异常，", e);
                    }
                });
            } else {
                SgSendBatchVoidResult data = vh.getData();
                List<SgSendVoidResult> voidResults = data.getVoidResults();
                if (log.isDebugEnabled()) {
                    log.debug("批量释放SG时效订单库存，返回集合条数：{}", voidResults.size());
                }
                for (SgSendVoidResult result : voidResults) {
                    try {
                        IpBTimeOrderVip timeOrderVip = collect.get(result.getSourceBillId()).get(0);
                        IpBTimeOrderVip ipbTimeOrderVipNew = new IpBTimeOrderVip();
                        ipbTimeOrderVipNew.setModifierid(Long.valueOf(user.getId()));
                        ipbTimeOrderVipNew.setModifiername(user.getName());
                        ipbTimeOrderVipNew.setModifierename(user.getEname());
                        ipbTimeOrderVipNew.setModifieddate(new Date());
                        QueryWrapper<IpBTimeOrderVip> wrapper = new QueryWrapper<>();
                        wrapper.eq("OCCUPIED_ORDER_SN", timeOrderVip.getOccupiedOrderSn());
                        if (ResultCode.FAIL == result.getCode()) {
                            if (log.isDebugEnabled()) {
                                log.debug("当前时效订单SG释放失败：{}", JSON.toJSONString(result));
                            }
                            ipbTimeOrderVipNew.setSysremark(result.getMessage());
                        } else {
                            if (request.getIsCancel() != null && request.getIsCancel()) {
                                ipbTimeOrderVipNew.setStatus(TimeOrderVipStatusEnum.CANCELLED.getValue());
                            } else {
                                ipbTimeOrderVipNew.setStatus(TimeOrderVipStatusEnum.MATCHED.getValue());
                            }
                            ipbTimeOrderVipNew.setSysremark(request.getRemark());
                        }
                        int update = mapper.update(ipbTimeOrderVipNew, wrapper);
                        boolean isMatch = request.getIsCancel() == null || !request.getIsCancel();
                        if (update > 0 && isMatch) {
                            timeOrderVipOccupyItemMapper.updateMatchedByOrderId(result.getSourceBillId());
                        }
                    } catch (Exception e) {
                        log.error("时效订单释放库存更新备注异常，", e);
                    }
                }
            }
        }*/
    }

    /**
     * 取消共享层占用
     * @param timeOrderVip
     * @return
     */
    public ValueHolderV14 cancelShareOccupy(IpBTimeOrderVip timeOrderVip, User operateUser) {
        SgBShareOutBillVoidRequest request = new SgBShareOutBillVoidRequest();
        SgBShareOutVoidRequest voidRequest = new SgBShareOutVoidRequest();
        voidRequest.setSourceBillId(timeOrderVip.getId());
        voidRequest.setSourceBillNo(timeOrderVip.getOccupiedOrderSn());
        voidRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
        voidRequest.setSourceBillDate(timeOrderVip.getCreateDate());
        request.setLoginUser(operateUser);
        request.setShareOutVoidRequest(voidRequest);
        return sgRpcService.voidShareOut(request);
    }

    /**
     * 取消逻辑层占用
     * @param
     * @return
     */
    public ValueHolderV14 cancelWarehouseOccupy(IpJitxDeliveryRelation orderInfo,IpVipTimeOrderRelation timeOrderRelation, User operateUser) {
        String timeOrderNo = timeOrderRelation.getOccupiedOrderSn();
        IpBJitxDelivery ipBJitxDelivery = orderInfo.getJitxDelivery();
        List<IpBJitxDeliveryItemEx> jitxDeliveryItemList = orderInfo.getJitxDeliveryItemList();
        Long timeOrderId = timeOrderRelation.getOrderId();

        Map<String, List<IpBJitxDeliveryItemEx>> itxDeliveryMap = jitxDeliveryItemList.stream()
                .collect(Collectors.groupingBy(IpBJitxDeliveryItemEx::getBarcode));

        SgOmsShareOutRequest request = new SgOmsShareOutRequest();
        request.setSourceBillId(ipBJitxDelivery.getId());
        request.setSourceBillNo(ipBJitxDelivery.getOrderSn());
        request.setTid(ipBJitxDelivery.getOrderSn());
        request.setLoginUser(operateUser);
        /**取消类型 01：明细取消，02：主表取消*/
        request.setCancelType("01");
        request.setSourceBillType(30);
        //明细
        List<SgOmsShareOutItemRequest> itemRequestList = new ArrayList<>();
        List<IpBTimeOrderVipOccupyItem> timeOrderVipOccupyItems = timeOrderRelation.getIpBTimeOrderVipOccupyItemList().stream().filter(x -> TimeOrderOccupyItemStatusEnum.OCCUPY_SUCCESS.getValue() == x.getStatus().intValue()).collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(timeOrderVipOccupyItems)){
            BigDecimal amount = timeOrderVipOccupyItems.stream()
                    .map(IpBTimeOrderVipOccupyItem::getAmount)
                    .reduce(BigDecimal.ZERO,BigDecimal::add);
            IpBTimeOrderVipOccupyItem occupyItem = timeOrderVipOccupyItems.get(0);
            SgOmsShareOutItemRequest itemRequest = new SgOmsShareOutItemRequest();
            itemRequest.setQtyPreout(amount);
            itemRequest.setPsCSkuId(occupyItem.getPsCSkuId());
            itemRequest.setPsCSkuEcode(occupyItem.getPsCSkuEcode());
            itemRequest.setSourceItemId(timeOrderId);
            itemRequestList.add(itemRequest);
        }else{
            log.error("{}.TimeOrderVoidSgSendService.cancelWarehouseOccupy 取消时效订单,OccupiedOrderSn:{} 对应时效订单占用成功的明细为空"
                    , this.getClass().getSimpleName(), timeOrderNo);
        }
        request.setItemRequestList(itemRequestList);
        return sgRpcService.voidSgOmsShareOut(request, null, null);
    }

    /**
     * 寻仓单指定仓库占单，释放时效单占用
     * @param timeOrderRelation
     * @param operateUser
     */
    public void assignStoreCancelShareOccupy(IpVipTimeOrderRelation timeOrderRelation, User operateUser){
        IpBTimeOrderVip ipBTimeOrderVip = timeOrderRelation.getIpBTimeOrderVip();
        List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipOccupyItemList = timeOrderRelation.getIpBTimeOrderVipOccupyItemList();
        Long phyWarehouseId = ipBTimeOrderVipOccupyItemList.get(0).getCpCPhyWarehouseId();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("寻仓单OrderSn:{}虚拟寻仓后寻源占单，时效单occupiedOrderSn:{}开始释放占用,phyWarehouseId:{}",
                    "assignStoreCancelShareOccupy",ipBTimeOrderVip.getOrderSn(),ipBTimeOrderVip.getOccupiedOrderSn()),ipBTimeOrderVip.getOrderSn(),ipBTimeOrderVip.getOccupiedOrderSn(),phyWarehouseId);
        }
        if(!phyWarehouseId.equals(-1)){
            cancelShareOccupy(ipBTimeOrderVip,operateUser);
        }
    }

}

