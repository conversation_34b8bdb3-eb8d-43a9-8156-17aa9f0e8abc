package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.oc.oms.es.ES4IpVipReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBVipReturnOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBVipReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrder;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrderItemEx;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 15:56 2020/5/9
 * description ：
 * @ Modified By：
 */
@Service
@Slf4j
public class IpVipReturnOrderService {
    @Autowired
    private IpBVipReturnOrderMapper vipReturnOrderMapper;

    @Autowired
    private IpBVipReturnOrderItemMapper vipReturnOrderItemMapper;

    public IpVipReturnOrderRelation selectVipReturnOrderRelation(String orderNo) {
        IpVipReturnOrderRelation ipVipReturnOrderRelation = new IpVipReturnOrderRelation();
        IpBVipReturnOrder vipReturnOrder = vipReturnOrderMapper.selectVipReturnOrderByOrderSnAndTransStatus(orderNo);
        if (vipReturnOrder != null) {
            /*if (vipReturnOrder.getTransStatus() == TransferOrderStatus.TRANSFERFAIL.toInteger() && vipReturnOrder.getTransNums() >= 5) {
                return null;
            }*/
            ipVipReturnOrderRelation.setVipReturnOrder(vipReturnOrder);
            List<IpBVipReturnOrderItemEx> vipReturnOrderItems = vipReturnOrderItemMapper.selectVipReturnOrderItemByForeignId(vipReturnOrder.getId());
            ipVipReturnOrderRelation.setVipReturnOrderItems(vipReturnOrderItems);
        }
        return ipVipReturnOrderRelation;
    }

    /**
     * 更新退供单中间表系统备注，转换状态，转换次数，转换时间
     *
     * @param remark      系统备注
     * @param orderNo     退供单号
     * @param transStatus 转换状态
     */
    public boolean updateRemark(String remark, String orderNo, int transStatus) {
        if (remark.length() > 500) {
            remark = remark.substring(0, 500);
        }
        int i = vipReturnOrderMapper.updateRemark(remark, orderNo, transStatus);
//        if (i > 0) {
//            try {
//                if (SpecialElasticSearchUtil.isUseIndexDocument()) {
//                    IpBVipReturnOrder vipReturnOrder = vipReturnOrderMapper.selectVipReturnOrderByOrderSn(orderNo);
//                    if (!SpecialElasticSearchUtil.indexExists(OcElasticSearchIndexResources.IP_B_VIP_RETURN_ORDER_INDEX_NAME)) {
//                        SpecialElasticSearchUtil.indexCreate(IpBVipReturnOrderItem.class, IpBVipReturnOrder.class);
//                    }
//                    SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.IP_B_VIP_RETURN_ORDER_INDEX_NAME,
//                            OcElasticSearchIndexResources.IP_B_VIP_RETURN_ORDER_TYPE_NAME,
//                            vipReturnOrder, vipReturnOrder.getId());
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
        return i > 0;
    }

    /**
     * SG回写退供单信息，更新退供单中间表系统备注，转换状态，转换时间
     *
     * @param remark      系统备注
     * @param orderNo     退供单号
     * @param transStatus 转换状态
     */
    public boolean updateTransferStatus(String remark, String orderNo, int transStatus) {
        if (remark.length() > 500) {
            remark = remark.substring(0, 500);
        }
        int i = vipReturnOrderMapper.updateTransferStatus(remark, orderNo, transStatus);
//        if (i > 0) {
//            try {
//                if (SpecialElasticSearchUtil.isUseIndexDocument()) {
//                    IpBVipReturnOrder vipReturnOrder = vipReturnOrderMapper.selectVipReturnOrderByOrderSn(orderNo);
//                    if (!SpecialElasticSearchUtil.indexExists(OcElasticSearchIndexResources.IP_B_VIP_RETURN_ORDER_INDEX_NAME)) {
//                        SpecialElasticSearchUtil.indexCreate(IpBVipReturnOrderItem.class, IpBVipReturnOrder.class);
//                    }
//                    SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.IP_B_VIP_RETURN_ORDER_INDEX_NAME,
//                            OcElasticSearchIndexResources.IP_B_VIP_RETURN_ORDER_TYPE_NAME,
//                            vipReturnOrder, vipReturnOrder.getId());
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
        return i > 0;
    }


    /**
     * 从ES中查询未转换成功的单据信息
     *
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return 单据编号列表
     */
    public List<String> selectUnTransferredReturnOrderFromEs(int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();
        try {
            orderNoList = ES4IpVipReturnOrder.getReturnSnByTransStatus(pageIndex, pageSize);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("selectUnTransferredOrderFromEs", ex);
        }
        return orderNoList;
    }
}
