package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.hub.model.minipt.TmmReturnLogisticNoReq;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.rpc.HubRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 通用商品发货后退单 转化服务
 *
 * @author: 夏继超
 * @since: 2019/7/13
 * create at : 2019/7/13 15:32
 */
@Slf4j
@Component
public class IpStandplatRefundService {
    //退换货主表
    private static final String OC_B_RETURN_ORDER_NAME = "OC_B_RETURN_ORDER";
    //退换货明细表
    private static final String OC_B_RETURN_ORDER_REFUND_NAME = "OC_B_RETURN_ORDER_REFUND";
    @Autowired
    public PsRpcService psRpcService;
    @Autowired
    protected OcSaveChangingOrRefundingService changingOrRefundingService;
    @Autowired
    IpBStandplatRefundMapper standplatRefundMapper;
    @Autowired
    OcBReturnOrderMapper returnOrderMapper;
    @Autowired
    IpBStandplatRefundItemMapper standplatRefundItemMapper;
    @Autowired
    OcBReturnOrderRefundMapper orderRefundMapper;
    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    OcBOrderItemMapper itemMapper;
    @Autowired
    OcCancelChangingOrRefundService cancelChangingOrRefundService;
    @Autowired
    IpJingdongRefundService refundService;
    @Autowired
    OcBReturnOrderRefundMapper refundMapper;
    @Autowired
    BuildSequenceUtil sequenceUtil;
    @Autowired
    HubRpcService hubRpcService;


    /**
     * @param ipBStandplatRefund
     * @param operateUser
     */
    public void checkStatus(IpBStandplatRefund ipBStandplatRefund, User operateUser) {
        //若不存在，则更新转换状态为未转换（0），系统备注：“不存在原始订单”；如果当前时间据退单的创建时间超过3天仍未找到原单，
        // 则超时标记为已转换2，修改系统备注：“不存在原始订单,超过3天,系统自动标记已转换
        Date createDate = ipBStandplatRefund.getCreationdate();
        Date currentDate = new Date();
        long overTime = 3 * 24 * 60 * 60 * 1000L + createDate.getTime();
        if (overTime < currentDate.getTime()) {
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    SysNotesConstant.SYS_REMARK35, ipBStandplatRefund);
        } else {
            this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                    SysNotesConstant.SYS_REMARK1, ipBStandplatRefund);
        }
    }

    /**
     * 出现异常时更新状态并插入日志
     */
    public void updateRefundIsTransError(IpBStandplatRefund ipBStandplatRefund, String error) {
        String sysRemark = SysNotesConstant.SYS_REMARK0;
        //异常信息超过500 截取500
        String str = sysRemark + error;
        if (str.length() > 500) {
            str = str.substring(0, 500);
        }
        updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                str, ipBStandplatRefund);
    }

    /**
     * 更新 中间表主表状态 和 备注
     *
     * @param toInteger          装换状态
     * @param sysRemark22        备注
     * @param ipBStandplatRefund 通用退单中间表
     */
    public void updateReturnOrder(int toInteger, String sysRemark22, IpBStandplatRefund ipBStandplatRefund) {
        IpBStandplatRefund refund = new IpBStandplatRefund();
        if (ipBStandplatRefund.getTransFailReason() != null) {
            refund.setTransFailReason(ipBStandplatRefund.getTransFailReason());
        }
        refund.setSysremark(sysRemark22);
        refund.setIstrans(toInteger);
        refund.setModifieddate(new Date());
        LambdaQueryWrapper<IpBStandplatRefund> wrapper = new LambdaQueryWrapper<IpBStandplatRefund>();
        List<Integer> status = new ArrayList<>();
        status.add(TransferOrderStatus.NOT_TRANSFER.toInteger());
        status.add(TransferOrderStatus.TRANSFERRING.toInteger());
        wrapper.in(IpBStandplatRefund::getIstrans, status.toArray());
        wrapper.eq(IpBStandplatRefund::getReturnNo, ipBStandplatRefund.getReturnNo());
        standplatRefundMapper.update(refund, wrapper);
    }

    /**
     * 团买买售后物流单号查询
     *
     * @param platformId
     */
    public void searchReturnLogisticNo(Integer platformId) {
        //获取当前时间往前一个月的date
        Date date = new Date();
        date.setTime(date.getTime() - 30 * 24 * 60 * 60 * 1000L);

        List<IpBStandplatRefund> standplatRefunds = standplatRefundMapper.selectNoLogisticNo(platformId, date);
        if (CollectionUtils.isEmpty(standplatRefunds)) {
            return;
        }

        List<TmmReturnLogisticNoReq.Item> items = Lists.newArrayList();
        for (IpBStandplatRefund standplatRefund : standplatRefunds) {
            TmmReturnLogisticNoReq.Item item = new TmmReturnLogisticNoReq.Item();
            item.setTid(standplatRefund.getOrderNo());
            item.setReturnNo(standplatRefund.getReturnNo());
            items.add(item);
        }

        TmmReturnLogisticNoReq req = new TmmReturnLogisticNoReq();
        req.setItemList(items);
        hubRpcService.searchReturnLogisticNo(req);
    }

}
