package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.enums.MatchAbnormalHandleTypeEnum;
import com.jackrain.nea.st.model.enums.MatchAbnormalHandleUnitEnum;
import com.jackrain.nea.st.model.table.StCMatchAbnormalStrategy;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * description:异常数据打标签
 * @Author: liuwenjin
 * @Date 2022/10/16 18:15
 */
@Slf4j
@Component
public class OrderExceptionTagService {

    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOccupyTaskMapper ocBOccupyTaskMapper;
    @Autowired
    private OmsReturnUtil omsReturnUtil;
    @Autowired
    private OcBOrderHoldItemService ocBOrderHoldItemService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderHoldService holdService;

    /**
     * description:匹配异常
     *
     * @Author: liuwenjin
     * @Date 2022/10/16 18:38
     */
    public void checkMateException(OcBOrder order, String msg, Integer type) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" matchAbnormal 异常信息匹配开始 msg:{},type:{}"), msg, type);
        }
        if (StringUtils.isEmpty(msg) || StringUtils.isEmpty(type)){
            return;
        }
        order.setIsException(OcBOrderConst.IS_STATUS_IY);
        order.setExceptionType(type.toString());
        //调用异常定义接口
        StCMatchAbnormalStrategy strategy = stRpcService.matchAbnormal(msg, type);
        if (Objects.nonNull(strategy)) {
            try {
                handleAbnormal(order, strategy);
            } catch (Exception e) {
                log.error(LogUtil.format(" matchAbnormal 异常信息执行异常失败 msg:{},type:{}, errorMsg:{}"), msg, type, e.getMessage(),e);
                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                        OrderLogTypeEnum.ORDER_EXCEPTION.getKey(), "异常匹配执行卡单/hold时异常", "", e.getMessage(),
                        SystemUserResource.getRootUser());
            }
        } else {
            if (!StringUtils.isEmpty(order.getExceptionExplain())) {
                //匹配不到就清空
                order.setExceptionExplain("");
            }
        }
        ocBOrderMapper.updateById(order);
    }

    private void handleAbnormal(OcBOrder order, StCMatchAbnormalStrategy strategy) {
        order.setExceptionExplain(strategy.getAbnormalDesc());
        // 匹配到之后 需要判断是仅打标还是需要卡单或者hold单 MatchAbnormalHandleTypeEnum
        Integer abnormalHandleType = strategy.getAbnormalHandleType();
        if (ObjectUtil.isNull(abnormalHandleType) || ObjectUtil.equal(abnormalHandleType, MatchAbnormalHandleTypeEnum.ONLY_TAG.getValue())) {
            return;
        }
        OcBOrder ocBOrder = ocBOrderMapper.selectById(order.getId());
        Integer orderStatus = ocBOrder.getOrderStatus();
        Long abnormalHandleTime = strategy.getAbnormalHandleTime();
        Integer abnormalHandleUnit = strategy.getAbnormalHandleUnit();
        DateField dateField = null;
        // 将单位转换
        if (ObjectUtil.equal(abnormalHandleUnit, MatchAbnormalHandleUnitEnum.MINUTE.getValue())) {
            dateField = DateField.MINUTE;
        } else if (ObjectUtil.equal(abnormalHandleUnit, MatchAbnormalHandleUnitEnum.HOUR.getValue())) {
            dateField = DateField.HOUR;
        } else if (ObjectUtil.equal(abnormalHandleUnit, MatchAbnormalHandleUnitEnum.DAY.getValue())) {
            dateField = DateField.DAY_OF_YEAR;
        }

        // 待分配/待传wms/仓库发货/平台发货/取消/作废,不处理
        if (ObjectUtil.equal(orderStatus, OmsOrderStatus.TO_BE_ASSIGNED.toInteger()) || ObjectUtil.equal(orderStatus, OmsOrderStatus.PENDING_WMS.toInteger()) ||
                ObjectUtil.equal(orderStatus, OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger()) || ObjectUtil.equal(orderStatus, OmsOrderStatus.PLATFORM_DELIVERY.toInteger()) ||
                ObjectUtil.equal(orderStatus, OmsOrderStatus.CANCELLED.toInteger()) || ObjectUtil.equal(orderStatus, OmsOrderStatus.SYS_VOID.toInteger())) {
            return;
        }
        boolean toBOrder = OmsBusinessTypeUtil.isToBOrder(ocBOrder);
        boolean toCOrder = OmsBusinessTypeUtil.isSapToCOrder(ocBOrder);
        if (ObjectUtil.equal(abnormalHandleType, MatchAbnormalHandleTypeEnum.TAG_DETENTION.getValue())) {
            if (toBOrder || toCOrder) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "执行异常匹配时,该业务类型不执行卡单策略", "", "",
                        SystemUserResource.getRootUser());
                return;
            }
        }
        if (ObjectUtil.equal(abnormalHandleType, MatchAbnormalHandleTypeEnum.TAG_HOLD.getValue())) {
            if (toBOrder || toCOrder) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_HOLD.getKey(), "执行异常匹配时,该业务类型不执行hold单策略", "", "",
                        SystemUserResource.getRootUser());
                return;
            }
        }

        // 已审核/配货中 需要调用反审核
        if (ObjectUtil.equal(orderStatus, OmsOrderStatus.CHECKED.toInteger()) || ObjectUtil.equal(orderStatus, OmsOrderStatus.IN_DISTRIBUTION.toInteger())) {
            omsReturnUtil.toExamineOrderLock(ocBOrder, SystemUserResource.getRootUser());
        }

        if (ObjectUtil.equal(abnormalHandleType, MatchAbnormalHandleTypeEnum.TAG_DETENTION.getValue())) {
            if ((ObjectUtil.isNotNull(ocBOrder.getIsDetention()) && ocBOrder.getIsDetention() == 1) || (ObjectUtil.isNotNull(ocBOrder.getIsInterecept()) && ocBOrder.getIsInterecept() == 1)) {
                return;
            }
            // 打标并卡单
            // 待审核/待寻源:(订单已经带卡则不执行) 调用卡单服务
            if (ObjectUtil.equal(ocBOrder.getOrderStatus(), OmsOrderStatus.UNCONFIRMED.toInteger()) || ObjectUtil.equal(ocBOrder.getOrderStatus(), OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())
                    || ObjectUtil.equal(ocBOrder.getOrderStatus(), OmsOrderStatus.OCCUPY_IN.toInteger())) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("IS_AUTO_RELEASE", "Y");
                jsonObject.put("HOLD_DETENTION_ORDER_REASON", "206");
                jsonObject.put("HOLD_ORDER_REASON_MSG", "自动卡单。发货单异常类型定义:打标并卡单， 释放时间：" + DateUtil.formatDateTime(DateUtil.offset(new Date(), dateField, Math.toIntExact(abnormalHandleTime))));
                jsonObject.put("RELEASE_TIME_TYPE", "1");
                jsonObject.put("RELEASE_TIME", DateUtil.formatDateTime(DateUtil.offset(new Date(), dateField, Math.toIntExact(abnormalHandleTime))));
                holdService.detainWithRedisLock(ocBOrder.getId(), jsonObject, SystemUserResource.getRootUser(), new ValueHolder());
                order.setSysremark("发货单异常类型定义打标并卡单！");
            }
        } else if (ObjectUtil.equal(abnormalHandleType, MatchAbnormalHandleTypeEnum.TAG_HOLD.getValue())) {
            if ((ObjectUtil.isNotNull(ocBOrder.getIsDetention()) && ocBOrder.getIsDetention() == 1) || (ObjectUtil.isNotNull(ocBOrder.getIsInterecept()) && ocBOrder.getIsInterecept() == 1)) {
                return;
            }
            // 打标并hold单
            if (ObjectUtil.equal(ocBOrder.getOrderStatus(), OmsOrderStatus.UNCONFIRMED.toInteger()) || ObjectUtil.equal(ocBOrder.getOrderStatus(), OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())
                    || ObjectUtil.equal(ocBOrder.getOrderStatus(), OmsOrderStatus.OCCUPY_IN.toInteger())) {
                ocBOrderHoldItemService.businessHold(order.getId(), OrderHoldReasonEnum.OC_B_ORDER_ERROR_HOLD, DateUtil.offset(new Date(), dateField, Math.toIntExact(abnormalHandleTime)),
                        "自动Hold单。发货单异常类型定义:打标并Hold单， 释放时间：" + DateUtil.formatDateTime(DateUtil.offset(new Date(), dateField, Math.toIntExact(abnormalHandleTime))));
                order.setSysremark("发货单异常类型定义打标并Hold单！");
            }
        }
    }
}
