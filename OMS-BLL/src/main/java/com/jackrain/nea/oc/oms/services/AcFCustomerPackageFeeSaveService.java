package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.AcFCustomerPackageFeeMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.AcFCustomerPackageFee;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;

/**
 * className: AcFCustomerPackageFeeSaveService
 * description:经销商打包费用设置新增/保存服务
 *
 * <AUTHOR>
 * create: 2021-06-19
 * @since JDK 1.8
 */
@Component
public class AcFCustomerPackageFeeSaveService extends CommandAdapter {

    @Autowired
    private AcFCustomerPackageFeeMapper customerPackageFeeMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        User user = querySession.getUser();
        ValueHolder valueHolder = CommandAdapterUtil.checkSaveSession(querySession, OcCommonConstant.AC_F_CUSTOMER_PACKAGE_FEE);
        if (!valueHolder.isOK()) {
            return valueHolder;
        }

        JSONObject fixColumn = (JSONObject)valueHolder.getData().get(OcCommonConstant.FIX_COLUMN);
        AcFCustomerPackageFee packageFee = ((JSONObject)fixColumn.get(OcCommonConstant.AC_F_CUSTOMER_PACKAGE_FEE)).toJavaObject(AcFCustomerPackageFee.class);
        Long id = (Long)((HashMap)valueHolder.getData().get("data")).get(OcCommonConstant.OBJ_ID);
        //校验经销商唯一性
        if(!checkUnique(id,packageFee)){
            valueHolder = ValueHolderUtils.getFailValueHolder("已存在经销商记录，不允许保存");
            return valueHolder;
        }

        //框架字段赋值
        packageFee.setId(id);
        CommandAdapterUtil.defaultOperator(packageFee,user);
        if (id < 0) {
            //新增
            id = ModelUtil.getSequence(OcCommonConstant.AC_F_CUSTOMER_PACKAGE_FEE);
            packageFee.setId(id);
            packageFee.setOwnerename(user.getEname());
            packageFee.setModifierename(user.getEname());
            customerPackageFeeMapper.insert(packageFee);
        }else {
            //更新
            packageFee.setModifierename(user.getEname());
            customerPackageFeeMapper.updateById(packageFee);
        }

        valueHolder = ValueHolderUtils.getSuccessValueHolder(id,OcCommonConstant.AC_F_CUSTOMER_PACKAGE_FEE,"保存成功");
        return valueHolder;
    }

    private boolean checkUnique(Long id,AcFCustomerPackageFee packageFee){
        Long customerId = packageFee.getCpCCustomerId();

        //如果经销商为空，直接校验通过
        if(ObjectUtils.isEmpty(customerId)){
            return true;
        }

        List<AcFCustomerPackageFee> storeKpiList = customerPackageFeeMapper
                .selectList(new QueryWrapper<AcFCustomerPackageFee>().lambda()
                .eq(AcFCustomerPackageFee::getCpCCustomerId,customerId)
                .ne(AcFCustomerPackageFee::getId,id));

        return CollectionUtils.isEmpty(storeKpiList);
    }
}
