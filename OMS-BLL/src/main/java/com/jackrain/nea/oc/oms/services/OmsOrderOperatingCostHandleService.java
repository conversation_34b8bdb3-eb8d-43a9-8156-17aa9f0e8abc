package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOperatingCostPlanType;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCOperationcostDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 操作费处理后端服务
 *
 * @author: hulinyang
 * @since: 2019/4/8
 * create at : 2019/4/8 14:37
 */
@Component
@Slf4j
public class OmsOrderOperatingCostHandleService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private StRpcService stRpcService;

    /**
     * 操作费处理服务入口
     *
     * @param orderId  订单id
     * @param PlanType 订单所属操作费方案类型
     * @return boolean
     */

    public ValueHolderV14 operatingCostHandle(Long orderId, int PlanType, User user) {

        ValueHolderV14 vh = new ValueHolderV14();

        try {
            OcBOrder ocBOrder = omsOrderService.selectOrderInfo(orderId);
            if (ocBOrder == null) {
                vh.setCode(-1);
                vh.setMessage("当前订单已不存在!");
                return vh;
            }
            //B2B订单处理
            if (PlanType == OmsOperatingCostPlanType.B2B_ORDER_HANDLE.toInteger()) {

            } else if (PlanType == OmsOperatingCostPlanType.B2B_ORDER_REFUNDS.toInteger()) {
                //B2B订单退货
            } else if (PlanType == OmsOperatingCostPlanType.B2C_ORDER_HANDLE.toInteger()) {
                //B2C订单处理
                Long cpCPhyWarehouseId = ocBOrder.getCpCPhyWarehouseId();
                //查询实体仓存在于维护的操作费方案中实体仓中，并且当前日期在操作费方案有效期内的“操作方案”
                List<StCOperationcostDO> stCOperationcostList = stRpcService.selectStCOperationcostInfo(PlanType, cpCPhyWarehouseId);
                StCOperationcostDO stCOperationcost = stCOperationcostList.get(0);
                String stCOperationcostName = stCOperationcost.getEname();
                BigDecimal operatingPrice = calculationOperatingCostRule(ocBOrder, stCOperationcost);
                vh.setData(operatingPrice);

            } else if (PlanType == OmsOperatingCostPlanType.B2C_ORDER_REFUNDS.toInteger()) {
                //B2C订单换货
            } else if (PlanType == OmsOperatingCostPlanType.JINGDONG_FLASH_PURCHASE_ORDER.toInteger()) {
                //京东闪购订单
            } else if (PlanType == OmsOperatingCostPlanType.VIP_ORDER_JITX.toInteger()) {
                //唯品会JIT订单
            } else {

            }
        } catch (Exception ex) {
            log.error(LogUtil.format("订单出库服务逻辑执行异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
        vh.setCode(0);
        vh.setMessage("订单出库成功");
        return vh;
    }

    /**
     * 按操作费方案上的计算规则，计算出操作费
     *
     * @param ocBOrder         订单对象
     * @param stCOperationcost 操作费方案对象
     * @return boolean
     */
    public BigDecimal calculationOperatingCostRule(OcBOrder ocBOrder, StCOperationcostDO stCOperationcost) {
        String stCOperationcostName = stCOperationcost.getEname();
        //操作费价格
        BigDecimal operatePrice = BigDecimal.ZERO;
        List<OcBOrderItem> ocBOrderItemList = selectOrderItemList(ocBOrder);
        if (ocBOrderItemList.size() > 0) {
            //配件数量
            int partsNum = 0;
            //在订单明细表中，查询配件数量
            List<OcBOrderItem> partsNumList = selectPartsNum(ocBOrder);
            if (partsNumList != null) {
                partsNum = partsNumList.size();
            }
            //收费件数
            BigDecimal chargeunit = stCOperationcost.getChargeunit();
            //收费价格
            BigDecimal chargeprice = stCOperationcost.getChargeprice();
            //配件数量
            BigDecimal partsnum = new BigDecimal(partsNum);
            //配件价格
            BigDecimal accessoryprice = stCOperationcost.getAccessoryprice();
            //续费数量
            BigDecimal continuenum = ocBOrder.getQtyAll().subtract(chargeunit).subtract(partsnum);
            //续件价格
            BigDecimal continueprice = stCOperationcost.getContinueprice();
            if (ocBOrder.getQtyAll().compareTo(chargeunit) <= 0) {
                operatePrice = chargeprice;
            } else {
                //续费总价
                BigDecimal continueCost = continuenum.multiply(continueprice);
                //配件总价
                BigDecimal partsCost = partsnum.multiply(accessoryprice);
                //操作费价格 =  收费价格 + 续费总价 + 配件总价
                operatePrice = chargeprice.add(continueCost).add(partsCost);
            }
        }
        //MATERIELTYPE 物料类型
        return operatePrice;
    }

    /**
     * 根据订单编号查询，订单详情的列表
     *
     * @param ocBOrder 订单实体
     * @return List<OcBOrderItem> 订单详情列表
     */

    public List<OcBOrderItem> selectOrderItemList(OcBOrder ocBOrder) {
        return ocBOrderItemMapper.selectOrderItemList(ocBOrder.getId());
    }

    /**
     * @param ocBOrder 订单实体
     * @return List<OcBOrderItem> 带配件的订单明细列表
     */

    public List<OcBOrderItem> selectPartsNum(OcBOrder ocBOrder) {
        //在订单明细表中，查询配件数量
        return ocBOrderItemMapper.selectPartsNum(ocBOrder.getId());
    }


}
