package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.model.enums.InterceptStatus;
import com.jackrain.nea.oc.oms.model.enums.IsReservedEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.RefundReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnProTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.BasePermission;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.OrderReturnResult;
import com.jackrain.nea.oc.oms.model.result.QueryChangingOrRefundingResult;
import com.jackrain.nea.oc.oms.model.result.ReturnOrderWareHouse;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.permission.OcOrderAuthorityMgtService;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.zip.GZIPOutputStream;

import static com.jackrain.nea.resource.RedisKeyConst.CP_PLATFORM_ALL;

/**
 * 退换货订单列表查询
 *
 * @author: 郑立轩
 * @since: 2019/3/11
 * create at : 2019/3/11 15:07
 */
@Component
@Slf4j
public class CpQueryChangingOrRefundingService {

    private static final String GZIP_FLAG = "IS_GZIP";
    private static final String index = "oc_b_return_order";
    private static final String type = "oc_b_return_order";
    final String hKey = "OmsReturnOrderQuery";
    final String key = "cpCPhyWareHouse";
    /**
     * tab标签.预退货
     */
    private final String tabIdxKey = "tabIndex";
    /**
     * 入库仓库.预退货
     */
    private final String preReturnStore = "CP_C_PHY_WAREHOUSE_IN_ID";
    private final String preReturnLogistics = "LOGISTICS_CODE";
    @Autowired
    private OcBReturnOrderMapper mapper;
    @Autowired
    private OcBReturnOrderExchangeMapper exchangeMapper;
    @Autowired
    private OcBReturnOrderRefundMapper refundMapper;
    @Autowired
    private OcOrderAuthorityMgtService ocOrderAuthorityMgtService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private ChannelTypeService channelTypeService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    public JSONObject selectReturnStatus(JSONObject object) {
//        if (object.containsKey("RETURN_STATUS")) {
        JSONArray return_status = object.getJSONArray("RETURN_STATUS");
        JSONArray lable_return_status = object.getJSONArray("LABLE_RETURN_STATUS");
        if (return_status == null && lable_return_status == null) {
            object.remove("LABLE_RETURN_STATUS");
        }

        if (CollectionUtils.isEmpty(lable_return_status)) {
            object.remove("LABLE_RETURN_STATUS");
        }
        if (CollectionUtils.isEmpty(return_status)) {
            object.put("RETURN_STATUS", lable_return_status);
            object.remove("LABLE_RETURN_STATUS");
        }
        if (!CollectionUtils.isEmpty(lable_return_status) && !CollectionUtils.isEmpty(return_status)) {
            JSONArray array = new JSONArray();
            array.addAll(lable_return_status);
            for (Object returnStatus : return_status) {
                if (!lable_return_status.contains(returnStatus)) {
                    array.add(returnStatus);
                }
            }
            if (array.size() > 0) {
                object.put("RETURN_STATUS", array);
            } else {
                array.add("100");
                object.put("RETURN_STATUS", array);
            }

            object.remove("LABLE_RETURN_STATUS");
        }

//        }
        return object;
    }

    public ValueHolderV14 queryChangingOrRefunding(UserPermission usrPem, JSONObject object, User user) {

        log.info("CpQueryChangingOrRefundingService.queryChangingOrRefunding object = {}", JSONObject.toJSONString(object));

        boolean unGzip = isReturnGzip(object);
        ValueHolderV14 vh = new ValueHolderV14<>();
        QueryChangingOrRefundingResult result = new QueryChangingOrRefundingResult();
        try {
            // export get es data 1.1 author by xiWen. start
            boolean flag = false;
            final int resultCode = 3;
            if (object != null && object.containsKey("esExport")) {
                flag = true;
                object.remove("esExport");
            }
            // export get es data 1.2 author by xiWen. end
            object = selectReturnStatus(object);
            //获取分页显示的页数
            Integer start = object.getInteger("start");
            Integer toNaikaStatus = object.getInteger("toNaikaStatus");
            //获取每页显示的数量 默认50条
            int count = object.getInteger("count") == null ? 50 : object.getInteger("count");
            //转换实体
            object.remove("start");
            object.remove("count");
            //获取查询条件 判断是否为空 如果不为空则设置到ES
            JSONObject filter = new JSONObject();
            JSONObject chileKey = new JSONObject();
            JSONObject ESObject = findSelect(object, filter, chileKey);
            if (CollectionUtils.isEmpty(ESObject)) {
                ESObject.put("AD_CLIENT_ID", 37);
                ESObject.put("AD_ORG_ID", 27);
            }

            JSONArray orderKeys = new JSONArray();
            JSONObject order = new JSONObject();
            if(count==999999){
                order.put("asc", true);
                order.put("name", "ID");
            }else{
                order.put("asc", false);
                order.put("name", "MODIFIEDDATE");
            }

            orderKeys.add(order);
            String[] arr = {"ID"};
            // xiwen.start.permission
//            filterSearchCondition(usrPem, ESObject);

            // 预退货查询
         /*   if (preReturn != null) {
                afterPrevReturnCnt(preReturn, ESObject);
            }*/
            // xiwen.end
            //如果查找返回超过10000需要分页ES
            int totalSize = count;
            int pageSize = totalSize;
            //如果查找返回超过10000需要分页ES
            if(totalSize>10000){
                pageSize = 10000;
            }
            int maxTimes = totalSize/pageSize;
            if(totalSize%pageSize!=0){
                maxTimes += 1;
            }
            int times=0;
            Integer totalCount = 0;
            JSONArray data = new JSONArray();
            while(true){
                JSONObject search = ElasticSearchUtil.search(index, type, OcElasticSearchIndexResources.OC_B_RETURN_ORDER_REFUND_TYPE_NAME,
                        ESObject, filter, orderKeys, chileKey, pageSize, times*pageSize, arr);
                totalCount = search.getInteger("total");
                if(!CollectionUtils.isEmpty(search.getJSONArray("data"))){
                    data.addAll(search.getJSONArray("data"));
                }
                if(Objects.isNull(search) || search.getJSONArray("data").size()< pageSize || times>=maxTimes){
                    break;
                }
                times++;

            }
            log.info("CpQueryChangingOrRefundingService.queryChangingOrRefunding ESObject = {}", JSONObject.toJSONString(ESObject));

            if (totalCount == 0 || CollectionUtils.isEmpty(data)) {
                return reSetReturnResult(vh, result, start, count, unGzip);
            }

            //获取IDS编号数组
            JSONArray jsonArray = new JSONArray();
            // export get es data 1.2 author by xiWen. start
            if (flag) {
                for (int i = 0, l = data.size(); i < l; i++) {
                    JSONObject jsnObj = data.getJSONObject(i);
                    if (jsnObj == null) {
                        continue;
                    }
                    Long v = jsnObj.getLong("ID");
                    if (v == null) {
                        continue;
                    }
                    jsonArray.add(v);
                }
                vh.setData(jsonArray);
                vh.setCode(resultCode);
                return vh;
            }
            // export get es data 1.2 author by xiWen. end
            for (int i = 0; i < data.size(); i++) {
                String id = "'" + data.getJSONObject(i).getString("ID") + "'";
                jsonArray.add(id);
            }

            String join = StringUtils.join(jsonArray, ",");
            //通过数据库中获取列表数据
            List<OrderReturnResult> returnOrderDtos = mapper.findByIds(join, OcBOrderConst.IS_ACTIVE_YES);
            if (returnOrderDtos == null) {
                return reSetReturnResult(vh, result, start, count, unGzip);
            }
            //获取all-sku信息字段 编号id*，退换货*，货号ps_c_pro_ecode*，规格sku_spec*，退款金额refund_amt*，申请数量qty_refund*，入库数量qty_in*
            //条码id集合,key是主表id，value是对应的SKUID集合
            //只要退货表中存在 就一定有skuid，除非该订单为换货，没有退货商品，才会查询不到
            List<OcBReturnOrderRefund> rfnList = refundMapper.selectReturnRefundByPIds(join);
            List<JSONObject> rfnJsnList = refundMapper.selectReturnRefundSomeFile(join);
            List<OcBReturnOrderExchange> exgList = exchangeMapper.selectReturnExchageByPIds(join);
            List<JSONObject> exgJsnList = exchangeMapper.selectReturnExchangeSomeFile(join);

            Map<Long, List<OcBReturnOrderRefund>> rfnMap = rfnList.parallelStream()
                    .collect(Collectors.groupingBy(x -> x.getOcBReturnOrderId(), Collectors.toList()));
            Map<Long, List<OcBReturnOrderExchange>> exgMap = null;
            if (exgList != null && exgList.size() > ResultCode.SUCCESS) {
                exgMap = exgList.parallelStream()
                        .collect(Collectors.groupingBy(x -> x.getOcBReturnOrderId(), Collectors.toList()));
            }
            Map<Long, List<JSONObject>> rfnJsnMap = rfnJsnList.parallelStream()
                    .collect(Collectors.groupingBy(x -> x.getLong("oc_b_return_order_id"), Collectors.toList()));
            Map<Long, List<JSONObject>> exgJsnMap = null;
            if (exgJsnList != null && exgJsnList.size() > ResultCode.SUCCESS) {
                exgJsnMap = exgJsnList.parallelStream()
                        .collect(Collectors.groupingBy(x -> x.getLong("oc_b_return_order_id"), Collectors.toList()));
            }
            Set<Long> cpPhySets = new HashSet<>();
            collectWareHouseId(returnOrderDtos, cpPhySets);
            Map<Long, String> phyMaps = getFromRedis(cpPhySets);
            Map<Integer, String> returnTypeMap = ReturnProTypeEnum.getMap();
            Map<Integer, String> interceptStatusMap = InterceptStatus.getMap();
            Map<Integer, String> proReturnMap = ProReturnStatusEnum.getKeyValueMap();
            Map<Long, String> sapMap = OcBorderListEnums.SendSAPEnum.toMap();
            Map<Integer, String> isReservedMap = IsReservedEnum.convertAllToHashVal();
            // 所有平台信息
            HashOperations<String, String, String> hps = redisTemplate.opsForHash();
            Map<String, String> pm = hps.entries(CP_PLATFORM_ALL);
            for (OrderReturnResult returnOrderDto : returnOrderDtos) {
                List<OcBReturnOrderRefund> ocBReturnOrderRefunds = rfnMap.get(returnOrderDto.getId());
                List<OcBReturnOrderExchange> ocBReturnOrderExchanges = exgMap == null
                        ? null : exgMap.get(returnOrderDto.getId());
                convertFieldText(returnOrderDto, returnTypeMap, interceptStatusMap, proReturnMap);
                //便于导出功能扩展，返回结果增加退换货明细
                returnOrderDto.setOcBReturnOrderExchanges(ocBReturnOrderExchanges);
                returnOrderDto.setOcBReturnOrderRefunds(ocBReturnOrderRefunds);
                //扩展增加QTYIN
                BigDecimal qty = BigDecimal.ZERO;
                if (ocBReturnOrderRefunds != null) {
                    for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefunds) {
                        if (ocBReturnOrderRefund == null) {
                            continue;
                        }
                        BigDecimal qtyIn = ocBReturnOrderRefund.getQtyIn();
                        qtyIn = qtyIn == null ? BigDecimal.ZERO : qtyIn;
                        qty = qty.add(qtyIn);
                    }
                }
                returnOrderDto.setQtyIn(qty);
                //转换平台店铺
                if (returnOrderDto.getPlatform() == null) {
                    returnOrderDto.setPlatName("暂无平台信息");
                } else {
                    // 平台
                    String platform = dealPlatform(pm, returnOrderDto.getPlatform());
                    returnOrderDto.setPlatName(platform);
                }
                //转换退款原因
                String returnReason = returnOrderDto.getReturnReason();
                if (StringUtils.isEmpty(returnReason)) {
                    returnOrderDto.setReturnReason("");
                } else {
                    if (returnReason.equals(RefundReasonEnum.productReason.getVal())) {
                        returnOrderDto.setReturnReason(RefundReasonEnum.productReason.getKey());
                    }
                    if (returnReason.equals(RefundReasonEnum.productFailure.getVal())) {
                        returnOrderDto.setReturnReason(RefundReasonEnum.productFailure.getKey());
                    }
                    if (returnReason.equals(RefundReasonEnum.productSize.getVal())) {
                        returnOrderDto.setReturnReason(RefundReasonEnum.productSize.getKey());
                    }
                    if (returnReason.equals(RefundReasonEnum.serviceReason.getVal())) {
                        returnOrderDto.setReturnReason(RefundReasonEnum.serviceReason.getKey());
                    }
                    if (returnReason.equals(RefundReasonEnum.service.getVal())) {
                        returnOrderDto.setReturnReason(RefundReasonEnum.service.getKey());
                    }
                    if (returnReason.equals(RefundReasonEnum.serviceIgnored.getVal())) {
                        returnOrderDto.setReturnReason(RefundReasonEnum.serviceIgnored.getKey());
                    }
                    if (returnReason.equals(RefundReasonEnum.refuse.getVal())) {
                        returnOrderDto.setReturnReason(RefundReasonEnum.refuse.getKey());
                    }
                    if (returnReason.equals(RefundReasonEnum.fix.getVal())) {
                        returnOrderDto.setReturnReason(RefundReasonEnum.fix.getKey());
                    }
                    if (returnReason.equals(RefundReasonEnum.wontNeed.getVal())) {
                        returnOrderDto.setReturnReason(RefundReasonEnum.wontNeed.getKey());
                    }

                }
                String isReserved = isReservedMap.get(returnOrderDto.getIsReserved());
                returnOrderDto.setIsReservedName(isReserved == null ? "" : isReserved);
                Long cpCPhyWarehouseId = returnOrderDto.getCpCPhyWarehouseId();
                if (phyMaps != null) {
                    String tmpName = phyMaps.get(cpCPhyWarehouseId);
                    returnOrderDto.setCpCPhyWarehouseIdName(tmpName == null ? "" : tmpName);
                } else if (cpCPhyWarehouseId == null) {
                    returnOrderDto.setShopName("暂无实体仓信息");
                } else {
                    CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseId);
                    returnOrderDto.setShopName(cpCPhyWarehouse.getEname());
                    returnOrderDto.setCpCPhyWarehouseIdName(cpCPhyWarehouse.getEname());
                }
                // 入库实体仓
                Long cpCPhyWarehouseInId = returnOrderDto.getCpCPhyWarehouseInId();
                if (phyMaps != null) {
                    String tmpName = phyMaps.get(cpCPhyWarehouseInId);
                    returnOrderDto.setCpCPhyWarehouseInIdName(tmpName == null ? "" : tmpName);
                } else if (cpCPhyWarehouseInId == null) {
                    returnOrderDto.setCpCPhyWarehouseInIdName("");
                } else {
                    CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseInId);
                    returnOrderDto.setCpCPhyWarehouseInIdName(cpCPhyWarehouse.getEname());
                }

                //同步通用平台退款状态
                Integer platformRefundStatus = returnOrderDto.getPlatformRefundStatus();
                if(OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_INIT.equals(platformRefundStatus)){
                    returnOrderDto.setPlatformRefundStatusName("未同步");
                }else if(OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_SUCCESS.equals(platformRefundStatus)){
                    returnOrderDto.setPlatformRefundStatusName("同步成功");
                }else if(OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_FAIL.equals(platformRefundStatus)){
                    returnOrderDto.setPlatformRefundStatusName("同步失败");
                }

                // end
                Integer returnStatus = returnOrderDto.getReturnStatus();
                if (returnStatus.equals(ReturnStatusEnum.COMPLETION.getVal())) {
                    returnOrderDto.setReturnStatusName(ReturnStatusEnum.COMPLETION.getKey());
                } else if (returnStatus.equals(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal())) {
                    returnOrderDto.setReturnStatusName(ReturnStatusEnum.WAIT_RETURN_STORAGE.getKey());
                } else if (returnStatus.equals(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal())) {
                    returnOrderDto.setReturnStatusName(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getKey());
                } else if (returnStatus.equals(ReturnStatusEnum.CANCLE.getVal())) {
                    returnOrderDto.setReturnStatusName(ReturnStatusEnum.CANCLE.getKey());

                }
                // 确认状态
                //returnOrderDto.setConfirmTypeName(ReturnOrderConfirmStatusEnum.getValueName(returnOrderDto.getConfirmStatus()));

                // 转换是否 传wms 字段
                if (returnOrderDto.getIsNeedToWms() != null) {
                    if (returnOrderDto.getIsNeedToWms().equals(0L)) {
                        returnOrderDto.setIsPassWmsName("否");
                    } else {
                        returnOrderDto.setIsPassWmsName("是");
                    }
                }

                if (returnOrderDto.getSigningStatus() != null) {
                    if (returnOrderDto.getSigningStatus().equals("0")) {
                        returnOrderDto.setSigningStatus("未签收");
                    } else if (returnOrderDto.getSigningStatus().equals("1")) {
                        returnOrderDto.setSigningStatus("已签收");
                    } else if (returnOrderDto.getSigningStatus().equals("2")) {
                        returnOrderDto.setSigningStatus("已退签");
                    }else {
                        returnOrderDto.setSigningStatus("未签收");
                    }
                }

                if (returnOrderDto.getSubscriptionStatus() != null) {
                    if (returnOrderDto.getSubscriptionStatus().equals("1")) {
                        returnOrderDto.setSubscriptionStatus("订阅成功");
                    } else if (returnOrderDto.getSubscriptionStatus().equals("2")){
                        returnOrderDto.setSubscriptionStatus("订阅失败");
                    }else {
                        returnOrderDto.setSubscriptionStatus("未订阅");
                    }
                }

                if (returnOrderDto.getOverdueStorageStatus() != null) {
                    if (returnOrderDto.getOverdueStorageStatus().equals("1")) {
                        returnOrderDto.setOverdueStorageStatus("是");
                    } else {
                        returnOrderDto.setOverdueStorageStatus("否");
                    }
                }

                Long id = returnOrderDto.getId();
                List<JSONObject> list = rfnJsnMap.get(id);
                List<JSONObject> list2 = exgJsnMap == null ? null : exgJsnMap.get(id);
                if (!CollectionUtils.isEmpty(list)) {
                    for (JSONObject jsonObject : list) {
                        jsonObject.put("return", "退货");
                    }
                    returnOrderDto.setAllSkuItem(list);
                }
                if (!CollectionUtils.isEmpty(list2)) {
                    for (JSONObject jsonObject : list2) {
                        jsonObject.put("return", "换货");
                        jsonObject.put("qty_refund", jsonObject.getString("qty_exchange"));
                        jsonObject.remove("qty_exchange");
                    }
                    returnOrderDto.setAllSkuItem(list2);
                }

                //财务预处理
                String isConfirmPre = returnOrderDto.getIsConfirmPre();
                if (isConfirmPre != null && YesNoEnum.Y.getKey().equals(isConfirmPre)) {
                    returnOrderDto.setIsConfirmPre("是");
                } else {
                    returnOrderDto.setIsConfirmPre("否");
                }
            }
            result.setQueryResult(returnOrderDtos);
            result.setPageSize(count);
            result.setPageNum(start);
            result.setTotalNum(totalCount);
            result.setTotalSize(totalCount % count == 0 ? totalCount / count : totalCount / count + 1);
            if (unGzip) {
                String zipString = javaCompress(result);
                vh.setData(zipString);
            } else {
                vh.setData(result);
            }
            vh.setCode(0);
            vh.setMessage("成功");
        } catch (Exception e) {
            //出异常后需要列表显示为空
            reSetReturnResult(vh, result, 1, 10, unGzip);
            vh.setMessage(ExceptionUtil.getMessage(e));
            log.error(LogUtil.format("QueryChangingOrRefunding.Query.异常: {}"), Throwables.getStackTraceAsString(e));
        }
        return vh;
    }

    /**
     * 筛选条件 排除为空的字段
     *
     * @param object
     * @return
     */
    private JSONObject findSelect(JSONObject object, JSONObject filterKey, JSONObject chileKey) {
        if (CollectionUtils.isEmpty(object)) {
            return new JSONObject();
        }

        Set<String> keys = object.keySet();

        List<String> list = keys.stream()
                .filter(key -> object.get(key) instanceof String ? StringUtils.isNotEmpty(object.get(key).toString()) : !CollectionUtils.isEmpty((List) object.get(key)))
                .collect(Collectors.toList());

        HashMap<String, Object> hashMap = new HashMap<>();
        for (String s : list) {
            // Long类型：id,原始订单编号,退货入库单编号,淘宝平台换货单号
            if ("ID".equalsIgnoreCase(s) || "ORIG_ORDER_ID".equalsIgnoreCase(s) || "TB_DISPUTE_ID".equalsIgnoreCase(s)
                    || "OC_B_REFUND_IN_ID".equalsIgnoreCase(s) || OcBOrderConst.MUL_REF_KEYS.contains(s)) {
                String result = (String) object.get(s);
                result = result.contains(OcBOrderConst.ORDER_COMMA_CN) ? result.replace(OcBOrderConst.ORDER_COMMA_CN,
                        OcBOrderConst.ORDER_COMMA).trim() : result.trim();
                String[] split = result.trim().split(",| ");
                JSONArray array = new JSONArray();
                for (String s1 : split) {
                    Long val = Long.valueOf(s1);
                    array.add(val);
                }
                hashMap.put(s, array);

                // String类型：原始平台单号,单据编号,审核人姓名,平台退款单号,WMS单据编号,收件人姓名,买家昵称
            } else if ("TID".equalsIgnoreCase(s) || "BILL_NO".equalsIgnoreCase(s)) {
                String phn = object.getString(s);
                if (StringUtils.isBlank(phn)) {
                    continue;
                }
                phn = phn.contains(OcBOrderConst.ORDER_COMMA_CN) ? phn.replace(OcBOrderConst.ORDER_COMMA_CN, OcBOrderConst.ORDER_COMMA).trim() : phn.trim();
                if (phn.contains(",") || phn.contains(" ")) {
                    dealBatchQuery(",| ", s, phn, hashMap);
                } else {
                    hashMap.put(s, phn);
                }
            } else if ("ORIG_SOURCE_CODE".equalsIgnoreCase(s)
                    || "CHECKER_ENAME".equalsIgnoreCase(s)
                    || "RETURN_ID".equalsIgnoreCase(s)
                    || "WMS_BILL_NO".equalsIgnoreCase(s)
                    || "RECEIVE_NAME".equalsIgnoreCase(s)
                    || "REMARK".equalsIgnoreCase(s)
                    || "BUYER_NICK".equalsIgnoreCase(s)
                    || "WMS_FAILREASON".equalsIgnoreCase(s)
                    || "TO_DRP_FAILED_REASON".equalsIgnoreCase(s)
                    || "ORIG_ORDER_NO".equalsIgnoreCase(s)
            ) {

                String phn = object.getString(s);
                if (StringUtils.isBlank(phn)) {
                    continue;
                }
                phn = phn.contains(OcBOrderConst.ORDER_COMMA_CN) ? phn.replace(OcBOrderConst.ORDER_COMMA_CN, OcBOrderConst.ORDER_COMMA).trim() : phn.trim();
                if (phn.contains(",") || phn.contains(" ")) {
                    dealBatchQuery(",| ", s, phn, hashMap);
                } else {
                    hashMap.put(s, "*" + phn + "*");
                }
            } else if ("PS_C_PRO_ECODE".equalsIgnoreCase(s) || "PS_C_SKU_ECODE".equalsIgnoreCase(s)) {
                String result = (String) object.get(s);
                String[] split = result.trim().split(",| ");
                JSONArray array = new JSONArray();
                for (String s1 : split) {
                    array.add(s1);
                }
                chileKey.put(s, array);
            } else if ("RECEIVE_MOBILE".equals(s)) {
                String phn = object.getString(s);
                if (StringUtils.isBlank(phn)) {
                    continue;
                }
                if (phn.contains(",") || phn.contains(" ")) {
                    dealBatchQuery(",| ", s, phn, hashMap);
                } else {
                    hashMap.put(s, "*" + phn + "*");
                }
            } else if (OcBOrderConst.MUL_GROUP_KEYS.contains(s)) {
                JSONArray jsnAry = object.getJSONArray(s);
                if (jsnAry == null || jsnAry.size() < OcBOrderConst.IS_STATUS_IY) {
                    continue;
                }
                hashMap.put(s, jsnAry);
            } else if ("REFUND_STATUS".equalsIgnoreCase(s)) {
                JSONArray jsnAry = object.getJSONArray(s);
                if (jsnAry == null || jsnAry.size() < OcBOrderConst.IS_STATUS_IY) {
                    continue;
                }
                chileKey.put(s, jsnAry);
            } else if ("LOGISTICS_CODE".equalsIgnoreCase(s)) {
                String result = (String) object.get(s);
                String[] split = result.trim().split(",| ");
                JSONArray array = new JSONArray();
                for (String s1 : split) {
                    array.add(s1);
                }
                hashMap.put(s, array);
            } else if ("CHANNEL_TYPE_ID".equalsIgnoreCase(s)) {
                JSONArray jsnAry = object.getJSONArray(s);
                channelTypeService.channelTypeSelectHandler(jsnAry, hashMap);

            } else {
                if ("RETURN_CREATE_TIME".equalsIgnoreCase(s)
                        || "AUDIT_TIME".equalsIgnoreCase(s)
                        || "IN_TIME".equalsIgnoreCase(s)
                        || "CREATIONDATE".equalsIgnoreCase(s)) {
                    String s1 = esDealDate(object.getJSONArray(s));
                    if (s1 != null) {
                        filterKey.put(s, s1);
                    }
                    continue;
                }
                hashMap.put(s, object.get(s));
            }
        }

        return new JSONObject(hashMap);


    }

    // xiwen.start. permission

    private String esDealDate(JSONArray ary) {
        if (ary == null || ary.size() < OcBOrderConst.IS_STATUS_IY) {
            return null;
        }
        String t1 = ary.getString(OcBOrderConst.IS_STATUS_IN);
        String t2 = ary.getString(OcBOrderConst.IS_STATUS_IY);
        if (StringUtils.isBlank(t1) && StringUtils.isBlank(t2)) {
            return null;
        }
        t1 = t1.replace("Z", " UTC");
        t2 = t2.replace("Z", " UTC");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS Z");

        try {
            Date d = format.parse(t1);
            Long time = d.getTime();
            if (time == null) {
                return null;
            }
            Date d2 = format.parse(t2);
            Long time2 = d2.getTime();
            if (time2 == null) {
                return null;
            }
            return time + "~" + time2;
        } catch (ParseException e) {
            log.error(LogUtil.format("dealStringConvertToTIme Error.异常: {}"), Throwables.getStackTraceAsString(e));
            return null;
        }
    }

    /**
     * @param c       ,
     * @param n       key
     * @param v       string value
     * @param hashMap es cdt
     */
    private void dealBatchQuery(String c, String n, String v, HashMap<String, Object> hashMap) {
        JSONArray cnJay = new JSONArray();
        String[] cnAry = v.split(c);
        int l = cnAry.length;
        boolean isNeedLike = l <= 10;
        for (int i = 0; i < l; i++) {
            if (cnAry[i] == null) {
                continue;
            }
            String s = cnAry[i].trim();
            if (s.length() < OcBOrderConst.IS_STATUS_IY) {
                continue;
            }
            // 10条以下模糊查询
            cnJay.add(isNeedLike ? String.format("*%s*", s) : s);
        }
        if (cnJay.size() > OcBOrderConst.IS_STATUS_IN) {
            hashMap.put(n, cnJay);
        }
    }

    /**
     * permission grant
     *
     * @param pem  UserPermission
     * @param wKey JSONObject
     */
    private void filterSearchCondition(UserPermission pem, JSONObject wKey) {
        keyLabel:
        if (pem != null) {
            Map<String, BasePermission> baseMap = pem.getBasePermission();
            if (baseMap == null) {
                break keyLabel;
            }
            BasePermission basePem = baseMap.get("OC_B_RETURN_ORDER");
            if (basePem == null) {
                return;
            }
            List<String> cdtList = new ArrayList<>();
            cdtList.add("CP_C_SHOP_ID");
//            cdtList.add("CP_C_LOGISTICS_ID");
            ocOrderAuthorityMgtService.recombinationSearchCdt(cdtList, basePem, wKey);
        }
    }

    /**
     * get from redis
     *
     * @param phy warehouse ids in this search time
     * @return exist warehouse name in redis
     */
    private Map<Long, String> getFromRedis(Set<Long> phy) {
        Map<Long, String> map = new HashMap<>();
        if (phy.size() < OcBOrderConst.IS_STATUS_IY) {
            map.put(null, "");
            return map;
        }
        try {
            for (Long x : phy) {
                String rs = rpcSearchPhyWareHouse(x);
                if (StringUtils.isNotEmpty(rs)) {
                    map.put(x, rs);
                }
            }
            return map;
        } catch (Exception e) {
            log.error(LogUtil.format("Rpc Invoke cpCPhyWareHouseIdConvertNameCmd .异常: {}"), Throwables.getStackTraceAsString(e));
            return null;
        }

    }

    /**
     * push to redis each item
     *
     * @param map current redis map value
     * @param v   short key
     * @param s   short value
     */
    private void push2Redis(Map<Long, String> map, Long v, String s) {
        map.put(v, s);
        ReturnOrderWareHouse obj = new ReturnOrderWareHouse();
        obj.setMap(map);
        RedisOpsUtil.getObjRedisTemplate().opsForHash().put(hKey, key, obj);
    }

    /**
     * search warehouse name by api
     *
     * @param v current warehouse id
     * @return warehouse name
     */
    private String rpcSearchPhyWareHouse(Long v) {
        try {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(v);
            String name = cpCPhyWarehouse.getEname();
            return name;
        } catch (Exception e) {
            log.error(LogUtil.format("Rpc Invoke cpCPhyWareHouseIdConvertNameCmd Error.异常: {}"), Throwables.getStackTraceAsString(e));
            return null;
        }
    }

    /**
     * collect warehouse ids
     *
     * @param rtnList   return order list
     * @param cpPhySets warehouse collection
     */
    private void collectWareHouseId(List<OrderReturnResult> rtnList, Set<Long> cpPhySets) {
        for (OrderReturnResult o : rtnList) {
            if (o == null) {
                continue;
            }
            cpPhySets.add(o.getCpCPhyWarehouseId());
            cpPhySets.add(o.getCpCPhyWarehouseInId());
        }
        cpPhySets.remove(null);
    }


    /**
     * 预退货查询条件
     *
     * @param jsnObj
     * @return
     */
    private JSONObject beforePrevReturnCnt(JSONObject jsnObj) {
        int billType = jsnObj.getIntValue("BILL_TYPE");
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(String.valueOf(billType));
        jsnObj.put("BILL_TYPE", jsonArray);
        if (billType != 3) {
            return null;
        }
        JSONObject whereKey = new JSONObject();
        int tabIndex = jsnObj.getIntValue(tabIdxKey);
        switch (tabIndex) {
            case 0:
                jsnObj.remove(tabIdxKey);
                return null;
            case 1:
                //无运单号
                whereKey.put(preReturnLogistics, null);
                break;
            case 2:
                //无退货仓
                String warehouseParam = jsnObj.getString(preReturnStore);
                if (StringUtils.isBlank(warehouseParam)) {
                    warehouseParam = null;
                }
                whereKey.put(preReturnStore, warehouseParam);
                break;
            case 3:
                //  退货类型为拦截 且 退货入库仓的第三方类型仓为非菜鸟仓
                whereKey.put("RETURN_PRO_TYPE", 1);
                whereKey.put("THIRD_WAREHOUSE_TYPE", "!=0");
                break;
            default:
                break;

        }
        jsnObj.remove(tabIdxKey);
        return whereKey;
    }

    /**
     * 预退货后置处理
     *
     * @param jsnObj
     * @param whereJsonObj
     */
    private void afterPrevReturnCnt(JSONObject jsnObj, JSONObject whereJsonObj) {

        if (jsnObj.containsKey(preReturnStore)) {
            String val = jsnObj.getString(preReturnStore);
            if (val != null) {
                whereJsonObj.clear();
                whereJsonObj.put("ID", -1);
                return;
            }
        }

        if (jsnObj.containsKey(preReturnLogistics)) {
            if (whereJsonObj.containsKey(preReturnLogistics)) {
                String logisticsCode = whereJsonObj.getString(preReturnLogistics);
                if (StringUtils.isNotBlank(logisticsCode)) {
                    whereJsonObj.clear();
                    whereJsonObj.put("ID", -1);
                    return;
                }
            }
        }
        whereJsonObj.putAll(jsnObj);
    }

    /**
     * 拦截,退货类型
     *
     * @param returnOrder
     * @param returnTypeMap
     * @param interceptStatusMap
     */
    private void convertFieldText(OrderReturnResult returnOrder, Map<Integer, String> returnTypeMap,
                                  Map<Integer, String> interceptStatusMap, Map<Integer, String> proReturnMap) {
        Integer returnProType = returnOrder.getReturnProType();
        Integer interceptStatus = returnOrder.getIntercerptStatus();
        Integer proReturnStatus = returnOrder.getProReturnStatus();

        returnOrder.setReturnProTypeName(returnTypeMap.get(returnProType));
        returnOrder.setProReturnStatusName(proReturnMap.get(proReturnStatus));
        returnOrder.setIntercerptStatusName(interceptStatusMap.get(interceptStatus));
    }
    // xiwen.end

    /**
     * Is Return GZip format
     *
     * @param paramJsn
     * @return
     */
    private boolean isReturnGzip(JSONObject paramJsn) {
        if (paramJsn.containsKey(GZIP_FLAG)) {
            boolean isZip = paramJsn.getBooleanValue(GZIP_FLAG);
            paramJsn.remove(GZIP_FLAG);
            return isZip;
        }
        return false;
    }

    /**
     * gzip
     *
     * @return zip
     * @throws IOException io
     */
    private String javaCompress(QueryChangingOrRefundingResult resultList) throws IOException {

        String gzipString = JSONObject.toJSONString(resultList, SerializerFeature.WriteMapNullValue);
        if (StringUtils.isBlank(gzipString)) {
            return gzipString;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzip = new GZIPOutputStream(out);
        gzip.write(gzipString.getBytes(StandardCharsets.UTF_8));
        gzip.close();
        String zipString = out.toString("ISO-8859-1");
        out.close();
        return zipString;
    }

    private ValueHolderV14 reSetReturnResult(ValueHolderV14 vh, QueryChangingOrRefundingResult result, Integer start,
                                             int count, boolean unGzip) {
        result.setTotalSize(0);
        result.setQueryResult(new ArrayList<>());
        result.setTotalNum(0);
        result.setPageNum(start);
        result.setPageSize(count);
        vh.setMessage("成功");
        vh.setCode(0);
        if (unGzip) {
            String zipString = null;
            try {
                zipString = javaCompress(result);
            } catch (IOException e) {
                e.printStackTrace();
                log.error("QueryChangingOrRefunding:{}", ExceptionUtil.getMessage(e));
            }
            vh.setData(zipString);
        } else {
            vh.setData(result);
        }
        return vh;
    }

    /**
     * 平台.转换
     *
     * @param m 集
     * @param p 值
     * @return 名称
     */
    private String dealPlatform(Map<String, String> m, Integer p) {

        if (m != null && m.size() > 0) {
            String s1 = m.get(String.valueOf(p));
            if (s1 != null) {
                return s1;
            } else {
                return cpRpcService.rpcQueryPlatformNameByCode(String.valueOf(p));
            }
        } else {
            return rpcGetPlatformName(String.valueOf(p));
        }
    }

    /**
     * RPC.查询.平台
     *
     * @param sp 值
     * @return 名称
     */
    private String rpcGetPlatformName(String sp) {
        try {
            String s = cpRpcService.rpcQueryPlatformNameByCode(sp);
            return s == null ? "" : s;
        } catch (Exception e) {
            log.error(LogUtil.format("CpQueryChangingOrRefundingService.rpcGetPlatformName.异常: {}"), Throwables.getStackTraceAsString(e));
            return "";
        }
    }
}

