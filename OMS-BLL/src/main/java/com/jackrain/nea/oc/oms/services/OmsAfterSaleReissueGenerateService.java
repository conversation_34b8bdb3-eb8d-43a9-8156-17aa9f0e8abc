package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWrongReceive;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpOrderReturnRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.ReissueRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.oc.oms.services.calculate.qty.OmsOrderQtyCalculateService;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnAfterUtil;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OmsTransferThreadLocalUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.oc.oms.util.StandplatRefundOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.TaobaoRefundOrderTransferUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Desc : 售后补寄, 单据生成
 * <AUTHOR> xiWen
 * @Date : 2023/4/12
 */
@Slf4j
@Component
public class OmsAfterSaleReissueGenerateService {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OrderAmountUtil orderAmountUtil;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private OcBReturnBuildService ocBReturnBuildService;

    @Autowired
    private OmsRefundOrderService omsRefundOrderService;

    @Autowired
    private ReturnOrderAuditService returnOrderAuditService;

    @Autowired
    private TaobaoRefundOrderTransferUtil taobaoRefundOrderTransferUtil;

    @Autowired
    private OmsStandPlatRefundOrderService omsStandPlatRefundOrderService;

    @Autowired
    private StandplatRefundOrderTransferUtil standplatRefundOrderTransferUtil;

    @Autowired
    private OmsBusinessTypeDistinguishService omsBusinessTypeDistinguishService;


    /**
     * 生成补寄相关单据
     *
     * @param standRelation
     */
    public List<OcBReturnOrderRelation> buildReturnReissueOrders(OmsStandPlatRefundRelation standRelation) {

        IpBStandplatRefund ipBStandplatRefund = standRelation.getIpBStandplatRefund();
        // 退款单 金额处理
        omsStandPlatRefundOrderService.setAfOrderAmount(standRelation, ipBStandplatRefund);

        // 退款明细组合商品处理
        boolean hasCombineGood = reArrangeStandRefundCombineGoods(standRelation);

        // standplatRefundOrderTransferUtil.standplatRefundOrderToReturnOid
        List<OcBReturnOrderRelation> relations = generateReturnReissueOrderProcess(standRelation, hasCombineGood);
        return relations;
    }

    /**
     * 生成补寄单
     *
     * @param relations
     */
    public void generateReissueOrder(List<OcBReturnOrderRelation> relations) {
        User user = SystemUserResource.getRootUser();
        Map<Long, ReissueRelation> map = OmsTransferThreadLocalUtil.returnReissueLocal.get();
        for (OcBReturnOrderRelation relation : relations) {
            OcBReturnOrder returnOrder = relation.getReturnOrderInfo();
            Integer billType = returnOrder.getBillType();
            boolean isReissue = OcReturnBillTypeEnum.REISSUE.getVal().equals(billType);
            if (!isReissue) {
                continue;
            }
            ReissueRelation reissueRelation = map.get(returnOrder.getId());
            AssertUtil.notNull(reissueRelation, "生成补发单失败,未获取到映射退单");
            // 构建单据
            List<OcBReturnOrderRefund> orderRefundList = relation.getOrderRefundList();
            IpOrderReturnRelation ipOrderReturnRelation = buildReissueOrderParam(reissueRelation, returnOrder, orderRefundList, user);
            // 重新计算金额
            OcBOrderRelation reCalcOmsRelation = new OcBOrderRelation();
            reCalcOmsRelation.setOrderInfo(ipOrderReturnRelation.getOcBOrder());
            reCalcOmsRelation.setOrderItemList(ipOrderReturnRelation.getOcBOrderItems());
            orderAmountUtil.recountOrderAmount(reCalcOmsRelation);
            // 新增数据
            ipOrderReturnRelation.setOrderType(OrderTypeEnum.REISSUE);
            ValueHolderV14 result = ocBReturnBuildService.buildExchange(ipOrderReturnRelation, user);
            AssertUtil.isTrue(result.isOK(), "生成补发单失败:" + result.getMessage());
            returnOrderAuditService.recordReturnOrderLog(returnOrder.getId(), "售后补寄", "退货单生成补发订单", true, user);
        }
    }

    /**
     * 退款单明细是否含有组合商品
     *
     * @param standRelation
     * @return
     */
    private boolean reArrangeStandRefundCombineGoods(OmsStandPlatRefundRelation standRelation) {
        List<IpBStandplatRefundItem> standRefundItems = standRelation.getIpBStandplatRefundItem();
        // 组合商品整单退逻辑： 判断中间表子订单号对应原单是否为组合商品，若为组合商品整单退
        List<IpBStandplatRefundItem> items = new ArrayList<>();
        if (CollectionUtils.isEmpty(standRefundItems)) {
            return false;
        }
        Set<String> subOrderIds = standRefundItems.stream().filter(e -> StringUtils.isNotBlank(e.getSubOrderId()))
                .map(IpBStandplatRefundItem::getSubOrderId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(subOrderIds)) {
            return false;
        }
        Map<String, List<OcBOrderItem>> omsOIdMap = standRelation.getOmsOrderRelation().stream()
                .map(OmsOrderRelation::getOcBOrderItems).filter(e -> CollectionUtils.isNotEmpty(e))
                .flatMap(e -> e.stream()).filter(e -> StringUtils.isNotBlank(e.getOoid()))
                .collect(Collectors.groupingBy(e -> e.getOoid(), Collectors.toList()));
        if (MapUtils.isEmpty(omsOIdMap)) {
            return false;
        }
        // 组合商品
        boolean hasCombineGoods = false;
        for (IpBStandplatRefundItem item : standRefundItems) {
            String subOrderId = item.getSubOrderId();
            if (StringUtils.isBlank(item.getSubOrderId())) {
                continue;
            }
            List<OcBOrderItem> omsOrderItems = omsOIdMap.get(subOrderId);
            if (CollectionUtils.isEmpty(omsOrderItems)) {
                continue;
            }
            Optional<Long> first = omsOrderItems.stream().map(e -> NumUtil.init(e.getProType()))
                    .filter(e -> SkuType.NO_SPLIT_COMBINE == e || SkuType.COMBINE_PRODUCT == e).findFirst();
            if (first.isPresent()) {
                item.setSubOrderId(null);
                items.add(item);
                hasCombineGoods = true;
            }
        }

        if (CollectionUtils.isNotEmpty(items)) {
            standRelation.setIpBStandplatRefundItem(items);
        }
        return hasCombineGoods;
    }

    /**
     * 单据生成流程
     *
     * @param standRelation
     * @param hasCombine
     */
    private List<OcBReturnOrderRelation> generateReturnReissueOrderProcess(OmsStandPlatRefundRelation standRelation,
                                                                           boolean hasCombine) {
        User user = SystemUserResource.getRootUser();
        IpBStandplatRefund standRefund = standRelation.getIpBStandplatRefund();
        List<OmsOrderRelation> orderRelations = standRelation.getOmsOrderRelation();
        List<IpBStandplatRefundItem> standRefundItems = standRelation.getIpBStandplatRefundItem();
        List<OcBReturnOrderRelation> returnRelations = new ArrayList<>();
        for (OmsOrderRelation omsOrderRelation : orderRelations) {
            // 生成主表数据
            OcBOrderDelivery orderDelivery = omsOrderRelation.getOrderDeliveries().get(0);
            OcBReturnOrder ocBReturnOrder = buildReturnOrderFromStandPlatRefund(omsOrderRelation, standRefund, orderDelivery, user);
            // 挂靠赠品处理
            OmsOrderRelation omsRelation = reCombineOmsRelationAboutGift(standRefund, omsOrderRelation);
            // 判断是否需要还原对等换货
            boolean isRevertEqExc = checkIsNeedRevertPeerExchange(omsRelation, standRefund);
            String returnNo = standRefund.getReturnNo();
            String billNo = omsOrderRelation.getOcBOrder().getBillNo();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("是否需要还原明细:{};", returnNo, billNo), isRevertEqExc);
            }

            List<OcBOrderItem> eqExcCacheOrderItems = null;
            List<OcBReturnOrderRefund> orderRefunds;
            boolean isFullRefund = standRelation.isFullRefund();
            boolean isEqExcProcess = !isFullRefund && isRevertEqExc;
            // 非整单退 & 对等换货
            if (isEqExcProcess) {
                // 对等换货
                eqExcCacheOrderItems = new ArrayList<>();
                orderRefunds = buildReturnItemEqExchangeProcess(omsRelation, standRefund, eqExcCacheOrderItems, user);
                if (CollectionUtils.isEmpty(orderRefunds)) {
                    /////////////////////
                    continue;
                }
            } else {
                List<OcBOrderItem> ocBOrderItems = omsRelation.getOcBOrderItems();
                if (isFullRefund) {
                    // 整单退, 组合商品 ???
                    orderRefunds = buildReturnItemCombineProcess(omsRelation, standRefund, user, hasCombine);
                } else {
                    // 通用
                    orderRefunds = buildReturnItemProcess(standRefund, standRefundItems, ocBOrderItems, user);
                }
                if (CollectionUtils.isEmpty(orderRefunds)) {
                    continue;
                }
                // 通用平台退单可退数量校验
                boolean qtyFlag = OmsOrderQtyCalculateService.getInstance().checkQtyCanReturn(orderRefunds, omsRelation.getOcBOrderItems());
                if (!qtyFlag) {
                    continue;
                }
            }
            // 退单重置相关数据
            String jointTid = OmsReturnAfterUtil.getJointTid(orderRefunds);
            ocBReturnOrder.setTid(jointTid);
            // return sku
            getAllSku(orderRefunds, ocBReturnOrder);
            OcBReturnOrderRelation relation = new OcBReturnOrderRelation();
            ocBReturnOrder.setReturnAmtActual(orderRefunds.stream().map(OcBReturnOrderRefund::getAmtRefund)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            ocBReturnOrder.setReturnAmtList(ocBReturnOrder.getReturnAmtActual());
            relation.setReturnOrderInfo(ocBReturnOrder);
            relation.setOrderRefundList(orderRefunds);
            returnRelations.add(relation);
            // 补寄缓存
            Map<Long, ReissueRelation> map = OmsTransferThreadLocalUtil.returnReissueLocal.get();
            Long id = ocBReturnOrder.getId();
            ReissueRelation reissueRelation = map.get(id);
            if (reissueRelation == null) {
                reissueRelation = ReissueRelation.build();
                map.put(id, reissueRelation);
            }
            reissueRelation.assignParam(omsRelation.getOcBOrder(), ocBReturnOrder);
            if (isEqExcProcess) {
                reissueRelation.addOrderItems(eqExcCacheOrderItems);
            } else {
                reissueRelation.addOrderItems(omsRelation.getOcBOrderItems());
            }
        }
        //  重算金额
        OmsReturnOrderService.getInstance().reCalculateReturnAmt(returnRelations);
        return returnRelations;
    }

    /**
     * 挂靠赠品处理
     *
     * @param standRefund
     * @param omsOrderRelation
     * @return
     */
    private OmsOrderRelation reCombineOmsRelationAboutGift(IpBStandplatRefund standRefund, OmsOrderRelation omsOrderRelation) {
        List<OcBOrderItem> orderItems = new ArrayList<>();
        orderItems.addAll(omsOrderRelation.getOcBOrderItems());
        OmsOrderRelation omsRelation = new OmsOrderRelation();
        OcBOrder order = omsOrderRelation.getOcBOrder();
        omsRelation.setOcBOrder(order);
        omsRelation.setOcBOrderItems(orderItems);
        if (!omsSystemConfig.isReturnOrderAddBringGift()) {
            return omsRelation;
        }
        // 判断赠品(是否有挂靠赠品)
        List<OmsOrderRelation.OcOrderGifts> giftItems = omsOrderRelation.getOcOrderGifts();
        if (CollectionUtils.isEmpty(giftItems)) {
            return omsRelation;
        }
        String orderNo = standRefund.getOrderNo();
        for (OmsOrderRelation.OcOrderGifts ocOrderGift : giftItems) {
            if (ocOrderGift.getGiftMark() == SkuType.COMBINE_PRODUCT) {
                List<OcBOrderItem> ocBOrderGifts = ocOrderGift.getOcBOrderGifts();
                orderItems.addAll(ocBOrderGifts);
            } else {
                Set<Long> existReturnIds = ES4ReturnOrder.findIdByTid(orderNo);
                boolean notExistReturnOrder = CollectionUtils.isEmpty(existReturnIds);
                if (notExistReturnOrder) {
                    List<OcBOrderItem> ocBOrderGifts = ocOrderGift.getOcBOrderGifts();
                    orderItems.addAll(ocBOrderGifts);
                }
            }
        }
        Long id = order.getId();
        String billNo = order.getBillNo();
        String returnNo = standRefund.getReturnNo();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("挂靠赠品:{}", orderNo, returnNo, billNo, id), JSON.toJSONString(orderItems));
        }
        return omsRelation;
    }

    /**
     * 判断是否需要进行还原对等换货
     *
     * @param omsRelation
     * @param standRefund 退款单
     * @return
     */
    private boolean checkIsNeedRevertPeerExchange(OmsOrderRelation omsRelation, IpBStandplatRefund standRefund) {

        BigDecimal returnQty;
        List<OcBOrderItem> orderItems = omsRelation.getOcBOrderItems();

        // 对等换货明细
        Optional<OcBOrderItem> first = orderItems.stream().filter(x -> OcBOrderConst.IS_STATUS_IY.equals(x.getIsEqualExchange())).findFirst();
        if (!first.isPresent()) {
            return false;
        }
        BigDecimal refundFee = NumUtil.init(standRefund.getRefundAmount());
        // 全退 = 申请的退款金额与成交金额,零售发货单明细成交金额
        BigDecimal sumRealAmt = orderItems.stream().map(OcBOrderItem::getRealAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 相等的时候
        if (refundFee.compareTo(sumRealAmt) >= 0) {
            return false;
        }
        OcBOrderItem item = first.get();
        // 还原对等换货,例如：对等换货比例 2:1
        String peerRatio = item.getEqualExchangeRatio();
        AssertUtil.assertException(StringUtils.isBlank(peerRatio), String.format("明细[%d]对等换货比例为空", item.getId()));
        String[] ratioArray = peerRatio.split(":");
        BigDecimal beforeQty = new BigDecimal(ratioArray[0]);
        BigDecimal afterQty = new BigDecimal(ratioArray[1]);
        // 比例是N:1
        if (beforeQty.compareTo(afterQty) == 0) {
            // 比例是1:1时，如果申请退款金额除以明细成交单价，如果是整数，则不用还原对等换货
            // 成交单价
            BigDecimal priceActual = NumUtil.init(item.getPriceActual());
            returnQty = refundFee.divide(priceActual, 4, BigDecimal.ROUND_HALF_UP);
            boolean isInteger = returnQty.setScale(0, RoundingMode.HALF_UP).compareTo(returnQty) == 0;
            return !isInteger;
        } else return beforeQty.compareTo(afterQty) > 0;
    }

    /**
     * 构建退单明细单据,对等换货流程
     * 还原对等换货明细,
     *
     * @param omsRelation OmsOrderRelation
     * @param standRefund IpBStandPlatRefund
     * @param user        User
     * @return List<OcBReturnOrderRefund>
     */
    private List<OcBReturnOrderRefund> buildReturnItemEqExchangeProcess(OmsOrderRelation omsRelation,
                                                                        IpBStandplatRefund standRefund,
                                                                        List<OcBOrderItem> cacheOrderItems, User user) {

        List<OcBOrderItem> orderItems = omsRelation.getOcBOrderItems();
        // 对等换货明细
        List<OcBOrderItem> exchanges = orderItems.stream().filter(x -> OcBOrderConst.IS_STATUS_IY.equals(x.getIsEqualExchange())).collect(Collectors.toList());
        List<OcBOrderEqualExchangeItem> eqExchanges = taobaoRefundOrderTransferUtil.queryEqualExchangeItems(exchanges);
        Map<String, List<OcBOrderEqualExchangeItem>> exchangeItemMap = eqExchanges.stream().collect(Collectors.groupingBy(x -> x.getOcBOrderId() + "-" + x.getOoid()));
        // 根据申请退款金额除以原明细成交单价计算退货数量
        BigDecimal refundAmount = standRefund.getRefundAmount();
        BigDecimal refundShipAmount = standRefund.getReturnShipamount();
        if (NumUtil.gtZero(refundShipAmount)) {
            refundAmount = refundAmount.subtract(refundShipAmount);
        }
        // 计算对等还原后的退货商品数量, 原明细成交单价
        BigDecimal priceActual = eqExchanges.get(0).getPriceActual();
        BigDecimal returnQty = refundAmount.divide(priceActual, 0, BigDecimal.ROUND_DOWN);
        // 还原对等换货,例如：对等换货比例 2:1
        String eqRatio = exchanges.get(0).getEqualExchangeRatio();
        String[] eqRatioArray = eqRatio.split(":");
        BigDecimal afterQty = new BigDecimal(eqRatioArray[1]);
        BigDecimal beforeQty = new BigDecimal(eqRatioArray[0]);
        // 如果有明细对应原明细数量满足数量，则不用还原对等换货明细
        List<OcBOrderItem> unnecessaryRevertItems = new ArrayList<>();
        for (OcBOrderItem item : orderItems) {
            boolean isGift = OcBOrderConst.IS_STATUS_IY.equals(item.getIsGift());
            if (isGift) {
                continue;
            }
            BigDecimal excQty = beforeQty.multiply(item.getQty()).divide(afterQty, 4, BigDecimal.ROUND_HALF_UP);
            if (NumUtil.eq(returnQty, excQty)) {
                unnecessaryRevertItems.add(item);
                break;
            }
        }
        List<OcBReturnOrderRefund> newReturnItems = new ArrayList<>();
        // 无需对换
        if (CollectionUtils.isNotEmpty(unnecessaryRevertItems)) {
            OcBOrderItem ocBOrderItem = unnecessaryRevertItems.get(0);
            OcBReturnOrderRefund returnOrderRefund = buildExcReturnItem(ocBOrderItem, standRefund, returnQty, user);
            newReturnItems.add(returnOrderRefund);
            returnQty = BigDecimal.ZERO;
            cacheOrderItems.add(ocBOrderItem);
        }
        // 对等换货
        for (OcBOrderItem item : orderItems) {
            BigDecimal qty;
            OcBReturnOrderRefund returnOrderRefund;
            boolean isGift = OcBOrderConst.IS_STATUS_IY.equals(item.getIsGift());
            if (isGift) {
                returnOrderRefund = buildExcReturnItem(item, standRefund, item.getQty(), user);
                newReturnItems.add(returnOrderRefund);
                cacheOrderItems.add(item);
                continue;
            }
            if (!NumUtil.gtZero(returnQty)) {
                break;
            }
            String exchangeKey = item.getOcBOrderId() + "-" + item.getOoid();
            List<OcBOrderEqualExchangeItem> exchangeItems = exchangeItemMap.get(exchangeKey);
            if (CollectionUtils.isEmpty(exchangeItems)) {
                // 此处....
                continue;
            }
            OcBOrderEqualExchangeItem exchangeItem = exchangeItems.get(0);
            // 计算当前明细可退数量: 换货后商品数量,换货前商品数量 , 可退数量
            BigDecimal currentQty = item.getQty();
            BigDecimal exchangeQty = currentQty.multiply(beforeQty).divide(afterQty, 4, BigDecimal.ROUND_HALF_UP);
            BigDecimal remainQty = exchangeQty.subtract(NumUtil.init(exchangeItem.getQtyRefund()));
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("计算当前明细还原对等换货,换货后明细数量={}，换货前={}，可退数量={}",
                        standRefund.getReturnNo(), item.getId()), currentQty, exchangeQty, remainQty);
            }
            if (NumUtil.prevLtNext(remainQty, returnQty)) {
                qty = remainQty;
                returnQty = returnQty.subtract(remainQty);
            } else {
                qty = returnQty;
                returnQty = BigDecimal.ZERO;
            }
            OcBOrderItem swapOrderItem = new OcBOrderItem();
            BeanUtils.copyProperties(exchangeItem, swapOrderItem);
            swapOrderItem.setId(item.getId());
            swapOrderItem.setIsEqualExchange(item.getIsEqualExchange());
            swapOrderItem.setEqualExchangeMark(item.getEqualExchangeMark());
            swapOrderItem.setEqualExchangeRatio(item.getEqualExchangeRatio());
            // generate
            returnOrderRefund = buildExcReturnItem(swapOrderItem, standRefund, qty, user);
            newReturnItems.add(returnOrderRefund);
            cacheOrderItems.add(swapOrderItem);
        }
        String bilNo = omsRelation.getOcBOrder().getBillNo();
        AssertUtil.assertException(NumUtil.gtZero(returnQty), String.format("订单[%s]还原对等换货,可退数量小于申请数量", bilNo));
        return newReturnItems;
    }

    /**
     * 整单退,组合商品
     * 非对等
     *
     * @param omsRelation
     * @param standRefund
     * @param user
     * @param hasCombine
     * @return
     */
    private List<OcBReturnOrderRefund> buildReturnItemCombineProcess(OmsOrderRelation omsRelation,
                                                                     IpBStandplatRefund standRefund,
                                                                     User user, Boolean hasCombine) {
        OcBOrder ocBOrder = omsRelation.getOcBOrder();
        List<OcBReturnOrderRefund> orderRefunds = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = omsRelation.getOcBOrderItems();
        // 退货金额
        Iterator<OcBOrderItem> iterator = ocBOrderItems.iterator();
        while (iterator.hasNext()) {
            OcBOrderItem orderItem = iterator.next();
            boolean isCombinePro = SkuType.NO_SPLIT_COMBINE == NumUtil.init(orderItem.getProType());
            if (hasCombine && isCombinePro) {
                continue;
            }
            OcBReturnOrderRefund item = buildFullCombReturnItem(standRefund, ocBOrder, orderItem, hasCombine, user);
            orderRefunds.add(item);
        }
        // 处理组合商品 (按照比例进行分摊)
        setReturnOrderItemsByRatio(ocBOrderItems, orderRefunds, standRefund);
        return orderRefunds;
    }

    /**
     * 构建通单明细
     *
     * @param standRefund 通用退单
     * @param order
     * @param orderItem
     * @param isCombine   是否为组合商品
     * @param user
     * @return
     */
    private OcBReturnOrderRefund buildFullCombReturnItem(IpBStandplatRefund standRefund, OcBOrder order,
                                                         OcBOrderItem orderItem, boolean isCombine, User user) {
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        BigDecimal qty = orderItem.getQty();
        returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        BigDecimal qtyReturnApply = NumUtil.init(orderItem.getQtyReturnApply());
        if (NumUtil.neZero(qtyReturnApply) && isCombine) {
            returnOrderRefund.setQtyRefund(qty.subtract(orderItem.getQtyReturnApply()));
        } else {
            returnOrderRefund.setQtyRefund(qty);
        }
        BigDecimal amtRefund = orderItem.getRealAmt().multiply(returnOrderRefund.getQtyRefund())
                .divide(orderItem.getQty(), OcBOrderConst.DECIMAL_QTY_FOUR, BigDecimal.ROUND_HALF_UP);
        returnOrderRefund.setAmtRefund(amtRefund);
        Integer returnStatus = standRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        returnOrderRefund.setRefundStatus(status);
        returnOrderRefund.setAmtPtRefund(standRefund.getRefundAmount());
        //商品名称
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPrice(orderItem.getPriceList());
        //1 qty_can_refund 购买数量 合计所有明细qty
        returnOrderRefund.setQtyCanRefund(orderItem.getQty());
        //商品单价
        returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
        //国标码
        returnOrderRefund.setBarcode(orderItem.getBarcode());
        //修改人用户名
        returnOrderRefund.setModifierename(orderItem.getModifierename());
        //商品规格
        returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setOid(orderItem.getOoid());
        returnOrderRefund.setTid(orderItem.getTid());
        //条码id
        returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
        returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCProId(orderItem.getPsCProId());
        returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
        returnOrderRefund.setGiftType(orderItem.getGiftType());
        //颜色尺寸
        returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());
        returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
        returnOrderRefund.setSex(orderItem.getSex());
        returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
        returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
        returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
        returnOrderRefund.setRefundBillNo(standRefund.getReturnNo());
        returnOrderRefund.setOcBOrderId(order.getId());
        returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                OcBOrderConst.DECIMAL_QTY_FOUR, BigDecimal.ROUND_HALF_UP));
        returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");
        returnOrderRefund.setPriceList(orderItem.getPriceList());
        OperateUserUtils.saveOperator(returnOrderRefund, user);
        /*long refundId = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND);
        returnOrderRefund.setId(refundId);*/
        return returnOrderRefund;
    }

    /**
     * 非组合, 非对等
     * 生成退单明细
     *
     * @param standRefund      中间表主表
     * @param standRefundItems 中间表明细
     * @param ocBOrderItems    订单明细
     * @param user             操作员
     * @return
     */
    private List<OcBReturnOrderRefund> buildReturnItemProcess(IpBStandplatRefund standRefund,
                                                              List<IpBStandplatRefundItem> standRefundItems,
                                                              List<OcBOrderItem> ocBOrderItems,
                                                              User user) {
        String orderNo = standRefund.getOrderNo();
        String returnNo = standRefund.getReturnNo();
        Map<String, List<OcBOrderItem>> subOrderMap = ocBOrderItems.stream()
                .filter(e -> StringUtils.isNotBlank(e.getOoid())).collect(Collectors.groupingBy(OcBOrderItem::getOoid));
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("itemMap:{};ipBStandplatRefundItems:{};ipBStandplatRefund:{};ocBOrderItems{}",
                    "getReturnRefunds", orderNo, returnNo), JSONObject.toJSONString(subOrderMap),
                    JSONObject.toJSONString(standRefundItems),
                    JSONObject.toJSONString(standRefund), JSONObject.toJSONString(ocBOrderItems));
        }
        List<OcBReturnOrderRefund> refunds = Lists.newArrayList();
        for (IpBStandplatRefundItem refundItem : standRefundItems) {
            Long ipItemRemainQty = NumUtil.init(refundItem.getReserveDecimal10());
            if (ipItemRemainQty > OcBOrderConst.IS_STATUS_IN) {
                continue;
            }
            List<OcBOrderItem> subOrderItems = subOrderMap.get(refundItem.getSubOrderId());
            AssertUtil.notEmpty(subOrderItems, "根据子订单号未匹配到订单明细");
            Optional<OcBOrderItem> first = subOrderItems.stream().filter(e -> NumUtil.gtZero(e.getRealAmt())).findFirst();
            if (!first.isPresent()) {
                continue;
            }
            OcBOrderItem orderItem = first.get();
            BigDecimal price = orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP);
            // calc qty
            BigDecimal baseRefundQty = NumUtil.init(refundItem.getReturnQuantity());
            BigDecimal refundFee = NumUtil.init(refundItem.getRefundFee());
            if (NumUtil.neZero(refundFee)) {
                baseRefundQty = refundFee.divide(price, 0, BigDecimal.ROUND_UP);
            }
            if (NumUtil.eqZero(baseRefundQty)) {
                BigDecimal refundAmount = standRefund.getRefundAmount();
                if (NumUtil.gtZero(refundAmount)) {
                    baseRefundQty = refundAmount.divide(price, 0, BigDecimal.ROUND_UP);
                }
            }
            // circle generate
            BigDecimal dynamicRefundQty = baseRefundQty;
            for (OcBOrderItem item : subOrderItems) {
                BigDecimal qty = item.getQty();
                BigDecimal applyQty = NumUtil.init(item.getQtyReturnApply());
                BigDecimal remainQty = qty.subtract(applyQty);
                if (!NumUtil.gtZero(remainQty)) {
                    continue;
                }
                // 退货单申请数量,退货单申请金额
                BigDecimal refundQty;
                BigDecimal refundAmt;
                BigDecimal realAmt = item.getRealAmt();
                if (NumUtil.eqZero(baseRefundQty)) {
                    refundAmt = NumUtil.eq(remainQty, qty) ? realAmt : remainQty.multiply(price);
                    refundQty = remainQty;
                } else {
                    if (!NumUtil.gtZero(dynamicRefundQty)) {
                        continue;
                    }
                    if (NumUtil.prevLtNext(remainQty, dynamicRefundQty)) {
                        dynamicRefundQty = dynamicRefundQty.subtract(remainQty);
                        applyQty = applyQty.add(remainQty);
                        refundAmt = NumUtil.eq(remainQty, qty) ? realAmt : remainQty.multiply(price);
                        refundQty = remainQty;
                    } else {
                        refundAmt = NumUtil.eq(dynamicRefundQty, qty) ? realAmt : dynamicRefundQty.multiply(price);
                        applyQty = applyQty.add(dynamicRefundQty);
                        refundQty = dynamicRefundQty;
                        dynamicRefundQty = BigDecimal.ZERO;
                    }
                }
                OcBReturnOrderRefund returnItem = buildReturnItem(item, refundQty, refundAmt, standRefund, refundItem, user);
                refunds.add(returnItem);
            }
            refundItem.setReserveDecimal10((NumUtil.prevLtNext(dynamicRefundQty, BigDecimal.ONE)) ? 3L : 0L);
        }
        setReturnOrderItemsByRatio(ocBOrderItems, refunds, standRefund);
        if (refunds.size() != ocBOrderItems.size()) {
            List<OcBOrderItem> gift = filterOrderItemGift(ocBOrderItems);
            if (gift.size() == (ocBOrderItems.size() - refunds.size())) {
                for (OcBOrderItem item : gift) {
                    OcBReturnOrderRefund refund = buildReturnItem(item, item.getQty(), BigDecimal.ZERO, standRefund, null, user);
                    refunds.add(refund);
                }
            }
        }
        return refunds;
    }

    /**
     * @param orderItem 订单详情
     * @param preQty    申请数量
     * @param preAmt    退单金额
     * @param user      操作人
     * @return
     */
    private OcBReturnOrderRefund buildReturnItem(OcBOrderItem orderItem, BigDecimal preQty, BigDecimal preAmt,
                                                 IpBStandplatRefund standRefund, IpBStandplatRefundItem refundItem,
                                                 User user) {
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        BigDecimal orderItemQty = orderItem.getQty();
        BigDecimal singlePrice = orderItem.getRealAmt().divide(orderItemQty, 4, BigDecimal.ROUND_HALF_UP);
        // 退单金额
        returnOrderRefund.setAmtRefund(preAmt);
        // 申请数量
        returnOrderRefund.setQtyRefund(preQty);
        //商品名称
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPrice(orderItem.getPrice());
        returnOrderRefund.setPriceList(orderItem.getPriceList());
        //1 qty_can_refund 购买数量 合计所有明细qty
        returnOrderRefund.setQtyCanRefund(orderItemQty);
        //商品单价
        returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
        //国标码
        returnOrderRefund.setBarcode(orderItem.getBarcode());
        //修改人用户名
        returnOrderRefund.setModifierename(orderItem.getModifierename());
        //商品规格
        returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setOid(orderItem.getOoid());
        //条码id
        returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCProId(orderItem.getPsCProId());
        //颜色尺寸
        returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
        returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());
        returnOrderRefund.setTid(orderItem.getTid());
        returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
        returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
        returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
        returnOrderRefund.setSex(orderItem.getSex());
        returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
        returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
        returnOrderRefund.setGiftType(orderItem.getGiftType());
        returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
        returnOrderRefund.setAmtRefundSingle(singlePrice);
        returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");

        if (Objects.nonNull(refundItem) && Objects.nonNull(refundItem.getSubOrderId()) && Objects.equals(orderItem.getOoid(), refundItem.getSubOrderId())) {
            Integer returnStatus = standRefund.getReturnStatus();
            String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
            returnOrderRefund.setRefundStatus(status);
            returnOrderRefund.setRefundBillNo(standRefund.getReturnNo());
            returnOrderRefund.setAmtPtRefund(standRefund.getRefundAmount());
        }
        OperateUserUtils.saveOperator(returnOrderRefund, user);
      /*  long refundId = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND);
        returnOrderRefund.setId(refundId);*/
        return returnOrderRefund;
    }

    /**
     * 处理组合商品 (按照比例进行分摊)
     *
     * @param orderItemList         订单子表明细
     * @param returnOrderRefundList 退单明细
     * @param ipBStandplatRefund    通用订单中间表
     */
    private void setReturnOrderItemsByRatio(List<OcBOrderItem> orderItemList,
                                            List<OcBReturnOrderRefund> returnOrderRefundList,
                                            IpBStandplatRefund ipBStandplatRefund) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("setReturnOrderItemsByRatio.before.orderItemList={};ipBStandplatRefund={}"),
                    JSON.toJSONString(orderItemList), JSON.toJSONString(returnOrderRefundList));
        }
        List<OcBOrderItem> combineOrderItemList = orderItemList
                .stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(combineOrderItemList)) {
            return;
        }
        Map<Long, OcBOrderItem> combineOrderItemListMap = combineOrderItemList.stream()
                .collect(Collectors.toMap(OcBOrderItem::getId, t -> t, (v, v1) -> v1));

        BigDecimal refundAmount = ipBStandplatRefund.getRefundAmount();
        BigDecimal remainRefundAmount = ipBStandplatRefund.getRefundAmount();
        for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefundList) {
            // ????
            if (StringUtils.isNotBlank(returnOrderRefund.getGiftType()) && !"0".equals(returnOrderRefund.getGiftType())) {
                returnOrderRefund.setAmtPtRefund(BigDecimal.ZERO);
                continue;
            }
            OcBOrderItem ocBOrderItem = combineOrderItemListMap.get(returnOrderRefund.getOcBOrderItemId());
            if (Objects.isNull(ocBOrderItem)) {
                // ???
                return;
            }
            BigDecimal amtPtRefund = refundAmount.multiply(Optional.ofNullable(ocBOrderItem.getGroupRadio())
                    .orElse(new BigDecimal("0"))).setScale(OcBOrderConst.DECIMAL_QTY_FOUR, BigDecimal.ROUND_HALF_UP);
            returnOrderRefund.setAmtPtRefund(amtPtRefund);
            remainRefundAmount = remainRefundAmount.subtract(amtPtRefund);
        }

        if (remainRefundAmount.compareTo(new BigDecimal("0")) != 0) {
            Collections.sort(returnOrderRefundList, (o1, o2) -> o2.getAmtPtRefund().compareTo(o1.getAmtPtRefund()));
            OcBReturnOrderRefund returnOrderRefund = returnOrderRefundList.get(0);
            returnOrderRefund.setAmtPtRefund(returnOrderRefund.getAmtPtRefund().add(remainRefundAmount));
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("setReturnOrderItemsByRatio.after={}",
                    "setReturnOrderItemsByRatio.after"), JSON.toJSONString(returnOrderRefundList));
        }
    }

    /**
     * 过滤出赠品
     *
     * @return
     */
    private List<OcBOrderItem> filterOrderItemGift(List<OcBOrderItem> items) {
        List<OcBOrderItem> gifts = Lists.newArrayList();
        if (CollectionUtils.isEmpty(items)) {
            return gifts;
        }
        for (OcBOrderItem item : items) {
            if (Objects.equals(item.getIsGift(), 1) && item.getProType() != SkuType.NO_SPLIT_COMBINE) {
                gifts.add(item);
            }
        }
        return gifts;
    }

    /**
     * 非全退, 对等换货
     * 构建退换货单明细
     *
     * @param orderItem
     * @param standRefund
     * @param qtyRefund
     * @param user
     * @return
     */
    private OcBReturnOrderRefund buildExcReturnItem(OcBOrderItem orderItem, IpBStandplatRefund standRefund,
                                                    BigDecimal qtyRefund, User user) {
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        returnOrderRefund.setTid(orderItem.getTid());
        returnOrderRefund.setOid(orderItem.getOoid());
        returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
        //申请数量
        returnOrderRefund.setQtyRefund(qtyRefund);
        returnOrderRefund.setQtyIn(BigDecimal.ZERO);
        returnOrderRefund.setQtyCanRefund(orderItem.getQty());
        // amt 商品单价
        returnOrderRefund.setPrice(orderItem.getPrice());
        returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
        returnOrderRefund.setPriceList(orderItem.getPriceList());
        BigDecimal singleAmt = orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP);
        returnOrderRefund.setAmtRefundSingle(singleAmt);
        BigDecimal refundAmt = singleAmt.multiply(qtyRefund);
        returnOrderRefund.setAmtRefund(refundAmt);
        // 条码,国标码
        returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
        returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
        returnOrderRefund.setPsCProId(orderItem.getPsCProId());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setBarcode(orderItem.getBarcode());
        // 颜色尺寸 , 规格
        returnOrderRefund.setSex(orderItem.getSex());
        returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());
        returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
        returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
        returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
        returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
        // properties
        returnOrderRefund.setGiftType(orderItem.getGiftType());
        returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
        returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");
        // ip
        if (standRefund != null && orderItem.getOoid() != null &&
                orderItem.getOoid().equals(standRefund.getSubOrderId())) {
            Integer returnStatus = standRefund.getReturnStatus();
            String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
            returnOrderRefund.setRefundStatus(status);
            returnOrderRefund.setRefundBillNo(standRefund.getReturnNo());
            returnOrderRefund.setAmtPtRefund(standRefund.getRefundAmount());
        }
        returnOrderRefund.setIsEqualExchange(orderItem.getIsEqualExchange());
        OperateUserUtils.saveOperator(returnOrderRefund, user);
        returnOrderRefund.setModifierename(orderItem.getModifierename());
        return returnOrderRefund;
    }

    /**
     * 封装主表的all_sku以及 商品数量
     *
     * @param returnOrderItems
     * @return
     */
    private void getAllSku(List<OcBReturnOrderRefund> returnOrderItems, OcBReturnOrder returnOrder) {
        String skuQyt = "";
        BigDecimal qtyInstore = BigDecimal.ZERO;
        int i = 0;
        for (OcBReturnOrderRefund returnOrderItem : returnOrderItems) {
            qtyInstore = qtyInstore.add(returnOrderItem.getQtyRefund());
            if (i == 5) {
                continue;
            }
            String str = returnOrderItem.getPsCSkuEcode() + "(" + returnOrderItem.getQtyRefund().intValue() + "),";
            skuQyt = skuQyt + str;
            i++;
        }
        if (StringUtils.isNotEmpty(skuQyt)) {
            skuQyt = skuQyt.substring(0, skuQyt.length() - 1);
        }
        returnOrder.setAllSku(skuQyt);
        returnOrder.setQtyInstore(qtyInstore);
    }

    /**
     * 退单主表数据创建
     *
     * @return
     */
    private OcBReturnOrder buildReturnOrderFromStandPlatRefund(OmsOrderRelation omsRelation, IpBStandplatRefund standRefund,
                                                               OcBOrderDelivery delivery, User user) {
        OcBOrder ocBOrder = omsRelation.getOcBOrder();
        OcBReturnOrder returnOrder = new OcBReturnOrder();

        //平台退款单号
        returnOrder.setReturnId(standRefund.getReturnNo());
        //卖家昵称
        returnOrder.setBuyerNick(standRefund.getBuyerNick());
        //申请退款时间
        returnOrder.setReturnCreateTime(standRefund.getCreated());
        //最后修改时间
        returnOrder.setLastUpdateTime(standRefund.getModified());
        //买家备注
        returnOrder.setBuyerRemark(standRefund.getBuyerRemark());
        //货物退回时间
        //returnOrder.setReturnTime(ipBStandplatRefund.getGoodReturnTime());
        //退款说明
        returnOrder.setReturnDesc(standRefund.getReturnReason());
        //退款原因
        returnOrder.setRemark(standRefund.getReturnReason());
        returnOrder.setReturnReason(standRefund.getReturnReason());
        //商品应退金额(
        returnOrder.setReturnAmtList(standRefund.getRefundAmount());
        //售后/售中
        // returnOrder.setReturnPhase(ipBStandplatRefund.getRefundPhase());
        //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
        returnOrder.setReturnAmtActual(standRefund.getRefundAmount());
        String companyName = standRefund.getCompanyName();
        //退回物流单号
        returnOrder.setLogisticsCode(standRefund.getLogisticsNo());
        returnOrder.setCpCLogisticsEname(companyName);
        //退回说明
        //returnOrder.setReserveVarchar02(ipBStandplatRefund.getRefunddesc());
        //standplatRefundOrderTransferUtil.setLogisticInfo(returnOrder, companyName);

        //下载时间
        //卖家呢城
        // returnOrder.setSellerNick(ocBOrder.getS);
        //物流公司名称
        returnOrder.setCreationdate(new Date());
        returnOrder.setBillNo(sequenceUtil.buildReturnBillNo());
        //等待退货入库(PRD数据对象)
        returnOrder.setReturnStatus(TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode());
        //原始订单编号
        returnOrder.setOrigOrderId(ocBOrder.getId());
        //退还运费，默认0
        returnOrder.setReturnAmtShip(BigDecimal.ZERO);
        //退还其他费用，默认0
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);
        //换货人姓名
        returnOrder.setReceiveName(ocBOrder.getReceiverName());
        //换货人手机
        returnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getOrderSource());
        //邮编
        returnOrder.setReceiveZip(ocBOrder.getReceiverZip());
        //发货仓库
        returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        //平台类型
        returnOrder.setPlatform(ocBOrder.getPlatform());
        // returnOrder.setThirdWarehouseType(ocBOrder.getThirdWarehouseType());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());

        //换货金额
        returnOrder.setExchangeAmt(BigDecimal.ZERO);
        returnOrder.setTid(ocBOrder.getTid());
        //是否传AG默认否
        returnOrder.setIsToag(AGStatusEnum.INIT.getVal());
        //是否生成调拨单，默认0
        returnOrder.setIsTransfer(0);
        //是否生成零售，默认0
        returnOrder.setIsTodrp(0);
        //退单状态，默认20
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //是否手工新增，默认0
        returnOrder.setIsAdd(0);
        //虚拟入库状态，默认0
        returnOrder.setInventedStatus(0);
        //是否原退，默认0
        returnOrder.setIsRefund(0);
        //是否确认收货，默认0
        returnOrder.setIsReceiveConfirm(0);
        //WMS撤回状态，默认0
        returnOrder.setWmsCancelStatus(0);
        //强制入库，默认0
        returnOrder.setIsForce(0);
        //是否手工审核，默认0
        returnOrder.setIsManualAudit(0);
        //是否传WMS
        returnOrder.setIsTowms(0);
        //是否入仓成功
        returnOrder.setIsInstorage(0);
        returnOrder.setOrigSourceCode(ocBOrder.getSourceCode());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());

        returnOrder.setReceiveAddress(ocBOrder.getReceiverAddress());
        //店铺id
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        returnOrder.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        returnOrder.setCpCShopEcode(ocBOrder.getCpCShopEcode());

        returnOrder.setReceiverProvinceName(ocBOrder.getCpCRegionProvinceEname());
        returnOrder.setReceiverCityName(ocBOrder.getCpCRegionCityEname());
        returnOrder.setReceiverAreaName(ocBOrder.getCpCRegionAreaEname());
        returnOrder.setReceiverProvinceId(ocBOrder.getCpCRegionProvinceId());
        returnOrder.setReceiverCityId(ocBOrder.getCpCRegionCityId());
        returnOrder.setReceiverAreaId(ocBOrder.getCpCRegionAreaId());
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        //原单卖家备注
        returnOrder.setOrigSellerRemark(ocBOrder.getSellerMemo());
        //原单买家留言
        returnOrder.setOrigBuyerMessage(ocBOrder.getBuyerMessage());
        //取值为发货实体仓档案中关联的退货待检实体仓仓库
        //this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder,false);
        Integer proType = TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode();
        // 退款类型等于 退货退款时,从中间表取物流信息
        // 假如退货物流单号不为空！ 并且和之前的物流单号不同
        if (StringUtils.isNotBlank(standRefund.getLogisticsNo())) {
            try {
                LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(standRefund.getCompanyName());
                if (Objects.nonNull(logisticsInfo)) {
                    returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                    returnOrder.setCpCLogisticsId(logisticsInfo.getId());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("错误信息:{}"), Throwables.getStackTraceAsString(e));
            }
            returnOrder.setLogisticsCode(standRefund.getLogisticsNo());
            returnOrder.setCpCLogisticsEname(standRefund.getCompanyName());
        } else if (delivery != null) {
            returnOrder.setLogisticsCode(delivery.getLogisticNumber());
            returnOrder.setCpCLogisticsId(delivery.getCpCLogisticsId());
            returnOrder.setCpCLogisticsEcode(delivery.getCpCLogisticsEcode());
            returnOrder.setCpCLogisticsEname(delivery.getCpCLogisticsEname());
            proType = TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode();
        }
        returnOrder.setReturnProType(proType);
        returnOrder.setInventedStatus(omsRelation.getInterceptMark());
        // 原退仓
        returnOrder.setCpCPhyWarehouseInId(ocBOrder.getCpCPhyWarehouseId());
        standplatRefundOrderTransferUtil.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder, true);
        OperateUserUtils.saveOperator(returnOrder, user);
        //获取退货业务类型
        String platCode = standRefund.getCpCPlatformEcode();
        Integer platId = standplatRefundOrderTransferUtil.getStandplatRefundPlatmform(standRefund);
        if (PlatFormEnum.SAP.getCode().equals(platId) || PlatFormEnum.DMS.getCode().equals(platId)) {
            //匹配业务类型策略
            OmsBusinessTypeDistinguishService.BusinessTypeResult bizResult = omsBusinessTypeDistinguishService
                    .querySapBusinessType(standRefund.getBusinessTypeCode(), Integer.valueOf(platCode),
                            standRefund.getReserveVarchar05(), omsRelation.getOcBOrderItems(), user);
            AssertUtil.notNull(bizResult, "SAP业务类型匹配失败");
            returnOrder.setBusinessTypeId(bizResult.getId());
            returnOrder.setBusinessTypeCode(bizResult.getCode());
            returnOrder.setBusinessTypeName(bizResult.getName());
        } else {
            StCBusinessType stCBusinessType = omsRefundOrderService.queryReturnOrderType(ocBOrder);
            returnOrder.setBusinessTypeId(stCBusinessType.getId());
            returnOrder.setBusinessTypeCode(stCBusinessType.getEcode());
            returnOrder.setBusinessTypeName(stCBusinessType.getEname());
        }
        returnOrder.setOriginalBillNo(standRefund.getOriginalBillNo());
        returnOrder.setCostCenter(standRefund.getCostCenter());
        returnOrder.setSalesOrganize(standRefund.getSalesOrganize());
        //来源系统
        returnOrder.setReserveVarchar05(ocBOrder.getGwSourceGroup());
        standplatRefundOrderTransferUtil.setReturnPhyWarehouseInId(returnOrder, standRefund);
        returnOrder.setIsWrongReceive(IsWrongReceive.NO.val());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("构建退货单主表数据:{}", "构建退货单主表数据"), JSONObject.toJSONString(returnOrder));
        }
        // 维护传奶卡系统值
        if (PlatFormEnum.CARD_CODE.getCode().equals(returnOrder.getPlatform())) {
            // 判断订单业务类型
            if (OcCommonConstant.list.contains(returnOrder.getBusinessTypeCode())) {
                returnOrder.setToNaikaStatus(ReturnNaiKaStatusEnum.UN_PUSH.getStatus());
            }
        }
        returnOrder.setGwSourceCode(ocBOrder.getGwSourceCode());
        returnOrder.setPlatformRefundStatus(OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_INIT);
        returnOrder.setBillType(OcReturnBillTypeEnum.REISSUE.getVal());
        returnOrder.setIsReserved(OcBOrderConst.IS_STATUS_IY);
        returnOrder.setIsBack(OcBOrderConst.IS_STATUS_IY);
        Long sequence = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER);
        returnOrder.setId(sequence);
        return returnOrder;
    }

    /**
     * 构建补寄订单
     *
     * @param returnOrder 退换货单
     * @return IpOrderReturnRelation
     */
    private IpOrderReturnRelation buildReissueOrderParam(ReissueRelation reissueRelation, OcBReturnOrder returnOrder,
                                                         List<OcBReturnOrderRefund> orderRefundList, User user) {
        OcBOrder origOrder = reissueRelation.getOrder();
        AssertUtil.notNull(origOrder, "构建补寄订单,未获取到映射原订单");
        Long returnOrderId = returnOrder.getId();
        IpOrderReturnRelation ocBOrderRelation = new IpOrderReturnRelation();
        OcBOrder newOrder = new OcBOrder();

        newOrder.setTid(returnOrder.getTid());
        newOrder.setSourceCode(returnOrder.getTid());
        newOrder.setOrderSource(returnOrder.getOrdeSource());
        newOrder.setOrigOrderId(returnOrder.getOrigOrderId());
        newOrder.setOrigReturnOrderId(returnOrderId);
        newOrder.setMergeSourceCode(origOrder.getMergeSourceCode());
        newOrder.setSysremark("由退换单" + returnOrderId + "，生成新订单");
        // basic info
        newOrder.setPlatform(returnOrder.getPlatform());
        newOrder.setCpCShopId(returnOrder.getCpCShopId());
        newOrder.setCpCShopEcode(returnOrder.getCpCShopEcode());
        newOrder.setCpCShopTitle(returnOrder.getCpCShopTitle());
        newOrder.setCpCRegionProvinceId(returnOrder.getReceiverProvinceId());
        newOrder.setCpCRegionCityId(returnOrder.getReceiverCityId());
        newOrder.setCpCRegionAreaId(returnOrder.getReceiverAreaId());
        newOrder.setReceiverAddress(returnOrder.getReceiveAddress());
        newOrder.setReceiverName(returnOrder.getReceiveName());
        newOrder.setReceiverMobile(returnOrder.getReceiveMobile());
        newOrder.setReceiverPhone(origOrder.getReceiverPhone());
        newOrder.setReceiverZip(origOrder.getReceiverZip());
        newOrder.setCpCRegionProvinceEcode(origOrder.getCpCRegionProvinceEcode());
        newOrder.setCpCRegionProvinceEname(origOrder.getCpCRegionProvinceEname());
        newOrder.setCpCRegionTownEname(origOrder.getCpCRegionTownEname());
        newOrder.setCpCRegionCityEcode(origOrder.getCpCRegionCityEcode());
        newOrder.setCpCRegionCityEname(origOrder.getCpCRegionCityEname());
        newOrder.setCpCRegionAreaEcode(origOrder.getCpCRegionAreaEcode());
        newOrder.setCpCRegionAreaEname(origOrder.getCpCRegionAreaEname());
        // time
        newOrder.setOrderDate(new Date());
        newOrder.setPayTime(origOrder.getPayTime());
        // status
        newOrder.setIsMerge(0);
        newOrder.setIsSplit(0);
        newOrder.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
        newOrder.setIsInterecept(0);
        newOrder.setOccupyStatus(0);
        newOrder.setIsInreturning(0);
        newOrder.setWmsCancelStatus(0);
        newOrder.setIsExchangeNoIn(0L);
        newOrder.setIsGeninvoiceNotice(0);
        newOrder.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        newOrder.setReturnStatus(0);
        newOrder.setRefundConfirmStatus(0);
        newOrder.setAutoAuditStatus(0);
        newOrder.setDouble11PresaleStatus(0);
        newOrder.setIsEqualExchange(origOrder.getIsEqualExchange());
        newOrder.setPlatformStatus(origOrder.getPlatformStatus());
        newOrder.setCpCShopSellerNick(origOrder.getCpCShopSellerNick());
        newOrder.setIsInvented(origOrder.getIsInvented());
        newOrder.setIsCombination(origOrder.getIsCombination());
        newOrder.setIsOutUrgency(0);
        newOrder.setIsHasTicket(0);
        // amt
        newOrder.setProductAmt(returnOrder.getReturnAmtActual());
        newOrder.setAmtReceive(returnOrder.getReturnAmtActual());
        newOrder.setOrderAmt(returnOrder.getReturnAmtActual());
        newOrder.setReceivedAmt(returnOrder.getReturnAmtActual());
        newOrder.setProductDiscountAmt(origOrder.getProductDiscountAmt());
        newOrder.setOrderDiscountAmt(origOrder.getOrderDiscountAmt());
        newOrder.setShipAmt(origOrder.getShipAmt());
        newOrder.setServiceAmt(origOrder.getServiceAmt());
        newOrder.setConsignAmt(origOrder.getConsignAmt());
        newOrder.setConsignShipAmt(origOrder.getConsignShipAmt());
        newOrder.setCodAmt(origOrder.getCodAmt());
        newOrder.setJdSettleAmt(BigDecimal.ZERO);
        newOrder.setJdReceiveAmt(BigDecimal.ZERO);
        newOrder.setLogisticsCost(BigDecimal.ZERO);
        // type
        newOrder.setBusinessType(origOrder.getBusinessType());
        newOrder.setBusinessTypeId(origOrder.getBusinessTypeId());
        newOrder.setBusinessTypeCode(origOrder.getBusinessTypeCode());
        newOrder.setBusinessTypeName(origOrder.getBusinessTypeName());
        newOrder.setOrderType(OrderTypeEnum.REISSUE.getVal());
        newOrder.setIsResetShip(OcBOrderConst.IS_STATUS_IY);
        newOrder.setSuffixInfo(returnOrder.getId() + "-RRS");
        newOrder.setUserNick(returnOrder.getBuyerNick());
        newOrder.setSellerMemo(returnOrder.getRemark());
        newOrder.setIsactive("Y");
        newOrder.setUserId(0L);
        /**
         * 明细
         */
        Map<Long, OcBOrderItem> itemMap = reissueRelation.getOrderItemMap();
        AssertUtil.notEmpty(itemMap, "构建补寄订单明细,未获取到映射原订单明细1");
        List<OcBOrderItem> newItems = new ArrayList<>();
        for (OcBReturnOrderRefund subItem : orderRefundList) {
            Long origItemId = subItem.getOcBOrderItemId();
            OcBOrderItem origItem = itemMap.get(origItemId);
            AssertUtil.notNull(origItem, "构建补寄订单明细,未获取到映射原订单明细2");
            OcBOrderItem newSubItem = new OcBOrderItem();
            newSubItem.setOoid(subItem.getOid());
            newSubItem.setTid(returnOrder.getTid());
            newSubItem.setReturnOrderId(returnOrderId);
            // good info
            newSubItem.setPsCSkuId(subItem.getPsCSkuId());
            newSubItem.setPsCSkuEcode(subItem.getPsCSkuEcode());
            newSubItem.setBarcode(subItem.getBarcode());
            newSubItem.setPsCProId(subItem.getPsCProId());
            newSubItem.setPsCProEcode(subItem.getPsCProEcode());
            newSubItem.setPsCProEname(subItem.getPsCProEname());
            newSubItem.setSkuSpec(subItem.getSkuSpec());

            newSubItem.setSex(origItem.getSex());
            newSubItem.setMDim4Id(origItem.getMDim4Id());
            newSubItem.setMDim6Id(origItem.getMDim6Id());
            newSubItem.setStandardWeight(origItem.getStandardWeight());
            // sku convert
            convertProductType(newSubItem);
            // qty
            BigDecimal qtyRefund = subItem.getQtyRefund();
            newSubItem.setQty(qtyRefund);
            newSubItem.setQtyRefund(BigDecimal.ZERO);
            // amt
            newSubItem.setAmtRefund(BigDecimal.ZERO);
            newSubItem.setPriceList(origItem.getPriceList());
            newSubItem.setPrice(origItem.getPrice());
            newSubItem.setPriceActual(origItem.getPriceActual());
            newSubItem.setPriceTag(origItem.getPriceTag());
            newSubItem.setRealAmt(subItem.getAmtRefund());
            BigDecimal disAmt = NumUtil.init(origItem.getAmtDiscount());
            BigDecimal adjustAmt = NumUtil.init(origItem.getAdjustAmt());
            BigDecimal orderSplitAmt = NumUtil.init(origItem.getOrderSplitAmt());
            BigDecimal origItemQty = origItem.getQty();
            if (NumUtil.ne(qtyRefund, origItemQty)) {
                disAmt = disAmt.multiply(qtyRefund).divide(origItemQty, 4, BigDecimal.ROUND_HALF_UP);
                adjustAmt = adjustAmt.multiply(qtyRefund).divide(origItemQty, 4, BigDecimal.ROUND_HALF_UP);
                orderSplitAmt = orderSplitAmt.multiply(qtyRefund).divide(origItemQty, 4, BigDecimal.ROUND_HALF_UP);
            }
            newSubItem.setAmtDiscount(disAmt);
            newSubItem.setAdjustAmt(adjustAmt);
            newSubItem.setOrderSplitAmt(orderSplitAmt);

            newSubItem.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
            newSubItem.setIsPresalesku(0);
            newSubItem.setIsSendout(0);
            String giftType = subItem.getGiftType();
            boolean isGift = OcBOrderConst.IS_STATUS_SY.equals(giftType) || "2".equals(giftType);
            newSubItem.setIsGift(origItem.getIsGift());
            newSubItem.setGiftType(subItem.getGiftType());
            // status
            newSubItem.setIsBuyerRate(0);
            newSubItem.setIsAllocatestock(0);
            newSubItem.setIsAllocatestock(0);
            newSubItem.setIsBuyerRate(0);
            newSubItem.setIsPresalesku(0);
            newSubItem.setIsSendout(0);
            newSubItem.setIsLackstock(0);
            newSubItem.setRefundStatus(0);
            newSubItem.setQtyRefund(new BigDecimal(0));
            newSubItem.setIsEqualExchange(origItem.getIsEqualExchange());
            newSubItem.setEqualExchangeMark(origItem.getEqualExchangeMark());
            newSubItem.setEqualExchangeRatio("");
            newSubItem.setGroupGoodsMark(origItem.getGroupGoodsMark());
            newSubItem.setGroupRadio(origItem.getGroupRadio());
            newSubItem.setGroupName(origItem.getGroupName());
            newSubItem.setQtyGroup(origItem.getQtyGroup());
            // base data
            newSubItem.setOwnername(user.getName());
            newSubItem.setOwnerename(user.getEname());
            newSubItem.setOwnerid((long) user.getId());
            newSubItem.setCreationdate(new Date());
            newSubItem.setModifieddate(new Date());
            newSubItem.setModifiername(user.getName());
            newSubItem.setModifierename(user.getEname());
            newSubItem.setModifierid((long) user.getId());
            newSubItem.setAdOrgId((long) user.getOrgId());
            newSubItem.setAdClientId((long) user.getClientId());
            newSubItem.setIsactive(IsActiveEnum.Y.getKey());
            newItems.add(newSubItem);
        }
        // 合单
        Integer isMerge = origOrder.getIsMerge();
        if (OcBOrderConst.IS_STATUS_IY.equals(isMerge)) {
            String tid = newItems.get(0).getTid();
            newOrder.setTid(tid);
            newOrder.setSourceCode(tid);
            newOrder.setMergeSourceCode(tid);
        }
        //调用生成换货订单服务
        ocBOrderRelation.setOcBOrder(newOrder);
        ocBOrderRelation.setOcBOrderItems(newItems);
        ocBOrderRelation.setOcBReturnOrder(returnOrder);
        ocBOrderRelation.setForbidHoldExchangeOrder(true);
        return ocBOrderRelation;
    }

    /**
     * @param ocBOrderItem 订单明细商品类型处理
     */
    private void convertProductType(OcBOrderItem ocBOrderItem) {
        ProductSku productSku = psRpcService.selectProductSku(ocBOrderItem.getPsCSkuEcode());
        AssertUtil.notNull(productSku, "条码: " + ocBOrderItem.getPsCSkuEcode() + ", 未查询到对应商品信息");
        int skuType = productSku.getSkuType();
        Long proType;
        if (skuType == 1 || skuType == 2) {
            proType = 4L;
        } else {
            proType = Long.valueOf(skuType);
        }
        // 获取是否进行效期管理字段 并维护
        if ("Y".equals(productSku.getIsEnableExpiry())) {
            ocBOrderItem.setIsEnableExpiry(1);
        } else {
            ocBOrderItem.setIsEnableExpiry(0);
        }
        ocBOrderItem.setProType(proType);
    }

}
