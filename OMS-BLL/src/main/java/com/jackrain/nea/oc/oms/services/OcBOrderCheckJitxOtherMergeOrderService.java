package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.es.ES4IpJitXOrder;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * description：检查是否存在其他可合并JITX订单
 *
 * <AUTHOR>
 * @date 2021/5/20
 */
@Component
@Slf4j
public class OcBOrderCheckJitxOtherMergeOrderService {
    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private VipcomJitxWarehouseService vipcomJitxWarehouseService;

    @Autowired
    private OmsSystemConfig omsSystemConfig;


    public ValueHolderV14 checkOtherMergeOrder(List<Long> ids, User user) throws NDSException {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.multiFormat("校验是否存在其他JITX可合并订单入参:", ids));
        }
        List<OcBOrder> orders = ocBOrderMapper.selectByIdsList(ids);
//        long count = orders.stream().filter(x -> PlatFormEnum.VIP_JITX.getCode().equals(x.getPlatform())).count();
//        if (count == 0) {
//            return ValueHolderV14Utils.getSuccessValueHolder("未包含JITX订单,跳过校验");
//        }
//        if (count < orders.size()) {
//            return ValueHolderV14Utils.getFailValueHolder("非JITX订单不可与JITX订单进行合单！");
//        }
        OcBOrder first = orders.get(0);
//        if (log.isDebugEnabled()) {
//            log.debug("mergedCode:{},jitxRequiresMerge:{},isForbiddenDelivery:{}", first.getMergedCode(), first.getJitxRequiresMerge(), first.getIsForbiddenDelivery());
//        }
//
//        String mergedCode = StringUtils.isNotEmpty(first.getMergedCode()) ? first.getMergedCode() : "";
//        Optional<OcBOrder> any = orders.stream().filter(x -> !mergedCode.equals(x.getMergedCode())
//                || !YesNoEnum.ONE.getKey().equals(x.getJitxRequiresMerge())
//                || !IsForbiddenDeliveryEnum.PASS.getCode().equals(x.getIsForbiddenDelivery())).findAny();
//        if (any.isPresent()) {
//            return ValueHolderV14Utils.getFailValueHolder("选中的JITX订单存在“JITX要求合包”为否的或合包码不相同的或存在禁发的订单，不允许合并！");
//        }
        ValueHolderV14 v14 = this.checkExistOtherMergeOrder(first, ids, orders);
        if (!v14.isOK()) {
            v14.setCode(-3);
        }
        return v14;
    }

    public ValueHolderV14 checkExistOtherMergeOrder(OcBOrder order, List<Long> idList, List<OcBOrder> orders) {
        log.debug("查询是否还存在其他未合并的JITX开始:idList:{}", idList);
        //判断若为JITX订单，中间表及发货单表中是否还存在其他可合并订单 判断依据
        if (PlatFormEnum.VIP_JITX.getCode().equals(order.getPlatform())) {
            if (StringUtils.isEmpty(order.getMergedCode())) {
                return ValueHolderV14Utils.getSuccessValueHolder("");
            }
            int qty = order.getQtyAll().intValue();
            if (qty >= businessSystemParamService.getJitxMergedOrderLimit()) {
                return ValueHolderV14Utils.getSuccessValueHolder("");
            }
            String returnMsg = "零售发货单/JITX订单中间表存在可合并的订单还未参与合并，仍要继续此操作吗？";
            //查询发货单
            List<Long> ids = ES4Order.findIdsByMergedCode(order);
            if (CollectionUtils.isNotEmpty(ids)) {
                ids.removeAll(idList);
                if (CollectionUtils.isNotEmpty(ids)) {
                    List<OcBOrder> orderList = ocBOrderMapper.selectByIdsList(ids);
                    Optional<OcBOrder> optional = orderList.stream()
                            .filter(x -> OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(x.getOrderStatus()) || OmsOrderStatus.UNCONFIRMED.toInteger().equals(x.getOrderStatus()))
                            .filter(x -> x.getQtyAll() != null && x.getQtyAll().intValue() < qty).findAny();
                    if (optional.isPresent()) {
                        return ValueHolderV14Utils.getFailValueHolder(returnMsg);
                    }
                } else {
                    StCVipcomJitxWarehouse stCVipcomJitxWarehouse = vipcomJitxWarehouseService.queryJitxCapacity(order.getCpCShopId(), order.getCpCPhyWarehouseId(), null);
                    String jitWarehouseEcode = stCVipcomJitxWarehouse.getVipcomWarehouseEcode();
                    if (!YesNoEnum.Y.getVal().equals(order.getIsStoreDelivery())) {
                        jitWarehouseEcode = stCVipcomJitxWarehouse.getVipcomUnshopWarehouseEcode();
                    }
                    //查询JITX订单表
                    List<String> orderSnList = ES4IpJitXOrder.findExistOtherCanMergedOrder(order.getMergedCode(), order.getCpCShopId(), jitWarehouseEcode);
                    if (CollectionUtils.isNotEmpty(orderSnList)) {
                        List<String> tidList = orders.stream().map(OcBOrder::getTid).collect(Collectors.toList());
                        orderSnList.remove(tidList);
                        if (CollectionUtils.isNotEmpty(orderSnList)) {
                            return ValueHolderV14Utils.getFailValueHolder(returnMsg);
                        }
                    }
                }
            }
        }
        return ValueHolderV14Utils.getSuccessValueHolder("");
    }
}