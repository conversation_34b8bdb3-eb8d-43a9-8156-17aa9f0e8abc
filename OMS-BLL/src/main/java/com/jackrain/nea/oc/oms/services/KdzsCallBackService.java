package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogisticsTrajectoryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.kdzs.SubLogisticsStatusEnum;
import com.jackrain.nea.oc.oms.model.request.kdzs.KdzsCallBackRequest;
import com.jackrain.nea.oc.oms.model.request.kdzs.LogisticsFullTrace;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLogisticsTrajectory;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.nums.ReturnOrderNodeEnum;
import com.jackrain.nea.resource.SystemTableNames;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 快递助手回调落库&揽收，签收 埋点
 *
 * <AUTHOR>
 * @date 2022年04月06日 16:40
 */
@Slf4j
@Component
public class KdzsCallBackService {

    @Autowired
    private OcBOrderLogisticsTrajectoryMapper logisticsTrajectoryMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderNodeRecordService nodeRecordService;

    public static final String KDZS_CODE = "KDZS";

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveAndUpdateLogisticsTrace(KdzsCallBackRequest kdzsCallBackRequest) {
        log.info("{}.saveAndUpdateLogisticsTrace 快递助手物流轨迹数据插入开始", this.getClass().getSimpleName());
        if (log.isDebugEnabled()) {
            log.debug("快递助手推送物流轨迹入参：{}", JSON.toJSONString(kdzsCallBackRequest));
        }
        try {
            // 获取物流轨迹
            List<LogisticsFullTrace> currentLogisticsFullTraceList = kdzsCallBackRequest.getLogisticsFullTraceList();
            if (CollectionUtils.isEmpty(currentLogisticsFullTraceList)) {
                return ValueHolderV14Utils.getFailValueHolder("物流轨迹为空");
            }
            // 先走ES去拿到ID列表（ID是分库键）
            List<Long> ids = ES4ReturnOrder.queryIdsByLogisticsCodeForLogisticsTrace(kdzsCallBackRequest.getMailNo());
            if (CollectionUtils.isEmpty(ids)) {
                log.info("快递助手订阅回调：物流单号（{}）查询不到退换货单ID", kdzsCallBackRequest.getMailNo());
                return ValueHolderV14Utils.getFailValueHolder("未从ES查询到物流单号关联的退换货单");
            }
            // 根据ES拿到的ID列表去退换货单表ID（这里还需要拿退换货单的数据去填充到物流轨迹表）
            LambdaQueryWrapper<OcBReturnOrder> queryOcBReturnOrderWrapper = new LambdaQueryWrapper();
            queryOcBReturnOrderWrapper.in(OcBReturnOrder::getId, ids);
            queryOcBReturnOrderWrapper.ne(OcBReturnOrder::getReturnStatus, ReturnStatusEnum.CANCLE.getVal());
            queryOcBReturnOrderWrapper.eq(OcBReturnOrder::getIsactive, IsActiveEnum.Y.getKey());
            List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectList(queryOcBReturnOrderWrapper);
            if (CollectionUtils.isEmpty(ocBReturnOrders)) {
                log.debug("快递助手订阅回调：ES去拿到ID列表：{}，查询不到退换货单数据", ids);
                return ValueHolderV14Utils.getFailValueHolder("未查询到有效的退换货单");
            }
            // 循环退换货单
            for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
                LambdaQueryWrapper<OcBOrderLogisticsTrajectory> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.eq(OcBOrderLogisticsTrajectory::getOrigOrderId, ocBReturnOrder.getId());
                queryWrapper.eq(OcBOrderLogisticsTrajectory::getOrigOrderType, OcCommonConstant.BILL_TYPE_RETURN_ORDER);
                queryWrapper.eq(OcBOrderLogisticsTrajectory::getTrackNo, kdzsCallBackRequest.getMailNo());
                List<OcBOrderLogisticsTrajectory> orderLogisticsTrajectoryList = logisticsTrajectoryMapper.selectList(queryWrapper);

                // 去重
                List<LogisticsFullTrace> currentAppendLogisticsFullTraceList = currentLogisticsFullTraceList.stream().filter(
                        a ->
                        {
                            for (OcBOrderLogisticsTrajectory ocBOrderLogisticsTrajectory : orderLogisticsTrajectoryList) {
                                if (StringUtils.equals(ocBOrderLogisticsTrajectory.getRecordTime(), DateUtil.format(new Date(a.getTime()), DateUtil.dateTimeMinisFormatter.getPattern()))
                                        && StringUtils.equals(ocBOrderLogisticsTrajectory.getDescription(), a.getDesc())) {
                                    return false;
                                }
                            }
                            return true;
                        }
                ).collect(Collectors.toList());
                // 无新增的物流轨迹，直接下一个。
                if (CollectionUtils.isEmpty(currentAppendLogisticsFullTraceList)) {
                    log.debug("快递助手订阅回调：退换货单ID{},无新增物流轨迹", ocBReturnOrder.getId());
                    continue;
                }
                List<OcBOrderLogisticsTrajectory> ocBOrderLogisticsTrajectoryList = new ArrayList<>(currentAppendLogisticsFullTraceList.size());
                // 循环物流轨迹
                for (LogisticsFullTrace logisticsFullTrace : currentAppendLogisticsFullTraceList) {
                    OcBOrderLogisticsTrajectory logisticsTrajectory = this.setField(kdzsCallBackRequest, ocBReturnOrder, logisticsFullTrace);
                    ocBOrderLogisticsTrajectoryList.add(logisticsTrajectory);

                    // 揽收时间和签收时间埋点
                    ReturnOrderNodeEnum nodeEnum = null;
                    if (StringUtils.equals(logisticsFullTrace.getSubLogisticsStatus(),
                            SubLogisticsStatusEnum.ACCEPT.getKey())) {
                        nodeEnum = ReturnOrderNodeEnum.EXPRESS_RECEIVE_TIME;
                    } else if (StringUtils.equals(logisticsFullTrace.getSubLogisticsStatus(), SubLogisticsStatusEnum.SIGN.getKey())
                            || StringUtils.equals(logisticsFullTrace.getSubLogisticsStatus(), SubLogisticsStatusEnum.STA_SIGN.getKey())
                            || StringUtils.equals(logisticsFullTrace.getSubLogisticsStatus(), SubLogisticsStatusEnum.RETURN_SIGN.getKey())) {
                        nodeEnum = ReturnOrderNodeEnum.STORE_RECEIVE_TIME;
                    }
                    if (!ObjectUtils.isEmpty(nodeEnum)) {
                        nodeRecordService.batchInsertNotExist(nodeEnum, DateUtil.dateTimeMinisFormatter.parse(logisticsTrajectory.getRecordTime()),
                                Collections.singletonList(logisticsTrajectory.getOrigOrderId()), SystemUserResource.getRootUser());
                    }

                }
                // 落库
                Integer insertNum = logisticsTrajectoryMapper.batchInsert(ocBOrderLogisticsTrajectoryList);
                if (insertNum > 0) {
                    //回写到退单上 物流状态
                    OcBReturnOrder update = new OcBReturnOrder();
                    update.setId(ocBReturnOrder.getId());
                    update.setLogisticsStatus(kdzsCallBackRequest.getLogisticsStatus());
                    Integer updateNum = ocBReturnOrderMapper.updateById(update);
                    if (updateNum > 0) {
                        continue;
                    } else {
                        log.debug("快递助手订阅回调：回写退换货单异常，退换货单ID{}", ocBReturnOrder.getId());
                    }
                } else {
                    log.debug("快递助手订阅回调：物流轨迹落库异常，退换货单ID{}", ocBReturnOrder.getId());
                }
            }
            return ValueHolderV14Utils.getSuccessValueHolder("插入物流轨迹数据成功");
        } catch (Exception e) {
            log.error("新增物流轨迹异常,信息为：{}", Throwables.getStackTraceAsString(e));
            throw new NDSException(String.format("插入物流轨迹异常:%s", e.getMessage()));
        }
    }

    private OcBOrderLogisticsTrajectory setField(KdzsCallBackRequest kdzsCallBackRequest, OcBReturnOrder returnOrder, LogisticsFullTrace logisticsFullTrace) {
        OcBOrderLogisticsTrajectory trajectory = new OcBOrderLogisticsTrajectory();
        // 物流轨迹信息
        trajectory.setDescription(logisticsFullTrace.getDesc());
        // 物流状态（子状态）
        trajectory.setLogisticStatus(logisticsFullTrace.getLogisticsStatus());
        // 填报时间（物流更新时间）
        trajectory.setRecordTime(DateUtil.format(new Date(logisticsFullTrace.getTime()), DateUtil.dateTimeMinisFormatter.getPattern()));
        // 物流单号
        trajectory.setTrackNo(kdzsCallBackRequest.getMailNo());
        // 原单id
        trajectory.setOrigOrderId(returnOrder.getId());
        // 原始订单类型：1-零售发货单；2-退换货单
        trajectory.setOrigOrderType(OcCommonConstant.BILL_TYPE_RETURN_ORDER);

        trajectory.setName(SubLogisticsStatusEnum.getValByKey(logisticsFullTrace.getSubLogisticsStatus()));

//        trajectory.setRemark(logisticsFullTrace.getDesc());
        //物流节点
        trajectory.setStatus(logisticsFullTrace.getSubLogisticsStatus());
        //节点时间
        trajectory.setKpiTime(DateUtil.format(new Date(logisticsFullTrace.getTime()), DateUtil.dateTimeMinisFormatter.getPattern()));
        trajectory.setCode(KDZS_CODE);
        trajectory.setOperPerson(KDZS_CODE);
        trajectory.setSender(KDZS_CODE);
        trajectory.setOriginalLegno(returnOrder.getTid());
        trajectory.setOriginalOrderCode(returnOrder.getOrigOrderNo());

        trajectory.setLegno(returnOrder.getBillNo());

        BaseModelUtil.makeBaseCreateField(trajectory, SystemUserResource.getRootUser());
        // 设置表ID
        Long id = ModelUtil.getSequence(SystemTableNames.OC_B_ORDER_LOGISTICS_TRAJECTORY);
        trajectory.setId(id);
        return trajectory;
    }
}
