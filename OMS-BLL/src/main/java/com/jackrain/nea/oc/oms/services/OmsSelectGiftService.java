package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPromItemMapper;
import com.jackrain.nea.oc.oms.model.GiftInfo;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromItem;
import com.jackrain.nea.oc.oms.nums.GiftReturnEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.pm.model.result.GiftResult;
import com.jackrain.nea.pm.service.PmSelectGiftService;
import com.jackrain.nea.ps.model.ProductSku;

import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2019-07-19 10:20
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsSelectGiftService {

    @Autowired
    private PmSelectGiftService pmSelectGiftService;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private PropertiesConf propertiesConf;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsSelectGiftService omsSelectGiftService;

    @Autowired
    private OcBOrderPromItemMapper ocBOrderPromItemMapper;

    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;

    public Integer selectGift(OcBOrderRelation orderInfo, User operateUser) {
        try {
            OcBOrder ocBOrder = orderInfo.getOrderInfo(); //订单主表信息
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
            List<OcBOrderItem> ocBOrderItemList = orderItemList.stream().filter(p -> p.getIsGift().equals(0)).collect(Collectors.toList());
            //获取促销返回赠品信息
            if (CollectionUtils.isEmpty(ocBOrderItemList)) {
                return GiftReturnEnum.NO_GIFT.getCode(); //无赠品
            }
            long startTime = System.currentTimeMillis();
            ValueHolderV14<List<GiftResult>> holderV14 = pmSelectGiftService.selectGift(ocBOrder, ocBOrderItemList);
            if (holderV14 == null) {
                return GiftReturnEnum.FAIL.getCode(); //调用失败
            }
            long endTime = System.currentTimeMillis();
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + " 调用促销耗时:{}ms", endTime - startTime);
            }
            if (holderV14.getCode() == -1) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.GIFT_ADD.getKey(), "获取赠品信息失败,原因:" + holderV14.getMessage(), null, null, operateUser);
                return GiftReturnEnum.FAIL.getCode(); //调用失败
            }
            List<GiftResult> giftResults = holderV14.getData();
            List<OcBOrderItem> list = new ArrayList<>(); //所有的赠品信息
            if (CollectionUtils.isNotEmpty(giftResults)) {
                for (GiftResult giftResult : giftResults) {
                    String psCSkuEcode = giftResult.getSku();
                    //数量
                    Long gitfNumber = giftResult.getGitfNumber();
                    //活动名称
                    String actName = giftResult.getActName();
                    //获取所有赠品的sku信息
                    ProductSku productSku = psRpcService.selectProductSku(psCSkuEcode);
                    OcBOrderItem orderItem = this.handleOrderItem(productSku, ocBOrder, gitfNumber, actName);
                    list.add(orderItem);
                }

            } else {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.GIFT_ADD.getKey(), holderV14.getMessage(), null, null, operateUser);
                return GiftReturnEnum.NO_GIFT.getCode();
            }

            omsSelectGiftService.updateOrderInfo(list);
            orderItemList.addAll(list);
            orderInfo.setOrderItemList(orderItemList);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.GIFT_ADD.getKey(), "添加赠品成功", null, null, operateUser);

            BigDecimal conutQty = BigDecimal.ZERO;
            for (OcBOrderItem orderItem : orderItemList) {
                if (orderItem.getQty() != null) {
                    conutQty = conutQty.add(orderItem.getQty());
                }
            }
            ocBOrder.setQtyAll(conutQty);
            int count = ocBOrderItemMapper.selectCountForOrder(ocBOrder.getId());
            ocBOrder.setSkuKindQty(new BigDecimal(count));
            ocBOrder.setIsHasgift(1);  //更新赠品标
            if (log.isDebugEnabled()) {
                long timeMillis = System.currentTimeMillis();
                log.debug(this.getClass().getName() + " 封装数并插入数据库耗时:{}ms", timeMillis - endTime);
            }
            return GiftReturnEnum.SUCCESS.getCode();
        } catch (Exception e) {
            log.error(LogUtil.format("获取赠品信息失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
        return GiftReturnEnum.FAIL.getCode();
    }

    /**
     * hold添加赠品信息
     *
     * @param orderInfo
     * @return
     */
    public Integer holdOrderAddGift(OcBOrderRelation orderInfo, String actName) {
        try {
            long beginTimeMillis = System.currentTimeMillis();
            //订单主表信息
            OcBOrder ocBOrder = orderInfo.getOrderInfo();
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
            //查询出未退款，且包含组合商品的所有明细列表
            List<OcBOrderItem> itemListTmp = ocBOrderItemMapper.selectOrderItemContainsCombination(orderInfo.getOrderInfo().getId());
            List<OcBOrderItem> ocBOrderItemList = orderItemList.stream().filter(p -> (p.getIsGift().equals(0) && p.getProType() == 4) || (p.getIsGift().equals(0) && p.getProType() == 0)).collect(Collectors.toList());
            Map<String,String> skuOidMap = ocBOrderItemList.stream().filter(o -> StringUtils.isNotEmpty(o.getOoid())).collect(Collectors.toMap(OcBOrderItem::getPsCSkuEcode,OcBOrderItem::getOoid,(v1, v2)->v1));
            //获取促销返回赠品信息
            if (CollectionUtils.isEmpty(ocBOrderItemList)) {
                //无赠品
                return GiftReturnEnum.NO_GIFT.getCode();
            }
            //所有的赠品信息
            List<OcBOrderItem> list = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderInfo.getGiftInfoList())) {
                for (GiftInfo giftInfo : orderInfo.getGiftInfoList()) {
                    String psCSkuEcode = giftInfo.getEcode();
                    //数量
                    Long gitfNumber = giftInfo.getQtty().longValue();
                    //来源商品编码
                    String formSkuCode = giftInfo.getSourceEsku();
                    Integer isGiftSplit = giftInfo.getIsGiftSplit();
                    //获取所有赠品的sku信息
                    ProductSku productSku = psRpcService.selectProductSku(psCSkuEcode);
                    if (productSku == null) {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                OrderLogTypeEnum.GIFT_ADD.getKey(), "促销通知添加赠品,当前系统中未查询到该赠品信息！", null, null, SystemUserResource.getRootUser());
                    }
                    /*if(StringUtils.isNotEmpty(formSkuCode)){
                        boolean alreadyAddFlag = true;
                        for (OcBOrderItem Item : itemListTmp) {
                            if (Item.getIsGift() == 0 && formSkuCode.equals(Item.getPsCSkuPtEcode()) && Item.getProType() == 4) {
                                //赠品的来源商品GiftRelation赋值
                                Item.setGiftRelation(formSkuCode);
                                ocBOrderItemMapper.updateGiftReleationByOrderId(formSkuCode,Item.getOcBOrderId(),Item.getPsCSkuEcode());
                            }else if(Item.getIsGift() == 0 && formSkuCode.equals(Item.getPsCSkuPtEcode()) && Item.getProType() == 2 && alreadyAddFlag){
                                //赠品的来源商品GiftRelation赋值
                                alreadyAddFlag = false;
                                Item.setGiftRelation(formSkuCode);
                                ocBOrderItemMapper.updateGiftReleationByOrderId(formSkuCode,Item.getOcBOrderId(),Item.getPsCSkuEcode());
                                break;
                            }else if(Item.getIsGift() == 0 && formSkuCode.equals(Item.getPsCSkuEcode()) && Item.getProType() == 0){
                                //赠品的来源商品GiftRelation赋值
                                Item.setGiftRelation(formSkuCode);
                                ocBOrderItemMapper.updateGiftReleationByOrderId(formSkuCode,Item.getOcBOrderId(),Item.getPsCSkuEcode());
                            }
                        }
                    }*/
                    //挂靠赠品构建，给GiftRelation赋值为formSkuCode,ooid
                    OcBOrderItem orderItem = this.buildHoldOrderItem(productSku, ocBOrder, gitfNumber, actName, formSkuCode, skuOidMap.get(formSkuCode));
                    list.add(orderItem);
                }
            } else {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.GIFT_ADD.getKey(), "无可用的赠品！", null, null, SystemUserResource.getRootUser());
                return GiftReturnEnum.NO_GIFT.getCode();
            }

            omsSelectGiftService.updateOrderInfo(list);
            orderItemList.addAll(list);
            orderInfo.setOrderItemList(orderItemList);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.GIFT_ADD.getKey(), "添加赠品成功", null, null, SystemUserResource.getRootUser());

            BigDecimal conutQty = BigDecimal.ZERO;
            for (OcBOrderItem orderItem : orderItemList) {
                if (orderItem.getQty() != null) {
                    conutQty = conutQty.add(orderItem.getQty());
                }
            }
            ocBOrder.setQtyAll(conutQty);
            ocBOrder.setIsHasgift(1);  //更新赠品标
            if (log.isDebugEnabled()) {
                long timeMillis = System.currentTimeMillis();
                log.debug(this.getClass().getName() + " 封装数并插入数据库耗时:{}ms", timeMillis - beginTimeMillis);
            }
            return GiftReturnEnum.SUCCESS.getCode();
        } catch (Exception e) {
            log.error(LogUtil.format("获取赠品信息失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
        return GiftReturnEnum.FAIL.getCode();
    }

    /**
     * 判断是否释放hold单订单
     *
     * @param orderId
     * @return boolean
     */
    public boolean checkReleaseHoldOrder(Long orderId) {

        List<OcBOrderPromItem> ocBOrderPromItemList = ocBOrderPromItemMapper.selectOcBOrderPromItemList(orderId);

        boolean releaseHoldOrderFlag = true;
        for (OcBOrderPromItem ocBOrderPromItem : ocBOrderPromItemList) {
            if ("N".equals(ocBOrderPromItem.getIsExcute())) {
                releaseHoldOrderFlag = false;
            }
        }
        return releaseHoldOrderFlag;
    }


    /**
     * 封装订单明细信息
     *
     * @param productSku
     */
    private OcBOrderItem handleOrderItem(ProductSku productSku, OcBOrder ocBOrder,
                                         Long gitfNumber, String actName) {

        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);
        //活动编号. 默认赋值为null
        item.setActiveId(null);
        BaseModelUtil.initialBaseModelSystemField(item);
        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //福袋条码
        item.setGiftbagSku(null);
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        item.setIsGift(1);
        //实缺标记
        item.setIsLackstock(0);
        item.setIsPresalesku(0);
        //商品数字编号
        item.setNumIid("0");
        //订单编号
        item.setOcBOrderId(ocBOrder.getId());
        //子订单编号(明细编号)
        item.setOoid(null);
        //平摊金额
        item.setOrderSplitAmt(BigDecimal.ZERO);
        //商品路径
        item.setPicPath(null);

        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        //单行实际成交金额. s.price * s.num - s.discount_fee + s.adjust_fee- part_mjz_discount
        //item.setRealAmt();
        // 平台退款编号
        item.setRefundId(null);
        //退款状态
        // 如果是退款完成，或者是交易关闭 状态=6
        item.setRefundStatus(0);

        this.initialTaobaoOrderItem(productSku, item);
        //库位。不用赋值
//        item.setStoreSite(null);
        //标题
        item.setTitle(null);
        item.setTid(ocBOrder.getTid());
        item.setPrice(BigDecimal.ZERO); //成交单价
        item.setRealAmt(BigDecimal.ZERO); //成交金额
        item.setAmtDiscount(BigDecimal.ZERO);
        item.setAdjustAmt(BigDecimal.ZERO);
        item.setPsCSkuEname(productSku.getSkuName());
        item.setQty(new BigDecimal(gitfNumber));//数量
        item.setActiveId(actName); //活动名称
        return item;
    }


    /**
     * 构建hold单订单明细信息
     *
     * @param productSku
     */
    private OcBOrderItem buildHoldOrderItem(ProductSku productSku, OcBOrder ocBOrder,
                                            Long gitfNumber, String actName, String fromSkuCode, String ooid) {
        OcBOrderItem item = new OcBOrderItem();
        if (StringUtils.isNotEmpty(fromSkuCode)) {
            item.setGiftRelation(fromSkuCode);
        }
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);
        //活动编号. 默认赋值为null
        item.setActiveId(null);
        BaseModelUtil.initialBaseModelSystemField(item);
        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //福袋条码
        item.setGiftbagSku(null);
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        item.setIsGift(1);
        //是否手工商品
        item.setIsManualAdd("1");
        //赠品类型
        item.setGiftType("1");
        //实缺标记
        item.setIsLackstock(0);
        item.setIsPresalesku(0);
        //商品数字编号
        item.setNumIid("0");
        //订单编号
        item.setOcBOrderId(ocBOrder.getId());
        //子订单编号(明细编号)
        item.setOoid(ooid);
        // 平台单号（为后续合单作区分标识）
        item.setTid(ocBOrder.getTid());
        //平摊金额
        item.setOrderSplitAmt(BigDecimal.ZERO);
        //商品路径
        item.setPicPath(null);

        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        //单行实际成交金额. s.price * s.num - s.discount_fee + s.adjust_fee- part_mjz_discount
        //item.setRealAmt();
        // 平台退款编号
        item.setRefundId(null);
        //退款状态
        // 如果是退款完成，或者是交易关闭 状态=6
        item.setRefundStatus(0);

        this.initialTaobaoOrderItem(productSku, item);
        //库位。不用赋值
//        item.setStoreSite(null);
        //标题
        item.setTitle(null);
        item.setTid(ocBOrder.getTid());
        item.setPrice(BigDecimal.ZERO); //成交单价
        item.setRealAmt(BigDecimal.ZERO); //成交金额
        item.setAmtDiscount(BigDecimal.ZERO);
        item.setAdjustAmt(BigDecimal.ZERO);
        item.setPsCSkuEname(productSku.getSkuName());
        item.setQty(new BigDecimal(gitfNumber));//数量
        item.setActiveId(actName); //活动名称
        return item;
    }


    private void initialTaobaoOrderItem(ProductSku productSku, OcBOrderItem item) {
        if (productSku != null) {
            item.setSex(productSku.getSex());
            //2019-08-30吊牌价改为取商品表数据
            item.setPriceTag(productSku.getPricelist()); //吊牌价
            item.setPsCProId(productSku.getProdId());
            item.setBarcode(productSku.getBarcode69());
            // ProECode
            item.setPsCProEcode(productSku.getProdCode());
            item.setPsCProEname(productSku.getName());
            String psSkuEcode = productSku.getSkuEcode();
            if (checkIsNeedTransferSkuUpperCase()) {
                psSkuEcode = StringUtils.upperCase(psSkuEcode);
            }
            item.setPsCSkuEcode(psSkuEcode);
            item.setPsCClrEcode(productSku.getColorCode());
            item.setPsCClrEname(productSku.getColorName());
            item.setPsCClrId(productSku.getColorId());
            item.setStandardWeight(productSku.getWeight());
            item.setSkuSpec(productSku.getSkuSpec());
            item.setPsCSizeEcode(productSku.getSizeCode());
            item.setPsCSizeEname(productSku.getSizeName());
            item.setPsCSizeId(productSku.getSizeId());
            item.setPsCProMaterieltype(productSku.getMaterialType());
            item.setPsCSkuId(productSku.getId());
            item.setMDim4Id(productSku.getMDim4Id());
            item.setMDim6Id(productSku.getMDim6Id());
            //标准价。
            item.setPriceList(BigDecimal.ZERO);
            if ("Y".equals(productSku.getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }


    /**
     * 构建hold单订单明细信息
     *
     * @param productSku
     */
    private OcBOrderItem buildHoldOrderItemNew(ProductSku productSku, OcBOrder ocBOrder,
                                            Long gitfNumber, String actName, String proCode) {
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);
        //活动编号. 默认赋值为null
        item.setActiveId(null);
//        item.setAddIntegral(null);
        BaseModelUtil.initialBaseModelSystemField(item);
        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
//        // 扩展内容. 在转单过程中用不到
//        item.setExtendText(null);
        //福袋条码
        item.setGiftbagSku(null);
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        item.setIsGift(1);
        //是否是手动新增商品
        //item.setIsManualAdd("1");
        //add 标记赠品类型为系统赠品 at 20200528
        item.setGiftType("1");
        //实缺标记
        item.setIsLackstock(0);
        item.setIsPresalesku(0);
        //商品数字编号
        item.setNumIid("0");
        //订单编号
        item.setOcBOrderId(ocBOrder.getId());
        //子订单编号(明细编号)
        item.setOoid(null);
        //平摊金额
        item.setOrderSplitAmt(BigDecimal.ZERO);
        //商品路径
        item.setPicPath(null);

        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        //单行实际成交金额. s.price * s.num - s.discount_fee + s.adjust_fee- part_mjz_discount
        //item.setRealAmt();
        // 平台退款编号
        item.setRefundId(null);
        //退款状态
        // 如果是退款完成，或者是交易关闭 状态=6
        item.setRefundStatus(0);
        BigDecimal qty = new BigDecimal(gitfNumber);
        item.setQty(qty);//数量
        this.initialTaobaoOrderItem(productSku, item, qty);
        //库位。不用赋值
//        item.setStoreSite(null);
        //标题
        item.setTitle(null);
        item.setTid(ocBOrder.getTid());
        item.setPrice(BigDecimal.ZERO); //成交单价
        item.setRealAmt(BigDecimal.ZERO); //成交金额
        item.setAmtDiscount(BigDecimal.ZERO);
        item.setAdjustAmt(BigDecimal.ZERO);
        //item.setPsCSkuEname(productSku.getSkuName()); 迁移到上面this.initialTaobaoOrderItem(productSku, item) 中赋值。目的是防止空指针发生。

        item.setActiveId(actName); //活动名称
        item.setReserveVarchar01(proCode);//促销活动编码
        return item;
    }

    private void initialTaobaoOrderItem(ProductSku productSku, OcBOrderItem item,BigDecimal qty) {
        if (productSku != null) {
            item.setSex(productSku.getSex());
            //2019-08-30吊牌价改为取商品表数据
            item.setPriceTag(productSku.getPricelist()); //吊牌价
            item.setPsCProId(productSku.getProdId());
            item.setBarcode(productSku.getBarcode69());
            // ProECode
            item.setPsCProEcode(productSku.getProdCode());
            item.setPsCProEname(productSku.getName());
            String psSkuEcode = productSku.getSkuEcode();
            if (checkIsNeedTransferSkuUpperCase()) {
                psSkuEcode = StringUtils.upperCase(psSkuEcode);
            }
            item.setPsCSkuPtEcode(psSkuEcode);
            item.setPsCSkuEcode(psSkuEcode);
            item.setPsCClrEcode(productSku.getColorCode());
            item.setPsCClrEname(productSku.getColorName());
            item.setPsCClrId(productSku.getColorId());
            item.setStandardWeight(productSku.getWeight());
            item.setSkuSpec(productSku.getSkuSpec());
            item.setPsCSizeEcode(productSku.getSizeCode());
            item.setPsCSizeEname(productSku.getSizeName());
            item.setPsCSizeId(productSku.getSizeId());
            item.setPsCProMaterieltype(productSku.getMaterialType());
            item.setPsCSkuId(productSku.getId());
            //标准价。
            item.setPriceList(BigDecimal.ZERO);
            if ("Y".equals(productSku.getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            item.setMDim4Id(productSku.getMDim4Id());
            item.setMDim6Id(productSku.getMDim6Id());
            if (productSku.getSkuType() == SkuType.COMBINE_PRODUCT
                    || productSku.getSkuType() == SkuType.GIFT_PRODUCT) {
                item.setProType(NumberUtils.toLong(SkuType.NO_SPLIT_COMBINE + ""));
                item.setGiftbagSku(productSku.getSkuEcode());
                item.setQtyGroup(qty);
                item.setGroupGoodsMark("CG"+item.getId());
            } else {
                item.setProType(NumberUtils.toLong(SkuType.NORMAL_PRODUCT + ""));
            }
            item.setPsCSkuEname(productSku.getSkuName());
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }


    /**
     * 是否需要转换成大写
     * 乔丹项目中：SAP系统存储的SKU部分有小写。为了统一，库里存储的全部为大写。因此在转单的时候强制转换成大写。
     *
     * @return true
     */
    private boolean checkIsNeedTransferSkuUpperCase() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.transfer.sku.toupper", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            return true;
        }
    }

    @Transactional
    public void updateOrderInfo(List<OcBOrderItem> list) {
        //将赠品信息更新到数据库中
        for (OcBOrderItem orderItem : list) {
            ocBOrderItemMapper.insert(orderItem);
        }
//        try {
//            SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
//                    OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, list,
//                    "OC_B_ORDER_ID");
//        } catch (Exception e) {
//            log.error(this.getClass().getName() + " 赠品推送ES失败", e);
//        }
    }

    /**
     * 获取赠品明细
     */
    public List<OcBOrderItem> getGiftOrderItemList (OcBOrderRelation orderInfo, String actName, String promCode){
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        long startTime = System.currentTimeMillis();
        List<OcBOrderItem> giftOrderItemList;
        /**
         * 初始数据
         */
        List<GiftInfo> giftInfoList = orderInfo.getGiftInfoList();
        List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
        OcBOrder order = orderInfo.getOrderInfo();
        Long orderId = order.getId();
        /**
         * 根据促销返回的赠品信息，构建出订单的赠品明细
         */
        long buildGiftOrderItemListStartTime = System.currentTimeMillis();
        giftOrderItemList = this.buildGiftOrderItemList(orderItemList, giftInfoList, actName, promCode, order);
        if (giftOrderItemList.size() == 0) {
            return giftOrderItemList;
        }
        /**
         * 如果赠品中包含组合商品，拆分出组合商品的下挂商品,并把拆分出来的下挂赋值给giftOrderItemList
         */
        long splitCombinationStartTime = System.currentTimeMillis();
        // 过滤出pro_type=4的
        List<OcBOrderItem> combinationHeadOrderItemList = giftOrderItemList.stream().filter(p -> p.getProType() != null && p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(combinationHeadOrderItemList)) {
            List<OcBOrderItem> combinationOrderItemList = omsConstituteSplitService.encapsulationParameter(combinationHeadOrderItemList, order, SystemUserResource.getRootUser(), 0);
            giftOrderItemList.addAll(combinationOrderItemList);
        }
        return giftOrderItemList;
    }

    /**
     * 构建赠品订单明细
     * @return
     */
    public List<OcBOrderItem> buildGiftOrderItemList(List<OcBOrderItem> orderItemList, List<GiftInfo> giftInfoList, String actName, String promCode, OcBOrder ocBOrder) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        List<OcBOrderItem> giftOrderItemList = new ArrayList<>(giftInfoList.size());
        long startTime = System.currentTimeMillis();

        // 过滤出4和0且非赠品的订单明细
        List<OcBOrderItem> orderItemListProType4And0 = orderItemList.stream().filter(obj -> ((obj.getIsGift() == null || obj.getIsGift() == 0) && obj.getProType() != null && (obj.getProType() == SkuType.NO_SPLIT_COMBINE || obj.getProType() == SkuType.NORMAL_PRODUCT))).collect(Collectors.toList());
        for (GiftInfo giftInfo : giftInfoList) {
            String giftInfoSourceEcode = giftInfo.getSourceEsku();
            // 获取赠品SKU信息
            ProductSku productSku = psRpcService.selectProductSku(giftInfo.getEcode());
            Integer isGiftSplit = giftInfo.getIsGiftSplit();
            Integer deliverNode = giftInfo.getDeliverNode();
            Integer intervalTime = giftInfo.getIntervalTime();
            // 构建赠品订单明细
            OcBOrderItem orderItem;
            if (productSku != null) {
                orderItem = this.buildHoldOrderItemNew(productSku, ocBOrder, giftInfo.getQtty().longValue(), actName, promCode);
                orderItem.setIsGiftSplit(isGiftSplit);
                orderItem.setGiftDeliverNode(deliverNode);
                orderItem.setGiftIntervalTime(intervalTime);
                /**
                 * 处理赠品明细的挂靠关系和ooid
                 */
                for (OcBOrderItem oldOrderItem : orderItemListProType4And0) {
                    Integer isGift = oldOrderItem.getIsGift();
                    String psCSkuEcode = oldOrderItem.getPsCSkuEcode();
                    if (isGift == 0 && giftInfoSourceEcode != null && giftInfoSourceEcode.equals(psCSkuEcode)) {
                        orderItem.setGiftRelation(giftInfoSourceEcode);
                        orderItem.setOoid(oldOrderItem.getOoid());
                    }
                }
                giftOrderItemList.add(orderItem);
            } else {
                String msg = "接收到促销中心添加赠品通知，但在添加赠品时，SKU信息不存在。skuCode:"+ giftInfo.getEcode() + " 活动名：" + actName + " 促销编码:" + promCode;
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.GIFT_ADD.getKey(), msg, null, null, SystemUserResource.getRootUser());
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("{} 构建赠品明细, 处理时间:{} 返回结果:{}]", prefix, System.currentTimeMillis() - startTime, JSON.toJSONString(giftOrderItemList));
        }
        return giftOrderItemList;
    }

    /**
     * 更新赠品的挂靠关系
     * @param giftInfoList
     * @param itemList
     */
    public void updateGiftReleation(List<GiftInfo> giftInfoList, List<OcBOrderItem> itemList) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        long startTime = System.currentTimeMillis();
        // 非赠品明细
        List<OcBOrderItem> orderItemListNotGiftList = itemList.stream().filter(obj -> (obj.getIsGift() == null || obj.getIsGift() == 0)).collect(Collectors.toList());
        // 普通商品(protype=0)
        List<OcBOrderItem> orderItemListNormalProductList = orderItemListNotGiftList.stream().filter(obj -> (obj.getProType() != null && obj.getProType() == SkuType.NORMAL_PRODUCT)).collect(Collectors.toList());
        // 组合商品或福袋(protype=4,1,2)
        List<OcBOrderItem> orderItemListCombineProductList = orderItemListNotGiftList.stream().filter(obj -> (obj.getProType() != null
                && (obj.getProType() == SkuType.NO_SPLIT_COMBINE || obj.getProType() == SkuType.COMBINE_PRODUCT ||  obj.getProType() == SkuType.GIFT_PRODUCT))).collect(Collectors.toList());

        List<JSONObject> updateNormalProductList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderItemListNormalProductList)) {
            for (GiftInfo giftInfo : giftInfoList) {
                String formSkuCode = giftInfo.getSourceEsku();
                for (OcBOrderItem orderItem : orderItemListNormalProductList) {
                    if (StringUtils.isNotEmpty(formSkuCode) && formSkuCode.equals(orderItem.getPsCSkuEcode())) {
                        JSONObject updateObj = new JSONObject();
                        updateObj.put("id", orderItem.getId());
                        updateObj.put("orderId", orderItem.getOcBOrderId());
                        updateObj.put("giftRelation", formSkuCode );
                        updateNormalProductList.add(updateObj);
                    }
                }
            }
        }

        List<JSONObject> updateCombineProductList = new ArrayList<>();
        Map<String, List<OcBOrderItem>> combineProductMap = orderItemListCombineProductList.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
        for (String key : combineProductMap.keySet()) {
            List<OcBOrderItem> orderItemList = combineProductMap.get(key);
            for (GiftInfo giftInfo : giftInfoList) {
                String formSkuCode = giftInfo.getSourceEsku();
                for (OcBOrderItem orderItem : orderItemList) {
                    if (StringUtils.isNotEmpty(formSkuCode) &&  formSkuCode.equals(orderItem.getPsCSkuEcode()) && orderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                        JSONObject updateObj = new JSONObject();
                        updateObj.put("orderId", orderItem.getOcBOrderId());
                        updateObj.put("groupGoodsMark", key);
                        updateObj.put("giftRelation", formSkuCode);
                        updateCombineProductList.add(updateObj);
                    }
                }
            }
        }
        if (updateNormalProductList.size() > 0) {
            this.batchUpdateGiftReleation(updateNormalProductList);
        }
        if (updateCombineProductList.size() > 0) {
            this.batchUpdateGiftReleationByGroupGoodsMark(updateCombineProductList);
        }
    }

    /**
     * 批量更新
     * @param list
     */
    public void batchUpdateGiftReleation(List<JSONObject> list){
        for (JSONObject json : list) {
            ocBOrderItemMapper.updateGiftReleationByOrderIdAndId(json.getString("giftRelation"), json.getLong("orderId"), json.getLong("id"));
        }
    }

    /**
     * 批量更新
     * @param list
     */
    public void batchUpdateGiftReleationByGroupGoodsMark(List<JSONObject> list){
        for (JSONObject json : list) {
            ocBOrderItemMapper.updateGiftReleationByOrderIdAndGroupGoodsMark(json.getString("giftRelation"), json.getLong("orderId"), json.getString("groupGoodsMark"));
        }
    }


    public void batchInsertOrderItem(List<OcBOrderItem> list) {
        //将赠品信息更新到数据库中
        for (OcBOrderItem orderItem : list) {
            ocBOrderItemMapper.insert(orderItem);
        }
    }

    /**
     * 统计商品数量和sku种类
     * @param orderItemList
     * @param orderInfo
     */
    public void totalOrderItemQtyAndSkuKindQty(List<OcBOrderItem> orderItemList, OcBOrder orderInfo){
        BigDecimal skuKindQty = BigDecimal.ZERO;
        int countQtyNum = 0;
        for (OcBOrderItem orderItem : orderItemList) {
            if (orderItem.getProType() == null || orderItem.getProType() ==  SkuType.NO_SPLIT_COMBINE || orderItem.getProType() ==  SkuType.NORMAL_PRODUCT) {
                countQtyNum++;
                if (orderItem.getQty() != null) {
                    skuKindQty = skuKindQty.add(orderItem.getQty());
                }
            }
        }
        orderInfo.setSkuKindQty(skuKindQty);
        orderInfo.setQtyAll(new BigDecimal(countQtyNum));
    }

    /**
     * 判断是否释放hold单订单
     *
     * @param orderId
     * @return boolean
     */
    public boolean isReleaseHoldOrder(Long orderId) {
        int count = ocBOrderPromItemMapper.countNotReleaseHoldOrder(orderId);
        if (count == 0) {
            return true;
        }
        return false;
    }

}
