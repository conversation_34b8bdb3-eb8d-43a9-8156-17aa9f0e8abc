package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.IsWriteoffEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 更改是否插入核销流水
 *
 * @author: 周琳胜
 * @since: 2019/4/19
 * create at : 2019/4/19 9:27
 */
@Component
@Slf4j
public class ModifyReturnOrderIsWriteoffService {

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;

    /**
     * 更改是否插入核销流水
     *
     * @param ids
     * @return
     */
    public ValueHolderV14 modifyIsWriteoffService(List<Long> ids) {
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            for (Long id : ids) {
                OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(id);
                if (ocBReturnOrder != null) {
                    ocBReturnOrder.setIsWriteoff(IsWriteoffEnum.ALREADY_WRITEOFF.getVal());
                    ocBReturnOrderMapper.updateById(ocBReturnOrder);
                    // @20201118 去除手推ES代码
                    /*try {
                        Boolean flag = ES4ReturnOrder.updateReturnOrderById(ocBReturnOrderMapper.selectById(id));
                        if (!flag) {
                            throw new NDSException("推送ES失败");
                        }
                    } catch (Exception e) {
                        throw new NDSException("推送ES失败");
                    }*/
                } else {
                    throw new NDSException(Resources.getMessage("未能查询到对应的退换货订单信息!"));
                }

            }
        } catch (Exception e) {
            log.debug("调用更改是否插入核销流水状态服务异常");
        }

        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("更改是否插入核销流水状态成功");
        return vh;
    }
}
