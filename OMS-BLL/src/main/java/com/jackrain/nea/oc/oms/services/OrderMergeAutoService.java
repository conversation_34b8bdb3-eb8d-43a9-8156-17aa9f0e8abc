package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.MergeOderGroups;
import com.jackrain.nea.oc.oms.model.result.MergeEsHavingcountResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCMergeOrderDO;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-07-10 17:58
 * @description: 自动合单任务
 */
@Slf4j
@Component
public class OrderMergeAutoService {
    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    OwnMergeOrderService ownMergeOrderService;
    @Autowired
    OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;
    @Autowired
    StRpcService stRpcService;
    @Autowired
    OrderMergeService orderMergeService;
    @Autowired
    CpRpcService cpRpcService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    /**
     * ES查询所有满足条件的分组 自动合单
     *
     * @return
     */
    //public void orderMergeAuto() {
    //    List<MergeEsHavingcountResult> result = Lists.newArrayList();
    //    String[] groups = orderMergeService.getGroups();//分组条件
    //    JSONObject filterKeyJo = new JSONObject();
    //    JSONObject whereKeys = orderMergeService.getGroupWhereKeys();//查询条件
    //    JSONObject jsonObject = ElasticSearchUtil.havingCount(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
    //            OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeys, filterKeyJo, "1~", groups);
    //    //去除平台=50 唯品会jitX的订单
    //    Object data = jsonObject.get("data");
    //    if (null != data) {
    //        result = JSONArray.parseArray(data.toString(), MergeEsHavingcountResult.class);
    //        result = result.stream().filter(x -> null != x.getMergeOderGroups()
    //                && !x.getMergeOderGroups().getPlatform().equals(PlatFormEnum.VIP_JITX.getCode())).collect(Collectors.toList());
    //    }
    //    this.circleGroups(result, SystemUserResource.getRootUser());
    //    return;
    //}

    /**
     * 循环分组合并订单
     *
     * @param mergeEsHavingcountResultList
     */
    //public void circleGroups(List<MergeEsHavingcountResult> mergeEsHavingcountResultList, User user) {
    //    if (CollectionUtils.isEmpty(mergeEsHavingcountResultList)) {
    //        log.debug(this.getClass().getName() + "没有可合并的组");
    //        return;
    //    }
    //    List<StCMergeOrderDO> stCMergeOrderDOList = stRpcService.queryAllMergeShop();
    //    if (CollectionUtils.isEmpty(stCMergeOrderDOList)) {
    //        log.debug("st查询不到可以执行合单的店铺");
    //        return;
    //    }
    //    for (MergeEsHavingcountResult mergeEsHavingcountResult : mergeEsHavingcountResultList) {
    //        if (null == mergeEsHavingcountResult.getMergeOderGroups()) {
    //            log.debug(this.getClass().getName() + "组信息为空");
    //            continue;
    //        }
    //        //获取一组ids
    //        JSONArray jsonArray = this.getIds(mergeEsHavingcountResult);
    //        //获取锁单的单据集合
    //        Map<String, List> map = this.getLockList(jsonArray, stCMergeOrderDOList);
    //        if (null == map) {
    //            continue;
    //        }
    //        List<RedisReentrantLock> lockListKey = map.get("lockList");//锁集合用来解锁
    //        List<OcBOrder> ocBOrderList = map.get("orderList");//锁定的单据集合 用来业务操作
    //        if (CollectionUtils.isEmpty(ocBOrderList)) {
    //            continue;
    //        }
    //        try {
    //
    //            Boolean isContinue = false;
    //
    //            //开始组装操作数据库的数据
    //            List<Long> ids = ocBOrderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
    //
    //            //反审核操作
    //            Iterator<OcBOrder> iterator = ocBOrderList.iterator();
    //            if (iterator.hasNext()) {
    //                OcBOrder order = iterator.next();
    //                if (order.getOrderStatus().intValue() == OmsOrderStatus.CHECKED.toInteger()) {
    //                    boolean b = orderMergeService.toExamineOrder(order, SystemUserResource.getRootUser());
    //                    if (!b) {
    //                        isContinue = true;
    //                        break;
    //                    }
    //                }
    //            }
    //
    //            if (isContinue) {
    //                continue;
    //            }
    //            OcBOrderRelation ocBOrderRelation = orderMergeService.getCopyOrderAndItem(ocBOrderList, ids, user);
    //            // tagger.002: 合单打标，调用打标服务 @20200710
    //            TaggerManager.get().doTag(ocBOrderRelation.getOrderInfo(), ocBOrderRelation.getOrderItemList());
    //
    //            boolean result = ownMergeOrderService.orderListMerge(ocBOrderList, ocBOrderRelation, user);
    //            if (!result) {
    //                continue;
    //            } else {
    //                orderMergeService.pushEsAddLogAndDistributeLogistics(ocBOrderList, ocBOrderRelation, user);
    //            }
    //        } catch (Exception e) {
    //            log.error("合单订单出现异常", e.getMessage());
    //            continue;
    //        } finally {
    //            for (RedisReentrantLock lockItem : lockListKey) {
    //                lockItem.unlock();
    //            }
    //        }
    //    }
    //}

    /**
     * 获取一组id 存放到JSONArray里
     *
     * @param mergeEsHavingcountResult
     * @return
     */
    public JSONArray getIds(MergeEsHavingcountResult mergeEsHavingcountResult) {
        if (null == mergeEsHavingcountResult.getMergeOderGroups()) {
            return null;
        }
        MergeOderGroups mergeOderGroups = mergeEsHavingcountResult.getMergeOderGroups();
        String jsonString = JSONObject.toJSONString(mergeOderGroups);
        JSONObject whereKeyJo = JSONObject.parseObject(jsonString);
        whereKeyJo.putAll(orderMergeService.getGroupQueryWhereKey());
        JSONObject filterKeyJo = new JSONObject();
        JSONObject esOrderJo = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeyJo, null, null,
                100, 0, new String[]{"ID"});
        if (null == esOrderJo) {
            return null;
        }
        JSONArray aryIds = esOrderJo.getJSONArray("data");
        if (aryIds == null || aryIds.size() < 2) {
            return null;
        }
        JSONArray jsonArray = new JSONArray();
        for (Object o : aryIds) {
            JSONObject jsonObject = (JSONObject) o;
            Map<String, Long> mas = new HashMap<>();
            Integer id = (Integer) jsonObject.get("ID");
            mas.put("ID", id.longValue());
            jsonArray.add(mas);
        }
        return jsonArray;
    }

    /**
     * 获取一组锁单的订单
     *
     * @param jsonArray
     * @param stCMergeOrderDOList
     * @return
     */
    public Map<String, List> getLockList(JSONArray jsonArray, List<StCMergeOrderDO> stCMergeOrderDOList) {
        Map<String, List> map = orderMergeService.getLock(jsonArray);
        //锁集合用来解锁
        List<RedisReentrantLock> lockListKey = map.get("lockList");
        try {
            if (CollectionUtils.isNotEmpty(lockListKey) && lockListKey.size() == 1) {
                //锁定成功的单据数=1直接解锁
                for (RedisReentrantLock lockItem : lockListKey) {
                    lockItem.unlock();
                }
                return null;
            }
            //ids代表锁定成功的id集合 为空代表全部锁定失败
            List<Long> ids = map.get("ids");
            if (CollectionUtils.isNotEmpty(ids)) {
                List<OcBOrder> dbOcBOrderList = ocBOrderMapper.selectOrderListByIds(ids);
                if (CollectionUtils.isEmpty(dbOcBOrderList) || dbOcBOrderList.size() == 1) {
                    //根据id数据库查不到数据或者只能查到一条 直接解锁
                    for (RedisReentrantLock lockItem : lockListKey) {
                        lockItem.unlock();
                    }
                    return null;
                }

                //校验店铺是否允许合并订单
                List<OcBOrder> finalDbOcBOrderList = dbOcBOrderList;
                List<StCMergeOrderDO> checkShop = stCMergeOrderDOList.stream().filter(x -> x.getIsAutomerge() == 1
                        && x.getCpCShopId().equals(finalDbOcBOrderList.get(0).getCpCShopId())).collect(Collectors.toList());
                if (checkShop.size() < 1) {
                    return null;
                }
                StCMergeOrderDO stCMergeOrderDO = checkShop.get(0);

                Iterator<OcBOrder> iterator = dbOcBOrderList.iterator();
                while (iterator.hasNext()) {
                    OcBOrder next = iterator.next();

                    //默认把预售状态为预售尾款未付的订单排除，不允许自动合并。
                   /* String reserveVarchar03 = next.getReserveVarchar03();
                    if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equals(reserveVarchar03)) {
                        ids.remove(next.getId());
                        iterator.remove();
                        continue;
                    }*/

                    if (stCMergeOrderDO.getIsAutochange().intValue() == 0) {
                        ids.remove(next.getId());
                        iterator.remove();
                        continue;
                    }

                    //如果开启自动合并直播订单  不处理  关闭的去掉直播订单
                /*    if (stCMergeOrderDO.getIsMergerLive().intValue() == 0 && next.getLiveFlag().equals("1")) {
                        ids.remove(next.getId());
                        iterator.remove();
                        continue;
                    }*/
                }
                if (CollectionUtils.isEmpty(dbOcBOrderList)) {
                    return null;
                }
                dbOcBOrderList = dbOcBOrderList.stream()
                        .filter(x -> OmsOrderStatus.UNCONFIRMED.toInteger().equals(x.getOrderStatus())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dbOcBOrderList) && dbOcBOrderList.size() > 1) {
                    //取出所有待审核的订单,并且订单数>2
                    if (!this.checkOrderNum(dbOcBOrderList, ids)) {
                        //自动合并订单不允许大于10单
                        for (RedisReentrantLock lockItem : lockListKey) {
                            lockItem.unlock();
                        }
                        return null;
                    }
                    map.put("orderList", dbOcBOrderList);
                    return map;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("合并订单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            for (RedisReentrantLock lockItem : lockListKey) {
                lockItem.unlock();
            }
        }
        return null;
    }

    /**
     * 校验自动合单的单数是否大于10
     *
     * @param dbOcBOrderList
     * @return
     */
    public boolean checkOrderNum(List<OcBOrder> dbOcBOrderList, List<Long> ids) {
        List<String> sourceList = dbOcBOrderList.stream()
                .map(OcBOrder::getSourceCode).collect(Collectors.toList());
        int sumMergeOrder = 0;
        for (String source : sourceList) {
            String[] mergeSource = source.split(",");
            sumMergeOrder += mergeSource.length;
        }
        if (sumMergeOrder > 10) {
            return false;
        }
        CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(
                dbOcBOrderList.get(0).getCpCPhyWarehouseId());
        if (cpCPhyWarehouse == null) {
            return false;
        }

        /*long qtyAll = dbOcBOrderList.stream().map(OcBOrder::getQtyAll).count();
        if (cpCPhyWarehouse.getQtyPkgMax() == null || cpCPhyWarehouse.getLinePkgMax() == null) {
            log.error(cpCPhyWarehouse.getEcode() + "该实体仓库未配置单包最大商品数或订单商品总行数");
            return false;
        } else if (cpCPhyWarehouse.getQtyPkgMax().compareTo(new BigDecimal(qtyAll)) < 0) {
            log.error("合并后的订单商品总数量超过仓库(仓库编码：" + cpCPhyWarehouse.getEcode() + ")限制的" + cpCPhyWarehouse.getQtyPkgMax() + "件");
            return false;
        } else if (ocBOrderItemMapper.selectItemsCountByOrderIds(ids) > cpCPhyWarehouse.getLinePkgMax()) {
            log.error("合并后的订单商品总行数超过仓库(仓库编码：" + cpCPhyWarehouse.getEcode() + ")限制的" + cpCPhyWarehouse.getQtyPkgMax());
            return false;
        }*/
        return true;
    }
}
