package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderHoldConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.request.StCOrderLabelRequest;
import com.jackrain.nea.st.model.table.StCOrderLabelDO;
import com.jackrain.nea.st.model.table.StCOrderLabelItemDO;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/7/29 下午3:27
 * @Version 1.0
 * 订单打标策略 (原有的转单里的打标废弃)
 */
@Slf4j
@Component
public class OmsOrderMarkStService {

    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private CpRpcService cpRpcService;

    /**
     * @param orderParam 订单信息
     * @param user
     */
    public void orderMarkStService(OcBOrderParam orderParam, User user) {
        OcBOrder ocBOrder = orderParam.getOcBOrder();
        List<OcBOrderItem> orderItemList = orderParam.getOrderItemList();

        //冻结店铺打标
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        if (!Objects.isNull(cpShop)) {
            if (IsActiveEnum.N.getKey().equals(cpShop.getIsactive())) {
                OcBOrder order = new OcBOrder();
                order.setId(ocBOrder.getId());
                order.setIsUnavailableShop(OcBOrderConst.IS_STATUS_IY);
                order.setModifieddate(new Date());
                ocBOrderMapper.updateById(order);
            }
        }

        //获取策略
        List<StCOrderLabelRequest> stCOrderLabelRequests = stRpcService.selectCOrderLabelList(ocBOrder.getCpCShopId());
        if (CollectionUtils.isEmpty(stCOrderLabelRequests)) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.LABLEPARSE.getKey(), "未匹配到打标策略", "", "", user);
            return;
        }
        Date orderDate = ocBOrder.getOrderDate();
        Date payTime = ocBOrder.getPayTime();
//        if (payTime == null) {
//            payTime = ocBOrder.getPresaleDepositTime();
//        }
        List<StCOrderLabelRequest> filterList = stCOrderLabelRequests.stream()
                .filter(o -> (OcBOrderHoldConst.ORDER_DATE.equals(Integer.valueOf(o.getStCOrderLabelDO().getTimeType())) && orderDate.before(o.getStCOrderLabelDO().getEndTime()) && orderDate.after(o.getStCOrderLabelDO().getBeginTime()))
                        || (OcBOrderHoldConst.PAY_TIME.equals(Integer.valueOf(o.getStCOrderLabelDO().getTimeType())) && payTime.before(o.getStCOrderLabelDO().getEndTime()) && payTime.after(o.getStCOrderLabelDO().getBeginTime())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.LABLEPARSE.getKey(), "未匹配到有效的自定义打标策略", "", "", user);
            return;
        }
        List<StCOrderLabelDO> labelDOS = new ArrayList<>();
        //商品校验
        List<StCOrderLabelItemDO> labelItemDOS = new ArrayList<>();
        //策略检验
        List<StCOrderLabelItemDO> labelStItemDOS = new ArrayList<>();
        for (StCOrderLabelRequest request : filterList) {
            StCOrderLabelDO stCOrderLabelDO = request.getStCOrderLabelDO();
            if (stCOrderLabelDO != null) {
                labelDOS.add(stCOrderLabelDO);
            }
            List<StCOrderLabelItemDO> stCOrderLabelItemDOList = request.getStCOrderLabelItemDOList();
            if (CollectionUtils.isNotEmpty(stCOrderLabelItemDOList)) {
                labelItemDOS.addAll(stCOrderLabelItemDOList);
            }
            List<StCOrderLabelItemDO> stCOrderLabelStrategyItemDOList = request.getStCOrderLabelStrategyItemDOList();
            if (CollectionUtils.isNotEmpty(stCOrderLabelStrategyItemDOList)) {
                labelStItemDOS.addAll(stCOrderLabelStrategyItemDOList);
            }
        }
        if (CollectionUtils.isEmpty(labelItemDOS) && CollectionUtils.isEmpty(labelStItemDOS)) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.LABLEPARSE.getKey(), "未匹配到有效的自定义打标策略", "", "", user);
            return;
        }
        List<Long> mainIdList = labelDOS.stream().map(StCOrderLabelDO::getId).collect(Collectors.toList());
        List<Long> stIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(labelItemDOS)) {
            stIds = matchSt(labelStItemDOS, orderItemList, ocBOrder);
            log.error(LogUtil.format("没有商品明细返回:{}", "没有商品明细返回"), JSONObject.toJSONString(stIds));
        } else {
            List<Long> mainIds = matchPro(labelItemDOS, orderItemList, mainIdList);
            if (CollectionUtils.isEmpty(mainIds)) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.LABLEPARSE.getKey(), "未匹配到有效的自定义打标策略中的商品条件", "", "", user);
                return;
            }
            if (CollectionUtils.isEmpty(labelStItemDOS)) {
                stIds = mainIds;
            } else {
                Map<Long, List<StCOrderLabelItemDO>> labelMap = labelStItemDOS.stream().collect(Collectors.groupingBy(StCOrderLabelItemDO::getStCOrderLabelId));
                List<StCOrderLabelItemDO> stLabelItemDOList = new ArrayList<>();
                for (Long mainId : mainIds) {
                    if (CollectionUtils.isNotEmpty(labelMap.get(mainId))) {
                        stLabelItemDOList.addAll(labelMap.get(mainId));
                    }
                }
                stIds = matchSt(stLabelItemDOList, orderItemList, ocBOrder);
            }
        }
        this.handleOrder(ocBOrder, labelDOS, stIds, user);
    }


    private void handleOrder(OcBOrder ocBOrder, List<StCOrderLabelDO> labelDOS, List<Long> stIds, User user) {
        if (CollectionUtils.isNotEmpty(stIds)) {
            String oldStCCustomLabelId = ocBOrder.getStCCustomLabelId();
            String oldStCCustomLabelName = ocBOrder.getStCCustomLabelEname();
            List<StCOrderLabelDO> lists = new ArrayList<>();
            Map<Long, StCOrderLabelDO> LabelMap = labelDOS.stream().collect(Collectors.toMap(StCOrderLabelDO::getId, Function.identity(), (key1, key2) -> key2));
            for (Long stId : stIds) {
                lists.add(LabelMap.get(stId));
            }
            OcBOrder order = new OcBOrder();
            List<String> labelIdStr = new ArrayList<>();
            List<String> labelNameStr = new ArrayList<>();
            for (StCOrderLabelDO list : lists) {
                String labelId = list.getStCCustomLabelId();
                String stCCustomLabelEname = list.getStCCustomLabelEname();
                labelIdStr.add(labelId);
                labelNameStr.add(stCCustomLabelEname);
            }
            if (StringUtils.isNotEmpty(oldStCCustomLabelId)) {
                labelIdStr.add(oldStCCustomLabelId);
            }
            if (StringUtils.isNotEmpty(oldStCCustomLabelName)) {
                labelNameStr.add(oldStCCustomLabelName);
            }
            order.setId(ocBOrder.getId());
            order.setStCCustomLabelId(StringUtils.join(labelIdStr, ","));
            order.setStCCustomLabelEname(StringUtils.join(labelNameStr, ","));
            ocBOrderMapper.updateById(order);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.LABLEPARSE.getKey(), "订单打标成功", "", "", user);
        } else {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.LABLEPARSE.getKey(), "未匹配到有效的自定义打标策略中的策略条件", "", "", user);
        }

    }


    /**
     * 匹配策略
     *
     * @param labelStItemDOS
     * @param orderItemList
     * @return
     */
    private List<Long> matchSt(List<StCOrderLabelItemDO> labelStItemDOS, List<OcBOrderItem> orderItemList, OcBOrder ocBOrder) {
        List<Long> mainList = new ArrayList<>();
        Map<Long, List<StCOrderLabelItemDO>> stMap = labelStItemDOS.stream().collect(Collectors.groupingBy(StCOrderLabelItemDO::getStCOrderLabelId));
        for (Long mainId : stMap.keySet()) {
            boolean flag = true;
            List<StCOrderLabelItemDO> labelItemDOS = stMap.get(mainId);
            for (StCOrderLabelItemDO labelItemDO : labelItemDOS) {
                String rulesRecognition = labelItemDO.getRulesRecognition();
                Integer compareType = labelItemDO.getCompareType();
                String contentMatch = labelItemDO.getContent();
                boolean b = matchStItem(rulesRecognition, orderItemList, contentMatch, ocBOrder, compareType);
                if (!b) {
                    //条件为且  只要有一个不满足  则直接跳出
                    flag = false;
                    break;
                }
            }
            if (flag) {
                mainList.add(mainId);
            }
        }
        return mainList;
    }

    /**
     * 匹配策略明细
     *
     * @param rulesRecognition
     * @param orderItemList
     * @param contentMatch
     * @param ocBOrder
     * @return compare_type` int(11) DEFAULT '2' COMMENT '条件：1大于 2等于 3小于 4包含 5不包含',
     */
    private boolean matchStItem(String rulesRecognition, List<OcBOrderItem> orderItemList,
                                String contentMatch, OcBOrder ocBOrder, Integer compareType) {
        log.error(LogUtil.format("匹配策略:策略内容:{},compareType:{},rulesRecognition:{}", "匹配策略"), contentMatch, compareType, rulesRecognition);
        if (AdvanceConstant.NICK.equals(rulesRecognition)) {
            String content = ocBOrder.getUserNick();
            return content.equals(contentMatch);
        }
        //手机
        if (AdvanceConstant.PNUM.equals(rulesRecognition)) {
            String content = ocBOrder.getReceiverMobile();
            return content.equals(contentMatch);
        }
        if (AdvanceConstant.MNAM.equals(rulesRecognition)) {
            String content = ocBOrder.getReceiverName();
            return content.equals(contentMatch);
        }
        if (AdvanceConstant.PRO.equals(rulesRecognition)) {
            String content = ocBOrder.getCpCRegionProvinceEname();
            return content.equals(contentMatch);
        }

        if (AdvanceConstant.ANCHOR.equals(rulesRecognition)) {
            String content = ocBOrder.getAnchorId();
            if (StringUtils.isNotEmpty(content)) {
                return content.equals(contentMatch);
            }
        }

        // 卖家备注
        if (AdvanceConstant.SEREMARK.equals(rulesRecognition)) {
            String content = ocBOrder.getSellerMemo();
            if (StringUtils.isNotBlank(content)) {
                return matchCompareType(compareType, content, contentMatch);
            }
        }

        //收货地址
        if (AdvanceConstant.ARESS.equals(rulesRecognition)) {
            String content = ocBOrder.getReceiverAddress();
            return matchCompareType(compareType, content, contentMatch);
        }
        //数量
        if (AdvanceConstant.NUM.equals(rulesRecognition)) {
            BigDecimal content = orderItemList.stream().map(OcBOrderItem::getQty).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            return matchCompareType(compareType, content.intValue() + "", contentMatch);
        }
        if (AdvanceConstant.PRICE.equals(rulesRecognition)) {
            String content = ocBOrder.getOrderAmt() + "";
            return matchCompareType(compareType, content, contentMatch);
        }
        if (AdvanceConstant.SKU.equals(rulesRecognition)) {
            Map<String, List<OcBOrderItem>> proMap = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCProEcode));
            String content = proMap.keySet().size() + "";
            return matchCompareType(compareType, content, contentMatch);
        }
        return false;
    }

    /**
     * compare_type` int(11) DEFAULT '2' COMMENT '条件：1大于 2等于 3小于 4包含 5不包含',
     *
     * @param compareType
     * @return
     */
    private boolean matchCompareType(Integer compareType, String content, String contentMatch) {
        if (compareType == 1 || compareType == 2 || compareType == 3) {
            boolean contentNum = NumberUtils.isNumber(content);
            boolean contentMatchNum = NumberUtils.isNumber(contentMatch);
            if (!contentNum || !contentMatchNum) {
                return contentMatch.equals(content);
            }
            BigDecimal a = new BigDecimal(content);
            BigDecimal b = new BigDecimal(contentMatch);
            if (compareType == 1) {
                return a.compareTo(b) > 0;
            }
            if (compareType == 2) {
                return a.compareTo(b) == 0;
            }
            return a.compareTo(b) < 0;
        }
        if (compareType == 4) {
            return content.contains(contentMatch);
        }
        if (compareType == 5) {
            return !content.contains(contentMatch);
        }
        return false;
    }


    /**
     * 返回满足商品条件的id
     *
     * @param labelItemDOS
     * @param orderItemList
     * @return
     */
    private List<Long> matchPro(List<StCOrderLabelItemDO> labelItemDOS, List<OcBOrderItem> orderItemList, List<Long> mainIdList) {
        List<Long> mainList = new ArrayList<>();
        Map<Long, List<StCOrderLabelItemDO>> proMap = labelItemDOS.stream().collect(Collectors.groupingBy(StCOrderLabelItemDO::getStCOrderLabelId));
        for (Long mainId : mainIdList) {
            List<StCOrderLabelItemDO> labelItemDOS1 = proMap.get(mainId);
            if (CollectionUtils.isEmpty(labelItemDOS1)) {
                mainList.add(mainId);
                continue;
            }
            for (StCOrderLabelItemDO stCOrderLabelItemDO : labelItemDOS1) {
                String rulesRecognition = stCOrderLabelItemDO.getRulesRecognition();
                String contentMatch = stCOrderLabelItemDO.getContent();
                if (matchProItem(rulesRecognition, orderItemList, contentMatch)) {
                    mainList.add(mainId);
                    break;
                }
            }
        }
        return mainList;
    }

    private boolean matchProItem(String rulesRecognition, List<OcBOrderItem> orderItemList, String contentMatch) {
        boolean flag = false;
        for (OcBOrderItem item : orderItemList) {
            String content = "";
            if (AdvanceConstant.PTSKU.equals(rulesRecognition)) {
                content = item.getSkuNumiid();
            }
            if (AdvanceConstant.MSKU.equals(rulesRecognition)) {
                content = item.getPsCSkuEcode();
                flag =  contentMatch.contains(content);
            }
            if (AdvanceConstant.TPID.equals(rulesRecognition)) {
                content = item.getNumIid();
                flag = contentMatch.contains(content);
            }
            if (AdvanceConstant.MPRO.equals(rulesRecognition)) {
                content = item.getPsCProEcode();
            }
            if (AdvanceConstant.NUTL.equals(rulesRecognition)) {
                content = item.getPsCProEname();
            }
            if (handleMatch(contentMatch, content)) {
                flag = true;
            }
             if (flag){
                return true;
            }
        }
        return false;
    }

    private boolean handleMatch(String contentMatch, String content) {
        return contentMatch.contains(content);
    }
}
