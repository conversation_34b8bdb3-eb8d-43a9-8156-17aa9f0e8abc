package com.jackrain.nea.oc.oms.services.directreport;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCDistributionOrganization;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.dto.OcBDirectReportOrderImportVo;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBDirectReportOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBDirectReportOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBDirectReportOrderStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrder;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrderItem;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgNewRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 直发预占保存
 *
 * <AUTHOR>
 * @since 2026-06-06 09:07
 */
@Slf4j
@Service
public class OcBDirectReportOrderImportService {
    @Resource
    private ExportUtil exportUtil;

    @Resource
    private CpRpcService cpRpcService;

    @Resource
    private SgNewRpcService sgNewRpcService;
    @Resource
    private PsRpcService psRpcService;


    @Resource
    private OcBDirectReportOrderMapper ocBDirectReportOrderMapper;

    @Resource
    private OcBDirectReportOrderItemMapper ocBDirectReportOrderItemMapper;

    public ValueHolderV14 genTemplateDownloadUrl() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "头明细导入模板下载成功！");
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String titleName = "注意：\n" +
                "1、红色标注项为必填项；\n" +
                "2、时间与日期格式：yyyyMMdd,例20250617；\n" +
                "3、输入时，不允许有空隔，否则无法识别；\n" +
                "4、单据日期为空时默认当前日期；";
        String[] mainNames = {"平台店铺编码", "分货部门编码", "配销仓编码", "逻辑仓编码", "预计发货时间", "库存释放时间", "商品编码", "数量", "开始生产时间", "结束生产时间"};
        String[] mustNames = {"平台店铺编码", "分货部门编码", "配销仓编码", "逻辑仓编码", "预计发货时间", "库存释放时间", "商品编码", "数量", "开始生产时间", "结束生产时间"};
//        String[] orderKeys = {"cpCShopName", "cpCLogisticsName"};

        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List<String> mainList = Lists.newArrayList(mainNames);
        List<String> mustList = Lists.newArrayList(mustNames);
//        List<String> orderKeyList = Lists.newArrayList(orderKeys);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        hssfWorkbook = exportUtil.executeSheet(hssfWorkbook, "头明细", titleName, mainList, mustList,
                null, Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "头明细导入模板",
                user, "OSS-Bucket/EXPORT/OC_B_DIRECT_REPORT_IMPORT/");
        holderV14.setData(putMsg);
        return holderV14;
    }

    public ValueHolderV14 importVoList(List<OcBDirectReportOrderImportVo> importVoList, User user) {
        ValueHolderV14 holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "导入成功！");
        //数据校验及对象构建
        Map<OcBDirectReportOrder, List<OcBDirectReportOrderItem>> importDtoMap = new HashMap<>();
        List<OcBDirectReportOrderImportVo> errorDataList =
                checkValidAndBuildBean(importVoList, importDtoMap, user);
        if (CollectionUtils.isNotEmpty(errorDataList)) {
            holderV14 = new ValueHolderV14<>();
            holderV14.setData(exportResult(errorDataList, user));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("头明细导入失败条数:[" + errorDataList.size() + "]，成功条数:[" + 0 + "]，详情见文件内容");
            return holderV14;
        }
        OcBDirectReportOrderImportService bean = ApplicationContextHandle.getBean(OcBDirectReportOrderImportService.class);
        int successNum = bean.batchSave(importDtoMap);
        int failNum = importVoList.size() - successNum;
        if (failNum > 0) {
            holderV14 = new ValueHolderV14<>();
            OcBDirectReportOrderImportVo importVo = new OcBDirectReportOrderImportVo();
            importVo.setRowNum(1);
            importVo.setErrorDesc("数据保存失败或者保存数据有丢失！");
            errorDataList.add(importVo);
            holderV14.setData(exportResult(errorDataList, user));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("头明细导入失败条数:[" + failNum + "]，成功条数:[" + successNum + "]，详情见文件内容");
        } else {
            holderV14.setMessage("头明细导入成功条数:[" + successNum + "]");
        }
        log.info("头明细导入结果 {}", JSON.toJSONString(holderV14));
        return holderV14;
    }


    @Transactional(rollbackFor = Exception.class)
    public int batchSave(Map<OcBDirectReportOrder, List<OcBDirectReportOrderItem>> importDtoMap) {
        int successNum = 0;
        for (Map.Entry<OcBDirectReportOrder, List<OcBDirectReportOrderItem>> entry : importDtoMap.entrySet()) {
            OcBDirectReportOrder main = entry.getKey();
            List<OcBDirectReportOrderItem> items = entry.getValue();
            if (main != null && CollectionUtils.isNotEmpty(items)) {
                int mainRet = ocBDirectReportOrderMapper.insert(main);
                if (mainRet != 1) {
                    throw new NDSException("主表保存失败！");
                }
                int itenRet = ocBDirectReportOrderItemMapper.batchInsert(items);
                if (itenRet <= 0) {
                    throw new NDSException("明细保存失败！");
                }
                successNum = successNum + itenRet;
            }
        }
        return successNum;
    }

    /**
     * 数据校验及对象构建
     *
     * @param importVoList
     * @param importDtoMap
     * @param user
     * @return
     */
    private List<OcBDirectReportOrderImportVo> checkValidAndBuildBean(List<OcBDirectReportOrderImportVo> importVoList,
                                                                      Map<OcBDirectReportOrder, List<OcBDirectReportOrderItem>> importDtoMap,
                                                                    User user) {
        //校验不通过数据
        List<OcBDirectReportOrderImportVo> errorDateList = new ArrayList<>();
        //主子表分组、必填校验、关联数据查询条件收集
        Map<String, List<OcBDirectReportOrderImportVo>> groupByMainTable = new HashMap<>();

        //店铺名称
        Set<String> cpCShopCodes = new HashSet<>();
        //部门名称
        Set<String> cpCDisOrgLv2Codes = new HashSet<>();
        //配销仓编码名称
        Set<String> sgCSaStoreCodes = new HashSet<>();
        //逻辑仓名称
        Set<String> cpCStoreCodes = new HashSet<>();
        //sku编码
        List<String> skuCodes = new ArrayList<>();

        //错误信息
        StringBuilder errorMsg = new StringBuilder();
        //主子表分组、必填校验、关联数据查询条件收集
        requiredCheck(importVoList, errorDateList, groupByMainTable,
                cpCShopCodes, cpCDisOrgLv2Codes, sgCSaStoreCodes, cpCStoreCodes, skuCodes,
                errorMsg);

        //一阶段校验通过的数据进行二阶段校验
        if (CollectionUtils.isEmpty(importVoList)) {
            return errorDateList;
        }

        //验引用表数据是否存在，并校验主表逻辑
        quoteTableCheck(errorDateList, groupByMainTable,
                cpCShopCodes, cpCDisOrgLv2Codes, sgCSaStoreCodes, cpCStoreCodes, skuCodes,
                errorMsg, importDtoMap, user);
        return errorDateList;
    }


    /**
     * 主子表分组、必填校验、关联数据查询条件收集
     */
    private void requiredCheck(List<OcBDirectReportOrderImportVo> importVoList,
                               List<OcBDirectReportOrderImportVo> errorDateList,
                               Map<String, List<OcBDirectReportOrderImportVo>> groupByMainTable,
                               Set<String> cpCShopCodes,
                               Set<String> cpCDisOrgLv2Codes,
                               Set<String> sgCSaStoreCodes,
                               Set<String> cpCStoreCodes,
                               List<String> skuCodes,
                               StringBuilder errorMsg) {
        Iterator<OcBDirectReportOrderImportVo> iterator = importVoList.iterator();
        while (iterator.hasNext()) {
            OcBDirectReportOrderImportVo importVo = iterator.next();

            //必填校验&&查询数据收集
            if (StringUtils.isEmpty(importVo.getCpCShopCode())) {
                errorMsg.append("平台店铺编码不能为空！");
            } else {
                cpCShopCodes.add(importVo.getCpCShopCode());
            }
            if (StringUtils.isEmpty(importVo.getCpCDisOrgLv2Code())) {
                errorMsg.append("分货部门编码不能为空！");
            } else {
                cpCDisOrgLv2Codes.add(importVo.getCpCDisOrgLv2Code());
            }
            if (StringUtils.isEmpty(importVo.getSgCSaStoreCode())) {
                errorMsg.append("配销仓编码不能为空！");
            } else {
                sgCSaStoreCodes.add(importVo.getSgCSaStoreCode());
            }
            if (StringUtils.isEmpty(importVo.getCpCStoreCode())) {
                errorMsg.append("逻辑仓编码不能为空！");
            } else {
                cpCStoreCodes.add(importVo.getCpCStoreCode());
            }
            if (StringUtils.isEmpty(importVo.getPsCSkuEcode())) {
                errorMsg.append("商品编码不能为空！");
            } else {
                skuCodes.add(importVo.getPsCSkuEcode());
            }

            if (!isValidNumber(importVo.getQty())) {
                errorMsg.append("调拨数量只能为正整数！");
            }


            //一阶段校验不通过，丢到错误集合并从当前集合移除
            if (errorMsg.length() > 0) {
                OcBDirectReportOrderImportVo errorData = new OcBDirectReportOrderImportVo();
                errorData.setRowNum(importVo.getRowNum());
                errorData.setErrorDesc(errorMsg.toString());
                errorDateList.add(errorData);
                iterator.remove();
                errorMsg.setLength(0);
                continue;
            }
            //主子表分组，后面按照分组处理
            String mainKey = generateMainKey(importVo);
            if (groupByMainTable.containsKey(mainKey)) {
                groupByMainTable.get(mainKey).add(importVo);
            } else {
                List<OcBDirectReportOrderImportVo> newList = new ArrayList<>();
                newList.add(importVo);
                groupByMainTable.put(mainKey, newList);
            }
        }
    }

    private String generateMainKey(OcBDirectReportOrderImportVo importVo) {
        return importVo.getCpCShopCode() + "_"
                + importVo.getCpCDisOrgLv2Code() + "_"
                + importVo.getSgCSaStoreCode() + "_"
                + importVo.getCpCStoreCode() + "_"
                + importVo.getEstimateConTimeStr() + "_"
                + importVo.getAutoReleaseTimeStr();
    }

    private String exportResult(List<OcBDirectReportOrderImportVo> errorList, User user) {
        // 列名
        String[] columnNames = {"主表行号", "错误原因"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"rowNum", "errorDesc"};
        List<String> k = Lists.newArrayList(keys);
        Workbook hssfWorkbook = exportUtil.execute("直发预占单", "直发预占单", c, k, errorList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "直发预占单错误信息", user, "OSS-Bucket/IMPORT/OC_B_DIRECT_REPORT_IMPORT/");
    }

    /**
     * 判断字符串是否为正整数
     *
     * @param str 字符串
     * @return 是否为正整数（true: 是; false: 否）
     */
    private boolean isValidNumber(String str) {
        try {
            int num = Integer.parseInt(str);
            return num >= 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }


    /**
     * 校验引用表数据是否存在，并校验主表逻辑
     *
     * @param errorDateList
     * @param groupByMainTable
     * @param sgCSaStoreCodes
     * @param skuCodes
     * @param errorMsg
     * @param importDtoMap
     * @param user
     */
    private void quoteTableCheck(List<OcBDirectReportOrderImportVo> errorDateList,
                                 Map<String, List<OcBDirectReportOrderImportVo>> groupByMainTable,
                                 Set<String> cpCShopCodes,
                                 Set<String> cpCDisOrgLv2Codes,
                                 Set<String> sgCSaStoreCodes,
                                 Set<String> cpCStoreCodes,
                                 List<String> skuCodes,
                                 StringBuilder errorMsg,
                                 Map<OcBDirectReportOrder, List<OcBDirectReportOrderItem>> importDtoMap, User user) {
        Map<String, CpShop> shopMap = cpRpcService.queryMapByCodes(new ArrayList<>(cpCShopCodes));
        Map<String, CpCDistributionOrganization> orgMap = cpRpcService.queryOrgMapByCodes(new ArrayList<>(cpCDisOrgLv2Codes));
        Map<String, SgCSaStore> saStoreMap = sgNewRpcService.querySaStoreMapByCodes(new ArrayList<>(sgCSaStoreCodes));
        Map<String, CpCStore> cpCStoreMap = cpRpcService.queryCpCStoreMapByCodes(new ArrayList<>(cpCStoreCodes));

        List<PsCProSkuResult> proList = psRpcService.selectProSkuByEcodesWithOutActive(skuCodes);
        Map<String, PsCProSkuResult> proMap = ListUtils.emptyIfNull(proList).stream()
                .collect(Collectors.toMap(PsCProSkuResult::getSkuEcode, x -> x, (a, b) -> a));

        List<PsCSku> skuList = psRpcService.selectSkuListbyEcode(skuCodes);
        Map<String, PsCSku> skuMap = ListUtils.emptyIfNull(skuList).stream()
                .collect(Collectors.toMap(PsCSku::getEcode, x -> x, (a, b) -> a));

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

        //按照主表分组处理
        for (List<OcBDirectReportOrderImportVo> importVos : groupByMainTable.values()) {
            OcBDirectReportOrder main = new OcBDirectReportOrder();
            main.setId(ModelUtil.getSequence("OC_B_DIRECT_REPORT_ORDER"));

            List<OcBDirectReportOrderItem> itemList = new ArrayList<>();
            for (int i = 0; i < importVos.size(); i++) {
                OcBDirectReportOrderImportVo importVo = importVos.get(i);
                //构建主表
                if (i == 0) {
                    CpShop shop = shopMap.get(importVo.getCpCShopCode());
                    if (Objects.isNull(shop)) {
                        errorMsg.append("平台店铺不存在或被冻结！");
                    } else {
                        main.setCpCShopId(shop.getId());
                    }

                    CpCDistributionOrganization disrtibutionOrg = orgMap.get(importVo.getCpCDisOrgLv2Code());
                    if (Objects.isNull(disrtibutionOrg)) {
                        errorMsg.append("分货部门不存在或被冻结！");
                    } else {
                        main.setCpCDisOrgLv2Id(disrtibutionOrg.getId());
                        main.setCpCDisOrgLv2Code(disrtibutionOrg.getEcode());
                        main.setCpCDisOrgLv2Name(disrtibutionOrg.getEname());
                    }


                    SgCSaStore saStore = saStoreMap.get(importVo.getSgCSaStoreCode());
                    if (Objects.isNull(saStore)) {
                        errorMsg.append("配销仓不存在或被冻结！");
                    } else {
                        main.setSgCSaStoreId(saStore.getId());
                    }

                    CpCStore cpCStore = cpCStoreMap.get(importVo.getCpCStoreCode());
                    if (Objects.isNull(cpCStore)) {
                        errorMsg.append("逻辑仓不存在或被冻结！");
                    } else {
                        main.setCpCStoreId(cpCStore.getId());
                    }

                    //处理单据日期
                    try {
                        main.setEstimateConTime(dateFormat.parse(importVo.getEstimateConTimeStr()));
                    } catch (ParseException e) {
                        errorMsg.append("预计发货时间数据格式错误！");
                    }
                    try {
                        main.setAutoReleaseTime(dateFormat.parse(importVo.getAutoReleaseTimeStr()));
                    } catch (ParseException e) {
                        errorMsg.append("库存释放时间数据格式错误！");
                    }
                    if (Objects.nonNull(main.getEstimateConTime())
                            && main.getEstimateConTime().before(new Date())) {
                        errorMsg.append("库存释放时间不能早于当前时间");
                    }
                    if (Objects.nonNull(main.getAutoReleaseTime())
                            && main.getAutoReleaseTime().before(new Date())) {
                        errorMsg.append("预计发货时间不能早于当前时间");
                    }

                    if (errorMsg.length() > 0) {
                        importVos.forEach(vo -> {
                            vo.setErrorDesc(errorMsg.toString());
                            errorDateList.add(vo);
                        });
                        errorMsg.setLength(0);
                        main = null;
                        break;
                    }
                }

                OcBDirectReportOrderItem item = new OcBDirectReportOrderItem();
                item.setQty(new BigDecimal(importVo.getQty()));
                if (BigDecimal.ZERO.compareTo(item.getQty()) >= 0) {
                    errorMsg.append("明细行数量必须大于0");
                }

                PsCSku sku = skuMap.get(importVo.getPsCSkuEcode());
                PsCProSkuResult pro = proMap.get(importVo.getPsCSkuEcode());

                //TODO 202506171533:上线前删掉
                log.info(LogUtil.format("商品款号信息:{}",
                        "OcBDirectReportOrderImportService.quoteTableCheck"), JSONObject.toJSONString(pro));

                if (Objects.isNull(sku) || Objects.isNull(pro)) {
                    errorMsg.append("商品编码不存在或被冻结！");
                } else if (YesNoEnum.Y.getKey().equals(pro.getIsGroup())) {
                    errorMsg.append("不可以选择组合商品！");
                } else {
                    item.setPsCSkuId(sku.getId());
                    item.setPsCSkuEcode(sku.getEcode());
                    item.setPsCProEname(sku.getPsCProEname());
                    item.setWeight(sku.getWeight());
                    item.setTotWeight(item.getQty()
                            .multiply(Optional.ofNullable(item.getWeight()).orElse(BigDecimal.ZERO)));
                }

                try {
                    item.setStartProduceDate(dateFormat.parse(importVo.getStartProduceDateStr()));
                } catch (ParseException e) {
                    errorMsg.append("开始生产时间数据格式错误！");
                }
                try {
                    item.setEndProduceDate(dateFormat.parse(importVo.getEndProduceDateStr()));
                } catch (ParseException e) {
                    errorMsg.append("结束生产时间数据格式错误！");
                }
                if (errorMsg.length() == 0) {
                    if (item.getStartProduceDate().after(item.getEndProduceDate())) {
                        errorMsg.append("开始日期必须在结束日期之前");
                    }
                }

                if (errorMsg.length() > 0) {
                    importVo.setErrorDesc(errorMsg.toString());
                    errorDateList.add(importVo);
                    errorMsg.setLength(0);
                } else {
                    item.setId(ModelUtil.getSequence("OC_B_DIRECT_REPORT_ORDER_ITEM"));
                    item.setOcBDirectReportOrderId(main.getId());

                    OmsModelUtil.setDefault4Add(item, user);
                    itemList.add(item);
                }
            }
            if (main != null && itemList.size() == importVos.size()) {
                main.setBillNo(SequenceGenUtil.generateSquence("OC_B_DIRECT_REPORT_ORDER",
                        new JSONObject(), user.getLocale(), false));
                main.setBillDate(new Date());
                main.setStatus(OcBDirectReportOrderStatusEnum.UN_AUDITED.getValue());
                main.setTotalQty(itemList.stream()
                        .map(OcBDirectReportOrderItem::getQty)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                main.setTotWeight(itemList.stream()
                        .map(OcBDirectReportOrderItem::getTotWeight)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));

                OmsModelUtil.setDefault4Add(main, user);
                importDtoMap.put(main, itemList);
            }
        }
    }


}
