package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderSendLogMapper;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.retail.OcBOrderSendLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @Author: 黄世新
 * @Date: 2019/12/4 1:11 下午
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsOrderGoBackService {

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderSendLogMapper ocBOrderSendLogMapper;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OmsOrderGoBackService omsOrderGoBackService;
//    @Autowired
//    private R3MqSendHelper sendHelper;
    @Autowired
    private DefaultProducerSend defaultProducerSend;


    public ValueHolderV14 omsOrderGoBack(List<Long> ids, User user) {
        int failedNumber = 0;
        int successNumber = 0;
        boolean hasError = false;
        ValueHolderV14 holder = new ValueHolderV14();
        try {
            if (CollectionUtils.isEmpty(ids)) {
                holder.setCode(-1);
                holder.setMessage("参数不能为空!");
                return holder;
            }
            for (Long id : ids) {
                holder = this.lockOrder(id, user);
                if (holder.getCode() == 0) {
                    successNumber++;
                } else {
                    failedNumber++;
                    hasError = true;
                }
            }
            if (hasError) {
                String message = holder.getMessage();
                holder.setCode(-1);
                holder.setMessage(String.format("退回成功%s条；退回失败%s条\r\n", successNumber, failedNumber)
                        + "失败原因:" + message);
            } else {
                holder.setCode(0);
                holder.setMessage("全部退回成功!");
            }
        } catch (Exception e) {
            holder.setCode(-1);
            holder.setMessage("程序异常!" + e.getMessage());
        }
        return holder;
    }


    private ValueHolderV14 lockOrder(Long id, User user) {
        ValueHolderV14 holder = new ValueHolderV14();
        //调用反审核
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder order = ocBOrderMapper.selectById(id);
                ValueHolderV14 valueHolderV14 = this.checkOrder(order);
                if (valueHolderV14 != null) {
                    return valueHolderV14;
                }
                this.toExamineOrder(order, user);
                //作废出库通知单成功,清空逻辑发货单成功 记录派单日志
                omsOrderGoBackService.addDistributeLeafletsLog(order, user, true);
                //成功后发送占单MQ
                this.sendMQ(order);
                holder.setCode(0);
                holder.setMessage("操作成功!");
            } else {
                holder.setCode(-1);
                holder.setMessage("当前订单其他人正在操作,请稍后再试!");
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用退回失败", e);
            holder.setCode(-1);
            holder.setMessage("退回失败!");
        } finally {
            redisLock.unlock();
        }
        return holder;
    }


    /**
     * 通知占单程序重新占单
     *
     * @param ocBOrder
     */
    private void sendMQ(OcBOrder ocBOrder) {
        if (ocBOrder != null) {
            long saveOrderId = ocBOrder.getId();
            String billNo = ocBOrder.getBillNo();
            String msgKey = "POS_TR_" + saveOrderId + "_" + billNo;
            OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
            orderMqInfo.setChannelType(ChannelType.TAOBAO);
            orderMqInfo.setOperateType(OperateType.TOBE_CONFIRMED);
            orderMqInfo.setOrderId(saveOrderId);
            orderMqInfo.setOrderNo(billNo);
            List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
            mqInfoList.add(orderMqInfo);
            String jsonValue = JSONObject.toJSONString(mqInfoList);
            if (log.isDebugEnabled()) {
                String string = (String) jsonValue;
                log.debug(this.getClass().getName() + " message大小:" + string.getBytes().length);
            }
            String messageId = null;
            //获取Topic
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            String topic = config.getProperty("r3.oc.oms.tobeConfirmed.mq.topic", "BJ_DEV_R3_OC_OMS_CALL_TOBECONFIRMED");
            String topic = Mq5Constants.TOPIC_R3_OC_OMS_CALL_TOBECONFIRMED;
//            String tag = config.getProperty("r3.oc.oms.tobeConfirmed.mq.tag", "OperateTobeConfirmed");
            String tag = Mq5Constants.TAG_R3_OC_OMS_CALL_TOBECONFIRMED;
            String uuid = UUID.randomUUID().toString();
            try {
//                messageId = sendHelper.sendMessage(jsonValue, topic, tag, uuid);
                MqSendResult result = defaultProducerSend.sendTopic(topic, tag, jsonValue, uuid);
                messageId = result.getMessageId();
            } catch (Exception e) {
                log.error(this.getClass().getName() + " 商品退回发送MQ失败=" + messageId + "MQKey=" + uuid, e);
                e.printStackTrace();
            }
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + "  商品退回发送MQ成功:MQId:{},MQKey:{}", messageId, uuid);
            }
        }
    }

    /**
     * 校验订单
     *
     * @param order
     * @return
     */
    private ValueHolderV14 checkOrder(OcBOrder order) {
        ValueHolderV14 holder = new ValueHolderV14();
        if (order == null) {
            holder.setCode(-1);
            holder.setMessage("订单信息不存在!");
            return holder;
        }
        if (OmsOrderStatus.PENDING_WMS.toInteger().equals(order.getOrderStatus())) {
            holder.setCode(-1);
            holder.setMessage("当前订单状态不允许退回!");
            return holder;
        }
        return null;
    }

    /**
     * 加入派单日志 并将订单改为待分配
     */
    @Transactional
    public void addDistributeLeafletsLog(OcBOrder order, User user, boolean flag) {
        int count = ocBOrderSendLogMapper.selectOcBOrderSendLogListCount(order.getId(), order.getCpCPhyWarehouseId());
        if (count == 0) {
            OcBOrderSendLog ocBOrderSendLog = new OcBOrderSendLog();
            ocBOrderSendLog.setId(sequenceUtil.buildOrderSendLogId());
            ocBOrderSendLog.setCpCPhyWarehouseId(order.getCpCPhyWarehouseId());
            ocBOrderSendLog.setOcBOrderId(order.getId());
            this.saveOperator(ocBOrderSendLog, user);
            ocBOrderSendLogMapper.insert(ocBOrderSendLog);
        }
        //修改订单信息
        if (flag) {
            ocBOrderMapper.updateOrderEmptyWarehouse(order.getId());
        }
    }


    public void saveOperator(BaseModel model, User operateUser) {
        if (operateUser != null) {
            model.setAdOrgId((long) operateUser.getOrgId());
            model.setOwnername(operateUser.getEname());
            model.setAdClientId((long) operateUser.getClientId());
            model.setOwnerid(Long.valueOf(operateUser.getId()));
            model.setCreationdate(new Date());
            model.setModifierid(Long.valueOf(operateUser.getId()));
            model.setModifieddate(new Date());
            model.setModifiername(operateUser.getName());
        } else {
            User rootUser = SystemUserResource.getRootUser();
            model.setAdOrgId((long) rootUser.getOrgId());
            model.setOwnername(rootUser.getEname());
            model.setAdClientId((long) rootUser.getClientId());
            model.setOwnerid(Long.valueOf(rootUser.getId()));
            model.setCreationdate(new Date());
            model.setModifierid(Long.valueOf(rootUser.getId()));
            model.setModifieddate(new Date());
            model.setModifiername(rootUser.getName());
        }
    }


    /**
     * 调用反审核并清空逻辑发货单
     *
     * @param order
     * @param user
     */
    private void toExamineOrder(OcBOrder order, User user) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, order.getId(), LogTypeEnum.NOT_CAPTURED_SCENE.getType());
        if (isSuccess) {
            //反审核成功 清空逻辑发货单
            boolean isEmpty = emptyLogic(order, user);
            if (!isEmpty) {
                throw new NDSException("清空逻辑发货单失败!");
            }
        } else {
            throw new NDSException("调用反审核失败!");
        }
    }

    /**
     * 清空逻辑发货单
     *
     * @param ocBOrder
     * @param user
     * @return
     */
    public boolean emptyLogic(OcBOrder ocBOrder, User user) {
        ValueHolderV14 sgValueHolder = sgRpcService.cleanSgLogicalShipment(ocBOrder, user);
        if (sgValueHolder.getCode() == -1) {
            return false;
        } else {
            return true;
        }
    }
}
