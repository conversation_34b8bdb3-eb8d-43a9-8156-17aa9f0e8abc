package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefudStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date ：Created in 20:19 2020/6/9
 * description ：通用平台发货前退款
 * @ Modified By：
 */
@Service
@Slf4j
public class BeforeShipmentReturnForStandplatService {
    @Autowired
    private IpStandplatRefundService standplatRefundService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private MarkRefundService markRefundService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    public void beforeShipmentReturn(OcBOrder ocBOrder, IpBStandplatRefund standplatRefund,
                                     User operateUser, List<OcBOrderItem> ocBOrderItems, IpBStandplatRefundItem standplatRefundItem) {
        Integer orderStatus = ocBOrder.getOrderStatus();
        //不是仓库发货、平台发货、交易完成、待分配、传WMS
        //2019-08-02 去掉校验待分配及待传wms
        if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                && !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                && !OmsOrderStatus.DEAL_DONE.toInteger().equals(orderStatus)) {
            //淘宝退单中间表的退单状态
            Integer status = standplatRefund.getReturnStatus();
            String oid = standplatRefund.getSubOrderId();
            String skuId = standplatRefundItem.getSkuId() == null ? "" : standplatRefundItem.getSkuId();
            OcBOrderItem ocBOrderItem = null;
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                if (StringUtils.isNotEmpty(oid)) {
                    ocBOrderItem = new OcBOrderItem();
                    for (OcBOrderItem bOrderItem : ocBOrderItems) {
                        String ooid = bOrderItem.getOoid();
                        Long ocBOrderId = bOrderItem.getOcBOrderId();
                        if (oid.equals(ooid) && ocBOrder.getId().equals(ocBOrderId)) {
                            ocBOrderItem = bOrderItem;
                            break;
                        }
                    }
                } else {
                    ocBOrderItem = new OcBOrderItem();
                    for (OcBOrderItem bOrderItem : ocBOrderItems) {
                        String skuNumiid = bOrderItem.getSkuNumiid();
                        Long ocBOrderId = bOrderItem.getOcBOrderId();
                        if (skuId.equals(skuNumiid) && ocBOrder.getId().equals(ocBOrderId)) {
                            ocBOrderItem = bOrderItem;
                            break;
                        }
                    }
                }
            }

            //更新主表状态(不管什么状态:只要是发货前立马拦截) 2019-08-04修改
            //是否退款中 0:N 1:Y
            OcBOrder order2 = new OcBOrder();
            //是否退款中
            order2.setIsInreturning(1);
            order2.setId(ocBOrder.getId());
            returnOrderTransferUtil.updateOperator(order2, operateUser);
            omsOrderService.updateOrderInfo(order2);

            //是否已经拦截
            order2.setIsInterecept(1); // 修改hold单状态 使用HOLD单方法修改
            ocBOrderHoldService.holdOrUnHoldOrder(order2, OrderHoldReasonEnum.REFUND_HOLD);
            boolean flag = false;
            if (IpBStandplatRefudStatusEnum.WAIT_SELLER_AGREE.getVal().equals(status)) {
                //买家已经申请退款，等待卖家同意
                flag = this.standplatRefundStatusAgree(ocBOrder, ocBOrderItem, standplatRefund, operateUser);
            }
            if (IpBStandplatRefudStatusEnum.SUCCESS.getVal().equals(status)) {
                //退款同意
                flag = this.refundStatusIsSuccess(ocBOrder, ocBOrderItem, standplatRefund, operateUser);
            }
            if (IpBStandplatRefudStatusEnum.CLOSED.getVal().equals(status)) {
                //退款关闭
                flag = this.orderStatusIsClosed(ocBOrder, ocBOrderItem, standplatRefund, operateUser);
            }
            if (!flag) {
                String remark = SysNotesConstant.SYS_REMARK25;
                standplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, standplatRefund);
            }
        }
    }


    /**
     * 为了适应组合商品 修改原逻辑
     *
     * @param ocBOrder        订单
     * @param ocBOrderItem    订单明细
     * @param standplatRefund 通用退单
     * @param operateUser     操作人
     * @return 结果
     */


    private boolean standplatRefundStatusAgree(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem,
                                               IpBStandplatRefund standplatRefund, User operateUser) {
        try {
            Integer orderStatus = ocBOrder.getOrderStatus();
            //通过订单id及ooid  获取当前订单下的所有的商品明细
            //liqb 更改ooid类型从Long类型改成String类型
            List<OcBOrderItem> orderItems = new ArrayList<>();
            orderItems.add(ocBOrderItem);

            for (OcBOrderItem orderItem : orderItems) {
                orderItem.setRefundStatus(OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal());
                omsOrderItemService.updateOcBOrderItem(orderItem, ocBOrder.getId());
                //插入订单日志
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "条码:" + orderItem.getPsCSkuEcode() + ",买家申请退款，订单拦截", null, null, operateUser);
            }
            if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)) {
                boolean flag = this.toExamineOrder(ocBOrder, operateUser);
                if (flag) {
                    orderStatus = ocBOrder.getOrderStatus();
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核成功", null, null, operateUser);
                } else {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核失败", null, null, operateUser);
                    standplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                            SysNotesConstant.SYS_REMARK46, standplatRefund);
                    return true;
                }
            }

            if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus)) {
                standplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        "订单状态为待分配或者传WMS中,等待下次转换", standplatRefund);
                return true;
            }

            //全渠道订单主表“订单状态”为配货中且“WMS撤回状态”为未撤回或者撤回失败，则自动调用WMS撤回服务
            // 撤回成功，则调用【订单传AG取消发货服务】，撤回失败，则不调用
            if (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
                Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger() == wmsCancelStatus
                        || OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger() == wmsCancelStatus) {
                    // 则自动调用WMS撤回服务// 撤回成功，则调用【订单传AG取消发货服务】
                    this.lockUpBackExamine(ocBOrder, operateUser);
                }
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == wmsCancelStatus) {
                    // 撤回成功，则调用【订单传AG取消发货服务】
                    this.lockUpBackExamine(ocBOrder, operateUser);
                }
                return true;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("发货前退款退单状态为买家已经申请退款，等待卖家同意装换失败.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
        return false;
    }


    /**
     * 标记退款完成失败或者成功的逻辑
     *
     * @param ocBOrder    主订单
     * @param operateUser 操作人
     */
    public void markRefundIsFail(OcBOrder ocBOrder, User operateUser,
                                 List<OcBOrderItem> items) {
        this.lockUpBackExamine(ocBOrder, operateUser);
        for (OcBOrderItem item : items) {
            Integer refundStatus = item.getRefundStatus();
            if (refundStatus != OcOrderRefundStatusEnum.SUCCESS.getVal()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("IDS", item.getId());
                //标记退款完成
                ValueHolderV14 holderV14 = markRefundService.markRefund(jsonObject, operateUser);
                int code1 = Tools.getInt(holderV14.getCode(), -1);
                if (code1 != 0) {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单退款明细id为:" + item.getId() + "条码为:" + item.getPsCSkuEcode() + "标记退款完成失败", null, null, operateUser);
                    break;
                }
            }
        }
    }

    /**
     * 配货中加锁调用反审核
     *
     * @param ocBOrder
     * @param operateUser
     */
    private void lockUpBackExamine(OcBOrder ocBOrder, User operateUser) {
        //  对订单加锁 调用反审核
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                boolean isSuccess = this.toExamineOrder(ocBOrder, operateUser);
                if (!isSuccess) {
                    throw new NDSException("配货中反审核操作失败!");
                }
            } else {
                throw new NDSException("当前订单正在反审核操作!");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("配货中调用反审核出错.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("调用反审核失败!");
        } finally {
            redisLock.unlock();
        }
    }


    /**
     * 处理订单的方法
     *
     * @param ocBOrder
     * @param ocBOrderItems
     * @param standplatRefund
     * @param operateUser
     */
    private void handleOrder(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems,
                             IpBStandplatRefund standplatRefund, User operateUser) {
        boolean isReturn = false;
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            Integer refundStatus = ocBOrderItem.getRefundStatus();
            if (OcOrderRefundStatusEnum.SUCCESS.getVal() != refundStatus) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("IDS", ocBOrderItem.getId());
                ValueHolderV14 execute = markRefundService.markRefund(jsonObject, operateUser);
                int code = Tools.getInt(execute.getCode(), -1);
                if (code == 0) {
                    isReturn = true;
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), ocBOrderItem.getPsCSkuEcode() + "条码退款完成, 明细id:" + ocBOrderItem.getId(), null, null, operateUser);
                } else {
                    isReturn = false;
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单退款明细id" + ocBOrderItem.getId() + "标记退款完成失败", null, null, operateUser);
                    break;
                }
            }
        }
        if (isReturn) {
            this.judgeOrder(ocBOrder, standplatRefund, operateUser);
        }
    }

    /**
     * 对订单明细做相应的判断
     *
     * @param ocBOrder        订单
     * @param standplatRefund 通用退单
     * @param operateUser     操作人
     */
    private void judgeOrder(OcBOrder ocBOrder, IpBStandplatRefund standplatRefund, User operateUser) {
        //todo 后期考虑赠品问题
        List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemList(ocBOrder.getId());

        //判断当前订单明细是否都为退款成功
        boolean flag = true;
        for (OcBOrderItem orderItem : orderItems) {
            if (OcOrderRefundStatusEnum.SUCCESS.getVal() != orderItem.getRefundStatus()) {
                flag = false;
                break;
            }
        }
        if (flag) {
            //当前退款明细状态都为success 调用订单取消服务(注释掉,标记退款完成已做判断)
            standplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    SysNotesConstant.SYS_REMARK19, standplatRefund);
        } else {
            boolean isFlag = false;
            for (OcBOrderItem orderItem : orderItems) {
                if (OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal() == orderItem.getRefundStatus()) {
                    isFlag = true;
                    break;
                }
            }
            if (isFlag) {
                standplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        SysNotesConstant.SYS_REMARK20, standplatRefund);
            } else {
                OcBOrder order = new OcBOrder();
                order.setId(ocBOrder.getId());
                this.updateOrder(order, operateUser);
                standplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        SysNotesConstant.SYS_REMARK20, standplatRefund);
            }
        }
    }

    /**
     * 更新主编是否已经
     *
     * @param ocBOrder 订单
     */

    private void updateOrder(OcBOrder ocBOrder, User operateUser) {
        ocBOrder.setIsInreturning(0);
        returnOrderTransferUtil.updateOperator(ocBOrder, operateUser);
        omsOrderService.updateOrderInfo(ocBOrder);
        // 修改hold单状态 使用HOLD单方法修改
        ocBOrder.setIsInterecept(0);
        ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder, OrderHoldReasonEnum.REFUND_HOLD);
    }


    /**
     * 退款同意（SUCCESS(退款成功)）
     *
     * @param ocBOrder        订单
     * @param ocBOrderItem    订单明细
     * @param standplatRefund 通用退单
     */
    private boolean refundStatusIsSuccess(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem,
                                          IpBStandplatRefund standplatRefund, User operateUser) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + "进入发货前退款退单状态为退款成功,订单id:{}", ocBOrder.getId());
            }
            //通过订单id及ooid  获取当前订单下的所有的商品明细
            //liqb 更改ooid类型从Long类型改成String类型
            List<OcBOrderItem> orderItems = new ArrayList<>();
            orderItems.add(ocBOrderItem);
            boolean isReturn = true;
            for (OcBOrderItem orderItem : orderItems) {
                Integer refundStatus = orderItem.getRefundStatus();
                if (OcOrderRefundStatusEnum.SUCCESS.getVal() != refundStatus) {
                    isReturn = false;
                }
            }
            //所有ooid相同的明细   全部退款完成
            if (isReturn) {
                //判断订单明细
                this.judgeOrder(ocBOrder, standplatRefund, operateUser);
                standplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        SysNotesConstant.SYS_REMARK30, standplatRefund);
                return true;
            }

            //订单状态
            Integer orderStatus = ocBOrder.getOrderStatus();
            Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
            if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)) {
                boolean flag = this.toExamineOrder(ocBOrder, operateUser);
                if (flag) {
                    orderStatus = ocBOrder.getOrderStatus();
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核成功！", null, null, operateUser);
                } else {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核失败！", null, null, operateUser);
                    standplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                            SysNotesConstant.SYS_REMARK46, standplatRefund);
                    return true;
                }
            }
            if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus)) {
                String remark1 = "订单状态为待分配或者传WMS中,等待下次转换";
                standplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        remark1, standplatRefund);
                return true;
            }
            if (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger() == wmsCancelStatus
                        || OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger() == wmsCancelStatus) {
                    //todo 调用WMS撤回服务
                    //若服务返回失败，更新【淘宝退单中间表】数据：“转换状态”=0，”系统备注”：订单已转WMS且不可撤回，不允许转换
                    //配货中调用反审核
                    this.lockUpBackExamine(ocBOrder, operateUser);
                    this.handleOrder(ocBOrder, orderItems, standplatRefund, operateUser);
                }
                //状态为配货中 wms状态为已撤回状态
                if (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == wmsCancelStatus) {
                    //配货中调用反审核
                    this.lockUpBackExamine(ocBOrder, operateUser);
                    this.handleOrder(ocBOrder, orderItems, standplatRefund, operateUser);
                }
                return true;
            }

            //b)若“订单状态”为待审核、缺货处理订单
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                this.handleOrder(ocBOrder, orderItems, standplatRefund, operateUser);
                return true;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("发货前退款退单状态为SUCCESS时转换失败.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
        return false;
    }


    /**
     * 退单关闭
     *
     * @param ocBOrder        订单
     * @param ocBOrderItem    订单明细
     * @param standplatRefund 通用退单
     */
    public boolean orderStatusIsClosed(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem,
                                       IpBStandplatRefund standplatRefund, User operateUser) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + " 进入发货前退款退单状态为退单关闭,订单id:{}", ocBOrder.getId());

            }
            //将当前订单明细改为0
            //通过订单id及ooid  获取当前订单下的所有的商品明细
            //liqb 更改ooid类型从Long类型改成String类型
            List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
            ocBOrderItems.add(ocBOrderItem);
            //订单主表id
            Long id = ocBOrder.getId();
            for (OcBOrderItem item : ocBOrderItems) {
                OcBOrderItem orderItem = new OcBOrderItem();
                orderItem.setId(item.getId());
                orderItem.setOcBOrderId(item.getOcBOrderId());
                orderItem.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
                omsOrderItemService.updateOcBOrderItem(orderItem, id);
            }
            //获取所有明细(排除赠品)
            List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemListNotGift(ocBOrder.getId());
            boolean flag = false;
            for (OcBOrderItem item : orderItems) {
                //退款状态
                Integer refundStatus = item.getRefundStatus();
                if (OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal() == refundStatus) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                standplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        SysNotesConstant.SYS_REMARK18, standplatRefund);
            } else {
                //否，则更新全渠道订单“是否已经拦截”=0，则更新【淘宝退单中间表】数据：“转换状态”=2，
                // ”系统备注”：退款关闭，
                // 转换成功，调用订单日志服务
                OcBOrder bOrder = new OcBOrder();
                bOrder.setId(ocBOrder.getId());
                this.updateOrder(bOrder, operateUser);
                standplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        SysNotesConstant.SYS_REMARK18, standplatRefund);
            }
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "订单退款关闭，取消拦截", null, null, operateUser);
            return true;
        } catch (Exception e) {
            log.error(LogUtil.format("发货前退款退单状态为退款关闭转换失败.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }


    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    private boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.BEFORE_SHIPMENT_REFUND_REVERSE_AUDIT.getType());
            if (isSuccess) {
                //反审核埋点
                ocBOrder.setExamineOrderDate(new Date());
                ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.EXAMINE_ORDER_DATE,new Date(),ocBOrder.getId(),user);
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(LogUtil.format("调用反审核失败.异常: {}"), Throwables.getStackTraceAsString(e));
            return false;
        }

    }
}
