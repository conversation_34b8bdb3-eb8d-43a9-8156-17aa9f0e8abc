package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.st.model.StCImperfectStrategyRelation;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 残次策略service
 *
 * <AUTHOR>
 */
public interface StCImperfectStrategyService {

    /**
     * 新增
     *
     * @param strategyRelation
     * @param user
     * @return
     * @throws NDSException
     */
    ValueHolderV14<Long> save(StCImperfectStrategyRelation strategyRelation, User user) throws NDSException;

    /**
     * 更新
     *
     * @param strategyRelation
     * @param before
     * @param after
     * @param user
     * @param id
     * @return
     * @throws NDSException
     */
    ValueHolderV14<Void> update(StCImperfectStrategyRelation strategyRelation, JSONObject before, JSONObject after, User user, Long id) throws NDSException;
}
