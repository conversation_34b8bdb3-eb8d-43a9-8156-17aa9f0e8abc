package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.burgeon.r3.sg.basic.model.request.SgPhyStorageQueryRequest;
import com.google.common.base.Throwables;
import com.burgeon.r3.sg.basic.model.request.SgPhyStorageQueryRequest;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.ascp.ConsignOrderOutOfStockCallbackModel;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/17 下午9:00
 * @description 猫超缺货回传service
 **/

@Slf4j
@Component
public class OcBorderShortageNoticeService {

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private CpRpcService cpRpcService;

    public ValueHolderV14 shortageNotice(List<Long> orderIdList, User user) {

        ValueHolderV14 valueHolder = new ValueHolderV14();
        try {
            int successNum = 0, errorNum = 0;
            //根据ids查询所有零售发货单
            for (Long id : orderIdList) {
                OcBOrderRelation ocBOrderRelation = omsOrderService.selectOmsOrderInfoOccupy(id);
                OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();

                if (!PlatFormEnum.ALIBABAASCP.getCode().equals(ocBOrder.getPlatform())) {
                    return new ValueHolderV14(null, ResultCode.SUCCESS, "只允许天猫超市类型订单缺货回传");
                }

                if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())) {
                    return new ValueHolderV14(null, ResultCode.SUCCESS, "只允许缺货状态天猫超市订单缺货回传");
                }

                SgPhyStorageQueryRequest request = new SgPhyStorageQueryRequest();

                List<Long> phyWarehouseIds = new ArrayList<>();
                phyWarehouseIds.add(ocBOrder.getCpCPhyWarehouseId());
                request.setPhyWarehouseIds(phyWarehouseIds);

                //根据平台单号查询订单信息
                List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();

                List<String> skuEcodes = new ArrayList<>();
                orderItemList.stream().forEach(item ->
                        skuEcodes.add(item.getPsCSkuEcode())
                );
                request.setSkuEcodes(skuEcodes);

                //缺货回传实体类
                ConsignOrderOutOfStockCallbackModel.Outofstockitems outofstockitems = new ConsignOrderOutOfStockCallbackModel.Outofstockitems();
                List<ConsignOrderOutOfStockCallbackModel.Outofstockitems> outofstockitemsList = new ArrayList<>();

                orderItemList.stream().forEach(item -> {
                    outofstockitems.setLackQuantity(item.getQtyLost().longValue());
                    outofstockitems.setScItemId(item.getSkuNumiid());
                    outofstockitems.setSubOrderCode(item.getOoid());
                    outofstockitemsList.add(outofstockitems);
                });

                //根据店铺id,查询店铺信息
                CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
                String supplierId = cpShop.getPlatformSupplierId();

                ConsignOrderOutOfStockCallbackModel model = new ConsignOrderOutOfStockCallbackModel();
                model.setOutOfStockItems(outofstockitemsList);
                model.setBizOrderCode(ocBOrder.getSourceCode());
                model.setOutOfStockReason("库存不足");
                model.setOutBizId(ocBOrder.getBillNo());
                model.setSupplierId(supplierId);

                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("缺货回传调用云枢纽参数model={},sellerNick=", ocBOrder.getCpCShopSellerNick()), JSON.toJSONString(model));
                }
                ValueHolderV14 valueHolderV14 = ipRpcService.alibabaAscpOutOfStockCallback(model, ocBOrder.getCpCShopSellerNick());

                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("缺货回传调用云枢纽结果={}"), JSON.toJSONString(valueHolderV14));
                }

                StringBuilder sb = new StringBuilder();
                if (valueHolderV14.isOK()) {
                    sb.append("缺货回传成功");
                    successNum++;
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.SHORTAGE_NOTICE.getKey(), "缺货回告成功", null, null, user);
                } else {
                    sb.append("缺货回传失败:").append("[").append(valueHolderV14.getMessage() != null ? valueHolderV14.getMessage() : "").append("]");
                    errorNum++;
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.SHORTAGE_NOTICE.getKey(), "缺货回告成功", null, null, user);
                }
            }
            valueHolder.setCode(ResultCode.SUCCESS);
            valueHolder.setMessage("缺货回传成功" + successNum + "条,失败" + errorNum + "条");
        } catch (Exception e) {
            log.error(LogUtil.format("缺货回传异常,error：{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("缺货回传异常");
        }
        return valueHolder;
    }
}