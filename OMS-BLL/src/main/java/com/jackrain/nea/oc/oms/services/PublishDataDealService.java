package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemExtMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.rpc.PsRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 发布处理数据统一入口
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PublishDataDealService {

    @Resource
    private OcBOrderItemMapper orderItemMapper;
    @Resource
    private PsRpcService psRpcService;
    @Resource
    private OcBOrderMapper ocBOrderMapper;
    @Resource
    private OcBOrderEqualExchangeItemMapper equalExchangeItemMapper;
    @Resource
    private OcBOrderItemExtService ocBOrderItemExtService;
    @Autowired
    private OcBOrderItemExtMapper ocBOrderItemExtMapper;

    @Autowired
    private ThreadPoolTaskExecutor lowTemperatureOccupyPollExecutor;
    @NacosValue(value = "${oms.order.item.product.refresh.stop:0}", autoRefreshed = true)
    private Integer stopRefresh;
    @Resource
    private CycleBuyInfoService cycleBuyInfoService;

    public void refreshProduceLevelByBillNos(List<Long> ids, int batchNum) {
        List<List<Long>> groupList = ListUtil.split(ids, batchNum);
        for (List<Long> orderIds : groupList) {
            deal(orderIds);
        }
    }

    public void refreshProduceLevelByEqualBillNos(List<Long> ids, int batchNum) {
        List<List<Long>> groupList = ListUtil.split(ids, batchNum);
        for (List<Long> orderIds : groupList) {
            dealEqualItem(orderIds);
        }
    }

    public void refreshProduceLevelByExtBillNos(List<Long> ids, int batchNum) {
        List<List<Long>> groupList = ListUtil.split(ids, batchNum);
        for (List<Long> orderIds : groupList) {
            dealExtItem(orderIds);
        }
    }

    private void dealExtItem(List<Long> orderIds) {
        List<OcBOrderItem> orderItems = orderItemMapper.selectAllStatusOrderItemsByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }
        List<OcBOrderItemExt> ocBOrderItemExtList = ocBOrderItemExtService.queryByOrderIdList(orderIds);
        if (CollectionUtils.isEmpty(ocBOrderItemExtList)) {
            return;
        }
        // 对orderItems进行分组 生成的对象是Map<Long, OcBOrderItem>
        Map<Long, OcBOrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OcBOrderItem::getId, x -> x, (a, b) -> a));
        // 对ocBOrderItemExtList进行分组 生成的对象是Map<Long, OcBOrderItemExt>
        Map<Long, OcBOrderItemExt> ocBOrderItemExtMap = ocBOrderItemExtList.stream().collect(Collectors.toMap(OcBOrderItemExt::getOrderItemId, x -> x, (a, b) -> a));
        List<OcBOrderItemExt> ocBOrderItemExtUpdateList = new ArrayList<>();
        for (Map.Entry<Long, OcBOrderItem> entry : orderItemMap.entrySet()) {
            Long orderItemId = entry.getKey();
            OcBOrderItem ocBOrderItem = orderItemMap.get(orderItemId);
            OcBOrderItemExt ocBOrderItemExt = ocBOrderItemExtMap.get(orderItemId);
            OcBOrderItemExt updateOcBOrderItemExt = new OcBOrderItemExt();
            updateOcBOrderItemExt.setId(ocBOrderItemExt.getId());
            updateOcBOrderItemExt.setMDim4Id(ocBOrderItem.getMDim4Id());
            updateOcBOrderItemExt.setMDim6Id(ocBOrderItem.getMDim6Id());
            updateOcBOrderItemExt.setModifieddate(new Date());
            ocBOrderItemExtMapper.updateById(updateOcBOrderItemExt);
//            ocBOrderItemExtUpdateList.add(updateOcBOrderItemExt);
        }

    }

    private void dealEqualItem(List<Long> orderIds) {
        List<OcBOrderEqualExchangeItem> exchangeItems = equalExchangeItemMapper.selectOcBOrderEqualExchangeItemList(orderIds);
        if (CollectionUtils.isEmpty(exchangeItems)) {
            return;
        }
        Map<String, List<OcBOrderEqualExchangeItem>> skuItemMap = exchangeItems.stream().collect(Collectors.groupingBy(OcBOrderEqualExchangeItem::getPsCSkuEcode));
        Set<String> skuCodes = skuItemMap.keySet();
        if (CollectionUtils.isEmpty(skuCodes)) {
            return;
        }
        List<PsCPro> pros = psRpcService.queryProByEcode(Lists.newArrayList(skuCodes));
        log.info("refreshProduceLevelByBillNos queryProByEcode skuCodes:{},pros:{}", skuCodes, JSON.toJSONString(pros));
        if (CollectionUtils.isEmpty(pros)) {
            return;
        }
        Map<String, PsCPro> skuCodeProMap = pros.stream().collect(Collectors.toMap(PsCPro::getEcode, x -> x, (a, b) -> a));
        for (Map.Entry<String, List<OcBOrderEqualExchangeItem>> entry : skuItemMap.entrySet()) {
            try {
                String skuCode = entry.getKey();
                List<Long> itemIds = entry.getValue().stream().map(OcBOrderEqualExchangeItem::getId).collect(Collectors.toList());

                PsCPro pro = skuCodeProMap.get(skuCode);
                if (pro == null) {
                    continue;
                }
                Integer mDim4Id = pro.getMDim4Id();
                Integer mDim6Id = pro.getMDim6Id();
                equalExchangeItemMapper.updateDimByIds(itemIds, mDim4Id, mDim6Id);
                log.info("refreshProduceLevelByBillNos success skuCode:{},itemIds2:{}", entry.getKey(), JSONUtil.toJsonStr(itemIds));
            } catch (Exception e) {
                log.error("refreshProduceLevelByBillNos error skuCode:{},itemIds2:{}", entry.getKey(), entry.getValue().stream().map(OcBOrderEqualExchangeItem::getId).collect(Collectors.toList()), e);
            }
        }
    }

    private void deal(List<Long> orderIds) {
        //no protype is 4
        List<OcBOrderItem> orderItems = orderItemMapper.selectAllStatusOrderItemsByOrderIds(orderIds);
        Map<String, List<OcBOrderItem>> skuItemMap = orderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuEcode));
        Set<String> skuCodes = skuItemMap.keySet();
        if (CollectionUtils.isEmpty(skuCodes)) {
            return;
        }
        List<PsCPro> pros = psRpcService.queryProByEcode(Lists.newArrayList(skuCodes));
        log.info("refreshProduceLevelByBillNos queryProByEcode skuCodes:{},pros:{}", skuCodes, JSON.toJSONString(pros));
        if (CollectionUtils.isEmpty(pros)) {
            return;
        }
        Map<String, PsCPro> skuCodeProMap = pros.stream().collect(Collectors.toMap(PsCPro::getEcode, x -> x, (a, b) -> a));

        for (Map.Entry<String, List<OcBOrderItem>> entry : skuItemMap.entrySet()) {
            try {
                String skuCode = entry.getKey();
                List<Long> itemIds = entry.getValue().stream().map(OcBOrderItem::getId).collect(Collectors.toList());

                PsCPro pro = skuCodeProMap.get(skuCode);
                if (pro == null) {
                    continue;
                }
                Integer mDim4Id = pro.getMDim4Id();
                Integer mDim6Id = pro.getMDim6Id();

                orderItemMapper.updateDimByIds(itemIds, mDim4Id, mDim6Id);
                log.info("refreshProduceLevelByBillNos success skuCode:{},itemIds:{}", entry.getKey(), itemIds);
            } catch (Exception e) {
                log.error("refreshProduceLevelByBillNos error skuCode:{},itemIds:{}", entry.getKey(), entry.getValue().stream().map(OcBOrderItem::getId).collect(Collectors.toList()), e);
            }
        }
    }

    public void refreshProduceLevelByStatus(Integer status, Integer batchNum) {
        final String taskTableName = "oc_b_order";
        List<Future<Boolean>> results = new ArrayList<>();

        List<Long> ids = ocBOrderMapper.ocBOrderSelectByStatusSql(taskTableName, status);
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<List<Long>> groupList = ListUtil.split(ids, batchNum);
        try {
            for (List<Long> data : groupList) {
                results.add(lowTemperatureOccupyPollExecutor.submit(new CallableRefreshhProduceLevelByStatus(data)));
            }
            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                if (!futureResult.get()) {
                    // 啥都不做
                }
            }
        } catch (Exception e) {

        }
    }


    class CallableRefreshhProduceLevelByStatus implements Callable<Boolean> {

        private final List<Long> data;

        public CallableRefreshhProduceLevelByStatus(List<Long> data) {
            this.data = data;
        }

        @Override
        public Boolean call() throws Exception {
            if (CollectionUtils.isEmpty(data)) {
                return true;
            }
            if (ObjectUtil.equal(stopRefresh, 1)) {
                return true;
            }
            try {
                deal(data);
            } catch (Exception e) {
                // 下面的日志 把订单id也打印出来
                log.error("refreshProduceLevelByStatus error orderIds:{}", JSONUtil.toJsonStr(data));
                log.error("refreshProduceLevelByStatus error", e);
            }
            // sleep 100毫秒
            try {
                Thread.sleep(100);
            } catch (Exception e) {
            }
            return true;
        }
    }

    public void cycleExtInfoInit(Integer batchNum) {
        cycleBuyInfoService.cycleExtInfoInit(batchNum);
    }

    public void cycleExtInfoInitSingle(List<String> tids) {
        cycleBuyInfoService.cycleExtInfoInitSingle(tids);
    }
}
