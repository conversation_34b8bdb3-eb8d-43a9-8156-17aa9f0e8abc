package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderAppointLogistics;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.st.api.ShopStrategyQueryServiceCmd;
import com.jackrain.nea.st.model.result.StCShopStrategyLogisticsItemResult;
import com.jackrain.nea.st.model.table.StCShopStrategyLogisticsItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/3/29 16:43
 * @Description 指定物流服务类
 */
@Slf4j
@Component
public class OcBOrderAppointLogisticsService {

    @Resource
    private BllRedisLockOrderUtil redisUtil;

    @Resource
    private OcBOrderMapper ocBOrderMapper;

    @Resource
    private OcBOrderItemMapper orderItemMapper;

    @Resource
    private CpRpcService cpRpcService;

    @Resource
    private OmsOrderLogService orderLogService;

    @Resource
    private OmsBusinessTypeStService omsBusinessTypeStService;

    @Resource
    private OmsOccupyTaskService omsOccupyTaskService;

    @Resource
    SgRpcService sgRpcService;

    @Resource
    private OcBOrderAppointLogisticsMapperService appointLogisticsMapperService;

    @DubboReference(group = "st", version = "1.0")
    private ShopStrategyQueryServiceCmd shopStrategyQueryCmd;

    /**
     * 校验订单信息并查询物流公司信息
     *
     * @param ids
     * @param page
     * @param size
     * @return
     */
    public ValueHolderV14<JSONObject> queryLogistics(List<Long> ids, Integer page, Integer size) {
        log.info(LogUtil.format("OcBOrderAppointLogisticsService.queryLogistics，ids:{}",
                "OcBOrderAppointLogisticsService.queryLogistics"), JSONObject.toJSONString(ids));
        ValueHolderV14<JSONObject> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        try {
            //校验单据状态和获取分页的物流信息
            IPage<CpLogistics> logisticsIPage = getCpLogisticsIPage(ids, page, size);
            //构建返回信息
            JSONObject response = buildResponse(logisticsIPage);
            v14.setData(response);
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    /**
     * 指定快递
     *
     * @param ids
     * @param logisticsId
     * @param user
     * @return
     */
    public ValueHolderV14<Void> appointLogistics(List<Long> ids, Long logisticsId, User user) {
        log.info(LogUtil.format("OcBOrderAppointLogisticsService.appointLogistics，ids:{},logisticsId:{}",
                "OcBOrderAppointLogisticsService.appointLogistics"), JSONObject.toJSONString(ids), logisticsId);
        ValueHolderV14<Void> v14 = new ValueHolderV14<>();
        try {
            //查询符合条件的订单
            List<OcBOrder> orderList = queryOrderInfo(ids);
            //查询店铺策略(前面已经校验只允许同一个店铺的订单指定快递)
            StCShopStrategyLogisticsItemResult shopLogistics = queryShopStrategy(orderList.get(0).getCpCShopId());
            //根据店铺策略查询物流公司信息
            IPage<CpLogistics> logisticsIPage = queryLogistics(1, 10000, shopLogistics);
            if (CollectionUtils.isEmpty(logisticsIPage.getRecords())) {
                throw new NDSException("可发物流公司为空！");
            }
            //验证物流公司档案
            CpLogistics cpLogistics = checkLogistics(logisticsId, logisticsIPage);
            //成功条数
            int successNum = 0;
            //订单逐个处理
            for (OcBOrder ocBOrder : orderList) {
                try {
                    OcBOrderAppointLogisticsService bean =
                            ApplicationContextHandle.getBean(OcBOrderAppointLogisticsService.class);
                    bean.orderAppointLogistics(ocBOrder.getId(), cpLogistics, user);
                    successNum++;
                } catch (Exception e) {
                    //记录失败日志
                    String message = e.getMessage();
                    if (e.getMessage() != null && message.length() > 1000) {
                        message = message.substring(0, 1000);
                    }
                    insertOrderLog(ocBOrder.getId(), null, OrderLogTypeEnum.APPOINT_LOGISTICS.getKey()
                            , message, null, null, user);
                }
            }
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("操作成功" + successNum + "条，失败" + (ids.size() - successNum) + "条！");
        } catch (Exception e) {
            log.error(LogUtil.format("OcBOrderAppointLogisticsService.appointLogistics，ids:{},error:{}",
                    "OcBOrderAppointLogisticsService.appointLogistics.error"),
                    JSONObject.toJSONString(ids), Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    /**
     * 指定快递
     *
     * @param orderId
     * @param cpLogistics
     * @param operateUser
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    public void orderAppointLogistics(Long orderId, CpLogistics cpLogistics, User operateUser) {
        // 给订单加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = this.ocBOrderMapper.selectByID(orderId);
                if (ocBOrder == null) {
                    throw new NDSException("订单不存在");
                }
                //验证业务类型
                if (!omsBusinessTypeStService.isAutoOccupy(ocBOrder)) {
                    throw new NDSException("该业务类型订单未开启自动寻源,不允许手工指定物流");
                }
                if (OmsOrderUtil.isToCCcOrder(ocBOrder)) {
                    throw new NDSException("toc残次订单不允许手工指定物流");
                }
                //更新指定快递信息
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setId(orderId);
                updateOrder.setAppointLogisticsId(cpLogistics.getId());
                updateOrder.setAppointLogisticsEcode(cpLogistics.getEcode());
                updateOrder.setAppointLogisticsEname(cpLogistics.getEname());
                ocBOrderMapper.updateById(updateOrder);

                // 手动指定快递完成之后 如果订单之前命中过自动指定快递策略 则将自动指定快递中间表
                List<OcBOrderAppointLogistics> appointLogisticsList = appointLogisticsMapperService.selectByOrderId(orderId);
                if (CollectionUtils.isNotEmpty(appointLogisticsList)) {
                    appointLogisticsMapperService.deleteByOrderId(orderId);
                }

                //卡单不释放
                if (!YesNoEnum.Y.getVal().equals(ocBOrder.getIsDetention())) {
                    //加入中间表
                    omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null, 0);
                }

                String logMessage = "手工指定快递成功！";
                //待审核状态需要回滚状态并释放占用
                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
                    if (ocBOrder.getCpCPhyWarehouseId() != null &&
                            StringUtils.isNotEmpty(ocBOrder.getCpCPhyWarehouseEname())) {
                        logMessage = logMessage + "指定前发货仓库" + ocBOrder.getCpCPhyWarehouseEname();
                    }
                    //记录日志
                    insertOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.APPOINT_LOGISTICS.getKey(),
                            logMessage, null, null, operateUser);
                    //修改订单状态为待分配，重新占单
                    voidSgOmsShareOut(operateUser, ocBOrder);
                } else if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())) {
                    //记录日志
                    insertOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.APPOINT_LOGISTICS.getKey(),
                            logMessage, null, null, operateUser);
                } else {
                    throw new NDSException("orderId " + orderId + "订单非待审核/缺货状态，不允许手工指定快递");
                }
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", operateUser.getLocale()));
            }
        } catch (Exception e) {
            log.info(LogUtil.format("OcBOrderAppointLogisticsService.orderAppointLogistics.error,orderId:{},error:{}",
                            "OcBOrderAppointLogisticsService.orderAppointLogistics.error"),
                    orderId, Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 取消指定快递
     *
     * @param ids
     * @param user
     * @return
     */
    public ValueHolderV14<Void> cancelAppointLogistics(List<Long> ids, User user, boolean isAuto) {
        log.info(LogUtil.format("OcBOrderAppointLogisticsService.cancelAppointLogistics，ids:{},user:{},isAuto;{}",
                "OcBOrderAppointLogisticsService.cancelAppointLogistics")
                , JSONObject.toJSONString(ids), JSONObject.toJSONString(user), isAuto);
        ValueHolderV14<Void> v14 = new ValueHolderV14<>();
        try {
            //成功条数
            int successNum = 0;
            //订单逐个处理
            for (Long id : ids) {
                try {
                    OcBOrderAppointLogisticsService bean =
                            ApplicationContextHandle.getBean(OcBOrderAppointLogisticsService.class);
                    bean.orderCancelAppointLogistics(id, user, isAuto);
                    successNum++;
                } catch (Exception e) {
                    //记录失败日志
                    String message = e.getMessage();
                    if (e.getMessage() != null && message.length() > 1000) {
                        message = message.substring(0, 1000);
                    }
                    insertOrderLog(id, null, OrderLogTypeEnum.CANCEL_APPOINT_LOGISTICS.getKey()
                            , message, null, null, user);
                }
            }
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("操作成功" + successNum + "条，失败" + (ids.size() - successNum) + "条！");
        } catch (Exception e) {
            log.error(LogUtil.format("OcBOrderAppointLogisticsService.cancelAppointLogistics，ids:{},error:{}",
                    "OcBOrderAppointLogisticsService.cancelAppointLogistics.error"),
                    JSONObject.toJSONString(ids), Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    /**
     * 取消指定快递
     *
     * @param orderId
     * @param operateUser
     * @param isAuto      是否自动取消
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    public ValueHolderV14<Void> orderCancelAppointLogistics(Long orderId, User operateUser, boolean isAuto) {
        ValueHolderV14<Void> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "取消指定快递成功！");
        // 给订单加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = this.ocBOrderMapper.selectByID(orderId);
                if (ocBOrder == null) {
                    throw new NDSException("订单不存在");
                }
                //验证业务类型
                if (!omsBusinessTypeStService.isAutoOccupy(ocBOrder)) {
                    throw new NDSException("该业务类型订单未开启自动寻源,不允许取消指定物流");
                }
                if (ocBOrder.getAppointLogisticsId() == null) {
                    throw new NDSException("订单未指定快递！");
                }
                if (isAuto && !OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())) {
                    throw new NDSException("状态非待寻源不允许自动取消指定快递！");
                }
                if (isAuto && YesNoEnum.Y.getVal().equals(ocBOrder.getIsDetention())) {
                    throw new NDSException("卡单状态不允许自动取消指定快递！");
                }
                if (!(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus()) ||
                        OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus()))) {
                    throw new NDSException("状态非待寻源或待审核不允许取消指定快递！");
                }
                //更新指定快递信息为空
                ocBOrderMapper.updateAppointLogistics(orderId);

                //卡单不释放
                if (!YesNoEnum.Y.getVal().equals(ocBOrder.getIsDetention())) {
                    //加入中间表
                    omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null, 0);
                }

                String logMessage = "取消指定快递成功！";
                if (ocBOrder.getAppointLogisticsId() != null &&
                        StringUtils.isNotEmpty(ocBOrder.getAppointLogisticsEname())) {
                    logMessage = logMessage + "（" + ocBOrder.getAppointLogisticsEname() + "）";
                }
                //记录日志
                insertOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.CANCEL_APPOINT_LOGISTICS.getKey(),
                        logMessage, null, null, operateUser);
                //待审核状态需要回滚状态并释放占用
                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
                    voidSgOmsShareOut(operateUser, ocBOrder);
                }
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", operateUser.getLocale()));
            }
        } catch (Exception e) {
            log.info(LogUtil.format("OcBOrderAppointLogisticsService.orderAppointLogistics.error,orderId:{},error:{}",
                            "OcBOrderAppointLogisticsService.orderAppointLogistics.error"),
                    orderId, Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        } finally {
            redisLock.unlock();
        }
        return v14;
    }

    /**
     * 检查指定物流公司是否合法
     *
     * @param logisticsId
     * @param logisticsIPage
     * @return
     */
    private CpLogistics checkLogistics(Long logisticsId, IPage<CpLogistics> logisticsIPage) {
        List<CpLogistics> logisticsList = logisticsIPage.getRecords()
                .stream().filter(s -> s.getId().equals(logisticsId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(logisticsList)) {
            throw new NDSException("店铺策略设置不可发该物流，不允许指定！");
        }
        return logisticsList.get(0);
    }

    /**
     * 调用sg释放库存
     *
     * @param operateUser
     * @param ocBOrder
     */
    private void voidSgOmsShareOut(User operateUser, OcBOrder ocBOrder) {
        //修改订单状态为待分配，重新占单
        ocBOrderMapper.updateWarehouse(ocBOrder.getId());
        //查询原单明细
        List<OcBOrderItem> ocBOrderItemList = orderItemMapper.selectOrderItemListOccupy(ocBOrder.getId());
        //封装库存释放数据
        SgOmsShareOutRequest request = buildSgOmsShareOutRequest(ocBOrder, ocBOrderItemList, operateUser);
        log.info("调用sg释放库存封装数据为：{}", JSON.toJSONString(request));
        ValueHolderV14 sgValueHolder = sgRpcService.voidSgOmsShareOut(request, ocBOrder, ocBOrderItemList);
        log.info("调用sg释放库存返回接口数据为：{}", JSON.toJSONString(sgValueHolder));
        AssertUtil.assertException(!sgValueHolder.isOK(), "释放库存失败，原因：" + sgValueHolder.getMessage());
    }

    /**
     * 构建库存释放占用请求参数
     *
     * @param orderInfo
     * @param ocBOrderItemList
     * @param user
     * @return
     */
    public SgOmsShareOutRequest buildSgOmsShareOutRequest(OcBOrder orderInfo, List<OcBOrderItem> ocBOrderItemList, User user) {
        SgOmsShareOutRequest request = new SgOmsShareOutRequest();
        request.setSourceBillId(orderInfo.getId());
        request.setSourceBillNo(orderInfo.getBillNo());
        request.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        request.setLoginUser(user);
        List<SgOmsShareOutItemRequest> itemRequestList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            SgOmsShareOutItemRequest sgOmsShareOutItemRequest = new SgOmsShareOutItemRequest();
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            sgOmsShareOutItemRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            sgOmsShareOutItemRequest.setQtyPreout(ocBOrderItem.getQty());
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            itemRequestList.add(sgOmsShareOutItemRequest);
        }
        request.setItemRequestList(itemRequestList);
        return request;
    }

    /**
     * 根据物流公司信息构建返回参数
     *
     * @param logisticsIPage 物流公司档案（分页）
     * @return
     */
    private JSONObject buildResponse(IPage<CpLogistics> logisticsIPage) {
        JSONObject response = new JSONObject();
        response.put("pages", logisticsIPage.getPages());
        response.put("total", logisticsIPage.getTotal());
        List<JSONObject> list = new ArrayList<>();
        for (CpLogistics record : logisticsIPage.getRecords()) {
            JSONObject obj = new JSONObject();
            obj.put("id", record.getId());
            obj.put("ecode", record.getEcode());
            obj.put("ename", record.getEname());
            obj.put("shortName", record.getShortName());
            list.add(obj);
        }
        response.put("data", list);
        return response;
    }


    /**
     * 校验单据状态和获取分页的物流信息
     *
     * @param ids
     * @param page
     * @param size
     * @return
     */
    private IPage<CpLogistics> getCpLogisticsIPage(List<Long> ids, Integer page, Integer size) {
        //查询零售发货单信息并校验状态
        List<OcBOrder> ocBOrderList = queryOrderInfo(ids);
        //查询店铺策略
        StCShopStrategyLogisticsItemResult shopLogistics = queryShopStrategy(ocBOrderList.get(0).getCpCShopId());
        //根据店铺策略查询物流公司信息
        return queryLogistics(page, size, shopLogistics);
    }

    /**
     * 根据店铺策略查询物流公司档案
     *
     * @param page          页码
     * @param size          每页数量
     * @param shopLogistics 店铺策略
     * @return
     */
    private IPage<CpLogistics> queryLogistics(Integer page, Integer size,
                                              StCShopStrategyLogisticsItemResult shopLogistics) {
        List<Long> logisticsIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(shopLogistics.getLogisticsItemList())) {
            logisticsIds = shopLogistics.getLogisticsItemList().stream()
                    .map(StCShopStrategyLogisticsItem::getCpCLogisticsId).collect(Collectors.toList());
        } else {
            //防止查询条件失效
            logisticsIds.add(-1L);
        }

        ValueHolderV14<IPage<CpLogistics>> holderV14 = new ValueHolderV14<>();
        //店铺策略的发货物流公司不做要求
        if (shopLogistics.getLogisticsType() == 0) {
            holderV14 = cpRpcService.queryLogisticsByPage(page, size, null, null);
        }
        //店铺策略的发货物流公司为可发所选物流公司
        if (shopLogistics.getLogisticsType() == 1) {
            holderV14 = cpRpcService.queryLogisticsByPage(page, size, logisticsIds, null);
        }
        //店铺策略的发货物流公司为排除所选物流公司
        if (shopLogistics.getLogisticsType() == 2) {
            holderV14 = cpRpcService.queryLogisticsByPage(page, size, null, logisticsIds);
        }
        if (!holderV14.isOK()) {
            throw new NDSException("查询物流公司失败！");
        }
        IPage<CpLogistics> logisticsIPage = holderV14.getData();
        if (logisticsIPage == null || CollectionUtils.isEmpty(logisticsIPage.getRecords())) {
            throw new NDSException("未能查到满足条件的物流公司！");
        }
        return logisticsIPage;
    }

    /**
     * 查询店铺策略
     *
     * @param shopId 店铺ID
     * @return
     */
    private StCShopStrategyLogisticsItemResult queryShopStrategy(Long shopId) {
        StCShopStrategyLogisticsItemResult shopLogistics =
                shopStrategyQueryCmd.queryShopStrategyLogisticsList(shopId);
        if (shopLogistics == null || shopLogistics.getLogisticsType() == null) {
            throw new NDSException("店铺策略未配置！");
        }
        return shopLogistics;
    }

    /**
     * 根据ID查询零售发货单信息
     *
     * @param ids 订单ID
     * @return
     */
    private List<OcBOrder> queryOrderInfo(List<Long> ids) {
        List<OcBOrder> orderList = ocBOrderMapper.selectList(new LambdaQueryWrapper<OcBOrder>()
                .select(OcBOrder::getId, OcBOrder::getOrderStatus, OcBOrder::getCpCShopId)
                .in(OcBOrder::getId, ids));
        orderList = orderList.stream().filter(ocBOrder -> OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus()) ||
                OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus()) ||
                ocBOrder.getCpCShopId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            throw new NDSException("状态非待寻源或待审核不允许指定快递！");
        }
        Set<Long> shopIds = orderList.stream().map(OcBOrder::getCpCShopId).collect(Collectors.toSet());
        if (shopIds.size() > 1) {
            throw new NDSException("满足状态的订单店铺不一致，不允许批量操作！");
        }
        return orderList;
    }

    /**
     * 订单日志
     *
     * @param orderId
     * @param billNo
     * @param logType
     * @param logMessage
     * @param param
     * @param errorMessage
     * @param operateUser
     */
    private void insertOrderLog(long orderId, String billNo, String logType, String logMessage, String param,
                                String errorMessage, User operateUser) {
        //调用添加订单日志
        try {
            orderLogService.addUserOrderLog(orderId, billNo, logType, logMessage, null, null, operateUser);
        } catch (Exception e) {
            log.error(LogUtil.format("新增订单日志失败，失败原因:{}", orderId), Throwables.getStackTraceAsString(e));
        }
    }
}
