package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.burgeon.r3.sg.share.model.request.translation.SgBStoStockTranslationRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.oc.oms.mapper.IpBJitxOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBJitxOrderMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.IsForbiddenDeliveryEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 中间表唯品会JITX订单处理服务
 *
 * @author: 黄超
 * @since: 2019-01-22
 * create at : 2019-01-22 15:23
 */
@Component
@Slf4j
public class IpJitxOrderService {
    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;

    @Autowired
    private IpBJitxOrderMapper ipJitxOrderMapper;

    @Autowired
    private IpBJitxOrderItemMapper jitxOrderItemMapper;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private IpBTimeOrderVipMapper ipBTimeOrderVipMapper;

    @Autowired
    private IpJitxDeliveryService ipJitxDeliveryService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;

    /**
     * 依据OrderNo进行查询中间表信息数据
     *
     * @param orderNo 订单号
     * @return 中间表信息数据
     */
    public IpJitxOrderRelation selectJitxOrder(String orderNo) {
        IpBJitxOrder orderInfo = this.ipJitxOrderMapper.selectJitxOrderByOrderSn(orderNo);

        if (orderInfo == null) {
            return null;
        }
        IpJitxOrderRelation jitxOrderRelation = new IpJitxOrderRelation();
        long jitxOrderId = orderInfo.getId();
        List<IpBJitxOrderItemEx> orderItemList = this.jitxOrderItemMapper.selectOrderItemList(jitxOrderId);
        List<IpBTimeOrderVip> ipBTimeOrderVips = ipBTimeOrderVipMapper.selectTimeOrderByOrderSn(orderNo);

        jitxOrderRelation.setJitxOrderItemList(orderItemList);
        jitxOrderRelation.setJitxOrder(orderInfo);
        jitxOrderRelation.setTimeOrderVipList(ipBTimeOrderVips);
        return jitxOrderRelation;
    }

    /**
     * 根据平台单号查询唯品会主表信息
     *
     * @param orderNo
     * @return
     */
    public IpBJitxOrder selectJitxOrderOnlyMain(String orderNo) {
        return this.ipJitxOrderMapper.selectJitxOrderByOrderSn(orderNo);
    }

    /**
     * 更新中间表订单状态值
     *
     * @param orderNo             订单编号
     * @param transferOrderStatus 转换订单状态
     * @param remarks             备注信息
     * @return 更新是否成功。true-成功；false-失败
     */
    public boolean updateJitxOrderTransStatus(String orderNo, TransferOrderStatus transferOrderStatus,
                                              String remarks) {
        boolean isUpdateTransNum = transferOrderStatus == TransferOrderStatus.TRANSFERRED;
        if (remarks != null && remarks.length() > IpBJitxOrderMapper.MAX_REMARK_LENGTH) {
            remarks = remarks.substring(0, IpBJitxOrderMapper.MAX_REMARK_LENGTH - 1);
        }
        int result = this.ipJitxOrderMapper.updateOrderIsTrans(orderNo, transferOrderStatus.toInteger(),
                isUpdateTransNum, remarks);
        return result > 0;
    }

    /**
     * 更新中间表
     *
     * @param order 订单
     * @return 更新是否成功。true-成功；false-失败
     */
    public boolean updateIpJitxOrderInfo(IpBJitxOrder order) {
        String orderId = order.getOrderSn();
        order.setOrderSn(null);
        int result = ipJitxOrderMapper.updateById(order);
        order.setOrderSn(orderId);
        if (result > 0) {
            this.updateJitxOrderES(order.getOrderSn());
        }
        return result > 0;
    }

    /**
     * 更新中间表订单ES
     *
     * @param orderNo 订单编号
     */
    public void updateJitxOrderES(String orderNo) {
        String indexName = OcElasticSearchIndexResources.IP_B_JITX_ORDER_INDEX_NAME;
        String typeName = OcElasticSearchIndexResources.IP_B_JITX_ORDER_TYPE_NAME;
        String itemTypeName = OcElasticSearchIndexResources.IP_B_JITX_ORDER_ITEM_TYPE_NAME;
        IpJitxOrderRelation newOrderRelation = this.selectJitxOrder(orderNo);
//        try {
//            if (!SpecialElasticSearchUtil.indexExists(indexName)) {
//                SpecialElasticSearchUtil.indexCreate(IpBJitxOrderItem.class, IpBJitxOrder.class);
//            }
//            SpecialElasticSearchUtil.indexDocument(indexName, typeName, newOrderRelation.getJitxOrder(),
//                    newOrderRelation.getOrderId());
//            if (newOrderRelation.getJitxOrderItemList() != null) {
//                SpecialElasticSearchUtil.indexDocuments(indexName, itemTypeName,
//                        newOrderRelation.getJitxOrderItemList(), "IP_B_JITX_ORDER_ID");
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    /**
     * 更新中间表订单修改地址状态值
     *
     * @param orderNo          单号
     * @param changeAddrStatus 地址更新状态
     * @return
     */
    public boolean updateChangeAddrStatus(String orderNo, Integer changeAddrStatus) {

        int result = ipJitxOrderMapper.updateChangeAddrStatus(orderNo, changeAddrStatus);
        if (result > 0) {
            this.updateJitxOrderES(orderNo);
        }
        return result > 0;
    }

    /**
     * 批量更新中间表转换状态
     *
     * @param orderNoList         单号集合
     * @param transferOrderStatus 转换状态
     * @param user                操作用户
     * @param remark              备注
     * @return
     */
    public boolean batchUpdateJitxOrderTransStatus(List<String> orderNoList, TransferOrderStatus transferOrderStatus,
                                                   User user, String remark) {
        return ipJitxOrderMapper.batchUpdateOrderIsTrans(orderNoList, transferOrderStatus.toInteger(), user.getName(),
                user.getEname(), user.getId().longValue(), remark) > 0;
    }

    /**
     * 批量查询中间表信息
     *
     * @param orderNoList 订单号集合
     * @param notTransfer 转换状态
     * @return 订单信息
     */
    public List<IpJitxOrderRelation> batchSelectJitxOrder(List<String> orderNoList, TransferOrderStatus notTransfer) {
        List<IpBJitxOrder> orderInfoList = ipJitxOrderMapper.batchSelectJitxOrder(orderNoList, notTransfer.toInteger());
        List<IpJitxOrderRelation> orderRelationList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return orderRelationList;
        }
        orderInfoList.forEach(orderInfo -> {
            IpJitxOrderRelation orderRelation = new IpJitxOrderRelation();
            List<IpBJitxOrderItemEx> orderItemList = jitxOrderItemMapper.selectOrderItemList(orderInfo.getId());
            if (CollectionUtils.isNotEmpty(orderItemList)) {
                orderRelation.setJitxOrderItemList(orderItemList);
                orderRelation.setJitxOrder(orderInfo);
                orderRelationList.add(orderRelation);
            } else {
                log.warn("AutoJitxMqTransferTask 查询jitx明细信息不存在，jitxorderId={}", orderInfo.getId());
            }
        });
        return orderRelationList;
    }

    public void updateJitxOrder(IpBJitxOrder jitxOrder, OcBOrder order) {
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(order.getId());
        updateOrder.setExpresscode(jitxOrder.getTransportNo());
        updateOrder.setIsForbiddenDelivery(jitxOrder.getIsForbiddenDelivery());
        updateOrder.setIsInterecept(jitxOrder.getIsForbiddenDelivery());
        updateOrder.setMergedCode(jitxOrder.getMergedCode());
        updateOrder.setJitxRequiresMerge(YesNoEnum.N.getVal() + "");
        if (StringUtils.isNotEmpty(jitxOrder.getMergedCode())) {
            updateOrder.setJitxRequiresMerge(YesNoEnum.Y.getVal() + "");
        }
        if (YesNoEnum.N.getVal().equals(jitxOrder.getIsForbiddenDelivery())) {
            updateOrder.setHoldReleaseTime(new Date());
        }
        LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByVipCarrierCode(jitxOrder.getCarrierCode());
        if (ObjectUtil.isNotNull(logisticsInfo)) {
            updateOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
            updateOrder.setCpCLogisticsEname(logisticsInfo.getName());
            updateOrder.setCpCLogisticsId(logisticsInfo.getId());
        }

        String deliveryWarehouseCode = jitxOrder.getDeliveryWarehouse();
        String type = OmsRedisKeyResources.STORE;
        if (!YesNoEnum.Y.getVal().equals(jitxOrder.getIsStoreDelivery())) {
            type = OmsRedisKeyResources.WAREHOUSE;
        }
        if (StringUtils.isNotEmpty(deliveryWarehouseCode)) {
            StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseService.queryVipcomWarehouse(jitxOrder.getCpCShopId(), deliveryWarehouseCode, type);
            if (ObjectUtil.isNotNull(jitxWarehouse)) {
                Long warehouseId = jitxWarehouse.getCpCPhyWarehouseId();
                CpCPhyWarehouse phyWarehouse = cpRpcService.queryByWarehouseId(warehouseId);
                if (ObjectUtil.isNotNull(phyWarehouse)) {
                    // 将JITX要求发货仓赋值 在改仓成功后会清空 改仓失败则保留 发货单仓库依然变更 但是传平台发货时传原仓库
                    updateOrder.setJitxRequiresDeliveryWarehouseId(phyWarehouse.getId());
                    updateOrder.setJitxRequiresDeliveryWarehouseCode(phyWarehouse.getEcode());
                    updateOrder.setJitxRequiresDeliveryWarehouseName(phyWarehouse.getEname());
                    // 如果实体仓是o2o仓库，对订单进行打标
                    if (StringUtils.equals(phyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
                        order.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
                    } else {
                        order.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
                    }
                }
            }
        }

        updateOrder.setModifieddate(new Date());
        MD5Util.encryptOrderInfo4Merge(order);
        updateOrder.setOrderEncryptionCode(order.getOrderEncryptionCode());
        orderMapper.updateById(updateOrder);
        try {
            StringBuffer sb = new StringBuffer();
            if (StringUtils.isNotEmpty(order.getExpresscode()) && !order.getExpresscode().equals(jitxOrder.getTransportNo())) {
                sb.append("物流信息变更:");
                sb.append("原:");
                sb.append(order.getExpresscode());
                sb.append("新:");
                sb.append(jitxOrder.getTransportNo());
            }
            if (jitxOrder.getIsForbiddenDelivery() != null && !jitxOrder.getIsForbiddenDelivery().equals(order.getIsForbiddenDelivery())) {
                if (sb.length() > 0) {
                    sb.append(";");
                }
                sb.append("禁发变更:");
                sb.append("原:");
                sb.append(IsForbiddenDeliveryEnum.getName(order.getIsForbiddenDelivery()));
                sb.append("新:");
                sb.append(IsForbiddenDeliveryEnum.getName(jitxOrder.getIsForbiddenDelivery()));
            }
            if (StringUtils.isNotEmpty(jitxOrder.getMergedCode()) && !jitxOrder.getMergedCode().equals(order.getMergedCode())) {
                if (sb.length() > 0) {
                    sb.append(";");
                }
                sb.append("合包码变更:");
                sb.append("原:");
                sb.append(order.getMergedCode());
                sb.append("新:");
                sb.append(jitxOrder.getMergedCode());
            }
            if (StringUtils.isNotEmpty(sb.toString())) {
                ocBOrderHoldService.insertOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.JITX_ORDER_INFO_CHANGE.getKey(), sb.toString(), null, null, SystemUserResource.getRootUser());
            }
        }catch (Exception e){
            log.error("JITX订单信息变更日志记录异常：{}", Throwables.getStackTraceAsString(e));
        }
        //根据是否可发货进行hold处理
        if (YesNoEnum.Y.getVal().equals(jitxOrder.getIsForbiddenDelivery())) {
            ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.JITX_FORBIDDEN_DELIVERY);
        } else {
            ocBOrderHoldService.autoUnHoldJitxOrder(order.getId());
        }
    }

    public void updateOcBOrder( Long orderId) {
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(orderId);
        updateOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
        orderMapper.updateById(updateOrder);
    }

    public List<IpBJitxOrder> existCancelOrder(String orderSns) {
        log.info("{},查询是否存在已发货|已揽收取消JitX单据开始 orderSns :{}", this.getClass().getSimpleName(), orderSns);
        String[] split = orderSns.split(",");
        if (ArrayUtils.isEmpty(split)) {
            return null;
        }
        LambdaQueryWrapper<IpBJitxOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(IpBJitxOrder::getOrderSn, Arrays.asList(split));
        queryWrapper.in(IpBJitxOrder::getOrderStatus, Lists.newArrayList(JitxOrderStatus.ORDER_SEND_REFUND, JitxOrderStatus.ORDER_COLLECTED_REFUND));
        queryWrapper.in(IpBJitxOrder::getIsactive, IsActiveEnum.Y.getKey());
        return ipJitxOrderMapper.selectList(queryWrapper);

    }

    public List<IpBJitxOrder> queryAuditOrder(List<String> orderSns) {
        log.info("{},查询是否存在已审核JitX单据开始 orderSns :{}", this.getClass().getSimpleName(), orderSns);
        LambdaQueryWrapper<IpBJitxOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(IpBJitxOrder::getOrderSn, orderSns);
        queryWrapper.and(w -> w.eq(IpBJitxOrder::getOrderStatus, JitxOrderStatus.ORDER_ALREADY_AUDITED)
                        .or().eq(IpBJitxOrder::getOrderStatus, JitxOrderStatus.ORDER_ALREADY_SEND));
        queryWrapper.eq(IpBJitxOrder::getIsactive, IsActiveEnum.Y.getKey());
        return ipJitxOrderMapper.selectList(queryWrapper);

    }

    /**
     * 取消库存占用
     *
     * @param ipJitxOrderRelation
     * @return
     */
    public ValueHolderV14 cancelStockOccupy(IpJitxOrderRelation ipJitxOrderRelation) {
        SgOmsShareOutRequest request = new SgOmsShareOutRequest();
        IpJitxDeliveryRelation ipJitxDeliveryRelation = ipJitxDeliveryService.selectJitxDelivery(ipJitxOrderRelation.getOrderNo());
        if(ipJitxDeliveryRelation!=null) {
            request.setSourceBillId(ipJitxDeliveryRelation.getOrderId());
            //寻仓单取消--平台单号
            request.setSourceBillNo(ipJitxDeliveryRelation.getOrderNo());
            //寻仓单
            request.setSourceBillType(SgConstantsIF.BILL_TYPE_FOR_WAREHOUSE);
            //寻仓单取消--平台单号
            request.setTid(ipJitxOrderRelation.getOrderNo());
            //明细取消、整单取消
            request.setCancelType(SgConstantsIF.OMS_STORAGE_OCCUPY_CANCEL_TYPE_MAIN);
            request.setLoginUser(SystemUserResource.getRootUser());
            ValueHolderV14 v14 = sgRpcService.voidSgOmsShareOut(request, null, null);
            if (log.isDebugEnabled()) {
                log.debug("{}，取消库存占用结果：{}", this.getClass().getName(), v14);
            }
            return v14;
        }
        return ValueHolderV14Utils.getSuccessValueHolder("没有找到关联寻仓单,不进行取消占用");
    }

    /**
     * 重新占用库存
     */
    public void occupyStockAgain(Long orderId) {
        OcBOrder ocBOrder = orderMapper.selectByID(orderId);
        sgOccupiedInventoryService.occupyOrder(ocBOrder, SystemUserResource.getRootUser());
    }

    /**
     * 库存平移
     */
    public ValueHolderV14 shiftStockOccupy(Long orderId, String orderNo) {
        if (log.isDebugEnabled()) {
            log.debug("shiftStockOccupy.执行库存平移接口调用开始");
        }
        OcBOrder ocBOrder = orderMapper.selectByID(orderId);
        List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItemListByOrderId(orderId);
        SgBStoStockTranslationRequest request = new SgBStoStockTranslationRequest();
        request.setLoginUser(SystemUserResource.getRootUser());
        SgBStoStockTranslationRequest.SgBStoStockTranslationMainRequest sgBStoStockTranslationMainRequest = new SgBStoStockTranslationRequest.SgBStoStockTranslationMainRequest();
        sgBStoStockTranslationMainRequest.setTid(ocBOrder.getTid());
        sgBStoStockTranslationMainRequest.setNewId(ocBOrder.getId());
        sgBStoStockTranslationMainRequest.setNewBillNo(ocBOrder.getBillNo());
        sgBStoStockTranslationMainRequest.setNewBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        sgBStoStockTranslationMainRequest.setNewBillDate(ocBOrder.getOrderDate());
        request.setMainRequest(sgBStoStockTranslationMainRequest);

        IpJitxDeliveryRelation ipJitxDeliveryRelation = ipJitxDeliveryService.selectJitxDelivery(orderNo);
        List<IpBJitxDeliveryItemEx> jitxDeliveryItemList = ipJitxDeliveryRelation.getJitxDeliveryItemList();
        List<SgBStoStockTranslationRequest.SgBStoStockTranslationItemRequest> itemList = new ArrayList<>(jitxDeliveryItemList.size());
        Map<String, List<OcBOrderItem>> groupByBarCode = ocBOrderItems.stream().filter(x-> StringUtils.isNotEmpty(x.getBarcode())).collect(Collectors.groupingBy(OcBOrderItem::getBarcode));
        for (IpBJitxDeliveryItemEx deliveryItem : jitxDeliveryItemList) {
            List<OcBOrderItem> ocBOrderItemList = groupByBarCode.get(deliveryItem.getBarcode());
            SgBStoStockTranslationRequest.SgBStoStockTranslationItemRequest item = new SgBStoStockTranslationRequest.SgBStoStockTranslationItemRequest();
            item.setOriginalId(ipJitxDeliveryRelation.getOrderId());
            item.setOriginalItemId(deliveryItem.getId());
            if(CollectionUtils.isNotEmpty(ocBOrderItemList)) {
                OcBOrderItem orderItem = ocBOrderItemList.get(0);
                item.setNewItemId(orderItem.getId());
                item.setPsCSkuId(orderItem.getPsCSkuId());
                item.setQty(orderItem.getQty());
                if (StringUtils.isNotEmpty(orderItem.getTimeOrderId())) {
                    item.setTimeOrderId(Long.valueOf(orderItem.getTimeOrderId()));
                }
            }
            itemList.add(item);
        }
        request.setItemRequestList(itemList);
        if (log.isDebugEnabled()) {
            log.debug("IpJitxOrderService.shiftStockOccupy 库存平移参数:{}", JSON.toJSONString(sgBStoStockTranslationMainRequest));
        }
        return sgRpcService.stockTranslation(request);
    }
}
