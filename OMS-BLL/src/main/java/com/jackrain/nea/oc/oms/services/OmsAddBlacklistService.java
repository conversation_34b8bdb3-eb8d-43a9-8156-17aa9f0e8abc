package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.vp.service.VpAddBlacklistService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2019-07-01 11:06
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsAddBlacklistService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private VpAddBlacklistService vpAddBlacklistService;

    public ValueHolder addBlacklist(JSONObject jsonObject, User user) {
        ValueHolder holder = new ValueHolder();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("加入黑名单入参:{}"), jsonObject);
        }
        Long orderId = jsonObject.getLong("orderId"); //订单主表id
        Integer type = jsonObject.getInteger("type"); //类型
        String remark = jsonObject.getString("remark");
        if (orderId == null || type == null) {
            holder.put("code", -1);
            holder.put("message", "参数错误");
            return holder;
        }
        OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
        if (ocBOrder == null) {
            holder.put("code", -1);
            holder.put("message", "未查询到对应的订单信息");
            return holder;
        }
        holder = vpAddBlacklistService.addBlacklist(ocBOrder, user, type, remark);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("加入黑名单返回参数:{}"), holder);
        }
        return holder;
    }
}

