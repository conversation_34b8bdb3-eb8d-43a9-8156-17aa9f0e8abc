package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillVoidResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.wing.cancel.WingOutStoOutNoticesCancelRequest;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.request.OrderICheckStopRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitOrderUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 订单反审核
 *
 * @date 2019/3/12
 * @author: ming.fz
 */
@Component
@Slf4j
public class OcBOrderTheAuditService {
    @Autowired
    private IpJitxRefundService ipJitxRefundService;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private IpBJitxResetShipWorkflowService ipBJitxResetShipWorkflowService;
    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    OrderStrategyPriceComputeService orderStrategyPriceComputeService;
    @Autowired
    SgRpcService sgRpcService;
    @Autowired
    OrderCancleWmsService orderCancleWmsService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OmsAuditTaskService omsAuditTaskService;

    @Autowired
    private SplitOrderUtils splitOrderUtils;

    @Autowired
    private OmsWmsTaskService wmsTaskService;

    @Autowired
    private OcBJitxDealerOrderTaskService ocBJitxDealerOrderTaskService;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private ThreadPoolTaskExecutor auditThreadPoolExecutor;
    @Autowired
    private ThreadPoolTaskExecutor orderTheAuditStopThreadPoolExecutor;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;


    public ValueHolderV14 orderTheAudit(OrderICheckRequest param, User user, Boolean isRelyOnWing) throws NDSException {
        ValueHolderV14 vh = new ValueHolderV14();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("订单反审核入参={}"), JSON.toJSONString(param));
        }
        if (param == null || param.getIds().length < 1) {
            throw new NDSException(Resources.getMessage("请选择需要审核的记录！", user.getLocale()));
        }

        Long[] ids = param.getIds();
        if (ids.length > 1) {
            int success = 0;
            int failed = 0;
            //列表界面反审核
            for (int i = 0; i < ids.length; i++) {
                //单条记录反审核
                Long orderId = ids[i];
                Long startTime = System.currentTimeMillis();
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("开始执行反审核,订单ID=", orderId));
                }
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        if (updateOrderInfo(user, vh, orderId, false, param.getType(), isRelyOnWing)) {
                            success++;
                        } else {
                            failed++;
                        }
                    } else {
                        throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
                    }
                } catch (Exception ex) {
                    log.error(LogUtil.format("反审核异常={},订单ID=", orderId), Throwables.getStackTraceAsString(ex));
                    failed++;
                } finally {
                    redisLock.unlock();
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("执行反审核结束，耗时={},订单ID=", orderId), System.currentTimeMillis() - startTime);
                }
            }
            vh.setCode(0);
            vh.setMessage("订单反审核成功 " + success + "，订单反审核失败 " + failed);
        } else {
            //单对象界面审核
            Long orderId = ids[0];
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    if (updateOrderInfo(user, vh, orderId, false, param.getType(), isRelyOnWing)) {
                        return vh;
                    }
                } else {
                    throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("反审核异常={},订单ID=", orderId), Throwables.getStackTraceAsString(ex));
                throw new NDSException(ex.getMessage());
            } finally {
                redisLock.unlock();
            }
        }

        return vh;

    }

    /**
     * 提供外部调用抛异常自己处理 自动审核状态改为自动审核
     *
     * @param user
     * @param vh
     * @param id
     * @return
     */
    public boolean updateOrderInfo(User user, ValueHolderV14 vh, Long id, Long type) {
        int record = 0;
        OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
        //唯品会YY经销商未发货取消反审核
        Boolean i = this.jitxDealerOrderCancelBackAudit(user, id, type, ocBOrder);
        if (i != null) {
            return i;
        }
        isOrderTheAudit(ocBOrder, user, true);

        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setWmsCancelStatus(OcOrderWmsStatus.WMS_NOTHING.getVal());
        updateOrder.setId(ocBOrder.getId());
        //d)判断【零售发货单】的【建议预下沉】是否为Y：
        //i.若否，则更新【零售发货单】的【单据状态】=待审核；
        //ii.若是并且单据状态为配货中，则更新【零售发货单】的【实际预下沉状态】=取消中；
        if (TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y.equals(ocBOrder.getSuggestPresinkStatus())) {
            if (OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal() == ocBOrder.getOrderStatus()) {
                updateOrder.setActualPresinkStatus(TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_CANCELING);
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(),
                        " 配货中的定金预售订单进行反审核时，需要等待仓库上架再更新为待审核", "", "", user);
            } else {
                updateOrder.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
                updateOrder.setActualPresinkStatus(TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_NOT_NOTIFIED);
            }
        } else {
            updateOrder.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
        }
        updateOrder.setAutoAuditStatus(0); //自动审核
        updateOrder.setModifierename(user.getEname());
        updateOrder.setModifieddate(new Date(System.currentTimeMillis()));
        updateOrder.setIsOverfive(0L);
        //自动反审核标识
        //updateOrder.setThirdPartyFailStatus("");
        updateOrder.setReserveAuditTag("自动反审核");
        //反审核排除交易平台为唯品会JITX的零售发货单，清空物流单号
        //反审核排除交易平台为唯品会JITX的零售发货单，清空物流单号
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(updateOrder));
        if (!PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
            jsonObject.put("EXPRESSCODE", null);
        }
        Date modifieddate = jsonObject.getDate("MODIFIEDDATE");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = sdf.format(modifieddate);
        jsonObject.put("MODIFIEDDATE", format);
        jsonObject.put("EXAMINE_ORDER_DATE", format);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("反审核结果为:{}"), jsonObject);
        }
        record = ocBOrderMapper.updateRecord(jsonObject);
        ocBOrderMapper.saveNoticesBillNo(id, null, null);
        //清空审核时间
        ocBOrderMapper.cleanAuditTime(id);
        Boolean x = esAndLogRecord(user, vh, id, record, ocBOrder, updateOrder, type);
        //更新审核任务表状态0，计算重试时间

        log.info(LogUtil.format("进行订单拆单"));
        // 当开关开启走仓库拆单 （插入拆单task表），不开启 则不走仓库拆单*（插入传订单审核）
        if (splitOrderUtils.isOpenWareHouseSplitOrder(updateOrder)) {
            // 插入仓库拆单任务表
            wmsTaskService.saveOrUpdateOcBWarehouseSplitTask(updateOrder.getId(), user);
        } else {
            // 插入传wms表
            omsAuditTaskService.createOcBAuditTask(updateOrder, OmsAuditTimeCalculateReason.RESERVE_AUDIT);
        }
        this.createResetShipWorkflow(ocBOrder);
        return x;
    }

    private void createResetShipWorkflow(OcBOrder ocBOrder) {
        try {
            if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
                if (StringUtils.isNotEmpty(ocBOrder.getMergedCode())) {
                    List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItems(ocBOrder.getId());
                    // JitX门店合包 都为一单一件  如果数量大于1 必为合包订单
                    if (itemList.size() > 1) {
                        /**
                         * 此类业务场景，不增加重置发货工单与缓存限制，解决jitx合包单审核报需重置问题（删除缓存后订单可往下流转）
                         */
//                        ipBJitxResetShipWorkflowService.createByOcBOrder(ocBOrder, itemList, SystemUserResource.getRootUser(), IpBJitxResetShipWorkflowService.BACK_AUDIT);
                        log.info(LogUtil.format("jitx合包单不处理重置发货,单号:{}", "jitx合包单不处理重置发货"), ocBOrder.getBillNo());
                    }
                }
            }
        } catch (Exception e) {
            log.error(" JitX合包订单反审核插入发货重置工单异常：{}", Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 提供外部调用抛异常自己处理 功能反审核后单据状态待审核 autoAuidtStatus控制是否开启自动审核
     *
     * @param user
     * @param vh
     * @param id
     * @param autoAuidtStatus true 反审核后自动审核 false反之
     * @param isRelyOnWing    false 作废出库通知单不依赖wing撤回结果，wing撤回失败也作废成功
     * @return
     */
    public boolean updateOrderInfo(User user, ValueHolderV14 vh, Long id, boolean autoAuidtStatus, Long type, Boolean isRelyOnWing) {

        if (autoAuidtStatus) {
            return updateOrderInfo(user, vh, id, type);
        } else {
            int record = 0;
            OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
            long startTime = System.currentTimeMillis();
            //唯品会YY经销商未发货取消反审核
            Boolean i = this.jitxDealerOrderCancelBackAudit(user, id, type, ocBOrder);
            if (i != null) {
                return i;
            }

            isOrderTheAudit(ocBOrder, user, isRelyOnWing);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用SG撤回WMS耗时={},订单ID={}", id), System.currentTimeMillis() - startTime);
            }

            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setWmsCancelStatus(OcOrderWmsStatus.WMS_NOTHING.getVal());
            updateOrder.setId(ocBOrder.getId());
            updateOrder.setIsOverfive(0L);
            //updateOrder.setThirdPartyFailStatus("");
            // @20200726 森马需求需要让反审核也能自动审核 由原来的 2 改为现在的 5
            updateOrder.setAutoAuditStatus(5); //乔丹：人工反审核将自动审核状态改为审核成功就不自动审核
            //d)判断【零售发货单】的【建议预下沉】是否为Y：
            //i.若否，则更新【零售发货单】的【单据状态】=待审核；
            //ii.若是并且单据状态为配货中，则更新【零售发货单】的【实际预下沉状态】=取消中；
            if (TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y.equals(ocBOrder.getSuggestPresinkStatus())) {
                if (OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal() == ocBOrder.getOrderStatus()) {
                    updateOrder.setActualPresinkStatus(TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_CANCELING);
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(),
                            " 配货中的定金预售订单进行反审核时，需要等待仓库上架再更新为待审核", "", "", user);
                } else {
                    updateOrder.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
                    updateOrder.setActualPresinkStatus(TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_NOT_NOTIFIED);
                }
            } else {
                updateOrder.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
            }
            //反审核埋点
            updateOrder.setExamineOrderDate(new Date());
            ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.EXAMINE_ORDER_DATE, new Date(), updateOrder.getId(), user);
            updateOrder.setModifieddate(new Date(System.currentTimeMillis()));
            updateOrder.setModifierename(user.getEname());
            //手动反审核标识
            updateOrder.setReserveAuditTag("手动反审核");
            //反审核排除交易平台为唯品会JITX的零售发货单，清空物流单号
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(updateOrder));
            if (!PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
                jsonObject.put("EXPRESSCODE", null);
            }
            Date modifieddate = jsonObject.getDate("MODIFIEDDATE");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String format = sdf.format(modifieddate);
            jsonObject.put("MODIFIEDDATE", format);
            jsonObject.put("EXAMINE_ORDER_DATE", format);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("反审核结果为:{}"), jsonObject);
            }
            record = ocBOrderMapper.updateRecord(jsonObject);
            ocBOrderMapper.saveNoticesBillNo(id, null, null);
            //清空审核时间
            ocBOrderMapper.cleanAuditTime(id);
            Boolean x = esAndLogRecord(user, vh, id, record, ocBOrder, updateOrder, type);
            //更新审核任务表状态0，计算重试时间
            log.info(LogUtil.format("进行订单拆单"));
            // 当开关开启走仓库拆单 （插入拆单task表），不开启 则不走仓库拆单*（插入传订单审核）
            if (splitOrderUtils.isOpenWareHouseSplitOrder(updateOrder)) {
                // 插入仓库拆单任务表
                wmsTaskService.saveOrUpdateOcBWarehouseSplitTask(updateOrder.getId(), user);
            } else {
                // 插入传wms表
                omsAuditTaskService.createOcBAuditTask(updateOrder, OmsAuditTimeCalculateReason.RESERVE_AUDIT);
            }
            this.createResetShipWorkflow(ocBOrder);
            return x;
        }
    }

    private Boolean jitxDealerOrderCancelBackAudit(User user, Long id, Long type, OcBOrder ocBOrder) {
        if (PlatFormEnum.VIP_JIT.getCode().equals(ocBOrder.getPlatform())) {
            boolean jitxDealerYYWarehouse = ocBJitxDealerOrderTaskService.isJitxDealerYYWarehouse(ocBOrder.getCpCPhyWarehouseId());
            if (jitxDealerYYWarehouse) {
                //未发货取消
                if (LogTypeEnum.JITX_REFUND_BEFORE_DELIVERY.getType().equals(type)) {
                    int orderStatus = ocBOrder.getOrderStatus();
                    //传wms中不允许反审核
                    if (orderStatus == OmsOrderStatus.PENDING_WMS.toInteger()) {
                        throw new NDSException(Resources.getMessage(id + "传wms中不允许反审核！", user.getLocale()));
                    }
                    log.info("当前为jitx订单且仓库是YY经销商仓库,反审核类型为：{}", LogTypeEnum.getValByType(type));
                    if (orderStatus == OmsOrderStatus.CHECKED.toInteger() || orderStatus == OmsOrderStatus.IN_DISTRIBUTION.toInteger()) {
                        CpCPhyWarehouse warehouse = cpRpcService.queryByWarehouseId(ocBOrder.getCpCPhyWarehouseId());
                        WingOutStoOutNoticesCancelRequest request = new WingOutStoOutNoticesCancelRequest();
                        request.setId(ocBOrder.getSgBOutBillNo());
                        request.setWmsType(warehouse.getWmsType());
                        request.setWmsCode(warehouse.getWmsWarehouseCode());
                        request.setWmsPass(warehouse.getEcode());
                        ValueHolderV14 cancelFromWingResult = ipRpcService.jitxDealerOrderCancelFromWing(request);
                        int i = 0;
                        if (cancelFromWingResult.isOK()) {
                            OcBOrder updateOrder = new OcBOrder();
                            updateOrder.setWmsCancelStatus(OcOrderWmsStatus.WMS_NOTHING.getVal());
                            updateOrder.setId(ocBOrder.getId());
                            updateOrder.setReverseAuditType(YesNoEnum.ONE.getKey());
                            updateOrder.setReserveAuditTag("手动反审核");
                            i = ocBOrderMapper.updateById(updateOrder);
                        }
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(),
                                String.format("JITX-YY经销商订单反审核结果:%s", cancelFromWingResult.getMessage()), "", "", user);
                        return i > 0;

                    }
                } else {
                    if (!LogTypeEnum.JITX_REJECT_DELIVERY.getType().equals(type)) {
                        throw new NDSException(Resources.getMessage("反审核失败：当前仓库为YY经销商仓库，只有拒单|未发货取消才能反审核", user.getLocale()));
                    }
                }
            }
        }
        return null;
    }

    public void updateJitxDealerOrderAfterBackAudit(String tid, boolean isReleaseSuccess) {
        List<OcBOrder> ocBOrderList = omsOrderService.selectOmsOrderInfo(tid);
        if (CollectionUtils.isEmpty(ocBOrderList)) {
            return;
        }
        Optional<OcBOrder> findOrder = ocBOrderList.stream().filter(x -> YesNoEnum.ONE.getKey().equals(x.getReverseAuditType())).findAny();
        if (!findOrder.isPresent()) {
            return;
        }
        if (log.isDebugEnabled()) {
            log.debug(" YY取消占单反馈，零售发货单：{}", JSON.toJSONString(findOrder));
        }
        OcBOrder ocBOrder = findOrder.get();
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(ocBOrder.getId());
        updateOrder.setModifieddate(new Date(System.currentTimeMillis()));
        updateOrder.setReverseAuditType(YesNoEnum.ZERO.getKey());

        if (Boolean.FALSE.equals(isReleaseSuccess)) {
            if (log.isDebugEnabled()) {
                log.debug(" 取消失败，取消发货单反审核标记");
            }
            ocBOrderMapper.updateById(updateOrder);
            return;
        }

        //调用作废出库通知单服务
        this.isOrderTheAudit(ocBOrder, SystemUserResource.getRootUser(), true);

        updateOrder.setWmsCancelStatus(OcOrderWmsStatus.WMS_NOTHING.getVal());

        updateOrder.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());

        updateOrder.setReserveAuditTag("手动反审核");

        updateOrder.setIsOverfive(0L);
        // @20200726 森马需求需要让反审核也能自动审核 由原来的 2 改为现在的 5
        updateOrder.setAutoAuditStatus(5);

        //updateOrder.setThirdPartyFailStatus("");
        ocBOrderMapper.updateById(updateOrder);

        ocBOrderMapper.saveNoticesBillNo(ocBOrder.getId(), null, null);

        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(),
                "JITX-YY经销商订单反审核成功", "", "", SystemUserResource.getRootUser());

        ValueHolderV14 vh14 = ipJitxRefundService.markRefund(ocBOrder.getTid(), ocBOrder, SystemUserResource.getRootUser());
    }

    /**
     * es和日志记录
     *
     * @param user
     * @param vh
     * @param id
     * @param record
     * @param ocBOrder
     * @param updateOrder
     * @return
     */
    private Boolean esAndLogRecord(User user, ValueHolderV14 vh, Long id, int record, OcBOrder ocBOrder, OcBOrder updateOrder, Long type) {
        String prefix = LogTypeEnum.getValByType(type);
        if (record < 1) {
            vh.setCode(-1);
            vh.setMessage("订单反审核失败！");
            /**
             *  调用日志服务
             *  参数 订单id、平台单号、用户名、日志类型、不必填、不必填
             */
            try {
               /* omsOrderLogService.addOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), "订单反审核",
                        "订单反审核成功日志", "", "", user.getLastloginip());*/
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(),
                        prefix + OcOrderCheckBoxEnum.enumToStringByValue(ocBOrder.getOrderStatus()) + " 订单反审核失败，原因数据保存失败，请重试！", "", "", user);
            } catch (Exception e) {
                log.error(LogUtil.format("类：OcBOrderTheAuditService 调用日志服务（OmsOrderLogService）异常！error:{}"),
                        Throwables.getStackTraceAsString(e));
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(),
                        prefix + OcOrderCheckBoxEnum.enumToStringByValue(ocBOrder.getOrderStatus()) + " 订单反审核失败日志", JSONObject.toJSONString(ocBOrder), e.toString(), user);
            }

            return false;
        } else {
            vh.setCode(0);
            vh.setMessage("订单反审核成功！");
            /**
             *  调用日志服务
             *  参数 订单id、平台单号、用户名、日志类型、不必填、不必填
             */
            try {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(),
                        prefix + OcOrderCheckBoxEnum.enumToStringByValue(ocBOrder.getOrderStatus()) + " 订单反审核成功日志", "", "", user);
            } catch (Exception e) {
                log.error(LogUtil.format("类：OcBOrderTheAuditService 调用日志服务（OmsOrderLogService）异常！error:{}"),
                        Throwables.getStackTraceAsString(e));
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(),
                        prefix + OcOrderCheckBoxEnum.enumToStringByValue(ocBOrder.getOrderStatus()) + " 订单反审核成功日志", JSONObject.toJSONString(ocBOrder), e.toString(), user);
            }

        }
        return true;
    }

    /**
     * 订单反审核校验
     */
    private void isOrderTheAudit(OcBOrder ocBOrder, User user, Boolean isRelyOnWing) throws NDSException {

        if (ocBOrder == null) {
            throw new NDSException(Resources.getMessage("当前订单不存在", user.getLocale()));
        }
        Long id = ocBOrder.getId();

        int orderStatus = ocBOrder.getOrderStatus();
        //传wms中不允许反审核
        if (orderStatus == OmsOrderStatus.PENDING_WMS.toInteger()) {
            throw new NDSException(Resources.getMessage(id + "传wms中不允许反审核！", user.getLocale()));
        }

        List<OcBOrder> orderList = new ArrayList<>();
        orderList.add(ocBOrder);

        OcBOrderParam orderInfo = new OcBOrderParam();
        orderInfo.setOcBOrder(ocBOrder);
        orderInfo.setOrderItemList(ocBOrderItemMapper.selectOrderItemList(ocBOrder.getId()));
        List<OcBOrderParam> orderInfos = new ArrayList<>();
        orderInfos.add(orderInfo);

        if (orderStatus == OmsOrderStatus.CHECKED.toInteger() || orderStatus == OmsOrderStatus.IN_DISTRIBUTION.toInteger()) {
            //调用【作废出库通知单并取消WMS服务】，调用成功，，如果返回出库通知单不存在或者存在并作废出库通知单成功，
            // 则反审核成功。
            //作废出库通知单并取消WMS服务
            Long startTime = System.currentTimeMillis();
            ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> valueHolderV14 = sgRpcService.invoildOutgoingNotice(orderList, user, isRelyOnWing);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用作废出库通知单并取消WMS服务结果,结果：{}，耗时={},"), JSON.toJSONString(valueHolderV14),
                        System.currentTimeMillis() - startTime);
            }
            if (valueHolderV14.getCode() != ResultCode.SUCCESS) {
                throw new NDSException(Resources.getMessage(id + "反审核失败，作废出库通知单失败！" + valueHolderV14.getMessage(), user.getLocale()));
            }
//            priceOccupation(user, orderInfos);

        } else {
            throw new NDSException(Resources.getMessage(id + "订单状态为已审核或配货中才允许反审核！", user.getLocale()));
        }

    }

    private void priceOccupation(User user, List<OcBOrderParam> orderInfos) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("反审核调用线上代销资金占用变动服务入参：{}"), JSONObject.toJSONString(orderInfos));
        }
//        ValueHolderV14 vh = orderStrategyPriceComputeService.onlineFundOccupy(orderInfos, AcScConstantsIF.BIll_VARIETY_NODE_NO_AUDIT, user);
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("反审核调用线上代销资金占用变动服务入参："
//                    + JSONObject.toJSONString(orderStrategyPriceComputeService));
//        }
//        if (vh.getCode() == ResultCode.FAIL) {
//            throw new NDSException(Resources.getMessage(vh.getMessage(), user.getLocale()));
//        }
    }


    public ValueHolderV14 batchOrderTheAudit(OrderICheckRequest param, User user) {
        Long[] ids = param.getIds();
        Long type = param.getType();
        List<Long> longList = Arrays.asList(ids);
        ValueHolderV14 holder = new ValueHolderV14();
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("零售发货单");
        asyncTaskBody.setTaskType("手动反审核");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        commonTaskExecutor.submit(() -> {
            int tmpNum = longList.size() / 30;//每个小list分的个数
            if (longList.size() % 30 != 0) {
                tmpNum = tmpNum + 1;
            }
            List<List<Long>> partition = Lists.partition(longList, tmpNum);
            List<Future<Integer>> result = new ArrayList<>();
            for (List<Long> longs : partition) {
                Future<Integer> submit = auditThreadPoolExecutor.submit(new OrderTheAuditTask(longs, user, type, this.redisUtil, this.ocBOrderTheAuditService));
                result.add(submit);
            }
            int failCount = 0;
            for (Future<Integer> integerFuture : result) {
                try {
                    failCount = failCount + integerFuture.get();
                } catch (Exception e) {

                }
            }

            ValueHolderV14 holderV14 = new ValueHolderV14();
            holderV14.setCode(0);
            holderV14.setMessage("反审核任务,失败:" + failCount + "条,成功:" + (longList.size() - failCount));
            asyncTaskManager.afterExecute(user, asyncTaskBody, holderV14.toJSONObject());

        });
        holder.setCode(0);
        holder.setData(asyncTaskBody.getId());
        holder.setMessage("零售发货单-反审核任务开始！详情请在我的任务查看");
        return holder;
    }

    public ValueHolderV14 batchOrderTheAuditStop(OrderICheckStopRequest param, User user) {
        Long[] ids = param.getIds();
        Long type = param.getType();
        List<Long> longList = Arrays.asList(ids);
        ValueHolderV14 holder = new ValueHolderV14();
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("零售发货单");
        asyncTaskBody.setTaskType("手动反审核并卡单");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        //卡单默认参数组装
        JSONObject stopJson = orderStopParam();

        commonTaskExecutor.submit(() -> {
            List<List<Long>> partition = Lists.partition(longList, 20);
            List<Future<Integer>> result = new ArrayList<>();
            for (List<Long> longs : partition) {
                Future<Integer> submit = orderTheAuditStopThreadPoolExecutor.submit(
                        new OrderTheAuditStopTask(longs, user, type, this.redisUtil, this.ocBOrderTheAuditService, this.ocBOrderHoldService, stopJson));
                result.add(submit);
            }
            int failCount = 0;
            for (Future<Integer> integerFuture : result) {
                try {
                    failCount = failCount + integerFuture.get();
                } catch (Exception e) {
                    log.error(" batchOrderTheAuditStop error", e);
                }
            }

            ValueHolderV14 holderV14 = new ValueHolderV14();
            holderV14.setCode(0);
            holderV14.setMessage("反审核并卡单任务,失败:" + failCount + "条,成功:" + (longList.size() - failCount));
            asyncTaskManager.afterExecute(user, asyncTaskBody, holderV14.toJSONObject());

        });
        holder.setCode(0);
        holder.setData(asyncTaskBody.getId());
        holder.setMessage("零售发货单-反审核并卡单任务开始！详情请在我的任务查看");
        return holder;
    }

    /**
     * 卡单传入参数如下
     * 卡单原因=审单卡单-疫情停发，自动释放=是，释放时点=指定时点释放，时间=当前时间+系统配置参数“审单员反审核卡单时长(单位：H）”
     *
     * @return
     */
    private JSONObject orderStopParam() {
        JSONObject stopParam = new JSONObject();
        stopParam.put("HOLD_DETENTION_ORDER_REASON", "203");
        stopParam.put("RELEASE_TIME_TYPE", "1");
        stopParam.put("DETENTION_REASON", "审单卡单-疫情停发");
        stopParam.put("IS_AUTO_RELEASE", "Y");

        CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        String releaseTimeHour = strRedisTemplate.opsForValue().get("business_system:oc_order_the_audit_stop_release_time");
        stopParam.put("RELEASE_TIME", DateUtil.formatDateTime(DateUtils.addHours(new Date(), Integer.parseInt(releaseTimeHour))));
        return stopParam;
    }

    private static class OrderTheAuditTask implements Callable<Integer> {

        public List<Long> orderIds;
        private final User user;
        private final Long type;
        private final BllRedisLockOrderUtil redisUtil;
        private final OcBOrderTheAuditService auditService;

        public OrderTheAuditTask(List<Long> orderIds, User user, Long type, BllRedisLockOrderUtil redisUtil, OcBOrderTheAuditService auditService) {
            this.orderIds = orderIds;
            this.user = user;
            this.type = type;
            this.redisUtil = redisUtil;
            this.auditService = auditService;
        }

        @Override
        public Integer call() throws Exception {
            int failCount = 0;
            for (Long id : orderIds) {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                ValueHolderV14 holderV14 = new ValueHolderV14();
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        if (!auditService.updateOrderInfo(user, holderV14, id, false, type, true)) {
                            failCount++;
                        }
                    } else {
                        throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
                    }
                } catch (Exception ex) {
                    failCount++;
                    log.info(LogUtil.format("反审核异常={},订单ID=", id), Throwables.getStackTraceAsString(ex));
                } finally {
                    redisLock.unlock();
                }
            }
            return failCount;
        }
    }


    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    public boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = this.updateOrderInfo(user, holderV14, id, LogTypeEnum.BEFORE_SHIPMENT_REFUND_REVERSE_AUDIT.getType());
            if (isSuccess) {
                //反审核成功  将订单状态改为 未确认
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用反审核失败", e);
            return false;
        }

    }

    private static class OrderTheAuditStopTask implements Callable<Integer> {
        public List<Long> orderIds;
        private final User user;
        private final Long type;
        private final BllRedisLockOrderUtil redisUtil;
        private final OcBOrderTheAuditService auditService;
        private final OcBOrderHoldService ocBOrderHoldService;
        private final JSONObject stopJson;

        public OrderTheAuditStopTask(List<Long> orderIds, User user, Long type, BllRedisLockOrderUtil redisUtil, OcBOrderTheAuditService auditService, OcBOrderHoldService ocBOrderHoldService, JSONObject stopJson) {
            this.orderIds = orderIds;
            this.user = user;
            this.type = type;
            this.redisUtil = redisUtil;
            this.auditService = auditService;
            this.ocBOrderHoldService = ocBOrderHoldService;
            this.stopJson = stopJson;
        }

        @Override
        public Integer call() throws Exception {
            int failCount = 0;
            for (Long id : orderIds) {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                ValueHolderV14 holderV14 = new ValueHolderV14();
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        boolean b = auditService.updateOrderInfo(user, holderV14, id, false, type, true);
                        if (!b) {
                            //反审核失败
                            failCount++;
                        } else {
                            //反审核成功，执行卡单
                            stopJson.put("ids", new JSONArray(Lists.newArrayList(id)));
                            ValueHolder valueHolder = ocBOrderHoldService.orderDetentionSync(stopJson, user, false);
                            if (!valueHolder.isOK()){
                                //卡单失败
                                failCount++;
                            }
                        }
                    } else {
                        throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
                    }
                } catch (Exception ex) {
                    failCount++;
                    log.error(LogUtil.format("反审核并卡单异常={},订单ID=", id), Throwables.getStackTraceAsString(ex));
                } finally {
                    redisLock.unlock();
                }
            }
            return failCount;
        }
    }

    /**
     * 地址修改专用反审核方法（不进入待审核中间表）
     * 只做核心反审核逻辑：校验和作废出库通知单，然后手动更新订单状态
     *
     * @param ocBOrder 订单信息
     * @param user 用户
     * @return 是否成功
     */
    public boolean reverseAuditForAddressModify(OcBOrder ocBOrder, User user) {
        try {
            // 1. 调用核心反审核逻辑（只做校验和作废出库通知单，不更新订单状态）
            this.isOrderTheAudit(ocBOrder, user, true);

            // 2. 手动更新订单状态为待审核（不进入待审核中间表）
            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setId(ocBOrder.getId());
            updateOrder.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
            updateOrder.setWmsCancelStatus(OcOrderWmsStatus.WMS_NOTHING.getVal());
            updateOrder.setAutoAuditStatus(0); // 自动审核状态重置
            updateOrder.setModifierename(user.getEname());
            updateOrder.setModifiername(user.getName());
            updateOrder.setModifieddate(new Date());
            updateOrder.setReserveAuditTag("地址修改反审核");

            // 清空物流单号（参考原反审核逻辑）
            if (!PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
                updateOrder.setExpresscode(null);
            }

            int updateCount = ocBOrderMapper.updateById(updateOrder);
            if (updateCount <= 0) {
                log.error("地址修改反审核：更新订单状态失败，订单ID={}", ocBOrder.getId());
                return false;
            }

            // 清空出库通知单号（参考原反审核逻辑）
            ocBOrderMapper.saveNoticesBillNo(ocBOrder.getId(), null, null);

            // 清空审核时间（参考原反审核逻辑）
            ocBOrderMapper.cleanAuditTime(ocBOrder.getId());

            // 3. 记录操作日志
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(),
                    "地址修改触发反审核成功", "", "", user);

            log.info("地址修改反审核成功，订单ID={}，状态已更新为待审核", ocBOrder.getId());
            return true;

        } catch (Exception e) {
            log.error("地址修改反审核失败，订单ID={}，异常信息={}", ocBOrder.getId(), e.getMessage(), e);
            return false;
        }
    }

}