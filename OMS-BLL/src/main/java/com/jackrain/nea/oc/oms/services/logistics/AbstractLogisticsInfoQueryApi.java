package com.jackrain.nea.oc.oms.services.logistics;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.ip.model.result.QueryTrackData;
import com.jackrain.nea.ip.model.result.QueryTrackResp;
import com.jackrain.nea.oc.oms.model.request.LogisticsInfoQueryRequest;
import com.jackrain.nea.oc.oms.model.result.LogisticsInfoItemNodeResult;
import com.jackrain.nea.oc.oms.model.result.LogisticsInfoItemResult;
import com.jackrain.nea.oc.oms.model.result.LogisticsInfoQueryResult;
import com.jackrain.nea.oc.oms.model.result.LogisticsInfoResult;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.RedisKeyConst;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/6/16
 * description :
 */
@Component
@Slf4j
public abstract class AbstractLogisticsInfoQueryApi implements LogisticsInfoQueryApi{

    @Autowired
    private IpRpcService ipRpcService;
    @Value("${oms.logistics.info.expire.time:40}")
    private Long logisticsInfoExpireTime;
    @Autowired
    private CpRpcService cpRpcService;

    @Override
    public ValueHolderV14<LogisticsInfoQueryResult> doHandle(LogisticsInfoQueryRequest request) {
        if (log.isInfoEnabled()) {
            log.info("Start AbstractLogisticsInfoQueryApi.doHandle request:{}", request);
        }
        ValueHolderV14<LogisticsInfoQueryResult> result = this.invoke(request);
        if(result.isOK()){
            this.fillLogisticsInfo(result);
        }
        if (log.isInfoEnabled()) {
            log.info("Start AbstractLogisticsInfoQueryApi.doHandle result:{}", result);
        }
        return result;
    }

    /**
     * 查询封装单据信息
     * @param request id
     * @return LogisticsInfoQueryResult
     */
    protected abstract ValueHolderV14<LogisticsInfoQueryResult> invoke(LogisticsInfoQueryRequest request);

    /**
     * 补充物流轨迹信息
     * @param result 单据信息
     */
    protected void fillLogisticsInfo(ValueHolderV14<LogisticsInfoQueryResult> result){
        LogisticsInfoResult logisticsInfoResult;
        LogisticsInfoQueryResult data = result.getData();
        if(data.getLogisticsId() == null){
            logisticsInfoResult = new LogisticsInfoResult();
            logisticsInfoResult.setCode(ResultCode.FAIL);
            logisticsInfoResult.setMessage("物流公司为空！");
            data.setLogisticsResult(logisticsInfoResult);
            return;
        }
        CpLogistics logistics = cpRpcService.cpLogisticsInfo(data.getLogisticsId());
        if(logistics == null){
            logisticsInfoResult = new LogisticsInfoResult();
            logisticsInfoResult.setCode(ResultCode.FAIL);
            logisticsInfoResult.setMessage("物流公司查询无结果！");
            data.setLogisticsResult(logisticsInfoResult);
            return;
        }
        data.setLogisticsName(logistics.getEname());
        data.setLogisticsCode(logistics.getTmsLogistic());
        if(StringUtils.isEmpty(data.getExpresscode())){
            logisticsInfoResult = new LogisticsInfoResult();
            logisticsInfoResult.setCode(ResultCode.FAIL);
            logisticsInfoResult.setMessage("物流单号为空！");
            data.setLogisticsResult(logisticsInfoResult);
            return;
        }
        String redisKey = RedisKeyConst.LOGISTICS_QUERY_KEY + data.getExpresscode();
        String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(redisKey);
        if(StringUtils.isEmpty(redisValue)){
            String phone = null;
            if("shunfeng".equals(data.getLogisticsCode())){
                phone = "1777";
            }
            //调用ip接口获取物流轨迹信息
            ValueHolderV14<QueryTrackResp> v14 = ipRpcService.queryLogisticsInfoFrom100(data.getLogisticsCode(), data.getExpresscode(), phone);
            logisticsInfoResult = new LogisticsInfoResult();
            logisticsInfoResult.setCode(v14.getCode());
            logisticsInfoResult.setMessage(v14.getMessage());
            //获取失败直接返回
            if(!v14.isOK()){
                data.setLogisticsResult(logisticsInfoResult);
                return;
            }
            List<LogisticsInfoItemResult> logisticsInfo = new ArrayList<>();
            //获取成功，组装数据
            QueryTrackResp queryTrackResp = v14.getData();
            List<QueryTrackData> queryTrackDataList =  queryTrackResp.getData();
            if(CollectionUtils.isNotEmpty(queryTrackDataList)){
                String lastStatus = null;
                int lastIndex = -1;
                for (int i = 0; i < queryTrackDataList.size(); i++) {
                    QueryTrackData queryTrackData = queryTrackDataList.get(i);
                    if(StringUtils.equals(queryTrackData.getStatus(), lastStatus)){
                        LogisticsInfoItemResult itemResult = logisticsInfo.get(lastIndex);
                        //状态值一样时，创建子节点
                        List<LogisticsInfoItemNodeResult> childNodeList = itemResult.getChildNode();
                        if(childNodeList == null){
                            childNodeList = new ArrayList<>();
                            itemResult.setChildNode(childNodeList);
                        }
                        LogisticsInfoItemNodeResult childNode = new LogisticsInfoItemNodeResult();
                        childNode.setTime(DateUtil.format(DateUtil.stringToDate(queryTrackData.getTime()), "MM-dd HH:mm"));
                        childNode.setContext(queryTrackData.getContext());
                        childNodeList.add(childNode);
                    }else{
                        LogisticsInfoItemResult itemResult = new LogisticsInfoItemResult();
                        itemResult.setStatus(queryTrackData.getStatus());
                        itemResult.setTime(DateUtil.format(DateUtil.stringToDate(queryTrackData.getTime()), "MM-dd HH:mm"));
                        itemResult.setContext(queryTrackData.getContext());
                        logisticsInfo.add(itemResult);
                        lastStatus = queryTrackData.getStatus();
                        lastIndex++;
                    }
                }
            }
            logisticsInfoResult.setLogisticsInfo(logisticsInfo);
            RedisOpsUtil.getStrRedisTemplate().opsForValue().set(redisKey, JSON.toJSONString(logisticsInfoResult), logisticsInfoExpireTime, TimeUnit.MINUTES);
        }else{
            logisticsInfoResult = JSON.parseObject(redisValue, LogisticsInfoResult.class);
        }
        data.setLogisticsResult(logisticsInfoResult);
    }
}
