package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.es.ES4InvoiceNotice;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeMapper;
import com.jackrain.nea.oc.oms.model.result.EsQueryByPageResult;
import com.jackrain.nea.oc.oms.model.result.OcBInvoiceNoticeResult;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNotice;
import com.jackrain.nea.oc.oms.nums.OcInvoiceStatusEnum;
import com.jackrain.nea.oc.oms.nums.OcInvoiceTypeEnum;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author:洪艺安
 * @since: 2019/7/27
 * @create at : 2019/7/27 22:37
 */
@Component
@Slf4j
public class InvoiceNoticeExportService extends CommandAdapter {
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private OcBInvoiceNoticeMapper invoiceNoticeMapper;
    @Autowired
    private InvoiceTableQueryService queryService;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;
    //ES查询起始页码
    private final Integer PAGE_INDEX = 0;
    //ES查询条数
    private final Integer PAGE_SISE = 2000;

    public ValueHolderV14 exportInvoiceNotice(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "开票通知导出成功！");
        @SuppressWarnings("unchecked")
        List<OcBInvoiceNoticeResult> mainExcelList = Lists.newArrayList();
        List<Long> idList = (List) obj.get("idList");
        if (CollectionUtils.isNotEmpty(idList)) {
            mainExcelList = getInvoiceNoticeData(idList).getData();
        } else {
            JSONObject whereKeys = queryService.getEsKeysJo(obj, "whereInfo");
            JSONObject filterKeys = queryService.getDateEsKeysJo(obj, "filterInfo");
            try {
                List<Long> ids = queryInvoicEs(whereKeys, filterKeys);
                mainExcelList = getInvoiceNoticeData(ids).getData();
            } catch (Exception ex) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("开票通知导出失败:" + ex.toString());
            }

        }
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(this.timeout);

        /**
         *  拼接Excel主表sheet表头字段和列表
         * */
        String mainNames[] = {"单据编号", "发票序列号", "平台单号", "发票类型", "下单店铺", "快递公司", "快递单号", "收货人", "手机号",
                "收货地址", "开票公司", "发票抬头", "识别号", "邮箱", "公司地址", "公司电话", "开户行", "开户账号", "制单日期",
                "卖家备注", "发票号", "开票日期", "开票人", "商品名称", "数量", "金额", "单位", "发票备注", "创建人", "发票状态"};
        String orderKeys[] = {"billNo", "invoiceOrder", "sourceCode", "invoiceTypeName", "cpCShopTitle", "cpCLogisticsEname",
                "logisticsNo", "receiveName", "receiverMobile", "receiverAddress", "invoiceCompany", "headerName",
                "taxpayerNo", "email", "companyAddress", "phoneNo", "openingBank", "openingBankAccount", "creationdate",
                "sellerRemark", "invoiceNo", "invoiceTime", "invoiceEname", "psCProEname", "qty", "amtTaxable",
                "unitName", "invoiceRemark", "ownerename", "estatusName"};
        List<String> mainName = Lists.newArrayList(mainNames);
        List<String> mainKey = Lists.newArrayList(orderKeys);
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "开票通知信息", "", mainName, mainKey, mainExcelList, false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "开票通知导出", user, "OSS-Bucket/EXPORT/OcBInvoiceNotice/");
        if (StringUtils.isEmpty(putMsg)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("开票通知导出失败");
        }
        vh.setData(putMsg);
        return vh;
    }

    public ValueHolderV14<List<OcBInvoiceNoticeResult>> getInvoiceNoticeData(List<Long> idList) {
        ValueHolderV14 vh = new ValueHolderV14();
        List<OcBInvoiceNoticeResult> mainExcelList = Lists.newArrayList();
        String ids = "";
        StringBuilder idsSb = new StringBuilder();
        if (idList != null && idList.size() > 0) {
            for (int i = 0; i < idList.size(); i++) {
                idsSb.append(idList.get(i));
                idsSb.append(",");
            }
            ids = idsSb.toString();
            ids = ids.substring(0, ids.length() - 1);
        }
        //查询符合的主表
        List<OcBInvoiceNoticeResult> invoiceNoticeList = invoiceNoticeMapper.getInvoiceExportSql(ids);
        //发票序号映射
        Map<String, String> invoiceOrderMap = new HashMap<>();
        //转换主表对象返回
        if (CollectionUtils.isNotEmpty(invoiceNoticeList)) {
            int invoiceOrderNum = 1;
            for (OcBInvoiceNoticeResult ocBInvoiceNoticeResult : invoiceNoticeList) {
                ocBInvoiceNoticeResult.setInvoiceTypeName(OcInvoiceTypeEnum.enumToStringBykey(ocBInvoiceNoticeResult.getInvoiceType()));
                ocBInvoiceNoticeResult.setEstatusName(OcInvoiceStatusEnum.enumToStringByVal(ocBInvoiceNoticeResult.getEstatus()));
                //发票序列号处理
                if (invoiceOrderMap.keySet().size() > 0) {
                    if (!invoiceOrderMap.keySet().contains(ocBInvoiceNoticeResult.getBillNo())) {
                        invoiceOrderNum++;
                        invoiceOrderMap.put(ocBInvoiceNoticeResult.getBillNo(), getInvoiceOrder(invoiceOrderNum));
                    }
                } else {
                    invoiceOrderMap.put(ocBInvoiceNoticeResult.getBillNo(), getInvoiceOrder(invoiceOrderNum));
                }
                ocBInvoiceNoticeResult.setInvoiceOrder(invoiceOrderMap.get(ocBInvoiceNoticeResult.getBillNo()));
                mainExcelList.add(ocBInvoiceNoticeResult);
            }
        }
        vh.setData(mainExcelList);
        return vh;
    }


    /**
     * @param invoiceOrderNum
     * @return java.lang.String
     * @Description 发票序列号转换
     * <AUTHOR>
     * @date 2019/7/29 11:05
     */
    private String getInvoiceOrder(int invoiceOrderNum) {
        DecimalFormat decimalFormat = new DecimalFormat("000");
        return decimalFormat.format(invoiceOrderNum);
    }

    /**
     * 查询条件
     *
     * @param obj   ES信息
     * @param eskey eskey
     */
    private JSONObject getEsKeysJo(JSONObject obj, String eskey) {
        JSONObject keysJo = new JSONObject();
        JSONObject infoJo = obj.getJSONObject(eskey);
        if (infoJo != null) {
            for (String key : infoJo.keySet()) {
                if (!infoJo.get(key).toString().contains("bSelect-all")) {
                    keysJo.put(key, infoJo.get(key));
                }
            }
        }
        return keysJo;
    }

    /**
     * 查询日期条件
     *
     * @param obj   ES信息
     * @param eskey eskey
     */
    private JSONObject getDateEsKeysJo(JSONObject obj, String eskey) {
        JSONObject keysJo = new JSONObject();
        JSONObject infoJo = obj.getJSONObject(eskey);
        if (infoJo != null) {
            for (String key : infoJo.keySet()) {
                if (!StringUtils.isBlank(infoJo.getJSONArray(key).getString(0)) &&
                        !StringUtils.isBlank(infoJo.getJSONArray(key).getString(1))) {
                    Long startTime = infoJo.getJSONArray(key).getDate(0).getTime();
                    Long endTime = infoJo.getJSONArray(key).getDate(1).getTime();
                    keysJo.put(key, startTime + "~" + endTime);
                }
            }
        }
        return keysJo;
    }

    /**
     * @param whereKeys
     * @return
     * @Description 开票通知ES查询
     * <AUTHOR>
     * @date 2019-08-15 2019-08-15
     */
    public List<Long> queryInvoicEs(JSONObject whereKeys, JSONObject filterKeys) throws IOException {

        List<Long> idList = new ArrayList<>();
        String[] returnFileds = {"ID"};//分库键
        //查询出仓单分库键
        EsQueryByPageResult esResponseInfoVo = selectPrimaryKeyByES(OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_INDEX_NAME, OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_TYPE_NAME, PAGE_INDEX,
                PAGE_SISE, whereKeys, filterKeys, returnFileds, OcBInvoiceNotice.class);
        if (esResponseInfoVo != null) {
            if (esResponseInfoVo.getTotalPage().equals(1)) {
                //第0页
                if (!CollectionUtils.isEmpty(esResponseInfoVo.getBillNoPrimaryList())) {
                    idList.addAll(JSONArray.parseArray(JSON.toJSONString(esResponseInfoVo.getBillNoPrimaryList()), Long.class));
                }
            } else {
                //第1，2,3...页
                int totalPage = esResponseInfoVo.getTotalPage();
                for (int i = 1; i < totalPage; i++) {
                    List<Object> objList = selectPrimaryKey2ByES(OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_INDEX_NAME, OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_TYPE_NAME, i, whereKeys, filterKeys, returnFileds);
                    if (CollectionUtils.isEmpty(objList)) {
                        idList.addAll(JSONArray.parseArray(JSON.toJSONString(objList), Long.class));
                    }
                }
            }
        }

        return idList;
    }

    /**
     * @param indexName
     * @param type
     * @param pageIndex
     * @param pageSize
     * @param whereKeys
     * @param filterKeys
     * @param modelClass
     * @return filterKeys
     * @Description ES查询，返回分库键
     */
    public EsQueryByPageResult selectPrimaryKeyByES(String indexName, String type, int pageIndex, int pageSize,
                                                    JSONObject whereKeys, JSONObject filterKeys, String[] returnFileds, Class modelClass) {
        List<Object> list = new ArrayList<>();
        if (!ElasticSearchUtil.indexExists(indexName)) {
            try {
                ElasticSearchUtil.indexCreate(modelClass);
            } catch (IOException e) {
                log.error(LogUtil.format("ES创建索引出错: {}"), Throwables.getStackTraceAsString(e));
            }
        }
        JSONObject search = ES4InvoiceNotice.findJSONObjectByIndexName(indexName,type,whereKeys, filterKeys, pageIndex, pageSize, returnFileds);
        if (search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getString(returnFileds[0]));
            }
        }
        //返回的总数
        Integer total = search.getInteger("total");
        Integer totalPage = 0;
        //计算循环的次数
        if (total % pageSize == 0) {
            //整除，页数=总数/每页的条数
            totalPage = total / pageSize;
        } else {
            //不整除，页数=总数/每页的条数+1
            totalPage = total / pageSize + 1;
        }
        EsQueryByPageResult esQueryByPageResult = new EsQueryByPageResult();
        esQueryByPageResult.setBillNoPrimaryList(list);
        esQueryByPageResult.setTotal(total);
        esQueryByPageResult.setTotalPage(totalPage);
        return esQueryByPageResult;
    }

    public List<Object> selectPrimaryKey2ByES(String indexName, String type, int pageIndex,
                                              JSONObject whereKeys, JSONObject filterKeys, String[] returnFileds) {

        JSONObject jsonSearch = ES4InvoiceNotice.findJSONObjectByIndexName(indexName, type, whereKeys, filterKeys,pageIndex,PAGE_SISE, returnFileds);
        List<Object> list = new ArrayList<>();
        if (jsonSearch.containsKey("rowcount") && jsonSearch.getInteger("rowcount") > 0) {
            JSONArray jsonArray = jsonSearch.getJSONArray("data");
            //分页循环
            for (Object obj : jsonArray) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getString(returnFileds[0]));
            }
        }
        return list;
    }

}
