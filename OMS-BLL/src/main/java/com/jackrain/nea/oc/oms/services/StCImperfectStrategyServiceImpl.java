package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOperationLogMapper;
import com.jackrain.nea.oc.oms.mapper.StCImperfectStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCImperfectStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.TocCcAppointRuleEnum;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategy;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategyItem;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.StCImperfectStrategyRelation;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.jackrain.nea.oc.oms.model.relation.OcBOrderConst.IS_ACTIVE_YES;

/**
 * 残次策略service impl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class StCImperfectStrategyServiceImpl implements StCImperfectStrategyService {

    @Resource
    private StCImperfectStrategyMapper stCImperfectStrategyMapper;
    @Resource
    private StCImperfectStrategyItemMapper stCImperfectStrategyItemMapper;
    @Resource
    private OcBOperationLogMapper operationLogMapper;
    @Resource
    private BuildSequenceUtil buildSequenceUtil;

    @Override
    public ValueHolderV14<Long> save(StCImperfectStrategyRelation strategyRelation, User user) throws NDSException {
        Long id = buildSequenceUtil.buildStCImperfectStrategySequenceId();
        StCImperfectStrategy stCImperfectStrategy = strategyRelation.getStCImperfectStrategy();
        // 校验主表的开始时间不能大于结束时间 而且是必填
        if (stCImperfectStrategy.getStartTime().after(stCImperfectStrategy.getEndTime())) {
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, "开始时间不能大于结束时间");
        }
        stCImperfectStrategy.setId(id);
        stCImperfectStrategy.setStatus(0);
        BaseModelUtil.initialBaseModelSystemField(stCImperfectStrategy);
        JSONObject obj = new JSONObject();
        obj.put("ST_C_IMPERFECT_STRATEGY", stCImperfectStrategy);
        String strategyNo = SequenceGenUtil.generateSquence("ST_C_IMPERFECT_STRATEGY", obj, user.getLocale(), false);
        stCImperfectStrategy.setStrategyCode(strategyNo);
        stCImperfectStrategy.setIsactive("N");

        // 构建item
        List<StCImperfectStrategyItem> imperfectStrategyItems = strategyRelation.getStCImperfectStrategyItems();
        if (CollectionUtils.isEmpty(imperfectStrategyItems)) {
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, "策略" + stCImperfectStrategy.getStrategyCode() + "保存失败!失败原因:规则明细未维护");
        }
        for (StCImperfectStrategyItem itemStrategy : imperfectStrategyItems) {
            Long itemId = buildSequenceUtil.buildStCImperfectStrategyItemSequenceId();
            itemStrategy.setId(itemId);
            itemStrategy.setImperfectStrategyId(id);
            BaseModelUtil.initialBaseModelSystemField(itemStrategy);
        }

        try {
            stCImperfectStrategyMapper.insert(stCImperfectStrategy);
            for (StCImperfectStrategyItem strategyItem : imperfectStrategyItems) {
                // 构建新增
                OcBOperationLog operationLog = buildItemOperationLog(strategyItem, user);
                stCImperfectStrategyItemMapper.insert(strategyItem);
                operationLogMapper.insert(operationLog);
            }
        } catch (Exception e) {
            log.error("save imperfect strategy error", e);
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, "保存残次策略失败");
        }
        return new ValueHolderV14<>(id, ResultCode.SUCCESS, "残次策略策略保存成功");
    }

    @Override
    public ValueHolderV14<Void> update(StCImperfectStrategyRelation strategyRelation, JSONObject before, JSONObject after, User user, Long id) throws NDSException {
        StCImperfectStrategy oldImperfectStrategy = stCImperfectStrategyMapper.selectById(id);
        // 判断当前状态是否为启用状态。 启用状态不允许添加商品以及规则
        if (IS_ACTIVE_YES.equals(oldImperfectStrategy.getIsactive())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "当前状态为启用状态，不允许修改");
        }
        StCImperfectStrategy stCImperfectStrategy = strategyRelation.getStCImperfectStrategy();
        if (stCImperfectStrategy != null) {
            if (after != null) {
                StCImperfectStrategy afterImperfectStrategy = after.getObject("ST_C_IMPERFECT_STRATEGY", StCImperfectStrategy.class);
                if (afterImperfectStrategy != null) {
                    StCImperfectStrategy updateImperfectStrategy = new StCImperfectStrategy();
                    updateImperfectStrategy.setId(id);
                    BaseModelUtil.makeBaseModifyField(updateImperfectStrategy, user);
                    updateImperfectStrategy.setRemark(afterImperfectStrategy.getRemark());
                    updateImperfectStrategy.setSaleProductAttr(afterImperfectStrategy.getSaleProductAttr());
                    if (StringUtils.isNotEmpty(afterImperfectStrategy.getStrategyName())) {
                        updateImperfectStrategy.setStrategyName(afterImperfectStrategy.getStrategyName().trim());
                    }
                    stCImperfectStrategyMapper.updateById(updateImperfectStrategy);
                }
            }
        }

        List<StCImperfectStrategyItem> stCCycleItemStrategyList = strategyRelation.getStCImperfectStrategyItems();
        if (CollectionUtils.isNotEmpty(stCCycleItemStrategyList)) {
            for (StCImperfectStrategyItem itemStrategy : stCCycleItemStrategyList) {
                try {
                    checkItem(itemStrategy, id);
                } catch (Exception e) {
                    return new ValueHolderV14<>(ResultCode.FAIL, "新增规则失败,失败原因:" + e.getMessage());
                }
                if (itemStrategy.getId() < 0) {
                    Long itemId = buildSequenceUtil.buildStCImperfectStrategyItemSequenceId();
                    itemStrategy.setId(itemId);
                    itemStrategy.setImperfectStrategyId(id);
                    BaseModelUtil.initialBaseModelSystemField(itemStrategy);
                    stCImperfectStrategyItemMapper.insert(itemStrategy);
                    OcBOperationLog operationLog = buildItemOperationLog(itemStrategy, user);
                    operationLogMapper.insert(operationLog);
                } else {
                    StCImperfectStrategyItem updateItemStrategy = new StCImperfectStrategyItem();
                    updateItemStrategy.setId(itemStrategy.getId());
                    updateItemStrategy.setAppointRule(itemStrategy.getAppointRule());
                    updateItemStrategy.setAppointContent(itemStrategy.getAppointContent());
                    BaseModelUtil.makeBaseModifyField(updateItemStrategy, user);
                    stCImperfectStrategyItemMapper.updateById(updateItemStrategy);
                }
            }
        }

        // 修改主表的修改时间
        StCImperfectStrategy strategy = new StCImperfectStrategy();
        strategy.setId(id);
        BaseModelUtil.makeBaseModifyField(strategy, user);
        stCImperfectStrategyMapper.updateById(strategy);

        //删除缓存
        String strategyRedisKey = OmsRedisKeyResources.buildStCImperfectStrategyRedisKey(strategy.getShopId());
        RedisOpsUtil.getStrRedisTemplate().delete(strategyRedisKey);

        return new ValueHolderV14<>(ResultCode.SUCCESS, "残次策略保存成功");
    }

    private OcBOperationLog buildItemOperationLog(StCImperfectStrategyItem strategyItem, User user) {
        StringBuilder afterValue = new StringBuilder();
        afterValue.setLength(0);
        afterValue.append("[").append(TocCcAppointRuleEnum.getDescriptionByVal(strategyItem.getAppointRule())).append("],[").append(strategyItem.getAppointContent())
                .append("]");
        OcBOperationLog operationLog = getOperationLog("ST_C_IMPERFECT_STRATEGY_ITEM", "ADD", strategyItem.getImperfectStrategyId(),
                "残次规则", "新增残次规则", "新增", afterValue.toString(), user);
        return operationLog;
    }

    /**
     * 获取操作日志对象
     *
     * @param tableName
     * @param operationType
     * @param updateId
     * @param tableDescription
     * @param columnName
     * @param columnBeforeValue
     * @param columnAfterValue
     * @param user
     * @return
     */
    private OcBOperationLog getOperationLog(String tableName, String operationType, Long updateId,
                                            String tableDescription, String columnName, String columnBeforeValue,
                                            String columnAfterValue, User user) {
        OcBOperationLog operationLog = new OcBOperationLog();
        operationLog.setId(ModelUtil.getSequence(StConstant.TAB_OC_B_OPERATION_LOG));
        operationLog.setTableName(tableName);
        operationLog.setOperationType(OperationTypeEnum.getNameByValue(operationType));
        operationLog.setUpdateId(updateId);
        operationLog.setUpdateModelName(tableDescription);
        operationLog.setModContent(columnName);
        operationLog.setBeforeData(columnBeforeValue);
        operationLog.setAfterData(columnAfterValue);
        StBeanUtils.makeCreateField(operationLog, user);
        return operationLog;
    }

    public void checkRule(StCImperfectStrategyRelation strategyRelation) {
        StCImperfectStrategy imperfectStrategy = strategyRelation.getStCImperfectStrategy();
        List<StCImperfectStrategyItem> imperfectStrategyItems = strategyRelation.getStCImperfectStrategyItems();
        if (CollectionUtils.isEmpty(imperfectStrategyItems)) {
            return;
        }

        // 获取店铺ID
        Long shopId = imperfectStrategy.getShopId();
        // 获取开始时间
        Date startTime = imperfectStrategy.getStartTime();
        // 获取结束时间
        Date endTime = imperfectStrategy.getEndTime();
        // 开始时间不能大于结束时间
        if (startTime.after(endTime)) {
            throw new NDSException("开始时间不能大于结束时间！");
        }

        // 根据店铺ID 查询出所有可用的规则
        List<StCImperfectStrategy> stCImperfectStrategies = stCImperfectStrategyMapper.selectByShopId(shopId);
        if (CollectionUtils.isEmpty(stCImperfectStrategies)) {
            return;
        }
        List<Long> strategyIdList = new ArrayList<>();
        for (StCImperfectStrategy strategy : stCImperfectStrategies) {
            Date oldStartTime = strategy.getStartTime();
            Date oldEndTime = strategy.getEndTime();
            // 判断strategy的startTime与endTime与新增的startTime与endTime 是否有交叉
            if (!(startTime.compareTo(oldEndTime) > 0 || endTime.compareTo(oldStartTime) < 0)) {
                strategyIdList.add(strategy.getId());
            }
        }
        if (CollectionUtils.isEmpty(strategyIdList)) {
            return;
        }

        List<StCImperfectStrategyItem> strategyItems = stCImperfectStrategyItemMapper.selectByStrategyIdList(strategyIdList);
        if (CollectionUtils.isEmpty(strategyItems)) {
            return;
        }
        // 对rule_type 进行分组
        Map<Integer, List<StCImperfectStrategyItem>> ruleTypeMap = strategyItems.stream().collect(Collectors.groupingBy(StCImperfectStrategyItem::getAppointRule));
        StCImperfectStrategyItem imperfectStrategyItem = imperfectStrategyItems.get(0);
        List<StCImperfectStrategyItem> ruleTypeList = ruleTypeMap.get(imperfectStrategyItem.getAppointRule());
        // 如果没有与保存的规则一致的数据 则返回
        if (CollectionUtils.isEmpty(ruleTypeList)) {
            return;
        }
        for (StCImperfectStrategyItem strategyItem : ruleTypeList) {
            if (imperfectStrategyItem.getAppointContent().equals(strategyItem.getAppointContent())) {
                throw new NDSException("已有相同的数据，请检查！");
            }
        }
    }

    private void checkItem(StCImperfectStrategyItem imperfectStrategyItem, Long mainId) {
        if (imperfectStrategyItem == null) {
            return;
        }

        List<StCImperfectStrategyItem> strategyItems = stCImperfectStrategyItemMapper.selectByStrategyIdWithActive(mainId);
        if (CollectionUtils.isEmpty(strategyItems)) {
            return;
        }
        // 对rule_type 进行分组
        Map<Integer, List<StCImperfectStrategyItem>> ruleTypeMap = strategyItems.stream().collect(Collectors.groupingBy(StCImperfectStrategyItem::getAppointRule));
        List<StCImperfectStrategyItem> ruleTypeList = ruleTypeMap.get(imperfectStrategyItem.getAppointRule());
        // 如果没有与保存的规则一致的数据 则返回
        if (CollectionUtils.isEmpty(ruleTypeList)) {
            return;
        }
        for (StCImperfectStrategyItem strategyItem : ruleTypeList) {
            if (imperfectStrategyItem.getAppointContent().equals(strategyItem.getAppointContent())) {
                throw new NDSException("已有相同的数据，请检查！");
            }
        }
    }
}
