package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.Address;
import com.jackrain.nea.oc.oms.model.relation.ExchangeOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.StandplatExchangeOrderTransferUtil;
import com.jackrain.nea.ps.api.table.IpCStandplatProItem;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @Author: 黄世新
 * @Date: 2020/11/30 4:04 下午
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsStandplatExchangeService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private IpBStandplatRefundMapper ipBStandplatRefundMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderExchangeMapper ocBReturnOrderExchangeMapper;
    @Autowired
    private OmsReturnUtil omsReturnUtil;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private StandplatExchangeOrderTransferUtil exchangeOrderTransferUtil;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;

    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;

    @Autowired
    private IpBStandplatRefundItemMapper ipBStandplatRefundItemMapper;


    public OmsStandPlatRefundRelation selectStandplatExchangeInfo(String returnNo) {

        log.info("{}.updateConsigneeAndPhone start returnNo:{}", this.getClass().getSimpleName(), returnNo);

        OmsStandPlatRefundRelation relation = new OmsStandPlatRefundRelation();
        IpBStandplatRefund standplatRefund = ipBStandplatRefundMapper.selectStandplatRefundByRefundId(returnNo);
        if (standplatRefund == null) {
            return null;
        }
        List<IpBStandplatRefundItem> ipBStandplatRefundItems = ipBStandplatRefundItemMapper.selectRefundItemByRefundId(standplatRefund.getId());
        relation.setIpBStandplatRefundItem(ipBStandplatRefundItems);
        //查询原单 对应订单明细的ooid
        relation.setIpBStandplatRefund(standplatRefund);
        //查询换货sku信息
        ProductSku productSku = this.selectSkuInfo(ipBStandplatRefundItems.get(0));
        if (productSku == null) {
            return relation;
        }
        relation.setProductSku(productSku);
        //原单的ooid
//        String bizOrderId = standplatRefund.getSubOrderId();
//        JSONArray returnData = ES4Order.getIdsByItemOoId(Long.valueOf(bizOrderId));
//        if (CollectionUtils.isEmpty(returnData)) {
//            return relation;
//        }

        /**
         * 拼多多无ooid
         */
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectList(new QueryWrapper<OcBOrder>().lambda()
                .eq(OcBOrder::getTid, standplatRefund.getOrderNo()));
        //查询原单信息
        if (log.isDebugEnabled()) {
            log.debug(" 获取原单和换货订单 {}", JSONObject.toJSONString(ocBOrders));
        }
        // 退单转单优化逻辑优化20230203： 过滤掉取消、作废、复制、补发的单据信息  沟通优化
        ocBOrders = ocBOrders.stream().filter(p -> !(
                OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus())
                        || OmsOrderStatus.SYS_VOID.toInteger().equals(p.getOrderStatus())
                        || YesNoEnum.Y.getVal().equals(p.getIsCopyOrder())
                        || YesNoEnum.Y.getVal().equals(p.getIsResetShip())
        )).collect(Collectors.toList());

        List<OmsOrderExchangeRelation> originalSingleOrders = new ArrayList<>();
        List<OmsOrderExchangeRelation> exchangeOrders = new ArrayList<>();
        for (OcBOrder ocBOrder : ocBOrders) {
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItems(ocBOrder.getId());
            if (CollectionUtils.isNotEmpty(orderItems)) {
                // 根据零售发货单的order_type判断是否为换货单
                // order_type = 2 代表换货单，其他值代表原单
                if (ocBOrder.getOrderType() != null && ocBOrder.getOrderType().equals(2)) {
                    // 换货单：包含所有订单明细
                    OmsOrderExchangeRelation exchangeRelation = new OmsOrderExchangeRelation();
                    exchangeRelation.setOcBOrder(ocBOrder);
                    exchangeRelation.setOcBOrderItems(orderItems);
                    exchangeOrders.add(exchangeRelation);
                    log.debug("添加换货单，订单ID={}，order_type={}，明细数量={}",
                            ocBOrder.getId(), ocBOrder.getOrderType(), orderItems.size());
                } else {
                    // 原单：包含所有订单明细
                    OmsOrderExchangeRelation exchangeRelation = new OmsOrderExchangeRelation();
                    exchangeRelation.setOcBOrder(ocBOrder);
                    exchangeRelation.setOcBOrderItems(orderItems);
                    originalSingleOrders.add(exchangeRelation);
                    log.debug("添加原单，订单ID={}，order_type={}，明细数量={}",
                            ocBOrder.getId(), ocBOrder.getOrderType(), orderItems.size());
                }
            }
        }
        relation.setOriginalSingleOrder(originalSingleOrders);
        relation.setExchangeRelation(exchangeOrders);
        return relation;
    }


    /**
     * 查询换货sku信息
     *
     * @param standplatRefundItem
     * @return
     */
    private ProductSku selectSkuInfo(IpBStandplatRefundItem standplatRefundItem) {
        if (log.isDebugEnabled()) {
            log.debug("{}.selectSkuInfo start", this.getClass().getSimpleName());
        }
        if (StringUtils.isNotEmpty(standplatRefundItem.getExchangeOutSkuId())) {
            String outerSkuId = standplatRefundItem.getExchangeOutSkuId();
            ProductSku selectProductSku = psRpcService.selectProductSku(outerSkuId);
            return selectProductSku;
        } else {
            //通过换货商品的sku判断换货商品是否存在
            //对应淘宝运枢纽商品表的skuid；由于换货信息下发的换货商品信息是淘宝平台的商品ID，因此需要到商品中间表进行查询对应的R3系统商品SKUID
            String exchangeSku = standplatRefundItem.getExchangeSkuId();
            List<IpCStandplatProItem> ipCStandplatProItems = psRpcService.selectIpCStandProductItemBySkuId(exchangeSku);
            if (CollectionUtils.isNotEmpty(ipCStandplatProItems)) {
                //对应sku表真实的skuid
                String outerId = ipCStandplatProItems.get(0).getOuterId();
                // 查询换货的sku信息
                ProductSku productSku = psRpcService.selectProductSku(outerId);
                standplatRefundItem.setExchangeSkuId(outerId);
                standplatRefundItem.setExchangeOutSkuId(outerId);
                return productSku;
            }
        }
        return null;
    }


    /**
     * 根据换货单号或者
     *
     * @param IpBStandplatRefund
     * @return
     */
    public ExchangeOrderRelation selectReturnOrderList(IpBStandplatRefund IpBStandplatRefund) {
        if (log.isDebugEnabled()) {
            log.debug("{}.selectReturnOrderList start", this.getClass().getSimpleName());
        }
        ExchangeOrderRelation relation = new ExchangeOrderRelation();
        List<Long> returnOrderByDisputeId = ES4ReturnOrder.isExistReturnOrderByDisputeId(IpBStandplatRefund.getReturnNo());
        List<OcBReturnOrder> list = null;
        if (CollectionUtils.isNotEmpty(returnOrderByDisputeId)) {
            list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(returnOrderByDisputeId);

        }
        if (CollectionUtils.isEmpty(list)) {
            List<Long> refundByoid = null;
            if(StringUtils.isNotEmpty(IpBStandplatRefund.getSubOrderId())){
                refundByoid = ES4ReturnOrder.isExistReturnOrderRefundByoid(IpBStandplatRefund.getSubOrderId());
            }else if (StringUtils.isNotEmpty(IpBStandplatRefund.getOrderNo())){
                refundByoid = ES4ReturnOrder.isExistReturnOrderByTid(IpBStandplatRefund.getOrderNo());
            }
            if (CollectionUtils.isNotEmpty(refundByoid)) {
                list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(refundByoid);
                relation.setFlag(2);
            }
        } else {
            relation.setFlag(1);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            relation.setOcBReturnOrders(list);
            return relation;
        }
        return null;
    }

    /**
     * 查询当前退换货单的换货明细
     *
     * @param returnId
     * @return
     */
    public List<OcBReturnOrderExchange> selectReturnOrderExchangeList(Long returnId) {
        return ocBReturnOrderExchangeMapper.selectByReturnIdList(returnId);
    }

    /**
     * 退换货转退货
     *
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    public void exchangeToReturn(OcBReturnOrder returnOrder, User user) {
        if (log.isDebugEnabled()) {
            log.debug("{}.exchangeToReturn start:{}", this.getClass().getSimpleName(), JSON.toJSONString(returnOrder));
        }
        Long id = returnOrder.getId();
        Integer returnStatus = returnOrder.getReturnStatus(); //退换货单的状态
        ocBReturnOrderExchangeMapper.deleteByReturnOrderId(id);
        OcBReturnOrder ocBReturnOrder = new OcBReturnOrder();
        ocBReturnOrder.setId(id);
        ocBReturnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());
        if (ReturnStatusEnum.COMPLETION.getVal().equals(returnStatus)) {
            ocBReturnOrder.setReturnStatus(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
        }
        ocBReturnOrder.setExchangeAmt(BigDecimal.ZERO);
        OperateUserUtils.saveOperator(ocBReturnOrder, user);
        ocBReturnOrderMapper.updateById(ocBReturnOrder);
    }

    /**
     * 取消退换货单单及换货订单
     *
     * @param orderInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelExchangeOrderAndReturnOrder(OmsStandPlatRefundRelation orderInfo, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("{}.cancelExchangeOrderAndReturnOrder start:{}", this.getClass().getSimpleName(), JSON.toJSONString(orderInfo));
        }
        OcBReturnOrder ocBReturnOrder = orderInfo.getOcBReturnOrder();
        Integer returnStatus = ocBReturnOrder.getReturnStatus();
        if (!TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode().equals(returnStatus)) {
            //退换货单无法取消了
            return false;
        }
        //取消退换货单 以及换货订单
        this.cancelExchangeOrder(orderInfo, operateUser);
        List<Long> returnOrderIds = new ArrayList<>();
        returnOrderIds.add(ocBReturnOrder.getId());
        omsRefundOrderService.refundOrderClose(returnOrderIds, null, null, operateUser);
        List<OmsOrderExchangeRelation> originalSingleOrder = orderInfo.getOriginalSingleOrder();
        for (OmsOrderExchangeRelation orderExchangeRelation : originalSingleOrder) {
            OcBOrder ocBOrder = orderExchangeRelation.getOcBOrder();
            List<OcBOrderItem> ocBOrderItems = orderExchangeRelation.getOcBOrderItems();
            //重新查询订单明细信息 此地重新查询主要是取消退换货单的时候 会清空已申请数量 不重新查询会导致后面无法生成新的
            List<Long> longList = ocBOrderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            ocBOrderItems = ocBOrderItemMapper.selectOrderItemListsByIds(ocBOrder.getId(), longList);
            orderExchangeRelation.setOcBOrderItems(ocBOrderItems);
        }

        return true;
    }

    /**
     * 取消换货订单
     *
     * @param orderInfo
     * @param operateUser
     */
    public boolean cancelExchangeOrder(OmsStandPlatRefundRelation orderInfo, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("{}.cancelExchangeOrder start", this.getClass().getSimpleName());
        }
        //取消退换货单 以及换货订单
        List<OmsOrderExchangeRelation> exchangeOrder = orderInfo.getExchangeRelation();
        if (CollectionUtils.isNotEmpty(exchangeOrder)) {
            List<OmsOrderExchangeRelation> after = exchangeOrder.stream().filter(p ->
                    OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(p.getOcBOrder().getOrderStatus())
                            || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(p.getOcBOrder().getOrderStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(after)) {
                return false;

            }
            List<OmsOrderExchangeRelation> middleState = exchangeOrder.stream().filter(p ->
                    OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(p.getOcBOrder().getOrderStatus())
                            || OmsOrderStatus.PENDING_WMS.toInteger().equals(p.getOcBOrder().getOrderStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(middleState)) {
                String message = "有换货订单状态为待分配或者待传wms,等待下一次准换";
                throw new NDSException(message);
            }
            //需要反审核
            List<OmsOrderExchangeRelation> needDeAudit = exchangeOrder.stream().filter(p ->
                    OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(p.getOcBOrder().getOrderStatus())
                            || OmsOrderStatus.CHECKED.toInteger().equals(p.getOcBOrder().getOrderStatus())).collect(Collectors.toList());
            for (OmsOrderExchangeRelation orderExchangeRelation : needDeAudit) {
                OcBOrder ocBOrder = orderExchangeRelation.getOcBOrder();
                if (!omsReturnUtil.toExamineOrder(ocBOrder, operateUser)) {
                    String message = "换货关闭,换货订单反审核失败,等待下次继续转换";
                    throw new NDSException(message);
                }
            }
            //此时换货订单的状态 只会是缺货  或者待审核  调用标记退款完成
            for (OmsOrderExchangeRelation orderExchangeRelation : exchangeOrder) {
                Long id = orderExchangeRelation.getOcBOrder().getId();
                List<OcBOrderItem> ocBOrderItems = orderExchangeRelation.getOcBOrderItems();
                List<OcBOrderItem> orderItems = ocBOrderItems.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE || p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
                List<Long> itemIds = orderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                if (!omsReturnUtil.signRefundNew(id, itemIds, operateUser, OrderHoldReasonEnum.ADD_EXCHANGE_ORDER)) {
                    String message = "换货关闭,标记退款完成失败,等待下次继续转换";
                    throw new NDSException(message);
                }
            }
        }
        return true;
    }

    /**
     * 更新换货订单以及退换货单
     *
     * @param orderInfo
     * @param operateUser
     */
    public boolean updateExchangeOrderAndReturnOrder(OmsStandPlatRefundRelation orderInfo, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("{}.updateExchangeOrderAndReturnOrder start:{}", this.getClass().getSimpleName(), JSON.toJSONString(orderInfo));
        }
        IpBStandplatRefund IpBStandplatRefund = orderInfo.getIpBStandplatRefund();
        OcBReturnOrder ocBReturnOrder = orderInfo.getOcBReturnOrder();

        OcBReturnOrder returnOrder = new OcBReturnOrder();
        returnOrder.setId(ocBReturnOrder.getId());
        returnOrder.setTbDisputeId(Long.valueOf(IpBStandplatRefund.getReturnNo()));
        //退款金额
        //returnOrder.setReturnAmtActual(IpBStandplatRefund.getPrice());
        //卖家昵称
        //returnOrder.setBuyerNick(IpBStandplatRefund.getBuyerNick());
        //退款创建时间
        // 更新是否换货预留库存   是
        returnOrder.setIsReserved(1);
        returnOrder.setReturnCreateTime(IpBStandplatRefund.getCreated());
        //最后修改时间
        returnOrder.setLastUpdateTime(IpBStandplatRefund.getModified());
        //退款说明
        returnOrder.setReturnDesc(IpBStandplatRefund.getReturnReason());
        if (StringUtils.isEmpty(ocBReturnOrder.getLogisticsCode())) {
            returnOrder.setLogisticsCode(IpBStandplatRefund.getLogisticsNo());
        }
        //物流公司
        String cpCLogisticsEname = ocBReturnOrder.getCpCLogisticsEname();
        if (StringUtils.isEmpty(cpCLogisticsEname)) {
            returnOrder.setCpCLogisticsEname(IpBStandplatRefund.getCompanyName());
            omsReturnUtil.setLogisticInfo(returnOrder, IpBStandplatRefund.getCompanyName());
        }
        //加入“空运单号延迟推单有效时间”字段
        returnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder));
        ocBReturnOrderMapper.updateById(returnOrder);
        List<OcBReturnOrderExchange> ocBReturnOrderExchanges = orderInfo.getOcBReturnOrderExchanges();
        if (CollectionUtils.isNotEmpty(ocBReturnOrderExchanges)) {
            for (OcBReturnOrderExchange ocBReturnOrderExchange : ocBReturnOrderExchanges) {
                OcBReturnOrderExchange orderExchange = new OcBReturnOrderExchange();
                orderExchange.setExchangeStatus(IpBStandplatRefund.getReturnStatus() + "");
                orderExchange.setDisputeId(IpBStandplatRefund.getReturnNo());
                QueryWrapper<OcBReturnOrderExchange> wrapper = new QueryWrapper<>();
                wrapper.eq("id", ocBReturnOrderExchange.getId());
                wrapper.eq("oc_b_return_order_id", ocBReturnOrderExchange.getOcBReturnOrderId());
                ocBReturnOrderExchangeMapper.update(orderExchange, wrapper);
            }

        }
        List<OmsOrderExchangeRelation> exchangeOrder = orderInfo.getExchangeRelation();
        if (CollectionUtils.isNotEmpty(exchangeOrder)) {
            for (OmsOrderExchangeRelation orderExchangeRelation : exchangeOrder) {
                List<OcBOrderItem> ocBOrderItems = orderExchangeRelation.getOcBOrderItems();
                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                    OcBOrderItem orderItem = new OcBOrderItem();
                    orderItem.setId(ocBOrderItem.getId());
                    orderItem.setExchangeBillNo(Long.valueOf(IpBStandplatRefund.getReturnNo()));
                    orderItem.setOcBOrderId(ocBOrderItem.getOcBOrderId());
                    omsOrderItemService.updateOcBOrderItem(orderItem, ocBOrderItem.getOcBOrderId());
                }
            }
            return false;
        } else {
            //则生成换货订单
            OcBReturnOrderRelation orderRelation = new OcBReturnOrderRelation();
            orderRelation.setReturnOrderInfo(ocBReturnOrder);
            exchangeOrderTransferUtil.buildExchangeOrder(orderRelation, orderInfo, operateUser);
            OcBOrder ocBOrder = orderRelation.getOcBOrder();
            List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
            if (ocBOrder != null) {
                ocBOrderMapper.insert(ocBOrder);
                ocBOrderItemMapper.batchInsert(ocBOrderItems);
                OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                toBeConfirmedTask.setOrderId(ocBOrder.getId());
                toBeConfirmedTask.setCreationdate(new Date());
                toBeConfirmedTask.setStatus(0);
                toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
                ocBOrder.setIsInterecept(OmsOrderIsInterceptEnum.YES.getVal());
                ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder, OrderHoldReasonEnum.ADD_EXCHANGE_ORDER);
                orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_ADD.getKey(), "新增换货订单成功", "", "", operateUser);
            }
        }
        return true;
    }

    /**
     * 更新物流公司名称及单号
     *
     * @param ocBReturnOrder
     * @param standplatRefund
     */
    public void updateLogisticsCodeAndName(OcBReturnOrder ocBReturnOrder,
                                           IpBStandplatRefund standplatRefund,
                                           User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("{}.updateLogisticsCodeAndName start:{}", this.getClass().getSimpleName(), JSON.toJSONString(standplatRefund));
        }
        String logisticsCode = ocBReturnOrder.getLogisticsCode(); //物流单号
        String buyerLogisticNo = standplatRefund.getLogisticsNo(); //买家物流单号
        if (StringUtils.isEmpty(logisticsCode) || !logisticsCode.equals(buyerLogisticNo)) {
            //更新退换单表的物流单号  物流公司
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            Long id = ocBReturnOrder.getId();
            String buyerLogisticName = standplatRefund.getCompanyName(); //买家物流公司名称
            returnOrder.setId(id);
            returnOrder.setLogisticsCode(buyerLogisticNo);
            returnOrder.setCpCLogisticsEcode(buyerLogisticName);
            omsReturnUtil.setLogisticInfoByCarrierCode(returnOrder, buyerLogisticName);
            OperateUserUtils.saveOperator(returnOrder, operateUser);
            //加入“空运单号延迟推单有效时间”字段
            returnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder));
            ocBReturnOrderMapper.updateById(returnOrder);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateConsigneeAndPhone(OcBReturnOrder ocBReturnOrder,
                                        IpBStandplatRefund taobaoExchange, List<OmsOrderExchangeRelation> exchangeOrder,
                                        User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("{}.updateConsigneeAndPhone start", this.getClass().getSimpleName());
        }
        //手机
        String receivePhone = ocBReturnOrder.getReceiveMobile();
        //收货人姓名
        String receiveName = ocBReturnOrder.getReceiveName();
        //平台上的手机号
        String buyerPhone = taobaoExchange.getBuyerMobile();
        //平台上的买家姓名
        String buyerName = taobaoExchange.getBuyerNick();
        if (StringUtils.isEmpty(buyerPhone) || !buyerPhone.equals(receivePhone)
                || StringUtils.isEmpty(buyerName) || !buyerName.equals(receiveName)) {
            //更新退换单表的物流单号  物流公司
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            Long id = ocBReturnOrder.getId();
            returnOrder.setId(id);
            returnOrder.setReceiveMobile(buyerPhone);
            returnOrder.setReceiveName(buyerName);
            OperateUserUtils.saveOperator(returnOrder, operateUser);
            ocBReturnOrderMapper.updateById(returnOrder);

        }
        if (CollectionUtils.isNotEmpty(exchangeOrder)) {
            for (OmsOrderExchangeRelation orderExchangeRelation : exchangeOrder) {
                OcBOrder ocBOrder = orderExchangeRelation.getOcBOrder();
                OcBOrder order = new OcBOrder();
                order.setId(ocBOrder.getId());
                order.setReceiverMobile(buyerPhone);
                order.setReceiverName(buyerName);
                OperateUserUtils.saveOperator(order, operateUser);
                ocBOrderMapper.updateById(order);
            }
        }
    }

    public boolean isAddressChange(OmsStandPlatRefundRelation orderInfo) {
        if (log.isDebugEnabled()) {
            log.debug("{}.isAddressChange start", this.getClass().getSimpleName());
        }
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        String buyerAddress = ipBStandplatRefund.getExchangeReceiverAddress();
        //新的地址
        Address address = new Address();
        address.setProvince(ipBStandplatRefund.getExchangeReceiverProvince());
        address.setCity(ipBStandplatRefund.getExchangeReceiverCity());
        address.setCounty(ipBStandplatRefund.getExchangeReceiverDistrict());
        address.setAddr(buyerAddress);
//        Address address = exchangeOrderTransferUtil.addressResolutionNew(buyerAddress);
        OcBReturnOrder ocBReturnOrder = orderInfo.getOcBReturnOrder();
        //省
        String receiverProvinceName = ocBReturnOrder.getReceiverProvinceName();
        //市
        String receiverCityName = ocBReturnOrder.getReceiverCityName();
        //区
        String receiverAreaName = ocBReturnOrder.getReceiverAreaName();
        String addr = address.getAddr();
        String receiveAddress = ocBReturnOrder.getReceiveAddress();
        boolean flag = (StringUtils.isNotEmpty(addr) && !addr.equals(receiveAddress))
                || (address.getProvince() != null && !address.getProvince().equals(receiverProvinceName))
                || (address.getCity() != null && !address.getCity().equals(receiverCityName))
                || (address.getCounty() != null && !address.getCounty().equals(receiverAreaName));
        return flag;
    }
}
