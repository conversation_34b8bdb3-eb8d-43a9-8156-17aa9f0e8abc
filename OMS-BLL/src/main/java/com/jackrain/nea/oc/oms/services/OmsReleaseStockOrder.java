package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2023/3/24 14:06
 * @Version 1.0
 */
public class OmsReleaseStockOrder {


    private List<OcBOrder> orders;

    private List<OcBOrderItem> orderItems;

    private String skuCode;

    private BigDecimal qty;

    private Map<Long, List<OcBOrderItem>> orderItemMap;

    /**
     * 需要反审核的订单
     */
    private List<OcBOrderParam> params = new ArrayList<>();

    public OmsReleaseStockOrder(List<OcBOrder> orders, List<OcBOrderItem> orderItems, String skuCode, BigDecimal qty){
        this.orders = orders;
        this.orderItems = orderItems;
        this.skuCode = skuCode;
        this.qty = qty;
        this.orderItemMap = orderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
    }


    public List<OcBOrderParam> handleOrder(){
        //代寻源
        List<OcBOrder> sourcingOrders = orders.stream().filter(p -> OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
        //List<OcBOrder> unConfirmOredrs = orders.stream().filter(p -> ().collect(Collectors.toList());
        //已审核
        List<OcBOrder> auditOrders = orders.stream().filter(p -> (OmsOrderStatus.CHECKED.toInteger().equals(p.getOrderStatus()) || OmsOrderStatus.UNCONFIRMED.toInteger().equals(p.getOrderStatus()))).collect(Collectors.toList());
        //配货中
        //List<OcBOrder> distributionOrders = orders.stream().filter(p -> OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sourcingOrders)) {
            for (OcBOrder sourcingOrder : sourcingOrders) {
                List<OcBOrderItem> itemList = orderItemMap.get(sourcingOrder.getId());
                BigDecimal minQty = getMinQty(skuCode, itemList);
                qty = qty.subtract(minQty);
                if (qty.compareTo(BigDecimal.ZERO) <= 0){
                   return null;
                }
            }
        }
        this.reverseOrder(auditOrders);
        //this.reverseOrder(distributionOrders);
        if (CollectionUtils.isNotEmpty(params)) {
            Collections.reverse(params);
        }
        return params;
    }

    private void reverseOrder(List<OcBOrder> orders) {
        if (qty.compareTo(BigDecimal.ZERO) > 0 && CollectionUtils.isNotEmpty(orders)) {
            for (OcBOrder order : orders) {
                List<OcBOrderItem> itemList = orderItemMap.get(order.getId());
                BigDecimal minQty = getMinQty(skuCode, itemList);
                if (qty.compareTo(BigDecimal.ZERO) > 0) {
                    OcBOrderParam orderParam = new OcBOrderParam();
                    orderParam.setOcBOrder(order);
                    orderParam.setOrderItemList(itemList);
                    params.add(orderParam);
                    qty = qty.subtract(minQty);
                } else {
                    break;
                }
            }
        }
    }


    private BigDecimal getMinQty(String skuCode,  List<OcBOrderItem> itemList) {
        itemList.sort(Comparator.comparing(OcBOrderItem::getQty));
        for (OcBOrderItem item : itemList) {
            String psCSkuEcode = item.getPsCSkuEcode();
            if (skuCode.equals(psCSkuEcode)) {
                return item.getQty();
            }
        }
        return BigDecimal.ZERO;
    }


}
