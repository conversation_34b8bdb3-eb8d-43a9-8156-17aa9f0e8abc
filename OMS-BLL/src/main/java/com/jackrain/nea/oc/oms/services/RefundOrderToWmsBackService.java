package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.enums.DrpStoreTypeEnum;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.wing.CustomerQueryOrderOutModel;
import com.jackrain.nea.ip.model.wing.PreSaleSinkBackModel;
import com.jackrain.nea.ip.model.wing.PreSaleSinkDeliveryBackModel;
import com.jackrain.nea.ip.model.wing.ReturnOrderNoticeBackModel;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.dto.ReturnOrderConfirmDTO;
import com.jackrain.nea.oc.oms.es.ES4RefundIn;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.mapper.task.OcBRefundInTaskMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBToWingDeliveryTaskMapper;
import com.jackrain.nea.oc.oms.model.CustomerQueryOrderResult;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundInTask;
import com.jackrain.nea.oc.oms.model.table.task.OcBToWingDeliveryTask;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.ReturnOrderNodeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.oc.oms.services.serviceimpl.RefundInTaskServiceImpl;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.ReturnOrderPushWingUtils;
import com.jackrain.nea.oc.oms.util.WmsUserCreateUtil;
import com.jackrain.nea.ps.api.request.SkuInfoListRequest;
import com.jackrain.nea.ps.api.result.ProAndSkuResult;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.services.PsGetCommodityInformationService;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.resource.WmsUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.AddOrderNoticeAndOutService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.*;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 零售退回传服务
 *
 * @author: 郑立轩
 * @since: 2019/5/9
 * create at : 2019/5/9 9:57
 */
@Slf4j
@Component
public class RefundOrderToWmsBackService {
    @Autowired
    private OmsReturnOrderService omsReturnOrderService;

    @Autowired
    private BasicCpQueryService basicCpQueryService;

    @Autowired
    private OcBRefundInLogService ocBRefundInLogService;

    @Autowired
    private ReturnOrderLogService returnOrderLogService;

    @Autowired
    protected PsRpcService psRpcService;
    @Autowired
    protected OcBOrderMapper ocBOrderMapper;
    @Autowired
    WmsUserCreateUtil wmsUserCreateUtil;
    @Autowired
    OmsReturnOrderService returnOrderService;
    @Autowired
    private AddOrderNoticeAndOutService addOrderNoticeAndOutService;
    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;
    @Autowired
    private OcBReturnOrderRefundMapper refundMapper;
    @Autowired
    private OcBRefundBatchMapper ocBRefundBatchMapper;
    @Autowired
    private OcBReturnOrderDefectMapper defectMapper;
    @Autowired
    private PsGetCommodityInformationService psGetCommodityInformationService;
//    @Autowired
//    private R3MqSendHelper r3MqSendHelper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private OcBRefundInMapper ocBRefundInMapper;
    @Autowired
    private OcBRefundInProductItemMapper ocBRefundInProductItemMapper;
    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OcBRefundInTaskMapper ocBRefundInTaskMapper;

    @Autowired
    private RefundInMakeUpService refundInMakeUpService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    OcBToWingDeliveryTaskMapper ocBToWingDeliveryTaskMapper;

    @Autowired
    private OcBReturnOrderNodeRecordService nodeRecordService;


    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    @Autowired
    private IpBJitxDeliveryRecordMapper ipBJitxDeliveryRecordMapper;

    @Autowired
    private IpBJitxOrderMapper ipBJitxOrderMapper;

    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;
    /*
    message范例：
    {
        "request": {
            "orderLines": [
                {
                    "orderLine": {
                        "itemId": "6919096007470",
                        "orderLineNo": "121",
                        "inventoryType": "TH",
                        "planQty": "1",
                        "sourceOrderCode": "1123342273002166384",
                        "itemCode": "6919096007470",
                        "actualQty": "1"
                    }
                }
            ],
            "returnOrder": {
                "logisticsName": "顺丰速运",
                "orderType": "THRK",
                "senderInfo": {
                    "province": "湖北省",
                    "city": "宜昌市",
                    "name": "侠071605向阳花开",
                    "mobile": "13797029502",
                    "detailAddress": "问安镇万水桥村七组"
                },
                "returnOrderCode": "TH20071600000167",
                "outBizCode": "A200716000035",
                "returnOrderId": "A200716000035",
                "logisticsCode": "SF",
                "extendProps": {
                    "picture": "FTP://**********/B2CRETURN/A200716000035.JPG"
                },
                "orderConfirmTime": "2020-07-16 22:22:15",
                "warehouseCode": "WP01-R-TH"
            }
        },
        "method": "returnorder.confirm"
    }
     */
    @Autowired
    private RefundInTaskServiceImpl refundInTaskService;


    /**
     * 依据单据编号未查询到退换货单：生成退货入库单，等待匹配流程
     *
     * @param orderLines
     * @param jsonSender
     * @param returnOrder
     * @param refundIn
     * @param returnOrderCode wms 编号
     * @param warehouseCode
     */
    @Transactional(rollbackFor = Exception.class)
    public void returnOrderNotMatch(OcBRefundInTask refundInTask, JSONArray orderLines, JSONObject jsonSender
            , JSONObject returnOrder, OcBRefundIn refundIn, String returnOrderCode, String warehouseCode) {
        if (refundIn != null) {
            throw new NDSException("退货入库单已经存在该WMS单号");
        }
        /**退货入库表**/
        String allSku = "";
        OcBRefundIn ocBRefundIn = buildOcBRefundIn(returnOrder, orderLines, jsonSender, returnOrderCode, warehouseCode);
        /**退货入库单-明细表**/
        List<OcBRefundInProductItem> itemList = new ArrayList<>();
        if (ocBRefundInMapper.insert(ocBRefundIn) > 0) {
            for (int i = 0; i < orderLines.size(); i++) {
                JSONObject orderObject = orderLines.getJSONObject(i).getJSONObject("orderLine");
                if (orderObject != null) {
                    //按照数量QTY进行拆明细
                    int qty = orderObject.getBigDecimal("planQty").intValue();
                    //拼接所有SKU
                    allSku = allSku + orderObject.getString("itemCode") + "(" + qty + "),";

                    for (int j = 0; j < qty; j++) {
                        OcBRefundInProductItem ocBRefundInItem = new OcBRefundInProductItem();
                        String itemCode = orderObject.getString("itemCode");
                        ProductSku skuInfo = psRpcService.selectProductSku(itemCode);
                        if (skuInfo == null) {
                            ocBRefundInMapper.deleteById(ocBRefundIn.getId());
                            // 更新为处理失败
                            this.update2RefundTask(refundInTask, OcBRefundInTaskStatusEnum.HANDLE_FAIL.getVal(),
                                    OcBRefundInTaskStatusEnum.HANDLE_FAIL.getDesc() + "SKU编码:itemCode商品信息不存在" +
                                            ",不生成退货入库单!", null, WmsUserResource.getWmsUser());
                            return;
                        }
                        //国际码 skuInfo.getBarcode69() "10010001S"
                        ocBRefundInItem.setGbcode(skuInfo.getBarcode69());
                        //条码
                        ocBRefundInItem.setPsCSkuEcode(itemCode);
                        // @20200718 增加SKUID等信息
                        ocBRefundInItem.setPsCSkuId(skuInfo.getId());

                        //实收条码(PRD要求为空)
                        //ocBRefundInItem.setRealSkuEcode(orderObject.getString(""));
                        //商品编号 skuInfo.getEcode() "10010"
                        ocBRefundInItem.setPsCProEcode(skuInfo.getSku());
                        //商品名称 skuInfo.getName() "SM爽肤水"
                        ocBRefundInItem.setPsCProEname(skuInfo.getName());
                        //商品标记
                        ocBRefundInItem.setProductMark(orderObject.getString("inventoryType"));
                        //是否无原单条码
                        ocBRefundInItem.setIsWithoutOrig(IsWithoutOrigEnum.IS_WITHOUT_ORIG.getVal());
                        //数量
                        ocBRefundInItem.setQty(new BigDecimal(1));
                        //退换货单编号、订单编号(PRD要求为空)
                        //明细编号
                        ocBRefundInItem.setId(ModelUtil.getSequence("OC_B_REFUND_IN_PRODUCT_ITEM"));
                        //是否匹配
                        ocBRefundInItem.setIsMatch(OcBOrderConst.REFUND_IN_MATCHSTATUS_UN);
                        //是否生成调整单
                        ocBRefundInItem.setIsGenAdjust(0);
                        //是否生成入库单
                        // ocBRefundInItem.setIsMatch(0);   // @20200717 代码和往上第四行重复了
                        //是否生成错发调整单
                        ocBRefundInItem.setIsGenWroAdjust(IsGenWroAdjustEnum.NO.integer());
                        //是否生成冲无头件调整单
                        ocBRefundInItem.setIsGenMinusAdjust(IsGenMinusAdjustEnum.NO.integer());
                        //退货入库单ID
                        ocBRefundInItem.setOcBRefundInId(ocBRefundIn.getId());

                        // @20200717 增加吊牌价、颜色、尺寸、国标
                        // 颜色id
                        ocBRefundInItem.setPsCClrId(skuInfo.getColorId());
                        // 颜色编码
                        ocBRefundInItem.setPsCClrEcode(skuInfo.getColorCode());
                        // 颜色名称
                        ocBRefundInItem.setPsCClrEname(skuInfo.getColorName());
                        // 尺寸id
                        ocBRefundInItem.setPsCSizeId(skuInfo.getSizeId());
                        // 尺寸编码
                        ocBRefundInItem.setPsCSizeEcode(skuInfo.getSizeCode());
                        // 尺寸名称
                        ocBRefundInItem.setPsCSizeEname(skuInfo.getSizeName());
                        // 实收条码国标码
                        ocBRefundInItem.setGbcode(skuInfo.getEcode());
                        // 吊牌价
                        ocBRefundInItem.setPriceList(skuInfo.getPricelist());
                        OperateUserUtils.saveOperator(ocBRefundInItem, SystemUserResource.getRootUser());
                        //记录系统日志 暂不做处理
                        itemList.add(ocBRefundInItem);
                    }
                }
            }
            //更新ALLSKU
            ocBRefundIn.setAllSku(allSku);
            ocBRefundInMapper.updateById(ocBRefundIn);
            //退单明细保存
            ocBRefundInProductItemMapper.batchInsert(itemList);
            // 更新为处理成功
            this.update2RefundTask(refundInTask, OcBRefundInTaskStatusEnum.SUCCESS.getVal(), "", ocBRefundIn.getId()
                    , WmsUserResource.getWmsUser());
        } else {
            // 更新为处理失败
            this.update2RefundTask(refundInTask, OcBRefundInTaskStatusEnum.HANDLE_FAIL.getVal(),
                    OcBRefundInTaskStatusEnum.HANDLE_FAIL.getDesc() + "插入退货入库失败", null, WmsUserResource.getWmsUser());
        }
    }

    /**
     * 执行退货入库回传的定时任务捞取到的明细
     *
     * @param refundInTask taskInfo
     */
    public void handle(OcBRefundInTask refundInTask) {
        // msg body
        String message = refundInTask.getMsg();
        // 定义变量
        // 退单编号
        String returnOrderCode = "";
        // wms 单据编号 returnOrderId
        String wmsBillNo = "";
        JSONObject request = null;
        JSONArray orderLines = null;
        JSONObject jsonSender = null;
        // 退货单信息
        JSONObject returnOrder = null;
        // 仓库编码
        String warehouseCode = null;
        // 仓库的最终入库时间
        Date orderConfirmTime = null;
        try {
            JSONObject object = JSONObject.parseObject(message);
            request = object.getJSONObject("request");
            if (MapUtils.isEmpty(request)) {
                throw new NDSException("解析异常!request为空!");
            }
            // 退货单信息
            returnOrder = request.getJSONObject("returnOrder");
            if (MapUtils.isEmpty(returnOrder)) {
                throw new NDSException("解析异常!returnOrder为空!");
            }
            // 退货入库单编码 @20200715 这个字段发送端已经修改为billNo，不在是id
            returnOrderCode = returnOrder.getString("returnOrderCode");
            // 回传Wms 编号
            wmsBillNo = returnOrder.getString("returnOrderId");
            if (StringUtils.isBlank(wmsBillNo)) {
                throw new NDSException("解析异常!WMS编号为空!");
            }
            String orderType = returnOrder.getString("orderType");
            if (StringUtils.isBlank(orderType)) {
                throw new NDSException("解析异常!单据类型为空!");
            }
            // 发件人信息
            jsonSender = returnOrder.getJSONObject("senderInfo");
            if ("WMJRK".equalsIgnoreCase(orderType) && MapUtils.isEmpty(jsonSender)) {
                throw new NDSException("解析异常!无名件入库发件人信息为空!");
            }
            // 仓库编码
            warehouseCode = returnOrder.getString("warehouseCode");
            // 订单信息
            orderLines = request.getJSONArray("orderLines");
            if (CollectionUtils.isEmpty(orderLines)) {
                throw new NDSException("解析异常!明细为空!");
            }
            orderConfirmTime = returnOrder.getDate("orderConfirmTime");

        } catch (Exception e) {
            // 失败更新为解析失败
            this.update2RefundTask(refundInTask, OcBRefundInTaskStatusEnum.RESOLVE_FAIL.getVal()
                    , OcBRefundInTaskStatusEnum.RESOLVE_FAIL.getDesc() + ":" + e.getMessage(), null
                    , WmsUserResource.getWmsUser());
            return;
        }
        try {
            // 执行
            ApplicationContextHandle.getBean(this.getClass())
                    .executeMatch(refundInTask, returnOrder, jsonSender, returnOrderCode, warehouseCode, wmsBillNo
                            , orderConfirmTime, orderLines);
        } catch (Exception e) {
            // 失败更新为处理失败
            this.update2RefundTask(refundInTask, OcBRefundInTaskStatusEnum.HANDLE_FAIL.getVal()
                    , OcBRefundInTaskStatusEnum.HANDLE_FAIL.getDesc() + ":" + e.getMessage(), null
                    , WmsUserResource.getWmsUser());
        }
    }

    /**
     * 执行匹配
     *
     * @param refundInTask    taskInfo
     * @param returnOrder     退单信息
     * @param jsonSender      发件人信息
     * @param returnOrderCode 退单编号
     * @param warehouseCode   仓库code
     * @param wmsBillNo       wms编号
     * @param orderLines      回传明细
     */
    public void executeMatch(OcBRefundInTask refundInTask, JSONObject returnOrder, JSONObject jsonSender, String returnOrderCode
            , String warehouseCode, String wmsBillNo, Date orderConfirmTime, JSONArray orderLines) throws InterruptedException {

        // 假如是退货单号空的,直接走无头件
        if (StringUtils.isBlank(returnOrderCode)) {
            ApplicationContextHandle.getBean(this.getClass())
                    .wtj(refundInTask, returnOrder, jsonSender, warehouseCode, wmsBillNo, orderLines);
            return;
        }
        // 获取退单id
        Long returnId = returnOrderService.selectReturnOrderIdByBillNoFromEs(returnOrderCode);
        // 假如退单id为空,且开头不为TH,继续走无头件,为TH则报错
        if (Objects.isNull(returnId)) {
            if (StringUtils.trim(returnOrderCode).startsWith("TH")) {
                throw new NDSException("退货编号不存在!");
            } else {
                ApplicationContextHandle.getBean(this.getClass())
                        .wtj(refundInTask, returnOrder, jsonSender, warehouseCode, wmsBillNo, orderLines);
            }
        } else {
            // 锁单,然后去判断是否有refundInId(有值: 已被使用,无值 :未被使用)
            this.lockReturnGoonExecute(returnId, refundInTask, returnOrder, jsonSender, returnOrderCode, warehouseCode, wmsBillNo
                    , orderConfirmTime, orderLines);
        }
    }

    /**
     * 锁退单,然后继续执行
     */
    private void lockReturnGoonExecute(Long returnId, OcBRefundInTask refundInTask, JSONObject returnOrder, JSONObject jsonSender
            , String returnOrderCode, String warehouseCode, String wmsBillNo, Date orderConfirmTime, JSONArray orderLines) throws InterruptedException {
        // redisKey
        String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(returnId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBReturnOrder ocBReturnOrder = returnOrderMapper.selectByid(returnId);
                if (Objects.isNull(ocBReturnOrder)) {
                    throw new NDSException(" 查询的退单为空! returnId:" + returnId);
                }
                // 退单明细
                List<OcBReturnOrderRefund> ocBReturnOrderRefunds = refundMapper.selectByOcOrderId(returnId);
                if (ocBReturnOrderRefunds == null || ocBReturnOrderRefunds.size() == 0) {
                    throw new NDSException(" 查询的退单明细为空! returnId:" + returnId);
                }
                // @WMS单据编号
                String originWmsBillNo = ocBReturnOrder.getWmsBillNo();
                // 判断WMs单据编号是不存在（不存在就更新，存在不做操作）
                if (StringUtils.isEmpty(originWmsBillNo)) {
                    ocBReturnOrder.setWmsBillNo(wmsBillNo);
                }
                ocBReturnOrder.setInTime(orderConfirmTime);
                // 校验明细行
                this.validateOrderLines(ocBReturnOrderRefunds, orderLines);
                // 生成生成并保存退货入库单操作
                this.executeRefundIn(ocBReturnOrder, ocBReturnOrderRefunds, refundInTask, returnOrder, jsonSender
                        , returnOrderCode, warehouseCode, orderLines);
            }
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 执行生成并保存退货入库单操作
     */
    private void executeRefundIn(OcBReturnOrder ocBReturnOrder, List<OcBReturnOrderRefund> refunds
            , OcBRefundInTask refundInTask, JSONObject returnOrder, JSONObject jsonSender, String returnOrderCode
            , String warehouseCode, JSONArray orderLines) {
        // @20210120 生成退货入库单
        OcBRefundIn ocBRefundIn = this.buildOcBRefundIn(returnOrder, orderLines, jsonSender, returnOrderCode
                , warehouseCode);
        // 退货入库单关系
        RefundInRelation refundInRelation = this.buildRefundInRelation(ocBRefundIn, orderLines);
        // 保存操作 退货入库单
        ApplicationContextHandle.getBean(this.getClass()).saveRefundIn(refundInRelation, ocBReturnOrder, refunds, refundInTask);
    }

    /**
     * 保存退货入库单和退单
     *
     * @param refundInRelation      退货入库单关系model
     * @param ocBReturnOrder        退单
     * @param ocBReturnOrderRefunds 退单明细
     */
    @Transactional(rollbackFor = Throwable.class)
    public void saveRefundIn(RefundInRelation refundInRelation, OcBReturnOrder ocBReturnOrder
            , List<OcBReturnOrderRefund> ocBReturnOrderRefunds, OcBRefundInTask refundInTask) {
        // 用来保存
        if (refundInRelation == null) {
            // 更新为处理成功
            this.update2RefundTask(refundInTask, OcBRefundInTaskStatusEnum.SUCCESS.getVal(), "", null
                    , WmsUserResource.getWmsUser());
            return;
        }
        OcBRefundIn refundIn = refundInRelation.getRefundIn();
        boolean isMatch = ocBReturnOrder.getOcBRefundInId() != null;
        // 更新匹配赋值
        this.match(refundIn, ocBReturnOrder, refundInRelation.getItems()
                , ocBReturnOrderRefunds);
        ocBRefundInMapper.insert(refundIn);
        ocBRefundInProductItemMapper.batchInsert(refundInRelation.getItems());
        // 去更新退单
        OcBReturnOrder forUpdate = new OcBReturnOrder();
        forUpdate.setId(ocBReturnOrder.getId());
        forUpdate.setIsAnonymous(ocBReturnOrder.getIsAnonymous());
        if (!isMatch) {
            for (OcBReturnOrderRefund refund : ocBReturnOrderRefunds) {
                ocBReturnOrderRefundMapper.updateMatchNum(refund.getQtyMatch(), ocBReturnOrder.getId()
                        , refund.getId());
            }
            // 未匹配在去更新WMS编号
            forUpdate.setWmsBillNo(ocBReturnOrder.getWmsBillNo());
            forUpdate.setOcBRefundInId(ocBReturnOrder.getOcBRefundInId());
        }
        returnOrderMapper.updateById(ocBReturnOrder);
        //入库时间埋点
        nodeRecordService.insertByNode(ReturnOrderNodeEnum.OMS_STORE_IN_TIME,ocBReturnOrder.getInTime(),
                ocBReturnOrder.getId(),WmsUserResource.getWmsUser());
        // 更新为处理成功
        this.update2RefundTask(refundInTask, OcBRefundInTaskStatusEnum.SUCCESS.getVal(), "", refundIn.getId()
                , WmsUserResource.getWmsUser());
    }

    @Transactional(rollbackFor = Throwable.class)
    public void saveOcBReturnOrder(OcBReturnOrder ocBReturnOrder, OcBRefundInTask refundInTask, Long refundInId) {
        OcBReturnOrder forUpdate = new OcBReturnOrder();
        forUpdate.setId(ocBReturnOrder.getId());
        forUpdate.setWmsBillNo(ocBReturnOrder.getWmsBillNo());
        returnOrderMapper.updateById(ocBReturnOrder);
        // 更新为处理成功
        this.update2RefundTask(refundInTask, OcBRefundInTaskStatusEnum.SUCCESS.getVal(), "", refundInId
                , WmsUserResource.getWmsUser());
    }

    /**
     * 无头件
     *
     * @param refundInTask
     * @param returnOrder
     * @param jsonSender
     * @param warehouseCode
     * @param wmsBillNo
     * @param orderLines
     */
    public void wtj(OcBRefundInTask refundInTask, JSONObject returnOrder, JSONObject jsonSender, String warehouseCode
            , String wmsBillNo, JSONArray orderLines) {
        OcBRefundIn refundIn = this.findByWmsBillNo(wmsBillNo);
        RefundOrderToWmsBackService bean = ApplicationContextHandle.getBean(RefundOrderToWmsBackService.class);
        bean.returnOrderNotMatch(refundInTask, orderLines, jsonSender, returnOrder, refundIn, wmsBillNo, warehouseCode);
    }

    /**
     * 校验明细行
     *
     * @param ocBReturnOrderRefunds
     * @param orderLines
     */
    private void validateOrderLines(List<OcBReturnOrderRefund> ocBReturnOrderRefunds, JSONArray orderLines) {

        // 下文用,判断WMS回传的是否,在原退单中
        List<String> skuCodes = ocBReturnOrderRefunds.stream().map(OcBReturnOrderRefund::getPsCSkuEcode)
                .collect(Collectors.toList());

        /*
         * 1.整理数据，返回的数据有itemCode相同，但是inventoryType不同的数据。
         * 需要将itemCode相等的actualQty累加
         * 2.将次品信息捞出来，进行sku匹配和oid,return_id存进去
         */
        Map<String, BigDecimal> map = new HashMap<>(10);
        // 次品逻辑注销，暂时无次品逻辑
        for (int i = 0; i < orderLines.size(); i++) {
            JSONObject orderLine = orderLines.getJSONObject(i).getJSONObject("orderLine");
            if (orderLine != null) {
                String itemCode = orderLine.getString("itemCode");
                if (StringUtils.isBlank(itemCode)) {
                    throw new NDSException("itemCode为空!处理失败!");
                }
                if (!skuCodes.contains(itemCode)) {
                    throw new NDSException("itemCode在原退单中不存在!处理失败!");
                }
                // 入库数量（包含次品和正品）
                BigDecimal qtyIn = BigDecimal.ZERO;
                for (int j = 0; j < orderLines.size(); j++) {
                    JSONObject orderLineJ = orderLines.getJSONObject(j).getJSONObject("orderLine");
                    if (orderLineJ != null) {
                        String itemCodeJ = orderLineJ.getString("itemCode");
                        if (itemCode.equalsIgnoreCase(itemCodeJ)) {
                            BigDecimal actualQty = orderLineJ.getBigDecimal("actualQty");
                            if (actualQty != null) {
                                qtyIn = qtyIn.add(actualQty);
                            }
                        }
                    }
                }
                //将整理后的qtyIn放入
                for (int j = 0; j < ocBReturnOrderRefunds.size(); j++) {
                    if (itemCode.equalsIgnoreCase(ocBReturnOrderRefunds.get(j).getPsCSkuEcode())) {
                        if (qtyIn.compareTo(ocBReturnOrderRefunds.get(j).getQtyRefund()) > 0) {
                            throw new NDSException("异常: SKU[" + itemCode + "]WMS回传入库数量和申请数量不等!");
                        }
                        ocBReturnOrderRefunds.get(j).setQtyIn(qtyIn);
                        // 次品库存
                        // @20200723 没有次品逻辑，所以这里也就不考虑数量合并的问题
                        if (OcBOrderConst.WMS_INVENTORY_TYPE_CC.equalsIgnoreCase(orderLine.getString("inventoryType"))) {
                            ocBReturnOrderRefunds.get(j).setPriceSettle(orderLine.getBigDecimal("actualQty"));// 次品数量
                        } else {
                            ocBReturnOrderRefunds.get(j).setPriceSettle(BigDecimal.ZERO);// 次品数量 -> 发送WMS次品数用的是这个字段，换货单转单这个字段赋值的是：结算金额，所以这里要清0
                        }
                    }
                }
                // 取所有次品
                // @20200717 去除hardCode
                if (OcBOrderConst.WMS_INVENTORY_TYPE_CC.equalsIgnoreCase(orderLine.getString("inventoryType"))) {
                    map.put(itemCode, orderLine.getBigDecimal("actualQty"));
                }
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void qimenInsertRefundIn(JSONObject object) {
        JSONObject request = object.getJSONObject("request");
       /* {
          "request": {
            "sell_return_record": {
              "order_return_goods": [{
                "shop_price": "dsd ",
                "goods_number": "fe",
                "goods_price": "80",
                "market_price": "dsd ",
                "colorCode": "01",
                "sizeCode": "L",
                "goodsCode": "A001",
                "sku": "dstrewqd "
              }],
              "return_type": "1",
              "return_ck_code": "HZ01",
              "return_kw_code": "A001",
              "order_sn": "A123456"
            },
            "extendProps": {
              "cp_c_logistics_id": "STO",
              "logistic_number": "43231313141139",
              "receiver_mobile": "17612187155",
              "storeCode": "hz0001",
              "storeName": "杭州延安路旗舰店",
              "orgName": "上海伯俊",
              "orgCode": "0001",
              "supplier_code": "0001",
              "supplier_name": "上海伯俊"
            }
          },
          "method": "3e9nl9rhrg.burgeon.taobao.pos.order.return.add",
          "customerid": "SEMIR_BJ_TEST"
        }*/


        //退单信息
        JSONObject sellReturnRecord = request.getJSONObject("sell_return_record");
        //退单类型（1退货2截回3拒收）
        //String returnType = sellReturnRecord.getString("return_type");
        //wms出库通知单
        String orderSn = sellReturnRecord.getString("order_sn");

        OcBRefundIn ocBRefundIn = new OcBRefundIn();
        // 获取实体仓逻辑仓信息
        getO2OPhyWarehouse(orderSn, ocBRefundIn);

        JSONArray orderReturnGoods = sellReturnRecord.getJSONArray("order_return_goods");

        //扩展字段
        String extendPropsStr = request.getString("extendProps");
        JSONObject extendProps = JSONObject.parseObject(extendPropsStr);
        String sourceCode = null;
        String allSku = "";

        //退货批次
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("id", 3);
        OcBRefundBatch refundBatch = ocBRefundBatchMapper.selectOne(wrapper);

        //这里对批次的使用不严格，可能存在批次不存在的情况，为空的话则默认一个
        if (Objects.isNull(refundBatch)) {
            refundBatch = new OcBRefundBatch();
            // 后面会用到的这两个值
            refundBatch.setBatchNo("yyyyMMddHHmmssSSS0001");
            refundBatch.setId(-1L);
        }

        //订单信息抬头  组装数据
        /*JSONObject orderLine = orderLines.getJSONObject(0).getJSONObject("orderLine");
        if(null != orderLine){
            sourceCode = orderLine.getString("sourceOrderCode");
        }*/
        //收货人姓名
        //ocBRefundIn.setReceiverName(jsonSender.getString("name"));
        //收货人手机号
        ocBRefundIn.setReceiverMobile(extendProps.getString("receiver_mobile") == null ? "" : extendProps.getString("receiver_mobile"));
        //发件地址
        //ocBRefundIn.setReceiverAddress(jsonSender.getString("detailAddress"));

        //批次编号
        ocBRefundIn.setBatchNo(refundBatch.getBatchNo());
        //批次编号ID
        ocBRefundIn.setOcBRefundBatchId(refundBatch.getId());
        //商品条码
        //ocBRefundIn.setAllSku();
        //下单用户编号
        //ocBRefundIn.setUserId(893L);
        //原平台单号
        ocBRefundIn.setSourceCode(sourceCode);

        //备注
        ocBRefundIn.setRemark("奇门O2O退单");
        //创建人
        ocBRefundIn.setOwnerid(893L);
        //创建人姓名
        ocBRefundIn.setOwnerename("系统管理员");
        //创建人用户名
        ocBRefundIn.setOwnername("root");
        //物流单号
        ocBRefundIn.setLogisticNumber(extendProps.getString("logistic_number"));
        //物流公司
        ocBRefundIn.setCpCLogisticsEname(extendProps.getString("receiver_mobile"));

        //门店编码
        ocBRefundIn.setStoreCode(extendProps.getString("storeCode"));

        //门店名称
        ocBRefundIn.setStoreName(extendProps.getString("storeName"));

        //门店id
        //ocBRefundIn.setStoreId(extendProps.getLong("storeCode"));

        //门店档案的结算组织名称
        ocBRefundIn.setSettleOrgName(extendProps.getString("orgName"));

        //门店档案的结算组织编码
        ocBRefundIn.setSettleOrgCode(extendProps.getString("orgCode"));

        //门店档案的结算供应商编码
        ocBRefundIn.setSettleSupplierCode(extendProps.getString("supplier_code"));

        //门店档案的结算供应商名称
        ocBRefundIn.setSettleSupplierName(extendProps.getString("supplier_name"));

        //是否可用
        ocBRefundIn.setIsactive("Y");
        //创建时间
        ocBRefundIn.setCreationdate(new Date());
        //修改时间
        ocBRefundIn.setModifieddate(new Date());
        //入库状态
        ocBRefundIn.setInStatus(2);
        //匹配状态
        ocBRefundIn.setMatchStatus(OcBOrderConst.REFUND_IN_MATCHSTATUS_UN);
        //特殊处理类型
        ocBRefundIn.setSpecialType(OcBOrderConst.ORDER_SPECIAL_TYPE_NORMAL);
        //入库单编
        ocBRefundIn.setId(ModelUtil.getSequence("OC_B_REFUND_IN"));
        //WMS单据编号
        ocBRefundIn.setWmsBillNo(orderSn);
        //是否关闭匹配
        ocBRefundIn.setIsOffMatch(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
        if (ocBRefundInMapper.insert(ocBRefundIn) > 0) {
            /**退货入库单-明细表**/

            List<OcBRefundInProductItem> itemList = new ArrayList<>();
            List<SkuInfoListRequest> proList = Lists.newArrayList();
            for (int i = 0; i < orderReturnGoods.size(); i++) {
                JSONObject orderReturnGood = orderReturnGoods.getJSONObject(i);

                //商品款号
                String goodsCode = orderReturnGood.getString("goodsCode");
                //颜色代码
                String colorCode = orderReturnGood.getString("colorCode");
                //尺码代码
                String sizeCode = orderReturnGood.getString("sizeCode");

                SkuInfoListRequest skuInfoListRequest = new SkuInfoListRequest();
                skuInfoListRequest.setColorCode(colorCode);
                skuInfoListRequest.setProEcode(goodsCode);
                skuInfoListRequest.setSizeCode(sizeCode);
                proList.add(skuInfoListRequest);

                //按照数量QTY进行拆明细
                int qty = orderReturnGood.getIntValue("goods_number");

                String key = goodsCode + "_" + colorCode + "_" + sizeCode;

                for (int i1 = 0; i1 < qty; i1++) {
                    OcBRefundInProductItem ocBRefundInItem = new OcBRefundInProductItem();

                    //创建人
                    ocBRefundInItem.setOwnerid(893L);
                    //创建人姓名
                    ocBRefundInItem.setOwnerename("系统管理员");
                    //创建人用户名
                    ocBRefundInItem.setOwnername("root");
                    //条码 TODO
                    ocBRefundInItem.setPsCSkuEcode(key);

                    //是否可用
                    ocBRefundInItem.setIsactive("Y");
                    //创建时间
                    ocBRefundInItem.setCreationdate(new Date());
                    //修改时间
                    ocBRefundInItem.setModifieddate(new Date());
                    //商品标记
                    //ocBRefundInItem.setProductMark(orderObject.getString("inventoryType"));
                    //是否无原单条码
                    ocBRefundInItem.setIsWithoutOrig(IsWithoutOrigEnum.IS_WITHOUT_ORIG.getVal());
                    //数量
                    ocBRefundInItem.setQty(new BigDecimal(1));
                    //退换货单编号、订单编号(PRD要求为空)
                    //明细编号
                    ocBRefundInItem.setId(ModelUtil.getSequence("OC_B_REFUND_IN_PRODUCT_ITEM"));
                    //是否匹配
                    ocBRefundInItem.setIsMatch(OcBOrderConst.REFUND_IN_MATCHSTATUS_UN);
                    //是否生成调整单
                    ocBRefundInItem.setIsGenAdjust(0);
                    //是否生成入库单
                    ocBRefundInItem.setIsGenInOrder(IsGenInEnum.NO.integer());
                    //是否生成错发调整单
                    ocBRefundInItem.setIsGenWroAdjust(IsGenWroAdjustEnum.NO.integer());
                    //是否生成冲无头件调整单
                    ocBRefundInItem.setIsGenMinusAdjust(IsGenMinusAdjustEnum.NO.integer());
                    //退货入库单ID
                    ocBRefundInItem.setOcBRefundInId(ocBRefundIn.getId());

                    //记录系统日志 暂不做处理
                    itemList.add(ocBRefundInItem);
                }

            }


            //获取商品信息
            Map<String, List<ProAndSkuResult>> posProMap = getShopSku(proList);
            itemList.forEach(ocBRefundInItem -> {
                String key = ocBRefundInItem.getPsCSkuEcode();
                if (!posProMap.containsKey(key)) {
                    throw new NDSException("商品信息不存在" + key);
                }
                List<ProAndSkuResult> proAndSkuResults = posProMap.get(key);
                if (CollectionUtils.isEmpty(proAndSkuResults)) {
                    throw new NDSException("商品信息不存在" + key);
                }
                ProAndSkuResult proAndSkuResult = proAndSkuResults.get(0);

                //商品编号 skuInfo.getEcode() "10010"
                ocBRefundInItem.setPsCProEcode(proAndSkuResult.getProEcode());
                //商品名称 skuInfo.getName() "SM爽肤水"
                ocBRefundInItem.setPsCProEname(proAndSkuResult.getProEname());

                ocBRefundInItem.setPsCSkuEcode(proAndSkuResult.getSkuEcode());
                // 增加SKUID等信息
                ocBRefundInItem.setPsCSkuId(proAndSkuResult.getSkuId());

                //国际码 skuInfo.getBarcode69() "10010001S"
                //ocBRefundInItem.setGbcode(proAndSkuResult.getBarcode69());

                ocBRefundInItem.setPsCClrId(proAndSkuResult.getColorId());
                // 颜色编码
                ocBRefundInItem.setPsCSizeEcode(proAndSkuResult.getColorEcode());
                // 颜色名称
                ocBRefundInItem.setPsCSizeEname(proAndSkuResult.getColorEname());
                // 尺寸id
                ocBRefundInItem.setPsCSizeId(proAndSkuResult.getSizeId());
                // 尺寸编码
                ocBRefundInItem.setPsCSizeEcode(proAndSkuResult.getSizeEcode());
                // 尺寸名称
                ocBRefundInItem.setPsCSizeEname(proAndSkuResult.getSizeEname());
                // 实收条码国标码
                ocBRefundInItem.setGbcode(proAndSkuResult.getSkuEcode());
                // 吊牌价
                //ocBRefundInItem.setReserveDecimal10(proAndSkuResult.getPricelist());
            });
            Map<String, Long> mapSku = itemList.stream().collect(Collectors.groupingBy(OcBRefundInProductItem::getPsCSkuEcode, Collectors.counting()));
            for (Map.Entry<String, Long> stringLongEntry : mapSku.entrySet()) {
                //拼接所有SKU
                allSku = allSku + stringLongEntry.getKey() + "(" + stringLongEntry.getValue() + "),";
            }

            //更新ALLSKU
            ocBRefundIn.setAllSku(allSku);
            ocBRefundInMapper.updateById(ocBRefundIn);
            //退单明细保存
            ocBRefundInProductItemMapper.batchInsert(itemList);
        }
    }

    public void checkParseMsg(List<OcBRefundInTask> refundInTaskList, Map<Long, ReturnOrderConfirmDTO> returnOrderMap) {
        List<OcBRefundInTask> errList = Lists.newArrayList();
        for (OcBRefundInTask refundInTask : refundInTaskList) {
            try {
                AssertUtil.assertException(StringUtils.isBlank(refundInTask.getMsg()), "解析异常！原始报文为空");

                ReturnOrderConfirmDTO returnOrderConfirmDTO = JSON.parseObject(refundInTask.getMsg(), ReturnOrderConfirmDTO.class);
                // 请求体
                ReturnOrderConfirmDTO.Request request = returnOrderConfirmDTO.getRequest();

                AssertUtil.assertException(Objects.isNull(request), "解析异常!request为空!");

                // 退货单信息
                ReturnOrderConfirmDTO.ReturnOrder returnOrder = request.getReturnOrder();
                AssertUtil.assertException(Objects.isNull(returnOrder), "解析异常!returnOrder为空!");

                // 回传Wms编号
                String wmsBillNo = returnOrder.getReturnOrderId();
                AssertUtil.assertException(StringUtils.isBlank(wmsBillNo), "解析异常!WMS编号为空!");

                // 单据类型
                String orderType = returnOrder.getOrderType();
                AssertUtil.assertException(StringUtils.isBlank(orderType), "解析异常!单据类型为空!");

                // 物流公司编号
                String logisticsCode = returnOrder.getLogisticsCode();
                AssertUtil.assertException(StringUtils.isBlank(logisticsCode), "解析异常!物流公司编码为空!");

                // 发件人信息
                ReturnOrderConfirmDTO.SenderInfo senderInfo = returnOrder.getSenderInfo();
                boolean error = Return2WmsBillEnum.NO_NAME.val().equalsIgnoreCase(orderType) && Objects.isNull(senderInfo);
                AssertUtil.assertException(error, "解析异常!无名件入库发件人信息为空!");

                // 订单明细信息
                List<ReturnOrderConfirmDTO.OrderLines> orderLines = request.getOrderLines();
                AssertUtil.assertException(CollectionUtils.isEmpty(orderLines), "解析异常!明细为空!");

                returnOrderMap.put(refundInTask.getId(), returnOrderConfirmDTO);
            } catch (Exception e) {
                errList.add(buildRefundTask(refundInTask, OcBRefundInStatusEnum.RefundInTaskStatusEnum.FAIL.getCode(), e.getMessage(), null));
            }
        }
        if (CollectionUtils.isNotEmpty(errList)) {
            refundInTaskService.updateBatchById(errList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 returnOrderWmsInResultBack(List<OcBRefundInTask> refundInTaskList) {
        if (CollectionUtils.isEmpty(refundInTaskList)) {
            return ValueHolderV14Utils.getFailValueHolder("OMS接收到的数据异常,请检查");
        }
        Map<Long, ReturnOrderConfirmDTO> returnOrderMap = Maps.newHashMap();
        checkParseMsg(refundInTaskList, returnOrderMap);

        Map<Long, OcBRefundInTask> refundInTaskMap = refundInTaskList.stream().collect(Collectors.toMap(OcBRefundInTask::getId, Function.identity()));

        List<String> billNoList = returnOrderMap.values().stream().map(model -> model.getRequest().getReturnOrder().getReturnOrderCode()).distinct().collect(Collectors.toList());
        List<OcBReturnOrder> returnOrderList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(billNoList)) {
            returnOrderList = returnOrderMapper.selectList(new QueryWrapper<OcBReturnOrder>().lambda()
                    .in(OcBReturnOrder::getBillNo, billNoList)
                    .eq(OcBReturnOrder::getIsactive, IsActiveEnum.Y.getKey()));
        }

        Map<String, List<OcBReturnOrder>> bReturnOrderMap = returnOrderList.stream().collect(Collectors.groupingBy(OcBReturnOrder::getBillNo));
        List<Long> returnOrderIdList = returnOrderList.stream().map(OcBReturnOrder::getId).distinct().collect(Collectors.toList());
        List<OcBReturnOrderRefund> allRefundList = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(returnOrderIdList);
        Map<Long, List<OcBReturnOrderRefund>> refungGroupedMap = allRefundList.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getOcBReturnOrderId));
        //批量查询实体仓
        List<Long> warehouseIds = returnOrderList.stream()
                .filter(o -> o != null).map(OcBReturnOrder::getCpCPhyWarehouseInId).distinct().collect(Collectors.toList());
        Map<Long, CpCPhyWarehouse> phyWarehouseMap = cpRpcService.rpcQueryCpCPhyWareHouses(warehouseIds);
        List<OcBReturnOrderLog> returnOrderLogList = new ArrayList<>(billNoList.size());
        List<OcBRefundInLog> refundInLogList = new ArrayList<>(billNoList.size());
        List<OcBRefundInTask> refundInTasks = Lists.newArrayList();

        returnOrderMap.forEach((taskId, value) -> {
            String returnNo = value.getRequest().getReturnOrder().getReturnOrderCode();
            try {
                List<OcBReturnOrder> returnList = bReturnOrderMap.get(returnNo);
                if (CollectionUtils.isEmpty(returnList)) {
                    refundInTasks.add(buildRefundTask(refundInTaskMap.get(taskId), OcBRefundInStatusEnum.RefundInTaskStatusEnum.FAIL.getCode(), "未查询到有效退单数据", null));
                    return;
                }
                if (returnList.size() > 1) {
                    refundInTasks.add(buildRefundTask(refundInTaskMap.get(taskId), OcBRefundInStatusEnum.RefundInTaskStatusEnum.FAIL.getCode(), "查询到多条数据", null));
                    return;
                }
                OcBReturnOrder returnOrder = returnList.get(0);
                if (returnOrder.getExistRefundInId() != null) {
                    refundInTasks.add(buildRefundTask(refundInTaskMap.get(taskId), OcBRefundInStatusEnum.RefundInTaskStatusEnum.FAIL.getCode(), "已收到过入库结果回传,无需重复回传", null));
                    return;
                }
                List<OcBReturnOrderRefund> refundList = refungGroupedMap.get(returnOrder.getId());
                if (CollectionUtils.isEmpty(refundList)) {
                    refundInTasks.add(buildRefundTask(refundInTaskMap.get(taskId), OcBRefundInStatusEnum.RefundInTaskStatusEnum.FAIL.getCode(), "对应退单未查询到明细数据", null));
                    return;
                }
                OcBRefundIn ocBRefundIn = this.buildOcBRefundIn(phyWarehouseMap, returnOrder);
                String allSku = "";
                /**退货入库单-明细表**/
                List<ReturnOrderConfirmDTO.OrderLines> resultModelItemList = value.getRequest().getOrderLines();
                List<OcBRefundInProductItem> itemList = new ArrayList<>(resultModelItemList.size());

                Map<String, List<OcBReturnOrderRefund>> refundGroupBySku = refundList.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getPsCSkuEcode));
                for (ReturnOrderConfirmDTO.OrderLines orderItem : resultModelItemList) {
                    List<OcBReturnOrderRefund> matchRefundList = refundGroupBySku.get(orderItem.getOrderLine().getItemCode());
                    if (CollectionUtils.isEmpty(matchRefundList)) {
                        continue;
                    }
                    //生成入库明细时只从退单明细拿sku信息  所以同sku可任取一条
                    OcBReturnOrderRefund refund = matchRefundList.get(0);
                    OcBRefundInProductItem ocBRefundInItem = this.buildOcBRefundInProductItem(ocBRefundIn, orderItem, refund);
                    itemList.add(ocBRefundInItem);
                }
                if (CollectionUtils.isEmpty(itemList)) {
                    refundInTasks.add(buildRefundTask(refundInTaskMap.get(taskId), OcBRefundInStatusEnum.RefundInTaskStatusEnum.FAIL.getCode(), "明细未匹配到对应条码", null));
                    return;
                }
                Map<String, Long> mapSku = itemList.stream().collect(Collectors.groupingBy(OcBRefundInProductItem::getPsCSkuEcode, Collectors.counting()));
                for (Map.Entry<String, Long> stringLongEntry : mapSku.entrySet()) {
                    //拼接所有SKU
                    allSku = allSku + stringLongEntry.getKey() + "(" + stringLongEntry.getValue() + "),";
                }
                ocBRefundIn.setAllSku(allSku);
                ocBRefundInMapper.insert(ocBRefundIn);
                //退单明细保存
                ocBRefundInProductItemMapper.batchInsert(itemList);
                OcBReturnOrder update = new OcBReturnOrder();
                update.setId(returnOrder.getId());
                //记录生成的退货入库单id 回传时防止重复生成  产品确认 wing会一次回传所有明细结果
                update.setExistRefundInId(ocBRefundIn.getId());
                returnOrderMapper.updateById(update);
                OcBReturnOrderLog returnOrderLog = returnOrderLogService.buildReturnOrderLog(returnOrder.getId(), "WMS入库回传", String.format("接收WMS入库成功，生成退货入库单ID:%d", ocBRefundIn.getId()), SystemUserResource.getRootUser());
                if (returnOrderLog != null) {
                    returnOrderLogList.add(returnOrderLog);
                }
                OcBRefundInLog refundInLog = ocBRefundInLogService.buildLogData(ocBRefundIn.getId(), "新增入库单", "接收WMS回传WMS入库结果成功", SystemUserResource.getRootUser());
                if (refundInLog != null) {
                    refundInLogList.add(refundInLog);
                }
                refundInTasks.add(buildRefundTask(refundInTaskMap.get(taskId), OcBRefundInStatusEnum.RefundInTaskStatusEnum.SUCCESS.getCode(), null, ocBRefundIn));
            } catch (Exception e) {
                log.error(LogUtil.format("wingToWmsInResultBack 数据处理发生异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                refundInTasks.add(buildRefundTask(refundInTaskMap.get(taskId), OcBRefundInStatusEnum.RefundInTaskStatusEnum.FAIL.getCode(), "处理数据发生异常" + e.getMessage(), null));
                return;
            }
        });
        refundInTaskService.updateBatchById(refundInTasks);
        try {
            commonTaskExecutor.submit(() -> {
                returnOrderLogService.batchInsertLog(returnOrderLogList);
                ocBRefundInLogService.batchInsertLog(refundInLogList);
            });
        } catch (Exception e) {
            log.error(LogUtil.format("退货入库记录日志异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
        return new ValueHolderV14(ResultCode.SUCCESS, "WMS入库回传处理完成");
    }

    /**
     * 中间表报文
     *
     * @param task        中间表实体
     * @param status      状态
     * @param reason      失败原因
     * @param ocBRefundIn 退货入库单
     */
    public OcBRefundInTask buildRefundTask(OcBRefundInTask task, int status, String reason, OcBRefundIn ocBRefundIn) {
        OcBRefundInTask update = new OcBRefundInTask();
        update.setId(task.getId());
        update.setBillStatus(status);
        // 转换次数
        if (OcBRefundInStatusEnum.RefundInTaskStatusEnum.SUCCESS.getCode().equals(status)) {
            update.setRefundInId(ocBRefundIn.getId());
            update.setFailedReason(StringUtils.EMPTY);
            update.setFailedCount(0);
        } else {
            update.setFailedReason(Objects.isNull(reason) ? "服务异常" : reason);
            update.setFailedCount(Optional.ofNullable(task.getFailedCount()).orElse(0) + 1);
        }
        BaseModelUtil.makeBaseModifyField(update, SystemUserResource.getRootUser());
        return update;
    }

    /**
     * 构建退货入库单
     * @param phyWarehouseMap
     * @param returnOrder
     * @return
     */
    private OcBRefundIn buildOcBRefundIn(Map<Long, CpCPhyWarehouse> phyWarehouseMap, OcBReturnOrder returnOrder) {
        OcBRefundIn ocBRefundIn = new OcBRefundIn();
        //入库单ID
        ocBRefundIn.setId(ModelUtil.getSequence(OcCommonConstant.OC_B_REFUND_IN));
        //获取实体仓
        Long wareId = returnOrder.getCpCPhyWarehouseInId();
        if (wareId != null) {
//                CpCPhyWarehouse warehouse = cpRpcService.selectPhyWarehouseById(wareId);
            CpCPhyWarehouse warehouse = phyWarehouseMap.get(wareId);
            if (warehouse != null) {
                ocBRefundIn.setCpCPhyWarehouseId(warehouse.getId() != null ? warehouse.getId() : warehouse.getCpCWarehouseId());
                ocBRefundIn.setCpCPhyWarehouseEcode(warehouse.getEcode() != null ? warehouse.getEcode() : warehouse.getCpCWarehouseEcode());
                ocBRefundIn.setCpCPhyWarehouseEname(warehouse.getEname() != null ? warehouse.getEname() : warehouse.getCpCWarehouseEname());
            }
            Long storeId = returnOrder.getCpCStoreId();
            String storeCode = returnOrder.getCpCStoreEcode();
            String storeName = returnOrder.getCpCStoreEname();
            if (storeId == null || StringUtils.isEmpty(storeCode) || StringUtils.isEmpty(storeName)) {
                StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
                storeInfoQueryRequest.setPhyId(wareId);
                //TODO：批量查询
                HashMap<Long, List<com.jackrain.nea.cp.result.CpCStore>> storeInfoByPhyId = basicCpQueryService.getStoreInfoByPhyId(storeInfoQueryRequest);
                if (MapUtils.isNotEmpty(storeInfoByPhyId)) {
                    List<com.jackrain.nea.cp.result.CpCStore> cpCStores = storeInfoByPhyId.get(wareId);
                    if (CollectionUtils.isNotEmpty(cpCStores)) {
                        Optional<com.jackrain.nea.cp.result.CpCStore> returnStore = cpCStores.stream().filter(x -> DrpStoreTypeEnum.TYPE_27.getValue().equals(x.getStoretype())).findFirst();
                        if (StringUtils.equals(warehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE_STORE_02)) {
                            returnStore = cpCStores.stream().findFirst();
                        }
                        if (returnStore.isPresent()) {
                            com.jackrain.nea.cp.result.CpCStore cpCStore = returnStore.get();
                            ocBRefundIn.setInStoreId(cpCStore.getId());
                            ocBRefundIn.setInStoreEcode(cpCStore.getEcode());
                            ocBRefundIn.setInStoreEname(cpCStore.getEname());
                        }
                    }
                } else {
                    ocBRefundInLogService.addLog(ocBRefundIn.getId(), "新增入库单", "数据异常:当前退货入库单逻辑仓信息为空", SystemUserResource.getRootUser());
                }
            } else {
                ocBRefundIn.setInStoreId(storeId);
                ocBRefundIn.setInStoreEcode(storeCode);
                ocBRefundIn.setInStoreEname(storeName);
            }
        }
        //收货人姓名
        ocBRefundIn.setReceiverName(returnOrder.getReceiveName());
        //收货人手机号
        ocBRefundIn.setReceiverMobile(returnOrder.getReceiveMobile());
        //发件地址
        ocBRefundIn.setReceiverAddress(returnOrder.getReceiveAddress());
        //批次编号
        ocBRefundIn.setBatchNo(DateUtil.format(new Date()));
        //批次编号ID
        ocBRefundIn.setOcBRefundBatchId(-1L);
        //原平台单号
        ocBRefundIn.setSourceCode(returnOrder.getTid());
        //备注
        ocBRefundIn.setRemark("WMS入库");
        BaseModelUtil.makeBaseCreateField(ocBRefundIn, SystemUserResource.getRootUser());
        //物流单号
        ocBRefundIn.setLogisticNumber(returnOrder.getLogisticsCode());
        //物流公司
        ocBRefundIn.setCpCLogisticsEname(returnOrder.getCpCLogisticsEname());
        //门店编码
        ocBRefundIn.setStoreCode(returnOrder.getStoreCode());
        //门店名称
        ocBRefundIn.setStoreName(returnOrder.getStoreName());
        //门店id
        ocBRefundIn.setStoreId(returnOrder.getStoreId());
        //门店档案的结算组织名称
        ocBRefundIn.setSettleOrgName(returnOrder.getSettleOrgName());
        //门店档案的结算组织编码
        ocBRefundIn.setSettleOrgCode(returnOrder.getSettleOrgCode());
        //门店档案的结算供应商编码
        ocBRefundIn.setSettleSupplierCode(returnOrder.getSettleSupplierCode());
        //门店档案的结算供应商名称
        ocBRefundIn.setSettleSupplierName(returnOrder.getSettleSupplierName());
        //是否可用
        ocBRefundIn.setIsactive(IsActiveEnum.Y.getKey());
        //入库状态
        ocBRefundIn.setInStatus(2);
        //匹配状态
        ocBRefundIn.setMatchStatus(OcBOrderConst.REFUND_IN_MATCHSTATUS_UN);
        //特殊处理类型
        ocBRefundIn.setSpecialType(OcBOrderConst.ORDER_SPECIAL_TYPE_NORMAL);
        //WMS单据编号
        ocBRefundIn.setWmsBillNo(returnOrder.getWmsBillNo());
        //是否关闭匹配
        ocBRefundIn.setIsOffMatch(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
        return ocBRefundIn;
    }

    /**
     * 构建退货入库明细
     * @param ocBRefundIn
     * @param orderItem
     * @param refund
     * @return
     */
    private OcBRefundInProductItem buildOcBRefundInProductItem(OcBRefundIn ocBRefundIn, ReturnOrderConfirmDTO.OrderLines orderItem, OcBReturnOrderRefund refund) {
        OcBRefundInProductItem ocBRefundInItem = new OcBRefundInProductItem();
        BaseModelUtil.makeBaseCreateField(ocBRefundInItem, SystemUserResource.getRootUser());
        //是否可用
        ocBRefundInItem.setIsactive(IsActiveEnum.Y.getKey());
        //商品标记
        ocBRefundInItem.setProductMark(StringUtils.isBlank(orderItem.getOrderLine().getInventoryType())
                || OcBOrderConst.WMS_INVENTORY_TYPE_ZP.equals(orderItem.getOrderLine().getInventoryType()) ? "1" : "0");
        //是否无原单条码
        ocBRefundInItem.setIsWithoutOrig(IsWithoutOrigEnum.IS_WITHOUT_ORIG.getVal());
        //数量
        ocBRefundInItem.setQty(orderItem.getOrderLine().getActualQty());
        //退换货单编号、订单编号(PRD要求为空)
        //明细编号
        ocBRefundInItem.setId(ModelUtil.getSequence(OcCommonConstant.OC_B_REFUND_IN_PRODUCT_ITEM));
        //是否匹配
        ocBRefundInItem.setIsMatch(OcBOrderConst.REFUND_IN_MATCHSTATUS_UN);
        //是否生成调整单
        ocBRefundInItem.setIsGenAdjust(0);
        //是否生成入库单
        ocBRefundInItem.setIsGenInOrder(IsGenInEnum.NO.integer());
        //是否生成错发调整单
        ocBRefundInItem.setIsGenWroAdjust(IsGenWroAdjustEnum.NO.integer());
        //是否生成冲无头件调整单
        ocBRefundInItem.setIsGenMinusAdjust(IsGenMinusAdjustEnum.NO.integer());
        //退货入库单ID
        ocBRefundInItem.setOcBRefundInId(ocBRefundIn.getId());
        //商品编号 skuInfo.getEcode() "10010"
        ocBRefundInItem.setPsCProEcode(refund.getPsCProEcode());
        //商品名称 skuInfo.getName() "SM爽肤水"
        ocBRefundInItem.setPsCProEname(refund.getPsCProEname());

        ocBRefundInItem.setPsCSkuEcode(refund.getPsCSkuEcode());
        // 增加SKUID等信息
        ocBRefundInItem.setPsCSkuId(refund.getPsCSkuId());
        ocBRefundInItem.setPsCClrId(refund.getPsCClrId());
        // 颜色编码
        ocBRefundInItem.setPsCClrEname(refund.getPsCClrEname());
        // 颜色名称
        ocBRefundInItem.setPsCClrEcode(refund.getPsCClrEcode());
        // 尺寸id
        ocBRefundInItem.setPsCSizeId(refund.getPsCSizeId());
        // 尺寸编码
        ocBRefundInItem.setPsCSizeEcode(refund.getPsCSizeEcode());
        // 尺寸名称
        ocBRefundInItem.setPsCSizeEname(refund.getPsCSizeEname());
        // 实收条码国标码
        ocBRefundInItem.setGbcode(refund.getBarcode());
        return ocBRefundInItem;
    }

//    @Transactional(rollbackFor = Exception.class)
//    public ValueHolderV14 wmsInResultBack(String msg) {
//        List<ReturnOrderWMSInResultModel> resultModelList = JSONObject.parseArray(msg, ReturnOrderWMSInResultModel.class);
//        List<ValueHolderV14> failedList = new ArrayList<>();
//
//        for (ReturnOrderWMSInResultModel resultModel : resultModelList) {
//            String returnNo = resultModel.getReturnNo();
//            LambdaQueryWrapper<OcBReturnOrder> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(OcBReturnOrder::getBillNo, returnNo);
//            queryWrapper.eq(OcBReturnOrder::getIsactive, IsActiveEnum.Y.getKey());
//            List<OcBReturnOrder> ocBReturnOrders = returnOrderMapper.selectList(queryWrapper);
//            if (CollectionUtils.isEmpty(ocBReturnOrders)) {
//                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "未查询到有效退单数据", returnNo));
//                continue;
//            }
//            OcBReturnOrder returnOrder = ocBReturnOrders.get(0);
//            if (returnOrder.getExistRefundInId() != null) {
//                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "已收到过入库结果回传,无需重复回传", returnNo));
//                continue;
//            }
//            //退货批次
//            QueryWrapper wrapper = new QueryWrapper();
//            wrapper.eq("id", 3);
//            OcBRefundBatch refundBatch = ocBRefundBatchMapper.selectOne(wrapper);
//
//            //这里对批次的使用不严格，可能存在批次不存在的情况，为空的话则默认一个
//            if (Objects.isNull(refundBatch)) {
//                refundBatch = new OcBRefundBatch();
//                // 后面会用到的这两个值
//                refundBatch.setBatchNo("yyyyMMddHHmmssSSS0001");
//                refundBatch.setId(-1L);
//            }
//            OcBRefundIn ocBRefundIn = new OcBRefundIn();
//            //入库单ID
//            ocBRefundIn.setId(ModelUtil.getSequence(OcCommonConstant.OC_B_REFUND_IN));
//            //获取实体仓
//            Long wareId = returnOrder.getCpCPhyWarehouseInId();
//            if (wareId != null) {
//                CpCPhyWarehouse warehouse = cpRpcService.selectPhyWarehouseById(wareId);
//                if (warehouse != null) {
//                    ocBRefundIn.setCpCPhyWarehouseId(warehouse.getId());
//                    ocBRefundIn.setCpCPhyWarehouseEcode(warehouse.getEcode());
//                    ocBRefundIn.setCpCPhyWarehouseEname(warehouse.getEname());
//                }
//                Long storeId = returnOrder.getCpCStoreId();
//                String storeCode = returnOrder.getCpCStoreEcode();
//                String storeName = returnOrder.getCpCStoreEname();
//                if (storeId == null || StringUtils.isEmpty(storeCode) || StringUtils.isEmpty(storeName)) {
//                    StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
//                    storeInfoQueryRequest.setPhyId(wareId);
//                    HashMap<Long, List<com.jackrain.nea.cp.result.CpCStore>> storeInfoByPhyId = basicCpQueryService.getStoreInfoByPhyId(storeInfoQueryRequest);
//                    if (MapUtils.isNotEmpty(storeInfoByPhyId)) {
//                        List<com.jackrain.nea.cp.result.CpCStore> cpCStores = storeInfoByPhyId.get(wareId);
//                        if (CollectionUtils.isNotEmpty(cpCStores)) {
//                            Optional<com.jackrain.nea.cp.result.CpCStore> returnStore = cpCStores.stream().filter(x -> DrpStoreTypeEnum.TYPE_27.getValue().equals(x.getStoretype())).findFirst();
//                            if (returnStore.isPresent()) {
//                                com.jackrain.nea.cp.result.CpCStore cpCStore = returnStore.get();
//                                ocBRefundIn.setInStoreId(cpCStore.getId());
//                                ocBRefundIn.setInStoreEcode(cpCStore.getEcode());
//                                ocBRefundIn.setInStoreEname(cpCStore.getEname());
//                            }
//                        }
//                    } else {
//                        ocBRefundInLogService.addLog(ocBRefundIn.getId(), "新增入库单", "数据异常:当前退货入库单逻辑仓信息为空", SystemUserResource.getRootUser());
//                    }
//                } else {
//                    ocBRefundIn.setInStoreId(storeId);
//                    ocBRefundIn.setInStoreEcode(storeCode);
//                    ocBRefundIn.setInStoreEname(storeName);
//                }
//            } else {
//                // 按逻辑来讲  实体仓为空不会传wing（wms）
//                log.debug("当前退单实体仓信息为空");
//            }
//            //收货人姓名
//            ocBRefundIn.setReceiverName(returnOrder.getReceiveName());
//            //收货人手机号
//            ocBRefundIn.setReceiverMobile(returnOrder.getReceiveMobile());
//            //发件地址
//            ocBRefundIn.setReceiverAddress(returnOrder.getReceiveAddress());
//            //批次编号
//            ocBRefundIn.setBatchNo(refundBatch.getBatchNo());
//            //批次编号ID
//            ocBRefundIn.setOcBRefundBatchId(refundBatch.getId());
//            //原平台单号
//            ocBRefundIn.setSourceCode(returnOrder.getTid());
//            //备注
//            ocBRefundIn.setRemark("WMS入库");
//            BaseModelUtil.makeBaseCreateField(ocBRefundIn, SystemUserResource.getRootUser());
//            //物流单号
//            ocBRefundIn.setLogisticNumber(returnOrder.getLogisticsCode());
//            //物流公司
//            ocBRefundIn.setCpCLogisticsEname(returnOrder.getCpCLogisticsEname());
//            //门店编码
//            ocBRefundIn.setStoreCode(returnOrder.getStoreCode());
//            //门店名称
//            ocBRefundIn.setStoreName(returnOrder.getStoreName());
//            //门店id
//            ocBRefundIn.setStoreId(returnOrder.getStoreId());
//            //门店档案的结算组织名称
//            ocBRefundIn.setSettleOrgName(returnOrder.getSettleOrgName());
//            //门店档案的结算组织编码
//            ocBRefundIn.setSettleOrgCode(returnOrder.getSettleOrgCode());
//            //门店档案的结算供应商编码
//            ocBRefundIn.setSettleSupplierCode(returnOrder.getSettleSupplierCode());
//            //门店档案的结算供应商名称
//            ocBRefundIn.setSettleSupplierName(returnOrder.getSettleSupplierName());
//            //是否可用
//            ocBRefundIn.setIsactive(IsActiveEnum.Y.getKey());
//            //入库状态
//            ocBRefundIn.setInStatus(2);
//            //匹配状态
//            ocBRefundIn.setMatchStatus(OcBOrderConst.REFUND_IN_MATCHSTATUS_UN);
//            //特殊处理类型
//            ocBRefundIn.setSpecialType(OcBOrderConst.ORDER_SPECIAL_TYPE_NORMAL);
//            //WMS单据编号
//            ocBRefundIn.setWmsBillNo(returnOrder.getWmsBillNo());
//            //是否关闭匹配
//            ocBRefundIn.setIsOffMatch(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
//
//            String allSku = "";
//            /**退货入库单-明细表**/
//            List<ReturnOrderWMSInResultModel.OrderItem> resultModelItemList = resultModel.getItemList();
//            List<OcBRefundInProductItem> itemList = new ArrayList<>();
//            List<OcBReturnOrderRefund> refundList = ocBReturnOrderRefundMapper.selectByOcOrderId(returnOrder.getId());
//            if (CollectionUtils.isEmpty(refundList)) {
//                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "对应退单未查询到明细数据", returnNo));
//                continue;
//            }
//            Map<String, List<OcBReturnOrderRefund>> refundGroupBySku = refundList.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getPsCSkuEcode));
//            for (ReturnOrderWMSInResultModel.OrderItem orderItem : resultModelItemList) {
//                List<OcBReturnOrderRefund> matchRefundList = refundGroupBySku.get(orderItem.getSkuCode());
//                if (CollectionUtils.isEmpty(matchRefundList)) {
//                    continue;
//                }
//                //生成入库明细时只从退单明细拿sku信息  所以同sku可任取一条
//                OcBReturnOrderRefund refund = matchRefundList.get(0);
//
//                OcBRefundInProductItem ocBRefundInItem = this.buildOcBRefundInProductItem(ocBRefundIn, orderItem, refund);
//                //记录系统日志 暂不做处理
//                itemList.add(ocBRefundInItem);
//            }
//            if (CollectionUtils.isEmpty(itemList)) {
//                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "明细未匹配到对应条码", returnNo));
//                continue;
//            }
//            Map<String, Long> mapSku = itemList.stream().collect(Collectors.groupingBy(OcBRefundInProductItem::getPsCSkuEcode, Collectors.counting()));
//            for (Map.Entry<String, Long> stringLongEntry : mapSku.entrySet()) {
//                //拼接所有SKU
//                allSku = allSku + stringLongEntry.getKey() + "(" + stringLongEntry.getValue() + "),";
//            }
//            ocBRefundIn.setAllSku(allSku);
//            ocBRefundInMapper.insert(ocBRefundIn);
//            //退单明细保存
//            int i = ocBRefundInProductItemMapper.batchInsert(itemList);
//            OcBReturnOrder update = new OcBReturnOrder();
//            update.setId(returnOrder.getId());
//            //记录生成的退货入库单id 回传时防止重复生成  产品确认 wing会一次回传所有明细结果
//            update.setExistRefundInId(ocBRefundIn.getId());
//            returnOrderMapper.updateById(update);
//            returnOrderLogService.addRefundOrderLog(returnOrder.getId(), "WMS入库回传", "接收WMS入库成功，生成退货入库单ID:" + ocBRefundIn.getId(), SystemUserResource.getRootUser());
//            ocBRefundInLogService.addLog(ocBRefundIn.getId(), "新增入库单", "接收WING回传WMS入库结果成功", SystemUserResource.getRootUser());
////            return i > 0 ? ValueHolderV14Utils.getSuccessValueHolder("OMS生成退货入库单成功") : ValueHolderV14Utils.getFailValueHolder("OMS生成退货入库单失败");
//        }
//        return ValueHolderV14Utils.custom(ResultCode.SUCCESS, "接收数据成功", failedList);
//    }
    private Map<String, List<ProAndSkuResult>> getShopSku(List<SkuInfoListRequest> proList) {
        ValueHolder valueHolder = null;
        try {
            valueHolder = psRpcService.querySkuInfoByProEcodeAndClrsize(proList);
        } catch (Exception e) {
            log.error(LogUtil.format("奇门退单新增查询商品异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("奇门退单新增查询商品异常", e);
        }
        if (null == valueHolder) {
            throw new NDSException("奇门退单新增店铺策略返回为空");
        }
        Integer code = (Integer) valueHolder.get("code");
        if (!code.toString().equals("0")) {
            throw new NDSException("奇门退单新增商品返回为空");
        }
        if (null == valueHolder.get("data")) {
            throw new NDSException("奇门退单新增商品返回数据为空");
        }
        List<ProAndSkuResult> data = (List<ProAndSkuResult>) valueHolder.get("data");
        return data.stream().collect(Collectors.groupingBy(ProAndSkuResult::getPosKey));
    }

    private void getO2OPhyWarehouse(String orderSn, OcBRefundIn ocBRefundIn) {
        //找原单信息
        ValueHolderV14 outNoticeValueHolderV14 = null;
        try {
            outNoticeValueHolderV14 = sgRpcService.querySgPhyOutNoticeByOutNoticeNo(orderSn);
        } catch (Exception e) {
            log.error(LogUtil.format("奇门退单新增出库通知单查询库存中心失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));

            throw new NDSException("奇门退单新增出库通知单查询库存中心失败", e);
        }
        if (ResultCode.FAIL == outNoticeValueHolderV14.getCode()) {
            throw new NDSException("奇门退单新增出库通知单查询库存中心返回失败" + orderSn);
        }

//        if (CollectionUtils.isEmpty(outNoticeValueHolderV14.getData())) {
//            throw new NDSException("奇门退单新增出库通知单查询库存中心返回为空信息" + orderSn);
//        }

        //SgBPhyOutNotices sgBPhyOutNotices = outNoticeValueHolderV14.getData().get(0);
        //查询原单信息
        OcBOrder order = null;
                //ocBOrderMapper.selectByID(sgBPhyOutNotices.getSourceBillId());
        if (null == order) {
            throw new NDSException("奇门退单新增查询原单为空信息" + orderSn);
        }
        //买家昵称
        ocBRefundIn.setUserNick(order.getUserNick() == null ? "" : order.getUserNick());
        // 20200818 向催 直接从原单中找实体仓和逻辑仓
        ocBRefundIn.setCpCPhyWarehouseId(order.getCpCPhyWarehouseId());
        ocBRefundIn.setCpCPhyWarehouseEcode(order.getCpCPhyWarehouseEcode());
        ocBRefundIn.setCpCPhyWarehouseEname(order.getCpCPhyWarehouseEname());
        /*ocBRefundIn.setInStoreId(order.getCpCStoreId());
        ocBRefundIn.setInStoreEcode(order.getCpCStoreEcode());
        ocBRefundIn.setInStoreEname(order.getCpCStoreEname());

        ShopStrategyQueryServiceCmd shopStrategyQueryServiceCmd = (ShopStrategyQueryServiceCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                ShopStrategyQueryServiceCmd.class.getName(), "st", "1.0");
        StCShopStrategyDO stCShopStrategyDO = null;
        try {
            //店铺id
            stCShopStrategyDO = shopStrategyQueryServiceCmd.selectOcStCShopStrategyByCpCshopId(cpCShopId);
        }catch (Exception e){
            log.debug("奇门退单新增查询店铺策略异常cpCShopId" + cpCShopId + e.getMessage());
            throw new NDSException("奇门退单新增查询店铺策略异常",e);
        }
        if (null == stCShopStrategyDO || null == stCShopStrategyDO.getPosRefundPhyWarehouseId()){
            throw new NDSException("奇门退单新增店铺策略返回为空");
        }
        Long posRefundPhyWarehouseId = stCShopStrategyDO.getPosRefundPhyWarehouseId();
        //查询实体仓信息
        CpPhyWarehouseSelectCmd cpPhyWarehouseSelectCmd;
        CpCPhyWarehouse cpCPhyWarehouse = null;
        try {
            cpCPhyWarehouse = cpPhyWarehouseSelectCmd.queryWarehouseById(posRefundPhyWarehouseId);
        }catch (Exception e){
            log.debug("奇门退单新增查询实体仓信息异常posRefundPhyWarehouseId" + posRefundPhyWarehouseId + e.getMessage());
            throw new NDSException("奇门退单新增查询实体仓信息异常",e);
        }
        if (null == cpCPhyWarehouse ){
            throw new NDSException("奇门退单新增店铺策略查询实体仓返回为空"+posRefundPhyWarehouseId);
        }
        ocBRefundIn.setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
        ocBRefundIn.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
        ocBRefundIn.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());*/
        CpCStore cpCStore = null;
        try {
            cpCStore = cpRpcService.queryMainStoreByWarehouseId(order.getCpCPhyWarehouseId());
        } catch (Exception e) {
            log.error(LogUtil.format("记录退货入库日志异常,异常信息为:{}",order.getCpCPhyWarehouseId()), Throwables.getStackTraceAsString(e));
            throw new NDSException("奇门退单新增查询逻辑仓信息异常", e);
        }
        if (null == cpCStore) {
            throw new NDSException("奇门退单新增店铺策略查询逻辑仓返回为空{}" + order.getCpCPhyWarehouseId());
        }
        ocBRefundIn.setInStoreId(cpCStore.getId());
        ocBRefundIn.setInStoreEcode(cpCStore.getEcode());
        ocBRefundIn.setInStoreEname(cpCStore.getEname());
    }


    /**
     * 回传保存之前操作
     *
     * @param msg
     */
    public void handleBefore(String msg) {
        // 定义变量
        // 退单编号
        String returnOrderCode = "";
        // wms 单据编号 returnOrderId
        String wmsBillNo = "";
        String orderType = "";
        JSONObject request = null;
        // 退货单信息
        JSONObject returnOrder = null;
        // 初始状态
        int status = OcBRefundInTaskStatusEnum.INIT.getVal();
        // 是否存在
        boolean exist = false;
        try {
            JSONObject object = JSONObject.parseObject(msg);
            request = object.getJSONObject("request");
            // 退货单信息
            returnOrder = request.getJSONObject("returnOrder");
            // 退货入库单编码 @20200715 这个字段发送端已经修改为billNo，不在是id
            returnOrderCode = returnOrder.getString("returnOrderCode");
            // 回传Wms 编号
            wmsBillNo = returnOrder.getString("returnOrderId");
             /*订单类型, ①orderType为THRK=退货入库单，HHRK=换货入库，走B2C退货收货规则
                       ②orderType为WMJRK=无名件入库，走无名件入库规则*/
            orderType = returnOrder.getString("orderType");
            if (StringUtils.isNotBlank(wmsBillNo)) {
                exist = refundInMakeUpService.isExist(wmsBillNo, "Y");
                if (exist) {
                    refundInMakeUpService.updateWmsCallBackCount(wmsBillNo);
                }
            }
        } catch (Exception e) {
            status = OcBRefundInTaskStatusEnum.RESOLVE_FAIL.getVal();
        }
        if (!exist) {
            // 不存在保存至中间表
            this.save2RefundTask(msg, status, wmsBillNo, returnOrderCode, orderType
                    , WmsUserResource.getWmsUser());
        }
    }
    public ValueHolderV14 wingToWmsNoticeBack(String msg) {
        List<ReturnOrderNoticeBackModel> modelList = JSONObject.parseArray(msg, ReturnOrderNoticeBackModel.class);
        List<ValueHolderV14> failedList = new ArrayList<>(modelList.size());
        List<ReturnOrderNoticeBackModel> checkedPassList = new ArrayList<>(modelList.size());

        for (ReturnOrderNoticeBackModel object : modelList) {
            String returnOrderCode = object.getReturnNo();
            // 回传Wms 编号(斯凯奇对应的是wing单据编号)
            String wmsBillNo = object.getBillNo();

            Integer resultStatus = object.getStatus();
            if (StringUtils.isEmpty(returnOrderCode)) {
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "回传参数returnNo为空", object.getReturnNo()));
                continue;
            }
            if (StringUtils.isEmpty(wmsBillNo)) {
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "回传参数billNo为空", object.getReturnNo()));
                continue;
            }
            if (resultStatus == null) {
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "回传参数status为空", object.getReturnNo()));
                continue;
            }
            checkedPassList.add(object);
        }
        if (CollectionUtils.isEmpty(checkedPassList)) {
            return ValueHolderV14Utils.custom(ResultCode.SUCCESS, "接收WING通知传WMS数据并处理成功", failedList);
        }
        List<String> billNoList = checkedPassList.stream().map(ReturnOrderNoticeBackModel::getReturnNo).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<OcBReturnOrder> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(OcBReturnOrder::getBillNo, billNoList);
        queryWrapper.eq(OcBReturnOrder::getIsactive, IsActiveEnum.Y.getKey());
        List<OcBReturnOrder> returnOrderList = returnOrderMapper.selectList(queryWrapper);
        Map<String, List<OcBReturnOrder>> returnOrderMap = returnOrderList.stream().collect(Collectors.groupingBy(OcBReturnOrder::getBillNo));
        Map<Long, ReturnOrderNoticeBackModel> returnOrderByIdMap = new HashMap<>(checkedPassList.size());
        List<Long> successIdList=new ArrayList<>(checkedPassList.size());
        List<OcBReturnOrderLog> returnOrderLogList=new ArrayList<>(checkedPassList.size());
        String logType="退单传WMS";
        String successMsg="WING通知传WMS成功";
        String errMsg="WING通知传WMS失败:";
        for (ReturnOrderNoticeBackModel object : checkedPassList) {
            String returnOrderCode = object.getReturnNo();
            Integer resultStatus = object.getStatus();

            List<OcBReturnOrder> orderList = returnOrderMap.get(returnOrderCode);
            if (CollectionUtils.isEmpty(orderList)) {
                log.error("退单不存在：returnNo:{}", returnOrderCode);
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "未查询到有效的对应退单", returnOrderCode));
                continue;
            }
            if (orderList.size() > 1) {
                log.error("数据异常：returnNo:{}", returnOrderCode);
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "查询到多条数据", returnOrderCode));
                continue;
            }
            OcBReturnOrder returnOrder=orderList.get(0);
            //传wing(及wms)结果成功
            if (resultStatus == ResultCode.SUCCESS) {

                //传wms成功埋点
                nodeRecordService.insertByNode(ReturnOrderNodeEnum.OMS_RECEIVE_WMS_SUCCESS_TIME, new Date(),
                        returnOrder.getId(), SystemUserResource.getRootUser());

                successIdList.add(returnOrder.getId());
                returnOrderByIdMap.put(returnOrder.getId(), object);
                OcBReturnOrderLog orderLog = returnOrderLogService.buildReturnOrderLog(returnOrder.getId(), logType, successMsg, wmsUserCreateUtil.initWmsUser());
                if (orderLog != null) {
                    returnOrderLogList.add(orderLog);
                }
            } else {
                String failReason = object.getFailReason();
                try {
                    OcBReturnOrder update = new OcBReturnOrder();
                    update.setId(returnOrder.getId());
                    if (StringUtils.isNotEmpty(failReason)) {
                        if (failReason.length() > 200) {
                            failReason = failReason.substring(0, 200);
                        }
                    }
                    update.setWmsFailreason(failReason);
                    update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
                    update.setIsTowms(WmsWithdrawalState.FAIL.toInteger());
                    Integer toDrpCount = returnOrder.getToDrpCount() == null ? 0 : returnOrder.getToDrpCount();
                    Long qtyWmsFail = returnOrder.getQtyWmsFail() == null ? 0 : returnOrder.getQtyWmsFail();
                    update.setToDrpCount(toDrpCount + 1);
                    update.setQtyWmsFail(qtyWmsFail + 1);
                    update.setThirdPartyFailStatus(ReturnOrderPushWingUtils.THIRD_PARTY_FAIL_STATUS_03);
                    update.setRemark(failReason);
                    update.setWmsFailreason(failReason);
                    returnOrderMapper.updateById(update);
                }catch (Exception e){
                    log.error(LogUtil.format("接收wing入库通知更新失败数据异常：billNo:{} error:{}",returnOrderCode),returnOrderCode, Throwables.getStackTraceAsString(e));
                }
                OcBReturnOrderLog orderLog = returnOrderLogService.buildReturnOrderLog(returnOrder.getId(), logType, errMsg + failReason, wmsUserCreateUtil.initWmsUser());
                if (orderLog != null) {
                    returnOrderLogList.add(orderLog);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(successIdList)) {
            successIdList.forEach(x -> {
                OcBReturnOrder update = new OcBReturnOrder();
                update.setId(x);
                update.setIsTowms(WmsWithdrawalState.YES.toInteger());
                update.setToDrpStatus(ToDRPStatusEnum.SUCCESS.getCode());
                ReturnOrderNoticeBackModel request = returnOrderByIdMap.get(x);
                if (request != null) {
                    update.setInWingToWmsTime(request.getInWingToWmsTime());
//                    update.setInWmsReceiveTime(request.getInWmsReceiveTime());
                }
                returnOrderMapper.updateById(update);
            });
        }
        if (CollectionUtils.isNotEmpty(returnOrderList)) {
            returnOrderLogService.batchInsertLog(returnOrderLogList);
        }
        return ValueHolderV14Utils.custom(ResultCode.SUCCESS, "接收WING通知传WMS数据并处理成功", failedList);
    }
    /**
     * 退换货通知WMS结果反馈接口
     *
     * @param msg
     */
    public ValueHolderV14 handleWmsNoticeReturnBack(String msg) {
        List<ReturnOrderNoticeBackModel> modelList = JSONObject.parseArray(msg, ReturnOrderNoticeBackModel.class);
        List<ValueHolderV14> failedList = new ArrayList<>();
        for (ReturnOrderNoticeBackModel object : modelList) {
            String returnOrderCode = object.getReturnNo();
            // 回传Wms 编号(斯凯奇对应的是wing单据编号)
            String wmsBillNo = object.getBillNo();
             /*订单类型, ①orderType为THRK=退货入库单，HHRK=换货入库，走B2C退货收货规则
                       ②orderType为WMJRK=无名件入库，走无名件入库规则*/
            String orderType = "THRK";
            Integer resultStatus = object.getStatus();
            if (StringUtils.isEmpty(returnOrderCode)) {
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "回传参数returnNo为空", object.getReturnNo()));
                continue;
            }
            if (StringUtils.isEmpty(wmsBillNo)) {
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "回传参数billNo为空", object.getReturnNo()));
                continue;
            }
            if (resultStatus == null) {
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "回传参数status为空", object.getReturnNo()));
                continue;
            }
            OcBReturnOrder returnOrder = returnOrderMapper.selectByBillNo(returnOrderCode);
            if (returnOrder == null) {
                log.error("退单不存在：returnNo:{}", returnOrderCode);
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "未查询到有效的对应退单", returnOrderCode));
                continue;
            }
            //传wing(及wms)结果成功
            if (resultStatus == ResultCode.SUCCESS) {
                // 初始状态
//                int status = OcBRefundInTaskStatusEnum.INIT.getVal();
//                // 是否存在
//                boolean exist = false;
//                try {
//                    if (StringUtils.isNotBlank(wmsBillNo)) {
//                        exist = refundInMakeUpService.isExist(wmsBillNo, "Y");
//                        if (exist) {
//                            refundInMakeUpService.updateWmsCallBackCount(wmsBillNo);
//                        }
//                    }
//                } catch (Exception e) {
//                    status = OcBRefundInTaskStatusEnum.RESOLVE_FAIL.getVal();
//                }
//                if (!exist) {
//                    // 不存在保存至中间表
//                    this.save2RefundTask(JSON.toJSONString(object), status, wmsBillNo, returnOrderCode, orderType
//                            , WmsUserResource.getWmsUser());
//                }

                //传wms成功埋点
                nodeRecordService.insertByNode(ReturnOrderNodeEnum.OMS_RECEIVE_WMS_SUCCESS_TIME, new Date(),
                        returnOrder.getId(), SystemUserResource.getRootUser());

                OcBReturnOrder update = new OcBReturnOrder();
                update.setId(returnOrder.getId());
                update.setIsTowms(WmsWithdrawalState.YES.toInteger());
                update.setToDrpStatus(ToDRPStatusEnum.SUCCESS.getCode());
                returnOrderMapper.updateById(update);
                omsReturnOrderService.saveAddOrderReturnLog(returnOrder.getId()
                        , "WING通知传WMS成功", "退单传WMS"
                        , wmsUserCreateUtil.initWmsUser());
            } else {
                OcBReturnOrder update = new OcBReturnOrder();
                update.setId(returnOrder.getId());
                String failReason = object.getFailReason();
                if (StringUtils.isNotEmpty(failReason)) {
                    if (failReason.length() > 200) {
                        failReason = failReason.substring(0, 200);
                    }
                }
                update.setWmsFailreason(failReason);
                update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
                update.setIsTowms(WmsWithdrawalState.FAIL.toInteger());
                Integer toDrpCount = returnOrder.getToDrpCount() == null ? 0 : returnOrder.getToDrpCount();
                Long qtyWmsFail = returnOrder.getQtyWmsFail() == null ? 0 : returnOrder.getQtyWmsFail();
                update.setToDrpCount(toDrpCount + 1);
                update.setQtyWmsFail(qtyWmsFail + 1);
                update.setThirdPartyFailStatus(ReturnOrderPushWingUtils.THIRD_PARTY_FAIL_STATUS_03);
                update.setRemark(failReason);
                update.setWmsFailreason(failReason);
                returnOrderMapper.updateById(update);
                omsReturnOrderService.saveAddOrderReturnLog(returnOrder.getId()
                        , "WING通知传WMS失败" + failReason, "退单传WMS"
                        , wmsUserCreateUtil.initWmsUser());
            }
        }
        return ValueHolderV14Utils.custom(ResultCode.SUCCESS, "接收回传数据成功", failedList);
    }

    private void batchUpdateReturnOrder(List<OcBReturnOrder> updateList, List<OcBReturnOrderLog> ocBReturnOrderLogList) {
//        returnOrderMapper.batchUpdateByIds(updateList);
        ocBReturnOrderLogMapper.batchInsert(ocBReturnOrderLogList);
    }

    /**
     * 保存报文到中间表
     *
     * @param msg             报文
     * @param status          状态
     * @param wmsBillNo       wms 单据编号 returnOrderId
     * @param returnOrderCode returnBillNo
     * @param user            操作人
     */
    public void save2RefundTask(String msg, int status, String wmsBillNo, String returnOrderCode
            , String orderType, User user) {
        OcBRefundInTask ocBRefundInTask = new OcBRefundInTask();
        ocBRefundInTask.setId(ModelUtil.getSequence("OC_B_REFUND_IN_TASK"));
        ocBRefundInTask.setBillStatus(status);
        ocBRefundInTask.setMsg(msg);
        ocBRefundInTask.setWmsBillNo(wmsBillNo);
        ocBRefundInTask.setReturnBillNo(returnOrderCode);
        ocBRefundInTask.setOrderType(orderType);
        // 0 ,借用一下枚举类的值
        ocBRefundInTask.setFailedCount(OcBRefundInTaskStatusEnum.INIT.getVal());
        // 1 ,借用一下枚举类的值
        //ocBRefundInTask.setWmsBackCount(OcBRefundInTaskStatusEnum.RESOLVE_FAIL.getVal());
        OperateUserUtils.defaultOperator(ocBRefundInTask, user);
        ocBRefundInTaskMapper.insert(ocBRefundInTask);
    }


    /**
     * 保存报文到中间表
     *
     * @param task       中间表报文信息
     * @param status     状态
     * @param reason     失败原因
     * @param refundInId 退货入库单号 记录无头件入库生成的退货入库单单号 (非必填)
     * @param user       操作人
     */
    public void update2RefundTask(OcBRefundInTask task, int status, String reason, Long refundInId, User user) {
        task.setBillStatus(status);
        task.setFailedReason(reason);
        // 失败次数
        int count = OcBRefundInTaskMapper.INIT_COUNT;
        if (Objects.equals(OcBRefundInTaskStatusEnum.RESOLVE_FAIL.getVal(), status)) {
            count = OcBRefundInTaskMapper.MAX_COUNT;
        } else if (Objects.equals(OcBRefundInTaskStatusEnum.HANDLE_FAIL.getVal(), status)) {
            count = Optional.ofNullable(task.getFailedCount()).orElse(OcBRefundInTaskMapper.INIT_COUNT)
                    + OcBRefundInTaskMapper.INCREMENT_COUNT;
        }
        task.setRefundInId(refundInId);
        task.setFailedCount(count);
        BaseModelUtil.setupUpdateParam(task, user);
        ocBRefundInTaskMapper.updateById(task);
    }

    /**
     * @param refundIn    退货入库单
     * @param returnOrder 退换货单
     * @param products    退货入库单商品明细
     * @param refunds     退换货单商品明细
     */
    private void match(OcBRefundIn refundIn, OcBReturnOrder returnOrder, List<OcBRefundInProductItem> products
            , List<OcBReturnOrderRefund> refunds) {

        if (returnOrder.getOcBRefundInId() != null) {
            // b2c标识
            refundIn.setReserveBigint01(OcBOrderConst.REFUND_IN_IS_ANONYMOUSO_YES.longValue());
            returnOrder.setIsAnonymous(OcBOrderConst.REFUND_IN_IS_ANONYMOUSO_YES);
            return;
        }

        Map<String, List<OcBRefundInProductItem>> productSkuMap = products.stream().collect(Collectors.groupingBy(OcBRefundInProductItem::getPsCSkuEcode, Collectors.toList()));
        for (OcBReturnOrderRefund refund : refunds) {
            String skuCode = refund.getPsCSkuEcode();
            BigDecimal qtyRefund = refund.getQtyRefund();
            if (!NumUtil.gtZero(refund.getQtyRefund())) {
                continue;
            }

            List<OcBRefundInProductItem> inProductItemList = productSkuMap.get(skuCode);
            if (CollectionUtils.isEmpty(inProductItemList)) {
                continue;
            }

            BigDecimal qtyInCount = BigDecimal.ZERO;
            for (OcBRefundInProductItem item : inProductItemList) {
                if (!NumUtil.gtZero(item.getQty())) {
                    continue;
                }
                if (NumUtil.prevGtNext(qtyInCount.add(item.getQty()), qtyRefund)) {
                    break;
                }
                qtyInCount = qtyInCount.add(item.getQty());
                item.setOcBReturnOrderId(returnOrder.getId());
                item.setIsWithoutOrig(IsWithoutOrigEnum.NOT_WITHOUT_ORIG.getVal());
            }
            if (!NumUtil.gtZero(qtyInCount)) {
                continue;
            }
            Long qtyMatch = refund.getQtyMatch() == null ? 0L : refund.getQtyMatch();
            refund.setQtyMatch(qtyMatch + qtyInCount.intValue());
        }
        // b2c标识
        refundIn.setReserveBigint01(OcBOrderConst.REFUND_IN_IS_ANONYMOUSO_YES.longValue());
        returnOrder.setIsAnonymous(OcBOrderConst.REFUND_IN_IS_ANONYMOUSO_YES);
        returnOrder.setOcBRefundInId(refundIn.getId());
    }


    /**
     * 生成退货入库单关系model
     * todo
     */
    public RefundInRelation buildRefundInRelation(OcBRefundIn ocBRefundIn, JSONArray orderLines) {
        /**退货入库单-明细表**/
        List<OcBRefundInProductItem> itemList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < orderLines.size(); i++) {
            JSONObject orderObject = orderLines.getJSONObject(i).getJSONObject("orderLine");
            if (orderObject != null) {
                //按照数量QTY进行拆明细
                int qty = orderObject.getBigDecimal("planQty").intValue();
                //拼接所有SKU
                sb.append(orderObject.getString("itemCode")).append("(").append(qty).append("),");
                for (int j = 0; j < qty; j++) {
                    OcBRefundInProductItem ocBRefundInItem = new OcBRefundInProductItem();
                    String itemCode = orderObject.getString("itemCode");
                    ProductSku skuInfo = psRpcService.selectProductSku(itemCode);
                    if (skuInfo == null) {
                        throw new NDSException("SKU编码:itemCode商品信息不存在,不生成退货入库单!");
                    }
                    ocBRefundInItem.setGbcode(skuInfo.getBarcode69());
                    //条码
                    ocBRefundInItem.setPsCSkuEcode(itemCode);
                    // @20200718 增加SKUID等信息
                    ocBRefundInItem.setPsCSkuId(skuInfo.getId());
                    ocBRefundInItem.setPsCProEcode(skuInfo.getSku());
                    //商品名称 skuInfo.getName() "SM爽肤水"
                    ocBRefundInItem.setPsCProEname(skuInfo.getName());
                    //商品标记
                    ocBRefundInItem.setProductMark(orderObject.getString("inventoryType"));
                    //数量
                    ocBRefundInItem.setQty(BigDecimal.ONE);
                    //退换货单编号、订单编号(PRD要求为空)
                    //明细编号
                    ocBRefundInItem.setId(ModelUtil.getSequence("OC_B_REFUND_IN_PRODUCT_ITEM"));
                    //是否匹配
                    ocBRefundInItem.setIsMatch(OcBOrderConst.REFUND_IN_MATCHSTATUS_UN);
                    //是否生成调整单
                    ocBRefundInItem.setIsGenAdjust(0);
                    //是否生成错发调整单
                    ocBRefundInItem.setIsGenWroAdjust(IsGenWroAdjustEnum.NO.integer());
                    //是否生成冲无头件调整单
                    ocBRefundInItem.setIsGenMinusAdjust(IsGenMinusAdjustEnum.NO.integer());
                    //退货入库单ID
                    ocBRefundInItem.setOcBRefundInId(ocBRefundIn.getId());
                    // @20200717 增加吊牌价、颜色、尺寸、国标
                    // 颜色id
                    ocBRefundInItem.setPsCClrId(skuInfo.getColorId());
                    // 颜色编码
                    ocBRefundInItem.setPsCClrEcode(skuInfo.getColorCode());
                    // 颜色名称
                    ocBRefundInItem.setPsCClrEname(skuInfo.getColorName());
                    // 尺寸id
                    ocBRefundInItem.setPsCSizeId(skuInfo.getSizeId());
                    // 尺寸编码
                    ocBRefundInItem.setPsCSizeEcode(skuInfo.getSizeCode());
                    // 尺寸名称
                    ocBRefundInItem.setPsCSizeEname(skuInfo.getSizeName());
                    // 实收条码国标码
                    ocBRefundInItem.setGbcode(skuInfo.getEcode());
                    // 吊牌价
                    ocBRefundInItem.setPriceList(skuInfo.getPricelist());
                    // 赋值默认1
                    ocBRefundInItem.setIsWithoutOrig(IsWithoutOrigEnum.IS_WITHOUT_ORIG.getVal());
                    //是否生成入库单
                    ocBRefundInItem.setIsGenInOrder(IsGenInEnum.NO.integer());
                    OperateUserUtils.saveOperator(ocBRefundInItem, SystemUserResource.getRootUser());
                    //记录系统日志 暂不做处理
                    itemList.add(ocBRefundInItem);
                }
            }
        }
        // 更新ALLSKU
        ocBRefundIn.setAllSku(sb.toString());
        RefundInRelation refundInRelation = new RefundInRelation();
        refundInRelation.setItems(itemList);
        refundInRelation.setRefundIn(ocBRefundIn);
        return refundInRelation;
    }

    /**
     * @param returnOrder
     * @param orderLines
     * @param jsonSender
     * @param returnOrderCode
     * @param warehouseCode
     * @return
     */
    private OcBRefundIn buildOcBRefundIn(JSONObject returnOrder, JSONArray orderLines, JSONObject jsonSender
            , String returnOrderCode, String warehouseCode) {
        /**退货入库表**/
        String sourceCode = null;
        OcBRefundIn ocBRefundIn = new OcBRefundIn();
        //退货批次
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("id", 2);
        OcBRefundBatch refundBatch = ocBRefundBatchMapper.selectOne(wrapper);

        // @20200717 这里对批次的使用不严格，可能存在批次不存在的情况，为空的话则默认一个
        if (Objects.isNull(refundBatch)) {
            refundBatch = new OcBRefundBatch();
            // 后面会用到的这两个值
            refundBatch.setBatchNo("yyyyMMddHHmmssSSS0001");
            refundBatch.setId(-1L);
        }

        //订单信息抬头  组装数据
        JSONObject orderLine = orderLines.getJSONObject(0).getJSONObject("orderLine");
        if (null != orderLine) {
            sourceCode = orderLine.getString("sourceOrderCode");
        }
        if (null != jsonSender) {
            //收货人姓名
            ocBRefundIn.setReceiverName(jsonSender.getString("name"));
            //收货人手机号
            ocBRefundIn.setReceiverMobile(jsonSender.getString("mobile"));
            //发件地址
            ocBRefundIn.setReceiverAddress(jsonSender.getString("detailAddress"));
        }
        CpCStore cpCStores = null;
        try {
            //匹配实体仓档案的外部编码 调用CP CMD
            List<String> wmscodes = new ArrayList<>();
            wmscodes.add(warehouseCode);
            ValueHolderV14<List<CpCStore>> cpCStoreByOrgNameList = cpRpcService.queryStoreBySapCodes(wmscodes);
            if (cpCStoreByOrgNameList.getData().size() > 0) {
                cpCStores = cpCStoreByOrgNameList.getData().get(0);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("匹配实体仓档案异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("匹配实体仓档案异常!");
        }
        if (null == cpCStores) {
            throw new NDSException("未匹配到逻辑仓档案!");
        }
        //入库仓库ID
        ocBRefundIn.setInStoreId(cpCStores.getId());
        //入库仓库编号
        ocBRefundIn.setInStoreEcode(cpCStores.getEcode());
        //入库仓库名称
        ocBRefundIn.setInStoreEname(cpCStores.getEname());
        //实体仓id
        ocBRefundIn.setCpCPhyWarehouseId(cpCStores.getCpCPhyWarehouseId());
        //实体仓编码
        ocBRefundIn.setCpCPhyWarehouseEcode(cpCStores.getCpCPhyWarehouseEcode());
        //实体仓名称
        ocBRefundIn.setCpCPhyWarehouseEname(cpCStores.getCpCPhyWarehouseEname());
        //批次编号
        ocBRefundIn.setBatchNo(refundBatch.getBatchNo());
        //批次编号ID
        ocBRefundIn.setOcBRefundBatchId(refundBatch.getId());
        //原平台单号
        ocBRefundIn.setSourceCode(sourceCode);
        //备注
        ocBRefundIn.setRemark(returnOrder.getString("remark"));
        //物流单号
        String expressCode = returnOrder.getString("expressCode");
        if (expressCode != null && expressCode.contains("-")) {
            int endIndex = expressCode.indexOf("-");
            expressCode = expressCode.substring(0, endIndex);
        }
        ocBRefundIn.setLogisticNumber(expressCode);
        //物流公司
        ocBRefundIn.setCpCLogisticsEname(returnOrder.getString("logisticsName"));
        //入库状态
        ocBRefundIn.setInStatus(2);
        //匹配状态
        ocBRefundIn.setMatchStatus(OcBOrderConst.REFUND_IN_MATCHSTATUS_UN);
        //特殊处理类型
        ocBRefundIn.setSpecialType(OcBOrderConst.ORDER_SPECIAL_TYPE_NORMAL);
        //入库单编
        ocBRefundIn.setId(ModelUtil.getSequence("OC_B_REFUND_IN"));
        //WMS单据编号
        ocBRefundIn.setWmsBillNo(returnOrderCode);
        OperateUserUtils.saveOperator(ocBRefundIn, SystemUserResource.getRootUser());
        //是否关闭匹配
        ocBRefundIn.setIsOffMatch(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());    // @20200717 避免魔法值
        return ocBRefundIn;
    }

    /**
     * 查询ES
     *
     * @param wmsBillNo
     * @return
     */
    public OcBRefundIn findByWmsBillNo(String wmsBillNo) {
        QueryWrapper<OcBRefundIn> refundInWrapper = new QueryWrapper<>();
        refundInWrapper.eq("WMS_BILL_NO", wmsBillNo);
        Set<Long> refundInIds = ES4RefundIn.findByWmsBillNo(wmsBillNo);
        OcBRefundIn refundIn = null;
        if (!refundInIds.isEmpty()) {
            refundIn = ocBRefundInMapper.selectById(refundInIds.iterator().next());
        }
        return refundIn;
    }

    @Transactional(rollbackFor = Throwable.class)
    public void updateWmsBillNo(Long returnId, String wmsBillNo) {
        OcBReturnOrder returnOrder1 = new OcBReturnOrder();
        returnOrder1.setWmsBillNo(wmsBillNo);
        returnOrderMapper.update(returnOrder1, new QueryWrapper<OcBReturnOrder>().eq("id", returnId));
    }

    public ValueHolderV14 preSaleSinkResultBack(String msg) {
        List<PreSaleSinkBackModel> resultModelList = JSONObject.parseArray(msg, PreSaleSinkBackModel.class);
        List<ValueHolderV14> failedList = new ArrayList<>();
        List<String> outBillNoList = new ArrayList<>();

        for (PreSaleSinkBackModel resultModel : resultModelList) {
            outBillNoList.add(resultModel.getSourceCode());
        }

        List<OcBOrder> orderList = ocBOrderMapper.selectList(new QueryWrapper<OcBOrder>().lambda()
                .in(OcBOrder::getReserveVarchar04, outBillNoList)
                .eq(OcBOrder::getIsactive, IsActiveEnum.Y.getKey()));

        if (CollectionUtils.isEmpty(orderList)){
            for (PreSaleSinkBackModel resultModel : resultModelList) {
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "未查询到有效零售发货单数据", resultModel.getSourceCode()));
            }
        }

        if(CollectionUtils.isNotEmpty(failedList)){
            return ValueHolderV14Utils.custom(ResultCode.SUCCESS, "接收数据成功", failedList);
        }

        Map<String, OcBOrder> ocBOrderMap = orderList.stream().collect(Collectors.toMap(OcBOrder::getReserveVarchar04, v -> v,
                (v1, v2) -> v1));

        for (PreSaleSinkBackModel resultModel : resultModelList) {
            String outBillNo = resultModel.getSourceCode();
            OcBOrder ocBOrder = ocBOrderMap.get(outBillNo);
            if (ocBOrder == null) {
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "未查询到有效零售发货单数据", outBillNo));
                continue;
            }
            //a)若回执中的【预下沉取消状态】=取消成功时，则更新：
            //i.【零售发货单】的【单据状态】=待审核；
            //ii.【零售发货单】的【实际预下沉状态】=取消成功；

            /**
             * 取消成功
             * 取消中 ==》 取消成功
             * 取消失败  ==》 取消成功
             */
            OcBOrder updateOrder = new OcBOrder();
            if (TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_CANCELING_SUCCESS.equals(ocBOrder.getActualPresinkStatus())){
                continue;
            }
            if (TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_CANCELING.equals(ocBOrder.getActualPresinkStatus()) || TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_CANCELING_FALL.equals(ocBOrder.getActualPresinkStatus())){
                updateOrder.setId(ocBOrder.getId());
                if(resultModel.getStatus() == ResultCode.SUCCESS){
                    updateOrder.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
                    updateOrder.setActualPresinkStatus(TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_CANCELING_SUCCESS);
                }else {
                    updateOrder.setActualPresinkStatus(TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_CANCELING_FALL);
                }
                OperateUserUtils.saveOperator(updateOrder, SystemUserResource.getRootUser());
                ocBOrderMapper.updateById(updateOrder);
            }else {
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "单据状态不允许取消", outBillNo));
            }

        }
        return ValueHolderV14Utils.custom(ResultCode.SUCCESS, "接收数据成功", failedList);
    }

    /**
     * description: 调用wing发货接口回执
     * @Author:  liuwenjin
     * @Date 2021/10/11 8:57 下午
     */
    public ValueHolderV14 preSaleSinkOrderDeliveryCallBack(String msg) {
        List<PreSaleSinkDeliveryBackModel> preSaleSinkDeliveryBackModelList = JSONObject.parseArray(msg, PreSaleSinkDeliveryBackModel.class);
        List<ValueHolderV14> failedList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(preSaleSinkDeliveryBackModelList)){
            List<String> outBillNoList = preSaleSinkDeliveryBackModelList.stream().map(PreSaleSinkDeliveryBackModel ::getSourceCode).collect(Collectors.toList());
            List<OcBToWingDeliveryTask> bToWingDeliveryTaskList = ocBToWingDeliveryTaskMapper.selectListByoutBill(outBillNoList);
            if (CollectionUtils.isEmpty(bToWingDeliveryTaskList)){
                failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "未查询到定金预售发货中间表", outBillNoList));
                ValueHolderV14 vh=ValueHolderV14Utils.custom(ResultCode.SUCCESS, "接收数据成功", failedList);
                return vh;
            }
            Map<String,List<OcBToWingDeliveryTask>>  outBillNoMap=bToWingDeliveryTaskList.stream().collect(Collectors.groupingBy(OcBToWingDeliveryTask::getOutBillNo));
            for (PreSaleSinkDeliveryBackModel resultModel : preSaleSinkDeliveryBackModelList) {
                String outBillNo = resultModel.getSourceCode();
                LambdaQueryWrapper<OcBToWingDeliveryTask> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(OcBToWingDeliveryTask::getOutBillNo, outBillNo);
                List<OcBToWingDeliveryTask> ocBToWingDeliveryTaskList = outBillNoMap.get(outBillNo);
                if (CollectionUtils.isEmpty(ocBToWingDeliveryTaskList)) {
                    failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "未查询到定金预售发货中间表", outBillNo));
                    continue;
                }
                if (ocBToWingDeliveryTaskList.size() > 1) {
                    failedList.add(ValueHolderV14Utils.custom(ResultCode.FAIL, "通过出库通知单查询到多个定金预售发货中间表", outBillNo));
                    continue;
                }
                OcBToWingDeliveryTask ocBToWingDeliveryTask = ocBToWingDeliveryTaskList.get(0);
                String billNo = ocBToWingDeliveryTask.getOutBillNo();
                String remake ="接口调用成功";
                Integer status = 1;
                if(resultModel.getStatus() == ResultCode.SUCCESS){
                    status = 4;
                }else {
                    status = 3;
                    remake =resultModel.getReason();
                }
                ocBToWingDeliveryTaskMapper.updateByOutBillNo(billNo,remake,status);
            }
        }
        ValueHolderV14 vh=ValueHolderV14Utils.custom(ResultCode.SUCCESS, "接收数据成功", failedList);
        return vh;
    }

    public JSONObject customerQueryOrderOut(String param) {
        if (log.isDebugEnabled()){
            log.debug(" 供应商查询零售发货单发货状态接口{}",param);
        }
        List<CustomerQueryOrderOutModel> resultModelList = JSONObject.parseArray(param, CustomerQueryOrderOutModel.class);
        List<String> outBillNoList = new ArrayList<>();
        List<CustomerQueryOrderResult> resultdList = new ArrayList<>();
        for (CustomerQueryOrderOutModel resultModel : resultModelList) {
            outBillNoList.add(resultModel.getOrder_id());
        }
        //根据出库通知单号查询出库通知单
        List<SgBStoOutNotices> stoOutNoticesList = sgRpcService.queryOutNotices(outBillNoList);
        if (log.isDebugEnabled()){
            log.debug(" 供应商查询零售发货单发货状态接口.stoOutNoticesList {}",JSON.toJSONString(stoOutNoticesList));
        }
        JSONObject valueHolder = new JSONObject();
        valueHolder.put("code",ResultCode.SUCCESS);
        valueHolder.put("message","接受数据成功");
        if (CollectionUtils.isEmpty(stoOutNoticesList)){
            valueHolder.put("code",ResultCode.FAIL);
            valueHolder.put("message","根据出库通知单号查询无结果，请检查！");
            return valueHolder;
        }
        Map<String, SgBStoOutNotices> SgBStoOutNoticeMap = stoOutNoticesList.stream().collect(Collectors.toMap(SgBStoOutNotices::getBillNo, v -> v,
                (v1, v2) -> v1));
        List<String> tidList = stoOutNoticesList.stream().map(o -> o.getSourcecode()).collect(Collectors.toList());
        List<String> OmList = stoOutNoticesList.stream().map(o -> o.getSourceBillNo()).collect(Collectors.toList());

        List<OcBOrder> orderList = ocBOrderMapper.selectList(new QueryWrapper<OcBOrder>().lambda()
                .in(OcBOrder::getBillNo, OmList));

        //根据订单TID查询寻仓结果表  最近换仓成功的
        List<IpBJitxDeliveryRecord> ipBJitxDeliveryRecords = ipBJitxDeliveryRecordMapper.selectList(new QueryWrapper<IpBJitxDeliveryRecord>().lambda()
                .in(IpBJitxDeliveryRecord::getTid, tidList)
                .eq(IpBJitxDeliveryRecord::getTransStatus,2)
                .orderByDesc(IpBJitxDeliveryRecord::getApplicationTime));
        Map<String, IpBJitxDeliveryRecord> tidRecordMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ipBJitxDeliveryRecords)){
            tidRecordMap = ipBJitxDeliveryRecords.stream().collect(Collectors.toMap(IpBJitxDeliveryRecord::getTid, v -> v,
                    (v1, v2) -> v1));
        }

        /**
         * 如果出库通知单已作废，则返回状态为 “0已取消”
         */

        //已拦截已作废已取消的订单返回“0已取消”
        Map<String, OcBOrder> ocBOrderMap = orderList.stream().collect(Collectors.toMap(OcBOrder::getSgBOutBillNo, v -> v,
                (v1, v2) -> v1));
        Map<String, OcBOrder> ocBOrderMapByNo = orderList.stream().collect(Collectors.toMap(OcBOrder::getBillNo, v -> v,
                (v1, v2) -> v1));

        for (CustomerQueryOrderOutModel orderOutModel : resultModelList){
            String outBillNo = orderOutModel.getOrder_id();
            SgBStoOutNotices sgBStoOutNotices = SgBStoOutNoticeMap.get(outBillNo);
            OcBOrder ocBOrder = ocBOrderMap.get(outBillNo);
            CustomerQueryOrderResult customerQueryOrderResult = new CustomerQueryOrderResult();
            customerQueryOrderResult.setOutBillNo(outBillNo);
            OcBOrder bOrder = ocBOrderMapByNo.get(sgBStoOutNotices.getSourceBillNo());
            List<IpBJitxOrder> ipBJitxOrders = ipBJitxOrderMapper.selectList(new QueryWrapper<IpBJitxOrder>().lambda()
                    .eq(IpBJitxOrder::getOrderSn, ocBOrder.getTid()));
            if (CollectionUtils.isNotEmpty(ipBJitxOrders)){
                customerQueryOrderResult.setPlatform_status(ipBJitxOrders.get(0).getOrderStatus());
            }
            customerQueryOrderResult.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
            IpBJitxDeliveryRecord ipBJitxDeliveryRecord = tidRecordMap.get(sgBStoOutNotices.getSourcecode());
            if (ipBJitxDeliveryRecord != null  && ipBJitxDeliveryRecord.getLogisticNumber() != null){
                customerQueryOrderResult.setLogistic_number(ipBJitxDeliveryRecord.getLogisticNumber());
            }else {
                customerQueryOrderResult.setLogistic_number(sgBStoOutNotices.getLogisticNumber());
            }
            if (ipBJitxDeliveryRecord != null  && ipBJitxDeliveryRecord.getEwaybillContent() != null){
                customerQueryOrderResult.setJitx_voucher_content(ipBJitxDeliveryRecord.getEwaybillContent());
            }else {
                customerQueryOrderResult.setJitx_voucher_content(sgBStoOutNotices.getJitxVoucherContent());
            }
            if (StringUtils.isNotEmpty(customerQueryOrderResult.getJitx_voucher_content())) {
                String s = Base64.getEncoder().encodeToString(customerQueryOrderResult.getJitx_voucher_content().getBytes(StandardCharsets.UTF_8));
                customerQueryOrderResult.setJitx_voucher_content(s);
            } else {
                customerQueryOrderResult.setJitx_voucher_content("");
            }

            //如果出库通知单已作废
            if (ocBOrder == null) {
                customerQueryOrderResult.setDelivery_status(0);
                resultdList.add(customerQueryOrderResult);
            }
            //已取消/已作废
            else if (OmsOrderStatus.SYS_VOID.toInteger().equals(ocBOrder.getOrderStatus())){
                customerQueryOrderResult.setDelivery_status(0);
                resultdList.add(customerQueryOrderResult);
            }
            //已拦截
            else if (ocBOrder.getIsInterecept() == 1){
                customerQueryOrderResult.setDelivery_status(0);
                resultdList.add(customerQueryOrderResult);
            }
            //仓库发货或平台发货返回“3已发货”
            else if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus()) || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus())){
                customerQueryOrderResult.setDelivery_status(3);
                resultdList.add(customerQueryOrderResult);
            }
            //未拦截未取消未作废的订单返回“1待发货”
            else {
                customerQueryOrderResult.setDelivery_status(1);
                resultdList.add(customerQueryOrderResult);
            }
        }
        valueHolder.put("data",resultdList);
        return valueHolder;
    }
}
