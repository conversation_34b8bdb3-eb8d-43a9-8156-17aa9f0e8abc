
package com.jackrain.nea.oc.oms;

import com.jackrain.nea.oc.oms.validate.AcFTaxMachineManageSaveValidator;
import com.jackrain.nea.oc.oms.validate.StCInvoiceStrategySaveValidator;
import com.jackrain.nea.tableService.Feature;
import com.jackrain.nea.tableService.feature.FeatureAnnotation;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 * @date 2022/8/23 16:30
 */

@FeatureAnnotation(value = "OmsFeature", description = "订单Feature")
public class OmsFeature extends Feature {
    @Autowired
    private StCInvoiceStrategySaveValidator stInvoiceStrategySaveValidator;

    @Autowired
    private AcFTaxMachineManageSaveValidator acFTaxMachineManageSaveValidator;

    private static final String ACTION_ADD = "ADD";
    private static final String ACTION_SAVE = "SAVE";
    private static final String ACTION_DELETE = "DELETE";
    private static final String ACTION_SUBMIT = "SUBMIT";
    private static final String ACTION_VOID = "VOID";


    @Override
    protected void initialization() {
        //开票策略保存
        addValidator(stInvoiceStrategySaveValidator, (tableName, actionName)
                -> ("st_c_invoice_strategy".equalsIgnoreCase(tableName))
                && (actionName.equalsIgnoreCase(ACTION_ADD)
                || actionName.equals(ACTION_SAVE)));

        //开票策略保存
        addValidator(acFTaxMachineManageSaveValidator, (tableName, actionName)
                -> ("ac_f_tax_machine_manage".equalsIgnoreCase(tableName))
                && (actionName.equalsIgnoreCase(ACTION_ADD)
                || actionName.equals(ACTION_SAVE)));


    }
}
