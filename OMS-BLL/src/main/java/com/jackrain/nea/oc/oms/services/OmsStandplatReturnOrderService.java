package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderIsInterceptEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderWmsStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 通用订单转单发货前退单转单流程
 *
 * @date 2019/7/11
 * @author: ming.fz
 */
@Component
@Slf4j
public class OmsStandplatReturnOrderService {

    @Autowired
    OmsOrderLogService omsOrderLogService;

    @Autowired
    MarkRefundService markRefundService;

    @Autowired
    OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private SgRpcService sgRpcervice;

    @Autowired
    protected IpStandplatOrderService ipStandplatOrderService;

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    /**
     * @param orderInfo
     * @param order
     * @param isAutoMakeup
     * @param user
     * @return
     */
    public String returnOrderService(IpStandplatOrderRelation orderInfo, OcBOrder order,
                                     boolean isAutoMakeup, User user, List<OcBOrderItem> ocBOrderItems) {
        IpBStandplatOrder standplatOrder = orderInfo.getStandplatOrder();
        String stSellerMemo = standplatOrder.getSellerMemo();
        String standplatSellerFlag = standplatOrder.getSellerFlag();
        //全渠道订单卖家备注
        String sellerMemo = order.getSellerMemo();
        String omsOrderFlag = order.getOrderFlag();
        OmsStandplatReturnOrderService bean = ApplicationContextHandle.getBean(OmsStandplatReturnOrderService.class);
        //卖家备注和旗帜是否相同
        if ((stSellerMemo != null && stSellerMemo.equals(sellerMemo)) ||
                (standplatSellerFlag != null && standplatSellerFlag.equals(omsOrderFlag))) {
            //发货前退单状态更新服务
            return bean.saveInReturning(orderInfo, order, isAutoMakeup, user, ocBOrderItems);
        } else {
            //更新卖家备注
            updateSellerNemo(order, user, stSellerMemo, standplatSellerFlag);
            //发货前退单状态更新服务
            return bean.saveInReturning(orderInfo, order, isAutoMakeup, user, ocBOrderItems);
        }
    }

    /**
     * 更新卖家备注
     *
     * @param order
     * @param user
     * @param stSellerMemo
     */
    private void updateSellerNemo(OcBOrder order, User user, String stSellerMemo, String stFlag) {

        OcBOrder ocBOrder = new OcBOrder();
        omsOrderService.setDefaultValueOrder(ocBOrder);
        ocBOrder.setId(order.getId());
        //设置成中间表的卖家备注
        ocBOrder.setSellerMemo(stSellerMemo);
        //标记
        ocBOrder.setOrderFlag(stFlag);
        omsOrderService.updateOrderInfo(ocBOrder);
        //记录操作日志
        String logContext = "卖家旗帜和备注修改成功！";
        saveLog(ocBOrder, user, logContext);
    }

    /**
     * 订单主表日志记录
     *
     * @param ocBOrder
     * @param user
     * @param logContent 日志内容
     */
    private void saveLog(OcBOrder ocBOrder, User user, String logContent) {

        /**
         *  调用日志服务
         *  参数 订单id、订单编号、订单类型、日志信息、参数、错误信息
         */
        try {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.STANDPALT_lOG_TYPE.getKey(),
                    logContent, "", "", user);
        } catch (Exception e) {
        }
    }


    /**
     * 发货前退单主流程
     *
     * @param order
     * @return 返回具体的信息
     */
    @Transactional
    public String saveInReturning(IpStandplatOrderRelation orderInfo, OcBOrder order,
                                  boolean isAutoMakeup, User user, List<OcBOrderItem> ocBOrderItems) {
        /*
        状态处理说明，明细退款状态：refund_status : OcOrderRefundStatusEnum
        null/0：
        1：拦截
        2：取消
        *3：目前无处理逻辑
        4/5：取消拦截
        6：取消
         */

        //转单结果记录
        StringBuffer returnStr = new StringBuffer();
        //通用中间表转单系统备注
        StringBuffer systemRemark = new StringBuffer();
        IpBStandplatOrder standplatOrder = orderInfo.getStandplatOrder();
        List<IpBStandplatOrderItemEx> standplatOrderItemList = orderInfo.getStandPlatOrderItemList();
        //Long omsOrderd = order.getId();
        //List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemList(omsOrderd);
        if (ocBOrderItems == null || ocBOrderItems.size() == 0) {
            return "当前单据没有明细！中间表tid:" + standplatOrder.getTid();
        }
        String tid = orderInfo.getOrderNo();

        returnStr.append("通用发货前退单TID");
        returnStr.append(tid);

        String status = standplatOrder.getStatus();
        //判断平台订单的状态是否为“待发货”或退款完成
        // @2020723 增加主单上状态的判断
        if (TaoBaoOrderStatus.TRADE_CANCELED.equals(status)) {
            // 赋值明细，让走明细的SUCCESS状态
            standplatOrderItemList.forEach(i -> i.setRefundStatus(String.valueOf(OmsOrderRefundStatus.SUCCESS.toInteger())));

        } else if (!(TaoBaoOrderStatus.WAIT_SELLER_SEND_GOODS.equals(status) || TaoBaoOrderStatus.REFUND_FINISHED.equals(status))) {
            systemRemark.append("发货前退款单据状态非待卖家发货 或 退款完成，标记为已转换");
            updateStandplatOrderTransStatus(tid, systemRemark);
            return returnStr.append(systemRemark.toString()).toString();
        }

        for (IpBStandplatOrderItemEx ipBStandplatOrderItemEx : standplatOrderItemList) {
            List<OcBOrderItem> orderItems = getOcBOrderItem(ocBOrderItems, ipBStandplatOrderItemEx);
            //第二次发货前退单会出现组合商品已拆分
            for (OcBOrderItem ocBOrderItem : orderItems) {

                systemRemark.append("  sku_id：");
                systemRemark.append(ipBStandplatOrderItemEx.getSkuId());
                if (ocBOrderItem == null) {
                    returnStr.append(", 全渠道订单明细不存在！");
                    returnStr.append("\r\n");
                    return returnStr.toString();
                } else {
                    //获取中间表明细退款状态
                    // @20200824 bug#************ 多明细的情况下，有的明细退款状态可能是空的
                    int standplatRefundStatus = Objects.isNull(ipBStandplatOrderItemEx.getRefundStatus()) ? -1 : Integer.parseInt(ipBStandplatOrderItemEx.getRefundStatus());
                    //记录通用商品修改前状态
                    Integer formerRefundStatus = ocBOrderItem.getRefundStatus();
                    //获取全渠道订单明细退款状态
                    // int omsRefundStatus = ocBOrderItem.getRefundStatus();
                    if (OmsOrderRefundStatus.WAITSELLERAGREE.toInteger() == standplatRefundStatus ||
                            OmsOrderRefundStatus.WAITBUYERRETURNGOODS.toInteger() == standplatRefundStatus ||
                            OmsOrderRefundStatus.WAITSELLERCONFIRMGOODS.toInteger() == standplatRefundStatus) {
                        //申请退款
                        //更细全渠道主表，是否拦截更新为已拦截（1） 是否退款中更新为1 es推送 并记录操作日志
                        updateIsInterecept(order, user, OmsOrderIsInterceptEnum.YES.getVal(), "拦截成功!是否退款中更新为退款中",
                                OmsOrderRefundStatus.WAITSELLERAGREE.toInteger());
                        //更新对应全渠道订单明细，退款状态更新为退款中 es推送 并记录操作日志
                        updateOrderItemRefundStatus(order, ocBOrderItem, user, standplatRefundStatus);
                        //判断退款状态
                        checkOrderStatus(order, ocBOrderItem, user, tid, systemRemark, formerRefundStatus);
                    } else if (standplatRefundStatus == OmsOrderRefundStatus.SUCCESS.toInteger()) {

                        //退款成功
                        //更细全渠道主表，是否拦截更新为已拦截（1） 是否退款中更新为1 es推送 并记录操作日志
                        updateIsInterecept(order, user, OmsOrderIsInterceptEnum.YES.getVal(), "拦截成功!是否退款中更新为退款中",
                                OmsOrderRefundStatus.WAITSELLERAGREE.toInteger());
                        //更新对应全渠道订单明细，退款状态更新为2 es推送 并记录操作日志
                        updateOrderItemRefundStatus(order, ocBOrderItem, user, OmsOrderRefundStatus.WAITBUYERRETURNGOODS.toInteger());
                        //判断订单状态
                        String str = null;
                        str = refundSuccess(order, user, tid, ocBOrderItem, systemRemark, formerRefundStatus);
                        if (str == null) {
                            //调用反审核失败直接结束本次转单
                            return returnStr.append(systemRemark).toString();
                        }
                        /*判断订单明细中“退款状态”是否存在退款中的明细？
                        若存在，则更新“转换状态”为已转换（2），”系统备注”：同意退款转换成功。
                        若不存在，则更新订单“是否拦截”=0，“是否退款中”=0，更新“转换状态”为已转换（2），”系统备注”：退款成功转换成功。记录操作日志。*/
                        checkOrderStatusRefund(order, user, tid, standplatOrderItemList, systemRemark.append("退款成功转换"));
                    } else if (standplatRefundStatus == OmsOrderRefundStatus.CLOSED.toInteger() ||
                            standplatRefundStatus == OmsOrderRefundStatus.SELLERREFUSEBUYER.toInteger() ||
                            standplatRefundStatus == OmsOrderRefundStatus.UNREFUND.toInteger()) {
                        //取消退款
                        //更新对应的订单明细：“退款状态”更新为未退款（值为0）。记录操作记录。
                        updateOrderItemRefundStatusZero(order, ocBOrderItem, user);

                        //判断订单明细中“退款状态”是否存在退款中的明细？
                        checkOrderStatusRefund(order, user, tid, standplatOrderItemList, systemRemark.append("取消退款转换"));
                    }
                }
            }

        }
        returnStr.append(systemRemark.toString());
        returnStr.append("发货前退单转单success!");

        return returnStr.toString();
    }


    /**
     * 2)	判断订单明细中“退款状态”是否存在退款中的明细
     *
     * @param order
     * @param user
     * @param standplatOrderItemList
     * @return
     */
    private String checkOrderStatusRefund(OcBOrder order, User user, String tid, List<IpBStandplatOrderItemEx> standplatOrderItemList, StringBuffer message) {
        boolean bl = false;
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemList(order.getId());
        //修复bug23613  1、同一个事务中，先update后的数据和后面select的数据是不一致的   2、优化代码防止空指针
        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            throw new NDSException(Resources.getMessage("平台单号 " + tid + "通用发货前退单转单失败，没有可用订单明细！" + message, user.getLocale()));
        }

        if (ocBOrderItems.size() == 1 && OmsOrderRefundStatus.SUCCESS.toInteger() == Integer.parseInt(standplatOrderItemList.get(0).getRefundStatus())) {
            bl = true;
        } else {
            bl = ocBOrderItems.stream().anyMatch(item -> OmsOrderRefundStatus.WAITSELLERAGREE.toInteger() == item.getRefundStatus()
                    || OmsOrderRefundStatus.WAITBUYERRETURNGOODS.toInteger() == item.getRefundStatus()
                    || OmsOrderRefundStatus.WAITSELLERCONFIRMGOODS.toInteger() == item.getRefundStatus());
        }

        if (bl) {
            //转换状态”为已转换（2），”系统备注”：取消退款转换成功。
            return updateStandplatOrderTransStatus(tid, message);
        } else {
            //则更新订单“是否拦截”=0， “是否退款中”=0，
            updateIsInterecept(order, user, OmsOrderIsInterceptEnum.NO.getVal(), "取消拦截成功!是否退款中更新为未退款",
                    OmsOrderRefundStatus.UNREFUND.toInteger());
            //则更新“转换状态”为已转换（2），”系统备注”：取消退款转换成功；记录操作日志。
            return updateStandplatOrderTransStatus(tid, message);
        }

    }

    /**
     * 转换状态”为已转换（2），”系统备注”：取消退款转换成功。
     *
     * @param tid
     * @param message 系统备注内容
     * @return
     */
    private String updateStandplatOrderTransStatus(String tid, StringBuffer message) {
        message.append("成功");
        boolean b = ipStandplatOrderService.updateStandPlatOrderTransStatus(tid,
                TransferOrderStatus.TRANSFERRED, message.toString(), null);
        if (b) {
            return message.toString();
        } else {
            return message.append("失败").toString();
        }
    }

    /**
     * 转换状态”为未转换（0），”系统备注”：取消退款转换成功。
     *
     * @param tid
     * @param message 系统备注内容
     * @return
     */
    private String updateNotStandplatOrderTransStatus(String tid, StringBuffer message) {
        if (message == null) {
            boolean b = ipStandplatOrderService.updateStandPlatOrderTransStatus(tid,
                    TransferOrderStatus.NOT_TRANSFER, null, null);
            return "更新单据状态为未转换系统备注为null成功";
        } else {
            message.append("成功");
            boolean b = ipStandplatOrderService.updateStandPlatOrderTransStatus(tid,
                    TransferOrderStatus.NOT_TRANSFER, message.toString(), null);
            if (b) {
                return message.toString();
            } else {
                return message.append("失败").toString();
            }
        }
    }

    /**
     * 退款成功 	判断订单状态
     *
     * @return
     */
    private String refundSuccess(OcBOrder order, User user, String tid, OcBOrderItem orderItem, StringBuffer message,
                                 Integer formerRefundStatus) {
        //订单状态
        Integer orderStatus = order.getOrderStatus();
        Integer wmsCancelStatus = order.getWmsCancelStatus();
        //	若订单状态是：【1,待审核】【2,缺货】【9,预售】【4,配货中且WMS撤回状态为撤回成功】时，
        if ((OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal() == orderStatus ||
                OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal() == orderStatus ||
                OcOrderCheckBoxEnum.CHECKBOX_ADVANCE_SALE.getVal() == orderStatus) ||
                (OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal() == orderStatus &&
                        wmsCancelStatus == OcOrderWmsStatus.WMS_SUCCESS.getVal())) {
            //调用【标记退款完成服务】
            boolean b = callMarkRefundService(orderItem, user, message);
            if (!b) {
                throw new NDSException(Resources.getMessage("发货前退单调用标记退款服务失败！" + message, user.getLocale()));
            }
        }

        //	若订单状态时：【3，已审核】或配货中，调用【反审核服务】。
        //	若反审核成功，则调用【标记退款完成服务】；
        //	若反审核失败，则更新“转换状态”为未转换（0），”系统备注”：订单反审核失败，等待重新转换。程序终止。
        if (OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal() == orderStatus
                || OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal() == orderStatus) {
            boolean bl = callTheAuditService(order, orderItem, user, message, formerRefundStatus, tid);
            if (bl) {
                //调用wms 撤回服务撤回成功 调用【标记退款完成服务】
                boolean b = callMarkRefundService(orderItem, user, message);
                if (!b) {
                    throw new NDSException(Resources.getMessage("发货前退单调用标记退款服务失败！" + message, user.getLocale()));
                }
                return message.toString();
            } else {
                message.append("订单反审核失败，等待重新转换。程序终止");
                updateNotStandplatOrderTransStatus(tid, null);
                return null;
            }
        }

        /*//若订单状态是：配货中且WMS撤销状态为未撤销或撤销失败，则调用【WMS撤销服务】
        if (OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal() == orderStatus &&
                (OcOrderWmsStatus.WMS_NOTHING.getVal() == wmsCancelStatus ||
                        OcOrderWmsStatus.WMS_FALSE.getVal() == wmsCancelStatus)) {
            List<OcBOrder> ocBOrderList = new ArrayList<>();
            ocBOrderList.add(order);
            //调用WMS撤回服务
            ValueHolderV14 execute =
                    sgRpcervice.invoildOutgoingNotice(ocBOrderList, user);
            int code = Tools.getInt(execute.getCode(), -1);
            if (code == 0) {
                SgR3BaseResult data = (SgR3BaseResult) execute.getData();
                JSONArray dataArr = data.getDataArr();
                Integer code1 = dataArr.getJSONObject(0).getInteger("code");
                if (code1 == 0) {
                    //调用wms 撤回服务撤回成功 调用【标记退款完成服务】
                    callMarkRefundService(orderItem, user);
                } else {
                    String strMessage = "同用退款转换失败，因为订单已转WMS并撤销失败";
                    //调用wms 撤回服务撤回失败 则更新“转换状态”为已转换（2），”系统备注”：同意退款转换失败，因为订单已转WMS并撤销失败。
                    ipStandplatOrderService.updateStandplatOrderTransStatus(tid, TransferOrderStatus.TRANSFERRED,
                            strMessage);

                    if (log.isDebugEnabled()) {
                        log.debug("同用退款转换失败，因为订单已转WMS并撤销失败");
                    }

                    return strMessage;
                }
            }
        }*/

        //	若订单状态是：【21,传wms中】，则更新“转换状态”为未转换（0），”系统备注”：null
        if (IsOrderStatusToWms(message, orderStatus)) {
            message.append(updateNotStandplatOrderTransStatus(tid, null));
            return message.toString();
        }
        return "通用转单 - 流程退款成功 \t判断订单状态通过！";
    }

    /**
     * 调用【标记退款完成服务】
     *
     * @param orderItem
     * @param user
     */
    private boolean callMarkRefundService(OcBOrderItem orderItem, User user, StringBuffer message) {
       /* //当前订单获取所有的明细
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemList(order.getId());
        if (ocBOrderItems == null) {
            if (log.isDebugEnabled()) {
                log.error("通用发货前退单转单，退款成功-全渠道订单 id: " + order.getId() + "没有可用订单明细！");
            }
        }
        StringBuilder itemIdStr = new StringBuilder();
        for (int i = 0; i < ocBOrderItems.size(); i++) {
            if (i == ocBOrderItems.size() - 1) {
                itemIdStr.append(ocBOrderItems.get(i).getId());
                break;
            }
            itemIdStr.append(ocBOrderItems.get(i).getId());
            itemIdStr.append(",");
        }
        JSONObject param = new JSONObject();
        param.put("IDS", itemIdStr);
        //调用【标记退款完成服务】
        ValueHolderV14 valueHolderV14 = markRefundService.markRefund(param, user);
        if (valueHolderV14.getCode() == -1) {
            if (log.isDebugEnabled()) {
                log.debug("mfz-通用-全渠道订单 id: " + order.getId() + "调用【标记退款完成服务】失败！");
            }
        }*/

        JSONObject param = new JSONObject();
        param.put("IDS", orderItem.getId());
        try {
            //调用【标记退款完成服务】
            ValueHolderV14 valueHolderV14 = markRefundService.markRefund(param, user);
            if (valueHolderV14.getCode() == -1) {
                message.append(valueHolderV14.getMessage());
            } else {
                return true;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("标记退款完成服务,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            message.append(e);
        }
        return false;
    }

    /**
     * 判断订单状态 返回具体信息
     *
     * @return
     */
    private String checkOrderStatus(OcBOrder order, OcBOrderItem ocBOrderItem, User user, String tid,
                                    StringBuffer message, Integer formerRefundStatus) {
        //订单状态
        Integer orderStatus = order.getOrderStatus();
        Integer wmsCancelStatus = order.getWmsCancelStatus();
        //	若订单状态是：【1,未确 认】【2,缺 货】 【9,预售】 【4,配货中且WMS撤回状态为撤回成功】，
        // 则更新“转换状态”为已转换（2），”系统备注”：买家申请退款转换完成。
        if ((OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal() == orderStatus ||
                OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal() == orderStatus ||
                OcOrderCheckBoxEnum.CHECKBOX_ADVANCE_SALE.getVal() == orderStatus) ||
                (OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal() == orderStatus &&
                        wmsCancelStatus == OcOrderWmsStatus.WMS_SUCCESS.getVal())) {
            //更新“转换状态”为已转换（2）
            message.append("买家申请退款转换");
            return updateStandplatOrderTransStatus(tid, message);
        }

        /* 	若订单状态时：【3,已审核】或配货中，则调用【反审核服务】，
        	若反审核成功，则更新“转换状态”为已转换（2），”系统备注”：买家申请退款转换完成。
        	若反审核失败，则此商品的退款状态回滚到原始状态，则更新“转换状态”为未转换（0），”系统备注”：“订单反审核失败，等待重新转换”
        */
        if (OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal() == orderStatus ||
                OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal() == orderStatus) {
            callTheAuditService(order, ocBOrderItem, user, message, formerRefundStatus, tid);
            return message.toString();
        }

        //	若订单状态是：配货中且WMS撤销状态为未撤销或撤销失败，则调用【WMS撤销服务】
        /*if (OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal() == orderStatus &&
                (OcOrderWmsStatus.WMS_NOTHING.getVal() == wmsCancelStatus ||
                        OcOrderWmsStatus.WMS_FALSE.getVal() == wmsCancelStatus)) {
            List<OcBOrder> ocBOrderList = new ArrayList<>();
            ocBOrderList.add(order);
            //调用撤销服务
            ValueHolderV14 execute =
                    sgRpcervice.invoildOutgoingNotice(ocBOrderList, user);
            int code = Tools.getInt(execute.getCode(), -1);
            if (code == 0) {
                SgR3BaseResult data = (SgR3BaseResult) execute.getData();
                JSONArray dataArr = data.getDataArr();
                Integer code1 = dataArr.getJSONObject(0).getInteger("code");
                if (code1 == 0) {
                    //调用wms 撤回服务撤回成功
                    message.append("买家申请退款转换");
                    return updateStandplatOrderTransStatus(tid, message);
                } else {
                    //调用wms 撤回服务撤回失败
                    message.append("订单已转WMS并撤销失败,但买家申请退款转换");
                    return updateStandplatOrderTransStatus(tid, message);
                }
            } else {
                //调用wms 撤回服务撤回成功
                message.append("订单已转WMS并撤销失败,但买家申请退款转换");
                return updateStandplatOrderTransStatus(tid, message);
            }
        }*/
        //	若订单状态是：【21,传wms中】或配货中，则更新“转换状态”为未转换（0），”系统备注”：null
        if (IsOrderStatusToWms(message, orderStatus)) {
            message.append(updateNotStandplatOrderTransStatus(tid, null));
            return message.toString();
        }

        return "申请退款，判断订单状态，没有符合的单据";
    }

    /**
     * 调用【反审核服务】。
     * 若反审核成功，则调用【标记退款完成服务】；
     * 若反审核失败，则更新“转换状态”为未转换（0），”系统备注”：订单反审核失败，等待重新转换。程序终止。
     *
     * @param order
     * @param ocBOrderItem
     * @param user
     * @param message
     * @param formerRefundStatus
     * @param tid
     * @return
     */
    private boolean callTheAuditService(OcBOrder order, OcBOrderItem ocBOrderItem, User user, StringBuffer message,
                                        Integer formerRefundStatus, String tid) {
        try {
            boolean b = ocBOrderTheAuditService.updateOrderInfo(user, new ValueHolderV14(), order.getId(), LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            if (b) {
                message.append("买家申请退款转换");
                updateStandplatOrderTransStatus(tid, message);
            } else {
                updateOrderItemRefundStatus(order, ocBOrderItem, user, formerRefundStatus);
                message.append(updateNotStandplatOrderTransStatus(tid, null));
            }
            return b;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    /**
     * 	若订单状态是：【21,传wms中】或配货中，则更新“转换状态”为未转换（0），”系统备注”：订单状态为转wms中，等待订单状态变化后再转换。
     *
     * @param message
     * @param orderStatus
     * @return
     */
    private boolean IsOrderStatusToWms(StringBuffer message, Integer orderStatus) {

        if (OcOrderCheckBoxEnum.CHECKBOX_PENDING_WMS.getVal() == orderStatus ||
                OcOrderCheckBoxEnum.CHECKBOX_PENDING_ALLOCATED.getVal() == orderStatus) {
            message.append("订单状态为转wms中或配货中，等待订单状态变化后再转换");
            return true;
        }
        return false;
    }

    /**
     * 拦截当前订单、es推送、日志记录 是否退款中(inTerecept)
     *
     * @param order
     */
    public void updateIsInterecept(OcBOrder order, User user, int inTerecept, String content, int orderRefundStatus) {

        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(order.getId());
        updateOrder.setIsInreturning(orderRefundStatus);
        int i = ocBOrderMapper.updateById(updateOrder);
        //是否已经拦截 订单hold 或释放hold单调用HOLD单接口
        updateOrder.setIsInterecept(inTerecept);//订单hold 或释放hold单调用HOLD单接口
        ocBOrderHoldService.holdOrUnHoldOrder(updateOrder, OrderHoldReasonEnum.REFUND_HOLD);
        if (i > 0) {

            //日志记录
            //记录操作日志
            String logContext = "订单id：" + order.getId() + content;
            saveLog(order, user, logContext);
        }
    }

    /**
     * 修改明细退款状态为退款中 es推送 日志记录
     *
     * @param user
     */
    private void updateOrderItemRefundStatus(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem, User user,
                                             Integer refundStatus) {
        JSONObject updateParam = new JSONObject();
        updateParam.put("oc_b_order_id", ocBOrderItem.getOcBOrderId());
        updateParam.put("id", ocBOrderItem.getId());
        updateParam.put("refund_status", refundStatus);
        ocBOrderItem.setRefundStatus(refundStatus);
        //设置默认字段
        setUpdateDelaultValue(user, updateParam, ocBOrderItem);
        int i = ocBOrderItemMapper.updateRecord(updateParam);

        if (i > 0) {
            //es 推送
            //esOrderItemPush(ocBOrderItem, ocBOrder.getId());

            //日志记录
            //记录操作日志
            String logContext = "订单明细id:" + ocBOrderItem.getId() + "已更新明细退款状态为:" +
                    OmsOrderRefundStatus.toStatusString(refundStatus) + refundStatus;
            saveLog(ocBOrder, user, logContext);
        }
    }

    /**
     * 修改明细退款状态为为退款 es推送 日志记录
     *
     * @param user
     */
    private void updateOrderItemRefundStatusZero(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem, User user) {
        JSONObject updateParam = new JSONObject();
        //设置默认字段
        updateParam.put("oc_b_order_id", ocBOrderItem.getOcBOrderId());
        updateParam.put("id", ocBOrderItem.getId());
        updateParam.put("refund_status", OmsOrderRefundStatus.UNREFUND.toInteger());
        ocBOrderItem.setRefundStatus(OmsOrderRefundStatus.UNREFUND.toInteger());
        setUpdateDelaultValue(user, updateParam, ocBOrderItem);
        int i = ocBOrderItemMapper.updateRecord(updateParam);

        if (i > 0) {
            //es 推送
            //esOrderItemPush(ocBOrderItem, ocBOrder.getId());

            //日志记录
            //记录操作日志
            String logContext = "订单明细id:" + ocBOrderItem.getId() + "已更新明细退款状态为未退款（0）";
            saveLog(ocBOrder, user, logContext);
        }
    }

    /**
     * 设置默认修改对象默认字段
     *
     * @param user
     * @param updateParam
     */
    private void setUpdateDelaultValue(User user, JSONObject updateParam, OcBOrderItem ocBOrderItem) {
        updateParam.put("modifierename", user.getEname());
        updateParam.put("modifiername", user.getName());
        updateParam.put("modifieddate", new Date(System.currentTimeMillis()));
        ocBOrderItem.setModifierename(user.getEname());
        ocBOrderItem.setModifiername(user.getName());
        ocBOrderItem.setModifieddate(new Date(System.currentTimeMillis()));
    }

    /**
     * 通用中间表明细匹配对应的全渠道订单明细 匹配到了返回对应的明细
     *
     * @param ocBOrderItems
     * @param ipBStandplatOrderItemEx
     * @return 返回对应的明细 没有匹配到返回null
     */
    private List<OcBOrderItem> getOcBOrderItem(List<OcBOrderItem> ocBOrderItems,
                                               IpBStandplatOrderItemEx ipBStandplatOrderItemEx) {
        List<OcBOrderItem> orderItems = new ArrayList<>();
        String numIid = ipBStandplatOrderItemEx.getNumIid();
        String skuId = ipBStandplatOrderItemEx.getSkuId();
        String outerSkuId = "" + numIid + skuId;
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            String numIid1 = ocBOrderItem.getNumIid();
            String skuNumiid = ocBOrderItem.getSkuNumiid();
            String psCSkuEcode = numIid1 + skuNumiid;
            if (outerSkuId.equals(psCSkuEcode)) {
                orderItems.add(ocBOrderItem);
            }
        }
        return orderItems;
    }

    /**
     * 全渠道订单表明细es推送
     *
     * @param ocBOrderItem
     */
    private void esOrderItemPush(OcBOrderItem ocBOrderItem, Long orderId) {
//        try {
//            Boolean document = SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
//                    OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME,
//                    ocBOrderItemMapper.queryOrderItemById(ocBOrderItem.getId(), orderId),
//                    ocBOrderItem.getId(), orderId);
//
//            if (!document) {
//                if (log.isDebugEnabled()) {
//                    log.error("mfz-通用发货前退单明细id为 ->" + ocBOrderItem.getId() + " ES推送失败！");
//                }
//            }
//        } catch (IOException e) {
//            if (log.isDebugEnabled()) {
//                log.error("mfz-通用发货前退单明细id为 ->" + ocBOrderItem.getId() + " ES推送失败！" + e);
//            }
//
//        }
    }

    /**
     * 全渠道订单主表es推送
     *
     * @param ocBOrder
     */
 /*   private void esOrderPush(OcBOrder ocBOrder) {
        try {
            Boolean document = SpecialElasticSearchUtil.indexDocument(OC_B_ORDER_INDEX_NAME, OC_B_ORDER_TYPE_NAME,
                    ocBOrderMapper.selectByID(ocBOrder.getId()), ocBOrder.getId());

            if (!document) {
                if (log.isDebugEnabled()) {
                    log.error("mfz-通用发货前退单主表id为 ->" + ocBOrder.getId() + " ES推送失败！");
                }
            }
        } catch (IOException e) {
            if (log.isDebugEnabled()) {
                log.error("mfz-通用发货前退单明细id为 ->" + ocBOrder.getId() + " ES推送失败！");
            }

        }
    }*/
}