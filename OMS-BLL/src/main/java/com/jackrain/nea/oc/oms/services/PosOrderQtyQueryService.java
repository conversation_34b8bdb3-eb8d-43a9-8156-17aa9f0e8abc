package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.result.PosOrderQtyQueryItemResult;
import com.jackrain.nea.oc.oms.model.result.PosOrderQtyQueryResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName : PosOrderQtyQueryService  
 * @Description : pos查询零售发货单发货数量
 * <AUTHOR>  YCH
 * @Date: 2021-09-08 12:08  
 */
@Slf4j
@Component
public class PosOrderQtyQueryService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    public ValueHolderV14<PosOrderQtyQueryResult> posOrderQtyQuery(String tid) {

        ValueHolderV14<PosOrderQtyQueryResult> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS,"success");
        try{
            //根据TID平台单号 查询出多张零售发货单
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectList(new QueryWrapper<OcBOrder>().lambda().eq(OcBOrder::getSourceCode, tid)
                    .eq(OcBOrder::getIsactive,"Y"));
            if (CollectionUtils.isEmpty(ocBOrders)){
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("根据平台单号：" + tid + "查询无可用零售发货单！");
                return holderV14;
            }

            PosOrderQtyQueryResult posOrderQtyQueryResult = new PosOrderQtyQueryResult();
            List<Long> idList = ocBOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());
            //根据主表ID查询明细 （此时明细可能包含重复sku）
            List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().lambda().in(OcBOrderItem::getOcBOrderId, idList));

            //统计零售发货单发货状态为平台发货或
            int count = 0;
            int voidSum = 0;

            Map<String, PosOrderQtyQueryItemResult> skuResultMap = new HashMap<>();
            for (OcBOrder ocBOrder : ocBOrders){
                List<OcBOrderItem> itemList = ocBOrderItems.stream().filter(item -> item.getOcBOrderId().equals(ocBOrder.getId())).collect(Collectors.toList());
                if (OmsOrderStatus.SYS_VOID.equals(ocBOrder.getOrderStatus())){
                    voidSum++;
                }
                //判断主表状态是否为仓库发货 || 平台发货  累加数量， 否则默认数量为0
                if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus()) || OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus())){
                    for (OcBOrderItem item : itemList){

                        PosOrderQtyQueryItemResult qtyQueryResult = skuResultMap.get(item.getPsCSkuEcode());
                        if (qtyQueryResult != null){
                            qtyQueryResult.setQtyOut(qtyQueryResult.getQtyOut().add(item.getQty()));
                            skuResultMap.put(item.getPsCSkuEcode(),qtyQueryResult);
                        }else {
                            PosOrderQtyQueryItemResult result = new PosOrderQtyQueryItemResult();
                            result.setQtyOut(item.getQty());
                            result.setPsCProEcode(item.getPsCProEcode());
                            result.setPsCProEname(item.getPsCProEname());
                            result.setPsCProId(item.getPsCProId());
                            result.setPsCSkuEcode(item.getPsCSkuEcode());
                            result.setPsCSkuId(item.getPsCSkuId());
                            skuResultMap.put(item.getPsCSkuEcode(),result);
                        }
                    }
                    count++;
                }else {
                    for (OcBOrderItem item : itemList){
                        PosOrderQtyQueryItemResult qtyQueryResult = skuResultMap.get(item.getPsCSkuEcode());
                        if (qtyQueryResult == null){
                            PosOrderQtyQueryItemResult result = new PosOrderQtyQueryItemResult();
                            result.setQtyOut(BigDecimal.ZERO);
                            result.setPsCProEcode(item.getPsCProEcode());
                            result.setPsCProEname(item.getPsCProEname());
                            result.setPsCProId(item.getPsCProId());
                            result.setPsCSkuEcode(item.getPsCSkuEcode());
                            result.setPsCSkuId(item.getPsCSkuId());
                            skuResultMap.put(item.getPsCSkuEcode(),result);
                        }
                    }
                }
            }
            List<PosOrderQtyQueryItemResult> resultList = new ArrayList<>();
            skuResultMap.forEach((k,v) ->{
                resultList.add(v);
            });

            posOrderQtyQueryResult.setReturnStatus("3");
            if (count == 0 || voidSum == ocBOrders.size()){
                posOrderQtyQueryResult.setReturnStatus("1");
            }else if (count == ocBOrders.size()){
                posOrderQtyQueryResult.setReturnStatus("2");
            }else if (count != 0 && (count+voidSum) == ocBOrders.size()){
                posOrderQtyQueryResult.setReturnStatus("2");
            }
            posOrderQtyQueryResult.setPosOrderQtyQueryItemResultList(resultList);

            holderV14.setData(posOrderQtyQueryResult);
            return holderV14;
        }catch (Exception ex){
            log.error(LogUtil.format("零售发货单查询发货数量异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("零售发货单查询发货数量异常:" + ex.getMessage());
            return holderV14;
        }

    }

}
