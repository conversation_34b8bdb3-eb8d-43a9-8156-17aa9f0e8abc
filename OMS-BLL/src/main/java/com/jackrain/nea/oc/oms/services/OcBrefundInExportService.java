package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.model.enums.MatchingSate;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderItemExtend;
import com.jackrain.nea.oc.oms.model.result.OcRefundExportResult;
import com.jackrain.nea.oc.oms.model.result.QueryOcRefundInResult;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInExt;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItemExtend;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 退货入库导出
 *
 * @author: 夏继超
 * @since: 2019/6/6
 * create at : 2019/6/6 15:09
 */
@Slf4j
@Component
public class OcBrefundInExportService {
    @Autowired
    OcBRefundInMapper refundInMapper;
    @Autowired
    OcBRefundInProductItemMapper productItemMapper;
    @Autowired
    ReturnStorageListService storageListService;

    public ValueHolderV14 exportList(String jsonStr, User loginUser) {
        ValueHolderV14 resultHolderV14 = new ValueHolderV14(ResultCode.SUCCESS, "退货入库导出成功！");
        log.debug(this.getClass().getName() + "[llf]退货管理导出查询入参" + jsonStr);
        JSONObject jo = JSONObject.parseObject(jsonStr);
        List<OcBRefundIn> refundIns = Lists.newArrayList();
        List<OcBRefundInProductItem> productItems = Lists.newArrayList();
        OcRefundExportResult data = new OcRefundExportResult();
        //判断是否为 勾选导出
        if (jo.containsKey("idList")) {
            //OcBOrderExtend ocBOrderExtend = new OcBOrderExtend();
            OcBRefundInExt refundInExt = new OcBRefundInExt();
            refundInExt.setIdList((List<Long>) jo.get("idList"));
            resultHolderV14 = queryList(refundInExt);
        } else {
            System.out.println(jsonStr);
            Integer integer = refundInMapper.selectCount(null);
            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            jsonObject.put("currentPage", 1);
            jsonObject.put("pageSize", integer);
            String s = jsonObject.toJSONString();
            //请求条件查询
            ValueHolderV14 holderV14 = storageListService.returnStorageList(s, loginUser);
            if (holderV14.getData() == null) {
                return holderV14;
            }
            //获取返回的数据
            QueryOcRefundInResult refundInResult = (QueryOcRefundInResult) holderV14.getData();
            List<OcBRefundInExt> queryOrderResultList = refundInResult.getQueryResult();
            List itemResultList = Lists.newArrayList();
            //获取所有订单id
            List<Long> refundIdList = queryOrderResultList.parallelStream().map(OcBRefundInExt::getId).collect(Collectors.toList());
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.in("oc_b_refund_in_id", refundIdList);
            List<OcBRefundInProductItem> productItemList = productItemMapper.selectList(wrapper);
            //置换字段
            List<OcBRefundInProductItemExtend> ocBOrderItemExtends = this.changeItemChildClassList(productItemList);
            //拼接返回对象
            //拼接返回对象
            OcRefundExportResult data1 = new OcRefundExportResult();
            data1.setOcBRefundIns(queryOrderResultList);
            data1.setProductItems(ocBOrderItemExtends);
            resultHolderV14.setData(data1);
        }
        return resultHolderV14;
    }

    public ValueHolderV14 queryList(OcBRefundInExt ocBRefundInExt) {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "成功！");

        //查询符合的主表
        QueryWrapper<OcBRefundIn> queryOcBRefund = new QueryWrapper<>();
        queryOcBRefund.in("id", ocBRefundInExt.getIdList());
        List<OcBRefundIn> ocBRefundInList = refundInMapper.selectList(queryOcBRefund);
        List<Long> ocBRefundInIdList = ocBRefundInList.parallelStream().map(OcBRefundIn::getId).collect(Collectors.toList());
        //查询符合的明细表
        OcBOrderItemExtend ocBOrderItemExtend = new OcBOrderItemExtend();
        QueryWrapper<OcBRefundInProductItem> queryOcBOrderItem = new QueryWrapper<>();
        queryOcBOrderItem.in("oc_b_refund_in_id", ocBRefundInIdList);
        List<OcBRefundInProductItem> productItemList = productItemMapper.selectList(queryOcBOrderItem);
        //置换字段
        List<OcBRefundInExt> ocBRefundInExts = this.changeOrderChildClassList(ocBRefundInList);
        List<OcBRefundInProductItemExtend> ocBOrderItemExtends = this.changeItemChildClassList(productItemList);
        //拼接返回对象
        OcRefundExportResult data = new OcRefundExportResult();
        data.setOcBRefundIns(ocBRefundInExts);
        data.setProductItems(ocBOrderItemExtends);
        holderV14.setData(data);
        return holderV14;
    }

    //明細表的字段置換
    private List<OcBRefundInProductItemExtend> changeItemChildClassList(List<OcBRefundInProductItem> productItemList) {
        List<OcBRefundInProductItemExtend> inProductItemExtends = new ArrayList<>();
        for (int i = 0; i < productItemList.size(); i++) {
            OcBRefundInProductItemExtend inProductItemExtend = new OcBRefundInProductItemExtend();
            OcBRefundInProductItem productItem = productItemList.get(i);
            if (productItem.getIsMatch() != null) {
                if (productItem.getIsMatch() == 0) {
                    inProductItemExtend.setIsMatchName("否");
                } else {
                    inProductItemExtend.setIsMatchName("是");
                }
            }
            if (productItem.getIsWithoutOrig() != null) {
                if (productItem.getIsWithoutOrig() == 0) {
                    inProductItemExtend.setIsWithoutOrigEname("否");
                } else {
                    inProductItemExtend.setIsWithoutOrigEname("是");
                }
            }
            if (productItem.getIsGenAdjust() != null) {
                if (productItem.getIsGenAdjust() == 0) {
                    inProductItemExtend.setIsGenAdjustEname("否");
                } else {
                    inProductItemExtend.setIsGenAdjustEname("是");
                }
            }
            if (StringUtils.isNotBlank(productItem.getProductMark())) {
                if ("0".equals(productItem.getProductMark())) {
                    inProductItemExtend.setProductMarkName("次品");
                } else {
                    inProductItemExtend.setProductMarkName("正品");
                }
            }

            inProductItemExtend.setId(productItem.getId());
            inProductItemExtend.setOcBRefundInId(productItem.getOcBRefundInId());
            inProductItemExtend.setGbcode(productItem.getGbcode());
            inProductItemExtend.setPsCSkuId(productItem.getPsCSkuId());
            inProductItemExtend.setPsCSkuEcode(productItem.getPsCSkuEcode());
            inProductItemExtend.setRealSkuId(productItem.getRealSkuId());
            inProductItemExtend.setRealSkuEcode(productItem.getRealSkuEcode());
            inProductItemExtend.setPsCProId(productItem.getPsCProId());
            inProductItemExtend.setPsCProEcode(productItem.getPsCProEcode());
            inProductItemExtend.setPsCProEname(productItem.getPsCProEname());
            inProductItemExtend.setScBInId(productItem.getScBInId());
            inProductItemExtend.setProductMark(productItem.getProductMark());
            inProductItemExtend.setIsWithoutOrig(productItem.getIsWithoutOrig());
            inProductItemExtend.setQty(productItem.getQty());
            inProductItemExtend.setOcBReturnOrderId(productItem.getOcBReturnOrderId());
            inProductItemExtend.setIsMatch(productItem.getIsMatch());
            inProductItemExtend.setIsGenAdjust(productItem.getIsGenAdjust());
            inProductItemExtend.setVersion(productItem.getVersion());
            inProductItemExtend.setOwnerename(productItem.getOwnerename());
            inProductItemExtend.setModifierename(productItem.getModifierename());
            inProductItemExtend.setAdClientId(productItem.getAdClientId());
            inProductItemExtend.setAdOrgId(productItem.getAdOrgId());
            inProductItemExtend.setCreationdate(productItem.getCreationdate());
            inProductItemExtend.setModifieddate(productItem.getModifieddate());
            inProductItemExtend.setOwnerid(productItem.getOwnerid());
            inProductItemExtend.setOwnername(productItem.getOwnername());
            inProductItemExtend.setModifierid(productItem.getModifierid());
            inProductItemExtend.setModifiername(productItem.getModifiername());
            inProductItemExtend.setIsactive(productItem.getIsactive());
            inProductItemExtend.setGbcodeActual(productItem.getGbcodeActual());// 新增实收国标码
            inProductItemExtends.add(inProductItemExtend);
        }
        return inProductItemExtends;
    }

    //主表的字段的置换
    private List<OcBRefundInExt> changeOrderChildClassList(List<OcBRefundIn> ocBRefundInList) {
        List<OcBRefundInExt> ocBRefundInExts = new ArrayList<>();
        for (int i = 0; i < ocBRefundInList.size(); i++) {
            OcBRefundIn record = ocBRefundInList.get(i);
            OcBRefundInExt ocBRefundInExt = new OcBRefundInExt();
            ocBRefundInExt.setItemList(Lists.newArrayList());
            if (record.getInStatus() != null) {
                if (ReturnStatus.WAREHOUSING_AND_SCRAP.toInteger().equals(record.getInStatus())) {
                    ocBRefundInExt.setInvalidState("已作废");
                } else {
                    ocBRefundInExt.setInvalidState("未作废");
                }
            } else {
                ocBRefundInExt.setInvalidState("未作废");
            }
            if (record.getInStatus() != null) {
                if (ReturnStatus.WAITING_FOR_STORAGE.toInteger().equals(record.getInStatus())) {
                    ocBRefundInExt.setWarehousingStatus("等待入库");
                } else if (ReturnStatus.WAREHOUSING.toInteger().equals(record.getInStatus())) {
                    ocBRefundInExt.setWarehousingStatus("已入库");
                } else if (ReturnStatus.WAREHOUSING_AND_SCRAP.toInteger().equals(record.getInStatus())) {
                    ocBRefundInExt.setWarehousingStatus("入库作废");
                } else {
                    ocBRefundInExt.setWarehousingStatus("未关联退货单");
                }
            }
            if (record.getMatchStatus() != null) {
                if (MatchingSate.PARTIAL_MATCHING.toInteger() == record.getMatchStatus()) {
                    ocBRefundInExt.setMatchstatusname("部分匹配");
                } else if (MatchingSate.UNMATCHED.toInteger() == record.getMatchStatus()) {
                    ocBRefundInExt.setMatchstatusname("未匹配");
                } else if (MatchingSate.NOT_MATCH.toInteger() == record.getMatchStatus()) {
                    ocBRefundInExt.setMatchstatusname("无需匹配");
                } else {
                    ocBRefundInExt.setMatchstatusname("全部匹配");
                }
            } else {
                ocBRefundInExt.setMatchstatusname("状态值为空");
            }
            QueryWrapper query = new QueryWrapper();
            query.eq("oc_b_refund_in_id", record.getId());
            BigDecimal qtyAll = new BigDecimal(0);
            List<OcBRefundInProductItem> list1 = productItemMapper.selectList(query);
            for (OcBRefundInProductItem productItem : list1) {
                qtyAll = qtyAll.add(productItem.getQty());
            }
            ocBRefundInExt.setQtyAll(qtyAll);
            ocBRefundInExt.setId(record.getId());
            ocBRefundInExt.setBatchNo(record.getBatchNo());
            ocBRefundInExt.setOcBRefundBatchId(record.getOcBRefundBatchId());
            ocBRefundInExt.setAllSku(record.getAllSku());
            ocBRefundInExt.setUserId(record.getUserId());
            ocBRefundInExt.setSourceCode(record.getSourceCode());
            ocBRefundInExt.setUserNick(record.getUserNick());
            ocBRefundInExt.setInStoreId(record.getInStoreId());
            ocBRefundInExt.setInStoreEcode(record.getInStoreEcode());
            ocBRefundInExt.setInStoreEname(record.getInStoreEname());
            ocBRefundInExt.setRemark(record.getRemark());
            ocBRefundInExt.setLogisticNumber(record.getLogisticNumber());
            ocBRefundInExt.setReceiverName(record.getReceiverName());
            ocBRefundInExt.setReceiverMobile(record.getReceiverMobile());
            ocBRefundInExt.setReceiverAddress(record.getReceiverAddress());
            ocBRefundInExt.setCpCLogisticsId(record.getCpCLogisticsId());
            ocBRefundInExt.setCpCLogisticsEcode(record.getCpCLogisticsEcode());
            ocBRefundInExt.setCpCLogisticsEname(record.getCpCLogisticsEname());
            ocBRefundInExt.setOrigOrderNo(record.getOrigOrderNo());
            ocBRefundInExt.setInStatus(record.getInStatus());
            ocBRefundInExt.setMatchStatus(record.getInStatus());
            ocBRefundInExt.setSpecialType(record.getSpecialType());
            ocBRefundInExt.setVersion(record.getVersion());
            ocBRefundInExt.setOwnerename(record.getOwnerename());
            ocBRefundInExt.setModifierename(record.getModifierename());
            ocBRefundInExt.setAdClientId(record.getAdClientId());
            ocBRefundInExt.setAdOrgId(record.getOwnerid());
            ocBRefundInExt.setMatchedTime(record.getMatchedTime());
            String format = DateFormatUtils.format(record.getCreationdate(), "yyyy-MM-dd HH:mm:ss");
            ocBRefundInExt.setCreateTime(format);
            try {
                ocBRefundInExt.setCreationdate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(format));
                String format1 = DateFormatUtils.format(record.getModifieddate(), "yyyy-MM-dd HH:mm:ss");
                ocBRefundInExt.setUpdateTime(format1);
                ocBRefundInExt.setModifieddate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(format1));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            ocBRefundInExt.setOwnerid(record.getOwnerid());
            ocBRefundInExt.setOwnername(record.getOwnername());
            ocBRefundInExt.setModifierid(record.getModifierid());
            ocBRefundInExt.setModifiername(record.getModifiername());
            ocBRefundInExt.setIsactive(record.getIsactive());
            ocBRefundInExt.setMatcher(record.getMatcher());
            ocBRefundInExt.setRemarkHandle(record.getRemarkHandle());
            ocBRefundInExt.setCpCPhyWarehouseId(record.getCpCPhyWarehouseId());
            ocBRefundInExt.setCpCPhyWarehouseEcode(record.getCpCPhyWarehouseEcode());
            ocBRefundInExt.setCpCPhyWarehouseEname(record.getCpCPhyWarehouseEname());
            ocBRefundInExts.add(ocBRefundInExt);
        }
        return ocBRefundInExts;
    }
}
