package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.hub.enums.OrderStatusEnum;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPaymentFiMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.GetOrderForCancelModel;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.result.OcBOrderPlatResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.retail.service.RetailNotifyService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StdRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.*;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 订单取消
 *
 * @date 2019/3/7
 * @author: ming.fz
 */
@Component
@Slf4j
public class OcBOrderOffService {

    @Autowired
    private OcBJitxDealerOrderTaskService ocBJitxDealerOrderTaskService;
    @Autowired
    OcBOrderMapper ocBorderMapper;

    @Autowired
    OcBOrderPaymentFiMapper ocBOrderPaymentFiMapper;

    @Autowired
    OmsOrderLogService omsOrderLogService;

    @Autowired
    SgRpcService sgRpcService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private RetailNotifyService retailNotifyService;

    @Autowired
    private OmsOrderService orderService;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private StdRpcService stdRpcService;

    @Autowired
    OrderExchangeHoldTowingTaskService orderExchangeHoldTowingTaskService;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;
    @Autowired
    private OmsOrderWholeCancelService omsOrderWholeCancelService;
    @Resource
    private ThreadPoolTaskExecutor cancelOrderPollExecutor;
    @Autowired
    private OmsReturnUtil omsReturnUtil;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private BllRedisLockOrderUtil bllRedisLockOrderUtil;
    @Autowired
    private OmsMarkCancelService omsMarkCancelService;

    /**
     * 提供RPC外部调用 功能：订单取消服务(不抛异常)
     *
     * @param user       用户
     * @param id         取消订单id
     * @param logType    日志类型
     * @param logContent 日志内容
     * @return true 订单取消成功，false 订单取消失败
     */
    public boolean doAutoOrderOff(User user, Long id, String logType, String logContent) {
        return this.doAutoOffOrder(user, id, logType, logContent);
    }




    /**
     * 奇门调用，封装一层
     *
     * @param obj  obj
     * @param user user
     * @return ValueHolderV14
     */
    public ValueHolderV14 qmOrderService(JSONObject obj, User user){
        log.info(LogUtil.format("QM取消单据入参:{}","qmOrderService"),obj.toJSONString());

        String orderCode = obj.getString("orderCode");

        if (StringUtils.isEmpty(orderCode)) {
            return new ValueHolderV14(ResultCode.FAIL, "orderCode为空");
        }
        List<Long> esIdList = GSI4Order.getIdListBySourceCode(orderCode);
        if (CollectionUtils.isEmpty(esIdList)) {
            return new ValueHolderV14(ResultCode.FAIL, "未查询到订单信息");
        }

        // 此处开始改造 之前旺店通默认如果多单的话 只会取一单去取消 现在需要对多个订单取处理
        // 根据订单id 查询出来订单 并且过滤掉已取消跟已作废的订单
        List<OcBOrder> ocBOrderList = ocBorderMapper.selectByIdsList(esIdList);
        List<OcBOrder> newOrderList = new ArrayList<>();
        for (OcBOrder ocBOrder : ocBOrderList) {
            Integer orderStatus = ocBOrder.getOrderStatus();
            if ((OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus))) {
                continue;
            }
            newOrderList.add(ocBOrder);
        }

        if (CollectionUtils.isEmpty(newOrderList)) {
            return new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        }
        for (OcBOrder ocBOrder : newOrderList) {
            // 有一单发货都不能取消订单
            Integer orderStatus = ocBOrder.getOrderStatus();
            if ((OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                    || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus))) {
                return new ValueHolderV14<>(ResultCode.FAIL, "存在已出库订单,取消失败");
            }
        }

        // 如果有需要反审核的 先进行反审核 再进行取消 反审核失败的话 则也返回失败
        for (OcBOrder ocBOrder : newOrderList) {
            Integer status = ocBOrder.getOrderStatus();
            if (OmsOrderStatus.CHECKED.toInteger().equals(status) || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(status)) {
                try {
                    // 反审核
                    if (omsReturnUtil.toExamineOrderLock(ocBOrder, user)) {
                        OcBOrder updateOcBOrder = new OcBOrder();
                        updateOcBOrder.setId(ocBOrder.getId());
                        updateOcBOrder.setOrderStatus(ocBOrder.getOrderStatus());
                        ocBOrderMapper.updateById(updateOcBOrder);
                    } else {
                        ValueHolderV14 holder = new ValueHolderV14();
                        holder.setCode(-1);
                        holder.setMessage("订单取消失败!");
                        return holder;
                    }
                } catch (Exception e) {
                    log.error(this.getClass().getName() + " 订单取消异常,orderId:" + ocBOrder.getId() + ",异常信息:" + e.getMessage(), e);
                    return new ValueHolderV14<>(ResultCode.FAIL, "订单反审核失败");
                }
            }
        }

        for (OcBOrder ocBOrder : newOrderList) {
            ValueHolderV14 holder = omsOrderWholeCancelService.orderWholeCancelService(ocBOrder.getId(), user, "(奇门取消)");
            if (!holder.isOK()) {
                return holder;
            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "success");
    }

    public ValueHolderV14 updateStatus(OrderICheckRequest param, User user) throws NDSException {
        ValueHolderV14 vh = new ValueHolderV14();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("mfz-订单取消入参 ->") + param);
        }
        if (param == null || param.getIds().length < 1) {
            throw new NDSException(Resources.getMessage("请选择需要取消订单的记录！", user.getLocale()));
        }

        Long[] ids = param.getIds();
        Long type = param.getType();
        String logType = OrderLogTypeEnum.ORDER_CANCLE.getKey();
        String logContent = "订单取消成功";
        if (ids.length > 2000) {
            throw new NDSException(Resources.getMessage("一次取消数量需要小于2000", user.getLocale()));
        }
        if (type != null && type == 1 && ids.length > 1) {
            List<Long> orderIds = Arrays.asList(ids);
            // 将取消进行分组 每组100条
            List<List<Long>> cancelOrderIds = Lists.partition(orderIds, 100);
            List<Future<ValueHolderV14<Map<String, Integer>>>> results = new ArrayList<>();
            for (List<Long> orderIdList : cancelOrderIds) {
                results.add(cancelOrderPollExecutor.submit(new CallableCancelOrder(orderIdList, user)));

            }
            AtomicInteger successCount = new AtomicInteger();
            AtomicInteger failureCount = new AtomicInteger();

            //线程执行结果获取
            for (Future<ValueHolderV14<Map<String, Integer>>> futureResult : results) {
                try {
                    ValueHolderV14<Map<String, Integer>> valueHolderV14 = futureResult.get();
                    Map<String, Integer> map = valueHolderV14.getData();
                    // 执行成功或者失败 都需要将结果拿到
                    if (valueHolderV14.isOK()) {
                        int success = map.get("success") == null ? 0 : map.get("success");
                        int fail = map.get("fail") == null ? 0 : map.get("fail");
                        successCount.addAndGet(success);
                        failureCount.addAndGet(fail);
                    } else {
                        // do nothing
                    }
                } catch (InterruptedException e) {
                    log.error(LogUtil.format("订单取消多线程获取InterruptedException异常：{}"),
                            Throwables.getStackTraceAsString(e));
                    Thread.currentThread().interrupt();
                } catch (ExecutionException e) {
                    log.error(LogUtil.format("订单取消多线程获取ExecutionException异常：{}"), Throwables.getStackTraceAsString(e));
                }
            }

            vh.setCode(0);
            vh.setMessage("订单取消成功" + successCount + "失败" + failureCount + "条！");
            return vh;
        } else {
            // 单对象界面订单取消
            // 判断是否旺店通订单
            // 判断是不是奇门同步过来的订单(平台编码188)
            Long orderId = ids[0];
            GetOrderForCancelModel orderForCancelModel = ocBorderMapper.getOrderForCancelById(orderId);
            if (ObjectUtil.isNull(orderForCancelModel)) {
                throw new NDSException("订单不存在");
            }
            if (orderForCancelModel.getPlatform().equals(PlatFormEnum.WANG_DIAN_TONG.getCode())) {
                // 旺店通订单 不允许手动取消平台推送过来的订单(手工、复制、补发除外)
                Integer isCopyOrder = orderForCancelModel.getIsCopyOrder() == null ? 0 : orderForCancelModel.getIsCopyOrder();
                Integer isResetShip = orderForCancelModel.getIsResetShip() == null ? 0 : orderForCancelModel.getIsResetShip();
                Integer isHandleOrder = StringUtils.isEmpty(orderForCancelModel.getOrderSource()) ? 0 : (ObjectUtil.equal("手工新增", orderForCancelModel.getOrderSource()) ? 1 : 0);
                if (isCopyOrder == 0 && isResetShip == 0 && isHandleOrder == 0) {
                    // 插入一条操作日志 然后返回取消失败
                    omsOrderLogService.addUserOrderLog(orderId, orderForCancelModel.getBillNo(), logType,
                            "旺店通下推订单 不支持手动取消", "", "", user);
                    vh.setCode(-1);
                    vh.setMessage("旺店通下推订单 不支持手动取消");
                    return vh;
                }
            }
            if (startCancelOrderByLock(user, ids[0], logType, logContent)) {
                vh.setCode(0);
                vh.setMessage("订单取消成功！");
            } else {
                vh.setCode(-1);
                vh.setMessage("订单取消失败！");
            }
        }
        return vh;
    }

    /**
     * 开始取消订单服务，增加锁单服务
     *
     * @param user       操作用户
     * @param id         订单ID
     * @param logType    取消订单日志类型
     * @param logContent 取消订单日志内容
     * @return 取消是否成功
     */
    public boolean startCancelOrderByLock(User user, Long id, String logType, String logContent) {
        OcBOrderOffService ocBOrderOffService = ApplicationContextHandle.getBean(OcBOrderOffService.class);
        //猫超订单不能取消订单
        OcBOrder ocBOrder = orderMapper.selectById(id);
        if (PlatFormEnum.ALIBABAASCP.getCode().equals(ocBOrder.getPlatform())) {
            return false;
        }

        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                return ocBOrderOffService.doAutoOffOrder(user, id, logType, logContent);
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
            }
        } catch (Exception ex) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单取消锁单错误,msg：{}"), Throwables.getStackTraceAsString(ex));
            }
            throw new NDSException(Resources.getMessage(ex.getMessage(), user.getLocale()));
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 检查是否可以取消订单
     *
     * @param orderInfo 订单信息
     * @return true - 可以取消
     */
    private boolean checkCanCancelOrder(OcBOrder orderInfo) {
        Integer status = orderInfo.getOrderStatus();

        //订单状态:为仓库发货5、平台发货6、已取消7、系统作废8、物流已送达11、交易完成12、待传wms(21)、
        // 待分配不允许取消的原因：不知道当前处理在哪个节点，所以不允许取消
        /// 若订单状态为“配货中”不允许取消订单
        if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(status)
                || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(status)
                || OmsOrderStatus.CANCELLED.toInteger().equals(status)
                || OmsOrderStatus.SYS_VOID.toInteger().equals(status)
                || OmsOrderStatus.DELIVERED.toInteger().equals(status)
                || OmsOrderStatus.DEAL_DONE.toInteger().equals(status)
                || OmsOrderStatus.PENDING_WMS.toInteger().equals(status)
                || OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(status)
                || OmsOrderStatus.CHECKED.toInteger().equals(status)
                || OmsOrderStatus.OCCUPY_IN.toInteger().equals(status)) {
            return false;
        }

        return !OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(status)
                || orderInfo.getWmsCancelStatus() == OcOrderWmsStatus.WMS_SUCCESS.getVal();
    }

    /**
     * 取消订单
     *
     * @param user       操作用户
     * @param id         订单ID
     * @param logType    日志类型
     * @param logContent 日志内容
     * @return 取消成功=true
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean doAutoOffOrder(User user, Long id, String logType, String logContent) {
        OcBOrder findOrderInfo = ocBorderMapper.selectById(id);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("取消订单：{}"), JSONObject.toJSONString(findOrderInfo));
        }

        //复制次数反向扣减
        copyOrderDeductionNum(findOrderInfo);

        //生成负向付款信息表
        insertPayment(findOrderInfo, user);

        //判断订单是否是预售标，和是不是定金预售
        boolean presaleStatus = Optional.ofNullable(findOrderInfo.getDouble11PresaleStatus()).orElse(0).intValue() == 1;
        boolean isAdvance = AdvanceConstant.PLATFORM_DEPOSIT_PRE_SALE.equals(findOrderInfo.getAdvanceType());
        if (presaleStatus && isAdvance){
            //交易关闭并且状态为淘宝预售尾款未付
            if (TaoBaoOrderStatus.TRADE_CLOSED.equalsIgnoreCase(findOrderInfo.getPlatformStatus()) && TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equalsIgnoreCase(findOrderInfo.getStatusPayStep())) {
                log.info(LogUtil.format("为平台定金预售，创建虚拟定金订单,orderId=", findOrderInfo.getId()));
                OcBOrderOffService ocBOrderOffService= ApplicationContextHandle.getBean(OcBOrderOffService.class);
                ocBOrderOffService.createInventeOrder(findOrderInfo);
            }
        }
        //日志服务
        if (StringUtils.isNotEmpty(logType) && StringUtils.isNotEmpty(logContent)) {
            this.logService(findOrderInfo, user, logType, logContent);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("订单取消成功 id=", id));
        }

        startCancelOrder(findOrderInfo, id, user);

        findOrderInfo.setModifierename(user.getEname());
        findOrderInfo.setModifiername(user.getName());
        findOrderInfo.setModifieddate(new Date(System.currentTimeMillis()));
        //取消时间埋点
        findOrderInfo.setCancelDate(new Date());
        ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.CANCEL_DATE,new Date(),findOrderInfo.getId(),user);
        findOrderInfo.setOrderStatus(OmsOrderStatus.CANCELLED.toInteger());
        boolean updateResult = orderService.updateOrderInfo(findOrderInfo);
        if (!updateResult) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("mfz-订单取消失败！ -> 在数据库中不存在,id = ", id));
            }
            return false;
        }
        return true;
    }
    /**
     * <AUTHOR>
     * @Date 19:15 2021/7/24
     * @Description 创建虚拟定金订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void createInventeOrder(OcBOrder findOrderInfo) {
        try {
            OcBOrder order = new OcBOrder();
            BeanUtils.copyProperties(findOrderInfo, order);
            Long orderId = ModelUtil.getSequence("oc_b_order");
            order.setId(orderId);
            order.setOrderType(OrderTypeEnum.VIRTUAL_DEPOSIT.getVal());
            order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            order.setBillNo(sequenceUtil.buildBillNo());
            order.setSysremark("");
            //发货仓库原则默认仓库
            StCShopStrategyDO stCShopStrategyDO=omsStCShopStrategyService.selectOcStCShopStrategy(findOrderInfo.getCpCShopId());
            if (stCShopStrategyDO !=null){
                order.setCpCPhyWarehouseId(stCShopStrategyDO.getDefaultStoreId());
            }
            //查询系统参数，生成新的对冲明细
            CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
            String virtualDeposit = strRedisTemplate.opsForValue().get("business_system:virtual_deposit");
            if (StringUtils.isBlank(virtualDeposit)){
                throw new NDSException(Resources.getMessage("查询系统参数为空！"));
            }else {
                //保存订单对象
                omsOrderService.saveOrderInfo(order);
                saveOrderItem(findOrderInfo,orderId,virtualDeposit);
            }
            log.info(LogUtil.format("创建虚拟定金订单成功,orderId=", findOrderInfo.getId()));
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.REFUND_ORDER_ADD.getKey(), "定金预售取消，生成虚拟定金订单，原单orderId="+findOrderInfo.getId(), "", "", SystemUserResource.getRootUser());
        }catch (Exception e){
            throw new NDSException(e.getMessage());
        }
    }
    /**
     * <AUTHOR>
     * @Date 10:48 2021/8/2
     * @Description 生成明细
     */
    private void saveOrderItem(OcBOrder ocBOrder, Long orderId, String skuCode) {
        ProductSku productSku = psRpcService.selectProductSku(skuCode);
        if (productSku ==null){
            throw new NDSException(Resources.getMessage("根据系统参数["+skuCode+"],查询条码信息为空！"));
        }
        log.info(LogUtil.format("款号信息为{},orderId/skuCode", orderId, skuCode), JSON.toJSONString(productSku));
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);
        BaseModelUtil.initialBaseModelSystemField(item);
        //活动编号. 默认赋值为null
        item.setActiveId(null);
        // 若不为组合商品，则【淘宝订单中间表】明细表的“调整金额”  调整金额 = 订单实际调整金额 + 税费
       // item.setAdjustAmt(this.buildAdjustFee(taobaoOrderItem));
        //优惠金额.若为组合商品的值，则【淘宝订单中间表】明细表的“优惠金额”*【组合商品】数量*【组合商品】价格比例，
        // 若不为组合商品，则【淘宝订单中间表】明细表的“优惠金额”
       // item.setAmtDiscount(taobaoOrderItem.getDiscountFee());
        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //国标码。SKU 69码。从条码档案中有一个69码字段
        item.setBarcode(productSku.getBarcode69());
        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        item.setIsGift(0);
        //实缺标记
        item.setIsLackstock(0);
        //预售状态
        //item.setIsPresalesku(isPreSaleProd ? 1 : 0);
        //商品数字编号
       // item.setNumIid(productSku.getNumIid());
        //平台条码
        item.setSkuNumiid(productSku.getId() + "");
        item.setPsCClrEcode(productSku.getColorCode());
        item.setPsCClrEname(productSku.getColorName());
        item.setPsCClrId(productSku.getColorId());
        //订单编号
        item.setOcBOrderId(orderId);
        //子订单编号(明细编号)
        //liqb 更改ooid类型从Long类型改成String类型
//        if (null != taobaoOrderItem.getOid()) {
//            item.setOoid(String.valueOf(taobaoOrderItem.getOid()));
//        }
        //整单平摊金额
//        item.setOrderSplitAmt(
//                taobaoOrderItem.getPartMjzDiscount() == null ? BigDecimal.ZERO : taobaoOrderItem.getPartMjzDiscount()
//        );
        //商品路径
//        item.setPicPath(taobaoOrderItem.getPicPath());
        // 整单平摊金额
        item.setOrderSplitAmt(BigDecimal.ZERO);
        //成交价格.若为组合商品的值，则【则【淘宝订单中间表】明细表的（“成本价”*“数量”-“优惠费用”+“调整费用”）/“数量”*【组合商品】的“价格比例”，
        // 若不为组合商品，则【淘宝订单中间表】明细表的（“成本价”*“数量”-“优惠费用”+“调整费用”）/“数量”
        //this.calcOrderItemPrice(taobaoOrderItem)
        item.setPrice(ocBOrder.getReceivedAmt());
        // 成交单价
        item.setPriceActual(ocBOrder.getReceivedAmt());
        //标准价。【淘宝订单中间表】明细表的“标准价”
        // item.setPriceList(taobaoOrderItem.getPrice() == null ? BigDecimal.ZERO : taobaoOrderItem.getPrice());
        //taobaoOrderItem.getPrice() == null ? BigDecimal.ZERO : taobaoOrderItem.getPrice()
        //数量
        item.setQty(BigDecimal.ONE);
        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        //单行实际成交金额. s.price * s.num - s.discount_fee + s.adjust_fee- part_mjz_discount
        // 平台退款编号
        //item.setRefundId(taobaoOrderItem.getRefundId());
        //退款状态
        // 如果是退款完成，或者是交易关闭 状态=6
//        if (isHistoryOrder) {
//            item.setRefundStatus(0);
//        } else {
//            item.setRefundStatus(this.convertRefundStatusToInteger(taobaoOrderItem.getRefundStatus()));
//        }
        //规格。商品条码. normsdetailnames
        //条码id
        //标准重量。商品条码. weight
        //条码编码。
        //若为组合商品的值，则【淘宝订单中间表】明细表的在【组合商品】中对应的实际商品编码（商品档案中存在，且状态为已启用），则【淘宝订单中间表】明细表的“商品编码”
        //initialTaobaoOrderItem(taobaoOrderItem, item);

        //库位。不用赋值
//        item.setStoreSite(null);
        //标题
        item.setTitle(productSku.getSkuName());
        item.setPsCSkuEcode(skuCode);
        item.setPsCSkuEname(productSku.getSkuName());
        item.setTid(ocBOrder.getTid());
        item.setPsCBrandId(productSku.getPsCBrandId());
        item.setPsCSizeId(productSku.getSizeId());
        item.setPsCSizeEcode(productSku.getSizeCode());
        item.setPsCSizeEname(productSku.getSizeName());
        item.setPsCProEname(productSku.getName());
        item.setPsCProEcode(productSku.getProdCode());
        item.setPriceList(productSku.getPricelist());
        item.setPriceTag(productSku.getPricelist());
        item.setRealAmt(ocBOrder.getReceivedAmt());
        item.setPrice(productSku.getPricelist());

        // 增加品类设值 20220923
        item.setMDim4Id(productSku.getMDim4Id());
        item.setMDim6Id(productSku.getMDim6Id());
        if ("Y".equals(productSku.getIsEnableExpiry())) {
            item.setIsEnableExpiry(1);
        } else {
            item.setIsEnableExpiry(0);
        }

        //购物金相关
        //购物金核销子订单本金分摊金额 平台下来单位为‘分’ 转为‘元’
//        if (StringUtils.isNotEmpty(taobaoOrderItem.getExpandCardBasicPriceUsedSuborder())) {
//            BigDecimal value = BigDecimal.valueOf(Long.valueOf(taobaoOrderItem.getExpandCardBasicPriceUsedSuborder()) / 100.00);
//            item.setExpandCardBasicPriceUsedSuborder(value);
//        }
//        //购物金核销子订单权益金分摊金额  平台下来单位为‘分’ 转为‘元’
//        if (StringUtils.isNotEmpty(taobaoOrderItem.getExpandCardExpandPriceUsedSuborder())) {
//            BigDecimal value = BigDecimal.valueOf(Long.valueOf(taobaoOrderItem.getExpandCardExpandPriceUsedSuborder()) / 100.00);
//            item.setExpandCardExpandPriceUsedSuborder(value);
//        }

//        if (item.getRealAmt().compareTo(BigDecimal.ZERO) == 0) {
//            item.setGiftType("2"); //平台赠品
//            item.setIsGift(1);
//        }
//        //给预计发货时间赋值
//        if (taobaoOrderItem.getEstimateConTime() !=null){
//            log.info("orderId={}开始解析预计发货时间{}",ocBOrder.getId(),taobaoOrderItem.getEstimateConTime());
//            item.setEstimateConTime(omsOrderAdvanceParseService.parseColumn(taobaoOrderItem.getEstimateConTime(),ocBOrder.getPayTime()));
//            item.setIsExistConTime(AdvanceConstant.IS_EXIST_CON_TIME_0);
//        }
        item.setPsCBrandId(productSku.getPsCBrandId());
        omsOrderItemService.saveOcBOrderItem(item, orderId);
        OcBOrderItem item2 = new OcBOrderItem();
        BeanUtils.copyProperties(item, item2);
        item2.setId(sequenceUtil.buildOrderItemSequenceId());
        item2.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item2.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item2.setQty(BigDecimal.ONE.negate());
        item2.setRealAmt(BigDecimal.ZERO);
        omsOrderItemService.saveOcBOrderItem(item2, orderId);
    }

    /**
     * 复制次数反向扣减
     *
     * @param ocBOrder
     */
    public void copyOrderDeductionNum(OcBOrder ocBOrder) {
        //如果是复制订单，复制次数反向扣减
        if ((Objects.nonNull(ocBOrder.getIsLoseCopyOrder()) && ocBOrder.getIsCopyOrder() == 1)) {
            //查询原单
            if (ocBOrder.getSuffixInfo().contains("-LC")) {
                String suffixInfo = ocBOrder.getSuffixInfo();
                suffixInfo = suffixInfo.replace("-LC", "").trim();
                Long aLong = null;
                try {
                    aLong = Long.valueOf(suffixInfo);
                } catch (Exception e) {
                    throw new NDSException(Resources.getMessage("复制订单补充信息查询原单失败！"));
                }
                OcBOrder oriOrder = ocBorderMapper.selectById(aLong);
                oriOrder.setCopyNum(oriOrder.getCopyNum() == null ? 0 : oriOrder.getCopyNum() - 1);
                int i = ocBorderMapper.updateById(oriOrder);
                if (i == 0) {
                    throw new NDSException(Resources.getMessage("复制订单扣减原单数量失败！"));
                }
            }
        }
    }

//    private void esPush(OcBOrder orderInfo) {
//        if (SpecialElasticSearchUtil.isUseIndexDocument()) {
//            try {
//                Boolean document = SpecialElasticSearchUtil.indexDocument(OC_B_ORDER_INDEX_NAME,
//                        OC_B_ORDER_TYPE_NAME,
//                        ocBorderMapper.selectById(orderInfo.getId()), orderInfo.getId());
//                if (!document) {
//                    log.error("订单id为 ->" + orderInfo.getId() + " 重单服务ES推送失败！");
//                }
//            } catch (IOException ex) {
//                log.error("订单id为 ->" + orderInfo.getId() + " 重单服务ES推送失败！", ex);
//            }
//        }
//    }

    class CallableCancelOrder implements Callable<ValueHolderV14<Map<String, Integer>>> {

        List<Long> cancelOrderIds;
        User user;

        public CallableCancelOrder(List<Long> cancelOrderIds, User user) {
            this.cancelOrderIds = cancelOrderIds;
            this.user = user;
        }

        @Override
        public ValueHolderV14<Map<String, Integer>> call() throws Exception {
            String logType = OrderLogTypeEnum.ORDER_CANCLE.getKey();
            String logContent = "订单取消成功";
            Map<String, Integer> map = new HashMap<>();
            int successSum = 0;
            int errySum = 0;
            try {
                for (Long orderId : cancelOrderIds) {
                    try {
                        OcBOrder ocBOrder = ocBorderMapper.selectByID(orderId);
                        if (ObjectUtil.isNull(ocBOrder)) {
                            throw new NDSException("订单不存在");
                        }
                        if (OmsOrderUtil.wdtPlatformSend(ocBOrder)) {
                            // 插入一条操作日志 然后返回取消失败
                            omsOrderLogService.addUserOrderLog(orderId, ocBOrder.getBillNo(), logType,
                                    "旺店通下推订单 不支持手动取消", "", "", user);
                            throw new NDSException("旺店通下推订单 不支持手动取消");
                        }
                        if (startCancelOrderByLock(user, orderId, logType, logContent)) {
                            successSum++;
                        } else {
                            errySum++;
                        }
                    } catch (NDSException e) {
                        errySum++;
                    }
                }
                map.put("success", successSum);
                map.put("fail", errySum);
            } catch (Exception e) {
                e.printStackTrace();
                log.error(LogUtil.format("CallableCancelOrder异常：{}"), Throwables.getStackTraceAsString(e));
            }
            ValueHolderV14<Map<String, Integer>> valueHolderV14 = new ValueHolderV14<>(map, ResultCode.SUCCESS, "success");
            return valueHolderV14;
        }
    }

    /**
     * 取消订单操作
     *
     * @param orderInfo 零售发货单信息
     * @param id        订单ID
     * @param user      操作用户信息
     */
    private synchronized void startCancelOrder(OcBOrder orderInfo, Long id, User user) throws NDSException {
        if (orderInfo == null) {
            throw new NDSException(Resources.getMessage("当前订单不存在", user.getLocale()));
        }
        Long shopId = orderInfo.getCpCShopId();
        Integer status = orderInfo.getOrderStatus();
        if (status == null) {
            throw new NDSException(Resources.getMessage("订单状态不能为空！", user.getLocale()));
        }
        //校验订单下单店铺是否为空
        if (shopId == null) {
            throw new NDSException(Resources.getMessage("当前订单没有下单店铺！", user.getLocale()));
        }

///        String shopType = cpCShopMapper.selectIdByShopType(shopId);
//
//        当前这个需求取消了，订单为分销商订单  （根据订单的下单店铺查找【平台店铺】表，若表中“渠道”=分销）
//        if ("分销商".equals(shopType)) {
//            throw new NDSException(Resources.getMessage(id + "订单为分销商订单，不允许取消订单！", user.getLocale()));
//        }

        boolean isCanCancelOrder = this.checkCanCancelOrder(orderInfo);
        if (!isCanCancelOrder) {
            throw new NDSException(Resources.getMessage(id + "订单状态非【待寻源】、【待审核】不允许取消订单！", user.getLocale()));
        }

        OrderBusinessTypeCodeEnum businessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(orderInfo.getBusinessTypeCode());
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.UNKNOW) {
            throw new NDSException(Resources.getMessage(id + "未知的业务类型信息", user.getLocale()));
        }
        if(businessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_GIVE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_CONSIGN_SALE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_STANDARD_SALE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_RAW_MATERIAL_SALE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_FREE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_INSIDE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_MILK_CARD
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_UNLINE_MILK_CARD){
            throw new NDSException(Resources.getMessage(id + "订单业务类型["+ orderInfo.getBusinessTypeName() +"]不允许取消", user.getLocale()));
        }
        /*
            若订单状态为“配货中”且WMS撤回状态为“已撤回”，
            调用失败，则不能取消订单，返回提示：“作废出库通知单失败，不能取消订单”，调用成功，则调用【取消逻辑发货单服务】
            调用取消服务失败，则返回“订单释放库存失败，不能取消”
            调用成功后调用订单日志服务
            “订单编号”：当前订单编号
            “用户名称”：当前操作人
            “日志类型”：库存释放
            “日志内容”：库存释放成功
            “日志参数”：空
            “IP地址”：当前操作电脑的IP地址
            “错误信息”：空
         */
        boolean checkCancelOrderStatus =  OmsOrderStatus.UNCONFIRMED.toInteger().equals(status);
        if (checkCancelOrderStatus) {
            //查询原单明细
            List<OcBOrderItem> ocBOrderItemList=orderItemMapper.selectOrderItemListOccupy(orderInfo.getId());
            log.info("order{}.开始调用库存释放接口", orderInfo.getId());
            //封装数据
            SgOmsShareOutRequest request = buildSgOmsShareOutRequest(orderInfo,ocBOrderItemList,user);
            log.info("调用sg释放库存封装数据为：{}", JSON.toJSONString(request));
            ValueHolderV14 sgValueHolder = sgRpcService.voidSgOmsShareOut(request, orderInfo, ocBOrderItemList);
            log.info("调用sg释放库存返回接口数据为：{}", JSON.toJSONString(sgValueHolder));
            if (!sgValueHolder.isOK()){
                throw new NDSException(Resources.getMessage("释放库存失败"));
            }

          /*
           prd更改时间2019.5.29，去掉这个功能的
           List<OcBOrder> orderList = new ArrayList<>();
            orderList.add(ocBOrder);
            //调用作废出库通知单服务
            ValueHolderV14 valueHolderV14 = sgRpcService.invoildOutgoingNotice(orderList, user);
            if (valueHolderV14 == null || valueHolderV14.getCode() != 0) {
                throw new NDSException(Resources.getMessage(id + "作废出库通知单失败，不能取消订单！", user.getLocale()));
            }*/

            //如果平台是线下pos，先调用pos的rpc通知线下取消，pos取消成功，继续走以前的取消逻辑；pos取消失败，不走逻辑，记录日志。
//            Integer platform = orderInfo.getPlatform();
//            if (platform.equals(PlatFormEnum.POS.getCode())) {
//                ValueHolder valueHolder;
//                try {
//                    valueHolder = retailNotifyService.orderCancelNotifyRetailByRpc(id, orderInfo, user);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    logService(orderInfo, user, OrderLogTypeEnum.ORDER_CANCLE.getKey(), "订单取消失败，调用pos取消服务异常：" + e.getMessage());
//                    throw new NDSException(Resources.getMessage("订单取消失败，调用pos取消服务失败：" + e.getMessage(), user.getLocale()));
//                }
//                if (log.isDebugEnabled()) {
//                    log.debug("订单取消通知线下POS端RPC调用结果：" + valueHolder);
//                }
//                if (valueHolder == null) {
//                    logService(orderInfo, user, OrderLogTypeEnum.ORDER_CANCLE.getKey(), "订单取消失败，调用pos取消服务失败：返回valueHolder is null");
//                    throw new NDSException(Resources.getMessage("订单取消失败，调用pos取消服务失败：返回valueHolder is null", user.getLocale()));
//                } else {
//                    if (valueHolder.isOK()) {
//                        //调用取消逻辑发货单服务
//                        voidSgLogicalSendBill(orderInfo, id, user);
//                    } else {
//                        logService(orderInfo, user, OrderLogTypeEnum.ORDER_CANCLE.getKey(), "订单取消失败，调用pos取消服务失败：" + valueHolder.get("message"));
//                        throw new NDSException(Resources.getMessage("订单取消失败，调用pos取消服务失败：" + valueHolder.get("message"), user.getLocale()));
//                    }
//                }
//            } else {
//                //调用取消逻辑发货单服务
//                voidSgLogicalSendBill(orderInfo, id, user);
//            }
        }

       /* if (status == OmsOrderStatus.UNCONFIRMED.toInteger() || status == OmsOrderStatus.BE_OUT_OF_STOCK.toInteger()
                || status == OmsOrderStatus.CHECKED.toInteger()) {
            //调用取消逻辑发货单服务
            voidSgBsend(ocBOrder, id, user);
        }*/
       /*   现在prd中没有这个东西了
           //若“订单状态”为待审核、缺货、已审核、配货中且（WMS撤回状态）需释放库存，调用释放库存服务（详情查看释放库存服务），成功后调用订单日志服务 （释放库存是库存中心的）
            if (status == OmsOrderStatus.UNCONFIRMED.toInteger() || status == OmsOrderStatus.BE_OUT_OF_STOCK.toInteger()
                    || status == OmsOrderStatus.CHECKED.toInteger() || status == OmsOrderStatus.IN_DISTRIBUTION.toInteger()) {
                if (ocBOrder.getWmsCancelStatus() == 1) {
                    //调用释放库存服务，成功后调用订单日志服务
                }

            }
        */
    }
    /**
     * <AUTHOR>
     * @Date 20:40 2021/7/19
     * @Description 封装数据
     */
    private SgOmsShareOutRequest buildSgOmsShareOutRequest(OcBOrder orderInfo, List<OcBOrderItem> ocBOrderItemList,User user) {
        SgOmsShareOutRequest request = new SgOmsShareOutRequest();
        request.setSourceBillId(orderInfo.getId());
        request.setSourceBillNo(orderInfo.getBillNo());
        request.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        request.setTid(orderInfo.getTid());
        request.setCancelType(SgConstantsIF.OMS_STORAGE_OCCUPY_CANCEL_TYPE_MAIN);
        request.setLoginUser(user);
        List<SgOmsShareOutItemRequest> itemRequestList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            SgOmsShareOutItemRequest sgOmsShareOutItemRequest = new SgOmsShareOutItemRequest();
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            sgOmsShareOutItemRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            sgOmsShareOutItemRequest.setQtyPreout(ocBOrderItem.getQty());
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            itemRequestList.add(sgOmsShareOutItemRequest);
        }
        request.setItemRequestList(itemRequestList);

        return request;
    }

    /**
     * 调用取消逻辑发货单服务
     *
     * @param orderInfo 逻辑发货单信息
     * @param id        订单ID
     * @param user      操作用户
     */
//    private void voidSgLogicalSendBill(OcBOrder orderInfo, Long id, User user) {
//        ValueHolderV14<SgSendBillVoidResult> vh = sgRpcService.voidSgBSend(orderInfo,
//                OrderBillTypeEnum.RETAIL_DELIVERY.getVal(), user);
//        if (vh.getCode() == ResultCode.FAIL) {
//            throw new NDSException(Resources.getMessage(id + "订单释放库存失败，不能取消！", user.getLocale()));
//        } else {
//            logService(orderInfo, user, OrderLogTypeEnum.RELEASE_STOCK_SUCCESS.getKey(), "库存释放成功");
//        }
//    }

    /**
     * @param orderInfo
     * @param user
     * @param logType
     * @param logContent
     */
    private void logService(OcBOrder orderInfo, User user, String logType, String logContent) {
        /**
         *  调用日志服务
         *  参数 订单id、订单编号、订单类型、日志信息、参数、错误信息
         */
        if (StringUtils.isEmpty(logType)) {
            logType = OrderLogTypeEnum.ORDER_CANCLE.getKey();
        }

        try {
            /*omsOrderLogService.addOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), logType,
                    logContent, "", "", user.getLastloginip());*/

            omsOrderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(), logType,
                    logContent, "", "", user);
        } catch (Exception e) {
            log.error(LogUtil.format("调用日志服务异常,error：{},id = ", orderInfo.getId()), Throwables.getStackTraceAsString(e));
            omsOrderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(), logType,
                    logContent, JSONObject.toJSONString(orderInfo), e.toString(), user);
        }

    }

    /**
     * 若订单“已收金额”大于0，则需要生成负向付款信息表。
     *
     * @param ocBOrder
     * @param user
     */
    private void insertPayment(OcBOrder ocBOrder, User user) {
        // 已收金额
        BigDecimal money = ocBOrder.getReceivedAmt();
        if (money != null && new BigDecimal(0).compareTo(money) < 0) {
            /*JSONObject jo = new JSONObject();
            jo.put("payment_amt", -money.doubleValue());*/
            OcBOrderPayment ocBorderPaymentDto = new OcBOrderPayment();
            ocBorderPaymentDto.setId(ModelUtil.getSequence("oc_b_order_payment"));
            ocBorderPaymentDto.setOcBOrderId(ocBOrder.getId());
            ocBorderPaymentDto.setPayTime(new Date(System.currentTimeMillis()));
            ocBorderPaymentDto.setEndTime(new Date(System.currentTimeMillis()));
            ocBorderPaymentDto.setPayType(ocBOrder.getPayType());
            ocBorderPaymentDto.setCreationdate(new Date(System.currentTimeMillis()));
            ocBorderPaymentDto.setOwnerename(user.getEname());
            ocBorderPaymentDto.setModifierename(user.getEname());
            ocBorderPaymentDto.setModifieddate(new Date(System.currentTimeMillis()));
            ocBorderPaymentDto.setPaymentAmt(BigDecimal.valueOf(-money.doubleValue()));
            ocBOrderPaymentFiMapper.insert(ocBorderPaymentDto);
        }
    }
}