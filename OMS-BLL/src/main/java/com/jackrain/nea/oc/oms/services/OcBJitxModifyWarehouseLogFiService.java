package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBJitxModifyWarehouseLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowCreatedStateEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowStateEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.vo.ResultDataVo;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2020-12-30
 * @desc JITX订单改仓日志表-Fi服务
 **/
@Slf4j
@Service
public class OcBJitxModifyWarehouseLogFiService {

    @Autowired
    private JitxService jitxService;

    @Autowired
    private OcBJitxModifyWarehouseLogService ocBJitxModifyWarehouseLogService;

    @Autowired
    private UpdateOrderInfoService updateOrderInfoService;

    @Autowired
    private OmsAuditTaskService omsAuditTaskService;

    /**
     * 强制改仓
     *
     * @param session
     * @return
     */
    public ValueHolder markForcedWarehouseChange(QuerySession session) {
        List<Long> idList = getIdList(session);
        List<ResultDataVo> resultDataVoList = new ArrayList<>();
        for (Long logId : idList) {
            ValueHolderV14<ResultDataVo> valueHolderV14 = markForcedWarehouseChangeSingle(logId, session.getUser(),null);
            if (!valueHolderV14.isOK()) {
                resultDataVoList.add(valueHolderV14.getData());
            }
        }
        ValueHolder valueHolder = new ValueHolder();
        if (CollectionUtils.isEmpty(resultDataVoList)) {
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("message", "强制改仓成功");
        } else {
            String message = new StringBuilder("强制改仓成功")
                    .append(idList.size() - resultDataVoList.size()).append("条，失败")
                    .append(resultDataVoList.size()).append("条").toString();
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", message);
            valueHolder.put("data", resultDataVoList);
        }
        return valueHolder;
    }

    /**
     * 单个强制改仓
     *
     * @param logId       中间表ID
     * @param operateUser 操作人
     * @param status 平台返回工单状态 为空表是 页面点击，不为空表示自动
     * @return
     */
    public ValueHolderV14<ResultDataVo> markForcedWarehouseChangeSingle(Long logId, User operateUser,String status) {
        ValueHolderV14<ResultDataVo> valueHolderV14 = new ValueHolderV14<>();
        try {
            OcBJitxModifyWarehouseLog warehouseLog = ocBJitxModifyWarehouseLogService.selectById(logId);
            String workflowState = warehouseLog.getWorkflowState();
            if (warehouseLog == null) {
                throw new NDSException("JITX改仓中间表不存在");
            }
            if (Objects.nonNull(status)){
                workflowState =status;
            }
            boolean statusFlag = VipJitxWorkflowStateEnum.REJECT.getKey().equals(workflowState) || VipJitxWorkflowStateEnum.CANCEL.getKey().equals(workflowState);
            AssertUtil.assertException(!statusFlag, "只有工单审核状态为驳回或取消时，才可以强制改仓!");
            OcBJitxModifyWarehouseLog updateLog = new OcBJitxModifyWarehouseLog();
            updateLog.setId(warehouseLog.getId());
            updateLog.setForcedWarehouseChange(YesNoEnum.Y.getKey());
            BaseModelUtil.makeBaseModifyField(updateLog, operateUser);
            int row = ocBJitxModifyWarehouseLogService.updateById(updateLog);
            if (row > 0) {
                updateOrderInfoService.removeJITXRedisChangeWarehouseFlag(warehouseLog.getOrderId(), operateUser);
                valueHolderV14.setCode(ResultCode.SUCCESS);

                //插入审核中间表
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setId(warehouseLog.getOrderId());
                omsAuditTaskService.createOcBAuditTask(updateOrder, OmsAuditTimeCalculateReason.RESERVE_AUDIT);
            } else {
                throw new NDSException("标记失败");
            }
        } catch (Exception e) {
            log.error("JITX改仓中间表强制改仓异常，OcBJitxModifyWarehouseLogId：{}，错误：{}",
                    logId, Throwables.getStackTraceAsString(e));
            ResultDataVo resultDataVo = new ResultDataVo();
            resultDataVo.setObjid(logId);
            String errorMessage = new StringBuffer("错误：")
                    .append(e.getMessage()).toString();
            resultDataVo.setMessage(errorMessage);
            valueHolderV14.setMessage(errorMessage);
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setData(resultDataVo);
        }
        return valueHolderV14;
    }

    /**
     * 标记为未创建
     *
     * @param session
     * @return
     */
    public ValueHolder markUnCreate(QuerySession session) {
        List<Long> idList = getIdList(session);
        List<ResultDataVo> resultDataVoList = new ArrayList<>();
        for (Long logId : idList) {
            ValueHolderV14<ResultDataVo> valueHolderV14 = markUnCreateSingle(logId, session.getUser());
            if (!valueHolderV14.isOK()) {
                resultDataVoList.add(valueHolderV14.getData());
            }
        }
        ValueHolder valueHolder = new ValueHolder();
        if (CollectionUtils.isEmpty(resultDataVoList)) {
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("message", "标记成功");
        } else {
            String message = new StringBuilder("标记成功")
                    .append(idList.size() - resultDataVoList.size()).append("条，失败")
                    .append(resultDataVoList.size()).append("条").toString();
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", message);
            valueHolder.put("data", resultDataVoList);
        }
        return valueHolder;
    }

    /**
     * 单个改仓工单标记为未创建
     *
     * @param logId       中间表ID
     * @param operateUser 操作人
     * @return
     */
    public ValueHolderV14<ResultDataVo> markUnCreateSingle(Long logId, User operateUser) {
        ValueHolderV14<ResultDataVo> valueHolderV14 = new ValueHolderV14<>();
        try {
            OcBJitxModifyWarehouseLog log = ocBJitxModifyWarehouseLogService.selectById(logId);
            if (log == null) {
                throw new NDSException("JITX改仓中间表不存在");
            }
            if (!IsActiveEnum.Y.getKey().equals(log.getIsactive())) {
                throw new NDSException("已作废改仓工单，不允许标记");
            }
            Integer createdStatus = log.getCreatedStatus();
            if (createdStatus == null) {
                throw new NDSException("改仓工单创建状态异常，不允许标记");
            }
            // 创建失败 或 创建成功且改仓工单状态不能为新建和处理中
            boolean createPermit = (VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode().equals(createdStatus)
                    || (VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode().equals(createdStatus)
                    && !VipJitxWorkflowStateEnum.CREATE.getKey().equals(log.getWorkflowState())
                    && !VipJitxWorkflowStateEnum.DOING.getKey().equals(log.getWorkflowState()))
            );
            if (createPermit) {
                OcBJitxModifyWarehouseLog updateLog = new OcBJitxModifyWarehouseLog();
                updateLog.setId(log.getId());
                updateLog.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.UNCREATE.getCode());
                updateLog.setWorkflowState(null);
                BaseModelUtil.makeBaseModifyField(updateLog, operateUser);
                int row = ocBJitxModifyWarehouseLogService.updateUnCreate(updateLog);
                if (row > 0) {
                    valueHolderV14.setCode(ResultCode.SUCCESS);
                } else {
                    throw new NDSException("标记失败");
                }
            } else {
                throw new NDSException("改仓工单状态异常，不允许标记");
            }
        } catch (Exception e) {
            log.info(LogUtil.format("JITX改仓中间表标记为未创建异常，错误：{},OcBJitxModifyWarehouseLogId：",
                    logId), Throwables.getStackTraceAsString(e));
            ResultDataVo resultDataVo = new ResultDataVo();
            resultDataVo.setObjid(logId);
            String errorMessage = new StringBuffer("错误：")
                    .append(e.getMessage()).toString();
            resultDataVo.setMessage(errorMessage);
            valueHolderV14.setMessage(errorMessage);
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setData(resultDataVo);
        }
        return valueHolderV14;
    }

    /**
     * 重试(创建改仓和获取改仓接口)
     *
     * @param session
     * @return
     */
    public ValueHolder retry(QuerySession session) {
        List<Long> idList = getIdList(session);
        List<ResultDataVo> resultDataVoList = new ArrayList<>();
        for (Long logId : idList) {
            ValueHolderV14<ResultDataVo> valueHolderV14 = retry(logId, session.getUser());
            if (!valueHolderV14.isOK()) {
                resultDataVoList.add(valueHolderV14.getData());
            }
        }
        ValueHolder valueHolder = new ValueHolder();
        if (CollectionUtils.isEmpty(resultDataVoList)) {
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("message", "重试成功");
        } else {
            String message = new StringBuilder("重试成功")
                    .append(idList.size() - resultDataVoList.size()).append("条，失败")
                    .append(resultDataVoList.size()).append("条").toString();
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", message);
            valueHolder.put("data", resultDataVoList);
        }
        return valueHolder;
    }

    /**
     * 单个改仓工单重试(创建改仓和获取改仓接口)
     *
     * @param logId       中间表ID
     * @param operateUser 操作人
     * @return
     */
    public ValueHolderV14<ResultDataVo> retry(Long logId, User operateUser) {
        ValueHolderV14<ResultDataVo> valueHolderV14 = new ValueHolderV14<>();
        String lockRedisKey = BllRedisKeyResources.buildJitxChangeWarehouseWorkflowKey(logId);
        RedisReentrantLock redisReentrantLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisReentrantLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                OcBJitxModifyWarehouseLog log = ocBJitxModifyWarehouseLogService.selectById(logId);
                if (log == null) {
                    throw new NDSException("JITX改仓中间表不存在");
                }
                if (!IsActiveEnum.Y.getKey().equals(log.getIsactive())) {
                    throw new NDSException("已作废改仓工单，不允许重试");
                }
                String workflowState = log.getWorkflowState();
                Integer createdStatus = log.getCreatedStatus();
                if (VipJitxWorkflowCreatedStateEnum.UNCREATE.getCode().equals(createdStatus)
                        && StringUtils.isEmpty(workflowState)) {
                    // 工单创建状态为未创建，且工单状态为初始状态，创建改仓工单
                    jitxService.createChangeWarehouseWorkflowSingle(log, operateUser);
                } else if (VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode().equals(createdStatus)
                        && (VipJitxWorkflowStateEnum.CREATE.getKey().equals(log.getWorkflowState())
                        || VipJitxWorkflowStateEnum.DOING.getKey().equals(log.getWorkflowState()))) {
                    // 工单创建状态为创建成功，且工单状态为新建或处理中，获取改仓工单
                    jitxService.getChangeWarehouseWorkflows(Lists.newArrayList(log), operateUser);
                } else {
                    throw new NDSException("改仓工单状态异常，不允许重试！");
                }
                valueHolderV14.setCode(ResultCode.SUCCESS);
            } else {
                throw new NDSException("正在被操作中，请稍后重试！");
            }
        } catch (Exception e) {
            log.info(LogUtil.format("JITX改仓中间表重试异常，错误：{},OcBJitxModifyWarehouseLogId：",
                    logId), Throwables.getStackTraceAsString(e));
            ResultDataVo resultDataVo = new ResultDataVo();
            resultDataVo.setObjid(logId);
            String errorMessage = new StringBuffer("错误：")
                    .append(e.getMessage()).toString();
            resultDataVo.setMessage(errorMessage);
            valueHolderV14.setMessage(errorMessage);
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setData(resultDataVo);
        } finally {
            redisReentrantLock.unlock();
        }
        return valueHolderV14;
    }

    /**
     * 获取ID列表
     *
     * @param querySession
     * @return
     * @throws NDSException
     */
    public List<Long> getIdList(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(
                JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss",
                        SerializerFeature.WriteMapNullValue
                ),
                Feature.OrderedField
        );
        User operateUser = querySession.getUser();
        if (null == operateUser) {
            throw new NDSException(Resources.getMessage("登录状态异常！", querySession.getLocale()));
        }
        if (null == param) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }
        boolean idIds = param.containsKey("ids");
        boolean isObjId = param.containsKey("objid");
        if (!(idIds || isObjId) || (idIds && isObjId)) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }
        if (idIds && (param.getJSONArray("ids").size() <= 0)) {
            throw new NDSException(Resources.getMessage("请选择数据！", querySession.getLocale()));
        }
        List<Long> idList = new ArrayList<>();
        if (idIds) {
            JSONArray jsonArray = param.getJSONArray("ids");
            idList = jsonArray.stream().map(a -> Long.valueOf(a.toString())).collect(Collectors.toList());
        } else {
            idList.add(param.getLong("objid"));
        }
        if (CollectionUtils.isEmpty(idList)) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.multiFormat("JITX订单改仓，ID列表：{}", idList));
        }
        return idList;
    }
}
