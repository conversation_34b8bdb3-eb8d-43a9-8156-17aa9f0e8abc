package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillVoidResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.AddOrderNoticeAndOutService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;

import static com.jackrain.nea.resource.OcElasticSearchIndexResources.OC_B_RETURN_ORDER_LOG_INDEX_NAME;
import static com.jackrain.nea.resource.OcElasticSearchIndexResources.OC_B_RETURN_ORDER_LOG_TYPE_NAME;

/**
 * 退换货订单 -> 手动入库
 *
 * @date 2019/10/9
 * @author: tqh
 */
@Component
@Slf4j
public class VirtualWarehouseStorageService {
    @Autowired
    private AddOrderNoticeAndOutService addOrderNoticeAndOutService;

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    ReturnOrderLogService returnOrderLogService;

    @Autowired
    private OmsToSapTaskService omsToSapTaskService;

    /**
     * 退换货订单 -> 手动入库
     *
     * @param ids
     * @param user
     * @return
     * @throws NDSException
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 virtualWarehouseStorage(List<Long> ids, User user) throws NDSException {
        ValueHolderV14 vh = new ValueHolderV14();
        List<OcBReturnOrder> failList = new ArrayList<>();//失败数
        List<JSONObject> logMsgList = new ArrayList<>();
        try {
            if (ids == null || ids.size() <= 0) {
                throw new NDSException(Resources.getMessage("手动入库ids为空或小于0！", user.getLocale()));
            }
            for (int i = 0; i < ids.size(); i++) {
                String lockRedisKey = "oc:oms:returnOrder:returnOrderId:" + ids.get(i);//BllRedisKeyResources.buildLockOrderKey(ids.get(i));
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                OcBReturnOrder ocBReturnOrder = new OcBReturnOrder();
                ocBReturnOrder.setId(ids.get(i));
                ocBReturnOrder = ocBReturnOrderMapper.selectById(ocBReturnOrder);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {//加锁
                        if (ocBReturnOrder == null) {
                            failList.add(ocBReturnOrder);//记录失败
                            continue;
                        }
                        Integer returnStatus = ocBReturnOrder.getReturnStatus();//退单状态为：20:等待退货入库
                        if (returnStatus == null || !(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus))) {
                            failList.add(ocBReturnOrder);//记录失败
                            logMsgList.add(logFun.apply(ocBReturnOrder, "退单状态非等待退货入库,不允许入库"));
                            insertReturnOrderLog(user, ocBReturnOrder, "退换货单手动入库失败，退单状态非“等待退货入库”，不允许手动入库");
                            continue;
                        }
//                        if (ocBReturnOrder.getIsNeedToWms() != null && ocBReturnOrder.getIsNeedToWms() == 1) {
                            //继续往下走调用服务
                            if (!virtualWarehouseMuntiFunc(ocBReturnOrder, user)) {
                                failList.add(ocBReturnOrder);//记录失败
                            }
//                        } else {
//                            //20191016：要求此状态也能调用3单服务  //调用生成入库通知单3单服务
//                            SgOmsStoInRequest sgOmsStoInRequest = getDatasByReturnOrder(ocBReturnOrder);//组装数据
//                            ValueHolderV14 result = sgRpcService.addOrderNotice(sgOmsStoInRequest);
//                            if (result.getCode() == ResultCode.SUCCESS) {
//                                String allSku = getSkuAll(ocBReturnOrder);
//                                insertReturnOrderLog(user, ocBReturnOrder, "退换货单手动入库完成，入库条码：" + allSku);
//                                //@20200820 产品沟通后，传SAP中间表写入数据更改
//                                //returnOrderTosapTask(user, ocBReturnOrder);
//                                //@20200822 根据实体仓是否为SAP管控仓 判断是否传SAP
////                                omsToSapTaskService.isToSapReturnOrder(user, ocBReturnOrder);
//                            } else {
//                                logMsgList.add(logFun.apply(ocBReturnOrder, result.getMessage()));
//                                log.debug(this.getClass().getName() + "手动入库手动按钮，id=" + ocBReturnOrder.getId() + "调用生成入库通知单3单服务失败，失败原因" + result.getMessage());
//                                insertReturnOrderLog(user, ocBReturnOrder, "调用生成入库通知单3单服务失败，失败原因:" + result.getMessage());
//                                failList.add(ocBReturnOrder);
//                            }
//                        }
                    } else {
                        failList.add(ocBReturnOrder);//记录失败
                        logMsgList.add(logFun.apply(ocBReturnOrder, "单据正在操作, 请稍后再试"));
                        insertReturnOrderLog(user, ocBReturnOrder, "退换货单ID[" + ids.get(i) + "] 当前订单其他人在操作，请稍后再试!");
                        redisLock.unlock();
                        continue;
                    }
                } catch (Exception e) {
                    String msg = "手动入库操作出现异常";
                    if (e instanceof NDSException) {
                        msg = e.getMessage();
                    }
                    logMsgList.add(logFun.apply(ocBReturnOrder, msg));
                    log.error(LogUtil.format("退换货单ID[" + ids.get(i) + "] VirtualWarehouseStorageService 手动入库操作异常={}"), Throwables.getStackTraceAsString(e));
                    Thread.currentThread().interrupt();
                } finally {
                    redisLock.unlock();
                }
            }
            if (failList.size() == ids.size()) {
                vh.setCode(ResultCode.FAIL);
            } else {
                vh.setCode(ResultCode.SUCCESS);
            }
            vh.setMessage("手动入库完成,成功 " + (ids.size() - failList.size()) + " 条" + "," + "失败 " + (failList.size()) + " 条");
        } catch (Exception e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("手动入库异常,请联系管理员!");
            log.error(LogUtil.format("手动入库异常 错误信息{}"), Throwables.getStackTraceAsString(e));
        }
        vh.setData(logMsgList);
        return vh;
    }

    private BiFunction<OcBReturnOrder, String, JSONObject> logFun = (order, s) -> {
        String billNo = order.getBillNo();
        String msg = "单据编号: " + billNo + " 入库失败, " + s;
        JSONObject jsn = new JSONObject();
        jsn.put("objid", order.getId());
        jsn.put("message", msg);
        return jsn;
    };


    /**
     * 条件判断：调用撤回，入库通知单3单服务
     *
     * @param ocBReturnOrder
     */
    private boolean virtualWarehouseMuntiFunc(OcBReturnOrder ocBReturnOrder, User user) {
        int isTowms = ocBReturnOrder.getIsTowms();//传WMS状态，0:未传WMS,1:传WMS中,2:传WMS成功,3:传WMS失败',
        SgOmsStoInRequest sgOmsStoInRequest = getDatasByReturnOrder(ocBReturnOrder);//组装数据
        String allSku = getSkuAll(ocBReturnOrder);
        if (isTowms == 1) {
            insertReturnOrderLog(user, ocBReturnOrder, "退换货单手动入库失败，传WMS状态为“传WMS中”，不允许手动入库");
            AssertUtil.assertException(true,"传WMS状态为: 传WMS中，不允许手动入库");
            return false;
        } else if (isTowms == 0 || isTowms == 3) {
            //调用生成入库通知单3单服务
            ValueHolderV14 result = sgRpcService.addOrderNotice(sgOmsStoInRequest);
            if (result.getCode() == ResultCode.SUCCESS) {
                // virtualWarehouseStorageFinish(ocBReturnOrder,user);
                insertReturnOrderLog(user, ocBReturnOrder, "退换货单手动入库完成，入库条码：" + allSku);
                return true;
            } else {
                insertReturnOrderLog(user, ocBReturnOrder, "调用生成入库通知单3单服务失败，失败原因:" + result.getMessage());
                AssertUtil.assertException(true,"调用生成入库通知单3单服务失败，失败原因" + result.getMessage());
            }
        } else {
            //WMS撤回状态
            if (ocBReturnOrder.getWmsCancelStatus() == 1) {//撤回成功
                //调用生成入库通知单3单服务
                ValueHolderV14 result = sgRpcService.addOrderNotice(sgOmsStoInRequest);
                if (result.getCode() == ResultCode.SUCCESS) {
                    //virtualWarehouseStorageFinish(ocBReturnOrder,user);
                    insertReturnOrderLog(user, ocBReturnOrder, "退换货单手动入库完成，入库条码：" + allSku);
                    return true;
                } else {
                    insertReturnOrderLog(user, ocBReturnOrder, "调用生成入库通知单3单服务失败，失败原因:" + result.getMessage());
                    AssertUtil.assertException(true,"调用生成入库通知单3单服务失败，失败原因" + result.getMessage());
                }
            } else {
                Long orderId = ocBReturnOrder.getOrigOrderId();//订单编号
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);//来源单据id --->查询订单表的订单编号字段
                ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> execute = sgRpcService
                        .invoildOutgoingNotice(Lists.newArrayList(ocBOrder), user,true);
                int code = com.jackrain.nea.util.Tools.getInt(execute.getCode(), -1);
                if (code == ResultCode.SUCCESS) {
                    insertReturnOrderLog(user, ocBReturnOrder, "手动入库 - WMS撤回成功");
                    //更新WMS撤回状态为“WMS撤回成功”调用WMS撤回服务后，更新“WMS撤回状态”：撤回成功
                    ocBReturnOrder.setWmsCancelStatus(1);
                    updateWmsStatusById(ocBReturnOrder);
                    //调用生成入库通知单3单服务
                    ValueHolderV14 result = sgRpcService.addOrderNotice(sgOmsStoInRequest);
                    if (result.getCode() == ResultCode.SUCCESS) {
                        //virtualWarehouseStorageFinish(ocBReturnOrder,user);
                        insertReturnOrderLog(user, ocBReturnOrder, "退换货单手动入库完成，入库条码：" + allSku);
                        return true;
                    } else {
                        insertReturnOrderLog(user, ocBReturnOrder, "调用生成入库通知单3单服务失败，失败原因:" + result.getMessage());
                        AssertUtil.assertException(true,"调用生成入库通知单3单服务失败，失败原因" + result.getMessage());
                    }
                } else {
                    insertReturnOrderLog(user, ocBReturnOrder, "调用wms撤回服务服务失败，失败原因:" + execute.getMessage());
                    AssertUtil.assertException(true, "调用生成入库通知单3单服务失败，失败原因" + execute.getMessage());
                }
            }
        }
        return false;
    }

    /**
     * 更新退换货订单并推送es
     *
     * @param ocBReturnOrder 退单实体
     */
    private void updateWmsStatusById(OcBReturnOrder ocBReturnOrder) {
        ocBReturnOrderMapper.updateById(ocBReturnOrder);
    }


    /**
     * 组装 调用入库通知单3单服务入参
     *
     * @param
     */
    private SgOmsStoInRequest getDatasByReturnOrder(OcBReturnOrder ocBReturnOrder) {
        List<OcBReturnOrderRefund> refundList = ocBReturnOrderRefundMapper.selectByOcOrderId(ocBReturnOrder.getId());
        SgOmsStoInRequest sgOmsStoInRequest = addOrderNoticeAndOutService.transferParam(ocBReturnOrder, refundList, SystemUserResource.getRootUser());
        return sgOmsStoInRequest;
    }
    /**
     * 获取主体数据信息
     **/
//    private SgPhyInNoticesRequest getNoticesRequestFunc(OcBReturnOrder ocBReturnOrder) {
//        SgPhyInNoticesRequest notices = new SgPhyInNoticesRequest();
//        notices.setCpCPhyWarehouseId(ocBReturnOrder.getCpCPhyWarehouseInId());//实体仓ID
//        CpCPhyWarehouse cpCPhyWarehouse = getQueryByWarehouseId(ocBReturnOrder.getCpCPhyWarehouseInId());
//        notices.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());//实体仓CODE
//        notices.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());//实体仓名称
//        notices.setInType(1);//入库类型：电商入库
//        notices.setSourceBillType(2);//零售退货单
//        notices.setSourceBillNo(ocBReturnOrder.getBillNo());
//        notices.setSourceBillId(ocBReturnOrder.getId());//来源单据ID
//        notices.setWmsBillNo(ocBReturnOrder.getWmsBillNo());
//        notices.setBillStatus(SgInNoticeConstants.BILL_STATUS_IN_PENDING);//单据状态
//        notices.setWmsStatus(Long.valueOf(ocBReturnOrder.getIsTowms()));//传wms状态
//        notices.setIsPassWms(Math.toIntExact(ocBReturnOrder.getIsNeedToWms() == null ? 0 : ocBReturnOrder.getIsNeedToWms()));//是否传WMS，默认 否
//        notices.setLogisticNumber(ocBReturnOrder.getLogisticsCode());//物流单号
//        notices.setCpCLogisticsId(ocBReturnOrder.getCpCLogisticsId());//物流公司id
//        notices.setCpCLogisticsEcode(ocBReturnOrder.getCpCLogisticsEcode());//物流公司code
//        notices.setCpCLogisticsEname(ocBReturnOrder.getCpCLogisticsEname());//物流公司name
//        notices.setInTime(new Date());//入库时间
//        notices.setCpCShopId(ocBReturnOrder.getCpCShopId());//平台店铺
//        notices.setCpCShopTitle(ocBReturnOrder.getCpCShopTitle());//平台店铺标题
//        notices.setSourcecode(ocBReturnOrder.getOrigSourceCode());//平台单号
//        return notices;
//    }
//
//    /**
//     * 获取明细信息
//     */
//    private List<SgPhyInNoticesItemRequest> itemListGetFunc(OcBReturnOrder ocBReturnOrder) {
//        QueryWrapper query = new QueryWrapper();
//        query.eq("OC_B_RETURN_ORDER_ID", ocBReturnOrder.getId());
//        query.eq("ISACTIVE", "Y");
//        List<OcBReturnOrderRefund> ocBReturnOrderRefundList = ocBReturnOrderRefundMapper.selectList(query);
//        List<SgPhyInNoticesItemRequest> itemList = new ArrayList<>();
//        if (ocBReturnOrderRefundList == null || ocBReturnOrderRefundList.size() <= 0) {
//            return new ArrayList<>();
//        }
//        ocBReturnOrderRefundList.forEach(transferItem -> {
//            SgPhyInNoticesItemRequest item = new SgPhyInNoticesItemRequest();
//            item.setId(-1L);
//            item.setQty(transferItem.getQtyRefund());//申请数量
//            item.setQtyIn(transferItem.getQtyRefund());
//            item.setPsCSpec1Id(transferItem.getPsCClrId());
//            item.setPsCSpec1Ecode(transferItem.getPsCClrEcode());
//            item.setPsCSpec1Ename(transferItem.getPsCClrEname());
//            item.setPsCSpec2Id(transferItem.getPsCSizeId());
//            item.setPsCSpec2Ecode(transferItem.getPsCSizeEcode());
//            item.setPsCSpec2Ename(transferItem.getPsCSizeEname());
//            item.setSourceBillItemId(transferItem.getId());
//            item.setPriceCost(transferItem.getPrice() == null ? BigDecimal.ZERO : transferItem.getPrice());//商品成交价
//            // 吊牌价如果为null 则为0
//            item.setPriceList(transferItem.getPriceList() == null ? BigDecimal.ZERO : transferItem.getPriceList());//吊牌价
//            item.setGbcode(transferItem.getBarcode());//添加国标码
//            item.setPsCSkuId(transferItem.getPsCSkuId());
//            item.setPsCSkuEcode(transferItem.getPsCSkuEcode());
//            item.setPsCProId(transferItem.getPsCProId());
//            item.setPsCProEcode(transferItem.getPsCProEcode());
//            item.setPsCProEname(transferItem.getPsCProEname());
//            //合并相同条码的情况条件：skuid，skucode，gbcode相同的话累积
//            if (itemList.size() <= 0) {
//                itemList.add(item);
//            } else {
//                for (SgPhyInNoticesItemRequest request : itemList) {
//                    if (request.getPsCSkuId() != null && request.getPsCSkuId().equals(transferItem.getPsCSkuId())
//                            && request.getGbcode() != null && request.getGbcode().equals(transferItem.getBarcode())
//                            && request.getPsCSkuEcode() != null && request.getPsCSkuEcode().equals(transferItem.getPsCSkuEcode())) {
//
//                      /*  BigDecimal requestQty = request.getQty() == null ? BigDecimal.ZERO : request.getQty();
//                        BigDecimal requestQtyIn = request.getQtyIn() == null ? BigDecimal.ZERO : request.getQtyIn();
//                        BigDecimal transferQtyRefund = transferItem.getQtyRefund() == null ? BigDecimal.ZERO : transferItem.getQtyRefund();
//                        request.setQty(requestQty.add(transferQtyRefund));
//                        request.setQtyIn(requestQtyIn.add(transferQtyRefund));*/
//
//                        request.setQty(request.getQty() == null ? transferItem.getQtyRefund() == null ? BigDecimal.ZERO : transferItem.getQtyRefund() : request.getQty().add(transferItem.getQtyRefund()));
//                        request.setQtyIn(request.getQtyIn() == null ? transferItem.getQtyRefund() == null ? BigDecimal.ZERO : transferItem.getQtyRefund() : request.getQtyIn().add(transferItem.getQtyRefund()));
//                    } else {
//                        itemList.add(item);
//                        break;
//                    }
//                }
//            }
//        });
//        return itemList;
//    }

    /**
     * 根据实体仓id获取 实体仓信息
     *
     * @param warehouseId
     * @return CpCPhyWarehouse
     */
    private CpCPhyWarehouse getQueryByWarehouseId(Long warehouseId) {
        CpCPhyWarehouse cpCPhyWarehouse = new CpCPhyWarehouse();
        try {
            cpCPhyWarehouse = cpRpcService.queryByWarehouseId(warehouseId);
        } catch (Exception e) {
            log.error(LogUtil.format("调用根据仓库ID获取对象的rpc接口异常={}"), Throwables.getStackTraceAsString(e));
            return cpCPhyWarehouse;
        }
        return cpCPhyWarehouse == null ? new CpCPhyWarehouse() : cpCPhyWarehouse;
    }


    /**
     * 获取条码集合
     */
    private String getSkuAll(OcBReturnOrder ocBReturnOrder) {
        List<OcBReturnOrderRefund> rtnORfnList = ocBReturnOrderRefundMapper
                .queryRtnOrderRefundByoId(ocBReturnOrder.getId(), OcBOrderConst.IS_ACTIVE_YES);
        StringBuilder str = new StringBuilder();
        if (rtnORfnList == null || rtnORfnList.size() <= 0) {
            return "";
        }
        for (OcBReturnOrderRefund refund : rtnORfnList) {
            str.append(refund.getPsCSkuEcode());
            str.append(",");
        }
        str.deleteCharAt(str.length() - 1);
        return str.toString();
    }

    /**
     * 添加退换货订单操作日志
     *
     * @param user
     * @param ocBReturnOrder
     */
    @Async
    public void insertReturnOrderLog(User user, OcBReturnOrder ocBReturnOrder, String logMessage) {
        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
        ocBReturnOrderLog.setLogType("退换货单手动入库");
        ocBReturnOrderLog.setLogMessage(logMessage);
        if (logMessage.length() > 150) {
            ocBReturnOrderLog.setLogMessage(logMessage.substring(0, 150));
        }
        ocBReturnOrderLog.setLogParam("");
        ocBReturnOrderLog.setIpAddress(user.getLastloginip());
        ocBReturnOrderLog.setUserName(user.getName());
        ocBReturnOrderLog.setOwnerename(user.getEname());
        ocBReturnOrderLog.setOcBReturnOrderId(ocBReturnOrder.getId());
        ocBReturnOrderLog.setId(ModelUtil.getSequence(OC_B_RETURN_ORDER_LOG_INDEX_NAME));
        ocBReturnOrderLog.setAdOrgId(new Long(user.getOrgId()));
        ocBReturnOrderLog.setAdClientId(new Long(user.getClientId()));
        if (ocBReturnOrderLogMapper.insert(ocBReturnOrderLog) > 0) {
        }
    }

}

