package com.jackrain.nea.oc.oms.services;

import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.jackrain.nea.oc.oms.model.SendPlanExecution;
import com.jackrain.nea.st.service.SendPlanRuleQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @create 2020-06-17
 * @desc 派单方案
 **/
@Component
@Slf4j
public class SendPlanService {

    @Autowired
    private SendPlanRuleQueryService sendPlanRuleQueryService;

    @Autowired
    private SendRuleService sendRuleService;

    /**
     * 执行派单方案
     *
     * @param sendPlanExecution   派单方案执行参数
     * @param storageQueryResults 实体仓库存
     * @return
     */
    public Long execute(SendPlanExecution sendPlanExecution, List storageQueryResults) {
        //先根据店铺找派单方案,如果找不到,则随机取一个发货仓库
        List<Long> sendPlanList = sendPlanRuleQueryService.selectPlanByShopId(sendPlanExecution.getShopId());
        if (CollectionUtils.isNotEmpty(sendPlanList)) {
            //sendPlanList已按大促、活动、日常（大促>活动>日常）优先级和创建时间排序
            for (Long sendPlanId : sendPlanList) {
                Long phyWarehouseId = execute(sendPlanExecution, storageQueryResults, sendPlanId);
                if (phyWarehouseId != null) {
                    return phyWarehouseId;
                }
            }
        }
        return null;
    }

    /**
     * 执行派单方案
     *
     * @param sendPlanExecution   派单方案执行参数
     * @param storageQueryResults 实体仓库存
     * @param sendPlanId          派单方案ID
     * @return
     */
    private Long execute(SendPlanExecution sendPlanExecution, List storageQueryResults, Long sendPlanId) {
        //根据方案找派单规则
        List<Long> sendRuleIdList = sendPlanRuleQueryService.selectRuleById(sendPlanId);
        if (CollectionUtils.isNotEmpty(sendRuleIdList)) {
            //sendRuleIdList已按优先级和创建时间排序
            for (Long sendRuleId : sendRuleIdList) {
                Long phyWarehouseId = sendRuleService.execute(sendPlanExecution, storageQueryResults, sendRuleId);
                if (phyWarehouseId != null) {
                    return phyWarehouseId;
                }
            }
        }
        return null;
    }
}
