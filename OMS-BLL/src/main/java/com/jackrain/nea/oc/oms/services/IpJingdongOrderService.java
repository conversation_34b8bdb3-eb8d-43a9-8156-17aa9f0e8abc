package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-24
 * create at : 2019-04-24 3:49 PM
 * 中间表京东转单处理服务
 */

@Component
@Slf4j
public class IpJingdongOrderService {

    @Autowired
    private IpBJingdongOrderMapper ipBJingdongOrderMapper;

    @Autowired
    private IpBJingdongOrderItemMapper ipBJingdongOrderItemMapper;

    @Autowired
    private IpBJingdongCoupondtaiMapper ipBJingdongCoupondtaiMapper;

    @Autowired
    private IpBJingdongUserMapper ipBJingdongUserMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;


    /**
     * 依据OrderNo进行查询中间表京东信息数据
     *
     * @param orderNo 京东订单号
     * @return 中间表京东信息数据
     */
    public IpJingdongOrderRelation selectJingdongOrder(String orderNo) {
        QueryWrapper<IpBJingdongOrder> wrapper = new QueryWrapper<>();
        wrapper.eq("order_id", new BigDecimal(orderNo));
        IpBJingdongOrder orderInfo = this.ipBJingdongOrderMapper.selectOne(wrapper);

        if (orderInfo == null) {
            return null;
        }

        //获取京东相关主子表信息
        IpJingdongOrderRelation jingdongOrderRelation = new IpJingdongOrderRelation();

        //获取明细
        List<IpBJingdongOrderItemExt> orderItemList = this.ipBJingdongOrderItemMapper.selectJingdongOrderList(orderInfo.getId());

        //获取优惠信息
        QueryWrapper<IpBJingdongCoupondtai> coupondueryWrapper = new QueryWrapper<>();
        coupondueryWrapper.eq("ip_b_jingdong_order_id", orderInfo.getId());
        List<IpBJingdongCoupondtai> coupondtaiList = this.ipBJingdongCoupondtaiMapper.selectList(coupondueryWrapper);

        //获取京东用户信息表(收货人信息)
        QueryWrapper<IpBJingdongUser> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("ip_b_jingdong_order_id", orderInfo.getId());
        IpBJingdongUser ipBJingdongUser = this.ipBJingdongUserMapper.selectOne(userQueryWrapper);
        ipBJingdongUser.setFullAddress(ipBJingdongUser.getFullAddress().replaceAll(",", "::::"));
        jingdongOrderRelation.setJingdongOrder(orderInfo);
        jingdongOrderRelation.setJingdongOrderItems(orderItemList);
        jingdongOrderRelation.setJingdongCoupondtaiList(coupondtaiList);
        jingdongOrderRelation.setJingdongUser(ipBJingdongUser);
        return jingdongOrderRelation;
    }

    /**
     * “转换状态”=1,“转换时间”：当前时间
     *
     * @param order 京东中间表实体
     */

    public void updateIpJingdongOrderInfo(IpBJingdongOrder order, Long orderId) {
        //转换中或未转换
        UpdateWrapper<IpBJingdongOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("order_id", orderId);

        ipBJingdongOrderMapper.update(order, updateWrapper);
    }


    public void updateIpJdOrderAfterTransSuccess(Long orderId) {
        ipBJingdongOrderMapper.updateIpJdOrderAfterTransSuccess(orderId);
    }

    /**
     * @param isTrans
     * @param orderId
     * @param sysremark
     * @param abnormalType
     */
    public void updateIpJingdongExchangeOrderInfo(Integer isTrans, Long orderId, String sysremark, Integer abnormalType) {
        ipBJingdongOrderMapper.updateIpJingdongExchangeOrderInfo(isTrans, orderId, sysremark, abnormalType);
    }

    /**
     * @param sourceCode 平台单号
     * @return OcBOrder
     */
    public List<OcBOrder> selectOmsOrderInfo(Long sourceCode) {
        long l0 = System.nanoTime();
        //    List<Long> ids = ES4Order.getIdsBySourceCode(sourceCode);
        List<Long> ids = GSI4Order.getIdListBySourceCode(String.valueOf(sourceCode));
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        // 先查询ES，再查询Redis。防止重复转单。因为ES有可能延迟100MS
        long l1 = System.nanoTime();
        if (log.isDebugEnabled()) {
            log.debug(String.format("OmsOrderService： 京东转单查询ES或者redis是否存在此单据: %s",
                    (l1 - l0) / Math.pow(10, 6)));
        }
        List<OcBOrder> orderList = ocBOrderMapper.selectByIdsList(ids);
        return orderList;
    }


    /**
     * 查询订单是否存在Redis中
     *
     * @param sourceCode 原始订单号
     * @return
     */
    private long selectOmsOrderFromRedis(Long sourceCode) {
        String redisKey = BllRedisKeyResources.getOmsOrderKey(sourceCode.toString());
        CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        Boolean hasKey = objRedisTemplate.hasKey(redisKey);
        if (hasKey != null && hasKey) {
            Long value = objRedisTemplate.opsForValue().get(redisKey);
            if (value == null) {
                return 0L;
            }
            return value;
        } else {
            return 0L;
        }

    }

    /**
     * 订单取消退款时的逻辑
     *
     * @param ocBOrders
     * @param orderId   京东的平台单号
     */
    public Boolean handleOrder(List<OcBOrder> ocBOrders, Long orderId, User operateUser) {
        //将订单中作废和已取消的去掉
        List<OcBOrder> ocBOrderList = ocBOrders.stream().filter(ocBorder -> (
                        !ocBorder.getOrderStatus().equals(OmsOrderStatus.CANCELLED.toInteger())
                                && !ocBorder.getOrderStatus().equals(OmsOrderStatus.SYS_VOID.toInteger())
                )
        ).collect(Collectors.toList());
        boolean isExecute = false; //是否执行了取消退款的逻辑
        if (CollectionUtils.isEmpty(ocBOrderList)) {
            return isExecute;
        }
        for (OcBOrder ocBOrder : ocBOrderList) {
            List<OcBOrderItem> ocBOrderItemsList = orderItemMapper.selectOrderItemList(ocBOrder.getId());
            boolean isReturn = true;
            for (OcBOrderItem ocBOrderItem : ocBOrderItemsList) {
                if (ocBOrderItem.getRefundStatus() == OmsOrderRefundStatus.WAITSELLERAGREE.toInteger()) {
                    isReturn = false;
                    break;
                }
            }
            if (isReturn) {
                //当前订单没有买家已经申请退款，等待卖家同意状态的明细
                continue;
            }

            //将订单下平台单号为orderId的明细更新为0(未退款)
            orderItemMapper.updateRefundStatusByTidAndOrderId(OmsOrderRefundStatus.UNREFUND.toInteger(), orderId, ocBOrder.getId());
            //查询订单的明细
            List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItemList(ocBOrder.getId());
            //判断明细中是否还有等待退款
            boolean flag = true;
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                if (ocBOrderItem.getRefundStatus() != OmsOrderRefundStatus.UNREFUND.toInteger()) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                //当前订单下的商品明细都为未退款(取消拦截)
                OcBOrder order = new OcBOrder();
                order.setId(ocBOrder.getId());
                order.setIsInreturning(0);
                returnOrderTransferUtil.updateOperator(order, operateUser);
                omsOrderService.updateOrderInfo(order);
                // 修改hold单状态 使用HOLD单方法修改
                order.setIsInterecept(0);//是否已经拦截 使用HOLD单方法修改
                ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
                //记录日志信息
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "订单取消拦截", null, null, operateUser);
                isExecute = true;
            }
        }
        return isExecute;
    }
}
