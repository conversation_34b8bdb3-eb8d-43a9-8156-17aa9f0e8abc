package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cp.enums.DrpStoreTypeEnum;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.enums.IsWithoutOrigEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBStoreRefundInStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * className: OcBStoreRefundInService
 * description:门店退货入库单服务
 *
 * <AUTHOR>
 * create: 2021-06-30
 * @since JDK 1.8
 */
@Component
@Slf4j
public class OcBStoreRefundInService {

    @Autowired
    private OcBRefundInMapper refundInMapper;

    @Autowired
    private OcBRefundInProductItemMapper productItemMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderExchangeMapper returnOrderExchangeMapper;

    @Autowired
    private OcBReturnOrderRefundMapper returnOrderRefundMapper;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder save(QuerySession querySession){

        User user = querySession.getUser();
        ValueHolder valueHolder = CommandAdapterUtil.checkSaveSession(querySession, OcCommonConstant.OC_B_STORE_REFUND_IN);
        if (!valueHolder.isOK()) {
            return valueHolder;
        }

        JSONObject fixColumn = (JSONObject)valueHolder.getData().get(OcCommonConstant.FIX_COLUMN);
        JSONObject mainObj = ((JSONObject)fixColumn.get(OcCommonConstant.OC_B_STORE_REFUND_IN));
        List<OcBRefundInProductItem> items = new ArrayList<>();
        JSONArray itemArray = fixColumn.getJSONArray(OcCommonConstant.OC_B_STORE_REFUND_IN_ITEM);
        if(!ObjectUtils.isEmpty(itemArray) && !itemArray.isEmpty()){
            itemArray.forEach(obj -> items.add(JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(obj)),OcBRefundInProductItem.class)));
        }

        Long id = (Long)((HashMap)valueHolder.getData().get("data")).get(OcCommonConstant.OBJ_ID);

        if(!ObjectUtils.isEmpty(mainObj)){

            OcBRefundIn refundIn = mainObj.toJavaObject(OcBRefundIn.class);
            //如果物流单号在记录中已存在，提示：“物流单号已存在，不允许保存”
            if(!checkUnique(id,refundIn)){
                return ValueHolderUtils.getFailValueHolder("物流单号已存在，不允许保存");
            }

            //物流公司、实体仓code、ename赋值
            if(!ObjectUtils.isEmpty(refundIn.getCpCLogisticsId())){
                CpLogistics logistics = cpRpcService.selectLogisticsById(refundIn.getCpCLogisticsId());
                if(ObjectUtils.isEmpty(logistics)){
                    return ValueHolderUtils.getFailValueHolder("该物流公司不存在，请检查物流公司档案");
                }
                refundIn.setCpCLogisticsEcode(logistics.getEcode());
                refundIn.setCpCLogisticsEname(logistics.getEname());
            }
            if(!ObjectUtils.isEmpty(refundIn.getCpCPhyWarehouseId())){
                CpCPhyWarehouse phyWarehouse = cpRpcService.selectPhyWarehouseById(refundIn.getCpCPhyWarehouseId());
                if(ObjectUtils.isEmpty(phyWarehouse)){
                    return ValueHolderUtils.getFailValueHolder("该实体仓不存在，请检查实体仓档案");
                }
                refundIn.setCpCPhyWarehouseEcode(phyWarehouse.getEcode());
                refundIn.setCpCPhyWarehouseEname(phyWarehouse.getEname());
                //查找关联逻辑仓档案
                List<Long> storeIds = cpRpcService.queryStoreList(refundIn.getCpCPhyWarehouseId());
                if(CollectionUtils.isEmpty(storeIds)){
                    return ValueHolderUtils.getFailValueHolder("该实体仓关联逻辑仓不存在，请检查逻辑仓档案");
                }
                CpStore cpStore = cpRpcService.selectCpCStoreById(storeIds.get(0));
                if(ObjectUtils.isEmpty(cpStore)){
                    return ValueHolderUtils.getFailValueHolder("该实体仓关联逻辑仓不存在，请检查逻辑仓档案");
                }
                refundIn.setInStoreId(cpStore.getId());
                refundIn.setInStoreEcode(cpStore.getEcode());
                refundIn.setInStoreEname(cpStore.getEname());
                refundIn.setStoreId(cpStore.getId());
                refundIn.setStoreCode(cpStore.getEcode());
                refundIn.setStoreName(cpStore.getEname());

            }

            //框架字段赋值
            refundIn.setId(id);
            CommandAdapterUtil.defaultOperator(refundIn,user);
            int num;
            if (id < 0) {

            /*
            c. 如果入库仓库为空，则提示：“入库仓库为空，不允许保存”
            d. 如果物流单号为空，则提示：“物流单号为空，不允许保存”
            */
                if(ObjectUtils.isEmpty(refundIn.getCpCPhyWarehouseId())){
                    return ValueHolderUtils.getFailValueHolder("入库仓库为空，不允许保存");
                }
                if(ObjectUtils.isEmpty(refundIn.getLogisticNumber())){
                    return ValueHolderUtils.getFailValueHolder("物流单号为空，不允许保存");
                }

                //新增
                id = ModelUtil.getSequence(OcCommonConstant.OC_B_REFUND_IN);
                refundIn.setId(id);
                refundIn.setOwnerename(user.getEname());
                refundIn.setModifierename(user.getEname());
                num = refundInMapper.insert(refundIn);
            }else {
                // 如果单据状态为已提交或已作废，则提示：“单据状态不符合，不允许保存”
                OcBRefundIn old = refundInMapper.selectById(refundIn.getId());
                if(ObjectUtils.isEmpty(old)){
                    return ValueHolderUtils.getFailValueHolder("操作的单据已不存在");
                }
                if(!OcBStoreRefundInStatusEnum.INIT.getVal().equals(old.getOrderStatus())){
                    return ValueHolderUtils.getFailValueHolder("单据状态不符合，不允许保存");
                }

                //更新
                refundIn.setModifierename(user.getEname());
                num = refundInMapper.updateById(refundIn);
            }
        }

        //保存明细
        saveItems(items,id,user);

        return ValueHolderUtils.getSuccessValueHolder(id,OcCommonConstant.OC_B_STORE_REFUND_IN,"保存成功");
    }

    public ValueHolder delete(QuerySession querySession){
        ValueHolder valueHolder = CommandAdapterUtil.checkDeleteSession(querySession, OcCommonConstant.OC_B_STORE_REFUND_IN);
        if (!valueHolder.isOK()) {
            return valueHolder;
        }
        HashMap data = (HashMap)valueHolder.getData().get("data");
        Long id = (Long)data.get(OcCommonConstant.OBJ_ID);
        JSONObject tabitem = (JSONObject) valueHolder.getData().get("tabitem");
        JSONArray itemIds = tabitem.getJSONArray(OcCommonConstant.OC_B_STORE_REFUND_IN_ITEM);
        Boolean isdelmtable = (Boolean)valueHolder.getData().get("isdelmtable");

        //校验主表状态
        OcBRefundIn old = refundInMapper.selectById(id);
        if(!OcBStoreRefundInStatusEnum.INIT.getVal().equals(old.getOrderStatus())){
            return ValueHolderUtils.getFailValueHolder("单据状态不符合，不允许删除");
        }

        if(Boolean.FALSE.equals(isdelmtable)){
            //删除明细
            if(!ObjectUtils.isEmpty(itemIds)){
                Set<Long> deleteIds = new HashSet<>();
                itemIds.forEach(itemId -> deleteIds.add(Long.valueOf((String)itemId)));
                productItemMapper.deleteBatchIds(deleteIds);
            }
        }else {
            //删除主表
            refundInMapper.deleteById(id);
        }

        return ValueHolderUtils.getSuccessValueHolder("删除成功");
    }

    public ValueHolder submit(QuerySession querySession){
        return updateStatus(querySession,OcBStoreRefundInStatusEnum.SUBMITTED);
    }

    public ValueHolder cancel(QuerySession querySession){
        return updateStatus(querySession,OcBStoreRefundInStatusEnum.CANCELED);
    }


    public ValueHolder inStoreByReturnOrder(QuerySession querySession){
        ValueHolder check = checkFrontParam(querySession);
        if(!check.isOK()){
            return check;
        }
        User user = querySession.getUser();
        JSONArray ids = (JSONArray) check.get("data");
        JSONArray errJa = new JSONArray();

        for(Object returnId : ids){
            try {
                OcBStoreRefundInService refundInService = ApplicationContextHandle.getBean(OcBStoreRefundInService.class);
                refundInService.singleInStore(returnId,errJa,user);
            }catch (Exception e){
                log.error(" 一键入库异常:{}",Throwables.getStackTraceAsString(e));
                JSONObject errjo = new JSONObject();
                errjo.put("objid",returnId);
                errjo.put("message","一键入库异常");
                errJa.add(errjo);
            }
        }

        ValueHolder valueHolder;

        if(errJa.isEmpty()){
            valueHolder = ValueHolderUtils.getSuccessValueHolder("一键入库成功：" + ids.size() + "条");
        }else {
            String errMsg = JSON.toJSONString(errJa);
            valueHolder = ValueHolderUtils
                    .getFailValueHolder("一键入库成功：" + (ids.size()-errJa.size()) + "条，" +
                            "失败" + errJa.size() +"条：" +
                            errMsg.substring(0,errMsg.length() > 100 ? 100 : errMsg.length()));
            valueHolder.put("data",errJa);
        }

        return valueHolder;
    }

    @Transactional(rollbackFor = Exception.class)
    public void singleInStore(Object returnId,JSONArray errJa,User user){
        JSONObject errjo = new JSONObject();
        Long id = Long.valueOf((String)returnId);
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(id);
        if(ObjectUtils.isEmpty(ocBReturnOrder)){
            errjo.put("objid",id);
            errjo.put("message","退换货单已不存在");
            errJa.add(errjo);
            return;
        }
        if(log.isDebugEnabled()){
            log.debug(" 一件入库，退换货单信息：{}",JSON.toJSONString(ocBReturnOrder));
        }
        //只允许【原退&待退货入库】状态的单子操作
        if(!OmsParamConstant.YES.equals(ocBReturnOrder.getIsBack()) ||
                !ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(ocBReturnOrder.getReturnStatus())){
            errjo.put("objid",id);
            errjo.put("message","退换货单非【原退&待退货入库】，不允许一键入库");
            errJa.add(errjo);
            return;
        }

        //组装门店入库单参数
        OcBRefundIn ocBRefundIn = new OcBRefundIn();
        CommandAdapterUtil.defaultOperator(ocBRefundIn,user);
        ocBRefundIn.setId(ModelUtil.getSequence(OcCommonConstant.OC_B_REFUND_IN));
        ocBRefundIn.setOwnerename(user.getEname());
        ocBRefundIn.setModifierename(user.getEname());
        ocBRefundIn.setLogisticNumber(ocBReturnOrder.getLogisticsCode());
        ocBRefundIn.setCpCLogisticsId(ocBReturnOrder.getCpCLogisticsId());
        ocBRefundIn.setCpCLogisticsEcode(ocBReturnOrder.getCpCLogisticsEcode());
        ocBRefundIn.setCpCLogisticsEname(ocBReturnOrder.getCpCLogisticsEname());
        ocBRefundIn.setCpCPhyWarehouseId(ocBReturnOrder.getCpCPhyWarehouseInId());
        CpCPhyWarehouse phyWarehouse = cpRpcService.selectPhyWarehouseById(ocBReturnOrder.getCpCPhyWarehouseInId());
        if(log.isDebugEnabled()){
            log.debug(" 一件入库，原退实体仓id：{},查询实体仓结果：{}",
                    ocBReturnOrder.getCpCPhyWarehouseInId(),JSON.toJSONString(phyWarehouse));
        }
        if(!ObjectUtils.isEmpty(phyWarehouse)){
            ocBRefundIn.setCpCPhyWarehouseEcode(phyWarehouse.getEcode());
            ocBRefundIn.setCpCPhyWarehouseEname(phyWarehouse.getEname());
            //查找关联逻辑仓档案
            List<Long> storeIds = cpRpcService.queryStoreList(ocBReturnOrder.getCpCPhyWarehouseId());
            if(CollectionUtils.isEmpty(storeIds)){
                errjo.put("objid",id);
                errjo.put("message","该实体仓关联逻辑仓不存在，请检查逻辑仓档案");
                errJa.add(errjo);
                return;
            }

            CpStore cpStore = null;
            if(OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE_STORE_02.equals(phyWarehouse.getWhType())){
                cpStore = cpRpcService.selectCpCStoreById(storeIds.get(0));
            }else {
                List<CpCStore> cpCStoreList = cpRpcService
                        .queryStoreInfoByIds(storeIds.stream().map(Long::intValue).collect(Collectors.toList()));
                if(!CollectionUtils.isEmpty(cpCStoreList)){
                    Optional<CpCStore> optionalCpCStore = cpCStoreList.stream()
                            .filter(s -> DrpStoreTypeEnum.TYPE_27.getValue().equals(s.getStoretype())).findFirst();
                    if(optionalCpCStore.isPresent()){
                        cpStore = new CpStore();
                        BeanUtils.copyProperties(optionalCpCStore.get(),cpStore);
                    }
                }
            }

            if(ObjectUtils.isEmpty(cpStore)){
                errjo.put("objid",id);
                errjo.put("message","该实体仓关联逻辑仓(退货仓库)不存在，请检查逻辑仓档案");
                errJa.add(errjo);
                return;
            }
            ocBRefundIn.setInStoreId(cpStore.getId());
            ocBRefundIn.setInStoreEcode(cpStore.getEcode());
            ocBRefundIn.setInStoreEname(cpStore.getEname());
            ocBRefundIn.setStoreId(cpStore.getId());
            ocBRefundIn.setStoreCode(cpStore.getEcode());
            ocBRefundIn.setStoreName(cpStore.getEname());
        }

        ocBRefundIn.setOrderStatus(OcBStoreRefundInStatusEnum.SUBMITTED.getVal());
        ocBRefundIn.setSubmitId(Long.valueOf(user.getId()));
        ocBRefundIn.setSubmitDate(new Date());
        //以下字段赋值之后才能被自动任务扫描到
        ocBRefundIn.setMatchStatus(0);
        ocBRefundIn.setIsOffMatch(0);
        ocBRefundIn.setInStatus(2);

        //如果物流单号在记录中已存在，提示：“物流单号已存在，不允许保存”
        if(!checkUnique(id,ocBRefundIn)){
            errjo.put("objid",id);
            errjo.put("message","物流单号已存在退货入库单，无需一键入库");
            errJa.add(errjo);
            return;
        }

        //保存主表
        int num = refundInMapper.insert(ocBRefundIn);

        if(num <= 0){
            errjo.put("objid",id);
            errjo.put("message","一键入库失败：保存退货入库单主表失败");
            errJa.add(errjo);
            return;
        }

        List<OcBReturnOrderRefund> returnOrderRefunds = returnOrderRefundMapper.selectList(new LambdaQueryWrapper<OcBReturnOrderRefund>()
                .eq(OcBReturnOrderRefund::getIsactive, R3CommonResultConstants.VALUE_Y)
                .eq(OcBReturnOrderRefund::getOcBReturnOrderId,returnId));
        List<OcBReturnOrderExchange> returnOrderExchanges = returnOrderExchangeMapper.selectList(new LambdaQueryWrapper<OcBReturnOrderExchange>()
                .eq(OcBReturnOrderExchange::getOcBReturnOrderId,returnId)
                .eq(OcBReturnOrderExchange::getIsactive, R3CommonResultConstants.VALUE_Y));

        //操作明细
        List<OcBRefundInProductItem> items = new ArrayList<>();
        if(!CollectionUtils.isEmpty(returnOrderExchanges)){
            returnOrderExchanges.forEach(e -> {
                OcBRefundInProductItem item = new OcBRefundInProductItem();
                item.setId(-1L);
                item.setPsCSkuId(e.getPsCSkuId());
                item.setQty(e.getQtyExchange());
                items.add(item);
            });
        }else if(!CollectionUtils.isEmpty(returnOrderRefunds)){
            returnOrderRefunds.forEach(r -> {
                OcBRefundInProductItem item = new OcBRefundInProductItem();
                item.setId(-1L);
                item.setQty(r.getQtyRefund());
                item.setPsCSkuId(r.getPsCSkuId());
                items.add(item);
            });
        }

        saveItems(items,ocBRefundIn.getId(),user);

    }

    private ValueHolder updateStatus(QuerySession querySession,OcBStoreRefundInStatusEnum status){

        ValueHolder check = checkFrontParam(querySession);
        if(!check.isOK()){
            return check;
        }
        JSONArray ids = (JSONArray) check.get("data");
        User user = querySession.getUser();
        JSONArray errJa = new JSONArray();
        String operate = OcBStoreRefundInStatusEnum.SUBMITTED.equals(status) ? "提交" : "作废";
        for(Object mainId : ids){
            //校验主表状态
            JSONObject errjo = new JSONObject();
            Long id = Long.valueOf((String)mainId);
            OcBRefundIn old = refundInMapper.selectById(id);
            if(!OcBStoreRefundInStatusEnum.INIT.getVal().equals(old.getOrderStatus())){
                errjo.put("objid",id);
                errjo.put("message","单据状态不符合，不允许" + operate);
                errJa.add(errjo);
                continue;
            }

            OcBRefundIn refundIn = new OcBRefundIn();
            refundIn.setId(id);
            refundIn.setOrderStatus(status.getVal());
            if(OcBStoreRefundInStatusEnum.SUBMITTED.equals(status)){
                refundIn.setSubmitId(Long.valueOf(user.getId()));
                //以下字段赋值之后才能被自动任务扫描到
                refundIn.setMatchStatus(0);
                refundIn.setIsOffMatch(0);
                refundIn.setInStatus(2);
                refundIn.setSubmitDate(new Date());
                BaseModelUtil.makeBaseModifyField(refundIn,user);
            }
            if(OcBStoreRefundInStatusEnum.CANCELED.equals(status)){
                refundIn.setCancelId(Long.valueOf(user.getId()));
                refundIn.setCancelDate(new Date());
                BaseModelUtil.makeBaseModifyField(refundIn,user);
            }

            try {
                refundInMapper.updateById(refundIn);
            }catch (Exception e){
                log.error(LogUtil.format("门店退货入库,异常{}"), Throwables.getStackTraceAsString(e));
                errjo.put("objid",id);
                errjo.put("message",operate + "异常");
                errJa.add(errjo);
            }
        }

        ValueHolder valueHolder;

        if(errJa.isEmpty()){
            valueHolder = ValueHolderUtils.getSuccessValueHolder(operate + "成功：" + ids.size() + "条");
        }else {
            valueHolder = ValueHolderUtils
                    .getFailValueHolder(operate + "成功：" + (ids.size()-errJa.size()) + "条，" +
                            "失败" + errJa.size() +"条");
            valueHolder.put("data",errJa);
        }

        return valueHolder;
    }


    private ValueHolder checkFrontParam(QuerySession querySession){

        if(log.isDebugEnabled()){
            log.debug(" 前端参数校验，param：{}",querySession.toDebugString());
        }
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param")));
        if(ObjectUtils.isEmpty(param)){
            return ValueHolderUtils.getFailValueHolder("参数不能为空");
        }
        JSONArray ids = param.getJSONArray("ids");
        if(ObjectUtils.isEmpty(ids)){
            return ValueHolderUtils.getFailValueHolder("请选择需要操作的数据");
        }

        return ValueHolderUtils.custom(ResultCode.SUCCESS,"校验通过",ids);
    }

    private boolean checkUnique(Long id,OcBRefundIn refundIn){
        String logisticNumber = refundIn.getLogisticNumber();

        //如果物流单号为空，直接校验通过
        if(ObjectUtils.isEmpty(logisticNumber)){
            return true;
        }

        return CollectionUtils.isEmpty(refundInMapper
                .selectList(new LambdaQueryWrapper<OcBRefundIn>()
                        .eq(OcBRefundIn::getLogisticNumber,logisticNumber)
                        .ne(OcBRefundIn::getId,id)));
    }

    private void saveItems(List<OcBRefundInProductItem> items,Long mainId,User user){
        if(CollectionUtils.isEmpty(items)){
            return;
        }

        List<OcBRefundInProductItem> insertItems = new ArrayList<>();
        List<OcBRefundInProductItem> modifyItems = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();
        items.forEach(item -> skuIds.add(item.getPsCSkuId()));

        List<OcBRefundInProductItem> oldItems = productItemMapper
                .selectList(new QueryWrapper<OcBRefundInProductItem>().lambda()
                .eq(OcBRefundInProductItem::getOcBRefundInId,mainId)
                .in(OcBRefundInProductItem::getPsCSkuId,skuIds));

        //如果SKU编码已存在，数量累加
        for(OcBRefundInProductItem item : items){
            boolean isExist = false;
            if(!CollectionUtils.isEmpty(oldItems)){
                for(OcBRefundInProductItem old : oldItems){
                    if(old.getPsCSkuId().equals(item.getPsCSkuId())){
                        OcBRefundInProductItem modifyItem = new OcBRefundInProductItem();
                        modifyItem.setId(old.getId());
                        modifyItem.setQty(old.getQty().add(item.getQty()));
                        modifyItems.add(modifyItem);
                        BaseModelUtil.makeBaseModifyField(modifyItem,user);
                        modifyItem.setModifierename(user.getEname());
                        isExist = true;
                        break;
                    }
                }
            }
            if(!isExist){
                insertItems.add(item);
            }
        }

        if(!CollectionUtils.isEmpty(insertItems)){
            //查询需要新增的SKU信息，并赋值给明细
            insertItems.forEach(item -> {
                List<Integer> skuIdList = new ArrayList<>();
                skuIdList.add(item.getPsCSkuId().intValue());
                List<SkuQueryListRequest> specList = psRpcService.querySkuListByIds(skuIdList);
                AssertUtil.assertException(CollectionUtils.isEmpty(specList),"SKU不存在");
                SkuQueryListRequest sku = specList.get(0);
                BaseModelUtil.makeBaseCreateField(item,user);
                item.setIsMatch(0);
                item.setIsWithoutOrig(IsWithoutOrigEnum.IS_WITHOUT_ORIG.getVal());
                item.setOwnerename(user.getEname());
                item.setModifierename(user.getEname());
                item.setId(ModelUtil.getSequence(OcCommonConstant.OC_B_REFUND_IN_PRODUCT_ITEM));
                item.setOcBRefundInId(mainId);
                item.setGbcode(sku.getGbcode());
                item.setPsCSkuEcode(sku.getEcode());
                item.setPsCProId(sku.getPsCProId());
                item.setPsCProEcode(sku.getPsCProEcode());
                item.setPsCProEname(sku.getPsCProEname());
                item.setPsCClrId(sku.getColorId());
                item.setPsCClrEcode(sku.getColorEcode());
                item.setPsCClrEname(sku.getColorName());
                item.setPsCSizeId(sku.getSizeId());
                item.setPsCSizeEcode(sku.getSizeEcode());
                item.setPsCSizeEname(sku.getSizeName());
            });

            productItemMapper.batchInsert(insertItems);
        }

        if(!CollectionUtils.isEmpty(modifyItems)){
            modifyItems.forEach(modify -> productItemMapper.updateById(modify));
        }

    }
}
