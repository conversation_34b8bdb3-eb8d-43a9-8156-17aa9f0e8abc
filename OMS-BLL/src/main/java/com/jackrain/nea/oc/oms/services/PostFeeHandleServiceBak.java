package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.util.BigDecimalUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @author: qin jun long
 * @since: 2020/7/12
 */
@Slf4j
@Component
public class PostFeeHandleServiceBak {
    @Autowired
    private IpBTaobaoOrderMapper ipTaobaoOrderMapper;

    /**
     * 设置失效时间
     */
    public static final long REDIS_TIME_OUT = 1_000L * 60 * 60 * 24 * 5;

    public static final long REDIS_TIME_DAY_OUT = 15;


    public void addPostFeeItem(OcBOrder order, List<OcBOrderItem> itemList, Map<String, IpBTaobaoOrder> taobaoOrderMap) {
        if (Objects.isNull(order.getPlatform()) || !Objects.equals(order.getPlatform(), PlatFormEnum.TAOBAO.getCode())) {
            return;
        }
        String redisKey;
        List<OcBOrderItem> postFeeList = Lists.newArrayList();
        for (OcBOrderItem item : itemList) {
            redisKey = getKey(item.getTid());
            Boolean hasKey = RedisMasterUtils.getObjRedisTemplate().hasKey(redisKey);
            if (hasKey != null && !hasKey) {
                IpBTaobaoOrder ipBTaobaoOrder = taobaoOrderMap.get(item.getTid());
                if (ipBTaobaoOrder == null) {
                    continue;
                }
                BigDecimal postFee = ipBTaobaoOrder.getPostFee();
                if (BigDecimalUtil.isZero(postFee)) {
                    continue;
                }
                RedisMasterUtils.getObjRedisTemplate().opsForValue().set(redisKey, item.getTid(), REDIS_TIME_DAY_OUT, TimeUnit.DAYS);
                postFeeList.add(this.getPostFeeItemInfo(postFee, item.getTid()));
            }
        }
        if (!postFeeList.isEmpty()) {
            itemList.addAll(postFeeList);
        }
    }


    /**
     * 获取 Apollo 上配置的邮费sku信息
     *
     * @return
     */
    public OcBOrderItem getPostFeeItemInfo(BigDecimal postFee, String tid) {
        if (BigDecimalUtil.isZero(postFee)) {
            return null;
        }
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        // todo change arg
        String postFeeJsonSkuCode = config.getProperty("oms.order.custom.postFee.sku", "SMYF001");
        OcBOrderItem ocBOrderItem = new OcBOrderItem();
        ocBOrderItem.setPsCSkuEcode(postFeeJsonSkuCode);
        // 邮费
        ocBOrderItem.setRealAmt(postFee);
        ocBOrderItem.setTid(tid);
        return ocBOrderItem;
    }


    /**
     * 根据平台获取单据邮费
     *
     * @param order
     * @return
     */
    private BigDecimal getPostFeeByPlatform(OcBOrder order, Map<String, IpBTaobaoOrder> taobaoOrderMap) {
        Integer platform = order.getPlatform();
        if (Objects.isNull(platform) || !Objects.equals(platform, PlatFormEnum.TAOBAO.getCode())) {
            return BigDecimal.ZERO;
        }
        // 合单查找下面所有的平台单号的运费
        if (Objects.equals(order.getIsMerge(), 1)) {
            String[] sourceCode = StringUtils.split(order.getSourceCode(), ",");
            BigDecimal postFee = BigDecimal.ZERO;
            for (String tid : sourceCode) {
                IpBTaobaoOrder tbOrder = taobaoOrderMap.get(tid);
                if (Objects.nonNull(tbOrder) && tbOrder.getPostFee() != null) {
                    postFee = postFee.add(tbOrder.getPostFee());
                }
            }
            return postFee;
        }
        // 拆单只有第一单才传运费
        else if (Objects.equals(order.getIsSplit(), 1)) {
            String tid = order.getTid();
            String redisKey = getKey(tid);
            Boolean hasKey = RedisMasterUtils.getObjRedisTemplate().hasKey(redisKey);
            if (hasKey != null && hasKey) {
                return BigDecimal.ZERO;
            } else {
                RedisMasterUtils.getObjRedisTemplate().opsForValue().set(redisKey, tid, REDIS_TIME_OUT, TimeUnit.MILLISECONDS);
            }
        }
        IpBTaobaoOrder tbOrder = taobaoOrderMap.get(order.getTid());
        return Objects.isNull(tbOrder) ? BigDecimal.ZERO : tbOrder.getPostFee();
    }

    private String getKey(String tid) {
        return "oc:oms:order:to:ac:split:" + tid;
    }

    /**
     * 根据平台获取单据邮费
     *
     * @param order
     * @param tid   平台单号
     * @return
     */
    public BigDecimal getPostFeeByPlatform(OcBOrder order, List<String> tid) {
        Integer platform = order.getPlatform();
        if (Objects.isNull(platform) || Objects.isNull(tid)) {
            return BigDecimal.ZERO;
        }
        if (Objects.equals(platform, PlatFormEnum.TAOBAO.getCode())) {
            List<IpBTaobaoOrder> tbOrders = ipTaobaoOrderMapper.selectList(new LambdaQueryWrapper<IpBTaobaoOrder>().in(IpBTaobaoOrder::getTid, tid));
            return CollectionUtils.isEmpty(tbOrders) ? BigDecimal.ZERO : tbOrders.stream().map(IpBTaobaoOrder::getPostFee).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return BigDecimal.ZERO;
    }
}
