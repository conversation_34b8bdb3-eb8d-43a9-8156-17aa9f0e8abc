package com.jackrain.nea.oc.oms.services;


import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * mq消费后，更新订单状态为“平台发货”后端服务
 *
 * @author: hulinyang
 * @since: 2019/4/02
 * create at : 019/4/02 15:29
 */
@Component
@Slf4j

public class OmsUpdatePlatformDeliveryStatusService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderItemService omsOrderItemServie;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    /**
     * 批量更新主子表订单状态,只推子表
     *
     * @param ocBOrder 订单信息
     * @return boolean
     */
    public boolean updateSublistOrderStatus(OcBOrder ocBOrder) {

        boolean flag = false;
        Long orderId = ocBOrder.getId();
        List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectUnSuccessRefund(orderId);
        if (ocBOrderItemList.size() > 0) {
            for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                ocBOrderItem.setIsSendout(1);
                flag = omsOrderItemServie.updateOcBOrderItem(ocBOrderItem, orderId);
            }
        } else {
            flag = false;
        }
        return flag;
    }

    /**
     * 单个更新主子表订单状态,只推子表
     *
     * @param ocBOrderItem 订单信息
     * @return boolean
     */
    public boolean updateSublistOrderStatusPushEs(OcBOrderItem ocBOrderItem) {

        boolean flag;
        try {
            Long orderId = ocBOrderItem.getOcBOrderId();
            //调用贺柳更新子表，并推送子表es公共方法；OcBOrderItem orderItemDto, Long orderId
            ocBOrderItem.setIsSendout(1);
            flag = omsOrderItemServie.updateOcBOrderItem(ocBOrderItem, orderId);

        } catch (Exception ex) {
            flag = false;
            log.error(LogUtil.format("订单平台发货服务，接收单个更新主子表订单状态,只推子表时异常{},异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
        return flag;
    }


    /**
     * 更新主表订单状态
     *
     * @param ocBOrder 订单信息
     * @return boolean
     */
    public boolean updateMasterOrderStatus(OcBOrder ocBOrder) {
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        order.setModifieddate(new Date());
        return omsOrderService.updateOrderInfo(order);
    }

    /**
     * 添加订单日志信息
     *
     * @param ocBOrderRelation 订单关系实体
     * @return boolean
     */
    public boolean addOrderLog(OcBOrderRelation ocBOrderRelation, User user) {

        boolean flag;
        try {
            OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
            if (ocBOrder == null) {
                flag = false;
            } else {
                flag = true;
            }
        } catch (Exception ex) {
            flag = false;
            omsOrderLogService.addUserOrderLog(ocBOrderRelation.getOrderInfo().getId(),
                    ocBOrderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), null, null, ex.getMessage(), null);
        }
        return flag;
    }

}
