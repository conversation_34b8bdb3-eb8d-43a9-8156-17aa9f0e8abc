package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.ProductSku;

import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 虚拟处理服务
 *
 * @author: heliu
 * @since: 2019/3/15
 * create at : 2019/3/15 15:29
 */
@Component
@Slf4j
public class OmsOrderDifferenPriceService {


    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private StRpcService stRpcService;

    /**
     * 判断是否全部为虚拟
     *
     * @param orderInfo 订单对象
     * @return string
     */
    public String doAllExistDifferenPrice(OcBOrderRelation orderInfo) {

        List<String> queryDiffpriceskuList = selectVirtualSkuEcodeList(orderInfo);
        if (CollectionUtils.isEmpty(queryDiffpriceskuList)) {
            return "none";
        } else {
            if (orderInfo.getOrderItemList().size() == selectVirtualSkuEcodeList(orderInfo).size()) {
                return "all";
            } else {
                orderInfo.setVirtualSkuCodeList(queryDiffpriceskuList);
                return "part";
            }
        }
    }


    /**
     * 先判断是否存在为虚拟条码--> [供hly调用]
     *
     * @param ocBOrderRelation 订单对象
     * @return boolean
     */
    public boolean doExistDifferenPrice(OcBOrderRelation ocBOrderRelation) {

        //只要找到虚拟条码返回true
        if (CollectionUtils.isNotEmpty(selectVirtualSkuEcodeList(ocBOrderRelation))) {
            return true;
        }
        return false;
    }

    /**
     * 查询虚拟商品条码
     *
     * @param orderRelation 订单对象
     * @return boolean
     */
    public List<String> selectVirtualSkuEcodeList(OcBOrderRelation orderRelation) {
        //获取非退款成功的明细
        List<OcBOrderItem> skuItemList = omsOrderItemService.selectUnSuccessRefund(orderRelation.getOrderId());
        //如果没有非退款的重新new个对象
        if (CollectionUtils.isEmpty(skuItemList)) {
            return new ArrayList<>();
        }
        List<String> isVirtualskuEcodeList = new ArrayList<>();
        //明细条码id
        for (OcBOrderItem orderItemInfo : skuItemList) {
            String psCskuEcode = orderItemInfo.getPsCSkuEcode();

            log.debug("订单OrderId" + orderRelation.getOrderId() + "productService.selectProductById,请求参数为：" + String.valueOf(orderItemInfo.getPsCSkuId()));
            ProductSku productSku = psRpcService.selectProductById(String.valueOf(orderItemInfo.getPsCSkuId()));
            log.debug("订单OrderId" + orderRelation.getOrderId() + "的订单ProductSku数据为：" + productSku);
            if (productSku != null && "Y".equals(productSku.getIsVirtual())) {
                isVirtualskuEcodeList.add(psCskuEcode);
            }
        }
        return isVirtualskuEcodeList;
    }

    /**
     * 传入店铺和sku,判断该sku是否为虚拟条码
     *
     * @param shopId   店铺Id
     * @param skuEcode 条码Ecode
     * @return boolean
     */
    public boolean isDifferenPriceSku(Long shopId, String skuEcode) {

        return stRpcService.isDiffPriceSku(shopId, skuEcode);
    }
}
