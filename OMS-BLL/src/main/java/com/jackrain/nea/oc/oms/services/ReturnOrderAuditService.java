package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.ac.service.AcFInvoiceReturnRedOffsetService;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.model.CpCLogisticsItem;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.hub.model.minipt.OrderRefundReq;
import com.jackrain.nea.ip.api.qimen.QimenReturnOrderConfirmCmd;
import com.jackrain.nea.ip.common.QiMenSdkConstant;
import com.jackrain.nea.ip.model.dms.request.DmsOrderBackItemRequest;
import com.jackrain.nea.ip.model.dms.request.DmsOrderBackRequest;
import com.jackrain.nea.ip.model.qimen.ReturnOrderConfirmItemModel;
import com.jackrain.nea.ip.model.qimen.ReturnOrderConfirmModel;
import com.jackrain.nea.ip.model.sap.SapOrderBackRequest;
import com.jackrain.nea.ip.service.IpOrderCancelToAgService;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.IsReservedEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWrongReceive;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderIsInterceptEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderSource;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendInStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnBillReceiveConfirm;
import com.jackrain.nea.oc.oms.model.enums.ReturnOrderIsAddEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.VirtualInStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpOrderReturnRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.OcBReturnStdNotice;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.RefundOrderSourceTypeEnum;
import com.jackrain.nea.oc.oms.nums.ReturnOrderNodeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.StandplatOrderTransferUtil;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.psext.api.PsCSkuThirdItemQueryCmd;
import com.jackrain.nea.psext.result.PsCSkuThirdItemResult;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.HubRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.service.OmsOrderStCAutocheckService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 退单审核
 *
 * @author: 周琳胜
 * @since: 2019/3/22
 * create at : 2019/3/22 14:45
 */
@Slf4j
@Component
public class ReturnOrderAuditService {


    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    @Autowired
    private OcBReturnAfSendMapper afSendMapper;

    @Autowired
    private OcBReturnAfSendItemMapper afSendItemMapper;

    @Autowired
    private IpBTaobaoRefundMapper refundMapper;

    @Autowired
    private OcBReturnBuildService ocBReturnBuildService;

    @Autowired
    private OcBReturnOrderExchangeMapper ocBReturnOrderExchangeMapper;

    @Autowired
    private OmsOrderStCAutocheckService omsOrderStCAutocheckService;

    @Autowired
    private ExchangeInService exchangeInService;

    @Autowired
    private RefundFormAfterDeliveryService deliveryService;

    @Autowired
    private OcBRefundOrderToSAService toSAService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Autowired
    private IpOrderCancelToAgService cancelToAgService;

    @Autowired
    private OmsOrderStCAutocheckService stCAutocheckService;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBRefundOrderStandPlatToRefundService platToRefundService;

    @Autowired
    private OcBReturnOrderNodeRecordService nodeRecordService;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;

    @Autowired
    private AcFInvoiceReturnRedOffsetService acFInvoiceReturnRedOffsetService;


    @Autowired
    private OcBReturnStdNoticeService ocBReturnStdNoticeService;

    @Reference(group = "ip", version = "1.4.0")
    private QimenReturnOrderConfirmCmd qimenReturnOrderConfirmCmd;

    @Reference(group = "cp-ext", version = "1.0")
    private CpShopQueryCmd cpShopQueryCmd;

    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCSkuThirdItemQueryCmd psSkuThirdItemQueryCmd;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private HubRpcService hubRpcService;

    @Autowired
    private StandplatOrderTransferUtil standplatOrderTransferUtil;


    //第三方日志单据类型(退换货单)
    private static final Long THIRD_LOG_BILL_TYPE_201 = 201L;

    /**
     * 批量退单审核
     *
     * @param obj
     * @param user
     * @return
     */
    public ValueHolderV14 returnOrderBatchAudit(JSONObject obj, User user) {
        String ids = obj.getString("ID");
        AssertUtil.notNull(obj, " 参数为空");
        AssertUtil.notNull(ids, " 参数ID为空");
        String[] idArray = ids.split(",");
        AssertUtil.assertException(idArray.length < 1, "参数退单编号数量至少一条");

        int failCount = 0;
        int successCount = 0;
        ValueHolderV14 vh = new ValueHolderV14();
        List<JSONObject> failReason = new ArrayList<>();
        for (int i = 0; i < idArray.length; i++) {
            Long returnOrderId = Long.valueOf(idArray[i]);
            ValueHolderV14 tmpResult = returnOrderAudit(returnOrderId, false, user);
            if (!tmpResult.isOK()) {
                failCount++;
                JSONObject jsn = new JSONObject();
                jsn.put("objid", returnOrderId);
                jsn.put("message", tmpResult.getMessage());
                failReason.add(jsn);
                continue;
            }
            successCount++;
        }
        if (failCount > 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("审核完成, 成功: " + successCount + "条, 失败: " + failCount + "条");
        } else {
            vh.setCode(ResultCode.SUCCESS);
            String msg = "审核完成, 成功: " + successCount + "条";
            if (successCount == 1 && successCount == idArray.length) {
                msg = "审核成功";
            }
            vh.setMessage(msg);
        }
        vh.setData(failReason);
        return vh;
    }

    /**
     * 退单审核
     * 提取.整理逻辑,暂不做逻辑,业务变动
     *
     * @param isAuto 自动审核
     * @param user   用户
     * @return 结果
     */
    public ValueHolderV14 returnOrderAudit(Long returnId, boolean isAuto, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        if (returnId == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("待审核退换货单编号为空");
            return vh;
        }
        //redis锁单
        String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(returnId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                throw new NDSException("锁单失败");
            }
            // 1. 基础校验
            OcBReturnOrder returnOrder = validateLogicData(returnId);
            //是否错收,如果为是,并且是自动审核，则直接返回失败
            Integer isWrongReceive = returnOrder.getIsWrongReceive();
            if (IsWrongReceive.YES.val().equals(isWrongReceive) && isAuto) {
                return ValueHolderV14Utils.getFailValueHolder("错收类型不能进行自动审核！");
            }
            boolean isReissue = OcReturnBillTypeEnum.REISSUE.getVal().equals(returnOrder.getBillType());
            if (OcReturnBillTypeEnum.RETURN.getVal().equals(returnOrder.getBillType()) || isReissue) {
                // 2. 退货单
                return returnTypeHandle(returnOrder, isAuto, user);
            } else if (OcReturnBillTypeEnum.EXCHANGE.getVal().equals(returnOrder.getBillType())) {
                // 3. 退换货单
                return exchangeBranchHandle(returnOrder, isAuto, user);
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("退单状态异常，请检查后重试!");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("退换货单审核失败:{}",returnId), Throwables.getStackTraceAsString(ex));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(ex.getMessage());
        } finally {
            redisLock.unlock();
        }
        return vh;
    }

    /**
     * 基础数据校验
     *
     * @param ocBReturnOrderId 退单编号
     * @return 退单
     */
    private OcBReturnOrder validateLogicData(Long ocBReturnOrderId) {

        // 退换货订单
        OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectById(ocBReturnOrderId);
        AssertUtil.notNull(returnOrder, "数据异常：未查到退换货单!");

        // 退单平台类型
        Integer platform = returnOrder.getPlatform();
        AssertUtil.notNull(platform, "数据异常：平台类型为空!");

        // 退款金额
        //    BigDecimal returnAmtList = returnOrder.getReturnAmtList();
        //  AssertUtil.notNull(returnAmtList, "数据异常：商品应退款金额为空!");

        // 虚拟入库状态: (非 虚拟入库未入库, 且 退货状态: 待入库) 不能进行审核
        int proReturnStatus = returnOrder.getProReturnStatus() == null ? 0 : returnOrder.getProReturnStatus();
        boolean waitReturnIn = ReturnAfSendInStatusEnum.PENDINGSTORAGE.getVal() == proReturnStatus;
        boolean neVirtualInNot = !VirtualInStatusEnum.NOT.integer().equals(returnOrder.getInventedStatus());
        AssertUtil.assertException(neVirtualInNot && waitReturnIn, "非虚拟入库未入库,待入库状态不能进行审核");

        boolean notEnsure = !ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal().equals(returnOrder.getReturnStatus());
        AssertUtil.assertException(notEnsure, "非等待售后确认状态,不允许审核");
        return returnOrder;
    }

    /**
     * 退货类型.流程
     *
     * @param user        操作用户
     * @param isAuto      自动审核
     * @param returnOrder 退货单
     * @return Vh
     */
    private ValueHolderV14 returnTypeHandle(OcBReturnOrder returnOrder, boolean isAuto, User user) throws Exception {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ReturnOrderAuditService returnTypeHandle :{}", returnOrder.getId()), JSONObject.toJSONString(returnOrder));
        }

        ValueHolderV14 vh = new ValueHolderV14();

        Integer platmform = standplatOrderTransferUtil.getReturnOrderPlatmform(returnOrder);

        // 先更新审核状态，再调接口，接口如果失败，后续做补偿
        updateReturnOrderStatus(returnOrder.getId(), user);
        recordReturnOrderLog(returnOrder.getId(), "退货单审核", "退换货单审核完成", isAuto, user);

        //审核完成后保存退单待红冲发票信息
        //List<OcBReturnOrderRefund> refunds = ocBReturnOrderRefundMapper.selectByOcOrderId(returnOrder.getId());
        //ReturnRedOffsetDTO dto = omsRefundOrderService.bulidReturnRedOffsetDTO(returnOrder,refunds);
        //acFInvoiceReturnRedOffsetService.createRedOffserRecord(dto);


        // 手工订单不调用平台接口
        if(!isAuto && StringUtils.isBlank(returnOrder.getReturnId())){
            updatePlatformRefundStatus(returnOrder.getId(), OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_SUCCESS);
            recordReturnOrderLog(returnOrder.getId(), "退换货单审核", "手工单直接标记平台退款成功", isAuto, user);
            return ValueHolderV14Utils.getSuccessValueHolder("退换货单审核完成！");
        }

        //是否错收
        Integer isWrongReceive = returnOrder.getIsWrongReceive();
        //是否错收,如果为是,并且是手动审核，则直接返回成功
        if (IsWrongReceive.YES.val().equals(isWrongReceive) && !isAuto) {
            updatePlatformRefundStatus(returnOrder.getId(),OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_SUCCESS);
            recordReturnOrderLog(returnOrder.getId(), "退换货单审核", "错收单直接标记平台退款成功", isAuto, user);
            return ValueHolderV14Utils.getSuccessValueHolder("错收类型退换货单审核完成！");
        }

        // 2.1 判断退单平台类型是否为淘
        if (PlatFormEnum.TAOBAO.getCode().equals(platmform)) {
            invokeAfterSendAudit(user, isAuto, returnOrder, "退换货单审核");
        } else if (PlatFormEnum.JINGDONG.getCode().equals(platmform)) {
            invokeJinDongSA(user, isAuto, returnOrder, "退换货单审核");
        }else {
            //通用退款
            invokeStandPlatAutoRefund(user, isAuto, returnOrder,platmform, "退换货单审核");
        }
        // 3. 调用已发货审核服务
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("退换货单审核完成！");
        return vh;
    }

    public ValueHolderV14 invokeWangDianTongSA(User user, boolean isAuto, OcBReturnOrder returnOrder, String type) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ReturnOrderAuditService invokeWangDianTongSA :{}", returnOrder.getId()), JSONObject.toJSONString(returnOrder));
        }

        try {
            List<OcBReturnOrderRefund> ocBReturnOrderRefunds = ocBReturnOrderRefundMapper.selectByOcOrderId(returnOrder.getId());
            List<String> skuEcodeList = ocBReturnOrderRefunds.stream().map(OcBReturnOrderRefund::getPsCSkuEcode).distinct().collect(Collectors.toList());

            CpShop shop = queryShop(returnOrder);
            Map<String, List<PsCSkuThirdItemResult>> skuAndModeMap = querySkuThirdItem(skuEcodeList);

            ReturnOrderConfirmModel returnOrderConfirmModel = new ReturnOrderConfirmModel();
            List<ReturnOrderConfirmItemModel> returnOrderConfirmItemModels = new ArrayList<>();

            CpCLogisticsItem cpLogisticsItem = cpRpcService.selectCpCLogisticsEcode(Long.valueOf(returnOrder.getReserveVarchar05()), returnOrder.getCpCLogisticsId());
            if (cpLogisticsItem == null) {
                throw new NDSException("未查到对应平台物流信息");
            }
            //customerId
            returnOrderConfirmModel.setCustomerId(shop.getCustomerId());
            //ERP的退货入库单据编码
            returnOrderConfirmModel.setReturnOrderCode(returnOrder.getReturnId());
            //仓库编码
            returnOrderConfirmModel.setWarehouseCode(shop.getWarehouseCode());
            //姓名
            returnOrderConfirmModel.setName(returnOrder.getReceiveName());
            //移动电话
            returnOrderConfirmModel.setMobile(returnOrder.getReceiveMobile());
            //省份
            returnOrderConfirmModel.setProvince(returnOrder.getReceiverProvinceName());
            //城市
            returnOrderConfirmModel.setCity(returnOrder.getReceiverCityName());
            //详细地址
            returnOrderConfirmModel.setDetailAddress(returnOrder.getReceiveAddress());
            returnOrderConfirmModel.setExpressCode(returnOrder.getLogisticsCode());

            returnOrderConfirmModel.setLogisticsCode(cpLogisticsItem.getCpCLogisticsEcode());
            returnOrderConfirmModel.setLogisticsName(cpLogisticsItem.getCpCLogisticsEname());

            //备注
            if (returnOrder.getIsWrongReceive() != null) {
                if (returnOrder.getIsWrongReceive() == 1) {
                    returnOrderConfirmModel.setRemark("是");
                }else {
                    returnOrderConfirmModel.setRemark("否");
                }
            }


            for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefunds) {
                ReturnOrderConfirmItemModel returnOrderConfirmItemModel = new ReturnOrderConfirmItemModel();
                //货主编码
                returnOrderConfirmItemModel.setOwnerCode(shop.getOwnerCode());
                //商品编码
                List<PsCSkuThirdItemResult> psCSkuThirdItemResults = skuAndModeMap.get(ocBReturnOrderRefund.getPsCSkuEcode());
                PsCSkuThirdItemResult psCSkuThirdItemResult = psCSkuThirdItemResults.get(0);
                returnOrderConfirmItemModel.setItemCode(psCSkuThirdItemResult.getDucode());
                //仓储系统商品编码
                returnOrderConfirmItemModel.setItemId(ocBReturnOrderRefund.getPsCSkuEcode());
                //应收商品数量
                returnOrderConfirmItemModel.setPlanQty(
                        new BigDecimal(ocBReturnOrderRefund.getQtyRefund().stripTrailingZeros().toPlainString()));
                //实收商品数量
                returnOrderConfirmItemModel.setActualQty(
                        new BigDecimal(ocBReturnOrderRefund.getQtyIn().stripTrailingZeros().toPlainString()));
                returnOrderConfirmItemModels.add(returnOrderConfirmItemModel);
            }

            returnOrderConfirmModel.setItems(returnOrderConfirmItemModels);
            returnOrderConfirmModel.setPlatform(Integer.parseInt(QiMenSdkConstant.WANG_DIAN_TONG));
            returnOrderConfirmModel.setMethod(QiMenSdkConstant.QM_RETURN_ORDER_CONFIRM);
            ValueHolderV14 valueHolderV14 = qimenReturnOrderConfirmCmd.returnOrderConfirm(returnOrderConfirmModel);

            return valueHolderV14;
            /*if (valueHolderV14.isOK()) {
                recordReturnOrderLog(returnOrder.getId(), "退货单审核", "调用SAP成功", isAuto, user);
            } else {
                recordReturnOrderLog(returnOrder.getId(), "退货单审核", "调用SAP返回失败:" + valueHolderV14.getMessage(), isAuto, user);
            }*/

        } catch (Exception e) {
            recordReturnOrderLog(returnOrder.getId(), type, "旺店通平台退款服务调用异常:" + e.getMessage(), isAuto, user);
            log.error(" 退单审核: 调用旺店通平台退款接口异常！失败原因: {}", e.getMessage(), e);
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }

    private Map<String, List<PsCSkuThirdItemResult>> querySkuThirdItem(List<String> skuEcodeList) {
        ValueHolderV14<List<PsCSkuThirdItemResult>> thirdItemsHolder =
                psSkuThirdItemQueryCmd.queryThirdItemsInfoBySkuEcode(skuEcodeList);

        if (!thirdItemsHolder.isOK()) {
            throw new NDSException(thirdItemsHolder.getMessage());
        }

        List<PsCSkuThirdItemResult> thirdItems = thirdItemsHolder.getData();
        if (CollectionUtils.isEmpty(thirdItems)) {
            throw new NDSException("第三方条码匹配失败");
        }

        return thirdItems.stream().collect(Collectors.groupingBy(PsCSkuThirdItemResult::getPsCSkuEcode));
    }

    private CpShop queryShop(OcBReturnOrder returnOrder) {
        List<Long> shopIdList = new ArrayList<>();
        shopIdList.add(returnOrder.getCpCShopId());
        List<CpShop> cpShops = cpShopQueryCmd.queryShopByIds(shopIdList);
        if (CollectionUtils.isEmpty(cpShops)) {
            throw new NDSException("未查到对应店铺信息");
        }
        return cpShops.get(0);
    }

    /**
     * 通用平台自动退款
     *
     * @param user  操作用户
     * @param order 退换货单
     */
    public ValueHolderV14 invokeStandPlatAutoRefund(User user, boolean isAuto, OcBReturnOrder order, Integer platform,String type) {
        try {
            //判断是否开启自动退款策略
            boolean isToAg = omsOrderStCAutocheckService.isToAgByShopStrategy(order.getCpCShopId());
            // 是否是拼多多
            boolean isPinduoduo = PlatFormEnum.PINDUODUO.getCode().equals(platform);
            //是否SAP销售退货
            String businessTypeCode = order.getBusinessTypeCode();
            boolean isSapReturn = (PlatFormEnum.SAP.getCode().equals(platform) || PlatFormEnum.DMS.getCode().equals(platform)) && OcOmsReturnOrderConstant.BUSINESS_TYPE_CODE_RYTH11.equals(businessTypeCode);
            if (!isToAg && !isPinduoduo && !isSapReturn) {
                if (log.isDebugEnabled()) {
                    log.debug("invokeStandPlatAutoRefund.店铺未开启自动退款策略.returnOrderId#{}, stShopId#{}", order.getId(),
                            order.getCpCShopId());
                }
                recordReturnOrderLog(order.getId(), type, "店铺[" + order.getCpCShopId() + "]未开启自动退款策略", isAuto, user);
                //失败次数
                updateFail(order);
                return ValueHolderV14Utils.getFailValueHolder("通用平台退款服务调用失败:" + "店铺[" + order.getCpCShopId() + "]未开启自动退款策略");
            }
            boolean isReissue = OcReturnBillTypeEnum.REISSUE.getVal().equals(order.getBillType());
            if (isReissue){
                return ValueHolderV14Utils.getSuccessValueHolder("补寄类型退货单不调用平台退款服务");
            }
            List<OcBReturnOrderRefund> refunds = ocBReturnOrderRefundMapper.selectByOcOrderId(order.getId());
            ValueHolderV14 vh = ValueHolderV14Utils.getFailValueHolder("同步失败！");
            if(PlatFormEnum.STD_XXY.getCode().equals(platform)){
                //私域
                vh = invokeStdSA(order);
            }else if(PlatFormEnum.WANG_DIAN_TONG.getCode().equals(platform)){
                vh = invokeWangDianTongSA(user, isAuto, order, "退换货单审核");
            }else if (PlatFormEnum.SAP.getCode().equals(platform)) {
                if (isSapReturn) {
                    vh = invokeSapAg(order, refunds);
                } else {
                    JSONObject object = new JSONObject();
                    object.put("message", "无需调用SAP");
                    return ValueHolderV14Utils.custom(0, "无需调用SAP", object);
                }
            } else if (PlatFormEnum.DMS.getCode().equals(platform)) {
                if (isSapReturn) {
                    vh = invokeDmsAg(order, refunds);
                } else {
                    JSONObject object = new JSONObject();
                    object.put("message", "无需调用DMS");
                    return ValueHolderV14Utils.custom(0, "无需调用DMS", object);
                }
            } else if (PlatFormEnum.MEITUAN_FLASH_SALES.getCode().equals(platform)
                    || PlatFormEnum.HAOSHIQI.getCode().equals(platform)
                    || PlatFormEnum.SHIPH.getCode().equals(platform)) {
                vh = invokeMeituanFlashSalesAg(order);
            } else {
                JSONObject object = new JSONObject();
                object.put("ID", order.getId());
                String param = object.toString();
                vh = platToRefundService.refundOrderToRedund(param, user);
            }
            if (log.isDebugEnabled()) {
                log.debug("OcBRefundOrderStandPlatToRefundService.refundOrderToRedund.vh:{}", JSON.toJSONString(vh));
            }
            if (vh != null && vh.isOK()) {
                updatePlatformRefundStatus(order.getId(), OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_SUCCESS);
                recordReturnOrderLog(order.getId(), type, "通用平台退款服务调用成功", isAuto, user);
                JSONObject object = new JSONObject();
                object.put("message", "通用平台退款服务调用成功");
                return ValueHolderV14Utils.custom(0, "通用平台退款服务调用成功", object);
            } else {
                //失败次数
                updateFail(order);
                updatePlatformRefundStatus(order.getId(),OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_FAIL);
                //updateReturnOrderStatusToConfirm(order.getId(), user);
                recordReturnOrderLog(order.getId(), type, "通用平台退款服务调用失败:" + vh.getMessage(), isAuto, user);
                log.error(" 退单审核: 通用平台退款接口失败！退单编号: {}", order.getId());
                return ValueHolderV14Utils.getFailValueHolder("通用平台退款服务调用失败:" + vh.getMessage());
            }
        } catch (Exception e) {
            updateReturnOrderStatusToConfirm(order.getId(), user);
            recordReturnOrderLog(order.getId(), type, "通用平台退款服务调用异常:" + e.getMessage(), isAuto, user);
            log.error(" 退单审核: 调用通用平台退款接口异常！退单编号: {}", order.getId());
            //失败次数
            updateFail(order);
            return ValueHolderV14Utils.getFailValueHolder("通用平台退款服务调用失败:" + e.getMessage());
        }
    }

    /**
     * 更新退换货单状态
     *
     * @param id   退换货单编号
     * @param user 操作用户 SG_B_CHANNEL_PRODUCT
     * @return true : 更新成功
     */
    public boolean updateReturnOrderStatusToConfirm(Long id, User user) {
        OcBReturnOrder newReturnOrder = new OcBReturnOrder();
        newReturnOrder.setId(id);
        newReturnOrder.setReturnStatus(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
        newReturnOrder.setAuditTime(new Date());
        newReturnOrder.setCheckerName(user.getName());
        newReturnOrder.setCheckerEname(user.getEname());
        newReturnOrder.setCheckerId(user.getId().longValue());
        newReturnOrder.setToSettleStatus(ToACStatusEnum.PENDING.getLongValue());
        int updateResult = ocBReturnOrderMapper.updateById(newReturnOrder);
        return updateResult > 0;
    }

    /**
     * 修改失败次数
     *
     * @param ocBReturnOrder
     * @return void
     * @Date 2021/9/10 11:22
     */
    private void updateFail(OcBReturnOrder ocBReturnOrder) {
        Integer auditFail = Objects.nonNull(ocBReturnOrder.getAuditFail()) ? ocBReturnOrder.getAuditFail() + 1 : 1;
        ocBReturnOrderMapper.updateAuditFail(ocBReturnOrder.getId(), auditFail);
    }

    /**
     * 京东Sa
     *
     * @param user  操作用户
     * @param order 退换货单
     */
    public void invokeJinDongSA(User user, boolean isAuto, OcBReturnOrder order, String type) {

        try {
            boolean isToAg = omsOrderStCAutocheckService.isToAgByShopStrategy(order.getCpCShopId());
            if (!isToAg) {
                if (log.isDebugEnabled()) {
                    log.debug("invokeJinDongSA.店铺未开启京东sa策略.returnOrderId#{}, stShopId#{}", order.getId(),
                            order.getCpCShopId());
                }
                return;
            }
            JSONObject object = new JSONObject();
            object.put("ID", order.getId());
            String param = object.toString();
            ValueHolderV14 vh = toSAService.refundOrderToSa(param, user);
            log.debug(" invokeJinDongSA.店铺未开启京东sa策略.vh {}", JSON.toJSONString(vh));
            if (vh != null && vh.isOK()) {
                recordReturnOrderLog(order.getId(), type, "京东拆包登记服务调用成功", isAuto, user);
            } else {
                recordReturnOrderLog(order.getId(), type, "京东拆包登记服务调用失败:" + vh.getMessage(), isAuto, user);
                log.error(" 退单审核: 调用拆包登记接口失败！退单编号: {}", order.getId());
            }

        } catch (Exception e) {
            recordReturnOrderLog(order.getId(), type, "京东拆包登记服务调用异常", isAuto, user);
            log.error(LogUtil.format("调用拆包登记接口异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 释放hold单
     * 审核,强制完成, 找不到原单不释放;  如果找到必须释放成功
     *
     * @param rtn 退换货单编号
     */
    public String releaseHoldOrder(OcBReturnOrder rtn, boolean isForce, User user) {

        List<Long> exchangeOrderIds = ES4Order.getIdsByOrigReturnOrderId(rtn.getId());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(exchangeOrderIds)) {
            return ", 未查询到关联换货订单, 不做释放操作";
        }
        OrderHoldReasonEnum holdEnum = OrderHoldReasonEnum.RETURN_AUDIT_COMPLETE;
        if (isForce) {
            holdEnum = OrderHoldReasonEnum.RETURN_FORCE_COMPLETE;
        }
        StringBuilder sb = new StringBuilder("; 释放换货订单编号: ");
        for (Long id : exchangeOrderIds) {
            OcBOrder tmpOrder = new OcBOrder();
            tmpOrder.setId(id);
            tmpOrder.setIsInterecept(OmsOrderIsInterceptEnum.NO.getVal());
            try {
                ValueHolder r = ocBOrderHoldService.holdOrUnHoldOrder(tmpOrder, holdEnum);
                AssertUtil.assertException(!r.isOK(), "释放Hold单失败, 换货订单编号: " + id);
                sb.append(id).append(" ");
            } catch (Exception ex) {
                String errorMsg = ExceptionUtil.getMessage(ex);
                log.error(LogUtil.format("退换货单审核, 释放hold单异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
                errorMsg = errorMsg.length() > 1000 ? errorMsg.substring(0, 1000) : errorMsg;
                omsOrderLogService.addUserOrderLog(id, "", OrderLogTypeEnum.RETURN_COMPELETED.getKey(),
                        "退换货单审核,释放Hold单异常", null, errorMsg, user);
                AssertUtil.assertException(true, errorMsg);
            }
        }
        return sb.toString();
    }

    /**
     * 生成换货订单
     *
     * @param returnOrder 退换货单
     * @param isAuto      自动?
     * @param user        操作用户
     * @return 生成审核结果
     */
    private ValueHolderV14 generateExchangeOrder(OcBReturnOrder returnOrder, boolean isAuto, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        Long returnOrderId = returnOrder.getId();
        //勾选了换货预留库存参数，调用生成换货订单服务
        IpOrderReturnRelation ocBOrderRelation = buildExchangeOrderParam(returnOrder);
        try {
            ocBOrderRelation.getOcBOrder().setOrderSource(OmsOrderSource.MANUAL_ADD.getEcode());
            ValueHolderV14 result = ocBReturnBuildService.buildExchange(ocBOrderRelation, user);
            if (result.getCode() == ResultCode.SUCCESS) {
                // 20190813修改 增加是否是换货未入库字段
                //    Long esOcbOrderId = ES4Order.getIdBySourceCode(returnOrder.getTid());
                // 个人认为没有必要再次查询,  更新可融入到新增流程
                Long esOcbOrderId = 0L;
                List<Long> idListBySourceCode = GSI4Order.getIdListBySourceCode(returnOrder.getTid());
                if (CollectionUtils.isNotEmpty(idListBySourceCode)) {
                    esOcbOrderId = idListBySourceCode.get(0);
                }

                OcBOrder ocBOrder = new OcBOrder();
                ocBOrder.setIsExchangeNoIn(0L);
                ocBOrder.setId(esOcbOrderId);
                ocBOrderMapper.updateById(ocBOrder);
                // 更新退货单状态为完成
                updateReturnOrderStatus(returnOrderId, user);
                //  添加退换货订单操作日志
                recordReturnOrderLog(returnOrderId, "退换货单审核", "退换货单审核完成", isAuto, user);
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("退换货单审核成功！");
                return vh;
            } else {
                //  添加退换货订单操作日志
                String message = "ID为：" + returnOrderId + "退换货单审核失败,原因:" + result.getMessage();
                recordReturnOrderLog(returnOrderId, "退换货单审核", message, isAuto, user);
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("生成换货订单异常，退单审核失败！" + result.getMessage());
                return vh;
            }
        } catch (Exception e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("调用生成换货订单服务异常:" + e.getMessage());
            log.error(LogUtil.format("调用生成换货订单服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));

            return vh;
        }
    }


    /**
     * 构建换货订单
     *
     * @param returnOrder 退换货单
     * @return IpOrderReturnRelation
     */
    public IpOrderReturnRelation buildExchangeOrderParam(OcBReturnOrder returnOrder) {
        Long returnOrderId = returnOrder.getId();
        IpOrderReturnRelation ocBOrderRelation = new IpOrderReturnRelation();
        OcBOrder newOrder = new OcBOrder();
        List<OcBOrderItem> list = new ArrayList<>();
        //生成订单主表对象
        newOrder.setSourceCode(String.valueOf(returnOrder.getTbDisputeId()));
        newOrder.setCpCShopId(returnOrder.getCpCShopId());
        newOrder.setCpCShopTitle(returnOrder.getCpCShopTitle());
        newOrder.setUserId(0L);
        newOrder.setUserNick(returnOrder.getBuyerNick());
        newOrder.setProductAmt(returnOrder.getExchangeAmt());
        newOrder.setAmtReceive(returnOrder.getExchangeAmt());
        newOrder.setOrderAmt(returnOrder.getExchangeAmt());
        newOrder.setReceivedAmt(returnOrder.getExchangeAmt());
        newOrder.setJdReceiveAmt(returnOrder.getExchangeAmt());
        newOrder.setReceiverName(returnOrder.getReceiveName());
        newOrder.setReceiverMobile(returnOrder.getReceiveMobile());
        newOrder.setReceiverPhone(returnOrder.getReceivePhone());
        newOrder.setCpCRegionProvinceId(returnOrder.getReceiverProvinceId());
        newOrder.setCpCRegionCityId(returnOrder.getReceiverCityId());
        newOrder.setCpCRegionAreaId(returnOrder.getReceiverAreaId());
        newOrder.setReceiverAddress(returnOrder.getReceiveAddress());
        newOrder.setCpCRegionTownEname(returnOrder.getCpCRegionTownEname());
        newOrder.setOrderSource(returnOrder.getOrdeSource());
        newOrder.setOrigOrderId(returnOrder.getOrigOrderId());
        newOrder.setOrigReturnOrderId(returnOrderId);
        newOrder.setSellerMemo(returnOrder.getRemark());
        newOrder.setSysremark("由退换货单" + returnOrderId + "，生成新订单");
        newOrder.setPlatform(returnOrder.getPlatform());
        newOrder.setTid(returnOrder.getTid());
        // 先查出换货订单明细  再塞进订单明细
        QueryWrapper<OcBReturnOrderExchange> exchangewrapper = new QueryWrapper<>();
        exchangewrapper.eq("oc_b_return_order_id", returnOrderId);
        List<OcBReturnOrderExchange> returnOrderExchanges = ocBReturnOrderExchangeMapper.selectList(exchangewrapper);
        for (OcBReturnOrderExchange returnOrderExchange : returnOrderExchanges) {
            OcBOrderItem ocBOrderItem = new OcBOrderItem();
            ocBOrderItem.setOrderSplitAmt(BigDecimal.ZERO);
            // 条码档案中的库位没有 store_site
            ocBOrderItem.setBarcode(returnOrderExchange.getBarcode());
            ocBOrderItem.setPsCProId(returnOrderExchange.getPsCProId());
            ocBOrderItem.setPsCProEcode(returnOrderExchange.getPsCProEcode());
            ocBOrderItem.setPsCProEname(returnOrderExchange.getPsCProEname());
            ocBOrderItem.setSkuSpec(returnOrderExchange.getSkuSpec());
            //liqb 更改ooid类型从Long类型改成String类型
            ocBOrderItem.setOoid(String.valueOf(returnOrderExchange.getOid()));
            ocBOrderItem.setPsCSkuId(returnOrderExchange.getPsCSkuId());
            ocBOrderItem.setPsCSkuEcode(returnOrderExchange.getPsCSkuEcode());
            // ocBOrderItem.setPriceList(returnOrderExchange.getPrice());
            ocBOrderItem.setPriceList(returnOrderExchange.getPriceList());// 吊牌价
            /*ocBOrderItem.setPrice(returnOrderExchange.getAmtRefund().divide(returnOrderExchange.getQtyExchange(),
                    4, BigDecimal.ROUND_HALF_UP));*/
            ocBOrderItem.setPrice(returnOrderExchange.getPrice());// 平台售价
            ocBOrderItem.setAmtDiscount(BigDecimal.ZERO);
            /*ocBOrderItem.setAdjustAmt(returnOrderExchange.getPrice().multiply(returnOrderExchange.getQtyExchange()));*/
            // 调整金额 = 成交金额 - 平台售价*数量
            ocBOrderItem.setAdjustAmt(returnOrderExchange.getAmtRefund().subtract(ocBOrderItem.getPrice().multiply(returnOrderExchange.getQtyExchange())));
            ocBOrderItem.setRealAmt(returnOrderExchange.getAmtRefund());
            ocBOrderItem.setIsAllocatestock(0);
            ocBOrderItem.setIsBuyerRate(0);
            ocBOrderItem.setQtyRefund(BigDecimal.ZERO);
            ocBOrderItem.setQty(returnOrderExchange.getQtyExchange());
            ocBOrderItem.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
            ocBOrderItem.setTid(returnOrder.getTid());
            ocBOrderItem.setIsPresalesku(0);
            ocBOrderItem.setIsSendout(0);
            ocBOrderItem.setIsactive(IsActiveEnum.Y.getKey());
            convertProductType(ocBOrderItem);
            list.add(ocBOrderItem);
        }
        //调用生成换货订单服务
        ocBOrderRelation.setOcBOrder(newOrder);
        ocBOrderRelation.setOcBOrderItems(list);
        ocBOrderRelation.setOcBReturnOrder(returnOrder);
        ocBOrderRelation.setForbidHoldExchangeOrder(true);
        return ocBOrderRelation;
    }

    /**
     * @param ocBOrderItem 订单明细商品类型处理
     */
    private void convertProductType(OcBOrderItem ocBOrderItem) {
        ProductSku productSku = psRpcService.selectProductSku(ocBOrderItem.getPsCSkuEcode());
        if (productSku == null) {
            throw new NDSException("条码: " + ocBOrderItem.getPsCSkuEcode() + ", 未查询到对应商品信息");
        }
        int skuType = productSku.getSkuType();
        Long proType;
        if (skuType == 1 || skuType == 2) {
            proType = 4L;
        } else {
            proType = Long.valueOf(skuType);
        }
        ocBOrderItem.setProType(proType);
    }

    /**
     * 调用AG
     * 记录相应日志
     *
     * @param user        操作用户
     * @param returnOrder 退单
     */
    public void invokeAfterSendAudit(User user, boolean isAuto, OcBReturnOrder returnOrder, String type) {
        OcBReturnAfSend refund = null;
        try {
            AssertUtil.assertException(StringUtils.isBlank(returnOrder.getReturnId()), "平台退款单号为空, 不调用AG");
            List<OcBReturnAfSend> refunds = afSendMapper.listReturnAfSend4ReturnAudit(returnOrder.getReturnId());
            AssertUtil.assertException(refunds == null, "查询发货后退款单异常, 已发货退款单为null");
            AssertUtil.assertException(refunds.size() > 1, "查询发货后退款单异常, 已发货退款单查询有效结果大于1条");
            refund = refunds.get(0);
            ReturnOrderAuditService bean = ApplicationContextHandle.getBean(ReturnOrderAuditService.class);
            bean.invokeAg(returnOrder, refund, isAuto, user, type);
        } catch (Exception e) {
            String msg = "调用AG流程异常";
            if (e != null && e.getMessage() != null) {
                msg = e.getMessage().length() > 200 ? e.getMessage().substring(0, 200) : e.getMessage();
                if (msg.startsWith("AG") && refund != null) {
                    updateRefundAgStatus(refund, AGStatusEnum.FAIL.getVal().toString(), user);
                }
            }
            log.error(LogUtil.format("退换货单审核,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            recordReturnOrderLog(returnOrder.getId(), type, "调用AG失败：" + msg, isAuto, user);
        }
    }

    /**
     * 调用Ag
     *
     * @param order
     * @param refund 退换货单
     * @param isAuto 自动
     * @param user   操作用户
     */
    @Transactional
    public void invokeAg(OcBReturnOrder order, OcBReturnAfSend refund, boolean isAuto, User user, String type) {

        // 1. 单据类型: 仅退款 或  手动来源
        if (TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode().equals(refund.getBillType())
                || RefundOrderSourceTypeEnum.MANUAL.getValue().equals(refund.getRefundOrderSourceType())) {
            return;
        }

        // 2. 已发货退款单明细校验
        List<OcBReturnAfSendItem> items = afSendItemMapper.selectList(
                new QueryWrapper<OcBReturnAfSendItem>().eq("oc_b_return_af_send_id", refund.getId()));
        AssertUtil.assertException(CollectionUtils.isEmpty(items), "发货后退款单明细不能为空, 不调用AG");

        // 3. 校验: 排除特殊明细, 存在未入库退单 则不允许调用ag入仓
        boolean isUnPass = deliveryService.exclusionDetails(items);
        AssertUtil.assertException(isUnPass, "退款单排除特殊明细[拒收/完成/拦截且拦截成功/全部入库/部分入库,排除赠品], 存在未全部入库关联退换货单, 不调用AG");

        // 4.1 淘宝订单
        boolean isNotTaoBaoOrder = !PlatFormEnum.TAOBAO.getLongVal().equals(refund.getCpCPlatformId());
        AssertUtil.assertException(isNotTaoBaoOrder, "非淘宝订单，不调用AG");

        // 4.2 店铺开启AG
        boolean isShopOpenAg = stCAutocheckService.isToAgByShopStrategy(refund.getCpCShopId());
        AssertUtil.assertException(!isShopOpenAg, "店铺未开启AG, 不调用AG");

        // 4.3 传Ag状态 已传AG（1）或者不传AG（3）
        int agStatusVal = refund.getAgStatus() == null ? 0 : Integer.valueOf(refund.getAgStatus());
        boolean agStatus = AGStatusEnum.NOT.getVal().equals(agStatusVal) || AGStatusEnum.SUCCESS.getVal().equals(agStatusVal);
        AssertUtil.assertException(agStatus, "传AG状态已传AG、不传AG, 不调用AG");

        // 4.4 平台退款单号
        AssertUtil.assertException(refund.getTReturnId() == null, "已发货退款单平台退款单号为空, 不调用AG");

        // 4.5 根据退单的平台退款单号查找淘宝退款单中间表是否有此退单，不存在，则不走AG程序，不进行处理
        IpBTaobaoRefund ipRefund = refundMapper.selectTaobaoByRefundId(refund.getTReturnId());
        AssertUtil.assertException(ipRefund == null, "淘宝退款单中间表无此退款单, 不调用AG");

        // 4.6 退单中间表的退单状态
        boolean ipStatus = TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(ipRefund.getStatus())
                || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(ipRefund.getStatus());
        AssertUtil.assertException(ipStatus, "淘定退单状态为退款关闭、卖家拒绝退款，不调用AG");

        // 5.0 更新
        if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(ipRefund.getStatus())) {
            // 当退单状态为退款成功时，则退货明细的AG状态更新为已传AG，退款单状态更新为已财审
            //  updateAGStatusAndRetuernStatus(ocBReturnAfSend, user);
        }

        // 6.0 调用ag: 当状态为：卖家同意退款,等待买家退货、等待卖家收货时，调用【AG入库接口】。
        ipStatus = TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode().equals(ipRefund.getStatus())
                || TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode().equals(ipRefund.getStatus());
        // ag入仓
        if (ipStatus) {
            updateRefundAgStatus(refund, AGStatusEnum.SUCCESS.getVal().toString(), user);
            recordReturnOrderLog(order.getId(), type, "AG入仓成功", isAuto, user);
            boolean agResult = ipRpcService.updateLogisticsWarehouse(refund.getTReturnId(), refund.getCpCShopId(), user);
            AssertUtil.assertException(!agResult, "AG入仓失败");
            return;
        }
        // ag取消发货
        ipStatus = TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(ipRefund.getStatus());
        if (ipStatus) {
            updateRefundAgStatus(refund, AGStatusEnum.SUCCESS.getVal().toString(), user);
            recordReturnOrderLog(order.getId(), type, "AG取消发货成功", isAuto, user);
            ValueHolderV14 vh = cancelToAgService.orderCancelToAgRetry(ipRefund, user);
            AssertUtil.assertException(vh == null || !vh.isOK(), "AG取消发货失败");
        }
    }

    /**
     * 更新已发货退款单传ag状态
     *
     * @param refund   已发货退款单
     * @param agStatus 传ag状态
     * @param user     操作用户
     */
    private void updateRefundAgStatus(OcBReturnAfSend refund, String agStatus, User user) {
        OcBReturnAfSend afSend = new OcBReturnAfSend();
        afSend.setId(refund.getId());
        afSend.setModifieddate(new Date());
        afSend.setModifiername(user.getName());
        afSend.setModifierename(user.getEname());
        afSend.setModifierid(user.getId() + 0L);
        afSend.setAgStatus(agStatus);
        afSend.setTReturnId(refund.getTReturnId());
        afSendMapper.updateAgStatus(afSend);
    }

    /**
     * 更新退换货单状态
     *
     * @param id   退换货单编号
     * @param user 操作用户 SG_B_CHANNEL_PRODUCT
     * @return true : 更新成功
     */
    public boolean updateReturnOrderStatus(Long id, User user) {
        OcBReturnOrder newReturnOrder = new OcBReturnOrder();
        newReturnOrder.setId(id);
        newReturnOrder.setReturnStatus(ReturnStatusEnum.COMPLETION.getVal());
        newReturnOrder.setAuditTime(new Date());
        newReturnOrder.setCheckerName(user.getName());
        newReturnOrder.setCheckerEname(user.getEname());
        newReturnOrder.setCheckerId(user.getId().longValue());
        newReturnOrder.setToSettleStatus(ToACStatusEnum.PENDING.getLongValue());
        int updateResult = ocBReturnOrderMapper.updateById(newReturnOrder);

        if (updateResult > 0) {
            //审核时间埋点
            nodeRecordService.insertByNode(ReturnOrderNodeEnum.OMS_AUDIT_TIME, newReturnOrder.getAuditTime(),
                    newReturnOrder.getId(), user);
        }

        return updateResult > 0;
    }

    /**
     * 更新退款同步平台状态
     * @param id
     * @param status
     * @return
     */
    public void updatePlatformRefundStatus(Long id,Integer status) {
        OcBReturnOrder newReturnOrder = new OcBReturnOrder();
        newReturnOrder.setId(id);
        newReturnOrder.setPlatformRefundStatus(status);
        int updateResult = ocBReturnOrderMapper.updateById(newReturnOrder);
    }
    /**
     * 添加退换货单日志
     *
     * @param user 操作用户
     * @param id   退单编号
     */
    public void insertReturnOrderLog(Long id, String type, String logMsg, User user) {
        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
        ocBReturnOrderLog.setAdClientId(user.getClientId() + 0L);
        ocBReturnOrderLog.setAdOrgId(user.getOrgId() + 0L);
        ocBReturnOrderLog.setLogType(type);
        ocBReturnOrderLog.setLogMessage(logMsg);
        ocBReturnOrderLog.setLogParam("");
        ocBReturnOrderLog.setIpAddress(user.getLastloginip());
        ocBReturnOrderLog.setUserName(user.getName());
        ocBReturnOrderLog.setOwnerename(user.getEname());
        ocBReturnOrderLog.setOcBReturnOrderId(id);
        ocBReturnOrderLog.setId(ModelUtil.getSequence("oc_b_return_order_log"));
        ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
    }

    /**
     * 批量增加日志
     *
     * @param sizeCount
     */
    public void synSetReturnOrderSendCWLog(Integer sizeCount) {
        List<OcBReturnOrder> returnOrders;
        Long start = System.currentTimeMillis();

        try {
            List<Long> orderIdList = Lists.newArrayList();
            JSONObject whereKey = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(ToACStatusEnum.PENDING.getValue());
            jsonArray.add(ToACStatusEnum.FAILED.getValue());
            whereKey.put("TO_SETTLE_STATUS", jsonArray);
            List<Long> orderIdList1 = ES4ReturnOrder.queryEsReturnOrderList(whereKey, 0, sizeCount);
            if (!CollectionUtils.isEmpty(orderIdList1)) {
                orderIdList.addAll(orderIdList1);
            }
            // 20200809 task#22262 AC支付宝对账新逻辑-注释退换货单非发送AC需要的内容2--end
            if (!CollectionUtils.isEmpty(orderIdList)) {
                QueryWrapper wrapper = new QueryWrapper();
                wrapper.in("id", orderIdList);
                returnOrders = ocBReturnOrderMapper.selectList(wrapper);
                if (!CollectionUtils.isEmpty(returnOrders)) {
                    List<Long> origOrderIds = Lists.newArrayListWithExpectedSize(returnOrders.size());
                    for (OcBReturnOrder returnOrder : returnOrders) {
                        origOrderIds.add(returnOrder.getOrigOrderId());
                    }
                    // List<AcPushAcBillRequest> requests = this.setBatchRequest4AC(returnOrders, orderIdList, origOrderIds, getRootUser());
                    // @20200809 加日志
//                    if (log.isDebugEnabled()) {
//                        log.debug("synSetReturnOrderSendCWLog.setBatchRequest.result:{}", CollectionUtils.isEmpty(requests) ? "empty.requests" : requests.size());
//                    }
//
//                    if (requests == null) {
//                        requests = new ArrayList<>();
//                    }

                    // 输入ID
                    List<Long> inputIds = returnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
                    // 输出ID
                    List<Long> outputIds = new ArrayList<>();

//                    requests.forEach(req -> {
//                        String billNo = req.getSourceBillNo();
//                        if (Objects.nonNull(billNo) && StringUtils.isNumeric(billNo)) {
//                            outputIds.add(Long.valueOf(billNo));
//                        }
//                    });

//                    if (log.isDebugEnabled()) {
//                        log.debug("synSetReturnOrderSendCWLog.setBatchRequest.result:{}", CollectionUtils.isEmpty(requests) ? "empty.requests" : requests.size());
//                        log.debug("synSetReturnOrderSendCWLog.outputIds2.size:{}", outputIds.size());
//                    }

                    // 两个相减
                    boolean lost = inputIds.removeAll(outputIds);

                    if (lost && inputIds.size() > 0) {
                        // 丢失的都是失败的
                        // @20200831 bug#20200831 AC支付宝对账这种数据不符合校验规则的直接更新成错误不再重试
                        ocBReturnOrderMapper.updateToACStatusByIds(ToACStatusEnum.ERROR.getValue(), inputIds);
                    }

//                    if (CollectionUtils.isNotEmpty(requests)) {
//                        ValueHolderV14 valueHolderV14 = AcScOrigBillUtil.batchPushAcBill(requests);
//                        if (valueHolderV14 != null && valueHolderV14.getCode() == ResultCode.SUCCESS) {
//                            // 批量更新
//                            ocBReturnOrderMapper.updateToACStatusByIds(ToACStatusEnum.SUCCESS.getValue(), outputIds);
//                            if (log.isDebugEnabled()) {
//                                log.debug("synSetReturnOrderSendCWLog.valueHolderV14.success");
//                            }
//                        } else {
//                            ocBReturnOrderMapper.updateToACStatusByIds(ToACStatusEnum.FAILED.getValue(), outputIds);
//                            if (log.isDebugEnabled()) {
//                                log.debug("synSetReturnOrderSendCWLog.valueHolderV14.fail");
//                            }
//                        }
//                    } else {
//                        ocBReturnOrderMapper.updateToACStatusByIds(ToACStatusEnum.ERROR.getValue(), outputIds);
//                        log.error("synSetReturnOrderSendCWLog.after.setBatchRequest4AC.result.empty");
//                    }
                }
            }
        } catch (Exception e) {
            // @20200809 task#22262 AC支付宝对账新逻辑-打印异常堆栈日志
            log.error(LogUtil.format("synSetReturnOrderSendCWLog,异常信息为:{}"), Throwables.getStackTraceAsString(e));

        }
    }

    /**
     * 批量增加日志
     */
    public void returnSync2Settlement(List<OcBReturnOrder> returnOrders) {
        Long start = System.currentTimeMillis();
        try {
            // 20200809 task#22262 AC支付宝对账新逻辑-注释退换货单非发送AC需要的内容2--end
            if (!CollectionUtils.isEmpty(returnOrders)) {
                // 输入ID
                List<Long> inputIds = Lists.newArrayListWithExpectedSize(returnOrders.size());
                List<Long> origOrderIds = Lists.newArrayListWithExpectedSize(returnOrders.size());
                for (OcBReturnOrder returnOrder : returnOrders) {
                    inputIds.add(returnOrder.getId());
                    origOrderIds.add(returnOrder.getOrigOrderId());
                }
                // List<AcPushAcBillRequest> requests = this.setBatchRequest4AC(returnOrders, inputIds, origOrderIds, getRootUser());
                // @20200809 加日志
//                if (log.isDebugEnabled()) {
//                    log.debug("returnSync2Settlement.setBatchRequest.result:{}", CollectionUtils.isEmpty(requests) ? "empty.requests" : requests.size());
//                }

//                if (requests == null) {
//                    requests = new ArrayList<>();
//                }
//                // 输出ID
//                List<Long> outputIds = new ArrayList<>();
//
//                requests.forEach(req -> {
//                    String billNo = req.getSourceBillNo();
//                    if (Objects.nonNull(billNo) && StringUtils.isNumeric(billNo)) {
//                        outputIds.add(Long.valueOf(billNo));
//                    }
//                });

//                if (log.isDebugEnabled()) {
//                    log.debug("returnSync2Settlement.setBatchRequest.result:{}", CollectionUtils.isEmpty(requests) ? "empty.requests" : requests.size());
//                    log.debug("returnSync2Settlement.outputIds2.size:{}", outputIds.size());
//                }
//
//                // 两个相减
//                boolean lost = inputIds.removeAll(outputIds);
//                if (log.isDebugEnabled()) {
//                    log.debug("returnSync2Settlement.lost.size:{}/{}", lost, inputIds.size());
//                }
//
//                if (lost && inputIds.size() > 0) {
//                    // 丢失的都是失败的
//                    // @20200831 bug#20200831 AC支付宝对账这种数据不符合校验规则的直接更新成错误不再重试
//                    ocBReturnOrderMapper.updateToACStatusByIds(ToACStatusEnum.ERROR.getValue(), inputIds);
//                }
//
//                if (CollectionUtils.isNotEmpty(requests)) {
//                    ValueHolderV14 valueHolderV14 = AcScOrigBillUtil.batchPushAcBill(requests);
//                    if (valueHolderV14 != null && valueHolderV14.getCode() == ResultCode.SUCCESS) {
//                        // 批量更新
//                        ocBReturnOrderMapper.updateToACStatusByIds(ToACStatusEnum.SUCCESS.getValue(), outputIds);
//                        if (log.isDebugEnabled()) {
//                            log.debug("returnSync2Settlement.valueHolderV14.success");
//                        }
//                    } else {
//                        ocBReturnOrderMapper.updateToACStatusByIds(ToACStatusEnum.FAILED.getValue(), outputIds);
//                        if (log.isDebugEnabled()) {
//                            log.debug("returnSync2Settlement.valueHolderV14.fail");
//                        }
//                    }
//                } else {
//                    log.error("synSetReturnOrderSendCWLog.after.setBatchRequest4AC.result.empty");
//                }
            }
        } catch (Exception e) {
            // @20200809 task#22262 AC支付宝对账新逻辑-打印异常堆栈日志
            log.error(LogUtil.format("returnSync2Settlement,异常信息为:{}"), Throwables.getStackTraceAsString(e));

        }
    }

    /**
     * 发送AC逻辑
     *
     * @param returnOrders
     * @param returnIds    退单ids
     * @param origOrderIds 原始订单id
     * @param user
     * @return
     */
//    public List<AcPushAcBillRequest> setBatchRequest4AC(List<OcBReturnOrder> returnOrders, List<Long> returnIds
//            , List<Long> origOrderIds, User user) {
//        List<AcPushAcBillRequest> requests = Lists.newArrayList();
//
//        if (CollectionUtils.isNotEmpty(returnOrders) && CollectionUtils.isNotEmpty(origOrderIds)) {
//            List<OcBOrder> ocBOrders = ocBOrderMapper.selectBatchIds(origOrderIds);
//            if (CollectionUtils.isEmpty(ocBOrders)) {
//                return new ArrayList<>();
//            }
//            List<OcBOrderItem> allItems = ocBOrderItemMapper.selectAllStatusOrderItemsByOrderIds(origOrderIds);
//            if (CollectionUtils.isEmpty(allItems)) {
//                return new ArrayList<>();
//            }
//            // 退单明细
//            List<OcBReturnOrderRefund> ocBReturnOrderRefunds = ocBReturnOrderRefundMapper.selectQtyInLgZeroByOcOrderIds(returnIds);
//            if (CollectionUtils.isEmpty(ocBReturnOrderRefunds)) {
//                return new ArrayList<>();
//            }
//            // map
//            Map<Long, OcBOrder> orderMap = ocBOrders.stream().collect(Collectors.toMap(OcBOrder::getId, Function.identity()));
//            Map<Long, List<OcBOrderItem>> itemsMap = allItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
//            Map<Long, List<OcBReturnOrderRefund>> refundMap = ocBReturnOrderRefunds.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getOcBReturnOrderId));
//            for (OcBReturnOrder returnOrder : returnOrders) {
//                //已推送的不再推送
//                if (null != returnOrder.getToSettleStatus() && ToACStatusEnum.SUCCESS.getLongValue().equals(returnOrder.getToSettleStatus())) {
//                    continue;
//                }
//                if (null == returnOrder.getCpCShopId()) {
//                    continue;
//                }
//                //原始订单id
//                if (log.isDebugEnabled()) {
//                    log.debug("synSetReturnOrderSendCWLog.returnOrder:{}", returnOrder.getId());
//                }
//
//                JSONObject jsonInfo = new JSONObject();
//                Long orderId = returnOrder.getId();
//
//                if (Objects.isNull(orderId)) {
//                    if (log.isDebugEnabled()) {
//                        log.debug("synSetReturnOrderSendCWLog.退单调用结算日志时，退单中没有订单id");
//                    }
//                    continue;
//                }
//                List<OcBReturnOrderRefund> orderRefunds = refundMap.get(orderId);
//                // @20200831 bug#20200831 AC支付宝对账加过滤未入库的明细
//                if (CollectionUtils.isEmpty(orderRefunds)) {
//                    log.debug("退单调用结算日志，为找到已入库的退货明细:{}", returnOrder.getId());
//                    continue;
//                }
//
//                OcBOrder ocBOrder = orderMap.get(returnOrder.getOrigOrderId());
//                if (Objects.isNull(ocBOrder)) {
//                    if (log.isDebugEnabled()) {
//                        log.debug("退单调用结算日志，反查订单未找到:{}", returnOrder.getId());
//                    }
//                    continue;
//                }
//
//                List<OcBOrderItem> items = itemsMap.get(ocBOrder.getId());
//                if (CollectionUtils.isEmpty(items)) {
//                    if (log.isDebugEnabled()) {
//                        log.debug("退单调用结算日志，反查订单明细未找到:{}", returnOrder.getId());
//                    }
//                    continue;
//                }
//                // 当退回邮费不为空,就添加至明细,且非手工单
//                if (!isNullOrZero(returnOrder.getReturnAmtShip()) && !StringUtils.contains(returnOrder.getTid(), ",")) {
//                    ocBReturnOrderRefunds.add(this.getPostFeeItemInfo(returnOrder.getReturnAmtShip(), returnOrder.getId()));
//                }
//                // @20210109 帮对账做一波处理;取tid
//                returnOrder.setOrigSourceCode(returnOrder.getTid());
//                jsonInfo.put("OC_B_RETURN_ORDER", returnOrder);
//                jsonInfo.put("OC_B_RETURN_ORDER_REFUND", ocBReturnOrderRefunds);
//                AcPushAcBillRequest acPushAcBillRequest = new AcPushAcBillRequest();
//                acPushAcBillRequest.setBill(jsonInfo);
//                acPushAcBillRequest.setSourceBillDate(returnOrder.getInTime());
//                acPushAcBillRequest.setUser(user);
//
//                // 增加内容
//                acPushAcBillRequest.setBillType(SourceBillType.RETAIL_REF);
//                acPushAcBillRequest.setSourceBillNo(returnOrder.getId().toString());
//                requests.add(acPushAcBillRequest);
//            }
//        }
//
//        return requests;
//    }

    /**
     * 返回默认的系统Root用户
     *
     * @return 默认的系统Root用户
     */
    public static User getRootUser() {
        User user = new UserImpl();
        ((UserImpl) user).setId(SystemUserResource.ROOT_USER_ID.intValue());
        ((UserImpl) user).setEname(SystemUserResource.ROOT_ENAME);
        ((UserImpl) user).setTruename(SystemUserResource.ROOT_USER_NAME);
        ((UserImpl) user).setClientId(SystemUserResource.AD_CLIENT_ID.intValue());
        ((UserImpl) user).setOrgId(SystemUserResource.AD_ORG_ID.intValue());
        ((UserImpl) user).setName(SystemUserResource.ROOT_USER_NAME);
        return user;
    }

    public Long selectOrder(Long orig_return_order_id) {
        Long id = null;

        List<Long> ids = ES4Order.getIdsByOrigReturnOrderId(orig_return_order_id);
        if (CollectionUtils.isNotEmpty(ids)) {
            id = ids.get(0);
        }
        return id;
    }

    /**
     * 是空的或者是0
     *
     * @param arg
     * @return
     */
    public boolean isNullOrZero(BigDecimal arg) {
        return arg == null || BigDecimal.ZERO.compareTo(arg) == 0;
    }

    /**
     * 获取 Apollo 上配置的邮费sku信息
     *
     * @return refund
     */
    public OcBReturnOrderRefund getPostFeeItemInfo(BigDecimal postFee, Long returnId) {
        if (postFee == null || postFee.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        // todo change arg
        String skuCode = config.getProperty("oms.order.custom.postFee.sku", "SMYF001");
        OcBReturnOrderRefund refund = new OcBReturnOrderRefund();
        refund.setAmtRefund(postFee);
        refund.setPsCSkuEcode(skuCode);
        refund.setFreight(IsActiveEnum.Y.getVal());
        // @20210115  运费处理添加默认数量
        refund.setQtyIn(BigDecimal.ONE);
        refund.setOcBReturnOrderId(returnId);
        return refund;
    }

    /**
     * 入库数量判断
     *
     * @param items 退货明细
     * @return null 通过
     */
    private String checkItemQtyIn(List<OcBReturnOrderRefund> items) {
        for (OcBReturnOrderRefund item : items) {
            if (NumUtil.nullAndNe(item.getQtyRefund(), item.getQtyIn())) {
                return "明细编号: " + item.getId() + ", 入库数量与申请退货数量不一致，不允许审核，若要换货发出则走强制完成！";
            }
        }
        return null;
    }

    /**
     * 退换货流程处理
     *
     * @param returnOrder 退换货单
     * @param user        操作用户
     * @return vh
     */
    public ValueHolderV14 exchangeBranchHandle(OcBReturnOrder returnOrder, boolean isAuto, User user) {

        QueryWrapper<OcBReturnOrderRefund> Wrapper = new QueryWrapper<>();
        Wrapper.eq("oc_b_return_order_id", returnOrder.getId());
        List<OcBReturnOrderRefund> refundItems = ocBReturnOrderRefundMapper.selectList(Wrapper);
        AssertUtil.notEmpty(refundItems, Resources.getMessage("未查询到退货明细数据!", user.getLocale()));

        // 1. 退换货明细: 申请数量与入库数量
        //如果是全部终止入库，则不校验申请数量和入库数量
        String terminationType = returnOrder.getTerminationType();
        if(!"1".equals(terminationType)){
            String errorMsg = checkItemQtyIn(refundItems);
            AssertUtil.assertException(errorMsg != null, Resources.getMessage(errorMsg, user.getLocale()));
        }
        // 2. 确认收货判断???
        String msg = taoMaoExchangeIn(returnOrder, isAuto, user);
        AssertUtil.assertException(StringUtils.isNotBlank(msg), msg);

        // 3.  判断退换货单对应的换货订单是否已经生成
        Long exchangeOrderId = this.selectOrder(returnOrder.getId());
        OcBOrder order = null;
        if (exchangeOrderId != null) {
            order = ocBOrderMapper.selectById(exchangeOrderId);
        }
        if (order != null) {
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(order.getId());
            ocBOrder.setIsExchangeNoIn(0L);
            ocBOrderMapper.updateById(ocBOrder);
        }

        ReturnOrderAuditService bean = ApplicationContextHandle.getBean(ReturnOrderAuditService.class);
        return bean.updateOrGenerateOrder(returnOrder, order, isAuto, user);

    }

    /**
     * 天猫换货确认
     *
     * @param returnOrder
     * @param user
     * @return
     */
    private String taoMaoExchangeIn(OcBReturnOrder returnOrder, boolean isAuto, User user) {

        if (returnOrder.getTbDisputeId() != null && PlatFormEnum.TAOBAO.getCode().equals(returnOrder.getPlatform())) {
            // 是否确认收货状态
            Integer receiveConfirm = returnOrder.getIsReceiveConfirm();
            boolean isUnReceive = ReturnBillReceiveConfirm.RECEIPT_NOT_CONFIRMED.getVal().equals(receiveConfirm)
                    || ReturnBillReceiveConfirm.CLOUD_COMFIRM_FAIL.getVal().equals(receiveConfirm);
            boolean isAdd = ReturnOrderIsAddEnum.IS_MANUALLY_ADD.getVal().equals(returnOrder.getIsAdd());
            // 未确认收货
            if (isUnReceive && !isAdd) {
                ValueHolderV14 vh = exchangeInService.exchangeIn(returnOrder.getId(), user);
                if (!vh.isOK()) {
                    String msg = "调用天猫换货确认服务失败: " + vh.getMessage();
                    recordReturnOrderLog(returnOrder.getId(), "退换货单审核", msg, isAuto, user);
                    return msg;
                }
            }
        }
        return null;
    }

    /**
     * 更新完成, 释放hold单
     *
     * @param returnOrder
     * @param order
     * @param user
     * @return
     */
    @Transactional
    public ValueHolderV14 updateOrGenerateOrder(OcBReturnOrder returnOrder, OcBOrder order, boolean isAuto, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        // 2. 存在
        if (order != null) {

            // 更新退换货单状态为完成
            updateReturnOrderStatus(returnOrder.getId(), user);
            // hold单释放
            String msg = releaseHoldOrder(returnOrder, false, user);
            recordReturnOrderLog(returnOrder.getId(), "退换货单审核", "释放Hold单成功" + msg, isAuto, user);
            //  添加退换货订单操作日志
            recordReturnOrderLog(returnOrder.getId(), "退换货单审核", "退换货单审核完成", isAuto, user);
        } else {

            // 3. 不存在
            if (IsReservedEnum.NO.getValue().equals(returnOrder.getIsReserved())) {
                return generateExchangeOrder(returnOrder, isAuto, user);
            } else {
                // 更新退货单状态为完成
                updateReturnOrderStatus(returnOrder.getId(), user);
                //  添加退换货订单操作日志
                recordReturnOrderLog(returnOrder.getId(), "退换货单审核", "退换货单审核完成", isAuto, user);
            }
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("退换单审核成功！");
        return vh;
    }

    /**
     * 记录退单日志
     *
     * @param returnId 退单id
     * @param type     日志类型
     * @param logMsg   日志信息
     * @param isAuto   自动审
     * @param user     操作用户
     */
    public void recordReturnOrderLog(Long returnId, String type, String logMsg, boolean isAuto, User user) {

        if(logMsg != null && logMsg.length() > 1000){
            logMsg = logMsg.substring(0,1000);
        }
        OcBReturnOrderLog logOrder = ocBReturnOrderLogMapper.queryLatestByReturnId(returnId);
        boolean isUpdate = false;
        logMsg = isAuto ? (logMsg + "[自动]") : (logMsg + "[手动]");
        if (logOrder != null) {
            String logType = logOrder.getLogType();
            String logMessage = logOrder.getLogMessage();
            boolean eqType = StringUtils.equalsIgnoreCase(type, logType);
            boolean eqMsg = StringUtils.equalsIgnoreCase(logMsg, logMessage);
            isUpdate = eqType && eqMsg;
        }
        if (isUpdate) {
            ocBReturnOrderLogMapper.updateLatestLogModifiedDate(returnId, logOrder.getId());
        } else {
            insertReturnOrderLog(returnId, type, logMsg, user);
        }
    }


    /**
     * @param order
     * @description: 私域退货入库通知平台
     * @return: void
     * <AUTHOR>
     * @date: 2021/12/30 10:46
     */
    public ValueHolderV14 invokeStdSA(OcBReturnOrder order) {
        OcBReturnStdNotice ocBReturnStdNotice = new OcBReturnStdNotice();
        ocBReturnStdNotice.setId(ModelUtil.getSequence("oc_b_return_std_notice"));
        ocBReturnStdNotice.setReturnId(order.getReturnId());
        ocBReturnStdNotice.setOcBReturnOrderId(order.getId());
        ocBReturnStdNotice.setReturnAmtActual(order.getReturnAmtActual());
        ocBReturnStdNotice.setCpCLogisticsEcode(order.getCpCLogisticsEcode());
        ocBReturnStdNotice.setCpCLogisticsEname(order.getCpCLogisticsEname());
        ocBReturnStdNotice.setLogisticsCode(order.getLogisticsCode());
        ocBReturnStdNotice.setCpCShopEcode(order.getCpCShopEcode());
        ocBReturnStdNotice.setQtyInstore(order.getQtyInstore());
        ocBReturnStdNotice.setInTime(order.getInTime());
        ocBReturnStdNotice.setNoticeStatus(0);
        ocBReturnStdNotice.setNoticeTimes(0);
        ocBReturnStdNotice.setPlatform(order.getPlatform());
        ocBReturnStdNotice.setTid(order.getTid());
        Date createTime = new Date();
        ocBReturnStdNotice.setCreateTime(createTime);
        ocBReturnStdNotice.setUpdateTime(createTime);
        boolean result = ocBReturnStdNoticeService.save(ocBReturnStdNotice);
        if (result) {
            return ValueHolderV14Utils.getSuccessValueHolder("同步成功！");
        } else {
            return ValueHolderV14Utils.getFailValueHolder("同步失败！");
        }
    }

    public ValueHolderV14 invokeDmsAg(OcBReturnOrder returnOrder, List<OcBReturnOrderRefund> refunds) throws Exception {
        DmsOrderBackRequest request = new DmsOrderBackRequest();
        request.setBillNo(returnOrder.getBillNo());
        request.setTid(returnOrder.getReturnId());
        request.setStoOutBillNo(returnOrder.getStoInNoticesNo());
        request.setExpresscode(returnOrder.getLogisticsCode());
        request.setScanTime(returnOrder.getInTime());
        List<DmsOrderBackItemRequest> itemList = new ArrayList<>();
        for (OcBReturnOrderRefund refund : refunds) {
            //入库数量为空或者0过滤掉
            if (refund.getQtyIn() == null || BigDecimal.ZERO.compareTo(refund.getQtyIn()) == 0) {
                continue;
            }
            DmsOrderBackItemRequest item = new DmsOrderBackItemRequest();
            // 判断
            if (StringUtils.isNotEmpty(refund.getOid())) {
                String ooid = refund.getOid();
                item.setLine(ooid);
            }
            CpCPhyWarehouse warehouse = cpRpcService.queryByWarehouseId(returnOrder.getCpCPhyWarehouseInId());
            if (warehouse != null) {
                item.setCpCPhyWarehouseEcode(warehouse.getCpCWarehouseEcode());
            }
            item.setRealOutNum(refund.getQtyIn() == null ? null : refund.getQtyIn().intValue());
            itemList.add(item);
        }
        request.setDetailList(itemList);

        return ipRpcService.dmsAg(request);
    }

    public ValueHolderV14 invokeSapAg(OcBReturnOrder returnOrder, List<OcBReturnOrderRefund> refunds) throws Exception {
        SapOrderBackRequest model = new SapOrderBackRequest();
        // 取值退换货单主表平台退款单号
        model.setVbeln(returnOrder.getReturnId());
        // todo 取值退换货单主表的入库通知单单号
        // model.setTraid();
        model.setLifex(returnOrder.getBillNo());
        model.setKeyId(returnOrder.getStoInNoticesNo());
        // 取值退换货单主表入库日期
        model.setBldate(DateUtil.datenoFormatter.format(returnOrder.getInTime()));
        //入库通知单号
        model.setItmKzabe(returnOrder.getStoInNoticesNo());
        // 取值退换货单主表物流公司
        model.setBolnr(returnOrder.getCpCLogisticsEname());
        // 取值退换货单主表物流单号
        model.setTraid(returnOrder.getLogisticsCode());
        //单据类型
        model.setBillType(THIRD_LOG_BILL_TYPE_201);


        List<SapOrderBackRequest.Item> items = new ArrayList<>();

        CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(returnOrder.getCpCPhyWarehouseInId());
        if (cpCPhyWarehouse == null) {
            return null;
        }
        for (OcBReturnOrderRefund refund : refunds) {
            SapOrderBackRequest.Item item = new SapOrderBackRequest.Item();
            //入库数量为空或者0过滤掉
            if (refund.getQtyIn() == null || BigDecimal.ZERO.compareTo(refund.getQtyIn()) == 0) {
                continue;
            }
            // 取值退换货单明细表子订单编号
            String oid;
            if(refund.getOid() != null){
                if(refund.getOid().contains("-")){
                    oid = refund.getOid().substring(refund.getOid().lastIndexOf("-")+1);
                }else{
                    oid = refund.getOid();
                }
                item.setPosnr(Integer.parseInt(oid));
            }
            // 取值退换货单明细表条码
            item.setMatnr(refund.getPsCSkuEcode());
            // 取值退换货单明细表对应行条码的入库数量
            item.setLfimg(refund.getQtyIn());
            // 取值退换货单主表入库实体仓库的仓库编码
            item.setLgort(cpCPhyWarehouse.getEcode());
            // 取值零售发货单明细表对应行条码的商品款号的基本计量单位
            PsCProdimItem psCProdimItem = psRpcService.selectPsCProDimItemInfo(refund.getPsCSkuEcode());
            item.setVrkme(psCProdimItem.getEcode());
            items.add(item);
        }
        model.setItem(items);

        return ipRpcService.sapAg(model);
    }

    /**
     * 发货后 - 退款同步美团闪购
     * @param returnOrder
     * @return
     */
    public ValueHolderV14 invokeMeituanFlashSalesAg(OcBReturnOrder returnOrder){
        OrderRefundReq orderReq = new OrderRefundReq();
        /**平台id*/
        orderReq.setPtId(returnOrder.getPlatform().longValue());
        /**店铺id*/
        orderReq.setShopId(returnOrder.getCpCShopId());
        /**平台单号*/
        orderReq.setBizOrderId(returnOrder.getTid());
        /**平台退款单号*/
        orderReq.setRefundSn(returnOrder.getReturnId());
        ValueHolderV14 v14 = hubRpcService.meituanFlashSalesAg(orderReq);
        return v14;
    }

    /**
     * 发货前 - 退款同步美团闪购、开心果
     * @param ipBStandplatRefund
     * @return
     */
    public ValueHolderV14 invokeBeforeSendAg(IpBStandplatRefund ipBStandplatRefund){
        OrderRefundReq orderReq = new OrderRefundReq();
        /**平台id*/
        orderReq.setPtId(ipBStandplatRefund.getCpCPlatformId());
        /**店铺id*/
        orderReq.setShopId(ipBStandplatRefund.getCpCShopId());
        /**平台单号*/
        orderReq.setBizOrderId(ipBStandplatRefund.getOrderNo());
        /**平台退款单号*/
        orderReq.setRefundSn(ipBStandplatRefund.getReturnNo());
        /**
         * 子单号
         */
        orderReq.setSubOrderId(ipBStandplatRefund.getSubOrderId());
        ValueHolderV14 v14 = hubRpcService.meituanFlashSalesAg(orderReq);
        return v14;
    }


    /**
     * 同步平台退款状态
     * @param id
     * @param isAuto
     * @param user
     * @return
     */
    public ValueHolderV14 syncPlatformRefundStatus(Long id, boolean isAuto, User user){
        OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByid(id);
        if(returnOrder == null){
            return ValueHolderV14Utils.getFailValueHolder("单据信息不存在！");
        }

        Integer returnStatus = returnOrder.getReturnStatus();
        if (returnStatus == null || !returnStatus.equals(ReturnStatusEnum.COMPLETION.getVal())) {
            return ValueHolderV14Utils.getFailValueHolder("同步通用平台状态失败，退换货状态非已完成！");
        }

        Integer platformRefundStatus = returnOrder.getPlatformRefundStatus();
        if(!OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_FAIL.equals(platformRefundStatus)){
            return ValueHolderV14Utils.getFailValueHolder("同步通用平台状态失败，同步状态非失败状态！");
        }
        Integer platmform = standplatOrderTransferUtil.getReturnOrderPlatmform(returnOrder);
        if (PlatFormEnum.TAOBAO.getCode().equals(platmform) || PlatFormEnum.JINGDONG.getCode().equals(platmform)) {
            return ValueHolderV14Utils.getFailValueHolder("同步通用平台状态失败，该平台不支持同步！");
        }
        ValueHolderV14 v14 = invokeStandPlatAutoRefund(user, isAuto, returnOrder,platmform, "同步通用平台状态");
        return v14;
    }

    /**
     * 批量处理
     * @param idList
     * @param isAuto
     * @param user
     * @return
     */
    public ValueHolderV14 batchSyncPlatform(List<Long> idList,boolean isAuto, User user) {
        AssertUtil.assertException(CollectionUtils.isEmpty(idList), "请选择需要操作的记录！");
        int failCount = 0;
        int successCount = 0;
        ValueHolderV14 vh = new ValueHolderV14();
        List<JSONObject> failReason = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            Long returnOrderId = idList.get(i);
            ValueHolderV14 v14 = syncPlatformRefundStatus(returnOrderId,isAuto,user);
            if (!v14.isOK()) {
                failCount++;
                JSONObject jsn = new JSONObject();
                jsn.put("objid", returnOrderId);
                jsn.put("message", v14.getMessage());
                failReason.add(jsn);
                continue;
            } else {
                JSONObject object = new JSONObject();
                object.put("objid", returnOrderId);
                object.put("message", "成功");
                failReason.add(object);
            }
            successCount++;
        }
        if (failCount > 0) {
            vh.setCode(ResultCode.FAIL);
        } else {
            vh.setCode(ResultCode.SUCCESS);
        }
        vh.setMessage("操作完成, 同步成功: " + successCount + "条, 失败: " + failCount + "条");
        vh.setData(failReason);
        return vh;
    }

    /**
     * SAP库存异动中间表同步更新退换货单 同步通用平台状态
     * @param returnIdSuccessList
     * @param returnIdFailList
     * @param user
     * @return
     */
    public ValueHolderV14 updatePlatformRefundStatus(List<String> returnIdSuccessList, List<String> returnIdFailList, User user){
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ReturnOrderAuditService.updatePlatformRefundStatus.returnIdSuccessList={},returnIdFailList={}", "ReturnOrderAuditService"), JSON.toJSONString(returnIdSuccessList),JSON.toJSONString(returnIdFailList));
        }
        if(CollectionUtils.isNotEmpty(returnIdSuccessList)){
            List<Long> ids = ES4ReturnOrder.queryReturnOrderIdByBillNo(returnIdSuccessList);
            if(CollectionUtils.isNotEmpty(ids)){
                for(Long id:ids){
                    updatePlatformRefundStatus(id,OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_SUCCESS);
                    recordReturnOrderLog(id, "同步通用平台状态", "SAP库存异动同步成功", true, user);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(returnIdFailList)){
            List<Long> ids = ES4ReturnOrder.queryReturnOrderIdByBillNo(returnIdFailList);
            if(CollectionUtils.isNotEmpty(ids)){
                for(Long id:ids){
                    OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectById(id);
                    if(returnOrder != null){
                        updateFail(returnOrder);
                        updatePlatformRefundStatus(id,OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_FAIL);
                        recordReturnOrderLog(id, "同步通用平台状态", "SAP库存异动同步失败", true, user);
                    }
                }
            }
        }
        return ValueHolderV14Utils.getSuccessValueHolder("更新成功！");
    }
}
