package com.jackrain.nea.oc.oms.spiltorder;

import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.SkuType;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/9/7 下午3:03
 * @Version 1.0
 * 寻源前策略按重量拆单逻辑
 */
@Component
public class OmsSplitWeightService {

    /**
     * @param orderItems
     */
    public List<List<OcBOrderItem>> splitWeightService(List<OcBOrderItem> orderItems, Map<Long, BigDecimal> weightStrategyMap) {
        List<List<OcBOrderItem>> items = new ArrayList<>();
        if (weightStrategyMap == null || weightStrategyMap.isEmpty()) {
            items.add(orderItems);
            return items;
        }
        if (orderItems.size() == 1 && orderItems.get(0).getQty().compareTo(BigDecimal.ONE) == 0) {
            items.add(orderItems);
            return items;
        }

        //没有品类的商品不需要拆的
        List<OcBOrderItem> noSplitItems = orderItems.stream().filter(p -> p.getMDim4Id() == null).collect(Collectors.toList());

        List<OcBOrderItem> spiltItems = orderItems.stream().filter(p -> p.getMDim4Id() != null).collect(Collectors.toList());
        //将商品分组出来  组合商品  赠品 挂靠赠品
        List<OcBOrderItem> groupItems = spiltItems.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT || p.getProType() == SkuType.GIFT_PRODUCT).collect(Collectors.toList());
        //不含赠品
        List<OcBOrderItem> normalItems = spiltItems.stream().filter(p -> (p.getIsGift() == null || p.getIsGift() == 0) && p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
        //赠品
        List<OcBOrderItem> giftItems = spiltItems.stream().filter(p -> (p.getIsGift() != null && p.getIsGift() == 1) && p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
        this.handleItem(noSplitItems, groupItems, normalItems, giftItems);
        Map<Long, List<OcBOrderItem>> itemGroup = normalItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getMDim4Id));
        // List<List<SpiltData>> lists = splitItem(itemGroup, weightStrategyMap);
        List<List<SpiltData>> listList = new ArrayList<>();
        for (Long mdim4 : weightStrategyMap.keySet()) {
            BigDecimal weight = weightStrategyMap.get(mdim4);
            List<OcBOrderItem> itemList = itemGroup.get(mdim4);
            if (CollectionUtils.isEmpty(itemList)) {
                continue;
            }
            List<List<SpiltData>> lists = this.handleSplit(weight, itemList);
            listList.addAll(lists);
            itemGroup.remove(mdim4);
        }
        if (CollectionUtils.isEmpty(listList)) {
            items.add(orderItems);
            return items;
        }
        if (!itemGroup.isEmpty()) {
            Collection<List<OcBOrderItem>> values = itemGroup.values();
            for (List<OcBOrderItem> value : values) {
                noSplitItems.addAll(value);
            }
        }

        List<OcBOrderItem> notSplit = new ArrayList<>();

        List<OcBOrderItem> giftItemsAll = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(noSplitItems)) {
            // items.add(noSplitItems);
            //判断不可拆的是否全部是赠品
            giftItemsAll = noSplitItems.stream().filter(p -> (p.getIsGift() != null && p.getIsGift() == 1) && p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
            List<OcBOrderItem> noSplit = noSplitItems.stream().filter(p -> ((p.getIsGift() == null || p.getIsGift() == 0))
                    || (p.getIsGift() != null && p.getIsGift() == 1 && p.getProType() == SkuType.COMBINE_PRODUCT)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noSplit)) {
                notSplit.addAll(noSplit);
            }
        }
        if (CollectionUtils.isNotEmpty(listList)) {
            Map<Long, OcBOrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
            for (List<SpiltData> list : listList) {
                List<OcBOrderItem> itemList = new ArrayList<>();
                Map<Long, List<SpiltData>> splitMap = list.stream().collect(Collectors.groupingBy(SpiltData::getItemId));
                for (Long itemId : splitMap.keySet()) {
                    List<SpiltData> spiltData = splitMap.get(itemId);
                    BigDecimal qty = spiltData.stream().map(SpiltData::getQty).
                            reduce(BigDecimal.ZERO, BigDecimal::add);
                    OcBOrderItem item = orderItemMap.get(itemId);
                    OcBOrderItem orderItem = new OcBOrderItem();
                    BeanUtils.copyProperties(item, orderItem);
                    orderItem.setQty(qty);
                    itemList.add(orderItem);
                }
                List<OcBOrderItem> collect = itemList.stream().filter(p -> StringUtils.isNotEmpty(p.getGiftRelation())).collect(Collectors.toList());
                Map<String, List<OcBOrderItem>> giftRelationMap = new HashedMap();
                if (CollectionUtils.isNotEmpty(collect)) {
                    giftRelationMap = collect.stream().collect(Collectors.groupingBy(OcBOrderItem::getGiftRelation));
                }
                if (CollectionUtils.isNotEmpty(giftItemsAll)){
                    for (int i = 0; i < giftItemsAll.size(); i++) {
                        OcBOrderItem item = giftItemsAll.get(i);
                        String giftRelation = item.getGiftRelation();
                        Integer isGiftSplit = item.getIsGiftSplit();
                        if (isGiftSplit == null || isGiftSplit == 1) {
                            if (CollectionUtils.isEmpty(notSplit)) {
                                if (StringUtils.isEmpty(giftRelation)){
                                    itemList.add(item);
                                    giftItemsAll.remove(item);
                                    i--;
                                } else {
                                    if (giftRelationMap.containsKey(giftRelation)){
                                        itemList.add(item);
                                        giftItemsAll.remove(item);
                                        i--;
                                    }
                                }
                            }
                        }
                    }
                }
                items.add(itemList);
            }
        }
        if (CollectionUtils.isNotEmpty(giftItemsAll)){
            notSplit.addAll(giftItemsAll);
        }
        if (CollectionUtils.isNotEmpty(notSplit)) {
            items.add(notSplit);
        }
        return items;
    }


    private List<List<SpiltData>> handleSplit(BigDecimal weight, List<OcBOrderItem> itemList) {
        List<SpiltData> spiltDataList = new ArrayList<>();
        for (OcBOrderItem item : itemList) {
            SpiltData data = new SpiltData();
            BigDecimal standardWeight = item.getStandardWeight() == null ? BigDecimal.ZERO : item.getStandardWeight();
            data.setItemId(item.getId());
            data.setQty(item.getQty());
            data.setWeight(standardWeight);
            data.setTotalWeight(standardWeight.multiply(item.getQty()));
            spiltDataList.add(data);
        }
        //排序
        spiltDataList.sort((o1, o2) -> o2.getTotalWeight().compareTo(o1.getTotalWeight()));
        List<List<SpiltData>> lists = new ArrayList<>();
        List<SpiltData> noSpiltDatas = new ArrayList<>();
        List<SpiltData> preSpiltDatas = new ArrayList<>();
        //预处理
        for (SpiltData data : spiltDataList) {
//            BigDecimal totalWeight = data.getTotalWeight();
//            BigDecimal qty = data.getQty();
//            BigDecimal divide = totalWeight.divide(qty, 4, BigDecimal.ROUND_HALF_UP);
//            if (divide.compareTo(weight) >= 0) {
//                noSpiltDatas.add(data);
//                continue;
//            }
//            List<SpiltData> dataList = new ArrayList<>();
//            if (totalWeight.compareTo(weight) == 0) {
//                dataList.add(data);
//                lists.add(dataList);
//                continue;
//            }
//            if (qty.compareTo(BigDecimal.ONE) == 0) {
//                preSpiltDatas.add(data);
//                continue;
//            }
//            BigDecimal downQty = weight.divide(data.getWeight(), 0, BigDecimal.ROUND_DOWN);
//            BigDecimal splitQty = qty.divide(downQty, 0, BigDecimal.ROUND_DOWN);
//            for (int i = 0; i < splitQty.intValue(); i++) {
//                SpiltData data1 = new SpiltData();
//                data1.setItemId(data.getItemId());
//                data1.setQty(downQty);
//                data1.setWeight(data.getWeight());
//                data1.setTotalWeight(downQty.multiply(data.getWeight()));
//                preSpiltDatas.add(data1);
//            }
//            BigDecimal multiply = splitQty.multiply(downQty);
//            BigDecimal overQty = qty.subtract(multiply);
//            if (overQty.compareTo(BigDecimal.ZERO) != 0) {
//                SpiltData data1 = new SpiltData();
//                data1.setItemId(data.getItemId());
//                data1.setQty(overQty);
//                data1.setWeight(data.getWeight());
//                data1.setTotalWeight(overQty.multiply(data.getWeight()));
//                preSpiltDatas.add(data1);
//            }
//        }
//        if (CollectionUtils.isNotEmpty(noSpiltDatas)) {
//            lists.add(noSpiltDatas);
//        }
            BigDecimal qty = data.getQty();
            for (int i = 0; i < qty.intValue(); i++) {
                SpiltData data1 = new SpiltData();
                data1.setItemId(data.getItemId());
                data1.setQty(BigDecimal.ONE);
                data1.setWeight(data.getWeight());
                data1.setTotalWeight(BigDecimal.ONE.multiply(data.getWeight()));
                preSpiltDatas.add(data1);
            }

        }
        this.handlePreSplit(preSpiltDatas, weight, lists);
        return lists;


    }

    /**
     * 处理预拆单
     *
     * @param preSpiltDatas
     */
    private void handlePreSplit(List<SpiltData> preSpiltDatas, BigDecimal weight, List<List<SpiltData>> lists) {
        if (CollectionUtils.isEmpty(preSpiltDatas)) {
            return;
        }
        if (preSpiltDatas.size() == 1) {
            lists.add(preSpiltDatas);
            return;
        }
        preSpiltDatas.sort((o1, o2) -> o2.getTotalWeight().compareTo(o1.getTotalWeight()));
        for (int i = 0; i < preSpiltDatas.size(); i++) {
            SpiltData data = preSpiltDatas.get(i);
            BigDecimal totalWeight = data.getTotalWeight();
            List<SpiltData> dataList = new ArrayList<>();
            for (int j = 0; j < preSpiltDatas.size(); j++) {
                SpiltData data1 = preSpiltDatas.get(j);
                if (j == i) {
                    if (preSpiltDatas.size() == 1) {
                        lists.add(preSpiltDatas);
                        return;
                    }
                    preSpiltDatas.remove(data1);
                    i--;
                    j--;
                    continue;
                }
                BigDecimal totalWeightNew = data1.getTotalWeight();
                if (CollectionUtils.isNotEmpty(dataList)) {
                    totalWeight = dataList.stream().map(SpiltData::getTotalWeight).
                            reduce(BigDecimal.ZERO, BigDecimal::add);
                    totalWeight = totalWeight.add(data.getTotalWeight());
                }
                BigDecimal add = totalWeight.add(totalWeightNew);
                if (add.compareTo(weight) > 0) {
                    continue;
                }
                //小于或者等于
                dataList.add(data1);
                preSpiltDatas.remove(data1);
                j--;
            }

            dataList.add(data);
            //preSpiltDatas.remove(data);
            lists.add(dataList);

        }
        if (CollectionUtils.isNotEmpty(preSpiltDatas)) {
            lists.add(preSpiltDatas);
        }
    }


    private void handleItem(List<OcBOrderItem> noSplitItems, List<OcBOrderItem> groupItems,
                            List<OcBOrderItem> normalItems, List<OcBOrderItem> giftItems) {
        if (CollectionUtils.isNotEmpty(groupItems)) {
            Map<String, List<OcBOrderItem>> groupMap = groupItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
            //判断是否可以拆单
            for (String key : groupMap.keySet()) {
                List<OcBOrderItem> itemList = groupMap.get(key);
                String canSplit = itemList.get(0).getCanSplit();
                if ("Y".equals(canSplit)) {
                    normalItems.addAll(itemList);
                } else {
                    noSplitItems.addAll(itemList);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(giftItems)) {
            for (OcBOrderItem giftItem : giftItems) {
                Integer isGiftSplit = giftItem.getIsGiftSplit();
                if (isGiftSplit == null || isGiftSplit == 1) {
                    noSplitItems.add(giftItem);
                } else {
                    normalItems.add(giftItem);
                }
            }
        }
    }

    @Data
    private static class SpiltData {
        private Long itemId;
        private BigDecimal qty;
        private BigDecimal weight;
        private BigDecimal totalWeight;
    }
}
