//package com.jackrain.nea.oc.oms.services;
//
//import com.jackrain.nea.jdbc.datasource.TargetDataSource;
//import com.jackrain.nea.model.util.ModelUtil;
//import com.jackrain.nea.oc.oms.model.enums.ChannelType;
//import com.jackrain.nea.oc.oms.model.enums.MakeupOrderStatus;
//import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
//import com.jackrain.nea.oc.oms.model.table.OcTOrderMakeup;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
////import com.jackrain.nea.oc.oms.mapper.OcTOrderMakeupMapper;
//
///**
// * @author: 易邵峰
// * @since: 2019-02-23
// * create at : 2019-02-23 19:09
// */
//@Component
//public class OmsOrderMakeupService {
//
//    private static final String ORDER_MAKEUP_TABLE_NAME = "oc_t_order_makeup";
//
//    @Autowired
//    private OcTOrderMakeupMapper orderMakeupMapper;
//
//    private OcTOrderMakeup buildOrderMakeup(long orderId, String orderNo, ChannelType channelType,
//                                            MakeupOrderType makeupOrderType) {
//        OcTOrderMakeup makeupInfo = new OcTOrderMakeup();
//        long autoId = ModelUtil.getSequence(ORDER_MAKEUP_TABLE_NAME);
//        makeupInfo.setId(autoId);
//        makeupInfo.setMakeupRemarks("");
//        makeupInfo.setMakeupStatus(MakeupOrderStatus.WAIT_MAKEUP.toInteger());
//        makeupInfo.setModifierename("");
//        makeupInfo.setOrderId(orderId);
//        makeupInfo.setOrderNo(orderNo);
//        makeupInfo.setMakeupType(makeupOrderType.toInteger());
//        makeupInfo.setChannelType(channelType.toInteger());
//        makeupInfo.setTimes(0L);
//        makeupInfo.setOwnerename("");
//        return makeupInfo;
//    }
//
//    private boolean insertOrderMakeup(long orderId, String orderNo, ChannelType channelType,
//                                      MakeupOrderType makeupOrderType) {
//        OcTOrderMakeup makeupInfo = this.buildOrderMakeup(orderId, orderNo, channelType, makeupOrderType);
//        int result = orderMakeupMapper.insert(makeupInfo);
//        return result > 0;
//    }
//
//    /**
//     * 更新订单转单补偿状态
//     * 处理逻辑：
//     * 1、依据OrderId、OrderType进行更新订单状态值
//     * 2、如果更新的条数=0，表示没有对应的补偿单据信息，则需要进行新增单据信息
//     *
//     * @param orderId       订单ID
//     * @param channelType   订单类型
//     * @param orderStatus   订单状态
//     * @param makeupRemarks 补偿备注说明
//     * @return 更新补偿订单状态结果。true-成功
//     */
//    @TargetDataSource(name = "mysql")
//    public boolean updateOrderMakeupStatus(long orderId, String orderNo,
//                                           ChannelType channelType, MakeupOrderType makeupOrderType,
//                                           MakeupOrderStatus orderStatus,
//                                           String makeupRemarks) {
//        int result = orderMakeupMapper.updateMakeupOrderStatus(orderId, channelType.toInteger(),
//                orderStatus.toInteger(), makeupRemarks);
//        if (result == 0) {
//            return this.insertOrderMakeup(orderId, orderNo, channelType, makeupOrderType);
//        }
//        return result > 0;
//    }
//
//    /**
//     * 保存订单转换补偿服务数据
//     *
//     * @param orderId     订单ID
//     * @param channelType 订单类型
//     * @return 保存补偿订单结果。true-成功
//     */
//    @TargetDataSource(name = "mysql")
//    public boolean saveOrderMakeup(long orderId, String orderNo, ChannelType channelType,
//                                   MakeupOrderType makeupOrderType) {
//        OcTOrderMakeup findMakeupInfo = this.orderMakeupMapper.selectMakeupOrder(orderId,
//                channelType.toInteger(), makeupOrderType.toInteger());
//
//        if (findMakeupInfo != null) {
//            MakeupOrderStatus makeupOrderStatus = MakeupOrderStatus.parseFromInt(findMakeupInfo.getMakeupStatus());
//            return this.updateOrderMakeupStatus(orderId, orderNo, channelType,
//                    makeupOrderType, makeupOrderStatus, findMakeupInfo.getMakeupRemarks());
//        } else {
//            return this.insertOrderMakeup(orderId, orderNo, channelType, makeupOrderType);
//        }
//    }
//
//
//    @TargetDataSource(name = "mysql")
//    public int selectNeedMakeupOrderNumber() {
//        return this.orderMakeupMapper.selectNeedMakeupOrderNumber();
//    }
//
//    @TargetDataSource(name = "mysql")
//    public List<OcTOrderMakeup> selectNeedMakeupOrder() {
//        return this.orderMakeupMapper.selectNeedMakeupOrder();
//    }
//
//    @TargetDataSource(name = "mysql")
//    public List<OcTOrderMakeup> selectNeedMakeupOrderByIds(String ids) {
//        return this.orderMakeupMapper.selectNeedMakeupOrderByIds(ids);
//    }
//
//    @TargetDataSource(name = "mysql")
//    public boolean deleteMakeupOrder(Long id) {
//        return this.orderMakeupMapper.deleteById(id) > 0;
//    }
//}
