package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 订单平摊金额服务
 *
 * @author: heliu
 * @since: 2019/3/12
 * create at : 2019/3/12 18:27
 */
@Component
@Slf4j
public class OmsOrderCheckAndUpdateService {

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderService omsOrderService;

    /**
     * 平摊金额为未退款明细
     *
     * @param orderInfo OcBOrderRelation对象
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean doCheckAndUpdateBlanceMoney(OcBOrderRelation orderInfo) {

        //执行内容不包含已退款明细selectUnSuccessRefundAndNoSplit
        List<OcBOrderItem> orderItemList = omsOrderItemService.selectUnSuccessRefund(orderInfo.getOrderInfo().getId());
        //去除成交单价为0的明细
        List<OcBOrderItem> unSuccessRefundList = orderItemList.stream().filter(
                o -> o.getPrice().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        try {
            if (CollectionUtils.isNotEmpty(unSuccessRefundList)) {
                if (unSuccessRefundList.size() == 1) {
                    //若订单明细中只有一条数据，则更新订单明细中“整单平摊金额”=主表（全渠道订单）的“订单优惠金额”
                    OcBOrderItem orderItemDto = unSuccessRefundList.get(0);
                    //更新明细表的明细平摊金额
                    orderItemDto.setOrderSplitAmt(orderInfo.getOrderInfo().getOrderDiscountAmt() == null ? BigDecimal.ZERO : orderInfo.getOrderInfo().getOrderDiscountAmt());
                    omsOrderItemService.updateOcBOrderItem(orderItemDto, orderInfo.getOrderId());
                } else {
                    int j = 0;
                    for (OcBOrderItem orderItem : unSuccessRefundList) {
                        //判断订单明细中的成交价格是否大于0
                        if (orderItem.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                            j++;
                        }
                    }
                    //只要有一条满足都执行平摊金额
                    if (j > 0) {
                        //除末尾行其他明细整单平摊金额之和
                        BigDecimal totalItemAmt = BigDecimal.ZERO;
                        //最后一行明细平摊金额
                        BigDecimal lastOrderSplitAmt = BigDecimal.ZERO;
                        //主表的订单优惠金额
                        BigDecimal orderDiscountAmt = (orderInfo.getOrderInfo().getOrderDiscountAmt() == null ? BigDecimal.ZERO : orderInfo.getOrderInfo().getOrderDiscountAmt());
                        //非退款明细求和
                        BigDecimal sumPrice = sumPrice(orderInfo.getOrderInfo(), unSuccessRefundList);
                        //避免明细求和为0
                        if (sumPrice.compareTo(BigDecimal.ZERO) != 0) {
                            for (int i = 0; i < unSuccessRefundList.size(); i++) {
                                OcBOrderItem newOderItem = unSuccessRefundList.get(i);
                                if (i < unSuccessRefundList.size() - 1) {
                                    //“整单平摊金额”= 【（当前明细“成交价格”*“数量”/sum（明细“成交价格”*“数量”）】*主表的订单优惠金额
                                    BigDecimal orderSplitAmt = BigDecimal.ZERO;
                                    // 京东订单明细行平摊金额 按照成交金额计算比例 通用平台订单按照price*qty计算比例
                                    if (orderInfo.getOrderInfo().getPlatform() != null
                                            && orderInfo.getOrderInfo().getPlatform().intValue() == PlatFormEnum.JINGDONG.getCode()) {
                                        orderSplitAmt = orderDiscountAmt.multiply(newOderItem.getRealAmt()).divide(sumPrice, 0, RoundingMode.HALF_UP).setScale(2);
                                    } else if (orderInfo.getOrderInfo().getPlatform() != null
                                            && orderInfo.getOrderInfo().getPlatform().intValue() == PlatFormEnum.DANGDANG.getCode()) {
                                        // 当当平摊金额 = 优惠金额 *（平台售价*数量-商品优惠）/sum(平台售价*数量-商品优惠）
                                        BigDecimal price = Optional.ofNullable(newOderItem.getPrice()).orElse(BigDecimal.ZERO);
                                        BigDecimal qty = Optional.ofNullable(newOderItem.getQty()).orElse(BigDecimal.ZERO);
                                        BigDecimal amtDiscount = Optional.ofNullable(newOderItem.getAmtDiscount()).orElse(BigDecimal.ZERO);
                                        orderSplitAmt = orderDiscountAmt.multiply(price.multiply(qty).subtract(amtDiscount)).divide(sumPrice, 4, RoundingMode.HALF_UP);
                                    } else {
                                        orderSplitAmt = orderDiscountAmt.multiply(newOderItem.getPrice()).multiply(newOderItem.getQty()).divide(sumPrice, 4, BigDecimal.ROUND_HALF_UP);
                                    }
                                    //除去最后一行的整体平摊金额之和
                                    totalItemAmt = totalItemAmt.add(orderSplitAmt);
                                    //更新明细平摊金额
                                    newOderItem.setOrderSplitAmt(orderSplitAmt);
                                    omsOrderItemService.updateOcBOrderItem(newOderItem, orderInfo.getOrderId());
                                } else if (i == unSuccessRefundList.size() - 1) {
                                    //减去平摊金额
                                    lastOrderSplitAmt = orderDiscountAmt.subtract(totalItemAmt);
                                    newOderItem.setOrderSplitAmt(lastOrderSplitAmt);
                                    omsOrderItemService.updateOcBOrderItem(newOderItem, orderInfo.getOrderId());
                                }
                            }
                        }
                    }
                }
                //重新查一遍更新平摊金额后的订单明细数据
                List<OcBOrderItem> unNewSuccessRefundList = omsOrderItemService.selectUnSuccessRefund(orderInfo.getOrderInfo().getId());
                //明细平摊金额计算完成之后，重新更新各个明细的单行实际成交金额
                BigDecimal realAmt = BigDecimal.ZERO;
                for (OcBOrderItem orderItem : unNewSuccessRefundList) {
                    //单行实际成交金额=明细的成交价*数量-明细的平摊金额
                    BigDecimal price = (orderItem.getPrice() == null ? BigDecimal.ZERO : orderItem.getPrice());
                    BigDecimal qty = (orderItem.getQty() == null ? BigDecimal.ZERO : orderItem.getQty());
                    BigDecimal orderSplitAmt = (orderItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : orderItem.getOrderSplitAmt());
                    BigDecimal amtDiscount = orderItem.getAmtDiscount() == null ? BigDecimal.ZERO : orderItem.getAmtDiscount();
                    realAmt = price.multiply(qty).subtract(orderSplitAmt).subtract(amtDiscount).add(Optional.ofNullable(orderItem.getAdjustAmt()).orElse(BigDecimal.ZERO));
                    orderItem.setRealAmt(realAmt);
                    omsOrderItemService.updateOcBOrderItem(orderItem, orderInfo.getOrderId());
                }
            }
            //非淘宝订单,清除流程系统备注
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setSysremark("");
            ocBOrder.setId(orderInfo.getOrderId());
            omsOrderService.updateOrderInfo(ocBOrder);
            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("doCheckAndUpdateBlanceMoney订单执行平摊逻辑服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 非退款明细求和
     *
     * @param orderInfo
     * @param unRefundItemList
     * @return BigDecimal
     */
    private BigDecimal sumPrice(OcBOrder orderInfo, List<OcBOrderItem> unRefundItemList) {
        BigDecimal priceTotal = BigDecimal.ZERO;
        for (OcBOrderItem ocBOrderItem : unRefundItemList) {
            if (orderInfo.getPlatform() != null
                    && orderInfo.getPlatform().intValue() == PlatFormEnum.JINGDONG.getCode()) {
                priceTotal = priceTotal.add(Optional.ofNullable(ocBOrderItem.getRealAmt()).orElse(BigDecimal.ZERO));
            } else if (orderInfo.getPlatform() != null
                    && orderInfo.getPlatform().intValue() == PlatFormEnum.DANGDANG.getCode()) {
                // 当当平摊金额 = 优惠金额 *（平台售价*数量-商品优惠）/sum(平台售价*数量-商品优惠）
                priceTotal = priceTotal.add(Optional.ofNullable(ocBOrderItem.getPrice()).orElse(BigDecimal.ZERO)
                        .multiply(Optional.ofNullable(ocBOrderItem.getQty()).orElse(BigDecimal.ZERO)).subtract(Optional.ofNullable(ocBOrderItem.getAmtDiscount()).orElse(BigDecimal.ZERO)));
            } else {
                priceTotal = priceTotal.add(Optional.ofNullable(ocBOrderItem.getPrice()).orElse(BigDecimal.ZERO)
                        .multiply(Optional.ofNullable(ocBOrderItem.getQty()).orElse(BigDecimal.ZERO)));
            }
        }
        return priceTotal;
    }


    /**
     * 检查平摊金额条件是否需要重新计算
     *
     * @param originalOrderRelation 原始单据信息
     * @return 返回布尔类型的值
     */
    public boolean checkSplitAmountIsNeedReCalc(OcBOrderRelation originalOrderRelation) {
        OcBOrder originalOrderInfo = originalOrderRelation.getOrderInfo();
        BigDecimal orderDiscountAmt = (originalOrderInfo.getOrderDiscountAmt() == null ? BigDecimal.ZERO : originalOrderInfo.getOrderDiscountAmt());
        BigDecimal orderSplitAmtTotal = BigDecimal.ZERO;
        //排除退款成功的明细
        List<OcBOrderItem> unSuccessRefundList = omsOrderItemService.selectUnSuccessRefund(originalOrderInfo.getId());
        if (CollectionUtils.isNotEmpty(unSuccessRefundList)) {
            for (OcBOrderItem unRefundItem : unSuccessRefundList) {
                orderSplitAmtTotal = orderSplitAmtTotal.add((unRefundItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : unRefundItem.getOrderSplitAmt()));
            }
        }

        //若非淘系订单并且订单优惠金额不等于明细平摊金额的合计，调用平摊金额服务
        if ((!originalOrderInfo.getPlatform().equals(PlatFormEnum.TAOBAO.getCode())) && (orderDiscountAmt.compareTo(orderSplitAmtTotal) != 0)) {
            return true;
        }
        return false;
    }
}