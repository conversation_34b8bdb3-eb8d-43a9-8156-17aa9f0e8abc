package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.model.BnQueryForLogisticsProblemResult;
import com.jackrain.nea.oc.oms.mapper.OcBOrderBnTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderBnTask;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCBnWarehouseLogisticsConfigDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderForBnService
 * @Description 班牛相关操作
 * <AUTHOR>
 * @Date 2024/11/15 16:11
 * @Version 1.0
 */
@Component
public class OcBOrderForBnService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private OcBOrderBnTaskMapper ocBOrderBnTaskMapper;

    public ValueHolderV14<BnQueryForLogisticsProblemResult> getBnInfoByOrderId(Long orderId) {
        ValueHolderV14<BnQueryForLogisticsProblemResult> vh = new ValueHolderV14();
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
        // 判断订单状态 以及物流信息
        if (ocBOrder == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单ID:" + orderId + "订单不存在");
            return vh;
        }
        Integer orderStatus = ocBOrder.getOrderStatus();
        if (!ObjectUtil.equal(orderStatus, OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger()) && !ObjectUtil.equal(orderStatus, OmsOrderStatus.PLATFORM_DELIVERY.toInteger())) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单ID:" + orderId + "订单状态异常");
            return vh;
        }
        Integer orderType = ocBOrder.getOrderType();
        if (ObjectUtil.equal(orderType, OrderTypeEnum.DIFFPRICE.getVal())) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单ID:" + orderId + "虚拟订单不支持创建物流工单");
            return vh;
        }
        // 查看是不是有物流信息
        String expressCode = ocBOrder.getExpresscode();
        if (StringUtils.isEmpty(expressCode)) {
            // 已经有物流信息
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单ID:" + orderId + "订单不存在物流单号");
            return vh;
        }
        String warehouseCode = ocBOrder.getCpCPhyWarehouseEcode();
        String logisticCode = ocBOrder.getCpCLogisticsEcode();
        BnQueryForLogisticsProblemResult logisticsProblemResult = new BnQueryForLogisticsProblemResult();
        // 根据这两个编码 去查 班牛仓库物流表
        StCBnWarehouseLogisticsConfigDO bnWarehouseLogisticsConfigDO = stRpcService.queryByWarehouseCodeAndLogisticsCode(warehouseCode, logisticCode);
        if (bnWarehouseLogisticsConfigDO == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单ID:" + orderId + "物流信息不存在");
            vh.setData(logisticsProblemResult);
            return vh;
        }
        if (bnWarehouseLogisticsConfigDO.getBnLogisticsId() == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单ID:" + orderId + "物流信息不存在");
            vh.setData(logisticsProblemResult);
            return vh;
        }
        List<OcBOrderBnTask> bOrderBnTaskList = ocBOrderBnTaskMapper.selectByOrderId(orderId);
        if (CollectionUtils.isEmpty(bOrderBnTaskList)) {
            logisticsProblemResult.setHasBnTask(0);
        } else {
            logisticsProblemResult.setHasBnTask(1);
        }
        logisticsProblemResult.setBnWarehouseLogisticsId(bnWarehouseLogisticsConfigDO.getBnLogisticsId());
        logisticsProblemResult.setBnWarehouseLogistics(bnWarehouseLogisticsConfigDO.getBnLogistics());
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("success");
        vh.setData(logisticsProblemResult);
        return vh;
    }
}
