package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.model.TransferModifyAddrInfo;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: 黄世新
 * @Date: 2022/10/12 下午2:38
 * @Version 1.0
 * 转单修改地址
 */
@Slf4j
@Component
public class OmsTransferModifyAddrService {

    @Autowired
    private OcBOrderUpdateAddressService ocBOrderUpdateAddressService;


    public String transferModifyAddrService(List<OcBOrder> ocBOrders, Object orderInfo, User operateUser, String typeName) {
        StringBuffer remark = new StringBuffer();
        for (OcBOrder afterTransferOrder : ocBOrders) {
            if (afterTransferOrder == null) {
                continue;
            }
            Integer isManualAddr = afterTransferOrder.getIsManualAddr();
            if (isManualAddr != null && isManualAddr == 1) {
                continue;
            }

            //零售发货单状态不为取消或作废状态
            if (checkOrderForModifyAddr(afterTransferOrder)) {
                ValueHolderV14 valueHolderV14 = null;
                //更新买家收货信息
                if ("taobao".equals(typeName)) {
                    valueHolderV14 = ocBOrderUpdateAddressService.updateOmsOrderReceiveInfoByTaoBao((IpTaobaoOrderRelation) orderInfo, afterTransferOrder, operateUser);
                }
                if ("jingdong".equals(typeName)){
                    valueHolderV14 = ocBOrderUpdateAddressService.updateOmsOrderReceiveInfoByJD((IpJingdongOrderRelation) orderInfo, afterTransferOrder, operateUser);
                }
                if (Objects.nonNull(valueHolderV14) && !valueHolderV14.isOK()) {
                    remark.append(valueHolderV14.getMessage());
                    remark.append(",");
                }
            }
        }
        if (remark.length() != 0) {
            //去掉备注最后一个逗号
            String sub = remark.substring(0, remark.length() - 1);
            String remarkStr = StringUtils.substring(sub, 0, 300);
            return remarkStr;
        }
        return null;
    }

    /**
     * 修改地址支持 正常+预售的订单
     */
    private boolean checkOrderForModifyAddr(OcBOrder order) {
        if(OmsOrderStatus.SYS_VOID.toInteger().equals(order.getOrderStatus()) ||
                OmsOrderStatus.CANCELLED.toInteger().equals(order.getOrderStatus())){
            return  false;
        }
        if("手工新增".equals(order.getOrderSource())){
            return  false;
        }
        return (order.getOrderType() == 1
                || Objects.equals(order.getOrderType(), OrderTypeEnum.TBA_PRE_SALE.getVal()));
    }
}
