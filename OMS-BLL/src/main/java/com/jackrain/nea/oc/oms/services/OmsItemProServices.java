package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.basic.model.request.BarcodeSaveCmdRequest;
import com.jackrain.nea.oc.oms.mapper.OmsItemProMapper;
import com.jackrain.nea.ps.model.OmsProDelCmdRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/6/30 下午4:06
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsItemProServices {


    public ValueHolderV14 getItemIds(OmsProDelCmdRequest model) {
        ValueHolderV14 vh14 = new ValueHolderV14();
        OmsItemProMapper omsItemProMapper = ApplicationContextHandle.getBean(OmsItemProMapper.class);
        vh14.setData(omsItemProMapper.getItemIds(model.getObjId(), model.getTableName(), model.getProECodes(), model
                .getKey()));
        return vh14;
    }

    public ValueHolderV14 selectSkuInfo(OmsProDelCmdRequest model, int operation) {
        ValueHolderV14 vh14 = new ValueHolderV14();
        OmsItemProMapper omsItemProMapper = ApplicationContextHandle.getBean(OmsItemProMapper.class);
        //兼容 pg库  pg  查询出来都是小写
        List<JSONObject> list = omsItemProMapper.selectSkuInfo(model.getObjId(), model.getTableName(), model.getProECode
                (), model.getKey(), operation);
        if (log.isDebugEnabled()) {
            log.debug("list==>" + list);
        }
        List<String> skuEcodeList = new ArrayList<>(list.size());
        List<JSONObject> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(jo -> {
                skuEcodeList.add(jo.getString("PS_C_SKU_ECODE"));
                JSONObject resultJo = new JSONObject();
                for (String key : jo.keySet()) {
                    resultJo.put(key.toUpperCase(), jo.get(key));
                }
                resultList.add(resultJo);
            });
            //发货店仓不为空计算下可用库存
/*            if (model.getDestId() != null) {
                List<Long> storeIds = new ArrayList<>();
                storeIds.add(model.getDestId());
                List<SgBStorage> sgStorageList = SgStorageUtilBase.queryStorage(storeIds, skuEcodeList, null, null, model.getUser());
                if (CollectionUtils.isNotEmpty(sgStorageList)) {
                    Map<String, SgBStorage> map = sgStorageList.stream().collect(Collectors.toMap(SgBStorage::getPsCSkuEcode, storage->storage));
                    resultList.forEach(jo -> {
                        SgBStorage sgBStorage = map.get(jo.getString("PS_C_SKU_ECODE"));
                        if (sgBStorage != null) {
                            jo.put("DEST_QTY_AVAILABLE", sgBStorage.getQtyAvailable());
                            jo.put("DEST_QTY_PREIN", sgBStorage.getQtyPrein());
                        }else{
                            jo.put("DEST_QTY_AVAILABLE", 0);
                            jo.put("DEST_QTY_PREIN", 0);
                        }

                    });
                }
            }*/
            //发货店仓不为空计算下可用库存
            if (model.getDestId() != null&&model.getBeginDate() != null&&model.getEndDate() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
                try {
//                    Long  beginTime=  sdf.parse(model.getBeginDate()).getTime();
//                    Long  endTime=  sdf.parse(model.getEndDate()).getTime();
//                    Map<String, BigDecimal> retailMap=  SgStorageUtilBase.batchSelectQtyRetail(model.getDestId(),null,skuEcodeList,beginTime,endTime);
//                    resultList.forEach(jo -> {
//                        String  ecode=jo.getString("PS_C_SKU_ECODE");
//                        if(retailMap.containsKey(ecode)){
//                            BigDecimal  count = retailMap.get(ecode);
//                            jo.put("DEST_QTY_RETAIL", count);
//                        }
//                        else{
//                            jo.put("DEST_QTY_RETAIL", 0);
//                        }
//                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        vh14.setData(resultList);
        return vh14;
    }

    public ValueHolderV14 selectBillSkuNum(BarcodeSaveCmdRequest model) {
        ValueHolderV14 vh14 = new ValueHolderV14();
        if (log.isDebugEnabled()) {
            log.debug("BarcodeSaveCmdRequest:" + model);
        }
        OmsItemProMapper omsItemProMapper = ApplicationContextHandle.getBean(OmsItemProMapper.class);
        JSONObject jsonObject = omsItemProMapper.selectBillSkuNum(model.getTable(), model.getDepotsKey(), model
                        .getDepotsValue(),
                model.getSkuEcode());
        if (log.isDebugEnabled()) {
            log.debug("jsonObject:" + jsonObject);
        }
        vh14.setData(jsonObject);
        return vh14;
    }

    /**
     * 扫描数量查询
     *
     * @param model 入参
     * @return 出参
     */
    public ValueHolderV14 selectBillScanSkuNum(BarcodeSaveCmdRequest model) {
        ValueHolderV14 vh14 = new ValueHolderV14();
        OmsItemProMapper omsItemProMapper = ApplicationContextHandle.getBean(OmsItemProMapper.class);
        vh14.setData(omsItemProMapper.selectBillScanSkuNum(model.getTable(), model.getDepotsKey(), model.getDepotsValue(),
                model.getSkuEcode()));
        return vh14;
    }

    public ValueHolderV14 selectSkuInfoSpec1(OmsProDelCmdRequest model, int operation) {
        ValueHolderV14 vh14 = new ValueHolderV14();
        OmsItemProMapper omsItemProMapper = ApplicationContextHandle.getBean(OmsItemProMapper.class);
        //兼容 pg库  pg  查询出来都是小写
        List<JSONObject> list = omsItemProMapper.selectSkuInfoSpec1(model.getObjId(), model.getTableName(), model
                .getProECode(), model.getKey(), operation);
        if (log.isDebugEnabled()) {
            log.debug("list==============" + list);
        }
        List<JSONObject> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(jo -> {
                JSONObject resultJo = new JSONObject();
                for (String key : jo.keySet()) {
                    resultJo.put(key.toUpperCase(), jo.get(key));
                }
                resultList.add(resultJo);
            });
        }
        vh14.setData(resultList);
        return vh14;
    }
}
