package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 淘宝预售.作废订单
 *
 * @author: xiWen.z
 * create at: 2019/10/23 0023
 */
@Slf4j
@Component
public class OmsPreSaleOrderService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OmsOrderCancellationService cancelService;

    /**
     * @param keys list
     * @param user User
     */
    public void executeVoidOmsOrder(List<Long> keys, User user) {
        final int msgSize = 100;
        int count = 0;
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        Map<Long, String> expMap = new HashMap<>();
        for (Long key : keys) {
            if (key == null) {
                continue;
            }
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(key);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    OcBOrder ocBOrder = ocBOrderMapper.selectUnPadPreSaleOrder(key);
                    if (ocBOrder == null) {
                        expMap.put(key, "OcBOrder->Null,Key->" + key);
                        continue;
                    }
                    Integer status = ocBOrder.getOrderStatus();
                    if (status == null) {
                        expMap.put(key, "Key->" + key + ",状态-Null");
                        continue;
                    }
                    String padStep = ocBOrder.getStatusPayStep();
                    if (!TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equalsIgnoreCase(padStep)) {
                        expMap.put(key, "Key->" + key + ",阶段付款状态-" + padStep);
                        continue;
                    }
                    if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(status) || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(status)) {
                        ValueHolderV14 vh = cancelService.doInvoildOutOrder(ocBOrder, user);
                        if (vh == null) {
                            expMap.put(key, "Key->" + key + ",状态-" + status + ",invoke->ValueHolder14-Null");
                            continue;
                        } else if (vh.getCode() == ResultCode.FAIL) {
                            String msg;
                            if (vh.getMessage() != null && vh.getMessage().length() > msgSize) {
                                msg = vh.getMessage().substring(0, msgSize);
                            } else {
                                msg = vh.getMessage();
                            }
                            expMap.put(key, "Key->" + key + ",ValueHolder14-FAIL,msg->" + msg);
                            continue;
                        }
                    } else {
                        expMap.put(key, "Key->" + key + ",状态-" + status + ",单据状态不符合");
                        continue;
                    }
                    count++;
                } else {
                    expMap.put(key, "Key->" + key + "处于锁定状态");
                }
            } catch (Exception e) {
                expMap.put(key, "异常Id->" + key);
                log.error(LogUtil.format("executeVoidOmsOrder,异常信息为:{}", key), Throwables.getStackTraceAsString(e));
            } finally {
                redisLock.unlock();
            }
        }
    }

}
