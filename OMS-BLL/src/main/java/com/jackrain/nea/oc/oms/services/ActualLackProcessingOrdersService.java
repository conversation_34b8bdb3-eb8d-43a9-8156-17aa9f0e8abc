package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBDeficiencyItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBDeficiencyMapper;
import com.jackrain.nea.oc.oms.mapper.OcBDeficiencyTransferMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBDeficiency;
import com.jackrain.nea.oc.oms.model.table.OcBDeficiencyItem;
import com.jackrain.nea.oc.oms.model.table.OcBDeficiencyTransfer;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemTableNames;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.jackrain.nea.sg.service.AddAndVoidStockListService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: 实缺处理单操作
 * @Package com.jackrain.nea.oc.oms.services
 * @Description: 实缺处理单操作
 * @date 2019/7/18 1:49 PM
 */
@Slf4j
@Component
public class ActualLackProcessingOrdersService {

    private static final int ONE_DAY_SECONDS = 24 * 60 * 60;

    private static final String OBJ_ID = "objid";

    @Autowired
    private OcBDeficiencyTransferMapper ocBDeficiencyTransferMapper;
    @Autowired
    private OcBDeficiencyItemMapper ocBDeficiencyItemMapper;
    @Autowired
    private OcBDeficiencyMapper ocBDeficiencyMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OmsOrderManualSplitService omsOrderManualSplitService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private BasicCpQueryService basicCpQueryService;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private AddAndVoidStockListService addAndVoidStockListService;

    @Autowired
    private AgainOccupyStockService againOccupyStockService;

    /**
     * 保存数据
     *
     * @param session
     * @return
     */
    @Transactional
    public ValueHolder saveData(QuerySession session) {

        ValueHolder result;
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        JSONObject rows = param.getJSONObject("fixcolumn");
        if (rows == null) {
            result = this.result(-1L, ResultCode.FAIL, "新增、更新时需传入对应数据!");
        } else {
            JSONObject mainRow = rows.getJSONObject("OC_B_DEFICIENCY");
            Long objid = param.getLong(OBJ_ID);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("ActualLackProcessingOrdersService:saveData进入方法开始,参数:{}", objid), JSON.toJSONString(rows));
            }
            if (null != objid && objid.doubleValue() > 0) {
                result = this.result(objid, ResultCode.SUCCESS, "保存成功！");
            } else {
                Long id = -1L;
                //保存
                id = this.save(mainRow, session.getUser(), id);
                if (id == -1L) {
                    result = this.result(id, ResultCode.FAIL, "实体仓名称或商品条码不能为空！");
                } else {
                    result = this.result(id, ResultCode.SUCCESS, "success");
                }
            }
        }
        return result;
    }

    /**
     * 保存
     *
     * @param mainRow
     * @param user
     * @param id
     * @return
     */
    private Long save(JSONObject mainRow, User user, Long id) {
        Long resultId = -1L;
        if (mainRow != null) {
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            long currentUserId = user.getId().longValue();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("ActualLackProcessingOrdersService:saveData", id));
            }
            if (null != id && id.doubleValue() == -1) {
                OcBDeficiency ocBDeficiency = new OcBDeficiency();
                Long warehouseId = mainRow.getLong("CP_C_PHY_WAREHOUSE_ID");
                Integer skuId = mainRow.getInteger("PS_C_SKU_ID");
                //实体仓名称或商品条码不能为空判断
                if (null == warehouseId || null == skuId) {
                    return resultId;
                }
                ocBDeficiency.setCpCPhyWarehouseId(warehouseId);
                ocBDeficiency.setBillNo(this.buildBillNo());
                CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(warehouseId);
                if (cpCPhyWarehouse != null) {
                    ocBDeficiency.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                    ocBDeficiency.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                }
                List<SkuQueryListRequest> skuQueryListRequestList = psRpcService.querySkuListByIds(Lists.newArrayList(skuId));
                if (!CollectionUtils.isEmpty(skuQueryListRequestList)) {
                    SkuQueryListRequest skuQueryListRequest = skuQueryListRequestList.get(0);
                    ocBDeficiency.setPsCSkuEcode(skuQueryListRequest.getEcode());
                    ocBDeficiency.setPsCSkuId(skuId.longValue());
                }
                String remark = mainRow.getString("REMARK");
                ocBDeficiency.setRemark(remark);
                id = ModelUtil.getSequence(SystemTableNames.OC_B_DEFICIENCY_TABLE_NAME);
                ocBDeficiency.setId(id);
                ocBDeficiency.setOwnerid(currentUserId);
                ocBDeficiency.setOwnerename(user.getEname());
                ocBDeficiency.setOwnername(user.getName());
                ocBDeficiency.setModifierename(user.getEname());
                ocBDeficiency.setModifierid(user.getId().longValue());
                ocBDeficiency.setModifiername(user.getName());
                ocBDeficiency.setAdOrgId(Long.valueOf(user.getOrgId()));
                ocBDeficiency.setCreationdate(timestamp);
                ocBDeficiency.setModifieddate(timestamp);
                ocBDeficiency.setAdClientId(Long.valueOf(user.getClientId()));
                ocBDeficiencyMapper.insert(ocBDeficiency);
                //推送es保存主表
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("ActualLackProcessingOrdersService:saveData进入方法保存成功:{}", id), JSON.toJSONString(ocBDeficiency));
                }
            }
        }
        return id;
    }

    /**
     * 删除信息
     *
     * @param session
     * @return
     * @throws NDSException
     */
    @Transactional
    public ValueHolder delData(QuerySession session) throws NDSException {
        ValueHolder result;
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Long id = param.getLong(OBJ_ID);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ActualLackProcessingOrdersService:delData", id));
        }
        try {
            if (id != null && id > 0) {
                OcBDeficiency ocBDeficiency = ocBDeficiencyMapper.selectById(id);
                if (ocBDeficiency != null && 0 == ocBDeficiency.getCheckStatus()) {
                    //删除操作
                    ocBDeficiencyMapper.deleteById(id);
                    //es操作
                    // this.updateESData(ocBDeficiency.getId(), null, null, OperateType.DEL_MAININFO);
                    result = this.result(id, ResultCode.SUCCESS, "保存成功！");
                } else {
                    result = this.result(id, ResultCode.FAIL, "已审核的数据不能进行删除！");
                }
            } else {
                result = this.result(id, ResultCode.FAIL, "当前记录已不存在！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result = this.result(id, ResultCode.FAIL, "操作异常");
        }
        return result;
    }

    /**
     * 标记
     *
     * @param obj
     * @param user
     * @return
     */
    @Transactional
    public ValueHolderV14 mark(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        JSONArray idArray = obj.getJSONArray("IDS");
        //转成数组
        if (CollectionUtils.isEmpty(idArray)) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("请选择需要标记的记录！");
        } else {
            log.info(LogUtil.format("标记已处理实缺处理单，参数：{}"), JSON.toJSONString(obj));
            for (Object object : idArray) {
                String id = object.toString();
                OcBDeficiencyItem item = ocBDeficiencyItemMapper.selectById(new Long(id));
                if (null != item && (null == item.getFlag() || item.getFlag() == 0)) {
                    OcBDeficiencyItem updateData = new OcBDeficiencyItem();
                    updateData.setId(item.getId());
                    updateData.setFlag(1);
                    updateData.setModifierename(user.getEname());
                    updateData.setModifiername(user.getName());
                    updateData.setModifierid(Long.valueOf(user.getId()));
                    updateData.setModifieddate(new Date());
                    ocBDeficiencyItemMapper.updateById(updateData);
                }
            }
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("操作成功！");
        }
        return vh;
    }

    /**
     * 批量审核
     *
     * @param obj
     * @param user
     * @return
     */
    @Transactional
    public ValueHolder batchAuditing(JSONObject obj, User user) {
        ValueHolder vh;
        if (null == obj) {
            vh = this.result(-1L, ResultCode.FAIL, "请勾选需要审核的数据！");
            return vh;
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (!CollectionUtils.isEmpty(ids)) {
            Boolean errorFlag = Boolean.FALSE;
            StringBuffer message = new StringBuffer();
            for (Object id : ids) {
                Long mainId = Long.valueOf(id.toString());
                JSONObject jsonObject = this.auditingCz(mainId, user);
                Integer code = jsonObject.getInteger("code");
                if (code != null && code == -1) {
                    errorFlag = Boolean.TRUE;
                    message.append(jsonObject.get("message")).append(";");
                }
            }
            if (errorFlag) {
                vh = this.result(-1L, ResultCode.SUCCESS, message.toString());
            } else {
                vh = this.result(-1L, ResultCode.SUCCESS, "操作成功！");
            }
        } else {
            vh = this.result(-1L, ResultCode.FAIL, "请勾选需要审核的数据！");
        }
        return vh;
    }

    /**
     * 单个审核逻辑处理
     *
     * @param obj
     * @param user
     * @return
     */
    @Transactional
    public ValueHolder auditing(JSONObject obj, User user) {
        ValueHolder vh;
        if (null == obj) {
            vh = this.result(-1L, ResultCode.FAIL, "请求参数不能为空！");
        } else {
            Long id = obj.getLong(OBJ_ID);
            JSONObject jsonObject = this.auditingCz(id, user);
            Integer code = jsonObject.getInteger("code");
            if (null != code && code == 1) {
                vh = this.result(-id, ResultCode.SUCCESS, "操作成功！");
            } else {
                vh = this.result(-id, ResultCode.FAIL, jsonObject.getString("message"));
            }
        }
        return vh;
    }

    /**
     * 审核操作处理
     *
     * @param id
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JSONObject auditingCz(Long id, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("审核方法开始执行", id));
        }
        JSONObject result = new JSONObject();
        // 获取实缺ID
        OcBDeficiency ocBDeficiency = ocBDeficiencyMapper.selectById(id);
        if (ocBDeficiency != null && null != ocBDeficiency.getCpCPhyWarehouseId()) {

            if (ocBDeficiency.getCheckStatus().equals(1)) {
                result.put("code", -1);
                result.put("message", "店仓：" + ocBDeficiency.getCpCPhyWarehouseEname() + "实缺单已被审核，不能重复审核！");
                return result;
            }

            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(ocBDeficiency.getCpCPhyWarehouseId());
            //1.判断是否存在实缺逻辑仓
            Long cpCStoreIdLostId = cpCPhyWarehouse.getCpCStoreIdLost();
            if (null == cpCStoreIdLostId) {
                result.put("code", -1);
                result.put("message", "请在实体仓下增加实缺逻辑仓！");
                return result;
            }
            CpStore cpStore = cpRpcService.selectCpCStoreById(cpCStoreIdLostId);
            if (null == cpStore) {
                result.put("code", -1);
                result.put("message", "逻辑仓信息不存在！");
                return result;
            }
            Long deficiencyId = ocBDeficiency.getId();
            Long warehouseId = ocBDeficiency.getCpCPhyWarehouseId();
            //2.查询实体仓下所有逻辑仓ids
            List<Long> idslistALL = this.getLjcIds(warehouseId);
            //去除实缺逻辑仓
            List<Long> idslist = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(idslistALL)) {
                for (Long aLong : idslistALL) {
                    if (aLong.doubleValue() != cpCStoreIdLostId.doubleValue()) {
                        idslist.add(aLong);
                    }
                }
            }
            Long skuId = ocBDeficiency.getPsCSkuId();
            String skuCode = ocBDeficiency.getPsCSkuEcode();
            //3.计算可调拨数量
            //3.1 查询所有占单量信息
            List<OcBDeficiencyItem> items = Lists.newArrayList();
            Map<String, JSONObject> qbMap = Maps.newHashMap();
            //接受返回的原始订单id
            List<OcBOrder> ocBOrderList = this.queryOccupyAllOrder(deficiencyId, idslist, skuId, items, qbMap, user);

            //3.2.查询条码所在实体仓下的所有逻辑仓的未发货的渠道订单
            //逻辑仓剩余可用的库存,组装可调拨的对象结合
            List<OcBDeficiencyTransfer> transfers = Lists.newArrayList();
            BigDecimal totalQty;
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("计算占单减去和wms回撤失败:qbMap:{}", id), JSON.toJSONString(qbMap));
            }
            totalQty = this.queryRemainingStock(deficiencyId, cpStore, idslist, skuCode, user, qbMap, transfers);
            if (!CollectionUtils.isEmpty(transfers)) {
                ocBDeficiencyTransferMapper.batchInsert(transfers);
            }

            //4.查询不能处理的订单
            //批量保存业务单据
            if (!CollectionUtils.isEmpty(items)) {
                ocBDeficiencyItemMapper.batchInsert(items);
            }

            //5.创建调拨单
            this.createDbd(id, user);

            //6.更新主表审核信息
            ocBDeficiency = new OcBDeficiency();
            ocBDeficiency.setId(deficiencyId);
            ocBDeficiency.setCheckId(user.getId() + 0L);
            ocBDeficiency.setCheckName(user.getName());
            ocBDeficiency.setCheckEname(user.getEname());
            //审核通过
            ocBDeficiency.setCheckStatus(1);
            ocBDeficiency.setChecktime(new Date());
            //可调拨数量
            ocBDeficiency.setTotQty(totalQty);
            ocBDeficiency.setAdClientId(Long.valueOf(user.getClientId()));
            ocBDeficiency.setAdOrgId(Long.valueOf(user.getOrgId()));
            ocBDeficiency.setModifierename(user.getEname());
            ocBDeficiency.setModifieddate(new Date());
            ocBDeficiency.setModifiername(user.getName());
            ocBDeficiency.setModifierid(user.getId().longValue());
            ocBDeficiencyMapper.updateById(ocBDeficiency);

            //7.审核成功之后对所有原单做释放库存的操作
            ValueHolderV14 v14 = ApplicationContextHandle.getBean(ActualLackProcessingOrdersService.class).releaseInventorySf(ocBOrderList, user);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("实缺订单调用释放库存并重新占单返回结果:{}", id), v14.toJSONObject());
            }
            if (v14.getCode() == ResultCode.FAIL) {
                result.put("code", -1);
                result.put("message", "实缺订单释放库存并重新占单服务执行异常--》" + v14.getMessage());
            }
            result.put("code", 1);
            result.put("message", "审核成功");
        } else {
            result.put("code", -1);
            result.put("message", "未找到仓店信息");
        }
        return result;
    }

    /**
     * 释放库存并重新占单
     *
     * @param ocBOrderList 实缺订单
     * @param user         用户名称
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 releaseInventory(List<OcBOrder> ocBOrderList, User user) {

        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            //订单集合对象组装好，用于传到库存中心进行重新占单
            List<OcBOrderRelation> ocBOrderRelationList = new ArrayList<>();
            for (OcBOrder ocBOrder : ocBOrderList) {
                //判断对象是否为空
                if (ocBOrder != null) {
                    //排除已审核或者配货中的单据
                    if (!OmsOrderStatus.CHECKED.toInteger().equals(ocBOrder.getOrderStatus()) || !OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(ocBOrder.getOrderStatus())) {
                        OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(ocBOrder.getId());
                        if (orderRelation != null) {
                            ocBOrderRelationList.add(orderRelation);
                        }
                    } else {
                        //审核状态的单据调用反审核服务
                        if (ocBOrderTheAuditService.updateOrderInfo(user, v14, ocBOrder.getId(), LogTypeEnum.NOT_CAPTURED_SCENE.getType())) {
                            OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(ocBOrder.getId());
                            if (orderRelation != null) {
                                ocBOrderRelationList.add(orderRelation);
                            }
                        } else {
                            log.error(LogUtil.format("实缺订单orderId{}调用反审核服务失败", ocBOrder.getId()), ocBOrder.getId());
                        }
                    }
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("实缺订单统计所有需重新占单订单条数size-->{}", ocBOrderRelationList.size());
            }
//            SgSendVoidSaveRequest request = new SgSendVoidSaveRequest();
//            List<SgSendBillVoidRequest> voidList = addAndVoidStockListService.getVoidList(ocBOrderList);
//            List<SgSendSaveWithPriorityRequest> addList = addAndVoidStockListService.getAddPriorityRequests(ocBOrderRelationList, user, true);
//            request.setVoidRequests(voidList);
//            request.setSaveWithPriorityRequests(addList);
//            request.setLoginUser(user);
//            if (log.isDebugEnabled()) {
//                log.debug("实缺订单调用批量作废批量新增逻辑发货单接口入参:{}", JSON.toJSONString(request));
//            }
            ValueHolderV14<List> result = null;//sgRpcService.voidAndAddStick(request);
            if (log.isDebugEnabled()) {
                log.debug("实缺订单调用批量作废批量新增逻辑发货单接口返回结果:" + JSON.toJSONString(result));
            }
            if (result.getCode() == ResultCode.FAIL) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("实缺订单调用逻辑发货单后并重新占单接口SgSendVoidSaveCmd.voidSaveSgBSend:异常信息！" + result.getMessage());
            } else {
//                List<SgSendSaveWithPriorityResult> resultList = result.getData();
//                log.debug("实缺订单调用批量新增逻辑发货单服务出参 resultList" + result.getData());
//                for (SgSendSaveWithPriorityResult sgSendSaveWithPriorityResult : resultList) {
//                    if (sgSendSaveWithPriorityResult.getPreoutResult() == 0) {
//                        log.debug("实缺订单订单OrderId" + sgSendSaveWithPriorityResult.getSourceBillId() + "订单sgSendSaveWithPriorityResult.getSourceBillId()" + "实缺订单满足库存");
//                        omsOrderLogService.addUserOrderLog(sgSendSaveWithPriorityResult.getSourceBillId(), sgSendSaveWithPriorityResult.getSourceBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), "实缺订单占用库存成功!", "", "", user);
//                        omsOrderManualSplitService.pushEs(sgSendSaveWithPriorityResult.getSourceBillId(), OmsOrderStatus.UNCONFIRMED.toInteger());
//                    } else {
//                        log.debug("实缺订单OrderId" + sgSendSaveWithPriorityResult.getSourceBillId() + "订单sgSendSaveWithPriorityResult.getSourceBillId()" + "缺货");
//                        //然后更新明细缺货数量
//                        Map<Long, BigDecimal> stringListMap = new HashMap<>();
//                        List<SgStoreWithPrioritySearchItemResult> sgStoreWithPrioritySearchItemResults = sgSendSaveWithPriorityResult.getOutStockItemList();
//                        log.debug("实缺订单OrderId" + sgSendSaveWithPriorityResult.getSourceBillId() + "订单缺货明细集合返回result" + sgStoreWithPrioritySearchItemResults);
//                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(sgStoreWithPrioritySearchItemResults)) {
//                            for (SgStoreWithPrioritySearchItemResult searchItemResult : sgStoreWithPrioritySearchItemResults) {
//                                BigDecimal outStock = (searchItemResult.getQtyOutOfStock() == null ? BigDecimal.ZERO : searchItemResult.getQtyOutOfStock());
//                                //避免出现不同明细相同sku,若出现,计算累加和
//                                if (stringListMap.containsKey(searchItemResult.getPsCSkuId())) {
//                                    stringListMap.put(searchItemResult.getPsCSkuId(), outStock.add(stringListMap.get(searchItemResult.getPsCSkuId())));
//                                } else {
//                                    stringListMap.put(searchItemResult.getPsCSkuId(), outStock);
//                                }
//                            }
//                        } else {
//                            log.debug("实缺订单OrderId" + sgSendSaveWithPriorityResult.getSourceBillId() + "订单缺货明细集合未返回~~~");
//                        }
//                        log.debug("实缺订单OrderId" + sgSendSaveWithPriorityResult.getSourceBillId() + "stringListMap聚合打印reuslt" + stringListMap);
//                        List<OcBOrderItem> orderItemList = omsOrderItemService.selectUnSuccessRefund(sgSendSaveWithPriorityResult.getSourceBillId());
//                        for (OcBOrderItem orderItem : orderItemList) {
//                            if (stringListMap.containsKey(orderItem.getPsCSkuId())) {
//                                log.debug("实缺订单OrderId" + sgSendSaveWithPriorityResult.getSourceBillId() + "明细sku" + orderItem.getPsCSkuId() + "缺货数量" + stringListMap.get(orderItem.getPsCSkuId()));
//                                orderItem.setQtyLost(stringListMap.get(orderItem.getPsCSkuId()));
//                                //更新明细表
//                                omsOrderItemService.updateOcBOrderItem(orderItem, sgSendSaveWithPriorityResult.getSourceBillId());
//                            }
//                        }
//                        omsOrderLogService.addUserOrderLog(sgSendSaveWithPriorityResult.getSourceBillId(), sgSendSaveWithPriorityResult.getSourceBillNo(), OrderLogTypeEnum.LESS_STOCK_OCCUPY_SUCCESS.getKey(), "实缺订单占用库存缺货!", "", "", user);
//                        omsOrderManualSplitService.pushEs(sgSendSaveWithPriorityResult.getSourceBillId(), OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
//                    }
//                }
//                v14.setCode(ResultCode.SUCCESS);
//                v14.setMessage("实缺订单调用逻辑发货单后并重新占单接口执行成功！");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("实缺订单调用逻辑发货单后并重新占单接口SgSendVoidSaveCmd.voidSaveSgBSend:异常信息:{}"), Throwables.getStackTraceAsString(ex));
            throw ex;
        }
        return v14;
    }

    /**
     * 释放库存并重新占单
     *
     * @param ocBOrderList 实缺订单
     * @param user         用户名称
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 releaseInventorySf(List<OcBOrder> ocBOrderList, User user) {
        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            //订单集合对象组装好，用于传到库存中心进行重新占单
            List<OcBOrderRelation> ocBOrderRelationList = new ArrayList<>();
            for (OcBOrder ocBOrder : ocBOrderList) {
                //判断对象是否为空
                if (ocBOrder != null) {
                    //排除已审核或者配货中的单据
                    if (!OmsOrderStatus.CHECKED.toInteger().equals(ocBOrder.getOrderStatus())
                            || !OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(ocBOrder.getOrderStatus())) {
                        OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(ocBOrder.getId());
                        if (orderRelation != null) {
                            ocBOrderRelationList.add(orderRelation);
                        }
                    } else {
                        invokeDeAudit(user, v14, ocBOrderRelationList, ocBOrder);
                    }
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("实缺订单统计所有需重新占单订单条数size-->" + ocBOrderRelationList.size());
            }
            //SgSendVoidSaveRequest request = new SgSendVoidSaveRequest();
            List voidList = addAndVoidStockListService.getVoidList(ocBOrderList);
//            if (log.isDebugEnabled()) {
//                log.debug("实缺订单调用批量作废批量新增逻辑发货单接口入参:" + JSON.toJSONString(request));
//            }

            if (CollectionUtils.isEmpty(voidList)) {
                log.debug("实缺订单调用批量释放库存为空");
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage("实缺订单调用批量释放库存为空！");
                return v14;
            } else {
//                //添加标记实缺
//                for (SgSendBillVoidRequest sgSendBillVoidRequest : voidList) {
//                    sgSendBillVoidRequest.setIsDeficiency(Boolean.TRUE);
//                }
            }
            //批量释放库存
            ValueHolderV14 result = null;//sgRpcService.batchVoidSgBSend(voidList, user);
            if (log.isDebugEnabled()) {
                log.debug("实缺订单调用批量作废批量新增逻辑发货单接口返回结果:" + JSON.toJSONString(result));
            }
            if (result.getCode() == ResultCode.FAIL) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("实缺订单调用逻辑发货单后并重新占单接口SgSendVoidSaveCmd.voidSaveSgBSend:异常信息！" + result.getMessage());
            } else {
//                SgSendBatchVoidResult resultList = result.getData();
//                List<SgSendVoidResult> sgSendVoidResults = resultList.getVoidResults();
//                log.debug("实缺订单调用批量新增逻辑发货单服务出参 resultList" + result.getData());
//                for (SgSendVoidResult sgSendSaveWithPriorityResult : sgSendVoidResults) {
//                    Long orderId = sgSendSaveWithPriorityResult.getSourceBillId();
//                    log.debug("实缺订单OrderId" + orderId + "订单sgSendSaveWithPriorityResult.getSourceBillId()" + "缺货");
//                    OcBOrder ocBOrder = omsOrderService.selectUnConfirmedOrderInfo(orderId);
//                    if (ocBOrder != null && OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
//                        List<OcBOrderItem> orderItemList = omsOrderItemService.selectUnSuccessRefund(sgSendSaveWithPriorityResult.getSourceBillId());
//                        for (OcBOrderItem orderItem : orderItemList) {
//                            OcBOrderItem updateItem = new OcBOrderItem();
//                            updateItem.setId(orderItem.getId());
//                            updateItem.setOcBOrderId(sgSendSaveWithPriorityResult.getSourceBillId());
//                            updateItem.setQtyLost(orderItem.getQty());
//                            omsOrderItemService.updateOcBOrderItem(updateItem, sgSendSaveWithPriorityResult.getSourceBillId());
//                        }
//                        omsOrderLogService.addUserOrderLog(sgSendSaveWithPriorityResult.getSourceBillId(), sgSendSaveWithPriorityResult.getSourceBillId() + "", OrderLogTypeEnum.RELEASE_STOCK_SUCCESS.getKey(), "实缺订单释放库存!", "", "", user);
//                        //加入占单表
//                        againOccupyStockService.updateTobeConfirmed(ocBOrder.getId());
//                        omsOrderManualSplitService.pushEs(sgSendSaveWithPriorityResult.getSourceBillId(), OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
//                    }
//                }
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage("实缺订单调用释放库存接口执行成功！");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("实缺订单调用逻辑发货单释放库存接口SgSendVoidSaveCmd.releaseInventorySf:异常信息:{}"), Throwables.getStackTraceAsString(ex));
            throw ex;
        }
        return v14;
    }

    /**
     * @param user                 User
     * @param v14                  调用结果
     * @param ocBOrderRelationList 订单关系model
     * @param ocBOrder             零售发货单
     */
    private void invokeDeAudit(User user, ValueHolderV14 v14, List<OcBOrderRelation> ocBOrderRelationList, OcBOrder ocBOrder) {
        try {
            //审核状态的单据调用反审核服务
            if (ocBOrderTheAuditService.updateOrderInfo(user, v14, ocBOrder.getId(), LogTypeEnum.NOT_CAPTURED_SCENE.getType())) {
                OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(ocBOrder.getId());
                if (orderRelation != null) {
                    ocBOrderRelationList.add(orderRelation);
                }
            } else {
                log.error(LogUtil.format("调用反审核服务失败",ocBOrder.getId()));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("调用反审核服务失败.异常:{}", ocBOrder.getId()), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 查询可用库存
     *
     * @param deficiencyId
     * @param cpStore
     * @param idslist
     * @param skuEcode
     * @param user
     * @param qbMap
     * @param transfers
     */
    private BigDecimal queryRemainingStock(Long deficiencyId, CpStore cpStore, List<Long> idslist, String skuEcode, User user, Map<String, JSONObject> qbMap, List<OcBDeficiencyTransfer> transfers) {
        BigDecimal totalQty = BigDecimal.ZERO;
        SgStorageQueryRequest sgStorageQueryRequest = new SgStorageQueryRequest();
        sgStorageQueryRequest.setStoreIds(idslist);
        sgStorageQueryRequest.setSkuEcodes(Lists.newArrayList(skuEcode));
        ValueHolderV14<List<SgBStorage>> v14 = sgRpcService.queryStorage(sgStorageQueryRequest, user);
        if (v14 != null) {
            Map<Long, JSONObject> ljcMap = Maps.newHashMap();
            List<SgBStorage> sgBStorages = v14.getData();
            OcBDeficiencyTransfer transfer = null;
            Map<String, JSONObject> sykcMap = Maps.newHashMap();
            for (SgBStorage sgBStorage : sgBStorages) {
                if (transfer == null) {
                    transfer = new OcBDeficiencyTransfer();
                    transfer.setOcBDeficiencyId(deficiencyId);
                    transfer.setPsCSkuId(sgBStorage.getPsCSkuId());
                    transfer.setPsCSkuEcode(sgBStorage.getPsCSkuEcode());

                    //入库操作 实缺仓库
                    transfer.setCpCStoreId(cpStore.getId());
                    transfer.setCpCStoreEcode(cpStore.getEcode());
                    transfer.setCpCStoreEname(cpStore.getEname());

                    transfer.setPsCProId(sgBStorage.getPsCProId());
                    transfer.setPsCProEcode(sgBStorage.getPsCProEcode());
                    transfer.setPsCProEname(sgBStorage.getPsCProEname());
                    transfer.setPsCClrId(sgBStorage.getPsCSpec1Id());
                    transfer.setPsCClrEcode(sgBStorage.getPsCSpec1Ecode());
                    transfer.setPsCClrEname(sgBStorage.getPsCSpec1Ename());
                    transfer.setPsCSizeEcode(sgBStorage.getPsCSpec2Ecode());
                    transfer.setPsCSizeEname(sgBStorage.getPsCSpec2Ename());
                    transfer.setPsCSizeId(sgBStorage.getPsCSpec2Id());
                    transfer.setPriceList(sgBStorage.getPriceList());
                    transfer.setAdClientId(Long.valueOf(user.getClientId()));
                    transfer.setAdOrgId(Long.valueOf(user.getOrgId()));
                    transfer.setModifierename(user.getEname());
                    transfer.setModifieddate(new Date());
                    transfer.setModifiername(user.getName());
                    transfer.setModifierid(user.getId().longValue());
                }

                JSONObject storeInfo = new JSONObject();
                storeInfo.put("CpCStoreIdOut", sgBStorage.getCpCStoreId());
                storeInfo.put("CpCStoreEcodeOut", sgBStorage.getCpCStoreEcode());
                storeInfo.put("CpCStoreEnameOut", sgBStorage.getCpCStoreEname());
                ljcMap.put(sgBStorage.getCpCStoreId(), storeInfo);
                //调出逻辑仓id
                transfer.setCpCStoreIdOut(sgBStorage.getCpCStoreId());
                transfer.setCpCStoreEcodeOut(sgBStorage.getCpCStoreEcode());
                transfer.setCpCStoreEnameOut(sgBStorage.getCpCStoreEname());

                String key = String.format("%d,%s", sgBStorage.getCpCStoreId(), sgBStorage.getPsCSkuEcode());
                //可用数量
                BigDecimal qtySl = BigDecimal.ZERO;
                if (null != qbMap.get(key)) {
                    qtySl = qbMap.get(key).getBigDecimal("QTY_SL");
                }

                JSONObject kyJson = new JSONObject();
                if (null == sykcMap.get(key)) {
                    if (qtySl != null && qtySl.compareTo(BigDecimal.ZERO) > 0) {
                        //可调拨数量+未传入wms的商品数
                        BigDecimal dbsl = qtySl.add(sgBStorage.getQtyAvailable());
                        kyJson.put("DBSL", dbsl);
                        //合并同类项
                        sykcMap.put(key, kyJson);
                    } else {
                        //可调拨数量+未传入wms的商品数
                        kyJson.put("DBSL", sgBStorage.getQtyAvailable());
                        //合并同类项
                        sykcMap.put(key, kyJson);
                    }
                } else {
                    BigDecimal ljBig = BigDecimal.ZERO;
                    if (null != sykcMap.get(key)) {
                        ljBig = sykcMap.get(key).getBigDecimal("DBSL");
                    }
                    kyJson.put("DBSL", sgBStorage.getQtyAvailable().add(ljBig).add(qtySl));
                    //合并同类项
                    sykcMap.put(key, kyJson);
                }
                qbMap.remove(key);
            }

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("计算占单合并前.sykcMap: {}"), JSON.toJSONString(sykcMap));
            }
            //将没有可用库存且有未传wms的数据添加到可调拨数据中
            if (!CollectionUtils.isEmpty(qbMap)) {
                for (Map.Entry<String, JSONObject> entry : qbMap.entrySet()) {
                    JSONObject jsonObject = entry.getValue();
                    String key = entry.getKey();
                    if (sykcMap.get(key) == null) {
                        jsonObject.put("DBSL", jsonObject.getBigDecimal("QTY_SL"));
                        sykcMap.put(key, jsonObject);
                    }
                }
            }

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("计算占单合并后.sykcMap: {}"), JSON.toJSONString(sykcMap));
            }
            String transferStr = JSON.toJSONString(transfer, SerializerFeature.DisableCircularReferenceDetect);
            for (Map.Entry<String, JSONObject> entry : sykcMap.entrySet()) {
                String[] key = entry.getKey().split(",");
                Long cpCStoreId = new Long(key[0]);
                String psCSkuEcode = key[1];
                JSONObject jsonObject = entry.getValue();
                BigDecimal dbsl = jsonObject.getBigDecimal("DBSL");
                //调拨数量为零不能调拨
                if (null == dbsl || dbsl.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                OcBDeficiencyTransfer tempTransfer = JSON.parseObject(transferStr, OcBDeficiencyTransfer.class);
                tempTransfer.setQty(dbsl);
                tempTransfer.setCpCStoreIdOut(cpCStoreId);
                String ename = (String) ljcMap.get(cpCStoreId).get("CpCStoreEnameOut");
                String ecode = (String) ljcMap.get(cpCStoreId).get("CpCStoreEcodeOut");
                tempTransfer.setCpCStoreEcodeOut(ecode);
                tempTransfer.setCpCStoreEnameOut(ename);
                tempTransfer.setPsCSkuEcode(psCSkuEcode);
                long ID = ModelUtil.getSequence(SystemTableNames.OC_B_DEFICIENCY_TRANSFER_TABLE_NAME);
                tempTransfer.setId(ID);
                transfers.add(tempTransfer);
                totalQty = totalQty.add(dbsl);
            }
        }
        return totalQty;
    }

    /**
     * 查询所有占单数据
     *
     * @return
     */
    private List<OcBOrder> queryOccupyAllOrder(Long deficiencyId, List<Long> idslist, Long skuId, List<OcBDeficiencyItem> items, Map<String, JSONObject> qbMap, User user) {

        List<OcBOrder> ocBOrderList = new ArrayList<>();
//        SgSendBillQueryRequest sendBillQueryRequest = new SgSendBillQueryRequest();
//        //逻辑仓id集合
//        sendBillQueryRequest.setStoreIds(idslist);
//        sendBillQueryRequest.setSkuIds(Lists.newArrayList(skuId));
//        sendBillQueryRequest.setBillStatus(Lists.newArrayList(SgSendConstantsIF.BILL_SEND_STATUS_DEAL,
//                SgSendConstantsIF.BILL_SEND_STATUS_UPDATE));
//        //渠道订单 唯品会JIT配货单 调拨单 采购退货出库单 销售单
//        sendBillQueryRequest.setSourceBillTypes(Lists.newArrayList(SgConstantsIF.BILL_TYPE_RETAIL,
//                SgConstantsIF.BILL_TYPE_VIPSHOP, SgConstantsIF.BILL_TYPE_TRANSFER,
//                SgConstantsIF.BILL_TYPE_PUR_REF, SgConstantsIF.BILL_TYPE_SALE));
//        ValueHolderV14<SgSendActualLackQueryResult> valueHolderV14 = sgRpcService.querySgSend(sendBillQueryRequest);
//        SgSendActualLackQueryResult sgSendBillQueryResult = valueHolderV14.getData();
//        Map<SgBSend, HashMap<String, SgBSendItem>> map = sgSendBillQueryResult.getResults();
//        for (Map.Entry<SgBSend, HashMap<String, SgBSendItem>> entry : map.entrySet()) {
//            SgBSend mapKey = entry.getKey();
//            Map<String, SgBSendItem> mapValue = entry.getValue();
//            //单号
//            String billNo = mapKey.getSourceBillNo();
//            //状态
//            Integer state = mapKey.getBillStatus();
//            //平台编号
//            String sourcecode = mapKey.getSourcecode();
//            for (Long idLong : idslist) {
//                SgBSendItem sgBSendItem = mapValue.get(idLong + "," + skuId);
//                if (null != sgBSendItem) {
//                    //封装相关业务单据
//                    OcBDeficiencyItem tempItem = new OcBDeficiencyItem();
//                    tempItem.setBillNo(billNo);
//                    tempItem.setBillStatus(state);
//                    tempItem.setSourceCode(sourcecode);
//                    //占用量
//                    BigDecimal qtyPreout = sgBSendItem.getQtyPreout();
//                    tempItem.setQty(qtyPreout);
//                    //发货量
//                    BigDecimal qty = sgBSendItem.getQty();
//                    tempItem.setTotQty(qty);
//                    //单据类型
//                    Integer sourceBillType = mapKey.getSourceBillType();
//                    tempItem.setBillType(sourceBillType);
//                    tempItem.setOcBDeficiencyId(deficiencyId);
//                    //如果是全渠道订单
//                    if (sourceBillType == SgConstantsIF.BILL_TYPE_RETAIL) {
//                        //qtySl = 已经撤回的+未发货的
//                        BigDecimal qtySl = BigDecimal.ZERO;
//                        OcBOrder ocBOrder = ocBOrderMapper.selectByID(mapKey.getSourceBillId());
//                        if (null == ocBOrder) {
//                            continue;
//                        }
//                        //已配货的订单wms撤回 1.已经撤回的+未发货的 +（可用的）= 要调拨的实缺逻辑仓的
//                        if (ocBOrder != null
//                                && ((OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(ocBOrder.getOrderStatus())
//                                && (null == ocBOrder.getWmsCancelStatus() || ocBOrder.getWmsCancelStatus() != 1))
//                                || (ocBOrder != null && OmsOrderStatus.CHECKED.toInteger().equals(ocBOrder.getOrderStatus())))) {
//                            //撤回wms直接换成反审核 liqb
//                            ValueHolderV14 v14 = new ValueHolderV14();
//                            try {
//                                Boolean result = ocBOrderTheAuditService.updateOrderInfo(user, v14, ocBOrder.getId(), Boolean.TRUE, LogTypeEnum.LACK_REVERSE_AUDIT.getType(),true);
//                                if (result) {
//                                    qtySl = qtySl.add(qtyPreout);
//                                    tempItem.setWmsCancelStatus(1);
//                                    //配货中且WMS撤回状态为撤回成功的单据，[标志：作废出库通知单成功的]
//                                    ocBOrderList.add(ocBOrder);
//                                }
//                            } catch (Exception e) {
//                                e.printStackTrace();
//                                log.error("实缺反审核失败,订单编号：" + ocBOrder.getId());
//                            }
//                        } else if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())
//                                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())) {
//                            //撤销成功+配货中的也需要计算
//                            qtySl = qtySl.add(qtyPreout);
//                            //【1,待审核】【2,缺货】【3,已审核】的单据
//                            ocBOrderList.add(ocBOrder);
//                        } else if (ocBOrder.getWmsCancelStatus() != null && ocBOrder.getWmsCancelStatus().equals(1)
//                                && OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(ocBOrder.getOrderStatus())) {
//                            //WMS撤回成功的单据
//                            ocBOrderList.add(ocBOrder);
//                            qtySl = qtySl.add(qtyPreout);
//                            tempItem.setWmsCancelStatus(1);
//                        } else if (OmsOrderStatus.PRE_SALE.toInteger().equals(ocBOrder.getOrderStatus())) {
//                            //预售状态的单据
//                            ocBOrderList.add(ocBOrder);
//                        }
//                        sgBSendItem.setQty(qtySl);
//
//                        JSONObject jsonObject = new JSONObject();
//                        String key = String.format("%d,%s", sgBSendItem.getCpCStoreId(), sgBSendItem.getPsCSkuEcode());
//                        //和并同类项：计算每个逻辑仓可调拨商品数量
//                        if (null != qbMap.get(key)) {
//                            qtySl = qtySl.add(qbMap.get(key).getBigDecimal("QTY_SL"));
//                            jsonObject.put("QTY_SL", qtySl);
//                            qbMap.put(key, jsonObject);
//                        } else {
//                            jsonObject.put("QTY_SL", qtySl);
//                            qbMap.put(key, jsonObject);
//                        }
//                    }
//                    //封装的相关占单的业务单据
//                    Long id = ModelUtil.getSequence(SystemTableNames.OC_B_DEFICIENCY_ITEM_TABLE_NAME);
//                    tempItem.setAdClientId(Long.valueOf(user.getClientId()));
//                    tempItem.setAdOrgId(Long.valueOf(user.getOrgId()));
//                    tempItem.setModifierename(user.getEname());
//                    tempItem.setModifieddate(new Date());
//                    tempItem.setModifiername(user.getName());
//                    tempItem.setModifierid(user.getId().longValue());
//                    tempItem.setId(id);
//                    items.add(tempItem);
//                }
//            }
//        }
        return ocBOrderList;
    }

    /**
     * 批量生成调拨单
     */
    private void createDbd(Long id, User user) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("oc_b_deficiency_id", id);
        List<OcBDeficiencyTransfer> ocBDeficiencyTransfer = ocBDeficiencyTransferMapper.selectByMap(params);
        if (!CollectionUtils.isEmpty(ocBDeficiencyTransfer)) {
            OcBDeficiency ocBDeficiency = ocBDeficiencyMapper.selectById(id);
            Date creationdate = ocBDeficiency.getCreationdate();
            String billNo = ocBDeficiency.getBillNo();
//            List<SgTransferBillSaveRequest> requests = Lists.newArrayList();
//            for (OcBDeficiencyTransfer bDeficiencyTransfer : ocBDeficiencyTransfer) {
//                SgTransferBillSaveRequest sgTransferBillSaveRequest = new SgTransferBillSaveRequest();
//                SgTransferSaveRequest sgTransferSaveRequest = new SgTransferSaveRequest();
//                sgTransferSaveRequest.setBillDate(creationdate);
//                sgTransferSaveRequest.setTransferType(SgTransferConstantsIF.TRANSFER_TYPE_DEFICIENCY);
//                sgTransferSaveRequest.setSendType(SgTransferConstantsIF.SEND_TYPE_ZT);
//                //发货仓ID
//                sgTransferSaveRequest.setCpCOrigId(bDeficiencyTransfer.getCpCStoreIdOut());
//                //收货仓ID
//                sgTransferSaveRequest.setCpCDestId(bDeficiencyTransfer.getCpCStoreId());
//                sgTransferSaveRequest.setSgBTransferPropId(SgTransferConstantsIF.TRANSFER_PROP_ZP);
//                sgTransferSaveRequest.setRemark(String.format("由实缺处理单【%s】审核生成。", billNo));
//                sgTransferBillSaveRequest.setTransfer(sgTransferSaveRequest);
//                List<SgTransferItemSaveRequest> sgTransferItemSaveRequests = Lists.newArrayList();
//                SgTransferItemSaveRequest sgTransferItemSaveRequest = new SgTransferItemSaveRequest();
//                sgTransferItemSaveRequest.setId(-1L);
//                sgTransferItemSaveRequest.setPsCProEcode(bDeficiencyTransfer.getPsCProEcode());
//                sgTransferItemSaveRequest.setPsCSkuEcode(bDeficiencyTransfer.getPsCSkuEcode());
//                sgTransferItemSaveRequest.setPsCSkuId(bDeficiencyTransfer.getPsCSkuId());
//                sgTransferItemSaveRequest.setQty(bDeficiencyTransfer.getQty());
//                sgTransferItemSaveRequests.add(sgTransferItemSaveRequest);
//                sgTransferBillSaveRequest.setItems(sgTransferItemSaveRequests);
//                sgTransferBillSaveRequest.setLoginUser(user);
//                requests.add(sgTransferBillSaveRequest);
//            }
//            sgRpcService.saveAndAuditBatch(requests);
       }
    }

    /**
     * 通过redis获取序号值
     *
     * @return
     */
    private Long getBillSequence() {
        String currentDayRedisKey = BllRedisKeyResources.getDeficiencyBillNoSequence();
        CusRedisTemplate<String, String> redisTemplate = RedisMasterUtils.getStrRedisTemplate();
        Boolean isExist = redisTemplate.hasKey(currentDayRedisKey);
        if (!isExist) {
            redisTemplate.opsForValue().set(currentDayRedisKey, "0", ONE_DAY_SECONDS, TimeUnit.SECONDS);
        }
        Long id = redisTemplate.opsForValue().increment(currentDayRedisKey, 1L);
        return id;
    }

    /**
     * 查询调拨单明细信息
     *
     * @param data
     * @param
     * @return
     */
    public ValueHolderV14 queryMx(JSONObject data) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        if (null != data.get("ID")) {
            String currentPageString = "currentPage";
            String pageSizeString = "pageSize";
            Long currentPage = data.getLong(currentPageString);
            Long pageSize = data.getLong(pageSizeString);
            Long id = new Long(data.get("ID").toString());
            if (id == -1) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("主键id不能为空！");
                return holderV14;
            }
            QueryWrapper<OcBDeficiencyItem> wrapper = new QueryWrapper<>();
            wrapper.eq("oc_b_deficiency_id", id);
            Page<OcBDeficiencyItem> page = new Page<>();
            if (currentPage != null) {
                page.setCurrent(currentPage);
            } else {
                page.setCurrent(0L);
            }
            if (pageSize != null) {
                page.setSize(pageSize);
            } else {
                page.setSize(10L);
            }
            IPage iPage = ocBDeficiencyItemMapper.selectPage(page, wrapper);
            List<OcBDeficiencyItem> records = iPage.getRecords();
            List<OcBDeficiencyItem> list;
            if (records != null && records.size() != 0) {
                list = new ArrayList<>();
                for (OcBDeficiencyItem ocBorderItemDto : records) {
                    list.add(ocBorderItemDto);
                }
                iPage.setRecords(list);
            }
            holderV14.setCode(ResultCode.SUCCESS);
            holderV14.setMessage("查询成功");
            holderV14.setData(iPage);
        } else {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("当前记录已不存在！");
        }
        return holderV14;
    }

    /**
     * 根据实仓id查询所有逻辑仓ids
     *
     * @param warehouseId
     * @return
     */
    private List<Long> getLjcIds(Long warehouseId) {
        HashMap<Long, List<CpCStore>> cpCStoreMap = new HashMap<Long, List<CpCStore>>();
        try {
            StoreInfoQueryRequest request = new StoreInfoQueryRequest();
            request.setPhyId(warehouseId);
            cpCStoreMap = basicCpQueryService.getStoreInfoByPhyId(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<Long> idslist = Lists.newArrayList();
        if (cpCStoreMap.isEmpty()) {
            return idslist;
        }
        List<CpCStore> cpCStores = cpCStoreMap.get(warehouseId);
        if (!CollectionUtils.isEmpty(cpCStores)) {
            for (CpCStore cpCStore : cpCStores) {
                //实缺只处理正品仓库
                if (null != cpCStore.getStoretype() && cpCStore.getStoretype().equals("01")) {
                    Long stocreId = cpCStore.getId();
                    idslist.add(stocreId);
                }
            }
        }
        return idslist;
    }

    /**
     * 统一返回
     *
     * @param id
     * @param resultCode
     * @param message
     * @return
     */
    private ValueHolder result(Long id, Integer resultCode, String message) {
        ValueHolder result = new ValueHolder();
        JSONObject data = new JSONObject();
        data.put(OBJ_ID, id);
        data.put("tablename", "OC_B_DEFICIENCY");
        result.put("data", data);
        result.put("code", resultCode);
        result.put("message", message);
        return result;
    }

    /**
     * 号码生成器
     *
     * @return
     */
    private String buildBillNo() {
        // 生成规则：序号生成器OM+年月日+8位流水
        long id = this.getBillSequence();
        String seq = String.format("%08d", id);
        String billNo = "PP" + Tools.dayFormatter.format(new Date()) + seq;
        return billNo;
    }


}
