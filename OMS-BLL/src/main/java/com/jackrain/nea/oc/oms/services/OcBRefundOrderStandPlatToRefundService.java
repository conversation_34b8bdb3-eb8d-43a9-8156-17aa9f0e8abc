package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
import com.jackrain.nea.hub.request.naika.NaiKaRefundStockInItemRequest;
import com.jackrain.nea.hub.request.naika.NaiKaRefundStockInRequest;
import com.jackrain.nea.ip.api.others.StandPlatRefundIntoWareHouseCmd;
import com.jackrain.nea.ip.model.others.StandPlatRefundIntoWareHouseModel;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefudStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

//import com.jackrain.nea.st.service.OmsOrderPolicyJingDongService;
//import com.jackrain.nea.st.service.OmsOrderStandPlatAutoRefundService;
//import com.jackrain.nea.utils.AssertUtils;

/**
 * @description: 通用平台自动退款
 * @author: 郑小龙
 * @date: 2020-06-04 17:25
 **/
@Component
@Slf4j
public class OcBRefundOrderStandPlatToRefundService {

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private ReturnOrderLogService logService;
    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;

    @Autowired
    private IpBStandplatRefundMapper ipBStandplatRefundMapper;
    @Autowired
    private IpBStandplatRefundItemMapper ipBStandplatRefundItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private CpRpcService cpRpcService;
    //    @Autowired
//    private OmsOrderPolicyJingDongService omsOrderPolicyJingDongService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsOrderStandPlatAutoRefundService omsOrderStandPlatAutoRefundService;

    @Reference(group = "ip", version = "1.4.0")
    private StandPlatRefundIntoWareHouseCmd standPlatRefundIntoWareHouseCmd;
    @Reference(group = "hub", version = "1.0")
    private NaiKaOrderCmd naiKaOrderCmd;

    @Autowired
    private OcBReturnAfSendMapper afSendMapper;


    public ValueHolderV14 refundOrderToRedund(String param, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("OcBRefundOrderStandPlatToRefundService.refundOrderToRedund.param:{}", JSON.toJSONString(param));
        }
        ValueHolderV14 holderV14 = new ValueHolderV14();
        JSONObject jsonObject = JSON.parseObject(param);
        if (jsonObject == null || jsonObject.size() == 0) {
            holderV14.setCode(-1);
            holderV14.setMessage("参数为空！");
            return holderV14;
        }
        //退换货Id
        Long id = jsonObject.getLong("ID");
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(id);
        List<OcBReturnOrderRefund> refundList = ocBReturnOrderRefundMapper.selectByOcOrderId(id);
        if (ocBReturnOrder == null) {
            holderV14.setCode(-1);
            holderV14.setMessage("退换货单已不存在！");
            return holderV14;
        }

        if (CollectionUtils.isEmpty(refundList)) {
            holderV14.setCode(-1);
            holderV14.setMessage("退换货单明细已不存在！");
            return holderV14;
        }
        //如果退换货单状态为完成状态，则进入下一步流程判断；如果为其他状态，则不进行处理
        Integer returnStatus = ocBReturnOrder.getReturnStatus();
        if (returnStatus == null || !(returnStatus.equals(ReturnStatusEnum.COMPLETION.getVal()))) {
            holderV14.setCode(-1);
            holderV14.setMessage("退单通用退款服务失败：退换货状态不匹配！");
            return holderV14;
        }

        //判断退货单平台类型如果为淘宝平台和京东的退单，进行下一步判断,否则不判断
        Integer platform = ocBReturnOrder.getPlatform();
        if (PlatFormEnum.JINGDONG.getCode().equals(platform) || PlatFormEnum.TAOBAO.getCode().equals(platform)) {
            holderV14.setCode(-1);
            holderV14.setMessage("退单通用退款服务失败：平台类型不匹配！");
            return holderV14;
        }

        /*单据类型,1退货单，2退换货单',
         *退单的类型，如果为退换货单，则不走SA程序，不进行处理，更新传SA状态为不传AG（3）
         *添加退换货订单操作日志中；如果类型为退货单时进行下一步判断
         */
        Integer billType = ocBReturnOrder.getBillType();
        if (billType == null || billType.equals(OcReturnBillTypeEnum.EXCHANGE.getVal())) {
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            returnOrder.setId(ocBReturnOrder.getId());
            returnOrder.setIsToag(AGStatusEnum.NOT.getVal());
            return updateRefundOrderRefundAndLog(returnOrder, loginUser, "单据类型不匹配！");
        }

        return platToRefund(ocBReturnOrder, refundList, loginUser);
    }


    /**
     * 通用平台退款
     *
     * @param ocBReturnOrder
     * @param refundList
     * @param loginUser
     * @return
     */
    private ValueHolderV14 platToRefund(OcBReturnOrder ocBReturnOrder, List<OcBReturnOrderRefund> refundList, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug("OcBRefundOrderStandPlatToRefundService.platToRefund.ocBReturnOrder.ID:{},refundList.size:{}",
                    ocBReturnOrder.getId(), refundList.size());
        }
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            /*
             * 判断退货单明细的平台退款单号是否为空 ，
             * 如果所有明细为空，则不走SA程序，不进行处理，更新传SA状态为不传SA
             * 添加退换货订单操作日志中；不为空进行下一步判断
             */
            Long id = ocBReturnOrder.getId();

            Integer platform = Optional.ofNullable(ocBReturnOrder.getPlatform()).orElse(0);
            // 是否是拼多多
            boolean isPinduoduo = PlatFormEnum.PINDUODUO.getCode().equals(platform);

            for (OcBReturnOrderRefund refund : refundList) {
                OcBReturnOrderRefund newRefund = new OcBReturnOrderRefund();
                newRefund.setId(refund.getId());
                newRefund.setPsCSkuEcode(refund.getPsCSkuEcode());
                returnOrderTransferUtil.updateOperator(newRefund, loginUser);

                // 平台退款单号, 替换字段  reserveVarchar01-> refund_bill_no
                String returnId = refund.getRefundBillNo();

                // 退换货单上单平台退款单号可能是空，去发货后退款单上拿
                if (StringUtils.isEmpty(returnId)) {
                    log.info("OcBRefundOrderStandPlatToRefundService 退换货单上的平台退款单号为空,去发货后退款单查找!");
                    List<OcBReturnAfSend> refunds = afSendMapper.listReturnAfSend5ReturnAudit(ocBReturnOrder.getTid());
                    if (CollectionUtils.isNotEmpty(refunds)) {
                        OcBReturnAfSend ocReturnAfSend = refunds.get(0);
                        returnId = ocReturnAfSend.getTReturnId();
                        refund.setRefundBillNo(returnId);
                        ocBReturnOrder.setReturnId(returnId);
                    }
                }


                //是否ag退款
                Integer isAg = refund.getIsToAg();
                if (null == isAg && !isPinduoduo) {
                    newRefund.setIsToAg(AGStatusEnum.FAIL.getVal());
                    updateRefundOrderRefundAndLog(id, newRefund, loginUser, "通用退款服务失败：自动退款状态异常！");
                    continue;
                }

                //赠品不参与退款
                if (StringUtils.isNotBlank(refund.getGiftType())) {
                    continue;
                }
                if (StringUtils.isNotEmpty(returnId)) {
                    //通用平台也需要传AG
                    if (isAg.equals(AGStatusEnum.INIT.getVal())
                            || isAg.equals(AGStatusEnum.FAIL.getVal())
                            // TODO 暂时传 为什么退换货单的is_to_AG全部都为不传？ 去掉
                            || isAg.equals(AGStatusEnum.NOT.getVal()) || isPinduoduo) {
                        QueryWrapper<IpBStandplatRefund> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("RETURN_NO", returnId);
                        IpBStandplatRefund standplatRefund = ipBStandplatRefundMapper.selectOne(queryWrapper);
                        if (null == standplatRefund) {
                            newRefund.setIsToAg(AGStatusEnum.NOT.getVal());
                            updateRefundOrderRefundAndLog(id, newRefund, loginUser, "通用退款单中间表不存在此退单！");
                            continue;
                        }
                        ValueHolderV14 v14 = agIncomingStock(ocBReturnOrder, loginUser, standplatRefund);
                        if (v14.isOK()) {
                            newRefund.setIsToAg(AGStatusEnum.SUCCESS.getVal());
                            updateRefundOrderRefundAndLog(id, newRefund, loginUser, "");
                            continue;
                        } else {
                            //如果通用退单状态为退款完成，则直接调用成功
                            if(IpBStandplatRefudStatusEnum.SUCCESS.getVal().equals(standplatRefund.getReturnStatus())){
                                newRefund.setIsToAg(AGStatusEnum.SUCCESS.getVal());
                                logService.addRefundOrderLog(id, "通用平台自动退款", "通用退单退款状态为退款完成，标记退款成功", loginUser);
                                updateRefundOrderRefundAndLog(id, newRefund, loginUser, "");
                            }else {
                                newRefund.setIsToAg(AGStatusEnum.FAIL.getVal());
                                updateRefundOrderRefundAndLog(id, newRefund, loginUser, v14.getMessage());
                            }
                            continue;
                        }
                    } else {
                        newRefund.setIsToAg(refund.getIsToAg());
                        updateRefundOrderRefundAndLog(id, newRefund, loginUser, "通用退款接口失败：明细退款状态不匹配！");
                        continue;
                    }
                } else {
                    // 拼多多不停止继续往下走
                    if (!isPinduoduo) {
                        newRefund.setIsToAg(AGStatusEnum.NOT.getVal());
                        updateRefundOrderRefundAndLog(id, newRefund, loginUser, "平台退款单号为空！");
                        continue;
                    }
                }
            }

            List<OcBReturnOrderRefund> returnOrderRefunds = ocBReturnOrderRefundMapper.selectByOcOrderIdNoGift(ocBReturnOrder.getId());

            //此实体仅用来更新
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            returnOrder.setId(ocBReturnOrder.getId());
            returnOrderTransferUtil.updateOperator(returnOrder, loginUser);

            // 已传数量
            int ALREADY_TO_AG = 0;
            // 不传数量
            int DO_NOT_TO_AG = 0;
            // 失败数量
            int FAil_TO_AG = 0;

            for (OcBReturnOrderRefund orderRefund : returnOrderRefunds) {
                if (orderRefund.getIsToAg().equals(AGStatusEnum.SUCCESS.getVal())) {
                    ALREADY_TO_AG++;
                    continue;
                } else if (orderRefund.getIsToAg().equals(AGStatusEnum.FAIL.getVal())) {
                    FAil_TO_AG++;
                    continue;
                } else if (orderRefund.getIsToAg().equals(AGStatusEnum.NOT.getVal())) {
                    DO_NOT_TO_AG++;
                    continue;
                }
            }

            if (FAil_TO_AG == returnOrderRefunds.size()) {
                returnOrder.setIsToag(AGStatusEnum.FAIL.getVal());
            }
            if (DO_NOT_TO_AG == returnOrderRefunds.size()) {
                returnOrder.setIsToag(AGStatusEnum.NOT.getVal());
            }
            if (ALREADY_TO_AG == returnOrderRefunds.size()) {
                returnOrder.setIsToag(AGStatusEnum.SUCCESS.getVal());
                returnOrder.setIsInstorage(IsInStorageEnum.IS_INSTORAGE.getVal());
                holderV14.setCode(0);
                holderV14.setMessage("通用平台自动退款服务成功！");
            } else {
                holderV14.setCode(-1);
                holderV14.setMessage("通用退款登记接口失败！");
            }
            ocBReturnOrderMapper.updateById(returnOrder);
        } catch (Exception e) {
            log.error("通用退款登记接口异常", e);
            holderV14.setCode(-1);
            holderV14.setMessage("通用退款登记接口异常！");
        }
        return holderV14;
    }

    /**
     * 通用退款接口
     *
     * @param returnOrder
     * @param loginUser
     * @return
     */
    private ValueHolderV14 agIncomingStock(OcBReturnOrder returnOrder,
                                           User loginUser,
                                           IpBStandplatRefund standplatRefund) {
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getSimpleName() + " 通用退款,returnOrder=" + JSONObject.toJSONString(returnOrder));
        }
        ValueHolderV14 v14 = new ValueHolderV14(0, "");
        try {
            OmsStandPlatRefundRelation omsStandPlatRefundRelation = new OmsStandPlatRefundRelation();
            omsStandPlatRefundRelation.setIpBStandplatRefund(standplatRefund);

            OmsOrderRelation orderRelation = new OmsOrderRelation();

            OcBOrder ocBOrder = ocBOrderMapper.selectById(returnOrder.getOrigOrderId());
            AssertUtil.notNull(ocBOrder, "无原单信息");
            orderRelation.setOcBOrder(ocBOrder);

            List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItems(returnOrder.getOrigOrderId());
            AssertUtil.notNull(ocBOrderItems, "无原单商品明细信息");
            orderRelation.setOcBOrderItems(ocBOrderItems);

            List<OmsOrderRelation> omsOrderRelation = Lists.newArrayList();
            omsOrderRelation.add(orderRelation);
            omsStandPlatRefundRelation.setOmsOrderRelation(omsOrderRelation);

            List<IpBStandplatRefundItem> ipBStandplatRefundItems =
                    ipBStandplatRefundItemMapper.selectRefundItemByRefundId(standplatRefund.getId());
            omsStandPlatRefundRelation.setIpBStandplatRefundItem(ipBStandplatRefundItems);

            //判断是否是虚拟入库
            Integer inventedStatus = returnOrder.getInventedStatus();
            Boolean invents = VirtualInStatusEnum.NOT.toInt() == inventedStatus;

//            BigDecimal inQty = ocBReturnOrderRefundMapper.selectSumInQtyByOcRefundInItem(ocBOrder.getId());

            List<OcBReturnOrderRefund> ocReturnOrderRefunds = ocBReturnOrderRefundMapper.selectList(new LambdaQueryWrapper<OcBReturnOrderRefund>()
                    .eq(OcBReturnOrderRefund::getOcBReturnOrderId, returnOrder.getId()));

            BigDecimal inQty = BigDecimal.ZERO;

            if (CollectionUtils.isNotEmpty(ocReturnOrderRefunds)) {
                //虚拟入库取申请数量
                if (invents) {
                    BigDecimal totQtyRefund = ocReturnOrderRefunds.stream().map(OcBReturnOrderRefund::getQtyRefund)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    inQty = inQty.add(Optional.ofNullable(totQtyRefund).orElse(BigDecimal.ZERO));
                    //其他取入库数量
                } else {
                    BigDecimal totQtyIn = ocReturnOrderRefunds.stream().map(OcBReturnOrderRefund::getQtyIn)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    inQty = inQty.add(Optional.ofNullable(totQtyIn).orElse(BigDecimal.ZERO));

                }
            }

            log.debug(this.getClass().getSimpleName() + " 通用退款接口服务入参=" + JSONObject.toJSONString(omsStandPlatRefundRelation));
            boolean flag = executeAutoRefund(omsStandPlatRefundRelation, loginUser, inQty, returnOrder);
            if (!flag) {
                v14.setMessage("通用退款失败!，请在零售发货单操作日志中查看失败原因!");
                v14.setCode(-1);
            }
            log.debug(this.getClass().getSimpleName() + " 通用退款接口服务出参=" + v14);
        } catch (Exception ex) {
            log.debug(this.getClass().getSimpleName() + " 通用退款接口服务异常=" + ex.getMessage());
            v14.setMessage("通用退款接口异常!");
            v14.setCode(-1);
        }
        return v14;
    }

    /**
     * 更新传SA状态，添加退换货订单操作日志中
     *
     * @param ocBReturnOrder 退换货实体
     * @param loginUser      loginUser
     * @param logContent     日志内容
     */
    private ValueHolderV14 updateRefundOrderRefundAndLog(OcBReturnOrder ocBReturnOrder, User loginUser, String logContent) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            ocBReturnOrderMapper.updateById(ocBReturnOrder);
        } catch (Exception ex) {
            log.debug("日志服务：通用平台自动退款接口异常" + ex);
            logContent = "同步异常：" + ex;
            logService.addRefundOrderLog(ocBReturnOrder.getId(), "通用平台自动退款", logContent, loginUser);
            throw new NDSException("通用平台自动退款拆包登记接口失败！" + ex);
        }

        if (ocBReturnOrder.getIsToag().equals(AGStatusEnum.SUCCESS.getVal())) {
            logContent = "同步成功：通用平台自动退款成功";
            holderV14.setCode(0);
            holderV14.setMessage("通用平台自动退款成功！");
        } else {
            holderV14.setCode(-1);
            logContent = "同步失败：" + logContent;
            holderV14.setMessage("通用平台自动退款接口失败！" + logContent);
        }
        logService.addRefundOrderLog(ocBReturnOrder.getId(), "通用平台自动退款", logContent, loginUser);
        return holderV14;
    }

    /**
     * 更新传SA状态，添加退换货订单操作日志中
     *
     * @param id
     * @param refund
     * @param loginUser
     * @param logContent
     * @return
     */
    private ValueHolderV14 updateRefundOrderRefundAndLog(Long id, OcBReturnOrderRefund refund, User loginUser, String logContent) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            ocBReturnOrderRefundMapper.updateById(refund);
        } catch (Exception ex) {
            log.debug("日志服务：通用平台自动退款接口异常" + ex);
            logContent = "明细sku[" + refund.getPsCSkuEcode() + "],同步异常：" + ex;
            logService.addRefundOrderLog(id, "通用平台自动退款", logContent, loginUser);
            throw new NDSException("通用平台自动退款接口失败！" + ex);
        }

        if (refund.getIsToAg().equals(AGStatusEnum.SUCCESS.getVal())) {
            logContent = "明细sku[" + refund.getPsCSkuEcode() + "],同步成功：通用平台自动退款成功";
            holderV14.setCode(0);
            holderV14.setMessage("通用平台自动退款登记接口成功！");
        } else {
            holderV14.setCode(-1);
            logContent = "明细sku[" + refund.getPsCSkuEcode() + "],同步失败：" + logContent;
            holderV14.setMessage("通用平台自动退款登记接口失败！" + logContent);
        }
        logService.addRefundOrderLog(id, "通用平台自动退款", logContent, loginUser);
        return holderV14;
    }

    /**
     * @param refundRelation
     * @param user
     * @param inQty
     * @return
     */
    public boolean executeAutoRefund(OmsStandPlatRefundRelation refundRelation, User user, BigDecimal inQty, OcBReturnOrder returnOrder) {
        //退单表
        IpBStandplatRefund ipBStandplatRefund = refundRelation.getIpBStandplatRefund();
        //原始订单
        OcBOrder ocBOrder = refundRelation.getOmsOrderRelation().get(0).getOcBOrder();
        CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(Long.valueOf(ocBOrder.getPlatform()));
        //获取退款明细
        List<OcBOrderItem> ocBOrderItems = refundRelation.getOmsOrderRelation().get(0).getOcBOrderItems();
        Long shopId = ocBOrder.getCpCShopId();
        boolean toAgByShopStrategy = this.isToAgByShopStrategy(shopId);

        ValueHolderV14 v14 = new ValueHolderV14();
        boolean isPinduoduo = PlatFormEnum.PINDUODUO.getCode().equals(ocBOrder.getPlatform());
        boolean isCardCode = PlatFormEnum.CARD_CODE.getCode().equals(ocBOrder.getPlatform());

        if (toAgByShopStrategy) {
            this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "店铺（AG/SA）状态：已开启", user);

            v14 = this.executeAutoRefundAfterShipment(ocBOrder, ocBOrderItems, ipBStandplatRefund, inQty);
            this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "通用取消发货" +
                    (v14.isOK() ? "成功" : "失败：" + v14.getMessage()) + ":退单号:" + ipBStandplatRefund.getReturnNo(), user);

            if (!v14.isOK()) {
                log.error("OcBRefundOrderStandPlatToRefundService.executeAutoRefund ag退款失败:{}:退单号:{}", v14.getMessage(),
                        ipBStandplatRefund.getReturnNo());
                if (!isPinduoduo) {
                    return false;
                }
            }
        } else {
            // 判断是不是小程序 是的话 调用小程序的接口
            if (isCardCode) {
                NaiKaRefundStockInRequest request = new NaiKaRefundStockInRequest();
                List<NaiKaRefundStockInItemRequest> refundStockInItemRequestList = new ArrayList<>();
                request.setRefundNo(ipBStandplatRefund.getReturnNo());
                request.setTid(ipBStandplatRefund.getOrderNo());
                request.setPlatformCode(cpCPlatform.getEcode());
                List<OcBReturnOrderRefund> ocBReturnOrderRefundList = refundRelation.getOcBReturnOrderRefunds();
                for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefundList) {
                    NaiKaRefundStockInItemRequest refundStockInItemRequest = new NaiKaRefundStockInItemRequest();
                    refundStockInItemRequest.setSubOrderId(ocBReturnOrderRefund.getOid());
                    refundStockInItemRequest.setNum(ocBReturnOrderRefund.getQtyRefund());
                    refundStockInItemRequest.setActNum(ocBReturnOrderRefund.getQtyIn());
                    refundStockInItemRequest.setSkuCode(ocBReturnOrderRefund.getPsCSkuEcode());
                    refundStockInItemRequestList.add(refundStockInItemRequest);
                    request.setRefundStockInItemRequestList(refundStockInItemRequestList);
                }
                naiKaOrderCmd.orderRefundStockIn(request);
            }

            if (!isPinduoduo) {
                this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "店铺（AG/SA）状态：未启用", user);
                return false;
            } else {
                this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "店铺（AG/SA）状态：未启用 平台：拼多多", user);
            }
        }

        //  拼多多还需要判断 判断店铺策略，是否同意退款状态
        if (isPinduoduo) {
            return this.executeAutoRefundByPinduoduo(shopId, ocBOrder, ocBOrderItems, returnOrder, ipBStandplatRefund, inQty, user, Boolean.FALSE);
        }


        //检验店铺策略
//        if (!omsOrderPolicyJingDongService.checkRefundStrategy(shopId, ipBStandplatRefund.getReturnReason())) {
//            //没有符合的策略:没有对应有效退款策略
//            this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "店铺退款策略：无有效退款策略", user);
//            return false;
//        }

        return v14.isOK();

    }

    /**
     * 拼多多退款 特殊
     *
     * @param shopId             店铺id
     * @param ocBOrder           零售发货单
     * @param ocBOrderItems      零售发货单明细
     * @param returnOrder        退换货单
     * @param ipBStandplatRefund 退换中间表
     * @param inQty              数量
     * @param user               用户
     * @param statusFront        发货前退款true or 发货后退款 false
     * @return Boolean
     */
    public Boolean executeAutoRefundByPinduoduo(Long shopId, OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems,
                                                OcBReturnOrder returnOrder, IpBStandplatRefund ipBStandplatRefund,
                                                BigDecimal inQty, User user, Boolean statusFront) {
        log.info("OcBRefundOrderStandPlatToRefundService.executeAutoRefund 拼多多退款 statusFront:{}", statusFront);

        if (!statusFront && returnOrder == null) {
            this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "退换货单为空！", user);
            return true;
        }

        OmsStCShopStrategyService bean = ApplicationContextHandle.getBean(OmsStCShopStrategyService.class);
        StCShopStrategyDO stShopStrategyDO = bean.selectOcStCShopStrategy(shopId);

        //没有符合的策略:没有对应有效退款策略
        if (stShopStrategyDO == null) {
            this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "店铺退款策略：无有效退款策略", user);
            return true;
        }

        String agreeRefund = stShopStrategyDO.getAgreeRefund();
        // 不同意退款
        if (agreeRefund == null ||
                OcBOrderConst.AGREE_REFUND_N.equals(agreeRefund) ||
                OcBOrderConst.AGREE_REFUND_NO.equals(agreeRefund)) {
            this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "店铺（AG/SA）状态：未启用！ 是否同意退款状态：否", user);
            // 2022-03-24 武浩：拼多多不管是否开启ag，是否同意退款，后续程序还是要走完
            return true;
        }
        statusFront = Optional.ofNullable(statusFront).orElse(Boolean.FALSE);

        log.info("OcBRefundOrderStandPlatToRefundService.executeAutoRefund 拼多多退款 statusFront={},", statusFront ? "发货前" : "发货后");

        // 发货后校验金额
        if (!statusFront) {
            //对比金额 为空无上限
            BigDecimal refundMoneylimit = stShopStrategyDO.getRefundMoneyLimit();

            BigDecimal refundAmount = Optional.ofNullable(ipBStandplatRefund.getRefundAmount()).orElse(BigDecimal.ZERO);
            if (refundMoneylimit != null && refundMoneylimit.compareTo(refundAmount) < 0) {
                this.updateOcBReturnOrder(returnOrder.getId(), "退单金额大于单笔退款上限，不能自动退款", OcBOrderConst.AGREE_REFUND_N);
                this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "退单金额大于单笔退款上限，不能自动退款", user);
                return true;
            }
        }

        Integer returnStatus = ipBStandplatRefund.getReturnStatus();
        if (statusFront && returnStatus.equals(4)) {
            log.info("OcBRefundOrderStandPlatToRefundService.executeAutoRefund 拼多多退款 发货前且发货已完成");
            return true;
        } else {
            //换货单退货金额小于或等于店铺策略设置的单笔退款金额上限，调用平台同意退款接口进行同意退款
            ValueHolderV14 v14 = this.executeAutoRefundByPinduoduoAfterShipment(ocBOrder, ocBOrderItems, ipBStandplatRefund, inQty);
            if (v14.isOK()) {
                if (!statusFront) {
                    this.updateOcBReturnOrder(returnOrder.getId(), null, OcBOrderConst.AGREE_REFUND_Y);
                }
                this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "拼多多退款成功", user);
            } else {
                if (!statusFront) {
                    this.updateOcBReturnOrder(returnOrder.getId(), v14.getMessage(), OcBOrderConst.AGREE_REFUND_N);
                }
                this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "拼多多退款失败:" + v14.getMessage(), user);
                return true;
            }
        }


        return true;
    }

    /**
     * 拼多多退款
     *
     * @param ocBOrder
     * @param ocBOrderItems
     * @param ipBStandplatRefund
     * @return
     */
    private ValueHolderV14 executeAutoRefundByPinduoduoAfterShipment(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, IpBStandplatRefund ipBStandplatRefund, BigDecimal inQty) {
        StandPlatRefundIntoWareHouseModel standPlatRefundIntoWareHouseModel = this.buildRefundIntoWareHouseModel(ocBOrder, ocBOrderItems, ipBStandplatRefund, inQty);
        log.info("executeAutoRefundByPinduoduoAfterShipment input parameter:{},time:{}", JSONObject.toJSONString(standPlatRefundIntoWareHouseModel), DateUtil.getDateTime());
        ValueHolderV14 v14 = standPlatRefundIntoWareHouseCmd.refundIntoWareHouseByPinduoduo(standPlatRefundIntoWareHouseModel);
        log.info("executeAutoRefundByPinduoduoAfterShipment return results:{},time:{}", v14.toJSONObject(), DateUtil.getDateTime());
        return v14;
    }

    /**
     * 更新退换货单
     *
     * @param objid         id
     * @param failReason    失败原因
     * @param isAgreeRefund 是否自动退款
     */
    private void updateOcBReturnOrder(Long objid, String failReason, String isAgreeRefund) {
        OcBReturnOrder update = new OcBReturnOrder();
        update.setId(objid);
        update.setAgreeRefund(isAgreeRefund);
        update.setRefundReason(failReason);
//        ocBReturnOrderMapper.updateById(update);
        ocBReturnOrderMapper.update(update, new LambdaUpdateWrapper<OcBReturnOrder>()
                .eq(OcBReturnOrder::getId, objid)
                .set(OcBReturnOrder::getRefundReason, failReason));
    }


    /**
     * 发货后退款
     *
     * @param ocBOrder
     * @param ocBOrderItems
     * @param ipBStandplatRefund
     * @return
     */
    private ValueHolderV14 executeAutoRefundAfterShipment(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, IpBStandplatRefund ipBStandplatRefund, BigDecimal inQty) {
        StandPlatRefundIntoWareHouseModel standPlatRefundIntoWareHouseModel = this.buildRefundIntoWareHouseModel(ocBOrder, ocBOrderItems, ipBStandplatRefund, inQty);
        log.info("executeAutoRefundAfterShipment input parameter:{},time:{}", JSONObject.toJSONString(standPlatRefundIntoWareHouseModel), DateUtil.getDateTime());
        ValueHolderV14 v14 = standPlatRefundIntoWareHouseCmd.refundIntoWareHouse(standPlatRefundIntoWareHouseModel);
        log.info("executeAutoRefundAfterShipment return results:{},time:{}", v14.toJSONObject(), DateUtil.getDateTime());
        return v14;
    }

    /**
     * 发货后退款入参
     *
     * @param ocBOrder
     * @param ocBOrderItems
     * @param ipBStandplatRefund
     * @return
     */
    private StandPlatRefundIntoWareHouseModel buildRefundIntoWareHouseModel(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems,
                                                                            IpBStandplatRefund ipBStandplatRefund, BigDecimal inQty) {
        String ooidStr = ocBOrderItems.stream().map(OcBOrderItem::getOoid).collect(Collectors.joining(","));
        String tid = ocBOrderItems.get(0).getTid();
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        String shopSellerNick = cpShop != null ? cpShop.getSellerNick() : "";
        StandPlatRefundIntoWareHouseModel standPlatRefundIntoWareHouseModel = new StandPlatRefundIntoWareHouseModel();
        /**
         * 入库状态
         * 0:入库成功
         * 1:入库失败
         */
        standPlatRefundIntoWareHouseModel.setStorageStatus("0");
        /**
         *是否整单退(Y是,N否)
         */
        standPlatRefundIntoWareHouseModel.setIsAllReturn("N");
        /**
         *物流公司编码
         */
        standPlatRefundIntoWareHouseModel.setLogisticsCompanyId(0);
        LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(ipBStandplatRefund.getCompanyName());
        String companyCode = "";
        if (logisticsInfo != null) {
            companyCode = cpRpcService.getPlatformLogisticEcode(logisticsInfo.getId(), Long.valueOf(ocBOrder.getPlatform()));/**
             *物流公司编码
             */
//            standPlatRefundIntoWareHouseModel.setLogisticsCompanyId(Integer.parseInt(String.valueOf(logisticsInfo.getId())));
            standPlatRefundIntoWareHouseModel.setCompanyCode(companyCode);
        }
        log.info("buildRefundIntoWareHouseModel,通用退货退款物流入参:{},物流公司编码:{},平台code:{},退单号:{}", ipBStandplatRefund.getCompanyName(), companyCode, ocBOrder.getPlatform(), ipBStandplatRefund.getReturnNo());

        /**
         * 退货物流单号
         */
        standPlatRefundIntoWareHouseModel.setOutSid(ipBStandplatRefund.getLogisticsNo());
        /**
         * 平台号code
         */
        standPlatRefundIntoWareHouseModel.setPlatform(ocBOrder.getPlatform());
        //退款金额
        standPlatRefundIntoWareHouseModel.setRefundAmount(ipBStandplatRefund.getRefundAmount());
        /**
         * 平台退款单号
         */
        standPlatRefundIntoWareHouseModel.setRefundId(ipBStandplatRefund.getReturnNo());
        if (PlatFormEnum.PINDUODUO.getCode().equals(ocBOrder.getPlatform())) {
            standPlatRefundIntoWareHouseModel.setReturnNo(ipBStandplatRefund.getReturnNo());
            // 买家备注
            standPlatRefundIntoWareHouseModel.setSellerRemark(ocBOrder.getSellerMemo());
        }

        /**
         * 店铺名称(卖家昵称)
         */
        standPlatRefundIntoWareHouseModel.setSellerNick(shopSellerNick);
//        standPlatRefundIntoWareHouseModel.setTag();
        standPlatRefundIntoWareHouseModel.setTid(tid);
        standPlatRefundIntoWareHouseModel.setOid(ooidStr);

        /**
         *退款拒绝原因
         *1退货与原订单不符（商品不符、退货地址不符）
         * 2退回商品影响二次销售/订单超出售后时效（订单完成超7天）
         * 3已与买家协商补偿，包括差价、赠品、额外补偿
         * 4已与买家协商补发商品/已与买家协商换货
         */
//        standPlatRefundIntoWareHouseModel.setRefuseType();
        /**
         * 拒绝原因
         */
//        standPlatRefundIntoWareHouseModel.setRefuseReason();
        if (PlatFormEnum.YOUZAN.getCode().equals(ocBOrder.getPlatform())) {
            //todo //版本号(有赞必填)
            standPlatRefundIntoWareHouseModel.setVersion("1.0");
        }
        standPlatRefundIntoWareHouseModel.setNum(inQty.intValue());
        standPlatRefundIntoWareHouseModel.setWarehousingTime(String.valueOf(System.currentTimeMillis()));
        //freightAmount
        if (PlatFormEnum.AI_KU_CUN.getCode().equals(ocBOrder.getPlatform())) {
            //todo 运费(爱库存必填) RETURN_SHIPAMOUNT
            standPlatRefundIntoWareHouseModel.setFreightAmount(Optional.ofNullable(ipBStandplatRefund.getReturnShipamount()).orElse(BigDecimal.ZERO));
        }

        return standPlatRefundIntoWareHouseModel;
    }


    /**
     * 检查ag
     *
     * @param shopId
     * @return
     */
    public boolean isToAgByShopStrategy(Long shopId) {
        return stRpcService.isToAgByShopStrategy(shopId);
    }


    /**
     * 添加日志
     *
     * @param orderId
     * @param logType
     * @param msg
     */
    public void addReturnOrderAutoRefundLog(Long orderId, OrderLogTypeEnum logType, String msg, User user) {
        omsOrderLogService.addUserOrderLog(orderId, null, logType.getKey(), msg, null, null,
                user);
    }
}
