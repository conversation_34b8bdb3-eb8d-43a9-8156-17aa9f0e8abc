package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.enums.naika.AccountToNaiKaEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.request.NaiKaAccountInitRequest;
import com.jackrain.nea.oc.oms.model.request.NaiKaAccountRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName NaiKaAccountService
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/5 17:35
 * @Version 1.0
 */
@Component
@Slf4j
public class NaiKaAccountService {

    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    public ValueHolderV14 execute(NaiKaAccountRequest request) {
        log.info(LogUtil.format("start to execute NaiKaAccountService request:{}", "OMS开始执行对账分摊任务"), JSONUtil.toJsonStr(request));
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");
        // 先判断是正向还是逆向的订单
        String billType = request.getBillType();
        // 零售发货单/
        Long sourceOriginBillId = request.getSourceOriginBillId();
        Long sourceOriginBillItemId = request.getSourceOriginBillItemId();
        Long sourceOriginRltId = request.getSourceOriginRltId();
        Long sourceOriginRltItemId = request.getSourceOriginRltItemId();
        BigDecimal wipeAmt = request.getWipeAmt();
        Date upToBillDate = request.getUpToBillDate();
        if (ObjectUtil.isEmpty(billType)) {
            throw new NDSException("单据类型有误");
        }
        // 所有的金额都是正的 用核销金额  根据金额除卡号数量 然后除不尽的话 金额分摊给最后面那个。  只关注订单跟已发货退款单
        // 正向
        try {
            if (ObjectUtil.equals("OM", billType)) {
                applicationContext.getBean(NaiKaAccountService.class).executeOM(wipeAmt, sourceOriginBillId, sourceOriginBillItemId, upToBillDate);
            }
            // 逆向
            if (ObjectUtil.equals("AF", billType)) {
                applicationContext.getBean(NaiKaAccountService.class).executeAf(wipeAmt, sourceOriginRltId, sourceOriginRltItemId, upToBillDate);
            }
        } catch (Exception e) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }
        return valueHolderV14;
    }

    public ValueHolderV14 naiKaAccountInit(NaiKaAccountInitRequest initRequest) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");

        // 找到对应的奶卡 然后执行
        List<OcBOrderNaiKa> ocBOrderNaiKaList =
                ocBOrderNaiKaMapper.selectNaiKaByOcBOrderIdAndItemId(initRequest.getSourceOriginBillId(), initRequest.getSourceOriginBillItemId());
        if (CollectionUtil.isEmpty(ocBOrderNaiKaList)) {
            return valueHolderV14;
        }
        // 根据零售发货单id+奶卡id 重置奶卡的对账金额
        for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKaList) {
            OcBOrderNaiKa updateOcBOrderNaiKa = new OcBOrderNaiKa();
            updateOcBOrderNaiKa.setWipeAmt(BigDecimal.ZERO);
            updateOcBOrderNaiKa.setAccountToNaika(AccountToNaiKaEnum.TODO.getStatus());
            updateOcBOrderNaiKa.setOcBOrderId(ocBOrderNaiKa.getOcBOrderId());
            updateOcBOrderNaiKa.setId(ocBOrderNaiKa.getId());
            updateOcBOrderNaiKa.setModifieddate(new Date());
            updateOcBOrderNaiKa.setAccountToNaikaTimes(0);
            updateOcBOrderNaiKa.setUpToBillDate(new Date());
            ocBOrderNaiKaMapper.updateById(updateOcBOrderNaiKa);
        }
        return valueHolderV14;
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeAf(BigDecimal wipeAmt, Long sourceOriginRltId, Long sourceOriginRltItemId, Date upToBillDate) {
        // 根据零售发货单id+明细表id 获取奶卡数据
        List<OcBOrderNaiKa> ocBOrderNaiKaList =
                ocBOrderNaiKaMapper.selectNaiKaByOcBOrderIdAndItemId(sourceOriginRltId, sourceOriginRltItemId);
        if (CollectionUtil.isEmpty(ocBOrderNaiKaList)) {
            return;
        }
        // 筛选出来 已作废状态的
        List<OcBOrderNaiKa> voidNaiKaList =
                ocBOrderNaiKaList.stream().filter(item -> item.getNaikaStatus().equals(OmsOrderNaiKaStatusEnum.VOID_SUCCESS.getStatus())).collect(Collectors.toList());
        List<OcBOrderNaiKa> leftNaiKaList =
                ocBOrderNaiKaList.stream().filter(item -> !item.getNaikaStatus().equals(OmsOrderNaiKaStatusEnum.VOID_SUCCESS.getStatus())).collect(Collectors.toList());
        BigDecimal leftWipeAmt = wipeAmt;
        if (CollectionUtil.isNotEmpty(voidNaiKaList)) {
            // 如果含有作废了的奶卡信息 则需要将退款的钱优先分摊给作废了的奶卡
            // 判断数量(因为有可能会存在除不尽的情况)
            if (ObjectUtil.equals(voidNaiKaList.size(), 1)) {
                OcBOrderNaiKa ocBOrderNaiKa = voidNaiKaList.get(0);
                // 判断金额够不够冲抵的
                if (wipeAmt.compareTo(ocBOrderNaiKa.getWipeAmt()) >= 0) {
                    leftWipeAmt = wipeAmt.subtract(ocBOrderNaiKa.getWipeAmt());
                    extracted(ocBOrderNaiKa, BigDecimal.ZERO, upToBillDate);
                } else {
                    leftWipeAmt = BigDecimal.ZERO;
                    extracted(ocBOrderNaiKa, ocBOrderNaiKa.getWipeAmt().subtract(wipeAmt), upToBillDate);
                }
            } else {
                // 如果数量多个的话 需要跟正向一样 对金额进行平摊
                // 先判断核销的金额会不会比已作废的奶卡剩余可核销金额多
                BigDecimal voidAllWipeAmt = BigDecimal.ZERO;
                for (OcBOrderNaiKa ocBOrderNaiKa : voidNaiKaList) {
                    voidAllWipeAmt = voidAllWipeAmt.add(ocBOrderNaiKa.getWipeAmt());
                }
                // 说明核销的金额 比剩余所有的已作废奶卡的可核销金额的钱还多 则将剩余的已作废的奶卡的金额都设置为0 同时调整leftWipeAmt
                if (wipeAmt.compareTo(voidAllWipeAmt) >= 0) {
                    for (OcBOrderNaiKa ocBOrderNaiKa : voidNaiKaList) {
                        extracted(ocBOrderNaiKa, BigDecimal.ZERO, upToBillDate);
                    }
                    leftWipeAmt = wipeAmt.subtract(voidAllWipeAmt);
                } else {
                    // 说明所有核销的钱 完全可以均摊到已作废的奶卡上面去
                    // 先均摊 向下取整
                    BigDecimal shareWipeAmt = wipeAmt.divide(new BigDecimal(voidNaiKaList.size()), 2, RoundingMode.DOWN);
                    BigDecimal lastWipeAmt = wipeAmt.subtract(shareWipeAmt.multiply(new BigDecimal(voidNaiKaList.size() - 1)));
                    for (int i = 0; i < voidNaiKaList.size(); i++) {
                        OcBOrderNaiKa ocBOrderNaiKa = voidNaiKaList.get(i);
                        if (i == (voidNaiKaList.size() - 1)) {
                            extracted(ocBOrderNaiKa, ocBOrderNaiKa.getWipeAmt().subtract(lastWipeAmt), upToBillDate);
                        } else {
                            extracted(ocBOrderNaiKa, ocBOrderNaiKa.getWipeAmt().subtract(shareWipeAmt), upToBillDate);
                        }
                    }
                    leftWipeAmt = BigDecimal.ZERO;
                }
            }
        }
        // 如果leftWipeAmt大于0  说明还没核销完成 则需要找出来剩余的 未作废的奶卡 然后对金额再进行冲销
        if (leftWipeAmt.compareTo(BigDecimal.ZERO) > 0) {
            // 所有作废的都均摊完了 也不剩下未作废的。则找最后面那个作废的倒霉蛋作为负值冲抵
            if (CollectionUtil.isEmpty(leftNaiKaList)) {
                for (int i = 0; i < voidNaiKaList.size(); i++) {
                    OcBOrderNaiKa ocBOrderNaiKa = voidNaiKaList.get(i);
                    if (i == (voidNaiKaList.size() - 1)) {
                        extracted(ocBOrderNaiKa, BigDecimal.ZERO.subtract(leftWipeAmt), upToBillDate);
                    }
                }
            } else {
                // 判断数量
                if (ObjectUtil.equals(leftNaiKaList.size(), 1)) {
                    OcBOrderNaiKa ocBOrderNaiKa = leftNaiKaList.get(0);
                    extracted(ocBOrderNaiKa, ocBOrderNaiKa.getWipeAmt().subtract(leftWipeAmt), upToBillDate);
                    return;
                }
                // 判断钱够不够冲抵的
                BigDecimal leftAllWipeAmt = BigDecimal.ZERO;
                for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKaList) {
                    leftAllWipeAmt = leftAllWipeAmt.add(ocBOrderNaiKa.getWipeAmt());
                }
                if (leftWipeAmt.compareTo(leftAllWipeAmt) >= 0) {
                    // 钱不够冲抵的 则选择一张卡承担负值 剩下的都为0
                    for (int i = 0; i < leftNaiKaList.size(); i++) {
                        OcBOrderNaiKa ocBOrderNaiKa = leftNaiKaList.get(i);
                        if (i == (leftNaiKaList.size() - 1)) {
                            extracted(ocBOrderNaiKa, leftAllWipeAmt.subtract(leftWipeAmt), upToBillDate);
                        } else {
                            extracted(ocBOrderNaiKa, BigDecimal.ZERO, upToBillDate);
                        }
                    }
                } else {
                    // 钱够冲抵的 则均摊
                    BigDecimal shareWipeAmt = leftWipeAmt.divide(new BigDecimal(leftNaiKaList.size()), 2, RoundingMode.DOWN);
                    BigDecimal lastWipeAmt = leftWipeAmt.subtract(shareWipeAmt.multiply(new BigDecimal(leftNaiKaList.size() - 1)));
                    for (int i = 0; i < leftNaiKaList.size(); i++) {
                        OcBOrderNaiKa ocBOrderNaiKa = leftNaiKaList.get(i);
                        if (i == (leftNaiKaList.size() - 1)) {
                            // 最后一张卡
                            extracted(ocBOrderNaiKa, ocBOrderNaiKa.getWipeAmt().subtract(lastWipeAmt), upToBillDate);
                        } else {
                            extracted(ocBOrderNaiKa, ocBOrderNaiKa.getWipeAmt().subtract(shareWipeAmt), upToBillDate);
                        }
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeOM(BigDecimal wipeAmt, Long sourceOriginBillId, Long sourceOriginBillItemId, Date upToBillDate) {
        // 先根据 零售发货单id+明细表的id 获取奶卡数据
        List<OcBOrderNaiKa> ocBOrderNaiKaList =
                ocBOrderNaiKaMapper.selectNaiKaByOcBOrderIdAndItemId(sourceOriginBillId, sourceOriginBillItemId);
        if (CollectionUtil.isEmpty(ocBOrderNaiKaList)) {
            return;
        }
        // 如果数量为1 则直接处理
        if (ObjectUtil.equals(ocBOrderNaiKaList.size(), 1)) {
            OcBOrderNaiKa ocBOrderNaiKa = ocBOrderNaiKaList.get(0);
            BigDecimal oldWipeAmt = wipeAmt.add(ocBOrderNaiKa.getWipeAmt());
            extracted(ocBOrderNaiKa, oldWipeAmt, upToBillDate);
        } else {
            // 计算出来需要均摊的金额。因为是多单 可能存在除不尽的情况 如果除不尽 则将金额分摊给最后面一位
            // 考虑到金额可能除不尽
            BigDecimal shareWipeAmt = wipeAmt.divide(new BigDecimal(ocBOrderNaiKaList.size()), 2, RoundingMode.DOWN);
            BigDecimal lastWipeAmt = wipeAmt.subtract(shareWipeAmt.multiply(new BigDecimal(ocBOrderNaiKaList.size() - 1)));
            for (int i = 0; i < ocBOrderNaiKaList.size(); i++) {
                OcBOrderNaiKa ocBOrderNaiKa = ocBOrderNaiKaList.get(i);
                if (i < (ocBOrderNaiKaList.size() - 1)) {
                    extracted(ocBOrderNaiKa, shareWipeAmt.add(ocBOrderNaiKa.getWipeAmt()), upToBillDate);
                } else {
                    extracted(ocBOrderNaiKa, lastWipeAmt.add(ocBOrderNaiKa.getWipeAmt()), upToBillDate);
                }
            }
        }
    }

    private void extracted(OcBOrderNaiKa ocBOrderNaiKa, BigDecimal oldWipeAmt, Date upToBillDate) {
        String lockRedisKey = BllRedisKeyResources.buildLockCardCodeKey(ocBOrderNaiKa.getCardCode());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrderNaiKa updateOcBOrderNaiKa = new OcBOrderNaiKa();
                updateOcBOrderNaiKa.setWipeAmt(oldWipeAmt);
                updateOcBOrderNaiKa.setAccountToNaika(AccountToNaiKaEnum.TODO.getStatus());
                updateOcBOrderNaiKa.setOcBOrderId(ocBOrderNaiKa.getOcBOrderId());
                updateOcBOrderNaiKa.setId(ocBOrderNaiKa.getId());
                updateOcBOrderNaiKa.setModifieddate(new Date());
                updateOcBOrderNaiKa.setAccountToNaikaTimes(0);
                updateOcBOrderNaiKa.setUpToBillDate(upToBillDate);
                ocBOrderNaiKaMapper.updateById(updateOcBOrderNaiKa);
            }
        } catch (Exception e) {
            log.error("获取锁失败,卡号:{}", ocBOrderNaiKa.getCardCode());
            throw new RuntimeException(e);
        } finally {
            redisLock.unlock();
        }

    }
}
