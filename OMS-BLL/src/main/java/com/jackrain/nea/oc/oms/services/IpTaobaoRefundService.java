package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.rpc.AcRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 孙勇生
 * create at:  19/3/7  15:35
 * @description: 中间表淘宝换货处理服务
 * 1)根据refundId获取中间表信息
 * 2)通过中间表oid去ES获取分库键oc_b_order_id
 * 3)通过分库键及oid去DRDS获取订单明细列表L0
 * 4)通过oc_b_order_id获取订单信息
 * 5）过滤掉订单信息订单状态是作废和取消状态，获得有效订单L1
 * 6) 通过有效订单的id过滤L0,得到有效订单明细L2；
 * 7）判断有效订单是否有赠品
 * 8）通过L1获取所有赠品集合L3（并且OID一致或OID=null）
 */
@Slf4j
@Component
public class IpTaobaoRefundService {

    @Autowired
    private IpBTaobaoRefundMapper ipBTaobaoRefundMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    protected OcBOrderMapper ocBOrderMapper;
    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;
    @Autowired
    private OmsReturnOrderService omsReturnOrderService;
    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;
    @Autowired
    private AcRpcService acRpcService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;


    /**
     * @param orderNo 平台退款单号
     * @return 封装参数
     */
    public IpTaobaoRefundRelation selectTaobaoRefundRelation(String orderNo) {
        IpTaobaoRefundRelation ipTaobaoRefundRelation = null;
        IpBTaobaoRefund taobaoRefund = ipBTaobaoRefundMapper.selectTaobaoRefundByRefundId(orderNo);
        if (null != taobaoRefund) {
            ipTaobaoRefundRelation = new IpTaobaoRefundRelation();
            ipTaobaoRefundRelation.setTaobaoRefund(taobaoRefund);
            /**
             *  2)通过中间表oid去ES获取分库键oc_b_order_id
             */
            //所有订单id信息
            Long oid = taobaoRefund.getOid();
            JSONArray returnData = ES4Order.getIdsByItemOoId(oid);

            if (returnData.isEmpty()) {
                return ipTaobaoRefundRelation;
            }
            List<HashMap> orderIdHmp = JSONArray.parseArray(returnData.toJSONString(), HashMap.class);
            List<Long> orderIds = new ArrayList<>();

            for (HashMap hashMap : orderIdHmp) {
                orderIds.add((Long) hashMap.get("OC_B_ORDER_ID"));

            }
            //去重
            orderIds = orderIds.stream().distinct().collect(Collectors.toList());

            /**
             *
             * es获取分库键 + id 原单明细
             * 3)通过分库键及oid去DRDS获取订单明细列表L0
             */
            //liqb 更改ooid类型从Long类型改成String类型
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListByOoid(String.valueOf(oid), orderIds);
            if (CollectionUtils.isNotEmpty(orderItems)) {
                /**
                 * 4)通过oc_b_order_id获取订单信息
                 */
                List<OcBOrder> orderList = ocBOrderMapper.selectOrderListByIds(orderIds);
                if (CollectionUtils.isNotEmpty(orderList)) {
                    /**
                     * 5)过滤掉订单信息订单状态是作废和取消状态，获得有效订单L1
                     */
                    List<OcBOrder> ocBOrderList = orderList.stream().filter(ocBorder -> (
                                    !ocBorder.getOrderStatus().equals(OmsOrderStatus.CANCELLED.toInteger())
                                            && !ocBorder.getOrderStatus().equals(OmsOrderStatus.SYS_VOID.toInteger())
                            )
                    ).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(ocBOrderList)) {
                        ipTaobaoRefundRelation.setEffectiveOrder(true);
                    }
                    /**
                     *  * 6) 通过有效订单的id过滤L0,得到有效订单明细L2；
                     */
                    List<OcBOrderItem> ocBOrderItems = null;
                    List<OcBOrderItem> orderGifts = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(ocBOrderList)) {
                        ocBOrderItems = new ArrayList<>();
                        for (OcBOrder ocO : ocBOrderList) {
                            ocBOrderItems.addAll(orderItems.stream().filter(item -> (
                                            ocO.getId().equals(item.getOcBOrderId())
                                    )
                            ).collect(Collectors.toList()));
                            //判断有效订单是否有赠品
                            if (ocO.getIsHasgift() == 1) {
                                List<OcBOrderItem> orderGiftsTemp =
                                        ocBOrderItemMapper.selectOrderGiftListByOrderId(ocO.getId());
                                if (CollectionUtils.isNotEmpty(orderGiftsTemp)) {
                                    orderGifts.addAll(orderGiftsTemp);
                                }
                            }
                        }
                        ipTaobaoRefundRelation.setOcBOrder(ocBOrderList);
                        //2019-8-02
//                        Map<Long, LockObject> lockObjectMap = new HashMap<>();
//                        for (OcBOrder order : ocBOrderList) {
//                            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(order.getId());
//                            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
//                            LockObject lockObject = new LockObject();
//                            try {
//                                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
//                                    lockObject.setRedisReentrantLock(redisLock);
//                                    lockObject.setLock(true);
//
//                                } else {
//                                    lockObject.setRedisReentrantLock(redisLock);
//                                    lockObject.setLock(false);
//                                }
//                            } catch (Exception e) {
//                                log.error(this.getClass().getName() + " 订单id为:" + order.getId() + "加锁失败", e);
//                                throw new NDSException("订单id为:" + order.getId() + "加锁失败");
//                            } finally {
//                                redisLock.unlock();
//                            }
//                            lockObjectMap.put(order.getId(),lockObject);
//                        }
//                        ipTaobaoRefundRelation.setLockObjectMap(lockObjectMap);


                        //return ipTaobaoRefundRelation;
                        if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                            // 判断明细是否有平台换货单号
                            ipTaobaoRefundRelation.setOcBOrderItems(ocBOrderItems);
                            this.isExchangeGoodsOdd(ocBOrderItems, ipTaobaoRefundRelation);
                        }
                        /**
                         *  * 8）通过L1获取所有赠品集合L3（并且OID一致或OID=null）
                         */
                        if (CollectionUtils.isNotEmpty(orderGifts)) {
                            ipTaobaoRefundRelation.setOcBOrderGifts(orderGifts);
                        }
                    }

                }
            }

            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + " 查询数据返回:{}", ipTaobaoRefundRelation);
            }
        }

        return ipTaobaoRefundRelation;
    }

    /**
     * 判断明细是否有平台换货单号
     */
    private void isExchangeGoodsOdd(List<OcBOrderItem> ocBOrderItems, IpTaobaoRefundRelation ipTaobaoRefundRelation) {
        List<OcBOrderItem> orderItems = new ArrayList<>();
        Set<Long> bigInt2 = new HashSet();
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            Long reserveBigint02 = ocBOrderItem.getExchangeBillNo();
            if (reserveBigint02 != null) {
                bigInt2.add(reserveBigint02);
            } else {
                orderItems.add(ocBOrderItem);
            }
        }
        //存放订单id
        Set<Long> orderIds = new HashSet();
        for (Long aLong : bigInt2) {
            //通过平台换货单号查询对应的明细信息

            JSONArray returnData = ES4Order.getIdsByItemOoId(aLong);

            if (returnData.isEmpty()) {
                throw new NDSException("未查询到对应的换货订单明细");
            }
            Set<Long> setList = new HashSet();
            for (Object returnDatum : returnData) {
                JSONObject returnDatum1 = (JSONObject) returnDatum;
                Long oc_b_order_id = returnDatum1.getLong("OC_B_ORDER_ID");
                setList.add(oc_b_order_id);
                orderIds.add(oc_b_order_id);
            }


            //根据平台换货单号和对应的订单id查询订单明细信息
            List<OcBOrderItem> items = ocBOrderItemMapper.selectOrderItemListByBigin02(setList, aLong);
            orderItems.addAll(items);
        }
        if (CollectionUtils.isNotEmpty(orderIds)) {
            //查询订单对应的订单信息
            List<Long> result = new ArrayList<>(orderIds);
            List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsList(result);
            List<OcBOrder> ocBOrders = ocBOrderList.stream().filter(ocBorder -> (
                            !ocBorder.getOrderStatus().equals(OmsOrderStatus.CANCELLED.toInteger())
                                    && !ocBorder.getOrderStatus().equals(OmsOrderStatus.SYS_VOID.toInteger())
                    )
            ).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ocBOrders)) {
                ipTaobaoRefundRelation.setOcBOrder(ocBOrders);
                ipTaobaoRefundRelation.setExchangeGoods(true); //为换货后退货的订单
                if (CollectionUtils.isNotEmpty(orderItems)) {
                    ipTaobaoRefundRelation.setOcBOrderItems(orderItems);
                }
            }
        }

    }


    /**
     * 发货前
     * 原单状态为 待分配50、待审核、缺货2、已审核3、待传WMS中21、配货中4
     */
    public boolean orderStatusHasGoodReturnStatusFront(IpBTaobaoRefund taobaoRefund) {
        IpTaobaoRefundService bean = ApplicationContextHandle.getBean(IpTaobaoRefundService.class);
        Integer hasGoodReturn = taobaoRefund.getHasGoodReturn();
        if (TaobaoReturnOrderExt.HasGoodReturnStatus.YES_RETURN.getCode()
                .equals(hasGoodReturn)) {
            //，则程序结束，更新退单中间表的退单转换状态为已转换，
            // 添加系统备注：“原始订单未发货，不能转换，请发货后处理
            String remark = SysNotesConstant.SYS_REMARK16;
            bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, taobaoRefund);
            return false;
        }
        //  调用【发货前退退款转换服务】
        return TaobaoReturnOrderExt.HasGoodReturnStatus.NO_RETURN.getCode()
                .equals(hasGoodReturn);

    }

    /**
     * @param orderInfo
     * @param operateUser
     * @return true 退单发货后转换服务 false 仅退款应收调整单服务
     */
    public Boolean orderStatusHasGoodReturnStatusAfter(IpTaobaoRefundRelation orderInfo,
                                                       User operateUser) throws IOException {
        IpTaobaoRefundService bean = ApplicationContextHandle.getBean(IpTaobaoRefundService.class);
        IpBTaobaoRefund taobaoRefund = orderInfo.getTaobaoRefund();
        Integer hasGoodReturn = taobaoRefund.getHasGoodReturn();
        OcBOrder order = orderInfo.getOcBOrder().get(0);
        String status = taobaoRefund.getStatus(); //退单中间表的退单状态
        if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status)) {
            //走退单关闭服务
            this.refundOrderClose(operateUser, taobaoRefund, order);
            return null;
        }
        //如果平台状态为(买家已经申请退款，等待卖家同意）(卖家拒绝退款)，则不进行处理，
        // 更新退单中间表的退单转换状态为已转换
        if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status)
                || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(status)) {
            String remark = SysNotesConstant.SYS_REMARK27;
            bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, taobaoRefund);
            return null;
        }
        if (TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode().equals(status)
                || TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode().equals(status)
                || TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
            if (TaobaoReturnOrderExt.HasGoodReturnStatus.YES_RETURN.getCode()
                    .equals(hasGoodReturn)) {
                //判断退款的申请金额是否小于30元
                BigDecimal refundFee = taobaoRefund.getRefundFee(); //退还金额
                if (refundFee.compareTo(new BigDecimal(30)) > 0) {
                    //【退单发货后转换服务】
                    return true;
                } else {
                    //小于等于30元，则判断退单中间表的申请金额与原单明细的金额是否一致
                    //true 下一步转换 【退单发货后转换服务】false【仅退款应收调整单服务】
                    return returnOrderTransferUtil.isAmtBalanceNew(orderInfo);
                }
            }
            if (TaobaoReturnOrderExt.HasGoodReturnStatus.NO_RETURN.getCode()
                    .equals(hasGoodReturn)) {
                //判断金额是否相等
                //true 下一步转换 【退单发货后转换服务】false【仅退款应收调整单服务】
                return returnOrderTransferUtil.isAmtBalanceNew(orderInfo);
            }
        }
        return null;
    }

    /**
     * 应收调整单服务
     *
     * @param orderInfo
     */
    public boolean receivablesAdjustment(IpTaobaoRefundRelation orderInfo, User user) {
        IpTaobaoRefundService bean = ApplicationContextHandle.getBean(IpTaobaoRefundService.class);
        OcBOrder ocBOrder = orderInfo.getOcBOrder().get(0);
        IpBTaobaoRefund taobaoRefund = orderInfo.getTaobaoRefund();
        List<OcBOrderItem> ocBOrderItems = orderInfo.getOcBOrderItems();
        JSONObject json = new JSONObject();
        JSONObject object = new JSONObject();
        object.put("CP_C_PLATFORM_ID", ocBOrder.getPlatform());
        object.put("ORDER_PRICE", ocBOrder.getOrderAmt()); //订单总额
        object.put("TID", taobaoRefund.getTid());
        object.put("ORDER_TYPE", ocBOrder.getOrderType());
        object.put("ORDER_FROM", ocBOrder.getOrderSource()); //订单来源
        object.put("CP_C_SHOP_TITLE", ocBOrder.getCpCShopTitle());
        object.put("PAY_TIME", ocBOrder.getPayTime());
        object.put("CUSTOMER_NICK", ocBOrder.getUserNick());
        object.put("CP_C_SHOP_ID", ocBOrder.getCpCShopId());
        object.put("CUSTOMER_NAME", ocBOrder.getReceiverName()); //收货人姓名
        object.put("CUSTOMER_TEL", ocBOrder.getReceiverMobile());
        object.put("ALIPAY_ACCOUNT", ocBOrder.getBuyerAlipayNo());//buyer_alipay_no
        object.put("SELLERMEMO", ocBOrder.getSellerMemo());
        object.put("AMT", taobaoRefund.getRefundFee());
        object.put("REASON", "退差价"); //约定写死
        object.put("REFUND_TIME", taobaoRefund.getCreated());
        object.put("REMARK", taobaoRefund.getSysremark());
        object.put("REFUND_NO", taobaoRefund.getRefundId());
        JSONArray jsonArray = new JSONArray();
        ocBOrderItems.forEach(p -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("OID", p.getOoid());
            jsonObject.put("ORDER_NO", ocBOrder.getBillNo());
            jsonObject.put("PS_C_PRO_ID", p.getPsCProId());
            jsonObject.put("PS_C_PRO_ECODE", p.getPsCProEcode());
            jsonObject.put("PS_C_PRO_ENAME", p.getPsCProEname());
            jsonObject.put("PS_C_SKU_ID", p.getPsCSkuId());
            jsonObject.put("PS_C_SKU_ECODE", p.getPsCSkuEcode());
            jsonObject.put("PS_C_CLR_ID", p.getPsCClrId());
            jsonObject.put("PS_C_CLR_ECODE", p.getPsCClrEcode());
            jsonObject.put("PS_C_CLR_ENAME", p.getPsCClrEname());
            jsonObject.put("PS_C_SIZE_ID", p.getPsCSizeId());
            jsonObject.put("PS_C_SIZE_ECODE", p.getPsCSizeEcode());
            jsonObject.put("PS_C_SIZE_ENAME", p.getPsCSizeEname());
            jsonObject.put("TRUE_PRICE", p.getRealAmt()); //单行实际成交金额
            jsonObject.put("QTY", taobaoRefund.getNum());
            jsonObject.put("STANDARD_PRICE", p.getPriceList()); //标准价
            jsonObject.put("BARCODE", p.getBarcode());//国标码 ljp add
            jsonArray.add(jsonObject);
        });
        json.put("AC_F_RECEIVABLES_ADJUSTMENT", object);
        json.put("AC_F_RECEIVABLES_ADJUSTMENT_ITEM", jsonArray);
        try {
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + " 仅退款应收调整单服务入参:{}", json);
            }
            Integer isgenincidental = taobaoRefund.getIsgenincidental();
            if (isgenincidental != null && isgenincidental == 1) {
                //已生成应收调整单
                //返回1时   当前已生成 更新已转换  添加备注
                String remark = SysNotesConstant.SYS_REMARK32;
                boolean flag = bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), remark
                        , taobaoRefund);
                return flag;
            }
            ValueHolderV14 holder = acRpcService.getReceivableAdjustmentData(json, user);
            int code = Tools.getInt(holder.getCode(), -1);
            if (code == -1) {
                throw new NDSException("仅退款应收调整单服务调用失败1");
            } else {
                //更新中间表生成应收调整单状态为1
                IpBTaobaoRefund ipBTaobaoRefund = new IpBTaobaoRefund();
                ipBTaobaoRefund.setIsgenincidental(1);
                ipBTaobaoRefund.setRefundId(taobaoRefund.getRefundId());
                this.updateTaobaoRefundOrder(ipBTaobaoRefund);
                //更新状态为已转化
                String remark = SysNotesConstant.SYS_REMARK28;
                boolean flag = bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);
                return flag;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 仅退款应收调整单服务调用失败", e);
            throw new NDSException(e);
        }
    }

    /**
     * 不存在原单
     */
    public boolean noOriginalOrder(IpBTaobaoRefund taobaoRefund,
                                   boolean isEffectiveOrder, List<OcBOrder> ocBOrder) {
        IpTaobaoRefundService bean = ApplicationContextHandle.getBean(IpTaobaoRefundService.class);
        if (CollectionUtils.isEmpty(ocBOrder)) {
            if (isEffectiveOrder) {
                String remark = SysNotesConstant.SYS_REMARK4;
                bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);
                return true;
            }
            Date date = new Date();
            //判断退单时间是否超过三天
            Date created = taobaoRefund.getCreated();
            Long threeDays = 3 * 24 * 60 * 60 * 1000L + created.getTime();
            if (threeDays < date.getTime()) {

                String remark = SysNotesConstant.SYS_REMARK2;
                bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);
                return true;
            }
            //找不到对应的原单,更新转换状态为 0 ,添加系统备注
            String remark = SysNotesConstant.SYS_REMARK1;
            bean.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                    remark, taobaoRefund);
            return true;
        }
        return false;
    }

    /**
     * 生成退单成功之后更新订单的退货状态为 退货中
     *
     * @param ocBOrder
     */
    public void updateOrderReturnStatus(OcBOrder ocBOrder, IpBTaobaoRefund taobaoRefund) {
        String sysRemark = SysNotesConstant.SYS_REMARK29;
        boolean b = this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), sysRemark, taobaoRefund);
        if (b) {
            OcBOrder order = new OcBOrder();
            order.setId(ocBOrder.getId());
            order.setReturnStatus(1); //退货中
            omsOrderService.updateOrderInfo(order);
        }
    }


    /**
     * 存在原始订单 并且订单状态为“仓库发货，平台发货，交易完成”，继续走下一步转换过程
     * 第三步 判断金额是否相等   返回true时相等 调用 保存服务 false是调用仅退款应收调整单服务
     *
     * @param taobaoRefundRelation 退款中间表关系
     * @return 退款金额是否相等
     */
    public boolean refundAmountIsEqual(IpTaobaoRefundRelation taobaoRefundRelation) {
        //判断金额是否相等
        boolean amtBalance = returnOrderTransferUtil.isAmtBalance(taobaoRefundRelation);
        return amtBalance;
    }


    public IpBTaobaoRefund selectIpBTaobaoRefund(String returnId) {
        return ipBTaobaoRefundMapper.selectTaobaoByRefundId(returnId);

    }

    public int updateIpBTaobaoRefund(IpBTaobaoRefund ipBTaobaoRefund) {
        return ipBTaobaoRefundMapper.updateById(ipBTaobaoRefund);
    }

    /**
     * @param transferOrderStatus 装换状态
     * @return 状态是否更新成功
     */
    public boolean updateRefundIsTrans(TransferOrderStatus transferOrderStatus,
                                       IpBTaobaoRefund taobaoRefund) {
        IpTaobaoRefundService bean = ApplicationContextHandle.getBean(IpTaobaoRefundService.class);

        boolean flag = bean.updateReturnOrder(transferOrderStatus.toInteger(),
                "", taobaoRefund);
        return flag;
    }

    /**
     * 出现异常时更新状态并插入日志
     *
     * @param taobaoRefund
     * @return
     */
    public boolean updateRefundIsTransError(IpBTaobaoRefund taobaoRefund, String error) {
        IpTaobaoRefundService bean = ApplicationContextHandle.getBean(IpTaobaoRefundService.class);

        String sysRemark = SysNotesConstant.SYS_REMARK0;
        //异常信息超过500 截取500
        String str = sysRemark + error;
        if (str.length() > 500) {
            str = str.substring(0, 500);
        }
        boolean flag = bean.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                str, taobaoRefund);
        return flag;
    }


    /**
     * 订单关闭服务
     * <p>
     * 查找退换货管理中的退货单，如果不存在退货单，则直接更新退单转换状态为已转换，如果存在，
     * 则判断退货单的状态，当退货单状态为“等待退货入库”时，直接取消退货单，更新退单转换状态为已转换，
     * 如果不是，则不进行处理，更新退单转换状态为已转换，添加系统备注：“退货单的状态不满足，不能取消退货单”
     */
    public void refundOrderClose(User operateUser, IpBTaobaoRefund taobaoRefund, OcBOrder order) throws IOException {
        IpTaobaoRefundService bean = ApplicationContextHandle.getBean(IpTaobaoRefundService.class);

        // 是否存在退款单号
        String refundId = taobaoRefund.getRefundId();
        //    Long returnId = omsReturnOrderService.isExistReturnOrder(refundId);
        List<Long> existReturnOrder = ES4ReturnOrder.isExistReturnOrder(refundId);

        if (CollectionUtils.isNotEmpty(existReturnOrder)) {
            //存在退款商品
            OcBReturnOrder ocBReturnOrder = omsReturnOrderService.selectReturnOrderById(existReturnOrder.get(0));
            if (ocBReturnOrder != null) {
                //获取退款表的退款状态
                //退换货状态,20等待退货入库，30等待售后确认，50完成，60取消',
                Integer returnStatus = ocBReturnOrder.getReturnStatus();
                // TaobaoReturnOrderExt.ReturnStatus.WAIT_RETURN_LIBRARY.getCode()
                if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus)) {
                    //更新状态为退货取消
//                    ocBReturnOrder.setReturnStatus(TaobaoReturnOrderExt.ReturnStatus.RETURN_CANCEL.getCode());
//                    //推送es
//                    returnOrderTransferUtil.updateOperator(ocBReturnOrder, operateUser);
//                    omsReturnOrderService.updateOcBReturnOrder(ocBReturnOrder);
                    JSONObject jsonObject = new JSONObject();
                    JSONArray jsonArray = new JSONArray();
                    jsonArray.add(existReturnOrder.get(0));
                    jsonObject.put("ids", jsonArray);
                    ValueHolderV14 holderV14 = ocCancelChangingOrRefundService.orRefundService(jsonObject, operateUser, Boolean.FALSE);
                    int code = Tools.getInt(holderV14.getCode(), -1);
                    if (code == 0) {
                        //更新中间表转换转状态
                        bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                "", taobaoRefund);
                        OcBOrder ocBOrder = new OcBOrder();
                        ocBOrder.setId(order.getId());
                        ocBOrder.setReturnStatus(0); //无退货
                        omsOrderService.updateOrderInfo(ocBOrder);
                        //插入日志
                        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
                        ocBReturnOrderLog.setLogMessage(SysNotesConstant.CANCEL_LOG_MESSAGE);
                        ocBReturnOrderLog.setLogType(SysNotesConstant.CANCEL_LOG_TYPE);
                        ocBReturnOrderLog.setOcBReturnOrderId(existReturnOrder.get(0));
                        returnOrderTransferUtil.saveSysLog(ocBReturnOrderLog, operateUser);
                        ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
                    } else {
                        Date date = new Date();
                        //判断退单时间是否超过三天
                        Date created = taobaoRefund.getCreated();
                        Long threeDays = 3 * 24 * 60 * 60 * 1000L + created.getTime();
                        String remark = holderV14.getMessage();
                        if (threeDays < date.getTime()) {
                            bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                    remark, taobaoRefund);
                            return;
                        }
                        bean.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                                remark, taobaoRefund);
                    }
                } else {
                    String remark = SysNotesConstant.SYS_REMARK7;
                    bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, taobaoRefund);
                }
            } else {
                String remark = SysNotesConstant.SYS_REMARK33;
                bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoRefund);
            }
        } else {
            String remark = SysNotesConstant.SYS_REMARK33;
            bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, taobaoRefund);
        }
    }


    public boolean updateReturnOrder(int istrans,
                                     String remark, IpBTaobaoRefund taobaoRefund) {
        return this.updateReturnOrderInfo(istrans, remark, taobaoRefund, null);

    }


    public boolean updateReturnOrderInfo(int istrans,
                                         String remark, IpBTaobaoRefund taobaoRefund, Long agStatus) {
        try {
            JSONObject object = new JSONObject();
            object.put("refund_id", taobaoRefund.getRefundId());
            object.put("istrans", istrans);
            object.put("sysremark", remark);
            object.put("modifieddate", new Date());
            object.put("transdate", new Date());
            Integer transFailReason = taobaoRefund.getTransFailReason();
            if (transFailReason != null) {
                object.put("trans_fail_reason", transFailReason);
            }
            if (agStatus != null) {
                object.put("ag_status", agStatus);
                ipBTaobaoRefundMapper.updateRefundOrderByStatus(object);
            } else {
                ipBTaobaoRefundMapper.updateRefundOrder(object);
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新退货中间表失败,RefundId:{}", taobaoRefund.getRefundId(), e);
            throw new NDSException(e);
        }
        return false;
    }
    public boolean updateTaobaoRefundOrder(IpBTaobaoRefund taobaoRefund) {
        try {
            JSONObject object = new JSONObject();
            object.put("refund_id", taobaoRefund.getRefundId());
            object.put("isgenincidental", taobaoRefund.getIsgenincidental());
            int i = ipBTaobaoRefundMapper.updateRefundOrder(object);
            //推送es
            if (i > 0) {
                try {
//                    IpBTaobaoRefund ipBTaobaoRefund =
//                            ipBTaobaoRefundMapper.selectTaobaoByRefundId(taobaoRefund.getRefundId());
//                    SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOREFUND,
//                            TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOREFUND,
//                            ipBTaobaoRefund, ipBTaobaoRefund.getId());
                    return true;
                } catch (Exception e) {
                    log.error(this.getClass().getName() + "推送es失败", e);
                }
            }

        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新退货中间表失败,RefundId:{}", taobaoRefund.getRefundId(), e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * 查询未转换的RefundId数据
     *
     * @param node             分库名
     * @param name             分库表名
     * @param size             查询数量
     * @param isTrans          转换状态
     * @param lessThanTransCnt 小于转换的次数
     * @param timestamp        转换时间戳
     * @param minutes          查询n分钟之前的数据(值为0时不作为查询条件)
     * @return OrderNo集合
     */
    public List<String> selectDynamicRefundId(String name, int size,
                                              int isTrans, int lessThanTransCnt, int timestamp, int minutes) {
        return ipBTaobaoRefundMapper.selectDynamicRefundId(name, size, isTrans, lessThanTransCnt, timestamp,
                minutes);
    }

    /**
     * 更新Refund补偿转单的时间戳
     *
     * @param node 分库名
     * @param name 分库表名
     * @param refundIds RefundId 集合
     * @param timestamp 转换时间戳
     * @return
     */
    public int updateDynamicRefundTimeStamp(String node, String name, List<String> refundIds, int timestamp) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0, l = refundIds.size(); i < l; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(refundIds.get(i));
        }
        return ipBTaobaoRefundMapper.updateDynamicRefundTimeStamp(node, name, sb.toString(), timestamp);
    }
}
