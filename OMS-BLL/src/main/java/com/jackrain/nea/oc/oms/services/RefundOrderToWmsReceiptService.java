package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.nums.ReturnOrderNodeEnum;
import com.jackrain.nea.oc.oms.util.WmsUserCreateUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.resource.SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 零售退传WMS回执服务
 *
 * @author: 郑立轩
 * @since: 2019/5/7
 * create at : 2019/5/7 13:12
 */
@Slf4j
@Component
public class RefundOrderToWmsReceiptService {
    @Autowired
    private OcBReturnOrderMapper mapper;
    @Autowired
    private OmsReturnOrderService returnOrderService;
    private final static String mehodName = "taobao.qimen.returnorder.create";
    @Autowired
    private WmsUserCreateUtil wmsUserCreateUtil;
    @Autowired
    private OcBReturnOrderNodeRecordService nodeRecordService;

    public void updateStatus(String messageBody) {
        try {
            JSONObject object = JSONObject.parseObject(messageBody);
            String method = object.getString("method");
            //根据方法名 处理message
            if (mehodName.equalsIgnoreCase(method)) {
                JSONArray data = object.getJSONArray("data");
                for (int i = 0; i < data.size(); i++) {
                    String code = data.getJSONObject(i).getString("code");
                    //退单id
                    // @20200715 已经不再是id，发送是已经把id修改为了billNo，所以这里应该取的也是billNo
                    // Long orderNo = data.getJSONObject(i).getLong("orderNo");
                    String billNo = data.getJSONObject(i).getString("orderNo");
                    //WMS单据编号
                    String returnOrderId = data.getJSONObject(i).getString("returnOrderId");

                    // @20200715 变更查询逻辑，依据billNo查询
                    // OcBReturnOrder ocBReturnOrder = mapper.selectById(orderNo);
                    // OcBReturnOrder ocBReturnOrder = mapper.selectOne(new LambdaQueryWrapper<OcBReturnOrder>().eq(OcBReturnOrder::getBillNo, billNo));
                    // 非ID查询，要走ES
                    OcBReturnOrder ocBReturnOrder = returnOrderService.selectReturnOrderByBillNo(billNo);

                    if (ocBReturnOrder != null
                            && ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(ocBReturnOrder.getReturnStatus())
                            && WmsWithdrawalState.PASS.toInteger().equals(ocBReturnOrder.getIsTowms())) {
                        // @20200715 由于id -> billNo，但是日志记录用的是id，所以这里需要找回id
                        Long orderNo = ocBReturnOrder.getId();

                        if ("0".equals(code)) {
                            //
                            /* 已传WMS
                             * 如果返回成功，则更新零售退货单【传WMS状态】为【传WMS成功】，【WMS单据编号】字段值更新取值回执服务中的returnOrderId字段
                             * 20190709 修改
                             */
                            ocBReturnOrder.setIsTowms(WmsWithdrawalState.YES.toInteger());
                            ocBReturnOrder.setModifieddate(new Timestamp(System.currentTimeMillis()));
                            //wms编号
                            ocBReturnOrder.setWmsBillNo(returnOrderId);
                            returnOrderService.updateOcBReturnOrder(ocBReturnOrder);
                            returnOrderService.saveAddOrderReturnLog(orderNo,
                                    "退货单传WMS成功", "零售退货单传WMS", wmsUserCreateUtil.initWmsUser());

                            //传wms成功埋点
                            nodeRecordService.insertByNode(ReturnOrderNodeEnum.OMS_RECEIVE_WMS_SUCCESS_TIME, new Date(),
                                    ocBReturnOrder.getId(), SystemUserResource.getRootUser());

                        } else {
                            ocBReturnOrder.setIsTowms(WmsWithdrawalState.FAIL.toInteger());
                            String failReason = data.getJSONObject(i).getString("message");
                            if (StringUtils.isNotBlank(failReason) && failReason.length() > 200) {
                                failReason = failReason.substring(0, 200);
                            }
                            ocBReturnOrder.setWmsFailreason(failReason);
                            ocBReturnOrder.setModifieddate(new Timestamp(System.currentTimeMillis()));
                            //wms失败次数+1
                            Long wmsFail = ocBReturnOrder.getQtyWmsFail();
                            if (null == wmsFail) {
                                wmsFail = 1L;
                            } else {
                                wmsFail = wmsFail + 1;
                            }
                            ocBReturnOrder.setQtyWmsFail(wmsFail);
                            returnOrderService.updateOcBReturnOrder(ocBReturnOrder);
                            returnOrderService.saveAddOrderReturnLog(orderNo,
                                    "退货单传WMS失败,原因:" + failReason, "零售退货单传WMS", wmsUserCreateUtil.initWmsUser());
                        }
                       /* try {
                            SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, ocBReturnOrder, ocBReturnOrder.getId());
                        } catch (Exception ex) {
                            log.debug("日志服务：推送ES异常" + ex);
                        }*/
                    }
                }

            }
        } catch (Exception e) {
            log.error(LogUtil.format("退单传WMS回执执行失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }


    }


}
