package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jackrain.nea.st.model.StCAppointExpressStrategy;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @ClassName StCAppointExpressStrategyService
 * @Description 指定快递策略服务
 * <AUTHOR>
 * @Date 2024/4/12 15:44
 * @Version 1.0
 */
public interface StCAppointExpressStrategyService extends IService<StCAppointExpressStrategy> {
    Boolean updateActive(User user, List<Long> objIds, String isActive);

    StCAppointExpressStrategy getByShopId(Long shopId);

    StCAppointExpressStrategy getCommonStrategy();
}
