package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.CpCShopChannelType;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.AcScRpcService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 渠道类型为分销类型的店铺，店铺中出售的商品包含：代理商品（多个代理商）、自用商品；从而导致一个订单中可能包含多个代理商的商品
 * 和自用商品，对于乔丹而言，只需处理乔丹代理的商品，需把其他品牌代理商品和自用商品自动过滤。
 *
 * @date 2019/8/14
 * @author: ming.fz
 */

@Component
@Slf4j
public class OrderFilteringPolicyService {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    AcScRpcService acScRpcService;

    @Autowired
    StRpcService stRpcService;

    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    OmsOrderCancellationService omsOrderCancellationService;

    @Autowired
    OmsOrderService omsOrderService;

    @Autowired
    OmsOrderLogService omsOrderLogService;

    /**
     * 经销商自有商品策略
     *
     * @param orderParamInfo 订单
     * @return 返回过滤后的订单
     */
    @Transactional
    public OcBOrderParam distributionStrategy(OcBOrderParam orderParamInfo) {
        User rootUser = SystemUserResource.getRootUser();
        OcBOrder ocBOrder = orderParamInfo.getOcBOrder();
        List<OcBOrderItem> orderItemLists = orderParamInfo.getOrderItemList();

        Long orderId = ocBOrder.getId();

        //店铺id
        Long cpCShopId = ocBOrder.getCpCShopId();
        //付款时间
        Date payTime = ocBOrder.getPayTime();
        //商品id集合
        List<Long> skuIds = getSkuIds(orderItemLists);


        CpShop cpShop = cpRpcService.selectShopById(cpCShopId);
        //获取渠道类型
        String channelType = cpShop.getChannelType();
        if (CpCShopChannelType.DISTRIBUTION.getVal().equals(channelType)) {
            //分销  经销商自有商品策略服务
            ValueHolderV14<List<Long>> listValueHolderV14 = stRpcService.voidSgPhyOutNotices(cpCShopId, payTime, skuIds);
            if (listValueHolderV14 == null || listValueHolderV14.getCode() == ResultCode.FAIL) {
                //调用失败，结束
                throw new NDSException("调用经销商自有商品策略服务失败！" + JSONObject.toJSONString(listValueHolderV14));
            }

            /*
             3.	判断返回的经销商自有商品信息；
                	若商品信息为空（不存在经销商自有商品），则调用【代销分销策略服务】。
                	若商品信息存在部分（存在部分经销商自有商品），则做以下操作：
                    	对经销商自有商品删除操作。
                    	再调用【代销分销策略服务】。
              */
            //经销商自有商品
            List<Long> dataIds = listValueHolderV14.getData();
            if (dataIds == null || dataIds.size() < 1) {
                //调用【代销分销策略服务】
                return this.theCommissionDistribution(cpCShopId, payTime, orderParamInfo, rootUser);

            } else {

                //bl 标识返回的经销商是否包含当前订单的所有商品
                boolean bl = true;
                for (int i = 0; i < skuIds.size(); i++) {
                    int skuIdsIndex = dataIds.indexOf(skuIds.get(i));
                    if (skuIdsIndex == -1) {
                        bl = false;
                        break;
                    }
                }

                //判断是否都是经销商商品
                if (dataIds.size() == skuIds.size() || bl) {
                    //作废订单
                    callDoInvoildOutOrder(ocBOrder);
                    //明细置为不可用
                    for (int f = 0; f < orderItemLists.size(); f++) {
                        orderItemUpdateIsactive(orderItemLists.get(f), rootUser);
                    }

                    List<String> skuEcodes = getSkuEcodes(orderItemLists, skuIds);

                    //日志记录
                    logService(ocBOrder, OrderLogTypeEnum.DEALER_STRATEGY_DELETE_SKU,
                            "分销类型店铺的经销商自有商品" + skuEcodes.toString() + "条码删除", rootUser);
                    //主表金额重新计算
                    orderParamInfo.setOrderItemList(null); //订单已经作废掉了金额全部置为0，将订单明细置为null即可
                    updateOrderPrice(orderParamInfo);
                    return null;
                }

                JSONObject update = new JSONObject();
                //删除经销商自有商品排除组合商品
                for (int i = 0, size = dataIds.size(); i < size; i++) {
                    //获取经销商商品
                    Long skuId = dataIds.get(i);
                    int skuIdsIndex = skuIds.indexOf(skuId);
                    //判断订单是否存在经销商商品
                    if (skuIdsIndex != -1) {
                        //删除入参集合中经销商自有商品
                        for (int f = 0; f < skuIds.size(); f++) {
                            OcBOrderItem orderItemInfo = orderItemLists.get(f);
                            if (orderItemInfo.getPsCSkuId().equals(skuId)) {
                                orderItemUpdateIsactive(orderItemInfo, rootUser);
                            }
                        }
                    }
                }

                orderParamInfo.setOrderItemList(ocBOrderItemMapper.selectOrderItemList(orderId));
                //主表金额重新计算
                orderParamInfo = updateOrderPrice(orderParamInfo);

                //获取删除商品条码
                List<String> skuEcodes = getSkuEcodes(orderItemLists, dataIds);

                //日志记录
                logService(ocBOrder, OrderLogTypeEnum.DEALER_STRATEGY_DELETE_SKU,
                        "分销类型店铺的经销商自有商品" + skuEcodes.toString() + "条码删除", rootUser);
                //调用【代销分销策略服务】
                return this.theCommissionDistribution(cpCShopId, payTime, orderParamInfo, rootUser);
            }
        }

        return orderParamInfo;
    }

    /**
     * 获取当前商品的sku
     *
     * @param orderItemLists
     * @return
     */
    private List<Long> getSkuIds(List<OcBOrderItem> orderItemLists) {
        List<Long> skuIds = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : orderItemLists) {
            skuIds.add(ocBOrderItem.getPsCSkuId());
        }
        return skuIds;
    }

    /**
     * 获取当前商品的sku
     *
     * @param orderItemLists
     * @return
     */
    private List<String> getSkuEcodes(List<OcBOrderItem> orderItemLists, List<Long> skuIds) {

        List<String> skuEcode = new ArrayList<>();

        for (Long skuId : skuIds) {
            for (OcBOrderItem orderItemList : orderItemLists) {
                if (skuId.equals(orderItemList.getPsCSkuId())) {
                    skuEcode.add(orderItemList.getPsCSkuEcode());
                    break;
                }
            }
        }

        return skuEcode;
    }


    /**
     * 更新商品为不可用
     *
     * @param ocBOrderItem
     * @param user
     */
    private void orderItemUpdateIsactive(OcBOrderItem ocBOrderItem, User user) {
        JSONObject update = new JSONObject();
        update.put("id", ocBOrderItem.getId());
        update.put("oc_b_order_id", ocBOrderItem.getOcBOrderId());
        update.put("isactive", "N");
        update.put("modifierename", user.getEname());
        update.put("modifiername", user.getName());
        update.put("modifieddate", new Date(System.currentTimeMillis()));
        OcBOrderItem orderItem = new OcBOrderItem();
        List<OcBOrderItem> orderItems = new ArrayList<>();
        orderItem.setId(ocBOrderItem.getId());
        orderItem.setOcBOrderId(ocBOrderItem.getOcBOrderId());
        orderItem.setIsactive("N");
        orderItem.setModifiername(user.getEname());
        orderItem.setModifierename(user.getEname());
        orderItem.setModifieddate(new Date(System.currentTimeMillis()));
        orderItems.add(orderItem);
        int record = ocBOrderItemMapper.updateRecord(update);
        if (record > 0) {
            //es推送
            //esPush(orderItems);
        } else {
            throw new NDSException("经销商自有商品策略订单商品isactive更新失败:" + update);
        }
    }

    /**
     * 调用
     *
     * @param ocBOrder
     * @return
     */
    private void callDoInvoildOutOrder(OcBOrder ocBOrder) {
        //调用作废订单服务
        //作废原单
        try {
            //做个补偿机制阿里drds库可能出现更新失败异常 补偿5次 再出异常 抛异常
            for (int i = 0; i < 5; i++) {
                boolean b = omsOrderCancellationService.doOrderInvalid(ocBOrder, SystemUserResource.getRootUser());
                if (b) {
                    break;
                }

                if (i == 4) {
                    throw new NDSException("调用作废订单服务失败! 入参：" + JSONObject.toJSONString(ocBOrder));
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("调用作废订单服务失败,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            throw new NDSException("调用作废订单服务失败! 入参：" + JSONObject.toJSONString(ocBOrder) + ex);
        }
    }

    /**
     * 代销分销商品策略
     *
     * @param cpCShopId   店铺id
     * @param paymentDate 付款时间
     * @return 返回三个值 null：调用服务失败  长度为0的集合：直接不用转单  长度大于0的集合：转单只转此集合中的明细
     */

    private OcBOrderParam theCommissionDistribution(Long cpCShopId, Date paymentDate, OcBOrderParam orderInfo, User user) {

        OcBOrder ocBOrder = orderInfo.getOcBOrder();
        List<OcBOrderItem> orderItemLists = orderInfo.getOrderItemList();
        //商品id集合
        List<Long> skuIds = getSkuIds(orderItemLists);
        Long orderId = ocBOrder.getId();

       // ValueHolderV14<List<Long>> listValueHolderV14 = acScRpcService.selectSkuByDistribution(cpCShopId, paymentDate, skuIds);

//        if (listValueHolderV14 == null || listValueHolderV14.getCode() == ResultCode.FAIL) {
//            //调用服务失败 结束
//            throw new NDSException("调用代销分销商品策略服务失败！" + JSONObject.toJSONString(listValueHolderV14));
//        }

        List<Long> dataSkuIds = null;//listValueHolderV14.getData();
        //判断是有代销分销商品 没有作废订单    有 删除非代销经销的商品
        if (dataSkuIds == null || dataSkuIds.size() < 1) {
            //作废订单
            callDoInvoildOutOrder(ocBOrder);
            //明细置为不可用
            for (int f = 0; f < orderItemLists.size(); f++) {
                orderItemUpdateIsactive(orderItemLists.get(f), user);
            }

            //获取删除商品条码
            List<String> skuEcodes = getSkuEcodes(orderItemLists, skuIds);

            //日志记录
            logService(ocBOrder, OrderLogTypeEnum.SELLGOODS_STRATEGY_DELETE_SKU,
                    "分销类型店铺的非代销分销商品" + skuEcodes.toString() + "条码删除", user);
            //主表金额重新计算
            orderInfo.setOrderItemList(null); //订单已经作废掉了金额全部置为0，将订单明细置为null即可
            updateOrderPrice(orderInfo);
            return null;
        } else {
            //删除非代销经销的商品
            //删除的sku
            List<Long> deleteSkuId = new ArrayList<>();
            //删除没有在返回值中的sku 并更新当前商品不可
            for (int i = 0; i < skuIds.size(); i++) {
                Long skuId = skuIds.get(i);
                int index = dataSkuIds.indexOf(skuId);
                if (index == -1) {
                    //修改当前订单sku为不可用
                    for (int f = 0; f < orderItemLists.size(); f++) {
                        OcBOrderItem ocBOrderItem = orderItemLists.get(f);
                        if (ocBOrderItem.getPsCSkuId().equals(skuId)) {
                            orderItemUpdateIsactive(ocBOrderItem, user);
                            deleteSkuId.add(skuId);
                        }
                    }
                }
            }
            //日志记录
            if (deleteSkuId.size() > 0) {

                //获取删除商品条码
                List<String> skuEcodes = getSkuEcodes(orderItemLists, deleteSkuId);

                logService(ocBOrder, OrderLogTypeEnum.SELLGOODS_STRATEGY_DELETE_SKU,
                        "分销类型店铺的非代销非分销商品" + skuEcodes.toString() + "条码删除", user);
            }
            orderInfo.setOrderItemList(ocBOrderItemMapper.selectOrderItemList(orderId));
            return updateOrderPrice(orderInfo);
        }

    }


    public OcBOrderParam updateOrderPrice(OcBOrderParam orderInfo) {
        Long id = orderInfo.getOcBOrder().getId();
        if (upOrderPrice(orderInfo)) {
            orderInfo.setOcBOrder(ocBOrderMapper.selectByID(id));
            return orderInfo;
        } else {
            throw new NDSException("订单OrderId" + id + "调用策略服务商品去除后订单重新计算金额保存失败");
        }
    }

    /**
     * 订单主表金额重新计算
     *
     * @param orderInfo
     * @return
     */
    public boolean upOrderPrice(OcBOrderParam orderInfo) {
        OcBOrder orderDto = orderInfo.getOcBOrder();
        List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //重新计算商品数量
        BigDecimal normalTotal = BigDecimal.ZERO;
        //重新计算商品总额
        BigDecimal priceListTotal = BigDecimal.ZERO;
        //指定商品优惠金额
        BigDecimal amtDiscountTotal = BigDecimal.ZERO;
        //平摊金额之和
        BigDecimal orderSplitAmtTotal = BigDecimal.ZERO;
        //代销结算金额
        BigDecimal consignmentTotal = BigDecimal.ZERO;
        //应收金额求和
        BigDecimal receivableTotal = BigDecimal.ZERO;
        //调整金额之和
        BigDecimal adjustTotal = BigDecimal.ZERO;

        if (orderItemList != null) {
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                //商品数量计算
                normalTotal = normalTotal.add(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty());
                //商品总额计算
                priceListTotal = priceListTotal.add((ocBOrderItem.getPriceList() == null ? BigDecimal.ZERO : ocBOrderItem.getPriceList()).multiply(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty()));
                //优惠金额计算
                amtDiscountTotal = amtDiscountTotal.add(ocBOrderItem.getAmtDiscount() == null ? BigDecimal.ZERO : ocBOrderItem.getAmtDiscount());
                //平摊金额求和
                orderSplitAmtTotal = orderSplitAmtTotal.add(ocBOrderItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getOrderSplitAmt());
                //代销结算金额求和
                consignmentTotal = consignmentTotal.add((ocBOrderItem.getDistributionPrice() == null ? BigDecimal.ZERO : ocBOrderItem.getDistributionPrice()).multiply(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty()));
                //应收金额求和
                receivableTotal = receivableTotal.add((ocBOrderItem.getPriceList() == null ? BigDecimal.ZERO : ocBOrderItem.getPriceList()).multiply(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty()));
                //调整金额之和
                adjustTotal = adjustTotal.add(ocBOrderItem.getAdjustAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getAdjustAmt());
            }
        }
        //订单总额计算
        BigDecimal orderAmtTotal = priceListTotal.add(orderDto.getShipAmt() == null ? BigDecimal.ZERO : orderDto.getShipAmt()).add(adjustTotal).add(orderDto.getServiceAmt() == null
                ? BigDecimal.ZERO : orderDto.getServiceAmt()).subtract(amtDiscountTotal).subtract(orderSplitAmtTotal);
        //更新正常订单表数据
        OcBOrder oneOcbOrder = new OcBOrder();
        oneOcbOrder.setId(orderDto.getId());
        //	【商品数量】：取明细中所有数量的合计；
        oneOcbOrder.setQtyAll(normalTotal);
        //	【商品总额】：取明细中所有吊牌金额的合计；
        oneOcbOrder.setProductAmt(priceListTotal);
        //	【商品优惠金额】：取明细中指定商品优惠金额
        oneOcbOrder.setProductDiscountAmt(amtDiscountTotal);
        //	【订单优惠金额】：整单优惠金额
        oneOcbOrder.setOrderDiscountAmt(orderSplitAmtTotal);
        //	【订单总额】：商品总额+物流费用+调整金额+服务费-商品优惠金额-订单优惠金额
        oneOcbOrder.setConsignAmt(consignmentTotal);
        //	【代销结算金额】：代销结算价*数量
        oneOcbOrder.setAmtReceive(receivableTotal);
        //	【应收金额】：商品标准价*数量
        oneOcbOrder.setOrderAmt(orderAmtTotal);
        //	【已支付金额】（已收金额）：等于计算后的订单总额
        oneOcbOrder.setReceivedAmt(orderAmtTotal);
        //	【调整金额之和】：取明细中所有调整金额的合计；
        oneOcbOrder.setAdjustAmt(adjustTotal);
        return omsOrderService.updateOrderInfo(oneOcbOrder);
    }

    /**
     * @param ocBOrder
     * @param orderLogTypeEnum
     * @param logContent
     * @param user
     */
    private void logService(OcBOrder ocBOrder, OrderLogTypeEnum orderLogTypeEnum,
                            String logContent, User user) {

        /**
         *  调用日志服务
         *  参数 订单id、订单编号、订单类型、日志信息、参数、错误信息
         */
        try {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), orderLogTypeEnum.getKey(),
                    logContent, "", "", user);
        } catch (Exception e) {
            log.error(LogUtil.format("调用日志服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), orderLogTypeEnum.getKey(),
                    "订单明细置为不可用", JSONObject.toJSONString(ocBOrder), e.toString(), user);
        }

    }


//    private void esPush(List<OcBOrderItem> ocBOrderItems) {
//        try {
//            SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
//                    OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, ocBOrderItems, "OC_B_ORDER_ID");
//        } catch (IOException e) {
//            if (log.isDebugEnabled()) {
//                log.error("mfz-订单-->" + JSONObject.toJSONString(ocBOrderItems) + "策略商品过滤后ES推送失败！");
//            }
//        }
//    }

}
