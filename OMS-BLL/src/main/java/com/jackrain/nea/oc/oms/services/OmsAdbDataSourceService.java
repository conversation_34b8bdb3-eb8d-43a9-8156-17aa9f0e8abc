package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * OMS数据源adb
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OmsAdbDataSourceService {

    private final String DATEFORMAT = "yyyy-MM-dd HH:mm:ss";
    private final String BEGIN_TIME = "2023-01-01 00:00:00";

    @Resource
    private OcBOrderMapper ocBOrderMapper;

    /**
     * 查询周期购进销存报表需要计算的订单数据
     * <p>
     * 只返回平台单号!!!
     * <p>
     * 创建时间大于23年
     * 排除系统作废&取消
     * 有效状态
     *
     * @param date
     * @param businessTypeCode
     * @return tid
     */
    @TargetDataSource(name = "adb")
    public IPage<OcBOrder> pageQueryNeedCalCycleOrders(Date date, String businessTypeCode, Page<OcBOrder> page) {
        LambdaQueryWrapper<OcBOrder> wrapper = Wrappers.lambdaQuery(new OcBOrder())
                .select(OcBOrder::getTid)
                .eq(OcBOrder::getBusinessTypeCode, businessTypeCode)
                .eq(OcBOrder::getIsactive, YesNoEnum.Y.getKey())
                //大于2023-01-01 00:00:00
                .gt(OcBOrder::getCreationdate, DateUtil.parse(BEGIN_TIME, DATEFORMAT))
                .lt(OcBOrder::getCreationdate, date)
                .notIn(OcBOrder::getOrderStatus, Lists.newArrayList(OmsOrderStatus.CANCELLED.toInteger(), OmsOrderStatus.SYS_VOID.toInteger()))
//                .ne(OcBOrder::getOrderSource, OcOrderTagEum.TAG_HAND.getVal())
                .orderByDesc(OcBOrder::getId);

        return ocBOrderMapper.selectPage(page, wrapper);
    }

}
