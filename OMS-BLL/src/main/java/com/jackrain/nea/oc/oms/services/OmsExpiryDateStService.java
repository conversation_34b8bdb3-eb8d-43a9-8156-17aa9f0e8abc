package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.AppointDimensionEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.ps.model.OmsProAttributeInfo;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgNewRpcService;
import com.jackrain.nea.st.model.StExpiryDateRelation;
import com.jackrain.nea.st.service.StExpirationDataService;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/6/21 下午2:06
 * @Version 1.0
 * <p>
 * 执行商品效期策略
 */
@Slf4j
@Component
public class OmsExpiryDateStService {

    private static final List<String> DEFAULT_MATERIAL = Lists.newArrayList("10800", "10801", "10802");
    @Autowired
    private StExpirationDataService stExpirationDataService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private SgNewRpcService sgNewRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;


    public boolean expiryDateStService(OcBOrderParam orderParam, User user, String logMessage) {
        OcBOrder ocBOrder = orderParam.getOcBOrder();
        //判断商品是否开启批次管理
        List<OcBOrderItem> orderItemList = orderParam.getOrderItemList();
        //新需求  有的话没开启批次管理的要清掉
        this.isCleanExpiryDate(orderItemList, ocBOrder, user);
        //判断toc或者奶卡订单

        // 执行订单会员标识匹配
        String sellerMark = ocBOrder.getSellerMemo();
        if (StringUtils.isNotEmpty(sellerMark)) {
            String memberMatchContent = businessSystemParamService.getMemberMatchContent();
            log.info(LogUtil.format("系统参数会员信息:{}, 卖家备注:{}", "系统参数会员信息"), memberMatchContent, sellerMark);
            if (StringUtils.isNotEmpty(memberMatchContent)) {
                String[] memberContentArr = memberMatchContent.split("/");
                for (String memberContent : memberContentArr) {
                    if (sellerMark.contains(memberContent)) {
                        OcBOrder updateOcBOrder = new OcBOrder();
                        updateOcBOrder.setId(ocBOrder.getId());
                        updateOcBOrder.setModifieddate(new Date());
                        updateOcBOrder.setIsMember(1);
                        ocBOrderMapper.updateById(updateOcBOrder);
                        // 设置为1  方便后续直接取
                        ocBOrder.setIsMember(1);
                        continue;
                    }
                }
            }
        }


        List<OcBOrderItem> itemList = orderItemList.stream().filter(p -> StringUtils.isEmpty(p.getExpiryDateRange())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemList)) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.MATCH_EXPIRY_DATE.getKey(), "商品已指定效期,不进行效期匹配", "", "", user);
            return false;
        }
        List<OcBOrderItem> orderItems = itemList.stream().filter(p -> p.getIsEnableExpiry() != null
                && p.getIsEnableExpiry() == 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItems)) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.MATCH_EXPIRY_DATE.getKey(), "商品未开启批次管理,不进行效期匹配", "", "", user);
            return false;
        }
        Map<Long, OcBOrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
        //匹配指定店铺
        List<ExpiryDate> expiryDateList = new ArrayList<>();
        boolean matchCommon = isMatchCommon(ocBOrder, orderItemList);
        if (matchCommon) {
            List<ExpiryDate> expiryDates = this.expiryDateStService(ocBOrder, orderItemMap);
            if (CollectionUtils.isNotEmpty(expiryDates)) {
                expiryDateList.addAll(expiryDates);
            }
            log.info(LogUtil.format("OmsExpiryDateStService.expiryShop expiryDates:{},orderItemMap:{}",
                    "OmsExpiryDateStService.expiryShop"),
                    JSONObject.toJSONString(expiryDateList), JSONObject.toJSONString(orderItemMap));
            if (!orderItemMap.isEmpty()) {
                List<ExpiryDate> expiryDateList1 = this.expiryDateStServiceCustomerGrouping(ocBOrder, orderItemMap);
                if (CollectionUtils.isNotEmpty(expiryDateList1)) {
                    expiryDateList.addAll(expiryDateList1);
                }
                log.info(LogUtil.format("OmsExpiryDateStService.expiryCustomer expiryDates:{},orderItemMap:{}",
                        "OmsExpiryDateStService.expiryCustomer"),
                        JSONObject.toJSONString(expiryDateList), JSONObject.toJSONString(orderItemMap));
            }

            if (!orderItemMap.isEmpty()) {
                //匹配会员策略
                List<ExpiryDate> expiryDateList1 = memberMatch(ocBOrder, orderItemMap);
                if (CollectionUtils.isNotEmpty(expiryDateList1)) {
                    expiryDateList.addAll(expiryDateList1);
                }
                log.info(LogUtil.format("OmsExpiryDateStService.expiryMember expiryDates:{},orderItemMap:{}",
                        "OmsExpiryDateStService.expiryMember"),
                        JSONObject.toJSONString(expiryDateList), JSONObject.toJSONString(orderItemMap));
            }
        }
        //匹配公共
        if (!orderItemMap.isEmpty()) {
            List<ExpiryDate> expiryDateList1 = expiryDateStServiceCommon(orderItemMap);
            if (CollectionUtils.isNotEmpty(expiryDateList1)) {
                expiryDateList.addAll(expiryDateList1);
                log.info(LogUtil.format("OmsExpiryDateStService.expiryCommon expiryDates:{},orderItemMap:{}",
                        "OmsExpiryDateStService.expiryCommon"),
                        JSONObject.toJSONString(expiryDateList), JSONObject.toJSONString(orderItemMap));
            }
        }
        if (!matchCommon) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.MATCH_EXPIRY_DATE.getKey(), "该类型订单只匹配公用效期策略", "", "", user);
        }
        //更新数据
        return this.updateItemExpiryDate(expiryDateList, ocBOrder, user, logMessage);
    }

    /**
     * 是否直接匹配业务类型
     *
     * @param ocBOrder
     * @param orderItemList
     * @return
     */
    private boolean isMatchCommon(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList) {
        String businessType = ocBOrder.getBusinessType();
        String gwSourceGroup = ocBOrder.getGwSourceGroup();
        if ("ZS03".equals(businessType) && ("38".equals(gwSourceGroup) || "39".equals(gwSourceGroup))) {
            OcBOrderItem orderItem = orderItemList.get(0);
            String psCSkuEcode = orderItem.getPsCSkuEcode();
            ProductSku skuInfo = psRpcService.selectProductSku(psCSkuEcode);
            if (skuInfo != null) {
                Map<String, OmsProAttributeInfo> proAttributeMap = skuInfo.getProAttributeMap();
                if (proAttributeMap != null && !proAttributeMap.isEmpty()) {
                    OmsProAttributeInfo info = proAttributeMap.get("M_DIM2_ID");
                    if (info != null) {
                        String ecode = info.getEcode();
                        if (StringUtils.isNotEmpty(ecode)) {
                            return DEFAULT_MATERIAL.contains(ecode);
                        }
                    }
                }
            }
        }
        return true;
    }


    private void isCleanExpiryDate(List<OcBOrderItem> orderItemList, OcBOrder ocBOrder, User user) {
        for (OcBOrderItem orderItem : orderItemList) {
            Integer isEnableExpiry = orderItem.getIsEnableExpiry();
            String expiryDateRange = orderItem.getExpiryDateRange();
            if ((isEnableExpiry == null || isEnableExpiry != 1)
                    && StringUtils.isNotEmpty(expiryDateRange)) {
                OcBOrderItem item = new OcBOrderItem();
                item.setId(orderItem.getId());
                item.setExpiryDateRange("");
                item.setOrderLabel("");
                item.setOcBOrderId(orderItem.getOcBOrderId());
                omsOrderItemService.updateOcBOrderItem(item, orderItem.getOcBOrderId());
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.MATCH_EXPIRY_DATE.getKey(), "条码:" + orderItem.getPsCSkuEcode() + "未开启批次管理,清空原有批次信息", "", "", user);
                orderItem.setExpiryDateRange("");
            }
        }
    }


    private List<ExpiryDate> memberMatch(OcBOrder ocBOrder, Map<Long, OcBOrderItem> orderItemMap) {

        // 非会员不匹配会员策略
        if (!ObjectUtil.equal(1, ocBOrder.getIsMember())) {
            return null;
        }

        List<ExpiryDate> expiryDateList = new ArrayList<>();
        Date payTime = ocBOrder.getPayTime();
        if (payTime == null) {
            payTime = ocBOrder.getPresaleDepositTime();
        }
        List<StExpiryDateRelation> relations = stExpirationDataService.selectExpirationMemberMatch(payTime);
        log.info(LogUtil.format("获取会员策略数据:{}", "获取会员策略数据"), JSONObject.toJSONString(relations));
        if (CollectionUtils.isEmpty(relations)) {
            return null;
        }
        //收集所有明细后按照指定维度分组
        Map<Integer, List<StCExpiryDateItem>> groupedItems = collectItemAndGroupAppointDimension(relations);
        log.info(LogUtil.format("OmsExpiryDateStService.memberMatch relations:{},groupedItems:{}",
                "OmsExpiryDateStService.memberMatch"),
                JSONObject.toJSONString(relations), JSONObject.toJSONString(groupedItems));
        List<ExpiryDate> expiryDates = expiryDate(orderItemMap, groupedItems);
        if (CollectionUtils.isNotEmpty(expiryDates)) {
            expiryDateList.addAll(expiryDates);
        }

        return expiryDateList;
    }

//
//    private List<ExpiryDate> sgOmsBigValidity(OcBOrder ocBOrder, Map<Long, OcBOrderItem> orderItemMap) {
//        List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
//        for (Long itemId : orderItemMap.keySet()) {
//            ocBOrderItems.add(orderItemMap.get(itemId));
//        }
//        Map<Long, List<OcBOrderItem>> skuMap = ocBOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuId));
//        List<SgOmsBigValiditySkuIdQueryResult> sgOmsBigValiditySkuIdQueryResults = sgNewRpcService.querySaStoreBigValidityBySkuIds(ocBOrder, ocBOrderItems);
//        List<ExpiryDate> expiryDateList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(sgOmsBigValiditySkuIdQueryResults)) {
//            for (SgOmsBigValiditySkuIdQueryResult sgOmsBigValiditySkuIdQueryResult : sgOmsBigValiditySkuIdQueryResults) {
//                if (sgOmsBigValiditySkuIdQueryResult.isBigValidity()) {
//                    Long skuId = sgOmsBigValiditySkuIdQueryResult.getPsCSkuId();
//                    List<OcBOrderItem> itemList = skuMap.get(skuId);
//                    //获取一个
//                    OcBOrderItem orderItem = itemList.get(0);
//                    //品类别
//                    Long mDim4Id = orderItem.getMDim4Id();
//                    if (mDim4Id == null) {
//                        continue;
//                    }
//                    List<PsCValidityDefinition> psCValidityDefinitions = psRpcService.selectPsCValidityDefinitionByType(mDim4Id, "4", skuId);
//                    if (CollectionUtils.isNotEmpty(psCValidityDefinitions)) {
//                        PsCValidityDefinition psCValidityDefinition = psCValidityDefinitions.get(0);
//                        for (OcBOrderItem item : itemList) {
//                            ExpiryDate expiryDate = new ExpiryDate();
//                            expiryDate.setItemId(item.getId());
//                            expiryDate.setOrderId(item.getOcBOrderId());
//                            //日期类型：1按天数 2按月份
//                            Integer dateType = psCValidityDefinition.getDateType();
//                            if (dateType == null || dateType == 1) {
//                                expiryDate.setExpiryDateType(2);
//                                expiryDate.setExpiryDateRange(psCValidityDefinition.getBeginParturitionNum() + "-" + psCValidityDefinition.getEndParturitionNum());
//                            } else {
//                                expiryDate.setExpiryDateType(1);
//                                //转换为日期
//                                LocalDate now = LocalDate.now();
//                                LocalDate startMonth = now.plusMonths(-psCValidityDefinition.getEndParturitionNum());
//                                String startDate = LocalDate.of(startMonth.getYear(), startMonth.getMonth(), 1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//                                LocalDate endMonth = now.plusMonths(-psCValidityDefinition.getBeginParturitionNum());
//                                String endDate = LocalDate.of(endMonth.getYear(), endMonth.getMonth(), endMonth.lengthOfMonth()).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//                                expiryDate.setExpiryDateRange(startDate + "-" + endDate);
//                            }
//                            expiryDate.setId(psCValidityDefinition.getId());
//                            expiryDate.setOrderLabel(item.getOrderLabel());
////                            expiryDate.setCheapestExpress(item.);
//                            orderItemMap.remove(item.getId());
//                            expiryDateList.add(expiryDate);
//                        }
//                    }
//                }
//            }
//        }
//        return expiryDateList;
//    }


    /**
     * @param ocBOrder 执行店铺的
     */
    public List<ExpiryDate> expiryDateStService(OcBOrder ocBOrder, Map<Long, OcBOrderItem> orderItemMap) {
        List<ExpiryDate> expiryDates = new ArrayList<>();
        Date payTime = ocBOrder.getPayTime();
        if (payTime == null) {
            payTime = ocBOrder.getPresaleDepositTime();
        }
        List<StExpiryDateRelation> relations = stExpirationDataService.selectExpirationData(ocBOrder.getCpCShopId(), payTime);
        if (CollectionUtils.isEmpty(relations)) {
            return null;
        }
        //收集所有明细后按照指定维度分组
        Map<Integer, List<StCExpiryDateItem>> groupedItems = collectItemAndGroupAppointDimension(relations);
        log.info(LogUtil.format("OmsExpiryDateStService.expiryDateStService relations:{},groupedItems:{}",
                "OmsExpiryDateStService.expiryDateStService"),
                JSONObject.toJSONString(relations), JSONObject.toJSONString(groupedItems));
        List<ExpiryDate> expiryDateList = expiryDate(orderItemMap, groupedItems);
        if (CollectionUtils.isNotEmpty(expiryDateList)) {
            expiryDates.addAll(expiryDateList);
        }
        return expiryDates;

    }

    /**
     * 收集商品效期策略明细并按照指定维度分组
     *
     * @param relations
     * @return
     */
    private Map<Integer, List<StCExpiryDateItem>> collectItemAndGroupAppointDimension(List<StExpiryDateRelation> relations) {
        return relations.stream()
                .filter(relation -> CollectionUtils.isNotEmpty(relation.getExpiryDateItems()))
                .flatMap(relation -> relation.getExpiryDateItems().stream())
                .collect(Collectors.groupingBy(StCExpiryDateItem::getAppointDimension));
    }

    public List<ExpiryDate> expiryDateStServiceCustomerGrouping(OcBOrder ocBOrder, Map<Long, OcBOrderItem> orderItemMap) {
        List<ExpiryDate> expiryDates = new ArrayList<>();
        Date payTime = ocBOrder.getPayTime();
        if (payTime == null) {
            payTime = ocBOrder.getPresaleDepositTime();
        }
        Long shopId = ocBOrder.getCpCShopId();
        CpShop cpShop = cpRpcService.selectCpCShopById(shopId);
        if (cpShop == null || cpShop.getCustomerGrouping() == null) {
            return null;
        }
        Integer customerGrouping = cpShop.getCustomerGrouping();
        List<StExpiryDateRelation> relations = stExpirationDataService.selectExpirationCustomerGrouping(customerGrouping, payTime);
        if (CollectionUtils.isEmpty(relations)) {
            return null;
        }
        //收集所有明细后按照指定维度分组
        Map<Integer, List<StCExpiryDateItem>> groupedItems = collectItemAndGroupAppointDimension(relations);
        log.info(LogUtil.format("OmsExpiryDateStService.expiryDateStServiceCustomerGrouping relations:{},groupedItems:{}",
                "OmsExpiryDateStService.expiryDateStServiceCustomerGrouping"),
                JSONObject.toJSONString(relations), JSONObject.toJSONString(groupedItems));
        List<ExpiryDate> expiryDateList = expiryDate(orderItemMap, groupedItems);
        if (CollectionUtils.isNotEmpty(expiryDateList)) {
            expiryDates.addAll(expiryDateList);
        }
        return expiryDates;

    }


    /**
     * 执行公用的
     *
     * @param orderItemMap
     * @return
     */
    public List<ExpiryDate> expiryDateStServiceCommon(Map<Long, OcBOrderItem> orderItemMap) {
        StExpiryDateRelation relation = stExpirationDataService.selectExpirationCommon();
        //收集明细后按照指定维度分组
        List<StCExpiryDateItem> expiryDateItems = relation.getExpiryDateItems();
        if (CollectionUtils.isNotEmpty(expiryDateItems)) {
            Map<Integer, List<StCExpiryDateItem>> groupedItems =
                    expiryDateItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getAppointDimension));
            log.info(LogUtil.format("OmsExpiryDateStService.expiryDateStService relation:{},groupedItems:{}",
                    "OmsExpiryDateStService.expiryDateStService"),
                    JSONObject.toJSONString(relation), JSONObject.toJSONString(groupedItems));
            return expiryDate(orderItemMap, groupedItems);
        }
        return null;
    }

    /**
     * 匹配商品效期策略明细
     *
     * @param orderItemMap
     * @param groupExpiryDateItems
     * @return
     */
    private List<ExpiryDate> expiryDate(Map<Long, OcBOrderItem> orderItemMap, Map<Integer, List<StCExpiryDateItem>> groupExpiryDateItems) {
        List<ExpiryDate> expiryDateList = new ArrayList<>();
        if (orderItemMap.isEmpty()) {
            return expiryDateList;
        }
        for (Long itemId : orderItemMap.keySet()) {
            OcBOrderItem orderItem = orderItemMap.get(itemId);
            ExpiryDate match = null;
            //执行主播id+sku编码
            List<StCExpiryDateItem> uidAndSkuCodeItems = groupExpiryDateItems.get(AppointDimensionEnum.UID_PRO_CODE.getKey());
            if (CollectionUtils.isNotEmpty(uidAndSkuCodeItems)) {
                Map<String, List<StCExpiryDateItem>> appointContentMap =
                        uidAndSkuCodeItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getAppointContent));
                String content = orderItem.getAnchorId();
                if (StringUtils.isNotEmpty(content)) {
                    match = matchUidAndCode(appointContentMap, orderItem, content);
                }
                if (match != null) {
                    expiryDateList.add(match);
                    continue;
                }
            }
            //执行规格ID+商品编码
            List<StCExpiryDateItem> specsIdProCodeItems = groupExpiryDateItems.get(AppointDimensionEnum.SPECS_PRO.getKey());
            if (CollectionUtils.isNotEmpty(specsIdProCodeItems)) {
                Map<String, List<StCExpiryDateItem>> appointContentMap =
                        specsIdProCodeItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getAppointContent));
                String content = orderItem.getSkuNumiid();
                if (StringUtils.isNotEmpty(content)) {
                    int i = content.indexOf("-");
                    if (i > 0) {
                        content = content.substring(i + 1);
                    }
                    match = matchTitleAndCode(appointContentMap, orderItem, content);
                    if (match != null) {
                        expiryDateList.add(match);
                        continue;
                    }
                }
            }
            //执行商品标题+商品编码
            List<StCExpiryDateItem> ptProTitleCodeItems = groupExpiryDateItems.get(AppointDimensionEnum.TITLE_PRO.getKey());
            if (CollectionUtils.isNotEmpty(ptProTitleCodeItems)) {
                Map<String, List<StCExpiryDateItem>> appointContentMap =
                        ptProTitleCodeItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getAppointContent));
                String content = orderItem.getTitle();
                match = matchTitleAndCode(appointContentMap, orderItem, content);
                if (match != null) {
                    expiryDateList.add(match);
                    continue;
                }
            }
            //执行平台商品ID
            List<StCExpiryDateItem> ptProIdItems = groupExpiryDateItems.get(AppointDimensionEnum.PLATFORM_PRO.getKey());
            if (CollectionUtils.isNotEmpty(ptProIdItems)) {
                Map<String, List<StCExpiryDateItem>> appointContentMap =
                        ptProIdItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getAppointContent));
                String content = orderItem.getNumIid();
                match = match(appointContentMap, orderItem, content);
                if (match != null) {
                    expiryDateList.add(match);
                    continue;
                }
            }
            //执行商品标题
            List<StCExpiryDateItem> proTitleItems = groupExpiryDateItems.get(AppointDimensionEnum.PRO_TITLE.getKey());
            if (CollectionUtils.isNotEmpty(proTitleItems)) {
                Map<String, List<StCExpiryDateItem>> appointContentMap =
                        proTitleItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getAppointContent));
                String content = orderItem.getTitle();
                match = matchTitle(appointContentMap, orderItem, content);
                if (match != null) {
                    expiryDateList.add(match);
                    continue;
                }
            }
            //执行商品编码
            List<StCExpiryDateItem> proCodeItems = groupExpiryDateItems.get(AppointDimensionEnum.PRO_CODE.getKey());
            if (CollectionUtils.isNotEmpty(proCodeItems)) {
                Map<String, List<StCExpiryDateItem>> appointContentMap =
                        proCodeItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getAppointContent));
                String content = orderItem.getPsCProEcode();
                match = match(appointContentMap, orderItem, content);
                if (match != null) {
                    expiryDateList.add(match);
                    continue;
                }
            }
            //执行四级分类
            List<StCExpiryDateItem> itemItems = groupExpiryDateItems.get(AppointDimensionEnum.CATEGORY.getKey());
            if (CollectionUtils.isNotEmpty(itemItems)) {
                Map<String, List<StCExpiryDateItem>> appointContentMap =
                        itemItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getAppointContent));
                String psCSkuEcode = orderItem.getPsCSkuEcode();
                ProductSku skuInfo = psRpcService.selectProductSku(psCSkuEcode);
                if (skuInfo != null) {
                    Map<String, OmsProAttributeInfo> proAttributeMap = skuInfo.getProAttributeMap();
                    if (proAttributeMap != null && !proAttributeMap.isEmpty()) {
                        OmsProAttributeInfo info = proAttributeMap.get("M_DIM6_ID");
                        if (info != null) {
                            String content = info.getEname();
                            match = match(appointContentMap, orderItem, content);
                            if (match != null) {
                                expiryDateList.add(match);
                                continue;
                            }
                        }
                    }
                }
            }
            //执行一级分类
            List<StCExpiryDateItem> itemClassItems = groupExpiryDateItems.get(AppointDimensionEnum.CATEGORY_CLASS.getKey());
            if (CollectionUtils.isNotEmpty(itemClassItems)) {
                Map<String, List<StCExpiryDateItem>> appointContentMap =
                        itemClassItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getAppointContent));
                String psCSkuEcode = orderItem.getPsCSkuEcode();
                ProductSku skuInfo1 = psRpcService.selectProductSku(psCSkuEcode);
                if (skuInfo1 != null) {
                    Map<String, OmsProAttributeInfo> proAttributeMap = skuInfo1.getProAttributeMap();
                    if (proAttributeMap != null && !proAttributeMap.isEmpty()) {
                        OmsProAttributeInfo info = proAttributeMap.get("M_DIM4_ID");
                        if (info != null) {
                            String content = info.getEname();
                            match = match(appointContentMap, orderItem, content);
                            if (match != null) {
                                expiryDateList.add(match);
                            }
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(expiryDateList)) {
            for (ExpiryDate expiryDate : expiryDateList) {
                orderItemMap.remove(expiryDate.getItemId());
            }
        }
        return expiryDateList;
    }


//    private List<ExpiryDate> matchOrderItem(Map<Integer, List<StCExpiryDateItem>> appointTypeMap,
//                                            Map<Long, OcBOrderItem> orderItemMap, Integer appointDimension) {
//        //指定维度(1.品项,2.商品编码,3.商品标题,4.平台商品ID,5商品标题+商品编码,6:品类)',
//        List<StCExpiryDateItem> dateItems = appointTypeMap.get(appointDimension);
//        if (CollectionUtils.isEmpty(dateItems)) {
//            return null;
//        }
//        List<ExpiryDate> expiryDateList = new ArrayList<>();
//        if (orderItemMap != null && !orderItemMap.isEmpty()) {
//            Map<String, List<StCExpiryDateItem>> appointContentMap = dateItems.stream().collect(Collectors.groupingBy(StCExpiryDateItem::getAppointContent));
//            for (Long itemId : orderItemMap.keySet()) {
//                OcBOrderItem orderItem = orderItemMap.get(itemId);
//                String content;
//                ExpiryDate match = null;
//                switch (appointDimension) {
//                    case ExpiryDateAppointDimensionConstant.UID_PRO_CODE:
//                        content = orderItem.getAnchorId();
//                        if (StringUtils.isNotEmpty(content)) {
//                            match = matchUidAndCode(appointContentMap, orderItem, content);
//                        }
//                        break;
//                    case ExpiryDateAppointDimensionConstant.SPECS_ID_PRO_CODE:
//                        content = orderItem.getSkuNumiid();
//                        if (StringUtils.isNotEmpty(content)) {
//                            int i = content.indexOf("-");
//                            if (i > 0) {
//                                content = content.substring(i + 1);
//                            }
//                            match = matchTitleAndCode(appointContentMap, orderItem, content);
//                        }
//                        break;
//                    case ExpiryDateAppointDimensionConstant.PT_PRO_TITLE_CODE:
//                        content = orderItem.getTitle();
//                        match = matchTitleAndCode(appointContentMap, orderItem, content);
//                        break;
//                    case ExpiryDateAppointDimensionConstant.PT_PRO_ID:
//                        content = orderItem.getNumIid();
//                        match = match(appointContentMap, orderItem, content);
//                        break;
//                    case ExpiryDateAppointDimensionConstant.PRO_TITLE:
//                        content = orderItem.getTitle();
//                        match = matchTitle(appointContentMap, orderItem, content);
//                        break;
//                    case ExpiryDateAppointDimensionConstant.PRO_CODE:
//                        content = orderItem.getPsCProEcode();
//                        match = match(appointContentMap, orderItem, content);
//                        break;
//                    case ExpiryDateAppointDimensionConstant.ITEM:
//                        //todo 品项
//                        String psCSkuEcode = orderItem.getPsCSkuEcode();
//                        ProductSku skuInfo = psRpcService.selectProductSku(psCSkuEcode);
//                        if (skuInfo != null) {
//                            Map<String, OmsProAttributeInfo> proAttributeMap = skuInfo.getProAttributeMap();
//                            if (proAttributeMap != null && !proAttributeMap.isEmpty()) {
//                                OmsProAttributeInfo info = proAttributeMap.get("M_DIM6_ID");
//                                if (info != null) {
//                                    content = info.getEname();
//                                    match = match(appointContentMap, orderItem, content);
//                                }
//                            }
//                        }
//                        break;
//                    case ExpiryDateAppointDimensionConstant.ITEM_CLASS:
//                        //todo 品类
//                        String psCSkuEcode1 = orderItem.getPsCSkuEcode();
//                        ProductSku skuInfo1 = psRpcService.selectProductSku(psCSkuEcode1);
//                        if (skuInfo1 != null) {
//                            Map<String, OmsProAttributeInfo> proAttributeMap = skuInfo1.getProAttributeMap();
//                            if (proAttributeMap != null && !proAttributeMap.isEmpty()) {
//                                OmsProAttributeInfo info = proAttributeMap.get("M_DIM4_ID");
//                                if (info != null) {
//                                    content = info.getEname();
//                                    match = match(appointContentMap, orderItem, content);
//                                }
//                            }
//                        }
//                        break;
//                    default:
//                }
//                if (match != null) {
//                    expiryDateList.add(match);
//                }
//            }
//        }
//        return expiryDateList;
//    }

    private ExpiryDate matchTitle(Map<String, List<StCExpiryDateItem>> appointContentMap, OcBOrderItem orderItem, String content) {
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        for (String s : appointContentMap.keySet()) {
            if (content.contains(s)) {
                return match(appointContentMap, orderItem, s);
            }
        }
        return null;

    }

    private ExpiryDate matchTitleAndCode(Map<String, List<StCExpiryDateItem>> appointContentMap,
                                         OcBOrderItem orderItem, String content) {
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        String psCProEcode = orderItem.getPsCProEcode();
        for (String s : appointContentMap.keySet()) {
            try {
                String[] split = s.split("\\+");
                if (content.contains(split[0]) && psCProEcode.equals(split[1])) {
                    return match(appointContentMap, orderItem, s);
                }
            } catch (Exception e) {
                log.error(LogUtil.format("商品标题加商品code匹配错误:{}", "商品标题加商品code匹配错误"), Throwables.getStackTraceAsString(e));
            }
        }
        return null;

    }

    private ExpiryDate matchUidAndCode(Map<String, List<StCExpiryDateItem>> appointContentMap,
                                       OcBOrderItem orderItem, String content) {
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        String psCProEcode = orderItem.getPsCProEcode();
        for (String s : appointContentMap.keySet()) {
            try {
                String[] split = s.split("\\+");
                if (content.equals(split[0]) && psCProEcode.equals(split[1])) {
                    return match(appointContentMap, orderItem, s);
                }
            } catch (Exception e) {
                log.error(LogUtil.format("主播ID加商品code匹配错误:{}", "主播ID加商品code匹配错误"), Throwables.getStackTraceAsString(e));
            }
        }
        return null;

    }


    private ExpiryDate match(Map<String, List<StCExpiryDateItem>> appointContentMap, OcBOrderItem orderItem, String content) {
        List<StCExpiryDateItem> expiryDateItems = appointContentMap.get(content);
        if (CollectionUtils.isNotEmpty(expiryDateItems)) {
            //有重复的 随便去一个
            StCExpiryDateItem stCExpiryDateItem = expiryDateItems.get(0);
            ExpiryDate expiryDate = new ExpiryDate();
            expiryDate.setId(stCExpiryDateItem.getStCExpiryDateId());
            expiryDate.setItemId(orderItem.getId());
            expiryDate.setOrderId(orderItem.getOcBOrderId());
            expiryDate.setExpiryDateType(stCExpiryDateItem.getAppointType());
            expiryDate.setExpiryDateRange(stCExpiryDateItem.getStartDateDay() + "-" + stCExpiryDateItem.getEndDateDay());
            expiryDate.setOrderLabel(stCExpiryDateItem.getOrderLabel());
            expiryDate.setCheapestExpress(stCExpiryDateItem.getCheapestExpress());
            return expiryDate;
        }
        return null;

    }


    public boolean updateItemExpiryDate(List<ExpiryDate> expiryDateList, OcBOrder ocBOrder, User user, String logMessage) {
        if (CollectionUtils.isNotEmpty(expiryDateList)) {
            Set<Long> expiryDateIds = new HashSet<>();
            for (ExpiryDate expiryDate : expiryDateList) {
                OcBOrderItem item = new OcBOrderItem();
                item.setId(expiryDate.getItemId());
                item.setOcBOrderId(expiryDate.getOrderId());
                item.setExpiryDateType(expiryDate.getExpiryDateType());
                item.setExpiryDateRange(expiryDate.getExpiryDateRange());
                item.setOrderLabel(expiryDate.getOrderLabel());
                if (expiryDate.getCheapestExpress() != null){
                    item.setReserveBigint02(expiryDate.getCheapestExpress().longValue());
                }else {
                    item.setReserveBigint02(0L);
                }
                omsOrderItemService.updateOcBOrderItem(item, expiryDate.getOrderId());
                expiryDateIds.add(expiryDate.getId());
            }
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.MATCH_EXPIRY_DATE.getKey(), logMessage + "匹配商品效期策略成功,策略id:" + expiryDateIds, "", "", user);
            return true;
        } else {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.MATCH_EXPIRY_DATE.getKey(), logMessage + "未匹配到商品效期策略", "", "", user);
            return false;
        }
    }


    @Data
    private static class ExpiryDate implements Serializable {
        /**
         * 策略id
         */
        private Long id;

        private Long itemId;

        private Long orderId;

        private Integer expiryDateType;

        private String expiryDateRange;

        /**
         * 订单标签
         */
        private String orderLabel;

        /**
         * 最便宜的快递
         */
        private Integer cheapestExpress;
    }
}

