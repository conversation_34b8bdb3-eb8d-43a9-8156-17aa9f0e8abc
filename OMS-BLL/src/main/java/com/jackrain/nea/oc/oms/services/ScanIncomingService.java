package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.config.ScanIncomingMqConfig;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.es.ES4RefundIn;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.result.OcBReturnOrderRefundBatchResult;
import com.jackrain.nea.oc.oms.model.result.PsProductInfo;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.WmsControlWarehouse;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.ProductSku;

import com.jackrain.nea.ps.services.PsGetCommodityInformationService;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.request.SkuQueryRequest;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-03-24
 * create at:  2019-03-24 18:31
 * 扫描入库核心服务
 */
@Slf4j
@Component
public class ScanIncomingService {

    private static final int OMS_ORDER_REDIS_TIMEOUT = 24 * 60 * 60 * 1000;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper itemMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private OcBRefundBatchMapper ocBRefundBatchMapper;
    @Autowired
    private OcBRefundInMapper ocBRefundInMapper;
    @Autowired
    private OcBRefundInProductItemMapper ocBRefundInProductItemMapper;
    @Autowired
    private PsGetCommodityInformationService psGetCommodityInformationService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBRefundInLogService logService;
//    @Autowired
//    private R3MqSendHelper sendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private ScanIncomingMqConfig mqConfig;

    @Autowired
    private BasicCpQueryService basicCpQueryService;

    @Autowired
    private OcBOrderItemFiMapper ocBorderItemMapper;

    @Autowired
    private PsRpcService psRpcService;

    /**
     * 根据当前登录用户获取创建的批次
     *
     * @param loginUser 当前用户
     * @return 批次列表
     */
    public ValueHolderV14 getCurrentBatch(User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        List<OcBRefundBatch> batch = getRefundBatch(loginUser);
        if (batch != null && batch.size() != 0) {
            holderV14.setCode(0);
            holderV14.setMessage("获取批次成功！");
            holderV14.setData(batch);
        } else {
            holderV14.setCode(-1);
            holderV14.setMessage("获取批次失败！");
        }
        return holderV14;
    }

    /**
     * 根据条码编码查询本地是否存在，存在返回条码+商品信息，拼接成一条明细
     *
     * @param param     条码编码,主表Id
     * @param loginUser 当前用户
     * @return 返回一条退换货商品明细
     */
    public ValueHolderV14 getOneRefundItem(String param, User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        JSONObject jsonObject = JSON.parseObject(param);
        if (jsonObject != null) {
            String psCSkuEcode = jsonObject.getString("PS_C_SKU_ECODE");
            if (StringUtils.isBlank(psCSkuEcode)) {
                holderV14.setCode(-1);
                holderV14.setMessage("发出国标码或条码不可为空！ ");
                return holderV14;
            }

            //ps中心查询商品信息拼接成一条明细返回(先用国标码查询，再用条码查询)
            List<PsProductInfo> standList = null;
            try {
                standList = psGetCommodityInformationService.getProductInfo(getCbRequest(psCSkuEcode));
            } catch (Exception ex) {
                log.debug("日志服务：通过国标码获取商品信息异常" + ex);
            }

            //国标码查询不到
            if (standList != null && standList.size() != 0) {
                OcBRefundInProductItemExt item = getPsProduct(standList.get(0), loginUser);
                holderV14.setCode(0);
                holderV14.setMessage("添加扫描明细成功！");
                holderV14.setData(item);
                return holderV14;
            } else {
                try {
                    standList = psGetCommodityInformationService.getProductInfo(getSkuRequest(psCSkuEcode));
                } catch (Exception ex) {
                    log.error(LogUtil.format("通过商品条码编码获取商品信息异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
                }
                if (standList != null && standList.size() != 0) {
                    OcBRefundInProductItemExt item = getPsProduct(standList.get(0), loginUser);
                    holderV14.setCode(0);
                    holderV14.setMessage("添加扫描明细成功！");
                    holderV14.setData(item);
                } else {
                    holderV14.setCode(-1);
                    holderV14.setMessage("本地国标码档案或条码档案中不存在此编码，请检查后重试");
                }
                return holderV14;
            }
        }
        holderV14.setCode(-1);
        holderV14.setMessage("参数为空！ ");
        return holderV14;
    }

    /**
     * @param productInfo 商品信息
     * @param loginUser   当前用户
     * @return 拼接好的扫描明细
     */
    private OcBRefundInProductItemExt getPsProduct(PsProductInfo productInfo, User loginUser) {
        OcBRefundInProductItemExt item = new OcBRefundInProductItemExt();
        item.setPsCSkuId(productInfo.getSkuId());
        item.setPsCSkuEcode(productInfo.getECODE());
        item.setPsCProId(productInfo.getPS_C_PRO_ID());
        item.setPsCProEcode(productInfo.getPS_C_PRO_ECODE());
        item.setPsCProEname(productInfo.getPS_C_PRO_ENAME());
        item.setOwnerename(loginUser.getEname());
        item.setSkuSpec(productInfo.getSPEC());
        item.setGbcode(productInfo.getGBCODE());
        item.setQty(new BigDecimal("0"));
        item.setQtyScan(new BigDecimal("0"));
        item.setModifierename(loginUser.getEname());
        item.setCreationdate(new Date());
        item.setModifieddate(new Date());
        item.setAdClientId((long) loginUser.getClientId());
        item.setOwnerid(Long.valueOf(loginUser.getId()));
        item.setOwnername(loginUser.getName());
        item.setModifierid(Long.valueOf(loginUser.getId()));
        item.setModifiername(loginUser.getEname());
        //是否无原单条码 无头件 一定是
        item.setIsWithoutOrig(1);
        //增加颜色，尺寸信息
        item.setPsCClrId(productInfo.getColorId());
        item.setPsCClrEcode(productInfo.getColorCode());
        item.setPsCClrEname(productInfo.getColorName());
        item.setPsCSizeId(productInfo.getSizeId());
        item.setPsCSizeEcode(productInfo.getSizeCode());
        item.setPsCSizeEname(productInfo.getSizeName());
        return item;
    }

    /**
     * 扫描入库列表信息(查询退换货信息)
     *
     * @param param     包含查询条件的json
     * @param loginUser 当前用户
     * @return holder
     */
    public ValueHolderV14 getScanIncomingInfo(String param, User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        JSONObject jsonObject = JSON.parseObject(param);
        if (jsonObject != null) {
            Long id = jsonObject.getLong("ID");
            String receivePhone = jsonObject.getString("RECEIVER_MOBILE");
            String receiveName = jsonObject.getString("RECEIVER_NAME");
            String logisticNumber = jsonObject.getString("LOGISTIC_CODE");
            String origOrderNo = jsonObject.getString("ORIG_ORDER_ID");
            QueryWrapper<OcBReturnOrder> queryWrapper = null;
            QueryWrapper<OcBOrder> queryOrderWrapper = null;

            if (StringUtils.isNotEmpty(receivePhone)) {
                queryWrapper = getQueryWrapper("receive_mobile", receivePhone);
                queryOrderWrapper = getQueryOrderWrapper("receiver_phone", receivePhone);
            }
            if (StringUtils.isNotEmpty(receiveName)) {
                queryWrapper = getQueryWrapper("receive_name", receiveName);
                queryOrderWrapper = getQueryOrderWrapper("receiver_name", receiveName);
            }
            if (StringUtils.isNotEmpty(logisticNumber)) {
                //先过滤物流单号
                logisticNumber = regular(logisticNumber);
                //扫描物流单号时，先判断退货入库单中是否存在此物流单号，如果存在则不允许扫描
                boolean result = selectRefundInOrderByLogisticNumber(logisticNumber);
                if (result) {
                    holderV14.setCode(-2);
                    holderV14.setMessage("退货入库单中存在此物流单号，不允许扫描入库！");
                    return holderV14;
                } else {
                    queryWrapper = getQueryWrapper("logistics_code", logisticNumber);
                    queryOrderWrapper = getQueryOrderWrapper("expresscode", logisticNumber);
                }
            }
            if (StringUtils.isNotEmpty(origOrderNo)) {
                queryWrapper = getQueryWrapper("orig_order_id", origOrderNo);
                queryOrderWrapper = getQueryOrderWrapper("source_code", origOrderNo);
            }
            if (id != null) {
                queryWrapper = getQueryWrapper("id", id);
                queryOrderWrapper = getQueryOrderWrapper("id", id);
            }

            if (queryWrapper == null) {
                holderV14.setCode(-1);
                holderV14.setMessage("查询条件为空！ ");
                return holderV14;
            }

            //此处除了id可以精确查询，其余可能需要在es先捞分库建，再查询
            List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectList(queryWrapper);
            //这里直接将查询到的退换货信息转换为退货入库信息，方便前端保存的时候直接存入数据库
            List<OcBReturnOrderExt> list = new ArrayList<>();
            if (ocBReturnOrders != null && ocBReturnOrders.size() != 0) {
                //如果通过物流单号\原单单号 查出多条，则取则取退单ID最大的退单信息，
                if (ocBReturnOrders.size() > 1) {
                    if (logisticNumber != null || origOrderNo != null) {
                        //取第一个主表信息，因为已经根据ID倒序了
                        OcBReturnOrder ocBReturnOrder = ocBReturnOrders.get(0);
                        //取出所有明细塞到这个主表下边，给前端
                        List<OcBReturnOrderRefund> allItemList = new ArrayList<>();
                        for (OcBReturnOrder bean : ocBReturnOrders) {
                            List<OcBReturnOrderRefund> itemList = getOcBRefundOrderRefundExtItem(bean);
                            allItemList.addAll(itemList);
                        }

                        OcBReturnOrderExt OcBReturnOrderExt = changeDataStatus(ocBReturnOrder, allItemList, null);
                        this.getStoreInfo(OcBReturnOrderExt);

                        list.add(OcBReturnOrderExt);
                        holderV14.setCode(0);
                        holderV14.setMessage("查询成功！");
                        holderV14.setData(list);
                        return holderV14;
                    }
                }
                //循环去查主表所带相关明细
                for (OcBReturnOrder bean : ocBReturnOrders) {
                    OcBReturnOrderExt ocBReturnOrder = getOcBRefundOrderItem(bean);
                    this.getStoreInfo(ocBReturnOrder);
                    list.add(ocBReturnOrder);
                }
                if (log.isDebugEnabled()) {
                    log.debug("ScanIncomingService.getScanIncomingInfo.search：", list);
                }
                holderV14.setCode(0);
                holderV14.setMessage("查询成功！");
                holderV14.setData(list);

            } else {
                /**如果无法找到退换单，则根据物流单号、手机号码、订单号等查找订单*/
                //List<OcBReturnOrderExt> orderExts = getRefundCopyOrderExt(logisticNumber, origOrderNo, queryOrderWrapper);
                List<OcBReturnOrderExt> orderExts = getOcBReturnOrderExt(logisticNumber, origOrderNo, queryOrderWrapper, 1, null, loginUser);
                if (CollectionUtils.isNotEmpty(orderExts)) {
                    holderV14.setCode(0);
                    holderV14.setMessage("查询成功！");
                    holderV14.setData(orderExts);
                } else {
                    holderV14.setCode(-1);
                    holderV14.setMessage("未能匹配相应的退换货单！ ");
                }
            }
            return holderV14;
        }
        holderV14.setCode(-1);
        holderV14.setMessage("查询条件为空！ ");
        return holderV14;
    }

    private List<OcBReturnOrderExt> getRefundCopyOrderExt(String logisticNumber, String origOrderNo, QueryWrapper<OcBOrder> queryOrderWrapper) {
        //这里直接将查询到的退换货信息转换为退货入库信息，方便前端保存的时候直接存入数据库
        List<OcBReturnOrderExt> retundList = new ArrayList<>();
        //订单查询-此处除了id可以精确查询，其余可能需要在es先捞分库建，再查询
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectList(queryOrderWrapper);
        if (ocBOrderList != null && ocBOrderList.size() != 0) {
            //如果通过物流单号\原单单号 查出多条，则取则取退单ID最大的退单信息，
            if (ocBOrderList.size() > 1) {
                if (logisticNumber != null || origOrderNo != null) {
                    //取第一个主表信息，因为已经根据ID倒序了
                    OcBOrder ocBOrder = ocBOrderList.get(0);
                    List<OcBReturnOrderRefund> refunds = new ArrayList<>();
                    OcBReturnOrder orderCopyRefund = new OcBReturnOrder();
                    if (null != ocBOrder) {
                        orderCopyRefund.setId(ocBOrder.getId());
                        orderCopyRefund.setTid(ocBOrder.getTid());
                        orderCopyRefund.setBuyerNick(ocBOrder.getUserNick());
                        orderCopyRefund.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
                        orderCopyRefund.setLogisticsCode(ocBOrder.getExpresscode());
                        orderCopyRefund.setReceiveMobile(ocBOrder.getReceiverMobile());
                        orderCopyRefund.setReceiveName(ocBOrder.getReceiverName());
                        orderCopyRefund.setReceiveAddress(ocBOrder.getReceiverAddress());
                        orderCopyRefund.setCpCShopTitle(ocBOrder.getCpCShopTitle());
                    }
                    //取出所有明细塞到这个主表下边，给前端
                    List<OcBOrderItem> allItemList = new ArrayList<>();
                    for (OcBOrder bean : ocBOrderList) {
                        List<OcBOrderItem> itemList = getOcBOrderExtItem(bean);
                        allItemList.addAll(itemList);
                    }
                    if (CollectionUtils.isNotEmpty(allItemList)) {
                        for (OcBOrderItem orderItem : allItemList) {
                            OcBReturnOrderRefund orderRefund = new OcBReturnOrderRefund();
                            //订单ID设入 退单ID
                            orderRefund.setId(orderItem.getId());
                            //商品编码
                            orderRefund.setPsCProEcode(orderItem.getPsCProEcode());
                            //商品名称
                            orderRefund.setPsCProEname(orderItem.getPsCProEname());
                            //发出条码编码
                            orderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
                            //规格
                            orderRefund.setSkuSpec(orderItem.getSkuSpec());
                            //数量
                            orderRefund.setQtyRefund(orderItem.getQty());
                            //国际码
                            orderRefund.setBarcode(orderItem.getBarcode());
                            //临时 将订单编号 设进 退换货单ID 字段
                            orderRefund.setOcBReturnOrderId(orderCopyRefund.getId());
                            refunds.add(orderRefund);
                        }

                    }
                    OcBReturnOrderExt OcBReturnExt = changeDataStatus(orderCopyRefund, refunds, null);
                    this.getStoreInfo(OcBReturnExt);
                    retundList.add(OcBReturnExt);
                }
            }
        }
        return retundList;
    }


    public List<OcBReturnOrderExt> getOcBReturnOrderExt(String logisticNumber, String origOrderNo, QueryWrapper<OcBOrder> queryOrderWrapper, Integer isback, List<String> listString, User user) {

        Long id = null;
        //这里直接将查询到的退换货信息转换为退货入库信息，方便前端保存的时候直接存入数据库
        List<OcBReturnOrderExt> retundList = new ArrayList<>();
        try {
            //订单查询-此处除了id可以精确查询，其余可能需要在es先捞分库建，再查询
            List<OcBOrder> ocBOrderList = ocBOrderMapper.selectList(queryOrderWrapper);
            if (ocBOrderList != null && ocBOrderList.size() != 0) {
                //如果通过物流单号\原单单号 查出多条，则取则取退单ID最大的退单信息，
                if (ocBOrderList.size() > 1) {
                    if (logisticNumber != null || origOrderNo != null) {
                        //取第一个主表信息，因为已经根据ID倒序了
                        OcBOrder ocBOrder = ocBOrderList.get(0);
                        id = ocBOrder.getId();
                        if (ocBOrder == null) {
                            listString.add("[" + id + "]新增退单失败，未找到原始订单！");
                            return retundList;
                        }
                        //验证原单状态
                        int orderStatus = ocBOrder.getOrderStatus();
                        if (orderStatus != OcOrderCheckBoxEnum.CHECKBOX_PLATFORM_DELIVERY.getVal()
                                && orderStatus != OcOrderCheckBoxEnum.CHECKBOX_WAREHOUSE_DELIVERY.getVal()
                                && orderStatus != OcOrderCheckBoxEnum.CHECKBOX_TRANSACTION_COMPLETED.getVal()) {
                            listString.add("[" + id + "]新增退单失败，状态不正确！");
                            return retundList;
                        }
                        //查询订单明细数据
                        QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("oc_b_order_id", id);
                        List<OcBOrderItem> ocBOrderItems = ocBorderItemMapper.selectList(queryWrapper);
                        if (CollectionUtils.isEmpty(ocBOrderItems)) {
                            listString.add("[" + id + "]新增退单失败,原单未查询到明细数据！");
                            return null;
                        }
                        //验证订单明细是否包含组合商品
                        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                            //验证退单明细是否包含组合商品
                            try {
                                checkReEcode(ocBOrderItem);
                            } catch (Exception e) {
                                log.error(LogUtil.format("原单含有组合商品明细,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                                listString.add("[" + id + "]获取退单失败,原单含有组合商品明细！");
                                return null;
                            }
                        }
                        Long returnid = ModelUtil.getSequence("oc_b_return_order");
                        //保存已退数量明细
                        List<OcBOrderItem> itemsList = new ArrayList<>();
                        //获取退单明细数据
                        OcBReturnOrderRefundBatchResult listRefund = GetRefundOrder(ocBOrderItems, itemsList, returnid, user);
                        //验证总金额
                        if (listRefund.getTotamt().compareTo(BigDecimal.ZERO) < 0) {
                            listString.add("[" + id + "]获取退单失败,退单总金额计算为负数！");
                            return null;
                        }
                        //验证是否有退货明细
                        if (CollectionUtils.isEmpty(listRefund.getListRefund())) {
                            listString.add("[" + id + "]获取退单失败,没有可退数量的明细！");
                            return null;
                        }
                        //查询退单主表
                        OcBReturnOrder returnOrder = GetReturnOrder(ocBOrder, returnid, isback, listRefund.getTotamt(),
                                listRefund.getAddQty(), listRefund.getAllSku(), user);
                        //查询退单明细表（退单商品表）
                        List<OcBReturnOrderRefund> refundlists = listRefund.getListRefund();

                        OcBReturnOrderExt OcBReturnExt = changeDataStatus(returnOrder, refundlists, id);
                        this.getStoreInfo(OcBReturnExt);
                        retundList.add(OcBReturnExt);
                    }
                }
            }
            return retundList;
        } catch (Exception e) {
            log.error(LogUtil.format("新增退单失败,异常信息为:{}",id), Throwables.getStackTraceAsString(e));
            listString.add("[" + id + "]新增退单失败，原因:" + e.toString());
            throw new NDSException(e);
        }
    }

    /**
     * 新增退货主表
     */
    private OcBReturnOrder GetReturnOrder(OcBOrder ocBOrder, Long returnid, Integer isback, BigDecimal totamt,
                                          BigDecimal addQty, String allSku, User user) {

        OcBReturnOrder returnOrder = new OcBReturnOrder();
        returnOrder.setTid(ocBOrder.getSourceCode());//原平台单号
        returnOrder.setOrigOrderId(ocBOrder.getId());//原始订单ID
        returnOrder.setOrigSourceCode(ocBOrder.getSourceCode());//订单来源1
        returnOrder.setBillType(OcReturnBillTypeEnum.RETURN.getVal());//单据类型,1退货单，2退换货单
        returnOrder.setBuyerNick(ocBOrder.getUserNick());//买家昵称
        returnOrder.setCpCShopTitle(ocBOrder.getCpCShopTitle());//平台店铺名称
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());//平台店铺id
        returnOrder.setCpCShopEcode(ocBOrder.getCpCShopEcode());//平台店铺编码
        returnOrder.setIsBack(isback);//是否原退
        returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        if (returnOrder.getIsBack() == 0) {
            // todo? mergg
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(returnOrder.getCpCPhyWarehouseId());
            if (null != cpCPhyWarehouse) {
                returnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouse.getReturnPhyWarehouseId());
            }
        } else {
            returnOrder.setLogisticsCode(ocBOrder.getExpresscode());//退回物流单号
            returnOrder.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());//退回物流公司id
            returnOrder.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());//退回物流公司编码
            returnOrder.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());//退回物流公司名称
            returnOrder.setCpCPhyWarehouseInId(returnOrder.getCpCPhyWarehouseId());
        }
        returnOrder.setReceiveName(ocBOrder.getReceiverName());//换货收货人
        returnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());//换货收货人手机
        returnOrder.setReceivePhone(ocBOrder.getReceiverPhone());//换货收货人电话
        returnOrder.setReceiverProvinceId(ocBOrder.getCpCRegionProvinceId());//换货收货人省份id
        returnOrder.setReceiverProvinceName(ocBOrder.getCpCRegionProvinceEname());//换货收货人省份
        returnOrder.setReceiverCityId(ocBOrder.getCpCRegionCityId());//换货收货人市id
        returnOrder.setReceiverCityName(ocBOrder.getCpCRegionCityEname());//换货收货人市
        returnOrder.setReceiverAreaId(ocBOrder.getCpCRegionAreaId());//换货收货人区id
        returnOrder.setReceiverAreaName(ocBOrder.getCpCRegionAreaEname());//换货收货人区
        returnOrder.setReceiveAddress(ocBOrder.getReceiverAddress());//地址
        returnOrder.setShipAmt(BigDecimal.ZERO);//换货邮费 默认0
        returnOrder.setPlatform(ocBOrder.getPlatform());//平台类型
        returnOrder.setOrdeSource(ocBOrder.getSourceCode());//订单来源
        returnOrder.setIsManualAudit(0);//是否手工审核
        returnOrder.setExchangeAmt(BigDecimal.ZERO);//换货金额
        returnOrder.setReturnAmtList(totamt);//商品应退金额
        returnOrder.setReturnAmtShip(ocBOrder.getShipAmt());//退还运费
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);//退还其他费用
        returnOrder.setReturnAmtActual(totamt.add(returnOrder.getReturnAmtShip()).add(returnOrder.getReturnAmtOther())
                .subtract(returnOrder.getExchangeAmt()));//退款金额 = 商品应退金额+退还运费+退还其他费用-换货金额
        returnOrder.setConsignAmtSettle(BigDecimal.ZERO);//代销结算金额
        returnOrder.setQtyInstore(addQty);//退货总数量
        returnOrder.setAllSku(allSku);//明细sku+数量 集合

        // 检查管控仓判断
        returnOrder = checkWmsCtrHouse(returnOrder);
        returnOrder.setId(returnid);
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        returnOrder.setIsAdd(1);//是否手工新增  默认 1
        returnOrder.setIsToag(0);//是否传ag,0未传，1已传，2失败，3不传
        returnOrder.setIsTransfer(0);
        returnOrder.setIsTodrp(0);
        returnOrder.setInventedStatus(0);
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        returnOrder.setIsTowms(0);//传WMS状态
        returnOrder.setIsReceiveConfirm(0);//是否确认收货
        returnOrder.setWmsCancelStatus(0);//wms撤回状态 默认未撤回0
        returnOrder.setIsForce(0);//强制入库 默认0

        returnOrder.setOwnerid(user.getId().longValue());
        returnOrder.setOwnername(user.getName());
        returnOrder.setOwnerename(user.getEname());
        returnOrder.setCreationdate(new Date());
        returnOrder.setModifierid(user.getId().longValue());
        returnOrder.setModifiername(user.getName());
        returnOrder.setModifierename(user.getEname());
        returnOrder.setModifieddate(new Date());
        returnOrder.setAdClientId(user.getClientId() + 0L);
        returnOrder.setAdOrgId(user.getOrgId() + 0L);
        return returnOrder;
    }

    /**
     * 新增退货明细数据
     */
    private OcBReturnOrderRefundBatchResult GetRefundOrder(List<OcBOrderItem> ocBOrderItems, List<OcBOrderItem> itemsList,
                                                           Long returnid, User user) {
        String allSku = "";
        BigDecimal totamt = BigDecimal.ZERO;//退单总金额
        BigDecimal addQty = BigDecimal.ZERO;//退单总数量
        //组装退货单明细数据
        List<OcBReturnOrderRefund> listRefund = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            //验证订单数量是否大于已退数量
            if (null == ocBOrderItem.getQtyHasReturn()) {
                ocBOrderItem.setQtyHasReturn(BigDecimal.ZERO);
            }
            BigDecimal returnQty = ocBOrderItem.getQty().subtract(ocBOrderItem.getQtyHasReturn());
            if (returnQty.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            //验证订单明细的退款状态不等于‘退款成功’
            if (ocBOrderItem.getRefundStatus().equals(Integer.valueOf(OcOrderRefundStatusEnum.SUCCESS.getVal()))) {
                continue;
            }

            addQty = addQty.add(returnQty);//累计退货总数量

            OcBReturnOrderRefund refund = new OcBReturnOrderRefund();
            Long reId = ModelUtil.getSequence("oc_b_return_order_refund");
            refund.setOcBReturnOrderId(returnid);
            refund.setId(reId);
            refund.setOwnerid(user.getId().longValue());
            refund.setOwnername(user.getName());
            refund.setOwnerename(user.getEname());
            refund.setCreationdate(new Date());
            refund.setAdClientId(Long.valueOf(user.getClientId()));
            refund.setAdOrgId(Long.valueOf(user.getOrgId()));
            refund.setModifierid(user.getId().longValue());
            refund.setModifiername(user.getName());
            refund.setModifierename(user.getEname());
            refund.setModifieddate(new Date());

            BigDecimal realAmt = ocBOrderItem.getRealAmt();
            BigDecimal qty = ocBOrderItem.getQty();
            BigDecimal sgAmt = BigDecimal.ZERO;

            if (null != realAmt && null != qty && qty.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                sgAmt = realAmt.divide(qty, OcBOrderConst.DECIMAL_QTY, BigDecimal.ROUND_HALF_UP);
                refund.setAmtRefundSingle(sgAmt);//单件退货金额
            }

            BigDecimal totPrice = ocBOrderItem.getTotPriceSettle();
            BigDecimal settle = ocBOrderItem.getPriceSettle();
            refund.setPriceList(ocBOrderItem.getPriceTag());//吊牌价
            refund.setPriceSettle(BigDecimal.ZERO);//算单价
            refund.setAmtSettleTot(BigDecimal.ZERO);//结算金额
            //结算单价
            if (null == settle || settle.compareTo(BigDecimal.ZERO) < OcBOrderConst.IS_STATUS_IY) {
                if (sgAmt.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                    refund.setPriceSettle(sgAmt);
                }
            }
            //结算总额,如果没值,则启用成交价格
            if (null == totPrice || totPrice.compareTo(BigDecimal.ZERO) < OcBOrderConst.IS_STATUS_IY) {
                if (realAmt != null && realAmt.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                    refund.setAmtSettleTot(realAmt);
                }
            }

            refund.setProductMark("1");//商品标记 默认1
            refund.setBarcode(ocBOrderItem.getBarcode());//国标码
            refund.setOid(ocBOrderItem.getOoid());//子订单id 原单明细id
            refund.setSkuSpec(ocBOrderItem.getSkuSpec());//规格
            refund.setPsCSkuId(ocBOrderItem.getPsCSkuId());//skuid
            refund.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());//SKU编码
            refund.setPsCProId(ocBOrderItem.getPsCProId());//商品ID
            refund.setPsCProEcode(ocBOrderItem.getPsCProEcode());//商品编码
            refund.setPsCProEname(ocBOrderItem.getPsCProEname());//商品名称
            refund.setPrice(ocBOrderItem.getPriceTag());//商品单价
            refund.setPsCSizeId(ocBOrderItem.getPsCSizeId());//尺寸id
            refund.setPsCSizeEcode(ocBOrderItem.getPsCSizeEcode());//尺寸编码
            refund.setPsCSizeEname(ocBOrderItem.getPsCSizeEname());//尺寸名称
            refund.setPsCClrId(ocBOrderItem.getPsCClrId());//颜色id
            refund.setPsCClrEcode(ocBOrderItem.getPsCClrEcode());//颜色编码
            refund.setPsCClrEname(ocBOrderItem.getPsCClrEname());//颜色名称
            refund.setIsReturn(0);//退、换货标识  默认0 退
            refund.setQtyIn(BigDecimal.ZERO);//入库数量
            refund.setQtyRefund(returnQty);//申请数量
            refund.setAmtRefund(refund.getQtyRefund().multiply(refund.getAmtRefundSingle()));//单件退货金额*申请数量
            refund.setQtyCanRefund(returnQty);//可退数量
            refund.setSex(ocBOrderItem.getSex());//性别
            //是否AG退款，3不传
            refund.setIsToAg(AGStatusEnum.NOT.getVal());
            totamt = totamt.add(refund.getAmtRefund());//累加退换货金额
            allSku = allSku + refund.getPsCSkuEcode() + "(" + refund.getQtyRefund().toString() + "),";
            listRefund.add(refund);

            //修改原单的已退数量
            BigDecimal retiredQty = ocBOrderItem.getQtyHasReturn().add(refund.getQtyRefund());
            OcBOrderItem item = new OcBOrderItem();
            item.setId(ocBOrderItem.getId());
            item.setQtyHasReturn(retiredQty);
            itemsList.add(item);
        }
        OcBReturnOrderRefundBatchResult result = new OcBReturnOrderRefundBatchResult();
        result.setAddQty(addQty);
        result.setAllSku(allSku);
        result.setTotamt(totamt);
        result.setListRefund(listRefund);
        return result;
    }

    /**
     * 检查退货明细是否有组合商品
     */
    private void checkReEcode(OcBOrderItem item) {
        Map map = psRpcService.querySku(item.getPsCSkuEcode());
        List<HashMap> hashMapList = (List<HashMap>) map.get("data");
        if (!hashMapList.isEmpty()) {
            String isGroup = (String) hashMapList.get(0).get("IS_GROUP");
            if ("Y".equals(isGroup)) {
                throw new NDSException("此退换货单含有组合商品明细，请检查后修改为实际出入库条码后重新操作");
            }
        }
    }

    /**
     * 判断新增或者更新的这个是不是wms管控仓
     */
    private OcBReturnOrder checkWmsCtrHouse(OcBReturnOrder returnOrder) {
        /**
         * 如果入库实体仓是WMS管控仓，则将reserve_bigint03此字段赋值为1，如果不是WMS管控仓或者入库实体仓为空，则此字段赋值为0.
         */
        //实体仓id
        Long cpCPhyWarehouseInId = returnOrder.getCpCPhyWarehouseInId();
        if (cpCPhyWarehouseInId == null) {
            returnOrder.setIsNeedToWms(0L);
            return returnOrder;
        }
        // merge return is ext , find is cp ->  cp
        CpCPhyWarehouse wareHouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseInId);
        //判断这个仓是不是wms 管控仓
        if (wareHouse != null) {
            //不是wms 管控仓或者为空，是否传WMS字段（reserve_bigint03）则此字段赋值为0.
            if (wareHouse.getWmsControlWarehouse() == null || WmsControlWarehouse.NOT_CONTROL.equals(wareHouse.getWmsControlWarehouse())) {
                returnOrder.setIsNeedToWms(0L);
            } else {
                //是wms 管控仓，是否传WMS字段（reserve_bigint03）则此字段赋值为0.
                returnOrder.setIsNeedToWms(1L);
            }
        } else {
            throw new NDSException("查询不到实体仓");
        }
        return returnOrder;
    }

    private void getStoreInfo(OcBReturnOrderExt ocBReturnOrder) {
        StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
        storeInfoQueryRequest.setPhyId(ocBReturnOrder.getCpCPhyWarehouseId());
        HashMap<Long, List<CpCStore>> storeInfoByPhyId = basicCpQueryService.getStoreInfoByPhyId(storeInfoQueryRequest);
        if (storeInfoByPhyId != null) {
            List<CpCStore> cpCStores = storeInfoByPhyId.get(ocBReturnOrder.getCpCPhyWarehouseId());
            if (CollectionUtils.isNotEmpty(cpCStores)) {
                CpCStore cpCStore = cpCStores.get(0);
                if (cpCStore != null) {
                    ocBReturnOrder.setScanStoreId(cpCStore.getId());
                    ocBReturnOrder.setScanStoreCode(cpCStore.getEcode());
                    ocBReturnOrder.setScanStoreName(cpCStore.getEname());
                }
            }
        }
    }

    /**
     * @param logisticNumber 物流单号
     */
    private boolean selectRefundInOrderByLogisticNumber(String logisticNumber) {
        /*
         * es有延迟，所以先查redis,再查es。（redis过期时间默认1天）
         */
        String redisKey = BllRedisKeyResources.buildReturnOrderLogisticsNumberKey(logisticNumber);
        CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        Boolean hasKey = objRedisTemplate.hasKey(redisKey);

        Long id = 0L;
        if (hasKey != null && hasKey) {
            id = objRedisTemplate.opsForValue().get(redisKey);
        } else {
            JSONObject search = ES4RefundIn.findJSONObjectByLogisticNumber(logisticNumber);
            // 先查询ES是否存在此退单
            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");
                JSONObject jsonObject = arrayObj.getJSONObject(0);
                if (jsonObject != null) {
                    id = jsonObject.getLongValue("ID");
                }
            }
        }

        if (id == null || id == 0) {
            return false;
        }

        //出现1单40s事务都没有提交 id=47052
        return true;
//        OcBRefundIn ocBRefundIn = ocBRefundInMapper.selectById(id);
//        return ocBRefundIn != null;

    }

    /**
     * 20190619 新增逻辑 孙俊磊
     * 扫描入库时，需要过滤京东和圆通物流单号（京东过滤后置的-1-1-，圆通过滤条件前置的R02T）
     *
     * @param logisticNumber 物流单号
     * @return 返回处理过的单号
     */
    private String regular(String logisticNumber) {
        String jd = "-1-1-";
        String yt = "R02T";
        if (logisticNumber.contains(jd)) {
            logisticNumber = logisticNumber.replaceAll(jd, "");
        }
        if (logisticNumber.contains(yt)) {
            logisticNumber = logisticNumber.replaceAll(yt, "");
        }
        return logisticNumber;
    }

    /**
     * 拼接查询条件
     *
     * @param column 列名
     * @param value  值
     * @return QueryWrapper
     */
    private QueryWrapper<OcBReturnOrder> getQueryWrapper(String column, Object value) {
        //退单状态为：等待退货入库且且是否传WMS字段值为否或者null  且是否生成零售字段为否，或者退单虚拟入库状态为：“虚拟入库未入库状态 且是否生成零售字段为否，退单状态=60
        QueryWrapper<OcBReturnOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .and(Wrapper -> Wrapper
                        .eq(column, value)
                        .eq("return_status", ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal())
                        .eq("is_need_to_wms", OmsOrderRefundCommonEum.ZERO.parseValue())
                        .eq("is_todrp", OmsOrderRefundCommonEum.ZERO.parseValue())
                        .eq("isactive", "Y"))
                .or()
                .eq(column, value)
                .eq("invented_status", OmsOrderRefundCommonEum.ONE.parseValue())
                .eq("is_todrp", OmsOrderRefundCommonEum.ZERO.parseValue())
                .eq("isactive", "Y")
                .ne("return_status", ReturnStatusEnum.CANCLE.getVal())
                .orderByDesc("id");
        return queryWrapper;
    }

    /**
     * 拼接查询条件-用作订单查询
     *
     * @param column 列名
     * @param value  值
     * @return QueryWrapper
     */
    private QueryWrapper<OcBOrder> getQueryOrderWrapper(String column, Object value) {
        //订单状态为仓库发货或者平台发货且匹配状态为未匹配或者部分匹配的订单
        QueryWrapper<OcBOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq(column, value)
                .and(Wrapper -> Wrapper
                        .eq("order_status", OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())
                        .or()
                        .eq("order_status", OmsOrderStatus.PLATFORM_DELIVERY.toInteger()))
                .and(i -> i.eq("isactive", "Y"))
                .orderByDesc("id");
        return queryWrapper;
    }

    /**
     * 退货入库保存
     *
     * @param param     退户入库单主子表数据
     * @param loginUser 当前用户
     * @return holder
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveRefundOrder(String param, User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        JSONObject jsonObject = JSON.parseObject(param);
        if (jsonObject == null) {
            holderV14.setCode(-1);
            holderV14.setMessage("参数为空！ ");
            return holderV14;
        }
        /*
         * 20190424
         * 新增两个字段退换货ID和是否强制，用来更新退换货主表
         * 20190831 用物流单号进行锁单
         */
        String logisticsCode = jsonObject.getString("LOGISTICS_CODE");
        if (StringUtils.isBlank(logisticsCode)) {
            holderV14.setCode(-1);
            holderV14.setMessage("物流单号不能为空！ ");
            return holderV14;
        }

        //进行锁单
        String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderLogisticsCodeKey(logisticsCode);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {

                //过滤京东和圆通物流单号（京东过滤后置的-1-1-，圆通过滤条件前置的R02T）
                logisticsCode = regular(logisticsCode);

                //扫描物流单号时，先判断退货入库单中是否存在此物流单号，如果存在则不允许扫描
                boolean result = selectRefundInOrderByLogisticNumber(logisticsCode);
                if (result) {
                    throw new NDSException("退货入库单中存在此物流单号，不允许扫描入库！");
                }

                Integer isForce = jsonObject.getInteger("IS_FORCE");
                Long returnId = jsonObject.getLong("ID");
                //存取都用过滤后的物流单号
                OcBRefundIn bean = getOcBRefundIn(jsonObject, loginUser, logisticsCode);
                List<OcBRefundInProductItem> itemList = getOcBRefundInProductItems(jsonObject);
                holderV14 = saveRedisRefundOrder(bean, isForce, itemList, loginUser, returnId, logisticsCode);
                return holderV14;
            } else {
                throw new NDSException(Resources.getMessage(" 当前退单其他人在操作，请稍后再试!", loginUser.getLocale()));
            }
        } catch (Exception ex) {
            CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
            objRedisTemplate.delete(BllRedisKeyResources.buildReturnOrderLogisticsNumberKey(logisticsCode));
            throw new NDSException(ex.getMessage());
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * returnId存在的时候进行锁单
     *
     * @param loginUser     user
     * @param returnId      退单Id
     * @param logisticsCode 物流单号，存入redis
     * @return holder
     */

    private ValueHolderV14 saveRedisRefundOrder(OcBRefundIn bean, Integer isForce, List<OcBRefundInProductItem> itemList, User loginUser, Long returnId, String logisticsCode) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        //保存条件：主表信息必要字段，必须有明细
        if (bean == null || itemList == null || itemList.size() == 0) {
            holderV14.setCode(-1);
            holderV14.setMessage("退换货信息缺失，不允许保存！");
            return holderV14;
        }

        //先调用退货入库单新增，再调扫描入库，一定会有明细，前端会卡
        insertRefundData(bean, itemList, loginUser);

        //入库单保存成功后将物流单号作为key存入redis,value为对应的退货入库单Id
        String redisKey = BllRedisKeyResources.buildReturnOrderLogisticsNumberKey(logisticsCode);
        CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        objRedisTemplate.opsForValue().set(redisKey, bean.getId(), OMS_ORDER_REDIS_TIMEOUT, TimeUnit.MILLISECONDS);
        log.debug(this.getClass().getSimpleName() + "redis存储成功，key=" + redisKey + ",value=" + bean.getId());

        JSONObject json = new JSONObject();
        json.put("id", bean.getId());
        json.put("returnId", returnId);
        json.put("isForce", isForce);
        try {
            String msgKey = UUID.randomUUID().toString();

//            String msgId = sendHelper.sendMessage((Object) json.toJSONString(), mqConfig.getSendMqTopic(), mqConfig.getSendMqTag(), msgKey);
            defaultProducerSend.sendTopic(Mq5Constants.TOPIC_R3_OC_OMS_CALL_REFUNDIN, Mq5Constants.TAG_R3_OC_OMS_CALL_REFUNDIN, json.toJSONString(), msgKey);
        } catch (Exception e) {
            throw new MqException("扫描入库失败，发送mq失败");
        }
        holderV14.setCode(0);
        holderV14.setMessage("扫描入库成功！");
        return holderV14;

    }

    /**
     * 插入退货入库主子表信息
     *
     * @param bean      主表信息
     * @param itemList  子表列表
     * @param loginUser loginUser
     */
    private void insertRefundData(OcBRefundIn bean, List<OcBRefundInProductItem> itemList, User loginUser) {
        try {
            if (ocBRefundInMapper.insert(bean) > 0) {
                for (OcBRefundInProductItem item : itemList) {
                    item.setOcBRefundInId(bean.getId());
                    item.setId(ModelUtil.getSequence("oc_b_refund_in_product_item"));
                    item.setOwnerename(loginUser.getEname());
                    item.setModifierename(loginUser.getEname());
                    item.setCreationdate(new Date());
                    item.setModifieddate(new Date());
                    item.setAdClientId((long) loginUser.getClientId());
                    item.setAdOrgId((long) loginUser.getOrgId());
                    item.setOwnerid(Long.valueOf(loginUser.getId()));
                    item.setOwnername(loginUser.getName());
                    item.setModifierid(Long.valueOf(loginUser.getId()));
                    item.setModifiername(loginUser.getEname());
                    item.setIsactive("Y");
                    //是否匹配
                    item.setIsMatch(0);
                    //是否生成调整单
                    item.setIsGenAdjust(0);
                    //是否生成入库单
                    item.setIsGenInOrder(IsGenInEnum.NO.integer());
                    //是否生成错发调整单
                    item.setIsGenWroAdjust(IsGenWroAdjustEnum.NO.integer());
                    //是否生成冲无头件调整单
                    item.setIsGenMinusAdjust(IsGenMinusAdjustEnum.NO.integer());
                    //获取实发条码id
                    if (StringUtils.isNotEmpty(item.getRealSkuEcode())) {
                        if (null == item.getRealSkuId()) {
                            ProductSku sku = psRpcService.selectProductSku(item.getRealSkuEcode() + "");
                            if (null != sku) {
                                item.setRealSkuId(sku.getId());
                            }
                        }
                    }
                }
                ocBRefundInProductItemMapper.batchInsert(itemList);
              /*  //推送主子表ES
                if (!SpecialElasticSearchUtil.indexExists(OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME)) {
                    SpecialElasticSearchUtil.indexCreate(OcBRefundInProductItem.class, OcBRefundIn.class);
                }
                SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, bean, bean.getId());

                SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_PRODUCT_ITEM_TYPE_NAME, itemList, "OC_B_REFUND_IN_ID");
*/
                //添加退货操作日志
                try {
                    logService.addLog(bean.getId(), "新增退货入库单", "新增退货入库单成功", loginUser);
                } catch (Exception ex) {
                    log.error(LogUtil.format("记录退货入库日志异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("退货入库主子表插入异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            throw new NDSException("日志服务：退货入库主子表插入异常");
        }

    }

    /**
     * ISFRONT字段前端特有，其他为Null
     *
     * @param jsonObject 将数组转换为退货入库单明细实体
     * @return List<OcBRefundInProductItem>
     */
    private List<OcBRefundInProductItem> getOcBRefundInProductItems(JSONObject jsonObject) {
        List<OcBRefundInProductItem> data;
        String itemList = jsonObject.getString("ITEM_LIST");
        List<JSONObject> jsonObjects = JSONArray.parseArray(itemList, JSONObject.class);
        boolean isExist = false;
        if (jsonObjects == null) {
            return null;
        }
        for (JSONObject json : jsonObjects) {
            if (json.getLong("ISFRONT") != null) {
                isExist = true;
                break;
            }
        }
        //存在前端约定标记
        if (isExist) {
            List<OcBRefundInProductItem> list = JSONArray.parseArray(itemList, OcBRefundInProductItem.class);
            for (int i = 0; i < list.size(); i++) {
                list.get(i).setQty(jsonObjects.get(i).getBigDecimal("QTY_SCAN"));
            }
            data = list;
        } else {
            data = JSONArray.parseArray(itemList, OcBRefundInProductItem.class);
        }

        //排除为0的明细
        data.removeIf(item -> item.getQty() == null || BigDecimal.ZERO.equals(item.getQty()));
        return data;
    }


    /**
     * 含有主表字段的json
     * 此处正常情况是前端调用，还有一种是后端通用此方法 IS_AFTER=0
     *
     * @param jsonObject    主表信息
     * @param loginUser     loginUser
     * @param logisticsCode
     * @return 主表
     */
    private OcBRefundIn getOcBRefundIn(JSONObject jsonObject, User loginUser, String logisticsCode) {
        Long isAfter = jsonObject.getLong("IS_AFTER");
        if (isAfter != null && isAfter == 0) {
            OcBRefundIn data = jsonObject.getObject("DATA", OcBRefundIn.class);
            data.setId(ModelUtil.getSequence("oc_b_refund_in"));
            return data;
        } else {
            //退货批次ID
            Long ocBRefundBatchId = jsonObject.getLong("OC_B_REFUND_BATCH_ID");
            //收货手机
            String receiveMobile = jsonObject.getString("RECEIVE_MOBILE");
            if (ocBRefundBatchId == null) {
                throw new NDSException("退货批次为空,不允许扫描入库！");
            }
            Long logical_warehouse_id = jsonObject.getLong("LOGICAL_WAREHOUSE_ID"); //传过来的逻辑仓id

            OcBRefundIn ocBRefundIn = new OcBRefundIn();
            ocBRefundIn.setId(ModelUtil.getSequence("oc_b_refund_in"));
            ocBRefundIn.setLogisticNumber(logisticsCode);
            ocBRefundIn.setReceiverMobile(receiveMobile);
            OcBRefundBatch refundBatch = ocBRefundBatchMapper.selectById(ocBRefundBatchId);
            //补充仓库信息
            setInStoreAndPhyWareInfo(refundBatch, ocBRefundIn, logical_warehouse_id);

            //拼5条明细的sku
            List<OcBRefundInProductItem> list = getOcBRefundInProductItems(jsonObject);
            int tempInt = 5;
            if (list != null && list.size() != 0) {
                if (list.size() > tempInt) {
                    list = list.subList(0, 5);
                }
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < list.size(); i++) {
                    if (i == list.size() - 1) {
                        builder.append(list.get(i).getPsCSkuEcode()).append("(").append(list.get(i).getQty()).append(")");
                    } else {
                        builder.append(list.get(i).getPsCSkuEcode()).append("(").append(list.get(i).getQty()).append(")").append(",");
                    }

                }
                ocBRefundIn.setAllSku(builder.toString());
            }

            //原平台单号
            ocBRefundIn.setSourceCode(jsonObject.getString("ORIG_SOURCE_CODE"));
            ocBRefundIn.setUserNick(jsonObject.getString("BUYER_NICK"));
            //备注
            ocBRefundIn.setRemark(jsonObject.getString("REAL_REMARK"));
            //收货人
            ocBRefundIn.setReceiverName(jsonObject.getString("RECEIVE_NAME"));
            //收货地址
            ocBRefundIn.setReceiverAddress(jsonObject.getString("RECEIVE_ADDRESS"));
            ocBRefundIn.setCpCLogisticsId(jsonObject.getLong("CP_C_LOGISTICS_ID"));
            ocBRefundIn.setCpCLogisticsEname(jsonObject.getString("CP_C_LOGISTICS_ENAME"));
            try {
                CpLogistics logisticsInfo = cpRpcService.queryLogisticsById(jsonObject.getLong("CP_C_LOGISTICS_ID"));
                ocBRefundIn.setCpCLogisticsEcode(logisticsInfo.getEcode());
            } catch (Exception ex) {
                log.error(LogUtil.format("通过物流公司ID查找物流公司ECODE数据异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));

            }

            //原单单号
            ocBRefundIn.setOrigOrderNo(jsonObject.getLong("ORIG_ORDER_ID"));
            ocBRefundIn.setSpecialType(jsonObject.getString("SPECIAL_TYPE"));
            ocBRefundIn.setAdClientId((long) loginUser.getClientId());
            ocBRefundIn.setOwnerename(loginUser.getEname());
            ocBRefundIn.setModifierename(loginUser.getEname());
            ocBRefundIn.setCreationdate(new Date());
            ocBRefundIn.setModifieddate(new Date());
            ocBRefundIn.setOwnerid(Long.valueOf(loginUser.getId()));
            ocBRefundIn.setOwnername(loginUser.getName());
            ocBRefundIn.setModifierid(Long.valueOf(loginUser.getId()));
            ocBRefundIn.setModifiername(loginUser.getEname());
            ocBRefundIn.setIsactive("Y");
            //增加取值
            ocBRefundIn.setAdOrgId((long) loginUser.getOrgId());
            //匹配状态
            ocBRefundIn.setMatchStatus(0);
            /*
             * 扫描入库当匹配到退换货订单，生成退货入库单时，匹配人、匹配时间赋值当前扫描人，当前系统时间
             * 无头件，则不赋值
             */
            Long id = jsonObject.getLong("ID");
            if (id != null) {
                ocBRefundIn.setMatcher(loginUser.getEname());
                ocBRefundIn.setMatchedTime(new Date());
            }
            //是否关闭匹配 默认为0
            ocBRefundIn.setIsOffMatch(0);
            //扫描入库增加一列实收条码国标码，生成退货入库单时传入oc_b_return_order_refund.reserve_varchar01字段
            // 修改设置为 匹配人
            // ocBRefundIn.setReserveVarchar01(jsonObject.getString("REAL_GBCODE"));
            ocBRefundIn.setMatcher(loginUser.getEname());
            /*
             * 扫描入库更新
             * 默认1 等待退货入库
             */
            ocBRefundIn.setInStatus(ReturnStatus.WAITING_FOR_STORAGE.toInteger());
            return ocBRefundIn;
        }
    }

    /**
     * @param refundBatch 批次信息
     * @param ocBRefundIn 退货入库主表信息
     */
    private void setInStoreAndPhyWareInfo(OcBRefundBatch refundBatch, OcBRefundIn ocBRefundIn, Long logical_warehouse_id) {
        //根据批次id获取
        ocBRefundIn.setBatchNo(refundBatch.getBatchNo());
        ocBRefundIn.setOcBRefundBatchId(refundBatch.getId());
//        if (logical_warehouse_id == null) {
//            //        Long inStoreId = refundBatch.getInStoreId();if (null != refundBatch.getInStoreId()) {
//                //逻辑仓信息
//                ocBRefundIn.setInStoreId(refundBatch.getInStoreId());
//                ocBRefundIn.setInStoreEcode(refundBatch.getInStoreEcode());
//                ocBRefundIn.setInStoreEname(refundBatch.getInStoreEname());
//            }
//        }else {
//            //通过页面选择的逻辑仓填充信息
//            inStoreId = logical_warehouse_id;
//        }
        CpStore cpStore = cpRpcService.selectCpCStoreById(logical_warehouse_id);
        if (cpStore != null) {

            ocBRefundIn.setInStoreId(cpStore.getId());
            ocBRefundIn.setInStoreEcode(cpStore.getEcode());
            ocBRefundIn.setInStoreEname(cpStore.getEname());
            ocBRefundIn.setCpCPhyWarehouseId(cpStore.getCpCPhyWarehouseId());
            ocBRefundIn.setCpCPhyWarehouseEcode(cpStore.getCpCPhyWarehouseEcode());
            ocBRefundIn.setCpCPhyWarehouseEname(cpStore.getCpCPhyWarehouseEname());
        }
    }

    /**
     * 生成查询条件
     *
     * @param psCSkuEcode 条码编码
     * @return 请求
     */
    private SkuQueryRequest getSkuRequest(String psCSkuEcode) {
        SkuQueryRequest queryRequest = new SkuQueryRequest();
        queryRequest.setIsBlur("N");
        PsCSku psCSku = new PsCSku();
        psCSku.setEcode(psCSkuEcode);
        queryRequest.setPsCSku(psCSku);
        return queryRequest;
    }

    /**
     * 生成查询条件
     *
     * @param gbCode 国标码
     * @return 请求
     */
    private SkuQueryRequest getCbRequest(String gbCode) {
        SkuQueryRequest queryRequest = new SkuQueryRequest();
        queryRequest.setIsBlur("N");
        PsCSku psCSku = new PsCSku();
        psCSku.setGbcode(gbCode);
        queryRequest.setPsCSku(psCSku);
        return queryRequest;
    }

    /**
     * 只获取明细列表
     *
     * @param refund 主表实体
     * @return 对应的主表明细
     */
    private List<OcBReturnOrderRefund> getOcBRefundOrderRefundExtItem(OcBReturnOrder refund) {
        //查询对应明细并且拼接
        QueryWrapper<OcBReturnOrderRefund> wrapper = new QueryWrapper<>();
        wrapper.eq("oc_b_return_order_id", refund.getId()).eq("isactive", "Y");
        return ocBReturnOrderRefundMapper.selectList(wrapper);
    }

    /**
     * 只获取订单明细列表
     *
     * @param ocBOrder 主表实体
     * @return 对应的订单主表明细
     */
    private List<OcBOrderItem> getOcBOrderExtItem(OcBOrder ocBOrder) {
        //查询对应明细并且拼接
        QueryWrapper<OcBOrderItem> wrapper = new QueryWrapper<>();
        wrapper.eq("OC_B_ORDER_ID", ocBOrder.getId()).eq("isactive", "Y");
        return itemMapper.selectList(wrapper);
    }

    /**
     * 根据一条主表信息获取对应的明细并拼接好一条主子表信息
     *
     * @param refund 主表实体
     * @return 主子表信息实体
     */
    private OcBReturnOrderExt getOcBRefundOrderItem(OcBReturnOrder refund) {
        //查询对应明细并且拼接
        QueryWrapper<OcBReturnOrderRefund> wrapper = new QueryWrapper<>();
        wrapper.eq("oc_b_return_order_id", refund.getId()).eq("isactive", "Y");
        List<OcBReturnOrderRefund> ocBRefundInProductItems = ocBReturnOrderRefundMapper.selectList(wrapper);
        return changeDataStatus(refund, ocBRefundInProductItems, null);
    }

    /**
     * 获取退换货批次和入库信息(查询条件：当前用户，未完结状态，倒序)
     * 1 处理中     2 已完结    3 已调拨 ，4已作废 最终写成枚举
     *
     * @param loginUser loginUser
     * @return 所有退换货批次 后续可能要分页
     */
    private List<OcBRefundBatch> getRefundBatch(User loginUser) {
        QueryWrapper<OcBRefundBatch> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("batch_status", OmsOrderRefundCommonEum.ONE.parseValue())
                .eq("ownerid", loginUser.getId())
                .eq("isactive", "Y")
                .or()
                .eq("ownerid", "default_semir")
                .orderByDesc("creationdate");
        return ocBRefundBatchMapper.selectList(queryWrapper);
    }

    /**
     * item转换为item扩展类
     * OcBReturnOrderRefund转换为OcBRefundInProductItem，方便后续退货入库保存
     *
     * @param bean OcBReturnOrderRefund
     * @return OcBRefundInProductItem
     */
    private OcBRefundInProductItemExt changeItemStatus(OcBReturnOrderRefund bean, Long ocBOrderid) {
        OcBRefundInProductItemExt productItem = new OcBRefundInProductItemExt();
        //设置订单编号  显示前端
        if (null != ocBOrderid) {
            productItem.setOcBOrderId(ocBOrderid);
        }
        productItem.setId(bean.getId());
        productItem.setSkuSpec(bean.getSkuSpec());
        productItem.setGbcode(bean.getBarcode());
        productItem.setPsCSkuId(bean.getPsCSkuId());
        productItem.setPsCSkuEcode(bean.getPsCSkuEcode());
        productItem.setPsCProId(bean.getPsCProId());
        productItem.setPsCProEcode(bean.getPsCProEcode());
        productItem.setPsCProEname(bean.getPsCProEname());
        productItem.setPsCClrId(bean.getPsCClrId());
        productItem.setPsCClrEcode(bean.getPsCClrEcode());
        productItem.setPsCClrEname(bean.getPsCClrEname());
        productItem.setPsCSizeId(bean.getPsCSizeId());
        productItem.setPsCSizeEcode(bean.getPsCSizeEcode());
        productItem.setPsCSizeEname(bean.getPsCSizeEname());
        productItem.setQty(bean.getQtyRefund());
        productItem.setOcBReturnOrderId(bean.getOcBReturnOrderId());
        productItem.setVersion(bean.getVersion());
        productItem.setOwnerename(bean.getOwnerename());
        productItem.setModifierename(bean.getModifierename());
        productItem.setAdClientId(bean.getAdClientId());
        productItem.setAdOrgId(bean.getAdOrgId());
        productItem.setCreationdate(bean.getCreationdate());
        productItem.setModifieddate(bean.getModifieddate());
        productItem.setOwnerid(bean.getOwnerid());
        productItem.setAdClientId(bean.getAdClientId());
        productItem.setOwnername(bean.getOwnername());
        productItem.setModifierid(bean.getModifierid());
        productItem.setModifiername(bean.getModifiername());
        productItem.setIsactive(bean.getIsactive());
        //是否无原单条码
        productItem.setIsWithoutOrig(0);
        //1正品0次品
        productItem.setProductMark(bean.getProductMark() != null ? bean.getProductMark() : "1");
        //扫描数量默认0
        productItem.setQtyScan(new BigDecimal("0"));
        return productItem;
    }

    /**
     * OcBRefundOrder数据转移到OcBReturnOrderExt
     *
     * @param bean OcBRefundOrder
     * @param list 明细列表
     */
    private OcBReturnOrderExt changeDataStatus(OcBReturnOrder bean, List<OcBReturnOrderRefund> list, Long ocBOrderid) {
        OcBReturnOrderExt OcBReturnOrderExt = new OcBReturnOrderExt();
        List<OcBRefundInProductItemExt> extList = new ArrayList<>();
        if (list != null && list.size() != 0) {
            for (OcBReturnOrderRefund ocBReturnOrderRefund : list) {
                extList.add(changeItemStatus(ocBReturnOrderRefund, ocBOrderid));
            }
        }
        if (null != ocBOrderid) {
            OcBReturnOrderExt.setId(null);
            OcBReturnOrderExt.setOcBOrderId(ocBOrderid);
        } else {
            OcBReturnOrderExt.setId(bean.getId());
        }
        OcBReturnOrderExt.setItemList(extList);
        OcBReturnOrderExt.setTid(bean.getTid());
        OcBReturnOrderExt.setIsForce(bean.getIsForce());
        OcBReturnOrderExt.setCpCStoreId(bean.getCpCStoreId());
        OcBReturnOrderExt.setCpCStoreEcode(bean.getCpCStoreEcode());
        OcBReturnOrderExt.setCpCStoreEname(bean.getCpCStoreEname());
        OcBReturnOrderExt.setOrdeSource(bean.getOrdeSource());
        OcBReturnOrderExt.setWmsCancelStatus(bean.getWmsCancelStatus());
        OcBReturnOrderExt.setReturnFlag(bean.getReturnFlag());
        OcBReturnOrderExt.setSysMsg(bean.getSysMsg());
        OcBReturnOrderExt.setIsReceiveConfirm(bean.getIsReceiveConfirm());
        OcBReturnOrderExt.setTbDisputeId(bean.getTbDisputeId());
        OcBReturnOrderExt.setWmsFailreason(bean.getWmsFailreason());
        OcBReturnOrderExt.setIsTowms(bean.getIsTowms());
        OcBReturnOrderExt.setIsNotlogmber(bean.getIsNotlogmber());
        OcBReturnOrderExt.setIsInstorage(bean.getIsInstorage());
        OcBReturnOrderExt.setIsExamine(bean.getIsExamine());
        OcBReturnOrderExt.setCheckFaileInfo(bean.getCheckFaileInfo());
        OcBReturnOrderExt.setIsCheck(bean.getIsCheck());
        OcBReturnOrderExt.setIsRefund(bean.getIsRefund());
        OcBReturnOrderExt.setInerId(bean.getInerId());
        OcBReturnOrderExt.setInerEname(bean.getInerEname());
        OcBReturnOrderExt.setInerName(bean.getInerName());
        OcBReturnOrderExt.setCheckerId(bean.getCheckerId());
        OcBReturnOrderExt.setCheckerEname(bean.getCheckerEname());
        OcBReturnOrderExt.setCheckerName(bean.getCheckerName());
        OcBReturnOrderExt.setInTime(bean.getInTime());
        OcBReturnOrderExt.setAuditTime(bean.getAuditTime());
        OcBReturnOrderExt.setQtyInstore(bean.getQtyInstore());
        OcBReturnOrderExt.setReturnPhase(bean.getReturnPhase());
        OcBReturnOrderExt.setAllSku(bean.getAllSku());
        OcBReturnOrderExt.setOvertimeInterval(bean.getOvertimeInterval());
        OcBReturnOrderExt.setOrderflag(bean.getOrderflag());
        OcBReturnOrderExt.setIsReserved(bean.getIsReserved());
        OcBReturnOrderExt.setDistributorId(bean.getDistributorId());
        OcBReturnOrderExt.setCpCPhyWarehouseId(bean.getCpCPhyWarehouseId());
        OcBReturnOrderExt.setSellerNick(bean.getSellerNick());
        OcBReturnOrderExt.setUserid(bean.getUserid());
        OcBReturnOrderExt.setInventedStatus(bean.getInventedStatus());
        OcBReturnOrderExt.setProReturnStatus(bean.getProReturnStatus());
        OcBReturnOrderExt.setPlatform(bean.getPlatform());
        OcBReturnOrderExt.setShipAmt(bean.getShipAmt());
        OcBReturnOrderExt.setOrigOrderId(bean.getOrigOrderId());
        OcBReturnOrderExt.setReceiveName(bean.getReceiveName());
        OcBReturnOrderExt.setReceiverAreaName(bean.getReceiverAreaName());
        OcBReturnOrderExt.setReceiverCityName(bean.getReceiverCityName());
        OcBReturnOrderExt.setReceiverProvinceName(bean.getReceiverProvinceName());
        OcBReturnOrderExt.setReturnId(bean.getReturnId());
        OcBReturnOrderExt.setExchangeAmt(bean.getExchangeAmt());
        OcBReturnOrderExt.setReceiverAreaId(bean.getReceiverAreaId());
        OcBReturnOrderExt.setReceiverCityId(bean.getReceiverCityId());
        OcBReturnOrderExt.setReceiverProvinceId(bean.getReceiverProvinceId());
        OcBReturnOrderExt.setRemark(bean.getRemark());
        OcBReturnOrderExt.setLogisticsCode(bean.getLogisticsCode());
        OcBReturnOrderExt.setReceivePhone(bean.getReceivePhone());
        OcBReturnOrderExt.setReceiveMobile(bean.getReceiveMobile());
        OcBReturnOrderExt.setReceiveZip(bean.getReceiveZip());
        OcBReturnOrderExt.setReceiveAddress(bean.getReceiveAddress());
        OcBReturnOrderExt.setCpCLogisticsId(bean.getCpCLogisticsId());
        OcBReturnOrderExt.setCpCLogisticsEcode(bean.getCpCLogisticsEcode());
        OcBReturnOrderExt.setCpCLogisticsEname(bean.getCpCLogisticsEname());
        OcBReturnOrderExt.setReturnDesc(bean.getReturnDesc());
        OcBReturnOrderExt.setReturnReason(bean.getReturnReason());
        OcBReturnOrderExt.setBuyerNick(bean.getBuyerNick());
        OcBReturnOrderExt.setOrigSourceCode(bean.getOrigSourceCode());
        OcBReturnOrderExt.setReturnAmtOther(bean.getReturnAmtOther());
        OcBReturnOrderExt.setReturnAmtShip(bean.getReturnAmtShip());
        OcBReturnOrderExt.setReturnAmtList(bean.getReturnAmtList());
        OcBReturnOrderExt.setReturnAmtActual(bean.getReturnAmtActual());
        OcBReturnOrderExt.setReturnTime(bean.getReturnTime());
        OcBReturnOrderExt.setLastUpdateTime(bean.getLastUpdateTime());
        OcBReturnOrderExt.setReturnCreateTime(bean.getReturnCreateTime());
        OcBReturnOrderExt.setReturnStatus(bean.getReturnStatus());
        OcBReturnOrderExt.setOrigOrderStatus(bean.getOrigOrderStatus());
        OcBReturnOrderExt.setBillType(bean.getBillType());
        OcBReturnOrderExt.setIsAdd(bean.getIsAdd());
        OcBReturnOrderExt.setIsTodrp(bean.getIsTodrp());
        OcBReturnOrderExt.setIsToag(bean.getIsToag());
        OcBReturnOrderExt.setIsBack(bean.getIsBack());
        OcBReturnOrderExt.setCpCShopId(bean.getCpCShopId());
        OcBReturnOrderExt.setCpCShopTitle(bean.getCpCShopTitle());
        OcBReturnOrderExt.setConsignAmtSettle(bean.getConsignAmtSettle());
        OcBReturnOrderExt.setReturnType(bean.getReturnType());
        OcBReturnOrderExt.setVersion(bean.getVersion());
        OcBReturnOrderExt.setOwnerename(bean.getOwnerename());
        OcBReturnOrderExt.setModifierename(bean.getModifierename());
        OcBReturnOrderExt.setAdOrgId(bean.getAdOrgId());
        OcBReturnOrderExt.setCreationdate(bean.getCreationdate());
        OcBReturnOrderExt.setModifieddate(bean.getModifieddate());
        OcBReturnOrderExt.setOwnerid(bean.getOwnerid());
        OcBReturnOrderExt.setOwnername(bean.getOwnername());
        OcBReturnOrderExt.setModifierid(bean.getModifierid());
        OcBReturnOrderExt.setModifiername(bean.getModifiername());
        OcBReturnOrderExt.setIsactive(bean.getIsactive());
        return OcBReturnOrderExt;
    }

    /**
     * 通过批次号获取批次号的逻辑仓信息
     *
     * @param batchId
     * @return
     */
    public ValueHolderV14 getLogicalWarehouseInfo(Long batchId) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        if (batchId == null) {
            holderV14.setCode(-1);
            holderV14.setMessage("参数为空!");
            return holderV14;
        }
        Map<String, Object> map = new HashMap<>();
        OcBRefundBatch ocBRefundBatch = ocBRefundBatchMapper.selectById(batchId);
        if (ocBRefundBatch != null) {
            if (ocBRefundBatch.getInStoreId() != null) {
                CpStore cpStore = cpRpcService.selectCpCStoreById(ocBRefundBatch.getInStoreId());
                if (null != cpStore) {
                    map.put("STORE_ID", cpStore.getId());
                    map.put("STORE_ECODE", cpStore.getEcode());
                    map.put("STORE_ENAME", cpStore.getEname());
                    map.put("BATCH_TYPE", ocBRefundBatch.getBatchType());
                } else {
                    holderV14.setCode(-1);
                    holderV14.setMessage("未查询到数据!");
                    return holderV14;
                }
            } else {
                map.put("STORE_ID", ocBRefundBatch.getInStoreId());
                map.put("STORE_ECODE", ocBRefundBatch.getInStoreEcode());
                map.put("STORE_ENAME", ocBRefundBatch.getInStoreEname());
                map.put("BATCH_TYPE", ocBRefundBatch.getBatchType());
            }
        } else {
            holderV14.setCode(-1);
            holderV14.setMessage("未查询到数据!");
            return holderV14;
        }
        holderV14.setCode(0);
        holderV14.setMessage("查询成功!");
        holderV14.setData(map);
        return holderV14;
    }
}

