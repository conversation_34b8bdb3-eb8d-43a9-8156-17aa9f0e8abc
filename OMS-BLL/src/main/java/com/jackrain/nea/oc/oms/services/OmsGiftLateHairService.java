package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.model.enums.OmsSpiltRuleEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.SpiltOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/8/4 下午3:12
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsGiftLateHairService {

    /**
     * 处理赠品晚发
     *
     * @param orderParam
     * @param user
     */
    public Map<Set<Long>, SpiltOrderParam> giftLateService(OcBOrderParam orderParam, User user){
        OcBOrder ocBOrder = orderParam.getOcBOrder();
        List<OcBOrderItem> orderItemList = orderParam.getOrderItemList();
        List<OcBOrderItem> giftItems = orderItemList.stream().filter(p -> p.getIsGiftSplit() != null && p.getIsGiftSplit() == 3).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(giftItems)){
            //无赠品晚发明细
            return null;
        }
        List<OcBOrderItem> noGiftItems = orderItemList.stream().filter(p -> p.getIsGiftSplit() == null || p.getIsGiftSplit() != 3).collect(Collectors.toList());
        //封装成拆单的map
        Map<Set<Long>, SpiltOrderParam> splitOrderMap = new HashMap<>(16);
        Set<Long> giftIds = giftItems.stream().map(OcBOrderItem::getId).collect(Collectors.toSet());
        SpiltOrderParam param = new SpiltOrderParam();
        param.setHoldOrder(true);
        splitOrderMap.put(giftIds, param);
        Set<Long> noGiftItemIds = noGiftItems.stream().map(OcBOrderItem::getId).collect(Collectors.toSet());
        SpiltOrderParam param1 = new SpiltOrderParam();
        splitOrderMap.put(noGiftItemIds, param1);
        return splitOrderMap;
    }
}
