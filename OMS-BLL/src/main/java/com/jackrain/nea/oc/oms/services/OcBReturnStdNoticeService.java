

package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.oc.oms.mapper.OcBReturnStdNoticeMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnStdNotice;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 退货单入库私域通知
 *
 * <AUTHOR>
 * @date 2021-12-30 11:55:13
 */
@Service
@RequiredArgsConstructor
public class OcBReturnStdNoticeService extends ServiceImpl<OcBReturnStdNoticeMapper, OcBReturnStdNotice> {


    /**
     * @description:  查询需要传std 的集合
     * @param limit
     * @param times
     * @return: java.util.List<com.jackrain.nea.oc.oms.model.table.OcBReturnStdNotice>
     * <AUTHOR>
     * @date: 2021/12/30 13:46
     */
    public List<OcBReturnStdNotice> listNeedToStd(int limit,int times){
        IPage page = new Page(1,limit);
        IPage dataPage = this.page(page, Wrappers.<OcBReturnStdNotice>lambdaQuery()
                .in(OcBReturnStdNotice::getNoticeStatus, 0, -1)
                .le(OcBReturnStdNotice::getNoticeTimes, times)
                .orderByDesc(OcBReturnStdNotice::getUpdateTime));
        return dataPage.getRecords();
    }
}
