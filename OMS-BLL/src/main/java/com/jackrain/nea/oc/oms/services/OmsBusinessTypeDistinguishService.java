package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.constant.NaiKaOrderTypeConstant;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.BusinessTypeMatchStRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.SpiltOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.table.StCBusinessTypeMatchStrategy;
import com.jackrain.nea.oc.oms.model.table.StCBusinessTypeMatchStrategyItem;
import com.jackrain.nea.oc.oms.nums.NaiKaOrderTypeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.ps.model.OmsProAttributeInfo;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.psext.model.table.PsCCollShopProMapping;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/7/14 下午3:29
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsBusinessTypeDistinguishService {

    private static final List<String> TYPE1 = Lists.newArrayList("1");
    private static final List<String> TYPE2 = Lists.newArrayList("5", "6");
    private static final List<Integer> PLATFORM = Lists.newArrayList(60, 116);

    private static final String DEFAULT_VALUE = "RYCK14";

    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsBusinessTypeDistinguishService omsBusinessTypeDistinguishService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBOrderNaiKaMapper naiKaMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    public Map<Set<Long>, SpiltOrderParam> businessTypeDistinguish(OcBOrderRelation orderInfo, User user) {
        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        boolean b = this.handleExchangeAndCopyOrder(ocBOrder, user);
        if (b) {
            return null;
        }
        Integer platform = ocBOrder.getPlatform();
        List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
        //业务类型  通用转单时会赋值 根据此字段匹配最终业务类型 更新上去
        String businessType = ocBOrder.getBusinessType();

        // 特殊平台 走策略匹配
        if (PLATFORM.contains(platform) && ObjectUtil.notEqual("38", ocBOrder.getGwSourceGroup()) && ObjectUtil.notEqual("39", ocBOrder.getGwSourceGroup())) {
            businessType = null;
        }

        if (PlatFormEnum.CREATE_CARD_CODE.getCode().equals(platform) || PlatFormEnum.CARD_CODE.getCode().equals(platform)) {
            return getSetSpiltOrderParamMap(user, ocBOrder, platform, orderItemList, businessType);
        }

        if (businessType == null || !(TYPE1.contains(businessType) || TYPE2.contains(businessType))) {
            List<BusinessTypeMatchStRelation> relations = omsBusinessTypeStService.selectBusinessTypeByOrderType(1);
            if (CollectionUtils.isNotEmpty(relations)) {
                Map<Set<Long>, SpiltOrderParam> setSpiltOrderParamMap = handleNull(relations, ocBOrder, businessType, platform, orderItemList, user);
                return setSpiltOrderParamMap;
            } else {
                //直接更新为"电商销售"
                omsBusinessTypeDistinguishService.handleOrder(ocBOrder, null);
            }
        }
        if (TYPE1.contains(businessType)) {
            BusinessTypeResult result = handleTYPE1();
            omsBusinessTypeDistinguishService.handleOrder(ocBOrder, result);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.BUSINESS_TYPE.getKey(), "业务类型区分", null, null, user);
        }
        if (TYPE2.contains(businessType)) {
            BusinessTypeResult result = handleTYPE2(ocBOrder, orderItemList);
            omsBusinessTypeDistinguishService.handleOrder(ocBOrder, result);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.BUSINESS_TYPE.getKey(), "业务类型区分", null, null, user);
        }
        return null;
    }

    private Map<Set<Long>, SpiltOrderParam> getSetSpiltOrderParamMap(User user, OcBOrder ocBOrder, Integer platform, List<OcBOrderItem> orderItemList, String businessType) {
        List<BusinessTypeResult> businessTypeResultList = new ArrayList<>();
        // 根据小程序给的明细的业务类型 组装setSpiltOrderParamMap
        // 需要注意 如果在前面的促销策略里面增加了赠品 相当于增加了明细行。那此时的明细行是不会有order_type的 需要注意对这种空的处理
        for (OcBOrderItem ocBOrderItem : orderItemList) {
            if (StringUtils.isNotEmpty(ocBOrderItem.getReserveVarchar03())) {
                Integer naikaOrderType = Integer.valueOf(ocBOrderItem.getReserveVarchar03());
                if (ObjectUtil.equal(NaiKaOrderTypeConstant.CARD_PLAN, naikaOrderType) ||
                        ObjectUtil.equal(NaiKaOrderTypeConstant.CYLE_PLAN, naikaOrderType)) {
                    // 提奶 也就是 5，6 需要特殊处理  而且提奶单 只会有单sku 单明细行
                    BusinessTypeResult businessTypeResult = handleNaiKaPickUp(ocBOrder);
                    if (businessTypeResult == null) {
                        throw new NDSException("提奶订单未匹配到业务类型");
                    }
                    businessTypeResultList.add(businessTypeResult);
                } else {
                    // 根据类型来直接获取中台的业务档案
                    NaiKaOrderTypeEnum naiKaOrderTypeEnum =
                            NaiKaOrderTypeEnum.getByNaiKaOrderType(naikaOrderType);
                    if (naiKaOrderTypeEnum == null) {
                        throw new NDSException("根据小程序订单明细的类型 找不到对应的业务类型");
                    }
                    StCBusinessType stCBusinessType =
                            omsBusinessTypeStService.selectStCBusinessTypeByCode(naiKaOrderTypeEnum.getBusinessTypeCode());
                    BusinessTypeResult businessTypeResult = new BusinessTypeResult();
                    businessTypeResult.setCode(stCBusinessType.getEcode());
                    businessTypeResult.setName(stCBusinessType.getEname());
                    businessTypeResult.setId(stCBusinessType.getId());
                    businessTypeResult.setCheckSource(stCBusinessType.getIsCheckSource());
                    businessTypeResult.setItemId(ocBOrderItem.getId());
                    businessTypeResultList.add(businessTypeResult);
                }
            } else {
                // 如果为空 则走业务类型匹配策略。 可能是因为促销或者对等换货、组合商品拆分 增加的赠品
                List<BusinessTypeMatchStRelation> relations = omsBusinessTypeStService.selectBusinessTypeByOrderType(1);
                BusinessTypeResult result = matchNullOrderType(ocBOrder, platform, businessType, ocBOrderItem, relations);
                if (ObjectUtil.isNull(result)) {
                    result = new BusinessTypeResult();
                    // 如果为空 则直接赋值电商销售
                    StCBusinessType ryck14 = omsBusinessTypeStService.selectStCBusinessTypeByCode(DEFAULT_VALUE);
                    result.setCheckSource(ryck14.getIsCheckSource());
                    result.setItemId(ocBOrderItem.getId());
                    result.setId(ryck14.getId());
                    result.setName(ryck14.getEname());
                    result.setCode(ryck14.getEcode());
                }
                businessTypeResultList.add(result);
            }
        }
        Map<String, List<BusinessTypeResult>> resultMap = businessTypeResultList.stream().collect(Collectors.groupingBy(BusinessTypeResult::getCode));
        if (resultMap.keySet().size() == 1) {
            BusinessTypeResult result = businessTypeResultList.get(0);
            omsBusinessTypeDistinguishService.handleOrder(ocBOrder, result);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.BUSINESS_TYPE.getKey(), "业务类型匹配成功", null, null, user);
            return null;
        }
        Map<Set<Long>, SpiltOrderParam> setSpiltOrderParamMap = checkMultiple(businessTypeResultList, resultMap);
        if (setSpiltOrderParamMap == null || setSpiltOrderParamMap.isEmpty()) {
            //多个类型 走默认 电商销售
            this.defaultType(ocBOrder);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.BUSINESS_TYPE.getKey(), "匹配到多个业务类型,走默认", null, null, user);
            return null;
        }
        Set<Long> itemIds = orderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toSet());
        Set<Set<Long>> sets = setSpiltOrderParamMap.keySet();
        for (Set<Long> set : sets) {
            for (Long aLong : set) {
                itemIds.remove(aLong);
            }
        }
        if (CollectionUtils.isNotEmpty(itemIds)) {
            SpiltOrderParam param = new SpiltOrderParam();
            StCBusinessType ryck14 = omsBusinessTypeStService.selectStCBusinessTypeByCode(DEFAULT_VALUE);
            if (ryck14 != null) {
                param.setBusinessTypeName(ryck14.getEname());
                param.setBusinessTypeCode(ryck14.getEcode());
                param.setBusinessTypeId(ryck14.getId());
            }
            setSpiltOrderParamMap.put(itemIds, param);
        }
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.BUSINESS_TYPE.getKey(), "匹配到不同的业务类型,等待拆单", null, null, user);
        return setSpiltOrderParamMap;
    }

    private BusinessTypeResult matchNullOrderType(OcBOrder ocBOrder, Integer platform, String businessType, OcBOrderItem ocBOrderItem, List<BusinessTypeMatchStRelation> relations) {
        for (BusinessTypeMatchStRelation relation : relations) {
            StCBusinessTypeMatchStrategy matchStrategy = relation.getMatchStrategy();
            List<StCBusinessTypeMatchStrategyItem> matchStrategyItems = relation.getMatchStrategyItems();
            boolean flag = true;
            for (StCBusinessTypeMatchStrategyItem matchStrategyItem : matchStrategyItems) {
                //识别类型 1=交易平台 2=物料组 3=SAP单据类型 4=商品实付
                Integer discernType = matchStrategyItem.getDiscernType();
                //内容
                String discernContent = matchStrategyItem.getDiscernContent();
                Integer discernSymbol = matchStrategyItem.getDiscernSymbol();
                boolean match = match(discernType, discernContent, businessType, ocBOrder.getGwSourceGroup(), discernSymbol, platform, ocBOrderItem);
                if (!match) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                BusinessTypeResult result = buildBusinessType(matchStrategy);
                if (result != null) {
                    result.setItemId(ocBOrderItem.getId());
                    return result;
                }
            }
        }
        return null;
    }


    /**
     * "零售发货单退换货单生成的换货单业务类型逻辑需优化：
     * 1、原订单的业务类型为：奶卡提货、免费奶卡提货、奶卡周期购提货、免费奶卡周期购提货、中台周期购提货、免费中台周期购提货 ​对应的换货订单取原订单的业务类型；
     * 2、除以上类型外，换货订单保存时需要重新匹配订单业务类型策略；"
     *
     * @param ocBOrder
     */
    private boolean handleExchangeAndCopyOrder(OcBOrder ocBOrder, User user) {
        Integer orderType = ocBOrder.getOrderType();
        boolean b = YesNoEnum.Y.getVal().equals(ocBOrder.getIsResetShip())
                || YesNoEnum.Y.getVal().equals(ocBOrder.getIsCopyOrder());
        if (!(OrderTypeEnum.EXCHANGE.getVal().equals(orderType) || b)) {
            return false;
        }
        Long origOrderId = ocBOrder.getOrigOrderId();
        if (origOrderId == null) {
            return false;
        }
        OcBOrder exchangeOrder = ocBOrderMapper.selectById(origOrderId);
        if (exchangeOrder == null) {
            return false;
        }
        String businessTypeCode = exchangeOrder.getBusinessTypeCode();
        if (StringUtils.isEmpty(businessTypeCode)) {
            return false;
        }
        if (OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.CYCLE_PICK_UP.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER_PICK_UP.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(businessTypeCode)
                || OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(businessTypeCode)) {
            OcBOrder order = new OcBOrder();
            order.setId(ocBOrder.getId());
            order.setBusinessTypeId(exchangeOrder.getBusinessTypeId());
            order.setBusinessTypeName(exchangeOrder.getBusinessTypeName());
            order.setBusinessTypeCode(exchangeOrder.getBusinessTypeCode());
            ocBOrderMapper.updateById(order);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.BUSINESS_TYPE.getKey(), "该业务类型的订单取原单业务类型", null, null, user);
            return true;
        }
        return false;
    }


    public void handleOrder(OcBOrder ocBOrder, BusinessTypeResult result) {
        if (result != null) {
            OcBOrder order = new OcBOrder();
            order.setId(ocBOrder.getId());
            order.setBusinessTypeCode(result.getCode());
            order.setBusinessTypeName(result.getName());
            order.setBusinessTypeId(result.getId());
            if (result.getShopId() != null) {
                order.setGwSourceCode(result.getShopId() + "");
                ocBOrder.setGwSourceCode(result.getShopId() + "");
                //重新更新合单加密串
                MD5Util.encryptOrderInfo4Merge(ocBOrder);
                order.setOrderEncryptionCode(ocBOrder.getOrderEncryptionCode());
            }
            ocBOrderMapper.updateById(order);
            extracted(order, result);
            return;
        }
        //更新为默认类型
        defaultType(ocBOrder);
    }

    private void extracted(OcBOrder ocBOrder, BusinessTypeResult result) {
        List<OcBOrderNaiKa> ocBOrderNaiKas = naiKaMapper.selectNaiKaByOcBOrderId(ocBOrder.getId());
        if (CollectionUtils.isEmpty(ocBOrderNaiKas)) {
            return;
        }
        for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKas) {
            OcBOrderNaiKa updateNaiKa = new OcBOrderNaiKa();
            updateNaiKa.setId(ocBOrderNaiKa.getId());
            updateNaiKa.setOcBOrderId(ocBOrderNaiKa.getOcBOrderId());
            updateNaiKa.setBusinessTypeId(result.getId());
            updateNaiKa.setBusinessTypeCode(result.getCode());
            updateNaiKa.setBusinessTypeName(result.getName());
            OrderBusinessTypeCodeEnum orderBusinessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(result.getCode());
            updateNaiKa.setBusinessType(orderBusinessTypeCodeEnum == null ? "" : orderBusinessTypeCodeEnum.getNaiKaType());
            naiKaMapper.updateById(updateNaiKa);
        }
    }

    private void defaultType(OcBOrder ocBOrder) {
        StCBusinessType ryck14 = omsBusinessTypeStService.selectStCBusinessTypeByCode(DEFAULT_VALUE);
        if (ryck14 != null) {
            OcBOrder order = new OcBOrder();
            order.setId(ocBOrder.getId());
            order.setBusinessTypeCode(ryck14.getEcode());
            order.setBusinessTypeName(ryck14.getEname());
            order.setBusinessTypeId(ryck14.getId());
            ocBOrderMapper.updateById(order);
        }
    }

    /**
     * 处理业务类型为空的情况
     *
     * @param businessType
     * @param platform
     * @param orderItemList
     * @return
     */
    private Map<Set<Long>, SpiltOrderParam> handleNull(List<BusinessTypeMatchStRelation> relations, OcBOrder order,
                                                       String businessType, Integer platform, List<OcBOrderItem> orderItemList, User user) {
        //需要过滤掉
        List<BusinessTypeResult> itemMapList = new ArrayList<>();
        String gwSourceGroup = order.getGwSourceGroup();
        List<OcBOrderItem> itemList = orderItemList.stream().filter(p -> p.getProType() != null && p.getProType() != 4).collect(Collectors.toList());
        for (BusinessTypeMatchStRelation relation : relations) {
            List<BusinessTypeResult> businessTypeResults = handleNullMatch(relation, gwSourceGroup, businessType, platform, itemList);
            if (CollectionUtils.isNotEmpty(businessTypeResults)) {
                itemMapList.addAll(businessTypeResults);
            }
        }
        if (CollectionUtils.isEmpty(itemMapList)) {
            //默认值 电商销售
            this.defaultType(order);
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.BUSINESS_TYPE.getKey(), "未匹配到符合条件的业务类型,走默认值", null, null, user);
            return null;
        }
        Map<String, List<BusinessTypeResult>> resultMap = itemMapList.stream().collect(Collectors.groupingBy(BusinessTypeResult::getCode));
        if (resultMap.keySet().size() == 1) {
            BusinessTypeResult result = itemMapList.get(0);
            omsBusinessTypeDistinguishService.handleOrder(order, result);
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.BUSINESS_TYPE.getKey(), "业务类型匹配成功", null, null, user);
            return null;
        }
        Map<Set<Long>, SpiltOrderParam> setSpiltOrderParamMap = checkMultiple(itemMapList, resultMap);
        if (setSpiltOrderParamMap == null || setSpiltOrderParamMap.isEmpty()) {
            //多个类型 走默认 电商销售
            this.defaultType(order);
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.BUSINESS_TYPE.getKey(), "匹配到多个业务类型,走默认:" + JSONUtil.toJsonStr(itemMapList), null, null, user);
            return null;
        }
        Set<Long> itemIds = orderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toSet());
        Set<Set<Long>> sets = setSpiltOrderParamMap.keySet();
        for (Set<Long> set : sets) {
            for (Long aLong : set) {
                itemIds.remove(aLong);
            }
        }
        if (CollectionUtils.isNotEmpty(itemIds)) {
            SpiltOrderParam param = new SpiltOrderParam();
            StCBusinessType ryck14 = omsBusinessTypeStService.selectStCBusinessTypeByCode(DEFAULT_VALUE);
            if (ryck14 != null) {
                param.setBusinessTypeName(ryck14.getEname());
                param.setBusinessTypeCode(ryck14.getEcode());
                param.setBusinessTypeId(ryck14.getId());
            }
            setSpiltOrderParamMap.put(itemIds, param);
        }
        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.BUSINESS_TYPE.getKey(), "匹配到不同的业务类型,等待拆单", null, null, user);
        return setSpiltOrderParamMap;
    }


    private Map<Set<Long>, SpiltOrderParam> checkMultiple(List<BusinessTypeResult> itemMapList, Map<String, List<BusinessTypeResult>> resultMap) {
        Map<Long, List<BusinessTypeResult>> itemTypeMap = itemMapList.stream().collect(Collectors.groupingBy(BusinessTypeResult::getItemId));
        for (Long id : itemTypeMap.keySet()) {
            List<BusinessTypeResult> businessTypeResults = itemTypeMap.get(id);
            Map<String, List<BusinessTypeResult>> collect = businessTypeResults.stream().collect(Collectors.groupingBy(BusinessTypeResult::getCode));
            if (collect.keySet().size() > 1) {
                //多个 默认
                return null;
            }
        }
        Map<Set<Long>, SpiltOrderParam> paramMap = new HashMap<>();
        for (String s : resultMap.keySet()) {
            List<BusinessTypeResult> businessTypeResults = resultMap.get(s);
            Set<Long> ids = businessTypeResults.stream().map(BusinessTypeResult::getItemId).collect(Collectors.toSet());
            SpiltOrderParam param = new SpiltOrderParam();
            BusinessTypeResult result = businessTypeResults.get(0);
            param.setBusinessTypeId(result.getId());
            param.setBusinessTypeCode(result.getCode());
            param.setBusinessTypeName(result.getName());
            paramMap.put(ids, param);
        }
        return paramMap;
    }

    public List<BusinessTypeResult> handleNullMatch(BusinessTypeMatchStRelation relation, String gwSourceGroup,
                                                    String businessType, Integer platform, List<OcBOrderItem> orderItemList) {
        StCBusinessTypeMatchStrategy matchStrategy = relation.getMatchStrategy();
        List<StCBusinessTypeMatchStrategyItem> matchStrategyItems = relation.getMatchStrategyItems();
        List<BusinessTypeResult> businessTypeList = new ArrayList<>();
        for (OcBOrderItem item : orderItemList) {
            boolean flag = true;
            for (StCBusinessTypeMatchStrategyItem matchStrategyItem : matchStrategyItems) {
                //识别类型 1=交易平台 2=物料组 3=SAP单据类型 4=商品实付
                Integer discernType = matchStrategyItem.getDiscernType();
                //内容
                String discernContent = matchStrategyItem.getDiscernContent();
                Integer discernSymbol = matchStrategyItem.getDiscernSymbol();
                boolean match = match(discernType, discernContent, businessType, gwSourceGroup, discernSymbol, platform, item);
                if (!match) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                BusinessTypeResult result = buildBusinessType(matchStrategy);
                if (result != null) {
                    result.setItemId(item.getId());
                    businessTypeList.add(result);
                }
            }
        }
        return businessTypeList;

    }

    private boolean match(Integer discernType, String discernContent, String businessType, String gwSourceGroup,
                          Integer discernSymbol, Integer platform, OcBOrderItem item) {
        if (StringUtils.isEmpty(discernContent)) {
            return false;
        }
        if (gwSourceGroup == null) {
            gwSourceGroup = "";
        }
        switch (discernType) {
            case 1:
                return matchDiscernSymbol(platform + "", discernContent, discernSymbol + "");
            case 2:
                //查询物料组
                String psCSkuEcode = item.getPsCSkuEcode();
                ProductSku skuInfo = psRpcService.selectProductSku(psCSkuEcode);
                if (skuInfo != null) {
                    Map<String, OmsProAttributeInfo> proAttributeMap = skuInfo.getProAttributeMap();
                    if (proAttributeMap != null && !proAttributeMap.isEmpty()) {
                        OmsProAttributeInfo info = proAttributeMap.get("M_DIM2_ID");
                        if (info != null) {
                            String ecode = info.getEcode();
                            return matchDiscernSymbol(ecode, discernContent, discernSymbol + "");
                        } else {
                            return matchDiscernSymbol("", discernContent, discernSymbol + "");
                        }
                    }
                }
                return matchDiscernSymbol("", discernContent, discernSymbol + "");
            case 3:
                return matchDiscernSymbol(businessType, discernContent, discernSymbol + "");
            case 4:
                BigDecimal realAmt = item.getRealAmt();
                return matchDiscernSymbol(realAmt.toString(), discernContent, discernSymbol + "");
            case 5:
                return matchDiscernSymbol(gwSourceGroup, discernContent, discernSymbol + "");
            default:
                break;
        }
        return false;

    }


    /**
     * 拆单
     *
     * @param spiltOrder
     */
    private Map<Set<Long>, SpiltOrderParam> spiltOrderHandle(Map<Long, List<OcBOrderItem>> spiltOrder, OcBOrder order) {
        Map<Set<Long>, SpiltOrderParam> splitMap = new HashMap<>();
        for (Long stMainId : spiltOrder.keySet()) {
            List<OcBOrderItem> orderItems = spiltOrder.get(stMainId);
            StCBusinessType stCBusinessType = omsBusinessTypeStService.selectStCBusinessTypeById(stMainId);
            SpiltOrderParam param = new SpiltOrderParam();
            param.setBusinessTypeId(stCBusinessType.getId());
            param.setBusinessTypeCode(stCBusinessType.getEcode());
            param.setBusinessTypeName(stCBusinessType.getEname());
            Set<Long> itemList = new HashSet<>();
            for (OcBOrderItem orderItem : orderItems) {
                itemList.add(orderItem.getId());
            }
            splitMap.put(itemList, param);
        }
        return splitMap;
    }


    private BusinessTypeResult buildBusinessType(StCBusinessTypeMatchStrategy matchStrategy) {
        StCBusinessType stCBusinessType = omsBusinessTypeStService.selectStCBusinessTypeById(matchStrategy.getStCBusinessTypeId());
        if (stCBusinessType != null) {
            BusinessTypeResult result = new BusinessTypeResult();
            result.setId(stCBusinessType.getId());
            result.setName(stCBusinessType.getEname());
            result.setCode(stCBusinessType.getEcode());
            result.setCheckSource(stCBusinessType.getIsCheckSource());
            return result;
        }
        return null;
    }


    /**
     * 运算符 1=等于 2=不等于 3=大于 4=大于等于 5=小于 6=小于等于 7=在列表  8=不在列表
     *
     * @param content
     * @param discernContent
     * @param discernSymbol
     * @return
     */
    private boolean matchDiscernSymbol(String content, String discernContent, String discernSymbol) {

        boolean contentNum = NumberUtils.isNumber(content);
        boolean contentMatchNum = NumberUtils.isNumber(discernContent);

        boolean flag = false;
        switch (discernSymbol) {
            case "1":
                if (!contentNum || !contentMatchNum) {
                    flag = discernContent.equals(content);
                } else {
                    BigDecimal realAmt = new BigDecimal(content);
                    flag = realAmt.compareTo(new BigDecimal(discernContent)) == 0;
                }
                break;
            case "2":
                if (!contentNum || !contentMatchNum) {
                    flag = !discernContent.equals(content);
                } else {
                    BigDecimal realAmt = new BigDecimal(content);
                    flag = realAmt.compareTo(new BigDecimal(discernContent)) != 0;
                }
                break;
            case "3":
                if (!contentNum || !contentMatchNum) {
                    flag = false;
                } else {
                    BigDecimal realAmt = new BigDecimal(content);
                    flag = realAmt.compareTo(new BigDecimal(discernContent)) > 0;
                }
                break;
            case "4":
                if (!contentNum || !contentMatchNum) {
                    flag = false;
                } else {
                    BigDecimal realAmt = new BigDecimal(content);
                    flag = realAmt.compareTo(new BigDecimal(discernContent)) >= 0;
                }
                break;
            case "5":
                if (!contentNum || !contentMatchNum) {
                    flag = false;
                } else {
                    BigDecimal realAmt = new BigDecimal(content);
                    flag = realAmt.compareTo(new BigDecimal(discernContent)) < 0;
                }
                break;
            case "6":
                if (!contentNum || !contentMatchNum) {
                    flag = false;
                } else {
                    BigDecimal realAmt = new BigDecimal(content);
                    flag = realAmt.compareTo(new BigDecimal(discernContent)) <= 0;
                }
                break;
            case "7":
                if (contentNum && contentMatchNum) {
                } else {
                    flag = discernContent.contains(content);
                }
                break;
            case "8":
                if (contentNum && contentMatchNum) {
                    flag = false;
                } else {
                    if (StringUtils.isEmpty(content)) {
                        flag = true;
                    } else {
                        flag = !discernContent.contains(content);
                    }
                }
                break;
            default:
                return false;
        }
        return flag;
    }


    /**
     * 直接通过业务类型和订单类型查档案
     *
     * @return
     */
    public BusinessTypeResult handleTYPE1() {
        StCBusinessType stCBusinessType = omsBusinessTypeStService.selectStCBusinessTypeByCode("RYCK21");
        if (stCBusinessType != null) {
            //业务类型取档案的上的
            BusinessTypeResult result = new BusinessTypeResult();
            result.setCode(stCBusinessType.getEcode());
            result.setName(stCBusinessType.getEname());
            result.setId(stCBusinessType.getId());
            return result;
        }
        return null;

    }

    /**
     * 直接通过业务类型和订单类型查档案
     *
     * @return
     */
    public void handleTYPE1(OcBOrder order) {
        StCBusinessType stCBusinessType = omsBusinessTypeStService.selectStCBusinessTypeByCode("RYCK21");
        if (stCBusinessType != null) {
            //业务类型取档案的上的
            order.setBusinessTypeCode(stCBusinessType.getEcode());
            order.setBusinessTypeName(stCBusinessType.getEname());
            order.setBusinessTypeId(stCBusinessType.getId());
        }
    }

    private BusinessTypeResult handleNaiKaPickUp(OcBOrder ocBOrder) {
        List<OcBOrderNaiKa> ocBOrderNaiKas = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(ocBOrder.getId());
        if (CollectionUtils.isNotEmpty(ocBOrderNaiKas)) {
            OcBOrderNaiKa ocBOrderNaiKa = ocBOrderNaiKas.get(0);
            if (ocBOrderNaiKa != null) {
                String cardCode = ocBOrderNaiKa.getCardCode();
                if (StringUtils.isNotEmpty(cardCode)) {
                    //通过卡号查询原单
                    //virtual   entity 二级索引查询
                    List<OcBOrderNaiKa> ocBOrderNaiKas1 = ocBOrderNaiKaMapper.selectBySourceCodebyGsi(cardCode);
                    if (CollectionUtils.isNotEmpty(ocBOrderNaiKas1)) {
                        ocBOrderNaiKas1 = ocBOrderNaiKas1.stream().filter(p -> "virtual".equals(p.getBusinessType()) || "entity".equals(p.getBusinessType())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(ocBOrderNaiKas1)) {
                            Long ocBOrderId = ocBOrderNaiKas1.get(0).getOcBOrderId();
                            OcBOrder order = ocBOrderMapper.selectById(ocBOrderId);
                            Long businessTypeId = order.getBusinessTypeId();
                            StCBusinessType stCBusinessType = omsBusinessTypeStService.selectStCBusinessTypeById(businessTypeId);
                            if (stCBusinessType != null) {
                                Long pickGoodsTypeId = stCBusinessType.getPickGoodsTypeId();
                                if (pickGoodsTypeId != null) {
                                    Long shopId = order.getCpCShopId();
                                    //云集特殊处理
                                    shopId = checkYunJi(shopId, order.getPlatform(), order.getCpCShopEcode(), ocBOrderId);
                                    if (log.isDebugEnabled()) {
                                        log.debug(LogUtil.format("订单奶卡提货云集特殊处理，shopId:{}"), shopId);
                                    }
                                    StCBusinessType stCBusinessType1 = omsBusinessTypeStService.selectStCBusinessTypeById(pickGoodsTypeId);
                                    BusinessTypeResult result = new BusinessTypeResult();
                                    result.setId(stCBusinessType1.getId());
                                    result.setName(stCBusinessType1.getEname());
                                    result.setCode(stCBusinessType1.getEcode());
                                    result.setShopId(shopId);
                                    return result;
                                }
                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 根据订单奶卡查询原订单
     *
     * @param orderItemList
     * @return
     */
    private BusinessTypeResult handleTYPE2(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList) {
        List<OcBOrderNaiKa> ocBOrderNaiKas = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(ocBOrder.getId());
        if (CollectionUtils.isNotEmpty(ocBOrderNaiKas)) {
            OcBOrderNaiKa ocBOrderNaiKa = ocBOrderNaiKas.get(0);
            if (ocBOrderNaiKa != null) {
                String cardCode = ocBOrderNaiKa.getCardCode();
                if (StringUtils.isNotEmpty(cardCode)) {
                    //通过卡号查询原单
                    //virtual   entity 二级索引查询
                    List<OcBOrderNaiKa> ocBOrderNaiKas1 = ocBOrderNaiKaMapper.selectBySourceCodebyGsi(cardCode);
                    if (CollectionUtils.isNotEmpty(ocBOrderNaiKas1)) {
                        ocBOrderNaiKas1 = ocBOrderNaiKas1.stream().filter(p -> "virtual".equals(p.getBusinessType()) || "entity".equals(p.getBusinessType())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(ocBOrderNaiKas1)) {
                            Long ocBOrderId = ocBOrderNaiKas1.get(0).getOcBOrderId();
                            OcBOrder order = ocBOrderMapper.selectById(ocBOrderId);
                            Long businessTypeId = order.getBusinessTypeId();
                            StCBusinessType stCBusinessType = omsBusinessTypeStService.selectStCBusinessTypeById(businessTypeId);
                            if (stCBusinessType != null) {
                                Long pickGoodsTypeId = stCBusinessType.getPickGoodsTypeId();
                                if (pickGoodsTypeId != null) {
                                    Long shopId = order.getCpCShopId();
                                    //云集特殊处理
                                    shopId = checkYunJi(shopId, order.getPlatform(), order.getCpCShopEcode(), ocBOrderId);
                                    if (log.isDebugEnabled()) {
                                        log.debug(LogUtil.format("订单奶卡提货云集特殊处理，shopId:{}"), shopId);
                                    }
                                    StCBusinessType stCBusinessType1 = omsBusinessTypeStService.selectStCBusinessTypeById(pickGoodsTypeId);
                                    BusinessTypeResult result = new BusinessTypeResult();
                                    result.setId(stCBusinessType1.getId());
                                    result.setName(stCBusinessType1.getEname());
                                    result.setCode(stCBusinessType1.getEcode());
                                    result.setShopId(shopId);
                                    return result;
                                }
                            }
                        }
                    }

                }
            }
        }
        return null;
    }

    /**
     * description:云集特殊处理
     *
     * @Author: liuwenjin
     * @Date 2022/9/27 18:19
     */
    private Long checkYunJi(Long shopId, Integer platform, String cpCShopEcode, Long ocBOrderId) {
        if (PlatFormEnum.PLATFORM62.getCode().equals(platform) && "200066".equals(cpCShopEcode)) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单奶卡提货云集特殊处理，platform:{},ocBOrderId{}"), platform, ocBOrderId);
            }
            List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItems(ocBOrderId);
            //获取所有的sku集合
            List<Long> skuList = orderItems.stream().map(OcBOrderItem::getPsCSkuId).distinct().collect(Collectors.toList());
            //查询映射
            PsCCollShopProMapping shopProMapping = psRpcService.queryProMappingByShopIdAndSku(shopId, skuList);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("通用订单转换单奶卡提货逻辑，查询映射:{}"), JSONObject.toJSONString(shopProMapping));
            }
            if (shopProMapping != null) {
                return shopProMapping.getCpCShopChildId();
            }
        }
        return shopId;
    }

    /**
     * description:赋值奶卡原销售店铺
     *
     * @Author: liuwenjin
     * @Date 2022/9/26 19:29
     */
    private void naiKaiParesGwSourceCode(String cardCode, OcBOrder newOcBOrder) {


    }


    @Data
    public static class BusinessTypeResult {
        /**
         * 订单明细id
         */
        private Long itemId;

        private Long id;

        private String name;

        private String code;

        private Long shopId;

        private Integer checkSource;

    }

    @Data
    private static class OrderItemBusinessType {
        /**
         * 订单明细id
         */
        private Long itemId;
        /**
         * 订单主id
         */
        private Long orderId;


        private Long stMainId;
    }


    /**
     * 查询SAP退单业务类型
     *
     * @param businessType
     * @param platform
     * @param orderItemList
     * @param user
     * @return
     */
    public BusinessTypeResult querySapBusinessType(String businessType,
                                                   Integer platform,
                                                   String sourcePlatform,
                                                   List<OcBOrderItem> orderItemList,
                                                   User user) {
        List<Long> ocBOrderItemIds = orderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());

        BusinessTypeResult businessTypeResult = null;
        List<BusinessTypeMatchStRelation> relations = omsBusinessTypeStService.selectBusinessTypeByOrderType(2);
        if (CollectionUtils.isEmpty(relations)) {
            return businessTypeResult;
        }

        for (BusinessTypeMatchStRelation relation : relations) {
            List<BusinessTypeResult> businessTypeResults = handleNullMatch(relation, sourcePlatform, businessType, platform, orderItemList);
            if (CollectionUtils.isEmpty(businessTypeResults)) {
                continue;
            }
            Map<Long, List<BusinessTypeResult>> businessTypeResultMap = businessTypeResults.stream().collect(Collectors.groupingBy(BusinessTypeResult::getId));
            for (Long key : businessTypeResultMap.keySet()) {
                List<BusinessTypeResult> results = businessTypeResultMap.get(key);
                List<Long> itemIds = results.stream().map(BusinessTypeResult::getItemId).collect(Collectors.toList());
                if (itemIds.containsAll(ocBOrderItemIds)) {
                    return results.get(0);
                }
            }
        }
        return businessTypeResult;
    }
}
