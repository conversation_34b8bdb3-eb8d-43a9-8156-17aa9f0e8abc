package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderAddServiceReportMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.AcFStoreKpi;
import com.jackrain.nea.oc.oms.model.table.OcBOrderAddServiceReport;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.StAddedServiceStrategyQueryResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @ClassName OcBOrderAddServiceService
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/10/26 18:26
 * @Version 1.0
 */
@Slf4j
@Component
public class OcBOrderAddServiceService {

    @Autowired
    private OcBOrderAddServiceReportMapper addServiceReportMapper;
    @Autowired
    private StRpcService stRpcService;


    /**
     * 重新计算
     *
     * @param querySession
     * @return
     */
    public ValueHolder recalculate(QuerySession querySession) {
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        List<OcBOrderAddServiceReport> unArchivedReportList = addServiceReportMapper.selectUnArchivedRecords();
        if (CollectionUtils.isEmpty(unArchivedReportList)) {
            return valueHolder;
        }
        for (OcBOrderAddServiceReport ocBOrderAddServiceReport : unArchivedReportList) {
            String labelingRequirements = ocBOrderAddServiceReport.getLabelingRequirements();
            OcBOrderAddServiceReport updateOcBOrderAddServiceReport = new OcBOrderAddServiceReport();
            updateOcBOrderAddServiceReport.setId(ocBOrderAddServiceReport.getId());
            updateOcBOrderAddServiceReport.setOcBOrderId(ocBOrderAddServiceReport.getOcBOrderId());
            updateOcBOrderAddServiceReport.setModifieddate(new Date());
            // 根据增值服务与仓库信息 重新去匹配
            ValueHolderV14<StAddedServiceStrategyQueryResult> result =
                    stRpcService.selectByTypeDocNameAndCpCStoreId(labelingRequirements, ocBOrderAddServiceReport.getCpCPhyWarehouseId());
            log.debug(LogUtil.format("开始根据增值服务与仓库信息获取增值服务策略:{},{},返回结果{}", "selectByTypeDocNameAndCpCStoreId"),
                    labelingRequirements, ocBOrderAddServiceReport.getCpCPhyWarehouseId(), JSONUtil.toJsonStr(result));
            if (result.isOK()) {
                StAddedServiceStrategyQueryResult addedServiceStrategyQueryResult = result.getData();
                if (ObjectUtil.isNull(addedServiceStrategyQueryResult)) {
                    // 未匹配到策略
                    addServiceReportMapper.clearAddServiceInfo(ocBOrderAddServiceReport.getId());
                } else {
                    updateOcBOrderAddServiceReport.setAddserviceStrategyUnitPrice(addedServiceStrategyQueryResult.getUnitPrice());
                    updateOcBOrderAddServiceReport.setAddserviceStrategyPrice(addedServiceStrategyQueryResult.getUnitPrice().multiply(ocBOrderAddServiceReport.getLabelNum()));
                    updateOcBOrderAddServiceReport.setIsMatch(1);
                    updateOcBOrderAddServiceReport.setRemark("");
                    addServiceReportMapper.updateById(updateOcBOrderAddServiceReport);
                }
            } else {
                addServiceReportMapper.clearAddServiceInfo(ocBOrderAddServiceReport.getId());
            }
        }
        return valueHolder;
    }

    /**
     * 归档
     *
     * @param querySession
     * @return
     */
    public ValueHolder archive(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.info("增值服务归档param：{}", param.toJSONString());
        JSONArray jsonArray = param.getJSONArray("ids");
        List<Long> IdList = new ArrayList<>();
        for (Object o : jsonArray) {
            IdList.add(Long.valueOf(o.toString()));
        }
        for (Long id : IdList) {
            OcBOrderAddServiceReport addServiceReport = addServiceReportMapper.selectById(id);
            if (ObjectUtil.isNull(addServiceReport)) {
                continue;
            }
            if (ObjectUtil.equal(addServiceReport.getIsArchived(), 1)) {
                continue;
            }
            OcBOrderAddServiceReport updateAddService = new OcBOrderAddServiceReport();
            updateAddService.setId(id);
            updateAddService.setOcBOrderId(addServiceReport.getOcBOrderId());
            updateAddService.setModifieddate(new Date());
            updateAddService.setIsArchived(1);
            addServiceReportMapper.updateById(updateAddService);
        }
        // 必须勾选制定的记录
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        return valueHolder;
    }

    public ValueHolder saveOrUpdate(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = CommandAdapterUtil.checkSaveSession(querySession, OcCommonConstant.OC_B_ORDER_ADDSERVICE_REPORT);
        if (!valueHolder.isOK()) {
            return valueHolder;
        }

        User user = querySession.getUser();
        JSONObject fixColumn = (JSONObject) valueHolder.getData().get(OcCommonConstant.FIX_COLUMN);
        Long id = (Long) ((HashMap) valueHolder.getData().get("data")).get(OcCommonConstant.OBJ_ID);
        OcBOrderAddServiceReport addServiceReport = ((JSONObject) fixColumn.get(OcCommonConstant.OC_B_ORDER_ADDSERVICE_REPORT)).toJavaObject(OcBOrderAddServiceReport.class);

        if (id < 0) {
            CommandAdapterUtil.defaultOperator(addServiceReport, user);

            id = ModelUtil.getSequence(OcCommonConstant.OC_B_ORDER_ADDSERVICE_REPORT);
            addServiceReport.setId(id);
            addServiceReport.setOwnername(user.getEname());
            addServiceReport.setModifiername(user.getEname());
            addServiceReportMapper.insert(addServiceReport);
        } else {
            // 如果已经归档了 就不让修改了
            OcBOrderAddServiceReport oldOcBOrderAddServiceReport = addServiceReportMapper.selectById(id);
            if (ObjectUtil.isNull(oldOcBOrderAddServiceReport)) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "记录已不存在");
                return valueHolder;
            }
            if (ObjectUtil.equal(oldOcBOrderAddServiceReport.getIsArchived(), 1)) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "已归档的数据 不允许修改");
                return valueHolder;
            }

            if (BigDecimal.ZERO.compareTo(addServiceReport.getLabelNum()) >= 0) {
                valueHolder.put("code", ResultCode.FAIL);
                valueHolder.put("message", "贴标数量不能小于0");
                return valueHolder;
            }
            addServiceReport.setId(id);
            addServiceReport.setModifieddate(new Date());
            addServiceReport.setModifiername(user.getEname());
            addServiceReport.setModifierid(Long.valueOf(user.getId()));
            addServiceReportMapper.updateById(addServiceReport);
            // 重新计算服务总费用
            OcBOrderAddServiceReport newOcBOrderAddServiceReport = addServiceReportMapper.selectById(id);

            // 判断是否有单价 如果没有单价 不用去计算总费用
            if (ObjectUtil.isNotNull(newOcBOrderAddServiceReport.getAddserviceStrategyUnitPrice()) && ObjectUtil.isNotNull(newOcBOrderAddServiceReport.getLabelNum())) {
                OcBOrderAddServiceReport updateOcBOrderAddServiceReport = new OcBOrderAddServiceReport();
                updateOcBOrderAddServiceReport.setAddserviceStrategyPrice(newOcBOrderAddServiceReport.getAddserviceStrategyUnitPrice().multiply(newOcBOrderAddServiceReport.getLabelNum()));
                updateOcBOrderAddServiceReport.setModifieddate(new Date());
                updateOcBOrderAddServiceReport.setId(id);
                addServiceReportMapper.updateById(updateOcBOrderAddServiceReport);
            }

        }
        valueHolder = ValueHolderUtils.getSuccessValueHolder(id, OcCommonConstant.OC_B_ORDER_ADDSERVICE_REPORT, "保存成功");
        return valueHolder;
    }

}
