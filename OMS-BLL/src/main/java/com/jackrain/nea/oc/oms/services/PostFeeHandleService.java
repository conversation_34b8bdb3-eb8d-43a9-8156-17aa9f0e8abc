package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.IpBJdOrderMapper;
import com.jackrain.nea.oc.oms.mapper.IpBJitxOrderMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
import com.jackrain.nea.oc.oms.sap.Oms2SapStatusEnum;
import com.jackrain.nea.oc.oms.sap.SapTaskTableEnum;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: qin jun long
 * @since: 2020/7/12
 */
@Slf4j
@Component
public class PostFeeHandleService {
    @Autowired
    private IpBStandplatOrderMapper ipBStandplatOrderMapper;
    @Autowired
    private IpBTaobaoOrderMapper ipTaobaoOrderMapper;
    @Autowired
    private IpBJdOrderMapper jdOrderMapper;
    @Autowired
    private Oms2SapMapper oms2SapMapper;
    @Autowired
    private IpBJitxOrderMapper jitxOrderMapper;

    /**
     * 获取运费明细
     *
     * @param order
     * @param itemList
     */
    public void getItem(OcBOrder order, List<OcBOrderItem> itemList) {
        // 传SAP中或传SAP成功的平台单号
        List<String> sending = getTids(order);
        if (Objects.equals(order.getIsMerge(), 1)) {
            List<String> tids = itemList.stream().map(OcBOrderItem::getTid).distinct().collect(Collectors.toList());
            tids.removeAll(sending);
            if (tids.isEmpty()) {
                return;
            }
            for (String tid : tids) {
                BigDecimal postFee = this.getPostFeeByPlatform(order, tid);
                if (postFee == null || postFee.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                OcBOrderItem postFeeItemInfo = this.getPostFeeItemInfo(postFee, tid);
                itemList.add(postFeeItemInfo);
            }
            return;
        } else if (Objects.equals(order.getIsSplit(), 1)) {
            if (CollectionUtils.isNotEmpty(sending) && sending.contains(order.getTid())) {
                return;
            }
        }
        addPostFeeItem(order, itemList);
    }

    /**
     * 添加运费条码明细
     *
     * @param order
     * @param itemList
     */
    private void addPostFeeItem(OcBOrder order, List<OcBOrderItem> itemList) {
        BigDecimal postFee = this.getPostFeeByPlatform(order);
        if (postFee == null || postFee.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        itemList.add(this.getPostFeeItemInfo(postFee, order.getTid()));
    }

    /**
     * 获取邮费信息 普通订单根据主表的TID,根据平台单号获取退款中间表邮费
     *
     * @param order
     * @param order
     * @return
     */
    public BigDecimal getPostFee(OcBOrder order, List<OcBOrderItem> items) {
        // 传SAP中或传SAP成功的平台单号
        List<String> sending = getTids(order);
//        if (!is2Ac) {
//            sending = getTids(order);
//        }
        // todo 拆分发货的订单，在传SAP、传对账时，第一张同步的订单才传真实的快递费用，其他固定传0
        if (Objects.equals(order.getIsSplit(), 1)) {
            if (CollectionUtils.isNotEmpty(sending) && sending.contains(order.getTid())) {
                return BigDecimal.ZERO;
            }
            return this.getPostFeeByPlatform(order);
        }
        //  todo 合并订单根据订单商品的TID进行查找，取中间表中未同步过运费的订单的运费和
        else if (Objects.equals(order.getIsMerge(), 1)) {
            List<String> tids = items.stream().map(OcBOrderItem::getTid).distinct().collect(Collectors.toList());
            tids.removeAll(sending);
            return this.getPostFeeByPlatform(order, tids);
        } else {
            return this.getPostFeeByPlatform(order);
        }
    }


    /**
     * 根据订单获取tids
     *
     * @param order
     * @return
     */
    private List<String> getTids(OcBOrder order) {
        // 传AC中或传AC成功的id
        List<String> tids = oms2SapMapper.selectToSapStatus(SapTaskTableEnum.ORDER.txt(),
                Lists.newArrayList(order.getTid()), "to_ac_status", Lists.newArrayList(Oms2SapStatusEnum.SUCCESS.val(),
                        Oms2SapStatusEnum.FINISH.val()));
        return tids;
    }


    /**
     * 获取 Apollo 上配置的邮费sku信息
     *
     * @return
     */
    public OcBOrderItem getPostFeeItemInfo(BigDecimal postFee, String tid) {
        if (postFee == null || postFee.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        // todo change arg
        String postFeeJsonSkuCode = config.getProperty("oms.order.custom.postFee.sku", "SMYF001");
        OcBOrderItem ocBOrderItem = new OcBOrderItem();
        ocBOrderItem.setPsCSkuEcode(postFeeJsonSkuCode);
        // 邮费
        ocBOrderItem.setRealAmt(postFee);
        ocBOrderItem.setTid(tid);
        return ocBOrderItem;
    }


    /**
     * 根据平台获取单据邮费
     *
     * @param order
     * @return
     */
    private BigDecimal getPostFeeByPlatform(OcBOrder order) {
        Integer platform = order.getPlatform();
        if (Objects.isNull(platform)) {
            return BigDecimal.ZERO;
        }
        String tid = order.getTid();
        if (Objects.equals(platform, PlatFormEnum.TAOBAO.getCode())) {
            IpBTaobaoOrder tbOrder = ipTaobaoOrderMapper.selectTaobaoOrderByTid(tid);
            return Objects.isNull(tbOrder) ? BigDecimal.ZERO : tbOrder.getPostFee();
        }
        return BigDecimal.ZERO;
    }

    /**
     * 根据平台获取单据邮费
     *
     * @param order
     * @param tids  平台单号s
     * @return
     */
    private BigDecimal getPostFeeByPlatform(OcBOrder order, List<String> tids) {
        Integer platform = order.getPlatform();
        if (Objects.isNull(platform) || CollectionUtils.isEmpty(tids)) {
            return BigDecimal.ZERO;
        }
        if (platform.equals(PlatFormEnum.JINGDONG.getCode())) {
            List<IpBJdOrder> ipBJdOrder = jdOrderMapper.selectList(new LambdaQueryWrapper<IpBJdOrder>().in(IpBJdOrder::getOrderId, tids));
            return CollectionUtils.isEmpty(ipBJdOrder) ? BigDecimal.ZERO : ipBJdOrder.stream().map(IpBJdOrder::getFreightPrice).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
        } else if (Objects.equals(platform, PlatFormEnum.TAOBAO.getCode())) {
            List<IpBTaobaoOrder> tbOrders = ipTaobaoOrderMapper.selectList(new LambdaQueryWrapper<IpBTaobaoOrder>().in(IpBTaobaoOrder::getTid, tids));
            return CollectionUtils.isEmpty(tbOrders) ? BigDecimal.ZERO : tbOrders.stream().map(IpBTaobaoOrder::getPostFee).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
        } else if (Objects.equals(platform, PlatFormEnum.VIP_JITX.getCode())) {
            // TODO VIP JITX
            // IpBJitxOrder ipBJitxOrder = jitxOrderMapper.selectJitxOrderByOrderSn(order.getTid());
            return BigDecimal.ZERO;
        } else {
            List<IpBStandplatOrder> standplatOrders = ipBStandplatOrderMapper.selectStandplatOrderByTids(tids);
            return CollectionUtils.isEmpty(standplatOrders) ? BigDecimal.ZERO : standplatOrders.stream().map(IpBStandplatOrder::getPostFee).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }


    /**
     * 根据平台获取单据邮费
     *
     * @param order
     * @param tid   平台单号
     * @return
     */
    public BigDecimal getPostFeeByPlatform(OcBOrder order, String tid) {
        Integer platform = order.getPlatform();
        if (Objects.isNull(platform) || Objects.isNull(tid)) {
            return BigDecimal.ZERO;
        }
        if (Objects.equals(platform, PlatFormEnum.TAOBAO.getCode())) {
            List<IpBTaobaoOrder> tbOrders = ipTaobaoOrderMapper.selectList(new LambdaQueryWrapper<IpBTaobaoOrder>().in(IpBTaobaoOrder::getTid, tid));
            return CollectionUtils.isEmpty(tbOrders) ? BigDecimal.ZERO : tbOrders.stream().map(IpBTaobaoOrder::getPostFee).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return BigDecimal.ZERO;
    }
}
