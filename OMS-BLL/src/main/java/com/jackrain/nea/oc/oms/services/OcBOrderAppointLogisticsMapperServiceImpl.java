package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.oc.oms.mapper.OcBOrderAppointLogisticsMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderAppointLogistics;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName OcBOrderAppointLogisticsMapperServiceImpl
 * @Description 指定快递
 * <AUTHOR>
 * @Date 2024/4/16 17:13
 * @Version 1.0
 */
@Slf4j
@Service
public class OcBOrderAppointLogisticsMapperServiceImpl extends ServiceImpl<OcBOrderAppointLogisticsMapper, OcBOrderAppointLogistics> implements OcBOrderAppointLogisticsMapperService {

    @Override
    public List<OcBOrderAppointLogistics> selectByOrderId(Long orderId) {
        return baseMapper.selectList(new QueryWrapper<OcBOrderAppointLogistics>().lambda().eq(OcBOrderAppointLogistics::getOrderId, orderId).eq(OcBOrderAppointLogistics::getIsactive, "Y"));
    }

    @Override
    public List<OcBOrderAppointLogistics> selectByOrderIds(List<Long> orderIds) {
        return baseMapper.selectList(new QueryWrapper<OcBOrderAppointLogistics>().lambda().in(OcBOrderAppointLogistics::getOrderId, orderIds).eq(OcBOrderAppointLogistics::getIsactive, "Y"));
    }

    @Override
    public void deleteByOrderId(Long orderId) {
        List<OcBOrderAppointLogistics> ocBOrderAppointLogistics = selectByOrderId(orderId);
        if (CollectionUtils.isNotEmpty(ocBOrderAppointLogistics)) {
            for (OcBOrderAppointLogistics ocBOrderAppointLogistic : ocBOrderAppointLogistics) {
                OcBOrderAppointLogistics updateOcBOrderAppointLogistics = new OcBOrderAppointLogistics();
                updateOcBOrderAppointLogistics.setOrderId(ocBOrderAppointLogistic.getOrderId());
                updateOcBOrderAppointLogistics.setId(ocBOrderAppointLogistic.getId());
                updateOcBOrderAppointLogistics.setIsactive("N");
                baseMapper.updateById(updateOcBOrderAppointLogistics);
            }
        }
    }

    @Override
    public void deleteByOrderIds(List<Long> orderIds) {
        List<OcBOrderAppointLogistics> ocBOrderAppointLogistics = selectByOrderIds(orderIds);
        if (CollectionUtils.isNotEmpty(ocBOrderAppointLogistics)) {
            for (OcBOrderAppointLogistics ocBOrderAppointLogistic : ocBOrderAppointLogistics) {
                OcBOrderAppointLogistics updateOcBOrderAppointLogistics = new OcBOrderAppointLogistics();
                updateOcBOrderAppointLogistics.setOrderId(ocBOrderAppointLogistic.getOrderId());
                updateOcBOrderAppointLogistics.setId(ocBOrderAppointLogistic.getId());
                updateOcBOrderAppointLogistics.setIsactive("N");
                baseMapper.updateById(updateOcBOrderAppointLogistics);
            }
        }
    }

    @Override
    public List<OcBOrderAppointLogistics> select4CancelAppointLogistics(String now, String tableName) {
        return baseMapper.select4CancelAppointLogistics(now, tableName);
    }

}
