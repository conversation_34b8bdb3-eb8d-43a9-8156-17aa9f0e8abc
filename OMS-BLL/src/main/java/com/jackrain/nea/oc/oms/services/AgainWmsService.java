package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019-08-21 14:17
 * @Version 1.0
 */
@Slf4j
@Component
public class AgainWmsService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsWmsTaskService wmsTaskService;

    public ValueHolderV14 againWms(List<Long> orderIds) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            List<OcBOrder> orderList = ocBOrderMapper.selectOrderListByIds(orderIds);
            if (CollectionUtils.isEmpty(orderList)) {
                holderV14.setCode(-1);
                holderV14.setMessage("订单不存在");
                return holderV14;
            }
            boolean flag = false;
            for (Long orderId : orderIds) {
                OcBOrder ocBOrder = new OcBOrder();
                ocBOrder.setId(orderId);
                ocBOrder.setIsOverfive(0L);
                ocBOrder.setSysremark("");
                flag = omsOrderService.updateOrderInfo(ocBOrder);
            }
            if (!flag) {
                holderV14.setCode(-1);
                holderV14.setMessage("更新失败");
                return holderV14;
            }
            /* 更新传OC_B_TO_WMS_TASK表 状态改为初始状态0*/
            wmsTaskService.batchUpdateOcBToWmsTask(orderIds, 0);
            holderV14.setCode(0);
            holderV14.setMessage("重置成功!");
        } catch (Exception e) {
            log.error(LogUtil.format("重传wms更新失败,异常信息{}"), Throwables.getStackTraceAsString(e));
            holderV14.setCode(-1);
            holderV14.setMessage("程序异常");
        }
        return holderV14;
    }
}
