package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

/**
 * 一件代发客户基价策略查询服务
 *
 * <AUTHOR>
 */
public interface StCDropshipBasePriceQueryService {

    /**
     * 根据店铺ID和SKU编码查询基价
     *
     * @param obj 包含CP_C_SHOP_ID和PS_C_SKU_ECODE的JSON对象
     * @param user 用户信息
     * @return 查询结果
     */
    ValueHolder getBasePrice(JSONObject obj, User user);
}
