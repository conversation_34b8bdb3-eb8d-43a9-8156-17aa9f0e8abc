package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOperationLogMapper;
import com.jackrain.nea.oc.oms.mapper.StCCycleItemStrategyMapper;
import com.jackrain.nea.oc.oms.mapper.StCCycleRuleStrategyMapper;
import com.jackrain.nea.oc.oms.mapper.StCCycleStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.StCCycleItemStrategy;
import com.jackrain.nea.oc.oms.model.table.StCCycleRuleStrategy;
import com.jackrain.nea.oc.oms.model.table.StCCycleStrategy;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.api.result.ProAttributeInfo;
import com.jackrain.nea.ps.api.result.ProSkuResult;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.model.StCCycleStrategyRelation;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.jackrain.nea.oc.oms.model.relation.OcBOrderConst.IS_ACTIVE_YES;

/**
 * @ClassName StCCycleStrategyServiceImpl
 * @Description 周期购促销
 * <AUTHOR>
 * @Date 2024/8/19 11:43
 * @Version 1.0
 */
@Slf4j
@Service
public class StCCycleStrategyServiceImpl implements StCCycleStrategyService {

    @Autowired
    private StCCycleStrategyMapper stCCycleStrategyMapper;
    @Autowired
    private StCCycleRuleStrategyMapper stCCycleRuleStrategyMapper;
    @Autowired
    private StCCycleItemStrategyMapper stCCycleItemStrategyMapper;
    @Autowired
    private PsRpcService psRpcService;
    @Resource
    private OcBOperationLogMapper operationLogMapper;

    @Autowired
    private BuildSequenceUtil buildSequenceUtil;

    @Override
    public ValueHolderV14<Long> save(StCCycleStrategyRelation strategyRelation, User user) throws NDSException {

        Long id = buildSequenceUtil.buildStCCycleStrategySequenceId();
        StCCycleStrategy stCCycleStrategy = strategyRelation.getStCCycleStrategy();
        // 校验主表的开始时间不能大于结束时间 而且是必填
        if (stCCycleStrategy.getStartTime().after(stCCycleStrategy.getEndTime())) {
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, "开始时间不能大于截至时间");
        }
        stCCycleStrategy.setId(id);
        stCCycleStrategy.setStatus(0);
        BaseModelUtil.initialBaseModelSystemField(stCCycleStrategy);
        JSONObject obj = new JSONObject();
        obj.put("ST_C_CYCLE_STRATEGY", stCCycleStrategy);
        String strategyNo = SequenceGenUtil.generateSquence("ST_C_CYCLE_STRATEGY", obj, user.getLocale(), false);
        stCCycleStrategy.setStrategyCode(strategyNo);
        stCCycleStrategy.setIsactive("N");


        // 构建 rule
        List<StCCycleRuleStrategy> stCCycleRuleStrategyList = strategyRelation.getStCCycleRuleStrategy();
        if (CollectionUtils.isNotEmpty(stCCycleRuleStrategyList)) {
            try {
                checkRule(strategyRelation);
            } catch (Exception e) {
                log.error("save cycle strategy error", e);
                return new ValueHolderV14<>(-1L, ResultCode.FAIL, "校验规则失败,失败原因:" + e.getMessage());
            }
            for (StCCycleRuleStrategy ruleStrategy : stCCycleRuleStrategyList) {
                Long ruleId = buildSequenceUtil.buildStCCycleRuleStrategySequenceId();
                ruleStrategy.setId(ruleId);
                ruleStrategy.setStrategyId(id);
                ruleStrategy.setStatus(0);
                BaseModelUtil.initialBaseModelSystemField(ruleStrategy);
            }
        }

        // 构建item
        List<StCCycleItemStrategy> stCCycleItemStrategyList = strategyRelation.getStCCycleItemStrategy();
        if (CollectionUtils.isNotEmpty(stCCycleItemStrategyList)) {
            for (StCCycleItemStrategy itemStrategy : stCCycleItemStrategyList) {
                buildItem(itemStrategy);
                Long itemId = buildSequenceUtil.buildStCCycleItemStrategySequenceId();
                itemStrategy.setId(itemId);
                itemStrategy.setStrategyId(id);
                itemStrategy.setStatus(0);
                BaseModelUtil.initialBaseModelSystemField(itemStrategy);
            }
        }

        try {
            stCCycleStrategyMapper.insert(stCCycleStrategy);

            if (CollectionUtils.isNotEmpty(stCCycleRuleStrategyList)) {
                for (StCCycleRuleStrategy ruleStrategy : stCCycleRuleStrategyList) {
                    // 构建新增
                    OcBOperationLog operationLog = buildRuleOperationLog(ruleStrategy, user);
                    stCCycleRuleStrategyMapper.insert(ruleStrategy);
                    operationLogMapper.insert(operationLog);
                }
            }

            if (CollectionUtils.isNotEmpty(stCCycleItemStrategyList)) {
                for (StCCycleItemStrategy itemStrategy : stCCycleItemStrategyList) {
                    stCCycleItemStrategyMapper.insert(itemStrategy);
                }
            }
        } catch (Exception e) {
            log.error("save cycle strategy error", e);
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, "保存周期购促销失败");
        }
        return new ValueHolderV14<>(id, ResultCode.SUCCESS, "周期购促销策略保存成功");
    }

    @Override
    public ValueHolderV14<Void> update(StCCycleStrategyRelation strategyRelation, JSONObject before, JSONObject after, User user, Long id) throws NDSException {

        StCCycleStrategy oldStCCycleStrategy = stCCycleStrategyMapper.selectById(id);
        // 判断当前状态是否为启用状态。 启用状态不允许添加商品以及规则
        if (IS_ACTIVE_YES.equals(oldStCCycleStrategy.getIsactive())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "当前状态为启用状态，不允许修改");
        }
        StCCycleStrategy stCCycleStrategy = strategyRelation.getStCCycleStrategy();
        if (stCCycleStrategy != null) {
            if (after != null) {
                StCCycleStrategy afterStCCycleStrategy = after.getObject("ST_C_CYCLE_STRATEGY", StCCycleStrategy.class);
                if (afterStCCycleStrategy != null) {
                    StCCycleStrategy updateStCCycleStrategy = new StCCycleStrategy();
                    updateStCCycleStrategy.setId(id);
                    BaseModelUtil.makeBaseModifyField(updateStCCycleStrategy, user);
                    updateStCCycleStrategy.setRemark(afterStCCycleStrategy.getRemark());
                    updateStCCycleStrategy.setType(afterStCCycleStrategy.getType());
                    if (StringUtils.isNotEmpty(afterStCCycleStrategy.getStrategyName())) {
                        updateStCCycleStrategy.setStrategyName(afterStCCycleStrategy.getStrategyName().trim());
                    }
                    stCCycleStrategyMapper.updateById(updateStCCycleStrategy);
                }
            }
        }
        List<StCCycleRuleStrategy> stCCycleRuleStrategyList = strategyRelation.getStCCycleRuleStrategy();
        if (CollectionUtils.isNotEmpty(stCCycleRuleStrategyList)) {
            StCCycleRuleStrategy stCCycleRuleStrategy = stCCycleRuleStrategyList.get(0);
            // 校验是否已经存在了这个规则
            List<StCCycleRuleStrategy> stCCycleRuleStrategyListWithActive = stCCycleRuleStrategyMapper.selectByStrategyIdWithActive(id);
            if (CollectionUtils.isNotEmpty(stCCycleRuleStrategyListWithActive)) {
                // 对ruleType进行分组
                Map<Integer, List<StCCycleRuleStrategy>> ruleTypeMap = stCCycleRuleStrategyListWithActive.stream().collect(Collectors.groupingBy(StCCycleRuleStrategy::getRuleType));
                List<StCCycleRuleStrategy> ruleStrategyList = ruleTypeMap.get(stCCycleRuleStrategy.getRuleType());
                if (CollectionUtils.isNotEmpty(ruleStrategyList)) {
                    for (StCCycleRuleStrategy ruleStrategy : ruleStrategyList) {
                        if (ruleStrategy.getRuleContent().equals(stCCycleRuleStrategy.getRuleContent())) {
                            return new ValueHolderV14<>(ResultCode.FAIL, "新增规则失败,规则已经存在");
                        }
                    }
                }
            }
            // 构建校验的Relation
            StCCycleStrategyRelation ruleStrategyRelation = new StCCycleStrategyRelation();
            ruleStrategyRelation.setStCCycleRuleStrategy(stCCycleRuleStrategyList);
            ruleStrategyRelation.setStCCycleStrategy(stCCycleStrategyMapper.selectById(id));
            try {
                checkRule(ruleStrategyRelation);
            } catch (Exception e) {
                return new ValueHolderV14<>(ResultCode.FAIL, "新增规则失败,失败原因:" + e.getMessage());
            }
            for (StCCycleRuleStrategy ruleStrategy : stCCycleRuleStrategyList) {
                if (ruleStrategy.getId() < 0) {
                    Long ruleId = buildSequenceUtil.buildStCCycleRuleStrategySequenceId();
                    ruleStrategy.setId(ruleId);
                    ruleStrategy.setStrategyId(id);
                    ruleStrategy.setStatus(0);
                    ruleStrategy.setRuleContent(ruleStrategy.getRuleContent().trim());
                    BaseModelUtil.initialBaseModelSystemField(ruleStrategy);
                    stCCycleRuleStrategyMapper.insert(ruleStrategy);
                    // 构建新增
                    OcBOperationLog operationLog = buildRuleOperationLog(ruleStrategy, user);
                    operationLogMapper.insert(operationLog);
                } else {
                    // 修改
                    StCCycleRuleStrategy updateRuleStrategy = new StCCycleRuleStrategy();
                    updateRuleStrategy.setId(ruleStrategy.getId());
                    updateRuleStrategy.setRuleType(ruleStrategy.getRuleType());
                    updateRuleStrategy.setRuleContent(ruleStrategy.getRuleContent().trim());
                    BaseModelUtil.makeBaseModifyField(updateRuleStrategy, user);
                    stCCycleRuleStrategyMapper.updateById(updateRuleStrategy);
                }
            }
        }

        List<StCCycleItemStrategy> stCCycleItemStrategyList = strategyRelation.getStCCycleItemStrategy();
        if (CollectionUtils.isNotEmpty(stCCycleItemStrategyList)) {
            for (StCCycleItemStrategy itemStrategy : stCCycleItemStrategyList) {
                try {
                    buildItem(itemStrategy);
                    checkItem(itemStrategy, id);
                } catch (Exception e) {
                    return new ValueHolderV14<>(ResultCode.FAIL, "新增商品失败,失败原因:" + e.getMessage());
                }
                if (itemStrategy.getId() < 0) {
                    Long itemId = buildSequenceUtil.buildStCCycleItemStrategySequenceId();
                    itemStrategy.setId(itemId);
                    itemStrategy.setStrategyId(id);
                    itemStrategy.setStatus(0);
                    BaseModelUtil.initialBaseModelSystemField(itemStrategy);
                    stCCycleItemStrategyMapper.insert(itemStrategy);
                    OcBOperationLog operationLog = buildItemOperationLog(itemStrategy, user);
                    operationLogMapper.insert(operationLog);
                } else {
                    StCCycleItemStrategy updateItemStrategy = new StCCycleItemStrategy();
                    updateItemStrategy.setId(itemStrategy.getId());
                    updateItemStrategy.setQty(itemStrategy.getQty());
                    updateItemStrategy.setSkuCode(itemStrategy.getSkuCode());
                    updateItemStrategy.setSkuId(itemStrategy.getSkuId());
                    updateItemStrategy.setSkuName(itemStrategy.getSkuName());
                    BaseModelUtil.makeBaseModifyField(updateItemStrategy, user);
                    stCCycleItemStrategyMapper.updateById(updateItemStrategy);
                }
            }
        }
        // 修改主表的修改时间
        StCCycleStrategy strategy = new StCCycleStrategy();
        strategy.setId(id);
        BaseModelUtil.makeBaseModifyField(strategy, user);
        stCCycleStrategyMapper.updateById(strategy);
        return new ValueHolderV14<>(ResultCode.SUCCESS, "周期购促销策略保存成功");
    }

    private OcBOperationLog buildRuleOperationLog(StCCycleRuleStrategy ruleStrategy, User user) {
        StringBuilder afterValue = new StringBuilder();
        afterValue.setLength(0);
        afterValue.append("[").append(ruleStrategy.getRuleType() == 1 ? "平台商品ID" : "SKU").append("],[").append(ruleStrategy.getRuleContent())
                .append("]");
        OcBOperationLog operationLog = getOperationLog("ST_C_CYCLE_RULE_STRATEGY", "ADD", ruleStrategy.getStrategyId(),
                "周期购规则", "新增周期购规则", "新增", afterValue.toString(), user);
        return operationLog;
    }

    private OcBOperationLog buildItemOperationLog(StCCycleItemStrategy itemStrategy, User user) {
        StringBuilder afterValue = new StringBuilder();
        afterValue.setLength(0);
        afterValue.append("[").append(itemStrategy.getSkuCode()).append("],[").append("期数:" + itemStrategy.getCycleNum())
                .append("],[").append("数量:" + itemStrategy.getQty()).append("],[").append("拆单方式:" + (itemStrategy.getSplitType() == 1 ? "不拆单" : "拆单")).append("]");
        OcBOperationLog operationLog = getOperationLog("ST_C_CYCLE_ITEM_STRATEGY", "ADD", itemStrategy.getStrategyId(),
                "周期购赠品明细", "新增周期购赠品明细", "新增", afterValue.toString(), user);
        return operationLog;
    }


    /**
     * 获取操作日志对象
     *
     * @param tableName
     * @param operationType
     * @param updateId
     * @param tableDescription
     * @param columnName
     * @param columnBeforeValue
     * @param columnAfterValue
     * @param user
     * @return
     */
    private OcBOperationLog getOperationLog(String tableName, String operationType, Long updateId,
                                            String tableDescription, String columnName, String columnBeforeValue,
                                            String columnAfterValue, User user) {
        OcBOperationLog operationLog = new OcBOperationLog();
        operationLog.setId(ModelUtil.getSequence(StConstant.TAB_OC_B_OPERATION_LOG));
        operationLog.setTableName(tableName);
        operationLog.setOperationType(OperationTypeEnum.getNameByValue(operationType));
        operationLog.setUpdateId(updateId);
        operationLog.setUpdateModelName(tableDescription);
        operationLog.setModContent(columnName);
        operationLog.setBeforeData(columnBeforeValue);
        operationLog.setAfterData(columnAfterValue);
        StBeanUtils.makeCreateField(operationLog, user);
        return operationLog;
    }

    private void buildItem(StCCycleItemStrategy itemStrategy) {
        Long skuId = itemStrategy.getSkuId();
        List<PsCProSkuResult> proSkuList;
        if (skuId != null) {
            try {
                ValueHolder valueHolder = psRpcService.selectProdSkuInfoBySkuIds(Collections.singletonList(skuId));
                if (!valueHolder.isOK()) {
                    throw new NDSException("查询商品信息失败");
                }
                ProSkuResult proSkuResult = (ProSkuResult) valueHolder.get("data");
                proSkuList = proSkuResult.getProSkuList();
            } catch (Exception e) {
                throw new NDSException("查询商品信息失败");
            }

            if (CollectionUtils.isNotEmpty(proSkuList)) {
                PsCProSkuResult pro = proSkuList.get(0);
                // 不能是组合品
                if (IS_ACTIVE_YES.equals(pro.getIsGroup())) {
                    throw new NDSException("商品不能是组合品");
                }
                // 不能是奶卡
                itemStrategy.setSkuCode(pro.getSkuEcode());
                itemStrategy.setSkuName(pro.getPsCProEname());
                ProAttributeInfo proAttributeInfo = pro.getProAttributeMap().get("M_DIM2_ID");
                if (proAttributeInfo != null) {
                    // 判断物料组编码是否包含10800 10801 10802
                    if (proAttributeInfo.getEcode().equals("10800") || proAttributeInfo.getEcode().equals("10801") || proAttributeInfo.getEcode().equals("10802")) {
                        throw new NDSException("商品不能是奶卡");
                    }
                }
            } else {
                throw new NDSException("商品不存在");
            }
        }
    }

    private void checkItem(StCCycleItemStrategy stCCycleItemStrategy, Long mainId) {
        if (stCCycleItemStrategy == null) {
            return;
        }

        // 根据策略id 获取所有的赠送商品
        List<StCCycleItemStrategy> itemStrategyList = stCCycleItemStrategyMapper.selectByStrategyIdWithActive(mainId);
        if (CollectionUtils.isEmpty(itemStrategyList)) {
            return;
        }
        // 根据期数分组
        Map<Integer, List<StCCycleItemStrategy>> itemStrategyMap = itemStrategyList.stream().collect(Collectors.groupingBy(StCCycleItemStrategy::getCycleNum));
        List<StCCycleItemStrategy> stCCycleItemStrategyList = itemStrategyMap.get(stCCycleItemStrategy.getCycleNum());
        if (CollectionUtils.isEmpty(stCCycleItemStrategyList)) {
            return;
        }
        // 根据stCCycleItemStrategyList 对skuCode进行分组
        Map<Long, List<StCCycleItemStrategy>> itemStrategyMap1 = stCCycleItemStrategyList.stream().collect(Collectors.groupingBy(StCCycleItemStrategy::getSkuId));
        List<StCCycleItemStrategy> skuList = itemStrategyMap1.get(stCCycleItemStrategy.getSkuId());
        if (CollectionUtils.isNotEmpty(skuList)) {
            throw new NDSException("同一期添加的商品不能重复");
        }
    }

    public void checkRule(StCCycleStrategyRelation strategyRelation) {
        StCCycleStrategy stCCycleStrategy = strategyRelation.getStCCycleStrategy();
        List<StCCycleRuleStrategy> stCCycleRuleStrategys = strategyRelation.getStCCycleRuleStrategy();
        if (CollectionUtils.isEmpty(stCCycleRuleStrategys)) {
            return;
        }
        StCCycleRuleStrategy stCCycleRuleStrategy = stCCycleRuleStrategys.get(0);
        // 如果选择的是SKU。则需要校验商品必须存在 而且不能是组合商品
        if (stCCycleRuleStrategy.getRuleType() == 2) {
            // 判断是不是天猫周期购
            if (stCCycleStrategy.getType() == 1) {
                throw new NDSException("天猫周期购不支持SKU条件");
            }
            ProductSku productSku = psRpcService.selectProductSku(stCCycleRuleStrategy.getRuleContent());
            if (productSku == null) {
                throw new NDSException("选择的商品不存在");
            }
            // 判断商品是否是组合商品
            if (IS_ACTIVE_YES.equals(productSku.getIsGroup())) {
                throw new NDSException("选择的商品不能是组合商品");
            }
        }

        // 获取店铺ID
        Long shopId = stCCycleStrategy.getShopId();
        // 获取开始时间
        Date startTime = stCCycleStrategy.getStartTime();
        // 获取结束时间
        Date endTime = stCCycleStrategy.getEndTime();
        // 如果开始时间小于结束时间 报错
        if (startTime.after(endTime)) {
            throw new NDSException("开始时间不能大于截至时间！");
        }
        // 根据店铺ID 查询出所有的规则
        List<StCCycleStrategy> stCCycleStrategyList = stCCycleStrategyMapper.selectByShopIdAndType(shopId, stCCycleStrategy.getType());
        if (CollectionUtils.isEmpty(stCCycleStrategyList)) {
            return;
        }
        List<Long> strategyIdList = new ArrayList<>();
        for (StCCycleStrategy strategy : stCCycleStrategyList) {

            Date oldStartTime = strategy.getStartTime();
            Date oldEndTime = strategy.getEndTime();
            // 判断strategy的startTime与endTime与新增的startTime与endTime 是否有交叉
            if (startTime.compareTo(oldEndTime) <= 0 && endTime.compareTo(oldStartTime) >= 0) {
                strategyIdList.add(strategy.getId());
            }
        }
        if (CollectionUtils.isEmpty(strategyIdList)) {
            return;
        }
        List<StCCycleRuleStrategy> stCCycleRuleStrategyList = stCCycleRuleStrategyMapper.selectByStrategyIdList(strategyIdList);
        if (CollectionUtils.isEmpty(stCCycleRuleStrategyList)) {
            return;
        }
        // 对rule_type 进行分组
        Map<Integer, List<StCCycleRuleStrategy>> ruleTypeMap = stCCycleRuleStrategyList.stream().collect(Collectors.groupingBy(StCCycleRuleStrategy::getRuleType));
        Integer ruleType = stCCycleRuleStrategy.getRuleType();
        List<StCCycleRuleStrategy> ruleTypeList = ruleTypeMap.get(ruleType);
        // 如果没有与保存的规则一致的数据 则返回
        if (CollectionUtils.isEmpty(ruleTypeList)) {
            return;
        }
        for (StCCycleRuleStrategy ruleStrategy : ruleTypeList) {
            if (ruleStrategy.getRuleContent().equals(stCCycleRuleStrategy.getRuleContent())) {
                throw new NDSException("已有相同的数据，请检查！");
            }
        }
    }
}
