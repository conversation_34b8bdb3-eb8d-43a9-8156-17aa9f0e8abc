package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.store.api.out.SgBStoOutNoticesCmd;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillSelectResult;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.request.OcBReturnOrderWarningRequest;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/12/15 10:28
 */
@Slf4j
@Component
public class OcBReturnOrderWarningService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBReturnOrderWarningMapper returnOrderWarningMapper;

    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;

    @Autowired
    private OcBReturnAfSendItemMapper ocBReturnAfSendItemMapper;

    @Autowired
    private OcBReturnBfSendMapper ocBReturnBfSendMapper;

    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;
    @Autowired
    private AutoInterceptService autoInterceptService;

    @Reference(group = "sg", version = "1.0")
    private SgBStoOutNoticesCmd sgBStoOutNoticesCmd;


    public ValueHolderV14 warning(List<OcBReturnOrderWarningRequest> request) {
        if (log.isDebugEnabled()) {
            log.debug(" OcBReturnOrderWarningService reqeust {}", JSONObject.toJSONString(request));
        }
        User user = SystemUserResource.getRootUser();
        OcBReturnOrderWarningService service = ApplicationContextHandle.getBean(OcBReturnOrderWarningService.class);

        Map<Integer, List<OcBReturnOrderWarningRequest>> warningMap = request.stream().collect(Collectors.groupingBy(OcBReturnOrderWarningRequest::getWarningType));

        for (Map.Entry<Integer, List<OcBReturnOrderWarningRequest>> entry : warningMap.entrySet()) {
            Integer warningType = entry.getKey();
            List<OcBReturnOrderWarningRequest> value = entry.getValue();
            for (OcBReturnOrderWarningRequest warningRequest : value) {
                List<Long> orderIds = warningRequest.getOrderIds();
                if (CollectionUtils.isNotEmpty(orderIds)) {
                    //对应异常类型 1退款又发货 2退款未入库 3退款后换货发货
                    List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(orderIds);
                    if (CollectionUtils.isNotEmpty(ocBOrders)) {
                        List<OcBReturnOrderWarning> orderWarningOrderIds = returnOrderWarningMapper.selectList(new LambdaQueryWrapper<OcBReturnOrderWarning>()
                                .select(OcBReturnOrderWarning::getOrderId)
                                .in(OcBReturnOrderWarning::getOrderId, orderIds));
                        //已存在的平台单号
                        List<Long> orderIdList = orderWarningOrderIds.stream().map(OcBReturnOrderWarning::getOrderId).collect(Collectors.toList());

                        List<OcBOrder> newOcBOrders = new ArrayList<>();
                        for (OcBOrder ocBOrder : ocBOrders) {
                            Long orderId = ocBOrder.getId();
                            if (!orderIdList.contains(orderId)) {
                                newOcBOrders.add(ocBOrder);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(newOcBOrders)) {
                            service.assembleData(newOcBOrders, warningType, user);
                        }
                    }
                }
            }
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "success");
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void assembleData(List<OcBOrder> ocBOrders, Integer warningType, User user) {
        if (log.isDebugEnabled()) {
            log.debug(" OcBReturnOrderWarningService ocBOrders {}", JSONObject.toJSONString(ocBOrders));
        }
        List<OcBReturnOrderWarning> inserts = new ArrayList<>();

        List<Long> ordeIds = new ArrayList<>();
        List<String> ordeTids = new ArrayList<>();
        List<Long> sgOutIds = new ArrayList<>();

        for (OcBOrder ocBOrder : ocBOrders) {
            Long id = ocBOrder.getId();
            String tid = ocBOrder.getTid();
            Long outBillId = ocBOrder.getSgBOutBillId();
            ordeIds.add(id);
            ordeTids.add(tid);
            if (outBillId != null) {
                sgOutIds.add(outBillId);
            }
        }
        //所有sku 逗号分隔
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectList(new LambdaQueryWrapper<OcBOrderItem>().in(OcBOrderItem::getOcBOrderId, ordeIds));
        Map<Long, String> skusMap = ocBOrderItems.stream().collect(
                Collectors.toMap(OcBOrderItem::getOcBOrderId, OcBOrderItem::getPsCSkuEcode, (o1, o2) -> o1 + "," + o2));

        // 1退款又发货  3退款后换货发货 退款金额 退款完成时间 出库通知单总数量
        Map<String, OcBReturnBfSend> bfSendMap = new HashMap<>();
        Map<Long, BigDecimal> stoOutAllQtyMap = new HashMap<>();
        //  2退款未入库 实际退款金额 退款完成时间
        Map<String, OcBReturnAfSend> afSendMap = new HashMap<>();
        Map<String, OcBReturnOrder> returnOrderMap = new HashMap<>();
        Map<Long, BigDecimal> afSendItemAllQtyIn = new HashMap<>();

        if (warningType == 1 || warningType == 3 || warningType == 4) {
            List<OcBReturnBfSend> ocBReturnBfSends = ocBReturnBfSendMapper.selectList(new LambdaQueryWrapper<OcBReturnBfSend>()
                    .select(OcBReturnBfSend::getTid, OcBReturnBfSend::getAmtReturn,
                            OcBReturnBfSend::getModifieddate, OcBReturnBfSend::getTReturnId)
                    .in(OcBReturnBfSend::getTid, ordeTids)
                    .eq(OcBReturnBfSend::getIsactive, OcBOrderConst.IS_ACTIVE_YES));
            bfSendMap = ocBReturnBfSends.stream().collect(Collectors.toMap(OcBReturnBfSend::getTid, Function.identity(), (o1, o2) -> o1));

            if (CollectionUtils.isNotEmpty(sgOutIds)) {
                ValueHolderV14<List<SgBStoOutNoticesBillSelectResult>> v14 = sgBStoOutNoticesCmd.selSgStoOutNotices(sgOutIds);
                List<SgBStoOutNoticesBillSelectResult> data = v14.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    stoOutAllQtyMap = data.stream().collect(Collectors.toMap(SgBStoOutNoticesBillSelectResult::getId,
                            SgBStoOutNoticesBillSelectResult::getQtyOut, (o1, o2) -> {
                                o1 = o1.add(o2);
                                return o1;
                            }));
                }
            }
        }

        if (warningType == 2) {
            List<Long> ocBReturnAfSendIds = new ArrayList<>();
            List<OcBReturnAfSend> ocBReturnAfSends = ocBReturnAfSendMapper.selectList(new LambdaQueryWrapper<OcBReturnAfSend>()
                    .select(OcBReturnAfSend::getId, OcBReturnAfSend::getTid, OcBReturnAfSend::getAmtReturnActual,
                            OcBReturnAfSend::getModifieddate, OcBReturnAfSend::getTReturnId)
                    .in(OcBReturnAfSend::getTid, ordeTids)
                    .eq(OcBReturnAfSend::getIsactive, OcBOrderConst.IS_ACTIVE_YES));
            for (OcBReturnAfSend ocBReturnAfSend : ocBReturnAfSends) {
                afSendMap.put(ocBReturnAfSend.getTid(), ocBReturnAfSend);
                ocBReturnAfSendIds.add(ocBReturnAfSend.getId());
            }
            List<OcBReturnAfSendItem> afSendItems = ocBReturnAfSendItemMapper.selectList(new LambdaQueryWrapper<OcBReturnAfSendItem>()
                    .in(OcBReturnAfSendItem::getOcBReturnAfSendId, ocBReturnAfSendIds)
                    .eq(OcBReturnAfSendItem::getIsactive, OcBOrderConst.IS_ACTIVE_YES));
            afSendItemAllQtyIn = afSendItems.stream().collect(Collectors.toMap(OcBReturnAfSendItem::getOcBReturnAfSendId, OcBReturnAfSendItem::getQtyReturnApply,
                    (o1, o2) -> {
                        o1 = o1.add(o2);
                        return o1;
                    }));

            List<OcBReturnOrder> ocBReturnOrders = returnOrderMapper.selectList(new LambdaQueryWrapper<OcBReturnOrder>()
                    .select(OcBReturnOrder::getTid, OcBReturnOrder::getBillNo, OcBReturnOrder::getCpCPhyWarehouseInId, OcBReturnOrder::getCpCLogisticsId,
                            OcBReturnOrder::getLogisticsCode)
                    .in(OcBReturnOrder::getTid, ordeTids)
                    .eq(OcBReturnOrder::getIsactive, OcBOrderConst.IS_ACTIVE_YES));
            returnOrderMap = ocBReturnOrders.stream().collect(Collectors.toMap(OcBReturnOrder::getTid, Function.identity(), (o1, o2) -> o1));
        }

        if (log.isDebugEnabled()) {
            log.debug(" OcBReturnOrderWarningService bfSendMap {} stoOutAllQtyMap{}  afSendMap {} returnOrderMap {} afSendItemAllQtyIn {}",
                    JSONObject.toJSONString(bfSendMap), JSONObject.toJSONString(stoOutAllQtyMap),
                    JSONObject.toJSONString(afSendMap), JSONObject.toJSONString(returnOrderMap),
                    JSONObject.toJSONString(afSendItemAllQtyIn));
        }

        for (OcBOrder ocBOrder : ocBOrders) {
            Long id = ocBOrder.getId();
            String tid = ocBOrder.getTid();
            Long sgBOutBillId = ocBOrder.getSgBOutBillId();
            Long logId = ModelUtil.getSequence("oc_b_return_order_warning");

            OcBReturnOrderWarning warning = new OcBReturnOrderWarning();
            warning.setId(logId);
            warning.setOrderId(id);
            warning.setTid(ocBOrder.getTid());
            warning.setUserNick(ocBOrder.getUserNick());
            if (!skusMap.containsKey(id)) {
                throw new NDSException("tid:" + tid + " 未获取到商品SKU集");
            }
            warning.setPsCProSku(skusMap.get(id));
            warning.setSgBOutBillId(ocBOrder.getSgBOutBillId());
            warning.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
            warning.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
            warning.setExpresscode(ocBOrder.getExpresscode());
            if (warningType == 1 || warningType == 3  || warningType == 4) {
                if (bfSendMap.containsKey(tid)) {
                    OcBReturnBfSend ocBReturnBfSend = bfSendMap.get(tid);
                    warning.setAmtReturn(ocBReturnBfSend.getAmtReturn());
                    warning.setReturndate(ocBReturnBfSend.getModifieddate());
                    warning.setTReturnId(ocBReturnBfSend.getTReturnId());
                }
                warning.setTotRefundQty(ocBOrder.getQtyAll());
                if (stoOutAllQtyMap.containsKey(sgBOutBillId)) {
                    warning.setTotSnedQty(stoOutAllQtyMap.get(sgBOutBillId));
                }
            }

            if (warningType == 2) {
                if (afSendMap.containsKey(tid)) {
                    OcBReturnAfSend ocBReturnAfSend = afSendMap.get(tid);
                    warning.setAmtReturn(ocBReturnAfSend.getAmtReturnActual());
                    warning.setReturndate(ocBReturnAfSend.getModifieddate());
                    warning.setTReturnId(ocBReturnAfSend.getTReturnId());
                    Long afsendId = ocBReturnAfSend.getId();
                    BigDecimal qtyReturnApply = afSendItemAllQtyIn.get(afsendId);
                    warning.setTotRefundQty(qtyReturnApply);
                }
                if (returnOrderMap.containsKey(tid)) {
                    OcBReturnOrder ocBReturnOrder = returnOrderMap.get(tid);
                    warning.setOrderReturnBillNo(ocBReturnOrder.getBillNo());
                    warning.setCpCPhyWarehouseInId(ocBReturnOrder.getCpCPhyWarehouseInId());
                    warning.setCpCLogisticsReturnId(ocBReturnOrder.getCpCLogisticsId());
                    warning.setLogisticsCode(ocBReturnOrder.getLogisticsCode());
                }
            }
            //是否处理
            warning.setManageResult(OcBOrderConst.IS_ACTIVE_NO);
            //平台店铺
            warning.setCpCShopId(ocBOrder.getCpCShopId());
            warning.setCpCShopEcode(ocBOrder.getCpCShopEcode());
            warning.setCpCShopTitle(ocBOrder.getCpCShopTitle());
            //异常类型
            warning.setWarningType(warningType);

            //20220112 异常预警添加字段
            warning.setOrderAmt(ocBOrder.getOrderAmt());
            warning.setPayTime(ocBOrder.getPayTime());
            warning.setWarehouseDeliveryTime(ocBOrder.getWarehouseDeliveryTime());
            warning.setOrderBillNo(ocBOrder.getBillNo());
            warning.setAutoIntercept(YesNoEnum.ZERO.getKey());

            BaseModelUtil.initialBaseModelSystemField(warning, user);
            inserts.add(warning);
        }
        List<List<OcBReturnOrderWarning>> lists = Lists.partition(inserts, 300);
        for (List<OcBReturnOrderWarning> list : lists) {
            returnOrderWarningMapper.batchInsert(list);
            // 判断type type=1 代表退款又发货
            autoInterceptService.autoIntercept(inserts);
        }
    }
}
