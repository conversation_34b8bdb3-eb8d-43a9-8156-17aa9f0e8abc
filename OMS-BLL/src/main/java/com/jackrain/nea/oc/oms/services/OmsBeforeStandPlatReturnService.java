package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/2/28 3:32 下午
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsBeforeStandPlatReturnService {

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private IpStandplatRefundService ipStandplatRefundService;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private MarkRefundService markRefundService;
    @Autowired
    protected OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderRecountAmountService omsOrderRecountAmountService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OmsReturnUtil omsReturnUtil;


    /**
     * 买家申请退款,等待卖家同意
     *
     * @param omsOrderRelation 订单对象
     * @param operateUser      操作人
     * @return
     */
    public boolean taobaoRefundStatusAgree(OmsOrderRelation omsOrderRelation,
                                           IpBStandplatRefund ipBStandplatRefund, User operateUser) {
        try {
            List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
            //判断明细是否全部退款成功(一个事务要么全部成功)  当前的明细
            boolean flag = true;
            for (int i = 0; i < ocBOrderItems.size(); i++) {
                OcBOrderItem item = ocBOrderItems.get(i);
                if (OcOrderRefundStatusEnum.SUCCESS.getVal() != item.getRefundStatus()) {
                    flag = false;
                }
            }
            if (flag) {
                return true;
            }
            OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
            List<OcBOrderItem> ocBOrderItemAll = omsOrderRelation.getOcBOrderItemAll();
            // 未拆分的组合商品信息
            List<Long> updateItemIds = ocBOrderItemAll.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            // 将订单明细的退款状态更新为 1
            ocBOrderItemMapper.updateOcBOrderItemById(ocBOrder.getId(), updateItemIds, OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal());
            omsReturnUtil.orderHandle(operateUser, ocBOrder);

        } catch (Exception e) {
            log.error(LogUtil.format("买家申请退款,等待卖家同意处理异常,error:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
        return false;
    }


    /**
     * 退款成功
     *
     * @param omsOrderRelation 订单对象
     * @param operateUser      用户
     * @return
     */
    public void refundStatusIsSuccess(OmsOrderRelation omsOrderRelation, User operateUser) {
        try {
            List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
            OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
            // 判断是否已退款完成  防止原单一时没有拉到原单，平台手动同意退款，明细有退款成功的，但是由于退单中间表还是未转化后续会继续转换；
            // 或者由于界面人工将已转换的标记为未转化。
            boolean flag = true;
            for (int i = 0; i < ocBOrderItems.size(); i++) {
                OcBOrderItem item = ocBOrderItems.get(i);
                if (OcOrderRefundStatusEnum.SUCCESS.getVal() == item.getRefundStatus()) {
                    ocBOrderItems.remove(item);
                    i--;
                } else {
                    flag = false;
                }
            }
            if (flag) {
                omsReturnUtil.handleRefundComplete(ocBOrder);
                return;
            }
            //校验订单状态,若需要反审核则需要对订单处理反审核
            omsReturnUtil.orderHandle(operateUser, ocBOrder);
            Integer orderStatus = ocBOrder.getOrderStatus();
            //未拆分的组合商品信息
            //若订单状态是：未确认、缺货、已审核状态时，直接调用【订单传AG取消发货服务】
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                List<OcBOrderItem> orderItems = ocBOrderItems.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE || p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
                List<Long> itemIds = orderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                if (omsReturnUtil.signRefundNew(ocBOrder.getId(), itemIds, operateUser, OrderHoldReasonEnum.REFUND_HOLD)) {
                    omsReturnUtil.handleRefundComplete(ocBOrder);
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("退款成功处理异常,error:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }

    private void orderHandle(IpBStandplatRefund ipBStandplatRefund, OcBOrder ocBOrder) {
        //判断订单是否全部退款完成
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        if (CollectionUtils.isEmpty(orderItems)) {
            this.cancelOrder(ocBOrder.getId());
            String remark = SysNotesConstant.SYS_REMARK19;
            TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBStandplatRefund);
            return;
        }
        //判断订单时候还有申请退款的明细
        orderItems = orderItems.stream().filter(p -> (
                        p.getRefundStatus().equals(OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal())
                )
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderItems)) {
            // this.cancelOrder(ocBOrder.getId());
            String remark = SysNotesConstant.SYS_REMARK19;
            TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBStandplatRefund);
        } else {
            OcBOrder order = new OcBOrder();
            order.setIsInreturning(0);
            order.setId(ocBOrder.getId());
            omsOrderService.updateOrderInfo(order);
            //是否已经拦截 hold单或释放hold单调用 HOLD单方法
            order.setIsInterecept(0);//修改hold单状态 使用HOLD单方法修改
            ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
            String remark = SysNotesConstant.SYS_REMARK20;
            TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBStandplatRefund);
        }
    }

    /**
     * 发货前全部退款完成之后,取消原单
     *
     * @param id
     */
    void cancelOrder(Long id) {
        OcBOrder order = new OcBOrder();
        order.setId(id);
        // 全部退款完成之后取消原单
        order.setOrderStatus(OmsOrderStatus.CANCELLED.toInteger());
        order.setModifieddate(new Date());
        omsOrderService.updateOrderInfo(order);
    }

    /**
     * @param omsOrderRelation 订单对象
     * @param operateUser
     * @return
     */
    public void orderStatusIsClosed(OmsOrderRelation omsOrderRelation,
                                    IpBStandplatRefund ipBStandplatRefund, User operateUser) {
        OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
        List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
        //将订单明细的对象的退款状态改为未退款
        for (OcBOrderItem item : ocBOrderItems) {
            OcBOrderItem orderItem = new OcBOrderItem();
            orderItem.setId(item.getId());
            orderItem.setOcBOrderId(item.getOcBOrderId());
            orderItem.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
            omsOrderItemService.updateOcBOrderItem(orderItem, ocBOrder.getId());
        }
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListNotGift(ocBOrder.getId());
        orderItems = orderItems.stream().filter(p -> (
                        p.getRefundStatus().equals(OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal())
                )
        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderItems)) {
            String remark = SysNotesConstant.SYS_REMARK18;
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBStandplatRefund);
            return;
        } else {
            OcBOrder order = new OcBOrder();
            order.setIsInreturning(0);
            order.setId(ocBOrder.getId());
            omsOrderService.updateOrderInfo(order);
            //是否已经拦截 hold单或释放hold单调用 HOLD单方法
            order.setIsInterecept(0);//修改hold单状态 使用HOLD单方法修改
            ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    "退款关闭,转换完成!", ipBStandplatRefund);
        }
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "订单退款关闭，取消拦截", null, null, operateUser);
    }

    /**
     * 订单状态处理
     *
     * @param operateUser
     * @param ocBOrder
     */
    private boolean orderHandle(IpBStandplatRefund ipBStandplatRefund, User operateUser, OcBOrder ocBOrder) {
        Integer orderStatus = ocBOrder.getOrderStatus();
        if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus)) {
            String remark1 = "订单状态为待分配或者待传wms,等待下次转换";
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                    remark1, ipBStandplatRefund);
            return false;
        } else if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)
                || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
            //订单状态为已审核或者配货中 调用反审核
            if (this.toExamineOrder(ocBOrder, operateUser)) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核成功", null, null, operateUser);
            } else {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核失败", null, null, operateUser);
                String remark1 = SysNotesConstant.SYS_REMARK46;
                TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DE_AUDIT);
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        remark1, ipBStandplatRefund);
                return false;
            }
        }
        return true;
    }


    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    private boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            if (isSuccess) {
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用反审核失败", e);
            return false;
        }

    }

    /**
     * 调用标记退款完成
     *
     * @param itemIds
     * @param user
     * @return
     */
    private boolean signRefund(List<Long> itemIds, User user) {
        List<String> itemIdstr = itemIds.stream().map(p -> p + "").collect(Collectors.toList());
        String join = String.join(",", itemIdstr);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("IDS", join);
        ValueHolderV14 holderV14 = markRefundService.markRefund(jsonObject, user);
        int code = Tools.getInt(holderV14.getCode(), -1);
        return code == 0;
    }
}


