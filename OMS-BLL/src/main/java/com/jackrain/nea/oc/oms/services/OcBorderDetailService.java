package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.result.SgGroupStorageQueryResult;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemExtentionMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemFiMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.DmsGiftAttrEnum;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.AccountToNaiKaEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefundExt;
import com.jackrain.nea.oc.oms.model.table.OcBorderItemExtention;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.util.BigDecimalUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.ps.services.PsGetCommodityInformationService;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.request.SkuQueryRequest;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.QueryOrderItemAvailbleStockService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: 孙俊磊
 * @since: 2019-03-11
 * create at:  2019-03-11 19:12
 */
@Slf4j
@Component
public class OcBorderDetailService {
    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBOrderItemFiMapper ocBorderItemMapper;

    @Autowired
    private OcBorderUpdateServiceExt service;

    @Autowired
    private PsGetCommodityInformationService psGetCommodityInformationService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    private QueryOrderItemAvailbleStockService queryOrderItemAvailbleStockService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBOrderItemExtentionMapper extMapper;
    @Autowired
    private OcBOrderItemExtService ocBOrderItemExtService;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;

    public ValueHolderV14 getOrderDetailList(String param, User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        String idString = "id";
        String currentPageString = "currentPage";
        String pageSizeString = "pageSize";
        String eCodeString = "ps_c_pro_ecode";
        String eNameString = "ps_c_pro_ename";
        String skuString = "ps_c_sku_ecode";
        String resetShip = "resetShip";

        JSONObject jsonObject = JSON.parseObject(param);
        try {
            if (jsonObject != null) {
                Long id = jsonObject.getLong(idString);
                Long currentPage = jsonObject.getLong(currentPageString);
                Long pageSize = jsonObject.getLong(pageSizeString);
                String eCode = jsonObject.getString(eCodeString);
                String eName = jsonObject.getString(eNameString);
                String sku = jsonObject.getString(skuString);
                String type = jsonObject.getString("type");
                // 区分是否查询组合商品
                String isCombination = jsonObject.getString("isCombination");
                String resetShipFlag = jsonObject.getString(resetShip);
                boolean isResetShip = StringUtils.isNotEmpty(resetShipFlag) && "1".equals(resetShipFlag);
                boolean isMilkCard = false;

                QueryWrapper<OcBorderItemExtention> wrapper = new QueryWrapper<>();
                //检查当前记录是否存在
                OcBOrder ocBorderDto = orderMapper.selectById(id);
                if (ocBorderDto == null) {
                    holderV14.setCode(-1);
                    holderV14.setMessage("当前记录已不存在！");
                    return holderV14;
                }
                // 判断是否是奶卡订单
                String businessTypeCode = ocBorderDto.getBusinessTypeCode();
                if (OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode().equals(businessTypeCode)) {
                    isMilkCard = true;
                }

                wrapper.eq("oc_b_order_id", id);
                if (StringUtils.isEmpty(isCombination)) {
                    wrapper.ne("pro_type", SkuType.NO_SPLIT_COMBINE);
                }
                if (StringUtils.isNotEmpty(isCombination)) {
                    if ("1".equals(isCombination)) {
                        wrapper.and(Wrapper -> Wrapper.eq("pro_type", SkuType.NORMAL_PRODUCT)
                                .or().eq("pro_type", SkuType.NO_SPLIT_COMBINE));
                    } else if ("2".equals(isCombination)) {
                        wrapper.and(Wrapper -> Wrapper.eq("pro_type", SkuType.COMBINE_PRODUCT)
                                .or().eq("pro_type", SkuType.GIFT_PRODUCT)
                                .or().eq("pro_type", SkuType.NORMAL_PRODUCT));
                    }
                }
                Page<OcBorderItemExtention> page = new Page<>();
                if (currentPage != null) {
                    page.setCurrent(currentPage);
                }
                if (pageSize != null) {
                    page.setSize(pageSize);
                }
                //查询条件：商品编码
                if (StringUtils.isNotEmpty(eCode)) {
                    wrapper.like(eCodeString, eCode);
                }
                //查询条件：商品名称
                if (StringUtils.isNotEmpty(eName)) {
                    wrapper.like(eNameString, eName);
                }
                //查询条件：条码编码 (前端精确查询)
                if (StringUtils.isNotEmpty(sku)) {
                    wrapper.eq(skuString, sku);
                }
                //查询条件：排除退款成功的
                if (StringUtils.isNotEmpty(type) && "copyOrder".equals(type)) {
                    wrapper.ne("refund_status", OcOrderRefundStatusEnum.SUCCESS.getVal());
                }
                wrapper.eq("isactive", IsActiveEnum.Y.getKey());
                IPage iPage = extMapper.selectPage(page, wrapper);
                List<OcBOrderItemExt> ocBOrderItemExtList = ocBOrderItemExtService.queryByOrderId(id);
                Map<Long, OcBOrderItemExt> ocBOrderItemExtMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(ocBOrderItemExtList)) {
                    ocBOrderItemExtMap = ocBOrderItemExtList.stream().collect(Collectors.toMap(OcBOrderItemExt::getOrderItemId, o -> o));
                }
                List<OcBorderItemExtention> records = iPage.getRecords();
                //判断是否有组合商品 给前端判断是否显示切换组合商品按钮
                if (records != null && records.size() != 0) {
                    for (OcBorderItemExtention bean : records) {
                        // 跨库查询 “商品编码”在【标准商品档案】中的存在且为启用状态的规格 放入本条bean数据里
                        changeStatus(bean);
                        calcAmtRefundSingle(bean);
                        // 尺寸,颜色,字段选项组
                        querySizeAndCLrs(bean);
                        // 商品编码不为空  生成查询条件
                        BigDecimal volume = BigDecimal.ZERO;
                        if (StringUtils.isNotEmpty(bean.getPsCProEcode())) {
                            SkuQueryRequest queryRequest = getProEcodeRequestBean(bean.getPsCProEcode());
                            try {
                                List standList = psGetCommodityInformationService.getStandardList(queryRequest);
                                if (standList != null && standList.size() != 0) {
                                    bean.setSkuNum(standList.size());
                                    bean.setStandardsList(standList);
                                    for (Object o : standList) {
                                        JSONObject obj = (JSONObject) o;
                                        volume = volume.add(BigDecimalUtil.isNullReturnZero(obj.getBigDecimal("VOLUME")).setScale(6, BigDecimal.ROUND_HALF_UP));
                                    }
                                }
                            } catch (Exception ex) {
                                log.error(LogUtil.format("日志服务：调用规格查询服务异常：{}")
                                        , Throwables.getStackTraceAsString(ex));
                            }
                        }
                        bean.setVolume(volume.stripTrailingZeros().toPlainString());
                        bean.setStock(BigDecimalUtil.isNullReturnZero(null));
                        bean.setOcBOrderItemId(bean.getId());
                        if (ObjectUtils.isEmpty(bean.getIsGift())) {
                            bean.setIsGift(0);
                        }
                        if (BigDecimalUtil.isZero(bean.getQtyLost())) {
                            bean.setQtyLost(BigDecimal.ZERO);
                        }
                        if (BigDecimalUtil.isZero(bean.getStock())) {
                            bean.setStock(BigDecimal.ZERO);
                        }
                        bean.setTotalVolume(BigDecimalUtil.isNullReturnZero(bean.getQty()).multiply(BigDecimalUtil.isNullReturnZero(volume)).stripTrailingZeros().toPlainString());

                        if (bean.getReserveBigint01() != null) {
                            bean.setGiftAttr(DmsGiftAttrEnum.getDescByVal(bean.getReserveBigint01()));
                        }
                        if (bean.getReserveBigint02() == null || bean.getReserveBigint02() == 0) {
                            bean.setCheapestExpress("否");
                        }else {
                            bean.setCheapestExpress("是");
                        }
                        OcBOrderItemExt ocBOrderItemExt = ocBOrderItemExtMap.get(bean.getId());
                        if (ocBOrderItemExt != null) {
                            bean.setCategoryName(ocBOrderItemExt.getMDim12Name());

                            // 如果销售123级 存在名称不存在 则根据编码查名称
                            if (StringUtils.isEmpty(ocBOrderItemExt.getSalesDepartmentName())) {
                                List<String[]> arrList = new ArrayList<>();
                                if (StringUtils.isNotBlank(ocBOrderItemExt.getSalesCenterCode())) {
                                    String[] costCenter = {"C_STOREATTRIB1_ID", ocBOrderItemExt.getSalesCenterCode()};
                                    arrList.add(costCenter);
                                }

                                if (StringUtils.isNotBlank(ocBOrderItemExt.getSalesDepartmentCode())) {
                                    String[] salesDepartment = {"C_STOREATTRIB2_ID", ocBOrderItemExt.getSalesDepartmentCode()};
                                    arrList.add(salesDepartment);
                                }

                                if (StringUtils.isNotBlank(ocBOrderItemExt.getSalesGroupCode())) {
                                    String[] costCenter = {"C_STOREATTRIB3_ID", ocBOrderItemExt.getSalesGroupCode()};
                                    arrList.add(costCenter);
                                }
                                Map<String, JSONObject> resultMap = cpRpcService.findStoredimItemIdByeCodeList(arrList);
                                bean.setSalesCenterName(resultMap.get("C_STOREATTRIB1_ID") != null ? resultMap.get("C_STOREATTRIB1_ID").getString("ename") : null);
                                bean.setSalesDepartmentName(resultMap.get("C_STOREATTRIB2_ID") != null ? resultMap.get("C_STOREATTRIB2_ID").getString("ename") : null);
                                bean.setSalesGroupName(resultMap.get("C_STOREATTRIB3_ID") != null ? resultMap.get("C_STOREATTRIB3_ID").getString("ename") : null);
                            } else {
                                bean.setSalesDepartmentName(ocBOrderItemExt.getSalesDepartmentName());
                                bean.setSalesGroupName(ocBOrderItemExt.getSalesGroupName());
                                bean.setSalesCenterName(ocBOrderItemExt.getSalesCenterName());
                            }
                            bean.setDistNameLevelOne(ocBOrderItemExt.getDistNameLevelOne());
                            bean.setDistNameLevelTwo(ocBOrderItemExt.getDistNameLevelTwo());
                            bean.setDistNameLevelThree(ocBOrderItemExt.getDistNameLevelThree());
                        }
                        // 需要重新计算成交单价跟成交金额
                        if (isResetShip && isMilkCard) {
                            // 根据订单id&订单明细id 查询奶卡信息
                            List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderIdAndItemId(id, bean.getId());
                            if (CollectionUtils.isNotEmpty(ocBOrderNaiKaList)) {
                                // 如果存在奶卡 则判断这些奶卡是否有一个是对账完成 如果有的话 则修改金额。 需要过滤掉作废了的奶卡
                                for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKaList) {
                                    if (Objects.equals(ocBOrderNaiKa.getAccountToNaika(), AccountToNaiKaEnum.SUCCESS.getStatus()) && !Objects.equals(ocBOrderNaiKa.getNaikaStatus(), OmsOrderNaiKaStatusEnum.VOID_SUCCESS.getStatus())) {
                                        // 随机取一个价格即可
                                        BigDecimal wipeAmt = ocBOrderNaiKa.getWipeAmt();
                                        bean.setPriceActual(wipeAmt);
                                        bean.setRealAmt(wipeAmt.multiply(bean.getQty()));
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                holderV14.setCode(0);
                holderV14.setMessage("查询成功");
                holderV14.setData(iPage);
                return holderV14;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询明细异常：{}"), Throwables.getStackTraceAsString(e));
            holderV14.setCode(-1);
            holderV14.setMessage("查询明细异常:" + e.getMessage());
            return holderV14;
        }

        holderV14.setCode(-1);
        holderV14.setMessage("当前记录已不存在！");
        return holderV14;
    }


    public ValueHolderV14<Map<Long, List<OcBorderItemExtention>>> getBatchOrderDetailList(List<JSONObject> params, User loginUser) {
        log.info(LogUtil.format("getBatchOrderDetailList.params:{}"), JSON.toJSONString(params));
        ValueHolderV14<Map<Long, List<OcBorderItemExtention>>> holderV14 = new ValueHolderV14();
        String idString = "id";
        String currentPageString = "currentPage";
        String pageSizeString = "pageSize";
        String eCodeString = "ps_c_pro_ecode";
        String eNameString = "ps_c_pro_ename";
        String skuString = "ps_c_sku_ecode";
        if (CollectionUtils.isEmpty(params)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请求参数不能为空！");
        }

        Map<Long, List<OcBorderItemExtention>> listMap = new HashMap<>();
        for (JSONObject jsonObject : params){
            try {
                if (jsonObject != null) {
                    Long id = jsonObject.getLong(idString);
                    Long currentPage = jsonObject.getLong(currentPageString);
                    Long pageSize = jsonObject.getLong(pageSizeString);
                    String eCode = jsonObject.getString(eCodeString);
                    String eName = jsonObject.getString(eNameString);
                    String sku = jsonObject.getString(skuString);
                    String type = jsonObject.getString("type");
                    String isCombination = jsonObject.getString("isCombination"); //区分是否查询组合商品


                    QueryWrapper<OcBorderItemExtention> wrapper = new QueryWrapper<>();
                    //检查当前记录是否存在
                    OcBOrder ocBorderDto = orderMapper.selectById(id);
                    if (ocBorderDto == null) {
                        holderV14.setCode(-1);
                        holderV14.setMessage("当前记录已不存在！");
                        return holderV14;
                    }
                    List<OcBorderItemExtention> ocBorderItemExts = new ArrayList<>();
                    wrapper.eq("oc_b_order_id", id);
                    if (StringUtils.isEmpty(isCombination)) {
                        wrapper.ne("pro_type", SkuType.NO_SPLIT_COMBINE);
                    }
                    if (StringUtils.isNotEmpty(isCombination)) {
                        if ("1".equals(isCombination)) {
                            wrapper.and(Wrapper -> Wrapper.eq("pro_type", SkuType.NORMAL_PRODUCT)
                                    .or().eq("pro_type", SkuType.NO_SPLIT_COMBINE));
                        } else if ("2".equals(isCombination)) {
                            wrapper.and(Wrapper -> Wrapper.eq("pro_type", SkuType.COMBINE_PRODUCT)
                                    .or().eq("pro_type", SkuType.GIFT_PRODUCT)
                                    .or().eq("pro_type", SkuType.NORMAL_PRODUCT));
                        }
                    }
                    Page<OcBorderItemExtention> page = new Page<>();
                    if (currentPage != null) {
                        page.setCurrent(currentPage);
                    }
                    if (pageSize != null) {
                        page.setSize(pageSize);
                    }
                    //查询条件：商品编码
                    if (StringUtils.isNotEmpty(eCode)) {
                        wrapper.like(eCodeString, eCode);
                    }
                    //查询条件：商品名称
                    if (StringUtils.isNotEmpty(eName)) {
                        wrapper.like(eNameString, eName);
                    }
                    //查询条件：条码编码 (前端精确查询)
                    if (StringUtils.isNotEmpty(sku)) {
                        wrapper.eq(skuString, sku);
                    }
                    //查询条件：排除退款成功的
                    if (StringUtils.isNotEmpty(type) && "copyOrder".equals(type)) {
                        wrapper.ne("refund_status", OcOrderRefundStatusEnum.SUCCESS.getVal());
                    }
                    wrapper.eq("isactive", IsActiveEnum.Y.getKey());
                    IPage iPage = extMapper.selectPage(page, wrapper);
                    List<OcBorderItemExtention> records = iPage.getRecords();
                    //判断是否有组合商品 给前端判断是否显示切换组合商品按钮
                    // boolean flag = false;
                    if (records != null && records.size() != 0) {
                        //查询主表对应的逻辑仓id集合
                        Long cpCShopId = ocBorderDto.getCpCShopId();
                        Long cpCPhyWarehouseId = ocBorderDto.getCpCPhyWarehouseId();
                        //获取逻辑仓交集ids集合
                        List<Long> storeIds = getStoreIds(cpCShopId, cpCPhyWarehouseId);
                        // 获取sku逻辑仓可用库存
                        //todo 查询库存接口
//                    Map<Long, BigDecimal> skuQtyMap = this.getStock(records, storeIds, loginUser);
//                    if (log.isDebugEnabled()) {
//                        log.debug("{} getOrderDetailList.skuQtyMap:{}", ocBorderDto.getId(), JSON.toJSONString(skuQtyMap));
//                    }
                        for (OcBorderItemExtention bean : records) {
                            // 跨库查询 “商品编码”在【标准商品档案】中的存在且为启用状态的规格 放入本条bean数据里
                            // 查询库存
                            changeStatus(bean);
                            calcAmtRefundSingle(bean);
                            // 尺寸,颜色,字段选项组
                            querySizeAndCLrs(bean);
                            // 商品编码不为空  生成查询条件
                            if (StringUtils.isNotEmpty(bean.getPsCProEcode())) {
                                SkuQueryRequest queryRequest = getProEcodeRequestBean(bean.getPsCProEcode());
                                try {
                                    List standList = psGetCommodityInformationService.getStandardList(queryRequest);
                                    if (standList != null && standList.size() != 0) {
                                        bean.setSkuNum(standList.size());
                                        bean.setStandardsList(standList);
                                    }
                                } catch (Exception ex) {
                                    log.error(LogUtil.format("日志服务：调用规格查询服务异常：") + Throwables.getStackTraceAsString(ex));
                                }
                            }
                            bean.setStock(BigDecimalUtil.isNullReturnZero(null));
                            // @20200714 退换货单明细加订单明细ID
                            bean.setOcBOrderItemId(bean.getId());
                            // @20200812 吊牌价查询赋值
                            // bean.setPrice(bean.getPriceList());
                            if (ObjectUtils.isEmpty(bean.getIsGift())) {
                                bean.setIsGift(0);
                            }
                            if (BigDecimalUtil.isZero(bean.getQtyLost())) {
                                bean.setQtyLost(BigDecimal.ZERO);
                            }
                            if (BigDecimalUtil.isZero(bean.getStock())) {
                                bean.setStock(BigDecimal.ZERO);
                            }
                            ocBorderItemExts.add(bean);
                            listMap.put(ocBorderDto.getId(),ocBorderItemExts);
                        }
                    }

                }
            } catch (Exception e) {
                log.error(LogUtil.format("查询明细异常：{}"), Throwables.getStackTraceAsString(e));
                holderV14.setCode(-1);
                holderV14.setMessage("查询明细异常:" + e.getMessage());
                return holderV14;
            }
        }
        holderV14.setCode(0);
        holderV14.setMessage("查询成功");
        holderV14.setData(listMap);
        return holderV14;

    }

    /**
     * 根据订单的下单店铺ID查询店铺同步库存策略表中的此店铺的供货仓库集合，
     * 在根据订单的发货实体仓ID，查找到实体仓对应逻辑仓的集合，
     * 然后获取店铺供货仓和实体仓对应逻辑仓的交集，
     * 将交集的逻辑仓信息和明细的SKU信息传给库存中心查询此SKU在各个逻辑仓中的可用库存之和
     * 即为展示的库存数
     *
     * @param cpCShopId         平台店铺Id
     * @param cpCPhyWarehouseId 实体仓Id
     */
    private List<Long> getStoreIds(Long cpCShopId, Long cpCPhyWarehouseId) {
        if (cpCShopId == null || cpCPhyWarehouseId == null) {
            return null;
        }
        //逻辑仓Id集合
        return getStoreIdList(cpCShopId, cpCPhyWarehouseId);
    }

    /**
     * 分别获取两个集合，计算交集
     *
     * @param cpCShopId         平台店铺Id
     * @param cpCPhyWarehouseId 实体仓Id
     * @return 交集
     */
    private List<Long> getStoreIdList(Long cpCShopId, Long cpCPhyWarehouseId) {
        List<Long> storeIds = null;
        List<Long> wareIds = null;
        try {
            log.info(LogUtil.format("日志服务：调用库存中心查询可用库存服务入参cpCShopId=", cpCShopId));
            storeIds = queryOrderItemAvailbleStockService.getStoreIds(cpCShopId);
        } catch (Exception ex) {
            log.error(LogUtil.format("日志服务：调用库存中心查询可用库存服务异常queryOrderItemAvailbleStockService.getStoreIds(),error:{}") , Throwables.getStackTraceAsString(ex));
        }
        try {
            log.info(LogUtil.format("日志服务：调用库存中心查询可用库存服务入参cpCPhyWarehouseId=", cpCPhyWarehouseId));
            wareIds = queryOrderItemAvailbleStockService.getWareIds(cpCPhyWarehouseId);
        } catch (Exception ex) {
            log.error(LogUtil.format("日志服务：调用库存中心查询可用库存服务异常queryOrderItemAvailbleStockService.getWareIds() error:{}"), Throwables.getStackTraceAsString(ex));
        }

        if (storeIds == null || storeIds.size() == 0) {
            return null;
        }
        if (wareIds == null || wareIds.size() == 0) {
            return null;
        }
        //计算交集
        storeIds.retainAll(wareIds);
        return storeIds;
    }

    private SkuQueryRequest getProEcodeRequestBean(String psCProEcode) {
        SkuQueryRequest queryRequest = new SkuQueryRequest();
        queryRequest.setIsBlur("N");
        PsCSku psCSku = new PsCSku();
        psCSku.setPsCProEcode(psCProEcode);
        queryRequest.setPsCSku(psCSku);
        queryRequest.setLimit(200);
        return queryRequest;
    }

    /**
     * 根据订单的下单店铺ID查询店铺同步库存策略表中的此店铺的供货仓库集合，
     * 在根据订单的发货实体仓ID，查找到实体仓对应逻辑仓的集合，
     * 然后获取店铺供货仓和实体仓对应逻辑仓的交集，
     * 将交集的逻辑仓信息和明细的SKU信息传给库存中心查询此SKU在各个逻辑仓中的可用库存之和
     * 即为展示的库存数
     *
     * @param storeIds  交集
     * @param loginUser User
     * @return 可用库存
     */
    private BigDecimal getStock(OcBorderItemExtention itemInfo, List<Long> storeIds, User loginUser) {
        if (storeIds == null || storeIds.size() == 0) {
            return BigDecimal.ZERO;
        }
        // 是否非正常品（组合和福袋）
        boolean isGroup = itemInfo.getProType() != null && itemInfo.getProType() == SkuType.NO_SPLIT_COMBINE;
        if (!isGroup) {
            SgStorageQueryRequest request = new SgStorageQueryRequest();
            List<String> list = new ArrayList<>();
            list.add(itemInfo.getPsCSkuEcode());
            request.setStoreIds(storeIds);
            request.setSkuEcodes(list);
            ValueHolderV14<List<SgBStorage>> holderV14 = sgRpcService.queryStorage(request, loginUser);
            if (holderV14 != null && holderV14.isOK()) {
                List<SgBStorage> data = holderV14.getData();
                //sum getQtyAvailable
                if (data != null && data.size() != 0) {
                    BigDecimal stock = BigDecimal.ZERO;
                    for (SgBStorage sgBStorage : data) {
                        stock = stock.add(sgBStorage.getQtyAvailable());
                    }
                    return stock;
                }
            }
        } else {
            ValueHolderV14<List<SgGroupStorageQueryResult>> holderV14 = sgRpcService
                    .queryGroupStorage(Lists.newArrayList(itemInfo.getPsCSkuId()), storeIds);
            if (holderV14 != null && holderV14.isOK()) {
                List<SgGroupStorageQueryResult> data = holderV14.getData();
                if (data != null && data.size() != 0) {
                    BigDecimal stock = BigDecimal.ZERO;
                    for (SgGroupStorageQueryResult sgBStorage : data) {
                        stock = stock.add(sgBStorage.getQtyStorage());
                    }
                    return stock;
                }
            }
        }

        return BigDecimal.ZERO;
    }

    /**
     * 获取库存
     *
     * @param items
     * @param storeIds
     * @param loginUser
     * @return map  key is skuId ,val sku qty
     */
    private Map<Long, BigDecimal> getStock(List<OcBorderItemExtention> items, List<Long> storeIds, User loginUser) {
        Map<Long, BigDecimal> skuStorageMap = Maps.newHashMap();
        if (storeIds == null || storeIds.size() == 0) {
            return skuStorageMap;
        }
        List<String> skuCodes = Lists.newArrayList();
        List<Long> skuIds = Lists.newArrayList();
        for (OcBorderItemExtention record : items) {
            if (record.getProType() == null || record.getProType() != SkuType.NO_SPLIT_COMBINE) {
                skuCodes.add(record.getPsCSkuEcode());
            } else {
                skuIds.add(record.getPsCSkuId());
            }
        }
        if (CollectionUtils.isNotEmpty(skuCodes)) {
            SgStorageQueryRequest request = new SgStorageQueryRequest();
            request.setStoreIds(storeIds);
            request.setSkuEcodes(skuCodes);
            ValueHolderV14<List<SgBStorage>> holderV14 = sgRpcService.queryStorage(request, loginUser);
            if (holderV14 != null && holderV14.isOK()) {
                List<SgBStorage> data = holderV14.getData();
                for (SgBStorage storage : data) {
                    if (skuStorageMap.containsKey(storage.getPsCSkuId())) {
                        skuStorageMap.put(storage.getPsCSkuId(), skuStorageMap.get(storage.getPsCSkuId()).add(storage.getQtyAvailable()));
                    } else {
                        skuStorageMap.put(storage.getPsCSkuId(), storage.getQtyAvailable());
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(skuIds)) {
            ValueHolderV14<List<SgGroupStorageQueryResult>> holderV14 = sgRpcService
                    .queryGroupStorage(skuIds, storeIds);
            if (holderV14 != null && holderV14.isOK()) {
                List<SgGroupStorageQueryResult> data = holderV14.getData();
                if (data != null && data.size() != 0) {
                    List<SgGroupStorageQueryResult> collect = data.stream().collect(Collectors
                            .collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(result -> {
                                // 根据 ID 和 逻辑仓id
                                return result.getId() + "," + result.getCpCStoreId();
                            }))), ArrayList::new));
                    for (SgGroupStorageQueryResult sgBStorage : collect) {
                        if (skuStorageMap.containsKey(sgBStorage.getId())) {
                            skuStorageMap.put(sgBStorage.getId(), skuStorageMap.get(sgBStorage.getId()).add(sgBStorage.getQtyStorage()));
                        } else {
                            skuStorageMap.put(sgBStorage.getId(), sgBStorage.getQtyStorage());
                        }
                    }
                }
            }
        }
        return skuStorageMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveStandards(String param, User loginUser) {
        OcBOrderRelation relation = new OcBOrderRelation();
        ValueHolderV14 holder = new ValueHolderV14();
        JSONObject jsonObject = JSON.parseObject(param);
        Long ocBOrderItemId = jsonObject.getLong("oc_b_order_item_id");
        Long ocBOrderId = jsonObject.getLong("oc_b_order_id");
        //锁单
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrderItem newItem = ocBorderItemMapper.selectById(ocBOrderItemId);

                OcBOrder ocBOrder = orderMapper.selectByID(ocBOrderId);

                if (ocBOrder == null || newItem == null) {
                    holder.setCode(-1);
                    holder.setMessage("当前记录已不存在! ");
                    return holder;
                }

                //如果是标记退款完成，不允许修改规格
                Integer refundStatus = newItem.getRefundStatus();
                if (refundStatus != null && refundStatus.equals(OcOrderRefundStatusEnum.SUCCESS.getVal())) {
                    holder.setCode(-1);
                    holder.setMessage("标记退款完成，不允许修改规格!");
                    return holder;
                }

                //修改订单服务用(clone订单明细)
                OcBOrderItem oldItem = new OcBOrderItem();
                BeanUtils.copyProperties(newItem, oldItem);
                //减少明细
                oldItem.setQty(new BigDecimal("0"));

                //配置商品信息
                setNewItemInfo(newItem, jsonObject, loginUser);

                //更新明细
                UpdateWrapper<OcBOrderItem> wrapper = new UpdateWrapper<>();
                wrapper.eq("oc_b_order_id", ocBOrderId).eq("id", ocBOrderItemId);
                try {
                    ocBorderItemMapper.update(newItem, wrapper);
                    // SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, ocBorderItemMapper.selectById(newItem.getId()), newItem.getId(), ocBOrderId);

                    //订单修改服务
                    relation.setOrderInfo(ocBOrder);
                    List<OcBOrderItem> itemList = new ArrayList<>();
                    itemList.add(newItem);
                    itemList.add(oldItem);
                    relation.setOrderItemList(itemList);
                    holder = service.updateOrder(relation, loginUser);
                    if (!holder.isOK()) {
                        throw new NDSException(holder.getMessage());
                    }
                    //赋值全部sku字段
                    List<OcBOrderItem> orderItems=orderItemMapper.selectOrderItems(ocBOrder.getId());
                    //加上sku
                    String allSku = "";
                    for (OcBOrderItem item : orderItems) {
                        if (allSku.length()>=100){
                            allSku += "...";
                            break;
                        }
                        Long proType = Optional.ofNullable(item.getProType()).orElse(0L);
                        if (proType.intValue() == SkuType.GIFT_PRODUCT || proType.intValue() == SkuType.COMBINE_PRODUCT) {
                            continue;
                        }
                        allSku = allSku + item.getPsCSkuEcode() + "(" + item.getQty().intValue() + "),";
                    }
                    if (StringUtils.isNotEmpty(allSku)) {
                        OcBOrder newOcBOrder =new OcBOrder();
                        newOcBOrder.setId(ocBOrder.getId());
                        newOcBOrder.setAllSku(allSku.substring(0, allSku.length() - 1));
                        orderMapper.updateById(newOcBOrder);
                    }
                    return holder;
                } catch (Exception ex) {
                    log.error(LogUtil.format("日志服务：修改规格异常！,error:{}"), Throwables.getStackTraceAsString(ex));
                    throw new NDSException("修改规格失败！失败原因：" + ex.getMessage());
                }

            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", loginUser.getLocale()));
            }
        } catch (Exception ex) {
            throw new NDSException(Resources.getMessage("订单锁单错误！", loginUser.getLocale()));
        } finally {
            redisLock.unlock();
        }
    }

    private void setNewItemInfo(OcBOrderItem item, JSONObject productInfo, User loginUser) {
        item.setPsCSkuId(productInfo.getLong("SKU_ID"));
        item.setPsCSkuEcode(productInfo.getString("SKU_ECODE"));
        item.setPsCProId(productInfo.getLong("PS_C_PRO_ID"));
        item.setPsCProEcode(productInfo.getString("PS_C_PRO_ECODE"));
        item.setPsCProEname(productInfo.getString("PS_C_PRO_ENAME"));
        item.setPsCClrId(productInfo.getLong("COLOR_ID"));
        item.setPsCClrEcode(productInfo.getString("COLOR_CODE"));
        item.setPsCClrEname(productInfo.getString("COLOR_NAME"));
        item.setPsCSizeId(productInfo.getLong("SIZE_ID"));
        item.setPsCSizeEcode(productInfo.getString("SIZE_CODE"));
        item.setPsCSizeEname(productInfo.getString("SIZE_NAME"));
        item.setSkuSpec(productInfo.getString("SPEC"));
        item.setBarcode(productInfo.getString("GBCODE"));
//        item.setPriceList(productInfo.getBigDecimal("PRICELIST"));
        item.setModifierename(loginUser.getEname());
        item.setModifieddate(new Date());
        //此处解决分库建不能更改的报错
        item.setOcBOrderId(null);
    }


    /**
     * 返回一个带扩展字段的子类
     *
     * @param bean 父类
     */
    private void changeStatus(OcBorderItemExtention bean) {
        Integer refundStatus = bean.getRefundStatus();
        if (refundStatus == null || refundStatus == 0 || refundStatus == 1) {
            bean.setRefund_status_ext("否");
        } else {
            bean.setRefund_status_ext("是");
        }

        // @20201202 明细表增加平台退款状态
        if (StringUtils.isEmpty(bean.getPtReturnStatus())) {
            bean.setPtReturnStatusExt("");
        } else {
            bean.setPtReturnStatusExt(TaobaoReturnOrderExt.RefundStatus.getValueByKey(bean.getPtReturnStatus()));
        }

        //2019-7-18 增加组合商品与福袋商品后 前端页面商品名称的展示 【组合】+ 实际商品的商品名称 + 【组合商品的虚拟条码】
        //判断商品类型
        if (StringUtils.isNotEmpty(bean.getGiftbagSku())) {
            Long reserveBigint01 = bean.getProType(); //商品类型
            if (reserveBigint01 == null) {
                bean.setPsCProEname(bean.getPsCProEname());
            } else if (SkuType.GIFT_PRODUCT == reserveBigint01 || SkuType.COMBINE_PRODUCT == reserveBigint01) {
                String prodName = "【组合】" + bean.getPsCProEname() + "【" + bean.getGiftbagSku() + "】";
                bean.setPsCProEname(prodName);
            } else {
                bean.setPsCProEname(bean.getPsCProEname());
            }
        } else {
            bean.setPsCProEname(bean.getPsCProEname());
        }
        if (bean.getQtyReturnApply() != null) {
            bean.setQtyReturnApplyNum(bean.getQtyReturnApply().intValue());
        }
        if (bean.getQtyRefund() != null) {
            bean.setQtyReturnApplyNum(bean.getQtyRefund().intValue());
        }
        if (bean.getQtyLost() != null) {
            bean.setQtyReturnApplyNum(bean.getQtyLost().intValue());
        }
    }

    /**
     * @param data      参数
     * @param loginUser 登录用户
     * @return list
     */
    List getOrderItemListByIdAndBillNo(JSONObject data, User loginUser) {
        if (data == null) {
            log.error(LogUtil.format("日志服务：OcBorderDetailService.getOrderItemListByIdAndBillNo参数为空"));
            return new ArrayList();
        }
        Integer flag = data.getInteger("FLAG");
        JSONArray order = data.getJSONArray("ORDER");
        if (flag == null) {
            log.error(LogUtil.format("日志服务：OcBorderDetailService.getOrderItemListByIdAndBillNo参数为空"));
            return new ArrayList();
        }
        //  0/1(0是全渠道 1 是退换货)
        if (flag == 0) {
            List<OcBOrder> ocBOrders = JSONArray.parseArray(order.toJSONString(), OcBOrder.class);
            if (ocBOrders != null && ocBOrders.size() != 0) {
                List<OcBorderItemExtention> itemExtList = new ArrayList<>();
                for (OcBOrder ocBOrder : ocBOrders) {
                    String billNo = ocBOrder.getBillNo();
                    //增加一个id属性，否则前端要改
                    JSONObject json = JSONObject.parseObject(JSON.toJSONString(ocBOrder));
                    json.put("id", ocBOrder.getId());
                    ValueHolderV14 holderV14 = getOrderDetailList(json.toJSONString(), loginUser);
                    if (holderV14.getCode() == 0) {
                        IPage iPage = (IPage) holderV14.getData();
                        List<OcBorderItemExtention> records = iPage.getRecords();
                        if (records != null) {
                            //加入bill_no
                            for (OcBorderItemExtention itemExt : records) {
                                itemExt.setBillNo(billNo);
                            }
                            itemExtList.addAll(records);
                        }
                    }
                }
                return itemExtList;
            } else {
                return new ArrayList();
            }
        } else {
            List<OcBReturnOrder> returnOrders = JSONArray.parseArray(order.toJSONString(), OcBReturnOrder.class);
            return getReturnOrderList(returnOrders);
        }

    }


    /**
     * 根据退换货主表查询对应的明细
     *
     * @param returnOrders 退换货主表list
     * @return JSONObject
     */
    private List getReturnOrderList(List<OcBReturnOrder> returnOrders) {
        if (returnOrders != null && returnOrders.size() != 0) {
            List<OcBReturnOrderRefundExt> list = new ArrayList<>();
            for (OcBReturnOrder ocBReturnOrder : returnOrders) {
                List<OcBReturnOrderRefundExt> itemList = ocBReturnOrderRefundMapper.selectListByReturnOrderId(ocBReturnOrder.getId());
                list.addAll(changeOrderRefund(itemList, ocBReturnOrder.getOrigOrderId()));
            }
            return list;
        } else {
            return new ArrayList();
        }
    }

    private List<OcBReturnOrderRefundExt> changeOrderRefund(List<OcBReturnOrderRefundExt> itemList, Long billNo) {
        if (itemList == null || itemList.size() == 0) {
            return new ArrayList<>();
        }
        for (OcBReturnOrderRefundExt item : itemList) {
            item.setBillNo(billNo);
            item.setPriceList(item.getPrice());
            item.setPsCSizeEname(getSizeEname(item));
            item.setQty(item.getQtyCanRefund());
            item.setRealMoney(getRealAmt(item));
        }
        return itemList;
    }

    private String getSizeEname(OcBReturnOrderRefundExt item) {
        if (StringUtils.isNotEmpty(item.getPsCProEcode())) {
            SkuQueryRequest queryRequest = getProEcodeRequestBean(item.getPsCProEcode());
            try {
                List<JSONObject> standList = psGetCommodityInformationService.getStandardList(queryRequest);
                if (standList != null && standList.size() != 0) {
                    JSONObject json = standList.get(0);
                    return json.getString("SIZE_NAME");
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("日志服务：调用规格查询服务异常,error:{}"), Throwables.getStackTraceAsString(ex));
            }
            return null;
        }
        return null;
    }

    /**
     * 商品.尺寸.颜色选项组
     *
     * @param bean OcBorderItemExt
     */
    private void querySizeAndCLrs(OcBorderItemExtention bean) {
        SkuQueryRequest queryRequest = getProEcodeRequestBean(bean.getPsCProEcode());
        List<JSONObject> standList = psGetCommodityInformationService.getStandardList(queryRequest);
        List<JSONObject> sizeList = new ArrayList<>();
        List<JSONObject> clrList = new ArrayList<>();
        if (standList != null && standList.size() > OcBOrderConst.IS_STATUS_IN) {
            JSONObject so;
            JSONObject co;
            //去重
            Set<Long> sizeSet = new HashSet<>();
            Set<Long> colorSet = new HashSet<>();
            for (JSONObject json : standList) {
                Long sizeId = json.getLong("SIZE_ID");
                if (sizeSet.add(sizeId)) {
                    so = new JSONObject();
                    so.put("SIZE_ID", json.get("SIZE_ID"));
                    so.put("SIZE_NAME", json.get("SIZE_NAME"));
                    so.put("SIZE_CODE", json.get("SIZE_CODE"));
                    sizeList.add(so);
                }
                Long colorId = json.getLong("COLOR_ID");
                if (colorSet.add(colorId)) {
                    co = new JSONObject();
                    co.put("COLOR_ID", json.get("COLOR_ID"));
                    co.put("COLOR_NAME", json.get("COLOR_NAME"));
                    co.put("COLOR_CODE", json.get("COLOR_CODE"));
                    clrList.add(co);
                }
            }

            bean.setClrList(clrList);
            bean.setSizeList(sizeList);
        }
    }

    /**
     * 返回实际成交价
     *
     * @param item 退换货明细
     * @return 实际成交价
     */
    private BigDecimal getRealAmt(OcBReturnOrderRefundExt item) {
        if (item.getQtyCanRefund() == null || item.getAmtRefundSingle() == null) {
            return BigDecimal.ZERO;
        }
        if (BigDecimal.ZERO.equals(item.getQtyCanRefund()) || BigDecimal.ZERO.equals(item.getAmtRefundSingle())) {
            return BigDecimal.ZERO;
        }
        return item.getQtyCanRefund().multiply(item.getAmtRefundSingle());
    }

    /**
     * 缺货数量 ,  计算单件成交价
     *
     * @param bean OcBorderItemExt
     */
    private void calcAmtRefundSingle(OcBorderItemExtention bean) {
        if (bean == null) {
            return;
        }

        BigDecimal realAmt = bean.getRealAmt();
        BigDecimal qty = bean.getQty();
        BigDecimal lost = bean.getQtyLost();
        BigDecimal sgAmt = BigDecimal.ZERO;
        BigDecimal priceActual = bean.getPriceActual();

        if (priceActual == null && realAmt != null && qty != null && qty.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
            sgAmt = realAmt.divide(qty, OcBOrderConst.DECIMAL_QTY_FOUR, BigDecimal.ROUND_HALF_UP);
            bean.setPriceActual(sgAmt);
        }
        sgAmt = bean.getPriceActual();
        if (lost == null) {
            bean.setQtyLost(BigDecimal.ZERO);
        }
        BigDecimal totPrice = bean.getTotPriceSettle();
        BigDecimal settle = bean.getPriceSettle();
        // 结算总额,如果没值,则启用成交价格
        if (totPrice == null || totPrice.compareTo(BigDecimal.ZERO) < OcBOrderConst.IS_STATUS_IY) {
            if (realAmt != null && realAmt.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                bean.setTotPriceSettle(realAmt);
            } else {
                bean.setTotPriceSettle(BigDecimal.ZERO);
            }
        }
        if (settle == null || settle.compareTo(BigDecimal.ZERO) < OcBOrderConst.IS_STATUS_IY) {
            if (sgAmt.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                bean.setPriceSettle(sgAmt);
            } else {
                bean.setPriceSettle(BigDecimal.ZERO);
            }
        }
        Long sex = bean.getSex();
        if (sex != null) {
            if (sex.intValue() == 1) {
                bean.setSexName("女");
            } else if (sex.intValue() == 2) {
                bean.setSexName("男");
            } else if (sex.intValue() == 3) {
                bean.setSexName("中性");
            } else {
                log.debug(LogUtil.format("通过性别id查询性别名，性别id=" ,sex));
                try {
                    ValueHolderV14<PsCProdimItem> holderV14 = psRpcService.queryProdimItem(sex);
                    log.debug(LogUtil.format("通过性别id查询性别名返回Holder=") + JSON.toJSONString(holderV14));
                    if (holderV14.isOK()) {
                        PsCProdimItem data = holderV14.getData();
                        if (data != null) {
                            bean.setSexName(data.getEname());
                        }
                    }
                } catch (Exception ex) {
                    log.error(LogUtil.format("通过性别id查询性别名异常={}"), Throwables.getStackTraceAsString(ex));
                }
            }
        }
    }

    /**
     * 计算-最晚发货时间
     * 1、若订单有主表预计发货时间或明细预计发货时间，优先取主表预计发货时间
     * 2、若无主表无预计发货时间，明细表有预计发货时间，则过滤明细中为空的预计发货时间后，取最早的时间
     * 3. 若主表和订单表中都没有则“最晚发货时间”取支付时间+店铺档案 平台时效（平台时效为空则默认按24处理）
     */
    public Date generateDeliveryTime(OcBOrder ocBOrder) {
        if (Objects.nonNull(ocBOrder.getEstimateConTime())) {
            return ocBOrder.getEstimateConTime();
        }

        List<OcBOrderItem> orderItemList = orderItemMapper.selectOrderItemListOccupy(ocBOrder.getId());
        if (orderItemList == null || orderItemList.isEmpty()) {
            return null;
        }

        Optional<Date> dateOptional = ListUtils.emptyIfNull(orderItemList).stream()
                .map(OcBOrderItem::getEstimateConTime)
                .filter(Objects::nonNull)
                .min(Date::compareTo);
        if (dateOptional.isPresent()) {
            return dateOptional.get();
        }

        Date date = ocBOrder.getPayTime();
        if (Objects.isNull(ocBOrder.getPayTime())) {
            date = ocBOrder.getCreationdate();
        }

        int defalutTimeoutPlate = 24;
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        if (Objects.nonNull(cpShop) && Objects.nonNull(cpShop.getTimeoutPlate())) {
            defalutTimeoutPlate = cpShop.getTimeoutPlate();
        }

        return DateUtils.addHours(date, defalutTimeoutPlate);
    }

    /**
     * 更新最晚发货时间（含订单最晚发货时间更新）
     * @param tid
     */
    public void updatgeDeliveryTime(String tid) {
        if (StringUtils.isBlank(tid)){
            return;
        }

        List<OcBOrder> orders = orderMapper.selectOcBOrderByTid(tid);
        //过滤取消和系统作废的订单
        orders = orders.stream().filter(p -> !OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus())
                && !OmsOrderStatus.SYS_VOID.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(orders)){
            return;
        }

        for (OcBOrder ocBOrder : orders) {
            Date latestDeliveryTime = ocBOrder.getLatestDeliveryTime();
            if (latestDeliveryTime != null){
                continue;
            }

            Date date = generateDeliveryTime(ocBOrder);
            if (date == null) {
                return;
            }

            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setId(ocBOrder.getId());
            updateOrder.setLatestDeliveryTime(date);
            updateOrder.setModifieddate(new Date());
            orderMapper.updateById(updateOrder);
        }

    }

}

