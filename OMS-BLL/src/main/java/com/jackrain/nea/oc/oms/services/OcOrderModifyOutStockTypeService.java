package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date ：Created in 13:24 2020/3/6
 * description ：
 * @ Modified By：
 */
@Service
@Slf4j
public class OcOrderModifyOutStockTypeService {
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    public ValueHolderV14<JSONArray> modifyOutStockType(JSONArray idArray, Integer type, User user) {
        ValueHolderV14<JSONArray> valueHolderV14 = new ValueHolderV14<>();
        JSONArray data = new JSONArray();
        int successCount = 0;
        for (int i = 0; i < idArray.size(); i++) {
            JSONObject object = new JSONObject();
            Long id = idArray.getLong(i);
            try {
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(id);
                if (ocBOrder == null) {
                    object.put("id", id);
                    object.put("message", "根据订单ID查询订单信息失败！");
                    data.add(object);
                    continue;
                }
                if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())
                        && !OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
                    object.put("id", id);
                    object.put("message", "当前状态异常，不允许修改出库类型！");
                    data.add(object);
                    omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_MODIFY_OUT_STOCK_TYPE.getKey(), "订单id：" + id + "，状态不是待审核和缺货，不允许修改出库类型！", null, null, user);
                    continue;
                }
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setId(id);
                updateOrder.setOutType(type);
                int count = ocBOrderMapper.updateById(updateOrder);
                if (count > 0) {
                    object.put("id", id);
                    object.put("message", "success");
                    successCount++;
                    omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_MODIFY_OUT_STOCK_TYPE.getKey(), "订单id：" + id + "，更新出库类型成功！", null, null, user);
                } else {
                    object.put("id", id);
                    object.put("message", "更新出库类型失败");
                    data.add(object);
                    omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_MODIFY_OUT_STOCK_TYPE.getKey(), "订单id：" + id + "，更新出库类型失败！", null, null, user);
                }
            } catch (Exception e) {
                e.printStackTrace();
                object.put("id", id);
                object.put("message", "修改出库类型失败：" + e.getMessage());
                omsOrderLogService.addUserOrderLog(id, null, OrderLogTypeEnum.ORDER_MODIFY_OUT_STOCK_TYPE.getKey(), "订单id：" + id + "，修改出库类型失败：" + e.getMessage(), null, null, user);
                data.add(object);
            }
        }
        String message;
        if (successCount <= 0) {
            message = "批量修改出库类型全部失败";
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(message);
        } else if (successCount < idArray.size()) {
            message = "批量修改出库类型，成功" + String.valueOf(successCount) + "条，失败" + String.valueOf(idArray.size() - successCount) + "条！";
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage(message);
        } else {
            message = "批量修改出库类型全部成功";
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage(message);
        }
        valueHolderV14.setData(data);
        return valueHolderV14;
    }
}
