package com.jackrain.nea.oc.oms.services.directreport;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.sourcing.model.result.SgDirectOrderStorageOccupyResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBDirectReportOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBDirectReportOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBDirectReportOrderStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrder;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrderItem;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgNewRpcService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 直发预占保存
 *
 * <AUTHOR>
 * @since 2024-11-29 11:07
 */
@Slf4j
@Service
public class OcBDirectReportOrderSubmitService {
    @Resource
    private OcBDirectReportOrderMapper ocBDirectReportOrderMapper;
    @Resource
    private OcBDirectReportOrderItemMapper ocBDirectReportOrderItemMapper;

    @Resource
    private SgNewRpcService sgNewRpcService;

    /**
     * 提交-加锁
     */
    @Transactional(rollbackFor = {Exception.class})
    public ValueHolderV14<SgDirectOrderStorageOccupyResult> submit(Long objId, User user) {
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(BllRedisKeyResources.buildDirectReportOptLockKey(objId));
        try {
            if (redisLock.tryLock(1, TimeUnit.MINUTES)) {
                return doSubmit(objId, user);
            } else {
                throw new NDSException("请勿同时操作，建议稍后再试");
            }
        } catch (InterruptedException e) {
            log.warn(LogUtil.format("直发预占加锁失败，请联系值班人员,异常信息:{}",
                    "DirectReportOptLockKey.error"), Throwables.getStackTraceAsString(e));
            throw new NDSException("加锁失败，请联系值班人员");
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 提交
     */
    private ValueHolderV14<SgDirectOrderStorageOccupyResult> doSubmit(Long objId, User user) {
        OcBDirectReportOrder main = ocBDirectReportOrderMapper.selectById(objId);
        if (main == null || YesNoEnum.N.getKey().equals(main.getIsactive())) {
            throw new NDSException("未找到有效记录");
        }
        if (Objects.isNull(main.getAutoReleaseTime())) {
            throw new NDSException("自动释放时间不能为空");
        }
        if (!OcBDirectReportOrderStatusEnum.UN_AUDITED.getValue().equals(main.getStatus())) {
            throw new NDSException("仅未审核状态支持审核");
        }

        List<OcBDirectReportOrderItem> items = ocBDirectReportOrderItemMapper.selectList(new QueryWrapper<OcBDirectReportOrderItem>().lambda()
                .eq(OcBDirectReportOrderItem::getOcBDirectReportOrderId, main.getId())
                .eq(BaseModel::getIsactive, YesNoEnum.Y.getKey()));
        if (CollectionUtils.isEmpty(items)) {
            throw new NDSException("未找到有效明细");
        }

        ValueHolderV14<SgDirectOrderStorageOccupyResult> holderV14 = sgNewRpcService.directReportOrderSubmit(main, items, user);
        if (Objects.nonNull(holderV14) && holderV14.isOK()) {
            SgDirectOrderStorageOccupyResult occupyResult = holderV14.getData();
            main.setSgBShareOutBillNo(occupyResult.getShareOutNo());
            main.setSgBStoOutBillNo(occupyResult.getStoOutNo());

            main.setStatus(OcBDirectReportOrderStatusEnum.AUDITED.getValue());
            main.setSubmitUserId(user.getId().longValue());
            main.setSubmitTime(new Date());
            OmsModelUtil.setDefault4Upd(main, user);
            ocBDirectReportOrderMapper.updateById(main);
        }

        return holderV14;
    }
}
