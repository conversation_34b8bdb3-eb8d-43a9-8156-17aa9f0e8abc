package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.GetDetailCmd;
import com.jackrain.nea.oc.oms.model.result.GetOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnType;
import com.jackrain.nea.oc.oms.model.table.OcBReturnTypeItem;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.vo.OcBReturnAfSendImpVO;
import com.jackrain.nea.oc.oms.vo.OcBReturnAfSendItemVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.SplitListUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @ClassName : OcBReturnAfSendImportService
 * @Description :
 * <AUTHOR>  YCH
 * @Date: 2021-12-15 19:01
 */
@Slf4j
@Component
public class OcBReturnAfSendImportService {

    String threadPoolName = "R3_OMS_CREATE_RETURNAFSENDIMPORTSERVICE_THREAD_POOL_%d";

    @Autowired
    private GetDetailService getDetailService;
    @Autowired
    private RefundFormAfterDeliveryService refundFormAfterDeliveryService;
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private ThreadPoolTaskExecutor createReturnAfSendImportThreadPoolExecutor;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    public ValueHolderV14 ocBReturnAfSendImport(Map<String, List<OcBReturnAfSendItemVO>> mapItem,List<OcBReturnAfSendImpVO> ocBOrderList,User user) {

        int successNum = 0;
        int errorNum = 0;
        List<OcBReturnAfSendImpVO> errorList = new ArrayList<>();
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS,"success");
        List<String> error = new ArrayList<>();
        try{
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            int splitNum = config.getProperty("ocBReturnAfSendImport.split.num", 5);
            int tmpNum = ocBOrderList.size() / splitNum;//每个小list分的个数
            if (ocBOrderList.size() % splitNum != 0) {
                tmpNum = tmpNum + 1;
            }
            List<List<OcBReturnAfSendImpVO>> partition = SplitListUtil.partition(ocBOrderList, tmpNum);
            List<Future<List<ValueHolderV14<OcBReturnAfSendImpVO>>>> results =
                    new ArrayList<Future<List<ValueHolderV14<OcBReturnAfSendImpVO>>>>();
            for (int i = 0; i < partition.size(); i++) {
                results.add(createReturnAfSendImportThreadPoolExecutor.submit(new OcBReturnAfSendImportService.CallableVirtualLibrary(partition.get(i), mapItem, user)));
            }
            //线程执行结果获取
            for (Future<List<ValueHolderV14<OcBReturnAfSendImpVO>>> futureResult : results) {
                List<ValueHolderV14<OcBReturnAfSendImpVO>> valueHolderV14List = futureResult.get();
                for (ValueHolderV14<OcBReturnAfSendImpVO> valueHolderV14 : valueHolderV14List){
                    int code = valueHolderV14.getCode();
                    if (ResultCode.SUCCESS == code){
                        successNum++;
                    }else {
                        error.add(valueHolderV14.getMessage());
                        errorNum++;
                        errorList.add(valueHolderV14.getData());
                    }
                }
            }
            ValueHolderV14 v141 = exportError(errorList, user);
            if (CollectionUtils.isNotEmpty(errorList)) {
                v14.setCode(ResultCode.FAIL);
            }else {
                v14.setCode(ResultCode.SUCCESS);
            }
            v14.setMessage(String.format("执行成功记录数：%s，执行失败记录数：%s", successNum, errorNum));
            v14.setData(v141.getData());
        }catch (InterruptedException e) {
            log.warn("{} Thread ocBReturnAfSendImport InterruptedException：{}", threadPoolName, e);
        }catch (ExecutionException e){
            log.error(" ocBReturnAfSendImport,额外退款单批量导入:{}",e.getMessage());
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("额外退款单批量导入异常！");
            return v14;
        }
        log.info(" ocBReturnAfSendImport InterruptedException：{}",v14);
        return v14;
    }


    class CallableVirtualLibrary implements Callable<List<ValueHolderV14<OcBReturnAfSendImpVO>>> {
        private List<OcBReturnAfSendImpVO> returnAfSendImpVOS;
        private User user;
        private Map<String, List<OcBReturnAfSendItemVO>> mapItem;

        public CallableVirtualLibrary(List<OcBReturnAfSendImpVO> ocBOrderList,Map<String, List<OcBReturnAfSendItemVO>> map,User logUser) {
            this.returnAfSendImpVOS = ocBOrderList;
            this.mapItem = map;
            this.user = logUser;
        }
        @Override
        public List<ValueHolderV14<OcBReturnAfSendImpVO>> call() throws Exception {
            List<ValueHolderV14<OcBReturnAfSendImpVO>> valueHolderV14List = new ArrayList<>();
            List<ValueHolderV14<OcBReturnAfSendImpVO>> valueHolderV14List1 = importReturnAfSend(returnAfSendImpVOS,mapItem,user);
            valueHolderV14List.addAll(valueHolderV14List1);
            return valueHolderV14List;
        }
    }


    public List<ValueHolderV14<OcBReturnAfSendImpVO>> importReturnAfSend(List<OcBReturnAfSendImpVO> returnAfSendImpVOS, Map<String, List<OcBReturnAfSendItemVO>> mapItem, User user) {
        List<ValueHolderV14<OcBReturnAfSendImpVO>> valueHolderV14List = new ArrayList<>();
        for (OcBReturnAfSendImpVO ocBReturnAfSend : returnAfSendImpVOS){
            ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "seuccss");
            try {
                String sourceBillNo = ocBReturnAfSend.getSourceBillNo();
                JSONObject obj = new JSONObject();
                obj.put("ID", sourceBillNo);
                obj.put("isShowPii", true);
                ValueHolder valueHolder = getDetailService.getDetail(obj, user);
                if (valueHolder.get("code") == null || "-1".equals(valueHolder.get("code").toString())) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("行号：" + ocBReturnAfSend.getRowNum() +","+ valueHolder.get("message"));
                    ocBReturnAfSend.setDesc((String) valueHolder.get("message"));
                    v14.setData(ocBReturnAfSend);
                    valueHolderV14List.add(v14);
                    continue;
                }

                GetOrderResult orederInfo = (GetOrderResult) valueHolder.get("data");
                if (log.isDebugEnabled()) {
                    log.debug("OcBReturnAfSendController.importByPro导入数据orederInfo:{}",
                            JSON.toJSONString(orederInfo));
                }
                //封装保存新增参数
                JSONObject saveParam = packageSaveParam(orederInfo, ocBReturnAfSend, mapItem.get(sourceBillNo));
                if (log.isDebugEnabled()) {
                    log.debug("OcBReturnAfSendController.importByPro导入数据orederInfo:{}",
                            JSON.toJSONString(orederInfo));
                }
                ValueHolderV14 v141 = refundFormAfterDeliveryService.saveAfterDeliver(saveParam, user);
                if (!v141.isOK()){
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("行号：" + ocBReturnAfSend.getRowNum() +","+ v141.getMessage());
                    ocBReturnAfSend.setDesc(v141.getMessage());
                    v14.setData(ocBReturnAfSend);
                    valueHolderV14List.add(v14);
                    continue;
                }
            } catch (Exception e) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("行号：" + ocBReturnAfSend.getRowNum() +","+ Throwables.getStackTraceAsString(e));
                ocBReturnAfSend.setDesc(Throwables.getStackTraceAsString(e));
                v14.setData(ocBReturnAfSend);
            }
            valueHolderV14List.add(v14);
        }

        return valueHolderV14List;
    }

    /**
     * 封装额外退款单新增保存入参
     */
    private JSONObject packageSaveParam(GetOrderResult orederInfo, OcBReturnAfSendImpVO ocBReturnAfSendImpVO, List<OcBReturnAfSendItemVO> itemVOList) {
        JSONObject result = new JSONObject();
        JSONObject main = new JSONObject();
        JSONObject itemObject = new JSONObject();
        JSONArray itemArray = new JSONArray();
        result.put("objId", -1);

        GetDetailCmd getDetailCmd = (GetDetailCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), GetDetailCmd.class.getName(),
                "oms-fi", "1.0");
        String sourceBillNo = ocBReturnAfSendImpVO.getSourceBillNo();
        ValueHolderV14<List<OcBOrderItem>> v14 = getDetailCmd.getOrderItem(Long.parseLong(sourceBillNo));
        if (log.isDebugEnabled()) {
            log.debug("OcBReturnAfSendController.packageSaveParam.v14:{}",
                    JSON.toJSONString(v14));
        }
        if (v14.getCode() == ResultCode.FAIL) {
            throw new NDSException(v14.getMessage());
        }
        //主表退款金额
        BigDecimal amtReturnApply = BigDecimal.ZERO;
        List<OcBOrderItem> ocBOrderItems = v14.getData();
        //校验导入明细是否存在零售发货单明细中

        for (OcBReturnAfSendItemVO itemVO : itemVOList) {
            List<OcBOrderItem> collect = ocBOrderItems.stream().filter(s -> s.getPsCSkuEcode().equals(itemVO.getPsCSkuEcode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                throw new NDSException("订单编号为:" + sourceBillNo + "的零售发货单明细，不存在条码编码：" + itemVO.getPsCSkuEcode());
            }
            amtReturnApply = amtReturnApply.add(itemVO.getAmtReturn());
            itemObject.put("ID", collect.get(0).getId());
            itemObject.put("AMT_RETURN", itemVO.getAmtReturn());
            itemObject.put("FREIGHT", itemVO.getFreight());
            itemObject.put("QTY_IN", itemVO.getQty());
            itemArray.add(itemObject);
        }
        //查询退款分类
        ValueHolderV14<Map<OcBReturnType, List<OcBReturnTypeItem>>> mapValueHolderV14 = getDetailCmd.selectTurnTypeByEname(ocBReturnAfSendImpVO.getOcBReturnTypeEname());
        if (mapValueHolderV14.getCode() == ResultCode.FAIL) {
            throw new NDSException(mapValueHolderV14.getMessage());
        }
        Map<OcBReturnType, List<OcBReturnTypeItem>> data = mapValueHolderV14.getData();
        OcBReturnType ocBReturnType = new OcBReturnType();
        List<OcBReturnTypeItem> ocBReturnTypeItemList = new ArrayList<>();
        for (OcBReturnType key : data.keySet()) {
            ocBReturnType = key;
            ocBReturnTypeItemList = data.get(key);
        }
        //判断退款描述是否维护
        List<OcBReturnTypeItem> collect = ocBReturnTypeItemList.stream().filter(s -> s.getEname().equals(ocBReturnAfSendImpVO.getOcBReturnTypeItemEname())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            throw new NDSException("退款分类：" + ocBReturnType.getEname() + "没有维护退款描述:" + ocBReturnAfSendImpVO.getOcBReturnTypeItemEname());
        }
        /**
         * 封装参数
         */
        main.put("ID", -1);
        main.put("AMT_RETURN_APPLY", amtReturnApply);
        main.put("BILL_NO", "");
        main.put("BILL_TYPE", "1");
        main.put("CP_C_SHOP_ECODE", orederInfo.getCpCShopEcode());
        main.put("CP_C_SHOP_ID", orederInfo.getCpCShopId());
        main.put("CP_C_SHOP_TITLE", orederInfo.getCpCShopTitle());
        main.put("IMAGE", "");
        main.put("OC_B_RETURN_TYPE_ENAME", ocBReturnType.getEname());
        main.put("OC_B_RETURN_TYPE_ID", ocBReturnType.getId());
        main.put("OC_B_RETURN_TYPE_ITEM_ID", collect.get(0).getId());
        main.put("PAY_ACCOUNT", ocBReturnAfSendImpVO.getPayAccount());
        main.put("PAY_MODE", changePay(ocBReturnAfSendImpVO.getPayModeV()));
        main.put("REASON", ocBReturnAfSendImpVO.getReason());
        main.put("RECEIVER_NAME", ocBReturnAfSendImpVO.getReceiverName());
        main.put("REFUND_ORDER_SOURCE_TYPE", "1");
        main.put("remark", ocBReturnAfSendImpVO.getRemark());
        main.put("RESPONSIBLE_PARTY", ocBReturnAfSendImpVO.getResponsibleParty());
        main.put("RESPONSIBLE_PARTY_REMARK", ocBReturnAfSendImpVO.getResponsiblePartyRemark());
        main.put("RETURN_APPLY_TIME", ocBReturnAfSendImpVO.getReturnApplyTime());
        main.put("SELLER_REMARK", ocBReturnAfSendImpVO.getSellerRemark());
        main.put("SOURCE_BILL_NO", Long.parseLong(ocBReturnAfSendImpVO.getSourceBillNo()));
        main.put("TID", orederInfo.getTid());
        main.put("VIP_NICK", "");
        main.put("VIP_PHONE", "");
        main.put("VIP_NICK", "");


        result.put("AfSend", main);
        result.put("AfSendItem", itemArray);
        if (log.isDebugEnabled()) {
            log.debug("OcBReturnAfSendController.packageSaveParam.result:{}",
                    JSON.toJSONString(v14));
        }

        return result;
    }


    public String changePay(String string) {
        switch (string) {
            case "支付宝":
                return "1";

            case "微信":
                return "2";

            case "现金":
                return "3";

            case "备用金":
                return "4";

            case "财付通":
                return "5";

            case "银行":
                return "6";
            default:
                return null;
        }
    }


    public ValueHolderV14 exportError(List<OcBReturnAfSendImpVO> errorList, User user){

        ValueHolderV14 holderV14 = new ValueHolderV14();
        try{//列名
            String columnNames[] = {"主表行号", "错误原因"};
            List c = Lists.newArrayList(columnNames);
            // map中的key
            String keys[] = {"rowNum", "desc"};
            List k = Lists.newArrayList(keys);
            exportUtil.setEndpoint(this.endpoint);
            exportUtil.setAccessKeyId(this.accessKeyId);
            exportUtil.setAccessKeySecret(this.accessKeySecret);
            exportUtil.setBucketName(this.bucketName);
            if (StringUtils.isEmpty(timeout)) {
                //如果获取不到apllo配置参数，设置默认过期时间为30分钟
                timeout = "1800000";
            }
            exportUtil.setTimeout(this.timeout);
            Workbook hssfWorkbook = exportUtil.execute("额外退款单自定义导入", "额外退款单自定义导入错误", c, k, errorList);
            String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "额外退款单自定义导入错误信息", user, "OSS-Bucket/IMPORT/OC_B_RETURN_AF_SEND/");
            holderV14.setData(sdd);
            holderV14.setCode(ResultCode.SUCCESS);
            return holderV14;
        }catch (Exception e){
            log.info("=========>>>>>>[llf]额外退款单自定义导入错误信息!错误信息为:" + e.getMessage());
            e.printStackTrace();
            holderV14.setMessage(e.getMessage());
            return holderV14;
        }
    }

}
