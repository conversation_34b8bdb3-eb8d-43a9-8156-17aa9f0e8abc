package com.jackrain.nea.oc.oms.services;

import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpOrderReturnRelation;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/25 13:49
 * @desc
 */
@Component
@Slf4j
public class ReturnChangeCompareOcBorderService {
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBorderItemMapper;
    @Autowired
    private OcBReturnBuildService ocBReturnBuildService;
    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;


    public void compareOcBorder(List<OcBReturnOrderExchange> returnOrderExchanges, OcBReturnOrder returnOrder, User user) {
        if (CollectionUtils.isEmpty(returnOrderExchanges)) {
            return;
        }

        OcBOrder order = ocBOrderMapper.selectByReturnId(returnOrder.getId());
        if (Objects.isNull(order)) {
            throw new NDSException(String.format("根据[订单退单id:%s]查询原单信息为空!", returnOrder.getId()));
        }

        List<OcBOrderItem> orderItems = ocBorderItemMapper.selectAllOrderItem(order.getId());
        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }

        // 换货信息sku
        Set<Long> exchangeSkuIds = returnOrderExchanges.stream().map(OcBReturnOrderExchange::getPsCSkuId)
                .collect(Collectors.toSet());
       /* // 原单明细sku
        Set<Long> skuIds = Sets.newHashSet();
        // 原单明细
        Map<Long, OcBOrderItem> itemMap = Maps.newHashMap();
        for (OcBOrderItem orderItem : orderItems) {
            skuIds.add(orderItem.getPsCSkuId());
            itemMap.put(orderItem.getId(), orderItem);
        }*/
        Set<Long> skuIds = orderItems.stream().distinct().map(OcBOrderItem::getPsCSkuId)
                .collect(Collectors.toSet());
        //
        // 不包含就要去作废原单生成新的
        if (!skuIds.containsAll(exchangeSkuIds)) {
            boolean isHasInvalidated = false;
            // 调用生成换货订单服务
            IpOrderReturnRelation ocBOrderRelation = new IpOrderReturnRelation();
            //原单奶卡
            ocBReturnBuildService.buildOrderNaikaFromOrigOrder(order, ocBOrderRelation);
            List<OcBOrderItem> exchangeAfterItem = Lists.newArrayListWithExpectedSize(exchangeSkuIds.size());
            for (OcBReturnOrderExchange exchange : returnOrderExchanges) {
                if (!isHasInvalidated) {
                    //作废原单 生成新单
                    ValueHolderV14 v14 = doInvoildOutOrder(order, user);
                    if (v14.getCode() == ResultCode.SUCCESS) {
                        order.setId(null);
                        order.setBillNo(null);
                        order.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
                        BaseModelUtil.initialBaseModelSystemField(order, user);
                        ocBOrderRelation.setOcBOrder(order);
                        isHasInvalidated = true;
                    } else {
                        throw new NDSException("作废零售发货单失败!" + v14.getMessage());
                    }
                }
                OcBOrderItem ocBOrderItem = new OcBOrderItem();
                BeanUtils.copyProperties(orderItems.get(0), ocBOrderItem);
                ocBOrderItem.setReturnOrderId(returnOrder.getId());
                ocBOrderItem.setQty(exchange.getQtyExchange());
                ocBOrderItem.setTid(order.getTid());
                ocBOrderItem.setAdjustAmt(BigDecimal.ZERO);
                ocBOrderItem.setPsCProId(exchange.getPsCProId());
                ocBOrderItem.setPsCSkuId(exchange.getPsCSkuId());
                ocBOrderItem.setPsCProEname(exchange.getPsCProEname());
                ocBOrderItem.setPsCProEcode(exchange.getPsCProEcode());
                ocBOrderItem.setPsCSkuEcode(exchange.getPsCSkuEcode());
                ocBOrderItem.setPsCSkuEname(exchange.getPsCSkuEname());
                ocBOrderItem.setPsCClrId(exchange.getPsCClrId());
                ocBOrderItem.setPsCClrEcode(exchange.getPsCClrEcode());
                ocBOrderItem.setPsCClrEname(exchange.getPsCClrEname());
                ocBOrderItem.setPsCSizeEcode(exchange.getPsCSizeEcode());
                ocBOrderItem.setPsCSizeId(exchange.getPsCSizeId());
                ocBOrderItem.setPsCSizeEname(exchange.getPsCSizeEname());
                ocBOrderItem.setPrice(exchange.getPrice());
                ocBOrderItem.setPriceList(exchange.getPriceList());
                exchangeAfterItem.add(ocBOrderItem);
            }
            ocBOrderRelation.setOcBOrderItems(exchangeAfterItem);
            returnOrder.setTid(order.getSourceCode());
            ocBOrderRelation.setOcBReturnOrder(returnOrder);
            ValueHolderV14 result = ocBReturnBuildService.buildExchange(ocBOrderRelation, user);
            if (result != null && result.getCode() == ResultCode.FAIL) {
                throw new NDSException("生成退换货订单失败!" + result.getMessage());
            }
        }
    }


    /**
     * 订单作废调用作废逻辑单服务【这个方法调用的前提是已经在库存中心生成逻辑发货单了,如果没有生成逻辑发货单就用上面的方法】
     *
     * @param orderInfo 订单对象
     * @return ValueHolderV14
     */
    public ValueHolderV14 doInvoildOutOrder(OcBOrder orderInfo, User user) {
        ValueHolderV14 v14 = new ValueHolderV14<>();
        // 是否能直接作废原单
        if (this.checkCanVoidOrder(orderInfo)) {
            this.voidOrder(orderInfo, user, v14);
        }
        // 是否要执行反审核 ,之后再去执行作废订单
        else if (this.checkIsReverseAudit(orderInfo)) {
            OrderICheckRequest auditRequest = new OrderICheckRequest();
            auditRequest.setIds(new Long[]{orderInfo.getId()});
            v14 = ocBOrderTheAuditService.orderTheAudit(auditRequest, user,true);
            if (v14.getCode() == ResultCode.FAIL) {
                throw new NDSException("零售发货单取消失败，不允许修改换货明细!");
            }
            // 作废订单
            this.voidOrder(orderInfo, user, v14);
        } else {
            String message = "订单ID为 " + orderInfo.getId() + "的订单状态异常!!!";
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(message);
        }
        return v14;
    }

    /**
     * 作废订单
     *
     * @param orderInfo
     * @param user
     * @param v14
     * @return
     */
    public ValueHolderV14 voidOrder(OcBOrder orderInfo, User user, ValueHolderV14 v14) {
        //调用取消发货逻辑单服务
        if (orderInfo.getOrderStatus().equals(OmsOrderStatus.UNCONFIRMED.toInteger())) {
            ValueHolderV14 voidSgOutOrderResult = null;
//                    sgRpcService.invoildOutOrder(orderInfo, user);
            if (voidSgOutOrderResult.getCode() == ResultCode.FAIL) {
                String message = "orderID的订单ID为" + orderInfo.getId() + "调用取消发货逻辑单服务异常-->" + voidSgOutOrderResult.getMessage();
                log.error(message);
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(message);
                return v14;
            }
        }
        omsOrderService.updateOrderSystemVoid(orderInfo, user);
        v14.setCode(ResultCode.SUCCESS);
        v14.setMessage("success");
        return v14;
    }

    /**
     * 若零售发货单状态为“缺货”或者“待审核”，
     * 则取消原零售发货单，重新生成换货类型的零售发货单；
     *
     * @param orderInfo 订单信息
     * @return true - 可以作废；False-不可以
     */
    private boolean checkCanVoidOrder(OcBOrder orderInfo) {
        return (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderInfo.getOrderStatus())
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderInfo.getOrderStatus()));
    }


    /**
     * 判断“订单状态”是否反审核
     * 若零售发货单状态为已审核或者配货中，则进行反审核，
     *
     * @param orderInfo 订单信息
     * @return true - 可以反审核；False-不可以
     */
    private boolean checkIsReverseAudit(OcBOrder orderInfo) {
        return (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderInfo.getOrderStatus())
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderInfo.getOrderStatus()));
    }
}
