package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBRefundBatchMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.model.enums.MatchingSate;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatus;
import com.jackrain.nea.oc.oms.model.table.OcBRefundBatch;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.SgStockAdjustmentService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 退换货作废按钮服务
 *
 * @author: 夏继超
 * @since: 2019/3/25
 * create at : 2019/3/25 16:56
 */
@Slf4j
@Component
//@MapperScan("com.jackrain.nea.oc.oms.mapper")
public class ReturnCancelService {
    @Autowired
    OcBRefundInMapper ocBRefundInMapper;
    @Autowired
    OcBRefundInProductItemMapper itemMapper;
    @Autowired
    OcBRefundBatchMapper batchMapper;
    @Autowired
    SgRpcService sgRpcService;
    @Autowired
    SgStockAdjustmentService adjustmentService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    /**
     * 退换入库单作废服务
     *
     * @param jsonObject 传入的参数
     * @param user       当前用户
     * @return 返回的结果
     */
    public ValueHolderV14 returnCancel(JSONObject jsonObject, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        //退货入库单id
        Object id = jsonObject.get("ids");
        if (id != null) {
            OcBRefundIn ocBRefundIn = ocBRefundInMapper.selectById(Long.valueOf(id.toString()));
            if (ocBRefundIn != null) {
                if (ocBRefundIn.getInStatus() != null) {
                    if (ocBRefundIn.getInStatus().equals(ReturnStatus.WAREHOUSING_AND_SCRAP.toInteger())) {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(Resources.getMessage("已经作废，不能再次作废", user.getLocale()));
                        return vh;
                    }
                    List<OcBRefundInProductItem> items = itemMapper.selectForItem(id);
                    BigDecimal num = new BigDecimal(0);
                    if (items.size() > 0) {
                        for (OcBRefundInProductItem item : items) {
                            //1.判断传入的退货入库单明细的条码是否存在退单编号，若有退单编号，则返回提示：“退货入库单对应的退货单已经入库，不能作废”，服务结束；
                            BigDecimal qty = item.getQty();
                            num = num.add(qty);
                            if (item.getOcBReturnOrderId() != null) {
                                vh.setCode(ResultCode.FAIL);
                                vh.setMessage(Resources.getMessage("退货入库单对应的退货单已经入库，不能作废", user.getLocale()));
                                return vh;
                            }
                        }
                        //生成负向调整单调用别人的服务
                     /* 2.若不存在退单编号，则生成一张负向调整单
                    a)调整仓库为退货入库单头表的入库仓库
                    b)调整条码为入库单明细发出条码（如果有实收条码则为实收条码）
                    c)调整数量为入库单明细数量的负向数量
                    d)调整单的状态为已完成，并减去调整仓库对应条码的库存数量。*/
                        //3.更新退货批次表中的数量值，减去本次作废的退货入库单明细的数量之和
                        try {
                            boolean b = adjustmentService.addStockAdjustmen(ocBRefundIn, items, user);
                            if (b == false) {
                                throw new NDSException("生成负向调整单失败");
                            }
                        } catch (Exception e) {
                            log.error(LogUtil.format("调用负向调整服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                            vh.setCode(ResultCode.FAIL);
                            vh.setMessage(Resources.getMessage("调用负向调整服务异常", user.getLocale()));
                            return vh;
                        }
                    } else {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(Resources.getMessage("退货入库单对应的明细不存在，你录入在重试", user.getLocale()));
                    }


                    Long ocBRefundBatchId = ocBRefundIn.getOcBRefundBatchId();
                    OcBRefundBatch ocBRefundBatch = batchMapper.selectById(ocBRefundBatchId);
                    ocBRefundBatch.setQty(ocBRefundBatch.getQty().subtract(num));
                    //更新退货批次数量值
                    try {
                        batchMapper.updateById(ocBRefundBatch);
                    } catch (Exception e) {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(Resources.getMessage("更新退货批次数量值异常，请重试", user.getLocale()));
                    }
                    //推退货批次表中的数量值到es
                    /*try {
                        Boolean aBoolean = SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_BATCH_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_BATCH_TYPE_NAME, ocBRefundBatch, ocBRefundBatch.getId());
                        if (!aBoolean) {
                            throw new NDSException(Resources.getMessage("更新退货批次推送ES失败!", user.getLocale()));
                        }
                    } catch (Exception e) {
                        log.debug("更新退货批次推送ES失败: " + e.getMessage());
                        throw new NDSException(Resources.getMessage("更新退货批次推送ES失败!", user.getLocale()));
                    }*/

                    //4.更新退货入库单的状态为入库作废，返回作废成功。
                    ocBRefundIn.setInStatus((ReturnStatus.WAREHOUSING_AND_SCRAP.toInteger()));
                    try {
                        ocBRefundInMapper.updateById(ocBRefundIn);
                    } catch (Exception e) {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(Resources.getMessage("更新退货入库状态失败", user.getLocale()));
                    }

                    vh.setCode(ResultCode.SUCCESS);
                    vh.setMessage(Resources.getMessage("作废成功", user.getLocale()));
                } else {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("入库状态不能为空"));
                    return vh;
                }
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("出入的退货入库id有误", user.getLocale()));
            }


        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("请传入退货入库单id", user.getLocale()));
        }

        return vh;
    }

    /**
     * 作废按钮服务
     *
     * @param object 传入的参数
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder execute(JSONObject object, User user) throws NDSException {
        ValueHolder vh = new ValueHolder();
        //1.若未选中退货入库单数据，点击【作废】按钮，返回提示：“未选择退货入库单，请选择一条数据后在操作”
        JSONArray ids1 = object.getJSONArray("ids");
        if (ids1.size() == 0 && ids1 != null) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("未选择退货入库单，请选择一条数据后在操作！", user.getLocale()));
            return vh;
        }
        //2.不允许批量选择退货入库单进行作废，如果退货入库单超过1条，则返回提示：“不允许批量选择退货入库单进行作废”
        if (ids1.size() > 1) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("不允许批量选择退货入库单进行作废！", user.getLocale()));
            return vh;
        }
        Object id = ids1.get(0);
        //3.选择一条退货入库单，判断退货入库单匹配状态，如果为“全部匹配，部分匹配”，则不允许选择，提示：“此退货入库单已经全部匹配入库”；如果为其他状态：“未匹配”，则调用退货入库单作废服务。
        OcBRefundIn ocBRefundIn = ocBRefundInMapper.selectById(Long.valueOf(id.toString()));
        Integer matchStatus = ocBRefundIn.getMatchStatus();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(Long.valueOf(id.toString()));
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        //0未匹配，1部分匹配，2全部匹配
        try {
            //添加锁单
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    if (matchStatus == null) {
                        vh.put("code", ResultCode.FAIL);
                        vh.put("message", Resources.getMessage("匹配状态不能为空！", user.getLocale()));
                        return vh;
                    }
                    //如果为“全部匹配”，“部分匹配”，则不允许选择，提示：“此退货入库单已经匹配入库”
                    if (MatchingSate.MATCH_ALL.toInteger() == matchStatus || MatchingSate.PARTIAL_MATCHING.toInteger() == matchStatus) {
                        vh.put("code", ResultCode.FAIL);
                        vh.put("message", Resources.getMessage("此退货入库单已经全部匹配入库！", user.getLocale()));
                    } else if (MatchingSate.UNMATCHED.toInteger() == matchStatus) {
                        //4.退货入库单作废服务返回作废成功，则提示“作废成功”，作废失败，则提示返回失败信息。
                        JSONObject obj = new JSONObject();
                        obj.put("ids", ids1.get(0));
                        try {
                            ValueHolderV14 valueHolderV14 = returnCancel(obj, user);
                            val code = valueHolderV14.getCode();
                            if (code != ResultCode.FAIL) {
                                vh.put("code", ResultCode.SUCCESS);
                                vh.put("message", Resources.getMessage("作废成功！", user.getLocale()));
                            } else {
                                vh.put("code", ResultCode.FAIL);
                                vh.put("message", Resources.getMessage(valueHolderV14.getMessage(), user.getLocale()));
                            }
                        } catch (Exception e) {
                            vh.put("code", ResultCode.FAIL);
                            vh.put("message", Resources.getMessage("调用作废服务异常!" + e.getMessage(), user.getLocale()));
                        }
                    }
                } else {
                    throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
                }
            } catch (Exception e) {
                throw new NDSException(Resources.getMessage("订单更新锁单错误！", user.getLocale()));
            } finally {
                redisLock.unlock();
            }
        } catch (Exception e) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("调用作废服务异常！", user.getLocale()));
        }
        return vh;
    }

}
