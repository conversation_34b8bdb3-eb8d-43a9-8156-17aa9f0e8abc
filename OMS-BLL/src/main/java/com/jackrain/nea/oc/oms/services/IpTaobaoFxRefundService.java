package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.TaobaoFxReturnTransferUtil;
import com.jackrain.nea.rpc.AcRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 周琳胜
 * @since: 2019/7/15
 * create at : 2019/7/15 16:55
 */
@Slf4j
@Component
public class IpTaobaoFxRefundService {

    @Autowired
    IpBTaobaoFxRefundMapper ipBTaobaoFxRefundMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    protected OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private TaobaoFxReturnTransferUtil taobaoFxReturnTransferUtil;

    @Autowired
    private OmsReturnOrderService omsReturnOrderService;

    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    @Autowired
    private AcRpcService acRpcService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;

    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;


    /**
     * @param orderNo 平台退款单号
     * @return 封装参数
     */
    public IpTaobaoFxRefundRelation selectTaobaoFxRefundRelation(String orderNo) {
        IpTaobaoFxRefundRelation ipTaobaoFxRefundRelation = null;
        IpBTaobaoFxRefund taobaoFxRefund = ipBTaobaoFxRefundMapper.selectTaobaoFxRefundByRefundId(orderNo);
        if (null != taobaoFxRefund) {
            ipTaobaoFxRefundRelation = new IpTaobaoFxRefundRelation();
            ipTaobaoFxRefundRelation.setTaobaoFxRefund(taobaoFxRefund);
            /**
             *  2)根据中间表的purchase_order_id + sub_order_id 从渠道订单的找跟SOURCE_CODE+OID相同的订单
             */
            //所有订单id信息
            //liqb 更改ooid类型从Long类型改成String类型
            String subOrderId = String.valueOf(taobaoFxRefund.getSubOrderId());
            Long purchaseOrderId = taobaoFxRefund.getPurchaseOrderId();

            //    List<Long> orderIds = ES4Order.getIdsBySourceCode(purchaseOrderId);
            List<Long> orderIds = GSI4Order.getIdListBySourceCode(String.valueOf(purchaseOrderId));

            if (CollectionUtils.isEmpty(orderIds)) {
                return ipTaobaoFxRefundRelation;
            }
            //去重
            orderIds = orderIds.stream().distinct().collect(Collectors.toList());


            /**
             *
             * es获取分库键 + id 原单明细
             * 3)通过分库键及subOrderId去DRDS获取订单明细列表L0
             */
            //liqb 更改ooid类型从Long类型改成String类型
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListByOoid(String.valueOf(subOrderId), orderIds);
            if (CollectionUtils.isNotEmpty(orderItems)) {
                /**
                 * 4)通过oc_b_order_id获取订单信息
                 */
                List<OcBOrder> orderList = ocBOrderMapper.selectOrderListByIds(orderIds);
                if (CollectionUtils.isNotEmpty(orderList)) {
                    /**
                     * 5)过滤掉订单信息订单状态是作废和取消状态，获得有效订单L1
                     */
                    List<OcBOrder> ocBOrderList = orderList.stream().filter(ocBorder -> (
                                    !ocBorder.getOrderStatus().equals(OmsOrderStatus.CANCELLED.toInteger())
                                            && !ocBorder.getOrderStatus().equals(OmsOrderStatus.SYS_VOID.toInteger())
                            )
                    ).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(ocBOrderList)) {
                        ipTaobaoFxRefundRelation.setEffectiveOrder(true);
                    }
                    /**
                     *  * 6) 通过有效订单的id过滤L0,得到有效订单明细L2；
                     */
                    List<OcBOrderItem> ocBOrderItems = null;
                    List<OcBOrderItem> orderGifts = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(ocBOrderList)) {
                        ocBOrderItems = new ArrayList<>();
                        for (OcBOrder ocO : ocBOrderList) {
                            ocBOrderItems.addAll(orderItems.stream().filter(item -> (
                                            ocO.getId().equals(item.getOcBOrderId())
                                    )
                            ).collect(Collectors.toList()));
                            Integer isGift = ocO.getIsHasgift();
                            //判断有效订单是否有赠品
                            if (isGift.equals(1)) {
                                List<OcBOrderItem> orderGiftsTemp =
                                        ocBOrderItemMapper.selectOrderGiftListByOrderId(ocO.getId());
                                if (CollectionUtils.isNotEmpty(orderGiftsTemp)) {
                                    orderGifts.addAll(orderGiftsTemp);
                                }
                            }
                        }
                        if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                            OcBOrderItem ocBOrderItem = ocBOrderItems.get(0);
                            OcBOrder ocBOrder = ocBOrderMapper.selectById(ocBOrderItem.getOcBOrderId());
                            ipTaobaoFxRefundRelation.setOcBOrder(ocBOrder);
                            ipTaobaoFxRefundRelation.setOcBOrderItems(ocBOrderItems);
                        }
                        /**
                         *  * 8）通过L1获取所有赠品集合L3（并且OID一致或OID=null）
                         */
                        if (CollectionUtils.isNotEmpty(orderGifts)) {
                            //liqb 更改ooid类型从Long类型改成String类型
                            orderGifts = orderGifts.stream().filter(item -> (
                                    subOrderId.equals(item.getOoid()) || item.getOoid() == null/*item.getOoid() == null || item.getOoid() <= 0L*/
                            )).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(orderGifts)) {
                                ipTaobaoFxRefundRelation.setOcBOrderGifts(orderGifts);
                            }
                        }

                    }

                }
            }
        }

        return ipTaobaoFxRefundRelation;
    }

    /**
     * @param transferOrderStatus 装换状态
     * @return 状态是否更新成功
     */
    public boolean updateRefundIsTrans(TransferOrderStatus transferOrderStatus, IpBTaobaoFxRefund taobaoFxRefund) {
        IpTaobaoFxRefundService bean = ApplicationContextHandle.getBean(IpTaobaoFxRefundService.class);
        boolean flag = bean.updateReturnOrder(transferOrderStatus.toInteger(),
                "", taobaoFxRefund);
        return flag;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateReturnOrder(int istrans, String remark, IpBTaobaoFxRefund taobaoFxRefund) {
        try {
            IpBTaobaoFxRefund newTaobaoFxRefund = new IpBTaobaoFxRefund();
            Long suborderid = taobaoFxRefund.getSubOrderId();
            newTaobaoFxRefund.setIstrans(istrans);
            newTaobaoFxRefund.setSysremark(remark);
            newTaobaoFxRefund.setTransdate(new Date());
            UpdateWrapper<IpBTaobaoFxRefund> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("sub_order_id", suborderid);
            int i = ipBTaobaoFxRefundMapper.update(newTaobaoFxRefund, updateWrapper);
            //推送es
            if (i > 0) {
//                IpBTaobaoFxRefund ipBTaobaoFxRefund = ipBTaobaoFxRefundMapper.selectTaobaoFxRefundBySubOrderId(taobaoFxRefund.getSubOrderId().toString());
//                SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOFXREFUND,
//                        TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOFXREFUND,
//                        ipBTaobaoFxRefund, taobaoFxRefund.getId());
                return true;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新退货中间表失败,RefundId:{}", taobaoFxRefund.getXrefundId(), e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * 出现异常时更新状态并插入日志
     *
     * @param taobaoFxRefund
     * @return
     */
    public boolean updateRefundIsTransError(IpBTaobaoFxRefund taobaoFxRefund, String error) {
        String sysRemark = SysNotesConstant.SYS_REMARK0;
        //异常信息超过500 截取500
        String str = sysRemark + error;
        if (str.length() > 255) {
            str = str.substring(0, 254);
        }
        IpTaobaoFxRefundService bean = ApplicationContextHandle.getBean(IpTaobaoFxRefundService.class);
        boolean flag = bean.updateReturnOrder(4,
                str, taobaoFxRefund);
        return flag;
    }

    /**
     * 不存在原单
     */
    public boolean noOriginalOrder(IpBTaobaoFxRefund taobaoFxRefund,
                                   OcBOrder ocBOrder, boolean isEffectiveOrder) {
        IpTaobaoFxRefundService bean = ApplicationContextHandle.getBean(IpTaobaoFxRefundService.class);
        if (ocBOrder == null) {
            if (isEffectiveOrder) {
                String remark = SysNotesConstant.SYS_REMARK4;
                bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoFxRefund);
                return true;
            }
            Date date = new Date();
            //判断退单时间是否超过三天
            Date created = taobaoFxRefund.getRefundCreateTime();
            Long threeDays = 3 * 24 * 60 * 60 * 1000L + created.getTime();
            if (threeDays < date.getTime()) {

                String remark = SysNotesConstant.SYS_REMARK2;
                bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoFxRefund);
                return true;
            }
            //找不到对应的原单,更新转换状态为 0 ,添加系统备注
            String remark = SysNotesConstant.SYS_REMARK1;
            bean.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                    remark, taobaoFxRefund);
            return true;
        }
        return false;
    }

    /**
     * @param orderInfo
     * @param operateUser
     * @return true 退单发货后转换服务 false 仅退款应收调整单服务
     */
    public Boolean orderStatusHasGoodReturnStatusAfter(IpTaobaoFxRefundRelation orderInfo,
                                                       User operateUser) throws IOException {
        IpBTaobaoFxRefund taobaoFxRefund = orderInfo.getTaobaoFxRefund();
        Integer hasGoodReturn = taobaoFxRefund.getIsReturnGoods();
        Long status = taobaoFxRefund.getRefundStatus(); //退单中间表的退单状态
        if (TaobaoReturnOrderExt.RefundStatus.CLOSED.toLong().equals(status)) {
            //走退单关闭服务
            this.refundOrderClose(operateUser, taobaoFxRefund);
            return null;
        }
        //【1，买家申请退款，等待卖家同意】、【6，卖家拒绝退款】，则不进行处理，
        // 更新退单中间表的退单转换状态为已转换
        if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.toLong().equals(status)
                || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.toLong().equals(status)) {
            String remark = SysNotesConstant.SYS_REMARK27;
            IpTaobaoFxRefundService bean = ApplicationContextHandle.getBean(IpTaobaoFxRefundService.class);
            bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, taobaoFxRefund);
            return null;
        }
        //【2，卖家已经同意退款，等待买家退货】【3，买家已经退货，等待卖家确认收货】【5，退款成功】【10】【12】
        if (TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.toLong().equals(status)
                || TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.toLong().equals(status)
                || TaobaoReturnOrderExt.RefundStatus.SUCCESS.toLong().equals(status)
                || TaobaoReturnOrderExt.RefundStatus.WAIT_RETURN_MONEY.toLong().equals(status)
                || 10L == status) {
            if (TaobaoReturnOrderExt.HasGoodReturnStatus.YES_RETURN.getCode().equals(hasGoodReturn)) {
                //判断退款的申请金额是否小于30元
                BigDecimal refundFee = taobaoFxRefund.getRefundFee(); //退款金额
                if (refundFee.compareTo(new BigDecimal(30)) > 0) {
                    //【退单发货后转换服务】
                    return true;
                } else {
                    //小于等于30元，则判断退单中间表的申请金额与原单明细的金额是否一致
                    //true 下一步转换 【退单发货后转换服务】false【仅退款应收调整单服务】
                    return taobaoFxReturnTransferUtil.isAmtBalanceNew(orderInfo);
                }
            }
            if (TaobaoReturnOrderExt.HasGoodReturnStatus.NO_RETURN.getCode()
                    .equals(hasGoodReturn)) {
                //判断金额是否相等
                //true 下一步转换 【退单发货后转换服务】false【仅退款应收调整单服务】
                return taobaoFxReturnTransferUtil.isAmtBalanceNew(orderInfo);
            }
        }
        return null;
    }

    /**
     * 订单关闭服务
     * <p>
     * 查找退换货管理中的退货单，如果不存在退货单，则直接更新退单转换状态为已转换，如果存在，
     * 则判断退货单的状态，当退货单状态为“等待退货入库”时，直接取消退货单，更新退单转换状态为已转换，
     * 如果不是，则不进行处理，更新退单转换状态为已转换，添加系统备注：“退货单的状态不满足，不能取消退货单”
     */
    public void refundOrderClose(User operateUser, IpBTaobaoFxRefund taobaoFxRefund) throws IOException {
        IpTaobaoFxRefundService bean = ApplicationContextHandle.getBean(IpTaobaoFxRefundService.class);
        // 是否存在退款单号
        String refundId = taobaoFxRefund.getSubOrderId().toString();
        //    Long returnId = omsReturnOrderService.isExistReturnOrder(refundId);
        List<Long> existReturnOrder = omsReturnOrderService.isExistReturnOrder(refundId);
        if (CollectionUtils.isNotEmpty(existReturnOrder)) {
            //存在退款商品
            OcBReturnOrder ocBReturnOrder = omsReturnOrderService.selectReturnOrderById(existReturnOrder.get(0));
            if (ocBReturnOrder != null) {
                //获取退款表的退款状态
                //退换货状态,20等待退货入库，30等待售后确认，50完成，60取消',
                Integer returnStatus = ocBReturnOrder.getReturnStatus();
                //TaobaoReturnOrderExt.ReturnStatus.WAIT_RETURN_LIBRARY.getCode()
                if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus)) {
                    //更新状态为退货取消
//                    ocBReturnOrder.setReturnStatus(TaobaoReturnOrderExt.ReturnStatus.RETURN_CANCEL.getCode());
//                    //推送es
//                    returnOrderTransferUtil.updateOperator(ocBReturnOrder, operateUser);
//                    omsReturnOrderService.updateOcBReturnOrder(ocBReturnOrder);
                    JSONObject jsonObject = new JSONObject();
                    JSONArray jsonArray = new JSONArray();
                    jsonArray.add(existReturnOrder.get(0));
                    jsonObject.put("ids", jsonArray);
                    ValueHolderV14 holderV14 = ocCancelChangingOrRefundService.orRefundService(jsonObject, operateUser, Boolean.FALSE);
                    int code = Tools.getInt(holderV14.getCode(), -1);
                    if (code == 0) {
                        //更新中间表转换转状态
                        bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                " ", taobaoFxRefund);
                        //插入日志
                        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
                        ocBReturnOrderLog.setLogMessage(SysNotesConstant.CANCEL_LOG_MESSAGE);
                        ocBReturnOrderLog.setLogType(SysNotesConstant.CANCEL_LOG_TYPE);
                        ocBReturnOrderLog.setOcBReturnOrderId(existReturnOrder.get(0));
                        returnOrderTransferUtil.saveSysLog(ocBReturnOrderLog, operateUser);
                        ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
                    } else {
                        Date date = new Date();
                        //判断退单时间是否超过三天
                        Date created = taobaoFxRefund.getCreationdate();
                        Long threeDays = 3 * 24 * 60 * 60 * 1000L + created.getTime();
                        String remark = holderV14.getMessage();
                        if (threeDays < date.getTime()) {
                            bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                    remark, taobaoFxRefund);
                            return;
                        }
                        bean.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                                remark, taobaoFxRefund);
                    }
                } else {
                    String remark = SysNotesConstant.SYS_REMARK7;
                    bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, taobaoFxRefund);
                }
            } else {
                String remark = SysNotesConstant.SYS_REMARK33;
                bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoFxRefund);
            }
        } else {
            String remark = "不存在退货单，标记为已转换。";
            bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, taobaoFxRefund);
        }
    }

    /**
     * 应收调整单服务
     *
     * @param orderInfo
     */
    public boolean receivablesAdjustment(IpTaobaoFxRefundRelation orderInfo, User user) {
        IpTaobaoFxRefundService bean = ApplicationContextHandle.getBean(IpTaobaoFxRefundService.class);
        OcBOrder ocBOrder = orderInfo.getOcBOrder();
        IpBTaobaoFxRefund taobaoFxRefund = orderInfo.getTaobaoFxRefund();
        List<OcBOrderItem> ocBOrderItems = orderInfo.getOcBOrderItems();
        JSONObject json = new JSONObject();
        JSONObject object = new JSONObject();
        object.put("CP_C_PLATFORM_ID", ocBOrder.getPlatform());
        object.put("ORDER_PRICE", ocBOrder.getOrderAmt()); //订单总额
        object.put("TID", orderInfo.getTaobaoFxRefund().getSubOrderId()); // todo TID
        object.put("ORDER_TYPE", ocBOrder.getOrderType());
        object.put("ORDER_FROM", ocBOrder.getOrderSource()); //订单来源
        object.put("CP_C_SHOP_TITLE", ocBOrder.getCpCShopTitle());
        object.put("PAY_TIME", ocBOrder.getPayTime());
        object.put("CUSTOMER_NICK", ocBOrder.getUserNick());
        object.put("CP_C_SHOP_ID", ocBOrder.getCpCShopId());
        object.put("CUSTOMER_NAME", ocBOrder.getReceiverName()); //收货人姓名
        object.put("CUSTOMER_TEL", ocBOrder.getReceiverMobile());
        object.put("ALIPAY_ACCOUNT", ocBOrder.getBuyerAlipayNo());//buyer_alipay_no
        object.put("SELLERMEMO", ocBOrder.getSellerMemo());
        object.put("AMT", taobaoFxRefund.getRefundFee());
        object.put("REASON", "退差价"); //约定写死
        object.put("REFUND_TIME", taobaoFxRefund.getCreationdate());
        object.put("REMARK", taobaoFxRefund.getSysremark());
        object.put("REFUND_NO", taobaoFxRefund.getXrefundId());
        JSONArray jsonArray = new JSONArray();
        ocBOrderItems.forEach(p -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("OID", p.getOoid());
            jsonObject.put("ORDER_NO", ocBOrder.getBillNo());
            jsonObject.put("PS_C_PRO_ID", p.getPsCProId());
            jsonObject.put("PS_C_PRO_ECODE", p.getPsCProEcode());
            jsonObject.put("PS_C_PRO_ENAME", p.getPsCProEname());
            jsonObject.put("PS_C_SKU_ID", p.getPsCSkuId());
            jsonObject.put("PS_C_SKU_ECODE", p.getPsCSkuEcode());
            jsonObject.put("PS_C_CLR_ID", p.getPsCClrId());
            jsonObject.put("PS_C_CLR_ECODE", p.getPsCClrEcode());
            jsonObject.put("PS_C_CLR_ENAME", p.getPsCClrEname());
            jsonObject.put("PS_C_SIZE_ID", p.getPsCSizeId());
            jsonObject.put("PS_C_SIZE_ECODE", p.getPsCSizeEcode());
            jsonObject.put("PS_C_SIZE_ENAME", p.getPsCSizeEname());
            jsonObject.put("TRUE_PRICE", p.getRealAmt()); //单行实际成交金额
            jsonObject.put("QTY", taobaoFxRefund.getNum());
            jsonObject.put("STANDARD_PRICE", p.getPriceList()); //标准价
            jsonObject.put("BARCODE", p.getBarcode());//国标码 ljp add
            jsonArray.add(jsonObject);
        });
        json.put("AC_F_RECEIVABLES_ADJUSTMENT", object);
        json.put("AC_F_RECEIVABLES_ADJUSTMENT_ITEM", jsonArray);
        try {
            //    log.info(this.getClass().getName() + taobaoFxRefund.getSubOrderId() + " 仅退款应收调整单服务入参:{}", json);
            Long isgenincidental = taobaoFxRefund.getReserveBigint01(); // todo
            if (isgenincidental != null && isgenincidental.equals(1L)) {
                //已生成应收调整单
                //返回1时   当前已生成 更新已转换  添加备注
                String remark = SysNotesConstant.SYS_REMARK32;

                boolean flag = bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), remark
                        , taobaoFxRefund);
                return flag;
            }
            ValueHolderV14 holder = acRpcService.getReceivableAdjustmentData(json, user);
            int code = Tools.getInt(holder.getCode(), -1);
            if (code == -1) {
                throw new NDSException("仅退款应收调整单服务调用失败" + holder.getMessage());
            } else {
                // 更新中间表生成应收调整单状态为1
                IpBTaobaoFxRefund ipBTaobaoFxRefund = new IpBTaobaoFxRefund();
                ipBTaobaoFxRefund.setReserveBigint01(1L);
                ipBTaobaoFxRefund.setSubOrderId(taobaoFxRefund.getSubOrderId());
                this.updateTaobaoFxRefundOrder(ipBTaobaoFxRefund);
                //更新状态为已转化
                String remark = SysNotesConstant.SYS_REMARK28;
                boolean flag = bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoFxRefund);
                return flag;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 仅退款应收调整单服务调用失败", e);
            throw new NDSException(e);
        }
    }

    public boolean updateTaobaoFxRefundOrder(IpBTaobaoFxRefund taobaoFxRefund) {
        try {
            Long suborderid = taobaoFxRefund.getSubOrderId();
            taobaoFxRefund.setSubOrderId(null);
            UpdateWrapper<IpBTaobaoFxRefund> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("sub_order_id", suborderid);
            int i = ipBTaobaoFxRefundMapper.update(taobaoFxRefund, updateWrapper);
            taobaoFxRefund.setSubOrderId(suborderid);
            //推送es
            if (i > 0) {
//                IpBTaobaoFxRefund ipBTaobaoFxRefund = ipBTaobaoFxRefundMapper.selectTaobaoFxRefundByRefundId(taobaoFxRefund.getSubOrderId().toString());
//                SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOFXREFUND,
//                        TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOFXREFUND,
//                        ipBTaobaoFxRefund, ipBTaobaoFxRefund.getId());
                return true;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新退货中间表失败,RefundId:{}", taobaoFxRefund.getSubOrderId(), e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * 生成退单成功之后更新订单的退货状态为 退货中
     *
     * @param ocBOrder
     */
    public void updateOrderReturnStatus(OcBOrder ocBOrder, IpBTaobaoFxRefund taobaofxRefund) {
        IpTaobaoFxRefundService bean = ApplicationContextHandle.getBean(IpTaobaoFxRefundService.class);
        String sysRemark = SysNotesConstant.SYS_REMARK29;
        boolean b = bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), sysRemark, taobaofxRefund);
        if (b) {
            OcBOrder order = new OcBOrder();
            order.setReturnStatus(1); //退货中
            order.setId(ocBOrder.getId());
            omsOrderService.updateOrderInfo(order);
        }
    }

    /**
     * 如果存在，则更新退货单中的物流公司和物流单号，同时插入退换货单的更新日志，修改退单的状态为已转换，
     * 添加系统备注“退货单中已经存在，修改退单状态为已转换”
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveExistReturnOrder(Long id, IpTaobaoFxRefundRelation
            taobaoFxRefundRelation, User operateUser) throws IOException {
        OcBOrder ocBOrder = taobaoFxRefundRelation.getOcBOrder();
        IpBTaobaoFxRefund ipBTaobaoFxRefund = taobaoFxRefundRelation.getTaobaoFxRefund();
        OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectById(id);
        // 排除已取消和已完成的退换货订单
        if (!returnOrder.getReturnStatus().equals(ReturnStatusEnum.CANCLE.getVal()) && !returnOrder.getReturnStatus().equals(ReturnStatusEnum.COMPLETION.getVal())) {
            // todo 物流单号
            String logisticsCode = returnOrder.getLogisticsCode();
            String cpCLogisticsEname = returnOrder.getCpCLogisticsEname();
            String sid = ocBOrder.getExpresscode();
            String companyName = ocBOrder.getCpCLogisticsEname();
            if (StringUtils.isNotEmpty(sid) && StringUtils.isNotEmpty(companyName)
                    && StringUtils.isEmpty(logisticsCode) && StringUtils.isEmpty(cpCLogisticsEname)) {
                returnOrder.setLogisticsCode(sid);
                returnOrder.setCpCLogisticsEname(companyName);
                returnOrderTransferUtil.setLogisticInfo(returnOrder, companyName);
                this.updateOcBReturnOrder(returnOrder);
                //修改退单中间表状态更新系统备注
                String remark = SysNotesConstant.SYS_REMARK5;
                this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBTaobaoFxRefund);
                //增加系统日志
                omsReturnOrderService.saveAddOrderReturnLog(id, SysNotesConstant.UPDATE_LOG_MESSAGE,
                        SysNotesConstant.UPDATE_LOG_TYPE, operateUser);
                // 更新订单主表为退货中
                OcBOrder order = new OcBOrder();
                order.setReturnStatus(1); //退货中
                order.setId(ocBOrder.getId());
                omsOrderService.updateOrderInfo(order);

            } else {
                String remark = SysNotesConstant.SYS_REMARK5;
                this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBTaobaoFxRefund);
            }
        } else {
            //修改退单中间表状态更新系统备注
            String remark = "退换货订单存在，且退货状态为取消";
            this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBTaobaoFxRefund);
        }

    }

    /**
     * 更新主表信息
     *
     * @param ocBReturnOrder
     * @return
     * @throws IOException
     */
    public boolean updateOcBReturnOrder(OcBReturnOrder ocBReturnOrder) throws IOException {
        int i = ocBReturnOrderMapper.updateById(ocBReturnOrder);
        //@20201118 去除手推ES代码
        //ES4ReturnOrder.updateReturnOrderById(ocBReturnOrder);
        return i > 0;
    }


    /**
     * 发货前
     * 原单状态为 待分配50、待审核1、缺货2、已审核3、待传WMS中21、配货中4
     */
    public boolean orderStatusHasGoodReturnStatusFront(IpBTaobaoFxRefund taobaoFxRefund) {
        Integer hasGoodReturn = taobaoFxRefund.getIsReturnGoods();
        IpTaobaoFxRefundService bean = ApplicationContextHandle.getBean(IpTaobaoFxRefundService.class);
        if (TaobaoReturnOrderExt.HasGoodReturnStatus.YES_RETURN.getCode()
                .equals(hasGoodReturn)) {
            //，则程序结束，更新退单中间表的退单转换状态为已转换，
            // 添加系统备注：“原始订单未发货，不能转换，请发货后处理
            String remark = SysNotesConstant.SYS_REMARK16;
            bean.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, taobaoFxRefund);
            return false;
        }
        //  调用【发货前退退款转换服务】
        return TaobaoReturnOrderExt.HasGoodReturnStatus.NO_RETURN.getCode()
                .equals(hasGoodReturn);

    }
}
