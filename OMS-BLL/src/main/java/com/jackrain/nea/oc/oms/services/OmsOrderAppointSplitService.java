package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.QueryOrderListResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.spiltorder.OmsOrderManualSplitNewService;
import com.jackrain.nea.oc.oms.util.*;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sg.service.AddAndVoidStockListService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OrderTagUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>指定商品订单拆分服务</>
 * <p>xu
 * * @author: 胡林洋
 * * @since: 2020/3/11
 * * create at : 2020/3/11 16:3
 */
@Component
@Slf4j
public class OmsOrderAppointSplitService {

    @Autowired
    OcBOrderListQueryService listQueryService;

    @Autowired
    private OmsOrderService omsOrderService;


    @Autowired
    private BllRedisLockOrderUtil redisUtil;


    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private AddAndVoidStockListService addAndVoidStockListService;

    @Autowired
    private OmsOrderManualSplitService omsOrderManualSplitService;

    @Autowired
    private OmsOrderSplitService omsOrderSplitService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsOrderJdSplitService omsOrderJdSplitService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsOrderAutoSplitService omsOrderAutoSplitService;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    @Autowired
    private OmsAuditTaskService omsAuditTaskService;

    @Autowired
    private OmsOrderDistributeWarehouseService omsWarehouseRuleService;

    @Autowired
    private OrderAutoSplitByGoodsUtil orderAutoSplitByGoodsUtil;

    @Autowired
    private OrderAmountUtil orderAmountUtil;


    @Autowired
    private SplitOrderUtils splitOrderUtils;

    @Autowired
    private OmsWmsTaskService wmsTaskService;

    @Autowired
    OmsOrderSplitReasonUtil omsOrderSplitReasonUtil;
    @Autowired
    private OmsOrderManualSplitNewService omsOrderManualSplitNewService;


    public ValueHolderV14 batchAppointSplitOrder(JSONObject param, User user, UserPermission usrPem) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (param == null || param.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("指定商品拆单传入参数不能为空");
            return vh;
        }
        if (!param.containsKey("appiontSplitSkuCode") && param.getString("appiontSplitSkuCode") == null) {
            vh.setMessage("请选择需要按照哪一个商品进行拆单");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        String appiontSplitSkuCode = param.getString("appiontSplitSkuCode");
        param.remove("appiontSplitSkuCode");
        param.remove("qty");

        /**如果包含ids参数 即为按页面选中订单处理*/
        List<Long> orderIdList = new ArrayList();
        if (param.containsKey("ids")) {
            if (!param.getJSONArray("ids").isEmpty()) {
                JSONArray ids = param.getJSONArray("ids");
                for (int i = 0; i < ids.size(); i++) {
                    orderIdList.add(ids.getLong(i));
                }
            }
        } else {
            orderIdList = queryOrderIdsByParam(param, user, usrPem);
        }
        if (CollectionUtils.isEmpty(orderIdList)) {
            vh.setMessage("根据查询条件，未查询到订单信息请检查查询条件后重试");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        return omsOrderManualSplitNewService.appointSplitOrder(orderIdList, appiontSplitSkuCode, user, param.getString("splitNum"));

    }

    /**
     * @param param
     * @param user
     * @param usrPem
     * @return
     */
    public ValueHolderV14 batchAppointSplitOrder1(JSONObject param, User user, UserPermission usrPem) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (param == null || param.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("指定商品拆单传入参数不能为空");
            return vh;
        }
        if (!param.containsKey("appiontSplitSkuCode") && param.getString("appiontSplitSkuCode") == null) {
            vh.setMessage("请选择需要按照哪一个商品进行拆单");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        String appiontSplitSkuCode = param.getString("appiontSplitSkuCode");
        param.remove("appiontSplitSkuCode");
        param.remove("qty");

        /**如果包含ids参数 即为按页面选中订单处理*/
        List<Long> orderIdList = new ArrayList();
        if (param.containsKey("ids")) {
            if (!param.getJSONArray("ids").isEmpty()) {
                JSONArray ids = param.getJSONArray("ids");
                for (int i = 0; i < ids.size(); i++) {
                    orderIdList.add(ids.getLong(i));
                }
            }
        } else {
            orderIdList = queryOrderIdsByParam(param, user, usrPem);
        }
        if (CollectionUtils.isEmpty(orderIdList)) {
            vh.setMessage("根据查询条件，未查询到订单信息请检查查询条件后重试");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        OmsOrderAppointSplitService appContext = ApplicationContextHandle.getBean(OmsOrderAppointSplitService.class);
        // 用来统计成功和失败条数
        String successId = "";
        Integer success = 0;
        for (Long aLong : orderIdList) {
            OcBOrder ocOrder = ocBOrderMapper.selectById(aLong);
            try {
                //一件代发经销平台订单不可拆单
                boolean isOrderIssuing = omsOrderService.checkOrderIssuing(ocOrder.getId(), ocOrder.getCpCShopId());
                if (isOrderIssuing) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("指定商品拆单失败,一件代发经销平台订单不可拆单.OrderID={},BillNo={}",
                                "OmsOrderAppointSplitService.batchAppointSplitOrder",
                                ocOrder.getId(), ocOrder.getBillNo()), ocOrder.getId(), ocOrder.getBillNo());
                    }
                    omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                            OrderLogTypeEnum.APPOINT_SPLIT.getKey(),
                            appiontSplitSkuCode + " 指定商品拆分失败！原因:一件代发经销平台订单不可拆单", null, null, user);
                    continue;
                }
                ValueHolderV14 v14 = appContext.appointSplitMainProcess(ocOrder, appiontSplitSkuCode, user);
                if (v14 != null && ResultCode.SUCCESS == v14.getCode()) {
                    success++;
                    if (success <= 3) {
                        successId = successId + aLong + ",";
                    }
                    //订单日志服务添加记录
                    omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                            OrderLogTypeEnum.APPOINT_SPLIT.getKey(), appiontSplitSkuCode + " 指定商品拆分成功！:", null, null,
                            user);

                } else {
                    //订单日志服务添加记录
                    String errorMessage = v14 == null ? "" : v14.getMessage() == null ? "" : v14.getMessage();
                    omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                            OrderLogTypeEnum.APPOINT_SPLIT.getKey(),
                            appiontSplitSkuCode + " 指定商品拆分失败！原因:" + errorMessage, null, null, user);
                }
            } catch (Exception e) {
                log.error(LogUtil.format("指定商品拆单服务异常,异常信息为:{}", aLong), Throwables.getStackTraceAsString(e));
                //订单日志服务添加记录
                omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                        OrderLogTypeEnum.APPOINT_SPLIT.getKey(),
                        appiontSplitSkuCode + " 指定商品拆分失败！原因:" + e.getMessage(), null, null, user);

            }
        }
        if (StringUtil.isNotEmpty(successId)) {
            successId = successId.substring(0, successId.length() - 1);
        }
        if (orderIdList.size() == success) {
            vh.setCode(ResultCode.SUCCESS);
            if (success <= 3) {
                vh.setMessage(successId + "订单，指定商品拆单成功");
            } else {
                vh.setMessage(successId + "等订单，指定商品拆单成功");
            }
            return vh;
        } else {
            vh.setCode(ResultCode.FAIL);
            if (StringUtil.isNotEmpty(successId)) {
                if (success <= 3) {
                    vh.setMessage(successId + "订单指定商品拆单成功,拆单成功" + success + "条数据，拆单失败" + (orderIdList.size() - success) + "条数据");
                } else {
                    vh.setMessage(successId + "等订单指定商品拆单成功,拆单成功" + success + "条数据，拆单失败" + (orderIdList.size() - success) + "条数据");
                }
            } else {
                vh.setMessage("指定商品拆单成功" + success + "条数据，拆单失败" + (orderIdList.size() - success) + "条数据");
            }
            return vh;
        }
    }


    /**
     * 按照查询条件来查询 满足条件的订单id
     *
     * @param param
     * @param loginUser
     * @param usrPem
     * @return
     */
    private List<Long> queryOrderIdsByParam(JSONObject param, User loginUser, UserPermission usrPem) {
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer pageSize = config.getProperty("search.order.size", 500);
        if (log.isDebugEnabled()) {
            log.debug("queryOrderIdsByParam方法开始，入参param为={}", param.toJSONString());
        }
        /**按查询条件，查询订单id集合*/
        // 特殊处理在参数中加入一个字段，用来标志我只查询id
        param.put("SearchForId", 1);
        if (param.containsKey("page")) {
            JSONObject page = param.getJSONObject("page");
            page.put("pageSize", pageSize);
        }
        ValueHolderV14<QueryOrderListResult> queryOrderListRetV14;
        try {
            queryOrderListRetV14 = listQueryService.queryOrderList(param.toJSONString(), loginUser, usrPem);
            if (log.isDebugEnabled()) {
                log.debug("queryOrderIdsByParam方法，调用订单查询服务出参={}", JSON.toJSONString(queryOrderListRetV14));
            }
            if (ResultCode.SUCCESS == queryOrderListRetV14.getCode()) {
                QueryOrderListResult data = queryOrderListRetV14.getData();
                if (data != null) {
                    if (!CollectionUtils.isEmpty(data.getIds())) {
                        return data.getIds();
                    }
                }
                return Lists.newArrayList();
            }
        } catch (Exception e) {
            log.error(LogUtil.format("调用订单查询服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            return Lists.newArrayList();
        }
        return Lists.newArrayList();
    }


    /**
     * 指定商品拆单主流程
     *
     * @param ocBOrder            订单信息
     * @param appiontSplitSkuCode 指定拆单商品条码
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 appointSplitMainProcess(OcBOrder ocBOrder, String appiontSplitSkuCode, User user) {
        ValueHolderV14 vh = new ValueHolderV14();

        //1.当前订单状态为 待审核/缺货 2.当前订单存在该SKU。
        ValueHolderV14 v14 = checkOrderInfo(ocBOrder, appiontSplitSkuCode);
        if (ResultCode.FAIL == v14.getCode()) {
            return v14;
        }
        ValueHolderV14 valueHolder = splitPreHandle(ocBOrder, appiontSplitSkuCode, user);
        if (valueHolder != null) {
            return valueHolder;
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("指定商品拆单成功！");
        return vh;
    }

    public ValueHolderV14 splitPreHandle(OcBOrder ocBOrder, String appiontSplitSkuCode, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(ocBOrder.getId());
                if (orderRelation.getOrderInfo().getPayType().equals(OmsPayType.CASH_ON_DELIVERY.toInteger())) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("货到付款订单不允许拆单");
                    return vh;
                }
                List<String> skuCodeList = new ArrayList<>();
                if (appiontSplitSkuCode.contains(",")) {
                    skuCodeList = Arrays.asList(appiontSplitSkuCode.split(","));
                } else {
                    skuCodeList.add(appiontSplitSkuCode);
                }
                vh = splitOrder(skuCodeList, orderRelation, user);
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("当前订单其他人在操作，请稍后再试! ");
                return vh;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("订单指定商品拆单执行异常,异常信息为:{}", ocBOrder.getId()), Throwables.getStackTraceAsString(ex));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单Id为" + ocBOrder.getId() + "的订单指定商品拆单执行异常!异常原因--> " + ex.getMessage());
            return vh;
        } finally {
            redisLock.unlock();
        }
        return vh;
    }

    public ValueHolderV14 checkOrderInfo(OcBOrder orderInfo, String appiontSplitSkuCode) {
        Long orderId = orderInfo.getId();
        ValueHolderV14 vh = new ValueHolderV14();
        //当平台类型为jitx时不允许拆单
        if (PlatFormEnum.VIP_JITX.getCode().equals(orderInfo.getPlatform())) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单orderId" + orderId + "订单为JITX订单,不能进行指定商品拆单！");
        }
        /**已拦截订单，不允许拆单*/
        if (OmsOrderIsInterceptEnum.YES.getVal().equals((orderInfo.getIsInterecept()))) {
            log.info("OmsOrderAppointSplitService,订单orderId={}订单已被拦截,不能进行指定商品拆单！", orderId);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单orderId" + orderId + "订单已被拦截,不能进行指定商品拆单！");
        }
        /**订单明细不包含前端传入的指定skuCode时，不允许拆单(此处已排除指定SKU为福袋或组合商品的子商品)*/
        if (!(isContainsAppointSkuCode(orderInfo, appiontSplitSkuCode))) {
            log.info("OmsOrderAppointSplitService,订单orderId={}订单明细中，未全部包含指定的skuCode，不能继续指定商品拆单!", orderId);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单orderId" + orderId + "订单明细中，未全部包含指定的skuCode，不能继续指定商品拆单!");
        }
        /**订单状态非“待审核”或者“缺货”时，不允许拆单*/
        if (!(OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderInfo.getOrderStatus())
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderInfo.getOrderStatus()))) {
            log.info("OmsOrderAppointSplitService,订单orderId={}状态非“未审核”或者“缺货”，不能继续指定商品拆单!", orderId);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单orderId" + orderId + "状态非“未审核”或者“缺货”，不能继续指定商品拆单!");
        }
        /**货到付款订单不允许拆单*/
        if (orderInfo.getPayType() == OmsPayType.CASH_ON_DELIVERY.toInteger()) {
            log.info("OmsOrderAppointSplitService,订单orderId={}为货到付款订单，不能继续指定商品拆单!", orderId);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单orderId" + orderId + "为货到付款订单，不再继续执行自动拆单逻辑!");
        }
        /**预售状态为预售尾款未付的订单不允许拆单*/
        String statusPayStep = orderInfo.getStatusPayStep();
        if (OrderTypeEnum.TBA_PRE_SALE.getVal().equals(orderInfo.getOrderType())
                && !TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equals(statusPayStep)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单orderId" + orderId + "为订单为预售状态为预售尾款未付订单，不再继续执行自动拆单逻辑!");
            log.info("OmsOrderAppointSplitService,订单orderId={}为订单为预售状态为预售尾款未付订单，不再继续执行自动拆单逻辑!", orderId);
        }

        /**指定商品在商品中心不存在，不允许拆单*/
        try {
            ProductSku productSku = psRpcService.selectProductSku(appiontSplitSkuCode);
            if (productSku == null) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("订单orderId" + orderId + "商品编码appiontSplitSkuCode为：" + appiontSplitSkuCode +
                        "未查询到其商品信息，不再继续执行自动拆单逻辑");
                log.info("OmsOrderAppointSplitService,订单orderId={}商品编码appiontSplitSkuCode={}未查询到其商品信息，不再继续执行自动拆单逻辑",
                        orderId, appiontSplitSkuCode);
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("ProductService.selectProductSku,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单orderId" + orderId + "商品编码appiontSplitSkuCode为：" + appiontSplitSkuCode +
                    "未查询到其商品信息，不再继续执行自动拆单逻辑");
        }
        return vh;
    }

    /**
     * 判断拆单信息入口//并发控制
     *
     * @param appiontSplitSkuCodeList 指定拆单skucode列表
     * @param orderRelation
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 splitOrder(List<String> appiontSplitSkuCodeList, OcBOrderRelation orderRelation, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        boolean autoSplitResultFlag = true;
        //取未退款成功的明细
        List<OcBOrderItem> orderUnRefundItemList =
                omsOrderItemService.selectUnSuccessRefund(orderRelation.getOrderInfo().getId());
        orderRelation.setOrderItemList(orderUnRefundItemList);
        if (CollectionUtils.isEmpty(orderUnRefundItemList)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("当前订单id为：" + orderRelation.getOrderId() + "，未退款成功的明细为空！");
            //回滚拆分状态
            return vh;
        } else if (orderUnRefundItemList.size() <= 1) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单id为：" + orderRelation.getOrderId() + "，未退款明细小于等于一条，无需订单拆分！");
            //回滚拆分状态
            return vh;
        }
        //指定拆分商品的订单明细
        List<OcBOrderItem> appointSkuOcBOrderItems = new ArrayList<>();
        // 排除指定商品后的其他明星
        List<OcBOrderItem> otherOcBOrderItems = new ArrayList<>();
        //查询出未退款，且包含组合商品的所有明细列表
        List<OcBOrderItem> allItems =
                ocBOrderItemMapper.selectOrderItemContainsCombination(orderRelation.getOrderInfo().getId());

        // 非挂靠关系的赠品
        List<OcBOrderItem> giftList = orderAutoSplitByGoodsUtil.getGiftList(allItems);
        // 组合商品，普通商品及其挂靠商品
        List<OrderAutoSplitByGoodsUtil.OrderItemGroup> goodsItems = orderAutoSplitByGoodsUtil.getGoodGroupList(allItems);
        for (OrderAutoSplitByGoodsUtil.OrderItemGroup itemGroup : goodsItems) {
            if (appiontSplitSkuCodeList.contains(itemGroup.getPsCSkuEcode())) {
                appointSkuOcBOrderItems.addAll(itemGroup.getItems());
            } else {
                otherOcBOrderItems.addAll(itemGroup.getItems());
            }
        }
        if (CollectionUtils.isEmpty(appointSkuOcBOrderItems)
                || CollectionUtils.isEmpty(otherOcBOrderItems)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("当前订单中不包含指定商品或只包含指定商品，不符合拆单条件");
            return vh;
        }
        // 将非挂靠关系的系统赠品 与 非指定商品拆一单
        if (CollectionUtils.isNotEmpty(giftList)) {
            otherOcBOrderItems.addAll(giftList);
        }
        //计算明细金额orderRelationList
        List<OcBOrderRelation> reAmountOcBOrderRelationList = new ArrayList<>();
        //实例化一个对象，用于批量作废和占用逻辑发货单
        List<OcBOrderRelation> occupyList = new ArrayList<>();
        List<OcBOrderRelation> jdOrderRelationList = new ArrayList<>();
        StCShopStrategyDO shopStrategy =
                shopStrategyService.selectOcStCShopStrategy(orderRelation.getOrderInfo().getCpCShopId());
        String isPlatformSplit = "N";
        if (!StringUtils.isEmpty(shopStrategy.getIsPlatformSplit())) {
            isPlatformSplit = shopStrategy.getIsPlatformSplit();
        }
        //指定商品订单构造
        OcBOrderRelation appointOcBOrderRelation = makeupNewOrder(orderRelation, appointSkuOcBOrderItems, 1,
                jdOrderRelationList, isPlatformSplit);
        appointOcBOrderRelation = commonSplitOrderFinalHandle(orderRelation, appointOcBOrderRelation,
                user, "appoint");
        if (appointOcBOrderRelation != null) {
            reAmountOcBOrderRelationList.add(appointOcBOrderRelation);
        }
        occupyList.add(appointOcBOrderRelation);
        //剩余部分商品订单构造
        OcBOrderRelation otherOcBOrderRelation = makeupNewOrder(orderRelation, otherOcBOrderItems, 2, jdOrderRelationList,
                isPlatformSplit);
        // 为子订单重新分仓
        otherOcBOrderRelation = commonSplitOrderFinalHandle(orderRelation, otherOcBOrderRelation,
                user, "other");
        if (otherOcBOrderRelation != null) {
            reAmountOcBOrderRelationList.add(otherOcBOrderRelation);
        }
        occupyList.add(otherOcBOrderRelation);


        if ("Y".equals(isPlatformSplit)
                && PlatFormEnum.JINGDONG.getCode().equals(orderRelation.getOrderInfo().getPlatform())
                && !"手工新增".equals(orderRelation.getOrderInfo().getOrderSource())) {
            omsOrderJdSplitService.splitOrderByJingdong(orderRelation.getOrderInfo(), jdOrderRelationList, user);
        } else {
            Long orginId = orderRelation.getOrderInfo().getOrigOrderId();
            List<OcBOrder> voidOrderList = new ArrayList<>();
            //作废订单列表
            voidOrderList.add(orderRelation.getOrderInfo());

            ValueHolderV14<List> result =
                    addAndVoidStockListService.AddAndVoidStock(voidOrderList, occupyList, user, true);
            if (CollectionUtils.isNotEmpty(voidOrderList)) {
                for (OcBOrder ocBOrder : voidOrderList) {
                    //订单操作日志
                    try {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                                ocBOrder.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(),
                                "指定商品拆单,作废原单", "", "", user
                        );
                    } catch (Exception e) {
                        log.error(LogUtil.format("作废原单调用日志服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    }
                }
            }
            if (result.isOK()) {
//                List<SgSendSaveWithPriorityResult> resultList = result.getData();
//                for (SgSendSaveWithPriorityResult sgSendSaveWithPriorityResult : resultList) {
//                    if (sgSendSaveWithPriorityResult.getPreoutResult() == 0) {
//                        OcBOrder ocBOrder = ocBOrderMapper.selectByID(sgSendSaveWithPriorityResult.getSourceBillId());
//                        log.info("进行订单拆单");
//                        // 当开关开启走仓库拆单 （插入拆单task表），不开启 则不走仓库拆单*（插入传订单审核）
//                        if (splitOrderUtils.isOpenWareHouseSplitOrder(ocBOrder)) {
//                            // 插入仓库拆单任务表
//                            wmsTaskService.saveOrUpdateOcBWarehouseSplitTask(ocBOrder.getId(), user);
//                        } else {
//                            // 插入传wms表
//                            omsAuditTaskService.createOcBAuditTask(ocBOrder, OmsAuditTimeCalculateReason.SPLIT);
//                        }
//                        omsOrderLogService.addUserOrderLog(sgSendSaveWithPriorityResult.getSourceBillId(),
//                                sgSendSaveWithPriorityResult.getSourceBillNo(),
//                                OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(),
//                                "指定拆单，订单id为【" + sgSendSaveWithPriorityResult.getSourceBillId() + "】占用库存成功!", "", "",
//                                user);
//                        omsOrderManualSplitService.pushEs(sgSendSaveWithPriorityResult.getSourceBillId(),
//                                OmsOrderStatus.UNCONFIRMED.toInteger());
//                    } else {
//                        //然后更新明细缺货数量
//                        Map<Long, BigDecimal> stringListMapTmp = new HashMap<>();
//                        List<SgStoreWithPrioritySearchItemResult> sgStoreWithPrioritySearchItemResults =
//                                sgSendSaveWithPriorityResult.getOutStockItemList();
//                        if (CollectionUtils.isNotEmpty(sgStoreWithPrioritySearchItemResults)) {
//                            for (SgStoreWithPrioritySearchItemResult searchItemResult :
//                                    sgStoreWithPrioritySearchItemResults) {
//                                BigDecimal outStock = (searchItemResult.getQtyOutOfStock() == null ? BigDecimal.ZERO
//                                        : searchItemResult.getQtyOutOfStock());
//                                //避免出现不同明细相同sku,若出现,计算累加和
//                                if (stringListMapTmp.containsKey(searchItemResult.getPsCSkuId())) {
//                                    stringListMapTmp.put(searchItemResult.getPsCSkuId(),
//                                            outStock.add(stringListMapTmp.get(searchItemResult.getPsCSkuId())));
//                                } else {
//                                    stringListMapTmp.put(searchItemResult.getPsCSkuId(), outStock);
//                                }
//                            }
//                        } else {
//                            log.debug("Appoint-Sp=====" + "OrderId" + sgSendSaveWithPriorityResult.getSourceBillId() + "订单缺货明细集合未返回~~~");
//                        }
//                        List<OcBOrderItem> orderItemListTmp =
//                                omsOrderItemService.selectUnSuccessRefund(sgSendSaveWithPriorityResult.getSourceBillId());
//                        for (OcBOrderItem orderItem : orderItemListTmp) {
//                            if (stringListMapTmp.containsKey(orderItem.getPsCSkuId())) {
//                                orderItem.setQtyLost(stringListMapTmp.get(orderItem.getPsCSkuId()));
//                                //更新明细表
//                                omsOrderItemService.updateOcBOrderItem(orderItem,
//                                        sgSendSaveWithPriorityResult.getSourceBillId());
//                            }
//                        }
//                        omsOrderLogService.addUserOrderLog(sgSendSaveWithPriorityResult.getSourceBillId(),
//                                sgSendSaveWithPriorityResult.getSourceBillNo(),
//                                OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(),
//                                "指定拆单，订单id为【" + sgSendSaveWithPriorityResult.getSourceBillId() + "】占用库存缺货!", "", "",
//                                user);
//                        omsOrderManualSplitService.pushEs(sgSendSaveWithPriorityResult.getSourceBillId(),
//                                OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
//                    }
//                }
            } else {
                //回滚拆分状态
                throw new NDSException(Resources.getMessage("指定拆单调用库存中心，批量作废和占用库存接口失败!异常信息-->" + result.getMessage()));
            }
        }
        if (autoSplitResultFlag) {
            vh.setCode(0);
            vh.setMessage("订单Id为" + orderRelation.getOrderInfo().getId() + "指定拆单执行成功! ");
            return vh;
        } else {
            vh.setCode(-1);
            vh.setMessage("订单Id为" + orderRelation.getOrderInfo().getId() + "指定拆单执行失败! ");
            return vh;
        }
    }

    private void distributeWarehouse(OcBOrderRelation ocBOrderRelation, User user) {
        CpCPhyWarehouse cpCPhyWarehouse = omsWarehouseRuleService.doCallDistributeWarehouseInfo(ocBOrderRelation,
                user);
        if (cpCPhyWarehouse == null) {
            throw new NDSException("指定商品拆单,子订单分仓失败");
        }
        //如果实体仓是o2o仓库，对订单进行打标
        if (StringUtils.equals(cpCPhyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
            ocBOrderRelation.getOrderInfo().setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
        } else {
            ocBOrderRelation.getOrderInfo().setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
        }
        ocBOrderRelation.getOrderInfo().setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
        ocBOrderRelation.getOrderInfo().setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
        ocBOrderRelation.getOrderInfo().setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
        omsOrderService.updateOrderInfo(ocBOrderRelation.getOrderInfo());
        omsOrderLogService.addUserOrderLog(ocBOrderRelation.getOrderId(), ocBOrderRelation.getOrderInfo().getBillNo(),
                OrderLogTypeEnum.WAREHOUSE_SERVICE.getKey(), "指定商品拆单，分仓成功", "", "", user);
    }


    /**
     * 去重订单明细中的sku,排除组合商品或福袋商品的子商品
     *
     * @param orderUnRefundItemList
     * @return skuCodeList
     */
    public List<String> distinctSku(List<OcBOrderItem> orderUnRefundItemList) {
        //取出订单明细条码Code
        List<String> skuCodeList = new ArrayList<>();
        for (OcBOrderItem orderItem : orderUnRefundItemList) {
            if (orderItem.getProType() != null
                    && SkuType.GIFT_PRODUCT != orderItem.getProType() && SkuType.COMBINE_PRODUCT != orderItem.getProType()) {
                skuCodeList.add(orderItem.getPsCSkuEcode());
            }
        }
        //去重明细skuCode
        List<String> distinctList = skuCodeList.stream().distinct().collect(Collectors.toList());
        return distinctList;
    }

    /**
     * 判断当前订单明细中是否全部包含，指定拆单skucode
     *
     * @param orderInfo
     * @param appointSplitSkuCode
     * @return
     */
    public boolean isContainsAppointSkuCode(OcBOrder orderInfo, String appointSplitSkuCode) {
        boolean containsFlag = false;
        List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectAllOrderItem(orderInfo.getId());
        List<String> distinctList = distinctSku(ocBOrderItemList);
        List<String> skuCodeList = new ArrayList<>();
        if (appointSplitSkuCode.contains(",")) {
            skuCodeList = Arrays.asList(appointSplitSkuCode.split(","));
        } else {
            skuCodeList.add(appointSplitSkuCode);
        }
        if (distinctList.containsAll(skuCodeList)) {
            containsFlag = true;
        }
        return containsFlag;
    }

    /**
     * 原订单orderRelation信息,新订单明细信息,index,待处理的jdOrderRelationList,是否平台拆单
     *
     * @param orderRelation
     * @param ocBOrderNewItemList
     * @param index
     * @param jdOrderRelationList
     * @param orderRelation
     * @param isPlatformSplit
     * @return
     */
    private OcBOrderRelation makeupNewOrder(OcBOrderRelation orderRelation, List<OcBOrderItem> ocBOrderNewItemList,
                                            int index, List<OcBOrderRelation> jdOrderRelationList,
                                            String isPlatformSplit) {

        OcBOrderRelation newOrderRelationTmp = new OcBOrderRelation();
        Long firstOrderNewId = ModelUtil.getSequence("oc_b_order");

        Long oldOrderId = orderRelation.getOrderInfo().getId();
        newOrderRelationTmp.setOrderItemList(ocBOrderNewItemList);

        String suffixInfo = (orderRelation.getOrderInfo().getId() + "-AppointSP" + index);
        try {
            OcBOrder ocBOrder = omsOrderManualSplitService.bulidOcbOrder(orderRelation.getOrderInfo(), firstOrderNewId,
                    suffixInfo, "指定商品拆单");
            // @20210107 确认后，包含指定商品的订单 拆单原因：按SKU拆单 不包含指定商品的订单 拆单原因：手工拆单
            if (index == 1) {
                ocBOrder.setSplitReason(SplitReason.SPLIT_BY_SKU);

            } else {
                ocBOrder.setSplitReason(SplitReason.SPLIT_MANUAL);
            }

            // 根据明细的退款状态 判断主表的退款标记 1205
            boolean isHashReturn = false;
            if (CollectionUtils.isNotEmpty(ocBOrderNewItemList)) {
                for (OcBOrderItem item : ocBOrderNewItemList) {
                    // 如果存在退款状态，并且状态不为：关闭，则需要打标
                    if (StringUtils.isNotBlank(item.getPtReturnStatus())
                            && OcOrderRefundStatusEnum.CLOSED.getVal() != item.getRefundStatus()) {
                        isHashReturn = true;
                        break;
                    }
                }
            }
            // 如果不退则取消标记
            if (!isHashReturn) {
                //是否已经拦截
                ocBOrder.setIsInterecept(0);
                //是否已经退款中
                ocBOrder.setIsInreturning(0);
            }

            //自定义拆单赋值
            omsOrderSplitReasonUtil.matchReason(ocBOrder);
            newOrderRelationTmp.setOrderInfo(ocBOrder);
            List<OcBOrderItem> noMalorderItemList = buildAppointOrderItemList(firstOrderNewId, oldOrderId,
                    ocBOrderNewItemList);
            newOrderRelationTmp.setOrderItemList(noMalorderItemList);

            OcBOrderRelation jdOcBOrderRelation = new OcBOrderRelation();
            if ("Y".equals(isPlatformSplit)
                    && PlatFormEnum.JINGDONG.getCode().equals(orderRelation.getOrderInfo().getPlatform())
                    && !"手工新增".equals(orderRelation.getOrderInfo().getOrderSource())) {
                jdOcBOrderRelation.setOrderInfo(ocBOrder);
            } else {
                // 重新计算金额
                orderAmountUtil.recountOrderAmount(newOrderRelationTmp);
                // 重新打标
                OrderTagUtil.orderTags(newOrderRelationTmp);
                //保存订单对象
                omsOrderService.saveOrderInfo(newOrderRelationTmp.getOrderInfo());
            }
            if ("Y".equals(isPlatformSplit)
                    && PlatFormEnum.JINGDONG.getCode().equals(orderRelation.getOrderInfo().getPlatform())
                    && !"手工新增".equals(orderRelation.getOrderInfo().getOrderSource())) {
                jdOcBOrderRelation.setOrderItemList(ocBOrderNewItemList);
                jdOrderRelationList.add(jdOcBOrderRelation);
                newOrderRelationTmp.setOrderItemList(noMalorderItemList);
            } else {
                for (OcBOrderItem ocBOrderItem : newOrderRelationTmp.getOrderItemList()) {
                    omsOrderItemService.saveOcBOrderItem(ocBOrderItem, firstOrderNewId);
                }
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.APPOINT_SPLIT.getKey(),
                        "指定商品拆单新生成订单", "", "", SystemUserResource.getRootUser());
            }
        } catch (Exception ex) {
            throw ex;
        }
        return newOrderRelationTmp;
    }

    /**
     * @param orderRelation       原订单信息
     * @param commonOrderRelation 拆分后的新订单信息
     * @param user                用户信息
     * @param indexTag            索引标签
     */
    public OcBOrderRelation commonSplitOrderFinalHandle(OcBOrderRelation orderRelation,
                                                        OcBOrderRelation commonOrderRelation, User user,
                                                        String indexTag) {
        // 为新单分仓
        //distributeWarehouse(commonOrderRelation, user);
        // 订单打标
        OrderTagUtil.orderTags(orderRelation);
        //重新计算订单主表数据[分物流]
        omsOrderSplitService.reAmount(commonOrderRelation.getOrderInfo(), commonOrderRelation.getOrderItemList(), true);
        OcBOrder commonOcBOrder = omsOrderService.selectOrderInfo(commonOrderRelation.getOrderInfo().getId());
        commonOrderRelation.setOrderInfo(commonOcBOrder);
        return commonOrderRelation;
    }


    /**
     * 指定商品拆单，构造新订单明细
     *
     * @param orderNewId      新拆分订单Id
     * @param originId        原始订单Id
     * @param ocBOrderItemDto 原始订单明细对象
     * @return OcBOrderItem 拆分后的订单明细
     */
    public OcBOrderItem bulidAppointOcBOrderItem(Long orderNewId, Long originId, OcBOrderItem ocBOrderItemDto) {

        OcBOrderItem orderItem = new OcBOrderItem();
        BeanUtils.copyProperties(ocBOrderItemDto, orderItem);
        //重新生成Id
        orderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
        //设置订单Id
        orderItem.setOcBOrderId(orderNewId);
        //修改人
        orderItem.setModifierename(SystemUserResource.ROOT_USER_NAME);
        //修改时间
        orderItem.setModifieddate(new Date());
        //缺货数量置为0
        orderItem.setQtyLost(BigDecimal.ZERO);
        return orderItem;
    }

    /**
     * 构造新明细
     *
     * @param orderNewId    新单Id
     * @param originId      原始订单Id
     * @param orderItemList 原单明细List
     * @return
     */
    private List<OcBOrderItem> buildAppointOrderItemList(Long orderNewId, Long
            originId, List<OcBOrderItem> orderItemList) {

        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
        for (OcBOrderItem ocbItemDto : orderItemList) {
            ocBOrderItemList.add(bulidAppointOcBOrderItem(orderNewId, originId, ocbItemDto));
        }
        return ocBOrderItemList;
    }

}
