package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.ip.model.result.QueryTrackData;
import com.jackrain.nea.ip.model.result.QueryTrackResp;
import com.jackrain.nea.ip.model.result.zto.ZtoCreateInterceptResponse;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogisticsInterceptMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogisticsServiceTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLogisticsIntercept;
import com.jackrain.nea.oc.oms.nums.KuaiDi100StateEnum;
import com.jackrain.nea.oc.oms.nums.LogisticsInterceptBeforeDeliveryEnum;
import com.jackrain.nea.oc.oms.nums.LogisticsInterceptStatusEnum;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/3/6 21:25
 * @Description
 */
@Slf4j
@Component
public class ZtoLogisticsInterceptCallBackService {

    @Resource
    private OcBOrderLogisticsInterceptMapper ocBOrderLogisticsInterceptMapper;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    protected OmsOrderStandPlatAutoRefundService standPlatAutoRefundService;
    @Autowired
    private IpBStandplatRefundMapper ipBStandplatRefundMapper;
    @Autowired
    private OmsStandplatRefundService omsStandplatRefundService;
    @Autowired
    private OmsBeforeShipmentReturnService omsBeforeShipmentReturnService;
    @Autowired
    private IpBTaobaoRefundMapper ipBTaobaoRefundMapper;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    public void execute(String messageBody) {
        List<ZtoCreateInterceptResponse> responseList =
                JSON.parseArray(messageBody, ZtoCreateInterceptResponse.class);
        if (CollectionUtils.isEmpty(responseList)) {
            return;
        }
        Map<Long, OcBOrderLogisticsIntercept> interceptMap = new HashMap<>();
        List<Long> ids = responseList.stream().map(ZtoCreateInterceptResponse::getMainTableId).collect(Collectors.toList());
        List<OcBOrderLogisticsIntercept> interceptList = ocBOrderLogisticsInterceptMapper.selectBatchIds(ids);
        if (CollectionUtils.isNotEmpty(interceptList)) {
            interceptMap = interceptList.stream().collect(Collectors.toMap(OcBOrderLogisticsIntercept::getId, Function.identity()));
        }
        for (ZtoCreateInterceptResponse response : responseList) {
            OcBOrderLogisticsIntercept orderLogisticsIntercept = interceptMap.get(response.getMainTableId());
            OcBOrderLogisticsIntercept logisticsIntercept = new OcBOrderLogisticsIntercept();

            // 极兔：极兔是订阅模式，消息发送回执防止覆盖
            if (orderLogisticsIntercept != null
                    && LogisticsServiceTypeEnum.JT.getVal().equals(orderLogisticsIntercept.getLogisticsServiceType())) {
                if (LogisticsInterceptStatusEnum.INTERCEPT_SUCCESS.getKey().equals(orderLogisticsIntercept.getInterceptStatus())
                        || LogisticsInterceptStatusEnum.INTERCEPT_FAIL.getKey().equals(orderLogisticsIntercept.getInterceptStatus())) {
                    log.info(LogUtil.format("极兔快递订阅模式，消息发送回执防止覆盖,拦截ID:{}，单号:{}",
                                    "ZtoLogisticsInterceptCallBackService.execute"),
                            orderLogisticsIntercept.getId(), orderLogisticsIntercept.getExpresscode());
                    continue;
                }
                /*这里记录一下，极兔是否接收了回执*/
                logisticsIntercept.setRemark("极兔接收订阅回执状态:" + response.getStatus());
            }

            logisticsIntercept.setCpCLogisticsEcode(orderLogisticsIntercept.getCpCLogisticsEcode());
            logisticsIntercept.setCpCLogisticsId(orderLogisticsIntercept.getCpCLogisticsId());
            logisticsIntercept.setExpresscode(orderLogisticsIntercept.getExpresscode());
            logisticsIntercept.setId(response.getMainTableId());

            if (response.getData() != null) {
                logisticsIntercept.setInterceptBillNo(response.getData().getCenterBizNo());
            }
            if (response.getStatus()) {
                logisticsIntercept.setInterceptFailureReason("");
            }
            if (orderLogisticsIntercept != null &&
                    (LogisticsInterceptStatusEnum.NOT_INTERCEPT.getKey().equals(orderLogisticsIntercept.getInterceptStatus())
                            || LogisticsInterceptStatusEnum.SEND_INTERCEPT_FAIL.getKey().equals(orderLogisticsIntercept.getInterceptStatus()))) {
                if (response.getStatus()) {
                    logisticsIntercept.setInterceptStatus(LogisticsInterceptStatusEnum.SEND_INTERCEPT_SUCCESS.getKey());
                    logisticsIntercept.setInterceptFailureReason("");
                } else {
                    logisticsIntercept.setInterceptStatus(LogisticsInterceptStatusEnum.SEND_INTERCEPT_FAIL.getKey());
                    logisticsIntercept.setInterceptFailureReason(response.getMessage());
                    logisticsIntercept.setInterceptFailureNum(orderLogisticsIntercept.getInterceptFailureNum() + 1);
                }
            }
            List<Integer> interceptStatus = Arrays.asList(LogisticsInterceptStatusEnum.INTERCEPTING.getKey()
                    , LogisticsInterceptStatusEnum.NOT_INTERCEPT.getKey()
                    , LogisticsInterceptStatusEnum.INTERCEPT_FAIL.getKey());
            // 京东拦截类型 未拦截、拦截中、拦截失败
            if (orderLogisticsIntercept != null
                    && LogisticsServiceTypeEnum.JD.getVal().equals(orderLogisticsIntercept.getLogisticsServiceType())
                    && interceptStatus.contains(orderLogisticsIntercept.getInterceptStatus())) {
                if (response.getStatus()) {
                    logisticsIntercept.setInterceptStatus(LogisticsInterceptStatusEnum.SEND_INTERCEPT_SUCCESS.getKey());
                    logisticsIntercept.setRemark("");
                } else {
                    logisticsIntercept.setInterceptStatus(LogisticsInterceptStatusEnum.SEND_INTERCEPT_FAIL.getKey());
                    logisticsIntercept.setInterceptFailureReason(response.getMessage());
                    logisticsIntercept.setInterceptFailureNum(orderLogisticsIntercept.getInterceptFailureNum() + 1);
                    logisticsIntercept.setRemark("");
                }
            }

            // 丹鸟
            if (orderLogisticsIntercept != null
                    && LogisticsServiceTypeEnum.DN.getVal().equals(orderLogisticsIntercept.getLogisticsServiceType())
                    && 0 != orderLogisticsIntercept.getInterceptStatus()) {
                if (response.getStatus()) {
                    logisticsIntercept.setInterceptStatus(LogisticsInterceptStatusEnum.SEND_INTERCEPT_SUCCESS.getKey());
                    logisticsIntercept.setRemark("");
                } else {
                    logisticsIntercept.setInterceptStatus(LogisticsInterceptStatusEnum.SEND_INTERCEPT_FAIL.getKey());
                    logisticsIntercept.setInterceptFailureReason(response.getMessage());
                    logisticsIntercept.setInterceptFailureNum(orderLogisticsIntercept.getInterceptFailureNum() + 1);
                    logisticsIntercept.setRemark("");
                }
            }

            executeAfterIntercept(logisticsIntercept);
            logisticsIntercept.setModifieddate(new Date());
            /*ocBOrderLogisticsInterceptMapper.updateById(logisticsIntercept);*/
            /*拦截状态如果是 1-发起拦截成功，4-拦截成功，5-拦截失败，则无需接收回执*/
            ocBOrderLogisticsInterceptMapper.update(logisticsIntercept, new UpdateWrapper<OcBOrderLogisticsIntercept>()
                    .lambda().eq(OcBOrderLogisticsIntercept::getId, response.getMainTableId())
                    .notIn(OcBOrderLogisticsIntercept::getInterceptStatus,
                            Arrays.asList(LogisticsInterceptStatusEnum.SEND_INTERCEPT_SUCCESS.getKey(),
                                    LogisticsInterceptStatusEnum.INTERCEPT_FAIL.getKey(),
                                    LogisticsInterceptStatusEnum.INTERCEPT_SUCCESS.getKey())));

            executeAfSuccess(logisticsIntercept, orderLogisticsIntercept);
        }
    }

    private void executeAfSuccess(OcBOrderLogisticsIntercept logisticsIntercept, OcBOrderLogisticsIntercept oldOrderLogisticsIntercept) {
        log.info(LogUtil.format("开始进入AG退款外层方法，logisticsIntercept:{}, oldOrderLogisticsIntercept:{}",
                "ZtoLogisticsInterceptCallBackService.executeAfSuccess"), logisticsIntercept, oldOrderLogisticsIntercept);

        /*【发起拦截成功】且【派件前拦截=是】*/
        if (LogisticsInterceptStatusEnum.SEND_INTERCEPT_SUCCESS.getKey().equals(logisticsIntercept.getInterceptStatus())
                && LogisticsInterceptBeforeDeliveryEnum.BEFORE_DELIVERY.getKey().equals(logisticsIntercept.getBeforeDelivery())) {
            // 获取退款单号
            String refundNo = oldOrderLogisticsIntercept.getTReturnId();
            String shopCode = oldOrderLogisticsIntercept.getCpCShopEcode();
            CpShop cpShop = cpRpcService.queryShop(shopCode);
            log.info(LogUtil.format("开始进入AG退款外层判断，refundNo:{}, cpShop:{}",
                    "ZtoLogisticsInterceptCallBackService.executeAfSuccess"), refundNo, cpShop);
            if (cpShop == null) {
                return;
            }

            OcBOrderLogisticsIntercept updateLogisticsIntercept = new OcBOrderLogisticsIntercept();
            updateLogisticsIntercept.setId(logisticsIntercept.getId());
            /*是否可以调用AG退款*/
            updateLogisticsIntercept.setCanAg(1);
            updateLogisticsIntercept.setModifieddate(new Date());
            ocBOrderLogisticsInterceptMapper.updateById(updateLogisticsIntercept);

            // 查询ag退款开关是否打开。 自动退款自动拦截AG退款 是否打开
            StCShopStrategyDO stCShopStrategyDO = stRpcService.selectOcStCShopStrategyByCpCshopId(cpShop.getId());
            log.info(LogUtil.format("开始进入AG退款策略判断，stCShopStrategyDO:{}",
                    "ZtoLogisticsInterceptCallBackService.executeAfSuccess"), stCShopStrategyDO);
            if (stCShopStrategyDO == null || stCShopStrategyDO.getIsAutoRefund() == null) {
                return;
            }
            if (stCShopStrategyDO.getIsAutoRefund().equals("0")) {
                return;
            }

            if (StringUtils.isEmpty(stCShopStrategyDO.getIsAg()) || "N".equals(stCShopStrategyDO.getIsAg())) {
                return;
            }
            // 可能会存在同一个平台单号多次拦截 需要判断每一个拦截都是发起拦截成功才能调用AG退款
            String tid = oldOrderLogisticsIntercept.getPlatformCode();
            // 根据平台单号查所有拦截单
            List<OcBOrderLogisticsIntercept> ocBOrderLogisticsInterceptList = ocBOrderLogisticsInterceptMapper.selectByTid(tid);
            log.info(LogUtil.format("开始进入AG退款拦截单判断，ocBOrderLogisticsInterceptList:{}",
                    "ZtoLogisticsInterceptCallBackService.executeAfSuccess"), ocBOrderLogisticsInterceptList);

            if (CollectionUtils.isEmpty(ocBOrderLogisticsInterceptList)) {
                log.warn("拦截自动退款时 查询拦截单据空, tid:{}", tid);
                return;
            }
            /*所有拦截单都拦截成功才退款*/
            for (OcBOrderLogisticsIntercept ocBOrderLogisticsIntercept : ocBOrderLogisticsInterceptList) {
                if (ocBOrderLogisticsIntercept.getCanAg() != null && ocBOrderLogisticsIntercept.getCanAg() != 1) {
                    log.warn("拦截自动退款时 有单据未拦截成功, ID:{}", ocBOrderLogisticsIntercept.getId());
                    return;
                }
            }
            Integer platformCode = Integer.parseInt(String.valueOf(cpShop.getCpCPlatformId()));
            try {
                // 根据不同的平台 组装不同的信息 用来调用ag退款
                if (PlatFormEnum.TAOBAO.getCode().equals(platformCode)) {
                    // 淘宝
                    IpBTaobaoRefund ipBTaobaoRefund = ipBTaobaoRefundMapper.selectTaobaoByRefundId(refundNo);
                    if (ipBTaobaoRefund == null) {
                        log.warn("拦截自动退款时 未查询到淘宝退款单信息, refundNo:{}", refundNo);
                        return;
                    }
                    omsBeforeShipmentReturnService.toAg(ipBTaobaoRefund, SystemUserResource.getRootUser());
                } else if (PlatFormEnum.TAOBAO_DISTRIBUTION.getCode().equals(platformCode)) {
                    // 淘宝分销
                } else if (PlatFormEnum.JINGDONG.getCode().equals(platformCode)) {
                    // 京东

                } else if (PlatFormEnum.JINGDONG_CZ.getCode().equals(platformCode)) {
                    // 京东厂直 无ag退款
                } else {
                    // 查询通用退单表是否能查到 能查到的话就调用通用退款同意 否则就直接结束
                    IpBStandplatRefund ipBStandplatRefund = ipBStandplatRefundMapper.selectStandplatRefundByRefundId(refundNo);
                    if (ipBStandplatRefund == null) {
                        log.info("拦截自动退款时 未查询到通用退单表信息, refundNo:{}", refundNo);
                        return;
                    }
                    OmsStandPlatRefundRelation refundRelation = omsStandplatRefundService.selectStandplatRefundRelation(refundNo);
                    log.info(LogUtil.format("开始进入AG退款调用方法，ipBStandplatRefund:{}, refundRelation:{}",
                            "ZtoLogisticsInterceptCallBackService.executeAfSuccess"), ipBStandplatRefund, refundRelation);
                    // 根据平台单号找到所有的拦截单 然后遍历
                    for (OcBOrderLogisticsIntercept ocBOrderLogisticsIntercept : ocBOrderLogisticsInterceptList) {
                        Long orderId = ocBOrderLogisticsIntercept.getOrderId();
                        if (orderId != null) {
                            OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                            List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItemListAndReturn(orderId);
                            boolean executeAutoRefund = standPlatAutoRefundService.executeAutoRefund(refundRelation, SystemUserResource.getRootUser(),
                                    ocBOrder, ocBOrderItemList, true);
                            /*产品逻辑：失败或成功不用管，只要触发过即可，记录日志便于业务排查*/
                            log.debug(LogUtil.format("执行AG退款调用方法，订单ID:{}, 是否成功:{}",
                                    "ZtoLogisticsInterceptCallBackService.executeAfSuccess"), orderId, executeAutoRefund);
                        }
                    }
                }
            } catch (Exception e) {
                // 打印失败的日志
                log.info("拦截自动退款时 调用AG失败, refundNo:{}", refundNo);
            }

            for (OcBOrderLogisticsIntercept intercept : ocBOrderLogisticsInterceptList) {
                OcBOrderLogisticsIntercept update = new OcBOrderLogisticsIntercept();
                update.setId(intercept.getId());
                update.setIsAg(1);
                update.setAgTime(new Date());
                ocBOrderLogisticsInterceptMapper.updateById(update);
            }

        }
        return;
    }

    /**
     * 执行发起拦截成功后的逻辑
     *
     * @param orderLogisticsIntercept
     */
    private void executeAfterIntercept(OcBOrderLogisticsIntercept orderLogisticsIntercept) {
        // 判断是否是发起拦截成功
        if (orderLogisticsIntercept.getInterceptStatus() == null) {
            return;
        }
        if (orderLogisticsIntercept.getInterceptStatus().equals(LogisticsInterceptStatusEnum.SEND_INTERCEPT_FAIL.getKey())) {
            return;
        }
        Long logisticsId = orderLogisticsIntercept.getCpCLogisticsId();
        String logisticsEcode = orderLogisticsIntercept.getCpCLogisticsEcode();
        CpLogistics cpLogistics = cpRpcService.cpLogisticsInfo(logisticsId);
        if (cpLogistics != null && cpLogistics.getTmsLogistic() != null) {
            logisticsEcode = cpLogistics.getTmsLogistic();
        }
        ValueHolderV14<QueryTrackResp> trackResp = ipRpcService.queryLogisticsInfoFrom100(logisticsEcode, orderLogisticsIntercept.getExpresscode(), "1777");
        if (!trackResp.isOK()) {
            // 判断异常内容 如果是  查询无结果，请隔段时间再查 则是 无物流轨迹
            log.error("查询物流轨迹失败，订单号：{}，物流单号：{}", orderLogisticsIntercept.getOrderBillNo(), orderLogisticsIntercept.getExpresscode());
            if (trackResp.getMessage() != null && trackResp.getMessage().contains("查询无结果，请隔段时间再查")) {
                // 无物流轨迹
                orderLogisticsIntercept.setBeforeDelivery(LogisticsInterceptBeforeDeliveryEnum.NO_TRACE.getKey());
            } else {
                // 查询异常
                orderLogisticsIntercept.setBeforeDelivery(LogisticsInterceptBeforeDeliveryEnum.QUERY_ERROR.getKey());
            }
            return;
        }
        QueryTrackResp resp = trackResp.getData();
        // 判断是否有轨迹
        if (CollectionUtils.isEmpty(resp.getData())) {
            log.error("查询物流轨迹失败，订单号：{}，物流单号：{}", orderLogisticsIntercept.getOrderBillNo(), orderLogisticsIntercept.getExpresscode());
            return;
        }
        List<QueryTrackData> trackDataList = resp.getData();
        // 判断是派件前还是派件后
        if (resp.getState().equals(KuaiDi100StateEnum.ZAI_TU.getKey()) || resp.getState().equals(KuaiDi100StateEnum.LAN_SHOU.getKey())) {
            // 如果当前状态是3或者5 则代表是派件后
            orderLogisticsIntercept.setBeforeDelivery(LogisticsInterceptBeforeDeliveryEnum.BEFORE_DELIVERY.getKey());
            return;
        }
        orderLogisticsIntercept.setBeforeDelivery(LogisticsInterceptBeforeDeliveryEnum.AFTER_DELIVERY.getKey());
//
//        // 获取trackDataList的status
//        List<String> statusList = trackDataList.stream().map(QueryTrackData::getStatus).collect(Collectors.toList());
//        if (statusList.contains(KuaiDi100StateEnum.QIAN_SHOU.getKey()) || statusList.contains(KuaiDi100StateEnum.PAI_JIAN.getKey())) {
//            // 如果包含3或者5 则代表是派件后
//            orderLogisticsIntercept.setBeforeDelivery(LogisticsInterceptBeforeDeliveryEnum.AFTER_DELIVERY.getKey());
//        } else {
//            orderLogisticsIntercept.setBeforeDelivery(LogisticsInterceptBeforeDeliveryEnum.BEFORE_DELIVERY.getKey());
//        }
    }
}
