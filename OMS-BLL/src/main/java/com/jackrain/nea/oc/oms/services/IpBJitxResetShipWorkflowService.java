package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.vips.VipCreateShipResetWorkflowRequest;
import com.jackrain.nea.ip.model.vips.VipCreateShipResetWorkflowResult;
import com.jackrain.nea.ip.model.vips.VipGetShipResetWorkflowRequest;
import com.jackrain.nea.ip.model.vips.VipGetShipResetWorkflowResult;
import com.jackrain.nea.ip.model.vips.VipShipResetWorkflowResult;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.IpBJitxResetShipWorkflowMapper;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJitxResetShipWorkflow;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxResetShipReasonEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowCreatedStateEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowStateEnum;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemTableNames;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * description：jitx重置平台发货
 *
 * <AUTHOR>
 * @date 2021/10/18
 */
@Slf4j
@Service
public class IpBJitxResetShipWorkflowService {
    @Autowired
    private IpJitxOrderService ipJitxOrderService;
    @Autowired
    private BllRedisLockOrderUtil bllRedisLockOrderUtil;

    @Autowired
    private IpBJitxResetShipWorkflowMapper ipBJitxResetShipWorkflowMapper;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsAuditTaskService omsAuditTaskService;

    public static final String SUCCESS_CODE = "200";

    // type 10 反审核 20 异常补发
    public static final Long BACK_AUDIT=10L;

    public static final Long RE_DELIVERY=20L;

    @Transactional(rollbackFor = Exception.class)
    public void createByOcBOrder(OcBOrder ocBOrder, List<OcBOrderItem> itemList, User user,Long type) {

        log.info("{},插入重置发货工单数据开始：ocBOrder ID:{}", this.getClass().getSimpleName(), ocBOrder.getId());
        List<String> tidList = itemList.stream().filter(x -> StringUtils.isNotEmpty(x.getTid())).map(OcBOrderItem::getTid).distinct().collect(Collectors.toList());
        String resetShipValue = this.getJITXRedisResetShipFlag(ocBOrder.getTid());
        if (StringUtils.isNotEmpty(resetShipValue)) {
            LambdaQueryWrapper<IpBJitxResetShipWorkflow> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IpBJitxResetShipWorkflow::getId, Long.valueOf(resetShipValue));
            queryWrapper.notIn(IpBJitxResetShipWorkflow::getWorkflowStatus, Lists.newArrayList(VipJitxWorkflowStateEnum.CREATE.getKey(), VipJitxWorkflowStateEnum.DOING.getKey()));
            IpBJitxResetShipWorkflow workflow = ipBJitxResetShipWorkflowMapper.selectById(queryWrapper);
            if (workflow != null) {
                log.debug(" 存在未通过的工单 requesetId：{},请检查", workflow.getRequestId());
            }
        }
        // 平台店铺档案
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        String sellerNick = cpShop == null ? ocBOrder.getCpCShopSellerNick() : cpShop.getSellerNick();
        String vendorId = cpShop == null ? null : cpShop.getPlatformSupplierId();
        IpBJitxResetShipWorkflow workflow = IpBJitxResetShipWorkflow.builder()
                .id(ModelUtil.getSequence(SystemTableNames.IP_B_JITX_RESET_SHIP_WORKFLOW))
                .orderId(ocBOrder.getId())
                .orderNo(ocBOrder.getBillNo())
                .orderSn(ocBOrder.getTid())
                .orderSnList(StringUtils.join(tidList, ","))
                .createdStatus(VipJitxWorkflowCreatedStateEnum.UNCREATE.getCode())
                .sellerNick(sellerNick)
                .vendorId(vendorId)
                .cpCShopId(ocBOrder.getCpCShopId())
                .cpCShopEcode(cpShop.getEcode())
                .cpCShopTitle(cpShop.getCpCShopTitle())
                .reasonCode(VipJitxResetShipReasonEnum.OUT_STOCK.getCode())
                .reasonRemark(VipJitxResetShipReasonEnum.OUT_STOCK.getName())
                .failNumber(0)
                .ipAddress(user.getLastloginip())
                .build();
        BaseModelUtil.makeBaseCreateField(workflow, user);
        workflow.setIsactive(IsActiveEnum.Y.getKey());
        workflow.setVersion(type);
        workflow.setRequestId("skq" + System.currentTimeMillis() + "_" + workflow.getId());
        ipBJitxResetShipWorkflowMapper.insert(workflow);
        if (CollectionUtils.isNotEmpty(tidList)) {
            for (String orderSn : tidList) {
                this.setJITXRedisResetShipFlag(orderSn, workflow.getId());
            }
        }
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.JITX_MERGED_RESHIP.getKey(), String.format("生成重置发货工单成功:工单ID:%d", workflow.getId()), null, null, user);
    }

    public void updateStatus(String id, String orderSn) {
        log.info("{}:更新重置发货工单开始：requestId:{},orderSn:{}", this.getClass().getSimpleName(), id, orderSn);
        LambdaQueryWrapper<IpBJitxResetShipWorkflow> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(IpBJitxResetShipWorkflow::getId, id);
        queryWrapper.eq(IpBJitxResetShipWorkflow::getOrderSn, orderSn);
        queryWrapper.eq(IpBJitxResetShipWorkflow::getIsactive, IsActiveEnum.Y.getKey());
        IpBJitxResetShipWorkflow workflow = IpBJitxResetShipWorkflow.builder()
                .id(Long.valueOf(id))
                .createdStatus(VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode())
                .workflowStatus(VipJitxWorkflowStateEnum.CREATE.getKey()).build();
        ipBJitxResetShipWorkflowMapper.updateById(workflow);

    }

    public List<IpBJitxResetShipWorkflow> selectByNode(String nodeName, String tableName,
                                                       String where, String order, int limit) {
        return ipBJitxResetShipWorkflowMapper.selectByNode(nodeName, tableName, where, order, limit);
    }

    public List<IpBJitxResetShipWorkflow> existOrderWorkflow(Long orderId) {
        //需要走ES
        LambdaQueryWrapper<IpBJitxResetShipWorkflow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IpBJitxResetShipWorkflow::getOrderId, orderId);
        queryWrapper.eq(IpBJitxResetShipWorkflow::getIsactive, "Y");
        queryWrapper.orderByDesc(IpBJitxResetShipWorkflow::getId);
//        queryWrapper.and(o -> {
//            o.isNull("WORKFLOW_STATE").or().in("WORKFLOW_STATE",
//                    Lists.newArrayList(VipJitxWorkflowStateEnum.CREATE.getKey(), VipJitxWorkflowStateEnum.DOING.getKey()));
//            return o;
//        });
        return ipBJitxResetShipWorkflowMapper.selectList(queryWrapper);
    }

    public List<IpBJitxResetShipWorkflow> existReDeliveryWorkflow(Long orderId) {
        //需要走ES
        LambdaQueryWrapper<IpBJitxResetShipWorkflow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IpBJitxResetShipWorkflow::getOrderId, orderId);
        queryWrapper.eq(IpBJitxResetShipWorkflow::getIsactive, IsActiveEnum.Y.getKey());
        queryWrapper.eq(IpBJitxResetShipWorkflow::getVersion, RE_DELIVERY);
        queryWrapper.orderByDesc(IpBJitxResetShipWorkflow::getId);
        return ipBJitxResetShipWorkflowMapper.selectList(queryWrapper);
    }

    public void transferResult(List<IpBJitxResetShipWorkflow> workflowList, ValueHolderV14<VipCreateShipResetWorkflowResult> v14) {
        if (log.isDebugEnabled()) {
            log.debug("{},定时任务处理发货重置工单创建结果开始:{}", this.getClass().getSimpleName(), JSON.toJSONString(workflowList));
        }
        List<Long> idList = workflowList.stream().map(IpBJitxResetShipWorkflow::getId).collect(Collectors.toList());
        List<RedisReentrantLock> lockList = new ArrayList<>(workflowList.size());
        String sqlIds = batchLockOrder(idList, lockList);
        if (lockList.size() < workflowList.size()) {
            batchUnLockOrder(lockList);
            return;
        }
        try {
            for (IpBJitxResetShipWorkflow workflow : workflowList) {
                if (v14.isOK() && v14.getData() != null) {
                    VipCreateShipResetWorkflowResult result = v14.getData();
                    Integer failNumber = workflow.getFailNumber() == null ? 0 : workflow.getFailNumber();
                    if (SUCCESS_CODE.equals(result.getResult().getStatus())) {
                        List<VipCreateShipResetWorkflowResult.VipCreateShipResetWorkflowResp> workflows = result.getWorkflows();
                        for (VipCreateShipResetWorkflowResult.VipCreateShipResetWorkflowResp resp : workflows) {
                            if (resp.getRequest_id().equals(workflow.getRequestId())) {
                                //成功
                                if (SUCCESS_CODE.equals(resp.getResult().getStatus())) {
                                    IpBJitxResetShipWorkflow update = new IpBJitxResetShipWorkflow();
                                    update.setId(workflow.getId());
                                    update.setWorkflowSn(resp.getWorkflow_sn());
                                    update.setWorkflowStatus(VipJitxWorkflowStateEnum.CREATE.getKey());
                                    update.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode());
                                    update.setFailNumber(0);
                                    ipBJitxResetShipWorkflowMapper.updateById(update);
                                } else {
                                    //失败
                                    List<IpBJitxOrder> ipBJitxOrders = ipJitxOrderService.existCancelOrder(workflow.getOrderSnList());
                                    IpBJitxResetShipWorkflow update = new IpBJitxResetShipWorkflow();
                                    update.setId(workflow.getId());
                                    if (CollectionUtils.isNotEmpty(ipBJitxOrders)) {
                                        update.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode());
                                        update.setWorkflowStatus(VipJitxWorkflowStateEnum.CANCEL.getKey());
                                    } else if (!ObjectUtils.isEmpty(resp.getResult().getMessage()) &&
                                            resp.getResult().getMessage().contains("状态为已发货才可以重置,未发货/已揽收/取消都无法重置")){
                                        String orderSnList = workflow.getOrderSnList();
                                        //标记为无需创建，移除redis标记
                                        if (StringUtils.isNotEmpty(orderSnList)) {
                                            String[] orderSnArr = orderSnList.split(",");
                                            for (String orderSn : orderSnArr) {
                                                this.removeJITXRedisResetShipFlag(orderSn);
                                            }
                                        }
                                        update.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.NO_NEED_CREATE.getCode());
                                        update.setWorkflowStatus(VipJitxWorkflowStateEnum.REJECT.getKey());

                                        //插入审核中间表
                                        OcBOrder updateOrder = new OcBOrder();
                                        updateOrder.setId(workflow.getOrderId());
                                        omsAuditTaskService.createOcBAuditTask(updateOrder, OmsAuditTimeCalculateReason.RESERVE_AUDIT);
                                    }else {
                                        update.setWorkflowSn(resp.getWorkflow_sn());
                                        update.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode());
                                        update.setFailNumber(failNumber + 1);
                                        update.setFailReason(resp.getResult().getMessage());
                                    }
                                    ipBJitxResetShipWorkflowMapper.updateById(update);
                                }
                            }
                        }
                    } else {
                        //调用失败，根据子订单号集合查询JITX中间表，判断子订单号是否存在交易状态=已
                        //发货取消/已揽收取消,若不存在，更新工单创建状态=创建失败，将失败原因记录在备
                        //注中；若存在，更新工单创建状态=创建成功，工单审核状态=取消
                        List<IpBJitxOrder> ipBJitxOrders = ipJitxOrderService.existCancelOrder(workflow.getOrderSnList());
                        IpBJitxResetShipWorkflow update = new IpBJitxResetShipWorkflow();
                        update.setId(workflow.getId());
                        if (CollectionUtils.isNotEmpty(ipBJitxOrders)) {
                            update.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode());
                            update.setWorkflowStatus(VipJitxWorkflowStateEnum.CANCEL.getKey());
                        } else {
                            update.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode());
                            update.setFailNumber(failNumber + 1);
                            update.setFailReason(SplitMessageUtil.splitMsgBySize(result.getResult().getMessage(), SplitMessageUtil.SIZE_500));
                        }
                        ipBJitxResetShipWorkflowMapper.updateById(update);
                    }
                } else {
                    List<IpBJitxOrder> ipBJitxOrders = ipJitxOrderService.existCancelOrder(workflow.getOrderSnList());
                    IpBJitxResetShipWorkflow update = new IpBJitxResetShipWorkflow();
                    update.setId(workflow.getId());
                    if (CollectionUtils.isNotEmpty(ipBJitxOrders)) {
                        update.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode());
                        update.setWorkflowStatus(VipJitxWorkflowStateEnum.CANCEL.getKey());
                    } else {
                        update.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode());
                        update.setFailReason(SplitMessageUtil.splitMsgBySize(v14.getMessage(), SplitMessageUtil.SIZE_500));
                    }
                    ipBJitxResetShipWorkflowMapper.updateById(update);
                }
            }
        } catch (Exception e) {
            log.error("{},定时任务创建发货重置工单异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
        } finally {
            batchUnLockOrder(lockList);
        }

    }

    public void getResetShipWorkflows(List<IpBJitxResetShipWorkflow> shipWorkflows, User operateUser) {
        if (CollectionUtils.isEmpty(shipWorkflows)) {
            return;
        }
        List<Long> idList = shipWorkflows.stream().map(IpBJitxResetShipWorkflow::getId).collect(Collectors.toList());
        List<RedisReentrantLock> lockList = new ArrayList<>(shipWorkflows.size());
        String sqlIds = batchLockOrder(idList, lockList);
        if (lockList.size() < shipWorkflows.size()) {
            batchUnLockOrder(lockList);
            return;
        }
        try {
            List<IpBJitxResetShipWorkflow> conditionList = new ArrayList<>(shipWorkflows.size());
            shipWorkflows.forEach(model -> {
                if (!VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode().equals(model.getCreatedStatus())) {
                    // 发货重置工单创建状态非'创建成功',退出
                    return;
                }
                if (!VipJitxWorkflowStateEnum.CREATE.getKey().equals(model.getWorkflowStatus())
                        && !VipJitxWorkflowStateEnum.DOING.getKey().equals(model.getWorkflowStatus())) {
                    // 发货重置工单状态非('新建'和'处理中'),退出
                    return;
                }
                if (StringUtils.isEmpty(model.getSellerNick()) || StringUtils.isEmpty(model.getVendorId())) {
                    log.info("getResetShipWorkflow sellerNick or VerdorId is empty，workflow：{}", model);
                    IpBJitxResetShipWorkflow workflow = IpBJitxResetShipWorkflow.builder()
                            .id(model.getId()).failReason("获取发货重置工单状态失败,卖家昵称或供应商ID不能为空").build();
                    ipBJitxResetShipWorkflowMapper.updateById(workflow);
                    return;
                }
                if (StringUtils.isEmpty(model.getWorkflowSn())) {
                    log.info("getResetShipWorkflow WorkflowSn is empty，workflow：{}", model);
                    IpBJitxResetShipWorkflow workflow = IpBJitxResetShipWorkflow.builder()
                            .id(model.getId()).failReason("获取发货重置工单状态失败,工单号不能为空").build();
                    ipBJitxResetShipWorkflowMapper.updateById(workflow);
                    return;
                }
                conditionList.add(model);
            });
            if (CollectionUtils.isEmpty(conditionList)) {
                return;
            }
            Map<Long, List<IpBJitxResetShipWorkflow>> GetShipResetWorkflowVopResultListMap =
                    conditionList.stream().collect(Collectors.groupingBy(IpBJitxResetShipWorkflow::getCpCShopId));
            for (Map.Entry<Long, List<IpBJitxResetShipWorkflow>> map : GetShipResetWorkflowVopResultListMap.entrySet()) {
                Long resetWorkflowVopResult = map.getKey();
                List<IpBJitxResetShipWorkflow> logList = map.getValue();
                List<List<IpBJitxResetShipWorkflow>> listList = Lists.partition(logList, 20);
                for (List<IpBJitxResetShipWorkflow> list : listList) {
                    processResetShipResult(list, operateUser);
                }
            }
        } catch (Exception e) {
            log.error("{},定时任务查询发货重置工单异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
        } finally {
            batchUnLockOrder(lockList);
        }
    }

    private void processResetShipResult(List<IpBJitxResetShipWorkflow> workflowList, User operateUser) {
        VipGetShipResetWorkflowRequest request = new VipGetShipResetWorkflowRequest();
        List<String> workflowSns = workflowList.stream().map(IpBJitxResetShipWorkflow::getWorkflowSn).distinct().collect(Collectors.toList());
        List<String> orderSns = workflowList.stream().map(IpBJitxResetShipWorkflow::getOrderSn).distinct().collect(Collectors.toList());
        request.setWorkflow_sn_list(workflowSns);
        request.setOrder_sn_list(orderSns);
        request.setVendor_id(Integer.valueOf(workflowList.get(0).getVendorId()));
        request.setSeller_nick(workflowList.get(0).getSellerNick());
        ValueHolderV14<VipGetShipResetWorkflowResult> v14 = ipRpcService.getShipResetWorkflowVop(request);
        if (v14 == null) {
            this.updateFailReasonById(workflowList, "调用IP发货重置工单接口返回为空", operateUser);
            return;
        }
        VipGetShipResetWorkflowResult shipResetResult = v14.getData();
        if (v14.isOK() && shipResetResult != null) {
            Map<String, List<IpBJitxResetShipWorkflow>> logGroupByOrderSn = workflowList.stream().collect(Collectors.groupingBy(IpBJitxResetShipWorkflow::getWorkflowSn));
            if (SUCCESS_CODE.equals(shipResetResult.getResult().getStatus())) {
                for (VipGetShipResetWorkflowResult.VipGetShipResetWorkflowResp result : shipResetResult.getWorkflows()) {
                    if (log.isDebugEnabled()) {
                        log.debug("调用【获取发货重置工单接口】单条返回结果：{}", JSON.toJSONString(result));
                    }
                    List<IpBJitxResetShipWorkflow> logList = logGroupByOrderSn.get(result.getWorkflow_sn());
                    if (CollectionUtils.isEmpty(logList)) {
                        continue;
                    }
                    if (logList.size() > 1) {
                        log.info("multiple ip_b_jitx_reset_ship_workflow at the same time, orderSn:{}, logList:{}", logList);
                        updateFailReasonById(workflowList, "发货重置订单号重复", operateUser);
                        continue;
                    }
                    IpBJitxResetShipWorkflow workflow = logList.get(0);
                    // 更新发货重置日志表状态
                    IpBJitxResetShipWorkflow shipWorkflow = new IpBJitxResetShipWorkflow();
                    shipWorkflow.setId(workflow.getId());
                    BaseModelUtil.setupUpdateParam(shipWorkflow, operateUser);
                    shipWorkflow.setRejectRemark(result.getReject_remark());
                    shipWorkflow.setUpdateTime(DateUtil.stringToDate(result.getUpdate_time()));
                    shipWorkflow.setWorkflowStatus(result.getStatus().toString());
                    if (Integer.valueOf(VipJitxWorkflowStateEnum.PASS.getKey()).equals(result.getStatus())) {
                        String orderSnList = workflow.getOrderSnList();
                        //通过后移除redis标记
                        if (StringUtils.isNotEmpty(orderSnList)) {
                            String[] orderSnArr = orderSnList.split(",");
                            for (String orderSn : orderSnArr) {
                                this.removeJITXRedisResetShipFlag(orderSn);
                            }
                        }
                    }
                    int row = ipBJitxResetShipWorkflowMapper.updateById(shipWorkflow);
                    if (row <= 0) {
                        log.info("update IpBJitxResetShipWorkflow fail, data:{}", workflow);
                    }else {
                        // 插入审核中间表
                        IpBJitxResetShipWorkflow shipWorkflow1 = ipBJitxResetShipWorkflowMapper.selectById(workflow.getId());
                        if(!ObjectUtils.isEmpty(shipWorkflow1)){
                            OcBOrder updateOrder = new OcBOrder();
                            updateOrder.setId(shipWorkflow1.getOrderId());
                            omsAuditTaskService.createOcBAuditTask(updateOrder, OmsAuditTimeCalculateReason.RESERVE_AUDIT);
                        }
                    }
                }
            } else {
                //根据子订单号查询JITX订单中间表，判断子订单号是否存在交易状态=已发货取消/已揽收取消，若存在，更新工单审核状态=取消
                //若不存在，不更新工单审核状态，将调用返回的失败原因记录在备注中
                for (IpBJitxResetShipWorkflow workflow : workflowList) {
                    List<IpBJitxOrder> ipBJitxOrders = ipJitxOrderService.existCancelOrder(workflow.getOrderSnList());
                    if (CollectionUtils.isNotEmpty(ipBJitxOrders)) {
                        IpBJitxResetShipWorkflow update = new IpBJitxResetShipWorkflow();
                        update.setId(workflow.getId());
                        update.setWorkflowStatus(VipJitxWorkflowStateEnum.CANCEL.getKey());
                        ipBJitxResetShipWorkflowMapper.updateById(workflow);
                    }
                }

            }
        } else {
            log.info("getResetShipWorkflows Error, request:{}, valueHolderV14:{}", request, v14);
            this.updateFailReasonById(workflowList, StringUtils.isEmpty(v14.getMessage()) ? "调用IP发货重置工单接口异常" : v14.getMessage(), operateUser);
        }
    }

    /**
     * 更新失败原因
     *
     * @param workflowList 日志记录
     * @param failReason   失败原因
     * @param operateUser  操作人
     */
    private void updateFailReasonById(List<IpBJitxResetShipWorkflow> workflowList, String failReason, User operateUser) {
        List<Long> idList = workflowList.stream().map(IpBJitxResetShipWorkflow::getId).collect(Collectors.toList());
        for (IpBJitxResetShipWorkflow workflow : workflowList) {
            List<IpBJitxOrder> ipBJitxOrders = ipJitxOrderService.existCancelOrder(workflow.getOrderSnList());
            if (CollectionUtils.isNotEmpty(ipBJitxOrders)) {
                IpBJitxResetShipWorkflow update = new IpBJitxResetShipWorkflow();
                update.setId(workflow.getId());
                update.setWorkflowStatus(VipJitxWorkflowStateEnum.CANCEL.getKey());
                ipBJitxResetShipWorkflowMapper.updateById(update);
                idList.remove(workflow.getId());
            }
        }
        if (CollectionUtils.isNotEmpty(idList)) {
            IpBJitxResetShipWorkflow workflow = new IpBJitxResetShipWorkflow();
            workflow.setFailReason(SplitMessageUtil.splitMsgBySize(failReason, SplitMessageUtil.SIZE_999));
            BaseModelUtil.makeBaseModifyField(workflow, operateUser);
            LambdaQueryWrapper<IpBJitxResetShipWorkflow> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(IpBJitxResetShipWorkflow::getId, idList);
            ipBJitxResetShipWorkflowMapper.update(workflow, queryWrapper);
        }
    }

    /**
     * 检查JITX订单发货重置结果，同时删除发货重置成功的redisKey和更新零售发货单是否唯品会发货重置为否
     *
     * @param workflow
     * @param operateUser
     * @return
     */
    private ValueHolderV14 checkJitxOrderResetShip(IpBJitxResetShipWorkflow workflow, User operateUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.FAIL, null);
        // 通过唯品会getOrdersByOrderSn接口订单信息
        ValueHolderV14 valueHolderV14 = ipRpcService.getJitxOrderByOrderSn(
                workflow.getOrderSn(), operateUser, workflow.getCpCShopId());
        if (valueHolderV14 == null) {
            log.info("ipRpcService getJitxOrderByOrderSn RPC return empty,sourceCode:{}", workflow.getOrderSn());
            v14.setMessage("调用IP服务获取JITX订单信息错误,返回为空");
            return v14;
        }
        if (!valueHolderV14.isOK()) {
            log.info("ipRpcService getJitxOrderByOrderSn RPC Error, sourceCode:{}, valueHolderV14:{}",
                    workflow.getOrderSn(), valueHolderV14);
            v14.setMessage("调用IP服务获取JITX订单信息失败，" + valueHolderV14.getMessage());
            return v14;
        }
        log.info("通过唯品会getOrdersByOrderSn接口订单信息：{}", valueHolderV14);
        JSONArray data = (JSONArray) valueHolderV14.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            JSONObject jsonObject = (JSONObject) data.get(0);
            String orderSn = jsonObject.getString("order_sn");
            String transportNo = jsonObject.getString("transport_no");

        } else {
            log.info("ipRpcService getJitxOrderByOrderSn RPC Data is empty, sourceCode:{}, valueHolderV14:{}",
                    workflow.getOrderSn(), valueHolderV14);
            v14.setMessage("调用IP服务获取JITX订单信息为空");
        }
        return v14;
    }

    public void setJITXRedisResetShipFlag(String tid, Long id) {
        String redisKey = BllRedisKeyResources.getJitxResetShipFlagKey(tid);
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        objRedisTemplate.opsForValue().set(redisKey, id.toString(), 15, TimeUnit.DAYS);
        log.info("修改发货重置标识redisKey:{}", redisKey);
    }

    public String getJITXRedisResetShipFlag(String tid) {
        String redisKey = BllRedisKeyResources.getJitxResetShipFlagKey(tid);
        log.info("发货重置标识redisKey:{}", redisKey);
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        String s = objRedisTemplate.opsForValue().get(tid);
        return s;
    }

    public void removeJITXRedisResetShipFlag(String tid) {
        // 删除重置发货成功的redisKey
        String redisKey = BllRedisKeyResources.getJitxResetShipFlagKey(tid);
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        boolean flag = objRedisTemplate.delete(redisKey);
        log.info("删除发货重置的redisKey：{}, 结果：{}", redisKey, flag);
    }

    /**
     * 重置发货
     *
     * @param id
     * @return
     */
    public ValueHolderV14 resetSendDelivery(Long id, User user) {
        String lockRedisKey = BllRedisKeyResources.buildJitxCreateChangeWarehouseWorkflowKey(id);
        RedisReentrantLock redisReentrantLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisReentrantLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                //查询原单
                IpBJitxResetShipWorkflow workflow = ipBJitxResetShipWorkflowMapper.selectById(id);
                if (log.isDebugEnabled()) {
                    log.debug("{},重置发货数据校验开始：{}", this.getClass().getSimpleName(), JSON.toJSONString(workflow));
                }
                checkResetSendDeliveryData(workflow, user);

                /**工单创建状态*/
                Integer createdStatus = workflow.getCreatedStatus();
                /**工单审核状态*/
                String workflowStatus = workflow.getWorkflowStatus();
                Integer failNumber = workflow.getFailNumber() == null ? 0 : workflow.getFailNumber();
                List<Integer> createdStatusList = new ArrayList<>();
                createdStatusList.add(VipJitxWorkflowCreatedStateEnum.UNCREATE.getCode());
                createdStatusList.add(VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode());

                IpBJitxResetShipWorkflow updateResetShipWorkflow = IpBJitxResetShipWorkflow
                        .builder()
                        .id(id)
                        .ipAddress(user.getLastloginip())
                        .build();
                if (createdStatusList.contains(createdStatus)) {
                    //TODO 调用JITX发货重置创建接口
                    VipCreateShipResetWorkflowRequest request = new VipCreateShipResetWorkflowRequest();
                    List<VipCreateShipResetWorkflowRequest.VipShipResetWorkflowItem> itemCreateRequestList = new ArrayList<>();
                    VipCreateShipResetWorkflowRequest.VipShipResetWorkflowItem itemCreateRequest = new VipCreateShipResetWorkflowRequest.VipShipResetWorkflowItem();
                    itemCreateRequest.setOrder_sn(workflow.getOrderSn());
                    itemCreateRequest.setRequest_id(workflow.getRequestId());
                    itemCreateRequest.setReason_code(workflow.getReasonCode());
                    //TODO mock测试传参
                    itemCreateRequest.setReason_remark(StringUtils.isNotEmpty(workflow.getReasonRemark()) ? workflow.getReasonRemark() : "200");
                    itemCreateRequestList.add(itemCreateRequest);
                    request.setWorkflows(itemCreateRequestList);
                    request.setVendor_id(Integer.valueOf(workflow.getVendorId()));
                    request.setSeller_nick(workflow.getSellerNick());
                    ValueHolderV14<VipCreateShipResetWorkflowResult> v14Reset = ipRpcService.createShipResetWorkflowVop(request);
                    BaseModelUtil.makeBaseModifyField(updateResetShipWorkflow, user);
                    if (v14Reset.isOK() && v14Reset.getData() != null) {
                        VipCreateShipResetWorkflowResult obj = v14Reset.getData();
                        /**工单创建状态*/
                        VipCreateShipResetWorkflowResult.VipCreateShipResetWorkflowResp workflowVopResp = obj.getWorkflows().get(0);
                        VipShipResetWorkflowResult result = workflowVopResp.getResult();
                        if (SUCCESS_CODE.equals(result.getStatus())) {
                            /**接口返回的工单号*/
                            updateResetShipWorkflow.setWorkflowSn(workflowVopResp.getWorkflow_sn());
                            updateResetShipWorkflow.setFailReason("");
                            updateResetShipWorkflow.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode());
                            updateResetShipWorkflow.setWorkflowStatus(VipJitxWorkflowStateEnum.CREATE.getKey());
                            updateResetShipWorkflow.setFailNumber(0);
                        } else {
                            List<IpBJitxOrder> ipBJitxOrders = ipJitxOrderService.existCancelOrder(workflow.getOrderSnList());
                            if (CollectionUtils.isNotEmpty(ipBJitxOrders)) {
                                //调用失败，根据子订单号集合查询JITX中间表，判断子订单号是否存在交易状态=已
                                //发货取消/已揽收取消,若不存在，更新工单创建状态=创建失败，将失败原因记录在备
                                //注中；若存在，更新工单创建状态=创建成功，工单审核状态=取消
                                updateResetShipWorkflow.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode());
                                updateResetShipWorkflow.setWorkflowStatus(VipJitxWorkflowStateEnum.CANCEL.getKey());
                            } else {
                                /**接口工单创建返回message*/
                                String message = obj.getResult().getMessage();
                                if (StringUtils.isNotEmpty(result.getMessage())) {
                                    message = result.getMessage();
                                }
                                updateResetShipWorkflow.setFailNumber(failNumber++);
                                updateResetShipWorkflow.setFailReason(message);
                                updateResetShipWorkflow.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode());
                            }
                        }
                        ipBJitxResetShipWorkflowMapper.updateById(updateResetShipWorkflow);
                    } else {
                        List<IpBJitxOrder> ipBJitxOrders = ipJitxOrderService.existCancelOrder(workflow.getOrderSnList());
                        if (CollectionUtils.isNotEmpty(ipBJitxOrders)) {
                            //调用失败，根据子订单号集合查询JITX中间表，判断子订单号是否存在交易状态=已
                            //发货取消/已揽收取消,若不存在，更新工单创建状态=创建失败，将失败原因记录在备
                            //注中；若存在，更新工单创建状态=创建成功，工单审核状态=取消
                            updateResetShipWorkflow.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode());
                            updateResetShipWorkflow.setWorkflowStatus(VipJitxWorkflowStateEnum.CANCEL.getKey());
                        } else {
                            String message = v14Reset.getMessage();
                            updateResetShipWorkflow.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode());
                            updateResetShipWorkflow.setFailReason(message);
                            updateResetShipWorkflow.setFailNumber(failNumber++);
                        }
                        ipBJitxResetShipWorkflowMapper.updateById(updateResetShipWorkflow);
                    }
                    return ValueHolderV14Utils.custom(v14Reset.getCode(), v14Reset.getMessage(), v14Reset.getData());
                }
                List<String> workflowStatusList = new ArrayList<>();
                workflowStatusList.add(VipJitxWorkflowStateEnum.CREATE.getKey());
                workflowStatusList.add(VipJitxWorkflowStateEnum.DOING.getKey());

                if (VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode().equals(createdStatus) && workflowStatusList.contains(workflowStatus)) {
                    //TODO 调用JITX发货重置查询接口
                    VipGetShipResetWorkflowRequest request = new VipGetShipResetWorkflowRequest();
                    List<String> workflowSns = Lists.newArrayList(workflow.getWorkflowSn());
                    request.setWorkflow_sn_list(workflowSns);
                    request.setVendor_id(Integer.valueOf(workflow.getVendorId()));
                    request.setSeller_nick(workflow.getSellerNick());
                    ValueHolderV14<VipGetShipResetWorkflowResult> v14query = ipRpcService.getShipResetWorkflowVop(request);
                    BaseModelUtil.makeBaseModifyField(updateResetShipWorkflow, user);
                    if (v14query.isOK() && v14query.getData() != null) {
                        VipGetShipResetWorkflowResult obj = v14query.getData();
                        if (SUCCESS_CODE.equals(obj.getResult().getStatus())) {
                            if (CollectionUtils.isEmpty(obj.getWorkflows())) {
                                return ValueHolderV14Utils.getFailValueHolder("未返回当前工单信息");
                            }
                            /**工单状态描述*/
                            VipGetShipResetWorkflowResult.VipGetShipResetWorkflowResp result = obj.getWorkflows().get(0);
                            // 更新发货重置日志表状态
                            IpBJitxResetShipWorkflow shipWorkflow = new IpBJitxResetShipWorkflow();
                            shipWorkflow.setId(workflow.getId());
                            BaseModelUtil.setupUpdateParam(shipWorkflow, user);
                            shipWorkflow.setRejectRemark(result.getReject_remark());
                            shipWorkflow.setWorkflowStatus(result.getStatus().toString());
                            shipWorkflow.setUpdateTime(DateUtil.stringToDate(result.getUpdate_time()));
                            if (Integer.valueOf(VipJitxWorkflowStateEnum.PASS.getKey()).equals(result.getStatus())) {
                                String orderSnList = workflow.getOrderSnList();
                                //通过后移除redis标记
                                if (StringUtils.isNotEmpty(orderSnList)) {
                                    String[] orderSnArr = orderSnList.split(",");
                                    for (String orderSn : orderSnArr) {
                                        this.removeJITXRedisResetShipFlag(orderSn);
                                    }
                                }
                            }
                            int row = ipBJitxResetShipWorkflowMapper.updateById(shipWorkflow);
                        } else {
                            List<IpBJitxOrder> ipBJitxOrders = ipJitxOrderService.existCancelOrder(workflow.getOrderSnList());
                            if (CollectionUtils.isNotEmpty(ipBJitxOrders)) {
                                //根据子订单号查询JITX订单中间表，判断子订单号是否存在交易状态=已
                                //发货取消/已揽收取消，若存在，更新工单审核状态=取消，若不存在，不更新工单审
                                //核状态，将调用返回的失败原因记录在备注中；
                                updateResetShipWorkflow.setWorkflowStatus(VipJitxWorkflowStateEnum.CANCEL.getKey());
                            } else {
                                updateResetShipWorkflow.setFailReason(SplitMessageUtil.splitMsgBySize(obj.getResult().getMessage(), SplitMessageUtil.SIZE_500));
                            }
                            int row = ipBJitxResetShipWorkflowMapper.updateById(updateResetShipWorkflow);
                        }
                    } else {
                        log.error("JitX重置发货单 ID:{} 调用JitX发货重置查询失败！", id);
                        List<IpBJitxOrder> ipBJitxOrders = ipJitxOrderService.existCancelOrder(workflow.getOrderSnList());
                        if (CollectionUtils.isNotEmpty(ipBJitxOrders)) {
                            //调用失败，根据子订单号集合查询JITX中间表，判断子订单号是否存在交易状态=已
                            //发货取消/已揽收取消,若不存在，更新工单创建状态=创建失败，将失败原因记录在备
                            //注中；若存在，更新工单创建状态=创建成功，工单审核状态=取消
                            updateResetShipWorkflow.setWorkflowStatus(VipJitxWorkflowStateEnum.CANCEL.getKey());
                            int row = ipBJitxResetShipWorkflowMapper.updateById(updateResetShipWorkflow);
                        }
                    }
                    return ValueHolderV14Utils.custom(v14query.getCode(), v14query.getMessage(), v14query.getData());
                }
            } else {
                throw new NDSException("正在被操作中，请稍后重试！");
            }
        } catch (Exception e) {
            log.info("JITX发货重置异常：{}", Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        } finally {
            redisReentrantLock.unlock();
        }
        return ValueHolderV14Utils.getSuccessValueHolder("执行成功！");
    }

    /**
     * 重置发货基础数据校验
     *
     * @param ipBJitxResetShipWorkflow
     */
    public void checkResetSendDeliveryData(IpBJitxResetShipWorkflow ipBJitxResetShipWorkflow, User user) {
        /**工单创建状态*/
        Integer createdStatus = ipBJitxResetShipWorkflow.getCreatedStatus();
        /**工单审核状态*/
        String workflowStatus = ipBJitxResetShipWorkflow.getWorkflowStatus();

        List<String> workflowStatusList = new ArrayList<>();
        workflowStatusList.add(VipJitxWorkflowStateEnum.CREATE.getKey());
        workflowStatusList.add(VipJitxWorkflowStateEnum.DOING.getKey());

        if (VipJitxWorkflowCreatedStateEnum.UNCREATE.getCode().equals(createdStatus) ||
                VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode().equals(createdStatus) ||
                (VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode().equals(createdStatus) && workflowStatusList.contains(workflowStatus))) {
        } else {
            throw new NDSException(Resources.getMessage("工单状态不满足，不允许重置发货！", user.getLocale()));
        }
    }

    private String batchLockOrder(List<Long> orderIdList, List<RedisReentrantLock> lockList) {
        StringBuilder sb = new StringBuilder();
        Iterator<Long> iterator = orderIdList.iterator();
        while (iterator.hasNext()) {
            Long next = iterator.next();
            try {
                String lockRedisKey = BllRedisKeyResources.buildJitxCreateChangeWarehouseWorkflowKey(next);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                if (redisLock.tryLock(bllRedisLockOrderUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockList.add(redisLock);
                    sb.append(",").append(next);
                    continue;
                }
            } catch (Exception e) {
                log.error("单据锁定异常.Id#{}", next);
            }
            iterator.remove();
        }
        return sb.length() > 1 ? sb.substring(1) : null;
    }

    private void batchUnLockOrder(List<RedisReentrantLock> locks) {
        for (RedisReentrantLock lock : locks) {
            try {
                lock.unlock();
            } catch (Exception ex) {
                log.error("合并订单解锁异常.Id#{}", lock.getLockId());
            }
        }
    }
}
