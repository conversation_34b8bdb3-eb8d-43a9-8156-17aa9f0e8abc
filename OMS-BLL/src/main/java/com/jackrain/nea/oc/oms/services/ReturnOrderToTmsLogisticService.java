package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.store.model.request.tms.OrderTrackBackRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * className: ReturnOrderToTmsLogisticService
 * description: 退换货单推送物流信息给TMS追踪物流轨迹
 *
 * <AUTHOR>
 * create: 2021-12-07
 * @since JDK 1.8
 */
@Component
@Slf4j
public class ReturnOrderToTmsLogisticService {

    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private StRpcService stRpcService;


    public void sendTraceInfo(JSONObject params){

        Integer pageSize = 200;
        if(ObjectUtils.isEmpty(params.getInteger("pageSize"))){
            pageSize = params.getInteger("pageSize");
        }

        //查询可用、未传TMS、待退货入库、物流公司物流单号不为空的退换货单
        List<OcBReturnOrder> returnOrderList = returnOrderMapper.selectList(new LambdaQueryWrapper<OcBReturnOrder>()
                .eq(OcBReturnOrder::getReturnStatus, ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal())
                .eq(OcBReturnOrder::getIsSendTmsLogistic,"0")
                .eq(OcBReturnOrder::getIsactive, R3CommonResultConstants.VALUE_Y)
                .isNotNull(OcBReturnOrder::getLogisticsCode)
                .isNotNull(OcBReturnOrder::getCpCLogisticsEcode)
                .last(" limit " + pageSize));

        if(log.isDebugEnabled()){
            log.debug(" 推送TMS追踪物流，退换货单：{}", JSON.toJSONString(returnOrderList));
        }

        returnOrderList = returnOrderList.stream()
                .filter(o -> !ObjectUtils.isEmpty(o.getLogisticsCode()) && !ObjectUtils.isEmpty(o.getCpCLogisticsEcode()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(returnOrderList)){
            log.warn(" 推送TMS追踪物流,暂无需推送的退换货单。。。");
            return;
        }

        User user = SystemUserResource.getRootUser();
        for(OcBReturnOrder returnOrder: returnOrderList){
            try {
                singleSend(returnOrder,user);
            }catch (Exception e){
                log.error(" 推送TMS追踪物流,操作异常：{}", Throwables.getStackTraceAsString(e));
            }
        }

    }

    /**
     * 单个推送
     * @param returnOrder 退换货单
     * @param user 用户信息
     */
    private void singleSend(OcBReturnOrder returnOrder,User user){

        if(log.isDebugEnabled()){
            log.debug(" 推送TMS追踪物流,当前退换货单：{}",JSON.toJSONString(returnOrder));
        }

        //判断店铺策略是否开启推送物流轨迹
        StCShopStrategyDO shopStrategyDO = stRpcService.selectOcStCShopStrategyByCpCshopId(returnOrder.getCpCShopId());
        if(ObjectUtils.isEmpty(shopStrategyDO) ||
                !R3CommonResultConstants.VALUE_Y.equals(shopStrategyDO.getIsReturnOrderTmsTrack())){
            log.warn(" 店铺【{}】无店铺策略开启退单推送物流轨迹配置",returnOrder.getCpCShopTitle());
            return;
        }

        OrderTrackBackRequest request = new OrderTrackBackRequest();
        request.setOwnerCompanyCode(returnOrder.getCpCLogisticsEcode());
        request.setTrackNo(returnOrder.getLogisticsCode());
        request.setLegNo(returnOrder.getBillNo());
        request.setFromLocationName(returnOrder.getCpCStoreEcode());
        request.setToName(returnOrder.getReceiveName());
        request.setToProvince(returnOrder.getReceiverProvinceName());
        request.setToCity(returnOrder.getReceiverCityName());
        request.setToAddress(returnOrder.getReceiveAddress());
        request.setToMobile(returnOrder.getReceiveMobile());
        request.setToTelPhone(returnOrder.getReceivePhone());
        //1、正常订单，2、宝尊仓订单，3、退换货单
        request.setExtcol35("3");
        ValueHolderV14 result = sgRpcService.queryOrderTrackBack(request);
        Assert.isTrue(result.isOK(),result.getMessage());

        if(log.isDebugEnabled()){
            log.debug(" 推送TMS追踪物流,推送结果：{}",JSON.toJSONString(result));
        }

        //更新退换货单为已推送TMS追踪
        OcBReturnOrder update = new OcBReturnOrder();
        update.setId(returnOrder.getId());
        update.setIsSendTmsLogistic("1");
        BaseModelUtil.makeBaseModifyField(update,user);
        update.setModifierename(user.getEname());
        returnOrderMapper.updateById(update);
    }
}
