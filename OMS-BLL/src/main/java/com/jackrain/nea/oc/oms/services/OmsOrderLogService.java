package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.kafka.producer.DefaultKafkaProducer;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.OmsKafkaConfig;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.BllWebUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单日志服务
 *
 * @author: heliu
 * @since: 2019/3/6
 * create at : 2019/3/6 17:22
 */
@Component
@Slf4j
public class OmsOrderLogService {

    private static final String LOG_TABLE_NAMAE = "oc_b_order_log";
    private static final List<Integer> SELECT_RANGE = Lists.newArrayList(10, 20, 30, 50, 100, 200, 500);

    @Autowired
    private DefaultKafkaProducer kafkaProducer;
    @Autowired
    private OmsKafkaConfig omsKafkaConfig;

    @Autowired
    private OcBOrderLogMapper ocBOrderLogMapper;

    @Autowired
    private BllWebUtil webUtil;

    @TargetDataSource(name = "adb")
    public JSONObject getLogByOrderId(Long orderId, String searchdata) {
        JSONObject o = JSON.parseObject(searchdata);
        Integer startindex = o.getInteger("startindex");
        Integer size = o.getInteger("range");
        Integer count = ocBOrderLogMapper.countByOrderId(orderId);
        List<OcBOrderLog> logs = ocBOrderLogMapper.getLogByOrderId(orderId, startindex, size);

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("defaultrange", 10);
        dataMap.put("totalRowCount", count);
        dataMap.put("rowCount", logs.size());
        dataMap.put("tabth", buildTabthList());
        dataMap.put("selectrange", SELECT_RANGE);
        dataMap.put("row", buildVal(logs));
        JSONObject data = new JSONObject();
        data.put("datas", dataMap);
        data.put("code", 0);
        return data;
    }

    @TargetDataSource(name = "adb")
    public OcBOrderLog processSingleList(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        OcBOrderLog ocBOrderLog = JSONUtil.toBean(value, OcBOrderLog.class);
        try {
            ocBOrderLogMapper.insert(ocBOrderLog);
            return null;
        } catch (Exception e) {
            log.error(LogUtil.format("写入订单操作日志，kafka消息消费异常，报文：{},异常信息：{}",
                    "OcBOrderLogKafkaListener.processSingleList"), consumerRecord.value(), Throwables.getStackTraceAsString(e));
            return ocBOrderLog;
        }
    }

    private JSONArray buildVal(List<OcBOrderLog> logs) {
        JSONArray rowList = new JSONArray();

        for (OcBOrderLog log : logs) {
            JSONObject row = new JSONObject();
            JSONObject logMessageColobj = new JSONObject();
            logMessageColobj.put("val", log.getLogMessage());
            row.put("LOG_MESSAGE", logMessageColobj);

            JSONObject logTypeColobj = new JSONObject();
            logTypeColobj.put("val", OrderLogTypeEnum.enumToStringBykey(log.getLogType()));
            row.put("LOG_TYPE", logTypeColobj);

            JSONObject createDateColobj = new JSONObject();
            createDateColobj.put("val", DateUtil.format(log.getCreationdate(), "yyyy-MM-dd HH:mm:ss"));
            row.put("CREATIONDATE", createDateColobj);

            JSONObject uaerNameColobj = new JSONObject();
            uaerNameColobj.put("val", log.getUserName());
            row.put("USER_NAME", uaerNameColobj);

            JSONObject ownerEnameColobj = new JSONObject();
            ownerEnameColobj.put("val", log.getOwnerename());
            row.put("OWNERENAME", ownerEnameColobj);

            JSONObject idColobj = new JSONObject();
            idColobj.put("val", log.getId());
            row.put("ID", idColobj);
            rowList.add(row);
        }
        return rowList;
    }

    private List<OcBOrderLogTabth> buildTabthList() {
        List<OcBOrderLogTabth> tabthList = new ArrayList<>();
        OcBOrderLogTabth idTabth = new OcBOrderLogTabth();
        idTabth.setName("ID");
        idTabth.setInputname("ID");
        idTabth.setColname("ID");

        OcBOrderLogTabth userNameTabth = new OcBOrderLogTabth();
        userNameTabth.setName("用户名称");
        userNameTabth.setInputname("USER_NAME");
        userNameTabth.setColname("USER_NAME");

        OcBOrderLogTabth logTypeTabth = new OcBOrderLogTabth();
        // 注意：此处有误，应更正为logTypeTabth.setName而不是userNameTabth.setName
        logTypeTabth.setName("日志类型");
        logTypeTabth.setInputname("LOG_TYPE");
        logTypeTabth.setColname("LOG_TYPE");
        OcBOrderLogTabth logMessageTabth = new OcBOrderLogTabth();
        // 同样，此处应更正为logMessageTabth.setName等
        logMessageTabth.setName("日志内容");
        logMessageTabth.setInputname("LOG_MESSAGE");
        logMessageTabth.setColname("LOG_MESSAGE");

        OcBOrderLogTabth ownerEnameTabth = new OcBOrderLogTabth();
        ownerEnameTabth.setName("创建人姓名");
        ownerEnameTabth.setInputname("OWNERENAME");
        ownerEnameTabth.setColname("OWNERENAME");

        OcBOrderLogTabth createDateTabth = new OcBOrderLogTabth();
        createDateTabth.setName("创建时间");
        createDateTabth.setInputname("CREATIONDATE");
        createDateTabth.setColname("CREATIONDATE");

        tabthList.add(idTabth);
        tabthList.add(userNameTabth);
        tabthList.add(logTypeTabth);
        tabthList.add(logMessageTabth);
        tabthList.add(ownerEnameTabth);
        tabthList.add(createDateTabth);
        return tabthList;
    }



    @TargetDataSource(name = "adb")
    public OcBOrderLog selectOcBOrderLogByOrderId(Long orderId) {
        return ocBOrderLogMapper.selectOcBOrderLogByOrderId(orderId);
    }

    /**
     * @param orderId
     * @param id
     * fixme 后面要修改 换成ADB。可以不走异步？
     */
    public void updateOcBOrderLogModifieddateById(Long orderId, Long id) {
        ocBOrderLogMapper.updateOcBOrderLogModifieddateById(orderId, id);
    }

    /**
     * 用户日志信息动态获取保存日志
     *
     * @param orderId      订单ID
     * @param billNo       订单编号
     * @param logType      日志类型
     * @param logMessage   日志消息
     * @param param        日志参数
     * @param errorMessage 错误信息
     * @param operateUser  用户对象
     */
    public void addUserOrderLog(long orderId, String billNo, String logType, String logMessage,
                                String param, String errorMessage, User operateUser) {

        long recordTimestamp = System.currentTimeMillis();
        int partition = (int) (orderId % omsKafkaConfig.getTotalnumber());
        try {
            if (StringUtils.isNotEmpty(logMessage) && logMessage.length() > 5000) {
                logMessage = logMessage.substring(0, 5000);
            }
            OcBOrderLog ocBOrderLog = new OcBOrderLog();
            long autoId = ModelUtil.getSequence(LOG_TABLE_NAMAE);
            ocBOrderLog.setId(autoId);
            ocBOrderLog.setBillNo(billNo);
            ocBOrderLog.setOcBOrderId(orderId);
            ocBOrderLog.setLogType(logType);
            ocBOrderLog.setLogMessage(logMessage);
            ocBOrderLog.setLogParam(param);
            ocBOrderLog.setErrorInfo(errorMessage);
            if (operateUser == null) {
                operateUser = SystemUserResource.getRootUser();
            }
            ocBOrderLog.setAdOrgId((long) operateUser.getOrgId());
            ocBOrderLog.setOwnername(operateUser.getEname());
            ocBOrderLog.setOwnerename(operateUser.getEname());
            ocBOrderLog.setAdClientId((long) operateUser.getClientId());
            ocBOrderLog.setOwnerid(Long.valueOf(operateUser.getId()));
            ocBOrderLog.setCreationdate(new Date());
            ocBOrderLog.setModifierid(Long.valueOf(operateUser.getId()));
            ocBOrderLog.setModifieddate(new Date());
            ocBOrderLog.setModifiername(operateUser.getName());
            ocBOrderLog.setUserName(operateUser.getName());
            // 前端传入IP
            if (StringUtils.isNotBlank(operateUser.getLastloginip())) {
                ocBOrderLog.setIpAddress(operateUser.getLastloginip());
            } else {
                // 再去判断底层获取IP地址是否为空
                if (StringUtils.isNotBlank(webUtil.getWebRequestIpAddress())) {
                    ocBOrderLog.setIpAddress(webUtil.getWebRequestIpAddress());
                } else {
                    ocBOrderLog.setIpAddress("127.0.0.1");
                }
            }
            String messageKey = orderId + ":" + recordTimestamp + ":" + autoId;
            kafkaProducer.sendByTopic(omsKafkaConfig.getOrderLogTopic(), partition, recordTimestamp, messageKey, JSONUtil.toJsonStr(ocBOrderLog));
        } catch (Exception e){
            log.error(LogUtil.format("拆入日志信息异常:{}", "拆入日志信息异常"), Throwables.getStackTraceAsString(e));
        }
    }

    public OcBOrderLog getOcBOrderLog(long orderId, String billNo, String logType, String logMessage,
                                      String param, String errorMessage, User operateUser) {

        OcBOrderLog ocBOrderLog = new OcBOrderLog();
        long autoId = ModelUtil.getSequence(LOG_TABLE_NAMAE);
        ocBOrderLog.setId(autoId);
        ocBOrderLog.setBillNo(billNo);
        ocBOrderLog.setOcBOrderId(orderId);
        ocBOrderLog.setLogType(logType);
        ocBOrderLog.setLogMessage(logMessage);
        ocBOrderLog.setLogParam(param);
        String ipAddress = operateUser.getLastloginip();
        //前端传入IP
        if (StringUtils.isNotBlank(operateUser.getLastloginip())) {
            ocBOrderLog.setIpAddress(ipAddress);
        } else {
            //再去判断底层获取IP地址是否为空
            if (StringUtils.isNotBlank(webUtil.getWebRequestIpAddress())) {
                ocBOrderLog.setIpAddress(webUtil.getWebRequestIpAddress());
            } else {
                ocBOrderLog.setIpAddress("127.0.0.1");
            }
        }
        ocBOrderLog.setErrorInfo(errorMessage);
        if (operateUser == null) {
            operateUser = SystemUserResource.getRootUser();
        }
        ocBOrderLog.setAdOrgId((long) operateUser.getOrgId());
        ocBOrderLog.setOwnername(operateUser.getEname());
        ocBOrderLog.setOwnerename(operateUser.getEname());
        ocBOrderLog.setAdClientId((long) operateUser.getClientId());
        ocBOrderLog.setOwnerid(Long.valueOf(operateUser.getId()));
        ocBOrderLog.setCreationdate(new Date());
        ocBOrderLog.setModifierid(Long.valueOf(operateUser.getId()));
        ocBOrderLog.setModifieddate(new Date());
        ocBOrderLog.setModifiername(operateUser.getName());
        ocBOrderLog.setUserName(operateUser.getName());
        return ocBOrderLog;
    }

    /**
     * 批量保存log
     *
     * @param logs
     */
    public void save(List<OcBOrderLog> logs) {
        if (CollectionUtils.isNotEmpty(logs)) {
            // 批量发送消息
            for (OcBOrderLog orderLog : logs) {
                long recordTimestamp = System.currentTimeMillis();
                int partition = (int) (recordTimestamp % omsKafkaConfig.getTotalnumber());
                String messageKey = orderLog.getOcBOrderId() + ":" + recordTimestamp + ":" + orderLog.getId();
                try {
                    kafkaProducer.sendByTopic(omsKafkaConfig.getOrderLogTopic(), partition, recordTimestamp, messageKey, JSONUtil.toJsonStr(orderLog));
                } catch (Exception e) {
                    log.error(LogUtil.format("拆入日志信息异常:{}", "拆入日志信息异常"), Throwables.getStackTraceAsString(e));
                }
            }
        }
    }

    @Data
    public class OcBOrderLogTabth implements Serializable {
        private static final long serialVersionUID = 6028649413580063348L;
        private String name;
        private String inputname;
        private String colname;
        private List<OcBOrderLogComboBox> combobox;
    }


    @Data
    public class OcBOrderLogComboBox implements Serializable {
        private static final long serialVersionUID = -1093573833611718601L;
        private String limitdesc;
        private String limitval;

    }
}
