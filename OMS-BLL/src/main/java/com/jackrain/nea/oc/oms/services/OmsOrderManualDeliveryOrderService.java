package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @Description 手工修改为平台发货服务
 * @Date 2020/1/6
 */
@Component
@Slf4j
public class OmsOrderManualDeliveryOrderService {
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    /**
     * @param param
     * @param user
     * <AUTHOR>
     * @Description 手工修改为平台发货
     * @Date 2020/1/6
     * @Return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    public ValueHolderV14 doManualDeliveryOrder(JSONObject param, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        //判断入参
        if (null == param) {
            throw new NDSException(Resources.getMessage("订单手动更改为平台发货请求参数不能为空!", user.getLocale()));
        }

        JSONArray jsonArray = param.getJSONArray("ids");

        if (CollectionUtils.isEmpty(jsonArray)) {
            throw new NDSException(Resources.getMessage("请选择需要更改的记录!", user.getLocale()));
        }

        List<Long> ids = JSONArray.parseArray(JSON.toJSONString(jsonArray), Long.class);

        QueryWrapper<OcBOrder> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(OcBOrder::getId, ids);
        //查询单据具体信息
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(ocBOrderList)) {
            throw new NDSException(Resources.getMessage("请选择需要更改的记录!", user.getLocale()));
        }
        //校验单据状态
        HashMap<OcBOrder, Integer> map = checkParam(ocBOrderList);

        int totalOrder = ocBOrderList.size();
        int failOrder = 0;
        if (map.size() > 0) {
            //存在校验不通过的单子
            failOrder = map.size();
            //收集失败的单据
            List<OcBOrder> failOrderList = new ArrayList<>();
            for (Map.Entry<OcBOrder, Integer> ocBOrderIntegerEntry : map.entrySet()) {
                failOrderList.add(ocBOrderIntegerEntry.getKey());
                if (1 == ocBOrderIntegerEntry.getValue()) {

                } else {
                }


            }
            //去除校验不通过的单据
            ocBOrderList.removeAll(failOrderList);
        }
        //更新单据状态为平台发货记录日志
        if (CollectionUtils.isEmpty(ocBOrderList)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单手动更改为平台发货成功 0，订单审核失败 " + failOrder);
            return vh;
        }
        OmsOrderManualDeliveryOrderService bean = ApplicationContextHandle.getBean(OmsOrderManualDeliveryOrderService.class);
        for (OcBOrder ocBOrder : ocBOrderList) {
            boolean flag = bean.changeOrderDeliver(ocBOrder, user);
            if (!flag) {
                failOrder++;
            }
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("订单手动更改为平台发货成功 " + (totalOrder - failOrder) + "，失败 " + failOrder);
        return vh;
    }

    /**
     * @param ocBOrderList
     * <AUTHOR>
     * @Description 校验单据状态
     * @Date 2020/1/6
     * @Return java.util.HashMap<java.lang.Long, java.lang.Integer>
     */
    private HashMap<OcBOrder, Integer> checkParam(List<OcBOrder> ocBOrderList) {
        //map的value是1为非手工新增，2为非已取消
        HashMap<OcBOrder, Integer> hashMap = new HashMap<>();
        for (OcBOrder ocBOrder : ocBOrderList) {
            //判断是否为手工单
            if (!OcOrderTagEum.TAG_HAND.getVal().equals(ocBOrder.getOrderSource())) {
                hashMap.put(ocBOrder, 1);
            }
            //判断状态为已取消或者系统作废
            if (!(ocBOrder.getOrderStatus().equals(OmsOrderStatus.CANCELLED.toInteger()) ||
                    ocBOrder.getOrderStatus().equals(OmsOrderStatus.SYS_VOID.toInteger()))) {
                hashMap.put(ocBOrder, 2);
            }
        }
        return hashMap;
    }

    /**
     * @param ocBOrder
     * @param user
     * <AUTHOR>
     * @Description 修改订单状态和记录日志
     * @Date 2020/1/6
     * @Return boolean
     */
    @Transactional
    public boolean changeOrderDeliver(OcBOrder ocBOrder, User user) {
        OcBOrder ocBOrderDo = new OcBOrder();
        ocBOrderDo.setId(ocBOrder.getId());
        ocBOrderDo.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());

        ocBOrderDo.setModifierid(Long.valueOf(user.getId()));
        ocBOrderDo.setModifiername(user.getName());
        ocBOrderDo.setModifierename(user.getEname());
        ocBOrderDo.setModifieddate(new Date());

        boolean flag = omsOrderService.updateOrderInfo(ocBOrderDo);
        if (flag) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_DELIVERY.getKey(),
                    "订单手动更改为平台发货成功!", "", "", user);
            return true;

        } else {
            return false;
        }

    }
}
