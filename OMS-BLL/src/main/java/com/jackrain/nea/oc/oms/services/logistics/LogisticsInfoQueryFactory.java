package com.jackrain.nea.oc.oms.services.logistics;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.LogisticsTableEnum;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR> wang<PERSON><PERSON>
 * @since : 2022/6/16
 * description :
 */
public class LogisticsInfoQueryFactory {

    protected static final Map<LogisticsTableEnum, LogisticsInfoQueryApi> HANDLE_MAP = new ConcurrentHashMap<>();

    /**
     * get table
     *
     * @param tableEnum 表名
     * @return LogisticsInfoQueryApi
     */
    public static LogisticsInfoQueryApi getHandle(LogisticsTableEnum tableEnum) {
        if (!HANDLE_MAP.containsKey(tableEnum)) {
            throw new NDSException("该表[" + tableEnum + "]无对应的处理类");
        }
        return HANDLE_MAP.get(tableEnum);
    }
}
