package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.AcFPriceSettingLogMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.AcFPriceSetting;
import com.jackrain.nea.oc.oms.model.table.AcFPriceSettingLog;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * className: AcFPriceSettingLogService
 * description:
 *
 * <AUTHOR>
 * create: 2021-10-26
 * @since JDK 1.8
 */
@Component
@Slf4j
public class AcFPriceSettingLogService {

    @Autowired
    private AcFPriceSettingLogMapper logMapper;


    @Async
    public void saveLog(AcFPriceSetting newModel, AcFPriceSetting oldModel, User user){
        List<AcFPriceSettingLog> logDOList = new ArrayList<>();
        try {
            if(ObjectUtils.isEmpty(newModel) || ObjectUtils.isEmpty(user) || ObjectUtils.isEmpty(newModel.getId())){
                log.debug(" 日志记录异常--newModel：{}--oldModel：{}--user：{}",newModel,oldModel,user);
                return ;
            }

            Field[] fields = newModel.getClass().getDeclaredFields();
            for(Field field : fields){
                ApiModelProperty modelProperty = field.getAnnotation(ApiModelProperty.class);
                if(ObjectUtils.isEmpty(modelProperty)){
                    continue;
                }
                String bValue = null;
                //日志类型:0-新增/1-修改
                String type = "0";
                if(!ObjectUtils.isEmpty(oldModel)){
                    bValue = getStringValue(field,oldModel);
                    type = "1";
                }
                String aValue = getStringValue(field,newModel);

                //比较当前字段是否做了更新操作
                if(modelProperty.allowEmptyValue() && null != aValue && !aValue.equals(bValue)){
                    AcFPriceSettingLog settingLog = new AcFPriceSettingLog();
                    settingLog.setId(ModelUtil.getSequence(OcCommonConstant.AC_F_PRICE_SETTING_LOG));
                    settingLog.setAcFPriceSettingId(newModel.getId());
                    settingLog.setBefore(bValue);
                    settingLog.setAfter(aValue);
                    settingLog.setLogType(type);
                    settingLog.setLogMessage(modelProperty.value());
                    BaseModelUtil.makeBaseCreateField(settingLog,user);
                    settingLog.setModifierename(user.getEname());
                    settingLog.setOwnerename(user.getEname());
                    logDOList.add(settingLog);
                }
            }

            if(CollectionUtils.isNotEmpty(logDOList)){
                logMapper.batchInsert(logDOList);
            }

        }catch (Exception e){
            log.error(" 记录日志异常{}", Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 获取属性的字符串值
     * @param field 属性
     * @param object 对象
     * @return
     */
    public static String getStringValue(Field field,Object object){
        if(ObjectUtils.isEmpty(field) || ObjectUtils.isEmpty(object)){
            return null;
        }
        Object value = getFieldValueByName(field.getName(), object);
        if(null == value){
            return null;
        }
        String result;
        if(value instanceof Date){
            result = DateUtil.dateTimeSecondsFormatter.format(value);
        }else if(value instanceof BigDecimal){
            result = ((BigDecimal) value).setScale(2, RoundingMode.HALF_DOWN).toString();
        }else {
            result = String.valueOf(value);
        }
        return result;
    }

    /**
     * 利用反射  根据属性名获取属性值
     * @param fieldName 属性名
     * @param o 对象
     * @return
     */
    public static Object getFieldValueByName(String fieldName, Object o) {
        try {
            if (o instanceof Map) {
                return ((Map) o).get(fieldName);
            } else {
                String firstLetter = fieldName.substring(0, 1).toUpperCase();
                String getter = "get" + firstLetter + fieldName.substring(1);
                Method method = o.getClass().getMethod(getter, new Class[]{});
                Object value = method.invoke(o, new Object[]{});
                return value;
            }

        } catch (Exception e) {
            log.error(" 获取属性值异常{}", Throwables.getStackTraceAsString(e));
            return null;
        }
    }
}
