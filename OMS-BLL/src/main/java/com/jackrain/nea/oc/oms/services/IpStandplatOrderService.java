package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.oc.request.StandplatOrderCreateModel;
import com.jackrain.nea.resource.SystemTableNames;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 中间表通用转单处理服务
 *
 * @author: ming.fz
 * @since: 2019-07-2
 * create at : 2019-07-2 10:38
 */
@Component
@Slf4j
public class IpStandplatOrderService {

    @Autowired
    private IpBStandplatOrderMapper ipStandplatOrderMapper;

    @Autowired
    private IpBStandplatOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OmsBeforeShipmentReturnService omsBeforeShipmentReturnService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBReturnOrderBatchAddService batchAddService;
    @Autowired
    private OmsReturnUtil omsReturnUtil;
    @Autowired
    private CpRpcService cpRpcService;

//    @Autowired
//    private R3MqSendHelper sendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    protected OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private StRpcService stRpcService;

    /**
     * 转单topic
     */
    @Value("${r3.oms.call.transfer.topic:}")
    private String sendTransferMqTopic;

    /**
     * 转单tag
     */
    @Value("${r3.oms.call.transfer.tag:}")
    private String sendTransferMqTag;

    /**
     * 查询单对象
     *
     * @param tid
     * @return
     */
    public IpBStandplatOrder selectIpBStandplatOrderByTid(String tid) {
        IpBStandplatOrder orderInfo = this.ipStandplatOrderMapper.selectStandplatOrderByTid(tid);
        return orderInfo;
    }

    /**
     * 依据OrderNo进行查询中间表通用信息数据
     *
     * @param orderNo 通用订单号
     * @return 中间表通用信息数据
     */
    public IpStandplatOrderRelation selectStandplatOrder(String orderNo) {
        IpBStandplatOrder orderInfo = this.ipStandplatOrderMapper.selectStandplatOrderByTid(orderNo);

        if (orderInfo == null) {
            return null;
        }

        IpStandplatOrderRelation StandplatOrderRelation = new IpStandplatOrderRelation();
        long StandplatOrderId = orderInfo.getId();
        List<IpBStandplatOrderItemEx> orderItemList = this.orderItemMapper.selectOrderItemList(StandplatOrderId);
        String receiverAddress = orderInfo.getReceiverAddress();
        if(StringUtils.isNotBlank(receiverAddress)){
            receiverAddress = receiverAddress.replaceAll(",", "::::");
        }
        // 替换之后,需重新赋值
        orderInfo.setReceiverAddress(receiverAddress);
        StandplatOrderRelation.setStandPlatOrderItemList(orderItemList);
        StandplatOrderRelation.setStandplatOrder(orderInfo);

        return StandplatOrderRelation;
    }

    /**
     * @param tid
     * @return
     * @20200830 查活动ID（通用平台-爱库存）
     */
    public String selectActivityidByTid(String tid) {
        return ipStandplatOrderMapper.selectActivityidByTid(tid);
    }

    /**
     * 更新通用中间表订单状态值
     *
     * @param orderNo             订单编号
     * @param transferOrderStatus 转换订单状态
     * @param remarks             备注信息
     * @param abnormalType        异常类型
     * @return
     */
    public boolean updateStandPlatOrderTransStatus(String orderNo, TransferOrderStatus transferOrderStatus,
                                                   String remarks, Integer abnormalType) {
        if (remarks != null && remarks.length() > IpBStandplatOrderMapper.MAX_REMARK_LENGTH) {
            remarks = remarks.substring(0, IpBStandplatOrderMapper.MAX_REMARK_LENGTH - 1);
        }
        return this.ipStandplatOrderMapper.updateOrderIsTrans(orderNo, transferOrderStatus.toInteger(), remarks, new Date(System.currentTimeMillis()), abnormalType) > 0;
    }

    /**
     * 获取实例
     *
     * @return
     */
    public static IpStandplatOrderService getInstance() {
        return ApplicationContextHandle.getBean(IpStandplatOrderService.class);
    }


    /**
     * 通用订单转单发货前处理
     *
     * @param ipStandplatOrderRelation
     * @return false 继续下一步 true 处理完成 结束流程
     */
    public void currencyDeliverGoodsBefore(IpStandplatOrderRelation ipStandplatOrderRelation, List<OcBOrder> ocBOrders, User user) {
        //根据明细tid查询所有的订单信息
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 进入通用转单发货前转换 参数:{}", JSONObject.toJSONString(ipStandplatOrderRelation));
        }
        IpBStandplatOrder standplatOrder = ipStandplatOrderRelation.getStandplatOrder();
        List<IpBStandplatOrderItemEx> standPlatOrderItemList = ipStandplatOrderRelation.getStandPlatOrderItemList();

        ocBOrders = ocBOrders.stream().filter(p -> !(OmsOrderStatus.SYS_VOID.toInteger().equals(p.getOrderStatus())
                || OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ocBOrders)) {
            return;
        }
        //判断订单中间表的数据
        boolean flag = true;
        if (TaoBaoOrderStatus.TRADE_CANCELED.equals(standplatOrder.getStatus())) {
            //主表为建议取消  则将订单下的明细全部标记退款成
            StringBuilder sb = new StringBuilder();
            for (OcBOrder ocBOrder : ocBOrders) {
                try {
                    this.handleOrderCancel(user, standplatOrder, ocBOrder, 1);
                } catch (Exception e) {
                    log.error(this.getClass().getName() + " 通用订单转换状态为取消处理失败", e);
                    flag = false;
                    sb.append(e.getMessage());
                }
            }
            if (!flag) {
                throw new NDSException(sb.toString());
            }
        }
        //判断明细是否有退款的状态
        standPlatOrderItemList = standPlatOrderItemList.stream().filter(p -> Objects.nonNull(p.getRefundStatus())
                && !String.valueOf(OmsOrderRefundStatus.UNREFUND.toInteger()).equals(p.getRefundStatus())).collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isNotEmpty(standPlatOrderItemList)) {
            try {
                this.handleOrderItemCancel(user, standPlatOrderItemList, ocBOrders, standplatOrder, 0);
            } catch (Exception e) {
                log.error(this.getClass().getName() + " 通用订单转换状态为取消处理失败11", e);
                flag = false;
                sb.append(e.getMessage());
            }
        }
        if (!flag) {
            throw new NDSException(sb.toString());
        }
    }

    /**
     * 根据通用订单明细表处理退款
     *
     * @param user
     * @param standPlatOrderItemList
     * @param ocBOrders
     */
    private void handleOrderItemCancel(User user, List<IpBStandplatOrderItemEx> standPlatOrderItemList,
                                       List<OcBOrder> ocBOrders, IpBStandplatOrder standplatOrder,Integer isBack) {
        String tid = standplatOrder.getTid();
        List<String> guestBacks = new ArrayList<>();
        guestBacks.add(String.valueOf(OmsOrderRefundStatus.WAITBUYERRETURNGOODS.toInteger()));
        guestBacks.add(String.valueOf(OmsOrderRefundStatus.WAITSELLERCONFIRMGOODS.toInteger()));
        guestBacks.add(String.valueOf(OmsOrderRefundStatus.SUCCESS.toInteger()));
        for (IpBStandplatOrderItemEx standplatOrderItemEx : standPlatOrderItemList) {
            String refundStatus = standplatOrderItemEx.getRefundStatus();
            String oid = standplatOrderItemEx.getOid();
            if (StringUtils.isEmpty(refundStatus) || StringUtils.isEmpty(oid)) {
                continue;
            }
            int standplatRefundStatus = Integer.parseInt(refundStatus);
            for (OcBOrder ocBOrder : ocBOrders) {
                Integer orderStatus = ocBOrder.getOrderStatus();
                if (OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(orderStatus)) {
                    throw new NDSException("订单待分配状态,暂不处理!");
                }
                // 通过oid查询 需退款的明细信息
                List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemsByOoid(ocBOrder.getId(), oid);
                if (CollectionUtils.isEmpty(ocBOrderItems)) {
                    omsReturnUtil.handleRefundComplete(ocBOrder);
                    continue;
                }
                if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                        || OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)) {
                    //如果是奶卡订单，则不生成退换货单
                    boolean isMilkCardOrder = omsRefundOrderService.checkIsMilkCardOrder(ocBOrderItems);
                    //如果是虚拟订单，则不生成退换货单
                    boolean isVirtual = OrderTypeEnum.DIFFPRICE.getVal().equals(ocBOrder.getOrderType());
                    if(isMilkCardOrder || isVirtual){
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("通用订单取消，奶卡订单或者虚拟订单不生成退换货单orderId:{};",
                                    "IpStandplatOrderService"),ocBOrder.getId());
                        }
                        continue;
                    }
                    // 生成退货单
                    if(guestBacks.contains(standplatOrderItemEx.getRefundStatus())){
                        StCShopStrategyDO stCShopStrategyDO = stRpcService.selectOcStCShopStrategyByCpCshopId(ocBOrders.get(0).getCpCShopId());
                        if (stCShopStrategyDO != null) {
                            // 判断是否没有勾选 订单不创建售后。 如果勾选了 则不生成退换货单
                            if (StringUtils.isEmpty(stCShopStrategyDO.getRefundNotFromOrder()) || ObjectUtil.equal(stCShopStrategyDO.getRefundNotFromOrder(), YesNoEnum.ZERO.getKey())) {
                                this.insertRefundOrder(ocBOrder.getId(), ocBOrder.getBillNo(), tid, user, ocBOrderItems, isBack);
                                continue;
                            }
                        }
                        continue;
                    }
                }
                //是否退款中
                OcBOrder order = new OcBOrder();
                //是否退款中
                order.setIsInreturning(1);
                order.setId(ocBOrder.getId());
                omsOrderService.updateOrderInfo(order);
                // HOLD单
                order.setIsInreturning(1);
                order.setId(ocBOrder.getId());
                omsOrderService.updateOrderInfo(order);
                //是否已经拦截 Hold单统一调用 HOLD单方法
                order.setIsInterecept(1);
                order.setBillNo(ocBOrder.getBillNo());
                OmsOrderRelation relation = new OmsOrderRelation();
                relation.setOcBOrder(ocBOrder);
                relation.setOcBOrderItems(ocBOrderItems);
                ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
                if (OmsOrderRefundStatus.WAITSELLERAGREE.toInteger() == standplatRefundStatus ||
                        OmsOrderRefundStatus.WAITBUYERRETURNGOODS.toInteger() == standplatRefundStatus ||
                        OmsOrderRefundStatus.WAITSELLERCONFIRMGOODS.toInteger() == standplatRefundStatus) {
                    omsBeforeShipmentReturnService.taoBaoRefundStatusAgree(relation, user);
                    for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                        ocBOrderItemMapper.updateOrderItemPtReturnStatusByOrderId(
                                ocBOrderItem.getOcBOrderId(), TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode(), ocBOrderItem.getId());
                    }
                }
                if (standplatRefundStatus == OmsOrderRefundStatus.SUCCESS.toInteger()) {
                    omsBeforeShipmentReturnService.refundStatusIsSuccess(relation, user);
                    for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                        ocBOrderItemMapper.updateOrderItemPtReturnStatusByOrderId(
                                ocBOrderItem.getOcBOrderId(), TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode(), ocBOrderItem.getId());
                    }
                }
                if (standplatRefundStatus == OmsOrderRefundStatus.CLOSED.toInteger() ||
                        standplatRefundStatus == OmsOrderRefundStatus.SELLERREFUSEBUYER.toInteger() ||
                        standplatRefundStatus == OmsOrderRefundStatus.UNREFUND.toInteger()) {
                    omsBeforeShipmentReturnService.orderStatusIsClosed(relation, user);
                    for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                        ocBOrderItemMapper.updateOrderItemPtReturnStatusByOrderId(
                                ocBOrderItem.getOcBOrderId(), TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode(), ocBOrderItem.getId());
                    }
                }
            }
        }
    }

    /**
     * 处理订单中间表状态为 取消的逻辑
     *
     * @param user
     * @param standplatOrder
     * @param ocBOrder
     */
    private void handleOrderCancel(User user, IpBStandplatOrder standplatOrder, OcBOrder ocBOrder,Integer isBack) {
        Integer orderStatus = ocBOrder.getOrderStatus();
        if (OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(orderStatus)) {
            throw new NDSException("订单待分配状态,暂不处理!");
        }
        //获取该订单下所有的
        String tid = standplatOrder.getTid();
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemListByTid(ocBOrder.getId(), standplatOrder.getTid());
        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            omsReturnUtil.handleRefundComplete(ocBOrder);
            return;
        }
        OcBOrder order = new OcBOrder();
        //是否退款中
        order.setIsInreturning(1);
        order.setId(ocBOrder.getId());
        omsOrderService.updateOrderInfo(order);
        if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                || OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)) {
            //如果是奶卡订单，则不生成退换货单
            boolean isMilkCardOrder = omsRefundOrderService.checkIsMilkCardOrder(ocBOrderItems);
            //如果是虚拟订单，则不生成退换货单
            boolean isVirtual = OrderTypeEnum.DIFFPRICE.getVal().equals(ocBOrder.getOrderType());
            if (isMilkCardOrder || isVirtual) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("通用订单取消，奶卡订单或者虚拟订单不生成退换货单orderId:{};",
                            "IpStandplatOrderService"), ocBOrder.getId());
                }
                return;
            }

            StCShopStrategyDO stCShopStrategyDO = stRpcService.selectOcStCShopStrategyByCpCshopId(ocBOrder.getCpCShopId());
            if (stCShopStrategyDO != null) {
                // 判断是否没有勾选 订单不创建售后。 如果勾选了 则不生成退换货单
                if (StringUtils.isNotEmpty(stCShopStrategyDO.getRefundNotFromOrder()) && ObjectUtil.equal(stCShopStrategyDO.getRefundNotFromOrder(), YesNoEnum.ONE.getKey())) {
                    return;
                }
            }
            // 生成退货单
            this.insertRefundOrder(ocBOrder.getId(), ocBOrder.getBillNo(), tid, user, ocBOrderItems, isBack);
            return;
        }
        //是否已经拦截 Hold单统一调用 HOLD单方法
        order.setIsInterecept(1);
        order.setBillNo(ocBOrder.getBillNo());
        ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
        //走退款成功的逻辑
        OmsOrderRelation relation = new OmsOrderRelation();
        relation.setOcBOrder(ocBOrder);
        relation.setOcBOrderItems(ocBOrderItems);
        omsBeforeShipmentReturnService.refundStatusIsSuccess(relation, user);
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            ocBOrderItemMapper.updateOrderItemPtReturnStatusByOrderId(
                    ocBOrderItem.getOcBOrderId(), TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode(), ocBOrderItem.getId());
        }
    }


    private void insertRefundOrder(Long orderId, String billNo, String tid, User user, List<OcBOrderItem> ocBOrderItems,Integer isBack) {
        if (log.isDebugEnabled()) {
            log.debug(" IpBJingDongSaRefundService.insertRefundOrder tid=" + tid + ",orderId=" + orderId + ",billNo=" + billNo);
        }
        //查询退换货订单是否存在，不存在，调用新增服务
        Long id = ES4ReturnOrder.findIdByOrderIdAndTid(orderId, tid);
        if (null == id || id <= 0) {
            List<String> listString = new ArrayList<>();
            id = batchAddService.addReturnOrder(orderId, isBack, listString, user, "通用转单发货前退款生成退换货单", tid, ocBOrderItems);
            log.error(this.getClass().getName() + " 订单id=" + orderId + ",通用转单发货前退款生成退换货单,返回参数:{}", listString);
            if (CollectionUtils.isNotEmpty(listString)) {
                //新增退单添加订单操作日志
                omsOrderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.REFUND_ORDER_ADD.getKey(),
                        listString.get(0), null, null, user);
            }
        }
    }


    /**
     * 处理wing同步到中台的通用订单
     * @param standplatOrderCreateModel
     * @return
     */
    public ValueHolderV14 saveIpStandplatOrder(StandplatOrderCreateModel standplatOrderCreateModel){
        ValueHolderV14 v14 = saveOrder(standplatOrderCreateModel);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("saveOrder.result={}",
                    "IpStandplatOrderService.saveIpStandplatOrder"),JSON.toJSONString(v14));
        }
        if(v14.isOK()){
            JSONArray data = (JSONArray)v14.getData();
            //发送MQ信息进行转单
            sendMQ(data);
            v14.setData(null);
        }
        return v14;
    }

    /**
     * 创建通用订单
     * @param standplatOrderCreateModel
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveOrder(StandplatOrderCreateModel standplatOrderCreateModel){
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("saveOrder.request={}",
                    "IpStandplatOrderService.saveIpStandplatOrder"),JSON.toJSONString(standplatOrderCreateModel));
        }
        ValueHolderV14 v14 = ValueHolderV14Utils.getSuccessValueHolder("创建成功！");
        //校验tid在中台是否存在
        String tid = standplatOrderCreateModel.getTid();
        Long oldOrder = ipStandplatOrderMapper.selectIdByTid(tid);
        if(oldOrder != null){
            return ValueHolderV14Utils.getSuccessValueHolder("订单已存在！");
        }
        //单据明细
        List<StandplatOrderCreateModel.OrderItem> orderItemList = standplatOrderCreateModel.getItemList();
        IpBStandplatOrder ipBStandplatOrder = new IpBStandplatOrder();
        BeanUtils.copyProperties(standplatOrderCreateModel,ipBStandplatOrder);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("saveOrder.ipBStandplatOrder={}",
                    "IpStandplatOrderService.saveIpStandplatOrder"),JSON.toJSONString(ipBStandplatOrder));
        }
        //补全店铺信息
        ipBStandplatOrder.setCpCPlatformEcode(standplatOrderCreateModel.getPlatform());
        ipBStandplatOrder.setCpCPlatformId(Long.valueOf(standplatOrderCreateModel.getPlatform()));
        complementShopInfo(ipBStandplatOrder);
        //收货人emoji表情去除
        String receiverName = ipBStandplatOrder.getReceiverName();
        if (StringUtils.isNotBlank(receiverName)) {
            ipBStandplatOrder.setReceiverName(receiverName.replaceAll("[^\\u0000-\\uFFFF]", ""));
        }
        //单据状态
        ipBStandplatOrder.setStatus(standplatOrderCreateModel.getOrderStatus());
        //ID赋值
        Long id = ModelUtil.getSequence(SystemTableNames.IP_B_STANDPLAT_ORDER);
        ipBStandplatOrder.setId(id);
        ipBStandplatOrder.setIstrans(0);
        BaseModelUtil.initialBaseModelSystemField(ipBStandplatOrder);
        ipStandplatOrderMapper.insert(ipBStandplatOrder);
        //保存明细数据
        List<IpBStandplatOrderItem> orderItems = new ArrayList<>();
        for(StandplatOrderCreateModel.OrderItem orderItem:orderItemList){
            IpBStandplatOrderItem standplatOrderItem = new IpBStandplatOrderItem();
            BeanUtils.copyProperties(orderItem,standplatOrderItem);
            standplatOrderItem.setSkuId(orderItem.getSkuid());
            standplatOrderItem.setNumIid(orderItem.getNumiid());
            standplatOrderItem.setNum(orderItem.getQty());
            Long itemId = ModelUtil.getSequence(SystemTableNames.IP_B_STANDPLAT_ORDER_ITEM);
            standplatOrderItem.setId(itemId);
            standplatOrderItem.setIpBStandplatOrderId(id);
            standplatOrderItem.setTid(ipBStandplatOrder.getTid());
            standplatOrderItem.setOuterSkuId(orderItem.getPsCSkuEcode());
            if(standplatOrderItem.getOid() == null || "".equals(standplatOrderItem.getOid())){
                standplatOrderItem.setOid(ipBStandplatOrder.getTid() + orderItem.getSkuid());
            }
            BaseModelUtil.initialBaseModelSystemField(standplatOrderItem);
            orderItems.add(standplatOrderItem);
        }
        if(!CollectionUtils.isEmpty(orderItems)){
            orderItemMapper.batchInsert(orderItems);
        }

        //数据保存后 立即发送MQ消息进行转单
        JSONArray data = new JSONArray();
        JSONObject obj = new JSONObject();
        obj.put("orderId",id);
        obj.put("orderNo",tid);
        data.add(obj);
        v14.setData(data);
        return v14;
    }

    /**
     * 补全店铺信息
     * @param ipBStandplatOrder
     */
    public void complementShopInfo(IpBStandplatOrder ipBStandplatOrder){
        //根据店铺编码查询店铺信息
        String shopEcode = ipBStandplatOrder.getCpCShopEcode();
        ValueHolderV14<CpShop> v14 = cpRpcService.queryByShopCode(shopEcode);
        if(v14.isOK()){
            CpShop cpShop = v14.getData();
            ipBStandplatOrder.setCpCShopId(cpShop.getId());
            ipBStandplatOrder.setSellerNick(cpShop.getSellerNick());
        }else{
            log.error(LogUtil.format("根据店铺编码:{}查询店铺信息失败:{}","complementShopInfo",shopEcode),
                    shopEcode,v14.getMessage());
        }
    }

    /**
     * 发送转单MQ消息
     * @param data
     */
    private void sendMQ(JSONArray data) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Wing同步到OMS通用订单开始发送转单MQ消息Data:{}",
                    "IpStandplatOrderService.saveIpStandplatOrder"),JSON.toJSONString(data));
        }
        if (CollectionUtils.isNotEmpty(data)) {
            JSONArray sendArr = new JSONArray();
            for (Object datum : data) {
                JSONObject tempObj = JSONObject.parseObject(datum.toString(), Feature.OrderedField);
                OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
                orderMqInfo.setChannelType(ChannelType.STANDPLAT);
                orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
                orderMqInfo.setOrderType(OrderType.NORMAL);
                orderMqInfo.setOrderId(tempObj.getLongValue("orderId"));
                orderMqInfo.setOrderNo(tempObj.getString("orderNo"));
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(orderMqInfo), Feature.OrderedField);
                sendArr.add(jsonObject);
            }
            String messageId = null;
            try {
                MqSendResult result = defaultProducerSend.sendTopic(Mq5Constants.TOPIC_R3_OC_OMS_CALL_TRANSFER, Mq5Constants.TAG_OPERATEMQORDER, sendArr.toJSONString(), null);
                messageId = result.getMessageId();
//                messageId = sendHelper.sendMessage(sendArr
//                        , sendTransferMqTopic
//                        , sendTransferMqTag);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("Wing同步到OMS通用订单发送转单MQ消息成功,messageId:{},mqMessageTopic:{},mqMessageTag:{},messageData:{}",
                            "IpStandplatOrderService.saveIpStandplatOrder"),messageId,"R3_OC_OMS_CALL_TRANSFER","OperateMqOrder",JSON.toJSONString(sendArr));
                }
            } catch (Exception e) {
                log.error(LogUtil.format("Wing同步到OMS通用订单发送转单MQ消息失败:{}","IpStandplatOrderService.saveIpStandplatOrder"),
                        Throwables.getStackTraceAsString(e)
                        );
            }
        }
    }

}
