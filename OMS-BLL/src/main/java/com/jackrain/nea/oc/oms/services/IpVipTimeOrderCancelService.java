package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.share.api.out.SgBShareOutCmd;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.IpBCancelTimeOrderVipItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBCancelTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBCancelTimeOrderVipRelation;
import com.jackrain.nea.oc.oms.model.request.TimeOrderVoidSgSendRequest;
import com.jackrain.nea.oc.oms.model.table.IpBCancelTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBCancelTimeOrderVipItem;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipItemEx;
import com.jackrain.nea.oc.oms.nums.TimeOrderOutEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 中间表唯品会时效订单取消处理服务
 *
 * @author: chenxiulou
 * @since: 2019-01-22
 * create at : 2019-01-22 15:23
 */
@Component
@Slf4j
public class IpVipTimeOrderCancelService {

    @Autowired
    private IpBCancelTimeOrderVipMapper cancelOrderMapper;

    @Autowired
    private IpBCancelTimeOrderVipItemMapper cancelItemMapper;
    @Autowired
    private TimeOrderVoidSgSendService timeOrderVoidSgSendService;

    @Autowired
    private IpBTimeOrderVipMapper ipBTimeOrderVipMapper;

    /**
     * @param occupiedOrderSn
     * @param user
     * @return
     * @Description 调用释放库存-取消
     * <AUTHOR>
     * @date 2019-08-20 2019-08-20
     */
    public ValueHolderV14 releaseTimeOrderStock(String occupiedOrderSn, User user, Integer type) {
        //SystemUserResource.getRootUser()
        String remark = "";
        if (TimeOrderOutEnum.MAUUAL.getKey().equals(type)) {
            remark = "手动释放【" + occupiedOrderSn + "】成功";
        } else if (TimeOrderOutEnum.TRANSFORMATION.getKey().equals(type)) {
            remark = "取消时效订单【" + occupiedOrderSn + "】释放成功";
        }

        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            log.info(this.getClass().getName() + " 释放时效订单库存！ occupiedOrderSn：" + occupiedOrderSn);
            TimeOrderVoidSgSendRequest request = new TimeOrderVoidSgSendRequest();
            request.setOccupiedOrderSn(occupiedOrderSn);
            request.setUser(user);
            request.setRemark(remark);
            request.setIsCancel(true);
            v14 = timeOrderVoidSgSendService.voidSgSendV14(request);
            return v14;
        } catch (Exception ex) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("释放时效订单库存异常：" + ex.toString());
            return v14;
        }
    }

    /**
     * 依据OrderNo进行查询中间表信息数据
     *
     * @param orderNo 订单号
     * @return 中间表信息数据
     */
    public IpBCancelTimeOrderVipRelation selectCancelTimeOrder(String orderNo) {
        IpBCancelTimeOrderVip orderInfo = this.cancelOrderMapper.selectCancelTimeOrderByOccupiedOrderSn(orderNo);

        if (orderInfo == null) {
            return null;
        }
        IpBCancelTimeOrderVipRelation timeOrderRelation = new IpBCancelTimeOrderVipRelation();
        List<IpBCancelTimeOrderVipItem> orderItemList = this.cancelItemMapper.selectOrderItemList(orderInfo.getId());
        timeOrderRelation.setCancelTimeOrderItemList(orderItemList);
        timeOrderRelation.setCancelTimeOrderVip(orderInfo);

        return timeOrderRelation;
    }

    /**
     * 更新中间表订单状态值
     *
     * @param orderNo             订单编号
     * @param transferOrderStatus 转换订单状态
     * @param remarks             备注信息
     * @return 更新是否成功。true-成功；false-失败
     */
    public boolean updateTimeOrderTransStatus(String orderNo, TransferOrderStatus transferOrderStatus,
                                              String remarks) {
        //boolean isUpdateTransNum = transferOrderStatus == TransferOrderStatus.TRANSFERRED;
        boolean isUpdateTransNum = true;
        if (remarks != null && remarks.length() > IpBCancelTimeOrderVipMapper.MAX_REMARK_LENGTH) {
            remarks = remarks.substring(0, IpBCancelTimeOrderVipMapper.MAX_REMARK_LENGTH - 1);
        }
        int result = this.cancelOrderMapper.updateCancelTimeOrderIsTrans(orderNo, transferOrderStatus.toInteger(), isUpdateTransNum, remarks);
        if (result > 0) {
            this.updateCacelTimeOrderES(orderNo);
        }
        return result > 0;
    }

    /**
     * 更新中间表订单ES
     *
     * @param orderNo 订单编号
     */
    public void updateCacelTimeOrderES(String orderNo) {
        String indexName = OcElasticSearchIndexResources.IP_B_CANCEL_TIME_ORDER_VIP_INDEX_NAME;
        String typeName = OcElasticSearchIndexResources.IP_B_CANCEL_TIME_ORDER_VIP_TYPE_NAME;
        String itemTypeName = OcElasticSearchIndexResources.IP_B_CANCEL_TIME_ORDER_VIP_ITEM_TYPE_NAME;
        IpBCancelTimeOrderVipRelation newOrderRelation = this.selectCancelTimeOrder(orderNo);
//        try {
//            if (!SpecialElasticSearchUtil.indexExists(indexName)) {
//                SpecialElasticSearchUtil.indexCreate(IpBCancelTimeOrderVipItem.class, IpBCancelTimeOrderVip.class);
//            }
//            SpecialElasticSearchUtil.indexDocument(indexName, typeName, newOrderRelation.getCancelTimeOrderVip(),
//                    newOrderRelation.getOrderId());
//            if (newOrderRelation.getCancelTimeOrderItemList() != null) {
//                SpecialElasticSearchUtil.indexDocuments(indexName, itemTypeName,
//                        newOrderRelation.getCancelTimeOrderItemList(), "IP_B_CANCEL_TIME_ORDER_VIP_ID");
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    /**
     * 更新取消时效订单信息
     * @param cancelTimeOrderVip
     * @param orderStatus
     * @param transStatus
     * @param remarks
     * @param isUpdateTransCount
     * @return
     */
    public boolean updateCancelTimeOrderData(IpBCancelTimeOrderVip cancelTimeOrderVip,Integer orderStatus, Integer transStatus, String remarks,Boolean isUpdateTransCount,User operateUser) {
        /**平台时效订单号*/
        String orderNo = cancelTimeOrderVip.getOccupiedOrderSn();
        if (remarks != null && remarks.length() > IpBCancelTimeOrderVipMapper.MAX_REMARK_LENGTH) {
            remarks = remarks.substring(0, IpBCancelTimeOrderVipMapper.MAX_REMARK_LENGTH - 1);
        }
        IpBCancelTimeOrderVip updateData = new IpBCancelTimeOrderVip();
        if(orderStatus != null){
            updateData.setStatus(orderStatus);
        }
        if(transStatus != null){
            updateData.setIstrans(transStatus);
        }
        updateData.setSysremark(remarks);
        if(isUpdateTransCount){
            Long transCount = cancelTimeOrderVip.getTransCount() == null?0:cancelTimeOrderVip.getTransCount();
            transCount +=1;
            updateData.setTransCount(transCount);
            updateData.setTransDate(new Date());
        }
        updateData.setModifieddate(new Date());
        updateData.setModifierid(Long.valueOf(operateUser.getId()));
        updateData.setModifiername(operateUser.getName());
        updateData.setModifierename(operateUser.getEname());
        int result = cancelOrderMapper.update(updateData,new QueryWrapper<IpBCancelTimeOrderVip>()
                .eq("occupied_order_sn",orderNo));
        if (result > 0) {
            this.updateCacelTimeOrderES(cancelTimeOrderVip.getOccupiedOrderSn());
        }
        return result > 0;
    }



}
