package com.jackrain.nea.oc.oms.mapperservice;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.oc.oms.mapper.OcBOrderOutStockRecordMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderOutStockRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName OcBOrderOutStockRecordMapperService
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/5/16 19:20
 * @Version 1.0
 */
@Service
@Slf4j
public class OcBOrderOutStockRecordMapperService extends ServiceImpl<OcBOrderOutStockRecordMapper, OcBOrderOutStockRecord> {

    @Override
    public boolean updateById(OcBOrderOutStockRecord outStockRecord) {
        Assert.notNull(outStockRecord.getOcBOrderId(), "OcBOrderId不能为空");
        Assert.notNull(outStockRecord.getId(), "id不能为空");
        Wrapper<OcBOrderOutStockRecord> updateWrapper = new UpdateWrapper<OcBOrderOutStockRecord>().lambda()
                .eq(OcBOrderOutStockRecord::getOcBOrderId, outStockRecord.getOcBOrderId())
                .eq(OcBOrderOutStockRecord::getId, outStockRecord.getId());
        return baseMapper.update(outStockRecord, updateWrapper) > 0;
    }

}
