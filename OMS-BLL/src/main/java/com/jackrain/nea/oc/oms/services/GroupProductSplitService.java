package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.util.BigDecimalUtil;
import com.jackrain.nea.ps.model.SkuType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组合商品解析服务
 *
 * @author: lahand
 * @since: 2020/4/17
 * create at : 2020/4/17 10:45
 */
@Slf4j
@Component
public class GroupProductSplitService {

    class SkuRatio {
        private Long skuId;
        // 占比
        private BigDecimal ratio;

        public Long getSkuId() {
            return skuId;
        }

        public void setSkuId(Long skuId) {
            this.skuId = skuId;
        }

        public BigDecimal getRatio() {
            return ratio;
        }

        public void setRatio(BigDecimal ratio) {
            this.ratio = ratio;
        }
    }

    /**
     * 每个SKU 占 多个SKU比例
     *
     * @param groupProductDetailList        SKU明细列表
     * @param groupProductDetailSkuAmtTotal SKU明细列表中[零售价 * 数量]合计
     * @return
     */
    public List<SkuRatio> getSkuRatioList(List<OcBOrderItem> groupProductDetailList, BigDecimal groupProductDetailSkuAmtTotal) {
        // 按照（零售价*数量）合计 倒序
        Collections.sort(groupProductDetailList, new Comparator<OcBOrderItem>() {
            @Override
            public int compare(OcBOrderItem o1, OcBOrderItem o2) {
                return o2.getPriceList().multiply(o2.getQty()).compareTo(o1.getPriceList().multiply(o1.getQty()));
            }
        });
        // 过滤出protype==2的数据
        List<OcBOrderItem> skuProType2 = groupProductDetailList.stream().filter(o -> o.getProType() == SkuType.COMBINE_PRODUCT).collect(Collectors.toList());
        int size = skuProType2.size();
        List<SkuRatio> skuRatioList = new ArrayList<>(size);
        BigDecimal one = BigDecimal.ONE;
        BigDecimal ratioTotal = BigDecimal.ZERO;
        for (int i = 0; i < size; i++) {
            SkuRatio skuRatio = new SkuRatio();
            OcBOrderItem orderItem = skuProType2.get(i);
            BigDecimal ratio = BigDecimal.ZERO;
            int num = i + 1;
            // 最后一条时
            if (num == size && ratioTotal.compareTo(BigDecimal.ZERO) > 0) {
                // 最后一个占比 = 1 - 前几个占比合计
                ratio = one.subtract(ratioTotal);
            } else {
                if (groupProductDetailSkuAmtTotal.compareTo(BigDecimal.ZERO) != 0) {
                    // 占比 = （零售价 * 数量）/ 多个SKU的[零售价 * 数量]合计
                    ratio = (orderItem.getPriceList().multiply(orderItem.getQty())).divide(groupProductDetailSkuAmtTotal, 2, BigDecimal.ROUND_HALF_UP);
                }
                // 占比和
                ratioTotal = ratioTotal.add(ratio);
            }
            /* 设定返回值*/
            skuRatio.setSkuId(orderItem.getId());
            skuRatio.setRatio(ratio);
            skuRatioList.add(skuRatio);
        }
        return skuRatioList;
    }

    /**
     * 获取组合商品Protype=2的（零售价 * 数量）合计
     *
     * @param list
     * @return
     */
    public BigDecimal getTotalAmt(List<OcBOrderItem> list) {
        BigDecimal totalAmt = BigDecimal.ZERO;
        for (OcBOrderItem orderItem : list) {
            if (orderItem.getProType() == SkuType.COMBINE_PRODUCT) {
                // SKU金额 = 零售价 * 数量
                BigDecimal skuAmt = orderItem.getPriceList().multiply(orderItem.getQty());
                // SKU金额合计
                totalAmt = totalAmt.add(skuAmt);
            }
        }
        return totalAmt;
    }

    /**
     * 获取组合商品明细head
     *
     * @param orderItemList
     */
    public OcBOrderItem getGroupProduct(List<OcBOrderItem> orderItemList) {
        for (OcBOrderItem orderItem : orderItemList) {
            if (orderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                return orderItem;
            }
        }
        return null;
    }

    /**
     * 平摊组合商品下SKU相关金额
     *
     * @param orderItemList 组合商品下SKU相关金额（只包含（protype == 4 || protype == 2））
     */
    public void splitGroupProduct(List<OcBOrderItem> orderItemList) {
        // 获取组合商品head
        OcBOrderItem groupProductHead = this.getGroupProduct(orderItemList);
        if (groupProductHead != null) {
            this.splitGroupProduct(groupProductHead, orderItemList);
        }
    }

    /**
     * 根据组合商品头信息，算出平摊到该组合商品下挂的SKU的相关金额
     *
     * @param groupProductHead       组合商品Head（是Protype == 4）
     * @param groupProductDetailList 组合商品下挂的SKU列表(只包含protype == 2)
     */
    public void splitGroupProduct(OcBOrderItem groupProductHead, List<OcBOrderItem> groupProductDetailList) {
        // 多个SKU金额合计[零售价 * 数量]
        BigDecimal groupProductDetailSkuAmtTotal = this.getTotalAmt(groupProductDetailList);
        // 每个SKU占比
        List<SkuRatio> skuRatioList = this.getSkuRatioList(groupProductDetailList, groupProductDetailSkuAmtTotal);

        /* 平摊Protype==2的相关金额*/
        for (OcBOrderItem orderItem : groupProductDetailList) {

            if (orderItem.getProType() == SkuType.COMBINE_PRODUCT) {

                for (SkuRatio skuRatio : skuRatioList) {
                    if (orderItem.getId().equals(skuRatio.getSkuId())) {
                        // 实际成交金额 = 组合商品成交金额 * 当前SKU的占比
                        orderItem.setRealAmt(BigDecimalUtil.multiply(groupProductHead.getRealAmt(), skuRatio.getRatio()));
                        // 平摊金额 = 组合商品平摊金额 * 当前SKU的占比
                        orderItem.setOrderSplitAmt(BigDecimalUtil.multiply(groupProductHead.getOrderSplitAmt(), skuRatio.getRatio()));
                        // 优惠金额 = 组合商品优惠金额 * 当前SKU的占比
                        orderItem.setAmtDiscount(BigDecimalUtil.multiply(groupProductHead.getAmtDiscount(), skuRatio.getRatio()));
                        // 成交单价 = 组合商品成交单价 * 当前SKU的占比
                        orderItem.setPriceActual(BigDecimalUtil.multiply(groupProductHead.getPriceActual(), skuRatio.getRatio()));
                        // 调整金额 = 组合商品调整金额 * 当前SKU的占比
                        orderItem.setAdjustAmt(BigDecimalUtil.multiply(groupProductHead.getAdjustAmt(), skuRatio.getRatio()));
                        /* 原价 = (组合商品原价 * 组合商品数量) * 当前SKU的占比*/
                        // 组合商品金额 = 组合商品原价 * 组合商品数量
                        BigDecimal groupProductHeadAmt = BigDecimalUtil.multiply(groupProductHead.getPrice(), groupProductHead.getQty());
                        if (orderItem.getQty().compareTo(BigDecimal.ZERO) != 0) {
                            // 原价（单价） = (组合商品金额 * 当前SKU的占比) / 当前SKU数量
                            BigDecimal price = BigDecimalUtil.multiply(groupProductHeadAmt, skuRatio.getRatio()).divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP);
                            orderItem.setPrice(price);
                        } else {
                            orderItem.setPrice(BigDecimal.ZERO);
                        }
                    }
                }
            }
        }
    }
}
