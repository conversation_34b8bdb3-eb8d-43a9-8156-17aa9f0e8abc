package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderSelectEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTableHeaderEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.TStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.*;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 订单管理: 订单列表-初始化查询条件
 *
 * @author: xiwen.z
 * create at: 2019/3/8 0008
 */
@Component
@Slf4j
public class OcBOrderInitService {

    @Autowired
    private UserQueryListService userQueryListService;

    @Autowired
    private DynamicListService dynamicListService;

    private final String shop_perm = "CP_C_SHOP_ID";
    private final String ware_perm = "CP_C_PHY_WAREHOUSE_ID";
    private final String store_perm = "CP_C_STORE_ID";

    /**
     * 初始化查询条件
     *
     * @param loginUser loginUser
     * @param param     string jsonobject
     * @param pem       UserPermission[]
     * @return vh vh
     */
    public ValueHolderV14<QueryOrderConditionResult> queryConditionInit(String param, User loginUser, UserPermission pem) {

        ValueHolderV14<QueryOrderConditionResult> vh = new ValueHolderV14<>();
        if (param == null || param.trim().length() < OcBOrderConst.IS_STATUS_IY) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("请求参数不正确", loginUser.getLocale()));
            return vh;
        }
        try {
            JSONObject o = JSON.parseObject(param);
            if (o == null) {
                throw new Exception("参数解析异常");
            }
            String tn = o.getString("table");
            if (tn == null) {
                throw new Exception("参数缺失: table:");
            }
            switch (tn.toUpperCase()) {
                case "OC_B_ORDER":
                case "OC_B_ORDER_TO_B":
                    vh = initOcBorder(param, loginUser, pem);
                    break;
                case "OC_B_REFUND_IN":
                case "OC_B_RETURN_ORDER":
                    vh = initOcBreturnOrder(param, loginUser);
                    break;
                default:
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("未查询到对应表的数据", loginUser.getLocale()));
                    break;
            }

        } catch (Exception ex) {
            log.error(LogUtil.format("OcBOrderInitService.queryConditionInit Error：{}"),
                    Throwables.getStackTraceAsString(ex));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("初始化时发生异常: " + ex, loginUser.getLocale()));
        }
        return vh;
    }

    /**
     * 订单管理.初始化
     *
     * @param tn        String
     * @param loginUser User
     * @param pem       UserPermission[]
     * @return vh14
     */
    private ValueHolderV14 initOcBorder(String tn, User loginUser, UserPermission pem) {
        ValueHolderV14<QueryOrderConditionResult> vh = new ValueHolderV14<>();

        // 标签集合
        List<QueryOrderTagResult> labelList = OcOrderTagEum.toQueryOrderTagResult();
        // 使用空数组
        if (labelList == null) {
            labelList = new ArrayList<>();
        }
        // 表头信息
        List<QueryOrderTableHeaderResult> tableHeaderList = initOcReturnOrderConvert(tn, loginUser, pem);
        if (tableHeaderList == null) {
            tableHeaderList = OcOrderTableHeaderEnum.convertToModel();
        }
        if (tableHeaderList == null) {
            // 使用空数组
            tableHeaderList = new ArrayList<>();
        }
        // 高级查询
        ValueHolder tableQuery = userQueryListService.getTableQuery(tn, loginUser);
        List<JSONObject> searchNames = (List<JSONObject>) tableQuery.get("date");
        dealPermission(searchNames);

        // 下拉条件选项框
        List<QueryOrderSelectResult> selectList = OcOrderSelectEnum.toQueryOrderSelectResult();
        if (selectList != null) {
            for (QueryOrderSelectResult o : selectList) {
                if (o == null) {
                    continue;
                }
                if (OcOrderSelectEnum.SELECT_STATUS.getQueryName().equals(o.getQueryName())) {
                    // 加入状态标签
                    List<QueryOrderCheckBoxResult> checkList = OcOrderCheckBoxEnum.toQueryOrderCheckBoxResult();
                    o.setList(checkList);
                    continue;
                }
                if (OcOrderSelectEnum.SELECT_PT_STATUS.getQueryName().equals(o.getQueryName())){
                    List<QueryOrderCheckBoxResult> ptStatusCheckList = TStatusEnum.toQueryOrderCheckBoxResult();
                    o.setList(ptStatusCheckList);
                    continue;
                }
                if (o.getQueryName().equals(OcOrderSelectEnum.SELECT_SHOP.getQueryName())) {
                    JSONObject shop = dealSelectCdt(searchNames, shop_perm);
                    o.setSelectTab(shop);
                    continue;
                }
                if (o.getQueryName().equals(OcOrderSelectEnum.SELECT_DELIVERY_WAREHOUSE.getQueryName())) {
                    JSONObject warehouse = dealSelectCdt(searchNames, ware_perm);
                    o.setSelectTab(warehouse);
                    continue;
                }
                if (o.getQueryName().equals(OcOrderSelectEnum.SELECT_LOGISTICS_COMPANY.getQueryName())) {
                    JSONObject logistics = dealSelectCdt(searchNames, "CP_C_LOGISTICS_ID");
                    o.setSelectTab(logistics);
                }
            }
        } else {
            selectList = new ArrayList<>();
        }

        QueryOrderConditionResult queryOrderConditionResult = new QueryOrderConditionResult();
        queryOrderConditionResult.setLabel(labelList);
        queryOrderConditionResult.setQueryInfo(selectList);
        queryOrderConditionResult.setTableHeader(tableHeaderList);
        queryOrderConditionResult.setHighSearch(searchNames);

        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(Resources.getMessage("success", loginUser.getLocale()));
        vh.setData(queryOrderConditionResult);
        return vh;
    }

    /**
     * 下拉选项
     *
     * @param searchNames list
     * @param sname       string
     * @return jsonObject
     */
    private JSONObject dealSelectCdt(List<JSONObject> searchNames, String sname) {
        JSONObject obj = new JSONObject();
        if (searchNames == null || searchNames.size() < OcBOrderConst.IS_STATUS_IY) {
            return obj;
        }
        int l = searchNames.size();
        for (int i = 0; i < l; i++) {
            JSONObject oc = searchNames.get(i);
            if (oc == null) {
                return obj;
            }

            JSONObject ot = oc.getJSONObject("tabth");
            if (ot == null) {
                return obj;
            }
            String tmpName = ot.getString("colname");
            if (sname.equals(tmpName)) {
                return oc;
            }
        }
        return obj;
    }

    /**
     * 退换货
     *
     * @param param     String
     * @param loginUser User
     * @return vh14
     */
    private ValueHolderV14<QueryOrderConditionResult> initOcBreturnOrder(String param, User loginUser) {
        return dynamicListService.dynamicList(param, loginUser);
    }

    /**
     * 订单管理表头查询
     *
     * @param param     String
     * @param loginUser User
     * @param pem       userPermission
     * @return list
     */
    private List<QueryOrderTableHeaderResult> initOcReturnOrderConvert(String param, User loginUser,
                                                                       UserPermission pem) {
        List<QueryOrderTableHeaderResult> tbRet = dynamicListService.queryTabHeaderList(param, loginUser);
        Set<String> forbidCols = pem.getForbiddenColumns();
        if (forbidCols != null) {
            for (int i = 0; i < tbRet.size(); i++) {
                QueryOrderTableHeaderResult tbh = tbRet.get(i);
                if (forbidCols.contains(tbh.getKey().toUpperCase())) {
                    tbRet.remove(i--);
                }
            }
        }
        return tbRet;
    }

    private void dealPermission(List<JSONObject> searchNames) {

        if (searchNames != null && searchNames.size() > OcBOrderConst.IS_STATUS_IN) {
            int l = searchNames.size();
            for (int i = 0; i < l; i++) {
                JSONObject oc = searchNames.get(i);
                if (oc == null) {
                    continue;
                }
                JSONObject ot = oc.getJSONObject("tabth");
                if (ot == null) {
                    continue;
                }
                String tmpName = ot.getString("colname");
                if (shop_perm.equals(tmpName)) {
                    addGrantCtrlSign(ot, "CP_C_SHOP_PERMISSION_ID");
                    continue;
                }
                if (ware_perm.equals(tmpName)) {
                    addGrantCtrlSign(ot, "CP_C_WAREHOUSE_ID");
                    continue;
                }
                if (store_perm.equals(tmpName)) {
                    addGrantCtrlSign(ot, "CP_C_MAIN_STORE_ID");
                    continue;
                }
            }
        }
    }

    private void addGrantCtrlSign(JSONObject ot, String permType) {
        JSONArray ary = new JSONArray();
        JSONObject warehouse = new JSONObject();
        warehouse.put("premtype", permType);
        warehouse.put("refcol", "ID");
        warehouse.put("iswrite", "false");
        ary.add(warehouse);
        ot.put("precolnameslist", ary);
    }
}
