package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.InterceptStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnProTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.request.UpdateReturnOrderRequest;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 修改物流公司.退换货
 *
 * @author: xiWen.z
 * create at: 2019/8/16 0016
 */
@Slf4j
@Component
public class ModifyReturnOrderLogisticsService {

    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    OcCancelChangingOrRefundService cancelChangingOrRefundService;
    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    OcSaveChangingOrRefundingService refundingService;

    /**
     * 批量修改物流公司
     *
     * @param req UpdateReturnOrderRequest
     * @param usr User
     * @return vh14
     */
    public ValueHolderV14 updateLogistics(UpdateReturnOrderRequest req, User usr) {

        /**
         * 1. 校验.获取
         */
        ValueHolderV14 vh = validateParam(req, usr);
        if (ResultCode.FAIL == vh.getCode()) {
            return vh;
        }
        recordDebug("###" + this.getClass().getName() + " #updateWarehouse 物流公司" + JSON.toJSONString(req));
        /**
         * 2. 锁单.查询.判断.更新
         */
        Long[] ids = req.getIds();
        List<String> errorList = new ArrayList<>();
        HashMap<Long, RedisReentrantLock> rdsMap = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        StringBuilder sb = new StringBuilder();
        try {
            HashMap<Long, Integer> countMap = new HashMap<>();
            for (int i = 0, l = ids.length; i < l; i++) {
                Long id = ids[i];
                countMap.put(id, (i + 1));
                if (id == null) {
                    errorList.add("第" + (i + 1) + "条: 发生错误,订单ID信息丢失");
                    continue;
                }
                String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        errorList.add("第" + (i + 1) + "条: 订单处于锁定状态, 修改失败");
                        continue;
                    }
                    rdsMap.put(id, redisLock);
                } catch (Exception e) {
                    errorList.add("第" + (i + 1) + "条: 锁单发生异常,修改失败");
                    continue;
                }
                sb.append(",");
                sb.append(id);
            }
            String mIds = sb.toString();
            if (mIds.length() < OcBOrderConst.IS_STATUS_IY) {
                return vhResult("订单全部处于锁定状态,暂时无法修改", false, usr);
            }
            mIds = mIds.substring(OcBOrderConst.IS_STATUS_IY);
            List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.queryReturnOrderByIds(mIds);
            if (ocBReturnOrders == null) {
                return vhResult("未能查询到所选订单数据", false, usr);
            }
            sb = new StringBuilder();
            for (int i = 0, lr = ocBReturnOrders.size(); i < lr; i++) {
                OcBReturnOrder o = ocBReturnOrders.get(i);
//                if (o != null && o.getReturnStatus() != null
//                        && OcBOrderConst.RETURN_STATUS_WAIT_REFUND.equals(o.getReturnStatus())) {
                if (o != null && o.getReturnStatus() != null
                        && ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(o.getReturnStatus())) {
                    sb.append(",");
                    sb.append(o.getId());
                    continue;
                }
                errorList.add("第" + countMap.get(o.getId()) + "条: 该状态订单不允许修改, 修改失败");
            }
            mIds = sb.toString();
            if (mIds.length() < OcBOrderConst.IS_STATUS_IY) {
                return vhResult("所选订单全部不符合修改条件", false, usr);
            }
            mIds = mIds.substring(OcBOrderConst.IS_STATUS_IY);

            int rlt = ocBReturnOrderMapper.updateCpcLogisticsInfo(req, mIds);
            if (rlt < OcBOrderConst.IS_STATUS_IY) {
                return vhResult("更新数据失败", false, usr);
            }
            jsonObject.put("successCount", rlt);

        } catch (Exception ex) {
            log.error("###" + this.getClass().getName() + " #批量更新异常.ERROR:", ex);
        } finally {
            Set<Long> sets = rdsMap.keySet();
            for (Long v : sets) {
                try {
                    RedisReentrantLock redisReentrantLock = rdsMap.get(v);
                    redisReentrantLock.unlock();
                } catch (Exception e) {
                    log.error("###" + this.getClass().getName() + " #解除锁单状态异常.ERROR: id" + v);
                    continue;
                }
            }
        }
        int rst = ids.length - jsonObject.getIntValue("successCount");
        String msg = "修改完成. 成功:" + jsonObject.getIntValue("successCount") + "条, 失败:" + rst + "条";
        vh = vhResult(msg, true, usr);
        jsonObject.put("errorCount", errorList.size());
        jsonObject.put("errorRecord", errorList);
        vh.setData(jsonObject);
        return vh;
    }

    /**
     * 参数校验
     *
     * @param req UpdateReturnOrderRequest
     * @param usr User
     * @return vh14
     */
    private ValueHolderV14 validateParam(UpdateReturnOrderRequest req, User usr) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (usr == null) {
            recordDebug("Verification: User Is Null");
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("当前用户不存在");
            return vh;
        }
        String eroMsg = null;
        if (req == null) {
            eroMsg = "参数丢失";
        }
        valLab:
        if (req != null) {
            if (req.getIds() == null || req.getIds().length < OcBOrderConst.IS_STATUS_IY) {
                eroMsg = "请确认已选择要修改的订单";
                break valLab;
            }
            if (req.getCpCLogisticsId() == null || req.getCpCLogisticsId() < OcBOrderConst.IS_STATUS_IY) {
                eroMsg = "物流公司信息参数缺失";
                break valLab;
            }
            try {
                CpLogistics cpLogistics = cpRpcService.cpLogisticsInfo(req.getCpCLogisticsId());
                if (cpLogistics == null) {
                    eroMsg = "未查询到该物流公司信息";
                } else {
                    req.setCpCLogisticsEcode(cpLogistics.getEcode());
                    req.setCpCLogisticsEname(cpLogistics.getEname());
                }
            } catch (Exception e) {
                eroMsg = "查询物流公司信息异常";
                log.error("###" + this.getClass().getName() + "RPC查询物流公司信息异常ID: " + req.getCpCLogisticsId(), e);
            }
        }
        if (eroMsg != null) {
            recordDebug("###" + this.getClass().getName() + "#Verification: UpdateReturnOrderRequest: " + eroMsg);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(eroMsg, usr.getLocale()));
            return vh;
        }
        vh.setCode(ResultCode.SUCCESS);
        return vh;
    }

    /**
     * debug level record
     *
     * @param s log
     */
    private void recordDebug(String s) {
        if (log.isDebugEnabled()) {
            log.debug("###" + this.getClass().getName() + " #" + s);
        }
    }

    /**
     * 返回信息
     *
     * @param msg       输出信息
     * @param flag      结果
     * @param loginUser 登录用户
     * @return vh
     */
    private ValueHolderV14 vhResult(String msg, boolean flag, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (flag) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        }
        return vh;
    }

    /**
     * 修改退回的快递
     *
     * @param model
     * @param loginUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 modifyReturnExpress(UpdateReturnOrderRequest model, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (model == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }
        Long[] ids = model.getIds();
        if (ids.length == 0 || ids.length > 1) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("需要选择退单进行操作,切只能选择一条进行操作");
            return vh;
        }
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(ids[0]);
        ocBReturnOrder.setLogisticsCode(model.getLogisticsNumber());
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        returnOrder.setCpCLogisticsId(model.getCpCLogisticsId());
        returnOrder.setCpCLogisticsEcode(model.getCpCLogisticsEcode());
        returnOrder.setCpCLogisticsEname(model.getCpCLogisticsEname());
        returnOrder.setLogisticsCode(model.getLogisticsNumber());
        returnOrder.setModifierid(loginUser.getId() + 0L);
        returnOrder.setModifiername(loginUser.getEname());
        returnOrder.setModifieddate(new Date());
        returnOrder.setModifiername(loginUser.getName());
        OcBReturnOrder ocBReturnOrder1 = refundingService.checkBillType(ocBReturnOrder);

        returnOrder.setBillType(ocBReturnOrder1.getBillType());
        int id = ocBReturnOrderMapper.update(returnOrder, new QueryWrapper<OcBReturnOrder>().eq("id", ids[0]));
        if (id > 0) {
            cancelChangingOrRefundService.insertReturnOrederLog("修改退回快递", "修改退回快递成功", null, loginUser, ids[0]);
        } else {
            vh.setMessage("修改退回快递失败");
            vh.setCode(ResultCode.FAIL);
            cancelChangingOrRefundService.insertReturnOrederLog("修改退回快递", "修改退回快递失败", null, loginUser, ids[0]);
            return vh;
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("修改退回快递成功");
        return vh;
    }

    /**
     * 预退货传WMS
     *
     * @param param     传入的参数
     * @param loginUser 当前用户
     * @return
     */
    public ValueHolderV14 returnToWms(JSONObject param, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (param.isEmpty() && !param.containsKey("ids")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("亲选择一条数据进行操作");
            return vh;
        }
        JSONArray ids = param.getJSONArray("ids");
        if (ids.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("亲选择一条数据进行操作");
            return vh;
        }
        // 是否继续(否就不继续， 是就继续)
        Boolean isContinue = param.getBoolean("IsContinue");
        List<Long> returnIDs = new ArrayList<>();
        ModifyReturnOrderLogisticsService bean = ApplicationContextHandle.getBean(ModifyReturnOrderLogisticsService.class);
        JSONArray errorMessage = new JSONArray();
        for (int i = 0; i < ids.size(); i++) {
            JSONObject object = new JSONObject();
            OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByid(ids.getLong(i));
            try {
                ValueHolderV14 v14 = bean.mainStep(returnOrder, loginUser, isContinue);
                if (ResultCode.FAIL == v14.getCode()) {
                    object.put("billNO", returnOrder.getBillNo());
                    object.put("Message", v14.getMessage());
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("预退货单传WMs异常:" + e);
                object.put("billNO", returnOrder.getBillNo());
                object.put("Message", e.getMessage());
                returnIDs.add(returnOrder.getId());
            }
            if (!object.isEmpty()) {
                errorMessage.add(object);
            }
        }
        if (errorMessage.size() > 0) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("操作成功" + (ids.size() - errorMessage.size()) + "条数据,失败" + errorMessage.size() + "条数据");
            JSONObject object = new JSONObject();
            object.put("errorMessage", errorMessage);
            if (!CollectionUtils.isEmpty(returnIDs)) {
                object.put("errorIds", returnIDs);
            }
            vh.setData(object);
        } else {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("操作成功" + ids.size() + "条数据 ");
        }
        return vh;
    }

    /**
     * @param returnOrder 预退货订单
     * @param loginUser   当前登录用户
     * @param isContinue  是否要继续
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 mainStep(OcBReturnOrder returnOrder, User loginUser, Boolean isContinue) {
        ValueHolderV14 vh = new ValueHolderV14();
        List<Integer> IntercerptStatus = new ArrayList<>();
        IntercerptStatus.add(InterceptStatus.DELIVERY_INTERCEPT_SUCCESS.getCode());
        IntercerptStatus.add(InterceptStatus.LAUNCH_INTERCEPT_SUCCESS.getCode());
        //        d、当退货入库仓为空进行时候时，系统提示：退货入库仓不能为空。
        if (returnOrder.getCpCPhyWarehouseInId() == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("退货入库仓不能为空");
            return vh;
        }
//        b、退货类型为客退，运单号为空的单据进行审核时，运单号自动填充为000000，并提示审核通过后，此界面不显示此单据。
        OcBReturnOrder ocBReturnOrder = new OcBReturnOrder();

        if (ReturnProTypeEnum.CUSTOMERRETREAT.getVal().equals(returnOrder.getReturnProType())
                && StringUtils.isEmpty(returnOrder.getLogisticsCode())) {
            if (isContinue) {
                ocBReturnOrder.setModifiername(loginUser.getName());
                ocBReturnOrder.setModifierename(loginUser.getEname());
                ocBReturnOrder.setModifieddate(new Date());
                ocBReturnOrder.setModifierid(loginUser.getId() + 0L);
                ocBReturnOrder.setLogisticsCode("000000");
                ocBReturnOrder.setBillType(1);
                ocBReturnOrderMapper.update(ocBReturnOrder, new QueryWrapper<OcBReturnOrder>().eq("id", returnOrder.getId()));
                vh.setMessage("操作成功");
                vh.setCode(ResultCode.SUCCESS);
                return vh;
            } else {
                throw new NDSException("运单号为空");
            }
        } else if (ReturnProTypeEnum.INTERCEPT.getVal().equals(returnOrder.getReturnProType())
                && !IntercerptStatus.contains(returnOrder.getIntercerptStatus())) {
//        c、退货类型为拦截，且拦截状态为非拦截成功的单据进行审核时，拦截状态更新为拦截成功，并提示审核通过后，此界 面不显示此单据。
            ocBReturnOrder.setModifiername(loginUser.getName());
            ocBReturnOrder.setModifierename(loginUser.getEname());
            ocBReturnOrder.setModifieddate(new Date());
            ocBReturnOrder.setModifierid(loginUser.getId() + 0L);
            ocBReturnOrder.setBillType(1);
            ocBReturnOrder.setIntercerptStatus(InterceptStatus.DELIVERY_INTERCEPT_SUCCESS.getCode());
            ocBReturnOrderMapper.update(ocBReturnOrder, new QueryWrapper<OcBReturnOrder>().eq("id", returnOrder.getId()));
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("操作成功");
            return vh;
        }


        vh.setCode(ResultCode.FAIL);
        vh.setMessage("操作失败");
        return vh;
    }
}
