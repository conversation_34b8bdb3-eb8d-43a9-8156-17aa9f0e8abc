package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderWmsStatus;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.services.returnin.OcReturnInSupport;
import com.jackrain.nea.oc.oms.util.OmsTransferThreadLocalUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.StandplatRefundOrderTransferUtil;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Desc : 售后补寄服务
 * <AUTHOR> xiWen
 * @Date : 2023/4/3
 */
@Slf4j
@Component
public class OmsAfterSaleReissueService {

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;

    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    private OcBReturnAfSendItemMapper ocBReturnAfSendItemMapper;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBOrderOffService ocBOrderOffService;

    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;

    @Autowired
    protected IpStandplatRefundService ipStandplatRefundService;

    @Autowired
    private RefundFormAfterDeliveryService refundFormAfterDeliveryService;

    @Autowired
    protected OmsStandPlatRefundOrderService omsStandPlatRefundOrderService;

    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;

    @Autowired
    protected OmsOrderStandPlatAutoRefundService standPlatAutoRefundService;

    @Autowired
    protected StandplatRefundOrderTransferUtil standplatRefundOrderTransferUtil;

    @Autowired
    private OmsAfterSaleReissueGenerateService omsAfterSaleReissueGenerateService;

    /**
     * 售后关闭
     *
     * @param orderInfo 退款转换关系类
     * @return vh
     */
    public ValueHolderV14 afterSaleReissueCloseProcess(OmsStandPlatRefundRelation orderInfo) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "success");
        IpBStandplatRefund ipRefund = orderInfo.getIpBStandplatRefund();
        String orderNo = ipRefund.getOrderNo();
        String returnNo = ipRefund.getReturnNo();
        try {
            // 查询退单
            List<OcBReturnOrderRelation> returnRelations = queryUnCancelReturnOrder(orderInfo);
            if (CollectionUtils.isEmpty(returnRelations)) {
                return vh;
            }

            // 取消补寄单.前置
            batchCancelReissueOrder(returnRelations);

            // 取消退单
            cancelReturnOrder(returnRelations);
        } catch (Exception e) {
            String message = OcReturnInSupport.expMsgFun.apply(e);
            vh.setMessage(message);
            vh.setCode(ResultCode.FAIL);
            log.error(LogUtil.format("售后关闭,exp：{}", orderNo, returnNo), Throwables.getStackTraceAsString(e));
        }
        return vh;
    }


    /**
     * 售后补寄流程
     * 卖家同意
     *
     * @param orderInfo 退款转换关系类
     * @return vh
     */
    public ValueHolderV14 afterSaleReissueSellerAgreeProcess(OmsStandPlatRefundRelation orderInfo) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "success");
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        String orderNo = ipBStandplatRefund.getOrderNo();
        String returnNo = ipBStandplatRefund.getReturnNo();
        int step = 0;
        try {
            List<OcBReturnOrderRelation> returnRelations = queryUnCancelReturnOrder(orderInfo);
            boolean generatedReturnOrder = false;
            // 不存在退换货
            if (CollectionUtils.isEmpty(returnRelations)) {
                // 取消退款单
                cancelReturnAfSend(ipBStandplatRefund);
            } else {
                generatedReturnOrder = true;
                updateExistReturnOrderInfo(ipBStandplatRefund, returnRelations);
            }
            if (generatedReturnOrder) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("退转补寄-生成补寄单.start", orderNo, returnNo));
                }
                //    generateReissueOrderWithExistReturnOrder(returnRelations);
                return vh;
            }
            // 生成
            step = 1;
            OmsAfterSaleReissueService bean = ApplicationContextHandle.getBean(OmsAfterSaleReissueService.class);
            bean.generateOrders(orderInfo);

        } catch (Exception e) {
            if (step > OcBOrderConst.IS_STATUS_IN) {
                cancelRedisCache(returnNo);
            }
            String message = OcReturnInSupport.expMsgFun.apply(e);
            vh.setMessage(message);
            vh.setCode(ResultCode.FAIL);
            log.error(LogUtil.format("卖家同意,exp：{}", orderNo, returnNo), Throwables.getStackTraceAsString(e));
        } finally {
            OmsTransferThreadLocalUtil.returnReissueLocal.remove();
        }
        return vh;
    }

    /**
     * 生成补寄单
     *
     * @param orderInfo
     */
    @Transactional
    public void generateOrders(OmsStandPlatRefundRelation orderInfo) {
        List<OcBReturnOrderRelation> ocBReturnOrderRelations = generateReturnOrder(orderInfo);
        omsAfterSaleReissueGenerateService.generateReissueOrder(ocBReturnOrderRelations);
    }

    /**
     * 生成补寄退单
     *
     * @param orderInfo
     * @return
     */
    private List<OcBReturnOrderRelation> generateReturnOrder(OmsStandPlatRefundRelation orderInfo) {
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        User user = SystemUserResource.getRootUser();
        List<OmsOrderRelation> omsOrderRelations = orderInfo.getOmsOrderRelation();
        // 按子订单维度生成退换货单, 添加补寄标识
        List<OcBReturnOrderRelation> returnRelations = omsAfterSaleReissueGenerateService.buildReturnReissueOrders(orderInfo);
        if (CollectionUtils.isEmpty(returnRelations)) {
            String remark = "创建退货单失败,返回单据关系集合为空";
            TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.QTY_NOT_ENOUGH);
            AssertUtil.assertException(true, remark);
        }
        omsReturnOrderService.insertOmsReturnOrderInfo(returnRelations, user);
        //  omsStandPlatRefundOrderService.foundRefundSlipAfter(refundIds, ocBOrder, ipBStandplatRefund, user);
        //赠品后发
        omsReturnOrderService.giftsThenSend(omsOrderRelations, orderInfo.getIsGiftOrderRelation(), orderInfo.getIntermediateTableRelation(), user);
       /* Integer returnStatus = returnRelations.get(0).getReturnOrderInfo().getReturnStatus();
        boolean isPinduoduo = PlatFormEnum.PINDUODUO.getCode().equals(ocBOrder.getPlatform());

        if (ReturnStatusEnum.COMPLETION.getVal().equals(returnStatus)) {
            if (!isPinduoduo) {
                //发货后ag
                standPlatAutoRefundService.executeAutoRefund(orderInfo, user);
            }
        }
        ipBStandplatRefund.setRefundAmount(refundAmount);*/
        TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
        return returnRelations;
    }

    /**
     * 退转补寄 - 生成补寄单
     *
     * @param relations
     */
    private void generateReissueOrderWithExistReturnOrder(List<OcBReturnOrderRelation> relations) {
        Iterator<OcBReturnOrderRelation> iterator = relations.iterator();
        while (iterator.hasNext()) {
            OcBReturnOrderRelation next = iterator.next();
            OcBReturnOrder returnOrderInfo = next.getReturnOrderInfo();
            Integer billType = returnOrderInfo.getBillType();
            boolean isReissue = OcReturnBillTypeEnum.REISSUE.getVal().equals(billType);
            if (!isReissue) {
                iterator.remove();
            }
            Long returnId = returnOrderInfo.getId();
            List<Long> ids = ES4Order.getIdsByOrigReturnOrderId(returnId);
            if (CollectionUtils.isNotEmpty(ids)) {
                iterator.remove();
            }
        }
        if (CollectionUtils.isEmpty(relations)) {
            return;
        }
        // 生成补寄单
        omsAfterSaleReissueGenerateService.generateReissueOrder(relations);
    }

    /**
     * 取消退货单
     *
     * @param returnRelations 退货单
     */
    private void cancelReturnOrder(List<OcBReturnOrderRelation> returnRelations) {
        User user = SystemUserResource.getRootUser();
        for (OcBReturnOrderRelation returnRelation : returnRelations) {
            OcBReturnOrder returnOrder = returnRelation.getReturnOrderInfo();
            JSONArray jsnAryParam = new JSONArray();
            jsnAryParam.add(returnOrder.getId());
            ValueHolderV14 vhParam = new ValueHolderV14();
            ValueHolderV14 result = ocCancelChangingOrRefundService.oneOcCancle(user, vhParam, jsnAryParam);
            String message = result.getMessage();
            message = message == null ? "null" : (message.length() > 150 ? message.substring(0, 150) : message);
            AssertUtil.isTrue(result.isOK(), String.format("退货单[%s]取消失败,%s", returnOrder.getBillNo(), message));
        }
    }

    /**
     * 取消退款单
     *
     * @param ipBStandPlatRefund 通用中间表退款单
     */
    private void cancelReturnAfSend(IpBStandplatRefund ipBStandPlatRefund) {
        // 退款单
        OcBReturnAfSendRelation returnAfSendInfo = getReturnAfSendInfo(ipBStandPlatRefund.getReturnNo());
        if (Objects.isNull(returnAfSendInfo)) {
            return;
        }
        // 取消
        OcBReturnAfSend ocBReturnAfSend = returnAfSendInfo.getOcBReturnAfSend();
        Integer returnStatus = ocBReturnAfSend.getReturnStatus();
        if (returnStatus == ReturnAfSendReturnBillTypeEnum.CANCEL.getVal()) {
            return;
        }
        AssertUtil.assertException(returnStatus == ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal(),
                String.format("退款单[%s]已退款完成", ocBReturnAfSend.getBillNo()));
        String tReturnId = ocBReturnAfSend.getTReturnId();
        if (ReturnAfSendReturnBillTypeEnum.REFUNDING.getVal() == returnStatus
                || ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal() == returnStatus) {
            int updateResult = ocBReturnAfSendMapper
                    .updateOcBReturnAfSendByReturnStatus(ReturnAfSendReturnStatusEnum.INVALID.getVal(), tReturnId);
            refundFormAfterDeliveryService.insertReturnAfSendLog("取消退款单", "售后补寄", null,
                    SystemUserResource.getRootUser(), ocBReturnAfSend.getId());
            AssertUtil.isTrue(updateResult > 0, String.format("退款单[%s]取消更新失败", ocBReturnAfSend.getBillNo()));
        }
    }

    /**
     * 查询退货单.未取消
     *
     * @param orderInfo 退款转换关系类
     * @return 退货单
     */
    private List<OcBReturnOrderRelation> queryUnCancelReturnOrder(OmsStandPlatRefundRelation orderInfo) {
        List<Long> returnOrderIdList = queryReturnOrderIdList(orderInfo);
        if (CollectionUtils.isEmpty(returnOrderIdList)) {
            return new ArrayList<>(1);
        }
        List<OcBReturnOrderRelation> returnRelations = getReturnOrderInfo(returnOrderIdList);
        AssertUtil.notEmpty(returnRelations, "未查询到退货单[DB]");
        return returnRelations;
    }

    /**
     * 查询退换货单id
     *
     * @param orderInfo 退款转换关系类
     * @return 退货单ids
     */
    private List<Long> queryReturnOrderIdList(OmsStandPlatRefundRelation orderInfo) {
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        String returnNo = ipBStandplatRefund.getReturnNo();
        List<Long> returnIds = ES4ReturnOrder.queryShardKeyByRefundBillNo(returnNo);
        if (CollectionUtils.isNotEmpty(returnIds)) {
            return returnIds;
        }
        List<IpBStandplatRefundItem> standPlatRefundItems = orderInfo.getIpBStandplatRefundItem();
        if (CollectionUtils.isNotEmpty(standPlatRefundItems)) {
            List<Object> subOrderIds = standPlatRefundItems.stream().filter(item -> item.getSubOrderId() != null)
                    .map(IpBStandplatRefundItem::getSubOrderId).collect(Collectors.toList());
            returnIds = ES4ReturnOrder.queryShardKeyByOidList(subOrderIds);
        }
        if (CollectionUtils.isNotEmpty(returnIds)) {
            return returnIds;
        }
        Long returnOrderId = OmsReturnOrderService.selectOmsReturnOrderFromRedisByReturnId(returnNo);
        if (returnOrderId > 0L) {
            returnIds.add(returnOrderId);
        }
        return returnIds;
    }

    /**
     * 数据库查询退货单
     * 非取消
     *
     * @param returnIds 退货单ids
     * @return 退货单
     */
    private List<OcBReturnOrderRelation> getReturnOrderInfo(List<Long> returnIds) {
        List<OcBReturnOrderRelation> returnRelations = new ArrayList<>();
        List<OcBReturnOrder> returnOrders = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(returnIds);
        if (CollectionUtils.isEmpty(returnOrders)) {
            return returnRelations;
        }
        returnIds = returnOrders.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
        List<OcBReturnOrderRefund> subItems = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(returnIds);
        AssertUtil.notEmpty(subItems, "批量查询,未获取到商品明细");
        Map<Long, List<OcBReturnOrderRefund>> subMap = subItems.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getOcBReturnOrderId));
        for (OcBReturnOrder returnOrder : returnOrders) {
            Long id = returnOrder.getId();
            List<OcBReturnOrderRefund> subMapItems = subMap.get(id);
            AssertUtil.notEmpty(subMapItems, String.format("退货单[%s]未获取到商品明细", returnOrder.getBillNo()));
            OcBReturnOrderRelation relation = new OcBReturnOrderRelation();
            relation.setReturnOrderInfo(returnOrder);
            relation.setOrderRefundList(subMapItems);
            returnRelations.add(relation);
        }
        return returnRelations;
    }

    /**
     * 查询退款单
     *
     * @param returnNo 退款单号
     * @return 退款单
     */
    private OcBReturnAfSendRelation getReturnAfSendInfo(String returnNo) {
        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(returnNo);
        if (Objects.isNull(ocBReturnAfSend)) {
            return null;
        }
        Long id = ocBReturnAfSend.getId();
        List<OcBReturnAfSendItem> items = ocBReturnAfSendItemMapper.selectByOcBReturnAfSendIdListBySendId(id);
        AssertUtil.notEmpty(items, String.format("退款单[%s]未获取到商品明细", ocBReturnAfSend.getBillNo()));
        OcBReturnAfSendRelation afSendRelation = new OcBReturnAfSendRelation();
        afSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        afSendRelation.setOcBReturnAfSendItems(items);
        return afSendRelation;
    }

    /**
     * 更新退货单
     * 退款原因, 单据类型
     *
     * @param ipBStandPlatRefund 退款单中间表
     * @param returnRelations    退货单
     */
    private void updateExistReturnOrderInfo(IpBStandplatRefund ipBStandPlatRefund, List<OcBReturnOrderRelation> returnRelations) {
        String ipReturnReason = ipBStandPlatRefund.getReturnReason();
        User user = SystemUserResource.getRootUser();
        for (OcBReturnOrderRelation returnRelation : returnRelations) {
            OcBReturnOrder newReturn = null;
            OcBReturnOrder returnOrder = returnRelation.getReturnOrderInfo();
            String returnDesc = returnOrder.getReturnDesc();
            if (!StringUtils.equals(ipReturnReason, returnDesc)) {
                newReturn = new OcBReturnOrder();
                newReturn.setReturnDesc(ipReturnReason);
                newReturn.setReturnReason(ipReturnReason);
            }
            Integer billType = returnOrder.getBillType();
            boolean isReissue = OcReturnBillTypeEnum.REISSUE.getVal().equals(billType);
            if (!isReissue) {
                if (Objects.isNull(newReturn)) {
                    newReturn = new OcBReturnOrder();
                }
                newReturn.setBillType(OcReturnBillTypeEnum.REISSUE.getVal());
                returnOrder.setBillType(OcReturnBillTypeEnum.REISSUE.getVal());
            }
            if (Objects.nonNull(newReturn)) {
                newReturn.setId(returnOrder.getId());
                BaseModelUtil.setupUpdateParam(newReturn, user);
                ocBReturnOrderMapper.updateById(newReturn);
            }
        }
    }

    /**
     * 取消补寄订单
     *
     * @param returnRelations 退货单
     */
    private void batchCancelReissueOrder(List<OcBReturnOrderRelation> returnRelations) {
        for (OcBReturnOrderRelation returnRelation : returnRelations) {
            OcBReturnOrder returnOrderInfo = returnRelation.getReturnOrderInfo();
            Long returnId = returnOrderInfo.getId();
            List<Long> ids = ES4Order.getIdsByOrigReturnOrderId(returnId);
            for (Long id : ids) {
                cancelReissueOrder(id);
            }
        }
    }

    /**
     * 取消补寄订单流程
     *
     * @param id 订单id
     */
    private void cancelReissueOrder(Long id) {
        User user = SystemUserResource.getRootUser();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            boolean isLock = redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS);
            AssertUtil.isTrue(isLock, String.format("零售发货单[%d]匹货中,锁单失败", id));
            OcBOrder order = orderMapper.selectById(id);
            if (Objects.isNull(order)) {
                return;
            }
            Integer orderType = order.getOrderType();
            boolean isReissueOrder = OrderTypeEnum.REISSUE.getVal().equals(orderType);
            AssertUtil.isTrue(isReissueOrder, String.format("零售发货单[%s]单据异常,非补发单据", order.getBillNo()));
            // 取消
            Integer status = order.getOrderStatus();
            if (OmsOrderStatus.CANCELLED.toInteger().equals(status) || OmsOrderStatus.SYS_VOID.toInteger().equals(status)) {
                return;
            }
            // 反审核
            Integer wmsCancelStatus = order.getWmsCancelStatus();
            boolean isNeedDeAudit = OmsOrderStatus.CHECKED.toInteger().equals(status)
                    || (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(status)
                    && wmsCancelStatus != OcOrderWmsStatus.WMS_SUCCESS.getVal());
            if (isNeedDeAudit) {
                boolean isDeAuditSuccess = ocBOrderTheAuditService.updateOrderInfo(user, new ValueHolderV14(), id, false, 1L, true);
                AssertUtil.isTrue(isDeAuditSuccess, String.format("零售发货单[%s]匹货中,撤回失败", order.getBillNo()));
            }
            // 取消订单
            boolean cancelResult = ocBOrderOffService.doAutoOffOrder(user, id, OrderLogTypeEnum.ORDER_CANCLE.getKey(), "订单取消");
            AssertUtil.isTrue(cancelResult, String.format("零售发货单[%s]取消订单失败", order.getBillNo()));
        } catch (Exception e) {
            log.error(LogUtil.format("订单取消异常,exp：{}", id), Throwables.getStackTraceAsString(e));
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 清楚缓存
     *
     * @param returnId
     */
    private void cancelRedisCache(String returnId) {
        String redisKey = BllRedisKeyResources.getOmsReturnOrderReturnIdKey(returnId);
        Boolean hasKey = RedisMasterUtils.getObjRedisTemplate().hasKey(redisKey);

        if (hasKey != null && hasKey) {
            RedisMasterUtils.getObjRedisTemplate().delete(redisKey);
        }
    }


}
