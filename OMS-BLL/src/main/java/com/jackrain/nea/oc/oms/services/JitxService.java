package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.request.VipJitxCreateChangeWarehouseWorkflowRequest;
import com.jackrain.nea.ip.model.request.VipJitxGetChangeWarehouseWorkflowsRequest;
import com.jackrain.nea.ip.model.result.VipJitxCreateChangeWarehouseWorkflowResult;
import com.jackrain.nea.ip.model.result.VipJitxGetChangeWarehouseWorkflowsResult;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBJitxModifyWarehouseLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxCreateChangeWareReasonEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowCreatedStateEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowStateEnum;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.impl.OcBJitxModifyWarehouseLogServiceImpl;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2020-12-15
 * @desc JITX订单服务
 **/
@Slf4j
@Service
public class JitxService {

    private static final String FORCE_CHANGE_WAREHOSE_ENABLE = "business_system:jitx_force_change_warehouse_enable";

    private static final String SUCCESS = "SUCCESS";

    private static final String ERROR_CODE = "ERROR_INVALID_NEW_DELIVERY_WAREHOUSE";

    @Autowired
    private OcBJitxModifyWarehouseLogService ocBJitxModifyWarehouseLogService;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private UpdateOrderInfoService updateOrderInfoService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBJitxModifyWarehouseLogFiService ocBJitxModifyWarehouseLogFiService;

    @Autowired
    private IpBJitxResetShipWorkflowService ipBJitxResetShipWorkflowService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OmsAuditTaskService omsAuditTaskService;

    @Autowired
    private OcBJitxDealerOrderTaskService dealerOrderTaskService;

    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    @Autowired
    private OcBJitxModifyWarehouseLogServiceImpl warehouseLogService;

    /**
     * 批量创建改仓工单
     *
     * @param modifyWarehouseLogList JITX订单改仓日志表
     * @param operateUser            操作人
     */
    public void createChangeWarehouseWorkflows(List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList, User operateUser) {
        if (CollectionUtils.isEmpty(modifyWarehouseLogList)) {
            return;
        }
        modifyWarehouseLogList.forEach(ocBJitxModifyWarehouseLog -> {
            createChangeWarehouseWorkflowSingle(ocBJitxModifyWarehouseLog, operateUser);
        });
    }

    /**
     * 创建改仓工单
     *
     * @param ocBJitxModifyWarehouseLog JITX订单改仓日志表
     * @param operateUser               操作人
     */
    public void createChangeWarehouseWorkflowSingle(OcBJitxModifyWarehouseLog ocBJitxModifyWarehouseLog, User operateUser) {
        String lockRedisKey = BllRedisKeyResources.buildJitxCreateChangeWarehouseWorkflowKey(
                ocBJitxModifyWarehouseLog.getOrderId());
        log.info("createChangeWarehouseWorkflows lockRedisKey：{}", lockRedisKey);
        RedisReentrantLock redisLock = new RedisReentrantLock(lockRedisKey);
        try {

            String resetShipFlag = ipBJitxResetShipWorkflowService.getJITXRedisResetShipFlag(ocBJitxModifyWarehouseLog.getSourceCode());
            if(!ObjectUtils.isEmpty(resetShipFlag)){
                throw new NDSException("存在未完成的重置发货工单，请等待重置发货工单结束后操作");
            }

            if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                OcBJitxModifyWarehouseLog modifyWarehouseLog = createChangeWarehouseWorkflow(
                        ocBJitxModifyWarehouseLog, operateUser);
                int row = ocBJitxModifyWarehouseLogService.updateById(modifyWarehouseLog);
                if (row <= 0) {
                    log.info("update OcBJitxModifyWarehouseLog fail, data:{}", modifyWarehouseLog);
                }
            } else {
                throw new NDSException("当前订单正在创建改仓工单操作!");
            }
        } catch (Exception e) {
            log.info("createChangeWarehouseWorkflow Error, Exception message:{} ocBJitxModifyWarehouseLog:{}",
                    e.getMessage(), ocBJitxModifyWarehouseLog, e);
            updateFailReasonById(Lists.newArrayList(ocBJitxModifyWarehouseLog),
                    new StringBuilder(e.getMessage()).append(e.toString()).toString(), operateUser);
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 创建改仓工单
     *
     * @param modifyWarehouseLog JITX订单改仓日志表
     * @param operateUser        操作人
     * @return OcBJitxModifyWarehouseLog JITX订单改仓结果
     */
    private OcBJitxModifyWarehouseLog createChangeWarehouseWorkflow(
            OcBJitxModifyWarehouseLog modifyWarehouseLog, User operateUser) {
        VipJitxCreateChangeWarehouseWorkflowRequest vipRequest = new VipJitxCreateChangeWarehouseWorkflowRequest();
        vipRequest.setOperateUser(operateUser);
        vipRequest.setSellerNick(modifyWarehouseLog.getSellerNick());
        vipRequest.setVendorId(modifyWarehouseLog.getVendorId());
        List<VipJitxCreateChangeWarehouseWorkflowRequest.CreateWorkflow> workflows = new ArrayList<>();
        VipJitxCreateChangeWarehouseWorkflowRequest.CreateWorkflow createWorkflow =
                new VipJitxCreateChangeWarehouseWorkflowRequest.CreateWorkflow();
        createWorkflow.setRequestId(UUID.randomUUID().toString());
        createWorkflow.setOrderSn(modifyWarehouseLog.getSourceCode());
        // 唯品会仓库编码
        createWorkflow.setNewDeliveryWarehouse(modifyWarehouseLog.getNewDeliveryWarehouse());
        createWorkflow.setReasonCode(VipJitxCreateChangeWareReasonEnum.OUT_STOCK.getCode());
        workflows.add(createWorkflow);
        vipRequest.setWorkflows(workflows);
        // 改仓日志表
        OcBJitxModifyWarehouseLog newModifyWarehouseLog = new OcBJitxModifyWarehouseLog();
        newModifyWarehouseLog.setId(modifyWarehouseLog.getId());
        newModifyWarehouseLog.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode());
        Integer failNumber = modifyWarehouseLog.getFailNumber() == null ? 1 : modifyWarehouseLog.getFailNumber() + 1;
        newModifyWarehouseLog.setFailNumber(failNumber);
        BaseModelUtil.setupUpdateParam(newModifyWarehouseLog, operateUser);
        log.info("ipRpcService，createChangeWarehouseWorkflow，vipRequest{}", vipRequest);
        // 申请创建改仓
        ValueHolderV14<List<VipJitxCreateChangeWarehouseWorkflowResult>> valueHolderV14 =
                ipRpcService.createChangeWarehouseWorkflow(vipRequest);
        if (valueHolderV14 == null) {
            log.info("createChangeWarehouseWorkflow RPC return empty,request:{}", vipRequest);
            newModifyWarehouseLog.setFailReason("创建改仓工单RPC返回为空");
            return newModifyWarehouseLog;
        }
        if (valueHolderV14.isOK() && CollectionUtils.isNotEmpty(valueHolderV14.getData())) {
            VipJitxCreateChangeWarehouseWorkflowResult result = valueHolderV14.getData().get(0);
            if (SUCCESS.equals(result.getResult())) {
                log.info("createChangeWarehouseWorkflow Success, id:{}, sourceCode:{}, workflowSn:{}",
                        new Object[]{modifyWarehouseLog.getId(), modifyWarehouseLog.getSourceCode(), result.getWorkflowSn()});
                newModifyWarehouseLog.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode());
                newModifyWarehouseLog.setWorkflowSn(result.getWorkflowSn());
                newModifyWarehouseLog.setWorkflowState(VipJitxWorkflowStateEnum.CREATE.getKey());
                // 重置失败次数，给获取改仓结果使用
                newModifyWarehouseLog.setFailNumber(0);
            } else {
                log.info("createChangeWarehouseWorkflow Error, id:{}, sourceCode:{}, result:{}",
                        modifyWarehouseLog.getId(), modifyWarehouseLog.getSourceCode(), result);
                StringBuilder failReason = new StringBuilder("ErrorCode:").append(result.getErrorCode())
                        .append(",ErrorMsg:").append(result.getErrorMsg());
                updateOrderInfoService.removeJITXRedisChangeWarehouseFlag(modifyWarehouseLog.getOrderId(),  operateUser);
                if (ERROR_CODE.equalsIgnoreCase(result.getErrorCode())) {
                    // 修改的唯品会仓库编码和唯品会平台的一致，所以无需创建
                    newModifyWarehouseLog.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.NO_NEED_CREATE.getCode());
                    failReason.append(",修改的唯品会仓库编码和唯品会平台中一致");
                    omsOrderLogService.addUserOrderLog(modifyWarehouseLog.getOrderId(), modifyWarehouseLog.getBillNo(),
                            OrderLogTypeEnum.JITX_ORDER_UPDATE_WAREHOUSE.getKey(),
                            "无需改仓：" + failReason.toString(), null, failReason.toString(),
                            SystemUserResource.getRootUser());
                }else {
                    newModifyWarehouseLog.setCreatedStatus(VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode());
                    newModifyWarehouseLog.setWorkflowState(VipJitxWorkflowStateEnum.REJECT.getKey());
                    failReason.append(",强制改仓");
                    omsOrderLogService.addUserOrderLog(modifyWarehouseLog.getOrderId(), modifyWarehouseLog.getBillNo(),
                            OrderLogTypeEnum.JITX_ORDER_UPDATE_WAREHOUSE.getKey(),
                            "强制改仓：" + failReason.toString(), null, failReason.toString(),
                            SystemUserResource.getRootUser());
                }
                String forceFlag = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FORCE_CHANGE_WAREHOSE_ENABLE);
                if(YesNoEnum.ONE.getKey().equals(forceFlag)){
                    newModifyWarehouseLog.setForcedWarehouseChange(YesNoEnum.Y.getKey());
                }
                newModifyWarehouseLog.setFailReason(failReason.toString());

                //插入审核中间表
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setId(modifyWarehouseLog.getOrderId());
                omsAuditTaskService.createOcBAuditTask(updateOrder, OmsAuditTimeCalculateReason.RESERVE_AUDIT);
            }
        } else {
            log.info("createChangeWarehouseWorkflow Error, id:{}, sourceCode:{}, valueHolderV14:{}", valueHolderV14);
            newModifyWarehouseLog.setFailReason(valueHolderV14.getMessage());
        }
        return newModifyWarehouseLog;
    }


    @Data
    public class CreateWorkflowRequest {
        public CreateWorkflowRequest(String sellerNick, String vendorId) {
            this.sellerNick = sellerNick;
            this.vendorId = vendorId;
        }

        private String sellerNick;
        private String vendorId;
    }

    /**
     * 获取改仓工单接口
     *
     * @param modifyWarehouseLogList
     * @param operateUser
     */
    public void getChangeWarehouseWorkflows(List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList, User operateUser) {
        if (CollectionUtils.isEmpty(modifyWarehouseLogList)) {
            return;
        }
        List<OcBJitxModifyWarehouseLog> conditionList = new ArrayList<>(modifyWarehouseLogList.size());
        modifyWarehouseLogList.forEach(model -> {
            if (!VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode().equals(model.getCreatedStatus())) {
                // 改仓工单创建状态非'创建成功',退出
                return;
            }
            if (!VipJitxWorkflowStateEnum.CREATE.getKey().equals(model.getWorkflowState())
                    && !VipJitxWorkflowStateEnum.DOING.getKey().equals(model.getWorkflowState())) {
                // 改仓工单状态非('新建'和'处理中'),退出
                return;
            }
            if (StringUtils.isEmpty(model.getSellerNick()) || StringUtils.isEmpty(model.getVendorId())) {
                log.info("getChangeWarehouseWorkflow sellerNick or VerdorId is empty，modifyWarehouseLog：{}", model);
                OcBJitxModifyWarehouseLog modifyWarehouseLog = OcBJitxModifyWarehouseLog.builder()
                        .id(model.getId()).failReason("获取改仓工单状态失败,卖家昵称或供应商ID不能为空").build();
                ocBJitxModifyWarehouseLogService.updateById(modifyWarehouseLog);
                return;
            }
            if (StringUtils.isEmpty(model.getWorkflowSn())) {
                log.info("getChangeWarehouseWorkflow WorkflowSn is empty，modifyWarehouseLog：{}", model);
                OcBJitxModifyWarehouseLog modifyWarehouseLog = OcBJitxModifyWarehouseLog.builder()
                        .id(model.getId()).failReason("获取改仓工单状态失败,工单号不能为空").build();
                ocBJitxModifyWarehouseLogService.updateById(modifyWarehouseLog);
                return;
            }
            conditionList.add(model);
        });
        if (CollectionUtils.isEmpty(conditionList)) {
            return;
        }
        // 以sellerNick和vendorId分组
        Map<CreateWorkflowRequest, List<OcBJitxModifyWarehouseLog>> createWorkflowRequestListMap =
                conditionList.stream().collect(Collectors.groupingBy(
                        g -> new CreateWorkflowRequest(g.getSellerNick(), g.getVendorId())));
        for (Map.Entry<CreateWorkflowRequest, List<OcBJitxModifyWarehouseLog>> map : createWorkflowRequestListMap.entrySet()) {
            CreateWorkflowRequest createWorkflowRequest = map.getKey();
            List<OcBJitxModifyWarehouseLog> logList = map.getValue();
            List<List<OcBJitxModifyWarehouseLog>> listList = Lists.partition(logList, 20);
            for (List<OcBJitxModifyWarehouseLog> list : listList) {
                processChangeWarehouseResult(createWorkflowRequest, list, operateUser);
            }
        }
    }

    /**
     * 处理获取改仓工单结果
     *
     * @param createWorkflowRequest  改仓请求参数
     * @param modifyWarehouseLogList 改仓日志
     * @param operateUser            操作人
     */
    private void processChangeWarehouseResult(CreateWorkflowRequest createWorkflowRequest,
                                              List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList, User operateUser) {
        VipJitxGetChangeWarehouseWorkflowsRequest request = buildChangeWarehouseWorkflowRequest(
                createWorkflowRequest, modifyWarehouseLogList, operateUser);
        ValueHolderV14<List<VipJitxGetChangeWarehouseWorkflowsResult>> valueHolderV14 =
                ipRpcService.getChangeWarehouseWorkflows(request);
        if (valueHolderV14.isOK() && CollectionUtils.isNotEmpty(valueHolderV14.getData())) {
            Map<String, List<OcBJitxModifyWarehouseLog>> logGroupBySourceCode =
                    modifyWarehouseLogList.stream().collect(Collectors.groupingBy(OcBJitxModifyWarehouseLog::getSourceCode));
            for (VipJitxGetChangeWarehouseWorkflowsResult result : valueHolderV14.getData()) {
                List<OcBJitxModifyWarehouseLog> logList = logGroupBySourceCode.get(result.getOrderSn());
                if (CollectionUtils.isEmpty(logList)) {
                    log.info("getChangeWarehouseWorkflows not matched jitx_modify_warehouse_log result:{}", result);
                    continue;
                }
                if (logList.size() > 1) {
                    log.info("multiple jitx_modify_warehouse_log at the same time, orderSn:{}, logList:{}",
                            logList);
                    updateFailReasonById(modifyWarehouseLogList, "改仓订单号重复", operateUser);
                    continue;
                }
                OcBJitxModifyWarehouseLog modifyWarehouseLog = logList.get(0);
                // 更新改仓日志表状态
                OcBJitxModifyWarehouseLog newModifyWarehouseLog = new OcBJitxModifyWarehouseLog();
                newModifyWarehouseLog.setId(modifyWarehouseLog.getId());
                BaseModelUtil.setupUpdateParam(newModifyWarehouseLog, operateUser);
                newModifyWarehouseLog.setRejectRemark(result.getRejectRemark());
                Integer failNumber = modifyWarehouseLog.getFailNumber() == null ? 1 : modifyWarehouseLog.getFailNumber() + 1;
                Boolean isSuccess = Boolean.TRUE;
                if (VipJitxWorkflowStateEnum.PASS.getKey().equals(result.getWorkflowState())) {
                    // 检查JITX订单改仓结果
                    ValueHolderV14 v14 = checkJitxOrderChangeWarehouse(modifyWarehouseLog, operateUser);
                    if (v14.isOK()) {
                        newModifyWarehouseLog.setWorkflowState(result.getWorkflowState());
                    } else {
                        log.error("释放改仓锁失败，sourceCode：{}，result：{}", modifyWarehouseLog.getSourceCode(), v14);
                        // 释放改仓锁失败
                        newModifyWarehouseLog.setFailNumber(failNumber);
                        Date addDate = calculateAddDate(failNumber);
                        newModifyWarehouseLog.setNextCompensationDate(addDate);
                        newModifyWarehouseLog.setFailReason(v14.getMessage());
                        newModifyWarehouseLog.setFailNumber(failNumber);
                        omsOrderLogService.addUserOrderLog(modifyWarehouseLog.getOrderId(), modifyWarehouseLog.getBillNo(),
                                OrderLogTypeEnum.JITX_ORDER_UPDATE_WAREHOUSE.getKey(),
                                "改仓后释放锁失败：" + v14.getMessage(), null, v14.getMessage(),
                                SystemUserResource.getRootUser());
                    }
                } else {
                    if(VipJitxWorkflowStateEnum.CREATE.getKey().equals(result.getWorkflowState()) ||
                            VipJitxWorkflowStateEnum.DOING.getKey().equals(result.getWorkflowState())){
                        isSuccess = null;
                    }
                    //系统参数 是否开启强制改仓
                    String forceFlag = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(FORCE_CHANGE_WAREHOSE_ENABLE);
                    if (YesNoEnum.ONE.getKey().equals(forceFlag)) {
                        //改仓结果=驳回/取消时，自动强制改仓
                        ocBJitxModifyWarehouseLogFiService.markForcedWarehouseChangeSingle(modifyWarehouseLog.getId(), SystemUserResource.getRootUser(),result.getWorkflowState());
                    }
                    newModifyWarehouseLog.setFailNumber(failNumber);
                    Date addDate = calculateAddDate(failNumber);
                    newModifyWarehouseLog.setNextCompensationDate(addDate);
                    newModifyWarehouseLog.setWorkflowState(result.getWorkflowState());
                }
                int row = ocBJitxModifyWarehouseLogService.updateById(newModifyWarehouseLog);
                if (row <= 0) {
                    log.info("update OcBJitxModifyWarehouseLog fail, data:{}", modifyWarehouseLog);
                }

                //判断是否YY经销商改仓，是的话推送通知YY经销商
                if(null != isSuccess){
                    dealerOrderTaskService.notifyYy(newModifyWarehouseLog,isSuccess);
                }

            }
        } else {
            String message = valueHolderV14.getMessage();
            List<OcBJitxModifyWarehouseLog> batchUpdateDatas = modifyWarehouseLogList.stream().map(obj -> {
                OcBJitxModifyWarehouseLog update = new OcBJitxModifyWarehouseLog();
                update.setId(obj.getId());
                BaseModelUtil.makeBaseModifyField(update, operateUser);
                update.setFailNumber(Optional.of(obj.getFailNumber()).orElse(0) + 1);
                Date addDate = calculateAddDate(update.getFailNumber());
                update.setNextCompensationDate(addDate);
                update.setFailReason(message);
                if (StringUtils.isNotBlank(message) && message.length() > 2000) {
                    update.setFailReason(message.substring(0, 1999));
                }
                return update;
            }).collect(Collectors.toList());
            warehouseLogService.updateBatchById(batchUpdateDatas, 200);
        }
    }

    private Date calculateAddDate(Integer failNumber) {
        Integer modifyWarehouseIntervalTime =
                businessSystemParamService.getJitxOrderVipGetModifyWarehouseIntervalTime();
        int tempAddMinutes = calculate(failNumber - 1) * modifyWarehouseIntervalTime;
        if (tempAddMinutes > 120) {
            tempAddMinutes = 120;
        }
        Date addDate = DateUtils.addMinutes(new Date(), tempAddMinutes);
        return addDate;
    }

    /**
     * 构建获取改仓工单接口参数
     *
     * @param createWorkflowRequest  改仓请求参数
     * @param modifyWarehouseLogList 改仓日志
     * @param operateUser            操作人
     */
    private VipJitxGetChangeWarehouseWorkflowsRequest buildChangeWarehouseWorkflowRequest(
            CreateWorkflowRequest createWorkflowRequest,
            List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList, User operateUser) {
        if (CollectionUtils.isEmpty(modifyWarehouseLogList)) {
            return null;
        }
        VipJitxGetChangeWarehouseWorkflowsRequest request = new VipJitxGetChangeWarehouseWorkflowsRequest();
        request.setOperateUser(operateUser);
        request.setSellerNick(createWorkflowRequest.getSellerNick());
        request.setVendorId(Integer.valueOf(createWorkflowRequest.getVendorId()));

        List<String> orderSnList = new ArrayList<>(modifyWarehouseLogList.size());
        List<String> workflowSnList = new ArrayList<>(modifyWarehouseLogList.size());
        modifyWarehouseLogList.forEach(f -> {
            if (StringUtils.isNotEmpty(f.getSourceCode())) {
                orderSnList.add(f.getSourceCode());
            }
            if (StringUtils.isNotEmpty(f.getWorkflowSn())) {
                workflowSnList.add(f.getWorkflowSn());
            }
        });
        request.setOrderSns(orderSnList);
        request.setWorkflowSns(workflowSnList);
        request.setPage(1);
        request.setPageSize(100);
        return request;
    }

    /**
     * 更新失败原因
     *
     * @param modifyWarehouseLogList 日志记录
     * @param failReason             失败原因
     * @param operateUser            操作人
     */
    private void updateFailReasonById(List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList, String failReason, User operateUser) {
        List<Long> idList = modifyWarehouseLogList.stream().map(OcBJitxModifyWarehouseLog::getId).collect(Collectors.toList());
        ocBJitxModifyWarehouseLogService.updateFailReasonById(idList, failReason, operateUser);
    }

    /**
     * 检查JITX订单改仓结果，同时删除改仓成功的redisKey和更新零售发货单是否唯品会改仓为否
     *
     * @param modifyWarehouseLog
     * @param operateUser
     * @return
     */
    private ValueHolderV14 checkJitxOrderChangeWarehouse(OcBJitxModifyWarehouseLog modifyWarehouseLog, User operateUser) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.FAIL, null);
        // 通过唯品会getOrdersByOrderSn接口订单信息
        ValueHolderV14 valueHolderV14 = ipRpcService.getJitxOrderByOrderSn(
                modifyWarehouseLog.getSourceCode(), operateUser, modifyWarehouseLog.getCpCShopId());
        if (valueHolderV14 == null) {
            log.info("ipRpcService getJitxOrderByOrderSn RPC return empty,sourceCode:{}", modifyWarehouseLog.getSourceCode());
            v14.setMessage("调用IP服务获取JITX订单信息错误,返回为空");
            return v14;
        }
        if (!valueHolderV14.isOK()) {
            log.info("ipRpcService getJitxOrderByOrderSn RPC Error, sourceCode:{}, valueHolderV14:{}",
                    modifyWarehouseLog.getSourceCode(), valueHolderV14);
            v14.setMessage("调用IP服务获取JITX订单信息失败，" + valueHolderV14.getMessage());
            return v14;
        }
        log.info("通过唯品会getOrdersByOrderSn接口订单信息：{}", valueHolderV14);
        JSONArray data = (JSONArray) valueHolderV14.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            JSONObject jsonObject = (JSONObject) data.get(0);
            String orderSn = jsonObject.getString("order_sn");
            String transportNo = jsonObject.getString("transport_no");
            String mergedCode = jsonObject.getString("merged_code");
            // 改仓记录的唯品会仓库编码等于唯品会平台获取订单信息的唯品会仓库编码一致
            if (modifyWarehouseLog.getSourceCode().equals(orderSn)) {
                String deliveryWarehouse = jsonObject.getString("delivery_warehouse");
                if (modifyWarehouseLog.getNewDeliveryWarehouse() != null
                        && modifyWarehouseLog.getNewDeliveryWarehouse().equals(deliveryWarehouse)) {
                    //改仓后合包码 物流单号变更
                    this.changeMergedCodeAndExpressCode(modifyWarehouseLog.getOrderId(), mergedCode,transportNo);
                    updateOrderInfoService.removeJITXRedisChangeWarehouseFlag(modifyWarehouseLog.getOrderId(), operateUser);
                    v14.setCode(ResultCode.SUCCESS);

                    //插入审核中间表
                    OcBOrder updateOrder = new OcBOrder();
                    updateOrder.setId(modifyWarehouseLog.getOrderId());
                    omsAuditTaskService.createOcBAuditTask(updateOrder, OmsAuditTimeCalculateReason.RESERVE_AUDIT);
                } else {
                    log.info("delivery_warehouse Inconsistent orderInfo:{}, sourceCode:{}", jsonObject,
                            modifyWarehouseLog.getSourceCode(), modifyWarehouseLog.getNewDeliveryWarehouse());
                    v14.setMessage("平台与中台唯品会仓库编码不一致,改仓失败");
                }
            } else {
                log.info("ipRpcService getJitxOrderByOrderSn get orderInfo Inconsistent, orderInfo:{}, sourceCode:{}",
                        jsonObject, modifyWarehouseLog.getSourceCode());
                v14.setMessage("调用IP服务获取JITX订单信息订单号不相等");
            }
        } else {
            log.info("ipRpcService getJitxOrderByOrderSn RPC Data is empty, sourceCode:{}, valueHolderV14:{}",
                    modifyWarehouseLog.getSourceCode(), valueHolderV14);
            v14.setMessage("调用IP服务获取JITX订单信息为空");
        }
        return v14;
    }

    private void changeMergedCodeAndExpressCode(Long id, String mergedCode, String transportNo) {
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(id);
        if (ocBOrder != null) {
            String originMergedCode = StringUtils.isEmpty(ocBOrder.getMergedCode()) ? "" : ocBOrder.getMergedCode();
            String originExpressCode = StringUtils.isEmpty(ocBOrder.getExpresscode()) ? "" : ocBOrder.getExpresscode();
            OcBOrder update = new OcBOrder();
            update.setId(ocBOrder.getId());
            StringBuffer sb = new StringBuffer();
            if (!originMergedCode.equals(mergedCode)) {
                sb.append("合包码变更->");
                sb.append("原:");
                sb.append(originMergedCode);
                sb.append("新:");
                mergedCode = StringUtils.isNotEmpty(mergedCode) ? mergedCode : "";
                sb.append(mergedCode);
                update.setMergedCode(mergedCode);
            }
            if (!originExpressCode.equals(transportNo)) {
                if (sb.length() > 0) {
                    sb.append(";");
                }
                sb.append("物流单号变更->");
                sb.append("原:");
                sb.append(originExpressCode);
                sb.append("新:");
                transportNo = StringUtils.isNotEmpty(transportNo) ? transportNo : "";
                sb.append(transportNo);
                update.setExpresscode(transportNo);
            }
            if (sb.length() > 0) {
                ocBOrderMapper.updateById(update);
                ocBOrderMapper.clearRequiresDeliveryWarehouse(ocBOrder.getId());
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.JITX_ORDER_UPDATE_WAREHOUSE.getKey(),
                        String.format("JITX订单平台改仓成功:%s", sb.toString()), "", "", SystemUserResource.getRootUser());
            }
        }
    }

    private static int calculate(int n) {
        if (n <= 0) {
            return 1;
        }
        if (n >= 10) {
            return 120;
        }
        return 2 * calculate(n - 1);
    }

}
