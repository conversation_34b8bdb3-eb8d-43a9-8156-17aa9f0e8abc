package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.model.table.OcBShopSkuBatchInfo;

import java.util.List;

/**
 * 店铺最新发货效期表 Service 接口
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
public interface OcBShopSkuBatchInfoService {

    /**
     * 根据店铺编码和SKU查询记录
     *
     * @param shopCode 店铺编码
     * @param sku SKU编码
     * @return 店铺SKU批次信息
     */
    OcBShopSkuBatchInfo getByShopCodeAndSku(String shopCode, String sku);

    /**
     * 根据店铺ID和SKU查询记录
     *
     * @param shopId 店铺ID
     * @param sku SKU编码
     * @return 店铺SKU批次信息
     */
    OcBShopSkuBatchInfo getByShopIdAndSku(Long shopId, String sku);

    /**
     * 根据店铺编码查询所有SKU记录
     *
     * @param shopCode 店铺编码
     * @return SKU批次信息列表
     */
    List<OcBShopSkuBatchInfo> getByShopCode(String shopCode);

    /**
     * 根据店铺ID查询所有SKU记录
     *
     * @param shopId 店铺ID
     * @return SKU批次信息列表
     */
    List<OcBShopSkuBatchInfo> getByShopId(Long shopId);

    /**
     * 保存或更新SKU批次信息
     * 如果记录存在则更新，不存在则插入
     * 只有当新的生产日期比数据库中的更新时才会保存
     *
     * @param shopCode 店铺编码
     * @param sku SKU编码
     * @param produceDate 生产日期（yyyyMMdd格式）
     * @return 是否操作成功
     */
    boolean saveOrUpdateSkuBatchInfo(String shopCode, String sku, String produceDate);

    /**
     * 保存或更新SKU批次信息（带详细结果）
     * 如果记录存在则更新，不存在则插入
     * 只有当新的生产日期比数据库中的更新时才会保存
     *
     * @param shopCode 店铺编码
     * @param sku SKU编码
     * @param produceDate 生产日期（yyyyMMdd格式）
     * @return 保存结果详情
     */
    SaveResult saveOrUpdateSkuBatchInfoWithResult(String shopCode, String sku, String produceDate);

    /**
     * 保存或更新SKU批次信息（基于店铺ID）
     * 如果记录存在则更新，不存在则插入
     * 只有当新的生产日期比数据库中的更新时才会保存
     *
     * @param shopId 店铺ID
     * @param shopCode 店铺编码
     * @param shopTitle 店铺名称
     * @param sku SKU编码
     * @param produceDate 生产日期（yyyyMMdd格式）
     * @return 是否操作成功
     */
    boolean saveOrUpdateSkuBatchInfo(Long shopId, String shopCode, String shopTitle, String sku, String produceDate);

    /**
     * 保存或更新SKU批次信息（基于店铺ID，带详细结果）
     * 如果记录存在则更新，不存在则插入
     * 只有当新的生产日期比数据库中的更新时才会保存
     *
     * @param shopId 店铺ID
     * @param shopCode 店铺编码
     * @param shopTitle 店铺名称
     * @param sku SKU编码
     * @param produceDate 生产日期（yyyyMMdd格式）
     * @param receiverAddress 收货人地址
     * @return 保存结果详情
     */
    SaveResult saveOrUpdateSkuBatchInfoWithResult(Long shopId, String shopCode, String shopTitle, String sku, String produceDate, String receiverAddress);

    /**
     * 批量保存或更新SKU批次信息
     *
     * @param shopCode 店铺编码
     * @param skuBatchInfoList SKU批次信息列表（包含SKU和生产日期）
     * @return 成功处理的记录数
     */
    int batchSaveOrUpdateSkuBatchInfo(String shopCode, List<SkuBatchInfo> skuBatchInfoList);

    /**
     * 根据店铺编码和SKU列表批量查询
     *
     * @param shopCode 店铺编码
     * @param skuList SKU编码列表
     * @return SKU批次信息列表
     */
    List<OcBShopSkuBatchInfo> getByShopCodeAndSkuList(String shopCode, List<String> skuList);

    /**
     * 根据店铺ID和SKU列表批量查询
     *
     * @param shopId 店铺ID
     * @param skuList SKU编码列表
     * @return SKU批次信息列表
     */
    List<OcBShopSkuBatchInfo> getByShopIdAndSkuList(Long shopId, List<String> skuList);

    /**
     * 检查是否存在相同店铺、SKU、收件人地址的批次信息
     *
     * @param shopId 店铺ID
     * @param sku SKU编码
     * @param receiverAddress 收件人地址
     * @return true-存在，false-不存在
     */
    boolean existsByShopAndSkuAndAddress(Long shopId, String sku, String receiverAddress);

    /**
     * SKU批次信息内部类
     */
    class SkuBatchInfo {
        private String sku;
        private String produceDate;

        public SkuBatchInfo() {}

        public SkuBatchInfo(String sku, String produceDate) {
            this.sku = sku;
            this.produceDate = produceDate;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getProduceDate() {
            return produceDate;
        }

        public void setProduceDate(String produceDate) {
            this.produceDate = produceDate;
        }
    }

    /**
     * 保存结果内部类
     */
    class SaveResult {
        private boolean success;
        private String reason;
        private String oldProduceDate;
        private String newProduceDate;

        public SaveResult(boolean success, String reason) {
            this.success = success;
            this.reason = reason;
        }

        public SaveResult(boolean success, String reason, String oldProduceDate, String newProduceDate) {
            this.success = success;
            this.reason = reason;
            this.oldProduceDate = oldProduceDate;
            this.newProduceDate = newProduceDate;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getReason() {
            return reason;
        }

        public String getOldProduceDate() {
            return oldProduceDate;
        }

        public String getNewProduceDate() {
            return newProduceDate;
        }
    }
}
