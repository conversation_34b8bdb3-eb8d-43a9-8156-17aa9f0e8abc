package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4InvoiceNotice;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBInvoiceNotice;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBInvoiceNoticeRelation;
import com.jackrain.nea.oc.oms.model.result.OcBInvoiceNoticeResult;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNotice;
import com.jackrain.nea.oc.oms.nums.OcInvoiceTypeEnum;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.util.ImportUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @author:洪艺安
 * @since: 2019/7/27
 * @create at : 2019/7/27 14:14
 */
@Component
@Slf4j
public class InvoiceNoticeImportService extends CommandAdapter {

    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private InvoiceNoticeSaveService invoiceNoticeSaveService;
    @Autowired
    private OcBInvoiceNoticeMapper invoiceNoticeMapper;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    /**
     * @param
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     * @Description 开票信息模板下载
     * <AUTHOR>
     * @date 2019/7/27 20:00
     */
    public ValueHolderV14 downloadTemp() {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "开票通知信息导入模板下载成功！");
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            timeout = "1800000";
        }
        exportUtil.setTimeout(this.timeout);
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String mainNames[] = {"单据编号", "发票号", "开票日期", "开票人", "快递公司", "快递单号"};
        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List<String> mainList = Lists.newArrayList(mainNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "开票通知信息数据", "", mainList, Lists.newArrayList(), Lists.newArrayList(), false);
        String putMsg = exportUtil.saveFileAndPutOss(hssfWorkbook, "开票通知信息导入模板", user, "OSS-Bucket/EXPORT/OcBInvoiceNotice/");
        vh.setData(putMsg);
        return vh;
    }

    /**
     * @param extInvoiceNoticeList
     * @param user
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     * @Description 开票通知导入
     * <AUTHOR>
     * @date 2019/7/30 15:02
     */
    public ValueHolderV14 importInvoiceNotice(List<ExtOcBInvoiceNotice> extInvoiceNoticeList, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (extInvoiceNoticeList.size() == 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("导入失败,上传文件未录入数据!");
            return vh;
        }
        List<String> errMsgList = Lists.newArrayList();
        List<OcBInvoiceNotice> invoiceNoticeList = Lists.newArrayList();
        //1.组装开票通知集合
        extInvoiceNoticeList.forEach(extInvoiceNotice -> {
            try {
                OcBInvoiceNotice invoiceNotice = getImportInvoiceNotice(extInvoiceNotice);
                invoiceNoticeList.add(invoiceNotice);
            } catch (Exception e) {
                errMsgList.add(e.getMessage());
            }
        });
        //2.调用开票确认接口
        for (OcBInvoiceNotice invoiceNotice : invoiceNoticeList) {
            try {
                QuerySession session = new QuerySessionImpl(user);
                DefaultWebEvent event = new DefaultWebEvent("test", new HashMap(16));
                JSONObject param = new JSONObject();
                Long objid = invoiceNotice.getId();
                OcBInvoiceNoticeRelation invoiceNoticeRelation = new OcBInvoiceNoticeRelation();
                invoiceNoticeRelation.setInvoiceNotice(invoiceNotice);
                param.put("fixcolumn", JSON.parseObject(JSON.toJSONStringWithDateFormat(invoiceNoticeRelation,
                        "yyyy-MM-dd HH:mm:ss", SerializerFeature.IgnoreNonFieldGetter), Feature.OrderedField));
                param.put("objid", objid);
                param.put("importFlag", "Y");
                event.put("param", param);
                session.setEvent(event);
                ValueHolder corfirmVh = invoiceNoticeSaveService.confirmInvoiceNotice(session);
                if (!corfirmVh.isOK()) {
                    String errMsg = String.format("单据编号为[%s]的开票通知单" + (String) corfirmVh.getData().get("message"), invoiceNotice.getBillNo());
                    throw new NDSException(errMsg);
                }
            } catch (Exception e) {
                errMsgList.add(e.getMessage());
            }
        }
        //3.若有错误信息支持导出
        if (errMsgList.size() > 0) {
            List<OcBInvoiceNoticeResult> errExcelList = Lists.newArrayList();
            errMsgList.forEach(errMsg -> {
                OcBInvoiceNoticeResult ocBInvoiceNoticeResult = new OcBInvoiceNoticeResult();
                ocBInvoiceNoticeResult.setErrMsg(errMsg);
                errExcelList.add(ocBInvoiceNoticeResult);
            });
            int successNum = extInvoiceNoticeList.size() - errMsgList.size();
            vh.setCode(ImportUtil.IMPORT_ERROR_CODE);
            vh.setMessage(String.format("导入成功%d条,失败%d条", successNum, errMsgList.size()));
            String columnNames[] = {"错误原因"};
            List<String> columnList = Lists.newArrayList(columnNames);
            String keys[] = {"errMsg"};
            List<String> keyList = Lists.newArrayList(keys);
            exportUtil.setEndpoint(this.endpoint);
            exportUtil.setAccessKeyId(this.accessKeyId);
            exportUtil.setAccessKeySecret(this.accessKeySecret);
            exportUtil.setBucketName(this.bucketName);
            if (StringUtils.isEmpty(timeout)) {
                //如果获取不到apllo配置参数，设置默认过期时间为30分钟
                timeout = "1800000";
            }
            exportUtil.setTimeout(this.timeout);
            Workbook hssfWorkbook = exportUtil.execute("开票通知", "开票通知", columnList, keyList, errExcelList);
            String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "开票通知导入错误信息", user, "OSS-Bucket/EXPORT/OcBInvoiceNotice/");
            vh.setData(sdd);
        } else {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("导入成功");
        }
        return vh;
    }

    private OcBInvoiceNotice getImportInvoiceNotice(ExtOcBInvoiceNotice extInvoiceNotice) throws NDSException {
        OcBInvoiceNotice invoiceNotice = getInvoiceForBillNo(extInvoiceNotice.getBillNo());
        if (invoiceNotice == null) {
            throw new NDSException(String.format("不存在单据编号为[%s]的开票通知单或状态错误", extInvoiceNotice.getBillNo()));
        }
        if (StringUtils.isEmpty(extInvoiceNotice.getInvoiceNo())) {
            throw new NDSException(String.format("单据编号[%s]的开票号不能为空", extInvoiceNotice.getBillNo()));
        }
        String logisticsEname = extInvoiceNotice.getCpCLogisticsEname();
        invoiceNotice.setCpCLogisticsEname(logisticsEname);
        invoiceNotice.setInvoiceNo(extInvoiceNotice.getInvoiceNo());
        invoiceNotice.setLogisticsNo(extInvoiceNotice.getLogisticsNo());
        if (!OcInvoiceTypeEnum.ELE_INVOICE.getKey().equals(invoiceNotice.getInvoiceType())) {
            if (StringUtils.isEmpty(invoiceNotice.getLogisticsNo()) ||
                    StringUtils.isEmpty(invoiceNotice.getCpCLogisticsEname())) {
                throw new NDSException(String.format("单据编号[%s]发票类型不为电子发票，需填写快递公司及快递单号", extInvoiceNotice.getBillNo()));
            }
        }
        try {
            if (StringUtils.isNotEmpty(extInvoiceNotice.getInvoiceTimeStr())) {
                Calendar c = new GregorianCalendar(1900, Calendar.JANUARY, 0);
                Date invoiceTime = DateUtils.addDays(c.getTime(), Integer.valueOf(extInvoiceNotice.getInvoiceTimeStr()));
                invoiceNotice.setInvoiceTime(invoiceTime);
            }
        } catch (Exception e) {
            throw new NDSException(String.format("单据编号[%s]发票日期格式不符合规范", extInvoiceNotice.getBillNo()));
        }
        invoiceNotice.setInvoiceEname(extInvoiceNotice.getInvoiceEname());
        //获取快递公司信息
        try {
            CpLogistics cpLogistics = cpRpcService.queryLogisticsByEname(logisticsEname);
            invoiceNotice.setCpCLogisticsId(cpLogistics.getId());
            invoiceNotice.setCpCLogisticsEcode(cpLogistics.getEcode());
        } catch (Exception e) {
            if (!OcInvoiceTypeEnum.ELE_INVOICE.getKey().equals(invoiceNotice.getInvoiceType())) {
                throw new NDSException(String.format("快递公司[%s]查询失败或不存在", logisticsEname));
            }
        }
        return invoiceNotice;
    }

    /**
     * @param billNo
     * @return com.jackrain.nea.oc.oms.model.table.OcBInvoiceNotice
     * @Description 单据编号获取开票通知信息
     * <AUTHOR>
     * @date 2019/7/27 19:45
     */
    private OcBInvoiceNotice getInvoiceForBillNo(String billNo) {
        OcBInvoiceNotice invoiceNotice = null;
        JSONArray data = ES4InvoiceNotice.findJSONArrayByBillNo(billNo);
        if (data != null && !data.isEmpty()) {
            JSONObject jobj = data.getJSONObject(0);
            Long invoiceId = jobj.getLong("ID");
            invoiceNotice = invoiceNoticeMapper.selectById(invoiceId);
        }
        return invoiceNotice;
    }
}
