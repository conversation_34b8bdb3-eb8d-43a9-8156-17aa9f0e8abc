package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPaymentMapper;
import com.jackrain.nea.oc.oms.model.enums.GiftTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderItemExtend;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.psext.result.goodsResult;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 批量新增订单（導入）
 *
 * @author: 郑立轩
 * @since: 2019/6/5
 * create at : 2019/6/5 14:54
 */
@Slf4j
@Component
@Transactional(rollbackFor = Exception.class)
public class BatchSaveBillService {
    @Autowired
    private OmsOrderDifferenPriceService omsOrderDifferenPriceService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    //订单明细表
    private static final String ORDER_ITEM_TABLE_NAME = "OC_B_ORDER_ITEM";
    //订单表
    private static final String ORDER_TABLE_NAME = "OC_B_ORDER";
    //付款信息表
    private static final String PAYMENT_TABLE_NAME = "OC_B_ORDER_PAYMENT";
    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderItemMapper ocBorderItemMapper;
    @Autowired
    CpRpcService cpRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBOrderPaymentMapper paymentMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private BasicCpQueryService basicCpQueryService;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;


    public ValueHolderV14 batchSaveBill(List<OcBOrderExtend> ocBOrderExtends, Map<String, goodsResult> goodsResultMap, User loginUser) {

        List<OcBOrderExtend> ocBOrderExtendList = new ArrayList<>();//主表扩展
        List<OcBOrder> ocBOrderList = new ArrayList<>();//主表
        List<OcBOrderItemExtend> ocBOrderItemExtendList = new ArrayList<>();//明细表扩展
        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();//明细表
        List<OcBOrderPayment> ocBOrderPaymentList = new ArrayList<>();//支付表
        List<OcBOrderLog> OcBOrderLogList = new ArrayList<>();//日志表
        long l1 = System.currentTimeMillis();
        for (OcBOrderExtend borderDto : ocBOrderExtends) {
            //设置是否拦截 默认为0
            borderDto.setIsInterecept(0);
            //WMS撤回状态默认为0
            borderDto.setWmsCancelStatus(0);
            //  2019。9/05  新加主表赋值字段
            OperateUserUtils.defaultOperator(borderDto, loginUser);
            borderDto.setOrigOrderId((long) loginUser.getOrgId());
            borderDto.setPayTime(new Date());
            borderDto.setInvoiceStatus(0);
            // 2020/1/15添加新逻辑，如果是换货订单[判断导入的平台单号是不是重复如果重复 要在补充字段（suffix_info）添加信息，规则是：重复的平台单号-diy加时间点].其他类型订单不做处理
            if (borderDto.getOrderType() == null) {
                //订单类型 1 正常
                borderDto.setOrderType(OrderTypeEnum.NORMAL.getVal());
            }
            if (OrderTypeEnum.EXCHANGE.getVal().equals(borderDto.getOrderType())) {
                String message = checkSourceCode(borderDto.getSourceCode());
                if (StringUtils.isNotBlank(message)) {
                    borderDto.setSuffixInfo(message);
                } else {
                    borderDto.setSuffixInfo(null);
                }
            }
            //整理订单字段赋值
            OcBOrderExtend ocBOrderExtend = saveOrder(borderDto, goodsResultMap, loginUser);
            ocBOrderExtendList.add(ocBOrderExtend);
            ocBOrderItemExtendList.addAll(ocBOrderExtend.getOrderItemList());
            //获取订单支付表
            OcBOrderPayment payment = createPayMent(ocBOrderExtend);
            ocBOrderPaymentList.add(payment);

            //添加日志
            OcBOrderLog ocBOrderLog = omsOrderLogService.getOcBOrderLog(ocBOrderExtend.getId(), ocBOrderExtend.getBillNo(), OrderLogTypeEnum.ORDER_IMPORT.getKey(),
                    "订单导入成功", "", "", loginUser);
            OcBOrderLogList.add(ocBOrderLog);

        }

        //批量执行插入
        for (OcBOrderExtend ocBOrderExtend : ocBOrderExtendList) {
            OcBOrder ocBOrder = new OcBOrder();
            BeanUtils.copyProperties(ocBOrderExtend, ocBOrder);
            ocBOrder.setIsExchangeNoIn(0L);
            //@20200917 手工单导入 历史标赋值'N'防止 排除历史单丢数据
            ocBOrder.setIsHistory("N");
            //批量导入时，如果storeId 为空，则从下单店铺中获取 赋值 liqb
//            Long storeId = ocBOrder.getCpCStoreId();
            /*if (null == storeId) {
                Long shopId = ocBOrder.getCpCShopId();
                ShopQueryRequest request = new ShopQueryRequest();
                request.setShopIds(Lists.newArrayList(shopId));
                try {
                    Map<Long, CpShop> shopMap = basicCpQueryService.getShopInfo(request);
                    if (null != shopMap && null != shopMap.get(shopId)) {
                        CpShop cpShop = shopMap.get(shopId);
                        ocBOrder.setCpCStoreId(cpShop.getCpCStoreId());
                        ocBOrder.setCpCStoreEcode(cpShop.getCpCStoreEcode());
                        ocBOrder.setCpCStoreEname(cpShop.getCpCStoreEname());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }*/

            ocBOrderList.add(ocBOrder);
        }
        for (OcBOrderItemExtend ocBOrderItemExtend : ocBOrderItemExtendList) {
            OcBOrderItem ocBOrderItem = new OcBOrderItem();
            BeanUtils.copyProperties(ocBOrderItemExtend, ocBOrderItem);
            try {
                Map map = psRpcService.querySku(ocBOrderItem.getPsCSkuEcode());
                if (map != null) {
                    String data = JSON.toJSONString(map.get("data"));
                    JSONArray jsonArray = JSON.parseArray(data);
                    if (jsonArray != null && jsonArray.size() != 0) {
                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                        //标准价 和性别
                        ocBOrderItem.setSex(jsonObject.getLong("sex"));
                        ocBOrderItem.setPriceTag(jsonObject.getBigDecimal("tagPrice"));
                        ocBOrderItem.setPsCClrId(jsonObject.getLong("colorId"));
                        ocBOrderItem.setPsCClrEcode(jsonObject.getString("colorCode"));
                        ocBOrderItem.setPsCClrEname(jsonObject.getString("colorName"));
                        ocBOrderItem.setPsCSizeId(jsonObject.getLong("sizeId"));
                        ocBOrderItem.setPsCSizeEcode(jsonObject.getString("sizeCode"));
                        ocBOrderItem.setPsCSizeEname(jsonObject.getString("sizeName"));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            ocBOrderItemList.add(ocBOrderItem);
        }

        long l2 = System.currentTimeMillis();
        ocBOrderMapper.batchInsert(ocBOrderList);
        ocBorderItemMapper.batchInsert(ocBOrderItemList);
        paymentMapper.batchInsert(ocBOrderPaymentList);
        omsOrderLogService.save(OcBOrderLogList);
        for (OcBOrder ocBOrder : ocBOrderList) {
            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(ocBOrder.getId());
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            try {
                toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
            } catch (Exception e) {
                log.error(LogUtil.format("BatchSaveBillService.batchSaveBill.异常: {}"), Throwables.getStackTraceAsString(e));
            }
        }

        // 插入
        if (log.isDebugEnabled()) {
            log.debug("订单管理批量导入时，插入数据库耗时：" + (System.currentTimeMillis() - l2));
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "导入成功！");

    }

    public ValueHolderV14 batchSaveBill(List<OcBOrderExtend> ocBOrderExtends, Integer isGift, User loginUser) {
        // List<OcBOrderExtend> ocBOrderExtendList = new ArrayList<>();//主表扩展
        List<OcBOrder> ocBOrderList = new ArrayList<>();//主表
        // List<OcBOrderItemExtend> ocBOrderItemExtendList = new ArrayList<>();//明细表扩展
        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();//明细表
        List<OcBOrderPayment> ocBOrderPaymentList = new ArrayList<>();//支付表
        List<OcBOrderLog> OcBOrderLogList = new ArrayList<>();//日志表
        List<OcBToBeConfirmedTask> ocBToBeConfirmedTaskList = new ArrayList<>();
        long l1 = System.currentTimeMillis();
        for (OcBOrderExtend borderDto : ocBOrderExtends) {
            OcBOrder ocBOrder = new OcBOrder();
            BeanUtils.copyProperties(borderDto, ocBOrder);
            // @20200116 新增订单时给默认值0，为了待分配订单可以在零售发货单列表页展示
            ocBOrder.setCpCPhyWarehouseId(null);
            if (isGift != null && isGift == 1) {
                // 商品金额
                ocBOrder.setProductAmt(BigDecimal.ZERO);
                // 服务费
                ocBOrder.setServiceAmt(BigDecimal.ZERO);
                // 物流费用
                ocBOrder.setShipAmt(BigDecimal.ZERO);
                // 商品优惠金额
                ocBOrder.setProductDiscountAmt(BigDecimal.ZERO);
                // 订单优惠金额
                ocBOrder.setOrderDiscountAmt(BigDecimal.ZERO);
                // 调整金额
                ocBOrder.setAdjustAmt(BigDecimal.ZERO);
                // 订单总金额
                ocBOrder.setOrderAmt(BigDecimal.ZERO);
                // 已收金额（对应详情页已支付金额）已支付金额 = 订单总金额 (拆合单时的逻辑)
                ocBOrder.setReceivedAmt(BigDecimal.ZERO);
                ocBOrder.setIsHasgift(YesNoEnum.Y.getVal());
                List<OcBOrderItemExtend> orderItemList = borderDto.getOrderItemList();
                for (OcBOrderItem item : orderItemList) {
                    item.setRealAmt(BigDecimal.ZERO); //成交金额
                    item.setAdjustAmt(BigDecimal.ZERO);
                    item.setOrderSplitAmt(BigDecimal.ZERO);
                    item.setPriceActual(BigDecimal.ZERO);
                    item.setPrice(BigDecimal.ZERO);
                    item.setIsGift(1);
                }
            }
            ocBOrderList.add(ocBOrder);
            ocBOrderItemList.addAll(borderDto.getOrderItemList());
            //     ocBOrderExtendList.add(borderDto);
            //     ocBOrderItemExtendList.addAll(borderDto.getOrderItemList());
            //获取订单支付表
            //     OcBOrderPayment payment = createPayMent(borderDto);
            ocBOrderPaymentList.add(borderDto.getPayment());

            //添加日志
            //  OcBOrderLog ocBOrderLog = omsOrderLogService.getOcBOrderLog(borderDto.getId(), borderDto.getBillNo(), OrderLogTypeEnum.ORDER_IMPORT.getKey(),
            //         "订单导入成功", "", "", SystemUserResource.getRootUser().getLastloginip(), loginUser);
            OcBOrderLogList.add(borderDto.getOcBOrderLog());
            // 构建 占单任务表数据
            ocBToBeConfirmedTaskList.add(borderDto.getOcBToBeConfirmedTask());
        }

        long l2 = System.currentTimeMillis();
        ocBOrderMapper.batchInsert(ocBOrderList);
        ocBorderItemMapper.batchInsert(ocBOrderItemList);
        paymentMapper.batchInsert(ocBOrderPaymentList);
        omsOrderLogService.save(OcBOrderLogList);
        toBeConfirmedTaskService.batchInsertToBeConfirmedTask(ocBToBeConfirmedTaskList);
        return new ValueHolderV14<>(ResultCode.SUCCESS, "导入成功！");

    }

    /**
     * 检查平台单号是不是重复
     *
     * @param sourceCode 平台单号
     * @return 如果 重复就返回要在补充字段（suffix_info）添加信息，规则是：重复的平台单号-diy加时间点  如果不重复返回空字符串
     */
    private String checkSourceCode(String sourceCode) {
        StringBuilder sb = new StringBuilder();
        List<Long> orderIds = GSI4Order.getIdListBySourceCode(sourceCode);
        if (orderIds != null && orderIds.size() > 0) {
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
            String format1 = format.format(new Date());
            sb.append(sourceCode).append("-diy").append(format1);
        }
        return sb.toString();
    }

    private void rollBackData(OcBOrder ocBOrder, List<OcBOrderItemExtend> item, OcBOrderPayment payment) {
        Long id = ocBOrder.getId();
        ocBOrderMapper.deleteById(id);
        ocBorderItemMapper.deleteItemByorderId(id);
        paymentMapper.deletePaymentByorderId(id);

    }

    private OcBOrderPayment createPayMent(OcBOrderExtend ocBOrderExtend) {
        OcBOrderPayment payment = new OcBOrderPayment();
        payment.setId(ModelUtil.getSequence(PAYMENT_TABLE_NAME));
        payment.setOcBOrderId(ocBOrderExtend.getId());
        payment.setPayType(ocBOrderExtend.getPayType());
        payment.setAmtOrder(ocBOrderExtend.getOrderAmt());
        payment.setPaymentAmt(ocBOrderExtend.getOrderAmt());
        payment.setPayTime(new Date(System.currentTimeMillis()));
        payment.setOwnerename(SystemUserResource.getRootUser().getEname());
        payment.setPayStatus(OmsPayStatus.PAID.toInteger());
        payment.setAdClientId((long) SystemUserResource.getRootUser().getClientId());
        payment.setAdOrgId((long) SystemUserResource.getRootUser().getOrgId());
        payment.setIsactive("Y");
        payment.setCreationdate(new Date(System.currentTimeMillis()));
        return payment;

    }

    private OcBOrderExtend saveOrder(OcBOrderExtend borderDto, Map<String, goodsResult> goodsResultMap, User user) {
        borderDto.setId(ModelUtil.getSequence(ORDER_TABLE_NAME));
        /*-----------------------------------------------------------------------订单明细赋值----------------------------------------------------*/
        List<OcBOrderItemExtend> orderItemList = borderDto.getOrderItemList();
        for (OcBOrderItemExtend ocBOrderItemExtend : orderItemList) {
            ocBOrderItemExtend.setOcBOrderId(borderDto.getId());
            //  获取数量
            BigDecimal qty = ocBOrderItemExtend.getQty();
            if (qty == null) {
                qty = new BigDecimal(0);
            }
            // 获取成交金额
            BigDecimal realAmt = ocBOrderItemExtend.getRealAmt();
            // 成交单价
            BigDecimal price = ocBOrderItemExtend.getPriceActual();
            // 如果为赠品，成交金额设置为0
            if (price.compareTo(BigDecimal.ZERO) == 0) {
                ocBOrderItemExtend.setIsGift(1);
                ocBOrderItemExtend.setGiftType(GiftTypeEnum.SYSTEM.getVal());
                ocBOrderItemExtend.setRealAmt(BigDecimal.ZERO);
            } else {
                ocBOrderItemExtend.setRealAmt(qty.multiply(ocBOrderItemExtend.getPriceActual()));
            }
            ocBOrderItemExtend.setId(ModelUtil.getSequence(ORDER_ITEM_TABLE_NAME));
            //调俊鹏服务获取条码价格信息
            goodsResult goodsResult = goodsResultMap.get(ocBOrderItemExtend.getPsCSkuEcode());

            //国标码
            ocBOrderItemExtend.setBarcode(goodsResult.getGbCode());
            //商品名称
            ocBOrderItemExtend.setPsCProEname(goodsResult.getProName());
            //商品货号
            ocBOrderItemExtend.setPsCProEcode(goodsResult.getProEcode());
            //商品ID
            ocBOrderItemExtend.setPsCProId(goodsResult.getProId());
            //规格=尺寸+颜色
            ocBOrderItemExtend.setSkuSpec(goodsResult.getSpec());
            //标准价
            ocBOrderItemExtend.setPriceList(goodsResult.getPriceList());
            ocBOrderItemExtend.setPriceTag(goodsResult.getPriceList());
            if (ocBOrderItemExtend.getPrice() == null || ocBOrderItemExtend.getPrice().compareTo(BigDecimal.ZERO) == 0) {
                ocBOrderItemExtend.setPrice(Optional.ofNullable(goodsResult.getPriceList()).orElse(BigDecimal.ZERO));
            }
            //标准重量
            ocBOrderItemExtend.setStandardWeight(goodsResult.getWeight());
            Integer isGift = 0;
            if (ocBOrderItemExtend.getIsGift() != null) {
                isGift = ocBOrderItemExtend.getIsGift();
            }
            if (isGift == 1) {
                ocBOrderItemExtend.setPriceList(BigDecimal.ZERO);
                ocBOrderItemExtend.setPrice(BigDecimal.ZERO);
                ocBOrderItemExtend.setRealAmt(BigDecimal.ZERO);
                ocBOrderItemExtend.setAdjustAmt(BigDecimal.ZERO);
            } else {
                //设置明细调整价
                ocBOrderItemExtend.setAdjustAmt(realAmt.subtract(qty.multiply(ocBOrderItemExtend.getPrice())));
            }
            //是否占用库存
            ocBOrderItemExtend.setIsAllocatestock(0);
            //整单平摊金额
            ocBOrderItemExtend.setOrderSplitAmt(BigDecimal.ZERO);
            //优惠金额
            ocBOrderItemExtend.setAmtDiscount(BigDecimal.ZERO);
            //退款状态
            ocBOrderItemExtend.setRefundStatus(0);
            //退款平台编号
            ocBOrderItemExtend.setRefundId("0");
            //已退数量
            ocBOrderItemExtend.setQtyRefund(BigDecimal.ZERO);
            //买家是否已评价
            ocBOrderItemExtend.setIsBuyerRate(0);
            //发货状态
            ocBOrderItemExtend.setIsSendout(0);
            //预售状态
            ocBOrderItemExtend.setIsPresalesku(0);
            //发货失败次数
            ocBOrderItemExtend.setOuterrcount(0);
            ocBOrderItemExtend.setIsactive("Y");
            if (StringUtils.isBlank(user.getOrgId() + "")) {
                ocBOrderItemExtend.setAdOrgId(27L);
            } else {
                ocBOrderItemExtend.setAdOrgId((long) user.getOrgId());
            }
            if (StringUtils.isBlank(user.getClientId() + "")) {
                ocBOrderItemExtend.setAdClientId(37L);
            } else {
                ocBOrderItemExtend.setAdClientId((long) user.getClientId());
            }


        }
        /*-----------------------------------------------------------------订单主表赋值--------------------------------------------------------------*/
        borderDto.setAdOrgId((long) SystemUserResource.getRootUser().getOrgId());
        borderDto.setAdClientId((long) SystemUserResource.getRootUser().getClientId());
        Integer orderType = borderDto.getOrderType();
        if (orderType == null) {
            //订单类型 1 正常
            borderDto.setOrderType(OrderTypeEnum.NORMAL.getVal());
        }
        //订单状态 50 待分配
    /*    if (borderDto.getWarehouseType() != null) {
            if (borderDto.getWarehouseType() == 1 || borderDto.getWarehouseType() == 2) {
                borderDto.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_PLATFORM_DELIVERY.getVal());
                borderDto.setSuffixInfo("JCWareHouseOrCNWareHouse");
            } else {
                borderDto.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_PENDING_ALLOCATED.getVal());
            }
        } else {
            borderDto.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_PENDING_ALLOCATED.getVal());
        }*/
        borderDto.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_PENDING_ALLOCATED.getVal());
        //订单占用状态 0 未占单
        borderDto.setOccupyStatus(0);
        //旗帜颜色:0, 无1, 红2, 橙 3, 绿4,蓝5, 紫
        String orderFlag = borderDto.getOrderFlag();
        if (StringUtils.isNotEmpty(orderFlag)) {
            borderDto.setOrderFlag(orderFlag);
        } else {
            borderDto.setOrderFlag(String.valueOf(0));
        }
        //开票,默认为否
        Integer isInvoice = borderDto.getIsInvoice();
        if (isInvoice != null && isInvoice == 1) {
            borderDto.setIsInvoice(isInvoice);
        } else {
            borderDto.setIsInvoice(0);
        }
        //生成开票通知,默认为否
        Integer isGeninvoiceNotice = borderDto.getIsGeninvoiceNotice();
        if (isGeninvoiceNotice != null && isGeninvoiceNotice == 1) {
            borderDto.setIsGeninvoiceNotice(isGeninvoiceNotice);
        } else {
            borderDto.setIsGeninvoiceNotice(0);
        }

        //商品优惠金额
        borderDto.setProductDiscountAmt(BigDecimal.ZERO);
        //订单优惠金额
        borderDto.setOrderDiscountAmt(BigDecimal.ZERO);
        //调整金额
        borderDto.setAdjustAmt(BigDecimal.ZERO);
        //服务费
        BigDecimal serviceAmt = borderDto.getServiceAmt();
        if (serviceAmt == null) {
            serviceAmt = BigDecimal.ZERO;
            borderDto.setServiceAmt(serviceAmt);
        }
        //订单总额
        borderDto.setOrderAmt(BigDecimal.ZERO);
        //已收金额
        borderDto.setReceivedAmt(BigDecimal.ZERO);
        //代销结算价
        borderDto.setConsignAmt(BigDecimal.ZERO);
        //代销运费
        borderDto.setConsignShipAmt(BigDecimal.ZERO);
        //应收金额
        borderDto.setAmtReceive(BigDecimal.ZERO);
        //到付应收金额
        borderDto.setCodAmt(BigDecimal.ZERO);
        //操作费
        // borderDto.setOperateAmt(BigDecimal.ZERO);
        //应收平台金额
        borderDto.setJdReceiveAmt(BigDecimal.ZERO);
        //京东结算金额
        borderDto.setJdSettleAmt(BigDecimal.ZERO);
        //物流成本
        borderDto.setLogisticsCost(BigDecimal.ZERO);
        //是否拆分订单
        borderDto.setIsSplit(0);
        //是否已经拦截
        borderDto.setIsInterecept(0);
        //是否已经退款中
        borderDto.setIsInreturning(0);
        //订单来源
        borderDto.setOrderSource("手工新增");
        //是否有赠品
        borderDto.setIsHasgift(0);
        //是否生成调拨零售
        // borderDto.setIsTodrp(0);
        //是否已给物流
        // borderDto.setIsGiveLogistic(0);
        //WMS撤回状态
        borderDto.setWmsCancelStatus(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger());
        //退货状态
        borderDto.setReturnStatus(0);
        //退款审核状态
        borderDto.setRefundConfirmStatus(0);
        //自动审核状态
        borderDto.setAutoAuditStatus(0);
        //京仓订单
        borderDto.setIsJcorder(0);
        //双11预售状态
        borderDto.setDouble11PresaleStatus(0);
        //会否催发货
        borderDto.setIsOutUrgency(0);
        //是否虚拟订单
        borderDto.setIsInvoice(0);
        borderDto.setIsForce(0L); //强制平台发货默认 0
        //如果订单状态失已审核 ，传wms 改成是 否则 改成否
        if (OmsOrderStatus.CHECKED.toInteger().equals(borderDto.getOrderStatus())) {
            borderDto.setIsOverfive(1L);
        } else {
            borderDto.setIsOverfive(0L); // 传wms 是否传5次默认为0
        }
        borderDto.setIsExchangeNoIn(0L);  // 是否是换货未入库默认为 0
//        borderDto.setToSettleStatus(0L);  // 戴传 结算标致 默认 0
        borderDto.setIsMultiPack(0L);  // 多包裹标志 默认为0
        //是否组合订单
        borderDto.setIsCombination(0);
        //系统预售状态
        // borderDto.setSysPresaleStatus(0);
        borderDto.setIsactive("Y");
        //创建时间
        borderDto.setCreationdate(new Date(System.currentTimeMillis()));
        //是否插入核销流水
        // borderDto.setIsWriteoff(1);
        //生成订单编号
        String autoNormalBillNo = sequenceUtil.buildBillNo();
        if (StringUtils.isNotEmpty(autoNormalBillNo)) {
            borderDto.setBillNo(autoNormalBillNo);
        } else {
            borderDto.setDesc("序号生成器异常!保存失败");
            return borderDto;
        }
        //是否有赠品
        long count = borderDto.getOrderItemList().stream().filter(s -> Objects.equals(s.getIsGift(), 1)).count();
        if (count == 0) {
            borderDto.setIsHasgift(0);
        } else {
            borderDto.setIsHasgift(1);
        }
        //商品数量
        BigDecimal qtyAll = BigDecimal.ZERO;
        //商品总额
        BigDecimal productAmt = BigDecimal.ZERO;
        //商品优惠金额
        BigDecimal productDiscountAmtSum = BigDecimal.ZERO;
        //订单调整金额
        BigDecimal orderAdjust = BigDecimal.ZERO;
        for (OcBOrderItemExtend ocBOrderItemExtend : orderItemList) {
            //商品明细数量
            BigDecimal qty = ocBOrderItemExtend.getQty();
            //商品明细标准价
            BigDecimal price = ocBOrderItemExtend.getPrice();
            qtyAll = ocBOrderItemExtend.getQty().add(qtyAll);
            productAmt = qty.multiply(price).add(productAmt);
            orderAdjust = orderAdjust.add(ocBOrderItemExtend.getAdjustAmt());
            //明细与主表ID绑定
            ocBOrderItemExtend.setOcBOrderId(borderDto.getId());

        }
        borderDto.setQtyAll(qtyAll);
        //商品总额
        borderDto.setProductAmt(productAmt);
        //商品优惠金额 【由于订单明细导入时 默认优惠金额为0，所以订单主表中优惠金额也为0】
        borderDto.setProductDiscountAmt(productDiscountAmtSum);
        //订单优惠金额 同上SUM(整单平摊金额)
        borderDto.setOrderDiscountAmt(BigDecimal.ZERO);
        //订单调整金额
        borderDto.setAdjustAmt(orderAdjust);
        //订单总额 商品总额+物流费用+调整金额+服务费-商品优惠金额-订单优惠金额
        BigDecimal orderAmtSum = productAmt.add(borderDto.getShipAmt() == null ? BigDecimal.ZERO : borderDto.getShipAmt())
                .add(orderAdjust).add(serviceAmt).subtract(productDiscountAmtSum);
        borderDto.setOrderAmt(orderAmtSum);
        //代销结算金额
        borderDto.setConsignAmt(BigDecimal.ZERO);
        //应收金额  标准价*数量
        borderDto.setAmtReceive(productAmt);
        //【tid】:在新增全渠道订单时，如果【平台单号】（SourceCode）字段有值，则将【平台单号】赋值给tid
        String sourceCode = borderDto.getSourceCode();
        if (StringUtils.isNotEmpty(sourceCode)) {
            borderDto.setTid(sourceCode);
        }
        if (StringUtils.isBlank(user.getOrgId() + "")) {
            borderDto.setAdOrgId(27L);
        } else {
            borderDto.setAdOrgId((long) user.getOrgId());
        }
        if (StringUtils.isBlank(user.getClientId() + "")) {
            borderDto.setAdClientId(37L);
        } else {
            borderDto.setAdClientId((long) user.getClientId());
        }
        borderDto.setOrderDate(new Date());
        //配送费用
        if (borderDto.getShipAmt() == null) {
            borderDto.setShipAmt(BigDecimal.valueOf(0));
        }
        //设置是否复制标记
        if (OcOrderTagEum.TAG_HAND.getVal().equals(borderDto.getOrderSource()) &&
                StringUtils.isNotEmpty(borderDto.getCopyReason())) {
            borderDto.setIsCopyOrder(OcBOrderConst.IS_STATUS_IY);
        } else {
            borderDto.setIsCopyOrder(OcBOrderConst.IS_STATUS_IN);
        }
        return borderDto;
    }


}


