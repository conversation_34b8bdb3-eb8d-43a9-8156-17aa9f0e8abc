package com.jackrain.nea.oc.oms.services;

import com.burgeon.r3.sg.stocksync.common.SgResultCode;
import com.jackrain.nea.oc.oms.model.constant.OcOmsConstant;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4MessageConsumeLog;
import com.jackrain.nea.oc.oms.mapper.OcBMessageConsumeLogMapper;
import com.jackrain.nea.oc.oms.model.table.OcBMessageConsumeLog;
import com.jackrain.nea.oc.oms.nums.MessageConsumeStatusEnum;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description： 消息消费日志表service
 * Author: RESET
 * Date: Created in 2020/8/23 16:19
 * Modified By:
 */
@Service
@Slf4j
public class OcBMessageConsumeLogService {

    // 业务redis 超时设置
    public static final long REDIS_TIME_OUT = 1_000L * 60 * 60 * 24;
    // 消息字段最大长度
    public static final Integer MAX_MSG_SIZE = 2000;

    @Autowired
    OcBMessageConsumeLogMapper messageConsumeLogMapper;

    /**
     * 保存数据 -- 可能是新增，也可能是插入
     *
     * @param messageConsumeLog
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int save(OcBMessageConsumeLog messageConsumeLog, User user) {
        if (Objects.nonNull(messageConsumeLog)) {
            // 先查询是否已经存在：按主键查询
            String topic = messageConsumeLog.getTopic();
            String messageId = messageConsumeLog.getMessageId();

            OcBMessageConsumeLog existsLog = findByUnique(topic, messageId);
            log.debug(LogUtil.format("save.existsLog.id:{}"), Objects.isNull(existsLog) ? -1 : existsLog.getId());

            if (Objects.isNull(existsLog)) {
                // 插入行
                return insert(messageConsumeLog, user);
            } else {
                // 如果已经存在，则更新
                return updateStatusById(
                        existsLog.getId(),
                        messageConsumeLog.getConsumeStatus(),
                        messageConsumeLog.getRetryCount(),
                        messageConsumeLog.getRetryErrorMsg(),
                        user
                );
            }
        }

        return SgResultCode.FAIL;
    }

    /**
     * 新增数据
     *
     * @param messageConsumeLog
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int insert(OcBMessageConsumeLog messageConsumeLog, User user) {
        if (Objects.nonNull(messageConsumeLog)) {
            // 取用户
            user = getUser(user);
            // 设置基础属性
            OmsModelUtil.setDefault4Add(messageConsumeLog, user);
            // 设置ID
            messageConsumeLog.setId(ModelUtil.getSequence(OcOmsConstant.OC_B_MESSAGE_CONSUME_LOG));
            // 裁剪
            cutErrorMsg(messageConsumeLog);
            int r = messageConsumeLogMapper.insert(messageConsumeLog);

            // 保存redis
            if (r > 0) {
                saveToRedis(messageConsumeLog);
            }

            return r;
        }

        return SgResultCode.FAIL;
    }

    /**
     * 保存到Redis
     *
     * @param messageConsumeLog
     */
    public void saveToRedis(OcBMessageConsumeLog messageConsumeLog) {
        if (Objects.nonNull(messageConsumeLog)) {
            saveToRedis(messageConsumeLog.getTopic(), messageConsumeLog.getMessageId(), messageConsumeLog.getId());
        }
    }

    /**
     * 单据ID保存到redis
     *
     * @param topic
     * @param messageId
     * @param id
     */
    public void saveToRedis(String topic, String messageId, Long id) {
        log.debug(LogUtil.format("OcBMessageConsumeLogService.saveToRedis.info:topic/messageId/id"
                , topic, messageId, id));

        if (Objects.nonNull(topic) && Objects.nonNull(messageId) && Objects.nonNull(id)) {
            String redisKey = BllRedisKeyResources.buildMqMessageKey(topic, messageId);
            RedisMasterUtils.getObjRedisTemplate().opsForValue().set(redisKey, id, REDIS_TIME_OUT,
                    TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 从Redis查id
     *
     * @param topic
     * @param messageId
     * @return
     */
    public Long selectIdFromRedis(String topic, String messageId) {
        if (Objects.nonNull(topic) && Objects.nonNull(messageId)) {
            String redisKey = BllRedisKeyResources.buildMqMessageKey(topic, messageId);
            Object r = RedisMasterUtils.getObjRedisTemplate().opsForValue().get(redisKey);
            if (Objects.nonNull(r)) {
                return Long.valueOf(r.toString());
            }
        }

        return -1L;
    }

    /**
     * 更新为错误状态
     *
     * @param id
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateStatusToErrorById(Long id, User user) {
        return updateStatusById(id, MessageConsumeStatusEnum.ERROR.getValue(), 0, null, user);
    }

    /**
     * 更新成重试错误
     *
     * @param topic
     * @param messageId
     * @param user
     * @return
     */
    public int updateStatusToRetrySuccessByUnique(String topic, String messageId, Integer retryCount, User user) {
        return updateStatusByUnique(topic, messageId, MessageConsumeStatusEnum.RETRY_SUCCESS.getValue(), retryCount, null, user);
    }

    /**
     * 更新成重试成功
     *
     * @param topic
     * @param messageId
     * @param retryCount
     * @param user
     * @return
     */
    public int updateToRetrySuccessByUnique(String topic, String messageId, Integer retryCount, User user) {
        return updateStatusByUnique(topic, messageId, MessageConsumeStatusEnum.RETRY_SUCCESS.getValue(), retryCount, null, user);
    }

    /**
     * 依据主键更新状态
     *
     * @param topic
     * @param messageId
     * @param status
     * @param user
     * @return
     */
    public int updateStatusByUnique(String topic, String messageId, Integer status, Integer retryCount, String stackTrace, User user) {
        OcBMessageConsumeLogService bean = ApplicationContextHandle.getBean(OcBMessageConsumeLogService.class);
        if (Objects.nonNull(topic) && Objects.nonNull(messageId)) {
            // 从ES查
            OcBMessageConsumeLog consumeLog = findByUnique(topic, messageId);

            if (Objects.nonNull(consumeLog) && Objects.nonNull(consumeLog.getId())) {
                return bean.updateStatusById(consumeLog.getId(), status, retryCount, stackTrace, user);
            }
        }

        return SgResultCode.FAIL;
    }

    /**
     * 更新状态
     *
     * @param id
     * @param status
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateStatusById(Long id, Integer status, Integer retryCount, String stackTrace, User user) {
        // 取用户
        user = getUser(user);

        if (Objects.nonNull(id) && Objects.nonNull(status)) {
            OcBMessageConsumeLog update = new OcBMessageConsumeLog();
            update.setId(id);
            update.setConsumeStatus(status);

            if (Objects.nonNull(retryCount)) {
                update.setRetryCount(retryCount);
            }

            if (Objects.nonNull(stackTrace)) {
                update.setRetryErrorMsg(stackTrace);
            }

            OmsModelUtil.setDefault4Upd(update, user);
            cutErrorMsg(update);

            return messageConsumeLogMapper.updateById(update);
        }

        return SgResultCode.FAIL;
    }

    /**
     * 依据ID查找
     *
     * @param id
     * @return
     */
    public OcBMessageConsumeLog findById(Long id) {
        if (Objects.nonNull(id)) {
            return messageConsumeLogMapper.selectById(id);
        }

        return null;
    }

    /**
     * 按主键查找
     *
     * @param topic
     * @param messageId
     * @return
     */
    public OcBMessageConsumeLog findByUnique(String topic, String messageId) {
        if (Objects.nonNull(topic) && Objects.nonNull(messageId)) {
            Long id = ES4MessageConsumeLog.selectIdByTopicAndMsgId(topic, messageId);

            // 第一次判断后要从redis查一次
            if (Objects.isNull(id)) {
                // 从redis查
                id = selectIdFromRedis(topic, messageId);
            }

            if (Objects.nonNull(id) && id.longValue() > 0) {
                return findById(id);
            }
        }

        return null;
    }

    /**
     * 截取
     *
     * @param consumeLog
     */
    private void cutErrorMsg(OcBMessageConsumeLog consumeLog) {
        if (Objects.nonNull(consumeLog)) {
            String errorMsg = consumeLog.getErrorMsg();
            String retryErrorMsg = consumeLog.getRetryErrorMsg();

            if (Objects.nonNull(errorMsg) && length(errorMsg) > MAX_MSG_SIZE) {
                consumeLog.setErrorMsg(errorMsg.substring(0, MAX_MSG_SIZE - 1));
            }

            if (Objects.nonNull(retryErrorMsg) && length(retryErrorMsg) > MAX_MSG_SIZE) {
                consumeLog.setRetryErrorMsg(retryErrorMsg.substring(0, MAX_MSG_SIZE - 1));
            }
        }
    }

    /**
     * 字符串长度
     *
     * @param text
     * @return
     */
    private int length(String text) {
        if (Objects.isNull(text)) {
            return 0;
        } else {
            return text.length();
        }
    }

    /**
     * 取用户
     *
     * @param user
     * @return
     */
    public static User getUser(User user) {
        if (Objects.nonNull(user)) {
            return user;
        } else {
            return R3SystemUserResource.getSystemRootUser();
        }
    }

    /**
     * 获取实例
     *
     * @return
     */
    public static OcBMessageConsumeLogService getInstance() {
        return ApplicationContextHandle.getBean(OcBMessageConsumeLogService.class);
    }

}
