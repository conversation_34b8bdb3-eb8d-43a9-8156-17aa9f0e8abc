package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoAdjustSaveRequest;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.IsGenAdjustEnum;
import com.jackrain.nea.oc.oms.model.enums.IsMatchEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWithoutOrigEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.VirtualInStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 退货入库单匹配
 *
 * @author: 周琳胜
 * @since: 2019/3/25
 * create at : 2019/3/25 14:45
 */
@Slf4j
@Component
@Deprecated
public class ReturnReceiptMatchService {

    @Autowired
    OcBRefundInMapper ocBRefundInMapper;

    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderFiMapper;

    @Autowired
    BuildSequenceUtil sequenceUtil;

    @Autowired
    OcBRefundInProductItemMapper ocBRefundInProductItemMapper;

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    SgRpcService sgRpcService;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    OcBReturnStockInService ocBReturnStockInService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private ReturnMatchLogService returnMatchLogService;

    @Autowired
    private OcBReturnOrderBatchAddService batchAddService;

   /* @Autowired
    private OcRefundInMatchService ocRefundInMatchService;*/


    /**
     * 退货入库单匹配
     *
     * @param obj  入参
     * @param user 用户
     * @return 结果
     */
    @Deprecated
    public ValueHolderV14 returnReceiptMatch(JSONObject obj, User user) {

        log.debug("入库匹配.start" + obj);
        ValueHolderV14 vh = new ValueHolderV14();
        int matchSuccess = 0;
        if (null == obj) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        // 获得退货入库单ID
        Long id = obj.getLong("ID");
        //redis锁单
//        String lockRedisKey = BllRedisKeyResources.buildLockReturnInKey(id);
//        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
//        try {
//            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
        // 初始化 是否无原单条码
        Integer isWithoutOrig = null;
        // 退单编号
        Long ocBReturnOrderId = null;
        // 订单编号
        Long ocBOrderId = null;
        // 获取退货入库单信息：买家昵称，收货人手机号，收货人姓名，物流单号
        String userNick, receiverMobile, receiverName, logisticNumber;
        OcBRefundIn ocBRefundIn = ocBRefundInMapper.selectById(id);
        if (ocBRefundIn == null) {
            throw new NDSException(Resources.getMessage("未能查询到对应的退货入库单信息!", user.getLocale()));
        } else {
            userNick = ocBRefundIn.getUserNick(); // 买家昵称
            receiverMobile = ocBRefundIn.getReceiverMobile(); // 收货人手机号
            receiverName = ocBRefundIn.getReceiverName(); // 收货人姓名
            logisticNumber = ocBRefundIn.getLogisticNumber(); // 物流单号
        }
        // 根据id查出入库单明细
        QueryWrapper<OcBRefundInProductItem> wrapper = new QueryWrapper<>();
        wrapper.eq("oc_b_refund_in_id", id);
        List<OcBRefundInProductItem> ocBRefundInProductItems = ocBRefundInProductItemMapper.selectList(wrapper);
        if (ocBRefundInProductItems.isEmpty()) {
            throw new NDSException(Resources.getMessage("未能查询到对应的入库单明细信息!", user.getLocale()));
        }
        // 遍历出库单明细进行判断
        // 存放匹配成功的入库单明细集合
        List<OcBRefundInProductItem> productItems = new ArrayList<>();
        Set<Long> setIds = new HashSet<Long>();
        a:
        for (OcBRefundInProductItem item : ocBRefundInProductItems) {
            ValueHolderV14 holderV14 = new ValueHolderV14();
            isWithoutOrig = item.getIsWithoutOrig();
            // 筛选【是否无原单条码】为是的明细进行下一把判断
            if (IsWithoutOrigEnum.IS_WITHOUT_ORIG.getVal().equals(isWithoutOrig)) {
                ocBReturnOrderId = item.getOcBReturnOrderId();
                ocBOrderId = item.getOcBOrderId();
                //退单编号不存在，订单编号存在，则对未匹配的退货入库明细根据订单号进行分组生成退换货订单
                // @20200717 从insertReturnOrder()逻辑上看，ocBOrderId应该不为空的，所以一下||应该改为&&
                // if(ocBReturnOrderId == null || ocBOrderId != null){
                if (ocBReturnOrderId == null && ocBOrderId != null) {
                    //生成退换货订单
                    insertReturnOrder(ocBOrderId);
                    //组装调用退货入库服务结构
                    QueryWrapper<OcBReturnOrder> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("ORIG_ORDER_ID", ocBOrderId);
                    OcBReturnOrder ocBReturnOrder = ocBReturnOrderFiMapper.selectOne(queryWrapper);
                    if (null != ocBReturnOrder) {
                        productItems.add(item);
                        setIds.add(ocBReturnOrder.getId());
                        matchSuccess++;
                        continue a;
                    }
                }
                //根据订单和退单编号 判断是否直接调用退货入库服务
                if (ocBReturnOrderId != null) {
                    productItems.add(item);
                    setIds.add(ocBReturnOrderId);
                    matchSuccess++;
                } else {
                    // 依次去查询退换货订单表中，是否存在状态为等待退货入库的退换货订单
                    // 物流单号---------------------------------------------------------------------------------
                    // 从ES查退单
                    List<OcBReturnOrder> logisticNumberlist = new ArrayList<>();
                    if (StringUtils.isNotEmpty(logisticNumber)) {
                        JSONObject whereKeys = new JSONObject();
                        whereKeys.put("LOGISTICS_CODE", logisticNumber);
                        JSONObject search = ES4ReturnOrder.getReturnOrderSearchByUserinfo(whereKeys, null, null, 1000, 0, new String[]{"ID"});
                        JSONArray arrayObj = search.getJSONArray("data");
                        for (Object o : arrayObj) {
                            JSONObject jsonObject = (JSONObject) o;
                            Long reId = jsonObject.getLong("ID");
                            OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(reId);
                            if (null != ocBReturnOrder) {
                                logisticNumberlist.add(ocBReturnOrder);
                            }
                        }
                    }
                    // 判断查出的退换货订单集合是否为空,不为空进行匹配
                    if (CollectionUtils.isNotEmpty(logisticNumberlist)) {
                        for (OcBReturnOrder ocBReturnOrder : logisticNumberlist) {
                            // 获得本次循环中退换货订单的退货状态
                            Integer returnStatus = ocBReturnOrder.getReturnStatus();
                            // 获得虚拟入库状态
                            Integer inventedStatus = ocBReturnOrder.getInventedStatus();
                            // 获得是否原退字段值 20190830修改为是否传WMS为否
                            Integer isBack = ocBReturnOrder.getIsNeedToWms().intValue();
                            // 获得是否生成零售字段值
                            Integer is_todrp = ocBReturnOrder.getIsTodrp();
                            // 获得退货订单明细
                            QueryWrapper<OcBReturnOrderRefund> itemsWrapperUser = new QueryWrapper<>();
                            itemsWrapperUser.eq("oc_b_return_order_id", ocBReturnOrder.getId());
                            List<OcBReturnOrderRefund> items = ocBReturnOrderRefundMapper.selectList(itemsWrapperUser);
                            // 判断入库数量是否大于等于退换货订单申请数量
                            //        Boolean checkNum = checkNumber(ocBRefundInProductItems, items);
                            // 限制条件 是否存在状态为等待退货入库的退换货订单
                            Boolean rs = ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus);
                            Boolean back = ReturnStatusEnum.NOT_BACK.getVal().equals(isBack);
                            Boolean todrp = ReturnStatusEnum.NOT_TODRP.getVal().equals(is_todrp);
                            Boolean invents = VirtualInStatusEnum.NOT.toInt() == inventedStatus;
                            if ((rs && back && todrp) || (invents && todrp)) {
                                // 判断入库单明细是否匹配
                                if (IsMatchEnum.UN_MATCH.getVal().equals(item.getIsMatch())) {
                                    // 遍历根据物流单号查出的退换货订单信息的明细信息 跟入库单明细信息的条码比较
                                    for (int i = 0; i < items.size(); i++) {
                                        // 一致说明匹配成功
                                        Long realSkuId = item.getRealSkuId();
                                        if (realSkuId != null && !(items.get(i).getPsCSkuId().equals(realSkuId))) {
                                            //退货入库 实收条码和 退单发出条码不一致，匹配下一条明细
                                            if (log.isDebugEnabled()) {
                                                log.debug("根据物流单号，入库匹配，实收条码和发出条码不一致id:{},realSkuId:{}", id, realSkuId);
                                            }
                                            continue;
                                        }
                                        if (items.get(i).getPsCSkuId().equals(realSkuId)
                                                || items.get(i).getPsCSkuId().equals(item.getPsCSkuId())) {
                                            if (checkMatchNum(items.get(i))) {
                                                // 插入入库匹配日志
                                                String type = "自动匹配退单";
                                                String message = id + "根据物流单号，匹配退单编号" + ocBReturnOrder.getId() + "成功，匹配条码" + item.getPsCSkuEcode();
                                                returnMatchLogService.insertLogCheck(id, type, message, user);

                                                // 更改入库单明细的【退换货订单号】
                                                Long newOcbReturnOrderId = ocBReturnOrder.getId();
                                                Long itemId = item.getId();
                                                Long ocbRefundInId = item.getOcBRefundInId();
                                                int count = ocBRefundInProductItemMapper.updateOcbReturnOrderId(newOcbReturnOrderId, itemId, ocbRefundInId);
                                                log.debug("根据物流单号，入库匹配" + id + "更新退货单号" + count);

                                                // 更新退换货订单明细的匹配数量字段  默认为0 每次+1
                                                Long matchNum = items.get(i).getQtyMatch() + 1L;
                                                int match = ocBReturnOrderRefundMapper.updateMatchNum(matchNum, newOcbReturnOrderId, items.get(i).getId());
                                                items.get(i).setQtyMatch(matchNum);
                                                log.debug("根据物流单号，入库匹配" + id + "更新匹配数量" + match + "退单明细id" + items.get(i).getId());
                                                // 回写入库单字段值
                                                Boolean bl = writeBackFields(id, ocBReturnOrder, user);
                                                log.debug("根据物流单号，入库匹配" + id + "回写入库单字段值" + bl);
                                                //获取新的明细信息
                                                QueryWrapper<OcBRefundInProductItem> newWrapper = new QueryWrapper<>();
                                                newWrapper.eq("id", itemId).eq("oc_b_refund_in_id", ocbRefundInId);
                                                OcBRefundInProductItem eSocBRefundInProductItem = ocBRefundInProductItemMapper.selectOne(newWrapper);
//                                                try {
//                                                    Boolean flag = SpecialElasticSearchUtil.indexDocument(OC_B_RETURN_IN_INDEX_NAME, OC_B_RETURN_IN_PRODUCT_ITEM_TYPE_NAME,
//                                                            eSocBRefundInProductItem, itemId, ocbRefundInId);
//                                                    if (!flag) {
//                                                        throw new NDSException("推送ES失败");
//                                                    }
//                                                } catch (Exception e) {
//                                                    holderV14.setCode(ResultCode.FAIL);
//                                                    holderV14.setMessage(id + "推送ES失败!");
//                                                    vh.setData(holderV14);
//                                                    log.debug("根据物流单号，入库匹配异常" + id + vh.toString());
//                                                }
                                                //退货入库商品明细设拿到的退单id
                                                item.setOcBReturnOrderId(newOcbReturnOrderId);
                                                //退货入库商品明细
                                                productItems.add(item);
                                                setIds.add(newOcbReturnOrderId);
                                                matchSuccess++;
                                                continue a;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(id + "根据物流单号，未能查询到对应的退换货订单信息!");
                        if (log.isDebugEnabled()) {
                            log.debug(this.getClass().getName() + "根据物流单号，入库匹配，退货入库单id:{},返回信息:{}", id, vh.toString());
                        }
                    }

                    // 买家昵称
                    // 从ES查退单
                    if (log.isDebugEnabled()) {
                        log.debug("进行根据买家昵称，入库匹配，退货入库单id:{}", id);
                    }
                    List<OcBReturnOrder> ocBReturnOrderByUserNick = new ArrayList<>();
                    if (StringUtils.isNotEmpty(userNick)) {
                        JSONObject whereKeysByUserNick = new JSONObject();
                        whereKeysByUserNick.put("BUYER_NICK", userNick);
                        JSONObject searchByUserNick = ES4ReturnOrder.getReturnOrderSearchByUserinfo(whereKeysByUserNick, null, null, 1000, 0, new String[]{"ID"});
                        JSONArray arrayObjByUserNick = searchByUserNick.getJSONArray("data");
                        for (Object o : arrayObjByUserNick) {
                            JSONObject jsonObject = (JSONObject) o;
                            Long reId = jsonObject.getLong("ID");
                            OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(reId);
                            if (null != ocBReturnOrder) {
                                ocBReturnOrderByUserNick.add(ocBReturnOrder);
                            }
                        }
                    }
//                  QueryWrapper<OcBReturnOrder> userNickWrapper = new QueryWrapper<>();
//                  userNickWrapper.eq("buyer_nick", userNick);
//                  List<OcBReturnOrder> ocBReturnOrderByUserNick = ocBReturnOrderMapper.selectList(userNickWrapper);
                    // 判断查出的退换货订单集合是否为空，,不为空进行匹配
                    if (CollectionUtils.isNotEmpty(ocBReturnOrderByUserNick)) {
                        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrderByUserNick) {
                            // 获得本次循环中退换货订单的退货状态
                            Integer returnStatus = ocBReturnOrder.getReturnStatus();
                            // 获得虚拟入库状态
                            Integer inventedStatus = ocBReturnOrder.getInventedStatus();
                            // 获得是否原退字段值
                            Integer isBack = ocBReturnOrder.getIsBack();
                            // 获得是否生成零售字段值
                            Integer is_todrp = ocBReturnOrder.getIsTodrp();
                            // 获得退货订单明细
                            QueryWrapper<OcBReturnOrderRefund> itemsWrapperUser = new QueryWrapper<>();
                            itemsWrapperUser.eq("oc_b_return_order_id", ocBReturnOrder.getId());
                            List<OcBReturnOrderRefund> items = ocBReturnOrderRefundMapper.selectList(itemsWrapperUser);
                            // 限制条件 是否存在状态为等待退货入库的退换货订单
                            Boolean rs = ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus);
                            Boolean back = ReturnStatusEnum.NOT_BACK.getVal().equals(isBack);
                            Boolean todrp = ReturnStatusEnum.NOT_TODRP.getVal().equals(is_todrp);
                            Boolean invents = VirtualInStatusEnum.NOT.toInt() == inventedStatus;
                            if ((rs && back && todrp) || (invents && todrp)) {
                                // 遍历根据买家昵称查出的退换货订单信息的明细信息 跟入库单明细信息的条码比较
                                for (int i = 0; i < items.size(); i++) {
                                    // 一致说明匹配成功
                                    if (items.get(i).getPsCSkuId().equals(item.getPsCSkuId())) {
                                        // 插入入库匹配日志
                                        String type = "自动匹配退单";
                                        String message = id + "根据买家昵称,匹配退单编号" + ocBReturnOrder.getId() + "成功，匹配条码" + item.getPsCSkuEcode();
                                        returnMatchLogService.insertLogCheck(id, type, message, user);
                                        // 更改入库单明细的【退换货订单号】
                                        Long newOcbReturnOrderId = ocBReturnOrder.getId();
                                        Long itemId = item.getId();
                                        Long ocbRefundInId = item.getOcBRefundInId();
                                        ocBRefundInProductItemMapper.updateOcbReturnOrderId(newOcbReturnOrderId, itemId, ocbRefundInId);
                                        // 回写入库单字段值
                                        Boolean bl = writeBackFields(id, ocBReturnOrder, user);
                                        log.debug("根据买家昵称,入库匹配" + id + "回写入库单字段值" + bl);
                                        //获取新的明细信息
                                        QueryWrapper<OcBRefundInProductItem> newWrapper = new QueryWrapper<>();
                                        newWrapper.eq("id", itemId).eq("oc_b_refund_in_id", ocbRefundInId);
                                        OcBRefundInProductItem eSocBRefundInProductItem = ocBRefundInProductItemMapper.selectOne(newWrapper);
//                                        try {
//                                            Boolean flag = SpecialElasticSearchUtil.indexDocument(OC_B_RETURN_IN_INDEX_NAME, OC_B_RETURN_IN_PRODUCT_ITEM_TYPE_NAME,
//                                                    eSocBRefundInProductItem, itemId, ocbRefundInId);
//                                            if (!flag) {
//                                                throw new NDSException("根据买家昵称匹配，推送ES失败");
//                                            }
//                                        } catch (Exception e) {
//                                            holderV14.setCode(ResultCode.FAIL);
//                                            holderV14.setMessage(id + "根据买家昵称匹配，推送ES失败!");
//                                            vh.setData(holderV14);
//                                            log.debug("根据买家昵称匹配，入库匹配" + id + vh.toString());
//                                        }
                                        productItems.add(item);
                                        setIds.add(newOcbReturnOrderId);
                                        matchSuccess++;
                                        continue a;
                                    }
                                }
                            }
                        }
                    } else {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(id + "根据买家昵称，未能查询到对应的退换货订单信息!");
                        if (log.isDebugEnabled()) {
                            log.debug(this.getClass().getName() + "根据买家昵称，入库匹配，退货入库单id:{},返回信息:{}", id, vh.toString());
                        }
                    }
                    // -------------------------------------------------------------------------------------
                    // 收货人手机号
                    // 从ES查退单
                    if (log.isDebugEnabled()) {
                        log.debug("进行根据收货人手机号，入库匹配，退货入库单id/matchSuccess:{}", id, matchSuccess);
                    }
                    List<OcBReturnOrder> ocBReturnOrderByMobile = new ArrayList<>();
                    if (StringUtils.isNotEmpty(receiverMobile)) {
                        JSONObject whereKeysByMobile = new JSONObject();
                        whereKeysByMobile.put("RECEIVE_MOBILE", receiverMobile);
                        JSONObject searchByMobile = ES4ReturnOrder.getReturnOrderSearchByUserinfo(whereKeysByMobile, null, null, 1000, 0, new String[]{"ID"});
                        JSONArray arrayObjByMobile = searchByMobile.getJSONArray("data");
                        for (Object o : arrayObjByMobile) {
                            JSONObject jsonObject = (JSONObject) o;
                            Long reId = jsonObject.getLong("ID");
                            OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(reId);
                            if (null != ocBReturnOrder) {
                                ocBReturnOrderByMobile.add(ocBReturnOrder);
                            }
                        }
                    }
                    // 判断查出的退换货订单集合是否为空，为空则进行匹配
                    if (CollectionUtils.isNotEmpty(ocBReturnOrderByMobile)) {
                        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrderByMobile) {
                            // 获得本次循环中退换货订单的退货状态
                            Integer returnStatus = ocBReturnOrder.getReturnStatus();
                            // 获得虚拟入库状态
                            Integer inventedStatus = ocBReturnOrder.getInventedStatus();
                            // 获得是否原退字段值
                            Integer isBack = ocBReturnOrder.getIsBack();
                            // 获得是否生成零售字段值
                            Integer is_todrp = ocBReturnOrder.getIsTodrp();
                            // 获得退货订单明细
                            QueryWrapper<OcBReturnOrderRefund> itemsWrapperUser = new QueryWrapper<>();
                            itemsWrapperUser.eq("oc_b_return_order_id", ocBReturnOrder.getId());
                            List<OcBReturnOrderRefund> items = ocBReturnOrderRefundMapper.selectList(itemsWrapperUser);
                            // 限制条件 是否存在状态为等待退货入库的退换货订单
                            Boolean rs = ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus);
                            Boolean back = ReturnStatusEnum.NOT_BACK.getVal().equals(isBack);
                            Boolean todrp = ReturnStatusEnum.NOT_TODRP.getVal().equals(is_todrp);
                            Boolean invents = VirtualInStatusEnum.NOT.toInt() == inventedStatus;
                            if ((rs && back && todrp) || (invents && todrp)) {
                                // 判断入库单明细是否匹配
                                if (IsMatchEnum.UN_MATCH.getVal().equals(item.getIsMatch())) {
                                    // 遍历根据买家昵称查出的退换货订单信息的明细信息 跟入库单明细信息的条码比较
                                    for (int i = 0; i < items.size(); i++) {
                                        // 一致说明匹配成功
                                        if (items.get(i).getPsCSkuId().equals(item.getPsCSkuId())) {
                                            // 插入入库匹配日志
                                            String type = "自动匹配退单";
                                            String message = id + "根据收货人手机号,匹配退单编号" + ocBReturnOrder.getId() + "成功，匹配条码" + item.getPsCSkuEcode();
                                            returnMatchLogService.insertLogCheck(id, type, message, user);
                                            // 更改入库单明细的【退换货订单号】
                                            Long newOcbReturnOrderId = ocBReturnOrder.getId();
                                            Long itemId = item.getId();
                                            Long ocbRefundInId = item.getOcBRefundInId();
                                            ocBRefundInProductItemMapper.updateOcbReturnOrderId(newOcbReturnOrderId, itemId, ocbRefundInId);
                                            // 回写入库单字段值
                                            Boolean bl = writeBackFields(id, ocBReturnOrder, user);
                                            log.debug("根据收货人手机号,入库匹配" + id + "回写入库单字段值" + bl);
                                            //获取新的明细信息
                                            QueryWrapper<OcBRefundInProductItem> newWrapper = new QueryWrapper<>();
                                            newWrapper.eq("id", itemId).eq("oc_b_refund_in_id", ocbRefundInId);
                                            OcBRefundInProductItem eSocBRefundInProductItem = ocBRefundInProductItemMapper.selectOne(newWrapper);
//                                            try {
//                                                Boolean flag = SpecialElasticSearchUtil.indexDocument(OC_B_RETURN_IN_INDEX_NAME, OC_B_RETURN_IN_PRODUCT_ITEM_TYPE_NAME,
//                                                        eSocBRefundInProductItem, itemId, ocbRefundInId);
//                                                if (!flag) {
//                                                    throw new NDSException("根据收货人手机号匹配,推送ES失败");
//                                                }
//                                            } catch (Exception e) {
//                                                holderV14.setCode(ResultCode.FAIL);
//                                                holderV14.setMessage(id + "根据收货人手机号匹配，推送ES失败!");
//                                                vh.setData(holderV14);
//                                                log.debug("根据收货人手机号匹配，入库匹配" + id + vh.toString());
//                                            }
                                            productItems.add(item);
                                            setIds.add(newOcbReturnOrderId);
                                            matchSuccess++;
                                            continue a;
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(id + "根据收货人手机号匹配，未能查询到对应的退换货订单信息!");
                        if (log.isDebugEnabled()) {
                            log.debug(this.getClass().getName() + "根据收货人手机号，入库匹配，退货入库单id:{},返回信息:{}", id, vh.toString());
                        }
                    }

                    // 收货人姓名-------------------------------------------------------------------------------
                    // 从ES查退单
                    if (log.isDebugEnabled()) {
                        log.debug("进行根据收货人姓名，入库匹配，退货入库单id:{}", id);
                    }
                    List<OcBReturnOrder> ocBReturnreceiverNames = new ArrayList<>();
                    if (StringUtils.isNotEmpty(receiverName)) {
                        JSONObject whereKeysByName = new JSONObject();
                        whereKeysByName.put("RECEIVE_NAME", receiverName);
                        JSONObject searchByName = ES4ReturnOrder.getReturnOrderSearchByUserinfo(whereKeysByName, null, null, 1000, 0, new String[]{"ID"});
                        JSONArray arrayObjByName = searchByName.getJSONArray("data");
                        for (Object o : arrayObjByName) {
                            JSONObject jsonObject = (JSONObject) o;
                            Long reId = jsonObject.getLong("ID");
                            OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(reId);
                            if (null != ocBReturnOrder) {
                                ocBReturnreceiverNames.add(ocBReturnOrder);
                            }
                        }
                    }
                    // 判断查出的退换货订单集合是否为空，为空则进行匹配
                    if (CollectionUtils.isNotEmpty(ocBReturnreceiverNames)) {
                        for (OcBReturnOrder ocBReturnOrder : ocBReturnreceiverNames) {
                            // 获得本次循环中退换货订单的退货状态
                            Integer returnStatus = ocBReturnOrder.getReturnStatus();
                            // 获得虚拟入库状态
                            Integer inventedStatus = ocBReturnOrder.getInventedStatus();
                            // 获得是否原退字段值
                            Integer isBack = ocBReturnOrder.getIsBack();
                            // 获得是否生成零售字段值
                            Integer is_todrp = ocBReturnOrder.getIsTodrp();
                            // 获得退货订单明细
                            QueryWrapper<OcBReturnOrderRefund> itemsWrapperUser = new QueryWrapper<>();
                            itemsWrapperUser.eq("oc_b_return_order_id", ocBReturnOrder.getId());
                            List<OcBReturnOrderRefund> items = ocBReturnOrderRefundMapper.selectList(itemsWrapperUser);
                            // 限制条件 是否存在状态为等待退货入库的退换货订单
                            Boolean rs = ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus);
                            Boolean back = ReturnStatusEnum.NOT_BACK.getVal().equals(isBack);
                            Boolean todrp = ReturnStatusEnum.NOT_TODRP.getVal().equals(is_todrp);
                            Boolean invents = VirtualInStatusEnum.NOT.toInt() == inventedStatus;
                            if ((rs && back && todrp) || (invents && todrp)) {
                                // 判断入库单明细是否匹配
                                if (IsMatchEnum.UN_MATCH.getVal().equals(item.getIsMatch())) {
                                    // 遍历根据买家昵称查出的退换货订单信息的明细信息 跟入库单明细信息的条码比较
                                    for (int i = 0; i < items.size(); i++) {
                                        // 一致说明匹配成功
                                        if (items.get(i).getPsCSkuId().equals(item.getPsCSkuId())) {
                                            // 插入入库匹配日志
                                            String type = "自动匹配退单";
                                            String message = id + "根据收货人姓名匹配，匹配退单编号" + ocBReturnOrder.getId() + "成功，匹配条码" + item.getPsCSkuEcode();
                                            returnMatchLogService.insertLogCheck(id, type, message, user);
                                            // 更改入库单明细的【退换货订单号】
                                            Long newOcbReturnOrderId = ocBReturnOrder.getId();
                                            Long itemId = item.getId();
                                            Long ocbRefundInId = item.getOcBRefundInId();
                                            ocBRefundInProductItemMapper.updateOcbReturnOrderId(newOcbReturnOrderId, itemId, ocbRefundInId);
                                            // 回写入库单字段值
                                            Boolean bl = writeBackFields(id, ocBReturnOrder, user);
                                            log.debug("根据收货人姓名匹配，入库匹配" + id + "回写入库单字段值" + bl);
                                            //获取新的明细信息
                                            QueryWrapper<OcBRefundInProductItem> newWrapper = new QueryWrapper<>();
                                            newWrapper.eq("id", itemId).eq("oc_b_refund_in_id", ocbRefundInId);
                                            OcBRefundInProductItem eSocBRefundInProductItem = ocBRefundInProductItemMapper.selectOne(newWrapper);
//                                            try {
//                                                Boolean flag = SpecialElasticSearchUtil.indexDocument(OC_B_RETURN_IN_INDEX_NAME, OC_B_RETURN_IN_PRODUCT_ITEM_TYPE_NAME,
//                                                        eSocBRefundInProductItem, itemId, ocbRefundInId);
//                                                if (!flag) {
//                                                    throw new NDSException("根据收货人姓名匹配，推送ES失败");
//                                                }
//                                            } catch (Exception e) {
//                                                holderV14.setCode(ResultCode.FAIL);
//                                                holderV14.setMessage(id + "根据收货人姓名匹配，推送ES失败!");
//                                                vh.setData(holderV14);
//                                                log.debug("根据收货人姓名，入库匹配" + id + vh.toString());
//                                            }
                                            productItems.add(item);
                                            setIds.add(newOcbReturnOrderId);
                                            matchSuccess++;
                                            continue a;
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage(id + "根据收货人姓名匹配，未能查询到对应的退换货订单信息!");
                        if (log.isDebugEnabled()) {
                            log.debug(this.getClass().getName() + "根据收货人姓名，入库匹配，退货入库单id:{},返回信息:{}", id, vh.toString());
                        }
                    }

                    /**以上根据退单id匹配数据匹配不到后，根据订单ID去匹配订单数据**/
                    // 匹配订单
                   /* RefundInRelation refundInRelation = new RefundInRelation();
                    List<OcBRefundInProductItem> itemRecord = new ArrayList<>();
                    refundInRelation.setRefundIn(ocBRefundIn);
                    refundInRelation.setItems(ocBRefundInProductItems);
                    refundInRelation.setMatchedItems(itemRecord);
                    ocRefundInMatchService.matchOrder(refundInRelation, user);
                    if (itemRecord.size() > 0) {
                        matchSuccess++;
                        for (OcBRefundInProductItem rfnItem : itemRecord) {
                            productItems.add(rfnItem);
                            setIds.add(rfnItem.getOcBReturnOrderId());
                        }
                    }*/
                }
            } else {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage(id + "无原单条码为为0!");
                vh.setData(holderV14);
                log.debug("入库匹配" + id + vh.toString());
            }
        }

        if (matchSuccess > 0) {
            // 生成负向调整单以及更新字段状态 调用退货入库服务
            try {
                //id: 退货入库id, setIds：退单id, productItems：退货入库商品明细
                vh = updateByParam(id, setIds, productItems, ocBRefundIn, user);
            } catch (Exception e) {
                throw new NDSException(e);
            }

            if (vh.isOK()) {
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("生成负向调整单 调用退货入库服务后，入库匹配成功条数：" + matchSuccess);
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("生成负向调整单 调用退货入库服务后，入库匹配失败：" + vh.getMessage());
            }
        } else {
            // @20200717 匹配失败，生成库存调整单
            RefundInRelation refundInRelation = new RefundInRelation();
            refundInRelation.setRefundIn(ocBRefundIn);
            refundInRelation.setItems(ocBRefundInProductItems);
            vh = ocBReturnStockInService.returnWarehousing(refundInRelation, user);
        }
        log.debug("完成改次入库匹配任务，任务id:{},返回信息:{}", id, vh.toString());
        return vh;
    }

    private void insertReturnOrder(Long ocBOrderId) {
        //退单编号不存在，订单编号存在，则对未匹配的退货入库明细根据订单号进行分组生成退换货订单
        List<String> listString = new ArrayList<>();
        try {
            batchAddService.addReturnOrder(ocBOrderId, 0, listString, getRootUser(), "退货入库单匹配生成退换货订单", null, null);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 订单id=" + ocBOrderId + ",退货入库单匹配-订单号进行分组生成退换货订单服务失败！异常:{}", e.getMessage());
        }
    }

    private static User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("admin");
        user.setEname("Pokemon-mapper");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }

    /**
     * 生成负向调整单
     *
     * @param id           入库单id
     * @param ocBRefundIn  入库单实体
     * @param productItems 入库单明细
     * @param user         用户
     * @return 是否成功
     */
    public boolean adjustment(Long id, OcBRefundIn ocBRefundIn, List<OcBRefundInProductItem> productItems, User user) {
        try {
            log.debug(this.getClass().getName() + " 调整单入参,OcBRefundIn:{},OcBRefundInProductItem:{}",
                    ocBRefundIn, productItems);
            SgOmsStoAdjustSaveRequest adjustSaveRequest = new SgOmsStoAdjustSaveRequest();
            adjustSaveRequest.setCpCStoreEcode(ocBRefundIn.getInStoreEcode());
            adjustSaveRequest.setSourceBillId(id); //来源单据id
            adjustSaveRequest.setSourceBillNo(id.toString()); //来源单据id
            adjustSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF); //来源单据类型
            adjustSaveRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL); //单据类型 1正常调整 2差异调整
            adjustSaveRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_FLUSH_NO_SOURCE); // 调整性质：冲无头件
            adjustSaveRequest.setRequestType(SgConstants.REQUEST_TYPE_SAVE_AND_SUBMIT);//请求类型 创建并提交
            adjustSaveRequest.setBillDate(ocBRefundIn.getSubmitDate());
            List<SgOmsStoAdjustItemSaveRequest> items = new ArrayList<>();
            //日志去重map
            Map<String, BigDecimal> logMap = new HashMap<>();

            productItems.forEach(p -> {
                SgOmsStoAdjustItemSaveRequest itemSaveRequest = new SgOmsStoAdjustItemSaveRequest();
                itemSaveRequest.setSourceBillItemId(p.getId());
                itemSaveRequest.setQty(p.getQty().negate()); //入库数量 负数
                itemSaveRequest.setPsCSkuEcode(p.getPsCSkuEcode());
                items.add(itemSaveRequest);
                BigDecimal bigDecimal = logMap.get(p.getPsCSkuEcode()) == null ? BigDecimal.ZERO : logMap.get(p.getPsCSkuEcode());
                BigDecimal qty = p.getQty().negate().add(bigDecimal);
                logMap.put(p.getPsCSkuEcode(), qty);
            });

            if (log.isDebugEnabled()) {
                log.debug(" 退货入库单匹配,生成负向调整单,日志优化去重:" + JSONObject.toJSONString(logMap) +
                        "集合：" + JSONObject.toJSONString(productItems));
            }
            for (String key : logMap.keySet()) {
                String type = "生成调整单(负)";
                BigDecimal logQty = logMap.get(key) == null ? BigDecimal.ZERO : logMap.get(key);
                logQty = logQty.setScale(0, RoundingMode.HALF_DOWN);
                String message = "生成冲无头件调整单成功，调整条码" + key + "(" + logQty + ")";
                returnMatchLogService.insertLogCheck(ocBRefundIn.getId(), type, message, user);
            }
            adjustSaveRequest.setItems(items);
            adjustSaveRequest.setLoginUser(user);
            log.debug(this.getClass().getName() + " 调用接口入参:{}", adjustSaveRequest);
            return sgRpcService.addStockAdjustment(adjustSaveRequest).isOK();
        } catch (Exception e) {
            log.error(this.getClass().getName() + "  库存调整单(实体仓)新增失败!", e);
        }
        return false;
    }

    /**
     * 生成负向调整单
     * 更新入库单明细条码是否无原单条码字段值为否
     * 调用退货入库
     *
     * @param productItems
     * @return
     */
    public ValueHolderV14 updateByParam(Long id, Set<Long> setIds, List<OcBRefundInProductItem> productItems, OcBRefundIn ocBRefundIn, User user) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        //  调用退货入库单入库服务
        QueryWrapper<OcBRefundInProductItem> wrapper1 = new QueryWrapper<>();
        wrapper1.eq("oc_b_refund_in_id", id);
        List<OcBRefundInProductItem> NewocBRefundInProductItems = ocBRefundInProductItemMapper.selectList(wrapper1);
        //将发出条码赋值为实收条码
        deassignPsCSku(NewocBRefundInProductItems);
        try {
            log.debug("入库匹配" + id + "ReturnReceiptMatchService.调用西文退货入库");
            RefundInRelation refundInRelation = new RefundInRelation();
            refundInRelation.setRefundIn(ocBRefundIn);
            refundInRelation.setItems(NewocBRefundInProductItems);
            holderV14 = ocBReturnStockInService.returnWarehousing(refundInRelation, user);
            log.debug("入库匹配" + id + "ReturnReceiptMatchService.调用西文退货入库" + holderV14.getMessage());
        } catch (Exception e) {
            // 插入入库匹配日志
            String type = "入库匹配服务";
            String message = id + "调用退货入库服务异常" + e.getMessage();
            returnMatchLogService.insertLogCheck(id, type, message, user);
            log.error("入库匹配" + id + "调用西文退货入库服务异常！" + e.getMessage());
            throw new NDSException("调用西文退货入库服务异常！" + e.getMessage());
        }
        if (holderV14.isOK()) {
            try {
                // 生成负向调整单
                boolean bl = false;
                log.debug("入库匹配" + setIds + "---" + productItems.toString());
                for (Long setId : setIds) {
                    List<OcBRefundInProductItem> productItemsForAdjust = new ArrayList<>();
                    for (OcBRefundInProductItem productItem : productItems) {
                        if (productItem.getOcBReturnOrderId().equals(setId)
                                && productItem.getIsGenAdjust().equals(IsGenAdjustEnum.YES.integer())) {
                            productItemsForAdjust.add(productItem);
                        }
                    }
                    if (productItemsForAdjust.size() > 0) {
                        log.debug("入库匹配.遍历调用生成负向调整单");
                        bl = adjustment(setId, ocBRefundIn, productItemsForAdjust, user);
                        //将关联的退换货订单'是否无名件'字段更新为是
                        QueryWrapper<OcBReturnOrder> wrapper = new QueryWrapper<>();
                        wrapper.eq("oc_b_refund_in_id", ocBRefundIn.getId());
                        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectOne(wrapper);
                        if (ocBReturnOrder != null) {
                            ocBReturnOrder.setIsAnonymous(OcBOrderConst.REFUND_IN_IS_ANONYMOUSO_YES);
                            ocBReturnOrderMapper.updateById(ocBReturnOrder);
                        } else {
                            log.debug("生成负向调整单，匹配不到退款单");
                        }
                        if (log.isDebugEnabled()) {
                            log.debug("入库匹配.遍历调用生成负向调整单完成，更新关联退换货单无名件状态为是");
                        }
                    }
                }
                log.debug("入库匹配" + id + "ReturnReceiptMatchService.生成负向调整单返回值：" + bl);
                if (bl) {
                    int num = 0;
                    for (OcBRefundInProductItem productItem : productItems) {
                        // 更新入库单明细条码是否无原单条码字段值为否和生成冲无头件调整单更新为是1
                        num = ocBRefundInProductItemMapper.updateIsWithoutOrig(0, productItem.getId(), id);
                        log.debug("入库匹配" + id + "更新入库单明细条码为否end" + num);
                    }
                    if (num > 0) {
                        holderV14.setCode(ResultCode.SUCCESS);
                        holderV14.setMessage(id + "入库匹配成功！");
                        return holderV14;
                    } else {
                        holderV14.setCode(ResultCode.FAIL);
                        holderV14.setMessage(id + "入库匹配失败！");
                        return holderV14;
                    }
                } else {
                    // 插入入库匹配日志
                    String type = "生成调整单";
                    String message = id + "入库匹配失败：调整单生成失败！";
                    returnMatchLogService.insertLogCheck(id, type, message, user);
                    holderV14.setCode(ResultCode.FAIL);
                    holderV14.setMessage("入库匹配失败：调整单生成失败！");
                    log.debug("入库匹配" + id + holderV14.toString());
                    throw new NDSException(id + "入库匹配失败：调整单生成失败！");
                }
            } catch (Exception e) {
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage(id + "生成负向调整单异常!" + e.getMessage());
                log.debug("入库匹配:生成负向调整单异常!" + id + e.toString());
                throw new NDSException(id + "生成负向调整单异常!" + e.getMessage());
            }
        } else {
            // 插入入库匹配日志
            String type = "入库匹配失败";
            String message = id + "入库匹配失败，原因为：" + holderV14.getMessage();
            returnMatchLogService.insertLogCheck(id, type, message, user);
            throw new NDSException(id + "入库匹配失败！");
        }

    }

    /**
     * 将发出条码赋值为实收条码
     *
     * @param newocBRefundInProductItems
     */
    private void deassignPsCSku(List<OcBRefundInProductItem> newocBRefundInProductItems) {
        for (OcBRefundInProductItem newocBRefundInProductItem : newocBRefundInProductItems) {
            String realSkuEcode = newocBRefundInProductItem.getRealSkuEcode();
            if (StringUtils.isNotEmpty(realSkuEcode)) {
                //将发出条码赋值为实收条码
                newocBRefundInProductItem.setPsCSkuId(newocBRefundInProductItem.getRealSkuId());
                newocBRefundInProductItem.setPsCSkuEcode(newocBRefundInProductItem.getRealSkuEcode());
            }
        }
    }

    /**
     * 入库单字段回写
     *
     * @param id             入库单id
     * @param ocBReturnOrder 匹配到的退换货订单
     * @return
     */
    private boolean writeBackFields(Long id, OcBReturnOrder ocBReturnOrder, User user) {
        // 回写入库单
        OcBRefundIn ocBRefundIn = new OcBRefundIn();
        ocBRefundIn.setId(id);
        // 如果入库单相应字段为空，则回写，不为空，则不回写，且如果退换货单字段为空 也不回写
        // 原单单号
        if (ocBRefundIn.getOrigOrderNo() == null && ocBReturnOrder.getOrigOrderId() != null) {
            ocBRefundIn.setOrigOrderNo(ocBReturnOrder.getOrigOrderId());
        }
        // 原平台单号
        if (ocBRefundIn.getSourceCode() == null && ocBReturnOrder.getOrigSourceCode() != null) {
            ocBRefundIn.setSourceCode(ocBReturnOrder.getOrigSourceCode());
        }
        // 买家昵称
        if (ocBRefundIn.getUserNick() == null && ocBReturnOrder.getBuyerNick() != null) {
            ocBRefundIn.setUserNick(ocBReturnOrder.getBuyerNick());
        }
        // 发件地址
        if (ocBRefundIn.getReceiverAddress() == null && ocBReturnOrder.getReceiveAddress() != null) {
            ocBRefundIn.setReceiverAddress(ocBReturnOrder.getReceiveAddress());
        }
        // 收件人
        if (ocBRefundIn.getOrigOrderNo() == null && ocBReturnOrder.getOrigOrderId() != null) {
            ocBRefundIn.setReceiverName(ocBReturnOrder.getReceiveName());
        }
        // 物流公司
        if (ocBRefundIn.getCpCLogisticsId() == null && ocBReturnOrder.getCpCLogisticsId() != null) {
            ocBRefundIn.setCpCLogisticsId(ocBReturnOrder.getCpCLogisticsId());
        }
        if (ocBRefundIn.getCpCLogisticsEcode() == null && ocBReturnOrder.getCpCLogisticsEcode() != null) {
            ocBRefundIn.setCpCLogisticsEcode(ocBReturnOrder.getCpCLogisticsEcode());
        }
        if (ocBRefundIn.getCpCLogisticsEname() == null && ocBReturnOrder.getCpCLogisticsEname() != null) {
            ocBRefundIn.setCpCLogisticsEname(ocBReturnOrder.getCpCLogisticsEname());
        }
        ocBRefundIn.setMatcher(user.getEname());
//        SimpleDateFormat change = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String format = change.format(new Date());
        ocBRefundIn.setMatchedTime(new Date());
        int num = ocBRefundInMapper.updateById(ocBRefundIn);

        if (num > 0) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 判断入库数量是否大于等于退换货订单申请数量
     *
     * @param ocBRefundInProductItems
     * @param refundList
     * @return
     */
    private Boolean checkNumber(List<OcBRefundInProductItem> ocBRefundInProductItems, List<OcBReturnOrderRefund> refundList) {
        BigDecimal num = new BigDecimal("0");
        for (OcBReturnOrderRefund orderRefund : refundList) {
            num = num.add(orderRefund.getQtyRefund());
        }
        if (num.intValue() > ocBRefundInProductItems.size()) {
            return false;
        }
        return true;
    }

    /**
     * 判断申请数量是否大于匹配数量
     *
     * @param orderRefund
     * @return
     */
    private Boolean checkMatchNum(OcBReturnOrderRefund orderRefund) {
        //匹配数量
        Long matchNum = orderRefund.getQtyMatch();
        Long applyNum = orderRefund.getQtyRefund().longValue();
        if (applyNum - matchNum > 0) {
            return true;
        }
        return false;
    }
}

