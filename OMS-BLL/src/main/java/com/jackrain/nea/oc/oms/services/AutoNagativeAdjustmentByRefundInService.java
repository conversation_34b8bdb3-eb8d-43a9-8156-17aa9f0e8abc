package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoAdjustSaveRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4RefundIn;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.model.enums.IsGenMinusAdjustEnum;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR> 田钦华
 * @since : 2019-09-25
 * create at : 2019-09-25 9:54 PM
 * 退货入库单 生成负向库存调整单 补偿任务处理类
 */
@Slf4j
@Component
public class AutoNagativeAdjustmentByRefundInService {

    @Autowired
    OcBRefundInMapper refundInMapper;

    @Autowired
    OcBRefundInProductItemMapper itemMapper;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    ScanIncomingLogicService scanIncomingUpdateService;

    public boolean doNagativeAdjustmentByRefundIn(Integer range) throws ParseException {
        range = range == null ? 200 : range;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat startSdf = new SimpleDateFormat("yyyy-MM-dd");
        Date d = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        calendar.add(Calendar.DATE, -1);
        String beginDate = startSdf.format(calendar.getTime());//昨天
        Long startTime = sdf.parse(beginDate + " 22:00:00").getTime();
        Long endTime = System.currentTimeMillis();
        Integer startIndex = 1;
        JSONObject search = ES4RefundIn.findJSONObjectByRangeStartEndTime(range, (startIndex - 1) * range, startTime, endTime);
        List<Long> ids = new ArrayList<>();//退货入库主键ids
        Integer total = search.getInteger("total");
        if (total == null || total <= 0) {
            return false;
        }
        Integer totalPage = 0;
        //计算循环的次数
        if (total % range == 0) {
            //整除，页数=总数/每页的条数
            totalPage = total / range;
        } else {
            //不整除，页数=总数/每页的条数+1
            totalPage = total / range + 1;
        }
        for (int i = 0; i < totalPage; i++) {
            JSONObject searchTmp = ES4RefundIn.findJSONObjectByRangeStartEndTime(range, i * range, startTime, endTime);
            JSONArray data = searchTmp.getJSONArray("data");
            for (int j = 0; j < data.size(); j++) {
                String id = data.getJSONObject(j).getString("ID");
                ids.add(Long.valueOf(id));
            }
            if (ids.size() > 0) {
                makeDatasByEs(ids);
            }
        }
        return false;
    }

    /*
     * 依据es筛选数据
     */
    private boolean makeDatasByEs(List<Long> ids) {
        List<OcBRefundIn> ocBRefundIns = refundInMapper.selectBatchIds(ids);
        //筛选数据
        QueryWrapper<OcBRefundInProductItem> queryWrapper;
        for (OcBRefundIn bean : ocBRefundIns) {
            queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("oc_b_refund_in_id", bean.getId());
            queryWrapper.eq("IS_GEN_ADJUST", 1);
            queryWrapper.eq("IS_GEN_MINUS_ADJUST", 0);
            List<OcBRefundInProductItem> items = itemMapper.selectList(queryWrapper);
            List<OcBRefundInProductItem> itemsTmp = new ArrayList<>();//筛选数据：存在退换货订单编号的明细
            for (OcBRefundInProductItem item : items) {
                if (item.getOcBReturnOrderId() != null) {
                    itemsTmp.add(item);
                }
            }
            //退换货新增库存调整单
            List<List<OcBRefundInProductItem>> itemsTmpList = getItemsGroupByItem(itemsTmp);//根据推单编号分组
            if (itemsTmpList != null && itemsTmpList.size() > 0) {
                for (int i = 0; i < itemsTmpList.size(); i++) {
                    List<OcBRefundInProductItem> itemsTmps = itemsTmpList.get(i);
                    long startTime = System.currentTimeMillis();
                    if (addStockAdjustmenByRealSku(bean, itemsTmps, SystemUserResource.getRootUser())) {
                        //更新退货入库单明细是否生成调整单字段值为是
                        for (OcBRefundInProductItem item : itemsTmps) {
                            try {
                                OcBRefundInProductItem newProductItem = new OcBRefundInProductItem();
                                newProductItem.setId(item.getId());
                                newProductItem.setIsGenMinusAdjust(IsGenMinusAdjustEnum.YES.integer());
                                itemMapper.updateById(newProductItem);
                            } catch (Exception ex) {
                                log.error(LogUtil.format("补偿任务更新退货入库单明细是否生成调整单字段值为是.异常: {}"), Throwables.getStackTraceAsString(ex));
                                throw new NDSException("更新退货入库单明细异常！");
                            }
                        }
                    }
                    long endTime = System.currentTimeMillis();
                }
            }
        }
        return false;
    }

    /**
     * 依据退单编号分组
     */
    private List<List<OcBRefundInProductItem>> getItemsGroupByItem(List<OcBRefundInProductItem> itemsTmp) {
        if (itemsTmp == null || itemsTmp.size() <= 0) {
            return new ArrayList<>();
        }
        //根据退换货单号进行分组
        HashSet set = new HashSet();
        for (int i = 0; i < itemsTmp.size(); i++) {
            OcBRefundInProductItem productItem = itemsTmp.get(i);
            Long ocBReturnOrderId = productItem.getOcBReturnOrderId();
            set.add(ocBReturnOrderId);
        }
        Iterator iterator = set.iterator();
        List<List<OcBRefundInProductItem>> itemList = new ArrayList<>();
        while (iterator.hasNext()) {
            Object next = iterator.next();
            List productItem = new ArrayList();
            for (int i = 0; i < itemsTmp.size(); i++) {
                if (next.equals(itemsTmp.get(i).getOcBReturnOrderId())) {
                    productItem.add(itemsTmp.get(i));
                }
            }
            itemList.add(productItem);
        }
        return itemList;
    }

    /**
     * 退换货新增库存调整单
     *
     * @param ocBRefundIn  退换货主表信息
     * @param productItems 退货明细
     * @param user         操作人信息
     * @return
     */
    public boolean addStockAdjustmenByRealSku(OcBRefundIn ocBRefundIn,
                                              List<OcBRefundInProductItem> productItems, User user) {
        try {
            log.debug(LogUtil.format("补偿任务调整单入参,OcBRefundIn:{},OcBRefundInProductItem:{}", ocBRefundIn.getId()),  ocBRefundIn, productItems);
            SgOmsStoAdjustSaveRequest adjustSaveRequest = new SgOmsStoAdjustSaveRequest();
            adjustSaveRequest.setSourceBillId(productItems.get(0).getOcBReturnOrderId()); //来源单据id
            adjustSaveRequest.setSourceBillNo(productItems.get(0).getOcBReturnOrderId().toString()); //来源单据编号
            adjustSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF); //来源单据类型  零售退货单
            adjustSaveRequest.setBillType(SgConstantsIF.SG_STO_ADJUST_BILL_TYPE_NORMAL); //单据类型 1正常调整 2差异调整
            adjustSaveRequest.setRequestType(SgConstants.REQUEST_TYPE_SAVE_AND_SUBMIT);//请求类型 创建并提交
            adjustSaveRequest.setCpCStoreEcode(ocBRefundIn.getInStoreEcode());//实体仓编码
            adjustSaveRequest.setBillDate(ocBRefundIn.getSubmitDate());
            //调整性质
            adjustSaveRequest.setSgBAdjustPropId((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_FLUSH_NO_SOURCE);//冲无头件
            List<SgOmsStoAdjustItemSaveRequest> items = new ArrayList<>();
            productItems.forEach(p -> {
                SgOmsStoAdjustItemSaveRequest itemSaveRequest = new SgOmsStoAdjustItemSaveRequest();
                itemSaveRequest.setSourceBillItemId(p.getId());
                itemSaveRequest.setQty(p.getQty().negate()); //入库数量,负向
                itemSaveRequest.setPsCSkuEcode(p.getPsCSkuEcode());
            });
            adjustSaveRequest.setItems(items);
            adjustSaveRequest.setLoginUser(user);
            log.debug(LogUtil.format("补偿任务库存调整单调用接口入参:{}", ocBRefundIn.getId()), adjustSaveRequest);
            return sgRpcService.addStockAdjustment(adjustSaveRequest).isOK();
        } catch (Exception e) {
            log.error(LogUtil.format("补偿任务库存调整单(实体仓)新增失败: {}"), Throwables.getStackTraceAsString(e));
        }
        return false;
    }

}
