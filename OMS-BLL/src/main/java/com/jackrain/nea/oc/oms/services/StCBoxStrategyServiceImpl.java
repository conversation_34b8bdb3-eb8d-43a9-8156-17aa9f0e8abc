package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.oc.oms.mapper.StCBoxStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.StCBoxStrategyEntity;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Stream;

@Slf4j
@Service
public class StCBoxStrategyServiceImpl extends ServiceImpl<StCBoxStrategyMapper, StCBoxStrategyEntity> implements StCBoxStrategyService {

    private static final String ORDER_GOODS_SPLIT_SYMBOL = ","; // 订单分隔符
    private static final String SPLIT_ORDER_RULES_SYMBOL1 = "/"; // 拆单规则分隔符
    private static final String SPLIT_ORDER_RULES_SYMBOL2 = "\\|"; // 拆单规则分隔符
    private static final String EQUALS_SIGN = "=";

    @Autowired
    private StCBoxStrategyMapper stCBoxStrategyMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ValueHolder updateBoxStrategy(User user, StCBoxStrategyEntity strategyEntity) {
        String splitOrderRules = strategyEntity.getSplitOrderRules();
        try {
            if (StringUtils.isNotBlank(splitOrderRules)) {
                StCBoxStrategyEntity stCBoxStrategyEntity = getById(strategyEntity.getId());
                if (Objects.isNull(stCBoxStrategyEntity)) {
                    return ValueHolderUtils.fail("编辑的记录不存在");
                }
                if (!checkSplitOrderRules(stCBoxStrategyEntity.getOrderGoods(), splitOrderRules)) {
                    return ValueHolderUtils.fail("请检查规则SKU和数量");
                }
            }
        }
        catch (Exception e) {
            log.error("箱型策略新增校验异常={}", Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.fail("请检查【拆单规则】格式");
        }

        strategyEntity.setModifieddate(new Date());
        strategyEntity.setModifierid(user.getId().longValue());
        strategyEntity.setModifierename(user.getEname());
        strategyEntity.setModifiername(user.getName());

        updateById(strategyEntity);

        if ("-1".equals(strategyEntity.getBoxName())) {
            boolean update = update(Wrappers.lambdaUpdate(new StCBoxStrategyEntity())
                    .set(StCBoxStrategyEntity::getBoxName, null)
                    .eq(StCBoxStrategyEntity::getId, strategyEntity.getId()));
            if (update){
                log.info("箱型策略更新空白名称成功");
            }
        }

        ValueHolder valueHolder = ValueHolderUtils.success("编辑成功");
        valueHolder.put("data", ValueHolderUtils.createAddErrorData("ST_C_BOX_STRATEGY", strategyEntity.getId(), null));
        return valueHolder;
    }

    @Override
    public ValueHolder addBoxStrategy(User user, StCBoxStrategyEntity strategyEntity) {

        String orderGoods = strategyEntity.getOrderGoods();
        String splitOrderRules = strategyEntity.getSplitOrderRules();

        if (StringUtils.isBlank(orderGoods) || StringUtils.isBlank(splitOrderRules)) {
            return ValueHolderUtils.fail("订单商品、拆单规则必填");
        }
        orderGoods = orderGoods.trim();
        splitOrderRules = splitOrderRules.trim();
        String orderGoodsSort;
        try {
            orderGoodsSort = orderGoodsSort(orderGoods);
            if (!checkOrderGoods(orderGoodsSort)) {
                return ValueHolderUtils.fail("订单商品重复");
            }

            if (!checkSplitOrderRules(orderGoodsSort, splitOrderRules)) {
                return ValueHolderUtils.fail("请检查规则SKU和数量");
            }
        }
        catch (Exception e) {
            log.error("箱型策略新增校验异常={}", Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.fail("请检查【订单商品、拆单规则】格式");
        }

        try {
            strategyEntity.setStrategyCode(getStrategyNo(strategyEntity, user.getLocale()));
            strategyEntity.setOrderGoods(orderGoodsSort);
            strategyEntity.setCreationdate(new Date());
            strategyEntity.setOwnerid(user.getId().longValue());
            strategyEntity.setOwnerename(user.getEname());
            strategyEntity.setOwnername(user.getName());
            strategyEntity.setModifieddate(new Date());
            strategyEntity.setModifierid(user.getId().longValue());
            strategyEntity.setModifierename(user.getEname());
            strategyEntity.setModifiername(user.getName());
            strategyEntity.setIsactive("N");
            save(strategyEntity);
        }
        catch (Exception e) {
            log.error("箱型策略保存异常={}", Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.fail("箱型策略保存异常");
        }

        ValueHolder valueHolder = ValueHolderUtils.success("新增成功");
        Integer maxId = stCBoxStrategyMapper.getMaxId();
        valueHolder.put("data", ValueHolderUtils.createAddErrorData("ST_C_BOX_STRATEGY", maxId.longValue(), null));
        return valueHolder;
    }

    @Override
    public String orderGoodsSort(String orderGoods) {
        Map<String, String> treeMap = new TreeMap<>();
        String[] var1 = StringUtils.split(orderGoods, ORDER_GOODS_SPLIT_SYMBOL);
        for (String s : var1) {
            String[] var2 = s.split(EQUALS_SIGN);
            if (treeMap.containsKey(var2[0])) {
                throw new NDSException("订单商品存在相同的SKU等式");
            }
            treeMap.put(var2[0], s);
        }

        return String.join(ORDER_GOODS_SPLIT_SYMBOL, treeMap.values());
    }

    @Override
    public Boolean checkSplitOrderRules(String orderGoods, String splitOrderRules) {
        Map<String, Integer> orderGoodsMap = new HashMap<>();
        String[] var1 = StringUtils.split(orderGoods, ORDER_GOODS_SPLIT_SYMBOL);
        for (String s : var1) {
            String[] var2 = s.split(EQUALS_SIGN);
            orderGoodsMap.put(var2[0], Integer.valueOf(var2[1]));
        }

        Map<String, Integer> splitOrderRulesMap = new HashMap<>();
        Stream.of(StringUtils.split(splitOrderRules, SPLIT_ORDER_RULES_SYMBOL1))
                .map(s -> s.split(SPLIT_ORDER_RULES_SYMBOL2))
                .flatMap(Arrays::stream)
                .forEach(s -> {
                    String[] var2 = s.split(EQUALS_SIGN);
                    splitOrderRulesMap.compute(var2[0], (k, v) -> {
                        if (Objects.isNull(v)) {
                            return Integer.valueOf(var2[1]);
                        }
                        return v + Integer.parseInt(var2[1]);
                    });
                });
        for (String key : splitOrderRulesMap.keySet()) {
            if (!orderGoodsMap.containsKey(key)) {
                return Boolean.FALSE;
            }
            if (!splitOrderRulesMap.get(key).equals(orderGoodsMap.get(key))) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean checkOrderGoods(String orderGoods) {
        StCBoxStrategyEntity one = getOne(Wrappers.lambdaQuery(new StCBoxStrategyEntity())
                .eq(StCBoxStrategyEntity::getOrderGoods, orderGoods));
        return Objects.isNull(one);
    }

    @Override
    public String getStrategyNo(StCBoxStrategyEntity strategyEntity, Locale locale) {
        JSONObject obj = new JSONObject();
        obj.put("ST_C_BOX_STRATEGY", strategyEntity);
        return SequenceGenUtil.generateSquence("ST_C_BOX_STRATEGY", obj, locale, false);
    }

    @Override
    public Boolean updateActive(User user, List<Long> objIds, String isActive) {
        Collection<StCBoxStrategyEntity> stCBoxStrategyEntities = listByIds(objIds);
        if (CollectionUtils.isEmpty(stCBoxStrategyEntities)) {
            return Boolean.FALSE;
        }
        stCBoxStrategyEntities.forEach(o -> {
            o.setModifieddate(new Date());
            o.setModifierid(user.getId().longValue());
            o.setModifierename(user.getEname());
            o.setModifiername(user.getName());
            o.setIsactive(isActive);
        });
        return updateBatchById(stCBoxStrategyEntities);
    }
}

