package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.CpCShopChannelType;
import com.jackrain.nea.oc.oms.model.enums.OrderWorehouseIsRetry;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.rpc.AcScRpcService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * 分销商店铺的订单由品牌商进行发货时，根据分销商与品牌商之间约定好商品的分销结算价，确定订单分销结算金额，对分销商资金进行占用，
 * 扣减等操作，实现品牌商对分销商的资金风险管控以及结算管理。
 * <p>
 * 包含功能  1）	订单审核时，调用【线上代销资金占用变动服务】，占用资金；
 * 2）	订单反审核时，调用【线上代销资金占用变动服务】，释放占用资金；
 * 3）	订单出库(仓库发货)时，调用【新增代销核算单】，新增代销核算单，结算中心对资金进行释放和扣减；
 * 4）	退换货单售后审核时，调用【新增代销退货核算单】，新增代销退货核算单，结算中心对资金进行更新；
 *
 * @date 2019/8/23
 * @author: ming.fz
 */
@Component
@Slf4j
public class OrderStrategyPriceComputeService {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    SgRpcService sgRpcService;

    @Autowired
    AcScRpcService acScRpcService;

    @Autowired
    OmsOrderLogService omsOrderLogService;

    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    PropertiesConf propertiesConf;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    /**
     * 线上代销资金占用变动服务
     *
     * @param orderInfos          订单和全部订单明细
     * @param acScConstantsIFType //零售发货单审核
     *                            Integer BIll_VARIETY_NODE_AUDIT = 1; 静态变量 AcScConstantsIF.BIll_VARIETY_NODE_AUDIT
     *                            //零售发货单反审核
     *                            Integer BIll_VARIETY_NODE_NO_AUDIT = 2;静态变量 AcScConstantsIF.BIll_VARIETY_NODE_NO_AUDIT
     *                            //零售发货单仓库出库
     *                            Integer BIll_VARIETY_NODE_OUT = 3; 静态变量 AcScConstantsIF.BIll_VARIETY_NODE_OUT
     *                            //代销核算单审核
     *                            Integer BILL_VARIETY_NODE_SALES_AUDIT = 4; 静态变量 AcScConstantsIF.BILL_VARIETY_NODE_SALES_AUDIT
     * @param user                用户
     * @return ValueHolderV14  code 0成功 -1失败 message失败信息
     */
    /*public ValueHolderV14 onlineFundOccupy(List<OcBOrderParam> orderInfos, Integer acScConstantsIFType, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        Config config = ConfigService.getConfig("common");
        Integer isOpen = config.getIntProperty("order.strategy.price.isOpen", 0);
        if(isOpen == 0){
            try {
                ValueHolderV14 servers = this.servers(orderInfos, acScConstantsIFType, user);
                if(log.isDebugEnabled()){
                    log.debug("资金占用出参" + JSONObject.toJSONString(servers));
                }
                servers.setCode(ResultCode.SUCCESS);
                return servers;
                // return this.servers(orderInfos, acScConstantsIFType, user);
            } catch (NDSException e) {
             *//*   e.printStackTrace();
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage(e.getMessage()));
                log.error("ming.fz线上代销资金占用变动服务调用失败!" + e.toString());
                return vh;*//*
                e.printStackTrace();
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage(Resources.getMessage(e.getMessage()));
                log.error("ming.fz线上代销资金占用变动服务调用失败!" + e.toString());
                return vh;
            } catch (Exception e) {
                *//*e.printStackTrace();
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("线上代销资金占用变动服务调用失败！");
                if (log.isDebugEnabled()) {
                    log.error("线上代销资金占用变动服务调用失败！" + e.toString());
                }
                return vh;
                *//*
                e.printStackTrace();
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("线上代销资金占用变动服务调用失败！");
                if (log.isDebugEnabled()) {
                    log.error("线上代销资金占用变动服务调用失败！" + e.toString());
                }
                return vh;
            }

        }else {
            vh.setCode(ResultCode.SUCCESS);
            return vh;
        }
    }*/
    public ValueHolderV14 onlineFundOccupy(List<OcBOrderParam> orderInfos, Integer acScConstantsIFType, User user) {
        long startTime = System.currentTimeMillis();
        ValueHolderV14 valueHolderV14 = this.onlineFundOccupyStart(orderInfos, acScConstantsIFType, user);
        long endTime = System.currentTimeMillis();
        return valueHolderV14;
    }

    public ValueHolderV14 onlineFundOccupyStart(List<OcBOrderParam> orderInfos, Integer acScConstantsIFType, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        boolean isOccupyOnlineFundEnabled = this.omsSystemConfig.isAuditOccupyOnlineFundEnabled();
        if (isOccupyOnlineFundEnabled) {
            try {
                return this.servers(orderInfos, acScConstantsIFType, user);
            } catch (NDSException e) {
                e.printStackTrace();
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage(e.getMessage()));
                log.error(LogUtil.format("fz线上代销资金占用变动服务调用失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                return vh;
            } catch (Exception e) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("线上代销资金占用变动服务调用失败！");
                log.error(LogUtil.format("线上代销资金占用变动服务调用失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));

                return vh;
            }

        } else {
            vh.setCode(ResultCode.SUCCESS);
            return vh;
        }
    }


    public ValueHolderV14 servers(List<OcBOrderParam> orderInfos, Integer acScConstantsIFType, User user) {

        ValueHolderV14 resultvh = new ValueHolderV14();

        if (orderInfos == null || orderInfos.size() == 0) {
            resultvh.setCode(ResultCode.FAIL);
            resultvh.setMessage("入参错误没有订单！");
            return resultvh;
        }

        if (orderInfos.size() == 1) {
            //单条操作
            OcBOrderParam ocBOrderParam = orderInfos.get(0);
            //判断当前店铺是否是分销 非分销店铺直接结束
            if (isShopChannelType(ocBOrderParam.getOcBOrder().getCpCShopId())) {
                return callOnlineFundOccupy(acScConstantsIFType, user, orderInfos, true);
            } else {
                //非分销店铺直接结束
                resultvh.setCode(ResultCode.SUCCESS);
                resultvh.setMessage("店铺渠道类型非分销！");
                return resultvh;
            }


        } else {
            //批量操作
            for (int i = orderInfos.size() - 1; i >= 0; i--) {
                OcBOrder ocBOrder = orderInfos.get(i).getOcBOrder();
                //判断当前店铺是否是分销 非分销店铺直接结束
                if (isShopChannelType(ocBOrder.getCpCShopId())) {
                    continue;
                } else {
                    //非分销店铺 移除单据
                    //直接标记为已处理（reserve_bigint07 = 0）
                    //updateOrder(user, ocBOrder, OrderWorehouseIsRetry.YES);
                    orderInfos.remove(i);
                    continue;
                }
            }
            if (orderInfos.size() > 0) {
                return callOnlineFundOccupy(acScConstantsIFType, user, orderInfos, false);
            }
        }

        resultvh.setCode(0);
        return resultvh;
    }

    /**
     * @param acScConstantsIFType
     * @param user
     * @param orderInfos
     * @param bl                  true标识是单条操作 false反之
     * @return
     */
    private ValueHolderV14 callOnlineFundOccupy(Integer acScConstantsIFType, User user, List<OcBOrderParam> orderInfos, boolean bl) {

        ValueHolderV14 vh = new ValueHolderV14();

        List<OcBOrder> ocBOrders = new ArrayList<>();
        for (OcBOrderParam orderInfo : orderInfos) {
            ocBOrders.add(orderInfo.getOcBOrder());
        }
//
//        ValueHolderV14<SgSendBillQueryResult> sgSendBillQueryResultVh =
//                sgRpcService.querySgBSend(ocBOrders, SgConstantsIF.BILL_TYPE_RETAIL);
//        if (sgSendBillQueryResultVh == null || sgSendBillQueryResultVh.getCode() == ResultCode.FAIL) {
//            if (log.isDebugEnabled()) {
//                log.debug("获取逻辑发货明细失败:" + JSONObject.toJSONString(sgSendBillQueryResultVh));
//            }
//            vh.setCode(ResultCode.FAIL);
//            vh.setMessage("获取逻辑发货明细失败!");
//            return vh;
//        }
//
//        SgSendBillQueryResult sgSendBillQueryResultVhData = sgSendBillQueryResultVh.getData();
//        HashMap<String, HashMap<SgBSend, List<SgBSendItem>>> results = sgSendBillQueryResultVhData.getResults();

//        ValueHolderV14<OnlineFundOccupyResult> onlineFundOccupyResultValueHolderV14 =
//                acScRpcService.onlineFundOccupy(orderInfos, user, results, acScConstantsIFType);
//
//        OnlineFundOccupyResult dataOnlineFundOccupyResult = onlineFundOccupyResultValueHolderV14.getData();
//        if (dataOnlineFundOccupyResult == null) {
//            //结束
//            vh.setCode(ResultCode.FAIL);
//            vh.setMessage("经销商资金占用失败且获取结算价失败，订单审核失败!");
//            return vh;
//        }
//
//        if (acScConstantsIFType.equals(AcScConstantsIF.BIll_VARIETY_NODE_NO_AUDIT)) {
//            //反审核才进 不用走后面流程只关心成功失败
//            return theauditServer(user, orderInfos, bl, vh, onlineFundOccupyResultValueHolderV14, dataOnlineFundOccupyResult);
//        } else if (acScConstantsIFType.equals(AcScConstantsIF.BIll_VARIETY_NODE_AUDIT)) {
//            //审核
//            return checkServer(user, orderInfos, bl, vh, onlineFundOccupyResultValueHolderV14, dataOnlineFundOccupyResult);
//        } else if (acScConstantsIFType.equals(AcScConstantsIF.BIll_VARIETY_NODE_OUT)) {
//            //仓库发货
//            return worehouseSendServer(user, orderInfos, bl, vh, onlineFundOccupyResultValueHolderV14, dataOnlineFundOccupyResult);
//        } else {
//            vh.setCode(ResultCode.FAIL);
//            vh.setMessage("acScConstantsIFType 变化节点错误！");
//            return vh;
//        }
        return vh;
    }

    /**
     * 仓库发货
     *
     * @param user
     * @param orderInfos
     * @param bl
     * @param vh
     * @param onlineFundOccupyResultValueHolderV14
     * @param dataOnlineFundOccupyResult
     * @return
     */
//    private ValueHolderV14 worehouseSendServer(User user, List<OcBOrderParam> orderInfos, boolean bl, ValueHolderV14 vh,
//                                               ValueHolderV14<OnlineFundOccupyResult> onlineFundOccupyResultValueHolderV14,
//                                               OnlineFundOccupyResult dataOnlineFundOccupyResult) {
//        if (bl) {
//            OcBOrderParam ocBOrderParam = orderInfos.get(0);
//            OcBOrder order = ocBOrderParam.getOcBOrder();
//            if (onlineFundOccupyResultValueHolderV14.getCode() == ResultCode.SUCCESS) {
//                //成功
//                List<OnlineFundOccupyBaseItemModel> itemList = dataOnlineFundOccupyResult.getSucModelList().get(0).getItemList();
//                Map<String, BigDecimal> map = updateOrderPrice(user, ocBOrderParam, itemList, OrderWorehouseIsRetry.YES);
//                if (map == null) {
//                    vh.setCode(ResultCode.FAIL);
//                    vh.setMessage("更新订单主表的【结算金额】、【代销运费】失败！");
//                    saveLog(order, user, OrderLogTypeEnum.DELIVERGOODS_PRICE_OCCUPY, "更新订单主表的【结算金额】、【代销运费】失败！");
//                    return vh;
//                }
//                //日志记录
//                saveLog(order, user, OrderLogTypeEnum.DELIVERGOODS_PRICE_OCCUPY, "代销资金占用成功! 主表结算金额更新为："
//                        + map.get("CONSIGN_AMT") + "  代销运费更新为：" + map.get("CONSIGN_SHIP_AMT"));
//                vh.setCode(ResultCode.SUCCESS);
//                return vh;
//            } else {
//                //代销资金释放失败
//                OnlineFundOccupyModelResult onlineFundOccupyModelResult = dataOnlineFundOccupyResult.getErrModelList().get(0);
//                //启用备用字段 reserve_bigint06 代销资金处理重试标识     reserve_bigint07 代销资金处理状态（0 已处理  1 未处理）
//                boolean b = updateOrder(user, order, OrderWorehouseIsRetry.NO);
//                if (!b) {
//                    vh.setCode(ResultCode.FAIL);
//                    vh.setMessage("更新代销资金处理重试失败！");
//                    saveLog(order, user, OrderLogTypeEnum.DELIVERGOODS_PRICE_OCCUPY, "更新代销资金处理重试失败！");
//                    return vh;
//                }
//                //日志记录
//                saveLog(order, user, OrderLogTypeEnum.DELIVERGOODS_PRICE_OCCUPY, onlineFundOccupyModelResult.getErrMsg());
//
//                vh.setCode(ResultCode.FAIL);
//                vh.setMessage(onlineFundOccupyModelResult.getErrMsg());
//
//                return vh;
//            }
//        } else {
//            //仓库发货多条操作 worehouseBatch
//
//            if (log.isDebugEnabled()) {
//                log.debug("仓库发货调用线上资金服务补偿任务处理返回结果开始----》");
//            }
//            List<OnlineFundOccupySucModelResult> sucModelList = dataOnlineFundOccupyResult.getSucModelList();
//            List<OnlineFundOccupyModelResult> errModelList = dataOnlineFundOccupyResult.getErrModelList();
//            for (OcBOrderParam ocBOrderParam : orderInfos) {
//                boolean bol = false;
//                OcBOrder order = ocBOrderParam.getOcBOrder();
//                Long orderId = order.getId();
//                //成功结果处理
//                for (OnlineFundOccupySucModelResult onlineFundOccupySucModelResult : sucModelList) {
//                    if (orderId.equals(onlineFundOccupySucModelResult.getSourceBillId())) {
//                        List<OnlineFundOccupyBaseItemModel> itemList = onlineFundOccupySucModelResult.getItemList();
//                        //   更新订单明细的【结算单价】、【结算金额】； 更新订单主表的【结算金额】、【代销运费】
//                        Map<String, BigDecimal> map = updateOrderPrice(user, ocBOrderParam, itemList, OrderWorehouseIsRetry.YES);
//                        if (map == null) {
//                            vh.setCode(ResultCode.FAIL);
//                            vh.setMessage("更新订单主表的【结算金额】、【代销运费】失败！");
//                            saveLog(order, user, OrderLogTypeEnum.DELIVERGOODS_PRICE_OCCUPY, "更新订单主表的【结算金额】、【代销运费】失败！");
//                            return vh;
//                        }
//                        //日志记录
//                        saveLog(order, user, OrderLogTypeEnum.DELIVERGOODS_PRICE_OCCUPY, "代销资金占用成功! 主表结算金额更新为："
//                                + map.get("CONSIGN_AMT") + "  代销运费更新为：" + map.get("CONSIGN_SHIP_AMT"));
//                        bol = true;
//                        break;
//                    }
//                }
//
//                //失败结果处理
//                if (!bol) {
//                    for (OnlineFundOccupyModelResult onlineFundOccupyModelResult : errModelList) {
//                        if (order.getId().equals(onlineFundOccupyModelResult.getModelRequest().getSourceBillId())) {
//                            boolean b = updateOrder(user, order, OrderWorehouseIsRetry.NO);
//                            if (b) {
//                                log.debug("仓库发货调用线上资金服务补偿任务处理返回结果失败处理成功！" + onlineFundOccupyModelResult.getErrMsg());
//                                //日志记录
//                                saveLog(order, user, OrderLogTypeEnum.DELIVERGOODS_PRICE_OCCUPY, onlineFundOccupyModelResult.getErrMsg());
//                            }
//                        }
//                    }
//                }
//
//            }
//            if (log.isDebugEnabled()) {
//                log.debug("仓库发货调用线上资金服务补偿任务处理返回结果结束！");
//            }
//            vh.setCode(ResultCode.SUCCESS);
//            vh.setMessage("处理成功！");
//            return vh;
//        }
//    }

    /**
     * 审核经销商资金占用失败且获取结算
     *
     * @param user
     * @param orderInfos
     * @param bl
     * @param vh
     * @param onlineFundOccupyResultValueHolderV14
     * @param dataOnlineFundOccupyResult
     * @return
     */
//    private ValueHolderV14 checkServer(User user, List<OcBOrderParam> orderInfos, boolean bl, ValueHolderV14 vh, ValueHolderV14<OnlineFundOccupyResult> onlineFundOccupyResultValueHolderV14, OnlineFundOccupyResult dataOnlineFundOccupyResult) {
//
//        if (bl) {
//            OcBOrderParam ocBOrderParam = orderInfos.get(0);
//            OcBOrder order = ocBOrderParam.getOcBOrder();
//            if (onlineFundOccupyResultValueHolderV14.getCode() == ResultCode.SUCCESS) {
//                //成功
//                List<OnlineFundOccupyBaseItemModel> itemList = dataOnlineFundOccupyResult.getSucModelList().get(0)
//                        .getItemList();
//                Map<String, BigDecimal> map = updateOrderPrice(user, ocBOrderParam, itemList, null);
//                if (map == null) {
//                    vh.setCode(ResultCode.FAIL);
//                    vh.setMessage("更新订单主表的【结算金额】、【代销运费】失败！");
//                    saveLog(order, user, OrderLogTypeEnum.CHECK__PRICE_OCCUPY, "更新订单主表的【结算金额】、【代销运费】失败！");
//                    return vh;
//                }
//                //日志记录
//                saveLog(order, user, OrderLogTypeEnum.CHECK__PRICE_OCCUPY, "代销资金占用成功! 主表结算金额更新为："
//                        + map.get("CONSIGN_AMT") + "  代销运费更新为：" + map.get("CONSIGN_SHIP_AMT"));
//                vh.setCode(ResultCode.SUCCESS);
//                return vh;
//            } else {
//                //代销资金释放失败
//                OnlineFundOccupyModelResult onlineFundOccupyModelResult = dataOnlineFundOccupyResult.getErrModelList().get(0);
//                List<OnlineFundOccupyBaseItemModel> itemList = onlineFundOccupyModelResult.getModelRequest().getItemList();
//                Map<String, BigDecimal> map = updateOrderPrice(user, ocBOrderParam, itemList, null);
//                if (map == null) {
//                    vh.setCode(ResultCode.FAIL);
//                    vh.setMessage("更新订单主表的【结算金额】、【代销运费】失败！");
//                    saveLog(order, user, OrderLogTypeEnum.CHECK__PRICE_OCCUPY, "更新订单主表的【结算金额】、【代销运费】失败！");
//                    return vh;
//                }
//                //日志记录
//                saveLog(order, user, OrderLogTypeEnum.CHECK__PRICE_OCCUPY, onlineFundOccupyModelResult.getErrMsg() + "主表结算金额更新为："
//                        + map.get("CONSIGN_AMT") + "  代销运费更新为：" + map.get("CONSIGN_SHIP_AMT"));
//                vh.setCode(ResultCode.FAIL);
//                vh.setMessage(onlineFundOccupyModelResult.getErrMsg());
//                return vh;
//            }
//        } else {
//            //todo 审核多条操作
//            return vh;
//        }
//    }
//
//    /**
//     * 更新订单明细的【结算单价】、【结算金额】；
//     * 更新订单主表的【结算金额】、【代销运费】
//     *
//     * @param user
//     * @param ocBOrderParam
//     * @param itemList
//     * @param reserveBigint07Status
//     * @return
//     */
//    private Map<String, BigDecimal> updateOrderPrice(User user, OcBOrderParam ocBOrderParam,
//                                                     List<OnlineFundOccupyBaseItemModel> itemList,
//                                                     OrderWorehouseIsRetry reserveBigint07Status) {
//        OcBOrder order = ocBOrderParam.getOcBOrder();
//        List<OcBOrderItem> orderItemList = ocBOrderParam.getOrderItemList();
//        //代销运费
//        BigDecimal consignShipAmt = BigDecimal.ZERO;
//        //主表结算金额
//        BigDecimal consignAmt = BigDecimal.ZERO;
//        for (OcBOrderItem orderItem : orderItemList) {
//            //订单明细id
//            Long id = orderItem.getId();
//            //结算金额
//            BigDecimal multiply = BigDecimal.ZERO;
//            //结算单价
//            BigDecimal settlePrice = BigDecimal.ZERO;
//            for (OnlineFundOccupyBaseItemModel onlineFundOccupyBaseItemModel : itemList) {
//                //订单明细id
//                Long id1 = onlineFundOccupyBaseItemModel.getId();
//                if (id.equals(id1)) {
//                    //更新明细的结算单价和结算金额
//                    settlePrice = onlineFundOccupyBaseItemModel.getSettlePrice();
//                    //数量
//                    BigDecimal qty = orderItem.getQty() == null ? BigDecimal.ZERO : orderItem.getQty();
//                    //结算金额
//                    multiply = settlePrice.multiply(qty);
//                    //主表代销结算金额
//                    consignAmt = consignAmt.add(multiply);
//                    break;
//                }
//            }
//            //更新订单明细的【结算单价】、【结算金额】
//            JSONObject updateOrderItem = new JSONObject();
//            updateOrderItem.put("PRICE_SETTLE", settlePrice);
//            updateOrderItem.put("TOT_PRICE_SETTLE", multiply);
//            updateOrderItem.put("MODIFIERENAME", user.getEname());
//            updateOrderItem.put("MODIFIERNAME", user.getName());
//            updateOrderItem.put("MODIFIEDDATE", new Date(System.currentTimeMillis()));
//            updateOrderItem.put("oc_b_order_id".toLowerCase(), order.getId());
//            updateOrderItem.put("id".toLowerCase(), orderItem.getId());
//            ocBOrderItemMapper.updateRecord(updateOrderItem);
//        }
//
//        for (OnlineFundOccupyBaseItemModel onlineFundOccupyBaseItemModel : itemList) {
//            BigDecimal freight = onlineFundOccupyBaseItemModel.getFreight() == null ? BigDecimal.ZERO : onlineFundOccupyBaseItemModel.getFreight();
//            consignShipAmt = consignShipAmt.add(freight);
//        }
//        //更新订单主表的【结算金额】、【代销运费】
//        boolean b = updateOrder(user, order, consignShipAmt, consignAmt, reserveBigint07Status);
//        if (b) {
//            Map<String, BigDecimal> map = new HashMap<>();
//            //代销运费
//            map.put("CONSIGN_SHIP_AMT", consignShipAmt);
//            //主表结算金额
//            map.put("CONSIGN_AMT", consignAmt);
//            return map;
//        } else {
//            return null;
//        }
//    }

    /**
     * @param user
     * @param order
     * @param consignShipAmt
     * @param consignAmt
     * @param reserveBigint07Status
     * @return
     */
    private boolean updateOrder(User user, OcBOrder order, BigDecimal consignShipAmt, BigDecimal consignAmt,
                                OrderWorehouseIsRetry reserveBigint07Status) {
        OcBOrder update = new OcBOrder();
        if (reserveBigint07Status != null) {
            /* update.setConsignAmtStatus(reserveBigint07Status.getVal());
            if (reserveBigint07Status.getVal() == OrderWorehouseIsRetry.NO.getVal()) {
               long l = order.getConsignAmtRetryFlag() == null ? 0L : order.getConsignAmtRetryFlag();
                update.setConsignAmtRetryFlag(++l);
            }*/
        }
        update.setId(order.getId());
        update.setConsignShipAmt(consignShipAmt);
        update.setConsignAmt(consignAmt);
        update.setModifierename(user.getEname());
        update.setModifiername(user.getName());
        update.setModifieddate(new Date(System.currentTimeMillis()));
        int record = ocBOrderMapper.updateById(update);
        if (record > 0) {

            return true;
        }
        return false;
    }

    /**
     * 更新代销资金处理重试标识
     *
     * @param user
     * @param order
     * @return
     */
    private boolean updateOrder(User user, OcBOrder order, OrderWorehouseIsRetry orderWorehouseIsRetry) {
        OcBOrder update = new OcBOrder();
 /*       long l = order.getConsignAmtRetryFlag() == null ? 0L : order.getConsignAmtRetryFlag();
        update.setConsignAmtRetryFlag(++l);*/
        // update.setConsignAmtStatus(orderWorehouseIsRetry.getVal());
        update.setId(order.getId());
        update.setModifierename(user.getEname());
        update.setModifiername(user.getName());
        update.setModifieddate(new Date(System.currentTimeMillis()));
        int record = ocBOrderMapper.updateById(update);
        if (record > 0) {

            return true;
        }
        return false;
    }

    /**
     * 反审核经销商资金占用失败且获取结算
     *
     * @param user
     * @param bl
     * @param vh
     * @param onlineFundOccupyResultValueHolderV14
     * @param dataOnlineFundOccupyResult
     * @return
     */
//    private ValueHolderV14 theauditServer(User user, List<OcBOrderParam> orderInfos, boolean bl, ValueHolderV14 vh,
//                                          ValueHolderV14<OnlineFundOccupyResult> onlineFundOccupyResultValueHolderV14,
//                                          OnlineFundOccupyResult dataOnlineFundOccupyResult) {
//
//        if (bl) {
//            OcBOrder ocBOrder = orderInfos.get(0).getOcBOrder();
//            if (onlineFundOccupyResultValueHolderV14.getCode() == ResultCode.SUCCESS) {
//                //成功
//                //日志记录
//                saveLog(ocBOrder, user, OrderLogTypeEnum.THEAUDIT_PRICE_OCCUPY, "代销资金释放成功");
//                vh.setCode(ResultCode.SUCCESS);
//                return vh;
//            } else {
//                OnlineFundOccupyModelResult onlineFundOccupyModelResult = dataOnlineFundOccupyResult.getErrModelList().get(0);
//                if (log.isDebugEnabled()) {
//                    log.debug("反审核代销资金释放失败" + JSONObject.toJSONString(onlineFundOccupyModelResult));
//                }
//                saveLog(ocBOrder, user, OrderLogTypeEnum.THEAUDIT_PRICE_OCCUPY, "代销资金释放失败" +
//                        onlineFundOccupyModelResult.getErrMsg());
//                vh.setCode(ResultCode.FAIL);
//                vh.setMessage(onlineFundOccupyModelResult.getErrMsg());
//                return vh;
//            }
//        } else {
//            //todo 反审核多条操作
//            return vh;
//        }
//    }


    /**
     * 店铺渠道类型判断
     *
     * @return 分销返回true  反之false
     */
    public boolean isShopChannelType(Long cpCShopId) {

        CpShop cpShop = cpRpcService.selectShopById(cpCShopId);

        //获取渠道类型
        String channelType = cpShop.getChannelType();
        if (CpCShopChannelType.DISTRIBUTION.getVal().equals(channelType)) {
            return true;
        }
        return false;
    }

    /**
     * 订单主表日志记录
     *
     * @param ocBOrder
     * @param user
     * @param logContent 日志内容
     */
    private void saveLog(OcBOrder ocBOrder, User user, OrderLogTypeEnum orderLogTypeEnum, String logContent) {

        /**
         *  调用日志服务
         *  参数 订单id、订单编号、订单类型、日志信息、参数、错误信息
         */
        try {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), orderLogTypeEnum.getKey(),
                    logContent, "", "", user);
        } catch (Exception e) {
            log.error(this.getClass().getName() + orderLogTypeEnum.getKey() + " id为 " + ocBOrder.getId() + " 调用日志服务异常 !" + e.toString());
        }
    }

    /**
     * 全渠道订单主表es推送
     *
     * @param ocBOrder
     */
 /*   private void esOrderPush(OcBOrder ocBOrder) {
        try {
            Boolean document = SpecialElasticSearchUtil.indexDocument(OC_B_ORDER_INDEX_NAME, OC_B_ORDER_TYPE_NAME,
                    ocBOrderMapper.selectByID(ocBOrder.getId()), ocBOrder.getId());

            if (!document) {
                if (log.isDebugEnabled()) {
                    log.error("mfz-经销商资金占用失败且获取结算价失败 主表id为 ->" + ocBOrder.getId() + " ES推送失败！");
                }
            }
        } catch (IOException e) {
            if (log.isDebugEnabled()) {
                log.error("mfz-经销商资金占用失败且获取结算价失败 主表id为 ->" + ocBOrder.getId() + " ES推送失败！");
            }

        }
    }*/

}