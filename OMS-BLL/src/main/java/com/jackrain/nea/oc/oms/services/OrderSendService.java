package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryProcessor;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.retail.service.RetailNotifyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单出库  仓库发货 批量平台发货  标记平台发货 按钮
 *
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-04-02 10:09
 */
@Slf4j
@Component
public class OrderSendService {

    public final static int type = 1;

    public final static String logType = "平台发货";
    public final static String logMassage = "平台发货成功";

    @Autowired
    private OmsOrderOutService omsOrderOutService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OrderDeliveryProcessor orderDeliveryProcessor;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private RetailNotifyService retailNotifyService;


    /**
     * 订单出库按钮
     *
     * @param querySession
     * @return
     */
    public ValueHolder warehouseSendMenu(QuerySession querySession) {
        ValueHolder result = new ValueHolder();
        User user = querySession.getUser();
        HashMap map = new HashMap();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        List<Integer> list = (List) param.get("ids");
        if (CollectionUtils.isEmpty(list)) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "参数为空");
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }
        StringBuffer errorMassage = new StringBuffer();
        int i = 1;
        for (Integer objid : list) {
            ValueHolderV14 massage = omsOrderOutService.orderOutOfStock(Long.valueOf(objid), type, user, new Date());
            if (massage.getCode() == -1) {
                errorMassage.append("第" + i + "条记录错误,错误原因为" + massage.getMessage() + "</br>");
            } else {
                OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(objid.longValue());
                OcBOrder orderInfo = orderRelation.getOrderInfo();
                if (orderInfo != null) {
                    Integer platform = orderInfo.getPlatform();
                    if (PlatFormEnum.POS.getCode().equals(platform)) {
                        ValueHolder valueHolder = retailNotifyService.notifyRetailByMq(orderRelation, 2);
                    }
                }

            }
            i++;
        }
        if (StringUtils.isNotEmpty(errorMassage.toString())) {
            map.put("code", ResultCode.FAIL);
            map.put("message", errorMassage.toString());
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }
        map.put("code", ResultCode.SUCCESS);
        map.put("message", "操作成功");
        map.put("data", new HashMap<>());
        result.setData(map);
        return result;
    }

    /**
     * 批量平台发货按钮
     *
     * @return
     */
    public ValueHolder sendsList(QuerySession querySession) {
        ValueHolder result = new ValueHolder();
        User user = querySession.getUser();
        HashMap map = new HashMap();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        List<String> list = getStrings(param);
        if (CollectionUtils.isEmpty(list)) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "参数不能为空");
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }
        StringBuffer errormassage = new StringBuffer();
        StringBuilder successMessage = new StringBuilder();
        try {
            for (String objId : list) {
                OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                OcBOrder dbOcBOrder = ocBOrderMapper.selectByID(Long.parseLong(objId));
                if (null == dbOcBOrder) {
                    errormassage.append("[订单编号:" + objId + ":不存在]</br>");
                    continue;
                }

                log.info(LogUtil.format("dbOcBOrder={}", "sendsList"), JSONObject.toJSONString(dbOcBOrder));

                if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(dbOcBOrder.getOrderStatus())) {
                    errormassage.append("[订单编号" + objId + ":状态非仓库发货，不允许平台发货！]</br>");
                    continue;
                }
                if (dbOcBOrder.getIsInreturning().equals(InreturningStatus.INRETURNING)) {
                    //如果是退款中不允许平台发货走强制发货
                    errormassage.append("[订单编号" + objId + ":退货中，请强制平台发货！]</br>");
                    continue;
                }
                ocBOrderRelation.setOrderInfo(dbOcBOrder);
                ocBOrderRelation.setAutomaticOperation(false);
                boolean orderResult = orderDeliveryProcessor.platformSend(ocBOrderRelation);
                if (false == orderResult) {
                    errormassage.append("[订单编号:" + objId + ":平台发货失败]</br>");
                    continue;
                }
                successMessage.append("[订单编号:" + objId + ":平台发货中 请稍后查看订单]</br>");
            }
        } catch (Exception e) {
            log.error("批量平台发货异常", e.getMessage());
            throw e;
        }
        if (StringUtils.isNotEmpty(errormassage.toString())) {
            map.put("code", ResultCode.FAIL);
            map.put("message", errormassage.toString() + successMessage.toString());
            map.put("data", new HashMap<>());
            if (log.isDebugEnabled()) {
                log.debug("批量平台发货按钮存在失败返回结果:" + result.toDebugString());
            }
            result.setData(map);
            return result;
        }
        map.put("code", ResultCode.SUCCESS);
        map.put("message", successMessage.toString());
        map.put("data", new HashMap<>());
        result.setData(map);
        return result;
    }

    /**
     * 标记平台发货
     *
     * @return
     */
    public ValueHolder markPlatformSend(QuerySession querySession) {
        User user = querySession.getUser();
        ValueHolder result = new ValueHolder();
        HashMap map = new HashMap();

        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        List<String> list = getStrings(param);

        if (CollectionUtils.isEmpty(list)) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "参数为空了");
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }
        List<Long> integerList = list.stream().map(x -> Long.valueOf(x)).collect(Collectors.toList());
        //锁单
        Map<String, List> lockList = this.getLock(integerList);
        List<OcBOrder> dbOcBOrderList = ocBOrderMapper.selectOrderListByIds(integerList);
        List<RedisReentrantLock> lockListKey = (List<RedisReentrantLock>) lockList.get("lockList");//锁集合用来解锁
        try {
            if (CollectionUtils.isNotEmpty(lockList.get("failIds"))) {
                return this.getResult(ResultCode.FAIL, "订单编号:" + lockList.get("failIds") + "正在被操作,请稍后重试</br>");
            }
            //开始参数校验
            ValueHolderV14 errorResult = checkPlatformMenu(dbOcBOrderList, integerList);
            if (errorResult.getCode() == -1) {
                map.put("code", ResultCode.FAIL);
                map.put("message", errorResult.getMessage());
                map.put("data", new HashMap<>());
                result.setData(map);
                return result;
            }
            //循环调用
            for (OcBOrder ocBOrder : dbOcBOrderList) {
                OcBOrder flagOrder = new OcBOrder();
                flagOrder.setId(ocBOrder.getId());
                flagOrder.setBillNo(ocBOrder.getBillNo());
                flagOrder.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                flagOrder.setModifierid((long) querySession.getUser().getId());
                flagOrder.setModifiername(querySession.getUser().getName());
                flagOrder.setModifierename(querySession.getUser().getEname());
                flagOrder.setModifieddate(new Date());

                //主表更新
                ocBOrderMapper.updateById(flagOrder);
                //调用日志服务
                omsOrderLogService.addUserOrderLog(flagOrder.getId(), flagOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMassage, "", "", user);

            }
            map.put("code", ResultCode.SUCCESS);
            map.put("message", "操作成功");
            map.put("data", new HashMap<>());
            result.setData(map);
        } catch (Exception e) {
            log.error(LogUtil.format("标记平台发货,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        } finally {
            for (RedisReentrantLock lockItem : lockListKey) {
                lockItem.unlock();
            }
        }
        return result;
    }


    public ValueHolderV14 checkPlatformMenu(List<OcBOrder> dbOcBOrderList, List<Long> ids) {
        ValueHolderV14 result = new ValueHolderV14();
        List<OcBOrder> ocBOrderList = dbOcBOrderList.stream().
                filter(x -> null != x.getId() && !ids.contains(x.getId())).collect(Collectors.toList());
        //   校验传入的参数是否全部存在
        if (CollectionUtils.isNotEmpty(ocBOrderList)) {
            List<String> billNoList = ocBOrderList.stream().map(x -> x.getBillNo()).collect(Collectors.toList());
            result.setCode(ResultCode.FAIL);
            result.setMessage("以下记录不存在,订单编号为" + billNoList);
            return result;
        }
        //判断订单状态是否为仓库发货   若不是，则提示“XXX订单状态非仓库发货，不允许平台发货！”
        StringBuffer errorMassage = new StringBuffer();
        List<OcBOrder> warehouseSendsList = dbOcBOrderList.stream().
                filter(x -> !x.getOrderStatus().equals(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(warehouseSendsList)) {
            List<String> billList = warehouseSendsList.stream().map(x -> x.getBillNo()).collect(Collectors.toList());
            errorMassage.append("{以下订单" + billList + "订单状态非仓库发货，不允许平台发货}</br>");
        }

        if (StringUtils.isNotEmpty(errorMassage)) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(errorMassage.toString());
            return result;
        }
        result.setCode(ResultCode.SUCCESS);
        return result;
    }

    /**
     * 强制平台发货
     * 修改 先锁单 再查询
     *
     * @return
     */
    public ValueHolder forceSendList(QuerySession querySession) {
        ValueHolder result = new ValueHolder();
        HashMap map = new HashMap();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        List<String> list = getStrings(param);

        if (CollectionUtils.isEmpty(list)) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "强制发货ids为空");
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }
        List<Long> ids = list.stream().map(x -> Long.valueOf(x)).collect(Collectors.toList());
        Map<String, List> lockList = this.getLock(ids);
        List<OcBOrder> dbOcBOrderList = ocBOrderMapper.selectOrderListByIds(ids);
        List<RedisReentrantLock> lockListKey = (List<RedisReentrantLock>) lockList.get("lockList");//锁集合用来解锁
        StringBuilder errormassage = new StringBuilder();
        StringBuilder successMeaaage = new StringBuilder();
        try {
            if (CollectionUtils.isNotEmpty(lockList.get("failIds"))) {
                return this.getResult(ResultCode.FAIL, "订单编号:" + lockList.get("failIds") + "正在被操作,请稍后重试");
            }
            ValueHolderV14 checkparam = this.checkParam(dbOcBOrderList);//校验订单状态,是否退款中
            if (checkparam.getCode() == ResultCode.FAIL) {
                return this.getResult(ResultCode.FAIL, checkparam.getMessage());
            }
            for (OcBOrder ocBOrder : dbOcBOrderList) {
                OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                ocBOrderRelation.setOrderInfo(ocBOrder);
                ocBOrderRelation.setAutomaticOperation(false);
                if (orderDeliveryProcessor.platformSend(ocBOrderRelation)) {
                    successMeaaage.append("订单编号:" + ocBOrder.getId() + ":强制平台发货中 请稍后查看订单");
                } else {
                    errormassage.append("订单编号:" + ocBOrder.getId() + ":强制平台发货失败");
                    continue;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("强制发货异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            return this.getResult(ResultCode.FAIL, e.getMessage());
        } finally {
            for (RedisReentrantLock lockItem : lockListKey) {
                lockItem.unlock();
            }
        }
        if (StringUtils.isNotEmpty(errormassage.toString())) {
            return this.getResult(ResultCode.FAIL, errormassage.toString() + "\n" + successMeaaage);
        }
        return this.getResult(ResultCode.SUCCESS, successMeaaage.toString());


    }

    /**
     * 锁单
     *
     * @param idsList
     * @return
     */
    public Map<String, List> getLock(List<Long> idsList) {
        List<RedisReentrantLock> lockList = new ArrayList<>();
        List<Long> ids = Lists.newArrayList();
        List<Long> failIds = Lists.newArrayList();
        Map<String, List> map = new HashMap<>();

        for (Long i : idsList) {
            try {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(i);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockList.add(redisLock);
                    ids.add(i);
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug(this.getClass().getName() + "订单锁定失败" + i);
                    }
                    failIds.add(i);
                }
            } catch (Exception e) {
                log.error(this.getClass().getName() + "订单锁定异常" + i);
                failIds.add(i);
            }
        }
        map.put("failIds", failIds);
        map.put("lockList", lockList);
        map.put("ids", ids);
        return map;
    }

    /**
     * 参数状态校验,是否退款中校验
     *
     * @param ocBOrderList
     * @return
     */
    public ValueHolderV14 checkParam(List<OcBOrder> ocBOrderList) {
        ValueHolderV14 result = new ValueHolderV14();
        StringBuilder errorMessage = new StringBuilder();
        for (OcBOrder ocBOrder : ocBOrderList) {
            if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus())) {
                errorMessage.append("[订单编号:" + ocBOrder.getId() + "订单状态非仓库发货,不允许强制平台发货]</br>");
            }
            if (!ocBOrder.getIsInreturning().equals(InreturningStatus.INRETURNING)) {
                errorMessage.append("[订单编号:" + ocBOrder.getId() + "不是退款中,不允许强制平台发货]</br>");
            }
        }
        if (StringUtils.isNotEmpty(errorMessage.toString())) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(errorMessage.toString());
            return result;
        }
        result.setCode(ResultCode.SUCCESS);
        return result;
    }

    /**
     * 返回结果
     *
     * @param code
     * @return
     */
    public ValueHolder getResult(Integer code, String message) {
        ValueHolder result = new ValueHolder();
        HashMap map = new HashMap();
        map.put("code", code);
        map.put("message", message);
        map.put("data", new HashMap<>());
        result.setData(map);
        return result;
    }

    private List<String> getStrings(JSONObject param) {
        JSONArray ids = param.getJSONArray("ids");
        List<String> list = Lists.newArrayList();
        for (Object i : ids) {
            if (i == null) {
                continue;
            }
            list.add(String.valueOf(i));
        }
        return list;
    }


}
