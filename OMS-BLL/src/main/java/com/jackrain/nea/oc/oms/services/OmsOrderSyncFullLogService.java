package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.ip.model.OrderFullLinkModel;
import com.jackrain.nea.ip.model.result.OrderFullLinkResult;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLinkMapper;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLink;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @author: heliu
 * @since: 2019/4/12
 * create at : 2019/4/12 11:02
 */
@Component
@Slf4j
public class OmsOrderSyncFullLogService {

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private OcBOrderLinkMapper ocBOrderLinkMapper;

    /**
     * 同步全链路日志到平台
     *
     * @param ocBOrderLink 同步全链路日志
     * @return boolean
     */
    public boolean doSyncLinkPlatform(OcBOrderLink ocBOrderLink) {

        try {
            //同步日志信息到平台l
            OrderFullLinkModel orderFullLinkModel = new OrderFullLinkModel();
            //链路主表 ocBOrderLinkId
            orderFullLinkModel.setId(ocBOrderLink.getId());
            //分库建
            orderFullLinkModel.setOcBOrderId(ocBOrderLink.getOcBOrderId());
            //平台单号
            orderFullLinkModel.setTid(ocBOrderLink.getTid());
            //卖家昵称
            orderFullLinkModel.setSellerNick(ocBOrderLink.getSellerNick());
            //回流状态
            orderFullLinkModel.setStatus(ocBOrderLink.getBackflowStatus());
            List<OrderFullLinkModel> linkResultList = new ArrayList<>();
            linkResultList.add(orderFullLinkModel);
//            log.debug("OrderId" + ocBOrderLink.getOcBOrderId() + "同步链路日志平台接口入参：" + JSONObject.toJSONString(linkResultList));
            ValueHolderV14<List<OrderFullLinkResult>> valueHolderV14 = ipRpcService.orderFullLink(linkResultList, SystemUserResource.getRootUser());
//            log.debug("OrderId[" + ocBOrderLink.getOcBOrderId() + "]同步链路日志平台接口出参: " + valueHolderV14.toJSONObject());
            //根据code判断接口返回状态
            if (valueHolderV14.isOK()) {
                OrderFullLinkResult orderFullLinkResult = valueHolderV14.getData().get(0);
                log.debug("订单OrderId=" + ocBOrderLink.getOcBOrderId() + "同步链路日志出参:" + "链路日志表Id" + ocBOrderLink.getId() + "平台返回结果!orderFullLinkResult-->" + orderFullLinkResult);
                if ("true".equalsIgnoreCase(orderFullLinkResult.getIsSend())) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("同步全链路日志接口调用失败,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }


    /**
     * 批量同步全链路日志到平台
     *
     * @param linkResultList 同步全链路日志
     * @return boolean
     */
    public boolean batchSyncLinkPlatform(List<OrderFullLinkModel> linkResultList) {

        try {
            if (CollectionUtils.isEmpty(linkResultList)) {
                return true;
            }
//            log.debug("batchSyncLinkPlatform  同步链路日志平台接口入参：" + JSONObject.toJSONString(linkResultList));
            ValueHolderV14<List<OrderFullLinkResult>> valueHolderV14 = ipRpcService.orderFullLink(linkResultList, SystemUserResource.getRootUser());
//            log.debug("batchSyncLinkPlatform 同步链路日志平台接口出参: " + valueHolderV14.toJSONObject());
            //根据code判断接口返回状态
            if (valueHolderV14.isOK()) {
                OrderFullLinkResult orderFullLinkResult = valueHolderV14.getData().get(0);
                if ("true".equalsIgnoreCase(orderFullLinkResult.getIsSend())) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("同步全链路日志接口调用失败,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 保存全链路日志
     *
     * @param ocBOrderLink 全链路日志对象
     * @return int
     */
    public int saveOrderLinkLog(OcBOrderLink ocBOrderLink) {
        return ocBOrderLinkMapper.insert(ocBOrderLink);
    }


    /**
     * 补偿任务执行成功
     *
     * @param ocBOrderLink ocBOrderLink
     */
    public void updateMakeupOrderStatusSuccess(OcBOrderLink ocBOrderLink) {

        String indexName = OcElasticSearchIndexResources.OC_B_ORDER_LINK;
        Long orderId = ocBOrderLink.getOcBOrderId();
        //更改同步状态为成功
        ocBOrderLink.setSyncStatus(SyncStatus.SYNCSUCCESS.toInteger());
        //补偿备注
        ocBOrderLink.setMakeupRemarks("订单OrderId" + ocBOrderLink.getOcBOrderId() + "链路日志自动补偿成功!");
        QueryWrapper<OcBOrderLink> wrapper = new QueryWrapper<>();
        wrapper.eq("id", ocBOrderLink.getId());
        wrapper.eq("oc_b_order_id", ocBOrderLink.getOcBOrderId());
        //更新之前分库建必须设置为空
        ocBOrderLink.setOcBOrderId(null);
        int resultEs = ocBOrderLinkMapper.update(ocBOrderLink, wrapper);
        ocBOrderLink.setOcBOrderId(orderId);
       /* if (resultEs > 0) {
            log.debug(ocBOrderLink.toString());
            try {
                boolean flag = SpecialElasticSearchUtil.indexDocument(indexName, OC_B_ORDER_LINK_TYPE_NAME,
                        ocBOrderLinkMapper.queryById(ocBOrderLink.getId(), orderId), ocBOrderLink.getId());
                if (!flag) {
                    log.debug("订单OrderId" + ocBOrderLink.getOcBOrderId() + "链路日志自动补偿更新失败!");
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }*/
    }


    public List<OcBOrderLink> selectFullLinkList(Set<Long> orderIds) {
        List<OcBOrderLink> ocBOrderLinks = ocBOrderLinkMapper.queryOcBOrderLinkList(orderIds);
        return ocBOrderLinks;
    }

}