package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOccupyTask;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/7/20 上午11:12
 * @Version 1.0
 */
@Component
public class OmsOccupyTaskService {

    @Autowired
    private OcBOccupyTaskMapper ocBOccupyTaskMapper;
    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;
    @Autowired
    private OcBLowTemperatureOccupyTaskService ocBLowTemperatureOccupyTaskService;


    public void addOcBOccupyTaskNew(OcBOrder order, Date nextTime, Integer itemNum, boolean containsLowTemperature, boolean isPickUp) {
        if (containsLowTemperature) {
            if (!isPickUp) {
                // 只有提奶订单才会进低温寻源中间表
                return;
            }
            ocBLowTemperatureOccupyTaskService.addLowTemperaturOcBOccupyTask(order, nextTime, itemNum);
        } else {
            addOcBOccupyTask(order, nextTime);
        }
    }

    public void addOcBOccupyTask(OcBOrder order, Date nextTime) {
        if (!omsBusinessTypeStService.isAutoOccupy(order)) {
            //to b不寻源占单
            return;
        }
        OcBOccupyTask task = new OcBOccupyTask();
        task.setId(ModelUtil.getSequence("oc_b_occupy_task"));
        if (nextTime == null) {
            task.setNextTime(new Date());
        } else {
            task.setNextTime(nextTime);
        }
        task.setStatus(0);
        task.setShopId(order.getCpCShopId());
        task.setRetryNumber(0);
        task.setOrderId(order.getId());
        task.setCreationdate(new Date());
        ocBOccupyTaskMapper.insert(task);
    }

    public void addOcBOccupyTask(OcBOrder order, Date nextTime, Integer autoFlag) {
        if (!omsBusinessTypeStService.isAutoOccupy(order)) {
            //to b不寻源占单
            return;
        }
        OcBOccupyTask task = new OcBOccupyTask();
        task.setId(ModelUtil.getSequence("oc_b_occupy_task"));
        if (nextTime == null) {
            task.setNextTime(new Date());
        } else {
            task.setNextTime(nextTime);
        }
        task.setStatus(0);
        task.setShopId(order.getCpCShopId());
        task.setRetryNumber(0);
        task.setIsAuto(autoFlag);
        task.setOrderId(order.getId());
        task.setCreationdate(new Date());
        ocBOccupyTaskMapper.insert(task);
    }

    /**
     * 缺货重新寻源 (轮询)
     */
    public void addOcBOccupyOutStockTask(OcBOrder order, Integer time) {
        List<Long> orderIds = ocBOccupyTaskMapper.selectOcBOccupyTaskListByOrderId(order.getId());
        //经过寻源缺货的肯定有数据  如果没有则判定不自动寻源
        time = time == null ? 50 : time;
        if (CollectionUtils.isNotEmpty(orderIds)) {
            int i = time * 60 * 1000;
            long l = System.currentTimeMillis() + i;
            Date date = new Date(l);
            Long id = orderIds.get(0);
            ocBOccupyTaskMapper.updateOcBOccupyTaskListByOrderId(id, date);
        }
    }

    public void addOrUpdateOccupyTask(OcBOrder order, Integer time) {
        List<Long> orderIds = ocBOccupyTaskMapper.selectOcBOccupyTaskListByOrderId(order.getId());
        //经过寻源缺货的肯定有数据  如果没有则判定不自动寻源
        time = time == null ? 50 : time;
        int i = time * 60 * 1000;
        long l = System.currentTimeMillis() + i;
        Date date = new Date(l);
        if (CollectionUtils.isNotEmpty(orderIds)) {
            Long id = orderIds.get(0);
            ocBOccupyTaskMapper.updateOcBOccupyTaskListByOrderId(id, date);
        } else {
            if (!omsBusinessTypeStService.isAutoOccupy(order)) {
                //to b不寻源占单
                return;
            }
            OcBOccupyTask task = new OcBOccupyTask();
            task.setId(ModelUtil.getSequence("oc_b_occupy_task"));
            if (date == null) {
                task.setNextTime(new Date());
            } else {
                task.setNextTime(date);
            }
            task.setStatus(0);
            task.setShopId(order.getCpCShopId());
            task.setRetryNumber(0);
            task.setOrderId(order.getId());
            task.setCreationdate(new Date());
            ocBOccupyTaskMapper.insert(task);
        }
    }


    public void updateOcBOccupyOutStockDate(Long orderId, Date date) {
        ocBOccupyTaskMapper.updateOcBOccupyTaskListByOrderId(orderId, date);
    }

}
