package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.pm.service.PmPreExecService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * 订单促销预执行补偿服务
 *
 * @author: hly
 * @since: 2020/04/19
 * create at : 2020/04/19 21:16
 */
@Slf4j
@Component
public class PreExecutionMakeUpOrdersService {

    @Autowired
    private PmPreExecService pmPreExecService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private CpRpcService cpRpcService;

    /**
     * 订单促销预执行补偿服务
     *
     * @param obj
     * @param user
     * @return
     */
    public ValueHolderV14 preExecutionMakeUp(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        JSONArray idArray = obj.getJSONArray("IDS");
        //转成数组
        if (idArray == null || idArray.size() == 0) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("请求参数为空！");
        } else {
            String result = "";
            if (null != idArray) {
                for (Object object : idArray) {
                    ValueHolderV14 vh1 = new ValueHolderV14();
                    String id = object.toString();
                    OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(new Long(id));
                    if (orderRelation != null) {
                        OcBOrder order = orderRelation.getOrderInfo();
                        JSONObject preExecParam = new JSONObject();
                        JSONObject paramJson = new JSONObject();
                        String orderSource = order.getOrderSource();
                        paramJson.put("orderId", order.getId());
                        paramJson.put("orderNo", order.getSourceCode());
                        paramJson.put("orderTime", order.getOrderDate());
                        paramJson.put("earnestMoneyTime", order.getPresaleDepositTime());
                        paramJson.put("orderType", order.getOrderType());
                        paramJson.put("payTime", order.getPayTime());
                        paramJson.put("provinceIdD", order.getCpCRegionProvinceId());
                        paramJson.put("shopId", order.getCpCShopId());
                        CpShop cpShop = cpRpcService.selectShopById(order.getCpCShopId());
                        paramJson.put("shopEcode", cpShop.getEcode() == null ? "" : cpShop.getEcode());
                        paramJson.put("shopEname", cpShop.getCpCStoreEname() == null ? "" : cpShop.getCpCStoreEname());
                        paramJson.put("sellerRemark", order.getSellerMemo());
                        paramJson.put("buyerRemark", order.getBuyerMessage());
                        paramJson.put("enumdataFlatfopm", StringUtils.isEmpty(orderSource) ? "" :orderSource);
                        paramJson.put("memberName", order.getUserNick());
                        JSONArray productArray = new JSONArray();
                        List<OcBOrderItem> orderItemList;
                        if (orderRelation.getOrderInfo().getIsCombination() == 1) {
                            orderItemList = ocBOrderItemMapper.selectOrderItemListCombinationExecPm(orderRelation.getOrderInfo().getId());
                        } else {
                            orderItemList = orderRelation.getOrderItemList();
                        }
                        for (OcBOrderItem ocBOrderItem : orderItemList) {
                            JSONObject json = new JSONObject();
                            //吊牌价
                            json.put("amt_list", ocBOrderItem.getPriceTag() == null ? BigDecimal.ZERO : ocBOrderItem.getPriceTag());
                            //标准价
                            json.put("amt_retail", ocBOrderItem.getPriceList() == null ? BigDecimal.ZERO : ocBOrderItem.getPriceList());
                            //成交价
                            json.put("amt_receivable", ocBOrderItem.getRealAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getRealAmt().divide(ocBOrderItem.getQty(), 2, BigDecimal.ROUND_HALF_UP));
                            json.put("qtty", ocBOrderItem.getQty().intValue());
                            //促销中心确认后，sku的value也设置为商品编码
                            json.put("sku", ocBOrderItem.getPsCSkuEcode());
                            json.put("ecode", ocBOrderItem.getPsCSkuEcode());
                            json.put("platformEcode", ocBOrderItem.getSkuNumiid());
                            json.put("platformSku", "");
                            productArray.add(json);
                        }
                        paramJson.put("productList", productArray);
                        preExecParam.put("param", paramJson);
                        ValueHolder ret = pmPreExecService.callpreExecMakeUpRpc(preExecParam);
                        HashMap hashMap = ret.getData();
                        if (hashMap.get("data") != null) {
                            JSONObject dataJson = JSONObject.parseObject(hashMap.get("data") + "");
                            int code = Integer.parseInt(hashMap.get("code").toString());
                            vh1.setData(dataJson);
                            vh1.setCode(code);
                            vh1.setMessage(hashMap.get("message") + "");
                        } else {
                            vh1.setData("");
                            vh1.setCode(-1);
                            vh1.setMessage("促销中心，预执行补偿接口，返回结果为空");
                        }
                    }
                    result = result + vh1.toString();
                }
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("订单补偿促销预执行，参数缺失！");
            }
            vh.setCode(ResultCode.SUCCESS);
            vh.setData(result);
        }
        return vh;
    }

}