package com.jackrain.nea.oc.oms.services;


import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.UserConfig;
import com.jackrain.nea.oc.oms.model.user.UserQueryConfig;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class SaveUserQueryListService {


    public ValueHolder saveTableQuery(String tableName, List<UserConfig> userConfigList, User userWeb) throws NDSException {
        CusRedisTemplate<String, UserConfig> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
        UserQueryConfig userQueryConfig = (UserQueryConfig) objRedisTemplate.opsForHash().get("UserQueryConfig",
                userWeb.getId().toString());
        if (userQueryConfig == null) {
            userQueryConfig = new UserQueryConfig();
        }
        userQueryConfig.addParam(tableName, userConfigList);
        objRedisTemplate.opsForHash().put("UserQueryConfig", userWeb.getId().toString(), userQueryConfig);
        ValueHolder vh = new ValueHolder();
        vh.put("code", 0);
        vh.put("message", "success");
        return vh;

    }


}