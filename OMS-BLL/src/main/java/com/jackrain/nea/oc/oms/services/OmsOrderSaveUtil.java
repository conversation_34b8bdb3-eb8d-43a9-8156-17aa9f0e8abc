package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPaymentMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPromotionMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromotion;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @author: 易邵峰
 * @since: 2019-03-11
 * create at : 2019-03-11 23:45
 */
@Component
public class OmsOrderSaveUtil {
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderPromotionMapper orderPromotionMapper;
    @Autowired
    private OcBOrderPaymentMapper orderPaymentMapper;

    // 地址需要替换的内容
    static final String[] SIGN_DOTS = new String[]{",", "，"};
    // 替换目标
    static final String SIGN_TARGET = "::::";

    public boolean saveOmsOrderToDb(OcBOrderRelation orderInfo) {
        if (orderInfo == null) {
            return false;
        }

        if (orderInfo.getOrderInfo() == null) {
            return false;
        }

        orderInfo.getOrderInfo().setId(ModelUtil.getSequence("oc_b_order"));
        int result = orderMapper.insert(orderInfo.getOrderInfo());

        if (orderInfo.getOrderItemList() != null) {
            for (OcBOrderItem orderItem : orderInfo.getOrderItemList()) {
                orderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
                orderItem.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderItemMapper.insert(orderItem);
            }
        }

        if (orderInfo.getOrderPromotionList() != null) {
            for (OcBOrderPromotion promotion : orderInfo.getOrderPromotionList()) {
                promotion.setId(ModelUtil.getSequence("oc_b_order_promotion"));
                promotion.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPromotionMapper.insert(promotion);
            }
        }

        if (orderInfo.getOrderPaymentList() != null) {
            for (OcBOrderPayment payment : orderInfo.getOrderPaymentList()) {
                payment.setId(ModelUtil.getSequence("oc_b_order_payment"));
                payment.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderPaymentMapper.insert(payment);
            }
        }
        return (result > 0);
    }

    /**
     * 订单上的地址替换 - 订单列表
     *
     * @param orders
     */
    public void addressReplaceDot(List<OcBOrder> orders) {
        if (CollectionUtils.isNotEmpty(orders)) {
            orders.forEach(o -> {
                addressReplaceDot(o);
            });
        }
    }

    /**
     * 订单上的地址替换
     *
     * @param order
     */
    public void addressReplaceDot(OcBOrder order) {
        if (Objects.nonNull(order)) {
            String address = order.getReceiverAddress();
            address = addressReplaceDot(address);
            // 替换后重新赋值
            order.setReceiverAddress(address);
        }
    }

    /**
     * 地址中有逗号影响合单（es查询机制问题），需要替换成其他字符
     *
     * @param address
     * @return
     */
    public String addressReplaceDot(String address) {
        if (Objects.nonNull(address)) {
            for (String dot : SIGN_DOTS) {
                address = StringUtils.replace(address, dot, SIGN_TARGET);
            }
        }

        return address;
    }

    /**
     * 获取实例
     *
     * @return
     */
    public static OmsOrderSaveUtil getInstance() {
        return ApplicationContextHandle.getBean(OmsOrderSaveUtil.class);
    }

}
