package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.data.basic.model.request.ProInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.ip.model.LogisticsSendModel;
import com.jackrain.nea.ip.model.ascp.ConsignOrderShipModel;
import com.jackrain.nea.ip.model.others.RPCOutStorageModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendDataModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendResult;
import com.jackrain.nea.ip.model.result.LogisticsSendResult;
import com.jackrain.nea.ip.model.vips.VipJitxOrderShipModel;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderSource;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.LogisticsTelEnum;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.ps.api.table.PsCPro;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 订单平台发货后端服务
 *
 * @author: hulinyang
 * @since: 2019/4/02
 * create at : 019/4/02 15:29
 */

@Slf4j
@Component
@Deprecated
public class OmsOrderPlatformDeliveryService {
    @Autowired
    OrderPlatformDeliveryService orderPlatformDeliveryService;

    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;

    @Autowired
    private OmsOrderItemService omsOrderItemServie;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private BasicPsQueryService basicPsQueryService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private IpBTaobaoOrderItemMapper ipBTaobaoOrderItemMapper;

    @Autowired
    private IpBStandplatOrderItemMapper ipBStandplatOrderItemMapper;

    @Autowired
    private IpAlibabaAscpOrderService ipAlibabaAscpOrderService;

    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;

    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;

    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;

    @Autowired
    private CpRpcService cpRpcService;


    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;


    /**
     * 平台发货
     *
     * @param ocBOrderRelation 零售发货单信息
     * @param user             操作用户信息
     * @param type             发货类型。Type=1手动；=0自动；
     * @return
     */
    public boolean platformSend(OcBOrderRelation ocBOrderRelation, User user, int type) {
        if (log.isDebugEnabled()) {
            log.debug("OrderId={},OmsOrderPlatformDeliveryService.platformSend.Order={};User={};Type={}",
                    ocBOrderRelation.getOrderId(), JSON.toJSONString(ocBOrderRelation), user, type);
        }
        user = Optional.ofNullable(user).orElse(SystemUserResource.getRootUser());
        boolean result = true;
        if (InreturningStatus.INRETURNING.equals(ocBOrderRelation.getOrderInfo().getIsInreturning()) && type == 0) {
            //退款中 自动需要校验店铺策略的强制发货
            StCShopStrategyDO stCShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(ocBOrderRelation.getOrderInfo().getCpCShopId());
            if (Integer.valueOf(0).equals(Optional.ofNullable(stCShopStrategyDO).map(StCShopStrategyDO::getIsForceSend).orElse(0))) {
                result = false;
            }
        } else {
            result = this.updatePlatformDelivery(ocBOrderRelation, user);
        }
        if (!result) {
            //失败
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(ocBOrderRelation.getOrderInfo().getId());
            ocBOrder.setIsForce(0L);
            ocBOrder.setForceSendFailReason("平台失败，请检查是否存在退款中的商品或订单状态不符合发货条件");
            omsOrderService.updateOrderInfo(ocBOrder);
        }
        return result;
    }

    /**
     * 更新为“平台发货”
     *
     * @param ocBOrderRelation 订单关系实体
     * @return boolean
     */
    public boolean updatePlatformDelivery(OcBOrderRelation ocBOrderRelation, User user) {
        log.debug("平台发货服务开始订单id为：{}", ocBOrderRelation.getOrderInfo().getId());
        boolean flag;
        try {
            OcBOrder ocBOrder = omsOrderService.selectOrderInfo(ocBOrderRelation.getOrderId());
            //判断订单状态是否为仓库发货或平台发货状态,订单状态非仓库发货，不允许平台发货
            flag = checkBillStatus(ocBOrder);
            if (!flag) {
                if (log.isDebugEnabled()) {
                    log.debug("updatePlatformDelivery.Cannot.Delivery;OrderId={}", ocBOrderRelation.getOrderId());
                }
                return false;
            }
            //查询订单的订单明细中的平台单号(非退款状态)
            List<String> tidList = selectOrderDetailNotRefundTids(ocBOrder);
            Set<String> tidSet = new HashSet<>(tidList);
            if (PlatFormEnum.TAOBAO.getCode().equals(ocBOrder.getPlatform())
                    || PlatFormEnum.JINGDONG.getCode().equals(ocBOrder.getPlatform())
                    || PlatFormEnum.TAOBAO_DEAL.getCode().equals(ocBOrder.getPlatform())
                    || PlatFormEnum.TAOBAO_DISTRIBUTION.getCode().equals(ocBOrder.getPlatform())) {
                callNormalPlatformDeliveryInterface(ocBOrderRelation, ocBOrder, tidSet);
            } else if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
                callVopJitxDeliveryInterface(user, ocBOrder);
            } else if (PlatFormEnum.ALIBABAASCP.getCode().equals(ocBOrder.getPlatform())) {
                callAlibabaAscpDeliveryInterface(user, ocBOrder);
            } else {
                Boolean callResult = callSpecialPlatformDeliveryInterface(ocBOrderRelation, ocBOrder);
                if (callResult != null) {
                    return callResult;
                }
            }
        } catch (Exception ex) {
            log.error("OmsOrderPlatformDeliveryService.updatePlatformDelivery.Error", ex);
            updateSendDeliveryErrorInfo(ocBOrderRelation, ex);
            throw ex;
        }
        return flag;
    }

    /**
     * 猫超仓库发货调用云枢纽
     *
     * @param user     用户
     * @param ocBOrder 订单关系实体
     */
    private void callAlibabaAscpDeliveryInterface(User user, OcBOrder ocBOrder) {
        log.debug(this.getClass().getName() + "猫超仓库发货调用云枢纽，callAlibabaAscpDeliveryInterface==========开始" + "订单id为：" + ocBOrder.getId());
        ValueHolderV14 vh = alibabaAscpShippingBack(ocBOrder, user);
        //ShipResponse
        int retCode = vh.getCode();
        String msg = vh.getMessage();
        //failed_list
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.WAREHOUSE_DELIVERY_CALL_INTERFACE.getKey(), ocBOrder.getId() + "猫超仓库发货接口接收云枢纽返回信息为：" + msg, null, msg, user);
        if (retCode == ResultCode.SUCCESS) {
            updatePlaformDeliveryOrderStatusPushES(ocBOrder, user);
        } else {
            JSONObject retData = (JSONObject) vh.getData();
            if (retData != null) {
                JSONArray failArray = retData.getJSONArray("failed_list");
                if (failArray != null && !failArray.isEmpty()) {
                    JSONObject tmpJson = failArray.getJSONObject(0);
                    msg = tmpJson.getString("msg");
                }
            }
            ocBOrderMapper.updateOcBorderInsideRemark(msg, ocBOrder.getId());
            //调用更新失败次数接口
            ocBOrderItemMapper.updateFailTimesByOrderid(ocBOrder.getId());
            //调用主表更新失败次数接口
            ocBOrderMapper.updateMasterFailTimesById(ocBOrder.getId());

            OcBOrder orderFlag = new OcBOrder();
            orderFlag.setId(ocBOrder.getId());
            orderFlag.setBillNo(ocBOrder.getBillNo());
            orderFlag.setIsForce(0L);
            orderFlag.setForceSendFailReason(vh.getMessage());
            omsOrderService.updateOrderInfo(ocBOrder);
        }
    }

    private void updateSendDeliveryErrorInfo(OcBOrderRelation ocBOrderRelation, Exception ex) {
        OcBOrder ocBOrderflag = new OcBOrder();
        ocBOrderflag.setId(ocBOrderRelation.getOrderInfo().getId());
        ocBOrderflag.setIsForce(0L);
        if (StringUtils.isNotEmpty(ex.getMessage()) && ex.getMessage().length() > 199) {
            ocBOrderflag.setForceSendFailReason(ex.getMessage().substring(0, 199));
        } else {
            ocBOrderflag.setForceSendFailReason(ex.getMessage());
        }
        omsOrderService.updateOrderInfo(ocBOrderflag);

    }

    /**
     * @param ocBOrderRelation
     * @param ocBOrder
     * @return java.lang.Boolean
     * <AUTHOR>
     * @Description 通用平台（除淘宝、天猫、京东、唯品会外的其他小平台）发货调用
     * @Date 15:51 2020/7/16
     **/
    private Boolean callSpecialPlatformDeliveryInterface(OcBOrderRelation ocBOrderRelation, OcBOrder ocBOrder) {
        //增加多包裹处理方法
        if (ocBOrder.getIsMultiPack().equals(1L) && ocBOrder.getIsMultiPack() != null && PlatFormEnum.SUNING_ONLINE_MARKET.getCode().equals(ocBOrder.getPlatform())) {
            List<String> expressCodeList = new ArrayList<>();
            List<OcBOrderItem> ocBOrderItemList = omsOrderItemServie.selectUnSuccessRefund(ocBOrder.getId());
            List<OcBOrderDelivery> ocBOrderDeliveryList = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
            for (OcBOrderDelivery ocBOrderDelivery : ocBOrderDeliveryList) {
                String expressCode = ocBOrderDelivery.getLogisticNumber();
                if (expressCodeList.size() > 0) {
                    if (!expressCodeList.contains(expressCode)) {
                        expressCodeList.add(expressCode);
                    }
                } else {
                    expressCodeList.add(expressCode);
                }
            }
            //调用多包裹平台发货方法
            multiplePakageSNCallInterface(ocBOrder, ocBOrder.getIsSplit().longValue(), ocBOrderItemList, expressCodeList);
        } else {
            log.debug(this.getClass().getName() + "平台发货服务,调用通用平台的发货接口,开始" + "订单id为：" + ocBOrder.getId() + "平台是:" + ocBOrder.getPlatform());
            List<StandplatLogisticsSendDataModel> standplatLogisticsSendDataModels = this.addCommonIpParam(ocBOrderRelation, null, null, null);
            try {
                if (CollectionUtils.isNotEmpty(standplatLogisticsSendDataModels)) {
                    for (StandplatLogisticsSendDataModel model : standplatLogisticsSendDataModels) {
                        log.debug("调用通用发货rpc入参:" + JSONObject.toJSONString(model));
                        ValueHolderV14<List<StandplatLogisticsSendResult>> vh = ipRpcService.sendStandPlatLogistics(model);
                        log.debug("调用通用发货rpc结果:" + JSONObject.toJSONString(vh));
                        if (vh == null || vh.getCode() == ResultCode.FAIL) {
                            return false;
                        }
                    }
                }
                return true;
            } catch (Exception e) {
                log.error("调用通用发货rpc异常", e);
                updateSendDeliveryErrorInfo(ocBOrderRelation, e);
                return false;
            }
        }
        return null;
    }

    private void callVopJitxDeliveryInterface(User user, OcBOrder ocBOrder) {
        log.debug(this.getClass().getName() + "平台发货服务，调用唯品会的发货接口，weiPinHuiPlaformSendGoods==========开始" + "订单id为：" + ocBOrder.getId());
//        ValueHolderV14 vh = weiPinHuiPlaformSendGoods(ocBOrder, user);
        ValueHolderV14 vh = orderPlatformDeliveryService.weiPinHuiPlaformSendGoods(ocBOrder, user, false);
        //ShipResponse
        int retCode = vh.getCode();
        String msg = vh.getMessage();
        //failed_list
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), ocBOrder.getId() + "JITX平台发货接口接收云枢纽返回信息为：" + msg, null, msg, user);
        if (retCode == 0) {
            updateMasterOrderStatusPushES(ocBOrder, user);
        } else {
            JSONObject retData = (JSONObject) vh.getData();
            if (retData != null) {
                JSONArray failArray = retData.getJSONArray("failed_list");
                if (failArray != null && !failArray.isEmpty()) {
                    JSONObject tmpJson = failArray.getJSONObject(0);
                    msg = tmpJson.getString("msg");
                }
            }
            ocBOrderMapper.updateOcBorderInsideRemark(msg, ocBOrder.getId());
            //调用更新失败次数接口
            ocBOrderItemMapper.updateFailTimesByOrderid(ocBOrder.getId());
            //调用主表更新失败次数接口
            ocBOrderMapper.updateMasterFailTimesById(ocBOrder.getId());
            //唯品会调用失败打标记
            OcBOrder orderFlag = new OcBOrder();
            orderFlag.setId(ocBOrder.getId());
            orderFlag.setBillNo(ocBOrder.getBillNo());
            orderFlag.setIsForce(0L);
            orderFlag.setForceSendFailReason(vh.getMessage());
            omsOrderService.updateOrderInfo(ocBOrder);

        }
    }

    private void callNormalPlatformDeliveryInterface(OcBOrderRelation ocBOrderRelation, OcBOrder ocBOrder, Set<String> tidSet) {
        if (Long.valueOf("1").equals(ocBOrder.getIsMultiPack()) && PlatFormEnum.TAOBAO.getCode().equals(ocBOrder.getPlatform())) {
            List<String> expressCodeList = new ArrayList<>();
            List<OcBOrderItem> ocBOrderItemList = omsOrderItemServie.selectUnSuccessRefund(ocBOrder.getId());
            List<OcBOrderDelivery> ocBOrderDeliveryList = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
            for (OcBOrderDelivery ocBOrderDelivery : ocBOrderDeliveryList) {
                String expressCode = ocBOrderDelivery.getLogisticNumber();
                if (expressCodeList.size() > 0) {
                    if (!expressCodeList.contains(expressCode)) {
                        expressCodeList.add(expressCode);
                    }
                } else {
                    expressCodeList.add(expressCode);
                }
            }
            //调用多包裹平台发货方法
            callMultiPackageInterface(ocBOrder, ocBOrderItemList, expressCodeList);
        } else {
            //订单类型=正常，且“是否合并订单”为否，且“是否拆分订单”为否，来源类型非手工新增的订单
            // 0为否，1为是;
            if ((ocBOrder.getOrderType().equals(OrderTypeEnum.NORMAL.getVal()) || ocBOrder.getOrderType().equals(OrderTypeEnum.TYPE_NONE.getVal()))
                    && ocBOrder.getIsMerge() == 0 && (ocBOrder.getIsSplit() == 0 || ocBOrder.getIsSplit() == 2)
                    && !ocBOrder.getOrderSource().equals(OmsOrderSource.MANUAL_ADD.getEcode())) {
                //正常订单平台发货
                log.debug(this.getClass().getName() + "平台发货服务，正常订单平台发货，normalOrderCallInterface==========开始" + "订单id为：" + ocBOrderRelation.getOrderInfo().getId());
                this.normalOrderCallInterface(ocBOrder, tidSet);
            } else if (ocBOrder.getOrderType().equals(OrderTypeEnum.NORMAL.getVal())
                    && ocBOrder.getIsMerge() == 1 && ocBOrder.getIsSplit() == 0
                    && !ocBOrder.getOrderSource().equals(OmsOrderSource.MANUAL_ADD.getEcode())) {
                //合单平台发货
                log.debug(this.getClass().getName() + "平台发货服务，合单发货，normalOrderCallInterface==========开始" + "订单id为：" + ocBOrderRelation.getOrderInfo().getId());
                this.normalOrderCallInterface(ocBOrder, tidSet);
            } else if (ocBOrder.getOrderType().equals(OrderTypeEnum.NORMAL.getVal())
                    && ocBOrder.getIsMerge() == 0 && (ocBOrder.getIsSplit() == 1 || ocBOrder.getIsSplit() == 3)
                    && !ocBOrder.getOrderSource().equals(OmsOrderSource.MANUAL_ADD.getEcode())) {
                //拆单平台发货
                log.debug(this.getClass().getName() + "平台发货服务，拆单平台发货，normalOrderCallInterface==========开始" + "订单id为：" + ocBOrderRelation.getOrderInfo().getId());
                this.isSplitOrderCallInterface(ocBOrder, 0);
            } else if (ocBOrder.getOrderType().equals(OrderTypeEnum.NORMAL.getVal())
                    && ocBOrder.getIsMerge() == 1 && ocBOrder.getIsSplit() == 1
                    && !ocBOrder.getOrderSource().equals(OmsOrderSource.MANUAL_ADD.getEcode())) {
                //既有拆单，又有合单
                log.debug(this.getClass().getName() + "平台发货服务，既有拆单，又有合单发货，isSplitOrderCallInterface==========开始" + "订单id为：" + ocBOrderRelation.getOrderInfo().getId());
                this.isSplitOrderCallInterface(ocBOrder, 1);
            } else if (ocBOrder.getPlatform().equals(PlatFormEnum.TAOBAO.getCode())
                    && ocBOrder.getOrderType().equals(OrderTypeEnum.EXCHANGE.getVal())
                    && ocBOrder.getSuffixInfo().contains("-TC")
                    && !ocBOrder.getOrderSource().equals(OmsOrderSource.MANUAL_ADD.getEcode())) {
                //调用淘宝订单平台换货发货接口
                log.debug(this.getClass().getName() + "平台发货服务，调用淘宝订单平台换货发货接口，tbExchangeCallInterface==========开始" + "订单id为：" + ocBOrderRelation.getOrderInfo().getId());
                tbExchangeCallInterface(ocBOrder);
                //TODO 淘宝换货发货接口未开发完成，待提供

            } else if ((ocBOrder.getOrderType().equals(OrderTypeEnum.NORMAL.getVal()) || ocBOrder.getOrderType().equals(OrderTypeEnum.TYPE_NONE.getVal())
                    || ocBOrder.getOrderType().equals(OrderTypeEnum.LOST.getVal())) && ocBOrder.getOrderSource().equals(OmsOrderSource.MANUAL_ADD.getEcode())) {
                //订单类型为“正常订单”或“预售订单”或“丢单订单”，“订单来源”为手动新增
                List<OcBOrderItem> ocBOrderItemList = omsOrderItemServie.selectUnSuccessRefund(ocBOrder.getId());
                long is_split = 0;//0非拆单，1拆单
                if (ocBOrderItemList.size() > 0) {
                    //如果明细OID存在,正常订单，循环订单明细调用平台发货接口
                    foreachCallInterface(ocBOrder, ocBOrderItemList, is_split);
                } else {
                    //如果明细OID不存在，根据订单主表的平台单号进行调用平台发货接口
                    notExistItemOrder(ocBOrder, is_split);
                }
            } else if (ocBOrder.getOrderType().equals(OrderTypeEnum.LOST.getVal())
                    && ocBOrder.getOrderSource().equals(OmsOrderSource.MANUAL_ADD.getEcode())) {
                //
                List<OcBOrderItem> ocBOrderItemList = omsOrderItemServie.selectUnSuccessRefund(ocBOrder.getId());
                if (ocBOrderItemList.size() > 0) {
                    for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                        //循环调用接口平台的发货接口
                        if (ocBOrder.getPlatform().equals(PlatFormEnum.TAOBAO.getCode())) {
                            String tid = ocBOrderItem.getTid();
                            if (!StringUtils.isEmpty(tid)) {
                                List<IpBTaobaoOrderItem> ipBTaobaoOrderItemList = ipBTaobaoOrderItemMapper.selectTaobaoOrderItemByTid(ocBOrder.getId(), Long.valueOf(tid));
                                if (ipBTaobaoOrderItemList.size() > 0) {
                                    for (IpBTaobaoOrderItem ipBTaobaoOrderItem : ipBTaobaoOrderItemList) {
                                        String status = ipBTaobaoOrderItem.getStatus();
                                        if (!("WAIT_BUYER_CONFIRM_GOODS".equals(status) || "TRADE_BUYER_SIGNED".equals(status)
                                                || "TRADE_FINISHED".equals(status) || "TRADE_CLOSED(".equals(status) || "TRADE_CLOSED_BY_TAOBAO".equals(status))) {
                                            long is_split = 0;//0非拆单，1拆单
                                            //如果明细OID存在,正常订单，循环订单明细调用平台发货接口
                                            List<OcBOrderItem> ocBOrderItemListTmp = new ArrayList<>();
                                            ocBOrderItemListTmp.add(ocBOrderItem);
                                            foreachCallInterface(ocBOrder, ocBOrderItemListTmp, is_split);
                                        }
                                    }
                                }
                            }
                            List<IpBStandplatOrderItem> ipBStandplatOrderItemList = ipBStandplatOrderItemMapper.selectIpBStandplatOrderItemByTid(tid);
                            if (ipBStandplatOrderItemList.size() > 0) {
                                //TODO 中间表中的状态是否为平台发货，状态值待确认
                                //TID对应的订单中间表的状态是否为平台发货，若是，则不调用接口，若否，则调用平台发货接口
                                if (!ipBStandplatOrderItemList.get(0).getStatus().equals(String.valueOf(OmsOrderStatus.PLATFORM_DELIVERY.toInteger()))) {
                                    //调用平台发货接口
                                }
                            }
                            //更新子表“平台发货状态”=已发货（1）
                        }

                    }
                    //更新主表订单状态为平台发货

                } else {
                    //根据订单主表的平台单号进行调用平台发货接口
                }
            } else if (ocBOrder.getOrderType().equals(OrderTypeEnum.EXCHANGE.getVal())
                    && ocBOrder.getOrderSource().equals(OmsOrderSource.MANUAL_ADD.getEcode())) {
                //直接更新表数据为，平台发货
            } else if (ocBOrder.getOrderType().equals(OrderTypeEnum.DIFFPRICE.getVal()) &&
                    ocBOrder.getIsSplit() == 0 &&
                    (PlatFormEnum.JINGDONG.getCode().equals(ocBOrder.getPlatform()) ||
                            PlatFormEnum.TAOBAO.getCode().equals(ocBOrder.getPlatform()) ||
                            PlatFormEnum.TAOBAO_DISTRIBUTION.getCode().equals(ocBOrder.getPlatform())
                    )) {
                //订单类型为'虚拟'，且“是否拆分订单”为'否'，平台为'淘宝，京东，淘宝分销'
                log.debug(this.getClass().getName() + "平台发货服务，虚拟订单平台发货，spreadOrderCallInterface==========开始订单id为：" + ocBOrderRelation.getOrderInfo().getId());
                this.spreadOrderCallInterface(ocBOrder, tidSet);

            } else if (isJinDongExchange(ocBOrder)) {
                if (log.isDebugEnabled()) {
                    log.debug("OmsOrderPlatformDeliveryService.平台发货服务，京东换货订单平台发货，normalOrderCallInterface"
                            + ".开始,订单id-{}, tidSet-{}", ocBOrder.getId(), JSON.toJSONString(tidSet));
                }
                this.normalOrderCallInterface(ocBOrder, tidSet);
            }

        }
    }

    /**
     * 京东换货
     *
     * @param ocBOrder 订单
     * @return 是否京单换货
     */
    private boolean isJinDongExchange(OcBOrder ocBOrder) {
        return OrderTypeEnum.EXCHANGE.getVal().equals(ocBOrder.getOrderType()) && PlatFormEnum.JINGDONG.getCode().equals(ocBOrder.getPlatform());
    }

    /**
     * 查询订单的订单明细中的平台单号(非退款状态)
     *
     * @param ocBOrder 订单对象
     * @return boolean
     */

    public List<String> selectOrderDetailNotRefundTids(OcBOrder ocBOrder) {

        List<String> tidList = new ArrayList<>();
        try {
            tidList = ocBOrderItemMapper.getTidsNoRefundstatus(ocBOrder.getId());
        } catch (Exception ex) {
            log.error("平台发货服务，" + "订单ID为：" + ocBOrder.getId() + "查询订单的订单明细中的平台单号(非退款状态)异常{} " + ex.getMessage(), ex.getMessage());
        }
        return tidList;
    }

    /**
     * 正常订单【非拆单，非合单】调用平台发货接口
     *
     * @param ocBOrder 订单对象
     * @param tidSet   订单对象
     * @return boolean
     */

    public void normalOrderCallInterface(OcBOrder ocBOrder, Set<String> tidSet) {
        log.debug(this.getClass().getName() + "平台发货服务==========调用平台发货接口normalOrderCallInterface" + "订单id为：" + ocBOrder.getId());
        try {
            if (tidSet.size() == 0) {//订单没有明细订单
                long is_split = 0;
                notExistItemOrder(ocBOrder, is_split);
            } else if (tidSet.size() == 1) {
                //订单下的tid全部一致
                tidSet.forEach(str -> {
                    //调用云枢纽发货接口
                    //封装接口参数，并调用陈顺发货平台接口
                    Object[] sub_tid_tmp = tidSet.toArray();
                    StringBuffer sb = new StringBuffer();
                    for (int k = 0; k < sub_tid_tmp.length; k++) {
                        sb.append(sub_tid_tmp[k]);
                    }
                    String sub_tid = sb.toString();
                    long tid = Long.valueOf(str);
                    long is_split = 0;//0非拆单，1拆单
                    String out_sid = ocBOrder.getExpresscode();
                    log.debug(this.getClass().getName() + "平台发货服务，请求云枢纽接口，pakageNormalInterfaceParam==========开始");
                    ValueHolderV14<List<LogisticsSendResult>> vh = packageNormalInterfaceParam(sub_tid, tid, is_split, out_sid, ocBOrder);
                    log.debug(this.getClass().getName() + "平台发货服务，请求云枢纽接口，pakageNormalInterfaceParam==========结束，返回结果为：" + vh.toString());
                    // @20200810 bug#20869 合单处理淘宝换货接口调用问题
                    foreachItem4Exchange(ocBOrder, null);
                });
            } else if (tidSet.size() > 1) {
                //订单下的tid不一致
                List<OcBOrderItem> ocBOrderItemList = omsOrderItemServie.selectUnSuccessRefund(ocBOrder.getId());
                long is_split = 0;//0非拆单，1拆单
                //正常订单，循环订单明细调用平台发货接口
                foreachCallInterface(ocBOrder, ocBOrderItemList, is_split);
            }
        } catch (Exception ex) {
            log.error("平台发货服务，" + "订单ID为：" + ocBOrder.getId() + "正常订单【非拆单，非合单】调用平台发货接口异常{} " + ex.getMessage(), ex.getMessage());
        }

    }

    /**
     * 遍历明细是否需要调用换货接口
     *
     * @param order
     * @param items
     */
    private void foreachItem4Exchange(OcBOrder order, List<OcBOrderItem> items) {
        if (Objects.nonNull(order)) {
            // 指处理淘宝
            if (Objects.nonNull(order.getPlatform()) && PlatFormEnum.TAOBAO.getCode().equals(order.getPlatform())) {
                // 查明细
                if (CollectionUtils.isEmpty(items)) {
                    // 按主表ID查询
                    items = omsOrderItemServie.selectUnSuccessRefund(order.getId());
                }
                if (CollectionUtils.isNotEmpty(items)) {
                    String outSid = order.getExpresscode();
                    // 遍历是否换货单
                    items.forEach(item -> {
                        // 如果是换货，则需要处理：调用淘宝换货接口
                        if (Objects.nonNull(item)) {
                            if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsExchangeItem())) {
                                if (Objects.nonNull(item.getOoid()) && StringUtils.isNumeric(item.getOoid())) {
                                    tamallExchangeSend(Long.valueOf(item.getOoid()), outSid, order);
                                }
                            }
                        }
                    });
                }
            }
        }
    }

    public void isSplitOrderCallInterface(OcBOrder ocBOrder, int splitAndMergeType) {
        //订单下的tid不一致
        List<OcBOrderItem> ocBOrderItemList = omsOrderItemServie.selectUnSuccessRefund(ocBOrder.getId());
        if (!ObjectUtils.isEmpty(ocBOrder.getSourceCode())) {
            String[] sourceCodes = ocBOrder.getSourceCode().split(",");
            for (String sourceCode : sourceCodes) {
                String oid = "";
                long tid = Long.valueOf(sourceCode);
                String out_sid = ocBOrder.getExpresscode();
                long is_split = 1;//0非拆单，1拆单
                oid = buildDistinctOrderItemOid(ocBOrderItemList, oid, sourceCode);
                ValueHolderV14 vh = packageNormalInterfaceParam(oid, tid, is_split, out_sid, ocBOrder);
                log.debug(this.getClass().getName() + "平台发货服务，请求云枢纽接口，isSplitOrderCallInterface==========请求已成功发出，返回结果为：" + vh.toString());
            }
        }
    }

    public void tbExchangeCallInterface(OcBOrder ocBOrder) {
        List<OcBOrderItem> ocBOrderItemList = omsOrderItemServie.selectUnSuccessRefund(ocBOrder.getId());
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            //封装接口参数，并调用陈顺淘宝换货发货接口
            String oid = "";
            if (StringUtils.isNotEmpty(ocBOrderItem.getOoid())) {
                oid = String.valueOf(ocBOrderItem.getOoid());
                String out_sid = ocBOrder.getExpresscode();
                //把oid作为tid发货
                ValueHolderV14 vh = tamallExchangeSend(Long.valueOf(oid), out_sid, ocBOrder);
                log.debug(this.getClass().getName() + "天猫换货平台发货接口，请求云枢纽，返回信息为：" + vh.toString());
            } else {
                log.debug(this.getClass().getName() + "订单id为：" + ocBOrder.getId() + "，oid为空，不调用平台发货接口" + "ocBOrderItemid为：" + ocBOrderItem.getId());
            }
        }
    }

    public void foreachCallInterface(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList, long is_split) {
        // boolean masterTableUpdateTmpFlag = true;
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            //循环调用接口平台的发货接口
            //封装接口参数，并调用陈顺发货平台接口
            String sub_tid = "";
            if (ocBOrderItem.getOoid() != null) {
                sub_tid = ocBOrderItem.getOoid();
            }
            long tid = Long.valueOf(ocBOrderItem.getTid());
            //long is_split = 0;//0非拆单，1拆单
            String out_sid = ocBOrder.getExpresscode();

            // @20200810 bug#20869 合单处理淘宝换货接口调用问题
            if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(ocBOrderItem.getIsExchangeItem())) {
                String oid = ocBOrderItem.getOoid();

                if (Objects.nonNull(oid) && StringUtils.isNumeric(oid)) {
                    tamallExchangeSend(Long.valueOf(oid), out_sid, ocBOrder);
                } else {
                    log.debug("ooid.taobao.has.error:{}/{}", oid, ocBOrder.getId());
                }
            } else {
                packageNormalInterfaceParam(sub_tid, tid, is_split, out_sid, ocBOrder);
            }
        }
    }

    public void callMultiPackageInterface(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList,
                                          List<String> expressCodeList) {
        long tid = Long.valueOf(ocBOrder.getTid());
        /**和曾志军沟通后多包裹平台发货,is_split = 1L*/
        long isSplitPackage = 1L;
        // 包裹数、订单明细数 最小值进行依次上传。
        if (ocBOrderItemList.size() >= expressCodeList.size()) {
            // 按照包裹数进行处理；每次处理一个订单明细；余下的订单明细，则统一生成新的Oid进行处理，有可能是多单合并成新单的数据；
            for (int i = 0; i < expressCodeList.size(); i++) {
                //发完一波,休息2秒
                try {
                    Thread.sleep(2000);
                } catch (Exception e) {
                }
                String expressCode = expressCodeList.get(i);
                if (i + 1 == expressCodeList.size()) {
                    String newOid = "";
                    if (ocBOrderItemList.size() > 0) {
                        newOid = buildDistinctOrderItemOid(ocBOrderItemList, newOid);
                        log.debug(this.getClass().getName() + "多包裹平台发货接口line547，请求云枢纽，请求信息，订单id = " + ocBOrder.getId() + "oid = " + newOid + "is_split = " + isSplitPackage + "expressCode = " + expressCode);
                        ValueHolderV14<List<LogisticsSendResult>> vh = packageNormalInterfaceParam(newOid, tid, isSplitPackage, expressCode, ocBOrder);
                        log.debug(this.getClass().getName() + "多包裹平台发货接口，请求云枢纽，返回信息为：" + vh.toString());
                    }
                } else {
                    OcBOrderItem ocBOrderItem = ocBOrderItemList.get(0);
                    String oid = "";
                    oid = ocBOrderItem.getOoid();
                    log.debug(this.getClass().getName() + "多包裹平台发货接口line556，请求云枢纽，请求信息，订单id = " + ocBOrder.getId() + "oid = " + oid + "is_split = " + isSplitPackage + "expressCode = " + expressCode);
                    ValueHolderV14<List<LogisticsSendResult>> vh = packageNormalInterfaceParam(oid, tid, isSplitPackage, expressCode, ocBOrder);
                    ocBOrderItemList.remove(ocBOrderItem);
                    log.debug(this.getClass().getName() + "多包裹平台发货接口，请求云枢纽，返回信息为：" + vh.toString());
                }
            }
        } else {
            for (int j = 0; j < ocBOrderItemList.size(); j++) {
                try {
                    Thread.sleep(2000);
                } catch (Exception e) {
                }
                String oid = ocBOrderItemList.get(j).getOoid();
                String expressCode = expressCodeList.get(j);
                log.debug(this.getClass().getName() + "多包裹平台发货接口line565，请求云枢纽，请求信息，订单id = " + ocBOrder.getId() + "oid = " + oid + "is_split = " + isSplitPackage + "expressCode = " + expressCode);
                ValueHolderV14<List<LogisticsSendResult>> vh = packageNormalInterfaceParam(oid, tid, isSplitPackage, expressCode, ocBOrder);
                log.debug(this.getClass().getName() + "多包裹平台发货接口，请求云枢纽，返回信息为：" + vh.toString());
            }
        }
    }

    /**
     * 淘宝合单发货oid根据tid解析oid
     *
     * @param ocBOrderItemList
     * @param newOid
     * @param sourceCode
     * @return
     */
    private String buildDistinctOrderItemOid(List<OcBOrderItem> ocBOrderItemList, String newOid, String sourceCode) {
        List<String> tmpList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            if (ocBOrderItem.getIsGift() == 1) {
                continue;
            }
            if (!tmpList.contains(ocBOrderItem.getOoid()) && StringUtils.equals(ocBOrderItem.getTid(), sourceCode)) {
                tmpList.add(ocBOrderItem.getOoid());
            }
        }
        if (CollectionUtils.isNotEmpty(tmpList)) {
            newOid = StringUtils.join(tmpList, ",");
        }
        return newOid;
    }

    /***
     * 按照订单明细，拼接生成新的Oid
     * @param ocBOrderItemList 订单明细
     * @param newOid
     * @return
     */
    private String buildDistinctOrderItemOid(List<OcBOrderItem> ocBOrderItemList, String newOid) {
        List<String> tmpList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            if (ocBOrderItem.getIsGift() == 1) {
                continue;
            }
            if (!tmpList.contains(ocBOrderItem.getOoid())) {
                tmpList.add(ocBOrderItem.getOoid());
            }
        }
        if (CollectionUtils.isNotEmpty(tmpList)) {
            newOid = StringUtils.join(tmpList, ",");
        }
        return newOid;
    }

    public void notExistItemOrder(OcBOrder ocBOrder, long is_split) {
        long tid = Long.valueOf(ocBOrder.getTid());
        String out_sid = ocBOrder.getExpresscode();
        packageNormalInterfaceParam(null, tid, is_split, out_sid, ocBOrder);
    }

    /**
     * 封装调用陈顺平台发货接口
     *
     * @param subTid    子订单集合
     * @param tid       淘宝交易ID【平台单号】
     * @param isSplit   是否拆单 0非拆单，1拆单
     * @param outSid    运单号
     * @param orderInfo 订单实体
     * @return
     */
    public ValueHolderV14<List<LogisticsSendResult>> packageNormalInterfaceParam(String subTid, long tid,
                                                                                 long isSplit,
                                                                                 String outSid,
                                                                                 OcBOrder orderInfo) {
        //vh.setCode(0);
        Long plaform = Long.valueOf(orderInfo.getPlatform());
        //物流公司代码
        String logisticsCompanyCode = "";
        String sellerNick = orderInfo.getCpCShopSellerNick();
        LogisticsSendModel tbLogisticsModel = new LogisticsSendModel();
        tbLogisticsModel.setId(orderInfo.getId());
        tbLogisticsModel.setTid(tid);
        tbLogisticsModel.setPlatform(plaform);
        tbLogisticsModel.setSellerNick(sellerNick);

        tbLogisticsModel.setIsSplit(isSplit);
        tbLogisticsModel.setOutSid(outSid);
        if (log.isDebugEnabled()) {
            log.debug("OmsOrderPlatformDeliveryService.packageNormalInterfaceParam.Before.OrderId={};LogisticsCompanyCode={}",
                    orderInfo.getId(), logisticsCompanyCode);
        }
        logisticsCompanyCode = cpRpcService.getPlatformLogisticEcode(orderInfo.getCpCLogisticsId(), Long.valueOf(orderInfo.getPlatform()));
        if (log.isDebugEnabled()) {
            log.debug("OmsOrderPlatformDeliveryService.packageNormalInterfaceParam.After.OrderId={};LogisticsCompanyCode={}",
                    orderInfo.getId(), logisticsCompanyCode);
        }
        if (orderInfo.getPlatform().equals(PlatFormEnum.TAOBAO.getCode()) || orderInfo.getPlatform().equals(PlatFormEnum.TAOBAO_DEAL.getCode())
                || orderInfo.getPlatform().equals(PlatFormEnum.TAOBAO_DISTRIBUTION.getCode())) {
            if (StringUtils.isEmpty(logisticsCompanyCode)) {
                if (log.isDebugEnabled()) {
                    log.debug("OmsOrderPlatformDeliveryService.packageNormalInterfaceParam.Use.Order.LogisticsCode.OrderId={}",
                            orderInfo.getId());
                }
                logisticsCompanyCode = orderInfo.getCpCLogisticsEcode();
            }
        } else if (orderInfo.getPlatform().equals(PlatFormEnum.JINGDONG.getCode())) {
            //TODO 平台为京东时，产品说店铺类型先写死“SOP”
            tbLogisticsModel.setShoptype("SOP");
        }
        tbLogisticsModel.setCompanyCode(logisticsCompanyCode);
        tbLogisticsModel.setLogisticsCompanyName(orderInfo.getCpCLogisticsEname());
        tbLogisticsModel.setSubTid(subTid);
        int orderTypeTmp = orderInfo.getOrderType();
        String suffixInfo = orderInfo.getSuffixInfo();
        long orderType = 0L;
        if (orderTypeTmp == 1) {
            orderType = 0L;
        } else if (orderTypeTmp == 2 && suffixInfo != null && suffixInfo.contains("TC")) {
            orderType = 1L;
            tbLogisticsModel.setDisputeId(Long.valueOf(orderInfo.getTid()));
            tbLogisticsModel.setLogisticsType(200L);
        }
        tbLogisticsModel.setOrdertype(orderType);
        tbLogisticsModel.setPaytype(Long.valueOf(orderInfo.getPayType()));
        return ipRpcService.sendAndExchangeGoods(tbLogisticsModel, plaform);
    }

    public ValueHolderV14 tamallExchangeSend(long oid, String out_sid, OcBOrder ocBOrder) {
        ValueHolderV14 vh;
        Long plaform = Long.valueOf(ocBOrder.getPlatform());
        LogisticsSendModel tbLogisticsModel = new LogisticsSendModel();
        int orderTypetmp = ocBOrder.getOrderType();
        Long orderType = 0L;
        if (orderTypetmp == 1) {
            orderType = 0L;
        } else if (orderTypetmp == 2 && ocBOrder.getSuffixInfo().contains("TC")) {
            orderType = 1L;
            tbLogisticsModel.setDisputeId(oid);
            tbLogisticsModel.setLogisticsType(200L);
        }
        tbLogisticsModel.setId(ocBOrder.getId());
        tbLogisticsModel.setTid(oid);
        tbLogisticsModel.setOrdertype(orderType);
        tbLogisticsModel.setOutSid(out_sid);
        tbLogisticsModel.setLogisticsCompanyName(ocBOrder.getCpCLogisticsEname());
        tbLogisticsModel.setPlatform(plaform);
        tbLogisticsModel.setSellerNick(ocBOrder.getCpCShopSellerNick());

        /**id,tid,platform,sellerNick,shoptype,isSplit,outSid,companyCode,disputeId,
         logisticsType,logisticsCompanyName,subTid,ordertype,paytype 接口所需参数*/
        log.debug("pakageNormalInterfaceParam.RequestParam---->tbLogisticsModel = " + tbLogisticsModel + "plaform ");
        vh = ipRpcService.sendAndExchangeGoods(tbLogisticsModel, plaform);
        return vh;
    }

    /**
     * 封装调用陈顺唯品会平台发货接口
     *
     * @param ocBOrder 订单对象
     * @param user     user对象
     * @return
     */
    @Deprecated
    public ValueHolderV14 weiPinHuiPlaformSendGoods(OcBOrder ocBOrder, User user) {

        ValueHolderV14 vh;
        VipJitxOrderShipModel vipJitxOrderShipModel = new VipJitxOrderShipModel();
        List<VipJitxOrderShipModel.Ship> shipList = new ArrayList();
        VipJitxOrderShipModel.Ship ship = new VipJitxOrderShipModel.Ship();
        List<VipJitxOrderShipModel.Ship.Package> pakageList = new ArrayList();
        VipJitxOrderShipModel.Ship.Package pakage = new VipJitxOrderShipModel.Ship.Package();
        List<VipJitxOrderShipModel.Ship.Package.PackageDetail> packageDetailList = new ArrayList();
        //获取订单的出库时间，【实际打包时间】
        Long oqc_date = ocBOrder.getScanTime().getTime() / 1000;
        log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，获取的实际打包时间为：" + oqc_date + "订单id为：" + ocBOrder.getId());
        //根据订单id查询订单所对应的发货信息列表
        List<OcBOrderDelivery> ocBOrderDeliveryList = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
        log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，selectOrderDeliveryByExpressCode结果为：" + ocBOrderDeliveryList + "订单id为：" + ocBOrder.getId());
        Map<String, Map<String, String>> expressCodeMap = new HashMap<>();
        Map<String, String> skuMap = new HashMap<>();
        for (OcBOrderDelivery orderDelivery : ocBOrderDeliveryList) {
            String num = String.valueOf(orderDelivery.getQty());
            num = StringUtils.substringBefore(num, ".");
            skuMap.put(orderDelivery.getGbcode(), num);
            expressCodeMap.put(orderDelivery.getLogisticNumber(), skuMap);
        }
        log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，skuMap的值为：" + skuMap + "订单id为：" + ocBOrder.getId());
        log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，expressCodeMap的值为：" + expressCodeMap + "订单id为：" + ocBOrder.getId());
        List<String> expressCodeList = new ArrayList<>();
        Map<String, List<String[]>> newExpressCodeMap = new HashMap<>();
        for (String key : expressCodeMap.keySet()) {
            String expressCode = key;
            Map<String, String> gbcodeMapTmp = new HashMap<>();
            gbcodeMapTmp = expressCodeMap.get(key);
            List<String[]> allGbcodeList = new ArrayList<>();
            for (String gbCode : gbcodeMapTmp.keySet()) {
                String[] skuArry = new String[2];
                skuArry[0] = gbCode;
                skuArry[1] = gbcodeMapTmp.get(gbCode);
                log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，line770，skuArry的值为：" + skuArry + "订单id为：" + ocBOrder.getId());
                allGbcodeList.add(skuArry);
            }
            log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，line773，allGbcodeList的值为：" + allGbcodeList + "订单id为：" + ocBOrder.getId());
            newExpressCodeMap.put(expressCode, allGbcodeList);
            expressCodeList.add(expressCode);
        }
        log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，newExpressCodeMap的值为：" + newExpressCodeMap + "订单id为：" + ocBOrder.getId());
        log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，expressCodeList的值为：" + expressCodeList + "订单id为：" + ocBOrder.getId());
        for (int j = 0; j < expressCodeList.size(); j++) {
            List<String[]> allGbcodeListTmp = newExpressCodeMap.get(expressCodeList.get(j));
            log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，allSkuListTmp的值为：" + allGbcodeListTmp + "订单id为：" + ocBOrder.getId());
            for (String[] skuArry : allGbcodeListTmp) {
                VipJitxOrderShipModel.Ship.Package.PackageDetail packageDetail = new VipJitxOrderShipModel.Ship.Package.PackageDetail();
                packageDetail.setBarcode(skuArry[0]);
                packageDetail.setQuantity(Integer.valueOf(skuArry[1]));
                packageDetailList.add(packageDetail);
            }
            log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，packageDetailList的值为：" + packageDetailList + "订单id为：" + ocBOrder.getId());
            pakage.setBox_no(j + 1);
            pakage.setOqc_date(oqc_date);
            pakage.setPackage_no(expressCodeList.get(j));
            log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，Package_no的值为：" + expressCodeList.get(j));
            //pakage.setPackage_no(String.valueOf(j+1));
            pakage.setTransport_no(expressCodeList.get(j));
            pakage.setDetails(packageDetailList);
            log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，pakage的值为：" + packageDetailList + "订单id为：" + ocBOrder.getId());
            pakageList.add(pakage);
        }
        //陈秀楼确认后，Order_sn取值修改为订单信息的SourceCode
        ship.setOrder_sn(String.valueOf(ocBOrder.getSourceCode()));
        ship.setTotal_package(ocBOrderDeliveryList.size());
        log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，pakageList的值为：" + pakageList + "订单id为：" + ocBOrder.getId());
        ship.setPackages(pakageList);
        //根据实体仓id
        Long wareHouseId = ocBOrder.getCpCPhyWarehouseId();
        //CpCPhyWarehouse cpCPhyWarehouse = cpRpcExtService.queryByWarehouseId(wareHouseId);
        StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseService.queryJitxCapacity(ocBOrder.getCpCShopId(),
                wareHouseId, null);
        log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，调用jitxWarehouseService.queryJitxCapacity返回的值为：" + jitxWarehouse + "订单id为：" + ocBOrder.getId());
        if (jitxWarehouse != null) {
            String jitWarehouseEcode = jitxWarehouse.getVipcomWarehouseEcode();
            if (!YesNoEnum.Y.getVal().equals(ocBOrder.getIsStoreDelivery())) {
                jitWarehouseEcode = jitxWarehouse.getVipcomUnshopWarehouseEcode();
            }
            ship.setDelivery_warehouse(jitWarehouseEcode);
        }
        shipList.add(ship);
        vipJitxOrderShipModel.setOperateUser(user);
        vipJitxOrderShipModel.setShips(shipList);
        vipJitxOrderShipModel.setSellerNick(ocBOrder.getCpCShopSellerNick());
        //取经销商id
        //1.根据店铺id,查询店铺信息
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        String vendorId = cpShop.getPlatformSupplierId();
        log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，vendorId为 ：" + vendorId + "订单id为：" + ocBOrder.getId());
        vipJitxOrderShipModel.setVendorId(vendorId);
        log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，调用ipRpcService.weiPinHuiSendGoods请求参数为：" + vipJitxOrderShipModel + "订单id为：" + ocBOrder.getId());
        vh = ipRpcService.weiPinHuiSendGoods(vipJitxOrderShipModel);
        log.debug(this.getClass().getName() + "平台发货服务,weiPinHuiPlaformSendGoods方法内，调用ipRpcService.weiPinHuiSendGoods返回信息为：" + vh + "订单id为：" + ocBOrder.getId());
        return vh;
    }

    /**
     * 猫超仓库发货调用云枢纽接口
     *
     * @param ocBOrder 订单对象
     * @param user     user对象
     * @return
     */
    public ValueHolderV14 alibabaAscpShippingBack(OcBOrder ocBOrder, User user) {

        ConsignOrderShipModel consignOrderShipModel = new ConsignOrderShipModel();

        OcBOrderRelation ocBOrderRelation = omsOrderService.selectOmsOrderInfo(ocBOrder.getId());
        List<OcBOrderItem> ocBOrderItemList = ocBOrderRelation.getOrderItemList();

        IpAlibabaAscpOrderRelation ipAlibabaAscpOrderRelation = ipAlibabaAscpOrderService.selectAlibabaAscpOrder(ocBOrder.getTid());
        IpBAlibabaAscpOrder alibabaAscpOrder = ipAlibabaAscpOrderRelation.getAlibabaAscpOrder();

        consignOrderShipModel.setWholeSheetConsigned(0 == ocBOrder.getIsMultiPack());

        //逻辑仓信息
        consignOrderShipModel.setStoreCode(alibabaAscpOrder.getStoreCode());
//        consignOrderShipModel.setStoreName(ocBOrder.getCpCStoreEname());
        consignOrderShipModel.setBizOrderCode(ocBOrder.getSourceCode());
        consignOrderShipModel.setOutBizId(ocBOrder.getBillNo());

        //根据店铺id,查询店铺信息
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        String supplierId = cpShop.getPlatformSupplierId();
        consignOrderShipModel.setSupplierId(supplierId);

        List<ConsignOrderShipModel.Orderitems> orderitemsList = new ArrayList();

        ocBOrderItemList.stream().forEach(item -> {
            ConsignOrderShipModel.Orderitems orderitems = new ConsignOrderShipModel.Orderitems();
            orderitems.setScItemId(item.getSkuNumiid());
            orderitems.setItemQuantity(item.getQty().longValue());
            orderitems.setLackQuantity(0L);
            orderitems.setSubOrderCode(item.getOoid());
            orderitemsList.add(orderitems);
        });

        consignOrderShipModel.setOrderItems(orderitemsList);

        //发货信息
        ConsignOrderShipModel.Senderinfo senderinfo = new ConsignOrderShipModel.Senderinfo();
        senderinfo.setSenderName(cpShop.getSellerName());
        senderinfo.setSenderZipCode(cpShop.getSellerZip());
        senderinfo.setSenderCountry(StringUtils.isBlank(alibabaAscpOrder.getSenderCountry()) ? "中国" : alibabaAscpOrder.getSenderCountry());
        senderinfo.setSenderProvince(cpShop.getSellerProvince());
        senderinfo.setSenderCity(cpShop.getSellerCity());
        senderinfo.setSenderArea(cpShop.getSellerArea());
        senderinfo.setSenderTown("无");
        senderinfo.setSenderPhone(cpShop.getSellerPhone());
        senderinfo.setSenderMobile(cpShop.getSellerPhone());
        senderinfo.setSenderAddress(cpShop.getSellerAddress());
        consignOrderShipModel.setSenderInfo(senderinfo);

        //包裹列表
        List<ConsignOrderShipModel.Tmsorders> tmsordersList = new ArrayList<>();
        ConsignOrderShipModel.Tmsorders tmsorders = new ConsignOrderShipModel.Tmsorders();
        tmsorders.setTmsServiceName(ocBOrder.getCpCLogisticsEname());
        tmsorders.setTmsServiceCode(ocBOrder.getCpCLogisticsEcode());
        tmsorders.setTmsOrderCode(ocBOrder.getExpresscode());
        tmsordersList.add(tmsorders);

        //包裹明细列表
        List<ConsignOrderShipModel.Tmsitems> tmsItems = new ArrayList<>();

        ocBOrderItemList.stream().forEach(item -> {
            ConsignOrderShipModel.Tmsitems im = new ConsignOrderShipModel.Tmsitems();
            im.setScItemId(item.getSkuNumiid());
            im.setItemQuantity(item.getQty().longValue());
            im.setLackQuantity(0L);
            im.setSubOrderCode(item.getOoid());
            tmsItems.add(im);
        });

        tmsorders.setTmsItems(tmsItems);
        consignOrderShipModel.setTmsOrders(tmsordersList);
        //猫超仓库发货（同步）
        ValueHolderV14 vh = ipRpcService.alibabaAscpShippingBack(consignOrderShipModel, ocBOrder.getCpCShopSellerNick());
        return vh;
    }

    /**
     * 检查订单状态是否为"仓库发货" 或 "平台发货"
     *
     * @param ocBOrder 订单实体
     * @return boolean
     */

    public boolean checkBillStatus(OcBOrder ocBOrder) {

        boolean flag;
        Integer orderStatus = ocBOrder.getOrderStatus();
        flag = OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus) || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus);
        return flag;
    }

    /**
     * 猫超仓库发货调用云枢纽成功
     * 更新主表订单状态,推ES
     *
     * @param ocBOrder 订单信息
     * @return boolean
     */

    public boolean updatePlaformDeliveryOrderStatusPushES(OcBOrder ocBOrder, User user) {
        if (log.isDebugEnabled()) {
            log.debug("猫超仓库发货调用云枢纽成功更新主表并推es入参:{}", JSONObject.toJSONString(ocBOrder));
        }
        boolean flag = false;
        try {
            OcBOrder ocBorderDto = null;
            ocBorderDto = omsOrderService.selectOrderInfo(ocBOrder.getId());
            ocBorderDto.setIsForce(1L);
            ocBorderDto.setForceSendFailReason("仓库发货调用云枢纽成功");
            Long orderId = ocBOrder.getId();
            try {
                //调用贺柳公共更新订单主表方法【包含推送到ES】
                if (log.isDebugEnabled()) {
                    log.debug("猫超仓库发货调用云枢纽修改状态入参{}", JSONObject.toJSONString(ocBorderDto));
                }
                flag = omsOrderService.updateOrderInfo(ocBorderDto);
                if (!flag) {
                    log.error("ES更新主表订单状态失败--> 订单ID{}", orderId);
                }
            } catch (Exception e) {
                log.error("ES更新订单状态失败", e);
            }
        } catch (Exception ex) {
            flag = false;
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                    ocBOrder.getBillNo(), OrderLogTypeEnum.WAREHOUSE_DELIVERY_CALL_INTERFACE.getKey(), ocBOrder.getId() + "仓库发货调用云枢纽-更新主表订单状态,推ES时异常" + ex.getMessage(), null, ex.getMessage(), user);
            log.error("仓库发货调用云枢纽-更新主表订单状态,推ES时异常{} ", ex);
        }
        return flag;
    }

    /**
     * 更新主表订单状态,推ES
     * jitx和拼多多和苏宁调用时如果是退款中作废退单 ljp add
     *
     * @param ocBOrder 订单信息
     * @return boolean
     */

    public boolean updateMasterOrderStatusPushES(OcBOrder ocBOrder, User user) {
        log.debug("平台发货更新主表并推es入参:" + JSONObject.toJSONString(ocBOrder));
        boolean flag = false;
        try {
            OcBOrder ocBorderDto = null;
            if (ocBOrder.getIsInreturning().equals(InreturningStatus.INRETURNING)) {
                try {
                    log.debug("平台发货更新主表并推es入参开始作废退单入参" + ocBOrder);
                    this.updateReturnOrder(ocBOrder);//作废退单并推es
                } catch (Exception e) {
                    log.error("平台发货更新主表并推es入参开始作废退单异常", e);
                }
            }
            ocBorderDto = omsOrderService.selectOrderInfo(ocBOrder.getId());
            ocBorderDto.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            ocBorderDto.setIsForce(1L);
            //ocBorderDto.setForceSendFailReason("平台发货成功");//平台发货成功后失败信息情况
            Long orderId = ocBOrder.getId();
            try {
                //平台发货时间赋值
                ocBorderDto.setPlatformDeliveryTime(new Date());
                ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.PLATFORM_DELIVERY_TIME,new Date(),ocBorderDto.getId(),SystemUserResource.getRootUser());
                //调用贺柳公共更新订单主表方法【包含推送到ES】
                log.debug("平台发货修改状态入参" + JSONObject.toJSONString(ocBorderDto));
                flag = omsOrderService.updateOrderInfo(ocBorderDto);
                if (!flag) {
                    log.error("ES更新主表订单状态失败--> 订单ID" + orderId);
                }
            } catch (Exception e) {
                log.error("ES更新订单状态失败" + e.getMessage());
            }
        } catch (Exception ex) {
            flag = false;
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                    ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), ocBOrder.getId() + "平台发货服务-更新主表订单状态,推ES时异常" + ex.getMessage(), null, ex.getMessage(), user);
            log.error("平台发货服务-更新主表订单状态,推ES时异常{} ", ex.getMessage());
        }
        return flag;
    }

    /**
     * @param ocBOrderRelation
     * @param subTid
     * @param isSplit
     * @param expressCode
     * @return List
     * <AUTHOR>
     * @Description 组装通用发货平台参数
     * @Date 16:40 2020/7/16
     **/
    private List<StandplatLogisticsSendDataModel> addCommonIpParam(OcBOrderRelation ocBOrderRelation, String subTid,
                                                                   Integer isSplit, String expressCode) {
        log.debug("通用平台发货调用入参:" + JSONObject.toJSONString(ocBOrderRelation));
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        List<StandplatLogisticsSendDataModel> result = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = null;
        if (StringUtils.isEmpty(expressCode)) {
            ocBOrderItems = omsOrderItemServie.selectUnSuccessRefund(orderInfo.getId());
        } else {
            ocBOrderItems = ocBOrderRelation.getOrderItemList();
        }
        //订单明细信息，需要排除赠品和退款完成的明细
        if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
            ocBOrderItems = ocBOrderItems.stream()
                    .filter(obj -> null != obj &&
                            (obj.getIsGift() == null ? 0 : obj.getIsGift()) != 1 &&
                            (obj.getRefundStatus() == null ? 0 : obj.getRefundStatus()) != 6
                    ).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
            Map<String, List<OcBOrderItem>> lists = ocBOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getTid));
            for (Map.Entry<String, List<OcBOrderItem>> map : lists.entrySet()) {
                String tid = map.getKey();
                List<OcBOrderItem> orderItems = map.getValue();
                if (CollectionUtils.isNotEmpty(orderItems)) {
                    //如果存在福袋、组合商品将真实商品明细转换为平台的虚拟明细
                    Long id = orderInfo.getId();
                    orderItems = transformCommonIpItemParam(orderItems, id);
                    StandplatLogisticsSendDataModel model = getStandplatLogisticsSendDataModel(ocBOrderRelation, subTid,
                            isSplit, expressCode, orderItems);
                    if (model != null) {
                        result.add(model);
                    }
                }
            }
        }
        log.debug("组装通用发货出参:" + JSONObject.toJSONString(result));
        return result;
    }

    /**
     * @param orderItems
     * @param id
     * @return java.util.List<com.jackrain.nea.oc.oms.model.table.OcBOrderItem>
     * <AUTHOR>
     * @Description 如果存在福袋、组合商品将真实商品明细转换为平台的虚拟明细
     * @Date 16:05 2020/8/26
     **/
    private List<OcBOrderItem> transformCommonIpItemParam(List<OcBOrderItem> orderItems, Long id) {
        List<OcBOrderItem> newOrderItems = new ArrayList<>();
        List<OcBOrderItem> transformOrderItems = new ArrayList<>();
        for (OcBOrderItem item : orderItems) {
            Long proType = item.getProType();
            if (proType != null && (proType.intValue() == SkuType.COMBINE_PRODUCT || proType.intValue() == SkuType.GIFT_PRODUCT)) {
                transformOrderItems.add(item);
            } else {
                newOrderItems.add(item);
            }
        }
        if (CollectionUtils.isEmpty(transformOrderItems)) {
            //需要转换的明细为空时，直接返回原明细
            return orderItems;
        }
        List<String> ooidList = transformOrderItems.stream()
                .filter(obj -> StringUtils.isNotBlank(obj.getOoid()))
                .map(obj -> obj.getOoid()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ooidList)) {
            //ooid为空时返回过滤后的明细列表
            return newOrderItems;
        }
        List<OcBOrderItem> specialItems = ocBOrderItemMapper.selectList(new LambdaQueryWrapper<OcBOrderItem>()
                .eq(OcBOrderItem::getIsactive, OcBOrderConst.IS_ACTIVE_YES)
                .eq(OcBOrderItem::getOcBOrderId, id)
                .eq(OcBOrderItem::getProType, Long.valueOf(SkuType.NO_SPLIT_COMBINE))
                .in(OcBOrderItem::getOoid, ooidList));
        if (CollectionUtils.isNotEmpty(specialItems)) {
            newOrderItems.addAll(specialItems);
        }
        return newOrderItems;
    }


    /**
     * @param ocBOrderRelation
     * @param subTid
     * @param isSplit
     * @param expressCode
     * @param orderItems
     * @return com.jackrain.nea.ip.model.others.StandplatLogisticsSendDataModel
     * <AUTHOR>
     * @Description 组装单个通用发货平台参数
     * @Date 22:07 2020/7/22
     **/
    private StandplatLogisticsSendDataModel getStandplatLogisticsSendDataModel(OcBOrderRelation ocBOrderRelation, String subTid,
                                                                               Integer isSplit, String expressCode, List<OcBOrderItem> orderItems) {
        // @20200830 爱库存平台发货需求TID取值逻辑（要查询中间表的）
        String itemTid = null;

        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OcBOrderItem item : orderItems) {
                if (Objects.nonNull(item) && Objects.nonNull(item.getTid())) {
                    itemTid = item.getTid();
                    break;
                }
            }
        }

        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        //查询订单明细
        QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper();
        queryWrapper.in("ISACTIVE", "Y");
        queryWrapper.eq("OC_B_ORDER_ID", ocBOrder.getId());
        BigDecimal skuNum = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OcBOrderItem ocBOrderItem : orderItems) {
                if (ocBOrderItem.getQty() != null) {
                    skuNum = skuNum.add(ocBOrderItem.getQty());
                }
            }
        }
        StandplatLogisticsSendDataModel standplatLogisticsSendDataModel = new StandplatLogisticsSendDataModel();
        StandplatLogisticsSendModel sendModel = new StandplatLogisticsSendModel();
        String company_code = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
        Map<Long, String> companyCodeMap = new HashMap<>();
        companyCodeMap.put(ocBOrder.getCpCLogisticsId(), company_code);
        log.debug("调用物流公司编码查询服务==============结束：结束后，物流编码为" + company_code);
        //mq回执返回过来用来订单ID
        sendModel.setErpOrderId(ocBOrder.getId());
        if (null != isSplit) {
            sendModel.setIsSplit(isSplit.longValue());
        } else {
            sendModel.setIsSplit(ocBOrder.getIsSplit() == null ? null : ocBOrder.getIsSplit().longValue());
        }
        //不拆单 传null
        sendModel.setSubTid(subTid);
        if (StringUtils.isEmpty(expressCode)) {
            sendModel.setOutSid(ocBOrder.getExpresscode());
        } else {
            sendModel.setOutSid(expressCode);
        }
        sendModel.setOutSid(ocBOrder.getExpresscode());
        //平台物流公司编码
        sendModel.setCompanyCode(company_code);
        sendModel.setPayType(ocBOrder.getPayType() == null ? "" : ocBOrder.getPayType().toString());
        sendModel.setPlatform(ocBOrder.getPlatform() == null ? null : ocBOrder.getPlatform().longValue());
        sendModel.setSellerNick(ocBOrder.getCpCShopSellerNick());
        //换货单号 暂时没有
        sendModel.setDisputeId(null);
        sendModel.setOrderType(ocBOrder.getOrderType() == null ? "" : ocBOrder.getOrderType().toString());
        sendModel.setLogisticsCompanyName(ocBOrder.getCpCLogisticsEname());
        //订单来源 先传空字符串，后面设计为可配置
        sendModel.setRetailSource("");
        //sku数量
        sendModel.setSize(skuNum.toString());
        //运单包裹状态(1发货完成，2部分发货， 取固定值)
        sendModel.setStatus("1");
        //物流电话
        sendModel.setLogisticsTel(LogisticsTelEnum.enumToTel(sendModel.getCompanyCode()));
        List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> sendDetails = new ArrayList<>();
        List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
        Map<String, OcBOrderItem> skuOoidMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderItems)) {
            skuOoidMap = orderItems.stream()
                    .filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getPsCSkuEcode()))
                    .collect(Collectors.toMap(OcBOrderItem::getPsCSkuEcode, a -> a, (k1, k2) -> k2));
        }
        //组装包裹信息
        if (CollectionUtils.isNotEmpty(ocBOrderDeliveries)) {
            List<String> proCodeList = ocBOrderDeliveries.stream()
                    .filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getPsCProEcode()))
                    .map(obj -> obj.getPsCProEcode())
                    .collect(Collectors.toList());
            ProInfoQueryRequest proInfoQueryRequest = new ProInfoQueryRequest();
            proInfoQueryRequest.setProEcodeList(proCodeList);
            List<PsCPro> proInfoList = basicPsQueryService.getProInfo(proInfoQueryRequest);
            Map<String, String> proCodeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(proInfoList)) {
//                proCodeMap = proInfoList.stream().filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getEcode()))
//                        .collect(Collectors.toMap(PsCPro::getEcode, PsCPro::getBrandEname, (k1, k2) -> k2));
            }
            for (OcBOrderDelivery item : ocBOrderDeliveries) {
                if (item == null) {
                    continue;
                }
                StandplatLogisticsSendModel.StandplatLogisticsSendDetails detail = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();
                OcBOrderItem ocBOrderItem = skuOoidMap.get(item.getPsCSkuEcode());
                if (ocBOrderItem == null) {
                    continue;
                }
                detail.setItemId(ocBOrderItem.getOoid());
                detail.setDeliveryNo(item.getLogisticNumber());
                //物流公司查询物流档案中是否存在
                String logisticsCompany = companyCodeMap.get(item.getCpCLogisticsId());
                if (StringUtils.isEmpty(logisticsCompany)) {
                    logisticsCompany = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
                    companyCodeMap.put(item.getCpCLogisticsId(), logisticsCompany);
                    detail.setLogisticsCompany(logisticsCompany);
                } else {
                    detail.setLogisticsCompany(logisticsCompany);
                }
//                detail.setBrandName(proCodeMap.get(item.getPsCProEcode()));
                detail.setSkuId(item.getPsCSkuId() == null ? null : item.getPsCSkuId().toString());
                detail.setSku(item.getPsCSkuEcode());
                detail.setProductCode(item.getPsCProEcode());
                detail.setNum(item.getQty() == null ? 0L : item.getQty().longValue());
                detail.setRealNum(item.getQty() == null ? 0L : item.getQty().longValue());
                detail.setLackNum(0L);
                sendModel.setTid(ocBOrderItem.getTid());
                sendDetails.add(detail);
            }
        }
        sendModel.setStandplatLogisticsSendDetails(sendDetails);
        List<StandplatLogisticsSendModel> list = new ArrayList<>();
        list.add(sendModel);

        // @20200830 爱库存平台发货加活动ID需求：赋值活动ID
        setActivityId4StandplatLogisticsSendModel(list, itemTid, ocBOrder.getPlatform());

        standplatLogisticsSendDataModel.setLogisticsSendModels(list);
        standplatLogisticsSendDataModel.setPlatform(sendModel.getPlatform());
        standplatLogisticsSendDataModel.setSellerNick(sendModel.getSellerNick());
        return standplatLogisticsSendDataModel;
    }

    /**
     * 通用平台发货设置活动ID：爱库存
     *
     * @param sendModels
     * @param tid
     * @param platform
     */
    private void setActivityId4StandplatLogisticsSendModel(List<StandplatLogisticsSendModel> sendModels, String tid, Integer platform) {
        if (Objects.nonNull(tid)) {
            if (PlatFormEnum.AI_KU_CUN.getCode().equals(platform)) {
                // 针对爱库存：平台发货加活动ID字段
                // 从中间表查询
                String activityId = IpStandplatOrderService.getInstance().selectActivityidByTid(tid);
                log.info("setActivityId4StandplatLogisticsSendModel:{}/{}/{}/{}", activityId, tid, platform, JSON.toJSONString(sendModels));

                if (Objects.nonNull(activityId)) {
                    if (CollectionUtils.isNotEmpty(sendModels)) {
                        sendModels.forEach(m -> {
                            if (Objects.nonNull(m)) {
                                m.setActivityid(activityId);
                            }
                        });
                    }
                }
            }
        }
    }

    /**
     * 组装拼多多苏宁参数
     *
     * @param ocBOrderRelation
     * @return
     */
    public List<RPCOutStorageModel> addIpParam(OcBOrderRelation ocBOrderRelation, String subTid, Integer isSplit, String expressCode) {
        log.debug("组装通用发货入参:" + JSONObject.toJSONString(ocBOrderRelation));
        List<RPCOutStorageModel> rpcOutStorageModelList = new ArrayList<>();
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        //查询订单明细
        QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper();
        queryWrapper.in("ISACTIVE", "Y");
        queryWrapper.eq("OC_B_ORDER_ID", ocBOrder.getId());
        List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectList(queryWrapper);
        List<String> ooidList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(expressCode)) {
            for (OcBOrderItem ocBOrderItem : ocBOrderRelation.getOrderItemList()) {
                if (StringUtils.isNotEmpty(ocBOrderItem.getOoid())
                        && ocBOrderItem.getRefundStatus() != OmsOrderRefundStatus.SUCCESS.toInteger()) {
                    ooidList.add(ocBOrderItem.getOoid());
                }
            }
        } else {
            for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                if (StringUtils.isNotEmpty(ocBOrderItem.getOoid())
                        && ocBOrderItem.getRefundStatus() != OmsOrderRefundStatus.SUCCESS.toInteger()) {
                    ooidList.add(ocBOrderItem.getOoid());
                }
            }
        }

        String company_code = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
        log.debug("调用物流公司编码查询服务==============结束：结束后，物流编码为" + company_code);
        RPCOutStorageModel rpcOutStorageModel = new RPCOutStorageModel();
        if (null != isSplit) {
            rpcOutStorageModel.setIsSplit(isSplit);
        } else {
            rpcOutStorageModel.setIsSplit(ocBOrder.getIsSplit());
        }
        rpcOutStorageModel.setTid(ocBOrder.getTid());
        rpcOutStorageModel.setCompanyCode(company_code);//平台物流公司编码
        rpcOutStorageModel.setDeliveryWarehouse(ocBOrder.getCpCPhyWarehouseEcode());//待定
        rpcOutStorageModel.setDisputeId(null);//换货单号 暂时没有
        rpcOutStorageModel.setID(ocBOrder.getId());//mq回执返回过来用来订单ID
        rpcOutStorageModel.setLogisticsCompanyName(ocBOrder.getCpCLogisticsEname());
        rpcOutStorageModel.setLogisticsType(null);//默认null
        rpcOutStorageModel.setOrderType(ocBOrder.getOrderType());
        if (StringUtils.isEmpty(expressCode)) {
            rpcOutStorageModel.setOutSid(ocBOrder.getExpresscode());
        } else {
            rpcOutStorageModel.setOutSid(expressCode);
        }
        rpcOutStorageModel.setPayType(ocBOrder.getPayType());
        rpcOutStorageModel.setPlatform(ocBOrder.getPlatform());
        rpcOutStorageModel.setSellerNick(ocBOrder.getCpCShopSellerNick());
        rpcOutStorageModel.setSubTid(subTid);//不拆单 传null
        rpcOutStorageModel.setSupplierCode(null);//默认null
        rpcOutStorageModel.setOrderLineNumberList(ooidList);
        rpcOutStorageModelList.add(rpcOutStorageModel);
        log.debug("组装通用发货出参:" + JSONObject.toJSONString(rpcOutStorageModelList));
        return rpcOutStorageModelList;
    }


    /**
     * 作废退单
     *
     * @param ocBOrder
     */
    public void updateReturnOrder(OcBOrder ocBOrder) {
        //去es查询出退单的数据 如果存在并且状态为待退换入库
        if (!ocBOrder.getIsInreturning().equals(InreturningStatus.INRETURNING)) {
            return;
        }
        JSONArray aryIds = ES4ReturnOrder.QueryReturnOrdersByOrderId(ocBOrder);
        if (aryIds == null) {
            return;
        }
        for (int i = 0; i < aryIds.size(); i++) {
            //  Map<String, Long> map = (Map<String, Long>) aryIds.get(i);
            //OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.getRefundOrder(map.get("ID"), ocBOrder.getId(),
            //      ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
            //作废退单
            //  ocBReturnOrder.setReturnStatus(ReturnStatusEnum.CANCLE.getVal());
            // if (null != ocBReturnOrder) {
            // ocBReturnOrderMapper.updateById(ocBReturnOrder);//作废退单
            JSONObject jsonObject = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(aryIds.get(i));
            jsonObject.put("ids", jsonArray);
            ValueHolderV14 holderV14 = ocCancelChangingOrRefundService.orRefundService(jsonObject, SystemUserResource.getRootUser(), Boolean.FALSE);
            if (holderV14.getCode() == -1) {
                log.error("updateReturnOrder 取消退单失败,退单id{}. 失败原因{}", aryIds.get(i), holderV14.getMessage());
            }

        }


    }

    /**
     * @param ocBOrder
     * @param is_split
     * @param ocBOrderItemList
     * @param expressCodeList
     * @return void
     * <AUTHOR>
     * @Description 通用平台多包裹发货
     * @Date 9:22 2020/7/17
     **/
    public void multiplePakageSNCallInterface(OcBOrder ocBOrder, long is_split, List<OcBOrderItem> ocBOrderItemList, List<String> expressCodeList) {
        long tid = Long.valueOf(ocBOrder.getTid());
        /**和曾志军沟通后多包裹平台发货,is_split = 1L*/
        is_split = 1L;
        if (ocBOrderItemList.size() >= expressCodeList.size()) {
            if (log.isDebugEnabled()) {
                log.debug("苏宁多包裹发货参数:" + JSONObject.toJSONString(ocBOrder) + JSONObject.toJSONString(ocBOrderItemList) + JSONObject.toJSONString(expressCodeList));
            }
            for (int i = 0; i < expressCodeList.size(); i++) {
                //发完一波,休息2秒
                try {
                    Thread.sleep(2000);
                } catch (Exception e) {
                }
                String expressCode = expressCodeList.get(i);
                if (i + 1 == expressCodeList.size()) {
                    String newOid = "";
                    if (ocBOrderItemList.size() > 0) {
                        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                        ocBOrderRelation.setOrderInfo(ocBOrder);
                        ocBOrderRelation.setOrderItemList(ocBOrderItemList);
                        newOid = buildDistinctOrderItemOid(ocBOrderItemList, newOid);
                        log.debug(this.getClass().getName() + "通用小平台多包裹平台发货接口line547，请求云枢纽，请求信息，订单id = " + ocBOrder.getId() + "oid = " + newOid + "is_split = " + is_split + "expressCode = " + expressCode);
                        List<StandplatLogisticsSendDataModel> standplatLogisticsSendDataModels = this.addCommonIpParam(ocBOrderRelation, newOid, 1, expressCode);
                        if (CollectionUtils.isNotEmpty(standplatLogisticsSendDataModels)) {
                            for (StandplatLogisticsSendDataModel model : standplatLogisticsSendDataModels) {
                                ValueHolderV14<List<StandplatLogisticsSendResult>> vh = ipRpcService.sendStandPlatLogistics(model);
                                log.debug(this.getClass().getName() + "通用小平台多包裹平台发货接口，请求云枢纽，返回信息为：" + vh.toString());
                            }
                        }
                    }
                } else {

                    OcBOrderItem ocBOrderItem = ocBOrderItemList.get(0);
                    String oid = "";
                    oid = ocBOrderItem.getOoid();
                    OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                    ocBOrderRelation.setOrderInfo(ocBOrder);
                    List<OcBOrderItem> items = new ArrayList<>();
                    items.add(ocBOrderItem);
                    ocBOrderRelation.setOrderItemList(items);
                    List<StandplatLogisticsSendDataModel> standplatLogisticsSendDataModels = this.addCommonIpParam(ocBOrderRelation, oid, 1, expressCode);
                    if (CollectionUtils.isNotEmpty(standplatLogisticsSendDataModels)) {
                        for (StandplatLogisticsSendDataModel model : standplatLogisticsSendDataModels) {
                            ValueHolderV14<List<StandplatLogisticsSendResult>> vh = ipRpcService.sendStandPlatLogistics(model);
                            log.debug(this.getClass().getName() + "通用小平台多包裹平台发货接口，请求云枢纽，返回信息为：" + vh.toString());
                        }
                    }
                    ocBOrderItemList.remove(ocBOrderItem);
                }
            }
        } else {
            for (int j = 0; j < ocBOrderItemList.size(); j++) {
                try {
                    Thread.sleep(2000);
                } catch (Exception e) {
                }
                OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                ocBOrderRelation.setOrderInfo(ocBOrder);
                List<OcBOrderItem> items = new ArrayList<>();
                items.add(ocBOrderItemList.get(j));
                ocBOrderRelation.setOrderItemList(items);

                String oid = ocBOrderItemList.get(j).getOoid();
                String expressCode = expressCodeList.get(j);
                List<StandplatLogisticsSendDataModel> standplatLogisticsSendDataModels = this.addCommonIpParam(ocBOrderRelation, oid, 1, expressCode);
                if (CollectionUtils.isNotEmpty(standplatLogisticsSendDataModels)) {
                    for (StandplatLogisticsSendDataModel model : standplatLogisticsSendDataModels) {
                        ValueHolderV14<List<StandplatLogisticsSendResult>> vh = ipRpcService.sendStandPlatLogistics(model);
                        log.debug(this.getClass().getName() + "苏宁多包裹平台发货接口，请求云枢纽，返回信息为：" + vh.toString());
                    }
                }
            }
        }
    }


    /**
     * 虚拟发货
     */
    public void spreadOrderCallInterface(OcBOrder ocBOrder, Set<String> tidSet) {
        log.debug(this.getClass().getName() + "平台发货服务==========调用平台发货接口 spreadOrderCallInterface 订单id为：" + ocBOrder.getId());
        try {
            long is_split = 0;//0非拆单，1拆单
            if (tidSet.size() == 0) {//订单没有明细订单
                long tid = Long.valueOf(ocBOrder.getTid());
                String out_sid = ocBOrder.getExpresscode();
                log.debug(this.getClass().getName() + "平台发货服务，请求云枢纽接口，没有明细 spreadInterfaceParam==========开始");
                ValueHolderV14 vh = spreadInterfaceParam(null, tid, is_split, out_sid, ocBOrder);
                log.debug(this.getClass().getName() + "平台发货服务，请求云枢纽接口，没有明细 spreadInterfaceParam==========结束，返回结果为：" + vh.toString());
            } else if (tidSet.size() == 1) {
                //订单下的tid全部一致
                for (String str : tidSet) {
                    //调用云枢纽发货接口
                    //封装接口参数，并调用陈顺发货平台接口
                    Object[] sub_tid_tmp = tidSet.toArray();
                    StringBuffer sb = new StringBuffer();
                    for (int k = 0; k < sub_tid_tmp.length; k++) {
                        sb.append(sub_tid_tmp[k]);
                    }
                    String sub_tid = sb.toString();
                    long tid = Long.valueOf(str);
                    String out_sid = ocBOrder.getExpresscode();
                    log.debug(this.getClass().getName() + "平台发货服务，请求云枢纽接口，明细tid一致" + sub_tid + " spreadInterfaceParam==========开始");
                    ValueHolderV14 vh = spreadInterfaceParam(sub_tid, tid, is_split, out_sid, ocBOrder);
                    log.debug(this.getClass().getName() + "平台发货服务，请求云枢纽接口，明细tid一致 spreadInterfaceParam==========结束，返回结果为：" + vh.toString());
                }
            } else if (tidSet.size() > 1) {
                //订单下的tid不一致
                List<OcBOrderItem> ocBOrderItemList = omsOrderItemServie.selectUnSuccessRefund(ocBOrder.getId());
                for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                    //循环调用接口平台的发货接口
                    //封装接口参数，并调用陈顺发货平台接口
                    String sub_tid = "";
                    if (ocBOrderItem.getOoid() != null) {
                        sub_tid = ocBOrderItem.getOoid();
                    }
                    long tid = Long.valueOf(ocBOrderItem.getTid());
                    String out_sid = ocBOrder.getExpresscode();
                    log.debug(this.getClass().getName() + "平台发货服务，请求云枢纽接口，明细tid不一致" + sub_tid + " spreadInterfaceParam==========开始");
                    ValueHolderV14 vh = spreadInterfaceParam(sub_tid, tid, is_split, out_sid, ocBOrder);
                    log.debug(this.getClass().getName() + "平台发货服务，请求云枢纽接口，明细tid不一致 spreadInterfaceParam==========结束，返回结果为：" + vh.toString());
                }
            }
        } catch (Exception ex) {
            log.error("平台发货服务，" + "订单ID为：" + ocBOrder.getId() + "虚拟订单【非拆单，非合单】调用平台发货接口异常{} " + ex.getMessage(), ex.getMessage());
        }
    }

    /**
     * 虚拟 封装调用平台发货接口
     *
     * @param sub_tid
     * @param tid
     * @param is_split
     * @param out_sid
     * @param ocBOrder
     * @return
     */
    public ValueHolderV14 spreadInterfaceParam(String sub_tid, long tid, long is_split, String out_sid, OcBOrder ocBOrder) {
        Long plaform = Long.valueOf(ocBOrder.getPlatform());
        String sellernick = ocBOrder.getCpCShopSellerNick();
        LogisticsSendModel tbLogisticsModel = new LogisticsSendModel();
        //订单id
        tbLogisticsModel.setId(ocBOrder.getId());
        //平台单号
        tbLogisticsModel.setTid(tid);
        //平台
        tbLogisticsModel.setPlatform(plaform);
        //卖家昵称
        tbLogisticsModel.setSellerNick(sellernick);
        //是否拆单，都是0
        tbLogisticsModel.setIsSplit(is_split);
        //运单号
        tbLogisticsModel.setOutSid(out_sid);
        //物流公司名称
        tbLogisticsModel.setLogisticsCompanyName(ocBOrder.getCpCLogisticsEname());
        //子订单号
        tbLogisticsModel.setSubTid(sub_tid);
        //0：正常  1：换货  8：虚拟单
        tbLogisticsModel.setOrdertype(8L);
        tbLogisticsModel.setPaytype(Long.valueOf(ocBOrder.getPayType()));

        if (ocBOrder.getPlatform().equals(PlatFormEnum.JINGDONG.getCode())) {
            //默认使用物流代码：1274，物流单号为空
            //TODO 平台为京东时，产品说店铺类型先写死“SOP”
            tbLogisticsModel.setShoptype("SOP");
            tbLogisticsModel.setCompanyCode("1274");//默认使用物流代码：1274，
            tbLogisticsModel.setOutSid("");//物流单号为空
        }
        return ipRpcService.sendAndExchangeGoods(tbLogisticsModel, plaform);
    }
}
