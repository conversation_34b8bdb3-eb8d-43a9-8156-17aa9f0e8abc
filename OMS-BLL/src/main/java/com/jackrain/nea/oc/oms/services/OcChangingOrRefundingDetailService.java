package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderDefectMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ToDRPStatusEnum;
import com.jackrain.nea.oc.oms.model.result.ChangingOrReFundingDetailResult;
import com.jackrain.nea.oc.oms.model.result.QueryReturnOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderDefect;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchangeExt;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefundExt;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 退换货列表详情查询
 *
 * @author: 郑立轩
 * @since: 2019/3/13
 * create at : 2019/3/13 11:15
 */
@Component
@Slf4j
public class OcChangingOrRefundingDetailService {
    @Autowired
    OcBReturnOrderMapper orderMapper;
    @Autowired
    OcBReturnOrderExchangeMapper exchangeMapper;
    @Autowired
    OcBReturnOrderRefundMapper refundMapper;
    @Autowired
    OcBReturnOrderDefectMapper defectMapper;
    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    public ValueHolderV14<ChangingOrReFundingDetailResult> findDetail(JSONObject object) {
        ValueHolderV14<ChangingOrReFundingDetailResult> vh = new ValueHolderV14<>();
        ChangingOrReFundingDetailResult result = new ChangingOrReFundingDetailResult();
        //获取退换单编号ID
        try {
            String id = object.getString("id");
            if (StringUtils.isEmpty(id)) {
                throw new NDSException("请点击正确的退换货订单");
            }
            //查询主表信息
            recordLog(this.getClass().getName() + " OcChangingOrRefundingDetailService.ID: " + id);
            QueryReturnOrderResult ocBReturnOrderDto = orderMapper.queryReturnOrderResultById(id);
            if (ocBReturnOrderDto == null) {
                vh.setMessage("未查询到主表信息，请确认id是否存在");
                vh.setCode(-1);
                vh.setData(null);
                return vh;
            }
            // @20210106 是否是从退转换进来的
            Integer isRefund2Exchange = object.getInteger("isRefund2Exchange");
            if (isRefund2Exchange != null && isRefund2Exchange == 1) {
                ocBReturnOrderDto.setIsReserved(IsActiveEnum.Y.getVal());
            }
            // author by xiwen.z start
            recordLog(this.getClass().getName() + " OcChangingOrRefundingDetailService.QueryReturnOrderResult"
                    + JSON.toJSONString(ocBReturnOrderDto));
            Long cpCPhyWarehouseId = ocBReturnOrderDto.getCpCPhyWarehouseId();
            ocBReturnOrderDto.setCpCPhyWarehouseIdName(getCpWarehouseName(cpCPhyWarehouseId));

            Long cpCPhyWarehouseInId = ocBReturnOrderDto.getCpCPhyWarehouseInId();
            ocBReturnOrderDto.setCpCPhyWarehouseInIdName(getCpWarehouseName(cpCPhyWarehouseInId));
            //设置传DRP状态名称
            ocBReturnOrderDto.setToDrpStatusName(ToDRPStatusEnum.getTextByCode(ocBReturnOrderDto.getToDrpStatus()));
            // end

            ReturnNaiKaStatusEnum returnNaiKaStatusEnum = ReturnNaiKaStatusEnum.getByStatus(ocBReturnOrderDto.getToNaikaStatus());
            ocBReturnOrderDto.setToNaikaStatusName(returnNaiKaStatusEnum == null ? "" : returnNaiKaStatusEnum.getDesc());

            if (StringUtils.isEmpty(ocBReturnOrderDto.getSigningStatus())){
                ocBReturnOrderDto.setSigningStatus("0");
            }
            if (StringUtils.isEmpty(ocBReturnOrderDto.getSubscriptionStatus())){
                ocBReturnOrderDto.setSubscriptionStatus("0");
            }
            if (StringUtils.isEmpty(ocBReturnOrderDto.getOverdueStorageStatus())){
                ocBReturnOrderDto.setOverdueStorageStatus("0");
            }
            //获取分页显示的页数
            Integer start = object.getInteger("start");
            //获取每页显示的数量 默认50条
            Integer count = object.getInteger("count") == null ? 50 : object.getInteger("count");
            //计算startindex
            int startIndex = (start - 1) * count;
            //查询子表(退货）信息
            List<OcBReturnOrderRefundExt> refundDtos = refundMapper.selectByReturnId(id, count, startIndex);
            for (OcBReturnOrderRefundExt refundDto : refundDtos) {
                if (refundDto.getSex() != null) {
                    log.debug(LogUtil.format("查询性别接口入参：", refundDto.getSex()));
                    ValueHolderV14<PsCProdimItem> valueHolderV14 = null;
                    try {
                        valueHolderV14 = psRpcService.queryProdimItem(refundDto.getSex());
                        log.debug(LogUtil.format("调用性别 接口出参") + JSONObject.toJSONString(valueHolderV14));
                    } catch (Exception e) {
                        log.error(LogUtil.format("调用性别接口异常,error:{}"), Throwables.getStackTraceAsString(e));
                    }
                    if (valueHolderV14 != null) {
                        // todo 调用查询性别 接口
                        PsCProdimItem data = valueHolderV14.getData();
                        if (data != null) {
                            String ename = data.getEname();
                            refundDto.setSexEname(ename);
                        }
                    }
                }
                packageRefundItem(refundDto);
            }
            log.debug(LogUtil.format("退货表查询结果是》》》》》") + refundDtos.toString());
            //查询子表(换货）信息
            List<OcBReturnOrderExchangeExt> exchangeDtos = exchangeMapper.selectByReturnId(id, count, startIndex);
            if (CollectionUtils.isNotEmpty(exchangeDtos)) {
                for (OcBReturnOrderExchangeExt exchangeDto : exchangeDtos) {
                    if (exchangeDto.getSex() != null) {
                        log.debug(LogUtil.format("查询性别接口入参：", exchangeDto.getSex()));
                        ValueHolderV14<PsCProdimItem> valueHolderV14 = null;
                        try {
                            valueHolderV14 = psRpcService.queryProdimItem(exchangeDto.getSex());
                            log.debug(LogUtil.format("调用性别 接口出参") + JSONObject.toJSONString(valueHolderV14));
                        } catch (Exception e) {
                            log.error(LogUtil.format("调用性别接口异常,error:{}"), Throwables.getStackTraceAsString(e));
                        }
                        if (valueHolderV14 != null) {
                            // todo 调用查询性别 接口
                            PsCProdimItem data = valueHolderV14.getData();
                            if (data != null) {
                                String ename = data.getEname();
                                exchangeDto.setSexEname(ename);
                            }
                        }
                    }
                    JSONObject dataMap = psRpcService.querySkuInfo(exchangeDto.getPsCProEcode(), SystemUserResource.getRootUser());
                    exchangeDto.setSelected(dataMap);
                }
            }
            log.debug(LogUtil.format("换货表查询结果是》》》》") + exchangeDtos.toString());
            //查詢 退换货正次品表
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("oc_b_return_order_id", id);
            List<OcBReturnOrderDefect> list = defectMapper.selectList(wrapper);
            result.setOrderDefects(list);
            //查询子表(退货）信息总数量
            Integer totalRE = refundMapper.selectRefundCount(id);

            //查询子表（换货）信息总数量
            Integer totalEX = exchangeMapper.selectExchangeCount(id);

            //查询子表(换货）信息总数量
            //整理返回
            result.setTotalEX(totalEX);
            result.setTotalRE(totalRE);
            result.setPageEX(totalEX % count == 0 ? totalEX / count : totalEX / count + 1);
            result.setPageRE(totalRE % count == 0 ? totalRE / count : totalRE / count + 1);
            result.setReturnOrders(ocBReturnOrderDto);
            result.setExchangeDtoList(exchangeDtos);
            result.setRefundDtoList(refundDtos);
            vh.setMessage("成功");
            vh.setCode(0);
            vh.setData(result);
        } catch (NDSException e) {
            log.debug(this.getClass().getName() + e.getMessage());
            vh.setCode(-1);
            vh.setMessage("查询失败");

        }
        return vh;
    }

    /**
     * 明细字段翻译
     *
     * @param refundDto
     */
    private void packageRefundItem(OcBReturnOrderRefundExt refundDto) {
        String toAgStatus = refundDto.getToAgStatus(); // 传AG 状态
        String giftType = refundDto.getGiftType();//  赠品类型
        String refundStatus = refundDto.getRefundStatus(); // 平台退款状态
        if (StringUtils.isEmpty(toAgStatus)) {
            refundDto.setToAgStatusName(null);
        } else {
            if (AGStatusEnum.INIT.getVal().equals(Integer.valueOf(toAgStatus))) {
                refundDto.setToAgStatusName(AGStatusEnum.INIT.getKey());
            } else if (AGStatusEnum.SUCCESS.getVal().equals(Integer.valueOf(toAgStatus))) {
                refundDto.setToAgStatusName(AGStatusEnum.SUCCESS.getKey());
            } else if (AGStatusEnum.FAIL.getVal().equals(Integer.valueOf(toAgStatus))) {
                refundDto.setToAgStatusName(AGStatusEnum.FAIL.getKey());
            } else {
                refundDto.setToAgStatusName(AGStatusEnum.NOT.getKey());
            }
        }
        // 0 否 1 是系统赠品 2 平台赠品
        if (StringUtils.isEmpty(giftType)) {

            refundDto.setGiftTypeName(null);
        } else {
            if ("0".equals(giftType)) {
                refundDto.setGiftTypeName("否");
            } else if ("1".equals(giftType)) {
                refundDto.setGiftTypeName("系统赠品");
            } else {
                refundDto.setGiftTypeName("平台赠品");
            }
        }
        if (StringUtils.isEmpty(refundStatus)) {
            refundDto.setRefundStatusName(null);
        } else {
            if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(refundStatus)) {
                refundDto.setRefundStatusName(TaobaoReturnOrderExt.RefundStatus.CLOSED.getName());
            } else if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(refundStatus)) {
                refundDto.setRefundStatusName(TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getName());
            } else if (TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode().equals(refundStatus)) {
                refundDto.setRefundStatusName(TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getName());
            } else if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode().equals(refundStatus)) {
                refundDto.setRefundStatusName(TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getName());
            } else if (TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(refundStatus)) {
                refundDto.setRefundStatusName(TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getName());
            } else if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(refundStatus)) {
                refundDto.setRefundStatusName(TaobaoReturnOrderExt.RefundStatus.SUCCESS.getName());
            } else if (TaobaoReturnOrderExt.RefundStatus.WAIT_RETURN_MONEY.getCode().equals(refundStatus)) {
                refundDto.setRefundStatusName(TaobaoReturnOrderExt.RefundStatus.WAIT_RETURN_MONEY.getName());
            } else if (TaobaoReturnOrderExt.RefundStatus.NOT_APPLY_RETURN.getCode().equals(refundStatus)) {
                refundDto.setRefundStatusName(TaobaoReturnOrderExt.RefundStatus.NOT_APPLY_RETURN.getName());
            } else {
                refundDto.setRefundStatusName(TaobaoReturnOrderExt.RefundStatus.REFUSE_ENSURE_GOODS.getName());
            }
        }
        JSONObject dataMap = psRpcService.querySkuInfo(refundDto.getPsCProEcode(), SystemUserResource.getRootUser());
        refundDto.setSelected(dataMap);
    }

    /**
     * debug.level.recored
     *
     * @param msg string
     */
    private void recordLog(String msg) {
        if (log.isDebugEnabled()) {
            log.debug(msg);
        }
    }

    /**
     * 根据仓库id,查询实体仓名称
     *
     * @param id 实体仓id
     * @return 实体仓名称
     */
    private String getCpWarehouseName(Long id) {
        if (id != null) {
            try {
                CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(id);
                String whName = cpCPhyWarehouse.getEname();
                whName = whName == null ? "" : whName;
                recordLog(this.getClass().getName() + " cpCPhyWarehouseIdName || cpCPhyWarehouseIdName:" + whName);
                return whName;
            } catch (Exception e) {
                log.error(this.getClass().getName() + " OcChangingOrRefundingDetailService.查询实体仓信息异常,ID:" + id);
                return "";
            }
        } else {
            return "";
        }
    }

    /**
     * 退换货订单复制 zxl 2019-10-11
     */
    public ValueHolderV14<ChangingOrReFundingDetailResult> returnOrderquery(JSONObject object) {
        ValueHolderV14<ChangingOrReFundingDetailResult> vh = new ValueHolderV14<>();
        ChangingOrReFundingDetailResult result = new ChangingOrReFundingDetailResult();
        //获取退换单编号ID
        try {
            String id = object.getString("id");
            if (StringUtils.isEmpty(id)) {
                throw new NDSException("请点击正确的退换货订单");
            }
            //查询主表信息
            QueryReturnOrderResult ocBReturnOrderDto = orderMapper.queryReturnOrderResultById(id);
            if (ocBReturnOrderDto == null) {
                vh.setMessage("未查询到主表信息，请确认id是否存在");
                vh.setCode(-1);
                vh.setData(null);
                return vh;
            }
            Long cpCPhyWarehouseId = ocBReturnOrderDto.getCpCPhyWarehouseId();
            ocBReturnOrderDto.setCpCPhyWarehouseIdName(getCpWarehouseName(cpCPhyWarehouseId));

            Long cpCPhyWarehouseInId = ocBReturnOrderDto.getCpCPhyWarehouseInId();
            ocBReturnOrderDto.setCpCPhyWarehouseInIdName(getCpWarehouseName(cpCPhyWarehouseInId));
            // end

            //获取分页显示的页数
            Integer start = object.getInteger("start");
            //获取每页显示的数量 默认50条
            Integer count = object.getInteger("count") == null ? 50 : object.getInteger("count");
            //计算startindex
            int startIndex = (start - 1) * count;
            //查询子表(退货）信息
            List<OcBReturnOrderRefundExt> refundDtos = refundMapper.selectByReturnId(id, count, startIndex);
            for (OcBReturnOrderRefundExt refundDto : refundDtos) {
                refundDto.setQtyIn(new BigDecimal(0));
                refundDto.setId(-1L);
                if (refundDto.getSex() != null) {
                    log.debug(LogUtil.format("查询性别接口入参：", refundDto.getSex()));
                    ValueHolderV14<PsCProdimItem> valueHolderV14 = null;
                    try {
                        valueHolderV14 = psRpcService.queryProdimItem(refundDto.getSex());
                        log.debug(LogUtil.format("调用性别 接口出参") + JSONObject.toJSONString(valueHolderV14));
                    } catch (Exception e) {
                        log.error(LogUtil.format("调用性别接口异常,error:{}"), Throwables.getStackTraceAsString(e));
                    }
                    if (valueHolderV14 != null) {
                        // todo 调用查询性别 接口
                        PsCProdimItem data = valueHolderV14.getData();
                        if (data != null) {
                            String ename = data.getEname();
                            refundDto.setSexEname(ename);
                        }
                    }
                }
            }
            //查询子表(换货）信息
            List<OcBReturnOrderExchangeExt> exchangeDtos = exchangeMapper.selectByReturnId(id, count, startIndex);
            if (CollectionUtils.isNotEmpty(exchangeDtos)) {
                for (OcBReturnOrderExchangeExt exchangeDto : exchangeDtos) {
                    exchangeDto.setQtyIn(new BigDecimal(0));
                    exchangeDto.setId(-1L);
                    if (exchangeDto.getSex() != null) {
                        ValueHolderV14<PsCProdimItem> valueHolderV14 = null;
                        try {
                            valueHolderV14 = psRpcService.queryProdimItem(exchangeDto.getSex());
                        } catch (Exception e) {
                            log.error(LogUtil.format("查询子表(换货）信息异常,error:{}"), Throwables.getStackTraceAsString(e));
                        }
                        if (valueHolderV14 != null) {
                            PsCProdimItem data = valueHolderV14.getData();
                            if (data != null) {
                                String ename = data.getEname();
                                exchangeDto.setSexEname(ename);
                            }
                        }
                    }
                }
            }
            //查詢 退换货正次品表
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("oc_b_return_order_id", id);
            List<OcBReturnOrderDefect> list = defectMapper.selectList(wrapper);
            result.setOrderDefects(list);
            //查询子表(退货）信息总数量
            Integer totalRE = refundMapper.selectRefundCount(id);

            //查询子表（换货）信息总数量
            Integer totalEX = exchangeMapper.selectExchangeCount(id);

            //查询子表(换货）信息总数量
            //整理返回
            result.setTotalEX(totalEX);
            result.setTotalRE(totalRE);
            result.setPageEX(totalEX % count == 0 ? totalEX / count : totalEX / count + 1);
            result.setPageRE(totalRE % count == 0 ? totalRE / count : totalRE / count + 1);
            //新增时默认的状态
            ocBReturnOrderDto.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());//退单状态
            ocBReturnOrderDto.setIsAdd(1);//是否手工新增订单
            ocBReturnOrderDto.setIsToag(0);//是否传AG ,0未传，1已传，2失败，3不传
            ocBReturnOrderDto.setIsTransfer(0);//是否生成调拨单
            ocBReturnOrderDto.setIsTodrp(0);//是否生成入库通知单  0未生成，1已生成，2生成失败
            ocBReturnOrderDto.setInventedStatus(0);//虚拟入库状态  0未虚拟入库 1虚拟入库未入库  2虚拟入库已入库
            ocBReturnOrderDto.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
            ocBReturnOrderDto.setIsTowms(0);//传WMS状态  0未传WMS，1传WMS中，2传WMS成功，3传WMS失败
            ocBReturnOrderDto.setIsReceiveConfirm(0);//是否确认收货
            ocBReturnOrderDto.setWmsCancelStatus(0);//wms撤回状态   0 未撤回；1 撤回成功；2 撤回失败
            ocBReturnOrderDto.setIsForce(0);//强制入库
            ocBReturnOrderDto.setQtyWmsFail(0L);//传wms失败次数
            ocBReturnOrderDto.setIsCheck(0);//是否已匹配 0未匹配 1 已匹配
            ocBReturnOrderDto.setBackMessage("");
            ocBReturnOrderDto.setStatusDefectiveTrans(0L);//（0 无次品调拨 1 次品未调拨 2 次品已调拨）
            ocBReturnOrderDto.setIsNeedToWms(0L);
            //
            ocBReturnOrderDto.setBackMessage("");//旗帜数据质问空
            ocBReturnOrderDto.setStatusDefectiveTrans(0L);//（0 无次品调拨 1 次品未调拨 2 次品已调拨）
            ocBReturnOrderDto.setIsNeedToWms(0L);//是否传wms
            ocBReturnOrderDto.setIsManualAudit(0);//是否手工审核
            ocBReturnOrderDto.setIsExamine(0);//是否提交审核
            ocBReturnOrderDto.setIsInstorage(0);  //是否入仓成功  0未入仓成功 1入仓成功
            ocBReturnOrderDto.setId(-1L);
            result.setReturnOrders(ocBReturnOrderDto);
            result.setExchangeDtoList(exchangeDtos);
            result.setRefundDtoList(refundDtos);
            vh.setMessage("成功");
            vh.setCode(0);
            vh.setData(result);
        } catch (Exception e) {
            log.error(LogUtil.format("退换货订单复制异常，error:{}"), Throwables.getStackTraceAsString(e));
            vh.setCode(-1);
            vh.setMessage("查询失败");
        }
        return vh;
    }
}
