package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.core.schema.Table;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.MergeOrderModel;
import com.jackrain.nea.oc.oms.util.Permission4ESUtil;
import com.jackrain.nea.oc.oms.util.Permission4SlfEnum;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-07-25 14:27
 */
@Slf4j
@Component
public class OrderOutService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderListQueryService queryService;

    /**
     * 订单出库列表查询界面
     *
     * @param querySession
     * @return
     */
    public ValueHolder orderListQuery(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject resultData = new JSONObject();
        JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
        JSONObject fixedcolumns = param.getJSONObject("fixedcolumns");
        Integer range = param.getInteger("range") == null ? QueryUtils.getdefalutrange() : param.getInteger("range");
        Integer startIndex = param.getInteger("startindex") == null ? 0 : param.getInteger("startindex");
        Table table = querySession.getTableManager().getTable(param.getString("table"));
        JSONArray orderByKey = this.getOrderByKey();
        try {
            JSONObject whereKey = this.getWhereKey(fixedcolumns);
            JSONObject filterKey = this.getFilterKey(fixedcolumns);
            boolean result = Permission4ESUtil.permissionHandler(querySession.getUser(), whereKey, Permission4SlfEnum.SHOP);
            if (!result) {
                resultData.put("start", startIndex);
                resultData.put("row", "");
                resultData.put("totalRowCount", 0);
                vh.put("data", resultData);
                vh.put("code", 0);
                vh.put("message", "success");
                return vh;
            }

            JSONObject esResult = ES4Order.queryOrderOutboundList(whereKey, filterKey, orderByKey, range, startIndex);

            if (null == esResult) {
                resultData.put("start", startIndex);
                resultData.put("row", "");
                resultData.put("totalRowCount", 0);
                vh.put("data", resultData);
                vh.put("code", 0);
                vh.put("message", "success");
                return vh;
            }
            JSONArray aryIds = esResult.getJSONArray("data");
            Integer totalCount = esResult.getInteger("total");
            if (CollectionUtils.isEmpty(aryIds)) {
                resultData.put("start", startIndex);
                resultData.put("row", "");
                resultData.put("totalRowCount", 0);
                vh.put("data", resultData);
                vh.put("code", 0);
                vh.put("message", "success");
                return vh;
            }
            List<Integer> ids = Lists.newArrayList();
            for (int i = 0; i < aryIds.size(); i++) {
                Map<String, Integer> map = (Map<String, Integer>) aryIds.get(i);
                ids.add(map.get("ID"));
            }
            List<MergeOrderModel> mergeOrderModelList = ocBOrderMapper.selectListByIds(ids);
            if (CollectionUtils.isNotEmpty(mergeOrderModelList)) {
                for (MergeOrderModel mergeOrderModel : mergeOrderModelList) {
                    exChangeOutPutField(mergeOrderModel);
                }
            }

            JSONArray jsonArray = (JSONArray) JSONArray.toJSON(mergeOrderModelList);

            List<JSONObject> jsonObjectList = JSONObject.parseArray(
                    JSONObject.toJSONString(jsonArray, SerializerFeature.WriteMapNullValue), JSONObject.class);
            JSONArray getFrameDataFormat = getFrameDataFormat(jsonObjectList);
            resultData.put("start", startIndex);
            resultData.put("rowCount", range);
            resultData.put("row", getFrameDataFormat);
            resultData.put("totalRowCount", totalCount);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
            return vh;
        } catch (Exception e) {
            log.error(LogUtil.format("订单出库查询异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            resultData.put("start", startIndex);
            resultData.put("row", "");
            resultData.put("totalRowCount", 0);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
            return vh;
        }


    }


    /**
     * 订单值:  转换可视化
     *
     * @param mergeOrderModel 订单结果
     */
    private void exChangeOutPutField(MergeOrderModel mergeOrderModel) {
        if (mergeOrderModel == null) {
            return;
        }
        log.debug("转换可视化入参" + JSONObject.toJSONString(mergeOrderModel));
        String orderStatusName = OcOrderCheckBoxEnum.enumToStringByValue(mergeOrderModel.getOrderStatus());
        mergeOrderModel.setOrderStatusName(orderStatusName);
        String orderTypeName = OrderTypeEnum.getTextByVal(mergeOrderModel.getOrderType());
        mergeOrderModel.setOrderTypeName(orderTypeName);
        String platFormName = PlatFormEnum.getName(mergeOrderModel.getPlatform());
        mergeOrderModel.setPlatFormName(platFormName);
        String payTypeName = OcBorderListEnums.PayTypeEnum.getTextByVal(mergeOrderModel.getPayType());
        mergeOrderModel.setPayTypeName(payTypeName);
        String occupyStatusName = OcBorderListEnums.OccupyStatusEnum.getTextByVal(mergeOrderModel.getOccupyStatus());
        mergeOrderModel.setOccupyStatusName(occupyStatusName);
        String wmsCancelStatusName = OcBorderListEnums.WmsCanceStatusEnum.getTextByVal(mergeOrderModel.getWmsCancelStatus());
        mergeOrderModel.setWmsCancelStatusName(wmsCancelStatusName);
        String isGeninvoiceNoticeNm = OcBorderListEnums.IsGeninvoiceNoticeEnum.getTextByVal(mergeOrderModel.getIsGeninvoiceNotice());
        mergeOrderModel.setIsGeninvoiceNoticeName(isGeninvoiceNoticeNm);
        String returnName = OcBorderListEnums.ReturnStatusEnum.getTextByVal(mergeOrderModel.getReturnStatus());
        mergeOrderModel.setReturnStatusName(returnName);
        String OutStatusName = OutStatusEnum.enumToStringByValue(mergeOrderModel.getOutStatus());
        mergeOrderModel.setOutStatusName(OutStatusName);
        if (null != mergeOrderModel.getIsForce() && mergeOrderModel.getIsForce().equals(1L)) {
            mergeOrderModel.setForceSendName("成功");
        }
        if (null != mergeOrderModel.getIsForce() && mergeOrderModel.getIsForce().equals(0L)) {
            mergeOrderModel.setForceSendName("失败");
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // @20200829 空指针问题修复
        mergeOrderModel.setOrderDateStr(Objects.isNull(mergeOrderModel.getOrderDate()) ? null : df.format(mergeOrderModel.getOrderDate()));
        mergeOrderModel.setScanTimeStr(mergeOrderModel.getScanTime() == null ? "" : df.format(mergeOrderModel.getScanTime()));
        mergeOrderModel.setModifieddateStr(df.format(mergeOrderModel.getModifieddate()));
//        String isToDrpName = OcBorderListEnums.IsToDrpOrderEnum.getTextByVal(mergeOrderModel.getIsTodrp());
//        mergeOrderModel.setIsToDrpName(isToDrpName);

    }

    /**
     * 框架格式返回
     *
     * @param dataList
     * @return
     */
    private static JSONArray getFrameDataFormat(List<JSONObject> dataList) {
        JSONArray array = new JSONArray();
        if (dataList != null && dataList.size() > 0) {
            for (JSONObject emp : dataList) {
                Set<String> keySet = emp.keySet();
                JSONObject json = new JSONObject();
                for (String key : keySet) {
                    JSONObject val = new JSONObject();
                    val.put("val", emp.get(key));
                    json.put(key.toUpperCase(), val);
                }
                array.add(json);
            }
        }
        return array;
    }


    /**
     * ES 查询orderby条件
     *
     * @return
     */
    public JSONArray getOrderByKey() {
        JSONArray orderKeys = new JSONArray();
        JSONObject orderByKey = new JSONObject();
        orderByKey.put("asc", false);
        orderByKey.put("name", "CREATIONDATE");
        orderKeys.add(orderByKey);
        return orderKeys;
    }

    /**
     * Es查询 日期过滤
     *
     * @param fixedcolumns
     * @return
     */
    public JSONObject getFilterKey(JSONObject fixedcolumns) {
        JSONObject filterKey = new JSONObject();
        if (fixedcolumns.containsKey("ORDER_DATE")) {
            String orderDate = fixedcolumns.getString("ORDER_DATE");
            String[] orderSplitDate = orderDate.split("~");
            String orderDateResult = convertDate(orderSplitDate[0], orderSplitDate[1]);
            filterKey.put("ORDER_DATE", orderDateResult);
        }
        if (fixedcolumns.containsKey("SCAN_TIME")) {
            String scanTime = fixedcolumns.getString("SCAN_TIME");
            String[] scanSplitTime = scanTime.split("~");
            String scanTimeResult = convertDate(scanSplitTime[0], scanSplitTime[1]);
            filterKey.put("SCAN_TIME", scanTimeResult);
        }
        return filterKey;
    }

    /**
     * 日期转成ES需要的格式
     *
     * @return
     */
    public String convertDate(String begindate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        if (StringUtils.isEmpty(begindate) || StringUtils.isEmpty(endDate)) {
            return "";
        }
        try {
            String result = sdf.parse(begindate).getTime() + "~" + sdf.parse(endDate).getTime();
            return result;

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";

    }

    /**
     * 框架传入的列解析成es查询需要的where条件
     *
     * @param fixedcolumns
     * @return
     */
    public JSONObject getWhereKey(JSONObject fixedcolumns) {

        JSONObject whereKey = new JSONObject();
        //whereKey.put("IS_INTERECEPT", 0); 去除挂起校验 已经确认去除20190807
        if (fixedcolumns.containsKey("ORDER_STATUS")) {
            //订单状态
            JSONArray orderStatusList = fixedcolumns.getJSONArray("ORDER_STATUS");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(orderStatusList)) {
                for (Object orderStatus : orderStatusList) {
                    jsonArray.add(orderStatus.toString().replaceAll("=", ""));
                }
                whereKey.put("ORDER_STATUS", jsonArray);
            }
        } else {
            JSONArray jsonArray = new JSONArray();
//            jsonArray.add(OmsOrderStatus.IN_DISTRIBUTION.toInteger());//配货中
//            jsonArray.add(OmsOrderStatus.CHECKED.toInteger());//已审核
            jsonArray.add(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());//仓库发货
            jsonArray.add(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());//平台发货
            whereKey.put("ORDER_STATUS", jsonArray);
        }
        if (fixedcolumns.containsKey("WMS_CANCEL_STATUS")) {
            JSONArray wmsCancleStatus = fixedcolumns.getJSONArray("WMS_CANCEL_STATUS");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(wmsCancleStatus)) {
                for (Object wmsStatus : wmsCancleStatus) {
                    jsonArray.add(wmsStatus.toString().replaceAll("=", ""));
                }
                whereKey.put("WMS_CANCEL_STATUS", jsonArray);
            }
        } else {
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger());
            jsonArray.add(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger());
            jsonArray.add(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger());
            whereKey.put("WMS_CANCEL_STATUS", jsonArray);
        }
        if (fixedcolumns.containsKey("ID")) {
            JSONArray ids = fixedcolumns.getJSONArray("ID");
            if (CollectionUtils.isNotEmpty(ids)){
                whereKey.put("ID", ids);
            }
        }
        if (fixedcolumns.containsKey("SOURCE_CODE")) {
            String sourceCode = fixedcolumns.getString("SOURCE_CODE");
            if (StringUtils.isNotEmpty(sourceCode)) {
                String sourceCodeReplace = sourceCode.replaceAll("\\s*", "");
                String[] splitSourceCode = sourceCodeReplace.split(",|，");
                JSONArray jsonArray = new JSONArray(Arrays.asList(splitSourceCode));
                whereKey.put("SOURCE_CODE", jsonArray);
            }
        }
        if (fixedcolumns.containsKey("BILL_NO")) {
            String sourceCode = fixedcolumns.getString("BILL_NO");
            if (StringUtils.isNotEmpty(sourceCode)) {
                String sourceCodeReplace = sourceCode.replaceAll("\\s*", "");
                String[] splitSourceCode = sourceCodeReplace.split(",|，");
                JSONArray jsonArray = new JSONArray(Arrays.asList(splitSourceCode));
                whereKey.put("BILL_NO", jsonArray);
            }
        }
        if (fixedcolumns.containsKey("USER_NICK")) {
            whereKey.put("USER_NICK", fixedcolumns.getString("USER_NICK"));
        }
        if (fixedcolumns.containsKey("CP_C_SHOP_ID")) {
            JSONArray shopIdList = fixedcolumns.getJSONArray("CP_C_SHOP_ID");
            whereKey.put("CP_C_SHOP_ID", shopIdList);
        }
        if (fixedcolumns.containsKey("CP_C_PHY_WAREHOUSE_ID")) {
            JSONArray warehouseIdList = fixedcolumns.getJSONArray("CP_C_PHY_WAREHOUSE_ID");
            whereKey.put("CP_C_PHY_WAREHOUSE_ID", warehouseIdList);
        }
        if (fixedcolumns.containsKey("ORDER_TYPE")) {
            JSONArray orderTypeList = fixedcolumns.getJSONArray("ORDER_TYPE");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(orderTypeList)) {
                for (Object orderType : orderTypeList) {
                    jsonArray.add(orderType.toString().replaceAll("=", ""));
                }
                whereKey.put("ORDER_TYPE", jsonArray);
            }
        }
        if (fixedcolumns.containsKey("IS_FORCE")) {
            //平台
            JSONArray flagList = fixedcolumns.getJSONArray("IS_FORCE");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(flagList)) {
                for (Object flagType : flagList) {
                    jsonArray.add(flagType.toString().replaceAll("=", ""));
                }
                whereKey.put("IS_FORCE", jsonArray);
            }
        }
        if (fixedcolumns.containsKey("CP_C_LOGISTICS_ID")) {
            // 物流
            JSONArray flagList = fixedcolumns.getJSONArray("CP_C_LOGISTICS_ID");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(flagList)) {
                for (Object flagType : flagList) {
                    jsonArray.add(flagType.toString().replaceAll("=", ""));
                }
                whereKey.put("CP_C_LOGISTICS_ID", jsonArray);
            }
        }
        //是否手工新增 新增查询条件
        if (fixedcolumns.containsKey("IS_ADD")) {
            //Integer isAdd = fixedcolumns.getInteger("IS_ADD");
            JSONArray flagList = fixedcolumns.getJSONArray("IS_ADD");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(flagList)) {
                for (Object flagType : flagList) {
                    jsonArray.add(flagType.toString().replaceAll("=", ""));
                }
            }
            if (jsonArray.size() == 1) {
                Integer isAdd = Integer.parseInt((String) jsonArray.get(0));
                if (isAdd == 1) {
                    whereKey.put("ORDER_SOURCE", OcOrderTagEum.TAG_HAND.getVal());
                } else {
                    whereKey.put("ORDER_SOURCE", "!=" + OcOrderTagEum.TAG_HAND.getVal());
                }
            }


        }
        //增加交易平台查询条件
        if (fixedcolumns.containsKey("PLATFORM")) {
            JSONArray flagList = fixedcolumns.getJSONArray("PLATFORM");
            JSONArray jsonArray = new JSONArray();
            if (CollectionUtils.isNotEmpty(flagList)) {
                for (Object flagType : flagList) {
                    jsonArray.add(flagType.toString().replaceAll("=", ""));
                }
                whereKey.put("PLATFORM", jsonArray);
            }
        }
        /**
         * 失败原因
         */
        if (fixedcolumns.containsKey("FORCE_SEND_FAIL_REASON")) {
            whereKey.put("FORCE_SEND_FAIL_REASON", "*" + fixedcolumns.getString("FORCE_SEND_FAIL_REASON") + "*");
        }
        return whereKey;

    }
}