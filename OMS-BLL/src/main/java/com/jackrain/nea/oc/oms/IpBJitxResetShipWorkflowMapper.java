package com.jackrain.nea.oc.oms;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJitxResetShipWorkflow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface IpBJitxResetShipWorkflowMapper extends ExtentionMapper<IpBJitxResetShipWorkflow> {

    @SelectProvider(type = IpBJitxResetShipWorkflowMapper.SqlProvider.class, method = "selectByNodeSql")
    List<IpBJitxResetShipWorkflow> selectByNode(
            @Param(value = "nodeName") String nodeName, @Param(value = "tableName") String tableName,
            @Param(value = "where") String where, @Param(value = "order") String order,
            @Param(value = "limit") int limit);

    @Slf4j
    class SqlProvider {
        public String selectByNodeSql(@Param(value = "nodeName") String nodeName, @Param(value = "tableName") String tableName,
                                      @Param(value = "where") String where, @Param(value = "order") String order,
                                      @Param(value = "limit") int limit) {
            log.info("IpBJitxResetShipWorkflowMapper selectByNodeSql begin");
            if (StringUtils.isEmpty(nodeName) || StringUtils.isEmpty(tableName)) {
                log.info("DrdsSql nodeName或taskTableName 没有值！参数:{}，{}", nodeName, tableName);
                return null;
            }
            limit = limit != 0 ? limit : 1000;
            StringBuilder limitStr = new StringBuilder(" LIMIT ").append(limit);

            StringBuilder sql = new StringBuilder();
            sql.append("/*!TDDL:NODE=").append(nodeName).append("*/")
                    .append("select * from ").append(tableName);
            if (StringUtils.isNotEmpty(where)) {
                sql.append(where);
            }
            if (StringUtils.isNotEmpty(order)) {
                sql.append(order);
            }
            sql.append(limitStr);
            if (log.isDebugEnabled()) {
                log.debug("DrdsSql sql :{}", sql.toString());
            }
            return sql.toString();
        }
    }
}