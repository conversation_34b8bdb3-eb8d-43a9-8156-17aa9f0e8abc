package com.jackrain.nea.oc.oms.services.directreport;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.log.service.LogCommonService;
import com.jackrain.nea.oc.oms.mapper.OcBDirectReportOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBDirectReportOrderStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgNewRpcService;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 直发预占作废
 *
 * <AUTHOR>
 * @since 2024-11-29 11:07
 */
@Slf4j
@Service
public class OcBDirectReportOrderVoidService {
    @Resource
    private OcBDirectReportOrderMapper ocBDirectReportOrderMapper;

    @Resource
    private SgNewRpcService sgNewRpcService;

    @Resource
    private LogCommonService logCommonService;


    /**
     * 库存释放时间小于当前时间&&（已审核||部分履约）的直发预占单进行作废
     */
    public void autoBatchVoidOrder() {
        List<Object> releaseIdList = ocBDirectReportOrderMapper.selectObjs(new QueryWrapper<OcBDirectReportOrder>()
                .select("id").lambda()
                .eq(BaseModel::getIsactive, YesNoEnum.Y.getKey())
                .lt(OcBDirectReportOrder::getAutoReleaseTime, new Date())
                .in(OcBDirectReportOrder::getStatus, Arrays.asList(OcBDirectReportOrderStatusEnum.AUDITED.getValue(),
                        OcBDirectReportOrderStatusEnum.PARTIALLY_FULFILLED.getValue())));
        if (CollectionUtils.isEmpty(releaseIdList)) {
            return;
        }

        for (Object id : releaseIdList) {
            voidOrder((Long) id, SystemUserResource.getRootUser());
        }
    }

    /**
     * 作废-加锁
     */
    @Transactional(rollbackFor = {Exception.class})
    public void voidOrder(Long objId, User user) {
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(BllRedisKeyResources.buildDirectReportOptLockKey(objId));
        try {
            if (redisLock.tryLock(1, TimeUnit.MINUTES)) {
                doVoidOrder(objId, user);
            } else {
                throw new NDSException("请勿同时操作，建议稍后再试");
            }
        } catch (InterruptedException e) {
            log.warn(LogUtil.format("直发预占加锁失败，请联系值班人员,异常信息:{}",
                    "DirectReportOptLockKey.error"), Throwables.getStackTraceAsString(e));
            throw new NDSException("加锁失败，请联系值班人员");
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 作废
     */
    private void doVoidOrder(Long objId, User user) {
        OcBDirectReportOrder main = ocBDirectReportOrderMapper.selectById(objId);
        if (main == null || YesNoEnum.N.getKey().equals(main.getIsactive())) {
            throw new NDSException("未找到有效记录");
        }

        if (YesNoEnum.N.getKey().equals(main.getIsactive())
                || OcBDirectReportOrderStatusEnum.FULFILLED.getValue().equals(main.getStatus())) {
            log.info(LogUtil.format("已作废或全部履约状态的单据不能作废：{}",
                    "OcBDirectReportOrderVoidService.voidByFi"), objId);
            throw new NDSException("已作废或全部履约状态的单据不能作废");
        }

        /*记录作废人及时间相关信息*/
        main.setCloseUserId(user.getId().longValue());
        main.setCloseTime(new Date());

        /*未审核直接标记不可用状态*/
        if (OcBDirectReportOrderStatusEnum.UN_AUDITED.getValue().equals(main.getStatus())) {
            main.setIsactive(YesNoEnum.N.getKey());
            OmsModelUtil.setDefault4Upd(main, user);
            ocBDirectReportOrderMapper.updateById(main);
        } else if (OcBDirectReportOrderStatusEnum.AUDITED.getValue().equals(main.getStatus())
                || OcBDirectReportOrderStatusEnum.PARTIALLY_FULFILLED.getValue().equals(main.getStatus())) {
            /*已审核||部分履约需要调用库存中心释放占用，标记不可用*/
            main.setIsactive(YesNoEnum.N.getKey());
            OmsModelUtil.setDefault4Upd(main, user);
            ocBDirectReportOrderMapper.updateById(main);

            sgNewRpcService.directReportOrderVoid(main, user);
        }
        OcBOperationLog ocBOperationLog = logCommonService.getOperationLog(StConstant.OC_B_DIRECT_REPORT_ORDER,
                OperationTypeEnum.VOID.getOperationValue(), objId, "直发预占单", "",
                "", "", user);
        logCommonService.insertLog(ocBOperationLog);
    }

}
