package com.jackrain.nea.oc.oms.permission;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.BasePermission;
import com.jackrain.nea.oc.oms.model.relation.DataPermission;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @author: xiWen.z
 * create at: 2019/8/23 0023
 */
@Slf4j
@Component
public class OcOrderAuthorityMgtService {

    @Autowired
    private CpRpcService cpRpcService;

    /**
     * get permission
     *
     * @param upm       UserPermission
     * @param tableName String
     * @param user      User
     * @return UserPermission
     */
    public UserPermission getCurrentUserPermission(UserPermission upm, String tableName, User user) {
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Boolean isPermissionOpen = config.getPropertyBoolean("oms.order.custom.permission");
        if (isPermissionOpen) {
            return new UserPermission();
        }
        AssertUtil.assertException(user == null, "当前操作用户不存在");
        if (user.isAdmin()) {
            return new UserPermission();
        }
        Map<String, BasePermission> basePermission = null;
        if (upm != null) {
            basePermission = upm.getBasePermission();
        }
        BasePermission cntPem = null;
        if (basePermission != null) {
            cntPem = basePermission.get(tableName);
            if (cntPem != null) {
                int version = upm.getVersion();
                if (version > 1) {
                    cntPem = null;
                    upm = null;
                } else {
                    upm.setVersion(++version);
                }
            }
        }
        // 不缓存,数据. 暂写
        upm = null;
        if (upm == null || cntPem == null) {
            try {
                upm = getAuthorityInit(tableName, user, null);
                if (upm == null) {
                    log.error(LogUtil.format("getCurrentUserPermission.Permission Is Null"));
                    throw new NDSException("获取用户权限失败");
                }
            } catch (Exception e) {
                log.error(LogUtil.format("getCurrentUserPermission.Permission.Exception: {}"), Throwables.getStackTraceAsString(e));
                throw new NDSException("获取用户权限异常");
            }
        }
        return upm;
    }

    /**
     * 权限初始化
     *
     * @param tableName tableName
     * @param usr       User
     * @param pemObj    UserPermission
     */
    private UserPermission getAuthorityInit(String tableName, User usr, UserPermission pemObj) {

        UserPermission userPermission;
        if (pemObj != null) {
            userPermission = pemObj;
        } else {
            userPermission = new UserPermission();
        }
        JSONObject jsnObj;
        try {
            ValueHolderV14 perVh = cpRpcService.getAllPermissionService(tableName, usr);
            if (perVh == null) {
                throw new NDSException("获取用户权限为空");
            } else if (ResultCode.FAIL == perVh.getCode()) {
                throw new NDSException("获取用户权限失败");
            }
            jsnObj = perVh.toJSONObject();
        } catch (Exception e) {
            log.error(LogUtil.format("getAuthorityInit.RpcInvoke CpRpcGetAllPermissionService.Exception: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("获取用户权限异常");
        }

        /**
         * 权限处理
         */
        return arrangementPermission(jsnObj, tableName, userPermission);
    }

    /**
     * 整理权限
     */
    private UserPermission arrangementPermission(JSONObject jsnObj, String tableName, UserPermission userPermission) {

        if (jsnObj != null) {
            String dtaKey = "data";
            JSONObject data = jsnObj.getJSONObject(dtaKey);
            if (data == null) {
                return null;
            }

            /**
             * 1. 敏感列处理
             */
            String colKey = "filterColums";
            JSONObject colPem = data.getJSONObject(colKey);
            Set<String> grantColumn = getGrantColumn(colPem, userPermission);
            userPermission.setForbiddenColumns(grantColumn);

            /**
             * 2. 功能权限
             */
            String baseKey = "filterPermission";
            JSONObject basePem = data.getJSONObject(baseKey);
            Set<String> priKeys = basePem.keySet();
            Map<String, BasePermission> baseMap = userPermission.getBasePermission();
            if (baseMap == null) {
                baseMap = new HashMap<>();
            }

            BasePermission basePermission = new BasePermission();
            String brandKey = "PS_C_BRAND_ID";
            String storeKey = "CP_C_MAIN_STORE_ID";
            String warehouseKey = "CP_C_WAREHOUSE_ID";
            String shopKey = "CP_C_SHOP_ID";
//            String logisticsKey = "CP_C_LOGISTICS_ID";
            if (basePem == null || priKeys == null || priKeys.size() < OcBOrderConst.IS_STATUS_IY) {
                basePermission.setBrandPermissions(null);
                basePermission.setShopPermissions(null);
                basePermission.setStorePermissions(null);
                basePermission.setWarehousePermissions(null);
                basePermission.setLogisticsKeyPermissions(null);
            } else {
                if (priKeys.contains(brandKey)) {
                    JSONArray brand = basePem.getJSONArray(brandKey);
                    if (brand == null || brand.size() < OcBOrderConst.IS_STATUS_IY) {
                        basePermission.setBrandPermissions(new ArrayList<>());
                    } else {
                        List<DataPermission> brandList = JSON.parseArray(brand.toJSONString(), DataPermission.class);
                        basePermission.setBrandPermissions(brandList);
                    }
                } else {
                    basePermission.setBrandPermissions(null);
                }
                if (priKeys.contains(storeKey)) {
                    JSONArray store = basePem.getJSONArray(storeKey);
                    if (store == null || store.size() < OcBOrderConst.IS_STATUS_IY) {
                        basePermission.setStorePermissions(new ArrayList<>());
                    } else {
                        List<DataPermission> storeList = JSON.parseArray(store.toJSONString(), DataPermission.class);
                        basePermission.setStorePermissions(storeList);
                    }
                } else {
                    basePermission.setStorePermissions(null);
                }
                if (priKeys.contains(warehouseKey)) {
                    JSONArray warehouse = basePem.getJSONArray(warehouseKey);
                    if (warehouse == null || warehouse.size() < OcBOrderConst.IS_STATUS_IY) {
                        basePermission.setWarehousePermissions(new ArrayList<>());
                    } else {
                        List<DataPermission> wareList = JSON.parseArray(warehouse.toJSONString(), DataPermission.class);
                        basePermission.setWarehousePermissions(wareList);
                    }
                } else {
                    basePermission.setWarehousePermissions(null);
                }
                if (priKeys.contains(shopKey)) {
                    JSONArray shop = basePem.getJSONArray(shopKey);
                    if (shop == null || shop.size() < OcBOrderConst.IS_STATUS_IY) {
                        basePermission.setShopPermissions(new ArrayList<>());
                    } else {
                        List<DataPermission> shopList = JSON.parseArray(shop.toJSONString(), DataPermission.class);
                        basePermission.setShopPermissions(shopList);
                    }
                } else {
                    basePermission.setShopPermissions(null);
                }
//                if (priKeys.contains(logisticsKey)) {
//                    JSONArray logistic = basePem.getJSONArray(logisticsKey);
//                    if (logistic == null || logistic.size() < OcBOrderConst.IS_STATUS_IY) {
//                        basePermission.setLogisticsKeyPermissions(new ArrayList<>());
//                    } else {
//                        List<DataPermission> logisticsList = JSON.parseArray(logistic.toJSONString(), DataPermission.class);
//                        basePermission.setLogisticsKeyPermissions(logisticsList);
//                    }
//                } else {
//                    basePermission.setLogisticsKeyPermissions(null);
//                }
            }
            baseMap.put(tableName, basePermission);
            userPermission.setBasePermission(baseMap);
            return userPermission;
        }
        return null;
    }

    /**
     * 敏感列权限级别
     *
     * @param pemObj 敏感列
     * @return 返回禁读列
     */
    private Set<String> getGrantColumn(JSONObject pemObj, UserPermission userPermission) {
        if (pemObj != null) {
            Set<String> colKeys = pemObj.keySet();
            int ln = colKeys.size();
            if (ln < OcBOrderConst.IS_STATUS_IY) {
                return null;
            }
            List<DataPermission> colList = new ArrayList<>();
            Set<String> forbidKeys = new HashSet<>();
            for (String colKey : colKeys) {
                JSONObject colJsnObj = pemObj.getJSONObject(colKey);
                if (colJsnObj == null) {
                    continue;
                }
                String isRead = colJsnObj.getString("isRead");
                String isModify = colJsnObj.getString("isModify");
                String ecode = colJsnObj.getString("ecode");
                Long columnId = colJsnObj.getLong("columnId");

                DataPermission colPem = new DataPermission();
                colPem.setId(columnId).setEcode(ecode).setIsRead(isRead).setIsWrite(isModify);
                colList.add(colPem);

                int v = this.dealGrant(isRead, isModify);
                if (v < OcBOrderConst.IS_STATUS_IN) {
                    forbidKeys.add(ecode.toUpperCase());
                }
            }
            userPermission.setSensitiveColumns(colList);
            return forbidKeys.size() < OcBOrderConst.IS_STATUS_IY ? null : forbidKeys;
        }
        return null;
    }

    /**
     * 列表权限级别
     *
     * @param isRead   read
     * @param isModify modify
     */
    private int dealGrant(String isRead, String isModify) {

        /**
         * 1. 不配置默认没有权限: isRead = null/N
         * 2. 配置,则根据配置判断
         * 3. -1:无, 1: 查看 , 2: 编辑
         */
        if (OcBOrderConst.IS_ACTIVE_YES.equals(isModify)) {
            return 2;
        }
        if (OcBOrderConst.IS_ACTIVE_YES.equals(isRead)) {
            return 1;
        }
        return -1;
    }

    /**
     * 重组
     *
     * @param keys
     * @param basePem
     * @param wKey
     */
    public void recombinationSearchCdt(List<String> keys, BasePermission basePem, JSONObject wKey) {

        for (String key : keys) {
            List<DataPermission> pemList;
            switch (key) {
                case "CP_C_SHOP_ID":
                    pemList = basePem.getShopPermissions();
                    break;
                case "CP_C_PHY_WAREHOUSE_ID":
                    pemList = basePem.getWarehousePermissions();
                    break;
                case "CP_C_PHY_WAREHOUSE_IN_ID":
                    pemList = basePem.getWarehousePermissions();
                    break;
                case "CP_C_STORE_ID":
                    pemList = basePem.getStorePermissions();
                    break;
                case "IN_STORE_ID":
                    pemList = basePem.getStorePermissions();
                    break;
                case "PS_C_BRAND_ID":
                    pemList = basePem.getBrandPermissions();
                    break;
//                case "CP_C_LOGISTICS_ID":
//                    pemList = basePem.getLogisticsKeyPermissions();
//                    break;
                default:
                    continue;
            }
            JSONArray wAry = wKey.getJSONArray(key);
            // 没配此权限
            if (pemList == null) {
                continue;
            } else if (pemList.size() < OcBOrderConst.IS_STATUS_IY) {
                wKey.put(key, -1);
            } else {
                JSONArray pemAry = new JSONArray();
                for (DataPermission p : pemList) {
                    if (OcBOrderConst.IS_ACTIVE_YES.equals(p.getIsWrite())
                            || OcBOrderConst.IS_ACTIVE_YES.equals(p.getIsRead())) {
                        pemAry.add(p.getId());
                    }
                }
                if (wAry == null || wAry.size() < OcBOrderConst.IS_STATUS_IY) {
                    wKey.put(key, pemAry);
                } else {
                    for (int i = 0; i < wAry.size(); i++) {
                        long id = wAry.getLongValue(i);
                        if (pemAry.contains(id)) {
                            continue;
                        }
                        wAry.remove(i--);
                    }
                    if (wAry.size() < OcBOrderConst.IS_STATUS_IY) {
                        wKey.put(key, -1);
                    } else {
                        wKey.put(key, wAry);
                    }
                }
            }
        } // end of for
    }

    /**
     * 全渠道订单.子表待查字段
     *
     * @return jsonObject
     */
    public JSONObject getOcOrderItemField(Set<String> sets) {
        String s = "`ID` proId,PS_C_SKU_ID skuId,PS_C_PRO_ID psCproId,PS_C_CLR_ID clrsId,"
                + "PS_C_CLR_ECODE clrsEcode,PS_C_CLR_ENAME clrs,PS_C_SIZE_ID sizeId,PS_C_SIZE_ECODE sizeEcode,"
                + "PS_C_SIZE_ENAME sizes,QTY qty,PRICE price,REAL_AMT realAmt, STANDARD_WEIGHT weight, "
                + "PS_C_SKU_ECODE skuEcode, PS_C_PRO_ENAME proEname, AMT_REFUND amtRefund,  BARCODE barCode,"
                + "price_list priceList, OC_B_ORDER_ID orderId, SKU_SPEC skuSpec,  PS_C_PRO_ECODE ecode, OOID oOId,"
                + "PRICE_SETTLE priceSettle, TOT_PRICE_SETTLE totPriceSettle, PRICE_ACTUAL priceActual, "
                + "QTY_LOST qtyLost, `SEX` sex, `QTY_REFUND` qtyRefund, `REFUND_STATUS` refundStatus, "
                + "`PRICE_TAG` reserveDecimal02,`RESERVE_DECIMAL03` reserveDecimal03,is_gift IS_Gift, "
                + "gift_type GIFT_TYPE, gift_relation GIFT_RELATION, ps_c_sku_pt_ecode PS_C_SKU_PT_ECODE, "
                + "pt_pro_name PT_PRO_NAME, ps_c_sku_ename PS_C_SKU_ENAME, pro_type PRO_TYPE, "
                + "qty_has_return QTY_HAS_RETURN, qty_return_apply QTY_RETURN_APPLY";
        String[] s1 = s.split(",");
        JSONObject fldJsnObj = new JSONObject();
        for (String s2 : s1) {
            String s3 = s2.replace("`", "");
            String[] s4 = s3.trim().split("\\s");
            fldJsnObj.put(s4[0], s4[1]);
        }
        // filter field
        List<String> lst = new ArrayList<>();
        if (sets != null && sets.size() > OcBOrderConst.IS_STATUS_IN) {
            Set<String> keySets = fldJsnObj.keySet();
            for (String k : keySets) {
                if (sets.contains(k)) {
                    lst.add(k);
                }
            }
        }
        for (String x : lst) {
            fldJsnObj.remove(x);
        }
        return fldJsnObj;
    }

}
