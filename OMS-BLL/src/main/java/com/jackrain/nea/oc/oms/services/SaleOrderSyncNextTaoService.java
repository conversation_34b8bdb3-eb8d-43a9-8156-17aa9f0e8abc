package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.TaskParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.request.o2o.SaleOrderRequest;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Desc : 销售订单同步
 * <AUTHOR> xiWen
 * @Date : 2020/8/22
 */
@Slf4j
@Component
public class SaleOrderSyncNextTaoService {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    public SaleOrderRequest saleOrderTransfer(OcBOrderParam param, Map<Long, CpCPlatform> pfMap, Map<Long,
            CpCPhyWarehouse> phyMap, User user) {

        Map<Long, String> errorMap = new HashMap<>();
        SaleOrderRequest saleOrderRequest = null;
        Long orderId = null;
        try {

            OcBOrder order = param.getOcBOrder();
            assertNotNull(order, "转换入参订单为null");
            orderId = order.getId();

            List<OcBOrderItem> orderItemList = param.getOrderItemList();
            assertNotEmpty(orderItemList, "转换入参订单明细为空");

            assertNotNull(order.getPlatform(), "平台Id为null");

            CpCPlatform platform = pfMap.get(Long.valueOf(order.getPlatform()));
            assertNotNull(platform, "平台信息未获取到");

            CpCPhyWarehouse phyWarehouse = phyMap.get(order.getCpCPhyWarehouseId());
            assertNotNull(phyWarehouse, "仓库信息未获取到");

            // 2. 赋值
            saleOrderRequest = setOrder(order, errorMap, platform, phyWarehouse);
            assertNotNull(saleOrderRequest, "转换结果null,错误信息-" + JSON.toJSONString(errorMap));

            // 2.2 子明细
            List<SaleOrderRequest.SaleOrderItem> itemRequests = new ArrayList<>();
            for (OcBOrderItem item : orderItemList) {
                SaleOrderRequest.SaleOrderItem saleItem = setOrderItem(item, order.getId(), errorMap);
                assertNotNull(saleItem, "明细转换结果null明细Id,错误信息-" + JSON.toJSONString(errorMap));
                itemRequests.add(saleItem);
            }
            if (CollectionUtils.isEmpty(itemRequests)) {
                throw new NDSException("明细集转换结果为空");
            }
            saleOrderRequest.setItem(itemRequests);

            // 2.3 扩展属性
            JSONObject extendProps = generateJsonProps(param);
            if (extendProps == null) {
                errorMap.put(order.getId(), "扩展属性转换结果null");
                return null;
            }

            saleOrderRequest.setExtend_props(extendProps);

        } catch (Exception ex) {
            String msg = ExceptionUtil.getMessage(ex);
            log.error("o2o订单转换.订单Id-{}, 错误信息-{}", orderId, msg);
            logError("saleOrderTransfer.", msg);
            recordOrderLog(param.getOcBOrder(), msg, user);
            return null;
        } finally {
            formatLocal.remove();
        }

        if (errorMap.size() > 0) {
            logDebug("saleOrderTransfer.本次错误信息记录-");
        }

        return saleOrderRequest;

    }

    private ThreadLocal<SimpleDateFormat> formatLocal = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));


    private void recordOrderLog(OcBOrder ocBOrder, String msg, User user) {
        try {

            StringBuilder logMsg = new StringBuilder("传POS转换失败.");
            if (msg != null) {
                if (msg.length() > 200) {
                    logMsg.append(msg, 0, 199);
                } else {
                    logMsg.append(msg);
                }
            }
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_TO_POS.getKey(),
                    logMsg.toString(), "", "", user);

        } catch (Exception ex) {
            log.error("订单转换.订单Id-{},记录日志发生异常,错误信息-{}", ocBOrder.getId(), ExceptionUtil.getMessage(ex));
        }
    }

    /**
     * @param order
     * @param errorMap
     * @return
     */
    private SaleOrderRequest setOrder(OcBOrder order, Map<Long, String> errorMap, CpCPlatform platform, CpCPhyWarehouse phyWarehouse) {

        try {
            SaleOrderRequest saleOrderRequest = new SaleOrderRequest();
            SimpleDateFormat sdf = formatLocal.get();
            saleOrderRequest.setOrderBillCode(order.getSgBOutBillNo());
            saleOrderRequest.setOrderWebCod(order.getBillNo());
            saleOrderRequest.setShopCode(phyWarehouse.getEcode());
            saleOrderRequest.setBillTime(sdf.format(order.getOrderDate()));

            illegalVal(order.getProductAmt(), "ProductAmt");
            illegalVal(order.getOrderAmt(), "OrderAmt");
            illegalVal(order.getProductDiscountAmt(), "ProductDiscountAmt");
            illegalVal(order.getQtyAll(), "QtyAll");

            saleOrderRequest.setQuantity(String.valueOf(order.getQtyAll().setScale(0, RoundingMode.DOWN)));
            // 1. sum(price)  总平台销售金额:
            saleOrderRequest.setMoney(String.valueOf(order.getProductAmt().setScale(4, RoundingMode.DOWN)));
            // 2. sum(realAmt)
            saleOrderRequest.setRealMoney(String.valueOf(order.getOrderAmt().setScale(4, RoundingMode.DOWN)));
            // 3. 折扣金额	sum(成交金额)- sum(平台售价)
            BigDecimal disAmt = order.getOrderAmt().subtract(order.getProductAmt());
            saleOrderRequest.setDiscount(String.valueOf(disAmt.setScale(4, RoundingMode.DOWN)));

            // 支付方式：0-线上支付1-线下支付,	支付方式	固定‘线上支付’
            saleOrderRequest.setPaymethod("0");
            saleOrderRequest.setName(order.getReceiverName());
            String phone = order.getReceiverMobile();
            if (StringUtils.isBlank(phone)) {
                phone = order.getReceiverPhone();
                if (StringUtils.isBlank(phone)) {
                    throw new NDSException("手机号码, 电话号码不允许全部为空");
                }
            }
            saleOrderRequest.setPhone(phone);
            saleOrderRequest.setProvince(order.getCpCRegionProvinceEname());
            saleOrderRequest.setCity(order.getCpCRegionCityEname());
            saleOrderRequest.setDistrict(order.getCpCRegionAreaEname());
            saleOrderRequest.setAddres("");
            saleOrderRequest.setShippingAddress(order.getReceiverAddress());
            saleOrderRequest.setCreater("");
            saleOrderRequest.setCreationDate("");
            saleOrderRequest.setNote("");
            saleOrderRequest.setShippingCode("");
            saleOrderRequest.setShippingSn("");
            saleOrderRequest.setSalerEmployeeNo("");
            saleOrderRequest.setThAct("false");
            saleOrderRequest.setSystem("R");

            saleOrderRequest.setLypt(platform.getEcode());

            saleOrderRequest.setLyzdDm(order.getCpCShopEcode());
            saleOrderRequest.setLyzdMc(order.getCpCShopTitle());

            saleOrderRequest.setLyorgDm(platform.getEcode());
            saleOrderRequest.setLyorgMc(platform.getEname());

            int i = initInteger(order.getIsSameCityPurchase());
          //   String buyOrderShopCode = order.getDeliveryStoreCode();
            String buyOrderShopCode = "";
            if (i == 0) {
                buyOrderShopCode = "default";
            }
            saleOrderRequest.setXdzdDm(buyOrderShopCode);
            saleOrderRequest.setGkly(order.getBuyerMessage());
            saleOrderRequest.setKfbz(order.getSellerMemo());
            saleOrderRequest.setPosOuterCode("000");

            saleOrderRequest.setCustomerid("");

            return saleOrderRequest;
        } catch (Exception ex) {
            errorMap.put(order.getId(), ExceptionUtil.getMessage(ex));
            log.error(LogUtil.format("订单转换参数时发生异常,异常信息为:{}", order.getId()), Throwables.getStackTraceAsString(ex));

            return null;
        }
    }


    /**
     * 明细转换
     *
     * @param item
     * @return
     */
    private SaleOrderRequest.SaleOrderItem setOrderItem(OcBOrderItem item, Long orderId, Map<Long, String> errorMap) {
        try {

            SaleOrderRequest.SaleOrderItem saleOrderItem = new SaleOrderRequest.SaleOrderItem();
            saleOrderItem.setStatus("0");
            // 0. 校验
            illegalVal(item.getPrice(), "Price");
            illegalVal(item.getRealAmt(), "RealAmt");
            illegalVal(item.getQty(), "Qty");
            if (item.getQty().compareTo(BigDecimal.ZERO) < 1) {
                throw new NDSException("数量不合法");
            }

            // 1. 成交金额	成交金额
            saleOrderItem.setAmount(String.valueOf(item.getRealAmt().setScale(4, RoundingMode.DOWN)));

            BigDecimal goodsAmt = item.getPrice().multiply(item.getQty());
            // 2. 参考金额	平台售价*数量
            saleOrderItem.setReferenceAmount(String.valueOf(goodsAmt.setScale(4, RoundingMode.DOWN)));
            // 3.
            saleOrderItem.setQuantity(String.valueOf(item.getQty().setScale(0, RoundingMode.DOWN)));

            // 4.折扣金额	成交金额-平台售价*数量
            BigDecimal subtract = item.getRealAmt().subtract(goodsAmt);
            saleOrderItem.setDiscount(String.valueOf(subtract.setScale(4, RoundingMode.DOWN)));

            // 5.	单价	成交单价
            BigDecimal singleAmt = item.getRealAmt().divide(item.getQty(), 4, BigDecimal.ROUND_DOWN);
            saleOrderItem.setPrice(String.valueOf(singleAmt));

            // 6. 	参考价	平台售价
            saleOrderItem.setReferencePrice(String.valueOf(item.getPrice().setScale(4, RoundingMode.DOWN)));

            saleOrderItem.setSizeCode(item.getPsCSizeEcode());
            saleOrderItem.setColorCode(item.getPsCClrEcode());
            saleOrderItem.setGoodsCode(item.getPsCProEcode());
            //    saleOrderItem.setSkuCode(item.getPsCSkuEcode());
            saleOrderItem.setSubOrderID(String.valueOf(item.getId()));

            return saleOrderItem;
        } catch (Exception ex) {
            errorMap.put(orderId, ExceptionUtil.getMessage(ex));
            log.error(LogUtil.format("订单明细转换参数时发现异常数据,订单Id-{}, 明细Id-{},异常信息为:{}", orderId),orderId, item.getId(), Throwables.getStackTraceAsString(ex));

            return null;
        }
    }

    /**
     * 扩展属性
     *
     * @param order
     * @return
     */
    private SaleOrderRequest.ExtendProps setExtendProps(OcBOrder order) {
        SaleOrderRequest.ExtendProps extendProps = new SaleOrderRequest.ExtendProps();
        int i = order.getIsSameCityPurchase() == null ? 0 : order.getIsSameCityPurchase();
        extendProps.setIsPerfectP(i);
        return extendProps;
    }

    /**
     * 空值校验
     *
     * @param obj
     * @param msg
     */
    private void assertNotNull(Object obj, String msg) {
        if (obj == null) {
            throw new NDSException(msg);
        }
    }

    private void assertNotEmpty(List<OcBOrderItem> list, String msg) {
        if (CollectionUtils.isEmpty(list)) {
            throw new NDSException(msg);
        }
    }

    private void illegalVal(BigDecimal bigDecimal, String fieldName) {
        if (bigDecimal == null) {
            throw new NDSException(fieldName + ": null ");
        }
    }

    private int initInteger(Integer integer) {
        return integer == null ? 0 : integer;
    }

    private int getIsPerfectP(OcBOrder order) {
        int isSameCity = order.getIsSameCityPurchase() == null ? 0 : order.getIsSameCityPurchase();
        return isSameCity == 1 ? 1 : 0;
    }

    /**
     * 扩展属性
     *
     * @param param
     * @return
     */
    private JSONObject generateJsonProps(OcBOrderParam param) {

        OcBOrder order = param.getOcBOrder();
        try {

            JSONObject jsn = new JSONObject();
            jsn.put("is_perfect_p", getIsPerfectP(order));
            jsn.put("tid", order.getTid());
            List<OcBOrderItem> orderItemList = param.getOrderItemList();

            boolean isFind = false;
            for (OcBOrderItem item : orderItemList) {
                String anchorId = item.getAnchorId();
                if (StringUtils.isNotBlank(anchorId)) {
                    jsn.put("anchor_id", item.getAnchorId());
                    jsn.put("anchor_name", item.getAnchorName());
                    isFind = true;
                    break;
                }
            }
            if (!isFind) {
                jsn.put("anchor_id", "");
                jsn.put("anchor_name", "");
            }
            return jsn;
        } catch (Exception ex) {
            log.error(LogUtil.format("订单扩展属性转换参数时异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * 异常信息记录
     *
     * @param id
     * @param msg
     * @param errorMap
     * @param sql
     */
    private void recordError(Long id, String msg, Map<Long, String> errorMap, TaskParam sql) {
        errorMap.put(id, msg);
        sql.getValidKeys().remove(id);
    }

    /**
     * level debug recorder
     *
     * @param msg log message
     */
    protected void logDebug(String msg, Object... params) {

        if (log.isDebugEnabled()) {
            if (params.length == 0) {
                log.debug(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString());
            } else {
                log.debug(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString(), params);
            }
        }
    }

    /**
     * level error recorder
     *
     * @param msg    message or format string
     * @param params print values
     */
    protected void logError(String msg, Object... params) {

        if (params.length == 0) {
            log.error(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString());
        } else {
            log.error(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString(), params);
        }
    }

    /**
     * 查询所有平台信息
     *
     * @return id, 平台信息
     */
    private Map<Long, CpCPlatform> rpcQueryPlatForm() {

        try {

            List<CpCPlatform> cpCPlatforms = cpRpcService.queryPlatform(null);
            if (cpCPlatforms == null) {
                return null;
            }
            return cpCPlatforms.stream().collect(Collectors.toMap(CpCPlatform::getId, o -> o));
        } catch (Exception ex) {
            log.error(LogUtil.format("SaleReturnSync4NextTaoService.rpcQueryPlatForm.查询.组装平台信息异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            return null;
        }

    }


}
