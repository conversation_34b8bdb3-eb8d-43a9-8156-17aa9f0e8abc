package com.jackrain.nea.oc.oms.services;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-08-23 09:57
 * @Description: 订单修改业务类型按钮
 */

@Slf4j
@Component
public class OcBOrderUpdateBusinessTypeService {

    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private StCBusinessTypeMapper typeMapper;
    @Autowired
    private OmsOrderLogService orderLogService;

    /**
     * 订单修改业务类型
     *
     * @param obj         入参
     * @param operateUser 用户
     * @return ValueHolder
     */
    public ValueHolder updateBusinessType(JSONObject obj, User operateUser) {

        log.info(LogUtil.format("订单修改业务类型入参:{}",
                "OcBOrderUpdateBusinessType.againOccupyStock"), ObjectUtil.isNull(obj) ? null : JSONObject.toJSONString(obj));

        if (operateUser == null) {
            operateUser = R3SystemUserResource.getSystemRootUser();
        }
        List<OcBOrder> ocOrders = checkParam(obj, operateUser);
        JSONArray ids = obj.getJSONArray("ids");
        List<Long> orderIds = ids.toJavaList(Long.class);

        List<StCBusinessType> stBusinessTypes = typeMapper.selectStCBusinessTypeByCode(OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode());

        if (CollectionUtils.isEmpty(stBusinessTypes)) {
            throw new NDSException(Resources.getMessage("线上奶卡销售信息获取失败！!", operateUser.getLocale()));
        }

        Map<Long, OcBOrder> orderMap = ocOrders.stream().collect(Collectors.toMap(OcBOrder::getId, order -> order, (v1, v2) -> v1));
        JSONArray errorMessage = new JSONArray();
        int fail = 0;
        ValueHolder vh = new ValueHolder();

        for (Long orderId : orderIds) {
            // 给订单加锁
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (!orderMap.containsKey(orderId)) {
                    errorMessage.add(setMessage(orderId, "无有效订单!", ResultCode.FAIL));
                    fail++;
                    continue;
                }
                OcBOrder ocOrder = orderMap.get(orderId);
                if (!checkOrder(ocOrder)) {
                    errorMessage.add(setMessage(orderId, "订单业务类型不支持修改!", ResultCode.FAIL));
                    fail++;
                    continue;
                }
                StCBusinessType stBusinessType = stBusinessTypes.get(0);
                OcBOrder update = new OcBOrder();
                update.setId(orderId);
                update.setBusinessTypeId(stBusinessType.getId());
                update.setBusinessTypeCode(stBusinessType.getEcode());
                update.setBusinessTypeName(stBusinessType.getEname());
                update.setModifierid(operateUser.getId().longValue());
                update.setModifiername(operateUser.getName());
                update.setModifieddate(new Date());
                orderMapper.updateById(update);
                //更新发货状态，插入日志
                String logMsg = "OrderId=" + orderId + "订单修改业务类型为线上奶卡销售";
                orderLogService.addUserOrderLog(orderId, ocOrder.getBillNo(), OrderLogTypeEnum.BUSINESS_TYPE_ECODE.getKey(), logMsg, "",
                        null, null);
            } catch (Exception e) {
                log.error(LogUtil.format("订单修改业务类型失败,OrderId={},异常信息={}", orderId), orderId, Throwables.getStackTraceAsString(e));
                errorMessage.add(setMessage(orderId, e.getMessage(), ResultCode.FAIL));
            } finally {
                redisLock.unlock();
            }
        }

        vh.put("data", errorMessage);

        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("订单修改业务类型成功", operateUser.getLocale()));
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("订单修改业务类型成功" + (ids.size() - fail) + "条，失败了" + fail + "条", operateUser.getLocale()));
        }

        log.info(LogUtil.format("订单修改业务类型出参:{}",
                "OcBOrderUpdateBusinessType.ValueHolder"), JSONObject.toJSONString(vh));
        return vh;
    }

    private JSONObject setMessage(Long orderId, String message, Integer code) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", code);
        jsonObject.put("message", message);
        jsonObject.put("objid", orderId);
        return jsonObject;
    }

    /**
     * 有效订单定义 check
     *
     * @param order 订单
     * @return true 有效 false 无效
     */
    private boolean checkOrder(OcBOrder order) {
        Integer platform = order.getPlatform();
        String businessTypeCode = order.getBusinessTypeCode();
        if (OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD.getCode().equals(businessTypeCode)
                && PlatFormEnum.YOUZAN.getCode().equals(platform)) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    private List<OcBOrder> checkParam(JSONObject obj, User operateUser) {
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", operateUser.getLocale()));
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", operateUser.getLocale()));
        }
        List<Long> orderIds = ids.toJavaList(Long.class);
        List<OcBOrder> ocOrders = orderMapper.selectList(new LambdaQueryWrapper<OcBOrder>()
                .in(OcBOrder::getId, orderIds)
                .eq(OcBOrder::getIsactive, SgConstants.IS_ACTIVE_Y));

        if (CollectionUtils.isEmpty(ocOrders)) {
            throw new NDSException(Resources.getMessage("无有效订单请检查！!", operateUser.getLocale()));
        }
        return ocOrders;
    }
}
