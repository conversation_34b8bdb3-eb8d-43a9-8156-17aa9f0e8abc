package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jackrain.nea.oc.oms.model.table.OcBOrderAppointLogistics;

import java.util.List;

/**
 * @ClassName OcBOrderAppointLogisticsMapperService
 * @Description 指定快递命中的数据
 * <AUTHOR>
 * @Date 2024/4/16 17:12
 * @Version 1.0
 */
public interface OcBOrderAppointLogisticsMapperService extends IService<OcBOrderAppointLogistics> {

    /**
     * 根据订单id 查询匹配到的信息
     *
     * @param orderId
     * @return
     */
    List<OcBOrderAppointLogistics> selectByOrderId(Long orderId);

    /**
     * 根据订单id列表 查询匹配到的信息
     *
     * @param orderIds
     * @return
     */
    List<OcBOrderAppointLogistics> selectByOrderIds(List<Long> orderIds);

    /**
     * 逻辑删除
     *
     * @param orderId
     */
    void deleteByOrderId(Long orderId);

    /**
     * 逻辑删除
     *
     * @param orderIds
     */
    void deleteByOrderIds(List<Long> orderIds);

    /**
     * 查询指定快递的订单
     *
     * @param nodeName
     * @param now
     * @param tableName
     * @return
     */
    List<OcBOrderAppointLogistics> select4CancelAppointLogistics(String now, String tableName);

}
