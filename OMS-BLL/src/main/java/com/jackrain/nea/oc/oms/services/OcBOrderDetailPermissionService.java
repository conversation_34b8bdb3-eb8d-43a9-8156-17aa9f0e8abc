package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.relation.BasePermission;
import com.jackrain.nea.oc.oms.model.relation.DataPermission;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: xiWen.z
 * create at: 2019/8/28 0028
 */
@Component
@Slf4j
public class OcBOrderDetailPermissionService {

    /**
     * 单对象界面.权限
     *
     * @param tn  table name
     * @param usr user
     * @param pms UserPermission
     * @return vh
     */
    public ValueHolderV14 getSingleObjectPermission(String tn, User usr, UserPermission pms) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (usr == null || usr.getId() == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数校验: 用户信息丢失");
            return vh;
        }
        if (tn == null || tn.length() < OcBOrderConst.IS_STATUS_IY) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("参数校验: 表信息丢失", usr.getLocale()));
            return vh;
        }
        if (pms == null) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage("该用户未进行权限控制", usr.getLocale()));
            return vh;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("#getSingleObjectPermission,Before：{}, UserInfo: ID/Name/Ename/tableName："
                    , usr.getId(), usr.getEname(), usr.getEname(), tn), JSON.toJSONString(pms));
        }
        JSONObject jsnObj = new JSONObject();
        JSONArray dataAry = new JSONArray();
        jsnObj.put("SENSITIVE_COLUMNS", dataAry);
        List<DataPermission> colPms = pms.getSensitiveColumns();
        if (colPms != null && colPms.size() > OcBOrderConst.IS_STATUS_IN) {
            dealDataPermission(tn, colPms, dataAry);
        }
        Map<String, BasePermission> basePemMap = pms.getBasePermission();
        BasePermission basePem = null;
        if (basePemMap != null) {
            basePem = basePemMap.get(tn);
        }
        if (basePem != null) {
            jsnObj.put("BRAND_PERMISSIONS", basePem.getBrandPermissions() != null
                    ? basePem.getBrandPermissions() : new JSONArray());
            jsnObj.put("STORE_PERMISSIONS", basePem.getStorePermissions() != null
                    ? basePem.getStorePermissions() : new JSONArray());
            jsnObj.put("WAREHOUSE_PERMISSIONS", basePem.getWarehousePermissions() != null
                    ? basePem.getWarehousePermissions() : new JSONArray());
            jsnObj.put("SHOP_PERMISSIONS", basePem.getShopPermissions() != null
                    ? basePem.getShopPermissions() : new JSONArray());
        } else {
            jsnObj.put("BRAND_PERMISSIONS", new JSONArray());
            jsnObj.put("STORE_PERMISSIONS", new JSONArray());
            jsnObj.put("WAREHOUSE_PERMISSIONS", new JSONArray());
            jsnObj.put("SHOP_PERMISSIONS", new JSONArray());
        }


        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(Resources.getMessage("SUCCESS", usr.getLocale()));
        vh.setData(jsnObj);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" #getSingleObjectPermission.After Perm: {} UserInfo: ID/Name/Ename/tableName："
                    , usr.getId(), usr.getEname(), usr.getEname(), tn), jsnObj.toJSONString());
        }
        return vh;
    }

    /**
     * 敏感列
     *
     * @param tn    table name
     * @param lst   data list
     * @param daAry return jsonAry
     */
    private void dealDataPermission(String tn, List<DataPermission> lst, JSONArray daAry) {
        Class[] clzClc = null;
        final String o1 = "OC_B_ORDER";
        final String o2 = "OC_B_RETURN_ORDER";
        final String o3 = "OC_B_REFUND_IN";
        if (o1.equals(tn)) {
            clzClc = new Class[]{OcBOrder.class, OcBOrderItem.class};
        } else if (o2.equals(tn)) {
            clzClc = new Class[]{OcBReturnOrder.class, OcBReturnOrderRefund.class, OcBReturnOrderExchange.class};
        } else if (o3.equals(tn)) {
            clzClc = new Class[]{OcBRefundIn.class, OcBRefundInProductItem.class};
        }
        /*switch (tn) {
            case "OC_B_ORDER":
                clzClc = new Class[]{OcBOrder.class, OcBOrderItem.class};
                break;
            case "OC_B_REFUND_IN":
                clzClc = new Class[]{OcBRefundIn.class, OcBRefundInProductItem.class};
                break;
            case "OC_B_RETURN_ORDER":
                clzClc = new Class[]{OcBReturnOrder.class, OcBReturnOrderRefund.class, OcBReturnOrderExchange.class};
                break;
            default:
                break;
        }*/
        if (clzClc != null && clzClc.length > OcBOrderConst.IS_STATUS_IN) {
            Set<String> dbFldSet = new HashSet<>();
            for (int i = 0; i < clzClc.length; i++) {
                Class clz = clzClc[i];
                Field[] feds = clz.getDeclaredFields();
                for (Field fld : feds) {
                    boolean hasJsonAno = fld.isAnnotationPresent(JSONField.class);
                    if (hasJsonAno) {
                        JSONField jsnAno = fld.getAnnotation(JSONField.class);
                        String fldNm = jsnAno.name();
                        if (fldNm == null || fldNm.length() < OcBOrderConst.IS_STATUS_IY) {
                            continue;
                        }
                        dbFldSet.add(fldNm);
                    }
                }
            }
            /*JSONObject itmJsnObj = filterOrderAlies(tn);
            Set<String> itmKeys = null;
            if (itmJsnObj != null) {
                itmKeys = itmJsnObj.keySet();
            }*/
            final String ignoreFields = "PAYTYPENAME,STOCK,CP_C_PHY_WAREHOUSE_ENAME,GBCODE";
            for (DataPermission dpm : lst) {
                String cde = dpm.getEcode();
                if (dbFldSet.contains(cde) || ignoreFields.contains(cde)) {
                    daAry.add(dpm);
                    /*if (itmKeys != null && itmKeys.contains(cde)) {
                        DataPermission dm = new DataPermission();
                        dm.setIsWrite(dpm.getIsWrite());
                        dm.setIsRead(dpm.getIsRead());
                        dm.setId(dpm.getId());
                        dm.setEname(dpm.getEname());
                        dm.setEcode(itmJsnObj.getString(cde));
                        daAry.add(dm);
                    }*/
                }
            }
        }

    }

    /**
     * 其它间接.调用接口. 明细表别名处理
     *
     * @param tn table name
     * @return jsonObject
     */
    private JSONObject filterOrderAlies(String tn) {
        if (!"OC_B_ORDER".equals(tn)) {
            return null;
        }
        String s = "`ID` proId,PS_C_SKU_ID skuId,PS_C_PRO_ID psCproId,PS_C_CLR_ID clrsId,"
                + "PS_C_CLR_ECODE clrsEcode,PS_C_CLR_ENAME clrs,PS_C_SIZE_ID sizeId,PS_C_SIZE_ECODE sizeEcode,"
                + "PS_C_SIZE_ENAME sizes,QTY qty,PRICE price,REAL_AMT realAmt, STANDARD_WEIGHT weight, "
                + "PS_C_SKU_ECODE skuEcode, PS_C_PRO_ENAME proEname, AMT_REFUND amtRefund,  BARCODE barCode,"
                + "price_list priceList, OC_B_ORDER_ID orderId, SKU_SPEC skuSpec,  PS_C_PRO_ECODE ecode, OOID oOId,"
                + "PRICE_SETTLE priceSettle, TOT_PRICE_SETTLE totPriceSettle, PRICE_ACTUAL priceActual, "
                + "QTY_LOST qtyLost, `SEX` sex, `QTY_REFUND` qtyRefund, `REFUND_STATUS` refundStatus, "
                + "`RESERVE_DECIMAL02` RESERVE_DECIMAL02";
        String[] s1 = s.split(",");
        JSONObject fldJsnObj = new JSONObject();
        for (String s2 : s1) {
            String s3 = s2.replace("`", "");
            String[] s4 = s3.trim().split("\\s");
            fldJsnObj.put(s4[0], s4[1]);
        }
        return fldJsnObj;
    }
}
