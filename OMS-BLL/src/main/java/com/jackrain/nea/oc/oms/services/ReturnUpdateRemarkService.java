package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.TradeMemoUpdateModel;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: 李杰
 * @since: 2019/3/27
 * create at : 2019/3/27 13:58
 */
@Slf4j
@Component
public class ReturnUpdateRemarkService {

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private CpRpcService cpRpcService;

    public ValueHolder remarkUpdate(JSONObject obj, User user) {
        ValueHolder vh = new ValueHolder();

        String ids = obj.getString("ids");
        String remark = obj.getString("remark");
        String cover = obj.getString("cover"); //是否覆盖原备注

        String[] idArray = ids.split(",");
        Integer num = 0;
        Integer flag = 0;
        for (int i = 0; i < idArray.length; i++) {
            Long id = Long.valueOf(idArray[i]);
            OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(id);
            if (ocBReturnOrder == null) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "退换货单不存在");
                return vh;
            }
            Integer status = ocBReturnOrder.getReturnStatus();
            String oldRemark = ocBReturnOrder.getRemark();
            if (ReturnStatusEnum.CANCLE.getVal().equals(status)) {
                flag += 1;
                continue;
            }
            if ("true".equals(cover)) { //覆盖原卖家备注
                OcBReturnOrder order = new OcBReturnOrder();
                order.setId(id);
                order.setRemark(remark);
                order.setModifierid(user.getId().longValue());
                order.setModifiername(user.getName());
                order.setModifierename(user.getEname());
                order.setModifieddate(new Date());
                Integer count = ocBReturnOrderMapper.updateById(order);
                if (count > 0) {
                    num += 1;
                } else {
                    flag += 1;
                }
                Long logId = ModelUtil.getSequence("oc_b_return_order_log");
                OcBReturnOrderLog ocBReturnOrderLog = getLog(logId, "修改退单备注",
                        "修改退单备注成功", user.getName(), id);
                ocBReturnOrderLog.setAdOrgId(user.getOrgId() + 0L);
                ocBReturnOrderLog.setAdClientId(user.getClientId() + 0L);
                ocBReturnOrderLog.setIpAddress(user.getLastloginip());
                ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
            } else { //追加到原备注
                oldRemark = oldRemark == null ? " " : oldRemark;
                OcBReturnOrder order = new OcBReturnOrder();
                order.setId(id);
                order.setRemark(oldRemark + "," + remark);
                order.setModifierid(user.getId().longValue());
                order.setModifiername(user.getName());
                order.setModifierename(user.getEname());
                order.setModifieddate(new Date());
                Integer count = ocBReturnOrderMapper.updateById(order);
                if (count > 0) {
                    num += 1;
                } else {
                    flag += 1;
                }
                Long logId = ModelUtil.getSequence("oc_b_return_order_log");
                OcBReturnOrderLog ocBReturnOrderLog = getLog(logId, "修改退单备注",
                        "修改退单备注成功", user.getName(), id);
                ocBReturnOrderLog.setAdOrgId(user.getOrgId() + 0L);
                ocBReturnOrderLog.setAdClientId(user.getClientId() + 0L);
                ocBReturnOrderLog.setIpAddress(user.getLastloginip());
                ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
            }
           /* try {
                SpecialElasticSearchUtil.indexDocument("oc_b_return_order",
                        "oc_b_return_order", ocBReturnOrderMapper.selectById(id), id);
            } catch (Exception e) {
                throw new NDSException("修改退单备注推送es失败");
            }*/
        }

        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", Resources.getMessage(String.format("%s条备注修改成功,%s条备注修改失败", num, flag),
                user.getLocale()));

        return vh;
    }

    private OcBReturnOrderLog getLog(Long id, String logType, String message, String userName, Long reOrderid) {
        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
        ocBReturnOrderLog.setId(id);
        ocBReturnOrderLog.setLogType(logType);
        ocBReturnOrderLog.setLogMessage(message);
        ocBReturnOrderLog.setUserName(userName);
        ocBReturnOrderLog.setOcBReturnOrderId(reOrderid);
        return ocBReturnOrderLog;
    }

    /**
     * 批量导入修改备注
     *
     * @param ocBOrderList 主表集合
     * @param sourceCode   平台单号集合
     * @param cover        是否覆盖
     * @param user         当前用户
     * @return
     */
    public ValueHolderV14 batchImport(List<OcBOrder> ocBOrderList, JSONArray sourceCode, Boolean cover, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        //判断有没有相同的平台单号
        ArrayList list = new ArrayList();
        StringBuilder sameSourcecode = new StringBuilder();
        for (int i = 0; i < sourceCode.size(); i++) {
            if (i == 0) {
                list.add(sourceCode.get(i));
            } else {
                if (list.contains(sourceCode.get(i))) {
                    sameSourcecode.append(sourceCode.get(i)).append(",");
                } else {
                    list.add(sourceCode.get(i));
                }
            }
        }
        if (list.size() != sourceCode.size()) {
            vh.setMessage("导入数据有存在相同的平台单号,相同的平台单号为》》" + sameSourcecode.toString());
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        Integer success = 0; //成功条数计数
        Integer loser = 0;  //失败条数技术

        JSONArray data = ES4Order.findDataBySourceCode(sourceCode);

        if (data.size() != sourceCode.size()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("查询");
        }
        if (data == null && data.size() == 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("暂无数据导入，请填充数据进行操作");
            return vh;
        }
        StringBuilder builder = new StringBuilder(); //用来存储失败的修改备注失败的 id
        //把从es查出来都得数据转成对象
        List<OcBOrder> orders = JSONObject.parseArray(data.toJSONString(), OcBOrder.class);
        //用来过滤相同的平台单号来调用平台备注同步接口
        List filterSource = new ArrayList();
        // 导入的集合 和 es 查询的集合进行对比
        for (int i = 0; i < orders.size(); i++) {
            //es导入的对象
            OcBOrder ocBOrderEs = orders.get(i);
            for (int j = 0; j < ocBOrderList.size(); j++) {
                OcBOrder needExport = new OcBOrder();
                //导入的对象
                OcBOrder ocBOrderExport = ocBOrderList.get(j);
                if (ocBOrderEs.getSourceCode().equals(ocBOrderExport.getSourceCode())) {
                    //查询数据库的备注再后面追加备注
                    //OcBOrder ocBOrder2 = ocBOrderMapper.selectById(ocBOrder.getId());
                    String remark = null;
                    if (cover == false) {
                        if (ocBOrderEs.getSellerMemo() == null) {
                            remark = ocBOrderExport.getSellerMemo();
                        } else {
                            remark = ocBOrderEs.getSellerMemo() + "," + ocBOrderExport.getSellerMemo();
                        }
                        needExport.setSellerMemo(remark);
                    } else {
                        remark = ocBOrderExport.getSellerMemo();
                        needExport.setSellerMemo(remark);
                    }
                    needExport.setId(ocBOrderEs.getId());
                    if (ocBOrderExport.getOrderFlag() != null) {
                        needExport.setOrderFlag(ocBOrderExport.getOrderFlag());
                    }
                    QueryWrapper wrapper = new QueryWrapper();
                    wrapper.eq("id", ocBOrderEs.getId());
                    //更新修改的数据
                    needExport.setModifierename(user.getEname());
                    needExport.setModifiername(user.getName());
                    needExport.setModifieddate(new Date());
                    int update = ocBOrderMapper.update(needExport, wrapper);
                    if (update < 1) {
                        loser++;
                        continue;
                    }
                  /*  OcBOrder ocBOrder2 = ocBOrderMapper.selectById(ocBOrderEs.getId());
                    try {
                        SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,ocBOrder2,ocBOrder2.getId());
                    } catch (IOException e) {
                        e.printStackTrace();
                    }*/
                    //调用订单日志服务
                    try {
                        // 调用订单日志服务
                        omsOrderLogService.addUserOrderLog(ocBOrderEs.getId(), ocBOrderEs.getBillNo(), OrderLogTypeEnum.SELLERMEMO_UPDATE.getKey(),
                                "卖家备注：修改前：" + ocBOrderEs.getSellerMemo() + "，修改后：" + remark, null,
                                null, user);
                    } catch (Exception e) {
                        log.error(LogUtil.format("修改卖家备注调用订单日志服务失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    }
                    // 调用服务来修改 淘宝京东 修改备注接口 相同平台单号的只去一个同步淘宝或者京东
                    if (filterSource.size() == 0) {
                        filterSource.add(ocBOrderEs.getSourceCode());
                    } else {
                        if (filterSource.contains(ocBOrderEs.getSourceCode())) {
                            success++;
                            continue;
                        } else {
                            filterSource.add(ocBOrderEs.getSourceCode());
                        }
                    }
                    try {
                        ValueHolderV14 v14 = updateRemarkByPlatform(needExport, user);
                        if (v14 != null) {
                            if (ResultCode.FAIL == v14.getCode()) {
                                loser++;
                                builder.append(needExport.getId()).append(",");
                            } else {
                                success++;
                            }
                        }
                    } catch (Exception e) {
                        log.error(LogUtil.format("调用平台修改备注服务,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                        loser++;
                        builder.append(needExport.getId()).append(",");
                    }
                }

                ocBOrderExport.setId(null);
            }

        }
        //找出查询不到的平台单号
        List errorSourceCode = new ArrayList();
        List searchSource = new ArrayList();
        for (int i = 0; i < orders.size(); i++) {
            OcBOrder ocBOrder = orders.get(i);
            if (ocBOrder.getSourceCode() != null) {
                searchSource.add(ocBOrder.getSourceCode());
            }
        }
        for (int i = 0; i < sourceCode.size(); i++) {
            Object o = sourceCode.get(i);
            if (o != null) {
                String soureCode = o.toString();
                if (!searchSource.contains(soureCode)) {
                    errorSourceCode.add(soureCode);
                }
            }
        }
        if (errorSourceCode != null && errorSourceCode.size() > 0) {
            builder.append("无效的平台单号为:").append(JSONObject.toJSONString(errorSourceCode));
        }
        vh.setCode(ResultCode.FAIL);
        vh.setMessage("修改备注成功" + success + "条数据,修改失败" + loser + "条数据，修改失败的id是》》" + builder.toString());
        return vh;
    }

    /**
     * 批量导入修改备注
     *
     * @param ocBOrderList 主表集合
     * @param sourceCode   平台单号集合
     * @param cover        是否覆盖
     * @param user         当前用户
     * @return
     */
    public ValueHolderV14 batchImport1(List<OcBOrder> ocBOrderList, JSONArray sourceCode, Boolean cover, User user) {
        // 去从完后的平台单号集合
        List<Object> collect = sourceCode.stream().distinct().collect(Collectors.toList());
        JSONArray arraySource = JSONObject.parseArray(JSONObject.toJSONString(collect));
        if (CollectionUtils.isEmpty(collect)) {
            throw new NDSException("导入数据不能为空");
        }
        ValueHolderV14 vh = new ValueHolderV14();
        Integer success = 0; //成功条数计数
        Integer loser = 0;  //失败条数技术

        JSONArray data = ES4Order.findDataBySourceCode(arraySource);

        if (CollectionUtils.isEmpty(data)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("Es查询暂无数据，请检查导入数据");
            return vh;
        }


        StringBuilder builder = new StringBuilder(); //用来存储失败的修改备注失败的 id
        //把从es查出来都得数据转成对象
        List<OcBOrder> orders = JSONObject.parseArray(data.toJSONString(), OcBOrder.class);
        // 给导入的数据分组
        Map<String, List<OcBOrder>> groupBySourceCode = ocBOrderList.stream().collect(Collectors.groupingBy(a -> a.getSourceCode(), Collectors.toList()));
        // 导入相同平台单号 备注信息聚合的订单集合 matchOrder
        List<OcBOrder> matchOrder = new ArrayList<>();
        for (Object sourceCode1 : collect) {
            List<OcBOrder> orders1 = groupBySourceCode.get(sourceCode1);
            StringBuilder stringBuilder = new StringBuilder();
            for (OcBOrder ocBOrder : orders1) {
                stringBuilder.append(ocBOrder.getSellerMemo()).append(",");
            }
            //判断导入进行的相同单号的旗帜 是否存在为空的，如果有空的默认红色 ，如果没空的默认 随机颜色
            int count = 0; // 计算旗帜不为空的个数
            for (OcBOrder ocBOrder : orders1) {
                if (ocBOrder.getOrderFlag() != null) {
                    count++;
                }
            }
            OcBOrder filterOcBOrder = orders1.get(0);
            if (count == orders1.size()) {
                //就进行随机 分配颜色
                List<Integer> randomFlag = new ArrayList<>();
                randomFlag.add(0);
                randomFlag.add(1);
                randomFlag.add(2);
                randomFlag.add(3);
                randomFlag.add(4);
                randomFlag.add(5);
                Integer color = randomFlag.get((int) (Math.random() * randomFlag.size()));
                if (count == 1) {
                    filterOcBOrder.setOrderFlag(filterOcBOrder.getOrderFlag());
                } else {
                    filterOcBOrder.setOrderFlag(color.toString());
                }
            } else {
                //默认设置为红色
                filterOcBOrder.setOrderFlag("2");
            }
            filterOcBOrder.setSellerMemo(stringBuilder.toString());
            matchOrder.add(filterOcBOrder);
        }
        // 进行一对一 Match
        List<OcBOrder> rightMatch = oneToOneMatch(matchOrder, orders, cover, user);
     /*   // 记录无效的平台单号
        String recordNumber = invalidRecordNumber(orders, collect);*/
        // 进行更新备注,同时传入平台
        ValueHolderV14 v14 = updateSellerDemo(rightMatch, user, collect);
        Object data1 = v14.getData();
        JSONObject object = JSONObject.parseObject(data1.toString());
        success = object.getInteger("success");
        loser = object.getInteger("loser");
        JSONArray errorSourceCode = object.getJSONArray("errorSourceCode");
        JSONArray errorOrderIds = object.getJSONArray("errorOrderIds");
        StringBuilder tips = new StringBuilder();
        if (errorSourceCode == null || errorSourceCode.size() == 0) {
            if (loser == 0) {
                tips.append("修改备注完成: 成功").append(success).append("条, ")
                        .append("失败").append(loser).append("条.");
            } else {
                if (success == 0) {
                    tips.append("修改备注完成: ")
                            .append("失败").append(loser).append("条.")
                            .append("失敗的id为:").append(errorOrderIds.toJSONString());
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(tips.toString());
                    return vh;
                }
                tips.append("修改备注完成: 成功").append(success).append("条, ")
                        .append("失败").append(loser).append("条.")
                        .append("失敗的id为:").append(errorOrderIds.toJSONString());
            }
        } else {
            if (loser == 0) {
                tips.append("修改备注完成: 成功").append(success).append("条, ")
                        .append("失败").append(loser).append("条,")
                        .append("无效的平台单号为:").append(errorSourceCode.toJSONString());
            } else {
                if (success == 0) {
                    tips.append("修改备注完成: ")
                            .append("失败").append(loser).append("条.")
                            .append("失敗的id为:").append(errorOrderIds.toJSONString());
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(tips.toString());
                    return vh;
                }
                tips.append("修改备注完成: 成功").append(success).append("条, ")
                        .append("失败").append(loser).append("条,")
                        .append("失敗的id为:").append(errorOrderIds.toJSONString())
                        .append("无效的平台单号为:").append(errorSourceCode.toJSONString());
            }
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(tips.toString());
        return vh;
    }

    /**
     * 更新备注并同步到平台 同时插入日志
     *
     * @param rightMatch
     * @param user
     * @param collect
     */
    private ValueHolderV14 updateSellerDemo(List<OcBOrder> rightMatch, User user, List<Object> collect) {
        List<String> error = new ArrayList<>();
        ValueHolderV14 v14 = new ValueHolderV14();
        List<Long> errorOrderIds = new ArrayList<>();
        Integer success = 0;
        Integer loser = 0;
        //进行按照 平台单号分组
        Map<String, List<OcBOrder>> groupMatch = rightMatch.stream().collect(Collectors.groupingBy(a -> a.getSourceCode(), Collectors.toList()));
        for (int k = 0; k < collect.size(); k++) {
            List<OcBOrder> orders = groupMatch.get(collect.get(k));
            if (orders == null) {
                error.add(collect.get(k).toString());
                continue;
            }
            for (int i = 0; i < orders.size(); i++) {
                OcBOrder ocBOrder = orders.get(i);
                QueryWrapper wrapper = new QueryWrapper();
                wrapper.eq("id", ocBOrder.getId());
                int update = ocBOrderMapper.update(ocBOrder, wrapper);
               /* OcBOrder ocBOrder1 = ocBOrderMapper.selectById(ocBOrder.getId());
                try {
                    SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocBOrder1, ocBOrder.getId());
                } catch (IOException e) {
                    e.printStackTrace();
                }*/
                //相同平台单号只调用一次 修改备注服务
                if (i == 0) {
                    try {
                        ValueHolderV14 valueHolderV14 = this.updateRemarkByPlatform(ocBOrder, user);
                        if (valueHolderV14 != null) {
                            if (ResultCode.FAIL == valueHolderV14.getCode()) {
                                loser++;
                                errorOrderIds.add(ocBOrder.getId());
                            } else {
                                success++;
                            }
                        }
                    } catch (Exception e) {
                        log.error(LogUtil.format("调用修改备注服务失败,异常信息为:{}", ocBOrder.getId()), Throwables.getStackTraceAsString(e));
                        loser++;
                        errorOrderIds.add(ocBOrder.getId());
                    }
                } else {
                    success++;
                }
            }
        }
        JSONObject object = new JSONObject();
        object.put("success", success);
        object.put("loser", loser);
        object.put("errorSourceCode", error);
        object.put("errorOrderIds", errorOrderIds);
        v14.setData(object);
        v14.setCode(ResultCode.SUCCESS);
        return v14;
    }

    /**
     * 一对一进行匹配组装集合
     *
     * @param matchOrder 这是导入的 订单对象集合
     * @param orders     这是es 的订单对象集合
     * @param cover
     * @param user
     * @return 返回组装好的订单集合
     */
    private List<OcBOrder> oneToOneMatch(List<OcBOrder> matchOrder, List<OcBOrder> orders, Boolean cover, User user) {

       /* for (int i = 0; i < matchOrder.size(); i++) {
            OcBOrder ocBOrder = matchOrder.get(i);
            int k = 0; // 解决相同的单号循环插入问题
            for (int j = 0; j < orders.size(); j++) {
                OcBOrder ocBOrder1 = orders.get(j);
                if (ocBOrder.getSourceCode().equals(ocBOrder1.getSourceCode()) && k != 1) {
                    if (cover) {
                        ocBOrder1.setSellerMemo(ocBOrder.getSellerMemo());
                        ocBOrder1.setOrderFlag(ocBOrder.getOrderFlag());
                    } else {
                        ocBOrder1.setSellerMemo(ocBOrder1.getSellerMemo() + ocBOrder.getSellerMemo());
                        ocBOrder1.setOrderFlag(ocBOrder.getOrderFlag());
                    }
                    ocBOrder1.setModifieddate(new Date());
                    ocBOrder1.setModifierid(user.getId() + 0L);
                    ocBOrder1.setModifiername(user.getEname());
                    ocBOrder1.setModifiername(user.getName());
                    k++;
                }
            }
        }*/
        //  matchOrder 是组装过滤的数据  ， orders es查询的数据
        for (int i = 0; i < orders.size(); i++) {
            //es的数据
            OcBOrder ocBOrder = orders.get(i);
            for (int j = 0; j < matchOrder.size(); j++) {
                OcBOrder exportOrder = matchOrder.get(j);
                if (ocBOrder.getSourceCode().equals(exportOrder.getSourceCode())) {
                    if (cover) {
                        ocBOrder.setSellerMemo(exportOrder.getSellerMemo());
                    } else {
                        String sellerMemo = ocBOrder.getSellerMemo();
                        if (StringUtils.isBlank(sellerMemo)) {
                            ocBOrder.setSellerMemo(exportOrder.getSellerMemo());
                        } else {
                            ocBOrder.setSellerMemo(ocBOrder.getSellerMemo() + "," + exportOrder.getSellerMemo());
                        }
                    }
                    ocBOrder.setModifieddate(new Date());
                    ocBOrder.setModifierid(user.getId() + 0L);
                    ocBOrder.setModifiername(user.getEname());
                    ocBOrder.setModifiername(user.getName());
                    ocBOrder.setOrderFlag(exportOrder.getOrderFlag());
                }
            }
        }
        return orders;
    }

    /**
     * 调用修改备注服务
     *
     * @param ocBOrder 导入传入的参数
     * @param user     当前登录的用户
     */
    private ValueHolderV14 updateRemarkByPlatform(OcBOrder ocBOrder, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        Long id = ocBOrder.getId();
        //查询 数据库
        OcBOrder ocBOrder1 = ocBOrderMapper.selectById(id);
        Integer platform = ocBOrder1.getPlatform();
        //调用淘宝修改卖家备注接口   77 经销   3 分销
        if (platform == 2 || platform == 77 || platform == 3) {
            TradeMemoUpdateModel request = new TradeMemoUpdateModel();
            ValueHolderV14 valueHolderV14 = new ValueHolderV14();
            try {
                String shopSecretKey = cpRpcService.selectShopById(ocBOrder1.getCpCShopId()).getShopSecretKey();
                String[] split = shopSecretKey.split("\\|");
                String[] split1 = split[2].split(":");
                String sessionKey = split1[1];
                request.setOperateUser(user);
                request.setTid(Long.valueOf(ocBOrder1.getTid()));
                request.setFlag(Long.valueOf(ocBOrder1.getOrderFlag()));
                request.setMemo(ocBOrder1.getSellerMemo());
                request.setSessionKey(sessionKey);
                request.setPlatform(platform);
                vh = ipRpcService.tradeMemoUpdate(request);
            } catch (Exception e) {
                log.error(LogUtil.format("调用淘宝修改备注接口异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                OcBOrder order = new OcBOrder();
                order.setId(id);
                order.setSysremark("修改备注同步平台异常:");
                ocBOrderMapper.updateById(order);
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(e.getMessage());
                return vh;
            }
            if (vh.getCode() == -1) {
                log.error(LogUtil.format("淘宝修改备注同步平台失败,异常信息为:{}"), vh.getMessage());
                OcBOrder order = new OcBOrder();
                order.setId(id);
                order.setSysremark("淘宝修改备注同步平台失败:" + vh.getMessage());
                ocBOrderMapper.updateById(order);
            }
        }
        if (platform == 4) {
            TradeMemoUpdateModel request = new TradeMemoUpdateModel();
            ValueHolderV14 valueHolderV14 = new ValueHolderV14();
            try {
                request.setOperateUser(user);
                request.setTid(Long.valueOf(ocBOrder1.getTid()));
                request.setFlag(Long.valueOf(ocBOrder1.getOrderFlag()));
                request.setMemo(ocBOrder1.getSellerMemo());
                request.setSellerNick(ocBOrder1.getCpCShopSellerNick());
                request.setPlatform(platform);
                vh = ipRpcService.tradeMemoUpdate(request);
            } catch (Exception e) {
                log.error(LogUtil.format("调用京东修改备注接口异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                OcBOrder order = new OcBOrder();
                order.setId(id);
                order.setSysremark("修改备注同步平台异常:");
                ocBOrderMapper.updateById(order);
                vh.setMessage(e.getMessage());
                vh.setCode(ResultCode.FAIL);
                return vh;
            }
            if (vh.getCode() == -1) {
                OcBOrder order = new OcBOrder();
                order.setId(id);
                order.setSysremark("京东修改备注同步平台失败:" + vh.getMessage());
                ocBOrderMapper.updateById(order);
            }
        }
        return vh;
    }

    /**
     * 退单修改卖家备注
     *
     * @param obj
     * @param user
     * @return
     */
    public ValueHolder reUpdateSellerRemark(JSONObject obj, User user) {
        ValueHolder vh = new ValueHolder();

        String ids = obj.getString("ids");
        String remark = obj.getString("remark");
        String cover = obj.getString("cover"); //是否覆盖原备注

        String[] idArray = ids.split(",");
        Integer num = 0;
        Integer flag = 0;
        for (int i = 0; i < idArray.length; i++) {
            Long id = Long.valueOf(idArray[i]);
            OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(id);
            if (ocBReturnOrder == null) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "退换货单不存在");
                return vh;
            }
            Integer status = ocBReturnOrder.getReturnStatus();
            String oldRemark = ocBReturnOrder.getSellerMemo();  // 卖家备注
            if (ReturnStatusEnum.CANCLE.getVal().equals(status)) {
                flag += 1;
                continue;
            }
            if ("true".equals(cover)) { //覆盖原卖家备注
                OcBReturnOrder order = new OcBReturnOrder();
                order.setId(id);
                order.setSellerMemo(remark);
                order.setModifierid(user.getId().longValue());
                order.setModifiername(user.getName());
                order.setModifierename(user.getEname());
                order.setModifieddate(new Date());
                Integer count = ocBReturnOrderMapper.updateById(order);
                if (count > 0) {
                    num += 1;
                } else {
                    flag += 1;
                }
                Long logId = ModelUtil.getSequence("oc_b_return_order_log");
                OcBReturnOrderLog ocBReturnOrderLog = getLog(logId, "修改退单卖家备注",
                        "修改退单卖家备注成功", user.getName(), id);
                ocBReturnOrderLog.setAdOrgId(user.getOrgId() + 0L);
                ocBReturnOrderLog.setAdClientId(user.getClientId() + 0L);
                ocBReturnOrderLog.setIpAddress(user.getLastloginip());
                ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
            } else { //追加到原备注
                oldRemark = oldRemark == null ? " " : oldRemark;
                OcBReturnOrder order = new OcBReturnOrder();
                order.setId(id);
                order.setSellerMemo(oldRemark + "," + remark);
                order.setModifierid(user.getId().longValue());
                order.setModifiername(user.getName());
                order.setModifierename(user.getEname());
                order.setModifieddate(new Date());
                Integer count = ocBReturnOrderMapper.updateById(order);
                if (count > 0) {
                    num += 1;
                } else {
                    flag += 1;
                }
                Long logId = ModelUtil.getSequence("oc_b_return_order_log");
                OcBReturnOrderLog ocBReturnOrderLog = getLog(logId, "修改退单卖家备注",
                        "修改退单卖家备注成功", user.getName(), id);
                ocBReturnOrderLog.setAdOrgId(user.getOrgId() + 0L);
                ocBReturnOrderLog.setAdClientId(user.getClientId() + 0L);
                ocBReturnOrderLog.setIpAddress(user.getLastloginip());
                ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
            }
           /* try {
                SpecialElasticSearchUtil.indexDocument("oc_b_return_order",
                        "oc_b_return_order", ocBReturnOrderMapper.selectById(id), id);
            } catch (Exception e) {
                throw new NDSException("修改退单卖家备注推送es失败");
            }*/
        }

        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", Resources.getMessage(String.format("%s条卖家备注修改成功,%s条卖家备注修改失败", num, flag),
                user.getLocale()));

        return vh;
    }
}
