package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.vo.OaOrderResissueVO;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName OaOrderResissueService
 * @Description OA补发
 * <AUTHOR>
 * @Date 2024/10/12 14:59
 * @Version 1.0
 */
@Slf4j
@Component
public class OaOrderResissueService {

    private static final String GROUP_GOODS_MARK = "CG";
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;
    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OmsExpiryDateStService omsExpiryDateStService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;

    public ValueHolderV14 oaOrderResissue(List<OaOrderResissueVO> oaOrderResissueVOList) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        log.info(LogUtil.format("OaOrderResissueService.oaOrderResissue.oaOrderResissueVOList：{}"), JSONUtil.toJsonStr(oaOrderResissueVOList));
        Map<String, List<OaOrderResissueVO>> resissueMap = new HashMap<>();
        // 先校验
        User user = SystemUserResource.getRootUser();
        for (OaOrderResissueVO oaOrderResissueVO : oaOrderResissueVOList) {

            // 校验oaOrderResissueVO中的qty需要为正整数
            if (oaOrderResissueVO.getQty() <= 0) {
                return ValueHolderV14Utils.custom(ResultCode.FAIL, "数量必须为正整数", null);
            }

            // 查看平台单号是否有已发货的订单 如果没有 则报错
            List<Long> ocBOrderIdList = ocBOrderMapper.selectOcBOrderIdByTid(oaOrderResissueVO.getTid());
            if (CollectionUtils.isEmpty(ocBOrderIdList)) {
                return ValueHolderV14Utils.custom(ResultCode.FAIL, "平台单号:" + oaOrderResissueVO.getTid() + "对应的订单不存在，请检查！", null);
            }
            // 根据订单id 查找订单
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectOrderListByIdsList(ocBOrderIdList);

            // 找到 ocborders中的businessTypeCode不为空的订单
            ocBOrders = ocBOrders.stream().filter(ocBOrder -> ocBOrder.getBusinessTypeCode() != null).collect(Collectors.toList());
            // 获取ocBOrders中businessTypeCode的值为RYCK14的订单
            ocBOrders = ocBOrders.stream().filter(ocBOrder -> ocBOrder.getBusinessTypeCode().equals("RYCK14")).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ocBOrders)) {
                return ValueHolderV14Utils.custom(ResultCode.FAIL, "平台单号:" + oaOrderResissueVO.getTid() + "对应的订单不存在电商销售的订单，不允许补发，请检查！", null);
            }
            // 判断是否有平台发货或者仓库发货的订单
            boolean flag = ocBOrders.stream().anyMatch(ocBOrder -> ocBOrder.getOrderStatus().equals(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())
                    || ocBOrder.getOrderStatus().equals(OmsOrderStatus.PLATFORM_DELIVERY.toInteger()));
            if (!flag) {
                return ValueHolderV14Utils.custom(ResultCode.FAIL, "平台单号:" + oaOrderResissueVO.getTid() + "对应的订单不存在已发货的订单，不允许补发，请检查！", null);
            }
            // 先校验sku是否存在
            ProductSku productSku = psRpcService.selectProductSku(oaOrderResissueVO.getPsCSkuEcode());
            if (ObjectUtil.isNull(productSku)) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("商品:" + oaOrderResissueVO.getPsCSkuEcode() + "不存在");
                return valueHolderV14;
            }
            if ("Y".equals(productSku.getIsVirtual())) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("商品:" + oaOrderResissueVO.getPsCSkuEcode() + "为虚拟品");
                return valueHolderV14;
            }

            // 组装map
            List<OaOrderResissueVO> oaOrderResissueVOS = resissueMap.get(oaOrderResissueVO.getTid());
            if (CollectionUtils.isEmpty(oaOrderResissueVOS)) {
                oaOrderResissueVOS = new ArrayList<>();
                oaOrderResissueVOS.add(oaOrderResissueVO);
                resissueMap.put(oaOrderResissueVO.getTid(), oaOrderResissueVOS);
            } else {
                resissueMap.get(oaOrderResissueVO.getTid()).add(oaOrderResissueVO);
            }
        }
        // 准备执行订单创建及后续操作
        try {
            OaOrderResissueService oaOrderResissueService = ApplicationContextHandle.getBean(OaOrderResissueService.class);
            oaOrderResissueService.oaOrderResissue(resissueMap, user);
        } catch (Exception e) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }
        return valueHolderV14;
    }

    @Transactional(rollbackFor = Exception.class)
    public void oaOrderResissue(Map<String, List<OaOrderResissueVO>> resissueMap, User user) {
        for (String tid : resissueMap.keySet()) {
            try {
                List<OcBOrder> ocBOrders = ocBOrderMapper.selectDeliveryOrderByTid(tid);
                if (CollectionUtils.isEmpty(ocBOrders)) {
                    throw new Exception("平台单号:" + tid + "对应的订单不存在已发货的订单，不允许补发，请检查！");
                }
                // 取出一个订单
                OcBOrder oriOrder = ocBOrders.get(0);
                // 校验原单的业务类型。必须只能是电商销售
                OcBOrder newOcBOrder = new OcBOrder();
                Long ocBOrderId = sequenceUtil.buildOrderSequenceId();
                String billNo = sequenceUtil.buildBillNo();
                BeanCopierUtil.copy(oriOrder, newOcBOrder);
                BaseModelUtil.initialBaseModelSystemField(newOcBOrder);

                newOcBOrder.setId(ocBOrderId);
                newOcBOrder.setBillNo(billNo);
                newOcBOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
                newOcBOrder.setIsResetShip(1);
                newOcBOrder.setIsCombination(0);
                // 写死业务类型
                StCBusinessType stCBusinessType = omsBusinessTypeStService.selectStCBusinessTypeByCode("RYCK15");
                newOcBOrder.setBusinessTypeId(stCBusinessType.getId());
                newOcBOrder.setBusinessTypeCode(stCBusinessType.getEcode());
                newOcBOrder.setBusinessTypeName(stCBusinessType.getEname());
                newOcBOrder.setProductAmt(BigDecimal.ZERO);
                newOcBOrder.setOrderDiscountAmt(BigDecimal.ZERO);
                newOcBOrder.setProductDiscountAmt(BigDecimal.ZERO);
                newOcBOrder.setAdjustAmt(BigDecimal.ZERO);
                newOcBOrder.setOrderAmt(BigDecimal.ZERO);
                newOcBOrder.setReceivedAmt(BigDecimal.ZERO);
                newOcBOrder.setOrderSource("手工新增");
                newOcBOrder.setOrderType(OrderTypeEnum.REISSUE.getVal());
                newOcBOrder.setOrigOrderId(oriOrder.getId());
                newOcBOrder.setPlatformStatus(null);
                newOcBOrder.setCopyReason("其他补发");
                newOcBOrder.setOrderDate(new Date());
                String suffixInfo = tid + "diy" + oriOrder.getId() + (Optional.ofNullable(oriOrder.getCopyNum()).orElse(0) + 1);
                newOcBOrder.setSuffixInfo(suffixInfo);
                removeValue(newOcBOrder);
                List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
                // 构建明细
                List<OaOrderResissueVO> oaOrderResissueVOS = resissueMap.get(tid);
                String ownerName = "";
                for (OaOrderResissueVO oaOrderResissueVO : oaOrderResissueVOS) {
                    if (StringUtils.isEmpty(oaOrderResissueVO.getOwnername())) {
                        oaOrderResissueVO.setOwnername("系统管理员");
                    }
                    newOcBOrder.setOwnername(oaOrderResissueVO.getOwnername());
                    ownerName = oaOrderResissueVO.getOwnername();
                    OcBOrderItem ocBOrderItem = new OcBOrderItem();
                    Long ocBOrderItemId = sequenceUtil.buildOrderItemSequenceId();
                    ocBOrderItem.setId(ocBOrderItemId);
                    ocBOrderItem.setOcBOrderId(ocBOrderId);
                    ProductSku productSku = psRpcService.selectProductSku(oaOrderResissueVO.getPsCSkuEcode());
                    if (oaOrderResissueVO.getQty() == null || oaOrderResissueVO.getQty() == 0) {
                        throw new NDSException("订单明细的数量不能为0或null");
                    }
                    makeCreateField(ocBOrderItem, user);
                    //记录原始明细ID，用于奶卡复制映射新的明细ID
                    ocBOrderItem.setOriginalId(null);
                    ocBOrderItem.setIsGift(0);
                    ocBOrderItem.setQtyReturnApply(BigDecimal.ZERO);
                    ocBOrderItem.setPsCSkuEcode(productSku.getSkuEcode());
                    // 供应类型 0 普通 1.代销轻供 2.寄售轻供
                    ocBOrderItem.setPsCProSupplyType(productSku.getPsCProSupplyType());
                    ocBOrderItem.setIsManualAdd("1");
                    ocBOrderItem.setQtyRefund(BigDecimal.ZERO);
                    ocBOrderItem.setTid(tid);

                    String isEnableExpiry = productSku.getIsEnableExpiry();
                    if ("Y".equals(isEnableExpiry)) {
                        ocBOrderItem.setIsEnableExpiry(1);
                    } else {
                        ocBOrderItem.setIsEnableExpiry(0);
                    }
                    //销售短出，新单子默认0
                    ocBOrderItem.setRealOutNum(BigDecimal.ZERO);

                    ocBOrderItem.setEqualExchangeMark("");
                    ocBOrderItem.setIsEqualExchange(0);
                    ocBOrderItem.setEqualExchangeRatio("");
                    ocBOrderItem.setNumIid("0");
                    ocBOrderItem.setBarcode(productSku.getBarcode69());
                    ocBOrderItem.setPsCSkuId(productSku.getId());
                    ocBOrderItem.setPsCSkuEname(productSku.getSkuName());
                    ocBOrderItem.setSkuSpec(productSku.getSkuSpec());
                    ocBOrderItem.setPsCProId(productSku.getProdId());
                    ocBOrderItem.setPsCProEcode(productSku.getProdCode());
                    ocBOrderItem.setPsCProEname(productSku.getName());
                    ocBOrderItem.setSex(productSku.getSex());
                    ocBOrderItem.setPsCClrId(productSku.getColorId());
                    ocBOrderItem.setPsCClrEcode(productSku.getColorCode());
                    ocBOrderItem.setPsCClrEname(productSku.getColorName());
                    ocBOrderItem.setPsCSizeId(productSku.getSizeId());
                    ocBOrderItem.setPsCSizeEcode(productSku.getSizeCode());
                    ocBOrderItem.setPsCSizeEname(productSku.getSizeName());
                    ocBOrderItem.setMDim4Id(productSku.getMDim4Id());
                    ocBOrderItem.setMDim6Id(productSku.getMDim6Id());
                    ocBOrderItem.setPsCProMaterieltype(productSku.getMaterialType());
                    ocBOrderItem.setStandardWeight(Optional.ofNullable(productSku.getWeight()).orElse(BigDecimal.ZERO));
                    ocBOrderItem.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
                    // 吊牌价
                    ocBOrderItem.setPriceTag(productSku.getPricelist());
                    // 吊牌价
                    ocBOrderItem.setPriceList(productSku.getPricelist());
                    // 金额相关字段的值依靠前端传入，若前端未传默认给0
                    //成交金额
                    ocBOrderItem.setRealAmt(BigDecimal.ZERO);
                    //成交单价
                    ocBOrderItem.setPriceActual(BigDecimal.ZERO);
                    ocBOrderItem.setPrice(BigDecimal.ZERO);
                    ocBOrderItem.setQty(new BigDecimal(oaOrderResissueVO.getQty()));
                    ocBOrderItem.setAdjustAmt(BigDecimal.ZERO);
                    ocBOrderItem.setAmtDiscount(BigDecimal.ZERO);
                    ocBOrderItem.setOrderSplitAmt(BigDecimal.ZERO);
                    ocBOrderItem.setAnchorId(null);
                    ocBOrderItem.setAnchorName(null);
                    ocBOrderItem.setEstimateConTime(null);
                    // 需要拆解的订单明细
                    if (productSku.getSkuType() == 0) {
                        ocBOrderItem.setProType(0L);
                        ocBOrderItemList.add(ocBOrderItem);
                    }
                    List<OcBOrderItem> combineOrGiftBagList = new ArrayList<>();
                    if (ocBOrderItem.getProType() == null
                            && (productSku.getSkuType() == SkuType.COMBINE_PRODUCT
                            || productSku.getSkuType() == SkuType.GIFT_PRODUCT)) {
                        ocBOrderItem.setProType((long) SkuType.NO_SPLIT_COMBINE);
                        ocBOrderItem.setGiftbagSku(productSku.getSkuEcode());
                        ocBOrderItem.setGroupGoodsMark(GROUP_GOODS_MARK + ocBOrderItem.getId());
                        newOcBOrder.setIsCombination(1);
                        combineOrGiftBagList.add(ocBOrderItem);
                        ocBOrderItemList.add(ocBOrderItem);
                    }
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(combineOrGiftBagList)) {
                        try {
                            List<OcBOrderItem> giftOrCombineList = omsConstituteSplitService
                                    .encapsulationParameter(combineOrGiftBagList, newOcBOrder, user, 0);
                            if (org.apache.commons.collections.CollectionUtils.isEmpty(giftOrCombineList)) {
                                throw new NDSException("福袋或组合商品抽取失败！");
                            }
                            ocBOrderItemList.addAll(giftOrCombineList);
                            omsOrderLogService.addUserOrderLog(newOcBOrder.getId(), newOcBOrder.getBillNo(),
                                    OrderLogTypeEnum.COMBINATION_SPLIT.getKey(), "组合商品解析成功", null,
                                    null, user);
                        } catch (Exception e) {
                            log.error(LogUtil.format("福袋或组合商品抽取失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                            throw new NDSException("福袋或组合商品抽取失败！");
                        }
                    }
                }
                OaOrderResissueService oaOrderResissueService = ApplicationContextHandle.getBean(OaOrderResissueService.class);
                oaOrderResissueService.insert(newOcBOrder, ocBOrderItemList);
                omsOrderLogService.addUserOrderLog(newOcBOrder.getId(), newOcBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_RESHIP.getKey(), "OA下发其他补发订单成功,源订单编号：" + oriOrder.getId() + ",提交人:" + ownerName, null, null, user);
                // 更新原始订单的copynum
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setId(oriOrder.getId());
                updateOrder.setCopyNum(Optional.ofNullable(oriOrder.getCopyNum()).orElse(0) + 1);
                updateOrder.setModifieddate(new Date());
                ocBOrderMapper.updateById(updateOrder);

                omsOccupyTaskService.addOcBOccupyTask(newOcBOrder, null);
                List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItemListAndReturn(newOcBOrder.getId());
                //指定效期
                OcBOrderParam param = new OcBOrderParam();
                param.setOcBOrder(newOcBOrder);
                param.setOrderItemList(itemList);
                omsExpiryDateStService.expiryDateStService(param, user, "自动");
            } catch (Exception e) {
                throw new NDSException("平台单号:" + tid + "执行异常，错误信息:" + e.getMessage());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void insert(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList) {
        ocBOrderMapper.insert(ocBOrder);
        ocBOrderItemMapper.batchInsert(ocBOrderItemList);
    }

    private void makeCreateField(BaseModel model, User user) {
        Date date = new Date();
        // 所属公司
        model.setAdClientId((long) user.getClientId());
        // 所属组织
        model.setAdOrgId((long) user.getOrgId());
        // 创建人id
        model.setOwnerid(Long.valueOf(user.getId()));
        // 创建时间
        model.setCreationdate(date);
        // 创建人用户名
        model.setOwnername(user.getName());
        // 修改人id
        model.setModifierid(Long.valueOf(user.getId()));
        // 修改人用户名
        model.setModifiername(user.getName());
        // 修改时间
        model.setModifieddate(date);
        // 是否启用
        model.setIsactive("Y");
    }

    private void removeValue(OcBOrder newOrder) {
        newOrder.setSgBOutBillNo(null);
        newOrder.setSgBOutBillId(null);
        newOrder.setCpCPhyWarehouseId(null);
        newOrder.setCpCPhyWarehouseEcode(null);
        newOrder.setCpCPhyWarehouseEname(null);
        newOrder.setCpCLogisticsId(null);
        newOrder.setCpCLogisticsEcode(null);
        newOrder.setCpCLogisticsEname(null);
        newOrder.setAuditTime(null);
        newOrder.setSysremark(null);
        newOrder.setInsideRemark(null);
        newOrder.setSellerMemo(null);
        newOrder.setScanTime(null);
        newOrder.setDistributionTime(null);
        newOrder.setIsForce(null);
        newOrder.setForceSendFailReason(null);
        newOrder.setAuditFailedReason(null);
        newOrder.setAuditSuccessDate(null);
        newOrder.setAuditId(null);
        newOrder.setWarehouseDeliveryTime(null);
        newOrder.setStockOccupyDate(null);
        newOrder.setOutWmsReceiveTime(null);
        newOrder.setIsOutStock(null);
        newOrder.setIsOccupyStockFail(null);
        newOrder.setIsException(null);
        newOrder.setExceptionExplain(null);
        newOrder.setExceptionType(null);
        newOrder.setExpresscode(null);
        newOrder.setReserveVarchar04(null);
        newOrder.setReserveAuditTag(null);
        newOrder.setOrderEncryptionCode(null);
        newOrder.setStoOutBillNo(null);
        newOrder.setOccupySuccessDate(null);
        newOrder.setAuditType(null);
        newOrder.setExamineOrderDate(null);
        newOrder.setIsSplit(0);
        newOrder.setIsCombination(null);
        newOrder.setIsHasgift(null);
        newOrder.setIsEqualExchange(null);
        newOrder.setLiveFlag(null);
        newOrder.setIsCycle(null);
        newOrder.setIsPromOrder(null);
        newOrder.setIsMerge(null);
        newOrder.setIsOverdue(null);
        newOrder.setIsInterecept(null);
        newOrder.setIsInreturning(null);
        newOrder.setIsInvoice(null);
        newOrder.setDouble11PresaleStatus(null);
        newOrder.setIsOutStock(null);
        newOrder.setIsDeliveryUrgent(null);
        newOrder.setIsUnavailableShop(null);
        newOrder.setIsException(null);
        newOrder.setAnchorId(null);
        newOrder.setAnchorName(null);
        newOrder.setReturnStatus(null);
    }
}
