package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInRequest;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultItemMqResult;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultMqResult;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultSendMsgResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.AddOrderNoticeAndOutService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单"仓库发货"MQ消息处理器-异步执行调用库存中心接口（生成入库通知单/结果单，逻辑收货单）
 *
 * @author: tianqinhua
 * @since: 2019/8/23
 * create at : 2019/8/23 16:29
 */
@Component
@Slf4j
public class OmsCreatePhyInNoticsService {
    @Autowired
    private AddOrderNoticeAndOutService addOrderNoticeAndOutService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private CpRpcService cpExtRpcService;

    /**
     * 异步执行调用库存中心接口（生成入库通知单/结果单，逻辑收货单）
     *
     * @param mqOutOrderList 消息体
     */
    @Async
    public String omsCreatePhyInNoticsFunc(List<SgOutResultSendMsgResult> mqOutOrderList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("omsCreatePhyInNoticsFunc入参={}"), mqOutOrderList);
        }
        try {
            if (CollectionUtils.isNotEmpty(mqOutOrderList)) {
                //主表and明细表赋值
                for (SgOutResultSendMsgResult sgOutResultSendMsgResult : mqOutOrderList) {
                    SgOutResultMqResult sgOutResultMqResult = sgOutResultSendMsgResult.getMqResult();
                    List<SgOutResultItemMqResult> sgOutResultItemMqResultList = sgOutResultSendMsgResult.getMqResultItem();
                    SgOmsStoInRequest omsStoInRequest = getNoticesRequestFunc(sgOutResultMqResult, sgOutResultItemMqResultList);
                    //转换明细数据
                    List<SgOmsStoInItemRequest> sgOmsStoInItemRequests = this.itemListGetFunc(sgOutResultItemMqResultList);
                    omsStoInRequest.setOmsStoInItemRequests(sgOmsStoInItemRequests);
//                    omsStoInRequest.setLoginUser(WmsUserResource.getWmsUser());
                    //参数校验不通过 跳过
                    String checkResult = addOrderNoticeAndOutService.checkParam(omsStoInRequest);
                    if (StringUtils.isNotEmpty(checkResult)) {
                        continue;
                    }
//                    omsStoInRequest.add(sgPhyInNoticesBillSaveRequest);
                    //调用库存接口订单明细不能为空或者是全退款明细，不允许审核！
                    try {
                        if (log.isDebugEnabled()) {
                            log.debug(this.getClass().getName() + " 调用sg生成生成入库通知单,生成逻辑收货单,生成入库结果单接口入参={}", JSONObject.parseObject(JSONObject.toJSONString(omsStoInRequest)));
                        }
                        ValueHolderV14 result = sgRpcService.addOrderNotice(omsStoInRequest);
                        if (log.isDebugEnabled()) {
                            log.debug(this.getClass().getName() + " 调用sg生成生成入库通知单,生成逻辑收货单,生成入库结果单接口出参={}", result);
                        }
                    } catch (Exception e) {
                        log.error(LogUtil.format(" 调用sg生成生成入库通知单,生成逻辑收货单,生成入库结果单接口rpc异常={}"),
                                Throwables.getStackTraceAsString(e));
                    }
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("mqOutOrderList为null"));
                }
                return "fail";
            }
        } catch (Exception e) {
            log.error(LogUtil.format("MQ消息体转化实体异常={}"), Throwables.getStackTraceAsString(e));
            return "fail";
        }

        return "success";
    }

    /**
     * 获取主体数据信息
     **/
    private SgOmsStoInRequest getNoticesRequestFunc(SgOutResultMqResult sgOutResultMqResult, List<SgOutResultItemMqResult> sgOutResultItemMqResultList) {
        SgOmsStoInRequest notices = new SgOmsStoInRequest();
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(sgOutResultMqResult.getSourceBillId());//来源单据id --->查询订单表的订单编号字段
        //来源单据类型
        notices.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);//零售发货
        //单据日期
        notices.setBillDate(ocBOrder.getOrderDate());
        //发货方编码
        notices.setSenderEcode(ocBOrder.getReceiverName());
        //发货方名称
        notices.setSenderName(ocBOrder.getReceiverName());
        //业务节点
        notices.setServiceNode(SgConstantsIF.SERVICE_NODE_RETAIL_REF_NO_SOURCE_IN);
        notices.setSourceBillNo(sgOutResultMqResult.getSourceBillNo());//来源单据编号
        notices.setSourceBillId(sgOutResultMqResult.getSourceBillId());//来源单据ID
        return notices;
    }

    /**
     * 获取明细信息
     */
    private List<SgOmsStoInItemRequest> itemListGetFunc(List<SgOutResultItemMqResult> sgOutResultItemMqResultList) {
        List<SgOmsStoInItemRequest> itemList = new ArrayList<>();
        sgOutResultItemMqResultList.forEach(transferItem -> {
            SgOmsStoInItemRequest item = new SgOmsStoInItemRequest();
            item.setQty(transferItem.getQty());
            item.setQtyPrein(transferItem.getQty());
            item.setPsCSpec1Id(transferItem.getPsCSpec1Id());
            item.setPsCSpec1Ecode(transferItem.getPsCSpec1Ecode());
            item.setPsCSpec1Ename(transferItem.getPsCSpec1Ename());
            item.setPsCSpec2Id(transferItem.getPsCSpec2Id());
            item.setPsCSpec2Ecode(transferItem.getPsCSpec2Ecode());
            item.setPsCSpec2Ename(transferItem.getPsCSpec2Ename());
//            item.setSourceBillItemId(transferItem.getSourceBillItemId());
//            item.setPriceList(transferItem.getPriceList());
            item.setGbcode(transferItem.getGbcode());//添加国标码
            item.setPsCSkuId(transferItem.getPsCSkuId());
            item.setPsCSkuEcode(transferItem.getPsCSkuEcode());
            item.setPsCProId(transferItem.getPsCProId());
            item.setPsCProEcode(transferItem.getPsCProEcode());
            item.setPsCProEname(transferItem.getPsCProEname());
            //合并相同条码的情况条件：skuid，skucode，gbcode相同的话累积
            if (itemList.size() <= 0) {
                itemList.add(item);
            } else {
                for (SgOmsStoInItemRequest request : itemList) {
                    if (request.getPsCSkuId() != null && request.getPsCSkuId().equals(transferItem.getPsCSkuId())
                            && request.getGbcode() != null && request.getGbcode().equals(transferItem.getGbcode())
                            && request.getPsCSkuEcode() != null && request.getPsCSkuEcode().equals(transferItem.getPsCSkuEcode())) {
                        request.setQty(request.getQty() == null ? transferItem.getQty() == null ? BigDecimal.ZERO : transferItem.getQty() : request.getQty().add(transferItem.getQty()));
                        request.setQtyPrein(request.getQtyPrein() == null ? transferItem.getQty() == null ? BigDecimal.ZERO : transferItem.getQty() : request.getQtyPrein().add(transferItem.getQty()));
                    } else {
                        itemList.add(item);
                        break;
                    }
                }
            }
        });
        return itemList;
    }

    /**
     * 根据下单店铺id获取 店铺相关信息
     *
     * @param shopId 店铺id
     * @return shop
     */
    private CpShop getCpCshop(Long shopId) {
        CpShop cpShop = new CpShop();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" 调用销售渠道档案店铺rpc接口入参=", shopId));
        }
        try {
            cpShop = cpRpcService.selectShopById(shopId);//调用渠道店铺接口
        } catch (Exception e) {
            log.error(LogUtil.format(" 调用渠道店铺接口rpc异常={}"), Throwables.getStackTraceAsString(e));
            return cpShop;
        }
        return cpShop == null ? new CpShop() : cpShop;
    }

    /**
     * 调用门店档案的接口获取实体仓
     *
     * @param ids 门店主键id集合
     * @return List<CpCStore> 门店档案集合
     */
    private List<CpCStore> cpCStoreList(List<Integer> ids) {

        List<CpCStore> queryStoreInfoByIdsFunc = new ArrayList<>();
        try {
            queryStoreInfoByIdsFunc = cpExtRpcService.queryStoreInfoByIds(ids);
        } catch (Exception e) {
            log.error(LogUtil.format(" 调用门店案店铺rpc接口获取实体仓异常={}"), Throwables.getStackTraceAsString(e));
            return queryStoreInfoByIdsFunc;
        }
        return queryStoreInfoByIdsFunc;
    }

}
