package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.OcBPreOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBPreOrderEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBPreOrderQueryModel;
import com.jackrain.nea.oc.oms.model.request.OcBPreOrderQueryRequest;
import com.jackrain.nea.oc.oms.util.Permission4ESUtil;
import com.jackrain.nea.oc.oms.util.Permission4SlfEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName OcBPreOrderQueryService
 * @Description 订单预导入查询
 * <AUTHOR>
 * @Date 2022/10/13 17:18
 * @Version 1.0
 */
@Component
@Slf4j
public class OcBPreOrderQueryService {

    @Autowired
    private OcBPreOrderMapper ocBPreOrderMapper;

    public ValueHolder preOrderQuery(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();

        DefaultWebEvent event = querySession.getEvent();
        JSONObject resultData = new JSONObject();
        JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
        JSONObject fixColumn = param.getJSONObject("fixedcolumns");
        Integer range = param.getInteger("range") == null ? QueryUtils.getdefalutrange() : param.getInteger("range");
        Integer startIndex = param.getInteger("startindex") == null ? 0 : param.getInteger("startindex");
        OcBPreOrderQueryRequest preOrderQueryRequest = fixColumn.toJavaObject(OcBPreOrderQueryRequest.class);
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKey = new JSONObject();
        JSONArray orderByKey = this.getOrderByKey();
        List<Long> ids;
        Integer totalCount;
        try {
            buildWhereKeys(whereKeys, preOrderQueryRequest);
            buildFilterKey(filterKey, preOrderQueryRequest);
            boolean result = Permission4ESUtil.permissionHandler(querySession.getUser(), whereKeys, Permission4SlfEnum.SHOP);
            if (!result) {
                resultData.put("start", startIndex);
                resultData.put("row", "");
                resultData.put("totalRowCount", 0);
                vh.put("data", resultData);
                vh.put("code", 0);
                vh.put("message", "success");
                return vh;
            }
            JSONObject esResult = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_PRE_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_PRE_ORDER_TYPE_NAME,
                    whereKeys, filterKey, orderByKey, range, startIndex, new String[]{"ID"});
            JSONArray aryIds = esResult.getJSONArray("data");
            totalCount = esResult.getInteger("total");
            if (CollectionUtils.isEmpty(aryIds)) {
                resultData.put("start", startIndex);
                resultData.put("row", "");
                resultData.put("totalRowCount", totalCount);
                vh.put("data", resultData);
                vh.put("code", 0);
                vh.put("message", "success");
                return vh;
            }
            ids = Lists.newArrayList();
            for (int i = 0; i < aryIds.size(); i++) {
                Map<String, Long> map = (Map<String, Long>) aryIds.get(i);
                ids.add(map.get("ID"));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询订单预导入页面异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            resultData.put("start", startIndex);
            resultData.put("row", "");
            resultData.put("totalRowCount", 0);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
            return vh;
        }
        // 通过预导入订单id查询数据
        List<OcBPreOrderQueryModel> ocBPreOrderList = ocBPreOrderMapper.selectByIds(ids);
        for (OcBPreOrderQueryModel model : ocBPreOrderList) {
            model.setTransferStatusName(OcBPreOrderEnum.OcBPreTransferEnum.getDescByVal(model.getTransferStatus()));
            model.setOrderDateStr(DateUtil.format(model.getOrderDate(), "yyyy-MM-dd HH:mm:ss"));
            model.setPayTimeStr(DateUtil.format(model.getPayTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        JSONArray jsonArray = (JSONArray) JSONArray.toJSON(ocBPreOrderList);
        List<JSONObject> jsonObjectList = JSONObject.parseArray(
                JSONObject.toJSONString(jsonArray, SerializerFeature.WriteMapNullValue), JSONObject.class);
        JSONArray getFrameDataFormat = getFrameDataFormat(jsonObjectList);
        resultData.put("start", startIndex);
        resultData.put("rowCount", range);
        resultData.put("row", getFrameDataFormat);
        resultData.put("totalRowCount", totalCount);
        vh.put("data", resultData);
        vh.put("code", 0);
        vh.put("message", "success");
        return vh;
    }

    private void buildFilterKey(JSONObject filterKey, OcBPreOrderQueryRequest request) {
        if (ObjectUtil.isNotEmpty(request.getCreationDate())) {
            String orderDate = request.getCreationDate();
            String[] orderSplitDate = orderDate.split("~");
            String orderDateResult = convertDate(orderSplitDate[0], orderSplitDate[1]);
            filterKey.put("CREATIONDATE", orderDateResult);
        }
    }

    private void buildWhereKeys(JSONObject whereKeys, OcBPreOrderQueryRequest request) {

        if (ObjectUtil.isNotEmpty(request.getTid())) {
            String sourceCode = request.getTid();
            String sourceCodeReplace = sourceCode.replaceAll("\\s*", "");
            String[] splitSourceCode = sourceCodeReplace.split(",|，");
            JSONArray jsonArray = new JSONArray(Arrays.asList(splitSourceCode));
            whereKeys.put("TID", jsonArray);
        }

        if (CollectionUtil.isNotEmpty(request.getTransferStatus())) {
            List<String> transferStatus = request.getTransferStatus();
            JSONArray jsonArray = new JSONArray();
            for (String to : transferStatus) {
                jsonArray.add(to.replaceAll("=", ""));
            }
            whereKeys.put("TRANSFER_STATUS", jsonArray);
        }

        if (CollectionUtil.isNotEmpty(request.getCpcShopId())) {
            whereKeys.put("CP_C_SHOP_ID", request.getCpcShopId());
        }

        if (StringUtils.isNotEmpty(request.getReceiverMobile())) {
            String phone = request.getReceiverMobile();
            whereKeys.put("RECEIVER_MOBILE", phone);
        }

        if (StringUtils.isNotEmpty(request.getReceiverName())) {
            String phone = request.getReceiverName();
            whereKeys.put("RECEIVER_NAME", phone);
        }

        if (StringUtils.isNotEmpty(request.getSerialNum())) {
            String serialNum = request.getSerialNum();
            whereKeys.put("SERIAL_NUMBER", serialNum);
        }

        if (StringUtils.isNotEmpty(request.getOwnerId())) {
            String ownerId = request.getOwnerId();
            whereKeys.put("OWNERID", ownerId);
        }

        if (StringUtils.isNotEmpty(request.getSalesmanName())) {
            String salesmanName = request.getSalesmanName();
            whereKeys.put("SALESMAN_NAME", salesmanName);
        }

        if (StringUtils.isNotEmpty(request.getSysRemark())) {
            String sysRemark = request.getSysRemark();
            whereKeys.put("SYSREMARK", sysRemark);
        }
    }

    /**
     * 日期转成ES需要的格式
     *
     * @return
     */
    public String convertDate(String begindate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        if (StringUtils.isEmpty(begindate) || StringUtils.isEmpty(endDate)) {
            return "";
        }
        try {
            String result = sdf.parse(begindate).getTime() + "~" + sdf.parse(endDate).getTime();
            return result;

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 框架格式返回
     *
     * @param dataList
     * @return
     */
    private static JSONArray getFrameDataFormat(List<JSONObject> dataList) {
        JSONArray array = new JSONArray();
        if (dataList != null && dataList.size() > 0) {
            for (JSONObject emp : dataList) {
                Set<String> keySet = emp.keySet();
                JSONObject json = new JSONObject();
                for (String key : keySet) {
                    JSONObject val = new JSONObject();
                    val.put("val", emp.get(key));
                    json.put(key.toUpperCase(), val);
                }
                array.add(json);
            }
        }
        return array;
    }

    /**
     * ES 查询orderby条件
     *
     * @return
     */
    public JSONArray getOrderByKey() {
        JSONArray orderKeys = new JSONArray();
        JSONObject orderByKey = new JSONObject();
        orderByKey.put("desc", true);
        orderByKey.put("name", "ID");
        orderKeys.add(orderByKey);
        return orderKeys;
    }
}
