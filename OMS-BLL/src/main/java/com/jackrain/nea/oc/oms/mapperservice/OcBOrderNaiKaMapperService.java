package com.jackrain.nea.oc.oms.mapperservice;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import org.springframework.stereotype.Service;

/**
 * @ClassName OcBOrderNaiKaMapperService
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/6/30 18:00
 * @Version 1.0
 */
@Service
public class OcBOrderNaiKaMapperService extends ServiceImpl<OcBOrderNaiKaMapper, OcBOrderNaiKa> {

    @Override
    public boolean updateById(OcBOrderNaiKa unfreeze) {
        Assert.notNull(unfreeze.getOcBOrderId(), "OcBOrderId不能为空");
        Assert.notNull(unfreeze.getId(), "id不能为空");
        Wrapper<OcBOrderNaiKa> updateWrapper = new UpdateWrapper<OcBOrderNaiKa>().lambda()
                .eq(OcBOrderNaiKa::getOcBOrderId, unfreeze.getOcBOrderId())
                .eq(OcBOrderNaiKa::getId, unfreeze.getId());
        return baseMapper.update(unfreeze, updateWrapper) > 0;
    }
}
