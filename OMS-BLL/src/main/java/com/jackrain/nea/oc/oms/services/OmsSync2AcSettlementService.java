package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 全渠道订单处理服务
 *
 * @author: 易邵峰
 * @since: 2019-01-22
 * create at : 2019-01-22 22:52
 */
@Component
@Slf4j
public class OmsSync2AcSettlementService {

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;


    @Autowired
    private PostFeeHandleServiceBak postFeeHandleService;

    @Autowired
    private IpBTaobaoOrderMapper ipTaobaoOrderMapper;
    @Autowired
    private ThreadPoolTaskExecutor settlementOrderThreadPoolExecutor;

    /**
     * 记录结算日志 qb
     *
     * @param orders
     */
    public void sendBatchSettlementLog(List<OcBOrder> orders, List<Long> ids) {
        User rootUser = SystemUserResource.getRootUser();
        try {
           // List<AcPushAcBillRequest> requests = Lists.newArrayList();
            List<Long> sendIds = Lists.newArrayList();
            List<Long> noSendIds = Lists.newArrayList();
            List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItemsByIds(ids);
            // 通过子单去查询tid
            Set<String> tids = ocBOrderItems.stream().filter(item -> StringUtils.isNotBlank(item.getTid()))
                    .map(OcBOrderItem::getTid).collect(Collectors.toSet());
            if (tids.isEmpty()) {
                noSendIds.addAll(ids);
                this.updateOrderDataByIds(noSendIds, ToACStatusEnum.ERROR.getValue().longValue());
                return;
            }
            // 淘宝订单接口
            List<IpBTaobaoOrder> taobaoOrders = ipTaobaoOrderMapper.selectList(new QueryWrapper<IpBTaobaoOrder>().lambda()
                    .in(IpBTaobaoOrder::getTid, tids));
            Map<String, IpBTaobaoOrder> taobaoOrderMap = taobaoOrders.stream().collect(Collectors.toMap(IpBTaobaoOrder::getTid, Function.identity()));
            Map<Long, List<OcBOrderItem>> itemsMap = ocBOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
            long mergeArgsBegin = System.currentTimeMillis();
            for (OcBOrder order : orders) {
                Long orderId = order.getId();
                //已推送的不再推送
                /*if (null != order.getToSettleStatus() && order.getToSettleStatus().equals(ToACStatusEnum.SUCCESS.getValue().longValue())) {
                    continue;
                }*/
                Long shopId = order.getCpCShopId();
                if (null == shopId) {
                    noSendIds.add(orderId);
                    continue;
                }
                JSONObject jsonInfo = new JSONObject();
                List<OcBOrderItem> items = itemsMap.get(orderId);
                if (CollectionUtils.isEmpty(items)) {
                    noSendIds.add(orderId);
                    continue;
                }
                // 正常的订单才传运费
                if (order.getOrderType().equals(OrderTypeEnum.NORMAL.getVal())) {
                    postFeeHandleService.addPostFeeItem(order, items, taobaoOrderMap);
                }
                jsonInfo.put("OC_B_ORDER", order);
                jsonInfo.put("OC_B_ORDER_ITEM", items);
//                AcPushAcBillRequest acPushAcBillRequest = new AcPushAcBillRequest();
//                acPushAcBillRequest.setBillType(SourceBillType.OMS_ORDER);
//                acPushAcBillRequest.setBill(jsonInfo);
//                acPushAcBillRequest.setSourceBillDate(order.getScanTime());
//                acPushAcBillRequest.setSourceBillNo(order.getBillNo());
//                acPushAcBillRequest.setUser(rootUser);
//                requests.add(acPushAcBillRequest);
//                sendIds.add(orderId);
            }
            long beginUpdate = System.currentTimeMillis();
            //this.sendOrderInfo2AcByIds(requests, sendIds);
            long endUpdate = System.currentTimeMillis();
            if (!noSendIds.isEmpty()) {
                // 不传。意味着没有明细或者店铺id
                this.updateOrderDataByIds(noSendIds, ToACStatusEnum.ERROR.getValue().longValue());
            }

        } catch (Exception e) {
            log.error(LogUtil.format("Order2SettlementTask批量调用结算中心失败OmsOrderItemService,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            // @20200814 修改日志输出方式
            this.updateOrderData(orders, ToACStatusEnum.FAILED.getValue().longValue());
        }
    }

    /**
     * 更新订单结算状态
     *
     * @param orders
     */
    private void updateOrderData(List<OcBOrder> orders, Long type) {
        if (!CollectionUtils.isEmpty(orders)) {
            List<Long> ids = orders.stream().map(OcBOrder::getId).collect(Collectors.toList());
//            orderMapper.updateToSettleStatusByIds(type, ids);
        }
    }

    /**
     * 订单信息传AC
     *
     * @param
     * @param ids
     */
//    public void sendOrderInfo2AcByIds(List<AcPushAcBillRequest> requestsNew, List<Long> ids) {
//        long begin = System.currentTimeMillis();
//        if (!CollectionUtils.isEmpty(requestsNew)) {
//            if (log.isDebugEnabled()) {
//                log.debug("Order2SettlementTask execute sendBatchSettlementLog request,ids begin{}", ids.size());
//            }
//            try {
//                ValueHolderV14 valueHolderV14 = AcScOrigBillUtil.batchPushAcBill(requestsNew);
//                if (valueHolderV14 != null && valueHolderV14.getCode() == ResultCode.SUCCESS) {
//                    if (log.isDebugEnabled()) {
//                        log.debug("Order2SettlementTask execute sendBatchSettlementLog success");
//                    }
//                    // 成功
//                    this.updateOrderDataByIds(ids, ToACStatusEnum.SUCCESS.getLongValue());
//                } else {
//                    // 失败
//                    this.updateOrderDataByIds(ids, ToACStatusEnum.FAILED.getLongValue());
//                    log.error("Order2SettlementTask execute sendBatchSettlementLog fail {}", JSON.toJSONString(valueHolderV14));
//                }
//            } catch (Exception e) {
//                this.updateOrderDataByIds(ids, ToACStatusEnum.FAILED.getLongValue());
//                log.error("Order2SettlementTask execute sendBatchSettlementLog error", e);
//            }
//        }
//        if (log.isDebugEnabled()) {
//            log.debug("Order2SettlementTask execute sendBatchSettlementLog success:{}", (System.currentTimeMillis() - begin) / 1000);
//        }
//    }

    private void updateOrderDataByIds(List<Long> ids, Long type) {
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(ids)) {
//            orderMapper.updateToSettleStatusByIds(type, ids);
        }
    }


    /**
     * order_to_ac when order_status in (5,6)
     *
     * @param ids
     * @throws Exception
     */
    public void threadTask(List<Long> ids) throws Exception {
        long start = System.currentTimeMillis();
        // 创建自定义线程池
        List<List<Long>> partition = Lists.partition(ids, 200);
        // 定义一个任务集合
        Runnable task;
        for (List<Long> currentIds : partition) {
            task = () -> {
                // List<OcBOrder> ocBOrders = orderMapper.selectOrderListByIdsAndToSettleStatus(currentIds);
                List<OcBOrder> ocBOrders = new ArrayList<>();
                if (!CollectionUtils.isEmpty(ocBOrders)) {
                    this.sendBatchSettlementLog(ocBOrders, currentIds);
                }
            };
            settlementOrderThreadPoolExecutor.execute(task);
        }
    }


    /**
     * order_to_ac when order_status in (5,6)
     *
     * @throws Exception
     */
    public void threadTask(Set<String> nodes, Map<String, String> topMap, Integer limit) throws Exception {
        long start = System.currentTimeMillis();
        // 创建自定义线程池
        // 定义一个任务集合
        Runnable task;
        for (String node : nodes) {
            task = () -> {
                List<OcBOrder> ocBOrders = orderMapper.selectOrderListByIdsAndToSettleStatus(node, topMap.get(node), limit);
                if (!CollectionUtils.isEmpty(ocBOrders)) {
                    List<Long> currentIds = ocBOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());
                    this.sendBatchSettlementLog(ocBOrders, currentIds);
                }
            };
            settlementOrderThreadPoolExecutor.execute(task);
        }
        if (log.isDebugEnabled()) {
            log.debug("Order2SettlementTask execute sendBatchSettlementLog end time:{}", System.currentTimeMillis() - start);
        }
    }

    /**
     * 返回默认的系统Root用户
     *
     * @return
     */
    public static User getRootUser() {
        User user = new UserImpl();
        ((UserImpl) user).setId(SystemUserResource.ROOT_USER_ID.intValue());
        ((UserImpl) user).setEname(SystemUserResource.ROOT_ENAME);
        ((UserImpl) user).setTruename(SystemUserResource.ROOT_USER_NAME);
        ((UserImpl) user).setClientId(SystemUserResource.AD_CLIENT_ID.intValue());
        ((UserImpl) user).setOrgId(SystemUserResource.AD_ORG_ID.intValue());
        ((UserImpl) user).setName(SystemUserResource.ROOT_USER_NAME);
        return user;
    }

}
