package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.ip.model.wing.add.WingOutStoOutNoticesRequest;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OrderExchangeHoldTowingTaskMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderHoldItem;
import com.jackrain.nea.oc.oms.model.table.task.OrderExchangeHoldTowingTask;
import com.jackrain.nea.oc.oms.nums.ExchangeHoldTowingcConstant;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * description:换货传winghol单
 * @Author:  liuwenjin
 * @Date 2021/10/19 6:13 下午
 */
@Slf4j
@Component
public class OrderExchangeHoldTowingTaskService {
    @Autowired
    private OrderExchangeHoldTowingTaskMapper orderExchangeHoldTowingTaskMapper;

    @Autowired
    private IpRpcService ipRpcService;

    public  int  creatExchangeHoldTowingTask(Long orderId,String billNo,String status){
        OrderExchangeHoldTowingTask orderExchangeHoldTowingTask =new OrderExchangeHoldTowingTask();
        orderExchangeHoldTowingTask.setId(ModelUtil.getSequence(ExchangeHoldTowingcConstant.TAB_ORDER_EXCHANGE_HOLD_TOWING_TASK));
        orderExchangeHoldTowingTask.setOcBOrderId(orderId);
        orderExchangeHoldTowingTask.setBillNo(billNo);
        orderExchangeHoldTowingTask.setEstatus(status);
        makeCreateField(orderExchangeHoldTowingTask, SystemUserResource.getRootUser());
        return orderExchangeHoldTowingTaskMapper.insert(orderExchangeHoldTowingTask);
    }
    /**
     * description:验证是否存在换货hold传wing成功的订单
     * @Author:  liuwenjin
     * @Date 2021/10/26 4:59 下午
     */
    public boolean checkExist(Long orderId){
        int n = orderExchangeHoldTowingTaskMapper.selectStatusSuccess(orderId);
        return n>0?true:false;

    }
    /**
     * description:赋值字段
     * @Author:  liuwenjin
     * @Date 2021/10/19 7:50 下午
     */
    private void makeCreateField(OrderExchangeHoldTowingTask item, User user) {
        Date date = new Date();
        item.setAdClientId((long) user.getClientId());//所属公司
        item.setAdOrgId((long) user.getOrgId());//所属组织
        item.setOwnerid(Long.valueOf(user.getId()));//创建人id
        item.setCreationdate(date);//创建时间
        item.setOwnername(user.getName());//创建人用户名
        item.setOwnerename(user.getEname());
        item.setModifierid(Long.valueOf(user.getId()));//修改人id
        item.setModifiername(user.getName());//修改人用户名
        item.setModifierename(user.getEname());
        item.setModifieddate(date);//修改时间
        item.setIsactive("Y");//是否启用
    }
    /**
     * description: 批量修改时间
     * @Author:  liuwenjin
     * @Date 2021/10/20 4:33 下午
     */
    public void updateOrderExchangeHoldTowing(List<Long> orderList) {
        orderExchangeHoldTowingTaskMapper.batchUpdateDate(orderList);
    }
    /**
     * description:批量修改状态
     * @Author:  liuwenjin
     * @Date 2021/10/20 7:32 下午
     */
    public void updateOrderExchangeHoldTowingStatus(List<Long> orderList,String status,String remake) {
        orderExchangeHoldTowingTaskMapper.updateOrderExchangeHoldTowingStatus(orderList,status,remake);
    }
    /**
     * description:消费mq消息
     * @Author:  liuwenjin
     * @Date 2021/10/26 3:59 下午
     */
    public void consumer(List<WingOutStoOutNoticesRequest> list,List<String> billNoList) {
        //oms调用ip的wing hold单接口
        ValueHolderV14<String> valueHolderV14 = ipRpcService.orderExchangeHoldTowing(list);
        //接口成功
        if (valueHolderV14.isOK()){
            String wingResult = valueHolderV14.getData();
            JSONObject wingResultObj = JSON.parseObject(wingResult);
            List<String> erroList=new ArrayList<>();

            if (ExchangeHoldTowingcConstant.WING_RESULT_SUCCESS_CODE.equals(wingResultObj.getString("status"))) {
                JSONArray faileds = wingResultObj.getJSONArray("faileds");
                if (CollectionUtils.isNotEmpty(faileds)) {
                    for (int i = 0; i < faileds.size(); i++) {
                        JSONObject jsonObject = faileds.getJSONObject(i);
                        String billNo = jsonObject.getString("id");
                        String errMsg = jsonObject.getString("errMsg");
                        erroList.add(billNo);
                        orderExchangeHoldTowingTaskMapper.updateStatus(billNo, ExchangeHoldTowingcConstant.STATUS_3, errMsg);
                    }
                }
                //成功修改状态
                for (String s : billNoList) {
                    if (!erroList.contains(s)){
                        String remake = "调用wing换货hold单接口成功！";
                        orderExchangeHoldTowingTaskMapper.updateStatus(s, ExchangeHoldTowingcConstant.STATUS_4, remake);

                    }
                }
                //全部失败
            }else if (ExchangeHoldTowingcConstant.WING_RESULT_FAILALL_CODE.equals(wingResultObj.getString("status"))){
                String remake = "调用wing换货hold单传给wing接口整批失败常";
                orderExchangeHoldTowingTaskMapper.updateStatusBybillNo(billNoList,remake,ExchangeHoldTowingcConstant.STATUS_3);
            }
        }else {
            String remake = "调用wing换货hold单传给wing接口返回异常";
            orderExchangeHoldTowingTaskMapper.updateStatusBybillNo(billNoList,remake,ExchangeHoldTowingcConstant.STATUS_3);
        }
    }
    /**
     * description:调用wing取消hold单接口
     * @Author:  liuwenjin
     * @Date 2021/10/26 5:06 下午
     */
    public void orderToWingExchangeUnHold(OcBOrder order){
        try {
            if (checkExist(order.getId())){
                List<String> stringList = new ArrayList<>();
                stringList.add(order.getBillNo());
                ValueHolderV14<String> valueHolderV14 = ipRpcService.orderToWingExchangeUnHold(stringList);
                if (valueHolderV14.isOK()){
                    String wingResult = valueHolderV14.getData();
                    JSONObject wingResultObj = JSON.parseObject(wingResult);
                    if (ExchangeHoldTowingcConstant.WING_RESULT_SUCCESS_CODE.equals(wingResultObj.getString("status"))) {
                        log.debug(LogUtil.format("调用wing取消hold单接口成功,返回参数:{}", order.getId()), wingResultObj.toJSONString());

                    }
                }
            }
        }catch (Exception e){
            log.error(LogUtil.format("调用wing取消hold单接口,异常信息为：{},异常信息为:{}", order.getId()),order.getId(), Throwables.getStackTraceAsString(e));
        }
    }
}
