package com.jackrain.nea.oc.oms;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.ip.model.yike.YiKeCouponModel;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPromotionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongCoupondtai;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromotion;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;


/**
 * description:译氪接口的业务类
 * @Author:  liuwenjin
 * @Date 2021/12/6 4:05 下午
 */
@Slf4j
@Component
public class OmsYiKeService {
    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private OcBOrderPromotionMapper ocBOrderPromotionMapper;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    /**
     * description:解析译氪优惠券
     * @Author:  liuwenjin
     * @Date 2021/12/6 4:06 下午
     */
    public void parseYiKeCoupon(OcBOrder orderInfo, User operateUser) {
        YiKeCouponModel yiKeCouponModel=null;//ipRpcService.queryYiKeCoupon(orderInfo.getUseCouponNo());
        log.info("OrderId{}.TobeConfirmed.Step083QueryYiKeCouponService[订单平台是否是译氪平台],yiKeCouponModel{}",orderInfo.getId(), JSON.toJSONString(yiKeCouponModel));
        if (Objects.nonNull(yiKeCouponModel) && !yiKeCouponModel.getCode().equals("202")){
            OcBOrderPromotion item = new OcBOrderPromotion();
            item.setId(sequenceUtil.buildOrderPromotionSequenceId());
            //设置分库键
            item.setOcBOrderId(orderInfo.getId());
            item.setCreationdate(new Date());
            item.setModifierename(SystemUserResource.ROOT_USER_NAME);
            item.setModifiername(SystemUserResource.ROOT_USER_NAME);
            item.setModifieddate(new Date());
            item.setModifierid(SystemUserResource.ROOT_USER_ID);
            item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
            item.setOwnerid(SystemUserResource.ROOT_USER_ID);
            item.setOwnername(SystemUserResource.ROOT_USER_NAME);
            item.setAdClientId(SystemUserResource.AD_CLIENT_ID);
            item.setAdOrgId(SystemUserResource.AD_ORG_ID);
            item.setVersion(0L);
            //优惠名称
            item.setPromotionName(yiKeCouponModel.getCouponName());
            //优惠金额
            item.setAmtDiscount(yiKeCouponModel.getCouponPrice());
            //赠送数量
            item.setGiftItemQty(new BigDecimal("0"));
            //赠送商品
            item.setGiftItemName(null);
            //优惠活动描述
            item.setPromotionDesc(yiKeCouponModel.getMessage());
            //活动编号T
            item.setActiveId(yiKeCouponModel.getCouponNo());
            //是否启用
            item.setIsactive("Y");
            ocBOrderPromotionMapper.insert(item);
        }
        omsOrderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(),"解析译氪优惠券", yiKeCouponModel.getMessage(), "", "", operateUser);
    }
}
