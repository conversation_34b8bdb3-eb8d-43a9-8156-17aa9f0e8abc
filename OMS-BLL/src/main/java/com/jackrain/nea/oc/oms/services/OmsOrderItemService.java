package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.model.request.ProInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.result.QueryOrderItemGroupByResult;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
import com.jackrain.nea.oc.oms.sap.SapTaskTableEnum;
import com.jackrain.nea.ps.api.result.PsCProResult;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.AcScRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单明细表相关查询服务
 *
 * @author: heliu
 * @since: 2019/3/7
 * create at : 2019/3/7 13:21
 */
@Component
@Slf4j
public class OmsOrderItemService {

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private BasicPsQueryService basicPsQueryService;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private AcScRpcService acScRpcService;

    @Autowired
    private PostFeeHandleService postFeeHandleService;

    @Autowired
    private Oms2SapMapper oms2SapMapper;


    /**
     * 未退款明细
     *
     * @param id 订单ID
     * @return List<OcBOrderItem>
     */
    public List<OcBOrderItem> selectUnSuccessRefund(Long id) {
        return orderItemMapper.selectUnSuccessRefund(id);
    }

    /**
     * 未退款明细(用proType in(0,4))
     *
     * @param id 订单Id
     * @return List<OcBOrderItem>
     */
    public List<OcBOrderItem> selectUnSuccessRefundAudit(Long id) {
        return orderItemMapper.selectUnSuccessRefundAudit(id);
    }

    /**
     * 查找正常明细[排除虚拟明细]
     *
     * @param orderId          订单 ID
     * @param diffpriceskuList 不同的虚拟条码
     * @return List<OcBOrderItem>
     */
    public List<OcBOrderItem> queryNormalPriceSku(Long orderId, List<String> diffpriceskuList) {
        return orderItemMapper.selectOrderNoramlItemList(orderId, diffpriceskuList);
    }

    /**
     * 虚拟明细
     *
     * @param orderId          订单ID
     * @param diffpriceskuList 虚拟条码ID
     * @return List<OcBOrderItem>
     */
    public List<OcBOrderItem> queryDiffenPriceItem(Long orderId, List<String> diffpriceskuList) {
        return orderItemMapper.selectDiffenPriceItemList(orderId, diffpriceskuList);
    }

    /**
     * 保存对象
     *
     * @param normalOcBOrderItem 订单明细对象
     * @param orderId            订单主表Id
     * @return Integer
     */
    public boolean saveOcBOrderItem(OcBOrderItem normalOcBOrderItem, Long orderId) {
        return orderItemMapper.insert(normalOcBOrderItem) > 0;
    }

    /**
     * 更新订单明细表
     *
     * @param orderItemDto 订单对象
     * @param orderId      订单Id
     * @return boolean
     */
    public boolean updateOcBOrderItem(OcBOrderItem orderItemDto, Long orderId) {
        if (orderItemDto.getId() == null || orderItemDto.getOcBOrderId() == null) {
            return false;
        } else {
            QueryWrapper<OcBOrderItem> wrapper = new QueryWrapper<>();
            wrapper.eq("id", orderItemDto.getId());
            wrapper.eq("oc_b_order_id", orderItemDto.getOcBOrderId());
            //更新之前分库建必须设置为空
            orderItemDto.setOcBOrderId(null);
            int result = orderItemMapper.update(orderItemDto, wrapper);
            orderItemDto.setOcBOrderId(orderId);
            if (result > 0) {
                orderItemDto.setOcBOrderId(orderId);
                //return updateOrderItemEs(orderItemDto, orderId);
                return true;
            } else {
                return false;
            }
        }
    }


    /**
     * 查询订单下相同skuId明细数量之和
     *
     * @param orginId 订单主表Id
     * @param psSkuId 订单明细psSkuId
     * @return OcBOrderItem
     */
    public BigDecimal queryItemQtyAmout(Long orginId, Long psSkuId) {
        return orderItemMapper.queryItemQtyAmout(orginId, psSkuId);
    }

    /**
     * 查看原订单下相同的sku存在几条
     *
     * @param orginId 订单主表Id
     * @param psSkuId 订单明细psSkuId
     * @return Integer
     */
    public Integer queryItemBySkuIdCount(Long orginId, Long psSkuId) {
        return orderItemMapper.queryItemBySkuIdCount(orginId, psSkuId);
    }

    /**
     * 查看原订单下相同的sku分组的金额合计
     *
     * @param orderList 订单主表Id
     * @param ooidList  订单主表Id
     * @return List<QueryOrderItemGroupByResult>
     */
    public List<QueryOrderItemGroupByResult> selectUnSuccessRefundGroupByItemList(List<Long> orderList, List<String> ooidList) {
        return orderItemMapper.selectUnSuccessRefundGroupByItemList(orderList, ooidList);
    }

    /**
     * 查询明细对象ooid集合
     *
     * @param orderId
     * @return
     */
    public List<String> queryOoidList(Long orderId) {
        return orderItemMapper.queryOoidList(orderId);
    }

    /**
     * 统计新单skuid分组后的金额合计
     *
     * @param orderId
     * @param ooid
     * @return
     */
    public QueryOrderItemGroupByResult queryOrderItemGroupByResult(Long orderId, String ooid) {
        return orderItemMapper.queryOrderItemGroupByResult(orderId, ooid);
    }

    /**
     * 根据orderId集合和sku查询明细
     *
     * @param orderIdList
     * @param ooid
     * @return
     */
    public List<OcBOrderItem> queryOrderItemBySkuIdList(List<Long> orderIdList, String ooid) {
        return orderItemMapper.queryOrderItemBySkuIdList(orderIdList, ooid);
    }

    /**
     * 记录结算日志 qb
     *
     * @param orders
     */
//    public void sendBatchSettlementLog(List<OcBOrder> orders, User user) {
//        log.info("批量调用结算中心成功OmsOrderItemService：sendBatchSettlementLog开始");
//        try {
//            List<AcPushAcBillRequest> requests = Lists.newArrayList();
//            //店铺ids
//            Map<Long, CpShop> shopIds = Maps.newHashMap();
//            //退单ids
//            List<Long> returnOrderIds = Lists.newArrayList();
//            //商品ids
//            Map<Long, PsCProResult> psCProIds = Maps.newHashMap();
////            Map<Long ,BigDecimal> priceMap = Maps.newHashMap();
//            List<OcBOrderParam> orderInfos = Lists.newArrayList();
//            for (OcBOrder order : orders) {
//                //已推送的不再推送
//                if (null != order.getToSettleStatus() && order.getToSettleStatus().equals(2L)) {
//                    continue;
//                }
//                Long shopId = order.getCpCShopId();
//                if (null == shopId) {
//                    continue;
//                }
//                shopIds.put(shopId, null);
//                returnOrderIds.add(order.getId());
//                JSONObject jsonInfo = new JSONObject();
//                List<OcBOrderItem> items = orderItemMapper.selectOrderItemListByOrderId(order.getId());
//                List<JSONObject> itemList = Lists.newArrayList();
//                for (OcBOrderItem item : items) {
//                    String itemString = JSONObject.toJSONString(item);
//                    JSONObject jSONObject = JSONObject.parseObject(itemString);
//                    if (null != item.getRealAmt() && item.getRealAmt().compareTo(BigDecimal.ZERO) > 0
//                            && null != item.getQty() && item.getQty().compareTo(BigDecimal.ZERO) > 0) {
//                        jSONObject.put("UNIT_PRICE", item.getRealAmt().divide(item.getQty(), 4, BigDecimal.ROUND_HALF_DOWN));
//                    } else {
//                        jSONObject.put("UNIT_PRICE", BigDecimal.ZERO);
//                    }
//                    itemList.add(jSONObject);
//                    psCProIds.put(item.getPsCProId(), null);
//                }
//                // todo 组装运费传给结算
//                BigDecimal postFee = postFeeHandleService.getPostFee(order, items, true);
//                if (BigDecimal.ZERO.compareTo(postFee) != 0) {
//                    OcBOrderItem item = postFeeHandleService.getPostFeeItemInfo(postFee);
//                    String itemString = JSONObject.toJSONString(item);
//                    JSONObject jSONObject = JSONObject.parseObject(itemString);
//                    jSONObject.put("UNIT_PRICE", postFee);
//                    itemList.add(jSONObject);
//                }
//                jsonInfo.put("OC_B_ORDER", order);
//                jsonInfo.put("OC_B_ORDER_ITEM", itemList);
//                AcPushAcBillRequest acPushAcBillRequest = new AcPushAcBillRequest();
//                acPushAcBillRequest.setBill(jsonInfo);
//                acPushAcBillRequest.setSourceBillDate(order.getScanTime());
//                acPushAcBillRequest.setUser(user);
//                acPushAcBillRequest.setSourceBillNo(order.getBillNo());
//                requests.add(acPushAcBillRequest);
//                Integer platform = order.getPlatform();
//                /*if(null != platform && platform ==  PlatformType.JINGDONG.toInteger()){
//                    priceMap.putAll(this.jdPtPrice(order,items));
//                }*/
////                if (log.isDebugEnabled())
////                    log.debug("ChargebackCheckService:sendBatchSettlementLog:returnOrder:{}", JSON.toJSONString(order));
//
//
//                if (!isShopChannelType(order.getCpCShopId())) {
//                    OcBOrderParam ocBOrderParam = new OcBOrderParam();
//                    ocBOrderParam.setOcBOrder(order);
//                    ocBOrderParam.setOrderItemList(items);
//                    orderInfos.add(ocBOrderParam);
//                }
//            }
//
//            //封装店铺信息
//            Collection<Long> shopIdsCollection = shopIds.keySet();
//            List<Long> shopIdsList = new ArrayList(shopIdsCollection);
//            List<CpShop> cpShopList = cpShopQueryCmd.queryShopParam(shopIdsList, null);
//            if (!CollectionUtils.isEmpty(cpShopList)) {
//                cpShopList.forEach(shopInfo -> {
//                    shopIds.put(shopInfo.getId(), shopInfo);
//                });
//            }
//            //封装商品信息
//            Collection<Long> psCPIdsCollection = psCProIds.keySet();
//            List<Long> psCPIdsList = new ArrayList(psCPIdsCollection);
//            ProInfoQueryRequest request = new ProInfoQueryRequest();
//            psCPIdsList = psCPIdsList.stream().filter(x -> (x != null)).collect(Collectors.toList());
//            request.setProIdList(psCPIdsList);
//            List<PsCPro> psCPros = Lists.newArrayList();
//            try {
//                psCPros = basicPsQueryService.getProInfo(request);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            //商品模型
//            Map<String, PsCPro> proMap = Maps.newHashMap();
//            if (!CollectionUtils.isEmpty(psCPros)) {
//                psCPros.forEach(proInfo -> {
//                    proMap.put(proInfo.getEcode(), proInfo);
//                });
//            }
//
//            SgSendBillQueryResult sgSendBillQueryResult = this.querySgsSend(orders);
//            List<AcPushAcBillRequest> requestsNew = Lists.newArrayList();
//            List<String> orderIdList = Lists.newArrayList();
//
//            HashMap<String, HashMap<SgBSend, List<SgBSendItem>>> results = sgSendBillQueryResult.getResults();
//            ValueHolderV14<OnlineFundOccupyResult> onlineFundOccupyResult =
//                    acScRpcService.onlineFundOccupy(orderInfos, user, results, AcScConstantsIF.BIll_VARIETY_NODE_OUT);
//
//            Map<Long, OnlineFundOccupyBaseItemModel> baseItemModelMap = Maps.newHashMap();
//            if (onlineFundOccupyResult.getCode() == ResultCode.SUCCESS) {
//                OnlineFundOccupyResult dataResult = onlineFundOccupyResult.getData();
//                if (dataResult != null) {
//                    for (OnlineFundOccupySucModelResult itemSucModel : dataResult.getSucModelList()) {
//                        for (OnlineFundOccupyBaseItemModel itemModel : itemSucModel.getItemList()) {
//                            if (itemModel.getFreight() != null && itemModel.getFreight().compareTo(BigDecimal.ZERO) > 0) {
//                                baseItemModelMap.put(itemModel.getId(), itemModel);
//                            }
//                        }
//                    }
//                }
//            }
//
//            for (AcPushAcBillRequest acPushAcBillRequest : requests) {
//                JSONObject billInfo = acPushAcBillRequest.getBill();
//                Object orderInfo = billInfo.get("OC_B_ORDER");
//                JSONObject orderJSONObj = JSON.parseObject(JSON.toJSONString(orderInfo));
//                Integer platform = orderJSONObj.getInteger("PLATFORM");
//
//                Long shopId = orderJSONObj.getLong("CP_C_SHOP_ID");
//                CpShop cpShop = shopIds.get(shopId);
//                //增加店仓id
//                if (cpShop != null && null != cpShop.getCpCStoreId()) {
//                    orderJSONObj.put("C_STORE_ID", cpShop.getCpCStoreId());
//                    orderJSONObj.put("C_STORE_ENAME", cpShop.getCpCStoreEname());
//                    orderJSONObj.put("C_STORE_ECODE", cpShop.getCpCStoreEcode());
//                }
//                //丢单或者唯品会jitx的不生成结算单据
//                if ((platform != null && platform == PlatFormEnum.VIPJITX.getCode())
//                        || (null != orderJSONObj.getInteger("ORDER_TYPE") && orderJSONObj.getInteger("ORDER_TYPE") == OrderTypeEnum.LOST.getVal())) {
//                    orderJSONObj.put("IS_SETTLE", 0);
//                } else {
//                    orderJSONObj.put("IS_SETTLE", 1);
//                }
//                billInfo.put("OC_B_ORDER", orderJSONObj);
//                //直营1 ，分销2
//                String channelType = cpShop.getChannelType();
//                SourceBillType billType = null;
//                if (!StringUtils.isEmpty(channelType) && channelType.equals("1")) {
//                    if (platform != null && platform == PlatFormEnum.POS.getCode()) {
//                        billType = SourceBillType.OMS_ORDER;
//                    } else {
//                        billType = SourceBillType.RETAIL_DIRECTLY_SEND;
//                    }
//                } else if (!StringUtils.isEmpty(channelType) && channelType.equals("2")) {
//                    if (platform != null && platform == PlatFormEnum.POS.getCode()) {
//                        billType = SourceBillType.OMS_ORDER;
//                    } else {
//                        billType = SourceBillType.RETAIL_SBP_SEND;
//                    }
//                }
//                Integer ordertype = orderJSONObj.getInteger("ORDER_TYPE");
//                //订单类型为 虚拟,传入结算类型为 结算价 类型
//                if (ordertype.equals(OmsOrderType.DIFFPRICE.toInteger())) {
//                    billType = SourceBillType.RETAIL_FIX_DIFF;
//                }
//
//                acPushAcBillRequest.setSourceBillNo(orderJSONObj.getString("BILL_NO"));
//                acPushAcBillRequest.setBillType(billType);
//                Map<SgBSend, List<SgBSendItem>> sgSendMap;
//                String billId = String.valueOf(orderJSONObj.getLong("ID"));
//                String billTypeInt = String.valueOf(SgConstantsIF.BILL_TYPE_RETAIL);
//                String mapKey = billId + "," + billTypeInt;
//                List<SgBSendItem> sgBSendItems = Lists.newArrayList();
//                if (null != sgSendBillQueryResult && null != sgSendBillQueryResult.getResults() && null != sgSendBillQueryResult.getResults().get(mapKey)) {
//                    sgSendMap = sgSendBillQueryResult.getResults().get(mapKey);
//                    for (SgBSend sgBSend : sgSendMap.keySet()) {
//                        sgBSendItems.addAll(sgSendMap.get(sgBSend));
//                    }
//                }
//                if (log.isDebugEnabled()) log.debug("发货封装结算参数信息：封装参数billInfo" + JSON.toJSONString(billInfo));
//                JSONArray orderItemArray = billInfo.getJSONArray("OC_B_ORDER_ITEM");
//                JSONArray newOrderItem = new JSONArray();
//                Integer size = orderItemArray.size();
//                Boolean storeFlg = Boolean.TRUE;
//                for (int j = 0; j < size; j++) {
//                    Object o = orderItemArray.get(j);
//                    String itemString = JSONObject.toJSONString(o);
//                    JSONObject orderItemJSONObj = JSON.parseObject(JSON.toJSONString(o));
//                    String psCProEcode = orderItemJSONObj.getString("PS_C_PRO_ECODE");
//                    Long itemsId = orderItemJSONObj.getLong("ID");
//                    Integer itemSize = sgBSendItems.size();
//                    //是否查到逻辑仓标记 ，如果没有查到，就不要调用结算日志接口
//                    Boolean flag = Boolean.FALSE;
//                    //累计
//                    BigDecimal lcjje = BigDecimal.ZERO;
//                    BigDecimal lptje = BigDecimal.ZERO;
//                    BigDecimal lfxje = BigDecimal.ZERO;
//                    //总数量
//                    BigDecimal zfqty = orderItemJSONObj.getBigDecimal("QTY") == null ? BigDecimal.ZERO : orderItemJSONObj.getBigDecimal("QTY");
//                    //实际成交金额
//                    BigDecimal realAmt = orderItemJSONObj.getBigDecimal("REAL_AMT") == null ? BigDecimal.ZERO : orderItemJSONObj.getBigDecimal("REAL_AMT");
//                    //分销金额
//                    BigDecimal distributionPrice = orderItemJSONObj.getBigDecimal("DISTRIBUTION_PRICE") == null ? BigDecimal.ZERO : orderItemJSONObj.getBigDecimal("DISTRIBUTION_PRICE");
//                    //平摊金额
//                    BigDecimal ordersplitAmt = orderItemJSONObj.getBigDecimal("ORDER_SPLIT_AMT") == null ? BigDecimal.ZERO : orderItemJSONObj.getBigDecimal("ORDER_SPLIT_AMT");
//                    for (int i = 0; i < itemSize; i++) {
//                        SgBSendItem sgBSendItem = sgBSendItems.get(i);
//                        if (sgBSendItem.getSourceBillItemId().equals(itemsId)) {
//                            flag = Boolean.TRUE;
//                            JSONObject jSONObject = JSONObject.parseObject(itemString);
//                            jSONObject.put("CP_C_STORE_ECODE", sgBSendItem.getCpCStoreEcode());
//                            jSONObject.put("CP_C_STORE_ENAME", sgBSendItem.getCpCStoreEname());
//                            jSONObject.put("CP_C_STORE_ID", sgBSendItem.getCpCStoreId());
//                            //发货数量
//                            BigDecimal qty = sgBSendItem.getQtySend() == null ? BigDecimal.ZERO : sgBSendItem.getQtySend();
//                            BigDecimal qtyPreout = sgBSendItem.getQtyPreout() == null ? BigDecimal.ZERO : sgBSendItem.getQtyPreout();
//                            //数量 = 逻辑发货单中出库数量
//                            jSONObject.put("QTY", qty);
//                            if (itemSize != 1 && i != itemSize - 1) {
//                                //成交金额 = 成交金额 * 逻辑发货单的出库数量 / 总数量
//                                BigDecimal cj = realAmt.multiply(qty).divide(zfqty, 4, BigDecimal.ROUND_HALF_UP);
//                                lcjje = lcjje.add(cj);
//                                jSONObject.put("REAL_AMT", cj);
//                                //平摊优惠 = 平摊金额 * 逻辑发货单的出库数量 / 总数量
//                                BigDecimal pt = ordersplitAmt.multiply(qty).divide(zfqty, 4, BigDecimal.ROUND_HALF_UP);
//                                lptje = lptje.add(pt);
//                                jSONObject.put("ORDER_SPLIT_AMT", pt);
//                                //分销金额 = 分销金额 * 逻辑发货单的出库数量 / 总数量
//                                BigDecimal fx = distributionPrice.multiply(qty).divide(zfqty, 4, BigDecimal.ROUND_HALF_UP);
//                                lfxje = lfxje.add(fx);
//                                jSONObject.put("DISTRIBUTION_PRICE", fx);
//                            } else if (i == itemSize - 1) {
//                                //成交金额 = 实际总成交-累计值
//                                BigDecimal cj = realAmt.subtract(lcjje);
//                                jSONObject.put("REAL_AMT", cj);
//                                //平摊优惠 = 总平摊金额 - 累计值
//                                BigDecimal pt = ordersplitAmt.subtract(lptje);
//                                jSONObject.put("ORDER_SPLIT_AMT", pt);
//                                //分销金额 = 总分销金额 - 累计值
//                                BigDecimal fx = distributionPrice.subtract(lfxje);
//                                jSONObject.put("DISTRIBUTION_PRICE", fx);
//                            }
//                            //封装商品信息
//                            PsCPro psCPro = proMap.get(psCProEcode);
//                            if (psCPro != null) {
//                                PsCProResult psCProResult = JSON.parseObject(JSON.toJSONString(psCPro), PsCProResult.class);
//                                jSONObject.put("GOODS_PRICE_LIST", psCProResult.getPricelist());
//                                jSONObject.put("PRO_LABEL_JSON", psCProResult);
//                            }
//                            //单条平台金额优惠金额
//                            if (null != platform && platform == PlatformType.JINGDONG.toInteger()) {
////                                BigDecimal price = priceMap.get(orderItemJSONObj.getLong("ID"));
//                                BigDecimal price = jSONObject.getBigDecimal("RESERVE_DECIMAL03");
//                                jSONObject.put("JD_PRICE", price);
//                            }
//                            //代销运费
//                            if (baseItemModelMap.containsKey(itemsId)) {
//                                jSONObject.put("CONSIGN_SHIP_AMT", baseItemModelMap.get(itemsId).getFreight());
//                            }
//                            newOrderItem.add(jSONObject);
//                        }
//                    }
//                    if (!flag) { //明细封装商品信息
//                        storeFlg = Boolean.FALSE;
//                        JSONObject jsonObject = JSONObject.parseObject(itemString);
//                        //逻辑仓信息
//                        jsonObject.put("CP_C_STORE_ECODE", cpShop.getCpCStoreEcode());
//                        jsonObject.put("CP_C_STORE_ENAME", cpShop.getCpCStoreEname());
//                        jsonObject.put("CP_C_STORE_ID", cpShop.getCpCStoreId());
//
//                        //明细封装商品信息
//                        //封装商品信息
//                        PsCPro psCPro = proMap.get(psCProEcode);
//                        if (psCPro != null) {
//                            PsCProResult psCProResult = JSON.parseObject(JSON.toJSONString(psCPro), PsCProResult.class);
//                            jsonObject.put("GOODS_PRICE_LIST", psCProResult.getPricelist());
//                            jsonObject.put("PRO_LABEL_JSON", psCProResult);
//                        }
//                        //单条平台金额优惠金额
//                        if (null != platform && platform == PlatformType.JINGDONG.toInteger()) {
////                            BigDecimal price = priceMap.get(orderItemJSONObj.getLong("ID"));
//                            BigDecimal price = jsonObject.getBigDecimal("RESERVE_DECIMAL03");
//                            jsonObject.put("JD_PRICE", price);
//                        }
//                        //代销运费
//                        if (baseItemModelMap.containsKey(itemsId)) {
//                            jsonObject.put("CONSIGN_SHIP_AMT", baseItemModelMap.get(itemsId).getFreight());
//                        }
//                        newOrderItem.add(jsonObject);
//                    }
//
//                    billInfo.put("OC_B_ORDER_ITEM", newOrderItem);
//                    if (log.isDebugEnabled()) log.debug("发货封装结算参数信息：封装参数billInfo111" + JSON.toJSONString(billInfo));
//                }
//                if (storeFlg) {
//                    requestsNew.add(acPushAcBillRequest);
//                    orderIdList.add(billId);
//                }
//            }
//            if (!CollectionUtils.isEmpty(requestsNew)) {
//                if (log.isDebugEnabled())
//                    log.debug("批量调用结算中心成功 OmsOrderItemService：sendSettlementLog：request开始调用{}", JSON.toJSONString(requestsNew));
//                ValueHolderV14 valueHolderV14 = AcScOrigBillUtil.batchPushAcBill(requestsNew);
//                if (valueHolderV14 != null && valueHolderV14.getCode() == ResultCode.SUCCESS) {
//                    if (log.isDebugEnabled())
//                        log.debug("批量调用结算中心成功 OmsOrderItemService：sendSettlementLog成功");
//                    List<OcBOrder> ordersNew = Lists.newArrayList();
//                    for (OcBOrder ocBOrder : orders) {
//                        if (orderIdList.contains(ocBOrder.getId().toString())) {
//                            ordersNew.add(ocBOrder);
//                        }
//                    }
//                    this.updateOrderData(ordersNew, 2L);
//                } else {
//                    this.updateOrderData(orders, 3L);
//                    log.error("批量调用结算中心失败 OmsOrderItemService：sendSettlementLog失败0：{}", JSON.toJSONString(valueHolderV14));
//                }
//            }
//        } catch (Exception e) {
//            // @20200814 修改日志输出方式
//            // e.printStackTrace();
//            this.updateOrderData(orders, 3L);
//            if (!CollectionUtils.isEmpty(orders)) {
//                log.error("批量调用结算中心失败OmsOrderItemService：sendSettlementLog失败1:", e);
//                // log.error("批量调用结算中心失败OmsOrderItemService：sendSettlementLog失败1" + e.getMessage());
//            } else {
//                log.error("批量调用结算中心失败OmsOrderItemService：sendSettlementLog失败2:", e);
//                // log.error("批量调用结算中心失败OmsOrderItemService：sendSettlementLog失败2" + e.getMessage());
//            }
//        }
//    }


    /**
     * 记录结算日志 qb
     *
     * @param orders
     */
    public void sendBatchSettlementLog(List<OcBOrder> orders) {
        OmsOrderItemService bean = ApplicationContextHandle.getBean(OmsOrderItemService.class);
        User rootUser = SystemUserResource.getRootUser();
        try {
           // List<AcPushAcBillRequest> requests = Lists.newArrayList();
            List<Long> sendIds = Lists.newArrayList();
            List<Long> noSendIds = Lists.newArrayList();
            for (OcBOrder order : orders) {
                Long orderId = order.getId();
                //已推送的不再推送
                /*if (null != order.getToSettleStatus() && order.getToSettleStatus().equals(ToACStatusEnum.SUCCESS.getValue().longValue())) {
                    continue;
                }*/
                Long shopId = order.getCpCShopId();
                if (null == shopId) {
                    noSendIds.add(orderId);
                    continue;
                }
                JSONObject jsonInfo = new JSONObject();
                List<OcBOrderItem> items = orderItemMapper.selectUnSuccessRefundItem(orderId);
                if (CollectionUtils.isEmpty(items)) {
                    noSendIds.add(orderId);
                    continue;
                }
                // 正常的订单才传运费
                if (order.getOrderType().equals(OrderTypeEnum.NORMAL.getVal())) {
                    postFeeHandleService.getItem(order, items);
                }
                jsonInfo.put("OC_B_ORDER", order);
                jsonInfo.put("OC_B_ORDER_ITEM", items);
//                AcPushAcBillRequest acPushAcBillRequest = new AcPushAcBillRequest();
//                acPushAcBillRequest.setBillType(SourceBillType.OMS_ORDER);
//                acPushAcBillRequest.setBill(jsonInfo);
//                acPushAcBillRequest.setSourceBillDate(order.getScanTime());
//                acPushAcBillRequest.setSourceBillNo(order.getBillNo());
//                acPushAcBillRequest.setUser(rootUser);
//                requests.add(acPushAcBillRequest);
                sendIds.add(orderId);
            }
            //this.sendOrderInfo2AcByIds(requests, sendIds);
            if (!noSendIds.isEmpty()) {
                // 不传。意味着没有明细或者店铺id
                bean.updateOrderDataByIds(noSendIds, ToACStatusEnum.ERROR.getValue().longValue());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("批量调用结算中心失败OmsOrderItemService,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            // @20200814 修改日志输出方式
            bean.updateOrderData(orders, ToACStatusEnum.FAILED.getValue().longValue());
        }
    }


    /**
     * 订单信息传AC
     *
     * @param requestsNew
     * @param ids
     */
//    public void sendOrderInfo2AcByIds(List<AcPushAcBillRequest> requestsNew, List<Long> ids) {
//        OmsOrderItemService bean = ApplicationContextHandle.getBean(OmsOrderItemService.class);
//        if (!CollectionUtils.isEmpty(requestsNew)) {
//            if (log.isDebugEnabled()) {
//                log.debug("批量调用结算中心成功 OmsOrderItemService：sendSettlementLog:request,ids开始调用{}", JSON.toJSONString(ids));
//            }
//            try {
//                ValueHolderV14 valueHolderV14 = AcScOrigBillUtil.batchPushAcBill(requestsNew);
//                if (valueHolderV14 != null && valueHolderV14.getCode() == ResultCode.SUCCESS) {
//                    if (log.isDebugEnabled()) {
//                        log.debug("批量调用结算中心成功 OmsOrderItemService：sendSettlementLog成功");
//                    }
//                    // 成功
//                    bean.updateOrderDataByIds(ids, ToACStatusEnum.SUCCESS.getLongValue());
//                } else {
//                    // 失败
//                    bean.updateOrderDataByIds(ids, ToACStatusEnum.FAILED.getLongValue());
//                    log.error("批量调用结算中心失败 OmsOrderItemService：sendSettlementLog失败0：{}", JSON.toJSONString(valueHolderV14));
//                }
//            } catch (Exception e) {
//                bean.updateOrderDataByIds(ids, ToACStatusEnum.FAILED.getLongValue());
//                log.error("批量调用结算中心失败 OmsOrderItemService：sendSettlementLog失败1{}", e);
//            }
//        }
//    }

    /**
     * 计算平摊金额
     *
     * @param order
     * @param items
     * @return
     */
    public Map<Long, BigDecimal> jdPtPrice(OcBOrder order, List<OcBOrderItem> items) {
        Map<Long, BigDecimal> pricesMap = Maps.newHashMap();
        if (null != order && !CollectionUtils.isEmpty(items)) {
            Integer platform = order.getPlatform();
            if (PlatFormEnum.JINGDONG.getCode().equals(platform)) {
                BigDecimal jdReceiveAmt = order.getJdReceiveAmt() == null ? BigDecimal.ZERO : order.getJdReceiveAmt();
                BigDecimal sumYhPrice = this.getSumYhPrice(items);
                Integer size = items.size();
                BigDecimal itemAdd = BigDecimal.ZERO;
                for (int i = 0; i < size; i++) {
                    OcBOrderItem item = items.get(i);
                    //均摊金额
                    BigDecimal jtPrice = BigDecimal.ZERO;
                    BigDecimal itemPrice = item.getPrice() == null ? BigDecimal.ZERO : item.getPrice();
                    BigDecimal qty = item.getQty();
                    if (i == size - 1) {
                        jtPrice = jdReceiveAmt.subtract(itemAdd).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                    } else {
                        jtPrice = itemPrice.multiply(qty).divide(sumYhPrice, 10, BigDecimal.ROUND_HALF_DOWN).multiply(jdReceiveAmt).setScale(2, BigDecimal.ROUND_HALF_UP);
                        itemAdd = itemAdd.add(jtPrice);
                    }
                    pricesMap.put(item.getId(), jtPrice.divide(item.getQty(), 4, BigDecimal.ROUND_HALF_DOWN));
                }
            }
        }
        return pricesMap;
    }

    /**
     * 获取分母
     *
     * @param items
     * @return
     */
    private BigDecimal getSumYhPrice(List<OcBOrderItem> items) {
        BigDecimal price = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(items)) {
            for (OcBOrderItem item : items) {
                BigDecimal itemPrice = item.getPrice() == null ? BigDecimal.ZERO : item.getPrice();
                BigDecimal itemQty = item.getQty() == null ? BigDecimal.ZERO : item.getQty();
                //【（当前明细“成交价格”*“数量”/sum（明细“成交价格”*“数量”）】*主表的订单优惠金额，保留两位小数
                BigDecimal tempPirce = itemPrice.multiply(itemQty);
                price = price.add(tempPirce);
            }
        }
        return price;
    }

    /**
     * 获取分母
     *
     * @param orderItemArray
     * @return
     */
    private BigDecimal getSumYhPrice(JSONArray orderItemArray) {
        BigDecimal price = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(orderItemArray)) {
            for (Object o : orderItemArray) {
                JSONObject orderItemJSONObj = JSON.parseObject(JSON.toJSONString(o));
                BigDecimal itemPrice = orderItemJSONObj.getBigDecimal("PRICE") == null ? BigDecimal.ZERO : orderItemJSONObj.getBigDecimal("PRICE");
                BigDecimal itemQty = orderItemJSONObj.getBigDecimal("QTY") == null ? BigDecimal.ZERO : orderItemJSONObj.getBigDecimal("QTY");
                //【（当前明细“成交价格”*“数量”/sum（明细“成交价格”*“数量”）】*主表的订单优惠金额，保留两位小数
                BigDecimal tempPirce = itemPrice.multiply(itemQty);
                price = price.add(tempPirce);
            }
        }
        return price;
    }


    /**
     * 更新订单结算状态
     *
     * @param orders
     */
    @Transactional(rollbackFor = Throwable.class)
    public void updateOrderData(List<OcBOrder> orders, Long type) {
        if (!CollectionUtils.isEmpty(orders)) {
            List<Long> ids = orders.stream().map(OcBOrder::getId).collect(Collectors.toList());
//            orderMapper.updateToSettleStatusByIds(type, ids);
            // 修改任务表传ac状态
            oms2SapMapper.updateDynamicOrigOrder(SapTaskTableEnum.ORDER.txt(), "to_ac_status", type.intValue(), ids);

        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public void updateOrderDataByIds(List<Long> ids, Long type) {
        if (!CollectionUtils.isEmpty(ids)) {
//            orderMapper.updateToSettleStatusByIds(type, ids);
            // 修改任务表传ac状态
            oms2SapMapper.updateDynamicOrigOrder(SapTaskTableEnum.ORDER.txt(), "to_ac_status", type.intValue(), ids);
        }
    }

    /**
     * 更新传对账中心-订单交易完成或者交易失败时候，传送状态
     *
     * @param order,tradeType,type
     */
    private void updateOrderDataByTrade(OcBOrder order, String tradeType, BigDecimal type) {
        if (null != order && StringUtils.isNotEmpty(tradeType)) {
            OcBOrder tempOrder = new OcBOrder();
            tempOrder.setId(order.getId());
           /* if (TaoBaoOrderStatus.TRADE_FINISHED.equals(tradeType)) {
                //交易完成状态 数据传输对账中心后 更新（2：成功，3失败）
                tempOrder.setStatusToTradSuc(type);
            } else {
                //交易关闭状态 数据传输对账中心后 更新（2：成功，3失败）
                tempOrder.setStatusToTradFail(type);
            }*/
            orderMapper.updateById(tempOrder);
        }
    }

    /**
     * 封装结算参数信息
     *
     * @param items
     * @param sgBSendItems
     * @param itemList
     */
//    private void encapsulationItems(List<OcBOrderItem> items, List<SgBSendItem> sgBSendItems, List<JSONObject> itemList) {
//        if (log.isDebugEnabled()) {
//            log.debug("发货封装结算参数信息：封装参数");
//        }
//        for (OcBOrderItem item : items) {
//            String itemString = JSONObject.toJSONString(item);
//            Long psCProId = item.getPsCProId();
//            Integer itemSize = sgBSendItems.size();
//            //是否查到逻辑仓标记 ，如果没有查到，也要调用结算日志接口
//            Boolean flag = Boolean.FALSE;
//            //累计
//            BigDecimal lcjje = BigDecimal.ZERO;
//            BigDecimal lptje = BigDecimal.ZERO;
//            BigDecimal lfxje = BigDecimal.ZERO;
//            for (int i = 0; i < itemSize; i++) {
//                SgBSendItem sgBSendItem = sgBSendItems.get(i);
//                if (sgBSendItem.getSourceBillItemId().equals(item.getId())) {
//                    flag = Boolean.TRUE;
//                    JSONObject jSONObject = JSONObject.parseObject(itemString);
//                    jSONObject.put("CP_C_STORE_ECODE", sgBSendItem.getCpCStoreEcode());
//                    jSONObject.put("CP_C_STORE_ENAME", sgBSendItem.getCpCStoreEname());
//                    jSONObject.put("CP_C_STORE_ID", sgBSendItem.getCpCStoreId());
//                    if (itemSize != 1 && i != itemSize - 1) {
//                        //发货数量
//                        BigDecimal qty = sgBSendItem.getQtySend();
//                        //总数量
//                        BigDecimal zfqty = item.getQty();
//                        //实际成交金额
//                        BigDecimal realAmt = item.getRealAmt();
//                        //分销金额
//                        BigDecimal distributionPrice = item.getDistributionPrice();
//                        //平摊金额
//                        BigDecimal ordersplitAmt = item.getOrderSplitAmt();
//                        //数量 = 逻辑发货单中出库数量
//                        jSONObject.put("QTY", qty);
//                        //成交金额 = 成交金额 * 逻辑发货单的出库数量 / 总数量
//                        BigDecimal cj = realAmt.multiply(qty).divide(zfqty, 4, BigDecimal.ROUND_HALF_UP);
//                        lcjje = lcjje.add(cj);
//                        jSONObject.put("REAL_AMT", cj);
//                        //平摊优惠 = 平摊金额 * 逻辑发货单的出库数量 / 总数量
//                        BigDecimal pt = ordersplitAmt.multiply(qty).divide(zfqty, 4, BigDecimal.ROUND_HALF_UP);
//                        lptje = lptje.add(pt);
//                        jSONObject.put("ORDER_SPLIT_AMT", pt);
//                        //分销金额 = 分销金额 * 逻辑发货单的出库数量 / 总数量
//                        BigDecimal fx = distributionPrice.multiply(qty).divide(zfqty, 4, BigDecimal.ROUND_HALF_UP);
//                        lfxje = lfxje.add(fx);
//                        jSONObject.put("DISTRIBUTION_PRICE", fx);
//                    } else if (i == itemSize - 1) {
//                        //发货数量
//                        BigDecimal qty = sgBSendItem.getQtySend();
//                        //实际成交金额
//                        BigDecimal realAmt = item.getRealAmt();
//                        //平摊优惠
//                        BigDecimal ordersplitAmt = item.getOrderSplitAmt();
//                        //分销金额
//                        BigDecimal distributionPrice = item.getDistributionPrice();
//                        //数量 = 逻辑发货单中出库数量
//                        jSONObject.put("QTY", qty);
//                        //成交金额 = 实际总成交-累计值
//                        BigDecimal cj = realAmt.subtract(lcjje);
//                        jSONObject.put("REAL_AMT", cj);
//                        //平摊优惠 = 总平摊金额 - 累计值
//                        BigDecimal pt = ordersplitAmt.subtract(lptje);
//                        jSONObject.put("ORDER_SPLIT_AMT", pt);
//                        //分销金额 = 总分销金额 - 累计值
//                        BigDecimal fx = distributionPrice.subtract(lfxje);
//                        jSONObject.put("DISTRIBUTION_PRICE", fx);
//                    }
//                    //封装商品信息
//                    PsCProResult psCProResult = this.querySkuInfoById(psCProId);
//                    if (psCProResult != null) {
//                        jSONObject.put("GOODS_PRICE_LIST", psCProResult.getPricelist());
//                        jSONObject.put("PRO_LABEL_JSON", psCProResult);
//                    }
//                    itemList.add(jSONObject);
//                }
//            }
//            if (!flag) { //明细封装商品信息
//                JSONObject jsonObject = JSONObject.parseObject(itemString);
//                //明细封装商品信息
//                //封装商品信息
//                PsCProResult psCProResult = this.querySkuInfoById(psCProId);
//                if (psCProResult != null) {
//                    jsonObject.put("GOODS_PRICE_LIST", psCProResult.getPricelist());
//                    jsonObject.put("PRO_LABEL_JSON", psCProResult);
//                }
//                itemList.add(jsonObject);
//            }
//        }
//    }

    /**
     * 根据skuid查询商品信息
     *
     * @param psCProId
     * @return
     */
    private PsCProResult querySkuInfoById(Long psCProId) {
        PsCProResult psCPro = new PsCProResult();
        try {
            ProInfoQueryRequest request = new ProInfoQueryRequest();
            request.setProIdList(Lists.newArrayList(psCProId));
            List<PsCProResult> psCPros = basicPsQueryService.getLittleProInfo(request);
            if (!CollectionUtils.isEmpty(psCPros)) {
                psCPro = psCPros.get(0);
            }
        } catch (Exception e) {
            e.printStackTrace();

            log.error("订单仓库发货，调用结算接口，根据skuid查询商品信息出错={}", e.getMessage());

        }
        return psCPro;
    }


    /**
     * 指定id，获取明细
     */
    public OcBOrderItem getOrderItemById(Long id) {
        return orderItemMapper.selectById(id);
    }

    /**
     * 未退款明细,未拆分的组合商品
     *
     * @param id 订单ID
     * @return List<OcBOrderItem>
     */
    public List<OcBOrderItem> selectUnSuccessRefundAndGroupOrderItem(Long id) {
        return orderItemMapper.selectUnSuccessRefundAndGroupOrderItem(id);
    }

    /**
     * 指定id，获取明细
     */
    public List<OcBOrderItem> getOrderItemOcBOrderId(Long id) {
        return orderItemMapper.selectOrderItems(id);
    }

    /**
     * @param orderId
     * @param orderItemId
     * @param skuEcode
     * @return
     * @20200811 计算可退数量需求 通过参数找订单明细
     */
    public List<OcBOrderItem> findOcBOrderItemsByIdOrSkuEcode(Long orderId, Long orderItemId, String skuEcode) {
        if (Objects.isNull(orderId)) {
            log.error("findOcBOrderItemsByIdOrSkuEcode.orderId.isNull:{},{},{}", orderId, orderItemId, skuEcode);
            return null;
        }

        List<OcBOrderItem> orderItems = null;

        // 如果ID不为空，则按ID查询优先
        if (Objects.nonNull(orderItemId)) {
            // 直接查
            orderItems = orderItemMapper.selectList(new LambdaQueryWrapper<OcBOrderItem>()
                    .eq(OcBOrderItem::getOcBOrderId, orderId)
                    .eq(OcBOrderItem::getId, orderItemId));
        }

        // 如果ID为空，或者ID查不到，则按SKU查询
        // 如果ID不为空，且查询到了记录，则不再查询
        if (org.apache.commons.collections.CollectionUtils.isEmpty(orderItems) && Objects.nonNull(skuEcode) && Objects.nonNull(orderId)) {
            orderItems = orderItemMapper.selectList(new LambdaQueryWrapper<OcBOrderItem>()
                    .eq(OcBOrderItem::getOcBOrderId, orderId)
                    .eq(OcBOrderItem::getPsCSkuEcode, skuEcode));
        }

        return orderItems;
    }

    /**
     * 更新订单明细的发货状态
     *
     * @param taobaoOrderItemList
     * @param orderId
     */
    public void updateOrderItemDeliveryStatus(List<IpBTaobaoOrderItemEx> taobaoOrderItemList, Long orderId) {
        List<Long> oidList = taobaoOrderItemList.stream().filter(s -> s.getStatus().equals(TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS)).map(IpBTaobaoOrderItemEx::getOid).collect(Collectors.toList());
        /**
         *  获取可发货的订单明细
         */
        if (CollectionUtils.isNotEmpty(oidList)) {
            List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectItemListOfUnshippedAndNonGift(orderId);
            ocBOrderItems.stream().forEach(s -> {
                if (oidList.contains(Long.valueOf(s.getOoid()))&&!Integer.valueOf(1).equals(s.getIsExchangeItem())) {
                    orderItemMapper.updateItemSendStatus(orderId, s.getId());
                }
            });
        }
    }
}