package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoFxRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TaobaoFxOrderStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-07-15
 * create at : 2019-07-15 2:14 PM
 * 淘宝发货前退单转换服务
 * 返回-1 代表程序终止
 */
@Component
@Slf4j
public class IpTaobaoFxRefundSendBeforeService {

    @Autowired
    private IpBTaobaoFxRefundMapper refundMapper;

    @Autowired
    private OcBOrderItemMapper itemMapper;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OmsOrderLogService logService;

    @Autowired
    private MarkRefundService markRefundService;

    @Autowired
    private OcBOrderTheAuditService auditService;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    public ValueHolderV14 beforeShipmentTransfer(IpTaobaoFxRefundRelation relation, User user) {
        log.debug(this.getClass().getSimpleName() + "分销退发货前退单入参relation=" + relation);
        ValueHolderV14 holderV14 = new ValueHolderV14();
        /**
         * 判断【退单中间表】的退款状态与原单对应的商品的退款状态是否一致？
         * 若一致，更新【退单中间表】数据：“转换状态”更新为2.
         * 若不一致，对【退款中间表】不同状态做不同处理。
         */
        IpBTaobaoFxRefund refundOrder = relation.getTaobaoFxRefund();
        List<OcBOrderItem> itemList = relation.getOcBOrderItems();
        if (itemList == null || itemList.size() == 0) {
            holderV14.setCode(-1);
            holderV14.setMessage("全渠道订单明细为空！无法转换！");
            return holderV14;
        }

        if (refundOrder.getRefundStatus() == null) {
            holderV14.setCode(-1);
            holderV14.setMessage("淘宝退单退款状态为空，无法转换！");
            return holderV14;
        }

        if (refundOrder.getSubOrderId() == null) {
            holderV14.setCode(-1);
            holderV14.setMessage("SubOrderId为空，无法转换！");
            return holderV14;
        }

        try {
            //是否匹配到
            int count = 0;
            //是否匹配到对应商品
            for (OcBOrderItem item : itemList) {
                if (item.getOoid() != null && refundOrder.getSubOrderId().equals(Long.valueOf(item.getOoid()))) {
                    Integer refundStatus = Integer.valueOf(refundOrder.getRefundStatus().toString());
                    updateRefundStatus(relation, refundStatus, item, refundOrder, user);
                    count += 1;
                }
            }
            if (count == 0) {
                IpBTaobaoFxRefund temp = new IpBTaobaoFxRefund();
                temp.setId(refundOrder.getId());
                temp.setSysremark("分销退单未匹配到对应商品");
                updateIpBTaobaoFxRefund(temp);

                holderV14.setMessage("分销退单未匹配到对应商品");
                holderV14.setCode(-1);
                return holderV14;
            }
        } catch (Exception ex) {
            holderV14.setCode(-1);
            holderV14.setMessage("淘宝发货前退单转换服务失败，原因" + ex);
            return holderV14;
        }


        //无异常，成功
        holderV14.setMessage("成功");
        holderV14.setCode(0);
        return holderV14;
    }

    private void updateRefundStatus(IpTaobaoFxRefundRelation relation, Integer refundStatus, OcBOrderItem item, IpBTaobaoFxRefund refundOrder, User user) {
        //统一更新明细
        updateOrderItemByRefundStatus(refundStatus, item.getId(), relation.getOcOrderId());
        logService.addUserOrderLog(relation.getOcBOrder().getId(), relation.getOcBOrder().getBillNo(), OrderLogTypeEnum.STANDPALT_lOG_TYPE.getKey(), "更新明细", null, null, user);

        if (TaobaoFxOrderStatusEnum.APPLY_REFUND_TO_SELLER.toInteger() == refundStatus
                || TaobaoFxOrderStatusEnum.AGREE_WAIT_BUY_RECEIVER.toInteger() == refundStatus
                || TaobaoFxOrderStatusEnum.WAIT_SELLER_RECEIVER.toInteger() == refundStatus
                || TaobaoFxOrderStatusEnum.REFUND_TEN.toInteger() == refundStatus) {
            updateByWaitSend(relation, refundStatus, item, refundOrder, user);
        } else if (TaobaoFxOrderStatusEnum.REFUND_SUCCESS.toInteger() == refundStatus
                || TaobaoFxOrderStatusEnum.REFUND_TWELVE.toInteger() == refundStatus) {
            updateBySuccess(relation, refundStatus, item, refundOrder, user);
        } else if (TaobaoFxOrderStatusEnum.REFUND_CLOSE.toInteger() == refundStatus || TaobaoFxOrderStatusEnum.SELLER_REFUSE.toInteger() == refundStatus) {
            updateByClose(relation, refundOrder, user);
        }
    }

    private void updateByWaitSend(IpTaobaoFxRefundRelation relation, Integer refundStatus, OcBOrderItem item, IpBTaobaoFxRefund refundOrder, User user) {
        /**
         * 更新对应全渠道订单主表数据
         * “是否已经拦截”更新为1，记录订单操作日志。
         * “退款中”=1，记录订单操作日志。
         */
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(relation.getOcOrderId());
        updateOrder.setIsInterecept(1); // Hold单统一调用 HOLD单方法
        updateOrder.setIsInreturning(1);
        updateOcOrder(updateOrder);

        logService.addUserOrderLog(relation.getOcBOrder().getId(), relation.getOcBOrder().getBillNo(), OrderLogTypeEnum.STANDPALT_lOG_TYPE.getKey(), "更新主表", null, null, user);

        /**
         * 判断订单状态？
         * 若订单状态是：【1,待审核】【2,缺货】 【9,预售】则更新“转换状态”为已转换（2），”系统备注”：买家申请退款转换完成。
         * 若订单状态是【3，已审核】【4,配货中】，调用【反审核服务】；
         * 若反审核成功，则更新“转换状态”为已转换（2），”系统备注”：买家申请退款转换完成，标记为已转换；
         * 若反审核失败，则更新“转换状态”为未转换（0），”系统备注”：反审核失败，等待重新转换。
         * 若订单状态是：【21,传wms中】或【50，待分配】，则更新“转换状态”为未转换（0），”系统备注”：订单状态为转wms中或待分配，等待订单状态变化后再转换。
         */

        Integer orderStatus = relation.getOcBOrder().getOrderStatus();

        IpBTaobaoFxRefund updateRefund = new IpBTaobaoFxRefund();
        updateRefund.setId(refundOrder.getId());
        if (orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal())
                || orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal())
                || orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_ADVANCE_SALE.getVal())) {
            updateRefund.setIstrans(2);
            updateRefund.setSysremark("买家申请退款转换完成");
            updateIpBTaobaoFxRefund(updateRefund);
        } else if (orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal())
                || orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal())) {
            theAudit(user, relation.getOcOrderId(), refundOrder.getId(), item.getId(), refundStatus);
        } else if (orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_PENDING_WMS.getVal())
                || orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_PENDING_ALLOCATED.getVal())) {
            updateRefund.setIstrans(0);
            updateRefund.setSysremark("订单状态为转wms中或待分配，等待订单状态变化后再转换");
            updateIpBTaobaoFxRefund(updateRefund);
        }
    }

    private void updateBySuccess(IpTaobaoFxRefundRelation relation, Integer refundStatus, OcBOrderItem item, IpBTaobaoFxRefund refundOrder, User user) {
        /**
         * 更新对应全渠道订单主表数据
         * “是否已经拦截”更新为1，记录订单操作日志。
         * “退款中”=1，记录订单操作日志。
         */
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(relation.getOcOrderId());
        updateOrder.setIsInterecept(1); // Hold单统一调用 HOLD单方法
        updateOrder.setIsInreturning(1);
        updateOcOrder(updateOrder);

        logService.addUserOrderLog(relation.getOcBOrder().getId(), relation.getOcBOrder().getBillNo(), OrderLogTypeEnum.STANDPALT_lOG_TYPE.getKey(), "更新主表", null, null, user);

        Integer orderStatus = relation.getOcBOrder().getOrderStatus();
        IpBTaobaoFxRefund updateRefund = new IpBTaobaoFxRefund();
        updateRefund.setId(refundOrder.getId());

        /**
         * 判断订单状态？
         * 若订单状态是：【1,待审核】【2,缺货】【9,预售】 【50,待分配】则调用【标记退款完成服务】。
         */
        if (orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal())
                || orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal())
                || orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_ADVANCE_SALE.getVal())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("IDS", item.getId());
            makeRefundSuccess(user, jsonObject);
        } else if (orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal())
                || orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal())) {
            theAudit(user, relation.getOcOrderId(), refundOrder.getId(), item.getId(), refundStatus);
        } else if (orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_PENDING_WMS.getVal())
                || orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_PENDING_ALLOCATED.getVal())) {
            updateRefund.setIstrans(0);
            updateRefund.setSysremark("订单状态为转wms中或待分配，等待订单状态变化后再转换");
            updateIpBTaobaoFxRefund(updateRefund);
            //程序终止
            return;
        }

        /**
         * 判断订单明细中“退款状态”是否存在退款中（商品的退款状态为1,2,3）的明细？
         * 若存在，则更新“转换状态”为已转换（2），”系统备注”：退款成功转换成功。
         * 若不存在，则更新订单“是否拦截”=0，“是否退款中”=0，更新“转换状态”为已转换（2），”系统备注”：退款成功转换成功。记录操作日志
         */

        //是否存在1，2，3的数据

        compareWithItem(refundOrder.getId(), relation.getOcOrderId(), "退款成功转换成功", relation.getOcBOrder().getBillNo(), user);
    }

    /**
     * @param user         当前用户
     * @param orderId      订单Id
     * @param itemId       明细id
     * @param refundId     fxRefundId
     * @param refundStatus 中间表退款状态
     */
    private void theAudit(User user, Long orderId, Long refundId, Long itemId, Integer refundStatus) {
        IpBTaobaoFxRefund updateRefund = new IpBTaobaoFxRefund();
        updateRefund.setId(refundId);

        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            log.debug(this.getClass().getSimpleName() + "订单反审核，入参=" + orderId);
            boolean isSuccess = auditService.updateOrderInfo(user, holderV14, orderId, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            log.debug(this.getClass().getSimpleName() + "订单反审核，出参isSuccess=" + isSuccess);
            log.debug(this.getClass().getSimpleName() + "订单反审核，出参holderV14=" + holderV14);
            if (isSuccess) {
                //退款成功
                if (refundStatus == TaobaoFxOrderStatusEnum.REFUND_SUCCESS.toInteger()
                        || TaobaoFxOrderStatusEnum.REFUND_TWELVE.toInteger() == refundStatus) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("IDS", itemId);
                    makeRefundSuccess(user, jsonObject);
                } else {
                    updateRefund.setIstrans(2);
                    updateRefund.setSysremark("买家申请退款转换完成，标记为已转换");
                    updateIpBTaobaoFxRefund(updateRefund);
                }
            } else {
                updateRefund.setIstrans(0);
                updateRefund.setSysremark("反审核失败，等待重新转换");
                updateIpBTaobaoFxRefund(updateRefund);
                throw new NDSException("反审核失败，等待重新转换");
            }
        } catch (Exception ex) {
            log.debug(this.getClass().getSimpleName() + "订单反审核，异常=" + ex);
            updateRefund.setIstrans(0);
            updateRefund.setSysremark("反审核失败，等待重新转换");
            updateIpBTaobaoFxRefund(updateRefund);
            throw new NDSException("反审核失败，等待重新转换");
        }
    }


    private void updateOrderItemByRefundStatus(Integer refundStatus, Long id, Long ocOrderId) {
        /**
         * 订单明细中的“退款状态”更新为对应状态，记录订单操作日志。
         * 判断中间表的退款状态
         * 1.若是【1,买家已经申请退款，等待卖家同意】或【2,卖家已经同意退款，等待买家退货】【3,买家已经退货，等待卖家确认收货】【10】【12】时
         * 更新对应全渠道订单明细数据
         * 渠道订单明细中的“退款状态”更新为对应的订单明细退款状态，记录订单操作日志。
         * 2. 若中间表退款状态时【5，退款成功】
         * 更新对应全渠道订单明细数据
         * 订单明细中的“退款状态”更新退款中1，记录订单操作日志。
         * 3. 若是退款关闭（CLOSED(退款关闭)）或商家拒绝退款
         * 更新对应的订单明细：“退款状态”更新为未退款（值为0）
         */

        OcBOrderItem itemTemp = new OcBOrderItem();
        itemTemp.setId(id);
        if (TaobaoFxOrderStatusEnum.REFUND_TEN.toInteger() == refundStatus) {
            itemTemp.setRefundStatus(OcOrderRefundStatusEnum.WAIT_SELLER_CONFIRM_GOODS.getVal());
        } else if (TaobaoFxOrderStatusEnum.SELLER_REFUSE.toInteger() == refundStatus || TaobaoFxOrderStatusEnum.REFUND_CLOSE.toInteger() == refundStatus) {
            itemTemp.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
        } else if (TaobaoFxOrderStatusEnum.REFUND_SUCCESS.toInteger() == refundStatus
                || TaobaoFxOrderStatusEnum.REFUND_TWELVE.toInteger() == refundStatus) {
            itemTemp.setRefundStatus(OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal());
        } else {
            itemTemp.setRefundStatus(refundStatus);
        }
        updateOcOrderItem(itemTemp, ocOrderId);
    }

    /**
     * 若是退款关闭（CLOSED(退款关闭)）或商家拒绝退款
     * 更新对应的订单明细：“退款状态”更新为未退款（值为0），
     * 判断订单明细中“退款状态”是否存在值为1 的数据
     * 若是，则更新【淘宝退单中间表】数据：“转换状态”=2，”系统备注”：退款关闭，转换成功
     * 若否，则更新全渠道订单“是否已经拦截”=0，全渠道订单表“是否退款中”=0，则更新【淘宝退单中间表】数据：“转换状态”=2，”系统备注”：退款关闭，转换成功，调用订单日志服务
     * “订单编号”：当前订单编号
     * “用户名称”：当前操作人
     * “日志类型”：取消拦截
     * 日志内容”：订单退款关闭，取消拦截
     * “日志参数”：空
     * “IP地址”：当前操作电脑的IP地址
     * “错误信息”：空
     *
     * @param relation
     * @param refundOrder
     * @param user
     */
    private void updateByClose(IpTaobaoFxRefundRelation relation, IpBTaobaoFxRefund refundOrder, User user) {
        compareWithItem(refundOrder.getId(), relation.getOcOrderId(), "退款关闭，转换成功", relation.getOcBOrder().getBillNo(), user);
    }

    private void compareWithItem(Long refundId, Long orderId, String remark, String billNo, User user) {
        IpBTaobaoFxRefund refundTemp = new IpBTaobaoFxRefund();
        refundTemp.setId(refundId);

        OcBOrder orderTemp = new OcBOrder();
        orderTemp.setId(orderId);

        //是否存在1，2，3的数据
        boolean isExist = false;
        QueryWrapper<OcBOrderItem> wrapper = new QueryWrapper<>();
        wrapper.eq("oc_b_order_id", orderId);
        List<OcBOrderItem> orderItems = itemMapper.selectList(wrapper);

        for (OcBOrderItem bean : orderItems) {
            if (1 == bean.getRefundStatus() || 2 == bean.getRefundStatus() || 3 == bean.getRefundStatus()) {
                isExist = true;
                break;
            }
        }

        //遍历后依然不存在
        if (isExist) {
            refundTemp.setIstrans(2);
            refundTemp.setSysremark(remark);
            updateIpBTaobaoFxRefund(refundTemp);
        } else {
            orderTemp.setIsInterecept(0); // Hold单统一调用 HOLD单方法
            orderTemp.setIsInreturning(0);
            updateOcOrder(orderTemp);
            logService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "订单退款关闭，取消拦截", null, null, user);

            refundTemp.setIstrans(2);
            refundTemp.setSysremark(remark);
            updateIpBTaobaoFxRefund(refundTemp);
        }
    }

    private void makeRefundSuccess(User user, JSONObject jsonObject) {
        try {
            ValueHolderV14 holderV14 = markRefundService.markRefund(jsonObject, user);
            if (!holderV14.isOK()) {
                log.debug(this.getClass().getSimpleName() + "标记退款完成失败" + holderV14.getMessage());
                throw new NDSException("标记退款完成失败" + holderV14.getMessage());
            }
        } catch (Exception ex) {
            log.debug(this.getClass().getSimpleName() + "标记退款完成异常" + ex);
            throw new NDSException("标记退款完成失败" + ex);
        }
    }

    private void updateOcOrder(OcBOrder order) {
        // Hold单统一调用 HOLD单方法
        ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(order.getId());
        updateOrder.setIsInreturning(order.getIsInreturning());
        orderMapper.updateById(updateOrder);
//        try {
//            SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, orderMapper.selectById(order.getId()), order.getId());
//        } catch (Exception ex) {
//            log.debug("日志服务：分销退单，发货前转单推送OcBOrder ES失败" + ex);
//        }
    }

    private void updateOcOrderItem(OcBOrderItem ocBOrderItem, Long ocOrderId) {
        itemMapper.updateById(ocBOrderItem);
//        try {
//            SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, itemMapper.selectById(ocBOrderItem.getId()), ocBOrderItem.getId(), ocOrderId.toString());
//        } catch (Exception ex) {
//            log.debug("日志服务：分销退单，发货前转单推送OcBOrderItem ES失败" + ex);
//        }
    }

    private void updateIpBTaobaoFxRefund(IpBTaobaoFxRefund refund) {
        refundMapper.updateById(refund);
//        try {
//            SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.IP_B_TAOBAO_FX_REFUND_INDEX_NAME, OcElasticSearchIndexResources.IP_B_TAOBAO_FX_REFUND_TYPE_NAME, refundMapper.selectById(refund.getId()), refund.getId());
//        } catch (Exception ex) {
//            log.debug("日志服务：分销退单，发货前转单推送IpBTaobaoFxRefund ES失败" + ex);
//        }
    }


}
