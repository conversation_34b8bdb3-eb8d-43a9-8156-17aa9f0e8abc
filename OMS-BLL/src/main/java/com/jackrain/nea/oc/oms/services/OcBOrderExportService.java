package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.relation.*;
import com.jackrain.nea.oc.oms.model.result.OcBOrderExportResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: 李龙飞
 * @create: 2019-05-14 13:32
 **/
@Component
@Slf4j
public class OcBOrderExportService {
    public static final String ID = "ID";//id
    public static final String OC_B_ORDER_ID = "OC_B_ORDER_ID";//明细中主表id

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    public ValueHolderV14 exportList(String jsonStr, User loginUser) {
        ValueHolderV14 resultHolderV14 = new ValueHolderV14(ResultCode.SUCCESS, "订单管理导出成功！");
        log.debug(LogUtil.format("[llf]订单管理导出查询入参") + jsonStr);
        JSONObject jo = JSONObject.parseObject(jsonStr);
        List<OcBOrder> ocBOrderList = Lists.newArrayList();
        List<OcBOrderItem> ocBOrderItemList = Lists.newArrayList();
        OcBOrderExportResult data = new OcBOrderExportResult();
        List<Long> idList = Lists.newArrayList();
        //判断是否为 勾选导出
        if (jo.containsKey("idList")) {
            idList = (List<Long>) jo.get("idList");
        } else {
            //请求条件查询
            idList = queryList(jsonStr);
        }
        if (CollectionUtils.isEmpty(idList)) {
            resultHolderV14.setCode(ResultCode.FAIL);
            resultHolderV14.setMessage("没有查询到记录");
            return resultHolderV14;
        }
        idList = idList.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());//降序
        long begin = System.currentTimeMillis();
        log.debug(LogUtil.format("[llf]订单管理导出开始查询数据库>>>>>>>>>>>>>>>>"));
        //按2000条处理
        int batchSize = 2000;
        while (idList.size() >= batchSize) {
            List<Long> batchSourceIdList = idList.subList(0, batchSize);
            //根据主表id查询主表
            QueryWrapper<OcBOrder> orderWrapper = new QueryWrapper<OcBOrder>();
            orderWrapper.in(ID, batchSourceIdList);
            List<OcBOrder> orderList = ocBOrderMapper.selectList(orderWrapper);
            //根据主表id查询明细表
            QueryWrapper<OcBOrderItem> itemWrapper = new QueryWrapper<OcBOrderItem>();
            itemWrapper.in(OC_B_ORDER_ID, batchSourceIdList);
            List<OcBOrderItem> itemList = ocBOrderItemMapper.selectList(itemWrapper);

            ocBOrderList.addAll(orderList);
            ocBOrderItemList.addAll(itemList);
            idList.removeAll(batchSourceIdList);
            try {
                Thread.sleep(1);
            } catch (Exception e) {

            }
        }
        if (CollectionUtils.isNotEmpty(idList)) {
            //根据主表id查询主表
            QueryWrapper<OcBOrder> orderWrapper = new QueryWrapper<OcBOrder>();
            orderWrapper.in(ID, idList);
            List<OcBOrder> orderList = ocBOrderMapper.selectList(orderWrapper);
            //根据主表id查询明细表
            QueryWrapper<OcBOrderItem> itemWrapper = new QueryWrapper<OcBOrderItem>();
            itemWrapper.in(OC_B_ORDER_ID, idList);
            List<OcBOrderItem> itemList = ocBOrderItemMapper.selectList(itemWrapper);

            ocBOrderList.addAll(orderList);
            ocBOrderItemList.addAll(itemList);
        }
        long end = System.currentTimeMillis();
        log.debug(LogUtil.format("[llf]订单管理导出查询总耗时--------" + (end - begin) + "ms"));
        log.debug(LogUtil.format("[llf]订单管理导出查询主表数量" + ocBOrderList.size() + ",订单管理导出查询明细数量" + ocBOrderItemList.size()));

        long s = System.currentTimeMillis();
        log.debug(LogUtil.format("[llf]订单管理导出开始字段置换>>>>>>>>>>>>>>>>>>>"));
        //置换字段
        List<OcBOrderExtend> ocBOrderExtends = OcBorderListEnums.changeOrderChildClassList(ocBOrderList);
        for (OcBOrderExtend ocBOrderExtend : ocBOrderExtends) {
            ocBOrderExtend.setCptAddress(ocBOrderExtend.getCpCRegionProvinceEname() + ocBOrderExtend.getCpCRegionCityEname() + ocBOrderExtend.getCpCRegionAreaEname() + ocBOrderExtend.getReceiverAddress());
        }
        List<OcBOrderItemExtend> ocBOrderItemExtends = OcBorderListEnums.changeItemChildClassList(ocBOrderItemList);
        long s1 = System.currentTimeMillis();
        log.debug(LogUtil.format("[llf]订单管理导出字段置换耗时--------" + (s1 - s) + "ms"));
        //统计缺货数量
        Map<Long, List<OcBOrderItemExtend>> ocBOrderItemMap = ocBOrderItemExtends.stream().collect(Collectors.groupingBy(a -> a.getOcBOrderId(), Collectors.toList()));
        for (OcBOrderExtend ocBOrderExtend : ocBOrderExtends) {
            List<OcBOrderItemExtend> ocBOrderItemExtendList = ocBOrderItemMap.get(ocBOrderExtend.getId());
            Integer sum = 0;
            if (CollectionUtils.isNotEmpty(ocBOrderItemExtendList)) {
                for (OcBOrderItemExtend ocBOrderItemExtend : ocBOrderItemExtendList) {
                    if (ocBOrderItemExtend.getQtyLost() == null) {
                        ocBOrderItemExtend.setQtyLost(BigDecimal.ZERO);
                    }
                    sum += ocBOrderItemExtend.getQtyLost().intValue();
                }
            }
            ocBOrderExtend.setTotQtyLost(BigDecimal.valueOf(sum.longValue()));
        }
        //拼接返回对象
        OcBOrderExportResult ocBOrderExportResult = new OcBOrderExportResult();
        ocBOrderExportResult.setOcBOrderList(ocBOrderExtends);
        ocBOrderExportResult.setOcBOrderItemList(ocBOrderItemExtends);
        resultHolderV14.setData(ocBOrderExportResult);

        return resultHolderV14;
    }

    public List<Long> queryList(String jsonStr) {
        List<Long> idList = Lists.newArrayList();
        /**
         * ES search部分
         */
        JSONObject whereKeyJo = new JSONObject();
        JSONObject filterKeyJo = new JSONObject();
        JSONObject childsKeys = new JSONObject();
        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", false);
        orderJo.put("name", "ORDER_DATE");
        orderJAry.add(orderJo);

        QueryOrderRelation queryOrderRelation = handlerSqlConditionQuery(jsonStr, whereKeyJo, filterKeyJo,
                childsKeys);
        int startIndex = getPageStartIndex(queryOrderRelation);
        recordLog("OcBOrderExportService.queryList.whereKey=>> " + whereKeyJo.toString()
                + "filterKey=>> " + filterKeyJo.toString());
        JSONObject esOrderJo = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, whereKeyJo, filterKeyJo, orderJAry,
                childsKeys, queryOrderRelation.getPage().getPageSize(), startIndex, new String[]{"ID"});
        JSONArray esJay = new JSONArray();
        idList = splitOrderIds(esOrderJo, esJay, queryOrderRelation);
        return idList;
    }

    /**
     * es提取主表id,并计算起始下标
     *
     * @param esJo               jsonObject
     * @param orderIdArray       orderIdArray
     * @param queryOrderRelation queryOrderRelation
     * @return list
     */
    private List<Long> splitOrderIds(JSONObject esJo, JSONArray orderIdArray, QueryOrderRelation queryOrderRelation) {
        List<Long> list = new ArrayList<>();
        if (esJo == null) {
            return list;
        }

        Long totalCount = esJo.getLong("total");
        Long totalPage = 0L;
        if (totalCount > OcBOrderConst.ORDER_STATUS_ALL) {
            long l = totalCount % (queryOrderRelation.getPage().getPageSize());
            if (l == OcBOrderConst.ORDER_STATUS_ALL) {
                totalPage = totalCount / (queryOrderRelation.getPage().getPageSize());
            } else {
                totalPage = (totalCount / (queryOrderRelation.getPage().getPageSize())) + 1;
            }
        }
        queryOrderRelation.getPage().setTotalNum(totalPage.intValue());
        queryOrderRelation.getPage().setTotalSize(totalCount);

        JSONArray ary = esJo.getJSONArray("data");
        if (ary == null) {
            return list;
        }
        for (int i = 0; i < ary.size(); i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            Long id = o.getLong("ID");
            if (id == null) {
                continue;
            }
            list.add(id);
            orderIdArray.add(id);
        }
        return list;
    }

    /**
     * debug 级 日志记录
     *
     * @param msg 信息
     */
    private void recordLog(String msg) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(msg));
        }
    }

    /**
     * ES 分页起始下标
     *
     * @param queryOrderRelation queryOrderRelation
     * @return int
     */
    private int getPageStartIndex(QueryOrderRelation queryOrderRelation) {
        Integer pageNum = queryOrderRelation.getPage().getPageNum();
        int startIndex = OcBOrderConst.ORDER_STATUS_ALL;
        if (pageNum != null && pageNum > OcBOrderConst.ORDER_STATUS_ALL) {
            startIndex = (pageNum - 1) * OcBOrderConst.PAGE_SIZE;
        }
        return startIndex;
    }

    /**
     * 分页处理
     *
     * @param o   分页信息
     * @param p   分页entity
     * @param qol 分页relation pojo
     */
    private void splitPageHandler(JSONObject o, Page p, QueryOrderRelation qol) {
        if (o != null) {
            Integer size = o.getInteger("pageSize");
            Integer num = o.getInteger("pageNum");
            if (num != null && num > 0) {
                p.setPageNum(num);
            }
            if (size != null && size > 0) {
                p.setPageSize(size);
            }
            qol.setPage(p);
        }
    }

    /**
     * 查询条件
     *
     * @param jsonStr     查询条件
     * @param whereKeyJo  es查询条件
     * @param filterKeyJo 区间查询条件
     * @param childKeys   查询条件
     * @return sql string
     */
    private QueryOrderRelation handlerSqlConditionQuery(String jsonStr, JSONObject whereKeyJo, JSONObject filterKeyJo,
                                                        JSONObject childKeys) {
        QueryOrderRelation queryOrderRelation = new QueryOrderRelation();
        Page page = new Page();
        page.setPageNum(OcBOrderConst.PAGE_NUM);
        page.setPageSize(OcBOrderConst.PAGE_SIZE);
        queryOrderRelation.setPage(page);

        if (jsonStr == null || jsonStr.trim().length() == 0) {
            return queryOrderRelation;
        }
        JSONObject jo = JSONObject.parseObject(jsonStr);
        if (jo != null) {
            JSONObject pageJo = jo.getJSONObject("page");
            JSONArray labelJas = jo.getJSONArray("label");
            JSONArray queryInfoJo = jo.getJSONArray("queryInfo");
            JSONObject statusJo = jo.getJSONObject("status");
            JSONArray highSearch = jo.getJSONArray("highSearch");
            splitPageHandler(pageJo, page, queryOrderRelation);

            // 2.0 标签部分
            sqlIntelliSearchTag(labelJas, whereKeyJo);
            // 3.0 多下拉智能查询部分
            if (queryInfoJo != null) {
                int infoLen = queryInfoJo.size();
                for (int i = 0; i < infoLen; i++) {
                    JSONObject tmpJo = queryInfoJo.getJSONObject(i);
                    String type = tmpJo.getString("type");
                    String queryName = tmpJo.getString("queryName");
                    if (type != null && "Select".equals(type)) {
                        JSONArray selectBox = tmpJo.getJSONArray("list");
                        sqlIntelliSearchCtrHandler(selectBox, queryName, whereKeyJo);
                    } else if (type != null && "date".equals(type)) {
                        String value = tmpJo.getString("value");
                        sqlHighSearchDateCtrHandler(value, queryName, filterKeyJo);
                    } else if (type != null && "Input".equals(type)) {
                        String value = tmpJo.getString("value");
                        sqlHighSearchTextCtrHandler(value, queryName, whereKeyJo, filterKeyJo, childKeys);
                    }
                }
            }

            // 4.0 高级搜索部分
            if (highSearch != null) {
                for (int i = 0; i < highSearch.size(); i++) {
                    JSONObject tmpJo = highSearch.getJSONObject(i);
                    if (tmpJo != null) {
                        String type = tmpJo.getString("type");
                        String queryName = tmpJo.getString("queryName");
                        if ("Select".equalsIgnoreCase(type)) {
                            String sltChk = tmpJo.getString("value");
                            highSearchSelectCtrlHandler(sltChk, queryName, whereKeyJo);
                        } else if ("date".equalsIgnoreCase(type)) {
                            String dateStr = tmpJo.getString("value");
                            sqlHighSearchDateCtrHandler(dateStr, queryName, filterKeyJo);
                        } else if ("Input".equalsIgnoreCase(type)) {
                            String txt = tmpJo.getString("value");
                            sqlHighSearchTextCtrHandler(txt, queryName, whereKeyJo, filterKeyJo, childKeys);
                        }
                    }
                }
            }
            // ES:合并状态- 状态按钮部分
            buttonStatuJoinList(statusJo, whereKeyJo);
            allStatuHandler(whereKeyJo);
        }
        return queryOrderRelation;
    }

    /**
     * 全部状态: 判断
     *
     * @param es es
     */
    private void allStatuHandler(JSONObject es) {
        if (es != null) {
            JSONArray arys = es.getJSONArray("ORDER_STATUS");
            if (arys != null) {
                int len = arys.size();
                for (int i = 0; i < len; i++) {
                    if (arys.getInteger(i) != null && OcBOrderConst.ORDER_STATUS_ALL.equals(arys.getInteger(i))) {
                        es.put("ORDER_STATUS", OcOrderCheckBoxEnum.joinAllStatusVal());
                        break;
                    }
                }
            }
        }
    }

    /**
     * 高级搜索: 时间控件
     *
     * @param dateStr   时间字符串
     * @param queryName 搜索字段
     * @param es        es
     */
    private void sqlHighSearchDateCtrHandler(String dateStr, String queryName, JSONObject es) {
        if (dateStr == null || dateStr.trim().length() < 1) {
            return;
        }
        String esDateStr = dealStringConvertToTime(dateStr);
        if (esDateStr != null && esDateStr.length() > 0) {
            es.put(queryName, esDateStr);
        }
    }

    /**
     * 字符串转换成日期,并拼接
     *
     * @param str 日期字符串
     * @return 日期时间戳字符串
     */
    private String dealStringConvertToTime(String str) {
        String dtStr = null;
        String[] sAry = str.split("~");
        // 严格格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (sAry[0] == null || sAry[1] == null || sAry[0].length() < OcBOrderConst.DATE_MIN_LENGTH
                || sAry[1].length() < OcBOrderConst.DATE_MIN_LENGTH) {
            return null;
        }
        try {
            Long startDt = sdf.parse(sAry[0]).getTime();
            Long endDt = sdf.parse(sAry[1]).getTime();
            dtStr = startDt + "~" + endDt;
        } catch (ParseException e) {
            log.error(LogUtil.format("OcBOrderExportService.dealStringConvertToTIme Error：{} "),
                    Throwables.getStackTraceAsString(e));
        }
        return dtStr;
    }

    /**
     * ES:合并按钮状态
     *
     * @param buttonWherekey buttonWherekey
     * @param whereKeyJo     whereKeyJo
     */
    private void buttonStatuJoinList(JSONObject buttonWherekey, JSONObject whereKeyJo) {
        if (buttonWherekey == null) {
            return;
        }
        String statusStr = buttonWherekey.getString("value");
        JSONArray tmpStatusList = whereKeyJo.getJSONArray("ORDER_STATUS");
        JSONArray reSetJa = new JSONArray();
        statusLbl:
        if (null != tmpStatusList && StringUtils.isNotBlank(statusStr)) {
            // 下拉没值, tab值 加入
            if (tmpStatusList.size() < OcBOrderConst.IS_STATUS_IY) {
                splitComposeStatus(statusStr, reSetJa);
                whereKeyJo.put("ORDER_STATUS", reSetJa);
                break statusLbl;
            }
            // tab 包含全部
            if (OcBOrderConst.STATUS_TAB_ALL.equals(statusStr)) {
                if (tmpStatusList.contains(OcBOrderConst.STATUS_TAB_ALL)) {
                    reSetJa.add(OcBOrderConst.STATUS_TAB_ALL);
                    whereKeyJo.put("ORDER_STATUS", reSetJa);
                } else {
                    whereKeyJo.put("ORDER_STATUS", tmpStatusList);
                }
            } else {
                // 组合性tab
                if (statusStr.contains(OcBOrderConst.ORDER_COMMA)) {
                    splitComposeStatus(statusStr, reSetJa);
                    dealComposeStatus(tmpStatusList, reSetJa, whereKeyJo);
                } else if (tmpStatusList.contains(statusStr)
                        || (tmpStatusList.contains(OcBOrderConst.STATUS_TAB_ALL))) {
                    reSetJa.add(statusStr);
                    whereKeyJo.put("ORDER_STATUS", reSetJa);
                } else {
                    reSetJa.add(OcBOrderConst.ORDER_STATUS_NONE);
                    whereKeyJo.put("ORDER_STATUS", reSetJa);
                }
            }
        } else if (tmpStatusList == null && StringUtils.isNotBlank(statusStr)) {
            // 下拉为null, tab有值
            splitComposeStatus(statusStr, reSetJa);
            whereKeyJo.put("ORDER_STATUS", reSetJa);
        }
    }

    /**
     * 处理组合性tab状态
     *
     * @param tmpStatusList 条件状态
     * @param reSetJa       tab状态
     * @param whereKeyJo    ES
     */
    private void dealComposeStatus(JSONArray tmpStatusList, JSONArray reSetJa, JSONObject whereKeyJo) {
        if (tmpStatusList.contains(OcBOrderConst.IS_STATUS_SN)) {
            whereKeyJo.put("ORDER_STATUS", reSetJa);
        } else {
            JSONArray ary = new JSONArray();
            for (int i = 0; i < reSetJa.size(); i++) {
                String stu = reSetJa.getString(i);
                if (tmpStatusList.contains(stu)) {
                    ary.add(stu);
                }
            }
            if (ary.size() > OcBOrderConst.IS_STATUS_IN) {
                whereKeyJo.put("ORDER_STATUS", ary);
            } else {
                ary.add(OcBOrderConst.ORDER_STATUS_NONE);
                whereKeyJo.put("ORDER_STATUS", ary);
            }
        }
    }

    /**
     * 拆分组合性tab状态
     *
     * @param stu 状态
     * @param ary jsonArray
     */
    private void splitComposeStatus(String stu, JSONArray ary) {
        if (stu.contains(OcBOrderConst.ORDER_COMMA)) {
            String[] stuAry = stu.split(OcBOrderConst.ORDER_COMMA);
            for (String s : stuAry) {
                if (StringUtils.isNotBlank(s)) {
                    ary.add(s);
                }
            }
        } else {
            ary.add(stu);
        }
    }

    /**
     * 高级搜索: 下拉多选
     *
     * @param valString 下拉值集合
     * @param queryName 搜索字段
     * @param es        es
     */
    private void highSearchSelectCtrlHandler(String valString, String queryName, JSONObject es) {
        if (valString == null || valString.trim().length() == 0) {
            return;
        }
        String[] valArys = valString.split(",");
        int len = valArys.length;
        JSONArray selectAry = new JSONArray();
        for (int i = 0; i < len; i++) {
            String v = valArys[i];
            if (v == null || v.trim().length() < 1) {
                continue;
            }
            selectAry.add(v);
        }
        es.put(queryName, selectAry);
    }

    /**
     * 高级搜索: 文本控件
     *
     * @param v         文本值
     * @param n         搜索字段
     * @param fKey      搜索字段
     * @param childKeys 搜索字段
     * @param es        es
     * @return t/f
     */
    private boolean sqlHighSearchTextCtrHandler(String v, String n, JSONObject es, JSONObject fKey,
                                                JSONObject childKeys) {
        if (StringUtils.isBlank(v) || "~".equals(v.trim())) {
            return false;
        }
        if ("ID".equals(n)) {
            es.put(n, v);
            return true;
        }
        if (OcBOrderConst.OCB_ORDER_QTY_ALL.equals(n) || OcBOrderConst.OCB_ORDER_ORDER_AMT.equals(n)) {
            boolean flag = Pattern.matches(OcBOrderConst.OCB_ORDER_NUMBER_REGES, v);
            if (flag) {
                fKey.put(n, splitAndJoinHighText(v));
                return true;
            }
            return false;
        }
        if (OcBOrderConst.OCB_ORDER_ITEM_PSC_SKUECODE.equals(n)) {
            childKeys.put(n, v + "*");
            return true;
        }
        es.put(n, v + "*");
        return true;
    }

    /**
     * 高级搜索: 文本控件_区间查询处理
     *
     * @param v 区间字符串
     * @return 后拼接
     */
    private String splitAndJoinHighText(String v) {
        String[] ary = v.split("~");
        if (ary.length > 1) {
            if (StringUtils.isNotBlank(ary[0]) && StringUtils.isNotBlank(ary[1])) {
                BigDecimal v0 = new BigDecimal(ary[0]);
                BigDecimal v1 = new BigDecimal(ary[1]);
                if (v0.compareTo(v1) > 0) {
                    return ary[1] + "~" + ary[0];
                }
            }
        }
        return v;
    }

    /**
     * 智能搜索: 标签
     *
     * @param oA tagArrays标签集合
     * @param es es
     */
    private void sqlIntelliSearchTag(JSONArray oA, JSONObject es) {
        if (oA != null) {
            int n = oA.size();
            for (int i = 0; i < n; i++) {
                JSONObject o = oA.getJSONObject(i);
                if (o != null) {
                    es.put(o.getString("key"), o.getString("val"));
                }
            }
        }
    }

    /**
     * 智能搜索: 下拉多选
     * 1. 当前: 状态,
     *
     * @param selectBoxs 下拉多选值集合
     * @param queryName  下拉,查询字段
     * @param es         es
     */
    private void sqlIntelliSearchCtrHandler(JSONArray selectBoxs, String queryName, JSONObject es) {
        if (selectBoxs == null || selectBoxs.size() < 1) {
            return;
        }
        int n = selectBoxs.size();
        JSONArray statuAry = new JSONArray();
        for (int i = 0; i < n; i++) {
            JSONObject o = selectBoxs.getJSONObject(i);
            if (o != null) {
                if ("ORDER_STATUS".equalsIgnoreCase(queryName)) {
                    statuAry.add(o.getString("value"));
                }
            }
        }
        es.put("ORDER_STATUS", statuAry);
    }

    public ValueHolderV14 queryList(OcBOrderExtend ocBOrderExtend) {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "成功！");

        //查询符合的主表
        QueryWrapper<OcBOrder> queryOcBOrder = ocBOrderExtend.createQueryWrapper();
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectList(queryOcBOrder);
        List<Long> ocBOrderIdList = ocBOrderList.parallelStream().map(OcBOrder::getId).collect(Collectors.toList());
        //查询符合的明细表
        OcBOrderItemExtend ocBOrderItemExtend = new OcBOrderItemExtend();
        ocBOrderItemExtend.setOcBOrderList(ocBOrderIdList);
        QueryWrapper<OcBOrderItem> queryOcBOrderItem = ocBOrderItemExtend.createQueryWrapper();
        List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectList(queryOcBOrderItem);

        //置换字段
        List<OcBOrderExtend> ocBOrderExtends = OcBorderListEnums.changeOrderChildClassList(ocBOrderList);
        List<OcBOrderItemExtend> ocBOrderItemExtends = OcBorderListEnums.changeItemChildClassList(ocBOrderItemList);
        //拼接返回对象
        OcBOrderExportResult ocBOrderExportResult = new OcBOrderExportResult();
        ocBOrderExportResult.setOcBOrderList(ocBOrderExtends);
        ocBOrderExportResult.setOcBOrderItemList(ocBOrderItemExtends);
        holderV14.setData(ocBOrderExportResult);

        return holderV14;
    }

}
