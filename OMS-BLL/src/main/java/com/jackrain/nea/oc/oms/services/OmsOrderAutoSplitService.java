package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.oc.model.SgBPhyInStorageItemExt;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.SgRpcService;

import com.jackrain.nea.st.model.result.SyncStockStrategyQueryResult;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCSyncStockStrategyDO;
import com.jackrain.nea.st.service.OmsQueryWareHouseService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>订单自动拆分服务</>
 * <p>
 * * @author: 胡林洋
 * * @since: 2019/5/13
 * * create at : 2019/5/13 11:30
 */
@Component
@Slf4j
public class OmsOrderAutoSplitService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsQueryWareHouseService omsQueryWareHouseService;

    @Autowired
    private OmsSyncStockStrategyService omsSyncStockStrategyService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OmsOrderJdSplitService omsOrderJdSplitService;

     /**
     * 去重订单明细中的sku
     *
     * @param orderUnRefundItemList
     * @return skuCodeList
     */
    public List<String> distinctSku(List<OcBOrderItem> orderUnRefundItemList) {
        //取出订单明细条码Code
        List<String> skuCodeList = new ArrayList<>();
        for (OcBOrderItem orderItem : orderUnRefundItemList) {
            skuCodeList.add(orderItem.getPsCSkuEcode());
        }
        //去重明细skuCode
        List<String> distinctList = skuCodeList.stream().distinct().collect(Collectors.toList());
        return distinctList;
    }

    /**
     * 返回存在库存的实体仓
     *
     * @param orderRelation 订单对象
     * @param
     * @return List<Long>
     */
    public List<SgSumStorageQueryResult> SgSumStorageQuery(OcBOrderRelation orderRelation, List<String> distinctList) {

        //取未退款成功的明细
        List<OcBOrderItem> orderUnRefundItemList = omsOrderItemService.selectUnSuccessRefund(orderRelation.getOrderInfo().getId());
        if (CollectionUtils.isEmpty(orderUnRefundItemList)) {
            List<SgSumStorageQueryResult> tmlist = new ArrayList<>();
            return tmlist;
        }
        orderRelation.setOrderItemList(orderUnRefundItemList);
        //去重明细skuCode
        //List<String> distinctList = distinctSku(orderRelation);
        //筛选实体仓
        ValueHolderV14 holderV14 = sgRpcService.querySendWareHouseStock(orderRelation);
        //库存中心实体仓的返回结
        //解析数据
        List<SgSumStorageQueryResult> storageQueryResults = (List<SgSumStorageQueryResult>) holderV14.getData();
        //库存返回没有数据的情况
        if (CollectionUtils.isEmpty(storageQueryResults)) {
            List<SgSumStorageQueryResult> tmlist = new ArrayList<>();
            return tmlist;
        }
        return storageQueryResults;
    }

    /**
     * 根据订单id查询订单对应的实体仓的可用总库存信息
     *
     * @param orderId
     * @return
     */
    public Map<String, List<SgBPhyInStorageItemExt>> querySkuTotal(Long orderId) {
        Map<String, List<SgBPhyInStorageItemExt>> retMap = new HashMap<>();
        Map<String, String> skuCountMap = new HashMap();
        try {
            //先查找订单对象
            OcBOrder ocBOrder = omsOrderService.selectOrderInfo(orderId);
            OcBOrderRelation orderRelation = new OcBOrderRelation();
            orderRelation.setOrderInfo(ocBOrder);
            //查找订单下的店铺，在店铺同步库存策略里所属逻辑仓列表
            List<Long> stCSyncStockStrategyIdList = omsSyncStockStrategyService.queryShopStoreNextList(ocBOrder.getCpCShopId());
            if (stCSyncStockStrategyIdList.size() == 0) {
                return retMap;
            }
            //根据逻辑仓找到对应的实体仓
            List<Long> wareHouseIds = omsQueryWareHouseService.queryWareHouseIds(stCSyncStockStrategyIdList);
            if (wareHouseIds.size() == 0) {
                return retMap;
            }
            //取未退款成功的明细
            List<OcBOrderItem> orderUnRefundItemList = omsOrderItemService.selectUnSuccessRefund(orderRelation.getOrderInfo().getId());
            if (CollectionUtils.isEmpty(orderUnRefundItemList)) {
                return retMap;
            }
            //去重明细skuCode
            List<String> distinctList = distinctSku(orderUnRefundItemList);
            if (distinctList.size() == 0) {
                return retMap;
            }
            //根据skuList和实体仓列表查询库存数量
//            List<SgSumStorageQueryResult> sgSumStorageQueryResultList = SgSumStorageQuery(orderRelation, distinctList);
//            if (sgSumStorageQueryResultList.size() == 0) {
//                return retMap;
//            }
            //批量查询逻辑发货单接口
//            SgSendBillQueryResult sgSendBillQueryResult = sgRpcService.querySgSend(orderRelation.getOrderInfo(), SgConstantsIF.BILL_TYPE_RETAIL);
//            //聚合可占用总库存库存数量【总可用库存 = 可用库存 + 已占用库存】
//            retMap = calcTotalAvailableNum(distinctList, orderRelation, sgSumStorageQueryResultList, sgSendBillQueryResult);
        } catch (Exception ex) {
            log.error(LogUtil.format("根据订单id查询订单对应的实体仓的可用总库存信息,异常信息为:{}", orderId), Throwables.getStackTraceAsString(ex));
            throw ex;
        }
        return retMap;
    }

    /**
     * 以skuCode为维度，聚合计算总可用库存数量
     *
     * @param distinctList
     * @param orderRelation
     * @param sgSumStorageQueryResultList
     * @param sgSendBillQueryResult
     * @return Map<String, List < gBPhyInStorageItemExt>>
     */
//    public Map<String, List<SgBPhyInStorageItemExt>> calcTotalAvailableNum(List<String> distinctList, OcBOrderRelation orderRelation, List<SgSumStorageQueryResult> sgSumStorageQueryResultList, SgSendBillQueryResult sgSendBillQueryResult) {
//        log.debug(this.getClass().getName() + "进入calcTotalAvailableNum方法======" + "订单id为：" + orderRelation.getOrderInfo().getId());
//        //构造对象，key值为skuCode，value为skuCode对应的实体仓Id和实体仓下对应的该skuCode的库存数量
//        Map<String, List<SgBPhyInStorageItemExt>> skuAndSgBPhyInStorageMap = new HashMap<>();
//        //List<SgBPhyInStorageItemExt> sgBPhyInStorageItemExtList = new ArrayList<>();
//        //解析舒威批量查询逻辑发货单接口，得出正在被占单skuCode和skuCode对应的被占用数量
//        String billId = String.valueOf(orderRelation.getOrderInfo().getId());
//        String billType = String.valueOf(SgConstantsIF.BILL_TYPE_RETAIL);
//        String mapKey = billId + "," + billType;
//        List<SgBSendItem> sgBSendItemOccupyList = new ArrayList<>();
//        Map<String, HashMap<SgBSend, List<SgBSendItem>>> resultsTmp = new HashMap<>();
//        Map<SgBSend, List<SgBSendItem>> sgSendMapTmp = new HashMap<>();
//        if (sgSendBillQueryResult != null) {
//            resultsTmp = sgSendBillQueryResult.getResults();
//            if (resultsTmp != null) {
//                sgSendMapTmp = resultsTmp.get(mapKey);
//                if (sgSendMapTmp != null) {
//                    for (SgBSend sgBSend : sgSendMapTmp.keySet()) {
//                        sgBSendItemOccupyList = sgSendMapTmp.get(sgBSend);
//                    }
//                }
//            }
//        }
//        log.debug(this.getClass().getName() + "sgBSendItemOccupyList的值为======" + sgBSendItemOccupyList.toString() + "订单id为：" + orderRelation.getOrderInfo().getId());
//        //遍历去重后的商品明细的sku列表
//        for (String skuCode : distinctList) {
//            List<SgBPhyInStorageItemExt> sgBPhyInStorageItemExtListTmp = new ArrayList<>();
//            boolean oldOrderPhyWareIdContainsFlag = false;
//            //根据skuCode查询斌哥接口返回数据，即先查询逻辑仓下面实体仓的库存情况，并以skuCode为key值将skuCode下的所有实体仓的库存数量聚合到一起。
//            Map<Long, Boolean> phyWareHouseAddOccupyFlagMap = new HashMap<>();
//            for (SgSumStorageQueryResult sgSumStorageQueryResult : sgSumStorageQueryResultList) {
//                phyWareHouseAddOccupyFlagMap.put(sgSumStorageQueryResult.getCpCPhyWarehouseId(), false);
//            }
//            for (SgSumStorageQueryResult sgSumStorageQueryResult : sgSumStorageQueryResultList) {
//                boolean phyWareIdContainsFlag = false;
//                if (sgBPhyInStorageItemExtListTmp.size() > 0) {
//                    //SgBPhyInStorageItemExt sgBPhyInStorageItemExt = new SgBPhyInStorageItemExt();
//                    if (sgSumStorageQueryResult.getPsCSkuEcode().equals(skuCode)) {
//                        //List<SgBPhyInStorageItemExt> newTmpList = skuAndSgBPhyInStorageMap.get(skuCode);
//                        for (SgBPhyInStorageItemExt tmpSgBPhyInStorageItemExt : sgBPhyInStorageItemExtListTmp) {
//                            if (tmpSgBPhyInStorageItemExt.getAdvise_phy_warehouse_id().equals(sgSumStorageQueryResult.getCpCPhyWarehouseId())
//                                    && tmpSgBPhyInStorageItemExt.getPs_c_sku_ecode().equals(sgSumStorageQueryResult.getPsCSkuEcode())) {
//                                phyWareIdContainsFlag = true;
//                                tmpSgBPhyInStorageItemExt.setTotal_qty_available(tmpSgBPhyInStorageItemExt.getTotal_qty_available().add(sgSumStorageQueryResult.getQtyAvailable()));
//                                if (sgSumStorageQueryResult.getCpCPhyWarehouseId().equals(orderRelation.getOrderInfo().getCpCPhyWarehouseId())) {
//                                    //如果是原发货实体仓，则是否包含原发货实体仓，oldOrderPhyWareIdContainsFlag的值设置为true
//                                    oldOrderPhyWareIdContainsFlag = true;
//                                    if (!phyWareHouseAddOccupyFlagMap.get(sgSumStorageQueryResult.getCpCPhyWarehouseId())) {
//                                        //遍历舒威逻辑发货单返回的正在被占用skuCode对象
//                                        for (SgBSendItem sgBSendItemTmp : sgBSendItemOccupyList) {
//                                            if (sgSumStorageQueryResult.getPsCSkuEcode().equals(sgBSendItemTmp.getPsCSkuEcode())) {
//                                                //聚合正在被占用的库存数量到，当前发货实体仓的可用总库存数中；原发货实体仓可用库存数量 = 实体仓可用库存数量 + 正在被占用的库存数量
//                                                tmpSgBPhyInStorageItemExt.setTotal_qty_available(tmpSgBPhyInStorageItemExt.getTotal_qty_available().add(sgBSendItemTmp.getQtyPreout()));
//                                            }
//                                        }
//                                        phyWareHouseAddOccupyFlagMap.put(sgSumStorageQueryResult.getCpCPhyWarehouseId(), true);
//                                    }
//                                }
//                            }
//                        }
//                        if (!phyWareIdContainsFlag) {
//                            SgBPhyInStorageItemExt tmpEntity = new SgBPhyInStorageItemExt();
//                            tmpEntity.setPs_c_sku_ecode(sgSumStorageQueryResult.getPsCSkuEcode());
//                            tmpEntity.setAdvise_phy_warehouse_id(sgSumStorageQueryResult.getCpCPhyWarehouseId());
//                            tmpEntity.setAdvise_phy_warehouse_ename(sgSumStorageQueryResult.getCpCPhyWarehouseEname());
//                            tmpEntity.setAdvise_phy_warehouse_ecode(sgSumStorageQueryResult.getCpCPhyWarehouseEcode());
//                            tmpEntity.setTotal_qty_available(sgSumStorageQueryResult.getQtyAvailable());
//                            if (sgSumStorageQueryResult.getCpCPhyWarehouseId().equals(orderRelation.getOrderInfo().getCpCPhyWarehouseId())) {
//                                //如果是原发货实体仓，则是否包含原发货实体仓，oldOrderPhyWareIdContainsFlag的值设置为true
//                                oldOrderPhyWareIdContainsFlag = true;
//                                //遍历舒威逻辑发货单返回的正在被占用skuCode对象
//                                for (SgBSendItem sgBSendItemTmp : sgBSendItemOccupyList) {
//                                    if (sgSumStorageQueryResult.getPsCSkuEcode().equals(sgBSendItemTmp.getPsCSkuEcode())) {
//                                        //聚合正在被占用的库存数量到，当前发货实体仓的可用总库存数中；原发货实体仓可用库存数量 = 实体仓可用库存数量 + 正在被占用的库存数量
//                                        tmpEntity.setTotal_qty_available(tmpEntity.getTotal_qty_available().add(sgBSendItemTmp.getQtyPreout()));
//                                    }
//                                }
//                                phyWareHouseAddOccupyFlagMap.put(sgSumStorageQueryResult.getCpCPhyWarehouseId(), true);
//                            }
//                            sgBPhyInStorageItemExtListTmp.add(tmpEntity);
//                        }
//                    }
//                } else {
//                    SgBPhyInStorageItemExt sgBPhyInStorageItemExt = new SgBPhyInStorageItemExt();
//                    if (sgSumStorageQueryResult.getPsCSkuEcode().equals(skuCode)) {
//                        sgBPhyInStorageItemExt.setPs_c_sku_ecode(sgSumStorageQueryResult.getPsCSkuEcode());
//                        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_id(sgSumStorageQueryResult.getCpCPhyWarehouseId());
//                        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_ename(sgSumStorageQueryResult.getCpCPhyWarehouseEname());
//                        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_ecode(sgSumStorageQueryResult.getCpCPhyWarehouseEcode());
//                        sgBPhyInStorageItemExt.setTotal_qty_available(sgSumStorageQueryResult.getQtyAvailable());
//                        //判断当前遍历的实体仓对象，是否是原发货实体仓
//                        if (sgSumStorageQueryResult.getCpCPhyWarehouseId().equals(orderRelation.getOrderInfo().getCpCPhyWarehouseId())) {
//                            //如果是原发货实体仓，则是否包含原发货实体仓，oldOrderPhyWareIdContainsFlag的值设置为true
//                            oldOrderPhyWareIdContainsFlag = true;
//                            //遍历舒威逻辑发货单返回的正在被占用skuCode对象
//                            for (SgBSendItem sgBSendItemTmp : sgBSendItemOccupyList) {
//                                if (sgSumStorageQueryResult.getPsCSkuEcode().equals(sgBSendItemTmp.getPsCSkuEcode())) {
//                                    //聚合正在被占用的库存数量到，当前发货实体仓的可用总库存数中；原发货实体仓可用库存数量 = 实体仓可用库存数量 + 正在被占用的库存数量
//                                    sgBPhyInStorageItemExt.setTotal_qty_available(sgBPhyInStorageItemExt.getTotal_qty_available().add(sgBSendItemTmp.getQtyPreout()));
//                                }
//                            }
//                            phyWareHouseAddOccupyFlagMap.put(sgSumStorageQueryResult.getCpCPhyWarehouseId(), true);
//                        }
//                        //将当前实体仓对象，放入到当前skuCode所对应的实体仓对象列表中
//                        sgBPhyInStorageItemExtListTmp.add(sgBPhyInStorageItemExt);
//                    }
//                    skuAndSgBPhyInStorageMap.put(skuCode, sgBPhyInStorageItemExtListTmp);
//                }
//
//            }
//            //判断斌哥返回的逻辑仓下面实体仓的库存情况列表中，是否包含原发货实体仓对象；
//            // 如果不包含原发货实体仓对象，则构造原发货实体仓对象，且原发货实体仓对象skuCode的可用库存数量等于该订单skuCode正在被占用的库存数量
//            if (!oldOrderPhyWareIdContainsFlag) {
//                SgBPhyInStorageItemExt SgBPhyInStorageItemExtThree = new SgBPhyInStorageItemExt();
//                //遍历舒威逻辑发货单返回的正在被占用skuCode对象
//                for (SgBSendItem sgBSendItemTmp : sgBSendItemOccupyList) {
//                    if (skuCode.equals(sgBSendItemTmp.getPsCSkuEcode())) {
//                        SgBPhyInStorageItemExtThree.setTotal_qty_available(sgBSendItemTmp.getQtyPreout());
//                        SgBPhyInStorageItemExtThree.setAdvise_phy_warehouse_ecode(orderRelation.getOrderInfo().getCpCPhyWarehouseEcode());
//                        SgBPhyInStorageItemExtThree.setAdvise_phy_warehouse_ename(orderRelation.getOrderInfo().getCpCPhyWarehouseEname());
//                        SgBPhyInStorageItemExtThree.setAdvise_phy_warehouse_id(orderRelation.getOrderInfo().getCpCPhyWarehouseId());
//                        SgBPhyInStorageItemExtThree.setPs_c_sku_ecode(skuCode);
//                        sgBPhyInStorageItemExtListTmp.add(SgBPhyInStorageItemExtThree);
//                    }
//                }
//            }
//            log.debug(this.getClass().getName() + "最终skuCode的值为：" + skuCode + "skuCode对应的最终sgBPhyInStorageItemExtListTmp为：" + sgBPhyInStorageItemExtListTmp.toString());
//            skuAndSgBPhyInStorageMap.put(skuCode, sgBPhyInStorageItemExtListTmp);
//        }
//        return skuAndSgBPhyInStorageMap;
//    }

    /**
     * 查询所有的店铺策略，加载到shopStrategyMap中
     *
     * @return
     */
    public Map<Long, String> loadShopStrategyToMap() {
        Map<Long, String> shopStrategyMap = new HashMap<>();
        List<StCShopStrategyDO> stCShopStrategyDOList = shopStrategyService.selectAllShopStrategy();
        for (StCShopStrategyDO stCShopStrategyDO : stCShopStrategyDOList) {
            String isPlatformSplit = "N";
            if (!StringUtils.isEmpty(stCShopStrategyDO.getIsPlatformSplit())) {
                isPlatformSplit = stCShopStrategyDO.getIsPlatformSplit();
            }
            shopStrategyMap.put(stCShopStrategyDO.getCpCShopId(), stCShopStrategyDO.getIsAutoSplit() + "-" + isPlatformSplit);
        }
        return shopStrategyMap;
    }

    /**
     * 查询所有的店铺库存同步策略缓存到shopSyncStockStrategyMap中
     *
     * @return
     */
    public Map<Long, List<Long>> loadshopSyncStockStrategyToMap() {
        Map<Long, List<Long>> shopSyncStockStrategyMap = new HashMap<>();
        List<SyncStockStrategyQueryResult> syncStockStrategyQueryResultList = omsSyncStockStrategyService.selectAllSyncStockStrategy();
        for (SyncStockStrategyQueryResult syncStockStrategyQueryResult : syncStockStrategyQueryResultList) {
            StCSyncStockStrategyDO syncStockStrategy = syncStockStrategyQueryResult.getSyncStockStrategy();
            List<CpCOrgChannelItemEntity> stCChannelStrategyItemList = syncStockStrategyQueryResult.getCpCOrgChannelItemEntityList();
            List<Long> wareHouseList = new ArrayList<>();
            for (CpCOrgChannelItemEntity stCChannelStrategyItemDO : stCChannelStrategyItemList) {
                wareHouseList.add(stCChannelStrategyItemDO.getCpCStoreId());
            }
            shopSyncStockStrategyMap.put(syncStockStrategy.getCpCShopId(), wareHouseList);
        }
        return shopSyncStockStrategyMap;
    }

    /**
     * 批量推“拆分中”的订单到ES
     *
     * @param ocBOrderList
     */
    public void batchTranSplitOrderPushEs(List<OcBOrder> ocBOrderList) {

        try {
            SpecialElasticSearchUtil.indexDocuments(
                    OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
                    ocBOrderList);
        } catch (Exception e) {
            log.error("批量更新订单为[拆单中]状态推ES异常", e);
        }
    }

}
