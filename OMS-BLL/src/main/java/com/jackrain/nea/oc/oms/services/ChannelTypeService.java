package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.rpc.CpRpcService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: chenxiulou
 * @description: 渠道类型服务
 * @since: 2019-10-17
 * create at : 2019-10-17 18:49
 */
@Component
public class ChannelTypeService {

    @Autowired
    private CpRpcService cpRpcService;

    public void channelTypeSelectHandler(JSONArray jsnAry, HashMap<String, Object> hashMap) {
        if (CollectionUtils.isNotEmpty(jsnAry) && jsnAry.size() == 1) {
            List<CpShop> shopList = new ArrayList<>();
            try {
                // 调用rpc获取店铺集合
                String val = jsnAry.getString(0);
                shopList = cpRpcService.selectShopByChannalType(val);
            } catch (Exception ex) {
                throw new NDSException(" 调用selectShopByChannalType服务异常：" + ex.toString());
            }
            if (CollectionUtils.isEmpty(shopList)) {
                return;
            }
            List<String> selShopIds;
            List<String> channelIds = shopList.stream().map(x -> x.getId().toString()).collect((Collectors.toList()));
            if (hashMap != null && hashMap.get("CP_C_SHOP_ID") != null) {
                if (hashMap.get("CP_C_SHOP_ID") != null) {
                    JSONArray shopArry = JSON.parseArray(hashMap.get("CP_C_SHOP_ID").toString());
                    if (shopArry != null) {
                        //店铺和渠道类型条件都不为空，取交集作为条件
                        List<String> shopIds = JSON.parseArray(shopArry.toJSONString(), String.class);
                        selShopIds = shopIds.stream().filter(item -> channelIds.contains(item)).collect(Collectors.toList());
                        if (selShopIds.size() == 0) {
                            //随机设置不可能的shopid值
                            selShopIds.add("##");
                        }
                    } else {
                        //店铺条件和渠道类型条件取交集
                        selShopIds = channelIds;
                    }
                } else {
                    //店铺条件和渠道类型条件取交集
                    selShopIds = channelIds;
                }
                hashMap.put("CP_C_SHOP_ID", joinallShopIds(selShopIds));

            } else if (hashMap != null) {
                hashMap.put("CP_C_SHOP_ID", joinallShopIds(channelIds));
            }

        }
    }

    public void channelTypeSelectHandler(String valString, JSONObject es) {
        if (StringUtils.isNotEmpty(valString)) {
            List<CpShop> shopList = new ArrayList<>();
            try {
                // 调用rpc获取店铺集合
                shopList = cpRpcService.selectShopByChannalType(valString);
            } catch (Exception ex) {
                throw new NDSException(" 调用selectShopByChannalType服务异常：" + ex.toString());
            }
            if (CollectionUtils.isEmpty(shopList)) {
                return;
            }
            List<String> selShopIds;
            List<String> channelIds = shopList.stream().map(x -> x.getId().toString()).collect((Collectors.toList()));
            if (es != null) {

                JSONArray shopArry = es.getJSONArray("CP_C_SHOP_ID");
                if (shopArry != null) {
                    //店铺和渠道类型条件都不为空，取交集作为条件
                    List<String> shopIds = JSON.parseArray(shopArry.toJSONString(), String.class);
                    selShopIds = shopIds.stream().filter(item -> channelIds.contains(item)).collect(Collectors.toList());
                    if (selShopIds.size() == 0) {
                        selShopIds.add("##");//随机设置不可能的shopid值
                    }
                } else {
                    //店铺条件和渠道类型条件取交集
                    selShopIds = channelIds;
                }
                es.put("CP_C_SHOP_ID", joinallShopIds(selShopIds));

            } else {
                throw new NDSException("channelTypeSelectHandler 传参失败错误！ 未传信息");
            }

        }
    }


    /**
     * 组装店铺id
     *
     * @return jsonArray
     */
    private JSONArray joinallShopIds(List<String> idList) {
        JSONArray jsArys = new JSONArray();
        for (String e : idList) {
            jsArys.add(e);
        }
        return jsArys;

    }


}
