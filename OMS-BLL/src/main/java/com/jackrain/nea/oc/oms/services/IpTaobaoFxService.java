package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.BackflowStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.TaobaoFxOrderTransferUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.BllSystemParameterKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.BllCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 淘宝分销订单中间表处理服务
 *
 * @author: 周琳胜
 * @since: 2019/7/9
 * create at : 2019/7/9 14:50
 */
@Component
@Slf4j
public class IpTaobaoFxService {
    @Autowired
    private IpBTaobaoFxOrderMapper ipBTaobaoFxOrderMapper;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private IpBTaobaoFxOrderItemMapper ipBTaobaoFxOrderItemMapper;

    @Autowired
    private TaobaoFxOrderTransferUtil taobaoFxOrderTransferUtil;

    @Autowired
    private OcBOrderPaymentMapper orderPaymentMapper;

    @Autowired
    private OmsOrderLogService orderLogService;

    @Autowired
    private OcBOrderLinkService orderLinkService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;

    private static final int OMS_ORDER_REDIS_TIMEOUT = 24 * 60 * 60 * 1000;

    /**
     * 依据OrderNo进行查询中间表淘宝信息数据
     *
     * @param orderNo 淘宝订单号
     * @return 中间表淘宝信息数据
     */
    public IpTaobaoFxOrderRelation selectTaobaoFxOrder(String orderNo) {
        Long orderN = Long.parseLong(orderNo);
        IpBTaobaoFxOrder orderInfo = this.ipBTaobaoFxOrderMapper.selectTaobaoFxOrderByFenxiaoId(orderN);

        if (orderInfo == null) {
            return null;
        }

        IpTaobaoFxOrderRelation taoBaoFxOrderRelation = new IpTaobaoFxOrderRelation();
        long taobaoFxOrderId = orderInfo.getId();
        List<IpBTaobaoFxOrderItemExt> orderItemList =
                this.ipBTaobaoFxOrderItemMapper.selectOrderItemList(taobaoFxOrderId);


        taoBaoFxOrderRelation.setTaobaoFxOrderItems(orderItemList);
        taoBaoFxOrderRelation.setIpBTaobaoFxOrder(orderInfo);

        return taoBaoFxOrderRelation;
    }

    /**
     * 更新淘宝分销中间表
     *
     * @param ipBTaobaoFxOrder 淘宝分销中间表更新实体
     * @return
     */
    public void updateTransferStatus(IpBTaobaoFxOrder ipBTaobaoFxOrder) {
        // 分库键置为null 否则会报错
        Long fenxiaoId = ipBTaobaoFxOrder.getFenxiaoId();
        ipBTaobaoFxOrder.setFenxiaoId(null);
        int count = ipBTaobaoFxOrderMapper.updateById(ipBTaobaoFxOrder);
        ipBTaobaoFxOrder.setFenxiaoId(fenxiaoId);
    }

    /**
     * 更新全渠道订单
     *
     * @param ocBOrder
     */
    public void updateOmsOrder(OcBOrder ocBOrder) {
        orderMapper.updateById(ocBOrder);
    }

    /**
     * 依据SourceCode查询订单信息
     *
     * @param sourceCode 平台单号
     * @return List<OcBOrder>
     */
    public List<OcBOrder> selectOmsOrderInfo(String sourceCode) {

        //  List<Long> ids = ES4Order.getIdsBySourceCode(sourceCode);
        List<Long> ids = GSI4Order.getIdListBySourceCode(sourceCode);
        // 先查询ES，再查询Redis。防止重复转单。因为ES有可能延迟100MS
        if (CollectionUtils.isEmpty(ids)) {
            Long id = this.selectOmsOrderFromRedis(sourceCode);
            if (Objects.nonNull(id)) {
                ids = new ArrayList<>();
                ids.add(id);
            }
        }
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<OcBOrder> orderList = orderMapper.selectBatchIds(ids);
        return orderList;
    }

    /**
     * 创建ElasticIndex
     *
     * @return 是否成功
     * @throws IOException IO操作异常
     */
    private boolean creatOrderIndex() throws IOException {
        List<Class> esChildsClass = new ArrayList<Class>();
        esChildsClass.add(OcBOrderItem.class);
        esChildsClass.add(OcBOrderLog.class);
        return SpecialElasticSearchUtil.indexCreate(esChildsClass, OcBOrder.class);
    }

    /**
     * 查询订单是否存在Redis中
     *
     * @param sourceCode 原始订单号
     * @return
     */
    private Long selectOmsOrderFromRedis(String sourceCode) {
        Long value = null;
        String redisKey = BllRedisKeyResources.getOmsOrderKey(sourceCode);
        CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        Boolean hasKey = objRedisTemplate.hasKey(redisKey);
        if (hasKey != null && hasKey) {
            value = objRedisTemplate.opsForValue().get(redisKey);
        }
        return value;
    }

    /**
     * 判断订单是否都为已作废或取消订单
     *
     * @param ocBOrderList 全渠道订单集合
     * @return
     */
    public List<OcBOrder> checkOrderStatus(List<OcBOrder> ocBOrderList) {
        Iterator<OcBOrder> it = ocBOrderList.iterator();
        while (it.hasNext()) {
            OcBOrder order = it.next();
            Integer orderStatus = order.getOrderStatus();
            if (OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus) || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) {
                it.remove();
            }
        }
        return ocBOrderList;
    }

    public IpTaobaoFxOrderRelation convertTaobaoFxToOrder(IpTaobaoFxOrderRelation orderInfo,
                                                          boolean isHistoryOrder) {
        OcBOrder ocBOrder = taobaoFxOrderTransferUtil.convertTaobaoFxOrderToOmsOrder(orderInfo, isHistoryOrder);
        orderInfo.setOcBOrder(ocBOrder);
        orderInfo.setOcBOrderItems(taobaoFxOrderTransferUtil.convertTaobaoFxOrderItemToOmsOrderItem(orderInfo.getTaobaoFxOrderItems(), ocBOrder));
        orderInfo.setOcBOrderPayment(taobaoFxOrderTransferUtil.convertTaobaoFxOrderPayToOmsOrderPay(ocBOrder,
                orderInfo.getIpBTaobaoFxOrder()));
        OcBOrderLink orderLink = taobaoFxOrderTransferUtil.buildOrderLink(ocBOrder, orderInfo.getIpBTaobaoFxOrder());
        orderInfo.setOrderLink(orderLink);

        return orderInfo;
    }

    /**
     * 执行全渠道订单数据新增
     *
     * @param relation       包含转换好的，订单主子表数据
     * @param isHistoryOrder 是否为历史单据信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderInfo(IpTaobaoFxOrderRelation relation, boolean isHistoryOrder) {
        try {
            OcBOrder ocBOrder = relation.getOcBOrder();
            if (orderMapper.insert(ocBOrder) > 0) {
                //    log.info("TaobaoFxTransferOrder.日志服务：订单主表新增成功");
                orderItemMapper.batchInsert(relation.getOcBOrderItems());
                orderPaymentMapper.insert(relation.getOcBOrderPayment());
                // 更新整单平摊金额
                taobaoFxOrderTransferUtil.doCheckAndUpdateBlanceMoney(ocBOrder);

                if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
                    List<OcBOrder> ocBOrders = new ArrayList<>();
                    ocBOrders.add(ocBOrder);
                    this.orderLinkService.addOrderFinkLogsThread(ocBOrders,
                            BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
                }
                if (isHistoryOrder) {
                    orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(),
                            "历史订单转换为新增订单成功", "", "", SystemUserResource.getRootUser());
                } else {
                    /**
                     * 单据转单或新增后插入占单任务表
                     */
                    OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                    toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                    toBeConfirmedTask.setOrderId(ocBOrder.getId());
                    toBeConfirmedTask.setCreationdate(new Date());
                    toBeConfirmedTask.setStatus(0);
                    toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
                    orderLogService.addUserOrderLog(ocBOrder.getId(),
                            ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(), "新增订单成功", ""
                            , "", SystemUserResource.getRootUser());
                }
                // Redis推送。主要是为了防止ES在存储数据时，会延迟100ms左右。为了保障不重复转单，在Redis进行存储。
                // 在进行查询时，先查询ES，若ES不存在，再查询Redis。
                String redisKey = BllRedisKeyResources.getOmsOrderKey(relation.getOrderNo().toString());
                CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
                objRedisTemplate.opsForValue().set(redisKey, ocBOrder.getId(), OMS_ORDER_REDIS_TIMEOUT, TimeUnit.MILLISECONDS);
            }
        } catch (Exception ex) {
            //    log.error("TaobaoFxTransferOrder.日志服务：新增全渠道订单失败", ex);
            throw new NDSException("新增全渠道订单失败" + ex);
        }
    }
}
