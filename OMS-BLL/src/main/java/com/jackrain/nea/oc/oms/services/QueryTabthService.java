package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.core.query.ColumnLink;
import com.jackrain.nea.core.schema.Column;
import com.jackrain.nea.core.schema.Table;
import com.jackrain.nea.core.schema.TableManager;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.UserConfig;
import com.jackrain.nea.oc.oms.model.user.UserQueryConfig;
import com.jackrain.nea.oc.oms.model.user.UserQueryRequest;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.*;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.SearchCmd;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;


@Slf4j
@Component
public class QueryTabthService {

    private String service = "com.jackrain.nea.web.QueryListCmd";

    public ValueHolder queryTabth(String obj, User userWeb) throws NDSException {
        ValueHolder vh = new ValueHolder();

        RuntimeCompute runtimeCompute = ApplicationContextHandle.getBean(RuntimeCompute.class);

        runtimeCompute.startRuntime();


        try {
            JSONObject jo = JSON.parseObject(obj);
            JSONObject jor = (JSONObject) jo.clone();
            JSONObject result = new JSONObject();
            JSONObject fullRangeSubTotalRow = null;
            QuerySession qsession = QueryUtils.createQuerySession(userWeb);
            TableManager tm = qsession.getTableManager();
            List ids = new ArrayList();
            //solr 合计分离
            boolean getsumfileds = Tools.getBoolean(jo.getBoolean("getsumfileds"), false);

            int Totline = -1;
            int RowCount = 0;
            boolean issolr = false;
            int totalRowCount = jo.getIntValue("totalRowCount");

            Table table = null, parentTable = null;

            int tableid = jo.getIntValue("tableid");
            int refcolid = jo.getIntValue("refcolid");
            //如果是海量表 第一次查询isbig 传 true
            Boolean isbig = jo.getBoolean("isbig") == null ? false : jo.getBoolean("isbig");

            boolean ifreftab = false;

            if (refcolid > 0) {
                table = tm.getColumn(refcolid).getReferenceTable();
                //判断是否外键关联
                ifreftab = true;
                parentTable = table.getParentTable(tm);

                //log.debug("getTableQuery refcol table name ->" + table.getName() + " refcolid->" + refcolid);
                //log.debug(table.getTrigger("solr").toString());
            }

            if (tableid > 0 && table == null) {
                table = tm.getTable(tableid);
            }

            if (jo.getString("table") != null && table == null) {
                table = tm.getTable(jo.getString("table"));
            }


            if (table == null) {
                throw new NDSException("tablenull");
            }

            if (!userWeb.isAdmin()) {
//
//                if(ifreftab&&parentTable!=null){
//                    boolean ParentisRead = hasPermission(qsession, MenuType.TABLE, Long.valueOf(parentTable.getId()), Security4Utils.PREMEnum.READ.getPrem());
//                    if(!ParentisRead) {
//                        throw new NDSSecurityException(table.getDescription(qsession.getLocale()) + Resources.getMessage("-没有访问权限！", qsession.getLocale()));
//                    }
//                }

            }

            issolr = table.getTrigger("solr") != null ? true : false;

            //如果fixedcolumns 里面含有ID
            if (jo.containsKey("isfixedid") && jo.getBoolean("isfixedid")) {
                issolr = false;
            }


            //TODO 开发支持跨库字段模糊查考虑到性能问题所以目前的 AK 查询限制返回100条作为搜索范围
            jo = remotecolfixcol(jo, table, qsession);

            runtimeCompute.endRuntime("after remotecolfixcol ->" + table);

            UserQueryRequest query = UserAjaxUtils.parseQuery(jo, qsession, userWeb.getId(), userWeb.getLocale(), table);


            //

            JSONArray tabth = query.SelectbyDisObj(true, userWeb.getLocale());
            if (CollectionUtils.isEmpty(tabth)) {
                throw new NDSException("tabth is null 请先配置对应表的字段！");
            }

            vh.put("code", 0);
            vh.put("message", "success");
            CusRedisTemplate<String, String> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            UserQueryConfig userQueryConfig = (UserQueryConfig) objRedisTemplate.opsForHash().get("UserQueryConfig",
                    userWeb.getId().toString());
            vh.put("date", setUserCofig(tabth, userQueryConfig, table.getName()));
        } catch (Exception e) {
            log.error(LogUtil.format("querylist,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            vh.put("code", -1);
            vh.put("message", e.getMessage());
            return vh;
        }
        return vh;
    }

    private JSONObject remotecolfixcol(JSONObject jo, Table table, QuerySession qsession) {

        if (Validator.isNotNull(jo.getJSONObject("fixedcolumns"))) {
            JSONObject fixedcolumns = jo.getJSONObject("fixedcolumns");
//            fixedcolumns = (JSONObject) JSONObject.parse(fixedcolumns.toJSONString().replace("=", ""));
            JSONObject newfix = new JSONObject();
            for (String key : fixedcolumns.keySet()) {
                JSONObject po = new JSONObject();
                ColumnLink cl = new ColumnLink(table.getName() + "." + key, qsession.getTableManager());
                Column column = cl.getColumns()[0];
                Object val = fixedcolumns.get(key);
                if (column.getIsremote() && cl.getColumnIDs().length > 1 && Validator.isNotNull(val)) {
                    String center = column.getReferenceTable().getCategory().getSubSystem().getCenter();
                    String[] gv = center.split(":");
                    if (gv.length != 2) {
                        throw new NDSException("center is error");
                    }

                    Object o = ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), service, gv[0], gv[1]);
                    JSONObject result = ((SearchCmd) o).fuzzyQueryByAK(qsession.getUser(), val.toString(), column.getId(), "", 100);
                    List ids = new ArrayList();
                    if (result.getInteger("code") == 0) {
                        JSONArray vals = JSONArray.parseArray(result.get("data").toString());
                        //log.debug(vals.toString());
                        if (vals.size() > 0) {
                            vals.forEach((entry) -> {
                                JSONObject obj = (JSONObject) entry;
                                String id = obj.getString("ID");
                                ids.add(id);
                            });
                            newfix.put(column.getName(), Tools.toString(ids, ","));
                        } else {
                            newfix.put(column.getName(), "=-1");
                        }
                    }
                } else {
                    newfix.put(key, val);
                }
                jo.put("fixedcolumns", newfix);
            }
        }
        return jo;
    }


    private ValueHolder solrserverdata(JSONObject jo, Table table, TableManager tm, QuerySession qsession, Boolean getsumfields) throws Exception {
        DefaultWebEvent event = new DefaultWebEvent("solr", new HashMap());

        ValueHolder vh = new ValueHolder();

        JSONObject jor = table.getJSONProps();

        JSONObject subfixcol = new JSONObject();

        //{"solrfilter":{"fixcol":{"BILL_NO":"1"}}}
        if (jor != null && jor.containsKey("solrfilter")) {
            subfixcol = jor.getJSONObject("solrfilter").getJSONObject("fixcol");
        }

        event.put("command", table.getTrigger("solr").toString());


        if (!jo.containsKey("fixedcolumns")) {

            if (subfixcol != null) {
                jo.put("fixedcolumns", subfixcol);
            } else {
                jo.put("fixedcolumns", new JSONObject());
            }

        }
//        修改subfix 逻辑  没有条件默认才走subfix
        else {
            JSONObject fixobj = jo.getJSONObject("fixedcolumns");
            for (String key : subfixcol.keySet()) {
                if (!fixobj.containsKey(key)) {
                    fixobj.put(key, subfixcol.get(key));
                }

            }
            // fixobj.putAll(subfixcol);
            jo.put("fixedcolumns", fixobj);
        }

        if (jo.getJSONObject("fixedcolumns") != null) {

            JSONObject fixedcolumns = jo.getJSONObject("fixedcolumns");
            //fixedcolumns = (JSONObject) JSONObject.parse(fixedcolumns.toJSONString().replace("=", ""));
            JSONArray orderby = jo.getJSONArray("orderby") == null ? new JSONArray() : jo.getJSONArray("orderby");

            //默认取表配置的orderby
            if (orderby.isEmpty()) {
                if (jor != null && jor.containsKey("orderby")) {
                    orderby = jor.getJSONArray("orderby");
                }
            }

            if (orderby.isEmpty()) {
                // [{"name":"VP_C_VIP.ID","asc":true}]
                JSONObject ordobj = new JSONObject();
                ordobj.put("column", table.getName() + "." + table.getPrimaryKey().getName());
                ordobj.put("asc", true);
                orderby.add(ordobj);
            }
            JSONArray searchtype = new JSONArray();
            for (String key : fixedcolumns.keySet()) {
                JSONObject po = new JSONObject();
                ColumnLink cl = new ColumnLink(table.getName() + "." + key, tm);
                Column column = cl.getColumns()[0];
                if (Validator.isNotNull(column.getSearchtype())) {
                    po.put("name", key);
                    po.put("type", column.getSearchtype());
                    searchtype.add(po);
                }
            }

            JSONObject sjo = new JSONObject();
            sjo.put("fixedcolumns", fixedcolumns);


            //if(!getsumfields){
            sjo.put("orderby", orderby);
            //}

            sjo.put("searchtype", searchtype);

            sjo.put("range", Tools.getInt(jo.getInteger("range"), QueryUtils.getdefalutrange()));
            sjo.put("startindex", jo.getIntValue("startindex"));
            sjo.put("issql", true);
            sjo.put("table", table.getName());

            if (getsumfields) {
                JSONArray jarr = new JSONArray();
                for (Iterator iter = table.getSumFields(); iter.hasNext(); ) {
                    Column col = (Column) iter.next();
                    jarr.add(col.getName());
                }
                if (jarr.size() > 0) {
                    sjo.put("sumfields", jarr);
                }
            }


            event.put("param", sjo);
//            vh = handle.process(sjo, qsession, event, "solr");


        }

        return vh;
    }

    /**
     * 绑定ad_client 元数据 配置和 用户 自定义 配置
     *
     * @param tabth
     * @param userQueryConfig
     * @param tableName
     */
    private JSONArray setUserCofig(JSONArray tabth, UserQueryConfig userQueryConfig, String tableName) {
        HashMap<String, JSONObject> map = new HashMap<>();
        // 用戶定制了查詢 ， 转换成 map
        JSONArray tabthNew = new JSONArray();
        for (int i = 0; i < tabth.size(); i++) {
            JSONObject tabthJson = tabth.getJSONObject(i);
            if (tabthJson != null && StringUtils.isNotEmpty(tabthJson.getString("colname")) && tabthJson.getBoolean("isfilter")) {
                map.put(tabthJson.getString("colname"), tabthJson);
                tabthNew.add(tabthJson);
            }
        }
        // 将map 与 tabth 数据进行匹配
        List<UserConfig> userConfig = userQueryConfig.getParam(tableName);
        if (CollectionUtils.isNotEmpty(userConfig)) {
            for (int i = 0; i < userConfig.size(); i++) {
                UserConfig json = userConfig.get(i);
                if (json != null) {
                    Long id = json.getId();
                    String colname = json.getColname();
                    Boolean isfilter = json.getIsfilter();
                    String orderno = json.getOrderno();
                    JSONObject mapJson = map.get(colname);
                    if (mapJson != null && id.longValue() == (mapJson.getLong("colid")).longValue()) {
                        mapJson.put("isfilter", isfilter);
                        mapJson.put("orderno", orderno);
                    }
                }
            }
        }
        return tabthNew;
    }


}