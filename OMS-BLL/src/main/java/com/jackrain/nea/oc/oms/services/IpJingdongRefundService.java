package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4IpJingDongOrder;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.gsi.GSI4OrderItem;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.RefundOrderSourceTypeEnum;
import com.jackrain.nea.oc.oms.services.calculate.qty.OmsOrderQtyCalculateService;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnAfterUtil;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OmsDeliveredRefundFormUtil;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.util.GetCustomerIpUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.web.face.User;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Descroption 京东退货转换
 * <AUTHOR>
 * @Date 2019/4/24 20:47
 */
@Slf4j
@Component
public class IpJingdongRefundService {

    @Autowired
    private IpBJingdongRefundMapper ipBJingdongRefundMapper;

    @Autowired
    private IpBJingdongOrderMapper ipBJingdongOrderMapper;

    @Autowired
    private IpBJingdongOrderItemMapper ipBJingdongOrderItemMapper;

    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private GetCustomerIpUtil getCustomerIpUtil;

    @Autowired
    protected CpRpcService cpRpcService;

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private OcBReturnBfSendMapper ocBReturnBfSendMapper;
    @Autowired
    private OmsDeliveredRefundFormUtil omsDeliveredRefundFormUtil;

    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;

    /**
     * @param refundId
     * @return com.jackrain.nea.oc.oms.model.relation.IpJingdongRefundRelation
     * @Descroption 获取京东退换货关系数据对象
     * @Author: 洪艺安
     * @Date 2019/4/25
     */
    public IpJingdongRefundRelation getJingdongRefundRelation(String refundId) {
        IpJingdongRefundRelation ipJingdongRefundRelation = null;
        long intRefundId = Long.valueOf(refundId);
        //获取平台单号

        IpBJingdongRefund jingdongRefund = ipBJingdongRefundMapper.selectJingdongRefundByAfsserviceId(intRefundId);
        if (null != jingdongRefund) {
            //商品编码
            Long wareid = jingdongRefund.getWareid();
            ipJingdongRefundRelation = new IpJingdongRefundRelation();
            //1.设置京东退单中间表对象
            ipJingdongRefundRelation.setJingdongRefund(jingdongRefund);
            String orderid = jingdongRefund.getOrderid();
            //2.设置退换货订单对象
            Long ocReturnId = ES4ReturnOrder.findJDIdByReturnIdAndStatus(jingdongRefund.getAfsserviceid());

            if (Objects.nonNull(ocReturnId)) {
                OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(ocReturnId);
                ipJingdongRefundRelation.setOcReturnOrder(ocBReturnOrder);
            }
            //3.设置京东订单中间表
            if (ipJingdongRefundRelation.getOcReturnOrder() == null) {

                JSONArray dataByJdOrder = ES4IpJingDongOrder.findIdByOrderId(orderid);
                if (log.isDebugEnabled()) {
                    log.debug(" es查询京东订单中间表返回数据  {}", dataByJdOrder);
                }

                if (dataByJdOrder != null && !dataByJdOrder.isEmpty()) {
                    JSONObject jsonObject = dataByJdOrder.getJSONObject(0);
                    Long id = jsonObject.getLong("ID");
                    IpBJingdongOrder ipBJingdongOrder = ipBJingdongOrderMapper.selectById(id);
                    ipJingdongRefundRelation.setIpJingdongOrder(ipBJingdongOrder);
                }
            }
            //4.设置全渠道订单明细表
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectOcBOrderByTid(orderid+"");
            if (CollectionUtils.isEmpty(ocBOrders)) {
                return ipJingdongRefundRelation;
            }
            ocBOrders = ocBOrders.stream().distinct().collect(Collectors.toList());

            //生成所有订单明细List
            List<OcBOrderItem> orderItemAll = new ArrayList<>();
            List<OcBOrderItem> isGiftItemList = new ArrayList<>();
            Integer isGift = 1;
            for (OcBOrder ocBOrder : ocBOrders) {
                Integer orderStatus = ocBOrder.getOrderStatus();
                if(OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                        || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)){
                    continue;
                }
                Long orderId = ocBOrder.getId();
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemList(orderId);
                List<OcBOrderItem> isGiftItems = orderItems.stream().filter(p -> isGift.equals(p.getIsGift())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(isGiftItems)){
                    isGiftItemList.addAll(isGiftItems);
                    orderItems.removeAll(isGiftItems);
                }
                orderItems = orderItems.stream().filter(p -> orderid.equals(p.getTid())
                        && p.getOoid().equals(String.valueOf(wareid))).collect(Collectors.toList());
                orderItemAll.addAll(orderItems);
            }

            //普通商品明细
            List<String> giftRelations = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(orderItemAll)){
                List<OcBOrder> ocBOrderList = ocBOrders.stream().filter(x -> x.getId().equals(orderItemAll.get(0).getOcBOrderId())).collect(Collectors.toList());
                giftRelations = orderItemAll.stream().map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
                ipJingdongRefundRelation.setOcBOrderItems(orderItemAll);
                ipJingdongRefundRelation.setOcBOrder(ocBOrderList.get(0));
                boolean isMilkCardOrder = omsRefundOrderService.checkIsMilkCardOrder(Lists.newArrayList(orderItemAll.get(0)));
                ipJingdongRefundRelation.setMilkCardOrder(isMilkCardOrder);
            }

            //赠品明细
            if(CollectionUtils.isNotEmpty(isGiftItemList)){
                List<String> finalGiftRelations = giftRelations;
                isGiftItemList = isGiftItemList.stream().filter(x -> StringUtils.isBlank(x.getGiftRelation()) || finalGiftRelations.contains(x.getGiftRelation())).collect(Collectors.toList());
                ipJingdongRefundRelation.setGiftItemList(isGiftItemList);
            }
        }
        return ipJingdongRefundRelation;
    }

    /**
     * @param jingdongRefund
     * @return boolean
     * @Descroption 异常修改退单中间表备注及推送ES并且记录日志
     * @Author: 洪艺安
     * @Date 2019/4/26
     */
    public boolean updateRefundIsTransError(IpBJingdongRefund jingdongRefund, String errorMsg) {
        String sysRemark = SysNotesConstant.SYS_REMARK0 + errorMsg;
        boolean flag = this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                sysRemark, jingdongRefund);
        return flag;
    }

    /**
     * @param istrans
     * @param remark
     * @param jingdongRefund
     * @return boolean
     * @Descroption 手动需改退单中间表相关信息
     * @Author: 洪艺安
     * @Date 2019/4/28
     */
    public boolean updateReturnOrder(int istrans, String remark, IpBJingdongRefund jingdongRefund) {
        try {
            jingdongRefund.setIstrans(istrans);
            jingdongRefund.setSysremark(remark);
            jingdongRefund.setModifydate(new Date());
            jingdongRefund.setModifierename("sys");
            jingdongRefund.setTransdate(new Date());
            JSONObject jobj = new JSONObject();
            jobj.put("afsserviceid", jingdongRefund.getAfsserviceid());
            jobj.put("istrans", istrans);
            jobj.put("sysremark", remark);
            jobj.put("modifieddate", new Date());
            jobj.put("modifierename", "sys");
            jobj.put("transdate", new Date());
            Integer transFailReason = jingdongRefund.getTransFailReason();
            if (transFailReason != null) {
                jobj.put("trans_fail_reason", transFailReason);
            }
            int i = ipBJingdongRefundMapper.updateRefundOrder(jobj);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新京东退货中间表失败,Id:{}", jingdongRefund.getId(), e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * @param ocReturnOrder
     * @return boolean
     * @Descroption 修改订单中心退换单表物流信息
     * @Author: 洪艺安
     * @Date 2019/4/28
     */
    public boolean updateReturnOrderLogisticsInfo(OcBReturnOrder ocReturnOrder) {
        boolean updateFlag = true;
        try {
            int updateNum = ocBReturnOrderMapper.updateReturnOrderLogisticsInfo(ocReturnOrder.getLogisticsCode(), ocReturnOrder.getCpCLogisticsId(), ocReturnOrder.getId());
            if (updateNum <= 0) {
                updateFlag = false;
            }
        } catch (Exception e) {
            log.error("updateReturnOrderLogisticsInfo更新退换货单据物流信息出错!", e);
            throw new NDSException(e);
        }
        return updateFlag;
    }

    /**
     * 更新物流信息
     *
     * @param order 退单
     * @return 更新结果
     */
    public boolean modifyReturnOrderLgInfo(OcBReturnOrder order) {
        return ocBReturnOrderMapper.updateById(order) > 0;
    }

    /**
     * @param
     * @return boolean
     * @Descroption 插入退换货日志
     * @Author: 洪艺安
     * @Date 2019/4/28
     */
    public void insetOcReturnOrderLog(String logType, String logMessage, Long returnOrderId, User user) {
        try {
            OcBReturnOrderLog ocReturnOrderLog = new OcBReturnOrderLog();
            ocReturnOrderLog.setLogType(logType);
            ocReturnOrderLog.setLogMessage(logMessage);
            String ip = user.getLastloginip();
            if (StringUtil.isEmpty(ip)) {
                ip = getCustomerIpUtil.getIp(null);
            }
            ocReturnOrderLog.setIpAddress(ip);
            ocReturnOrderLog.setOcBReturnOrderId(returnOrderId);
            returnOrderTransferUtil.saveSysLog(ocReturnOrderLog, user);
            int insertNum = ocBReturnOrderLogMapper.insert(ocReturnOrderLog);
//            if (insertNum > 0) {
//                SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_OCBRETURNORDERLOG,
//                        TaobaoReturnOrderExt.TABLENAME_OCBRETURNORDERLOG, ocReturnOrderLog,
//                        ocReturnOrderLog.getId());
//            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 插入退换货订单日志异常!", e);
            throw new NDSException(e);
        }
    }

    /**
     * @param jingdongRefund
     * @param ipJingdongOrder
     * @return boolean
     * @Descroption 转换京东订单原单
     * @Author: 洪艺安
     * @Date 2019/4/28
     */
    public boolean transformOriginalOrder(IpBJingdongRefund jingdongRefund,
                                          IpBJingdongOrder ipJingdongOrder, IpJingdongRefundRelation orderInfo) {
        boolean existSkuFlag = false;
        List<IpBJingdongOrderItemExt> orderItemList = ipBJingdongOrderItemMapper.selectJingdongOrderList(ipJingdongOrder.getId());
        for (IpBJingdongOrderItemExt item : orderItemList) {
            if (jingdongRefund.getWareid().equals(item.getSkuId())) {
                if (StringUtil.isNotEmpty(item.getOuterSkuId())) {
                    ProductSku productSku = psRpcService.selectProductSku(item.getOuterSkuId());
                    if (productSku != null) {
                        existSkuFlag = true;
                        item.setProdSku(productSku);
                        orderInfo.setIpBJingdongOrderItemExt(item);
                    }
                }
                break;
            }
        }
        if (!existSkuFlag) {
            Date createDate = jingdongRefund.getCreationdate();
            Date currentDate = new Date();
            long overTime = 3 * 24 * 60 * 60 * 1000L + createDate.getTime();
            if (overTime < currentDate.getTime()) {
                this.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        SysNotesConstant.SYS_REMARK22, jingdongRefund);
            } else {
                this.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        SysNotesConstant.SYS_REMARK23, jingdongRefund);
            }
        }
        return existSkuFlag;
    }

    /**
     * @param orderIds
     * @return boolean
     * @Descroption 修改全渠道订单退货状态为1-退货中
     * @Author: 洪艺安
     * @Date 2019/4/28
     */
    public boolean updateOrderReturnStatus(IpJingdongRefundRelation orderInfo, List<Long> orderIds) {
        boolean exitsOrderFlag = false;
        IpBJingdongRefund jingdongRefund = orderInfo.getJingdongRefund();
        for (Long orderId : orderIds) {
            String orderStatus = OmsOrderStatus.CANCELLED.toInteger() + "," + OmsOrderStatus.SYS_VOID.toInteger();
            OcBOrder ocBOrder = ocBOrderMapper.selectValidOrderByID(orderId, orderStatus, jingdongRefund.getOrderid(), jingdongRefund.getWareid().toString());
            if (ocBOrder != null) {
                ocBOrderMapper.updateOcBorderReturnStatus(ocBOrder.getId(), 1);//1-退货中
            }
        }
        return true;
    }

    /**
     * @param orderInfo
     * @return java.util.List<com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation>
     * @Descroption 生成退单关系对象
     * @Author: 洪艺安
     * @Date 2019/4/29
     */
    public List<OcBReturnOrderRelation> buildJingdongReturnOrders(IpJingdongRefundRelation orderInfo) {
        List<OcBReturnOrderRelation> returnOrderRelations = new ArrayList<>();
        IpBJingdongOrderItemExt jdOrderItem = orderInfo.getIpBJingdongOrderItemExt();
        OcBOrder order = orderInfo.getOcBOrder();
        BigDecimal returnAmtListTotal = new BigDecimal(0);
        List<OcBReturnOrderRefund> returnOrderRefunds = new ArrayList<>();
        StringBuilder allSkuSb = new StringBuilder();
        BigDecimal qtyInstore = BigDecimal.ZERO;
        //退货数量
        BigDecimal servicecount = orderInfo.getJingdongRefund().getServicecount();

        //1.计算商品应退金额及生成退单明细
        if (jdOrderItem != null) {
            if (order != null) {
                List<Long> orderIds = new ArrayList<>();
                orderIds.add(order.getId());
                //liqb 更改ooid类型从Long类型改成String类型
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListByOoid(String.valueOf(jdOrderItem.getSkuId()), orderIds);
                if (CollectionUtils.isEmpty(orderItems)) {
                    orderInfo.getJingdongRefund().setTransFailReason(TransNodeTipEnum.ORDER_NOT_FOUND.val());
                    updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK1, orderInfo.getJingdongRefund());
                    return null;
                }
                ProductSku productSku = jdOrderItem.getProdSku();
                int skuType = productSku.getSkuType();
                if (SkuType.COMBINE_PRODUCT == skuType) {
                    for (int i = 0; i < orderItems.size(); i++) {
                        OcBOrderItem orderItem = orderItems.get(i);
                        BigDecimal amtRefund = orderItem.getRealAmt().divide(orderItem.getQty(), 2, BigDecimal.ROUND_CEILING).multiply(productSku.getNum()).setScale(2,
                                BigDecimal.ROUND_CEILING);
                        OcBReturnOrderRefund returnOrderRefund = buildReturnOrderRefund(orderItem, orderInfo.getJingdongRefund());
                        returnOrderRefund.setAmtRefund(amtRefund);

                        // @20200811 bug#20832 去实际可退和申请退数量比较，如果实际可退 < 申请退，则抛出异常
                        BigDecimal qtyCanReturn = OmsOrderQtyCalculateService.getInstance().calQtyCanReturn4Sku(Lists.newArrayList(orderItem));

                        if (qtyCanReturn.compareTo(productSku.getNum()) < 0) {
                            // 申请数量 < 可退数量
                            log.error("京东退换货单申请数量大于可退数量：{}/{}", jdOrderItem.getId(), jdOrderItem.getSkuId());
                            throw new NDSException("申请数量大于可退数量");
                        }

                        returnOrderRefund.setQtyRefund(productSku.getNum());
                        //计算结算金额和结算单价
                        setPriceAndTotPrice(orderItem, returnOrderRefund);
                        returnAmtListTotal = returnAmtListTotal.add(amtRefund);
                        returnOrderRefunds.add(returnOrderRefund);
                        //最多显示5个条码信息
                        if (i <= 4) {
                            getAllSkuSb(allSkuSb, orderItem);
                        }
                    }
                } else {
                    List<OcBOrderItem> orderItemList = orderInfo.getOcBOrderItems();
                    List<OcBOrderItem> isGiftItems = orderInfo.getGiftItemList();
                    if(CollectionUtils.isNotEmpty(isGiftItems)){
                        for(OcBOrderItem ocBOrderItem:isGiftItems){
                            OcBReturnOrderRefund returnOrderRefund = buildReturnOrderRefund(ocBOrderItem, orderInfo.getJingdongRefund());
                            returnOrderRefund.setAmtRefund(ocBOrderItem.getRealAmt());
                            returnOrderRefund.setQtyRefund(ocBOrderItem.getQty());
                            returnOrderRefunds.add(returnOrderRefund);
                            getAllSkuSb(allSkuSb, ocBOrderItem);
                        }
                    }
                    if(CollectionUtils.isNotEmpty(orderItemList)){
                        // 计算可退数量
                        BigDecimal qtyCanReturn = OmsOrderQtyCalculateService.getInstance().calQtyCanReturn4Sku(orderItemList);
                        servicecount = servicecount == null?BigDecimal.ZERO:servicecount;
                        if(qtyCanReturn.compareTo(servicecount) < 0){
                            updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), "申请数量大于可退数量", orderInfo.getJingdongRefund());
                            return null;
                        }
                        BigDecimal realAmt = orderItemList.stream().map(OcBOrderItem::getRealAmt).
                                reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal qtySum = orderItemList.stream().map(OcBOrderItem::getQty).
                                reduce(BigDecimal.ZERO, BigDecimal::add);
                        //单价
                        BigDecimal price = realAmt.divide(qtySum, 4, BigDecimal.ROUND_HALF_UP);
                        for(OcBOrderItem orderItem:orderItemList){
                            if(servicecount.compareTo(BigDecimal.ZERO) <= 0){
                                break;
                            }
                            //计算可退数量
                            BigDecimal qtyReturnApply = orderItem.getQtyReturnApply() == null?BigDecimal.ZERO:orderItem.getQtyReturnApply();
                            BigDecimal qty = orderItem.getQty().subtract(qtyReturnApply);
                            BigDecimal amtRefund = BigDecimal.ZERO;
                            BigDecimal qtyRefund = BigDecimal.ZERO;
                            if(qty.compareTo(servicecount) >= 0){
                                amtRefund = price.multiply(servicecount);
                                qtyRefund = servicecount;
                            }else {
                                amtRefund = price.multiply(qty);
                                qtyRefund = qty;
                                servicecount = servicecount.subtract(qty);
                            }
                            OcBReturnOrderRefund returnOrderRefund = buildReturnOrderRefund(orderItem, orderInfo.getJingdongRefund());
                            returnOrderRefund.setAmtRefund(amtRefund);
                            returnOrderRefund.setQtyRefund(qtyRefund);
                            //计算结算金额和结算单价
                            setPriceAndTotPrice(orderItem, returnOrderRefund);
                            returnAmtListTotal = returnAmtListTotal.add(amtRefund);
                            returnOrderRefunds.add(returnOrderRefund);
                            getAllSkuSb(allSkuSb, orderItem);
                        }
                    }

                    /*OcBOrderItem orderItem = orderItems.get(0);
                    if (orderItem != null) {
                        BigDecimal amtRefund = orderItem.getRealAmt().divide(orderItem.getQty(), 2, BigDecimal.ROUND_CEILING);
                        OcBReturnOrderRefund returnOrderRefund = buildReturnOrderRefund(orderItem, orderInfo.getJingdongRefund());
                        returnOrderRefund.setAmtRefund(amtRefund);

                        // @20200811 bug#20832 去实际可退和申请退数量比较，如果实际可退 < 申请退，则抛出异常
                        BigDecimal qtyCanReturn = OmsOrderQtyCalculateService.getInstance().calQtyCanReturn4Sku(Lists.newArrayList(orderItem));

                        if (qtyCanReturn.intValue() < 1) {
                            // 申请数量 < 可退数量
                            log.error("京东退换货单申请数量大于可退数量：{}/{}", jdOrderItem.getId(), jdOrderItem.getSkuId());
                            updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), "申请数量大于可退数量", orderInfo.getJingdongRefund());
                            return null;
                        }

                        returnOrderRefund.setQtyRefund(new BigDecimal(1));
                        //计算结算金额和结算单价
                        setPriceAndTotPrice(orderItem, returnOrderRefund);
                        returnAmtListTotal = amtRefund;
                        returnOrderRefunds.add(returnOrderRefund);
                        getAllSkuSb(allSkuSb, orderItem);
                    }*/
                }
            }
        }
        String allSku = allSkuSb.toString();
        if (StringUtils.isNotEmpty(allSku)) {
            allSku = allSku.substring(0, allSku.length() - 1);
        }
        //汇总退货明细金额
        for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefunds) {
            qtyInstore = qtyInstore.add(returnOrderRefund.getQtyRefund());
        }
        //2.生成退单主表
        OcBReturnOrder returnOrder = buildReturnOrder(order, orderInfo.getJingdongRefund());
        returnOrder.setReturnAmtList(returnAmtListTotal);
        returnOrder.setReturnAmtActual(returnAmtListTotal);
        returnOrder.setAllSku(allSku);
        returnOrder.setQtyInstore(qtyInstore);
        String jointTid = OmsReturnAfterUtil.getJointTid(returnOrderRefunds);
        returnOrder.setTid(jointTid);
        //3.设置退换货关系数据
        OcBReturnOrderRelation returnOrderRelation = new OcBReturnOrderRelation();
        returnOrderRelation.setReturnOrderInfo(returnOrder);
        returnOrderRelation.setOrderRefundList(returnOrderRefunds);
        returnOrderRelations.add(returnOrderRelation);
        return returnOrderRelations;
    }

    /**
     * 设置退货明细结算单价和结算金额
     */
    private void setPriceAndTotPrice(OcBOrderItem ocBOrderItem, OcBReturnOrderRefund refund) {
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + "计算结算单价和结算金额ocBOrderItem=" + JSONObject.toJSONString(ocBOrderItem)
                    + ",refund=" + JSONObject.toJSONString(refund));
        }
        try {
            BigDecimal sgAmt = BigDecimal.ZERO;
            BigDecimal qtyRefund = refund.getQtyRefund();
            BigDecimal qty = ocBOrderItem.getQty();
            BigDecimal realAmt = ocBOrderItem.getRealAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getRealAmt();
            if (realAmt != null && qty != null && qty.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                sgAmt = realAmt.divide(qty, OcBOrderConst.DECIMAL_QTY, BigDecimal.ROUND_HALF_UP);
            }
            BigDecimal settle = ocBOrderItem.getPriceSettle() == null ? sgAmt : ocBOrderItem.getPriceSettle();
            settle = settle.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN ? settle : BigDecimal.ZERO;

            BigDecimal totPrice = settle.multiply(qtyRefund);
            //结算单价
            refund.setPriceSettle(settle);
            //结算金额
            refund.setAmtSettleTot(totPrice);
        } catch (Exception e) {
            log.debug("京东转退货单计算结算单价和金额出错-{}", ExceptionUtil.getMessage(e));
            refund.setPriceSettle(BigDecimal.ZERO);
            refund.setAmtSettleTot(BigDecimal.ZERO);
        }
    }

    /**
     * @param allSkuSb
     * @param orderItem
     * @return void
     * @Description 拼装AllSku信息
     * <AUTHOR>
     * @date 2019/5/30 11:56
     */
    private void getAllSkuSb(StringBuilder allSkuSb, OcBOrderItem orderItem) {
        allSkuSb.append(orderItem.getPsCSkuEcode()).append("(").append(orderItem.getQtyRefund().intValue()).append(")").append(",");
    }

    /**
     * @param orderItem
     * @return com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund
     * @Descroption 生成退单明细数据
     * @Author: 洪艺安
     * @Date 2019/4/29
     */
    private OcBReturnOrderRefund buildReturnOrderRefund(OcBOrderItem orderItem, IpBJingdongRefund ipJingdongRefund) {
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setPrice(orderItem.getPriceList());
        returnOrderRefund.setAmtAdjust(orderItem.getPrice().subtract(orderItem.getPriceList()));
        returnOrderRefund.setQtyCanRefund(orderItem.getQty());
        returnOrderRefund.setQtyIn(new BigDecimal(0));
        returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
        returnOrderRefund.setIsactive("Y");
        returnOrderRefund.setBarcode(orderItem.getBarcode());
        BigDecimal amtRefundSingle = orderItem.getRealAmt().divide(orderItem.getQty(), 2, BigDecimal.ROUND_CEILING);
        returnOrderRefund.setAmtRefundSingle(amtRefundSingle);
        returnOrderRefund.setPsCProId(orderItem.getPsCProId());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setOid(orderItem.getOoid());
        returnOrderRefund.setProductMark("1");
        returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
        returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
        returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
        returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());
        returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
        returnOrderRefund.setSex(orderItem.getSex());
        returnOrderRefund.setPriceList(orderItem.getPriceTag());
        returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
        returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        returnOrderRefund.setTid(orderItem.getTid());
        returnOrderRefund.setRefundBillNo(ipJingdongRefund.getAfsserviceid().toString());
        returnOrderRefund.setIsToAg(AGStatusEnum.INIT.getVal());
        return returnOrderRefund;
    }

    /**
     * @param ocBOrder
     * @param ipJingdongRefund
     * @return com.jackrain.nea.oc.oms.model.table.OcBReturnOrder
     * @Descroption 生成退单主表数据
     * @Author: 洪艺安
     * @Date 2019/4/29
     */
    private OcBReturnOrder buildReturnOrder(OcBOrder ocBOrder, IpBJingdongRefund ipJingdongRefund) {
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        returnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());

        // @20200722 增加单据编码
        returnOrder.setBillNo(BuildSequenceUtil.getInstance().buildReturnBillNo());

        //平台退款单号
        returnOrder.setReturnId(ipJingdongRefund.getAfsserviceid().toString());
        //原始订单编号
        returnOrder.setOrigOrderId(ocBOrder.getId());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());
        //买家昵称
        returnOrder.setBuyerNick(ipJingdongRefund.getCustomerpin());
        //退款创建时间
        returnOrder.setReturnCreateTime(ipJingdongRefund.getAfsapprovedtime());
        //退单状态
        returnOrder.setReturnStatus(TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode());
        //货物退回时间
//        returnOrder.setReturnTime();
        //退款说明
        returnOrder.setReturnDesc(ipJingdongRefund.getReason());
        //物流公司名称
        String expresscompany = ipJingdongRefund.getExpresscompany();
        returnOrder.setCpCLogisticsEname(expresscompany);
        //获取物流公司信息
        LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(expresscompany);
        if (logisticsInfo != null) {
            returnOrder.setCpCLogisticsId(logisticsInfo.getId());
            returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
        }
        //退回物流单号
        returnOrder.setLogisticsCode(ipJingdongRefund.getExpresscode());
        //最后修改时间
        returnOrder.setLastUpdateTime(ipJingdongRefund.getModifydate());
        //退还运费，默认0
        returnOrder.setReturnAmtShip(BigDecimal.ZERO);
        //退还其他费用，默认0
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);
        //申请人姓名
        returnOrder.setReceiveName(ocBOrder.getReceiverName());
        //申请人手机
        returnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());
        //邮编
        returnOrder.setReceiveZip(ocBOrder.getReceiverZip());
        //设置省市区信息
        returnOrder.setReceiverAreaId(ocBOrder.getCpCRegionAreaId());
        returnOrder.setReceiverCityId(ocBOrder.getCpCRegionCityId());
        returnOrder.setReceiverProvinceId(ocBOrder.getCpCRegionProvinceId());
        returnOrder.setReceiverAreaName(ocBOrder.getCpCRegionAreaEname());
        returnOrder.setReceiverCityName(ocBOrder.getCpCRegionCityEname());
        returnOrder.setReceiverProvinceName(ocBOrder.getCpCRegionProvinceEname());
        //售后/售中
        returnOrder.setReturnPhase("售后");
        //是否启用
        returnOrder.setIsactive("Y");
        //原平台单号
        returnOrder.setTid(ocBOrder.getTid());
        //店铺id
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        //获取店铺信息
        CpShop cpShop = queryShopById(ocBOrder.getCpCShopId());
        if (cpShop != null) {
            returnOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
        }
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getOrderSource());
        //发货仓库
        returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        //平台类型
        returnOrder.setPlatform(4);
        //换货金额
        returnOrder.setExchangeAmt(BigDecimal.ZERO);
        //代销结算金额
        returnOrder.setConsignAmtSettle(ocBOrder.getConsignAmt());
        //是否传AG默认否
        returnOrder.setIsToag(AGStatusEnum.INIT.getVal());
        //是否生成调拨单，默认0
        returnOrder.setIsTransfer(0);
        //是否生成零售，默认0
        returnOrder.setIsTodrp(0);
        //是否手工新增，默认0
        returnOrder.setIsAdd(0);
        //虚拟入库状态，默认0
        returnOrder.setInventedStatus(0);
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        //是否原退，默认0
        returnOrder.setIsRefund(0);
        //是否确认收货，默认0
        returnOrder.setIsReceiveConfirm(0);
        //WMS撤回状态，默认0
        returnOrder.setWmsCancelStatus(0);
        //强制入库，默认0
        returnOrder.setIsForce(0);
        //是否手工审核，默认0
        returnOrder.setIsManualAudit(0);
        //是否传WMS
        returnOrder.setIsTowms(0);
        //是否入仓成功
        returnOrder.setIsInstorage(0);
        //是否有运单号
        if (StringUtils.isEmpty(returnOrder.getLogisticsCode())) {
            returnOrder.setIsNotlogmber(0);
        } else {
            returnOrder.setIsNotlogmber(1);
        }
        returnOrder.setOrigSourceCode(ocBOrder.getSourceCode());
        returnOrder.setReceiveAddress(ipJingdongRefund.getPickwareaddress());
        //换货不需要地址2019-08-07修改
        //returnOrderTransferUtil.returnOrderAddress(returnOrder, ipJingdongRefund.getPickwareaddress());

        // @20200801 京东退货入库仓选择店铺策略中的默认入库仓，如果取值不到，则留空
        //取值为发货实体仓档案中关联的退货待检实体仓仓库
        // returnOrder.setCpCPhyWarehouseInId(this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId()));
        //业务类型
        StCBusinessType resultType = omsRefundOrderService.queryReturnOrderType(ocBOrder);
        returnOrder.setBusinessTypeId(resultType.getId());
        returnOrder.setBusinessTypeCode(resultType.getEcode());
        returnOrder.setBusinessTypeName(resultType.getEname());
        returnOrder.setIsWrongReceive(IsWrongReceive.NO.val());
        setReturnWarehouse(returnOrder);
        return returnOrder;
    }

    /**
     * 设置退货实体仓
     *
     * @param returnOrder
     */
    private void setReturnWarehouse(OcBReturnOrder returnOrder) {
        if (Objects.nonNull(returnOrder)) {
            // 从店铺策略查入库实体仓
            if (Objects.nonNull(returnOrder.getCpCShopId())) {
                StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(returnOrder.getCpCShopId());

                if (Objects.nonNull(shopStrategy)) {
                    Integer isMultiReturnWarehouse = shopStrategy.getIsMultiReturnWarehouse();

                    // @20200729 如果为空，则认为没指定，则为否（可能策略维护没维护默认值，但是如果为是，则需要界面控件打钩，即一定会有值）
                    if (Objects.isNull(isMultiReturnWarehouse) || isMultiReturnWarehouse == 0) {
                        Long cpCWarehouseDefId = shopStrategy.getCpCWarehouseDefId();

                        returnOrder.setCpCPhyWarehouseInId(cpCWarehouseDefId);

                        // 设置是否发送wms
                        boolean isNeedToWms = isNeedToWmsByWarehouseId(cpCWarehouseDefId);

                        if (isNeedToWms) {
                            returnOrder.setIsNeedToWms(Long.valueOf(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal()));
                        }
                    }
                }
            }
        }
    }

    /**
     * 判断仓库控制是否需要发送WMS
     *
     * @param warehouseId
     * @return
     */
    private boolean isNeedToWmsByWarehouseId(Long warehouseId) {
        boolean isNeed = false;

        if (Objects.nonNull(warehouseId)) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(warehouseId);
            isNeed = Objects.nonNull(cpCPhyWarehouse) && Objects.nonNull(cpCPhyWarehouse.getWmsControlWarehouse()) && cpCPhyWarehouse.getWmsControlWarehouse() == 1;
        }

        return isNeed;
    }

    /**
     * 通过实体仓id查询该实体仓的退货仓id
     */
//    private Long selectReturnCPhyWarehouse(Long cPhyWarehouseId) {
//        if (cPhyWarehouseId != null) {
//            CpCPhyWarehouse cpCPhyWarehouse = cpRpcExtService.queryByWarehouseId(cPhyWarehouseId);
//            if (cpCPhyWarehouse != null) {
//                return cpCPhyWarehouse.getReturnPhyWarehouseId();
//            }
//        }
//        return null;
//    }

    /**
     * @param id
     * @return com.jackrain.nea.cpext.model.table.CpShop
     * @Descroption 根据店铺id查询店铺信息
     * @Author: 洪艺安
     * @Date 2019/5/23
     */
    public CpShop queryShopById(Long id) {
        CpShop cpShop = null;
        try {
            cpShop = cpRpcService.selectShopById(id);
        } catch (Exception e) {
            String errMsg = "调用根据店铺id查询店铺信息RPC接口失败";
            log.error(errMsg);
            throw new NDSException(errMsg);
        }
        return cpShop;
    }


    /**
     * 京东退单生成已发货退款单
     *
     * @param returnId
     * @param orderInfo
     * @param jingdongRefund
     * @param user
     */
    public void foundRefundSlipAfterNoUpdate(Long returnId, IpJingdongRefundRelation orderInfo, IpBJingdongRefund jingdongRefund,
                                             User user) {
        //获取所有的退单明细数据
        try {
            if (!omsRefundOrderService.isRefundSlipAfExist(jingdongRefund.getAfsserviceid() + "")) {
                //ocBReturnBfSendMapper.updateOcBReturnBfSend(jingdongRefund.getAfsservicestatusname(), jingdongRefund.getAfsserviceid() + "");
                return;
            }
            OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByid(returnId);
            if (returnOrder != null) {
                List<OcBOrderItem> ocBOrderItems = orderInfo.getOcBOrderItems();
                //排除退款完成的明细
                List<OcBOrderItem> orderItems = ocBOrderItems.stream().filter(x -> x.getRefundStatus() == null || OcOrderRefundStatusEnum.SUCCESS.getVal() != x.getRefundStatus().intValue()).collect(Collectors.toList());
                List<OcBReturnOrderRefund> orderRefunds =
                        ocBReturnOrderRefundMapper.selectByOcOrderId(returnId);
                //获取发货单主表数据
                OcBReturnAfSendRelation ocBReturnAfSendRelation = omsDeliveredRefundFormUtil.jDRefundAfSendToReturn(orderRefunds, orderInfo.getOcBOrder(),
                        jingdongRefund, user, orderItems, returnOrder);
                omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation,user);
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 生成退款单异常", e);
            throw new NDSException(e);
        }

    }


    /**
     * 京东退单生成已发货退款单
     ** @param ocBOrder
     * @param jingdongRefund
     * @param user
     */
    public void foundRefundSlipAfterNoUpdate(IpJingdongRefundRelation orderInfo,IpBJingdongRefund jingdongRefund,
                                             User user) {
        //获取所有的退单明细数据
        try {
            if (!omsRefundOrderService.isRefundSlipAfExist(jingdongRefund.getAfsserviceid() + "")) {
                return;
            }
            OcBOrder ocBOrder = orderInfo.getOcBOrder();
            List<OcBOrderItem> ocBOrderItems = orderInfo.getOcBOrderItems();
            //排除退款完成的明细
            List<OcBOrderItem> orderItems = ocBOrderItems.stream().filter(x -> x.getRefundStatus() == null || OcOrderRefundStatusEnum.SUCCESS.getVal() != x.getRefundStatus().intValue()).collect(Collectors.toList());
            //获取发货单主表数据
            OcBReturnAfSendRelation ocBReturnAfSendRelation = omsDeliveredRefundFormUtil.jDBulidRefundAfSendToReturn(ocBOrder,
                    jingdongRefund, user, orderItems);
            omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation,user);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 生成退款单异常", e);
            throw new NDSException(e);
        }

    }

}
