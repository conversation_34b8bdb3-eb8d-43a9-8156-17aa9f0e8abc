package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsPhyStorageOutRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.request.CpShopQueryRequest;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.request.OmsOcBOrder;
import com.jackrain.nea.oc.oms.model.request.OmsOcBOrderItem;
import com.jackrain.nea.oc.oms.model.request.OmsOcBOrderRequest;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/11/4 2:17 下午
 * @Version 1.0
 * 订单新增
 */
@Slf4j
@Component
public class OmsOrderAddService {

    @Autowired
    private RegionNewService regionService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private UpdateOrderInfoService infoService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    protected PsRpcService psRpcService;
    @Autowired
    private PropertiesConf propertiesConf;
    @Autowired
    private OmsOrderAddService omsOrderAddService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private BasicCpQueryService basicCpQueryService;
    @Autowired
    private BasicPsQueryService basicPsQueryService;
    @Reference(group = "cp-ext", version = "1.0")
    private CpShopQueryCmd cpShopQueryCmd;

    public ValueHolderV14 omsOrderAdd(OmsOcBOrderRequest request) {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        try {
            if (request == null) {
                valueHolder.setCode(-1);
                valueHolder.setMessage("参数为空");
                return valueHolder;
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("新增订单入参:{}"), JSONObject.toJSONString(request));
            }
            //一件代发生成零售发货单数据处理
            if (request.getOcBOrder() != null && Integer.valueOf(1).equals(request.getOcBOrder().getIsOneDistribution())){
                String error = buildParam(request);
                if (StringUtils.isNotEmpty(error)){
                    valueHolder.setCode(-1);
                    valueHolder.setMessage(error);
                    return valueHolder;
                }
            }
            String parameter = this.checkOrderParameter(request);
            if (StringUtils.isNotEmpty(parameter)) {
                valueHolder.setCode(-1);
                valueHolder.setMessage(parameter);
                return valueHolder;
            }
            OmsOcBOrder ocBOrder = request.getOcBOrder();
            List<OmsOcBOrderItem> omsOcBOrderItemList = request.getOmsOcBOrderItemList();
            User user = request.getUser();
            OcBOrder ocBOrder1 = this.buildOcBOrderFromIpTaobaoOrder(ocBOrder);
            this.buildUserData(ocBOrder1, user);
            List<OcBOrderItem> orderItems = new ArrayList<>();
            for (OmsOcBOrderItem omsOcBOrderItem : omsOcBOrderItemList) {
                OcBOrderItem item = this.buildOrderItemFromTaobaoOrderItemNew(ocBOrder1, omsOcBOrderItem,ocBOrder.getIsOneDistribution());
                this.buildUserData(item, user);
                orderItems.add(item);
            }
            //计算主表的商品金额,优惠金额
            BigDecimal productAmt = orderItems.stream().map(p ->
                    p.getQty().multiply(p.getPriceList())
            ).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            BigDecimal prodDiscountAmt = orderItems.stream().map(OcBOrderItem::getAmtDiscount).
                    reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal count = orderItems.stream().map(OcBOrderItem::getQty).
                    reduce(BigDecimal.ZERO, BigDecimal::add);

            ocBOrder1.setOrderAmt(productAmt.subtract(prodDiscountAmt));
            ocBOrder1.setReceivedAmt(productAmt.subtract(prodDiscountAmt));
            ocBOrder1.setQtyAll(count);
            ocBOrder1.setSkuKindQty(new BigDecimal(orderItems.size()));
            ocBOrder1.setProductAmt(productAmt);
            ocBOrder1.setProductDiscountAmt(productAmt.subtract(prodDiscountAmt));
            //商品优惠金额.
            ocBOrder1.setProductDiscountAmt(prodDiscountAmt);

            //如果是云仓单，指定实体仓占用库存，占用失败则新增失败
            if(NumberUtils.INTEGER_ONE.equals(ocBOrder.getIsDrp())){
                SgOmsPhyStorageOutRequest sgOmsPhyStorageOutItemRequest = infoService.buildSgOmsPhyStorageOutRequest(
                        ocBOrder1,orderItems
                        // ,ocBOrder1.getDeliveryStoreId(),"",
                         ,null,"",
                        ocBOrder1.getCpCPhyWarehouseId(),ocBOrder1.getCpCPhyWarehouseEcode());
                ValueHolderV14 v14 =sgRpcService.phyStorageOut(sgOmsPhyStorageOutItemRequest);
                AssertUtil.assertException(!v14.isOK(),v14.getMessage());

                //修改订单状态
                ocBOrder1.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                //还原发货仓
               // ocBOrder1.setDeliveryStoreId(ocBOrder.getDeliveryStoreId());
            }

            omsOrderAddService.saveOrderData(ocBOrder1, orderItems);
            valueHolder.setCode(0);
            valueHolder.setMessage("新增成功!");
        } catch (Exception e) {
            log.error(LogUtil.format("新增订单异常,error:{}"), Throwables.getStackTraceAsString(e));
            valueHolder.setCode(-1);
            valueHolder.setMessage("新增订单失败!" + e.getMessage());
        }
        return valueHolder;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrderData(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItem) {
        omsOrderService.saveOrderInfo(ocBOrder);
        for (OcBOrderItem item : ocBOrderItem) {
            ocBOrderItemMapper.insert(item);
        }
//        try {
//            SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
//                    OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, ocBOrder, ocBOrder.getId());
//            SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
//                    OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, ocBOrderItem,
//                    "OC_B_ORDER_ID");
//        } catch (Exception e) {
//            log.error(this.getClass().getName() + " 订单新增ES失败", e);
//
//        }
    }


    private void buildUserData(BaseModel baseModel, User user) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        baseModel.setAdClientId((long) user.getClientId());//所属公司
        baseModel.setAdOrgId((long) user.getOrgId());//所属组织
        baseModel.setOwnerid(Long.valueOf(user.getId()));//创建人id
        baseModel.setCreationdate(timestamp);//创建时间
        baseModel.setOwnername(user.getName());//创建人用户名
        baseModel.setModifierid(Long.valueOf(user.getId()));//修改人id
        baseModel.setModifiername(user.getName());//修改人用户名
        baseModel.setModifieddate(timestamp);//修改时间
        baseModel.setIsactive("Y");//是否启用
    }

    private String buildParam(OmsOcBOrderRequest request) {
        String error = null;
        OmsOcBOrder ocBOrder = request.getOcBOrder();

        //根据店铺ECODE查询店铺信息
        String cpCShopEcode = ocBOrder.getCpCShopEcode();

        if (StringUtils.isNotEmpty(cpCShopEcode)){
            CpShopQueryRequest shopQueryRequest = new CpShopQueryRequest();
            List<String> shopList = new ArrayList<>();
            shopList.add(cpCShopEcode);
            shopQueryRequest.setShopCodes(shopList);
            List<CpShop> cpShops = cpShopQueryCmd.queryShop(shopQueryRequest);
            if (CollectionUtils.isEmpty(cpShops)){
                error = "根据店铺编码查询无可用记录，请检查！";
                return error;
            }
            //店铺ID
            ocBOrder.setCpCShopId(cpShops.get(0).getId() != null ? cpShops.get(0).getId() : cpShops.get(0).getCpCShopId());
            ocBOrder.setCpCShopEcode(cpShops.get(0).getEcode());
            //平台
            ocBOrder.setPlatform(Math.toIntExact(cpShops.get(0).getCpCPlatformId()));
            ocBOrder.setCpCShopTitle(cpShops.get(0).getCpCShopTitle());
            ocBOrder.setCpCShopSellerNick(cpShops.get(0).getSellerNick());
            //根据店铺编码查询逻辑仓
            StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
            List<String> ecodeList = new ArrayList<>();
            ecodeList.add(cpCShopEcode);
            storeInfoQueryRequest.setEcodes(ecodeList);
            HashMap<String, CpCStore> storeHashMap = basicCpQueryService.getStoreInfoByEcode(storeInfoQueryRequest);
            CpCStore cpCStore = storeHashMap.get(cpCShopEcode);
            if (cpCStore != null){
                ocBOrder.setCpCStoreId(cpCStore.getId());
                ocBOrder.setCpCStoreEcode(cpCStore.getEcode());
            }
        }else {
            error = "店铺编码不能为空！";
            return error;
        }
        return error;
    }

    /**
     * 检验入参是否正常
     *
     * @param request
     * @return
     */
    private String checkOrderParameter(OmsOcBOrderRequest request) {
        OmsOcBOrder ocBOrder = request.getOcBOrder();
        List<OmsOcBOrderItem> omsOcBOrderItemList = request.getOmsOcBOrderItemList();
        StringBuilder errMessage = new StringBuilder();
        if (ocBOrder == null) {
            errMessage.append("订单主表对象不能为空!");
            return errMessage.toString();
        }
        if (CollectionUtils.isEmpty(omsOcBOrderItemList)) {
            errMessage.append("订单明细表对象不能为空!");
            return errMessage.toString();
        }
        if (request.getUser() == null) {
            errMessage.append("用户信息不能为空!");
            return errMessage.toString();
        }
        if (StringUtils.isEmpty(ocBOrder.getTid())) {
            errMessage.append("平台单号不能为空!");
            return errMessage.toString();
        }
        if (!Integer.valueOf(1).equals(ocBOrder.getIsOneDistribution()) && ocBOrder.getCpCStoreId() == null) {
            errMessage.append("门店ID不能为空!");
        }
        if (Integer.valueOf(1).equals(ocBOrder.getIsDrp()) && ocBOrder.getDeliveryStoreId() == null) {
            errMessage.append("发货门店ID不能为空!");
        }
        if (StringUtils.isEmpty(ocBOrder.getCpCRegionProvinceEname())) {
            errMessage.append("省名称不能为空!");
        }
        if (StringUtils.isEmpty(ocBOrder.getCpCRegionCityEname())) {
            errMessage.append("市名称不能为空!");
        }
        if (StringUtils.isEmpty(ocBOrder.getCpCRegionCityEname())) {
            errMessage.append("市名称不能为空!");
        }
        if (Integer.valueOf(1).equals(ocBOrder.getIsOneDistribution()) &&  ocBOrder.getCpCRegionAreaEname() == null) {
            errMessage.append("区名称不能为空!");
        }
        if (StringUtils.isEmpty(ocBOrder.getReceiverName())) {
            errMessage.append("买家名称不能为空!");
        }
        if (Integer.valueOf(1).equals(ocBOrder.getIsOneDistribution()) &&  ocBOrder.getPay_time() == null) {
            errMessage.append("付款时间不能为空!");
        }
        if (StringUtils.isEmpty(ocBOrder.getReceiverAddress())) {
            errMessage.append("买家详细地址不能为空!");
        }
        if (StringUtils.isEmpty(ocBOrder.getReceiverMobile())) {
            errMessage.append("买家手机不能为空!");
        }
        if (!Integer.valueOf(1).equals(ocBOrder.getIsDrp()) && ocBOrder.getPlatform() == null) {
            errMessage.append("平台不能为空!");
        }
        for (OmsOcBOrderItem item : omsOcBOrderItemList) {
            if (!Integer.valueOf(1).equals(request.getOcBOrder().getIsOneDistribution())){
                if (StringUtils.isEmpty(item.getPsCSkuEcode())) {
                    errMessage.append("条码code不能为空!");
                }
                if (item.getQty() == null || item.getQty().compareTo(BigDecimal.ZERO) == 0) {
                    errMessage.append("数量不能为空!");
                }
                if (item.getPriceList() == null || item.getPriceList().compareTo(BigDecimal.ZERO) == 0) {
                    errMessage.append("标准价不能为空!");
                }
            }else {
                if (StringUtils.isEmpty(item.getPsCSkuEcode())) {
                    errMessage.append("条码code不能为空!");
                }
                if (item.getQty() == null || item.getQty().compareTo(BigDecimal.ZERO) == 0) {
                    errMessage.append("数量不能为空!");
                }
                if (item.getRealAmt() == null || item.getRealAmt().compareTo(BigDecimal.ZERO) == 0) {
                    errMessage.append("成交金额不能为空!");
                }

            }
        }
        return errMessage.toString();
    }

    /**
     * 封装订单对象
     *
     * @param omsOcBOrder
     * @return
     */
    private OcBOrder buildOcBOrderFromIpTaobaoOrder(OmsOcBOrder omsOcBOrder) throws Exception {
        OcBOrder order = new OcBOrder();
        //id自增长
        order.setId(sequenceUtil.buildOrderSequenceId());
        //调整金额
        order.setAdjustAmt(BigDecimal.ZERO);
        //所有SKU（最多五个超过，显示数量）。转单赋值空
        order.setAllSku(null);
        //应收金额. =订单金额
        order.setReceivedAmt(null);
        //审核时间
        order.setAuditTime(null);
        //自动审核状态
        order.setAutoAuditStatus(0);

        if(!Integer.valueOf(1).equals(omsOcBOrder.getIsDrp())){
            //平台
            order.setPlatform(omsOcBOrder.getPlatform());
            //单据编号
            order.setBillNo(sequenceUtil.buildBillNo());
        }else {
            order.setBillNo(omsOcBOrder.getBillNo());
        }
        //买家邮箱
        //order.setBuyerEmail(omsOcBOrder.getBuyerEmail());
        //买家留言
        order.setBuyerMessage(omsOcBOrder.getBuyerMessage());
        //到付代收金额. 如果是支付方式=到付，则赋值订单金额
        order.setCodAmt(BigDecimal.ZERO);
        //代销结算金额. 默认为0
        order.setConsignAmt(BigDecimal.ZERO);
        //代销运费. 默认为0
        order.setConsignShipAmt(BigDecimal.ZERO);
        // 处理解析物流公司信息
        //this.parseLogisticsInfo(tbOrder, order);

        if(!Integer.valueOf(1).equals(omsOcBOrder.getIsDrp())){
            //发货实体仓. 赋值null，后续在分配物流中使用
            order.setCpCPhyWarehouseId(null);
        }
        //下单店铺id.
        // TODO: 需要按照云店类型进行赋值。现在还没有云店类型。暂时不进行判断赋值
        //下单店铺标题。需要查个表获取Title（平台店铺信息表）
        //平台店铺标题
        if (!Integer.valueOf(1).equals(omsOcBOrder.getIsOneDistribution())){
            StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
            List<Long> ids = new ArrayList<>();
            Long storeId = omsOcBOrder.getCpCStoreId();
            ids.add(storeId);
            if(!ObjectUtils.isEmpty(omsOcBOrder.getDeliveryStoreId())){
                ids.add(omsOcBOrder.getDeliveryStoreId());
            }
            storeInfoQueryRequest.setIds(ids);
            HashMap<Long, CpCStore> storeInfo = basicCpQueryService.getStoreInfo(storeInfoQueryRequest);
            if (storeInfo == null || null == storeInfo.get(storeId)) {
                throw new NDSException("门店id=" + storeId + "不存在");
            }

            if(!ObjectUtils.isEmpty(omsOcBOrder.getDeliveryStoreId())){
                AssertUtil.assertException(!storeInfo.containsKey(omsOcBOrder.getDeliveryStoreId()),
                        "发货门店id=" + omsOcBOrder.getDeliveryStoreId() + "不存在");
                CpCStore deliveryStore = storeInfo.get(omsOcBOrder.getDeliveryStoreId());
                AssertUtil.assertException(ObjectUtils.isEmpty(deliveryStore.getCpCPhyWarehouseId()),
                        "发货门店id=" + omsOcBOrder.getDeliveryStoreId() + "未维护关联实体仓库");
                order.setCpCPhyWarehouseId(deliveryStore.getCpCPhyWarehouseId());
                order.setCpCPhyWarehouseEcode(deliveryStore.getCpCPhyWarehouseEcode());
                order.setCpCPhyWarehouseEname(deliveryStore.getCpCPhyWarehouseEname());

                AssertUtil.assertException(ObjectUtils.isEmpty(deliveryStore.getCpCShopId()),
                        "发货门店id=" + storeId + "对应的平台店铺为空");
                CpShop shopInfo = cpRpcService.selectShopById(deliveryStore.getCpCShopId());
                AssertUtil.assertException(ObjectUtils.isEmpty(shopInfo),
                        "平台店铺id=" + deliveryStore.getCpCShopId() + "不存在");
                // order.setDeliveryStoreId(shopInfo.getSgCShareStoreId());
            }

            CpCStore cpCStore = storeInfo.get(storeId);
            Long shopId = cpCStore.getCpCShopId();
            if (shopId == null) {
                throw new NDSException("门店id=" + storeId + "对应的平台店铺为空");
            }
            CpShop shopInfo = cpRpcService.selectShopById(shopId);
            if (shopInfo != null) {
                order.setCpCShopEcode(shopInfo.getEcode());
                order.setCpCShopId(shopId);
                order.setCpCShopTitle(shopInfo.getCpCShopTitle());
                //下单店仓编码. 到平台店铺信息表中获取下单店仓字段ID值；
//                order.setCpCStoreEcode(cpCStore.getEcode());
                //下单店仓名称. 到平台店铺信息表中获取下单店仓字段ID值；
//                order.setCpCStoreEname(cpCStore.getEname());
                //下单店仓id. 到平台店铺信息表中获取下单店仓字段ID值；
//                order.setCpCStoreId(cpCStore.getId());
                //下单店仓id. 到平台店铺信息表中获取下单卖家店铺名称
                order.setCpCShopSellerNick(shopInfo.getSellerNick());
                if(!ObjectUtils.isEmpty(shopInfo.getCpCPlatformId())){
                    order.setPlatform(shopInfo.getCpCPlatformId().intValue());
                }
            } else {
                // 20190727修改：如果 平台店铺不存在，则不再继续保持。而是抛出异常，不允许转单操作
                throw new NDSException("平台店铺id=" + omsOcBOrder.getCpCShopId() + "不存在");
            }
        }else {
            order.setCpCShopId(omsOcBOrder.getCpCShopId());
            order.setCpCShopEcode(omsOcBOrder.getCpCShopEcode());
            order.setCpCShopTitle(omsOcBOrder.getCpCShopTitle());
            order.setCpCShopSellerNick(omsOcBOrder.getCpCShopSellerNick());
//            order.setCpCStoreId(omsOcBOrder.getCpCStoreId());
//            order.setCpCStoreEcode(omsOcBOrder.getCpCStoreEcode());
        }
        //配货时间. 配货阶段进行赋值
        order.setDistributionTime(null);

        //物流编码. 分配物流后进行赋值
        order.setExpresscode(null);
        //内部备注。后续手动填写
        order.setInsideRemark(null);
        //开票内容
        order.setInvoiceContent(null);
        //商品计算重量。是否需要进行称重。根据系统配置来进行赋值。可能没用。产品也不知道
        order.setIsCalcweight(0);

        order.setIsCombination(0);
        //是否生成开票通知。现在赋值为N。占用订单后再进行赋值
        order.setIsGeninvoiceNotice(0);
        // 是否已给物流。占单后再进行赋值
        // order.setIsGiveLogistic(0);
        // 是否有赠品.0.否。计算完赠品策略赋值
        // TODO: 后期需要根据逻辑判断进行赋值
        order.setIsHasgift(0);
        //包含预售商品
        // order.setIsHaspresalesku(0);
        //是否退款中
        order.setIsInreturning(0);
        //是否已经拦截
        order.setIsInterecept(0);

        order.setIsInvoice(0);

        //是否虚拟订单。现在赋值为N
        order.setIsInvented(0);
        //京仓订单
        order.setIsJcorder(0);
        //实缺标记
//        order.setIsLackstock(0);
        //是否合并订单 默认0不合并
        order.setIsMerge(0);
        //是否拆分订单
        order.setIsSplit(0);
        //是否生成调拨零售
        // order.setIsTodrp(0);
        //应收平台金额（京东）
        order.setJdReceiveAmt(BigDecimal.ZERO);
        //京东结算金额
        order.setJdSettleAmt(BigDecimal.ZERO);
        //物流成本.需要计算成本。默认为0
        order.setLogisticsCost(BigDecimal.ZERO);
        //合并单据后生成的订单，对原始数据进行修改
        order.setMergeOrderId(null);
        //合并单据后生成的订单，对原始数据进行修改
        order.setMergeSourceCode(omsOcBOrder.getTid());
        //订单占单状态
        order.setOccupyStatus(0);
        //操作费.默认为0
        // order.setOperateAmt(BigDecimal.ZERO);
        //下单时间
        order.setOrderDate(new Date());
        order.setPayTime(omsOcBOrder.getPay_time());
        //订单优惠金额。
        //order.setOrderDiscountAmt(tbOrder.getDiscountFee());
        //订单旗帜
        //order.setOrderFlag(tbOrder.getSellerFlag());
        //订单来源
        // order.setOrderSource(tbOrder.getTradeFrom());

        order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());

        //订单标签
        order.setOrderTag(null);
        //订单类型
        order.setOrderType(OrderTypeEnum.NORMAL.getVal());
        //原始订单号。默认空
        order.setOrigOrderId(null);
        //原始退货单号
        order.setOrigReturnOrderId(null);
        //出库状态. WMS后调用,已出库未出库,现在没有用
        order.setOutStatus(null);
        //付款时间
        // order.setPayTime(tbOrder.getPayTime());
        //支付方式（淘宝，天猫没有货到付款类型。PRD中写到COD=货到付款的判断可用忽视）
        order.setPayType(OmsPayType.ON_LINE_PAY.toInteger());

        // 双11的预售状态。现在暂时赋值0
        order.setDouble11PresaleStatus(0);

        order.setReceivedAmt(BigDecimal.ZERO);
        // 买家收货详细地址
        order.setReceiverAddress(omsOcBOrder.getReceiverAddress());
        //买家所在省
        String provinceName = omsOcBOrder.getCpCRegionProvinceEname();
        //买家所在市
        String cityName = omsOcBOrder.getCpCRegionCityEname();
        //买家所在区ID。
        String areaName = omsOcBOrder.getCpCRegionAreaEname();

        ProvinceCityAreaInfo provinceCityAreaInfo = this.regionService.selectProvinceCityAreaInfo(provinceName,
                cityName, areaName);
        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getProvinceInfo() != null) {
            order.setCpCRegionProvinceId(provinceCityAreaInfo.getProvinceInfo().getId());
            order.setCpCRegionProvinceEcode(provinceCityAreaInfo.getProvinceInfo().getCode());
            order.setCpCRegionProvinceEname(provinceName);
        } else {
            order.setCpCRegionCityId(null);
            order.setCpCRegionAreaEcode(null);
        }

        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getCityInfo() != null) {
            order.setCpCRegionCityId(provinceCityAreaInfo.getCityInfo().getId());
            order.setCpCRegionCityEcode(provinceCityAreaInfo.getCityInfo().getCode());
            order.setCpCRegionCityEname(provinceCityAreaInfo.getCityInfo().getName());
        } else {
            order.setCpCRegionCityId(null);
            order.setCpCRegionAreaEcode(null);
        }
        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getAreaInfo() != null) {
            order.setCpCRegionAreaId(provinceCityAreaInfo.getAreaInfo().getId());
            order.setCpCRegionAreaEcode(provinceCityAreaInfo.getAreaInfo().getCode());
            order.setCpCRegionAreaEname(provinceCityAreaInfo.getAreaInfo().getName());
        } else {
            order.setCpCRegionAreaId(null);
            order.setCpCRegionAreaEcode(null);
        }
        //order.setReceiverEmail(omsOcBOrder.getBuyerEmail());
        order.setReceiverMobile(omsOcBOrder.getReceiverMobile());
        order.setReceiverName(omsOcBOrder.getReceiverName());
        order.setReceiverPhone(omsOcBOrder.getReceiverPhone());

        order.setReceiverZip(omsOcBOrder.getReceiverZip());
        //退款审核状态（AG使用）
        order.setRefundConfirmStatus(null);
        //退货状态
        order.setReturnStatus(null);
        //销售员编号
        order.setSalesmanId(null);
        //销售员名称
        order.setSalesmanName(null);
        //扫描出库时间.WMS回传值
        order.setScanTime(null);
        //卖家备注
        order.setSellerMemo(omsOcBOrder.getSellerMemo());
        //平台预售活动。暂时用不到。有可能是平台传输过来
        // order.setSendTime(null);
        //货到付款服务费。如果为空，则赋值0.
        order.setServiceAmt(BigDecimal.ZERO);
        //配送费用。如果为空，则赋值0.
        order.setShipAmt(BigDecimal.ZERO);
        //平台单号信息
        order.setSourceCode(omsOcBOrder.getTid());
        //拆分原单单号
        order.setSplitOrderId(null);
        //订单补充信息
        order.setSuffixInfo(null);
        //系统备注
        order.setSysremark(null);
        //淘宝店铺编号（星盘使用）
        order.setTbStorecode(null);
        //初始平台单号（确定唯一）
        order.setTid(omsOcBOrder.getTid());
        //订单唯一码，通过明细tid、oid、sku+头表订单补充信息字段生成
        //order.setUniqueKey(this.buildUniqueKey(taobaoOrderRelation));
        //下单用户
        order.setUserId(null);

        //order.setUserNick(omsOcBOrder.getBuyerNick());
        //版本信息
        order.setVersion(0L);
        //商品重量
        order.setWeight(BigDecimal.ZERO);
        //wms撤回状态调用WMS撤回是否成功。1=成功；2=失败
        order.setWmsCancelStatus(0);
        //仓储状态（拣货中，已打印，已装箱）
        //order.setWmsStatus(null);
        //是否插入核销流水
        // order.setIsWriteoff(0);
        //出库状态
        order.setOutStatus(1);
        //支付宝交易账号
        //order.setAlipayNo(tbOrder.getAlipayNo());
        //买家支付账号
        //order.setBuyerAlipayNo(tbOrder.getBuyerAlipayNo());
        //订单总额.“商品总额”+“物流费用”+“调整金额”-“订单优惠金额”-“商品优惠金额”
        order.setPosBillId(omsOcBOrder.getPosId());
        return order;
    }

    private OcBOrderItem buildOrderItemFromTaobaoOrderItemNew(OcBOrder ocBOrder, OmsOcBOrderItem omsOcBOrderItem, Integer isOneDistribution) {
        ProductSku skuInfo = psRpcService.selectProductSku(omsOcBOrderItem.getPsCSkuEcode());
        if (skuInfo == null) {
            throw new NDSException("商品信息不存在!");
        }
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(item);

        //活动编号. 默认赋值为null
        item.setActiveId(null);
        // 若不为组合商品，则【淘宝订单中间表】明细表的“调整金额”
        //item.setAdjustAmt(taobaoOrderItem.getAdjustFee());
//        item.setAmtDiscount(BigDecimal.ZERO);
        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //国标码。SKU 69码。从条码档案中有一个69码字段

        item.setBarcode(skuInfo.getBarcode69());

        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        item.setIsGift(0);
        //实缺标记
        item.setIsLackstock(0);
        //预售状态
        item.setIsPresalesku(0);
        //订单编号
        item.setOcBOrderId(ocBOrder.getId());
        //子订单编号(明细编号)
        //liqb 更改ooid类型从Long类型改成String类型
        if (null != omsOcBOrderItem.getSourceId()) {
            item.setOoid(String.valueOf(omsOcBOrderItem.getSourceId()));
        }
        //整单平摊金额
        item.setOrderSplitAmt(BigDecimal.ZERO);
        if (!Integer.valueOf(1).equals(isOneDistribution)){
            BigDecimal actualPrice = (omsOcBOrderItem.getPriceList().multiply(omsOcBOrderItem.getQty()).
                    subtract(omsOcBOrderItem.getAmtDiscount())).divide(omsOcBOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP);
            item.setPrice(omsOcBOrderItem.getPriceList());
            item.setPriceActual(actualPrice);
            item.setPriceList(omsOcBOrderItem.getPriceList());
            item.setRealAmt(actualPrice.multiply(omsOcBOrderItem.getQty()));
            item.setAmtDiscount(omsOcBOrderItem.getAmtDiscount());
        }else {
            //一件代发
            BigDecimal price = skuInfo.getPrice();
            item.setPrice(price);
            item.setPriceActual(omsOcBOrderItem.getRealAmt().divide(omsOcBOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
            item.setAmtDiscount((item.getPrice().subtract(item.getPriceActual())).multiply(omsOcBOrderItem.getQty()));
            item.setPriceList(price);
            item.setRealAmt(omsOcBOrderItem.getRealAmt());
        }

        //数量
        item.setQty(omsOcBOrderItem.getQty());
        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        //退款状态
        // 如果是退款完成，或者是交易关闭 状态=6
        item.setRefundStatus(0);

        //规格。商品条码. normsdetailnames
        initialTaobaoOrderItem(skuInfo, item);

        //库位。不用赋值
//        item.setStoreSite(null);
        //标题
        //item.setTitle(taobaoOrderItem.getTitle());

        item.setTid(ocBOrder.getTid());
        return item;
    }


    /**
     * 初始化TaobaoOrderItem内容
     * 2019-07-30 组合福袋商品修改
     */

    private void initialTaobaoOrderItem(ProductSku skuInfo, OcBOrderItem item) {
        if (skuInfo != null) {
            item.setPsCProId(skuInfo.getProdId());
            // ProECode
            item.setPsCProEcode(skuInfo.getProdCode());
            item.setPsCSkuId(skuInfo.getId());
            item.setSex(skuInfo.getSex());
            //2019-08-30吊牌价改为取商品表数据
            item.setPriceTag(skuInfo.getPricelist()); //吊牌价
            item.setPsCClrEcode(skuInfo.getColorCode());
            item.setPsCClrEname(skuInfo.getColorName());
            item.setPsCClrId(skuInfo.getColorId());
            item.setPsCSizeEcode(skuInfo.getSizeCode());
            item.setPsCSizeEname(skuInfo.getSizeName());
            item.setPsCSizeId(skuInfo.getSizeId());
            item.setPsCProMaterieltype(skuInfo.getMaterialType());
            item.setStandardWeight(skuInfo.getWeight());
            item.setSkuSpec(skuInfo.getSkuSpec());
            item.setProType(NumberUtils.toLong(skuInfo.getSkuType() + ""));

            item.setMDim4Id(skuInfo.getMDim4Id());
            item.setMDim6Id(skuInfo.getMDim6Id());
            if ("Y".equals(skuInfo.getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            // 2019-06-16 易邵峰修改：增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
            String psCSkuEcode = skuInfo.getSkuEcode();
            if (checkIsNeedTransferSkuUpperCase()) {
                psCSkuEcode = StringUtils.upperCase(psCSkuEcode);
            }
            item.setPsCSkuEcode(psCSkuEcode);
            item.setPsCProEname(skuInfo.getName()); //商品名称
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }

    /**
     * 是否需要转换成大写
     * 乔丹项目中：SAP系统存储的SKU部分有小写。为了统一，库里存储的全部为大写。因此在转单的时候强制转换成大写。
     *
     * @return true
     */
    private boolean checkIsNeedTransferSkuUpperCase() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.transfer.sku.toupper", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            log.error(LogUtil.format("checkIsNeedTransferSkuUpperCase,error:{}"), Throwables.getStackTraceAsString(ex));
            return true;
        }
    }
}
