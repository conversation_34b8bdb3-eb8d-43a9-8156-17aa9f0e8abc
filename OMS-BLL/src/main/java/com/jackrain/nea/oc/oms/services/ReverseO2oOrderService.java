package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.RegionInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.request.ReverseO2oOrderRequest;
import com.jackrain.nea.oc.oms.model.request.ReverseO2oReturnOrderRequest;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.StandplatRefundOrderTransferUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @ClassName ReverseO2oOrderService
 * @description:
 * @author: Lay.Jiang
 * @create: 2021-06-09 14:25
 **/
@Component
@Slf4j
public class ReverseO2oOrderService {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private RegionNewService regionNewService;

    private static final String ORDER_TABLE_NAME = "OC_B_ORDER";

    private static final String ORDER_ITEM_TABLE_NAME = "OC_B_ORDER_ITEM";

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBOrderOffService ocBOrderOffService;

    @Autowired
    private StandplatRefundOrderTransferUtil standplatRefundOrderTransferUtil;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    @Autowired
    private OmsReturnOrderService omsReturnOrderService;

    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OmsOrderLogService orderLogService;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;


    @Transactional(rollbackFor = Throwable.class)
    public OcBOrderRelation saveOcBOrderRelation(ReverseO2oOrderRequest request) {
        List<Long> orderIds = GSI4Order.getIdListBySourceCode(request.getO2oSourceCode());
        if (!CollectionUtils.isEmpty(orderIds)) {
            throw new NDSException("当前单号在系统中已经存在!");
        }
        User user = SystemUserResource.getRootUser();
        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
        OcBOrder ocBOrder = buildOcBOrder(request, user);
        ocBOrderRelation.setOrderItemList(buildOcBOrderItems(ocBOrder, request, user));
        ocBOrderRelation.setOrderInfo(ocBOrder);
        ocBOrderMapper.insert(ocBOrder);
        ocBOrderItemMapper.batchInsert(ocBOrderRelation.getOrderItemList());
        orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.ORDER_ADD.getKey(), "反向O2O订单新增成功", "", "", user);
        OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
        toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
        toBeConfirmedTask.setOrderId(ocBOrder.getId());
        toBeConfirmedTask.setCreationdate(new Date());
        toBeConfirmedTask.setStatus(0);
        toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
        return ocBOrderRelation;
    }

    @Transactional(rollbackFor = Throwable.class)
    public void cancelOrder(ReverseO2oOrderRequest request) {
        List<Long> orderIds = GSI4Order.getIdListBySourceCode(request.getO2oSourceCode());
        if (CollectionUtils.isEmpty(orderIds) || orderIds.size() > 1) {
            throw new NDSException("当前单号在系统中不存在!");
        }
        User user = SystemUserResource.getRootUser();
        boolean cancleFlag = ocBOrderOffService
                .startCancelOrderByLock(user, orderIds.get(0), "取消订单", "反向O2O订单取消");
        if (!cancleFlag) {
            throw new NDSException("取消订单失败");
        }

    }

    @Transactional(rollbackFor = Throwable.class)
    public void finishOrder(ReverseO2oOrderRequest request) {
        List<Long> orderIds = GSI4Order.getIdListBySourceCode(request.getO2oSourceCode());
        if (CollectionUtils.isEmpty(orderIds) || orderIds.size() > 1) {
            throw new NDSException("当前单号在系统中不存在!");
        }
        Long orderId = orderIds.get(0);
        OcBOrder oldOrder = ocBOrderMapper.selectByID(orderId);
        if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(oldOrder.getOrderStatus()) && !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(oldOrder.getOrderStatus())) {
            throw new NDSException("当前订单在系统中非(仓库发货或平台发货)的状态不允许完成！");
        }

        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(orderId);
        ocBOrder.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        ocBOrder.setPlatformStatus(TStatusEnum.TRADE_FINISHED.getKey());
        ocBOrder.setModifieddate(new Date());
        ocBOrder.setScanTime(request.getTradeEndTime());
        ocBOrderMapper.updateById(ocBOrder);
    }

    public void createReturnOrder(ReverseO2oReturnOrderRequest request, User user) {
   /*     List<Long> orderIds = GSI4Order.getIdListBySourceCode(request.getO2oSourceCode());
        if (CollectionUtils.isEmpty(orderIds) || orderIds.size() > 1) {
            throw new NDSException("当前单号在系统中不存在原单!");
        }
        *//**如果是多条**//*
        if (orderIds.size() > 1) {
            *//**重新算一遍**//*
            orderIds.clear();
            *//**根据订单取原单状态**//*
            List<OcBOrder> orders = ocBOrderMapper.selectByIdsListWithIsActiveY(orderIds);
            orders.stream().forEach(ocBOrder -> {
                boolean validOrigOrderStatus = !OmsOrderStatus.CANCELLED.toInteger().equals(ocBOrder.getOrderStatus())
                        && !OmsOrderStatus.SYS_VOID.toInteger().equals(ocBOrder.getOrderStatus());
                if (validOrigOrderStatus) {
                    orderIds.add(ocBOrder.getId());
                }
            });
            if (orderIds.size() > 1) {
                throw new NDSException("当前单号在系统中存在不明确原单!");
            }
        }
        Long orderId = orderIds.get(0);
        // 新增
        String origOrderLockKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock reentrantLock = RedisMasterUtils.getReentrantLock(origOrderLockKey);
        try {
            if (!reentrantLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                AssertUtil.assertException(true, "原订单正在操作, 请稍后尝试");
            }
            OmsOrderRelation orderRelation = new OmsOrderRelation();
            OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
            Set<String> subOrderIds = request.getItemList().stream().map(ReverseO2oReturnOrderRequest.ReturnOrderItem::getOid).collect(Collectors.toSet());
            Map<String, BigDecimal> returnMap = request.getItemList().stream().collect(Collectors.toMap(ReverseO2oReturnOrderRequest.ReturnOrderItem::getOid, ReverseO2oReturnOrderRequest.ReturnOrderItem::getReturnQuantity));
            //根据订单查询需要退货的明细
            List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemByOoIds4CommonRefund(orderId, request.getO2oSourceCode(), subOrderIds);
            //
            for (OcBOrderItem item : ocBOrderItems) {
                item.setQtyRefund(returnMap.get(item.getOoid()));
            }
            orderRelation.setOcBOrder(ocBOrder);
            orderRelation.setOcBOrderItems(ocBOrderItems);
            Integer orderStatus = ocBOrder.getOrderStatus();
            boolean validOrigOrderStatus = !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                    && !OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                    && !OmsOrderStatus.DEAL_DONE.toInteger().equals(orderStatus);
            AssertUtil.assertException(validOrigOrderStatus, "原始订单: " + orderId + "，订单未发货不能新增退换货订单");
            OcBReturnOrder returnOrder = buildReturnOrder(request, user, ocBOrder);
            List<OcBReturnOrderRefund> orderRefunds = buildReturnOrderItemFromRefund(orderRelation, returnOrder, user);
            boolean qtyFlag = OmsOrderQtyCalculateService.getInstance().checkQtyCanReturn(orderRefunds, orderRelation.getOcBOrderItems());
            if (!qtyFlag) {
                throw new NDSException("申请数量大于可退数量");
            }
            String jointTid = OmsReturnAfterUtil.getJointTid(orderRefunds);
            returnOrder.setTid(jointTid);
            this.getAllSku(orderRefunds, returnOrder);
            OcBReturnOrderRelation ocBReturnOrderRelation = new OcBReturnOrderRelation();
            ocBReturnOrderRelation.setReturnOrderInfo(returnOrder);
            ocBReturnOrderRelation.setOrderRefundList(orderRefunds);
            omsReturnOrderService.insertOmsReturnOrderInfo(ocBReturnOrderRelation, user);
        } catch (Exception e) {
            log.error("退单新增异常,退单编号#{},异常信息:", request.getO2oReturnCode(), e);
            throw new NDSException(e.getMessage());
        } finally {
            reentrantLock.unlock();
        }*/
    }


    public ValueHolderV14 cancelReturnOrder(ReverseO2oReturnOrderRequest request, User user) {
/*
        ValueHolderV14 v14 = new ValueHolderV14();
        JSONArray array = new JSONArray();
        try {
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderList(request.getO2oReturnCode());
            for (OcBReturnOrder order : list) {
                array.add(order.getId());
            }
            return ocCancelChangingOrRefundService.oneOcCancle(user, v14, array);
        } catch (Exception e) {
            log.error("退单新增异常,退单编号#{},异常信息:", request.getO2oReturnCode(), e);
            throw new NDSException(e.getMessage());
        }*/
        return null;
    }

    public ValueHolderV14 updateReturnOrder(ReverseO2oReturnOrderRequest request, User user) {
       /* try {
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderList(request.getO2oReturnCode());
            for (OcBReturnOrder returnOrder : list) {
                OcBReturnOrder order = new OcBReturnOrder();
                order.setLogisticsCode(request.getLogisticsNo());
                order.setCpCLogisticsEcode(request.getCompanyCode());
                order.setCpCLogisticsEname(request.getCompanyName());
                setLogisticInfo(returnOrder, request.getCompanyName());
                order.setId(returnOrder.getId());
                order.setModifieddate(new Date());
                order.setModifierename(user.getEname());
                order.setModifiername(user.getName());
                order.setModifierid(Long.valueOf(user.getId()));
                ocBReturnOrderMapper.updateById(order);
                omsReturnOrderService.saveAddOrderReturnLog(returnOrder.getId(), SysNotesConstant.UPDATE_EXCHANGEORDER_LOG_MESSAGE_FOR_LOGISTICS + "[" + request.getCompanyName() + "]",
                        SysNotesConstant.UPDATE_EXCHANGEORDER_LOG_TYPE, user);
            }
            return ValueHolderV14Utils.getSuccessValueHolder("成功");
        } catch (Exception e) {
            log.error("退单信息更新异常,退单编号#{},异常信息:", request.getO2oReturnCode(), e);
            throw new NDSException(e.getMessage());
        }*/
        return ValueHolderV14Utils.getSuccessValueHolder("成功");
    }


    private OcBReturnOrder buildReturnOrder(ReverseO2oReturnOrderRequest request, User user, OcBOrder ocBOrder) {
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        //反向O2O退款单号
        returnOrder.setReturnId(request.getO2oReturnCode());
        //买家昵称
        returnOrder.setBuyerNick(request.getBuyerNick());
        //申请退款时间
        returnOrder.setReturnCreateTime(request.getCreationdate());
        //最后修改时间
        returnOrder.setLastUpdateTime(new Date());
        //退款说明
        returnOrder.setReturnDesc(request.getReturnReason());
        String companyName = request.getCompanyName();
        //退回物流单号
        returnOrder.setLogisticsCode(request.getLogisticsNo());
        returnOrder.setCpCLogisticsEname(companyName);
        //退回说明
        this.setLogisticInfo(returnOrder, companyName);
        //卖家呢城
        returnOrder.setSellerNick(ocBOrder.getCpCShopSellerNick());
        //物流公司名称
        returnOrder.setCreationdate(new Date());
        returnOrder.setBillNo(sequenceUtil.buildReturnBillNo());
        //等待退货入库(PRD数据对象)
        returnOrder.setReturnStatus(TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode());
        //原始订单编号
        returnOrder.setOrigOrderId(ocBOrder.getId());
        //退还运费，默认0
        returnOrder.setReturnAmtShip(BigDecimal.ZERO);
        //退还其他费用，默认0
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);
        //换货人姓名
        returnOrder.setReceiveName(ocBOrder.getReceiverName());
        //换货人手机
        returnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getOrderSource());
        //邮编
        returnOrder.setReceiveZip(ocBOrder.getReceiverZip());
        //发货仓库
        returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        //平台类型
        returnOrder.setPlatform(ocBOrder.getPlatform());
        // returnOrder.setThirdWarehouseType(ocBOrder.getThirdWarehouseType());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());

        //换货金额
        returnOrder.setExchangeAmt(BigDecimal.ZERO);
        returnOrder.setTid(ocBOrder.getTid());
        //是否传AG默认否
        returnOrder.setIsToag(AGStatusEnum.INIT.getVal());
        //是否生成调拨单，默认0
        returnOrder.setIsTransfer(0);
        //是否生成零售，默认0
        returnOrder.setIsTodrp(0);
        //退单状态，默认20
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //是否手工新增，默认0
        returnOrder.setIsAdd(0);
        //虚拟入库状态，默认0
        returnOrder.setInventedStatus(0);
        //是否原退，默认0
        returnOrder.setIsRefund(0);
        //是否确认收货，默认0
        returnOrder.setIsReceiveConfirm(0);
        //WMS撤回状态，默认0
        returnOrder.setWmsCancelStatus(0);
        //强制入库，默认0
        returnOrder.setIsForce(0);
        //是否手工审核，默认0
        returnOrder.setIsManualAudit(0);
        //是否传WMS
        returnOrder.setIsTowms(0);
        //是否入仓成功
        returnOrder.setIsInstorage(0);
        returnOrder.setOrigSourceCode(ocBOrder.getSourceCode());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());
        //退款原因
        //returnOrder.setRemark(ipBTaobaoRefund.getReason());
        //returnOrder.setReturnReason(ipBTaobaoRefund.getReason());
        returnOrder.setReceiveAddress(ocBOrder.getReceiverAddress());
        //店铺id
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        returnOrder.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        returnOrder.setCpCShopEcode(ocBOrder.getCpCShopEcode());

        returnOrder.setReceiverProvinceName(ocBOrder.getCpCRegionProvinceEname());
        returnOrder.setReceiverCityName(ocBOrder.getCpCRegionCityEname());
        returnOrder.setReceiverAreaName(ocBOrder.getCpCRegionAreaEname());
        returnOrder.setReceiverProvinceId(ocBOrder.getCpCRegionProvinceId());
        returnOrder.setReceiverCityId(ocBOrder.getCpCRegionCityId());
        returnOrder.setReceiverAreaId(ocBOrder.getCpCRegionAreaId());
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        //原单卖家备注
        returnOrder.setOrigSellerRemark(ocBOrder.getSellerMemo());
        //原单买家留言
        returnOrder.setOrigBuyerMessage(ocBOrder.getBuyerMessage());
        //取值为发货实体仓档案中关联的退货待检实体仓仓库
        this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder);
        returnOrder.setReturnProType(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode());

        returnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());
        returnOrder.setInventedStatus(0); //未发起拦截
        StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
        Integer isMultiReturnWarehouse = shopStrategy.getIsMultiReturnWarehouse();
        if (isMultiReturnWarehouse == null || isMultiReturnWarehouse == 0) {
            returnOrder.setCpCPhyWarehouseInId(shopStrategy.getCpCWarehouseDefId());
            this.selectReturnCPhyWarehouse(shopStrategy.getCpCWarehouseDefId(), returnOrder);
        }
        OperateUserUtils.saveOperator(returnOrder, user);
        return returnOrder;
    }

    /**
     * 退单明细表数量
     *
     * @param
     * @return
     */
    private List<OcBReturnOrderRefund> buildReturnOrderItemFromRefund(OmsOrderRelation orderRelation, OcBReturnOrder returnOrder, User user) {
        List<OcBReturnOrderRefund> result = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        for (OcBOrderItem orderItem : ocBOrderItems) {

            OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
            //申请数量
            returnOrderRefund.setQtyRefund(orderItem.getQtyRefund());
            //商品名称
            returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());

            returnOrderRefund.setPrice(orderItem.getPrice());
            //1 qty_can_refund 购买数量 合计所有明细qty
            returnOrderRefund.setQtyCanRefund(orderItem.getQty());
            //商品单价
            returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
            returnOrderRefund.setPriceList(orderItem.getPriceList());
            //国标码
            returnOrderRefund.setBarcode(orderItem.getBarcode());
            //修改人用户名
            returnOrderRefund.setModifierename(orderItem.getModifierename());
            //商品规格
            returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
            returnOrderRefund.setOid(orderItem.getOoid());
            returnOrderRefund.setTid(orderItem.getTid());
            //条码id
            returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
            returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
            returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
            returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
            returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
            returnOrderRefund.setPsCProId(orderItem.getPsCProId());
            //颜色尺寸
            returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
            returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
            returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

            returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
            returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
            returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
            returnOrderRefund.setSex(orderItem.getSex());
            returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
            returnOrderRefund.setOcBOrderItemId(orderItem.getId());
            returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
            //returnOrderRefund.setAmtPtRefund(ipBTaobaoRefund.getRefundFee());
            returnOrderRefund.setGiftType(orderItem.getGiftType());
            returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
            //returnOrderRefund.setRefundStatus(ipBTaobaoRefund.getStatus());
            //returnOrderRefund.setRefundBillNo(ocBOrder.getBillNo());
            returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                    4, BigDecimal.ROUND_HALF_UP));
            returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");

            returnOrderRefund.setRefundStatus("WAIT_BUYER_RETURN_GOODS");
            returnOrderRefund.setRefundBillNo(returnOrder.getReturnId());
            OperateUserUtils.saveOperator(returnOrderRefund, user);
            result.add(returnOrderRefund);
        }
        return result;
    }

    // 构建订单信息
    private OcBOrder buildOcBOrder(ReverseO2oOrderRequest request, User user) {
        //新增根据省市区名称查询省市区的信息
        ProvinceCityAreaInfo provinceCityAreaInfo = regionNewService.selectNewProvinceCityAreaInfo(
                request.getReceiverProvince(), request.getReceiverCity(), request.getReceiverDistrict());
        RegionInfo province = provinceCityAreaInfo.getProvinceInfo();
        if (province == null) {
            throw new NDSException("省:《" + request.getReceiverProvince() + "》不存在！");
        }
        //开始赋值市的信息
        RegionInfo city = provinceCityAreaInfo.getCityInfo();
        if (city == null) {
            throw new NDSException("市:《" + request.getReceiverCity() + "》不存在！");
        }
        OcBOrder ocBOrder = new OcBOrder();
        // 来源平台编码
       /* ocBOrder.setSourcePlatformCode(request.getSourcePlatformCode());
        ocBOrder.setSourceShopInfo(request.getSourceShopEcode() + " " + request.getSourceShopName());*/
        // 店铺信息
        CpShop cpShop = this.queryCpShopByCode(request.getCpCShopEcode());
        ocBOrder.setCpCShopId(cpShop.getCpCShopId());
        ocBOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
        ocBOrder.setCpCShopSellerNick(cpShop.getSellerNick());
        ocBOrder.setCpCShopEcode(cpShop.getEcode());
        ocBOrder.setPlatform(Optional.ofNullable(cpShop.getCpCPlatformId()).orElse(-1L).intValue());
        ocBOrder.setId(ModelUtil.getSequence(ORDER_TABLE_NAME));
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        ocBOrder.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        ocBOrder.setIsInterecept(0);
        ocBOrder.setIsInreturning(0);
        ocBOrder.setQtySplit(0L);
        ocBOrder.setIsSplit(0);
        ocBOrder.setIsMerge(0);
        ocBOrder.setIsCancelMerge(0);
        //待分配
        ocBOrder.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
        ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
        ocBOrder.setInvoiceStatus(0);
        ocBOrder.setTid(request.getO2oSourceCode());
        ocBOrder.setSourceCode(request.getO2oSourceCode());
        ocBOrder.setMergeSourceCode(request.getO2oSourceCode());
        ocBOrder.setOccupyStatus(OrderOccupyStatus.STATUS_0);
        ocBOrder.setIsSameCityPurchase(0);
        ocBOrder.setOrderDate(request.getTradeCreateTime());
        ocBOrder.setPayTime(request.getPayTime());
        ocBOrder.setServiceAmt(BigDecimal.ZERO);
        ocBOrder.setOrderFlag("0");
        ocBOrder.setOutStatus(1);
        ocBOrder.setWmsCancelStatus(0);
        ocBOrder.setRefundConfirmStatus(0);
        ocBOrder.setAutoAuditStatus(0);
        // ocBOrder.setSysPresaleStatus(0);
        ocBOrder.setIsModifiedOrder(0);
        //订单 清空审核时间 、配货时间
        ocBOrder.setDistributionTime(null); //配货时间
        ocBOrder.setAuditTime(null); // 审核时间
        ocBOrder.setSplitStatus(0); // 拆单状态
        ocBOrder.setIsExchangeNoIn(0L);// 是否换货未入库
        // 订单相关金额设置
        ocBOrder.setOrderAmt(request.getPayment());
        ocBOrder.setProductAmt(request.getPayment());
        ocBOrder.setReceivedAmt(request.getPayment());
        ocBOrder.setAmtReceive(request.getPayment());
        ocBOrder.setAdjustAmt(BigDecimal.ZERO);
        ocBOrder.setProductDiscountAmt(BigDecimal.ZERO);
        ocBOrder.setOrderDiscountAmt(BigDecimal.ZERO);
        ocBOrder.setShipAmt(request.getPostFee());

        // 省市区赋值
        ocBOrder.setReceiverMobile(request.getReceiverMobile());
        ocBOrder.setReceiverName(request.getReceiverName());
        ocBOrder.setCpCRegionProvinceEname(request.getReceiverProvince());
        ocBOrder.setCpCRegionCityEname(request.getReceiverCity());
        ocBOrder.setCpCRegionAreaEname(request.getReceiverDistrict());
        ocBOrder.setReceiverAddress(request.getReceiverAddress());
        ocBOrder.setCpCRegionProvinceId(province.getId());
        ocBOrder.setCpCRegionProvinceEcode(province.getCode());
        ocBOrder.setCpCRegionCityId(city.getId());
        ocBOrder.setCpCRegionCityEcode(city.getCode());
        //开始赋值区的信息
        RegionInfo area = provinceCityAreaInfo.getAreaInfo();
        if (area != null) {
            ocBOrder.setCpCRegionAreaId(area.getId());
            ocBOrder.setCpCRegionAreaEcode(area.getCode());
        }
        //商品优惠金额
        ocBOrder.setProductDiscountAmt(request.getDiscountFee());
        makeCreateField(ocBOrder, user);
        ocBOrder.setOwnerename(user.getEname());
        ocBOrder.setModifierename(user.getEname());
        return ocBOrder;
    }

    private List<OcBOrderItem> buildOcBOrderItems(OcBOrder ocBOrder, ReverseO2oOrderRequest request, User user) {
        if (CollectionUtils.isEmpty(request.getItems())) {
            throw new NDSException("订单明细为空！");
        }
        BigDecimal productAmt = BigDecimal.ZERO;
        List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
        for (ReverseO2oOrderRequest.ReverseO2oOrderItem item : request.getItems()) {
            ProductSku productSku = psRpcService.selectProductSku(item.getOuterSkuId());
            if (productSku == null) {
                throw new NDSException("商品:" + item.getOuterSkuId() + ",不存在");
            }
            OcBOrderItem ocBOrderItem = new OcBOrderItem();
            makeCreateField(ocBOrderItem, user);
            // 供应类型 0 普通 1.代销轻供 2.寄售轻供
            ocBOrderItem.setPsCProSupplyType(productSku.getPsCProSupplyType());
            ocBOrderItem.setModifierename(user.getEname());
            ocBOrderItem.setOwnerename(user.getEname());
            ocBOrderItem.setId(ModelUtil.getSequence(ORDER_ITEM_TABLE_NAME));
            ocBOrderItem.setOcBOrderId(ocBOrder.getId());
            ocBOrderItem.setOoid(item.getOid());
            ocBOrderItem.setPsCSkuEcode(item.getOuterSkuId().toUpperCase());
            ocBOrderItem.setQty(item.getNum());
            ocBOrderItem.setPrice(item.getPrice());
            ocBOrderItem.setPriceActual(item.getPrice());
            ocBOrderItem.setAmtDiscount(BigDecimal.ZERO);
            ocBOrderItem.setOrderSplitAmt(BigDecimal.ZERO);
            ocBOrderItem.setAdjustAmt(BigDecimal.ZERO);
            ocBOrderItem.setQtyRefund(BigDecimal.ZERO);
            ocBOrderItem.setTid(ocBOrder.getTid());
            ocBOrderItem.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
            ocBOrderItem.setIsGift(0);
            if (productSku.getSkuType() == SkuType.NORMAL_PRODUCT) {
                ocBOrderItem.setProType(Long.valueOf(productSku.getSkuType()));
            } else {
                ocBOrderItem.setProType(Long.valueOf(SkuType.NO_SPLIT_COMBINE));
            }
            ocBOrderItem.setPriceTag(Optional.ofNullable(productSku.getPricelist()).orElse(BigDecimal.ZERO)); // 吊牌价
            ocBOrderItem.setPriceList(Optional.ofNullable(productSku.getPricelist()).orElse(BigDecimal.ZERO)); // 吊牌价
            ocBOrderItem.setPsCSkuId(productSku.getId());
            ocBOrderItem.setPsCSkuPtEcode(productSku.getSkuEcode());
            ocBOrderItem.setPsCSkuEname(productSku.getSkuName());
            ocBOrderItem.setSkuSpec(productSku.getSkuSpec());
            ocBOrderItem.setBarcode(productSku.getBarcode69());
            ocBOrderItem.setPsCProId(productSku.getProdId());
            ocBOrderItem.setPsCProEcode(productSku.getProdCode());
            ocBOrderItem.setPsCProEname(productSku.getName());
            ocBOrderItem.setPsCBrandId(productSku.getPsCBrandId());
            ocBOrderItem.setSex(productSku.getSex());
            ocBOrderItem.setPsCClrId(productSku.getColorId());
            ocBOrderItem.setPsCClrEcode(productSku.getColorCode());
            ocBOrderItem.setPsCClrEname(productSku.getColorName());
            ocBOrderItem.setPsCSizeId(productSku.getSizeId());
            ocBOrderItem.setPsCSizeEcode(productSku.getSizeCode());
            ocBOrderItem.setPsCSizeEname(productSku.getSizeName());
            // 增加品类信息 20220923
            ocBOrderItem.setMDim4Id(productSku.getMDim4Id());
            ocBOrderItem.setMDim6Id(productSku.getMDim6Id());
            if ("Y".equals(productSku.getIsEnableExpiry())) {
                ocBOrderItem.setIsEnableExpiry(1);
            } else {
                ocBOrderItem.setIsEnableExpiry(0);
            }

            ocBOrderItem.setPsCProMaterieltype(productSku.getMaterialType());
            ocBOrderItem.setStandardWeight(productSku.getWeight());
            //设置明细优惠金额
            ocBOrderItem.setAmtDiscount(item.getDiscountFee());

            //单行实际成交金额 = (平台售价*数量) - 优惠金额 - 平摊金额 + 调整金额  （反向o2o这里平摊金额和调整金额都为0）
            BigDecimal price = ocBOrderItem.getPrice() == null ? BigDecimal.ZERO : ocBOrderItem.getPrice();
            BigDecimal qty = ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty();
            BigDecimal amtDiscount = ocBOrderItem.getAmtDiscount() == null ? BigDecimal.ZERO : ocBOrderItem.getAmtDiscount();
            ocBOrderItem.setRealAmt(price.multiply(qty).subtract(amtDiscount).setScale(2, BigDecimal.ROUND_HALF_UP));
            ocBOrderItems.add(ocBOrderItem);
            //计算 明细数量 * 平台售价
            productAmt = productAmt.add(price.multiply(qty));
        }
        //为了让审核能够通过，这里将 订单商品金额 = sum（明细数量 * 平台售价）
        ocBOrder.setProductAmt(productAmt.setScale(2, BigDecimal.ROUND_HALF_UP));
        return ocBOrderItems;
    }

    public void setLogisticInfo(OcBReturnOrder returnOrder, String buyerLogisticName) {
        if (StringUtils.isNotEmpty(buyerLogisticName)) {
            LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(buyerLogisticName);
            if (logisticsInfo != null) {
                returnOrder.setCpCLogisticsId(logisticsInfo.getId());
            }
        }
    }

    private void makeCreateField(BaseModel model, User user) {
        Date date = new Date();
        model.setAdClientId((long) user.getClientId());//所属公司
        model.setAdOrgId((long) user.getOrgId());//所属组织
        model.setOwnerid(Long.valueOf(user.getId()));//创建人id
        model.setCreationdate(date);//创建时间
        model.setOwnername(user.getName());//创建人用户名
        model.setModifierid(Long.valueOf(user.getId()));//修改人id
        model.setModifiername(user.getName());//修改人用户名
        model.setModifieddate(date);//修改时间
        model.setIsactive("Y");//是否启用
    }

    // 根据店铺编码  查询店铺信息
    private CpShop queryCpShopByCode(String shopCode) {
        List<String> shopCodes = new ArrayList<>();
        shopCodes.add(shopCode);
        // 根据店铺编码查询店铺信息
        List<CpShop> cpShopList = cpRpcService.queryByListByNameOrCode(shopCodes);
        if (CollectionUtils.isEmpty(cpShopList)) {
            throw new NDSException("店铺编码对应的店铺信息不存在!");
        }
        return cpShopList.get(0);
    }

    /**
     * 通过实体仓id查询该实体仓的退货仓id
     */
    private void selectReturnCPhyWarehouse(Long cPhyWarehouseId, OcBReturnOrder returnOrder) {
        if (cPhyWarehouseId != null) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cPhyWarehouseId);
            if (cpCPhyWarehouse != null) {
                Integer wmsControlWarehouse = cpCPhyWarehouse.getWmsControlWarehouse();
                if (wmsControlWarehouse != null && wmsControlWarehouse == 1) {
                    returnOrder.setIsNeedToWms(1L);
                } else {
                    //@20210917是否pos管控仓下发wms
                    if (StringUtils.isNotBlank(cpCPhyWarehouse.getIsPos()) && StringUtils.equals(cpCPhyWarehouse.getIsPos(), "1")) {
                        returnOrder.setIsNeedToWms(1L);
                    }
                }
            }
        }
    }

    /**
     * 封装主表的all_sku以及 商品数量
     *
     * @param returnOrderItems
     * @return
     */
    private void getAllSku(List<OcBReturnOrderRefund> returnOrderItems, OcBReturnOrder returnOrder) {
        //拼接退货sku加数量
        String skuQyt = "";
        BigDecimal qtyInstore = BigDecimal.ZERO;
        int i = 0;
        for (OcBReturnOrderRefund returnOrderItem : returnOrderItems) {
            qtyInstore = qtyInstore.add(returnOrderItem.getQtyRefund());
            if (i == 5) {
                continue;
            }
            String str = returnOrderItem.getPsCSkuEcode() + "(" + returnOrderItem.getQtyRefund().intValue() + "),";
            skuQyt = skuQyt + str;
            i++;

        }
        if (StringUtils.isNotEmpty(skuQyt)) {
            //去掉最后一个,号
            skuQyt = skuQyt.substring(0, skuQyt.length() - 1);
        }
        returnOrder.setAllSku(skuQyt);
        returnOrder.setQtyInstore(qtyInstore);
    }

}