package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Maps;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCImperfectStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCImperfectStrategyMapper;
import com.jackrain.nea.oc.oms.model.StCImperfectStrategyInfo;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.enums.TocCcAppointRuleEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategy;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategyItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 残次service
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OmsOrderImperfectStrategyService {

    /**
     * 残次策略适用平台，用逗号,隔开
     */
    @NacosValue(value = "${r3.oc.oms.imperfect.strategy.match.platforms:0}", autoRefreshed = true)
    public String matchPlatforms;

    @Resource
    private StCImperfectStrategyMapper stCImperfectStrategyMapper;
    @Resource
    private StCImperfectStrategyItemMapper stCImperfectStrategyItemMapper;
    @Resource
    private SplitBeforeSourcingStService splitBeforeSourcingStService;
    @Resource
    private OcBOrderMapper ocBOrderMapper;
    @Resource
    private OmsOrderLogService omsOrderLogService;

    /**
     * 残次策略匹配
     * 平台商品ID>SKu+商品标题>商品标题
     *
     * @param orderInfo
     */
    public boolean matchImperfectStrategy(OcBOrderRelation orderInfo, User operateUser) {
        OcBOrder ocBOrder = orderInfo.getOrderInfo();

        if (StringUtils.isBlank(matchPlatforms)) {
            return false;
        }

        List<Integer> matchPlatformList = Arrays.stream(matchPlatforms.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchPlatformList) || !matchPlatformList.contains(ocBOrder.getPlatform()) || OmsBusinessTypeUtil.isToBOrder(ocBOrder)) {
            return false;
        }

        //查询有效的残次策略
        List<StCImperfectStrategyInfo> strategyInfos = getImperfectStrategyInfos(ocBOrder.getCpCShopId());
        if (CollectionUtils.isEmpty(strategyInfos)) {
            return false;
        }

        //符合支付时间范围的策略
        List<StCImperfectStrategyInfo> useStrategies = strategyCheck(ocBOrder, strategyInfos);
        if (useStrategies == null) {
            return false;
        }

        if (CollectionUtils.isEmpty(useStrategies)) {
            log.info(LogUtil.format("matchImperfectStrategy useStrategies empty orderId:{}", "残次策略匹配"), ocBOrder.getId());
            return false;
        }
        Map<Long, StCImperfectStrategyInfo> strategyMap = Maps.newHashMap();
        for (StCImperfectStrategyInfo useStrategy : useStrategies) {
            strategyMap.put(useStrategy.getImperfectStrategy().getId(), useStrategy);
        }

        List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
        //过滤取消和组合商品的明细
        orderItemList = orderItemList.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE
                && p.getRefundStatus() != OmsOrderRefundStatus.SUCCESS.toInteger()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemList)) {
            //无符合条件的明细
            log.info(LogUtil.format("matchImperfectStrategy orderItemList empty orderId:{}", "残次策略匹配"), ocBOrder.getId());
            return false;
        }
        Map<Long, OcBOrderItem> orderItemMap = orderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, x -> x, (a, b) -> a));

        //匹配残次策略明细
        List<StCImperfectStrategyItem> strategyItems = useStrategies.stream()
                .filter(useStrategy -> useStrategy != null && useStrategy.getImperfectStrategyItems() != null)
                .flatMap(useStrategy -> useStrategy.getImperfectStrategyItems().stream())
                .collect(Collectors.toList());

        //itemId,saleProductAttr
        Map<Long, String> matchItemMap = Maps.newHashMap();
        //策略编码，规则+内容
        Map<String, Set<String>> logMessageMap = Maps.newHashMap();

        //先匹配平台商品id
        List<StCImperfectStrategyItem> platformList =
                strategyItems.stream().filter(p -> p.getAppointRule().equals(TocCcAppointRuleEnum.PLATFORM_SKU_ID.getVal())).collect(Collectors.toList());
        Map<String, List<StCImperfectStrategyItem>> platformContentMap =
                platformList.stream().collect(Collectors.groupingBy(StCImperfectStrategyItem::getAppointContent));
        matchPlatformId(platformList, orderItemList, matchItemMap, platformContentMap, strategyMap, logMessageMap);

        //再匹配sku编码+商品标题
        List<StCImperfectStrategyItem> skuAndTitleStrategyItems =
                strategyItems.stream().filter(p -> p.getAppointRule().equals(TocCcAppointRuleEnum.SKU_CODE_AND_TITLE.getVal())).collect(Collectors.toList());
        matchSkuCodeAndTitle(skuAndTitleStrategyItems, orderItemList, matchItemMap, strategyMap, logMessageMap);

        //最后匹配商品标题
        List<StCImperfectStrategyItem> titleStrategyItems =
                strategyItems.stream().filter(p -> p.getAppointRule().equals(TocCcAppointRuleEnum.SKU_TITLE.getVal())).collect(Collectors.toList());
        matchSkuTitle(titleStrategyItems, orderItemList, matchItemMap, strategyMap, logMessageMap);

        if (MapUtils.isEmpty(matchItemMap)) {
            //所有明细未匹配到残次策略
            log.info(LogUtil.format("matchImperfectStrategy matchItemMap empty orderId:{}", "残次策略匹配"), ocBOrder.getId());
            return false;
        }

        log.info(LogUtil.format("matchImperfectStrategy orderId:{},matchItemMap:{}", "残次策略匹配"), ocBOrder.getId(), JSON.toJSONString(matchItemMap));

        Map<String, List<OcBOrderItem>> orderItemsMap = Maps.newHashMap();
        //matchItemMap根据value分组
        for (Map.Entry<Long, OcBOrderItem> entry : orderItemMap.entrySet()) {
            Long orderItemId = entry.getKey();
            String saleProductAttr = matchItemMap.get(orderItemId);
            if (StringUtils.isBlank(saleProductAttr)) {
                //非残次
                saleProductAttr = OrderSaleProductAttrEnum.NULL.getVal();
            }
            if (orderItemsMap.containsKey(saleProductAttr)) {
                orderItemsMap.get(saleProductAttr).add(orderItemMap.get(orderItemId));
            } else {
                orderItemsMap.put(saleProductAttr, Lists.newArrayList(orderItemMap.get(orderItemId)));
            }
        }

        StringBuilder stringBuilder = new StringBuilder();
        if (MapUtils.isNotEmpty(logMessageMap)) {
            stringBuilder.append("toc残次");
            stringBuilder.append(" ");
            for (Map.Entry<String, Set<String>> entry : logMessageMap.entrySet()) {
                String key = entry.getKey();
                Set<String> value = entry.getValue();
                stringBuilder.append(key);
                stringBuilder.append(" ");
                stringBuilder.append(value);
                stringBuilder.append("/");
            }
        }

        //无需拆单
        if (orderItemsMap.size() == 1) {
            log.info(LogUtil.format("matchImperfectStrategy orderItemsMap size==1 orderId:{}", "残次策略匹配"), ocBOrder.getId());

            if (orderItemsMap.containsKey(OrderSaleProductAttrEnum.NULL.getVal())) {
                //全部普通品
                return false;
            }

            Set<String> strings = orderItemsMap.keySet();
            String saleProductAttr = strings.iterator().next();

            OcBOrder order = new OcBOrder();
            order.setId(ocBOrder.getId());
            order.setSaleProductAttr(saleProductAttr);
            order.setModifieddate(new Date());
            ocBOrderMapper.updateById(order);

            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.IMPERFECT_STRATEGY.getKey(), stringBuilder.toString(), null, null, operateUser);

            //后续策略跳过标记
            orderInfo.getOrderInfo().setSaleProductAttr(saleProductAttr);
            return false;
        }

        //拆单
        List<List<OcBOrderItem>> splitItemNew = new ArrayList<>();
        for (Map.Entry<String, List<OcBOrderItem>> entry : orderItemsMap.entrySet()) {
            List<OcBOrderItem> orderItemsPart = new ArrayList<>();
            for (OcBOrderItem ocBOrderItem : entry.getValue()) {
                OcBOrderItem orderItem = new OcBOrderItem();
                BeanUtils.copyProperties(ocBOrderItem, orderItem);
                orderItemsPart.add(orderItem);
            }
            splitItemNew.add(orderItemsPart);
        }

        ocBOrder.setOccupyStatus(OrderOccupyStatus.STATUS_40);

        log.info(LogUtil.format("matchImperfectStrategy orderId:{},splitItemNew:{}", "残次策略匹配"), ocBOrder.getId(), JSON.toJSONString(splitItemNew));
        splitBeforeSourcingStService.insertNewOrders(splitItemNew, ocBOrder, orderItemList, operateUser, null, stringBuilder.toString(),
                null, null, false, Lists.newArrayList(), matchItemMap);
        return true;
    }

    private void matchSkuTitle(List<StCImperfectStrategyItem> titleStrategyItems, List<OcBOrderItem> orderItemList, Map<Long, String> matchItemMap, Map<Long, StCImperfectStrategyInfo> strategyMap, Map<String, Set<String>> logMessageMap) {
        if (CollectionUtils.isNotEmpty(titleStrategyItems)) {
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                if (matchItemMap.containsKey(ocBOrderItem.getId())) {
                    continue;
                }
                String title = ocBOrderItem.getTitle();

                if (StringUtils.isBlank(title)) {
                    continue;
                }

                for (StCImperfectStrategyItem strategyItem : titleStrategyItems) {
                    String appointContent = strategyItem.getAppointContent();
                    // 检查商品标题是否包含策略中的关键词
                    if (StringUtils.isNotBlank(appointContent) && title.contains(appointContent)) {
                        StCImperfectStrategyInfo strategyInfo = strategyMap.get(strategyItem.getImperfectStrategyId());
                        matchItemMap.put(ocBOrderItem.getId(), strategyInfo.getImperfectStrategy().getSaleProductAttr());
                        //日志
                        logMessage(logMessageMap, strategyItem, strategyInfo);
                        break;
                    }
                }
            }
        }
    }

    private void matchSkuCodeAndTitle(List<StCImperfectStrategyItem> skuAndTitleStrategyItems, List<OcBOrderItem> orderItemList, Map<Long, String> matchItemMap, Map<Long, StCImperfectStrategyInfo> strategyMap, Map<String, Set<String>> logMessageMap) {
        if (CollectionUtils.isNotEmpty(skuAndTitleStrategyItems)) {
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                if (matchItemMap.containsKey(ocBOrderItem.getId())) {
                    continue;
                }
                String psCProEcode = ocBOrderItem.getPsCProEcode();
                String title = ocBOrderItem.getTitle();

                if (StringUtils.isBlank(psCProEcode) || StringUtils.isBlank(title)) {
                    continue;
                }
                for (StCImperfectStrategyItem strategyItem : skuAndTitleStrategyItems) {
                    String[] split = strategyItem.getAppointContent().split("\\+");
                    if (psCProEcode.equals(split[0]) && title.equals(split[1])) {
                        StCImperfectStrategyInfo strategyInfo = strategyMap.get(strategyItem.getImperfectStrategyId());
                        matchItemMap.put(ocBOrderItem.getId(), strategyInfo.getImperfectStrategy().getSaleProductAttr());
                        //日志
                        logMessage(logMessageMap, strategyItem, strategyInfo);
                        break;
                    }
                }
            }
        }
    }

    private void matchPlatformId(List<StCImperfectStrategyItem> platformList, List<OcBOrderItem> orderItemList, Map<Long, String> matchItemMap, Map<String, List<StCImperfectStrategyItem>> platformContentMap, Map<Long, StCImperfectStrategyInfo> strategyMap, Map<String, Set<String>> logMessageMap) {
        if (CollectionUtils.isNotEmpty(platformList)) {
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                if (matchItemMap.containsKey(ocBOrderItem.getId())) {
                    continue;
                }
                String numIid = ocBOrderItem.getNumIid();
                if (StringUtils.isNotBlank(numIid) && platformContentMap.containsKey(numIid)) {
                    StCImperfectStrategyItem strategyItem = platformContentMap.get(numIid).get(0);
                    StCImperfectStrategyInfo strategyInfo = strategyMap.get(strategyItem.getImperfectStrategyId());
                    matchItemMap.put(ocBOrderItem.getId(), strategyInfo.getImperfectStrategy().getSaleProductAttr());
                    //日志
                    logMessage(logMessageMap, strategyItem, strategyInfo);
                }
            }
        }
    }

    private void logMessage(Map<String, Set<String>> logMessageMap, StCImperfectStrategyItem strategyItem, StCImperfectStrategyInfo strategyInfo) {
        Set<String> strings = logMessageMap.get(strategyInfo.getImperfectStrategy().getStrategyCode());
        if (CollectionUtils.isEmpty(strings)) {
            logMessageMap.put(strategyInfo.getImperfectStrategy().getStrategyCode(),
                    Sets.newHashSet(Lists.newArrayList(TocCcAppointRuleEnum.getDescriptionByVal(strategyItem.getAppointRule()) + ":" + strategyItem.getAppointContent())));
        } else {
            strings.add(TocCcAppointRuleEnum.getDescriptionByVal(strategyItem.getAppointRule()) + ":" + strategyItem.getAppointContent());
        }
    }

    /**
     * 查询符合条件的策略（支付时间）
     *
     * @param ocBOrder
     * @param strategyInfos
     * @return
     */
    private List<StCImperfectStrategyInfo> strategyCheck(OcBOrder ocBOrder, List<StCImperfectStrategyInfo> strategyInfos) {
        Date payTime = ocBOrder.getPayTime();
        if (payTime == null) {
            log.warn(LogUtil.format("matchImperfectStrategy payTime is null orderId:{}", "残次策略匹配"), ocBOrder.getId());
            return null;
        }

        List<StCImperfectStrategyInfo> useStrategies = Lists.newArrayList();
        for (StCImperfectStrategyInfo strategyInfo : strategyInfos) {
            StCImperfectStrategy imperfectStrategy = strategyInfo.getImperfectStrategy();
            Date startTime = imperfectStrategy.getStartTime();
            Date endTime = imperfectStrategy.getEndTime();
            //如果payTime在策略的开始时间和结束时间之间或者payTime等于开始时间或者payTime等于结束时间，则符合支付时间范围
            if (payTime.getTime() >= startTime.getTime() && payTime.getTime() <= endTime.getTime()) {
                useStrategies.add(strategyInfo);
            }
        }
        return useStrategies;
    }

    /**
     * 根据店铺获取有效缓存策略
     *
     * @param cpCShopId
     * @return
     */
    private List<StCImperfectStrategyInfo> getImperfectStrategyInfos(Long cpCShopId) {
        String strategyRedisKey = OmsRedisKeyResources.buildStCImperfectStrategyRedisKey(cpCShopId);
        String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(strategyRedisKey);

        List<StCImperfectStrategyInfo> strategyInfos = Lists.newArrayList();
        if (StringUtils.isNotBlank(redisValue)) {
            return JSON.parseArray(redisValue, StCImperfectStrategyInfo.class);
        } else {
            //查库（如果没配置有点问题，每次都会查）
            List<StCImperfectStrategy> stCImperfectStrategies = stCImperfectStrategyMapper.selectByShopId(cpCShopId);
            if (CollectionUtils.isEmpty(stCImperfectStrategies)) {
                return Lists.newArrayList();
            } else {
                //明细
                List<Long> strategyIds = stCImperfectStrategies.stream().map(StCImperfectStrategy::getId).collect(Collectors.toList());
                List<StCImperfectStrategyItem> stCImperfectStrategyItems = stCImperfectStrategyItemMapper.selectByStrategyIdList(strategyIds);
                if (CollectionUtils.isEmpty(stCImperfectStrategyItems)) {
                    return Lists.newArrayList();
                }
                Map<Long, List<StCImperfectStrategyItem>> strategyIdItemMap = stCImperfectStrategyItems.stream().collect(
                        Collectors.groupingBy(StCImperfectStrategyItem::getImperfectStrategyId));

                //缓存
                for (StCImperfectStrategy stCImperfectStrategy : stCImperfectStrategies) {
                    List<StCImperfectStrategyItem> strategyItems = strategyIdItemMap.get(stCImperfectStrategy.getId());
                    StCImperfectStrategyInfo imperfectStrategyInfo = new StCImperfectStrategyInfo();
                    imperfectStrategyInfo.setImperfectStrategy(stCImperfectStrategy);
                    imperfectStrategyInfo.setImperfectStrategyItems(strategyItems);
                    strategyInfos.add(imperfectStrategyInfo);
                }
                RedisOpsUtil.getStrRedisTemplate().opsForValue().set(strategyRedisKey, JSON.toJSONString(strategyInfos), 12, TimeUnit.HOURS);
            }
        }
        return strategyInfos;
    }
}
