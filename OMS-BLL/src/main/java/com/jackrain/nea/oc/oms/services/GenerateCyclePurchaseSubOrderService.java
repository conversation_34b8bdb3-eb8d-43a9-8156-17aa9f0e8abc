package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOccupyTask;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.table.StCCycleItemStrategy;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.cycle.StCCyclePurchaseStrategyResult;
import com.jackrain.nea.st.model.table.cycle.StCCyclePurchaseStrategy;
import com.jackrain.nea.st.model.table.cycle.StCCyclePurchaseStrategyItem;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.AssertUtils;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/9/5
 * description :生成周期购子订单
 */
@Slf4j
@Component
public class GenerateCyclePurchaseSubOrderService {

    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOccupyTaskMapper occupyTaskMapper;
    @Autowired
    private StCBusinessTypeMapper typeMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private BllRedisLockOrderUtil bllRedisLockOrderUtil;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private StCCycleStrategyMatchService cycleStrategyMatchService;
    @Autowired
    private OmsExpiryDateStService omsExpiryDateStService;
    @Autowired
    private SplitBeforeSourcingStService stSplitBeforeSourceStrategyService;

    /**
     * 生成周期购子订单
     *
     * @param sourceCode 平台原始订单号
     * @param user       用户
     */
    public void generateCyclePurchaseSubOrder(String sourceCode, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start GenerateCyclePurchaseSubOrderService.generateCyclePurchaseSubOrder sourceCode:{}", sourceCode), sourceCode);
        }
        List<Long> esIdList = GSI4Order.getIdListBySourceCode(sourceCode);
        List<OcBOrder> orderList = orderMapper.selectByIdsListBy50(esIdList);
        // 周期购平台发货订单集合（此处修改未待寻源状态）
        Set<Long> platformSendIds = new HashSet<>();
        List<OcBOrder> newOrderList = new ArrayList<>();
        List<OcBOrderLog> orderLogList = new ArrayList<>();
        List<OcBOrderItem> newOrderItemList = new ArrayList<>();
        List<OcBOccupyTask> newTaskList = new ArrayList<>();
        for (OcBOrder order : orderList) {
            if(log.isDebugEnabled()){
                log.debug(LogUtil.format("Start Recycling CyclePurchaseOrder orderId:{}", order.getId()), order.getId());
            }
            if(!checkIsCyclePurchaseOrder(order)){
                continue;
            }
            //  这里需要校验是否已生成过子单，避免重复生成 20220220 （周期购这里增加判断 是否已生成过子单）
            List<Long> subCycleOrderIdList = ES4Order.searchCyclePurchaseSubOrderIds(order.getBillNo(), null);
            if (CollectionUtils.isNotEmpty(subCycleOrderIdList)) {
                continue;
            }
            List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemListOccupy(order.getId());
            if (CollectionUtils.isEmpty(orderItems)) {
                continue;
            }
            singleGenerate(newOrderList, newOrderItemList, newTaskList, orderLogList, platformSendIds, order, orderItems, user);
        }
        Date now = new Date();
        List<OcBOrder> updateOrderList = platformSend(platformSendIds, user, now);
        GenerateCyclePurchaseSubOrderService service = ApplicationContextHandle.getBean(GenerateCyclePurchaseSubOrderService.class);
        // 因为下面要走寻源前拆单 还是会往寻源中间表写数据。所以此处不再往中间表写数据
        service.doSave(updateOrderList, newOrderList, newOrderItemList, newTaskList, orderLogList);
        buildCycleOrderGiftItem(newOrderList);
        // 新增明细完成之后 需要再手动走几个策略(商品效期策略、寻源前拆单策略)
        for (OcBOrder order : newOrderList) {
            OcBOrderParam ocBOrderParam = new OcBOrderParam();
            List<OcBOrderItem> orderItemList = orderItemMapper.selectOrderItemListOccupy(order.getId());
            ocBOrderParam.setOcBOrder(order);
            ocBOrderParam.setOrderItemList(orderItemList);
            omsExpiryDateStService.expiryDateStService(ocBOrderParam, user, "自动");
            OcBOrder newOrder = orderMapper.selectById(order.getId());
            List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemListOccupy(order.getId());
            // 重新赋值
            ocBOrderParam.setOcBOrder(newOrder);
            ocBOrderParam.setOrderItemList(orderItems);
            stSplitBeforeSourceStrategyService.splitBeforeSourcingStrategy(ocBOrderParam, user, null, null, false, false);
        }
    }

    private void buildCycleOrderGiftItem(List<OcBOrder> newSubOrderList) {
        for (OcBOrder order : newSubOrderList) {
            List<OcBOrderItem> giftOrderItemList;
            // 获取订单明细
            List<OcBOrderItem> orderItemList = orderItemMapper.selectOrderItemListOccupy(order.getId());
            Map<Long, List<StCCycleItemStrategy>> cycleItemStrategyMap = cycleStrategyMatchService.match(order, orderItemList, 2);
            // 判断cycleItemStrategyMap是否为空
            if (cycleItemStrategyMap.isEmpty()) {
                continue;
            }
            // 构建giftOrderItemList
            giftOrderItemList = cycleStrategyMatchService.buildGiftOrderItemList(cycleItemStrategyMap, order);
            if (CollectionUtils.isEmpty(giftOrderItemList)) {
                continue;
            }
            // 重新计算原订单明细
            // 先计算出来giftOrderItemList中的realAmt的总和
            BigDecimal giftOrderItemListRealAmtSum = giftOrderItemList.stream().map(OcBOrderItem::getRealAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 对ocBOrderItemList中的realAmt进行排序 找到最大的那一个
            OcBOrderItem maxRealAmtOrderItem = orderItemList.stream().max(Comparator.comparing(OcBOrderItem::getRealAmt)).get();
            BigDecimal newRealAmt = maxRealAmtOrderItem.getRealAmt().subtract(giftOrderItemListRealAmtSum);
            BigDecimal newPriceActual = newRealAmt.divide(maxRealAmtOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP);

            BigDecimal totalPrice = newRealAmt.add(maxRealAmtOrderItem.getOrderSplitAmt()).add(maxRealAmtOrderItem.getAdjustAmt()).add(maxRealAmtOrderItem.getAmtDiscount());
            BigDecimal price = totalPrice.divide(maxRealAmtOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP);

            OcBOrderItem updateOcBOrderItem = new OcBOrderItem();
            updateOcBOrderItem.setRealAmt(newRealAmt);
            updateOcBOrderItem.setPriceActual(newPriceActual);
            updateOcBOrderItem.setOcBOrderId(order.getId());
            updateOcBOrderItem.setId(maxRealAmtOrderItem.getId());
            updateOcBOrderItem.setPrice(price);
            orderItemMapper.updateById(updateOcBOrderItem);

            orderItemMapper.batchInsert(giftOrderItemList);
            List<OcBOrderItem> allItemList = orderItemMapper.selectOrderItemListOccupy(order.getId());
            String allSku = getAllSku(allItemList);
            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setId(order.getId());
            updateOrder.setAllSku(allSku);
            updateOrder.setIsHasgift(1);
            updateOrder.setIsPromOrder(1);
            updateOrder.setModifieddate(new Date());
            orderMapper.updateById(updateOrder);
            order.setAllSku(allSku);
            order.setIsHasgift(1);
            order.setIsPromOrder(1);
            order.setModifieddate(new Date());
        }
    }

    private String getAllSku(List<OcBOrderItem> newItems) {
        String allSku = "";
        for (OcBOrderItem item : newItems) {
            if (allSku.length() >= 100) {
                allSku += "...";
                break;
            }
            allSku = allSku + item.getPsCSkuEcode() + "(" + item.getQty().intValue() + "),";
        }
        return allSku;
    }

    /**
     * 数据更新保存服务
     *
     * @param updateOrderList  订单更新集合
     * @param newOrderList     订单新增集合
     * @param newOrderItemList 订单明细更新集合
     * @param newTaskList      寻源中间表新增集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void doSave(List<OcBOrder> updateOrderList, List<OcBOrder> newOrderList, List<OcBOrderItem> newOrderItemList, List<OcBOccupyTask> newTaskList, List<OcBOrderLog> orderLogList){
        if(CollectionUtils.isNotEmpty(updateOrderList)) {
            for (OcBOrder updateOrder : updateOrderList) {
                orderMapper.updateById(updateOrder);
            }
        }
        if (CollectionUtils.isNotEmpty(newOrderList)) {
            orderMapper.batchInsert(newOrderList);
        }
        if (CollectionUtils.isNotEmpty(newOrderItemList)) {
            orderItemMapper.batchInsert(newOrderItemList);
        }
//        if(CollectionUtils.isNotEmpty(newTaskList)){
//            occupyTaskMapper.batchInsert(newTaskList);
//        }
        if (CollectionUtils.isNotEmpty(orderLogList)) {
            omsOrderLogService.save(orderLogList);
        }
    }
    /**
     * 处理单个周期购主表
     * @param newOrderList
     * @param newOrderItemList
     * @param platformSendIds
     * @param order
     * @param orderItemList
     * @param user
     */
    public void singleGenerate(List<OcBOrder> newOrderList,
                               List<OcBOrderItem> newOrderItemList,
                               List<OcBOccupyTask> newTaskList,
                               List<OcBOrderLog> orderLogList,
                               Set<Long> platformSendIds,
                               OcBOrder order,
                               List<OcBOrderItem> orderItemList,
                               User user) {
        // 处理单个周期购明细行
        for (OcBOrderItem orderItem : orderItemList) {
            String spu = orderItem.getPsCProEcode();
            Date payTime = order.getPayTime();
            StCCyclePurchaseStrategyResult cyclePurchaseStrategyResult = matchCyclePurchaseStrategy(spu, payTime);
            // 未匹配到策略 不生成周期购子订单
            if(cyclePurchaseStrategyResult == null){
                OcBOrderLog orderLog = omsOrderLogService.getOcBOrderLog(order.getId(), order.getBillNo(),
                        OrderLogTypeEnum.GENERATE_CYCLE_PURCHASE_SUB_ORDER.getKey(), "商品["+ spu +"]未匹配到周期购策略", null, null, user);
                orderLogList.add(orderLog);
                continue;
            }
            // 子单生成成功后主单修改未待寻源状态
            platformSendIds.add(order.getId());
            // 处理单个周期购单个明细行多数量
            int qty = Integer.parseInt(orderItem.getQty().stripTrailingZeros().toPlainString());
            int totalQty = qty * cyclePurchaseStrategyResult.getMain().getQtyCycle();
            List<OcBOrder> newSubOrderList = new ArrayList<>(totalQty);
            List<OcBOrderItem> newSubOrderItemList = new ArrayList<>(totalQty);
            Date lastEstimateDeliveryDate = null;
            for (int i = 0; i < qty; i++) {
                // 生成周期购子订单
                lastEstimateDeliveryDate = buildCyclePurchaseSubOrder(newSubOrderList, newSubOrderItemList, newTaskList, order, orderItem, user, cyclePurchaseStrategyResult, i, lastEstimateDeliveryDate);
            }
            // 计算金额
            computeAmt(orderItem, newSubOrderList, newSubOrderItemList, totalQty);
            newOrderList.addAll(newSubOrderList);
            newOrderItemList.addAll(newSubOrderItemList);
            for (OcBOrder newSubOrder : newSubOrderList) {
                OcBOrderLog orderLog = omsOrderLogService.getOcBOrderLog(newSubOrder.getId(), newSubOrder.getBillNo(),
                        OrderLogTypeEnum.GENERATE_CYCLE_PURCHASE_SUB_ORDER.getKey(), "由周期购订单"+order.getId()+"商品[" + newSubOrder.getReserveVarchar02() + "]生成", null, null, user);
                orderLogList.add(orderLog);
            }
            OcBOrderLog orderLog = omsOrderLogService.getOcBOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.GENERATE_CYCLE_PURCHASE_SUB_ORDER.getKey(), "商品["+ spu +"]生成周期购子订单成功", null, null, user);
            orderLogList.add(orderLog);
        }
    }

    /**
     * 计算周期购子订单金额
     * @param orderItem 周期购原明细
     * @param newSubOrderList 周期购子订单集合
     * @param newSubOrderItemList 周期购子订单明细集合
     * @param totalQty 总期数
     */
    public void computeAmt(OcBOrderItem orderItem, List<OcBOrder> newSubOrderList, List<OcBOrderItem> newSubOrderItemList, int totalQty){
        // 1.商品金额：原单明细的成交金额÷原单的数量÷【周期数】 尾差放在最后一行
        BigDecimal productAmt = orderItem.getRealAmt().divide(BigDecimal.valueOf(totalQty), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal overProductAmt = orderItem.getRealAmt();
        // 2.订单优惠金额 同上
        BigDecimal orderDiscountAmt = orderItem.getOrderSplitAmt().divide(BigDecimal.valueOf(totalQty), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal overOrderDiscountAmt = orderItem.getOrderSplitAmt();
        // 3.商品优惠金额 同上
        BigDecimal productDiscountAmt = orderItem.getAmtDiscount().divide(BigDecimal.valueOf(totalQty), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal overProductDiscountAmt = orderItem.getAmtDiscount();
        // 4.调整金额 同上
        BigDecimal adjustAmt = orderItem.getAdjustAmt().divide(BigDecimal.valueOf(totalQty), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal overAdjustAmt = orderItem.getAdjustAmt();
        for (int i = 0; i < newSubOrderList.size(); i++) {
            OcBOrder subOrder = newSubOrderList.get(i);
            OcBOrderItem subOrderItem = newSubOrderItemList.get(i);

            if(i == newSubOrderList.size() - 1){
                subOrder.setProductAmt(overProductAmt);
                subOrder.setOrderDiscountAmt(overOrderDiscountAmt);
                subOrder.setProductDiscountAmt(overProductDiscountAmt);
                subOrder.setAdjustAmt(overAdjustAmt);
            }else{
                subOrder.setProductAmt(productAmt);
                subOrder.setOrderDiscountAmt(orderDiscountAmt);
                subOrder.setProductDiscountAmt(productDiscountAmt);
                subOrder.setAdjustAmt(adjustAmt);

                overProductAmt = overProductAmt.subtract(productAmt);
                overOrderDiscountAmt = overOrderDiscountAmt.subtract(orderDiscountAmt);
                overProductDiscountAmt = overProductDiscountAmt.subtract(productDiscountAmt);
                overAdjustAmt = overAdjustAmt.subtract(adjustAmt);
            }
            BigDecimal orderAmt = subOrder.getProductAmt().add(subOrder.getShipAmt())
                    .add(subOrder.getAdjustAmt()).add(subOrder.getServiceAmt())
                    .subtract(subOrder.getOrderDiscountAmt()).subtract(subOrder.getProductDiscountAmt());
            subOrder.setOrderAmt(orderAmt);
            subOrder.setReceivedAmt(orderAmt);
            // (平台售价*数量) - 优惠金额 - 平摊金额 + 调整金额 与 单行实际成交金额 误差在0.1以内

            subOrderItem.setAmtDiscount(subOrder.getProductDiscountAmt());
            subOrderItem.setAdjustAmt(subOrder.getAdjustAmt());
            subOrderItem.setOrderSplitAmt(subOrder.getOrderDiscountAmt());
            // 单行实际成交金额
            subOrderItem.setRealAmt(subOrder.getOrderAmt());
            // 单件实际成交价
            BigDecimal priceSettle = subOrderItem.getRealAmt().divide(subOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP);
            subOrderItem.setPriceActual(priceSettle);
            subOrderItem.setPriceSettle(priceSettle);


            // 王佳要求：单件平台售价 = (成交金额+优惠金额+调整金额+平摊金额)/数量
            BigDecimal totalPrice = subOrderItem.getRealAmt().add(subOrderItem.getOrderSplitAmt()).add(subOrderItem.getAdjustAmt()).add(subOrderItem.getAmtDiscount());
            BigDecimal price = totalPrice.divide(subOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP);
            subOrderItem.setPrice(price);
            subOrder.setAllSku(subOrderItem.getPsCSkuEcode() + "(" + subOrderItem.getQty().intValue() + ")");
            subOrder.setQtyAll(subOrderItem.getQty());
        }
    }

    /**
     * 构建需要生成的周期购子订单信息
     *
     * @param newSubOrderList             新订单集合
     * @param newSubOrderItemList         新订单明细集合
     * @param order                       周期购订单
     * @param orderItem                   周期购订单单个明细
     * @param user                        用户
     * @param cyclePurchaseStrategyResult 已匹配周期购商品策略
     */
    public Date buildCyclePurchaseSubOrder(List<OcBOrder> newSubOrderList,
                                           List<OcBOrderItem> newSubOrderItemList,
                                           List<OcBOccupyTask> newTaskList,
                                           OcBOrder order,
                                           OcBOrderItem orderItem,
                                           User user,
                                           StCCyclePurchaseStrategyResult cyclePurchaseStrategyResult,
                                           int index, Date lastEstimateDeliveryDate) {
        Date now = new Date();
        // 周期购策略总期数
        int qtyCycle = cyclePurchaseStrategyResult.getMain().getQtyCycle();
        if (orderItem.getEstimateConTime() != null && order.getEstimateConTime() == null) {
            order.setEstimateConTime(orderItem.getEstimateConTime());
        }
        List<Date> preDeliveryTimeList = computePreDeliveryTime(order, newSubOrderList, cyclePurchaseStrategyResult, lastEstimateDeliveryDate);

        int currentCycle = 0;
        for (Date preDeliveryTime : preDeliveryTimeList) {
            currentCycle++;
            OcBOrder newSubOrder = new OcBOrder();
            OcBOrderItem newSubOrderItem = new OcBOrderItem();
            BeanUtils.copyProperties(order, newSubOrder);
            newSubOrder.setId(ModelUtil.getSequence("oc_b_order"));
            newSubOrder.setBillNo(sequenceUtil.buildBillNo());
            newSubOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            // 记录【周期购商品策略】主表的ID，作为策略快照，用于后续在重算预计发货时间时获取策略的规则
            newSubOrder.setReserveBigint01(cyclePurchaseStrategyResult.getMain().getId());
            // 记录周期购主订单 原商品编码
            newSubOrder.setReserveVarchar02(orderItem.getPsCProEcode());
            newSubOrder.setSourceBillNo(order.getBillNo());
            newSubOrder.setOrderSourcePlatformEcode(order.getSourceCode());
            // 不继承虚标
            newSubOrder.setIsInvented(0);
            newSubOrder.setPriceLabel(IsActiveEnum.N.getKey());
            newSubOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
            // 主单仓库清空
            newSubOrder.setCpCPhyWarehouseId(null);
            newSubOrder.setCpCPhyWarehouseEname(null);
            newSubOrder.setCpCPhyWarehouseEcode(null);
            newSubOrder.setScanTime(null);
            setOrderBusinessTypeCode(order, newSubOrder, user);
            setOrderDefaultValue(newSubOrder, user, now);
            // 预计发货日期
            newSubOrder.setEstimateConTime(preDeliveryTime);
            // 卡单释放日期
            newSubOrder.setDetentionReleaseDate(preDeliveryTime);
            newSubOrder.setIsDetention(AdvanceConstant.DETENTION_STATUS_1);
            // 当前期数，同明细多数量期数累加
            newSubOrder.setCurrentCycleNumber(index * qtyCycle + currentCycle);
            newSubOrder.setSuffixInfo("CYCLE-" + orderItem.getId() + "-" + index + "-" + newSubOrder.getCurrentCycleNumber());
            // 商品金额：取明细表【成交金额】汇总 ；
            // 订单优惠金额：取明细表【平摊金额】汇总；
            // 商品优惠金额：取明细表【优惠金额】汇总；
            newSubOrder.setShipAmt(BigDecimal.ZERO);
            newSubOrder.setServiceAmt(BigDecimal.ZERO);
            newSubOrderList.add(newSubOrder);
            // 构建明细
            BeanUtils.copyProperties(orderItem, newSubOrderItem);
            newSubOrderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
            newSubOrderItem.setOcBOrderId(newSubOrder.getId());
            // 周期购设置默认值
            setOrderItemDefaultValue(newSubOrderItem, user, now);
            // 周期购设置明细商品信息 0927
            setOrderItemSkuInfo(newSubOrderItem, cyclePurchaseStrategyResult);
            newSubOrderItemList.add(newSubOrderItem);
            newTaskList.add(buildOccupyTask(newSubOrder));
            lastEstimateDeliveryDate = preDeliveryTime;
        }
        return lastEstimateDeliveryDate;

    }

    /**
     * 计算订单预计发货时间
     *
     * @param cyclePurchaseStrategyResult 周期购商品策略
     * @param order                       订单
     * @param newSubOrderList             同行明细的周期购子订单
     * @return 预计发货时间列表
     */
    public List<Date> computePreDeliveryTime(OcBOrder order, List<OcBOrder> newSubOrderList, StCCyclePurchaseStrategyResult cyclePurchaseStrategyResult, Date lastEstimateDeliveryDate) {
        int qtyCycle = cyclePurchaseStrategyResult.getMain().getQtyCycle();
        Date start = order.getPayTime();
        boolean dyFirst = false;
        // 判断策略是顺延还是并行 1并行 2顺延
        if (OmsParamConstant.TWO.equals(cyclePurchaseStrategyResult.getMain().getSendPattern())) {
            if (CollectionUtils.isEmpty(newSubOrderList)) {
                // 如果存在相同收货人相同周期购商品 起始日期要使用最近的一条预计提货时间
                OcBOrder lastOrder = getLastCyclePurchaseOrderByUserNick(order, cyclePurchaseStrategyResult.getMain().getPsCProEcode());
                if (lastOrder != null) {
                    start = lastOrder.getEstimateConTime();
                }
            } else {
                // 同行明细顺延模式起始日期使用之前数据
                start = newSubOrderList.get(newSubOrderList.size() - 1).getEstimateConTime();
            }
        }
        // 判断是否是抖音
        if (PlatFormEnum.DOU_YIN.getCode().equals(order.getPlatform()) || PlatFormEnum.HONGSHU_OPEN.getCode().equals(order.getPlatform())
                || PlatFormEnum.KUAISHOU.getCode().equals(order.getPlatform())) {
            if (lastEstimateDeliveryDate == null) {
                if (order.getEstimateConTime() != null) {
                    start = order.getEstimateConTime();
                } else {
                    start = order.getPayTime();
                }
                // start减去72小时
                start = DateUtil.offset(start, DateField.HOUR, -72);
                dyFirst = true;
            } else {
                start = lastEstimateDeliveryDate;
            }
        }
        return doComputePreDeliveryTime(cyclePurchaseStrategyResult, qtyCycle, start, dyFirst);
    }

    /**
     * 计算预计发货时间
     *
     * @param cyclePurchaseStrategyResult 周期购商品策略
     * @param cycleQty                    剩余期数(策略总期数 - 已发货期数)
     * @param start                       起始日期
     * @return 预计发货时间列表
     */
    public List<Date> doComputePreDeliveryTime(StCCyclePurchaseStrategyResult cyclePurchaseStrategyResult, int cycleQty, Date start, boolean dyFirst) {
        List<Date> dateList = new ArrayList<>();
        // 周期类型 1指定星期 2每月几日 3间隔天数
        String cycleType = cyclePurchaseStrategyResult.getMain().getCycleType();
        int typeValue = cyclePurchaseStrategyResult.getMain().getType();
        LocalDateTime localDateTime = LocalDateTime.from(start.toInstant().atZone(ZoneId.systemDefault()));
        for (int i = 0; i < cycleQty; i++) {
            if (dyFirst && i == 0) {
                dateList.add(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
                continue;
            }
            if (OmsParamConstant.ONE.equals(cycleType)) {
                // 1指定星期
                localDateTime = localDateTime.with(TemporalAdjusters.next(DayOfWeek.of(typeValue)));
            } else if (OmsParamConstant.TWO.equals(cycleType)) {
                // 2每月几日
                int currentDay = localDateTime.getDayOfMonth();
                // 如果当前日期大于等于设定日期 或第二次loop 或当前已经是本月最后一天 月份+1
                if (currentDay >= typeValue || i > 0 || currentDay == localDateTime.getMonth().length(localDateTime.toLocalDate().isLeapYear())) {
                    localDateTime = localDateTime.plusMonths(1);
                }
                // 当月最大天数
                int maxDayOfMonth = localDateTime.getMonth().length(localDateTime.toLocalDate().isLeapYear());
                if(typeValue > maxDayOfMonth){
                    localDateTime = localDateTime.withDayOfMonth(maxDayOfMonth);
                }else{
                    localDateTime = localDateTime.withDayOfMonth(typeValue);
                }
            }else {
                // 3间隔天数
                localDateTime = localDateTime.plusDays(typeValue);
            }
            dateList.add(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        }
        return dateList;
    }

    /**
     * 根据手机号匹配同一收货人最后一期周期购发货时间
     * @param currentOrder 当前周期购主订单
     * @param spu 当前周期购虚拟商品
     * @return 已存在的周期购子订单最后一期的发货时间
     */
    public OcBOrder getLastCyclePurchaseOrderByUserNick(OcBOrder currentOrder, String spu){
        if(StringUtils.isBlank(currentOrder.getUserNick())){
            return null;
        }
        // 平台+店铺+买家昵称 状态=待寻源 原商品=当前虚拟商品 取发货时间最新的一条
        Long orderId = ES4Order.searchCyclePurchaseOrderId(currentOrder.getUserNick(), currentOrder.getPlatform(), currentOrder.getCpCShopId(), spu);
        if(orderId != null){
            return orderMapper.selectById(orderId);
        }
        return null;
    }
    public StCCyclePurchaseStrategyResult matchCyclePurchaseStrategy(String spu, Date payTime){
        // 1.根据spu查询已审核周期购策略
        List<StCCyclePurchaseStrategyResult> cyclePurchaseStrategyResultList = stRpcService.queryCyclePurchaseStrategyByProCode(spu);
        if(CollectionUtils.isEmpty(cyclePurchaseStrategyResultList)){
            return null;
        }
        // 2.从返回列表中获取日期符合的一条数据
        for (StCCyclePurchaseStrategyResult cyclePurchaseStrategyResult : cyclePurchaseStrategyResultList) {
            StCCyclePurchaseStrategy cyclePurchaseStrategy = cyclePurchaseStrategyResult.getMain();
            if(cyclePurchaseStrategy.getStartDate() != null && cyclePurchaseStrategy.getEndDate() != null){
                if(payTime.getTime() >= cyclePurchaseStrategy.getStartDate().getTime()
                        && payTime.getTime() <= cyclePurchaseStrategy.getEndDate().getTime()){
                    return cyclePurchaseStrategyResult;
                }
            }
        }
        return null;
    }

    public ValueHolderV14<String> updatePreDeliveryTime(JSONObject obj, User user){

        ValueHolderV14<String> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "修改成功");

        JSONArray idArr = obj.getJSONArray("orderIds");
        Date estimateConTime = obj.getDate("estimateConTime");
        List<OcBOrder> orderList = orderMapper.selectByIdsList(idArr.toJavaList(Long.TYPE));
        int success = 0;
        List<String> failList = new ArrayList<>();
        for (OcBOrder order : orderList) {
            try{
                doUpdatePreDeliveryTime(order, user, estimateConTime);
                success++;
            }catch (Exception e){
                log.error("更新预计发货时间失败",e);
                failList.add(order.getBillNo());
            }
        }
        if(success==0){
            String mesage = "全部修改失败！";
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage(mesage);
        }else if(success<orderList.size()){
            String mesage = "修改成功数量："+success+",失败数量："+(orderList.size()-success);
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage(mesage);
        }else{
            String mesage = "全部修改成功";
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage(mesage);
        }
        return v14;
    }
    public void doUpdatePreDeliveryTime(OcBOrder order, User user, Date estimateConTime) throws InterruptedException {
        String tag = "订单" + order.getBillNo();
        // 校验订单
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(order.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(bllRedisLockOrderUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                Date now = new Date();
                List<OcBOrder> updateOrderList = new ArrayList<>();
                List<OcBOccupyTask> updateTaskList = new ArrayList<>();

                if(!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus())){
                    throw  new NDSException(tag+"状态为非待寻源");
                }
                if(order.getEstimateConTime() == null){
                    throw  new NDSException(tag+"预计发货时间不能为空");
                }
                OcBOrder update  = new OcBOrder();
                update.setId(order.getId());
                setOrderUpdateDefaultValue(update, user, now);
                // 预计发货日期
                update.setEstimateConTime(estimateConTime);
                updateOrderList.add(update);

                OcBOccupyTask task = new OcBOccupyTask();
                task.setOrderId(order.getId());
                task.setNextTime(estimateConTime);
                updateTaskList.add(task);

                GenerateCyclePurchaseSubOrderService service = ApplicationContextHandle.getBean(GenerateCyclePurchaseSubOrderService.class);
                service.doUpdateOrderAndTask(updateOrderList, updateTaskList);

                // 修改完订单的预计发货时间之后，检查是否需要自动顺延后续期数
                autoDelaySubsequentOrders(order, user, estimateConTime);
            }
        }finally {
            redisLock.unlock();
        }
    }

    /**
     * 判断之前订单是否发货，更新卡单时间
     * @param order 周期购子订单第n期订单
     * @param operateUser user
     * @return true=允许执行寻源
     */
    public boolean updatePreDeliveryTimeByOccupy(OcBOrder order, User operateUser){
        try {
            List<OcBOrder> ocBOrderList = orderMapper.selectDeliveryByTidAndCycleNumber(order.getTid(), order.getCurrentCycleNumber() - 1);
            if (CollectionUtils.isNotEmpty(ocBOrderList)) {
                return true;
            }
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.OCCUPY.getKey(), "OrderId=" + order.getId() + "寻源占单失败，中台周期购订单上一期未发货", "", "", operateUser);
            Long cyclePurchaseStrategyId = order.getReserveBigint01();
            AssertUtils.notNull(cyclePurchaseStrategyId, "周期购商品策略ID不能为空");
            List<Long> subOrderIds = ES4Order.searchCyclePurchaseSubOrderIds(order.getSourceBillNo(), null);
            List<OcBOrder> subOrderList = orderMapper.selectByIdsList(subOrderIds);
            // 大于等于本期的子订单延期
            subOrderList = subOrderList.stream().filter(o -> o.getCurrentCycleNumber() != null && o.getCurrentCycleNumber() >= order.getCurrentCycleNumber()).collect(Collectors.toList());
            // 使用之前的周期购策略
            StCCyclePurchaseStrategyResult cyclePurchaseStrategyResult = stRpcService.queryCyclePurchaseStrategyById(cyclePurchaseStrategyId);
            AssertUtils.notNull(cyclePurchaseStrategyResult, "周期购商品策略不存在");
            // 根据期数进行分组
            Map<Integer, List<OcBOrder>> subOrderMap = subOrderList.stream().collect(Collectors.groupingBy(OcBOrder::getCurrentCycleNumber));
            List<Date> preDeliveryTimeList = doComputePreDeliveryTime(cyclePurchaseStrategyResult, subOrderMap.size(), order.getEstimateConTime(), false);
            List<OcBOrder> updateOrderList = new ArrayList<>();
            List<OcBOccupyTask> updateTaskList = new ArrayList<>();
            Date now = new Date();
            int index = 0;
            Set<Integer> cycleNumberSet = subOrderMap.keySet();
            List<Integer> cycleNumberList = new ArrayList<>(cycleNumberSet);
            Collections.sort(cycleNumberList);
            for (Date preDeliveryTime : preDeliveryTimeList) {
                // 按照期数来取订单
                Integer cycleNumber = cycleNumberList.get(index);
                List<OcBOrder> subOrderListByCycleNumber = subOrderMap.get(cycleNumber);
                for (OcBOrder subOrder : subOrderListByCycleNumber) {
                    OcBOrder update = new OcBOrder();
                    update.setId(subOrder.getId());
                    setOrderUpdateDefaultValue(update, operateUser, now);
                    // 预计发货日期
                    update.setEstimateConTime(preDeliveryTime);
                    // 卡单释放日期
                    update.setDetentionReleaseDate(preDeliveryTime);
                    updateOrderList.add(update);
                    OcBOccupyTask task = new OcBOccupyTask();
                    task.setOrderId(subOrder.getId());
                    task.setNextTime(preDeliveryTime);
                    task.setStatus(0);
                    updateTaskList.add(task);
                }
                index++;
            }
            GenerateCyclePurchaseSubOrderService service = ApplicationContextHandle.getBean(GenerateCyclePurchaseSubOrderService.class);
            service.doUpdateOrderAndTask(updateOrderList, updateTaskList);
        } catch (Exception e) {
            log.error(LogUtil.format("updatePreDeliveryTimeByOccupy.error {}"), Throwables.getStackTraceAsString(e));
        }
        return false;
    }
    @Transactional(rollbackFor = Exception.class)
    public void doUpdateOrderAndTask(List<OcBOrder> updateOrderList, List<OcBOccupyTask> updateTaskList){
        for (OcBOrder order : updateOrderList) {
            orderMapper.updateById(order);
        }
        for (OcBOccupyTask task : updateTaskList) {
            occupyTaskMapper.update(task, new UpdateWrapper<OcBOccupyTask>().lambda().eq(OcBOccupyTask::getOrderId, task.getOrderId()));
        }
    }
    /**
     * 判断订单是否周期购订单
     *
     * @param orderInfo 订单信息
     * @return true=周期购
     */
    private boolean checkIsCyclePurchaseOrder(OcBOrder orderInfo) {
        return OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(orderInfo.getBusinessTypeCode())
                || OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER.getCode().equals(orderInfo.getBusinessTypeCode());
    }
    /**
     * 周期购批量更新平台发货
     * @param ids 订单ID
     * @param user 用户
     * @param user 用户
     */
    public List<OcBOrder> platformSend(Set<Long> ids, User user, Date now){
        if(CollectionUtils.isEmpty(ids)){
            return null;
        }
        List<OcBOrder> orderList = new ArrayList<>();
        for (Long id : ids) {
            OcBOrder order = new OcBOrder();
            order.setId(id);
            order.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            setOrderUpdateDefaultValue(order, user, now);
            orderList.add(order);
        }
        return orderList;
    }
    public void setOrderDefaultValue(OcBOrder order, User user, Date now){
        order.setSysremark("");
        order.setOwnerid(Long.valueOf(user.getId()));
        order.setOwnername(user.getName());
        order.setOwnerename(user.getEname());
        order.setCreationdate(now);
        order.setModifierid(Long.valueOf(user.getId()));
        order.setModifierename(user.getEname());
        order.setModifiername(user.getName());
        order.setModifieddate(now);
    }
    public void setOrderItemDefaultValue(OcBOrderItem orderItem, User user, Date now){
        orderItem.setOwnerid(Long.valueOf(user.getId()));
        orderItem.setOwnername(user.getName());
        orderItem.setOwnerename(user.getEname());
        orderItem.setCreationdate(now);
        orderItem.setModifierid(Long.valueOf(user.getId()));
        orderItem.setModifierename(user.getEname());
        orderItem.setModifiername(user.getName());
        orderItem.setModifieddate(now);
    }
    public void setOrderItemSkuInfo(OcBOrderItem orderItem, StCCyclePurchaseStrategyResult cyclePurchaseStrategyResult){
        StCCyclePurchaseStrategyItem cyclePurchaseStrategyItem = cyclePurchaseStrategyResult.getItemList().get(0);
        String sku = cyclePurchaseStrategyItem.getPsCSkuEcode();
        ProductSku productSku = psRpcService.selectProductSku(sku);
        AssertUtils.notNull(productSku, "策略商品" + cyclePurchaseStrategyResult.getMain().getPsCProEcode() + ",条码" + sku + "不存在");
        orderItem.setPsCBrandId(productSku.getPsCBrandId());
        orderItem.setPsCProId(productSku.getProdId());
        orderItem.setPsCProEcode(productSku.getProdCode());
        orderItem.setPsCProEname(productSku.getName());
        orderItem.setPsCSkuId(productSku.getId());
        orderItem.setPsCSkuEcode(productSku.getSkuEcode());
        orderItem.setPsCSkuEname(productSku.getSkuName());
        orderItem.setPsCProMaterieltype(productSku.getMaterialType());
        orderItem.setPsCProSupplyType(productSku.getPsCProSupplyType());
        orderItem.setPsCSizeId(productSku.getSizeId());
        orderItem.setPsCSizeEcode(productSku.getSizeCode());
        orderItem.setPsCSizeEname(productSku.getSizeName());
        orderItem.setPsCClrId(productSku.getColorId());
        orderItem.setPsCClrEcode(productSku.getColorCode());
        orderItem.setPsCClrEname(productSku.getColorName());
        orderItem.setStandardWeight(productSku.getWeight());
        // 增加品类信息 20220923
        orderItem.setMDim4Id(productSku.getMDim4Id());
        orderItem.setMDim6Id(productSku.getMDim6Id());
        if ("Y".equals(productSku.getIsEnableExpiry())) {
            orderItem.setIsEnableExpiry(1);
        } else {
            orderItem.setIsEnableExpiry(0);
        }
        orderItem.setQty(new BigDecimal(cyclePurchaseStrategyItem.getQty()+""));
    }
    public void setOrderBusinessTypeCode(OcBOrder order, OcBOrder newOrder, User user){
        OrderBusinessTypeCodeEnum orderBusinessTypeCodeEnum;
        if(OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(order.getBusinessTypeCode())){
            orderBusinessTypeCodeEnum = OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP;
        }else{
            orderBusinessTypeCodeEnum = OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER_PICK_UP;
        }
        List<StCBusinessType> stBusinessTypes = typeMapper.selectStCBusinessTypeByCode(orderBusinessTypeCodeEnum.getCode());
        if (CollectionUtils.isEmpty(stBusinessTypes)) {
            throw new NDSException(Resources.getMessage("周期购提货业务类型信息获取失败！!", user.getLocale()));
        }
        StCBusinessType stBusinessType = stBusinessTypes.get(0);
        newOrder.setBusinessTypeId(stBusinessType.getId());
        newOrder.setBusinessTypeCode(stBusinessType.getEcode());
        newOrder.setBusinessTypeName(stBusinessType.getEname());
    }
    private void setOrderUpdateDefaultValue(OcBOrder order, User user, Date now){
        order.setModifierid(Long.valueOf(user.getId()));
        order.setModifierename(user.getEname());
        order.setModifiername(user.getName());
        order.setModifieddate(now);
    }
    public OcBOccupyTask buildOccupyTask(OcBOrder order){
        OcBOccupyTask task = new OcBOccupyTask();
        task.setId(ModelUtil.getSequence("oc_b_occupy_task"));
        task.setStatus(0);
        task.setShopId(order.getCpCShopId());
        task.setRetryNumber(0);
        task.setNextTime(order.getDetentionReleaseDate());
        task.setOrderId(order.getId());
        task.setCreationdate(new Date());
        return task;
    }

    /**
     * 自动顺延后续期数订单的预计发货时间
     *
     * @param currentOrder 当前修改的订单
     * @param user 操作用户
     * @param newEstimateConTime 新的预计发货时间
     */
    public void autoDelaySubsequentOrders(OcBOrder currentOrder, User user, Date newEstimateConTime) {
        try {
            // 获取自动顺延间隔天数配置
            Integer delayDays = businessSystemParamService.getCyclePurchaseAutoDelayDays();
            if (delayDays == null) {
                log.info("中台周期购自动顺延功能未启用，订单ID={}", currentOrder.getId());
                return;
            }

            // 判断该平台单号是否有订单状态=待寻源且大于本期的提货单
            List<OcBOrder> subsequentOrders = findSubsequentPendingOrders(currentOrder);
            if (CollectionUtils.isEmpty(subsequentOrders)) {
                log.info("没有找到后续期数的待寻源订单，订单ID={}，平台单号={}",
                        currentOrder.getId(), currentOrder.getSourceBillNo());
                return;
            }

            log.info("找到{}个后续期数的待寻源订单，开始自动顺延，订单ID={}，平台单号={}，顺延天数={}",
                    subsequentOrders.size(), currentOrder.getId(), currentOrder.getSourceBillNo(), delayDays);

            // 批量修改后续期数的"主表预计发货时间"
            batchUpdateSubsequentOrdersDeliveryTime(subsequentOrders, user, delayDays, currentOrder.getCurrentCycleNumber(), newEstimateConTime);

        } catch (Exception e) {
            log.error("自动顺延后续期数订单失败，订单ID={}，异常信息={}", currentOrder.getId(), e.getMessage(), e);
        }
    }

    /**
     * 查找后续期数的待寻源订单
     * 参考 updatePreDeliveryTimeByOccupy 方法的逻辑
     *
     * @param currentOrder 当前订单
     * @return 后续期数的待寻源订单列表
     */
    private List<OcBOrder> findSubsequentPendingOrders(OcBOrder currentOrder) {
        try {
            // 根据平台单号查询所有周期购子订单
            List<Long> subOrderIds = ES4Order.searchCyclePurchaseSubOrderIds(currentOrder.getSourceBillNo(), null);
            if (CollectionUtils.isEmpty(subOrderIds)) {
                return new ArrayList<>();
            }

            List<OcBOrder> subOrderList = orderMapper.selectByIdsList(subOrderIds);
            if (CollectionUtils.isEmpty(subOrderList)) {
                return new ArrayList<>();
            }

            // 筛选出状态=待寻源且期数大于本期的订单
            return subOrderList.stream()
                    .filter(order -> order.getCurrentCycleNumber() != null
                            && order.getCurrentCycleNumber() > currentOrder.getCurrentCycleNumber()
                            && OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查找后续期数待寻源订单失败，订单ID={}，异常信息={}", currentOrder.getId(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量修改后续期数订单的预计发货时间
     * 按期数+业务系统参数顺延
     *
     * @param subsequentOrders 后续期数订单列表
     * @param user 操作用户
     * @param delayDays 顺延天数
     * @param currentCycleNumber 当前修改订单的期数
     * @param currentEstimateConTime 当前修改订单的新预计发货时间（作为基准时间）
     */
    private void batchUpdateSubsequentOrdersDeliveryTime(List<OcBOrder> subsequentOrders, User user, Integer delayDays, Integer currentCycleNumber, Date currentEstimateConTime) {
        try {
            Date now = new Date();
            List<OcBOrder> updateOrderList = new ArrayList<>();
            List<OcBOccupyTask> updateTaskList = new ArrayList<>();

            // 验证参数
            if (currentCycleNumber == null) {
                log.warn("当前期数参数为空，跳过自动顺延");
                return;
            }
            if (currentEstimateConTime == null) {
                log.warn("当前预计发货时间参数为空，跳过自动顺延");
                return;
            }

            // 按期数分组
            Map<Integer, List<OcBOrder>> ordersByCycle = subsequentOrders.stream()
                    .collect(Collectors.groupingBy(OcBOrder::getCurrentCycleNumber));

            // 按期数排序处理
            List<Integer> sortedCycles = ordersByCycle.keySet().stream()
                    .sorted()
                    .collect(Collectors.toList());

            for (Integer cycleNumber : sortedCycles) {
                List<OcBOrder> ordersInCycle = ordersByCycle.get(cycleNumber);

                for (OcBOrder order : ordersInCycle) {
                    // 计算实际期数差
                    int actualCycleDiff = cycleNumber - currentCycleNumber;
                    if (actualCycleDiff <= 0) {
                        log.warn("期数差计算异常，当前期数={}，目标期数={}，跳过订单ID={}",
                                currentCycleNumber, cycleNumber, order.getId());
                        continue;
                    }

                    // 计算新的预计发货时间：本期预计发货时间 + (实际期数差 * 顺延天数)
                    // 使用本期订单的新预计发货时间作为基准，而不是后续订单的原预计发货时间
                    long delayMillis = (long) actualCycleDiff * delayDays * 24 * 60 * 60 * 1000L;
                    Date newEstimateTime = new Date(currentEstimateConTime.getTime() + delayMillis);

                    // 准备更新订单
                    OcBOrder updateOrder = new OcBOrder();
                    updateOrder.setId(order.getId());
                    setOrderUpdateDefaultValue(updateOrder, user, now);
                    updateOrder.setEstimateConTime(newEstimateTime);
                    updateOrderList.add(updateOrder);

                    // 准备更新寻源任务
                    OcBOccupyTask task = new OcBOccupyTask();
                    task.setOrderId(order.getId());
                    task.setNextTime(newEstimateTime);
                    updateTaskList.add(task);

                    log.info("准备顺延订单，订单ID={}，当前期数={}，目标期数={}，实际期数差={}，本期基准时间={}，新预计发货时间={}",
                            order.getId(), currentCycleNumber, cycleNumber, actualCycleDiff, currentEstimateConTime, newEstimateTime);
                }
            }

            // 批量更新
            if (!updateOrderList.isEmpty()) {
                GenerateCyclePurchaseSubOrderService service = ApplicationContextHandle.getBean(GenerateCyclePurchaseSubOrderService.class);
                service.doUpdateOrderAndTask(updateOrderList, updateTaskList);

                log.info("成功自动顺延{}个后续期数订单的预计发货时间", updateOrderList.size());
            }

        } catch (Exception e) {
            log.error("批量修改后续期数订单预计发货时间失败，异常信息={}", e.getMessage(), e);
        }
    }
}
