package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutSaveRequest;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCRegionRelation;
import com.jackrain.nea.cpext.model.table.CpCVipcomWahouse;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.cpext.model.table.TOmsvipfulladdress;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.exception.UpdateException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.GsiIpBTimeOrderVipOrderSnMapper;
import com.jackrain.nea.oc.oms.mapper.IpBCancelTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryRecordMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipOccupyItemMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBJitxDealerOrderTaskMapper;
import com.jackrain.nea.oc.oms.model.SendPlanExecution;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderOccupyItemStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.result.WareHouseResult;
import com.jackrain.nea.oc.oms.model.table.IpBCancelTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDelivery;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryRecord;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipItem;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBJitxDealerOrderTask;
import com.jackrain.nea.oc.oms.services.time.order.IpVipTimeOrderOccupyItemService;
import com.jackrain.nea.oc.oms.services.time.order.SubWarehouseService;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.st.model.request.StStockPriorityRequest;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.vo.StCSyncStockStrategyVo;
import com.jackrain.nea.st.service.OmsQueryWareHouseService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.vo.PhyWarehouseVo;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description JITX退款转换服务类
 * @Date 2019-6-26
 **/
@Component
@Slf4j
public class IpVipTimeOrderService {

    @Autowired
    private IpBTimeOrderVipMapper ipBTimeOrderVipMapper;
    @Autowired
    private IpBCancelTimeOrderVipMapper cancelTimeOrderVipMapper;
    @Autowired
    private IpBTimeOrderVipItemMapper ipBTimeOrderVipItemMapper;
    @Autowired
    private IpBTimeOrderVipOccupyItemMapper ipBTimeOrderVipOccupyItemMapper;
    @Autowired
    private OmsSyncStockStrategyService omsSyncStockStrategyService;
    @Autowired
    private BasicCpQueryService basicCpQueryService;
    @Autowired
    private OmsQueryWareHouseService omsQueryWareHouseService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private IpJitxDeliveryService ipJitxDeliveryService;
    @Autowired
    private SendPlanService sendPlanService;
    @Autowired
    private GsiIpBTimeOrderVipOrderSnMapper gsiIpBTimeOrderVipOrderSnMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private IpVipTimeOrderItemService ipVipTimeOrderItemService;
    @Autowired
    private SubWarehouseService subWarehouseService;
    @Autowired
    private IpVipTimeOrderOccupyItemService timeOrderOccupyItemService;
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;
    @Autowired
    private PropertiesConf propertiesConf;
//    @Autowired
//    private R3MqSendHelper r3MqSendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private IpBJitxDeliveryRecordMapper deliveryRecordMapper;

    @Autowired
    private OcBJitxDealerOrderTaskMapper dealerOrderTaskMapper;

    /**
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @Description 查询ES分库键
     * @Date 2019-8-19
     * @Param [pageIndex, pageSize]
     **/
    public List<String> selectTimeOrderEs(int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("STATUS", TimeOrderVipStatusEnum.CREATED.getValue());
            whereKeys.put("ISTRANS", 0);

            String[] returnFieldNames = new String[]{"OCCUPIED_ORDER_SN"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                    OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("OCCUPIED_ORDER_SN");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            log.error(this.getClass().getName() + " 查询未转换已创建订单失败！");
        }
        return orderNoList;
    }

    /**
     * 查询ES分库键
     *
     * @param pageIndex
     * @param pageSize
     * @param isTrans
     * @param maxCompensationTime 最大补偿次数
     * @param status              时效订单状态
     * @return
     */
    public List<String> selectTimeOrderEs(int pageIndex, int pageSize, long currentTimeMillis, int isTrans, int maxCompensationTime
            , int status) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            if (isTrans != -1) {
                whereKeys.put("ISTRANS", isTrans);
            }
            if (status != -1) {
                whereKeys.put("STATUS", status);
            }

            JSONObject filterKeys = new JSONObject();
            // 时间条件，只捞取小于当前时间的
            filterKeys.put("NEXT_COMPENSATION_DATE", "~" + currentTimeMillis);
            // 小于最大补偿次数
            filterKeys.put("COMPENSATION_TIME", "~" + maxCompensationTime);

            String[] returnFieldNames = new String[]{"OCCUPIED_ORDER_SN"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                    OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                    whereKeys, filterKeys, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("OCCUPIED_ORDER_SN");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            log.error(this.getClass().getName() + " 查询未转换已创建订单失败！");
        }
        return orderNoList;
    }

    /**
     * @return com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation
     * <AUTHOR>
     * @Description 查询转单关系类
     * @Date 2019-8-19
     * @Param [occupiedOrderSn]
     **/
    public IpVipTimeOrderRelation selectTimeOrder(String occupiedOrderSn) {
        IpBTimeOrderVip orderInfo = this.ipBTimeOrderVipMapper.selectTimeOrderByOccupiedOrderSn(occupiedOrderSn);

        if (orderInfo == null) {
            return null;
        }
        IpVipTimeOrderRelation ipVipTimeOrderRelation = new IpVipTimeOrderRelation();
        long orderInfoId = orderInfo.getId();
        List<IpBTimeOrderVipItemEx> orderItemList = this.ipBTimeOrderVipItemMapper.selectOrderItemList(orderInfoId);

        List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipOccupyItems = this.ipBTimeOrderVipOccupyItemMapper.selectOrderOccupyItemList(orderInfoId);

        ipVipTimeOrderRelation.setIpBTimeOrderVip(orderInfo);
        ipVipTimeOrderRelation.setIpBTimeOrderVipItemExList(orderItemList);
        ipVipTimeOrderRelation.setIpBTimeOrderVipOccupyItemList(ipBTimeOrderVipOccupyItems);
        // 取消时效订单
        IpBCancelTimeOrderVip cancelTimeOrderVip = cancelTimeOrderVipMapper
                .selectCancelTimeOrderByOccupiedOrderSn(occupiedOrderSn);
        ipVipTimeOrderRelation.setCancelTimeOrderVip(cancelTimeOrderVip);
        return ipVipTimeOrderRelation;
    }

    public List<IpBTimeOrderVip> listBatchTimeOrderByOccupiedOrderSns(List<String> occupiedOrderSns) {
        return this.ipBTimeOrderVipMapper.listTimeOrderByOccupiedOrderSns(occupiedOrderSns);
    }

    public int updateVipOrder(String occupiedOrderSn, User user, Date currentDate, String remark
            , Date nextCompensationTime, Integer status, Integer isTrans) {
        return this.ipBTimeOrderVipMapper.updateVipOrder(occupiedOrderSn
                , user.getId().longValue()
                , user.getName()
                , user.getEname()
                , currentDate
                , remark
                , nextCompensationTime
                , status, isTrans
        );
    }

    /**
     * 更新中间表订单状态值
     *
     * @param occupiedOrderSn     库存占用单号
     * @param transferOrderStatus 转换订单状态
     * @param remarks             备注信息
     * @return 更新是否成功。true-成功；false-失败
     */
    public boolean updateTimeOrderTransStatus(String occupiedOrderSn, TransferOrderStatus transferOrderStatus,
                                              String remarks) {
        boolean isUpdateTransNum = transferOrderStatus == TransferOrderStatus.TRANSFERRED;
        if (remarks != null && remarks.length() > IpBTimeOrderVipMapper.MAX_REMARK_LENGTH) {
            remarks = remarks.substring(0, IpBTimeOrderVipMapper.MAX_REMARK_LENGTH - 1);
        }
        int result = this.ipBTimeOrderVipMapper.updateTimeOrderIsTrans(occupiedOrderSn, transferOrderStatus.toInteger(),
                isUpdateTransNum, remarks);
        if (result > 0) {
            this.updateTimeOrderES(occupiedOrderSn);
        }
        return result > 0;
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 中间表信息推送ES
     * @Date 2019-8-20
     * @Param [orderSn]
     **/
    public void updateTimeOrderES(String occupiedOrderSn) {
        String indexName = OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME;
        String typeName = OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME;
        String itemTypeName = OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_ITEM_TYPE_NAME;
        IpVipTimeOrderRelation ipVipTimeOrderRelation = this.selectTimeOrder(occupiedOrderSn);
//        try {
//            if (!SpecialElasticSearchUtil.indexExists(indexName)) {
//                SpecialElasticSearchUtil.indexCreate(IpBTimeOrderVipItem.class, IpBTimeOrderVip.class);
//            }
//            SpecialElasticSearchUtil.indexDocument(indexName, typeName, ipVipTimeOrderRelation.getIpBTimeOrderVip(),
//                    ipVipTimeOrderRelation.getOrderId());
//            if (ipVipTimeOrderRelation.getIpBTimeOrderVipItemExList() != null) {
//                SpecialElasticSearchUtil.indexDocuments(indexName, itemTypeName,
//                        ipVipTimeOrderRelation.getIpBTimeOrderVipItemExList(), "IP_B_TIME_ORDER_VIP_ID");
//            }
//        } catch (IOException e) {
//            log.error(this.getClass().getName() + " 时效订单主表推送ES失败，{}", e);
//            e.printStackTrace();
//        }
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 更新时效订单主表并推送ES
     * @Date 2019-9-5
     * @Param [ipBTimeOrderVip, wrapper]
     **/
    public void updateTimeOrder(String occupiedOrderSn, IpBTimeOrderVip ipBTimeOrderVip, Wrapper wrapper) {
        int result = ipBTimeOrderVipMapper.update(ipBTimeOrderVip, wrapper);
        if (result > 0) {
            updateTimeOrderES(occupiedOrderSn);
        }
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 更新时效订单子表并推送ES
     * @Date 2019-9-5
     * @Param [occupiedOrderSn, ipBTimeOrderVipItemEx, wrapper]
     **/
    public void updateTimeOrderItem(String occupiedOrderSn, IpBTimeOrderVipItemEx ipBTimeOrderVipItemEx, Wrapper wrapper) {
        int result = ipBTimeOrderVipItemMapper.update(ipBTimeOrderVipItemEx, wrapper);
        if (result > 0) {
            this.updateTimeOrderES(occupiedOrderSn);
        }
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新异常转换状态
     * @Date 2019-9-2
     * @Param [ipBTimeOrderVip, error]
     **/
    public boolean updateIsTransError(IpBTimeOrderVip ipBTimeOrderVip, String error) {
        IpVipTimeOrderService bean = ApplicationContextHandle.getBean(IpVipTimeOrderService.class);
        String sysRemark = SysNotesConstant.SYS_REMARK0;
        //异常信息超过500 截取500
        String str = sysRemark + error;
        if (str.length() > 500) {
            str = str.substring(0, 500);
        }
        boolean flag = bean.updateTimeOrderTransferStatus(TransferOrderStatus.TRANSFERFAIL.toInteger(), str, ipBTimeOrderVip);
        return flag;
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新异常转换状态和异常类型
     * @Date 2021-06-03
     * @Param [ipBTimeOrderVip, error]
     **/
    public boolean updateIsTransException(IpBTimeOrderVip ipBTimeOrderVip, String error) {
        IpVipTimeOrderService bean = ApplicationContextHandle.getBean(IpVipTimeOrderService.class);
        String sysRemark = SysNotesConstant.SYS_REMARK0;
        //异常信息超过500 截取500
        String str = sysRemark + error;
        if (str.length() > 500) {
            str = str.substring(0, 500);
        }
        boolean flag = bean.updateTimeOrderIsTransAndExceptionType(TransferOrderStatus.TRANSFERFAIL.toInteger(), str, ipBTimeOrderVip);
        return flag;
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新转换状态方法
     * @Date 2019-9-2
     * @Param [istrans, sysRemark, ipBTimeOrderVip]
     **/
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTimeOrderTransferStatus(int istrans, String sysRemark, IpBTimeOrderVip ipBTimeOrderVip) {
        try {
            int i = ipBTimeOrderVipMapper.updateTimeOrderIsTrans(ipBTimeOrderVip.getOccupiedOrderSn(),
                    istrans, true, sysRemark);
            //推送es
            if (i > 0) {
                return true;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新唯品会时效订单中间表失败,occupiedOrderSn:{}", ipBTimeOrderVip.getOccupiedOrderSn(), e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新转换状态和异常类型
     * @Date 2021/06/03
     * @Param [istrans, sysRemark, ipBTimeOrderVip]
     **/
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTimeOrderIsTransAndExceptionType(int istrans, String sysRemark, IpBTimeOrderVip ipBTimeOrderVip) {
        try {

            int i = ipBTimeOrderVipMapper.updateTimeOrderIsTransAndExceptionType(ipBTimeOrderVip.getOccupiedOrderSn(),
                    istrans, true, sysRemark,ipBTimeOrderVip.getExceptionType());
            //推送es
            if (i > 0) {
                return true;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新唯品会时效订单中间表失败,occupiedOrderSn:{}", ipBTimeOrderVip.getOccupiedOrderSn(), e);
            throw new NDSException(e);
        }
        return false;
    }
    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新转换状态方法
     * @Date 2019-9-2
     * @Param [istrans, sysRemark, ipBTimeOrderVip]
     **/
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTimeOrderTransAndOutStockQty(IpBTimeOrderVip ipBTimeOrderVip
            , IpBTimeOrderVipOccupyItem occupyItem, User user) {
        int record = 0;
        int vipTimeOrderRecord = 0;
        Date date = new Date();
        record = ipBTimeOrderVipOccupyItemMapper.updateByCondition(occupyItem.getId(), occupyItem.getStatus()
                , null, null, occupyItem.getAmount(), null, null
                , Long.valueOf(user.getId()), user.getEname(), user.getName(), date);
        if (record <= 0) {
            log.error("{}.updateTimeOrderTransAndOutStockQty 时效订单库存占用明细修改失败,param:{}"
                    , this.getClass().getSimpleName(), JSON.toJSONString(occupyItem));
            throw new UpdateException("时效订单明细修改失败");
        }
        vipTimeOrderRecord = ipBTimeOrderVipMapper.updateTimeOrderTransAndOutStockQty(ipBTimeOrderVip.getOccupiedOrderSn(),
                ipBTimeOrderVip.getIstrans(), ipBTimeOrderVip.getOutStockQuantity(), ipBTimeOrderVip.getSysremark());
        if (vipTimeOrderRecord <= 0) {
            log.error("{}.updateTimeOrderTransAndOutStockQty.updateTimeOrderTransAndOutStockQty 时效订单修改失败,param:{}"
                    , this.getClass().getSimpleName(), JSON.toJSONString(ipBTimeOrderVip));
            throw new UpdateException("时效订单主表修改失败");
        }
        return record > 0 && vipTimeOrderRecord > 0;
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 判断List的值是否全部相同
     * @Date 2019-9-3
     * @Param [stringList]
     **/
    private boolean checkRepeatList(List<String> stringList) {
        if (CollectionUtils.isEmpty(stringList)) {
            return false;
        }
        int setSize = new HashSet<String>(stringList).size();
        return 1 == setSize;
    }

    /**
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     * <AUTHOR>
     * @Description orderSn判断是否所有的平台发货仓库都一致
     * @Date 2019-9-3
     * @Param [orderSn]
     **/
    public ValueHolderV14 getWarehouseCodeByOrderSn(String orderSn, IpJitxDeliveryRelation deliveryRelation) {
        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            IpBJitxDelivery jitxDelivery = deliveryRelation.getJitxDelivery();
            List<IpBJitxDeliveryItemEx> deliveryItems = deliveryRelation.getJitxDeliveryItemList();
            List<String> occupiedOrderSnList = gsiIpBTimeOrderVipOrderSnMapper.selectByOrderSn(orderSn);
            if (CollectionUtils.isEmpty(occupiedOrderSnList)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("ES分库键查询为空！");
            }
            List<String> wareHouseList = new ArrayList<>();
            List<IpBTimeOrderVipItemEx> timeOrderItemExList = new ArrayList<>();
            for (String s : occupiedOrderSnList) {

                IpVipTimeOrderRelation ipVipTimeOrderRelation = this.selectTimeOrder(s);
                if (ipVipTimeOrderRelation == null) {
                    log.info(this.getClass().getName() + " 单号：occupiedOrderSn = " + s + "查无对应信息！");
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(" 单号：occupiedOrderSn = " + s + "查无对应信息！");
                    return v14;
                }
                if (CollectionUtils.isEmpty(ipVipTimeOrderRelation.getIpBTimeOrderVipItemExList())) {
                    log.info(this.getClass().getName() + " 单号：occupiedOrderSn = " + s + "查无对应明细！");
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(" 单号：occupiedOrderSn = " + s + "查无对应明细！");
                    return v14;
                }
                //主表判断
                Integer status = ipVipTimeOrderRelation.getIpBTimeOrderVip().getStatus();
                if (status == null || !status.equals(TimeOrderVipStatusEnum.OCCUPIED.getValue())) {
                    log.info(this.getClass().getName() + " 单号：occupiedOrderSn = " + s + "订单状态为非已占用！");
                    if (YesNoEnum.Y.getVal().equals(jitxDelivery.getIsStoreDelivery())) {
                        v14.setCode(ResultCode.SUCCESS);
                        v14.setMessage(" 单号：occupiedOrderSn = " + s + "是店发");
                        return v14;
                    }
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(" 单号：occupiedOrderSn = " + s + "订单状态为非已占用！");
                    return v14;
                }
                //明细判断
                if (CollectionUtils.isNotEmpty(ipVipTimeOrderRelation.getIpBTimeOrderVipOccupyItemList())) {
                    for (IpBTimeOrderVipOccupyItem vipOccupyItem : ipVipTimeOrderRelation.getIpBTimeOrderVipOccupyItemList()) {
                        if (StringUtils.isNotEmpty(vipOccupyItem.getWarehouseCode())) {
                            wareHouseList.add(vipOccupyItem.getWarehouseCode());
                        } else {
                            log.info(this.getClass().getName() + " 单号：occupiedOrderSn = " + s + "明细存在平台仓库编码为空！");
                            v14.setCode(ResultCode.FAIL);
                            v14.setMessage(" 单号：occupiedOrderSn = " + s + "明细存在平台仓库编码为空！");
                            return v14;
                        }
                    }
                }
                timeOrderItemExList.addAll(ipVipTimeOrderRelation.getIpBTimeOrderVipItemExList());
            }


            if (CollectionUtils.isNotEmpty(deliveryItems)) {
                if (log.isDebugEnabled()) {
                    log.debug("开始进行寻仓单时效订单对比：{}/{}", JSON.toJSONString(deliveryItems), JSON.toJSONString(timeOrderItemExList));
                }
                if (deliveryItems.size() != timeOrderItemExList.size()) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("orderSn =" + orderSn + "寻仓单明细数量不等于对应时效订单明细量");
                    return v14;
                }
            } else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("orderSn =" + orderSn + "寻仓单明细为空！");
                return v14;
            }
            if (CollectionUtils.isEmpty(wareHouseList)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("orderSn =" + orderSn + "无对平台仓库编码！");
                return v14;
            }
            boolean isRepeat = this.checkRepeatList(wareHouseList);
            if (isRepeat) {
                String warehouseCode = wareHouseList.get(0);

                String availableWarestr = jitxDelivery.getAvailableWarehouses();
                List<String> availableWares = new ArrayList<>();
                if (StringUtils.isNotEmpty(availableWarestr)) {
                    availableWares = JSON.parseArray(availableWarestr, String.class);
                }else{
                    if (YesNoEnum.Y.getVal().equals(jitxDelivery.getIsStoreDelivery())) {
                        v14.setCode(ResultCode.SUCCESS);
                        v14.setMessage("可选仓库列表为空且是店发");
                        return v14;
                    }
                }
                //判断匹配仓是否在可用仓列表
                v14 = ipJitxDeliveryService.isAvailableWareHouseExists(availableWares, warehouseCode);
                if (v14.getCode() == ResultCode.FAIL) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("时效订单仓库不在可选仓库列表之内！");
                } else {
                    v14.setCode(ResultCode.SUCCESS);
                    v14.setMessage(warehouseCode);
                }
            } else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("orderSn =" + orderSn + "平台仓库不为同一个！");
            }
        } catch (Exception ex) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("异常！" + ex);
        }
        return v14;
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新时效订单交易状态
     * @Date 2019-9-4
     * @Param [status, sysRemark, ipBTimeOrderVip]
     **/
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTimeOrderDealStatus(Integer status, String sysRemark, IpBTimeOrderVip ipBTimeOrderVip) {
        String occupiedOrderSn = ipBTimeOrderVip.getOccupiedOrderSn();
        if (sysRemark != null && sysRemark.length() > IpBTimeOrderVipMapper.MAX_REMARK_LENGTH) {
            sysRemark = sysRemark.substring(0, IpBTimeOrderVipMapper.MAX_REMARK_LENGTH - 1);
        }
        IpBTimeOrderVip updateObj = new IpBTimeOrderVip();
        if (null != status) {
            updateObj.setStatus(status);
        }
        updateObj.setSysremark(sysRemark);
        Wrapper wrapper = new QueryWrapper();
        ((QueryWrapper) wrapper).eq("OCCUPIED_ORDER_SN", occupiedOrderSn);
        int result = ipBTimeOrderVipMapper.update(updateObj, wrapper);
        if (result > 0) {
            this.updateTimeOrderES(occupiedOrderSn);
        }
        return result > 0;
    }

    /**
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     * <AUTHOR>
     * @Description 寻仓
     * @Date 2019-9-5
     * @Param [ipVipTimeOrderRelation]
     **/
    public ValueHolderV14 branchWarehouse(IpVipTimeOrderRelation ipVipTimeOrderRelation) {
        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            Long shopId = ipVipTimeOrderRelation.getIpBTimeOrderVip().getCpCShopId();

            //查询订单中店铺在【店铺库存同步策略】中存在的逻辑仓（启用状态）
            List<Long> storeList = omsSyncStockStrategyService.queryShopStoreNextList(shopId);
            if (CollectionUtils.isEmpty(storeList)) {
                if (log.isDebugEnabled()) {
                    log.debug(this.getClass().getName() + " 该店铺【" + shopId + "】" + "未查找到相应的供货仓");
                }
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("分配发货仓库失败，店铺未设置供货仓库");
                return v14;
            }
            //根据逻辑仓找到对应的实体仓
            List<Long> warePhyHouseIds = omsQueryWareHouseService.queryWareHouseIds(storeList);
            if (CollectionUtils.isEmpty(warePhyHouseIds)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("逻辑仓查询实体仓为空");
                return v14;
            }
            //若根据逻辑供货仓查找出一个实体仓，则更新唯品会时效订单商品表的发货仓库为当前实体仓，返回分配发货仓库成功
            if (1 == warePhyHouseIds.size()) {
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage("分配发货仓库成功");
                v14.setData(warePhyHouseIds.get(0));
                return v14;
            }
            //根据逻辑仓聚合实体仓
            //若有多个实体仓,店铺供货仓集合[即实体仓+逻辑仓名称+逻辑仓ID]和订单的SKU集合[即商品条码+商品ID]的入参，调用【寻仓库存服务】
            Long wareId = checkWareHouseList(ipVipTimeOrderRelation);
            if (null == wareId) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("分配发货仓库失败，寻找发货仓库失败");
                return v14;
            }
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("寻仓成功！");
            v14.setData(wareId);
            return v14;
        } catch (NDSException ex) {
            ex.printStackTrace();
            log.error("订单OrderId" + ipVipTimeOrderRelation.getOrderId() + "调用分配发货仓库服务异常: ", ex);
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("寻仓操作系统异常！");
            return v14;
        }
    }

    /**
     * 返回存在库存的实体仓
     *
     * @param ipVipTimeOrderRelation 订单对象
     * @return List<Long>
     */
    public Long checkWareHouseList(IpVipTimeOrderRelation ipVipTimeOrderRelation) {
        //取出订单明细条码Code
        List<String> skuCodeList = new ArrayList<>();
        List<IpBTimeOrderVipItemEx> ipBTimeOrderVipItemExList = ipVipTimeOrderRelation.getIpBTimeOrderVipItemExList();
        for (IpBTimeOrderVipItemEx itemEx : ipBTimeOrderVipItemExList) {
            if (itemEx.getProdSku() != null) {
                skuCodeList.add(itemEx.getProdSku().getSkuEcode());
            }
        }
        //去重明细skuCode
        List<String> distinctList = skuCodeList.stream().distinct().collect(Collectors.toList());
        log.debug("寻仓订单OrderId" + ipVipTimeOrderRelation.getOrderId() + "打印明细集合skuCodeList" + distinctList);
        //筛选实体仓
        ValueHolderV14 holderV14 = sgRpcService.queryTimeSendWareHouseStock(ipVipTimeOrderRelation);
        //库存中心实体仓的返回结果
        if (log.isDebugEnabled()) {
            log.debug("OrderId[" + ipVipTimeOrderRelation.getOrderId() + "],占单分仓服务查询库存服务出参:" + holderV14.toJSONObject().toJSONString() + ";");
        }
        //解析数据
        List<SgSumStorageQueryResult> storageQueryResults = (List<SgSumStorageQueryResult>) holderV14.getData();
        if (log.isDebugEnabled()) {
            log.debug("OrderId[" + ipVipTimeOrderRelation.getOrderId() + "],占单分仓服务获取data:" + JSONObject.toJSONString(storageQueryResults) + ";");
        }
        //库存返回没有数据的情况
        if (CollectionUtils.isEmpty(storageQueryResults)) {
            return null;
        }
        return compareStock(ipVipTimeOrderRelation, distinctList, storageQueryResults, ipBTimeOrderVipItemExList);
    }

    /**
     * 比较sku数量比较得出符合库存数的逻辑仓
     *
     * @param timeOrderRelation      店铺Id
     * @param distinctList           去重的明细sku集合
     * @param storageQueryResultList 实体仓库存集合
     * @param timeOrderVipItemExList 明细集合
     * @return List<Long>
     */
    private Long compareStock(IpVipTimeOrderRelation timeOrderRelation, List<String> distinctList
            , List<SgSumStorageQueryResult> storageQueryResultList, List<IpBTimeOrderVipItemEx> timeOrderVipItemExList) {

        //将skuCode 和商品购买数量存在Map集合里面
        Map<String, BigDecimal> stringListMap = new HashMap<>();
        for (IpBTimeOrderVipItemEx timeOrderVipItemEx : timeOrderVipItemExList) {
            //取出明细购买数量 相同skuCode数量累加
            BigDecimal qty = (timeOrderVipItemEx.getAmount() == null ? BigDecimal.ZERO : timeOrderVipItemEx.getAmount());
            //避免出现不同明细相同sku,若出现,计算累加和
            if (stringListMap.containsKey(timeOrderVipItemEx.getProdSku().getSkuEcode())) {
                stringListMap.put(timeOrderVipItemEx.getProdSku().getSkuEcode(), qty
                        .add(stringListMap.get(timeOrderVipItemEx.getProdSku().getSkuEcode())));
            } else {
                stringListMap.put(timeOrderVipItemEx.getProdSku().getSkuEcode(), qty);
            }
        }
        log.debug("寻仓订单OrderId" + timeOrderRelation.getOrderId() + "打印明细stringListMap" + stringListMap);
        //满足库存的临时对象
        List<WareHouseResult> wareHouseResultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(storageQueryResultList)) {
            //逻辑仓对应一个skuCode和可用数量
            for (SgSumStorageQueryResult sgBStorage : storageQueryResultList) {
                BigDecimal qtyAvailable = (sgBStorage.getQtyAvailable() == null ? BigDecimal.ZERO : sgBStorage.getQtyAvailable());
                //将符合条件的逻辑仓都存到集合中  [sku相同  购买数量<库存数量]
                if ((stringListMap.containsKey(sgBStorage.getPsCSkuEcode())) && (stringListMap.get(sgBStorage.getPsCSkuEcode()).compareTo(qtyAvailable) <= 0)) {
                    wareHouseResultList.add(new WareHouseResult(sgBStorage.getPsCSkuEcode(), sgBStorage.getCpCPhyWarehouseId()));
                }
            }
        }
        log.debug("寻仓订单OrderId" + timeOrderRelation.getOrderId() + "聚合的list" + wareHouseResultList);
        //转换计算将相同实体仓的满足sku库存的聚合起来
        Map<Long, StringBuffer> wareHouseListMap = new HashMap<>();
        for (WareHouseResult wareHouseResult : wareHouseResultList) {
            StringBuffer stringBuffer = new StringBuffer();
            if (wareHouseListMap.containsKey(wareHouseResult.getWareHosueId())) {
                wareHouseListMap.put(wareHouseResult.getWareHosueId(), wareHouseListMap.get(wareHouseResult.getWareHosueId()).append(",").append(wareHouseResult.getSkuCode()));
            } else {
                wareHouseListMap.put(wareHouseResult.getWareHosueId(), stringBuffer.append(wareHouseResult.getSkuCode()));
            }
        }
        log.debug("寻仓订单OrderId" + timeOrderRelation.getOrderId() + "聚合计算的wareHouseListMap" + wareHouseListMap);
        List<Long> wareHouseIds = new ArrayList<>();
        //得到key集合
        for (Long key : wareHouseListMap.keySet()) {
            StringBuffer skuCodeString = wareHouseListMap.get(key);
            log.debug(key + " _ " + skuCodeString);
            //判断当前实体发货仓库的明细sku是否都满足库存
            boolean reulst = ipJitxDeliveryService.checkContain(skuCodeString, distinctList);
            log.debug("寻仓订单OrderId" + timeOrderRelation.getOrderId() + "实体仓" + key + "_是否都满足库存" + reulst);
            if (reulst) {
                wareHouseIds.add(key);
            }
        }
        //聚合过得实体仓去重
        List<Long> secondWareHouseIds = wareHouseIds.stream().distinct().collect(Collectors.toList());
        log.debug("寻仓订单OrderId" + timeOrderRelation.getOrderId() + "_最终得到的实体仓" + secondWareHouseIds);
        if (CollectionUtils.isEmpty(secondWareHouseIds)) {
            //都不满足库存,执行部分占单处理
            return execPartiallyOccupied(timeOrderRelation, storageQueryResultList);
        } else {
            if (secondWareHouseIds.size() == 1) {
                return secondWareHouseIds.get(0);
            } else {
                return execSendPlan(timeOrderRelation, storageQueryResultList, secondWareHouseIds);
            }
        }
    }

    /**
     * 部分占单处理
     *
     * @param timeOrderRelation      时效订单
     * @param storageQueryResultList 实体仓库存集合
     * @return
     */
    private Long execPartiallyOccupied(IpVipTimeOrderRelation timeOrderRelation, List<SgSumStorageQueryResult> storageQueryResultList) {
        List<ProductSku> productSkuList = timeOrderRelation.getIpBTimeOrderVipItemExList().stream().map(IpBTimeOrderVipItemEx::getProdSku).collect(Collectors.toList());
        List<String> skuCodeList = productSkuList.stream().map(ProductSku::getSkuEcode).distinct().collect(Collectors.toList());
        List<Long> wareHouseList = new ArrayList<>();
        for (SgSumStorageQueryResult sgBStorage : storageQueryResultList) {
            BigDecimal qtyAvailable = (sgBStorage.getQtyAvailable() == null ? BigDecimal.ZERO : sgBStorage.getQtyAvailable());
            //将任意一个符合sku且可用数量大于0的实体仓返回，作为部分占单
            if (skuCodeList.contains(sgBStorage.getPsCSkuEcode()) && qtyAvailable.compareTo(BigDecimal.ZERO) > 0) {
                wareHouseList.add(sgBStorage.getCpCPhyWarehouseId());
            }
        }
        if (CollectionUtils.isEmpty(wareHouseList)) {
            return null;
        }
        if (wareHouseList.size() == 1) {
            return wareHouseList.get(0);
        }
        return execSendPlan(timeOrderRelation, storageQueryResultList, wareHouseList);
    }

    /**
     * 派单方案执行
     *
     * @param timeOrderRelation      时效订单
     * @param storageQueryResultList 实体仓库存集合
     * @param secondWareHouseIds     实体仓ID列表
     * @return
     */
    private Long execSendPlan(IpVipTimeOrderRelation timeOrderRelation, List<SgSumStorageQueryResult> storageQueryResultList, List<Long> secondWareHouseIds) {
        //实体仓过滤
        storageQueryResultList = storageQueryResultList.stream().filter(f -> secondWareHouseIds.contains(f.getCpCPhyWarehouseId())).collect(Collectors.toList());
        //通过销售区域（sale_warehouse）查询JIT仓库信息
        CpCVipcomWahouse cpCVipcomWahouse = cpRpcService.selectByCode(timeOrderRelation.getIpBTimeOrderVip().getSaleWarehouse());
        if (null == cpCVipcomWahouse) {
            log.error("寻仓订单OrderId{}，销售区域{}对应JIT仓库信息不存在", timeOrderRelation.getOrderId(), timeOrderRelation.getIpBTimeOrderVip().getSaleWarehouse());
            return Collections.max(secondWareHouseIds);
        }
        //派单方案执行
        SendPlanExecution sendPlanExecution = new SendPlanExecution(
                timeOrderRelation.getOrderId(), timeOrderRelation.getIpBTimeOrderVip().getCpCShopId(),
                secondWareHouseIds, OrderType.TIMEORDER);
        sendPlanExecution.setCpCVipcomWahouseId(cpCVipcomWahouse.getId());
        Long phyWarehouseId = sendPlanService.execute(sendPlanExecution, storageQueryResultList);
        if (null == phyWarehouseId) {
            //多条满足库存按实体仓优先级取优先级最高
            List<Long> priorityPhyWareHouseIds = sortPhyWarehouse(timeOrderRelation.getIpBTimeOrderVip().getCpCShopId());
            //返回优先级最高的整包库存符合实体仓
            return selectMaxPriorityPhyWareHouse(priorityPhyWareHouseIds, secondWareHouseIds);
        }
        return phyWarehouseId;
    }

    /**
     * 按逻辑仓优先级排序实体仓
     *
     * @param cpCShop
     * @return 返回按优先级实体仓排序列表
     */
    private List<Long> sortPhyWarehouse(Long cpCShop) {
        HashMap<Long, CpCStore> hashMap = new HashMap<>();
        List<Long> phyWarehouseVoList = new ArrayList<>();
        //调用店铺同步库存策略接口
        List<StCSyncStockStrategyVo> stCSyncStockStrategyItemDOList = omsSyncStockStrategyService.selectSyncStockStrategyByPriority(cpCShop);
        //逻辑仓不存在
        if (CollectionUtils.isEmpty(stCSyncStockStrategyItemDOList)) {
            log.debug("调用店铺同步库存策略获取优先级逻辑仓列表出错:selectSyncStockStrategyByPriority;");
            return null;
        }
        //逻辑仓存在
        if (CollectionUtils.isNotEmpty(stCSyncStockStrategyItemDOList)) {
            List<Long> storeIdList = stCSyncStockStrategyItemDOList.stream().filter(x -> x.getCpCStoreId() != null).map(x -> x.getCpCStoreId()).collect(Collectors.toList());
            //逻辑仓为空，店铺同步库存策略保存导致逻辑仓为空
            if (CollectionUtils.isEmpty(storeIdList)) {
                return null;
            }
            StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
            storeInfoQueryRequest.setIds(storeIdList);
            try {
                hashMap = basicCpQueryService.getStoreInfo(storeInfoQueryRequest);
            } catch (Exception e) {
                log.error(this.getClass().getName() + " 逻辑仓查询实体仓basicCpQueryService.getStoreInfo出错{}", e);
                return null;
            }
            //判断逻辑仓的实体仓数量，若就一个实体仓就用这个实体仓作为发货仓
            for (Long cpCStoreId : storeIdList) {
                if (hashMap.get(cpCStoreId) != null) {
                    CpCStore cpCStore = hashMap.get(cpCStoreId);
                    phyWarehouseVoList.add(cpCStore.getCpCPhyWarehouseId());//实体仓
                }
            }
        }
        return phyWarehouseVoList;
    }

    /**
     * 返回优先级最高的整包库存符合实体仓
     *
     * @param priorityPhyWareHouseIds 优先级排序的实体仓列表
     * @param secondWareHouseIds      整包符合实体仓列表
     * @return 返回优先级最高的整包库存符合实体仓
     */
    private Long selectMaxPriorityPhyWareHouse(List<Long> priorityPhyWareHouseIds, List<Long> secondWareHouseIds) {
        for (Long phyWareHouseId : priorityPhyWareHouseIds) {
            List<Long> filterPhyWareHouse = secondWareHouseIds.stream()
                    .filter(x -> x.longValue() == phyWareHouseId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterPhyWareHouse)) {
                return filterPhyWareHouse.get(0);
            }
        }
        return null;
    }

    /**
     * 占用库存
     *
     * @param timeOrderRelation 订单信息
     * @param user              用户信息
     */
    public ValueHolderV14 queryWareHouse(IpVipTimeOrderRelation timeOrderRelation, User user) {
        try {
            ValueHolderV14 v14 = new ValueHolderV14();
            //调用占用库存服务。占用成功后调用订单日志服务
            log.debug("执行占用库存服务,入参为----->>>>" + timeOrderRelation);
            ValueHolderV14 searchStockWareHouse = this.querySearchStockWareHouse(timeOrderRelation, user);
            log.debug("执行占用库存服务,返回参数----->>>>" + searchStockWareHouse + ">>>Measage=======>" + searchStockWareHouse.getMessage());
            int code = searchStockWareHouse.getCode();
            //占库存成功 状态为待审核
            if (code == ResultCode.SUCCESS) {
                log.debug("调用占用库存--->订单号为-->" + timeOrderRelation.getOrderId() + "占用库存成功");
                log.debug("调用占用库存--->订单号为-->" + timeOrderRelation.getOrderId() + "返回信息：" + searchStockWareHouse.getMessage());
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage("保存成功！");
            } else if (code == ResultCode.FAIL) {
                //占用库存失败
                log.debug("调用占用库存--->订单号为-->" + timeOrderRelation.getOrderId() + "返回信息：" + searchStockWareHouse.getMessage());
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(searchStockWareHouse.getMessage());
            } else if (code == -2) {
                //占用库存失败
                log.debug("调用占用库存--->订单号为-->" + timeOrderRelation.getOrderId() + "返回信息：" + searchStockWareHouse.getMessage());
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("占单异常！异常信息：" + searchStockWareHouse.getMessage());
                throw new NDSException(this.getClass().getName() + "调用占用库存,返回值为--->" + code + "," + searchStockWareHouse.getMessage());
            }
            return v14;
        } catch (Exception ex) {
            log.debug(this.getClass().getName() + "调用占用库存异常》》" + ex.getMessage());
            throw new NDSException(ex.getMessage());
        }
    }

    /**
     * 寻源新增或更新逻辑发货单
     *
     * @param timeOrderRelation 实体对象
     * @return boolean
     */
    public ValueHolderV14 querySearchStockWareHouse(IpVipTimeOrderRelation timeOrderRelation, User user) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.FAIL, null);
        try {
            if (CollectionUtils.isEmpty(timeOrderRelation.getIpBTimeOrderVipItemExList())) {
                vh.setMessage(timeOrderRelation.getOrderId() + "订单明细为空!");
                return vh;
            }
            //查找实体仓下面对应的逻辑仓
            List<Long> storeList = omsQueryWareHouseService.queryStoreList(timeOrderRelation.getCphyWarehouseId());
            if (CollectionUtils.isEmpty(storeList)) {
                vh.setMessage(timeOrderRelation.getOrderId() + "该实体仓下面逻辑仓为空!");
                return vh;
            }
            List<StStockPriorityRequest> stStockPriorityRequests = omsSyncStockStrategyService.queryStStockPriority(timeOrderRelation.getIpBTimeOrderVip().getCpCShopId(), storeList);
            if (CollectionUtils.isEmpty(stStockPriorityRequests)) {
                vh.setMessage(timeOrderRelation.getOrderId() + "该店铺逻辑发货仓为空!");
                return vh;
            }
            //SgSendSaveWithPriorityRequest model = packageLogicSearchModel(timeOrderRelation, stStockPriorityRequests, true, user);
            if (log.isDebugEnabled()) {
                //log.debug("OrderId[" + timeOrderRelation.getOrderId() + "],OMS占用库存服务入参:" + JSON.toJSONString(model) + ";");
            }
            ValueHolderV14 v14 = null;//sgRpcService.saveSendWithPriority(model);
            if (log.isDebugEnabled()) {
                log.debug("OrderId[" + timeOrderRelation.getOrderId() + "],OMS占用库存服务出参:" + v14.toJSONObject().toJSONString() + ";");
            }
            JSONObject resultJson = v14.toJSONObject();
            //调用占用库存服务成功
            if ("0".equalsIgnoreCase(resultJson.getString("code"))) {
                log.debug("_解析data数据" + resultJson.getJSONObject("data"));
                //解析占用情况 0成功 3缺货
                if ("0".equalsIgnoreCase(resultJson.getJSONObject("data").getString("preoutResult"))) {
                    if (log.isDebugEnabled()) {
                        log.debug("调用占用库存--->订单号为-->" + timeOrderRelation.getOrderId() + "订单占用库存成功,返回参数" + resultJson);
                    }
                    vh.setCode(ResultCode.SUCCESS);
                    vh.setMessage(timeOrderRelation.getOrderId() + "订单占用库存成功!");
                } else if ("3".equalsIgnoreCase(resultJson.getJSONObject("data").getString("preoutResult"))) {
                    if (log.isDebugEnabled()) {
                        log.debug("调用占用库存--->订单号为-->" + timeOrderRelation.getOrderId() + "订单占用库存缺货,返回参数" + resultJson);
                    }
                    JSONArray jsonArray = resultJson.getJSONObject("data").getJSONArray("outStockItemList");
                    int itemSize = jsonArray.size();
                    StringBuffer errorMessage = new StringBuffer("订单占用库存部分缺货!");
                    for (int i = 0; i < itemSize; i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if (jsonObject == null) {
                            throw new NDSException("时效订单部分占单错误" + timeOrderRelation.getOrderId());
                        }
                        //部分占单后，直接更新明细的占用情况
                        BigDecimal qtyOutOfStock = jsonObject.getBigDecimal("qtyOutOfStock");
                        Long itemId = jsonObject.getLong("sourceItemId");
                        String barcode = jsonObject.getString("BARCODE");
                        IpBTimeOrderVipItem ipBTimeOrderVipItem = new IpBTimeOrderVipItem();
                        ipBTimeOrderVipItem.setOutStockQuantity(qtyOutOfStock);
                        QueryWrapper queryWrapper = new QueryWrapper();
                        queryWrapper.eq("IP_B_TIME_ORDER_VIP_ID", timeOrderRelation.getOrderId());
                        queryWrapper.eq("ID", itemId);
                        ipVipTimeOrderItemService.update(queryWrapper, ipBTimeOrderVipItem);
                        errorMessage.append("商品条码：").append(barcode).append("，缺货数量：").append(qtyOutOfStock);
                    }
                    vh.setCode(1);
                    vh.setMessage(errorMessage.toString());
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("调用占用库存--->订单号为-->" + timeOrderRelation.getOrderId() + "订单占用库存失败,返回参数" + resultJson);
                    }
                    vh.setMessage(timeOrderRelation.getOrderId() + "订单占用库存失败!");
                }
            } else {
                vh.setMessage(timeOrderRelation.getOrderId() + "订单库存占用失败!" + resultJson.getString("message"));
            }
        } catch (Exception e) {
            log.error("OrderId[" + timeOrderRelation.getOrderId() + "]调用寻源逻辑仓库存服务 querySearchStockWareHouse 异常信息！", e);
            vh.setMessage(timeOrderRelation.getOrderId() + "订单库存占用失败!" + e.getMessage());
        }
        return vh;
    }

    /**
     * 时效订单占单新逻辑
     *
     * @param timeOrderRelation 对象
     * @param operateUser       操作人
     * @return 占单结果
     */
    public ValueHolderV14 timeOrderOccupyStock(IpVipTimeOrderRelation timeOrderRelation, User operateUser) throws Exception {
        IpVipTimeOrderService vipTimeOrderService = ApplicationContextHandle.getBean(IpVipTimeOrderService.class);
        ValueHolderV14 vh = new ValueHolderV14();
        Long orderId = timeOrderRelation.getOrderId();
        String occupiedOrderSn = timeOrderRelation.getOccupiedOrderSn();
        Long shopId = timeOrderRelation.getIpBTimeOrderVip().getCpCShopId();
        List<IpBTimeOrderVipOccupyItem> timeOrderVipOccupyItemList =
                ipBTimeOrderVipOccupyItemMapper.selectOrderOccupyItemListByStatus(orderId,
                        TimeOrderOccupyItemStatusEnum.NOT_CONFIRM.getValue());
        if (CollectionUtils.isEmpty(timeOrderVipOccupyItemList)) {
            if (log.isDebugEnabled()) {
                log.debug("时效订单未确认状态库存占用明细为空，时效订单ID：{}", orderId);
            }
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("时效订单未确认状态库存占用明细为空!");
            return vh;
        }
        for (IpBTimeOrderVipOccupyItem vipOccupyItem : timeOrderVipOccupyItemList) {
            Long occupyItemId = vipOccupyItem.getId();
            Long phyWarehouseId = vipOccupyItem.getCpCPhyWarehouseId();
            if (phyWarehouseId == null) {
                if (log.isDebugEnabled()) {
                    log.debug("时效订单明细实体仓为空，时效订单：{}，明细ID：{}",
                            occupiedOrderSn, occupyItemId);
                }
                ipBTimeOrderVipOccupyItemMapper.updateOccItemStatus(TimeOrderOccupyItemStatusEnum.OUT_OF_STOCK.getValue(),
                        IsActiveEnum.Y.getKey(), orderId, occupyItemId);
                continue;
            }
            //查找实体仓下面对应的逻辑仓
            List<Long> storeList = omsQueryWareHouseService.queryStoreList(vipOccupyItem.getCpCPhyWarehouseId());
            if (CollectionUtils.isEmpty(storeList)) {
                if (log.isDebugEnabled()) {
                    log.debug("时效订单明细实体仓下面的逻辑仓为空，时效订单：{}，明细ID：{}",
                            occupiedOrderSn, occupyItemId);
                }
                vipTimeOrderService.occupyOutOfStockHandle(orderId, vipOccupyItem, shopId, null);
                continue;
            }
            List stStockPriorityRequests = omsSyncStockStrategyService.queryStStockPriority(timeOrderRelation.getIpBTimeOrderVip().getCpCShopId(), storeList);
            if (CollectionUtils.isEmpty(stStockPriorityRequests)) {
                if (log.isDebugEnabled()) {
                    log.debug("时效订单明细实体仓下面的逻辑仓供货优先级为空，时效订单：{}，明细ID：{}",
                            occupiedOrderSn, occupyItemId);
                }
                vipTimeOrderService.occupyOutOfStockHandle(orderId, vipOccupyItem, shopId, null);
                continue;
            }
            //SgSendSaveWithPriorityRequest model = packageOccupyStockModel(timeOrderRelation, vipOccupyItem, stStockPriorityRequests, operateUser);
            ValueHolderV14 v14 = null;//sgRpcService.saveSendWithPriority(model);
            if (log.isDebugEnabled()) {
                log.debug("时效订单明细占单出参：{}，时效订单ID：{}，占用明细ID：{}", JSON.toJSONString(v14), orderId, occupyItemId);
            }
            JSONObject resultJson = v14.toJSONObject();
            //调用占用库存服务成功
            if (v14.isOK()) {
                //解析占用情况 0成功 3缺货
                if ("0".equalsIgnoreCase(resultJson.getJSONObject("data").getString("preoutResult"))) {
                    if (log.isDebugEnabled()) {
                        log.debug("时效订单占用库存成功，订单ID：{}，占单明细ID：{}", orderId, occupyItemId);
                    }
                    ipBTimeOrderVipOccupyItemMapper.updateOccItemStatus(TimeOrderOccupyItemStatusEnum.OCCUPY_SUCCESS.getValue(),
                            IsActiveEnum.Y.getKey(), orderId, occupyItemId);
                } else if ("3".equalsIgnoreCase(resultJson.getJSONObject("data").getString("preoutResult"))) {
                    if (log.isDebugEnabled()) {
                        log.debug("时效订单占用库存缺货，订单ID：{}，占单明细ID：{}", orderId, occupyItemId);
                    }
                    JSONArray jsonArray = resultJson.getJSONObject("data").getJSONArray("outStockItemList");
                    if (CollectionUtils.isEmpty(jsonArray)) {
                        if (log.isDebugEnabled()) {
                            log.debug("时效订单占用库存缺货List为空，订单ID：{}，占单明细ID：{}", orderId, occupyItemId);
                        }
                        vipTimeOrderService.occupyOutOfStockHandle(orderId, vipOccupyItem, shopId, null);
                        continue;
                    }
                    JSONObject jsonObject = jsonArray.getJSONObject(0);
                    //部分占单后，直接更新明细的占用情况
                    BigDecimal qtyOutOfStock = jsonObject.getBigDecimal("qtyOutOfStock");
                    vipTimeOrderService.occupyOutOfStockHandle(orderId, vipOccupyItem, shopId, qtyOutOfStock);
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("时效订单占用库存失败01，订单ID：{}，占单明细ID：{}", orderId, occupyItemId);
                    }
                    vipTimeOrderService.occupyOutOfStockHandle(orderId, vipOccupyItem, shopId, null);
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("时效订单占用库存失败02，订单ID：{}，占单明细ID：{}", orderId, occupyItemId);
                }
                vipTimeOrderService.occupyOutOfStockHandle(orderId, vipOccupyItem, shopId, null);
            }

        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(orderId + "时效订单占用库存完成!");
        return vh;
    }


    /**
     * <AUTHOR>
     * 时效订单寻源占单-发送mq方法
     * @param timeOrderRelation 对象
     * @param operateUser       操作人
     * @return  ValueHolderV14
     */
    public ValueHolderV14 timeOrderFindSourceStrategyAndOccupyStock(IpVipTimeOrderRelation timeOrderRelation, User operateUser) throws Exception {

        ValueHolderV14 vh = new ValueHolderV14();
        Long orderId = timeOrderRelation.getOrderId();
        IpBTimeOrderVip ipBTimeOrderVip = timeOrderRelation.getIpBTimeOrderVip();
        List<IpBTimeOrderVipOccupyItem> timeOrderVipOccupyItemList =
                ipBTimeOrderVipOccupyItemMapper.selectOrderOccupyItemListByStatus(orderId,
                        TimeOrderOccupyItemStatusEnum.NOT_CONFIRM.getValue());
        if (CollectionUtils.isEmpty(timeOrderVipOccupyItemList)) {
            if (log.isDebugEnabled()) {
                log.debug("时效订单未确认状态，库存占用明细为空，时效订单ID：{}", orderId);
            }
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("时效订单未确认状态，但库存占用明细为空，无法进行时效订单寻源占单!");
            return vh;
        }
        log.info("开始构建调用sg寻源占单参数，时效订单id：{}",orderId);
        SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest  = buildFindAndOccupySgRequest(ipBTimeOrderVip,timeOrderVipOccupyItemList);
        log.info("开始构建调用sg寻源占单参数，构建结束参数,{}",sgFindSourceStrategyC2SRequest);
        try {
            // fixme 该消息已无效，只有test和uat有，线上环境没有该配置
//            String topic = propertiesConf.getProperty("r3.vip.sg.occupy.stock.topic", "BJ_UAT_R3_VIP_SG_OCCUPY_STOCK");
            String topic = "R3_VIP_SG_OCCUPY_STOCK";
//            String tag = propertiesConf.getProperty("r3.vip.sg.occupy.stock.timeorder.tag", "oms_to_sg_timeorder_vip");
            String tag = "oms_to_sg_timeorder_vip";
            String jsonValue = JSONObject.toJSONString(sgFindSourceStrategyC2SRequest);
//            String result = r3MqSendHelper.sendMessage(jsonValue, topic, tag);
            MqSendResult result = defaultProducerSend.sendTopic(topic, tag, jsonValue, null);

            log.info("时效订单，发送mq结果{}",result);
        } catch (Exception e) {
            log.error("时效订单寻源占库存，发送Mq异常", e);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("时效订单寻源占库存，发送Mq异常!");
            return vh;
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(orderId + "时效订单寻源占单mq发送成功!");
        return vh;
    }

    /**
     * <AUTHOR>
     * @Date 16:16 2021/7/7
     * @Description 构建寻源占单接口Request参数
     */
    private SgFindSourceStrategyC2SRequest buildFindAndOccupySgRequest(IpBTimeOrderVip ipBTimeOrderVip,List<IpBTimeOrderVipOccupyItem> timeOrderVipOccupyItemList) {

        SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest = new SgFindSourceStrategyC2SRequest();
        sgFindSourceStrategyC2SRequest.setSourceBillId(ipBTimeOrderVip.getId());
        sgFindSourceStrategyC2SRequest.setSourceBillNo(ipBTimeOrderVip.getOccupiedOrderSn());
        /**唯品会订单号*/
        sgFindSourceStrategyC2SRequest.setTid(ipBTimeOrderVip.getOrderSn());
        //单据类型  1.零售发货，2.销售发货，3采购退货发货，4 调拨发货， 5 销售退货发货 6 采购退货单  7 调拨单  8 唯品会单  9 库存调整单
        sgFindSourceStrategyC2SRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
        sgFindSourceStrategyC2SRequest.setShopId(ipBTimeOrderVip.getCpCShopId());
        //获取店铺中维护的，唯品会常态合作编码
        CpShop cpShop = cpRpcService.selectShopById(ipBTimeOrderVip.getCpCShopId());
        if(cpShop == null){
            log.error("时效订单{},店铺{}对应的店铺信息未维护！",ipBTimeOrderVip.getOrderSn(),ipBTimeOrderVip.getCpCShopTitle());
        }
        sgFindSourceStrategyC2SRequest.setBillDate(ipBTimeOrderVip.getCreateDate());
        //占单类型-店铺策略配置
        StCShopStrategyDO stCShopStrategyDO= omsStCShopStrategyService.selectOcStCShopStrategy(ipBTimeOrderVip.getCpCShopId());
        if(stCShopStrategyDO != null){
            log.info("{}.时效订单，占单类型为{}",ipBTimeOrderVip.getId(),stCShopStrategyDO.getOccupyType());
            sgFindSourceStrategyC2SRequest.setSplitType(stCShopStrategyDO.getOccupyType());
            sgFindSourceStrategyC2SRequest.setShopTitle(cpShop.getCpCShopTitle());
        }else{
            log.error("时效订单{},店铺{}对应店铺策略配置不存在",ipBTimeOrderVip.getOrderSn(),ipBTimeOrderVip.getCpCShopTitle());
        }
        //根据地址编码获取省市区名称
        String addressCode = ipBTimeOrderVip.getAddressCode();
        //获取唯品会平台仓库编码
        String jitWareHouseCode = ipBTimeOrderVip.getSaleWarehouse();
        //时效订单号
        String orderSn = ipBTimeOrderVip.getOrderSn();
        if(StringUtils.isNotEmpty(addressCode)){
            //根据地址编码查询唯品会对应的省市区名称
            TOmsvipfulladdress  tOmsvipfulladdress = cpRpcService.queryProvinceCityAreaNameByCode(addressCode);
            if(tOmsvipfulladdress != null){
                String provinceName = tOmsvipfulladdress.getProvinceName();
                String cityName = tOmsvipfulladdress.getCityName();
                String areaName = tOmsvipfulladdress.getCountyName();
                CpCRegionRelation cpCRegionRelation = cpRpcService.selectRegionRelationByProvinceCityArea(provinceName,cityName,areaName);
                if(cpCRegionRelation != null){
                    sgFindSourceStrategyC2SRequest.setProvinceId(cpCRegionRelation.getCpCRegionProvinceId());
                    sgFindSourceStrategyC2SRequest.setCityId(cpCRegionRelation.getCpCRegionCityId());
                    sgFindSourceStrategyC2SRequest.setAreaId(cpCRegionRelation.getCpCRegionAreaId());
                }else{
                    //通过仓库编码查询JIT仓库信息，并赋值省市区id
                    setProvinceCityAreaId(sgFindSourceStrategyC2SRequest,jitWareHouseCode,orderSn);
                }
            }else{
                //通过仓库编码查询JIT仓库信息，并赋值省市区id
                setProvinceCityAreaId(sgFindSourceStrategyC2SRequest,jitWareHouseCode,orderSn);
            }
        }else{
            //通过仓库编码查询JIT仓库信息，并赋值省市区id
            setProvinceCityAreaId(sgFindSourceStrategyC2SRequest,jitWareHouseCode,orderSn);
        }
        //构建明细信息
        List<SkuItemC2S> skuItems = buildSgSkuItemC2S(timeOrderVipOccupyItemList);
        sgFindSourceStrategyC2SRequest.setSkuItems(skuItems);
        return sgFindSourceStrategyC2SRequest;
    }

    /**
     * 通过仓库编码查询JIT仓库信息，并赋值省市区id
     * @param sgFindSourceStrategyC2SRequest
     * @param jitWareHouseCode
     * @param orderSn
     * @return SgFindSourceStrategyC2SRequest
     */
    public void setProvinceCityAreaId(SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest,String jitWareHouseCode,String orderSn){
        //通过销售区域（sale_warehouse）查询JIT仓库信息
        CpCVipcomWahouse cpCVipcomWahouse = cpRpcService.selectByCode(jitWareHouseCode);
        if (null == cpCVipcomWahouse) {
            log.error("时效订单{},销售区域{}对应JIT仓库信息不存在",orderSn,jitWareHouseCode);
        }else{
            //根据唯品会仓库编码，查询仓库对应的省市区
            sgFindSourceStrategyC2SRequest.setProvinceId(cpCVipcomWahouse.getCpCRegionProvinceId());
            sgFindSourceStrategyC2SRequest.setCityId(cpCVipcomWahouse.getCpCRegionCityId());
            sgFindSourceStrategyC2SRequest.setAreaId(cpCVipcomWahouse.getCpCRegionAreaId());
        }
    }

    /**
     * <AUTHOR>
     * @Date 16:25 2021/7/7
     * @Description 构建寻源占单及接口Request明细信息参数
     */
    private List<SkuItemC2S> buildSgSkuItemC2S(List<IpBTimeOrderVipOccupyItem> timeOrderVipOccupyItemList) {
        List<SkuItemC2S> skuItemC2SList = new ArrayList<>();
        log.info("{}.orderd订单,开始构建构建Sg寻源占单明细参数");
        for (IpBTimeOrderVipOccupyItem timeOrderVipOccupyItem : timeOrderVipOccupyItemList) {
            SkuItemC2S skuItemC2S = new SkuItemC2S();
            skuItemC2S.setSourceItemId(timeOrderVipOccupyItem.getId());
            skuItemC2S.setPsCSkuId(timeOrderVipOccupyItem.getPsCSkuId());
            //商品类型：1：正常商品 2：组合商品子商品 3：预售商品 4：组合商品头表商品
            skuItemC2S.setProType(StrategyConstants.PRO_TYPE_NORMAL);
            //待发货数量
            skuItemC2S.setQty(timeOrderVipOccupyItem.getAmount());
            //TODO 国标码，后续库存中心新增后再进行赋值
            skuItemC2SList.add(skuItemC2S);
        }
        return skuItemC2SList;
    }


    public ValueHolder occupyResultHandle(){
        ValueHolder vh = new ValueHolder();

        return vh;
    }


    /**
     * 时效订单明细缺货处理
     *
     * @param orderId       时效订单ID
     * @param vipOccupyItem 库存占用明细
     * @param shopId        店铺Id
     * @param qtyOutOfStock 缺货数量
     */
    @Transactional(rollbackFor = Exception.class)
    public void occupyOutOfStockHandle(Long orderId, IpBTimeOrderVipOccupyItem vipOccupyItem,
                                       Long shopId, BigDecimal qtyOutOfStock) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("时效订单占用库存缺货处理开始");
        }
        Long occupyItemId = vipOccupyItem.getId();
        BigDecimal vipOccupyItemAmount = vipOccupyItem.getAmount();
        //查询缺货的库存占用明细，如果不存在：直接修改当前占单明细状态为缺货，并且更新缺货数量；如果存在：则根据缺货情况更新缺货明细和当前明细
        List<IpBTimeOrderVipOccupyItem> outStockOccupyItemList =
                ipBTimeOrderVipOccupyItemMapper.selectOrderOccupyItemListByStatus(orderId,
                        TimeOrderOccupyItemStatusEnum.OUT_OF_STOCK.getValue());
        if (qtyOutOfStock == null) {
            if (CollectionUtils.isEmpty(outStockOccupyItemList)) {
                ipBTimeOrderVipOccupyItemMapper.updateOccItemStatusAndOutStockQuantity(vipOccupyItemAmount, orderId, occupyItemId);
            } else {
                IpBTimeOrderVipOccupyItem outStockOccupyItem = outStockOccupyItemList.get(0);
                ipBTimeOrderVipOccupyItemMapper.updateOccItemStatus(TimeOrderOccupyItemStatusEnum.VOID.getValue(),
                        IsActiveEnum.N.getKey(), orderId, occupyItemId);
                BigDecimal amount = outStockOccupyItem.getAmount().add(vipOccupyItemAmount);
                BigDecimal stockQuantity = outStockOccupyItem.getOutStockQuantity().add(vipOccupyItemAmount);
                ipBTimeOrderVipOccupyItemMapper.updateOccItemAmountAndOutStockQuantityAndStatus(amount, stockQuantity,
                        TimeOrderOccupyItemStatusEnum.OUT_OF_STOCK.getValue(), orderId, outStockOccupyItem.getId());
            }
        } else {
            if (CollectionUtils.isEmpty(outStockOccupyItemList)) {
                //如果没有缺货明细，直接新增一条
                IpBTimeOrderVipOccupyItem newOccupyItem = new IpBTimeOrderVipOccupyItem();
                BeanUtils.copyProperties(vipOccupyItem, newOccupyItem);
                newOccupyItem.setId(ModelUtil.getSequence("ip_b_time_order_vip_occupy_item"));
                newOccupyItem.setAmount(qtyOutOfStock);
                newOccupyItem.setOutStockQuantity(qtyOutOfStock);
                newOccupyItem.setCreationdate(new Date());
                newOccupyItem.setStatus(TimeOrderOccupyItemStatusEnum.OUT_OF_STOCK.getValue());
                ValueHolderV14<PhyWarehouseVo> vh =
                        subWarehouseService.getDefaultPhyWarehouse(shopId);
                if (vh.isOK() && vh.getData() != null) {
                    PhyWarehouseVo phyWarehouseVo = vh.getData();
                    Long phyWarehouseId = phyWarehouseVo.getPhyWarehouseId();
                    CpCPhyWarehouse phyWarehouse = cpRpcService.queryByWarehouseId(phyWarehouseId);
                    newOccupyItem.setCpCPhyWarehouseId(phyWarehouseId);
                    newOccupyItem.setCpCPhyWarehouseEname(phyWarehouse.getEname());
                    newOccupyItem.setCpCPhyWarehouseEcode(phyWarehouse.getEcode());
                    timeOrderOccupyItemService.buildPhyWareHouse(phyWarehouse, newOccupyItem, shopId);
                }
                ipBTimeOrderVipOccupyItemMapper.insert(newOccupyItem);
            } else {
                //如果存在缺货明细，更新缺货数量
                IpBTimeOrderVipOccupyItem outStockOccupyItem = outStockOccupyItemList.get(0);
                BigDecimal amount = outStockOccupyItem.getAmount().add(qtyOutOfStock);
                BigDecimal stockQuantity = outStockOccupyItem.getOutStockQuantity().add(qtyOutOfStock);
                ipBTimeOrderVipOccupyItemMapper.updateOccItemAmountAndOutStockQuantityAndStatus(amount, stockQuantity,
                        TimeOrderOccupyItemStatusEnum.OUT_OF_STOCK.getValue(), orderId, outStockOccupyItem.getId());
            }

            //应发数量小于等于缺货数量，直接作废当前明细
            if (vipOccupyItemAmount.compareTo(qtyOutOfStock) <= 0) {
                ipBTimeOrderVipOccupyItemMapper.updateOccItemStatus(TimeOrderOccupyItemStatusEnum.VOID.getValue(),
                        IsActiveEnum.N.getKey(), orderId, occupyItemId);
            } else {
                vipOccupyItemAmount = vipOccupyItemAmount.subtract(qtyOutOfStock);
                ipBTimeOrderVipOccupyItemMapper.updateOccItemAmountAndOutStockQuantityAndStatus(vipOccupyItemAmount, BigDecimal.ZERO,
                        TimeOrderOccupyItemStatusEnum.OCCUPY_SUCCESS.getValue(), orderId, occupyItemId);
            }
        }
    }

    /**
     * 时效订单转换 -》更新时效订单信息
     * @param orderVip
     * @param orderStatus
     * @param istrans
     * @param qty
     * @param sysRemark
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTimeOrderData(IpBTimeOrderVip orderVip,Integer orderStatus,Integer istrans,BigDecimal qty, String sysRemark,Boolean updateTransCount) {
        try {
            IpBTimeOrderVip ipBTimeOrderVip = new IpBTimeOrderVip();
            if(orderStatus != null){
                ipBTimeOrderVip.setStatus(orderStatus);
            }
            if(istrans != null){
                ipBTimeOrderVip.setIstrans(istrans);
            }
            if(qty != null){
                ipBTimeOrderVip.setOutStockQuantity(qty);
            }
            if(sysRemark != null){
                if (sysRemark.length() > 500) {
                    sysRemark = sysRemark.substring(0, 500);
                }
                ipBTimeOrderVip.setSysremark(sysRemark);
            }
            /**更新转换次数*/
            if(updateTransCount){
                Long transCount = orderVip.getTransCount() == null?0:orderVip.getTransCount();
                transCount += 1;
                ipBTimeOrderVip.setTransCount(transCount);
                ipBTimeOrderVip.setTransDate(new Date());
            }
            ipBTimeOrderVip.setModifieddate(new Date());
            int i = ipBTimeOrderVipMapper.update(ipBTimeOrderVip,
                    new QueryWrapper<IpBTimeOrderVip>()
                            .eq("occupied_order_sn",orderVip.getOccupiedOrderSn()));
            //推送es
            if (i > 0) {
                return true;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新唯品会时效订单中间表失败,occupiedOrderSn:{}", orderVip.getOccupiedOrderSn(), e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * 通过平台单号查询时效订单
     * @param orderSn
     * @return
     */
    public List<IpBTimeOrderVip> queryIpBTimeOrderVipByOrderSn(String orderSn){
        List<IpBTimeOrderVip> timeOrderVipList =ipBTimeOrderVipMapper.selectList(Wrappers.<IpBTimeOrderVip>lambdaQuery()
                //.select(IpBTimeOrderVip::getOccupiedOrderSn,IpBTimeOrderVip::getId,IpBTimeOrderVip::getStatus)
                .eq(IpBTimeOrderVip::getOrderSn, orderSn));
        return timeOrderVipList;
    }

    public void updateOrderByOrderSn(IpBTimeOrderVip orderVip) {
        if (StringUtils.isNotEmpty(orderVip.getOrderSn())) {
            List<IpBTimeOrderVip> ipBTimeOrderVips = ipBTimeOrderVipMapper.selectTimeOrderByOrderSn(orderVip.getOrderSn());
            if (CollectionUtils.isEmpty(ipBTimeOrderVips)) {
                return;
            }
            //更新时效单为完成
            LambdaQueryWrapper<IpBTimeOrderVip> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(IpBTimeOrderVip::getOrderSn, orderVip.getOrderSn());
            queryWrapper.eq(IpBTimeOrderVip::getIsactive, IsActiveEnum.Y.getKey());
            ipBTimeOrderVipMapper.update(orderVip, queryWrapper);
            List<Long> timeOrderIdList = ipBTimeOrderVips.stream().map(IpBTimeOrderVip::getId).distinct().collect(Collectors.toList());
            //更新时效订单占用明细为已匹配成功
            LambdaQueryWrapper<IpBTimeOrderVipOccupyItem> updateOccupyItem = new LambdaQueryWrapper();
            updateOccupyItem.in(IpBTimeOrderVipOccupyItem::getIpBTimeOrderVipId, timeOrderIdList);
            updateOccupyItem.eq(IpBTimeOrderVipOccupyItem::getIsactive, IsActiveEnum.Y.getKey());
            IpBTimeOrderVipOccupyItem occupyItem = new IpBTimeOrderVipOccupyItem();
            occupyItem.setStatus(TimeOrderOccupyItemStatusEnum.MATCH_SUCCESS.getValue());
            ipBTimeOrderVipOccupyItemMapper.update(occupyItem, updateOccupyItem);
        }
    }

    public IpBTimeOrderVipItem selectOneTimeOrderItemByOrderId(Long orderId) {
        IpBTimeOrderVipItem ipBTimeOrderVipItem = ipBTimeOrderVipItemMapper.selectOneOrderItem(orderId);
        return ipBTimeOrderVipItem;
    }

    public IpBTimeOrderVipOccupyItem selectOneTimeOrderOccupyItemByOrderId(Long orderId) {
        IpBTimeOrderVipOccupyItem ipBTimeOrderVipOccupyItem = ipBTimeOrderVipOccupyItemMapper.selectOneOrderOccupyItem(orderId);
        return ipBTimeOrderVipOccupyItem;
    }

    public List<IpBTimeOrderVipItem> selectTimeOrderItemByOrderIdList(List<Long> orderIdList) {
        LambdaQueryWrapper<IpBTimeOrderVipItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(IpBTimeOrderVipItem::getIpBTimeOrderVipId, orderIdList);
        List<IpBTimeOrderVipItem> ipBTimeOrderVipItems = ipBTimeOrderVipItemMapper.selectList(lambdaQueryWrapper);
        return ipBTimeOrderVipItems;
    }

    /**
     * 根据唯品会订单号更新时效订单
     * @param orderSn
     * @param remark
     */
    public void updateIpBTimeOrderVipByOrderSn(String orderSn,Integer orderStatus, String remark){
        IpBTimeOrderVip ipBTimeOrderVip = new IpBTimeOrderVip();
        ipBTimeOrderVip.setStatus(orderStatus);
        ipBTimeOrderVip.setSysremark(remark);
        ipBTimeOrderVip.setModifieddate(new Date());
        int i = ipBTimeOrderVipMapper.update(ipBTimeOrderVip,
                new QueryWrapper<IpBTimeOrderVip>()
                        .eq("order_sn",orderSn));
    }


    /**
     * 获取 - 反馈云枢纽仓库结果
     * @param deliveryRelation
     * @return
     */
    public ValueHolderV14 getFeedbackresult(IpJitxDeliveryRelation deliveryRelation) {
        ValueHolderV14 v14 = new ValueHolderV14();
        try {

            IpBJitxDelivery ipBJitxDelivery = deliveryRelation.getJitxDelivery();
            List<IpBJitxDeliveryItemEx> deliveryItems = deliveryRelation.getJitxDeliveryItemList();
            String orderSn = deliveryRelation.getOrderNo();
            //是否缺货
            boolean isOutofStock = false;
            //缺货的时效订单
            List<IpVipTimeOrderRelation> outStockRelationList = new ArrayList<>();

            List<String> wareHouseList = new ArrayList<>();

            List<String> occupiedOrderSnList = gsiIpBTimeOrderVipOrderSnMapper.selectByOrderSn(orderSn);
            if (CollectionUtils.isEmpty(occupiedOrderSnList)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("ES分库键查询为空！");
            }

            for (String s : occupiedOrderSnList) {
                IpVipTimeOrderRelation ipVipTimeOrderRelation = this.selectTimeOrder(s);
                String occupiedOrderSn = ipVipTimeOrderRelation.getOccupiedOrderSn();
                Integer status = ipVipTimeOrderRelation.getIpBTimeOrderVip().getStatus();
                if(ipVipTimeOrderRelation == null){
                    log.info(this.getClass().getName() + " 时效订单单号：occupiedOrderSn = {} 查询单据为空！",occupiedOrderSn);
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(" 时效订单单号：occupiedOrderSn = " + occupiedOrderSn + "查询单据为空！");
                    return v14;
                }
                if(TimeOrderVipStatusEnum.CANCELLED.getValue().equals(status)){
                    //排除已取消状态的时效订单
                    continue;
                }
                if (CollectionUtils.isEmpty(ipVipTimeOrderRelation.getIpBTimeOrderVipOccupyItemList())) {
                    log.info(this.getClass().getName() + " 时效订单单号：occupiedOrderSn = {} 查无对应库存占用明细！",occupiedOrderSn);
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage(" 时效订单单号：occupiedOrderSn = " + occupiedOrderSn + "查无对应库存占用明细！");
                    return v14;
                }

                if (TimeOrderVipStatusEnum.OUT_STOCK.getValue().equals(status)) {
                    isOutofStock = true;
                    outStockRelationList.add(ipVipTimeOrderRelation);
                }

                //明细判断
                for (IpBTimeOrderVipOccupyItem vipOccupyItem : ipVipTimeOrderRelation.getIpBTimeOrderVipOccupyItemList()) {
                    if (StringUtils.isNotEmpty(vipOccupyItem.getWarehouseCode())) {
                        wareHouseList.add(vipOccupyItem.getWarehouseCode());
                    } else {
                        log.info(this.getClass().getName() + " 时效订单单号：occupiedOrderSn = {} 寻源实体仓在仓库对照表中未找到平台仓库编码！",occupiedOrderSn);
                        //v14.setCode(ResultCode.FAIL);
                        v14.setCode(-2);
                        v14.setMessage(" 时效订单单号：occupiedOrderSn = " + occupiedOrderSn + "寻源实体仓在仓库对照表中未找到平台仓库编码！");
                        return v14;
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(deliveryItems)) {
                if (deliveryItems.size() != occupiedOrderSnList.size()) {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("orderSn =" + orderSn + "寻仓单明细数量不等于对应时效订单数量");
                    return v14;
                }
            } else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("orderSn =" + orderSn + "寻仓单明细为空！");
                return v14;
            }
            if (CollectionUtils.isEmpty(wareHouseList)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("orderSn =" + orderSn + "无对平台仓库编码！");
                return v14;
            }

            //判断是否YY经销商发货，是的话如果JITX寻仓结果表有记录，反馈寻仓结果表中的唯品会仓库编码
            Long timeId = deliveryRelation.getIpVipTimeOrderRelationList().get(0).getIpBTimeOrderVip().getId();
            SgBShareOutSaveRequest request = new SgBShareOutSaveRequest();
            request.setSourceBillId(timeId);
            request.setSourceBillType(com.burgeon.r3.sg.basic.common.SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
            ValueHolderV14<Map<Long,Boolean>> isYyV14 = sgRpcService.queryShareStoreIsYy(Collections.singletonList(request));
            if(isYyV14.isOK() && !ObjectUtils.isEmpty(isYyV14.getData()) && Boolean.TRUE.equals(isYyV14.getData().get(timeId))){

                //如果寻源实体仓id不为空，且存在YY占单任务，更新YY占单任务实体仓id字段
                List<OcBJitxDealerOrderTask> dealerOrderTasks = dealerOrderTaskMapper
                        .selectList(new LambdaQueryWrapper<OcBJitxDealerOrderTask>()
                                .eq(OcBJitxDealerOrderTask::getTid,orderSn)
                                .eq(OcBJitxDealerOrderTask::getType,JitxDealerTaskTypeEnum.YY_OCCUPY.getCode())
                                .eq(OcBJitxDealerOrderTask::getIsactive,R3CommonResultConstants.VALUE_Y));
                if(CollectionUtils.isNotEmpty(dealerOrderTasks) && !ObjectUtils.isEmpty(deliveryRelation.getCpCWarehouseId())){
                    dealerOrderTasks.sort(Comparator.comparing(OcBJitxDealerOrderTask::getCreationdate));
                    OcBJitxDealerOrderTask update = new OcBJitxDealerOrderTask();
                    update.setId(dealerOrderTasks.get(dealerOrderTasks.size()-1).getId());
                    update.setCpCPhyWarehouseId(deliveryRelation.getCpCWarehouseId());
                    update.setModifieddate(new Date());
                    dealerOrderTaskMapper.updateById(update);
                }

                //查询JITX寻仓结果
                List<IpBJitxDeliveryRecord> deliveryRecordList = deliveryRecordMapper
                        .selectList(new LambdaQueryWrapper<IpBJitxDeliveryRecord>()
                                .eq(IpBJitxDeliveryRecord::getTid,orderSn)
                                .eq(IpBJitxDeliveryRecord::getIsactive, R3CommonResultConstants.VALUE_Y));
                if(CollectionUtils.isNotEmpty(deliveryRecordList)){
                    IpBJitxDeliveryRecord record = deliveryRecordList.stream()
                            .max(Comparator.comparing(IpBJitxDeliveryRecord::getApplicationTime)).get();
                    v14.setMessage(record.getVipStoreCode());
                    v14.setCode(ResultCode.SUCCESS);
                    return v14;
                }
            }

            //是否JITX门店
            Integer isStoreDelivery = ipBJitxDelivery.getIsStoreDelivery();
            if(isStoreDelivery.equals(1)){
                //是JITX门店
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage(wareHouseList.get(0));
            }else{
                //非JITX门店，判断是否缺货
                if(isOutofStock){
                    //缺货，判断是否一单一件缺货
                    boolean isSinglePieceOfOutStock = checkASinglePieceOfOutStock(outStockRelationList);
                    if(isSinglePieceOfOutStock){

                        v14.setCode(ResultCode.SUCCESS);
                        v14.setMessage(wareHouseList.get(0));
                    }else{
                        v14.setCode(ResultCode.FAIL);
                        v14.setMessage("非JITX门店，缺货，确认为JIT");
                    }
                }else{
                    //不缺货，判断是否同一个仓库
                    boolean isRepeat = this.checkRepeatList(wareHouseList);
                    if(isRepeat){
                        //是否在寻仓列表中
                        String warehouseCode = wareHouseList.get(0);
                        String availableWarestr = ipBJitxDelivery.getAvailableWarehouses();
                        List<String> availableWares = new ArrayList<>();
                        if (StringUtils.isNotEmpty(availableWarestr)) {
                            availableWares = JSON.parseArray(availableWarestr, String.class);
                            //判断匹配仓是否在可用仓列表
                            v14 = ipJitxDeliveryService.isAvailableWareHouseExists(availableWares, warehouseCode);
                            if (v14.getCode() == ResultCode.FAIL) {
                                v14.setCode(ResultCode.FAIL);
                                v14.setMessage("时效订单仓库不在可选仓库列表之内，确认为JIT");
                            } else {
                                v14.setCode(ResultCode.SUCCESS);
                                v14.setMessage(warehouseCode);
                            }
                        }else{
                            v14.setCode(ResultCode.FAIL);
                            v14.setMessage("寻仓单寻仓列表为空，确认为JIT");
                        }
                    }else{
                        //确认为JIT
                        v14.setCode(ResultCode.FAIL);
                        v14.setMessage("时效订单非同一仓库，确认为JIT");
                    }
                }
            }
        } catch (Exception ex) {
            log.error(" 寻仓反馈异常：{}", Throwables.getStackTraceAsString(ex));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("异常！" + ex);
        }
        return v14;
    }

    /**
     * 判断是否一单一件缺货
     * @param ipVipTimeOrderRelations
     */
    public Boolean checkASinglePieceOfOutStock(List<IpVipTimeOrderRelation> ipVipTimeOrderRelations){
        Boolean isSinglePieceOfOutStock = true;
        for(IpVipTimeOrderRelation relation:ipVipTimeOrderRelations){
            List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipOccupyItemList = relation.getIpBTimeOrderVipOccupyItemList();
            if(!CollectionUtils.isEmpty(ipBTimeOrderVipOccupyItemList) && ipBTimeOrderVipOccupyItemList.size() == 1){
                IpBTimeOrderVipOccupyItem occupyItem = ipBTimeOrderVipOccupyItemList.get(0);
                //缺货数量
                BigDecimal qutStockQuantity = occupyItem.getOutStockQuantity() == null?BigDecimal.ZERO:occupyItem.getOutStockQuantity();
                //是否是一单一件缺货
                if(qutStockQuantity.compareTo(BigDecimal.ONE) != 0){
                    isSinglePieceOfOutStock = false;
                    break;
                }
            }
        }
        return isSinglePieceOfOutStock;
    }


    /**
     * 取消时效订单 - 更新时效订单为：已取消
     * @param occupiedOrderSn
     */
    public void updateTimeOrderIsactiveN(String occupiedOrderSn,User user,String Sysremark) {

        //更新时效订单状态为：已取消
        IpBTimeOrderVip updateTimeOrder = new IpBTimeOrderVip();
        updateTimeOrder.setStatus(TimeOrderVipStatusEnum.CANCELLED.getValue());
        updateTimeOrder.setModifieddate(new Date());
        updateTimeOrder.setModifierid(Long.valueOf(user.getId()));
        updateTimeOrder.setModifiername(user.getName());
        updateTimeOrder.setModifierename(user.getEname());
        updateTimeOrder.setSysremark(Sysremark);
        ipBTimeOrderVipMapper.update(updateTimeOrder,
                new QueryWrapper<IpBTimeOrderVip>()
                        .in("occupied_order_sn",occupiedOrderSn));
        IpBTimeOrderVip ipBTimeOrderVip = ipBTimeOrderVipMapper.selectOne(Wrappers.<IpBTimeOrderVip>lambdaQuery()
                .select(IpBTimeOrderVip::getId)
                .eq(IpBTimeOrderVip::getOccupiedOrderSn, occupiedOrderSn));
        if(ipBTimeOrderVip != null){
            //更新时效订单占用明细状态为：未确认
            IpBTimeOrderVipOccupyItem updateData = new IpBTimeOrderVipOccupyItem();
            updateData.setStatus(TimeOrderOccupyItemStatusEnum.NOT_CONFIRM.getValue());
            updateData.setModifieddate(new Date());
            int i = ipBTimeOrderVipOccupyItemMapper.update(updateData,
                    new QueryWrapper<IpBTimeOrderVipOccupyItem>()
                            .eq("ip_b_time_order_vip_id",ipBTimeOrderVip.getId()));
        }
    }


    /**
     * 查询时效订单是否全部缺货
     * @param orderInfo
     * @return
     */
    public Boolean isAllStock(IpVipTimeOrderRelation orderInfo){
        boolean isAllStock = false;
        IpBTimeOrderVip timeOrderVip = orderInfo.getIpBTimeOrderVip();
        //如果时效单状态为待占单，则直接返回全缺
        if(TimeOrderVipStatusEnum.CREATED.getValue().equals(timeOrderVip.getStatus()) ){
            return true;
        }
        //主表缺货数量
        BigDecimal outStock = timeOrderVip.getOutStockQuantity();
        List<IpBTimeOrderVipItemEx> vipItemList = orderInfo.getIpBTimeOrderVipItemExList();
        if(!CollectionUtils.isEmpty(vipItemList)){
            IpBTimeOrderVipItemEx ipBTimeOrderVipItemEx = vipItemList.get(0);
            BigDecimal vipItemAmount = ipBTimeOrderVipItemEx.getAmount();
            if(outStock.compareTo(vipItemAmount) == 0){
                isAllStock = true;
            }
        }else{
            log.error("时效订单OccupiedOrderSn:{} 库存占用明细为空",orderInfo.getOccupiedOrderSn());
        }
        return isAllStock;
    }


    /**
     * 更新虚拟寻源
     * @param timeOrderIdList
     * @param wareHouseId
     * @param wareHouseEname
     * @param wareHouseEcode
     */
    public void updateTimeOrderVirtualOccupy(List<Long> timeOrderIdList,Long wareHouseId,String wareHouseEname,String wareHouseEcode){
        if(CollectionUtils.isEmpty(timeOrderIdList)){
            return;
        }
        IpBTimeOrderVipOccupyItem vipOccupyItem = new IpBTimeOrderVipOccupyItem();
        vipOccupyItem.setModifieddate(new Date());
        vipOccupyItem.setCpCPhyWarehouseId(wareHouseId);
        if(!wareHouseId.equals(-1)){
            vipOccupyItem.setCpCPhyWarehouseEcode(wareHouseEcode);
            vipOccupyItem.setCpCPhyWarehouseEname(wareHouseEname);
        }
        int i = ipBTimeOrderVipOccupyItemMapper.update(vipOccupyItem,
                new QueryWrapper<IpBTimeOrderVipOccupyItem>()
                        .eq("isactive","Y")
                        .in("ip_b_time_order_vip_id",timeOrderIdList));
    }


}
