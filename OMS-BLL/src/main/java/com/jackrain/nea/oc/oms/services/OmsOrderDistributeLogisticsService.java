package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.api.AcLogisticsFeeQueryCmd;
import com.jackrain.nea.ac.model.request.AcExpectedLogisticsFeeQueryRequest;
import com.jackrain.nea.ac.model.request.AcStExpressQueryRequest;
import com.jackrain.nea.ac.model.result.AcExpectedLogisticsFeeQueryResult;
import com.jackrain.nea.ac.model.result.AcStExpressQueryResult;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.dto.DistributeShopAndProLogisticsDTO;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderDoublellPresaleStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsJudgingCondition;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPackageType;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OmsRemarkPriority;
import com.jackrain.nea.oc.oms.model.enums.OmsSTAreaType;
import com.jackrain.nea.oc.oms.model.enums.OmsSTBillType;
import com.jackrain.nea.oc.oms.model.enums.OmsSTDayType;
import com.jackrain.nea.oc.oms.model.enums.OmsSysParam;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.api.ShopStrategyQueryServiceCmd;
import com.jackrain.nea.st.model.request.ExpressAreaRequest;
import com.jackrain.nea.st.model.request.WarehouseLogisticsRankRequest;
import com.jackrain.nea.st.model.result.StCShopStrategyLogisticsItemResult;
import com.jackrain.nea.st.model.result.StCWarehouseLogisticStrategyResult;
import com.jackrain.nea.st.model.table.StCExpressAllocationItemDO;
import com.jackrain.nea.st.model.table.StCExpressAreaItemDO;
import com.jackrain.nea.st.model.table.StCExpressDO;
import com.jackrain.nea.st.model.table.StCExpressPackageDO;
import com.jackrain.nea.st.model.table.StCExpressPlanAreaItemDO;
import com.jackrain.nea.st.model.table.StCExpressProItemDO;
import com.jackrain.nea.st.model.table.StCExpressWarehouseItemDO;
import com.jackrain.nea.st.model.table.StCProLogisticStrategy;
import com.jackrain.nea.st.model.table.StCShopLogisticStrategyItem;
import com.jackrain.nea.st.model.table.StCShopStrategyLogisticsItem;
import com.jackrain.nea.st.model.table.StCWarehouseLogisticsRankDO;
import com.jackrain.nea.st.service.OmsDistributeLogisticStRpcService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.AssertUtils;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.SplitListUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 全渠道订单分配物流处理服务
 *
 * @author: 胡林洋
 * @since: 2019-08-22
 * create at : 2019-08-22 14:11
 */
@Component
@Slf4j
public class OmsOrderDistributeLogisticsService {

    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OmsDistributeLogisticStRpcService omsDistributeLogisticStRpcService;
    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsOrderLogService orderLogService;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;

    @DubboReference(group = "ac", version = "1.0")
    private AcLogisticsFeeQueryCmd acLogisticsFeeQueryCmd;

    @DubboReference(group = "st", version = "1.0")
    private ShopStrategyQueryServiceCmd shopStrategyQueryCmd;

    @Autowired
    private AsyncTaskManager asyncTaskManager;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private StCBusinessTypeMapper typeMapper;

    private static final String OC_B_ORDER_EXPRESS_STARTING_WEIGHT = "business_system:oc_b_order_express_starting_weight";

    @Autowired
    private OrderExceptionTagService orderExceptionTagService;
    @Autowired
    private ThreadPoolTaskExecutor createDistributionLogisticsOrderThreadPoolExecutor;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    /**
     * 分物流
     *
     * @param orderRelation
     * @return
     */
    public CpCLogistics distributeLogistics(OcBOrderRelation orderRelation) {
        OcBOrderRelation normal = new OcBOrderRelation();
        BeanCopierUtil.copy(orderRelation, normal);
        List<OcBOrderItem> itemList = orderRelation.getOrderItemList();
        itemList = itemList.stream().filter(p -> p.getProType() != null && p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        normal.setOrderItemList(itemList);
        ValueHolderV14<Long> v14 = this.adapterExpress(normal, SystemUserResource.getRootUser());
        OcBOrder order = new OcBOrder();
        order.setId(orderRelation.getOrderInfo().getId());
        order.setExceptionExplain(orderRelation.getOrderInfo().getExceptionExplain());
        if (v14.isOK()) {
            Long cpClogisticsId = v14.getData();
            //去除订单异常标签
            order.setIsException(OcBOrderConst.IS_STATUS_IN);
            order.setExceptionType("");
            order.setExceptionExplain("");
            omsOrderService.updateOrderInfo(order);
            if (cpClogisticsId != null) {
                return cpRpcService.queryLogisticsByIdNew(cpClogisticsId);
            } else {
                return null;
            }
        } else {
            String msg = v14.getMessage();
            orderExceptionTagService.checkMateException(order, msg, OcBOrderConst.SUBLOGISTICS_SERVICE);
            omsOrderService.updateOrderInfo(order);
            return null;
        }
    }

    /**
     * 下面这段代码，产品设计的就是这个顺序加载，我也没办法，个人能力有限，只能写成这样（我给你优化--lijin）
     * 店铺物流策略+商品物流策略
     *
     * @param relation  订单信息
     * @param isExpress 是否快运（true-快运，false-快递）
     * @return
     */
    public ValueHolderV14<List<Long>> distributeLogisticsList(OcBOrderRelation relation, boolean isExpress, User user) {
        log.info(LogUtil.format(" OmsOrderDistributeLogisticsService.distributeLogisticsList param:{}",
                "OmsOrderDistributeLogisticsService.distributeLogisticsList"), JSONObject.toJSONString(relation));
        // 订单信息
        OcBOrder order = relation.getOrderInfo();
        List<OcBOrderItem> items = relation.getOrderItemList();
        // 查询店铺物流策略
        List<StCShopLogisticStrategyItem> shopExpress = stRpcService.getShopExpress(order.getCpCShopId());
        log.info("shopExpress:{}", JSONObject.toJSONString(shopExpress));
        //匹配卖家备注
        List<Long> logisticsIds = matchSellerMemo(order.getSellerMemo(), shopExpress);
        if (CollectionUtils.isNotEmpty(logisticsIds)) {
            this.recordLog(order.getId(), order.getBillNo(), "店铺物流策略命中了卖家备注", user);
            return new ValueHolderV14<>(logisticsIds, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
        }
        // 定义店铺和商品物流优先级DTO
        DistributeShopAndProLogisticsDTO logisticsDTO = new DistributeShopAndProLogisticsDTO();
        //根据店铺物流明细按照不同维度分类
        shopLogisticsClassify(isExpress, order, shopExpress, logisticsDTO);

        //商品款号ids
        List<Long> psCProIdList = new ArrayList<>();
        //四级分类ids
        List<Long> dimIdList = new ArrayList<>();
        //平台商品ID
        List<String> numIids = new ArrayList<>();
        for (OcBOrderItem item : items) {
            psCProIdList.add(item.getPsCProId());
            dimIdList.add(item.getMDim6Id());
            numIids.add(item.getNumIid());
        }
        //查询商品物流
        List<StCProLogisticStrategy> proLogisticStrategy =
                stRpcService.queryStCProLogisticStrategy(psCProIdList, dimIdList, numIids, order.getCpCRegionProvinceId(),
                        order.getCpCPhyWarehouseId(), order.getCpCRegionCityId(), order.getCpCRegionAreaId());
        log.info("proLogisticStrategy:{}", JSONObject.toJSONString(proLogisticStrategy));
        //根据商品物流明细按照不同维度分类
        proLogisticsClassify(isExpress, proLogisticStrategy, logisticsDTO);
        log.info(LogUtil.format(" OmsOrderDistributeLogisticsService.distributeLogisticsList logisticsDTO:{}",
                "OmsOrderDistributeLogisticsService.distributeLogisticsList"), logisticsDTO.getJsonBySort());
        //匹配物流取交集
        List<Long> shopExpressIds;
        try {
            shopExpressIds = matchLogisticsByItem(items, logisticsDTO);
        } catch (Exception e) {
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
        return new ValueHolderV14<>(shopExpressIds, ResultCode.SUCCESS, SgConstants.MESSAGE_STATUS_SUCCESS);
    }

    /**
     * 匹配每行的物流取交集（都没有命中就没事，没交集就有事）
     *
     * @param items
     * @param logisticsDTO
     * @return
     */
    private List<Long> matchLogisticsByItem(List<OcBOrderItem> items, DistributeShopAndProLogisticsDTO logisticsDTO) {
        List<Long> shopExpressIds = new ArrayList<>();
        for (OcBOrderItem item : items) {
            Long psCProId = item.getPsCProId();
            Long mDim6Id = item.getMDim6Id();
            String numIid = item.getNumIid();
            //根据店铺/商品优先级获取物流公司ids
            List<Long> idList = getLogisticsIdList(psCProId, mDim6Id, numIid, logisticsDTO);
            log.info("shopAndPro Match after itemId:{},psCProId:{},mDim6Id:{},numIid:{},idList:{}",
                    item.getId(), psCProId, mDim6Id, numIid, JSON.toJSONString(idList));
            //没有命中就跟着有命中的走
            if (CollectionUtils.isEmpty(idList)) {
                continue;
            }
            // 第一条明细取第一个，否则取交集
            if (CollectionUtils.isEmpty(shopExpressIds)) {
                shopExpressIds = idList;
            } else {
                shopExpressIds.retainAll(idList);
            }
            if (CollectionUtils.isEmpty(shopExpressIds)) {
                throw new NDSException("店铺物流策略/商品物流策略，匹配到明细间可发物流公司无交集！");
            }
        }
        return shopExpressIds;
    }

    /**
     * 店铺物流匹配买家备注
     *
     * @param sellerMemo  订单上的卖家备注
     * @param shopExpress 店铺物流明细
     * @return
     */
    private List<Long> matchSellerMemo(String sellerMemo, List<StCShopLogisticStrategyItem> shopExpress) {
        List<Long> logisticsIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(shopExpress) && StringUtils.isNotEmpty(sellerMemo)) {
            Map<String, List<Long>> logisticsItemMap = shopExpress.stream().filter(s -> StringUtils.isNotEmpty(s.getSellerRemark()))
                    .collect(Collectors.groupingBy(
                            StCShopLogisticStrategyItem::getSellerRemark,
                            Collectors.mapping(StCShopLogisticStrategyItem::getCpCLogisticsId, Collectors.toList())
                    ));
            if (MapUtils.isEmpty(logisticsItemMap)) {
                return logisticsIds;
            }
            for (String remark : logisticsItemMap.keySet()) {
                if (sellerMemo.contains(remark)) {
                    logisticsIds.addAll(logisticsItemMap.get(remark));
                }
            }
        }
        return logisticsIds;
    }

    /**
     * 根据商品物流明细按照不同维度分类
     *
     * @param isExpress           是否快运（true-快运，false-快递）
     * @param proLogisticStrategy 商品物流策略
     * @param logisticsDTO        店铺/商品物流优先级对象
     */
    private void proLogisticsClassify(boolean isExpress, List<StCProLogisticStrategy> proLogisticStrategy,
                                      DistributeShopAndProLogisticsDTO logisticsDTO) {
        if (CollectionUtils.isEmpty(proLogisticStrategy)) {
            return;
        }
        proLogisticStrategy.forEach(x -> {
            if ((isExpress && x.getLogisticType() != 2) || (!isExpress && x.getLogisticType() != 1)) {
                return;
            }
            // 1.平台商品ID
            if (StringUtils.isNotBlank(x.getNumIid()) && x.getCpCProvinceId() == null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() == null && x.getPsCProId() == null && x.getPsCProdimId() == null) {
                List<Long> idList = logisticsDTO.getProPlatformProIDMap().computeIfAbsent(x.getNumIid(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 2.商品+省市区+仓
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() != null && x.getCpCCityId() != null &&
                    x.getCpCAreaId() != null && x.getCpCPhyWarehouseId() != null && x.getPsCProId() != null && x.getPsCProdimId() == null) {
                List<Long> idList = logisticsDTO.getProProvinceAreaWarehouseProMap().computeIfAbsent(x.getPsCProId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 3.四级+省市区+仓
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() != null && x.getCpCCityId() != null &&
                    x.getCpCAreaId() != null && x.getCpCPhyWarehouseId() != null && x.getPsCProId() == null && x.getPsCProdimId() != null) {
                List<Long> idList = logisticsDTO.getProProvinceAreaWarehouseDimMap().computeIfAbsent(x.getPsCProdimId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 4. 省 + 仓 + 商品
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() != null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() != null && x.getPsCProId() != null && x.getPsCProdimId() == null) {
                List<Long> idList = logisticsDTO.getProProvinceWarehouseProMap().computeIfAbsent(x.getPsCProId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 5. 省 + 仓 + 品相
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() != null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() != null && x.getPsCProId() == null && x.getPsCProdimId() != null) {
                List<Long> idList = logisticsDTO.getProProvinceWarehouseDimMap().computeIfAbsent(x.getPsCProdimId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 6. 仓 + 商品
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() == null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() != null && x.getPsCProId() != null && x.getPsCProdimId() == null) {
                List<Long> idList = logisticsDTO.getProWarehouseProMap().computeIfAbsent(x.getPsCProId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 7. 仓 + 品相
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() == null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() != null && x.getPsCProId() == null && x.getPsCProdimId() != null) {
                List<Long> idList = logisticsDTO.getProWarehouseDimMap().computeIfAbsent(x.getPsCProdimId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 8. 仓 + 省
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() != null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() != null && x.getPsCProId() == null && x.getPsCProdimId() == null) {
                logisticsDTO.getProProvinceWarehouseList().add(x.getCpCLogisticsId());
                return;
            }
            // 9. 仓
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() == null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() != null && x.getPsCProId() == null && x.getPsCProdimId() == null) {
                logisticsDTO.getProWarehouseIdList().add(x.getCpCLogisticsId());
                return;
            }
            // 10.商品+省市区
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() != null && x.getCpCCityId() != null &&
                    x.getCpCAreaId() != null && x.getCpCPhyWarehouseId() == null && x.getPsCProId() != null && x.getPsCProdimId() == null) {
                List<Long> idList = logisticsDTO.getProProvinceAreaProMap().computeIfAbsent(x.getPsCProId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 11.四级+省市区
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() != null && x.getCpCCityId() != null &&
                    x.getCpCAreaId() != null && x.getCpCPhyWarehouseId() == null && x.getPsCProId() == null && x.getPsCProdimId() != null) {
                List<Long> idList = logisticsDTO.getProProvinceAreaDimMap().computeIfAbsent(x.getPsCProdimId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 12. 省 + 商品
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() != null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() == null && x.getPsCProId() != null && x.getPsCProdimId() == null) {
                List<Long> idList = logisticsDTO.getProProvinceProMap().computeIfAbsent(x.getPsCProId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 13. 省 + 品相
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() != null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() == null && x.getPsCProId() == null && x.getPsCProdimId() != null) {
                List<Long> idList = logisticsDTO.getProProvinceDimMap().computeIfAbsent(x.getPsCProdimId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 14. 商品
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() == null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() == null && x.getPsCProId() != null && x.getPsCProdimId() == null) {
                List<Long> idList = logisticsDTO.getProProMap().computeIfAbsent(x.getPsCProId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 15. 品相
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() == null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() == null && x.getPsCProId() == null && x.getPsCProdimId() != null) {
                List<Long> idList = logisticsDTO.getProDimMap().computeIfAbsent(x.getPsCProdimId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 16. 省
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() != null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() == null && x.getPsCProId() == null && x.getPsCProdimId() == null) {
                logisticsDTO.getProProvinceIdList().add(x.getCpCLogisticsId());
                return;
            }
            // 17. 空
            if (StringUtils.isBlank(x.getNumIid()) && x.getCpCProvinceId() == null && x.getCpCCityId() == null &&
                    x.getCpCAreaId() == null && x.getCpCPhyWarehouseId() == null && x.getPsCProId() == null && x.getPsCProdimId() == null) {
                logisticsDTO.getProNoAllList().add(x.getCpCLogisticsId());
            }
        });
    }

    /**
     * 根据店铺物流按照不同维度分类
     *
     * @param isExpress    是否快运（true-快运，false-快递）
     * @param order        订单信息
     * @param shopExpress  店铺物流明细
     * @param logisticsDTO 店铺/商品物流优先级对象
     */
    private void shopLogisticsClassify(boolean isExpress, OcBOrder order, List<StCShopLogisticStrategyItem> shopExpress,
                                       DistributeShopAndProLogisticsDTO logisticsDTO) {
        if (CollectionUtils.isEmpty(shopExpress)) {
            return;
        }
        shopExpress.forEach(x -> {
            if ((isExpress && x.getLogisticType() != 2) || (!isExpress && x.getLogisticType() != 1)) {
                return;
            }
            //省 + 市 + 仓 + 商品
            if (x.getCpCProvinceId() != null && x.getCpCCityId() != null && x.getCpCPhyWarehouseId() != null
                    && x.getPsCProId() != null && x.getPsCProdimId() == null) {
                if (!x.getCpCProvinceId().equals(order.getCpCRegionProvinceId())
                        || !x.getCpCCityId().equals(order.getCpCRegionCityId())
                        || !x.getCpCPhyWarehouseId().equals(order.getCpCPhyWarehouseId())) {
                    return;
                }
                List<Long> idList = logisticsDTO.getShopProvinceCityWarehouseProMap().computeIfAbsent(x.getPsCProId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            //省 + 市 + 仓 + 四级分类
            if (x.getCpCProvinceId() != null && x.getCpCCityId() != null && x.getCpCPhyWarehouseId() != null
                    && x.getPsCProId() == null && x.getPsCProdimId() != null) {
                if (!x.getCpCProvinceId().equals(order.getCpCRegionProvinceId())
                        || !x.getCpCCityId().equals(order.getCpCRegionCityId())
                        || !x.getCpCPhyWarehouseId().equals(order.getCpCPhyWarehouseId())) {
                    return;
                }
                List<Long> idList = logisticsDTO.getShopProvinceCityWarehouseDimMap().computeIfAbsent(x.getPsCProdimId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            //省 + 仓 + 商品
            if (x.getCpCProvinceId() != null && x.getCpCCityId() == null && x.getCpCPhyWarehouseId() != null
                    && x.getPsCProId() != null && x.getPsCProdimId() == null) {
                if (!x.getCpCProvinceId().equals(order.getCpCRegionProvinceId())
                        || !x.getCpCPhyWarehouseId().equals(order.getCpCPhyWarehouseId())) {
                    return;
                }
                List<Long> idList = logisticsDTO.getShopProvinceWarehouseProMap().computeIfAbsent(x.getPsCProId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            //仓 + 商品
            if (x.getCpCProvinceId() == null && x.getCpCCityId() == null && x.getCpCPhyWarehouseId() != null
                    && x.getPsCProId() != null && x.getPsCProdimId() == null) {
                if (!x.getCpCPhyWarehouseId().equals(order.getCpCPhyWarehouseId())) {
                    return;
                }
                List<Long> idList = logisticsDTO.getShopWarehouseProMap().computeIfAbsent(x.getPsCProId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            //省 + 仓
            if (x.getCpCProvinceId() != null && x.getCpCCityId() == null && x.getCpCPhyWarehouseId() != null
                    && x.getPsCProId() == null && x.getPsCProdimId() == null) {
                if (!x.getCpCProvinceId().equals(order.getCpCRegionProvinceId())
                        || !x.getCpCPhyWarehouseId().equals(order.getCpCPhyWarehouseId())) {
                    return;
                }
                logisticsDTO.getShopProvinceWarehouseList().add(x.getCpCLogisticsId());
                return;
            }
            // 商品
            if (x.getCpCProvinceId() == null && x.getCpCCityId() == null && x.getCpCPhyWarehouseId() == null
                    && x.getPsCProId() != null && x.getPsCProdimId() == null) {
                List<Long> idList = logisticsDTO.getProMap().computeIfAbsent(x.getPsCProId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            // 四级分类
            if (x.getCpCProvinceId() == null && x.getCpCCityId() == null && x.getCpCPhyWarehouseId() == null
                    && x.getPsCProId() == null && x.getPsCProdimId() != null) {
                List<Long> idList = logisticsDTO.getShopDimMap().computeIfAbsent(x.getPsCProdimId(), k -> new ArrayList<>());
                idList.add(x.getCpCLogisticsId());
                return;
            }
            //仓
            if (x.getCpCProvinceId() == null && x.getCpCCityId() == null && x.getCpCPhyWarehouseId() != null
                    && x.getPsCProId() == null && x.getPsCProdimId() == null) {
                if (!x.getCpCPhyWarehouseId().equals(order.getCpCPhyWarehouseId())) {
                    return;
                }
                logisticsDTO.getShopWarehouseList().add(x.getCpCLogisticsId());
            }
        });
    }

    /**
     * 根据排好的优先级按照不同维度去命中
     *
     * @param proId        商品款号
     * @param mDim6Id      商品四级
     * @param numIid       平台商品ID
     * @param logisticsDTO 店铺/商品物流优先级对象
     * @return
     */
    private List<Long> getLogisticsIdList(Long proId, Long mDim6Id, String numIid,
                                          DistributeShopAndProLogisticsDTO logisticsDTO) {
        if (CollectionUtils.isNotEmpty(logisticsDTO.getShopProvinceCityWarehouseProMap().get(proId))) {
            return logisticsDTO.getShopProvinceCityWarehouseProMap().get(proId);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getShopProvinceCityWarehouseDimMap().get(mDim6Id))) {
            return logisticsDTO.getShopProvinceCityWarehouseDimMap().get(mDim6Id);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getShopProvinceWarehouseProMap().get(proId))) {
            return logisticsDTO.getShopProvinceWarehouseProMap().get(proId);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getShopWarehouseProMap().get(proId))) {
            return logisticsDTO.getShopWarehouseProMap().get(proId);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getShopProvinceWarehouseList())) {
            return logisticsDTO.getShopProvinceWarehouseList();
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProMap().get(proId))) {
            return logisticsDTO.getProMap().get(proId);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getShopDimMap().get(mDim6Id))) {
            return logisticsDTO.getShopDimMap().get(mDim6Id);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getShopWarehouseList())) {
            return logisticsDTO.getShopWarehouseList();
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProPlatformProIDMap().get(numIid))) {
            return logisticsDTO.getProPlatformProIDMap().get(numIid);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProProvinceAreaWarehouseProMap().get(proId))) {
            return logisticsDTO.getProProvinceAreaWarehouseProMap().get(proId);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProProvinceAreaWarehouseDimMap().get(mDim6Id))) {
            return logisticsDTO.getProProvinceAreaWarehouseDimMap().get(mDim6Id);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProProvinceWarehouseProMap().get(proId))) {
            return logisticsDTO.getProProvinceWarehouseProMap().get(proId);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProProvinceWarehouseDimMap().get(mDim6Id))) {
            return logisticsDTO.getProProvinceWarehouseDimMap().get(mDim6Id);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProWarehouseProMap().get(proId))) {
            return logisticsDTO.getProWarehouseProMap().get(proId);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProWarehouseDimMap().get(mDim6Id))) {
            return logisticsDTO.getProWarehouseDimMap().get(mDim6Id);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProProvinceWarehouseList())) {
            return logisticsDTO.getProProvinceWarehouseList();
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProWarehouseIdList())) {
            return logisticsDTO.getProWarehouseIdList();
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProProvinceAreaProMap().get(proId))) {
            return logisticsDTO.getProProvinceAreaProMap().get(proId);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProProvinceAreaDimMap().get(mDim6Id))) {
            return logisticsDTO.getProProvinceAreaDimMap().get(mDim6Id);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProProvinceProMap().get(proId))) {
            return logisticsDTO.getProProvinceProMap().get(proId);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProProvinceDimMap().get(mDim6Id))) {
            return logisticsDTO.getProProvinceDimMap().get(mDim6Id);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProProMap().get(proId))) {
            return logisticsDTO.getProProMap().get(proId);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProDimMap().get(mDim6Id))) {
            return logisticsDTO.getProDimMap().get(mDim6Id);
        }
        if (CollectionUtils.isNotEmpty(logisticsDTO.getProProvinceIdList())) {
            return logisticsDTO.getProProvinceIdList();
        }
        return logisticsDTO.getProNoAllList();
    }

    /**
     * 【重构后】分配物流服务新入口
     *
     * @param orderInfo 订单对象
     * @return Long
     */

    public Long orderDistributeLogistics(OcBOrderRelation orderInfo, User user) {

        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Long defaultLogisticsId = config.getProperty("defaultLogisticsId", 0L);
        if (defaultLogisticsId != 0L) {
            return defaultLogisticsId;
        }
        /**step1:判断是否为唯品会订单,如果平台为唯品会则不会走分配物流服务逻辑,直接返回原订单物流公司id*/
        if (checkIsJitxOrder(orderInfo)) {
            return orderInfo.getOrderInfo().getCpCLogisticsId();
        }
        /**step2:判断是否为京东货到付款订单,分配京东物流公司*/
        if (checkIsJdCashDeliveryOrder(orderInfo)) {
            return -2L;
        }
        Long retLogisticsId = 0L;
        /**step3:判断订单信息中,省份id或者市id或者区id为空,如果是,订单为异常订单,需要人工手动处理*/
        if (checkIsNullProvinceIdOrCityId(orderInfo, user)) {
            return retLogisticsId;
        }
        /**step4:判断订单状态和wms撤回状态是否正常*/
        if (!checkOrderStatus(orderInfo, user)) {
            return retLogisticsId;
        }
        try {
            /**step6:【取物流区域设置里符合订单地址的物流公司】根据订单收货地址筛选出可发的物流（含有排除区域逻辑）*/
            List<Long> stCExpressAreaList = filterExpressArea(orderInfo);
            /**step7:【取仓库物流规则下物流公司】根据发货仓库id 和订单商品数量获取物流分配规则明细中的物流公司*/
            List<StCExpressAllocationItemDO> stCExpressAllocationItemList = getLogisticsFromWarehouseRule(orderInfo);
            /**step8:【物流区域设置】和【仓库物流规则】下的物流公司取交集*/
            List<Long> baseIntersectionList = getExpressAreaAndWareHouseRuleIntersection(orderInfo,
                    stCExpressAllocationItemList, stCExpressAreaList);
            /**step9:判断【物流区域设置】和【仓库物流规则】下的物流公司交集的个数(个数=0 基础数据无可用物流; 个数=1 返回当前物流; 个数>1 继续执行step10走物流方案)*/
            OcBOrder ocBOrder = orderInfo.getOrderInfo();
            if (PlatFormEnum.JINGDONG.getCode().equals(ocBOrder.getPlatform())) {
                String jdNoSendLogisticsStr = getJdNoSendLogisticsStr();
                /**step10:京东平台订单时,基础数据中过滤掉京东店铺不发的物流公司*/
                if (baseIntersectionList.size() > 0) {
                    baseIntersectionList = removeNoSendLogistics(jdNoSendLogisticsStr, baseIntersectionList,
                            orderInfo, "jd");
                }
            } else if (PlatFormEnum.TAOBAO_DISTRIBUTION.getCode().equals(ocBOrder.getPlatform()) || PlatFormEnum.TAOBAO_DEAL.getCode().equals(ocBOrder.getPlatform()) || PlatFormEnum.TAOBAO.getCode().equals(ocBOrder.getPlatform())) {
                String tbNoSendLogisticsStr = getTbNoSendLogisticsStr();
                /**step10:淘宝平台、淘宝经销、淘宝分销订单,将基础数据过滤掉,京东店铺不发的快递公司*/
                if (baseIntersectionList.size() > 0) {
                    baseIntersectionList = removeNoSendLogistics(tbNoSendLogisticsStr, baseIntersectionList,
                            orderInfo, "tb");
                }
            }
            if (baseIntersectionList.size() == 0) {
                //基础数据无可用物流
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), ocBOrder.getId() + "分配物流服务" + "基础数据无可用物流",
                        null, "基础数据无可用物流", user);
                return retLogisticsId;
            } else if (baseIntersectionList.size() == 1) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), ocBOrder.getId() + "分配物流服务" +
                                "根据基础数据分配唯一物流公司", null, "根据基础数据分配唯一物流公司", user);
                retLogisticsId = baseIntersectionList.get(0);
                return retLogisticsId;
            } else {
                /**step11:开始走物流方案,取物流方案中的物流公司*/
                /**step12:取出所有的符合订单下单日期的物流方案,按照物流方案的优先级和修改时间进行排序*/
                List<StCExpressDO> stCExpressList = getExpressListByOrderDateAndCurrentDate(orderInfo);
                /**step13:根据各种校验规则,过滤得到有效的物流方案列表*/
                List<StCExpressDO> isActivestCExpressList = filterLogisticsByExpress(stCExpressList, orderInfo);
                List<String> logisticNameList = getLogisticsByBuyerOrSalerRemark(orderInfo);
                List<Long> baseAndRemarkIntersectionList = new ArrayList<>();
                List<Long> baseExpressAndRemarkIntersectionList = new ArrayList<>();
                StCExpressDO activeStCExpressDO = new StCExpressDO();
                List<Long> baseAndExpressIntersectionList = new ArrayList<>();
                if (isActivestCExpressList != null && isActivestCExpressList.size() > 0) {
                    activeStCExpressDO = isActivestCExpressList.get(0);
                    String logisticStr = activeStCExpressDO.getCpCLogisticsIdSet();
                    List<String> logistics = Arrays.asList(logisticStr.split(","));
                    /**step24:将基础数据的结果集和物流方案的结果集取交集*/
                    for (Long logistic : baseIntersectionList) {
                        if (logistics.contains(logistic.toString())) {
                            baseAndExpressIntersectionList.add(logistic);
                        }
                    }
                    /**step25:将基础数据的结果集和物流方案的结果集取交集后,判断交集物流公司个数为【0】时*/
                    if (baseAndExpressIntersectionList.size() == 0) {
                        /**step26:将基础数据的结果集和买卖家备注物流公司再取交集*/
                        baseAndRemarkIntersectionList = getBaseAndRemarkIntersectionList(logisticNameList, orderInfo,
                                baseIntersectionList);
                    } else if (baseAndExpressIntersectionList.size() == 1) {
                        /**step27:将基础数据的结果集和物流方案的结果集取交集后,判断交集物流公司个数为【1】时,直接返回数据*/
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), ocBOrder.getId() + "分配物流服务" +
                                        "【基础数据的结果集】和【物流方案的结果集】取交集后，物流公司唯一，直接返回", null, "", user);
                        return baseAndExpressIntersectionList.get(0);
                    } else {
                        /**step28:将基础数据的结果集和物流方案的结果集取交集后,判断交集物流公司个数为【多个】时,再和买卖家备注的物流公司取交集*/
                        for (String logistName : logisticNameList) {
                            LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByshortName(logistName);
                            if (baseAndExpressIntersectionList.contains(logisticsInfo.getId())) {
                                baseExpressAndRemarkIntersectionList.add(logisticsInfo.getId());
                            }
                        }
                        Long retLogistId = 0L;
                        /**step29:将【基础数据的结果集和物流方案的结果集取交集后】再和【买卖家备注的物流公司】取交集,当交集个数为【0】时,
                         * 拿【基础数据的结果集和物流方案的结果集取交集】和【仓库物流优先级的物流公司】取交集*/
                        if (baseExpressAndRemarkIntersectionList.size() == 0) {
                            //再拿baseAndExpressIntersectionList和仓库物流优先级的物流公司取交集
                            List<Long> newLogisticAndRankList = getLogisticAndRankSortList(orderInfo);
                            String logTag = "【基础数据和方案取交集后】再和【仓库物流优先级的物流公司列表】";
                            retLogistId = endIntersectionLogisticId(baseAndExpressIntersectionList,
                                    newLogisticAndRankList, ocBOrder, user, logTag);
                            return retLogistId;
                        } else if (baseExpressAndRemarkIntersectionList.size() == 1) {
                            /**step30:将【基础数据的结果集和物流方案的结果集取交集后】再和【买卖家备注的物流公司】取交集,当交集个数为【1】时,直接返回数据*/
                            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                    OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), ocBOrder.getId() + "分配物流服务" +
                                            "【基础数据的结果集和物流方案的结果集取交集后】再和【买卖家备注的物流公司】取交集，物流公司唯一，直接返回", null, "", user);
                            return baseExpressAndRemarkIntersectionList.get(0);
                        } else {
                            /**step31:将【基础数据的结果集和物流方案的结果集取交集后】再和【买卖家备注的物流公司】取交集,当交集个数为【多个】时,再和【仓库物流优先级的物流公司】取交集*/
                            //再拿baseExpressAndRemarkIntersectionList结果集根据仓库物流优先级的物流公司取交集
                            /**step32:根据订单的仓库id和订单的省市,查询物流公司优先级并且根据优先级的值进行升序,得到升序后的【仓库物流优先级的物流公司】列表*/
                            List<Long> newLogisticAndRankList = getLogisticAndRankSortList(orderInfo);
                            String logTag = "【[基础数据和方案取交集后]和[订单备注取交集]】再和【仓库物流优先级的物流公司列表】";
                            retLogistId = endIntersectionLogisticId(baseExpressAndRemarkIntersectionList,
                                    newLogisticAndRankList, ocBOrder, user, logTag);
                            return retLogistId;
                        }
                    }
                } else {
                    /**step14:没有有效的物流方案,拿【基础数据物流公司】和【买卖家备注物流公司】取交集*/
                    baseAndRemarkIntersectionList = getBaseAndRemarkIntersectionList(logisticNameList, orderInfo,
                            baseIntersectionList);
                }
                Long retLogistId = 0L;
                /**step33:如果基础数据所得物流公司集合和买卖家备注物流公司无交集,则【基础数据】再和【仓库物流优先级的物流公司列表】取交集*/
                if (baseAndRemarkIntersectionList.size() == 0) {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), ocBOrder.getId() + "分配物流服务" +
                                    "【基础数据】和【买卖家备注】无交集", null, "", user);
                    //再拿baseIntersectionList和仓库物流优先级的物流公司取交集
                    List<Long> newLogisticAndRankList = getLogisticAndRankSortList(orderInfo);
                    String logTag = "【基础数据】再和【仓库物流优先级的物流公司列表】";
                    retLogistId = endIntersectionLogisticId(baseIntersectionList, newLogisticAndRankList, ocBOrder,
                            user, logTag);
                    return retLogistId;
                } else if (baseAndRemarkIntersectionList.size() == 1) {
                    /**step34:如果基础数据所得物流公司集合和买卖家备注物流公司集合有交集且唯一,则直接返回数据*/
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), ocBOrder.getId() + "分配物流服务" +
                                    "【基础数据物流公司集合】和【买卖家备注物流公司集合】取交集，且物流公司唯一，直接返回", null, "", user);
                    return baseAndRemarkIntersectionList.get(0);
                } else {
                    /**step35:【[基础数据]和[订单备注取交集]】再和【仓库物流优先级的物流公司列表】取交集*/
                    /**根据订单的仓库id和订单的省市,查询物流公司优先级并且根据优先级的值进行升序,得到升序后的物流公司列表*/
                    List<Long> newLogisticAndRankList = getLogisticAndRankSortList(orderInfo);
                    String logTag = "【[基础数据]和[订单备注取交集]】再和【仓库物流优先级的物流公司列表】";
                    retLogistId = endIntersectionLogisticId(baseAndRemarkIntersectionList, newLogisticAndRankList,
                            ocBOrder, user, logTag);
                    return retLogistId;
                }
            }
        } catch (NDSException ex) {
            log.error(LogUtil.format("分物流服务失败,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            omsOrderLogService.addUserOrderLog(orderInfo.getOrderInfo().getId(), orderInfo.getOrderInfo().getBillNo()
                    , OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), orderInfo.getOrderInfo().getId() + "分配物流服务" +
                            "分配物流服务异常", null, "分配物流服务异常", user);
        }
        return retLogisticsId;
    }

    /**
     * 判断卖家或者卖家备注中是否含有否定值
     *
     * @param expressNegativeValue 物流方案否定值
     * @param priorityValueStr     优先级字符串
     * @param buyerRemark          卖家备注
     * @param sellerRemark         卖家备注
     * @return boolean
     */
    public boolean checkNegativeValueFlag(String[] expressNegativeValue, String priorityValueStr, String buyerRemark,
                                          String sellerRemark) {
        boolean flag = false;
        for (int i = 0; i < expressNegativeValue.length; i++) {
            //如果系统参数“买卖家优先级”设置的是 【1：买家优先】
            if ((priorityValueStr.equals(OmsRemarkPriority.BUYER_PRIORITY.parseValue()) && StringUtils.isNotEmpty(buyerRemark)) || (priorityValueStr.equals(OmsRemarkPriority.SELLER_PRIORITY.parseValue()) && StringUtils.isEmpty(sellerRemark))) {
                //买家备注里包含快递否定值
                if (buyerRemark.contains(expressNegativeValue[i])) {
                    flag = true;
                    break;
                }
            } else { //如果系统参数“买卖家优先级”设置的是 【2：卖家优先 】
                //买家备注里包含快递否定值
                if (sellerRemark.contains(expressNegativeValue[i])) {
                    flag = true;
                    break;
                }
            }

        }
        return flag;
    }

    /**
     * 取出,买家或者卖家备注中的快递公司关键字
     *
     * @param logitsicsKeyList 物流关键字
     * @param priorityValueStr 优先级字符串
     * @param buyerRemark      买家备注
     * @param sellerRemark     买家备注
     * @return List<String>
     */
    public List<String> filterLogitsicsKeyWord(List<String> logitsicsKeyList, String priorityValueStr,
                                               String buyerRemark, String sellerRemark) {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < logitsicsKeyList.size(); i++) {
            //如果系统参数“买卖家优先级”设置的是 【1：买家优先】
            if ((priorityValueStr.equals(OmsRemarkPriority.BUYER_PRIORITY.parseValue()) && StringUtils.isNotEmpty(buyerRemark)) || (priorityValueStr.equals(OmsRemarkPriority.SELLER_PRIORITY.parseValue()) && StringUtils.isEmpty(sellerRemark))) {
                //买家备注里包含快递否定值
                if (buyerRemark.contains(String.valueOf(logitsicsKeyList.get(i)))) {
                    list.add(logitsicsKeyList.get(i));
                }
            } else { //如果系统参数“买卖家优先级”设置的是 【2：卖家优先 】
                //买家备注里包含快递否定值
                if (sellerRemark.contains(String.valueOf(logitsicsKeyList.get(i)))) {
                    list.add(logitsicsKeyList.get(i));
                }
            }
        }
        return list;
    }

    /**
     * 判断店铺
     *
     * @param stCExpress 物流方案实体
     * @param orderInfo  订单实体
     * @return boolean
     */
    public boolean checkExpressCShop(StCExpressDO stCExpress, OcBOrderRelation orderInfo) {
        boolean flag = false;
        //店铺(物流方案)
        String expressCShopIdStr = stCExpress.getCpCShopId();
        // 20200728易邵峰修改：如果物流方案的店铺为空，则认为可用
        if (StringUtils.isEmpty(expressCShopIdStr)) {
            return true;
        }
        String orderCpCShopId = String.valueOf(orderInfo.getOrderInfo().getCpCShopId());
        if (expressCShopIdStr.contains(",")) {
            String[] expressCShopIdArray = expressCShopIdStr.split(",");
            for (String expressCShopId : expressCShopIdArray) {
                if (expressCShopId.equals(orderCpCShopId)) {
                    flag = true;
                    break;
                }
            }
        } else {
            if (expressCShopIdStr.equals(orderCpCShopId)) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 判断日期类型
     *
     * @param stCExpress 物流方案实体
     * @param orderInfo  订单实体
     * @return boolean
     */
    public boolean checkDateType(StCExpressDO stCExpress, OcBOrderRelation orderInfo) {
        boolean flag = true;
        //日期类型(物流方案)
        Integer dayType = stCExpress.getDayType();
        if (OmsSTDayType.ORDER_DATE.toInteger() == dayType) {
            //判断“下单日期”是否在有效期内
            if (!(stCExpress.getBeginTime().before(orderInfo.getOrderInfo().getOrderDate()) && stCExpress.getEndTime().after(orderInfo.getOrderInfo().getOrderDate()))) {
                flag = false;
            }
        } else if (OmsSTDayType.DISTRIBUTION_DATE.toInteger() == dayType) {
            try {
                Date crrentTime = new Date();
                //判断“分配日期”是否在有效期内
                if (!(stCExpress.getBeginTime().before(crrentTime) && stCExpress.getEndTime().after(crrentTime))) {
                    flag = false;
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("分物流异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
                flag = false;
            }

        }
        return flag;
    }

    /**
     * 判断订单类型
     *
     * @param stCExpress 物流实体
     * @param orderInfo  订单实体
     * @return boolean
     */
    public boolean checkBillType(StCExpressDO stCExpress, OcBOrderRelation orderInfo) {
        boolean flag = true;
        //付款方式（订单表）
        int payType = orderInfo.getOrderInfo().getPayType();
        Integer billType = stCExpress.getBillType();
        //预售状态（双11预售状态）
        Integer double11PresaleStatus =
                Optional.ofNullable(orderInfo.getOrderInfo().getDouble11PresaleStatus()).orElse(0);
        //单据类型为：【普通订单】,订单的“付款方式”非货到付款,且“预售状态”0
        if (OmsSTBillType.NORMAL_ORDER.toInteger() == billType) {
            if (!((OmsPayType.CASH_ON_DELIVERY.toInteger() != payType) && double11PresaleStatus == OcOrderDoublellPresaleStatus.FEI_PRESALL.toInteger())) {
                flag = false;
            }
            //单据类型为：【预售订单】：判断“预售状态”非0
        } else if (OmsSTBillType.PRESALE_ORDER.toInteger() == billType) {
            if (double11PresaleStatus == OcOrderDoublellPresaleStatus.FEI_PRESALL.toInteger()) {
                flag = false;
            }
            //单据类型为：【货到付款订单】
        } else if (OmsSTBillType.CASH_ON_DELIVERY_ORDER.toInteger() == billType) {
            if (OmsPayType.ON_LINE_PAY.toInteger() == payType) {
                flag = false;
            }
        }
        return flag;
    }

    /**
     * 判断物流公司
     *
     * @param stCExpress 物流实体
     * @param orderInfo  订单实体
     * @return boolean
     */
    public boolean checkLogisticsId(StCExpressDO stCExpress, OcBOrderRelation orderInfo) {
        boolean flag = false;
        String cpCLogisticsIdStr = "";
        if (stCExpress.getCpCLogisticsIdSet() != null) {
            cpCLogisticsIdStr = stCExpress.getCpCLogisticsIdSet();
        }
        //发货仓库
        Long cpCPhyWarehouseId = orderInfo.getOrderInfo().getCpCPhyWarehouseId();
        //订单商品数量
        BigDecimal qtyAll = orderInfo.getOrderInfo().getQtyAll();
        //根据仓库id
        List<StCExpressAllocationItemDO> stCExpressAllocationItemList =
                omsDistributeLogisticStRpcService.selectLogisticsIdBycpCPhyWarehouseId(cpCPhyWarehouseId, qtyAll,
                        orderInfo.getOrderInfo().getId());
        if (stCExpressAllocationItemList.size() > 0) {
            for (StCExpressAllocationItemDO stCExpressAllocationItem : stCExpressAllocationItemList) {
                if (cpCLogisticsIdStr.contains(String.valueOf(stCExpressAllocationItem.getCpCLogisticsId()))) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }

    /**
     * 判断发货仓库
     *
     * @param stCExpress 物流实体
     * @param orderInfo  订单实体
     * @return boolean值
     */
    public boolean checkPhyWarehouseId(StCExpressDO stCExpress, OcBOrderRelation orderInfo) {
        boolean flag = false;
        //发货仓库（订单信息）
        Long orderCpCPhyWarehouseId = orderInfo.getOrderInfo().getCpCPhyWarehouseId();
        //发货仓库（物流方案中）,根据物流方案主表id,查询物流方案仓库明细表中的发货仓库数据
        List<StCExpressWarehouseItemDO> stCExpressWarehouseItemList =
                omsDistributeLogisticStRpcService.selectStCExpressWarehouseItemInfo(stCExpress.getId(),
                        orderInfo.getOrderInfo().getId());
        if (stCExpressWarehouseItemList.size() == 0) {
            flag = true;
        } else {
            for (StCExpressWarehouseItemDO stCExpressWarehouseItem : stCExpressWarehouseItemList) {
                if (stCExpressWarehouseItem.getCpCPhyWarehouseId().equals(orderCpCPhyWarehouseId)) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }

    /**
     * 判断包裹重量
     *
     * @param stCExpressPackage 物流包裹实体
     * @param orderInfo         订单实体
     * @return boolean值
     */
    public boolean checkPackageWeight(StCExpressPackageDO stCExpressPackage, OcBOrderRelation orderInfo) {
        boolean flag = false;
        //如果判断条件为“小于”
        if (OmsJudgingCondition.LESS_THAN.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制商品重量,小于方案中设置的限制“包裹重量”的值
            if (orderInfo.getOrderInfo().getWeight().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) < 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.LESS_THAN_OR_EQUAL_TO.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制商品重量,小于等于方案中设置的限制“包裹重量”的值
            if (orderInfo.getOrderInfo().getWeight().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) <= 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.EQUAL_TO.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制商品重量,等于方案中设置的限制“包裹重量”的值
            if (orderInfo.getOrderInfo().getWeight().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) <= 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.GREAT_THAN.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果商品重量,大于方案中设置的限制“包裹重量”的值
            if (orderInfo.getOrderInfo().getWeight().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) > 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.GREAT_THAN_OR_EQUAL_TO.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制商品重量,大于等于方案中设置的限制“包裹重量”的值
            if (orderInfo.getOrderInfo().getWeight().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) >= 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.BETWEEN.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制商品重量,介于方案中设置的限制“包裹重量”的值
            if (orderInfo.getOrderInfo().getWeight().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) > 0 && orderInfo.getOrderInfo().getWeight().compareTo(new BigDecimal(stCExpressPackage.getEndVal())) < 0) {
                flag = true;
            }
        } else {
            flag = false;
        }
        return flag;
    }

    /**
     * 判断订单金额
     *
     * @param stCExpressPackage 物流包裹实体
     * @param orderInfo         订单实体
     * @return boolean值
     */
    public boolean checkAmount(StCExpressPackageDO stCExpressPackage, OcBOrderRelation orderInfo) {
        boolean flag = false;
        //如果判断条件为“小于”
        if (OmsJudgingCondition.LESS_THAN.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制订单总额,小于方案中设置的限制“订单金额”的值
            if (orderInfo.getOrderInfo().getOrderAmt().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) < 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.LESS_THAN_OR_EQUAL_TO.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制订单总额,小于等于方案中设置的限制“订单金额”的值
            if (orderInfo.getOrderInfo().getOrderAmt().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) <= 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.EQUAL_TO.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制订单总额,等于方案中设置的限制“订单金额”的值
            if (orderInfo.getOrderInfo().getOrderAmt().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) <= 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.GREAT_THAN.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制订单总额,大于方案中设置的限制“订单金额”的值
            if (orderInfo.getOrderInfo().getOrderAmt().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) > 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.GREAT_THAN_OR_EQUAL_TO.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制订单总额,大于等于方案中设置的限制“订单金额”的值
            if (orderInfo.getOrderInfo().getOrderAmt().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) >= 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.BETWEEN.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制订单总额,介于方案中设置的限制“订单金额”的值
            if (orderInfo.getOrderInfo().getOrderAmt().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) > 0 && orderInfo.getOrderInfo().getWeight().compareTo(new BigDecimal(stCExpressPackage.getEndVal())) < 0) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 判断包裹数量
     *
     * @param stCExpressPackage 物流包裹实体
     * @param orderInfo         订单实体
     * @return boolean值
     */
    public boolean checkQuantity(StCExpressPackageDO stCExpressPackage, OcBOrderRelation orderInfo) {
        boolean flag = false;
        //如果判断条件为“小于”
        if (OmsJudgingCondition.LESS_THAN.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制商品数量,小于方案中设置的限制“包裹数量”的值
            if (orderInfo.getOrderInfo().getQtyAll().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) < 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.LESS_THAN_OR_EQUAL_TO.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制商品数量,小于等于方案中设置的限制“包裹数量”的值
            if (orderInfo.getOrderInfo().getQtyAll().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) <= 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.EQUAL_TO.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制商品数量,等于方案中设置的限制“包裹数量”的值
            if (orderInfo.getOrderInfo().getQtyAll().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) == 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.GREAT_THAN.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制商品数量,大于方案中设置的限制“包裹数量”的值
            if (orderInfo.getOrderInfo().getQtyAll().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) > 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.GREAT_THAN_OR_EQUAL_TO.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制商品数量,大于等于方案中设置的限制“包裹数量”的值
            if (orderInfo.getOrderInfo().getQtyAll().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) >= 0) {
                flag = true;
            }
        } else if (OmsJudgingCondition.BETWEEN.paseValue().equals(stCExpressPackage.getConditions())) {
            //如果限制商品数量,介于方案中设置的限制“包裹数量”的值
            if (orderInfo.getOrderInfo().getQtyAll().compareTo(new BigDecimal(stCExpressPackage.getBeginVal())) > 0 && orderInfo.getOrderInfo().getWeight().compareTo(new BigDecimal(stCExpressPackage.getEndVal())) < 0) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 判断指定商品
     *
     * @param orderInfo             订单关系实体
     * @param stCExpressProItemList 物流指定商品明细
     * @return boolean值
     */
    public boolean checkProSku(OcBOrderRelation orderInfo, List<StCExpressProItemDO> stCExpressProItemList) {
        boolean flag = true;
        List skuList = new ArrayList();
        for (StCExpressProItemDO stCExpressProItemDO : stCExpressProItemList) {
            skuList.add(stCExpressProItemDO.getPsCSkuId());
        }
        List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemListAndReturn(orderInfo.getOrderInfo().getId());
        ///先取出订单中的所有商品
        for (int a = 0; a < orderItems.size(); a++) {
            if (!skuList.contains(orderItems.get(a).getPsCSkuId())) {
                flag = false;
            }
        }
        return flag;
    }

    /**
     * 判断指定地址
     *
     * @param stCExpress                 物流实体
     * @param orderInfo                  订单关系实体
     * @param stCExpressPlanAreaItemList 物流方案指定区域
     * @return boolean值
     */
    public boolean checkArea(StCExpressDO stCExpress, OcBOrderRelation orderInfo,
                             List<StCExpressPlanAreaItemDO> stCExpressPlanAreaItemList) {
        boolean flag = false;
        for (StCExpressPlanAreaItemDO stCExpressPlanAreaItem : stCExpressPlanAreaItemList) {
            //判定指定区域类型（物流方案）0:包含 ；1:排除
            Long province = stCExpressPlanAreaItem.getCpCRegionProvinceId();
            Long city = stCExpressPlanAreaItem.getCpCRegionCityId();
            Long area = stCExpressPlanAreaItem.getCpCRegionAreaId();
            Integer areaType = stCExpress.getAreaType();
            // 2020-07-28增加非空判断
            if (areaType != null && OmsSTAreaType.CONTAINS.toInteger() == areaType) {
                if (area != null) {
                    if (area.equals(orderInfo.getOrderInfo().getCpCRegionAreaId()) && orderInfo.getOrderInfo().getCpCRegionCityId().equals(city)) {
                        flag = true;
                    }
                } else {
                    if (city != null) {
                        if (city.equals(orderInfo.getOrderInfo().getCpCRegionCityId())) {
                            flag = true;
                        }
                    } else {
                        if (orderInfo.getOrderInfo().getCpCRegionProvinceId().equals(province)) {
                            flag = true;
                        }
                    }
                }
            } else if (areaType != null && OmsSTAreaType.UN_CONTAINS.toInteger() == areaType) {
                if (city != null) {
                    if (!orderInfo.getOrderInfo().getCpCRegionCityId().equals(city)) {
                        flag = true;
                    }
                } else {
                    if (!orderInfo.getOrderInfo().getCpCRegionProvinceId().equals(province)) {
                        flag = true;
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 根据物流区域设置中的排除区域,排除物流公司,得到所有有效物流公司
     *
     * @param stCExpressAreaInfoList
     * @param orderInfo              订单关系实体
     * @return boolean值
     */
    public List<Long> checkExcludedAreaGetLogistics(List<ExpressAreaRequest> stCExpressAreaInfoList,
                                                    OcBOrderRelation orderInfo) {
        List<Long> logisticsIdList = new ArrayList<>();
        for (ExpressAreaRequest expressAreaRequest : stCExpressAreaInfoList) {
            boolean containsExclusionAreaFlag = false;
            Long logisticsId = expressAreaRequest.getStCExpressArea().getCpCLogisticsId();
            List<StCExpressAreaItemDO> stCExpressAreaItemDOList = expressAreaRequest.getStCExpressAreaItemList();
            StCExpressAreaItemDO stCExpressAreaItemDO = stCExpressAreaItemDOList.get(0);
            if (stCExpressAreaItemDO != null) {
                String exclusionAreaStr = stCExpressAreaItemDO.getExclusionArea();
                if (StringUtils.isNotEmpty(exclusionAreaStr)) {
                    String[] exclusionArea = exclusionAreaStr.split("\\|");
                    for (String area : exclusionArea) {
                        String orderAddress = orderInfo.getOrderInfo().getReceiverAddress();
                        if (orderAddress.contains(area)) {
                            containsExclusionAreaFlag = true;
                            break;
                        }
                    }
                    if (!containsExclusionAreaFlag) {
                        logisticsIdList.add(logisticsId);
                    }
                } else {
                    logisticsIdList.add(logisticsId);
                }
            }
        }
        return logisticsIdList;
    }

    /**
     * 取出AD_PARAM表中的快递否定值所有的值
     *
     * @return
     */
    public String getNegativeValueStr() {
        String expressNegativeName = OmsSysParam.EXPRESS_NEGATIVE_VALUE.parseValue();
        String negativeValueStr = "";
        negativeValueStr = AdParamUtil.getParam(expressNegativeName);
        return negativeValueStr;
    }

    /**
     * 取出AD_PARAM表中的优先级的所有的值
     *
     * @return
     */
    public String getPriorityValueStr() {
        //查询买家,买家优先级
        String remarkPriorityName = OmsSysParam.REMARK_PRIORITY_NAME.parseValue();
        String priorityValueStr = "";
        priorityValueStr = AdParamUtil.getParam(remarkPriorityName);
        return priorityValueStr;
    }

    /**
     * 取出AD_PARAM表中的不发京东快递的物流公司的所有的值
     *
     * @return
     */
    public String getJdNoSendLogisticsStr() {
        String jdNoSendLogistics = OmsSysParam.JD_NO_SEND_LOGITSICS.parseValue();
        String jdNoSendLogisticsStr = "";
        jdNoSendLogisticsStr = AdParamUtil.getParam(jdNoSendLogistics);
        return jdNoSendLogisticsStr;
    }

    /**
     * 取出AD_PARAM表中的不发京东快递的物流公司的所有的值
     *
     * @return
     */
    public String getTbNoSendLogisticsStr() {
        String tbNoSendLogistics = OmsSysParam.TB_NO_SEND_LOGITSICS.parseValue();
        String tbNoSendLogisticsStr = "";
        tbNoSendLogisticsStr = AdParamUtil.getParam(tbNoSendLogistics);
        return tbNoSendLogisticsStr;
    }

    /**
     * 如果是京东平台订单,将基础数据过滤掉,京东店铺不发的快递公司
     * 如果是淘宝、淘宝分销、淘宝经销平台订单,将基础数据过滤掉,淘宝店铺不发的快递公司
     *
     * @param dontSendLogisticIdValue
     * @param baseIntersectionList
     * @return
     */
    public List<Long> removeNoSendLogistics(String dontSendLogisticIdValue, List<Long> baseIntersectionList,
                                            OcBOrderRelation orderInfo, String type) {
        if (StringUtils.isNotEmpty(dontSendLogisticIdValue) && dontSendLogisticIdValue.contains(",")) {
            String[] notSendLogisticArray = dontSendLogisticIdValue.split(",");
            for (String logistId : notSendLogisticArray) {
                baseIntersectionList.remove(Long.valueOf(logistId));
            }
        } else if (StringUtils.isNotEmpty(dontSendLogisticIdValue) && !dontSendLogisticIdValue.contains(",")) {
            baseIntersectionList.remove(Long.valueOf(dontSendLogisticIdValue));
        }
        return baseIntersectionList;
    }

    /**
     * 20190702,新增判断,如果平台为唯品会则不会走分配物流服务逻辑,直接返回原订单物流公司id
     *
     * @param orderInfo
     * @return boolean值
     */
    public boolean checkIsJitxOrder(OcBOrderRelation orderInfo) {
        boolean flag = PlatFormEnum.VIP_JITX.getCode().equals(orderInfo.getOrderInfo().getPlatform());
        return flag;
    }

    /**
     * 判断是否为京东货到付款订单
     *
     * @param orderInfo
     * @return
     */
    public boolean checkIsJdCashDeliveryOrder(OcBOrderRelation orderInfo) {
        boolean flag = OmsPayType.CASH_ON_DELIVERY.toInteger() == orderInfo.getOrderInfo().getPayType() && PlatFormEnum.JINGDONG.getCode().equals(orderInfo.getOrderInfo().getPlatform());
        return flag;
    }

    /**
     * 判断省份id或者市id是否为空
     *
     * @param orderInfo
     * @return flag
     */
    public boolean checkIsNullProvinceIdOrCityId(OcBOrderRelation orderInfo, User user) {
        boolean flag = false;
        //订单信息中,省份id或者市id为空时,订单为异常订单,需要人工手动处理
        if (orderInfo.getOrderInfo().getCpCRegionCityId() == null || orderInfo.getOrderInfo().getCpCRegionProvinceId() == null) {
            flag = true;
            String cityName = "";
            if (StringUtils.isNotEmpty(orderInfo.getOrderInfo().getCpCRegionCityEname())) {
                cityName = orderInfo.getOrderInfo().getCpCRegionCityEname();
            }
            String provinceName = "";
            if (StringUtils.isNotEmpty(orderInfo.getOrderInfo().getCpCRegionProvinceEname())) {
                provinceName = orderInfo.getOrderInfo().getCpCRegionProvinceEname();
            }
            String msg = "请检查订单的" + provinceName + "省或" + cityName + "市是否为空!";
            omsOrderLogService.addUserOrderLog(orderInfo.getOrderInfo().getId(), orderInfo.getOrderInfo().getBillNo()
                    , OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), msg, "", msg, user);
        }
        return flag;
    }

    /**
     * 检查订单状态和wms撤回状态是否正常
     *
     * @param orderInfo
     * @param user
     * @return
     */
    public boolean checkOrderStatus(OcBOrderRelation orderInfo, User user) {
        boolean flag = true;
        //wms撤回状态
        Integer wmsCancelStatus = orderInfo.getOrderInfo().getWmsCancelStatus();
        //订单状态
        Integer orderStatus = orderInfo.getOrderInfo().getOrderStatus();
        if (!(OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus) || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus) || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus) || (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus) && OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == wmsCancelStatus))) {
            flag = false;
            String msg = Resources.getMessage("状态非待分配、待确认、缺货、配货中,不允许分配物流公司！");
            omsOrderLogService.addUserOrderLog(orderInfo.getOrderInfo().getId(), orderInfo.getOrderInfo().getBillNo()
                    , OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), msg, "", msg, user);
        }
        return flag;
    }

    /**
     * 根据订单中的,“省”,“市”,“区”查询物流区域设置表中可用的物流公司列表
     *
     * @param orderInfo
     * @return
     */
    public List<Long> filterExpressArea(OcBOrderRelation orderInfo) {
        //取出订单中的省,市,区的值,根据物流区域设置过滤
        Long provinceId = orderInfo.getOrderInfo().getCpCRegionProvinceId();
        Long cityId = orderInfo.getOrderInfo().getCpCRegionCityId();
        Long areaId = orderInfo.getOrderInfo().getCpCRegionAreaId();
        //根据订单中的,“省”,“市”,“区”查询物流区域设置表中可用的物流公司列表
        List<Long> stCExpressAreaList = new ArrayList<>();
        List<ExpressAreaRequest> stCExpressAreaInfoList =
                omsDistributeLogisticStRpcService.selectLogisticsIdInfoByProvinceCityArea(provinceId, cityId, areaId,
                        orderInfo.getOrderInfo().getId());
        stCExpressAreaList = checkExcludedAreaGetLogistics(stCExpressAreaInfoList, orderInfo);
        return stCExpressAreaList;
    }

    /**
     * 根据发货仓库id 和订单商品数量获取物流分配规则明细中的物流公司
     *
     * @param orderInfo
     * @return
     */
    public List<StCExpressAllocationItemDO> getLogisticsFromWarehouseRule(OcBOrderRelation orderInfo) {
        //发货实体仓id
        Long cpCPhyWarehouseId = orderInfo.getOrderInfo().getCpCPhyWarehouseId();
        //订单商品数量
        BigDecimal qtyAll = orderInfo.getOrderInfo().getQtyAll();
        //根据发货仓库id 和订单商品数量获取物流分配规则明细中的物流公司
        List<StCExpressAllocationItemDO> stCExpressAllocationItemList =
                omsDistributeLogisticStRpcService.selectLogisticsIdBycpCPhyWarehouseId(cpCPhyWarehouseId, qtyAll,
                        orderInfo.getOrderInfo().getId());
        return stCExpressAllocationItemList;
    }

    /**
     * 【物流区域设置】和【仓库物流规则】下的物流公司取交集
     *
     * @param orderInfo
     * @param stCExpressAllocationItemList
     * @param stCExpressAreaList
     * @return List<Long> 交集物流公司
     */
    public List<Long> getExpressAreaAndWareHouseRuleIntersection(OcBOrderRelation orderInfo,
                                                                 List<StCExpressAllocationItemDO> stCExpressAllocationItemList, List<Long> stCExpressAreaList) {
        //交集后最终所有有效的物流公司的集合
        List<Long> intersectionList = new ArrayList<>();
        if (stCExpressAllocationItemList.size() > 0) {
            for (StCExpressAllocationItemDO stCExpressAllocationItem : stCExpressAllocationItemList) {
                if (stCExpressAreaList.contains(stCExpressAllocationItem.getCpCLogisticsId())) {
                    //如果在可送达的物流公司列表里包含此物流公司,则认为此物流公司是有效的物流公司
                    intersectionList.add(stCExpressAllocationItem.getCpCLogisticsId());
                }
            }
        }
        return intersectionList;
    }

    /**
     * 根据各种校验条件,获取所有有效的物流方案
     *
     * @param stCExpressList
     * @param orderInfo
     * @return
     */
    public List<StCExpressDO> filterLogisticsByExpress(List<StCExpressDO> stCExpressList, OcBOrderRelation orderInfo) {
        Long retLogisticsId = 0L;
        //初始化一个空的有效物流方案list
        List<StCExpressDO> isActivestCExpressList = new ArrayList<>();
        //遍历stCExpressList,判断方案是否有效
        if (stCExpressList.size() > 0) {
            for (StCExpressDO stCExpress : stCExpressList) {
                Long stCExpressId = stCExpress.getId();
                String stCExpressName = stCExpress.getEname();
                /**step14:判断店铺(物流方案)*/
                boolean expressCShopFlag = checkExpressCShop(stCExpress, orderInfo);
                if (!expressCShopFlag) {
                    continue;
                }
                /**step15:判断日期类型(物流方案)*/
                boolean dateTypeFlag = checkDateType(stCExpress, orderInfo);
                if (!dateTypeFlag) {
                    continue;
                }
                /**step16:判断订单类型(物流方案)*/
                boolean billTypeFlag = checkBillType(stCExpress, orderInfo);
                if (!billTypeFlag) {
                    continue;
                }
                /**step17:判断物流公司(物流方案)*/
                boolean logisticsIdFlag = checkLogisticsId(stCExpress, orderInfo);
                if (!logisticsIdFlag) {
                    continue;
                }
                /**step18:判断发货仓库(物流方案)*/
                boolean phyWarehouseIdFlag = true;
                //如果物流方案中没有设置发货仓库则认为全部仓库,是true
                phyWarehouseIdFlag = checkPhyWarehouseId(stCExpress, orderInfo);
                if (!phyWarehouseIdFlag) {
                    continue;
                }
                //判断是否有维护规则(物流方案);包裹重量”“订单金额”“包裹数量”“指定商品”“指定区域”
                //根据物流方案id,查询物流方案商品明细
                List<StCExpressProItemDO> stCExpressProItemList =
                        omsDistributeLogisticStRpcService.selectStCExpressProItem(stCExpressId,
                                orderInfo.getOrderInfo().getId());
                //根据物流方案id,查询物流方案区域明细
                List<StCExpressPlanAreaItemDO> stCExpressPlanAreaItemList =
                        omsDistributeLogisticStRpcService.selectStCExpressPlanAreaItem(stCExpressId,
                                orderInfo.getOrderInfo().getId());
                //根据物流方案id,查询物流方案包裹明细
                List<StCExpressPackageDO> stCExpressPackageList =
                        omsDistributeLogisticStRpcService.selectStCExpressPackageInfo(stCExpressId,
                                orderInfo.getOrderInfo().getId());
                if (stCExpressProItemList.size() == 0 && stCExpressPlanAreaItemList.size() == 0 && stCExpressPackageList.size() == 0) {
                    //若都未维护,则查看是否有其它方案存在,将当前方案放进有效方案列表中
                    isActivestCExpressList.add(stCExpress);
                    continue;
                } else {
                    boolean packageWeightFlag = true;
                    boolean packageAmtFlag = true;
                    boolean packageQuantityFlag = true;
                    //当物流方案“包裹属性”包裹明细有维护时
                    if (stCExpressPackageList.size() > 0) {
                        for (StCExpressPackageDO stCExpressPackage : stCExpressPackageList) {
                            /**step19:包裹属性为“包裹重量”,判断“包裹重量”*/
                            if (OmsPackageType.WEIGHT.paseValue().equals(stCExpressPackage.getPkgAttribute())) {
                                packageWeightFlag = checkPackageWeight(stCExpressPackage, orderInfo);
                                if (!packageWeightFlag) {
                                    break;
                                }
                                /**step20:包裹属性为“订单金额”,判断“订单金额”*/
                            } else if (OmsPackageType.AMOUNT.paseValue().equals(stCExpressPackage.getPkgAttribute())) {
                                packageAmtFlag = checkAmount(stCExpressPackage, orderInfo);
                                if (!packageAmtFlag) {
                                    break;
                                }
                                /**step21:包裹属性为“商品数量”,判断“商品数量”*/
                            } else if (OmsPackageType.QUANTITY.paseValue().equals(stCExpressPackage.getPkgAttribute())) {
                                packageQuantityFlag = checkQuantity(stCExpressPackage, orderInfo);
                                if (!packageQuantityFlag) {
                                    break;
                                }
                            }
                        }
                    }
                    boolean proSkuFlag = true;
                    if (stCExpressProItemList.size() > 0) {
                        /**step22:当物流方案“指定商品”商品明细有维护时,判断“指定商品”*/
                        proSkuFlag = checkProSku(orderInfo, stCExpressProItemList);
                    }
                    /**step23:当物流方案“指定地址”区域明细有维护时,判断“指定地址”*/
                    boolean areaFlag = true;
                    if (stCExpressPlanAreaItemList.size() > 0) {
                        areaFlag = checkArea(stCExpress, orderInfo, stCExpressPlanAreaItemList);
                    }
                    if (packageWeightFlag && packageAmtFlag && packageQuantityFlag && proSkuFlag && areaFlag) {
                        isActivestCExpressList.add(stCExpress);
                    }
                }
            }
        }
        return isActivestCExpressList;
    }

    /**
     * 根据订单的仓库id和订单的省市,查询物流公司优先级,并且根据优先级的值进行升序
     *
     * @param orderInfo
     * @return
     */
    public List<Long> getLogisticAndRankSortList(OcBOrderRelation orderInfo) {
        List<Long> newLogisticAndRankList = new ArrayList<>();
        WarehouseLogisticsRankRequest warehouseLogisticsRankRequest = new WarehouseLogisticsRankRequest();
        warehouseLogisticsRankRequest.setCpCPhyWarehouseId(orderInfo.getOrderInfo().getCpCPhyWarehouseId());
        warehouseLogisticsRankRequest.setCpCRegionProvinceId(orderInfo.getOrderInfo().getCpCRegionProvinceId());
        warehouseLogisticsRankRequest.setCpCRegionCityId(orderInfo.getOrderInfo().getCpCRegionCityId());
        ValueHolderV14<StCWarehouseLogisticsRankDO> vh =
                omsDistributeLogisticStRpcService.queryLogisticsRankInfo(orderInfo.getOrderInfo(),
                        orderInfo.getOrderInfo().getCpCPhyWarehouseId(), warehouseLogisticsRankRequest);
        if (vh.isOK()) {
            StCWarehouseLogisticsRankDO logisticsRankInfo = vh.getData();
            if (logisticsRankInfo != null) {
                String logisticsRankStr = logisticsRankInfo.getLogisticsRank();
                if (StringUtils.isNotEmpty(logisticsRankStr) && !logisticsRankStr.equals("[]")) {
                    JSONArray logisticsRank = JSON.parseArray(logisticsRankStr);
                    newLogisticAndRankList = getSortAscMethodByRankList(logisticsRank);
                }
            }
        }
        return newLogisticAndRankList;
    }

    /**
     * 根据物流公司优先级JSONArray对象,获取根据优先级值列表升序后的物流公司列表
     *
     * @param logisticsRank
     * @return
     */
    public List<Long> getSortAscMethodByRankList(JSONArray logisticsRank) {
        Map<Long, Integer> logisticAndRankMap = new HashMap<>();
        List<Long> newLogisticAndRankList = new ArrayList<>();
        for (Object object : logisticsRank) {
            JSONObject jsonObject = JSON.parseObject(object.toString());
            Long logisticsId = jsonObject.getLong("logisticsId");
            Integer rank = jsonObject.getInteger("rank");
            logisticAndRankMap.put(logisticsId, rank);
        }
        Map<Long, Integer> sortResult = new LinkedHashMap<Long, Integer>();
        sortResult = sortAscMethodByRank(logisticAndRankMap);
        //map根据Value的排序方法====结束
        for (Long phyWareHouseId : sortResult.keySet()) {
            newLogisticAndRankList.add(phyWareHouseId);
        }
        return newLogisticAndRankList;
    }

    /**
     * 根据物流公司优先级的值排序【升序】
     *
     * @param logisticAndRankMap
     * @return
     */
    public Map<Long, Integer> sortAscMethodByRank(Map<Long, Integer> logisticAndRankMap) {
        //map根据Value的排序方法[升序]====开始
        List<Map.Entry<Long, Integer>> list = new LinkedList<Map.Entry<Long, Integer>>(logisticAndRankMap.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<Long, Integer>>() {
            @Override
            public int compare(Map.Entry<Long, Integer> o1, Map.Entry<Long, Integer> o2) {
                return (o1.getValue()).compareTo(o2.getValue());
            }
        });
        Map<Long, Integer> sortResult = new LinkedHashMap<Long, Integer>();
        for (Map.Entry<Long, Integer> entry : list) {
            sortResult.put(entry.getKey(), entry.getValue());
        }
        return sortResult;
    }

    /**
     * 根据下单时间和当前时间查找物流方案
     *
     * @param orderInfo
     * @return
     */
    public List<StCExpressDO> getExpressListByOrderDateAndCurrentDate(OcBOrderRelation orderInfo) {
        //下单时间
        Date orderDate = orderInfo.getOrderInfo().getOrderDate();
        //当前时间
        Date currentDate = new Date();
        List<StCExpressDO> stCExpressList = omsDistributeLogisticStRpcService.selectStCExpress(orderDate, currentDate
                , orderInfo.getOrderInfo().getId());
        return stCExpressList;
    }

    /**
     * 根据买买鸡备注优先级,获取相关可用的物流公司
     *
     * @param orderInfo
     * @return
     */
    public List<String> getLogisticsByBuyerOrSalerRemark(OcBOrderRelation orderInfo) {
        List<String> retlogisticsList = new ArrayList();
        try {
            //EXPRESS_NEGATIVE_VALUE 快递否定值 expressNegativeValue
            //在AD_PARAM表中先取出快递否定值所有的值
            String negativeValueStr = getNegativeValueStr();
            String[] expressNegativeValue = negativeValueStr.split(",");
            //查询买家,买家优先级
            String priorityValueStr = getPriorityValueStr();
            String buyerRemark = "";
            if (orderInfo.getOrderInfo().getBuyerMessage() != null) {
                buyerRemark = orderInfo.getOrderInfo().getBuyerMessage();
            }
            String sellerRemark = "";
            if (orderInfo.getOrderInfo().getSellerMemo() != null) {
                sellerRemark = orderInfo.getOrderInfo().getSellerMemo();
            }
            //查询所有物流公司列表
            List<String> logitsicsKeyList = cpRpcService.selectLogisticsKeyList();
            //取出卖家或者卖家备注中的物流公司
            List<String> logisticsList = filterLogitsicsKeyWord(logitsicsKeyList, priorityValueStr, buyerRemark,
                    sellerRemark);
            boolean containsNegativeValueFlag = false;
            if (!StringUtils.isEmpty(negativeValueStr)) {
                //判断买卖家备注是否包含否定值
                containsNegativeValueFlag = checkNegativeValueFlag(expressNegativeValue, priorityValueStr,
                        buyerRemark, sellerRemark);
            }
            //买家或者卖家备注中含有否定值
            if (containsNegativeValueFlag) {
                logitsicsKeyList.removeAll(logisticsList);
                retlogisticsList = logitsicsKeyList;
            } else { //卖家或者卖家备注中,不含有否定值;走无值逻辑
                retlogisticsList = logisticsList;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("根据买卖家备注获取物流公司,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
        return retlogisticsList;
    }

    /**
     * 将基础数据的结果集和买卖家备注物流公司再取交集
     *
     * @param logisticNameList
     * @param orderInfo
     * @param baseIntersectionList
     * @return
     */
    public List<Long> getBaseAndRemarkIntersectionList(List<String> logisticNameList, OcBOrderRelation orderInfo,
                                                       List<Long> baseIntersectionList) {
        /**将基础数据的结果集和买卖家备注物流公司再取交集*/
        List<Long> baseAndRemarkIntersectionList = new ArrayList<>();
        if (logisticNameList != null && logisticNameList.size() > 0) {
            for (String loisticName : logisticNameList) {
                LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByshortName(loisticName);
                if (baseIntersectionList.contains(logisticsInfo.getId())) {
                    baseAndRemarkIntersectionList.add(logisticsInfo.getId());
                }
            }
        } else {
            baseAndRemarkIntersectionList = baseIntersectionList;
        }
        return baseAndRemarkIntersectionList;
    }

    /**
     * 最终交集,获取物流公司id
     *
     * @param IntersectionList
     * @param newLogisticAndRankList
     * @param ocBOrder
     * @return
     */
    public Long endIntersectionLogisticId(List<Long> IntersectionList, List<Long> newLogisticAndRankList,
                                          OcBOrder ocBOrder, User user, String LogTag) {
        Long retLogistId = 0L;
        for (Long logisticId : newLogisticAndRankList) {
            if (IntersectionList.contains(logisticId)) {
                retLogistId = logisticId;
                break;
            }
        }
        if (retLogistId.equals(0L)) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), ocBOrder.getId() + "分配物流服务" + LogTag + "无交集",
                    null, LogTag + "无交集", user);
        }
        return retLogistId;
    }

    //分物流
    public void distributeLogisticsDistributeLogistics(OcBOrder ocBOrderDto) {
        try {
            //TO B 不需要自动寻物流
            if (!omsBusinessTypeStService.isAutoOccupy(ocBOrderDto)) {
                return;
            }
            if (log.isDebugEnabled()) {
                log.debug("OrderId{}.distributeLogisticsDistributeLogistics占单分物流开始,物流id={},平台={},店铺为:{}",
                        ocBOrderDto.getId(), ocBOrderDto.getCpCPhyWarehouseId(), ocBOrderDto.getPlatform(),
                        ocBOrderDto.getCpCShopId());
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
            ocBOrderRelation.setOrderInfo(ocBOrderDto);
            /**step1:判断是否为唯品会订单,如果平台为唯品会则不会走分配物流服务逻辑,直接返回原订单物流公司id*/
            if (checkIsJitxOrder(ocBOrderRelation)) {
                log.info("订单id：{}为唯品会订单,如果平台为唯品会则不会走分配物流服务逻辑,直接返回原订单物流公司", ocBOrderRelation.getOrderId());
                return;
            }
            List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItemListAndReturn(ocBOrderDto.getId());
            ocBOrderRelation.setOrderItemList(ocBOrderItems);

            CpCLogistics cpCLogistics = distributeLogistics(ocBOrderRelation);
            OcBOrder order = new OcBOrder();
            order.setId(ocBOrderDto.getId());
            if (cpCLogistics == null) {
                String errorMessage = "订单OrderId" + ocBOrderDto.getId() + "的订单调用分物流服务未匹配到有效物流公司, 操作时间" + df.format(new Date());
                log.info(errorMessage);
                ocBOrderDto.setSysremark(SplitMessageUtil.splitMesssage(errorMessage));
                //  清空物流信息 需要清空物流信息
                order.setCpCLogisticsId(-1L);
                order.setCpCLogisticsEname("");
                order.setCpCLogisticsEcode("");
                //是否寻源失败
                order.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IY);
                omsOrderService.updateOrderInfo(order);

                //  插入日志
                orderLogService.addUserOrderLog(order.getId(), ocBOrderDto.getBillNo(),
                        OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), errorMessage, null, null,
                        SystemUserResource.getRootUser());
            } else {
                String message = "订单OrderId" + ocBOrderDto.getId() + "的订单调用分物流服务,返回物流公司Id[" + cpCLogistics.getId() +
                        "][" + cpCLogistics.getEname() + "],操作时间" + df.format(new Date());
                if (log.isDebugEnabled()) {
                    log.debug("OrderId{}.TobeConfirmed.Step130CallDistributeLogisticsService,物流公司Id#{},物流名称#{}",
                            ocBOrderDto.getId(), cpCLogistics.getId(), cpCLogistics.getEname());
                }
                order.setCpCLogisticsId(cpCLogistics.getId());
                order.setCpCLogisticsEname(cpCLogistics.getEname());
                order.setCpCLogisticsEcode(cpCLogistics.getEcode());
                //寻源成功
                order.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IN);
                //去除订单异常标签
                order.setIsException(OcBOrderConst.IS_STATUS_IN);
                omsOrderService.updateOrderInfo(order);
                // 插入日志
                orderLogService.addUserOrderLog(order.getId(), ocBOrderDto.getBillNo(),
                        OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), message, null, null,
                        SystemUserResource.getRootUser());
            }
        } catch (Exception e) {
            e.printStackTrace();
            OcBOrder erroOcBOrder = new OcBOrder();
            erroOcBOrder.setId(ocBOrderDto.getId());
            //是否寻源失败
            erroOcBOrder.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IY);
            omsOrderService.updateOrderInfo(erroOcBOrder);
            log.info(LogUtil.format("分物流异常异常信息为:{}", "distributeLogisticsDistributeLogistics", "分物流异常", ocBOrderDto.getId()), Throwables.getStackTraceAsString(e));
            orderLogService.addUserOrderLog(ocBOrderDto.getId(), ocBOrderDto.getBillNo(),
                    OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), "分物流失败" + e.getMessage(), null, null,
                    SystemUserResource.getRootUser());
        }
    }


    private boolean beforeValidate(OcBOrderRelation relation, User user) {
        if (relation == null || relation.getOrderInfo() == null) {
            return false;
        }
        OcBOrder order = relation.getOrderInfo();

        List<OcBOrderItem> items = relation.getOrderItemList();

        if (items == null) {
            //查询原单明细
            items = orderItemMapper.selectOrderItemListOccupy(order.getId());
        }
        if (CollectionUtils.isEmpty(items)) {
            this.recordLog(order.getId(), order.getBillNo(), "订单明细均已退款,无需分物流!", user);
            return false;
        }

        Long warehouseId = order.getCpCPhyWarehouseId();
        Long shopId = order.getCpCShopId();

        return warehouseId != null && shopId != null;
    }

    /**
     * 匹配物流
     *
     * @param relation
     * @param user
     * @return
     */
    private ValueHolderV14<Long> adapterExpress(OcBOrderRelation relation, User user) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        if (log.isInfoEnabled()) {
            log.info("Match adapterExpress :{}", JSON.toJSONString(relation));
        }
        // validate
        if (!this.beforeValidate(relation, user)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("beforeValidate 校验失败！");
            holderV14.setData(null);
            return holderV14;
        }
        // 订单信息
        OcBOrder order = relation.getOrderInfo();
        // 查找条件
        Long warehouseId = order.getCpCPhyWarehouseId();
        // step 1 查找仓库物流
        List<StCWarehouseLogisticStrategyResult> warehouseExpressAll = stRpcService.getWarehouseExpress(warehouseId);
        if (CollectionUtils.isEmpty(warehouseExpressAll)) {
            this.recordLog(order.getId(), order.getBillNo(), "执行【仓库物流策略】，本订单的仓库物流没有配置，寻物流失败；", user);
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("执行【仓库物流策略】，本订单的仓库物流没有配置，寻物流失败");
            holderV14.setData(null);
            return holderV14;
        }
        log.info(LogUtil.format("OmsOrderDistributeLogisticsService.adapterExpress,warehouseExpress:{}"
                , "OmsOrderDistributeLogisticsService.adapterExpress"), JSONObject.toJSONString(warehouseExpressAll));

        BigDecimal weight = relation.getOrderItemList().stream().map(x -> x.getQty().multiply(x.getStandardWeight() == null ? BigDecimal.ZERO : x.getStandardWeight())).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal expressStartingWeight = getExpressStartingWeight();
        boolean flag = weight.compareTo(expressStartingWeight) >= 0;
        warehouseExpressAll = warehouseExpressAll.stream().filter(x -> flag ? x.getLogisticsType() == 2 : x.getLogisticsType() == 1).collect(Collectors.toList());
        Map<Long, String> logisticsMap = warehouseExpressAll.stream().collect(Collectors.toMap(StCWarehouseLogisticStrategyResult::getCpCLogisticsId, StCWarehouseLogisticStrategyResult::getCpCLogisticsEname, (k1, k2) -> k1));
        List<Long> warehouseExpressIds = warehouseExpressAll.stream().map(StCWarehouseLogisticStrategyResult::getCpCLogisticsId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseExpressIds)) {
            String type = flag ? "快运" : "快递";
            this.recordLog(order.getId(), order.getBillNo(), "执行【仓库物流策略】，本订单只能发" + type + "类型的物流公司，仓库物流策略中没有符合条件的物流公司，寻物流失败；", user);
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("执行【仓库物流策略】，本订单只能发" + type + "类型的物流公司，仓库物流策略中没有符合条件的物流公司，寻物流失败；");
            holderV14.setData(null);
            return holderV14;
        }
        this.recordLog(order.getId(), order.getBillNo(), "执行【仓库物流策略】，获取到的物流公司：" + new ArrayList<>(logisticsMap.values()), user);

        List<StCWarehouseLogisticStrategyResult> warehouseExpress = new ArrayList<>();
        if (YesNoEnum.Y.getKey().equals(warehouseExpressAll.get(0).getIsExpire())) {
            LocalTime now = LocalTime.now();
            for (StCWarehouseLogisticStrategyResult result : warehouseExpressAll) {
                if (StringUtils.isEmpty(result.getExpireStartTime()) || StringUtils.isEmpty(result.getExpireEndTime())) {
                    warehouseExpress.add(result);
                    continue;
                }
                boolean expireTime = checkTimeInRange(result.getExpireStartTime(), result.getExpireEndTime(), now);
                if (!expireTime) {
                    warehouseExpress.add(result);
                }
            }
            if (CollectionUtils.isEmpty(warehouseExpress)) {
                this.recordLog(order.getId(), order.getBillNo(), "执行【仓库物流失效策略】后无可发货物流，寻物流失败", user);
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("执行【仓库物流失效策略】后无可发货物流，寻物流失败");
                holderV14.setData(null);
                return holderV14;
            } else {
                List<String> logisticsNames = warehouseExpress.stream().map(StCWarehouseLogisticStrategyResult::getCpCLogisticsEname).filter(Objects::nonNull).collect(Collectors.toList());
                this.recordLog(order.getId(), order.getBillNo(), "执行【仓库物流失效策略】，获取到的物流公司：" + logisticsNames, user);
                warehouseExpressIds = warehouseExpress.stream().map(StCWarehouseLogisticStrategyResult::getCpCLogisticsId).collect(Collectors.toList());
            }
        } else {
            //没有启用失效时间，按照正常逻辑处理
            warehouseExpress = warehouseExpressAll;
        }

        List<Long> sortList = new ArrayList<>();
        warehouseExpress.stream().collect(Collectors.groupingBy(StCWarehouseLogisticStrategyResult::getItemPriority)).entrySet().stream()
                .sorted(Map.Entry.comparingByKey()).forEachOrdered(x -> x.getValue().forEach(y -> sortList.add(y.getCpCLogisticsId())));
        boolean lowPriceLogistics = false;
        List<OcBOrderItem> itemList = relation.getOrderItemList().stream().filter(s -> s.getReserveBigint02() != null &&
                Long.valueOf(YesNoEnum.ONE.getVal()).equals(s.getReserveBigint02())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemList) && itemList.size() == relation.getOrderItemList().size()) {
            lowPriceLogistics = true;
        }

        //用户指定了物流
        if (order.getAppointLogisticsId() != null) {
            List<Long> appointLogisticsIds = ListUtil.toList(order.getAppointLogisticsId());
            warehouseExpressIds.retainAll(appointLogisticsIds);
            if (CollectionUtils.isEmpty(warehouseExpressIds)) {
                log.info("仓库物流策略与指定物流公司无交集");
                this.recordLog(order.getId(), order.getBillNo(), "仓库物流策略与指定物流公司无交集", user);
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("仓库物流策略与指定物流公司无交集");
                holderV14.setData(null);
                return holderV14;
            }
            StringBuilder proMessage = new StringBuilder("[");
            for (Long id : warehouseExpressIds) {
                if (proMessage.length() != 1) {
                    proMessage.append(",");
                }
                String name = logisticsMap.get(id);
                proMessage.append(name);
            }
            proMessage.append("]");

            this.recordLog(order.getId(), order.getBillNo(), "执行【指定快递寻源】，与【仓库物流策略】间交集：" + proMessage, user);
        } else if (lowPriceLogistics) {
            this.recordLog(order.getId(), order.getBillNo(), "执行【优先最便宜快递】，排除店铺物流策略、商品物流策略", user);
        } else {
            // 执行店铺物流策略、商品物流策略，获取各个明细间的交集
            ValueHolderV14<List<Long>> v14 = this.distributeLogisticsList(relation, flag, user);
            log.info("v14.getData:{}", JSONObject.toJSONString(v14));
            if (!v14.isOK()) {
                this.recordLog(order.getId(), order.getBillNo(), StringUtils.isEmpty(v14.getMessage()) ? "" : v14.getMessage().substring(0, Math.min(v14.getMessage().length(), 200)), user);
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage(StringUtils.isEmpty(v14.getMessage()) ? "" : v14.getMessage().substring(0, Math.min(v14.getMessage().length(), 200)));
                holderV14.setData(null);
                return holderV14;
            }
            List<Long> shopExpressIds = v14.getData();
            if (CollectionUtils.isNotEmpty(shopExpressIds)) {
                warehouseExpressIds.retainAll(shopExpressIds);
                if (CollectionUtils.isEmpty(warehouseExpressIds)) {
                    log.info("仓库物流策略与店铺/商品物流策略间物流公司无交集");
                    this.recordLog(order.getId(), order.getBillNo(), "仓库物流策略与店铺/商品物流策略间物流公司无交集", user);
                    holderV14.setCode(ResultCode.FAIL);
                    holderV14.setMessage("仓库物流策略与店铺/商品物流策略间物流公司无交集");
                    holderV14.setData(null);
                    return holderV14;
                }
            }

            StringBuilder proMessage = new StringBuilder("[");
            for (Long id : warehouseExpressIds) {
                if (proMessage.length() != 1) {
                    proMessage.append(",");
                }
                String name = logisticsMap.get(id);
                proMessage.append(name);
            }
            proMessage.append("]");
            this.recordLog(order.getId(), order.getBillNo(), "执行【店铺/商品物流策略】，与【仓库物流策略】间交集：" + proMessage, user);
        }
        // 20220916 夏彬新增需求，店铺策略增加物流明细
        StCShopStrategyLogisticsItemResult shopLogistics = shopStrategyQueryCmd.queryShopStrategyLogisticsList(order.getCpCShopId());
        if (shopLogistics != null && shopLogistics.getLogisticsType() != null && shopLogistics.getLogisticsType() != 0 && CollectionUtils.isNotEmpty(shopLogistics.getLogisticsItemList())) {
            if (log.isDebugEnabled()) {
                log.debug("店铺策略物流公司配置：{}", JSONObject.toJSONString(shopLogistics));
            }
            List<String> shopStrategyLogisticsName = shopLogistics.getLogisticsItemList().stream().map(StCShopStrategyLogisticsItem::getCpCLogisticsEname).collect(Collectors.toList());
            this.recordLog(order.getId(), order.getBillNo(), "执行【店铺策略-物流公司】策略，获取到的物流公司：" + shopStrategyLogisticsName, user);
            List<Long> shopStrategyLogisticsList = shopLogistics.getLogisticsItemList().stream().map(StCShopStrategyLogisticsItem::getCpCLogisticsId).collect(Collectors.toList());
            if (shopLogistics.getLogisticsType() == 1) {
                warehouseExpressIds.retainAll(shopStrategyLogisticsList);
                if (CollectionUtils.isEmpty(warehouseExpressIds)) {
                    log.info("可发物流公司与店铺策略指定的物流公司无交集，无可发物流");
                    this.recordLog(order.getId(), order.getBillNo(), "执行【店铺策略-物流公司】后物流公司无交集，寻物流失败", user);
                    holderV14.setCode(ResultCode.FAIL);
                    holderV14.setMessage("执行【店铺策略-物流公司】后物流公司无交集，寻物流失败");
                    holderV14.setData(null);
                    return holderV14;
                }
            }
            if (shopLogistics.getLogisticsType() == 2) {
                warehouseExpressIds.removeAll(shopStrategyLogisticsList);
                if (CollectionUtils.isEmpty(warehouseExpressIds)) {
                    log.info("可发物流公司被店铺物流策略排除的物流公司排除，无可发物流");
                    this.recordLog(order.getId(), order.getBillNo(), "执行【店铺策略-物流公司】排除后物流公司无交集，寻物流失败", user);
                    holderV14.setCode(ResultCode.FAIL);
                    holderV14.setMessage("执行【【店铺策略-物流公司】排除后物流公司无交集，寻物流失败");
                    holderV14.setData(null);
                    return holderV14;
                }
            }
        } else {
            this.recordLog(order.getId(), order.getBillNo(), "执行【店铺策略-物流公司】策略，本订单的店铺没有配置策略；", user);
        }

        StringBuilder shopMessage = new StringBuilder("[");
        for (Long id : warehouseExpressIds) {
            if (shopMessage.length() != 1) {
                shopMessage.append(",");
            }
            String name = logisticsMap.get(id);
            shopMessage.append(name);
        }
        shopMessage.append("]");
        this.recordLog(order.getId(), order.getBillNo(), "执行【【店铺策略-物流公司】后物流公司交集:" + shopMessage, user);

        // step 2 过滤物流禁发区域设置
        List<Long> bansExpressIds = stRpcService.getBansExpress(order.getCpCPhyWarehouseId(), order.getCpCRegionProvinceId(),
                order.getCpCRegionCityId(), order.getCpCRegionAreaId());
        if (log.isDebugEnabled()) {
            log.debug("Match before bansExpressIds:{}", JSON.toJSONString(bansExpressIds));
        }
        if (CollectionUtils.isNotEmpty(bansExpressIds)) {
            StringBuilder bansMessage = new StringBuilder("[");
            for (Long id : bansExpressIds) {
                if (bansMessage.length() != 1) {
                    bansMessage.append(",");
                }
                String name = logisticsMap.get(id);
                if (name == null) {
                    continue;
                }
                bansMessage.append(name);
            }
            bansMessage.append("]");
            warehouseExpressIds.removeAll(bansExpressIds);
            if (CollectionUtils.isEmpty(warehouseExpressIds)) {
                log.info("可发物流公司被物流禁发区域设置排除，无可发物流");
                this.recordLog(order.getId(), order.getBillNo(), "剔除掉禁发物流公司：" + bansMessage + "后无可发货物流，寻物流失败", user);
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("剔除掉禁发物流公司：" + bansMessage + "后无可发货物流，寻物流失败");
                holderV14.setData(null);
                return holderV14;
            }
            bansMessage = new StringBuilder("[");
            for (Long id : warehouseExpressIds) {
                if (bansMessage.length() != 1) {
                    bansMessage.append(",");
                }
                String name = logisticsMap.get(id);
                bansMessage.append(name);
            }
            bansMessage.append("]");
            this.recordLog(order.getId(), order.getBillNo(), "执行【物流禁发区域】后物流公司集合:" + bansMessage, user);
        }
        //京东分销订单分物流逻辑
        if (PlatFormEnum.JINGDONG_DX.getCode().equals(order.getPlatform())) {
            ValueHolderV14 v141 = jingDongDxDistributeLogistics(order, warehouseExpressIds);
            if (v141.isOK()) {
                warehouseExpressIds = (List<Long>) v141.getData();
                this.recordLog(order.getId(), order.getBillNo(), "执行【京东自营物流策略】取交集后物流公司集合:" + warehouseExpressIds, user);
            } else {
                this.recordLog(order.getId(), order.getBillNo(), v141.getMessage(), user);
                return v141;
            }
        }
        if (weight.compareTo(BigDecimal.ZERO) <= 0) {
            this.recordLog(order.getId(), order.getBillNo(), "执行【物流成本】，订单重量为空，按照仓库优先级执行", user);
            for (Long entry : sortList) {
                if (warehouseExpressIds.contains(entry)) {
                    holderV14.setCode(ResultCode.SUCCESS);
                    holderV14.setMessage("寻物流成功！");
                    holderV14.setData(entry);
                    return holderV14;
                }
            }
        }
        AcExpectedLogisticsFeeQueryRequest acRequest = new AcExpectedLogisticsFeeQueryRequest();
        acRequest.setWeight(weight);
        acRequest.setCalBaseDate(new Date());
        acRequest.setCpCLogisticsIds(warehouseExpressIds);
        acRequest.setCpCPhyWarehouseId(order.getCpCPhyWarehouseId());
        acRequest.setCpCRegionCityId(order.getCpCRegionCityId());
        acRequest.setCpCRegionProvinceId(order.getCpCRegionProvinceId());
        acRequest.setCarpoolNo(order.getCarpoolNo());
        acRequest.setSourceBillNo(order.getBillNo());
        acRequest.setIsIncludeZero(Boolean.TRUE);
        List<AcExpectedLogisticsFeeQueryResult> logisticsFeeQueryResultList;
        try {
            ValueHolderV14<List<AcExpectedLogisticsFeeQueryResult>> acV14 = acLogisticsFeeQueryCmd.queryExpectedLogisticsFee(acRequest, user);
            log.info(LogUtil.format("OmsOrderDistributeLogisticsService.adapterExpress billNo:{},acV14:{}",
                    "OmsOrderDistributeLogisticsService.adapterExpress.acV14"), order.getBillNo(), JSONObject.toJSONString(acV14));
            logisticsFeeQueryResultList = acV14.getData();
            if (!acV14.isOK()) {
                log.error("获取物流公司成本失败，失败原因：{}", acV14.getMessage());
                this.recordLog(order.getId(), order.getBillNo(), "获取物流公司成本失败", user);
                holderV14.setCode(ResultCode.FAIL);
                holderV14.setMessage("获取物流公司成本失败");
                holderV14.setData(null);
                return holderV14;
            }
        } catch (Exception e) {
            log.error("获取物流公司成本异常，异常原因：{}", Throwables.getStackTraceAsString(e));
            this.recordLog(order.getId(), order.getBillNo(), "获取物流公司成本异常", user);
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("获取物流公司成本异常");
            holderV14.setData(null);
            return holderV14;
        }

        //无配置物流公司ids
        List<Long> notExistPriceLogistics = logisticsFeeQueryResultList.stream().filter(s -> Boolean.FALSE.equals(s.getExistPrice()))
                .map(AcExpectedLogisticsFeeQueryResult::getCpCLogisticsId).collect(Collectors.toList());
        warehouseExpressIds.removeAll(notExistPriceLogistics);
        if (CollectionUtils.isEmpty(warehouseExpressIds)) {
            List<String> expressNoLogisNames = notExistPriceLogistics.stream().map(detail -> logisticsMap.getOrDefault(detail, "")).collect(Collectors.toList());
            log.info("获取快递费用配置，无可发物流");
            this.recordLog(order.getId(), order.getBillNo(), "剔除掉快递费用未配置物流公司：" + expressNoLogisNames + "后无可发货物流，寻物流失败", user);
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("剔除掉快递费用未配置物流公司：" + expressNoLogisNames + "后无可发货物流，寻物流失败");
            holderV14.setData(null);
            return holderV14;
        }
        this.recordLog(order.getId(), order.getBillNo(), "执行【排除未维护目的省份报价快递】结果:" +
                warehouseExpressIds.stream().map(p -> logisticsMap.getOrDefault(p, "")).collect(Collectors.toList()), user);

        List<AcExpectedLogisticsFeeQueryResult> existPriceResult = logisticsFeeQueryResultList.stream()
                .filter(s -> Boolean.TRUE.equals(s.getExistPrice())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existPriceResult)) {
            StringBuilder acMessage = new StringBuilder("[");
            for (AcExpectedLogisticsFeeQueryResult obj : existPriceResult) {
                if (acMessage.length() != 1) {
                    acMessage.append(";");
                }
                String name = logisticsMap.get(obj.getCpCLogisticsId());
                acMessage.append(name).append(":").append(obj.getLogisticsFee());
            }
            acMessage.append("]");
            this.recordLog(order.getId(), order.getBillNo(), "执行【物流成本】结果:" + acMessage, user);

            List<AcExpectedLogisticsFeeQueryResult> minPriceExpress = existPriceResult.stream()
                    .filter(x -> x.getLogisticsFee().compareTo(existPriceResult.get(0).getLogisticsFee()) == 0).collect(Collectors.toList());
            if (minPriceExpress.size() == 1) {
                holderV14.setCode(ResultCode.SUCCESS);
                holderV14.setMessage("寻物流成功！");
                holderV14.setData(minPriceExpress.get(0).getCpCLogisticsId());
                return holderV14;
            }
            warehouseExpressIds = minPriceExpress.stream().map(AcExpectedLogisticsFeeQueryResult::getCpCLogisticsId).collect(Collectors.toList());
        } else {
            this.recordLog(order.getId(), order.getBillNo(), "执行【物流成本】结果，全部物流公司均未匹配到成本，执行仓库物流设置优先级", user);
        }
        for (Long entry : sortList) {
            if (warehouseExpressIds.contains(entry)) {
                holderV14.setCode(ResultCode.SUCCESS);
                holderV14.setMessage("寻物流成功！");
                holderV14.setData(entry);
                return holderV14;
            }
        }
        //会在上面一个循环return，如果走到这里，就是有问题
        log.error("寻物流异常！执行了不该执行的逻辑");
        holderV14.setCode(ResultCode.FAIL);
        holderV14.setMessage("寻物流异常！执行了不该执行的逻辑");
        holderV14.setData(null);
        return holderV14;
    }

    /**
     * 时间格式为HH:mm:ss，判断当前时间是不是在指定时间范围内
     *
     * @param expireStartTime
     * @param expireEndTime
     * @param currentTime
     * @return
     */
    private boolean checkTimeInRange(String expireStartTime, String expireEndTime, LocalTime currentTime) {
        LocalTime startTime = LocalTime.parse(expireStartTime);
        LocalTime endTime = LocalTime.parse(expireEndTime);

        // 判断是否跨天
        if (endTime.isBefore(startTime)) {
            return currentTime.isAfter(startTime) || currentTime.isBefore(endTime);
        } else {
            return currentTime.isAfter(startTime) && currentTime.isBefore(endTime);
        }
    }

    /**
     * 查询物流公司报价配置
     *
     * @param orderId
     * @param warehouseId
     * @param provinceId
     * @param warehouseExpressIds
     * @return
     */
    public List<AcStExpressQueryResult> queryStExpress(Long orderId, Long warehouseId, Long provinceId,
                                                       List<Long> warehouseExpressIds) {
        AcStExpressQueryRequest expressQueryRequest = new AcStExpressQueryRequest();
        expressQueryRequest.setCalBaseDate(new Date());
        expressQueryRequest.setCpCLogisticsIds(warehouseExpressIds);
        expressQueryRequest.setCpCPhyWarehouseId(warehouseId);
        expressQueryRequest.setCpCRegionProvinceId(provinceId);

        ValueHolderV14<List<AcStExpressQueryResult>> acExV14;
        try {
            acExV14 = acLogisticsFeeQueryCmd.queryStExpress(expressQueryRequest);
        } catch (Exception e) {
            log.error("获取快递费用配置 orderId:{}, 异常原因：{}", orderId, Throwables.getStackTraceAsString(e));
            DingTalkUtil.dingLogisticFee(orderId, "获取快递费用配置异常");
            throw new NDSException("获取快递费用配置异常");
        }
        if (!acExV14.isOK()) {
            log.error("获取快递费用配置失败 orderId:{}, 失败原因：{}", orderId, acExV14.getMessage());
            throw new NDSException("获取快递费用配置失败");
        }
        return acExV14.getData();
    }

    /**
     * description:京东代销店铺查询物流公司
     *
     * @Author: liuwenjin
     * @Date 2022/12/9 13:37
     */
    private ValueHolderV14 jingDongDxDistributeLogistics(OcBOrder order, List<Long> warehouseExpressIds) {
        ValueHolderV14 v14 = new ValueHolderV14();
        //order缺少字段重新查询原单
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(order.getId());
        //调用京东代销物流接口
        List<Long> logisticIds = stRpcService.queryJdDistributionLogistics(ocBOrder.getCpCShopId(), ocBOrder.getSellerId());
        if (CollectionUtils.isNotEmpty(logisticIds) && CollectionUtils.isNotEmpty(warehouseExpressIds)) {
            //取两个中间的交集
            logisticIds.retainAll(warehouseExpressIds);
            if (CollectionUtils.isNotEmpty(logisticIds)) {
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage("京东代销店铺查询物流成功！");
                v14.setData(logisticIds);
            } else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("与分销商可用物流不存在交集！");
                v14.setData(null);
            }
        } else {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("京东分销商物流设置不存在有效物流!");
            v14.setData(null);
        }
        return v14;
    }

    /**
     * 记录操作日志
     *
     * @param orderId
     * @param billNo
     * @param msg
     */
    private void recordLog(Long orderId, String billNo, String msg, User user) {
        omsOrderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), msg, "",
                "", user);
    }

    /**
     * description:异步执行重新寻物流
     *
     * @Author: liuwenjin
     * @Date 2022/9/23 16:40
     */
    public ValueHolder syncAgainDistributionLogistics(JSONObject obj, User operateUser) {

        ValueHolder vh = new ValueHolder();
        if (obj.getString("isAsync") == null || !obj.getBoolean("isAsync")) {
            List<Long> longs = JSON.parseArray(obj.getJSONArray("ids").toJSONString(), Long.class);
            if (longs == null || longs.size() == 0) {
                throw new NDSException(Resources.getMessage("请求参数不能为空!", operateUser.getLocale()));
            }
            JSONArray errorMessage = new JSONArray();
            int fail = 0;
            for (int i = 0; i < longs.size(); i++) {
                JSONObject jsonObject = new JSONObject();
                Long orderId = longs.get(i);
                try {
                    this.manualDistributionLogistics(orderId, operateUser);
                } catch (Exception e) {
                    log.error("重新寻物流失败，orderId:{},失败原因：{}", orderId, e.getMessage());
                    jsonObject.put("code", -1);
                    jsonObject.put("message", e.getMessage());
                    jsonObject.put("objid", longs.get(i));
                    errorMessage.add(jsonObject);
                    vh.put("code", com.jackrain.nea.constants.ResultCode.FAIL);
                    vh.put("message", e.getMessage());
                    String message = e.getMessage();
                    if (e.getMessage() != null && message.length() > 1000) {
                        message = message.substring(0, 1000);
                    }
                    insertOrderLog(orderId, null, OrderLogTypeEnum.AGAIN_DISTRIBUTION_LOGISTICS.getKey(), message, null, null, operateUser);
                }
            }
            vh.put("data", errorMessage);
            if (fail == 0) {
                vh.put("code", com.jackrain.nea.constants.ResultCode.SUCCESS);
                vh.put("message", Resources.getMessage("重新寻物流成功", operateUser.getLocale()));
            } else {
                vh.put("code", com.jackrain.nea.constants.ResultCode.FAIL);
                vh.put("message", Resources.getMessage("重新寻物流成功" + (longs.size() - fail) + "条，失败了" + fail + "条", operateUser.getLocale()));
            }
            return vh;
        } else {
            //插入我的任务里
            AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
            asyncTaskBody.setTaskId(UUID.randomUUID().toString());
            asyncTaskBody.setMenu("异步重新寻物流");
            asyncTaskBody.setTaskType("异步重新寻物流");
            //任务开始
            asyncTaskManager.beforeExecute(operateUser, asyncTaskBody);
            commonTaskExecutor.submit(() -> {
                //主线任务
                ValueHolder valueHolder = againDistributionLogistics(obj, operateUser);
                HashMap hashMap = valueHolder.getData();
                Map<Object, Object> retMap = new LinkedHashMap<>();
                if (valueHolder.isOK()) {
                    retMap.put("code", com.jackrain.nea.constants.ResultCode.SUCCESS);
                    Map<Object, Object> map = new LinkedHashMap<>();
                    map.put("入参", obj);
                    map.put("出参", JSON.toJSONString(hashMap));
                    retMap.put("message", map);
                } else {
                    retMap.put("code", com.jackrain.nea.constants.ResultCode.FAIL);
                    retMap.put("入参", obj);
                    retMap.put("出参", JSON.toJSONString(hashMap));
                }
                log.info("主线任务返回结果为:{}", valueHolder);
                //任务完成
                asyncTaskManager.afterExecute(operateUser, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            });
            vh.put("code", ResultCode.SUCCESS);
            vh.put("data", asyncTaskBody.getId());
            vh.put("message", Resources.getMessage("执行成功，异步重新寻物流任务开始！", operateUser.getLocale()));
        }
        return vh;

    }

    /**
     * description:重新寻物流
     *
     * @Author: liuwenjin
     * @Date 2022/9/23 16:44
     */
    private ValueHolder againDistributionLogistics(JSONObject obj, User operateUser) {

        if (log.isDebugEnabled()) {
            log.debug("againDistributionLogistics.param{}", obj);
        }
        List<Long> longList = JSON.parseArray(obj.getJSONArray("ids").toJSONString(), Long.class);
        if (longList == null || longList.size() == 0) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", operateUser.getLocale()));
        }

        String threadPoolName = "R3_OMS_CREATE_DISTRIBUTION_LOGISTICS_ORDER_THREAD_POOL_%d";
        int failed = 0;
        ValueHolder vh = new ValueHolder();
        JSONArray errorMessage = new JSONArray();
        try {
            if (CollectionUtils.isNotEmpty(longList)) {
                PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
                //分5个线程处理
                int splitNum = config.getProperty("lts.order.distribution.logistics.split.num", 10);
                int tmpNum = longList.size() / splitNum;//每个小list分的个数
                if (longList.size() % splitNum != 0) {
                    tmpNum = tmpNum + 1;
                }
                List<List<Long>> splitList = SplitListUtil.partition(longList, tmpNum);
                List<Future<List<JSONObject>>> results =
                        new ArrayList<Future<List<JSONObject>>>();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitList)) {
                    log.info("againDistributionLogistics.分组数据为{}条", splitList.size());
                    //线程提交
                    for (int i = 0; i < splitList.size(); i++) {
                        results.add(createDistributionLogisticsOrderThreadPoolExecutor.submit(new OmsOrderDistributeLogisticsService.CallableTaskWithResult(splitList.get(i), operateUser)));
                    }
                    //线程执行结果获取
                    for (Future<List<JSONObject>> futureResult : results) {
                        if (log.isDebugEnabled()) {
                            log.debug("againDistributionLogistics------>线程结果:{}", JSON.toJSONString(futureResult));
                        }
                        List<JSONObject> callableMsgList = futureResult.get();
                        for (JSONObject jsonObject : callableMsgList) {
                            if (Objects.nonNull(jsonObject)) {
                                errorMessage.add(jsonObject);
                                failed++;
                            }
                        }
                    }
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("{} Thread againDistributionLogistics InterruptedException：{}", threadPoolName, e);
        } catch (ExecutionException e) {
            log.warn("{} Thread againDistributionLogistics  ExecutionException：{}", threadPoolName, e);
        }
        vh.put("data", errorMessage);
        if (failed == 0) {
            vh.put("code", com.jackrain.nea.constants.ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("重新寻物流成功", operateUser.getLocale()));
        } else {
            vh.put("code", com.jackrain.nea.constants.ResultCode.FAIL);
            vh.put("message", Resources.getMessage("重新寻物流成功" + (longList.size() - failed) + "条，失败了" + failed + "条", operateUser.getLocale()));
        }
        return vh;
    }

    /**
     * description:多线程重新寻物流
     *
     * @Author: liuwenjin
     * @Date 2021/12/13 3:27 下午
     */
    public class CallableTaskWithResult implements Callable<List<JSONObject>> {
        private final List<Long> longs;
        private final User operateUser;

        public CallableTaskWithResult(List<Long> longs, User operateUser) {
            this.longs = longs;
            this.operateUser = operateUser;
        }

        @Override
        public List<JSONObject> call() throws Exception {
            ValueHolder vh = new ValueHolder();
            List<JSONObject> jsonObjectList = new ArrayList<>(longs.size());
            for (int i = 0; i < longs.size(); i++) {
                JSONObject jsonObject = new JSONObject();
                Long orderId = longs.get(i);
                try {
                    vh = manualDistributionLogistics(orderId, operateUser);
                } catch (Exception e) {
                    log.error("重新寻物流失败，orderId:{},失败原因：{}", orderId, e.getMessage());
                    jsonObject.put("code", -1);
                    jsonObject.put("message", e.getMessage());
                    jsonObject.put("objid", longs.get(i));
                    jsonObjectList.add(jsonObject);
                    vh.put("code", com.jackrain.nea.constants.ResultCode.FAIL);
                    vh.put("message", e.getMessage());
                    String message = e.getMessage();
                    if (e.getMessage() != null && message.length() > 1000) {
                        message = message.substring(0, 1000);
                    }
                    insertOrderLog(orderId, null, OrderLogTypeEnum.AGAIN_DISTRIBUTION_LOGISTICS.getKey(), message, null, null, operateUser);
                }
            }
            return jsonObjectList;
        }
    }

    /**
     * description:重新寻物流
     *
     * @Author: liuwenjin
     * @Date 2022/9/23 17:25
     */
    private ValueHolder manualDistributionLogistics(Long orderId, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("distributionLogistics 重新寻物流 start orderId=", orderId));
        }
        ValueHolder vh = new ValueHolder();
        // 给订单加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
                if (ocBOrder == null) {
                    throw new NDSException("订单不存在");
                }
                if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
                    throw new NDSException("订单非待审核单状态,不允许寻物流");
                }
                //业务类型
                Long businessTypeId = ocBOrder.getBusinessTypeId();
                if (businessTypeId == null) {
                    AssertUtils.logAndThrow("当前订单的业务类型为空");
                }
                StCBusinessType businessType = typeMapper.selectById(businessTypeId);
                if (businessType.getIsSourceOccupy() != null && businessType.getIsSourceOccupy() == 0) {
                    AssertUtils.logAndThrow("该类型的订单,不允许寻物流！");
                }
                //执行寻重新寻物流
                distributeLogisticsDistributeLogistics(ocBOrder);
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", operateUser.getLocale()));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("重新寻物流,异常信息={},OrderId=", orderId), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        } finally {
            redisLock.unlock();
        }
        return vh;
    }

    // 插入订单日志
    public void insertOrderLog(long orderId, String billNo, String logType, String logMessage, String param,
                               String errorMessage, User operateUser) {
        //调用添加订单日志
        try {
            orderLogService.addUserOrderLog(orderId, billNo, logType, logMessage, null, null, operateUser);
        } catch (Exception e) {
            log.error(LogUtil.format("新增订单日志失败，失败原因:{}"), Throwables.getStackTraceAsString(e));
        }
    }

    private BigDecimal getExpressStartingWeight() {
        try {
            String value =
                    (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(OC_B_ORDER_EXPRESS_STARTING_WEIGHT);
            if (StringUtils.isEmpty(value)) {
                return BigDecimal.ZERO;
            }
            return new BigDecimal(value);
        } catch (Exception e) {
            log.error("Step150SourcingBeforeDisassemble.getExpressStartingWeight.error={}",
                    Throwables.getStackTraceAsString(e));
            return BigDecimal.ZERO;
        }
    }

}
