package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: 夏继超
 * @since: 2019/3/13
 * create at : 2019/3/13 16:04
 */
@Slf4j
@Component
@Transactional
public class ReturnGiftService {
    /**
     * 查询满足条件的赠品的集合
     *
     * @param param
     * @return
     */
    public ValueHolderV14 returnGift(String param) {
        ValueHolderV14 vh = new ValueHolderV14();
        // TODO: 2019/3/13  调用别人的接口 返回一个满足条件的结果集

        return vh;
    }
}
