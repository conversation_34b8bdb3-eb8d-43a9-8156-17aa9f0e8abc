package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4RefundIn;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBRefundInTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundInTask;
import com.jackrain.nea.oc.oms.util.WmsUserCreateUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sg.service.AddOrderNoticeAndOutService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @author: 陈秀楼
 * @since: 2019/9/25
 */
@Slf4j
@Component
public class RefundInMakeUpService {
    private static final Integer pageSize = 500;
    @Autowired
    private OcBRefundInMapper refundInMapper;
    @Autowired
    private OcBRefundInProductItemMapper refundInItemMapper;
    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;
    @Autowired
    private OcBReturnStockInService refundInExtService;

    @Autowired
    private OcBReturnOrderRefundMapper refundMapper;

    @Autowired
    private WmsUserCreateUtil wmsUserCreateUtil;
    @Autowired
    private OcBRefundInTaskMapper ocBRefundInTaskMapper;

    @Autowired
    private AddOrderNoticeAndOutService addOrderNoticeAndOutService;

    @Autowired
    private RefundOrderToWmsBackService refundOrderToWmsBackService;

    /**
     * @param total   单程处理数
     * @param timeout 超时时长
     * @return
     * @Description 处理退货入库超时未入库 设置异常状态
     * <AUTHOR>
     * @date 2019-09-25 2019-09-25
     */
    public ValueHolderV14 dealTimeOutRefundInsException(Integer total, Integer timeout) {
        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            //1.查询 所有状态为：等待退货入库状态 并且 是原退的退货单
            JSONObject whereKeys = new JSONObject();
            //退货入库:等待退货入库
            whereKeys.put("IN_STATUS", ReturnStatus.WAITING_FOR_STORAGE.toInteger());
            whereKeys.put("ISACTIVE", "Y");


            JSONObject filterKeys = new JSONObject();

            Calendar calendar = Calendar.getInstance();
            calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH),
                    0, 0, 0);
            Long beginToday = calendar.getTimeInMillis();
            Long endTime = DateUtils.addMinutes(new Date(), -1 * timeout).getTime();
            filterKeys.put("MODIFIEDDATE", beginToday + "~" + endTime);

            JSONObject orderKey = new JSONObject();
            //CREATIONDATE降序
            orderKey.put("asc", false);
            orderKey.put("name", "MODIFIEDDATE");
            JSONArray order = new JSONArray();
            order.add(orderKey);
            int totalPage = getTotalPage(total);
            for (int i = 1; i < totalPage; i++) {
                List<Long> idList = ES4RefundIn.selectRefundInIdListByES(whereKeys, filterKeys, order, null, pageSize, i);

                if (CollectionUtils.isNotEmpty(idList)) {
                    List<OcBRefundIn> ocBRefundIns = refundInMapper.selectBatchIds(idList);
                    if (CollectionUtils.isNotEmpty(ocBRefundIns)) {
                        for (OcBRefundIn refundIn : ocBRefundIns) {
                            dealRefundInExceptionsToTable(refundIn);
                        }
                    }
                    ValueHolderV14 v = updateRefundInStatus(idList);
                    if (v.getCode() == ResultCode.FAIL) {
                        log.error(LogUtil.format("定时任务处理超时退货未入库异常失败：第" + i + "页，数量：" + idList.size() + ";异常：" + v.getMessage()));
                    }
                }
            }
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("自动化任务完成！");
        } catch (Exception ex) {
            log.error(LogUtil.format("定时任务处理超时退货未入库异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("自动化任务异常！");
        }
        return v14;
    }

    /**
     * @param range
     * @return void
     * @Description 处理退货入库入库状态为已入库, 退货入库单明细存在退单编号，是否匹配为否
     * <AUTHOR>
     * @date 2019/9/25 19:13
     */
    public void dealWaitMatchRefundInException(Integer range) {
        try {
            //1.查询当天退货入库单主表入库状态为已入库（2），退货入库单明细存在退单编号，是否匹配为否
            JSONObject whereKeys = new JSONObject();
            //入库状态:已入库
            whereKeys.put("IN_STATUS", ReturnStatus.WAREHOUSING.toInteger());
            whereKeys.put("ISACTIVE", "Y");
            //日期当天
            JSONObject filterKeys = new JSONObject();
            Calendar calendar = Calendar.getInstance();
            calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH),
                    0, 0, 0);
            Long todayBegin = calendar.getTimeInMillis();
            Long todayEnd = System.currentTimeMillis();
            filterKeys.put("MODIFIEDDATE", todayBegin + "~" + todayEnd);
            //是否匹配为否
            JSONObject childsKeys = new JSONObject();
            childsKeys.put("IS_MATCH", 0);
            //CREATIONDATE降序
            JSONObject orderKey = new JSONObject();
            orderKey.put("asc", false);
            orderKey.put("name", "MODIFIEDDATE");
            JSONArray order = new JSONArray();
            order.add(orderKey);
            List<Long> idList = ES4RefundIn.selectRefundInIdListByES(whereKeys, filterKeys, order, childsKeys, range, 1);

            if (CollectionUtils.isNotEmpty(idList)) {
                List<OcBRefundIn> ocBRefundIns = refundInMapper.selectBatchIds(idList);
                RefundInMakeUpService refundInMakeUpService = ApplicationContextHandle.getBean(RefundInMakeUpService.class);
                //2.处理满足条件的退货入库单
                ocBRefundIns.forEach(refundIn -> {
                    try {
                        refundInMakeUpService.dealRefundInForWaitMatch(refundIn);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                });
            }
        } catch (Exception e) {
            log.error(LogUtil.format("定时任务处理已入库,退货入库单明细存在退单编号,是否匹配为否退货未入库异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void dealRefundInForWaitMatch(OcBRefundIn refundIn) {
        try {
            QueryWrapper<OcBRefundInProductItem> wrapper = new QueryWrapper<>();
            wrapper.eq("OC_B_REFUND_IN_ID", refundIn.getId());
            List<OcBRefundInProductItem> ocBRefundInProductItems = refundInItemMapper.selectList(wrapper);
            if (ocBRefundInProductItems.isEmpty()) {
                throw new NDSException("未能查询到明细信息!");
            }
            User rootUser = SystemUserResource.getRootUser();
            boolean updateFlag = false;
            boolean returnStatusFlag = wareHousingServiceFun(refundIn, ocBRefundInProductItems, rootUser);//退换货状态是否为：退货等待入库
            if (!returnStatusFlag) {
                for (OcBRefundInProductItem refundInProductItem : ocBRefundInProductItems) {
                    if (refundInProductItem.getOcBReturnOrderId() != null) {
                        OcBReturnOrder returnOrder = returnOrderMapper.getMatchReturnOrderById(refundInProductItem.getOcBReturnOrderId());
                        if (returnOrder != null) {

                            JSONObject jsonObject = new JSONObject();
                            //修改是否匹配,是否生成入库单
                            jsonObject.put("ID", refundInProductItem.getId());
                            jsonObject.put("MODIFIERID", Long.valueOf(rootUser.getId()));
                            jsonObject.put("MODIFIERNAME", rootUser.getName());
                            jsonObject.put("MODIFIERENAME", rootUser.getEname());
                            jsonObject.put("MODIFIEDDATE", new Date());
                            jsonObject.put("IS_MATCH", 1);
                            jsonObject.put("IS_GEN_IN_ORDER", 1);
                            int updateCount = refundInItemMapper.updateByIdSql(jsonObject);
                            if (updateCount > 0) {
                                updateFlag = true;
                            }
                        }
                    }
                }
                //明细变动时修改退货入库主表匹配状态判断改为全部匹配或者部分匹配并推送ES
                if (updateFlag) {
                    List<OcBRefundInProductItem> refundInProductItems = refundInItemMapper.selectProductItemByRefundInId(refundIn.getId());
                    //1-部分匹配,2-全部匹配
                    refundIn.setMatchStatus(OcBOrderConst.REFUND_IN_MATCHSTATUS_ALL);
                    if (CollectionUtils.isNotEmpty(refundInProductItems)) {
                        refundIn.setMatchStatus(OcBOrderConst.REFUND_IN_MATCHSTATUS_PORTION);
                    }
                    refundInMapper.updateById(refundIn);
                   /* //推送主子表ES
                    if (!SpecialElasticSearchUtil.indexExists(OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME)) {
                        SpecialElasticSearchUtil.indexCreate(OcBRefundInProductItem.class, OcBRefundIn.class);
                    }
                    SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, refundIn, refundIn.getId());
                    SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_PRODUCT_ITEM_TYPE_NAME, ocBRefundInProductItems, "OC_B_REFUND_IN_ID");*/
                }
            }

        } catch (Exception e) {
            throw new NDSException(String.format("处理退货入库单[%d]失败!" + e.getMessage(), refundIn.getId()));
        }
    }

    /**
     * @param refundIn                退货入库单主信息
     * @param ocBRefundInProductItems 退货入库单明细信息
     * @param rootUser
     * @deprecation 如果是等待退货入库状态则调用退货入库服务
     */
    private boolean wareHousingServiceFun(OcBRefundIn refundIn, List<OcBRefundInProductItem> ocBRefundInProductItems, User rootUser) {
        boolean returnStatusFlag = false;//退换货状态是否为：退货等待入库
        for (OcBRefundInProductItem refundInProductItem : ocBRefundInProductItems) {
            if (refundInProductItem.getOcBReturnOrderId() != null) {
                //退单编号非空，先判断是否为等待退货入库状态
                //如果是等待退货入库状态则调用退货入库服务
                OcBReturnOrder returnOrderNew = returnOrderMapper.selectByid(refundInProductItem.getOcBReturnOrderId());
                if (returnOrderNew == null) {
                    continue;
                }
//                if( OcBOrderConst.RETURN_STATUS_WAIT_REFUND.equals(returnOrderNew.getReturnStatus())){
//                    returnStatusFlag = true;
//                    break;
//                }
                if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnOrderNew.getReturnStatus())) {
                    returnStatusFlag = true;
                    break;
                }
            }
        }
        //如果是等待退货入库状态则调用退货入库服务
        if (returnStatusFlag) {
            // 调用退货入库服务
            try {
                RefundInRelation refundInRelation = new RefundInRelation();
                refundInRelation.setRefundIn(refundIn);
                refundInRelation.setItems(ocBRefundInProductItems);
                ValueHolderV14 holderV14 = refundInExtService.returnWarehousing(refundInRelation, rootUser);
                if (!holderV14.isOK()) {
                    log.debug(this.getClass().getSimpleName() + " 补偿task退货入库服务失败" + holderV14.getMessage());
                    throw new NDSException(holderV14.getMessage());
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("补偿task退货入库服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
                throw new NDSException(ex.getMessage());
            }
        }
        return returnStatusFlag;
    }


    /**
     * @param refundIn
     * @return
     * @Description 处理退单入库表 字段
     * <AUTHOR>
     * @date 2019-09-29 2019-09-29
     */
    private ValueHolderV14 dealRefundInExceptionsToTable(OcBRefundIn refundIn) {
        ValueHolderV14 v14 = new ValueHolderV14();
        try {
            QueryWrapper<OcBRefundIn> wrapper = new QueryWrapper<OcBRefundIn>();
            OcBRefundIn uprefundIn = new OcBRefundIn();
            if (refundIn.getInStatus().equals(ReturnStatus.WAITING_FOR_STORAGE.toInteger())) {
                //货入库单状态为全部匹配或者部分匹配的，将入库状态改为2，失败次数改为0（reserve_bigint01）,失败原因清空（reserve_varchar06）
                if (refundIn.getMatchStatus().intValue() == OcBOrderConst.REFUND_IN_MATCHSTATUS_PORTION || refundIn.getMatchStatus().intValue() == OcBOrderConst.REFUND_IN_MATCHSTATUS_ALL) {
                    //已入库
                    uprefundIn.setInStatus(ReturnStatus.WAREHOUSING.toInteger());
                    uprefundIn.setQtyFail(0L);
                    uprefundIn.setRemarkIn("");
                    makeRefundInModifyField(uprefundIn, SystemUserResource.getRootUser());
                } else {
                    uprefundIn.setInStatus(ReturnStatus.REFUND_EXCEPTION.toInteger());
                    makeRefundInModifyField(uprefundIn, SystemUserResource.getRootUser());
                }
            } else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("单据Id：" + refundIn.getId() + "状态为：" + refundIn.getInStatus() + ",无需做货入库单修复！");
                return v14;
            }

            wrapper.eq("ID", refundIn.getId());
            int update = refundInMapper.update(uprefundIn, wrapper);
            if (update > 0) {

            } else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("更新数据库失败！");
            }
        } catch (Exception ex) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("更新数据库失败！" + ex.toString());
        }
        return v14;
    }

    /**
     * @param idList
     * @return
     * @Description 更新退货入库 批量
     * <AUTHOR>
     * @date 2019-09-29 2019-09-29
     */
    private ValueHolderV14 updateRefundInStatus(List<Long> idList) {
        ValueHolderV14 v14 = new ValueHolderV14();
        try {

            QueryWrapper<OcBRefundIn> wrapper = new QueryWrapper<OcBRefundIn>();
            OcBRefundIn refundIn = new OcBRefundIn();
            refundIn.setInStatus(ReturnStatus.REFUND_EXCEPTION.toInteger());
            makeRefundInModifyField(refundIn, SystemUserResource.getRootUser());
            wrapper.in("ID", idList);
            wrapper.eq("IN_STATUS", ReturnStatus.WAITING_FOR_STORAGE.toInteger());
            int update = refundInMapper.update(refundIn, wrapper);
            if (update > 0) {

            } else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("更新数据库失败！");
            }
        } catch (Exception ex) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("更新数据库失败！" + ex.toString());
        }
        return v14;
    }

    /**
     * @param total 总数量
     * @return
     * @Description 获取循环总页数
     * <AUTHOR>
     * @date 2019-09-25 2019-09-25
     */
    private Integer getTotalPage(Integer total) {
        Integer totalPage = 1;
        //计算循环的次数
        if (total % pageSize == 0) {
            //整除，页数=总数/每页的条数
            totalPage = total / pageSize;
        } else {
            //不整除，页数=总数/每页的条数+1
            totalPage = total / pageSize + 1;
        }
        return totalPage;
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description 自动任务修改操作默认基础信息
     * @Date 2019-4-17
     * @Param [baseModelDO]
     * @Param [operateUser] 前端用户对象
     **/
    public OcBRefundIn makeRefundInModifyField(OcBRefundIn model, User user) {
        if (user != null) {
            model.setModifierid(Long.valueOf(user.getId()));
            if (StringUtils.isNotEmpty(user.getName())) {
                model.setModifiername(user.getName());
            }
            if (StringUtils.isNotEmpty(user.getEname())) {
                model.setModifierename(user.getEname());
            }
        }
        //修改时间
        model.setModifieddate(new Date());
        return model;
    }


    /**
     * 分页补偿，生成入库通知单
     *
     * @param pageSize size
     * @param count    失败次数
     */
    public void refundInCreateInNoticeMakeUp(Integer pageSize, Integer count, String statusStr) {
        List<OcBRefundInTask> ocBRefundInTasks = ocBRefundInTaskMapper.selectPageBySize(pageSize, count, statusStr);
        if (CollectionUtils.isEmpty(ocBRefundInTasks)) {
            return;
        }

        for (OcBRefundInTask task : ocBRefundInTasks) {
            refundOrderToWmsBackService.handle(task);
        }
    }


    /**
     * 根据WMS编码查询是否存在中间表
     *
     * @param wmsBillNo wms编号
     * @return active :Y/N
     */
    public boolean isExist(String wmsBillNo, String active) {
        List<OcBRefundInTask> ocBRefundInTasks = ocBRefundInTaskMapper.selectByWmsBillNo(wmsBillNo, active);
        return CollectionUtils.isNotEmpty(ocBRefundInTasks);
    }

    /**
     * 根据wms编号更新wms回传次数
     *
     * @param wmsBillNo wms编号
     */
    public void updateWmsCallBackCount(String wmsBillNo) {
        ocBRefundInTaskMapper.updateWmsCallBackCount(wmsBillNo);
    }
}

