package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.task.OcBRefundInTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundInTask;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/12/14 11:25
 * @Description B2C入库回传中间表
 */
@Slf4j
@Component
public class OcBRefundInTaskThirdService {

    @Resource
    private OcBRefundInTaskMapper mapper;

    /**
     * 重置失败次数
     *
     * @param session
     * @return
     */
    public ValueHolder resetFailCount(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.info(LogUtil.format("OcBRefundInTaskThirdService.resetFailCount param:{},user:{}",
                        "OcBRefundInTaskThirdService.resetFailCount"),
                param.toJSONString(), JSONObject.toJSONString(user));
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException("请先选择需要重制失败次数的记录！");
        }
        int successCount = 0;
        int failCount = 0;
        if (ids.size() == 1) {
            executeResetFailCount(ids.getLong(0), user);
            return ValueHolderUtils.getSuccessValueHolder("重置失败次数成功！");
        } else {
            for (int i = 0; i < ids.size(); i++) {
                Long id = ids.getLong(i);
                try {
                    executeResetFailCount(id, user);
                    successCount++;
                } catch (Exception e) {
                    log.warn(LogUtil.format("SgBWmsToStoOutResultService.resetFailCount id:{},error:{}",
                            "SgBWmsToStoOutResultService.resetFailCount"), id, Throwables.getStackTraceAsString(e));
                    failCount++;
                }
            }
        }
        return ValueHolderUtils.getSuccessValueHolder("重置失败次数成功" + successCount + "条，失败" + failCount + "条");
    }

    /**
     * 重置失败次数（单条）
     *
     * @param id
     * @param user
     */
    private void executeResetFailCount(Long id, User user) {
        OcBRefundInTask ocBRefundInTask = mapper.selectById(id);
        if (YesNoEnum.N.getKey().equals(ocBRefundInTask.getIsactive())) {
            throw new NDSException("记录已作废！");
        }
        if (!(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED == ocBRefundInTask.getBillStatus()
                && 6 == ocBRefundInTask.getFailedCount())) {
            throw new NDSException("仅转化失败且转化次数等于6才允许重置失败次数！");
        }
        OcBRefundInTask update = new OcBRefundInTask();
        update.setId(ocBRefundInTask.getId());
        update.setFailedCount(0);
        OmsModelUtil.setDefault4Upd(update, user);
        int count = mapper.updateById(update);
        if (count <= 0) {
            throw new NDSException("更新失败！");
        }
    }

    /**
     * 作废
     *
     * @param session
     * @return
     */
    public ValueHolder voidData(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.info(LogUtil.format("OcBRefundInTaskThirdService.voidData param:{},user:{}",
                        "OcBRefundInTaskThirdService.voidData"),
                param.toJSONString(), JSONObject.toJSONString(user));
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException("请先选择需要作废的记录！");
        }
        int successCount = 0;
        int failCount = 0;
        if (ids.size() == 1) {
            executeVoidData(ids.getLong(0), user);
            return ValueHolderUtils.getSuccessValueHolder("作废成功！");
        } else {
            for (int i = 0; i < ids.size(); i++) {
                Long id = ids.getLong(i);
                try {
                    executeVoidData(id, user);
                    successCount++;
                } catch (Exception e) {
                    log.warn(LogUtil.format("SgBWmsToStoOutResultService.voidData id:{},error:{}",
                            "SgBWmsToStoOutResultService.voidData"), id, Throwables.getStackTraceAsString(e));
                    failCount++;
                }
            }
        }
        return ValueHolderUtils.getSuccessValueHolder("作废成功" + successCount + "条，失败" + failCount + "条");
    }

    /**
     * 作废（单条）
     *
     * @param id
     * @param user
     */
    private void executeVoidData(Long id, User user) {
        OcBRefundInTask ocBRefundInTask = mapper.selectById(id);
        if (YesNoEnum.N.getKey().equals(ocBRefundInTask.getIsactive())) {
            throw new NDSException("记录已作废！");
        }
        if (!(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED == ocBRefundInTask.getBillStatus()
                && 6 == ocBRefundInTask.getFailedCount())) {
            throw new NDSException("仅转化失败且转化次数等于6才允许作废！");
        }
        OcBRefundInTask update = new OcBRefundInTask();
        update.setId(ocBRefundInTask.getId());
        update.setIsactive(YesNoEnum.N.getKey());
        OmsModelUtil.setDefault4Upd(update, user);
        int count = mapper.updateById(update);
        if (count <= 0) {
            throw new NDSException("更新失败！");
        }
    }

    /**
     * 标记为转化
     *
     * @param session
     * @return
     */
    public ValueHolder markUnconverted(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        log.info(LogUtil.format("OcBRefundInTaskThirdService.markUnconverted param:{},user:{}",
                        "OcBRefundInTaskThirdService.markUnconverted"),
                param.toJSONString(), JSONObject.toJSONString(user));
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException("请先选择需要标记为未转化的记录！");
        }
        int successCount = 0;
        int failCount = 0;
        if (ids.size() == 1) {
            executeMarkUnconverted(ids.getLong(0), user);
            return ValueHolderUtils.getSuccessValueHolder("标记为未转化成功！");
        } else {
            for (int i = 0; i < ids.size(); i++) {
                Long id = ids.getLong(i);
                try {
                    executeMarkUnconverted(id, user);
                    successCount++;
                } catch (Exception e) {
                    log.warn(LogUtil.format("SgBWmsToStoOutResultService.markUnconverted id:{},error:{}",
                            "SgBWmsToStoOutResultService.markUnconverted"), id, Throwables.getStackTraceAsString(e));
                    failCount++;
                }
            }
        }
        return ValueHolderUtils.getSuccessValueHolder("标记为未转化成功" + successCount + "条，失败" + failCount + "条");
    }

    /**
     * 标记为转化（单条）
     *
     * @param id
     * @param user
     */
    private void executeMarkUnconverted(Long id, User user) {
        OcBRefundInTask ocBRefundInTask = mapper.selectById(id);
        if (YesNoEnum.Y.getKey().equals(ocBRefundInTask.getIsactive())) {
            throw new NDSException("仅作废状态允许标记为未转化！");
        }
        Integer selectCount = mapper.selectCount(new LambdaQueryWrapper<OcBRefundInTask>()
                .eq(OcBRefundInTask::getWmsBillNo, ocBRefundInTask.getWmsBillNo())
                .eq(OcBRefundInTask::getIsactive, YesNoEnum.Y.getKey()));
        if (selectCount > 0) {
            throw new NDSException("存在同WMS单号且未作废单据不允许标记为未转化！");
        }
        OcBRefundInTask update = new OcBRefundInTask();
        update.setId(ocBRefundInTask.getId());
        update.setIsactive(YesNoEnum.Y.getKey());
        update.setBillStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
        update.setFailedCount(0);
        update.setFailedReason("");
        OmsModelUtil.setDefault4Upd(update, user);
        int count = mapper.updateById(update);
        if (count <= 0) {
            throw new NDSException("更新失败！");
        }
    }

    public void resetFailCount() {
        try {
            // 创建更新对象
            OcBRefundInTask update = new OcBRefundInTask();
            update.setFailedCount(0);
            update.setModifieddate(new Date());

            // 执行更新操作
            mapper.update(update, new LambdaUpdateWrapper<OcBRefundInTask>()
                    .eq(OcBRefundInTask::getBillStatus, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED)
                    .eq(BaseModel::getIsactive, IsActiveEnum.Y.getKey())
                    .ge(OcBRefundInTask::getFailedCount, SgConstantsIF.FAIL_COUNT));
        } catch (Exception e) {
            log.error(LogUtil.format("OcBRefundInTaskThirdService.resetFailCount error:{}",
                    "OcBRefundInTaskThirdService.resetFailCount"), Throwables.getStackTraceAsString(e));
        }
    }

}
