package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoJxOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoJxOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPaymentMapper;
import com.jackrain.nea.oc.oms.model.enums.BackflowStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.TaobaoJxOrderTransferUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.BllSystemParameterKeyResources;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description 淘宝经销订单逻辑处理服务类
 **/
@Component
@Slf4j
public class IpTaobaoJxOrderService {
    @Autowired
    private IpBTaobaoJxOrderMapper ipBTaobaoJxOrderMapper;
    @Autowired
    private IpBTaobaoJxOrderItemMapper ipBTaobaoJxOrderItemMapper;
    @Autowired
    private TaobaoJxOrderTransferUtil taobaoJxOrderTransferUtil;
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private OcBOrderLinkService orderLinkService;
    @Autowired
    private OcBOrderPaymentMapper orderPaymentMapper;
    @Autowired
    private OmsOrderCheckAndUpdateService omsOrderCheckAndUpdateService;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;


    private static final int OMS_ORDER_REDIS_TIMEOUT = 24 * 60 * 60 * 1000;

    /**
     * @return com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation
     * <AUTHOR>
     * @Description 查询淘宝经销订单
     * @Date 2019-7-10
     * @Param [orderNo]
     **/
    public IpTaobaoJxOrderRelation selectTaobaoJxOrder(String orderNo) {
        IpBTaobaoJxOrder orderInfo = this.ipBTaobaoJxOrderMapper.selectTaobaoJxOrderByOrderSn(orderNo);

        if (orderInfo == null) {
            return null;
        }
        IpTaobaoJxOrderRelation taobaoJxOrderRelation = new IpTaobaoJxOrderRelation();
        long jxOrderId = orderInfo.getId();
        List<IpBTaobaoJxOrderItemEx> orderItemList = this.ipBTaobaoJxOrderItemMapper.selectOrderItemList(jxOrderId);

        taobaoJxOrderRelation.setTaobaoJxOrder(orderInfo);
        taobaoJxOrderRelation.setIpBTaobaoJxOrderItemExList(orderItemList);

        return taobaoJxOrderRelation;
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新淘宝经销订单主表转换状态
     * @Date 2019-7-4
     * @Param [transferOrderStatus, sysRemark, ipBTaobaoJxOrder]
     **/
    public boolean updateIsTrans(TransferOrderStatus transferOrderStatus, String sysRemark, IpBTaobaoJxOrder ipBTaobaoJxOrder) {
        IpTaobaoJxOrderService bean = ApplicationContextHandle.getBean(IpTaobaoJxOrderService.class);
        boolean flag = bean.updateJxOrder(transferOrderStatus.toInteger(), sysRemark, ipBTaobaoJxOrder);
        return flag;
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新方法
     * @Date 2019-7-4
     * @Param [istrans, sysRemark, ipBTaobaoJxOrder]
     **/
    @Transactional(rollbackFor = Exception.class)
    public boolean updateJxOrder(int istrans, String sysRemark, IpBTaobaoJxOrder ipBTaobaoJxOrder) {
        try {

            int i = ipBTaobaoJxOrderMapper.updateIsTrans(ipBTaobaoJxOrder.getDealerOrderId().toString(), istrans, true, sysRemark);
            //推送es
            if (i > 0) {
//                ipBTaobaoJxOrder.setIstrans(istrans);
//                ipBTaobaoJxOrder.setSysremark(sysRemark);
//                SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.IP_B_TAOBAO_JX_ORDER_INDEX_NAME,
//                        OcElasticSearchIndexResources.IP_B_TAOBAO_JX_ORDER_TYPE_NAME,
//                        ipBTaobaoJxOrder, ipBTaobaoJxOrder.getId());
                return true;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新淘宝经销商订单中间表失败,DealerOrderId:{}", ipBTaobaoJxOrder.getDealerOrderId(), e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 更新异常转换状态
     * @Date 2019-6-26
     * @Param [ipBJitxOrder, error]
     **/
    public boolean updateIsTransError(IpBTaobaoJxOrder ipBTaobaoJxOrder, String error) {
        IpTaobaoJxOrderService bean = ApplicationContextHandle.getBean(IpTaobaoJxOrderService.class);
        String sysRemark = SysNotesConstant.SYS_REMARK0;
        //异常信息超过500 截取500
        String str = sysRemark + error;
        if (str.length() > 500) {
            str = str.substring(0, 500);
        }
        boolean flag = bean.updateJxOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), str, ipBTaobaoJxOrder);
        return flag;
    }

    /**
     * @return com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation
     * <AUTHOR>
     * @Description 转换后的bean
     * @Date 2019-7-9
     * @Param [taobaoJxOrderRelation]
     **/
    public OcBOrderRelation convertOrder(IpTaobaoJxOrderRelation taobaoJxOrderRelation) {
        return taobaoJxOrderTransferUtil.taobaoJxOrderToOmsOrder(taobaoJxOrderRelation);
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description 新增订单保存
     * @Param [orderInfo, taobaoJxOrderRelation, isHistoryOrder, operateUser]
     **/
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOmsOrderInfo(OcBOrderRelation orderInfo,
                                    boolean isHistoryOrder, User operateUser) {
        if (orderInfo == null) {
            return false;
        }
        if (orderInfo.getOrderInfo() == null) {
            return false;
        }
        if (log.isDebugEnabled()) {
            log.debug("Start SaveOrder");
        }
        int result = orderMapper.insert(orderInfo.getOrderInfo());
        if (orderInfo.getOrderItemList() != null) {
            for (OcBOrderItem orderItem : orderInfo.getOrderItemList()) {
                orderItem.setOcBOrderId(orderInfo.getOrderInfo().getId());
                orderItemMapper.insert(orderItem);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("Start SaveOrder OrderFinkLog");
        }
        if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
            List<OcBOrder> ocBOrders = new ArrayList<>();
            ocBOrders.add(orderInfo.getOrderInfo());
            this.orderLinkService.addOrderFinkLogsThread(ocBOrders, BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
        }
        if (log.isDebugEnabled()) {
            log.debug("Start SaveOrder OrderLog");
        }
        if (isHistoryOrder) {
            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "历史单据新增订单成功", "", "", operateUser);
        } else {
            orderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(),
                    OrderLogTypeEnum.ORDER_ADD.getKey(), "新增订单成功", "", "", operateUser);
        }

        //明细插入成功后，调用金额平摊服务（详情请查看金额平摊服务PRD）
        omsOrderCheckAndUpdateService.doCheckAndUpdateBlanceMoney(orderInfo);
        //订单支付信息
        orderPaymentMapper.insert(orderInfo.getOrderPaymentList().get(0));
//        if (!SpecialElasticSearchUtil.indexExists(OcElasticSearchIndexResources.OC_B_ORDER_PAYMENT_TYPE_NAME)) {
//            try {
//                SpecialElasticSearchUtil.indexCreate(OcBOrderPayment.class);
//                SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_PAYMENT_INDEX_NAME
//                        , OcElasticSearchIndexResources.OC_B_ORDER_PAYMENT_TYPE_NAME, orderInfo.getOrderPaymentList().get(0)
//                        , orderInfo.getOrderPaymentList().get(0).getId());
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
        log.debug("日志服务：订单支付信息成功");
        if (result > 0) {
            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(orderInfo.getOrderInfo().getId());
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
//            String indexName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;
//            try {
//                SpecialElasticSearchUtil.indexDocument(indexName, OC_B_ORDER_TYPE_NAME,
//                        orderInfo.getOrderInfo(), orderInfo.getOrderInfo().getId());
//                if (orderInfo.getOrderItemList() != null && orderInfo.getOrderItemList().size() > 0) {
//                    SpecialElasticSearchUtil.indexDocuments(indexName, OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME,
//                            orderInfo.getOrderItemList(),
//                            "OC_B_ORDER_ID");
//                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }


            // Redis推送。主要是为了防止ES在存储数据时，会延迟100ms左右。为了保障不重复转单，在Redis进行存储。
            // 在进行查询时，先查询ES，若ES不存在，再查询Redis。
            String redisKey = BllRedisKeyResources.getOmsOrderKey(orderInfo.getOrderInfo().getSourceCode());
            CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
            objRedisTemplate.opsForValue().set(redisKey, orderInfo.getOrderId(), OMS_ORDER_REDIS_TIMEOUT,
                    TimeUnit.MILLISECONDS);
        }
        return (result > 0);
    }
}
