package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillVoidResult;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.ExchangeAgreeModel;
import com.jackrain.nea.ip.model.ExchangeRefuseModel;
import com.jackrain.nea.ip.model.IpCTaobaoProductItem;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoExchangeOrderExt;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.psext.model.table.ExtractLuckyBag;
import com.jackrain.nea.psext.model.table.SingleProInfo;
import com.jackrain.nea.psext.model.table.SplitVirtualPro;
import com.jackrain.nea.psext.request.VirtualProSplitRequest;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCExchangeStrategyOrderDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR> 孙勇生
 * create at:  19/3/7  15:35
 * @description: 中间表淘宝换货处理服务
 */
@Slf4j
@Component
public class IpTaobaoExchangeService {
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private SgRpcService sgRpcervice;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderOffService ocBOrderOffService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsReturnOrderService omsReturnOrderService;
    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;
    @Autowired
    private IpBTaobaoExchangeMapper ipBTaobaoExchangeMapper;
    @Autowired
    private OmsOrderCancellationService omsOrderCancellationService;
    @Autowired
    private OcBReturnOrderExchangeMapper ocBReturnOrderExchangeMapper;
    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;
    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private MarkRefundService markRefundService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
//    @Autowired
//    protected R3MqSendHelper r3MqSendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    /**
     * 获取实例
     *
     * @return
     */
    public static IpTaobaoExchangeService getInstance() {
        return ApplicationContextHandle.getBean(IpTaobaoExchangeService.class);
    }

    /**
     * 查询换货相关数据
     *
     * @param orderNo 换货中间表换货订单号
     * @return 换货相关组合信息
     */
    public IpTaobaoExchangeRelation selectTaobaoExchangeRelation(String orderNo) {
        IpTaobaoExchangeRelation ipTaobaoExchangeRelation = new IpTaobaoExchangeRelation();
        try {
            IpBTaobaoExchange taoBaoExchange = ipBTaobaoExchangeMapper.selectTaobaoExchangeByDisputeId(orderNo);
            if (null != taoBaoExchange) {
                ipTaobaoExchangeRelation.setTaobaoExchange(taoBaoExchange);
                //通过正向订单id查询到订单明细表中的OOID
                Long bizOrderId = taoBaoExchange.getBizOrderId();

                JSONArray returnData = ES4Order.getIdsByItemOoId(taoBaoExchange.getBizOrderId());

                if (CollectionUtils.isEmpty(returnData)) {
                    return ipTaobaoExchangeRelation;
                }
                //获取原单的明细
                this.selectOrder(returnData, ipTaobaoExchangeRelation, bizOrderId);
                //根据中间表平台单号查询是否有已转换的的主表数据
                // 这个查询应该在转单步骤中查询，不应该在这里查询（应该在锁的范围内）
                //this.selectOrderExchange(ipTaobaoExchangeRelation);
            }

        } catch (Exception e) {
            log.error(this.getClass().getName() + ".查询换货数据异常,中间表:DisputeId :{}", orderNo, e);
        }
        return ipTaobaoExchangeRelation;
    }

    /**
     * 查询原单信息及商品明细
     *
     * @param returnData               ES查询的数据
     * @param ipTaobaoExchangeRelation 封装的实体
     * @param bizOrderId               正向订单id
     */
    private void selectOrder(JSONArray returnData,
                             IpTaobaoExchangeRelation ipTaobaoExchangeRelation,
                             Long bizOrderId) {
        IpBTaobaoExchange taoBaoExchange = ipTaobaoExchangeRelation.getTaobaoExchange();
        List<Long> orderIdList = new ArrayList<>();
        for (Object returnDatum : returnData) {
            JSONObject jsonObject = (JSONObject) returnDatum;
            Long orderId = jsonObject.getLong("OC_B_ORDER_ID");
            orderIdList.add(orderId);
        }
        //获取原单的明细表分库键
        //通过id查询是否有原单信息
        List<OcBOrder> afterDeliveryOrderList = new ArrayList<>();
        for (Long orderId : orderIdList) {
            OcBOrder afterDeliveryOrderInfo = ocBOrderMapper.selectByIdAndOrderStatus(orderId,
                    OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger(),
                    OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            if (afterDeliveryOrderInfo != null) {
                afterDeliveryOrderList.add(afterDeliveryOrderInfo);
            }
        }

        if (CollectionUtils.isEmpty(afterDeliveryOrderList)) {
            return;
        }

        OcBOrder originalValidOrderInfo = afterDeliveryOrderList.get(0);
        ipTaobaoExchangeRelation.setOriginalValidOrderInfo(originalValidOrderInfo);
        //通过分库键及OOID查询原单明细(是组合商品时对应多条)
        //liqb 更改ooid类型从Long类型改成String类型
        List<OcBOrderItem> originalOrderItemList =
                ocBOrderItemMapper.selectByOrderIdAndOOId(originalValidOrderInfo.getId(),
                        String.valueOf(bizOrderId));

        if (originalOrderItemList != null) {
            ipTaobaoExchangeRelation.setOriginalOrderItemList(originalOrderItemList);
        }

        ProductSku productSku = null;
        //先通过自定义sku进行匹配
        if (!StringUtils.isEmpty(taoBaoExchange.getOuterSkuId())) {
            String outerSkuId = taoBaoExchange.getOuterSkuId();
            productSku = psRpcService.selectProductSku(outerSkuId);
        } else {
            //通过换货商品的sku判断换货商品是否存在
            //对应淘宝运枢纽商品表的skuid；由于换货信息下发的换货商品信息是淘宝平台的商品ID，因此需要到商品中间表进行查询对应的R3系统商品SKUID
            String exchangeSku = taoBaoExchange.getExchangeSku();
            IpCTaobaoProductItem taobaoProductItem = cpRpcService.selectIpCTaobaoProductItemBySkuId(exchangeSku);
            if (taobaoProductItem != null) {
                //对应sku表真实的skuid
                String outerId = taobaoProductItem.getOuterId();
                // 查询换货的sku信息
                productSku = psRpcService.selectProductSku(outerId);
                taoBaoExchange.setOuterSkuId(outerId);
            }
        }

        setExchangeProductDetailList(productSku, ipTaobaoExchangeRelation, taoBaoExchange);
    }

    /**
     * 换货商品的sku赋值
     *
     * @param productSku
     * @param ipTaobaoExchangeRelation
     * @param taobaoExchange
     */
    private void setExchangeProductDetailList(ProductSku productSku,
                                              IpTaobaoExchangeRelation ipTaobaoExchangeRelation,
                                              IpBTaobaoExchange taobaoExchange) {
        List<ProductSku> exchangeSkuList = null;
        if (productSku != null) {
            if (ObjectUtils.isEmpty(productSku.getPricelist()) || productSku.getPricelist().compareTo(BigDecimal.ZERO) == 0) {
                log.info("当前换货转单查询商品吊牌价为0，请先维护价格:{}", taobaoExchange.getDisputeId());
            }

            ipTaobaoExchangeRelation.setExchangeSku(productSku.getSkuEcode());
            taobaoExchange.setOuterSkuId(productSku.getSkuEcode());
            //判断是否为组合商品
            int skuType = productSku.getSkuType();
            if (SkuType.COMBINE_PRODUCT == skuType) {
                Long qty = taobaoExchange.getQty();
                //为组合商品；组合商品的虚拟条码
                String skuEcode = productSku.getSkuEcode();
                exchangeSkuList = this.selectGroupPro(skuEcode, qty, taobaoExchange.getId());
                ipTaobaoExchangeRelation.setIsGroupGoods(true);
            } else if (SkuType.GIFT_PRODUCT == skuType) {
                ipTaobaoExchangeRelation.setIsFortuneBag(true);
            } else {
                exchangeSkuList = new ArrayList<>();
                exchangeSkuList.add(productSku);
            }
            ipTaobaoExchangeRelation.setExchangeProductDetailList(exchangeSkuList);
            ipTaobaoExchangeRelation.setTaobaoExchange(taobaoExchange);
        }
    }

    /**
     * 根据组合商品SKU编码查询对应的组合商品信息列表
     *
     * @param skuEcode 组合商品SKU编码
     * @param qty      组合商品数量
     * @return 组合商品详细商品信息列表
     */
    public List<ProductSku> selectGroupPro(String skuEcode, Long qty, Long exchangeId) {
        VirtualProSplitRequest proRequest = new VirtualProSplitRequest();
        List<SplitVirtualPro> splitGroupPros = new ArrayList<>();
        SplitVirtualPro groupPro = new SplitVirtualPro();
        groupPro.setVirtualSku(skuEcode);
        groupPro.setNum(qty.intValue());
        groupPro.setItemId(exchangeId);
        splitGroupPros.add(groupPro);
        proRequest.setSplitVirtualProList(splitGroupPros);

        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 虚拟条码获取真实条码返回入参{}", proRequest);
        }
        ValueHolderV14<Map<String, List<ExtractLuckyBag>>> holder = psRpcService.selectGroupPro(proRequest);
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 虚拟条码获取真实条码返回参数参{}", holder);
        }
        if (holder.getCode() == -1) {
            //调用错误 结束程序 将错误信息更新至系统备注
            log.error(this.getClass().getName() + " 调用库存中心通过虚拟条码拿取真实的sku信息出错", holder.getMessage());
            throw new NDSException("调用库存中心通过虚拟条码拿取真实的sku信息出错");
        }
        Map<String, List<ExtractLuckyBag>> dataMap = holder.getData();
        if (!dataMap.containsKey(skuEcode + exchangeId)) {
            log.error(this.getClass().getName() + " 调用虚拟条码拿不到真实的sku信息", holder.getMessage());
            throw new NDSException("调用虚拟条码拿不到真实的sku信息");
        }
        List<ExtractLuckyBag> extractLuckyBags = dataMap.get(skuEcode + exchangeId);
        List<ProductSku> list = new ArrayList<>();
        for (ExtractLuckyBag extractLuckyBag : extractLuckyBags) {
            List<SingleProInfo> extractLuckyBag1 = extractLuckyBag.getExtractLuckyBag();
            for (SingleProInfo singleProInfo : extractLuckyBag1) {
                String productCode = singleProInfo.getPsCSkuEcode();
                BigDecimal num = singleProInfo.getNum();
                ProductSku productSku = psRpcService.selectProductSku(productCode);
                productSku.setNum(num);
                list.add(productSku);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 组合商品真实商品:{}", JSONObject.toJSONString(list));
        }
        return list;
    }

    /**
     * 查询是否退单信息
     *
     * @param ipTaobaoExchangeRelation 封装返回参数对象
     */
    public void selectOrderExchange(IpTaobaoExchangeRelation ipTaobaoExchangeRelation) {
        //根据中间表平台单号查询是否有已转换的的主表数据
        IpBTaobaoExchange taobaoExchange = ipTaobaoExchangeRelation.getTaobaoExchange();
        Long disputeId = taobaoExchange.getDisputeId();

        //根據ooid查询 原换货单id
        List<Long> oriOrderIdList = ES4Order.findIdsByOid(disputeId);
        if (CollectionUtils.isEmpty(oriOrderIdList)) {
            // bug-prd-286 换货单重复转单报空指针问题 加一层从redis查
            Long orderId = OmsOrderService.getOmsOrderToRedisForExchange(disputeId);

            if (Objects.nonNull(orderId) && orderId.longValue() > 0) {
                oriOrderIdList = new ArrayList<>();
                oriOrderIdList.add(orderId);
            } else {
                // bug-prd-286-again@20200804 查询出结果，需要判断，不能直接返回
                return;
            }
        }

        List<OcBOrder> ocBOrders = ocBOrderMapper.selectOrderListByIds(oriOrderIdList);
        if (CollectionUtils.isEmpty(ocBOrders)) {
            return;
        }
        //过滤已取消的和作废的
        Iterator<OcBOrder> iterator = ocBOrders.iterator();
        while (iterator.hasNext()) {
            OcBOrder next = iterator.next();
            if (next.getOrderStatus().intValue() == OmsOrderStatus.CANCELLED.toInteger()
                    || next.getOrderStatus().intValue() == OmsOrderStatus.SYS_VOID.toInteger()) {
                iterator.remove();
            }
        }
        if (CollectionUtils.isEmpty(ocBOrders)) {
            return;
        }
        ipTaobaoExchangeRelation.setAfterExchangeOrder(ocBOrders);

        //先查询redis再查询es
        long returnOrderId = this.selectOmsReturnOrderFromRedis(disputeId);
        if (returnOrderId == 0) {
            returnOrderId = ES4ReturnOrder.findIdByTbDisputeId(disputeId);
            if (log.isDebugEnabled()) {
                log.debug("SelectOrderExchange.DisputeId={};Result={}", disputeId, returnOrderId);
            }
        }
        if (returnOrderId == 0) {
            return;
        }
        OcBReturnOrder findReturnOrderInfo = ocBReturnOrderMapper.selectById(returnOrderId);
        ipTaobaoExchangeRelation.setReturnOrderInfo(findReturnOrderInfo);
    }

    /**
     * 查询订单是否存在Redis中
     *
     * @param disputeId 原始订单号
     * @return 新生成退换货订单ID
     */
    private long selectOmsReturnOrderFromRedis(Long disputeId) {
        if (Objects.nonNull(disputeId)) {
            String redisKey = BllRedisKeyResources.getOmsReturnOrderReturnIdKey(disputeId.toString());
            CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
            Boolean hasKey = objRedisTemplate.hasKey(redisKey);
            if (hasKey != null && hasKey) {
                Long value = objRedisTemplate.opsForValue().get(redisKey);
                if (Objects.nonNull(value)) {
                    return value;
                }
            }
        }

        return 0L;
    }

    /**
     * 换货关闭(4),请退款(14)
     *
     * @param ipTaobaoExchangeRelation 换货单中间表信息
     * @param operateUser              操作用户
     */
    public void exchangeStatusCloseAndRefund(IpTaobaoExchangeRelation ipTaobaoExchangeRelation,
                                             User operateUser) {
        IpBTaobaoExchange taobaoExchange = ipTaobaoExchangeRelation.getTaobaoExchange();
        try {
            //订单是否取消成功的标识
            boolean flag = false;
            //需要添加的系统备注
            String sysRemark = null;
            //订单类型=换货的零售发货单
            List<OcBOrder> exchangeOrderList = ipTaobaoExchangeRelation.getAfterExchangeOrder();
            if (CollectionUtils.isEmpty(exchangeOrderList)) {
                //不存在换货订单  更新状态为已装换
                String remark = SysNotesConstant.SYS_REMARK11;
                this.updateExchangeRemarkAndIsTrans(
                        TransferOrderStatus.TRANSFERRED.toInteger(), remark, taobaoExchange);
                return;
            }

            int index = 0;
            for (OcBOrder exchangeOrder : exchangeOrderList) {
                Long orderId = exchangeOrder.getId();
                //换货订单的wms状态
                Integer wmsCancelStatus = exchangeOrder.getWmsCancelStatus();
                Integer orderStatus = exchangeOrder.getOrderStatus();

                //查询订单明细信息
                List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectAllOrderItem(exchangeOrder.getId());
                if (CollectionUtils.isEmpty(ocBOrderItems)) {
                    continue;
                }

                //是否取消部分
                Boolean isCancelPart = false;
                Long itemId = null;
                if (ocBOrderItems.size() > 1) {
                    //换货零售单明细大于2  说明做了合单操作 需要取消部分明细
                    //找出换货的明细
                    for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                        if (!ObjectUtils.isEmpty(ocBOrderItem.getOoid()) && ocBOrderItem.getOoid().equals(taobaoExchange.getDisputeId().toString())) {
                            itemId = ocBOrderItem.getId();
                        }
                    }
                    if (itemId == null) {
                        throw new NDSException("换货关闭请退款，数据查询合单明细商品找不到DisputeId:" + taobaoExchange.getDisputeId());
                    }
                    //取消明细
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("IDS", itemId);
                    ValueHolderV14 vh = markRefundService.markRefund(jsonObject, SystemUserResource.getRootUser());
                    int code = Tools.getInt(vh.getCode(), -1);
                    if (code == 0) {
                        flag = true;
                        //获取换货订单的id
                        omsOrderLogService.addUserOrderLog(exchangeOrder.getId(), exchangeOrder.getBillNo(),
                                OrderLogTypeEnum.TB_EXCHANGE_CONVERT.getKey(),
                                SysNotesConstant.VOID_ORDER_LOG_MESSAGE, null, null, operateUser);
                    }
                    sysRemark = SysNotesConstant.SYS_REMARK15;
                }

                // 订单状态 = 缺货 或者 未审核 或者 已审核 时，需要作废换货生成的零售发货单
                if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)
                        || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                        || OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)) {
                    ValueHolderV14<Object> holder = new ValueHolderV14<>();
                    //是否取消部分
                    if (isCancelPart) {
                        //取消明细
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("IDS", itemId);
                        holder = markRefundService.markRefund(jsonObject, SystemUserResource.getRootUser());
                    } else {
                        //调用订单作废服务
                        holder = omsOrderCancellationService.doInvoildOutOrder(exchangeOrder,
                                operateUser);
                    }
                    int code = holder.getCode();
                    if (code == 0) {
                        flag = true;
                        //获取换货订单的id
                        omsOrderLogService.addUserOrderLog(exchangeOrder.getId(), exchangeOrder.getBillNo(),
                                OrderLogTypeEnum.TB_EXCHANGE_CONVERT.getKey(),
                                SysNotesConstant.VOID_ORDER_LOG_MESSAGE, null, null, operateUser);
                    }
                    sysRemark = SysNotesConstant.SYS_REMARK15;
                } else if (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)
                        && OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == wmsCancelStatus) {
                    // 订单状态=配货中 并且 WMS状态= 已撤销【撤销成功】

                    //是否取消部分
                    if (isCancelPart) {
                        //取消明细
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("IDS", itemId);
                        ValueHolderV14<Object> holder = markRefundService.markRefund(jsonObject,
                                SystemUserResource.getRootUser());
                        int code = holder.getCode();
                        if (code == 0) {
                            flag = true;
                        }
                    } else {
                        // 取消订单，并生成负向付款信息
                        flag = ocBOrderOffService.startCancelOrderByLock(operateUser, orderId,
                                SysNotesConstant.CANCEL_ORDER_LOG_TYPE,
                                SysNotesConstant.CANCEL_ORDER_LOG_MESSAGE);
                    }
                    sysRemark = SysNotesConstant.SYS_REMARK15;
                } else if (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus) &&
                        (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger() == wmsCancelStatus
                                || OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger() == wmsCancelStatus)) {
                    // 配货中，并且WMS状态 = 未撤销 或者 WMS状态 = 撤销失败；作废出库通知单
                    // 如果作废出库通知单失败，则标记为拦截；如果作废出库通知单成功，则取消订单
                    List<OcBOrder> ocBOrderList = new ArrayList<>();
                    ocBOrderList.add(exchangeOrder);
                    ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> holder = sgRpcervice.invoildOutgoingNotice(ocBOrderList,
                            operateUser,true);
                    int code = holder.getCode();
                    if (code == 0) {
                        List<SgBStoOutNoticesBillVoidResult> data = holder.getData();
                        Integer code1 = data.get(0).getCode();
                        if (code1 == 0) {
                            //是否取消部分
                            if (isCancelPart) {
                                //取消明细
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("IDS", itemId);
                                ValueHolderV14<Object> holder2 = markRefundService.markRefund(jsonObject,
                                        SystemUserResource.getRootUser());
                                int code2 = holder2.getCode();
                                if (code2 == 0) {
                                    flag = true;
                                }
                            } else {
                                // 取消订单，并生成负向付款信息
                                flag = ocBOrderOffService.startCancelOrderByLock(operateUser, orderId,
                                        SysNotesConstant.CANCEL_ORDER_LOG_TYPE,
                                        SysNotesConstant.CANCEL_ORDER_LOG_MESSAGE);
                            }
                            omsOrderLogService.addUserOrderLog(exchangeOrder.getId(), exchangeOrder.getBillNo(),
                                    OrderLogTypeEnum.WMS_CANCLE_SUCCESS.getKey(), "换货订单WMS撤回成功!",
                                    null, null, operateUser);
                        } else {
                            //拦截订单   更新为已拦截 修改hold单状态 使用HOLD单方法修改
                            exchangeOrder.setIsInterecept(1);// 更新为已拦截 修改hold单状态 使用HOLD单方法修改
                            ocBOrderHoldService.holdOrUnHoldOrder(exchangeOrder, OrderHoldReasonEnum.REFUND_HOLD);
                            omsOrderLogService.addUserOrderLog(exchangeOrder.getId(), exchangeOrder.getBillNo(),
                                    OrderLogTypeEnum.TB_EXCHANGE_CONVERT.getKey(),
                                    "换货转退货退款，配货中拦截订单!", null, null, operateUser);
                        }
                    }
                    sysRemark = SysNotesConstant.SYS_REMARK14;
                } else if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                        || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                        || OmsOrderStatus.DEAL_DONE.toInteger().equals(orderStatus)) {
                    //修改换货单状态已转换，添加换货单中间表系统备注：“换货转退货退款，对应的换货单已经发货
                    String remark = SysNotesConstant.SYS_REMARK12;
                    this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, taobaoExchange);
                }

                if (sysRemark != null) {
                    if (flag) {
                        index++;
                    }
                }
            }
            //全部取消
            if (index == exchangeOrderList.size()) {
                //订单取消成功
                boolean isExecute = this.exchangeIsClose(ipTaobaoExchangeRelation, operateUser);
                if (!isExecute) {
                    String remark = SysNotesConstant.SYS_REMARK26;
                    this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), remark,
                            taobaoExchange);
                }
            } else {
                //订单取消失败
                this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.NOT_TRANSFER.toInteger(), sysRemark,
                        taobaoExchange);
            }

        } catch (Exception e) {
            log.error("{} 状态为换货关闭或请退款 逻辑异常", this.getClass().getName(), e);
            throw new NDSException(e);
        }
    }

    /**
     * 换货订单取消成功之后 换货单状态是“换货关闭”，则判断退换货单的状态
     *
     * @param ipTaobaoExchangeRelation
     */
    private boolean exchangeIsClose(IpTaobaoExchangeRelation ipTaobaoExchangeRelation, User operateUser) {
        IpBTaobaoExchange taobaoExchange = ipTaobaoExchangeRelation.getTaobaoExchange();
        Long disputeId = taobaoExchange.getDisputeId();
        String status = ipTaobaoExchangeRelation.getTaobaoExchange().getStatus();
        OcBReturnOrder ocBReturnOrder = ipTaobaoExchangeRelation.getReturnOrderInfo();
        Integer wmsCancelStatus = ocBReturnOrder.getWmsCancelStatus();
        OcBOrder ocBOrder = ipTaobaoExchangeRelation.getOriginalValidOrderInfo();
        Integer returnStatus = ocBReturnOrder.getReturnStatus(); //退换货单的状态
        try {
            //换货关闭
            if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_CLOSE.getName().equals(status) ||
                    TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.WAIT_BUYER_MODIFY.getName().equals(status)) {
                Integer isTowms = ocBReturnOrder.getIsTowms();
                //TaobaoReturnOrderExt.ReturnStatus.WAIT_RETURN_LIBRARY.getCode()
                if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus)) {
                    if (WmsWithdrawalState.NO.toInteger().equals(isTowms)) {
                        // 直接 取消退换货单
                        this.cancelReturnOrder(operateUser, ocBReturnOrder, taobaoExchange, ocBOrder);
                        return true;
                    }
                    if (ToDRPStatusEnum.SUCCESS.getCode().equals(ocBReturnOrder.getToDrpStatus())||WmsWithdrawalState.YES.toInteger().equals(isTowms) &&
                            (OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger() == wmsCancelStatus
                                    || OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger() == wmsCancelStatus)) {
                        //调用撤回wms服务
                        ValueHolderV14 holder = ipRpcService.cancelReturnOrder(ocBReturnOrder, operateUser);
                        int code = Tools.getInt(holder.getCode(), -1);
                        if (code == 0) {
                            //取消退换货单
                            this.cancelReturnOrder(operateUser, ocBReturnOrder, taobaoExchange, ocBOrder);
                        } else {
                            ocBReturnOrder.setRemark("wms撤回失败");
                            returnOrderTransferUtil.setOperateUserInfo(ocBReturnOrder, operateUser);
                            omsReturnOrderService.updateOcBReturnOrder(ocBReturnOrder);
                            this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(),
                                    "wms撤回失败", taobaoExchange);
                        }
                        return true;
                    }
                    //更新
                    updateExchangeIsTransTransferring(taobaoExchange, TransferOrderStatus.NOT_TRANSFER.toInteger());
                    return true;
                }
                //如果退换单的状态为 等待售后确认”
                //TaobaoReturnOrderExt.ReturnStatus.WAIT_AFTERSALE_CONFIRM.getCode()
                if (ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal().equals(returnStatus)
                        || ReturnStatusEnum.COMPLETION.getVal().equals(returnStatus)) {
                    //更新换货单状态为已转换
                    //删除换货单的商品明细   更新为已转换
                    //根据退换货单的id 删除商品明细
                    this.exchangeTurnReturn(ocBReturnOrder, taobaoExchange, operateUser);
                    return true;
                }
                //更新
                updateExchangeIsTransTransferring(taobaoExchange, TransferOrderStatus.NOT_TRANSFER.toInteger());
                return true;
            }
            //如果换货单状态是请退款
            if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.REFUND_PLEASE.getName().equals(status)) {

//                if (TaobaoReturnOrderExt.ReturnStatus.WAIT_RETURN_LIBRARY.getCode().equals(returnStatus)
//                        || TaobaoReturnOrderExt.ReturnStatus.WAIT_AFTERSALE_CONFIRM.getCode().equals(returnStatus)) {
                if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus)
                        || ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal().equals(returnStatus)
                        || ReturnStatusEnum.COMPLETION.getVal().equals(returnStatus)) {
                    //删除换货单的商品明细   更新为已转换
                    //根据退换货单的id 删除商品明细
                    this.exchangeTurnReturn(ocBReturnOrder, taobaoExchange, operateUser);
                    return true;
                }
                //更新
                updateExchangeIsTransTransferring(taobaoExchange, TransferOrderStatus.NOT_TRANSFER.toInteger());
                return true;
            }
            //更新
            updateExchangeIsTransTransferring(taobaoExchange, TransferOrderStatus.NOT_TRANSFER.toInteger());
        } catch (Exception e) {
            log.error("{} 换货单状态是换货关闭逻辑异常", this.getClass().getName(), e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * 依据disputeId查找退单：解决换货单并发问题后段，查询校验阶段
     *
     * @param disputeId
     * @return
     */
    private OcBReturnOrder findOcBReturnOrderByDisputeId(Long disputeId) {
        OcBReturnOrder returnOrder = null;

        if (Objects.nonNull(disputeId)) {
            long returnOrderId = OmsReturnOrderService.selectOmsReturnOrderFromRedisByReturnId(disputeId.toString());

            if (returnOrderId > 0) {
                // 查询到了单据，查数据库
                returnOrder = ocBReturnOrderMapper.selectByid(returnOrderId);
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("依据换货单平台换货单号dispute_id.{}.找到对应的returnOrder为：{}", disputeId, Objects.isNull(returnOrder) ? null
                    : JSONObject.toJSON(returnOrder));
        }

        return returnOrder;
    }

    /**
     * 买家已退货，待收货', '待买家退货'
     *
     * @param ipTaobaoExchangeRelation 淘宝换货中间表对象
     * @param operateUser              操作人员
     * @return 是否需要创建退换单和零售发货单。True=需要创建
     */
    public boolean exchangeStatusIsReceivingGoods(IpTaobaoExchangeRelation ipTaobaoExchangeRelation,
                                                  User operateUser) {
        OcBReturnOrder returnOrderInfo = ipTaobaoExchangeRelation.getReturnOrderInfo();
        IpBTaobaoExchange taobaoExchange = ipTaobaoExchangeRelation.getTaobaoExchange();
        List<OcBOrder> exchangeOrder = ipTaobaoExchangeRelation.getAfterExchangeOrder();
        // @20200802 如果为空，则从redis里面查，解决并发重复生成问题后段：查询校验阶段
        if (Objects.isNull(returnOrderInfo)) {
            returnOrderInfo = findOcBReturnOrderByDisputeId(taobaoExchange.getDisputeId());
        }

        //判断时间是否是三天之前
        if (returnOrderInfo != null) {
            Integer returnStatus = returnOrderInfo.getReturnStatus();

            if (!ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus)) {
                //状态不等于等待退货入库，
                String nameByCode = ReturnStatusEnum.getNameByCode(returnStatus);
                String remark = SysNotesConstant.SYS_REMARK8;
                remark = String.format(remark, nameByCode);
                this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, taobaoExchange);

            } else {
                //查询换货skuID是否和之前相同  不同取消原单 重新生成新单
                List<OcBReturnOrderExchange> ocBReturnOrderExchanges =
                        ocBReturnOrderExchangeMapper.selectByReturnIdList(returnOrderInfo.getId());
                if (CollectionUtils.isEmpty(ocBReturnOrderExchanges)) {
                    this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(),
                            "异常数据,找不到退换货单明细", taobaoExchange);
                } else {
                    if (!ObjectUtils.isEmpty(ocBReturnOrderExchanges.get(0).getExchangeSku()) && !ocBReturnOrderExchanges.get(0).getExchangeSku().equals(taobaoExchange.getExchangeSku())) {
                        //相同不处理  不同重新重新生成新单
                        //调用订单作废服务  这里不会拆单和合单 直接取第一个
                        ValueHolderV14<Object> objectValueHolderV14 =
                                omsOrderCancellationService.doInvoildOutOrder(exchangeOrder.get(0), operateUser);
                        int code = objectValueHolderV14.getCode();
                        if (code == 0) {
                            try {
                                //取消退换货单
                                ValueHolderV14 vhCan = new ValueHolderV14();
                                String s = "[" + returnOrderInfo.getId() + "]";
                                JSONArray jsonarray = JSONArray.parseArray(s);
                                if (log.isDebugEnabled()) {
                                    log.debug("换货转单取消退换单id{}", returnOrderInfo.getId());
                                }
                                ValueHolderV14 valueHolderV14 =
                                        ocCancelChangingOrRefundService.oneOcCancle(SystemUserResource.getRootUser(), vhCan, jsonarray);
                                if (log.isDebugEnabled()) {
                                    log.debug("换货转单取消退换单结果{}", valueHolderV14);
                                }
                            } catch (Exception e) {
                                log.error("退单取消失败", e);
                                throw new NDSException("退单取消失败!");
                            }
                            //重新对sku赋值
                            String exchangeSku = taobaoExchange.getExchangeSku();
                            IpCTaobaoProductItem taobaoProductItem =
                                    cpRpcService.selectIpCTaobaoProductItemBySkuId(exchangeSku);
                            IpTaobaoExchangeService bean =
                                    ApplicationContextHandle.getBean(IpTaobaoExchangeService.class);
                            if (taobaoProductItem != null) {
                                //对应sku表真实的skuid
                                String outerId = taobaoProductItem.getOuterId();
                                // 查询换货的sku信息
                                ProductSku productSku = psRpcService.selectProductSku(outerId);
                                taobaoExchange.setOuterSkuId(outerId);
                                //修改自定义sku
                                bean.updateExchangeOuterSku(outerId, taobaoExchange);
                                setExchangeProductDetailList(productSku, ipTaobaoExchangeRelation, taobaoExchange);
                            } else {
                                //修改自定义sku
                                bean.updateExchangeOuterSku("", taobaoExchange);
                                this.updateExchangeSkuNotExistRemark(ipTaobaoExchangeRelation);
                                return false;
                            }
                            //重新生成退换单和零售发货单
                            return true;
                        }
                    }
                }
            }
            //更新物流单号和名称
            this.updateLogisticsCodeAndName(returnOrderInfo, taobaoExchange, operateUser);
            //更新收货地址   地址一样取第一个
            this.updateReceiveGoodsAddr(returnOrderInfo, taobaoExchange, exchangeOrder.get(0), operateUser);
            //更新手机号 手机号一样取第一个
            this.updateBuyerPhone(returnOrderInfo, taobaoExchange, exchangeOrder.get(0), operateUser);
            updateBuyerName(returnOrderInfo, taobaoExchange, exchangeOrder.get(0));
            this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), "", taobaoExchange);
            return false;
        }
        return true;
    }

    private void updateBuyerName(OcBReturnOrder returnOrderInfo, IpBTaobaoExchange taobaoExchange, OcBOrder ocBOrder) {
        if (Objects.isNull(returnOrderInfo) || Objects.isNull(taobaoExchange) || Objects.isNull(ocBOrder)) {
            return;
        }
        if (!ObjectUtils.isEmpty(taobaoExchange.getBuyerName()) &&
                (ObjectUtils.isEmpty(ocBOrder.getReceiverName()) || !taobaoExchange.getBuyerName().equals(ocBOrder.getReceiverName()))) {
            ocBOrder.setReceiverName(taobaoExchange.getBuyerName());
            omsOrderService.updateOrderInfo(ocBOrder);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.TB_EXCHANGE_CONVERT.getKey(),
                    "换货修改收货人成功", null, null, SystemUserResource.getRootUser());
        }
    }

    /**
     * 更新物流公司名称及单号
     *
     * @param ocBReturnOrder
     * @param taobaoExchange
     */
    private void updateLogisticsCodeAndName(OcBReturnOrder ocBReturnOrder,
                                            IpBTaobaoExchange taobaoExchange,
                                            User operateUser) {
        try {
            String logisticsCode = ocBReturnOrder.getLogisticsCode(); //物流单号
            String buyerLogisticNo = taobaoExchange.getBuyerLogisticNo(); //买家物流单号
            if (StringUtils.isEmpty(logisticsCode) || !logisticsCode.equals(buyerLogisticNo)) {
                //更新退换单表的物流单号  物流公司
                Long id = ocBReturnOrder.getId();
                String buyerLogisticName = taobaoExchange.getBuyerLogisticName(); //买家物流公司名称
                ocBReturnOrder.setId(id);
                ocBReturnOrder.setLogisticsCode(buyerLogisticNo);
                ocBReturnOrder.setCpCLogisticsEname(buyerLogisticName);
                returnOrderTransferUtil.setLogisticInfo(ocBReturnOrder, buyerLogisticName);
                returnOrderTransferUtil.updateOperator(ocBReturnOrder, operateUser);
                //加入“空运单号延迟推单有效时间”字段
                ocBReturnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(ocBReturnOrder));
                omsReturnOrderService.updateOcBReturnOrder(ocBReturnOrder);
            }
        } catch (IOException e) {
            log.error(this.getClass().getName() + " 更新退换货订单物流公司名称及单号异常", e);
            throw new NDSException(e);
        }
    }

    /**
     * 更新收货地址
     *
     * @param ocBReturnOrder
     * @param taobaoExchange
     * @param exchangeOrder
     * @param operateUser
     */
    private void updateReceiveGoodsAddr(OcBReturnOrder ocBReturnOrder,
                                        IpBTaobaoExchange taobaoExchange,
                                        OcBOrder exchangeOrder,
                                        User operateUser) {
        try {
            //获取退换货订单的买家地址
            String receiveAddress = ocBReturnOrder.getReceiveAddress();
            //退款中间表的买家换货地址
            String buyerAddress = taobaoExchange.getBuyerAddress();
            if (StringUtils.isEmpty(receiveAddress) || !receiveAddress.equals(buyerAddress)) {
                //跟新退换货订单的地址   和换货订单的地址
                ocBReturnOrder.setReceiveAddress(buyerAddress);
                ocBReturnOrder.setModifieddate(new Date());
                if (!StringUtils.isEmpty(taobaoExchange.getBuyerName())) {
                    ocBReturnOrder.setReceiveName(taobaoExchange.getBuyerName());
                }
                returnOrderTransferUtil.returnOrderAddress(ocBReturnOrder, buyerAddress);
                //ocBReturnOrderMapper.updateById(returnOrder);
                returnOrderTransferUtil.updateOperator(ocBReturnOrder, operateUser);
                omsReturnOrderService.updateOcBReturnOrder(ocBReturnOrder);
                //更新换货订单的地址
                // returnOrderTransferUtil.orderAddress(exchangeOrder, buyerAddress);
                exchangeOrder.setReceiverAddress(buyerAddress.replaceAll(",", "::::"));
                //returnOrderTransferUtil.updateOperator(exchangeOrder, operateUser);
                updateOrderAddressByExchange(exchangeOrder, buyerAddress);
                //omsOrderService.updateOrderInfo(exchangeOrder);
                //添加日志
                log.debug("退换货id修改地址" + ocBReturnOrder.getId() + "换货前地址" + receiveAddress + "换货后地址" + buyerAddress);
                String taobaoExchangeLogMessage = SysNotesConstant.TAOBAO_EXCHANGE_LOG_MESSAGE;
                taobaoExchangeLogMessage = String.format(taobaoExchangeLogMessage, receiveAddress);
                String taobaoExchangeLogType = SysNotesConstant.TAOBAO_EXCHANGE_LOG_TYPE;
                omsReturnOrderService.saveAddOrderReturnLog(ocBReturnOrder.getId(),
                        taobaoExchangeLogMessage, taobaoExchangeLogType, operateUser);
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新退换货订单地址异常", e);
            throw new NDSException(e);
        }
    }

    /**
     * 重新修改地址
     *
     * @param exchangeOrder
     * @param buyerAddress
     */
    private void updateOrderAddressByExchange(OcBOrder exchangeOrder, String buyerAddress) {
        returnOrderTransferUtil.orderAddress(exchangeOrder, buyerAddress);
        exchangeOrder.setReceiverAddress(buyerAddress.replace(",", "::::"));
        if (exchangeOrder.getOrderStatus().intValue() == OmsOrderStatus.TO_BE_ASSIGNED.toInteger()) {
            //待分配直接修改
            omsOrderService.updateOrderInfo(exchangeOrder);
            omsOrderLogService.addUserOrderLog(exchangeOrder.getId(), exchangeOrder.getBillNo(),
                    OrderLogTypeEnum.TB_EXCHANGE_CONVERT.getKey(),
                    "换货修改地址成功", null, null, SystemUserResource.getRootUser());
            return;
        }

        OcBOrderRelation orderRelation = new OcBOrderRelation();
        orderRelation.setOrderInfo(exchangeOrder);
        orderRelation.setOrderItemList(omsOrderItemService.getOrderItemOcBOrderId(exchangeOrder.getId()));
        ValueHolderV14 valueHolderV14 = sgRpcervice.querySearchStockAndModifyAddress(orderRelation,
                SystemUserResource.getRootUser());
        if (valueHolderV14.getCode() == -1) {
            //失败
            omsOrderLogService.addUserOrderLog(exchangeOrder.getId(), exchangeOrder.getBillNo(),
                    OrderLogTypeEnum.TB_EXCHANGE_CONVERT.getKey(),
                    "修改地址失败：" + valueHolderV14.getMessage(), null, null, SystemUserResource.getRootUser());
        } else if (valueHolderV14.getCode() == 0) {
            //成功
            omsOrderLogService.addUserOrderLog(exchangeOrder.getId(), exchangeOrder.getBillNo(),
                    OrderLogTypeEnum.TB_EXCHANGE_CONVERT.getKey(),
                    "修改地址成功", null, null, SystemUserResource.getRootUser());
        } else if (valueHolderV14.getCode() == 3) {
            //缺货
            OcBOrder orderInfo = orderRelation.getOrderInfo();
            orderInfo.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            orderRelation.setOrderInfo(orderInfo);
            omsOrderService.updateOrderInfo(orderRelation.getOrderInfo());
            omsOrderLogService.addUserOrderLog(exchangeOrder.getId(), exchangeOrder.getBillNo(),
                    OrderLogTypeEnum.TB_EXCHANGE_CONVERT.getKey(),
                    "修改地址成功，状态缺货：" + valueHolderV14.getMessage(), null, null, SystemUserResource.getRootUser());
        }
    }

    /**
     * 取消退换货单
     *
     * @param operateUser
     * @param ocBReturnOrder
     * @param taobaoExchange
     * @throws IOException
     */
    private void cancelReturnOrder(User operateUser,
                                   OcBReturnOrder ocBReturnOrder,
                                   IpBTaobaoExchange taobaoExchange,
                                   OcBOrder order) throws IOException {
        Long id = ocBReturnOrder.getId();
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(id);
        jsonObject.put("ids", jsonArray);
        ValueHolderV14 holderV14 = ocCancelChangingOrRefundService.orRefundService(jsonObject, operateUser, Boolean.FALSE);
        int code = Tools.getInt(holderV14.getCode(), -1);
        if (code == 0) {
            this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(),
                    "", taobaoExchange);
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(order.getId());
            ocBOrder.setReturnStatus(0); //无退货
            omsOrderService.updateOrderInfo(ocBOrder);
            String taobaoExchangeLogMessage = SysNotesConstant.CANCEL_EXCHANGEORDER_LOG_MESSAGE;
            String cancelExchangeorderLogType = SysNotesConstant.CANCEL_EXCHANGEORDER_LOG_TYPE;
            omsReturnOrderService.saveAddOrderReturnLog(ocBReturnOrder.getId(), taobaoExchangeLogMessage,
                    cancelExchangeorderLogType, operateUser);
        } else {
            String sysRemark = holderV14.getMessage();
            Date created = taobaoExchange.getCreated();
            Long threeDays = 3 * 24 * 60 * 60 * 1000L + created.getTime();
            Date date = new Date();
            if (date.getTime() < threeDays) {
                //小于三天
                this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        sysRemark, taobaoExchange);
                return;
            }
            this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(),
                    sysRemark, taobaoExchange);
        }
    }

    /**
     * 退换单转退货单 删除换货单明细
     *
     * @param ocBReturnOrder 退换货单信息
     * @param taobaoExchange 淘宝退货单中间表信息
     * @param operateUser    操作用户
     * @throws IOException
     */
    private void exchangeTurnReturn(OcBReturnOrder ocBReturnOrder,
                                    IpBTaobaoExchange taobaoExchange,
                                    User operateUser) throws IOException {
        List<OcBReturnOrderExchange> ocBReturnOrderExchanges =
                ocBReturnOrderExchangeMapper.selectByReturnIdList(ocBReturnOrder.getId());
        if (CollectionUtils.isNotEmpty(ocBReturnOrderExchanges)) {
            int i = ocBReturnOrderExchangeMapper.deleteByReturnOrderId(ocBReturnOrder.getId());
            //@20201118 去除手推ES代码
            /*if (i > 0) {
                for (OcBReturnOrderExchange ocBReturnOrderExchange : ocBReturnOrderExchanges) {
                    ES4ReturnOrder.delOcBOrderExchange(ocBReturnOrder, ocBReturnOrderExchange);
                }
            }*/
        }
        ocBReturnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());
        ocBReturnOrder.setExchangeAmt(BigDecimal.ZERO);
        returnOrderTransferUtil.updateOperator(ocBReturnOrder, operateUser);
        omsReturnOrderService.updateOcBReturnOrder(ocBReturnOrder);
        this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(),
                "", taobaoExchange);
    }

    /**
     * 更新手机号
     *
     * @param ocBReturnOrder
     * @param taobaoExchange
     * @param exchangeOrder
     * @param operateUser
     */
    private void updateBuyerPhone(OcBReturnOrder ocBReturnOrder,
                                  IpBTaobaoExchange taobaoExchange,
                                  OcBOrder exchangeOrder,
                                  User operateUser) {
        try {
            //获取退换货订单的买家手机号
            String receiveMobile = ocBReturnOrder.getReceiveMobile();
            String buyerPhone = taobaoExchange.getBuyerPhone();
            if (StringUtils.isEmpty(receiveMobile) || !receiveMobile.equals(buyerPhone)) {
                ocBReturnOrder.setId(ocBReturnOrder.getId());
                ocBReturnOrder.setReceiveMobile(buyerPhone);
                ocBReturnOrder.setModifieddate(new Date());
                returnOrderTransferUtil.updateOperator(ocBReturnOrder, operateUser);
                omsReturnOrderService.updateOcBReturnOrder(ocBReturnOrder);

                exchangeOrder.setReceiverMobile(buyerPhone);
                omsOrderService.updateOrderInfo(exchangeOrder);
                //添加日志
                String phoneLogMessage = SysNotesConstant.TAOBAO_EXCHANGE_PHONE_LOG_MESSAGE;
                phoneLogMessage = String.format(phoneLogMessage, buyerPhone);
                String taobaoExchangeLogType = SysNotesConstant.TAOBAO_EXCHANGE_LOG_TYPE;
                omsReturnOrderService.saveAddOrderReturnLog(ocBReturnOrder.getId(), phoneLogMessage,
                        taobaoExchangeLogType, operateUser);
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新退换货订单手机号异常", e);
            throw new NDSException(e);
        }
    }

    /**
     * 换货sku不存在
     *
     * @param ipTaobaoExchangeRelation 淘宝换货中间表信息
     */
    public void updateExchangeSkuNotExistRemark(IpTaobaoExchangeRelation ipTaobaoExchangeRelation) {
        IpBTaobaoExchange taobaoExchange = ipTaobaoExchangeRelation.getTaobaoExchange();
        String exchangeSku = taobaoExchange.getExchangeSku(); //换货的sku
        Date created = taobaoExchange.getCreated();
        long threeDays = 3 * 24 * 60 * 60 * 1000L + created.getTime();
        Date date = new Date();
        if (date.getTime() < threeDays) {
            //小于三天
            String remark = SysNotesConstant.SYS_REMARK9;
            remark = String.format(remark, exchangeSku);
            this.updateExchangeRemarkAndIsTrans(
                    TransferOrderStatus.NOT_TRANSFER.toInteger(), remark, taobaoExchange);
        } else {
            String remark = SysNotesConstant.SYS_REMARK10;
            remark = String.format(remark, exchangeSku);
            this.updateExchangeRemarkAndIsTrans(
                    TransferOrderStatus.TRANSFERRED.toInteger(), remark, taobaoExchange);
        }
    }

    /**
     * 不存在原单
     *
     * @param created
     * @param ipBTaobaoExchange
     */
    public void noOriginalOrder(Date created, IpBTaobaoExchange ipBTaobaoExchange) {
        Date date = new Date();
        //判断退单时间是否超过三天
        Long threeDays = 3 * 24 * 60 * 60 * 1000L + created.getTime();
        if (threeDays > date.getTime()) {
            //找不到对应的原单,更新转换状态为 0 ,添加系统备注
            String remark = SysNotesConstant.SYS_REMARK1;
            this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.NOT_TRANSFER.toInteger(), remark,
                    ipBTaobaoExchange);
            return;
        }
        String remark = SysNotesConstant.SYS_REMARK2;
        this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), remark,
                ipBTaobaoExchange);
    }

    /**
     * 状态不满足更新装换状态
     *
     * @param ipBTaobaoExchange
     */

    public void statusDissatisfaction(IpBTaobaoExchange ipBTaobaoExchange) {
        String remark = SysNotesConstant.SYS_REMARK7;
        this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), remark,
                ipBTaobaoExchange);
    }

    /**
     * 生成退换货成功之后修改中间表状态为已转换
     * 同事插入日志
     */
    public void updateExchangeIsSuccess(Long returnOrderId, User operateUser, String exchangeSku,
                                        String boughtSku, IpTaobaoExchangeRelation orderInfo) {
        //更新状态并将退换货的sku更新到中间表
        OcBOrder ocBOrder = orderInfo.getOriginalValidOrderInfo();
        IpBTaobaoExchange taobaoExchange = orderInfo.getTaobaoExchange();
        Long disputeId = taobaoExchange.getDisputeId();
        String tid = ocBOrder.getTid();
        this.updateExchangeSkuAndIstrans(disputeId, exchangeSku, boughtSku, taobaoExchange, tid);
        //更新订单的退货状态为 退货中
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setReturnStatus(1);
        omsOrderService.updateOrderInfo(order);
        //2019-8-20 因为换货又退货找到的还是原单  将平台换货单号更新到明细中
        List<OcBOrderItem> ocBOrderItems = orderInfo.getOriginalOrderItemList();
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            OcBOrderItem orderItem = new OcBOrderItem();
            orderItem.setExchangeBillNo(disputeId);
            orderItem.setId(ocBOrderItem.getId());
            orderItem.setOcBOrderId(ocBOrderItem.getOcBOrderId());
            omsOrderItemService.updateOcBOrderItem(orderItem, ocBOrderItem.getOcBOrderId());
        }
        //插入日志
        String addExchangeorderLogMessage = SysNotesConstant.ADD_EXCHANGEORDER_LOG_MESSAGE;
        String addExchangeorderLogType = SysNotesConstant.ADD_EXCHANGEORDER_LOG_TYPE;
        omsReturnOrderService.saveAddOrderReturnLog(returnOrderId, addExchangeorderLogMessage,
                addExchangeorderLogType, operateUser);
    }

    /**
     * 系统异常时改变换货中间表状态为未转换并插入备注
     *
     * @param ipBTaobaoExchange
     * @return
     */
    public void updateExchangeIsTransError(IpBTaobaoExchange ipBTaobaoExchange, String error) {
        String sysRemark = SysNotesConstant.SYS_REMARK0;
        this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                sysRemark + error, ipBTaobaoExchange);

    }

    public IpBTaobaoExchange selectIpBTaobaoExchange(String disputeId) {
        return ipBTaobaoExchangeMapper.selectTaobaoExchangeDisputeId(disputeId);
    }

    /**
     * 更新状态为转换
     *
     * @param ipBTaobaoExchange
     */
    public void updateExchangeIsTransTransferring(IpBTaobaoExchange ipBTaobaoExchange, Integer isTrans) {
        JSONObject object = new JSONObject();
        object.put("dispute_id", ipBTaobaoExchange.getDisputeId());
        object.put("istrans", isTrans);
        ipBTaobaoExchange.setIstrans(isTrans + "");
        this.updateExchange(object, ipBTaobaoExchange);
    }

    /**
     * 更新换货中间表状态=已转换
     *
     * @param ipBTaobaoExchange 换货中间表信息
     */
    public void updateExchangeIsTransTransferred(IpBTaobaoExchange ipBTaobaoExchange) {
        String sysRemark = SysNotesConstant.SYS_REMARK47;
        this.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(),
                sysRemark, ipBTaobaoExchange);

    }

    /**
     * 更新中间表的转换状态以及退换会的sku信息
     *
     * @param disputeId
     * @param exchangeSku
     * @param boughtSku
     * @param ipBTaobaoExchange
     */
    private void updateExchangeSkuAndIstrans(Long disputeId, String exchangeSku, String boughtSku,
                                             IpBTaobaoExchange ipBTaobaoExchange, String tid) {
        int istrans = TransferOrderStatus.TRANSFERRED.toInteger();
        JSONObject object = new JSONObject();
        object.put("dispute_id", disputeId);
        object.put("bought_outerid", boughtSku);
        object.put("exchange_outerid", exchangeSku);
        object.put("istrans", istrans);
        object.put("modifieddate", new Date());
        object.put("sysremark", "");
        object.put("transdate", new Date());
        object.put("tid", tid);
        this.updateExchange(object, ipBTaobaoExchange);

    }

    /**
     * 更新换货单中间表备注和转换状态
     *
     * @param isTrans           转换状态
     * @param remark            系统憋着
     * @param ipBTaobaoExchange 换货单中间表对象
     */
    public void updateExchangeRemarkAndIsTrans(int isTrans, String remark,
                                               IpBTaobaoExchange ipBTaobaoExchange) {
        JSONObject object = new JSONObject();
        object.put("dispute_id", ipBTaobaoExchange.getDisputeId());
        object.put("istrans", isTrans);
        object.put("sysremark", remark);
        object.put("modifieddate", new Date());
        object.put("transdate", new Date());
        if (!StringUtils.isEmpty(ipBTaobaoExchange.getOuterSkuId())) {
            object.put("outer_sku_id", ipBTaobaoExchange.getOuterSkuId());
            object.put("tid", ipBTaobaoExchange.getTid());

        }
        ipBTaobaoExchangeMapper.updateTaobaoExchange(object);

    }

    /**
     * 更新换货中间表状态及备注
     */
    private void updateExchange(JSONObject object, IpBTaobaoExchange ipBTaobaoExchange) {
        try {
            ipBTaobaoExchange.setTransdate(new Date());
            int exchange = ipBTaobaoExchangeMapper.updateTaobaoExchange(object);
         /*   //推送es
            if (exchange > 0) {
                SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOEXCHANGE,
                        TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOEXCHANGE,
                        ipBTaobaoExchange, ipBTaobaoExchange.getId());
            }*/
        } catch (Exception e) {
            log.error("{} 更新换货中间表失败,平台单号:{}", this.getClass().getName(), object, e);
            throw new NDSException(e);
        }
    }

    /**
     * 处理换货同意或拒绝
     *
     * @param disputeIdList
     */
    public void handleExchangeAgreeOrRefuse(List<Long> disputeIdList, int type) {
        //查询所有单据
        if (CollectionUtils.isEmpty(disputeIdList)) {
            if (log.isDebugEnabled()) {
                log.info("换货待处理没有查询到处理的数据");
            }
            return;
        }
        //查询所有店铺策略
        List<StCExchangeStrategyOrderDO> stCExchangeOrderDOList = stRpcService.queryAllExchangeShop();
        if (CollectionUtils.isEmpty(stCExchangeOrderDOList)) {
            if (log.isDebugEnabled()) {
                log.warn("换货待处理没有查询到店铺策略");
            }
            return;
        }

        //无备注同意自动同意
        Map<Long, StCExchangeStrategyOrderDO> shopMap =
                stCExchangeOrderDOList.stream().collect(Collectors.toMap(x -> x.getCpCShopId(), x -> x));
        if (shopMap.size() == 0) {
            if (log.isDebugEnabled()) {
                log.debug("换货待处理没有查询到对应的店铺策略");
            }
            return;
        }

        //处理换货策略
        handleExchangeByStrategy(disputeIdList, shopMap, type);

    }

    /**
     * 处理换货策略
     *
     * @param disputeIdList
     * @param shopMap
     * @param type
     */
    private void handleExchangeByStrategy(List<Long> disputeIdList, Map<Long, StCExchangeStrategyOrderDO> shopMap, int type) {
        log.debug("处理换货策略" + disputeIdList.toString());
        List<RedisReentrantLock> lockListKey = Lists.newArrayList();
        try {

            List<IpBTaobaoExchange> exchangeList =
                    ipBTaobaoExchangeMapper.selectTaobaoExchangeByDisputeIdList(disputeIdList);
            //先锁单
            List<IpBTaobaoExchange> exchangeLockList = lockOrderList(exchangeList, lockListKey);
            if (CollectionUtils.isEmpty(exchangeLockList)) {
                return;
            }

            for (IpBTaobaoExchange exchange : exchangeLockList) {
                //原换货单id
                List<Long> oriOrderIdList = ES4Order.findIdsByOid(exchange.getDisputeId());

                if (CollectionUtils.isEmpty(oriOrderIdList)) {
                    if (log.isDebugEnabled()) {
                        log.debug("{}数据异常没有生产零售发货单", exchange.getDisputeId());
                    }
                    continue;
                }
                //同一店铺取第一个
                List<OcBOrder> dbOcBOrderList = ocBOrderMapper.selectOrderListByIds(oriOrderIdList);
                if (CollectionUtils.isEmpty(dbOcBOrderList)) {
                    if (log.isDebugEnabled()) {
                        log.debug("{}数据异常没有找到零售发货单", oriOrderIdList);
                    }
                    continue;
                }
                if (!shopMap.containsKey(dbOcBOrderList.get(0).getCpCShopId())) {
                    if (log.isDebugEnabled()) {
                        log.debug("{}没找到对应的店铺策略", dbOcBOrderList);
                    }
                    //没找到对应的店铺策略 不处理
                    continue;
                }
                log.debug("换货开始处理" + exchange.getDisputeId());
                StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO =
                        shopMap.get(dbOcBOrderList.get(0).getCpCShopId());
                if (type == AgreeOrRefuseEnum.REFUSE.getVal().intValue()) {
                    //缺货处理原信息 --- 拒绝处理
                    updateDisposeRefuse(dbOcBOrderList, exchange, stCExchangeStrategyOrderDO);
                } else {
                    //同意处理原信息
                    updateDisposeAgree(exchange, stCExchangeStrategyOrderDO);
                }
            }
        } catch (Exception e) {
            log.error("换货同意拒绝失败:", e);
            throw new NDSException("换货同意拒绝失败!");
        } finally {
            if (!CollectionUtils.isEmpty(lockListKey)) {
                for (RedisReentrantLock lockItem : lockListKey) {
                    lockItem.unlock();
                }
            }
        }
    }

    /**
     * 同意处理原信息
     *
     * @param exchange
     * @param stCExchangeStrategyOrderDO
     */
    private void updateDisposeAgree(IpBTaobaoExchange exchange, StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO) {
        log.debug("同意处理原信息" + exchange.getDisputeId() + "策略信息" + JSONObject.toJSONString(stCExchangeStrategyOrderDO));
        try {
            if (ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getIsOffAgree()) || stCExchangeStrategyOrderDO.getIsOffAgree() == 0) {
                updateExchangeRemarkAndIstransAndDispose(ExchangeOccupancyOrderStatus.OCCUPANCYFAIL.getVal(), "未开启自动同意，处理失败", exchange);
                return;
            }

            //先修改为处理中
            updateExchangeRemarkAndIstransAndDispose(ExchangeOccupancyOrderStatus.OCCUPANCY.getVal(), "", exchange);

            //有备注处理
            if (!StringUtils.isEmpty(exchange.getDescription())) {
                if (ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getIsNoRemarkAgree()) || stCExchangeStrategyOrderDO.getIsNoRemarkAgree() == 0) {
                    //未勾选有备注
                    updateExchangeRemarkAndIstransAndDispose(ExchangeOccupancyOrderStatus.OCCUPANCYFAIL.getVal(), "未勾选有备注，处理失败", exchange);
                    return;
                } else {
                    //判断偏差n元 勾选有备注
                    compareAmtAgree(stCExchangeStrategyOrderDO, exchange);
                }
            } else {
                //无备注  判断偏差n元

                compareAmtAgree(stCExchangeStrategyOrderDO, exchange);
            }

        } catch (Exception e) {
            //异常回滚为  未处理
            updateExchangeRemarkAndIstransAndDispose(ExchangeOccupancyOrderStatus.OCCUPANCYEND.getVal(), "异常同意换货单" + e.getMessage(), exchange);
            log.error("异常同意换货信息{}", exchange.getDisputeId(), e.getMessage());
        }

    }

    /**
     * 判断偏差n元
     *
     * @param stCExchangeStrategyOrderDO
     * @param exchange
     */
    private void compareAmtAgree(StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO, IpBTaobaoExchange exchange) {
        //判断价格
        BigDecimal amt = ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getDeviationAmtAgree()) ? BigDecimal.ZERO :
                stCExchangeStrategyOrderDO.getDeviationAmtAgree();
        //查询中间表金额
        IpCTaobaoProductItem exchangeTaobaoProductItem =
                cpRpcService.selectIpCTaobaoProductItemBySkuId(exchange.getExchangeSku());
        IpCTaobaoProductItem boughtTaobaoProductItem =
                cpRpcService.selectIpCTaobaoProductItemBySkuId(exchange.getBoughtSku());

        String sysRemark = "";
        if (null == exchangeTaobaoProductItem || null == boughtTaobaoProductItem) {
            sysRemark = "价格不同，不自动同意换货,商品条码ID查询不存在!";
        } else if (ObjectUtils.isEmpty(exchangeTaobaoProductItem.getPrice()) || ObjectUtils.isEmpty(boughtTaobaoProductItem.getPrice())) {
            sysRemark = "价格不同，不自动同意换货,商品价格查询不存在!";
        } else if (exchangeTaobaoProductItem.getPrice().compareTo(boughtTaobaoProductItem.getPrice()) == 0) {
            agreeExchange(exchange);
            return;

        } else if (exchangeTaobaoProductItem.getPrice().subtract(boughtTaobaoProductItem.getPrice()).abs().compareTo(amt) <= 0) {
            //价格超出
            agreeExchange(exchange);
            return;
        } else {
            sysRemark = "价格不同，不自动同意换货";
        }
        updateExchangeRemarkAndIstransAndDispose(ExchangeOccupancyOrderStatus.OCCUPANCYFAIL.getVal(), sysRemark, exchange);

    }

    /**
     * 同意换货
     *
     * @param exchange
     */
    private void agreeExchange(IpBTaobaoExchange exchange) {
        ExchangeAgreeModel model = new ExchangeAgreeModel();
        model.setSellerNick(exchange.getSellerNick());
        model.setDisputeId(exchange.getDisputeId());
        model.setShopId(exchange.getCpCShopId());
        ValueHolderV14 vh;
        try {
            vh = ipRpcService.agree(model);
        } catch (Exception e) {
            log.error("oms同意换货异常信息", e);
            throw new NDSException("同意换货失败!");
        }

        Boolean result = false;
        int code = Tools.getInt(vh.getCode(), -1);
        if (code == 0) {
            result = true;
        }

        Integer occStatus = ExchangeOccupancyOrderStatus.OCCUPANCYEND.getVal();
        String sysRemark = "";
        if (result) {
            sysRemark = "自动同意换货成功!";
        } else {
            occStatus = ExchangeOccupancyOrderStatus.OCCUPANCYFAIL.getVal();
            sysRemark = "调用卖家同意换货申请接口失败";
        }
        //修改已转换
        updateExchangeRemarkAndIstransAndDispose(occStatus, sysRemark, exchange);
    }

    /**
     * 缺货处理原信息
     *
     * @param dbOcBOrderList
     * @param exchange
     * @param stCExchangeStrategyOrderDO
     */
    private void updateDisposeRefuse(List<OcBOrder> dbOcBOrderList, IpBTaobaoExchange exchange, StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO) {
        //缺货是否开启
        if (ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getIsOffRefuse()) || stCExchangeStrategyOrderDO.getIsOffRefuse() == 0) {
            updateExchangeRemarkAndIstransAndDispose(ExchangeOccupancyOrderStatus.OCCUPANCYFAIL.getVal(), "缺货未开启，处理失败", exchange);
            return;
        }
        //缺货自动拒绝换货
        if (!ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getIsOutRefuse()) && stCExchangeStrategyOrderDO.getIsOutRefuse() == 1) {
            //未勾选 取拒绝原因
            refuseExchange(dbOcBOrderList, exchange, stCExchangeStrategyOrderDO, false);
            return;
        } else {
            if (ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getIsPriceOtherRefuse()) || stCExchangeStrategyOrderDO.getIsPriceOtherRefuse() == 0) {
                // 未勾选开启价格
                updateExchangeRemarkAndIstransAndDispose(ExchangeOccupancyOrderStatus.OCCUPANCYEND.getVal(), "缺货拒绝未开启，未勾选价格，不拒绝换货", exchange);
                return;
            } else {
                BigDecimal compareAmt = ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getDeviationAmtRefuse()) ? BigDecimal.ZERO : stCExchangeStrategyOrderDO.getDeviationAmtRefuse();
                //判断价格 查询中间表金额
                IpCTaobaoProductItem exchangeTaobaoProductItem = cpRpcService.selectIpCTaobaoProductItemBySkuId(exchange.getExchangeSku());
                IpCTaobaoProductItem boughtTaobaoProductItem = cpRpcService.selectIpCTaobaoProductItemBySkuId(exchange.getBoughtSku());
                BigDecimal abs = exchangeTaobaoProductItem.getPrice().subtract(boughtTaobaoProductItem.getPrice()).abs();
                if (abs.compareTo(compareAmt) > 0) {
                    //相差金额大约设置金额 拒绝换货
                    refuseExchange(dbOcBOrderList, exchange, stCExchangeStrategyOrderDO, true);
                } else {
                    updateExchangeRemarkAndIstransAndDispose(ExchangeOccupancyOrderStatus.OCCUPANCYEND.getVal(), "价格在金额范围内，不拒绝换货", exchange);
                    return;
                }
            }
        }
    }

    /**
     * 拒绝换货
     *
     * @param dbOcBOrderList
     * @param exchange
     * @param stCExchangeStrategyOrderDO
     * @param b
     */
    private void refuseExchange(List<OcBOrder> dbOcBOrderList, IpBTaobaoExchange exchange, StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO, boolean b) {
        try {
            //先修改为处理中
            updateExchangeRemarkAndIstransAndDispose(ExchangeOccupancyOrderStatus.OCCUPANCY.getVal(), "", exchange);

            Integer refuseId = null;
            String copywriting = "亲您好， 您想要换的款式没有库存了哦，这边只能暂时拒绝下您的换货申请了。建议您换下其他有库存的款式或者转让他人哦，带来不便万分抱歉~";
            //价格原因
            if (b) {
                if (Objects.isNull(stCExchangeStrategyOrderDO.getDeviationAmtRefuseReasonId())) {
                    refuseId = 4001;
                    copywriting = "库存不足或商品已下架";
                } else {
                    refuseId = stCExchangeStrategyOrderDO.getDeviationAmtRefuseReasonId();
                    if (!StringUtils.isEmpty(stCExchangeStrategyOrderDO.getDeviationAmtRefuseCopywriting())) {
                        copywriting = stCExchangeStrategyOrderDO.getDeviationAmtRefuseCopywriting();
                    }
                }
            } else {
                if (Objects.isNull(stCExchangeStrategyOrderDO.getOutRefuseReasonId())) {
                    refuseId = 4016;
                    copywriting = "所换商品差价，未协商一致";
                } else {
                    refuseId = stCExchangeStrategyOrderDO.getOutRefuseReasonId();
                    if (!StringUtils.isEmpty(stCExchangeStrategyOrderDO.getOutRefuseCopywriting())) {
                        copywriting = stCExchangeStrategyOrderDO.getOutRefuseCopywriting();
                    }
                }
            }

            //调用淘宝自动拒绝换货接口
            Boolean result = taobaoRefuseExchange(exchange, refuseId, copywriting);
            //成功取消原换货单  失败不处理
            dbOcBOrderList.forEach(order -> {
                if (order.getOrderStatus().intValue() != OmsOrderStatus.SYS_VOID.toInteger()
                        && order.getOrderStatus().intValue() != OmsOrderStatus.CANCELLED.toInteger()) {
                    //调用订单作废服务
                    ValueHolderV14 holder = omsOrderCancellationService.doInvoildOutOrder(order,
                            SystemUserResource.getRootUser());
                    int code = Tools.getInt(holder.getCode(), -1);
                    if (code == 0) {
                        //获取换货订单的id
                        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                                OrderLogTypeEnum.TB_EXCHANGE_CONVERT.getKey(),
                                SysNotesConstant.VOID_ORDER_LOG_MESSAGE, null, null, SystemUserResource.getRootUser());
                    }
                }
            });
            Integer occStatus = ExchangeOccupancyOrderStatus.OCCUPANCYEND.getVal();
            String sysRemark = "";
            if (result) {
                sysRemark = "缺货自动拒绝换货成功!";
            } else {
                occStatus = ExchangeOccupancyOrderStatus.OCCUPANCYFAIL.getVal();
                sysRemark = "调用缺货自动拒绝换货接口失败!";
            }
            //修改已转换
            updateExchangeRemarkAndIstransAndDispose(occStatus, sysRemark, exchange);
        } catch (Exception e) {
            //异常处理为 未处理
            //修改回滚
            updateExchangeRemarkAndIstransAndDispose(ExchangeOccupancyOrderStatus.NOTOCCUPANCY.getVal(), "异常同意拒绝换货单" + e.getMessage(), exchange);
            log.debug("异常缺货拒绝信息" + exchange.getDisputeId() + "  " + e.getMessage());
        }
    }

    /**
     * 淘宝自动拒绝接口
     *
     * @param exchange
     * @param refuseId
     * @param copywriting
     * @return
     */
    private Boolean taobaoRefuseExchange(IpBTaobaoExchange exchange, Integer refuseId, String copywriting) {
        ExchangeRefuseModel model = new ExchangeRefuseModel();
        model.setDisputeId(exchange.getDisputeId());
        model.setShopId(exchange.getCpCShopId());
        model.setFields("dispute_id, bizorder_id, modified, status");
        model.setLeaveMessage(copywriting);
        model.setSellerRefuseReasonId(refuseId.longValue());
        ValueHolderV14 vh;
        try {
            vh = ipRpcService.refuse(model);
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.error("oms拒绝换货异常信息", e);
            }
            throw new NDSException("拒绝换货失败!");
        }

        int code = Tools.getInt(vh.getCode(), -1);
        return code == 0;
    }

    //加锁
    private List<IpBTaobaoExchange> lockOrderList(List<IpBTaobaoExchange> toList, List<RedisReentrantLock> lockListKey) {
        List<IpBTaobaoExchange> lockList = new ArrayList<>();
        for (IpBTaobaoExchange order : toList) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(order.getId());
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockList.add(order);
                    lockListKey.add(redisLock);
                } else {
                    redisLock.unlock();
                }
            } catch (Exception e) {
                redisLock.unlock();
            }
        }
        return lockList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateExchangeRemarkAndIstransAndDispose(int dispose, String remark,
                                                         IpBTaobaoExchange ipBTaobaoExchange) {
        JSONObject object = new JSONObject();
        object.put("dispute_id", ipBTaobaoExchange.getDisputeId());
        object.put("occupancy_dispose_result", remark);
        object.put("modifieddate", new Date());
        object.put("transdate", new Date());
        object.put("OCCUPANCY_DISPOSE_STATUS", dispose);
        ipBTaobaoExchange.setOccupancyDisposeResult(remark);
        ipBTaobaoExchange.setModified(new Date());
        ipBTaobaoExchange.setModifieddate(new Date());
        ipBTaobaoExchange.setOccupancyDisposeStatus(dispose);
        this.updateExchange(object, ipBTaobaoExchange);

    }

    /**
     * 修改淘宝换货中间表处理结果
     *
     * @param ocBOrder        零售发货单
     * @param occupancyStatus 处理状态  OccupancyStatusEnum  枚举
     */
    public void updateIpTaoBaoExchangeDisposeStatusByOcBOrder(OcBOrder ocBOrder, List<OcBOrderItem> itemList,
                                                              int occupancyStatus) {
        if (null == ocBOrder) {
            return;
        }
        if (ObjectUtils.isEmpty(ocBOrder.getOrderType())) {
            return;
        }
        if (!OrderTypeEnum.EXCHANGE.getVal().equals(ocBOrder.getOrderType())) {
            //不是换货不处理，合单变为普通单类型 但是已经占单 所以不处理
            return;
        }
        List<Long> disputeId = new ArrayList<>();
        for (OcBOrderItem orderItem : itemList) {
            if (orderItem.getExchangeBillNo() != null) {
                disputeId.add(orderItem.getExchangeBillNo());
            }
        }
        if (CollectionUtils.isEmpty(disputeId)) {
            return;
        }
        List<IpBTaobaoExchange> exchangeList = ipBTaobaoExchangeMapper.selectTaobaoExchangeByDisputeIdList(disputeId);
        log.debug("换货单回写exchangeList" + exchangeList.size());
        if (CollectionUtils.isEmpty(exchangeList)) {
            return;
        }
        for (IpBTaobaoExchange ipBTaobaoExchange : exchangeList) {
            String status = ipBTaobaoExchange.getStatus();
            log.debug("换货单回写淘宝中间表" + ipBTaobaoExchange.getId() + " 状态" + occupancyStatus);
            //回写中间表状态
            JSONObject object = new JSONObject();
            object.put("dispute_id", ipBTaobaoExchange.getDisputeId());
            object.put("modifieddate", new Date());
            object.put("occupancy_status", occupancyStatus);
            if (occupancyStatus == OccupancyStatusEnum.SUCCESS.getVal()
                    && TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_STAY_HANDLE.getName().equals(status)) {
                object.put("istrans", TransferOrderStatus.NOT_TRANSFER.toInteger());
            }
            this.updateExchange(object, ipBTaobaoExchange);
            //发送换货转换的mq 信息
            if (occupancyStatus == OccupancyStatusEnum.SUCCESS.getVal()
                    && TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_STAY_HANDLE.getName().equals(status)) {
                this.sendDelayMq(ipBTaobaoExchange.getDisputeId() + "");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateExchangeOuterSku(String outerSku, IpBTaobaoExchange ipBTaobaoExchange) {
        JSONObject object = new JSONObject();
        object.put("dispute_id", ipBTaobaoExchange.getDisputeId());
        object.put("modifieddate", new Date());
        object.put("transdate", new Date());
        object.put("outer_sku_id", outerSku);
        ipBTaobaoExchange.setModified(new Date());
        ipBTaobaoExchange.setModifieddate(new Date());
        ipBTaobaoExchange.setOuterSkuId(outerSku);
        this.updateExchange(object, ipBTaobaoExchange);
    }


    /**
     * 延时1分钟再次发送退单转换mq
     */
    private void sendDelayMq(String disputeId) {
        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
        orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
        orderMqInfo.setChannelType(ChannelType.TAOBAO);
        orderMqInfo.setOrderType(OrderType.EXCHANGE);
        orderMqInfo.setOrderNo(disputeId);
        mqInfoList.add(orderMqInfo);
        String jsonValue = JSONObject.toJSONString(mqInfoList);
        PropertiesConf propertiesConf = ApplicationContextHandle.getBean(PropertiesConf.class);
//        String topic = propertiesConf.getProperty("r3.oc.oms.transfer.mq.topic");
//        String tag = propertiesConf.getProperty("r3.oc.oms.transfer.mq.tag");
        try {
            log.info("updateIpTaoBaoExchangeDisposeStatusByOcBOrder.sendDelayMq");
//            r3MqSendHelper.sendDelayMessage(jsonValue, topic, tag, 100L);
            defaultProducerSend.sendDelayTopic(Mq5Constants.TOPIC_R3_OC_OMS_CALL_TRANSFER, null, jsonValue, null, 100L);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public int updateDynamicExchangeTimeStamp(String node, String name, List<String> disputeIds, int timestamp) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0, l = disputeIds.size(); i < l; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(disputeIds.get(i));
        }
        return ipBTaobaoExchangeMapper.updateDynamicExchangeTimeStamp(node, name, sb.toString(), timestamp);
    }
}
