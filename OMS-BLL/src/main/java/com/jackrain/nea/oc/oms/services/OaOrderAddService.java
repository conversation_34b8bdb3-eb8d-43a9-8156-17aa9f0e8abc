package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.RegionInfo;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPaymentMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsPayStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderItemOaExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderOaExtend;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.vo.OaOrderAddVO;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AddressResolutionUtils;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @ClassName OaOrderAddService
 * @Description OA订单创建
 * <AUTHOR>
 * @Date 2024/1/22 17:01
 * @Version 1.0
 */
@Slf4j
@Component
public class OaOrderAddService {

    private static final String ORDER_TABLE_NAME = "OC_B_ORDER";

    private static final String ORDER_ITEM_TABLE_NAME = "OC_B_ORDER_ITEM";

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private AddressResolutionUtils addressResolutionUtils;

    @Autowired
    private RegionNewService regionNewService;

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBorderItemMapper;
    @Autowired
    private OcBOrderPaymentMapper paymentMapper;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    public ValueHolderV14 oaOrderAdd(List<OaOrderAddVO> oaOrderAddVOS) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>();
        log.info(LogUtil.format("OaOrderAddService.oaOrderAdd.oaOrderAddVOS：{}"), JSONUtil.toJsonStr(oaOrderAddVOS));
        Map<String, CpShop> cpShopMap = new HashMap<>();
        Map<String, ProductSku> productSkuMap = new HashMap<>();
        Map<String, OcBOrderOaExtend> oaExtendHashMap = new HashMap<>();

        for (OaOrderAddVO oaOrderAddVO : oaOrderAddVOS) {

            List<Long> ocBOrderIdList = ocBOrderMapper.selectOcBOrderIdByTid(oaOrderAddVO.getTid());
            if (CollectionUtil.isNotEmpty(ocBOrderIdList)) {
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("订单:" + oaOrderAddVO.getTid() + "已存在");
                return valueHolderV14;
            }

            OcBOrderOaExtend oaExtend = oaExtendHashMap.get(oaOrderAddVO.getTid());
            if (ObjectUtil.isNull(oaExtend)) {
                OcBOrderOaExtend orderOaExtend = new OcBOrderOaExtend();
                orderOaExtend.setSourceCode(oaOrderAddVO.getTid());
                orderOaExtend.setCpCShopTitle(oaOrderAddVO.getCpCShopTitle());
                orderOaExtend.setCpCRegionProvinceEname(oaOrderAddVO.getCpCRegionProvinceEname());
                orderOaExtend.setCpCRegionCityEname(oaOrderAddVO.getCpCRegionCityEname());
                orderOaExtend.setCpCRegionAreaEname(oaOrderAddVO.getCpCRegionAreaEname());
                orderOaExtend.setReceiverAddress(oaOrderAddVO.getReceiverAddress());
                orderOaExtend.setReceiverName(oaOrderAddVO.getReceiverName());
                orderOaExtend.setReceiverMobile(oaOrderAddVO.getReceiverMobile());
                orderOaExtend.setUserNick(StringUtils.substring(oaOrderAddVO.getReceiverName(), 0, 100));
                orderOaExtend.setOwnername(oaOrderAddVO.getOwnername());
                if (ObjectUtil.isNotNull(oaOrderAddVO.getIsPlainAddr())) {
                    orderOaExtend.setIsPlainAddr(oaOrderAddVO.getIsPlainAddr());
                }
                List<OcBOrderItem> orderItemOaExtendList = new ArrayList<>();
                OcBOrderItem orderItemOaExtend = new OcBOrderItemOaExtend();
                orderItemOaExtend.setPsCSkuEcode(oaOrderAddVO.getPsCSkuEcode());
                orderItemOaExtend.setQty(BigDecimal.valueOf(oaOrderAddVO.getQty()));
                orderItemOaExtend.setRealAmt(oaOrderAddVO.getPriceActual().multiply(orderItemOaExtend.getQty()));
                orderItemOaExtend.setPriceActual(oaOrderAddVO.getPriceActual());
                orderItemOaExtend.setPrice(BigDecimal.ZERO);
                if (ObjectUtil.isNull(oaOrderAddVO.getIsGift()) || oaOrderAddVO.getIsGift() == 0) {
                    orderItemOaExtend.setIsGift(0);
                } else {
                    orderItemOaExtend.setIsGift(1);
                    orderItemOaExtend.setGiftType("1");
                }
                orderItemOaExtendList.add(orderItemOaExtend);
                orderOaExtend.setOrderItemOaExtendList(orderItemOaExtendList);
                oaExtendHashMap.put(oaOrderAddVO.getTid(), orderOaExtend);
            } else {
                OcBOrderItemOaExtend orderItemOaExtend = new OcBOrderItemOaExtend();
                orderItemOaExtend.setPsCSkuEcode(oaOrderAddVO.getPsCSkuEcode());
                orderItemOaExtend.setQty(BigDecimal.valueOf(oaOrderAddVO.getQty()));
                orderItemOaExtend.setRealAmt(oaOrderAddVO.getPriceActual().multiply(orderItemOaExtend.getQty()));
                orderItemOaExtend.setPriceActual(oaOrderAddVO.getPriceActual());
                orderItemOaExtend.setPrice(BigDecimal.ZERO);
                if (ObjectUtil.isNull(oaOrderAddVO.getIsGift()) || oaOrderAddVO.getIsGift() == 0) {
                    orderItemOaExtend.setIsGift(0);
                } else {
                    orderItemOaExtend.setIsGift(1);
                    orderItemOaExtend.setGiftType("1");
                }
                oaExtend.getOrderItemOaExtendList().add(orderItemOaExtend);
            }
            if (ObjectUtil.isNull(cpShopMap.get(oaOrderAddVO.getCpCShopTitle()))) {
                CpShop cpShop = cpRpcService.queryByShopTitle(oaOrderAddVO.getCpCShopTitle());
                if (ObjectUtil.isNull(cpShop)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("店铺:" + oaOrderAddVO.getCpCShopTitle() + "不存在");
                    return valueHolderV14;
                }
                cpShopMap.put(oaOrderAddVO.getCpCShopTitle(), cpShop);
            }

            if (ObjectUtil.isNull(productSkuMap.get(oaOrderAddVO.getPsCSkuEcode()))) {
                ProductSku productSku = psRpcService.selectProductSku(oaOrderAddVO.getPsCSkuEcode());
                if (ObjectUtil.isNull(productSku)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("商品:" + oaOrderAddVO.getPsCSkuEcode() + "不存在");
                    return valueHolderV14;
                }
                productSkuMap.put(oaOrderAddVO.getPsCSkuEcode(), productSku);
            }
        }
        List<OcBOrderOaExtend> ocBOrderOaExtendList = new ArrayList<>(oaExtendHashMap.values());
        try {
            buildOcBOrder(ocBOrderOaExtendList, cpShopMap, productSkuMap, SystemUserResource.getRootUser());
            insert(ocBOrderOaExtendList);
        } catch (Exception e) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
            return valueHolderV14;
        }

        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");
        return valueHolderV14;
    }

    public void insert(List<OcBOrderOaExtend> ocBOrderOaExtendList) {

        List<OcBOrder> ocBOrderList = new ArrayList<>();
        List<OcBOrderItem> allOcBOrderItemList = new ArrayList<>();
        // 支付表
        List<OcBOrderPayment> ocBOrderPaymentList = new ArrayList<>();
        List<OcBToBeConfirmedTask> ocBToBeConfirmedTaskList = new ArrayList<>();

        for (OcBOrderOaExtend oaExtend : ocBOrderOaExtendList) {
            OcBOrder ocBOrder = new OcBOrder();
            BeanUtils.copyProperties(oaExtend, ocBOrder);
            ocBOrderList.add(ocBOrder);
            List<OcBOrderItem> ocBOrderItemList = oaExtend.getOrderItemOaExtendList();
            allOcBOrderItemList.addAll(ocBOrderItemList);
            ocBOrderPaymentList.add(oaExtend.getOcBOrderPayment());
            ocBToBeConfirmedTaskList.add(oaExtend.getOcBToBeConfirmedTask());
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(), "OA订单创建成功", null, null, SystemUserResource.getRootUser());
        }
        ocBOrderMapper.batchInsert(ocBOrderList);
        ocBorderItemMapper.batchInsert(allOcBOrderItemList);
        paymentMapper.batchInsert(ocBOrderPaymentList);
        toBeConfirmedTaskService.batchInsertToBeConfirmedTask(ocBToBeConfirmedTaskList);
    }

    private boolean buildOcBOrder(List<OcBOrderOaExtend> ocBOrderExtends, Map<String, CpShop> cpShopMap,
                                  Map<String, ProductSku> proSkuMap, User user) {
        boolean checkFlag = true;
        for (OcBOrderOaExtend ocBOrder : ocBOrderExtends) {
            String ownername = ocBOrder.getOwnername();
            ocBOrder.setShipAmt(BigDecimal.ZERO);
            ocBOrder.setPayTime(new Date());
            ocBOrder.setId(ModelUtil.getSequence(ORDER_TABLE_NAME));
            ocBOrder.setTid(ocBOrder.getSourceCode());
            ocBOrder.setMergeSourceCode(ocBOrder.getSourceCode());
            ocBOrder.setIsJcorder(0);
            ocBOrder.setBillNo(sequenceUtil.buildBillNo());
            ocBOrder.setPayType(1);
            ocBOrder.setIsInterecept(0);
            ocBOrder.setIsInreturning(0);
            ocBOrder.setQtySplit(0L);
            ocBOrder.setIsSplit(0);
            ocBOrder.setIsMerge(0);
            ocBOrder.setIsCancelMerge(0);
            ocBOrder.setOrderSource("手工新增");
            // 待分配
            ocBOrder.setOrderStatus(50);
            ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
            ocBOrder.setInvoiceStatus(0);
            ocBOrder.setTid(ocBOrder.getSourceCode());
            ocBOrder.setOccupyStatus(OrderOccupyStatus.STATUS_0);
            ocBOrder.setIsSameCityPurchase(0);
            ocBOrder.setOrderDate(Optional.ofNullable(ocBOrder.getOrderDate()).orElse(new Date()));
            if (ocBOrder.getServiceAmt() == null) {
                ocBOrder.setServiceAmt(BigDecimal.ZERO);
            }
            ocBOrder.setOrderFlag("0");
            ocBOrder.setOutStatus(1);
            ocBOrder.setWmsCancelStatus(0);
            ocBOrder.setRefundConfirmStatus(0);
            ocBOrder.setAutoAuditStatus(0);
            // ocBOrder.setSysPresaleStatus(0);
            ocBOrder.setIsModifiedOrder(0);
            //复制订单 需要清空审核时间 、配货时间
            //配货时间
            ocBOrder.setDistributionTime(null);
            // 审核时间
            ocBOrder.setAuditTime(null);
            // 拆单状态
            ocBOrder.setSplitStatus(0);
            // 是否换货未入库
            ocBOrder.setIsExchangeNoIn(0L);
            // 设置店铺信息
            CpShop cpShop = cpShopMap.get(ocBOrder.getCpCShopTitle());
            ocBOrder.setCpCShopId(cpShop.getCpCShopId());
            ocBOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
            ocBOrder.setCpCShopSellerNick(cpShop.getSellerNick());
            ocBOrder.setCpCShopEcode(cpShop.getEcode());
            ocBOrder.setPlatform(Optional.ofNullable(cpShop.getCpCPlatformId()).orElse(-1L).intValue());
            // 省名称或者市名称为空 ,就从详细地址中解析
            if ((StringUtils.isBlank(ocBOrder.getCpCRegionProvinceEname())
                    || StringUtils.isBlank(ocBOrder.getCpCRegionCityEname()))
                    && StringUtils.isNotBlank(ocBOrder.getReceiverAddress())) {
                Map<String, String> map = new HashMap<>();
                try {
                    map = addressResolutionUtils.addressResolutionNew(ocBOrder.getReceiverAddress());
                } catch (Exception e) {
                    checkFlag = false;
                    ocBOrder.setDesc((ocBOrder.getDesc() == null ? "" : ocBOrder.getDesc()) + "行政区域解析失败,请检查");
                }
                ocBOrder.setCpCRegionProvinceEname(map.get("province"));
                ocBOrder.setCpCRegionCityEname(map.get("city"));
                ocBOrder.setCpCRegionAreaEname(map.get("area"));
            }

            if (StringUtils.isEmpty(ocBOrder.getDesc()) || !ocBOrder.getDesc().contains("行政区域解析失败,请检查")) {
                //新增根据省市区名称查询省市区的信息
                ProvinceCityAreaInfo provinceCityAreaInfo = regionNewService.selectNewProvinceCityAreaInfo(
                        ocBOrder.getCpCRegionProvinceEname(), ocBOrder.getCpCRegionCityEname(), ocBOrder.getCpCRegionAreaEname());
                RegionInfo province = provinceCityAreaInfo.getProvinceInfo();
                if (province != null) {
                    ocBOrder.setCpCRegionProvinceId(province.getId());
                    ocBOrder.setCpCRegionProvinceEcode(province.getCode());
                } else {
                    checkFlag = false;
                    ocBOrder.setDesc((ocBOrder.getDesc() == null ? "" : ocBOrder.getDesc()) + "省不存在!请检查!");
                }
                //开始赋值市的信息
                RegionInfo city = provinceCityAreaInfo.getCityInfo();
                if (city != null) {
                    ocBOrder.setCpCRegionCityId(city.getId());
                    ocBOrder.setCpCRegionCityEcode(city.getCode());
                } else {
                    checkFlag = false;
                    ocBOrder.setDesc((ocBOrder.getDesc() == null ? "" : ocBOrder.getDesc()) + "市不存在!请检查!");
                }
                //开始赋值区的信息
                RegionInfo area = provinceCityAreaInfo.getAreaInfo();
                if (area != null) {
                    ocBOrder.setCpCRegionAreaId(area.getId());
                    ocBOrder.setCpCRegionAreaEcode(area.getCode());
                }

                if (StringUtils.isNotEmpty(ocBOrder.getDesc()) && ocBOrder.getDesc().contains("不存在!请检查!")) {
                    throw new RuntimeException(ocBOrder.getSourceCode() + "行政区转换异常");
                }
            }
            makeCreateField(ocBOrder, user);
            ocBOrder.setOwnerename(user.getEname());
            ocBOrder.setModifierename(user.getEname());
            if (StringUtils.isNotEmpty(ownername)) {
                ocBOrder.setOwnerename(ownername);
                ocBOrder.setModifierename(ownername);
            }
            checkDefault(ocBOrder);
            if (checkFlag) {
                checkFlag = buildOcBOrderItems(ocBOrder.getOrderItemOaExtendList(), proSkuMap, ocBOrder, user);
            }
            OrderAddressConvertUtil.convert(ocBOrder);
            // 重新订单头金额
            recountAmount(ocBOrder);

            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(ocBOrder.getId());
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTask.setIsactive("Y");
            ocBOrder.setOcBToBeConfirmedTask(toBeConfirmedTask);

            OcBOrderPayment payment = createPayMent(ocBOrder);
            ocBOrder.setOcBOrderPayment(payment);
        }
        return checkFlag;
    }

    //设置默认值
    public void checkDefault(OcBOrderOaExtend ocBOrderImpVo) {
        if (ocBOrderImpVo.getOrderStatus() == null) {
            ocBOrderImpVo.setOrderStatus(1);
        }
        if (ocBOrderImpVo.getIsInvoice() == null) {
            ocBOrderImpVo.setIsInvoice(0);
        }
        if (ocBOrderImpVo.getIsGeninvoiceNotice() == null) {
            ocBOrderImpVo.setIsGeninvoiceNotice(0);
        }
        if (ocBOrderImpVo.getIsCalcweight() == null) {
            ocBOrderImpVo.setIsCalcweight(0);
        }
        if (ocBOrderImpVo.getIsMerge() == null) {
            ocBOrderImpVo.setIsMerge(0);
        }
        if (ocBOrderImpVo.getIsSplit() == null) {
            ocBOrderImpVo.setIsSplit(0);
        }
        if (ocBOrderImpVo.getIsInterecept() == null) {
            ocBOrderImpVo.setIsInterecept(0);
        }
        if (ocBOrderImpVo.getIsInreturning() == null) {
            ocBOrderImpVo.setIsInreturning(0);
        }
        if (ocBOrderImpVo.getIsHasgift() == null) {
            ocBOrderImpVo.setIsHasgift(0);
        }
        if (ocBOrderImpVo.getIsJcorder() == null) {
            ocBOrderImpVo.setIsJcorder(0);
        }
        if (ocBOrderImpVo.getIsCombination() == null) {
            ocBOrderImpVo.setIsCombination(0);
        }
        if (ocBOrderImpVo.getIsOutUrgency() == null) {
            ocBOrderImpVo.setIsOutUrgency(0);
        }
        if (ocBOrderImpVo.getIsHasTicket() == null) {
            ocBOrderImpVo.setIsHasTicket(0);
        }
    }

    private void recountAmount(OcBOrderOaExtend ocBOrder) {
        ocBOrder.setIsInterecept(0);
        List<OcBOrderItem> orderItems = ocBOrder.getOrderItemOaExtendList();
        BigDecimal productAmt = BigDecimal.ZERO;
        BigDecimal orderAmt;
        BigDecimal productDiscountAmt = BigDecimal.ZERO;
        BigDecimal orderDiscountAmt = BigDecimal.ZERO;
        BigDecimal qtyAll = BigDecimal.ZERO;
        BigDecimal adjustAmt = BigDecimal.ZERO;
        BigDecimal weight = BigDecimal.ZERO;
        Boolean flag = false;
        for (OcBOrderItem item : orderItems) {
            if (item.getIsGift() == 1) {
                flag = true;
            }
            Long proType = Optional.ofNullable(item.getProType()).orElse(0L);
            if (proType.intValue() == SkuType.GIFT_PRODUCT || proType.intValue() == SkuType.COMBINE_PRODUCT) {
                continue;
            }
            // 打组合标
            if (proType.intValue() == SkuType.NO_SPLIT_COMBINE) {
                ocBOrder.setIsCombination(1);
            }
            productAmt = productAmt.add(Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO)
                    .multiply(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO)));
            productDiscountAmt =
                    productDiscountAmt.add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO));
            orderDiscountAmt =
                    orderDiscountAmt.add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            adjustAmt = adjustAmt.add(Optional.ofNullable(item.getAdjustAmt()).orElse(BigDecimal.ZERO));
            qtyAll = qtyAll.add(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO));
            weight = weight.add(Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO));
        }
        if (flag) {
            ocBOrder.setIsHasgift(1);
        }
        ocBOrder.setProductAmt(productAmt);
        ocBOrder.setProductDiscountAmt(productDiscountAmt);
        ocBOrder.setOrderDiscountAmt(orderDiscountAmt);
        ocBOrder.setAdjustAmt(adjustAmt);
        ocBOrder.setQtyAll(qtyAll);
        orderAmt = productAmt.subtract(productDiscountAmt)
                .subtract(orderDiscountAmt)
                .add(adjustAmt).add(ocBOrder.getShipAmt());
        ocBOrder.setOrderAmt(orderAmt);
        ocBOrder.setReceivedAmt(orderAmt);
        ocBOrder.setAmtReceive(orderAmt);
        ocBOrder.setServiceAmt(Optional.ofNullable(ocBOrder.getServiceAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setConsignAmt(Optional.ofNullable(ocBOrder.getConsignAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setConsignShipAmt(Optional.ofNullable(ocBOrder.getConsignShipAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setCodAmt(Optional.ofNullable(ocBOrder.getCodAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setJdReceiveAmt(Optional.ofNullable(ocBOrder.getJdReceiveAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setJdSettleAmt(Optional.ofNullable(ocBOrder.getJdSettleAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setLogisticsCost(Optional.ofNullable(ocBOrder.getLogisticsCost()).orElse(BigDecimal.ZERO));
        ocBOrder.setWeight(weight);
    }

    private OcBOrderPayment createPayMent(OcBOrderOaExtend ocBOrderExtend) {
        OcBOrderPayment payment = new OcBOrderPayment();
        payment.setId(ModelUtil.getSequence("OC_B_ORDER_PAYMENT"));
        payment.setOcBOrderId(ocBOrderExtend.getId());
        payment.setPayType(ocBOrderExtend.getPayType());
        payment.setAmtOrder(ocBOrderExtend.getOrderAmt());
        payment.setPaymentAmt(ocBOrderExtend.getOrderAmt());
        /*支付时间逻辑优化，如果订单上有支付时间则以订单的支付时间为准*/
        payment.setPayTime(ocBOrderExtend.getPayTime() == null ? new Date(System.currentTimeMillis()) : ocBOrderExtend.getPayTime());
        payment.setOwnerename(SystemUserResource.getRootUser().getEname());
        payment.setPayStatus(OmsPayStatus.PAID.toInteger());
        payment.setAdClientId((long) SystemUserResource.getRootUser().getClientId());
        payment.setAdOrgId((long) SystemUserResource.getRootUser().getOrgId());
        payment.setIsactive("Y");
        payment.setCreationdate(new Date(System.currentTimeMillis()));
        return payment;

    }

    private boolean buildOcBOrderItems(List<OcBOrderItem> orderItems, Map<String, ProductSku> proSkuMap,
                                       OcBOrderOaExtend ocBOrder, User user) {
        if (log.isDebugEnabled()) {
            log.debug(" OA订单创建 batchSaveOrders buildOcBOrderItems ,orderItems:{}", JSONObject.toJSONString(orderItems));
        }
        boolean checkFlag = true;
        for (OcBOrderItem item : orderItems) {
            if (StringUtils.isEmpty(item.getPsCSkuEcode())) {
                ocBOrder.setDesc("订单明细商品SKU条码为空");
                checkFlag = false;
                break;
            }
            if (item.getQty() == null || item.getQty().compareTo(BigDecimal.ZERO) == 0) {
                ocBOrder.setDesc("订单明细的数量不能为0或null");
                checkFlag = false;
                break;
            }
            makeCreateField(item, user);
            item.setModifierename(user.getEname());
            item.setOwnerename(user.getEname());
            item.setId(ModelUtil.getSequence(ORDER_ITEM_TABLE_NAME));
            item.setOcBOrderId(ocBOrder.getId());
            item.setPsCSkuEcode(item.getPsCSkuEcode().toUpperCase());
            if (proSkuMap == null || proSkuMap.isEmpty()) {
                ocBOrder.setDesc("Sku《" + item.getPsCSkuEcode() + "》在商品中心不存在！");
                checkFlag = false;
                break;
            }
            ProductSku productSku = proSkuMap.get(item.getPsCSkuEcode());
            if (productSku == null) {
                ocBOrder.setDesc("Sku《" + item.getPsCSkuEcode() + "》在商品中心不存在！");
                checkFlag = false;
                break;
            }
            // 供应类型 0 普通 1.代销轻供 2.寄售轻供
            item.setPsCProSupplyType(productSku.getPsCProSupplyType());
            item.setIsManualAdd("1");
            item.setQtyRefund(BigDecimal.ZERO);
            item.setTid(ocBOrder.getTid());
            item.setProType((long) productSku.getSkuType());
            item.setIsGift(Optional.ofNullable(item.getIsGift()).orElse(0));
            item.setNumIid(Optional.ofNullable(item.getNumIid()).orElse("0"));
            // 一米有品
            item.setReserveVarchar04(item.getReserveVarchar04());
            item.setPsCSkuId(productSku.getId());
            item.setPsCSkuPtEcode(productSku.getSkuEcode());
            item.setPsCSkuEname(productSku.getSkuName());
            item.setSkuSpec(productSku.getSkuSpec());
            item.setBarcode(productSku.getBarcode69());
            item.setPsCProId(productSku.getProdId());
            item.setPsCProEcode(productSku.getProdCode());
            item.setPsCProEname(productSku.getName());
            item.setPsCBrandId(productSku.getPsCBrandId());
            item.setSex(productSku.getSex());
            item.setPsCClrId(productSku.getColorId());
            item.setPsCClrEcode(productSku.getColorCode());
            item.setPsCClrEname(productSku.getColorName());
            item.setPsCSizeId(productSku.getSizeId());
            item.setPsCSizeEcode(productSku.getSizeCode());
            item.setPsCSizeEname(productSku.getSizeName());
            item.setPsCProMaterieltype(productSku.getMaterialType());
            item.setStandardWeight(Optional.ofNullable(productSku.getWeight()).orElse(BigDecimal.ZERO));
            item.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());

            // 一头牛优化内容：补充字段赋值 0917 产线问题修复
            item.setMDim4Id(productSku.getMDim4Id());
            item.setMDim6Id(productSku.getMDim6Id());
            if ("Y".equals(productSku.getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            // 吊牌价
            item.setPriceTag(Optional.ofNullable(productSku.getPricelist()).orElse(BigDecimal.ZERO));
            // 吊牌价
            item.setPriceList(Optional.ofNullable(productSku.getPricelist()).orElse(BigDecimal.ZERO));
            // 金额相关字段的值依靠前端传入，若前端未传默认给0 成交金额，成交单价，平台售价之前已经处理了
            // 平摊金额
            item.setOrderSplitAmt(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            // 商品优惠金额
            item.setAmtDiscount(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO));
            // 调整金额
            BigDecimal adjustAmt = item.getRealAmt()
                    .subtract(item.getPrice().multiply(item.getQty()))
                    .add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            // 调整金额
            item.setAdjustAmt(adjustAmt);
            if (productSku.getSkuType() == SkuType.NORMAL_PRODUCT) {
                item.setProType(Long.valueOf(productSku.getSkuType()));
            } else {
                item.setProType(Long.valueOf(SkuType.NO_SPLIT_COMBINE));
            }

            if (StringUtils.isEmpty(item.getOoid())) {
                item.setOoid(item.getTid() + productSku.getSkuEcode() + item.getIsGift());
            }


            if (log.isDebugEnabled()) {
                log.debug(" OA订单创建 batchSaveOrders buildOcBOrderItems ,item:{}", JSONObject.toJSONString(item));
            }
        }
        return checkFlag;
    }


    private void makeCreateField(BaseModel model, User user) {
        Date date = new Date();
        // 所属公司
        model.setAdClientId((long) user.getClientId());
        // 所属组织
        model.setAdOrgId((long) user.getOrgId());
        // 创建人id
        model.setOwnerid(Long.valueOf(user.getId()));
        // 创建时间
        model.setCreationdate(date);
        // 创建人用户名
        model.setOwnername(user.getName());
        // 修改人id
        model.setModifierid(Long.valueOf(user.getId()));
        // 修改人用户名
        model.setModifiername(user.getName());
        // 修改时间
        model.setModifieddate(date);
        // 是否启用
        model.setIsactive("Y");
    }

}
