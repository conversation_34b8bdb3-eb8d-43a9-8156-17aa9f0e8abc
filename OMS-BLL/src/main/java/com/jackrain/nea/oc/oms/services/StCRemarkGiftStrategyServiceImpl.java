package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOperationLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCRemarkGiftStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.RemarkAddGiftStrategyInfo;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.StCRemarkGiftStrategy;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.api.result.ProAttributeInfo;
import com.jackrain.nea.ps.api.result.ProSkuResult;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.model.StCRemarkGiftStrategyRelation;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.jackrain.nea.oc.oms.model.relation.OcBOrderConst.IS_ACTIVE_YES;

/**
 * 备注赠品策略service impl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class StCRemarkGiftStrategyServiceImpl implements StCRemarkGiftStrategyService {

    @Resource
    private StCRemarkGiftStrategyMapper stCRemarkGiftStrategyMapper;
    @Resource
    private BuildSequenceUtil buildSequenceUtil;
    @Resource
    private PsRpcService psRpcService;
    @Resource
    private OcBOperationLogMapper operationLogMapper;
    @Resource
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Resource
    private BatchOperationGoodsService batchOperationGoodsService;
    @Autowired
    private OcBOrderItemExtService ocBOrderItemExtService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Override
    public ValueHolderV14<Long> save(StCRemarkGiftStrategyRelation relation, User user) throws NDSException {
        StCRemarkGiftStrategy strategy = relation.getStCRemarkGiftStrategy();
        // 参数校验
        if (strategy.getShopId() == null) {
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, "店铺ID不能为空");
        }
        if (StringUtils.isEmpty(strategy.getMatchKeyword())) {
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, "关键字不能为空");
        }
        //关键字只支持中文和英文
        if (!StringUtils.isAlphanumeric(strategy.getMatchKeyword())) {
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, "关键字只支持中文和英文");
        }
        //关键字去除前后空格
        strategy.setMatchKeyword(strategy.getMatchKeyword().trim());

        if (strategy.getGiftSkuId() == null) {
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, "赠品商品ID不能为空");
        }
        if (strategy.getGiftQuantity() == null || strategy.getGiftQuantity() <= 0) {
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, "赠送数量必须大于0");
        }

        //查询商品信息(保存时若有组合商品 或一级类目=奶卡 保存失败，提示“不支持组合商品或奶卡”)
        try {
            ValueHolder valueHolder = psRpcService.selectProdSkuInfoBySkuIds(Lists.newArrayList(strategy.getGiftSkuId()));
            if (!valueHolder.isOK()) {
                throw new NDSException("查询商品信息失败");
            }
            ProSkuResult proSkuResult = (ProSkuResult) valueHolder.get("data");
            List<PsCProSkuResult> proSkuList = proSkuResult.getProSkuList();
            if (CollectionUtils.isEmpty(proSkuList)) {
                throw new NDSException("商品不存在");
            }
            PsCProSkuResult pro = proSkuList.get(0);
            strategy.setGiftSkuCode(pro.getSkuEcode());

            if (IS_ACTIVE_YES.equals(pro.getIsGroup())) {
                throw new NDSException("不支持组合商品或奶卡");
            }
            ProAttributeInfo proAttributeInfo = pro.getProAttributeMap().get("M_DIM2_ID");
            if (proAttributeInfo != null) {
                // 判断物料组编码是否包含10800 10801 10802
                if (proAttributeInfo.getEcode().equals("10800") || proAttributeInfo.getEcode().equals("10801") || proAttributeInfo.getEcode().equals("10802")) {
                    throw new NDSException("不支持组合商品或奶卡");
                }
            }
        } catch (Exception e) {
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, e.getMessage());
        }

        Long id = buildSequenceUtil.buildStCRemarkGiftStrategySequenceId();
        strategy.setId(id);
        strategy.setStatus(0);
        BaseModelUtil.initialBaseModelSystemField(strategy);

        JSONObject obj = new JSONObject();
        obj.put("ST_C_REMARK_GIFT_STRATEGY", strategy);
        String strategyNo = SequenceGenUtil.generateSquence("ST_C_REMARK_GIFT_STRATEGY", obj, user.getLocale(), false);
        strategy.setStrategyCode(strategyNo);
        strategy.setIsactive("N");

        try {
            // 构建新增
            OcBOperationLog operationLog = buildAddOperationLog(strategy, user);
            operationLogMapper.insert(operationLog);
            stCRemarkGiftStrategyMapper.insert(strategy);
        } catch (Exception e) {
            log.warn("save remark gift strategy error", e);
            return new ValueHolderV14<>(-1L, ResultCode.FAIL, "保存备注赠品策略失败");
        }
        return new ValueHolderV14<>(id, ResultCode.SUCCESS, "备注赠品策略保存成功");
    }

    @Override
    public ValueHolderV14<Void> update(StCRemarkGiftStrategyRelation relation, JSONObject before, JSONObject after, User user, Long id) throws NDSException {
        StCRemarkGiftStrategy strategy = relation.getStCRemarkGiftStrategy();
        StCRemarkGiftStrategy oldStrategy = stCRemarkGiftStrategyMapper.selectById(id);
        // 判断当前状态是否为启用状态。启用状态不允许修改
        if (IS_ACTIVE_YES.equals(oldStrategy.getIsactive())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "当前状态为启用状态，不允许修改");
        }
        if (strategy.getGiftQuantity() != null && strategy.getGiftQuantity() <= 0) {
            return new ValueHolderV14<>(ResultCode.FAIL, "赠送数量必须大于0");
        }
        //关键字只支持中文和英文
        if (strategy.getMatchKeyword() != null && !StringUtils.isAlphanumeric(strategy.getMatchKeyword())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "关键字只支持中文和英文");
        }
        //关键字去除前后空格
        if (strategy.getMatchKeyword() != null) {
            strategy.setMatchKeyword(strategy.getMatchKeyword().trim());
        }

        if (after != null) {
            StCRemarkGiftStrategy afterStrategy = after.getObject("ST_C_REMARK_GIFT_STRATEGY", StCRemarkGiftStrategy.class);
            if (afterStrategy != null) {
                StCRemarkGiftStrategy updateStrategy = new StCRemarkGiftStrategy();
                updateStrategy.setId(id);
                BaseModelUtil.makeBaseModifyField(updateStrategy, user);
                updateStrategy.setRemark(afterStrategy.getRemark());
                updateStrategy.setGiftQuantity(afterStrategy.getGiftQuantity());
                if (StringUtils.isNotBlank(afterStrategy.getMatchKeyword())) {
                    updateStrategy.setMatchKeyword(afterStrategy.getMatchKeyword());
                }
                updateStrategy.setModifierid(user.getId().longValue());
                updateStrategy.setModifieddate(new Date());
                OcBOperationLog operationLog = buildUpdateOperationLog(strategy, oldStrategy, user);
                operationLogMapper.insert(operationLog);
                stCRemarkGiftStrategyMapper.updateById(updateStrategy);
            }
        }

        return new ValueHolderV14<>(ResultCode.SUCCESS, "备注赠品策略保存成功");
    }

    @Override
    public void checkRule(StCRemarkGiftStrategy strategy) {
        // 获取店铺ID
        Long shopId = strategy.getShopId();

        // 根据店铺ID 查询出所有可用的规则
        List<StCRemarkGiftStrategy> stCRemarkGiftStrategies = stCRemarkGiftStrategyMapper.selectByShopId(shopId);
        if (CollectionUtils.isEmpty(stCRemarkGiftStrategies)) {
            return;
        }

        for (StCRemarkGiftStrategy stCRemarkGiftStrategy : stCRemarkGiftStrategies) {
            Long shopId1 = stCRemarkGiftStrategy.getShopId();
            String giftSkuCode = stCRemarkGiftStrategy.getGiftSkuCode();
            //店铺+赠品编码不能重复
            if (shopId1.equals(shopId) && giftSkuCode.equals(strategy.getGiftSkuCode())) {
                throw new NDSException("请检查数据，疑似策略已经有");
            }
        }
    }

    /**
     * 卖家备注添加赠品
     *
     * @param shopId
     * @param tid
     */
    @Override
    public boolean addGiftSku(Long shopId, String tid) {
        List<StCRemarkGiftStrategy> stCRemarkGiftStrategyInfos = getStCRemarkGiftStrategyInfos(shopId);
        if (CollectionUtils.isEmpty(stCRemarkGiftStrategyInfos)) {
            log.warn("addGiftSku 无有效卖家备注添加赠品策略 is null tid:{}", tid);
            return true;
        }

        List<OcBOrder> ocBOrders = ocBOrderMapper.selectOcBOrderByTid(tid);
        if (CollectionUtils.isEmpty(ocBOrders)) {
            return false;
        }

        //备赠过不处理
        List<OcBOrder> remarkGiftOrders =
                ocBOrders.stream().filter(p -> OcBOrderConst.IS_STATUS_IY.equals(p.getIsRemarkGift())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(remarkGiftOrders)) {
            log.warn("addGiftSku 备赠过不处理 is not null tid:{}", tid);
            return true;
        }

        // 筛选符合条件的订单
        List<OcBOrder> filteredOrders = getMatchOrders(ocBOrders);
        if (CollectionUtils.isEmpty(filteredOrders)) {
            log.warn("addGiftSku 无符合条件订单 is not null tid:{}", tid);
            return false;
        }

        //过滤toc残次订单
        List<OcBOrder> realOrders = filteredOrders.stream().filter(p -> !OmsOrderUtil.isToCCcOrder(p)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(realOrders)) {
            log.warn("addGiftSku 过滤残次后无符合条件订单 is not null tid:{}", tid);
            return false;
        }

        //获取filteredOrders里id最小的一条数据
        OcBOrder order = realOrders.stream().min(Comparator.comparing(OcBOrder::getId)).get();

        String sellerMemo = order.getSellerMemo();
        if (StringUtils.isBlank(sellerMemo)) {
            return true;
        }

        //校验关键词在卖家备注里出现次数
        List<String> matchKeywords =
                stCRemarkGiftStrategyInfos.stream().map(StCRemarkGiftStrategy::getMatchKeyword).distinct().collect(Collectors.toList());
        int num = 0;
        for (String keyword : matchKeywords) {
            num = num + StringUtils.countMatches(sellerMemo, keyword);
        }
        if (num > 1) {
            log.warn("addGiftSku 关键词出现多次不处理 tid:{}", tid);
            return true;
        }

        // 遍历每个策略
        Map<String, RemarkAddGiftStrategyInfo> map = Maps.newHashMap();
        for (StCRemarkGiftStrategy strategy : stCRemarkGiftStrategyInfos) {
            try {
                parseContent(sellerMemo, strategy, map);
            } catch (Exception e) {
                log.warn("addGiftSku parseContent error strategyCode:{}", strategy.getStrategyCode(), e);
            }
        }

        if (MapUtils.isEmpty(map)) {
            log.warn("addGiftSku 没有需要添加的商品 is null tid:{}", tid);
            return true;
        }

        // 添加赠品
        User rootUser = SystemUserResource.getRootUser();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(order.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocOrder = ocBOrderMapper.selectById(order.getId());
                if (OcBOrderConst.IS_STATUS_IY.equals(ocOrder.getIsRemarkGift())) {
                    throw new NDSException("订单已经备赠过");
                }

                //toc残次订单不允许添加赠品
                if (OmsOrderUtil.isToCCcOrder(ocOrder)) {
                    throw new NDSException("残次不支持添加赠品");
                }

                for (Map.Entry<String, RemarkAddGiftStrategyInfo> entry : map.entrySet()) {
                    String changeGoodsSKu = entry.getKey();
                    RemarkAddGiftStrategyInfo strategyInfo = entry.getValue();
                    Integer addQuantity = strategyInfo.getQuantity();
                    try {
                        BatchOperationGoodsService bean = ApplicationContextHandle.getBean(BatchOperationGoodsService.class);
                        ValueHolderV14 v14 = bean.addGoodsMainStep(ocOrder, rootUser, changeGoodsSKu, addQuantity);
                        if (v14 != null) {
                            if (ResultCode.SUCCESS == v14.getCode()) {
                                OcBOrder modifiedOrder = new OcBOrder();
                                modifiedOrder.setId(ocOrder.getId());
                                modifiedOrder.setIsHasgift(1);
                                modifiedOrder.setIsRemarkGift(1);
                                ocBOrderMapper.updateById(modifiedOrder);
                                OcBOrder newOrder = new OcBOrder();
                                newOrder.setId(ocOrder.getId());
                                batchOperationGoodsService.updateOrderAllSku(newOrder);
                                ocBOrderItemExtService.deleteByOrderId(ocOrder.getId());

                                omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                                        OrderLogTypeEnum.REMARK_GIFT_STRATEGY.getKey()
                                        , changeGoodsSKu + " 新增赠品成功！策略编码:" + strategyInfo.getStrategyCode() + ";来源卖家备注:" + sellerMemo, null, null,
                                        rootUser);
                            } else {
                                DingTalkUtil.notice("新增赠品失败!原因:" + v14.getMessage() + " tid:" + tid + " billNo:" + ocOrder.getBillNo());
                                omsOrderLogService.addUserOrderLog(ocOrder.getId(), ocOrder.getBillNo(),
                                        OrderLogTypeEnum.REMARK_GIFT_STRATEGY.getKey()
                                        , changeGoodsSKu + " 新增商品失败！原因:" + v14.getMessage(), null, null, rootUser);
                            }
                        }
                    } catch (Exception e) {
                        log.warn(LogUtil.format("备赠新增赠品服务异常.异常: {}"), Throwables.getStackTraceAsString(e));
                        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.REMARK_GIFT_STRATEGY.getKey(),
                                changeGoodsSKu + " 新增商品失败！原因:" + e.getMessage(), null, null, rootUser);
                    }
                }
            } else {
                DingTalkUtil.notice("当前订单其他人在操作，备赠新增赠品失败! tid:" + tid);
                throw new NDSException(Resources.getMessage("当前订单其他人在操作，备赠新增赠品失败!"));
            }
        } catch (Exception e) {
            log.warn(LogUtil.format("备赠新增赠品服务异常.异常: {}"), Throwables.getStackTraceAsString(e));
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.REMARK_GIFT_STRATEGY.getKey(),
                    " 新增商品失败！原因:" + e.getMessage(), null, null, rootUser);
        } finally {
            redisLock.unlock();
        }
        return true;
    }

    /**
     * 1. 业务类型为电商销售
     * 2. 卡单状态
     * 3. 订单状态为待寻源
     * 4. 非补发单
     * 5. 非复制单
     *
     * @param ocBOrders
     * @return
     */
    private static List<OcBOrder> getMatchOrders(List<OcBOrder> ocBOrders) {
        List<OcBOrder> filteredOrders = ocBOrders.stream()
                .filter(order ->
                        // 业务类型为电商销售
                        OrderBusinessTypeCodeEnum.E_COMMERCE_SALE_ORDER.getCode().equals(order.getBusinessTypeCode())
                                // 卡单状态
                                && (order.getIsDetention() != null && AdvanceConstant.DETENTION_STATUS_1.equals(order.getIsDetention()))
                                // 订单状态为待寻源
                                && OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus())
                                // 非复制单
                                && !YesNoEnum.Y.getVal().equals(order.getIsCopyOrder())
                                // 非补发单
                                && !YesNoEnum.Y.getVal().equals(order.getIsResetShip()))
                .collect(Collectors.toList());
        return filteredOrders;
    }

    private void parseContent(String sellerMemo, StCRemarkGiftStrategy strategy, Map<String, RemarkAddGiftStrategyInfo> map) {
        if (!sellerMemo.contains(strategy.getMatchKeyword())) {
            return;
        }

        // 获取keyword后面到"-"之间的内容
        int startIndex = sellerMemo.indexOf(strategy.getMatchKeyword() + "-");
        if (startIndex == -1) {
            return;
        }

        startIndex = startIndex + strategy.getMatchKeyword().length() + 1;
        int endIndex = sellerMemo.indexOf("-", startIndex);
        if (endIndex == -1) {
            endIndex = sellerMemo.length();
        }

        String skuContent = sellerMemo.substring(startIndex, endIndex);
        String[] skuPairs = skuContent.split("/");


        for (String pair : skuPairs) {
            if (!pair.contains("+")) {
                continue;
            }

            String[] parts = pair.split("\\+");
            if (parts.length != 2) {
                continue;
            }

            // 检查是否匹配指定的skuCode
            if (!parts[0].equals(strategy.getGiftSkuCode())) {
                continue;
            }

            Integer qty = Integer.parseInt(parts[1]);

            // 只添加不超过quantity的数量
            if (qty <= strategy.getGiftQuantity()) {
                RemarkAddGiftStrategyInfo info = new RemarkAddGiftStrategyInfo();
                info.setStrategyCode(strategy.getStrategyCode());
                info.setQuantity(qty);
                map.put(strategy.getGiftSkuCode(), info);
            }
        }
    }

    private OcBOperationLog buildAddOperationLog(StCRemarkGiftStrategy strategy, User user) {
        StringBuilder afterValue = new StringBuilder();
        afterValue.setLength(0);
        afterValue.append(JSON.toJSONString(strategy));
        OcBOperationLog operationLog = getOperationLog("ST_C_REMARK_GIFT_STRATEGY", "ADD", strategy.getId(),
                "卖家备注赠品策略", "新增卖家备注赠品策略", "新增", afterValue.toString(), user);
        return operationLog;
    }

    private OcBOperationLog buildUpdateOperationLog(StCRemarkGiftStrategy strategy, StCRemarkGiftStrategy oldStrategy, User user) {
        StringBuilder afterValue = new StringBuilder();
        afterValue.setLength(0);
        afterValue.append(JSON.toJSONString(strategy));
        OcBOperationLog operationLog = getOperationLog("ST_C_REMARK_GIFT_STRATEGY", "MOD", oldStrategy.getId(),
                "卖家备注赠品策略", "修改卖家备注赠品策略", JSON.toJSONString(oldStrategy), afterValue.toString(), user);
        return operationLog;
    }

    private OcBOperationLog getOperationLog(String tableName, String operationType, Long updateId,
                                            String tableDescription, String columnName, String columnBeforeValue,
                                            String columnAfterValue, User user) {
        OcBOperationLog operationLog = new OcBOperationLog();
        operationLog.setId(ModelUtil.getSequence(StConstant.TAB_OC_B_OPERATION_LOG));
        operationLog.setTableName(tableName);
        operationLog.setOperationType(OperationTypeEnum.getNameByValue(operationType));
        operationLog.setUpdateId(updateId);
        operationLog.setUpdateModelName(tableDescription);
        operationLog.setModContent(columnName);
        operationLog.setBeforeData(columnBeforeValue);
        operationLog.setAfterData(columnAfterValue);
        StBeanUtils.makeCreateField(operationLog, user);
        return operationLog;
    }

    /**
     * 根据店铺获取有效缓存策略
     *
     * @param cpCShopId
     * @return
     */
    private List<StCRemarkGiftStrategy> getStCRemarkGiftStrategyInfos(Long cpCShopId) {
        String strategyRedisKey = OmsRedisKeyResources.buildStCRemarkGiftStrategyRedisKey(cpCShopId);
        String redisValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(strategyRedisKey);
        if (StringUtils.isNotBlank(redisValue)) {
            if ("-1".equals(redisValue)) {
                return Lists.newArrayList();
            } else {
                return JSON.parseArray(redisValue, StCRemarkGiftStrategy.class);
            }
        }

        List<StCRemarkGiftStrategy> stCRemarkGiftStrategies = stCRemarkGiftStrategyMapper.selectByShopId(cpCShopId);
        if (CollectionUtils.isEmpty(stCRemarkGiftStrategies)) {
            //缓存
            RedisOpsUtil.getStrRedisTemplate().opsForValue().set(strategyRedisKey, "-1", 1, TimeUnit.DAYS);
            return Lists.newArrayList();
        }

        //缓存
        RedisOpsUtil.getStrRedisTemplate().opsForValue().set(strategyRedisKey, JSON.toJSONString(stCRemarkGiftStrategies), 1, TimeUnit.DAYS);

        return stCRemarkGiftStrategies;
    }

}