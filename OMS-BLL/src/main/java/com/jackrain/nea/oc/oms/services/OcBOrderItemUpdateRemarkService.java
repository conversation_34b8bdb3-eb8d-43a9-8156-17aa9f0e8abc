package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> lin yu
 * @date : 2022/7/20 下午4:45
 * @describe :
 */
@Component
@Slf4j
public class OcBOrderItemUpdateRemarkService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    public ValueHolderV14 updateItemRemark(JSONObject jsonObject, User user) {

        ValueHolderV14 valueHolderV14;

        valueHolderV14 = checkParam(jsonObject, user);
        if (!valueHolderV14.isOK()) {
            return valueHolderV14;
        }

        Long orderId = jsonObject.getLong("orderId");
        String remark = jsonObject.getString("remark");
        JSONArray itemIdList = jsonObject.getJSONArray("itemIdList");

        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {

                doUpdateItemRemark(orderId, remark, user, itemIdList);

            } else {
                log.error(LogUtil.format("修改订单明细备注加锁失败", orderId));
                throw new NDSException("当前订单正在操作中");

            }
        } catch (Exception e) {
            log.error(LogUtil.format("修改订单明细备注失败", orderId), Throwables.getStackTraceAsString(e));

            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
            return valueHolderV14;

        } finally {
            redisLock.unlock();
        }

        return valueHolderV14;
    }


    private void doUpdateItemRemark(Long orderId, String remark, User user, JSONArray itemIdList) {

        OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);

        if (ocBOrder == null) {
            throw new NDSException("当前订单已经不存在");
        }
        List<Long> itemIds= itemIdList.toJavaList(Long.class);
        Integer orderStatus = ocBOrder.getOrderStatus();

        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {

            OcBOrderItem updateModel = new OcBOrderItem();
            updateModel.setReserveVarchar02(remark);
            updateModel.setModifierid(user.getId().longValue());
            updateModel.setModifiername(user.getName());
            updateModel.setModifierename(user.getEname());
            updateModel.setModifieddate(new Date());

            ocBOrderItemMapper.update(updateModel, new UpdateWrapper<OcBOrderItem>().lambda()
                    .in(OcBOrderItem::getId, itemIds));

        } else {
            throw new NDSException("当前订单状态不允许明细备注");
        }
    }


    private ValueHolderV14 checkParam(JSONObject jsonObject, User user) {

        ValueHolderV14 valueHolderV14 = new ValueHolderV14(ResultCode.SUCCESS, "SUCCESS");

        if (jsonObject.getLong("orderId") == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请传入订单主表ID");
            return valueHolderV14;
        }

        if (CollectionUtils.isEmpty(jsonObject.getJSONArray("itemIdList"))) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请选择需要修改备注的记录");
            return valueHolderV14;
        }

        return valueHolderV14;

    }
}
