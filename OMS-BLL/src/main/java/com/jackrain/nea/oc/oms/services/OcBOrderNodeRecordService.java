package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNodeRecordMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNodeRecord;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * description:零售发货单节点业务类
 * @Author:  liuwenjin
 * @Date 2022/1/19 11:15 上午
 */
@Component
@Slf4j
public class OcBOrderNodeRecordService {

    @Autowired
    private OcBOrderNodeRecordMapper ocBOrderNodeRecordMapper;

    public void insertByNode(OcBOrderNodeEnum nodeEnum, Date operateTime, Long ocBOrderId, User user){

        try {
            if(log.isDebugEnabled()){
                log.debug("零售发货单节点信息，节点-{}，单据编号-{}", JSON.toJSONString(nodeEnum),ocBOrderId);
            }
            OcBOrderNodeRecord nodeRecord = new OcBOrderNodeRecord();
            nodeRecord.setId(ModelUtil.getSequence(OcCommonConstant.OC_B_ORDER_NODE_RECORD));
            nodeRecord.setNode(nodeEnum.getValue());
            nodeRecord.setNodeName(nodeEnum.getDesc());
            nodeRecord.setNodeOperateTime(operateTime);
            nodeRecord.setOcBOrderId(ocBOrderId);
            nodeRecord.setOwnerid(Long.valueOf(user.getId()));
            nodeRecord.setCreationdate(new Date());
            nodeRecord.setOwnername(user.getName());
            nodeRecord.setOwnerename(user.getEname());
            nodeRecord.setModifierename(user.getEname());
            nodeRecord.setModifieddate(new Date());
            if(log.isDebugEnabled()){
                log.debug("零售发货单节点插入数据为：{}", JSON.toJSONString(nodeRecord));
            }
            ocBOrderNodeRecordMapper.insert(nodeRecord);
        }catch (Exception e){
            log.error("零售发货单节点信息异常：{}", Throwables.getStackTraceAsString(e));
        }

    }
}