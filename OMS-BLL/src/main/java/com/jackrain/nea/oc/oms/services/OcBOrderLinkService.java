package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.ip.model.OrderFullLinkModel;
import com.jackrain.nea.ip.model.yuchenghe.YuChengHeOperateTypeEnum;
import com.jackrain.nea.ip.model.yuchenghe.YuChengHeOrderLogEventRequest;
import com.jackrain.nea.ip.model.yuchenghe.YuChengHeRequest;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLink;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.jackrain.nea.resource.OcElasticSearchIndexResources.OC_B_ORDER_LINK_TYPE_NAME;

/**
 * 全链路日志服务 御城河
 *
 * @author: heliu
 * @since: 2019/3/8
 * create at : 2019/3/8 17:32
 */
@Component
@Slf4j
public class OcBOrderLinkService {

    private static final String LOG_TABLE_NAMAE = "oc_b_order_link";

    @Autowired
    private OmsOrderSyncFullLogService omsOrderSyncFullLogService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private IpRpcService ipRpcService;


    public void addOrderFinkLogThread(OcBOrderLink ocBOrderLink, User user) {

        try {
            String indexName = OcElasticSearchIndexResources.OC_B_ORDER_LINK;
            long autoId = ModelUtil.getSequence(LOG_TABLE_NAMAE);
            ocBOrderLink.setId(autoId);
            ocBOrderLink.setOcBOrderId(ocBOrderLink.getOcBOrderId());
            ocBOrderLink.setTid(ocBOrderLink.getTid());
            ocBOrderLink.setBackflowStatus(ocBOrderLink.getBackflowStatus());
            ocBOrderLink.setExtAttribute(ocBOrderLink.getExtAttribute());
            ocBOrderLink.setPlatform(ocBOrderLink.getPlatform());
            ocBOrderLink.setSellerNick(ocBOrderLink.getSellerNick());
            ocBOrderLink.setSyncStatus(ocBOrderLink.getSyncStatus());
            ocBOrderLink.setSyncTime(new Date());
            ocBOrderLink.setErrorInfo(ocBOrderLink.getErrorInfo());
            if (user != null) {
                ocBOrderLink.setAdOrgId((long) user.getOrgId());
                ocBOrderLink.setOwnername(user.getEname());
                ocBOrderLink.setOwnerename(user.getEname());
                ocBOrderLink.setAdClientId((long) user.getClientId());
                ocBOrderLink.setOwnerid(Long.valueOf(user.getId()));
                ocBOrderLink.setCreationdate(new Date());
                ocBOrderLink.setModifierid(Long.valueOf(user.getId()));
                ocBOrderLink.setModifieddate(new Date());
                ocBOrderLink.setModifiername(user.getName());
            } else {
                User rootUser = SystemUserResource.getRootUser();
                ocBOrderLink.setAdOrgId((long) rootUser.getOrgId());
                ocBOrderLink.setOwnername(rootUser.getEname());
                ocBOrderLink.setOwnerename(rootUser.getEname());
                ocBOrderLink.setAdClientId((long) rootUser.getClientId());
                ocBOrderLink.setOwnerid(Long.valueOf(rootUser.getId()));
                ocBOrderLink.setCreationdate(new Date());
                ocBOrderLink.setModifierid(Long.valueOf(rootUser.getId()));
                ocBOrderLink.setModifieddate(new Date());
                ocBOrderLink.setModifiername(rootUser.getName());
            }
            //同步全链路日志平台返回结果
            boolean reuslt = omsOrderSyncFullLogService.doSyncLinkPlatform(ocBOrderLink);
            String message = "";
            if (reuslt) {
                ocBOrderLink.setSyncStatus(SyncStatus.SYNCFAILD.toInteger());
                message = "订单链路日志同步成功";
            } else {
                ocBOrderLink.setSyncStatus(SyncStatus.SYNCFAILD.toInteger());
                message = "订单链路日志同步失败";
            }
            omsOrderSyncFullLogService.saveOrderLinkLog(ocBOrderLink);
            omsOrderLogService.addUserOrderLog(ocBOrderLink.getOcBOrderId(), "", OrderLogTypeEnum.FULL_LINKLOG.getKey(),
                    "订单OrderId" + ocBOrderLink.getOcBOrderId() + message, "", "", user);
            if (!SpecialElasticSearchUtil.indexExists(indexName)) {
                SpecialElasticSearchUtil.indexCreate(OcBOrderLink.class);
            }
            SpecialElasticSearchUtil.indexDocument(indexName, OC_B_ORDER_LINK_TYPE_NAME,
                    ocBOrderLink, autoId);

        } catch (Exception ex) {
            log.error(LogUtil.format("同步全链路日志平台失败!异常-->,订单OrderId=", ocBOrderLink.getOcBOrderId()),
                    Throwables.getStackTraceAsString(ex));
        }
    }

    /**
     * 新批量调用传全链
     *
     * @param ocBOrders      订单集合
     * @param backflowStatus 参考BackflowStatus
     */
    public void addOrderFinkLogsThread(List<OcBOrder> ocBOrders, String backflowStatus) {
//        ExecutorService threadPool = AutoSplitOrderThreadPool.getExecutorService();
//        threadPool.submit(new Thread() {
//            public void run() {
//                addOrderFinkLogs(ocBOrders, backflowStatus);
//            }
//        });
        addOrderFinkLogs(ocBOrders, backflowStatus);
//        if (backflowStatus.equals(BackflowStatus.QIMEN_ERP_CHECK.parseValue())) {
////            // yuChengHeEventExecute(ocBOrders);
////        }

    }

    /**
     * 新批量调用传全链路日志
     *
     * @param ocBOrders      订单集合
     * @param backflowStatus 参考BackflowStatus
     */
    public void addOrderFinkLogs(List<OcBOrder> ocBOrders, String backflowStatus) {

        try {
            if (CollectionUtils.isEmpty(ocBOrders)) {
                return;
            }
            List<OrderFullLinkModel> linkResultList = new ArrayList<>(ocBOrders.size());
            for (OcBOrder orderInfo : ocBOrders) {
                if (PlatFormEnum.TAOBAO.getCode().equals(orderInfo.getPlatform()) || PlatFormEnum.TAOBAO_DEAL.getCode().equals(orderInfo.getPlatform()) || PlatFormEnum.TAOBAO_DISTRIBUTION.getCode().equals(orderInfo.getPlatform())) {
                    long autoId = ModelUtil.getSequence(LOG_TABLE_NAMAE);
                    //同步日志信息到平台l
                    OrderFullLinkModel orderFullLinkModel = new OrderFullLinkModel();
                    //链路主表 ocBOrderLinkId
                    orderFullLinkModel.setId(autoId);
                    //分库建
                    orderFullLinkModel.setOcBOrderId(orderInfo.getId());
                    //平台单号
                    orderFullLinkModel.setTid(orderInfo.getTid());
                    //卖家昵称
                    orderFullLinkModel.setSellerNick(orderInfo.getCpCShopSellerNick());
                    //回流状态
                    orderFullLinkModel.setStatus(backflowStatus);
                    linkResultList.add(orderFullLinkModel);
                } else {
                    continue;
                }
            }
            //同步全链路日志平台返回结果
            boolean reuslt = omsOrderSyncFullLogService.batchSyncLinkPlatform(linkResultList);
            String message = "";
            if (reuslt) {
                message = "订单链路日志同步成功";
            } else {
                message = "订单链路日志同步失败";
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
     * 新批量调用传全链路日志
     *
     * @param ocBOrders 订单集合
     */
    public void yuChengHeEventExecute(List<OcBOrder> ocBOrders) {

        try {
            if (CollectionUtils.isEmpty(ocBOrders)) {
                return;
            }
            List<YuChengHeOrderLogEventRequest> batchLogEventList = new ArrayList<>(ocBOrders.size());
            for (OcBOrder orderInfo : ocBOrders) {
                if (PlatFormEnum.TAOBAO.getCode().equals(orderInfo.getPlatform()) || PlatFormEnum.TAOBAO_DEAL.getCode().equals(orderInfo.getPlatform()) || PlatFormEnum.TAOBAO_DISTRIBUTION.getCode().equals(orderInfo.getPlatform())) {
                    YuChengHeOrderLogEventRequest yuChengHeOrderLogEventRequest = new YuChengHeOrderLogEventRequest();
                    yuChengHeOrderLogEventRequest.setTradeId(orderInfo.getTid());
                    yuChengHeOrderLogEventRequest.setOperateType(YuChengHeOperateTypeEnum.ORDER_AUDIT.getName());
                    batchLogEventList.add(yuChengHeOrderLogEventRequest);
                }
            }

            if (CollectionUtils.isNotEmpty(batchLogEventList)) {
                YuChengHeRequest yuChengHeRequest = new YuChengHeRequest();
                yuChengHeRequest.setType(2);
                yuChengHeRequest.setBatchLogEventList(batchLogEventList);
                ValueHolderV14 valueHolderV14 = ipRpcService.yuChengHeEventExecute(yuChengHeRequest, SystemUserResource.getRootUser());
                if (valueHolderV14 != null) {
                    if (valueHolderV14.isOK()) {
                        log.debug(LogUtil.format("御城河订单审核日志成功!"));
                    } else {
                        log.error(LogUtil.format("御城河订单审核日志失败!") + valueHolderV14.getMessage());
                    }
                } else {
                    log.error(LogUtil.format("御城河订单审核日志返回结果为null"));
                }

            }


        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
