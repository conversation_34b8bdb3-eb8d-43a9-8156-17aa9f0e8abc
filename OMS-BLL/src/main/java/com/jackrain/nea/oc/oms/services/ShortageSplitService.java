package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 缺货拆单
 */
@Slf4j
@Component
public class ShortageSplitService {

    @Autowired
    private OmsOrderAutoSplitService orderAutoSplitService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderAutoSearchWarehouseService omsOrderAutoSearchWarehouseService;

    @Autowired
    private OmsOrderAutoSplitByStockService orderAutoSplitByStockService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    public ValueHolderV14<JSONArray> splitShortageOrder(JSONObject obj, User user) {
        ValueHolderV14<JSONArray> v14 = new ValueHolderV14<>();
        JSONArray errorMessage = new JSONArray();
        JSONArray resJson = obj.getJSONArray("ids");
        List<Long> orderIdList = new ArrayList<>();
        if (resJson != null && !resJson.isEmpty()) {
            for (int i = 0; i < resJson.size(); i++) {
                Long orderId = resJson.getLong(i);
                orderIdList.add(orderId);
            }
        }
        // 查询所有的店铺库存同步策略缓存到map中
        Map<Long, List<Long>> shopSyncStockStrategyMap = orderAutoSplitService.loadshopSyncStockStrategyToMap();
        Map<Long, StCShopStrategyDO> shopStrategyMap = orderAutoSplitByStockService.loadShopStrategyToMap();
        if (shopSyncStockStrategyMap == null || shopSyncStockStrategyMap.isEmpty()
                || shopStrategyMap == null || shopStrategyMap.isEmpty()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("系统繁忙，请稍后再处理！");
            return v14;
        }
        int fail = 0;
        for (Long orderId : orderIdList) {
            JSONObject jsonObject = new JSONObject();
            //调用缺货重新占单
            ValueHolderV14 vH14 = omsOrderAutoSearchWarehouseService.checkAutoSerachOrderInfo(user,
                    orderId, false);
            if (vH14.isOK()) {
//                insertOrderLog(orderId, null, OrderLogTypeEnum.RE_OCCUPANCY.getKey(), "缺货重新占单成功！", "", "", user);
                continue;
            }
            // 拆单结果处理
            vH14 = orderAutoSplitByStockService.doSplitOrder(user, orderId, shopSyncStockStrategyMap,
                    shopStrategyMap);
            if (vH14.isOK()) {
                v14.setCode(ResultCode.SUCCESS);
                v14.setMessage(Resources.getMessage("成功", user.getLocale()));
            } else {
                fail++;
                jsonObject.put("code", -1);
                jsonObject.put("message", vH14.getMessage());
                jsonObject.put("objid", orderId);
                errorMessage.add(jsonObject);
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(vH14.getMessage());
            }
        }
        v14.setData(errorMessage);
        if (orderIdList.size() == 1) {
            return v14;
        }
        if (fail == 0) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage(Resources.getMessage("成功", user.getLocale()));
        } else {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(Resources.getMessage("订单拆单成功" + (orderIdList.size() - fail) + "条，失败了" + fail + "条",
                    user.getLocale()));
        }
        return v14;
    }
}

