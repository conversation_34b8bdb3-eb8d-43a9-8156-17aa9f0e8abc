package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.jackrain.nea.oc.oms.mapper.StCBoxStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCBoxStrategyEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName BoxStrategyService
 * @Description 箱型策略处理
 * <AUTHOR>
 * @Date 2024/2/1 14:25
 * @Version 1.0
 */
@Slf4j
@Component
public class BoxStrategyService {

    @Autowired
    private StCBoxStrategyMapper boxStrategyMapper;

    public static void main(String[] args) {
        String splitOrderRule = "110100000025=1";
        String[] skuAndNumArr = splitOrderRule.split("\\|");
        System.err.println(JSONUtil.toJsonStr(skuAndNumArr));
    }

    public String boxStrategyMatch(Collection<List<OcBOrderItem>> listCollection, List<OcBOrderItem> ocBOrderItemList, String boxStrategyLogMessage) {
        List<String> skuQtyStrList = new ArrayList<>();
        // 对入参列表进行深拷贝，避免修改原始数据
        List<OcBOrderItem> newOrderItemList = ocBOrderItemList.stream().map(item -> {
            OcBOrderItem newItem = new OcBOrderItem();
            BeanUtils.copyProperties(item, newItem);
            return newItem;
        }).collect(Collectors.toList());

        // 先过滤掉newOrderItemList中的 getRefundStatus为6的数据
        newOrderItemList = newOrderItemList.stream().filter(ocBOrderItem -> ocBOrderItem.getRefundStatus() == null || !ObjectUtil.equal(6, ocBOrderItem.getRefundStatus())).collect(Collectors.toList());

        // 按SKU分组并合并数量
        Map<String, List<OcBOrderItem>> skuGroups = newOrderItemList.stream()
                .collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuEcode));

//        // 检查是否存在相同SKU且数量大于等于2的情况
//        boolean hasDuplicateSku = skuGroups.values().stream().anyMatch(list -> list.size() >= 2);
//        if (!hasDuplicateSku) {
//            listCollection.add(ocBOrderItemList);
//            return boxStrategyLogMessage;
//        }

        // 合并相同SKU的数量并保存原始订单项
        for (Map.Entry<String, List<OcBOrderItem>> entry : skuGroups.entrySet()) {
            List<OcBOrderItem> items = entry.getValue();
            BigDecimal totalQty = items.stream()
                    .map(OcBOrderItem::getQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 将sku+合并后的数量进行拼接
            skuQtyStrList.add(entry.getKey() + "=" + totalQty.intValue());
        }
        if (CollectionUtil.isEmpty(skuQtyStrList)) {
            listCollection.add(newOrderItemList);
            return boxStrategyLogMessage;
        }
        // 对数据进行排序
        Collections.sort(skuQtyStrList);
        // 去匹配有没有匹配到的策略
        String orderGoods = String.join(",", skuQtyStrList);
        StCBoxStrategyEntity boxStrategyEntity = boxStrategyMapper.selectEnableStraetgy(orderGoods);
        if (ObjectUtil.isNull(boxStrategyEntity)) {
            listCollection.add(newOrderItemList);
            return boxStrategyLogMessage;
        }
        boxStrategyLogMessage = "命中了箱型拆单策略:" + boxStrategyEntity.getStrategyCode();

        String splitOrderRules = boxStrategyEntity.getSplitOrderRules();
        String[] splitOrderRuleArr = splitOrderRules.split("/");
        for (String splitOrderRule : splitOrderRuleArr) {
            List<OcBOrderItem> addOrderItemList = new ArrayList<>();
            // 根据"|" 来进行分割
            String[] skuAndNumArr = splitOrderRule.split("\\|");
            for (String skuAndNum : skuAndNumArr) {
                String[] skuNumArr = skuAndNum.split("=");
                String skuCode = skuNumArr[0];
                String num = skuNumArr[1];
                // 获取相同SKU的所有订单项，并按数量升序排序
                List<OcBOrderItem> sameSkuItems = skuGroups.get(skuCode);
                sameSkuItems.sort(Comparator.comparing(OcBOrderItem::getQty));

                // 计算需要拆分的总数量
                int remainingQty = Integer.parseInt(num);
                List<OcBOrderItem> splitItems = new ArrayList<>();

                // 从最小数量开始分配
                for (OcBOrderItem item : sameSkuItems) {
                    if (remainingQty <= 0) {
                        break;
                    }

                    int itemQty = item.getQty().intValue();
                    int splitQty = Math.min(itemQty, remainingQty);

                    if (splitQty > 0) {
                        OcBOrderItem addOcBOrderItem = new OcBOrderItem();
                        BeanUtils.copyProperties(item, addOcBOrderItem);
                        addOcBOrderItem.setQty(new BigDecimal(splitQty));
                        splitItems.add(addOcBOrderItem);
                        
                        // 更新原始订单项的数量
                        item.setQty(new BigDecimal(itemQty - splitQty));
                        remainingQty -= splitQty;
                    }
                }
                skuGroups.put(skuCode, sameSkuItems);
                addOrderItemList.addAll(splitItems);
            }
            listCollection.add(addOrderItemList);
        }
        return boxStrategyLogMessage;
    }
}
