package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBToBeConfirmedTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * method:
 * description: 重新寻源
 *
 * <AUTHOR>
 * @data 2021/9/16 4:00 下午
 * @return
 */
@Slf4j
@Component
public class AgainOccupyStockService {

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    SgRpcService sgRpcService;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBToBeConfirmedTaskMapper toBeConfirmedTaskMapper;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;

    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;
    @Autowired
    private AgainOccupyStockService againOccupyStockService;

    @Resource
    private ThreadPoolTaskExecutor againOccupyPollExecutor;

    /**
     * method:
     * description: 取消逻辑发货单，单子变为待分配，重新寻源
     *
     * @return
     * <AUTHOR>
     * @data 2021/9/16 4:01 下午
     */
    public ValueHolder againOccupyStock(JSONObject obj, User operateUser) {

        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", operateUser.getLocale()));
        }
        List<List<Object>> batchAgainOccupyList = Lists.partition(ids, 50);
        List<Future<BatchAgainOccupyResult>> faildCountList = new ArrayList<>();
        for (List<Object> batchAgainOccupy : batchAgainOccupyList) {
            faildCountList.add(againOccupyPollExecutor.submit(new BatchAgainOccupy(batchAgainOccupy, operateUser)));
        }

        AtomicInteger failureCount = new AtomicInteger(0);
        JSONArray errorMessage = new JSONArray();
        for (Future<BatchAgainOccupyResult> failCount : faildCountList) {
            BatchAgainOccupyResult againOccupyResult = null;
            try {
                againOccupyResult = failCount.get();
                failureCount.addAndGet(againOccupyResult.getFailNum());
                errorMessage.addAll(againOccupyResult.getErrorMessage());
            } catch (InterruptedException e) {
                log.error(LogUtil.format("批量重新寻源InterruptedException异常：{}"), Throwables.getStackTraceAsString(e));
            } catch (ExecutionException e) {
                log.error(LogUtil.format("批量重新寻源ExecutionException异常：{}"), Throwables.getStackTraceAsString(e));
            }
        }

        ValueHolder vh = new ValueHolder();
        vh.put("data", errorMessage);
        if (failureCount.get() == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("手工重新寻源成功", operateUser.getLocale()));
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("手工重新寻源成功" + (ids.size() - failureCount.get()) + "条，失败了" + failureCount.get() + "条", operateUser.getLocale()));
        }
        return vh;

    }

    @Data
    class BatchAgainOccupyResult {
        private JSONArray errorMessage;
        private Integer failNum;
    }

    class BatchAgainOccupy implements Callable<BatchAgainOccupyResult> {

        User user;
        List<Object> ids;

        public BatchAgainOccupy(List<Object> ids, User user) {
            this.user = user;
            this.ids = ids;
        }

        @Override
        public BatchAgainOccupyResult call() throws Exception {
            BatchAgainOccupyResult againOccupyResult = new BatchAgainOccupyResult();
            JSONArray errorMessage = new JSONArray();
            Integer fail = 0;
            for (int i = 0; i < ids.size(); i++) {
                JSONObject jsonObject = new JSONObject();
                Long orderId = Long.valueOf(ids.get(i).toString());
                try {
                    againOccupyStockService.againOccupyOrder(orderId, user);
                } catch (Exception e) {
                    log.info(LogUtil.format("手动重新寻源，orderId:{},失败原因：{}", orderId), orderId, Throwables.getStackTraceAsString(e));
                    jsonObject.put("code", -1);
                    jsonObject.put("message", e.getMessage());
                    jsonObject.put("objid", ids.get(i));
                    errorMessage.add(jsonObject);
                    fail++;
                    String message = e.getMessage();
                    if (e.getMessage() != null && message.length() > 1000) {
                        message = message.substring(0, 1000);
                    }
                    insertOrderLog(orderId, null, OrderLogTypeEnum.AGAIN_OCCUPY_STOCK.getKey(), message, null, null, user);
                }
            }
            againOccupyResult.setFailNum(fail);
            againOccupyResult.setErrorMessage(errorMessage);
            return againOccupyResult;
        }
    }

    /**
     * method:
     * description: 订单从待审核变成待分配
     *
     * @return
     * <AUTHOR>
     * @data 2021/9/16 4:06 下午
     */
    @Transactional(rollbackFor = {Exception.class})
    public ValueHolder againOccupyOrder(Long orderId, User operateUser) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("againOccupyOrder 手工重新寻源 start orderId={}", orderId), orderId);
        }
        ValueHolder vh = new ValueHolder();
        // 给订单加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = this.ocBOrderMapper.selectByID(orderId);
                if (ocBOrder == null) {
                    throw new NDSException("订单不存在");
                }
                if (!omsBusinessTypeStService.isAutoOccupy(ocBOrder)) {
                    throw new NDSException("该业务类型订单未开启自动寻源");
                }
                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
                    //修改订单状态为待分配，重新占单
                    this.updateStatus(ocBOrder, operateUser);
                    //查询原单明细
                    List<OcBOrderItem> ocBOrderItemList = orderItemMapper.selectOrderItemListOccupy(orderId);
                    log.info("order{}.开始调用库存取消接口", orderId);
                    //封装数据
                    SgOmsShareOutRequest request = buildSgOmsShareOutRequest(ocBOrder, ocBOrderItemList, operateUser);
                    log.info("调用sg取消库存封装数据为：{}", JSON.toJSONString(request));
                    ValueHolderV14 sgValueHolder = sgRpcService.voidSgOmsShareOut(request, ocBOrder, ocBOrderItemList);
                    AssertUtil.assertException(!sgValueHolder.isOK(), "释放库存失败");
                    log.info("调用sg取消库存返回接口数据为：{}", JSON.toJSONString(sgValueHolder));
                    //加入中间表
                    omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null, 0);
                    insertOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.AGAIN_OCCUPY_STOCK.getKey(),
                            "手工重新寻源成功！", null, null, operateUser);
                    vh.put("code", ResultCode.SUCCESS);
                    vh.put("message", Resources.getMessage("手工重新寻源成功", operateUser.getLocale()));
                } else if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())) {
                    if (ocBOrder.getIsDetention() != null && AdvanceConstant.DETENTION_STATUS_1.equals(ocBOrder.getIsDetention())) {
                        throw new NDSException("订单卡单状态,不允许重新寻源！");
                    }
                    //修改订单状态为待分配，重新占单
                    this.updateStatus(ocBOrder, operateUser);
                    //加入中间表
                    omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null, 0);
                    insertOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.AGAIN_OCCUPY_STOCK.getKey(),
                            "手工缺货重新寻源成功！", null, null, operateUser);
                    vh.put("code", ResultCode.SUCCESS);
                    vh.put("message", Resources.getMessage("手工重新寻源成功", operateUser.getLocale()));
                } else {
                    throw new NDSException("orderId " + orderId + "订单非待审核/缺货状态，不允许重新寻源");
                }
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", operateUser.getLocale()));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("手工重新寻源失败,OrderId={},异常信息={}", orderId), orderId, Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        } finally {
            redisLock.unlock();
        }
        return vh;
    }

    //更新中间表状态
    public void updateOccupyTask(OcBOrder order) {
        omsOccupyTaskService.addOcBOccupyTask(order, null);
    }

    /**
     * description:修改订单状态 加入审核中间表
     *
     * @Author: liuwenjin
     * @Date 4:46 下午
     */
    private void updateStatus(OcBOrder ocBOrder, User user) {
        ocBOrderMapper.updateWarehouse(ocBOrder.getId());
    }

    /**
     * method:
     * description: 新增日志
     *
     * @return
     * <AUTHOR>
     * @data 2021/9/16 4:11 下午
     */
    private void insertOrderLog(long orderId, String billNo, String logType, String logMessage, String param,
                                String errorMessage, User operateUser) {
        //调用添加订单日志
        try {
            orderLogService.addUserOrderLog(orderId, billNo, logType, logMessage, null, null, operateUser);
        } catch (Exception e) {
            log.error(LogUtil.format("新增订单日志失败，失败原因:{}", orderId), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * <AUTHOR>
     * @Date 14:31 2021/7/30
     * @Description 封装sg、所用的数据
     */
    public SgOmsShareOutRequest buildSgOmsShareOutRequest(OcBOrder orderInfo, List<OcBOrderItem> ocBOrderItemList, User user) {
        SgOmsShareOutRequest request = new SgOmsShareOutRequest();
        request.setSourceBillId(orderInfo.getId());
        request.setSourceBillNo(orderInfo.getBillNo());
        request.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        request.setLoginUser(user);
        List<SgOmsShareOutItemRequest> itemRequestList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            SgOmsShareOutItemRequest sgOmsShareOutItemRequest = new SgOmsShareOutItemRequest();
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            sgOmsShareOutItemRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            sgOmsShareOutItemRequest.setQtyPreout(ocBOrderItem.getQty());
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            itemRequestList.add(sgOmsShareOutItemRequest);
        }
        request.setItemRequestList(itemRequestList);

        return request;
    }

    /**
     * description:
     * method: 占单中间表
     *
     * @return
     * <AUTHOR>
     * @data 2021/9/16 5:10 下午
     */
    private void createToBeConfirmedTask(Long oredrId) {

        int n = toBeConfirmedTaskMapper.selectCount(new QueryWrapper<OcBToBeConfirmedTask>().lambda().eq(OcBToBeConfirmedTask::getOrderId, oredrId));
        if (n > 0) {
            List<Long> ids = new ArrayList<>();
            ids.add(oredrId);
            toBeConfirmedTaskService.updateToBeConfirmedTask(ids);
        } else {
            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(oredrId);
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
        }
    }
}
