package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.TaskParam;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.request.o2o.SaleReturnRequest;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Desc : 门店发货销退单同步
 * <AUTHOR> xiWen
 * @Date : 2020/8/14
 */
@Slf4j
@Component
public class SaleReturnSyncNextTaoService {

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    public List<SaleReturnRequest> saleReturnOrderTransfer(List<OcBReturnOrder> returnOrders,
                                                           List<OcBReturnOrderRefund> returnRefunds, TaskParam taskParam) {
        Map<Long, String> errorMap = new HashMap<>();

        // 1. 查询入库实体仓编码
        Map<Long, CpCPhyWarehouse> phyWarehouseMap = queryInPhyWareHouse(returnOrders);
        //assertIlLegal(phyWarehouseMap, "查询所有入库实体仓编码结果Null");
        if (phyWarehouseMap == null) {
            return null;
        }

        // 2. 查询出库通知单号
        Map<Long, String> outNoticeMap = rpcQueryOutNotices(returnOrders);
        //assertIlLegal(outNoticeMap, "查询所有出库通知单号结果Null");
        if (outNoticeMap == null) {
            return null;
        }

        // 3. 查询平台信息
        Map<Long, CpCPlatform> pfMap = rpcQueryPlatForm();
        //assertIlLegal(outNoticeMap, "查询所有平台信息结果Null");
        if (pfMap == null) {
            return null;
        }

        // 4. 子明细根据退单编号分组
        Map<Long, List<OcBReturnOrderRefund>> itemMap = returnRefunds
                .stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getOcBReturnOrderId, Collectors.toList()));
        List<SaleReturnRequest> saleReturnRequests = new ArrayList<>(returnOrders.size());
        SaleReturnRequest saleReturnRequest;
        SaleReturnRequest.SaleReturnItem saleReturnItem;
        List<SaleReturnRequest.SaleReturnItem> itemRequests;
        try {
            index:
            for (OcBReturnOrder order : returnOrders) {

                if (!reVerifyReturn(order, errorMap, taskParam)) {
                    continue;
                }

                // 2. 赋值
                saleReturnRequest = new SaleReturnRequest();
                saleReturnRequest.setLineId(String.valueOf(order.getId()));
                saleReturnRequest.setRefundId(order.getBillNo());
                saleReturnRequest.setSourcecode(order.getOrigSourceCode());
                CpCPhyWarehouse warehouse = phyWarehouseMap.get(order.getCpCPhyWarehouseInId());
                if (warehouse == null) {
                    recordError(order.getId(), "入库实体仓:null", errorMap, taskParam);
                    continue;
                }
                if (warehouse.getEcode() == null) {
                    recordError(order.getId(), "入库实体仓编码:null", errorMap, taskParam);
                    continue;
                }
                saleReturnRequest.setRefundWarehouse(warehouse.getEcode());

                Long origOrderId = order.getOrigOrderId();
                if (origOrderId == null) {
                    recordError(order.getId(), "原始订单:null", errorMap, taskParam);
                    continue;
                }
                String outNoticeNo = outNoticeMap.get(origOrderId);
                if (outNoticeNo == null) {
                    recordError(order.getId(), "出库通知单号null", errorMap, taskParam);
                    continue;
                }
                saleReturnRequest.setOrigOrderId(outNoticeNo);

                CpCPlatform platform = pfMap.get(Long.valueOf(order.getPlatform() == null ? 0L : order.getPlatform()));
                if (platform == null) {
                    recordError(order.getId(), "平台信息null", errorMap, taskParam);
                    continue;
                }
                saleReturnRequest.setLyorgDm(platform.getEcode());
                saleReturnRequest.setLyorgMc(platform.getEname());

                SimpleDateFormat sdf = formatLocal.get();
                if (inValidParam(order.getCreationdate())) {
                    recordError(order.getId(), "创建时间null", errorMap, taskParam);
                    continue;
                }
                saleReturnRequest.setCreationDate(sdf.format(order.getCreationdate()));
                if (inValidParam(order.getInTime())) {
                    recordError(order.getId(), "入库时间null", errorMap, taskParam);
                    continue;
                }
                saleReturnRequest.setInTime(sdf.format(order.getInTime()));
                if (inValidParam(order.getCpCShopEcode()) || inValidParam(order.getCpCShopTitle())) {
                    recordError(order.getId(), "店铺信息存在null", errorMap, taskParam);
                    continue;
                }
                saleReturnRequest.setLyzdDm(order.getCpCShopEcode());
                saleReturnRequest.setLyzdMc(order.getCpCShopTitle());

                if (inValidParam(order.getStoreCode())) {
                    recordError(order.getId(), "门店编码为空", errorMap, taskParam);
                    continue;
                }
                saleReturnRequest.setXdzdDm(order.getStoreCode());

                List<OcBReturnOrderRefund> items = itemMap.get(order.getId());
                if (CollectionUtils.isEmpty(items)) {
                    recordError(order.getId(), "订单明细为空", errorMap, taskParam);
                    continue;
                }
                itemRequests = new ArrayList<>();
                for (OcBReturnOrderRefund item : items) {
                    saleReturnItem = new SaleReturnRequest.SaleReturnItem();

                    if (isIlLegal(item.getQtyIn())) {
                        recordError(order.getId(), (item.getId() + ": 明细入库数量不合法"), errorMap, taskParam);
                        continue index;
                    }
                    // 商品代码	根据退货商品SKU从商品中心获取，商品中心提供获取服务
                    saleReturnItem.setGoodsCode(item.getPsCProEcode());
                    // 颜色代码	根据退货商品SKU从商品中心获取，商品中心提供获取服务
                    saleReturnItem.setColorCode(item.getPsCClrEcode());
                    // 尺码代码	根据退货商品SKU从商品中心获取，商品中心提供获取服务
                    saleReturnItem.setSizeCode(item.getPsCSizeEcode());
                    // 数量	退换货单退货明细相应商品的订单数量
                    saleReturnItem.setQty(item.getQtyIn());
                    // 单件退货金额	退换货单退货明细相应商品的单件退货金额
                    saleReturnItem.setAmtRefundSingle(item.getAmtRefundSingle());
                    // 退货金额	退换货单退货明细相应商品的退货金额
                    saleReturnItem.setAmtRefundSingle(item.getAmtRefund());
                    // 赠品标识	退换货单退货明细相应商品的赠品状态
                    saleReturnItem.setGiftSign(String.valueOf(item.getGiftType() == null ? 0 : item.getGiftType()));

                    itemRequests.add(saleReturnItem);
                }
                if (inValidParam(itemRequests)) {
                    recordError(order.getId(), "入库明细数量为空", errorMap, taskParam);
                    continue index;
                }
                saleReturnRequest.setCustomerId(warehouse.getWmsAccount());
                saleReturnRequest.setItem(itemRequests);
                saleReturnRequests.add(saleReturnRequest);
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("单据转换参数时发生异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            assertIlLegal(null, "单据转换参数时发生异常");
        } finally {
            formatLocal.remove();
        }
        return saleReturnRequests;
    }

    private boolean reVerifyReturn(OcBReturnOrder returnOrder, Map<Long, String> errorMap, TaskParam sql) {
        // 1.  todo 再次校验是否是pos需要的单据
        if (!ReturnStatusEnum.COMPLETION.getVal().equals(returnOrder.getReturnStatus())) {
            recordError(returnOrder.getId(), "退换货订单退货状态未完成", errorMap, sql);
            return false;
        }
        return true;
    }

    private boolean isIlLegal(BigDecimal d) {
        return d == null || d.compareTo(BigDecimal.ZERO) == 0;
    }

    private ThreadLocal<SimpleDateFormat> formatLocal = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd HH:mm:ss"));

    /**
     * 查询实体仓
     *
     * @param returnOrders
     * @return
     */
    private Map<Long, CpCPhyWarehouse> queryInPhyWareHouse(List<OcBReturnOrder> returnOrders) {

        Map<Long, CpCPhyWarehouse> warehouseMap = new HashMap<>();
        try {
            List<Long> phyWarehouseIds = returnOrders.stream()
                    .map(x -> x.getCpCPhyWarehouseInId()).distinct().collect(Collectors.toList());
            assertIlLegal(phyWarehouseIds, "查询入库实体仓参数为空");
            for (Long phyId : phyWarehouseIds) {
                if (warehouseMap.containsKey(phyId)) {
                    continue;
                }
                CpCPhyWarehouse warehouse = cpRpcService.queryByWarehouseId(phyId);
                if (warehouse == null || warehouse.getEcode() == null) {
                    continue;
                }
                warehouseMap.put(phyId, warehouse);
            }
            assertIlLegal(warehouseMap, "入库实体仓查询转换提取为空");
        } catch (Exception ex) {
            log.error(LogUtil.format("组装实体仓异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            return null;
        }
        return warehouseMap;
    }

    /**
     * 根据退单查询.出库通知单
     *
     * @return
     */
    private Map<Long, String> rpcQueryOutNotices(List<OcBReturnOrder> returnOrders) {
        try {

            List<Long> origIds = returnOrders.stream().filter(o -> Objects.nonNull(o.getOrigOrderId()))
                    .map(OcBReturnOrder::getOrigOrderId).distinct().collect(Collectors.toList());
            assertIlLegal(origIds, "退换货单提取原始订单Id为空");

//            ValueHolderV14<List<SgBPhyOutNotices>> vh = sgRpcService.querySgPhyOutNoticeByOrderId(origIds);
//            if (!vh.isOK()) {
//                assertIlLegal(null, vh.getMessage());
//            }
//            List<SgBPhyOutNotices> outNotices = vh.getData();
//            Map<Long, String> outNoticeMap = outNotices.stream()
//                    .collect(Collectors.toMap(SgBPhyOutNotices::getSourceBillId, SgBPhyOutNotices::getBillNo, (x, y) -> y));
//            assertIlLegal(outNoticeMap, "出库通知单转换提取为空");
            return null;
        } catch (Exception ex) {
            log.error(LogUtil.format("组装出库通知单异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            return null;
        }

    }

    /**
     * 查询所有平台信息
     *
     * @return id, 平台信息
     */
    private Map<Long, CpCPlatform> rpcQueryPlatForm() {

        try {

            List<CpCPlatform> cpCPlatforms = cpRpcService.queryPlatform(null);
            if (cpCPlatforms == null) {
                return null;
            }
            return cpCPlatforms.stream().collect(Collectors.toMap(CpCPlatform::getId, o -> o));
        } catch (Exception ex) {
            log.error(LogUtil.format("组装平台信息异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            return null;
        }

    }


    /**
     * 异常信息记录
     *
     * @param id
     * @param msg
     * @param errorMap
     * @param sql
     */
    private void recordError(Long id, String msg, Map<Long, String> errorMap, TaskParam sql) {
        errorMap.put(id, msg);
        sql.getValidKeys().remove(id);
    }

    /**
     * level debug recorder
     *
     * @param msg log message
     */
    private void logDebug(String msg, Object... params) {

        if (log.isDebugEnabled()) {
            if (params.length == 0) {
                log.debug(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString());
            } else {
                log.debug(new StringBuilder(this.getClass().getSimpleName()).append(".").append(msg).toString(), params);
            }
        }
    }

    private boolean inValidParam(Object o) {
        if (o == null) {
            return true;
        } else if (o instanceof String) {
            if (StringUtils.isBlank((CharSequence) o)) {
                return true;
            }
        } else if (o instanceof List) {
            if (CollectionUtils.isEmpty((Collection) o)) {
                return true;
            }
        } else if (o instanceof Map) {
            if (MapUtils.isEmpty((Map) o)) {
                return true;
            }
        }
        return false;
    }

    private void assertIlLegal(Object o, String msg) {

        if (o == null) {
            throw new NDSException(msg);
        } else if (o instanceof String) {
            if (StringUtils.isBlank((CharSequence) o)) {
                throw new NDSException(msg);
            }
        } else if (o instanceof List) {
            if (CollectionUtils.isEmpty((Collection) o)) {
                throw new NDSException(msg);
            }
        } else if (o instanceof Map) {
            if (MapUtils.isEmpty((Map) o)) {
                throw new NDSException(msg);
            }
        }
    }

}
