package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderInvoiceInformMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.result.QueryOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrderInvoiceInform;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 校验.记录开票.全渠道订单
 *
 * @author: xiWen.z
 * create at: 2019/7/24 0024
 */
@Component
@Slf4j
public class OcBOrderCheckRecordInvoice {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderInvoiceInformMapper ocBOrderInvoiceInformMapper;

    /**
     * 记录开票.订单.校验
     *
     * @param paramObj Oc_b_order.id
     * @param usr      User
     * @return vh14
     */
    public ValueHolderV14 judgedRecordInvoicing(JSONObject paramObj, User usr) {

        recordDebugLog("###judgedRecordInvoicing.Start...");
        // 1.校验
        boolean b = validateParam(paramObj, usr);
        if (!b) {
            recordDebugLog("###validateParam: 参数不正确");
            return returnValue("参数不正确", false, usr);
        }

        // 2. 解析. 提取. 查询.处理
        return mainProgram(paramObj, usr);
    }


    /**
     * 参数.校验
     *
     * @param paramObj Oc_b_order.id
     * @param usr      User
     * @return bool
     */
    private boolean validateParam(JSONObject paramObj, User usr) {
        if (paramObj == null || usr == null) {
            return false;
        }
        Long id = paramObj.getLong("ID");
        if (id == null) {
            return false;
        }
        return true;
    }

    /**
     * 解析.提取. 查询
     *
     * @param paramObj JSONObject
     * @param usr      User
     * @return ValueHolderV14
     */
    private ValueHolderV14 mainProgram(JSONObject paramObj, User usr) {
        Long id = paramObj.getLong("ID");
        QueryOrderResult queryOrderResult = ocBOrderMapper.selectQueryOrderResultById(id, OcBOrderConst.IS_ACTIVE_YES);
        OcBOrderInvoiceInform inform =
                ocBOrderInvoiceInformMapper.selectInvoiceInformByRefId(id, OcBOrderConst.IS_ACTIVE_YES);

        if (queryOrderResult == null) {
            return returnValue("未查询到数据信息", false, usr);
        }
        Integer orderStatus = queryOrderResult.getOrderStatus();
        if (orderStatus == null) {
            return returnValue("未知订单状态", false, usr);
        }
        if (OcOrderCheckBoxEnum.CHECKBOX_SYSTEM_INVALIDATION.getVal() == orderStatus) {
            return returnValue("该单据状态,不允许此项操作", false, usr);
        }
        Integer invoiceStatus = queryOrderResult.getInvoiceStatus();
        if (invoiceStatus != null) {
            if (!OcBorderListEnums.InvoiceStatusEnum.UN_REGISTER.getVal().equals(invoiceStatus)) {
                return returnValue("单据开票状态必须为未登记", false, usr);
            }
        }
        // 当前脏数据,情况下,默认允许,未存在开票状态,记录开票信息 todo
        List<JSONObject> headerTypeList = OcBorderListEnums.HeaderTypeEnum.getAllEnumToList();
        List<JSONObject> invoiceTypeList = OcBorderListEnums.InvoiceTypeEnum.getAllEnumToList();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("HEADER_TYPE_LIST", headerTypeList);
        jsonObject.put("INVOICE_TYPE_LIST", invoiceTypeList);
        jsonObject.put("QUERYORDERRESULT", queryOrderResult);
        jsonObject.put("OC_B_ORDER_INVOICE_INFORM", inform);
        ValueHolderV14 vh = returnValue("success", true, usr);
        vh.setData(jsonObject);
        return vh;
    }


    /**
     * @param msg       String
     * @param flag      bool
     * @param loginUser User
     * @return vh14
     */
    private ValueHolderV14 returnValue(String msg, boolean flag, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (flag) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        }
        return vh;
    }

    /**
     * 记录debug级别日志
     *
     * @param msg String
     */
    private void recordDebugLog(String msg) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(msg));
        }
    }
}
