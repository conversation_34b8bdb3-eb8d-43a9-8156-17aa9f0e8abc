package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.mapper.BillCopyMapper;
import com.jackrain.nea.oc.oms.mapper.IpBJitxOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderSplitRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJitxResetShipWorkflow;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.nums.OcOrderBillResetShipTypeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.service.StCBusinessTypeService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: jg.zhan
 * @Date: 2022/6/27 20:05
 * @Description: 批量补发订单
 */

@Component
@Slf4j
public class OmsOrderVopBatchResetShipService {
    @Autowired
    private SaveBillService saveBillService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private IpBJitxOrderMapper ipBJitxOrderMapper;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private IpBJitxResetShipWorkflowService ipBJitxResetShipWorkflowService;
    @Autowired
    private OmsOrderLockStockAndReOccupyStockService omsOrderLockStockAndReOccupyStockService;
    @Autowired
    private OmsOrderVopBatchResetShipService omsOrderVopBatchResetShipService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private StCBusinessTypeService stCBusinessTypeService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    BillCopyMapper billCopyMapper;
    @Autowired
    CpRpcService cpRpcService;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    /**
     * 批量补发订单
     *
     * @param user
     * @param restShipEnumByType
     * @param idList
     */
    public ValueHolderV14 batchRestShipBill(User user, OcOrderBillResetShipTypeEnum restShipEnumByType, List<Long> idList) {

        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectBatchIds(idList);
        if (ocBOrderList.size() != ocBOrderList.size()) {
            throw new NDSException("全部或部分订单不符合要求，不能补发。");
        }
        int successNum = 0;
        StringBuffer failMsgBuffer = new StringBuffer();
        for (Long iid : idList) {
            ValueHolderV14 valueHolderV14 = new ValueHolderV14();
            valueHolderV14 = omsOrderVopBatchResetShipService.reissueOrder(user, valueHolderV14, restShipEnumByType, iid, true);
            if (valueHolderV14.isOK()) {
                successNum++;
            } else {
                failMsgBuffer.append(valueHolderV14.getMessage()).append("\n");
            }
        }
        ValueHolderV14<Object> vh = new ValueHolderV14<>();
        if (successNum == 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("零售发货单补发失败！");
        } else {
            vh.setCode(ResultCode.SUCCESS);
            String message = "零售发货单补发，成功条数：" + successNum;
            if (idList.size() - successNum > 0) {
                message = message + "，失败条数：" + (idList.size() - successNum);
                message = message + "\n" + failMsgBuffer;
            }
            vh.setMessage(message);
        }
        return vh;
    }


    public ValueHolderV14 resetShip(String param, User user) {
        log.info("{}.resetShip start:{}", this.getClass().getSimpleName(), param);
        JSONObject jsonObject = JSONObject.parseObject(param);
        String ids = jsonObject.getString("IDS");
        Integer type = jsonObject.getInteger("TYPE");
        ValueHolderV14 vh = new ValueHolderV14();
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("补发订单成功!");
        //转成数组
        JSONArray idArray = JSON.parseArray(ids);
        if (CollectionUtils.isEmpty(idArray)) {
            return ValueHolderV14Utils.getFailValueHolder("请选择数据");
        }
        List<Long> idList = JSONObject.parseArray(ids, Long.class);
        if (CollectionUtils.isEmpty(idList)) {
            return ValueHolderV14Utils.getFailValueHolder("参数有误");
        }
        OcOrderBillResetShipTypeEnum restShipEnumByType = OcOrderBillResetShipTypeEnum.getRestShipEnumByType(type);
        if (idList.size() > 1) {
            //插入我的任务里
            AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
            asyncTaskBody.setTaskId(UUID.randomUUID().toString());
            asyncTaskBody.setMenu("批量补发订单");
            asyncTaskBody.setTaskType("批量补发订单");
            //任务开始
            asyncTaskManager.beforeExecute(user, asyncTaskBody);
            commonTaskExecutor.submit(() -> {
                JSONObject res = new JSONObject();
                res.put("message", "成功补发" + idList.size() + "条单据");
                res.put("code", ResultCode.SUCCESS);
                try {
                    //补发订单
                    ValueHolderV14 valueHolderV14 = omsOrderVopBatchResetShipService.batchRestShipBill(user, restShipEnumByType, idList);
                    log.info(" 批量补发订单任务返回结果为:{}", "批量补发订单执行成功！");
                    //任务完成
                    asyncTaskBody.setUrl(null);
                    res.put("message", valueHolderV14.getMessage());
                    res.put("code", valueHolderV14.getCode());
                } catch (Exception e) {
                    log.error(LogUtil.format("批量补发订单异常：{}", "Error"), e);
                    res.put("message", e.getMessage());
                    res.put("code", ResultCode.FAIL);
                }
                asyncTaskManager.afterExecute(user, asyncTaskBody, res);
            });
            vh.setCode(ResultCode.SUCCESS);
            vh.setData(asyncTaskBody.getId());
            vh.setMessage(Resources.getMessage("执行成功，批量补发订单任务开始！"));
            return vh;
        } else {
            Long id = idList.get(0);
            vh = reissueOrder(user, vh, restShipEnumByType, id, false);
        }
        return vh;
    }

    private ValueHolderV14 reissueOrder(User user, ValueHolderV14 vh, OcOrderBillResetShipTypeEnum restShipEnumByType, Long id, boolean isBatch) {
        log.info("补发业务处理入参：user={}，restShipEnumByType={}，id={}", user, restShipEnumByType.toString(), id);
        OmsOrderVopBatchResetShipService bean = ApplicationContextHandle.getBean(OmsOrderVopBatchResetShipService.class);
        switch (restShipEnumByType) {
            case UNKNOW:
                throw new NDSException("未知的补发订单类型！");
            case GIFT_RESET_SHIP:
                vh = bean.doOtherResetShipFunction(id, user, restShipEnumByType, isBatch);
                break;
            case JITX_ORDER_RESET_SHIP:
                vh = bean.doJITXFunction(id, user, restShipEnumByType, isBatch);
                break;
            case LOST_ORDER_RESET_SHIP:
                vh = bean.doOtherResetShipFunction(id, user, restShipEnumByType, isBatch);
                break;
            case ORDER_MISS_RESET_SHIP:
                vh = bean.doOtherResetShipFunction(id, user, restShipEnumByType, isBatch);
                break;
            case ORDER_WRONG_RESET_SHIP:
                log.info("omsOrderVopBatchResetShipService=" + bean);
                vh = bean.doOtherResetShipFunction(id, user, restShipEnumByType, isBatch);
                break;
            case OTHER_ORDER_RESET_SHIP:
                vh = bean.doOtherResetShipFunction(id, user, restShipEnumByType, isBatch);
        }
        return vh;
    }

    /**
     * 补发JITX订单
     *
     * @param ocBOrderId
     * @param user
     * @param restShipEnumByType
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 doJITXFunction(Long ocBOrderId, User user, OcOrderBillResetShipTypeEnum restShipEnumByType, boolean isBatch) {
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(ocBOrderId);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectUnSuccessRefund(ocBOrderId);
                //过滤掉贴纸赠品
                orderItemList = orderItemList.stream().filter(s ->
                        !Objects.equals(s.getStickerGift(), YesNoEnum.Y.getVal())).collect(Collectors.toList());
                ValueHolderV14 v141 = this.checkOrder(ocBOrder, orderItemList, restShipEnumByType);
                if (!v141.isOK()) {
                    log.info("错误信息为：" + v141.getMessage());
                    String message = "单据编号[" + ocBOrder.getBillNo() + "]，" + v141.getMessage();
                    v141.setMessage(message);
                    return v141;
                }
                IpBJitxOrder ipBJitxOrder = ipBJitxOrderMapper.selectJitxOrderByOrderSn(ocBOrder.getTid());
                ValueHolderV14 v14 = this.checkJitxOrder(ipBJitxOrder);
                //返回订单信息
                JSONObject jsonObject = copyOrderData(restShipEnumByType.getName(), Integer.valueOf(String.valueOf(ocBOrderId)), ocBOrder);
                if (isBatch) {
                    if (v14.isOK()) {
                        boolean jitxOrderShippedFlag =
                                JitxOrderStatus.ORDER_ALREADY_SEND.equals(ipBJitxOrder.getOrderStatus());
                        if (StringUtils.isNotEmpty(ocBOrder.getMergedCode()) && ocBOrder.getQtyAll().compareTo(BigDecimal.ONE) > 0 && orderItemList.size() > 1) {
                            for (OcBOrderItem item : orderItemList) {
                                String resetShipFlag = ipBJitxResetShipWorkflowService.getJITXRedisResetShipFlag(item.getTid());
                                if (StringUtils.isNotEmpty(resetShipFlag)) {
                                    return ValueHolderV14Utils.getFailValueHolder("单据编号[" + ocBOrder.getBillNo() + "]，存在未完成的发货重置工单,请稍后再试");
                                }
                            }
                            OcBOrderSplitRelation splitRelation = new OcBOrderSplitRelation();
                            splitRelation.setOrderInfo(ocBOrder);
                            splitRelation.setOrderItemList(orderItemList);
                            splitRelation.setUser(user);
                            splitRelation.setOriginOrderId(ocBOrder.getId());
                            splitRelation.setLogType(OrderLogTypeEnum.JITX_REDELIVERY_SPLIT.getKey());
                            splitRelation.setSplitReason(SplitReason.SPLIT_REDELIVERY);
                            ValueHolderV14 splitResult = omsOrderLockStockAndReOccupyStockService.splitOrigOrder(splitRelation, null);
                            if (splitResult.isOK()) {
                                if (jitxOrderShippedFlag) {
                                    ipBJitxResetShipWorkflowService.createByOcBOrder(ocBOrder, orderItemList, user, IpBJitxResetShipWorkflowService.RE_DELIVERY);
                                }
                            }
                        } else {
                            Object newOcOrder = jsonObject.get("baseInfo");
                            JSONObject object = new JSONObject();
                            object.put("ocBorderDto", newOcOrder);
                            object.put("ocBorderItemDto", orderItemList);
                            object.put("orderId", ocBOrder.getId());
                            //漏发复制
                            object.put("type", 3);
                            object.put("jitx_redelivery", true);
                            ValueHolder valueHolder = saveBillService.saveBill(object, user, Boolean.TRUE);
                            if (valueHolder.isOK()) {
                                if (jitxOrderShippedFlag) {
                                    ipBJitxResetShipWorkflowService.createByOcBOrder(ocBOrder, orderItemList, user, IpBJitxResetShipWorkflowService.RE_DELIVERY);
                                }
                                v14 = ValueHolderV14Utils.getSuccessValueHolder("异常发货补发创建成功");
                            } else {
                                return ValueHolderV14Utils.getFailValueHolder("单据编号[" + ocBOrder.getBillNo() + "]，" + String.format("异常发货补发创建失败：%s", valueHolder.get("message")));
                            }
                        }
                        v14 = ValueHolderV14Utils.getSuccessValueHolder("异常发货补发创建成功");
                    }
                    return v14;
                } else {
                    v14.setData(jsonObject);
                    return v14;
                }

            } else {
                log.error("订单id:{}插入重置发货数据失败,当前订单其他人在操作，请稍后再试", ocBOrderId);
                return ValueHolderV14Utils.getFailValueHolder("单据编号[" + ocBOrder.getBillNo() + "]，当前订单其他人在操作，请稍后再试");
            }
        } catch (Exception ex) {
            log.info("{},订单id:{},异常{}", this.getClass().getSimpleName(), ocBOrderId, Throwables.getStackTraceAsString(ex), ex);
            return ValueHolderV14Utils.getFailValueHolder("单据编号[" + ocBOrder.getBillNo() + "]，" + ex.getMessage());
            //throw new NDSException(ex.getMessage());
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 根据商品id获取物料组信息
     *
     * @param psCProId
     * @return
     */
    public PsCProdimItem getProdimItem(Long psCProId) {
        //查询商品
        List<PsCPro> psCProList = psRpcService.queryProByIds(Lists.newArrayList(psCProId.intValue()));
        if (CollectionUtils.isNotEmpty(psCProList)) {
            PsCPro psCPro = psCProList.get(0);
            if (Objects.nonNull(psCPro.getMDim2Id())) {
                //取得物料组id查询属性明细表
                PsCProdimItem psCProdimItem = psRpcService.selectPsCProdimItem(psCPro.getMDim2Id().longValue());
                return psCProdimItem;
            }
        }
        return null;
    }

    /**
     * 漏发补发订单
     *
     * @param ocBOrderId
     * @param user
     * @param restShipEnumByType
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 doOtherResetShipFunction(Long ocBOrderId, User user, OcOrderBillResetShipTypeEnum restShipEnumByType, boolean isBatch) {
        log.info("doOtherResetShipFunction：ocBOrderId={}，restShipEnumByType={}，user={}", ocBOrderId, restShipEnumByType, user);
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(ocBOrderId);
        ValueHolderV14 v141 = null;
        try {
            List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectUnSuccessRefund(ocBOrder.getId());
            //过滤掉贴纸赠品
            orderItemList = orderItemList.stream().filter(s ->
                    !Objects.equals(s.getStickerGift(), YesNoEnum.Y.getVal())).collect(Collectors.toList());
            v141 = this.checkOrder(ocBOrder, orderItemList, restShipEnumByType);
            if (!v141.isOK()) {
                log.info("错误信息为：" + v141.getMessage());
                String message = "单据编号[" + ocBOrder.getBillNo() + "]，" + v141.getMessage();
                v141.setMessage(message);
                return v141;
            }
            //返回订单信息
            JSONObject jsonObject = copyOrderData(restShipEnumByType.getName(), Integer.valueOf(String.valueOf(ocBOrderId)), ocBOrder);

            if (isBatch) {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    Object newOcOrder = jsonObject.get("baseInfo");
                    JSONObject object = new JSONObject();
                    object.put("ocBorderDto", newOcOrder);
                    object.put("ocBorderItemDto", orderItemList);
                    object.put("orderId", ocBOrder.getId());
                    //漏发补发
                    object.put("type", restShipEnumByType.getType());
                    object.put("typeName", restShipEnumByType.getName());
                    object.put("jitx_redelivery", true);
                    ValueHolder valueHolder = saveBillService.saveBill(object, user, Boolean.TRUE);
                    if (valueHolder.isOK()) {
                        return ValueHolderV14Utils.getSuccessValueHolder("保存成功");
                    } else {
                        return ValueHolderV14Utils.getFailValueHolder(String.format("订单[%s]发货补发创建失败：%s；", ocBOrder.getBillNo(), valueHolder.get("message")));
                    }
                } else {
                    log.error("订单id:{}插入重置发货数据失败,当前订单其他人在操作，请稍后再试", ocBOrderId);
                    return ValueHolderV14Utils.getFailValueHolder("订单[" + ocBOrder.getBillNo() + "]发货补发创建失败：当前订单其他人在操作，请稍后再试;");
                }
            }
            v141.setData(jsonObject);
        } catch (Exception ex) {
            log.info("{},订单id:{},异常{}", this.getClass().getSimpleName(), ocBOrderId, Throwables.getStackTraceAsString(ex));
            v141 = new ValueHolderV14();
            v141.setMessage(String.format("订单[%s]发货补发创建失败：%s；", ocBOrder.getBillNo(), ex.getMessage()));
            v141.setCode(ResultCode.FAIL);
        } finally {
            redisLock.unlock();
        }
        return v141;
    }


    /**
     * 复制单据
     *
     * @param type
     * @param iid
     * @param oldOcBOrder
     * @return
     */
    private JSONObject copyOrderData(String type, int iid, OcBOrder oldOcBOrder) {
        JSONObject jsonObject = new JSONObject();
        //获取基本信息
        JSONObject orderInfoById = billCopyMapper.queryOrderInfoById(iid);
        //获取收货人信息
        JSONObject receiverInfoById = billCopyMapper.queryReceiverInfoById(iid);
        if (Objects.nonNull(receiverInfoById)) {
            orderInfoById.putAll(receiverInfoById);
        }
        //获取备注信息
        JSONObject messageById = billCopyMapper.queryMessageById(iid);
        if (Objects.nonNull(messageById)) {
            orderInfoById.putAll(messageById);
        }
        //复制付款时间
        orderInfoById.put("PAY_TIME", oldOcBOrder.getPayTime());
        //复值组合商品标记
        orderInfoById.put("IS_COMBINATION", oldOcBOrder.getIsCombination());

        //主表信息
        OcBOrder ocBOrder = JSON.parseObject(orderInfoById.toJSONString(), OcBOrder.class);
        //跟据店铺id 获取店铺信息
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        ocBOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
        // 自动打标：补发订单
        ocBOrder.setIsResetShip(1);
        //旧平台单号
        String oldTid = orderInfoById.getString("SOURCE_CODE");
        //平台单号 可能为空,不是必填
        ocBOrder.setTid(oldTid);
        ocBOrder.setSuffixInfo(iid + "-CP");
        // 自动打标：补发订单
        ocBOrder.setIsResetShip(1);

        //查询业务类型，获取补发业务类型
        if (Objects.nonNull(ocBOrder.getBusinessTypeId())) {
            StCBusinessType stCBusinessType = stCBusinessTypeService.selectOneById(ocBOrder.getBusinessTypeId());
            if (Objects.nonNull(stCBusinessType.getResendTypeId())) {
                StCBusinessType resendBusinessType = stCBusinessTypeService.selectOneById(stCBusinessType.getResendTypeId());
                ocBOrder.setBusinessTypeId(resendBusinessType.getId());
                ocBOrder.setBusinessTypeCode(resendBusinessType.getEcode());
                ocBOrder.setBusinessTypeName(resendBusinessType.getEname());
                //ocBOrder.setBusinessType(resendBusinessType.getId().intValue());
            }
        }
        ocBOrder.setCopyReason(type);
        jsonObject.put("baseInfo", ocBOrder);
        jsonObject.put("receivingGoods", receiverInfoById);
        jsonObject.put("remarksInfo", messageById);
        //解密收货人信息
        // 确定是否是抖音平台 如果是抖音平台的话 不进行解密
        if (ObjectUtil.equal(PlatFormEnum.DOU_YIN.getCode(), ocBOrder.getPlatform())) {
            return jsonObject;
        }
        decrypt(jsonObject, oldOcBOrder);
        return jsonObject;
    }

    private void decrypt(JSONObject data, OcBOrder decryptOrder) {
        JSONObject receiverInfo = data.getJSONObject("receivingGoods");

        ipRpcService.decrypt(decryptOrder);
        receiverInfo.put("RECEIVER_NAME", decryptOrder.getReceiverName());
        receiverInfo.put("RECEIVER_MOBILE", decryptOrder.getReceiverMobile());
        receiverInfo.put("RECEIVER_PHONE", decryptOrder.getReceiverPhone());
        receiverInfo.put("RECEIVER_ADDRESS", decryptOrder.getReceiverAddress());

        data.put("receivingGoods", receiverInfo);
    }


//    /**
//     * 校验零售发货单明细是否符合补发规则
//     * @param businessType
//     * @param item
//     */
//    private void checkOcBOrderItem(String businessType, OcBOrderItem item) {
//        // 获取商品属性组
//        PsCProdimItem prodimItem = getProdimItem(item.getPsCProId());
//
//        OrderBusinessTypeEnum orderBusinessEnumByType = OrderBusinessTypeEnum.getOrderBusinessTypeEnumByCode(businessType);
//        switch (orderBusinessEnumByType){
//            case ON_LINE_MILK_CARD_SALE:
//                if (Objects.isNull(prodimItem) || !"10800".equals(prodimItem.getEcode())){
//                    throw new NDSException("补发订单创建失败！");
//                }
//                break;
//            case ON_LINE_FREE_MILK_CARD:
//                if (Objects.isNull(prodimItem) || !"10800".equals(prodimItem.getEcode())){
//                    throw new NDSException("补发订单创建失败！");
//                }
//                break;
//            case UNKNOW:
//                break;
//        }
//    }

    public ValueHolderV14 checkJitxOrder(IpBJitxOrder ipBJitxOrder) {
        if (ipBJitxOrder == null) {
            return ValueHolderV14Utils.getFailValueHolder("JITX订单不存在");
        }
        if (!OcBOrderConst.IS_ACTIVE_YES.equals(ipBJitxOrder.getIsactive())) {
            return ValueHolderV14Utils.getFailValueHolder("JITX订单已作废");
        }
        boolean canDevelReplenishmentShipment =
                JitxOrderStatus.ORDER_ALREADY_AUDITED.equals(ipBJitxOrder.getOrderStatus())
                        || JitxOrderStatus.ORDER_ALREADY_SEND.equals(ipBJitxOrder.getOrderStatus());
        if (!canDevelReplenishmentShipment) {
            return ValueHolderV14Utils.getFailValueHolder("当前仅支持JITX订单【已发货】或者【已审核】进行发货异常补发！");
        }
        return ValueHolderV14Utils.getSuccessValueHolder("校验jitx订单成功");
    }

    /**
     * description：校验订单数据
     *
     * <AUTHOR>
     * @date 2021/11/26
     */
    public ValueHolderV14 checkOrder(OcBOrder ocBOrder, List<OcBOrderItem> itemList, OcOrderBillResetShipTypeEnum restShipEnumByType) {
        log.info("{},订单补发校验开始,orderId:{}", this.getClass().getSimpleName(), ocBOrder.getId());
        if (ocBOrder == null) {
            log.error("{} 订单ID：{} 订单不存在", this.getClass().getSimpleName(), ocBOrder.getId());
            return ValueHolderV14Utils.getFailValueHolder("订单不存在");
        }

        if (CollectionUtils.isEmpty(itemList)) {
            return ValueHolderV14Utils.getFailValueHolder("订单不存在有效明细");
        }
        String tid = ocBOrder.getTid();
        if (StringUtils.isBlank(tid)) {
            return ValueHolderV14Utils.getFailValueHolder("订单平台单号为空");
        }
        if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus())
                && !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus())) {
            throw new NDSException("只能对【仓库发货，平台发货】订单状态的原单进行补发操作");
        }

        if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsResetShip())) {
            throw new NDSException("订单标签带有【补】,不允许补发!");
        }
        Long businessTypeId = ocBOrder.getBusinessTypeId();
        if (Objects.isNull(businessTypeId)) {
            throw new NDSException("业务类型为空，不允许补发!");
        }
        StCBusinessType stCBusinessType = stCBusinessTypeService.selectOneById(businessTypeId);
        if (Objects.isNull(stCBusinessType)) {
            throw new NDSException("未查询到该业务类型信息");
        }
        if (!YesNoEnum.Y.getVal().equals(stCBusinessType.getIsAllowResend())) {
            throw new NDSException(ocBOrder.getBusinessTypeName() + "类型的设置为不允许补发!");
        }
        switch (restShipEnumByType) {
            case UNKNOW:
                throw new NDSException("未知的补发订单类型！");
            case GIFT_RESET_SHIP:

                break;
            case JITX_ORDER_RESET_SHIP:
                if (!PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
                    return ValueHolderV14Utils.getFailValueHolder("当前仅支持JITX订单进行发货异常补发！");
                }
                if (!(OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus()) || OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus()))) {
                    return ValueHolderV14Utils.getFailValueHolder("当前仅支持状态为“仓库发货”或“平台发货”的JITX单据进行异常补发！");
                }

                List<IpBJitxResetShipWorkflow> shipWorkflows = ipBJitxResetShipWorkflowService.existReDeliveryWorkflow(ocBOrder.getId());
                if (CollectionUtils.isNotEmpty(shipWorkflows)) {
                    return ValueHolderV14Utils.getFailValueHolder("该笔订单已操作过异常补发,请勿重复补发！");
                }
                //当前单据的退换货单未完成，不可进行门店补发！
                LambdaQueryWrapper<OcBReturnOrder> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(OcBReturnOrder::getTid, StringUtils.isEmpty(ocBOrder.getMergeSourceCode()) ? tid : ocBOrder.getMergeSourceCode());
                queryWrapper.eq(OcBReturnOrder::getOrigOrderId, ocBOrder.getId());
                queryWrapper.eq(OcBReturnOrder::getReturnStatus, ReturnStatusEnum.COMPLETION.getVal());
                List<OcBReturnOrder> returnOrderList = ocBReturnOrderMapper.selectList(queryWrapper);
                if (CollectionUtils.isEmpty(returnOrderList)) {
                    return ValueHolderV14Utils.getFailValueHolder("当前单据的退换货单未完成，不可进行门店补发！");
                }
                //当前单据的退换货单未将原单的退货明细及数量进行全退，不可进行门店补发！
                List<Long> returnOrderIdList = returnOrderList.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
                String join = StringUtils.join(returnOrderIdList, ",");
                List<OcBReturnOrderRefund> rfnList = ocBReturnOrderRefundMapper.selectReturnRefundByPIds(join);
                Map<String, BigDecimal> refundSkuQtyMap = new HashMap<>(rfnList.size());
                //退换货单明细sku及入库数量
                for (OcBReturnOrderRefund refund : rfnList) {
                    BigDecimal existQty = refundSkuQtyMap.get(refund.getPsCSkuEcode());
                    BigDecimal qtyIn = refund.getQtyIn() == null ? BigDecimal.ZERO : refund.getQtyIn();
                    if (existQty == null) {
                        refundSkuQtyMap.put(refund.getPsCSkuEcode(), qtyIn);
                    } else {
                        refundSkuQtyMap.put(refund.getPsCSkuEcode(), existQty.add(refund.getQtyIn()));
                    }
                }
                //发货单明细sku及数量
                Map<String, BigDecimal> orderSkuQtyMap = new HashMap<>(itemList.size());
                String businessType = ocBOrder.getBusinessType();
                for (OcBOrderItem item : itemList) {
                    //校验明细数据
                    //checkOcBOrderItem(businessType, item);

                    BigDecimal existQty = orderSkuQtyMap.get(item.getPsCSkuEcode());
                    BigDecimal qty = item.getQty() == null ? BigDecimal.ZERO : item.getQty();
                    if (existQty == null) {
                        orderSkuQtyMap.put(item.getPsCSkuEcode(), qty);
                    } else {
                        orderSkuQtyMap.put(item.getPsCSkuEcode(), existQty.add(item.getQty()));
                    }

                }
                boolean isDifferentSkuOrQty = false;
                for (String k : orderSkuQtyMap.keySet()) {
                    BigDecimal qtyIn = refundSkuQtyMap.get(k);
                    BigDecimal qty = orderSkuQtyMap.get(k);
                    if (qtyIn == null || qtyIn.compareTo(qty) != 0) {
                        isDifferentSkuOrQty = true;
                        break;
                    }
                }
                if (isDifferentSkuOrQty) {
                    return ValueHolderV14Utils.getFailValueHolder("当前单据的退换货单未将原单的退货明细及数量进行全退，不可进行门店补发！");
                }
                break;
            case LOST_ORDER_RESET_SHIP:
                break;
            case ORDER_MISS_RESET_SHIP:
                break;
            case ORDER_WRONG_RESET_SHIP:
                break;
        }
        return ValueHolderV14Utils.getSuccessValueHolder("校验成功");
    }

    public ValueHolderV14 checkJitxMergedOrder(OcBOrder ocBOrder, List<OcBOrderItem> itemList) {
        if (StringUtils.isNotEmpty(ocBOrder.getMergedCode()) && ocBOrder.getQtyAll().compareTo(BigDecimal.ONE) > 0 && itemList.size() > 1) {
            for (OcBOrderItem item : itemList) {
                String resetShipFlag = ipBJitxResetShipWorkflowService.getJITXRedisResetShipFlag(item.getTid());
                if (StringUtils.isNotEmpty(resetShipFlag)) {
                    return ValueHolderV14Utils.getFailValueHolder("存在未完成的发货重置工单,无法重置发货");
                }
            }
            return ValueHolderV14Utils.getSuccessValueHolder("当前订单是合包订单,需重置发货");
        } else {
            return ValueHolderV14Utils.getFailValueHolder("当前订单非合包订单,不需重置发货");
        }
    }
}
