package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @author: xiWen.z
 * create at: 2019/10/16 0016
 */
@Slf4j
@Component
public class OmsOrderUpdate4PreSaleService {
    @Autowired
    private OcBOrderPaymentMapper ocBOrderPaymentMapper;

    @Autowired
    private OcBOrderPromotionMapper ocBOrderPromotionMapper;

    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OmsOrderService orderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    OcBOrderCountPriceService orderCountPriceService;

    @Autowired
    GroupProductSplitService groupProductSplitService;

    @Autowired
    private OcBOrderUpdateAddressService ocBOrderUpdateAddressService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OmsOrderLogService omsOrderLogService;


    private static final String GIFTTYPE = "0";//O不是赠品


    /**
     * oms 订单. 加锁操作
     *
     * @param ipOder      淘宝中间关系表
     * @param omsOder     oms订单
     * @param operateUser 当前用户
     * @return vh
     */
    public ValueHolderV14 updateEachOmsInfo(IpTaobaoOrderRelation ipOder, OcBOrder omsOder, User operateUser) {

        final int msgSize = 200;
        ValueHolderV14 vh = new ValueHolderV14();
        vh.setCode(ResultCode.FAIL);
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(omsOder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                return this.updateOmsInfo(ipOder, omsOder, operateUser);
            } else {
                vh.setMessage(Resources.getMessage(" 当前订单处于锁定状态!"));
                return vh;
            }
        } catch (Exception ex) {
            String exMsg = "";
            ex.printStackTrace();
            exMsg = ExceptionUtil.getMessage(ex);
            if (log.isDebugEnabled()) {
                log.debug(exMsg);
            }
            if (exMsg == null) {
                exMsg = "逐条更新出现异常, 异常信息Null";
            }
            if (exMsg.length() > msgSize) {
                exMsg = exMsg.substring(0, msgSize);
            }
            vh.setMessage(exMsg);
            return vh;
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * Oms 全渠道订单.修改
     *
     * @param ipOder      淘宝中间关系表
     * @param omsOder     oms订单
     * @param operateUser 当前操作用户
     * @return vh
     */
    public ValueHolderV14 updateOmsInfo(IpTaobaoOrderRelation ipOder, OcBOrder omsOder, User operateUser) {

        ValueHolderV14 vh = new ValueHolderV14();
        long l = System.currentTimeMillis();

        //1. 卖家备注
        OcBOrder newOmsOdr = new OcBOrder();
        newOmsOdr.setId(omsOder.getId());
        newOmsOdr.setAutoAuditStatus(0);
        boolean isUpSeller = isUpdateSellerMemo(ipOder, omsOder, newOmsOdr);
        // merge ys code 0702
        //add 添加更新平台订单状态 at 20200529
        boolean isUpStatus = isUpdateStatus(ipOder, omsOder, newOmsOdr);
        // end

//        // 2. 收货信息
//        boolean receiveResult = ocBOrderUpdateAddressService.updateOmsOrderReceiveInfo(ipOder, omsOder, operateUser);
//        if (!receiveResult) {
//            vh.setMessage("更新收货信息异常");
//            vh.setCode(ResultCode.FAIL);
//            return vh;
//        }
        long l1 = System.currentTimeMillis();

        // 3. 尾款已付阶段
        String stepTradeStatus = ipOder.getTaobaoOrder().getStepTradeStatus();
        if (TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equalsIgnoreCase(stepTradeStatus)) {
            String omsStepPaid = omsOder.getStatusPayStep();
            if (TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equalsIgnoreCase(omsStepPaid)) {
                return updateOmsOrderRemarkOrStatus(newOmsOdr);
            }
            //修改预下沉状态
            newOmsOdr.setSuggestPresinkStatus(omsOder.getSuggestPresinkStatus());
            newOmsOdr.setActualPresinkStatus(omsOder.getActualPresinkStatus());
            newOmsOdr.setStatusPayStep(stepTradeStatus);
            //阶段付款金额
            BigDecimal receivedAmt = ipOder.getTaobaoOrder().getStepPaidFee() ==null ? omsOder.getOrderAmt(): ipOder.getTaobaoOrder().getStepPaidFee();
            newOmsOdr.setReceivedAmt(receivedAmt == null ? BigDecimal.ZERO : receivedAmt);
            //买家备注
            newOmsOdr.setBuyerMessage(ipOder.getTaobaoOrder().getBuyerMessage());
            OcBOrderPayment ocBOrderPayment=buildOrderPaymentFromTaobaoOrder(omsOder,ipOder.getTaobaoOrder());
            ocBOrderPaymentMapper.insert(ocBOrderPayment);
            OmsOrderUpdate4PreSaleService bean = ApplicationContextHandle.getBean(OmsOrderUpdate4PreSaleService.class);
            boolean amtResult = bean.updateOmsPrevSaleOrderAmt(omsOder, ipOder);
            if (!amtResult) {
                vh.setMessage("更新金额异常");
                vh.setCode(ResultCode.FAIL);
                return vh;
            }
            long l2 = System.currentTimeMillis();
        }

        // 4. 更新订单
        boolean normalResult = this.orderService.updateOrderInfo(newOmsOdr);
        long l0 = System.currentTimeMillis() - l;
        if (!normalResult) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("更新卖家备注或状态失败");
            return vh;
        }

        vh.setCode(ResultCode.SUCCESS);
        return vh;
    }

    private OcBOrderPayment buildOrderPaymentFromTaobaoOrder(OcBOrder order, IpBTaobaoOrder tbOrder) {
        OcBOrderPayment orderPayment = new OcBOrderPayment();
        orderPayment.setId(sequenceUtil.buildOrderPaymentSequenceId());
        orderPayment.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderPayment.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        orderPayment.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(orderPayment);
        //orderPayment.setPaymentNo(order.getAlipayNo());
        orderPayment.setOcBOrderId(order.getId());
        //付款时间
        orderPayment.setPayTime(tbOrder.getPayTime());
        //完成时间
        orderPayment.setEndTime(tbOrder.getEndTime());
        BigDecimal receivedAmt = tbOrder.getStepPaidFee() ==null ? tbOrder.getPayment(): tbOrder.getStepPaidFee();
        //支付金额  去实付金额 不取收货人金额
        orderPayment.setPaymentAmt(receivedAmt);
        //订单金额
        orderPayment.setAmtOrder(tbOrder.getPayment());
        //付款方式
        orderPayment.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        //备注
        orderPayment.setRemark(null);
        //付款状态（1已付款，0未付款）
        orderPayment.setPayStatus(OmsPayStatus.PAID.toInteger());

        return orderPayment;
    }

    /**
     * 是否需要更新平台订单状态
     *
     * @param ipOder    IpTaobaoOrderRelation
     * @param omsOder   OcBOrder
     * @param newOmsOdr OcBOrder
     * @return bool
     */
    private boolean isUpdateStatus(IpTaobaoOrderRelation ipOder, OcBOrder omsOder, OcBOrder newOmsOdr) {
        String beforeStatus = "";
        String transferStatus = omsOder.getPlatformStatus();
        if (ipOder != null && ipOder.getTaobaoOrder() != null) {
            beforeStatus = ipOder.getTaobaoOrder().getStatus();
        }
        if (!StringUtils.equalsIgnoreCase(beforeStatus, transferStatus)) {
            newOmsOdr.setPlatformStatus(beforeStatus);
            return true;
        }
        return false;
    }


    /**
     * 金额处理
     * 更新金额:  平摊金额, 调整金额, 优惠金额
     *
     * @param omsOdr
     * @param orderInfo
     * @return
     */
    public boolean updateOmsPrevSaleOrderAmt(OcBOrder omsOdr, IpTaobaoOrderRelation orderInfo) {

        List<OcBOrderItem> omsItemList = orderItemMapper.selectOrderItemListOccupy(omsOdr.getId());
        if (CollectionUtils.isEmpty(omsItemList)) {
            return false;
        }

        // 1. 参数处理
        // 1.1 中间表明细处理
        IpBTaobaoOrder ipOdr = orderInfo.getTaobaoOrder();
        List<IpBTaobaoOrderItemEx> ipItemList = orderInfo.getTaobaoOrderItemList();
        if (ipOdr == null || CollectionUtils.isEmpty(omsItemList) || CollectionUtils.isEmpty(ipItemList)) {
            return false;
        }
        Map<Long, IpBTaobaoOrderItemEx> ipItemMap = ipItemList.stream().collect(Collectors.toMap(IpBTaobaoOrderItemEx::getOid, x -> x));
        nullExpCsm.accept(ipItemMap, "中间商品明细分组结果为空");

        // 1.2 oms 明细处理
        Map<String, List<OcBOrderItem>> omsItemMap = omsItemList.stream()
                .filter(
                        ocBOrderItem ->
                                StringUtils.isEmpty(ocBOrderItem.getGiftType())
                                        || StringUtils.equals(ocBOrderItem.getGiftType(), GIFTTYPE)
                ).collect(Collectors.groupingBy(x -> x.getOoid(), Collectors.toList()));
        nullExpCsm.accept(ipItemMap, "Oms商品明细分组结果为空");

        // 2. 处理oms明细金额
        List<OcBOrderItem> waitUpdateItems = new ArrayList<>();
        dealOmsItemOrder(omsItemMap, ipItemMap, waitUpdateItems);

        // 3. Oms金额
        OcBOrder newOmsOdr = reassignOmsAmt(omsOdr, ipOdr, ipItemList);
        OmsOrderUpdate4PreSaleService bean = ApplicationContextHandle.getBean(OmsOrderUpdate4PreSaleService.class);
        // 4. 更新数据
        bean.updateOmsOrder(newOmsOdr, waitUpdateItems);
        return true;
    }

    /**
     * 整理Oms订单明细
     *
     * @param omsItemMap
     * @param ipItemMap
     * @param waitUpdateItems
     */
    private void dealOmsItemOrder(Map<String, List<OcBOrderItem>> omsItemMap, Map<Long, IpBTaobaoOrderItemEx> ipItemMap,
                                  List<OcBOrderItem> waitUpdateItems) {

        for (Map.Entry<String, List<OcBOrderItem>> groupItem : omsItemMap.entrySet()) {
            List<OcBOrderItem> eachGroupList = groupItem.getValue();
            for (OcBOrderItem omsItem : eachGroupList) {
                long omsItemProType = omsItem.getProType() == null ? 0 : omsItem.getProType();
                if (omsItemProType == SkuType.COMBINE_PRODUCT) {
                    updateNewItem(waitUpdateItems, omsItem);
                    continue;
                }
                prevAssignOmsAmt(omsItem, ipItemMap);
                if (omsItemProType == SkuType.NO_SPLIT_COMBINE) {
                    groupProductSplitService.splitGroupProduct(eachGroupList);
                    updateNewItem(waitUpdateItems, omsItem);
                    break;
                }
                updateNewItem(waitUpdateItems, omsItem);
            }
        }

    }

    /**
     * 更新明细
     *
     * @param waitUpdateItems
     * @param omsItem
     */
    private void updateNewItem(List<OcBOrderItem> waitUpdateItems, OcBOrderItem omsItem) {
        OcBOrderItem newItem = new OcBOrderItem();
        newItem.setPrice(omsItem.getPrice());
        newItem.setRealAmt(omsItem.getRealAmt());
        newItem.setAdjustAmt(omsItem.getAdjustAmt());
        newItem.setAmtDiscount(omsItem.getAmtDiscount());
        newItem.setOrderSplitAmt(omsItem.getOrderSplitAmt());
        newItem.setId(omsItem.getId());
        newItem.setOcBOrderId(omsItem.getOcBOrderId());
        BigDecimal realAmt = omsItem.getRealAmt();
        BigDecimal goodsQty = omsItem.getQty();
        BigDecimal priceActual = Optional.ofNullable(realAmt).orElse(BigDecimal.ZERO).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
        newItem.setPriceActual(priceActual);
        newItem.setPriceSettle(priceActual);
        waitUpdateItems.add(newItem);
    }

    /**
     * @param omsItem
     * @param ipItemMap
     */
    private void prevAssignOmsAmt(OcBOrderItem omsItem, Map<Long, IpBTaobaoOrderItemEx> ipItemMap) {

        String oOId = omsItem.getOoid();
        nullExpCsm.accept(oOId, "Oms商品明细OOId为空, omsItem.ID: " + omsItem.getId());

        IpBTaobaoOrderItemEx ipItem = ipItemMap.get(Long.valueOf(oOId));
        nullExpCsm.accept(ipItem, "Ip商品明细根据OOid关联Oms商品明细出现空值, omsItem.OOId: " + oOId);

        BigDecimal ipNum = ipItem.getNum() == null ? BigDecimal.ZERO : BigDecimal.valueOf(ipItem.getNum());
        if (ipNum.compareTo(BigDecimal.ZERO) == 0) {
            throw new NDSException("Ip商品明细,当前明细数量为0, ipItemId: " + ipItem.getId());
        }

        BigDecimal ipPrice = initDecimal.apply(ipItem.getPrice());
        BigDecimal ipJust = initDecimal.apply(ipItem.getAdjustFee());
        BigDecimal ipDis = initDecimal.apply(ipItem.getDiscountFee());
        BigDecimal ipParDis = initDecimal.apply(ipItem.getPartMjzDiscount());
        BigDecimal realAmt = calcOmsItemRealAmt(ipItem);

        omsItem.setOrderSplitAmt(ipParDis);
        omsItem.setAdjustAmt(ipJust);
        omsItem.setAmtDiscount(ipDis);
        omsItem.setPrice(ipPrice);
        omsItem.setRealAmt(realAmt);
    }

    /**
     * 重新计算oms 订单金额
     *
     * @param omsOdr
     * @param ipOdr
     * @param ipItemList
     * @return
     */
    private OcBOrder reassignOmsAmt(OcBOrder omsOdr, IpBTaobaoOrder ipOdr, List<IpBTaobaoOrderItemEx> ipItemList) {

        BigDecimal ipOrderDisFee = BigDecimal.ZERO;
        for (IpBTaobaoOrderItemEx ipItem : ipItemList) {
            ipOrderDisFee = ipOrderDisFee.add(initDecimal.apply(ipItem.getDiscountFee()));
        }
        OcBOrder newOmsOdr = new OcBOrder();
        BigDecimal ipPost = initDecimal.apply(ipOdr.getPostFee());
        BigDecimal receiveAmt = initDecimal.apply(ipOdr.getPayment());
        BigDecimal discountFee = initDecimal.apply(ipOdr.getDiscountFee());

        newOmsOdr.setStatusPayStep(TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID);
        newOmsOdr.setId(omsOdr.getId());
        newOmsOdr.setPayTime(ipOdr.getPayTime());
        newOmsOdr.setProductDiscountAmt(ipOrderDisFee);
        newOmsOdr.setOrderDiscountAmt(discountFee);
        newOmsOdr.setOrderAmt(receiveAmt);
        newOmsOdr.setAdjustAmt(ipOdr.getAdjustFee());
        newOmsOdr.setReceivedAmt(receiveAmt);
        newOmsOdr.setShipAmt(ipPost);
        return newOmsOdr;
    }


    /**
     * assign oms items amt
     *
     * @param splitItems
     * @param combinedIts
     * @param waitUpdateItems
     */
    private void reassignOmsItemAmt(List<OcBOrderItem> splitItems, List<OcBOrderItem> combinedIts,
                                    List<OcBOrderItem> waitUpdateItems) {

        for (OcBOrderItem splitItem : splitItems) {
            String oOId = splitItem.getOoid();
            nullExpCsm.accept(oOId, "渠道订单商品明细OOId为空, omsItemID: " + splitItem.getId());
            Long omsSkuId = splitItem.getPsCSkuId();
            nullExpCsm.accept(oOId, "渠道订单商品明细SKUId为空, omsItemID: " + splitItem.getId());
            for (OcBOrderItem resultItem : combinedIts) {
                String rstItmOOId = resultItem.getOoid();
                nullExpCsm.accept(rstItmOOId, "获取到的渠道订单商品明细OOId为空, omsItemID: " + splitItem.getId());
                Long rstSkuId = resultItem.getPsCSkuId();
                nullExpCsm.accept(rstSkuId, "获取到的渠道订单商品明细SKUId为空, omsItemID: " + splitItem.getId());

                if (!(omsSkuId.equals(rstSkuId)) || !(oOId.equalsIgnoreCase(rstItmOOId))) {
                    continue;
                }
                splitItem.setPrice(resultItem.getPrice());
                splitItem.setRealAmt(resultItem.getRealAmt());
                splitItem.setAdjustAmt(resultItem.getAdjustAmt());
                splitItem.setAmtDiscount(resultItem.getAmtDiscount());
                splitItem.setOrderSplitAmt(resultItem.getOrderSplitAmt());
                waitUpdateItems.add(splitItem);
            }
        }
    }

    /**
     * 更新
     *
     * @param newOmsOdr
     * @param waitUpdateItems
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOmsOrder(OcBOrder newOmsOdr, List<OcBOrderItem> waitUpdateItems) {

        boolean omsUpdateResult = orderService.updateOrderInfo(newOmsOdr);
        if (!omsUpdateResult) {
            throw new NDSException("更新Oms订单异常,ID: " + newOmsOdr.getId());
        }

        for (OcBOrderItem omsItem : waitUpdateItems) {
            QueryWrapper<OcBOrderItem> wrapper = new QueryWrapper<>();
            wrapper.eq("ID", omsItem.getId());
            wrapper.eq("OC_B_ORDER_ID", omsItem.getOcBOrderId());
            omsItem.setOcBOrderId(null);
            int updateResult = orderItemMapper.update(omsItem, wrapper);
            if (updateResult < 1) {
                throw new NDSException("更新Oms订单明细异常,ID: " + omsItem.getId());
            }
        }

    }


    /**
     * not null
     */
    private BiConsumer<Object, String> nullExpCsm = (o, s) -> {
        if (o == null) {
            throw new NDSException(s);
        }
    };

    /**
     * safe get bigDecimal for use
     */
    private Function<BigDecimal, BigDecimal> initDecimal = num -> num == null ? BigDecimal.ZERO : num;


    /**
     * 更新备注
     *
     * @param omsOdr 临时订单
     * @return vh
     */
    private ValueHolderV14 updateOmsOrderRemarkOrStatus(OcBOrder omsOdr) {
        ValueHolderV14 vh = new ValueHolderV14();
        boolean normalResult = this.orderService.updateOrderInfo(omsOdr);
        if (!normalResult) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("更新卖家备注,自动审核状态失败");
            return vh;
        }
        vh.setCode(ResultCode.SUCCESS);
        return vh;
    }


    /**
     * 是否需要更新卖家备注
     *
     * @param ipOder    IpTaobaoOrderRelation
     * @param omsOder   OcBOrder
     * @param newOmsOdr OcBOrder
     * @return bool
     */
    private boolean isUpdateSellerMemo(IpTaobaoOrderRelation ipOder, OcBOrder omsOder, OcBOrder newOmsOdr) {
        String beforeMemo = "";
        String transferMemo = omsOder.getSellerMemo();
        if (ipOder != null && ipOder.getTaobaoOrder() != null) {
            beforeMemo = ipOder.getTaobaoOrder().getSellerMemo();
        }
        if (!StringUtils.equalsIgnoreCase(beforeMemo, transferMemo)) {
            if (StringUtils.isNotEmpty(beforeMemo) && beforeMemo.length() > 1000) {
                //卖家备注
                newOmsOdr.setSellerMemo(beforeMemo.substring(beforeMemo.length() - 1000, beforeMemo.length() - 1));
            } else {
                //卖家备注
                newOmsOdr.setSellerMemo(beforeMemo);
            }
            omsOrderLogService.addUserOrderLog(omsOder.getId(), omsOder.getBillNo(), OrderLogTypeEnum.SELLERMEMO_UPDATE.getKey(),
                    "修改卖家备注为:" + beforeMemo,
                    "", null, SystemUserResource.getRootUser());
            return true;
        }
        return false;
    }

    private Consumer<String> logDebugCsm = s -> {
        if (log.isDebugEnabled()) {
            log.debug(s);
        }
    };

    /**
     * calc oms real amt
     *
     * @param ipOrderItm
     * @return
     */
    private BigDecimal calcOmsItemRealAmt(IpBTaobaoOrderItemEx ipOrderItm) {
        BigDecimal bigOriginalPrice = initDecimal.apply(ipOrderItm.getPrice());
        BigDecimal bigPrice = bigOriginalPrice.multiply(BigDecimal.valueOf(ipOrderItm.getNum()));
        bigPrice = bigPrice.subtract(initDecimal.apply(ipOrderItm.getDiscountFee()));
        bigPrice = bigPrice.add(initDecimal.apply(ipOrderItm.getAdjustFee()));
        bigPrice = bigPrice.subtract(initDecimal.apply(ipOrderItm.getPartMjzDiscount()));
        return bigPrice;
    }

    /**
     * description：生成虚拟预定订单
     *
     * <AUTHOR>
     * @date 2021/6/1
     */
    public void createVirtualDepositOrder(List<OcBOrder> orderList) {
        for (OcBOrder order : orderList) {
            OcBOrder newOrder = new OcBOrder();
            BeanUtils.copyProperties(order, newOrder);
            newOrder.setId(ModelUtil.getSequence("oc_b_order"));
//            b)【零售发货单主表】字段赋值逻辑如下：
//            i.【订单类型】赋值：虚拟定金；
            newOrder.setOrderType(OrderTypeEnum.VIRTUAL_DEPOSIT.getVal());
//            ii.【预售方式】赋值：定金预售；
            newOrder.setPresellType(PreSellTypeEnum.DEPOSIT.getVal());
//            iii.【单据状态】赋值：直接更新为平台发货；
            newOrder.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
//            iv.【发货仓库】赋值：取店铺策略【默认仓库】；TODO

//            v.【物流公司】赋值：为空；
            newOrder.setCpCLogisticsId(null);
            newOrder.setCpCLogisticsEname("");
            newOrder.setCpCLogisticsEcode("");
            newOrder.setSysremark("当前订单为系统取消订单ID：" + order.getId() + "生成的定金虚拟订单");
//            vi.【已支付金额】赋值：原【取消订单】的【已支付金额】；
            ocBOrderMapper.insert(newOrder);
//            c)【商品明细表】字段赋值逻辑如下：
//            i.【商品明细】：取原【取消订单】的【商品明细】信息；
//            ii.【成交单价】赋值：取原（【取消订单】的该商品明细的【成交单价】；
//            iii.【成交金额】赋值：取原【取消订单】的该商品明细的【成交金额】；
            List<OcBOrderItem> itemList = omsOrderItemService.getOrderItemOcBOrderId(order.getId());
            List<OcBOrderItem> itemInsertList = new ArrayList<>(itemList.size());
            for (OcBOrderItem item : itemList) {
                OcBOrderItem newItem = new OcBOrderItem();
                BeanUtils.copyProperties(item, newItem);
                newItem.setOcBOrderId(newOrder.getId());
                newItem.setId(ModelUtil.getSequence("oc_b_order_item"));
                itemInsertList.add(newItem);
            }
            orderItemMapper.batchInsert(itemInsertList);

//            d)【订单收货地址明细表】赋值逻辑如下：
//            i.取原【取消订单】的【订单收货地址明细下所有字段】的赋值；
//            e)【备注明细表】赋值逻辑如下：
//            i.买家备注：取原【取消订单】的【买家备注】信息；
//            ii.卖家备注：取原【取消订单】的【卖家备注】信息；
//            iii.系统备注：“当前订单为系统取消订单ID：xxx(被取消订单的平台订单号)生成的定金虚拟订单“；
//            iv.内部备注：为空；
//            v.WMS备注：为空；

            //优惠信息组装
            QueryWrapper<OcBOrderPromotion> promotionWrapper = new QueryWrapper<>();
            promotionWrapper.eq("oc_b_order_id", order.getId());
            List<OcBOrderPromotion> ocBOrderPromotionList = ocBOrderPromotionMapper.selectList(promotionWrapper);
            if (CollectionUtils.isNotEmpty(ocBOrderPromotionList)) {
//            f)【优惠信息明细表】赋值逻辑如下：
//            i.取【取消订单】的【优惠信息明细表】下所有数据，复制到当前明细表中；
                List<OcBOrderPromotion> orderPromotionList = new ArrayList<>(ocBOrderPromotionList.size());
                for (OcBOrderPromotion ocBOrderPromotion : ocBOrderPromotionList) {
                    OcBOrderPromotion orderPromotionItem = new OcBOrderPromotion();
                    BeanUtils.copyProperties(ocBOrderPromotion, orderPromotionItem);
                    orderPromotionItem.setId(ModelUtil.getSequence("oc_b_order_promotion"));
                    orderPromotionItem.setOcBOrderId(newOrder.getId());
                    orderPromotionList.add(orderPromotionItem);
                }
                ocBOrderPromotionMapper.batchInsert(orderPromotionList);
            }
//            g)【支付信息明细表】赋值逻辑如下：
//            i.取【取消订单】的【支付信息明细表】下所有数据，复制到当前明细表中；
            QueryWrapper<OcBOrderPayment> paymentWrapper = new QueryWrapper<>();
            paymentWrapper.eq("oc_b_order_id", order.getId());
            List<OcBOrderPayment> ocBOrderPaymentList = ocBOrderPaymentMapper.selectList(paymentWrapper);
            if (CollectionUtils.isNotEmpty(ocBOrderPaymentList)) {
                List<OcBOrderPayment> orderPaymentList = new ArrayList<>(ocBOrderPaymentList.size());
                for (OcBOrderPayment ocBOrderPayment : ocBOrderPaymentList) {
                    OcBOrderPayment ocBOrderPaymentItem = new OcBOrderPayment();
                    BeanUtils.copyProperties(ocBOrderPayment, ocBOrderPaymentItem);
                    //重新生成Id
                    ocBOrderPaymentItem.setId(ModelUtil.getSequence("oc_b_order_payment"));
                    ocBOrderPaymentItem.setOcBOrderId(newOrder.getId());
                    orderPaymentList.add(ocBOrderPaymentItem);
                }
                ocBOrderPaymentMapper.batchInsert(orderPaymentList);
            }
//            h)【发货信息明细表】赋值逻辑如下：
//            i.取【取消订单】的【发货信息明细表】下所有数据，复制到当前明细表中；
            List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(order.getId());
            if (CollectionUtils.isNotEmpty(ocBOrderDeliveries)) {
                List<OcBOrderDelivery> orderDeliveryList = new ArrayList<>(ocBOrderDeliveries.size());
                for (OcBOrderDelivery delivery : ocBOrderDeliveries) {
                    OcBOrderDelivery ocBOrderDelivery = new OcBOrderDelivery();
                    BeanUtils.copyProperties(delivery, ocBOrderDelivery);
                    ocBOrderDelivery.setId(ModelUtil.getSequence("oc_b_order_delivery"));
                    ocBOrderDelivery.setOcBOrderId(newOrder.getId());
                    orderDeliveryList.add(ocBOrderDelivery);
                }
                ocBOrderDeliveryMapper.batchInsert(orderDeliveryList);
            }
        }
    }
}