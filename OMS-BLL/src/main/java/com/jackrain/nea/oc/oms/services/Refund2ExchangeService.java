package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemFiMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnOrderConfirmStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.relation.IpOrderReturnRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.WmsControlWarehouse;
import com.jackrain.nea.oc.oms.util.BigDecimalUtil;
import com.jackrain.nea.oc.oms.util.PrintLogUtils;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 退转换
 *
 * @author: qinjunlong
 * @since: 2019-03-26
 * create at : 2019-03-26 10:21
 */
@Slf4j
@Component
public class Refund2ExchangeService {

    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderFiMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemFiMapper ocBorderItemMapper;
    @Autowired
    private OcBReturnOrderExchangeMapper ocBReturnOrderExchangeFiMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundFiMapper;
    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;
    @Autowired
    private OcBReturnBuildService ocBReturnBuildService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private OcBReturnAfSendMapper afSendMapper;

    /**
     * 参考退转换PRD
     *
     * @param obj  obj
     * @param user user
     * @return ValueHolder
     */
    @Transactional(rollbackFor = Throwable.class)
    public ValueHolder refund2Exchange(JSONObject obj, User user) {
        ValueHolder vh;
        // 根据原单id去查询退单 记录不存在，则提示：“当前记录已不存在！
        Long objid = obj.getLong("objid");
        OcBReturnOrder originReturnOrder = ocBReturnOrderFiMapper.selectById(objid);
        try {
            vh = this.is2Operate(originReturnOrder);
            if (!vh.isOK()) {
                return vh;
            }
            // 退换货订单
            JSONObject ocBreturnOrderDto = obj.getJSONObject("OcBreturnOrder");
            // 换货明细
            JSONArray ocBreturnOrderExchange = obj.getJSONArray("OcBreturnOrderExchange");
            // 退货明细
            JSONArray ocBreturnOrderRefund = obj.getJSONArray("OcBreturnOrderRefund");
            if (MapUtils.isEmpty(ocBreturnOrderDto)) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "退换货主表信息为空!无法转换!");
                return vh;
            }
            if (CollectionUtils.isEmpty(ocBreturnOrderExchange)) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "换货信息为空!无法转换!");
                return vh;
            }
            if (CollectionUtils.isEmpty(ocBreturnOrderRefund)) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", "退货明细为空!无法转换!");
                return vh;
            }
            String confirmStatus = ocBreturnOrderDto.getString("CONFIRM_STATUS");
            if(StringUtils.isNotBlank(confirmStatus)) {
                if (ReturnOrderConfirmStatusEnum.CONFIRM.getValue().equals(confirmStatus)) {
                    ocBreturnOrderDto.put("CONFIRM_STATUS", ReturnOrderConfirmStatusEnum.CONFIRM.getKey());
                }
                if (ReturnOrderConfirmStatusEnum.NOT_CONFIRM.getValue().equals(confirmStatus)) {
                    ocBreturnOrderDto.put("CONFIRM_STATUS", ReturnOrderConfirmStatusEnum.NOT_CONFIRM.getKey());
                }
            }else{
                ocBreturnOrderDto.put("CONFIRM_STATUS",null);
            }

            String signingStatus = ocBreturnOrderDto.getString("SIGNING_STATUS");
            if (StringUtils.isEmpty(signingStatus)){
                ocBreturnOrderDto.put("SIGNING_STATUS",null);
            }
            String subscriptionStatus = ocBreturnOrderDto.getString("SUBSCRIPTION_STATUS");
            if (StringUtils.isEmpty(subscriptionStatus)){
                ocBreturnOrderDto.put("SUBSCRIPTION_STATUS",null);
            }
            String overdueStorageStatus = ocBreturnOrderDto.getString("OVERDUE_STORAGE_STATUS");
            if (StringUtils.isEmpty(overdueStorageStatus)){
                ocBreturnOrderDto.put("OVERDUE_STORAGE_STATUS",null);
            }

            // 转换
            ReturnNaiKaStatusEnum returnNaiKaStatusEnum = ReturnNaiKaStatusEnum.getByDesc(ocBreturnOrderDto.getString("TO_NAIKA_STATUS"));
            if (ObjectUtil.isNotNull(returnNaiKaStatusEnum)) {
                ocBreturnOrderDto.put("TO_NAIKA_STATUS", returnNaiKaStatusEnum.getStatus());
            } else {
                ocBreturnOrderDto.put("TO_NAIKA_STATUS", null);
            }
            // 退单主表信息转换成
            OcBReturnOrder returnOrder = JSONObject.parseObject(ocBreturnOrderDto.toString(), OcBReturnOrder.class);

            List<OcBReturnOrderExchange> returnOrderExchanges = new ArrayList<>();

            if (CollectionUtils.isNotEmpty(ocBreturnOrderExchange)) {
                returnOrderExchanges = JSONObject.parseArray(ocBreturnOrderExchange.toString(),
                        OcBReturnOrderExchange.class);
            }

            List<OcBReturnOrderRefund> returnOrderRefunds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(ocBreturnOrderRefund)) {
                returnOrderRefunds = JSONObject.parseArray(ocBreturnOrderRefund.toJSONString(),
                        OcBReturnOrderRefund.class);
            }
            if (Objects.isNull(returnOrder) || CollectionUtils.isEmpty(returnOrderExchanges) ||
                    CollectionUtils.isEmpty(returnOrderRefunds)) {
                return ValueHolderUtils.getFailValueHolder("单据信息异常!无法继续转换!");
            }
            // 假如是待退货入库
            if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(originReturnOrder.getReturnStatus())) {
                // 校验
                vh = isPass(returnOrder, returnOrderRefunds, returnOrderExchanges);
                if (!vh.isOK()) {
                    return vh;
                }
            }
            // 原始订单id
            Long origOrderId = returnOrder.getOrigOrderId();
            OcBOrder ocBOrder = ocBOrderMapper.selectById(origOrderId);
            // 原始订单
            if (Objects.isNull(ocBOrder)) {
                return ValueHolderUtils.getFailValueHolder("原逻辑发货单已不存在!");
            }
            // 单据类型
            if (!OcReturnBillTypeEnum.EXCHANGE.getVal().equals(returnOrder.getBillType())) {
                return ValueHolderUtils.getFailValueHolder("单据类型异常");
            }
            // 加锁
            String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(objid);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);

            // 原退单
            List<OcBReturnOrderRefund> refunds = ocBReturnOrderRefundFiMapper.selectList(new QueryWrapper<OcBReturnOrderRefund>()
                    .lambda().eq(OcBReturnOrderRefund::getOcBReturnOrderId, objid));

            boolean isHold = true;
            try {
                // 退单状态是等待退货入库 ,主要是 判断申请数量和换货数量,金额已在前端做了校验
                if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(originReturnOrder.getReturnStatus())) {
                    if (StringUtils.isNotBlank(returnOrder.getReturnId())) {
                        returnOrder.setReturnId("");
                       /* vh.setCode(ResultCode.FAIL);
                        vh.setMessage("当前退单类型为退换货，平台退款单号需清空!");
                        return vh;*/
                    }
                    // 只有待退货入库的时候,退转换才可以修改退货明细信息
                    this.refund(returnOrderRefunds, refunds, objid, user);
                } else if (ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal().equals(originReturnOrder.getReturnStatus()) ||
                        ReturnStatusEnum.COMPLETION.getVal().equals(originReturnOrder.getReturnStatus())) {
                    isHold = false;
                    // 换货数量
                    BigDecimal exchangeCount = returnOrderExchanges.stream().map(OcBReturnOrderExchange::getQtyExchange)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 已退货入库数量
                    BigDecimal qtyIn = refunds.stream().map(OcBReturnOrderRefund::getQtyIn).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (exchangeCount.compareTo(qtyIn) > 0) {
                        return ValueHolderUtils.getFailValueHolder("换货数量不能大于退货入库数量,请检查后重试。");
                    }
                    if (StringUtils.isNotBlank(returnOrder.getReturnId())) {
                        List<OcBReturnAfSend> afSends = afSendMapper.listQueryByTReturnId(returnOrder.getReturnId());
                        if (CollectionUtils.isNotEmpty(afSends)) {
                            for (OcBReturnAfSend afSend : afSends) {
                                if (("" + (AGStatusEnum.SUCCESS.getVal())).equals(afSend.getAgStatus())) {
                                    return ValueHolderUtils.getFailValueHolder("退款单已传AG,不允许退转换!");
                                }
                            }
                        }
                    }
                }
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    vh = this.createExchangeInfo(user, vh, returnOrder, returnOrderExchanges, isHold, ocBOrder);
                }
            } finally {
                redisLock.unlock();
            }
        } catch (Exception e) {
            log.error(LogUtil.format("OcSaveChangingOrRefundingService.saveChangingOrRefunding 新增退换货订单失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
        vh.put("objid", objid);
        vh.put("RETURN_STATUS_NAME", ReturnStatusEnum.getNameByCode(originReturnOrder.getReturnStatus()));
        return vh;
    }


    /**
     * 添加换货信息 ,根据是否预留库存生成新换货类型的零售发货单
     *
     * @param user                 操作人
     * @param vh                   返回值
     * @param returnOrder          退单
     * @param returnOrderExchanges 换货信息
     * @param isHold               是否需要hold单
     * @param ocBOrder             原单
     * @return
     */
    public ValueHolder createExchangeInfo(User user, ValueHolder vh, OcBReturnOrder returnOrder,
                                          List<OcBReturnOrderExchange> returnOrderExchanges,
                                          boolean isHold, OcBOrder ocBOrder) {
        // 是否生成预留库存
        if (returnOrder.getIsReserved() == 1) {
            // 勾选了换货预留库存参数，调用生成换货订单服务
            IpOrderReturnRelation ocBOrderRelation = new IpOrderReturnRelation();
            List<OcBOrderItem> list = new ArrayList<>();
            // 生成订单主表对象
            OcBOrder newOrder = this.createOcBOrder(returnOrder);
            // 订单明细
            for (OcBReturnOrderExchange exchange : returnOrderExchanges) {
                list.add(this.createOcBOrderItem(exchange, returnOrder));
            }
            newOrder.setSkuKindQty(BigDecimal.valueOf(list.size(), 4));
            newOrder.setGwSourceCode(ocBOrder.getGwSourceCode());
            //原单奶卡
            ocBReturnBuildService.buildOrderNaikaFromOrigOrder(ocBOrder, ocBOrderRelation);
            //调用生成换货订单服务
            ocBOrderRelation.setOcBOrder(newOrder);
            ocBOrderRelation.setOcBOrderItems(list);
            ocBOrderRelation.setOcBReturnOrder(returnOrder);
            ocBOrderRelation.setForbidHoldExchangeOrder(!isHold);
            try {
                ValueHolderV14 result = ocBReturnBuildService.buildExchange(ocBOrderRelation, user);
                if (result.getCode() == ResultCode.FAIL) {
                    return ValueHolderUtils.getFailValueHolder(result.getMessage());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("调用生成换货订单服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                throw new NDSException("调用生成换货订单服务异常");
            }
        }
        try {
            returnOrder.setCpCLogisticsEcode(cpRpcService.cpLogisticsInfo(returnOrder.getCpCLogisticsId()).getEcode());
        } catch (Exception e) {
            log.error(LogUtil.format("根据物流公司id:{}查找物流公司编码异常,异常信息为:{}",returnOrder.getCpCLogisticsId()),returnOrder.getCpCLogisticsId(), Throwables.getStackTraceAsString(e));
        }

        if (Objects.isNull(returnOrder.getCpCPhyWarehouseInId())) {
            if (Objects.isNull(returnOrder.getIsBack()) || returnOrder.getIsBack() == 0) {
                // 非原退，取店铺策略
                if (Objects.nonNull(returnOrder.getCpCShopId())) {
                    StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());

                    if (Objects.nonNull(shopStrategy)) {
                        Integer isMultiReturnWarehouse = shopStrategy.getIsMultiReturnWarehouse();

                        if (Objects.isNull(isMultiReturnWarehouse) || isMultiReturnWarehouse == 0) {
                            returnOrder.setCpCPhyWarehouseInId(shopStrategy.getCpCWarehouseDefId());
                        }
                    }
                }
            } else {
                // 原退，取发货实体仓档案
                if (Objects.nonNull(returnOrder.getCpCPhyWarehouseId())) {
                    CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(returnOrder.getCpCPhyWarehouseId());

                    if (Objects.nonNull(cpCPhyWarehouse) && Objects.nonNull(cpCPhyWarehouse.getOriginalReturnPhyWarehouseId())) {
                        returnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouse.getOriginalReturnPhyWarehouseId());
                    } else {
                        // 使用发货实体仓
                        returnOrder.setCpCPhyWarehouseInId(returnOrder.getCpCPhyWarehouseId());
                    }
                }
            }
        }
        // 判断新增或者更新的这个是不是wms管控仓
        BaseModelUtil.setupUpdateParam(returnOrder, user);
        ocBReturnOrderFiMapper.updateById(returnOrder);
        for (int i = 0; i < returnOrderExchanges.size(); i++) {
            OcBReturnOrderExchange exchange = returnOrderExchanges.get(i);
            Long exchangeId = ModelUtil.getSequence("oc_b_return_order_exchange");
            exchange.setOcBReturnOrderId(returnOrder.getId());
            exchange.setId(exchangeId);
            BaseModelUtil.initialBaseModelSystemField(exchange, user);
        }
        // 批量保存换货信息
        ocBReturnOrderExchangeFiMapper.batchInsert(returnOrderExchanges);
        // 添加新增日志到退单操作日志中
        Long logId = ModelUtil.getSequence("oc_b_return_order_log");
        String logType = "手工退转换";
        String logMessage = "手工退转换成功";
        OcBReturnOrderLog ocBReturnOrderLog = getLog(logId, logType, logType, user.getName(), returnOrder.getId());
        ocBReturnOrderLog.setIpAddress(user.getLastloginip());
        BaseModelUtil.initialBaseModelSystemField(ocBReturnOrderLog, user);
        ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.REFUND_ORDER_ADD.getKey(),
                logMessage, null, null, user);
        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", "成功！");
        vh.put("objid", returnOrder.getId());
        return vh;
    }


    /**
     * 判断新增或者更新的这个是不是wms管控仓
     *
     * @param returnOrder
     */
    public OcBReturnOrder checkWmsCtrHouse(OcBReturnOrder returnOrder) {
        /**
         * 如果入库实体仓是WMS管控仓，则将reserve_bigint03此字段赋值为1，如果不是WMS管控仓或者入库实体仓为空，则此字段赋值为0.
         */
        //实体仓id
        Long cpCPhyWarehouseInId = returnOrder.getCpCPhyWarehouseInId();
        if (cpCPhyWarehouseInId == null) {
            returnOrder.setIsNeedToWms(0L);
            return returnOrder;
        }
        CpCPhyWarehouse wareHouse = cpRpcService.queryByWarehouseId(cpCPhyWarehouseInId);
        //判断这个仓是不是wms 管控仓
        if (wareHouse != null) {
            //不是wms 管控仓或者为空，是否传WMS字段（reserve_bigint03）则此字段赋值为0.
            if (wareHouse.getWmsControlWarehouse() == null || WmsControlWarehouse.NOT_CONTROL.equals(wareHouse.getWmsControlWarehouse())) {
                returnOrder.setIsNeedToWms(0L);
            } else {
                //是wms 管控仓，是否传WMS字段（reserve_bigint03）则此字段赋值为0.
                returnOrder.setIsNeedToWms(1L);
            }
        } else {
            throw new NDSException("查询不到实体仓");
        }
        return returnOrder;
    }


    //生成日志对象
    private OcBReturnOrderLog getLog(Long id, String logType, String message, String userName, Long reOrderid) {
        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
        ocBReturnOrderLog.setId(id);
        ocBReturnOrderLog.setLogType(logType);
        ocBReturnOrderLog.setLogMessage(message);
        ocBReturnOrderLog.setUserName(userName);
        ocBReturnOrderLog.setOcBReturnOrderId(reOrderid);
        return ocBReturnOrderLog;
    }

    /**
     * //退货明细操作
     *
     * @param returnOrderRefunds 前端传过来的明细
     * @param refunds            数据库的退货，明细
     * @param objid              主表的id
     * @param user               当前登录用户
     */
    private void refund(List<OcBReturnOrderRefund> returnOrderRefunds, List<OcBReturnOrderRefund> refunds,
                        Long objid, User user) {
        for (int i = 0; i < returnOrderRefunds.size(); i++) {
            for (int j = 0; j < refunds.size(); j++) {
                if (returnOrderRefunds.get(i).getId().toString().equals(refunds.get(j).getId().toString())) {
                    refunds.remove(refunds.get(j));
                }
            }
        }
        for (OcBReturnOrderRefund refund : returnOrderRefunds) {
            // 新增明细
            if (refund.getId() == -1) {
                Long reId = ModelUtil.getSequence("oc_b_return_order_refund");
                refund.setOcBReturnOrderId(objid);
                refund.setId(reId);
                BaseModelUtil.setupUpdateParam(refund, user);
                ocBReturnOrderRefundFiMapper.insert(refund); //新增明细
            } else { //修改明细
                QueryWrapper<OcBReturnOrderRefund> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("oc_b_return_order_id", objid).eq("id", refund.getId());
                ocBReturnOrderRefundFiMapper.update(refund, queryWrapper);
            }
        }
    }


    /**
     * 更新原单明细已退数量
     *
     * @param item 原单明细
     * @param qty  数量
     * @param flag 加减标识
     * @return
     */
    public void updateOrderItem(OcBOrderItem item, BigDecimal qty, Boolean flag) {
        BigDecimal add = BigDecimal.ZERO;
        BigDecimal applyQty = BigDecimal.ZERO;
        if (flag) {
            // 商品已退数量
            if (item.getQtyHasReturn() != null) {
                add = item.getQtyHasReturn().add(qty);
            } else {
                add = qty;
            }
            // 已申请退货数量
            if (item.getQtyReturnApply() != null) {
                applyQty = item.getQtyReturnApply().add(qty);
            } else {
                applyQty = qty;
            }
        } else {
            if (item.getQtyHasReturn() != null) {
                add = item.getQtyHasReturn().subtract(qty);
                if (add.compareTo(BigDecimal.ZERO) < 0) {
                    add = BigDecimal.ZERO;
                }
            } else {
                add = BigDecimal.ZERO;
            }
            if (item.getQtyReturnApply() != null) {
                applyQty = item.getQtyReturnApply().add(qty);
            } else {
                applyQty = qty;
            }
        }
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("id", item.getId());
        wrapper.eq("oc_b_order_id", item.getOcBOrderId());
        item.setQtyHasReturn(add);
        item.setQtyReturnApply(applyQty);
        item.setOcBOrderId(null);
        //ocBorderItemMapper.updateById(item);
        ocBorderItemMapper.update(item, wrapper);
    }

    /**
     * 判断是不是已经存在相同明细进行累加还是 新增一条明细
     *
     * @param ocBOrderItem
     * @param orderRefundList
     * @return
     */
    private OcBReturnOrderRefund ajustItemIsExit(OcBOrderItem ocBOrderItem, List<OcBReturnOrderRefund> orderRefundList) {
        for (OcBReturnOrderRefund returnOrderRefund : orderRefundList) {
            if (ocBOrderItem.getPriceList().compareTo(returnOrderRefund.getPrice()) == 0 &&
                    ocBOrderItem.getPsCSkuEcode().equals(returnOrderRefund.getPsCSkuEcode())) {
                return returnOrderRefund;
            }
        }
        return null;
    }


    /**
     * 退转换:列表前置校验
     *
     * @param obj
     * @return
     */
    public ValueHolder validate(JSONObject obj) {
        JSONArray ids = obj.getJSONArray("ids");
        if (CollectionUtils.isEmpty(ids)) {
            return ValueHolderUtils.getFailValueHolder("请选择需要退转换的退单!");
        }
        if (ids.size() > 1) {
            return ValueHolderUtils.getFailValueHolder("只能选择1条退单!");
        }
        Long id = Tools.getLong(ids.get(0), 0L);
        if (id == 0L) {
            return ValueHolderUtils.getFailValueHolder("参数异常!");
        }
        OcBReturnOrder returnOrder = ocBReturnOrderFiMapper.selectById(id);
        return this.is2Operate(returnOrder);
    }


    /**
     * 退转换:前置校验:是否可以继续操作
     *
     * @param originReturnOrder
     * @return
     */
    private ValueHolder is2Operate(OcBReturnOrder originReturnOrder) {
        ValueHolder vh = ValueHolderUtils.getSuccessValueHolder("退转换前置校验成功!");
        if (Objects.isNull(originReturnOrder)) {
            vh.put("message", "当前记录已不存在！");
            vh.put("code", ResultCode.FAIL);
            return vh;
        }
        if (OcReturnBillTypeEnum.EXCHANGE.getVal().equals(originReturnOrder.getBillType())) {
            vh.put("message", "当前记录单据类型为退换货单，不允许退转换！");
            vh.put("code", ResultCode.FAIL);
            return vh;
        }
        if (ReturnStatusEnum.CANCLE.getVal().equals(originReturnOrder.getReturnStatus())) {
            vh.put("message", "当前记录已取消，不允许退转换！");
            vh.put("code", ResultCode.FAIL);
            return vh;
        }
        if (WmsWithdrawalState.YES.toInteger().equals(originReturnOrder.getIsTowms())) {
            vh.put("message", "传wms 成功，不允许更新！");
            vh.put("code", ResultCode.FAIL);
            return vh;
        }
        //如果传wms 中也不能去保存
        if (WmsWithdrawalState.PASS.toInteger().equals(originReturnOrder.getIsTowms())) {
            vh.put("message", "传wms中，不允许更新！");
            vh.put("code", ResultCode.FAIL);
            return vh;
        }
        return vh;
    }

    public void saveOrder(OcBReturnOrder returnOrder, List<OcBReturnOrderExchange> returnOrderExchanges,
                          ValueHolderV14 vh, User user) {
        // 是否生成预留库存
        if (returnOrder.getIsReserved() == 1) {
            // 勾选了换货预留库存参数，调用生成换货订单服务
            IpOrderReturnRelation ocBOrderRelation = new IpOrderReturnRelation();
            List<OcBOrderItem> list = new ArrayList<>();
            // 生成订单主表对象
            OcBOrder newOrder = this.createOcBOrder(returnOrder);
            // 订单明细
            for (OcBReturnOrderExchange exchange : returnOrderExchanges) {
                list.add(this.createOcBOrderItem(exchange, returnOrder));
            }
            newOrder.setSkuKindQty(BigDecimal.valueOf(list.size(), 4));
            //调用生成换货订单服务
            ocBOrderRelation.setOcBOrder(newOrder);
            ocBOrderRelation.setOcBOrderItems(list);
            ocBOrderRelation.setOcBReturnOrder(returnOrder);
            try {
                ValueHolderV14 result = ocBReturnBuildService.buildExchange(ocBOrderRelation, user);
                if (result.getCode() == -1) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(result.getMessage());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("调用生成换货订单服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("调用生成换货订单服务异常:" + e.getMessage());
                throw new NDSException("调用生成换货订单服务异常");
            }
        }
    }

    /**
     * 生成订单主表对象
     *
     * @param returnOrder 退单
     * @return
     */
    private OcBOrder createOcBOrder(OcBReturnOrder returnOrder) {
        OcBOrder newOrder = new OcBOrder();
        newOrder.setOrigReturnOrderId(returnOrder.getId());
        // 生成订单主表对象
        newOrder.setOrderSource("手工新增");
        newOrder.setCpCShopId(returnOrder.getCpCShopId());
        newOrder.setCpCShopEcode(returnOrder.getCpCShopEcode());
        newOrder.setQtyAll(returnOrder.getQtyInstore());
        newOrder.setCpCShopTitle(returnOrder.getCpCShopTitle());
        newOrder.setUserNick(returnOrder.getBuyerNick());
        newOrder.setProductAmt(returnOrder.getExchangeAmt());
        newOrder.setOrderAmt(returnOrder.getExchangeAmt());
        newOrder.setReceivedAmt(returnOrder.getExchangeAmt());
        newOrder.setAmtReceive(returnOrder.getExchangeAmt());
        newOrder.setReceiverName(returnOrder.getReceiveName());
        newOrder.setReceiverMobile(returnOrder.getReceiveMobile());
        newOrder.setReceiverPhone(returnOrder.getReceivePhone());
        newOrder.setCpCRegionProvinceId(returnOrder.getReceiverProvinceId());
        newOrder.setCpCRegionCityId(returnOrder.getReceiverCityId());
        newOrder.setCpCRegionAreaId(returnOrder.getReceiverAreaId());
        newOrder.setReceiverAddress(returnOrder.getReceiveAddress());
        newOrder.setCpCRegionTownEname(returnOrder.getCpCRegionTownEname());
        newOrder.setReceiverZip(returnOrder.getReceiveZip());
        newOrder.setOrderSource(OcOrderTagEum.TAG_HAND.getVal());
        newOrder.setOrigOrderId(returnOrder.getOrigOrderId());
        newOrder.setSellerMemo(returnOrder.getRemark());
        newOrder.setPlatform(returnOrder.getPlatform());
        if (returnOrder.getTbDisputeId() == null) {
            newOrder.setSourceCode(returnOrder.getTid());
        } else {
            newOrder.setSourceCode(returnOrder.getTbDisputeId().toString());
        }
        newOrder.setTid(returnOrder.getTid());
        newOrder.setMergeSourceCode(returnOrder.getTid());
        return newOrder;
    }

    private OcBOrderItem createOcBOrderItem(OcBReturnOrderExchange returnOrderExchange, OcBReturnOrder returnOrder) {
        OcBOrderItem ocBOrderItem = new OcBOrderItem();
        ocBOrderItem.setBarcode(returnOrderExchange.getBarcode());
        ocBOrderItem.setPsCProId(returnOrderExchange.getPsCProId());
        ocBOrderItem.setPsCProEcode(returnOrderExchange.getPsCProEcode());
        ocBOrderItem.setPsCProEname(returnOrderExchange.getPsCProEname());
        ocBOrderItem.setSkuSpec(returnOrderExchange.getSkuSpec());
        ocBOrderItem.setPsCSkuId(returnOrderExchange.getPsCSkuId());
        ocBOrderItem.setPsCSkuEcode(returnOrderExchange.getPsCSkuEcode());
        ocBOrderItem.setOoid(returnOrderExchange.getOid());
        ocBOrderItem.setPriceList(BigDecimalUtil.isNullReturnZero(returnOrderExchange.getPrice()));
        ocBOrderItem.setPrice(returnOrderExchange.getAmtRefund().divide(returnOrderExchange.getQtyExchange(),
                4, BigDecimal.ROUND_HALF_UP));
        if (returnOrderExchange.getPrice() != null) {
            // 调整金额 = 成交金额-平台售价*数量
            ocBOrderItem.setAdjustAmt(returnOrderExchange.getAmtRefund().subtract(ocBOrderItem.getPrice()
                    .multiply(returnOrderExchange.getQtyExchange())));
        }
        if (ocBOrderItem.getAdjustAmt() == null) {
            ocBOrderItem.setAdjustAmt(BigDecimal.ZERO);
        }
        ocBOrderItem.setPsCSizeEname(returnOrderExchange.getPsCSizeEname());
        ocBOrderItem.setPsCSizeEcode(returnOrderExchange.getPsCSizeEcode());
        ocBOrderItem.setPsCSizeId(returnOrderExchange.getPsCSizeId());
        ocBOrderItem.setPsCClrId(returnOrderExchange.getPsCClrId());
        ocBOrderItem.setPsCClrEcode(returnOrderExchange.getPsCClrEcode());
        ocBOrderItem.setPsCClrEname(returnOrderExchange.getPsCClrEname());
        ocBOrderItem.setRealAmt(BigDecimalUtil.isNullReturnZero(returnOrderExchange.getAmtRefund()));
        ocBOrderItem.setQty(returnOrderExchange.getQtyExchange());
        ocBOrderItem.setTid(Objects.isNull(returnOrder.getTbDisputeId()) ? returnOrder.getTid()
                : String.valueOf(returnOrder.getTbDisputeId()));
        ocBOrderItem.setIsactive(IsActiveEnum.Y.getKey());
        return ocBOrderItem;
    }

    /**
     * 是否通过校验
     *
     * @param ocBReturnOrder 退单
     * @param refunds        退货明细
     * @param exchanges      换货明细
     * @return ValueHolderV14.code  0 通过; -1 不通过
     */
    private static ValueHolder isPass(OcBReturnOrder ocBReturnOrder, List<OcBReturnOrderRefund> refunds
            , List<OcBReturnOrderExchange> exchanges) {
        BigDecimal returnAmtList = ocBReturnOrder.getReturnAmtList();
        BigDecimal returnAmtShip = ocBReturnOrder.getReturnAmtShip();
        BigDecimal returnAmtOther = ocBReturnOrder.getReturnAmtOther();
        BigDecimal exchangeAmt = ocBReturnOrder.getExchangeAmt();
        PrintLogUtils.printDeBugLog("isPass1.returnAmtList:{},returnAmtShip:{},returnAmtOther:{},exchangeAmt:{};"
                , returnAmtList, returnAmtShip, returnAmtOther, exchangeAmt);
        if (Objects.isNull(returnAmtList) || Objects.isNull(returnAmtShip) || Objects.isNull(returnAmtOther)
                || Objects.isNull(exchangeAmt)) {
            return ValueHolderUtils.getFailValueHolder("金额异常:金额为空!");
        }
        if ((returnAmtList.add(returnAmtShip).add(returnAmtOther).add(returnAmtOther)).compareTo(exchangeAmt) != 0) {
            return ValueHolderUtils.getFailValueHolder("金额异常:应退金额 + 邮费 + 其他 != 换货金额!");
        }
        // 退货金额
        AtomicReference<BigDecimal> refundItemAmt = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> refundItemQty = new AtomicReference<>(BigDecimal.ZERO);
        refunds.forEach(refund -> {
            refundItemAmt.set(refundItemAmt.get().add(refund.getAmtRefund()));
            refundItemQty.set(refundItemQty.get().add(refund.getQtyRefund()));
        });
        // 换货明细金额,数量
        AtomicReference<BigDecimal> exchangeItemAmt = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> exchangeItemQty = new AtomicReference<>(BigDecimal.ZERO);

        exchanges.forEach(exchange -> {
            exchangeItemAmt.set(exchangeItemAmt.get().add(exchange.getAmtRefund()));
            exchangeItemQty.set(exchangeItemQty.get().add(exchange.getQtyExchange()));
        });
        // 应退金额+邮费+其他=换货金额
        if ((refundItemAmt.get().add(returnAmtShip).subtract(returnAmtOther)).compareTo(exchangeItemAmt.get()) != 0) {
            PrintLogUtils.printDeBugLog("isPass2.refundItemAmt:{},returnAmtShip:{},returnAmtOther:{}" +
                    ",exchangeItemAmt:{};", refundItemAmt, returnAmtShip, returnAmtOther, exchangeItemAmt);
            return ValueHolderUtils.getFailValueHolder("金额异常:应退金额 + 邮费 + 其他 != 换货金额!");
        }
        // 数量,退货数量 >= 换货数量 为true,否则为false
        if (refundItemQty.get().compareTo(exchangeItemQty.get()) < 0) {
            PrintLogUtils.printDeBugLog("isPass3.refundItemQty:{},exchangeItemQty:{};", refundItemQty, exchangeItemQty);
            return ValueHolderUtils.getFailValueHolder("换货数量异常: 换货数量 > 退货数量!");
        }
        return ValueHolderUtils.getSuccessValueHolder("success");
    }


    /**
     * 获取实例
     *
     * @return
     */
    public static Refund2ExchangeService getInstance() {
        return ApplicationContextHandle.getBean(Refund2ExchangeService.class);
    }
}
