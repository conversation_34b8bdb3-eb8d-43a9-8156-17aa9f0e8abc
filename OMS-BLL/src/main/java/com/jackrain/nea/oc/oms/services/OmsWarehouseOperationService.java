//package com.jackrain.nea.oc.oms.services;
//
//import com.alibaba.fastjson.JSONArray;
//import com.ctrip.framework.apollo.Config;
//import com.ctrip.framework.apollo.ConfigService;
//import com.jackrain.nea.oc.oms.es.ES4IpWhInnerOperate;
//import com.jackrain.nea.oc.oms.es.ES4OrderDelivery;
//import com.jackrain.nea.oc.oms.mapper.IpBWhInnerOperateMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.model.enums.LogisticsStatusEnum;
//import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
//import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
//import com.jackrain.nea.oc.oms.model.table.IpBWhInnerOperate;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
//import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
//import com.jackrain.nea.resource.SystemUserResource;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.*;
//
///**
// * @Author: 黄世新
// * @Date: 2020/3/12 3:00 下午
// * @Version 1.0
// * 仓内作业转换
// */
//@Slf4j
//@Component
//public class OmsWarehouseOperationService {
//
//
//    @Autowired
//    private IpBWhInnerOperateMapper ipBWhInnerOperateMapper;
//    @Autowired
//    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//    @Autowired
//    private OcBOrderItemMapper ocBOrderItemMapper;
//    @Autowired
//    private OmsGoodsAfterRefundOnlyService omsGoodsAfterRefundOnlyService;
//    @Autowired
//    private OmsReturnOrderService omsReturnOrderService;
//
//
//    public void selectWarehouseOperationData(User user) {
//        //es查询未转换的数据
//        try {
//            Config config = ConfigService.getConfig("lts");
//            Integer pageSize = config.getIntProperty("lts.AutoWarehouseOperationTask.range", 500);
//
//            List<String> expressCodes = ES4IpWhInnerOperate.findExpressCodesByTransStatus(pageSize);
//
//            if (CollectionUtils.isEmpty(expressCodes)) {
//                return;
//            }
//            //根据物流单号查询发货信息表
//            List<IpBWhInnerOperate> ipBWhInnerOperates = ipBWhInnerOperateMapper.selectIpBWhInnerOperateByExpressCode(expressCodes);
//            this.selectOcBOrderDelivery(ipBWhInnerOperates, user);
//        } catch (Exception e) {
//            log.error(this.getClass().getName() + " 仓类作业转换异常", e);
//        }
//
//    }
//
//
//    /**
//     * 查询发货信息表
//     */
//    private String selectOcBOrderDelivery(List<IpBWhInnerOperate> ipBWhInnerOperates, User user) {
//        boolean flag = ipBWhInnerOperates.size() == 1;
//        Map<String, IpBWhInnerOperate> expressCodeMap = new HashMap<>();
//        // List<String> expressCodes = new ArrayList<>();
//        JSONArray jsonArray = new JSONArray();
//
//        for (IpBWhInnerOperate ipBWhInnerOperate : ipBWhInnerOperates) {
//            expressCodeMap.put(ipBWhInnerOperate.getExpressCode(), ipBWhInnerOperate);
//            jsonArray.add(ipBWhInnerOperate.getExpressCode());
//        }
//
//        JSONArray data = ES4OrderDelivery.findDataByLogisticNumber(jsonArray);
//
//        if (CollectionUtils.isEmpty(data)) {
//            String remark = "找不到有效的原单,转换完成";
//            for (Object obj : jsonArray) {
//                String expressCode = (String) obj;
//                ipBWhInnerOperateMapper.updateIpBWhInnerOperate(expressCode, TransferOrderStatus.TRANSFERRED.toInteger(), remark);
//            }
//            return remark;
//        }
//        Set<Long> orderIds = new HashSet<>();
//        for (int i = 0; i < data.size(); i++) {
//            Long orderId = data.getJSONObject(i).getLong("OC_B_ORDER_ID");
//            orderIds.add(orderId);
//        }
//
//        List<OcBOrderDelivery> deliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderIdList(new ArrayList<>(orderIds));
//        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(new ArrayList<>(orderIds));
//        Map<String, OcBOrder> orderMap = new HashMap<>();
//        for (OcBOrder ocBOrder : ocBOrders) {
//            orderMap.put(ocBOrder.getExpresscode(), ocBOrder);
//        }
//        Map<String, List<OcBOrderDelivery>> deliveryMap = new HashMap<>();
//        for (OcBOrderDelivery delivery : deliveries) {
//            if (!deliveryMap.containsKey(delivery.getLogisticNumber())) {
//                List<OcBOrderDelivery> deliveryList = new ArrayList<>();
//                deliveryList.add(delivery);
//                deliveryMap.put(delivery.getLogisticNumber(), deliveryList);
//            } else {
//                List<OcBOrderDelivery> deliveryList = deliveryMap.get(delivery.getLogisticNumber());
//                deliveryList.add(delivery);
//                deliveryMap.put(delivery.getLogisticNumber(), deliveryList);
//            }
//        }
//        for (Object str : jsonArray) {
//            String logisticNumber = String.valueOf(str);
//            IpBWhInnerOperate innerOperate = expressCodeMap.get(logisticNumber);
//            List<OcBOrderDelivery> deliveriesList = deliveryMap.get(logisticNumber);
//            OcBOrder ocBOrder = orderMap.get(logisticNumber);
//            if (ocBOrder == null) {
//                String remark = "找不到有效的原单,转换完成";
//                ipBWhInnerOperateMapper.updateIpBWhInnerOperate(logisticNumber, TransferOrderStatus.TRANSFERRED.toInteger(), remark);
//                if (flag) {
//                    return remark;
//                }
//                continue;
//            }
//            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemList(ocBOrder.getId());
//            //更新订单主表的物流状态
//            OcBOrder order = new OcBOrder();
//            order.setId(ocBOrder.getId());
//            order.setCainiaoWhStatus(innerOperate.getBillStatus());
//            ocBOrderMapper.updateById(order);
//
//            for (OcBOrderDelivery delivery : deliveriesList) {
//                //更新发货信息的表的物流状态
//                ocBOrderDeliveryMapper.updateSendGoodsStatusById(innerOperate.getBillStatus(), delivery.getId(), delivery.getOcBOrderId());
//                //判断菜鸟仓作业状态
//            }
//            this.judgeLogisticsStatus(innerOperate, deliveriesList, ocBOrder, orderItems, user);
//
//        }
//        return "转换完成";
//    }
//
//    /**
//     * 判断物流作业状态
//     *
//     * @param innerOperate
//     * @param deliveriesList 一个包裹
//     */
//    private void judgeLogisticsStatus(IpBWhInnerOperate innerOperate, List<OcBOrderDelivery> deliveriesList,
//                                      OcBOrder ocBOrder, List<OcBOrderItem> orderItems, User user) {
//        String expressCode = innerOperate.getExpressCode();
//        User rootUser = SystemUserResource.getRootUser();
//        String remark = "";
//        List<OcBReturnOrderRelation> orderRelations = null;
//        if (LogisticsStatusEnum.REFUSE.getCode().equals(innerOperate.getBillStatus())) {
//            //买家拒收
//            remark = "状态为买家拒收,转换完成!";
//            orderRelations = omsGoodsAfterRefundOnlyService.logisticsInterceptSuccessAndRejection(ocBOrder, orderItems,
//                    deliveriesList, expressCode, 0, TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_REJECTION, user);
//        } else if (LogisticsStatusEnum.TMSCANCELED.getCode().equals(innerOperate.getBillStatus())) {
//            //拦截成功
//            remark = "状态为拦截成功,转换完成!";
//            orderRelations = omsGoodsAfterRefundOnlyService.logisticsInterceptSuccessAndRejection(ocBOrder, orderItems,
//                    deliveriesList, expressCode, 3, TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT, user);
//        } else if (LogisticsStatusEnum.TMSCANCELFAILED.getCode().equals(innerOperate.getBillStatus())) {
//            //拦截失败
//            remark = "状态为拦截失败,转换完成!";
//            orderRelations = omsGoodsAfterRefundOnlyService.logisticsInterceptSuccessAndRejection(ocBOrder, orderItems,
//                    deliveriesList, expressCode, 2, TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT, user);
//        } else {
//            remark = "状态不是拦截及拒收,转换完成!";
//        }
//        if (CollectionUtils.isNotEmpty(orderRelations)) {
//            omsReturnOrderService.insertOmsReturnOrderInfo(orderRelations, rootUser);
//        }
//        ipBWhInnerOperateMapper.updateIpBWhInnerOperate(expressCode, TransferOrderStatus.TRANSFERRED.toInteger(), remark);
//    }
//
//
//    /**
//     * 仓内作业手动转换
//     *
//     * @param expressNo
//     * @return
//     */
//    public String warehouseOperationTransfer(String expressNo, User user) {
//        IpBWhInnerOperate innerOperate = ipBWhInnerOperateMapper.selectIpBWhInnerOperate(expressNo);
//        List<IpBWhInnerOperate> list = new ArrayList<>();
//        list.add(innerOperate);
//        return this.selectOcBOrderDelivery(list, user);
//    }
//}
