package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsShopStorageQueryItemResult;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsShopStorageQueryResult;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutQueryShareStoreItemRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutQueryShareStoreRequest;
import com.google.common.collect.Lists;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.model.SgBPhyInStorageItemExt;
import com.jackrain.nea.oc.model.SgBShopStorageResult;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPromItemMapper;
import com.jackrain.nea.oc.oms.model.GroupOrder;
import com.jackrain.nea.oc.oms.model.OrderMoney;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.result.OrderItemExtResult;
import com.jackrain.nea.oc.oms.model.result.SplitOrderResult;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromItem;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.util.*;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.AddAndVoidStockListService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OrderTagUtil;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.jackrain.nea.oc.oms.util.BigDecimalUtil.isNullReturnZero;

/**
 * <p>手动拆分订单服务</>
 *
 * @author: heliu
 * @since: 2019/3/22
 * create at : 2019/3/22 16:35
 */
@Slf4j
@Component
public class OmsOrderManualSplitService {
    @Autowired
    private IpJitxOrderService ipJitxOrderService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;

    @Autowired
    private OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private AddAndVoidStockListService addAndVoidStockListService;

    @Autowired
    private GroupProductSplitService groupProductSplitService;

    @Autowired
    private OmsOrderDistributeWarehouseService omsWarehousRuleService;

    @Autowired
    private OcBOrderPromItemMapper ocBOrderPromItemMapper;

    @Autowired
    private OmsOrderJdSplitService omsOrderJdSplitService;

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    @Autowired
    private OmsAuditTaskService omsAuditTaskService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OrderAmountUtil orderAmountUtil;

    //组合商品标识前缀
    private static final String GROUP_GOODS_MARK = "CG";

    @Autowired
    private SplitOrderUtils splitOrderUtils;

    @Autowired
    private OmsWmsTaskService wmsTaskService;

    @Autowired
    OmsOrderSplitReasonUtil omsOrderSplitReasonUtil;

    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;

    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcbCancelOrderMergeService cancelOrderMergeService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    /**
     * 初始化查询接口
     *
     * @param obj  json对象
     * @param user 用户对象
     * @return ValueHolderV14
     */
    public ValueHolderV14<JSONArray> querySkuListAndStorageInfo(JSONObject obj, User user) {

        ValueHolderV14<JSONArray> vh = new ValueHolderV14<>(ResultCode.SUCCESS,"查询成功");
        try {
            Long orderId = obj.getLong("orderId");
            Long skuId = obj.getLong("skuId");
            Long shareStoreId = obj.getLong("shareStoreId");

            //查询待确认和缺货状态单据
            OcBOrder ocBorderDto = omsOrderService.selectOrderInfo(orderId);
            AssertUtil.assertException(ocBorderDto == null,"订单不存在！");

            //如果是经销商OMS订单转成的零售发货单，不允许拆单
            boolean isCheckOrderIssuing = omsOrderService.checkOrderIssuing(ocBorderDto.getId(),ocBorderDto.getCpCShopId());
            AssertUtil.assertException(isCheckOrderIssuing,"订单对应平台店铺的店铺渠道为一件代发经销平台，无法拆分!");

            AssertUtil.assertException(!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBorderDto.getOrderStatus())
                    && !OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBorderDto.getOrderStatus()),
                    "订单非待审核或缺货状态，不符合拆单条件，无法拆分!");

            //订单是否被拦截
            AssertUtil.assertException(OmsOrderIsInterceptEnum.YES.getVal().equals(ocBorderDto.getIsInterecept()),
                    "订单HOLD单状态，不符合拆单条件，无法拆分!");
            AssertUtil.assertException(PlatFormEnum.VIP_JITX.getCode().equals(ocBorderDto.getPlatform()),
                    "订单为JITX订单，不符合拆单条件，无法拆分!");

            //订单是否在退款中
            AssertUtil.assertException(ocBorderDto.getIsInreturning() == 1,"订单退款中，不符合拆单条件，无法拆分!");

            // 货到付款订单不允许拆单
            AssertUtil.assertException(ocBorderDto.getPayType() != null &&
                    OmsPayType.CASH_ON_DELIVERY.toInteger() == ocBorderDto.getPayType(),
                    "订单为货到付款订单,不符合拆单条件，无法拆分!");

            //预售状态为预售尾款未付的订单不允许手工拆单
            String statusPayStep = ocBorderDto.getStatusPayStep();
            AssertUtil.assertException(OrderTypeEnum.TBA_PRE_SALE.getVal().equals(ocBorderDto.getOrderType())
                    && StringUtils.isNotEmpty(statusPayStep) && !TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equals(statusPayStep),
                    "订单为预售状态为预售尾款未付订单,不符合拆单条件，无法拆分!");

            JSONArray jsonArray = new JSONArray();
            List<Long> skuList = new ArrayList<>();
            //判断是查询聚合仓还是实体仓，实体仓不用查询明细直接根据前端传入的参数查询
            if(!ObjectUtils.isEmpty(skuId) && !ObjectUtils.isEmpty(shareStoreId)){
                OrderItemExtResult orderItemExtResult = new OrderItemExtResult();
                skuList.add(skuId);
                ValueHolderV14<Map<Long,SgBShopStorageResult>> shareResult =
                        queryOmsShopStorage(ocBorderDto,skuList,shareStoreId,user);
                if(!shareResult.isOK()){
                    vh.setCode(shareResult.getCode());
                    vh.setMessage(shareResult.getMessage());
                    return vh;
                }
                Map<Long,SgBShopStorageResult> shareMap = shareResult.getData();
                orderItemExtResult.setSgBPhyInStorageItemExt(shareMap.get(skuId).getPhyInStorageItemExtList());
                jsonArray.add(JSON.toJSON(orderItemExtResult));
                vh.setData(jsonArray);
                return vh;
            }

            //查询未退款的明细，未拆分的组合商品
            List<OcBOrderItem> ocBOrderItemList = omsOrderItemService.selectUnSuccessRefundAndGroupOrderItem(ocBorderDto.getId());
            AssertUtil.assertException(CollectionUtils.isEmpty(ocBOrderItemList),"订单明细为空,不符合拆单条件，无法拆分!");

            ocBOrderItemList.forEach(item -> skuList.add(item.getPsCSkuId()));
            ValueHolderV14<Map<Long,SgBShopStorageResult>> shareResult =
                    queryOmsShopStorage(ocBorderDto,skuList,shareStoreId,user);

            SgBShareOutQueryShareStoreRequest shareStoreRequest = new SgBShareOutQueryShareStoreRequest();
            List<SgBShareOutQueryShareStoreItemRequest > storeItemRequest = new ArrayList<>();
            for (OcBOrderItem item : ocBOrderItemList){
                SgBShareOutQueryShareStoreItemRequest sgBShareOutQueryShareStoreItemRequest = new SgBShareOutQueryShareStoreItemRequest();
                sgBShareOutQueryShareStoreItemRequest.setPsCSkuId(item.getPsCSkuId());
                storeItemRequest.add(sgBShareOutQueryShareStoreItemRequest);
            }
            shareStoreRequest.setSourceBillNo(ocBorderDto.getBillNo());
            shareStoreRequest.setItems(storeItemRequest);
            Map<Long, Map<Long, String>> longListMap = sgRpcService.sgQuerySgBShareOut(shareStoreRequest);
            if(!shareResult.isOK()){
                vh.setMessage(shareResult.getMessage());
                vh.setCode(shareResult.getCode());
                return vh;
            }
            Map<Long,SgBShopStorageResult> shareMap = shareResult.getData();


            for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                SgBShopStorageResult storageResult = shareMap.get(ocBOrderItem.getPsCSkuId());
                OrderItemExtResult orderItemExtResult = new OrderItemExtResult();
                //建议仓库赋值
                if(ObjectUtils.isEmpty(storageResult)){
                    orderItemExtResult.setSgBPhyInStorageItemExt(getDefaultItemExt(ocBorderDto));
                }else {
//                    orderItemExtResult.setShareStoreIds(storageResult.getShareStorageIds());
                    if (MapUtils.isNotEmpty(longListMap)){
                        orderItemExtResult.setShareStoreIds(longListMap.get(ocBOrderItem.getPsCSkuId()));
                    }
                    orderItemExtResult.setSgBPhyInStorageItemExt(Optional
                            .ofNullable(storageResult.getPhyInStorageItemExtList())
                            .orElse(getDefaultItemExt(ocBorderDto)));
                }
                //其他拆单明细赋值
                transOrderItem(orderItemExtResult,ocBOrderItem,ocBorderDto);
                jsonArray.add(JSON.toJSON(orderItemExtResult));
            }
            vh.setData(jsonArray);
            return vh;
        } catch (Exception ex) {
            log.error(LogUtil.format("查询平台店铺库存关系异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            vh.setCode(-1);
            vh.setMessage(ex.getMessage());
            return vh;
        }
    }

    /**
     * 将订单明细信息转换为拆单界面拆单明细
     * @param orderItemExtResult 拆单明细
     * @param ocBOrderItem 订单明细
     * @param ocBOrderDto 订单
     */
    private void transOrderItem(OrderItemExtResult orderItemExtResult,OcBOrderItem ocBOrderItem,OcBOrder ocBOrderDto){
        orderItemExtResult.setPlatform(ocBOrderDto.getPlatform());
        orderItemExtResult.setTid(ocBOrderItem.getTid());
        orderItemExtResult.setOrig_order_id(ocBOrderDto.getId());
        orderItemExtResult.setOrig_order_item_id(ocBOrderItem.getId());
        orderItemExtResult.setProType(ocBOrderItem.getProType());
        orderItemExtResult.setPs_c_pro_ecode(ocBOrderItem.getPsCProEcode());
        orderItemExtResult.setPs_c_pro_ename(ocBOrderItem.getPsCProEname());
        orderItemExtResult.setPs_c_clr_id(ocBOrderItem.getPsCClrId());
        orderItemExtResult.setPs_c_clr_ecode(ocBOrderItem.getPsCClrEcode());
        orderItemExtResult.setPs_c_clr_ename(ocBOrderItem.getPsCClrEname());
        orderItemExtResult.setPs_c_size_id(ocBOrderItem.getPsCSizeId());
        orderItemExtResult.setPs_c_size_ecode(ocBOrderItem.getPsCSizeEcode());
        orderItemExtResult.setPs_c_size_ename(ocBOrderItem.getPsCSizeEname());
        orderItemExtResult.setPs_c_sku_id(ocBOrderItem.getPsCSkuId());
        orderItemExtResult.setPs_c_sku_ecode(ocBOrderItem.getPsCSkuEcode());
        orderItemExtResult.setPs_c_sku_name(ocBOrderItem.getPsCSkuEname());
        orderItemExtResult.setPrice_list(ocBOrderItem.getPriceList());
        orderItemExtResult.setPrice(ocBOrderItem.getPrice());
        orderItemExtResult.setAmt_discount(ocBOrderItem.getAmtDiscount());
        orderItemExtResult.setAdjust_amt(ocBOrderItem.getAdjustAmt());
        orderItemExtResult.setQty(ocBOrderItem.getQty());
        orderItemExtResult.setWaiting_split_num(ocBOrderItem.getQty());
        orderItemExtResult.setSplit_num(new BigDecimal(0));
        orderItemExtResult.setIs_gift(ocBOrderItem.getIsGift());
        orderItemExtResult.setCp_c_phy_warehouse_id(ocBOrderDto.getCpCPhyWarehouseId());
        orderItemExtResult.setCp_c_phy_warehouse_ecode(ocBOrderDto.getCpCPhyWarehouseEcode());
        orderItemExtResult.setCp_c_phy_warehouse_ename(ocBOrderDto.getCpCPhyWarehouseEname());
    }

    /**
     * 组装明细查询库存
     *
     * @param orderItemExtResult 明细
     * @param stringListMap      stringListMap
     * @return List<SgBPhyInStorageItemExt>
     */
    private List<SgBPhyInStorageItemExt> querySkuListAndStorageItemExt(OrderItemExtResult orderItemExtResult, Map<String, List<SgBPhyInStorageItemExt>> stringListMap) {

        Long orderId = orderItemExtResult.getOrig_order_id();
        String psCSkuEcode = orderItemExtResult.getPs_c_sku_ecode();
        try {
            //当skuCode和sku对应的明细库存集合不为空时
            if (stringListMap.containsKey(psCSkuEcode) && CollectionUtils.isNotEmpty(stringListMap.get(psCSkuEcode))) {
                //按照可用数量排序
                List<SgBPhyInStorageItemExt> newSgBPhyInStorageItemExtList = stringListMap.get(psCSkuEcode).stream()
                        .sorted(Comparator.comparing(SgBPhyInStorageItemExt::getTotal_qty_available).reversed())
                        .collect(Collectors.toList());
                return newSgBPhyInStorageItemExtList;
            } else {
                //当没有匹配到对应sku的可用库存时直接默认给0
                List<SgBPhyInStorageItemExt> sgBPhyInStorageItemExtList = new ArrayList<>();
                SgBPhyInStorageItemExt sgBPhyInStorageItemExt = new SgBPhyInStorageItemExt();
                sgBPhyInStorageItemExt.setPs_c_sku_ecode(orderItemExtResult.getPs_c_sku_ecode());
                sgBPhyInStorageItemExt.setAdvise_phy_warehouse_id(orderItemExtResult.getCp_c_phy_warehouse_id());
                sgBPhyInStorageItemExt.setAdvise_phy_warehouse_ecode(orderItemExtResult.getCp_c_phy_warehouse_ecode());
                sgBPhyInStorageItemExt.setAdvise_phy_warehouse_ename(orderItemExtResult.getCp_c_phy_warehouse_ename());
                // 商品类型
                Long proType = orderItemExtResult.getProType();
                if (proType != null && (SkuType.COMBINE_PRODUCT == proType || SkuType.GIFT_PRODUCT == proType)) {
                    //sgRpcService.//.selectGroupPro();
                    sgBPhyInStorageItemExt.setTotal_qty_available(BigDecimal.ZERO);
                } else {
                    sgBPhyInStorageItemExt.setTotal_qty_available(BigDecimal.ZERO);
                }
                sgBPhyInStorageItemExtList.add(sgBPhyInStorageItemExt);
                return sgBPhyInStorageItemExtList;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("OMS订单调用聚合计算服务失败,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            return new ArrayList<>();
        }
    }

    /**
     * 组装拆分订单明细
     */
    private List<SplitOrderResult> assemblySplitOrderDetailResult(SplitOrder splitOrder, JSONArray jsonArray) {
        List<SplitOrderResult> splitOrderResultList = new ArrayList<>();
        SplitOrderResult splitOrderResult = null;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject resJson = jsonArray.getJSONObject(i);
            Long shareStoreId = resJson.getLong("share_storage_id");

            splitOrderResult = new SplitOrderResult();
            if (splitOrder.getOrigOrderId() == null) {
                splitOrder.setOrigOrderId(resJson.getLong("orig_order_id"));
            }
            // 原始订单Id
            splitOrderResult.setOrigOrderId(resJson.getLong("orig_order_id"));
            // 原始明细Id
            splitOrderResult.setOrigOrderItemId(resJson.getLong("orig_order_item_id"));
            // 拆单重新生成的实体仓ID
            splitOrderResult.setPhyWarehouseId(resJson.getLong("advise_phy_warehouse_id"));
            // 实体仓编码
            splitOrderResult.setPhyWarehouseEcode(resJson.getString("advise_phy_warehouse_ecode"));
            // 实体仓名称
            splitOrderResult.setPhyWarehouseEname(resJson.getString("advise_phy_warehouse_ename"));

            if (PlatFormEnum.VIP_JITX.getCode().equals(resJson.getInteger("platform"))) {
                if (resJson.getLong("cp_c_phy_warehouse_id") != null && !resJson.getLong("cp_c_phy_warehouse_id").equals(resJson.getLong("advise_phy_warehouse_id"))) {
                    // 拆单重新生成的实体仓ID
                    splitOrderResult.setPhyWarehouseId(resJson.getLong("cp_c_phy_warehouse_id"));
                    // 实体仓编码
                    splitOrderResult.setPhyWarehouseEcode(resJson.getString("cp_c_phy_warehouse_ecode"));
                    // 实体仓名称
                    splitOrderResult.setPhyWarehouseEname(resJson.getString("cp_c_phy_warehouse_ename"));
                }
            }

            // 获取拆分数量
            splitOrderResult.setQty(new BigDecimal(resJson.getLong("split_num")));

            splitOrderResult.setShareStorageId(shareStoreId);
            splitOrderResult.setShareStorageEcode(resJson.getString("share_storage_ecode"));
            splitOrderResult.setShareStorageEname(resJson.getString("share_storage_ename"));

            splitOrderResultList.add(splitOrderResult);
        }
        return splitOrderResultList;
    }

    /**
     * 组装拆分订单
     */
    private List<SplitOrder> assemblySplitOrderResult(JSONArray jsonArray) {
        List<SplitOrder> retList = new ArrayList<>();
        SplitOrder splitOrder = null;
        Integer realSize = 0;
        Integer emptySize = 0;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray detailArray = jsonArray.getJSONArray(i);
            if (detailArray != null && !detailArray.isEmpty()) {
                splitOrder = new SplitOrder();
                List<SplitOrderResult> splitOrderResultList = assemblySplitOrderDetailResult(splitOrder, detailArray);
                splitOrder.setSplitOrderItemList(splitOrderResultList);
                retList.add(splitOrder);

                List<SplitOrderResult> empty = splitOrderResultList.stream()
                        .filter(split -> ObjectUtils.isEmpty(split.getShareStorageId()) || ObjectUtils.isEmpty(split.getPhyWarehouseId()))
                        .collect(Collectors.toList());
                realSize += splitOrderResultList.size();
                if(CollectionUtils.isNotEmpty(empty)){
                    emptySize += empty.size();
                }
            }
        }
        
        AssertUtil.assertException(emptySize > 0 && !realSize.equals(emptySize),"手工拆单若指定实体仓拆单，所有明细的建议聚合仓、建议实体仓都不能为空");
        return retList;
    }

    /**
     * 保存拆分订单sku明细
     *
     * @param param 拆分sku明细
     * @param user  用户对象
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveSplitOrderInfo(String param, User user) {
        /** 1.解析前台传入参数*/
        ValueHolderV14 vh = new ValueHolderV14();
        // 解析JSON字符串
        JSONObject jsonObject = JSON.parseObject(param);
        // 获取前台传入Data数据
        JSONArray jsonArray = jsonObject.getJSONArray("data");
        if (jsonArray == null || jsonArray.isEmpty()) {
            vh.setCode(-1);
            vh.setMessage("拆单数据为空！");
            return vh;
        }
        /** 2.封装前台参数并校验*/
        // 解析并封装SplitOrder
        List<SplitOrder> splitOrderList = this.assemblySplitOrderResult(jsonArray);
        /*校验参数*/
        ValueHolderV14<OcBOrder> holderV14 = checkData(splitOrderList.get(0));
        if (holderV14 != null) {
            return holderV14;
        }

        /*原订单信息*/
        final OcBOrder ocBOrder = checkOcBOrder(splitOrderList.get(0), false);
        if (ocBOrder == null) {
            vh.setCode(-1);
            vh.setMessage("订单处于HOLD单状态或非缺货或非待审核状态不能拆单");
            return vh;
        }
        // 猫超订单和货到付款订单不允许拆单
        if (PlatFormEnum.ALIBABAASCP.getCode().equals(ocBOrder.getPlatform()) ||
                Optional.ofNullable(ocBOrder.getPayType()).orElse(1) == OmsPayType.CASH_ON_DELIVERY.toInteger()) {
            vh.setCode(-1);
            vh.setMessage("猫超订单或货到付款订单不进行拆单操作");
            return vh;
        }

        /** 3.加锁订单*/
        // 原订单ID，该处不用判空，因为this.checkData方法返回必然不会为空
        // 加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                // 校验订单数据
                ValueHolderV14 checkResult = this.checkData(splitOrderList.get(0), false);
                final OcBOrder origOrder;
                if (checkResult.isOK()) {
                    origOrder = (OcBOrder) checkResult.getData();
                } else {
                    return checkResult;
                }
                /** 3.1.拼装即将落库的orderRelationList*/
                List<OcBOrderRelation> orderRelationList = new ArrayList<>();
                int splitIndex = 1;
                for (SplitOrder so : splitOrderList) {
                    // 组装订单orderRelation
                    OcBOrderRelation orderRelation = this.assemblyOrderRelation(splitIndex, origOrder, so.getSplitOrderItemList(), user);
                    orderRelationList.add(orderRelation);
                    splitIndex++;
                }

                /** 3.2.调整订单明细金额（拆分前原单订单相关金额和拆分后订单相关金额进行比较，算出差值后补缺）*/
                this.orderAmtRegulator(origOrder, orderRelationList);

                /** 3.3.调整拆分后订单金额*/
                int orderSplitNum = orderRelationList.size();
                int num =0 ;
                for (OcBOrderRelation orderRelation : orderRelationList) {
                    num++;
                    /* 根据订单明细计算订单相关金额*/
                    List<OcBOrderItem> orderItemList = orderRelation.getOrderItemList();
                    OcBOrder orderInfo = orderRelation.getOrderInfo();
                    if (num!=1){
                        origOrder.setShipAmt(BigDecimal.ZERO);
                    }
                    this.computeOrderAmt(origOrder, orderSplitNum, orderInfo, orderItemList);
                    orderAmountUtil.recountOrderAmount(orderRelation);
                    OrderTagUtil.orderTags(orderRelation);
                }
                StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(origOrder.getCpCShopId());
                if (shopStrategy == null) {
                    vh.setCode(-1);
                    vh.setMessage("店铺策略为空!");
                    return vh;
                }
                if (PlatFormEnum.JINGDONG.getCode().equals(origOrder.getPlatform())
                        && "Y".equalsIgnoreCase(shopStrategy.getIsPlatformSplit())
                        && !"手工新增".equals(origOrder.getOrderSource())) {
                    ValueHolderV14 jdVh = splitJinDongPlatformOrder(orderRelationList, origOrder, user);
                    /**  京东拆单  */
                    if (jdVh.getCode() == ResultCode.SUCCESS) {
                        String logMsg = "OrderId="+ocBOrder.getId()+"调用京东拆单接口成功！";
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), logMsg, "", "", user);
                        return jdVh;
                    }
                }

                //是否指定实体仓拆单
                boolean isAppoint = true;
                for(OcBOrderRelation relation : orderRelationList){
                    if(ObjectUtils.isEmpty(relation.getShareStoreId()) || ObjectUtils.isEmpty(relation.getOrderInfo().getCpCPhyWarehouseId())){
                        isAppoint = false;
                        break;
                    }
                }
                if(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(origOrder.getOrderStatus()) || !isAppoint){
                    //如果原单缺货或非指定实体仓拆分，不保存仓库信息
                    orderRelationList.forEach(o -> {
                        OcBOrder order = o.getOrderInfo();
                        order.setCpCPhyWarehouseId(null);
                        order.setCpCPhyWarehouseEcode(null);
                        order.setCpCPhyWarehouseEname(null);
                        o.setOrderInfo(order);
                    });
                }

                /** 3.4.落库订单信息，并调用分配物流服务*/
                this.saveSplitOrderAndCallDistributeLogistics(origOrder.getId(), orderRelationList, user, false);

                /** 3.5.调用批量作废新增逻辑发货单*/
                // 排除组合商品明细中protype = 4的OrderItem
                List<OcBOrderRelation> newOrderRelationList = this.removeGroupOrderItemForProType4(orderRelationList);
                ValueHolderV14 returnResult = this.callAddAndVoidStock(origOrder.getId(), origOrder,isAppoint, newOrderRelationList, user);
                if (!returnResult.isOK()) {
                    throw new NDSException(returnResult.getMessage());
                }
            } else {
                vh.setCode(-1);
                vh.setMessage("当前订单其他人在操作，请稍后再试!");
                return vh;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("手动拆单保存服务失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("手动拆单保存服务失败!异常信息:" + e.getMessage());
        } finally {
            redisLock.unlock();
        }
        vh.setCode(0);
        vh.setMessage("手动拆单保存服务成功!");
        return vh;
    }

    /**
     * O2O 订单拆分
     *
     * @param param 拆分sku明细
     * @param user  用户对象
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 splitO2Order(String param, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        JSONObject jsonObject = JSON.parseObject(param);
        JSONArray jsonArray = jsonObject.getJSONArray("data");
        List<SplitOrder> splitOrderList = this.assemblySplitOrderResult(jsonArray);

        OcBOrder origOrder;
        try {
            ValueHolderV14<OcBOrder> checkResult = this.checkData(splitOrderList.get(0), true);
            if (checkResult.isOK()) {
                origOrder = checkResult.getData();
            } else {
                return checkResult;
            }
            /** 3.1.拼装即将落库的orderRelationList*/
            List<OcBOrderRelation> orderRelationList = new ArrayList<>();
            int splitIndex = 1;
            for (SplitOrder so : splitOrderList) {
                OcBOrderRelation orderRelation = this.assemblyOrderRelation(splitIndex, origOrder, so.getSplitOrderItemList(), user);
                // o2o 物流为原单
                orderRelation.getOrderInfo().setCpCLogisticsId(origOrder.getCpCLogisticsId());
                orderRelation.getOrderInfo().setCpCLogisticsEcode(origOrder.getCpCLogisticsEcode());
                orderRelation.getOrderInfo().setCpCLogisticsEname(origOrder.getCpCLogisticsEname());
                orderRelationList.add(orderRelation);
                splitIndex++;
            }
            /** 3.2.调整订单明细金额（拆分前原单订单相关金额和拆分后订单相关金额进行比较，算出差值后补缺）*/
            this.orderAmtRegulator(origOrder, orderRelationList);

            /** 3.3.调整拆分后订单金额*/
            int orderSplitNum = orderRelationList.size();
            int num =0 ;
            for (OcBOrderRelation orderRelation : orderRelationList) {
                num++;
                if (num!=1){
                    origOrder.setShipAmt(BigDecimal.ZERO);
                }
                // 子单打标
                OrderTagUtil.orderTags(orderRelation);
                /* 根据订单明细计算订单相关金额*/
                this.computeOrderAmt(origOrder, orderSplitNum, orderRelation.getOrderInfo(), orderRelation.getOrderItemList());
            }

            StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(origOrder.getCpCShopId());
            if (shopStrategy == null) {
                vh.setCode(-1);
                vh.setMessage("店铺策略为空!");
                return vh;
            }
            if (PlatFormEnum.JINGDONG.getCode().equals(origOrder.getPlatform())
                    && "Y".equalsIgnoreCase(shopStrategy.getIsPlatformSplit())) {
                ValueHolderV14 jdVh = splitJinDongPlatformOrder(orderRelationList, origOrder, user);
                /**  京东拆单  */
                if (jdVh.getCode() == ResultCode.SUCCESS) {
                    return jdVh;
                }
            }
            /** 3.4.落库订单信息，并调用分配物流服务*/
            this.saveSplitOrderAndCallDistributeLogistics(origOrder.getId(), orderRelationList, user, true);

            /** 3.5.调用批量作废新增逻辑发货单*/
            List<OcBOrderRelation> newOrderRelationList = this.removeGroupOrderItemForProType4(orderRelationList);
            ValueHolderV14 returnResult = this.callAddAndVoidStock(origOrder.getId(), origOrder,true, newOrderRelationList, user);
            if (!returnResult.isOK()) {
                throw new NDSException(returnResult.getMessage());
            }

        } catch (Exception e) {
            String expMsg = ExceptionUtil.getMessage(e);
            log.error(LogUtil.format("订单拆分异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            String msg = "订单拆分异常:" + expMsg;
            throw new NDSException(msg);
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("订单拆分成功!");
        return vh;
    }

    /**
     * 京东订单，单独调用云枢纽，京东拆单接口
     *
     * @param
     * @param user
     * @return
     */
    private ValueHolderV14 splitJinDongPlatformOrder(List<OcBOrderRelation> relations, OcBOrder origOrder, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        try {
            omsOrderJdSplitService.splitOrderByJingdong(origOrder, relations, user);
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("成功");
        } catch (Exception e) {
            log.error(LogUtil.format("京东拆单失败,异常信息为:{}",origOrder.getId()), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
        }
        return vh;
    }

    private ValueHolderV14<OcBOrder> checkData(SplitOrder splitOrder) {
        ValueHolderV14 result = new ValueHolderV14();
        result.setCode(-1);
        if (splitOrder == null) {
            String errorMessage = "拆单数据无效,查询失败!";
            result.setMessage(errorMessage);
            return result;
        }
        return null;
    }

    /**
     * 查询订单信息
     *
     * @param splitOrder
     * @param isO2oOrder
     * @return
     */
    private OcBOrder checkOcBOrder(SplitOrder splitOrder, boolean isO2oOrder) {
        final OcBOrder ocBorderDto;
        Long origOrderId = splitOrder.getOrigOrderId();
        if (isO2oOrder) {
            ocBorderDto = omsOrderService.selectInDistributionOrder(origOrderId);
        } else {
            ocBorderDto = omsOrderService.selectOrderUnConfirmAndStockInfo(origOrderId);
        }
        return ocBorderDto;
    }

    /**
     * 校验订单数据
     *
     * @return
     */
    private ValueHolderV14<OcBOrder> checkData(SplitOrder splitOrder, boolean isO2oOrder) {
        ValueHolderV14 result = new ValueHolderV14();
        result.setCode(-1);
        if (splitOrder == null) {
            String errorMessage = "拆单数据无效,查询失败!";
            result.setMessage(errorMessage);
            return result;
        }
        Long origOrderId = splitOrder.getOrigOrderId();
        // 保存时候再次查询订单对象
        final OcBOrder ocBorderDto;
        if (isO2oOrder) {
            ocBorderDto = omsOrderService.selectInDistributionOrder(origOrderId);
        } else {
            ocBorderDto = omsOrderService.selectOrderUnConfirmAndStockInfo(origOrderId);
        }
        if (ocBorderDto == null) {
            String errorMessage = "订单" + origOrderId + "拆单查询对象无效,查询失败!";
            result.setMessage(errorMessage);
            return result;
        }
        // 订单在虚拟拆单过程中是否被拦截,再判断下
        if (ocBorderDto.getIsInterecept() == 1) {
            String errorMessage = "订单" + origOrderId + "已经被拦截，不允许操作！";
            result.setMessage(errorMessage);
            return result;
        }
        // 再次判断订单是否在退款中
        if (ocBorderDto.getIsInreturning() == 1) {
            String errorMessage = "订单" + origOrderId + "的订单已经在退款中，不允许操作！";
            result.setMessage(errorMessage);
            return result;
        }
        Integer orderStatus = ocBorderDto.getOrderStatus();
        String statusMsg;
        boolean isAllowSplit;
        if (isO2oOrder) {
            isAllowSplit = OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus);
            statusMsg = "订单状态不正确，无法执行订单拆分";
        } else {
            isAllowSplit = OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus);
            statusMsg = "订单" + origOrderId + "的订单状态异常，不允许手动拆单！";
        }
        if (!isAllowSplit) {
            result.setMessage(statusMsg);
            return result;
        }
        result.setCode(0);
        result.setData(ocBorderDto);
        return result;
    }

    /**
     * 如果是组合商品，获取组合商品信息
     */
    public GroupOrder getGroupOrder(OcBOrderRelation orderRelation) {
        GroupOrder groupOrder = null;
        // 是否为组合商品
        if (OcBOrderConst.IS_STATUS_IY.equals(orderRelation.getOrderInfo().getIsCombination())) {
            groupOrder = new GroupOrder();
            groupOrder.setOrderId(orderRelation.getOrderInfo().getId());

            List<OcBOrderItem> itemList = orderRelation.getOrderItemList();
            List<Long> groupSkuIdList = new ArrayList<>();
            BigDecimal groupSkuNum = BigDecimal.ZERO;
            for (OcBOrderItem item : itemList) {
                int proType = item.getProType() == null ? 0 : item.getProType().intValue();
                if (proType == SkuType.COMBINE_PRODUCT || proType == SkuType.GIFT_PRODUCT) {
                    groupSkuIdList.add(item.getPsCSkuId());
                    groupSkuNum = groupSkuNum.add(item.getQty() == null ? BigDecimal.ZERO : item.getQty());
                } else if (proType == SkuType.NO_SPLIT_COMBINE) {
                    groupOrder.setGroupOrderItem(item);
                }
            }
            groupOrder.setSkuIdList(groupSkuIdList);
            groupOrder.setSkuNum(groupSkuNum);
        }
        return groupOrder;
    }

    /**
     * 1.拆分前是组合商品，拆分后的组合商品和拆分前一致（即sku总数量，sku条数，skuId都一致），复制之前的组合商品头信息
     * 2.拆分前是组合商品，拆分后的组合商品和拆分前不一致，更改订单主表（IsCombination == 0），订单明细表（ProType == 0）
     */
    public void isGroupForSplitOrder(GroupOrder oldGroupOrder, OcBOrderRelation orderRelation, User user) {
        if (oldGroupOrder != null) {
            GroupOrder newGroupOrder = getGroupOrder(orderRelation);
            if (newGroupOrder != null
                    && oldGroupOrder.getSkuNum().compareTo(BigDecimal.ZERO) != 0
                    && oldGroupOrder.getSkuIdList().size() != 0
                    && oldGroupOrder.getSkuNum().compareTo(newGroupOrder.getSkuNum()) == 0
                    && oldGroupOrder.getSkuIdList().size() == newGroupOrder.getSkuIdList().size()
                    && oldGroupOrder.getSkuIdList().containsAll(newGroupOrder.getSkuIdList())) {
                // 新增组合商品明细
                OcBOrderItem ocBOrderItem = oldGroupOrder.getGroupOrderItem();
                OcBOrderItem ocBOrderItemNew = new OcBOrderItem();
                BeanUtils.copyProperties(ocBOrderItem, ocBOrderItemNew);
                // 重新生成Id
                ocBOrderItemNew.setId(ModelUtil.getSequence("oc_b_order_item"));
                // 设置订单Id(增加非空判断)
                if (null != orderRelation && null != orderRelation.getOrderInfo()) {
                    ocBOrderItemNew.setOcBOrderId(orderRelation.getOrderInfo().getId());
                }
                // 修改人
                ocBOrderItemNew.setModifierename(user.getEname());
                // 修改时间
                ocBOrderItemNew.setModifieddate(new Date());
                // 修改人ID
                ocBOrderItemNew.setModifierid(Long.valueOf(user.getId()));
                //增加非空判断
                if (null != orderRelation && CollectionUtils.isNotEmpty(orderRelation.getOrderItemList())) {
                    orderRelation.getOrderItemList().add(ocBOrderItemNew);
                }
            } else {
                if (null != orderRelation && null != orderRelation.getOrderInfo()) {
                    orderRelation.getOrderInfo().setIsCombination(OcBOrderConst.IS_STATUS_IN);
                }
                if (null != orderRelation && CollectionUtils.isNotEmpty(orderRelation.getOrderItemList())) {
                    for (OcBOrderItem orderItem : orderRelation.getOrderItemList()) {
                        orderItem.setProType(Long.valueOf(SkuType.NORMAL_PRODUCT));
                    }
                }
            }
        }
    }

    /**
     * 组装订单信息
     */
    private OcBOrderRelation assemblyOrderRelation(int splitIndex, OcBOrder origOrder,
                                                   List<SplitOrderResult> splitOrderResultList, User user) {
        // 订单信息
        OcBOrderRelation orderRelation = new OcBOrderRelation();
        int promCount = ocBOrderPromItemMapper.selectCount(new QueryWrapper<OcBOrderPromItem>().lambda().
                eq(OcBOrderPromItem::getOcBOrderId, origOrder.getId()));
        if (promCount > 0) {
            origOrder.setIsPromOrder(OcBOrderConst.IS_STATUS_IY);
        } else {
            origOrder.setIsPromOrder(OcBOrderConst.IS_STATUS_IN);
        }
        // 组装订单信息
        OcBOrder newOrder = this.assemblyOrder(splitIndex, origOrder, splitOrderResultList.get(0), user);
        //自定义拆单赋值
        omsOrderSplitReasonUtil.matchReason(newOrder);
        // 组装订单明细
        List<OcBOrderItem> newOrderItemList = this.assemblyOrderItem(newOrder, splitOrderResultList, user);
        orderRelation.setShareStoreId(splitOrderResultList.get(0).getShareStorageId());
        orderRelation.setShareStoreEcode(splitOrderResultList.get(0).getShareStorageEcode());
        orderRelation.setOrderInfo(newOrder);
        orderRelation.setOrderItemList(newOrderItemList);
        if (log.isDebugEnabled()) {
            log.debug("组装订单OrderRelation={}assemblyOrderRelation end", JSON.toJSONString(orderRelation));
        }
        return orderRelation;
    }

    /**
     * 组装订单
     */
    private OcBOrder assemblyOrder(int splitIndex, OcBOrder origOrder, SplitOrderResult splitOrderResult, User user) {
        OcBOrder newOrder = new OcBOrder();
        BeanUtils.copyProperties(origOrder, newOrder);
        Date now = new Date();
        BigDecimal zero = BigDecimal.ZERO;
        // 重新生成Id
        newOrder.setId(ModelUtil.getSequence("oc_b_order"));
        // 实体id
        newOrder.setCpCPhyWarehouseId(splitOrderResult.getPhyWarehouseId());
        // 设置ecode
        newOrder.setCpCPhyWarehouseEcode(splitOrderResult.getPhyWarehouseEcode());
        // 设置ename
        newOrder.setCpCPhyWarehouseEname(splitOrderResult.getPhyWarehouseEname());
        newOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        //如果实体仓是o2o仓库，对订单进行打标
        CpCPhyWarehouse cpCPhyWarehouse = omsWarehousRuleService.queryByWarehouseId(splitOrderResult.getPhyWarehouseId());
        if (!ObjectUtils.isEmpty(cpCPhyWarehouse) && StringUtils.equals(cpCPhyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
            newOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
        } else {
            newOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
        }
        // 订单补充信息 原订单ID-SP-splitIndex
        newOrder.setSuffixInfo(origOrder.getId() + "-SP" + splitIndex);
        // 订单编号
        newOrder.setBillNo(sequenceUtil.buildBillNo());
        // 是否拆分原单
        newOrder.setIsSplit(1);
        // 拆单原因
        newOrder.setSplitReason(SplitReason.SPLIT_MANUAL);
        // 拆分原订单号
        newOrder.setSplitOrderId(origOrder.getId());
        // 平台单号
        newOrder.setSourceCode(origOrder.getSourceCode());
        //oaid
        newOrder.setOaid(origOrder.getOaid());
        // 配送费用复制
        newOrder.setShipAmt(zero);
        // 服务费复制
        newOrder.setServiceAmt(zero);
        // 商品金额
        newOrder.setProductAmt(zero);
        // 调整金额
        newOrder.setAdjustAmt(zero);
        // 订单优惠金额
        newOrder.setOrderDiscountAmt(zero);
        // 商品优惠金额
        newOrder.setProductDiscountAmt(zero);
        // 订单总额
        newOrder.setOrderAmt(zero);
        // 应收金额
        newOrder.setAmtReceive(zero);
        // 已收金额
        newOrder.setReceivedAmt(zero);
        // 系统备注
        newOrder.setSysremark("");
        // 创建ID
        newOrder.setOwnerid(Long.valueOf(user.getId()));
        // 创建人用户名
        newOrder.setOwnername(user.getName());
        // 创建人姓名
        newOrder.setOwnerename(user.getEname());
        // 创建时间
        newOrder.setCreationdate(now);
        // 修改人ID
        newOrder.setModifierid(Long.valueOf(user.getId()));
        // 修改人姓名
        newOrder.setModifierename(user.getEname());
        // 修改人用户名
        newOrder.setModifiername(user.getName());
        // 修改时间
        newOrder.setModifieddate(now);
        //自定义拆单赋值
        omsOrderSplitReasonUtil.matchReason(newOrder);
        if (log.isDebugEnabled()) {
            log.debug("订单OrderID" + newOrder.getId() + "为手动拆单生成的订单");
            log.debug("assemblyOrder newOrder->" + JSON.toJSONString(newOrder));
        }
        log.debug("assemblyOrder end");
        return newOrder;
    }

    /**
     * 组装订单明细
     */
    private List<OcBOrderItem> assemblyOrderItem(OcBOrder newOrder, List<SplitOrderResult> splitOrderResultList, User user) {
        log.debug("assemblyOrderItem start");
        List<OcBOrderItem> orderItemList = new ArrayList<>();
        AtomicBoolean flag = new AtomicBoolean(false);
        String advanceTypeAll ="";
        Date estimateConTimeMax = null;
        Set<Long> set = new HashSet();
        // 循环处理拆分的订单明细
        for (SplitOrderResult splitOrderItem : splitOrderResultList) {
            // 取出原订单明细信息
            OcBOrderItem origOrderItem = omsOrderItemService.getOrderItemById(splitOrderItem.getOrigOrderItemId());

            if (origOrderItem != null && "Y".equals(origOrderItem.getIsactive())) {
                OcBOrderItem newOrderItem = new OcBOrderItem();
                // 复制并初始化newOrderItem
                this.copyAndInitOrderItem(origOrderItem, newOrderItem, splitOrderItem.getQty(), newOrder.getId(), user);
                if (!OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(newOrderItem.getIsGift()) && !set.contains(newOrderItem.getId())){
                    flag.set(true);
                    set.add(newOrderItem.getId());
                }else {
                    flag.set(false);
                }
                // 计算订单明细相关金额
                this.computeOrderItemAmt(splitOrderItem.getOrigOrderId(), origOrderItem, newOrderItem, flag.get());

                // 订单明细是【组合商品】(前台拆分过来的数据，protype 只能是4和0)
                if (origOrderItem.getProType() != null && origOrderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                    /* 调用方法【 omsConstituteSplitService.encapsulationParameter】，会返回分摊好组合下挂的sku金额*/
                    List<OcBOrderItem> groupOrderItemList = new ArrayList<>();
                    newOrderItem.setGroupGoodsMark(GROUP_GOODS_MARK + newOrderItem.getId());
                    groupOrderItemList.add(newOrderItem);
                    // 赋值TID在下方拆解组合商品使用订单
                    newOrder.setTid(newOrderItem.getTid());
                    List<OcBOrderItem> groupOfSkuList = omsConstituteSplitService.encapsulationParameter(groupOrderItemList, newOrder, user, 0);
                    if (groupOfSkuList == null || groupOfSkuList.size() == 0) {
                    } else {
                        orderItemList.addAll(groupOfSkuList);
                    }
                }
                if (newOrderItem.getEstimateConTime() !=null){
                    if (estimateConTimeMax ==null){
                        estimateConTimeMax =newOrderItem.getEstimateConTime();
                    }else {
                        if (newOrderItem.getEstimateConTime().before(estimateConTimeMax)){
                            estimateConTimeMax=newOrderItem.getEstimateConTime();
                        }
                    }
                }
                //拆单赋值对应的预售类型，明细类型的综合
                if (newOrderItem.getAdvanceType() !=null){
                    if (advanceTypeAll != null && !advanceTypeAll.contains(newOrderItem.getAdvanceType())){
                        advanceTypeAll+=newOrderItem.getAdvanceType()+",";
                    }
                }
                orderItemList.add(newOrderItem);
            }
        };
        //预售 类型
        if (advanceTypeAll.length() >0){
            newOrder.setAdvanceType(advanceTypeAll.substring(0,advanceTypeAll.length()-1));
            //订单=普通商品，不显示预售标
            if (AdvanceConstant.ORDINARY_GOODS.equals(advanceTypeAll.substring(0,advanceTypeAll.length()-1))){
                newOrder.setDouble11PresaleStatus(0);
                newOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
            }
        }else {
            newOrder.setAdvanceType("");
        }
        newOrder.setEstimateConTime(estimateConTimeMax);
        log.debug("assemblyOrderItem orderItemList->" + JSON.toJSONString(orderItemList));
        log.debug("assemblyOrderItem end");
        return orderItemList;
    }

    /**
     * 转换平台店铺库存关系
     * @param ocBorderDto 单据信息
     * @param skuIds skuid
     * @param shareStoreId 聚合仓id
     * @param loginUser 用户
     * @return 各个SKU对应的仓库列表
     */
    private ValueHolderV14<Map<Long,SgBShopStorageResult>> queryOmsShopStorage(OcBOrder ocBorderDto, List<Long> skuIds,
                                                                               Long shareStoreId, User loginUser){

        //RPC查询平台店铺库存关系
        ValueHolderV14<SgOmsShopStorageQueryResult> queryValueHolder = sgRpcService
                .queryOmsShopStorage(ocBorderDto.getCpCShopId(), skuIds, shareStoreId, loginUser);
        ValueHolderV14<Map<Long,SgBShopStorageResult>> result = new ValueHolderV14<>(ResultCode.SUCCESS,"查询成功");

        if(!queryValueHolder.isOK()){
            //查询失败直接返回
            result.setCode(com.jackrain.nea.sys.constants.ResultCode.FAIL);
            result.setMessage(queryValueHolder.getMessage());
            return result;
        }
        SgOmsShopStorageQueryResult queryResult = queryValueHolder.getData();
        Map<Long,SgBShopStorageResult> mapResult = Maps.newHashMap();

        skuIds.forEach(skuId -> {
            SgBShopStorageResult storageResult = new SgBShopStorageResult();
            if(queryResult.getPsSkuStorageResult().containsKey(skuId)){
                SgOmsShopStorageQueryItemResult queryItemResult = queryResult.getPsSkuStorageResult().get(skuId);
                if(ObjectUtils.isEmpty(shareStoreId)){
                    //聚合仓
                    Map<Long,BigDecimal> shareMap = queryItemResult.getShareStorageInfo();
                    storageResult.setShareStorageIds(shareMap.keySet());
                    storageResult.setPhyInStorageItemExtList(getDefaultItemExt(ocBorderDto));
                }else {
                    //实体仓
                    List<SgBPhyInStorageItemExt> storageItemExtList = new ArrayList<>();
                    Set<Long> phySet = queryItemResult.getPhyStorageInfo().keySet();
                    List<Long> phyIds = new ArrayList<>(phySet);
                    //查询实体仓信息
                    Map<Long, CpCPhyWarehouse> phyMap = Optional
                            .ofNullable(cpRpcService.rpcQueryCpCPhyWareHouses(phyIds))
                            .orElse(Maps.newHashMap());
                    BigDecimal shareQty = queryItemResult.getShareStorageInfo().get(shareStoreId);
                    phySet.forEach(phy -> {

                        BigDecimal phyQty = queryItemResult.getPhyStorageInfo().get(phy);
                        phyQty = shareQty.compareTo(phyQty) < 0 ? shareQty : phyQty;

                        SgBPhyInStorageItemExt sgBPhyInStorageItemExt = new SgBPhyInStorageItemExt();
                        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_id(phy);
                        sgBPhyInStorageItemExt.setTotal_qty_available(phyQty);
                        CpCPhyWarehouse phyWarehouse = Optional.ofNullable(phyMap.get(phy)).orElse(new CpCPhyWarehouse());
                        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_ecode(phyWarehouse.getEcode());
                        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_ename(phyWarehouse.getEname());
                        storageItemExtList.add(sgBPhyInStorageItemExt);
                    });
                    storageResult.setPhyInStorageItemExtList(storageItemExtList);
                }
            }else {
                //查询为空返回默认的仓库
                if(!ObjectUtils.isEmpty(shareStoreId)){
                    //实体仓
                    storageResult.setPhyInStorageItemExtList(getDefaultItemExt(ocBorderDto));
                    result.setCode(ResultCode.FAIL);
                    result.setMessage("未查询到有可用库存的实体仓！");
                }
            }
            mapResult.put(skuId,storageResult);
        });
        result.setData(mapResult);
        return result;

    }

    /**
     * 根据订单创建默认建议拆单实体仓
     * @param ocBOrder 订单
     * @return 建议拆弹实体仓实体
     */
    private List<SgBPhyInStorageItemExt> getDefaultItemExt(OcBOrder ocBOrder){
        List<SgBPhyInStorageItemExt> sgBPhyInStorageItemExtList = new ArrayList<>();
        SgBPhyInStorageItemExt sgBPhyInStorageItemExt = new SgBPhyInStorageItemExt();
        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_id(ocBOrder.getCpCPhyWarehouseId());
        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_ecode(ocBOrder.getCpCPhyWarehouseEcode());
        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_ename(ocBOrder.getCpCPhyWarehouseEname());
        sgBPhyInStorageItemExt.setTotal_qty_available(BigDecimal.ZERO);
        sgBPhyInStorageItemExtList.add(sgBPhyInStorageItemExt);
        return sgBPhyInStorageItemExtList;
    }

    /**
     * 复制并初始化订单明细
     *
     * @param sourceOrderItem 原始订单明细
     * @param targetOrderItem 新订单明细
     * @param qty             数量
     * @param newOrderId      新订单ID
     * @param user            用户
     */
    private void copyAndInitOrderItem(OcBOrderItem sourceOrderItem, OcBOrderItem targetOrderItem, BigDecimal qty, Long newOrderId, User user) {
        BeanUtils.copyProperties(sourceOrderItem, targetOrderItem);
        Date now = new Date();
        BigDecimal zero = BigDecimal.ZERO;
        // 重新生成Id
        targetOrderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
        // 设置订单Id
        targetOrderItem.setOcBOrderId(newOrderId);
        targetOrderItem.setQty(qty);
        targetOrderItem.setOrderSplitAmt(zero);
        targetOrderItem.setRealAmt(zero);
        targetOrderItem.setAmtDiscount(zero);
        targetOrderItem.setAdjustAmt(zero);
        // 创建ID
        targetOrderItem.setOwnerid(Long.valueOf(user.getId()));
        // 创建人用户名
        targetOrderItem.setOwnername(user.getName());
        // 创建人姓名
        targetOrderItem.setOwnerename(user.getEname());
        // 创建时间
        targetOrderItem.setCreationdate(now);
        // 修改人ID
        targetOrderItem.setModifierid(Long.valueOf(user.getId()));
        // 修改人姓名
        targetOrderItem.setModifierename(user.getEname());
        // 修改人用户名
        targetOrderItem.setModifiername(user.getName());
        // 修改时间
        targetOrderItem.setModifieddate(now);
    }

    /**
     * 根据订单明细计算订单相关金额
     *
     * @param originOrderSplitNum 原单拆分条数
     * @param order
     * @param orderItemList
     */
    private void computeOrderAmt(OcBOrder originOrder, int originOrderSplitNum, OcBOrder order, List<OcBOrderItem> orderItemList) {
        /* 计算订单明细下相关金额 （只算protype == 4 或 protype == 0）*/
        OrderMoney orderMoney = OrderMoneyCalculationUtils.calculateOrderAmtByOrderItemList(orderItemList);
        // 商品优惠金额
        BigDecimal amtDiscountTotal = orderMoney.getProductDiscountAmt();
        // 平摊金额之和
        BigDecimal orderSplitAmtTotal = orderMoney.getOrderDiscountAmt();
        // 调整金额之和
        BigDecimal adjustTotal = orderMoney.getAdjustAmt();
        // 商品金额
        BigDecimal productAmtTotal = orderMoney.getProductAmt();

        // 商品数量
        BigDecimal skuQtyTotal = BigDecimal.ZERO;
        // 赠品标识 false:没赠品 true: 有赠品
        boolean isGift = false;
        // 是否为组合商品 false:非组合商品 true: 组合商品
        boolean isGroup = false;
        // SKU条数（排除组合商品的条数）
        int orderItemNum = 0;
        for (OcBOrderItem ocBOrderItem : orderItemList) {
            if (ocBOrderItem.getProType() != SkuType.NO_SPLIT_COMBINE) {
                orderItemNum++;
                BigDecimal qty = BigDecimalUtil.isNullReturnZero(ocBOrderItem.getQty());
                // 商品数量计算
                skuQtyTotal = skuQtyTotal.add(qty);
            }
            // 订单明细中是否组合商品
            if (ocBOrderItem.getProType() == SkuType.COMBINE_PRODUCT && !isGroup) {
                isGroup = true;
            }
            // 订单明细中是否有赠品
            if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(ocBOrderItem.getIsGift()) && !isGift) {
                isGift = true;
            }
        }

        // 原单物流费用，服务费
        BigDecimal originShipAmt = isNullReturnZero(originOrder.getShipAmt());
        BigDecimal originServiceAmt = isNullReturnZero(originOrder.getServiceAmt());

        // 新单物流费用，服务费
        //BigDecimal newShipAmt = originShipAmt.divide(BigDecimal.valueOf(originOrderSplitNum), 4, BigDecimal.ROUND_DOWN);
        BigDecimal newServiceAmt = originServiceAmt.divide(BigDecimal.valueOf(originOrderSplitNum), 4, BigDecimal.ROUND_DOWN);

        // 订单总额计算 = 商品总额 + 物流费用 + 调整金额 + 服务费 - 商品优惠金额 - 订单优惠金额
        BigDecimal orderAmtTotal = productAmtTotal.add(originShipAmt).add(adjustTotal).add(newServiceAmt).subtract(amtDiscountTotal).subtract(orderSplitAmtTotal).setScale(4, BigDecimal.ROUND_DOWN);
        String msg = "订单orderId{} 订单总额计算{} = 商品总额{} + 物流费用{} + 调整金额{} + 服务费{} - 商品优惠金额{} - 订单优惠金额{}";
        log.debug(msg, order.getId(), orderAmtTotal, productAmtTotal, originShipAmt, adjustTotal, newServiceAmt, amtDiscountTotal, orderSplitAmtTotal);
        if (orderAmtTotal.compareTo(BigDecimal.ZERO) < 0) {
            log.debug("订单orderId{} 订单总额为负数", order.getId());
            orderAmtTotal = BigDecimal.ZERO;
        }

        /* 设定订单相关金额*/
        order.setShipAmt(originShipAmt);
        order.setServiceAmt(newServiceAmt);
        // 【商品数量】
        order.setQtyAll(skuQtyTotal);
        order.setSkuKindQty(new BigDecimal(orderItemNum));
        // 【商品总额】
        order.setProductAmt(productAmtTotal);
        // 【商品优惠金额】
        order.setProductDiscountAmt(amtDiscountTotal);
        // 【订单优惠金额】
        order.setOrderDiscountAmt(orderSplitAmtTotal);
        // 【订单总额】
        order.setOrderAmt(orderAmtTotal);
        // 【已支付金额】（已收金额）
        order.setReceivedAmt(orderAmtTotal);
        // 应收金额
        order.setAmtReceive(orderAmtTotal);
        // 【调整金额之和】
        order.setAdjustAmt(adjustTotal);
        // 是否有赠品 0:没赠品 1: 有赠品
        order.setIsHasgift(isGift ? 1 : 0);
        // 是否是组合商品 0:不是组合商品 1: 是组合商品
        order.setIsCombination(isGroup ? 1 : 0);
    }

    /**
     * 调用分配物流服务并更新订单表
     *
     * @param orderInfo
     * @param user
     * @return
     */
    private void callDistributeLogisticsAndUpdateOrder(OcBOrderRelation orderInfo, User user) {
        Long orderId = orderInfo.getOrderId();
        // 然后对于新订单执行分配物流服务
        // 调用分配物流服务
        CpCLogistics cpCLogistics = omsOrderDistributeLogisticsService.distributeLogistics(orderInfo);
        OcBOrder order = new OcBOrder();
        order.setId(orderId);
        if (cpCLogistics != null) {
            String message = "订单OrderId" + orderId + "拆分新订单调用分物流服务,订单分配到物流公司.返回物流公司Id[" + cpCLogistics.getId() + "][" + cpCLogistics.getEname() + "]";
            log.debug(message);
            order.setCpCLogisticsId(cpCLogistics.getId());
            order.setCpCLogisticsEname(cpCLogistics.getEname());
            order.setCpCLogisticsEcode(cpCLogistics.getEcode());
            // 插入日志
            omsOrderLogService.addUserOrderLog(orderId, orderInfo.getOrderInfo().getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), message, null, null, user);
        } else {
            String errorMessage = "订单OrderId" + orderId + "拆分新订单调用分物流服务未匹配到有效物流公司";
            // 为了清除原有的物流公司
            order.setSysremark(errorMessage);
            order.setCpCLogisticsId(0L);
            order.setCpCLogisticsEname("");
            order.setCpCLogisticsEcode("");
            // 插入日志
            omsOrderLogService.addUserOrderLog(orderId, orderInfo.getOrderInfo().getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), errorMessage, null, null, user);
        }
        // 更新订单信息
        omsOrderService.updateOrderInfo(order);
    }

    /**
     * 落库订单信息，并调用分配物流服务
     */
    public void saveSplitOrderAndCallDistributeLogistics(Long originOrderId, List<OcBOrderRelation> orderRelationList, User user, boolean isO2o) {

        List<Long> ids = null;
        for (OcBOrderRelation orderInfo : orderRelationList) {

            OcBOrder order = orderInfo.getOrderInfo();
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
            Long orderId = order.getId();
            //JITX订单需处理平台单号及物流单号
            if(PlatFormEnum.VIP_JITX.getCode().equals(order.getPlatform())){
                if(CollectionUtils.isNotEmpty(orderItemList)){
                    Optional<OcBOrderItem> first = orderItemList.stream().filter(x -> !YesNoEnum.Y.getVal().equals(x.getIsGift())).findFirst();
                    OcBOrderItem firstItem = first.get();
                    List<IpBJitxOrder> jitxOrders = ipJitxOrderService.queryAuditOrder(Lists.newArrayList(firstItem.getTid()));
                    if (CollectionUtils.isNotEmpty(jitxOrders)) {
                        IpBJitxOrder ipBJitxOrder = jitxOrders.get(0);
                        order.setTid(ipBJitxOrder.getOrderSn());
                        order.setSourceCode(ipBJitxOrder.getOrderSn());
                        order.setJitxMergedDeliverySn(ipBJitxOrder.getOrderSn());
                        order.setExpresscode(ipBJitxOrder.getTransportNo());
                    } else {
                        log.error("{},未查询到已审核的JITX订单,orderSn:{}", this.getClass().getSimpleName(), firstItem.getTid());
                    }
                }
            }

            // 手工拆单保存前打标
            OrderTagUtil.orderTags(orderInfo);
            if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderInfo.getOrderInfo().getOrderStatus()) && order.getCpCPhyWarehouseId() !=null) {
                if (sgOccupiedInventoryService.isO2OOrder(orderInfo.getOrderInfo())){
                    orderInfo.getOrderInfo().setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
                }else{
                    orderInfo.getOrderInfo().setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
                }
            }
            // 落库订单信息
            omsOrderService.saveOrderInfo(orderInfo.getOrderInfo());
            List<String> giftRelationList = orderItemList.stream()
                    .filter(it -> !OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(it.getIsGift())
                            && StringUtils.isNotEmpty(it.getGiftRelation()))
                    .map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
            List<String> giftList = orderItemList.stream()
                    .filter(it -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(it.getIsGift())
                            && StringUtils.isNotEmpty(it.getGiftRelation()))
                    .map(OcBOrderItem::getGiftRelation).collect(Collectors.toList());
            /* 落库订单明细*/
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                String giftRelation = ocBOrderItem.getGiftRelation();
                if(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(ocBOrderItem.getIsGift())){
                    // 若挂靠赠品与主品拆分开，则将挂靠赠品的挂靠关系清空
                    if (StringUtils.isNotEmpty(giftRelation)
                            && (CollectionUtils.isEmpty(giftRelationList) || !giftRelationList.contains(giftRelation))) {
                        ocBOrderItem.setGiftRelation(null);
                    }
                } else {
                    // 若挂靠赠品与主品拆分开，则将主品的挂靠关系清空
                    if (StringUtils.isNotEmpty(giftRelation)
                            && (CollectionUtils.isEmpty(giftList) || !giftList.contains(giftRelation))) {
                        ocBOrderItem.setGiftRelation(null);
                    }
                }
                omsOrderItemService.saveOcBOrderItem(ocBOrderItem, orderId);
            }
            // 正常订单新增调用日志服务
            String message = "订单ID" + orderId + "为手动拆分生成的新订单!原订单ID" + originOrderId;
            // 新增订单
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(), message, "", "", user);
            if (ids == null){
                ids = new ArrayList<>();
            }
            omsOccupyTaskService.addOcBOccupyTask(order, null);
        }
       // toBeConfirmedTaskService.insert(ids);
    }

    /**
     * 推ES
     *
     * @param orderId     订单Id
     * @param orderStatus 订单状态
     */
    public void pushEs(Long orderId, int orderStatus) {

        OcBOrder ocBOrder = omsOrderService.selectOrderInfo(orderId);
        //订单状态
        ocBOrder.setOrderStatus(orderStatus);
        //修改人
        ocBOrder.setModifierename(SystemUserResource.ROOT_USER_NAME);
        //修改时间
        ocBOrder.setModifieddate(new Date());
        //再次更新订单信息
        omsOrderService.updateOrderInfo(ocBOrder);
    }

    /**
     * 构造新订单
     *
     * @param ocBorderDto 原始订单对象
     * @param orderNewId  新订单Id
     * @param suffixInfo  订单补充信息
     * @return 拆分后的订单对象
     */
    public OcBOrder bulidOcbOrder(OcBOrder ocBorderDto, Long orderNewId, String suffixInfo, String type) {

        OcBOrder ocBOrder = new OcBOrder();
        //复制其他属性
        BeanUtils.copyProperties(ocBorderDto, ocBOrder);
        //设置ID
        ocBOrder.setId(orderNewId);
        //订单编号
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        //是否拆分原单
        ocBOrder.setIsSplit(1);
        // 拆单原因
        ocBOrder.setSplitReason(SplitReason.SPLIT_MANUAL);
        //拆分原订单号
        ocBOrder.setSplitOrderId(ocBorderDto.getId());
        //平台单号
        ocBOrder.setSourceCode(ocBorderDto.getSourceCode());
        //设置创建人
        ocBOrder.setOwnername(SystemUserResource.ROOT_USER_NAME);
        //创建时间
        ocBOrder.setCreationdate(new Date());
        //订单补充信息
        ocBOrder.setSuffixInfo(suffixInfo);
        //修改人
        ocBOrder.setModifierename(SystemUserResource.ROOT_USER_NAME);
        //修改时间
        ocBOrder.setModifieddate(new Date());
        //配送费用复制
        ocBOrder.setShipAmt(BigDecimal.ZERO);
        //服务费复制
        ocBOrder.setServiceAmt(BigDecimal.ZERO);
        //系统备注
        ocBOrder.setSysremark("");
        //自定义拆单赋值
        omsOrderSplitReasonUtil.matchReason(ocBOrder);
        return ocBOrder;
    }

    /**
     * 构造新明细
     *
     * @param orderNewId    新单Id
     * @param originId      原始订单Id
     * @param orderItemList 原单明细List
     * @return
     */
    public List<OcBOrderItem> bulidOrderItemList(Long orderNewId, Long
            originId, List<OcBOrderItem> orderItemList) {

        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
        for (OcBOrderItem ocbItemDto : orderItemList) {
            ocBOrderItemList.add(bulidOcBOrderItem(orderNewId, originId, ocbItemDto));
        }
        return ocBOrderItemList;
    }

    /**
     * 构造新订单明细[需要重新计算明细平摊金额和明细成交金额]
     *
     * @param orderNewId      新拆分订单Id
     * @param originId        原始订单Id
     * @param ocBOrderItemDto 原始订单明细对象
     * @return OcBOrderItem 拆分后的订单明细
     */
    public OcBOrderItem bulidOcBOrderItem(Long orderNewId, Long originId, OcBOrderItem ocBOrderItemDto) {

        OcBOrderItem orderItem = new OcBOrderItem();
        BeanUtils.copyProperties(ocBOrderItemDto, orderItem);
        //重新生成Id
        orderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
        //设置订单Id
        orderItem.setOcBOrderId(orderNewId);
        //修改人
        orderItem.setModifierename(SystemUserResource.ROOT_USER_NAME);
        //修改时间
        orderItem.setModifieddate(new Date());
        //原单平摊金额
        BigDecimal originSplitAmt = (ocBOrderItemDto.getOrderSplitAmt() == null ? BigDecimal.ZERO : ocBOrderItemDto.getOrderSplitAmt());
        //原单优惠金额
        BigDecimal originAmtDiscount = (ocBOrderItemDto.getAmtDiscount() == null ? BigDecimal.ZERO : ocBOrderItemDto.getAmtDiscount());
        //原单调整金额
        BigDecimal originAdJustAmt = (ocBOrderItemDto.getAdjustAmt() == null ? BigDecimal.ZERO : ocBOrderItemDto.getAdjustAmt());
        //原单分销金额
        BigDecimal originDistributionPrice = (ocBOrderItemDto.getDistributionPrice() == null ? BigDecimal.ZERO : ocBOrderItemDto.getDistributionPrice());
        //临时存储新的明细平摊金额
        BigDecimal orderSplitAmt;
        //临时存储新的优惠金额
        BigDecimal amtDiscount;
        //临时存储新的调整金额
        BigDecimal adJustAmt;
        //临时存储新的分销金额
        BigDecimal distributionPrice;
        //临时存储新的成交单价
        BigDecimal price = BigDecimal.ZERO;
        //临时存储新的成交金额
        BigDecimal realAmt;
        //原单下相同skuId的总购买数量
        BigDecimal totalAmtAmout = omsOrderItemService.queryItemQtyAmout(originId, ocBOrderItemDto.getPsCSkuId());
        // 原单下相同skuId的总行数
        Integer reuslt = omsOrderItemService.queryItemBySkuIdCount(originId, ocBOrderItemDto.getPsCSkuId());

        //多行时候[单行数量/总数量=计算比例*原订单明细平摊金额]
        orderSplitAmt = ocBOrderItemDto.getQty().divide(totalAmtAmout, 6, RoundingMode.HALF_EVEN).multiply(originSplitAmt).setScale(2, BigDecimal.ROUND_CEILING);
        amtDiscount = ocBOrderItemDto.getQty().divide(totalAmtAmout, 6, RoundingMode.HALF_EVEN).multiply(originAmtDiscount).setScale(2, BigDecimal.ROUND_CEILING);
        adJustAmt = ocBOrderItemDto.getQty().divide(totalAmtAmout, 6, RoundingMode.HALF_EVEN).multiply(originAdJustAmt).setScale(2, BigDecimal.ROUND_CEILING);
        distributionPrice = ocBOrderItemDto.getQty().divide(totalAmtAmout, 6, RoundingMode.HALF_EVEN).multiply(originDistributionPrice).setScale(2, BigDecimal.ROUND_CEILING);
        /* 王帅 2020.4.2 新增判断 防止by zero错误发生*/
        if (ocBOrderItemDto.getQty() != null && ocBOrderItemDto.getQty().compareTo(BigDecimal.ZERO) != 0) {
            price = (ocBOrderItemDto.getPriceList().multiply(ocBOrderItemDto.getQty()).subtract(amtDiscount).add(adJustAmt)).divide(ocBOrderItemDto.getQty(), 2, RoundingMode.HALF_EVEN).setScale(2, BigDecimal.ROUND_CEILING);
        }
        //【成交金额】=吊牌价*数量-优惠金额+调整金额-整单平摊金额
        realAmt = orderItem.getPriceList().multiply(orderItem.getQty()).subtract(amtDiscount).add(adJustAmt).subtract(orderSplitAmt).setScale(2, BigDecimal.ROUND_CEILING);
        orderItem.setPrice(price);
        orderItem.setOrderSplitAmt(orderSplitAmt);
        orderItem.setAdjustAmt(adJustAmt);
        orderItem.setAmtDiscount(amtDiscount);
        orderItem.setDistributionPrice(distributionPrice);
        orderItem.setRealAmt(realAmt);
        //缺货数量置为0
        orderItem.setQtyLost(BigDecimal.ZERO);
        return orderItem;
    }

    /**
     * 计算订单明细相关金额（不包含组合商品，proType != 4）
     *
     * @param originOrderId
     * @param originOrderItem
     * @param newOrderItem
     */
    private void computeOrderItemAmt(Long originOrderId, OcBOrderItem originOrderItem, OcBOrderItem newOrderItem,boolean flag) {
        /* 原单SKU相关金额*/
        // 原单平摊金额
        BigDecimal originSplitAmt = isNullReturnZero(originOrderItem.getOrderSplitAmt());
        // 原单优惠金额
        BigDecimal originAmtDiscount = isNullReturnZero(originOrderItem.getAmtDiscount());
        // 原单数量
        BigDecimal originQty = isNullReturnZero(originOrderItem.getQty());
        // 原单成交金额
        BigDecimal originRealAmt = isNullReturnZero(originOrderItem.getRealAmt());
        // 原单调整金额
        BigDecimal originAdjustAmt = isNullReturnZero(originOrderItem.getAdjustAmt());


        // 原单单个SKU平摊金额 = 原单平摊金额 / 原单数量
        BigDecimal originSingleSplitAmt = originSplitAmt.divide(originQty, 2, BigDecimal.ROUND_DOWN);

        // 原单单个SKU优惠金额 = 原单优惠金额 / 原单数量
        BigDecimal originSingleAmtDiscount = originAmtDiscount.divide(originQty, 2, BigDecimal.ROUND_DOWN);

        // 原单单个SKU调整金额 = 原单调整金额 / 原单数量
        BigDecimal originSingleAdjustAmt = originAdjustAmt.divide(originQty, 2, BigDecimal.ROUND_DOWN);

        // 原单实际成交单价 = 原单成交金额 / 原单数量
        BigDecimal originPriceActual = originRealAmt.divide(originQty, 2, BigDecimal.ROUND_DOWN);

        /* 新单SKU相关金额*/
        // 新单数量
        BigDecimal newQty = isNullReturnZero(newOrderItem.getQty());
        // 新单平摊金额 = 原单单个SKU平摊金额 * 新单数量
        BigDecimal newOrderSplitAmt = originSingleSplitAmt.multiply(newQty).setScale(2, BigDecimal.ROUND_HALF_UP);;

        // 新单优惠金额 = 原单单个SKU优惠金额 * 新单数量
        BigDecimal newAmtDiscount = originSingleAmtDiscount.multiply(newQty).setScale(2, BigDecimal.ROUND_HALF_UP);;

        // 新单成交金额 = 原单成交单价 * 新单数量
//        BigDecimal newRealAmt = originPriceActual.multiply(newQty);
//        log.debug("原订单OrderID{}新单成交金额{} = 原单成交单价{} * 新单数量{}", originOrderId, newRealAmt, originPriceActual, newQty);

        // 新单调整金额 = 原单单个SKU调整金额 * 新单数量
        BigDecimal newAdJustAmt = originSingleAdjustAmt.multiply(newQty).setScale(2, BigDecimal.ROUND_HALF_UP);;

        //需要补全金额的明细
        if(flag){
            //计算出每个金额的误差
            BigDecimal erroOrderSplitAmt =  Optional.ofNullable(originSingleSplitAmt).orElse(BigDecimal.ZERO).multiply(originQty).subtract(originSplitAmt);

            BigDecimal erroAmtDiscount =  Optional.ofNullable(originSingleAmtDiscount).orElse(BigDecimal.ZERO).multiply(originQty).subtract(originAmtDiscount);

            BigDecimal erroAdjustAmt =  Optional.ofNullable(originSingleAdjustAmt).orElse(BigDecimal.ZERO).multiply(originQty).subtract(originAdjustAmt);


//            BigDecimal erroTotPriceActual =  Optional.ofNullable(originPriceActual).orElse(BigDecimal.ZERO).multiply(originQty).subtract(originAdjustAmt);
//            log.debug("原订单OrderID{}误差调整金额{} = 原单单个SKU调整金额{} * 新单数量{}", originOrderId, erroTotPriceActual, originSingleAdjustAmt, originQty);
            //因为除金额的时候是小数向下取，所以要加上
            newOrderSplitAmt = newOrderSplitAmt.subtract(erroOrderSplitAmt);
            newAmtDiscount = newAmtDiscount.subtract(erroAmtDiscount);
            newAdJustAmt = newAdJustAmt.subtract(erroAdjustAmt);
        }
        /* 设定计算后相关金额*/
        newOrderItem.setOrderSplitAmt(newOrderSplitAmt);
        newOrderItem.setAmtDiscount(newAmtDiscount);
        newOrderItem.setAdjustAmt(newAdJustAmt);
        BigDecimal newRealAmt = Optional.ofNullable(newOrderItem.getPrice()).orElse(BigDecimal.ZERO).multiply(newQty) // 平台售价*商品数量
                .subtract(Optional.ofNullable(newAmtDiscount).orElse(BigDecimal.ZERO))  // 减去商品优惠金额
                .subtract(Optional.ofNullable(newOrderSplitAmt).orElse(BigDecimal.ZERO)) //减去平摊金额
                .add(Optional.ofNullable(newAdJustAmt).orElse(BigDecimal.ZERO)) // 加调整金额
                .setScale(4, BigDecimal.ROUND_HALF_UP);
        newOrderItem.setRealAmt(newRealAmt);
    }

    /**
     * 校验订单相关金额与改订单下SKU相关金额 是否一致。
     *
     * @param originOrder
     * @return
     */
    public ValueHolderV14 checkOrderAmt(OcBOrder originOrder) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        List<String> errorMsgList = new ArrayList<>();
        /* 计算订单下订单明细相关金额合计*/
        // 原订单明细
        List<OcBOrderItem> originOrderItemList = orderItemMapper.selectOrderItemListOccupy(originOrder.getId());
        // 获取订单明细下相关金额合计（只算protype == 4 或 protype == 0）
        OrderMoney orderMoney = OrderMoneyCalculationUtils.calculateOrderAmtByOrderItemList(originOrderItemList);
        // 原单平摊金额
        BigDecimal splitAmtTotal = orderMoney.getOrderDiscountAmt();
        // 原单优惠金额
        BigDecimal amtDiscountTotal = orderMoney.getProductDiscountAmt();
        // 原单调整金额
        BigDecimal adjustAmtTotal = orderMoney.getAdjustAmt();
        // 原单实际成交价格
        BigDecimal realAmtTotal = orderMoney.getOrderAmt();

        // 订单的【商品优惠金额】 != 订单下【SKU优惠金额】合计
        if (!BigDecimalUtil.equals(amtDiscountTotal, originOrder.getProductDiscountAmt())) {
            String errorMsg = "订单OrderID" + originOrder.getId() + "->【商品优惠金额" + originOrder.getProductDiscountAmt() + "】不等于【SKU优惠金额合计" + amtDiscountTotal + "】";
            errorMsgList.add(errorMsg);
        }
        // 订单的【订单优惠金额】 != 订单下【SKU优惠分摊】合计
        if (!BigDecimalUtil.equals(splitAmtTotal, originOrder.getOrderDiscountAmt())) {
            String errorMsg = "订单OrderID" + originOrder.getId() + "->【订单优惠金额" + originOrder.getOrderDiscountAmt() + "】不等于【SKU优惠分摊合计" + splitAmtTotal + "】";
            errorMsgList.add(errorMsg);
        }
        // 订单的【调整金额】 != 订单下【SKU调整金额】合计
        if (!BigDecimalUtil.equals(adjustAmtTotal, originOrder.getAdjustAmt())) {
            String errorMsg = "订单OrderID" + originOrder.getId() + "->【订单调整金额" + originOrder.getAdjustAmt() + "】不等于【SKU调整金额合计" + adjustAmtTotal + "】";
            errorMsgList.add(errorMsg);
        }
        // 订单的【订单总额】 != 订单下【SKU实际成交价格】合计
        if (!BigDecimalUtil.equals(realAmtTotal, originOrder.getOrderAmt())) {
            String errorMsg = "订单OrderID" + originOrder.getId() + "->【订单总额" + originOrder.getOrderAmt() + "】不等于【SKU实际成交价格合计" + realAmtTotal + "】";
            errorMsgList.add(errorMsg);
        }

        if (errorMsgList.size() == 0) {
            valueHolderV14.setCode(0);
        } else {
            valueHolderV14.setCode(-1);
            valueHolderV14.setData(errorMsgList);
            valueHolderV14.setMessage(JSON.toJSONString(errorMsgList));
        }
        return valueHolderV14;
    }

    /**
     * 是否存在组合商品
     *
     * @param orderItemList
     * @return true: 存在 false：不存在
     */
    public boolean isExistenceGroupOrder(List<OcBOrderItem> orderItemList) {
        for (OcBOrderItem orderItem : orderItemList) {
            if (orderItem.getProType() == SkuType.NO_SPLIT_COMBINE || orderItem.getProType() == SkuType.COMBINE_PRODUCT) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取组合商品明细head
     *
     * @param orderItemList
     */
    public OcBOrderItem getGroupOrderItem(List<OcBOrderItem> orderItemList) {
        for (OcBOrderItem orderItem : orderItemList) {
            if (orderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                return orderItem;
            }
        }
        return null;
    }

    /**
     * 用新的组合商品head信息，重新调用拆分组合商品方法，成功后替换原有组合商品下挂SKU的信息
     * flag -> 1:成交金额  2:调整金额 3:商品优惠 4:订单平摊优惠 5:原价
     */
    public void computeGroupOrderItem(OcBOrder order, OcBOrderItem groupOrderItem, List<OcBOrderItem> orderItemList, User user, int flag) {
        /* 调用方法【 omsConstituteSplitService.encapsulationParameter】，会返回分摊好组合下挂的sku金额*/
        List<OcBOrderItem> groupOrderItemList = new ArrayList<>();
        groupOrderItemList.add(groupOrderItem);
        List<OcBOrderItem> groupOfSkuList = omsConstituteSplitService.encapsulationParameter(groupOrderItemList,
                order, user, 0);
        if (CollectionUtils.isEmpty(groupOfSkuList)) {
            log.error("调用omsConstituteSplitService.encapsulationParameter异常！！！！");
        }
        for (OcBOrderItem oldOrderItem : orderItemList) {
            for (OcBOrderItem newOrderItem : groupOfSkuList) {
                if (oldOrderItem.getPsCSkuId().equals(newOrderItem.getPsCSkuId())) {
                    if (flag == 1) {
                        oldOrderItem.setRealAmt(newOrderItem.getRealAmt());
                        oldOrderItem.setPriceActual(newOrderItem.getPriceActual());
                    } else if (flag == 2) {
                        oldOrderItem.setAdjustAmt(newOrderItem.getAdjustAmt());
                    } else if (flag == 3) {
                        oldOrderItem.setAmtDiscount(newOrderItem.getAmtDiscount());
                    } else if (flag == 4) {
                        oldOrderItem.setOrderSplitAmt(newOrderItem.getOrderSplitAmt());
                    } else if (flag == 5) {
                        oldOrderItem.setPrice(newOrderItem.getPrice());
                    }
                }
            }
        }
    }

    /**
     * 计算订单明细成交金额和成交单价
     */
    public void computeOrderItemRealAmtAndPriceActual(Long orderId, BigDecimal differenceValue, OcBOrderItem orderItem) {
        orderItem.setRealAmt(orderItem.getRealAmt().add(differenceValue));
        orderItem.setPriceActual(orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
    }

    /**
     * 成交金额总金额
     *
     * @param orderItemList
     * @return
     */
    private BigDecimal realAmtTotal(List<OcBOrderItem> orderItemList) {
        BigDecimal realAmtToal = BigDecimal.ZERO;
        for (OcBOrderItem oi : orderItemList) {
            if (oi.getProType() != SkuType.NO_SPLIT_COMBINE) {
                realAmtToal = realAmtToal.add(oi.getRealAmt());
            }
        }
        return realAmtToal;
    }

    /**
     * 拆分后订单明额的调节器（拆分前原单订单相关金额和拆分后订单相关金额进行比较，算出差值后补缺）
     *
     * @param originOrder          原始订单对象
     * @param newOrderRelationList 新的订单集合对象
     */
    public void orderAmtRegulator(OcBOrder originOrder, List<OcBOrderRelation> newOrderRelationList) {
        // 总成交金额从大到小排序
        Collections.sort(newOrderRelationList,
                (o1, o2) -> realAmtTotal(o2.getOrderItemList()).compareTo(realAmtTotal(o1.getOrderItemList())));
        /* 原单拆分前相关金额*/
        BigDecimal originOrderDiscountAmt = isNullReturnZero(originOrder.getOrderDiscountAmt());
        BigDecimal originAdjustAmt = isNullReturnZero(originOrder.getAdjustAmt());
        BigDecimal originProductDiscountAmt = isNullReturnZero(originOrder.getProductDiscountAmt());
        // 原订单总额 = 原单订单总额 - 原订单运费 - 原订单服务费,  因为订单明细中没有存储运费，服务的地方，所以这里不做计算
        BigDecimal originOrderAmt = isNullReturnZero(originOrder.getOrderAmt()).subtract(originOrder.getShipAmt()).subtract(originOrder.getServiceAmt());
        /* 拆分后所有订单相关金额合计*/
        // 拆分后订单下 优惠平摊金额合计
        BigDecimal splitAllSplitAmtTotal = BigDecimal.ZERO;
        // 拆分后订单下 优惠金额合计
        BigDecimal splitAllAmtDiscountTotal = BigDecimal.ZERO;
        // 拆分后订单下 调整金额合计
        BigDecimal splitAllAdjustAmtTotal = BigDecimal.ZERO;
        // 拆分后订单下 实际成交价格合计
        BigDecimal splitAllRealAmtTotal = BigDecimal.ZERO;
        // 拆分后订单下 商品金额合计
        BigDecimal productAmtTotal = BigDecimal.ZERO;

        /** 1.调整拆分后订单明细金额*/
        for (OcBOrderRelation orderRelation : newOrderRelationList) {
            List<OcBOrderItem> newOrderItemList = orderRelation.getOrderItemList();
            // 获取订单明细下相关金额合计（只算protype == 4 或 protype == 0）
            OrderMoney orderMoney = OrderMoneyCalculationUtils.calculateOrderAmtByOrderItemList(newOrderItemList);
            splitAllSplitAmtTotal = splitAllSplitAmtTotal.add(orderMoney.getOrderDiscountAmt());
            splitAllAmtDiscountTotal = splitAllAmtDiscountTotal.add(orderMoney.getProductDiscountAmt());
            splitAllAdjustAmtTotal = splitAllAdjustAmtTotal.add(orderMoney.getAdjustAmt());
            splitAllRealAmtTotal = splitAllRealAmtTotal.add(orderMoney.getOrderAmt());
            productAmtTotal = productAmtTotal.add(orderMoney.getProductAmt());
        }

        // 原订单总额 不等于 所有拆分后SKU实际成交价格合计
        if (BigDecimalUtil.isNotZero(splitAllRealAmtTotal) && !BigDecimalUtil.equals(splitAllRealAmtTotal, originOrderAmt)) {

            OcBOrderRelation orderRelation = newOrderRelationList.get(0);
            OcBOrder order = orderRelation.getOrderInfo();
            List<OcBOrderItem> orderItemList = orderRelation.getOrderItemList();
            Long orderId = order.getId();

            // 差值 = 订单总额 - 所有拆分后SKU实际成交价格合计
            BigDecimal differenceValue = originOrderAmt.subtract(splitAllRealAmtTotal);

            for (OcBOrderItem orderItem : orderItemList) {
                if (orderItem.getProType() == SkuType.NORMAL_PRODUCT) {
                    // 计算订单明细成交金额和成交单价
                    computeOrderItemRealAmtAndPriceActual(orderId, differenceValue, orderItem);
                    break;
                } else if (orderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                    // 计算订单明细成交金额和成交单价
                    computeOrderItemRealAmtAndPriceActual(orderId, differenceValue, orderItem);

//                    // 用新的组合商品head信息，重新调用拆分组合商品方法，成功后替换原有组合商品下挂SKU的信息
//                    computeGroupOrderItem(order, orderItem, orderItemList, user, 1);
                    // 根据组合商品头信息，算出平摊到该组合商品下挂的SKU的相关金额
                    groupProductSplitService.splitGroupProduct(orderItem, orderItemList);
                    break;
                }
            }
        }

        // 原订单优惠金额 不等于 所有拆分后SKU优惠分摊合计
        if (BigDecimalUtil.isNotZero(splitAllSplitAmtTotal) && !BigDecimalUtil.equals(splitAllSplitAmtTotal, originOrderDiscountAmt)) {
            OcBOrderRelation orderRelation = newOrderRelationList.get(0);
            OcBOrder order = orderRelation.getOrderInfo();
            List<OcBOrderItem> orderItemList = orderRelation.getOrderItemList();
            Long orderId = order.getId();

            // 差值 = 订单优惠金额 - 所有拆分后SKU优惠分摊合计
            BigDecimal differenceValue = originOrderDiscountAmt.subtract(splitAllSplitAmtTotal);

            for (OcBOrderItem orderItem : orderItemList) {
                if (orderItem.getProType() == SkuType.NORMAL_PRODUCT) {
                    // 计算订单明细订单平摊优惠
                    orderItem.setOrderSplitAmt(orderItem.getOrderSplitAmt().add(differenceValue));
                    break;
                } else if (orderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                    // 计算订单明细订单平摊优惠
                    orderItem.setOrderSplitAmt(orderItem.getOrderSplitAmt().add(differenceValue));
//                    // 用新的组合商品head信息，重新调用拆分组合商品方法，成功后替换原有组合商品下挂SKU的信息
//                    computeGroupOrderItem(order, orderItem, orderItemList, user, 4);
                    // 根据组合商品头信息，算出平摊到该组合商品下挂的SKU的相关金额
                    groupProductSplitService.splitGroupProduct(orderItem, orderItemList);
                    break;
                }
            }
        }

        // 原订单商品优惠金额 不等于 所有拆分后SKU优惠金额合计
        if (BigDecimalUtil.isNotZero(splitAllAmtDiscountTotal) && !BigDecimalUtil.equals(splitAllAmtDiscountTotal, originProductDiscountAmt)) {
            // 差值 = 订单商品优惠金额- 所有拆分后SKU优惠金额合计
            BigDecimal differenceValue = originProductDiscountAmt.subtract(splitAllAmtDiscountTotal);

            OcBOrderRelation orderRelation = newOrderRelationList.get(0);
            OcBOrder order = orderRelation.getOrderInfo();
            List<OcBOrderItem> orderItemList = orderRelation.getOrderItemList();
            Long orderId = order.getId();

            for (OcBOrderItem orderItem : orderItemList) {
                if (orderItem.getProType() == SkuType.NORMAL_PRODUCT) {
                    // 计算订单明细商品优惠金额
                    orderItem.setAmtDiscount(orderItem.getAmtDiscount().add(differenceValue));
                    break;
                } else if (orderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                    // 计算订单明细商品优惠金额
                    orderItem.setAmtDiscount(orderItem.getAmtDiscount().add(differenceValue));
//                    // 用新的组合商品head信息，从新调用拆分组合商品方法，成功后替换原有组合商品下挂SKU的信息
//                    computeGroupOrderItem(order, orderItem, orderItemList, user, 3);
                    // 根据组合商品头信息，算出平摊到该组合商品下挂的SKU的相关金额
                    groupProductSplitService.splitGroupProduct(orderItem, orderItemList);
                    break;
                }
            }
        }

        // 原订单调整金额 不等于 所有拆分后SKU调整金额合计
        if (BigDecimalUtil.isNotZero(splitAllAdjustAmtTotal) && !BigDecimalUtil.equals(splitAllAdjustAmtTotal, originAdjustAmt)) {
            // 差值 = 订单调整金额 - 所有拆分后SKU调整金额合计
            BigDecimal differenceValue = originAdjustAmt.subtract(splitAllAdjustAmtTotal);

            OcBOrderRelation orderRelation = newOrderRelationList.get(0);
            OcBOrder order = orderRelation.getOrderInfo();
            List<OcBOrderItem> orderItemList = orderRelation.getOrderItemList();
            Long orderId = order.getId();

            for (OcBOrderItem orderItem : orderItemList) {
                if (orderItem.getProType() == SkuType.NORMAL_PRODUCT) {
                    // 计算订单明细商品优惠金额
                    orderItem.setAdjustAmt(orderItem.getAdjustAmt().add(differenceValue));
                    if (log.isDebugEnabled()) {
                        log.debug("订单OrderId{} 订单明细ID{} 计算后订单调整金额 {}", orderId, orderItem.getId(),
                                orderItem.getAdjustAmt());
                    }
                    break;
                } else if (orderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                    // 计算订单明细商品优惠金额
                    orderItem.setAdjustAmt(orderItem.getAdjustAmt().add(differenceValue));
//                    // 用新的组合商品head信息，重新调用拆分组合商品方法，成功后替换原有组合商品下挂SKU的信息
//                    computeGroupOrderItem(order, orderItem, orderItemList, user, 2);
                    // 根据组合商品头信息，算出平摊到该组合商品下挂的SKU的相关金额
                    groupProductSplitService.splitGroupProduct(orderItem, orderItemList);
                    break;
                }
            }
        }
    }

    /**
     * 调用拆单后根据聚合仓、实体仓重新占用库存
     *
     * @param origOrderId
     * @param origOrder
     * @param orderRelationList
     * @param user
     * @return
     */
    public ValueHolderV14 callAddAndVoidStock(Long origOrderId, OcBOrder origOrder,boolean isAppoint,
                                              List<OcBOrderRelation> orderRelationList, User user) {
        ValueHolderV14 returnResult = new ValueHolderV14();
        // 作废订单原单以及批量新增逻辑发货单
        List<OcBOrder> ocbList = new ArrayList<>();
        ocbList.add(origOrder);
        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(origOrder.getOrderStatus())) {
            List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(origOrder.getId());
            SgOmsShareOutRequest request = cancelOrderMergeService.buildSgOmsShareOutRequest(origOrder, ocBOrderItems, user);
            ValueHolderV14 vh = sgRpcService.voidSgOmsShareOut(request, origOrder, ocBOrderItems);
            AssertUtil.assertException(!vh.isOK(),vh.getMessage());
        }
        OcBOrder order = new OcBOrder();
        order.setId(origOrder.getId());
        order.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
        ocBOrderMapper.updateById(order);
        if (CollectionUtils.isNotEmpty(ocbList)) {
            for (OcBOrder ocBOrder : ocbList) {
                //订单操作日志
                try {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                            ocBOrder.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(),
                            "手工拆单,作废原单", "", "", user
                    );
                } catch (Exception e) {
                    log.error(LogUtil.format("作废原单调用日志服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                }
            }
        }
//        if (result.isOK()) {
//            for (OcBOrderRelation relation : orderRelationList) {
//                OcBOrder ocBOrder = new OcBOrder();
//                if(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(relation.getOrderInfo().getOrderStatus()) || !isAppoint){
//                    //缺货重新寻源
//                    ocBOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
//                }else {
//                    //待审核直接占单成功待审核
//                    ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
//                    //占用库存成功后，记录日志，并更新拆分后的订单状态和占单状态
//                    omsOrderLogService.addUserOrderLog(relation.getOrderInfo().getId(), relation.getOrderInfo().getBillNo(),
//                            OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), "占用库存成功!", "", "", user);
//                }
//
//                ocBOrder.setId(relation.getOrderId());
//                BaseModelUtil.makeBaseModifyField(ocBOrder,user);
//                ocBOrder.setModifierename(user.getEname());
//                omsOrderService.updateOrderInfo(ocBOrder);
//
//                returnResult.setCode(result.getCode());
//                returnResult.setMessage(result.getMessage());
//
//            }
//        } else {
//            returnResult.setCode(-1);
//            returnResult.setMessage(result.getMessage());
//        }
        return returnResult;
    }

    /**
     * 排除组合商品明细中protype = 4的明细
     *
     * @param orderRelationList
     */
    private List<OcBOrderRelation> removeGroupOrderItemForProType4(List<OcBOrderRelation> orderRelationList) {
        List<OcBOrderRelation> resultList = new ArrayList<>();
        for (OcBOrderRelation orderRelation : orderRelationList) {
            OcBOrderRelation newOrderRelation = new OcBOrderRelation();
            /* 复制newOcBOrder*/
            OcBOrder newOcBOrder = new OcBOrder();
            BeanUtils.copyProperties(orderRelation.getOrderInfo(), newOcBOrder);

            /* 复制newOrderItemList*/
            List<OcBOrderItem> newOrderItemList = new ArrayList<>();
            List<OcBOrderItem> orderItemList = orderRelation.getOrderItemList();
            for (OcBOrderItem orderItem : orderItemList) {
                if (orderItem.getProType() != SkuType.NO_SPLIT_COMBINE) {
                    OcBOrderItem newOrderItem = new OcBOrderItem();
                    BeanUtils.copyProperties(orderItem, newOrderItem);
                    newOrderItemList.add(newOrderItem);
                }
            }

            newOrderRelation.setShareStoreId(orderRelation.getShareStoreId());
            newOrderRelation.setShareStoreEcode(orderRelation.getShareStoreEcode());
            newOrderRelation.setOrderInfo(newOcBOrder);
            newOrderRelation.setOrderItemList(newOrderItemList);
            resultList.add(newOrderRelation);
        }
        return resultList;
    }

    public class SplitOrder {
        private Long origOrderId;
        private List<SplitOrderResult> splitOrderItemList;

        public Long getOrigOrderId() {
            return origOrderId;
        }

        public void setOrigOrderId(Long origOrderId) {
            this.origOrderId = origOrderId;
        }

        public List<SplitOrderResult> getSplitOrderItemList() {
            return splitOrderItemList;
        }

        public void setSplitOrderItemList(List<SplitOrderResult> splitOrderItemList) {
            this.splitOrderItemList = splitOrderItemList;
        }
    }

}