package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyDetailEntity;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyEntity;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyRelation;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;

import java.util.List;
import java.util.Locale;

public interface StCShortStockNoSplitStrategyService extends IService<StCShortStockNoSplitStrategyEntity> {

    /**
     * 缺货不拆策略编辑
     * @param user
     * @param shortStockNoSplitStrategyRelation
     * @return
     */
    ValueHolder updateShortStockNoSplitStrategy(User user, StCShortStockNoSplitStrategyRelation shortStockNoSplitStrategyRelation);

    /**
     * 缺货不拆策略新增
     * @param user
     * @param shortStockNoSplitStrategyRelation
     * @return
     */
    ValueHolder addShortStockNoSplitStrategy(User user, StCShortStockNoSplitStrategyRelation shortStockNoSplitStrategyRelation);

    String getStrategyNo(StCShortStockNoSplitStrategyEntity strategyEntity, Locale locale);

    /**
     * 校验策略明细
     * @param detail
     * @return
     */
    Boolean checkStrategyDetail(List<StCShortStockNoSplitStrategyDetailEntity> detail, StCShortStockNoSplitStrategyEntity mainStrategy);

    /**
     * 编辑状态
     * @param user
     * @param ids
     * @param status
     * @return
     */
    ValueHolder editShortStockNoSplitStrategyStatus(User user, List<Long> ids, String status);

    /**
     * 编辑明细状态
     * @param user
     * @param ids
     * @param status
     * @return
     */
    ValueHolder editShortStockNoSplitStrategyDetailStatus(User user, List<Long> ids, String status);

    /**
     * 获取记录id
     * @param session
     * @return
     */
    List<Long> getObjIds(QuerySession session);
}

