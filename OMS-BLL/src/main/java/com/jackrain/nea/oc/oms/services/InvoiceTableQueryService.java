package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.es.ES4InvoiceNotice;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeProMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.Page;
import com.jackrain.nea.oc.oms.model.result.*;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNotice;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticeItem;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticeLog;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticePro;
import com.jackrain.nea.oc.oms.nums.MaterieltypeEnum;
import com.jackrain.nea.oc.oms.nums.OcInvoiceLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.OcInvoiceStatusEnum;
import com.jackrain.nea.oc.oms.nums.OcInvoiceTypeEnum;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.psext.api.utils.JsonUtils;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AmountCalcUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: huang.zaizai
 * @since: 2019/7/24
 * create at : 2019/7/24 09:00
 */
@Slf4j
@Component
public class InvoiceTableQueryService {
    @Resource
    private CpRpcService cpRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBInvoiceNoticeMapper mapper;
    @Autowired
    private OcBInvoiceNoticeItemMapper itemMapper;
    @Autowired
    private OcBInvoiceNoticeProMapper proMapper;
    @Autowired
    private OcBInvoiceNoticeLogMapper logMapper;

    /**
     * 查询开票商品明细
     *
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryInvoiceItem(JSONObject obj) {
        ValueHolderV14<List<OcBInvoiceNoticeItemResult>> vh = new ValueHolderV14();

        if (null == obj || !obj.containsKey("OC_B_INVOICE_NOTICE_ITEM")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        try {
            List<OcBInvoiceNoticePro> invoiceNoticeProList = JsonUtils.jsonToList(
                    OcBInvoiceNoticePro.class, obj.get("OC_B_INVOICE_NOTICE_ITEM").toString());
            List<Integer> proIdList = new ArrayList<>();
            for (OcBInvoiceNoticePro pro : invoiceNoticeProList) {
                proIdList.add(pro.getPsCProId().intValue());
            }
            List<PsCPro> psCProList = psRpcService.queryProByIds(proIdList);
            Map<Long, PsCPro> proMap = psCProList.stream().collect(Collectors.toMap(PsCPro::getId, Function.identity()));
            //汇总明细信息
            Map<String, OcBInvoiceNoticeItemResult> itemMap = getInvoiceNoticeItemMap(invoiceNoticeProList, proMap);

            List<OcBInvoiceNoticeItemResult> itemList = new ArrayList<OcBInvoiceNoticeItemResult>(itemMap.values());
            vh.setData(itemList);
            vh.setCode(ResultCode.SUCCESS);
            return vh;
        } catch (Exception e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
            return vh;
        }
    }

    public Map<String, OcBInvoiceNoticeItemResult> getInvoiceNoticeItemMap(List<OcBInvoiceNoticePro> invoiceNoticeProList,
                                                                           Map<Long, PsCPro> proMap) {
        Map<String, OcBInvoiceNoticeItemResult> itemMap = new HashMap<>();
        /*for (OcBInvoiceNoticePro invoicePro : invoiceNoticeProList) {
            if (proMap.containsKey(invoicePro.getPsCProId())) {
                PsCPro psCPro = proMap.get(invoicePro.getPsCProId());
                String invoiceName = "";
                if (MaterieltypeEnum.SHOES_ADULT.getCode().equals(psCPro.getMaterieltype())) {
                    invoiceName = psCPro.getEname().replaceAll("男", "")
                            .replaceAll("女", "");
                } else if (MaterieltypeEnum.SHOES_CHILD.getCode().equals(psCPro.getMaterieltype())) {
                    invoiceName = "童鞋";
                } else if (MaterieltypeEnum.CLOTHES_ADULT.getCode().equals(psCPro.getMaterieltype())
                        || MaterieltypeEnum.CLOTHES_CHILD.getCode().equals(psCPro.getMaterieltype())) {
                    if (psCPro.getSex() != null) {
                        PsCProdimItem prodimItem = psRpcService.selectPsCProdimItem(psCPro.getSex());
                        if (prodimItem != null) {
                            invoiceName = prodimItem.getEname();
                        }
                    }
                    invoiceName = invoiceName + psCPro.getEname();
                } else {
                    invoiceName = psCPro.getEname();
                }
                //合计
                OcBInvoiceNoticeItemResult invoiceNoticeItem = new OcBInvoiceNoticeItemResult();
                if (itemMap.containsKey(invoiceName)) {
                    invoiceNoticeItem = itemMap.get(invoiceName);
                } else {
                    invoiceNoticeItem.setId(-1L);
                    invoiceNoticeItem.setPsCProId(psCPro.getId());
                    invoiceNoticeItem.setPsCProEcode(psCPro.getEcode());
                    invoiceNoticeItem.setPsCProEname(invoiceName);
                    invoiceNoticeItem.setUnit(psCPro.getBasicunit());
                    if (invoiceNoticeItem.getUnit() != null) {
                        PsCProdimItem prodimItem = psRpcService.selectPsCProdimItem(invoiceNoticeItem.getUnit());
                        if (prodimItem != null) {
                            invoiceNoticeItem.setUnitName(prodimItem.getEname());
                        }
                    }
                }
                invoiceNoticeItem.setQty(AmountCalcUtils.subtractBigDecimal(
                        AmountCalcUtils.addBigDecimal(invoiceNoticeItem.getQty(), invoicePro.getQty()),
                        invoicePro.getQtyRefund()));
                invoiceNoticeItem.setAmtTaxable(AmountCalcUtils.subtractBigDecimal(
                        AmountCalcUtils.addBigDecimal(invoiceNoticeItem.getAmtTaxable(), invoicePro.getAmt()),
                        invoicePro.getAmtRefund()));

                itemMap.put(invoiceName, invoiceNoticeItem);
            }
        }*/
        return itemMap;
    }

    /**
     * 查询开票通知信息
     *
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryInvoiceNoticeInfo(JSONObject obj) {
        ValueHolderV14<InvoiceNoticeResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("objid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        Long id = obj.getLong("objid");
        //查询数据
        InvoiceNoticeResult result = new InvoiceNoticeResult();
        OcBInvoiceNotice invoiceNotice = mapper.selectById(id);
        if (invoiceNotice != null) {
            List<OcBInvoiceNoticeItem> itemList = itemMapper.listByMainid(invoiceNotice.getId());
            List<OcBInvoiceNoticePro> proList = proMapper.listByMainid(invoiceNotice.getId());
            List<OcBInvoiceNoticeLog> logList = logMapper.listByMainid(invoiceNotice.getId());

            //补足冗余字段
            OcBInvoiceNoticeResult invoiceNoticeResult = new OcBInvoiceNoticeResult();
            BeanUtils.copyProperties(invoiceNotice, invoiceNoticeResult);

            List<OcBInvoiceNoticeItemResult> itemResultList = new ArrayList<>();
            for (OcBInvoiceNoticeItem item : itemList) {
                OcBInvoiceNoticeItemResult itemResult = new OcBInvoiceNoticeItemResult();
                BeanUtils.copyProperties(item, itemResult);
                itemResultList.add(itemResult);
            }

            List<OcBInvoiceNoticeLogResult> logResultList = new ArrayList<>();
            for (OcBInvoiceNoticeLog log : logList) {
                OcBInvoiceNoticeLogResult logResult = new OcBInvoiceNoticeLogResult();
                BeanUtils.copyProperties(log, logResult);
                logResult.setLogTypeName(OcInvoiceLogTypeEnum.enumToStringBykey(logResult.getLogType()));
                logResultList.add(logResult);
            }
            result.setInvoiceNotice(invoiceNoticeResult);
            result.setInvoiceNoticeItemList(itemResultList);
            result.setInvoiceNoticeProList(proList);
            result.setInvoiceNoticeLogList(logResultList);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("数据不存在！");
            return vh;
        }
        vh.setData(result);
        return vh;
    }

    /**
     * 查询开票公司信息
     *
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 queryByShop(JSONObject obj) {
        ValueHolderV14<CpShop> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        if (null == obj || !obj.containsKey("CP_C_SHOP_ID")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }

        Long shopId = obj.getLong("CP_C_SHOP_ID");
        CpShop cpShop = cpRpcService.selectShopById(shopId);
        if (cpShop != null) {
            vh.setData(cpShop);
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("店铺不存在");
            return vh;
        }
        return vh;
    }

    /**
     * 查询开票通知信息
     *
     * @param obj
     * @return ValueHolderV14
     */
    public ValueHolderV14 selectInvoiceNoticeList(JSONObject obj) {
        ValueHolderV14<OcBInvoiceNoticeQueryResult> vh = new ValueHolderV14<>();
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("查询成功");
        if (null == obj) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空");
            return vh;
        }
        JSONObject whereKeys = getEsKeysJo(obj, "whereInfo");
        JSONObject filterKeys = getDateEsKeysJo(obj, "filterInfo");
        //ES查询 返回ids
        OcBInvoiceNoticeQueryResult result = this.splitPageHandler(obj);
        int pageStartIndex = this.getPageStartIndex(result);
        JSONObject search = ES4InvoiceNotice.findJSONObjectByEs(result, whereKeys, filterKeys, pageStartIndex);
        List<Long> ids = splitOrderIds(search, result);
        if (CollectionUtils.isEmpty(ids)) {
            return vh;
        }
        List<OcBInvoiceNotice> invoiceNoticelist = mapper.selectBatchIds(ids);
        List<OcBInvoiceNoticeResult> invoiceNoticeResultlist = new ArrayList<>();
        for (OcBInvoiceNotice invoiceNotice : invoiceNoticelist) {
            OcBInvoiceNoticeResult invoiceResult = new OcBInvoiceNoticeResult();
            BeanUtils.copyProperties(invoiceNotice, invoiceResult);
            invoiceResult.setInvoiceTypeName(OcInvoiceTypeEnum.enumToStringBykey(invoiceResult.getInvoiceType()));
            invoiceResult.setEstatusName(OcInvoiceStatusEnum.enumToStringByVal(invoiceResult.getEstatus()));
            invoiceNoticeResultlist.add(invoiceResult);
        }
        if (CollectionUtils.isNotEmpty(invoiceNoticeResultlist)) {
            invoiceNoticeResultlist = invoiceNoticeResultlist.stream()
                    .sorted(Comparator.comparing(OcBInvoiceNoticeResult::getCreationdate).reversed()).collect(Collectors.toList());
        }
        result.setInvoiceNoticelist(invoiceNoticeResultlist);
        vh.setData(result);
        return vh;
    }

    /**
     * 查询条件
     *
     * @param obj   ES信息
     * @param eskey eskey
     */
    public JSONObject getEsKeysJo(JSONObject obj, String eskey) {
        JSONObject keysJo = new JSONObject();
        JSONObject infoJo = obj.getJSONObject(eskey);
        if (infoJo != null) {
            for (String key : infoJo.keySet()) {
                if (!infoJo.get(key).toString().contains("bSelect-all")) {
                    String strInfo = infoJo.get(key).toString();
                    if (strInfo.contains(",") && !strInfo.contains("[")) {
                        List<String> strList = Lists.newArrayList();
                        for (String str : strInfo.split(",")) {
                            strList.add("*" + str + "*");
                        }
                        keysJo.put(key, JsonUtils.toJsonString(strList));
                    } else if (StringUtils.isNotEmpty(strInfo)) {
                        String sourceCode = "*" + strInfo + "*";
                        keysJo.put(key, "SOURCE_CODE".equals(key) ? sourceCode : strInfo);
                    }
                }
            }
        }
        return keysJo;
    }

    /**
     * 查询日期条件
     *
     * @param obj   ES信息
     * @param eskey eskey
     */
    public JSONObject getDateEsKeysJo(JSONObject obj, String eskey) {
        JSONObject keysJo = new JSONObject();
        JSONObject infoJo = obj.getJSONObject(eskey);
        if (infoJo != null) {
            for (String key : infoJo.keySet()) {
                if (!StringUtils.isBlank(infoJo.getJSONArray(key).getString(0)) &&
                        !StringUtils.isBlank(infoJo.getJSONArray(key).getString(1))) {
                    Long startTime = infoJo.getJSONArray(key).getDate(0).getTime();
                    Long endTime = infoJo.getJSONArray(key).getDate(1).getTime();
                    keysJo.put(key, startTime + "~" + endTime);
                }
            }
        }
        return keysJo;
    }

    /**
     * 分页处理
     *
     * @param o 分页信息
     */
    private OcBInvoiceNoticeQueryResult splitPageHandler(JSONObject o) {
        OcBInvoiceNoticeQueryResult result = new OcBInvoiceNoticeQueryResult();
        Page page = new Page();
        if (o != null) {
            Integer size = o.getInteger("pageSize");
            Integer num = o.getInteger("pageNum");
            if (num != null && num > 0) {
                page.setPageNum(num);
            }
            if (size != null && size > 0) {
                page.setPageSize(size);
            }
            result.setPage(page);
        } else {
            page.setPageNum(OcBOrderConst.PAGE_NUM);
            page.setPageSize(OcBOrderConst.PAGE_SIZE);
            result.setPage(page);
        }
        return result;
    }

    /**
     * ES 分页起始下标
     *
     * @param result result
     * @return int
     */
    private int getPageStartIndex(OcBInvoiceNoticeQueryResult result) {
        Page page = result.getPage();
        int startIndex = OcBOrderConst.ORDER_STATUS_ALL;
        if (page.getPageNum() != null && page.getPageNum() > OcBOrderConst.ORDER_STATUS_ALL) {
            startIndex = (page.getPageNum() - 1) * (page.getPageSize());
        }
        return startIndex;
    }

    /**
     * es提取主表id,并计算起始下标
     *
     * @param esJo   jsonObject
     * @param result result
     * @return list
     */
    private List<Long> splitOrderIds(JSONObject esJo, OcBInvoiceNoticeQueryResult result) {
        List<Long> list = new ArrayList<>();
        if (esJo == null) {
            return list;
        }
        Long totalCount = esJo.getLong("total");
        Long totalPage = 0L;
        if (totalCount > OcBOrderConst.ORDER_STATUS_ALL) {
            long l = totalCount % (result.getPage().getPageSize());
            if (l == OcBOrderConst.ORDER_STATUS_ALL) {
                totalPage = totalCount / (result.getPage().getPageSize());
            } else {
                totalPage = (totalCount / (result.getPage().getPageSize())) + 1;
            }
        }
        result.getPage().setTotalNum(totalPage.intValue());
        result.getPage().setTotalSize(totalCount);

        JSONArray ary = esJo.getJSONArray("data");
        if (ary == null) {
            return list;
        }
        for (int i = 0; i < ary.size(); i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            Long id = o.getLong("ID");
            if (id == null) {
                continue;
            }
            list.add(id);
        }
        return list;
    }
}
