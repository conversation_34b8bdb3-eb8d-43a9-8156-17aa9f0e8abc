package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.request.SaveInvoiceRequest;
import com.jackrain.nea.oc.oms.model.result.QueryOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单开票
 *
 * @author: xiWen.z
 * create at: 2019/7/23 0023
 */
@Component
@Slf4j
public class OcBorderInvoiceService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    /**
     * 订单开票新增.校验
     *
     * @param saveInvoiceRequest SaveInvoiceRequest
     * @param usr                User
     * @return vh14
     */
    public ValueHolderV14 checkAddOrderInvoicing(SaveInvoiceRequest saveInvoiceRequest, User usr) {

        // 参数校验
        Long[] ids = saveInvoiceRequest.getIds();
        String sql = sqlHandler(ids);
        if (StringUtils.isBlank(sql)) {
            return valueHolderResult("请确认,存在要开票的订单", false, usr);
        }

        // 查询订单
        List<QueryOrderResult> odrList = ocBOrderMapper.orderListQueryByIds(sql, OcBOrderConst.IS_ACTIVE_YES);
        if (odrList == null || odrList.size() < OcBOrderConst.IS_STATUS_IY) {
            return valueHolderResult("所选订单不存在", false, usr);
        }

        // 订单校验
       /* ValueHolderV14 vhv = validateOrderInfo(odrList, usr);
        if (ResultCode.FAIL == vhv.getCode()) {
            return vhv;
        }*/

        return validateOrderInfo(odrList, usr);

    }

    /**
     * 订单校验
     *
     * @param odrList List
     * @param usr     User
     * @return vh14
     */
    private ValueHolderV14 validateOrderInfo(List<QueryOrderResult> odrList, User usr) {

        Long shopId = null;
        List<Long> idList = new ArrayList<>();
        for (QueryOrderResult o : odrList) {
            if (o == null) {
                recordLog("###" + this.getClass().getName() + "### validateOrderInfo: 订单查询存在未知订单");
                return valueHolderResult("订单查询存在未知订单", false, usr);
            }
            // 开票状态
            Integer invoiceStatus = o.getInvoiceStatus();
            String ocBInvoiceNoticeId = o.getOcBInvoiceNoticeId();
            if (StringUtils.isNotBlank(ocBInvoiceNoticeId)) {
                recordLog("###" + this.getClass().getName() + "### validateOrderInfo: 不允许选择已开票的单据,"
                        + "ocBInvoiceNoticeId 存在相应开票通知记录");
                return valueHolderResult("不允许选择已开票的单据", false, usr);
            }

            if (invoiceStatus != null
                    && (!OcBorderListEnums.InvoiceStatusEnum.UN_REGISTER.getVal().equals(invoiceStatus))) {
                // 不允许选择已开票的单据
                recordLog("###" + this.getClass().getName() + "### validateOrderInfo: 不允许选择已开票的单据");
                return valueHolderResult("不允许选择已开票的单据", false, usr);
            }

            // 店铺
            if (shopId == null) {
                Long cpCShopId = o.getCpCShopId();
                if (cpCShopId == null) {
                    recordLog("###" + this.getClass().getName() + "### validateOrderInfo: ID:" + o.getId()
                            + ". 订单不存在店铺信息");
                    return valueHolderResult("订单不存在店铺信息", false, usr);
                }
                shopId = o.getCpCShopId();
            } else {
                if (!shopId.equals(o.getCpCShopId())) {
                    recordLog("###" + this.getClass().getName() + "### validateOrderInfo: 不允许不同店铺合并开票");
                    return valueHolderResult("不允许不同店铺合并开票", false, usr);
                }
            }

            // 订单状态
            Integer orderStatus = o.getOrderStatus();
            if (orderStatus == null) {
                recordLog("###" + this.getClass().getName() + "### validateOrderInfo: ID:"
                        + o.getId() + "订单状态不正确");
                return valueHolderResult("订单状态不正确", false, usr);
            }
            //仓库发货、平台发货、物流已送达、交易完成
            boolean osg;
            switch (orderStatus) {
                case 5:
                    osg = true;
                    break;
                case 6:
                    osg = true;
                    break;
                case 11:
                    osg = true;
                    break;
                case 12:
                    osg = true;
                    break;
                default:
                    osg = false;
                    break;
            }
            if (!osg) {
                return valueHolderResult("不允许未发货订单进行开票", false, usr);
            }
            idList.add(o.getId());
        }
        ValueHolderV14 vh = valueHolderResult("success", true, usr);
        vh.setData(idList);
        return vh;
    }

    /**
     * 拼接id
     *
     * @param ids Long[]
     * @return sting
     */
    private String sqlHandler(Long[] ids) {
        StringBuilder sb = new StringBuilder();
        boolean i = false;
        for (Long e : ids) {
            if (e == null) {
                continue;
            }
            if (i) {
                sb.append(",");
            }
            sb.append("'");
            sb.append(e);
            sb.append("'");
            if (!i) {
                i = true;
            }
        }
        return sb.toString();
    }

    /**
     * @param msg       String
     * @param flag      bool
     * @param loginUser User
     * @return vh14
     */
    private ValueHolderV14 valueHolderResult(String msg, boolean flag, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (flag) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        }
        return vh;
    }

    /**
     * debug 级别日志
     *
     * @param msg String
     */
    private void recordLog(String msg) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(msg));
        }
    }
}
