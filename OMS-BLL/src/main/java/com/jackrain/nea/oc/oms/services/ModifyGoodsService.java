package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.result.PsProductInfo;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.services.PsGetCommodityInformationService;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.request.SkuQueryRequest;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * @author: 周琳胜
 * @since: 2019/3/12
 * create at : 2019/3/12 11:45
 */
@Slf4j
@Component
public class ModifyGoodsService {
    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBorderUpdateServiceExt OcBorderUpdateServiceExt;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private PsRpcService psRpcService;


    /**
     * 更换商品
     *
     * @param obj  入参
     * @param user 用户
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 modifyGoods(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (null == obj) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        //获取待更换商品id
        Long orderItemId = obj.getLong("oc_b_order_item_id");
        // 获取主表id
        Long esOcbOrderId = obj.getLong("oc_b_order_id");
        //获取条码编码
        String ecode = obj.getString("SKU_ECODE");
        if (ecode == null) {
            throw new NDSException(Resources.getMessage("数据不符合，请核对后重试！", user.getLocale()));
        }

        //redis锁单
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(esOcbOrderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                QueryWrapper<OcBOrderItem> wrapper = new QueryWrapper<>();
                wrapper.eq("id", orderItemId).eq("oc_b_order_id", esOcbOrderId).ne("pro_type", 4);
                //获取明细信息
                OcBOrderItem ocBorderItem = ocBOrderItemMapper.selectOne(wrapper);
                //获取明细信息(不修改)
                OcBOrderItem ocBorderItemOld = new OcBOrderItem();
                BeanUtils.copyProperties(ocBorderItem, ocBorderItemOld);
                // 查到主表对象，查出订单编号（用作日志插入）
                OcBOrder order = ocBOrderMapper.selectById(esOcbOrderId);
                String billNo = order.getBillNo();
                // 更新明细信息
                UpdateWrapper<OcBOrderItem> wrapper1 = new UpdateWrapper<>();
                wrapper1.eq("oc_b_order_id", esOcbOrderId).eq("id", orderItemId);
                ocBorderItem.setModifierename(user.getEname());
                ocBorderItem.setModifieddate(new Date());
                //增加吊牌价和sex传值 20190827
                String skuEcode = obj.getString("SKU_ECODE");
                ProductSku productSku = psRpcService.selectProductSku(skuEcode);
                if (productSku != null ) {
                    ocBorderItem.setSex(productSku.getSex());
                    ocBorderItem.setPriceTag(productSku.getPricelist());
                }
                //此处解决分库建不能更改的报错
                ocBorderItem.setOcBOrderId(null);
                int count = ocBOrderItemMapper.update(ocBorderItem, wrapper1);
                log.debug("更换商品更新明细结果" + count);

                //获取新的明细信息
                QueryWrapper<OcBOrderItem> newWrapper = new QueryWrapper<>();
                newWrapper.eq("oc_b_order_id", esOcbOrderId).eq("id", orderItemId).ne("pro_type", 4);
                OcBOrderItem ESocBOrderItem = ocBOrderItemMapper.selectOne(newWrapper);
                try {
                    //  编辑保存服务
                    OcBOrderRelation relation = new OcBOrderRelation();
                    relation.setOrderInfo(order);
                    List<OcBOrderItem> itemList = new ArrayList<>();
                    ocBorderItemOld.setQty(BigDecimal.ZERO);
                    itemList.add(ocBorderItemOld);
                    itemList.add(ESocBOrderItem);
                    relation.setOrderItemList(itemList);
                    log.debug("更换商品调保存入参：" + relation.toString());
                    vh = OcBorderUpdateServiceExt.updateOrder(relation, user);
                    if (!vh.isOK()) {
                        throw new NDSException(vh.getMessage());
                    }
                } catch (Exception e) {
                    log.debug("调用编辑保存服务异常" + e.getMessage());
                    throw new NDSException(Resources.getMessage("调用编辑保存服务异常，不允许修改商品" + e.getMessage(), user.getLocale()));
                }
                // 推ES
//                try {
//                    Boolean b = SpecialElasticSearchUtil.indexDocument(OC_B_ORDER_INDEX_NAME, OC_B_ORDER_ITEM_TYPE_NAME,
//                            ESocBOrderItem, orderItemId, esOcbOrderId);
//                    if (!b) {
//                        throw new NDSException("推送es失败");
//                    }
//                } catch (Exception e) {
//                    throw new NDSException("推送es失败");
//                }
                //  调用订单日志服务
                try {
                    String logType = OrderLogTypeEnum.GOODS_REPLACE.getKey();
                    String logMessage = "更换为" + ecode + "条码";
                    String param = "";
                    String errorMessage = "";
                    omsOrderLogService.addUserOrderLog(orderItemId, billNo, logType, logMessage, param, errorMessage, user);
                } catch (Exception e) {
                    log.debug("调用服务出错:" + e.getMessage());
                }
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("更换成功！");
                return vh;
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
            }
        } catch (Exception ex) {
            throw new NDSException(Resources.getMessage("订单更新锁单错误！", user.getLocale()));
        } finally {
            redisLock.unlock();
        }


    }

    private SkuQueryRequest getRequest(String skuId) {
        SkuQueryRequest queryRequest = new SkuQueryRequest();
        queryRequest.setIsBlur("N");
        PsCSku psCSku = new PsCSku();
        psCSku.setEcode(skuId);
        queryRequest.setPsCSku(psCSku);
        return queryRequest;
    }

}
