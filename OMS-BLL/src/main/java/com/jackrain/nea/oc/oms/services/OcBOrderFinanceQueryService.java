package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemFiMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.result.OrderFinanceResult;
import com.jackrain.nea.oc.oms.model.result.OrderItemFinanceResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 给财务提供的服务
 *
 * @date 2019/4/19
 * @author: ming.fz
 */
@Component
@Slf4j
public class OcBOrderFinanceQueryService {

    @Autowired
    private ElasticSearchUtil elasticSearchUtil;

    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderItemFiMapper ocBOrderItemFiMapper;

    /**
     * 通过财务传入条件查询财务需要字段
     *
     * @param orderSource 订单来源
     * @param isWriteoff  是否插入核销流水
     * @param orderType   订单类型
     * @param orderStatus 订单状态
     * @param pageSize    每页显示条数
     * @param pageNum     当前页
     * @return 订单主子表对象
     * @throws NDSException
     */
    public ValueHolderV14 query(Long shopId, String orderSource, Long isWriteoff, List<Long> orderType, List<Long> orderStatus,
                                Integer pageSize, Integer pageNum) throws NDSException {
        ValueHolderV14 vh = new ValueHolderV14();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("mfz-通过财务传入条件查询财务需要字段入参 ->是否插入核销流水：{},订单状态：{}, 下单店铺/订单来源/订单类型：" , shopId,
                    orderSource, orderType), isWriteoff, orderStatus);
        }
        if (shopId == null || orderSource == null || isWriteoff == null || orderType == null || orderStatus == null) {
            vh.setCode(-1);
            vh.setMessage("部分入参为null！下单店铺：" + shopId + "订单来源：" + orderSource +
                    ",是否插入核销流水：" + isWriteoff + ",订单类型：" + orderType + "订单状态：" + orderStatus);
            return vh;
        }

        JSONObject search = ES4Order.getIdByShopIdAndSourceAndWriteAndTypeAndStatus(
                shopId, orderSource, isWriteoff, orderType, orderStatus, pageSize, pageNum);

        if (search.getLong("code") < 0 || search.get("data") == null) {
            vh.setCode(-1);
            vh.setMessage("当前没有符合条件的数据！");
            return vh;
        }

        List<OrderFinanceResult> orderFinanceResultsList = new ArrayList<>();
        JSONArray jsonArray = search.getJSONArray("data");
        if (jsonArray.size() > 0) {
            //es中查出的ID取出存入集合中
            List<Long> idList = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                Long id = jsonArray.getJSONObject(i).getLong("ID");
                idList.add(id);
            }
            //将es主表中查出的ids去数据库中查出对应的数据
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectOrderListByIds(idList);

            //拼接没有退款的数据
            StringBuffer sb = getAppendRefundStatus();
            String appendRefundStatus = sb.toString();

            for (OcBOrder ocBOrder : ocBOrders) {
                List<OrderItemFinanceResult> tidAndSumRealAmt = ocBOrderItemFiMapper.getTidAndRealAmt(ocBOrder.getId(), appendRefundStatus);
                if (tidAndSumRealAmt == null) {
                    continue;
                }
                OrderFinanceResult orderFinanceResult = new OrderFinanceResult();
                orderFinanceResult.setOcBOrder(ocBOrder);
                orderFinanceResult.setOrderItemFinanceResults(tidAndSumRealAmt);
                orderFinanceResultsList.add(orderFinanceResult);
            }
        } else {
            vh.setCode(-1);
            vh.setMessage("当前没有符合条件的数据！");
            return vh;
        }
        vh.setCode(0);
        vh.setMessage("success");
        vh.setData(orderFinanceResultsList);
        return vh;
    }

    //拼接没有退单的状态
    private StringBuffer getAppendRefundStatus() {
        StringBuffer sb = new StringBuffer();
        sb.append("(");
        sb.append(OmsOrderRefundStatus.UNREFUND.toInteger());
        sb.append(",");
        sb.append(OmsOrderRefundStatus.SELLERREFUSEBUYER.toInteger());
        sb.append(",");
        sb.append(OmsOrderRefundStatus.CLOSED.toInteger());
        sb.append(")");
        return sb;
    }
}