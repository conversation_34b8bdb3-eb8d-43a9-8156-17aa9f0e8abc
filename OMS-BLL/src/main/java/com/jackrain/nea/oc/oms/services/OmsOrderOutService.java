package com.jackrain.nea.oc.oms.services;

import com.burgeon.r3.sg.store.model.result.out.SgOutResultSendMsgResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.CommonIdempotentMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.BackflowStatus;
import com.jackrain.nea.oc.oms.model.enums.CommonIdempotentTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.CommonIdempotent;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryProcessor;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BigDecimalUtil;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.WmsUserCreateUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.util.SendToBOrderMessageUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.jackrain.nea.oc.oms.model.constant.OcCommonConstant.DELIVERY_SEND_DINGTALK;
import static com.jackrain.nea.util.SendToBOrderMessageUtil.DELIVERY;

/**
 * 订单出库后端服务
 *
 * @author: hulinyang
 * @since: 2019/3/26
 * create at : 2019/3/26 15:29
 */
@Component
@Slf4j
public class OmsOrderOutService {

    private static final int OMS_ORDER_REDIS_TIMEOUT = 24 * 60 * 60 * 1000;

    private final static String TOB_OUT_PART = "TOB_OUT_PART";

    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderLinkService ocBOrderLinkService;

    @Autowired
    private OrderDeliveryProcessor orderDeliveryProcessor;

    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;

    @Autowired
    private OcBOrderMapper OcBOrderMapper;

    @Autowired
    private OrderStrategyPriceComputeService orderStrategyPriceComputeService;

    @Autowired
    private WmsUserCreateUtil wmsUserCreateUtil;

    @Autowired
    private OmsToSapTaskService omsToSapTaskService;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private SendToBOrderMessageUtil sendToBOrderMessageUtil;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private CommonIdempotentMapper commonIdempotentMapper;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;

    /**
     * 订单出库服务入口
     *
     * @param orderId 订单关系实体
     * @return boolean
     */

    public ValueHolderV14 orderOutOfStock(Long orderId, int Type, User user, Date outDate) {
        OmsOrderOutService bean = ApplicationContextHandle.getBean(OmsOrderOutService.class);
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            boolean flag = false;
            OcBOrder ocBOrder = omsOrderService.selectOrderInfo(orderId);
            if (ocBOrder == null) {
                vh.setCode(-1);
                vh.setMessage("当前订单不存在!");
                return vh;
            }
            //校验订单状态
            flag = checkBillStatus(ocBOrder, user);
            if (!flag) {
                vh.setCode(-1);
                vh.setMessage("订单已发货，不允许重复发货！");
                return vh;
            }
            //判断条码非虚拟条码且订单状态已审核、配货中，若否则报错提示
            OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
            ocBOrderRelation.setOrderInfo(ocBOrder);
            List<OcBOrderItem> orderItemList = orderItemMapper.selectOrderItemList(orderId);
            if (orderItemList.size() == 0) {
                vh.setCode(-1);
                vh.setMessage("该订单不存在明细，不允许订单出库！");
                return vh;
            }
            ocBOrderRelation.setOrderItemList(orderItemList);
            if (!(OmsOrderStatus.CHECKED.toInteger().equals(ocBOrder.getOrderStatus())
                    || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(ocBOrder.getOrderStatus()))) {
                vh.setCode(-1);
                vh.setMessage("订单状态异常，不允许订单出库！");
                return vh;
            }
            //TODO 调用扣减库存服务（释放库存在单，扣减库存）OmsOrderStockQueryService
            OcBOrder newOcBOrder = new OcBOrder();
            //设置出库时间为wms回传时间【sg传过来的出库时间】
            newOcBOrder.setScanTime(outDate);
            newOcBOrder.setId(ocBOrder.getId());
            newOcBOrder.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
            if (Type == 1) {
                //走订单出库按钮调用的出库服务
                OcBOrderDelivery ocBOrderDelivery = new OcBOrderDelivery();
                ocBOrderDelivery.setId(ModelUtil.getSequence("oc_b_order_delivery"));
                ocBOrderDelivery.setOcBOrderId(ocBOrder.getId());
                //设置物流公司id，code，name
                ocBOrderDelivery.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
                ocBOrderDelivery.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
                ocBOrderDelivery.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
                //设置快递单号
                ocBOrderDelivery.setLogisticNumber(ocBOrder.getExpresscode());
                ocBOrderDelivery.setIsactive("N");
                //调用插入发货信息表的公共方法
                insertOcBOrderDelivery(ocBOrderDelivery, ocBOrder);
                //根据订单明细表，计算“商品数量”和“所有商品”的字段值
                bean.updateOcBOrder(newOcBOrder);
                /**
                 * 同步sap:  1. 订单出库按钮调用的出库服务
                 */
                //根据实体仓是否为SAP管控仓 判断是否传SAP
                //omsToSapTaskService.isToSapOrder(ocBOrder);
            } else if (Type == 2) {
                //自动出库流程调用出库服务
                bean.updateOcBOrder(newOcBOrder);
                /**
                 * 同步sap: 2. 自动出库流程调用出库服务
                 */
                //根据实体仓是否为SAP管控仓 判断是否传SAP
                //omsToSapTaskService.isToSapOrder(ocBOrder);
                //是否调用结算日志开关增加，不配置时，默认false不调用
            }
            //传全链路日志注释掉【20191019压测】
            if (PlatFormEnum.TAOBAO.getCode().equals(ocBOrder.getPlatform())) {
                List<OcBOrder> ocBOrders = new ArrayList<>(1);
                ocBOrders.add(ocBOrder);
                ocBOrderLinkService.addOrderFinkLogs(ocBOrders, BackflowStatus.QIMEN_CP_OUT.parseValue());
            }
            ocBOrderRelation.getOrderInfo().setOrderStatus(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
            ocBOrderRelation.setAutomaticOperation(true);
            boolean PlatformDeliveryFlag = orderDeliveryProcessor.platformSend(ocBOrderRelation);
            if (!PlatformDeliveryFlag) {
                vh.setCode(-1);
                vh.setMessage("调用平台发货服务，平台发货请求通知云枢纽失败!");
                return vh;
            } else {
                vh.setCode(0);
                vh.setMessage("调用平台发货服务，平台发货请求通知云枢纽成功!");
                return vh;
            }

        } catch (Exception ex) {
            log.error(LogUtil.format("订单出库服务逻辑执行异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            vh.setCode(-1);
            vh.setMessage("订单出库服务逻辑执行异常!");
            return vh;
        }
    }

    public ValueHolderV14 batchOrderOutOfStock(List<Long> orderIdList, List<SgOutResultSendMsgResult> mqOutOrderList) {
        OmsOrderOutService bean = ApplicationContextHandle.getBean(OmsOrderOutService.class);
        ValueHolderV14 vh = new ValueHolderV14();
        vh.setCode(0);
        vh.setMessage("订单批量出库处理完毕！");
        User user = wmsUserCreateUtil.initWmsUser();
        if (orderIdList.size() > 0) {
            for (Long orderId : orderIdList) {
                try {
                    OcBOrder ocBOrder = omsOrderService.selectOrderInfo(orderId);
                    if (ocBOrder == null) {
                        continue;
                    }
                    //校验订单状态
                    if (!checkBillStatus(ocBOrder, user)) {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_SEND.getKey(), ocBOrder.getId() + "仓库发货服务,订单已发货，不允许重复发货!", null, "订单已发货，不允许重复发货", user);
                        continue;
                    }
                    //判断条码非虚拟条码且订单状态已审核、配货中，若否则报错提示

                    List<OcBOrderItem> orderItemList = orderItemMapper.selectOrderItemList(orderId);
                    if (CollectionUtils.isEmpty(orderItemList)) {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_SEND.getKey(), ocBOrder.getId() + "仓库发货服务,订单不存在明细，不允许订单出库!", null, "订单不存在明细，不允许订单出库", user);
                        continue;
                    }
                    if (!(OmsOrderStatus.CHECKED.toInteger().equals(ocBOrder.getOrderStatus())
                            || OmsOrderStatus.PENDING_WMS.toInteger().equals(ocBOrder.getOrderStatus())
                            || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(ocBOrder.getOrderStatus()))) {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_SEND.getKey(), ocBOrder.getId() + "仓库发货服务,订单状态非“已审核或传wms中或配货中”,不允许订单出库", null, "订单状态非“已审核或传wms中或配货中”,不允许订单出库", user);
                        continue;
                    }

                    OcBOrder newOcBOrder = new OcBOrder();
                    newOcBOrder.setId(ocBOrder.getId());
                    newOcBOrder.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
                    bean.updateOcBOrder(newOcBOrder);
                    sendToBOrderMessageUtil.checkAndSendDingding(ocBOrder, DELIVERY, mqOutOrderList, DELIVERY_SEND_DINGTALK);

                    /**
                     * 同步sap: 3. 仓库发货批量接mq
                     */
                    //根据实体仓是否为SAP管控仓 判断是否传SAP
                    //omsToSapTaskService.isToSapOrder(ocBOrder);
                    //调用全链路日志服务
                    if (PlatFormEnum.TAOBAO.getCode().equals(ocBOrder.getPlatform())) {
                        List<OcBOrder> ocBOrders = new ArrayList<>(1);
                        ocBOrders.add(ocBOrder);
                        ocBOrderLinkService.addOrderFinkLogs(ocBOrders, BackflowStatus.QIMEN_CP_OUT.parseValue());
                    }
                    //调用订单平台发货服务。
                    OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                    ocBOrder.setOrderStatus(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
                    ocBOrderRelation.setOrderInfo(ocBOrder);
                    ocBOrderRelation.setOrderItemList(orderItemList);
                    ocBOrderRelation.setAutomaticOperation(true);
                    orderDeliveryProcessor.platformSend(ocBOrderRelation);
                } catch (Exception ex) {
                    log.error("batchOrderOutOfStock方法内，订单出库服务逻辑执行异常:" + orderId, ex);
                    vh.setCode(-1);
                    vh.setMessage("订单出库服务逻辑执行异常！单号:"+orderId);
                }
            }
        }
        return vh;
    }

    /**
     * 仓库发货资金占用
     *
     * @param user
     * @param ocBOrder
     */
    private void callOnlineFundOccupy(User user, OcBOrder ocBOrder) {
        OcBOrder order = OcBOrderMapper.selectByID(ocBOrder.getId());
        int orderStatus = order.getOrderStatus();

        if (orderStatus == OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger() || orderStatus == OmsOrderStatus.PLATFORM_DELIVERY.toInteger()) {
            OcBOrderParam orderInfo = new OcBOrderParam();
            orderInfo.setOcBOrder(order);
            orderInfo.setOrderItemList(orderItemMapper.selectOrderItemList(order.getId()));
            List<OcBOrderParam> orderInfos = new ArrayList<>();
            orderInfos.add(orderInfo);
//            ValueHolderV14 valueHolderV14 = orderStrategyPriceComputeService.onlineFundOccupy(orderInfos, AcScConstantsIF.BIll_VARIETY_NODE_OUT, user);
//            if (log.isDebugEnabled()) {
//                log.debug("仓库发货调用线上代销资金占用变动服务出参：{}", JSONObject.toJSONString(valueHolderV14));
//            }

        }
    }


    /**
     * 检查订单状态
     *
     * @param ocBOrder 订单实体
     * @return boolean
     */
    public boolean checkBillStatus(OcBOrder ocBOrder, User user) {

        boolean flag;
        try {
            int orderStatus = ocBOrder.getOrderStatus();

            flag = orderStatus != OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger()
                    && orderStatus != OmsOrderStatus.PLATFORM_DELIVERY.toInteger();
        } catch (Exception ex) {
            flag = false;
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                    ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_SEND.getKey(), ocBOrder.getId() + "仓库发货服务-判断订单状态方法发生异常" + ex.getMessage(), null, ex.getMessage(), user);
            log.error("判断订单状态异常{} ", ex.getMessage());
        }
        return flag;
    }

    /**
     * 新增发货信息
     *
     * @param ocBOrderDelivery 发货信息实体
     * @return 无
     */

    public void insertOcBOrderDelivery(OcBOrderDelivery ocBOrderDelivery, OcBOrder ocBOrder) {
        ocBOrderDeliveryMapper.insert(ocBOrderDelivery);
    }

    /**
     * 更新订单信息主表【订单状态，扫描入库时间】
     *
     * @param ocBOrder 订单信息实体
     * @return 无
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOcBOrder(OcBOrder ocBOrder) {
        boolean flag = false;
        ocBOrder.setOrderStatus(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
        //仓库发货时间
        ocBOrder.setWarehouseDeliveryTime(new Date());
        ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.WAREHOUSE_DELIVERY_TIME,new Date(),ocBOrder.getId(),SystemUserResource.getRootUser());
        try {
            flag = omsOrderService.updateOrderInfo(ocBOrder);
            if (!flag) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.STOCK_SEND.getKey(), ocBOrder.getId() + "仓库发货服务,更新出库状态失败！", null, "",
                        SystemUserResource.getRootUser());
            } else {
                String cpCLogisticsEname = ocBOrder.getCpCLogisticsEname() != null ?
                        "物流公司:" + ocBOrder.getCpCLogisticsEname() : "";
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.STOCK_SEND.getKey(),
                        ocBOrder.getId() + "仓库发货服务,更新出库状态成功！" + cpCLogisticsEname, null, "",
                        SystemUserResource.getRootUser());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("ES更新订单状态失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 部分出库生成新tob订单
     *
     * @param orderId
     */
    public void outPartCreateNewOrder(Long orderId) {
        OcBOrder order = OcBOrderMapper.selectByID(orderId);
        List<OcBOrderItem> itemList = orderItemMapper.selectOrderItemContainsCombination(orderId);
        if (CollectionUtils.isEmpty(itemList)){
            commonIdempotentMapper.updateDone(String.valueOf(orderId));
            return;
        }

        //实发数量 小于 数量的明细
        List<OcBOrderItem> dealItems = itemList.stream().filter(p -> p.getRealOutNum().compareTo(p.getQty()) < 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealItems)) {
            commonIdempotentMapper.updateDone(String.valueOf(orderId));
            return;
        }

        //新明细
        List<OcBOrderItem> newItems = getNewItems(dealItems);

        //新订单(区分订单是否带'合')
        OcBOrder newOrder = getNewOrder(order, newItems);

        //新增订单&明细
        saveOrder(newOrder, newItems, order.getBillNo(), order.getId());

        log.info(LogUtil.format("ToBOutPartTask outPartCreateNewOrder done.sourceId:{},newId:{}", "tob部分出库生成订单"), orderId, newOrder.getId());
    }

    /**
     * 组装新订单
     * 部分出库
     *
     * @param order
     * @param newItems
     * @return
     */
    private OcBOrder getNewOrder(OcBOrder order, List<OcBOrderItem> newItems) {
        OcBOrder newOrder = new OcBOrder();
        BeanUtils.copyProperties(order, newOrder);

        //主单
        defaultOrderValue(newOrder);
        //自动生成
        newOrder.setId(ModelUtil.getSequence("oc_b_order"));
        newOrder.setBillNo(sequenceUtil.buildBillNo());
        newOrder.setSuffixInfo(TOB_OUT_PART + "_" + newOrder.getId());
        //清空
        removeValue(newOrder);
        //赋值
        newOrder.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
        newOrder.setOccupyStatus(OcBorderListEnums.OccupyStatusEnum.UN_OCCUPY.getVal());
        newOrder.setAutoAuditStatus(OcBorderListEnums.AutoAuditStatusEnum.AUTO_AUDIT_NO.getVal());
        newOrder.setIsForce(0L);
        newOrder.setMakeupFailNum(0L);
        newOrder.setIsOverfive(0L);

        //金额计算
        amt(newItems, newOrder);

        //所有sku
        String allSku = getAllSku(newItems);
        newOrder.setAllSku(allSku.substring(0, allSku.length() - 1));

        Set<String> tids = newItems.stream().map(OcBOrderItem::getTid).collect(Collectors.toSet());
        if (tids.size() == 1) {
            String s = Lists.newArrayList(tids).get(0);
            newOrder.setMergeSourceCode(s);
            newOrder.setOrderSourcePlatformEcode(s);
            newOrder.setSourceCode(s);
            newOrder.setIsMerge(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
        }
        if (tids.size() > 1) {
            String join = StringUtils.join(tids, ",");
            newOrder.setMergeSourceCode(join);
            newOrder.setOrderSourcePlatformEcode(Lists.newArrayList(tids).get(0));
            newOrder.setSourceCode(Lists.newArrayList(tids).get(0));
            newOrder.setIsMerge(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
        }

        return newOrder;
    }

    private void amt(List<OcBOrderItem> newItems, OcBOrder newOrder) {
        //商品金额
        BigDecimal productAmt = BigDecimal.ZERO;
        //订单金额
        BigDecimal orderAmt = BigDecimal.ZERO;
        //商品数量
        BigDecimal qtyAll = BigDecimal.ZERO;
        //sku条数
        int skuKindQty = 0;

        for (OcBOrderItem newItem : newItems) {
            productAmt = productAmt.add(BigDecimalUtil.multiply(newItem.getQty(), newItem.getPrice()));
            orderAmt = orderAmt.add(BigDecimalUtil.multiply(newItem.getQty(), newItem.getPriceActual()));
            qtyAll = qtyAll.add(newItem.getQty());
            skuKindQty++;
        }
        newOrder.setProductAmt(productAmt);
        newOrder.setOrderAmt(orderAmt);
        //实付金额
        newOrder.setReceivedAmt(orderAmt);
        newOrder.setQtyAll(qtyAll);
        newOrder.setSkuKindQty(new BigDecimal(skuKindQty));
    }

    private String getAllSku(List<OcBOrderItem> newItems) {
        String allSku = "";
        for (OcBOrderItem item : newItems) {
            if (allSku.length() >= 100) {
                allSku += "...";
                break;
            }
            allSku = allSku + item.getPsCSkuEcode() + "(" + item.getQty().intValue() + "),";
        }
        return allSku;
    }

    /**
     * 组装新明细
     * 部分出库
     *
     * @param dealItems
     * @return
     */
    private List<OcBOrderItem> getNewItems(List<OcBOrderItem> dealItems) {
        List<OcBOrderItem> newItems = Lists.newArrayList();
        for (OcBOrderItem dealItem : dealItems) {
            OcBOrderItem newItem = new OcBOrderItem();

            BeanUtils.copyProperties(dealItem, newItem);
            defaultOrderItemValue(newItem);

            newItem.setId(sequenceUtil.buildOrderItemSequenceId());
            newItem.setQty(dealItem.getQty().subtract(dealItem.getRealOutNum()));
            newItem.setRealAmt(BigDecimalUtil.multiply(newItem.getQty(), newItem.getPriceActual()));
            newItem.setIsSendout(0);
            newItem.setOuterrcount(0);
            newItem.setRealOutNum(BigDecimal.ZERO);

            newItems.add(newItem);
        }
        return newItems;
    }

    /**
     * 清空特定字段
     *
     * @param newOrder
     */
    private void removeValue(OcBOrder newOrder) {
        newOrder.setSgBOutBillNo(null);
        newOrder.setSgBOutBillId(null);
        newOrder.setCpCPhyWarehouseId(null);
        newOrder.setCpCPhyWarehouseEcode(null);
        newOrder.setCpCPhyWarehouseEname(null);
        newOrder.setCpCLogisticsId(null);
        newOrder.setCpCLogisticsEcode(null);
        newOrder.setCpCLogisticsEname(null);
        newOrder.setAuditTime(null);
        newOrder.setSysremark(null);
        newOrder.setInsideRemark(null);
        newOrder.setSellerMemo(null);
        newOrder.setScanTime(null);
        newOrder.setDistributionTime(null);
        newOrder.setIsForce(null);
        newOrder.setForceSendFailReason(null);
        newOrder.setAuditFailedReason(null);
        newOrder.setAuditSuccessDate(null);
        newOrder.setAuditId(null);
        newOrder.setWarehouseDeliveryTime(null);
        newOrder.setStockOccupyDate(null);
        newOrder.setOutWmsReceiveTime(null);
        newOrder.setIsOutStock(null);
        newOrder.setIsOccupyStockFail(null);
        newOrder.setIsException(null);
        newOrder.setExceptionExplain(null);
        newOrder.setExceptionType(null);
        newOrder.setExpresscode(null);
        newOrder.setReserveVarchar04(null);
        newOrder.setReserveAuditTag(null);
        newOrder.setOrderEncryptionCode(null);
        newOrder.setStoOutBillNo(null);
        newOrder.setOccupySuccessDate(null);
        newOrder.setAuditType(null);
        newOrder.setExamineOrderDate(null);
    }

    /**
     * 主单默认值
     *
     * @param order
     */
    public void defaultOrderValue(OcBOrder order) {
        User user = SystemUserResource.getRootUser();
        Date now = new Date();

        order.setOwnerid(Long.valueOf(user.getId()));
        order.setOwnername(user.getName());
        order.setOwnerename(user.getEname());
        order.setCreationdate(now);
        order.setModifierid(Long.valueOf(user.getId()));
        order.setModifierename(user.getEname());
        order.setModifiername(user.getName());
        order.setModifieddate(now);
    }

    /**
     * 明细默认值
     *
     * @param item
     */
    public void defaultOrderItemValue(OcBOrderItem item) {
        User user = SystemUserResource.getRootUser();
        Date now = new Date();

        item.setOwnerid(Long.valueOf(user.getId()));
        item.setOwnername(user.getName());
        item.setOwnerename(user.getEname());
        item.setCreationdate(now);
        item.setModifierid(Long.valueOf(user.getId()));
        item.setModifierename(user.getEname());
        item.setModifiername(user.getName());
        item.setModifieddate(now);
    }

    /**
     * 新增订单/明细（tob）
     * 幂等
     * @param order
     * @param items
     * @param oldBillNo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrder(OcBOrder order, List<OcBOrderItem> items, String oldBillNo, Long oldOrderId) {
        OrderAddressConvertUtil.convert(order);
        OcBOrderMapper.insert(order);

        for (OcBOrderItem orderItem : items) {
            try {
                orderItem.setOcBOrderId(order.getId());
                orderItemMapper.insert(orderItem);
            } catch (Exception e) {
                e.printStackTrace();
                throw new NDSException(e);
            }
        }

        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                OrderLogTypeEnum.ORDER_ADD.getKey(), "tob部分出库新增订单成功,来源订单:" + oldBillNo, "", "", null);

        //幂等
        int i = commonIdempotentMapper.updateDone(String.valueOf(oldOrderId));
        if (i <= 0) {
            throw new NDSException("该数据已处理，无法二次生成");
        }

        // Redis推送。主要是为了防止ES在存储数据时，会延迟100ms左右。为了保障不重复转单，在Redis进行存储。
        // 在进行查询时，先查询ES，若ES不存在，再查询Redis。
        String redisKey = BllRedisKeyResources.getOmsOrderKey(order.getSourceCode());
        this.getRedisTemplate().opsForValue().set(redisKey, order.getId(), OMS_ORDER_REDIS_TIMEOUT,
                TimeUnit.MILLISECONDS);
        /**
         * 单据转单或新增后插入占单任务表
         */
        OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
        toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
        toBeConfirmedTask.setOrderId(order.getId());
        toBeConfirmedTask.setCreationdate(new Date());
        toBeConfirmedTask.setStatus(0);
        toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
    }

    private CusRedisTemplate<String, Long> getRedisTemplate() {
        return RedisMasterUtils.getObjRedisTemplate();
    }

    /**
     * 部分出库异常补偿
     *
     * @param orderId
     */
    public String outPartErrorFix(Long orderId) {
        //新增未出库明细的订单幂等任务
        CommonIdempotent commonIdempotent = new CommonIdempotent();
        commonIdempotent.setId(sequenceUtil.buildCommonIdempotentSequenceId());
        commonIdempotent.setBusinessCode(String.valueOf(orderId));
        commonIdempotent.setBatchNo("0");
        commonIdempotent.setType(CommonIdempotentTypeEnum.TOB_PART_OUT.getKey());
        commonIdempotent.setAdClientId(SystemUserResource.AD_CLIENT_ID);
        commonIdempotent.setAdOrgId(SystemUserResource.AD_ORG_ID);
        commonIdempotent.setCreationdate(new Date());
        commonIdempotent.setModifieddate(new Date());
        commonIdempotent.setOwnerid(SystemUserResource.ROOT_USER_ID);
        commonIdempotent.setOwnername(SystemUserResource.ROOT_USER_NAME);
        commonIdempotent.setModifierid(SystemUserResource.ROOT_USER_ID);
        commonIdempotent.setModifiername(SystemUserResource.ROOT_USER_NAME);
        commonIdempotent.setIsactive("Y");
        try {
            commonIdempotentMapper.insert(commonIdempotent);
        } catch (Exception e) {
            if (e instanceof DuplicateKeyException) {
                log.warn(LogUtil.format("tob部分发货被幂等 orderId:{}", "tobPartOutErrorBu"), orderId);
                return "tob部分发货被幂等";
            } else {
                log.error(LogUtil.format("tob部分发货数据存储失败 orderId:{}", "tobPartOutErrorBu"), orderId, e);
                return "tob部分发货数据存储失败";
            }
        }
        return "success";
    }
}
