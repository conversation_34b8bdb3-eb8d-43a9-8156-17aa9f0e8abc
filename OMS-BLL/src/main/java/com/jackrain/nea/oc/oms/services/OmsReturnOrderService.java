package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.GiftTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderIsInterceptEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.o2o.retailreturn.RetailReturnOrder;
import com.jackrain.nea.oc.oms.model.o2o.retailreturn.RetailReturnOrderItem;
import com.jackrain.nea.oc.oms.model.relation.IntermediateTableRelation;
import com.jackrain.nea.oc.oms.model.relation.IpOrderReturnRelation;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.ExchangeHoldTowingcConstant;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.returnin.OcRefundInGenerateStorageBillService;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.StandplatRefundOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.TaobaoRefundOrderTransferUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author:孙勇生
 * @description: 全渠道退换货处理服务
 * @Date: 19/3/11 12:08
 */
@Slf4j
@Component
public class OmsReturnOrderService {

    @Autowired
    private PropertiesConf pconf;
//    @Autowired
//    private R3MqSendHelper r3MqSendHelper;

    @Autowired
    private OcBReturnOrderMapper orderMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBReturnOrderRefundMapper orderRefundMapper;

    @Autowired
    private OcBReturnOrderExchangeMapper orderExchangeMapper;

    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OcBReturnBuildService ocBReturnBuildService;

    @Autowired
    private IpTaobaoRefundService ipTaobaoRefundService;

    @Autowired
    private OmsReturnOrderService omsReturnOrderService;

    @Autowired
    BuildSequenceUtil sequenceUtil;

    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;

    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;

    private static final int OMS_ORDER_REDIS_TIMEOUT = 24 * 60 * 60 * 1000;

    private static final int LOCK_ORDER_TIMEOUT = 60 * 1000;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Autowired
    OrderExchangeHoldTowingTaskService orderExchangeHoldTowingTaskService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcRefundInGenerateStorageBillService ocRefundInGenerateStorageBillService;

    @Autowired
    private OmsReturnOrderConfirmService omsReturnOrderConfirmService;

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;

    @Autowired
    private OcBOrderOffService ocBOrderOffService;

    @Autowired
    private TaobaoRefundOrderTransferUtil taobaoRefundOrderTransferUtil;

    @Autowired
    protected StandplatRefundOrderTransferUtil standplatRefundOrderTransferUtil;

    /**
     * 保存oms退单
     *
     * @param returnOrderRelations
     * @return -1:失败 1：新增成功 2：单据已存在
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveOmsReturnOrderInfo(List<OcBReturnOrderRelation> returnOrderRelations,
                                       User operateUser) {
        if (CollectionUtils.isNotEmpty(returnOrderRelations)) {
            for (int i = 0; i < returnOrderRelations.size(); i++) {
                OcBReturnOrderRelation ocBReturnOrderRelation = returnOrderRelations.get(i);
                if (CollectionUtils.isEmpty(ocBReturnOrderRelation.getOrderRefundList())) {
                    continue;
                }
                //处理商品
                if (i == 0) {
//                    String refundId = ocBReturnOrderRelation.getReturnOrderInfo().getReturnId();
//                    List<Long> idList = isExistReturnOrder(refundId);
//                    if (CollectionUtils.isNotEmpty(idList)) {
//                        return idList.get(0);
//                    }
                    try {
                        Long aLong = insertOmsRetuenOrderInfo(ocBReturnOrderRelation, operateUser);
                        if (aLong != null) {
                            return aLong;
                        }
                    } catch (Exception e) {
                        log.error(LogUtil.format("saveOmsReturnOrderInfo退单转单新增退单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    }
                } else {
                    //赠品
                    Long origOrderNo = ocBReturnOrderRelation.getReturnOrderInfo().getOrigOrderId();
                    if (!ES4ReturnOrder.isExistGiftReturnOrder(origOrderNo)) {
                        try {
                            insertOmsRetuenOrderInfo(ocBReturnOrderRelation, operateUser);
                        } catch (Exception e) {
                            log.error(LogUtil.format("saveOmsReturnOrderInfo退单转单新增退单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                        }
                    }
                }
            }
            return 0L;

        }

        return -1L;
    }

    public List<Long> isExistReturnOrder(String refundId) {
        List<Long> existReturnOrder = ES4ReturnOrder.isExistReturnOrder(refundId);
        List<Long> idList = null;
        if (CollectionUtils.isNotEmpty(existReturnOrder)) {
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(existReturnOrder);
            if (CollectionUtils.isNotEmpty(list)) {
                idList = list.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            }
        }
        return idList;
    }


    @Transactional(rollbackFor = Exception.class)
    public Long saveOmsReturnOrderInfos(OcBReturnOrderRelation returnOrderRelations,
                                        User operateUser) {
        if (returnOrderRelations != null) {
            if (CollectionUtils.isEmpty(returnOrderRelations.getOrderRefundList())) {
                return null;
            }
            return insertOmsRetuenOrderInfo(returnOrderRelations, operateUser);

        }
        return null;
    }


    /**
     * 重算应退金额、实退金额、物流费用
     * 主要是计算物流费用
     *
     * @param orderRelations
     * @param ipBTaobaoRefund
     */
    private void refreshAmtActualAndListAndShip(List<OcBReturnOrderRelation> orderRelations,
                                                IpBTaobaoRefund ipBTaobaoRefund) {
        if (Objects.nonNull(ipBTaobaoRefund)) {
            refreshAmtActualAndListAndShip(orderRelations, ipBTaobaoRefund.getRefundFee());
        }
    }

    /**
     * 重算应退金额、实退金额、物流费用
     *
     * @param orderRelations
     */
    private void refreshAmtActualAndListAndShip(List<OcBReturnOrderRelation> orderRelations, BigDecimal refundFee) {
        if (CollectionUtils.isNotEmpty(orderRelations) && Objects.nonNull(refundFee)) {
            BigDecimal totalAmt = BigDecimal.ZERO;

            // 重算
            for (int i = 0; i < orderRelations.size(); i++) {
                // @20200722 按索引取值，而不是写死第一个
                OcBReturnOrderRelation returnOrderRelation = orderRelations.get(i);

                OcBReturnOrder ocBReturnOrder = returnOrderRelation.getReturnOrderInfo();
                List<OcBReturnOrderRefund> ocBReturnOrderRefundList = returnOrderRelation.getOrderRefundList();

                if (Objects.nonNull(ocBReturnOrder) && CollectionUtils.isNotEmpty(ocBReturnOrderRefundList)) {
                    // 应退金额、实退金额
                    BigDecimal amt =
                            ocBReturnOrderRefundList.stream().map(OcBReturnOrderRefund::getAmtRefund).reduce(BigDecimal.ZERO, BigDecimal::add);
                    totalAmt = totalAmt.add(amt);
                    ocBReturnOrder.setReturnAmtList(amt);
                    ocBReturnOrder.setReturnAmtActual(amt);

                    // 如果有多笔退单，运费应该挂最后一笔
                    if (i == orderRelations.size() - 1) {
                        BigDecimal offset = refundFee.subtract(totalAmt);

                        if (offset.doubleValue() > 0) {
                            ocBReturnOrder.setReturnAmtShip(offset);
                        }
                    }
                }
            }
        }
    }

    /**
     * @param orderRelations
     * @param ipBTaobaoRefund
     * @param ocBOrderSize
     * @param operateUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Long> insertOmsReturnOrderInfo(List<OcBReturnOrderRelation> orderRelations,
                                               IpBTaobaoRefund ipBTaobaoRefund, int ocBOrderSize, User operateUser) {
        // 如果ocBOrderSize > 1，说明经过拆单，拆单的情况不重新计算物流费用
//        if (ocBOrderSize == 1) {
//            refreshAmtActualAndListAndShip(orderRelations, ipBTaobaoRefund);
//        }

        // 保存数据
        return insertOmsReturnOrderInfo(orderRelations, operateUser);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<Long> insertOmsReturnOrderInfo(List<OcBReturnOrderRelation> orderRelations, User operateUser) {
        List<Long> idList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderRelations)) {
            throw new NDSException("数据异常!");
        }
        for (OcBReturnOrderRelation orderRelation : orderRelations) {
            if (CollectionUtils.isEmpty(orderRelation.getOrderRefundList())) {
                if (log.isDebugEnabled()) {
                    log.debug(" 退单不存在明细:{}", JSON.toJSONString(orderRelation));
                }
                continue;
            }
            Long id = this.insertOmsRetuenOrderInfo(orderRelation, operateUser);
            Long tbDisputeId = orderRelation.getReturnOrderInfo().getTbDisputeId();
            if (tbDisputeId != null) {
                String redisKey = BllRedisKeyResources.getReturnOrderDisputeIdKey(tbDisputeId + "");
                RedisMasterUtils.getStrRedisTemplate().opsForValue().set(redisKey, id + "");
            }
            idList.add(id);
            OcBReturnOrder returnOrderInfo = orderRelation.getReturnOrderInfo();
            // @20200720 加订单状态的更新
            // lombok @AllArgsConstructor(access = AccessLevel.PUBLIC) 因OcBOrder 参数过多报错 导致@Builder不可用 特更改以下代码
            OcBOrder order = new OcBOrder();
            order.setId(returnOrderInfo.getOrigOrderId());
            order.setReturnStatus(OcBorderListEnums.ReturnStatusEnum.RETURN_ING.getVal());
//            omsOrderService.updateOrderInfo(OcBOrder.builder().id(returnOrderInfo.getOrigOrderId()).returnStatus(OcBorderListEnums.ReturnStatusEnum.RETURN_ING.getVal()).build());
            omsOrderService.updateOrderInfo(order);
        }
        return idList;

    }


    @Transactional(rollbackFor = Exception.class)
    public Long insertOmsRetuenOrderInfo(OcBReturnOrderRelation orderInfo, User operateUser) {
        try {
            if (orderInfo == null) {
                return null;
            }

            this.isOcBReturnOrderIndexExit(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER);
            if (orderInfo.getReturnOrderInfo().getId() == null) {
                long orderId = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER);
                orderInfo.getReturnOrderInfo().setId(orderId);
            }
            OcBReturnOrder returnOrderInfo = orderInfo.getReturnOrderInfo();
            returnOrderInfo.setOwnerename(operateUser.getEname());
            //加入“空运单号延迟推单有效时间”字段
            returnOrderInfo.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrderInfo));
            Long origOrderId = null;
            if (orderInfo.getOrderExchangeList() != null) {
                for (OcBReturnOrderExchange orderItem : orderInfo.getOrderExchangeList()) {
                    if (orderItem.getId() == null) {
                        long exchangeId = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDEREXCHAGE);
                        orderItem.setId(exchangeId);
                        orderItem.setOcBReturnOrderId(orderInfo.getReturnOrderInfo().getId());
                    }
                    this.insertOrderReturnExchange(orderItem);
                }
            }
            if (orderInfo.getOrderRefundList() != null) {
                Long orderIdFlag = 0L;
                for (OcBReturnOrderRefund orderItem : orderInfo.getOrderRefundList()) {
                    if (orderItem.getId() == null) {
                        long refundId = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND);
                        orderItem.setId(refundId);
                        orderItem.setOcBReturnOrderId(orderInfo.getReturnOrderInfo().getId());
                    }
                    OcBOrderItem ocBOrderItem =
                            ocBOrderItemMapper.queryOrderById(orderItem.getOcBOrderItemId(), orderItem.getOcBOrderId());
                    returnOrderInfo.setOwnerename(operateUser.getEname());
                    origOrderId = orderItem.getOcBOrderId();
                    this.insertOrderReturnRefund(orderItem);
                    //更新订单明细的可退数量 已退数量
                    OcBOrderItem item = new OcBOrderItem();
                    if (ocBOrderItem != null && ocBOrderItem.getProType() == SkuType.COMBINE_PRODUCT && !orderIdFlag.equals(ocBOrderItem.getOcBOrderId())) {
                        //找出该下挂商品的组合商品
                        //明细id
                        String groupGoodsMark = ocBOrderItem.getGroupGoodsMark();
                        OcBOrderItem bOrderItem = ocBOrderItemMapper.selectOcBOrderItemByGoodsMark(ocBOrderItem.getOcBOrderId(), groupGoodsMark);
                        if (bOrderItem != null) {
                            OcBOrderItem groupItem = new OcBOrderItem();
                            groupItem.setId(bOrderItem.getId());
                            groupItem.setOcBOrderId(bOrderItem.getOcBOrderId());
                            BigDecimal qtyRefund = orderItem.getQtyRefund();
                            if (qtyRefund.compareTo(ocBOrderItem.getQty()) == 0) {
                                qtyRefund = bOrderItem.getQty();
                            } else {
                                qtyRefund = qtyRefund.divide(ocBOrderItem.getQtyGroup(), 0, BigDecimal.ROUND_HALF_UP);

                            }
                            BigDecimal qtyReturnApply = bOrderItem.getQtyReturnApply() == null ? BigDecimal.ZERO : bOrderItem.getQtyReturnApply();
                            BigDecimal addQty = qtyRefund.add(qtyReturnApply);
                            if (addQty.compareTo(bOrderItem.getQty()) > 0) {
                                addQty = bOrderItem.getQty();
                            }
                            groupItem.setQtyReturnApply(addQty);
                            omsOrderItemService.updateOcBOrderItem(groupItem, orderItem.getOcBOrderId());
                            orderIdFlag = ocBOrderItem.getOcBOrderId();
                        }
                    }
                    //是否为对等换货还原明细
                    Integer isEqualExchange = orderItem.getIsEqualExchange();
                    if((isEqualExchange == null || isEqualExchange.equals(0)) && StringUtils.isNotBlank(ocBOrderItem.getEqualExchangeRatio())){
                        //计算已退数量
                        List<OcBOrderEqualExchangeItem> equalExchangeItems = ocBOrderEqualExchangeItemMapper.selectOcBOrderEqualExchangeItemByOrderId(ocBOrderItem.getOcBOrderId(),ocBOrderItem.getEqualExchangeMark());
                        if(CollectionUtils.isNotEmpty(equalExchangeItems)){
                            BigDecimal qtyRefund = equalExchangeItems.get(0).getQtyRefund();
                            QueryWrapper<OcBOrderEqualExchangeItem> wrapper = new QueryWrapper<>();
                            wrapper.eq("oc_b_order_id", ocBOrderItem.getOcBOrderId());
                            wrapper.eq("equal_exchange_mark", ocBOrderItem.getEqualExchangeMark());
                            OcBOrderEqualExchangeItem updateExchangeItem = new OcBOrderEqualExchangeItem();
                            updateExchangeItem.setQtyRefund(orderItem.getQtyRefund().add(qtyRefund == null?BigDecimal.ZERO:qtyRefund));
                            ocBOrderEqualExchangeItemMapper.update(updateExchangeItem, wrapper);
                        }
                    }else {
                        item.setId(orderItem.getOcBOrderItemId());
                        item.setOcBOrderId(orderItem.getOcBOrderId());
                        BigDecimal qtyReturnApply = ocBOrderItem.getQtyReturnApply() == null ? BigDecimal.ZERO : ocBOrderItem.getQtyReturnApply();
                        item.setQtyReturnApply(orderItem.getQtyRefund().add(qtyReturnApply));
                        omsOrderItemService.updateOcBOrderItem(item, orderItem.getOcBOrderId());
                    }
                }
            }
            returnOrderInfo.setOrigOrderId(origOrderId);
            this.insertOrderReturn(returnOrderInfo);
            String returnId = returnOrderInfo.getReturnId();
            CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
            if (StringUtils.isNotEmpty(returnId)) {
                String redisKey = BllRedisKeyResources.getOmsReturnOrderReturnIdKey(returnId);
                objRedisTemplate.opsForValue().set(redisKey, returnOrderInfo.getId(), OMS_ORDER_REDIS_TIMEOUT,
                        TimeUnit.MILLISECONDS);
            }
            OcBOrder ocBOrder = orderInfo.getOcBOrder();
            List<OcBOrderItem> ocBOrderItems = orderInfo.getOcBOrderItems();
            if (ocBOrder != null) {
                ocBOrderMapper.insert(ocBOrder);
                ocBOrderItemMapper.batchInsert(ocBOrderItems);
                this.creatToBeConfirmedTask(ocBOrder);
                ocBOrder.setIsInterecept(OmsOrderIsInterceptEnum.YES.getVal());
                orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_ADD.getKey(), "新增换货订单成功", "", "", operateUser);
                ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder, OrderHoldReasonEnum.ADD_EXCHANGE_ORDER);
                try {
                    //插入换货hold传wing中间表
                    orderExchangeHoldTowingTaskService.creatExchangeHoldTowingTask(ocBOrder.getId(), ocBOrder.getBillNo(), ExchangeHoldTowingcConstant.STATUS_0);
                } catch (Exception e) {
                    log.info("换货开始调用wing接口hold住库存异常,orderId{}", ocBOrder.getId());
                }
            }
            this.saveAddOrderReturnLog(returnOrderInfo.getId(), SysNotesConstant.ADD_LOG_MESSAGE,
                    SysNotesConstant.ADD_LOG_TYPE, operateUser);
            if (ocBOrder != null) {
                this.saveAddOrderReturnLog(returnOrderInfo.getId(), String.format("对应换货订单ID:%d,单据编号:%s", ocBOrder.getId(), ocBOrder.getBillNo()),
                        "生成换货订单成功", operateUser);
            }
            return returnOrderInfo.getId();
        } catch (Exception e) {
            log.error(LogUtil.format("生成退换货单失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }

    /**
     * 单据转单或新增后插入占单任务表
     */
    public void creatToBeConfirmedTask(OcBOrder ocBOrder) {
        OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
        toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
        toBeConfirmedTask.setOrderId(ocBOrder.getId());
        toBeConfirmedTask.setCreationdate(new Date());
        toBeConfirmedTask.setStatus(0);
        toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
    }


    /**
     * @param id 退单主表id
     * @return 主表数据
     */
    public OcBReturnOrder selectReturnOrderById(Long id) {
        return orderMapper.selectById(id);
    }

    /**
     * 按主表ID查明细列表
     *
     * @param returnOrderId
     * @return
     */
    public List<OcBReturnOrderRefund> selectRefundsByOcOrderId(Long returnOrderId) {
        return orderRefundMapper.selectByOcOrderId(returnOrderId);
    }

    /**
     * 如果存在，则更新退货单中的物流公司和物流单号，同时插入退换货单的更新日志，修改退单的状态为已转换，
     * 添加系统备注“退货单中已经存在，修改退单状态为已转换”
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveExistReturnOrder(Long id, IpTaobaoRefundRelation
            taobaoRefundRelation, User operateUser) throws IOException {
        IpBTaobaoRefund taobaoRefund = taobaoRefundRelation.getTaobaoRefund();
        OcBReturnOrder returnOrder = orderMapper.selectById(id);
        String logisticsCode = returnOrder.getLogisticsCode();
        String cpCLogisticsEname = returnOrder.getCpCLogisticsEname();
        String sid = taobaoRefund.getSid();
        String companyName = taobaoRefund.getCompanyName();
        if (StringUtils.isNotEmpty(sid) && StringUtils.isNotEmpty(companyName)
                && StringUtils.isEmpty(logisticsCode) && StringUtils.isEmpty(cpCLogisticsEname)) {
            OcBReturnOrder returnOrder1 = new OcBReturnOrder();
            returnOrder1.setId(id);
            returnOrder1.setLogisticsCode(sid);
            returnOrder1.setCpCLogisticsEname(companyName);
            returnOrderTransferUtil.setLogisticInfo(returnOrder1, companyName);
            //加入“空运单号延迟推单有效时间”字段
            returnOrder1.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder1));
            this.updateOcBReturnOrder(returnOrder1);
            //修改退单中间表状态更新系统备注
            String remark = SysNotesConstant.SYS_REMARK5;
            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, taobaoRefund);
            //增加系统日志
            this.saveAddOrderReturnLog(id, SysNotesConstant.UPDATE_LOG_MESSAGE,
                    SysNotesConstant.UPDATE_LOG_TYPE, operateUser);
        } else {
            String remark = SysNotesConstant.SYS_REMARK5;
            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, taobaoRefund);
        }
    }


    /**
     * 保存换货数据 并调用生成换货订单服务
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveOmsExchangeOrderInfo(IpTaobaoExchangeRelation exchangeRelation, User operateUser) {
        if (exchangeRelation == null) {
            return null;
        }

        // @20200802 换货单ID
        Long disputeId = exchangeRelation.getTaobaoExchange().getDisputeId();

        try {
            ES4ReturnOrder.isOcBReturnOrderIndexExit(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER);
            OcBReturnOrderRelation orderInfo =
                    returnOrderTransferUtil.taobaoExchangeOrderToReturnOrder(exchangeRelation);
            List<OcBReturnOrderExchange> orderExchangeList = orderInfo.getOrderExchangeList();
            List<OcBReturnOrderRefund> orderRefundList = orderInfo.getOrderRefundList();
            long orderId = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER);
            OcBReturnOrder returnOrderInfo = orderInfo.getReturnOrderInfo();
            returnOrderInfo.setId(orderId);
            returnOrderInfo.setOwnerename(operateUser.getEname());
            returnOrderTransferUtil.setOperateUserInfo(returnOrderInfo, operateUser);
            // this.insertOrderReturn(returnOrderInfo);
            returnOrderInfo.setBillNo(sequenceUtil.buildReturnBillNo()); // 退单编号
            orderMapper.insert(returnOrderInfo); //先不推ES

            // @20200802 存redis，解决并发重复问题的前段：赋值阶段
            OmsReturnOrderService.setOmsReturnOrderToRedisByReturnId(String.valueOf(disputeId), orderId);

            if (CollectionUtils.isNotEmpty(orderExchangeList)) {
                for (OcBReturnOrderExchange orderItem : orderExchangeList) {
                    long exchangeId = ModelUtil.getSequence(
                            TaobaoReturnOrderExt.TABLENAME_OCRETURNORDEREXCHAGE);
                    orderItem.setId(exchangeId);
                    returnOrderTransferUtil.setOperateUserInfo(orderItem, operateUser);
                    orderItem.setOcBReturnOrderId(orderId);
                    orderItem.setExchangeSku(exchangeRelation.getTaobaoExchange().getExchangeSku());
                    orderExchangeMapper.insert(orderItem); //先不推ES
                    // this.insertOrderReturnExchange(orderItem);
                }
            }
            if (CollectionUtils.isNotEmpty(orderRefundList)) {
                for (OcBReturnOrderRefund orderItem : orderInfo.getOrderRefundList()) {
                    long refundId = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND);
                    orderItem.setId(refundId);
                    returnOrderInfo.setOwnerename(operateUser.getEname());
                    returnOrderTransferUtil.setOperateUserInfo(orderItem, operateUser);
                    orderItem.setOcBReturnOrderId(orderId);
                    // this.insertOrderReturnRefund(orderItem);
                    orderRefundMapper.insert(orderItem);

                    // @20200811 bug#20832 生成退换货单之后要更新原单上的退数量
                    OcSaveChangingOrRefundingService.getInstance().setOrderItemRetuenCount(returnOrderInfo.getOrigOrderId(), orderItem, orderItem.getOcBOrderItemId(), new QueryWrapper<>());
                }
            }
            //todo 调用生成换货订单的服务
            IpBTaobaoExchange taobaoExchange = exchangeRelation.getTaobaoExchange(); //换货中间表数据
            OcBOrder ocBOrder = exchangeRelation.getOriginalValidOrderInfo(); //原始订单
            OcBOrder ocBOrder1 = returnOrderTransferUtil.
                    allChannelsOcBOrder(returnOrderInfo, ocBOrder, taobaoExchange, operateUser);
            //商品金额 = 标准价 * 数量
            ocBOrder1.setOrigReturnOrderId(orderId);
            List<ProductSku> psCProSkuResult = exchangeRelation.getExchangeProductDetailList();
            List<OcBOrderItem> orderItems = returnOrderTransferUtil.allChannelsOcBOrderItem(orderExchangeList,
                    psCProSkuResult, taobaoExchange, operateUser);
            BigDecimal reduce =
                    orderItems.stream().map(x -> x.getPrice().multiply(x.getQty())).reduce(BigDecimal.ZERO,
                            BigDecimal::add);
            ocBOrder1.setProductAmt(reduce); //商品总额
            String tid = ocBOrder.getTid();
            for (OcBOrderItem orderItem : orderItems) {
                orderItem.setTid(tid);
            }
            IpOrderReturnRelation ocBOrderRelation = new IpOrderReturnRelation();
            ocBOrderRelation.setOcBOrder(ocBOrder1);
            ocBOrderRelation.setOcBOrderItems(orderItems);
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            returnOrder.setId(orderId); //退换单id
            returnOrder.setTbDisputeId(returnOrderInfo.getTbDisputeId());
            returnOrder.setTid(returnOrderInfo.getTid());
            ocBOrderRelation.setOcBReturnOrder(returnOrder);
            // @20200811 没用的代码删除掉
            // String ip = returnOrderTransferUtil.getIp(operateUser);
            //"errormassage" -> "退单编号不能为空  [下单用户编号不能为空]    [原始订单号不能为空]"
            ValueHolderV14 valueHolderV14 = ocBReturnBuildService.buildExchange(ocBOrderRelation, operateUser);
            if (valueHolderV14 != null && valueHolderV14.getCode() == -1) {
                String message = valueHolderV14.getMessage();
                throw new NDSException("生成退换货订单失败!" + message);
            }

            // @20200803 bug-prd-286 换货单转单空指针问题：保存生成的订单ID到redis
            OmsOrderService.setOmsOrderToRedisForExchange(disputeId, ocBOrder1.getId());

            //@20201118 去除手推ES代码
            /*try {
                //推 退货单主编以及退单明细表和换货明细表数据(ES)
                ES4ReturnOrder.updateReturnOrderById(returnOrderInfo);
                //推换货明细表数据
                ES4ReturnOrder.updateOrderExchangeListByReturnId(orderExchangeList);
                //推退单明细数据
                ES4ReturnOrder.updateOrderRefundListByReturnId(orderRefundList);
            } catch (Exception e) {
                log.error(this.getClass().getName() + " 换货生成退换货单及换货订单推es异常", e);
            }*/

            // 2019-08-04 易邵峰修改：将Redis 和 ES的推送 调换位置。目的是为了防止ES操作时间过长
            // Redis推送。主要是为了防止ES在存储数据时，会延迟100ms左右。为了保障不重复转单，在Redis进行存储。
            // 在进行查询时，先查询ES，若ES不存在，再查询Redis。
            return orderId;
        } catch (Exception e) {
            // 如果出现异常，需要清理redis已经存的退单key，避免阻塞二次转单
            if (Objects.nonNull(disputeId)) {
                OmsReturnOrderService.removeOmsReturnOrderKeyByReturnId(disputeId.toString());
            }
            log.error(LogUtil.format("换货生成退换货单及换货订单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }

    /**
     * 依据returnId查询redis
     *
     * @param returnId
     * @return
     */
    public static long selectOmsReturnOrderFromRedisByReturnId(String returnId) {
        String redisKey = BllRedisKeyResources.getOmsReturnOrderReturnIdKey(returnId);
        Boolean hasKey = RedisMasterUtils.getObjRedisTemplate().hasKey(redisKey);

        if (hasKey != null && hasKey) {
            Object value = RedisMasterUtils.getObjRedisTemplate().opsForValue().get(redisKey);
            if (value == null) {
                return 0L;
            }
            return Long.valueOf(value.toString());
        } else {
            return 0L;
        }
    }

    /**
     * 移除KEY，一般在异常后使用
     *
     * @param returnId
     * @20200802 0419
     */
    public static void removeOmsReturnOrderKeyByReturnId(String returnId) {
        if (StringUtils.isNotEmpty(returnId)) {
            String redisKey = BllRedisKeyResources.getOmsReturnOrderReturnIdKey(returnId);

            Boolean hasKey = RedisMasterUtils.getObjRedisTemplate().hasKey(redisKey);

            if (hasKey != null && hasKey) {
                RedisMasterUtils.getObjRedisTemplate().delete(redisKey);
            }
        }
    }

    /**
     * 依据returnId设置redis
     *
     * @param returnId
     * @param returnOrderId
     */
    public static void setOmsReturnOrderToRedisByReturnId(String returnId, Long returnOrderId) {
        String redisKey = BllRedisKeyResources.getOmsReturnOrderReturnIdKey(returnId);
        RedisMasterUtils.getObjRedisTemplate().opsForValue().set(redisKey, returnOrderId, OMS_ORDER_REDIS_TIMEOUT,
                TimeUnit.MILLISECONDS);
    }

    /**
     * 插入退换单日志(推ES)
     *
     * @param returnId    退换货主表id
     * @param logMessage  日志类容
     * @param LogType     日志理性
     * @param operateUser USER对象(没有的话传null,会自动取默认值)
     */

    public void saveAddOrderReturnLog(Long returnId, String logMessage, String LogType, User operateUser) {
        try {
            OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
            ocBReturnOrderLog.setLogMessage(logMessage);
            ocBReturnOrderLog.setLogType(LogType);
            ocBReturnOrderLog.setOcBReturnOrderId(returnId);
            returnOrderTransferUtil.saveSysLog(ocBReturnOrderLog, operateUser);
            int insert = ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
           /* if (insert > 0) {
                Boolean aBoolean =
                        SpecialElasticSearchUtil.indexExists(TaobaoReturnOrderExt.TABLENAME_OCBRETURNORDERLOG);
                if (!aBoolean) {
                    SpecialElasticSearchUtil.indexCreate(OcBReturnOrderLog.class);
                }
                SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_OCBRETURNORDERLOG,
                        TaobaoReturnOrderExt.TABLENAME_OCBRETURNORDERLOG, ocBReturnOrderLog,
                        ocBReturnOrderLog.getId());
            }*/
        } catch (Exception e) {
            log.error(LogUtil.format("插入退换货订单日志异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }

    /**
     * 批量插入退换单日志
     *
     * @param returnOrderList 退换货单集合
     * @param logType         日志类型
     * @param logMsg          日志内容
     * @param operateUser     USER对象(没有的话传null,会自动取默认值)
     */
    public void batchSaveAddOrderReturnLog(List<OcBReturnOrder> returnOrderList, String logType, String logMsg, User operateUser) {
        try {
            List<OcBReturnOrderLog> returnOrderLogList = returnOrderList.stream().map(returnOrder -> {
                OcBReturnOrderLog ocBRefundInLog = new OcBReturnOrderLog();
                ocBRefundInLog.setOcBReturnOrderId(returnOrder.getId());
                ocBRefundInLog.setLogType(logType);
                ocBRefundInLog.setLogMessage(logMsg);
                returnOrderTransferUtil.saveSysLog(ocBRefundInLog, operateUser);
                return ocBRefundInLog;
            }).collect(Collectors.toList());
            ocBReturnOrderLogMapper.batchInsert(returnOrderLogList);
        } catch (Exception e) {
            log.error(LogUtil.format("插入退换货订单日志异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }

    public OcBReturnOrderLog buildAddOrderReturnLog(Long returnId, String logMessage, String LogType, User operateUser) {
        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
        ocBReturnOrderLog.setLogMessage(logMessage);
        ocBReturnOrderLog.setLogType(LogType);
        ocBReturnOrderLog.setOcBReturnOrderId(returnId);
        returnOrderTransferUtil.saveSysLog(ocBReturnOrderLog, operateUser);
        return ocBReturnOrderLog;
    }

    /**
     * 保存退换货单主表数据
     *
     * @param returnOrderInfo
     * @throws IOException
     */
    public boolean insertOrderReturn(OcBReturnOrder returnOrderInfo) throws IOException {
        //存在调用该方法前就已经生成单号的情况
        if (StringUtils.isEmpty(returnOrderInfo.getBillNo())) {
            returnOrderInfo.setBillNo(sequenceUtil.buildReturnBillNo()); // 退单编号
        }
        int result = orderMapper.insert(returnOrderInfo);
        /*SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                    TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, returnOrderInfo,
                    returnOrderInfo.getId());*/
        return result > 0;
    }

    /**
     * 保存退货单表数据
     *
     * @param returnOrderInfo
     * @throws IOException
     */
    public void insertOrderReturnRefund(OcBReturnOrderRefund returnOrderInfo) throws IOException {
        int result = orderRefundMapper.insert(returnOrderInfo);
        if (result > 0) {
           /* SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, TaobaoReturnOrderExt
                            .TABLENAME_OCRETURNORDERREFUND, returnOrderInfo, returnOrderInfo.getId(),
                    returnOrderInfo.getOcBReturnOrderId());*/
        }
    }


    /**
     * 保存换货单数据
     *
     * @param orderItem
     * @throws IOException
     */
    public void insertOrderReturnExchange(OcBReturnOrderExchange orderItem) throws IOException {
        orderExchangeMapper.insert(orderItem);
       /* if (flag1 > 0) {
            SpecialElasticSearchUtil.indexDocument(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                    TaobaoReturnOrderExt.TABLENAME_OCRETURNORDEREXCHAGE, orderItem,
                    orderItem.getId(), orderItem.getOcBReturnOrderId());
        }*/
    }

    /**
     * 更新主表信息
     *
     * @param ocBReturnOrder
     * @return
     * @throws IOException
     */
    public boolean updateOcBReturnOrder(OcBReturnOrder ocBReturnOrder) throws IOException {

        int i = orderMapper.updateById(ocBReturnOrder);
        return i > 0;
    }


    /**
     * 判断退换货订单ES索引是否存在
     *
     * @param index
     * @throws IOException
     */
    public void isOcBReturnOrderIndexExit(String index) throws IOException {
        Boolean aBoolean = SpecialElasticSearchUtil.indexExists(index);
        if (!aBoolean) {
            List<Class> list = new ArrayList<>();
            list.add(OcBReturnOrderExchange.class);
            list.add(OcBReturnOrderRefund.class);
            SpecialElasticSearchUtil.indexCreate(list, OcBReturnOrder.class);
        }
    }

    /**
     * 依据编码查订单
     *
     * @param billNo
     * @return
     */
    public OcBReturnOrder selectReturnOrderByBillNo(String billNo) {
        if (StringUtils.isNotEmpty(billNo)) {
            // 查ES，获取ID
            Long id = selectReturnOrderIdByBillNoFromEs(billNo);

            if (Objects.nonNull(id)) {
                OcBReturnOrder order = orderMapper.selectByid(id);
                return order;
            }
        }

        return null;
    }

    /**
     * 查询ID
     *
     * @param billNo
     * @return
     */
    public Long selectReturnOrderIdByBillNoFromEs(String billNo) {
        Set<Long> idSet = ES4ReturnOrder.findIdByBillNo(billNo);
        if (idSet.size() > 0) {
            return idSet.iterator().next();
        }
        return null;
    }

    /**
     * 根据退款单号获取退单
     *
     * @param returnId
     * @return
     */
    public OcBReturnOrder selectRefundOrderByRefundId(String returnId) {

        JSONArray data = ES4ReturnOrder.findIdByReturnIdAndStatus(returnId);
        if (CollectionUtils.isNotEmpty(data)) {
            JSONObject ocReturnJsonObject = data.getJSONObject(0);
            Long ocReturnId = ocReturnJsonObject.getLong("ID");
            return ocBReturnOrderMapper.selectByid(ocReturnId);
        }
        return null;
    }


    /**
     * 零售退单发送pos
     *
     * @param returnOrder
     * @param returnOrderRefunds
     */
    public boolean send4O2OByRetailReturn(OcBReturnOrder returnOrder, List<OcBReturnOrderRefund> returnOrderRefunds) {
        try {
            return send4O2OByRetailReturnWithException(returnOrder, returnOrderRefunds);
        } catch (Exception e) {
            log.error(LogUtil.format("send4O2OByRetailReturn,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }

        return false;
    }

    /**
     * 发送消息
     *
     * @param retailReturnOrder
     */
    private boolean send4O2OByRetailReturn(RetailReturnOrder retailReturnOrder) {
//        String topic = pconf.getProperty("r3.oc.oms.toqimen.mq.topic");
//        String tag = pconf.getProperty("r3.oc.oms.toqimen.mq.retailReturnOrder.tag");
//
//        if (Objects.nonNull(retailReturnOrder) && StringUtils.isNotEmpty(topic) && StringUtils.isNotEmpty(tag)) {
//            String key = UUID.randomUUID().toString();
//
//            try {
//                String messageId = r3MqSendHelper.sendMessage(retailReturnOrder, topic, tag, key);
//                log.debug("send4O2OByRetailReturn.send.success.messageId:{}/{}", retailReturnOrder.getRefundId(),
//                messageId);
//                return true;
//            } catch (SendMqException e) {
//                log.error("send4O2OByRetailReturn.send.error.info:{}/{}/{}/{}", retailReturnOrder.getRefundId(),
//                topic, tag, key);
//                log.error("send4O2OByRetailReturn.send.error:" + retailReturnOrder.getRefundId(), e);
//                throw new NDSException(e);
//            }
//        }
//
//        return false;
        return true;
    }

    /**
     * 更新成待发送状态
     *
     * @param id
     * @return
     */
    public int updateToACStatusToPendingById(Long id) {
        return updateToACStatusById(id, ToACStatusEnum.PENDING.getValue());
    }

    public int updateToACStatusToPendingById(List<Long> ids) {
        return updateToACStatusByIds(ids, ToACStatusEnum.PENDING.getValue());
    }

    /**
     * 更新成发送完成状态
     *
     * @param id
     * @return
     */
    public int updateToACStatusToSuccessById(Long id) {
        return updateToACStatusById(id, ToACStatusEnum.SUCCESS.getValue());
    }

    public int updateToACStatusToSuccessByIds(List<Long> ids) {
        return updateToACStatusByIds(ids, ToACStatusEnum.SUCCESS.getValue());
    }

    /**
     * 更新成失败状态
     *
     * @param id
     * @return
     */
    public int updateToACStatusToFailedById(Long id) {
        return updateToACStatusById(id, ToACStatusEnum.FAILED.getValue());
    }

    public int updateToACStatusToFailedByIds(List<Long> ids) {
        return updateToACStatusByIds(ids, ToACStatusEnum.FAILED.getValue());
    }

    /**
     * 更新传结算标识
     *
     * @param id
     * @param toACStatus
     * @return
     */
    public int updateToACStatusById(Long id, Integer toACStatus) {
        if (Objects.nonNull(id) && Objects.nonNull(toACStatus)) {
            List<Long> ids = new ArrayList<>();
            ids.add(id);
            return updateToACStatusByIds(ids, toACStatus);
        }

        return 0;
    }

    public int updateToACStatusByIds(List<Long> ids, Integer toACStatus) {
        if (CollectionUtils.isNotEmpty(ids) && Objects.nonNull(toACStatus)) {
            ocBReturnOrderMapper.updateToACStatusByIds(toACStatus, ids);
        }

        return 0;
    }

    /**
     * 重算应退金额和退款金额
     *
     * @param returnOrderRelation
     */
    public void reCalculateReturnAmt(OcBReturnOrderRelation returnOrderRelation) {
        if (Objects.nonNull(returnOrderRelation) && Objects.nonNull(returnOrderRelation.getReturnOrderInfo())) {
            // 退单
            OcBReturnOrder returnOrder = returnOrderRelation.getReturnOrderInfo();

            // 算：应退金额(AMT_REFUND)
            BigDecimal sumAmtRefund = BigDecimal.ZERO;

            if (CollectionUtils.isNotEmpty(returnOrderRelation.getOrderRefundList())) {
                for (OcBReturnOrderRefund refund : returnOrderRelation.getOrderRefundList()) {
                    if (Objects.nonNull(refund)) {
                        sumAmtRefund = sumAmtRefund.add(Objects.nonNull(refund.getAmtRefund()) ?
                                refund.getAmtRefund() : BigDecimal.ZERO);
                    }
                }
            }

            // 算：换货金额(换货明细：AMT_REFUND)
            BigDecimal sumAmtRefundExchange = BigDecimal.ZERO;

            if (CollectionUtils.isNotEmpty(returnOrderRelation.getOrderExchangeList())) {
                for (OcBReturnOrderExchange exchange : returnOrderRelation.getOrderExchangeList()) {
                    if (Objects.nonNull(exchange)) {
                        sumAmtRefundExchange = sumAmtRefundExchange.add(Objects.isNull(exchange.getAmtRefund()) ?
                                BigDecimal.ZERO : exchange.getAmtRefund());
                    }
                }
            }

            // 应退邮费(RETURN_AMT_SHIP)
            BigDecimal returnAmtShip = Objects.isNull(returnOrder.getReturnAmtShip()) ? BigDecimal.ZERO :
                    returnOrder.getReturnAmtShip();
            // 其他金额(RETURN_AMT_OTHER)
            BigDecimal returnAmtOther = Objects.isNull(returnOrder.getReturnAmtOther()) ? BigDecimal.ZERO :
                    returnOrder.getReturnAmtOther();

            // 前端逻辑：退单应退 = sum(应退金额(AMT_REFUND)) + 应退邮费(RETURN_AMT_SHIP) + 其他金额(RETURN_AMT_OTHER) - sum(换货金额
            // (换货明细：AMT_REFUND))
            BigDecimal returnAmtPrice =
                    sumAmtRefund.add(returnAmtShip).add(returnAmtOther).subtract(sumAmtRefundExchange);
            returnOrder.setReturnAmtActual(returnAmtPrice);
            returnOrder.setReturnAmtList(returnAmtPrice);
        }
    }

    /**
     * 重算应退金额和退款金额
     *
     * @param returnOrderRelations
     */
    public void reCalculateReturnAmt(List<OcBReturnOrderRelation> returnOrderRelations) {
        if (CollectionUtils.isNotEmpty(returnOrderRelations)) {
            returnOrderRelations.forEach(rel -> {
                if (Objects.nonNull(rel)) {
                    reCalculateReturnAmt(rel);
                }
            });
        }
    }

    /**
     * 不处理异常
     *
     * @param returnOrder
     * @param returnOrderRefunds
     */
    public boolean send4O2OByRetailReturnWithException(OcBReturnOrder returnOrder,
                                                       List<OcBReturnOrderRefund> returnOrderRefunds) {
        if (Objects.nonNull(returnOrder) && CollectionUtils.isNotEmpty(returnOrderRefunds)) {
            Long returnOrderId = returnOrder.getId();
            // 判断仓 -- 电商仓的退单
            boolean isPosWarehouse = isPosWarehouse(returnOrder.getCpCPhyWarehouseInId(), returnOrderId);

            if (isPosWarehouse) {
                // 组参数 -- 封装MQ发送参数
                RetailReturnOrder retailReturnOrder = buildRetailReturnOrder(returnOrder, returnOrderRefunds);

                if (Objects.nonNull(retailReturnOrder)) {
                    // 发送
                    return send4O2OByRetailReturn(retailReturnOrder);
                }
            }
        }

        return false;
    }


    /**
     * 构建发送参数对象
     *
     * @param returnOrder
     * @param returnOrderRefunds
     * @return
     */
    private RetailReturnOrder buildRetailReturnOrder(OcBReturnOrder returnOrder,
                                                     List<OcBReturnOrderRefund> returnOrderRefunds) {
        if (Objects.nonNull(returnOrder) && CollectionUtils.isNotEmpty(returnOrderRefunds)) {
            // 主表
            // 入库实体仓编码
            String cpCPhyWarehouseEcode = null;
            CpCPhyWarehouse warehouse = getCpCPhyWarehouse(returnOrder.getCpCPhyWarehouseInId());

            if (Objects.nonNull(warehouse)) {
                cpCPhyWarehouseEcode = warehouse.getEcode();
            }

            // 退单信息
            RetailReturnOrder retailReturnOrder = RetailReturnOrder.builder()
                    .refundId(returnOrder.getReturnId())
                    .origOrderId(returnOrder.getOrigOrderId())
                    .sourceCode(returnOrder.getOrigSourceCode())
                    .refundWarehouse(cpCPhyWarehouseEcode)
                    .creationdate(returnOrder.getCreationdate())
                    .inTime(returnOrder.getInTime())
                    .xdzdDm(returnOrder.getCpCShopEcode())
                    .build();

            // 原单信息
            fillOrigOrderInfo(retailReturnOrder, returnOrder.getOrigOrderId());

            // 明细表
            List<RetailReturnOrderItem> items = new ArrayList<>();

            returnOrderRefunds.forEach(refunds -> {
                RetailReturnOrderItem item = buildRetailReturnOrderItem(refunds);

                if (Objects.nonNull(item)) {
                    items.add(item);
                }
            });

            retailReturnOrder.setItem(items);

            // 返回
            return retailReturnOrder;
        }

        return null;
    }

    /**
     * 补充原单信息
     *
     * @param retailReturnOrder
     * @param origOrderId
     */
    private void fillOrigOrderInfo(RetailReturnOrder retailReturnOrder, Long origOrderId) {
        if (Objects.isNull(retailReturnOrder)) {
            return;
        }

        if (Objects.nonNull(origOrderId)) {
            // 查询原单
            OcBOrder order = omsOrderService.selectOrderInfo(origOrderId);

            if (Objects.nonNull(order)) {
                retailReturnOrder.setLyzdDm(order.getCpCShopEcode());
                retailReturnOrder.setLyzdMc(order.getCpCShopTitle());

                // 渠道信息
                String platform = Objects.isNull(order.getPlatform()) ? null : order.getPlatform().toString();
                String platformName = null;

                if (Objects.nonNull(platform)) {
                    platformName = CpRpcService.getInstance().queryPlatformName(platform);
                }

                retailReturnOrder.setLyorgDm(platform);
                retailReturnOrder.setLyorgMc(platformName);
            }
        }
    }

    /**
     * 构建明细
     *
     * @param returnOrderRefund
     * @return
     */
    private RetailReturnOrderItem buildRetailReturnOrderItem(OcBReturnOrderRefund returnOrderRefund) {
        if (Objects.nonNull(returnOrderRefund)) {
            Integer isGift = 0;
            String giftType = returnOrderRefund.getGiftType();

            if (Objects.nonNull(giftType) && !GiftTypeEnum.NO.getVal().equals(giftType)) {
                isGift = 1;
            }

            return RetailReturnOrderItem.builder()
                    .goodsCode(returnOrderRefund.getPsCSkuEcode())
                    .colorCode(returnOrderRefund.getPsCClrEcode())
                    .sizeCode(returnOrderRefund.getPsCSizeEcode())
                    .qty(Objects.isNull(returnOrderRefund.getQtyIn()) ? 0 : returnOrderRefund.getQtyIn().intValue())
                    .amtRefundSingle(returnOrderRefund.getAmtRefundSingle())
                    .amtRefund(returnOrderRefund.getAmtRefund())
                    .giftSign(isGift)
                    .build();
        }

        return null;
    }

    /**
     * 是否电商仓：是否POS管控仓
     *
     * @param cpCPhyWarehouseInId
     * @param logTag
     * @return
     */
    private boolean isPosWarehouse(Long cpCPhyWarehouseInId, Object logTag) {
        if (Objects.nonNull(cpCPhyWarehouseInId)) {
            CpCPhyWarehouse warehouse = getCpCPhyWarehouse(cpCPhyWarehouseInId);

            // TODO 是否pos管控仓
            return Objects.nonNull(warehouse);
        }

        return false;
    }

    /**
     * 查询实体仓
     *
     * @param cpCPhyWarehouseInId
     * @return
     */
    private CpCPhyWarehouse getCpCPhyWarehouse(Long cpCPhyWarehouseInId) {
        if (Objects.nonNull(cpCPhyWarehouseInId)) {
            return CpRpcService.getInstance().queryByWarehouseId(cpCPhyWarehouseInId);
        }

        return null;
    }

    /**
     * 获取实例
     *
     * @return
     */
    public static OmsReturnOrderService getInstance() {
        return ApplicationContextHandle.getBean(OmsReturnOrderService.class);
    }

    /**
     * 赠品后发
     * @param orderRelationList
     * @param isGiftOrderRelations
     * @param intermediateTableRelation
     * @param user
     */
    public void giftsThenSend(List<OmsOrderRelation> orderRelationList,List<OmsOrderRelation> isGiftOrderRelations,IntermediateTableRelation intermediateTableRelation,User user){
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("赠品订单取消orderRelationList:{}，isGiftOrderRelations:{},intermediateTableRelation:{};",
                    "OmsReturnOrderService"),JSON.toJSONString(orderRelationList),JSON.toJSONString(isGiftOrderRelations),JSON.toJSONString(intermediateTableRelation));
        }
        //赠品订单
        if(CollectionUtils.isEmpty(isGiftOrderRelations)){
            return;
        }
        List<String> giftRelations = new ArrayList<>();
        for(OmsOrderRelation omsOrderRelation:orderRelationList){
            List<OcBOrderItem> orderItems = omsOrderRelation.getOcBOrderItems();
            for(OcBOrderItem ocBOrderItem:orderItems){
                String giftRelation = ocBOrderItem.getGiftRelation();
                if(StringUtils.isNotBlank(giftRelation) && !giftRelations.contains(giftRelation)){
                    giftRelations.add(giftRelation);
                }
            }
        }
        for(OmsOrderRelation isGiftOrderRelation:isGiftOrderRelations) {
            OcBOrder ocBOrder = isGiftOrderRelation.getOcBOrder();
            Long id = ocBOrder.getId();
            // 根据零售发货单id 查询对应的退换货单是否已经存在 如果存在 则不需要再生成对应的退换货单
            List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectOrigOrderId(id);
            if (CollectionUtils.isNotEmpty(ocBReturnOrders)) {
                log.info("赠品订单取消，对应的退换货单已经存在，不需要再生成对应的退换货单，id:{}", id);
                continue;
            }
            Integer orderStatus = ocBOrder.getOrderStatus();
            if (OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus) || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) {
                continue;
            }
            List<OcBOrderItem> orderItemList = isGiftOrderRelation.getOcBOrderItems();
            boolean isCancelGift = false;
            for (OcBOrderItem item : orderItemList) {
                String giftRelation = item.getGiftRelation();
                if (StringUtils.isBlank(giftRelation) || giftRelations.contains(giftRelation) || CollectionUtils.isEmpty(giftRelations)) {
                    isCancelGift = true;
                    break;
                }
                if (ObjectUtil.isNotNull(item.getGiftType()) && ObjectUtil.equal("2", item.getGiftType())) {
                    isCancelGift = true;
                    break;
                }

            }
            if(!isCancelGift){
                continue;
            }
            String logContent = "赠品订单取消成功";
            if(OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus) || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus) || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)){
                //待分配或者未审核：取消赠品订单
                boolean isCancel = ocBOrderOffService.startCancelOrderByLock(user, id, OrderLogTypeEnum.ORDER_CANCLE.getKey(), logContent);
                if(!isCancel){
                    throw new NDSException("取消赠品订单失败！");
                }
            }else if(OmsOrderStatus.OCCUPY_IN.toInteger().equals(orderStatus)){
                throw new NDSException("赠品订单寻源中，取消失败！");
            } else if(OmsOrderStatus.CHECKED.toInteger().equals(orderStatus) || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)){
                //已审核或者配货中：先反审核再取消订单
                boolean isAuditSuccess = ocBOrderTheAuditService.updateOrderInfo(user,new ValueHolderV14(),id,false,1L,true);
                if(!isAuditSuccess){
                    throw new NDSException("赠品订单反审核失败！");
                }
                if(isAuditSuccess){
                    boolean isCancel = ocBOrderOffService.startCancelOrderByLock(user, id, OrderLogTypeEnum.ORDER_CANCLE.getKey(), logContent);
                    if(!isCancel){
                        throw new NDSException("取消赠品订单失败！");
                    }
                }
            }else if(OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus)){
                //待传WMS:打退款拦截标识，并执行退款HOLD逻辑
                // HOLD单
                OcBOrder holdOrder = new OcBOrder();
                //是否退款中
                holdOrder.setIsInreturning(1);
                holdOrder.setId(ocBOrder.getId());
                omsOrderService.updateOrderInfo(holdOrder);
                //是否已经拦截 Hold单统一调用 HOLD单方法
                holdOrder.setIsInterecept(1);
                holdOrder.setBillNo(ocBOrder.getBillNo());
                ocBOrderHoldService.holdOrUnHoldOrder(holdOrder, OrderHoldReasonEnum.REFUND_HOLD);
            }else if(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus) || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)){
                //查询是否已经生成了退换货单
                if(ES4ReturnOrder.isExistGiftReturnOrder(ocBOrder.getId())){
                    log.debug(LogUtil.format("当前赠品已存在退换货单orderId={};",
                            "OmsReturnOrderService"),ocBOrder.getId());
                    continue;
                }
                Integer returnBillsStatus = TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode();
                //仓库发货或者平台发货：赠品订单生成退换货单
                if(intermediateTableRelation.getIpBTaobaoRefund() != null){
                    Integer hasGoodReturn = intermediateTableRelation.getIpBTaobaoRefund().getHasGoodReturn();
                    if (TaobaoReturnOrderExt.HasGoodReturnStatus.NO_RETURN.getCode().equals(hasGoodReturn)) {
                        returnBillsStatus = TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode();
                    }
                    OcBOrderDelivery ocBOrderDelivery = null;
                    if(CollectionUtils.isNotEmpty(isGiftOrderRelation.getOrderDeliveries())){
                        ocBOrderDelivery = isGiftOrderRelation.getOrderDeliveries().get(0);
                    }
                    //淘宝退单
                    OcBReturnOrder ocBReturnOrder = taobaoRefundOrderTransferUtil.buildOcBReturnOrderFromTaobaoRefund(isGiftOrderRelation, intermediateTableRelation.getIpBTaobaoRefund(),
                            ocBOrderDelivery, returnBillsStatus, user);
                    List<OcBReturnOrderRefund> orderRefunds = taobaoRefundOrderTransferUtil.buildReturnOrderItemFromOid(isGiftOrderRelation, intermediateTableRelation.getIpBTaobaoRefund(), user, BigDecimal.ZERO);
                    List<OcBReturnOrderRelation> orderRelations = new ArrayList<>();
                    OcBReturnOrderRelation returnOrderRelation = new OcBReturnOrderRelation();
                    returnOrderRelation.setReturnOrderInfo(ocBReturnOrder);
                    returnOrderRelation.setOrderRefundList(orderRefunds);
                    orderRelations.add(returnOrderRelation);
                    insertOmsReturnOrderInfo(orderRelations, user);
                }else if(intermediateTableRelation.getIpBStandplatRefund() != null){
                    Integer refundType = intermediateTableRelation.getIpBStandplatRefund().getRefundType();
                    if(refundType == 1){
                        returnBillsStatus = TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode();
                    }
                    //通用订单
                    standplatRefundOrderTransferUtil.bulidGiftReturnOrder(isGiftOrderRelation,intermediateTableRelation.getIpBStandplatRefund(),null,returnBillsStatus,user);
                }
            }
        }
    }


    /**
     * 判断是否为赠品订单
     * @param ocBOrder
     * @return
     */
    public OmsOrderRelation checkTbIsGiftOrder(OcBOrder ocBOrder, String oid){
        Integer isHasgift = ocBOrder.getIsHasgift();
        Integer isSplit = ocBOrder.getIsSplit();
        if(isHasgift != null && isHasgift.equals(1) && isSplit != null && isSplit.equals(1)){
            //查询是否是赠品订单
            List<Long> ids = new ArrayList<>();
            ids.add(ocBOrder.getId());
            List<OcBOrderItem> orderItems= ocBOrderItemMapper.selectOrderItemsByOrderIdsAndOid(ids, oid);
            List<OcBOrderItem> allOrderItems = ocBOrderItemMapper.selectOrderItemsByOrderIds(ids);
            List<OcBOrderItem> isGiftList = orderItems.stream().filter(x -> x.getIsGift().equals(1)).collect(Collectors.toList());
            if (isGiftList.size() == allOrderItems.size()) {
                //赠品订单
                OmsOrderRelation omsOrderRelation = new OmsOrderRelation();
                omsOrderRelation.setOcBOrder(ocBOrder);
                omsOrderRelation.setOcBOrderItems(orderItems);
                return omsOrderRelation;
            }
        }
        return null;
    }

    /**
     * 判断是否为赠品订单
     * @param ocBOrder
     * @return
     */
    public OmsOrderRelation checkIsGiftOrder(OcBOrder ocBOrder){
        Integer isHasgift = ocBOrder.getIsHasgift();
        Integer isSplit = ocBOrder.getIsSplit();
        if(isHasgift != null && isHasgift.equals(1) && isSplit != null && isSplit.equals(1)){
            //查询是否是赠品订单
            List<Long> ids = new ArrayList<>();
            ids.add(ocBOrder.getId());
            List<OcBOrderItem> orderItems= ocBOrderItemMapper.selectOrderItemsByOrderIds(ids);
            List<OcBOrderItem> isGiftList = orderItems.stream().filter(x -> x.getIsGift().equals(1)).collect(Collectors.toList());
            if (isGiftList.size() == orderItems.size()) {
                //赠品订单
                OmsOrderRelation omsOrderRelation = new OmsOrderRelation();
                omsOrderRelation.setOcBOrder(ocBOrder);
                omsOrderRelation.setOcBOrderItems(orderItems);
                return omsOrderRelation;
            }
        }
        return null;
    }

    /**
     * 判断是否为赠品订单
     *
     * @param ocBOrder
     * @return
     */
    public OmsOrderRelation checkIsGiftItem(OcBOrder ocBOrder, Long oid) {
        Integer isHasgift = ocBOrder.getIsHasgift();
        Integer isSplit = ocBOrder.getIsSplit();
        if (isHasgift != null && isHasgift.equals(1) && oid != null) {
            //查询是否是赠品订单
            List<Long> ids = new ArrayList<>();
            ids.add(ocBOrder.getId());
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.queryOrderItemBySkuIdListWithGift(ids, oid + "");
            if (CollectionUtils.isEmpty(orderItems)){
                return null;
            }
            List<OcBOrderItem> isGiftList = orderItems.stream().filter(x -> x.getIsGift().equals(1)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(isGiftList)) {
                // 赠品订单
                OmsOrderRelation omsOrderRelation = new OmsOrderRelation();
                omsOrderRelation.setOcBOrder(ocBOrder);
                omsOrderRelation.setOcBOrderItems(isGiftList);
                return omsOrderRelation;
            }
        }
        return null;
    }

    public OmsOrderRelation checkIsSystemGiftOrder(OcBOrder ocBOrder) {
        Integer isHasgift = ocBOrder.getIsHasgift();
        Integer isSplit = ocBOrder.getIsSplit();
        if (isHasgift != null && isHasgift.equals(1) && isSplit != null && isSplit.equals(1)) {
            //查询是否是系统赠品订单
            List<Long> ids = new ArrayList<>();
            ids.add(ocBOrder.getId());
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemsByOrderIds(ids);
            List<OcBOrderItem> isGiftList = orderItems.stream().filter(x -> "1".equals(x.getGiftType())).collect(Collectors.toList());
            if (isGiftList.size() == orderItems.size()) {
                //赠品订单
                OmsOrderRelation omsOrderRelation = new OmsOrderRelation();
                omsOrderRelation.setOcBOrder(ocBOrder);
                omsOrderRelation.setOcBOrderItems(orderItems);
                return omsOrderRelation;
            }
        }
        return null;
    }

}
