package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.api.oms.SgOmsShareOutVoidCmd;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderJingdongSplitTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderJingdongSplitTask;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 京东自动拆单
 *
 * @author: 胡林洋
 * @since: 2019/9/19
 * create at : 2019/9/19 16:30
 */
@Component
@Slf4j
public class OmsOrderJdSplitService {

    @Autowired
    private JsonUtils jsonUtils;
    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderJingdongSplitTaskMapper jingdongSplitTaskMapper;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @DubboReference(group = "sg",version = "1.0")
    private SgOmsShareOutVoidCmd sgOmsShareOutVoidCmd;

    /**
     * 作废订单，并释放库存(调用库存中心作废逻辑发货单)
     * 接口变更 改成新的接口
     * @param
     * @return
     */
    public ValueHolderV14 voidSgBSend(OcBOrder ocBOrder) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>();
        if(ocBOrder.getOrderStatus().equals(OmsOrderStatus.UNCONFIRMED.toInteger())) {
            SgOmsShareOutRequest sgOmsShareOutRequest = new SgOmsShareOutRequest();
            sgOmsShareOutRequest.setSourceBillId(ocBOrder.getId());
            sgOmsShareOutRequest.setSourceBillNo(ocBOrder.getBillNo());
            sgOmsShareOutRequest.setTid(ocBOrder.getTid());
            sgOmsShareOutRequest.setCancelType(SgConstantsIF.OMS_STORAGE_OCCUPY_CANCEL_TYPE_MAIN);
            sgOmsShareOutRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
            sgOmsShareOutRequest.setLoginUser(SystemUserResource.getRootUser());
            log.debug("OrderId" + ocBOrder.getId() + "作废逻辑发货单调用库存中心接口入参：" + JSONObject.toJSONString(sgOmsShareOutRequest));
            valueHolderV14 = sgOmsShareOutVoidCmd.voidSgOmsShareOut(sgOmsShareOutRequest);
        }
        return valueHolderV14;
    }

    /**
     * 京东拆单
     */
    public void splitOrderByJingdong(OcBOrder orderInfo, List<OcBOrderRelation> jdOrderRelationList, User user) {
        String param = null;
        try {
            ValueHolderV14 vh = this.voidSgBSend(orderInfo);
            if (log.isDebugEnabled()) {
                log.debug("OmsOrderJdSplitService.splitOrderByJingdong,调用库存中心作废逻辑发货单接口出参={}", JSON.toJSONString(vh));
            }
            if (vh == null) {
                throw new NDSException("调用库存中心作废逻辑发货单失败");
            }
            //订单没作废的要先作废
            if (!OmsOrderStatus.SYS_VOID.toInteger().equals(orderInfo.getOrderStatus())){
                OcBOrder ocBOrderTmp = new OcBOrder();
                ocBOrderTmp.setId(orderInfo.getId());
                //订单状态
                ocBOrderTmp.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
                //修改时间
                ocBOrderTmp.setModifieddate(new Date());
                ocBOrderTmp.setModifierid(Long.valueOf(user.getId()));
                ocBOrderTmp.setModifiername(user.getName());
                //修改人
                ocBOrderTmp.setModifierename(user.getEname());
                //再次更新订单信息
                omsOrderService.updateOrderInfo(ocBOrderTmp);
            }
            param = bulidJdSplitOrderParam(jdOrderRelationList,
                    orderInfo.getId(), orderInfo.getSourceCode());
            OcBOrderJingdongSplitTask task = new OcBOrderJingdongSplitTask();
            task.setId(sequenceUtil.buildJingdongSplitOrderTaskId());
            task.setOcBOrderId(orderInfo.getId());
            task.setStatus(0);
            task.setTimes(0);
            task.setNextTime(new Date());
            task.setJdSplitParams(param);
            makeCreateField(task, user);
            task.setModifierename(user.getEname());
            task.setOwnerename(user.getEname());
            QueryWrapper<OcBOrderJingdongSplitTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(OcBOrderJingdongSplitTask::getOcBOrderId,orderInfo.getId());
            List<OcBOrderJingdongSplitTask> tasks = jingdongSplitTaskMapper.selectList(queryWrapper);
            if(CollectionUtils.isEmpty(tasks)){
                jingdongSplitTaskMapper.insert(task);
            } else {
                task.setId(null);
                task.setOcBOrderId(null);
                task.setTimes(null);
                makeModiferField(task, user);
                task.setModifierename(user.getEname());
                jingdongSplitTaskMapper.update(task, queryWrapper);
                }
           // }
            try {
                omsOrderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(),
                        OrderLogTypeEnum.ORDER_SPLIT.getKey(), "京东自动拆单，订单作废", param, "", user);
            } catch (Exception e) {
                log.error("京东拆单记录日志报错,orderId={}，异常信息={}", orderInfo.getId(), e);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("京东拆单失败,异常信息为:{}", orderInfo.getId()), Throwables.getStackTraceAsString(e));

            throw new NDSException(e.getMessage());
        }
    }

    /***
     * 查询待调用京东拆单的订单
     * @param nodeName
     * @param taskTableName
     * @param limit
     * @param splitTimes
     * @return
     */
    public List<OcBOrderJingdongSplitTask> getJingdongSplitTaskList(String nodeName, String taskTableName,
                                                                    int limit, int splitTimes) {
        return jingdongSplitTaskMapper.selectTaskIdList(nodeName, limit, taskTableName, splitTimes);
    }

    /***
     * 调用京东拆单接口
     * @param taskList
     * @return
     */
    public void sendPreSplitOrderToJingdong(List<OcBOrderJingdongSplitTask> taskList) {
        User user = SystemUserResource.getRootUser();
        if (CollectionUtils.isNotEmpty(taskList)) {
            for (OcBOrderJingdongSplitTask task : taskList) {
                OcBOrder order = omsOrderService.selectOrderInfo(task.getOcBOrderId());
                if(order == null || !OmsOrderStatus.SYS_VOID.toInteger().equals(order.getOrderStatus())){
                    updateJingdongSplitTask(user, task, null, "原单非作废状态,不允许进行平台拆单", 1);
                    continue;
                }
                JSONObject jsonObject = JSON.parseObject(task.getJdSplitParams());
                try {
                    ValueHolderV14<String> vh = ipRpcService.requestJdSplit(order.getId(), JsonUtils.json2xml(jsonObject),
                            order.getCpCShopSellerNick());
                    if (vh != null && vh.isOK()) {
                        updateJingdongSplitTask(user, task, JSON.toJSONString(vh), "调用京东平台拆单成功", 1);
                    } else {
                        updateJingdongSplitTask(user, task, JSON.toJSONString(vh), null, 2);
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("调用京东拆单接口失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    updateJingdongSplitTask(user, task, null, e.getMessage(), 2);
                }
            }
        }
    }

    /***
     * 更新京东平台拆单任务表
     * @param user
     * @param task
     * @param result
     * @param remark
     * @param status
     */
    private void updateJingdongSplitTask(User user, OcBOrderJingdongSplitTask task, String result,
                                         String remark, int status) {
        task.setOcBOrderId(null);
        task.setTimes(Optional.ofNullable(task.getTimes()).orElse(0) + 1);
        task.setStatus(status);
        task.setJdResult(result);
        task.setRemark(SplitMessageUtil.splitMesssage(remark));
        Date nextTime = Date.from(LocalDateTime.now()
                .plusMinutes(3)
                .atZone(ZoneId.systemDefault()).toInstant());
        task.setNextTime(nextTime);
        makeModiferField(task, user);
        task.setModifierename(user.getEname());
        jingdongSplitTaskMapper.updateById(task);
    }

    /**
     * 构建京东拆单接口参数
     *
     * @param jdOrderRelationList
     * @param orderId
     * @param sourceCode
     * @return
     */
    public String bulidJdSplitOrderParam(List<OcBOrderRelation> jdOrderRelationList, Long orderId, String sourceCode) {
        if (CollectionUtils.isEmpty(jdOrderRelationList) || jdOrderRelationList.size() == 1) {
            throw new NDSException("京东拆单失败!");
        }
        JSONObject jsonObject = new JSONObject();
        JSONObject jdSplitParam = new JSONObject();
        jdSplitParam.put("orderId", sourceCode);
        //systemName
        jdSplitParam.put("systemName", "poporderjos");
        jdSplitParam.put("systemId", "42e755522c2ca734fc35983c4692380d");
        jdSplitParam.put("type", "11");
        JSONArray jsonArratSkuGroup = new JSONArray();
        List<OcBOrderItem> combinationItems = orderItemMapper.selectOrderItemListCombinationExecPm(orderId);
        Map<String,OcBOrderItem> combinationMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(combinationItems)){
            for(OcBOrderItem item : combinationItems){
                if(item.getProType()!=null && item.getProType().intValue() == SkuType.NO_SPLIT_COMBINE){
                    combinationMap.put(item.getGiftbagSku(), item);
                }
            }
        }
        for (OcBOrderRelation ocBOrderRelation : jdOrderRelationList) {
            JSONObject jsonObjectSkuGroup = new JSONObject();
            JSONObject jsonObjectSkuGroupValue = new JSONObject();
            JSONArray jsonObjectSkuInfoList = new JSONArray();

            List<String> groupGoodsMarkList = ocBOrderRelation.getOrderItemList()
                    .stream().filter(item -> Long.valueOf(SkuType.COMBINE_PRODUCT).equals(item.getProType())
                    || Long.valueOf(SkuType.GIFT_PRODUCT).equals(item.getProType()))
                    .map(item -> item.getGiftbagSku())
                    .distinct().collect(Collectors.toList());
            List<OcBOrderItem> noSplitItems = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(groupGoodsMarkList)){
                for(String key : groupGoodsMarkList){
                    noSplitItems.add(combinationMap.get(key));
                }
            }
            if(CollectionUtils.isNotEmpty(noSplitItems)){
                ocBOrderRelation.getOrderItemList().addAll(noSplitItems);
            }

            for (OcBOrderItem ocBOrderItem : ocBOrderRelation.getOrderItemList()) {
                if (ocBOrderItem == null) {
                    continue;
                }
                // 是否中台赠品
                boolean isOmsGift = OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(ocBOrderItem.getIsGift())
                        && "1".equals(ocBOrderItem.getGiftType());
                // 排除 福袋和组合商品的子明细，排除中台赠品
                if(isOmsGift || Long.valueOf(SkuType.COMBINE_PRODUCT).equals(ocBOrderItem.getProType())
                        || Long.valueOf(SkuType.GIFT_PRODUCT).equals(ocBOrderItem.getProType())){
                    continue;
                }
                JSONObject jsonObjectSkuInfo = new JSONObject();
                JSONObject jsonObjectSkuInfoValue = new JSONObject();
                jsonObjectSkuInfoValue.put("skuId", ocBOrderItem.getOoid());
                jsonObjectSkuInfoValue.put("num", ocBOrderItem.getQty().intValue());
                jsonObjectSkuInfo.put("SkuInfo", jsonObjectSkuInfoValue);
                jsonObjectSkuInfoList.add(jsonObjectSkuInfo);
            }
            if (CollectionUtils.isNotEmpty(jsonObjectSkuInfoList)){
                jsonObjectSkuGroupValue.put("skuInfoList", jsonObjectSkuInfoList);
                jsonObjectSkuGroup.put("SkuGroup", jsonObjectSkuGroupValue);
                jsonArratSkuGroup.add(jsonObjectSkuGroup);
            }
        }
        jdSplitParam.put("skuGroups", jsonArratSkuGroup);
        jdSplitParam.put("code", 1);
        jdSplitParam.put("desc", "中台拆单");
        jsonObject.put("OrderSplitApplyParam", jdSplitParam);
        return jsonObject.toJSONString();
    }

    private void makeCreateField(BaseModel model, User user) {
        Date date = new Date();
        model.setAdClientId((long) user.getClientId());//所属公司
        model.setAdOrgId((long) user.getOrgId());//所属组织
        model.setOwnerid(Long.valueOf(user.getId()));//创建人id
        model.setCreationdate(date);//创建时间
        model.setOwnername(user.getName());//创建人用户名
        model.setModifierid(Long.valueOf(user.getId()));//修改人id
        model.setModifiername(user.getName());//修改人用户名
        model.setModifieddate(date);//修改时间
        model.setIsactive("Y");//是否启用
    }

    private void makeModiferField(BaseModel model, User user) {
        Date date = new Date();
        model.setModifierid(Long.valueOf(user.getId()));//修改人id
        model.setModifiername(user.getName());//修改人用户名
        model.setModifieddate(date);//修改时间
    }
}