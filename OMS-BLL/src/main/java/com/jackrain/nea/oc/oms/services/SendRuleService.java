package com.jackrain.nea.oc.oms.services;

import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.jackrain.nea.oc.oms.model.SendPlanExecution;
import com.jackrain.nea.st.model.table.StCSendRuleDO;
import com.jackrain.nea.st.service.SendPlanRuleQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-06-17
 * @desc 派单规则
 **/
@Component
@Slf4j
public class SendRuleService {

    private List<DispatchSendRule> dispatchSendRuleList;

    @Autowired(required = false)
    public void setDispatchSendRuleList(List<DispatchSendRule> dispatchSendRuleList) {
        this.dispatchSendRuleList = dispatchSendRuleList;
    }

    @Autowired
    private SendPlanRuleQueryService sendPlanRuleQueryService;

    /**
     * 执行派单规则
     *
     * @param sendPlanExecution
     * @param storageQueryResults
     * @param sendRuleId
     * @return
     */
    public Long execute(SendPlanExecution sendPlanExecution, List<SgSumStorageQueryResult> storageQueryResults, Long sendRuleId) {
        //派单规则查询
        StCSendRuleDO stCSendRuleDO = sendPlanRuleQueryService.selectSendRuleBySendRuleId(sendRuleId);
        return dispatch(sendPlanExecution, storageQueryResults, stCSendRuleDO);
    }

    /**
     * 派单规则调度开始
     *
     * @param stCSendRuleDO
     * @return
     */
    private Long dispatch(SendPlanExecution sendPlanExecution, List<SgSumStorageQueryResult> storageQueryResults, StCSendRuleDO stCSendRuleDO) {
        if (CollectionUtils.isNotEmpty(dispatchSendRuleList)) {
            for (DispatchSendRule dispatchSendRule : dispatchSendRuleList) {
                if (dispatchSendRule.support(stCSendRuleDO)) {
                    return dispatchSendRule.execute(sendPlanExecution, storageQueryResults, stCSendRuleDO);
                }
            }
        }
        return null;
    }
}
