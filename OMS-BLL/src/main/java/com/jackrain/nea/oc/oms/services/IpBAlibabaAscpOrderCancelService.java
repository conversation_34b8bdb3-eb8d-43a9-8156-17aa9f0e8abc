package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.ascp.ConsignOrderCancelFeedbackModel;
import com.jackrain.nea.oc.oms.es.ES4IpAlibabaAscpCancelOrder;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.IpBAlibabaAscpOrderCancelMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderIsInterceptEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderCancelRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderCancel;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: 中间表猫超直发取消订单转单服务
 * @author: xtt
 * @date: 2020-09-04 11:54
 **/
@Component
@Slf4j
public class IpBAlibabaAscpOrderCancelService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private IpBAlibabaAscpOrderCancelMapper ipBAlibabaAscpOrderCancelMapper;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;
    @Autowired
    private MarkRefundService markRefundService;
    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;
    @Autowired
    private OcBReturnOrderBatchAddService batchAddService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    /**
     * 猫超直发取消订单 转单 处理逻辑
     *
     * @param orderInfo
     * @param user
     */
    public void tAlibabaAscpOrderCancel(IpBAlibabaAscpOrderCancelRelation orderInfo, User user) {
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " .tAlibabaAscpOrderCancel orderInfo=" + JSONObject.toJSONString(orderInfo));
        }
        IpBAlibabaAscpOrderCancel ipBAlibabaAscpOrderCancel = orderInfo.getIpBAlibabaAscpOrderCancel();
        Long id = orderInfo.getOcBOrder().getId();
        tradeCanceled(ipBAlibabaAscpOrderCancel, id, user);
    }

    /**
     * 交易状态为LOCKED的处理方法
     *
     * @param ipBAlibabaAscpOrderCancel 接口平台订单表
     * @param orderId                   全渠道订单集
     */
    private void tradeCanceled(IpBAlibabaAscpOrderCancel ipBAlibabaAscpOrderCancel, Long orderId, User user) {
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + ".locked orderId=" + orderId
                    + ",ipBAlibabaAscpOrderCancel=" + JSONObject.toJSONString(ipBAlibabaAscpOrderCancel));
        }
        Long shopid = null;
        String tid = ipBAlibabaAscpOrderCancel.getBizOrderCode();
        int retractNum = 0;//撤回失败记录
        int statusNum = 0;//"已审核"、"待分配"、"传WMS中" 记录
        int closeNum = 0;//"已取消"、"系统作废" 记录
        int succNum = 0;//"待审核"、"缺货" 或者（配货中且WMS撤回状态为已撤回） 记录
        int redisNum = 0;//其他人操作记录
        int errNum = 0;//转单异常
        int rtnNum = 0;//生成退单 记录

        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                shopid = ocBOrder.getCpCShopId();
                Integer orderStatus = ocBOrder.getOrderStatus();
                Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
                //原单状态 初始状态"待分配"、"缺货" 作废原单
                if (orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())) {
                    //更新订单明细的退款状态,根据平台单号将明细表中同平台单号的明细退款状态修改为1
                    //订单主表更新,是否退款中”：退款中
                    updateOrderAndItem(tid, orderId, user);
                    // 取消成功
                    updateWarehouseStatus(ipBAlibabaAscpOrderCancel, ocBOrder, null, true, user);

                }
                //订单状态 为 "订单取消"、"系统作废"
                else if (orderStatus.equals(OmsOrderStatus.CANCELLED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.SYS_VOID.toInteger())) {
                    // 取消成功
                    updateWarehouseStatus(ipBAlibabaAscpOrderCancel, ocBOrder, null, true, user);
                    //订单状态："仓库发货"、"平台发货" 取消失败
                } else if (orderStatus.equals(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())
                        || orderStatus.equals(OmsOrderStatus.PLATFORM_DELIVERY.toInteger())) {
                    // 取消失败
                    updateWarehouseStatus(ipBAlibabaAscpOrderCancel, ocBOrder, "商品已发货，取消失败", false, user);

                    //订单状态："已审核"、"传WMS中" 、"配货中"取消失败
                } else if (orderStatus.equals(OmsOrderStatus.CHECKED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger())
                        || orderStatus.equals(OmsOrderStatus.PENDING_WMS.toInteger())) {
                    // 反审核
                    //调用作废出库通知单并从wms撤回接口
                    boolean flag = this.toExamineOrder(ocBOrder, user);
                    if (log.isDebugEnabled()) {
                        log.debug(this.getClass().getName() + ".locked 反审核返回flag={},订单id={},原单数据={}", flag, orderId, JSONObject.toJSONString(ocBOrder));
                    }
                    if (flag) {
                        ocBOrder.setWmsCancelStatus(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger());
                        updateOrderAndItem(tid, orderId, user);
                        // 1.1、反审核成功 1.2 作废订单、 取消成功
                        updateWarehouseStatus(ipBAlibabaAscpOrderCancel, ocBOrder, null, true, user);

                    } else {
                        // 2.1、反审核失败 2.2、 取消失败
                        updateWarehouseStatus(ipBAlibabaAscpOrderCancel, ocBOrder, "平台反审核失败，取消失败", false, user);
                    }
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(" 订单id=" + orderId + this.getClass().getName() + ".locked 当前订单其他人在操作!");
                }
                redisNum++;
            }
        } catch (Exception ex) {
            errNum++;
            log.error(" 订单id=" + orderId + this.getClass().getName() + ".locked 异常：" + ex);
        } finally {
            redisLock.unlock();
        }

        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + ".locked errNum=" + errNum + ",redisNum=" + redisNum + ",retractNum="
                    + retractNum + ",statusNum=" + statusNum + ",rtnNum=" + rtnNum + ",closeNum="
                    + closeNum + ",succNum=" + succNum + ",orderId=" + orderId);
        }
    }

    /**
     * 交易状态为TRADE_CANCELED的处理方法
     *
     * @param saRefund 接口平台订单表
     * @param orderIds 全渠道订单集
     * @param user     用户
     */
    private void tradeCanceled(IpBAlibabaAscpOrderCancel saRefund, List<Long> orderIds, User user) {
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + ".tradeCanceled orderIds=" + JSONObject.toJSONString(orderIds)
                    + ",saRefund=" + JSONObject.toJSONString(saRefund));
        }
        String tid = saRefund.getBizOrderCode();
        int retractNum = 0;//撤回失败记录
        int statusNum = 0;//"已审核"、"待分配"、"传WMS中" 记录
        int closeNum = 0;//"已取消"、"系统作废" 记录
        int refundErrNum = 0;//标记为退款完成服务失败 记录
        int errNum = 0;//转单异常
        int rtnNum = 0;//生成退单 记录
        for (int i = 0; i < orderIds.size(); i++) {
            Long orderId = orderIds.get(i);
            try {
                OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                Integer orderStatus = ocBOrder.getOrderStatus();
                Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
                //订单状态 为 "已审核"、"待分配"、"传WMS中",不拦截
                if (!orderStatus.equals(OmsOrderStatus.TO_BE_ASSIGNED.toInteger())
                        && !orderStatus.equals(OmsOrderStatus.PENDING_WMS.toInteger())) {
                    //订单拦截，更新主表：是否已经拦截：是，修改人,修改时间，插入订单日志
                    interceptAndLog(ocBOrder);
                }
                if (log.isDebugEnabled()) {
                    log.debug(" 订单id=" + orderId + ",IpBAlibabaAscpOrderCancelService.tradeCanceled ocBOrder=" + JSONObject.toJSONString(ocBOrder));
                }
                //订单状态”为配货中且”WMS撤回状态”不是已撤回，则调用WMS撤回服务
                if (orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger())
                        && orderStatus.equals(OmsOrderStatus.CHECKED.toInteger())
                        && !wmsCancelStatus.equals(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger())) {
                    //调用作废出库通知单并从wms撤回接口
                    boolean flag = this.lockUpBackExamine(ocBOrder, user);
                    if (log.isDebugEnabled()) {
                        log.debug(" 订单id=" + orderId + ",IpBAlibabaAscpOrderCancelService.tradeCanceled 反审核返回flag" + flag + ",订单=" + JSONObject.toJSONString(ocBOrder));
                    }
                    if (flag) {
                        wmsCancelStatus = OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger();
                    } else {
                        retractNum++;
                    }
                }
                //订单状态 "待审核"、"缺货" 或者（配货中且WMS撤回状态为已撤回）
                if (orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())
                        || (orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger())
                        && (wmsCancelStatus.equals(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger())))) {
                    //调用 标记为退款完成服务
                    boolean issucc = markRefund(tid, orderId, user);
                    if (issucc) {
                        //更新主表数据,是否已经拦截”：0、“是否退款中”：0
                        OcBOrder updateOrder = new OcBOrder();
                        updateOrder.setId(orderId);
                        updateOrder.setIsInreturning(InreturningStatus.INRETURN_NO);
                        returnOrderTransferUtil.updateOperator(updateOrder, user);
                        ocBOrderMapper.updateById(updateOrder);

                        //订单hold 或释放hold单调用HOLD单接口
                        updateOrder.setIsInterecept(OmsOrderIsInterceptEnum.NO.getVal());
                        ocBOrderHoldService.holdOrUnHoldOrder(updateOrder, OrderHoldReasonEnum.REFUND_HOLD);

                        orderStatus = OmsOrderStatus.CANCELLED.toInteger();
                    } else {
                        refundErrNum++;
                    }
                }
                //订单状态 为 "已审核"、"待分配"、"传WMS中"
                if (orderStatus.equals(OmsOrderStatus.CHECKED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.TO_BE_ASSIGNED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.PENDING_WMS.toInteger())) {
                    statusNum++;
                }
                //订单状态 为 "仓库发货"、"平台发货"、"交易完成"、"物流已送达"
                if (orderStatus.equals(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())
                        || orderStatus.equals(OmsOrderStatus.PLATFORM_DELIVERY.toInteger())
                        || orderStatus.equals(OmsOrderStatus.DEAL_DONE.toInteger())
                        || orderStatus.equals(OmsOrderStatus.DELIVERED.toInteger())) {
                    //新增退换货订单服务
                    insertRefundOrder(orderId, ocBOrder.getBillNo(), tid, user);
                    rtnNum++;
                }
                //订单状态 为 "已取消"、"系统作废"
                if (orderStatus.equals(OmsOrderStatus.CANCELLED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.SYS_VOID.toInteger())) {
                    closeNum++;
                }
            } catch (Exception ex) {
                errNum++;
                log.error(" 订单id=" + orderId + ",IpBAlibabaAscpOrderCancelService.tradeCanceled 异常：" + ex);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(" IpBAlibabaAscpOrderCancelService.tradeCanceled errNum=" + errNum + ",retractNum=" + retractNum
                    + ",statusNum=" + statusNum + ",rtnNum=" + rtnNum + ",closeNum=" + closeNum + ",refundErrNum=" + refundErrNum
                    + ",orderIds=" + orderIds.size());
        }
        if (errNum > 0) {
            //转单保存异常，标记 未转换     备注：系统异常,转换失败
            this.updateOrderCancel(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK0, saRefund);
            return;
        }
        if (retractNum > 0) {
            //wms撤回失败，标记 未转换     备注：WMS撤回失败,请等待下次转换
            this.updateOrderCancel(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK45, saRefund);
            return;
        }
        if (statusNum > 0) {
            //有已审核、待分配、传wms中，标记 未转换     备注：订单状态为已审核、待分配或传WMS中，请等待下次转换
            this.updateOrderCancel(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK60, saRefund);
            return;
        }
        if (refundErrNum > 0) {
            //执行标记退款服务失败，标记 未转换     备注：标记退款完成服务失败，请等待下次转换
            int markRefundCount = saRefund.getMarkRefundCount() == null ? 0 : saRefund.getMarkRefundCount();
            int istrans = TransferOrderStatus.NOT_TRANSFER.toInteger();
            String sysRemark = SysNotesConstant.SYS_REMARK62;
            if (markRefundCount >= 3) {
                //执行退款服务失败3次，标记 已转换     备注：执行退款完成服务失败3次，需人为操作，标记已转换
                istrans = TransferOrderStatus.TRANSFERRED.toInteger();
                sysRemark = SysNotesConstant.SYS_REMARK66;
            }
            saRefund.setMarkRefundCount(markRefundCount + 1);
            this.updateOrderCancel(istrans, sysRemark, saRefund);
            return;
        }
        if (rtnNum > 0) {
            //有仓库发货、平台发货、交易完成、物流已送达，标记 已转换     备注：订单已发货，待退货
            this.updateOrderCancel(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK65, saRefund);
            return;
        }
        if (closeNum > 0 && closeNum == orderIds.size()) {
            //全部为 已取消、系统作废,标记 已转换     备注：买家申请退款，转换完成
            this.updateOrderCancel(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK17, saRefund);
        }
    }

    /**
     * 交易状态为WAIT_SELLER_STOCK_OUT的处理方法
     *
     * @param saRefund 接口平台订单表
     * @param orderIds 全渠道订单集
     * @param user     用户
     */
    private void waitSellerStockOut(IpBAlibabaAscpOrderCancel saRefund, List<Long> orderIds, User user) {
        if (log.isDebugEnabled()) {
            log.debug(" IpBAlibabaAscpOrderCancelService.waitSellerStockOut orderIds=" + JSONObject.toJSONString(orderIds)
                    + ",saRefund=" + JSONObject.toJSONString(saRefund));
        }
        String tid = saRefund.getBizTime();
        int statusNum = 0;///"待审核"、"已审核"、"待分配"、"传WMS中"、"缺货" 或者（配货中且WMS撤回状态为已撤回） 记录
        int closeNum = 0;//"已取消"、"系统作废" 记录
        int refundFailNum = 0;//取消退单失败 记录
        int errNum = 0;//转单异常
        int succ = 0;
        for (int i = 0; i < orderIds.size(); i++) {
            Long orderId = orderIds.get(i);
            try {
                OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                Integer orderStatus = ocBOrder.getOrderStatus();
                Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
                if (log.isDebugEnabled()) {
                    log.debug(" 订单id=" + orderId + ",IpBAlibabaAscpOrderCancelService.waitSellerStockOut ocBOrder=" + JSONObject.toJSONString(ocBOrder));
                }
                //订单状态 为 "仓库发货"、"平台发货"、"交易完成"、"物流已送达"
                if (orderStatus.equals(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())
                        || orderStatus.equals(OmsOrderStatus.PLATFORM_DELIVERY.toInteger())
                        || orderStatus.equals(OmsOrderStatus.DEAL_DONE.toInteger())
                        || orderStatus.equals(OmsOrderStatus.DELIVERED.toInteger())) {
                    boolean flag = this.cancelRefundOrder(tid, orderId, user);
                    succ++;
                    if (log.isDebugEnabled()) {
                        log.debug(" 订单id=" + orderId + ",IpBAlibabaAscpOrderCancelService.waitSellerStockOut 查询退单并取消返回flag" + flag);
                    }
                    if (!flag) {
                        refundFailNum++;
                    }
                }
                //订单状态 "待审核"、"已审核"、"待分配"、"传WMS中"、"缺货" 或者（配货中且WMS撤回状态为已撤回）
                if (orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.CHECKED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.TO_BE_ASSIGNED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.PENDING_WMS.toInteger())
                        || orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())
                        || (orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger())
                        && (wmsCancelStatus.equals(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger())))) {
                    //则更新订单管理明细商品退款状态更新为0，记录操作日志，
                    //判断是否存在退款状态为1的商品,不存在 取消拦截，订单退款状态更新为0，
                    updateOrderItem(tid, orderId, ocBOrder.getBillNo(), user);
                    statusNum++;
                }
                //订单状态 为 "已取消"、"系统作废"
                if (orderStatus.equals(OmsOrderStatus.CANCELLED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.SYS_VOID.toInteger())) {
                    closeNum++;
                }
            } catch (Exception ex) {
                errNum++;
                log.error(" 订单id=" + orderId + ",IpBAlibabaAscpOrderCancelService.waitSellerStockOut 异常：" + ex);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(" IpBAlibabaAscpOrderCancelService.waitSellerStockOut errNum=" + errNum + ",refundFailNum=" + refundFailNum
                    + ",statusNum=" + statusNum + ",succ=" + succ + ",closeNum=" + closeNum + ",orderIds=" + orderIds.size());
        }
        if (errNum > 0) {
            //转单保存异常，标记 未转换     备注：系统异常,转换失败
            this.updateOrderCancel(TransferOrderStatus.NOT_TRANSFER.toInteger(), SysNotesConstant.SYS_REMARK0, saRefund);
            return;
        }
        if (refundFailNum > 0) {
            //有"仓库发货"、"平台发货"、"交易完成"、"物流已送达"  取消退单失败
            //标记 已转换     备注：退货单的状态不满足，不能取消退货单
            this.updateOrderCancel(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK63, saRefund);
            return;
        }
        if (statusNum > 0 || succ > 0) {
            //有"待审核"、"已审核"、"待分配"、"传WMS中"、"缺货" 或者（配货中且WMS撤回状态为已撤回）
            //有"仓库发货"、"平台发货"、"交易完成"、"物流已送达"  无退单或退单取消成功
            //标记 已转换     备注：转换成功
            this.updateOrderCancel(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK67, saRefund);
            return;
        }
        if (closeNum > 0) {
            //全部为 已取消、系统作废,标记 已转换     备注：订单已取消、系统作废，不能撤销
            this.updateOrderCancel(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK64, saRefund);
        }
    }

    /**
     * 调用执行标记退款服务
     *
     * @param tid
     * @param id  全渠道订单id
     */
    private boolean markRefund(String tid, Long id, User user) {
        List<Long> itemIds = ocBOrderItemMapper.queryIdByTidAndOrderId(tid, id);
        if (CollectionUtils.isEmpty(itemIds)) {
            return false;
        } else {
            String join = StringUtils.join(itemIds, ",");
            JSONObject ids = new JSONObject();
            ids.put("IDS", join);
            try {
                ValueHolderV14 v14 = markRefundService.markRefund(ids, user);
                if (log.isDebugEnabled()) {
                    log.debug("订单id=" + id + ",IpBAlibabaAscpOrderCancelService.markRefund 调用执行标记退款服务返回" + JSONObject.toJSONString(v14));
                }
                if (!v14.isOK()) {
                    return false;
                }
            } catch (Exception e) {
                log.debug("订单id=" + id + ",IpBAlibabaAscpOrderCancelService.markRefund 异常" + e.getMessage());
                return false;
            }
        }
        return true;
    }

    /**
     * 配货中加锁调用反审核
     *
     * @param ocBOrder
     * @param user
     */
    private Boolean lockUpBackExamine(OcBOrder ocBOrder, User user) {
        boolean isSuccess = true;
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                isSuccess = this.toExamineOrder(ocBOrder, user);
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(" 订单id=" + ocBOrder.getId() + ",IpBAlibabaAscpOrderCancelService.lockUpBackExamine 当前订单其他人在操作!");
                }
                isSuccess = false;
            }
        } catch (Exception e) {
            log.debug(" 订单id=" + ocBOrder.getId() + ",IpBAlibabaAscpOrderCancelService.lockUpBackExamine 异常" + e.getMessage());
            isSuccess = false;
        } finally {
            redisLock.unlock();
        }
        return isSuccess;
    }

    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    private boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            if (isSuccess) {
                //反审核埋点
                ocBOrder.setExamineOrderDate(new Date());
                ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.EXAMINE_ORDER_DATE,new Date(),id,user);
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用反审核失败", e);
            return false;
        }
    }


    /**
     * 调用新增退换货订单
     *
     * @param orderId
     * @param billNo
     * @param tid
     * @param user
     */
    private void insertRefundOrder(Long orderId, String billNo, String tid, User user) {
        if (log.isDebugEnabled()) {
            log.debug(" IpBAlibabaAscpOrderCancelService.insertRefundOrder tid=" + tid + ",orderId=" + orderId + ",billNo=" + billNo);
        }
        try {
            //查询退换货订单是否存在，不存在，调用新增服务
            Long id = isExistReturnOrderByOrderIdAndTid(orderId, tid);
            if (null == id || id <= 0) {
                List<String> listString = new ArrayList<>();
                try {
                    batchAddService.addReturnOrder(orderId, 0, listString, user, "猫超直发取消订单新增退换货单",tid,null);
                } catch (Exception e) {
                    log.error(this.getClass().getName() + " 订单id=" + orderId + ",猫超直发取消订单,调用新增退单服务失败！异常:{}", e.getMessage());
                }
                if (listString.size() > 0) {
                    //新增退单添加订单操作日志
                    omsOrderLogService.addUserOrderLog(id, billNo, OrderLogTypeEnum.REFUND_ORDER_ADD.getKey(),
                            listString.get(0), null, null, user);
                }
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 订单id=" + orderId + ",猫超直发取消订单新增退单失败！异常:{}", e.getMessage());
        }
    }

    /**
     * 取消退换货订单
     *
     * @param tid
     * @param orderId
     * @param user
     * @return
     */
    private Boolean cancelRefundOrder(String tid, Long orderId, User user) {
        if (log.isDebugEnabled()) {
            log.debug(" IpBAlibabaAscpOrderCancelService.checkRefundDoesIsExist tid=" + tid + ",orderid=" + orderId);
        }
        boolean isSuccess = true;
        Long id = isExistReturnOrderByOrderIdAndTid(orderId, tid);
        if (id != null && id > 0) {
            OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByid(id);
            if (null != returnOrder && returnOrder.getReturnStatus() != null
                    && returnOrder.getReturnStatus().equals(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal())) {
                //这里调用取消退换货订单
                JSONObject jsonObject = new JSONObject();
                JSONArray jsonArray = new JSONArray();
                jsonArray.add(id);
                jsonObject.put("ids", jsonArray);
                ValueHolderV14 v14 = ocCancelChangingOrRefundService.orRefundService(jsonObject, user, Boolean.FALSE);
                if (log.isDebugEnabled()) {
                    log.debug(" 订单id=" + orderId + ",IpBAlibabaAscpOrderCancelService.cancelRefundOrder 取消退换货单返回" + JSONObject.toJSONString(v14));
                }
                if (!v14.isOK()) {
                    isSuccess = false;
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(" 订单id=" + orderId + ",IpBAlibabaAscpOrderCancelService.cancelRefundOrder 退单信息" + JSONObject.toJSONString(returnOrder));
                }
                isSuccess = false;
            }
        }
        return isSuccess;
    }

    /**
     * 根据oid和tid查询退换货单id
     *
     * @param
     * @return
     */
    private Long isExistReturnOrderByOrderIdAndTid(Long orderId, String tid) {
        return ES4ReturnOrder.findIdByOrderIdAndTid(orderId, tid);
    }

    /**
     * 调用【猫超直发取消订单更新出库状态接口】
     *
     * @param saRefund
     * @param ocBOrder
     * @param reason         取消失败，才需填写理由
     * @param isCancelStatus true 取消成功，false 取消失败
     * @param user
     */
    private void updateWarehouseStatus(IpBAlibabaAscpOrderCancel saRefund, OcBOrder ocBOrder, String reason, boolean isCancelStatus, User user) {
        ConsignOrderCancelFeedbackModel cancelFeedbackModel = new ConsignOrderCancelFeedbackModel();
        cancelFeedbackModel.setSupplierId(saRefund.getSupplierId());
        cancelFeedbackModel.setBizOrderCode(saRefund.getBizOrderCode());
        cancelFeedbackModel.setBizTime(new Date());
        cancelFeedbackModel.setCancelResult(isCancelStatus);
        cancelFeedbackModel.setCancelReason(reason);

        try {
            ValueHolderV14 v14 = ipRpcService.orderCancelFeedback(cancelFeedbackModel, saRefund.getSellerNick());
            if (!v14.isOK()) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ALIBABA_ASCP_CANCEL_DELIVER_GOODS_REPLY.getKey(), "调用猫超取消回告接口失败", "", "", user);
                long transCount = saRefund.getTransCount() == null ? 0 : saRefund.getTransCount();
                if (transCount >= 5) {
                    this.updateOrderCancel(TransferOrderStatus.TRANSFERRED.toInteger(), "调用猫超取消回告接口失败超过配置次数，标记转换失败", saRefund);
                } else {
                    this.updateOrderCancelAndTransTimes(TransferOrderStatus.NOT_TRANSFER.toInteger(), "调用猫超取消回告接口失败", saRefund);
                }
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ALIBABA_ASCP_CANCEL_DELIVER_GOODS_REPLY.getKey(), "缺货回告成功:" + "订单取消发货回告失败", null, null, user);
            } else {
                //调用【猫超直发取消订单更新出库状态接口】 失败，标记 已转换， 备注：调用猫超直发取消订单更新出库状态接口失败！
                this.updateOrderCancel(TransferOrderStatus.TRANSFERRED.toInteger(), "调用猫超取消回告接口成功", saRefund);
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ALIBABA_ASCP_CANCEL_DELIVER_GOODS_REPLY.getKey(), "缺货回告成功:" + "订单取消发货回告成功", null, null, user);
            }
        } catch (Exception e) {
            log.error("调用猫超取消回告接口error" + e);
            throw new NDSException(e);
        }
    }

    /**
     * 出现异常时更新状态
     *
     * @param saRefund
     * @return
     */
    public void updateSaRefundIsTransError(IpBAlibabaAscpOrderCancel saRefund, String error) {
        String sysRemark = SysNotesConstant.SYS_REMARK0;
        //异常信息超过500 截取500
        String str = sysRemark + error;
        if (str.length() > 500) {
            str = str.substring(0, 500);
        }
        this.updateOrderCancel(TransferOrderStatus.NOT_TRANSFER.toInteger(), str, saRefund);
    }

    /**
     * 更新订单明细状态为0,并取消拦订单
     *
     * @param tid     接口平台单号
     * @param orderId 订单id
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderItem(String tid, Long orderId, String billno, User user) {
        try {
            //则更新订单管理明细商品退款状态更新为0，判断是否存在退款状态为1的商品,不存在 取消拦截，订单退款状态更新为0，记录操作日志
            ocBOrderItemMapper.updateRefundStatusByTidAndOrderId(OmsOrderRefundStatus.UNREFUND.toInteger(), Long.valueOf(tid), orderId);
            // List<Long> itemids = ocBOrderItemMapper.queryIdByTidAndOrderId(Long.valueOf(tid), orderId);
            // if (CollectionUtils.isEmpty(itemids)) {
            //订单主表更新,是否退款中”：退款中
            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setId(orderId);
            updateOrder.setIsInreturning(InreturningStatus.INRETURN_NO);
            returnOrderTransferUtil.updateOperator(updateOrder, user);
            ocBOrderMapper.updateById(updateOrder);
            //是否已经拦截 订单hold 或释放hold单调用HOLD单接口
            updateOrder.setIsInterecept(OmsOrderIsInterceptEnum.NO.getVal()); //订单hold 或释放hold单调用HOLD单接口
            ocBOrderHoldService.holdOrUnHoldOrder(updateOrder, OrderHoldReasonEnum.REFUND_HOLD);
                /*omsOrderLogService.addUserOrderLog(orderId, billno, OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(),
                        "订单取消拦截", "", "", user);*/
            // }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 订单id=" + orderId + ",更新明细退款状态为0,取消主表拦截失败！异常:{}", e.getMessage());
            throw new NDSException(e);
        }
    }

    /**
     * 更新订单明细状态  订单明细：退款成功，订单主表系统作废
     *
     * @param tid     接口平台单号
     * @param orderId 订单id
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderAndItem(String tid, Long orderId, User user) {
        try {
            //更新订单明细的退款状态,根据平台单号将明细表中同平台单号的明细退款状态修改为1
            ocBOrderItemMapper.updateRefundStatusByStringTidAndOrderId(OmsOrderRefundStatus.SUCCESS.toInteger(), tid, orderId);
            //订单主表更新,系统作废
            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setId(orderId);
            updateOrder.setOrderStatus(OmsOrderStatus.CANCELLED.toInteger());
            returnOrderTransferUtil.updateOperator(updateOrder, user);
            ocBOrderMapper.updateById(updateOrder);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 订单id=" + orderId + ",更新明细退款状态和主表是否退款中失败！异常:{}", e.getMessage());
            throw new NDSException(e);
        }
    }

    /**
     * 更新猫超直发取消订单转单状态
     *
     * @param istrans                   状态
     * @param remark                    备注
     * @param ipBAlibabaAscpOrderCancel 猫超直发取消订单
     */
    public void updateOrderCancel(int istrans, String remark, IpBAlibabaAscpOrderCancel ipBAlibabaAscpOrderCancel) {
        try {
            JSONObject object = new JSONObject();
            object.put("id", ipBAlibabaAscpOrderCancel.getId());
            object.put("istrans", istrans);
            if (StringUtils.isNotBlank(remark) && remark.length() >= 200) {
                remark = remark.substring(0, 200);
            }
            object.put("sysremark", remark);
            object.put("modifieddate", new Date());
            object.put("transdate", new Date());
            ipBAlibabaAscpOrderCancelMapper.updateOrderCancel(object);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 履约单号=" + ipBAlibabaAscpOrderCancel.getBizOrderCode() + "，猫超直发取消订单更新转单失败,异常:{}", e.getMessage());
            throw new NDSException(e);
        }
    }

    /**
     * 更新猫超直发取消订单转单状态
     *
     * @param istrans                   状态
     * @param remark                    备注
     * @param ipBAlibabaAscpOrderCancel 猫超直发取消订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderCancelAndTransTimes(int istrans, String remark, IpBAlibabaAscpOrderCancel ipBAlibabaAscpOrderCancel) {
        String bizOrderCode = null;
        try {
            bizOrderCode = ipBAlibabaAscpOrderCancel.getBizOrderCode();
            JSONObject object = new JSONObject();
            object.put("id", ipBAlibabaAscpOrderCancel.getId());
            object.put("istrans", istrans);
            if (StringUtils.isNotBlank(remark) && remark.length() >= 200) {
                remark = remark.substring(0, 200);
            }
            object.put("sysremark", remark);
            object.put("modifieddate", new Date());
            object.put("transdate", new Date());
            ipBAlibabaAscpOrderCancelMapper.updateOrderCancelAndTransCount(object);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 履约单号=" + bizOrderCode + "，猫超直发取消订单更新转单失败,异常:{}", e.getMessage());
            throw new NDSException(e);
        }
    }

    /**
     * 拦截订单并记录日志
     *
     * @param ocBOrder 全渠道订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void interceptAndLog(OcBOrder ocBOrder) {
        try {
            OcBOrder newOrder = new OcBOrder();
            newOrder.setId(ocBOrder.getId());
            newOrder.setIsInterecept(OmsOrderIsInterceptEnum.YES.getVal()); //订单hold 或释放hold单调用HOLD单接口
            ocBOrderHoldService.holdOrUnHoldOrder(newOrder, OrderHoldReasonEnum.REFUND_HOLD);
            ocBOrder.setIsInterecept(OmsOrderIsInterceptEnum.YES.getVal());
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 订单id=" + ocBOrder.getId() + ",更新拦截失败！异常:{}", e.getMessage());
            throw new NDSException(e);
        }
    }

    /**
     * 根据退款id查询猫超直发取消订单等信息
     *
     * @param bizOrderCode
     * @return
     */
    public IpBAlibabaAscpOrderCancelRelation getIpBAlibabaAscpOrderCancelRelation(String bizOrderCode) {
        printLog(this.getClass().getName() + ".getIpBAlibabaAscpOrderCancelRelation.bizOrderCode:{}", bizOrderCode);
        //猫超直发取消订单中间表关系
        IpBAlibabaAscpOrderCancelRelation cancelRelation = new IpBAlibabaAscpOrderCancelRelation();
        try {
            IpBAlibabaAscpOrderCancel ipBAlibabaAscpOrderCancel = ipBAlibabaAscpOrderCancelMapper.selectIpBAlibabaAscpOrderCancelByBizOrderCode(bizOrderCode);
            if (null == ipBAlibabaAscpOrderCancel) {
                return null;
            }
            cancelRelation.setIpBAlibabaAscpOrderCancel(ipBAlibabaAscpOrderCancel);

            List<OcBOrder> ocBOrders = this.getOcBOrders(bizOrderCode);
            cancelRelation.setOcBOrder(ocBOrders.get(0));
            printLog(this.getClass().getName() + ".getIpBAlibabaAscpOrderCancelRelation.cancelRelation:{}", JSONObject.toJSONString(cancelRelation));
        } catch (Exception e) {
            printLog(this.getClass().getName() + "getIpBAlibabaAscpOrderCancelRelation.error:{}", e.getMessage());
        }
        return cancelRelation;
    }


    public List<OcBOrder> getOcBOrders(String bizOrderCode) {
        List<OcBOrder> orderList = new ArrayList<>();

     //   List<Long> ids = ES4Order.getIdsBySourceCode(bizOrderCode);
        List<Long> ids = GSI4Order.getIdListBySourceCode(bizOrderCode);

        if (CollectionUtils.isNotEmpty(ids)) {
            //查询订单
            orderList = ocBOrderMapper.selectByIdsList(ids);
        }
        return orderList;
    }

    /**
     * 猫超直发取消订单补偿服务 查询未转换的数据
     * ES获取猫超直发取消订单中间表数据分库键
     */
    public List<String> selectAlibabaAscpCancelOrder(int pageIndex, int pageSize) {
        List<String> list = new ArrayList<>();
        JSONObject search = ES4IpAlibabaAscpCancelOrder.findBizOrderCodeByTrans(pageIndex, pageSize);
        printLog(this.getClass().getName() + " selectAlibabaAscpCancelOrder es return  {}", search.toString());
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getString("BIZ_ORDER_CODE"));
            }
        }
        return list.stream().filter(item -> StringUtils.isNotBlank(item)).distinct().collect(Collectors.toList());
    }

    private void printLog(String s, Object... s2) {
        if (log.isDebugEnabled()) {
            log.debug(s, s2);
        }
    }

}
