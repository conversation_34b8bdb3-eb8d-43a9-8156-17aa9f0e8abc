package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 退款成功明细处理服务
 *
 * @author: heliu
 * @since: 2019/3/12
 * create at : 2019/3/12 20:29
 */
@Component
@Slf4j
public class OmsOrderCheckHasRefundProductService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;


    /**
     * @param orderInfo 订单对象
     * @return boolean
     */

    public String doOrderDetalRefund(OcBOrderRelation orderInfo) {

        //判断订单明细是否全部为退款成功的明细
        List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectOrderItems(orderInfo.getOrderId());
        List<OcBOrderItem> orderRefundItemList = orderItemList.stream().filter(p -> p.getRefundStatus().equals(OcOrderRefundStatusEnum.SUCCESS.getVal())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderRefundItemList)) {
            //没有退款
            return "none";
        } else {
            //全部为退款成功商品
            if (orderRefundItemList.size() == orderItemList.size()) {
                //全部退款
                return "all";
            } else {
                //部分退款
                return "part";
            }
        }
    }
}
