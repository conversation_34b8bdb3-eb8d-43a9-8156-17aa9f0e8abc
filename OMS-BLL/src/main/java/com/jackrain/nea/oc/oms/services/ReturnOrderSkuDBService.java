package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderDefectMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderReturnReserveBigint07Type;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/9/24
 * <p>
 * 退单次品调拨
 */
@Slf4j
@Component
public class ReturnOrderSkuDBService {

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    OcBReturnOrderDefectMapper ocBReturnOrderDefectMapper;
    @Autowired
    CpRpcService cpRpcService;
    @Autowired
    ReturnOrderAuditService returnOrderAuditService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private ReturnOrderLogService logService;

    /**
     * 次品调拨
     *
     * @param returnOrderId 退单明细id
     * @param user
     * @param bl            是否记录操作日志
     * @return
     */
    public ValueHolderV14<List> skuDb(Long returnOrderId, User user, Boolean bl) {
//        if (log.isDebugEnabled()) {
//            log.debug("次品调拨开始id" + returnOrderId);
//        }
//        ValueHolderV14<List> returnVh = new ValueHolderV14<>();
//        List<ValueHolderV14<Long>> resultList = new ArrayList<>();
//        if (returnOrderId == null) {
//            returnVh.setCode(ResultCode.FAIL);
//            returnVh.setMessage("退单id不能为空！");
//            log.debug("退单id不能为空");
//            return returnVh;
//        }
//        if (checkReserveBigint07(returnOrderId, returnVh)) {
//            return returnVh;
//        }
//
//        List<OcBReturnOrderRefund> orderRefundList = ocBReturnOrderRefundMapper.selectByOcOrderId(returnOrderId);
//        if (orderRefundList == null || orderRefundList.size() == 0) {
//            returnVh.setCode(ResultCode.FAIL);
//            returnVh.setMessage("无需要处理的数据！");
//            log.debug("无需要处理的数据id" + returnOrderId);
//            return returnVh;
//        }
//
//        List<Long> ids = new ArrayList<>();
//        ids.add(returnOrderId);
//        //逻辑收货单
//        SgReceiveBillQueryResult sgSendBillQueryResult = sgRpcService.querySgBReceiveByrefundIds(ids, user);
//        if (log.isDebugEnabled()) {
//            log.debug("逻辑收货单出参" + JSONObject.toJSONString(sgSendBillQueryResult));
//        }
//        Map<SgBReceive, List<SgBReceiveItem>> sgSendMap;
//        String billType = String.valueOf(SgConstantsIF.BILL_TYPE_RETAIL_REF);
//        String mapKey = returnOrderId + "," + billType;
//        List<SgBReceiveItem> sgBSendItems = Lists.newArrayList();
//        if (null != sgSendBillQueryResult && null != sgSendBillQueryResult.getResults() && null != sgSendBillQueryResult.getResults().get(mapKey)) {
//            sgSendMap = sgSendBillQueryResult.getResults().get(mapKey);
//            for (SgBReceive sgBSend : sgSendMap.keySet()) {
//                sgBSendItems.addAll(sgSendMap.get(sgBSend));
//            }
//        }
//        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(returnOrderId);
////        List<SgTransferBillSaveRequest> requests = new ArrayList<>();
////        SgTransferBillSaveRequest sgTransferBillSaveRequest = null;
////        List<SgTransferItemSaveRequest> sgTransferItemSaveRequests = Lists.newArrayList();
////        boolean bol = true;
////        for (SgBReceiveItem sgBSendItem : sgBSendItems) {
////            if (sgTransferBillSaveRequest == null) {
////                sgTransferBillSaveRequest = setRequestParent(sgBSendItem, ocBReturnOrder);
////                sgTransferBillSaveRequest.setLoginUser(user);
////            }
////            //匹配对应的明细
////            for (OcBReturnOrderRefund ocBReturnOrderRefund : orderRefundList) {
////                if (sgBSendItem.getSourceBillItemId().equals(ocBReturnOrderRefund.getId())) {
////                    ValueHolderV14<Long> vh = new ValueHolderV14<>();
////                    Long ocBReturnOrderId = ocBReturnOrderRefund.getOcBReturnOrderId();
////                    Long psCSkuId = ocBReturnOrderRefund.getPsCSkuId();
////                    //查询当前单据是否是
////                    int record1 = ocBReturnOrderRefundMapper.selectRecord(ocBReturnOrderId, ocBReturnOrderRefund.getId());
////                    if (record1 == 0) {
////                        vh.setCode(ResultCode.FAIL);
////                        vh.setMessage("此商品商品状态非无次品调拨0和次品未调拨1！");
////                        vh.setData(ocBReturnOrderRefund.getId());
////                        resultList.add(vh);
////                        continue;
////                    }
////                    //查询次品是否存在
////                    int record2 = ocBReturnOrderDefectMapper.selectRecord(ocBReturnOrderId, psCSkuId);
////                    if (record2 == 0) {
////                        vh.setCode(ResultCode.FAIL);
////                        vh.setMessage("此商品在退单次品表中不存在！");
////                        vh.setData(ocBReturnOrderRefund.getId());
////                        resultList.add(vh);
////                        continue;
////                    }
////                   // SgTransferItemSaveRequest sgTransferItemSaveRequest = setRequestItem(ocBReturnOrderRefund);
////                    //sgTransferItemSaveRequests.add(sgTransferItemSaveRequest);
////                    bol = false;
////                }
//            }
//        }
//        if (bol) {
//            returnVh.setCode(ResultCode.FAIL);
//            returnVh.setMessage("无次品需要调拨！");
//            log.debug("无次品需要调拨！");
//            return returnVh;
//        }
//        //sgTransferBillSaveRequest.setItems(sgTransferItemSaveRequests);
//        requests.add(sgTransferBillSaveRequest);
//        //次品调拨
//        try {
//            if (log.isDebugEnabled()) {
//                log.debug("退货次品调拨入参：" + JSONObject.toJSONString(requests));
//            }
//            //returnVh = sgRpcService.saveAndAuditBatch(requests);
//            if (log.isDebugEnabled()) {
//                log.debug("退货次品调拨出参：" + JSONObject.toJSONString(requests));
//            }
//            //对结果日志记录
//            if (bl && returnVh.getCode() == ResultCode.SUCCESS) {
//                logService.addRefundOrderLog(returnOrderId, "退货次品调拨", "退货次品调拨成功", user);
//            }
//            //更新标识
//            if (bl && returnVh.getCode() == ResultCode.SUCCESS) {
//                updateOrderRefund(OrderReturnReserveBigint07Type.YES.getVal(), user, returnOrderId);
//            } else {
//                updateOrderRefund(OrderReturnReserveBigint07Type.NO.getVal(), user, returnOrderId);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("退货次品调拨错误：" + e);
//            updateOrderRefund(OrderReturnReserveBigint07Type.NO.getVal(), user, returnOrderId);
//            returnVh.setCode(ResultCode.FAIL);
//            String message = Resources.getMessage(e.getMessage());
//            returnVh.setMessage(message);
//            logService.addRefundOrderLog(returnOrderId, "退货次品调拨", message, user);
//        }
        return null;
    }

    /**
     * 入参父
     */
//    private SgTransferBillSaveRequest setRequestParent(SgBReceiveItem sgBSendItem, OcBReturnOrder ocBReturnOrder) {
//        Date creationdate = ocBReturnOrder.getCreationdate();
//        SgTransferBillSaveRequest sgTransferBillSaveRequest = new SgTransferBillSaveRequest();
//        SgTransferSaveRequest sgTransferSaveRequest = new SgTransferSaveRequest();
//        //单据日期 date
//        sgTransferSaveRequest.setBillDate(creationdate);
//        //调拨类型
//        sgTransferSaveRequest.setTransferType(SgTransferConstantsIF.TRANSFER_TYPE_NORMAL);
//        //发货类型 自提
//        sgTransferSaveRequest.setSendType(SgTransferConstantsIF.SEND_TYPE_ZT);
//
//
//        //发货仓
//        sgTransferSaveRequest.setCpCOrigId(sgBSendItem.getCpCStoreId());
//        sgTransferSaveRequest.setCpCOrigEcode(sgBSendItem.getCpCStoreEcode());
//        sgTransferSaveRequest.setCpCOrigEname(sgBSendItem.getCpCStoreEname());
//
//        CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(ocBReturnOrder.getCpCPhyWarehouseInId());
//        if (log.isDebugEnabled()) {
//            log.debug("实体仓数据：" + JSONObject.toJSONString(cpCPhyWarehouse));
//        }
//        //收货仓ID
//        sgTransferSaveRequest.setCpCDestId(cpCPhyWarehouse.getCpCStoreId());
//        //调拨类型 TRANSFER_PROP_CP次品调拨  TRANSFER_PROP_ZP正品调拨
//        sgTransferSaveRequest.setSgBTransferPropId(SgTransferConstantsIF.TRANSFER_PROP_ZP);
//        sgTransferSaveRequest.setRemark(String.format("退货【%s】入库次品生成调拨单。", ocBReturnOrder.getId()));
//        sgTransferBillSaveRequest.setTransfer(sgTransferSaveRequest);
//        return sgTransferBillSaveRequest;
//    }

    private boolean checkReserveBigint07(Long returnOrderId, ValueHolderV14<List> returnVh) {
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(returnOrderId);
        if (ocBReturnOrder == null ||
                (ocBReturnOrder.getStatusDefectiveTrans() == null ||
                        ocBReturnOrder.getStatusDefectiveTrans().equals((long) OrderReturnReserveBigint07Type.YES.getVal()))) {
            returnVh.setCode(ResultCode.FAIL);
            returnVh.setMessage("当前单据状态为已调拨！");
            return true;
        }
        return false;
    }

    /**
     * 修改退单明细次品调拨状态
     *
     * @param reserveBigint07
     * @param user
     * @return
     */
    private int updateOrderRefund(int reserveBigint07, User user, Long returnOrderId) {

        JSONObject updateParam = new JSONObject();
        updateParam.put("modifierename", user.getEname());
        updateParam.put("modifiername", user.getName());
        updateParam.put("modifieddate", new Date(System.currentTimeMillis()));
        updateParam.put("id", returnOrderId);
        updateParam.put("status_defective_trans", reserveBigint07);
        int i = ocBReturnOrderMapper.updateByid(updateParam);
        /*try {
            Boolean aBoolean = SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, ocBReturnOrderMapper.selectById(returnOrderId), returnOrderId);
            if (!aBoolean) {
                log.debug("次品调拨es推送失败！");
            }
        } catch (Exception e) {
            log.debug("次品调拨es推送异常！" + e);
        }*/
        return i;
    }

//    /**
//     * 入参子
//     */
//    private SgTransferItemSaveRequest setRequestItem(OcBReturnOrderRefund ocBReturnOrderRefund) {
//
//        SgTransferItemSaveRequest sgTransferItemSaveRequest = new SgTransferItemSaveRequest();
//        //新增标识 -1新增
//        sgTransferItemSaveRequest.setId(-1L);
//        //商品
//        sgTransferItemSaveRequest.setPsCProId(ocBReturnOrderRefund.getPsCProId());
//        sgTransferItemSaveRequest.setPsCProEcode(ocBReturnOrderRefund.getPsCProEcode());
//        sgTransferItemSaveRequest.setPsCProEname(ocBReturnOrderRefund.getPsCProEname());
//        //SKU
//        sgTransferItemSaveRequest.setPsCSkuId(ocBReturnOrderRefund.getPsCSkuId());
//        sgTransferItemSaveRequest.setPsCSkuEcode(ocBReturnOrderRefund.getPsCSkuEcode());
//        //数量
//        sgTransferItemSaveRequest.setQty(ocBReturnOrderRefund.getQtyIn());
//
//        return sgTransferItemSaveRequest;
//    }

    /**
     * 标记次品为已调拨
     *
     * @param returnOrderId
     * @param user
     * @return
     */
    public ValueHolderV14 markAsCompleted(Long returnOrderId, User user) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(returnOrderId);
        //调拨状态校验
        if (checkReserveBigint07(returnOrderId, valueHolderV14)) {
            return valueHolderV14;
        }
        int i = updateOrderRefund(OrderReturnReserveBigint07Type.YES.getVal(), user, returnOrderId);
        if (i > 0) {
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("标记成功！");
        } else {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("标记失败！");
        }
        return valueHolderV14;
    }
}