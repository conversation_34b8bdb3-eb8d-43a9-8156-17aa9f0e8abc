package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNotice;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OcInvoiceLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.OcInvoiceStatusEnum;
import com.jackrain.nea.oc.oms.util.ArrayUtil;
import com.jackrain.nea.oc.oms.util.InvoiceNoticeDealUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: chenxiulou
 * @description: 开票通知 作废
 * @since: 2019-07-20
 * create at : 2019-07-20 17:54
 */
@Slf4j
@Component
public class InvoiceNoticeVoidService extends CommandAdapter {
    @Autowired
    private OcBInvoiceNoticeMapper invoiceNoticeMapper;
    @Autowired
    private InvoiceNoticeDealUtil invoiceNoticeDealUtil;


    public ValueHolder voidInvoiceNotice(QuerySession session) throws NDSException {
        ValueHolder holder = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        //生成数组
        JSONArray voidArray = ArrayUtil.buildJsonArray(param);
        //列表批量
        if (!CollectionUtils.isEmpty(voidArray)) {
            JSONArray errorInfo = new JSONArray();
            for (int i = 0; i < voidArray.size(); i++) {
                //遍历 jsonarray 数组，把每一个对象转成 json 对象
                Long id = Long.valueOf(voidArray.get(i).toString());
                try {
                    //验证
                    checkInvoicNotice(id);
                    voidInvoicNoticeSingle(id, session.getUser());
                } catch (Exception e) {
                    JSONObject errorInfoObj = new JSONObject();
                    errorInfoObj.put("objid", id);
                    errorInfoObj.put("ecode", -1);
                    errorInfoObj.put("message", e.getMessage());
                    errorInfo.add(errorInfoObj);
                }
            }
            if (CollectionUtils.isNotEmpty(errorInfo)) {
                holder.put("code", ResultCode.FAIL);
                holder.put("message", "作废失败！");
                holder.put("data", errorInfo);
            } else {
                holder.put("code", ResultCode.SUCCESS);
                holder.put("message", "作废成功！");
            }
        } else {
            holder.put("code", ResultCode.FAIL);
            holder.put("message", "开票单据为空，不允许进行作废操作！");
        }
        //返回日志
        return holder;
    }

    /**
     * 批量作废
     *
     * @param id
     * @param user
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    public void voidInvoicNoticeSingle(long id, User user) {
        //作废
        OcBInvoiceNotice invoiceNotice = new OcBInvoiceNotice();
        invoiceNotice.setId(id);
        invoiceNotice.setIsactive(OcBOrderConst.IS_ACTIVE_NO);//作废状态
        invoiceNotice.setEstatus(OcInvoiceStatusEnum.VOID.getVal());
        invoiceNotice.setDelerName(user.getName());//作废人用户名
        invoiceNotice.setDelerId(Long.valueOf(user.getId()));//作废人
        invoiceNotice.setDelerEname(user.getEname());//作废人姓名
        invoiceNotice.setDelTime(new Date());//作废时间
        invoiceNotice.setModifiername(user.getName());//作废人用户名
        invoiceNotice.setModifierid(Long.valueOf(user.getId()));//作废人
        invoiceNotice.setModifierename(user.getEname());//作废人姓名
        invoiceNotice.setModifieddate(new Date());//作废时间

        int update = invoiceNoticeMapper.updateById(invoiceNotice);
        if (update < 0) {
            throw new NDSException("作废失败！");
        } else {
            //更新全渠道订单 发票Id
            OcBInvoiceNotice esInvoice = invoiceNoticeMapper.selectById(id);
            ValueHolderV14<List<OcBOrder>> v14Orders = invoiceNoticeDealUtil.updateOcBOrderList(esInvoice.getId(),
                    esInvoice.getOcBOrderId(), "void", user);
            List<OcBOrder> esOrders = new ArrayList<>();
            if (v14Orders != null && v14Orders.getCode() == ResultCode.SUCCESS) {
                esOrders = v14Orders.getData();
            }
            //推送ES
            invoiceNoticeDealUtil.pushInvoiceNoticeToEs(esInvoice, null, null, esOrders);
            //新增日志
            invoiceNoticeDealUtil.addInvoiceNoticeLog((Long) id, OcInvoiceLogTypeEnum.VOID.getKey(),
                    "作废完成", user);
        }
    }

    /**
     * check
     *
     * @param id
     * @return java.lang.String
     * @Author: 陈秀楼
     * @Date 2019/3/8
     */
    private void checkInvoicNotice(long id) {
        OcBInvoiceNotice notice = invoiceNoticeMapper.selectById(id);
        if (notice == null) {
            throw new NDSException("当前记录已不存在！");
        } else {
            String billNo = notice.getBillNo() != null ? notice.getBillNo() : "";
            if (!OcBOrderConst.IS_ACTIVE_YES.equals(notice.getIsactive())) {
                throw new NDSException("[" + billNo + "]当前记录已作废，不允许重复作废！");
            }
            if (!OcInvoiceStatusEnum.NOT_AUDITED.getVal().equals(notice.getEstatus())) {
                throw new NDSException("[" + billNo + "]当前记录非未审核，不允许作废！");
            }
        }
    }

    public static JSONObject GetValueHolderObject(Long objid, String message) {
        JSONObject object = new JSONObject();
        object.put("objid", objid);
        object.put("message", message);
        return object;
    }
}
