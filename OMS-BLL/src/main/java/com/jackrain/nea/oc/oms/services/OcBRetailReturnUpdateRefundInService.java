package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.service.PayableAdjustmentVoidService;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.eventListener.SmsSendEvent;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.model.SgBPhyInResultExt;
import com.jackrain.nea.oc.model.SgBPhyInResultItemExt;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.SmsSendStrategyInfo;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.BigDecimalExt;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.ReturnOrderNodeEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.st.service.OmsOrderStCAutocheckService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.jackrain.nea.oc.oms.services.ReturnOrderAuditService.getRootUser;

/**
 * 零售退货单更新入库结果服务
 *
 * @author: xiWen.z
 * create at: 2019/5/7 0007
 */
@Slf4j
@Component
public class OcBRetailReturnUpdateRefundInService {

    @Autowired
    ReturnOrderLogService returnOrderLogService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private ExchangeInService exchangeInService;
    @Autowired
    private OcBOrderItemFiMapper ocBorderItemMapper;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private ReturnOrderAutoAuditService returnOrderAutoAuditService;
    @Autowired
    private OmsOrderStCAutocheckService omsOrderStCAutocheckService;
    @Autowired
    private ReturnOrderSkuDBService returnOrderSkuDBService;

    @Autowired
    private PayableAdjustmentVoidService adjustmentVoidService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsToSapTaskService omsToSapTaskService;

    @Autowired
    private OcBReturnOrderNodeRecordService nodeRecordService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private IpBAlibabaAscpOrderRefundService orderRefundService;

    @Autowired
    private IpRpcService service;

    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;

    /**
     * safe get number
     */
    private Function<BigDecimal, BigDecimal> initDecimal = num -> num == null ? BigDecimal.ZERO : num;
    /**
     * set origin item
     */
    private BiConsumer<OcBOrderItem, OcBReturnOrderRefund> setOrderQtyCsm = new BiConsumer<OcBOrderItem, OcBReturnOrderRefund>() {
        @Override
        public void accept(OcBOrderItem o1, OcBReturnOrderRefund o2) {
            BigDecimal itemQtyRefund = initDecimal.apply(o1.getQtyRefund());
            BigDecimal itmQtyReturnApply = initDecimal.apply(o1.getQtyReturnApply());
            BigDecimal qtyIn = initDecimal.apply(o2.getQtyIn());
            BigDecimal qtyRefund = initDecimal.apply(o2.getQtyRefund());

            BigDecimal subQty = qtyRefund.subtract(qtyIn);
            itmQtyReturnApply = itmQtyReturnApply.subtract(subQty);
            itemQtyRefund = itemQtyRefund.add(qtyIn);
            o1.setQtyRefund(itemQtyRefund);
            o1.setQtyReturnApply(itmQtyReturnApply);
        }
    };
    private Predicate<BigDecimal> gtZero = num -> num.compareTo(BigDecimal.ZERO) > 0;
    private Predicate<BigDecimal> eqZero = num -> num.compareTo(BigDecimal.ZERO) == 0;
    private BiPredicate<BigDecimal, BigDecimal> prevGtNext = (num, numb) -> num.compareTo(numb) > 0;
    private BiPredicate<BigDecimal, BigDecimal> prevEqNext = (num, numb) -> num.compareTo(numb) == 0;

    /**
     * 零售退货单更新入库结果服务
     *
     * @param sgPir     入库结果单
     * @param inRstList 入库结果单明细
     * @param usr       User
     * @return VH
     */
    public ValueHolderV14 updateWareHousingResultService(SgBPhyInResultExt sgPir,
                                                         List<SgBPhyInResultItemExt> inRstList, User usr) {

        // 1.0 validate
        if (usr == null || usr.getId() == null) {
            ValueHolderV14 vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数异常,用户信息丢失");
            return vh;
        }
        if (sgPir == null || sgPir.getSourceBillId() == null || inRstList == null) {
            return valueHolderResult("参数异常", false, usr);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBRetailReturnUpdateRefundInService.returnOrderId=", sgPir.getSourceBillId()));
        }

        // 1.1
        inRstList = inRstList.stream().filter(x -> x != null && x.getPsCSkuId() != null).collect(Collectors.toList());
        if (inRstList.size() < 1) {
            return valueHolderResult("入库结果单明细数据不正确", false, usr);
        }

        // 2.0
        ValueHolderV14 valueHolderV14 = mainProgramService(sgPir, inRstList, usr);

        // 4.0
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        int on_off = config.getProperty("return.order.rejects.db", 1);
        if (on_off == 1) {
            returnOrderSkuDBService.skuDb(sgPir.getSourceBillId(), usr, true);
        }
        return valueHolderV14;
    }

    /**
     * 主程
     *
     * @param sgPir     入库结果单
     * @param inRstList 入库结果单明细
     * @param usr       User
     * @return VH
     */
    private ValueHolderV14 mainProgramService(SgBPhyInResultExt sgPir,
                                              List<SgBPhyInResultItemExt> inRstList, User usr) {
        boolean matchStu;
        try {
            // 1. prepare parameter
            JSONObject paramJo = new JSONObject();
            paramJo.put("id", sgPir.getSourceBillId());
            paramJo.put("returnStatus", ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
            paramJo.put("inventedStatus", OcBOrderConst.INVENTED_STATUS_UN);
            paramJo.put("returnStatusSec", ReturnStatusEnum.CANCLE.getVal());
            paramJo.put("isActive", OcBOrderConst.IS_ACTIVE_YES);

            // 2. search, validate
            OcBReturnOrder rtn = ocBReturnOrderMapper.queryOcBReturnOrderByCdt(paramJo);
            if (rtn == null || rtn.getId() == null) {
                recordLogMsg("OcBRetailReturnUpdateRefundInService.mainProgramService:->  未查询到退换货订单数据:");
                return valueHolderResult("未查询到退换货订单数据", false, usr);
            }

            List<OcBReturnOrderRefund> rtnORfnList = ocBReturnOrderRefundMapper
                    .queryRtnOrderRefundByoId(rtn.getId(), OcBOrderConst.IS_ACTIVE_YES);
            if (rtnORfnList == null || rtnORfnList.isEmpty()) {
                recordLogMsg("OcBRetailReturnUpdateRefundInService.mainProgramService: 未查询到退换货订单明细数据");
                return valueHolderResult("未查询到退换货订单明细数据", false, usr);
            }

            List<OcBOrderItem> orderItems = ocBorderItemMapper.selectItemList(rtn.getOrigOrderId());
            if (orderItems == null || orderItems.isEmpty()) {
                recordLogMsg("OcBRetailReturnUpdateRefundInService.mainProgramService: 未查询到原订单明细数据");
                return valueHolderResult("未查询到原订单明细数据", false, usr);
            }

            OcBOrder ocBOrder = ocBOrderMapper.selectById(rtn.getOrigOrderId());
            if (null == ocBOrder) {
                recordLogMsg("OcBRetailReturnUpdateRefundInService.mainProgramService: 未查询到原订单数据");
                return valueHolderResult("未查询到原订单明细数据", false, usr);
            }

            // 3. 匹配流程. 更新数量
            boolean matchRstBool = mainMatchProgram(rtn, rtnORfnList, sgPir, inRstList, usr);
            if (!matchRstBool) {
                return valueHolderResult("更新入库服务失败, 至少需要一条明细数据符合匹配要求", false, usr);
            }
            // 4. 更新.重新计算退单金额,退货状态
            matchStu = reCalcAmt(rtn, rtnORfnList, orderItems);
            // 5. 数据处理
            OcBRetailReturnUpdateRefundInService bean = ApplicationContextHandle
                    .getBean(OcBRetailReturnUpdateRefundInService.class);
            bean.updateDataMethods(rtn, rtnORfnList, matchStu, usr, orderItems);

            if (matchStu) {
                autoAuditReturnOrderService(rtn, usr);
            }
            //@20200818 新增退货单类型 '等待售后确认'节点，快递单号查询丢件单信息并对丢件单作废，并记录日志
            adjustmentVoidToCmd(rtn);

            // 4. task
            //        omsSyncTh3SysTaskService.insertTaskReturnSync(rtn);
            //判断是否是门店 todo 1207先不上
            CpCPhyWarehouse wareHouse = cpRpcService.queryByWarehouseId(rtn.getCpCPhyWarehouseId());
            List<Long> storeIdList = cpRpcService.queryStoreList(rtn.getCpCPhyWarehouseId());
            if(CollectionUtils.isEmpty(storeIdList)){
                throw new NDSException("通过订单实体仓ID未查询到逻辑仓ID");
            }
            CpStore cpStore = cpRpcService.selectCpCStoreById(storeIdList.get(0));
            if (cpStore == null) {
                throw new NDSException("未查询到逻辑仓信息");
            }
            //上级经销商=1 表示经销商的门店
            boolean customerFlag = YesNoEnum.Y.getVal().equals(cpStore.getCCustomerupId());
            if (wareHouse == null) {
                throw new NDSException("通过订单实体仓ID获取实体仓信息获取的对象为空");
            }else {
                if (StringUtils.equals(wareHouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE_STORE_02)&&!customerFlag){
                    List<OcBReturnOrder> list = Lists.newArrayList(rtn);
                    log.info("门店类型，当退货入库时，再下发wing{}",list);
                    ValueHolderV14 v14 = null;//service.pushReturnOrderToWing(list,true);
                    if (log.isDebugEnabled()) {
                        log.debug("退单传wms结果：{}", v14);
                    }
                    if (v14.isOK()){
                        for (OcBReturnOrder returnOrder : list) {
                            OcBReturnOrder ocBReturnOrder =new OcBReturnOrder();
                            ocBReturnOrder.setId(returnOrder.getId());
                            ocBReturnOrder.setModifieddate(new Date());
                            ocBReturnOrder.setToDrpStatus(ToDRPStatusEnum.SUCCESS.getCode());
                            ocBReturnOrder.setToDrpCount(0);
                            ocBReturnOrder.setToDrpFailedReason("");
                            ocBReturnOrder.setWmsFailreason("");
                            ocBReturnOrder.setIsTowms(WmsWithdrawalState.YES.toInteger());
                            ocBReturnOrder.setRemark("");
                            ocBReturnOrder.setQtyWmsFail(0L);
                            returnOrderMapper.updateById(ocBReturnOrder);
                        }
                    }
                }
            }
            try {
                //发送短信
                SmsSendStrategyInfo smsSendStrategyInfo = new SmsSendStrategyInfo();
                smsSendStrategyInfo.setOcBReturnOrder(rtn);
                smsSendStrategyInfo.setServiceType("smsSendRefundIn");
                smsSendStrategyInfo.setTaskNode(OmsSendMsgTaskNodeEnum.REFUND_SWAP_IN_STORAGE.getVal().toString());
                applicationContext.publishEvent(new SmsSendEvent<>(this, smsSendStrategyInfo));
            } catch (Exception e) {
                log.error(LogUtil.format("退货异步短信监听处理异常->: ,error:{}"), Throwables.getStackTraceAsString(e));
            }

            //发送短信
            SmsSendStrategyInfo smsSendStrategyInfo = new SmsSendStrategyInfo();
            smsSendStrategyInfo.setOcBReturnOrder(rtn);
            smsSendStrategyInfo.setServiceType("smsSendRefundIn");
            smsSendStrategyInfo.setTaskNode(OmsSendMsgTaskNodeEnum.REFUND_SWAP_IN_STORAGE.getVal().toString());

            applicationContext.publishEvent(new SmsSendEvent<>(this, smsSendStrategyInfo));

            // 猫超-销售退货入库回传接口
            orderRefundService.invokeAscpInStorageFeedbackCmdService(rtn, rtnORfnList, ocBOrder);

            // @20200808 task#22262 更新退换货单发送AC状态
            // @20200821 过滤条件：单据状态为已完成/待售后确认：return_status = 50/30
            if (Objects.nonNull(rtn.getReturnStatus()) && (ReturnStatusEnum.COMPLETION.getVal().equals(rtn.getReturnStatus()) || ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal().equals(rtn.getReturnStatus()))) {
                OmsReturnOrderService.getInstance().updateToACStatusToPendingById(rtn.getId());
            }

            recordLogMsg("OcBRetailReturnUpdateRefundInService.END 零售退货单更新服务结束...");
            return valueHolderResult(" Return To Warehouse Successfully", true, usr);
        } catch (Exception e) {
            log.error(LogUtil.format("OcBRetailReturnUpdateRefundInService.mainProgramService,error:{}"),
                    Throwables.getStackTraceAsString(e));
            return valueHolderResult("运行零售退货单更新服务时异常:" + ExceptionUtil.getMessage(e), false, usr);
        }
    }

    private void adjustmentVoidToCmd(OcBReturnOrder rtn) {
        try {
            Integer billType = rtn.getBillType();
            if (billType != null) {
                ValueHolder vh = adjustmentVoidService.adjustmentVoidById(rtn.getLogisticsCode(), getRootUser());
                if (null != vh) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("OcBRetailReturnUpdateRefundInService.persistDataMethods,作废丢件单结果:{}," +
                                "根据物流单号=", rtn.getLogisticsCode()), vh);
                    }
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("OcBRetailReturnUpdateRefundInService.mainProgramService ,error:{}"),
                    Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 匹配
     *
     * @param rtn         退单
     * @param rtnORfnList 退单明细
     * @param sgPir       入库结果单
     * @param inRstList   入库结果单明细集合
     * @param usr         User
     * @return T?F
     */
    private boolean mainMatchProgram(OcBReturnOrder rtn, List<OcBReturnOrderRefund> rtnORfnList, SgBPhyInResultExt sgPir,
                                     List<SgBPhyInResultItemExt> inRstList, User usr) {
        boolean matchFlag = false;
        Map<String, BigDecimal> ecdMap = new HashMap<>();
        StringBuilder sb = new StringBuilder();
        Collections.sort(rtnORfnList, (x1, x2) -> x2.getQtyRefund().compareTo(x1.getQtyRefund()));
        Collections.sort(inRstList, (x1, x2) -> x2.getQtyIn().compareTo(x1.getQtyIn()));

        for (SgBPhyInResultItemExt sgItm : inRstList) {
            if (sgItm == null) {
                continue;
            }
            Long sgSku = sgItm.getPsCSkuId();
            if (sgSku == null) {
                continue;
            }
            BigDecimal qty = sgItm.getQtyIn();
            if (qty == null || !(gtZero.test(qty))) {
                continue;
            }

            for (OcBReturnOrderRefund rtnItm : rtnORfnList) {
                if (rtnItm == null) {
                    continue;
                }
                if (!sgSku.equals(rtnItm.getPsCSkuId())) {
                    continue;
                }
                BigDecimal qtyRefund = rtnItm.getQtyRefund();
                if (qtyRefund == null) {
                    continue;
                }
                BigDecimal qtyIn = initDecimal.apply(rtnItm.getQtyIn());
                BigDecimal rSubQty = qtyRefund.subtract(qtyIn);
                if (gtZero.test(rSubQty) && gtZero.test(qty)) {
                    if (prevGtNext.test(qty, rSubQty)) {
                        BigDecimal nFullQty = qtyIn.add(rSubQty);
                        rtnItm.setQtyIn(nFullQty);
                        qty = qty.subtract(rSubQty);
                        statisticInQty(ecdMap, rtnItm.getPsCSkuEcode(), rSubQty);
                    } else {
                        BigDecimal nPorOrFulQty = qtyIn.add(qty);
                        rtnItm.setQtyIn(nPorOrFulQty);
                        statisticInQty(ecdMap, rtnItm.getPsCSkuEcode(), qty);
                        qty = BigDecimal.ZERO;
                    }
                    if (!matchFlag) {
                        matchFlag = true;
                    }
                } else {
                    continue;
                }
            }
            boolean g = eqZero.test(qty);
            if (!g) {
                return false;
            }
        }
        if (!matchFlag) {
            return false;
        }
        Set<Map.Entry<String, BigDecimal>> es = ecdMap.entrySet();
        for (Map.Entry<String, BigDecimal> e : es) {
            sb.append("," + e.getKey() + "(" + e.getValue().setScale(0, BigDecimal.ROUND_DOWN) + ")");
        }
        // 更新退单状态
        rtn.setAllSku(sb.substring(OcBOrderConst.IS_STATUS_IY));
        Integer rtnStatus = rtn.getReturnStatus();

        if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(rtnStatus)) {
            rtn.setReturnStatus(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
        }
        Integer ivdStatus = rtn.getInventedStatus();
        if (OcBOrderConst.INVENTED_STATUS_UN.equals(ivdStatus)) {
            rtn.setInventedStatus(OcBOrderConst.INVENTED_STATUS_FINISH);
        }
        // 添加仓库信息.添加用户信息
        addReturnStoreInfo(rtn, sgPir, usr);
        return true;
    }

    /**
     * 多明细匹配,日志记录,合并
     *
     * @param map   Map
     * @param ecode ecode
     * @param qty   add new qty
     */
    private void statisticInQty(Map<String, BigDecimal> map, String ecode, BigDecimal qty) {
        Set<String> keys = map.keySet();
        if (keys.contains(ecode)) {
            BigDecimal inQty = map.get(ecode);
            BigDecimal tmpQty = inQty.add(qty);
            map.put(ecode, tmpQty);
        } else {
            map.put(ecode, qty);
        }
    }

    /**
     * 重新计算金额
     *
     * @param rtn        退单
     * @param rfnList    退单明细
     * @param orderItems 原单明细
     * @return T?F
     */
    private boolean reCalcAmt(OcBReturnOrder rtn, List<OcBReturnOrderRefund> rfnList, List<OcBOrderItem> orderItems) {

        Map<Long, OcBOrderItem> itemMap = orderItems.stream().collect(Collectors.toMap(OcBOrderItem::getId, x -> x));
        BigDecimalExt amt = new BigDecimalExt();
        boolean flag = true;
        for (OcBReturnOrderRefund o : rfnList) {
            if (o == null) {
                continue;
            }
            OcBOrderItem item = null;
            Long rltItemId = o.getOcBOrderItemId();
            if (rltItemId != null) {
                item = itemMap.get(o.getOcBOrderItemId());
            }

            BigDecimal qtyIn = initDecimal.apply(o.getQtyIn());
            BigDecimal qtyRefund = initDecimal.apply(o.getQtyRefund());
            BigDecimal amtRefund = initDecimal.apply(o.getAmtRefund());

            // 申请数量大于0 且 == 入库数量 , 申请数量等于入库数量, 明细金额原值
            if (gtZero.test(qtyRefund) && prevEqNext.test(qtyRefund, qtyIn)) {

                if (item != null) {
                    setOrderQtyCsm.accept(item, o);
                }
                amt.value = amt.value.add(amtRefund);
            } else if (gtZero.test(qtyRefund) && prevGtNext.test(qtyRefund, qtyIn)) {

                // 申请数量 > 入库数量(不为0, 为0) : 单件成交价如果为0=>重新计算
                BigDecimal amtRfnSingle = initDecimal.apply(o.getAmtRefundSingle());
                if (eqZero.test(amtRfnSingle)) {
                    amtRfnSingle = amtRefund.divide(qtyRefund, 2, RoundingMode.HALF_EVEN);
                }
                amtRefund = qtyIn.multiply(amtRfnSingle);
                o.setAmtRefund(amtRefund);
                amt.value = amt.value.add(amtRefund);
                flag = false;
                if (item != null) {
                    setOrderQtyCsm.accept(item, o);
                }
            } else {

                o.setAmtRefund(BigDecimal.ZERO);
                flag = false;
            }
        }
        rtn.setProReturnStatus(ProReturnStatusEnum.WHOLE.getVal());
        if (!flag) {

            BigDecimal shipAmt = initDecimal.apply(rtn.getShipAmt());
            BigDecimal returnAmtOther = initDecimal.apply(rtn.getReturnAmtOther());
            BigDecimal exchangeAmt = initDecimal.apply(rtn.getExchangeAmt());
            BigDecimal amtActual = amt.value.add(shipAmt).add(returnAmtOther).subtract(exchangeAmt);
            rtn.setReturnAmtList(amt.value);
            rtn.setReturnAmtActual(amtActual);
            rtn.setProReturnStatus(ProReturnStatusEnum.PORTION.getVal());
        }
        return flag;
    }

    /**
     * 返回信息
     *
     * @param msg  输出信息
     * @param bool 结果
     * @param usr  登录用户
     * @return VH
     */
    private ValueHolderV14 valueHolderResult(String msg, boolean bool, User usr) {
        ValueHolderV14 v = new ValueHolderV14();
        if (bool) {
            v.setCode(ResultCode.SUCCESS);
            v.setMessage(Resources.getMessage("SUCCESS: " + msg, usr.getLocale()));
        } else {
            v.setCode(ResultCode.FAIL);
            v.setMessage(Resources.getMessage("FAILED: " + msg, usr.getLocale()));
        }
        return v;
    }

    /**
     * 调用天猫在线换货入库确认服务,店铺自动审核策略
     *
     * @param rtn       OcBReturnOrder
     * @param loginUser User
     */
    private void autoAuditReturnOrderService(OcBReturnOrder rtn, User loginUser) {

        invokeTanMaoOnlineRefundInService(rtn, loginUser);

        invokeShopAutoAuditStrategy(rtn, loginUser);

    }

    /**
     * 调用天猫在线换货入库确认服务
     *
     * @param rtn       OcBReturnOrder
     * @param loginUser User
     */
    private void invokeTanMaoOnlineRefundInService(OcBReturnOrder rtn, User loginUser) {

        Long tbDisputeId = rtn.getTbDisputeId();
        labelExIn:
        if (tbDisputeId != null) {
            Long rtnOrderId = rtn.getId();
            if (rtnOrderId == null) {
                log.error(LogUtil.format("调用天猫在线换货入库确认服务异常. -> 退单号编号.ID Is Null."));
                break labelExIn;
            }
            try {
                ValueHolderV14 vh = exchangeInService.exchangeIn(rtnOrderId, loginUser);
                if (vh != null) {
                    boolean b = ResultCode.SUCCESS == vh.getCode() ? true : false;
                    if (!b) {
                        log.error(LogUtil.format("调用天猫在线换货入库确认服务失败, Msg: {},状态码=", vh.getCode()), vh.getMessage());
                    }
                } else {
                    log.error(LogUtil.format("调用天猫在线换货入库确认服务返回值异常. 返回值为Null"));
                }
            } catch (Exception e) {
                log.error(LogUtil.format("调用天猫在线换货入库确认服务发生异常,error:{}"), Throwables.getStackTraceAsString(e));
            }
        }
    }

    /**
     * 调用店铺自动审核策略
     *
     * @param ocBReturnOrder OcBReturnOrder
     * @param loginUser      User
     */
    private boolean invokeShopAutoAuditStrategy(OcBReturnOrder ocBReturnOrder, User loginUser) {
        Long cpCShopId = ocBReturnOrder.getCpCShopId();
        if (cpCShopId == null) {
            log.error(LogUtil.format("OcBRetailReturnUpdateRefundInService.调用店铺自动审核异常: 店铺ID.cpCShopId Is Null"));
            return false;
        }
        try {
            boolean iShopStrategy = omsOrderStCAutocheckService.isExitsRefundOrderStrategy(cpCShopId);
            if (iShopStrategy) {
                JSONObject jo = new JSONObject();
                jo.put("ID", ocBReturnOrder.getId());
                ValueHolderV14 vh = returnOrderAutoAuditService.returnOrderAutoAudit(jo, loginUser);
                if (vh != null) {
                    boolean b = ResultCode.SUCCESS == vh.getCode() ? true : false;
                    if (b) {
                        return true;
                    } else {
                        log.error(LogUtil.format("OcBRetailReturnUpdateRefundInService.调用:退单自动审核服务失败: {}"),
                                vh.getMessage());
                    }
                } else {
                    log.error(LogUtil.format("OcBRetailReturnUpdateRefundInService.Error: 调用退单自动审核服务返回值异常. 返回值为Null"));
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("OcBRetailReturnUpdateRefundInService.Exp:服务发生异常. ex:{}"),
                    Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    /**
     * @param rtn           OcBReturnOrder
     * @param rtnOdrRfnList List<OcBReturnOrderRefund>
     * @param matchStu      boolean
     * @param usr           User
     * @param orderItems    orderItem
     */
    @Transactional(rollbackFor = NDSException.class)
    public void updateDataMethods(OcBReturnOrder rtn, List<OcBReturnOrderRefund> rtnOdrRfnList, boolean matchStu,
                                  User usr, List<OcBOrderItem> orderItems) {

        Set<Long> itmSet = new HashSet<>();
        try {
            // 1. 退单主,子
            ocBReturnOrderMapper.updateOcBreturnOrderInfoById(rtn);
            //入库时间埋点
            nodeRecordService.insertByNode(ReturnOrderNodeEnum.OMS_STORE_IN_TIME,rtn.getInTime(),rtn.getId(),usr);
            for (OcBReturnOrderRefund x : rtnOdrRfnList) {
                if (x.getOcBOrderItemId() != null) {
                    itmSet.add(x.getOcBOrderItemId());
                }
                ocBReturnOrderRefundMapper.updateReturnOdrRefundQtyAndAmt(x);
            }
            // 2. 全渠道
            int odrStu = OcBOrderConst.ORDER_RETURN_STATUS_PORTION;
            if (matchStu) {
                odrStu = OcBOrderConst.ORDER_RETURN_STATUS_ALL;
            }
            //更新零售单，发货方入库状态未为 1-是
            OcBOrder updateOcBOrder = new OcBOrder();
            updateOcBOrder.setId(rtn.getOrigOrderId());
            updateOcBOrder.setDeliveryInStatus("1");
            updateOcBOrder.setReturnStatus(odrStu);
            BaseModelUtil.makeBaseModifyField(updateOcBOrder,usr);
            ocBOrderMapper.updateById(updateOcBOrder);
            for (OcBOrderItem orderItem : orderItems) {
                Long id = orderItem.getId();
                if (itmSet.contains(id)) {
                    ocBorderItemMapper.updateOrderItemReturnQty(orderItem);
                }
            }

            // 3. 日志
            List<OcBReturnOrderLog> rtnLogList = new ArrayList<>();
            writeReturnOderLog(rtn, usr, rtnLogList);
            recordLogMsg("OcBRetailReturnUpdateRefundInService.persistDataMethods: Data Persistence Success");

        } catch (Exception e) {
            log.error(LogUtil.format("OcBRetailReturnUpdateRefundInService.persistDataMethods Error: {}"),
                    Throwables.getStackTraceAsString(e));
            throw new NDSException("Exceptions Occur When Data Storage And Push ES");
        }
    }

    /**
     * 入库日志
     *
     * @param rtnOrder   OcBReturnOrder
     * @param usr        loginUser
     * @param rtnLogList 日志
     */
    private void writeReturnOderLog(OcBReturnOrder rtnOrder, User usr, List<OcBReturnOrderLog> rtnLogList) {
        OcBReturnOrderLog rtnLg = new OcBReturnOrderLog();
        rtnLg.setLogType("退货单入库");
        rtnLg.setIpAddress(usr.getLastloginip());
        rtnLg.setAdOrgId(Long.valueOf(usr.getOrgId()));
        rtnLg.setAdClientId(Long.valueOf(usr.getClientId()));
        rtnLg.setOwnerid(Long.valueOf(usr.getId()));
        rtnLg.setOwnername(usr.getName());
        rtnLg.setOwnerename(usr.getEname());
        rtnLg.setCreationdate(new Date());
        rtnLg.setModifierid(Long.valueOf(usr.getId()));
        rtnLg.setModifiername(usr.getName());
        rtnLg.setModifierename(usr.getEname());
        rtnLg.setModifieddate(new Date());
        rtnLg.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
        rtnLg.setUserName(usr.getName());

        StringBuilder content = new StringBuilder("入库结果单入库完成[更新]，入库条码: ");
        Long id = Tools.getSequence(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_LOG_TYPE_NAME);
        rtnLg.setId(id);
        String contentString = content.append(rtnOrder.getAllSku()).toString();
        rtnLg.setLogMessage(contentString);
        rtnLg.setOcBReturnOrderId(rtnOrder.getId());
        ocBReturnOrderLogMapper.insert(rtnLg);
        rtnLogList.add(rtnLg);

    }

    /***
     * 记录Debug级别日志
     * @param msg 日志信息
     */
    private void recordLogMsg(String msg) {
        if (log.isDebugEnabled()) {
            log.debug(msg);
        }
    }

    /**
     * 退单添加仓库.入库人信息
     *
     * @param rtn OcBReturnOrder
     * @param spr SgBPhyInResultExt
     * @param usr User
     */
    private void addReturnStoreInfo(OcBReturnOrder rtn, SgBPhyInResultExt spr, User usr) {
        rtn.setIsTodrp(OcBOrderConst.IS_STATUS_IY);
        rtn.setInerId(Long.valueOf(usr.getId()));
        rtn.setInerName(usr.getName());
        rtn.setInerEname(usr.getEname());
        rtn.setInTime(new Date());
        if (spr.getCpCPhyWarehouseId() != null) {
            rtn.setCpCPhyWarehouseInId(spr.getCpCPhyWarehouseId());
        }
        if (spr.getCpCStoreId() != null) {
            rtn.setCpCStoreId(spr.getCpCStoreId());
        }
        setStoreNature(rtn);
        if (StringUtils.isNotEmpty(spr.getCpCStoreEcode())) {
            rtn.setCpCStoreEcode(spr.getCpCStoreEcode());
        }
        if (StringUtils.isNotEmpty(spr.getCpCStoreEcode())) {
            rtn.setCpCStoreEname(spr.getCpCstoreEname());
        }
    }

    private void setStoreNature(OcBReturnOrder returnOrder) {
        if (ObjectUtils.isEmpty(returnOrder)) {
            return;
        }
        Long storeId = returnOrder.getStoreId();
        if (ObjectUtils.isEmpty(storeId)) {
            return;
        }
        CpStore cpStore = cpRpcService.selectCpCStoreById(storeId);
        if (ObjectUtils.isEmpty(cpStore)) {
            return;
        }
        returnOrder.setStoreNature(cpStore.getStorenature());
    }
}
