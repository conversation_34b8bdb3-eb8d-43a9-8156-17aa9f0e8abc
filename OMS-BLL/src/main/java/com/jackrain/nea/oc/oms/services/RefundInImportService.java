package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4RefundIn;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-08-26 10:29
 */
@Slf4j
@Component
public class RefundInImportService {

    private static final String INDEX = "oc_b_refund_in_log";
    private static final String TYPE = "oc_b_refund_in_log";

    @Autowired
    OcBRefundInMapper ocBRefundInMapper;

    @Autowired
    OcBRefundInLogMapper ocBRefundInLogMapper;


    public ValueHolderV14 batchImport(List<OcBRefundIn> ocBRefundInList, Boolean cover, User user) {
        ValueHolderV14 result = new ValueHolderV14();
        if (CollectionUtils.isEmpty(ocBRefundInList)) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("获取不到导入的信息");
            return result;
        }
        //todo 校验物流单号是否重复
        List<OcBRefundIn> ocBRefundIndist = ocBRefundInList.stream().distinct().collect(Collectors.toList());
        if (ocBRefundIndist.size() != ocBRefundInList.size()) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("存在重复的物流单号");
            return result;
        }


        for (OcBRefundIn ocBRefundIn : ocBRefundInList) {
            try {
                if (StringUtils.isEmpty(ocBRefundIn.getLogisticNumber())) {
                    continue;
                }
                this.handleRefundIn(ocBRefundIn, cover, user);
            } catch (Exception e) {
                log.error(LogUtil.format("物流单号:{},异常信息为:{}",ocBRefundIn.getLogisticNumber()),ocBRefundIn.getLogisticNumber(), Throwables.getStackTraceAsString(e));
            }
        }
        result.setCode(ResultCode.SUCCESS);
        result.setMessage("导入成功");
        return result;

    }

    /**
     * 退货入库单批量处理
     *
     * @param ocBRefundIn
     * @param cover
     * @param user
     */
    public void handleRefundIn(OcBRefundIn ocBRefundIn, Boolean cover, User user) {
        RefundInImportService bean = ApplicationContextHandle.getBean(RefundInImportService.class);
        if (StringUtils.isEmpty(ocBRefundIn.getLogisticNumber())) {
            return;
        }
        //根据物流单号es查询退货入库id
        JSONArray data = ES4RefundIn.getReturnStorageIds(ocBRefundIn.getLogisticNumber());
        if (Objects.isNull(data) || data.size() < 1){
            return;
        }
        List<OcBRefundIn> ocBRefundInList = JSONObject.parseArray(data.toJSONString(), OcBRefundIn.class);
        for (OcBRefundIn ocBRefundIns : ocBRefundInList) {
            try {
                OcBRefundIn ocBRefundInUpdate = ocBRefundInMapper.selectById(ocBRefundIns.getId());
                ocBRefundInUpdate.setId(ocBRefundIns.getId());
                ocBRefundInUpdate.setModifieddate(new Date());
                ocBRefundInUpdate.setRemarkHandle(ocBRefundIn.getRemarkHandle());
                ocBRefundInUpdate.setHandler(ocBRefundIn.getHandler());
                if (cover) {
                    if (StringUtils.isEmpty(ocBRefundIns.getRemarkHandle())) {
                        ocBRefundInUpdate.setRemarkHandle(ocBRefundIn.getRemarkHandle());
                    } else {
                        ocBRefundInUpdate.setRemarkHandle(ocBRefundIns.getRemarkHandle() + "," + ocBRefundIn.getRemarkHandle());
                    }
                }
                bean.updateReturnIn(ocBRefundInUpdate, user);
            } catch (Exception e) {
                log.error(LogUtil.format("退单导入异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            }

        }
        return;
    }

    /**
     * 更新退货入库单,和新增日志信息并推es
     *
     * @param ocBRefundIn
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateReturnIn(OcBRefundIn ocBRefundIn, User user) {
        //更新退货入库表
        ocBRefundInMapper.updateById(ocBRefundIn);
       /* //推ES
        try {
            boolean flag = SpecialElasticSearchUtil.indexDocument(
                    OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME,
                    OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME,
                    ocBRefundIn, ocBRefundIn.getId());
            if (!flag) {
                throw new NDSException(ocBRefundIn.getId() + "退单编号推ES失败");
            }
        } catch (Exception e) {
            throw new NDSException(ocBRefundIn.getId() + "退单编号推ES失败");
        }*/
        OcBRefundInLog ocBRefundInLog = new OcBRefundInLog();
        Long logId = ModelUtil.getSequence("oc_b_refund_in_log");
        ocBRefundInLog.setId(logId);
        ocBRefundInLog.setOmsonlineorderid(ocBRefundIn.getId());
        ocBRefundInLog.setLogtype("修改处理人:处理人备注");
        ocBRefundInLog.setLogmessage(ocBRefundIn.getHandler() + ":" + ocBRefundIn.getRemarkHandle());
        ocBRefundInLog.setIpaddress(user.getLastloginip());
        ocBRefundInLog.setOwnerid(user.getId() + 0L);
        ocBRefundInLog.setOwnername(user.getName());
        ocBRefundInLog.setOwnerename(user.getEname());
        ocBRefundInLog.setCreationdate(new Date());
        ocBRefundInLog.setAdClientId(user.getClientId() + 0L);
        ocBRefundInLog.setAdOrgId(user.getOrgId() + 0L);
        int result = ocBRefundInLogMapper.insert(ocBRefundInLog);
       /* if (result > 0) {
            try {
                SpecialElasticSearchUtil.indexDocument(INDEX, TYPE, ocBRefundInLog, logId);
            } catch (Exception e) {
                e.printStackTrace();
                log.debug("退货入库单推送es异常");
            }
        }*/
    }
}
