package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;

/**
 * <p>重新计算金额服务</>
 *
 * @author: heliu
 * @since: 2019/3/23
 * create at : 2019/3/23 13:51
 */
@Component
@Slf4j
public class OmsOrderRecountAmountService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    /**
     * 重新计算金额
     *
     * @param orderInfo             订单对象
     * @param orderRefundItemList   退款成功的明细
     * @param orderUnRefundItemList 未退款成功的明细
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean doRecountAmount(OcBOrderRelation orderInfo, List<OcBOrderItem> orderRefundItemList,
                                   List<OcBOrderItem> orderUnRefundItemList) {

        OcBOrder recountOrderInfo = new OcBOrder();
        try {
            //调整金额合计
            BigDecimal adJustAmountTotal = BigDecimal.ZERO;
            //优惠金额合计
            BigDecimal amtDiscountAmont = BigDecimal.ZERO;
            //订单优惠金额合计
            BigDecimal orderAmtDiscountAmont = BigDecimal.ZERO;
            //商品数量
            BigDecimal goodsOrderItemCount = BigDecimal.ZERO;
            //商品总额
            BigDecimal proCount = BigDecimal.ZERO;
            //针对已退款的明细
            for (OcBOrderItem refundDetailItem : orderRefundItemList) {
                //退货数量：商品数量
                refundDetailItem.setQtyRefund(refundDetailItem.getQty() == null ? BigDecimal.ZERO : refundDetailItem.getQty());
                //成交价 若数量-已退数量为0则“成交价”为0
                if ((((refundDetailItem.getQty() == null ? BigDecimal.ZERO : refundDetailItem.getQty())
                        .subtract(refundDetailItem.getQtyRefund() == null ? BigDecimal.ZERO
                                : refundDetailItem.getQtyRefund())).compareTo(BigDecimal.ZERO)) == 0) {
                    refundDetailItem.setPrice(BigDecimal.ZERO);
                }
                //平摊金额
                refundDetailItem.setOrderSplitAmt(BigDecimal.ZERO);
                refundDetailItem.setPrice(BigDecimal.ZERO);
                refundDetailItem.setAmtDiscount(BigDecimal.ZERO);
                refundDetailItem.setAdjustAmt(BigDecimal.ZERO);
                refundDetailItem.setRealAmt(BigDecimal.ZERO);
                refundDetailItem.setPriceActual(BigDecimal.ZERO);
                refundDetailItem.setOcBOrderId(orderInfo.getOrderInfo().getId());
                //更新退款的明细
                omsOrderItemService.updateOcBOrderItem(refundDetailItem, orderInfo.getOrderInfo().getId());
            }
            //计算非退款的明细去更新主表
            //排序正序，且空值排前面
            orderUnRefundItemList.sort(Comparator.comparing(OcBOrderItem::getPrice, Comparator.nullsFirst(Comparator.naturalOrder())));
            for (OcBOrderItem unRefundDetailItem : orderUnRefundItemList) {
                //调整金额
                adJustAmountTotal = adJustAmountTotal.add(unRefundDetailItem.getAdjustAmt() == null ? BigDecimal.ZERO : unRefundDetailItem.getAdjustAmt());
                //优惠金额,如果优惠金额大于成交金额的话，默认优惠金额等于成交金额；2020/2/12追加逻辑，2/23号提交
                amtDiscountAmont = amtDiscountAmont.add(unRefundDetailItem.getAmtDiscount() == null ? BigDecimal.ZERO : unRefundDetailItem.getAmtDiscount());
//                if(amtDiscountAmont.compareTo(unRefundDetailItem.getPriceList() == null ? BigDecimal.ZERO : unRefundDetailItem.getPriceList())>0){
//                    amtDiscountAmont = unRefundDetailItem.getPriceList();
//                }
                //整体平摊金额
                orderAmtDiscountAmont = orderAmtDiscountAmont.add(unRefundDetailItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : unRefundDetailItem.getOrderSplitAmt());
                //商品数量 明细“商品数量”-“已退数量”合计
                goodsOrderItemCount = goodsOrderItemCount.add((unRefundDetailItem.getQty()
                        == null ? BigDecimal.ZERO : unRefundDetailItem.getQty()).subtract(unRefundDetailItem.getQtyRefund()
                        == null ? BigDecimal.ZERO : unRefundDetailItem.getQtyRefund()).abs());
                //商品总额 明细中“标准价”*（“商品数量”-“已退数量”）的绝对值合计
                proCount = proCount.add((unRefundDetailItem.getPrice()
                        == null ? BigDecimal.ZERO : unRefundDetailItem.getPrice()).multiply(((unRefundDetailItem.getQty()
                        == null ? BigDecimal.ZERO : unRefundDetailItem.getQty()).subtract(unRefundDetailItem.getQtyRefund()
                        == null ? BigDecimal.ZERO : unRefundDetailItem.getQtyRefund()).abs())));
            }
            //已付金额
            //调整金额
            recountOrderInfo.setAdjustAmt(adJustAmountTotal);
            //商品总额
            recountOrderInfo.setProductAmt(proCount);
            //商品数量
            recountOrderInfo.setQtyAll(goodsOrderItemCount);
            //商品优惠金额
            recountOrderInfo.setProductDiscountAmt(amtDiscountAmont);
            //订单优惠金额
            recountOrderInfo.setOrderDiscountAmt(orderAmtDiscountAmont);
            recountOrderInfo.setId(orderInfo.getOrderId());
            BigDecimal orderAmt = proCount.add(orderInfo.getOrderInfo().getShipAmt()  == null ? BigDecimal.ZERO : orderInfo.getOrderInfo().getShipAmt())
                    .add(adJustAmountTotal)
                    .add(orderInfo.getOrderInfo().getServiceAmt() == null ? BigDecimal.ZERO : orderInfo.getOrderInfo().getServiceAmt())
                    .subtract(amtDiscountAmont).subtract(orderAmtDiscountAmont);

            recountOrderInfo.setOrderAmt(orderAmt);
            recountOrderInfo.setReceivedAmt(orderAmt);
            omsOrderService.updateOrderInfo(recountOrderInfo);
            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("执行重新计算金额异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 根据明细计算主表金额
     *
     * @param ocBOrder
     * @param orderRefundItemList
     */
    public void doRecountAmount(OcBOrder ocBOrder, List<OcBOrderItem> orderRefundItemList) {
        try {
            //调整金额合计
            BigDecimal adJustAmountTotal = BigDecimal.ZERO;
            //优惠金额合计
            BigDecimal amtDiscountAmont = BigDecimal.ZERO;
            //订单优惠金额合计
            BigDecimal orderAmtDiscountAmont = BigDecimal.ZERO;
            //商品总额
            BigDecimal proCount = BigDecimal.ZERO;
            //计算非退款的明细去更新主表
            //排序正序，且空值排前面
            for (OcBOrderItem unRefundDetailItem : orderRefundItemList) {
                //调整金额
                adJustAmountTotal = adJustAmountTotal.add(unRefundDetailItem.getAdjustAmt() == null ? BigDecimal.ZERO : unRefundDetailItem.getAdjustAmt());
                //优惠金额,如果优惠金额大于成交金额的话，默认优惠金额等于成交金额；2020/2/12追加逻辑，2/23号提交
                amtDiscountAmont = amtDiscountAmont.add(unRefundDetailItem.getAmtDiscount() == null ? BigDecimal.ZERO : unRefundDetailItem.getAmtDiscount());
//                if(amtDiscountAmont.compareTo(unRefundDetailItem.getPriceList() == null ? BigDecimal.ZERO : unRefundDetailItem.getPriceList())>0){
//                    amtDiscountAmont = unRefundDetailItem.getPriceList();
//                }
                //整体平摊金额
                orderAmtDiscountAmont = orderAmtDiscountAmont.add(unRefundDetailItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : unRefundDetailItem.getOrderSplitAmt());

                //商品总额 明细中“标准价”*（“商品数量”-“已退数量”）的绝对值合计
                proCount = proCount.add((unRefundDetailItem.getPrice()
                        == null ? BigDecimal.ZERO : unRefundDetailItem.getPrice()).multiply(((unRefundDetailItem.getQty()
                        == null ? BigDecimal.ZERO : unRefundDetailItem.getQty()).subtract(unRefundDetailItem.getQtyRefund()
                        == null ? BigDecimal.ZERO : unRefundDetailItem.getQtyRefund()).abs())));
            }
            //调整金额
            ocBOrder.setAdjustAmt(adJustAmountTotal);
            //商品总额
            ocBOrder.setProductAmt(proCount);
            //商品优惠金额
            ocBOrder.setProductDiscountAmt(amtDiscountAmont);
            //订单优惠金额
            ocBOrder.setOrderDiscountAmt(orderAmtDiscountAmont);
            BigDecimal orderAmt = proCount.add(ocBOrder.getShipAmt() == null ? BigDecimal.ZERO: ocBOrder.getShipAmt())
                    .add(adJustAmountTotal)
                    .add(ocBOrder.getServiceAmt() == null ? BigDecimal.ZERO: ocBOrder.getServiceAmt())
                    .subtract(amtDiscountAmont).subtract(orderAmtDiscountAmont);

            ocBOrder.setOrderAmt(orderAmt);
            ocBOrder.setReceivedAmt(orderAmt);
        } catch (Exception ex) {
            log.error(LogUtil.format("执行重新计算金额异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
    }
}