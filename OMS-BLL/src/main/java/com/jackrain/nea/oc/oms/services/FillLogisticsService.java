package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StdRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 申请补充物流信息
 * @date 2021/12/8 11:19
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class FillLogisticsService {

    private final OcBReturnOrderMapper ocBReturnOrderMapper;

    private final StdRpcService stdRpcService;

    private final CpRpcService cpRpcService;

    private final ReturnOrderLogService returnOrderLogService;

    /**
     * @description:  申请补充物流信息
     * @param obj
     * @param user
     * @return: com.jackrain.nea.sys.domain.ValueHolderV14
     * <AUTHOR>
     * @date: 2021/12/8 11:21
     */
    public ValueHolderV14 fillLogistics(JSONObject obj, User user){
        if (log.isDebugEnabled()) {
            log.debug("申请补充物流信息入参：{}", obj.toJSONString());
        }
        ValueHolderV14 vh = new ValueHolderV14();
        // 根据原单id去查询退单 记录不存在，则提示：“当前记录已不存在！
        JSONArray ids = obj.getJSONArray("ids");
        try {
            if (!ids.isEmpty()) {
                List<Long> idList = ids.stream().map(e -> NumberUtils.toLong(e+"")).collect(Collectors.toList());
                List<OcBReturnOrder> ocBReturnOrderList = ocBReturnOrderMapper.selectList(Wrappers.<OcBReturnOrder>lambdaQuery().in(OcBReturnOrder::getId, idList));
                if(CollectionUtils.isNotEmpty(ocBReturnOrderList)){
                    long owCount = ocBReturnOrderList.stream().filter(e ->{
                        if(e.getCpCPhyWarehouseInId() == null){
                            throw new RuntimeException("入库实体仓库不能为空");
                        }
                        return ObjectUtils.equals(PlatFormEnum.OFFICIAL_WEBSITE.getCode(), e.getPlatform());
                    }).count();
                    if(owCount != idList.size()){
                        throw new RuntimeException("仅支持官网订单进行申请！");
                    }
                    long notWing = ocBReturnOrderList.stream().filter(e -> !"2".equals(e.getToDrpStatus())).count();
                    if(notWing != idList.size()){
                        throw new RuntimeException("退单已传Wing成功，无法申请！");
                    }
                    ValueHolderV14 valueHolderV14 = this.stdCmd(ocBReturnOrderList);
                    if(valueHolderV14.isOK()){
                        //添加退换货单日志
                        List<OcBReturnOrderLog> logList = ocBReturnOrderList.stream().map(e -> returnOrderLogService.buildReturnOrderLog(e.getId(), "申请补充物流信息", "申请补充物流信息操作成功", user)).filter(e->e!=null).collect(Collectors.toList());
                        returnOrderLogService.batchInsertLog(logList);
                        vh.setCode(ResultCode.SUCCESS);
                        vh.setMessage("申请补充物流信息操作成功");
                    }else{
                        vh.setCode(ResultCode.FAIL);
                        vh.setMessage("申请补充物流信息操作失败，失败详情："+valueHolderV14.getMessage());
                    }
                }else{
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("所选中的数据不存在");
                }
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("请选择一条记录", user.getLocale()));
            }
        }catch (Exception e){
            log.error("申请补充物流信息操作失败，失败详情："+e.getMessage(),e);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage(), user.getLocale()));
        }
        return vh;
    }


    private ValueHolderV14 stdCmd(List<OcBReturnOrder> ocBReturnOrderList){
        List<Long> warehouseIds = ocBReturnOrderList.stream().map(OcBReturnOrder::getCpCPhyWarehouseInId).distinct().collect(Collectors.toList());
        Map<Long, CpCPhyWarehouse> longCpCPhyWarehouseMap = cpRpcService.rpcQueryCpCPhyWareHouses(warehouseIds);
        JSONObject jsonObject = new JSONObject();
        JSONArray dataArray = new JSONArray();
        ocBReturnOrderList.stream().forEach(e->{
            JSONObject owObject = new JSONObject();
            owObject.put("tid",e.getReturnId());
            CpCPhyWarehouse cpCPhyWarehouse = longCpCPhyWarehouseMap.get(e.getCpCPhyWarehouseInId());
            if(cpCPhyWarehouse == null){
                throw new RuntimeException("退货入库实体仓不存在【"+e.getCpCPhyWarehouseInId()+"】");
            }
            owObject.put("returnAddress",cpCPhyWarehouse.getSendAddress());
            owObject.put("orderNo",e.getTid());
            dataArray.add(owObject);
        });
        jsonObject.put("data",dataArray);
        return stdRpcService.refundApprove(jsonObject, ocBReturnOrderList.get(0).getPlatform());
    }
}
