package com.jackrain.nea.oc.oms.mapperservice;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryFailMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDeliveryFail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName OcBOrderDeliveryFailMapperService
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/5/16 19:12
 * @Version 1.0
 */
@Service
@Slf4j
public class OcBOrderDeliveryFailMapperService extends ServiceImpl<OcBOrderDeliveryFailMapper, OcBOrderDeliveryFail> {

    @Override
    public boolean updateById(OcBOrderDeliveryFail deliveryFail) {
        Assert.notNull(deliveryFail.getOcBOrderId(), "OcBOrderId不能为空");
        Assert.notNull(deliveryFail.getId(), "id不能为空");
        Wrapper<OcBOrderDeliveryFail> updateWrapper = new UpdateWrapper<OcBOrderDeliveryFail>().lambda()
                .eq(OcBOrderDeliveryFail::getOcBOrderId, deliveryFail.getOcBOrderId())
                .eq(OcBOrderDeliveryFail::getId, deliveryFail.getId());
        return baseMapper.update(deliveryFail, updateWrapper) > 0;
    }
}
