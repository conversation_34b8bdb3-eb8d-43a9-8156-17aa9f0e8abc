package com.jackrain.nea.oc.oms.services;

import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.InterceptStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.StandplatRefundOrderTransferUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/6/24 1:50 下午
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsStandPlatAfterRefundOnlyService {


    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private OmsStandPlatRefundOrderService omsStandPlatRefundOrderService;

    @Autowired
    private StandplatRefundOrderTransferUtil standplatRefundOrderTransferUtil;


    public List<OcBReturnOrderRelation> goodsAfterRefundOnly(OmsOrderRelation orderRelation,
                                                             IpBStandplatRefund ipBStandplatRefund,
                                                             User user) {
        List<OcBReturnOrderRelation> returnOrderRelations = new ArrayList<>();
        OcBOrder ocBOrder = orderRelation.getOcBOrder();
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        List<OcBOrderItem> giftItem = new ArrayList<>();
        List<OmsOrderRelation.OcOrderGifts> ocOrderGifts = orderRelation.getOcOrderGifts();
        if (CollectionUtils.isNotEmpty(ocOrderGifts)) {
            for (OmsOrderRelation.OcOrderGifts ocOrderGift : ocOrderGifts) {
                giftItem.addAll(ocOrderGift.getOcBOrderGifts());
            }
        }
        //   List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        List<OcBOrderItem> orderItems = orderRelation.getOcBOrderItems();
        Map<String, List<OcBOrderDelivery>> deliveriesMap = new HashMap<>(10);
        List<OcBOrderDelivery> orderDeliveries = orderRelation.getOrderDeliveries();
        //合并包裹信息
        for (OcBOrderDelivery orderDelivery : orderDeliveries) {
            String logisticNumber = orderDelivery.getLogisticNumber();
            if (!deliveriesMap.containsKey(logisticNumber)) {
                List<OcBOrderDelivery> orderDeliveryList = new ArrayList<>();
                orderDeliveryList.add(orderDelivery);
                deliveriesMap.put(logisticNumber, orderDeliveryList);
            } else {
                List<OcBOrderDelivery> deliveries = deliveriesMap.get(logisticNumber);
                deliveries.add(orderDelivery);
                deliveriesMap.put(logisticNumber, deliveries);
            }
        }
        //判断当前包裹的物流状态
        List<Long> refundIds = new ArrayList<>();
        BigDecimal qtyCount = ocBOrderItems.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<OcBReturnOrderRelation> relations = new ArrayList<>();

        List<OcBReturnOrderRelation> relationGifts = new ArrayList<>();
        BigDecimal qty = BigDecimal.ZERO;

        boolean flag = false;
        for (String logisticNumber : deliveriesMap.keySet()) {
            //判断该物流单号时候已经生成退换货单
            List<Long> refundId = this.checkReturnExistByLogisticsNo(logisticNumber, ipBStandplatRefund.getReturnNo());
            if (CollectionUtils.isEmpty(refundId)) {
                List<OcBOrderDelivery> deliveries = deliveriesMap.get(logisticNumber);
                BigDecimal qtyDelivery = deliveries.stream().map(OcBOrderDelivery::getQty).reduce(BigDecimal.ZERO,
                        BigDecimal::add);
                OcBReturnOrderRelation relation = this.logisticsIntercept(ocBOrder, orderItems,
                        deliveries, ipBStandplatRefund, 0, TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT,
                        user);
                if (relation == null) {
                    continue;
                }
                if (this.checkAllGift(giftItem, deliveries)) {
                    relation.getReturnOrderInfo().setIntercerptStatus(InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());
                    relationGifts.add(relation);
                    continue;
                }
                Boolean intercerpt = handleParcel(deliveries, ocBOrderItems);
                if (intercerpt != null && intercerpt) {
                    //需要发起拦截
                    relation.getReturnOrderInfo().setIntercerptStatus(InterceptStatus.NEED_INTERCEPT.getCode());
                    returnOrderRelations.add(relation);
                    flag = true;
                } else if (intercerpt == null) {
                    relation.getReturnOrderInfo().setIntercerptStatus(InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());
                    returnOrderRelations.add(relation);
                } else {
                    relation.getReturnOrderInfo().setIntercerptStatus(InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());
                    qty = qty.add(qtyDelivery);
                    relations.add(relation);
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(this.getClass().getName() + " 退换货已存在!物流单号:{},退换货id:{}", logisticNumber, refundId);
                }
                refundIds.addAll(refundId);
            }
        }

        if (CollectionUtils.isNotEmpty(relationGifts)) {
            if (flag) {
                for (OcBReturnOrderRelation relationGift : relationGifts) {
                    relationGift.getReturnOrderInfo().setIntercerptStatus(InterceptStatus.NEED_INTERCEPT.getCode());
                    relations.add(relationGift);
                }
            } else {
                relations.addAll(relationGifts);
            }
        }
        if (CollectionUtils.isNotEmpty(refundIds)) {
            omsStandPlatRefundOrderService.foundRefundSlipAfter(refundIds, ocBOrder, ipBStandplatRefund, user, null);
        }
        if (qtyCount.compareTo(qty) == 0) {
            for (OcBReturnOrderRelation relation : relations) {
                //需要发起拦截
                relation.getReturnOrderInfo().setIntercerptStatus(InterceptStatus.NEED_INTERCEPT.getCode());
            }
        }
        returnOrderRelations.addAll(relations);

        // @20200825 重算退换货金额
        OmsReturnOrderService.getInstance().reCalculateReturnAmt(returnOrderRelations);

        return returnOrderRelations;
    }

    /**
     * 处理包裹信息 是否需要拦截
     *
     * @param deliveries    一个包裹
     * @param ocBOrderItems 需要退的明细
     * @return
     */
    private Boolean handleParcel(List<OcBOrderDelivery> deliveries, List<OcBOrderItem> ocBOrderItems) {
        //退款的sku信息
        String psCSkuEcode = ocBOrderItems.get(0).getPsCSkuEcode();
        //获取退款明细的总数量
        BigDecimal qtyCount = ocBOrderItems.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        //判断包裹里是否都是退款条码
        OcBOrderDelivery ocBOrderDelivery = deliveries.get(0);
        if (deliveries.size() == 1 && ocBOrderDelivery.getPsCSkuEcode().equals(psCSkuEcode)
                && qtyCount.compareTo(ocBOrderDelivery.getQty()) == 0) {
            return true;
        } else if (ocBOrderDelivery.getPsCSkuEcode().equals(psCSkuEcode)
                && qtyCount.compareTo(ocBOrderDelivery.getQty()) != 0) {
            return false;
        }
        return null;
    }


    /**
     * 拦截(对一个包裹进行处理)
     *
     * @param ocBOrder
     * @param ocBOrderItems 当前订单下所有的明细信息
     * @param deliveries
     */
    private OcBReturnOrderRelation logisticsIntercept(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems,
                                                      List<OcBOrderDelivery> deliveries,
                                                      IpBStandplatRefund ipBStandplatRefund, Integer interceptMark,
                                                      TaobaoReturnOrderExt.ReturnBillsStatus billsStatus,
                                                      User user) {
        //判断包裹是否有多个sku 获取sku的数量
        Map<String, BigDecimal> skuCount = new HashMap<>();
        for (OcBOrderDelivery delivery : deliveries) {
            String psCSkuEcode = delivery.getPsCSkuEcode();
            if (!skuCount.containsKey(psCSkuEcode)) {
                skuCount.put(psCSkuEcode, delivery.getQty());
            } else {
                BigDecimal qty = skuCount.get(psCSkuEcode);
                skuCount.put(psCSkuEcode, delivery.getQty().add(qty));
            }
        }
        OmsOrderRelation orderRelation = new OmsOrderRelation();
        orderRelation.setOcBOrder(ocBOrder);
        orderRelation.setOcBOrderItems(ocBOrderItems);
        orderRelation.setInterceptMark(interceptMark);
        return standplatRefundOrderTransferUtil.standplatRefundOrderToReturnOrder(orderRelation,
                ipBStandplatRefund, skuCount, deliveries.get(0), billsStatus, user);
    }


    /**
     * 根据物流单号判断退换货订单是否存在(拦截是以包裹维度生成)
     */
    public List<Long> checkReturnExistByLogisticsNo(String logisticNumber, String returnId) {
        if (StringUtils.isEmpty(logisticNumber) || StringUtils.isBlank(returnId)) {
            return Lists.newArrayListWithExpectedSize(0);
        }
        //获取物流单号
        //根据物流单号查询退换单是否存在
        Set<Long> ids = ES4ReturnOrder.findIdByLogisticsCodeAndReturnIdAndStatus(logisticNumber, returnId);
        if (CollectionUtils.isEmpty(ids)) {
            String newLogisticNumber = "T" + logisticNumber;
            ids = ES4ReturnOrder.findIdByLogisticsCodeAndReturnIdAndStatus(newLogisticNumber, returnId);
        }

        List<Long> idList = new ArrayList<>(ids);

        if (CollectionUtils.isEmpty(idList)) {
            String redisKey = BllRedisKeyResources.getOmsReturnOrderLogisticsKey(logisticNumber);
            Long id = this.selectOmsReturnOrderFromRedis(redisKey);
            if (id != null) {
                idList.add(id);
            }
        }

        if (CollectionUtils.isNotEmpty(idList)) {
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrderInfo(idList, returnId);
            if (CollectionUtils.isNotEmpty(list)) {
                idList = list.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            } else {
                idList.clear();
            }
        }
        return idList;
    }

    private Long selectOmsReturnOrderFromRedis(String redisKey) {
        CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        Boolean hasKey = objRedisTemplate.hasKey(redisKey);
        if (hasKey != null && hasKey) {
            Long value = objRedisTemplate.opsForValue().get(redisKey);
            return value;
        } else {
            return null;
        }
    }

    /**
     * 判断当前需要拦截的全是赠品的包裹
     */
    private boolean checkAllGift(List<OcBOrderItem> ocBOrderItems, List<OcBOrderDelivery> deliveries) {
        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            return false;
        }
        List<String> skuList = ocBOrderItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
        List<String> stringList =
                deliveries.stream().map(OcBOrderDelivery::getPsCSkuEcode).collect(Collectors.toList());
        return skuList.containsAll(stringList);
    }


    public List<OcBReturnOrderRelation> goodsAfterRefundOnly(OmsOrderRelation orderRelation,
                                                             IpBStandplatRefund ipBStandplatRefund,
                                                             List<IpBStandplatRefundItem> ipBStandplatRefundItem,
                                                             User user) {



     return null;
    }



}
