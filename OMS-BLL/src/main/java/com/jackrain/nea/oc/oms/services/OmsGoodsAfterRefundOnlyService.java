package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.InterceptStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.TaobaoRefundOrderTransferUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @Author: 黄世新
 * @Date: 2020/3/9 1:59 下午
 * @Version 1.0
 * 发货后仅退款的逻辑
 */
@Slf4j
@Component
public class OmsGoodsAfterRefundOnlyService {

    @Autowired
    private TaobaoRefundOrderTransferUtil taobaoRefundOrderTransferUtil;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private RedisOpsUtil<String, Long> redisOpsUtil;


    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;

    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;


    public List<OcBReturnOrderRelation> goodsAfterRefundOnly(OmsOrderRelation orderRelation,
                                                             IpBTaobaoRefund ipBTaobaoRefund,
                                                             User user) {
        List<OcBReturnOrderRelation> returnOrderRelations = new ArrayList<>();
        OcBOrder ocBOrder = orderRelation.getOcBOrder();
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        List<OcBOrderItem> giftItem = new ArrayList<>();
        List<OmsOrderRelation.OcOrderGifts> ocOrderGifts = orderRelation.getOcOrderGifts();
        if (CollectionUtils.isNotEmpty(ocOrderGifts)) {
            for (OmsOrderRelation.OcOrderGifts ocOrderGift : ocOrderGifts) {
                giftItem.addAll(ocOrderGift.getOcBOrderGifts());
            }
        }
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        Map<String, List<OcBOrderDelivery>> deliveriesMap = new HashMap<>(10);
        List<OcBOrderDelivery> orderDeliveries = orderRelation.getOrderDeliveries();
        //合并包裹信息
        for (OcBOrderDelivery orderDelivery : orderDeliveries) {
            String logisticNumber = orderDelivery.getLogisticNumber();
            if (!deliveriesMap.containsKey(logisticNumber)) {
                List<OcBOrderDelivery> orderDeliveryList = new ArrayList<>();
                orderDeliveryList.add(orderDelivery);
                deliveriesMap.put(logisticNumber, orderDeliveryList);
            } else {
                List<OcBOrderDelivery> deliveries = deliveriesMap.get(logisticNumber);
                deliveries.add(orderDelivery);
                deliveriesMap.put(logisticNumber, deliveries);
            }
        }
        //判断当前购买商品是否有即是正常商品 也可能是赠品的条码 如与申请的条码是否一致
        boolean giftSkuAndNormalSku = isGiftSkuAndNormalSku(ocBOrderItems, orderItems);
        //判断当前包裹的物流状态
        List<Long> refundIds = new ArrayList<>();
        boolean needIntercept = true;
        for (String logisticNumber : deliveriesMap.keySet()) {
            //判断该物流单号时候已经生成退换货单
            List<Long> returnOrderIds = this.getReturnOrderIdsByLogisticsNo(logisticNumber);
            if (CollectionUtils.isEmpty(returnOrderIds)) {
                List<OcBOrderDelivery> deliveries = deliveriesMap.get(logisticNumber);
                //当前包裹是否全是赠品标识
                boolean isAllGift = false;
                if (giftSkuAndNormalSku) {
                    isAllGift = checkAllGift(giftItem, deliveries);
                }
                if (!isContainApplySku(deliveries, orderRelation) && !isAllGift) {
                    continue;
                }
                OcBReturnOrderRelation relation = this.logisticsIntercept(ocBOrder, orderItems,
                        deliveries, ipBTaobaoRefund, 0, TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT, user);
                if (relation == null) {
                    this.updateReturnOrder(ipBTaobaoRefund, ocBOrder, user);
                    continue;
                }

//                boolean numAgreement = isNumAgreement(relation, deliveries, ipBTaobaoRefund, ocBOrder, user);
//                if (!numAgreement) {
//                    continue;
//                }
                if (isAllGift) {
                    relation.getReturnOrderInfo().setIntercerptStatus(InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());
                    returnOrderRelations.add(relation);
                    continue;
                }
                if (needIntercept) {
                    needIntercept = this.handleParcelNew(deliveries, ocBOrderItems, giftItem, orderItems);
                }
                relation.getReturnOrderInfo().setIntercerptStatus(InterceptStatus.NO_LAUNCH_INTERCEPT.getCode());
                returnOrderRelations.add(relation);

            } else {
                if (log.isDebugEnabled()) {
                    log.debug(this.getClass().getName() + " 退换货已存在!物流单号:{},退换货id:{}", logisticNumber, returnOrderIds);
                }
                refundIds.addAll(returnOrderIds);
            }
        }
        if (needIntercept) {
            if (CollectionUtils.isNotEmpty(returnOrderRelations)) {
                for (OcBReturnOrderRelation returnOrderRelation : returnOrderRelations) {
                    returnOrderRelation.getReturnOrderInfo().setIntercerptStatus(InterceptStatus.NEED_INTERCEPT.getCode());
                }
            }
        }
        if (CollectionUtils.isEmpty(returnOrderRelations) && CollectionUtils.isNotEmpty(refundIds)) {
            omsRefundOrderService.foundRefundSlipAfter(refundIds, ocBOrder, ipBTaobaoRefund, user);
        }

        return returnOrderRelations;
    }

    /**
     * 判断是否有商品即是正常商品  也是赠品
     *
     * @param ocBOrderItems 申请退的条码,
     * @param orderItems    订单全部条码
     */
    public boolean isGiftSkuAndNormalSku(List<OcBOrderItem> ocBOrderItems, List<OcBOrderItem> orderItems) {
        List<OcBOrderItem> giftList = orderItems.stream().filter(p -> p.getIsGift() == 1).collect(toList());
        if (CollectionUtils.isEmpty(giftList)) {
            return false;
        }
        List<OcBOrderItem> normalList = orderItems.stream().filter(p -> p.getIsGift() == 0).collect(toList());
        List<String> giftSku = giftList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(toList());
        List<String> normalSku = normalList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(toList());
        //得到即是赠品   也是正常商品的条码
        List<String> intersection = giftSku.stream().filter(normalSku::contains).collect(toList());
        if (CollectionUtils.isEmpty(intersection)) {
            return true;
        }
        List<String> applySku = ocBOrderItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(toList());
        return applySku.containsAll(intersection);
    }

    //判断生成拦截类型的预退货单明细是否与包裹里的一直

    private boolean isNumAgreement(OcBReturnOrderRelation relation, List<OcBOrderDelivery> deliveries,
                                   IpBTaobaoRefund ipBTaobaoRefund, OcBOrder ocBOrder, User operateUser) {
        List<OcBReturnOrderRefund> orderRefundList = relation.getOrderRefundList();
        BigDecimal qtyRefund = orderRefundList.stream().map(OcBReturnOrderRefund::getQtyRefund).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal qtyCount = deliveries.stream().map(OcBOrderDelivery::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (qtyRefund.compareTo(qtyCount) != 0) {
            updateReturnOrder(ipBTaobaoRefund, ocBOrder, operateUser);
        }
        return qtyRefund.compareTo(qtyCount) == 0;
    }

    private void updateReturnOrder(IpBTaobaoRefund ipBTaobaoRefund, OcBOrder ocBOrder, User operateUser) {
        //判断是否存在手动创建的客退类型的预退货单
        Long oid = ipBTaobaoRefund.getOid();
        String refundId = ipBTaobaoRefund.getRefundId();
        List<Long> returnOrderId = omsRefundOrderService.isExistReturnOrderByOidAndReturnId(refundId, oid + "");
        if (CollectionUtils.isNotEmpty(returnOrderId)) {
            omsRefundOrderService.foundRefundSlipAfter(returnOrderId, ocBOrder, ipBTaobaoRefund, operateUser);
        }
    }

    /**
     * 判断该包裹是否包含申请退的条码
     *
     * @param deliveries    单个包裹的sku
     * @param orderRelation 所申请的sku(包裹挂靠赠品和正常赠品)
     * @return
     */
    private boolean isContainApplySku(List<OcBOrderDelivery> deliveries, OmsOrderRelation orderRelation) {
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        List<String> packSku = deliveries.stream().map(OcBOrderDelivery::getPsCSkuEcode).collect(toList());
        List<String> applySku = ocBOrderItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(toList());
        //取交集
        List<String> intersection = packSku.stream().filter(applySku::contains).collect(toList());
        if (CollectionUtils.isNotEmpty(intersection)) {
            return true;
        } else {
            List<OmsOrderRelation.OcOrderGifts> ocOrderGifts = orderRelation.getOcOrderGifts();
            if (CollectionUtils.isNotEmpty(ocOrderGifts)) {
                List<OcBOrderItem> giftOrderItem = new ArrayList<>();
                for (OmsOrderRelation.OcOrderGifts ocOrderGift : ocOrderGifts) {
                    giftOrderItem.addAll(ocOrderGift.getOcBOrderGifts());
                }
                List<String> applyGiftSku = giftOrderItem.stream().map(OcBOrderItem::getPsCSkuEcode).collect(toList());
                //取交集
                List<String> intersectionGift = packSku.stream().filter(applyGiftSku::contains).collect(toList());
                if (CollectionUtils.isNotEmpty(intersectionGift)) {
                    return false;
                } else {
                    if (intersectionGift.size() == packSku.size()) {
                        return true;
                    }
                }
            } else {
                return false;
            }
        }
        return false;
    }


    /**
     * 处理包裹信息 是否需要拦截
     *
     * @param deliveries    一个包裹
     * @param ocBOrderItems 需要退的明细
     * @return
     */
    private boolean handleParcelNew(List<OcBOrderDelivery> deliveries, List<OcBOrderItem> ocBOrderItems,
                                    List<OcBOrderItem> giftItem, List<OcBOrderItem> AllItem) {
        //当前包裹的sku信息
        List<String> packSku = deliveries.stream().map(OcBOrderDelivery::getPsCSkuEcode).collect(toList());
        //申请退的sku信心
        List<String> applySku = ocBOrderItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(toList());
        if (applySku.containsAll(packSku)) {
            return true;
        }
        //将包裹里的申请的sku移除
        packSku.removeAll(applySku);
        //不为空   判断剩余的条码是否为全是赠品
        if (CollectionUtils.isNotEmpty(giftItem)) {
            List<String> giftSku = giftItem.stream().map(OcBOrderItem::getPsCSkuEcode).collect(toList());
            if (giftSku.containsAll(packSku)) {
                List<OcBOrderItem> noGiftItem = AllItem.stream().filter(p -> p.getIsGift() == 0).collect(toList());
                List<String> normalSku = noGiftItem.stream().map(OcBOrderItem::getPsCSkuEcode).collect(toList());
                if (normalSku.containsAll(packSku)) {
                    return false;
                }
                return true;
            }
        }
        return false;
    }


    /**
     * 拦截(对一个包裹进行处理)
     *
     * @param ocBOrder
     * @param ocBOrderItems 当前订单下所有的明细信息
     * @param deliveries
     */
    private OcBReturnOrderRelation logisticsIntercept(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems,
                                                      List<OcBOrderDelivery> deliveries,
                                                      IpBTaobaoRefund ipBTaobaoRefund, Integer interceptMark,
                                                      TaobaoReturnOrderExt.ReturnBillsStatus billsStatus,
                                                      User user) {
        Map<String, BigDecimal> skuAndQuantitiesInThisOnePackage = getSkuAndQuantitiesInThisOnePackage(deliveries);
        OmsOrderRelation orderRelation = new OmsOrderRelation();
        orderRelation.setOcBOrder(ocBOrder);
        orderRelation.setOcBOrderItems(ocBOrderItems);
        orderRelation.setInterceptMark(interceptMark);
        return taobaoRefundOrderTransferUtil.taobaoRefundOrderToReturnOrder(orderRelation,
                ipBTaobaoRefund, skuAndQuantitiesInThisOnePackage, deliveries.get(0), billsStatus, user);
    }

    private Map<String, BigDecimal> getSkuAndQuantitiesInThisOnePackage(List<OcBOrderDelivery> deliveries) {
        //判断包裹是否有多个sku 获取sku的数量
        Map<String, BigDecimal> skuCount = new HashMap<>();
        for (OcBOrderDelivery delivery : deliveries) {
            String psCSkuEcode = delivery.getPsCSkuEcode();
            if (!skuCount.containsKey(psCSkuEcode)) {
                skuCount.put(psCSkuEcode, delivery.getQty());
            } else {
                BigDecimal qty = skuCount.get(psCSkuEcode);
                skuCount.put(psCSkuEcode, delivery.getQty().add(qty));
            }
        }
        return skuCount;
    }

    public void addReturnLog(Long returnId, String logMsg, String logType, User user) {
        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
        ocBReturnOrderLog.setId(ModelUtil.getSequence("oc_b_return_order_log"));
        ocBReturnOrderLog.setLogMessage(logMsg);
        ocBReturnOrderLog.setLogType(logType);
        ocBReturnOrderLog.setOcBReturnOrderId(returnId);
        returnOrderTransferUtil.saveSysLog(ocBReturnOrderLog, user);
        ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
    }


    /**
     * 根据物流单号判断退换货订单是否存在(拦截是以包裹维度生成)
     */
    public List<Long> getReturnOrderIdsByLogisticsNo(String logisticNumber) {
        List<Long> returnIds = new ArrayList<>();
        List<OcBReturnOrder> list = getReturnOrderByLogisticNumber(logisticNumber);
        if (CollectionUtils.isNotEmpty(list)) {
            list = list.stream().filter(p -> TaobaoReturnOrderExt.BillType.REFUND.getCode().equals(p.getBillType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                returnIds = list.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            }
        }
        return returnIds;
    }

    private Long selectOmsReturnOrderFromRedis(String redisKey) {
        CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        Boolean hasKey = objRedisTemplate.hasKey(redisKey);
        if (hasKey != null && hasKey) {
            Long value = objRedisTemplate.opsForValue().get(redisKey);
            return value;
        } else {
            return null;
        }
    }

    public List<OcBReturnOrder> getReturnOrderByLogisticNumber(String logisticNumber) {
        //获取物流单号
        //根据物流单号查询退换单是否存在
        String[] returnFileds = {"ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("LOGISTICS_CODE", logisticNumber);
        whereKeys.put("RETURN_STATUS", "!=" + TaobaoReturnOrderExt.ReturnOrderStatus.CANCEL.getCode());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER,
                TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER, whereKeys, null, null, 10, 0, returnFileds);
        log.debug(this.getClass().getName() + " 查询退换货单是否存在:{}", search.toString());
        JSONArray returnData = search.getJSONArray("data");
        List<Long> idList = new ArrayList<>();
        if (null != returnData && !returnData.isEmpty()) {
            for (int i = 0; i < returnData.size(); i++) {
                JSONObject jsonObject = returnData.getJSONObject(i);
                Long id = jsonObject.getLong("ID");
                idList.add(id);
            }
        } else {
            String redisKey = BllRedisKeyResources.getOmsReturnOrderLogisticsKey(logisticNumber);
            Long id = this.selectOmsReturnOrderFromRedis(redisKey);
            if (id != null) {
                idList.add(id);
            }
        }
        List<OcBReturnOrder> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(idList)) {
            list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(idList);
        }
        return list;
    }

    /**
     * 判断当前需要拦截的全是赠品的包裹
     */
    private boolean checkAllGift(List<OcBOrderItem> giftList, List<OcBOrderDelivery> deliveries) {
        if (CollectionUtils.isEmpty(giftList)) {
            return false;
        }
        List<String> skuList = giftList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(toList());
        List<String> stringList = deliveries.stream().map(OcBOrderDelivery::getPsCSkuEcode).collect(toList());
        if (skuList.containsAll(stringList)) {
            //全部包含后  判断包裹的数量 是否与赠品的
            //包裹的数量
            BigDecimal qtyDelivery = deliveries.stream().map(OcBOrderDelivery::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal giftQty = giftList.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (qtyDelivery.compareTo(giftQty) <= 0) {
                return true;
            }
        }
        return false;
    }
}
