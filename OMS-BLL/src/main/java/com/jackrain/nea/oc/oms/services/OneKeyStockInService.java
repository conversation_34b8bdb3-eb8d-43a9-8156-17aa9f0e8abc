package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sg.service.AddOrderNoticeAndOutService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.OmsReturnInStockSupply;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/10
 */
@Slf4j
@Component
public class OneKeyStockInService {

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OcBRefundInMapper refundInMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    private OcBRefundInProductItemMapper ocBRefundInProductItemMapper;

    @Autowired
    private OcBRefundInLogService ocBRefundInLogService;

    @Autowired
    private ReturnOrderLogService returnOrderLogService;

    @Autowired
    private AddOrderNoticeAndOutService addOrderNoticeAndOutService;

    /**
     * @param list
     * @param usr
     * @return
     */
    public ValueHolderV14<JSONArray> batchStockIn(List<Long> list, User usr) {
        AssertUtil.assertException(Objects.isNull(usr), "用户为空");
        AssertUtil.assertException(CollectionUtils.isEmpty(list), "请选择单据");
        ValueHolderV14<JSONArray> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "");
        OmsReturnInStockSupply.logsWithoutTime.accept("OneKeyStockInService.start");
        JSONArray metaDataTips = new JSONArray();
        int error = 0;
        int success = 0;
        try {
            String tipMessage = "";
            for (Long bilId : list) {
                ValueHolderV14 eachResult = stockInProcess(bilId, usr);
                if (!eachResult.isOK()) {
                    error += 1;
                    tipMessage = eachResult.getMessage();
                    metaDataTips.add(OmsReturnInStockSupply.buildWebTipFun.apply(bilId, tipMessage));
                    continue;
                }
                success++;
            }
            buildResult(list.size(), vh, error, success, tipMessage);
            vh.setData(metaDataTips);
            OmsReturnInStockSupply.logs.accept(",OneKeyStockInService.end, Start Release Resources...");
            OmsReturnInStockSupply.printLogFunction.exec();
        } catch (Exception ex) {
            log.error("OneKeyStockInService.batchStockIn.{}", Throwables.getStackTraceAsString(ex));
        } finally {
            OmsReturnInStockSupply.releaseFunction.exec();
        }
        return vh;
    }

    /**
     * 入库
     *
     * @param bilId
     * @param usr
     * @return
     */
    public ValueHolderV14 stockInProcess(Long bilId, User usr) {
        OmsReturnInStockSupply.logsWithoutTime.accept(String.format("OneKeyStockInService.ReturnId.%d,start", bilId));
        String lockRedisKey = BllRedisKeyResources.buildLockReturnInKey(bilId);
        Boolean isLock = OmsReturnInStockSupply.lockBill.apply(lockRedisKey);
        ValueHolderV14<String> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "成功");
        try {
            AssertUtil.assertException(!isLock, "锁单失败");
            OcBReturnOrder returnOrder = getReturnOrder(bilId);
            // todo 待优化
            boolean isExistLogisticNumber = checkIsExistLogisticNumber(returnOrder.getLogisticsCode());
            AssertUtil.assertException(isExistLogisticNumber, "已存在此物流单号的退货入库单, 无需一键入库");
            OcBRefundIn refundIn = buildOcBRefundInBill(returnOrder, usr);
            assignRefundInStockInInfo(returnOrder.getCpCPhyWarehouseInId(), refundIn);
            List<OcBReturnOrderRefund> returnRefunds = getReturnRefunds(bilId);
            List<OcBRefundInProductItem> proItems = buildRefundInSubItems(returnRefunds, refundIn, returnOrder, usr);

            OneKeyStockInService bean = ApplicationContextHandle.getBean(OneKeyStockInService.class);
            bean.PersistentDataNoticeStockIn(returnOrder, refundIn, returnRefunds, proItems, usr);
            return vh;
        } catch (Exception e) {
            vh.setCode(ResultCode.FAIL);
            String m = OmsReturnInStockSupply.dealExpMessageFunction.apply(e);
            vh.setMessage(m);
            OmsReturnInStockSupply.logs.accept(String.format("ReturnId.%d,Exp:%s", bilId, m));
            return vh;
        } finally {
            OmsReturnInStockSupply.unlockBill.exec();
        }
    }

    /**
     * 退单
     *
     * @param bilId
     * @return
     */
    private OcBReturnOrder getReturnOrder(Long bilId) {
        OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByid(bilId);
        AssertUtil.assertException(Objects.isNull(returnOrder), "未查询到退换货单, 请刷新页面后再进行尝试");
        boolean isLegal = OmsParamConstant.YES.equals(returnOrder.getIsBack())
                && ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnOrder.getReturnStatus());
        AssertUtil.assertException(!isLegal, "原退且待退货入库状态的单据方可进行此项操作");
        String logisticsCode = returnOrder.getLogisticsCode();
        AssertUtil.assertException(StringUtils.isBlank(logisticsCode), "物流单号不允许为空");
        return returnOrder;
    }

    /**
     * 退单明细
     *
     * @return
     */
    private List<OcBReturnOrderRefund> getReturnRefunds(Long shardKey) {
        List<OcBReturnOrderRefund> subReturnItems = ocBReturnOrderRefundMapper
                .selectList(new LambdaQueryWrapper<OcBReturnOrderRefund>()
                        .eq(OcBReturnOrderRefund::getOcBReturnOrderId, shardKey));
        AssertUtil.assertException(CollectionUtils.isEmpty(subReturnItems), "未查询到退货商品");
        return subReturnItems;
    }


    /**
     * 构建入库单
     *
     * @param returnOrder
     * @param usr
     * @return
     */
    private OcBRefundIn buildOcBRefundInBill(OcBReturnOrder returnOrder, User usr) {
        OcBRefundIn refundIn = new OcBRefundIn();
        refundIn.setCpCPhyWarehouseId(returnOrder.getCpCPhyWarehouseId());
        refundIn.setSourceCode(returnOrder.getTid());
        refundIn.setOrigOrderNo(returnOrder.getOrigOrderId());
        refundIn.setUserNick(returnOrder.getBuyerNick());
        refundIn.setReceiverName(returnOrder.getReceiveName());
        refundIn.setReceiverAddress(returnOrder.getReceiveAddress());
        refundIn.setLogisticNumber(returnOrder.getLogisticsCode());
        refundIn.setCpCLogisticsId(returnOrder.getCpCLogisticsId());
        refundIn.setCpCLogisticsEcode(returnOrder.getLogisticsCode());
        refundIn.setCpCLogisticsEname(returnOrder.getCpCLogisticsEname());
        refundIn.setMatcher(usr.getEname());
        refundIn.setMatchedTime(new Date());
        refundIn.setOrderStatus(OcBStoreRefundInStatusEnum.SUBMITTED.getVal());
        refundIn.setSubmitId(Long.valueOf(usr.getId()));
        refundIn.setSubmitDate(new Date());
        refundIn.setIsOffMatch(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
        refundIn.setInStatus(ReturnStatus.WAREHOUSING.toInteger());
        refundIn.setQtyMatch(0L);
        refundIn.setOwnerename(usr.getEname());
        refundIn.setModifierename(usr.getEname());
        CommandAdapterUtil.defaultOperator(refundIn, usr);
        refundIn.setId(ModelUtil.getSequence(OcCommonConstant.OC_B_REFUND_IN));
        refundIn.setMatchStatus(OcBOrderConst.REFUND_IN_MATCHSTATUS_ALL);
        returnOrder.setOcBRefundInId(refundIn.getId());
        return refundIn;
    }

    /**
     * 构建入库明细
     *
     * @param list
     * @param refundIn
     * @param returnOrder
     * @param usr
     * @return
     */
    private List<OcBRefundInProductItem> buildRefundInSubItems(List<OcBReturnOrderRefund> list, OcBRefundIn refundIn,
                                                               OcBReturnOrder returnOrder, User usr) {
        OcBRefundInProductItem newItem;
        List<OcBRefundInProductItem> subInItems = new ArrayList<>();
        Map<String, ProductSku> skuMapping = new HashMap<>();
        for (OcBReturnOrderRefund subReturn : list) {
            BigDecimal itemQtyRefund = subReturn.getQtyRefund();
            AssertUtil.assertException(NumUtil.eqZero(itemQtyRefund, true),
                    String.format("商品条码:%s申请数量不正确", subReturn.getPsCSkuEcode()));
            String skuECode = subReturn.getPsCSkuEcode();
            AssertUtil.assertException(StringUtils
                    .isBlank(skuECode), String.format("商品编号:%s条码信息缺失", subReturn.getPsCProEcode()));
            ProductSku productSku = skuMapping.get(skuECode);
            if (productSku == null) {
                productSku = psRpcService.selectProductSku(skuECode);
                AssertUtil.assertException(productSku == null, "未获取到商品条码信息");
                skuMapping.put(skuECode, productSku);
            }
            newItem = new OcBRefundInProductItem();
            newItem.setPsCSkuId(productSku.getId());
            newItem.setPsCSkuEcode(productSku.getEcode());
            newItem.setPsCProId(productSku.getProdId());
            newItem.setPsCProEcode(productSku.getProdCode());
            newItem.setPsCProEname(productSku.getName());
            newItem.setGbcode(productSku.getBarcode69());
            newItem.setPsCSizeId(productSku.getSizeId());
            newItem.setPsCSizeEcode(productSku.getSizeCode());
            newItem.setPsCSizeEname(productSku.getSizeName());
            newItem.setPsCClrId(productSku.getColorId());
            newItem.setPsCClrEcode(productSku.getColorCode());
            newItem.setPsCClrEname(productSku.getColorName());
            newItem.setOwnerename(usr.getEname());
            newItem.setModifierename(usr.getEname());
            CommandAdapterUtil.defaultOperator(newItem, usr);
            newItem.setQty(itemQtyRefund);
            newItem.setOcBRefundInId(refundIn.getId());

            newItem.setOcBReturnOrderId(returnOrder.getId());
            newItem.setIsMatch(IsMatchEnum.MATCHED.getVal());
            newItem.setIsGenAdjust(IsGenAdjustEnum.NO.integer());
            newItem.setIsGenInOrder(IsGenInEnum.YES.integer());
            newItem.setIsGenMinusAdjust(IsGenMinusAdjustEnum.NO.integer());
            newItem.setIsGenWroAdjust(IsGenWroAdjustEnum.NO.integer());
            newItem.setIsWithoutOrig(IsWithoutOrigEnum.NOT_WITHOUT_ORIG.getVal());
            newItem.setId(ModelUtil.getSequence(OcCommonConstant.OC_B_REFUND_IN_PRODUCT_ITEM));
            subInItems.add(newItem);

            String allSku = skuMapping.keySet().stream().collect(Collectors.joining(","));
            refundIn.setAllSku(allSku);
            subReturn.setQtyIn(itemQtyRefund);
            subReturn.setQtyMatch(itemQtyRefund.longValue());
        }
        return subInItems;
    }

    /**
     * 仓库信息
     *
     * @param phyWarehouseId
     * @param refundIn
     */
    private void assignRefundInStockInInfo(Long phyWarehouseId, OcBRefundIn refundIn) {

        AssertUtil.assertException(Objects.isNull(phyWarehouseId), "实体仓信息有误,编号不存在");
        CpCPhyWarehouse phyWarehouse = cpRpcService.queryByWarehouseId(phyWarehouseId);
        AssertUtil.assertException(Objects.isNull(phyWarehouse), "未查询到该实体仓信息");
        boolean isStoreNature = OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE_STORE_02.equals(phyWarehouse.getWhType());
        AssertUtil.assertException(!isStoreNature, "非门店性质实体仓不允许此项操作");
        List<Long> storeIds = cpRpcService.queryStoreList(phyWarehouseId);
        AssertUtil.assertException(CollectionUtils.isEmpty(storeIds), "未查询到实体仓关联的逻辑仓");
        CpStore cpStore = cpRpcService.selectCpCStoreById(storeIds.get(0));
        AssertUtil.assertException(Objects.isNull(cpStore), "未查询到实体仓关联的退货逻辑仓");

        refundIn.setCpCPhyWarehouseEcode(phyWarehouse.getEcode());
        refundIn.setCpCPhyWarehouseEname(phyWarehouse.getEname());
        refundIn.setInStoreId(cpStore.getId());
        refundIn.setInStoreEcode(cpStore.getEcode());
        refundIn.setInStoreEname(cpStore.getEname());
        refundIn.setStoreId(cpStore.getId());
        refundIn.setStoreCode(cpStore.getEcode());
        refundIn.setStoreName(cpStore.getEname());
    }

    /**
     * todo 应优化为es或分库查询
     * 物流校验
     *
     * @param lgNo
     * @return
     */
    private boolean checkIsExistLogisticNumber(String lgNo) {
        if (StringUtils.isBlank(lgNo)) {
            return false;
        }
        List<OcBRefundIn> refundIns = refundInMapper
                .selectList(new LambdaQueryWrapper<OcBRefundIn>().eq(OcBRefundIn::getLogisticNumber, lgNo));
        if (CollectionUtils.isEmpty(refundIns)) {
            return false;
        }
        return true;
    }

    @Transactional
    public void PersistentDataNoticeStockIn(OcBReturnOrder returnOrder, OcBRefundIn refundIn,
                                            List<OcBReturnOrderRefund> returnRefunds,
                                            List<OcBRefundInProductItem> proItems, User usr) {
        OmsReturnInStockSupply.logs.accept("Transactional.start...");
        refundInMapper.insert(refundIn);
        for (OcBRefundInProductItem proItem : proItems) {
            ocBRefundInProductItemMapper.insert(proItem);
        }
        OcBReturnOrder newReturn = new OcBReturnOrder();
        newReturn.setId(returnOrder.getId());
        newReturn.setOcBRefundInId(returnOrder.getOcBRefundInId());
        ocBReturnOrderMapper.updateById(newReturn);
        for (OcBReturnOrderRefund item : returnRefunds) {
            ocBReturnOrderRefundMapper.updateMatchNum(item.getQtyMatch(), returnOrder.getId(), item.getId());
        }
        ocBRefundInLogService.addLog(refundIn.getId(),
                "新增入库单", String.format("退货单一键入库,退货单编码:%s", returnOrder.getBillNo()), usr);
        returnOrderLogService.addRefundOrderLog(returnOrder.getId(), "一键入库",
                String.format("入库成功,退货入库单编号:%s,等待入库结果信息", refundIn.getId()), usr);
        OcBReturnOrderRelation relation = new OcBReturnOrderRelation();
        relation.setReturnOrderInfo(returnOrder);
        relation.setOrderRefundList(returnRefunds);
        invokeSgStockInService(relation, refundIn, usr);
        OmsReturnInStockSupply.logs.accept("Transactional.end...");
    }

    /**
     * 创建入库通知单
     *
     * @param relation
     * @param inRfnIn
     * @param usr
     */
    private void invokeSgStockInService(OcBReturnOrderRelation relation, OcBRefundIn inRfnIn, User usr) {
        OcBReturnOrder returnOrder = relation.getReturnOrderInfo();
        try {
            ValueHolderV14 vh = addOrderNoticeAndOutService.addNoticeAndOutOrderNew(relation, inRfnIn, usr);
            if (vh != null) {
                if (ResultCode.SUCCESS == vh.getCode()) {
                    OmsReturnInStockSupply.logs.accept(String
                            .format("invokeSgStockInService.ReturnOrderId-%d,ResultSuccess,Code:%d,Msg:%s",
                                    returnOrder.getId(), vh.getCode(), vh.getMessage()));
                    return;
                } else {
                    OmsReturnInStockSupply.logs.accept(String
                            .format("invokeSgStockInService.ReturnOrderId-%d,ResultFail,Code:%d,Msg:%s",
                                    returnOrder.getId(), vh.getCode(), vh.getMessage()));
                }
            } else {
                OmsReturnInStockSupply.logs.accept(String
                        .format("invokeSgStockInService.ReturnOrderId-%d,返回值异常:Null", returnOrder.getId()));
            }
        } catch (Exception ex) {
            String message = OmsReturnInStockSupply.dealExpMessageFunction.apply(ex);
            OmsReturnInStockSupply.logs.accept(String
                    .format("invokeSgStockInService.ReturnOrderId-%d,调用服务异常,%s", returnOrder.getId(), message));
        }
        throw new NDSException("通知库存中心创建入库通知单失败");
    }

    /**
     * @param size
     * @param vh
     * @param error
     * @param success
     * @param tipMessage
     */
    private void buildResult(int size, ValueHolderV14<JSONArray> vh, int error, int success, String tipMessage) {
        String msg;
        TipLabel:
        if (size == 1) {
            if (success > 0) {
                msg = "入库成功";
                break TipLabel;
            }
            vh.setCode(ResultCode.FAIL);
            msg = String.format("入库失败: %s", tipMessage);
        } else if (error > 0) {
            if (error == size) {
                vh.setCode(ResultCode.FAIL);
                msg = String.format("入库失败%d条", error);
                break TipLabel;
            }
            msg = String.format("入库完成,成功:%d条,失败:%d条",success,error);
        } else {
            msg = String.format("入库成功%d条", success);
        }
        vh.setMessage(msg);
    }

}
