package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.dto.EqualExchangeStInfo;
import com.jackrain.nea.oc.oms.function.OrderLogEqualExchange;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategyItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.model.StEqualExchangeRelation;
import com.jackrain.nea.st.service.StEqualExchangeService;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/6/30 下午2:57
 * @Version 1.0
 * 执行对等换货策略
 */
@Slf4j
@Component
public class OmsEqualExchangeStService {

    @Autowired
    private StEqualExchangeService stEqualExchangeService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OmsEqualExchangeStService omsEqualExchangeStService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    private static final String EQUAL_EXCHANGE_MARK = "EEM";
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;


    public boolean equalExchangeStService(OcBOrderParam orderParam, User user) {
        OcBOrder ocBOrder = orderParam.getOcBOrder();
        boolean toBOrderPt = OmsBusinessTypeUtil.isToBOrderPt(ocBOrder);
        boolean toCOrderPt = OmsBusinessTypeUtil.isToCOrderPt(ocBOrder);
        String gwSourceGroup = ocBOrder.getGwSourceGroup();
        if (toBOrderPt || toCOrderPt || "38".equals(gwSourceGroup) || "39".equals(gwSourceGroup)) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), "该业务类型订单不支持对等换货", "", "", user);
            return false;
        }

        if (OcBOrderConst.IS_STATUS_IY.equals(orderParam.getOcBOrder().getIsCycle())) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), "天猫周期购订单不支持对等换货", "", "", user);
            return false;
        }

        CpShop cpShop = cpRpcService.selectCpCShopById(ocBOrder.getCpCShopId());
        if (cpShop == null || "N".equals(cpShop.getIsEqualExchange())) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), "店铺未开启对等换货", "", "", user);
            return false;
        }
        List<OcBOrderItem> orderItemList = orderParam.getOrderItemList();
        //判断明细的效期是否有类型为3的
        List<OcBOrderItem> orderItems = orderItemList.stream().filter(p -> p.getExpiryDateType() == null || p.getExpiryDateType() != 3).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItems)) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), "订单明细不满足对等换货", "", "", user);
            //明细不满足对等换货
            return false;
        }

        Map<Long, OcBOrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
        //查询策略
        List<EquityExchangeItem> equityExchangeItemList = new ArrayList<>();
        StEqualExchangeRelation relation = stEqualExchangeService.selectEqualExchange(ocBOrder.getCpCShopId());
        log.info(LogUtil.format("OmsEqualExchangeStService.equalExchangeStService orderId:{},shopExchangeRelation:{}",
                "OmsEqualExchangeStService.equalExchangeStService"), ocBOrder.getId(), JSONObject.toJSONString(relation));
        if (relation != null) {
            StringBuilder logMessageBuilder = new StringBuilder("订单执行对等换货指定店铺策略 " + ocBOrder.getCpCShopTitle() + " ");
            List<StCEquityBarterStrategyItem> stCEquityBarterStrategyItems = relation.getStCEquityBarterStrategyItems();
            List<EquityExchangeItem> equityExchangeItems = this.matchEquityExchange(orderItems, stCEquityBarterStrategyItems, OrderLogEqualExchange.consumer(orderItemMap, logMessageBuilder));
            if (CollectionUtils.isNotEmpty(equityExchangeItems)) {
                equityExchangeItemList.addAll(equityExchangeItems);
                for (EquityExchangeItem equityExchangeItem : equityExchangeItems) {
                    OcBOrderItem ocBOrderItem = equityExchangeItem.getOcBOrderItem();
                    orderItemMap.remove(ocBOrderItem.getId());
                }
            }
            if (orderItemMap.isEmpty()) {
                omsEqualExchangeStService.saveEquityExchange(equityExchangeItemList, ocBOrder, null);
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), OrderLogEqualExchange.getBuilderToString(logMessageBuilder) + " 完成", "", "", user);
                return true;
            }
            if (CollectionUtils.isNotEmpty(equityExchangeItemList)) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), OrderLogEqualExchange.getBuilderToString(logMessageBuilder), "", "", user);
            }
        } else {
            Collection<OcBOrderItem> values = orderItemMap.values();
            StEqualExchangeRelation relationCommon = stEqualExchangeService.selectEqualExchangeCommon();
            log.info(LogUtil.format("OmsEqualExchangeStService.equalExchangeStService orderId:{},commonExchangeRelation:{}",
                    "OmsEqualExchangeStService.equalExchangeStService"), ocBOrder.getId(), JSONObject.toJSONString(relationCommon));
            if (relationCommon != null) {
                StringBuilder logMessageBuilder = new StringBuilder("订单执行对等换货公用策略 ");
                List<StCEquityBarterStrategyItem> strategyItems = relationCommon.getStCEquityBarterStrategyItems();
                List<EquityExchangeItem> equityExchange = this.matchEquityExchange(new ArrayList<>(values), strategyItems, OrderLogEqualExchange.consumer(orderItemMap, logMessageBuilder));
                if (CollectionUtils.isNotEmpty(equityExchange)) {
                    equityExchangeItemList.addAll(equityExchange);
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), OrderLogEqualExchange.getBuilderToString(logMessageBuilder), "", "", user);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(equityExchangeItemList)) {
            omsEqualExchangeStService.saveEquityExchange(equityExchangeItemList, ocBOrder, null);
            return true;
        } else {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), "订单执行对等换货未匹配到策略", "", "", user);
        }
        return false;
    }

    /**
     * 手动执行对等换货
     *
     * @param orderItemList
     * @param order
     * @param equalExchangeStInfo
     * @param user
     */
    public boolean manualEqualExchangeService(List<OcBOrderItem> orderItemList, OcBOrder order,
                                              EqualExchangeStInfo equalExchangeStInfo, List<OcBOrderItem> delItemList, User user) {
        List<StCEquityBarterStrategyItem> strategyItems = new ArrayList<>();
        StCEquityBarterStrategyItem strategyItem = new StCEquityBarterStrategyItem();
        strategyItem.setPsCSkuCode(equalExchangeStInfo.getExchangeSkuCode());
        strategyItem.setPsCSkuName(equalExchangeStInfo.getExchangeSkuTitle());
        strategyItem.setQty(equalExchangeStInfo.getExchangeQty());
        strategyItem.setEquityQty(equalExchangeStInfo.getEqualQty());
        strategyItem.setEquitySkuCode(equalExchangeStInfo.getEqualSkuCode());
        strategyItem.setOutStockNoRestore(equalExchangeStInfo.getOutStockNoRestore());
        strategyItems.add(strategyItem);
        log.info(LogUtil.format("对等换货明细:{},订单明细:{},删除的明细:{}", "对等换货明细"), JSONObject.toJSONString(strategyItems), JSONObject.toJSONString(orderItemList), JSONObject.toJSONString(delItemList));
        List<EquityExchangeItem> equityExchangeItems = this.matchEquityExchange(orderItemList, strategyItems, null);
        if (CollectionUtils.isNotEmpty(equityExchangeItems)) {
            omsEqualExchangeStService.saveEquityExchange(equityExchangeItems, order, delItemList);
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), "订单执行手动对等换货", "", "", user);
            return true;
        }
        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), "订单执行手动对等换货失败,原因:未匹配到有效的策略", "", "", user);
        return false;

    }


    /**
     * 匹配对等换货
     *
     * @param orderItemList
     * @param stCEquityBarterStrategyItems
     */
    private List<EquityExchangeItem> matchEquityExchange(List<OcBOrderItem> orderItemList,
                                                         List<StCEquityBarterStrategyItem> stCEquityBarterStrategyItems, OrderLogEqualExchange consumer) {
        List<EquityExchangeResult> exchangeResults = new ArrayList<>();
        Map<String, List<StCEquityBarterStrategyItem>> skuIdMap = stCEquityBarterStrategyItems.stream().collect(Collectors.groupingBy(StCEquityBarterStrategyItem::getPsCSkuCode));
        for (OcBOrderItem item : orderItemList) {
            List<StCEquityBarterStrategyItem> barterStrategyItems = skuIdMap.get(item.getPsCSkuEcode());
            if (CollectionUtils.isEmpty(barterStrategyItems)) {
                continue;
            }
            List<EquityExchangeResult> results = this.handleEquityExchange(item, barterStrategyItems);
            if (CollectionUtils.isNotEmpty(results)) {
                exchangeResults.addAll(results);
                if (Objects.nonNull(consumer)) {
                    try {
                        consumer.accept(results);
                    }
                    catch (Exception e) {
                        log.warn("matchEquityExchange匹配对等换货执行日志记录异常：{}", Throwables.getStackTraceAsString(e));
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(exchangeResults)) {
            //没有匹配上对等换货 直接退出
            return null;
        }
        //开始处理明细
        Map<Long, OcBOrderItem> itemMap = orderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
        Map<Long, List<EquityExchangeResult>> exchangeResultMap = exchangeResults.stream().collect(Collectors.groupingBy(EquityExchangeResult::getItemId));
        List<EquityExchangeItem> equityExchangeItems = new ArrayList<>();
        //todo 这个处理奶卡信息
        OcBOrderItem orderItem1 = orderItemList.get(0);
        Long ocBOrderId = orderItem1.getOcBOrderId();
        List<OcBOrderNaiKa> orderNaiKas = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(ocBOrderId);
        for (Long itemId : exchangeResultMap.keySet()) {
            String outStockNoRestore = "0";

            OcBOrderItem item = itemMap.get(itemId);
            //查询奶卡信息
            List<EquityExchangeResult> results = exchangeResultMap.get(itemId);
            if (CollectionUtils.isEmpty(results)) {
                continue;
            }
            List<OcBOrderItem> orderItems = new ArrayList<>();
            for (EquityExchangeResult result : results) {
                if (!result.isOriginalItem()) {
                    outStockNoRestore = result.getOutStockNoRestore();
                }
                OcBOrderItem orderItem = buildEquityExchangeOrderItem(item, result);
                orderItems.add(orderItem);
            }
            List<OcBOrderNaiKa> orderNaiKas1 = this.handleOrderNaika(itemId, orderNaiKas, orderItems);
            //todo 重新计算金额
            this.computeAmt(item, orderItems);
            //生成换货的明细
            EquityExchangeItem equityExchangeItem = new EquityExchangeItem();
            OcBOrderEqualExchangeItem equalExchangeItem = new OcBOrderEqualExchangeItem();
            BeanUtils.copyProperties(item, equalExchangeItem);
            equalExchangeItem.setId(sequenceUtil.buildEqualExchangeItemSequenceId());
            equalExchangeItem.setOutStockNoRestore(outStockNoRestore);
            equityExchangeItem.setEqualExchangeItem(equalExchangeItem);
            equityExchangeItem.setOcBOrderItems(orderItems);
            equityExchangeItem.setOcBOrderItem(item);
            equityExchangeItem.setOcBOrderNaiKas(orderNaiKas1);
            equityExchangeItems.add(equityExchangeItem);
        }
        return equityExchangeItems;
    }

    /**
     * 处理奶卡信息
     *
     * @param itemId
     * @param orderNaiKas
     * @param orderItemsNew
     * @return
     */
    public List<OcBOrderNaiKa> handleOrderNaika(Long itemId, List<OcBOrderNaiKa> orderNaiKas, List<OcBOrderItem> orderItemsNew) {
        if (CollectionUtils.isEmpty(orderNaiKas)) {
            return null;
        }
        List<OcBOrderNaiKa> naiKaList = orderNaiKas.stream().filter(p -> p.getOcBOrderItemId() != null && p.getOcBOrderItemId().equals(itemId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(naiKaList)) {
            return null;
        }
        List<OcBOrderNaiKa> orderNaiKaList = new ArrayList<>();
        for (OcBOrderNaiKa naiKa : naiKaList) {
            for (OcBOrderItem orderItem : orderItemsNew) {
                OcBOrderNaiKa orderNaiKa = new OcBOrderNaiKa();
                BeanUtils.copyProperties(naiKa, orderNaiKa);
                orderNaiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
                orderNaiKa.setOcBOrderItemId(orderItem.getId());
                orderNaiKa.setPsCProEcode(orderItem.getPsCProEcode());
                orderNaiKa.setPsCProEname(orderItem.getPsCProEname());
                orderNaiKa.setPsCProId(orderItem.getPsCProId());
                orderNaiKa.setPsCSkuEcode(orderItem.getPsCSkuEcode());
                orderNaiKa.setPsCSkuEname(orderItem.getPsCSkuEname());
                orderNaiKa.setPsCSkuId(orderItem.getPsCSkuId());
                orderNaiKa.setSkuSpec(orderItem.getSkuSpec());
                orderNaiKaList.add(orderNaiKa);
            }
        }
        return orderNaiKaList;
    }


    /**
     * 计算金额
     *
     * @param item       原明细
     * @param orderItems 对等换货后的明细
     */
    private void computeAmt(OcBOrderItem item, List<OcBOrderItem> orderItems) {
        // 成交金额
        BigDecimal qty = item.getQty();
        BigDecimal realAmtSingle = item.getRealAmt().divide(qty, 10, BigDecimal.ROUND_HALF_UP);
        //调整金额
        BigDecimal adjustAmtSingle = item.getAdjustAmt().divide(qty, 10, BigDecimal.ROUND_HALF_UP);
        //优惠金额
        BigDecimal amtDiscountSingle = item.getAmtDiscount().divide(qty, 10, BigDecimal.ROUND_HALF_UP);
        //平摊金额
        BigDecimal orderSplitAmtSingle = item.getOrderSplitAmt().divide(qty, 10, BigDecimal.ROUND_HALF_UP);
        BigDecimal expandCardExpandPriceUsedSuborder = item.getExpandCardExpandPriceUsedSuborder();
        BigDecimal expandCardBasicPriceUsedSuborder = item.getExpandCardBasicPriceUsedSuborder();
        if (expandCardExpandPriceUsedSuborder != null) {
            expandCardExpandPriceUsedSuborder = expandCardExpandPriceUsedSuborder.divide(qty, 10, BigDecimal.ROUND_HALF_UP);
        }
        if (expandCardBasicPriceUsedSuborder != null) {
            expandCardBasicPriceUsedSuborder = expandCardBasicPriceUsedSuborder.divide(qty, 10, BigDecimal.ROUND_HALF_UP);
        }
        for (OcBOrderItem orderItem : orderItems) {
            String equalExchangeRatio = orderItem.getEqualExchangeRatio();
            BigDecimal exchangeQty = null;
            BigDecimal afterQty = BigDecimal.ONE;
            if (StringUtils.isNotEmpty(equalExchangeRatio)) {
                String[] split = equalExchangeRatio.split(":");
                exchangeQty = new BigDecimal(split[0]);
                afterQty = new BigDecimal(split[1]);
            } else {
                exchangeQty = orderItem.getQty();
            }
            // 平台售价
            orderItem.setPrice(item.getPrice().multiply(exchangeQty).divide(afterQty).setScale(4, BigDecimal.ROUND_HALF_UP));
            // 成交金额
            orderItem.setRealAmt(realAmtSingle.multiply(exchangeQty).setScale(4, BigDecimal.ROUND_HALF_UP));
            // 调整金额
            orderItem.setAdjustAmt(adjustAmtSingle.multiply(exchangeQty).setScale(4, BigDecimal.ROUND_HALF_UP));
            // 优惠金额
            orderItem.setAmtDiscount(amtDiscountSingle.multiply(exchangeQty).setScale(4, BigDecimal.ROUND_HALF_UP));
            // 平摊金额
            orderItem.setOrderSplitAmt(orderSplitAmtSingle.multiply(exchangeQty).setScale(4, BigDecimal.ROUND_HALF_UP));
            // 成交金额
            orderItem.setPriceActual(orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
            if (expandCardExpandPriceUsedSuborder != null) {
                orderItem.setExpandCardExpandPriceUsedSuborder(expandCardExpandPriceUsedSuborder.multiply(exchangeQty).setScale(4, BigDecimal.ROUND_HALF_UP));
            }
            if (expandCardBasicPriceUsedSuborder != null) {
                orderItem.setExpandCardBasicPriceUsedSuborder(expandCardBasicPriceUsedSuborder.multiply(exchangeQty).setScale(4, BigDecimal.ROUND_HALF_UP));
            }
        }
    }

    /**
     * 构建明细数据
     *
     * @param item
     * @param result
     * @return
     */
    private OcBOrderItem buildEquityExchangeOrderItem(OcBOrderItem item, EquityExchangeResult result) {
        OcBOrderItem orderItem = new OcBOrderItem();
        BeanUtils.copyProperties(item, orderItem);
        orderItem.setId(sequenceUtil.buildOrderItemSequenceId());
        orderItem.setIsExchangeItem(null);
        orderItem.setEqualExchangeMark(null);
        BigDecimal equityQty = result.getEquityQty();
        orderItem.setQty(equityQty);
        // 如果是剩余的原条码明细  则不需要查询条码信息
        if (!result.isOriginalItem()) {
            String equitySkuCode = result.getEquitySkuCode();
            ProductSku productSku = psRpcService.selectProductSku(equitySkuCode);
            this.initialTaobaoOrderItem(productSku, orderItem);
            orderItem.setEqualExchangeRatio(result.getExchangeQty().intValue() + ":" + result.getEquityQty().intValue());
            orderItem.setIsEqualExchange(1);
            //手动替换时不会为空
            if (StringUtils.isEmpty(orderItem.getEqualExchangeMark())) {
                orderItem.setEqualExchangeMark(EQUAL_EXCHANGE_MARK + item.getId());
            }
        }
        item.setEqualExchangeMark(EQUAL_EXCHANGE_MARK + item.getId());
        return orderItem;
    }

    private void initialTaobaoOrderItem(ProductSku productSku, OcBOrderItem item) {
        if (productSku != null) {
            item.setPsCProId(productSku.getProdId());
            // ProECode
            item.setSex(productSku.getSex());
            //2019-08-30吊牌价改为取商品表数据 //吊牌价
            item.setPriceTag(productSku.getPricelist());
            item.setPsCProEcode(productSku.getProdCode());
            item.setPsCProEname(productSku.getName());
            item.setPsCSkuId(productSku.getId());
            String psSkuEcode = productSku.getSkuEcode();
            item.setPsCSkuEcode(psSkuEcode);
            item.setPsCClrEcode(productSku.getColorCode());
            item.setPsCClrEname(productSku.getColorName());
            item.setPsCClrId(productSku.getColorId());
            item.setStandardWeight(productSku.getWeight());
            item.setSkuSpec(productSku.getSkuSpec());
            item.setPsCSizeEcode(productSku.getSizeCode());
            item.setPsCSizeEname(productSku.getSizeName());
            item.setPsCSizeId(productSku.getSizeId());
            item.setPsCProMaterieltype(productSku.getMaterialType());
            item.setBarcode(productSku.getBarcode69());
            //标准价。
            item.setPriceList(productSku.getPricelist());
            item.setPriceTag(productSku.getPricelist());
            item.setMDim4Id(productSku.getMDim4Id());
            item.setMDim6Id(productSku.getMDim6Id());
            if ("Y".equals(productSku.getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }


    private List<EquityExchangeResult> handleEquityExchange(OcBOrderItem item, List<StCEquityBarterStrategyItem> barterStrategyItems) {
        //匹配
        BigDecimal qty1 = item.getQty();
        BigDecimal qty = new BigDecimal(qty1.toString());
        //数量排序
        barterStrategyItems.sort((o1, o2) -> o2.getQty().compareTo(o1.getQty()));
        List<EquityExchangeResult> results = new ArrayList<>();
        for (StCEquityBarterStrategyItem barterStrategyItem : barterStrategyItems) {
            if (qty.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            BigDecimal exchangeQty = barterStrategyItem.getQty();
            BigDecimal equityQty = barterStrategyItem.getEquityQty();
            if (BigDecimal.ZERO.compareTo(equityQty) == 0 || BigDecimal.ZERO.compareTo(exchangeQty) == 0) {
                continue;
            }
            int i = qty.intValue() / exchangeQty.intValue();
            //判断是否能被整除  如果能被整除 则除数*数量 就是最终的数量
            if (qty.compareTo(exchangeQty) > 0 && i != 0) {
                BigDecimal divide = new BigDecimal(i).multiply(equityQty);
                EquityExchangeResult result = new EquityExchangeResult();
                result.setItemId(item.getId());
                result.setEquityQty(divide);
                result.setEquitySkuCode(barterStrategyItem.getEquitySkuCode());
                result.setExchangeQty(exchangeQty.multiply(new BigDecimal(i)));
                result.setOutStockNoRestore(barterStrategyItem.getOutStockNoRestore() == null ? "0" : barterStrategyItem.getOutStockNoRestore());
                results.add(result);
                qty = qty.subtract(exchangeQty.multiply(new BigDecimal(i)));
                continue;
            }
            if (exchangeQty.compareTo(qty) <= 0) {
                EquityExchangeResult result = new EquityExchangeResult();
                result.setItemId(item.getId());
                result.setEquityQty(barterStrategyItem.getEquityQty());
                result.setEquitySkuCode(barterStrategyItem.getEquitySkuCode());
                result.setExchangeQty(barterStrategyItem.getQty());
                result.setOutStockNoRestore(barterStrategyItem.getOutStockNoRestore() == null ? "0" : barterStrategyItem.getOutStockNoRestore());
                qty = qty.subtract(exchangeQty);
                results.add(result);
            }
        }
        //如果results为空  说明没有匹配上  所以这条明细不用处理
        if (qty.compareTo(BigDecimal.ZERO) > 0 && CollectionUtils.isNotEmpty(results)) {
            EquityExchangeResult result = new EquityExchangeResult();
            result.setItemId(item.getId());
            result.setEquityQty(qty);
            result.setOriginalItem(true);
            results.add(result);
        }
        return results;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveEquityExchange(List<EquityExchangeItem> equityExchangeItems, OcBOrder
            ocBOrder, List<OcBOrderItem> delItemList) {
        List<Long> delItemIds = new ArrayList<>();
        List<OcBOrderItem> saveItems = new ArrayList<>();
        List<OcBOrderEqualExchangeItem> exchangeItems = new ArrayList<>();
        List<OcBOrderNaiKa> orderNaiKas = new ArrayList<>();
        for (EquityExchangeItem equityExchangeItem : equityExchangeItems) {
            OcBOrderItem ocBOrderItem = equityExchangeItem.getOcBOrderItem();
            if (ocBOrderItem != null) {
                delItemIds.add(ocBOrderItem.getId());
            }
            List<OcBOrderItem> ocBOrderItems = equityExchangeItem.getOcBOrderItems();
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                saveItems.addAll(ocBOrderItems);
            }
            OcBOrderEqualExchangeItem equalExchangeItem = equityExchangeItem.getEqualExchangeItem();
            if (equalExchangeItem != null) {
                exchangeItems.add(equalExchangeItem);
            }
            List<OcBOrderNaiKa> ocBOrderNaiKas = equityExchangeItem.getOcBOrderNaiKas();
            if (CollectionUtils.isNotEmpty(ocBOrderNaiKas)) {
                orderNaiKas.addAll(ocBOrderNaiKas);
            }
        }
        if (CollectionUtils.isNotEmpty(delItemList)) {
            List<Long> items = delItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            ocBOrderItemMapper.deleteOcBOrderItemById(ocBOrder.getId(), items);
            ocBOrderItemMapper.batchInsert(saveItems);
            if (CollectionUtils.isNotEmpty(orderNaiKas)) {
                ocBOrderNaiKaMapper.batchInsert(orderNaiKas);
            }
            List<String> equalExchangeMarks = delItemList.stream().map(OcBOrderItem::getEqualExchangeMark).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(exchangeItems) && CollectionUtils.isNotEmpty(equalExchangeMarks)) {
                ocBOrderEqualExchangeItemMapper.delByEqualExchangeMark(ocBOrder.getId(), equalExchangeMarks);
                ocBOrderEqualExchangeItemMapper.batchInsert(exchangeItems);
            }
        } else {
            //删除
            ocBOrderItemMapper.deleteOcBOrderItemById(ocBOrder.getId(), delItemIds);
            ocBOrderItemMapper.batchInsert(saveItems);
            ocBOrderEqualExchangeItemMapper.batchInsert(exchangeItems);
            if (CollectionUtils.isNotEmpty(orderNaiKas)) {
                ocBOrderNaiKaMapper.batchInsert(orderNaiKas);
            }
        }
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setIsEqualExchange(1);
        ocBOrderMapper.updateById(order);

    }


    @Data
    public static class EquityExchangeResult implements Serializable {
        /**
         * 明细id
         */
        private Long itemId;
        /**
         * 对等skuId
         */
        private String equitySkuCode;

        /**
         * 换货数量
         */
        private BigDecimal exchangeQty;

        /**
         * 对等数量
         */
        private BigDecimal equityQty;

        /**
         * 是否为原明细 true 是
         */
        private boolean originalItem;

        /**
         * 缺货不还原
         */
        private String outStockNoRestore;
    }

    @Data
    private static class EquityExchangeItem implements Serializable {
        /**
         * 对等的明细
         */
        private OcBOrderEqualExchangeItem equalExchangeItem;
        /**
         * 原明细
         */
        private OcBOrderItem ocBOrderItem;
        /**
         * 换货后的明细
         */
        private List<OcBOrderItem> ocBOrderItems;

        private List<OcBOrderNaiKa> ocBOrderNaiKas;

    }


}
