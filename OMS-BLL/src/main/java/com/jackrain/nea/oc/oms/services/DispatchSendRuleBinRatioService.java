//package com.jackrain.nea.oc.oms.services;
//
//import com.jackrain.nea.oc.oms.model.SendPlanExecution;
//import com.jackrain.nea.sg.basic.model.result.SgSumStorageQueryResult;
//import com.jackrain.nea.st.model.enums.SendRuleEtype;
//import com.jackrain.nea.st.model.table.StCSendRuleDO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @create 2020-06-17
// * @desc 派单规则-按分仓比例调度
// **/
//@Component
//@Slf4j
//public class DispatchSendRuleBinRatioService extends AbstractDispatchSendRule {
//    /**
//     * 派单规则处理
//     *
//     * @param sendPlanExecution   派单方案执行参数
//     * @param storageQueryResults 实体仓库存
//     * @param stCSendRuleDO       派单规则
//     * @return
//     * @warning 找不到一定要返回null，允许一个派单方案和派单规则执行，否则会中断派单方案和派单规则
//     */
//    @Override
//    public Long execute(SendPlanExecution sendPlanExecution, List<SgSumStorageQueryResult> storageQueryResults, StCSendRuleDO stCSendRuleDO) {
//        //TODO 派单规则-按分仓比例调度
//        return null;
//    }
//
//    /**
//     * 进行预处理
//     */
//    @Override
//    public void prepare() {
//
//    }
//
//    /**
//     * 判断是否支持的派单规则类型
//     *
//     * @param stCSendRuleDO 派单规则
//     */
//    @Override
//    public boolean support(StCSendRuleDO stCSendRuleDO) {
//        return SendRuleEtype.BIN_RATIO.getEtype().equals(stCSendRuleDO.getEtype());
//    }
//}
