package com.jackrain.nea.oc.oms.services;

import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.jackrain.nea.oc.oms.model.SendPlanExecution;
import com.jackrain.nea.st.model.table.StCSendRuleDO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-06-17
 * @desc 派单规则调度
 **/
public interface DispatchSendRule {
    /**
     * 派单规则处理
     *
     * @param sendPlanExecution   派单方案执行参数
     * @param storageQueryResults 实体仓库存
     * @param stCSendRuleDO       派单规则
     * @return
     * @warning 找不到一定要返回null，允许一个派单方案和派单规则执行，否则会中断派单方案和派单规则
     */
    Long execute(SendPlanExecution sendPlanExecution, List storageQueryResults, StCSendRuleDO stCSendRuleDO);

    /**
     * 进行预处理
     */
    void prepare();

    /**
     * 判断是否支持的派单规则类型 派单规则
     */
    boolean support(StCSendRuleDO stCSendRuleDO);

    /**
     * 结束后执行
     */
    void complete();
}
