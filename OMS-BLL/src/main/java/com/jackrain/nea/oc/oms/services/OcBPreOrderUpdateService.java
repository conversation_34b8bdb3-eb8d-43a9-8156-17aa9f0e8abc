package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.RegionInfo;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPaymentMapper;
import com.jackrain.nea.oc.oms.mapper.OcBPreOrderMapper;
import com.jackrain.nea.oc.oms.mapperservice.OcBPreOrderItemMapperService;
import com.jackrain.nea.oc.oms.mapperservice.OcBPreOrderMapperService;
import com.jackrain.nea.oc.oms.model.enums.OcBPreOrderEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsPayStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.OcBPreOrder;
import com.jackrain.nea.oc.oms.model.table.OcBPreOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;

/**
 * @ClassName OcBPreOrderUpdateService
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/12 16:07
 * @Version 1.0
 */
@Service
@Slf4j
public class OcBPreOrderUpdateService {

    @Autowired
    private OcBPreOrderMapperService ocBPreOrderMapperService;
    @Autowired
    private OcBPreOrderMapper ocBPreOrderMapper;
    @Autowired
    private RegionNewService regionNewService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBPreOrderItemMapperService preOrderItemMapperService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBorderItemMapper;
    @Autowired
    private OcBOrderPaymentMapper paymentMapper;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ThreadPoolTaskExecutor preOrderUpdateThreadPoolExecutor;

    private static final String ORDER_TABLE_NAME = "OC_B_ORDER";
    private static final String ORDER_ITEM_TABLE_NAME = "OC_B_ORDER_ITEM";
    @Autowired
    private BuildSequenceUtil sequenceUtil;

    public ValueHolderV14 updateShop(List<String> tids, String shopTitle, User user) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14(ResultCode.SUCCESS, "success");
        // 先根据tid 查询出来所有的预导入订单
        List<OcBPreOrder> ocBPreOrders = ocBPreOrderMapperService.getOrderByTids(tids);
        if (CollectionUtils.isEmpty(ocBPreOrders)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("平台单号有误");
            return valueHolderV14;
        }
        List<CpShop> cpShopList = cpRpcService.queryByShopTitle(Collections.singletonList(shopTitle));
        List<String> unUpdateTids = new ArrayList<>();
        if (CollectionUtils.isEmpty(cpShopList)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("平台单号有误");
        } else {
            CpShop cpShop = cpShopList.get(0);
            for (OcBPreOrder ocBPreOrder : ocBPreOrders) {
                OcBPreOrder updateOcBPreOrder = new OcBPreOrder();
                if (ocBPreOrder.getTransferStatus().equals(OcBPreOrderEnum.OcBPreTransferEnum.TRANSFERED.getVal()) ||
                        ocBPreOrder.getTransferStatus().equals(OcBPreOrderEnum.OcBPreTransferEnum.TRANSFERING.getVal())) {
                    updateOcBPreOrder.setId(ocBPreOrder.getId());
                    updateOcBPreOrder.setTid(ocBPreOrder.getTid());
                    updateOcBPreOrder.setModifieddate(new Date());
                    ocBPreOrderMapper.updateById(updateOcBPreOrder);
                    unUpdateTids.add(ocBPreOrder.getTid());
                    continue;
                }
                updateOcBPreOrder.setId(ocBPreOrder.getId());
                updateOcBPreOrder.setTid(ocBPreOrder.getTid());
                updateOcBPreOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
                updateOcBPreOrder.setCpCShopId(cpShop.getCpCShopId());
                updateOcBPreOrder.setCpCShopEcode(cpShop.getEcode());
                updateOcBPreOrder.setModifieddate(new Date());
                ocBPreOrderMapper.updateById(updateOcBPreOrder);
            }
        }
        if (CollectionUtils.isNotEmpty(unUpdateTids)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(JSONUtil.toJsonStr(unUpdateTids) + "单据状态有误，其他状态正常单据执行更新店铺完成");
        }
        return valueHolderV14;
    }

    public ValueHolder transfer(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", "success");

        DefaultWebEvent event = querySession.getEvent();
        User user = querySession.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONArray jsonArray = JSON.parseArray(param.get("ids").toString());
        log.info("OcBPreOrderUpdateService.execute.jsonArray:{}", JSONUtil.toJsonStr(jsonArray));

        for (Object object : jsonArray) {
            try {
                Long id = Long.valueOf(object.toString());
                extracted(user, id);
            } catch (Exception e) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", e.getMessage());
            }
        }

        return vh;
    }

    private void extracted(User user, Long id) {
        OcBPreOrder ocBPreOrder = ocBPreOrderMapper.selectById(id);
        OcBPreOrder updateOcBPreOrder = new OcBPreOrder();
        if (ocBPreOrder.getTransferStatus().equals(OcBPreOrderEnum.OcBPreTransferEnum.TRANSFERED.getVal())) {
            updateOcBPreOrder.setId(id);
            updateOcBPreOrder.setTid(ocBPreOrder.getTid());
            updateOcBPreOrder.setModifieddate(new Date());
            ocBPreOrderMapperService.updateById(updateOcBPreOrder);
            return;
        }
        if (ocBPreOrder.getTransferStatus().equals(OcBPreOrderEnum.OcBPreTransferEnum.TRANSFERING.getVal())) {
            updateOcBPreOrder.setId(id);
            updateOcBPreOrder.setTid(ocBPreOrder.getTid());
            updateOcBPreOrder.setModifieddate(new Date());
            updateOcBPreOrder.setSysremark("转入时状态异常");
            ocBPreOrderMapperService.updateById(updateOcBPreOrder);
            return;
        }

        // 先查询零售发货单是否有数据
        List<OcBOrder> ocBOrderList = GSI4Order.getOrderListBySourceCode(ocBPreOrder.getTid());
        if (CollectionUtils.isNotEmpty(ocBOrderList)) {
            updateOcBPreOrder.setId(id);
            updateOcBPreOrder.setTid(ocBPreOrder.getTid());
            updateOcBPreOrder.setModifieddate(new Date());
            updateOcBPreOrder.setTransferStatus(OcBPreOrderEnum.OcBPreTransferEnum.TRANSFER_FAILED.getVal());
            updateOcBPreOrder.setSysremark("转入时已存在零售发货单");
            ocBPreOrderMapperService.updateById(updateOcBPreOrder);
            return;
        }

        // 先把状态调整为转换中
        updateOcBPreOrder.setId(id);
        updateOcBPreOrder.setTid(ocBPreOrder.getTid());
        updateOcBPreOrder.setTransferStatus(OcBPreOrderEnum.OcBPreTransferEnum.TRANSFERING.getVal());
        updateOcBPreOrder.setModifieddate(new Date());
        ocBPreOrderMapperService.updateById(updateOcBPreOrder);
        try {
            // 先根据店铺名称、省、市、区名称 来校验合法性
            ProvinceCityAreaInfo provinceCityAreaInfo =
                    regionNewService.selectProvinceCityAreaInfo(ocBPreOrder.getCpCRegionProvinceEname(), ocBPreOrder.getCpCRegionCityEname(), ocBPreOrder.getCpCRegionAreaEname());

            if (Objects.isNull(provinceCityAreaInfo) || Objects.isNull(provinceCityAreaInfo.getProvinceInfo())) {
                throw new NDSException("平台单号:" + ocBPreOrder.getTid() + ", 匹配省市区失败,不存在省：" + ocBPreOrder.getCpCRegionProvinceEname());
            }
            if (Objects.isNull(provinceCityAreaInfo.getCityInfo())) {
                throw new NDSException("平台单号:" + ocBPreOrder.getTid() +
                        ", 匹配市区失败，省" + ocBPreOrder.getCpCRegionProvinceEname() + ", 不存在城市" + ocBPreOrder.getCpCRegionCityEname());
            }

            // 店铺信息
            List<CpShop> cpShopList =
                    cpRpcService.queryByShopTitle(Collections.singletonList(ocBPreOrder.getCpCShopTitle()));
            if (CollectionUtils.isEmpty(cpShopList)) {
                throw new NDSException("平台单号:" + ocBPreOrder.getTid() + ", 匹配店铺信息失败,店铺名称：" + ocBPreOrder.getCpCShopTitle());
            }
            CpShop cpShop = cpShopList.get(0);
            OcBOrder ocBOrder = getOcBOrder(user, id, ocBPreOrder, updateOcBPreOrder, provinceCityAreaInfo, cpShop);
            if (ocBOrder == null) {
                return;
            }


            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(ocBOrder.getId());
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTask.setIsactive("Y");
            OcBOrderLog ocBOrderLog = omsOrderLogService.getOcBOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_IMPORT.getKey(),
                    "订单导入成功", "", "", user);

            List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
            List<OcBPreOrderItem> ocBPreOrderItemList = preOrderItemMapperService.getByPreOrderId(ocBPreOrder.getId());
            if (CollectionUtils.isEmpty(ocBPreOrderItemList)) {
                throw new NDSException("平台单号:" + ocBPreOrder.getTid() + ", 明细为空");
            }
            for (OcBPreOrderItem preOrderItem : ocBPreOrderItemList) {
                // 校验商品是否有问题
                ProductSku productSku = psRpcService.selectProductSku(preOrderItem.getPsCSkuEcode());
                if (ObjectUtil.isNull(productSku)) {
                    updateOcBPreOrder.setId(id);
                    updateOcBPreOrder.setTid(ocBPreOrder.getTid());
                    updateOcBPreOrder.setModifieddate(new Date());
                    updateOcBPreOrder.setSysremark("商品编码" + preOrderItem.getPsCSkuEcode() + "不存在");
                    ocBPreOrderMapperService.updateById(updateOcBPreOrder);
                    return;
                }
                OcBOrderItem ocBOrderItem = getOcBOrderItem(user, ocBOrder, preOrderItem, productSku);
                ocBOrderItemList.add(ocBOrderItem);
            }
            recountAmount(ocBOrder, ocBOrderItemList);
            OcBOrderPayment payment = createPayMent(ocBOrder);
            applicationContext.getBean(OcBPreOrderUpdateService.class).insertForPreOrder(ocBOrder, ocBOrderItemList, payment, ocBOrderLog, toBeConfirmedTask);
            updateOcBPreOrder.setTransferStatus(OcBPreOrderEnum.OcBPreTransferEnum.TRANSFERED.getVal());
            updateOcBPreOrder.setSysremark("");
        } catch (Exception e) {
            updateOcBPreOrder.setTransferStatus(OcBPreOrderEnum.OcBPreTransferEnum.TRANSFER_FAILED.getVal());
            updateOcBPreOrder.setSysremark(e.getMessage());
        }
        ocBPreOrderMapperService.updateById(updateOcBPreOrder);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertForPreOrder(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList, OcBOrderPayment payment,
                                  OcBOrderLog ocBOrderLog, OcBToBeConfirmedTask ocBToBeConfirmedTask) {
        ocBOrderMapper.insert(ocBOrder);
        ocBorderItemMapper.batchInsert(ocBOrderItemList);
        paymentMapper.insert(payment);
        omsOrderLogService.save(Collections.singletonList(ocBOrderLog));
        toBeConfirmedTaskService.insertToBeConfirmedTask(ocBToBeConfirmedTask);
    }

    private OcBOrder getOcBOrder(User user, Long id, OcBPreOrder ocBPreOrder, OcBPreOrder updateOcBPreOrder, ProvinceCityAreaInfo provinceCityAreaInfo, CpShop cpShop) {
        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(ModelUtil.getSequence(ORDER_TABLE_NAME));
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        ocBOrder.setIsInterecept(0);
        ocBOrder.setOrderDate(ocBPreOrder.getOrderDate());
        ocBOrder.setIsInreturning(0);
        ocBOrder.setQtySplit(0L);
        ocBOrder.setIsSplit(0);
        ocBOrder.setIsMerge(0);
        ocBOrder.setIsCancelMerge(0);
        ocBOrder.setOrderSource("手工新增");
        ocBOrder.setOrderStatus(50);
        ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
        ocBOrder.setInvoiceStatus(0);
        ocBOrder.setTid(ocBOrder.getSourceCode());
        ocBOrder.setOccupyStatus(OrderOccupyStatus.STATUS_0);
        ocBOrder.setIsSameCityPurchase(0);

        ocBOrder.setOrderDate(Optional.ofNullable(ocBOrder.getOrderDate()).orElse(new Date()));
        if (ocBOrder.getServiceAmt() == null) {
            ocBOrder.setServiceAmt(BigDecimal.ZERO);
        }
        ocBOrder.setOrderFlag("0");
        ocBOrder.setOutStatus(1);
        ocBOrder.setWmsCancelStatus(0);
        ocBOrder.setRefundConfirmStatus(0);
        ocBOrder.setAutoAuditStatus(0);
        ocBOrder.setIsModifiedOrder(0);
        ocBOrder.setSplitStatus(0);
        ocBOrder.setIsExchangeNoIn(0L);
        // 设置店铺信息
        if (cpShop == null) {
            updateOcBPreOrder.setId(id);
            updateOcBPreOrder.setTid(ocBPreOrder.getTid());
            updateOcBPreOrder.setModifieddate(new Date());
            updateOcBPreOrder.setSysremark("店铺异常");
            ocBPreOrderMapperService.updateById(updateOcBPreOrder);
            return null;
        } else {
            ocBOrder.setCpCShopId(cpShop.getCpCShopId());
            ocBOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
            ocBOrder.setCpCShopSellerNick(cpShop.getSellerNick());
            ocBOrder.setCpCShopEcode(cpShop.getEcode());
            ocBOrder.setPlatform(Optional.ofNullable(cpShop.getCpCPlatformId()).orElse(-1L).intValue());
        }
        RegionInfo province = provinceCityAreaInfo.getProvinceInfo();
        RegionInfo city = provinceCityAreaInfo.getCityInfo();
        RegionInfo area = provinceCityAreaInfo.getAreaInfo();
        ocBOrder.setReceiverName(ocBPreOrder.getReceiverName());
        ocBOrder.setReceiverMobile(ocBPreOrder.getReceiverMobile());
        ocBOrder.setReceiverPhone(ocBPreOrder.getReceiverPhone());
        ocBOrder.setCpCRegionProvinceId(province.getId());
        ocBOrder.setCpCRegionProvinceEcode(province.getCode());
        ocBOrder.setCpCRegionProvinceEname(province.getName());
        ocBOrder.setCpCRegionCityId(city.getId());
        ocBOrder.setCpCRegionCityEcode(city.getCode());
        ocBOrder.setCpCRegionCityEname(city.getName());
        ocBOrder.setReceiverZip(ocBPreOrder.getReceiverZip());
        ocBOrder.setSalesmanName(ocBPreOrder.getSalesmanName());
        if (ObjectUtil.isNotNull(area)) {
            ocBOrder.setCpCRegionAreaId(area.getId());
            ocBOrder.setCpCRegionAreaEcode(area.getCode());
            ocBOrder.setCpCRegionAreaEname(area.getName());
        } else {
            ocBOrder.setCpCRegionAreaEname(ocBPreOrder.getCpCRegionAreaEname());
        }
        ocBOrder.setReceiverAddress(ocBPreOrder.getReceiverAddress());

        ocBOrder.setShipAmt(ocBPreOrder.getShipAmt() == null ? BigDecimal.ZERO : ocBPreOrder.getShipAmt());
        ocBOrder.setUserNick(ocBPreOrder.getUserNick());
        ocBOrder.setTid(ocBPreOrder.getTid());
        ocBOrder.setSourceCode(ocBPreOrder.getTid());
        ocBOrder.setPayType(ocBPreOrder.getPayType());
        ocBOrder.setPayTime(ocBPreOrder.getPayTime());
        ocBOrder.setInsideRemark("");
        ocBOrder.setBuyerMessage(ocBPreOrder.getBuyerMessage());
        ocBOrder.setSellerMemo(ocBPreOrder.getSellerMemo());
        if (ocBOrder.getReceiverAddress() != null && ocBOrder.getReceiverAddress().length() > 200) {
            updateOcBPreOrder.setId(id);
            updateOcBPreOrder.setTid(ocBPreOrder.getTid());
            updateOcBPreOrder.setModifieddate(new Date());
            updateOcBPreOrder.setSysremark("详细地址的长度不能大于200");
            ocBPreOrderMapperService.updateById(updateOcBPreOrder);
            return null;
        }
        OrderAddressConvertUtil.convert(ocBOrder);
        setDefault(ocBOrder);
        makeCreateField(ocBOrder, user);
        ocBOrder.setOwnerename(user.getEname());
        ocBOrder.setModifierename(user.getEname());
        return ocBOrder;
    }

    private OcBOrderPayment createPayMent(OcBOrder ocBOrderExtend) {
        OcBOrderPayment payment = new OcBOrderPayment();
        payment.setId(ModelUtil.getSequence("OC_B_ORDER_PAYMENT"));
        payment.setOcBOrderId(ocBOrderExtend.getId());
        payment.setPayType(ocBOrderExtend.getPayType());
        payment.setAmtOrder(ocBOrderExtend.getOrderAmt());
        payment.setPaymentAmt(ocBOrderExtend.getOrderAmt());
        /*支付时间逻辑优化，如果订单上有支付时间则以订单的支付时间为准*/
        payment.setPayTime(ocBOrderExtend.getPayTime() == null ? new Date(System.currentTimeMillis()) : ocBOrderExtend.getPayTime());
        payment.setOwnerename(SystemUserResource.getRootUser().getEname());
        payment.setPayStatus(OmsPayStatus.PAID.toInteger());
        payment.setAdClientId((long) SystemUserResource.getRootUser().getClientId());
        payment.setAdOrgId((long) SystemUserResource.getRootUser().getOrgId());
        payment.setIsactive("Y");
        payment.setCreationdate(new Date(System.currentTimeMillis()));
        return payment;

    }

    private void recountAmount(OcBOrder ocBOrder, List<OcBOrderItem> orderItems) {
        ocBOrder.setIsInterecept(0);
        BigDecimal productAmt = BigDecimal.ZERO;
        BigDecimal orderAmt;
        BigDecimal productDiscountAmt = BigDecimal.ZERO;
        BigDecimal orderDiscountAmt = BigDecimal.ZERO;
        BigDecimal qtyAll = BigDecimal.ZERO;
        BigDecimal adjustAmt = BigDecimal.ZERO;
        BigDecimal weight = BigDecimal.ZERO;
        Boolean flag = false;
        for (OcBOrderItem item : orderItems) {
            if (item.getIsGift() == 1) {
                flag = true;
                item.setPrice(BigDecimal.ZERO);
            }
            Long proType = Optional.ofNullable(item.getProType()).orElse(0L);
            if (proType.intValue() == SkuType.GIFT_PRODUCT || proType.intValue() == SkuType.COMBINE_PRODUCT) {
                continue;
            }
            // 打组合标
            if (proType.intValue() == SkuType.NO_SPLIT_COMBINE) {
                ocBOrder.setIsCombination(1);
            }
            productAmt = productAmt.add(Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO)
                    .multiply(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO)));
            productDiscountAmt =
                    productDiscountAmt.add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO));
            orderDiscountAmt =
                    orderDiscountAmt.add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            adjustAmt = adjustAmt.add(Optional.ofNullable(item.getAdjustAmt()).orElse(BigDecimal.ZERO));
            qtyAll = qtyAll.add(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO));
            weight = weight.add(Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO));
        }

        ocBOrder.setProductAmt(productAmt);
        ocBOrder.setProductDiscountAmt(productDiscountAmt);
        ocBOrder.setOrderDiscountAmt(orderDiscountAmt);
        ocBOrder.setAdjustAmt(adjustAmt);
        ocBOrder.setQtyAll(qtyAll);
        orderAmt = productAmt.subtract(productDiscountAmt)
                .subtract(orderDiscountAmt)
                .add(adjustAmt).add(ocBOrder.getShipAmt());
        ocBOrder.setOrderAmt(orderAmt);
        ocBOrder.setReceivedAmt(orderAmt);
        ocBOrder.setAmtReceive(orderAmt);
        ocBOrder.setServiceAmt(Optional.ofNullable(ocBOrder.getServiceAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setConsignAmt(Optional.ofNullable(ocBOrder.getConsignAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setConsignShipAmt(Optional.ofNullable(ocBOrder.getConsignShipAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setCodAmt(Optional.ofNullable(ocBOrder.getCodAmt()).orElse(BigDecimal.ZERO));
        // ocBOrder.setOperateAmt(Optional.ofNullable(ocBOrder.getOperateAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setJdReceiveAmt(Optional.ofNullable(ocBOrder.getJdReceiveAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setJdSettleAmt(Optional.ofNullable(ocBOrder.getJdSettleAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setLogisticsCost(Optional.ofNullable(ocBOrder.getLogisticsCost()).orElse(BigDecimal.ZERO));
        ocBOrder.setWeight(weight);
        if (flag) {
            ocBOrder.setIsHasgift(YesNoEnum.Y.getVal());
        }
    }

    private void setDefault(OcBOrder ocBOrder) {
        ocBOrder.setOrderStatus(Optional.ofNullable(ocBOrder.getOrderStatus()).orElse(1));
        ocBOrder.setIsInvoice(Optional.ofNullable(ocBOrder.getIsInvoice()).orElse(0));
        ocBOrder.setIsGeninvoiceNotice(Optional.ofNullable(ocBOrder.getIsGeninvoiceNotice()).orElse(0));
        ocBOrder.setIsCalcweight(Optional.ofNullable(ocBOrder.getIsCalcweight()).orElse(0));
        ocBOrder.setIsMerge(Optional.ofNullable(ocBOrder.getIsMerge()).orElse(0));
        ocBOrder.setIsSplit(Optional.ofNullable(ocBOrder.getIsSplit()).orElse(0));
        ocBOrder.setIsInterecept(Optional.ofNullable(ocBOrder.getIsInterecept()).orElse(0));
        ocBOrder.setIsInreturning(Optional.ofNullable(ocBOrder.getIsInreturning()).orElse(0));
        ocBOrder.setIsHasgift(Optional.ofNullable(ocBOrder.getIsHasgift()).orElse(0));
        ocBOrder.setIsJcorder(Optional.ofNullable(ocBOrder.getIsJcorder()).orElse(0));
        ocBOrder.setIsCombination(Optional.ofNullable(ocBOrder.getIsCombination()).orElse(0));
        ocBOrder.setIsOutUrgency(Optional.ofNullable(ocBOrder.getIsOutUrgency()).orElse(0));
        //ocBOrder.setIsShopCommission(Optional.ofNullable(ocBOrder.getIsShopCommission()).orElse(0));
        ocBOrder.setIsHasTicket(Optional.ofNullable(ocBOrder.getIsHasTicket()).orElse(0));
        ocBOrder.setMergeSourceCode(Optional.ofNullable(ocBOrder.getSourceCode()).orElse(""));
    }

    private OcBOrderItem getOcBOrderItem(User user, OcBOrder ocBOrder, OcBPreOrderItem preOrderItem, ProductSku productSku) {
        OcBOrderItem ocBOrderItem = new OcBOrderItem();
        ocBOrderItem.setRealAmt(preOrderItem.getPriceActual().multiply(preOrderItem.getQty()));
        if (preOrderItem.getPlatformPrice() == null) {
            ocBOrderItem.setPrice(preOrderItem.getPriceActual());
        } else {
            ocBOrderItem.setPrice(preOrderItem.getPlatformPrice());
        }
        ocBOrderItem.setPsCSkuEcode(productSku.getSkuEcode());
        ocBOrderItem.setPsCSkuEname(productSku.getSkuName());
        ocBOrderItem.setPsCProEcode(productSku.getEcode());
        ocBOrderItem.setPsCProId(productSku.getId());
        ocBOrderItem.setPsCProEname(productSku.getName());
        ocBOrderItem.setQty(preOrderItem.getQty());

        makeCreateField(ocBOrderItem, user);
        ocBOrderItem.setId(ModelUtil.getSequence(ORDER_ITEM_TABLE_NAME));
        ocBOrderItem.setOcBOrderId(ocBOrder.getId());
        ocBOrderItem.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode().toUpperCase());

        // 供应类型 0 普通 1.代销轻供 2.寄售轻供
        ocBOrderItem.setPsCProSupplyType(productSku.getPsCProSupplyType());
        ocBOrderItem.setIsManualAdd("1");
        ocBOrderItem.setQtyRefund(BigDecimal.ZERO);
        ocBOrderItem.setTid(ocBOrder.getTid());
        ocBOrderItem.setProType((long) productSku.getSkuType());
        ocBOrderItem.setIsGift(Optional.ofNullable(ocBOrderItem.getIsGift()).orElse(0));
        ocBOrderItem.setNumIid(Optional.ofNullable(ocBOrderItem.getNumIid()).orElse("0"));
//                // 一米有品
//                item.setReserveVarchar04(item.getReserveVarchar04());
        ocBOrderItem.setPsCSkuId(productSku.getId());
        ocBOrderItem.setPsCSkuPtEcode(productSku.getSkuEcode());
        ocBOrderItem.setPsCSkuEname(productSku.getSkuName());
        ocBOrderItem.setSkuSpec(productSku.getSkuSpec());
        ocBOrderItem.setBarcode(productSku.getBarcode69());
        ocBOrderItem.setPsCProId(productSku.getProdId());
        ocBOrderItem.setPsCProEcode(productSku.getProdCode());
        ocBOrderItem.setPsCProEname(productSku.getName());
        ocBOrderItem.setPsCBrandId(productSku.getPsCBrandId());
        ocBOrderItem.setSex(productSku.getSex());
        ocBOrderItem.setPsCClrId(productSku.getColorId());
        ocBOrderItem.setPsCClrEcode(productSku.getColorCode());
        ocBOrderItem.setPsCClrEname(productSku.getColorName());
        ocBOrderItem.setPsCSizeId(productSku.getSizeId());
        ocBOrderItem.setPsCSizeEcode(productSku.getSizeCode());
        ocBOrderItem.setPsCSizeEname(productSku.getSizeName());
        ocBOrderItem.setPsCProMaterieltype(productSku.getMaterialType());
        ocBOrderItem.setStandardWeight(Optional.ofNullable(productSku.getWeight()).orElse(BigDecimal.ZERO));
        ocBOrderItem.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());

        // 一头牛优化内容：补充字段赋值 0917 产线问题修复
        ocBOrderItem.setMDim4Id(productSku.getMDim4Id());
        ocBOrderItem.setMDim6Id(productSku.getMDim6Id());
        if ("Y".equals(productSku.getIsEnableExpiry())) {
            ocBOrderItem.setIsEnableExpiry(1);
        } else {
            ocBOrderItem.setIsEnableExpiry(0);
        }
        if (ObjectUtil.equal(preOrderItem.getIsGift(), 1)) {
            ocBOrderItem.setPrice(BigDecimal.ZERO);
            ocBOrderItem.setIsGift(1);
        }

        ocBOrderItem.setPriceTag(Optional.ofNullable(productSku.getPricelist()).orElse(BigDecimal.ZERO)); // 吊牌价
        ocBOrderItem.setPriceList(Optional.ofNullable(productSku.getPricelist()).orElse(BigDecimal.ZERO)); // 吊牌价
        // 金额相关字段的值依靠前端传入，若前端未传默认给0 成交金额，成交单价，平台售价之前已经处理了
        ocBOrderItem.setOrderSplitAmt(Optional.ofNullable(ocBOrderItem.getOrderSplitAmt()).orElse(BigDecimal.ZERO)); //平摊金额
        ocBOrderItem.setAmtDiscount(Optional.ofNullable(ocBOrderItem.getAmtDiscount()).orElse(BigDecimal.ZERO)); //商品优惠金额
        // 调整金额
        BigDecimal adjustAmt = ocBOrderItem.getRealAmt() // 成交金额
                .subtract(ocBOrderItem.getPrice().multiply(ocBOrderItem.getQty())) //平台售价*数量 = 商品金额
                .add(Optional.ofNullable(ocBOrderItem.getAmtDiscount()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(ocBOrderItem.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
        ocBOrderItem.setAdjustAmt(adjustAmt); //调整金额
        ocBOrderItem.setPriceActual(preOrderItem.getPriceActual());

        if (productSku.getSkuType() == SkuType.NORMAL_PRODUCT) {
            ocBOrderItem.setProType(Long.valueOf(productSku.getSkuType()));
        } else {
            ocBOrderItem.setProType(Long.valueOf(SkuType.NO_SPLIT_COMBINE));
        }
        ocBOrderItem.setOwnerename(user.getEname());
        ocBOrderItem.setModifierename(user.getEname());
        return ocBOrderItem;
    }

    private void makeCreateField(BaseModel model, User user) {
        Date date = new Date();
        //所属公司
        model.setAdClientId((long) user.getClientId());
        //所属组织
        model.setAdOrgId((long) user.getOrgId());
        //创建人id
        model.setOwnerid(Long.valueOf(user.getId()));
        //创建时间
        model.setCreationdate(date);
        //创建人用户名
        model.setOwnername(user.getName());
        //修改人id
        model.setModifierid(Long.valueOf(user.getId()));
        //修改人用户名
        model.setModifiername(user.getName());
        //修改时间
        model.setModifieddate(date);
        //是否启用
        model.setIsactive("Y");
    }


    public static void main(String[] args) {
        String json = "{\"serial_number\":2022112810006}";
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(json);
        String serialNumber = String.valueOf(jsonObject.get("serial_number"));
        System.err.println(serialNumber);
    }

    public ValueHolderV14 transferBySerial(String serial, User user) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(serial);
        String serialNumber = String.valueOf(jsonObject.get("serial_number"));
        log.info("流水号:" + serialNumber);
        List<String> ids = getIdBySerial(serialNumber);
        log.info("ids:" + JSONUtil.toJsonStr(ids));
        if (CollectionUtils.isEmpty(ids)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("该流水号无效");
            return valueHolderV14;
        }
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        int maxImportSize = config.getProperty("r3.oc.oms.import.order.max.qty", 5000);
        int pageSize = maxImportSize / 20;
        List<List<String>> baseModelPageList = Lists.partition(ids, pageSize);
        for (List<String> ocBOrderExtends : baseModelPageList) {
            preOrderUpdateThreadPoolExecutor.submit(new CallableBuildOcBOrder(ocBOrderExtends, user));
        }
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");
        return valueHolderV14;
    }

    // 根据流水号查询ES获取ID
    private List<String> getIdBySerial(String serial) {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("SERIAL_NUMBER", serial);
        JSONObject filterKey = new JSONObject();
        JSONArray orderKeys = new JSONArray();
        JSONObject orderByKey = new JSONObject();
        orderByKey.put("desc", true);
        orderByKey.put("name", "ID");
        orderKeys.add(orderByKey);
        Integer startIndex = 0;
        Integer range = 10000;
        List<String> ids = new ArrayList<>();
        boolean flag = true;
        while (flag) {
            List<String> another = getIds(whereKeys, filterKey, orderKeys, startIndex, range);
            if (CollectionUtils.isEmpty(another)) {
                flag = false;
                break;
            }
            ids.addAll(another);
            startIndex += range;
        }
        return ids;
    }

    private List<String> getIds(JSONObject whereKeys, JSONObject filterKey, JSONArray orderKeys, Integer startIndex, Integer range) {
        JSONObject esResult = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_PRE_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_PRE_ORDER_TYPE_NAME,
                whereKeys, filterKey, orderKeys, range, startIndex, new String[]{"ID"});
        JSONArray aryIds = esResult.getJSONArray("data");
        if (CollectionUtils.isEmpty(aryIds)) {
            return null;
        }
        List<String> ids = Lists.newArrayList();
        for (int i = 0; i < aryIds.size(); i++) {
            Map<String, String> map = (Map<String, String>) aryIds.get(i);
            ids.add(String.valueOf(map.get("ID")));
        }
        return ids;
    }


    class CallableBuildOcBOrder implements Callable<List<String>> {

        List<String> ids;
        User user;

        public CallableBuildOcBOrder(List<String> ids, User user) {
            this.ids = ids;
            this.user = user;
        }

        @Override
        public List<String> call() throws Exception {
            for (String id : ids) {
                extracted(user, Long.valueOf(id));
            }
            return ids;
        }
    }

}
