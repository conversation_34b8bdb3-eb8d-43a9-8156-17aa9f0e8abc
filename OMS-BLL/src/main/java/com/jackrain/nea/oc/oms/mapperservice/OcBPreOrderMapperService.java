package com.jackrain.nea.oc.oms.mapperservice;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.oc.oms.mapper.OcBPreOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBPreOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName OcBPreOrderMapperService
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/12 16:12
 * @Version 1.0
 */
@Service
@Slf4j
public class OcBPreOrderMapperService extends ServiceImpl<OcBPreOrderMapper, OcBPreOrder> {

    /**
     * 根据tid获取订单数据
     *
     * @param tid
     * @return
     */
    public OcBPreOrder getOrderByTid(String tid) {
        return baseMapper.selectOne(new QueryWrapper<OcBPreOrder>()
                .lambda().eq(OcBPreOrder::getTid, tid).eq(OcBPreOrder::getIsactive, "Y"));
    }

    /**
     * 根据平台订单列表获取订单数据
     *
     * @param tids
     * @return
     */
    public List<OcBPreOrder> getOrderByTids(List<String> tids) {
        return baseMapper.selectList(new QueryWrapper<OcBPreOrder>()
                .lambda().in(OcBPreOrder::getTid, tids).eq(OcBPreOrder::getIsactive, "Y"));
    }

    /**
     * 根据流水号获取订单数据
     *
     * @param tids
     * @return
     */
    public List<OcBPreOrder> getOrderBySerial(String serial) {
        return baseMapper.selectList(new QueryWrapper<OcBPreOrder>()
                .lambda().in(OcBPreOrder::getSerialNumber, serial).eq(OcBPreOrder::getIsactive, "Y"));
    }

    @Override
    public boolean updateById(OcBPreOrder ocBPreOrder) {
        Assert.notNull(ocBPreOrder.getTid(), "平台单号不能为空");
        Assert.notNull(ocBPreOrder.getId(), "id不能为空");
        Wrapper<OcBPreOrder> updateWrapper = new UpdateWrapper<OcBPreOrder>().lambda()
                .eq(OcBPreOrder::getTid, ocBPreOrder.getTid())
                .eq(OcBPreOrder::getId, ocBPreOrder.getId());
        return baseMapper.update(ocBPreOrder, updateWrapper) > 0;
    }

}
