package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.es.ES4ReturnAfSend;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description： 已发货退款单服务逻辑
 * Author: RESET
 * Date: Created in 2020/8/7 14:07
 * Modified By:
 */
@Component
@Slf4j
public class OmsReturnAfSendService {

    // ES查询返回的字段列表
    static final String FIELD_T_RETURN_ID = "T_RETURN_ID";
    static final String FIELD_MODIFIEDDATE = "MODIFIEDDATE";
    static final String[] ES_RETURN_FIELDS = new String[]{FIELD_T_RETURN_ID};

    @Autowired
    OcBReturnAfSendMapper afSendMapper;
    @Autowired
    OcBReturnAfSendItemMapper afSendItemMapper;

    /**
     * 更新成失败状态
     *
     * @param tReturnId
     * @return
     */
    public int updateToACStatusToFailedByTReturnId(String tReturnId) {
        return updateToACStatusByTReturnId(tReturnId, ToACStatusEnum.FAILED.getValue());
    }

    public int updateToACStatusToFailedByTReturnIds(List<String> tReturnIds) {
        return updateToACStatusByTReturnIds(tReturnIds, ToACStatusEnum.FAILED.getValue());
    }

    public int updateToACStatusToErrorByTReturnIds(List<String> tReturnIds) {
        return updateToACStatusByTReturnIds(tReturnIds, ToACStatusEnum.ERROR.getValue());
    }

    /**
     * 更新成完成状态
     *
     * @param tReturnId
     * @return
     */
    public int updateToACStatusToSuccessByTReturnId(String tReturnId) {
        return updateToACStatusByTReturnId(tReturnId, ToACStatusEnum.SUCCESS.getValue());
    }

    public int updateToACStatusToSuccessByTReturnIds(List<String> tReturnIds) {
        return updateToACStatusByTReturnIds(tReturnIds, ToACStatusEnum.SUCCESS.getValue());
    }

    /**
     * 更新成待传状态
     *
     * @param tReturnId
     * @return
     */
    public int updateToACStatusToPendingByTReturnId(String tReturnId) {
        return updateToACStatusByTReturnId(tReturnId, ToACStatusEnum.PENDING.getValue());
    }

    public int updateToACStatusToPendingByTReturnIds(List<String> tReturnIds) {
        return updateToACStatusByTReturnIds(tReturnIds, ToACStatusEnum.PENDING.getValue());
    }

    /**
     * 更新
     *
     * @param tReturnId
     * @param toACStatus
     * @return
     */
    public int updateToACStatusByTReturnId(String tReturnId, Integer toACStatus) {
        if (StringUtils.isNotEmpty(tReturnId) && Objects.nonNull(toACStatus)) {
            List<String> tReturnIds = new ArrayList<>();
            tReturnIds.add(tReturnId);
            return updateToACStatusByTReturnIds(tReturnIds, toACStatus);
        }

        return 0;
    }

    /**
     * 批量更新传AC状态
     *
     * @param tReturnIds
     * @param toACStatus
     * @return
     */
    public int updateToACStatusByTReturnIds(List<String> tReturnIds, Integer toACStatus) {
        if (CollectionUtils.isNotEmpty(tReturnIds) && Objects.nonNull(toACStatus)) {
            return afSendMapper.updateToACStatusByTReturnIds(toACStatus, tReturnIds);
        }

        return 0;
    }

    /**
     * 查单个
     *
     * @param afSend
     * @return
     */
    public List<OcBReturnAfSendItem> queryItemsToBeSendingACListByAfSend(OcBReturnAfSend afSend) {
        if (Objects.nonNull(afSend) && Objects.nonNull(afSend.getId())) {
            List<OcBReturnAfSend> afSends = new ArrayList<>();
            afSends.add(afSend);
            return queryItemsToBeSendingACListByAfSends(afSends);
        }

        return null;
    }

    /**
     * 按主表列表查询明细列表
     *
     * @param afSends
     * @return
     */
    public List<OcBReturnAfSendItem> queryItemsToBeSendingACListByAfSends(List<OcBReturnAfSend> afSends) {
        List<OcBReturnAfSendItem> afSendItems = null;

        if (CollectionUtils.isNotEmpty(afSends)) {
            List<Long> afSendIds = afSends.stream().map(OcBReturnAfSend::getId).collect(Collectors.toList());
            afSendItems = queryItemsToBeSendingACListByAfSendIds(afSendIds);
        }

        return afSendItems;
    }

    /**
     * 按主表ID列表查询列表
     *
     * @param afSendIds
     * @return
     */
    public List<OcBReturnAfSendItem> queryItemsToBeSendingACListByAfSendIds(List<Long> afSendIds) {
        List<OcBReturnAfSendItem> afSendItems = null;

        if (CollectionUtils.isNotEmpty(afSendIds)) {
            afSendItems = afSendItemMapper.selectByOcBReturnAfSendIdList(afSendIds);
        }

        return afSendItems;
    }


    /**
     * 查询待发送AC的已发货退款单数据主表
     *
     * @param limit
     * @return
     */
    public List<OcBReturnAfSend> queryToBeSendingACList(int limit) {
        List<OcBReturnAfSend> afSends = null;

        // 状态
        List<Integer> toAcStatus = new ArrayList<>();
        // 查询待传、失败的记录
        toAcStatus.add(ToACStatusEnum.PENDING.getValue());
        toAcStatus.add(ToACStatusEnum.FAILED.getValue());

        // 起始页 0
        int pageIndex = 0;
        afSends = queryByAcSendStatus(toAcStatus, pageIndex, limit);

        return afSends;
    }

    /**
     * 按发送AC状态查询列表
     *
     * @param toAcStatus
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public List<OcBReturnAfSend> queryByAcSendStatus(List<Integer> toAcStatus, int pageIndex, int pageSize) {
        List<OcBReturnAfSend> afSends = null;
        // 从ES查分库键列表
        List<String> tReturnIds = ES4ReturnAfSend.findTReturnIdByToAcStatus(toAcStatus, pageIndex, pageSize);

        if (log.isDebugEnabled()) {
            log.debug("queryByAcSendStatus.result:{}", tReturnIds);
        }

        // 查询到ID后从数据库查询具体数据
        if (CollectionUtils.isNotEmpty(tReturnIds)) {
            afSends = queryByTReturnIdSFromDB(tReturnIds);
        }

        return afSends;
    }

    /**
     * 按t_return_id列表查询对象列表
     *
     * @param tReturnIds
     * @return
     */
    public List<OcBReturnAfSend> queryByTReturnIdSFromDB(List<String> tReturnIds) {
        List<OcBReturnAfSend> afSends = null;

        if (CollectionUtils.isNotEmpty(tReturnIds)) {
            afSends = afSendMapper.selectOcBReturnAfSendListByTReturnId(tReturnIds);
        }

        return afSends;
    }

    /**
     * 获取实例
     *
     * @return
     */
    public static OmsReturnAfSendService getInstance() {
        return ApplicationContextHandle.getBean(OmsReturnAfSendService.class);
    }

}
