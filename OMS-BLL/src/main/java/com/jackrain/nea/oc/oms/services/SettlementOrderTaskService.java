package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description： 传结算/财务任务服务
 * Author: RESET
 * Date: Created in 2020/8/8 19:57
 * Modified By:
 */
@Component
@Slf4j
public class SettlementOrderTaskService {

    // 主表
    static final String TBL_OC_B_RETURN_AF_SEND = "OC_B_RETURN_AF_SEND";
    // 明细表
    static final String TBL_OC_B_RETURN_AF_SEND_ITEM = "OC_B_RETURN_AF_SEND_ITEM";

    /**
     * 发送：已发货退款单 至AC -- 支付宝对账
     *
     * @param limit
     */
    public void sendReturnOrderAfSendToAc(Integer limit) {
        OmsReturnAfSendService afSendService = OmsReturnAfSendService.getInstance();
        List<OcBReturnAfSend> afSends = null;
        List<String> lostTReturnIds = new ArrayList<>();

        try {
            if (Objects.isNull(limit) || limit <= 0) {
                limit = 1000;
            }
            // 从ES查找可发送列表
            afSends = afSendService.queryToBeSendingACList(limit);
            if (CollectionUtils.isNotEmpty(afSends)) {
                // 查明细
                List<OcBReturnAfSendItem> afSendItems = afSendService.queryItemsToBeSendingACListByAfSends(afSends);

                if (CollectionUtils.isNotEmpty(afSendItems)) {
                    // 按主表ID分组
                    Map<Long, List<OcBReturnAfSendItem>> itemsMap = afSendItems.stream().collect(Collectors.groupingBy(OcBReturnAfSendItem::getOcBReturnAfSendId));

                    afSends.forEach(afSend -> {
                        List<OcBReturnAfSendItem> items = itemsMap.get(afSend.getId());
                        String tReturnId = afSend.getTReturnId();

//                        if (CollectionUtils.isNotEmpty(items)) {
//                            AcPushAcBillRequest request = buildAcPushAcBillRequestByAfSendAndItems(afSend, items);
//
//                            if (Objects.nonNull(request)) {
//                                requests.add(request);
//                            } else {
//                                if (log.isDebugEnabled()) {
//                                    log.debug("sendReturnOrderAfSendToAc.nullRequest.id={}", afSend.getId());
//                                }
//                                if (StringUtils.isNotEmpty(tReturnId)) {
//                                    lostTReturnIds.add(tReturnId);
//                                }
//                            }
//                        } else {
//                            if (log.isDebugEnabled()) {
//                                log.debug("sendReturnOrderAfSendToAc.emptyItems.id={}", afSend.getId());
//                            }
//                            if (StringUtils.isNotEmpty(tReturnId)) {
//                                lostTReturnIds.add(tReturnId);
//                            }
//                        }
                    });
                }

//                if (log.isDebugEnabled()) {
//                    log.debug("sendReturnOrderAfSendToAc.requests:{}", CollectionUtils.isEmpty(requests) ? "empty.requests" : requests.size());
//                }

                // 丢失的更新
                if (CollectionUtils.isNotEmpty(lostTReturnIds)) {
                    afSendService.updateToACStatusToErrorByTReturnIds(lostTReturnIds);
                }

                // 发送
//                if (CollectionUtils.isNotEmpty(requests)) {
//                    ValueHolderV14 valueHolderV14 = AcScOrigBillUtil.batchPushAcBill(requests);
//                    if (log.isDebugEnabled()) {
//                        log.debug("sendReturnOrderAfSendToAc.SendResult:{}", JSON.toJSONString(valueHolderV14));
//                    }
//
//                    if (Objects.nonNull(valueHolderV14) && valueHolderV14.getCode() == ResultCode.SUCCESS) {
//                        // 发送成功：更新状态 过滤更新失败的
//                        List<String> tReturnIds = afSends.stream().filter(f -> StringUtils.isNotBlank(f.getTReturnId()) &&
//                                !lostTReturnIds.contains(f.getTReturnId()))
//                                .map(OcBReturnAfSend::getTReturnId).collect(Collectors.toList());
//                        afSendService.updateToACStatusToSuccessByTReturnIds(tReturnIds);
//                    } else {
//                        // 发送失败：方法只会抛异常，不会返回失败状态，所以不会走到这个逻辑
//                    }
//                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("sendReturnOrderAfSendToAc.已发货退款单发送AC出错,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            // 更新状态
            if (CollectionUtils.isNotEmpty(afSends)) {
                List<String> tReturnIds = afSends.stream().map(OcBReturnAfSend::getTReturnId).collect(Collectors.toList());
                List<String> tReturnIdsNotNull = tReturnIds.stream().filter(f -> Objects.nonNull(f)).collect(Collectors.toList());
                afSendService.updateToACStatusToFailedByTReturnIds(tReturnIdsNotNull);
            }
        }
    }

    /**
     * 组装AcPushAcBillRequest
     *
     * @param afSend
     * @param afSendItems
     * @return
     */
//    private AcPushAcBillRequest buildAcPushAcBillRequestByAfSendAndItems(OcBReturnAfSend afSend, List<OcBReturnAfSendItem> afSendItems) {
//        if (Objects.nonNull(afSend) && CollectionUtils.isNotEmpty(afSendItems)) {
//            AcPushAcBillRequest request = new AcPushAcBillRequest();
//
//            // 组装3个元素：主表、明细表、单据类型
//            JSONObject bill = new JSONObject();
//            bill.put(TBL_OC_B_RETURN_AF_SEND, afSend);
//            bill.put(TBL_OC_B_RETURN_AF_SEND_ITEM, afSendItems);
//
//            request.setBill(bill);
//            request.setBillType(SourceBillType.OMS_ORDER_RETURN_AF_SEND);
//            request.setUser(SystemUserResource.getRootUser());
//            request.setSourceBillNo(afSend.getBillNo());
//
//            return request;
//        }
//
//        return null;
//    }


    /**
     * 发送：已发货退款单 至AC -- 支付宝对账
     *
     * @param afSends
     */
//    public void syncAf2Settlement(List<OcBReturnAfSend> afSends) {
//        OmsReturnAfSendService afSendService = OmsReturnAfSendService.getInstance();
//        List<String> lostTReturnIds = new ArrayList<>();
//        if (log.isDebugEnabled()) {
//            log.debug("syncAf2Settlement.limit:{}", afSends.size());
//        }
//        try {
//            List<AcPushAcBillRequest> requests = new ArrayList<>();
//            // 查明细
//            List<OcBReturnAfSendItem> afSendItems = afSendService.queryItemsToBeSendingACListByAfSends(afSends);
//
//            if (CollectionUtils.isNotEmpty(afSendItems)) {
//                // 按主表ID分组
//                Map<Long, List<OcBReturnAfSendItem>> itemsMap = afSendItems.stream().collect(Collectors.groupingBy(OcBReturnAfSendItem::getOcBReturnAfSendId));
//
//                afSends.forEach(afSend -> {
//                    List<OcBReturnAfSendItem> items = itemsMap.get(afSend.getId());
//                    String tReturnId = afSend.getTReturnId();
//
//                    if (CollectionUtils.isNotEmpty(items)) {
//                        AcPushAcBillRequest request = buildAcPushAcBillRequestByAfSendAndItems(afSend, items);
//
//                        if (Objects.nonNull(request)) {
//                            requests.add(request);
//                        } else {
//                            if (log.isDebugEnabled()) {
//                                log.debug("syncAf2Settlement.nullRequest.id={}", afSend.getId());
//                            }
//                            if (StringUtils.isNotEmpty(tReturnId)) {
//                                lostTReturnIds.add(tReturnId);
//                            }
//                        }
//                    } else {
//                        if (log.isDebugEnabled()) {
//                            log.debug("syncAf2Settlement.emptyItems.id={}", afSend.getId());
//                        }
//                        if (StringUtils.isNotEmpty(tReturnId)) {
//                            lostTReturnIds.add(tReturnId);
//                        }
//                    }
//                });
//            } else {
//                List<String> tReturnIds = afSends.stream().map(OcBReturnAfSend::getTReturnId).collect(Collectors.toList());
//                log.error(" all Item is empty tReturnIds:{}", tReturnIds.size());
//                afSendService.updateToACStatusToErrorByTReturnIds(tReturnIds);
//                return;
//            }
//
//            if (log.isDebugEnabled()) {
//                log.debug("syncAf2Settlement.requests:{}", CollectionUtils.isEmpty(requests) ? "empty.requests" : requests.size());
//            }
//
//            // 丢失的更新
//            if (CollectionUtils.isNotEmpty(lostTReturnIds)) {
//                afSendService.updateToACStatusToErrorByTReturnIds(lostTReturnIds);
//            }
//
//            // 发送
//            if (CollectionUtils.isNotEmpty(requests)) {
//                ValueHolderV14 valueHolderV14 = AcScOrigBillUtil.batchPushAcBill(requests);
//                if (log.isDebugEnabled()) {
//                    log.debug("syncAf2Settlement.SendResult:{}", JSON.toJSONString(valueHolderV14));
//                }
//
//                if (Objects.nonNull(valueHolderV14) && valueHolderV14.getCode() == ResultCode.SUCCESS) {
//                    // 发送成功：更新状态 过滤更新失败的
//                    List<String> tReturnIds = afSends.stream().filter(f -> StringUtils.isNotBlank(f.getTReturnId()) &&
//                            !lostTReturnIds.contains(f.getTReturnId()))
//                            .map(OcBReturnAfSend::getTReturnId).collect(Collectors.toList());
//                    afSendService.updateToACStatusToSuccessByTReturnIds(tReturnIds);
//                } else {
//                    // 发送失败：方法只会抛异常，不会返回失败状态，所以不会走到这个逻辑
//                }
//            }
//        } catch (Exception e) {
//            log.error("syncAf2Settlement.已发货退款单发送AC出错:", e);
//            // 更新状态
//            if (CollectionUtils.isNotEmpty(afSends)) {
//                List<String> tReturnIds = afSends.stream().map(OcBReturnAfSend::getTReturnId).collect(Collectors.toList());
//                List<String> tReturnIdsNotNull = tReturnIds.stream().filter(f -> Objects.nonNull(f)).collect(Collectors.toList());
//                afSendService.updateToACStatusToFailedByTReturnIds(tReturnIdsNotNull);
//            }
//        }
//    }

    /**
     * 获取实例
     *
     * @return
     */
    public static SettlementOrderTaskService getInstance() {
        return ApplicationContextHandle.getBean(SettlementOrderTaskService.class);
    }

}
