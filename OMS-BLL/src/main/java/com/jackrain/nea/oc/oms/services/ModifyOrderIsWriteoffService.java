package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * @author: 李杰
 * @since: 2019/4/19
 * create at : 2019/4/19 16:27
 */
@Component
@Slf4j
public class ModifyOrderIsWriteoffService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    public ValueHolderV14 updateIsWriteoff(List<Long> ids) {
        ValueHolderV14 vh = new ValueHolderV14();
        for (Long id : ids) {
            OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
            if (ocBOrder == null) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("订单不存在！");
            } else {
                ocBOrder.setId(id);
                // ocBOrder.setIsWriteoff(1);
                ocBOrderMapper.updateById(ocBOrder);
            }
//            try {
////                Boolean flag = SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
////                        OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
//                        ocBOrderMapper.selectById(id), id);
//                if (!flag) {
//                    throw new NDSException("推送ES失败");
//                }
//            } catch (Exception e) {
//                throw new NDSException("推送ES失败");
//            }
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("更新is_writeoff字段成功");
        return vh;
    }
}
