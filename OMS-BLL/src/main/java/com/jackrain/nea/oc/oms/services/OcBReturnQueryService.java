package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 汪聿森
 * @Date: Created in 2019-09-17 14:22
 * @Description : 退换货订单查询
 */
@Component
@Slf4j
public class OcBReturnQueryService {
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    public OcBReturnOrder queryById(Long id) {
        return ocBReturnOrderMapper.selectByid(id);
    }
}
