package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.AdColumnMapper;
import com.jackrain.nea.oc.oms.mapper.AdTableMapper;
import com.jackrain.nea.oc.oms.model.result.QueryOrderTableHeaderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @author: 夏继超
 * @since: 2019/6/19
 * create at : 2019/6/19 16:45
 */
@Slf4j
@Component
public class DynamicListService {
    @Autowired
    UserQueryListService userQueryListService;
    @Autowired
    AdColumnMapper adColumnMapper;
    @Autowired
    AdTableMapper adTableMapper;

    /**
     * 动态查询头表和查询条件
     *
     * @param param
     * @param user
     * @return
     */
    public ValueHolderV14 dynamicList(String param, User user) {
        ValueHolder tableQuery = userQueryListService.getTableQuery(param, user);
        ValueHolderV14 vh = new ValueHolderV14();
        dealPermission(tableQuery);
        try {

            HashMap data = tableQuery.getData();
            HashMap hashMap = new HashMap();
            hashMap.put("columns", data.get("colum"));
            data.remove("colum");
            hashMap.put("search", tableQuery.getData());
            vh.setData(hashMap);
            vh.setCode(ResultCode.SUCCESS);
            return vh;
        } catch (Exception e) {
            vh.setCode(ResultCode.FAIL);
            vh.setData(null);
            vh.setMessage(e.getMessage());
            return vh;
        }
    }

    /**
     * 表头查询
     *
     * @param param param
     * @return list
     */
    public List<QueryOrderTableHeaderResult> queryTabHeaderList(String param, User user) {

        try {
            ValueHolder vh = userQueryListService.getTableQuery(param, user);
            if (vh == null || !vh.isOK()) {
                throw new NDSException("未查询到列表标签");
            }
            HashMap data = vh.getData();
            List<JSONObject> columnList = (List<JSONObject>) data.get("colum");
            List<QueryOrderTableHeaderResult> tabHeaderList = new ArrayList<>();
            for (int i = 0, l = columnList.size(); i < l; i++) {
                JSONObject jsonObject = columnList.get(i);
                QueryOrderTableHeaderResult qtr = new QueryOrderTableHeaderResult();
                qtr.setSort(i);
                qtr.setKey(jsonObject.getString("key"));
                qtr.setTitle(jsonObject.getString("title"));
                tabHeaderList.add(qtr);
            }
            return tabHeaderList;
        } catch (NDSException e) {
            log.error(LogUtil.format("表头查询: {}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("列表标签数据获取异常");
        }

    }

    private void dealPermission(ValueHolder tableQuery) {
        HashMap data = tableQuery.getData();
        dataLabel:
        if (data != null) {
            JSONObject jo = new JSONObject(data);
            JSONArray date = jo.getJSONArray("date");
            if (date == null) {
                break dataLabel;
            }
            for (int i = 0; i < date.size(); i++) {
                JSONObject jsonObject = date.getJSONObject(i);
                if (jsonObject == null) {
                    continue;
                }
                JSONObject tabth = jsonObject.getJSONObject("tabth");
                if (tabth == null) {
                    continue;
                }
                String tmpName = tabth.getString("colname");
                if ("CP_C_SHOP_ID".equals(tmpName)) {
                    JSONArray ary = new JSONArray();
                    JSONObject shop = new JSONObject();
                    shop.put("premtype", "CP_C_SHOP_PERMISSION_ID");
                    shop.put("refcol", "ID");
                    shop.put("iswrite", "false");
                    ary.add(shop);
                    tabth.put("precolnameslist", ary);

                }

            }

        }
    }
}
