package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.service.IpOrderCancelToAgService;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnBfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnTypeMapper;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.CardAutoVoidEnum;
import com.jackrain.nea.oc.oms.model.enums.GiftTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.InterceptStatus;
import com.jackrain.nea.oc.oms.model.enums.OcBReturnAfSendListEnums;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendInStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendPaymentEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnProTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendItemExtend;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.OcBReturnType;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.RefundOrderSourceTypeEnum;
import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
import com.jackrain.nea.oc.oms.sap.SapTaskTableEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.util.ImportUtil;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.st.service.OmsOrderStCAutocheckService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 发货后退款单服务
 *
 * @author: 夏继超
 * @since: 2020/3/10
 * create at : 2020/3/10 19:28
 */
@Slf4j
@Component
public class RefundFormAfterDeliveryService {
    @Autowired
    OcBReturnAfSendMapper afSendMapper;
    @Autowired
    OcBReturnBfSendMapper bfSendMapper;
    @Autowired
    OcBReturnAfSendItemMapper afSendItemMapper;
    @Autowired
    OmsOrderStCAutocheckService stCAutocheckService;
    @Autowired
    IpBTaobaoRefundMapper refundMapper;

    @Autowired
    OcCancelChangingOrRefundService refundService;
    @Autowired
    OcBRefundOrderToAGService toAGService;
    @Autowired
    OcBReturnOrderMapper returnOrderMapper;
    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    OcBReturnOrderRefundMapper returnOrderRefundMapper;
    @Autowired
    IpOrderCancelToAgService cancelToAgService;
    @Autowired
    OcBReturnAfSendLogMapper afSendLogMapper;
    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private OmsRefundOrderService omsRefundOrderService;

    @Autowired
    private Oms2SapMapper oms2SapMapper;
    @Autowired
    private ExportUtil exportUtil;
    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    @Value("${return.type.system.default.code:xt001}")
    private String defaultReturnTypeCode;
    @Autowired
    private ImportUtil importUtil;

    @Autowired
    private OcBReturnTypeMapper ocbReturnTypeMapper;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    /**
     * 作废发货后退款单
     *
     * @param ids  退款单id 集合
     * @param user 当前用户0
     * @return
     */
    public ValueHolder voidTheFefundAfterDelivery(JSONArray ids, User user) {
        ValueHolder vh = new ValueHolder();
        RefundFormAfterDeliveryService bean = ApplicationContextHandle.getBean(RefundFormAfterDeliveryService.class);
        JSONArray errorMessage = new JSONArray(); // 错误信息
        Integer fail = 0;
        for (int i = 0; i < ids.size(); i++) {
            OcBReturnAfSend ocBReturnAfSend = afSendMapper.selectById((Long) ids.get(i));
            JSONObject jsonObject = new JSONObject();
            try {
                vh = bean.voidMainStep(ocBReturnAfSend, user);
                if (!vh.isOK()) {
                    jsonObject.put("code", ResultCode.FAIL);
                    jsonObject.put("message", Resources.getMessage(vh.get("message").toString()));
                    jsonObject.put("objid", ids.get(i));
                    fail++;
                    errorMessage.add(jsonObject);
                    insertReturnAfSendLog("额外退款单取消", "额外退款单取消失败" + vh.get("message").toString(), null, user,
                            ocBReturnAfSend.getId());
                } else {
                    insertReturnAfSendLog("额外退款单取消", "额外退款单取消成功", null, user, ocBReturnAfSend.getId());
                }
            } catch (NDSException e) {
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", Resources.getMessage(e.getMessage()));
                jsonObject.put("objid", ids.get(i));
                fail++;
                errorMessage.add(jsonObject);
                insertReturnAfSendLog("额外退款单取消", "额外退款单取消失败" + e.getMessage(), null, user, ocBReturnAfSend.getId());
            } catch (Exception ex) {
                log.error(LogUtil.format("取消额外退款单异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", Resources.getMessage(ex.getMessage()));
                jsonObject.put("objid", ids.get(i));
                fail++;
                errorMessage.add(jsonObject);
            }
        }
        vh.put("data", errorMessage);
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "取消成功" + ids.size() + "数据，取消失败0条数据");
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "取消成功" + (ids.size() - fail) + "数据，取消失败" + fail + "条数据");
        }
        return vh;
    }

    /**
     * 作废主流程
     *
     * @param ocBReturnAfSend
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder voidMainStep(OcBReturnAfSend ocBReturnAfSend, User user) {
        ValueHolder vh = new ValueHolder();
        if (ReturnAfSendReturnBillTypeEnum.CANCEL.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
            throw new NDSException("单据已取消，不允许重复取消！");

        }
        if (ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
            throw new NDSException("当前记录退款完成，不允许取消！");
        }

        if (ReturnAfSendReturnBillTypeEnum.REFUNDING.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
            throw new NDSException("当前记录退款中，不允许取消！");
        }
        OcBReturnAfSend send = new OcBReturnAfSend();
        send.setReturnStatus(ReturnAfSendReturnBillTypeEnum.CANCEL.getVal());
        send.setId(ocBReturnAfSend.getId());
        send.setModifierid(user.getId() + 0L);
        send.setModifierename(user.getEname());
        send.setModifiername(user.getName());
        send.setModifieddate(new Date());
        send.setIsactive("N");
        int i = afSendMapper.updateById(send);
        // 当订单对应的所有手工新建的额外退款单都取消后，将订单的‘额’标去掉
        if (RefundOrderSourceTypeEnum.MANUAL.getValue().equals(ocBReturnAfSend.getRefundOrderSourceType())) {
            OcBReturnAfSend ocBReturnAfSendTemp = afSendMapper.selectById(ocBReturnAfSend.getId());
            QueryWrapper<OcBReturnAfSend> wrapper = new QueryWrapper<>();
            wrapper.eq("source_bill_no", ocBReturnAfSendTemp.getSourceBillNo());
            wrapper.ne("return_status", ReturnAfSendReturnBillTypeEnum.CANCEL.getVal());
            wrapper.eq("refund_order_source_type", 1);
            wrapper.eq("isactive", "Y");
            int count = afSendMapper.selectCount(wrapper);
            if (count == 0) {
                // 更新订单
                OcBOrder updateOrder = new OcBOrder();
                if (ocBReturnAfSend.getSourceBillNo() != null) {
                    updateOrder.setId(Long.parseLong(ocBReturnAfSend.getSourceBillNo()));
                    updateOrder.setIsExtra(0);
                    ocBOrderMapper.updateById(updateOrder);
                }
            }
        }
        List<OcBReturnAfSendItem> ocBReturnAfSendItems =
                afSendItemMapper.selectList(new QueryWrapper<OcBReturnAfSendItem>().eq("oc_b_return_af_send_id",
                        ocBReturnAfSend.getId()));
        for (OcBReturnAfSendItem ocBReturnAfSendItem : ocBReturnAfSendItems) {
            OcBOrderItem ocBOrderItem1 = ocBOrderItemMapper.selectOne(new QueryWrapper<OcBOrderItem>().eq(
                    "oc_b_order_id", ocBReturnAfSendItem.getRelationBillId()).
                    eq("id", ocBReturnAfSendItem.getRelationBillItemId()));
            OcBOrderItem ocBOrderItem = new OcBOrderItem();
            ocBOrderItem.setModifieddate(new Date());
            ocBOrderItem.setModifierename(user.getEname());
            ocBOrderItem.setModifierid(user.getId() + 0L);
            BigDecimal amtRefund = ocBOrderItem1.getAmtRefund().subtract(ocBReturnAfSendItem.getAmtReturn());
            if (amtRefund.compareTo(BigDecimal.ZERO) < 0) {
                ocBOrderItem.setAmtRefund(BigDecimal.ZERO);
            } else {
                ocBOrderItem.setAmtRefund(amtRefund);
            }
            int update = ocBOrderItemMapper.update(ocBOrderItem,
                    new QueryWrapper<OcBOrderItem>().eq("oc_b_order_id", ocBReturnAfSendItem.getRelationBillId()).
                            eq("id", ocBReturnAfSendItem.getRelationBillItemId()));
        }

        if (i > 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "取消成功");
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "取消失败");
        }
        return vh;
    }

    /**
     * 发货后退款单审核
     *
     * @param ids  id集合
     * @param user 当前用户
     * @return
     */
    public ValueHolder examineTheFefundAfterDelivery(JSONArray ids, User user) {
        ValueHolder vh = new ValueHolder();
        RefundFormAfterDeliveryService bean = ApplicationContextHandle.getBean(RefundFormAfterDeliveryService.class);
        JSONArray errorMessage = new JSONArray(); // 错误信息
        Integer fail = 0;
        for (int i = 0; i < ids.size(); i++) {
            JSONObject jsonObject = new JSONObject();
            Long ocBReturnAfSendId = ids.getLong(i);
            OcBReturnAfSend ocBReturnAfSend = afSendMapper.selectById(ocBReturnAfSendId);
            try {
                if (ocBReturnAfSend == null) {
                    throw new NDSException("当前记录已不存在！");
                }
                if (ReturnAfSendReturnBillTypeEnum.REFUNDING.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
                    throw new NDSException("当前记录退款中，不允许重复审核！");
                }
                if (ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
                    throw new NDSException("当前记录已退款完成，不允许审核！");
                }
                if (ReturnAfSendReturnBillTypeEnum.CANCEL.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
                    throw new NDSException("当前记录已取消，不允许审核！");
                }
                vh = bean.examineMainStep(ocBReturnAfSend, user, false);

                boolean ok = vh.isOK();
                if (!ok) {
                    jsonObject.put("code", ResultCode.FAIL);
                    jsonObject.put("message", vh.get("message"));
                    jsonObject.put("objid", ids.get(i));
                    errorMessage.add(jsonObject);
                    insertReturnAfSendLog("额外退款单审核", "额外退款单审核失败" + vh.get("message"), null, user,
                            ocBReturnAfSend.getId());
                    fail++;
                } else {
                    insertReturnAfSendLog("额外退款单审核", "额外退款单审核成功", null, user, ocBReturnAfSend.getId());
                }
            } catch (NDSException e) {
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", Resources.getMessage(e.getMessage()));
                jsonObject.put("objid", ids.get(i));
                errorMessage.add(jsonObject);
                insertReturnAfSendLog("额外退款单审核", "额外退款单审核失败" + e.getMessage(), null, user,
                        ocBReturnAfSendId);
                fail++;
            } catch (Exception e) {
                log.error(LogUtil.format("额外退款单审核异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", Resources.getMessage(e.getMessage()));
                jsonObject.put("objid", ids.get(i));
                errorMessage.add(jsonObject);
                fail++;
            }
        }
        vh.put("data", errorMessage);
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "审核成功" + ids.size() + "数据，审核失败0条数据");
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "审核成功" + (ids.size() - fail) + "数据，审核失败" + fail + "条数据");
        }
        return vh;
    }

    /**
     * 发货后退款单 反审核
     *
     * @param ids  id集合
     * @param user 当前用户
     * @return
     */
    public ValueHolder unExamineTheFefundAfterDelivery(JSONArray ids, User user) {
        ValueHolder vh = new ValueHolder();
        Integer fail = 0;
        JSONArray errorMessage = new JSONArray(); // 错误信息
        for (int i = 0; i < ids.size(); i++) {
            JSONObject jsonObject = new JSONObject();
            Long ocBReturnAfSendId = ids.getLong(i);
            OcBReturnAfSend ocBReturnAfSend = afSendMapper.selectById(ocBReturnAfSendId);
            try {
                unExamineTheFefundAfterDelivery(user, ocBReturnAfSend);
                insertReturnAfSendLog("额外退款单反审核", "额外退款单反审核成功", null, user, ocBReturnAfSend.getId());
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "反审核成功!");
            } catch (NDSException e) {
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", e.getMessage());
                jsonObject.put("objid", ids.get(i));
                errorMessage.add(jsonObject);
                log.error(LogUtil.format("额外退款单反审核异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                vh.put("code", ResultCode.FAIL);
                vh.put("message", e.getMessage());
                fail++;
                insertReturnAfSendLog("额外退款单反审核", "额外退款单反审核失败，" + e.getMessage(), null, user, ocBReturnAfSend.getId());
            } catch (Exception ex) {
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", ex.getMessage());
                jsonObject.put("objid", ids.get(i));
                errorMessage.add(jsonObject);
                log.error(LogUtil.format("额外退款单反审核异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
                vh.put("code", ResultCode.FAIL);
                vh.put("message", ex.getMessage());
                fail++;
            }
        }
        vh.put("data", errorMessage);
        if (ids.size() == 1) {
            return vh;
        }
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "反审核成功" + ids.size() + "数据，反审核失败0条数据");
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "反审核成功" + (ids.size() - fail) + "数据，反审核失败" + fail + "条数据");
        }
        return vh;
    }

    /**
     * 额外退款，拒绝打款
     *
     * @param ids  id集合
     * @param user 当前用户
     * @return
     */
    public ValueHolder refuseToPayOcBReturnAfSend(JSONArray ids, String reason, User user) {
        ValueHolder vh = new ValueHolder();
        Integer fail = 0;
        JSONArray errorMessage = new JSONArray(); // 错误信息
        for (int i = 0; i < ids.size(); i++) {
            JSONObject jsonObject = new JSONObject();
            Long ocBReturnAfSendId = ids.getLong(i);
            OcBReturnAfSend ocBReturnAfSend = afSendMapper.selectById(ocBReturnAfSendId);
            try {
                if (ocBReturnAfSend == null) {
                    throw new NDSException("当前记录已不存在！");
                }
                // 单据状态是退款中的订单才允许拒绝打款
                if (!ReturnAfSendReturnBillTypeEnum.REFUNDING.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
                    throw new NDSException("只支持【退款中】的订单拒绝打款");
                }
                Integer paymentStatus = ocBReturnAfSend.getPaymentStatus();
                // 打款状态判断，只支持打款状态为【未打款】、【打款中】的订单拒绝打款
                if (!ReturnAfSendPaymentEnum.PAYMENTING.toInteger().equals(paymentStatus)
                        && !ReturnAfSendPaymentEnum.NO_PAYMENT.toInteger().equals(paymentStatus)) {
                    throw new NDSException("只支持打款状态为【未打款】、【打款中】的订单拒绝打款");
                }

                insertReturnAfSendLog("拒绝打款", "拒绝打款成功", null, user,
                        ocBReturnAfSend.getId());
                OcBReturnAfSend updateReturnAfSend = new OcBReturnAfSend();
                updateReturnAfSend.setId(ids.getLong(i));
                updateReturnAfSend.setModifierid(Long.valueOf(user.getId()));
                updateReturnAfSend.setModifiername(user.getName());
                updateReturnAfSend.setModifierename(user.getEname());
                updateReturnAfSend.setModifieddate(new Date());
                updateReturnAfSend.setPaymentStatus(ReturnAfSendPaymentEnum.PAYMENT_FAIL.getVal());
                updateReturnAfSend.setPaymentFailReason(reason);
                updateReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal());
                afSendMapper.updateById(updateReturnAfSend);
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "拒绝打款成功!");
            } catch (NDSException ex) {
                if (ocBReturnAfSend != null) {
                    insertReturnAfSendLog("拒绝打款", "拒绝打款失败" + ex.getMessage(), null, user,
                            ocBReturnAfSend.getId());
                }
                jsonObject.put("code", -1);
                jsonObject.put("message", ex.getMessage());
                jsonObject.put("objid", ids.get(i));
                errorMessage.add(jsonObject);
                vh.put("code", ResultCode.FAIL);
                vh.put("message", ex.getMessage());
                fail++;
            } catch (Exception e) {
                jsonObject.put("code", -1);
                jsonObject.put("message", e.getMessage());
                jsonObject.put("objid", ids.get(i));
                errorMessage.add(jsonObject);
                log.error(LogUtil.format("拒绝打款异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                vh.put("code", ResultCode.FAIL);
                vh.put("message", e.getMessage());
                fail++;
            }
        }
        vh.put("data", errorMessage);
        if (ids.size() == 1) {
            return vh;
        }
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "拒绝打款成功");
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "拒绝打款成功" + (ids.size() - fail) + "数据，拒绝打款失败" + fail + "条数据");
        }
        return vh;
    }

    /**
     * 反审核
     */
    private void unExamineTheFefundAfterDelivery(User user, OcBReturnAfSend ocBReturnAfSend) {
        upExamineCheckStatus(ocBReturnAfSend);
        afSendMapper.unExamineTheFefundAfterDelivery(ReturnAfSendPaymentEnum.NO_PAYMENT.toInteger(), ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal(),
                ocBReturnAfSend.getId());
    }

    /**
     * 批量更新打款状态
     *
     * @param list
     * @param user
     * @return
     */
    public ValueHolderV14 batchUpdateOcBReturnAfSend(List<OcBReturnAfSend> list, User user) {
        ValueHolderV14 vh14 = new ValueHolderV14();
        log.info(" 额外退款单打款结果导入 {}",JSON.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            vh14.setCode(ResultCode.FAIL);
            vh14.setMessage("数据为空！");
            return vh14;
        }
        Integer fail = 0;
        JSONArray errorMessage = new JSONArray(); // 错误信息
        for (OcBReturnAfSend temp : list) {
            OcBReturnAfSend ocBReturnAfSend = afSendMapper.selectOne(new QueryWrapper<OcBReturnAfSend>().eq("bill_no"
                    , temp.getBillNo()));
            try {
                if (ocBReturnAfSend == null) {
                    throw new NDSException("单据编号:" + temp.getBillNo() + "不存在");
                }
                if (temp.getPaymentStatus() == null) {
                    throw new NDSException("单据编号:" + temp.getBillNo() + "处理结果文案不正确，（处理成功，校验失败）");
                }
                if (!ReturnAfSendReturnBillTypeEnum.REFUNDING.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
                    throw new NDSException("单据编号:" + temp.getBillNo() + "当前记录非退款中状态,导入打款结果失败");
                }
                if (ReturnAfSendPaymentEnum.PAYMENT_SUCCESS.toInteger().equals(ocBReturnAfSend.getPaymentStatus())) {
                    throw new NDSException("单据编号:" + temp.getBillNo() + ",当前记录打款成功，不允许再处理");
                }
                if (ReturnAfSendPaymentEnum.NO_PAYMENT.toInteger().equals(ocBReturnAfSend.getPaymentStatus())) {
                    throw new NDSException("单据编号:" + temp.getBillNo() + ",当前记录未打款，不允许处理");
                }
                // 打款失败进行反审核
                if (ReturnAfSendPaymentEnum.PAYMENT_FAIL.toInteger().equals(temp.getPaymentStatus())) {
                    insertReturnAfSendLog("额外退款单，导入打款结果成功", "导入打款结果,打款失败", null, user,
                            ocBReturnAfSend.getId());
                    temp.setId(ocBReturnAfSend.getId());
                    temp.setReturnStatus(ReturnAfSendReturnBillTypeEnum.NOREFUND.toInteger());
                    afSendMapper.updateById(temp);
                    insertReturnAfSendLog("额外退款单反审核", "额外退款单反审核成功", null, user, ocBReturnAfSend.getId());

                } else {
                    temp.setId(ocBReturnAfSend.getId());
                    afSendMapper.updateById(temp);
                    insertReturnAfSendLog("额外退款单，导入打款结果成功", "导入打款结果,打款成功", null, user,
                            ocBReturnAfSend.getId());
                }
            } catch (NDSException e) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("message", e.getMessage());
                jsonObject.put("bill_no", temp.getBillNo());
                jsonObject.put("id", ocBReturnAfSend == null ? "" : ocBReturnAfSend.getId());
                errorMessage.add(jsonObject);
                fail++;
                if (ocBReturnAfSend != null) {
                    insertReturnAfSendLog("额外退款单，导入打款结果失败", "导入打款结果失败，" + e.getMessage(), null, user,
                            ocBReturnAfSend.getId());
                }
            } catch (Exception e) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("message", e.getMessage());
                jsonObject.put("bill_no", temp.getBillNo());
                jsonObject.put("id", ocBReturnAfSend == null ? "" : ocBReturnAfSend.getId());
                errorMessage.add(jsonObject);
                fail++;
                log.error(LogUtil.format("额外退款单，导入打款结果失败，参数{},异常信息：{}"), JSONObject.toJSONString(temp),Throwables.getStackTraceAsString(e));

            }
        }
        if (fail == 0) {
            vh14.setCode(ResultCode.SUCCESS);
            vh14.setMessage("导入成功" + list.size() + "条，导入失败0条数据");
        } else {
            vh14.setData(errorMessage);
            vh14.setCode(ResultCode.FAIL);
            vh14.setMessage("导入成功" + (list.size() - fail) + "条，导入失败" + fail + "条数据");
        }
        return vh14;
    }

    public List<OcBReturnAfSendLog> getOcBReturnAfSendLog(Long ocBReturnAfSendId) {
        QueryWrapper queryWrapper = new QueryWrapper<OcBReturnAfSendLog>();
        queryWrapper.eq("oc_b_return_af_send_id", ocBReturnAfSendId);
        queryWrapper.orderByDesc("creationdate");
        return afSendLogMapper.selectList(queryWrapper);
    }

    /**
     * 发货后退款单打款
     *
     * @param ids  id集合
     * @param user 当前用户
     * @return
     */
    public ValueHolder payment(JSONArray ids, User user) {
        ValueHolder vh = new ValueHolder();
        Integer fail = 0;
        JSONArray errorMessage = new JSONArray(); // 错误信息
        for (int i = 0; i < ids.size(); i++) {
            OcBReturnAfSend ocBReturnAfSend = afSendMapper.selectById(ids.getLong(i));
            JSONObject jsonObject = new JSONObject();
            try {
                paymentCheckStatus(ocBReturnAfSend);
                this.afSendMapper.updateOcBPaymentStatus(ReturnAfSendPaymentEnum.PAYMENTING.toInteger(), ocBReturnAfSend.getId());
                insertReturnAfSendLog("额外退款单打款", "额外退款单打款成功", null, user, ocBReturnAfSend.getId());
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "打款成功!");
            } catch (NDSException ex) {
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", ex.getMessage());
                jsonObject.put("objid", ids.get(i));
                errorMessage.add(jsonObject);
                vh.put("code", ResultCode.FAIL);
                vh.put("message", ex.getMessage());
                fail++;
                insertReturnAfSendLog("额外退款单打款", "额外退款单打款失败，" + ex.getMessage(), null, user, ids.getLong(i));
            } catch (Exception e) {
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", e.getMessage());
                jsonObject.put("objid", ids.get(i));
                errorMessage.add(jsonObject);
                log.error(LogUtil.format("额外退款单打款异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                vh.put("code", ResultCode.FAIL);
                vh.put("message", e.getMessage());
                fail++;
            }
        }
        vh.put("data", errorMessage);
        if (ids.size() == 1) {
            return vh;
        }
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "打款成功" + ids.size() + "数据，打款失败0条数据");
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "打款成功" + (ids.size() - fail) + "数据，打款失败" + fail + "条数据");
        }
        return vh;
    }


    // 订单状态校验
    private void paymentCheckStatus(OcBReturnAfSend ocBReturnAfSend) {
        if (ocBReturnAfSend == null) {
            throw new NDSException("当前记录已不存在！");
        }
        if (ReturnAfSendReturnBillTypeEnum.NOREFUND.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
            throw new NDSException("当前记录未退款，不允许打款！");
        }
        if (ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
            throw new NDSException("当前记录退款完成，不允许打款！");
        }
        if (ReturnAfSendReturnBillTypeEnum.CANCEL.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
            throw new NDSException("当前记录已取消，不允许打款！");
        }
        if (!ReturnAfSendPaymentEnum.NO_PAYMENT.toInteger().equals(ocBReturnAfSend.getPaymentStatus())) {
            throw new NDSException("当前记录非未打款状态，不允许打款！");
        }
    }

    // 订单状态校验
    private void upExamineCheckStatus(OcBReturnAfSend ocBReturnAfSend) {
        if (ocBReturnAfSend == null) {
            throw new NDSException("请选择需反审核的订单！");
        }
        if (ReturnAfSendReturnBillTypeEnum.NOREFUND.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
            throw new NDSException("当前记录未退款，不允许反审核！");
        }
        if (ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
            throw new NDSException("当前记录退款完成，不允许反审核！");
        }
        if (ReturnAfSendReturnBillTypeEnum.CANCEL.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
            throw new NDSException("当前记录已取消，不允许反审核！");
        }
        if (!ReturnAfSendPaymentEnum.NO_PAYMENT.toInteger().equals(ocBReturnAfSend.getPaymentStatus())
                && !ReturnAfSendPaymentEnum.PAYMENT_FAIL.toInteger().equals(ocBReturnAfSend.getPaymentStatus())) {
            throw new NDSException("当前记录非未打款或打款失败状态，不允许反审核！");
        }
    }

    /**
     * 发货后审核主流程
     *
     * @param ocBReturnAfSend
     * @param user
     * @return
     */
    @Transactional(rollbackFor = NDSException.class, propagation = Propagation.REQUIRES_NEW)
    public ValueHolder examineMainStep(OcBReturnAfSend ocBReturnAfSend, User user, boolean isInventedReturn) {
        ValueHolder vh = new ValueHolder();
        Integer count = afSendItemMapper.selectCount(new QueryWrapper<OcBReturnAfSendItem>().eq(
                "oc_b_return_af_send_id", ocBReturnAfSend.getId()));
        if (count == 0) {
            throw new NDSException("当前记录无明细，不允许审核！");
        }
        // step002 判断单据类型 （0 退货退款 1仅退款）
        // @20200822 增加单据来源为手动时，也直接审核完成，更新成退款中
        if (1 == ocBReturnAfSend.getBillType()
                || RefundOrderSourceTypeEnum.MANUAL.getValue().equals(ocBReturnAfSend.getRefundOrderSourceType())) {
            // 更新单据状态
            updateReturnStauts(ocBReturnAfSend, user);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "审核完成");
            return vh;
        }
        List<OcBReturnAfSendItem> afSendItemList =
                afSendItemMapper.selectList(new QueryWrapper<OcBReturnAfSendItem>().eq("oc_b_return_af_send_id",
                        ocBReturnAfSend.getId()));
      /*  //step 003 判断退货状态

        if (ReturnAfSendInStatusEnum.ALLINSTORAGE.getVal() == ocBReturnAfSend.getProReturnStatus()) {
            // 如果全部入库直接 更新状态为已审核
            updateReturnStauts(ocBReturnAfSend, user);
        }*/
        // step 004 待入库 和部分入库继续走（排除特殊退货明细是否待入库）
        // boolean isExitPendingStorage = exclusionDetails(ocBReturnAfSend, afSendItemList, user);
        boolean isExitPendingStorage = exclusionDetails(afSendItemList);
        if (isInventedReturn) {
            if (log.isDebugEnabled()) {
                log.debug("RefundFormAfterDeliveryService 虚拟入库,默认不校验数量");
            }
            isExitPendingStorage = false;
        }
        if (isExitPendingStorage == true) {
            //存在待入库明细
            vh.put("code", ResultCode.FAIL);
            vh.put("data", ocBReturnAfSend.getId());
            vh.put("message", "申请数量与退货数量不一致");
            return vh;
        } else {
            // 不存在待入库明细
            // 单据转态改为审核
            updateReturnStauts(ocBReturnAfSend, user);
            // 在调用退款单传ag //// TODO: 2020/3/12 正在开发
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "审核成功");
            JSONArray array = new JSONArray();
            array.add(ocBReturnAfSend.getId());
            ValueHolder valueHolder = null;
            try {
                valueHolder = financialMainStep(ocBReturnAfSend, user);
                boolean ok = valueHolder.isOK();
                if (ok) {
                    // @20200810 退款完成才发AC @20200808 task#22262 AC支付宝对账--已发货退款单审核完成更新发送AC状态
                    // @20200818 task#24952 【支付宝对账】已发货退款单，创建类型=手动的类型，进行过滤不推数据给对账中心
                    // @20200821 过滤条件：单据来源为自动：refund_order_source_type = 2/return_status = 2
                    if (!RefundOrderSourceTypeEnum.MANUAL.getValue().equals(ocBReturnAfSend.getRefundOrderSourceType())
                            && Objects.nonNull(ocBReturnAfSend.getReturnStatus())
                            && ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal() == ocBReturnAfSend.getReturnStatus()) {
                        OmsReturnAfSendService.getInstance().updateToACStatusToPendingByTReturnId(ocBReturnAfSend.getTReturnId());
                    }

                    insertReturnAfSendLog("已发货退款单同意退款", "已发货退款单同意退款成功", null, user, ocBReturnAfSend.getId());
                } else {
                    Object data = valueHolder.get("data"); // 返回的错误信息
                    JSONArray array1 = JSONArray.parseArray(data.toString());
                    Object o = array1.get(0);
                    JSONObject object = JSONObject.parseObject(o.toString());
                    insertReturnAfSendLog("已发货退款单同意退款", "已发货退款单同意退款失败" + object.get("message"), null, user,
                            ocBReturnAfSend.getId());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("调用同意退款服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                insertReturnAfSendLog("已发货退款单同意退款", "已发货退款单同意退款失败" + e.getMessage(), null, user,
                        ocBReturnAfSend.getId());
            }
        }
        return vh;
    }

    /**
     * @param afSendItemList
     * @return
     * @20200822 变更自原来的方法
     * 排除 特殊明细后是不是 还是待入库和部分入库
     * （1。退换换单为已完成的的退货明细 2. 退货单为拒收的明细 3。退货类型为拦截而且拦截成功）
     */
    public boolean exclusionDetails(List<OcBReturnAfSendItem> afSendItemList) {
        if (CollectionUtils.isNotEmpty(afSendItemList)) {
            for (int i = 0; i < afSendItemList.size(); ) {
                OcBReturnAfSendItem ocBReturnAfSendItem = afSendItemList.get(i);

                if (Objects.nonNull(ocBReturnAfSendItem)) {
                    Long relationBillId = ocBReturnAfSendItem.getRelationBillId();
                    OcBReturnOrder ocBReturnOrder = returnOrderMapper.selectByid(relationBillId);

                    if (Objects.nonNull(ocBReturnOrder)) {
                        if (ReturnProTypeEnum.REJECTION.getVal().equals(ocBReturnOrder.getReturnProType())) {
                            // 退货单为拒收的明细
                            afSendItemList.remove(i);
                            continue;
                        }

                        if (ReturnStatusEnum.COMPLETION.getVal().equals(ocBReturnOrder.getReturnStatus())) {
                            // 退换货为完成的
                            afSendItemList.remove(i);
                            continue;
                        }

                        if (ReturnProTypeEnum.INTERCEPT.getVal().equals(ocBReturnOrder.getReturnProType()) &&
                                InterceptStatus.LAUNCH_INTERCEPT_SUCCESS.getCode().equals(ocBReturnOrder.getIntercerptStatus())) {
                            //退货类型为拦截而且拦截成功
                            afSendItemList.remove(i);
                            continue;
                        }

                        // 排除全部入库的退单
                        if (Objects.nonNull(ocBReturnOrder.getProReturnStatus()) && ReturnAfSendInStatusEnum.ALLINSTORAGE.getVal() == ocBReturnOrder.getProReturnStatus()) {
                            afSendItemList.remove(i);
                            continue;
                        }

                        // 部分入库,排除赠品
                        boolean excludeItem = isExcludeItem(ocBReturnOrder);
                        if (excludeItem) {
                            afSendItemList.remove(i);
                            continue;
                        }
                    }

                    i++;
                }
            }

            // 排除完该排除的内容后，如果还有明细，则不能审核通过
            return afSendItemList.size() > 0;
        }

        return false;
    }

    /**
     * 部分入库,商品明细是否包含赠品处理
     * 赠品类型: 0 否 1 是系统赠品 2 平台赠品'
     *
     * @param ocBReturnOrder 退货订单
     * @return 是否排除此退单
     */
    private boolean isExcludeItem(OcBReturnOrder ocBReturnOrder) {
        // 部分入库. 排除赠品,处理
        if (ReturnAfSendInStatusEnum.PARTIALWAREHOUSING.getVal() == ocBReturnOrder.getProReturnStatus()) {
            List<OcBReturnOrderRefund> refundItems =
                    returnOrderRefundMapper.queryRtnOrderRefundByoId(ocBReturnOrder.getId(),
                            OcBOrderConst.IS_ACTIVE_YES);
            if (refundItems == null || refundItems.size() < 1) {
                return false;
            }
            for (OcBReturnOrderRefund refundItem : refundItems) {
                String giftType = refundItem.getGiftType();
                boolean isGift = StringUtils.equalsIgnoreCase(giftType, GiftTypeEnum.SYSTEM.getVal())
                        || StringUtils.equalsIgnoreCase(giftType, GiftTypeEnum.PLATFORM.getVal());
                if (isGift) {
                    continue;
                }
                BigDecimal itemQtyRefund = refundItem.getQtyRefund();
                if (itemQtyRefund == null) {
                    return false;
                }
                BigDecimal qtyIn = refundItem.getQtyIn() == null ? BigDecimal.ZERO : refundItem.getQtyIn();
                boolean isPortion = itemQtyRefund.compareTo(qtyIn) != 0;
                if (isPortion) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * @param ocBReturnAfSend
     * @param user
     * @20200808 TODO id不是分库键，需要依据退单ID更新，但是退单ID可能为空
     */
    private void updateReturnStauts(OcBReturnAfSend ocBReturnAfSend, User user) {
        OcBReturnAfSend send = new OcBReturnAfSend();
        send.setId(ocBReturnAfSend.getId());
        send.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDING.getVal());
        send.setPaymentStatus(ReturnAfSendPaymentEnum.NO_PAYMENT.toInteger());
        send.setModifieddate(new Date());
        send.setModifierename(user.getEname());
        send.setModifiername(user.getName());
        send.setModifierid(user.getId() + 0L);
        send.setCheckTime(new Date());
        afSendMapper.updateById(send);
    }


    /**
     * 发货后退款单财审（退款单传AG服务 ）
     *
     * @param ids  id集合
     * @param user 当前用户
     * @return
     */
    public ValueHolder financialTrialTheFefundAfterDelivery(JSONArray ids, User user) {
        ValueHolder vh = new ValueHolder();
        RefundFormAfterDeliveryService bean = ApplicationContextHandle.getBean(RefundFormAfterDeliveryService.class);
        JSONArray errorMessage = new JSONArray(); // 错误信息
        Integer fail = 0;
        for (int i = 0; i < ids.size(); i++) {
            JSONObject jsonObject = new JSONObject();
            OcBReturnAfSend ocBReturnAfSend = afSendMapper.selectById((Long) ids.get(i));
            try {
               /* 2.未审核，则提示：“当前记录未审核，不允许同意退款！”
                3.已财审，则提示：“当前记录已财审，不允许重复同意退款！”
                4.已作废，则提示：“当前记录已作废，不允许同意退款！”*/
                if (ReturnAfSendReturnBillTypeEnum.NOREFUND.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
                    jsonObject.put("code", -1);
                    jsonObject.put("message", "当前记录未审核，不允许同意退款！");
                    jsonObject.put("objid", ids.get(i));
                    fail++;
                    errorMessage.add(jsonObject);
                    continue;
                } else if (ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
                    jsonObject.put("code", -1);
                    jsonObject.put("message", "当前记录已同意退款，不允许重复同意退款！");
                    jsonObject.put("objid", ids.get(i));
                    fail++;
                    errorMessage.add(jsonObject);
                } else if (ReturnAfSendReturnBillTypeEnum.CANCEL.toInteger().equals(ocBReturnAfSend.getReturnStatus())) {
                    jsonObject.put("code", ResultCode.FAIL);
                    jsonObject.put("message", "当前记录已取消，不允许同意退款！");
                    jsonObject.put("objid", ids.get(i));
                    fail++;
                    errorMessage.add(jsonObject);
                }
                vh = bean.financialMainStep(ocBReturnAfSend, user);
                Object code = vh.get("code");
                if (!vh.isOK()) {
                    jsonObject.put("code", ResultCode.FAIL);
                    jsonObject.put("message", Resources.getMessage(vh.get("message").toString()));
                    jsonObject.put("objid", ids.get(i));
                    fail++;
                    errorMessage.add(jsonObject);
                }
            } catch (Exception e) {
                log.error(LogUtil.format("额外退款单同意退款异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", Resources.getMessage(e.getMessage()));
                jsonObject.put("objid", ids.get(i));
                fail++;
                errorMessage.add(jsonObject);
            }
        }
        vh.put("data", errorMessage);
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "同意退款成功" + ids.size() + "数据，同意退款失败0条数据");
        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "同意退款成功" + (ids.size() - fail) + "数据，同意退款失败" + fail + "条数据");
        }
        return vh;
    }

    /**
     * 财审主流程
     *
     * @param ocBReturnAfSend
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder financialMainStep(OcBReturnAfSend ocBReturnAfSend, User user) {
        Boolean flag = this.omsSystemConfig.isRefundAutoCallAgEnabled();
        ValueHolder vh = new ValueHolder();
        Integer count = afSendItemMapper.selectCount(new QueryWrapper<OcBReturnAfSendItem>().eq(
                "oc_b_return_af_send_id", ocBReturnAfSend.getId()));
        if (count == 0) {
            throw new NDSException("发货后退款单明细不能为空");
        }
        // 如果退款单是仅退款 直接结束 标记退款完成
        Integer billType = ocBReturnAfSend.getBillType();
        //  判断单据类型 （0 退货退款 1仅退款）
        if (1 == billType) {
            updateAGStatusAndRetuernStatus(ocBReturnAfSend, user);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "同意退款完成");
            return vh;
        }
        // 单据来源为手动，可以同意退款
        if (ocBReturnAfSend.getRefundOrderSourceType().intValue() == 1) {
            updateAGStatusAndRetuernStatus(ocBReturnAfSend, user);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "同意退款完成");
            return vh;
        }
        //step 1.判断退款单平台类型，如果平台类型不是淘宝平台，则不走AG，不进行处理，如果是淘宝平台的退款单，进行下一步判断。
        if (PlatFormEnum.TAOBAO.getCode() != ocBReturnAfSend.getCpCPlatformId().intValue()) {
            throw new NDSException("平台类型不是淘宝平台，不走AG");
        }
        //step 2.判断退单所关联的店铺参数是否勾选【是否开启AG】参数，若没有勾选，则此店铺不进行传AG处理，如果勾选了AG参数，则继续进行判断。
        boolean toAgByShopStrategy = stCAutocheckService.isToAgByShopStrategy(ocBReturnAfSend.getCpCShopId());
        if (!toAgByShopStrategy) {
            throw new NDSException("店铺未勾选AG,不进行传AG处理");
        }
//        3.则继续判断退单传AG状态，
        if (AGStatusEnum.NOT.getVal().equals(Integer.valueOf(ocBReturnAfSend.getAgStatus()))
                || AGStatusEnum.SUCCESS.getVal().equals(Integer.valueOf(ocBReturnAfSend.getAgStatus()))) {
            //如果退单状态为已传AG（1）或者不传AG（3），则不进行处理
            throw new NDSException("传AG状态为已传和不传不进行处理");
        }
        //如果退单状态为未传AG（0）或者传AG失败（2），则进行下一步判断
//        4.判断退款单的平台退款单号是否为空 ，如果为空，则不走AG程序，不进行处理，不为空进行下一步判断。
        if (ocBReturnAfSend.getTReturnId() == null) {
            throw new NDSException("平台退款单号是否为空 不进行处理");
        }
//        5.根据退单的平台退款单号查找淘宝退款单中间表是否有此退单，不存在，则不走AG程序，不进行处理；若查找到退单记录，进行下一步判断
        IpBTaobaoRefund ipBTaobaoRefund = refundMapper.selectOne(new QueryWrapper<IpBTaobaoRefund>().eq("refund_id",
                ocBReturnAfSend.getTReturnId()));
        log.debug("step005");
        if (ipBTaobaoRefund == null) {
            throw new NDSException("查找淘宝退款单中间表是无此退单不进行处理");
        }
//        6.判断退单中间表的退单状态，
        if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(ipBTaobaoRefund.getStatus()) ||
                TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(ipBTaobaoRefund.getStatus())) {
//         当退单状态为退款关闭、卖家拒绝退款时，则不走AG程序，不进行处理，程序结束。
            throw new NDSException("退单状态为退款关闭、卖家拒绝退款时，则不走AG程序");
        }
        if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(ipBTaobaoRefund.getStatus())) {
//        当退单状态为退款成功时，则退货明细的AG状态更新为已传AG，退款单状态更新为已财审
            updateAGStatusAndRetuernStatus(ocBReturnAfSend, user);
        }
        boolean AgFlag = true;
        int type = 0; // 0 调用的是 ag取消 发货 1Ag入库接口
       /* List<OcBReturnAfSendItem> afSendItemList = afSendItemMapper.selectList(new
       QueryWrapper<OcBReturnAfSendItem>().eq("oc_b_return_af_send_id", ocBReturnAfSend.getId()));
        Long relationBillId = afSendItemList.get(0).getRelationBillId();*/
        if (TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode().equals(ipBTaobaoRefund.getStatus())
                || TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode().equals(ipBTaobaoRefund.getStatus())) {
            //         当状态为：等待卖家同意退款、卖家同意退款等待买家退货、等待卖家收货时，调用【AG入库接口】。
            // step  7.判断调用AG入仓接口是否成功？
            //1. 成功(调用接口成功，则更新传AG状态为已传AG，且退款单状态更新已财审；添加退换货订单操作日志；)
            type = 1;
            if (flag) {
                AgFlag = ipRpcService.updateLogisticsWarehouse(ocBReturnAfSend.getTReturnId(),
                        ocBReturnAfSend.getCpCShopId(), user);
            }
        } else if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(ipBTaobaoRefund.getStatus())) {
            //如果是买家申请退款，等待买卖同意 （调ag取消发货接口 )
            String sourceBillNo = ocBReturnAfSend.getSourceBillNo();
            if (flag) {
                ValueHolderV14 holderV14 = cancelToAgService.orderCancelToAgRetry(ipBTaobaoRefund, user);
                if (holderV14.getCode() == 0) {
                    AgFlag = true;
                }
            }
        }
        if (AgFlag == true) {
            updateAGStatusAndRetuernStatus(ocBReturnAfSend, user);
            if (type == 1) {
                insertReturnAfSendLog("同步AG入仓", "同步AG入仓成功", null, user, ocBReturnAfSend.getId());
            } else {
                insertReturnAfSendLog("AG取消发货", "AG取消发货接口成功", null, user, ocBReturnAfSend.getId());
            }
        } else {
            //2. 失败(调用接口失败，则返回失败信息，添加到退换货订单操作日志中，更新传AG状态为传AG失败；)
            OcBReturnAfSend afSend = new OcBReturnAfSend();
            afSend.setId(ocBReturnAfSend.getId());
            afSend.setModifieddate(new Date());
            afSend.setModifiername(user.getName());
            afSend.setModifierename(user.getEname());
            afSend.setModifierid(user.getId() + 0L);
            afSend.setAgStatus(AGStatusEnum.FAIL.getVal().toString());
            afSendMapper.updateById(afSend);
            if (type == 1) {
                insertReturnAfSendLog("同步AG入仓", "同步AG入仓失败原因为:", null, user, ocBReturnAfSend.getId());
            } else {
                insertReturnAfSendLog("取消AG发货", "取消AG发货失败原因为:", null, user, ocBReturnAfSend.getId());
            }
        }
        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", "同意退款完成");
        return vh;
    }

    private void updateAGStatusAndRetuernStatus(OcBReturnAfSend ocBReturnAfSend, User user) {
        OcBReturnAfSend afSend = new OcBReturnAfSend();
        afSend.setId(ocBReturnAfSend.getId());
        afSend.setModifieddate(new Date());
        afSend.setModifiername(user.getName());
        afSend.setModifierename(user.getEname());
        afSend.setModifierid(user.getId() + 0L);
        afSend.setAgStatus(AGStatusEnum.SUCCESS.getVal().toString());
        afSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
        // 退款完成传对账,赋待传
        ocBReturnAfSend.setToSettleStatus(ToACStatusEnum.PENDING.val());
        afSend.setFinancialAuditTime(new Date());
        // @20201209 同意退款时，打款状态改为打款成功
        afSend.setPaymentStatus(ReturnAfSendPaymentEnum.PAYMENT_SUCCESS.getVal());

        afSendMapper.updateById(afSend);

        /** wangshuai add  2020.3.25 start*/
        // 插入oc_b_task_refund_sap表
        oms2SapMapper.insertSingleTaskOrder(SapTaskTableEnum.REFUND.txt(), ocBReturnAfSend.getId(),
                ModelUtil.getSequence(SapTaskTableEnum.REFUND.txt()));
        /** wangshuai add  2020.3.25  end*/
    }


    /**
     * 添加已发货退款单操作日志
     *
     * @param type     日志的类型
     * @param message  日志信息
     * @param param    日志参数
     * @param user     登录用户
     * @param afSendId 已发货退款单id
     */
    public void insertReturnAfSendLog(String type, String message, String param, User user, Long afSendId) {
        OcBReturnAfSendLog afSendLog = new OcBReturnAfSendLog();
        afSendLog.setId(ModelUtil.getSequence("oc_b_return_af_send_log"));
        afSendLog.setUserName(user.getName());
        afSendLog.setIpAddress(user.getLastloginip());
        afSendLog.setLogParam(param);
        afSendLog.setLogMessage(message);
        afSendLog.setLogType(type);
        afSendLog.setOcBReturnAfSendId(afSendId);
        afSendLog.setAdOrgId(user.getOrgId() + 0L);
        afSendLog.setAdClientId(user.getClientId() + 0L);
        afSendLog.setOwnerid(user.getId() + 0L);
        afSendLog.setOwnerename(user.getEname());
        afSendLog.setOwnername(user.getName());
        afSendLog.setCreationdate(new Date());
        afSendLog.setModifierid(user.getId() + 0L);
        afSendLog.setModifierename(user.getEname());
        afSendLog.setModifiername(user.getName());
        afSendLog.setModifieddate(new Date());
        afSendLog.setIsactive("Y");


        afSendLogMapper.insert(afSendLog);
    }

    /**
     * 新增明细
     *
     * @param param
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveAfterDeliverItem(JSONObject param, User user) {

        ValueHolderV14 vh = new ValueHolderV14();

        if (!param.containsKey("id") && !param.containsKey("OcBReturnAfSendItem") && !param.containsKey("orderId")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }
        Long id = param.getLong("id");
        if (id != null && id > 0) {
            OcBReturnAfSend afSend = afSendMapper.selectById(id);
            if (afSend == null) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("已发货退款单不存在");
                return vh;
            }
            if (ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal() != afSend.getReturnStatus()) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("非未退款状态不允许修改");
                return vh;
            }
        }
        Long orderId = param.getLong("orderId");
        JSONArray ocBReturnAfSendItemList = param.getJSONArray("OcBReturnAfSendItem");
        if (id == null && ocBReturnAfSendItemList.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }
        OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
        List<OcBReturnAfSendItem> needInsertItem = new ArrayList<>();
//        List<OcBReturnAfSendItem> afSendItemList = JSONArray.parseArray(ocBReturnAfSendItem.toJSONString(),
//        OcBReturnAfSendItem.class);
        for (int i = 0; i < ocBReturnAfSendItemList.size(); i++) {
            JSONObject item = ocBReturnAfSendItemList.getJSONObject(i);
            Long itemId = item.getLong("id");  //原单明细的id
            OcBOrderItem ocOrderItem = ocBOrderItemMapper.selectOne(new QueryWrapper<OcBOrderItem>().eq(
                    "oc_b_order_id", orderId).eq("id", itemId));
            if (ocOrderItem == null) {
                throw new NDSException("订单明细不存在");
            }
            BigDecimal amtReruen = item.getBigDecimal("AMT_RETURN");
            BigDecimal freight = item.getBigDecimal("FREIGHT");
            OcBReturnAfSendItem afSendItem = new OcBReturnAfSendItem();
            // 查询添加的明细是不是有一样的
            OcBReturnAfSendItem findAfSendItem =
                    afSendItemMapper.selectOne(new QueryWrapper<OcBReturnAfSendItem>().eq("oc_b_return_af_send_id",
                            id).eq("ps_c_sku_id", ocOrderItem.getPsCSkuId()));
            if (ocOrderItem.getAmtRefund().compareTo(ocOrderItem.getRealAmt()) == 0) {
                throw new NDSException("已退款金额等于单行成交金额不允许新增");
            }
            // 判断退款金额不能大于原始订单明细中的“成交金额-已退金额”；
            if (amtReruen.compareTo(ocOrderItem.getRealAmt().subtract(ocOrderItem.getAmtRefund())) > 0) {
                throw new NDSException("退款金额不能大于订单明细的成交金额减去已退金额");
            }
            if (findAfSendItem == null) {
                afSendItem.setId(ModelUtil.getSequence("oc_b_return_af_send_item"));
                afSendItem.setOcBReturnAfSendId(id);
                afSendItem.setRelationBillType(1L);
                afSendItem.setRelationBillId(ocOrderItem.getOcBOrderId());
                if (ocOrderItem.getIsGift() != null) {
                    afSendItem.setGift(ocOrderItem.getIsGift().toString());
                } else {
                    afSendItem.setGift("0");
                }
                afSendItem.setRelationBillItemId(itemId);
                afSendItem.setRelationBillNo(ocBOrder.getBillNo());
                afSendItem.setPsCSkuId(ocOrderItem.getPsCSkuId());
                afSendItem.setPsCSkuEcode(ocOrderItem.getPsCSkuEcode());
                afSendItem.setPsCProId(ocOrderItem.getPsCProId());
                afSendItem.setPsCProEcode(ocOrderItem.getPsCProEcode());
                afSendItem.setPsCProEname(ocOrderItem.getPsCProEname());
                afSendItem.setPsCSpecEname(ocOrderItem.getSkuSpec());
                afSendItem.setPsCSkuPtEcode(ocOrderItem.getPsCSkuPtEcode());
                afSendItem.setPsCSkuEname(ocOrderItem.getPsCSkuEname());
                afSendItem.setPtProName(ocOrderItem.getPtProName());
                afSendItem.setAmtReturn(amtReruen);
                afSendItem.setAmtActual(ocOrderItem.getRealAmt());
                afSendItem.setAmtHasReturn(ocOrderItem.getRealAmt().subtract(ocOrderItem.getAmtRefund()).subtract(amtReruen));// 可退金额
                afSendItem.setPurchaseQty(ocOrderItem.getQty());
                afSendItem.setAdOrgId(user.getOrgId() + 0L);
                afSendItem.setIsactive("Y");
                afSendItem.setFreight(freight);
                afSendItem.setAdClientId(user.getClientId() + 0L);
                afSendItem.setOwnerid(user.getId() + 0L);
                afSendItem.setOwnerename(user.getEname());
                afSendItem.setOwnername(user.getName());
                afSendItem.setCreationdate(new Date());
                afSendItem.setModifierid(user.getId() + 0L);
                afSendItem.setModifierename(user.getEname());
                afSendItem.setGift(ocOrderItem.getGiftType());
                afSendItem.setModifiername(user.getName());
                afSendItem.setModifieddate(new Date());
                needInsertItem.add(afSendItem);
            } else {
                OcBReturnAfSendItem afSendItem1 = new OcBReturnAfSendItem();
                afSendItem1.setAmtReturn(findAfSendItem.getAmtReturn().add(amtReruen));
                afSendItem1.setAmtHasReturn(ocOrderItem.getRealAmt().subtract(ocOrderItem.getAmtRefund()).subtract(amtReruen));
                afSendItem1.setModifieddate(new Date());
                afSendItem1.setModifierename(user.getEname());
                afSendItem1.setModifierid(user.getId() + 0L);
                afSendItem.setFreight(freight);
                afSendItem1.setModifiername(user.getName());
                int update = afSendItemMapper.update(afSendItem1, new QueryWrapper<OcBReturnAfSendItem>().eq(
                        "oc_b_return_af_send_id", findAfSendItem.getOcBReturnAfSendId()).
                        eq("id", findAfSendItem.getId()));
            }
            OcBOrderItem ocBOrderItem = new OcBOrderItem();
            ocBOrderItem.setAmtRefund(ocOrderItem.getAmtRefund().add(amtReruen));
            ocBOrderItemMapper.update(ocBOrderItem, new QueryWrapper<OcBOrderItem>().eq("oc_b_order_id", orderId).eq(
                    "id", itemId));
        }
        if (!CollectionUtils.isEmpty(needInsertItem)) {
            int i = afSendItemMapper.batchInsert(needInsertItem);
        }
        updateAplicaAmount(user, id);
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("新增明细成功");
        return vh;


    }

    /**
     * 更新退款金额
     *
     * @param user
     * @param id
     */
    private void updateAplicaAmount(User user, Long id) {
        BigDecimal applicaAmount = afSendItemMapper.calculateApplicationAmount(id);
        if (applicaAmount == null) {
            applicaAmount = BigDecimal.ZERO;
        }
        OcBReturnAfSend afSend = new OcBReturnAfSend();
        afSend.setId(id);
        afSend.setModifierid(user.getId() + 0L);
        afSend.setModifierename(user.getEname());
        afSend.setModifiername(user.getName());
        afSend.setModifieddate(new Date());
        afSend.setAmtReturnApply(applicaAmount);
        afSendMapper.updateById(afSend);
    }

    /**
     * 明细删除
     *
     * @param param
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 deleteAfterDeliverItem(JSONObject param, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (!param.containsKey("AfSendID") && !param.containsKey("AfSendItemIds")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }
        Long AfSendID = param.getLong("AfSendID");
        JSONArray AfSendItemIds = param.getJSONArray("AfSendItemIds");
        if (AfSendID == null && AfSendItemIds.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }
        OcBReturnAfSend afSend = afSendMapper.selectById(AfSendID);
        if (ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal() != afSend.getReturnStatus()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("非未退款状态不允许修改");
            return vh;
        }
        for (int i = 0; i < AfSendItemIds.size(); i++) {
            Long aLong = AfSendItemIds.getLong(i);
            OcBReturnAfSendItem afSendItem = afSendItemMapper.selectOne(new QueryWrapper<OcBReturnAfSendItem>().eq(
                    "oc_b_return_af_send_id", AfSendID).eq("id", aLong));
            OcBOrderItem ocBOrderItem1 = ocBOrderItemMapper.selectOne(new QueryWrapper<OcBOrderItem>().
                    eq("oc_b_order_id", afSendItem.getRelationBillId()).eq("id", afSendItem.getRelationBillItemId()));
            OcBOrderItem ocBOrderItem = new OcBOrderItem();
            ocBOrderItem.setAmtRefund(ocBOrderItem1.getAmtRefund().subtract(afSendItem.getAmtReturn()));
            ocBOrderItem.setModifierid(user.getId() + 0L);
            ocBOrderItem.setModifierename(user.getEname());
            ocBOrderItem.setModifiername(user.getName());
            ocBOrderItem.setModifieddate(new Date());

            ocBOrderItemMapper.update(ocBOrderItem, new QueryWrapper<OcBOrderItem>().eq("oc_b_order_id",
                    afSendItem.getRelationBillId()).eq("id", afSendItem.getRelationBillItemId()));
        }
        int delete = afSendItemMapper.delete(new QueryWrapper<OcBReturnAfSendItem>().in("id", AfSendItemIds).eq(
                "oc_b_return_af_send_id", AfSendID));
        updateAplicaAmount(user, AfSendID);
        if (delete > 0) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("删除明细成功");
            return vh;
        } else {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("删除明细失败");
            return vh;
        }
    }

    /**
     * 发货后退款单编辑保存服务
     *
     * @param param
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 saveAfterDeliver(JSONObject param, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (param.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }
        if (!param.containsKey("AfSend") && !param.containsKey("AfSendItem") && param.getJSONObject("AfSend").isEmpty()
                && param.getJSONObject("AfSendItem").isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }
        Long objId = param.getLong("objId");
        OcBReturnAfSend afSend = JSONObject.parseObject(param.getJSONObject("AfSend").toJSONString(),
                OcBReturnAfSend.class);
        JSONArray itemParam = param.getJSONArray("AfSendItem");
        if (afSend == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请选择关联的原单！");
            return vh;
        }
        if (itemParam.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("退款明细不能为空，请重新录入!");
            return vh;
        }
        if (StringUtils.isEmpty(afSend.getSourceBillNo())) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("原始订单编号不能为空，请重新录入!");
            return vh;
        }
//        List < OcBReturnAfSendItem > afSendItemList = JSONArray.parseArray(param.getJSONArray("AfSendItem").
//        toJSONString(), OcBReturnAfSendItem.class);
        try {
            String redisKey = "oms:core:extra:orderId:".concat(afSend.getSourceBillNo());
            RedisReentrantLock lock = new RedisReentrantLock(redisKey);
            if (lock.tryLock(0, TimeUnit.SECONDS)) {
                try {
                    if (objId < 0) {
                        // 新增服务
                        log.debug("saveAfterDeliver itemParam={}", JSON.toJSONString(itemParam));
                        saveOcBReturnAfSend(afSend, itemParam, user);
                    } else {
                        // 编辑服务
                        updateOcBReturnAfSend(afSend, itemParam, user);
                    }
                } catch (Exception ee) {
                    log.error("额外退款保存失败", ee);
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(ee.getMessage());
                    return vh;
                } finally {
                    lock.unlock();
                }
            } else {
                throw new Exception("Redis lock failed");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("额外退款redislock异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.toString());
            return vh;
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("保存成功");
        return vh;
    }

    /**
     * 更新服务
     *
     * @param afSend
     * @param itemParam
     * @param user
     */
    private void updateOcBReturnAfSend(OcBReturnAfSend afSend, JSONArray itemParam, User user) {
        // @20200805 bug-prd-已发货退款单平台退款单号需要可以更新为空，如果要更新某个字段为null，需要设置为空字符串
//        if (ObjectUtils.isEmpty(afSend.getTReturnId())) {
//            afSend.setTReturnId("");
//        }
        afSend.setTReturnId(null);
        // @20200805 删除无效的查询
        // OcBReturnAfSend afSend1 = afSendMapper.selectById(afSend.getId());

        // @20200729 bug-20265 删除非为退款状态的更新校验
//        if (ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal() != afSend1.getReturnStatus()) {
//            throw new NDSException("非未退款状态不允许修改");
//        }
        if (afSend.getAmtReturnApply() == null) {
            throw new NDSException("请输入申请退款金额");
        }
        if (afSend.getAmtReturnActual() == null) {
            throw new NDSException("请输入实际退款金额");
        }
//        if (afSend.getAmtReturnApply().compareTo(afSend.getAmtReturnActual()) < 0) {
//            throw new NDSException("实际退款金额不能大于申请退款金额");
//        }
        // 自动生成卖家备注
        this.autoUpdateSellerRemark(afSend);
        if(StringUtils.isNotBlank(afSend.getSourceBillNo())){
            //修改原始单据 查询业务类型：原单业务类型对应的退款业务类型
            OcBOrder ocBOrder = ocBOrderMapper.selectById(Long.valueOf(afSend.getSourceBillNo()));
            try{
                StCBusinessType stCBusinessType = omsRefundOrderService.queryRefundOrderType(ocBOrder);
                afSend.setBusinessTypeId(stCBusinessType.getId());
                afSend.setBusinessTypeCode(stCBusinessType.getEcode());
                afSend.setBusinessTypeName(stCBusinessType.getEname());
            }catch (Exception e){
                log.error(LogUtil.format("查询业务类型失败={}",
                        "RefundFormAfterDeliveryService"), Throwables.getStackTraceAsString(e));
            }
        }
        afSendMapper.updateById(afSend);
        boolean checkAmount = this.isCheckAmount(afSend.getOcBReturnTypeId());
        for (int i = 0; i < itemParam.size(); i++) {
            JSONObject item = itemParam.getJSONObject(i);
            OcBReturnAfSendItem afSendItem = new OcBReturnAfSendItem();
            afSendItem.setModifiername(user.getName());
            afSendItem.setModifierid(user.getId() + 0L);
            afSendItem.setModifierename(user.getEname());
            afSendItem.setModifieddate(new Date());
            QueryWrapper<OcBReturnAfSendItem> wrapper = new QueryWrapper<>();
            Long id = item.getLong("id");  //发后后退单明细id
            OcBReturnAfSendItem afSendItem1 = afSendItemMapper.selectOne(new QueryWrapper<OcBReturnAfSendItem>().
                    eq("oc_b_return_af_send_id", afSend.getId()).eq("id", id));
            BigDecimal amtReruen = item.getBigDecimal("AMT_RETURN"); // 退款金额
            BigDecimal freight = item.getBigDecimal("FREIGHT");  // 运费
            afSendItem.setFreight(freight);
            afSendItem.setAmtReturn(amtReruen);
            OcBOrderItem ocBOrderItem = ocBOrderItemMapper.selectOne(new QueryWrapper<OcBOrderItem>().
                    eq("oc_b_order_id", afSendItem1.getRelationBillId()).eq("id", afSendItem1.getRelationBillItemId()));
            OcBOrderItem orderItem = new OcBOrderItem();
            // 更新时候跟原来值比较 来进行 更新原单明细的 退款金额
            if (afSendItem1.getAmtReturn().compareTo(amtReruen) != 0) {
                BigDecimal differenceAmount = BigDecimal.ZERO;
                if (amtReruen.compareTo(afSendItem1.getAmtReturn()) > 0) {
                    differenceAmount = amtReruen.subtract(afSendItem1.getAmtReturn());
                    orderItem.setAmtRefund(ocBOrderItem.getAmtRefund().add(differenceAmount));
                    afSendItem.setAmtHasReturn(afSendItem1.getAmtHasReturn().subtract(differenceAmount));
                } else if (amtReruen.compareTo(afSendItem1.getAmtReturn()) < 0) {
                    differenceAmount = amtReruen.subtract(afSendItem1.getAmtReturn());
                    orderItem.setAmtRefund(ocBOrderItem.getAmtRefund().add(differenceAmount));
                    afSendItem.setAmtHasReturn(afSendItem1.getAmtHasReturn().subtract(differenceAmount));
                }
               /* if (ocBOrderItem.getAmtRefund().compareTo(ocBOrderItem.getRealAmt()) == 0) {
                    throw new NDSException("已退款金额等于成交金额不允许编辑");
                }*/
                if (checkAmount) {
                    // 剩余可退金额
                    BigDecimal remainReturnAmt =
                            ocBOrderItem.getRealAmt().subtract(ocBOrderItem.getAmtRefund()).add(afSendItem1.getAmtReturn());
                    // 判断退款金额不能大于原始订单明细中的“成交金额-已退金额”；
                    if (amtReruen.compareTo(remainReturnAmt.add(new BigDecimal(20))) > 0) {
//                    throw new NDSException("退款金额不能大于订单明细的成交金额减去已退金额");
                        throw new NDSException("退款金额大于成交金额超20元，不允许保存");
                    }
                }
            }

            // @20200805 task#23951 增加qty_in（退货数量）字段的更新
            afSendItem.setQtyIn(item.containsKey("QTY_IN") ? item.getBigDecimal("QTY_IN") : BigDecimal.ZERO);

            wrapper.eq("id", id);
            wrapper.eq("oc_b_return_af_send_id", afSend.getId());
            int update = afSendItemMapper.update(afSendItem, wrapper);
            if (update > 0) {
                if (afSendItem1.getAmtReturn().compareTo(amtReruen) != 0) {
                    orderItem.setAmtRefund(amtReruen);
                    int update1 = ocBOrderItemMapper.update(orderItem, new QueryWrapper<OcBOrderItem>().
                            eq("id", afSendItem1.getRelationBillItemId()).eq("oc_b_order_id",
                            afSendItem1.getRelationBillId()));
                }
            }
        }
        updateAplicaAmount(user, afSend.getId());
    }

    /**
     * 按钮权限
     *
     * @param param
     * @param user
     */
    public ValueHolderV14 checkDisplayBtn(JSONObject param, User user) {
        log.info("checkDisplayBtn param : {}",param);
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        OcBReturnAfSend afSend = new OcBReturnAfSend();
        try{
            afSend = JSONObject.parseObject(param.getJSONObject("AfSend").toJSONString(),
                    OcBReturnAfSend.class);
        }catch (Exception e){
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("参数格式有误！");
        }

        Long afSendId = afSend.getId();
        if(afSendId==null){
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("没有id！");
        }

        //申请退款金额 afSend.getAmtReturnApply() 实际退款金额 getAmtReturnActual
        QueryWrapper queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id",afSend.getId());
        OcBReturnAfSend querySend = afSendMapper.selectById(queryWrapper);

        if(querySend == null ){
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("当前记录已不存在！");
        }
        if(querySend.getAmtReturnActual() == null ){
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("退款金额异常！");
        }

        /** todo 需要判断策略*/
        JSONObject json = new JSONObject();
        json.put("user_id", afSendId);
        if (afSendId > 10) {
            json.put("is_show_btn", true);
        } else {
            json.put("is_show_btn", false);
        }
        valueHolderV14.setData(json);
        return valueHolderV14;

    }

    private void buildCardAutoVoidStatus(OcBReturnAfSend ocBReturnAfSend) {
        if (ObjectUtil.isNotNull(ocBReturnAfSend.getBusinessTypeId())) {
            // 判断是否是需要设置成"未作废"的订单(主要是奶卡相关的订单)
            if (ObjectUtil.equal(ocBReturnAfSend.getBusinessTypeCode(), OrderBusinessTypeCodeEnum.MILK_RETURN_ONLY.getCode()) ||
                    ObjectUtil.equal(ocBReturnAfSend.getBusinessTypeCode(), OrderBusinessTypeCodeEnum.CYCLE_RETURN_ONLY.getCode())) {
                ocBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.HAVE_NOT_VOID.getCode());
                ocBReturnAfSend.setCardAutoVoidMark(1);
            }
        }
    }

    /**
     * 新增服务
     *
     * @param afSend
     * @param itemParam
     * @param user
     */
    private void saveOcBReturnAfSend(OcBReturnAfSend afSend, JSONArray itemParam, User user) {
        // @20200730 非空判断方式变更，!= null这种方式无法过滤空字符串
        // if (afSend.getTReturnId() != null) {
        if (StringUtils.isNotEmpty(afSend.getTReturnId())) {
            Integer count = afSendMapper.selectCount(new QueryWrapper<OcBReturnAfSend>().eq("t_return_id",
                    afSend.getTReturnId()).ne("return_status", ReturnAfSendReturnBillTypeEnum.CANCEL.getVal()));
            if (count > 0) {
                throw new NDSException("已存在相同的平台退款单号,不允许保存");
            }
        }
        //当退款分类为系统设置的默认值时  可多倍赔偿 跳过金额校验
        boolean checkAmount = this.isCheckAmount(afSend.getOcBReturnTypeId());

        afSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal()); // 默认待审核
        afSend.setProReturnStatus(ReturnAfSendInStatusEnum.PENDINGSTORAGE.getVal());  // 默认待入库
//        afSend.setTReturnStatus();
        afSend.setAgStatus(AGStatusEnum.INIT.getVal().toString());
        IpBTaobaoRefund ipBTaobaoRefund = refundMapper.selectTaobaoByRefundId(afSend.getTReturnId());
        if (ipBTaobaoRefund != null) {
            afSend.setTReturnStatus(ipBTaobaoRefund.getStatus());
        }
//        afSend.setAmtReturnApply();
//        afSend.setAmtReturnActual();
        afSend.setReturnApplyTime(afSend.getReturnApplyTime() == null ? new Date() : afSend.getReturnApplyTime());
//        afSend.setReturnOverTime();
        afSend.setBillNo(sequenceUtil.aFbuildBillNo());
        afSend.setAmtReturnActual(Optional.ofNullable(afSend.getAmtReturnApply()).orElse(BigDecimal.ZERO)); // 实际退款金额默认 跟退款金额一样
        afSend.setAdOrgId(user.getOrgId() + 0L);
        afSend.setIsactive("Y");
        afSend.setAdClientId(user.getClientId() + 0L);
        afSend.setOwnerid(user.getId() + 0L);
        afSend.setOwnerename(user.getEname());
        afSend.setOwnername(user.getName());
        afSend.setCreationdate(new Date());
        afSend.setModifierid(user.getId() + 0L);
        afSend.setModifierename(user.getEname());
        afSend.setModifiername(user.getName());
        afSend.setModifieddate(new Date());
        Long ocBReturnAfSendId = ModelUtil.getSequence("oc_b_return_af_send");
        afSend.setId(ocBReturnAfSendId);
        List<OcBReturnAfSendItem> afSendItemList = new ArrayList<>();
        OcBOrder ocBOrder = ocBOrderMapper.selectById(Long.valueOf(afSend.getSourceBillNo()));
        Long platformId = ocBOrder.getPlatform() != null ? Long.valueOf(ocBOrder.getPlatform()) : null;
        afSend.setCpCPlatformId(platformId);
        for (int i = 0; i < itemParam.size(); i++) {
            JSONObject item = itemParam.getJSONObject(i);
            Long id = item.getLong("id") != null ? item.getLong("id") : item.getLong("ID");  //原单明细的id
            BigDecimal amtReruen = item.getBigDecimal("AMT_RETURN");
            BigDecimal freight = item.getBigDecimal("FREIGHT");
            OcBOrderItem ocBOrderItem = ocBOrderItemMapper.selectOne(new QueryWrapper<OcBOrderItem>().
                    eq("oc_b_order_id", afSend.getSourceBillNo()).eq("id", id));
//            if (ocBOrderItem.getAmtRefund().compareTo(ocBOrderItem.getRealAmt()) == 0) {
//                throw new NDSException("已退款金额等于成交金额不允许新增");
//            }
            //当退款分类为系统设置的默认值时  可多倍赔偿 跳过金额校验
            if (checkAmount) {
                // 可退金额 ocBOrderItem.getAmtRefund()如果为null 则赋值0
                BigDecimal remainReturnAmt = ocBOrderItem.getRealAmt().subtract(ocBOrderItem.getAmtRefund() == null ? BigDecimal.ZERO : ocBOrderItem.getAmtRefund());
                // 判断退款金额不能大于原始订单明细中的“成交金额-已退金额”；
                if (amtReruen.compareTo(remainReturnAmt.add(new BigDecimal(20))) > 0) {
//                throw new NDSException("退款金额不能大于订单明细的成交金额减去已退金额");
                    throw new NDSException("大于成交金额超20元，不允许保存");
                }
            }
            OcBReturnAfSendItem afSendItem = new OcBReturnAfSendItem();
            if (1 == afSend.getBillType()) {
                afSendItem.setRelationBillType(1L);
            } else {
                afSendItem.setRelationBillType(0L);
            }
            afSendItem.setRelationBillId(Long.valueOf(afSend.getSourceBillNo()));
            afSendItem.setRelationBillNo(ocBOrder.getBillNo());
            if (ocBOrderItem.getIsGift() != null) {
                afSendItem.setGift(ocBOrderItem.getIsGift().toString());
            } else {
                afSendItem.setGift("0");
            }
            afSendItem.setRelationBillItemId(id); //原单明细id
            afSendItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            afSendItem.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            afSendItem.setPsCProId(ocBOrderItem.getPsCProId());
            afSendItem.setPsCProEcode(ocBOrderItem.getPsCProEcode());
            afSendItem.setPsCProEname(ocBOrderItem.getPsCProEname());
            afSendItem.setPsCSpecEname(ocBOrderItem.getSkuSpec());
            afSendItem.setAmtReturn(amtReruen);
            afSendItem.setFreight(freight);
            afSendItem.setAmtActual(ocBOrderItem.getRealAmt());
            afSendItem.setPsCSkuEname(ocBOrderItem.getPsCSkuEname()); //
            afSendItem.setPsCSkuPtEcode(ocBOrderItem.getPsCSkuPtEcode());
            afSendItem.setPtProName(ocBOrderItem.getPsCProEname());
            afSendItem.setAmtHasReturn(ocBOrderItem.getRealAmt().subtract(ocBOrderItem.getAmtRefund() == null ? BigDecimal.ZERO : ocBOrderItem.getAmtRefund()).subtract(amtReruen));// 可退金额
            afSendItem.setPurchaseQty(ocBOrderItem.getQty());
            afSendItem.setGift(ocBOrderItem.getGiftType());
            afSendItem.setId(ModelUtil.getSequence("oc_b_return_af_send_item"));
            afSendItem.setOcBReturnAfSendId(ocBReturnAfSendId);
            afSendItem.setAdOrgId(user.getOrgId() + 0L);
            afSendItem.setIsactive("Y");
            afSendItem.setAdClientId(user.getClientId() + 0L);
            afSendItem.setOwnerid(user.getId() + 0L);
            afSendItem.setOwnerename(user.getEname());
            afSendItem.setOwnername(user.getName());
            afSendItem.setCreationdate(new Date());
            afSendItem.setModifierid(user.getId() + 0L);
            afSendItem.setModifierename(user.getEname());
            afSendItem.setModifiername(user.getName());
            afSendItem.setModifieddate(new Date());

            // @20200805 task#23951 增加qty_in（退货数量）字段的保存
            afSendItem.setQtyIn(item.containsKey("QTY_IN") ? item.getBigDecimal("QTY_IN") : BigDecimal.ZERO);

            afSendItem.setOcBOrderItemId(ocBOrderItem.getId());
            if (ObjectUtil.isNotNull(ocBOrderItem.getOcBOrderId())) {
                OcBOrder order = ocBOrderMapper.get4AfReturn(ocBOrderItem.getOcBOrderId());
                if (ObjectUtil.isNotNull(order)) {
                    afSendItem.setOcBOrderId(order.getId());
                    afSendItem.setBusinessTypeCode(order.getBusinessTypeCode());
                    afSendItem.setBusinessTypeId(order.getBusinessTypeId());
                    afSendItem.setBusinessTypeName(order.getBusinessTypeName());
                }
            }

            afSendItemList.add(afSendItem);
            OcBOrderItem orderItem = new OcBOrderItem();
            orderItem.setAmtRefund((ocBOrderItem.getAmtRefund() == null ? BigDecimal.ZERO : ocBOrderItem.getAmtRefund()).add(amtReruen));
            int update = ocBOrderItemMapper.update(orderItem,
                    new QueryWrapper<OcBOrderItem>().eq("oc_b_order_id", afSend.getSourceBillNo()).eq("id", id));
        }
        // 自动生成卖家备注
        this.autoUpdateSellerRemark(afSend);

        //查询业务类型：原单业务类型对应的退款业务类型
        try{
            StCBusinessType stCBusinessType = omsRefundOrderService.queryRefundOrderType(ocBOrder);
            afSend.setBusinessTypeId(stCBusinessType.getId());
            afSend.setBusinessTypeCode(stCBusinessType.getEcode());
            afSend.setBusinessTypeName(stCBusinessType.getEname());
        }catch (Exception e){
            log.error(LogUtil.format("查询业务类型失败={}",
                    "RefundFormAfterDeliveryService"), Throwables.getStackTraceAsString(e));
        }
        buildCardAutoVoidStatus(afSend);
        // 保存
        afSendMapper.insert(afSend);
        afSendItemMapper.batchInsert(afSendItemList);

        // 手工新建的额外退款单，给订单表加是否包含额外退单的标签
        if (RefundOrderSourceTypeEnum.MANUAL.getValue().equals(afSend.getRefundOrderSourceType())) {
            // 更新订单
            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setId(ocBOrder.getId());
            updateOrder.setIsExtra(1);
            ocBOrderMapper.updateById(updateOrder);
            // 新增日志
            insertReturnAfSendLog("新增已发货退款单", "已发货退款单新增成功", null, user, afSend.getId());
        }
    }

    private boolean isCheckAmount(Long ocBReturnTypeId) {
        boolean checkAmount = true;
        if (ocBReturnTypeId != null) {
            OcBReturnType ocBReturnType = ocbReturnTypeMapper.selectById(ocBReturnTypeId);
            if (ocBReturnType != null && defaultReturnTypeCode.equals(ocBReturnType.getEcode())) {
                checkAmount = false;
            }
        }
        return checkAmount;
    }

    /**
     * 自动生成卖家备注
     */
    private void autoUpdateSellerRemark(OcBReturnAfSend afSend) {
        // 退款分类不存在，则不自动生成卖家备注
        if (afSend.getOcBReturnTypeId() == null) {
            return;
        }
        OcBReturnType ocBReturnType = ocbReturnTypeMapper.selectById(afSend.getOcBReturnTypeId());
        if (ocBReturnType == null) {
            throw new NDSException("退款分类不存在！");
        }
        // 获取支付类型
        String payTypeName = OcBReturnAfSendListEnums.PayTypeEnum.getTextByVal(afSend.getPayMode());
        // 判断金额
        if (afSend.getAmtReturnApply() == null) {
            afSend.setAmtReturnApply(new BigDecimal(0));
        }
        String sellerRemark = String.format("%s-%s，亲，您好！您的订单号%s因为%s原因，给您申请%s元的额外退款，" +
                        "我们将会在 3 个工作日之内给您打到您%s%s中，请您注意查收！感谢您的惠顾，欢迎下次光临，祝您生活愉快！",
                afSend.getVipNick(), afSend.getVipPhone() == null ? "" : afSend.getVipPhone(), afSend.getTid(),
                ocBReturnType.getEname(),
                afSend.getAmtReturnApply(), payTypeName, afSend.getPayAccount());
        afSend.setSellerRemark(sellerRemark);
    }

    /**
     * 发货后退单复制
     *
     * @param param
     * @param user
     * @return
     */
    public ValueHolderV14 copyAfterDeliver(JSONObject param, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (param.isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }
        if (!param.containsKey("ID") && param.getJSONObject("ID").isEmpty()) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("传入的参数不能为空");
            return vh;
        }
        Long id = param.getLong("ID");
        OcBReturnAfSend afSend = afSendMapper.selectById(id);
        if (afSend == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("查询不到数据");
            return vh;
        }
        List<OcBReturnAfSendItem> afSendItemList = afSendItemMapper.selectList(new QueryWrapper<OcBReturnAfSendItem>().
                eq("oc_b_return_af_send_id", id));
       /* if (CollectionUtils.isEmpty(afSendItemList)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("查询不到数据");
            return vh;
        }*/
        JSONObject packageParam = new JSONObject();
        packageParam.put("AfSend", afSend);
        packageParam.put("AfSendItemList", afSendItemList);
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("查询成功");
        vh.setData(packageParam);
        return vh;
    }

    /**
     * 导入模板下载
     *
     * @param usr
     * @return
     */
    public ValueHolderV14 afterDeliverImportDownload(User usr) {

        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "已发货退款单导入模板下载成功！");
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(timeout);
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String[] orderNames = {"原始订单编号", "店铺名称", "原始平台单号", "平台退款单号", "退款类型", "买家昵称", "退款原因", "支付方式",
                "支付账号", "退款金额", "判责方", "判责方备注", "备注", "头子表关联列"};

        /**
         *  拼接Excel明细表sheet表头字段
         * */
        String[] itemNames = {"SKU编码", "退款金额", "运费", "头子表关联列"};
        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List orderN = Lists.newArrayList(orderNames);
        List itemN = Lists.newArrayList(itemNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "已发货退款单主表", "", orderN, Lists.newArrayList(), Lists.newArrayList(),
                false);
        exportUtil.executeSheet(hssfWorkbook, "已发货退款单明细", "", itemN, Lists.newArrayList(), Lists.newArrayList(), false);
        String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "已发货退款单导入模板", user, "OSS-Bucket/EXPORT" +
                "/OC_B_RETURN_AF_SEND/");
        holderV14.setData(sdd);
        return holderV14;

    }

    public ValueHolderV14 importList(List<OcBReturnAfSendExtend> afSendList,
                                     List<OcBReturnAfSendItemExtend> afSendItemExtendList, User user) {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "发货后退款单导入成功！");
        //匹配主表和明细表数据，校验必传字段
        if (!OcBReturnAfSendExtend.dealBK(afSendList, afSendItemExtendList)) {
            holderV14.setCode(ImportUtil.IMPORT_ERROR_CODE);
            holderV14.setMessage("发货后退款单导入模板存在错误数据!");
        }
        if (holderV14.getCode() == ResultCode.SUCCESS && !OcBReturnAfSendExtend.checkForImport(afSendList)) {
            holderV14.setCode(ImportUtil.IMPORT_ERROR_CODE);
            holderV14.setMessage("发货后退款单导入模板存在错误数据!");
        }
        // 判断退款金额是否超20
        if (holderV14.getCode() == ResultCode.SUCCESS && !this.checkOrderAmount(afSendList)) {
            holderV14.setCode(ImportUtil.IMPORT_ERROR_CODE);
            holderV14.setMessage("此平台单号关联的退款单退款金额大于原始平台单号订单明细成交金额+20，不允许保存!");
        }

        //执行插入
        if (holderV14.getCode() == ResultCode.SUCCESS) {
            RefundFormAfterDeliveryService bean =
                    ApplicationContextHandle.getBean(RefundFormAfterDeliveryService.class);
            for (int i = 0; i < afSendList.size(); i++) {
                OcBReturnAfSendExtend afSendExtend = afSendList.get(i);
                try {
                    // 组装参数
                    JSONObject object = PackageData(afSendExtend);
                    // 调用保存接口
                    ValueHolderV14 v14 = bean.saveAfterDeliver(object, user);
                    if (ResultCode.FAIL == v14.getCode()) {
                        afSendExtend.setDesc(v14.getMessage());
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("发货后退款单新增异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    afSendExtend.setDesc(e.toString());
                    holderV14.setMessage("发货后退款单新增异常!");
                    holderV14.setCode(ImportUtil.IMPORT_ERROR_CODE);
                }
            }
        }
        try {
            //校验 holderV14
            if (holderV14.getCode() == ImportUtil.IMPORT_ERROR_CODE) {
                // 需返回结果集
                List<OcBReturnAfSendExtend> detailsList = (List<OcBReturnAfSendExtend>) holderV14.getData();
                List<OcBReturnAfSendExtend> errorList =
                        detailsList.parallelStream().filter(x -> x.getDesc() != null).collect(Collectors.toList());
                // 列名
                String[] columnNames = {"主表行号", "错误原因"};
                List c = Lists.newArrayList(columnNames);
                // map中的key
                String[] keys = {"rowNum", "desc"};
                List k = Lists.newArrayList(keys);
                exportUtil.setEndpoint(this.endpoint);
                exportUtil.setAccessKeyId(this.accessKeyId);
                exportUtil.setAccessKeySecret(this.accessKeySecret);
                exportUtil.setBucketName(this.bucketName);
                if (StringUtils.isEmpty(timeout)) {
                    //如果获取不到apllo配置参数，设置默认过期时间为30分钟
                    timeout = "1800000";
                }
                exportUtil.setTimeout(this.timeout);
                Workbook hssfWorkbook = exportUtil.execute("已发货后退款单", "已发货后退款单", c, k, errorList);
                String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "已发货后退款单导入错误信息", user, "OSS-Bucket/IMPORT" +
                        "/OC_B_RETURN_AF_SEND/");
                holderV14.setData(sdd);
                return holderV14;
            } else {
                return holderV14;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("返回已发货后退款单导入错误,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage(e.getMessage());
            e.printStackTrace();
            return holderV14;
        }
    }

    /**
     * 检查退款金额是否超过原订单明细金额+20
     */
    private boolean checkOrderAmount(List<OcBReturnAfSendExtend> afSendList) {
        for (OcBReturnAfSendExtend ocBReturnAfSendExtend : afSendList) {
            String tid = ocBReturnAfSendExtend.getTid();
            BigDecimal currentReturnAmt = BigDecimal.ZERO;
            BigDecimal originOrderAmt = BigDecimal.ZERO;
            // 计算当前导入的退款金额
            for (OcBReturnAfSendItemExtend ocBReturnAfSendItemExtend :
                    ocBReturnAfSendExtend.getAfSendItemExtendList()) {
                currentReturnAmt = currentReturnAmt.add(ocBReturnAfSendItemExtend.getAmtReturn());
            }
            // 累计已存在的退款金额
            List<OcBReturnAfSend> ocBReturnAfSendList =
                    afSendMapper.selectList(new QueryWrapper<OcBReturnAfSend>().in("tid", tid));
            List<Long> ocBReturnAfSendIdList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(ocBReturnAfSendList)) {
                ocBReturnAfSendList.forEach(obj -> ocBReturnAfSendIdList.add(obj.getId()));
                List<OcBReturnAfSendItem> ocBReturnAfSendItemList =
                        afSendItemMapper.selectByOcBReturnAfSendIdList(ocBReturnAfSendIdList);
                if (CollectionUtils.isNotEmpty(ocBReturnAfSendItemList)) {
                    for (OcBReturnAfSendItem ocBReturnAfSendItem : ocBReturnAfSendItemList) {
                        currentReturnAmt = currentReturnAmt.add(ocBReturnAfSendItem.getAmtReturn());
                    }
                }
            }
            // 计算原平台订单明细金额
            List<OcBOrderItem> ocBOrderList = ocBOrderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().in("ooid"
                    , tid));
            if (CollectionUtils.isNotEmpty(ocBOrderList)) {
                for (OcBOrderItem ocBOrderItem : ocBOrderList) {
                    originOrderAmt = originOrderAmt.add(ocBOrderItem.getRealAmt());
                }
            }
            // 判断退款金额是否超20
            if (currentReturnAmt.subtract(originOrderAmt).compareTo(new BigDecimal(20)) > 0) {
                return false;
            }
        }
        return true;
    }

    private JSONObject PackageData(OcBReturnAfSendExtend afSendExtend) {
        OcBOrder ocBOrder = ocBOrderMapper.selectById(afSendExtend.getSourceBillNo());
        if (ocBOrder == null) {
            throw new NDSException("导入的" + afSendExtend.getSourceBillNo() + "原始订单号有误");
        }
        JSONObject object = new JSONObject();
        object.put("objId", -1);
        JSONObject afSennd = new JSONObject();
        afSennd.put("BILL_TYPE", afSendExtend.getBillType());
        afSennd.put("SOURCE_BILL_NO", afSendExtend.getSourceBillNo());
        afSennd.put("CP_C_SHOP_ECODE", ocBOrder.getCpCShopEcode());
        afSennd.put("CP_C_SHOP_ID", ocBOrder.getCpCShopId());
        afSennd.put("CP_C_SHOP_TITLE", ocBOrder.getCpCShopTitle());
        afSennd.put("TID", afSendExtend.getTid());
        afSennd.put("VIP_NICK", afSendExtend.getVipNick());
        afSennd.put("T_RETURN_ID", afSendExtend.getTReturnId());
        afSennd.put("REASON", afSendExtend.getReason());
        afSennd.put("PAY_MODE", afSendExtend.getPayMode());
        afSennd.put("PAY_ACCOUNT", afSendExtend.getPayAccount());
        afSennd.put("AMT_RETURN_APPLY", afSendExtend.getAmtReturnApply());
        afSennd.put("RESPONSIBLE_PARTY", afSendExtend.getResponsibleParty());
        afSennd.put("RESPONSIBLE_PARTY_REMARK", afSendExtend.getResponsiblePartyRemark());
        afSennd.put("REMARK", afSendExtend.getRemark());

        object.put("AfSend", afSennd);
        JSONArray array = new JSONArray();
        List<OcBReturnAfSendItemExtend> afSendItemExtendList = afSendExtend.getAfSendItemExtendList();
        for (OcBReturnAfSendItemExtend returnAfSendItemExtend : afSendItemExtendList) {
            OcBOrderItem ocBOrderItem = ocBOrderItemMapper.selectOne(new QueryWrapper<OcBOrderItem>().eq(
                    "oc_b_order_id", afSendExtend.getSourceBillNo()).
                    eq("ps_c_sku_ecode", returnAfSendItemExtend.getPsCSkuEcode()));
            if (ocBOrderItem == null) {
                throw new NDSException("在原单单号" + afSendExtend.getSourceBillNo() + "查询不到条码为：" + returnAfSendItemExtend.getPsCSkuEcode() + "数据");
            }

            JSONObject item = new JSONObject();
            item.put("id", ocBOrderItem.getId());
            item.put("AMT_RETURN", returnAfSendItemExtend.getAmtReturn());
            item.put("FREIGHT", returnAfSendItemExtend.getFreight());
            array.add(item);
        }
        object.put("AfSendItem", array);
        return object;
    }
}
