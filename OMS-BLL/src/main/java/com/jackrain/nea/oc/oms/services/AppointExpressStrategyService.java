package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.date.DateUtil;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderAppointLogisticsMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderAppointLogistics;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.model.StCAppointExpressStrategy;
import com.jackrain.nea.st.model.StCAppointExpressStrategyDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @ClassName AppointExpressStrategyService
 * @Description 指定快递策略
 * <AUTHOR>
 * @Date 2024/4/15 15:27
 * @Version 1.0
 */
@Slf4j
@Component
public class AppointExpressStrategyService {

    @Autowired
    private StCAppointExpressStrategyService strategyService;
    @Autowired
    private StCAppointExpressStrategyDetailServiceImpl detailService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBOrderAppointLogisticsMapper appointLogisticsMapper;
    @Autowired
    private OcBOrderAppointLogisticsMapperService appointLogisticsMapperService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    public void getAppointExpressStrategyRelation(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem) {

        // 如果匹配过快递策略，则直接返回
        List<OcBOrderAppointLogistics> appointLogisticsList = appointLogisticsMapperService.selectByOrderId(ocBOrder.getId());
        if (CollectionUtils.isNotEmpty(appointLogisticsList)) {
            return;
        }
        Long shopId = ocBOrder.getCpCShopId();
        // 根据订单明细中的平台商品id获取指定快递策略
        StCAppointExpressStrategy stCAppointExpressStrategy = strategyService.getByShopId(shopId);
        if (stCAppointExpressStrategy == null) {
            stCAppointExpressStrategy = strategyService.getCommonStrategy();
        }
        if (stCAppointExpressStrategy == null) {
            return;
        }
        Long strategyId = stCAppointExpressStrategy.getId();
        List<StCAppointExpressStrategyDetail> detailList = detailService.selectByStrategyId(strategyId);
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        String numIid = ocBOrderItem.getNumIid();
        for (StCAppointExpressStrategyDetail detail : detailList) {
            if (detail.getMatchContent().equals(numIid)) {
                CpLogistics cpLogistics = cpRpcService.cpLogisticsInfo(detail.getCpCLogisticsId());
                if (cpLogistics != null) {
                    ocBOrder.setAppointLogisticsId(cpLogistics.getId());
                    ocBOrder.setAppointLogisticsEcode(cpLogistics.getEcode());
                    ocBOrder.setAppointLogisticsEname(cpLogistics.getEname());
                    ocBOrder.setModifieddate(new Date());
                    ocBOrderMapper.updateById(ocBOrder);
                    // 增加操作日志
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.AUTO_APPOINT_LOGISTICS.getKey(),
                            "匹配到自动制定快递策略,策略编码:" + stCAppointExpressStrategy.getStrategyCode() + ",匹配内容:" + detail.getMatchContent(),
                            "", "", SystemUserResource.getRootUser());
                    // 记录一下命中的记录
                    OcBOrderAppointLogistics appointLogistics = new OcBOrderAppointLogistics();
                    appointLogistics.setOrderId(ocBOrder.getId());
                    appointLogistics.setCpCLogisticsId(detail.getCpCLogisticsId());
                    appointLogistics.setCreationdate(new Date());
                    Long id = ModelUtil.getSequence("OC_B_ORDER_APPOINT_LOGISTICS");
                    appointLogistics.setId(id);
                    // 策略里面配置的秒 加上当前时间 得到取消的时间
                    Long ageing = Long.valueOf(detail.getAgeing());
                    if (ageing < 0) {
                        appointLogistics.setCancelAppointTime(DateUtil.parse("2099-12-31 00:00:00"));
                    } else {
                        appointLogistics.setCancelAppointTime(new Date(System.currentTimeMillis() + ageing * 1000));
                    }
                    appointLogistics.setIsactive("Y");
                    appointLogisticsMapper.insert(appointLogistics);
                    return;
                }
            }
        }
    }
}
