package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.AcFPriceSettingMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.AcFPriceSetting;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * className: AcFPriceSettingService
 * description:
 *
 * <AUTHOR>
 * create: 2021-10-26
 * @since JDK 1.8
 */
@Component
@Slf4j
public class AcFPriceSettingService {

    @Autowired
    private AcFPriceSettingMapper priceSettingMapper;

    @Autowired
    private AcFPriceSettingLogService logService;

    @Autowired
    private PsRpcService psRpcService;

    public ValueHolder save(AcFPriceSetting priceSetting,User user) {

        Long id = priceSetting.getId();
        ValueHolder valueHolder = ValueHolderUtils.success("保存成功");
        try {

            //转换商品信息
            if(!StringUtils.isEmpty(priceSetting.getPsCProEcode())){
                List<PsCPro> psCPros = psRpcService.queryProByEcode(Collections.singletonList(priceSetting.getPsCProEcode()));
                if(CollectionUtils.isEmpty(psCPros)){
                    log.error(" 保存全渠道价格配置，商品信息不存在，{}", JSON.toJSONString(priceSetting));
                } else {
                    PsCPro psCPro = psCPros.get(0);
                    priceSetting.setPsCProId(psCPro.getId());
                    priceSetting.setPsCProEname(psCPro.getEname());
                }
            }

            //框架字段赋值
            CommandAdapterUtil.defaultOperator(priceSetting,user);
            AcFPriceSetting old = null;
            if (id < 0) {
                //新增
                id = ModelUtil.getSequence(OcCommonConstant.AC_F_PRICE_SETTING);
                priceSetting.setId(id);
                priceSetting.setOwnerename(user.getEname());
                priceSetting.setModifierename(user.getEname());
                priceSettingMapper.insert(priceSetting);
            }else {
                old = priceSettingMapper.selectById(priceSetting.getId());
                if(ObjectUtils.isEmpty(old)){
                    return ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
                }

                //更新
                priceSetting.setModifierename(user.getEname());
                priceSettingMapper.updateById(priceSetting);
            }

            //记录日志
            logService.saveLog(priceSetting,old,user);

        }catch (Exception e){
            log.error(" 全渠道价格配置保存异常：{}", Throwables.getStackTraceAsString(e));
            valueHolder = ValueHolderUtils.getFailValueHolder(e.getMessage());
        }
        return valueHolder;
    }
}
