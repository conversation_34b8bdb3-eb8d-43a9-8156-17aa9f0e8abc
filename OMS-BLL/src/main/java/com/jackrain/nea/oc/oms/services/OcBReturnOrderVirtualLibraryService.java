package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.*;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 退换货订单修改 -> 虚拟入库
 *
 * @date 2019/3/25
 * @author: ming.fz
 */
@Component
@Slf4j
public class OcBReturnOrderVirtualLibraryService {

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    OmsOrderLogService omsOrderLogService;

    @Autowired
    OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    @Autowired
    ReturnOrderAuditService returnOrderAuditService;

    @Autowired
    ReturnOrderLogService returnOrderLogService;

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private ThreadPoolTaskExecutor createReturnVirtualLibraryThreadPoolExecutor;

    String threadPoolName = "R3_OMS_CREATE_VIRTUALLIBRARYSERVICE_THREAD_POOL_%d";

    /**
     * 虚拟入库
     *
     * @param id   退换货单编号
     * @param user 操作用户
     * @return 入库结果
     */
    public ValueHolderV14 updateReturnInfo(Long id, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBReturnOrderVirtualLibraryService.ReturnOrderId=", id));
        }
        AssertUtil.assertException(id == null || id < 1, "虚拟入库失败, 退换货单编号参数异常");

        String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {

                // 1. 查询
                OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectById(id);
                // 2. 校验
                checkParam(returnOrder, user);
                // 3. 入库
                OcBReturnOrderVirtualLibraryService bean =
                        ApplicationContextHandle.getBean(OcBReturnOrderVirtualLibraryService.class);
                List<Long> longs = bean.virtualStockIn(returnOrder, user);
                insertReturnOrderLog(id, "释放Hold单成功, 释放换货订单编号: " + JSON.toJSONString(longs), user);
                insertReturnOrderLog(id, "虚拟入库成功", user);
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("虚拟入库成功");
            } else {
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("当前单据正在操作, 请稍后再试");
            }
        } catch (Exception ex) {
            insertReturnOrderLog(id, ex.getMessage(), user);
            log.error(LogUtil.format("OcBReturnOrderVirtualLibraryService.exp error：{}"),
                    Throwables.getStackTraceAsString(ex));
            vh.setMessage(ex.getMessage());
            vh.setCode(ResultCode.FAIL);
        } finally {
            redisLock.unlock();
        }
        return vh;

    }

    /**
     * 虚拟入库
     *
     * @param ids   退换货单编号
     * @param user 操作用户
     * @return 入库结果
     */
    public ValueHolderV14 batchUpdateReturnInfo(JSONArray ids, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.multiFormat("OcBReturnOrderVirtualLibraryService.batchUpdateReturnInfo 参数：", ids));
        }

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS,"success");
        int successNum = 0;
        int errorNum = 0;
        //虚拟入库失败退单编号集
        List<Long> errorList = new ArrayList<>();
        try{
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            List<Integer> idList = new ArrayList<>();
            for (int i = 0; i < ids.size(); i++) {
                idList.add(ids.getIntValue(i));
            }
            List<OcBReturnOrder> returnOrderList = ocBReturnOrderMapper.selectBatchIds(idList);
            if (CollectionUtils.isNotEmpty(returnOrderList)){
                int splitNum = config.getProperty("updateReturnInfo.split.num", 5);
                int tmpNum = returnOrderList.size() / splitNum;//每个小list分的个数
                if (returnOrderList.size() % splitNum != 0) {
                    tmpNum = tmpNum + 1;
                }
                List<List<OcBReturnOrder>> partition = SplitListUtil.partition(returnOrderList, tmpNum);
                List<Future<List<ValueHolderV14>>> results =
                        new ArrayList<Future<List<ValueHolderV14>>>();
                for (int i = 0; i < partition.size(); i++) {
                    results.add(createReturnVirtualLibraryThreadPoolExecutor.submit(new OcBReturnOrderVirtualLibraryService.CallableVirtualLibrary(partition.get(i), user)));
                }
                //线程执行结果获取
                for (Future<List<ValueHolderV14>> futureResult : results) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("batchUpdateReturnInfo------>线程结果:{}"),
                                JSON.toJSONString(futureResult));
                    }
                    List<ValueHolderV14> valueHolderV14List = futureResult.get();
                    for (ValueHolderV14 valueHolderV14 : valueHolderV14List){
                        int code = valueHolderV14.getCode();
                        if (ResultCode.SUCCESS == code){
                            successNum++;
                        }else {
                            errorNum++;
                            errorList.add((Long) valueHolderV14.getData());
                        }
                    }
                }
            }
        }catch (InterruptedException e) {
            log.warn(LogUtil.format("Thread batchUpdateReturnInfo InterruptedException：{},threadPoolName=",
                    threadPoolName), Throwables.getStackTraceAsString(e));
        }catch (ExecutionException e){
            log.error(LogUtil.format("OcBReturnOrderVirtualLibraryService,虚拟入库异常,error:{}"),
                    Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("虚拟入库异常！");
            return v14;
        }
        v14.setCode(ResultCode.SUCCESS);
        if (CollectionUtils.isNotEmpty(errorList)){
            v14.setMessage(String.format("执行成功记录数：%s，执行失败记录数：%s，失败记录退单编号：%s", successNum, errorNum,errorList));
        }else {
            v14.setMessage(String.format("执行成功记录数：%s，执行失败记录数：%s", successNum, errorNum));
        }
        return v14;
    }

    /**
     * 退换货订单修改 -> 虚拟入库服务
     *
     * @param user
     * @return
     * @throws NDSException
     */
    @Transactional
    public List<Long> virtualStockIn(OcBReturnOrder returnOrder, User user) {

        // 1. 更新状态
        OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
        //去除此处状态更新 在入库结果回传后统一更新状态
//        updateOcBReturnOrder.setReturnStatus(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
        updateOcBReturnOrder.setInventedStatus(VirtualInStatusEnum.NOT.toInt());
        updateOcBReturnOrder.setModifierename(user.getEname());
        updateOcBReturnOrder.setModifieddate(new Date(System.currentTimeMillis()));
        updateOcBReturnOrder.setId(returnOrder.getId());
        // @20200822 传AC标识状态更新
        updateOcBReturnOrder.setToSettleStatus(ToACStatusEnum.PENDING.getLongValue());
        int updateResult = ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
        AssertUtil.assertException(updateResult < 1, "虚拟入库失败, 更新数据结果异常");

        // 2. hold单释放
        List<Long> exchangeOrderIds = ES4Order.getIdsByOrigReturnOrderId(returnOrder.getId());
        AssertUtil.assertException(CollectionUtils.isEmpty(exchangeOrderIds), "未查询到换货订单, 不允许入库");
        for (Long id : exchangeOrderIds) {
            OcBOrder tmpOrder = new OcBOrder();
            tmpOrder.setId(id);
            tmpOrder.setIsInterecept(OmsOrderIsInterceptEnum.NO.getVal());
            try {
                ValueHolder r = ocBOrderHoldService.holdOrUnHoldOrder(tmpOrder, OrderHoldReasonEnum.RETURN_VIRTUAL_IN);
                AssertUtil.assertException(!r.isOK(), "释放Hold单失败, 换货订单编号: " + id);
            } catch (Exception ex) {
                String errorMsg = ExceptionUtil.getMessage(ex);
                log.error(LogUtil.format("虚拟入库, 释放hold单异常: {}"), errorMsg);
                errorMsg = errorMsg.length() > 1000 ? errorMsg.substring(0, 1000) : errorMsg;
                AssertUtil.assertException(true, errorMsg);
            }
        }
        return exchangeOrderIds;
    }

    /**
     * 添加退换货订单操作日志
     *
     * @param id   退单编号
     * @param msg  日志信息
     * @param user 操作用户
     */
    private void insertReturnOrderLog(Long id, String msg, User user) {
        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
        ocBReturnOrderLog.setLogType("虚拟入库");
        ocBReturnOrderLog.setLogMessage(msg);
        ocBReturnOrderLog.setLogParam("");
        ocBReturnOrderLog.setIpAddress(user.getLastloginip());
        ocBReturnOrderLog.setUserName(user.getName());
        ocBReturnOrderLog.setOwnerename(user.getName());
        ocBReturnOrderLog.setOcBReturnOrderId(id);
        ocBReturnOrderLog.setId(ModelUtil.getSequence("oc_b_return_order_log"));
        ocBReturnOrderLog.setAdOrgId(new Long(user.getOrgId()));
        ocBReturnOrderLog.setAdClientId(new Long(user.getClientId()));
        BaseModelUtil.makeBaseCreateField(ocBReturnOrderLog, user);
        ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
    }

    /**
     * 参数校验
     *
     * @param order
     * @param user
     */
    private void checkParam(OcBReturnOrder order, User user) {

        if (order == null) {
            throw new NDSException(Resources.getMessage("当前记录在退换货订单表中不存在！", user.getLocale()));
        }

        Integer returnStatus = order.getReturnStatus();
        if (returnStatus == null || !(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus))) {
            throw new NDSException(Resources.getMessage("非待退货入库状态, 不允许虚拟入库！", user.getLocale()));
        }

        if (!OcReturnBillTypeEnum.EXCHANGE.getVal().equals(order.getBillType())) {
            throw new NDSException(Resources.getMessage("非换货类型退换货单,不允许虚拟入库", user.getLocale()));
        }
       /* List<OcBReturnOrderRefund> ocBReturnOrderRefunds = ocBReturnOrderRefundMapper.selectPrentIdByAll(id);
        if (ocBReturnOrderRefunds == null || ocBReturnOrderRefunds.size() < 1) {
            throw new NDSException(Resources.getMessage(id + "当前退换货订单没有退单商品！", user.getLocale()));
        }
        return ocBReturnOrderRefunds;*/

    }

    public List<ValueHolderV14> updateReturnInfo(List<OcBReturnOrder> returnOrderList, User user) {

        List<ValueHolderV14> valueHolderV14List = new ArrayList<>();

        for (OcBReturnOrder returnOrder : returnOrderList){
            ValueHolderV14 vh = new ValueHolderV14();
            Long id = returnOrder.getId();
            String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    // 2. 校验
                    checkParam(returnOrder, user);
                    // 3. 入库
                    OcBReturnOrderVirtualLibraryService bean =
                            ApplicationContextHandle.getBean(OcBReturnOrderVirtualLibraryService.class);
                    List<Long> longs = bean.virtualStockIn(returnOrder, user);
                    insertReturnOrderLog(id, "释放Hold单成功, 释放换货订单编号: " + JSON.toJSONString(longs), user);
                    insertReturnOrderLog(id, "虚拟入库成功", user);
                    vh.setCode(ResultCode.SUCCESS);
                    vh.setMessage("虚拟入库成功");
                } else {
                    vh.setCode(ResultCode.SUCCESS);
                    vh.setMessage("当前单据" + returnOrder.getBillNo()  + "正在操作, 请稍后再试");
                }
            } catch (Exception ex) {
                insertReturnOrderLog(id, ex.getMessage(), user);
                log.error(LogUtil.format("OcBReturnOrderVirtualLibraryService.error:{}"), Throwables.getStackTraceAsString(ex));
                vh.setMessage(ex.getMessage());
                vh.setData(id);
                vh.setCode(ResultCode.FAIL);
            } finally {
                redisLock.unlock();
                valueHolderV14List.add(vh);
            }
        }
        return valueHolderV14List;

    }


    class CallableVirtualLibrary implements Callable<List<ValueHolderV14>> {
        private List<OcBReturnOrder> returnOrderList;
        private User user;

        public CallableVirtualLibrary(List<OcBReturnOrder> ocBReturnOrders,User logUser) {
            this.returnOrderList = ocBReturnOrders;
            this.user = logUser;
        }
        @Override
        public List<ValueHolderV14> call() throws Exception {
            List<ValueHolderV14> valueHolderV14List = new ArrayList<>();
            //更新中间表 出库通知单
            List<ValueHolderV14> valueHolderV14List1 = updateReturnInfo(returnOrderList, user);
            valueHolderV14List.addAll(valueHolderV14List1);
            return valueHolderV14List;
        }
    }
}

