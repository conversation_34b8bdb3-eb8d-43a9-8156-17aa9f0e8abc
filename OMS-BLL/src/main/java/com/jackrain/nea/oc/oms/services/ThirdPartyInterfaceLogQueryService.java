package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.IpBThirdPartyInterfaceLogMapper;
import com.jackrain.nea.oc.oms.model.table.IpBThirdPartyInterfaceLog;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ThirdPartyInterfaceLogQueryService
 * @Description 第三方接口日志查询服务
 * <AUTHOR>
 * @Date 2025/4/22 20:20
 * @Version 1.0
 */
@Slf4j
@Service
public class ThirdPartyInterfaceLogQueryService {

    @Autowired
    private IpBThirdPartyInterfaceLogMapper ipBThirdPartyInterfaceLogMapper;

    /**
     * 框架格式返回
     *
     * @param dataList 原始数据列表
     * @return 格式化后的数据数组
     */
    private JSONArray getFrameDataFormat(List<JSONObject> dataList) {
        JSONArray array = new JSONArray();
        if (dataList != null && dataList.size() > 0) {
            for (JSONObject emp : dataList) {
                JSONObject json = new JSONObject();

                // 遍历所有字段
                for (String key : emp.keySet()) {
                    JSONObject val = new JSONObject();
                    Object value = emp.get(key);

                    // 处理时间字段
                    if ("REQUEST_TIME".equalsIgnoreCase(key) || "RESPONSE_TIME".equalsIgnoreCase(key)) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        if (value instanceof Date) {
                            val.put("val", sdf.format((Date) value));
                        } else if (value instanceof Long) {
                            // 处理时间戳
                            val.put("val", sdf.format(new Date((Long) value)));
                        } else if (value instanceof String && !((String) value).isEmpty()) {
                            try {
                                // 尝试将字符串解析为时间戳
                                long timestamp = Long.parseLong((String) value);
                                val.put("val", sdf.format(new Date(timestamp)));
                            } catch (NumberFormatException e) {
                                // 如果不是数字格式，直接返回原值
                                val.put("val", value);
                            }
                        } else {
                            val.put("val", value);
                        }
                    } else if (value instanceof Date) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        val.put("val", sdf.format((Date) value));
                    }
                    // 处理单据类型字段
                    else if ("RESERVE_BIGINT01".equalsIgnoreCase(key) && value != null) {
                        val.put("val", getBillTypeDesc(value.toString()));
                    }
                    // 处理接口类型字段
                    else if ("TYPE".equalsIgnoreCase(key) && value != null) {
                        val.put("val", getInterfaceTypeDesc(value.toString()));
                    }
                    // 处理调用结果字段
                    else if ("CODE".equalsIgnoreCase(key) && value != null) {
                        if ("0".equals(value.toString())) {
                            val.put("val", "成功");
                        } else {
                            val.put("val", "失败");
                        }
                    }
                    // 处理可用字段
                    else if ("ISACTIVE".equalsIgnoreCase(key) && value != null) {
                        if ("Y".equals(value.toString())) {
                            val.put("val", "是");
                        } else {
                            val.put("val", "否");
                        }
                    }
                    // 其他字段直接转换
                    else {
                        val.put("val", value);
                    }

                    json.put(key.toUpperCase(), val);
                }
                array.add(json);
            }
        }
        return array;
    }

    /**
     * 根据单据类型代码获取单据类型描述
     *
     * @param billTypeCode 单据类型代码
     * @return 单据类型描述
     */
    private String getBillTypeDesc(String billTypeCode) {
        switch (billTypeCode) {
            case "101":
                return "零售发货单";
            case "201":
                return "退换货单";
            case "301":
                return "杂费单";
            case "401":
                return "出库通知单";
            case "601":
                return "入库通知单";
            case "701":
                return "主数据";
            case "801":
                return "商品";
            case "901":
                return "仓内加工";
            case "501":
                return "唯品会退供PO单";
            case "150":
                return "其他入库单";
            case "160":
                return "其他出库单";
            case "6":
                return "库存调整单";
            case "15":
                return "逻辑调拨单";
            case "16":
                return "逻辑入库单";
            case "17":
                return "逻辑出库单";
            case "18":
                return "通用退单";
            case "19":
                return "通用订单";
            case "20":
                return "销售数据汇总";
            case "21":
                return "对账结算单";
            case "22":
                return "月结汇总单";
            case "241":
                return "冻结出库单";
            default:
                return billTypeCode;
        }
    }

    /**
     * 根据接口类型代码获取接口类型描述
     *
     * @param typeCode 接口类型代码
     * @return 接口类型描述
     */
    private String getInterfaceTypeDesc(String typeCode) {
        switch (typeCode) {
            case "1":
                return "WEBSERVICE";
            case "2":
                return "RESTFUL";
            case "3":
                return "RPC";
            case "4":
                return "MQ";
            default:
                return typeCode;
        }
    }

    /**
     * 查询第三方接口日志
     *
     * @param session 查询会话
     * @return 查询结果
     */
    public ValueHolder queryThirdPartyInterfaceLog(QuerySession session) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        log.info("查询第三方接口日志参数: {}", param.toJSONString());
        // 获取分页参数
        Integer startIndex = param.getInteger("startindex");
        Integer range = param.getInteger("range");
        try {

            if (startIndex == null) {
                startIndex = 0;
            }
            if (range == null) {
                range = 100;
            }

            // 构建查询条件
            LambdaQueryWrapper<IpBThirdPartyInterfaceLog> queryWrapper = new LambdaQueryWrapper<>();

            // 处理固定查询条件
            if (param.containsKey("fixedcolumns")) {
                JSONObject fixedColumns = param.getJSONObject("fixedcolumns");

                // 检查是否有业务单号
                boolean hasBillNo = false;
                String billNo = null;
                if (fixedColumns.containsKey("BILL_NO")) {
                    billNo = fixedColumns.getString("BILL_NO");
                    if (billNo != null && !billNo.isEmpty()) {
                        hasBillNo = true;
                        queryWrapper.eq(IpBThirdPartyInterfaceLog::getBillNo, billNo);
                    }
                }

                // 处理第三方平台名称查询
                if (fixedColumns.containsKey("THIRD_PARTY_NAME")) {
                    String thirdPartyName = fixedColumns.getString("THIRD_PARTY_NAME");
                    if (thirdPartyName != null && !thirdPartyName.isEmpty()) {
                        queryWrapper.eq(IpBThirdPartyInterfaceLog::getThirdPartyName, thirdPartyName);
                    }
                }

                // 处理单据类型查询
                if (fixedColumns.containsKey("RESERVE_BIGINT01")) {
                    Object billTypeObj = fixedColumns.get("RESERVE_BIGINT01");
                    if (billTypeObj instanceof JSONArray) {
                        JSONArray billTypeArray = fixedColumns.getJSONArray("RESERVE_BIGINT01");
                        if (billTypeArray != null && !billTypeArray.isEmpty()) {
                            // 如果有多个值，使用OR条件连接
                            List<String> eqBillTypes = new ArrayList<>();
                            List<String> neBillTypes = new ArrayList<>();

                            for (int i = 0; i < billTypeArray.size(); i++) {
                                String billTypeStr = billTypeArray.getString(i);
                                if (billTypeStr.startsWith("=")) {
                                    eqBillTypes.add(billTypeStr.substring(1));
                                } else if (billTypeStr.startsWith("!=")) {
                                    neBillTypes.add(billTypeStr.substring(2));
                                }
                            }

                            // 处理等于条件
                            if (!eqBillTypes.isEmpty()) {
                                queryWrapper.and(wrapper -> {
                                    for (int i = 0; i < eqBillTypes.size(); i++) {
                                        if (i == 0) {
                                            wrapper.eq(IpBThirdPartyInterfaceLog::getReserveBigint01, eqBillTypes.get(i));
                                        } else {
                                            wrapper.or().eq(IpBThirdPartyInterfaceLog::getReserveBigint01, eqBillTypes.get(i));
                                        }
                                    }
                                    return wrapper;
                                });
                            }

                            // 处理不等于条件
                            for (String neBillType : neBillTypes) {
                                queryWrapper.ne(IpBThirdPartyInterfaceLog::getReserveBigint01, neBillType);
                            }
                        }
                    } else if (billTypeObj instanceof String) {
                        String billTypeStr = fixedColumns.getString("RESERVE_BIGINT01");
                        if (billTypeStr.startsWith("=")) {
                            queryWrapper.eq(IpBThirdPartyInterfaceLog::getReserveBigint01, billTypeStr.substring(1));
                        } else if (billTypeStr.startsWith("!=")) {
                            queryWrapper.ne(IpBThirdPartyInterfaceLog::getReserveBigint01, billTypeStr.substring(2));
                        }
                    }
                }

                // 处理接口名查询
                if (fixedColumns.containsKey("METHOD")) {
                    String method = fixedColumns.getString("METHOD");
                    if (method != null && !method.isEmpty()) {
                        queryWrapper.eq(IpBThirdPartyInterfaceLog::getMethod, method);
                    }
                }

                // 处理CODE查询
                if (fixedColumns.containsKey("CODE")) {
                    Object codeObj = fixedColumns.get("CODE");
                    if (codeObj instanceof JSONArray) {
                        JSONArray codeArray = fixedColumns.getJSONArray("CODE");
                        if (codeArray != null && !codeArray.isEmpty()) {
                            // 如果有多个值，使用OR条件连接
                            List<Integer> eqCodes = new ArrayList<>();
                            List<Integer> neCodes = new ArrayList<>();

                            for (int i = 0; i < codeArray.size(); i++) {
                                String codeStr = codeArray.getString(i);
                                if (codeStr.startsWith("=")) {
                                    eqCodes.add(Integer.parseInt(codeStr.substring(1)));
                                } else if (codeStr.startsWith("!=")) {
                                    neCodes.add(Integer.parseInt(codeStr.substring(2)));
                                }
                            }

                            // 处理等于条件
                            if (!eqCodes.isEmpty()) {
                                queryWrapper.and(wrapper -> {
                                    for (int i = 0; i < eqCodes.size(); i++) {
                                        if (i == 0) {
                                            wrapper.eq(IpBThirdPartyInterfaceLog::getCode, eqCodes.get(i));
                                        } else {
                                            wrapper.or().eq(IpBThirdPartyInterfaceLog::getCode, eqCodes.get(i));
                                        }
                                    }
                                    return wrapper;
                                });
                            }

                            // 处理不等于条件
                            for (Integer neCode : neCodes) {
                                queryWrapper.ne(IpBThirdPartyInterfaceLog::getCode, neCode);
                            }
                        }
                    } else if (codeObj instanceof String) {
                        String codeStr = fixedColumns.getString("CODE");
                        if (codeStr.startsWith("=")) {
                            queryWrapper.eq(IpBThirdPartyInterfaceLog::getCode, Integer.parseInt(codeStr.substring(1)));
                        } else if (codeStr.startsWith("!=")) {
                            queryWrapper.ne(IpBThirdPartyInterfaceLog::getCode, Integer.parseInt(codeStr.substring(2)));
                        }
                    }
                }

                // 处理接口类型查询
                if (fixedColumns.containsKey("TYPE")) {
                    Object typeObj = fixedColumns.get("TYPE");
                    if (typeObj instanceof JSONArray) {
                        JSONArray typeArray = fixedColumns.getJSONArray("TYPE");
                        if (typeArray != null && !typeArray.isEmpty()) {
                            // 如果有多个值，使用OR条件连接
                            List<String> eqTypes = new ArrayList<>();
                            List<String> neTypes = new ArrayList<>();

                            for (int i = 0; i < typeArray.size(); i++) {
                                String typeStr = typeArray.getString(i);
                                if (typeStr.startsWith("=")) {
                                    eqTypes.add(typeStr.substring(1));
                                } else if (typeStr.startsWith("!=")) {
                                    neTypes.add(typeStr.substring(2));
                                }
                            }

                            // 处理等于条件
                            if (!eqTypes.isEmpty()) {
                                queryWrapper.and(wrapper -> {
                                    for (int i = 0; i < eqTypes.size(); i++) {
                                        if (i == 0) {
                                            wrapper.eq(IpBThirdPartyInterfaceLog::getType, eqTypes.get(i));
                                        } else {
                                            wrapper.or().eq(IpBThirdPartyInterfaceLog::getType, eqTypes.get(i));
                                        }
                                    }
                                    return wrapper;
                                });
                            }

                            // 处理不等于条件
                            for (String neType : neTypes) {
                                queryWrapper.ne(IpBThirdPartyInterfaceLog::getType, neType);
                            }
                        }
                    } else if (typeObj instanceof String) {
                        String typeStr = fixedColumns.getString("TYPE");
                        if (typeStr.startsWith("=")) {
                            queryWrapper.eq(IpBThirdPartyInterfaceLog::getType, typeStr.substring(1));
                        } else if (typeStr.startsWith("!=")) {
                            queryWrapper.ne(IpBThirdPartyInterfaceLog::getType, typeStr.substring(2));
                        }
                    }
                }

                // 处理请求时间范围查询
                if (fixedColumns.containsKey("REQUEST_TIME")) {
                    String requestTimeRange = fixedColumns.getString("REQUEST_TIME");
                    if (requestTimeRange != null && !requestTimeRange.isEmpty() && requestTimeRange.contains("~")) {
                        String[] timeRange = requestTimeRange.split("~");
                        if (timeRange.length == 2) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                            try {
                                Date startTime = sdf.parse(timeRange[0].trim());
                                Date endTime = sdf.parse(timeRange[1].trim());

                                // 如果没有业务单号，需要检查时间范围不能超过一天
                                if (!hasBillNo) {
                                    // 计算时间差值（毫秒）
                                    long timeDiff = endTime.getTime() - startTime.getTime();
                                    // 24小时 = 24 * 60 * 60 * 1000 = 86400000毫秒
                                    if (timeDiff > 86400000 * 3) {
                                        JSONObject resultData = new JSONObject();
                                        resultData.put("start", startIndex);
                                        resultData.put("rowCount", range);
                                        resultData.put("row", new JSONArray());
                                        resultData.put("totalRowCount", 0);
                                        vh.put("code", ResultCode.FAIL);
                                        vh.put("message", "查询时间范围不能超过三天，除非指定业务单号");
                                        vh.put("data", resultData);
                                        return vh;
                                    }
                                }

                                queryWrapper.ge(IpBThirdPartyInterfaceLog::getRequestTime, startTime);
                                queryWrapper.le(IpBThirdPartyInterfaceLog::getRequestTime, endTime);
                            } catch (Exception e) {
                                log.error("解析时间范围失败: {}", requestTimeRange, e);
                            }
                        }
                    }
                } else if (!hasBillNo) {
                    // 如果没有业务单号且没有请求时间，返回错误
                    JSONObject resultData = new JSONObject();
                    resultData.put("start", startIndex);
                    resultData.put("rowCount", range);
                    resultData.put("row", new JSONArray());
                    resultData.put("totalRowCount", 0);
                    vh.put("code", ResultCode.FAIL);
                    vh.put("message", "请求时间必填，除非指定业务单号");
                    vh.put("data", resultData);
                    return vh;
                }
            }

            // queryWrapper需要设置根据id 倒序
            queryWrapper.orderByDesc(IpBThirdPartyInterfaceLog::getId);

            // 直接从数据库查询数据和总数
            // 查询总数
            int totalCount = ipBThirdPartyInterfaceLogMapper.selectCount(queryWrapper);

            // 如果没有数据，直接返回空结果
            if (totalCount == 0) {
                JSONObject resultData = new JSONObject();
                resultData.put("start", startIndex);
                resultData.put("rowCount", range);
                resultData.put("row", new JSONArray());
                resultData.put("totalRowCount", 0);
                // 打印出来参数
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", "success");
                vh.put("data",resultData);
                return vh;
            }

            // 创建分页对象
            Page<IpBThirdPartyInterfaceLog> page = new Page<>(startIndex / range + 1, range);

            // 执行分页查询
            IPage<IpBThirdPartyInterfaceLog> resultPage = ipBThirdPartyInterfaceLogMapper.selectPage(page, queryWrapper);

            // 获取查询结果
            List<IpBThirdPartyInterfaceLog> logList = resultPage.getRecords();
            JSONArray jsonArray = (JSONArray) JSONArray.toJSON(logList);
            List<JSONObject> jsonObjectList = JSONObject.parseArray(
                    JSONObject.toJSONString(jsonArray, SerializerFeature.WriteMapNullValue), JSONObject.class);
            JSONObject resultData = new JSONObject();
            resultData.put("start", startIndex);
            resultData.put("rowCount", range);
            resultData.put("row", getFrameDataFormat(jsonObjectList));
            resultData.put("totalRowCount", totalCount);
            // 打印出来参数
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "success");
            vh.put("data", resultData);
            return vh;

        } catch (Exception e) {
            JSONObject resultData = new JSONObject();
            resultData.put("start", startIndex);
            resultData.put("rowCount", range);
            resultData.put("row", new JSONArray());
            resultData.put("totalRowCount", 0);

            vh.put("datas", resultData);
            vh.put("code", -1);
        }

        return vh;
    }
}
