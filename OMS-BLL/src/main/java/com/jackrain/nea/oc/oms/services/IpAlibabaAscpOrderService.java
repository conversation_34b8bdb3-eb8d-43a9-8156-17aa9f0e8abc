package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.IpBAlibabaAscpOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBAlibabaAscpOrderMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrder;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderItemExt;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/3 下午4:38
 * @description 中间表天猫超市直发订单处理服务
 **/

@Slf4j
@Component
public class IpAlibabaAscpOrderService {

    @Autowired
    private IpBAlibabaAscpOrderMapper orderMapper;

    @Autowired
    private IpBAlibabaAscpOrderItemMapper orderItemMapper;


    /**
     * 依据OrderNo进行查询中间表天猫直发信息数据
     *
     * @param orderCode 天猫直发订单号
     * @return 中间表天猫直发信息数据
     */
    public IpAlibabaAscpOrderRelation selectAlibabaAscpOrder(String orderCode) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("进入中间表数据查询orderCode", orderCode));
        }
        IpBAlibabaAscpOrder orderInfo = this.orderMapper.selectAlibabaAscpOrderByTid(orderCode);

        if (orderInfo == null) {
            return null;
        }

        IpAlibabaAscpOrderRelation alibabaAscpOrderRelation = new IpAlibabaAscpOrderRelation();
        long orderId = orderInfo.getId();
        List<IpBAlibabaAscpOrderItemExt> orderItemList = this.orderItemMapper.selectOrderItemList(orderId);

        alibabaAscpOrderRelation.setAlibabaAscpOrderItemExList(orderItemList);
        alibabaAscpOrderRelation.setAlibabaAscpOrder(orderInfo);
        orderInfo.setReceiverAddress(orderInfo.getReceiverAddress().replace(",", "::::"));
        return alibabaAscpOrderRelation;
    }

    /**
     * 更新猫超中间表订单状态值
     *
     * @param orderNo             订单编号
     * @param transferOrderStatus 转换订单状态
     * @param remarks             备注信息
     * @return 更新是否成功。true-成功；false-失败
     */
    public boolean updateAlibabaAscpOrderTransStatus(String orderNo, TransferOrderStatus transferOrderStatus,
                                                     String remarks) {
        boolean isUpdateTransNum = transferOrderStatus == TransferOrderStatus.TRANSFERRED;
        if (remarks != null && remarks.length() > IpBTaobaoOrderMapper.MAX_REMARK_LENGTH) {
            remarks = remarks.substring(0, IpBTaobaoOrderMapper.MAX_REMARK_LENGTH - 1);
        }

        int result = this.orderMapper.updateOrderIsTrans(orderNo, transferOrderStatus.toInteger(),
                isUpdateTransNum, remarks, new Date(System.currentTimeMillis()));
        return result > 0;
    }
}
