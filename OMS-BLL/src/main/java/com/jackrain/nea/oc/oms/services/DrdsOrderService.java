package com.jackrain.nea.oc.oms.services;

import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * DRDS全渠道订单分库查询
 *
 * @author: 孙勇生
 * @since: 2019-10-31
 * create at : 2019-10-31
 */
@Component
@Slf4j
public class DrdsOrderService {

    @Autowired
    private OcBOrderMapper orderMapper;

    public List<OcBOrder> selectByNode(String nodeName, String whereStr, String orderStr, int limit) {
        StringBuffer sql = new StringBuffer();
        //TODO 如果是DRDS数据库 必传参节点名称nodeName
        if (StringUtils.isEmpty(nodeName)) {
            return null;
        }
        sql.append("/!TDDL:NODE='" + nodeName + "'*/").append("select id from oc_b_order");

        if (!StringUtils.isEmpty(whereStr)) {
            sql.append(" where ").append(whereStr);
        }
        if (!StringUtils.isEmpty(orderStr)) {
            sql.append(" order by  ").append(orderStr);
        }
        List<OcBOrder> orderList = Lists.newArrayList();
        return orderList;
    }


}
