package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.TradeMemoUpdateModel;
import com.jackrain.nea.oc.oms.gsi.GSI4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 修改退单卖家备注
 *
 * @author: xiWen.z
 * create at: 2019/9/18 0018
 */
@Slf4j
@Component
public class OcBModifyReturnRemarkService {

    /**
     * 淘宝
     */
    private final int tb = 2;
    /**
     * 淘宝分销
     */
    private final int tbd1 = 3;
    /**
     * 淘宝经销
     */
    private final int tbD2 = 77;
    /**
     * 京东
     */
    private final int jd = 4;
    /**
     * 平台异常码
     */
    private final int exCode = -2;
    /**
     * 异常长度
     */
    private final int exLen = 100;
    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private CpRpcService cpRpcService;

    /**
     * @param jsnObj
     * @param usr
     * @return
     */
    public ValueHolderV14 modifySellerRemark(JSONObject jsnObj, User usr) throws NDSException {

        ValueHolderV14 vh = validateParam(jsnObj, usr);
        if (ResultCode.FAIL == vh.getCode()) {
            return vh;
        }

        String ids = jsnObj.getString("IDS");
        List<JSONObject> rtnJsnObj = ocBReturnOrderMapper.querySourceCodeRemarkByIds(ids.trim());
        JSONArray jsnAry = new JSONArray();
        for (JSONObject o : rtnJsnObj) {
            String code = o.getString("ORIG_SOURCE_CODE");
            if (StringUtils.isBlank(code)) {
                continue;
            }
            if (!jsnAry.contains(code)) {
                jsnAry.add(code);
            }
        }
        //    JSONObject search = ES4ReturnOrder.getReturnOrderByOrigSourcecode(jsnAry);
        List<String> codes = JSONArray.parseArray(jsnAry.toJSONString(), String.class);
        //    List<Long> doIds = splitOrderIds(search);
        List<Long> doIds = GSI4ReturnOrder.getReturnIdListByOrigSourceCodes(codes);
        if (CollectionUtils.isEmpty(doIds)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("未查询到退单", usr.getLocale()));
            return vh;
        }

        String remark = jsnObj.getString("BACK_MESSAGE");
        String ordFlag = jsnObj.getString("ORDERFLAG");
        boolean cover = jsnObj.getBoolean("COVER");
        Map<String, String> pfMap = new HashMap<>();
        Map<String, String> lcMap = new HashMap<>();
        Set<String> spfSet = new HashSet<>();
        Set<String> fpfSet = new HashSet<>();

        int pfCnt = 0;
        int lcCnt = 0;
        int scCnt = 0;
        int spfCnt = 0;
        Set<String> disSet = new HashSet<>();
        for (int i = 0, l = doIds.size(); i < l; i++) {
            boolean sg;
            OcBReturnOrder r = ocBReturnOrderMapper.querySourceCodeRemarkById(doIds.get(i));
            if (r == null) {
                continue;
            }
            //  r.setOrderflag(ordFlag == null ? r.getOrderflag() : ordFlag);
            r.setOrderflag("1");
            r.setModifierid(Long.valueOf(usr.getId()));
            r.setModifiername(usr.getName());
            r.setModifierename(usr.getEname());
            r.setModifieddate(new Date());
            if (cover) {
                r.setBackMessage(remark);
            } else {
                if (StringUtils.isNotBlank(r.getBackMessage())) {
                    r.setBackMessage(r.getBackMessage() + ";" + remark);
                } else {
                    r.setBackMessage(remark);
                }
            }
            if (r.getBackMessage().length() > 1600) {
                recordMsg(lcMap, ++lcCnt, r, "失败; [平台未同步]; 原因: 备注过长");
                continue;
            }
            try {
                sg = (ocBReturnOrderMapper.updateSellerRemark(r)) > ResultCode.SUCCESS ? true : false;
            } catch (Exception e) {
                recordMsg(lcMap, ++lcCnt, r, "异常; [平台未同步]; 异常信息:" + cutOutExpString(e));
                continue;
            }
            if (sg) {
                scCnt++;
                if (!disSet.add(r.getOrigSourceCode())) {
                    continue;
                }
                ValueHolderV14 pvh = synchronizedPlatformOrder(r, usr);
                if (pvh.getCode() == ResultCode.FAIL) {
                    if (fpfSet.add(r.getOrigSourceCode())) {
                        ++pfCnt;
                    }
                    recordMsg(pfMap, pfCnt, r, "[同步平台]: 失败; " + pvh.getMessage());
                    disSet.remove(r.getOrigSourceCode());
                    continue;
                }
                if (pvh.getCode() == exCode) {
                    if (fpfSet.add(r.getOrigSourceCode())) {
                        ++pfCnt;
                    }
                    recordMsg(pfMap, pfCnt, r, "[同步平台]: 异常; " + pvh.getMessage());
                    disSet.remove(r.getOrigSourceCode());
                    continue;
                }
                if (spfSet.add(r.getOrigSourceCode())) {
                    spfCnt++;
                }
            } else {
                recordMsg(lcMap, ++lcCnt, r, "失败; [平台未同步]; 原因: 更新退单备注失败");
                continue;
            }
        }
        JSONObject vhJsn = new JSONObject();
        vhJsn.put("LOCAL", lcMap);
        vhJsn.put("PLATFORM", pfMap);
       /* vh.setMessage("修改完成; 成功: " + scCnt + " 条, 失败: " + lcCnt + " 条; 同步平台: 成功"
                + spfCnt + " 单, 失败: " + pfCnt + " 单");*/
        vh.setMessage("修改完成; 成功: " + scCnt + " 条, 失败: " + lcCnt + " 条");

        vh.setData(vhJsn);
        return vh;
    }

    /**
     * 记录信息
     *
     * @param map
     * @param n
     * @param r
     * @param msg
     */
    private void recordMsg(Map<String, String> map, int n, OcBReturnOrder r, String msg) {
        map.put(String.valueOf(n), "编号:" + r.getId() + ", 平台单号:" + r.getOrigSourceCode() + msg);
    }

    /**
     * 同步平台
     *
     * @param rdto
     * @param usr
     * @return
     */
    private ValueHolderV14 synchronizedPlatformOrder(OcBReturnOrder rdto, User usr) {
        ValueHolderV14 vh;
        Integer platform = rdto.getPlatform();
        if (platform == null || platform == ResultCode.SUCCESS) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("同步平台失败; 平台: " + platform + ". 信息: 平台不存在");
            return vh;
        }
        if (platform == tb || platform == tbd1 || platform == tbD2) {
            TradeMemoUpdateModel request = new TradeMemoUpdateModel();
            try {
                String shopSecretKey = cpRpcService.selectShopById(rdto.getCpCShopId()).getShopSecretKey();
                String[] split = shopSecretKey.split("\\|");
                String[] split1 = split[2].split(":");
                String sessionKey = split1[1];
                request.setOperateUser(usr);
                request.setTid(Long.valueOf(rdto.getOrigSourceCode()));
                // request.setFlag(Long.valueOf(rdto.getOrderflag()));
                request.setFlag(Long.valueOf(1));
                request.setMemo(rdto.getBackMessage());
                request.setSessionKey(sessionKey);
                request.setPlatform(platform);
                vh = ipRpcService.tradeMemoUpdate(request);
                if (vh == null) {
                    vh = new ValueHolderV14();
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("同步淘宝平台失败; 平台:" + platform + ". 平台接口返回结果为Null");
                    return vh;
                }
                if (vh.getCode() == ResultCode.FAIL) {
                    vh.setMessage("同步淘宝平台失败; 平台:" + platform + ". 状态码:" + vh.getCode() + " .平台接口返回信息: "
                            + vh.getMessage());
                    return vh;
                }
            } catch (Exception e) {
                vh = new ValueHolderV14();
                vh.setCode(exCode);
                vh.setMessage("同步淘宝平台异常; 平台:" + platform + ". 异常信息: " + cutOutExpString(e));
                return vh;
            }
        } else if (platform == jd) {
            TradeMemoUpdateModel request = new TradeMemoUpdateModel();
            try {
                request.setOperateUser(usr);
                request.setTid(Long.valueOf(rdto.getOrigSourceCode()));
                //    request.setFlag(Long.valueOf(rdto.getOrderflag()));
                request.setFlag(Long.valueOf(1));
                request.setMemo(rdto.getBackMessage());
                request.setSellerNick(rdto.getSellerNick());
                request.setPlatform(platform);
                vh = ipRpcService.tradeMemoUpdate(request);
                if (vh == null) {
                    vh = new ValueHolderV14();
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("同步京东平台失败; 平台:" + platform + ". 平台接口返回结果为Null");
                    return vh;
                }
                if (vh.getCode() == ResultCode.FAIL) {
                    vh.setMessage("同步京东平台失败; 平台:" + platform + ". 状态码:" + vh.getCode() + " .平台接口返回信息: "
                            + vh.getMessage());
                    return vh;
                }
            } catch (Exception e) {
                vh = new ValueHolderV14();
                vh.setCode(exCode);
                vh.setMessage("同步京东平台异常; 平台:" + platform + ". 异常信息: " + cutOutExpString(e));
                return vh;
            }
        } else {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("未同步平台; 平台: " + platform + ". 信息: 此平台单号不同步平台");
            return vh;
        }
        vh = new ValueHolderV14();
        vh.setCode(ResultCode.SUCCESS);
        return vh;
    }

    /**
     * 校验
     *
     * @param jsnObj
     * @param usr
     * @return
     */
    private ValueHolderV14 validateParam(JSONObject jsnObj, User usr) {
        ValueHolderV14 vh = new ValueHolderV14();
        vh.setCode(ResultCode.FAIL);
        String msg = null;
        String ids = jsnObj.getString("IDS");
        String remark = jsnObj.getString("BACK_MESSAGE");
        String ordFlag = jsnObj.getString("ORDERFLAG");
        Boolean cover = jsnObj.getBoolean("COVER");
        valLabel:
        if (usr != null && usr.getId() != null) {
            if (StringUtils.isBlank(ids)) {
                msg = "订单数量为0";
                break valLabel;
            }
            if (StringUtils.isBlank(remark)) {
                msg = "备注不能为空";
                break valLabel;
            }
            vh.setCode(ResultCode.SUCCESS);
        } else {
            msg = "用户不存在";
        }
        vh.setMessage(msg);
        return vh;
    }


    /**
     * @param esJo
     * @return
     */
    private List<Long> splitOrderIds(JSONObject esJo) {
        List<Long> list = new ArrayList<>();
        if (esJo == null) {
            return null;
        }
        JSONArray ary = esJo.getJSONArray("data");
        if (ary == null) {
            return null;
        }
        for (int i = 0, l = ary.size(); i < l; i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            Long id = o.getLong("ID");
            if (id == null) {
                continue;
            }
            list.add(id);
        }
        return list;
    }

    /**
     * 字符串截取
     *
     * @param e
     * @return
     */
    private String cutOutExpString(Exception e) {
        if (e == null) {
            return "";
        }
        String msg = e.getMessage();
        if (msg == null) {
            return "";
        }
        if (msg.length() > exLen) {
            msg = msg.substring(0, exLen);
        }
        return msg;
    }

}
