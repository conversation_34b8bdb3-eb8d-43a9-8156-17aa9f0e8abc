package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/10/21 下午4:50
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsToBOrderManualMergeService {

    private static final List<String> BUSINESS_CODE_LIST = Lists.newArrayList("RYCK16", "RYCK17", "RYCK18");

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OmsToBOrderManualMergeService omsToBOrderManualMergeService;
    @Autowired
    private OrderAmountUtil orderAmountUtil;


    public ValueHolderV14 orderManualMergeService(List<Long> ids, User user) {
        ValueHolderV14 holder = new ValueHolderV14();
        if (ids.size() == 1) {
            holder.setCode(-1);
            holder.setMessage("请勾选至少两笔订单");
            return holder;
        }
        List<RedisReentrant> redisReentrants = this.batchLock(ids);
        try {
            if (redisReentrants.size() != ids.size()) {
                List<Long> orderIds = redisReentrants.stream().filter(p -> p.getId() != null).map(RedisReentrant::getId).collect(Collectors.toList());
                List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsList(orderIds);
                List<String> billNos = new ArrayList<>();
                for (OcBOrder ocBOrder : ocBOrderList) {
                    billNos.add(ocBOrder.getBillNo());
                }
                String join = StringUtils.join(billNos, ",");
                holder.setCode(-1);
                holder.setMessage("勾选订单:" + join + "正在被被操作,无法合单");
                return holder;
            }
            List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsList(ids);
            for (OcBOrder ocBOrder : ocBOrderList) {
                Integer orderStatus = ocBOrder.getOrderStatus();
                if (!(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)
                        || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus))) {
                    holder.setCode(-1);
                    holder.setMessage("勾选订单:" + ocBOrder.getBillNo() + "订单状态不是待审核或者待寻源,无法合单");
                    return holder;
                }
            }
            Set<String> businessCodes = new HashSet<>();
            Set<String> addrList = new HashSet<>();
            Set<String> saleProductAttrList = new HashSet<>();
            for (OcBOrder ocBOrder : ocBOrderList) {
                businessCodes.add(ocBOrder.getBusinessTypeCode());
                Long shopId = ocBOrder.getCpCShopId();
                Long cpCRegionProvinceId = ocBOrder.getCpCRegionProvinceId();
                Long cpCRegionCityId = ocBOrder.getCpCRegionCityId();
                Long cpCRegionAreaId = ocBOrder.getCpCRegionAreaId();
                String receiverAddress = ocBOrder.getReceiverAddress();
                String receiverName = ocBOrder.getReceiverName();
                String receiverMobile = ocBOrder.getReceiverMobile();
                Long cpCPhyWarehouseId = ocBOrder.getCpCPhyWarehouseId() == null ? 0L : ocBOrder.getCpCPhyWarehouseId();
                addrList.add(shopId + cpCRegionProvinceId + cpCRegionCityId + cpCRegionAreaId + receiverAddress + receiverName + receiverMobile + cpCPhyWarehouseId);
                saleProductAttrList.add(ocBOrder.getSaleProductAttr());
            }
            if (businessCodes.size() != 1) {
                holder.setCode(-1);
                holder.setMessage("勾选订单业务类型不一致,无法合单");
                return holder;
            }
            if(saleProductAttrList.size() != 1){
                holder.setCode(-1);
                holder.setMessage("已选择订单的“销售商品属性”不同，不允许合单");
                return holder;
            }
            OcBOrder ocBOrder = ocBOrderList.get(0);
            String businessTypeCode = ocBOrder.getBusinessTypeCode();
            if (!BUSINESS_CODE_LIST.contains(businessTypeCode)) {
                holder.setCode(-1);
                holder.setMessage("勾选订单业务类型不支持合单");
                return holder;
            }
            //判断地址
            if (addrList.size() != 1) {
                holder.setCode(-1);
                holder.setMessage("勾选订单收货地址或发货仓库不一致,无法合单");
                return holder;
            }
            List<OcBOrderItem> bOrderItems = ocBOrderItemMapper.selectOrderItemListOccupyByOrderIds(ids);
            Map<String, String> skuAddServiceMap = new HashMap<>();

            // 校验增值服务
            for (OcBOrderItem ocBOrderItem : bOrderItems) {
//
//                if (ocBOrderItem.getReserveBigint01() != null && ocBOrderItem.getReserveBigint01().intValue() != (DmsGiftAttrEnum.OUR_PRODUCT.getVal())) {
//                    holder.setCode(-1);
//                    holder.setMessage(ocBOrderItem.getTid() + "订单存在非本品,不允许合单");
//                    return holder;
//                }
                if (StringUtils.isNotEmpty(ocBOrderItem.getLabelingRequirements())) {
                    String addService = skuAddServiceMap.get(ocBOrderItem.getPsCSkuEcode());
                    if (addService != null && addService == "") {
                        holder.setCode(-1);
                        holder.setMessage("存在与其他订单明细同sku不同增值服务类型");
                        return holder;
                    }
                    if (StringUtils.isEmpty(addService)) {
                        skuAddServiceMap.put(ocBOrderItem.getPsCSkuEcode(), ocBOrderItem.getLabelingRequirements());
                    } else if (!ocBOrderItem.getLabelingRequirements().equals(addService)) {
                        holder.setCode(-1);
                        holder.setMessage("存在与其他订单明细同sku不同增值服务类型");
                        return holder;
                    }
                } else {
                    String addService = skuAddServiceMap.get(ocBOrderItem.getPsCSkuEcode());
                    if (StringUtils.isNotEmpty(addService)) {
                        holder.setCode(-1);
                        holder.setMessage("存在与其他订单明细同sku不同增值服务类型");
                        return holder;
                    } else {
                        skuAddServiceMap.put(ocBOrderItem.getPsCSkuEcode(), "");
                    }
                }
            }

            Map<Long, List<OcBOrderItem>> itemMap = bOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
            for (OcBOrder order : ocBOrderList) {
                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(order.getOrderStatus())) {
                    List<OcBOrderItem> orderItems = itemMap.get(order.getId());
                    ValueHolderV14 sgValueHolder = sgRpcService.voidSgStockOccupy(order, orderItems, user);
                    if (sgValueHolder.getCode() != 0) {
                        holder.setCode(-1);
                        holder.setMessage("勾选订单" + order.getBillNo() + "释放库存失败,无法合单");
                        return holder;
                    }
                }
            }
            List<OcBOrderEqualExchangeItem> exchangeItems = ocBOrderEqualExchangeItemMapper.selectOcBOrderEqualExchangeItemList(ids);
            List<OcBOrderNaiKa> naiKaList = ocBOrderNaiKaMapper.getOrderNaiKaListByOrderId(ids);
            omsToBOrderManualMergeService.saveOrder(ocBOrderList, bOrderItems, exchangeItems, naiKaList, user);
            holder.setCode(0);
            holder.setMessage("TOB订单手工合并成功!");
            return holder;
        } catch (Exception e) {
            log.error(LogUtil.format("TOB订单手工合并异常:{}", "TOB订单手工合并异常"), Throwables.getStackTraceAsString(e));
            holder.setCode(-1);
            holder.setMessage("TOB订单手工合并异常");
            return holder;
        } finally {
            if (CollectionUtils.isNotEmpty(redisReentrants)) {
                for (RedisReentrant redisReentrant : redisReentrants) {
                    RedisReentrantLock lock = redisReentrant.getLock();
                    if (lock != null) {
                        lock.unlock();
                    }
                }
            }

        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveOrder(List<OcBOrder> ocBOrderList, List<OcBOrderItem> bOrderItems,
                          List<OcBOrderEqualExchangeItem> exchangeItems, List<OcBOrderNaiKa> naiKaList, User user) {
        OcBOrder ocBOrder1 = ocBOrderList.get(0);
        OcBOrder ocBOrder = new OcBOrder();
        BeanUtils.copyProperties(ocBOrder1, ocBOrder);
        long mainId = sequenceUtil.buildOrderSequenceId();
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        ocBOrder.setIsMerge(1);
        ocBOrder.setId(mainId);
        ocBOrder.setSuffixInfo("MG-" + mainId);
        ocBOrder.setCpCPhyWarehouseEcode(null);
        ocBOrder.setCpCPhyWarehouseEname(null);
        ocBOrder.setCpCPhyWarehouseId(null);
        ocBOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        Set<String> tids = new HashSet<>();
        for (OcBOrderItem bOrderItem : bOrderItems) {
            bOrderItem.setId(sequenceUtil.buildOrderItemSequenceId());
            bOrderItem.setOcBOrderId(mainId);
            tids.add(bOrderItem.getTid());
        }
        if (tids.size() == 1){
            ocBOrder.setIsMerge(0);
            ocBOrder.setIsSplit(0);
        }
        String join = StringUtils.join(tids, ",");
        ocBOrder.setMergeSourceCode(join);
        for (OcBOrder bOrder : ocBOrderList) {
            OcBOrder order = new OcBOrder();
            order.setId(bOrder.getId());
            order.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
            ocBOrderMapper.updateById(order);
            omsOrderLogService.addUserOrderLog(bOrder.getId(), bOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_MERGE.getKey(), "TOB订单手工合并作废原单", "", "", user);
        }

        OcBOrderParam param = new OcBOrderParam();
        param.setOcBOrder(ocBOrder);
        param.setOrderItemList(bOrderItems);
        orderAmountUtil.recountOrderAmount(param);
        ocBOrderMapper.insert(ocBOrder);
        ocBOrderItemMapper.batchInsert(bOrderItems);
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.ORDER_MERGE.getKey(), "TOB订单手工合并,生成新单", "", "", user);
        if (CollectionUtils.isNotEmpty(exchangeItems)) {
            for (OcBOrderEqualExchangeItem exchangeItem : exchangeItems) {
                exchangeItem.setId(sequenceUtil.buildEqualExchangeItemSequenceId());
                exchangeItem.setOcBOrderId(mainId);
            }
            ocBOrderEqualExchangeItemMapper.batchInsert(exchangeItems);
        }
        if (CollectionUtils.isNotEmpty(naiKaList)) {
            for (OcBOrderNaiKa orderNaiKa : naiKaList) {
                orderNaiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
                orderNaiKa.setOcBOrderId(mainId);
            }
            ocBOrderNaiKaMapper.batchInsert(naiKaList);
        }

    }


    private List<RedisReentrant> batchLock(List<Long> relationIds) {
        List<RedisReentrant> batchLocks = new ArrayList<>();
        for (Long relationId : relationIds) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(relationId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            RedisReentrant redisReentrant = new RedisReentrant();
            try {
                if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                    redisReentrant.setLock(redisLock);
                    batchLocks.add(redisReentrant);
                } else {
                    redisReentrant.setId(relationId);
                    redisLock.unlock();
                }
            } catch (Exception e) {
                redisLock.unlock();
            }
        }
        return batchLocks;
    }


    @Data
    class RedisReentrant implements Serializable {

        private RedisReentrantLock lock;

        private Long id;
    }
}
