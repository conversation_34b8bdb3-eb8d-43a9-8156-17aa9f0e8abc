package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.extmodel.SqlHandleModel;
import com.jackrain.nea.oc.oms.mapper.OcBAutoReleaseHangTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.task.OcBAutoReleaseHangTaskTable;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/12/28 13:26
 * @desc 自动解挂
 */
@Component
@Slf4j
public class AutoReleaseHangTaskService {

    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;

    @Autowired
    private BuildSequenceUtil buildSequenceUtil;

    @Autowired
    private BllRedisLockOrderUtil redisLockOrderUtil;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    protected OcBOrderHoldService ocBOrderHoldService;

    @Autowired
    private OcBOrderHoldItemService holdItemService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBAutoReleaseHangTaskMapper autoReleaseHangTaskMapper;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;


    public static User user;

    public static List<Integer> ORDER_STATUS;


    /**
     * 保存
     *
     * @param orderId 订单id
     */
    public void save(Long orderId, Date releaseDate,User user) {
        autoReleaseHangTaskMapper.insert(this.build(orderId, releaseDate,user));
    }


    public List<Long> selectCanExecuteData(String node, String tableName, int pageSize, List<SqlHandleModel> models) {
        return null;
    }


    /**
     * 卡单释放任务
     *
     * @param ids ids
     * @return
     */
    public int handle(List<Long> ids) {
        int successSize = 0;
        List<Long> errorIds = Lists.newArrayList();
        for (Long id : ids) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisLockOrderUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    this.cancelHang(id, getUser());
                    successSize++;
                } else {
                    throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
                }
            } catch (Exception ex) {
                errorIds.add(id);
                log.error(this.getClass().getName() + ",订单ID:" + id + ",取消解挂失败", ex);
            } finally {
                redisLock.unlock();
            }
        }

        ids.removeAll(errorIds);
        if (ids.isEmpty()) {
            return successSize;
        }
        OcBAutoReleaseHangTaskTable releaseHangTaskTable = new OcBAutoReleaseHangTaskTable();
        releaseHangTaskTable.setStatus(NumberUtils.INTEGER_ONE);
        releaseHangTaskTable.setModifieddate(new Date());
        autoReleaseHangTaskMapper.update(releaseHangTaskTable, new LambdaQueryWrapper<OcBAutoReleaseHangTaskTable>().in(OcBAutoReleaseHangTaskTable::getOrderId, ids));
        return successSize;
    }


    @Transactional(rollbackFor = Exception.class)
    public void cancelHang(Long id, User loginUser) {
        OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
        if (ocBOrder == null || !NumberUtils.INTEGER_ONE.equals(ocBOrder.getIsDetention()) || !getOrderStatus().contains(ocBOrder.getOrderStatus())) {
            return;
        }
        //取消挂标
        OcBOrder updateModel = new OcBOrder();
        updateModel.setId(id);
        updateModel.setIsDetention(AdvanceConstant.DETENTION_STATUS_2);
        updateModel.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
        //更新状态为分仓
        updateModel.setSysremark("卡单释放成功");
        updateModel.setDetentionReason("");
        //最后一次卡单释放埋点
        updateModel.setDetentionReleaseDate(new Date());
        // 不在走卡单接口，直接去占单
        updateModel.setOccupyStatus(OrderOccupyStatus.STATUS_13);

        ocBOrderMapper.update(updateModel, Wrappers.<OcBOrder>lambdaUpdate()
                .set(OcBOrder::getDetentionReasonId, null)
                .eq(OcBOrder::getId, id));
        //重新占单
        toBeConfirmedTaskService.insert(id);

        ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.DETENTION_RELEASE_DATE, new Date(), id, loginUser);

        String message = "订单OrderId" + id + "卡单自动释放成功！";

        omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.DETENTION_RELEASE.getKey(), message, null, null, loginUser);
    }


    /**
     * 构造 model
     *
     * @param orderId 订单id
     * @return
     */
    public OcBAutoReleaseHangTaskTable build(Long orderId, Date releaseTime,User user) {

        OcBAutoReleaseHangTaskTable releaseHangTaskTable = new OcBAutoReleaseHangTaskTable();
        releaseHangTaskTable.setId(buildSequenceUtil.buildAutoReleaseHangId());
        releaseHangTaskTable.setOrderId(orderId);
        //  不自动释放
        if (releaseTime == null){
            releaseHangTaskTable.setStatus(NumberUtils.INTEGER_ONE);
        }else {
            releaseHangTaskTable.setAutoReleaseTime(releaseTime);
        }
        OperateUserUtils.saveOperator(releaseHangTaskTable, user);
        return releaseHangTaskTable;
    }


    /**
     * 获取字段解挂时间
     *
     * @return
     */
    public Date getAutoReleaseHangTime(Date last) {
        return DateUtils.addHours(last, this.getParameters());
    }

    /**
     * 获取it参数
     *
     * @return
     */
    public int getParameters() {

        String times = redisOpsUtil.strRedisTemplate.opsForValue().get("business_system:auto_release_is_hang:time");

        return StringUtils.isBlank(times) ? -48 : -Integer.parseInt(times);
    }

    public static User getUser() {
        if (user == null) {
            user = SystemUserResource.getRootUser();
        }
        return user;
    }

    /**
     * 获取可解挂的状态
     *
     * @return
     */
    public static List<Integer> getOrderStatus() {
        if (CollectionUtils.isEmpty(ORDER_STATUS)) {
            ORDER_STATUS = Lists.newArrayList(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger(), OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
        }
        return ORDER_STATUS;
    }


    /**
     * getInstance
     *
     * @return AutoReleaseHangTaskService
     */
    public static AutoReleaseHangTaskService getInstance() {
        return ApplicationContextHandle.getBean(AutoReleaseHangTaskService.class);
    }
}
