package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.request.UpdateReturnOrderRequest;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 修改退换货订单仓库
 *
 * @author: xiWen.z
 * create at: 2019/7/16 0016
 */
@Component
@Slf4j
public class ModifyReturnOrderWarehouseService {

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    OcSaveChangingOrRefundingService refundingService;

    /**
     * 批量修改退货仓库
     *
     * @param req UpdateReturnOrderRequest
     * @param usr User
     * @return vh14
     */
    public ValueHolderV14 updateWarehouse(UpdateReturnOrderRequest req, User usr) {

        /**
         * 1. 校验
         */
        ValueHolderV14 vh = validateParam(req, usr);
        if (ResultCode.FAIL == vh.getCode()) {
            return vh;
        }
        recordDebug("updateWarehouse 退货仓库" + JSON.toJSONString(req));
        /**
         * 2. 业务
         */
        Long[] ids = req.getIds();
        List<String> errorList = new ArrayList<>();
        HashMap<Long, RedisReentrantLock> rdsMap = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        StringBuilder sb = new StringBuilder();
        try {
            HashMap<Long, Integer> countMap = new HashMap<>();
            for (int i = 0, l = ids.length; i < l; i++) {
                Long id = ids[i];
                countMap.put(id, (i + 1));
                if (id == null) {
                    errorList.add("第" + (i + 1) + "条: 发生错误,订单ID信息丢失");
                    continue;
                }
                String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        errorList.add("第" + (i + 1) + "条: 订单处于锁定状态, 修改失败");
                        continue;
                    }
                    rdsMap.put(id, redisLock);
                } catch (Exception e) {
                    errorList.add("第" + (i + 1) + "条: 锁单发生异常,修改失败");
                    continue;
                }
                sb.append(id);
                sb.append(",");
            }
            String mIds = sb.toString();
            if (mIds.length() < OcBOrderConst.IS_STATUS_IY) {
                return vhResult("订单全部处于锁定状态,暂时无法修改", false, usr);
            }
            mIds = mIds.substring(OcBOrderConst.IS_STATUS_IN, mIds.length() - OcBOrderConst.IS_STATUS_IY);
            List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.queryReturnOrderByIds(mIds);
            if (ocBReturnOrders == null) {
                return vhResult("未能查询到所选订单数据", false, usr);
            }
            sb = new StringBuilder();
            int lr = ocBReturnOrders.size();

            if (lr == 1) {
                OcBReturnOrder o = ocBReturnOrders.get(0);
                if (o != null && o.getCpCPhyWarehouseInId() != null && o.getCpCPhyWarehouseInId().equals(req.getCpCPhyWarehouseInId())) {
                    return vhResult("修改仓库与原仓库一致，请重新选择!", false, usr);
                }
            }

            for (int i = 0; i < lr; i++) {
                OcBReturnOrder o = ocBReturnOrders.get(i);
//                if (o != null && o.getReturnStatus() != null
//                        && OcBOrderConst.RETURN_STATUS_WAIT_REFUND.equals(o.getReturnStatus())) {
                if (o != null && o.getCpCPhyWarehouseInId() != null && o.getCpCPhyWarehouseInId().equals(req.getCpCPhyWarehouseInId())) {
                    errorList.add("第" + countMap.get(o.getId()) + "条: 修改仓库与原仓库一致，请重新选择");
                    continue;
                }
                if (o != null && o.getReturnStatus() != null
                        && ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(o.getReturnStatus())) {
                    if (o.getIsTowms() != null && !WmsWithdrawalState.YES.toInteger().equals(o.getIsTowms())) {
                        sb.append(o.getId());
                        sb.append(",");
                        continue;
                    }
                }
                errorList.add("第" + countMap.get(o.getId()) + "条: 该状态订单不允许修改, 修改失败");
            }
            mIds = sb.toString();
            if (mIds.length() < OcBOrderConst.IS_STATUS_IY) {
                vh = vhResult("所选订单全部不符合修改条件", false, usr);
                jsonObject.put("errorCount", errorList.size());
                jsonObject.put("errorRecord", errorList);
                vh.setData(jsonObject);
                return vh;
            }
            mIds = mIds.substring(OcBOrderConst.IS_STATUS_IN, mIds.length() - OcBOrderConst.IS_STATUS_IY);
//            OcBReturnOrder ocBReturnOrder = new OcBReturnOrder();
//            ocBReturnOrder.setCpCPhyWarehouseInId(req.getCpCPhyWarehouseInId());
//            OcBReturnOrder ocBReturnOrder1 = null;
//            try {
//                ocBReturnOrder1 = refundingService.checkWmsCtrHouse(ocBReturnOrder);
//            } catch (Exception e) {
//                log.error("查询出的是否传WMS异常：", e);
//                ocBReturnOrder1.setReserveBigint03(1L);
//            }
//            req.setReserveBigint03(ocBReturnOrder1.getReserveBigint03());
            int rlt = ocBReturnOrderMapper.updateCpWarehouseInId(req.getCpCPhyWarehouseInId(), mIds);
            for (int i = 0; i < ocBReturnOrders.size(); i++) {
                OcBReturnOrder ocBReturnOrder = ocBReturnOrders.get(i);
                ocBReturnOrder.setCpCPhyWarehouseInId(req.getCpCPhyWarehouseInId());
                ocBReturnOrder = refundingService.checkBillType(ocBReturnOrder);
                OcBReturnOrder ocBReturnOrder2 = null;
                try {
                    ocBReturnOrder2 = refundingService.checkWmsCtrHouse(ocBReturnOrder);
                } catch (Exception e) {
                    log.error("查询出的是否传WMS异常：", e);
                    ocBReturnOrder2.setIsNeedToWms(1L);
                }
                OcBReturnOrder ocBReturnOrder1 = new OcBReturnOrder();
                ocBReturnOrder1.setBillType(ocBReturnOrder.getBillType());
                ocBReturnOrder1.setIsNeedToWms(ocBReturnOrder2.getIsNeedToWms());
                ocBReturnOrderMapper.update(ocBReturnOrder1, new QueryWrapper<OcBReturnOrder>().lambda().eq(OcBReturnOrder::getId, ocBReturnOrder.getId()));
            }
            if (rlt < OcBOrderConst.IS_STATUS_IY) {
                return vhResult("更新数据失败", false, usr);
            }
            jsonObject.put("successCount", rlt);

        } catch (Exception ex) {
            log.error("{}###批量更新异常.ERROR:{}", this.getClass().getName(), Throwables.getStackTraceAsString(ex));
        } finally {
            Set<Long> longs = rdsMap.keySet();
            for (Long aLong : longs) {
                try {
                    RedisReentrantLock redisReentrantLock = rdsMap.get(aLong);
                    redisReentrantLock.unlock();
                } catch (Exception e) {
                    log.error("解除锁单状态异常.ERROR: id-{}", aLong);
                    continue;
                }

            }
        }
        String msg = "修改成功, 成功" + jsonObject.getIntValue("successCount") + "条, 失败:" + errorList.size() + "条";
        vh = vhResult(msg, true, usr);
        jsonObject.put("errorCount", errorList.size());
        jsonObject.put("errorRecord", errorList);
        vh.setData(jsonObject);
        return vh;
    }

    /**
     * 参数校验
     *
     * @param req UpdateReturnOrderRequest
     * @param usr User
     * @return vh14
     */
    private ValueHolderV14 validateParam(UpdateReturnOrderRequest req, User usr) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (usr == null) {
            recordDebug("###" + this.getClass().getName() + "###Verification: User Is Null");
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("当前用户不存在");
            return vh;
        }

        if (req == null || req.getCpCPhyWarehouseInId() == null
                || req.getIds() == null || req.getIds().length < OcBOrderConst.IS_STATUS_IY) {
            recordDebug("###" + this.getClass().getName() + "###Verification: Some Things Was Wrong Abt Param "
                    + "UpdateReturnOrderRequest");
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("请确认已选择要修改的订单", usr.getLocale()));
            return vh;
        }
        vh.setCode(ResultCode.SUCCESS);
        return vh;
    }

    /**
     * debug level record
     *
     * @param s log
     */
    private void recordDebug(String s) {
        if (log.isDebugEnabled()) {
            log.debug(s);
        }
    }

    /**
     * 返回信息
     *
     * @param msg       输出信息
     * @param flag      结果
     * @param loginUser 登录用户
     * @return vh
     */
    private ValueHolderV14 vhResult(String msg, boolean flag, User loginUser) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (flag) {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(msg, loginUser.getLocale()));
        }
        return vh;
    }
}
