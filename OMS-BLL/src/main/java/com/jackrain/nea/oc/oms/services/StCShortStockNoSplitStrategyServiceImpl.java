package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Throwables;
import com.google.common.primitives.Longs;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.oc.oms.mapper.StCShortStockNoSplitStrategyDetailMapper;
import com.jackrain.nea.oc.oms.mapper.StCShortStockNoSplitStrategyMapper;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyDetailEntity;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyEntity;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyRelation;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class StCShortStockNoSplitStrategyServiceImpl extends ServiceImpl<StCShortStockNoSplitStrategyMapper, StCShortStockNoSplitStrategyEntity> implements StCShortStockNoSplitStrategyService {


    @Autowired
    private BuildSequenceUtil buildSequenceUtil;

    @Autowired
    private StCShortStockNoSplitStrategyDetailMapper shortStockNoSplitStrategyDetailMapper;

    @Override
    public ValueHolder updateShortStockNoSplitStrategy(User user, StCShortStockNoSplitStrategyRelation shortStockNoSplitStrategyRelation) {
        Long mainId = shortStockNoSplitStrategyRelation.getShortStockNoSplitStrategyEntity().getId();
        StCShortStockNoSplitStrategyEntity shortStockNoSplitStrategyEntity = getById(mainId);
        List<StCShortStockNoSplitStrategyDetailEntity> shortStockNoSplitStrategyDetailEntityList = shortStockNoSplitStrategyRelation.getShortStockNoSplitStrategyDetailEntityList();

        if (CollectionUtils.isNotEmpty(shortStockNoSplitStrategyDetailEntityList)) {
            try {
                for (StCShortStockNoSplitStrategyDetailEntity detailEntity : shortStockNoSplitStrategyDetailEntityList) {
                    if (StringUtils.isBlank(detailEntity.getAgeing())) {
                        continue;
                    }
                    if (Integer.parseInt(detailEntity.getAgeing()) < 0 && !"-1".equals(detailEntity.getAgeing())) {
                        throw new NDSException("数据不规范");
                    }

                    if ("0".equals(detailEntity.getAgeing())) {
                        throw new NDSException("数据不规范");
                    }
                }
            }
            catch (NumberFormatException e) {
                log.error("缺货不拆策略新增明细数据不规范 => {}", Throwables.getStackTraceAsString(e));
                throw new NDSException("数据不规范");
            }

            if (!checkStrategyDetail(shortStockNoSplitStrategyDetailEntityList, shortStockNoSplitStrategyEntity)) {
                throw new NDSException("数据不规范");
            }

            for (StCShortStockNoSplitStrategyDetailEntity detailEntity : shortStockNoSplitStrategyDetailEntityList) {
                if (-1 == detailEntity.getId()) {
                    detailEntity.setId(buildSequenceUtil.buildShortStockNoSplitStrategyDetailSequenceId());
                    detailEntity.setStrategyId(mainId);
                    detailEntity.setCreationdate(new Date());
                    detailEntity.setOwnerid(user.getId().longValue());
                    detailEntity.setOwnerename(user.getEname());
                    detailEntity.setOwnername(user.getName());
                    detailEntity.setModifieddate(new Date());
                    detailEntity.setModifierid(user.getId().longValue());
                    detailEntity.setModifierename(user.getEname());
                    detailEntity.setModifiername(user.getName());
                    detailEntity.setIsactive("Y");
                    shortStockNoSplitStrategyDetailMapper.insert(detailEntity);
                } else {
                    detailEntity.setModifieddate(new Date());
                    detailEntity.setModifierid(user.getId().longValue());
                    detailEntity.setModifierename(user.getEname());
                    detailEntity.setModifiername(user.getName());
                    shortStockNoSplitStrategyDetailMapper.updateById(detailEntity);
                }
            }
        }
//        if (shortStockNoSplitStrategyRelation.getIsMain()) {
//            updateById(shortStockNoSplitStrategyRelation.getShortStockNoSplitStrategyEntity());
//        }
        ValueHolder valueHolder = ValueHolderUtils.success("编辑成功");
        valueHolder.put("data", ValueHolderUtils.createAddErrorData("ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY", mainId, null));
        return valueHolder;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ValueHolder addShortStockNoSplitStrategy(User user, StCShortStockNoSplitStrategyRelation shortStockNoSplitStrategyRelation) {

        StCShortStockNoSplitStrategyEntity shortStockNoSplitStrategyEntity = shortStockNoSplitStrategyRelation.getShortStockNoSplitStrategyEntity();

        // 公共的不指定店铺
        if (1 == shortStockNoSplitStrategyEntity.getStrategyType() && Objects.nonNull(shortStockNoSplitStrategyEntity.getShopId())) {
            return ValueHolderUtils.fail("数据不规范");
        }

        try {
            shortStockNoSplitStrategyEntity.setId(buildSequenceUtil.buildShortStockNoSplitStrategySequenceId());

            List<StCShortStockNoSplitStrategyDetailEntity> shortStockNoSplitStrategyDetailEntityList = shortStockNoSplitStrategyRelation.getShortStockNoSplitStrategyDetailEntityList();
            // 识别规则不为空，需要检验
            if (CollectionUtils.isNotEmpty(shortStockNoSplitStrategyDetailEntityList)) {
                try {
                    for (StCShortStockNoSplitStrategyDetailEntity detailEntity : shortStockNoSplitStrategyDetailEntityList) {
                        if (StringUtils.isBlank(detailEntity.getAgeing())){
                            continue;
                        }
                        if (Integer.parseInt(detailEntity.getAgeing()) < 0 && !"-1".equals(detailEntity.getAgeing())) {
                            return ValueHolderUtils.fail("数据不规范");
                        }

                        if ("0".equals(detailEntity.getAgeing())) {
                            return ValueHolderUtils.fail("数据不规范");
                        }
                    }
                }
                catch (NumberFormatException e) {
                    log.error("缺货不拆策略新增明细数据不规范 => {}", Throwables.getStackTraceAsString(e));
                    return ValueHolderUtils.fail("数据不规范");
                }

                if (!checkStrategyDetail(shortStockNoSplitStrategyDetailEntityList, shortStockNoSplitStrategyEntity)) {
                    return ValueHolderUtils.fail("策略规则出现重复，请检查");
                }
                shortStockNoSplitStrategyDetailEntityList.forEach(detail -> {
                    detail.setId(buildSequenceUtil.buildShortStockNoSplitStrategyDetailSequenceId());
                    detail.setStrategyId(shortStockNoSplitStrategyEntity.getId());
                    detail.setCreationdate(new Date());
                    detail.setOwnerid(user.getId().longValue());
                    detail.setOwnerename(user.getEname());
                    detail.setOwnername(user.getName());
                    detail.setModifieddate(new Date());
                    detail.setModifierid(user.getId().longValue());
                    detail.setModifierename(user.getEname());
                    detail.setModifiername(user.getName());
                    detail.setIsactive("Y");
                    shortStockNoSplitStrategyDetailMapper.insert(detail);
                });
            }

            shortStockNoSplitStrategyEntity.setStrategyCode(getStrategyNo(shortStockNoSplitStrategyEntity, user.getLocale()));
            shortStockNoSplitStrategyEntity.setCreationdate(new Date());
            shortStockNoSplitStrategyEntity.setOwnerid(user.getId().longValue());
            shortStockNoSplitStrategyEntity.setOwnerename(user.getEname());
            shortStockNoSplitStrategyEntity.setOwnername(user.getName());
            shortStockNoSplitStrategyEntity.setModifieddate(new Date());
            shortStockNoSplitStrategyEntity.setModifierid(user.getId().longValue());
            shortStockNoSplitStrategyEntity.setModifierename(user.getEname());
            shortStockNoSplitStrategyEntity.setModifiername(user.getName());
            shortStockNoSplitStrategyEntity.setIsactive("N");
            save(shortStockNoSplitStrategyEntity);
        }
        catch (Exception e) {
            log.error("缺货不拆策略保存异常={}", Throwables.getStackTraceAsString(e));
            throw new NDSException("缺货不拆策略保存异常");
        }

        JSONObject object = new JSONObject();
        object.put("tablename", "ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY");
        object.put("objid", String.valueOf(shortStockNoSplitStrategyEntity.getId()));
        ValueHolder valueHolder = ValueHolderUtils.success("新增成功");
        valueHolder.put("data", object);
        return valueHolder;
    }

    @Override
    public String getStrategyNo(StCShortStockNoSplitStrategyEntity strategyEntity, Locale locale) {
        JSONObject obj = new JSONObject();
        obj.put("ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY", strategyEntity);
        return SequenceGenUtil.generateSquence("ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY", obj, locale, false);
    }

    @Override
    public Boolean checkStrategyDetail(List<StCShortStockNoSplitStrategyDetailEntity> detail, StCShortStockNoSplitStrategyEntity mainStrategy) {

        StCShortStockNoSplitStrategyEntity shortStockNoSplitStrategy = getOne(Wrappers.lambdaQuery(new StCShortStockNoSplitStrategyEntity())
                .select(StCShortStockNoSplitStrategyEntity::getId)
                .eq(StCShortStockNoSplitStrategyEntity::getStrategyType, mainStrategy.getStrategyType())
                .eq(Objects.nonNull(mainStrategy.getShopId()), StCShortStockNoSplitStrategyEntity::getShopId, mainStrategy.getShopId()));

        // 新增且已存在对应策略
        if (Objects.isNull(mainStrategy.getId()) && Objects.nonNull(shortStockNoSplitStrategy)) {
            return Boolean.FALSE;
        }
        // 更新出了重复的策略
        if (Objects.nonNull(mainStrategy.getId()) && (Objects.nonNull(shortStockNoSplitStrategy) &&
                !shortStockNoSplitStrategy.getId().equals(mainStrategy.getId()))) {
            return Boolean.FALSE;
        }
        // 更新
        if (Objects.nonNull(mainStrategy.getId()) && (Objects.isNull(shortStockNoSplitStrategy) ||
                shortStockNoSplitStrategy.getId().equals(mainStrategy.getId()))) {
            List<StCShortStockNoSplitStrategyDetailEntity> stCShortStockNoSplitStrategyDetails = shortStockNoSplitStrategyDetailMapper.selectList(Wrappers.lambdaQuery(new StCShortStockNoSplitStrategyDetailEntity())
                    .eq(StCShortStockNoSplitStrategyDetailEntity::getStrategyId, mainStrategy.getId()));
            if (CollectionUtils.isNotEmpty(stCShortStockNoSplitStrategyDetails)) {
                Map<String, StCShortStockNoSplitStrategyDetailEntity> keyMap = stCShortStockNoSplitStrategyDetails.stream()
                        .collect(Collectors.toMap(o -> o.getMatchRule() + o.getMatchContent(), Function.identity()));
                for (StCShortStockNoSplitStrategyDetailEntity detailEntity : detail) {
                    String key = detailEntity.getMatchRule() + detailEntity.getMatchContent();
                    if (keyMap.containsKey(key)) {
                        return Boolean.FALSE;
                    }
                }
            }
        }

        return Boolean.TRUE;
    }

    @Override
    public ValueHolder editShortStockNoSplitStrategyStatus(User user, List<Long> ids, String status) {
        if (CollectionUtils.isEmpty(ids)) {
            return ValueHolderUtils.fail("请选择记录");
        }
        Collection<StCShortStockNoSplitStrategyEntity> list = listByIds(ids);

        if (CollectionUtils.isEmpty(list)) {
            return ValueHolderUtils.fail("操作记录不存在");
        }

        list.forEach(o -> {
            o.setModifieddate(new Date());
            o.setModifierid(user.getId().longValue());
            o.setModifierename(user.getEname());
            o.setModifiername(user.getName());
            o.setIsactive(status);
        });

        updateBatchById(list);

        return ValueHolderUtils.success("修改成功");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ValueHolder editShortStockNoSplitStrategyDetailStatus(User user, List<Long> ids, String status) {
        if (CollectionUtils.isEmpty(ids)) {
            return ValueHolderUtils.fail("请选择记录");
        }
        List<StCShortStockNoSplitStrategyDetailEntity> list = shortStockNoSplitStrategyDetailMapper.selectBatchIds(ids);

        if (CollectionUtils.isEmpty(list)) {
            return ValueHolderUtils.fail("操作记录不存在");
        }

        list.forEach(o -> {
            o.setModifieddate(new Date());
            o.setModifierid(user.getId().longValue());
            o.setModifierename(user.getEname());
            o.setModifiername(user.getName());
            o.setIsactive(status);
            shortStockNoSplitStrategyDetailMapper.updateById(o);
        });

        return ValueHolderUtils.success("修改成功");
    }

    @Override
    public List<Long> getObjIds(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        String objids = (String) event.getParameterValue("objids");

        if (StringUtils.isBlank(objids)) {

            String objid = (String) event.getParameterValue("objid");
            if (StringUtils.isNotBlank(objid)) {
                return Lists.newArrayList(Long.valueOf(objid));
            }

            JSONObject param = (JSONObject) event.getParameterValue("param");
            JSONArray ids = param.getJSONArray("ids");
            if (CollectionUtil.isEmpty(ids)) {
                return Lists.emptyList();
            }

            return ids.toJavaList(Long.class);
        }

        long[] longs = Stream.of(objids.split(","))
                .mapToLong(Long::new)
                .toArray();

        return Longs.asList(longs);
    }
}

