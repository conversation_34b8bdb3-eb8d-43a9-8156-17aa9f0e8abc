package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderAddServiceReportMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.StAddedServiceStrategyDocResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;

/**
 * @ClassName OcBOrderItemUpdateAddServiceService
 * @Description 修改增值服务
 * <AUTHOR>
 * @Date 2023/10/23 11:17
 * @Version 1.0
 */
@Component
@Slf4j
public class OcBOrderItemUpdateAddServiceService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private StRpcService stRpcService;

    public ValueHolderV14 updateItemAddService(JSONObject object, User user) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14(ResultCode.SUCCESS, "SUCCESS");

        Long ocBOrderId = object.getLong("orderId");
        if (ObjectUtil.isNull(ocBOrderId)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请传入订单主表ID");
            return valueHolderV14;
        }

        JSONArray ocBOrderItemArr = object.getJSONArray("itemIdList");
        if (CollectionUtils.isEmpty(ocBOrderItemArr)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请选择需要修改增值服务的记录");
            return valueHolderV14;
        }
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(ocBOrderId);
        if (ObjectUtil.isNull(ocBOrder)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("订单已不存在");
            return valueHolderV14;
        }

        Integer orderStatus = ocBOrder.getOrderStatus();

        // 校验当前的订单状态 需要时 待分配、待寻源、寻源中、待审核、已审核 则可以进行修改
        if (!(ObjectUtil.equal(orderStatus, OmsOrderStatus.ORDER_DEFAULT.toInteger()) ||
                ObjectUtil.equal(orderStatus, OmsOrderStatus.BE_OUT_OF_STOCK.toInteger()) ||
                ObjectUtil.equal(orderStatus, OmsOrderStatus.UNCONFIRMED.toInteger()) ||
                ObjectUtil.equal(orderStatus, OmsOrderStatus.OCCUPY_IN.toInteger()))) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("订单状态不合法,只有待审核之前的状态 才能修改");
            return valueHolderV14;
        }


        List<Long> ocBOrderItemIdList = new ArrayList<>();
        for (Object obj : ocBOrderItemArr) {
            ocBOrderItemIdList.add(Long.parseLong(obj.toString()));
        }
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectByOrderAndItem(ocBOrderId, ocBOrderItemIdList);
        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("明细已不存在");
            return valueHolderV14;
        }


        String addService = object.getString("addService");
        if (StringUtils.isEmpty(addService)) {
            ocBOrderItemMapper.clearLabelingRequirements(ocBOrderId, ocBOrderItemIdList);
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("success");
            return valueHolderV14;
        }

        // 校验增值服务是否合法
        String[] addServiceArr = addService.split(";");
        StringBuilder stringBuilder = new StringBuilder();
        // 使用treemap 既可以排序 又能去重
        TreeMap<String, Integer> treeMap = new TreeMap<>();
        for (String labelingRequirement : addServiceArr) {
            if (StringUtils.isNotEmpty(labelingRequirement)) {
                // 校验增值服是否存在
                ValueHolderV14<StAddedServiceStrategyDocResult> result =
                        stRpcService.selectDocInfoByTypeDocName(labelingRequirement);
                if (!result.isOK()) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("校验增值服务:" + labelingRequirement + ",失败");
                    return valueHolderV14;
                }
                StAddedServiceStrategyDocResult data = result.getData();
                if (data == null) {
                    stringBuilder.append(labelingRequirement);
                    stringBuilder.append(" ");
                } else {
                    treeMap.put(labelingRequirement, 1);
                }
            }
        }

        if (StringUtils.isNotEmpty(stringBuilder.toString())) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("增值服务:" + stringBuilder + " 不存在");
            return valueHolderV14;
        }


        String labelingRequirements = null;
        if (!treeMap.isEmpty()) {
            List<String> labelingRequirementList = new ArrayList<>();
            for (String labelingRequirement : treeMap.keySet()) {
                labelingRequirementList.add(labelingRequirement);
            }
            if (CollectionUtils.isNotEmpty(labelingRequirementList)) {
                labelingRequirements = String.join(";", labelingRequirementList);
            }
        }

        if (StringUtils.isEmpty(labelingRequirements)) {
            // 置空增值服务
            ocBOrderItemMapper.clearLabelingRequirements(ocBOrderId, ocBOrderItemIdList);
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("success");
            return valueHolderV14;
        }

        if (labelingRequirements.length() >= 500) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("增值服务长度不允许超过500");
            return valueHolderV14;
        }

        // 修改增值服务
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            OcBOrderItem updateOcBOrderItem = new OcBOrderItem();
            updateOcBOrderItem.setOcBOrderId(ocBOrderId);
            updateOcBOrderItem.setId(ocBOrderItem.getId());
            updateOcBOrderItem.setModifieddate(new Date());
            updateOcBOrderItem.setLabelingRequirements(labelingRequirements);
            ocBOrderItemMapper.updateById(updateOcBOrderItem);
        }
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");
        return valueHolderV14;
    }
}
