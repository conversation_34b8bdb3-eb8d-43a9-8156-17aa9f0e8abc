package com.jackrain.nea.oc.oms.services;

import cn.hutool.json.JSONUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefundType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IntermediateTableRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.ps.model.SkuType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/6/22 11:21 上午
 * @Version 1.0
 * <p>
 * 通用退单
 */
@Slf4j
@Component
public class OmsStandplatRefundService {


    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;

    @Autowired
    private IpBStandplatRefundMapper standplatRefundMapper;

    @Autowired
    private IpBStandplatRefundItemMapper standplatRefundItemMapper;

    @Autowired
    private OmsStandplatExchangeService omsStandplatExchangeService;

    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    @Autowired
    protected IpStandplatRefundService ipStandplatRefundService;

    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;

    @Autowired
    private IpBStandplatOrderMapper ipBStandplatOrderMapper;

    /**
     * 根据退单号 return_no 查询退单信息
     *
     * @param refundId 退单id
     * @return
     */
    public OmsStandPlatRefundRelation selectStandplatRefundRelation(String refundId) {

        OmsStandPlatRefundRelation refundRelation = new OmsStandPlatRefundRelation();

        //通用退单主表信息
        IpBStandplatRefund ipBStandplatRefund = standplatRefundMapper.selectStandplatRefundByRefundId(refundId);

        if (ipBStandplatRefund == null) {
            return null;
        }
        if (log.isDebugEnabled()) {
            log.debug("{} start standard order processor. into refundId :{},select mid order information params:{}", this.getClass().getName(), refundId, JSONUtil.toJsonStr(ipBStandplatRefund));
        }
        //标记退货类型为空的通用退已转化
        if (ipBStandplatRefund.getRefundType() == null) {
            String remark = "单据退货类型为空，标记为已转换";
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, ipBStandplatRefund);
        } else if (businessSystemParamService.standlatExchange()) {
            //查询换货订单数据
            if (IpBStandplatRefundType.EXCHANGE_GOODS == ipBStandplatRefund.getRefundType()) {
                refundRelation = omsStandplatExchangeService.selectStandplatExchangeInfo(refundId);
                return refundRelation;
            }
        }

        //通过退单明细数据
        List<IpBStandplatRefundItem> ipBStandplatRefundItems = standplatRefundItemMapper.selectRefundItemByRefundId(ipBStandplatRefund.getId());
        if (ipBStandplatRefundItems == null) {
            ipBStandplatRefundItems = new ArrayList<>();
        }
        if (StringUtils.isNotEmpty(ipBStandplatRefund.getOrderNo())) {
            IpBStandplatOrder ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(ipBStandplatRefund.getOrderNo());
            if (ipBStandplatOrder != null) {
                refundRelation.setIpBStandplatOrder(ipBStandplatOrder);
            }
        }

        refundRelation.setIpBStandplatRefund(ipBStandplatRefund);
        refundRelation.setIpBStandplatRefundItem(ipBStandplatRefundItems);

        Set<String> subOrderIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(ipBStandplatRefundItems)) {
            subOrderIds = ipBStandplatRefundItems.stream().filter(refundItem -> refundItem.getSubOrderId() != null).
                    map(IpBStandplatRefundItem::getSubOrderId).collect(Collectors.toSet());
        }
        List<OcBOrder> ocBOrders = new ArrayList<>();
        String orderNo = ipBStandplatRefund.getOrderNo();
        //List<Long> idList = new ArrayList<>();
        //如果是SAP下发退单，则通过OMS单号查询零售发货单ID
        Integer platmform;
        String reserveVarchar05 = ipBStandplatRefund.getReserveVarchar05();
        if (StringUtils.isNotEmpty(reserveVarchar05)) {
            platmform = Integer.valueOf(reserveVarchar05);
        } else {
            platmform = Integer.valueOf(ipBStandplatRefund.getCpCPlatformEcode());
        }
        if (PlatFormEnum.SAP.getCode().equals(platmform) || PlatFormEnum.DMS.getCode().equals(platmform)) {
            Long id = ES4Order.getIdByBillNo(ipBStandplatRefund.getOriginalBillNo());
            OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
            if (ocBOrder != null) {
                ocBOrders.add(ocBOrder);
            }
        } else if (IpBStandplatRefundType.RESEND == NumUtil.init(ipBStandplatRefund.getRefundType())) {
            ocBOrders = queryOmsOrdersByTid(ipBStandplatRefund);
        } else {
            List<Long> idList = ocBOrderMapper.selectOcBOrderIdByTid(orderNo);
            if (CollectionUtils.isEmpty(idList)) {
                return refundRelation;
            }
            ocBOrders = ocBOrderMapper.selectByIdsList(idList);
        }
        if (CollectionUtils.isEmpty(ocBOrders)) {
            return refundRelation;
        }
        ocBOrders = ocBOrders.stream().distinct().collect(Collectors.toList());

        //此处订单已经去重
        List<OmsOrderRelation> omsOrderRelations = new ArrayList<>();
        //赠品订单
        List<OmsOrderRelation> isGiftOrderRelation = new ArrayList<>();
        for (OcBOrder ocBOrder : ocBOrders) {
            Long orderId = ocBOrder.getId();
            // 如果是抖音平台 则 isGiftOrderRelation不取平台赠品 只取系统赠品
            //是否为赠品订单
            if (PlatFormEnum.DOU_YIN.getCode().equals(platmform)) {
                OmsOrderRelation giftOrderRelation = omsReturnOrderService.checkIsSystemGiftOrder(ocBOrder);
                if (giftOrderRelation != null) {
                    List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(orderId);
                    giftOrderRelation.setOrderDeliveries(ocBOrderDeliveries);
                    isGiftOrderRelation.add(giftOrderRelation);
                    continue;
                }
            } else {
                OmsOrderRelation giftOrderRelation = omsReturnOrderService.checkIsGiftOrder(ocBOrder);
                if (giftOrderRelation != null) {
                    List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(orderId);
                    giftOrderRelation.setOrderDeliveries(ocBOrderDeliveries);
                    isGiftOrderRelation.add(giftOrderRelation);
                    continue;
                }
            }

            OmsOrderRelation omsOrderRelation = new OmsOrderRelation();
            //根据订单查询需要退货的明细
            List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemByOoIds4CommonRefund(orderId, orderNo, subOrderIds);
            //根据订单查询需要退货的明细
            if (CollectionUtils.isEmpty(ocBOrderItems)) {
                continue;
            }
            List<OcBOrderItem> orderItemsGift = ocBOrderItemMapper.selectOrderItemsGiftList(orderId, orderNo, subOrderIds);
            List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(orderId);
            omsOrderRelation.setOrderDeliveries(ocBOrderDeliveries);
            omsOrderRelation.setOcBOrder(ocBOrder);
            omsOrderRelation.setOcBOrderItems(ocBOrderItems);
            if (CollectionUtils.isNotEmpty(orderItemsGift)) {
                Map<String, List<OcBOrderItem>> stringListMap = this.giftHandle(orderItemsGift);
                List<OmsOrderRelation.OcOrderGifts> gifts = this.getOcOrderGifts(ocBOrderItems, stringListMap);
                omsOrderRelation.setOcOrderGifts(gifts);
            }
            omsOrderRelations.add(omsOrderRelation);
        }
        refundRelation.setOmsOrderRelation(omsOrderRelations);
        refundRelation.setIsGiftOrderRelation(isGiftOrderRelation);
        IntermediateTableRelation intermediateTableRelation = new IntermediateTableRelation();
        intermediateTableRelation.setIpBStandplatRefund(ipBStandplatRefund);
        refundRelation.setIntermediateTableRelation(intermediateTableRelation);
        return refundRelation;
    }

    public OmsStandPlatRefundRelation selectStandplatRefundRelationBak(String refundId) {

        OmsStandPlatRefundRelation refundRelation = new OmsStandPlatRefundRelation();
        //通用退单主表信息
        IpBStandplatRefund ipBStandplatRefund = standplatRefundMapper.selectStandplatRefundByRefundId(refundId);
        if (ipBStandplatRefund == null) {
            return null;
        }
        //通过退单明细数据
        List<IpBStandplatRefundItem> ipBStandplatRefundItems = standplatRefundItemMapper.selectRefundItemByRefundId(ipBStandplatRefund.getId());
        refundRelation.setIpBStandplatRefund(ipBStandplatRefund);
        refundRelation.setIpBStandplatRefundItem(ipBStandplatRefundItems);
        //子订单号  对应订单明细的ooid
        String subOrderId = ipBStandplatRefund.getSubOrderId();

        List<Long> ids = ES4Order.findIdsByOid(subOrderId);

        if (CollectionUtils.isEmpty(ids)) {
            return refundRelation;
        }

        // 去重
        List<Long> idList = ids.stream().distinct().collect(Collectors.toList());

        //此处订单已经去重
        List<OmsOrderRelation> omsOrderRelations = new ArrayList<>();
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(idList);
        for (OcBOrder ocBOrder : ocBOrders) {
            OmsOrderRelation omsOrderRelation = new OmsOrderRelation();
            //根据订单查询需要退货的明细
            List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemByOoid4CommonRefund(ocBOrder.getId(), subOrderId);
            List<OcBOrderItem> orderItemsGift = ocBOrderItemMapper.selectOrderItemGiftList(ocBOrder.getId(), subOrderId);

            List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
            omsOrderRelation.setOrderDeliveries(ocBOrderDeliveries);
            omsOrderRelation.setOcBOrder(ocBOrder);
            omsOrderRelation.setOcBOrderItems(ocBOrderItems);
            //循环明细数据 todo 暂时不考虑换货
            if (CollectionUtils.isNotEmpty(orderItemsGift)) {
                Map<String, List<OcBOrderItem>> stringListMap = this.giftHandle(orderItemsGift);
                List<OmsOrderRelation.OcOrderGifts> gifts = this.getOcOrderGifts(ocBOrderItems, stringListMap);
                omsOrderRelation.setOcOrderGifts(gifts);
            }
            omsOrderRelations.add(omsOrderRelation);
        }
        refundRelation.setOmsOrderRelation(omsOrderRelations);
        return refundRelation;
    }


    /**
     * 处理正常赠品 及 挂靠赠品
     *
     * @param ocBOrderItems
     * @param stringListMap
     * @return
     */
    private List<OmsOrderRelation.OcOrderGifts> getOcOrderGifts(List<OcBOrderItem> ocBOrderItems, Map<String, List<OcBOrderItem>> stringListMap) {
        List<OmsOrderRelation.OcOrderGifts> gifts = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            if (ocBOrderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                continue;
            }
            List<OcBOrderItem> orderItems = stringListMap.get("1");
            if (CollectionUtils.isNotEmpty(orderItems)) {
                OmsOrderRelation.OcOrderGifts ocOrderGifts = new OmsOrderRelation.OcOrderGifts();
                ocOrderGifts.setOcBOrderGifts(orderItems);
                //不是挂靠赠品
                ocOrderGifts.setGiftMark(1);
                gifts.add(ocOrderGifts);
                stringListMap.remove("1");
            }
            String giftRelation = ocBOrderItem.getGiftRelation();
            orderItems = stringListMap.get(giftRelation);
            if (CollectionUtils.isNotEmpty(orderItems)) {
                OmsOrderRelation.OcOrderGifts ocOrderGifts = new OmsOrderRelation.OcOrderGifts();
                ocOrderGifts.setOcBOrderGifts(orderItems);
                //挂靠赠品
                ocOrderGifts.setGiftMark(2);
                gifts.add(ocOrderGifts);
                stringListMap.remove(giftRelation);
            }
        }
        return gifts;
    }

    /**
     * 对赠品及下挂赠品的处理
     *
     * @param orderItemsGift
     * @return
     */
    private Map<String, List<OcBOrderItem>> giftHandle(List<OcBOrderItem> orderItemsGift) {
        Map<String, List<OcBOrderItem>> giftList = new HashMap<>(10);
        for (OcBOrderItem orderItemGift : orderItemsGift) {
            //判断明细是否有挂靠赠品及 订单下是否有赠品
            String giftRelation = orderItemGift.getGiftRelation();
            if (StringUtils.isEmpty(giftRelation)) {
                //不为下挂商品
                giftRelation = "1";
            }
            if (!giftList.containsKey(giftRelation)) {
                List<OcBOrderItem> list = new ArrayList<>();
                list.add(orderItemGift);
                giftList.put(giftRelation, list);
            } else {
                List<OcBOrderItem> list = giftList.get(giftRelation);
                list.add(orderItemGift);
                giftList.put(giftRelation, list);
            }
        }
        return giftList;
    }


    private String getRefundKey() {
        return "REFUND:ORDER:ISOOID";
    }

    /*private void removeMergerOrderItem(Long orderId, OcBOrder ocBOrder, boolean containsTid, List<OcBOrderItem> ocBOrderItems, List<OcBOrderItem> orderItemsGift) {
        if (whereKeys.containsKey("TID") && containsTid && Objects.equals(ocBOrder.getIsMerge(), 1)) {
            List<OcBOrder> sourceOrder = ocBOrderMapper.selectList(new LambdaQueryWrapper<OcBOrder>().ne(OcBOrder::getId, orderId).eq(OcBOrder::getTid, ocBOrder.getTid()));
            if (CollectionUtils.isNotEmpty(sourceOrder)) {
                orderId = sourceOrder.get(0).getId();
            }
            List<OcBOrderItem> itemList = ocBOrderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().lambda().eq(OcBOrderItem::getOcBOrderId, orderId));
            if (itemList.isEmpty()){
                return;
            }
            itemList.stream().map(OcBOrderItem::get)
        }
    }*/

    /**
     * 根据tid查询订单
     *
     * @param ipBStandplatRefund
     * @return
     */
    private List<OcBOrder> queryOmsOrdersByTid(IpBStandplatRefund ipBStandplatRefund) {
        List<Long> orderIds = ocBOrderItemMapper.selectOcBOrderIdByTid(ipBStandplatRefund.getOrderNo());
        if (CollectionUtils.isEmpty(orderIds)) {
            return null;
        }
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(orderIds);
        return ocBOrders;
    }
}
