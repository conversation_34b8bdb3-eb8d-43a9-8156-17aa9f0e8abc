package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderNodeRecordMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderNodeRecord;
import com.jackrain.nea.oc.oms.nums.ReturnOrderNodeEnum;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * className: OcBReturnOrderNodeRecordService
 * description: 退换货单节点触发时间记录表服务
 *
 * <AUTHOR>
 * create: 2021-12-08
 * @since JDK 1.8
 */
@Component
@Slf4j
public class OcBReturnOrderNodeRecordService {

    @Autowired
    private OcBReturnOrderNodeRecordMapper nodeRecordMapper;

    public ValueHolderV14<String> saveNodeRecord(OcBReturnOrderNodeRecord nodeRecord, User user){

        int num;
        CommandAdapterUtil.defaultOperator(nodeRecord,user);
        if (nodeRecord.getId() < 0) {
            //新增
            nodeRecord.setId(ModelUtil.getSequence(OcCommonConstant.OC_B_RETURN_ORDER_NODE_RECORD));
            nodeRecord.setOwnerename(user.getEname());
            nodeRecord.setModifierename(user.getEname());
            num = nodeRecordMapper.insert(nodeRecord);
        }else {
            //更新
            nodeRecord.setModifierename(user.getEname());
            num = nodeRecordMapper.updateById(nodeRecord);
        }

        if(num > 0){
            return new ValueHolderV14<>(ResultCode.SUCCESS,"保存成功");
        }else {
            return new ValueHolderV14<>(ResultCode.FAIL,"保存失败");
        }
    }

    public void insertByNode(ReturnOrderNodeEnum nodeEnum, Date operateTime, Long returnOrderId, User user){

        try {
            if(log.isDebugEnabled()){
                log.debug(" 新增退换货单节点信息，节点-{}，退换货单编号-{}", JSON.toJSONString(nodeEnum),returnOrderId);
            }
            OcBReturnOrderNodeRecord nodeRecord = new OcBReturnOrderNodeRecord();
            nodeRecord.setId(-1L);
            nodeRecord.setNode(nodeEnum.getValue());
            nodeRecord.setNodeOperateTime(operateTime);
            nodeRecord.setOcBReturnOrderId(returnOrderId);
            ValueHolderV14 v14 = saveNodeRecord(nodeRecord,user);
            if(!v14.isOK()){
                log.error(" 新增退换货单节点信息失败：" + v14.getMessage());
            }
        }catch (Exception e){
            log.error(" 新增退换货单节点信息异常：{}", Throwables.getStackTraceAsString(e));
        }

    }


    public void batchInsert(ReturnOrderNodeEnum nodeEnum, Date operateTime, String returnOrderId, User user){

        try {
            if(log.isDebugEnabled()){
                log.debug(" 批量新增退换货单节点信息，节点-{}，退换货单编号-{}", JSON.toJSONString(nodeEnum),returnOrderId);
            }
            if(ObjectUtils.isEmpty(returnOrderId)){
                return;
            }
            List<OcBReturnOrderNodeRecord> insertList = new ArrayList<>();
            for(String id : returnOrderId.split(",")){
                OcBReturnOrderNodeRecord nodeRecord = new OcBReturnOrderNodeRecord();
                nodeRecord.setNode(nodeEnum.getValue());
                nodeRecord.setNodeOperateTime(operateTime);
                nodeRecord.setOcBReturnOrderId(Long.valueOf(id));
                CommandAdapterUtil.defaultOperator(nodeRecord,user);
                nodeRecord.setId(ModelUtil.getSequence(OcCommonConstant.OC_B_RETURN_ORDER_NODE_RECORD));
                nodeRecord.setOwnerename(user.getEname());
                nodeRecord.setModifierename(user.getEname());
                insertList.add(nodeRecord);
            }

            int result = nodeRecordMapper.batchInsert(insertList);
            if(result < 1){
                log.error(" 批量新增退换货单节点信息失败");
            }
        }catch (Exception e){
            log.error(" 批量新增退换货单节点信息异常：{}", Throwables.getStackTraceAsString(e));
        }

    }

    public void batchInsertNotExist(ReturnOrderNodeEnum nodeEnum, Date operateTime, List<Long> returnOrderIds, User user) {
        try {
            if(log.isDebugEnabled()){
                log.debug(" 批量新增退换货单未入库节点信息，节点-{}，退换货单编号-{}", JSON.toJSONString(nodeEnum),returnOrderIds);
            }
            if(CollectionUtils.isEmpty(returnOrderIds)){
                return;
            }
            List<String> insertReturnIds = new ArrayList<>();
            List<OcBReturnOrderNodeRecord> returnOrderNodeRecords = nodeRecordMapper.selectList(
                    new LambdaQueryWrapper<OcBReturnOrderNodeRecord>()
                            .eq(OcBReturnOrderNodeRecord::getNode,nodeEnum.getValue())
                            .in(OcBReturnOrderNodeRecord::getOcBReturnOrderId,returnOrderIds));
            String exist = "";
            if(CollectionUtils.isNotEmpty(returnOrderNodeRecords)){
                exist = returnOrderNodeRecords.stream().map(r -> r.getOcBReturnOrderId() + "")
                        .collect(Collectors.joining(","));
            }
            for(Long id : returnOrderIds){
                if(!("," + exist + ",").contains("," + id + ",")){
                    insertReturnIds.add(id + "");
                }
            }
            if(CollectionUtils.isNotEmpty(insertReturnIds)){
                this.batchInsert(nodeEnum,operateTime,String.join(",",insertReturnIds),user);
            }
        }catch (Exception e){
            log.error(" 批量新增退换货单未入库节点信息异常：{}",Throwables.getStackTraceAsString(e));
        }

    }

}
