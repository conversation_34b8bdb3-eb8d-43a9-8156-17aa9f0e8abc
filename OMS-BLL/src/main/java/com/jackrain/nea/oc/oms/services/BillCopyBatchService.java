package com.jackrain.nea.oc.oms.services;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.stocksync.common.OmsConstantsIF;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.BillCopyMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.nums.OcOrderBillCopyTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.st.service.StCBusinessTypeService;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * Created with IntelliJ IDEA.
 *
 * @Author: jg.zhan
 * @Date: 2022/6/24 11:30
 * @Description: 批量复制单据
 */
@Slf4j
@Component
public class BillCopyBatchService {
    @Autowired
    BillCopyMapper billCopyMapper;
    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    CpRpcService cpRpcService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private BillCopyBatchService billCopyBatchService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private StCBusinessTypeService stCBusinessTypeService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private SaveBillService saveBillService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    /**
     * 满足复制条件的订单业务类型：电商销售订单、线上奶卡销售、线上免费奶卡、奶卡提货、免费奶卡提货、周期购提货;
     */
    private static final List<String> okCopyBusinessTypeList = Lists.newArrayList(
            OrderBusinessTypeCodeEnum.E_COMMERCE_SALE_ORDER.getCode(),
            OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode(),
            OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD.getCode(),
            OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS.getCode(),
            //OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_PICK_UP_GOODS.getCode(),
            OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS.getCode());


    /**
     * 订单复制
     *
     * @param obj  参数
     * @param user 用户
     * @return 订单信息
     */
    public ValueHolder billCopy(JSONObject obj, User user) {

        ValueHolder vh = new ValueHolder();
        JSONObject resultData = null;
        //ids
        String ids = obj.getString("IDS");
        //转成数组
        JSONArray idArray = JSON.parseArray(ids);
        if (CollectionUtils.isEmpty(idArray)) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("请选择需要复制的记录!", user.getLocale()));
            return vh;
        }
        List<Long> oldOrderIdList = JSONObject.parseArray(ids, Long.class);
        //type:1单据复制 2丢单复制
        Integer type = obj.getInteger("TYPE");
        OcOrderBillCopyTypeEnum copyEnumByType = OcOrderBillCopyTypeEnum.getCopyEnumByType(type);
        //批量复制走异步任务处理
        if (oldOrderIdList.size() > 1) {
            //插入我的任务里
            AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
            asyncTaskBody.setTaskId(UUID.randomUUID().toString());
            asyncTaskBody.setMenu("单据批量复制");
            asyncTaskBody.setTaskType("批量复制");
            //任务开始
            asyncTaskManager.beforeExecute(user, asyncTaskBody);
            commonTaskExecutor.submit(() -> {
                JSONObject res = new JSONObject();
                res.put("message", "成功复制" + oldOrderIdList.size() + "条单据");
                res.put("code", ResultCode.SUCCESS);
                try {
                    //复制单据
                    billCopyBatchService.copyBill(copyEnumByType, oldOrderIdList, user);
                    log.info(" 批量复制单据任务返回结果为:{}", "批量复制执行成功！");
                    //任务完成
                    asyncTaskBody.setUrl(null);
                } catch (Exception e) {
                    log.error(LogUtil.format("批量复制单据异常：{}", "Error"), e);
                    res.put("message", e.getMessage());
                    res.put("code", ResultCode.FAIL);
                }
                asyncTaskManager.afterExecute(user, asyncTaskBody, res);
            });
            vh.put("code", ResultCode.SUCCESS);
            vh.put("data", asyncTaskBody.getId());
            vh.put("message", Resources.getMessage("执行成功，单据批量复制任务开始！"));
            return vh;
        } else {
            try {
                int iid = NumberUtils.toInt(oldOrderIdList.get(0).toString());
                //复制单据
                resultData = copyBill(copyEnumByType, iid, user, false);
            } catch (NDSException ndsException) {
                log.error(LogUtil.format("复制单据异常！{}", "Error"), ndsException);
                vh.put("code", ResultCode.FAIL);
                vh.put("message", Resources.getMessage(ndsException.getMessage(), user.getLocale()));
                return vh;
            }
        }
        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", Resources.getMessage("成功", user.getLocale()));
        vh.put("data", resultData);
        return vh;
    }

    private JSONObject copyBill(OcOrderBillCopyTypeEnum copyEnumByType, int iid, User user, boolean isBatch) {
        JSONObject resultData;
        //检查ID是否存在
        OcBOrder oldOcBOrder = ocBOrderMapper.selectById(iid);
        //校验是否符合复制条件
        checkData(oldOcBOrder, copyEnumByType);
        switch (copyEnumByType) {
            case UNKNOW:
                throw new NDSException("未知的复制单据类型！");
            case NORMAL_COPY:
                resultData = copyOrderData(copyEnumByType.getName(), iid, oldOcBOrder, user, copyEnumByType, isBatch);
                //数据特殊处理
                break;
            case ORIGINAL_INVALID_COPY:
                resultData = copyOrderData(copyEnumByType.getName(), iid, oldOcBOrder, user, copyEnumByType, isBatch);
                //数据特殊处理
                break;
            case NEW_RETURN:
                resultData = copyOrderData(copyEnumByType.getName(), iid, oldOcBOrder, user, copyEnumByType, isBatch);
                //数据特殊处理
                break;
            default:
                throw new NDSException("未知的复制单据类型！");
        }
        return resultData;
    }

    @Transactional(rollbackFor = Exception.class)
    public void copyBill(OcOrderBillCopyTypeEnum copyEnumByType, List<Long> oldOrderIdList, User user) {
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectBatchIds(oldOrderIdList);
        if (ocBOrderList.size() != ocBOrderList.size()) {
            throw new NDSException("全部或部分订单不符合要求，不能复制。");
        }
        for (OcBOrder oldOcBOrder : ocBOrderList) {
            //校验是否符合复制条件
            checkData(oldOcBOrder, copyEnumByType);
        }
        oldOrderIdList.forEach(iid -> {
            Integer orderId = new Integer(String.valueOf(iid));
            switch (copyEnumByType) {
                case UNKNOW:
                    throw new NDSException("未知的复制单据类型！");
                case NORMAL_COPY:
                    copyBill(copyEnumByType, orderId, user, true);
                    //saveCopyOrderInfo(copyEnumByType.getName(), iid.intValue(), user);
                    break;
                case ORIGINAL_INVALID_COPY:
                    //数据特殊处理
                    //saveCopyOrderInfo(copyEnumByType.getName(), iid.intValue(), user);
                    copyBill(copyEnumByType, orderId, user, true);
                    break;
                default:
                    throw new NDSException("未知的复制单据类型！");
            }
        });
    }


    /**
     * 校验数据是否符合
     *
     * @param oldOcBOrder
     * @param copyEnumByType
     */
    private void checkData(OcBOrder oldOcBOrder, OcOrderBillCopyTypeEnum copyEnumByType) {
        if (ObjectUtils.isEmpty(oldOcBOrder)) {
            throw new NDSException("数据库不存在该订单Id!");
        }
        //纯虚拟商品订单不允许复制
        if (null != oldOcBOrder.getIsInvented() && OmsConstantsIF.YES == oldOcBOrder.getIsInvented()) {
            throw new NDSException("此为虚拟订单不支持复制!");
        }
        /**
         * 查询业务类型设置是否允许复制，且订单标签不带有【复】【补】。
         * 其余订单类型操作复制订单提示XX类型不允许复制。如选中多单执行复制，其中有部分单据不符合条件，则全部不执行复制，提示失败信息。
         */
        Long businessTypeId = oldOcBOrder.getBusinessTypeId();
        if (Objects.isNull(businessTypeId)) {
            throw new NDSException("业务类型为空，不允许复制!");
        }
        StCBusinessType stCBusinessType = stCBusinessTypeService.selectOneById(businessTypeId);

        switch (copyEnumByType) {
            case UNKNOW:
                throw new NDSException("未知的复制单据类型！");
            case NORMAL_COPY:
                if (!YesNoEnum.Y.getVal().equals(stCBusinessType.getIsAllowCopy())) {
                    throw new NDSException(oldOcBOrder.getBusinessTypeName() + "类型的设置为不允许复制!");
                }
                if (YesNoEnum.Y.getVal().equals(oldOcBOrder.getIsCopyOrder()) || YesNoEnum.Y.getVal().equals(oldOcBOrder.getIsResetShip())) {
                    throw new NDSException("订单标签带有【复】|【补】,不允许复制!");
                }

                //正常复制：原单状态非作废状态，才可操作此复制按钮。
                if (OmsOrderStatus.CANCELLED.toInteger().equals(oldOcBOrder.getOrderStatus())
                        || OmsOrderStatus.SYS_VOID.toInteger().equals(oldOcBOrder.getOrderStatus())) {
                    throw new NDSException("原订单状态必须为非“取消/作废”状态，才可操作此按钮。");
                }
                if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(oldOcBOrder.getOrderStatus())
                        && !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(oldOcBOrder.getOrderStatus())) {
                    throw new NDSException("只能对【仓库发货，平台发货】订单状态的原单进行复制操作");
                }

                break;
            case ORIGINAL_INVALID_COPY:
                if (!YesNoEnum.Y.getVal().equals(stCBusinessType.getIsAllowCopy())) {
                    throw new NDSException(oldOcBOrder.getBusinessTypeName() + "类型的设置为不允许复制!");
                }
                if (YesNoEnum.Y.getVal().equals(oldOcBOrder.getIsCopyOrder()) || YesNoEnum.Y.getVal().equals(oldOcBOrder.getIsResetShip())) {
                    throw new NDSException("订单标签带有【复】|【补】,不允许复制!");
                }
                //原单无效复制：原单必须为作废状态才可操作此复制按钮
                if (!OmsOrderStatus.CANCELLED.toInteger().equals(oldOcBOrder.getOrderStatus())
                        && !OmsOrderStatus.SYS_VOID.toInteger().equals(oldOcBOrder.getOrderStatus())) {
                    throw new NDSException("原订单状态必须为“取消/作废”才可操作此按钮。");
                }
                break;
            case NEW_RETURN:
                // 一头牛业务调整： 产品确认后调整，取消复制单、补单不能创建退单的限制 20220908
//                if (YesNoEnum.Y.getVal().equals(oldOcBOrder.getIsCopyOrder()) || YesNoEnum.Y.getVal().equals(oldOcBOrder.getIsResetShip())) {
//                    throw new NDSException("订单标签带有【复】|【补】,不允许退单!");
//                }
                if (!YesNoEnum.Y.getVal().equals(stCBusinessType.getIsAllowHandReturn())) {
                    throw new NDSException(oldOcBOrder.getBusinessTypeName() + "类型的设置为不允许手工建退单!");
                }
                //正常复制：原单状态非作废状态，才可操作此复制按钮。
                if (OmsOrderStatus.CANCELLED.toInteger().equals(oldOcBOrder.getOrderStatus())
                        || OmsOrderStatus.SYS_VOID.toInteger().equals(oldOcBOrder.getOrderStatus())) {
                    throw new NDSException("原订单状态必须为非“取消/作废”状态，才可操作此按钮。");
                }
                break;
            default:
                throw new NDSException("未知的复制单据类型！");
        }
    }

    /**
     * 保存复制数据
     *
     * @param type
     * @param iid
     * @param
     * @return
     */
    private void saveCopyOrderInfo(String type, int iid, User user) {
        OcBOrderRelation ocBOrderRelation = omsOrderService.selectOcBOrderRelation(iid);
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        orderInfo.setIsCopyOrder(OmsConstantsIF.YES);
        orderInfo.setCopyReason(type);
        orderInfo.setOrderStatus(OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal());
        orderInfo.setExpresscode(null);
        orderInfo.setCpCLogisticsId(null);
        orderInfo.setCpCLogisticsEcode(null);
        orderInfo.setCpCLogisticsEname(null);
        orderInfo.setSgBOutBillNo(null);
        orderInfo.setSgBOutBillId(null);
        omsOrderService.saveOmsOrderInfoByCopy(ocBOrderRelation, user);
    }


    /**
     * 复制单据
     *
     * @param type
     * @param iid
     * @param oldOcBOrder
     * @return
     */
    private JSONObject copyOrderData(String type, int iid, OcBOrder oldOcBOrder, User user, OcOrderBillCopyTypeEnum billCopyEnumByType, boolean isBatch) {
        JSONObject jsonObject = new JSONObject();
        //获取基本信息
        JSONObject orderInfoById = billCopyMapper.queryOrderInfoById(iid);
        List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectUnSuccessRefund(new Long(iid));
        //过滤掉贴纸赠品
        orderItemList = orderItemList.stream().filter(s ->
                !Objects.equals(s.getStickerGift(), YesNoEnum.Y.getVal())).collect(Collectors.toList());
        //复制付款时间
        orderInfoById.put("PAY_TIME", oldOcBOrder.getPayTime());
        //复值组合商品标记
        orderInfoById.put("IS_COMBINATION", oldOcBOrder.getIsCombination());

        //主表信息
        OcBOrder ocBOrder = JSON.parseObject(orderInfoById.toJSONString(), OcBOrder.class);
        //跟据店铺id 获取店铺信息
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        ocBOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
        // 自动打标：复制订单
        ocBOrder.setIsCopyOrder(1);
        //获取收货人信息
        JSONObject receiverInfoById = billCopyMapper.queryReceiverInfoById(iid);
        //获取备注信息
        JSONObject messageById = billCopyMapper.queryMessageById(iid);
        //旧平台单号
        String oldTid = orderInfoById.getString("SOURCE_CODE");
        //平台单号 可能为空,不是必填
        ocBOrder.setTid(oldTid);
        ocBOrder.setSuffixInfo(iid + "-CP");
        // 自动打标：复制订单
        ocBOrder.setIsCopyOrder(1);
        ocBOrder.setIsLoseCopyOrder(0);

        ocBOrder.setCopyReason(type);
        jsonObject.put("baseInfo", ocBOrder);
        jsonObject.put("receivingGoods", receiverInfoById);
        jsonObject.put("remarksInfo", messageById);

        //解密收货人信息【修改地址、复制订单，抖音平台去掉自动解密，恢复密文】
        if (!(ObjectUtil.equal(PlatFormEnum.DOU_YIN.getCode(), oldOcBOrder.getPlatform())
                || ObjectUtil.equal(PlatFormEnum.JINGDONG.getCode(), oldOcBOrder.getPlatform())
                || ObjectUtil.equal(PlatFormEnum.JINGDONG_CZ.getCode(), oldOcBOrder.getPlatform())
                || ObjectUtil.equal(PlatFormEnum.JINGDONG_DX.getCode(), oldOcBOrder.getPlatform())
        )) {
            decrypt(jsonObject, oldOcBOrder);
        }
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(iid);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (isBatch) {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    Object newOcOrder = jsonObject.get("baseInfo");
                    JSONObject object = new JSONObject();
                    object.put("ocBorderDto", newOcOrder);
                    object.put("ocBorderItemDto", orderItemList);
                    object.put("orderId", ocBOrder.getId());
                    //漏发补发
                    object.put("type", billCopyEnumByType.getType());
                    object.put("typeName", billCopyEnumByType.getName());
                    object.put("jitx_redelivery", true);
                    saveBillService.saveBill(object, user, Boolean.FALSE);
                } else {
                    log.error("订单id:{}插入重置发货数据失败,当前订单其他人在操作，请稍后再试", iid);
                    //return ValueHolderV14Utils.getFailValueHolder("当前订单其他人在操作，请稍后再试");
                }
            }
        } catch (Exception ex) {
            log.error("{},订单id:{},异常{}", this.getClass().getSimpleName(), iid, Throwables.getStackTraceAsString(ex));
            new NDSException(ex.getMessage());
        } finally {
            redisLock.unlock();
        }

        return jsonObject;
    }


    private void decrypt(JSONObject data, OcBOrder decryptOrder) {
        JSONObject receiverInfo = data.getJSONObject("receivingGoods");

        ipRpcService.decrypt(decryptOrder);
        receiverInfo.put("RECEIVER_NAME", decryptOrder.getReceiverName());
        receiverInfo.put("RECEIVER_MOBILE", decryptOrder.getReceiverMobile());
        receiverInfo.put("RECEIVER_PHONE", decryptOrder.getReceiverPhone());
        receiverInfo.put("RECEIVER_ADDRESS", decryptOrder.getReceiverAddress());

        data.put("receivingGoods", receiverInfo);
    }

}