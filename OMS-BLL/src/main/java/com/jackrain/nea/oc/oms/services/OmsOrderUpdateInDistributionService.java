package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBToWingDeliveryTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.BackflowStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLink;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 更新配货中后端服务
 *
 * @author: hulinyang
 * @since: 2019/3/21
 * create at : 2019/3/21 15:29
 */
@Component
@Slf4j

public class OmsOrderUpdateInDistributionService {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBOrderLinkService ocBOrderLinkService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OmsOrderAdvanceParseService orderAdvanceParseService;

    @Autowired
    OcBToWingDeliveryTaskMapper ocBToWingDeliveryTaskMapper;

    /**
     * 更新为“配货中”
     *
     * @param ocBOrderRelation 订单关系实体
     * @return boolean
     */
    public boolean updateInDistribution(OcBOrderRelation ocBOrderRelation, User user) {
        boolean flag = false;
        Long orderId = ocBOrderRelation.getOrderInfo().getId();
        String sgBOutBillNo = ocBOrderRelation.getOrderInfo().getSgBOutBillNo();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = omsOrderService.selectOrderInfo(orderId);
                //检查订单是否存在,若不存在，则提示“当前订单不存在！”
                flag = checkOrderIsExist(ocBOrder, user);
                if (!flag) {
                    return flag;
                }
                //判断订单状态是否在已审核或者传WMS中状态，若不是，则提示“订单状态不正确，不允许操作！”
                flag = checkBillStatus(ocBOrder, user);
                if (!flag) {
                    return flag;
                }
                //更新订单状态之前，设置分配时间，“取当前系统时间”
                ocBOrder.setDistributionTime(new Date());
                ocBOrder.setSgBOutBillNo(sgBOutBillNo);
                //更新订单状态
                flag = updateOrderStatus(ocBOrder, user);
            }

        } catch (Exception ex) {
            log.error(LogUtil.format("异常信息,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        } finally {
            redisLock.unlock();
        }
        return flag;
    }


    /**
     * 批量更新为“配货中”
     *
     * @param orderIdList 批量更新订单Id列表
     * @return boolean
     */
    public boolean batchUpdateInDistribution(List<Long> orderIdList, User user) {
        boolean flag = false;

        // @20200831 bug#prd-202008310611 为空的不处理，避免为空查数据库
        if (CollectionUtils.isEmpty(orderIdList)) {
            return false;
        }

        long starTime = System.currentTimeMillis();

        //批量更新"配货中"
        ocBOrderMapper.updateInDistributionStatusList(orderIdList);
        //传全链路日志注释掉【20191019压测】
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectOrderListByIdsList(orderIdList);
        for (OcBOrder ocBOrder : ocBOrders) {
            //接收WMS回执信息时，查询【零售发货单】的【建议预下沉】=Y，
            // 则更新【零售发货单】的【实际预下沉状态】=已通知；
            if (orderAdvanceParseService.checkIsDepositPreSale(ocBOrder)) {
                if (TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y.equals(ocBOrder.getSuggestPresinkStatus())) {
                    OcBOrder updateOcBOrder = new OcBOrder();
                    updateOcBOrder.setId(ocBOrder.getId());
                    updateOcBOrder.setActualPresinkStatus(TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_NOTIFIED_SUCCESS);
                    ocBOrderMapper.updateById(updateOcBOrder);
                }
            }
        }
        ocBOrderLinkService.addOrderFinkLogs(ocBOrders, BackflowStatus.QIMEN_CP_NOTIFY.parseValue());
        long endTime1 = System.currentTimeMillis();
        flag = true;
        return flag;
    }

    /**
     * 判断订单是否存在，若不存在，则提示“当前订单不存在！”
     *
     * @param ocBOrder 订单对象
     * @return boolean
     */
    public boolean checkOrderIsExist(OcBOrder ocBOrder, User user) {

        /*boolean flag;
        try {
            if (ocBOrder == null) {
                flag = false;
                log.error("当前订单不存在！");
            } else {
                flag = true;
            }
        } catch (Exception ex) {
            flag = false;
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                    ocBOrder.getBillNo(), OrderLogTypeEnum.DISTRIBUTION_UPDATE.getKey(),
                    ocBOrder.getId() + "更新配货中，服务异常" + ex.getMessage(), null, ex.getMessage(), user);
            log.error("判断订单是否存在执行异常{} ", ex.getMessage());
        }
        return flag;*/
        return ocBOrder != null;
    }

    /**
     * 检查订单状态
     *
     * @param ocBOrder 订单实体
     * @return boolean
     */
    public boolean checkBillStatus(OcBOrder ocBOrder, User user) {

        boolean flag;
        try {
            int orderStatus = ocBOrder.getOrderStatus();
            if (orderStatus == OmsOrderStatus.CHECKED.toInteger()
                    || orderStatus == OmsOrderStatus.PENDING_WMS.toInteger()) {
                flag = true;
            } else {
                flag = false;
            }
        } catch (Exception ex) {
            flag = false;
            if (ocBOrder != null) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                        ocBOrder.getBillNo(), OrderLogTypeEnum.DISTRIBUTION_UPDATE.getKey(), ocBOrder.getId() + "更新配货中服务异常" + ex.getMessage(), null, ex.getMessage(), user);
            }
            log.error("判断订单状态异常{} ", ex.getMessage());
        }
        return flag;
    }

    /**
     * @param ocBOrder 订单信息
     * @return boolean
     */
    public boolean updateOrderStatus(OcBOrder ocBOrder, User user) {

        boolean flag = false;
        try {
            Long orderId = ocBOrder.getId();
            OcBOrder ocBorderDto = new OcBOrder();
            ocBorderDto.setId(orderId);
            ocBorderDto.setOrderStatus(OmsOrderStatus.IN_DISTRIBUTION.toInteger());
            ocBorderDto.setDistributionTime(new Date());
            ocBorderDto.setSgBOutBillNo(ocBOrder.getSgBOutBillNo());
            flag = omsOrderService.updateOrderInfo(ocBorderDto);
            ////传全链路日志注释掉【20191019压测】
            //addAllFinkLoglog(ocBOrder, user);
            //调用订单日志服务
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.DISTRIBUTION_UPDATE.getKey(), ocBOrder.getId() + "更新配货中服务",
                    null, null, null);
        } catch (Exception ex) {
            flag = false;
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                    ocBOrder.getBillNo(), OrderLogTypeEnum.DISTRIBUTION_UPDATE.getKey(), ocBOrder.getId() + "更新配货中服务异常" + ex.getMessage(), null, ex.getMessage(), null);
            log.error(LogUtil.format("判断订单状态为“配货中”时异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));

        }
        return flag;
    }

    public void addAllFinkLoglog(OcBOrder ocBOrder, User user) {
        //调用全链路日志服务

        OcBOrderLink ocBOrderLink = new OcBOrderLink();
        //平台单号
        ocBOrderLink.setTid(ocBOrder.getTid());
        //订单编号
        ocBOrderLink.setOcBOrderId(ocBOrder.getId());
        //商家平台编码 .main:官方渠道,jd:京东,dd:当当,pp:拍拍,yx:易讯,ebay:ebay,amazon:亚马逊,sn:苏宁,gm:国美,wph:唯品会,
        //    // jm:聚美,mgj:蘑菇街,yt:银泰,yhd:1号店,1688:1688,pos:pos门店,other:其他
        ocBOrderLink.setPlatform("MAIN");
        //回流状态
        ocBOrderLink.setBackflowStatus(BackflowStatus.QIMEN_CP_NOTIFY.parseValue());
        //卖家昵称
        ocBOrderLink.setSellerNick(ocBOrder.getCpCShopSellerNick());
        //同步状态
        //TODO 枚举暂时还没建，后期建好之后把0改掉
        ocBOrderLink.setSyncStatus(SyncStatus.UNSYNC.toInteger());
        //同步时间
        ocBOrderLink.setSyncTime(new Date());
        if (PlatFormEnum.TAOBAO.getCode().equals(ocBOrder.getPlatform())) {
            //ocBOrderLinkService.addOrderFinkLog(ocBOrderLink, user);
        }
    }

}
