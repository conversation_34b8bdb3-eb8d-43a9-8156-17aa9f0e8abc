package com.jackrain.nea.oc.oms.services.returnorder;

import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillVoidResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * className: SgBStoOutNoticesRejectService
 * description: 零售退货单基础服务
 *
 * <AUTHOR>
 * create: 2021-07-06
 * @since JDK 1.8
 */
@Slf4j
@Component
@AllArgsConstructor
public class OcBReturnFromWMSWithDrawService {
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private SgRpcService sgRpcService;


    /**
     * 零售退货单--从WMS撤回操作
     *
     * @param id   id
     * @param user 用户
     * @return 标准出参
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<OcBReturnOrder> withDraw(Long id, User user, String reasonCode) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.FAIL, null);
        String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                v14 = dealWithDraw(id, user);
            } else {
                v14.setMessage("当前单据id:" + id + "正在操作, 请稍后再试");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("异常信息为:{},id=", id), Throwables.getStackTraceAsString(ex));
        } finally {
            redisLock.unlock();
        }
        return v14;
    }

    /**
     * 从WMS撤回处理逻辑
     *
     * @param id 零售退货单id
     * @return 校验结果
     */
    private ValueHolderV14<OcBReturnOrder> dealWithDraw(Long id, User user) {
        ValueHolderV14<OcBReturnOrder> v14 = new ValueHolderV14<>(ResultCode.FAIL, null);
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(id);
        if (null == ocBReturnOrder) {
            v14.setMessage("当前零售退货单ID不存在");
            return v14;
        }
        /*
        a. 当零售退货单“单据状态”不为"传WMS成功"时，提示：“当前状态，不允许从WMS撤回”
         */
        if (!Long.valueOf(OcOmsReturnOrderConstant.WMS_STATUS_PASS_SUCCESS).equals(ocBReturnOrder.getIsTowms())) {
            v14.setMessage("当前状态，不允许从WMS撤回");
            return v14;
        }

        OcBReturnOrder returnOrder = v14.getData();
        ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> holderV14 = sgRpcService
                .callReturnOrderVoidOutNoticeAndToWms(Arrays.asList(returnOrder), user);
        if (!holderV14.isOK()) {
            v14.setMessage(holderV14.getMessage());
            return v14;
        }
        v14.setCode(ResultCode.SUCCESS);
        v14.setMessage("从WMS撤回成功！");
        return v14;
    }

}
