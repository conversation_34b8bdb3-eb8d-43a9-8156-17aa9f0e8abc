package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.table.StCRemarkGiftStrategy;
import com.jackrain.nea.st.model.StCRemarkGiftStrategyRelation;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 备注赠品策略service
 *
 * <AUTHOR>
 */
public interface StCRemarkGiftStrategyService {

    /**
     * 新增
     *
     * @param relation 策略
     * @param user 用户
     * @return
     * @throws NDSException
     */
    ValueHolderV14<Long> save(StCRemarkGiftStrategyRelation relation, User user) throws NDSException;

    /**
     * 更新
     *
     * @param relation 策略
     * @param before 修改前数据
     * @param after 修改后数据
     * @param user 用户
     * @param id 主键
     * @return
     * @throws NDSException
     */
    ValueHolderV14<Void> update(StCRemarkGiftStrategyRelation relation, J<PERSON><PERSON>Object before, JSONObject after, User user, Long id) throws NDSException;

    /**
     * 校验规则
     * @param strategy
     */
    void checkRule(StCRemarkGiftStrategy strategy);

    /**
     * 添加赠品
     * @param shopId
     * @param tid
     */
    boolean addGiftSku(Long shopId, String tid);
}