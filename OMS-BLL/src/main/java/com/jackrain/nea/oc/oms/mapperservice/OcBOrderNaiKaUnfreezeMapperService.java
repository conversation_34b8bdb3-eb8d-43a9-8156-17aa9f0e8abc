package com.jackrain.nea.oc.oms.mapperservice;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaUnfreezeMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaUnfreeze;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * @ClassName OcBOrderNaiKaUnfreezeMapperService
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/6/30 17:37
 * @Version 1.0
 */
@Service
@Slf4j
public class OcBOrderNaiKaUnfreezeMapperService extends ServiceImpl<OcBOrderNaiKaUnfreezeMapper, OcBOrderNaikaUnfreeze> {

    public OcBOrderNaikaUnfreeze selectUnFreezeNaiKaOrder(Long ocBOrderId) {
        return baseMapper.selectOne(new QueryWrapper<OcBOrderNaikaUnfreeze>().lambda()
                .eq(OcBOrderNaikaUnfreeze::getOcBOrderId, ocBOrderId).eq(OcBOrderNaikaUnfreeze::getIsactive, "Y"));
    }

    @Override
    public boolean updateById(OcBOrderNaikaUnfreeze unfreeze) {
        Assert.notNull(unfreeze.getOcBOrderId(), "OcBOrderId不能为空");
        Assert.notNull(unfreeze.getId(), "id不能为空");
        Wrapper<OcBOrderNaikaUnfreeze> updateWrapper = new UpdateWrapper<OcBOrderNaikaUnfreeze>().lambda()
                .eq(OcBOrderNaikaUnfreeze::getOcBOrderId, unfreeze.getOcBOrderId())
                .eq(OcBOrderNaikaUnfreeze::getId, unfreeze.getId());
        return baseMapper.update(unfreeze, updateWrapper) > 0;
    }

}
