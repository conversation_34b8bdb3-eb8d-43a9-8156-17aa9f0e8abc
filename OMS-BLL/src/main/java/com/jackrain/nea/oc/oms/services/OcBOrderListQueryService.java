package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderInvoiceInformMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemFiMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.model.enums.IsForbiddenDeliveryEnum;
import com.jackrain.nea.oc.oms.model.enums.LogisticsStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderFlagEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.BasePermission;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.Page;
import com.jackrain.nea.oc.oms.model.relation.QueryOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.request.OrderQueryRequest;
import com.jackrain.nea.oc.oms.model.result.QueryOrderByBillNoResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderFlagResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderItemResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderListResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderTagResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderInvoiceInform;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderReason;
import com.jackrain.nea.oc.oms.permission.OcOrderAuthorityMgtService;
import com.jackrain.nea.oc.oms.util.ElasticSearchUtil4Csm;
import com.jackrain.nea.oc.request.OcBOederQueryByBillNoRequest;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.StCHoldOrderReasonQueryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.jackrain.nea.resource.RedisKeyConst.CP_PLATFORM_ALL;

/**
 * 订单列表
 *
 * @author: xiwen.z
 * create at: 2019/3/11 0011
 */
@Component
@Slf4j
public class OcBOrderListQueryService {

    final String labelTag = "CP_C_LABEL_ID";
    /**
     * 最大导出数量限制
     */
    private final static String EXPORT_MAX_ROW_NUM_LIMIT = "export_max_row_num_limit";

    /**
     * 最大导出数量默认值
     */
    private final static int DEF_EXPORT_MAX_ROW_NUM_LIMIT = 1000000;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemFiMapper ocBorderItemMapper;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private SgRpcService sgRpcPhyOutQueryService;
    @Autowired
    private PsRpcService psRpcCProdimItemQueryService;
    @Autowired
    private OcBOrderInvoiceInformMapper ocBOrderInvoiceInformMapper;
    @Autowired
    private OcOrderAuthorityMgtService ocOrderAuthorityMgtService;
    @Autowired
    private ChannelTypeService channelTypeService;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private QueryOrderListService queryOrderListService;
    @Autowired
    private StCHoldOrderReasonQueryService holdOrderReasonQueryService;

    @Autowired
    private StCBusinessTypeMapper stCBusinessTypeMapper;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    /**
     * 库存中心查零售发货单
     *
     * @param request
     * @return
     */
    public ValueHolderV14<QueryOrderByBillNoResult> queryOrderByBillNoList(OcBOederQueryByBillNoRequest request) {

        List<String> billNoList = request.getBillNoList();

        ValueHolderV14<QueryOrderByBillNoResult> v14 = new ValueHolderV14<>();
        OcBOrderMapper orderMapper = ApplicationContextHandle.getBean(OcBOrderMapper.class);
        if (CollectionUtils.isEmpty(billNoList)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("入参数为空！");
            return v14;

        }
        try {
            log.info("OcBOrderListQueryService.queryOrderByBillNoList billNoList.size = {}", billNoList.size());

            List<List<String>> pageList =
                    getPageList(billNoList, 500);
            List<OcBOrder> ocOrderList = new ArrayList<>();

            for (List<String> billNos : pageList) {
                List<OcBOrder> ocOrders = orderMapper.queryOrderByBillNo(billNos);
                if (CollectionUtils.isNotEmpty(ocOrders)) {
                    ocOrderList.addAll(ocOrders);
                }
            }

            if (CollectionUtils.isNotEmpty(ocOrderList)) {
                Map<String, Integer> statusMap = ocOrderList.stream().collect(Collectors.toMap(OcBOrder::getBillNo,
                        OcBOrder::getOrderStatus, (v1, v2) -> v1));
                QueryOrderByBillNoResult queryOrderByBillNoResult = new QueryOrderByBillNoResult();
                queryOrderByBillNoResult.setResultMap(statusMap);
                v14.setData(queryOrderByBillNoResult);
            }
        } catch (Exception e) {
            log.error("OcBOrderListQueryService.queryOrderByBillNoList error={}", e.getMessage());
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
            return v14;
        }

        v14.setCode(ResultCode.SUCCESS);
        v14.setMessage("查询成功！");
        return v14;
    }

    /**
     * 分页
     *
     * @param sourceList
     * @param pageSize
     * @param <T>
     * @return
     */
    public <T> List<List<T>> getPageList(List<T> sourceList, int pageSize) {

        List<List<T>> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(sourceList)) {
            return resultList;
        }

        int listSize = sourceList.size();
        int page = listSize / pageSize;
        if (listSize % pageSize != 0) {
            page++;
        }

        for (int i = 0; i < page; i++) {

            List<T> pageList = sourceList.subList(i * pageSize,
                    (((i + 1) * pageSize > listSize ? listSize : pageSize * (i + 1))));
            resultList.add(pageList);
        }
        return resultList;
    }

    /**
     * check and init search conditions
     */
    private static final Consumer<OrderQueryRequest> checkInitFun = o -> {

        if (o.getWhere() == null) {
            o.setWhere(new JSONObject());
        }
        if (o.getFilter() == null) {
            o.setFilter(new JSONObject());
        }
        if (o.getChild() == null) {
            o.setChild(new JSONObject());
        }
        if (o.getField() == null) {
            o.setField(new String[]{"ID"});
        }
        if (o.getOrder() == null) {
            o.setOrder(new JSONArray());
        }
        if (o.getExistKeys() == null) {
            o.setExistKeys(new JSONObject());
        }
        if (o.getNotExistKeys() == null) {
            o.setNotExistKeys(new JSONObject());
        }
    };

    private void buildOrderQueryRequest(QueryOrderRelation queryOrderRelation, JSONObject jsonStr,
                                        JSONObject whereKeyJo, JSONObject filterKeyJo, JSONObject childsKeys,
                                        OrderQueryRequest queryDto) {
        checkInitFun.accept(queryDto);
        JSONObject pageJo = jsonStr.getJSONObject("page");
        int index = OcBOrderConst.PAGE_NUM;

        int size = OcBOrderConst.PAGE_SIZE;
        if (Objects.nonNull(pageJo)) {
            size = pageJo.getIntValue("pageSize");
            // current es not support long type
            index = pageJo.getIntValue("pageNum");
        }
        if (index < 1) {
            index = 1;
        }
        if (size < 1) {
            size = 20;
        }
        splitPageHandler(pageJo, queryOrderRelation.getPage(), queryOrderRelation);
        int start = (index - 1) * size;
        queryDto.setStart(start);
        queryDto.setIndex(index);
        queryDto.setSize(size);
        queryDto.setWhere(whereKeyJo);
        queryDto.setFilter(filterKeyJo);
        queryDto.setChild(childsKeys);
        // 1.1 增加TOB或TOC标识筛选订单数据
        List<Long> businessTypeIdList = stCBusinessTypeMapper.getBusinessTypeIdByToB();
        ElasticSearchOrderService.getToBOrToCParam(businessTypeIdList, jsonStr);
        ElasticSearchOrderService.paramConditionHandler(jsonStr, queryDto, omsSystemConfig.getOrderQueryElasticUseKeyword());
    }

    /**
     * 查询
     *
     * @param jsonStr   jsonString
     * @param loginUser user
     * @param pem       jsonObject[]
     * @return vh vh
     */
    public ValueHolderV14<QueryOrderListResult> queryOrderList(String jsonStr, User loginUser, UserPermission pem, boolean... isExport) {
        log.info(LogUtil.format("Query UserOrder Info>>>>>>>>>>>>>"));
        PropertiesConf pconf = ApplicationContextHandle.getBean(PropertiesConf.class);
        int exportMaxRowNumLimit = pconf.getProperty(EXPORT_MAX_ROW_NUM_LIMIT, DEF_EXPORT_MAX_ROW_NUM_LIMIT);
        ValueHolderV14<QueryOrderListResult> vh = new ValueHolderV14<>();
        Boolean isSearchForId = false;
        // 0.校验,解析
        if (loginUser == null || jsonStr == null || jsonStr.trim().length() < OcBOrderConst.IS_STATUS_IY) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("查询参数缺失");
            log.error(LogUtil.format("OcBOrderListQueryService.queryOrderList Error: 查询参数缺失"));
            return vh;
        }
        if (pem != null) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("#queryOrderList. 当前用户权限->IsAdmin：{},Permission：{}, UserID/UserName/UserEname "
                        , loginUser.getId(), loginUser.getName(), loginUser.getEname()), loginUser.isAdmin(), JSON.toJSONString(pem));
            }
        }
        /**
         * ES search部分
         */
        JSONObject whereKeyJo = new JSONObject();
        JSONObject filterKeyJo = new JSONObject();
        JSONObject childsKeys = new JSONObject();
        JSONObject existKeys = new JSONObject();
        JSONObject notExistKeys = new JSONObject();
        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", true);
        orderJo.put("name", "ID");
        orderJAry.add(orderJo);

        try {
            JSONObject jo = JSONObject.parseObject(jsonStr);
            if (jo.containsKey("SearchForId")) {
                isSearchForId = true;
                jo.remove("SearchForId");
            }
            if (jo == null) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("参数解析异常");
                log.error(LogUtil.format("OcBOrderListQueryService.queryOrderList Error: 参数解析异常"));
                return vh;
            }
            boolean isSearchInvoice = OcBOrderConst.SEARCH_INVOICE.equals(jo.getString(OcBOrderConst.SEARCH_KEY));
            //--方法替换 begin
            QueryOrderRelation queryOrderRelation = new QueryOrderRelation();
            Page page = new Page();
            page.setPageNum(OcBOrderConst.PAGE_NUM);
            page.setPageSize(OcBOrderConst.PAGE_SIZE);
            queryOrderRelation.setPage(page);
            OrderQueryRequest queryDto = new OrderQueryRequest();
            buildOrderQueryRequest(queryOrderRelation, jo, whereKeyJo, filterKeyJo, childsKeys, queryDto);
            recordLog("OcBOrderListQueryService.queryOrderRelation=>>" + queryOrderRelation);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("queryKeysOnElasticSearch 入参={}"), JSON.toJSONString(queryDto));
            }
            int totalSize = queryDto.getSize();
            int pageSize = totalSize;
            //如果查找返回超过10000需要分页ES
            if (totalSize > 10000) {
                pageSize = 10000;
            }
            int maxTimes = totalSize / pageSize;
            if (totalSize % pageSize != 0) {
                maxTimes += 1;
            }
            int times = 0;
            Long total = 0L;
            JSONArray dataArray = new JSONArray();
            while(true){
                queryDto.setStart(times*pageSize);
                queryDto.setSize(pageSize);
                JSONObject esOrderJoPage = ElasticSearchUtil4Csm.orderListSearch("oc_b_order", "oc_b_order", queryDto);
                total = esOrderJoPage.getLong("total");
                if(isExport!=null && isExport.length>0){
                    boolean b = isExport[0];
                    if(b && total>exportMaxRowNumLimit){
                        log.error(LogUtil.format("OcBOrderListQueryService.queryOrderList Error: 导出数据超出最大行数限制"));
                        throw new NDSException("导出数据超出最大行数限制,最大行数："+exportMaxRowNumLimit);
                    }
                }

                if(CollectionUtils.isNotEmpty(esOrderJoPage.getJSONArray("data"))){
                    dataArray.addAll(esOrderJoPage.getJSONArray("data"));
                }
                if(Objects.isNull(esOrderJoPage) || esOrderJoPage.getJSONArray("data").size()< pageSize || times>=maxTimes){
                    break;
                }
                times++;

            }

            List<Long> orderIds = splitOrderIds(total,dataArray, queryOrderRelation);
            if (isSearchForId) {
                vh.setCode(ResultCode.SUCCESS);
                QueryOrderListResult result = new QueryOrderListResult();
                result.setIds(orderIds);
                vh.setData(result);
                vh.setMessage("查询成功");
                return vh;
            }
            String sqlOrderParams = sqlHandler(orderIds);
            if (StringUtils.isBlank(sqlOrderParams)) {
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage(Resources.getMessage("未查询到数据", loginUser.getLocale()));
                return vh;
            }
            recordLog("OcBOrderListQueryService.queryOrderList.order PKid=>> " + sqlOrderParams);
            List<QueryOrderResult> orderList = ocBOrderMapper.orderListQueryByIds(sqlOrderParams,
                    OcBOrderConst.IS_ACTIVE_YES);
            QueryOrderListResult queryOrderListResult = new QueryOrderListResult();
            if (orderList == null || orderList.size() < OcBOrderConst.PAGE_NUM) {
                addSplitPageInfo(queryOrderListResult, queryOrderRelation);
                vh.setData(queryOrderListResult);
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage(Resources.getMessage("数据库中未查询到数据", loginUser.getLocale()));
                return vh;
            }
            if (CollectionUtils.isNotEmpty(orderList)) {
                List<StCShopStrategyDO> stCShopStrategyDOList = shopStrategyService.selectAllShopStrategy();
                Map<Long, StCShopStrategyDO> shopStrategyMap =
                        stCShopStrategyDOList.stream().collect(Collectors.toMap(StCShopStrategyDO::getCpCShopId, t -> t, (v, v1) -> v1));
                for (QueryOrderResult queryOrderResult : orderList) {
                    if (MapUtils.isNotEmpty(shopStrategyMap)) {
                        StCShopStrategyDO stCShopStrategyDO = shopStrategyMap.get(queryOrderResult.getCpCShopId());
                        if (stCShopStrategyDO != null) {
                            queryOrderResult.setCpCWarehouseExchangeId(stCShopStrategyDO.getCpCWarehouseDefId());
                            queryOrderResult.setCpCWarehouseExchangeName(stCShopStrategyDO.getCpCWarehouseDefName());
                            queryOrderResult.setCpCWarehouseExchangeCode(stCShopStrategyDO.getCpCWarehouseDefCode());
                        }
                    }
                }
            }
            String sqlParams = sqlHandler(orderIds);
            recordLog("OcBOrderListQueryService.queryOrderList.orderItem FKIds=>> " + sqlOrderParams);

            List<QueryOrderItemResult> ocBorderItemDtoList = new ArrayList<>();
            if (StringUtils.isNotBlank(sqlParams)) {
                JSONObject fldJsnObj = filterForbidSensitiveColumn(pem);
                ocBorderItemDtoList = ocBorderItemMapper.orderItemsQueryByOrderIds(fldJsnObj, sqlParams,
                        OcBOrderConst.IS_ACTIVE_YES);
                if (ocBorderItemDtoList != null) {
                    mergeGoodsHandler(ocBorderItemDtoList);
                }
            }

            // 开票.预
            Map<Long, List<OcBOrderInvoiceInform>> invoiceMap = null;
            if (isSearchInvoice) {
                invoiceMap = getocBOrderInvoiceInform(sqlOrderParams);
            }

            // 0.0 查询出库通知单
            List sgOutQueryResults = null;//querySgPhyOut(sgRequest);
            /*recordLog(this.getClass().getName() + " OcBOrderListQueryService.RPC查询出库通知单.结果"
                    + JSON.toJSONString(sgOutQueryResults));*/
            // 1.0  合并主子表数据
            orderUnionItems(orderList, ocBorderItemDtoList, sgOutQueryResults, invoiceMap);
            // 2.0 返回数据赋值
            queryOrderListResult.setQueryOrderResultList(orderList);
            // 3.0 加入分页信息
            addSplitPageInfo(queryOrderListResult, queryOrderRelation);
            //4.0 加入旗帜
            List<QueryOrderFlagResult> flagList = OcOrderFlagEnum.toQueryOrderFlagResult();
            queryOrderListResult.setOrderFlagList(flagList);

            vh.setData(queryOrderListResult);
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage("succuess", loginUser.getLocale()));
        } catch (Exception ex) {
            log.info(LogUtil.format("OcBOrderListQueryService.queryOrderList Error: {}"),
                    Throwables.getStackTraceAsString(ex));
            String message = ex.getMessage();
            if(StringUtils.isNotBlank(message) && message.startsWith("导出数据超出最大行数限制")){
                throw ex;
            }
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("查询订单列表失败", loginUser.getLocale()));
        }
        return vh;
    }

    /**
     * 合并主子表信息
     *
     * @param orderList         主表信息
     * @param itemResultsList   子表信息
     * @param sgOutQueryResults 出库通知单主子信息
     * @param invoiceMap        开票信息
     */
    private void orderUnionItems(List<QueryOrderResult> orderList, List<QueryOrderItemResult> itemResultsList,
                                 List sgOutQueryResults,
                                 Map<Long, List<OcBOrderInvoiceInform>> invoiceMap) {
        // 此处需要优化
        if (itemResultsList == null) {
            return;
        }
        boolean b = invoiceMap != null;

        HashOperations<String, String, String> hps = redisTemplate.opsForHash();
        Map<String, String> pm = hps.entries(CP_PLATFORM_ALL);
        // 刷单.预
        Map<Integer, String> brushOrderMap = OcBorderListEnums.ScalpingTypeEnum.getAllConvertToMap();
        // 淘宝.预售
        Map<String, String> preSaleMap = OcBorderListEnums.OrderPreSaleStatus.convertAllToHashVal();
        // 锁单
        Map<Long, String> lockMap = OcOrderLockStatusEnum.convertAllToHashVal();
        // 菜鸟作业状态
        Map<String, String> caiNiaoMap = LogisticsStatusEnum.getStatusMap();
        // 传结算状态
        Map<Long, String> toSettleMap = ToACStatusEnum.toMap();
        // SAP状态
        Map<Integer, String> sapMap = OcBorderListEnums.SendSAPEnum.toIntegerMap();

        for (QueryOrderResult o : orderList) {
            if (o == null) {
                continue;
            }
            Long tmpL = o.getId();
            if (tmpL == null || tmpL <= OcBOrderConst.ORDER_ID) {
                continue;
            }
            List<QueryOrderItemResult> tmpJo = new ArrayList<>();
            BigDecimal totQtyLost = BigDecimal.ZERO;
            for (QueryOrderItemResult i : itemResultsList) {
                BigDecimal amtRefund = i.getAmtRefund() == null ? BigDecimal.ZERO : i.getAmtRefund();
                BigDecimal decimal = i.getRealAmt().subtract(amtRefund);
                i.setReturnableAmount(decimal); // 设置可退数量
                if (i == null) {
                    continue;
                }
                Long tmpOId = i.getOrderId();
                if (tmpL.equals(tmpOId)) {
                    tmpJo.add(i);
                    BigDecimal qty = i.getQtyLost();
                    totQtyLost = totQtyLost.add(qty == null ? BigDecimal.ZERO : qty);
                }
            }
            String lr = "";
            if (totQtyLost.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                lr = String.valueOf(totQtyLost.intValue());
            }
            o.setTotQtyLost(lr);
            o.setQueryOrderItemResultList(tmpJo);
            exChangeOutPutField(o, pm);
            dealAddressInfo(o);

            // 淘宝.预售
            String preSale = o.getStatusPayStep();
            String preSaleName = preSaleMap.get(preSale);
            o.setReserveVarchar03Name(preSaleName == null ? "非预售" : preSaleName);
            // 锁单
            Long lockStatus = o.getLockStatus();
            String lockStatusName = lockMap.get(lockStatus);
            o.setReserveBigint09Name(lockStatusName == null ? "" : lockStatusName);

//            // 菜鸟作业状态
//            String cns = caiNiaoMap.get(o.getCainiaoWhStatus());
//            o.setCainiaoWhStatus(cns);

            // 票
            if (b) {
                List<OcBOrderInvoiceInform> invList = invoiceMap.get(tmpL);
                if (invList != null) {
                    o.setInvoiceFormList(invList);
                }
            }
            // 开票转化
            if (o.getInvoiceStatus() == null) {
                o.setInvoiceStatus(OcBOrderConst.IS_STATUS_IN);
                o.setInvoiceStatusName(OcBorderListEnums.InvoiceStatusEnum.getTextByVal(OcBOrderConst.IS_STATUS_IN));
            } else {
                o.setInvoiceStatusName(OcBorderListEnums.InvoiceStatusEnum.getTextByVal(o.getInvoiceStatus()));
            }
          /*  // 刷单转换
            Integer scalpingType = o.getScalpingType();
            if (scalpingType == null) {
                o.setScalpingTypeName("");
            }
            String scalStr = brushOrderMap.get(scalpingType);
            if (scalStr == null) {
                scalStr = "";
            }
            o.setScalpingTypeName(scalStr);*/
            // 是否是换货入库
            Long v1 = o.getIsExchangeNoIn();
            o.setReserveBigint03Name(v1 == null ? "否" : (v1.intValue() == 0 ? "否" : "是"));
            o.setJitxRequiresMergeName(o.getJitxRequiresMerge() == null ? "否" : (YesNoEnum.ZERO.equals(o.getJitxRequiresMerge()) ? "否" : "是"));
//            o.setToSettleStatusName(toSettleMap.get(o.getToSettleStatus()));

            // 出库通知单
            o.setSgBPhyOutNoticesBillNo("");
            if (sgOutQueryResults == null) {
                continue;
            }
//            for (SgOutQueryResult sg : sgOutQueryResults) {
//                if (sg == null) {
//                    continue;
//                }
//                SgOutNoticesByBillNoResult notices = sg.getNotices();
//                if (notices == null) {
//                    continue;
//                }
//                SgBPhyOutNotices outNotices = notices.getOutNotices();
//                if (outNotices == null) {
//                    continue;
//                }
//                Long sourceBillId = outNotices.getSourceBillId();
//                if (sourceBillId == null) {
//                    continue;
//                }
//                if (tmpL.equals(sourceBillId)) {
//                    o.setSgBPhyOutNoticesBillNo(outNotices.getBillNo());
//                    o.setWmsBillNo(outNotices.getWmsBillNo());
//                    break;
//                }
//            }
        }
    }

    /**
     * 收货地址
     *
     * @param o QueryOrderResult
     */
    private void dealAddressInfo(QueryOrderResult o) {

        StringBuilder sb = new StringBuilder();
        String pn = o.getCpCRegionProvinceEname();
        if (pn != null && pn.trim().length() > OcBOrderConst.IS_STATUS_IN) {
            sb.append(pn + "/");
        }
        String cn = o.getCpCRegionCityEname();
        if (cn != null && cn.trim().length() > OcBOrderConst.IS_STATUS_IN) {
            sb.append(cn + "/");
        }
        String an = o.getCpCRegionAreaEname();
        if (an != null && an.trim().length() > OcBOrderConst.IS_STATUS_IN) {
            sb.append(an + "/");
        }
        String rad = o.getReceiverAddress();
        if (rad != null && rad.trim().length() > OcBOrderConst.IS_STATUS_IN) {
            sb.append(rad);
            o.setRegionReceiverAddress(sb.toString());
        } else {
            String s = sb.toString();
            if (s.length() > OcBOrderConst.IS_STATUS_IY) {
                s = s.substring(OcBOrderConst.IS_STATUS_IN, s.length() - OcBOrderConst.IS_STATUS_IY);
            }
            o.setRegionReceiverAddress(s);
        }
    }

    /**
     * es提取主表id,并计算起始下标
     *
     * @param esJo               jsonObject
     * @param orderIdArray       orderIdArray
     * @param queryOrderRelation queryOrderRelation
     * @return list
     */
    private List<Long> splitOrderIds(Long totalCount,JSONArray ary,QueryOrderRelation queryOrderRelation) {
        List<Long> list = new ArrayList<>();
        if (totalCount == null || totalCount == 0L || CollectionUtils.isEmpty(ary)) {
            return list;
        }

        Long totalPage = 0L;
        if (totalCount > OcBOrderConst.ORDER_STATUS_ALL) {
            long l = totalCount % (queryOrderRelation.getPage().getPageSize());
            if (l == OcBOrderConst.ORDER_STATUS_ALL) {
                totalPage = totalCount / (queryOrderRelation.getPage().getPageSize());
            } else {
                totalPage = (totalCount / (queryOrderRelation.getPage().getPageSize())) + 1;
            }
        }
        queryOrderRelation.getPage().setTotalNum(totalPage.intValue());
        queryOrderRelation.getPage().setTotalSize(totalCount);

        if (ary == null) {
            return list;
        }
        for (int i = 0; i < ary.size(); i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            Long id = o.getLong("ID");
            if (id == null) {
                continue;
            }
            list.add(id);
        }
        return list;
    }

    /**
     * sql in查询条件拼接
     *
     * @param list list
     * @return String
     */
    private String sqlHandler(List list) {
        if (list == null || list.size() == 0) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        int n = list.size();
        for (int i = 0; i < n; i++) {
            if (i == (n - 1)) {
                sb.append("'");
                sb.append(list.get(i));
                sb.append("'");
            } else {
                sb.append("'");
                sb.append(list.get(i));
                sb.append("',");
            }
        }
        return sb.toString();
    }

    /**
     * 查询条件
     *
     * @param jsonStr     查询条件
     * @param whereKeyJo  es查询条件
     * @param filterKeyJo 区间查询条件
     * @param childKeys   查询条件
     * @return sql string
     */
    public QueryOrderRelation handlerSqlConditionQuery(String jsonStr, JSONObject whereKeyJo, JSONObject filterKeyJo,
                                                       JSONObject childKeys) {
        QueryOrderRelation queryOrderRelation = new QueryOrderRelation();
        Page page = new Page();
        page.setPageNum(OcBOrderConst.PAGE_NUM);
        page.setPageSize(OcBOrderConst.PAGE_SIZE);
        queryOrderRelation.setPage(page);

        if (jsonStr == null || jsonStr.trim().length() == 0) {
            return queryOrderRelation;
        }
        JSONObject jo = JSONObject.parseObject(jsonStr);
        if (jo != null) {
            JSONObject pageJo = jo.getJSONObject("page");
            JSONArray labelJas = jo.getJSONArray("label");
            JSONArray queryInfoJo = jo.getJSONArray("queryInfo");
            JSONObject statusJo = jo.getJSONObject("status");
            JSONArray highSearch = jo.getJSONArray("highSearch");
            splitPageHandler(pageJo, page, queryOrderRelation);

            // 2.0 标签部分
            sqlIntelliSearchTag(labelJas, whereKeyJo);
            // 3.0 多下拉智能查询部分
            if (queryInfoJo != null) {
                int infoLen = queryInfoJo.size();
                for (int i = 0; i < infoLen; i++) {
                    JSONObject tmpJo = queryInfoJo.getJSONObject(i);
                    String type = tmpJo.getString("type");
                    String queryName = tmpJo.getString("queryName");
                    if (type != null && "Select".equals(type)) {
                        JSONArray selectBox = tmpJo.getJSONArray("list");
                        sqlIntelliSearchCtrHandler(selectBox, queryName, whereKeyJo);

                    } else if (type != null && "date".equals(type)) {
                        String value = tmpJo.getString("value");
                        sqlHighSearchDateCtrHandler(value, queryName, filterKeyJo);
                    } else if (type != null && "Input".equals(type)) {
                        String value = tmpJo.getString("value");
                        sqlHighSearchTextCtrHandler(value, queryName, whereKeyJo, filterKeyJo, childKeys);
                    } else if (type != null && "DropDownSelectFilter".equals(type)) {
                        JSONArray selectDrop = tmpJo.getJSONArray("list");
                        intelliSearchDropDownRef(selectDrop, queryName, whereKeyJo);
                    }
                }
            }

            // 4.0 高级搜索部分
            if (highSearch != null) {
                for (int i = 0; i < highSearch.size(); i++) {
                    JSONObject tmpJo = highSearch.getJSONObject(i);
                    if (tmpJo != null) {
                        String type = tmpJo.getString("type");
                        String queryName = tmpJo.getString("queryName");
                        if ("Select".equalsIgnoreCase(type)) {
                            String sltChk = tmpJo.getString("value");
                            highSearchSelectCtrlHandler(sltChk, queryName, whereKeyJo);

                        } else if ("date".equalsIgnoreCase(type)) {
                            String dateStr = tmpJo.getString("value");
                            sqlHighSearchDateCtrHandler(dateStr, queryName, filterKeyJo);
                        } else if ("Input".equalsIgnoreCase(type)) {
                            String txt = tmpJo.getString("value");
                            sqlHighSearchTextCtrHandler(txt, queryName, whereKeyJo, filterKeyJo, childKeys);
                        }
                    }
                }
            }

            // ES:合并状态- 状态按钮部分
            buttonStatuJoinList(statusJo, whereKeyJo);
            allStatuHandler(whereKeyJo);
            afterArrangeEsHandler(whereKeyJo);

        }
        return queryOrderRelation;
    }


    /**
     * 全部状态: 判断
     *
     * @param es es
     */
    private void allStatuHandler(JSONObject es) {
        if (es != null) {
            JSONArray arys = es.getJSONArray("ORDER_STATUS");
            if (arys != null) {
                int len = arys.size();
                for (int i = 0; i < len; i++) {
                    if (arys.getInteger(i) != null && OcBOrderConst.ORDER_STATUS_ALL.equals(arys.getInteger(i))) {
                        es.put("ORDER_STATUS", OcOrderCheckBoxEnum.joinAllStatusVal());
                        break;
                    }
                }
            }
        }
    }

    /**
     * 智能搜索: 标签
     *
     * @param oA tagArrays标签集合
     * @param es es
     */
    private void sqlIntelliSearchTag(JSONArray oA, JSONObject es) {
        if (oA != null) {
            int n = oA.size();
            for (int i = 0; i < n; i++) {
                JSONObject o = oA.getJSONObject(i);
                if (o == null) {
                    continue;
                }
                if (labelTag.equalsIgnoreCase(o.getString("key"))) {
                    es.put("CP_C_LABEL_ENAME", "*");
                    continue;
                }
                es.put(o.getString("key"), o.getString("val"));
            }
        }
    }

    /**
     * 智能搜索: 下拉多选
     * 1. 当前: 状态,
     *
     * @param selectBoxs 下拉多选值集合
     * @param queryName  下拉,查询字段
     * @param es         es
     */
    private void sqlIntelliSearchCtrHandler(JSONArray selectBoxs, String queryName, JSONObject es) {
        if (selectBoxs == null || selectBoxs.size() < 1) {
            return;
        }
        int n = selectBoxs.size();
        JSONArray statuAry = new JSONArray();
        for (int i = 0; i < n; i++) {
            JSONObject o = selectBoxs.getJSONObject(i);
            if (o != null) {
                if ("ORDER_STATUS".equalsIgnoreCase(queryName)) {
                    statuAry.add(o.getString("value"));
                }
            }
        }
        es.put("ORDER_STATUS", statuAry);
    }

    /**
     * 智能搜索. 下拉外键,关联查询
     *
     * @param ary 关联数据
     * @param n   字段
     * @param e   es
     */
    private void intelliSearchDropDownRef(JSONArray ary, String n, JSONObject e) {
        if (ary == null || ary.size() < OcBOrderConst.IS_STATUS_IY) {
            return;
        }
        if (n.equals("CP_C_SHOP_TITLE")) {
            n = "CP_C_SHOP_ID";
        } else if (n.equals("CP_C_PHY_WAREHOUSE_ENAME")) {
            n = "CP_C_PHY_WAREHOUSE_ID";
        } else if (n.equals("CP_C_LOGISTICS_ENAME")) {
            n = "CP_C_LOGISTICS_ID";
        }
        JSONArray fk = new JSONArray();
        int l = ary.size();
        for (int i = 0; i < l; i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o != null) {
                fk.add(o.getString("ID"));
            }
        }
        e.put(n, fk);
    }

    /**
     * 高级搜索: 下拉多选
     *
     * @param valString 下拉值集合
     * @param queryName 搜索字段
     * @param es        es
     */
    private void highSearchSelectCtrlHandler(String valString, String queryName, JSONObject es) {
        if (valString == null || valString.trim().length() == 0) {
            return;
        }
        String[] valArys = valString.split(",");
        int len = valArys.length;
        JSONArray selectAry = new JSONArray();
        for (int i = 0; i < len; i++) {
            String v = valArys[i];
            if (v == null || v.trim().length() < 1) {
                continue;
            }
            selectAry.add(v);
        }
        if ("STATUS_PAY_STEP".equalsIgnoreCase(queryName)) {
            if (selectAry.contains("NOT_PRESALE") && selectAry.size() == OcBOrderConst.IS_STATUS_IY) {
                es.put(queryName, null);
                return;
            } else if (selectAry.contains("NOT_PRESALE") && selectAry.size() == 3) {
                return;
            } else if (selectAry.contains("NOT_PRESALE") && selectAry.contains("FRONT_PAID_FINAL_PAID")) {
                es.put(queryName, "!=FRONT_PAID_FINAL_NOPAID");
                return;
            } else if (selectAry.contains("NOT_PRESALE") && selectAry.contains("FRONT_PAID_FINAL_NOPAID")) {
                es.put(queryName, "!=FRONT_PAID_FINAL_PAID");
                return;
            }
        }
        //ES:渠道类型条件
        if ("CHANNEL_TYPE_ID".equalsIgnoreCase(queryName) && StringUtils.isNotEmpty(valString)) {
            channelTypeService.channelTypeSelectHandler(valString, es);
        } else {
            es.put(queryName, selectAry);
        }
    }

    /**
     * 高级搜索: 时间控件
     *
     * @param dateStr   时间字符串
     * @param queryName 搜索字段
     * @param es        es
     */
    private void sqlHighSearchDateCtrHandler(String dateStr, String queryName, JSONObject es) {
        if (dateStr == null || dateStr.trim().length() < 1) {
            return;
        }
        String esDateStr = dealStringConvertToTime(dateStr);
        if (esDateStr != null && esDateStr.length() > 0) {
            es.put(queryName, esDateStr);
        }
    }

    /**
     * 高级搜索: 文本控件
     *
     * @param v         文本值
     * @param n         搜索字段
     * @param fKey      搜索字段
     * @param childKeys 搜索字段
     * @param es        es
     * @return t/f
     */
    private boolean sqlHighSearchTextCtrHandler(String v, String n, JSONObject es, JSONObject fKey,
                                                JSONObject childKeys) {
        if (StringUtils.isBlank(v) || "~".equals(v.trim())) {
            return false;
        }

        v = v.trim();
        if (v.contains(OcBOrderConst.ORDER_COMMA)) {
            if (OcBOrderConst.OCB_ORDER_LIST_BATCH_INCLUDEKEYS.contains(n)) {
                dealBatchQuery(OcBOrderConst.ORDER_COMMA, n, v, es);
                return true;
            }
        }
        if (v.contains(OcBOrderConst.ORDER_COMMA_CN)) {
            if (OcBOrderConst.OCB_ORDER_LIST_BATCH_INCLUDEKEYS.contains(n)) {
                dealBatchQuery(OcBOrderConst.ORDER_COMMA_CN, n, v, es);
                return true;
            }
        }

        if ("ID".equals(n)) {
            es.put(n, v);
            return true;
        }
        if ("SYSREMARK".equals(n) || "BUYER_MESSAGE".equals(n) || "RECEIVER_ADDRESS".equals(n)) {
            es.put(n, "*" + v + "*");
            return true;
        }
        if (OcBOrderConst.OCB_ORDER_QTY_ALL.equals(n) || OcBOrderConst.OCB_ORDER_ORDER_AMT.equals(n)
                || "SKU_KIND_QTY".equals(n)) {
            boolean flag = Pattern.matches(OcBOrderConst.OCB_ORDER_NUMBER_REGES, v);
            if (flag) {
                fKey.put(n, splitAndJoinHighText(v));
                return true;
            }
            return false;
        }
        if (OcBOrderConst.OCB_ORDER_ITEM_PSC_SKUECODE.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_PS_C_PRO_ECODE.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_PT_ECODE.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_PT_NAME.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_RESERVE_VARCHAR01.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_SKU_NUMIID.equals(n)
                || OcBOrderConst.OCB_ORDER_ITEM_NUM_IID.equals(n)) {
            if (v.contains(OcBOrderConst.ORDER_COMMA)) {
                dealBatchQuery(OcBOrderConst.ORDER_COMMA, n, v, childKeys);
                return true;
            }
            if (v.contains(OcBOrderConst.ORDER_COMMA_CN)) {
                dealBatchQuery(OcBOrderConst.ORDER_COMMA_CN, n, v, childKeys);
                return true;
            }
            childKeys.put(n, "*" + v + "*");
            return true;
        }
        es.put(n, v + "*");
        return true;
    }

    /**
     * 高级搜索: 文本控件.批量查询
     *
     * @param c  split c
     * @param n  key
     * @param v  value
     * @param es whereKey
     */
    private void dealBatchQuery(String c, String n, String v, JSONObject es) {
        JSONArray cnJay = new JSONArray();
        String[] cnAry = v.split(c);
        int l = cnAry.length;
        for (int i = 0; i < l; i++) {
            if (cnAry[i] == null) {
                continue;
            }
            String s = cnAry[i].trim();
            if (s.length() < OcBOrderConst.IS_STATUS_IY) {
                continue;
            }
            cnJay.add(s);
        }
        if (cnJay.size() > OcBOrderConst.IS_STATUS_IN) {
            es.put(n, cnJay);
        }
    }

    /**
     * 高级搜索: 文本控件_区间查询处理
     *
     * @param v 区间字符串
     * @return 后拼接
     */
    private String splitAndJoinHighText(String v) {
        String[] ary = v.split("~");
        if (ary.length > 1) {
            if (StringUtils.isNotBlank(ary[0]) && StringUtils.isNotBlank(ary[1])) {
                BigDecimal v0 = new BigDecimal(ary[0]);
                BigDecimal v1 = new BigDecimal(ary[1]);
                if (v0.compareTo(v1) > 0) {
                    return ary[1] + "~" + ary[0];
                }
            }
        }
        return v;
    }

    /**
     * 分页处理
     *
     * @param o   分页信息
     * @param p   分页entity
     * @param qol 分页relation pojo
     */
    private void splitPageHandler(JSONObject o, Page p, QueryOrderRelation qol) {
        if (o != null) {
            Integer size = o.getInteger("pageSize");
            Integer num = o.getInteger("pageNum");
            if (num != null && num > 0) {
                p.setPageNum(num);
            }
            if (size != null && size > 0) {
                p.setPageSize(size);
            }
            qol.setPage(p);
        }
    }

    /**
     * 提取子表: 商品id,skuid
     *
     * @param ocBorderItemDtoList 子表查询结果
     * @param skuIdList           skuId集合
     */
    private void splitItemsIds(List<QueryOrderItemResult> ocBorderItemDtoList, List<Long> skuIdList) {
        if (ocBorderItemDtoList == null) {
            return;
        }

        for (QueryOrderItemResult o : ocBorderItemDtoList) {
            if (o == null) {
                continue;
            }
            Long skuId = o.getSkuId();
            if (skuId != null) {
                skuIdList.add(o.getSkuId());
            }
        }
    }

    /**
     * 字符串转换成日期,并拼接
     *
     * @param str 日期字符串
     * @return 日期时间戳字符串
     */
    private String dealStringConvertToTime(String str) {
        String dtStr = null;
        String[] sAry = str.split("~");
        // 严格格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (sAry[0] == null || sAry[1] == null || sAry[0].length() < OcBOrderConst.DATE_MIN_LENGTH
                || sAry[1].length() < OcBOrderConst.DATE_MIN_LENGTH) {
            return null;
        }
        try {
            Long startDt = sdf.parse(sAry[0]).getTime();
            Long endDt = sdf.parse(sAry[1]).getTime();
            dtStr = startDt + "~" + endDt;
        } catch (ParseException e) {
            log.error(LogUtil.format("OcBOrderListQueryServer.dealStringConvertToTIme Error：{}"),
                    Throwables.getStackTraceAsString(e));
        }
        return dtStr;
    }


    /**
     * 标签处理
     *
     * @param list      订单查询结果集
     * @param sgRequest 出库通单入参
     */
//    private void dealTagResult(List<QueryOrderResult> list, SgPhyOutBillQueryRequest sgRequest) {
//        List<SgPhyOutBillBaseRequest> sgBaseReqList = new ArrayList<>();
//        for (QueryOrderResult o : list) {
//            if (o == null) {
//                continue;
//            }
//
//            SgPhyOutBillBaseRequest spob = new SgPhyOutBillBaseRequest();
//            spob.setSourceBillId(o.getId());
//            spob.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
//            sgBaseReqList.add(spob);
//
//            List<QueryOrderTagResult> tagList = packageTags(o);
//            o.setOrderTagList(tagList);
//        }
//        sgRequest.setBaseRequests(sgBaseReqList);
//    }

    /**
     * 根据标签值,转化为标签对象
     *
     * @param qor 订单查询结果
     * @return 标签对象集合
     */
    private List<QueryOrderTagResult> packageTags(QueryOrderResult qor) {
        List<String> list = new ArrayList<>();
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsMerge())) {
            list.add("IS_MERGE");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsInterecept())) {
            list.add("IS_INTERECEPT");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsInreturning())) {
            list.add("IS_INRETURNING");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsHasgift())) {
            list.add("IS_HASGIFT");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsSplit())) {
            list.add("IS_SPLIT");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsInvoice())) {
            list.add("IS_INVOICE");
        }
        if (qor.getCpCLabelId() != null) {
            list.add("CP_C_LABEL_ID");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsJcorder())) {
            list.add("IS_JCORDER");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsOutUrgency())) {
            list.add("IS_OUT_URGENCY");
        }
        /*if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsLackstock())
                && OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(qor.getOrderStatus())) {
            list.add("IS_LACKSTOCK");
        }*/
//        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsShopCommission())) {
//            list.add("IS_SHOP_COMMISSION");
//        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsHasTicket())) {
            list.add("IS_HAS_TICKET");
        }
        /*if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsInvented())) {
            list.add("IS_INVENTED");
        }*/
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsCombination())) {
            list.add("IS_COMBINATION");
        }
        if (qor.getOrderType() != null && OcBOrderConst.ORDER_PAY_TYPE == qor.getOrderType()) {
            list.add("ORDER_TYPE");
        }
        if (qor.getDouble11PresaleStatus() != null
                && !(OcBOrderConst.ORDER_STATUS_ALL.equals(qor.getDouble11PresaleStatus()))) {
            list.add("DOUBLE11_PRESALE_STATUS");
        }
        /*if (!(OcBOrderConst.ORDER_STATUS_ALL.equals(qor.getSysPresaleStatus()))
                && qor.getSysPresaleStatus() != null) {
            list.add("SYS_PRESALE_STATUS");
        }*/
        if (qor.getPayType() != null && OcBOrderConst.ORDER_PAY_TYPE == qor.getPayType()) {
            list.add("PAY_TYPE");
        }
        //价
        if (StringUtils.isNotEmpty(qor.getPriceLabel()) && OcBOrderConst.IS_ACTIVE_YES.equals(qor.getPriceLabel())) {
            list.add("PRICE_LABEL");
        }
        if (qor.getLockStatus() != null && OcOrderLockStatusEnum.LOCKED.getKey() == qor.getLockStatus()) {
            list.add("LOCK_STATUS");
        }
        /*if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsWosUrge())) {
            //WOS催
            list.add("IS_WOS_URGE");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsWosCut())) {
            //WOS截
            list.add("IS_WOS_CUT");
        }*/
        if (IsForbiddenDeliveryEnum.FORBIDDEN.getCode().equals(qor.getIsForbiddenDelivery())) {
            //JitX禁发
            list.add("IS_FORBIDDEN_DELIVERY");
        }
        if (YesNoEnum.Y.getVal().equals(qor.getIsVipUpdateWarehouse())) {
            //JitX改仓
            list.add("IS_VIP_UPDATE_WAREHOUSE");
        }
        if (YesNoEnum.ONE.getKey().equals(qor.getReverseAuditType())) {
            //反审核中
            list.add("REVERSE_AUDIT_TYPE");
        }
        // key 值转化
        List<QueryOrderTagResult> tagList = OcOrderTagEum.toListQueryOrderTagResult(list);
        if (OcOrderTagEum.TAG_HAND.getVal().equals(qor.getOrderSource())) {
            QueryOrderTagResult q = OcOrderTagEum.getQueryOrderTagResult(OcBOrderConst.ORDER_TAG_HAND);
            tagList.add(q);
        }

        if (OcBOrderConst.ORDER_TAG_VALLAGE.equals(qor.getOrderSource())) {
            QueryOrderTagResult q = OcOrderTagEum.getQueryOrderTagResult(OcBOrderConst.ORDER_TAG_VALLAGE);
            tagList.add(q);
        }

        return tagList;
    }

    /**
     * ES:合并按钮状态
     *
     * @param buttonWherekey buttonWherekey
     * @param whereKeyJo     whereKeyJo
     */
    private void buttonStatuJoinList(JSONObject buttonWherekey, JSONObject whereKeyJo) {
        if (buttonWherekey == null) {
            return;
        }
        String statusStr = buttonWherekey.getString("value");
        JSONArray tmpStatusList = whereKeyJo.getJSONArray("ORDER_STATUS");
        JSONArray reSetJa = new JSONArray();
        statusLbl:
        if (null != tmpStatusList && StringUtils.isNotBlank(statusStr)) {
            // 下拉没值, tab值 加入
            if (tmpStatusList.size() < OcBOrderConst.IS_STATUS_IY) {
                splitComposeStatus(statusStr, reSetJa);
                whereKeyJo.put("ORDER_STATUS", reSetJa);
                break statusLbl;
            }
            // tab 包含全部
            if (OcBOrderConst.STATUS_TAB_ALL.equals(statusStr)) {
                if (tmpStatusList.contains(OcBOrderConst.STATUS_TAB_ALL)) {
                    reSetJa.add(OcBOrderConst.STATUS_TAB_ALL);
                    whereKeyJo.put("ORDER_STATUS", reSetJa);
                } else {
                    whereKeyJo.put("ORDER_STATUS", tmpStatusList);
                }
            } else {
                // 组合性tab
                if (statusStr.contains(OcBOrderConst.ORDER_COMMA)) {
                    splitComposeStatus(statusStr, reSetJa);
                    dealComposeStatus(tmpStatusList, reSetJa, whereKeyJo);
                } else if (tmpStatusList.contains(statusStr)
                        || (tmpStatusList.contains(OcBOrderConst.STATUS_TAB_ALL))) {
                    reSetJa.add(statusStr);
                    whereKeyJo.put("ORDER_STATUS", reSetJa);
                } else {
                    reSetJa.add(OcBOrderConst.ORDER_STATUS_NONE);
                    whereKeyJo.put("ORDER_STATUS", reSetJa);
                }
            }
        } else if (tmpStatusList == null && StringUtils.isNotBlank(statusStr)) {
            // 下拉为null, tab有值
            splitComposeStatus(statusStr, reSetJa);
            whereKeyJo.put("ORDER_STATUS", reSetJa);
        }
    }

    /**
     * 处理组合性tab状态
     *
     * @param tmpStatusList 条件状态
     * @param reSetJa       tab状态
     * @param whereKeyJo    ES
     */
    private void dealComposeStatus(JSONArray tmpStatusList, JSONArray reSetJa, JSONObject whereKeyJo) {
        if (tmpStatusList.contains(OcBOrderConst.IS_STATUS_SN)) {
            whereKeyJo.put("ORDER_STATUS", reSetJa);
        } else {
            JSONArray ary = new JSONArray();
            for (int i = 0; i < reSetJa.size(); i++) {
                String stu = reSetJa.getString(i);
                if (tmpStatusList.contains(stu)) {
                    ary.add(stu);
                }
            }
            if (ary.size() > OcBOrderConst.IS_STATUS_IN) {
                whereKeyJo.put("ORDER_STATUS", ary);
            } else {
                ary.add(OcBOrderConst.ORDER_STATUS_NONE);
                whereKeyJo.put("ORDER_STATUS", ary);
            }
        }
    }

    /**
     * 拆分组合性tab状态
     *
     * @param stu 状态
     * @param ary jsonArray
     */
    private void splitComposeStatus(String stu, JSONArray ary) {
        if (stu.contains(OcBOrderConst.ORDER_COMMA)) {
            String[] stuAry = stu.split(OcBOrderConst.ORDER_COMMA);
            for (String s : stuAry) {
                if (StringUtils.isNotBlank(s)) {
                    ary.add(s);
                }
            }
        } else {
            ary.add(stu);
        }
    }

    /**
     * 查询合并商品信息
     *
     * @param ocBorderItemDtoList ocBorderItemDtoList
     * @return t/f
     */
    private boolean mergeGoodsHandler(List<QueryOrderItemResult> ocBorderItemDtoList) {
        Map<Long, PsCProdimItem> sexMap = querySexInfo();
        for (QueryOrderItemResult item : ocBorderItemDtoList) {
            // 商品空值处理
            dealNullProEcode(item);
            // 规格转换
            dealSpec(item);
            // 单件实际成交价
            item.setAmtRefundSingle(calcAmtRefundSingle(item.getRealAmt(), item.getQty()));
            BigDecimal price = item.getPrice() == null ? BigDecimal.ZERO : item.getPrice();
            BigDecimal realAmt = item.getRealAmt() == null ? BigDecimal.ZERO : item.getRealAmt();
            item.setPrice(price.setScale(OcBOrderConst.DECIMAL_QTY, BigDecimal.ROUND_DOWN));
            item.setRealAmt(realAmt.setScale(OcBOrderConst.DECIMAL_QTY, BigDecimal.ROUND_DOWN));
            // 结算总额,如果没值,则启用成交价格
            if (item.getTotPriceSettle() == null ||
                    item.getTotPriceSettle().compareTo(BigDecimal.ZERO) < OcBOrderConst.IS_STATUS_IY) {
                item.setTotPriceSettle(realAmt);
            }
            //结算单价
            if (item.getPriceSettle() == null ||
                    item.getPriceSettle().compareTo(BigDecimal.ZERO) < OcBOrderConst.IS_STATUS_IY) {
                item.setPriceSettle(item.getAmtRefundSingle());
            }

            Long sx = item.getSex();
            if (sx == null) {
                item.setSexName("");
                continue;
            }
            if (sexMap != null) {
                PsCProdimItem pim = sexMap.get(sx);
                if (pim == null) {
                    item.setSexName("");
                    continue;
                }
                String en = pim.getEname();
                if (en == null) {
                    en = "";
                }
                item.setSexName(en);
                continue;
            }
            item.setSexName("");
        }
        return true;
    }

    /**
     * 拆分规格
     *
     * @param item QueryOrderItemResult
     */
    private void dealSpec(QueryOrderItemResult item) {
        String skuSpec = item.getSkuSpec();
        if (StringUtils.isNotBlank(skuSpec)) {
            String[] split = skuSpec.split(",");
            if (split.length == OcBOrderConst.ORDER_PAY_TYPE) {
                item.setSizes(split[0]);
                item.setClrs(split[1]);
                return;
            }
        }
        item.setSizes("");
        item.setClrs("");
    }

    /**
     * 处理商品编码空值问题
     *
     * @param item item
     */
    private void dealNullProEcode(QueryOrderItemResult item) {
        String ecode = item.getEcode();
        if (ecode == null) {
            ecode = "";
        }
        item.setEcode(ecode);
    }

    /**
     * 图片地址,处理, 出参
     *
     * @param img img
     * @return image
     */

    private String imageDeal(String img) {
        String imgUrl = OcBOrderConst.GOODS_DEFAULT_IMAGE;
        if (StringUtils.isBlank(img)) {
            return imgUrl;
        }
        if (img.startsWith(OcBOrderConst.IMG_PATH_HEAD_VAL)) {
            return img;
        }
        try {
            JSONArray picJo = JSONArray.parseArray(img);
            if (picJo == null) {
                return imgUrl;
            }
            JSONObject po = picJo.getJSONObject(OcBOrderConst.IMG_INDEX);
            if (po == null) {
                return imgUrl;
            }
            String imgUrls = po.getString(OcBOrderConst.IMG_KEY_NAME);
            if (StringUtils.isBlank(imgUrls)) {
                return imgUrl;
            }
            return imgUrls;
        } catch (Exception e) {
            recordLog("OcBOrderListQueryService.imageDeal:-> 商品图片解析异常, 启用默认图片");
            return imgUrl;
        }

    }

    /**
     * ES 分页起始下标
     *
     * @param queryOrderRelation queryOrderRelation
     * @return int
     */
    private int getPageStartIndex(QueryOrderRelation queryOrderRelation) {
        Integer pageNum = queryOrderRelation.getPage().getPageNum();
        int startIndex = OcBOrderConst.ORDER_STATUS_ALL;
        if (pageNum != null && pageNum > OcBOrderConst.ORDER_STATUS_ALL) {
            startIndex = (pageNum - 1) * (queryOrderRelation.getPage().getPageSize());
        }
        return startIndex;
    }

    /**
     * 计算单件实际成交价
     *
     * @param realAmt realAmt
     * @param qty     qty
     * @return decimal
     */
    private BigDecimal calcAmtRefundSingle(BigDecimal realAmt, BigDecimal qty) {
        if (realAmt != null && qty != null && qty.compareTo(BigDecimal.ZERO) != OcBOrderConst.IS_STATUS_IN) {
            return realAmt.divide(qty, OcBOrderConst.DECIMAL_QTY_FOUR, BigDecimal.ROUND_DOWN);
        }
        return new BigDecimal("0");
    }

    /**
     * 订单值:  转换可视化
     *
     * @param o  订单结果
     * @param pm map
     */
    private void exChangeOutPutField(QueryOrderResult o, Map<String, String> pm) {
        if (o == null) {
            return;
        }

        String orderStatusName = OcOrderCheckBoxEnum.enumToStringByValue(o.getOrderStatus());
        o.setOrderStatusName(orderStatusName);
        String orderTypeName = OrderTypeEnum.getTextByVal(o.getOrderType());
        o.setOrderTypeName(orderTypeName);
        String platFormName = dealPlatform(pm, o.getPlatform());
        o.setPlatFormName(platFormName);
        String payTypeName = OcBorderListEnums.PayTypeEnum.getTextByVal(o.getPayType());
        o.setPayTypeName(payTypeName);
        String occupyStatusName = OcBorderListEnums.OccupyStatusEnum.getTextByVal(o.getOccupyStatus());
        o.setOccupyStatusName(occupyStatusName);
        String wmsCancelStatusName = OcBorderListEnums.WmsCanceStatusEnum.getTextByVal(o.getWmsCancelStatus());
        o.setWmsCancelStatusName(wmsCancelStatusName);
        String autoAuditStatusName = OcBorderListEnums.AutoAuditStatusEnum.getTextByVal(o.getAutoAuditStatus());
        o.setAutoAuditStatusName(autoAuditStatusName);
        String isGeninvoiceNoticeNm = OcBorderListEnums.IsGeninvoiceNoticeEnum.getTextByVal(o.getIsGeninvoiceNotice());
        o.setIsGeninvoiceNoticeName(isGeninvoiceNoticeNm);
        String returnName = OcBorderListEnums.ReturnStatusEnum.getTextByVal(o.getReturnStatus());
        o.setReturnStatusName(returnName);

        // hold单原因
        if(Objects.nonNull(o.getHoldReasonId())) {
            StCHoldOrderReason stCHoldOrderReason = holdOrderReasonQueryService.selectHoldOrderById(Long.valueOf(o.getHoldReasonId()));
            if (Objects.nonNull(stCHoldOrderReason)) {
                o.setHoldReasonName(stCHoldOrderReason.getReason());
            }
        }
        // 卡单原因
        if(Objects.nonNull(o.getDetentionReasonId())) {
            StCHoldOrderReason detentionReason = holdOrderReasonQueryService.selectHoldOrderById(Long.valueOf(o.getDetentionReasonId()));
            if (Objects.nonNull(detentionReason)) {
                o.setDetentionReasonName(detentionReason.getReason());
            }
        }

       /* String isToDrpName = OcBorderListEnums.IsToDrpOrderEnum.getTextByVal(o.getIsTodrp());
        o.setIsToDrpName(isToDrpName);*/

    }

    /**
     * debug 级 日志记录
     *
     * @param msg 信息
     */
    private void recordLog(String msg) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(msg));
        }
    }

    /**
     * 加入分页信息
     *
     * @param queryOrderListResult QueryOrderListResult
     * @param queryOrderRelation   QueryOrderRelation
     */
    private void addSplitPageInfo(QueryOrderListResult queryOrderListResult, QueryOrderRelation queryOrderRelation) {
        queryOrderListResult.setTotalSize(queryOrderRelation.getPage().getTotalSize());
        queryOrderListResult.setTotalNum(queryOrderRelation.getPage().getTotalNum());
        queryOrderListResult.setPageNum(queryOrderRelation.getPage().getPageNum());
        queryOrderListResult.setPageSize(queryOrderRelation.getPage().getPageSize());
    }

    /**
     * 平台.转换
     *
     * @param m 集
     * @param p 值
     * @return 名称
     */
    public String dealPlatform(Map<String, String> m, Integer p) {

        if (m != null && m.size() > OcBOrderConst.IS_STATUS_IN) {
            String s1 = m.get(String.valueOf(p));
            if (s1 != null) {
                return s1;
            } else {
                return cpRpcService.rpcQueryPlatformNameByCode(String.valueOf(p));
            }
        } else {
            return rpcGetPlatformName(String.valueOf(p));
        }
    }

    /**
     * RPC.查询.平台
     *
     * @param sp 值
     * @return 名称
     */
    private String rpcGetPlatformName(String sp) {
        try {
            String s = cpRpcService.rpcQueryPlatformNameByCode(sp);
            return s == null ? "" : s;
        } catch (Exception e) {
            log.error(LogUtil.format("OcBOrderListQueryService.rpcGetPlatformName,error：{}"),
                    Throwables.getStackTraceAsString(e));
            return "";
        }
    }

    /**
     * Rpc 查询性别
     *
     * @return Map
     */
    private Map<Long, PsCProdimItem> querySexInfo() {
        ValueHolderV14<Map<Long, PsCProdimItem>> mpv = null;
        try {
            mpv = psRpcCProdimItemQueryService.queryAllSexInfo();
        } catch (Exception e) {
            String ems = "RPC查询性别信息.发生异常";
            if (e != null) {
                ems = e.getMessage();
            }
            log.error(LogUtil.format("###RPC查询性别信息.发生异常: -> {}"), ems);
        }
        if (mpv != null && mpv.getCode() == ResultCode.SUCCESS) {
            Map<Long, PsCProdimItem> sexMap = mpv.getData();
            if (sexMap != null && sexMap.size() > OcBOrderConst.IS_STATUS_IN) {
                return sexMap;
            }
        }
        return null;
    }

    /**
     * 查询.开票信息
     *
     * @param refIds refIds
     * @return Map
     */
    private Map<Long, List<OcBOrderInvoiceInform>> getocBOrderInvoiceInform(String refIds) {
        Map<Long, List<OcBOrderInvoiceInform>> m;
        List<OcBOrderInvoiceInform> invoiceList = ocBOrderInvoiceInformMapper.selectInvoiceInformByRids(refIds,
                OcBOrderConst.IS_ACTIVE_YES);

        if (invoiceList == null || invoiceList.size() < OcBOrderConst.IS_STATUS_IY) {
            return null;
        }
        m = new HashMap<>();
        Set<Long> set = new HashSet<>();
        for (OcBOrderInvoiceInform o : invoiceList) {
            Long oId = o.getOcBOrderId();
            if (oId == null) {
                continue;
            }
            if (set.add(oId)) {
                List<OcBOrderInvoiceInform> lt = new ArrayList<>();
                lt.add(o);
                m.put(oId, lt);
                continue;
            }
            List<OcBOrderInvoiceInform> lt1 = m.get(oId);
            if (lt1 != null) {
                lt1.add(o);
            }
        }
        if (m != null && m.size() > OcBOrderConst.IS_STATUS_IN) {
            return m;
        }
        return null;

    }

    /**
     * 过滤不可查询字段
     *
     * @param pem all permission collection
     */
    private JSONObject filterForbidSensitiveColumn(UserPermission pem) {
        if (pem != null) {
            Set<String> forbidCols = pem.getForbiddenColumns();
            return ocOrderAuthorityMgtService.getOcOrderItemField(forbidCols);
        }
        return ocOrderAuthorityMgtService.getOcOrderItemField(null);
    }

    /**
     * 重组查询
     *
     * @param pem  UserPermission
     * @param wKey JSONObject
     */
    public void filterSearchCondition(UserPermission pem, JSONObject wKey) {
        keyLabel:
        if (pem != null) {
            Map<String, BasePermission> baseMap = pem.getBasePermission();
            if (baseMap == null) {
                break keyLabel;
            }
            BasePermission basePem = baseMap.get("OC_B_ORDER");
            // 表没配
            if (basePem == null) {
                return;
            }
            List<String> cdtList = new ArrayList<>();
            cdtList.add("CP_C_SHOP_ID");
            cdtList.add("CP_C_PHY_WAREHOUSE_ID");
            ocOrderAuthorityMgtService.recombinationSearchCdt(cdtList, basePem, wKey);
        }
    }

    /**
     * 一商特有业务查询
     */
    private void afterArrangeEsHandler(JSONObject whereKey) {

        final String nullStatus = "1";
        final String notNullStatus = "2";
        final String buyerMsg = "BUYER_MESSAGE";
        final String buyerMsg02 = "BUYER_MESSAGE_02";

        buyMsg:
        if (whereKey.containsKey(buyerMsg02)) {
            JSONArray buyMsg = whereKey.getJSONArray(buyerMsg02);
            whereKey.remove(buyerMsg02);
            if (buyMsg.contains("0") || (buyMsg.contains(nullStatus) && buyMsg.contains(notNullStatus))) {
                break buyMsg;
            }
            if (buyMsg.contains(nullStatus)) {
                String buys = whereKey.getString(buyerMsg);
                if (StringUtils.isNotBlank(buys)) {
                    whereKey.clear();
                    whereKey.put("ID", -1);
                    break buyMsg;
                }
                whereKey.put(buyerMsg, null);
            }
            if (buyMsg.contains(notNullStatus)) {
                String buys = whereKey.getString(buyerMsg);
                if (StringUtils.isNotBlank(buys)) {
                    break buyMsg;
                }
                whereKey.put(buyerMsg, "*");
            }
        }

        final String orderTag = "ORDER_TAG";
        if (whereKey.containsKey(orderTag)) {
            JSONArray tagAry = whereKey.getJSONArray(orderTag);
            for (Object o : tagAry) {
                String[] kv = String.valueOf(o).split("=");
                if (kv == null || kv.length < 2) {
                    continue;
                }
                if (labelTag.equalsIgnoreCase(kv[0])) {
                    whereKey.put("CP_C_LABEL_ENAME", "*");
                    continue;
                }
                if (whereKey.containsKey(kv[0])) {
                    JSONArray jsnAry = new JSONArray();
                    jsnAry.add(kv[1]);
                    Object o1 = whereKey.get(kv[0]);
                    jsnAry.add(o1);
                    whereKey.put(kv[0], jsnAry);
                    continue;
                }
                whereKey.put(kv[0], kv[1]);
            }
            whereKey.remove(orderTag);
        }

    }


    public OcBOrder getOrderByBillNo(String billNo) {
        Long id = ES4Order.getIdByBillNo(billNo);
        if (id == null) {
            return null;
        }
        return ocBOrderMapper.selectById(id);
    }
}
