package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsReleaseOutRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.request.PosOrderCancelRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * className: OcBOrderAllRefundService
 * description:全渠道订单退款服务
 *
 * <AUTHOR>
 * create: 2021-06-22
 * @since JDK 1.8
 */
@Slf4j
@Service
public class OcBOrderAllRefundService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper itemMapper;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcBOrderCancelService cancelService;

    @Autowired
    private OcBOrderTheAuditService theAuditService;

    @Autowired
    private OcSaveChangingOrRefundingService saveChangingOrRefundingService;

    @Autowired
    private OmsMarkCancelService markCancelService;

    private final String objid = "objid";
    private final String message = "message";


    /**
     * 全渠道订单退款
     * @param session 参数
     * @return ValueHolder
     */
    public ValueHolder refund(QuerySession session){

        List<Long> ids = new ArrayList<>();
        boolean isBatch = isBatch(session," 全渠道订单退款服务",ids);

        //查询订单，校验订单状态
        List<OcBOrder> orderList = ocBOrderMapper.selectOrderListByIdsList(ids);
        if(CollectionUtils.isEmpty(orderList)){
            throw new NDSException(Resources.getMessage("操作的订单不存在！", session.getLocale()));
        }

        //退款操作
        List<Map<String,String>> errorList = new ArrayList<>();
        for(OcBOrder ocBOrder : orderList) {
            excute(ocBOrder,errorList,session);
        }

        return getExcuteValueHolder(errorList,isBatch);
    }

    private boolean isBatch(QuerySession session,String msg,List<Long> ids){
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("全渠道订单退款服务start...\nparam-->{}"), JSON.toJSONString(param));
        }

        boolean isParamEmpty = null == param ||
                (!param.containsKey(objid) && !param.containsKey("ids"));
        if (isParamEmpty) {
            throw new NDSException(Resources.getMessage("参数格式错误！", session.getLocale()));
        }

        Long id = param.getLong(objid);
        boolean isBatch = false;
        if(null == id){
            isBatch = true;
            JSONArray idArray = param.getJSONArray("ids");
            idArray.forEach(mainId -> ids.add(Long.valueOf((String)mainId)));
        }else {
            ids.add(id);
        }
        return isBatch;
    }

    /**
     * 单条退款操作
     * @param ocBOrder 发货单信息
     * @param errorList 错误信息
     * @param session 用户信息
     */
    private void excute(OcBOrder ocBOrder,List<Map<String,String>> errorList,QuerySession session){

        Map<String,String> errMsg = new HashMap<>();
        //当订单状态为“待分配、待审核、缺货、已审核、配货中”，允许操作退款按钮服务，否则将提示：“当前订单状态不允许退款”；
        Integer orderStatus = ocBOrder.getOrderStatus();
        if(!canDoRefund(orderStatus)){
            errMsg.put(objid,ocBOrder.getId() + "");
            errMsg.put(message,"当前订单状态不允许退款");
            errorList.add(errMsg);
            return;
        }

        try {
            //订单状态为已审核、配货中的时候,调用反审核服务
            ValueHolderV14 theAuditVh = new ValueHolderV14(ResultCode.SUCCESS,"反审核成功");
            if(OmsOrderStatus.CHECKED.toInteger().equals(orderStatus) ||
                    OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)){
                OrderICheckRequest request = new OrderICheckRequest();
                Long[] theAuditIds = {ocBOrder.getId()};
                request.setIds(theAuditIds);
                request.setType(LogTypeEnum.REFUND_REVERSE_AUDIT.getType());
                theAuditVh = theAuditService.orderTheAudit(request,session.getUser(),true);
            }
            if(theAuditVh.isOK()){
                //调用订单取消服务
                JSONObject cancelObj = cancelService.separateMarked(ocBOrder.getId(),session);
                if(-1 == cancelObj.getIntValue("code")){
                    errMsg.put(objid,ocBOrder.getId() + "");
                    errMsg.put(message,"退款失败【"+cancelObj.get(message)+"】");
                    errorList.add(errMsg);
                }
            }else {
                errMsg.put(objid,ocBOrder.getId() + "");
                errMsg.put(message,"退款失败【"+theAuditVh.getMessage()+"】");
                errorList.add(errMsg);
            }


        }catch (Exception e){
            log.error(" 全渠道发货单退款失败：" + Throwables.getStackTraceAsString(e));
            errMsg.put(objid,ocBOrder.getId() + "");
            errMsg.put(message, e.getMessage());
            errorList.add(errMsg);
        }
    }

    /**
     * DRP退款取消发货单
     * @param billNos 云仓单号
     * @return valueHolder
     */
    public ValueHolder drpBatchCancel(List<String> billNos){

        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("DRP退款取消发货单-->入参：", billNos));
        }

        List<Map<String,String>> errorList = new ArrayList<>();
        if(CollectionUtils.isEmpty(billNos)){
            Map<String,String> errMsg = Maps.newHashMap();
            errMsg.put(message,"单据编号不能为空");
            errorList.add(errMsg);
            return getExcuteValueHolder(errorList,false);
        }

        //根据订单编号查询所有订单
        List<OcBOrder> orderList = ocBOrderMapper.selectList(new LambdaQueryWrapper<OcBOrder>()
                .in(OcBOrder::getBillNo,billNos)
                .ne(OcBOrder::getOrderStatus,OmsOrderStatus.CANCELLED.toInteger())
                .ne(OcBOrder::getOrderStatus,OmsOrderStatus.SYS_VOID.toInteger()));
        QuerySession session = new QuerySessionImpl(SystemUserResource.getRootUser());

        if(CollectionUtils.isEmpty(orderList)){
            Map<String,String> errMsg = Maps.newHashMap();
            errMsg.put(message,"单据编号"+ billNos + "未找到对应零售发货单");
            errorList.add(errMsg);
            return getExcuteValueHolder(errorList,false);
        }

        Map<String,List<OcBOrder>> orderMap = orderList.stream().collect(Collectors.groupingBy(OcBOrder::getBillNo));
        for(String tid : billNos) {
            List<OcBOrder> ocBOrderList = orderMap.get(tid);
            if(CollectionUtils.isEmpty(ocBOrderList)){
                Map<String,String> errMsg = Maps.newHashMap();
                errMsg.put(message,"单据编号["+ tid + "]未找到对应零售发货单");
                errorList.add(errMsg);
                continue;
            }

            //执行退款操作
            ocBOrderList.forEach(order -> excute(order,errorList,session));
        }

        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("DRP退款取消发货单-->异常记录："), errorList);
        }

        return getExcuteValueHolder(errorList,true);
    }

    /**
     * 云仓退款
     * 1、入参支持多个tid
     *
     * 2、当pos调用中台订单拦截时，根据tid+pscskuid+qty查询中台零售发货单
     *
     *     ①首先零售发货单是非仓库发货、非平台发货或非传wms中的零售单，否则反馈pos拦截失败
     *
     *      如果根据tid+pscskuid查询的中台零售发货单总数量与入参数量不匹配，反馈pos拦截失败，数量不匹配
     *
     *     ②零售发货单存在配货中或已审核，需要首先将配货中或已审核的零售发货单先进行反审核
     *
     *     ③其次批量拦截零售发货单的明细，如果存在部分拦截成功，部分拦截失败的情况，需要回滚拦截成功的零售发货单明细，反馈pos拦截失败
     *
     *     ④如果零售发货单均拦截成功，则反馈pos拦截成功
     * @param requests 单号
     * @return valueHolder
     */
    public ValueHolder posCancel(List<PosOrderCancelRequest> requests){

        if(log.isDebugEnabled()){
            log.debug(" 云仓退款参数-->{}", JSON.toJSONString(requests));
        }
        if(CollectionUtils.isEmpty(requests)){
            return getExcuteValueHolder(setErrMsg("参数不能为空"),false);
        }

        List<String> tids = requests.stream().map(PosOrderCancelRequest::getSourceCode).collect(Collectors.toList());

        //合包订单没法通过主表查询，要通过明细tid查询
        List<OcBOrderItem> origOrders = itemMapper.selectList(new LambdaQueryWrapper<OcBOrderItem>()
                .select(OcBOrderItem::getOcBOrderId).in(OcBOrderItem::getTid,tids));

        if(CollectionUtils.isEmpty(origOrders)){
            return getExcuteValueHolder(setErrMsg("平台单号对应零售发货单不存在"),false);
        }

        //查询对应发货单
        List<OcBOrder> orderList = ocBOrderMapper.selectList(new LambdaQueryWrapper<OcBOrder>()
                .in(OcBOrder::getId,origOrders.stream().map(OcBOrderItem::getOcBOrderId).collect(Collectors.toList()))
                .eq(OcBOrder::getIsactive,R3CommonResultConstants.VALUE_Y)
                .ne(OcBOrder::getOrderStatus,OmsOrderStatus.SYS_VOID.toInteger()));
        if(CollectionUtils.isEmpty(orderList)){
            return getExcuteValueHolder(setErrMsg("平台单号对应零售发货单不存在"),false);
        }

        if(log.isDebugEnabled()){
            log.debug(" 云仓退款,tid对应所有零售发货单-->{}", JSON.toJSONString(orderList));
        }

        List<Long> orderIds = orderList.stream().map(OcBOrder::getId).collect(Collectors.toList());

        //查询明细
        List<OcBOrderItem> itemList = itemMapper.selectOrderItemsByOrderIds(orderIds);
        String itemErr = "可退款明细与申请退款明细不一致，不允许退款";
        if(CollectionUtils.isEmpty(itemList)){
            return getExcuteValueHolder(setErrMsg(itemErr),false);
        }
        if(log.isDebugEnabled()){
            log.debug(" 云仓退款,tid对应所有可退明细-->{}", JSON.toJSONString(itemList));
        }

        List<OcBOrderItem> inItemList = new ArrayList<>();
        List<OcBOrderItem> cancelItemList = new ArrayList<>();
        //校验参数
        for(PosOrderCancelRequest r : requests){

            if(ObjectUtils.isEmpty(r.getSourceCode()) || CollectionUtils.isEmpty(r.getItemList())){
                return getExcuteValueHolder(setErrMsg("平台单号或退款明细不能为空"),false);
            }

            inItemList.addAll(r.getItemList());
        }
        //如果传入的明细和已有明细不一致，不允许退款
        Map<Long,List<OcBOrderItem>> inMap = inItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuId));
        Map<Long,List<OcBOrderItem>> itemMap = itemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuId));
        for(Map.Entry<Long,List<OcBOrderItem>> entry : inMap.entrySet()){
            List<OcBOrderItem> skuItems = itemMap.get(entry.getKey());
            if(CollectionUtils.isEmpty(skuItems)){
                return getExcuteValueHolder(setErrMsg(itemErr),false);
            }
            BigDecimal inQty = entry.getValue().stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO,BigDecimal::add);
            BigDecimal orderQty = skuItems.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO,BigDecimal::add);
            if(inQty.compareTo(orderQty) != 0){
                return getExcuteValueHolder(setErrMsg(itemErr),false);
            }
            cancelItemList.addAll(skuItems);
        }

        //只操作可退明细对应的零售发货单，过滤掉无需操作的单据
        List<Long> itemOrderIds = cancelItemList.stream().map(OcBOrderItem::getOcBOrderId).distinct().collect(Collectors.toList());
        orderList = orderList.stream().filter(o -> itemOrderIds.contains(o.getId())).collect(Collectors.toList());

        for(OcBOrder ocBOrder : orderList){
            //零售发货单状态校验
            if(!canDoRefund(ocBOrder.getOrderStatus())){
                return getExcuteValueHolder(setErrMsg("仅订单状态为“待分配、待审核、缺货、已审核、配货中”允许退款"),false);
            }
            //配货中、已审核零售发货单反审核
            if(OmsOrderStatus.CHECKED.toInteger().equals(ocBOrder.getOrderStatus()) ||
                    OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(ocBOrder.getOrderStatus())){
                OrderICheckRequest request = new OrderICheckRequest();
                Long[] theAuditIds = {ocBOrder.getId()};
                request.setIds(theAuditIds);
                request.setType(LogTypeEnum.REFUND_REVERSE_AUDIT.getType());
                ValueHolderV14 theAuditVh = theAuditService.orderTheAudit(request,SystemUserResource.getRootUser(),true);
                if(!theAuditVh.isOK()){
                    return getExcuteValueHolder(setErrMsg("退款失败:"+theAuditVh.getMessage()),false);
                }
            }
        }

        //标记退款完成
        try {
            OcBOrderAllRefundService refundService = ApplicationContextHandle.getBean(OcBOrderAllRefundService.class);
            refundService.batchMarkCancel(orderList,cancelItemList);
        }catch (Exception e){
            log.error(" 云POS标记退款完成异常：{}",Throwables.getStackTraceAsString(e));
            return getExcuteValueHolder(setErrMsg("退款失败：" + e.getMessage()),false);
        }

        return getExcuteValueHolder(new ArrayList<>(),false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchMarkCancel(List<OcBOrder> orderList,List<OcBOrderItem> cancelItemList){

        Map<Long,List<OcBOrderItem>> itemMap = cancelItemList.stream()
                .collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
        List<SgOmsReleaseOutRequest> requests = new ArrayList<>();
        Map<Long,OcBOrder> orderMap = orderList.stream().collect(Collectors.toMap(OcBOrder::getId, Function.identity()));

        itemMap.forEach((k,v) -> {
            List<Long> itemIds = v.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            //标记退款完成
            ValueHolderV14 cancelResult = markCancelService.markCancel(k,itemIds, SystemUserResource.getRootUser(),
                    OrderHoldReasonEnum.REFUND_HOLD,Boolean.TRUE);
            AssertUtil.assertException(!cancelResult.isOK(),cancelResult.getMessage());
            requests.add(markCancelService.builReleaseParam(orderMap.get(k),v,SystemUserResource.getRootUser()));
        });

        //批量多商品释放库存
        if(!CollectionUtils.isEmpty(requests)){
            ValueHolderV14<String> valueHolderV14 = sgRpcService.batchReleaseOutStock(requests);
            AssertUtil.assertException(!valueHolderV14.isOK(),valueHolderV14.getMessage());
        }

    }


    /**
     * 批量通知发货方入库
     * @param session 入参
     * @return
     */
    public ValueHolder noticeIn(QuerySession session) {

        List<Long> ids = new ArrayList<>();
        boolean isBatch = isBatch(session," 全渠道订单通知发货方入库",ids);
        //查询订单，校验订单状态
        List<OcBOrder> orderList = ocBOrderMapper.selectOrderListByIdsList(ids);
        if(CollectionUtils.isEmpty(orderList)){
            return ValueHolderUtils.getFailValueHolder("操作的发货单不存在！");
        }

        //通知操作
        List<Map<String,String>> errorList = new ArrayList<>();
        for(OcBOrder ocBOrder : orderList) {
            singleNotice(ocBOrder,errorList,session);
        }

        return getNoticeValueHolder(errorList,isBatch);
    }

    /**
     * 单个通知发货方入库
     * @param ocBOrder 发货单
     * @param errorList 错误信息
     * @param session 入参
     */
    private void singleNotice(OcBOrder ocBOrder, List<Map<String,String>> errorList, QuerySession session){

        Map<String,String> errMsg = new HashMap<>();

        try{
            List<OcBOrderItem> itemList = itemMapper.selectOrderItemListOccupy(ocBOrder.getId());
            Assert.isTrue(!CollectionUtils.isEmpty(itemList),"发货单可退款明细为空");
            if(log.isDebugEnabled()){
                log.debug(" 通知发货方入库--订单--{}，明细--{}",JSON.toJSONString(ocBOrder),JSON.toJSONString(itemList));
            }

            //给退单主表赋值
            OcBReturnOrder ocBReturnOrder = new OcBReturnOrder();
            BeanUtils.copyProperties(ocBOrder,ocBReturnOrder);
            ocBReturnOrder.setOrigOrderId(ocBOrder.getId());
            ocBReturnOrder.setOrigSourceCode(ocBOrder.getSourceCode());
            //默认退货单
            ocBReturnOrder.setBillType(1);
            ocBReturnOrder.setBuyerNick(ocBOrder.getUserNick());
            //默认退货原因：我不想要了
            ocBReturnOrder.setReturnReason("9");
            ocBReturnOrder.setIsReserved(1);
            //默认是否原退：是
            ocBReturnOrder.setIsBack(1);
            ocBReturnOrder.setLogisticsCode(ocBOrder.getExpresscode());
            ocBReturnOrder.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
            ocBReturnOrder.setCpCPhyWarehouseInId(ocBOrder.getCpCPhyWarehouseId());
            ocBReturnOrder.setReceiverAreaId(ocBOrder.getCpCRegionAreaId());
            ocBReturnOrder.setReceiverAreaName(ocBOrder.getCpCRegionAreaEname());
            ocBReturnOrder.setReceiverCityId(ocBOrder.getCpCRegionCityId());
            ocBReturnOrder.setReceiverCityName(ocBOrder.getCpCRegionCityEname());
            ocBReturnOrder.setReceiverProvinceId(ocBOrder.getCpCRegionProvinceId());
            ocBReturnOrder.setReceiverProvinceName(ocBOrder.getCpCRegionProvinceEname());
            ocBReturnOrder.setReceiveAddress(ocBOrder.getReceiverAddress());
            ocBReturnOrder.setReceivePhone(ocBOrder.getReceiverPhone());
            ocBReturnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());
            ocBReturnOrder.setReceiveName(ocBOrder.getReceiverName());
            ocBReturnOrder.setReceiveZip(ocBOrder.getReceiverZip());
            ocBReturnOrder.setOrdeSource(ocBOrder.getOrderSource());
            ocBReturnOrder.setReturnAmtList(ocBOrder.getReceivedAmt());
            ocBReturnOrder.setReturnAmtActual(ocBOrder.getReceivedAmt());
            ocBReturnOrder.setQtyInstore(ocBOrder.getQtyAll());

            //给明细赋值
            List<OcBReturnOrderRefund> refundList = new ArrayList<>();
            itemList.forEach(item -> {
                OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
                BeanUtils.copyProperties(item,returnOrderRefund);
                returnOrderRefund.setId(-1L);
                returnOrderRefund.setOcBOrderId(ocBOrder.getId());
                returnOrderRefund.setOcBOrderItemId(item.getId());
                returnOrderRefund.setAmtAdjust(item.getAdjustAmt());
                returnOrderRefund.setAmtRefund(item.getRealAmt());
                returnOrderRefund.setAmtRefundSingle(item.getPriceActual());
                returnOrderRefund.setQtyIn(BigDecimal.ZERO);
                returnOrderRefund.setProductMark(item.getGroupGoodsMark());
                returnOrderRefund.setQtyCanRefund(item.getQty());
                returnOrderRefund.setQtyRefund(item.getQty());
                refundList.add(returnOrderRefund);
            });


            JSONObject object = new JSONObject();
            object.put(objid,-1);
            object.put("isRefund2Exchange",0);
            object.put("OcBreturnOrder",ocBReturnOrder);
            object.put("OcBreturnOrderRefund",refundList);


            if(log.isDebugEnabled()){
                log.debug(" 通知发货方入库，创建退货单参数--{}",object.toJSONString());
            }
            User user = session.getUser();
            ValueHolder valueHolder = saveChangingOrRefundingService.saveChangingOrRefunding(object,user);
            if(log.isDebugEnabled()){
                log.debug(" 通知发货方入库，创建退货单结果--{}",JSON.toJSONString(valueHolder));
            }
            Assert.isTrue(valueHolder.isOK(),(String)valueHolder.get(message));

            //修改发货单为--已通知发货方入库
            OcBOrder update = new OcBOrder();
            update.setId(ocBOrder.getId());
            update.setIsNoticeDelivery("1");
            BaseModelUtil.makeBaseModifyField(update,user);
            ocBOrderMapper.updateById(update);

        }catch (Exception e){
            log.error(" 全渠道发货单通知发货方入库失败：" + Throwables.getStackTraceAsString(e));
            errMsg.put(objid,ocBOrder.getId() + "");
            errMsg.put(message, e.getMessage());
            errorList.add(errMsg);
        }

    }

    /**
     * 设置错误信息
     * @param msg 提示
     * @return 错误
     */
    private List<Map<String,String>> setErrMsg(String msg){
        List<Map<String,String>> errorList = new ArrayList<>();
        Map<String,String> errMsg = Maps.newHashMap();
        errMsg.put(message,msg);
        errorList.add(errMsg);
        return errorList;
    }

    /**
     * 组装返回结果
     * @param errorArray 错误信息
     * @param isBatch 是否批量操作
     * @return valueHolder
     */
    private ValueHolder getExcuteValueHolder(List<Map<String,String>> errorArray,boolean isBatch) {
        ValueHolder valueHolder = new ValueHolder();
        if (errorArray.isEmpty()) {
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put(message, "退款成功！");
        }else if(isBatch){
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put(message, "退款失败记录数：" + errorArray.size());
        }else {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put(message, errorArray.get(0).get(message));
        }

        valueHolder.put("data", errorArray);
        return valueHolder;
    }

    /**
     * 组装返回结果
     * @param errorArray 错误信息
     * @param isBatch 是否批量操作
     * @return valueHolder
     */
    private ValueHolder getNoticeValueHolder(List<Map<String,String>> errorArray,boolean isBatch) {
        ValueHolder valueHolder = new ValueHolder();
        if (errorArray.isEmpty()) {
            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put(message, "通知发货方入库成功！");
        }else if(isBatch){
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put(message, "通知发货方入库失败记录数：" + errorArray.size());
        }else {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put(message, errorArray.get(0).get(message));
        }

        valueHolder.put("data", errorArray);
        return valueHolder;
    }

    /**
     * 是否满足退款条件
     * @param orderStatus 单据状态
     * @return 是/否
     */
    private boolean canDoRefund(Integer orderStatus){

        return OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(orderStatus) ||
                    OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus) ||
                    OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus) ||
                    OmsOrderStatus.CHECKED.toInteger().equals(orderStatus) ||
                    OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus);
    }
}
