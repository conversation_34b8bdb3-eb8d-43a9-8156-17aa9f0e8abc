package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @ClassName GetAppointDistLevel3Service
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/23 18:34
 * @Version 1.0
 */
@Component
public class GetAppointDistLevel3Service {

    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    public String getAppointDistLevel3(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems) {
        // 优先级  店铺强指定分货部门>特定业务类型与领用类型强指定分货部门>特定业务类型强指定分货部门
        String shopCode = ocBOrder.getCpCShopEcode();
        String businessTypeCode = ocBOrder.getBusinessTypeCode();
        String planLineCategory = ocBOrderItems.get(0).getPlanLineCategory();
        Map<String, String> specifiedShopMap = businessSystemParamService.getSpecifiedShop();
        // 店铺强指定分货部门
        if (specifiedShopMap != null && specifiedShopMap.containsKey(shopCode)) {
            return specifiedShopMap.get(shopCode);
        }
        // 特定业务类型与领用类型强指定分货部门
        Map<String, Map<String, String>> specifiedBusinessCollectTypeMap = businessSystemParamService.getSpecifiedBusinessCollectType();
        if (StringUtils.isNotEmpty(planLineCategory)) {
            Map<String, String> specifiedBusinessCollectType = specifiedBusinessCollectTypeMap.get(businessTypeCode);
            if (specifiedBusinessCollectType != null && specifiedBusinessCollectType.containsKey(planLineCategory)) {
                return specifiedBusinessCollectType.get(planLineCategory);
            }
        }
        // 特定业务类型强指定分货部门
        Map<String, String> specifiedBusinessTypeMap = businessSystemParamService.getSpecifiedBusinessType();
        if (specifiedBusinessTypeMap != null && specifiedBusinessTypeMap.containsKey(businessTypeCode)) {
            return specifiedBusinessTypeMap.get(businessTypeCode);
        }
        return null;
    }
}
