package com.jackrain.nea.oc.oms.services.audit;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.github.pagehelper.util.StringUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.es.ES4IpJitXOrder;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBAuditTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.AutoAuditStatus;
import com.jackrain.nea.oc.oms.model.enums.IpBTaobaoModifyAddrStatus;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.LogisticsCompany;
import com.jackrain.nea.oc.oms.model.enums.MaterielType;
import com.jackrain.nea.oc.oms.model.enums.OcOrderDoublellPresaleStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedReason;
import com.jackrain.nea.oc.oms.model.enums.OmsEffectiveConditionEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsGiftJudge;
import com.jackrain.nea.oc.oms.model.enums.OmsMethod;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OmsStOrderType;
import com.jackrain.nea.oc.oms.model.enums.OmsSwitch;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoModifyAddr;
import com.jackrain.nea.oc.oms.model.table.OcBJitxModifyWarehouseLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBAuditTask;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowStateEnum;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.services.IpBJitxResetShipWorkflowService;
import com.jackrain.nea.oc.oms.services.IpTaobaoOrderService;
import com.jackrain.nea.oc.oms.services.OcBJitxModifyWarehouseLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderDistributeWarehouseService;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.OrderStrategyPriceComputeService;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderSpiltUtill;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.oc.oms.util.SplitOrderUtils;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.rpc.VpRpcService;
import com.jackrain.nea.sg.service.SgOutStockNoticeService;
import com.jackrain.nea.st.model.StCAutoCheck;
import com.jackrain.nea.st.model.StCAutoCheckAutoTime;
import com.jackrain.nea.st.model.StCAutoCheckExcludeProduct;
import com.jackrain.nea.st.model.StCAutoCheckRelation;
import com.jackrain.nea.st.model.result.MailInfoResult;
import com.jackrain.nea.st.model.result.StCAutoCheckResult;
import com.jackrain.nea.st.model.result.StCPriceResult;
import com.jackrain.nea.st.model.table.StCAutoCheckAutoTimeDO;
import com.jackrain.nea.st.model.table.StCAutoCheckExcludeProductDO;
import com.jackrain.nea.st.model.table.StCOrderPriceItemDO;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.model.table.StCVipcomMailDO;
import com.jackrain.nea.st.service.OmsOrderPriceSchemeService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.VipComMailPlanService;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ComputeEqualExchangeQtyUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <p>订单自动审核</>
 *
 * @author: heliu
 * @since: 2019/3/25
 * create at : 2019/3/25 16:56
 */
@Component
@Slf4j
public class OmsOrderAutoAuditService {
    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    @Autowired
    private OrderPlatformDeliveryService orderPlatformDeliveryService;

    @Autowired
    private OcBJitxModifyWarehouseLogService ocBJitxModifyWarehouseLogService;

    @Autowired
    private IpBJitxResetShipWorkflowService ipBJitxResetShipWorkflowService;

    @Autowired
    private VipcomJitxWarehouseService vipcomJitxWarehouseService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsOrderPriceSchemeService omsOrderPriceSchemeService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderDistributeWarehouseService omsOrderDistributeWarehouseService;

    @Autowired
    private IpTaobaoOrderService ipTaobaoOrderService;

    @Autowired
    private OrderStrategyPriceComputeService orderStrategyPriceComputeService;

    @Autowired
    private VpRpcService vpRpcService;

    @Autowired
    private SgOutStockNoticeService sgOutStockNoticeService;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OmsWmsTaskService wmsTaskService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsOrderManualAuditService omsOrderManualAuditService;

    @Autowired
    private VipComMailPlanService vipComMailPlanService;

    @Autowired
    private OcBAuditTaskMapper ocBAuditTaskMapper;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    private SplitOrderUtils splitOrderUtils;

    @Autowired
    OmsOrderSpiltUtill omsOrderSpiltUtill;

    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;

    @Autowired
    private IpBStandplatOrderMapper ipBStandplatOrderMapper;

    @Autowired
    private IpBStandplatOrderItemMapper ipBStandplatOrderItemMapper;

    private static int[] ladder = new int[]{1, 10, 20, 30, 60, 120, 180, 240, 300, 480, 300, 300, 300, 300, 300, 300};

    private static String ORDER_AMOUNT = "1";
    private static String ORDER_DISCOUNT = "2";

    /**
     * 更新系统备注
     *
     * @param orderInfo 订单对象
     * @param sysRemark 系统备注
     */
    public void updateOrderInfo(OmsMethod omsMethod, OcBOrder orderInfo, String sysRemark,
                                OmsAuditFailedReason auditFailedReason) {

        if (orderInfo == null) {
            return;
        }
        OcBOrder ocBOrderDto = new OcBOrder();
        ocBOrderDto.setId(orderInfo.getId());
        ocBOrderDto.setSysremark(SplitMessageUtil.splitMesssage(sysRemark));
        ocBOrderDto.setAuditTime(new Date(System.currentTimeMillis()));
        if (omsMethod != null && OmsMethod.AUTO.equals(omsMethod)) {
            //自动审核状态
            ocBOrderDto.setAutoAuditStatus(AutoAuditStatus.Audit_FAIL.toInteger());
        }

        if (auditFailedReason != null) {
            //审核失败类型
            ocBOrderDto.setAuditFailedType(auditFailedReason.getVal());
            ocBOrderDto.setAuditFailedReason(auditFailedReason.getKey());
        }

        omsOrderService.updateOrderInfo(ocBOrderDto);

        if (omsMethod != null && OmsMethod.AUTO.equals(omsMethod)) {
            //处理task的时间
            this.failAuditHandle(ocBOrderDto.getId());
        }

    }

    /**
     * 自动审核失败重置审核task表
     *
     * @param orderId
     */
    public void failAuditHandle(Long orderId) {
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Long retryTime = config.getProperty("audit_retry_time", 60000L);
            List<OcBAuditTask> auditTasks = ocBAuditTaskMapper.selectTaskListByOrderId(orderId);
            if (CollectionUtils.isEmpty(auditTasks)) {
                return;
            }
            OcBAuditTask ocBAuditTask = null;
            if (auditTasks.size() > 1) {
                //如果发现出现多条task数据  则删除多余的只剩一条
                List<Long> ids = new ArrayList<>();
                for (int i = 0; i < auditTasks.size(); i++) {
                    OcBAuditTask ocBAudit = auditTasks.get(i);
                    if (i == 0) {
                        ocBAuditTask = ocBAudit;
                        continue;
                    }
                    ids.add(ocBAudit.getId());
                }
                if (ocBAuditTask != null) {
                    ids.remove(ocBAuditTask.getId());
                }
                ocBAuditTaskMapper.delTaskStatusByOrderId(orderId, ids);
            }
            if (ocBAuditTask == null) {
                ocBAuditTask = auditTasks.get(0);
            }
            if (ocBAuditTask != null) {
                //错误次数
                Integer retryNumber = ocBAuditTask.getRetryNumber() == null ? 0 : ocBAuditTask.getRetryNumber();
                if (retryNumber > 15) {
                    return;
                }
                int i = ladder[retryNumber];
                retryNumber = retryNumber + 1;
                long time = retryTime * i;
                Long nextTime = System.currentTimeMillis() + time;
                ocBAuditTaskMapper.updateTaskByOrderId(orderId, retryNumber, nextTime);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("重置Task表状态失败.异常: {}"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 是否存在店铺审核策略
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public StCAutoCheckRelation isExitsStrategy(OcBOrderRelation orderInfo) {

        try {
            StCAutoCheckResult autoCheckResult = stRpcService.queryOcStCAutocheck(orderInfo.getOrderInfo().getCpCShopId());
            if (autoCheckResult == null || autoCheckResult.getStCAutoCheckDO() == null) {
                throw new NDSException("ShopId=" + orderInfo.getOrderInfo().getCpCShopId() + ",店铺审核策略为空");
            }

            StCAutoCheckRelation autoCheckRelation = modelTransfer(autoCheckResult);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OrderId={},isExitsStrategy方法内，return autoCheck为：{}"), orderInfo.getOrderId(), autoCheckRelation.getAutoCheck());
            }

            return autoCheckRelation;
        } catch (Exception ex) {
            String message = "OrderId为" + orderInfo.getOrderInfo().getId() + "的订单调用策略中心店铺审核策略接口异常！" + ex.getMessage();
            log.error(LogUtil.format("订单调用策略中心店铺审核策略接口异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            OcBOrder ocBOrderDto = new OcBOrder();
            ocBOrderDto.setId(orderInfo.getOrderId());
            ocBOrderDto.setAutoAuditStatus(AutoAuditStatus.Audit_FAIL.toInteger());
            ocBOrderDto.setSysremark(SplitMessageUtil.splitMesssage(message));
            omsOrderService.updateOrderInfo(ocBOrderDto);
            throw new NDSException(message);
        }
    }

    private StCAutoCheckRelation modelTransfer(StCAutoCheckResult autoCheckResult) {

        StCAutoCheckRelation autoCheckRelation = new StCAutoCheckRelation();

        StCAutoCheck stCAutoCheck = new StCAutoCheck();
        this.copyBean(autoCheckResult.getStCAutoCheckDO(), stCAutoCheck);
        autoCheckRelation.setAutoCheck(stCAutoCheck);

        List<StCAutoCheckAutoTime> autoCheckAutoTimeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(autoCheckResult.getAutoTimes())) {
            for (StCAutoCheckAutoTimeDO item : autoCheckResult.getAutoTimes()) {
                StCAutoCheckAutoTime autoCheckAutoTime = new StCAutoCheckAutoTime();
                this.copyBean(item, autoCheckAutoTime);
                autoCheckAutoTimeList.add(autoCheckAutoTime);
            }
            autoCheckRelation.setAutoCheckAutoTimes(autoCheckAutoTimeList);
        }

        List<StCAutoCheckExcludeProduct> excludeProductList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(autoCheckResult.getExcludeProducts())) {
            for (StCAutoCheckExcludeProductDO item : autoCheckResult.getExcludeProducts()) {
                StCAutoCheckExcludeProduct excludeProduct = new StCAutoCheckExcludeProduct();
                this.copyBean(item, excludeProduct);
                excludeProductList.add(excludeProduct);
            }
            autoCheckRelation.setAutoCheckExcludeProducts(excludeProductList);
        }

        return autoCheckRelation;
    }

    /**
     * 是否包含有效条件
     *
     * @param effectiveConditionStr
     * @param effectiveConditionEnum
     * @return
     */
    public boolean isContainsEffectiveValue(String effectiveConditionStr, String effectiveConditionEnum) {
        boolean isContainsEffectiveFlag = false;
        if (StringUtils.isNotEmpty(effectiveConditionStr)) {
            if (effectiveConditionStr.contains(",")) {
                String[] effectiveArray = effectiveConditionStr.split(",");
                for (String effectiveValue : effectiveArray) {
                    if (effectiveValue.equals(effectiveConditionEnum)) {
                        isContainsEffectiveFlag = true;
                        break;
                    }
                }
            } else {
                if (effectiveConditionStr.equals(effectiveConditionEnum)) {
                    isContainsEffectiveFlag = true;
                }
            }
        }
        return isContainsEffectiveFlag;
    }

    public <T> T copyBean(T oldBean, T newBean) {
        if (null != oldBean) {
            BeanUtils.copyProperties(oldBean, newBean);
            return newBean;
        }
        return null;
    }

    /**
     * 检查付款时间
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkDateType(OcBOrderRelation orderInfo) {

        try {
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            String effectiveCondition = ocStCAutoCheck.getEffectiveCondition();
            boolean isContainsEffectiveFlag = isContainsEffectiveValue(effectiveCondition,
                    OmsEffectiveConditionEnum.PAY_TIME.parseValue());

            if (!isContainsEffectiveFlag) {
                return false;
            }

            if (ocStCAutoCheck.getBeginTime() != null && ocStCAutoCheck.getEndTime() == null) {
                return ocStCAutoCheck.getBeginTime().before(orderInfo.getOrderInfo().getPayTime());
            }
            if (ocStCAutoCheck.getBeginTime() == null && ocStCAutoCheck.getEndTime() != null) {
                return ocStCAutoCheck.getEndTime().after(orderInfo.getOrderInfo().getPayTime());
            }
            if (ocStCAutoCheck.getBeginTime() != null && ocStCAutoCheck.getEndTime() != null) {
                //判断付款日期是否在审核策略时间范围内
                return (ocStCAutoCheck.getBeginTime().before(orderInfo.getOrderInfo().getPayTime())
                        && ocStCAutoCheck.getEndTime().after(orderInfo.getOrderInfo().getPayTime()));
            } else {
                return true;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("检查订单付款时间异常,审核失败={}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 检查付款类型
     *
     * @param orderInfo 订单表
     * @return boolean
     */
    public boolean checkPayType(OcBOrderRelation orderInfo) {

        try {
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            if (StringUtils.isEmpty(ocStCAutoCheck.getIsAutocheckPay())) {
                return true;
            } else {
                //判断订单中付款方式为货到付款，则根据【店铺】在对应的【订单自动审核策略】中未启用“自动审核货到付款”
                if (OmsPayType.CASH_ON_DELIVERY.toInteger() == orderInfo.getOrderInfo().getPayType()
                        && IsActiveEnum.N.getKey().equalsIgnoreCase(ocStCAutoCheck.getIsAutocheckPay())) {
                    return false;
                } else {
                    return true;
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("检查付款类型,审核失败={}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 检查买家备注
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkSellerRemark(OcBOrderRelation orderInfo) {

        try {
            //取出审核策略
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            String effectiveCondition = ocStCAutoCheck.getEffectiveCondition();
            boolean isContainsEffectiveFlag = isContainsEffectiveValue(effectiveCondition,
                    OmsEffectiveConditionEnum.SELLER_REMARK.parseValue());
            if (!isContainsEffectiveFlag) {
                return true;
            }
            //订单卖家备注
            if (StringUtils.isEmpty(orderInfo.getOrderInfo().getSellerMemo())) {
                return true;
            }
            String sellerRemark = orderInfo.getOrderInfo().getSellerMemo().trim();
            String autoSellerRemark = ocStCAutoCheck.getSellerRemark().trim();
            //勾选有卖家备注订单自动审核按钮 且 存在卖家备注
            if (StringUtils.isEmpty(sellerRemark) || StringUtils.isEmpty(autoSellerRemark)) {
                return true;
            }
            /** 存储临时卖家备注 **/
            List<String> sellerString = new ArrayList<>();
            autoSellerRemark = autoSellerRemark.replace(",", "，");
            //卖家备注
            String[] sellerKeyWordValue = autoSellerRemark.split("，");
            for (int i = 0; i < sellerKeyWordValue.length; i++) {
                sellerString.add(sellerKeyWordValue[i]);
            }
            /** 买家备注内容判断 场景：平台自动匹配备注，需要中台设置改备注进行自动审核 **/
            for (String sellerKeyWord : sellerString) {
                if (sellerRemark.contains(sellerKeyWord.trim())) {
                    return false;
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("检查订单卖家备注异常,审核失败={}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
        return true;
    }

    /**
     * 检查买家备注
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkBuyerRemark(OcBOrderRelation orderInfo) {
        try {
            //取出审核策略
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            String effectiveCondition = ocStCAutoCheck.getEffectiveCondition();
            boolean isContainsEffectiveFlag = isContainsEffectiveValue(effectiveCondition,
                    OmsEffectiveConditionEnum.BUYER_REMARK.parseValue());
            if (!isContainsEffectiveFlag) {
                return true;
            }
            if (ObjectUtil.isNull(orderInfo.getOrderInfo().getBuyerMessage())) {
                return true;
            }
            //订单买家备注
            String buyerReamrk = orderInfo.getOrderInfo().getBuyerMessage().trim();
            //判断策略买家备注是否为空
            String autoBuyerRemark = ocStCAutoCheck.getBuyerRemark().trim();
            //勾选有买家备注订单自动审核按钮 且 订单存在买家备注
            if (StringUtils.isEmpty(buyerReamrk) || StringUtils.isEmpty(autoBuyerRemark)) {
                return true;
            }
            /** 存储临时策略买家备注 **/
            List<String> buyerString = new ArrayList<>();
            autoBuyerRemark = autoBuyerRemark.replace(",", "，");
            //买家备注
            String[] buyerKeyWordValue = autoBuyerRemark.split("，");
            for (int i = 0; i < buyerKeyWordValue.length; i++) {
                buyerString.add(buyerKeyWordValue[i]);
            }
            /** 买家备注内容判断 场景：平台自动匹配备注，需要中台设置改备注进行自动审核 **/
            for (String buyerKeyWord : buyerString) {
                if (buyerReamrk.contains(buyerKeyWord.trim())) {
                    return false;
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("检查买家备注,审核失败={}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
        return true;
    }

    /**
     * 检查物流公司
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkLogistics(OcBOrderRelation orderInfo) {
        try {
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            //订单的“物流公司”在【订单自动审核策略】中的“排除物流公司”字段中存在，
            if (StringUtils.isEmpty(ocStCAutoCheck.getCpCLogisticsId())) {
                return true;
            } else {
                String[] logitsicsKeyWordValue = ocStCAutoCheck.getCpCLogisticsId().split(",");
                List<String> idsString = new ArrayList<>();
                for (int i = 0; i < logitsicsKeyWordValue.length; i++) {
                    idsString.add(logitsicsKeyWordValue[i]);
                }
                if (idsString.contains(String.valueOf(orderInfo.getOrderInfo().getCpCLogisticsId()))) {
                    return false;
                } else {
                    return true;
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("检查排除策略物流公司异常,审核失败={}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 检查订单中“订单总额”（主表）【订单自动审核策略】中的限制订单金额范围
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkOrderAmountScope(OcBOrderRelation orderInfo) {

        try {
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            String effectiveCondition = ocStCAutoCheck.getEffectiveCondition();
            boolean isContainsEffectiveFlag = isContainsEffectiveValue(effectiveCondition,
                    OmsEffectiveConditionEnum.ORDER_AMOUNT.parseValue());
            if (!isContainsEffectiveFlag) {
                return true;
            }
            BigDecimal orderAmt = (orderInfo.getOrderInfo().getOrderAmt() == null ?
                    BigDecimal.ZERO : orderInfo.getOrderInfo().getOrderAmt());

            BigDecimal limitPriceUp = (ocStCAutoCheck.getLimitPriceUp() == null ?
                    BigDecimal.ZERO : ocStCAutoCheck.getLimitPriceUp());

            BigDecimal limitPriceDown = (ocStCAutoCheck.getLimitPriceDown() == null ?
                    BigDecimal.ZERO : ocStCAutoCheck.getLimitPriceDown());

            if (limitPriceUp.compareTo(BigDecimal.ZERO) != 0
                    || limitPriceDown.compareTo(BigDecimal.ZERO) != 0) {
                if ((orderAmt.compareTo(limitPriceUp) < 0 || orderAmt.compareTo(limitPriceUp) == 0)
                        && (orderAmt.compareTo(limitPriceDown) > 0 || orderAmt.compareTo(limitPriceDown) == 0)) {
                    return false;
                }
            }
            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("检查订单中“订单总额”（主表）大于【订单自动审核策略】中的限制订单金额异常,审核失败={}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 排除收货地址：关键词组，如“上海，北京”
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkReceiverAddress(OcBOrderRelation orderInfo) {

        try {
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            String effectiveCondition = ocStCAutoCheck.getEffectiveCondition();
            boolean isContainsEffectiveFlag = isContainsEffectiveValue(effectiveCondition,
                    OmsEffectiveConditionEnum.RECIEVE_ADRESS.parseValue());
            if (!isContainsEffectiveFlag) {
                return true;
            }
            String splitString = ocStCAutoCheck.getReceiverAddress().trim();
            List<String> strList = new ArrayList<>();
            if (StringUtils.isNotEmpty(splitString)) {
                splitString = splitString.replace(",", "，");
                strList = Arrays.asList(splitString.split("，"));
            }
            String receiverAddress = orderInfo.getOrderInfo().getReceiverAddress().trim();
            if (StringUtil.isNotEmpty(receiverAddress)) {
                for (String str : strList) {
                    if (receiverAddress.contains(str.trim())) {
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception ex) {
            String errorMsg = StringUtils.isEmpty(ex.getMessage()) ? "" : ex.getMessage();
            String message = "订单判断收货人省存在【订单自动审核策略】中的“排除收货人所在省”信息服务异常,审核失败!" + errorMsg;
            log.error(LogUtil.format("排除收货地址,审核失败={}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_22);
            return false;
        }
    }

    /**
     * 替换指定后缀的字符串
     *
     * @param str 传进来参数
     * @return 替换过后缀的字符
     */
    public String replaceStr(String str) {

        if (str.contains("省")) {
            return str.replace("省", "");
        } else if (str.contains("市")) {
            return str.replace("市", "");
        } else if (str.contains("维吾尔自治区")) {
            return str.replace("维吾尔自治区", "");
        } else if (str.contains("壮族自治区")) {
            return str.replace("壮族自治区", "");
        } else if (str.contains("回族自治区")) {
            return str.replace("回族自治区", "");
        } else if (str.contains("西藏自治区")) {
            return str.replace("自治区", "");
        } else if (str.contains("特别行政区")) {
            return str.replace("特别行政区", "");
        } else if (str.contains("自治州")) {
            return str.replace("自治州", "");
        } else if (str.contains("县")) {
            return str.replace("县", "");
        } else if (str.contains("区")) {
            return str.replace("区", "");
        } else if (str.contains("镇")) {
            return str.replace("镇", "");
        } else {
            return str;
        }
    }

    /**
     * 检查黑名单
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkOrderBlacklist(OcBOrderRelation orderInfo) {

        try {
            //用户手机
            String phone = orderInfo.getOrderInfo().getReceiverMobile();
            //平台类型
            Integer platform = orderInfo.getOrderInfo().getPlatform();
            //订单Id
            Long orderId = orderInfo.getOrderId();
            //查询黑名单服务
            ValueHolderV14 valueHolderV14 = null;// vpRpcService.queryBlackList(orderId, phone, platform);
            if (valueHolderV14.getCode() == 0) {
                log.error(LogUtil.format("用户为黑名单用户，不允许审核", orderId));
                return false;
            } else if (valueHolderV14.getCode() == -1) {
                log.error(LogUtil.format("的订单调用查询黑名单服务异常，不允许审核", orderId));
                return false;
            } else {
                return true;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("的订单调用查询黑名单服务异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 判断“平台”是京东，且“付款方式”为货到付款订单，若物流公司非京东(根据物流公司ID为-2进行判断)
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkOrderPlatform(OcBOrderRelation orderInfo) {

        try {
            //判断“付款方式”为货到付款订单
            if (OmsPayType.CASH_ON_DELIVERY.toInteger() == orderInfo.getOrderInfo().getPayType()) {
                //物流公司非京东(根据物流公司ID为-2进行判断)
                if (LogisticsCompany.JINGDONG.getLongId().compareTo(
                        orderInfo.getOrderInfo().getCpCLogisticsId()) != 0 &&
                        !LogisticsCompany.JINGDONG_DX.getCode().equals(orderInfo.getOrderInfo().getCpCLogisticsEcode())) {
                    return false;
                }
            }
            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("京东，且“付款方式”为货到付款订单服务异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 重单.校验
     *
     * @param relation 订单
     * @return true : 通过
     */
    public boolean checkDuplicateOrder(OcBOrderRelation relation) {

        String sourceCode = relation.getOrderInfo().getSourceCode();
        String suffixInfo = relation.getOrderInfo().getSuffixInfo();
        if (StringUtils.isBlank(sourceCode)) {
            log.error(LogUtil.format("平台单号为空,不进行处理", relation.getOrderId()), relation.getOrderId());
            return true;
        }
        List<Long> dds;
        try {

            if (StringUtils.isNotBlank(suffixInfo)) {
                dds = GSI4Order.listIdsBySourceCodeSuffixInfo4Audit(sourceCode, relation.getOrderId(), suffixInfo);
            } else {
                dds = GSI4Order.listIdsBySourceCode4AuditOrder(sourceCode, relation.getOrderId());
            }

            if (CollectionUtils.isEmpty(dds)) {
                return true;
            }

            StringBuilder sb = new StringBuilder("订单编号: ").append(relation.getOrderId())
                    .append("与").append(StringUtils.join(dds)).append("重单，请人工进行处理！");
            OcBOrder newOrder = new OcBOrder();
            newOrder.setId(relation.getOrderId());
            newOrder.setSysremark(sb.substring(0, sb.length() > 300 ? 300 : sb.length()));
            newOrder.setAuditTime(new Date(System.currentTimeMillis()));
            newOrder.setAutoAuditStatus(AutoAuditStatus.Audit_FAIL.toInteger());
            omsOrderService.updateOrderInfo(newOrder);

        } catch (Exception ex) {
            String message = "订单查询重单服务异常,审核失败!";
            log.error(LogUtil.format("重单校验: {}", relation.getOrderId()), Throwables.getStackTraceAsString(ex));
            updateOrderInfo(relation.getOmsMethod(), relation.getOrderInfo(), message, OmsAuditFailedReason.ERROR_24);
        }
        return false;
    }

    /**
     * 判断发货仓库和物流是否在系统中存在
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkWarehouseAndLogistics(OcBOrderRelation orderInfo) {

        try {
            CpCPhyWarehouse ocCpCPhyWarehouse = omsOrderDistributeWarehouseService.queryByWarehouseId(orderInfo.getOrderInfo().getCpCPhyWarehouseId());
            if (ocCpCPhyWarehouse == null) {
                return false;
            } else {
                return true;
            }
        } catch (Exception ex) {
            String message = "订单OrderId" + orderInfo.getOrderId() + "订单查询仓库服务规则接口异常,审核失败!" + ex.getMessage();
            log.error(LogUtil.format("订单查询仓库服务规则接口异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_24);
            return false;
        }
    }

    /**
     * 检查订单的“双11预售状态”为预售未付尾款（20）
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkPresaleStatus(OcBOrderRelation orderInfo) {

        try {
            if (orderInfo.getOrderInfo().getDouble11PresaleStatus() != null
                    && orderInfo.getOrderInfo().getDouble11PresaleStatus() == OcOrderDoublellPresaleStatus.NOT_PRESALL.toInteger()) {
                return false;
            }
            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("检查订单的“双11预售状态”为预售未付尾款（20）方法异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 检查若“订单来源”为手工新增，且“付款方式”为货到付款，则判断代收货款必须大于0
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkOrderSource(OcBOrderRelation orderInfo) {
        try {
            if (OmsStOrderType.MANUAL_ADD.getKey().equals(orderInfo.getOrderInfo().getOrderSource())
                    && orderInfo.getOrderInfo().getPayType() == OmsPayType.CASH_ON_DELIVERY.toInteger()
                    && orderInfo.getOrderInfo().getCodAmt().compareTo(BigDecimal.ZERO) <= 0) {
                return false;
            } else {
                return true;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("订单来源”为手工新增，且“付款方式”为货到付款，则判断代收货款必须方法异常: {}",
                    orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * “付款方式”为在线支付，且“服务费”、“代收货款”（表字段叫到付代收金额）都为0
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkOrderServiceAmtAndCodeAmt(OcBOrderRelation orderInfo) {

        try {
            BigDecimal serviceAmt = (orderInfo.getOrderInfo().getServiceAmt() == null ? BigDecimal.ZERO : orderInfo.getOrderInfo().getServiceAmt());
            BigDecimal codeAmt = (orderInfo.getOrderInfo().getCodAmt() == null ? BigDecimal.ZERO : orderInfo.getOrderInfo().getCodAmt());
            //在线支付
            if (orderInfo.getOrderInfo().getPayType() == OmsPayType.ON_LINE_PAY.toInteger()
                    //“服务费”、“代收货款”（表字段叫到付代收金额）都为0
                    && (serviceAmt.compareTo(BigDecimal.ZERO) != 0 || codeAmt.compareTo(BigDecimal.ZERO) != 0)) {
                return false;
            } else {
                return true;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("付款方式”为在线支付，且“服务费”、“代收货款”（表字段叫到付代收金额）方法异常: {}",
                    orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }


    /**
     * 判断【淘宝待修改地址表】同平台单号的“是否更新”状态为未更新
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean updateTaobaoSourceStatus(OcBOrderRelation orderInfo) {

        try {
            String sourcecode = orderInfo.getOrderInfo().getSourceCode();
            if (StringUtils.isEmpty(sourcecode)) {
                return true;
            } else {
                IpBTaobaoModifyAddr ipBTaobaoModifyAddr = ipTaobaoOrderService.selectSourcecodeByIsUpdate(orderInfo.getOrderInfo().getSourceCode());
                if (ipBTaobaoModifyAddr == null) {
                    return true;
                } else {
                    if (ipBTaobaoModifyAddr.getIsUpdate() == null
                            || IpBTaobaoModifyAddrStatus.ISUPDATE_STATUS_NOT_SYN.getKey() == ipBTaobaoModifyAddr.getIsUpdate()
                            || IpBTaobaoModifyAddrStatus.ISUPDATE_STATUS_WAIT.getKey() == ipBTaobaoModifyAddr.getIsUpdate()) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("订单查询淘宝待修改地址表异常异常: {}",
                    orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 判断订单明细中成交价金额是否正确
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkItemPriceAmount(OcBOrderRelation orderInfo) {

        /**
         * 判断订单明细中金额是否正确，若明细中（平台售价*数量）-优惠金额- 平摊金额+调整金额-（成交价*数量）
         * 的值=单行实际成交金额，若不等于，则提示“XXX订单的
         * 明细商品成交价不正确，不允许审核！” （XXX为订单编号）
         */
        try {
            // 如果是对等换货不校验 改成校验主表成交金额与明细成交金额是否一致
            if (OcBOrderConst.IS_STATUS_IY.equals(orderInfo.getOrderInfo().getIsEqualExchange())) {
                return checkItemRealAmtForEqualExchange(orderInfo);
            } else {
                List<OcBOrderItem> orderItemList = orderInfo.getNoRefundOrderItems();
                //明细中（平台售价*数量）-优惠金额 - 平摊金额 + 调整金额
                for (OcBOrderItem ocBOrderItem : orderItemList) {
                    //数量
                    BigDecimal qty = ComputeEqualExchangeQtyUtil.computeQty(ocBOrderItem);
                    //优惠金额
                    BigDecimal amtDiscount = (ocBOrderItem.getAmtDiscount() == null ? BigDecimal.ZERO : ocBOrderItem.getAmtDiscount());
                    //调整金额
                    BigDecimal adjustAmt = (ocBOrderItem.getAdjustAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getAdjustAmt());
                    //成交价格
                    BigDecimal price = (ocBOrderItem.getPrice() == null ? BigDecimal.ZERO : ocBOrderItem.getPrice());
                    //单行实际成交金额
                    BigDecimal realAmt = ocBOrderItem.getRealAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getRealAmt();
                    //平摊金额
                    BigDecimal orderSplitAmt = (ocBOrderItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getOrderSplitAmt());
                    //(平台售价*数量) - 优惠金额 - 平摊金额 + 调整金额
                    BigDecimal out5 = price.multiply(qty).subtract(amtDiscount).subtract(orderSplitAmt).add(adjustAmt);
                    BigDecimal dValue = realAmt.subtract(out5).abs();
                    if (dValue.compareTo(BigDecimal.valueOf(0.1)) > 0) {
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("订单统计明细中成交价金额异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 判断组合商品金额是否一致
     *
     * @param orderInfo
     * @return
     * @30136【订单中心】订单审核针对福袋和组合增加校验
     */
    public boolean checkItemPriceAmountGroup(OcBOrderRelation orderInfo) {

        /**
         * 零售发货单审核时，判断group_goods_mark这个字段值相同的明细行中，
         * 商品类型为1，2的明细行的成交金额是否等于商品类型4的明细行的成交金额；
         * 若不相等则提示组合福袋商品成交金额计算错误，不允许审核！
         * 若group_goods_mark无值，则不需判断
         */
        try {
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
            List<OcBOrderItem> groupItems = orderItemList.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(groupItems)) {
                return true;
            }

            Map<String, List<OcBOrderItem>> orderItemMaps = orderItemList.stream().filter(e -> StringUtils.isNotBlank(e.getGroupGoodsMark()))
                    .collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));

            if (MapUtils.isEmpty(orderItemMaps)) {
                return true;
            }

            for (OcBOrderItem item : groupItems) {
                String groupGoodsMark = item.getGroupGoodsMark();
                List<OcBOrderItem> ocBOrderItems = orderItemMaps.get(groupGoodsMark);
                List<OcBOrderItem> combineAndGiftList = ocBOrderItems.stream()
                        .filter(e -> Objects.nonNull(e.getProType())
                                && (e.getProType().intValue() == SkuType.GIFT_PRODUCT
                                || e.getProType().intValue() == SkuType.COMBINE_PRODUCT)).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(combineAndGiftList)) {
                    log.error(LogUtil.format("组合商品数据为空", orderInfo.getOrderId()));
                    throw new NDSException("OrderId=" + orderInfo.getOrderId() + ",组合商品数据异常");
                }

                BigDecimal realAmtCombineAndGift = BigDecimal.ZERO;
                for (OcBOrderItem ocBOrderItem : combineAndGiftList) {
                    BigDecimal bigDecimal = ocBOrderItem.getRealAmt() == null ?
                            BigDecimal.ZERO : ocBOrderItem.getRealAmt();
                    realAmtCombineAndGift = realAmtCombineAndGift.add(bigDecimal);
                }

                BigDecimal realAmtNoSplitCombineItem = item.getRealAmt() == null ?
                        BigDecimal.ZERO : item.getRealAmt();

                BigDecimal abs = realAmtCombineAndGift.subtract(realAmtNoSplitCombineItem).abs();
                if (abs.compareTo(BigDecimal.valueOf(0.01)) > 0) {
                    return false;
                }
            }

            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("组合福袋商品成交金额计算异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }

    }

    /**
     * 订单总金额校验
     *
     * @param orderInfo
     * @return
     */
    public boolean checkOrderTotalAmount(OcBOrderRelation orderInfo) {
        try {
            OcBOrder ocBOrder = orderInfo.getOrderInfo();
            if (ocBOrder.getOrderAmt() == null || ocBOrder.getReceivedAmt() == null) {
                return false;
            }

            if (ocBOrder.getOrderAmt().compareTo(BigDecimal.ZERO) < 0
                    || ocBOrder.getReceivedAmt().compareTo(BigDecimal.ZERO) < 0) {
                return false;
            }
            //未退款明细(用proType in(0,4))
            List<OcBOrderItem> orderItemList = orderInfo.getNoRefundOrderItems();
            for (OcBOrderItem orderItem : orderItemList) {
                if (orderItem.getRealAmt() == null || orderItem.getRealAmt().compareTo(BigDecimal.ZERO) < 0) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error(LogUtil.format("判断主表“商品金额”是否和明细金额: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(e));
            return false;
        }
    }


    /**
     * 判断主表“商品金额”是否和明细金额[成交价]合计一致
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkItemAmount(OcBOrderRelation orderInfo) {
        try {
            BigDecimal priceCount = BigDecimal.ZERO;
            //存在审核时申请退款查询的时候主表数据是原来的(怀疑是事务提交慢的问题导致)
            OcBOrder ocBOrder = orderInfo.getOrderInfo();
            // 如果是对等换货不校验 商品金额
            if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsEqualExchange())) {
                return true;
            }
            //未退款明细(用proType in(0,4))
            List<OcBOrderItem> orderItemList = orderInfo.getNoRefundOrderItems();
            for (OcBOrderItem orderItem : orderItemList) {
                //平台售价*数量
                BigDecimal price = (orderItem.getPrice() == null ? BigDecimal.ZERO : orderItem.getPrice());
                BigDecimal qty = ComputeEqualExchangeQtyUtil.computeQty(orderItem);
                priceCount = priceCount.add(price.multiply(qty));
            }
            BigDecimal productAmt = (ocBOrder.getProductAmt() == null) ? BigDecimal.ZERO : ocBOrder.getProductAmt();
            BigDecimal dValue = productAmt.subtract(priceCount).abs();
            if (dValue.compareTo(BigDecimal.valueOf(0.1)) > 0) {
                return false;
            } else {
                return true;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("判断主表“商品金额”是否和明细金额: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 校验明细成交金额(适用于对等换货逻辑)
     *
     * @param orderInfo
     * @return
     */
    public boolean checkItemRealAmtForEqualExchange(OcBOrderRelation orderInfo) {
        try {
            // 订单总金额 - 服务费用 - 配送费用 = 成交金额汇总
            OcBOrder ocBOrder = orderInfo.getOrderInfo();
            BigDecimal orderAmt = Optional.ofNullable(ocBOrder.getOrderAmt()).orElse(BigDecimal.ZERO);
            BigDecimal serviceAmt = Optional.ofNullable(ocBOrder.getServiceAmt()).orElse(BigDecimal.ZERO);
            BigDecimal shipAmt = Optional.ofNullable(ocBOrder.getShipAmt()).orElse(BigDecimal.ZERO);

            //未退款明细(用proType in(0,4))
            List<OcBOrderItem> orderItemList = orderInfo.getNoRefundOrderItems();
            BigDecimal realAmtTotal = BigDecimal.ZERO;
            for (OcBOrderItem orderItem : orderItemList) {
                // 明细成交金额汇总
                realAmtTotal = realAmtTotal.add(Optional.ofNullable(orderItem.getRealAmt()).orElse(BigDecimal.ZERO));
            }
            BigDecimal orderRealAmt = orderAmt.subtract(serviceAmt).subtract(shipAmt);
            BigDecimal dValue = orderRealAmt.subtract(realAmtTotal).abs();
            if (dValue.compareTo(BigDecimal.valueOf(0.1)) > 0) {
                return false;
            } else {
                return true;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("判断主表“订单总金额-服务费用-配送费用”是否和明细的成交金额汇总一致: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 判断明细中条码在【商品档案】中“物料类型”为鞋且该类型条码数量合计是否大于“发货仓库”+“物流公司”在【物流分配比例设置】中的限制数量
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkOrderItemSku(OcBOrderRelation orderInfo) {

        try {
            OcBOrder ocBorderDto = orderInfo.getOrderInfo();
            //统计订单中“物料类型”为鞋且该类型条码数量
            BigDecimal countQty = BigDecimal.ZERO;
            //计算发货仓库 + 物流公司在【物流分配比例设置】中的限制数量
            Long itemTotal = omsOrderPriceSchemeService.queryPriceList(ocBorderDto.getCpCPhyWarehouseId(), ocBorderDto.getCpCLogisticsId());
            //当维护了限制数量时
            if (itemTotal != null && itemTotal != 0L) {
                BigDecimal itemQty = new BigDecimal(itemTotal);
                List<OcBOrderItem> orderItemList = omsOrderItemService.selectUnSuccessRefund(ocBorderDto.getId());
                if (CollectionUtils.isEmpty(orderItemList)) {
                    return false;
                }
                for (OcBOrderItem orderItem : orderItemList) {
                    ProductSku productSku = psRpcService.selectProductById(String.valueOf(orderItem.getPsCSkuId()));
                    if (productSku == null) {
                        log.error(LogUtil.format("明细sku在商品档案中没有记录: {}", orderInfo.getOrderId()), orderItem.getPsCSkuEcode());
                        return false;
                    }
                    if (MaterielType.ADULT.getEcode().equals(productSku.getMaterialType()) || MaterielType.CHILD.getEcode().equals(productSku.getMaterialType())) {
                        countQty = countQty.add(orderItem.getQty());
                    }
                }
                if (countQty.compareTo(itemQty) > 0) {
                    String temp = countQty.stripTrailingZeros().toPlainString();
                    String message = "订单OrderId" + ocBorderDto.getId() + "的订单中含有鞋类商品" + temp + "双，且大于限制数量" + itemTotal + "双，不允许审核！";
                    log.error(LogUtil.format("的订单中含有鞋类商品.双且大于限制数量: {}", orderInfo.getOrderId()));
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            String message = "订单OrderId" + orderInfo.getOrderId() + "的订单审核物料类型为鞋类服务调用异常" + e.getMessage();
            log.error(LogUtil.format("订单审核物料类型为鞋类服务调用异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(e));
            return false;
        }
    }

    /**
     * 调用价格方案服务
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkOrderProPrice(OcBOrderRelation orderInfo) {

        try {
            if (!this.omsSystemConfig.isAuditCheckOrderPriceStrategyEnabled()) {
                return true;
            }
            int unPricrItemCount = 0;
            //查询有效价格方案
            //List<Long> stCPriceList = stRpcService.queryPriceList(orderInfo.getOrderInfo().getCpCShopId());
            StCPriceResult stCPriceResult = stRpcService.queryPricesByShopId(orderInfo.getOrderInfo().getCpCShopId());

            //根据明细去比对价格方案策略
            List<OcBOrderItem> orderItemList = omsOrderService.getUnSuccessRefundAuditExcludeNoSplitCombine(orderInfo.getOrderItemList());
            StringBuilder sbErrorMsg = new StringBuilder("订单OrderId" + orderInfo.getOrderId());
            StringBuilder sbOrderErrorMsg = new StringBuilder("订单OrderId" + orderInfo.getOrderId());

            //根据商品价格策略排除商品明细
            omsOrderPriceSchemeService.priceExcludeOrderItem(stCPriceResult, orderItemList);

            for (OcBOrderItem orderItem : orderItemList) {
                //商品价格
                BigDecimal price = orderItem.getRealAmt() == null ? BigDecimal.ZERO : orderItem.getRealAmt();
                //String result = omsOrderPriceSchemeService.checkPriceScheme(stCPriceList, orderInfo.getOrderInfo(),
                //orderItem.getPsCSkuEcode(), orderItem.getPsCProId(), price, orderItem.getPsCProEcode());
                String result = omsOrderPriceSchemeService.checkOrderItemPrice(orderInfo.getOrderInfo().getId(),
                        orderItem.getId(), orderItem.getPsCSkuEcode(),
                        orderItem.getPsCProId(), price, orderItem.getPsCProEcode(),
                        stCPriceResult);
                //明细条码不满足最低成交价+1
                if (!StringUtils.equalsIgnoreCase("success", result)) {
                    unPricrItemCount++;
                    sbErrorMsg.append(result);
                    sbErrorMsg.append("\r\n");
                    sbOrderErrorMsg.append(result);
                }
            }
            if (unPricrItemCount > 0) {
                updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), sbOrderErrorMsg.toString(), OmsAuditFailedReason.ERROR_33);
                orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_33);
                this.sendAuditPriceErrorMsg(orderInfo, sbErrorMsg.toString());
                return false;
            } else {
                return true;
            }
        } catch (Exception ex) {
            String message = "订单执行审单流程异常,审核失败!" + ex.getMessage();
            log.error(LogUtil.format("调用价格方案服务: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_33);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_33);
            return false;
        }
    }

    private void sendAuditPriceErrorMsg(OcBOrderRelation orderInfo, String errorMsg) {
        try {
            if (!orderInfo.getOrderInfo().getPlatform().equals(PlatFormEnum.TAOBAO.getCode())) {
                return;
            }

            String isCanSendErrorMsgParam = ApplicationContextHandle.getBean(PropertiesConf.class).getProperty("r3.oc.oms.audit.send.error.msg", "true");
            if (!StringUtils.equalsIgnoreCase(isCanSendErrorMsgParam, "true")) {
                return;
            }

            int auditTaskNode = 14;
            int saleType = 3;
            long shopId = orderInfo.getOrderInfo().getCpCShopId();
            MailInfoResult mailInfoResult = vipComMailPlanService.selectVipComMainPlan(shopId,
                    auditTaskNode);
            if (mailInfoResult != null) {
                List<StCVipcomMailDO> stCVipcomMailDOS = mailInfoResult.getStCVipcomMailDOList();
                if (!org.springframework.util.CollectionUtils.isEmpty(stCVipcomMailDOS)) {
                    //发送钉钉
                    for (StCVipcomMailDO stCVipcomMailDO : stCVipcomMailDOS) {
                        if (!ObjectUtils.isEmpty(stCVipcomMailDO.getIsDingTalk())
                                && stCVipcomMailDO.getSaleType() == saleType
                                && stCVipcomMailDO.getIsDingTalk() == 1
                                && !org.springframework.util.StringUtils.isEmpty(stCVipcomMailDO.getDingTalkNo())) {
                            StringBuffer mailContent = new StringBuffer();
                            mailContent.append(stCVipcomMailDO.getMailTitle() == null ? ""
                                    : stCVipcomMailDO.getMailTitle() + "\n\n" + stCVipcomMailDO.getMailContent() == null ? ""
                                    : stCVipcomMailDO.getMailContent());
                            mailContent.append(errorMsg);
                            DingTalkUtil service = ApplicationContextHandle.getBean(DingTalkUtil.class);
                            service.sendRobotMsg(mailContent.toString(), stCVipcomMailDO.getDingTalkNo());
                            return;
                        }
                    }
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("OrderAutoAuditService.SendError.Msg.Not.Found.Mail.Config.ShopId={},OrderId={}"
                            , orderInfo.getOrderId()), shopId, orderInfo.getOrderId());
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("调用价格方案服务: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
        }
    }

    /**
     * 最后更新状态为已审核 status=3
     *
     * @param orderInfo   订单对象
     * @param operateUser 用户对象
     * @return boolean
     */
    public boolean updateOrderStatus(OcBOrderRelation orderInfo, User operateUser) {

        try {
            omsOrderLogService.addUserOrderLog(orderInfo.getOrderId(), orderInfo.getOrderInfo().getBillNo(), OrderLogTypeEnum.ORDER_EXAMINE.getKey(),
                    "订单自动审核成功!", "", "", operateUser);
            boolean flag = omsOrderService.updateAuditSuccess(orderInfo.getOrderId(), operateUser.getName());
            if (flag) {
                //审核成功传WMS
                log.debug(LogUtil.format("审核成功传生成出库通知单WMS", orderInfo.getOrderId()));
                this.wmsTaskService.saveOrUpdateOcBToWmsTask(orderInfo.getOrderInfo(), operateUser);

                //加入开关判断是否审核通过后直接上传wms
                if (this.omsSystemConfig.isAuditAutoSendOrderToWmsEnabled()) {
                    long startTime = System.currentTimeMillis();
                    //调用订单传wms
                    sgOutStockNoticeService.addOutStockNotice(orderInfo.getOrderId(), operateUser);
                }
            }
            return flag;
        } catch (Exception ex) {
            String message = "订单OrderId" + orderInfo.getOrderInfo().getId() + "的订单更新订单状态异常" + ex.getMessage();
            log.error(LogUtil.format("的订单更新订单状态异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_31);
            return false;
        }
    }

    /**
     * 调用线上代销资金占用变动服务
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkStrategyPriceComputeOrder(OcBOrderRelation orderInfo) {

        try {
            List<OcBOrderParam> orderInfos = new ArrayList<>();
            OcBOrderParam ocBOrderParam = new OcBOrderParam();
            ocBOrderParam.setOcBOrder(orderInfo.getOrderInfo());
            ocBOrderParam.setOrderItemList(orderInfo.getOrderItemList());
            orderInfos.add(ocBOrderParam);
            return false;
        } catch (Exception ex) {
            String message = ex.getMessage();
            log.error(LogUtil.format("调用线上代销资金占用变动服务: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_36);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_36);
            return false;
        }
    }

    /**
     * description：订单为JITX订单，且存在可合并的订单
     *
     * <AUTHOR>
     * @date 2021/6/2
     */
    public boolean checkExistCanMergeJitxOrder(OcBOrderRelation orderInfo) {
        OcBOrder order = orderInfo.getOrderInfo();
        if (StringUtils.isEmpty(order.getMergedCode())) {
            return true;
        }
        //店铺策略是否开启检查可合并订单属性 未开启跳过检查
        StCAutoCheck stCAutoCheck = orderInfo.getStCAutoCheck();
        if (!YesNoEnum.Y.getKey().equals(stCAutoCheck.getIsMergeOrder())) {
            return true;
        }
        return checkExistOtherSameOrder(order).isOK();
    }

    public ValueHolderV14 checkExistOtherSameOrder(OcBOrder order) {
        String returnMsg = "零售发货单/JITX订单中间表存在可合并的订单还未参与合并，仍要继续此操作吗？";
        //判断若为JITX订单，中间表及发货单表中是否还存在其他可合并订单 判断依据
        if (PlatFormEnum.VIP_JITX.getCode().equals(order.getPlatform())) {
            if (StringUtils.isEmpty(order.getMergedCode())) {
                return ValueHolderV14Utils.getSuccessValueHolder("");
            }
            int qty = order.getQtyAll().intValue();
            if (qty >= businessSystemParamService.getJitxMergedOrderLimit()) {
                return ValueHolderV14Utils.getSuccessValueHolder("");
            }
            //查询发货单
            List<Long> ids = ES4Order.findIdsByMergedCode(order);
            if (CollectionUtils.isNotEmpty(ids)) {
                ids.remove(order.getId());
                if (CollectionUtils.isNotEmpty(ids)) {
                    List<OcBOrder> orderList = orderMapper.selectByIdsList(ids);
                    Optional<OcBOrder> optional = orderList.stream()
                            .filter(x -> OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(x.getOrderStatus()) || OmsOrderStatus.UNCONFIRMED.toInteger().equals(x.getOrderStatus()))
                            .filter(x -> x.getQtyAll() != null && x.getQtyAll().intValue() < qty).findAny();
                    if (optional.isPresent()) {
                        return ValueHolderV14Utils.getFailValueHolder(returnMsg);
                    }
                } else {
                    StCVipcomJitxWarehouse stCVipcomJitxWarehouse = vipcomJitxWarehouseService.queryJitxCapacity(order.getCpCShopId(), order.getCpCPhyWarehouseId(), null);
                    String jitWarehouseEcode = stCVipcomJitxWarehouse.getVipcomWarehouseEcode();
                    if (!YesNoEnum.Y.getVal().equals(order.getIsStoreDelivery())) {
                        jitWarehouseEcode = stCVipcomJitxWarehouse.getVipcomUnshopWarehouseEcode();
                    }
                    //查询JITX订单表
                    List<String> orderSnList = ES4IpJitXOrder.findExistOtherCanMergedOrder(order.getMergedCode(), order.getCpCShopId(), jitWarehouseEcode);
                    if (CollectionUtils.isNotEmpty(orderSnList)) {
                        orderSnList.remove(order.getSourceCode());
                        if (CollectionUtils.isNotEmpty(orderSnList)) {
                            return ValueHolderV14Utils.getFailValueHolder(returnMsg);
                        }
                    }
                }
            }
        }
        return ValueHolderV14Utils.getSuccessValueHolder("");
    }

    /**
     * 判断订单店铺是否启用全赠品订单自动审核
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkOrderGift(OcBOrderRelation orderInfo) {
        try {
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            //未退款明细(protype(0,4))
            List<OcBOrderItem> unSuccessRefundList = orderInfo.getNoRefundOrderItems();
            //未退款明细数量
            int size = unSuccessRefundList.size();
            //赠品数量
            int fullGiftSize = 0;
            for (OcBOrderItem item : unSuccessRefundList) {
                if (item.getIsGift() != null
                        && item.getIsGift().intValue() == OmsGiftJudge.YES.toInteger()) {
                    fullGiftSize++;
                }
            }

            if (fullGiftSize < size) {
                return true;
            } else if (fullGiftSize == size) {
                if (OmsSwitch.ENABLED.toString().equals(ocStCAutoCheck.getIsFullGiftOrder())) {
                    return true;
                }
            }
            return false;
        } catch (Exception ex) {
            log.error(LogUtil.format("判断订单店铺是否启用全赠品订单自动审核: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 判断订单类型是否可以自动审核
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkOrderType(OcBOrderRelation orderInfo) {
        try {
            //当前订单类型
            Integer orderType = orderInfo.getOrderInfo().getOrderType();
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            //自动审核策略中勾选的订单类型

            //非空判断
            if (StringUtils.isBlank(ocStCAutoCheck.getOrderType())) {
                return true;
            }
            String orderTypeStr = ocStCAutoCheck.getOrderType();

            String[] orderTypeArray = orderTypeStr.split(",");

            boolean isBooking = false;
            boolean isNormal = false;
            boolean isExchange = false;

            for (String orderTypes : orderTypeArray) {
                /**
                 * 勾选正常订单
                 */
                if (orderTypes.equals(OmsStOrderType.NORMAL.getVal().toString())) {
                    isNormal = true;
                }

                if (orderTypes.equals(OmsStOrderType.BOOKING.getVal().toString())) {
                    isBooking = true;
                }
                /**
                 * 店铺策略排除类型没有勾选换货类型，需要判断是否完成换货入库
                 */
                if (!orderTypes.equals(OmsStOrderType.EXCHANGE.getVal().toString())) {
                    isExchange = true;
                }

            }

            if (isNormal) {
                if (OmsStOrderType.NORMAL.getOmsVal().equals(orderType)) {
                    return false;
                }
            }

            if (isBooking) {
                if (OmsStOrderType.BOOKING.getOmsVal().equals(orderType)) {
                    return false;
                }
            }

            if (isExchange) {
                if (OmsStOrderType.EXCHANGE.getOmsVal().equals(orderType)) {
                    return false;
                }
            }

            return true;

        } catch (Exception ex) {
            log.error(LogUtil.format("判断订单类型是否可以自动审核: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 判断是否启用手工新增订单自动审核,过滤掉是换货单据
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public boolean checkManualOrder(OcBOrderRelation orderInfo) {

        try {
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            if ((OmsStOrderType.MANUAL_ADD.getKey().equalsIgnoreCase(orderInfo.getOrderInfo().getOrderSource()))
                    && (!Objects.equals(orderInfo.getOrderInfo().getOrderType(), OrderTypeEnum.EXCHANGE.getVal()))
                    && IsActiveEnum.N.getKey().equalsIgnoreCase(ocStCAutoCheck.getIsManualOrder())) {
                return false;
            } else {
                return true;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("判断是否启用手工新增订单自动审核异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 判断淘宝预售是否修改地址
     *
     * @param order
     * @return
     */
    public boolean checkOrderAddressAwaitSys(OcBOrder order) {
        String provinceRedisKey = BllRedisKeyResources.getUpdateOrderAddressKey(order.getSourceCode());
        return RedisMasterUtils.getObjRedisTemplate().hasKey(provinceRedisKey);
    }

    /**
     * 判断唯品会JitX是否改仓
     *
     * @return
     */
    public boolean checkVipJitxChangeWarehouse(OcBOrderRelation orderRelation, User user) {
        OcBOrder orderInfo = orderRelation.getOrderInfo();
        if (orderInfo.getPlatform().equals(PlatFormEnum.VIP_JITX.getCode())) {
            List<OcBJitxModifyWarehouseLog> ocBJitxModifyWarehouseLogs = ocBJitxModifyWarehouseLogService.existOrderLog(orderInfo.getId());
            if (CollectionUtils.isNotEmpty(ocBJitxModifyWarehouseLogs)) {
                if (YesNoEnum.Y.getKey().equals(ocBJitxModifyWarehouseLogs.get(0).getForcedWarehouseChange())) {
                    return true;
                }
            }
            if (omsOrderManualAuditService.checkRedisJitxChangeWarehouseFlag(orderInfo)) {
                this.updateOrderInfo(orderRelation.getOmsMethod(), orderRelation.getOrderInfo(), "JITX订单正在平台改仓,不允许审核。", OmsAuditFailedReason.ERROR_29);
                orderRelation.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_29);
                return false;
            }
            if (CollectionUtils.isNotEmpty(ocBJitxModifyWarehouseLogs)) {
                if (VipJitxWorkflowStateEnum.REJECT.getKey().equals(ocBJitxModifyWarehouseLogs.get(0).getWorkflowState()) || VipJitxWorkflowStateEnum.CANCEL.getKey().equals(ocBJitxModifyWarehouseLogs.get(0).getWorkflowState())) {
                    this.updateOrderInfo(orderRelation.getOmsMethod(), orderRelation.getOrderInfo(), "改仓失败，请在JITX订单改仓中间表中重试或者强行改仓", OmsAuditFailedReason.ERROR_29);
                    orderRelation.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_29);
                    return false;
                }
            }
        }
        return true;
    }


    /**
     * 判断唯品会JitX发货重置
     *
     * @return
     */
    public boolean checkVipJitxResetShip(OcBOrderRelation orderRelation, User user) {
        OcBOrder orderInfo = orderRelation.getOrderInfo();
        if (PlatFormEnum.VIP_JITX.getCode().equals(orderInfo.getPlatform())) {
            List<OcBOrderItem> orderItemList = orderRelation.getOrderItemList();

            if (omsOrderManualAuditService.checkRedisJitxReseShipFlag(orderItemList)) {
                log.error("{}.audit.JITX订单平台发货重置中,不允许审核", orderRelation.getOrderId());
                this.updateOrderInfo(orderRelation.getOmsMethod(), orderRelation.getOrderInfo(), "JITX订单正在平台发货重置中,不允许审核。", OmsAuditFailedReason.ERROR_480);
                orderRelation.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_480);
                return false;
            }
//            List<IpBJitxResetShipWorkflow> shipWorkflows = ipBJitxResetShipWorkflowService.existOrderWorkflow(orderInfo.getId());
//            if (CollectionUtils.isNotEmpty(shipWorkflows)) {
//                if (!VipJitxWorkflowStateEnum.PASS.getKey().equals(shipWorkflows.get(0).getWorkflowStatus())) {
//                    log.error("{}.audit.JITX订单平台发货重置未通过,不允许审核", orderRelation.getOrderId());
//                    this.updateOrderInfo(orderRelation.getOmsMethod(), orderRelation.getOrderInfo(), "发货重置未通过，请在JITX订单发货重置工单表中重试", OmsAuditFailedReason.ERROR_480);
//                    orderRelation.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_480);
//                    return false;
//                }
//            }
            //非合包不需要 调用提前平台发货
            if (orderItemList.size() < 2) {
                return true;
            }
            //提前调用平台发货接口
            ValueHolderV14 v14 = orderPlatformDeliveryService.weiPinHuiPlaformSendGoods(orderInfo, SystemUserResource.getRootUser(), true);
            log.info("{},orderPlatformDeliveryService.weiPinHuiPlaformSendGoods 调用唯品会平台发货结果：{}", JSON.toJSONString(v14));
            if (!v14.isOK()) {
                this.updateOrderInfo(orderRelation.getOmsMethod(), orderRelation.getOrderInfo(), "JitX订单合包订单提前调用平台发货失败", OmsAuditFailedReason.ERROR_490);
                orderRelation.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_490);
                return omsSystemConfig.isJitxMergedOrderShipFailAuditResult();
            }
        }
        return true;
    }

    /**
     * <AUTHOR>
     * @Date 11:15 2021/4/19
     * @Description 检验订单折扣是否在范围内
     */
    public boolean checkOrderDiscountScope(OcBOrderRelation orderInfo) {
        try {
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            String effectiveCondition = ocStCAutoCheck.getEffectiveCondition();
            boolean isContainsEffectiveFlag = isContainsEffectiveValue(effectiveCondition,
                    OmsEffectiveConditionEnum.ORDER_DISCOUNT.parseValue());
            if (!isContainsEffectiveFlag) {
                return true;
            }
            BigDecimal OrderDiscount = (orderInfo.getOrderInfo().getOrderDiscount() == null ?
                    BigDecimal.ZERO : orderInfo.getOrderInfo().getOrderDiscount());

            BigDecimal orderDiscountUp = (ocStCAutoCheck.getOrderDiscountUp() == null ?
                    BigDecimal.ZERO : ocStCAutoCheck.getOrderDiscountUp());

            BigDecimal orderDiscountDown = (ocStCAutoCheck.getOrderDiscountDown() == null ?
                    BigDecimal.ZERO : ocStCAutoCheck.getOrderDiscountDown());

            if (orderDiscountUp.compareTo(BigDecimal.ZERO) != 0
                    || orderDiscountDown.compareTo(BigDecimal.ZERO) != 0) {
                if ((OrderDiscount.compareTo(orderDiscountUp) < 0 || OrderDiscount.compareTo(orderDiscountUp) == 0)
                        && (OrderDiscount.compareTo(orderDiscountDown) > 0 || OrderDiscount.compareTo(orderDiscountDown) == 0)) {
                    return false;
                }
            }
            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("检验订单折扣是否在范围内异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * <AUTHOR>
     * @Date 15:52 2021/9/2
     * @Description 检验单条码sku的限制
     */
    public boolean checkSingleSkuNum(OcBOrderRelation orderInfo) {
        try {
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            String effectiveCondition = ocStCAutoCheck.getEffectiveCondition();
            boolean isContainsEffectiveFlag = isContainsEffectiveValue(effectiveCondition,
                    OmsEffectiveConditionEnum.SINGLE_SKU_NUM.parseValue());
            if (!isContainsEffectiveFlag) {
                return true;
            }

            //不包含增品 获取明细包含组合商品虚拟条码
            List<OcBOrderItem> orderItemList = omsOrderSpiltUtill.getOcBOrderItem(orderInfo.getOrderItemList());

            Map<String, BigDecimal> mapItem = orderItemList.stream()
                    .sorted(Comparator.comparing(OcBOrderItem::getPsCSkuEcode))
                    .collect(Collectors.groupingBy(
                            OcBOrderItem::getPsCSkuEcode,
                            Collectors.reducing(
                                    BigDecimal.ZERO,
                                    OcBOrderItem::getQty,
                                    BigDecimal::add)));
            if (MapUtils.isNotEmpty(mapItem)) {
                for (String n : mapItem.keySet()) {
                    Integer num = mapItem.get(n).intValue();
                    if (num > ocStCAutoCheck.getSingleSkuNum()) {
                        return false;
                    }
                }
            } else {
                return false;
            }
            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("检验单条码sku的限制: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * <AUTHOR>
     * @Date 19:11 2021/5/25
     * @Description 是否满足拆单
     */
    public boolean checkWareHouseSplitOrder(OcBOrderRelation orderInfo) {
        if (splitOrderUtils.isOpenWareHouseSplitOrder(orderInfo.getOrderInfo())) {
            return false;
        }
        return true;
    }

    /**
     * <AUTHOR>
     * @Date 13:56 2021/5/26
     * @Description 判断订单主表的【总金额】，大于等于最低成交金额
     */
    //todo 最低折扣需要从商品价格策略里取，策略暂时没有这个明细
    public boolean checkOrderAmount(OcBOrderRelation orderInfo) {
        try {
            //查询有效方案
            List<Long> stCPriceList = stRpcService.queryPriceList(orderInfo.getOrderInfo().getCpCShopId());
            if (CollectionUtils.isEmpty(stCPriceList)) {
                return true;
            }
            BigDecimal orderAmount = (orderInfo.getOrderInfo().getOrderAmt() == null ?
                    BigDecimal.ZERO : orderInfo.getOrderInfo().getOrderAmt());
            //策略的金额
            BigDecimal minAmount = this.queryStCOrderPriceItem(stCPriceList, ORDER_AMOUNT);

            if (minAmount.compareTo(BigDecimal.ZERO) != 0
                    || orderAmount.compareTo(BigDecimal.ZERO) != 0) {
                if (orderAmount.compareTo(minAmount) < 0) {
                    return false;
                }
            }
            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("检查订单金额是否满足最低成交金额异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * <AUTHOR>
     * @Date 14:20 2021/5/26
     * @Description 判断订单主表的【整单折扣】，大于等于最低折扣
     */
    //todo 最低折扣需要从商品价格策略里取，策略暂时没有这个明细
    public boolean checkOrderDiscount(OcBOrderRelation orderInfo) {
        try {
            //查询有效方案
            List<Long> stCPriceList = stRpcService.queryPriceList(orderInfo.getOrderInfo().getCpCShopId());
            if (CollectionUtils.isEmpty(stCPriceList)) {
                return true;
            }
            BigDecimal orderDiscount = (orderInfo.getOrderInfo().getOrderDiscount() == null ?
                    BigDecimal.ZERO : orderInfo.getOrderInfo().getOrderDiscount());
            //策略的金额
            BigDecimal minDiscount = this.queryStCOrderPriceItem(stCPriceList, ORDER_DISCOUNT);
            if (minDiscount.compareTo(BigDecimal.ZERO) != 0
                    || orderDiscount.compareTo(BigDecimal.ZERO) != 0) {
                if (orderDiscount.compareTo(minDiscount) < 0) {
                    return false;
                }
            }
            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("订单中“整单折扣”检查整单折扣是否大于等于最低折扣异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    public BigDecimal queryStCOrderPriceItem(List<Long> stCPriceList, String policyType) {

        //查询店铺价格方案是否在有效期内
        if ((CollectionUtils.isEmpty(stCPriceList))) {
            return BigDecimal.ZERO;
        } else {
            //判断价格策略明细是否存在,存在明细的时候取出第一条
            Long priceId = stCPriceList.get(0);
            StCOrderPriceItemDO stCOrderPriceItemDO = stRpcService.queryStCOrderPriceItemByPriceId(priceId, policyType);
            if (Objects.isNull(stCOrderPriceItemDO)) {
                return BigDecimal.ZERO;
            } else {
                return stCOrderPriceItemDO.getQtyDiscount();
            }
        }
    }

    public boolean checkReturnAndClosed(OcBOrderRelation orderInfo) {
        List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
        for (OcBOrderItem ocBOrderItem : orderItemList) {
            if (OcOrderRefundStatusEnum.SUCCESS.getVal() == ocBOrderItem.getRefundStatus() && StringUtils.isNotEmpty(ocBOrderItem.getPtReturnStatus())) {
                return false;
            }
        }
        return true;
    }

    public boolean checkCustomLabeL(OcBOrderRelation orderInfo) {
        try {
            log.info(" 订单自动审核，自定义标签拦截 {}", JSON.toJSONString(orderInfo));
            StCAutoCheck ocStCAutoCheck = orderInfo.getStCAutoCheck();
            String effectiveCondition = ocStCAutoCheck.getEffectiveCondition();
            boolean isContainsEffectiveFlag = isContainsEffectiveValue(effectiveCondition,
                    OmsEffectiveConditionEnum.CUSTOM_LABEL.parseValue());
            if (!isContainsEffectiveFlag) {
                return true;
            }
            //订单自定义打标
            String customLabelId = orderInfo.getOrderInfo().getStCCustomLabelId().trim();
            //策略自定义打标策略
            String autoCustomLabelId = ocStCAutoCheck.getStCCustomLabelId().trim();
            //勾选有自定义打标订单自动审核按钮 且 订单存在自定义打标
            if (StringUtils.isEmpty(customLabelId) || StringUtils.isEmpty(autoCustomLabelId)) {
                return true;
            }
            List<String> customLabelString = new ArrayList<>();
            //自定义打标
            String[] labelKeyWordValue = autoCustomLabelId.split(",");
            for (int i = 0; i < labelKeyWordValue.length; i++) {
                customLabelString.add(labelKeyWordValue[i]);
            }
            for (String buyerKeyWord : customLabelString) {
                if (customLabelId.contains(buyerKeyWord.trim())) {
                    return false;
                }
            }
        } catch (Exception ex) {
            log.error("{}.audit.message=检查订单买家异常,审核失败={}", orderInfo.getOrderId(), ex.getMessage(), ex);
            return false;
        }
        return true;
    }

    public boolean checkProduct(OcBOrderRelation orderInfo) {

        List<StCAutoCheckExcludeProduct> autoCheckExcludeProducts = orderInfo.getAutoCheckExcludeProducts();
        List<OcBOrderItem> ocBOrderItems = orderInfo.getNoRefundOrderItems();

        if (CollectionUtils.isEmpty(autoCheckExcludeProducts)) {
            return true;
        }

        List<String> platformSkuId = new ArrayList<>();
        List<String> platformProId = new ArrayList<>();
        List<Long> psCSkuId = new ArrayList<>();
        List<Long> psCProId = new ArrayList<>();
        List<Long> prodimItemId = new ArrayList<>();

        for (StCAutoCheckExcludeProduct autoCheckExcludeProduct : autoCheckExcludeProducts) {
            if (5 == autoCheckExcludeProduct.getProLevel()) {

                platformSkuId.add(autoCheckExcludeProduct.getPlatformSkuId());

            } else if (4 == autoCheckExcludeProduct.getProLevel()) {

                platformProId.add(autoCheckExcludeProduct.getPlatformNumiid());

            } else if (3 == autoCheckExcludeProduct.getProLevel()) {

                psCSkuId.add(autoCheckExcludeProduct.getPsCSkuId());

            } else if (2 == autoCheckExcludeProduct.getProLevel()) {

                psCProId.add(autoCheckExcludeProduct.getPsCProId());

            } else if (1 == autoCheckExcludeProduct.getProLevel()) {

                prodimItemId.add(autoCheckExcludeProduct.getPsCProdimItemId());
            }
        }

        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {

            if (CollectionUtils.isNotEmpty(platformSkuId)
                    && platformSkuId.contains(ocBOrderItem.getSkuNumiid())) {
                return false;
            }

            if (CollectionUtils.isNotEmpty(platformProId)
                    && platformProId.contains(ocBOrderItem.getNumIid())) {
                return false;
            }

            if (CollectionUtils.isNotEmpty(psCSkuId)
                    && psCSkuId.contains(ocBOrderItem.getPsCSkuId())) {
                return false;
            }

            if (CollectionUtils.isNotEmpty(psCProId)
                    && psCProId.contains(ocBOrderItem.getPsCProId())) {
                return false;
            }

            if (CollectionUtils.isNotEmpty(prodimItemId)
                    && prodimItemId.contains(ocBOrderItem.getMDim4Id())) {
                return false;
            }
        }

        return true;
    }

    public boolean checkWarehouse(OcBOrderRelation orderInfo) {
        OcBOrder orderInfo1 = orderInfo.getOrderInfo();

        StCAutoCheck autoCheck = orderInfo.getStCAutoCheck();
        String phyWarehouseIds = autoCheck.getCpCPhyWarehouseIds();

        if (StringUtils.isEmpty(phyWarehouseIds)) {
            return true;
        }

        String[] split = phyWarehouseIds.split(",");
        List<String> phyWarehouseIdList = Arrays.asList(split);

        if (phyWarehouseIdList.contains(String.valueOf(orderInfo1.getCpCPhyWarehouseId()))) {
            return false;
        }

        return true;
    }

    public boolean checkBillType(OcBOrderRelation orderInfo) {

        OcBOrder orderInfo1 = orderInfo.getOrderInfo();

        StCAutoCheck autoCheck = orderInfo.getStCAutoCheck();
        String businessTypeIds = autoCheck.getStCBusinessTypeIds();

        if (StringUtils.isEmpty(businessTypeIds)) {
            return true;
        }

        String[] split = businessTypeIds.split(",");
        List<String> businessTypeIdList = Arrays.asList(split);

        if (businessTypeIdList.contains(String.valueOf(orderInfo1.getBusinessTypeId()))) {
            return false;
        }

        return true;
    }

    public boolean checkShop(OcBOrderRelation orderInfo) {

        Long shopId = orderInfo.getOrderInfo().getCpCShopId();
        StCShopStrategyDO stCShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(shopId);
        String freezeShopCanAudit = stCShopStrategyDO.getFreezeShopCanAudit();

        if (StringUtils.isNotEmpty(freezeShopCanAudit) && "Y".equalsIgnoreCase(freezeShopCanAudit)) {
            return false;
        }

        return true;
    }

    /**
     * audit check factory
     * @param relation
     * @return
     */
    public String checkFactory(OcBOrderRelation relation) {
        OcBOrder ocBOrder = relation.getOrderInfo();
        try {
            Long warehouseId = ocBOrder.getCpCPhyWarehouseId();
            List<Long> storeIds = cpRpcService.queryStoreList(warehouseId);
            if (CollectionUtils.isEmpty(storeIds)) {
                return null;
            }

            CpStore cpStore = cpRpcService.selectCpCStoreById(storeIds.get(0));
            if (Objects.isNull(cpStore)) {
                return null;
            }

            //通用订单
            IpBStandplatOrder ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(ocBOrder.getTid());
            if (Objects.isNull(ipBStandplatOrder)) {
                return null;
            }

            List<IpBStandplatOrderItemEx> orderItemExes = ipBStandplatOrderItemMapper.selectOrderItemList(ipBStandplatOrder.getId());
            if (CollectionUtils.isEmpty(orderItemExes)) {
                return null;
            }

            //逻辑仓档案工厂
            String werks = cpStore.getWerks();

            //工厂
            String factory = orderItemExes.get(0).getFactory();

            if (!StringUtils.equals(werks, factory)) {
                return factory;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("工厂校验: {}", relation.getOrderId()), Throwables.getStackTraceAsString(ex));
            return null;
        }
        return "";
    }

    /**
     * 检查效期范围
     *
     * @param orderInfo
     * @return
     */
    public List<String> checkExpiryDate(OcBOrderRelation orderInfo) {
        try {
            //获取订单明细中商品效期范围为空的数据
            List<OcBOrderItem> noRangeItems =
                    orderInfo.getOrderItemList().stream().filter(p -> StringUtils.isBlank(p.getExpiryDateRange())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(noRangeItems)) {
                return Lists.newArrayList();
            }

            //强制审核不校验
            if (Boolean.TRUE.equals(orderInfo.getMandatoryAudit())) {
                return Lists.newArrayList();
            }

            //查询商品是否开启批次管理
            List<String> noRangeSkuCodes = Lists.newArrayList();
            List<String> skuCodes = noRangeItems.stream().map(OcBOrderItem::getPsCSkuEcode).distinct().collect(Collectors.toList());

            List<PsCPro> psCPros = psRpcService.queryProByEcodes(skuCodes);
            Map<String, PsCPro> codeMap = psCPros.stream().collect(Collectors.toMap(PsCPro::getEcode, x -> x, (a, b) -> a));


            for (String skuCode : skuCodes) {
                PsCPro psCPro = codeMap.get(skuCode);
                if (psCPro == null){
                    noRangeSkuCodes.add(skuCode);
                    continue;
                }

                //是否开启批次管理
                if ("Y".equals(psCPro.getIsSerialNumber())) {
                    continue;
                }

                if ("Y".equals(psCPro.getIsEnableExpiry())) {
                    noRangeSkuCodes.add(skuCode);
                }
            }

            if (CollectionUtils.isNotEmpty(noRangeSkuCodes)) {
                return noRangeSkuCodes;
            }

        } catch (Exception ex) {
            log.warn(LogUtil.format("检查效期范围异常: {}", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            return null;
        }
        return Lists.newArrayList();
    }

}