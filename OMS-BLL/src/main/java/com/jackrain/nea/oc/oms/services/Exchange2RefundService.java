package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 换货转退货
 * @date 2021/10/13 15:28
 */
@Slf4j
@Component
public class Exchange2RefundService {

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderExchangeMapper ocBReturnOrderExchangeMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    /**
     * 换货转退货
     */
    public ValueHolderV14 exchange2Refund(JSONObject obj, User user){
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("换货转退货入参: {}", "换货转退货"), obj.toJSONString());
        }
        ValueHolderV14 vh = new ValueHolderV14();
        // 根据原单id去查询退单 记录不存在，则提示：“当前记录已不存在！
        JSONArray ids = obj.getJSONArray("ids");
        try {
            if (!ids.isEmpty()) {
                Exchange2RefundService exchange2RefundServiceBean = ApplicationContextHandle.getBean(Exchange2RefundService.class);
                exchange2RefundServiceBean.doExchange2Refund(user,ids);
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("换货转退货操作成功");
            } else {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("请选择一条记录", user.getLocale()));
            }
        }catch (Exception e){
            log.error(LogUtil.format("换货转退货失败，失败详情异常: {}"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage(), user.getLocale()));
        }
        return vh;
    }


    @Transactional(rollbackFor = Exception.class)
    public void doExchange2Refund(User user, JSONArray ids){
        List<Long> idList = ids.stream().map(e -> NumberUtils.toLong(e+"")).collect(Collectors.toList());
        List<OcBReturnOrder> ocBReturnOrderList = ocBReturnOrderMapper.selectList(Wrappers.<OcBReturnOrder>lambdaQuery().in(OcBReturnOrder::getId, idList));
        if(ocBReturnOrderList.size() != idList.size()){
            throw new NDSException(Resources.getMessage("选择的退换单号中存在库里不存在的退换货单", user.getLocale()));
        }
        ocBReturnOrderList.stream().forEach(e->{
            ValueHolderV14 is2OperateResult = this.is2Operate(e);
            if(!is2OperateResult.isOK()){
                throw new NDSException(Resources.getMessage(is2OperateResult.getMessage(), user.getLocale()));
            }
        });
        List<Long> refundIdList = ocBReturnOrderList.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectList(Wrappers.<OcBOrder>lambdaQuery().in(OcBOrder::getOrigReturnOrderId, refundIdList));
        if(ocBOrderList.size() != refundIdList.size()){
            throw new NDSException(Resources.getMessage("选中的退换货单中有不存在的零售发货单", user.getLocale()));
        }
        this.validOrderStatus(ocBOrderList,user);
        this.doUpdateData(refundIdList,user,ocBReturnOrderList);

    }

    /**
     * 换转退:前置校验:是否可以继续操作
     *
     * @param originReturnOrder
     * @return
     */
    private ValueHolderV14 is2Operate(OcBReturnOrder originReturnOrder) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS,"校验成功");
        if (Objects.isNull(originReturnOrder)) {
            vh.setMessage("退换单号："+originReturnOrder.getId()+"，当前记录已不存在！");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        if (!OcReturnBillTypeEnum.EXCHANGE.getVal().equals(originReturnOrder.getBillType())) {
            vh.setMessage("退换单号："+originReturnOrder.getId()+"，当前记录单据类型不是退换货单，不允许换转退！");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        if (ReturnStatusEnum.CANCLE.getVal().equals(originReturnOrder.getReturnStatus())) {
            vh.setMessage("退换单号："+originReturnOrder.getId()+"，当前记录已取消，不允许换转退！");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        //如果传wms 中也不能去保存
        if (WmsWithdrawalState.PASS.toInteger().equals(originReturnOrder.getIsTowms())) {
            vh.setMessage("退换单号："+originReturnOrder.getId()+"，传wms中，不允许更新！");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        return vh;
    }

    /**
     * @description: 效验订单状态  只能是已取消、作废的
     * <AUTHOR>
     * @date 2021/10/13 19:37
     * @version 1.0
     */
    private void validOrderStatus(List<OcBOrder> ocBOrderList,User user){
        ocBOrderList.stream().forEach(e->{
            if(!ObjectUtils.equals(e.getOrderStatus(), OmsOrderStatus.CANCELLED.toInteger())
                    &&!ObjectUtils.equals(e.getOrderStatus(), OmsOrderStatus.SYS_VOID.toInteger())){
                throw new NDSException(Resources.getMessage("请手动取消零售发货单("+e.getBillNo()+")再进行操作", user.getLocale()));
            }
        });
    }

    /**
     * @description: 更新退货单单据类型、删除换货单项、添加操作记录
     * <AUTHOR>
     * @date 2021/10/13 20:31
     * @version 1.0
     */
    private void doUpdateData(List<Long> refundIdList,User user,List<OcBReturnOrder> ocBReturnOrderList){
        //修改退换单类型为退货单
        int upateCount = ocBReturnOrderMapper.updateReturnOrderBillType(refundIdList, OcReturnBillTypeEnum.RETURN.getVal());
        if(upateCount != refundIdList.size()){
            throw new NDSException(Resources.getMessage("更新退换货单类型为退货单类型失败", user.getLocale()));
        }
        //删除退换单下的换货单项
        ocBReturnOrderExchangeMapper.delete(Wrappers.<OcBReturnOrderExchange>lambdaQuery().in(OcBReturnOrderExchange::getOcBReturnOrderId,refundIdList));
        //  换货转退货操作日志
        Date now = new Date();
        List<OcBReturnOrderLog> ocBReturnOrderLogs = ocBReturnOrderList.stream().map(e -> {
            OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
            ocBReturnOrderLog.setAdClientId(user.getClientId() + 0L);
            ocBReturnOrderLog.setAdOrgId(user.getOrgId() + 0L);
            ocBReturnOrderLog.setLogType("换货转退货");
            ocBReturnOrderLog.setLogMessage("换货转退货成功");
            ocBReturnOrderLog.setIpAddress(user.getLastloginip());
            ocBReturnOrderLog.setUserName(StringUtils.isNotBlank(user.getEname())?user.getEname():user.getName());
            ocBReturnOrderLog.setOwnerename(StringUtils.isNotBlank(user.getEname())?user.getEname():user.getName());
            ocBReturnOrderLog.setOwnername(user.getName());
            ocBReturnOrderLog.setOwnerid(user.getId()!=null?user.getId().longValue():0L);
            ocBReturnOrderLog.setCreationdate(now);
            ocBReturnOrderLog.setModifieddate(now);
            ocBReturnOrderLog.setIsactive("Y");
            ocBReturnOrderLog.setOcBReturnOrderId(e.getId());
            ocBReturnOrderLog.setId(ModelUtil.getSequence("oc_b_return_order_log"));
            return ocBReturnOrderLog;
        }).collect(Collectors.toList());
        ocBReturnOrderLogMapper.batchInsert(ocBReturnOrderLogs);
    }
}
