package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-07-08
 * create at : 2019-07-08 6:11 PM
 */
@Component
@Slf4j
public class ReturnOrderLogService {

    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    /**
     * 添加退换货订单操作日志：
     * 日志类型：同步AG入仓
     * 日志内容：同步成功“同步AG入仓成功”，同步失败：添加同步失败原因日志
     * 日志参数：空
     * IP地址：当前操作电脑的IP地址
     * 用户名称：当前操作人账号
     *
     * @param returnOrderId 退换货id
     * @param logContent    日志内容
     * @param loginUser     loginUser
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void addRefundOrderLog(Long returnOrderId, String logType, String logContent, User loginUser) {
        try {
            // 校验
            if (ObjectUtil.isNotEmpty(logContent) && logContent.length() > 999) {
                logContent = logContent.substring(0, 999);
            }
            OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
            ocBReturnOrderLog.setLogType(logType);
            ocBReturnOrderLog.setLogMessage(logContent);
            ocBReturnOrderLog.setLogParam("");
            ocBReturnOrderLog.setIpAddress(loginUser.getLastloginip());
            ocBReturnOrderLog.setUserName(loginUser.getEname());
            ocBReturnOrderLog.setOcBReturnOrderId(returnOrderId);
            ocBReturnOrderLog.setId(ModelUtil.getSequence("oc_b_return_order_log"));
            //增加owner和modifier信息
            ocBReturnOrderLog.setOwnerid(Long.valueOf(loginUser.getId()));
            ocBReturnOrderLog.setOwnername(loginUser.getName());
            ocBReturnOrderLog.setOwnerename(loginUser.getEname());
            ocBReturnOrderLog.setModifierid(Long.valueOf(loginUser.getId()));
            ocBReturnOrderLog.setModifiername(loginUser.getName());
            ocBReturnOrderLog.setModifierename(loginUser.getEname());
            ocBReturnOrderLog.setAdOrgId(27L);
            ocBReturnOrderLog.setAdClientId(37L);
            ocBReturnOrderLog.setCreationdate(new Date());
            ocBReturnOrderLog.setIsactive("Y");
            ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);

        } catch (Exception ex) {
            log.error(LogUtil.format("添加退换货订单操作日志失败,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
    }


    public OcBReturnOrderLog buildReturnOrderLog(Long returnOrderId, String logType, String logContent, User loginUser) {
        try {
            OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
            ocBReturnOrderLog.setLogType(logType);
            ocBReturnOrderLog.setLogMessage(logContent);
            ocBReturnOrderLog.setLogParam("");
            ocBReturnOrderLog.setIpAddress(loginUser.getLastloginip());
            ocBReturnOrderLog.setUserName(loginUser.getEname());
            ocBReturnOrderLog.setOcBReturnOrderId(returnOrderId);
            ocBReturnOrderLog.setId(ModelUtil.getSequence("oc_b_return_order_log"));
            //增加owner和modifier信息
            ocBReturnOrderLog.setOwnerid(Long.valueOf(loginUser.getId()));
            ocBReturnOrderLog.setOwnername(loginUser.getName());
            ocBReturnOrderLog.setOwnerename(loginUser.getEname());
            ocBReturnOrderLog.setModifierid(Long.valueOf(loginUser.getId()));
            ocBReturnOrderLog.setModifiername(loginUser.getName());
            ocBReturnOrderLog.setModifierename(loginUser.getEname());
            ocBReturnOrderLog.setAdOrgId(27L);
            ocBReturnOrderLog.setAdClientId(37L);
            ocBReturnOrderLog.setCreationdate(new Date());
            ocBReturnOrderLog.setIsactive(IsActiveEnum.Y.getKey());
            return ocBReturnOrderLog;
        } catch (Exception ex) {
            log.error(LogUtil.format("构建退换货订单操作日志失败,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
        return null;
    }

    public void batchInsertLog(List<OcBReturnOrderLog> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return;
        }
        try {
            ocBReturnOrderLogMapper.batchInsert(logList);
        } catch (Exception ex) {
            log.error(LogUtil.format("添加退换货订单操作日志失败,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
        }
    }
}
