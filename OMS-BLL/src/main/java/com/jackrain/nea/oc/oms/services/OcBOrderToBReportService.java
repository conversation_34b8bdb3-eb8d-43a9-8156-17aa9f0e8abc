package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.oc.oms.mapper.OcBOrderByAdbMapper;
import com.jackrain.nea.oc.oms.model.enums.DmsGiftAttrEnum;
import com.jackrain.nea.oc.oms.model.request.OcBOrderToBReportRequest;
import com.jackrain.nea.oc.oms.model.result.OcBOrderToBReportResult;
import com.jackrain.nea.oc.oms.nums.EnvEnum;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @ClassName OcBOrderToBReportService
 * @Description 新零售tob订单监控
 * <AUTHOR>
 * @Date 2024/11/25 17:07
 * @Version 1.0
 */
@Component
@Slf4j
public class OcBOrderToBReportService {

    @Autowired
    private OcBOrderByAdbMapper ocBOrderByAdbMapper;
    @Autowired
    private CpRpcService cpRpcService;

    private static final Map<String, List<Integer>> statusMap = MapUtil.builder(new HashMap<String, List<Integer>>())
            .put("待寻源", Arrays.asList(50, 2)).put("待审核", Arrays.asList(1)).put("已发货", Arrays.asList(3, 4, 5, 6)).build();

    /**
     * 框架格式返回
     *
     * @param dataList
     * @return
     */
    private static JSONArray getFrameDataFormat(List<JSONObject> dataList) {
        JSONArray array = new JSONArray();
        if (dataList != null && dataList.size() > 0) {
            for (JSONObject emp : dataList) {
                Set<String> keySet = emp.keySet();
                JSONObject json = new JSONObject();
                for (String key : keySet) {
                    JSONObject val = new JSONObject();
                    val.put("val", emp.get(key));
                    json.put(key.toUpperCase(), val);
                }
                array.add(json);
            }
        }
        return array;
    }

    public ValueHolder orderListQuery(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
        JSONObject fixColumn = param.getJSONObject("fixedcolumns");
        Integer range = param.getInteger("range") == null ? 20 : param.getInteger("range");
        Integer startindex = param.getInteger("startindex") == null ? 0 : param.getInteger("startindex");
        log.info("orderListQuery.param:{}", param.toJSONString());
        OcBOrderToBReportRequest request = fixColumn.toJavaObject(OcBOrderToBReportRequest.class);
        request.setRange(range);
        request.setStartindex(startindex);
        if (request != null && request.getOrderStatusList() != null) {
            List<String> orderStatusList = request.getOrderStatusList();
            List<Integer> omsOrderStatusList = new ArrayList<>();
            for (String orderStatus : orderStatusList) {
                if (Objects.equals(orderStatus, "=1") || Objects.equals(orderStatus, "1")) {
                    omsOrderStatusList.addAll(Arrays.asList(50, 2));
                } else if (Objects.equals(orderStatus, "=2") || Objects.equals(orderStatus, "2")) {
                    omsOrderStatusList.addAll(Arrays.asList(1));
                } else if (Objects.equals(orderStatus, "=3") || Objects.equals(orderStatus, "3")) {
                    omsOrderStatusList.addAll(Arrays.asList(3, 4, 5, 6));
                }
            }
            request.setOmsOrderStatusList(omsOrderStatusList);
        }
        if (request != null && StringUtils.isNotEmpty(request.getCreationDate())) {
            // 订单创建时间
            String[] times = request.getCreationDate().split("~");
            request.setStartDate(times[0]);
            request.setEndDate(times[1]);
        }

        if (CollectionUtils.isNotEmpty(request.getLogisticsIdList())) {
            Long logisticsId = request.getLogisticsIdList().get(0);
            request.setLogisticsId(logisticsId);
        }
        // 获取31天前的日期 格式为yyyy-MM-dd HH:mm:ss
        String startDate = DateUtil.format(DateUtil.offsetDay(new Date(), -31), "yyyy-MM-dd HH:mm:ss");


        log.info("orderListQuery.request:{}", JSONUtil.toJsonStr(request));
//        Long maxId = ApplicationContextHandle.getBean(OcBOrderToBReportService.class).getMaxId(startDate);
//        request.setLimitId(maxId);
        int count = ApplicationContextHandle.getBean(OcBOrderToBReportService.class).count(request);
        List<OcBOrderToBReportResult> resultList = ApplicationContextHandle.getBean(OcBOrderToBReportService.class).getOrderList(request);
        // 需要转换resultList
        for (OcBOrderToBReportResult result : resultList) {
            // 判断orderStatus是否在statusMap的value中被包含 如果是的话 取key
            for (Map.Entry<String, List<Integer>> entry : statusMap.entrySet()) {
                if (entry.getValue().contains(result.getOrderStatus())) {
                    result.setOrderStatusName(entry.getKey());
                    break;
                }
            }
            // 根据枚举 查询赠品属性
            if (result.getGiftAttr() != null) {
                result.setGiftAttr(DmsGiftAttrEnum.getDescByVal(Long.valueOf(result.getGiftAttr())));
            }
            if (StringUtils.isNotEmpty(result.getRefundStatus()) && result.getRefundStatus().equals("6")) {
                result.setRefundStatus("已取消");
            } else {
                result.setRefundStatus("未取消");
            }

        }
        JSONArray jsonArray = (JSONArray) JSONArray.toJSON(resultList);
        List<JSONObject> jsonObjectList = JSONObject.parseArray(
                JSONObject.toJSONString(jsonArray, SerializerFeature.WriteMapNullValue), JSONObject.class);
        JSONObject resultData = new JSONObject();
        resultData.put("start", startindex);
        resultData.put("rowCount", range);
        resultData.put("row", getFrameDataFormat(jsonObjectList));
        resultData.put("totalRowCount", count);
        // 打印出来参数
        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", "success");
        vh.put("data", resultData);
        return vh;
    }

    @TargetDataSource(name = "adb")
    public Long getMaxId(String requestDate) {
        Long maxId = ocBOrderByAdbMapper.findMaxIdByOrderDate(requestDate);
        return maxId;
    }

    @TargetDataSource(name = "adb")
    public int count(OcBOrderToBReportRequest request) {
        String db = "test_rpt_basics";
        // 生产查询太慢。限制下id 会快一些
        request.setLimitId(203493966L);
        if (EnvEnum.getEnv().equals(EnvEnum.PROD)) {
            db = "prod_rpt_basics";
            request.setLimitId(247468401L);
        }
        request.setDb(db);
        return ocBOrderByAdbMapper.count(request);
    }

    @TargetDataSource(name = "adb")
    public List<OcBOrderToBReportResult> getOrderList(OcBOrderToBReportRequest request) {
        String db = "test_rpt_basics";
        request.setLimitId(203493966L);
        if (EnvEnum.getEnv().equals(EnvEnum.PROD)) {
            db = "prod_rpt_basics";
            request.setLimitId(247468401L);
        }
        request.setDb(db);
        return ocBOrderByAdbMapper.getOrderList(request);
    }
}
