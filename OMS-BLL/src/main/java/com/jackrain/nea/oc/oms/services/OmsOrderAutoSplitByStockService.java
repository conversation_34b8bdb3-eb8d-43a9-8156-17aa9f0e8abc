package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.model.SgBPhyInStorageItemExt;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderSkuSplitTaskMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderSplitTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderIsInterceptEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSkuSplitTask;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSplitTask;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.util.OmsOrderAutoSplitUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitOrderUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.AddAndVoidStockListService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsQueryWareHouseService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.OmsRetryTimeUtil;
import com.jackrain.nea.util.OrderTagUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>缺货拆单服务</>
 * <p>
 * * @author: 江家雷
 * * @since: 2020/09/13
 * * create at : 2020/09/13 11:30
 */
@Component
@Slf4j
public class OmsOrderAutoSplitByStockService {

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsQueryWareHouseService omsQueryWareHouseService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private AddAndVoidStockListService addAndVoidStockListService;

    @Autowired
    private OmsOrderManualSplitService omsOrderManualSplitService;
    ;

    @Autowired
    private OmsOrderJdSplitService omsOrderJdSplitService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsOrderAutoSearchWarehouseService omsOrderAutoSearchWarehouseService;

    @Autowired
    private OmsOrderAutoSplitUtil omsOrderAutoSplitUtil;

    @Autowired
    private OcBOrderSplitTaskMapper ocBOrderSplitTaskMapper;

    @Autowired
    private OcBOrderSkuSplitTaskMapper ocBOrderSkuSplitTaskMapper;

    @Autowired
    private OmsOrderAutoSplitByGoodsService omsOrderAutoSplitByGoodsService;

    @Autowired
    private SplitOutStockOrderService splitOutStockOrderService;
    @Autowired
    private OmsAuditTaskService omsAuditTaskService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OrderAmountUtil orderAmountUtil;
    @Autowired
    private OmsOrderGoBackService omsOrderGoBackService;

    private SplitOrderUtils splitOrderUtils;

    @Autowired
    private OmsWmsTaskService wmsTaskService;

    public void handleSplitMqOrder(Long orderId, boolean isExeOccupy, Map<Long, List<Long>> shopSyncStockStrategyMap,  Map<Long,StCShopStrategyDO> shopStrategyMap) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        User user = SystemUserResource.getRootUser();
        Long startTime = System.currentTimeMillis();
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);

        /**
         * 校验数据
         */
        if (ocBOrder == null) {
            return;
        }
        if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())) {
            insertOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_SPLIT.getKey(), "订单状态非缺货，停止拆单！", "", "", user);
            // 更新订单拆分任务表
            this.updateOrderSplitTask(orderId, 2, "订单状态非缺货，停止拆单");
            return;
        }
        // 如果订单ID在oc_b_order_split_task表不存在，插入task表
        if (!isExistAtTaskTable(orderId)) {
            // 实缺标记:0否 1是
            int lackStock = 1;
            String remark = "补偿任务新增";
            int status = 1;
            List<OcBOrderItem>  orderItemList = ocBOrderItemMapper.selectOrderItemList(orderId);
            this.insertOrderSplitTask(ocBOrder, lackStock, remark, orderItemList, status);
        }
        /**
         * 判断订单是否重新占单
         */
        // 执行缺货从新占单
        if (isExeOccupy) {
            Long againOccupyStartTime = System.currentTimeMillis();
            // 调用缺货重新占单
            ValueHolderV14 vH14 = omsOrderAutoSearchWarehouseService.checkAutoSerachOrderInfo(user, orderId, false);
            if (vH14.isOK()) {
                // 按照SKU/SPU拆单
                omsOrderAutoSplitByGoodsService.splitOrderBySkuAndSpu(orderId);
//                // 新增操作日志
//                insertOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.RE_OCCUPANCY.getKey(), "缺货重新占单成功！", "", "", user);
                // 更新订单拆分任务表
                this.updateOrderSplitTask(orderId, 2, vH14.getMessage());
                return;
            }
        }

        /**
         * 校验订单店铺策略是否开启
         */
        // 店铺策略
        StCShopStrategyDO shopStrategy = shopStrategyMap.get(ocBOrder.getCpCShopId());
        if (shopStrategy == null || !"Y".equalsIgnoreCase(shopStrategy.getIsAutoSplit())) {
            // 更新订单拆分任务表
            this.updateOrderSplitTask(orderId, 0,"店铺未配置自动拆单，不能自动拆单");
            return;
        }
        /**
         * 如果订单的分的实体仓库和店铺策略的默认发货实体仓库一致，清空库存逻辑发货单
         */
        Long cpCPhyWarehouseId = ocBOrder.getCpCPhyWarehouseId();
        Long defaultStoreId = shopStrategy.getDefaultStoreId();
        if (cpCPhyWarehouseId != null && cpCPhyWarehouseId.compareTo(defaultStoreId) == 0) {
            boolean flag = omsOrderGoBackService.emptyLogic(ocBOrder, user);
        }
        /**
         * 处理拆单
         */
        try {
            Long doSplitOrderStartTime = System.currentTimeMillis();
            // 拆单结果处理
            ValueHolderV14 v14 = doSplitOrder(user, orderId, shopSyncStockStrategyMap, shopStrategyMap);
            if (v14.isOK()) {
                // 更新订单拆分任务表
                this.updateOrderSplitTask(orderId, 2, v14.getMessage());
            } else {
                // 更新订单拆分任务表
                this.updateOrderSplitTask(orderId, 0, v14.getMessage());
            }
        } catch (Exception e) {
            // 更新订单拆分任务表
            this.updateOrderSplitTask(orderId, 0, "处理拆单失败");
        }
    }

    private boolean isExistAtTaskTable(Long orderId) {
        int num = ocBOrderSplitTaskMapper.countNum(orderId);
        if (num > 0) {
            return true;
        }
        return false;
    }

    private OcBOrderSplitTask getOcBOrderSplitTaskByOrderId(Long orderId) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        OcBOrderSplitTask ocBOrderSplitTask;
        QueryWrapper<OcBOrderSplitTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("oc_b_order_id", orderId);
        queryWrapper.eq("status", 1);
        List<OcBOrderSplitTask> list = ocBOrderSplitTaskMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        ocBOrderSplitTask = list.get(0);
        return  ocBOrderSplitTask;
    }


    private void updateOrderSplitTask(Long orderId, int status, String remark) {
        OcBOrderSplitTask orderSplitTask = this.getOcBOrderSplitTaskByOrderId(orderId);
        if (orderSplitTask != null) {
            Date now = new Date();
            /**
             * 更新oc_b_order_split_task
             */
            UpdateWrapper<OcBOrderSplitTask> whereConditionOrderSplitTask = new UpdateWrapper<>();
            whereConditionOrderSplitTask.eq("id", orderSplitTask.getId());
            whereConditionOrderSplitTask.eq("oc_b_order_id", orderId);
            whereConditionOrderSplitTask.eq("status", 1);

            int splitTimes = Optional.ofNullable(orderSplitTask.getSplitTimes()).orElse(0) + 1;

            OcBOrderSplitTask orderSplitTaskDB = new OcBOrderSplitTask();
            orderSplitTaskDB.setSplitTimes(splitTimes);
            orderSplitTaskDB.setModifieddate(now);
            orderSplitTaskDB.setStatus(status);
            orderSplitTaskDB.setRemark(remark);
            orderSplitTaskDB.setNextTime(OmsRetryTimeUtil.getNextTimeOfDate(splitTimes));
            ocBOrderSplitTaskMapper.update(orderSplitTaskDB, whereConditionOrderSplitTask);

            /**
             * 更新oc_b_order拆单次数
             */
            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setQtySplit(Long.valueOf(orderSplitTaskDB.getSplitTimes()));
            updateOrder.setId(orderId);
            ocBOrderMapper.updateById(updateOrder);

            /**
             * 更新oc_b_order_sku_split_task
             */
            UpdateWrapper<OcBOrderSkuSplitTask> whereConditionForOrderSkuSplitTask = new UpdateWrapper<>();
            whereConditionForOrderSkuSplitTask.eq("oc_b_order_id", orderId);
            whereConditionForOrderSkuSplitTask.eq("status", 1);

            OcBOrderSkuSplitTask ocBOrderSkuSplitTask = new OcBOrderSkuSplitTask();
            ocBOrderSkuSplitTask.setStatus(status);
            ocBOrderSkuSplitTask.setModifieddate(now);
            ocBOrderSkuSplitTaskMapper.update(ocBOrderSkuSplitTask, whereConditionForOrderSkuSplitTask);

            /**
             * 如果订单明细是状态为退款完成，更新oc_b_order_sku_split_task的状态为已处理
             */
            List<OcBOrderItem>  orderItemList = ocBOrderItemMapper.selectOrderItemList(orderId);
            for (OcBOrderItem orderItem : orderItemList) {
                if (orderItem.getRefundStatus() == OcOrderRefundStatusEnum.SUCCESS.getVal()) {
                    ocBOrderSkuSplitTaskMapper.updateStatus(orderId, orderItem.getPsCSkuId(), 2);
                }
            }
        }
    }

    /**
     * 处理需要拆单的订单列表
     *
     * @param orderIds
     * @param shopSyncStockStrategyMap
     * @param shopStrategyMap
     */
    public void handleSplitMqOrderList(List<Long> orderIds, Map<Long, List<Long>> shopSyncStockStrategyMap, Map<Long,
            StCShopStrategyDO> shopStrategyMap) {

        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        User user = SystemUserResource.getRootUser();

        for (Long orderId : orderIds) {
            Long startTime = System.currentTimeMillis();
            QueryWrapper<OcBOrderSplitTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("oc_b_order_id", orderId);
            OcBOrderSplitTask task = ocBOrderSplitTaskMapper.selectOne(queryWrapper);
            //调用缺货重新占单
            ValueHolderV14 vH14 = omsOrderAutoSearchWarehouseService.checkAutoSerachOrderInfo(user, orderId, false);
            if (vH14.isOK()) {
                omsOrderAutoSplitByGoodsService.splitOrderBySkuAndSpu(orderId);
                if (task != null) {
                    int splitTimes = Optional.ofNullable(task.getSplitTimes()).orElse(0) + 1;
                    Date nextTime = Date.from(LocalDateTime.now()
                            .plusMinutes(30L * splitTimes)
                            .atZone(ZoneId.systemDefault()).toInstant());
                    task.setNextTime(nextTime);
                    task.setSplitTimes(splitTimes);
                    task.setModifieddate(new Date());
                    task.setStatus(2);
                    task.setRemark(vH14.getMessage());
                    ocBOrderSplitTaskMapper.updateOcBOrderSplitTask(task);
                }
                insertOrderLog(orderId, null, OrderLogTypeEnum.RE_OCCUPANCY.getKey(), "缺货重新占单成功！", "", "", user);
                continue;
            }
            // 店铺未配置自动拆单，不进行拆单
            OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
            StCShopStrategyDO shopStrategy = shopStrategyMap.get(ocBOrder.getCpCShopId());
            if (shopStrategy == null || !"Y".equalsIgnoreCase(shopStrategy.getIsAutoSplit())) {
                int splitTimes = Optional.ofNullable(task.getSplitTimes()).orElse(0) + 1;
                Date nextTime = Date.from(LocalDateTime.now()
                        .plusMinutes(30 * splitTimes)
                        .atZone(ZoneId.systemDefault()).toInstant());
                task.setNextTime(nextTime);
                task.setSplitTimes(splitTimes);
                task.setModifieddate(new Date());
                task.setStatus(0);
                task.setRemark("店铺未配置自动拆单，不能自动拆单");
                ocBOrderSplitTaskMapper.updateOcBOrderSplitTask(task);
                // 更新拆单次数
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setQtySplit(Long.valueOf(task.getSplitTimes()));
                updateOrder.setId(task.getOcBOrderId());
                ocBOrderMapper.updateById(updateOrder);
                return;
            }
            // 拆单结果处理
            ValueHolderV14 v14 = doSplitOrder(user, orderId, shopSyncStockStrategyMap, shopStrategyMap);
            if (task != null) {
                int splitTimes = Optional.ofNullable(task.getSplitTimes()).orElse(0) + 1;
                Date nextTime = Date.from(LocalDateTime.now()
                        .plusMinutes(splitTimes< 5 ? 30L * splitTimes : 60)
                        .atZone(ZoneId.systemDefault()).toInstant());
                task.setNextTime(nextTime);
                task.setSplitTimes(splitTimes);
                task.setModifieddate(new Date());
                if (v14.isOK()) {
                    task.setStatus(2);
                    task.setRemark(v14.getMessage());
                } else {
                    task.setStatus(0);
                    task.setRemark(v14.getMessage());
                }
                ocBOrderSplitTaskMapper.updateOcBOrderSplitTask(task);
                // 更新拆单次数
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setQtySplit(Long.valueOf(task.getSplitTimes()));
                updateOrder.setId(task.getOcBOrderId());
                ocBOrderMapper.updateById(updateOrder);
            }
            Long endTime = System.currentTimeMillis();
        }


    }


    /**
     * 判断拆单信息入口//并发控制
     *
     * @param user    用户对象
     * @param orderId 订单Id
     * @return boolean
     */

    public ValueHolderV14 doSplitOrder(User user, Long orderId, Map<Long, List<Long>> shopSyncStockStrategyMap, Map<Long,
            StCShopStrategyDO> shopStrategyMap) {
        ValueHolderV14 v14 = new ValueHolderV14();
        v14.setCode(0);
        v14.setMessage("订单缺货重新占单或拆单成功");
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrderRelation orderRelation = new OcBOrderRelation();
                OcBOrder ocBOrder = this.ocBOrderMapper.selectById(orderId);
                List<OcBOrderItem> orderUnRefundItemList = omsOrderItemService.selectUnSuccessRefund(orderId);
                orderRelation.setOrderInfo(ocBOrder);
                orderRelation.setOrderItemList(orderUnRefundItemList);
                // 根据订单及订单明细判断是否可以拆单
                checkOrder(orderRelation);
                // 根据店铺策略校验，判断当前订单是否可以拆单
                StCShopStrategyDO shopStrategy = shopStrategyMap.get(ocBOrder.getCpCShopId());
                checkShopStrategy(shopStrategy, ocBOrder);
                // 获取订单店铺下逻辑仓的列表
                List<Long> logicalWarehouseIdList = shopSyncStockStrategyMap.get(ocBOrder.getCpCShopId());
                if (CollectionUtils.isEmpty(logicalWarehouseIdList)) {
                    throw new NDSException("下单店铺《" + ocBOrder.getCpCShopTitle() + "》对应的逻辑仓配置为空,缺货拆单执行失败!");
                }
                //根据逻辑仓找到对应的实体仓
                List<Long> wareHouseIds = omsQueryWareHouseService.queryWareHouseIds(logicalWarehouseIdList);
                if (CollectionUtils.isEmpty(wareHouseIds)) {
                    throw new NDSException("下单店铺《" + ocBOrder.getCpCShopTitle() + "》下逻辑仓对应的实体仓为空,缺货拆单执行失败!");
                }
                // 聚合出 sku对应的仓库Map
                Map<String, List<SgBPhyInStorageItemExt>> skuStockMap = skuStockMap(orderRelation);
                // 对订单进行分组，进行预拆单
                // 缺货
                Map<Long, List<OcBOrderItem>> shortageSkuMap = new HashMap<>();
                // 有库存的商品
                Map<Long, List<OmsOrderAutoSplitUtil.OrderItemGroup>> hasStockSkuWareHouseMap = new HashMap<>();
                preSplitOrder(orderRelation, shopStrategy.getDefaultStoreId(), skuStockMap, shortageSkuMap, hasStockSkuWareHouseMap);
                // 判断是否拆分成2单及以上，否则则拆单失败,防止重复拆
                int orderNum = hasStockSkuWareHouseMap.size();
                orderNum = orderNum + (CollectionUtils.isEmpty(shortageSkuMap.get(shopStrategy.getDefaultStoreId())) ? 0 : 1);
                if (orderNum < 2) {
                    throw new NDSException("订单缺货拆单失败且不符合拆单条件!");
                }
                // 执行拆单
                ApplicationContextHandle.getBean(OmsOrderAutoSplitByStockService.class)
                        .splitOrder(orderRelation, shopStrategy, shortageSkuMap, hasStockSkuWareHouseMap);

            } else {
                v14.setCode(-1);
                v14.setMessage("当前订单其他人在操作，请稍后再试!");
                insertOrderLog(orderId, null, OrderLogTypeEnum.LACK_RE_OCCUPANCY.getKey(), "当前订单其他人在操作，请稍后再试!", "",
                        "", user);
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("缺货拆单执行异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            String message = ex.getMessage();
            if (ex.getMessage().length() > 1000) {
                message = message.substring(0, 1000);
            }
            insertOrderLog(orderId, null, OrderLogTypeEnum.LACK_RE_OCCUPANCY.getKey(), message, "", "", user);
            v14.setCode(-1);
            v14.setMessage(message);
        } finally {
            redisLock.unlock();
        }
        return v14;
    }

    private void preSplitOrder(OcBOrderRelation orderRelation, Long defaultPhyWarehouseId, Map<String, List<SgBPhyInStorageItemExt>> skuStockMap,
                               Map<Long, List<OcBOrderItem>> shortageSkuMap, Map<Long, List<OmsOrderAutoSplitUtil.OrderItemGroup>> hasStockSkuWareHouseMap) {
        List<OcBOrderItem> orderUnRefundItemList = orderRelation.getOrderItemList();
        shortageSkuMap.put(defaultPhyWarehouseId, new ArrayList<>());

        // 福袋/组合商品+挂靠赠品
        Map<OmsOrderAutoSplitUtil.OrderItemGroup, Set<Long>> combinedGroupMap = new HashMap<>();
        Map<Long, List<OmsOrderAutoSplitUtil.OrderItemGroup>> combinedMap = new HashMap<>();
        List<OmsOrderAutoSplitUtil.OrderItemGroup> combinedOrGiftBagList =
                omsOrderAutoSplitUtil.getCombinedOrGiftBag(orderUnRefundItemList);
        omsOrderAutoSplitUtil.buildCombinedOrderItemGroupMap(combinedOrGiftBagList, skuStockMap,
                defaultPhyWarehouseId, shortageSkuMap, combinedMap, combinedGroupMap);

        // 有库存普通商品+挂靠赠品
        Map<Long, List<OmsOrderAutoSplitUtil.OrderItemGroup>> goodsMap = new HashMap<>();
        Map<OmsOrderAutoSplitUtil.OrderItemGroup, Set<Long>> goodsGroupMap = new HashMap<>();
        List<OmsOrderAutoSplitUtil.OrderItemGroup> goodsAndGiftList = omsOrderAutoSplitUtil.getGoodsList(orderUnRefundItemList);
        omsOrderAutoSplitUtil.buildCombinedOrderItemGroupMap(goodsAndGiftList, skuStockMap, defaultPhyWarehouseId,
                shortageSkuMap, goodsMap, goodsGroupMap);

        // 有库存订单赠品
        Map<Long, List<OmsOrderAutoSplitUtil.OrderItemGroup>> giftMap = new HashMap<>();
        Map<OmsOrderAutoSplitUtil.OrderItemGroup, Set<Long>> giftGroupMap = new HashMap<>();
        List<OcBOrderItem> giftList = omsOrderAutoSplitUtil.getGiftList(orderUnRefundItemList);
        omsOrderAutoSplitUtil.buildOrderItemGroupMap(skuStockMap, defaultPhyWarehouseId, shortageSkuMap, giftMap,
                giftGroupMap, giftList);

        // 正常商品均无库存 不做拆分
        if (combinedMap.isEmpty() && goodsMap.isEmpty()) {
            return;
        }

        Map<OmsOrderAutoSplitUtil.OrderItemGroup, Set<Long>> startMap = new HashMap<>();
        startMap.putAll(combinedGroupMap);
        startMap.putAll(goodsGroupMap);

        // 将赠品绑定到普通商品上去,防止赠品单独拆分
        omsOrderAutoSplitUtil.bindGiftAndGoods(giftGroupMap, startMap);
        // 与普通商品没有相同仓库的赠品挂缺货仓
        omsOrderAutoSplitUtil.giftBindDefautPhyWarehouse(defaultPhyWarehouseId, shortageSkuMap, giftGroupMap);
        // 如果缺货商品都为赠品，需要将一个正常商品也放到缺货商品里，防止赠品缺货单独拆分
        omsOrderAutoSplitUtil.goodsBindDefaultPhyWarehouse(defaultPhyWarehouseId, shortageSkuMap, startMap);
        if (startMap.isEmpty()) {
            return;
        }
        // 开始分组
        omsOrderAutoSplitUtil.doSplitOrder(startMap, hasStockSkuWareHouseMap);


    }

    /**
     * sku对应的实体仓库
     *
     * @param orderRelation
     * @return
     */
    private Map<String, List<SgBPhyInStorageItemExt>> skuStockMap(OcBOrderRelation orderRelation) {
        //去重明细skuCode
        List<String> distinctSkuList = distinctSku(orderRelation.getOrderItemList());
        //根据skuList和实体仓列表查询库存数量
        List<SgSumStorageQueryResult> sgSumStorageQueryResultList = sgSumStorageQuery(orderRelation);
        //批量查询逻辑发货单接口
        //SgSendBillQueryResult sgSendBillQueryResult = sgRpcService.querySgSend(orderRelation.getOrderInfo(), SgConstantsIF.BILL_TYPE_RETAIL);
        // 当前订单库存中心没有可用库存且订单对应的逻辑发货单为空
        if (CollectionUtils.isEmpty(sgSumStorageQueryResultList)) {
            log.info("OmsOrderAutoSplitByStockService.doSplitOrder订单orderId={}," +
                    "缺货拆单执行异常!失败原因：库存不足 ", orderRelation.getOrderInfo().getId());
            throw new NDSException("库存不足，自动拆单执行失败!");
        }

        //sku所对应的实体仓和实体仓下的可占用数量，Map集合
        Map<String, List<SgBPhyInStorageItemExt>> skuCountMap = null;//calcTotalAvailableNum(distinctSkuList, orderRelation, sgSumStorageQueryResultList, sgSendBillQueryResult);
        if (log.isDebugEnabled()) {
            log.debug("OmsOrderAutoSplitByStockService.doSplitOrder，sku所对应的实体仓和实体仓下的可占用总数量Map={}", JSON.toJSONString(skuCountMap));
        }
        return skuCountMap;
    }


    @Transactional(rollbackFor = Exception.class)
    public void splitOrder(OcBOrderRelation orderRelation, StCShopStrategyDO shopStrategy, Map<Long, List<OcBOrderItem>> shortageSkuMap,
                           Map<Long, List<OmsOrderAutoSplitUtil.OrderItemGroup>> hasStockSkuWareHouseMap) {
        Long oriOrderId = orderRelation.getOrderInfo().getId();
        // 京东拆单后结果
        List<OcBOrderRelation> jdOrderRelationList = new ArrayList<>();
        try {
            int index = 1;
            User user = SystemUserResource.getRootUser();
            List<Long> outStockOrderIdList = new ArrayList<>();
            // 开始拆分子订单 有库存的订单
            //实例化一个对象，用于批量作废和占用逻辑发货单
            List<OcBOrderRelation> occupyList = new ArrayList<>();
            for (Long key : hasStockSkuWareHouseMap.keySet()) {
                List<OmsOrderAutoSplitUtil.OrderItemGroup> itemGroups = hasStockSkuWareHouseMap.get(key);
                OcBOrderRelation ocBOrderRelation = omsOrderAutoSplitUtil.saveOrder(itemGroups, orderRelation, index,
                        OmsOrderStatus.UNCONFIRMED.toInteger(), jdOrderRelationList, shopStrategy.getIsPlatformSplit());
                if ("Y".equals(shopStrategy.getIsPlatformSplit())
                        && PlatFormEnum.JINGDONG.getCode().equals(orderRelation.getOrderInfo().getPlatform())
                        && !"手工新增".equals(orderRelation.getOrderInfo().getOrderSource())) {
                    continue;
                }
                index++;
                // 分仓
                ocBOrderRelation = omsOrderAutoSplitUtil.distributeWarehouse(ocBOrderRelation, key);
                // 重算订单主表金额
                ocBOrderRelation = omsOrderAutoSplitUtil.reorganizeAmount(ocBOrderRelation, oriOrderId);
                occupyList.add(ocBOrderRelation);
                // 查询组合商品原明细信息，并保存
                omsOrderAutoSplitUtil.saveCombinedOrGiftBagItem(orderRelation.getOrderInfo().getId(), ocBOrderRelation);
                // 重新计算金额
                OcBOrderRelation relation = omsOrderService.selectOmsOrderInfoOccupy(ocBOrderRelation.getOrderId());
                OcBOrder order = orderAmountUtil.recountOrderAmount(relation);
                // 直播标  轻供标 组标  赠品标 换货标
                relation.setOrderInfo(order);
                OrderTagUtil.orderTags(relation);
                omsOrderService.updateOrderInfo(order);

            }
            // 缺货子单处理
//            List<OcBOrderItem> shortageItems = shortageSkuMap.get(shopStrategy.getDefaultStoreId());
//            if (CollectionUtils.isNotEmpty(shortageItems)) {
//                String code = shortageItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.joining());
//                OmsOrderAutoSplitUtil.OrderItemGroup group = new OmsOrderAutoSplitUtil.OrderItemGroup();
//                Random random = new Random();
//                group.setCode(code + random.nextInt(1000));
//                group.setItems(shortageItems);
//                List<OmsOrderAutoSplitUtil.OrderItemGroup> itemGroups = new ArrayList<>();
//                itemGroups.add(group);
//                OcBOrderRelation shortageOcBOrderRelation = omsOrderAutoSplitUtil.saveOrder(itemGroups, orderRelation
//                        , index, OmsOrderStatus.BE_OUT_OF_STOCK.toInteger(), jdOrderRelationList, shopStrategy.getIsPlatformSplit());
//                //调用生成订单方法
//                if (log.isDebugEnabled()) {
//                    log.debug("OmsOrderAutoSplitByStockService.splitOrder 原单orderId={},拆出的缺货子单{}={}",
//                            orderRelation.getOrderInfo().getId(), index, JSON.toJSONString(shortageOcBOrderRelation));
//                }
//                // 手工订单，非京东订单，店铺策略 是否平台拆单
//                if (!"Y".equals(shopStrategy.getIsPlatformSplit())
//                        || !PlatFormEnum.JINGDONG.getCode().equals(orderRelation.getOrderInfo().getPlatform())
//                        || "手工新增".equals(orderRelation.getOrderInfo().getOrderSource())) {
//
//                    // 分仓
//                    shortageOcBOrderRelation = omsOrderAutoSplitUtil.distributeWarehouse(shortageOcBOrderRelation,
//                            shopStrategy.getDefaultStoreId());
//                    // 重算订单主表金额
//                    shortageOcBOrderRelation = omsOrderAutoSplitUtil.reorganizeAmount(shortageOcBOrderRelation,
//                            oriOrderId);
//                    // 重新分物流
//                    omsOrderAutoSplitUtil.distributeLogistics(shortageOcBOrderRelation.getOrderInfo());
//                    occupyList.add(shortageOcBOrderRelation);
//                    // 查询组合商品原明细信息，并保存
//                    omsOrderAutoSplitUtil.saveCombinedOrGiftBagItem(orderRelation.getOrderInfo().getId(),
//                            shortageOcBOrderRelation);
//                    // 重新计算金额
//                    OcBOrderRelation relation =
//                            omsOrderService.selectOmsOrderInfoOccupy(shortageOcBOrderRelation.getOrderId());
//                    OcBOrder order = orderAmountUtil.recountOrderAmount(relation);
//                    // 直播标  轻供标 组标  赠品标 换货标
//                    relation.setOrderInfo(order);
//                    OrderTagUtil.orderTags(relation);
//                    omsOrderService.updateOrderInfo(order);
//                }
//            }

            if ("Y".equals(shopStrategy.getIsPlatformSplit())
                    && PlatFormEnum.JINGDONG.getCode().equals(orderRelation.getOrderInfo().getPlatform())
                    && !"手工新增".equals( orderRelation.getOrderInfo().getOrderSource())) {
                omsOrderJdSplitService.splitOrderByJingdong(orderRelation.getOrderInfo(), jdOrderRelationList, user);
                insertOrderLog(orderRelation.getOrderInfo().getId(),
                        orderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.ORDER_SPLIT.getKey(), "京东自动拆单，订单作废", "", "", user);
            } else {
                Long orginId = orderRelation.getOrderInfo().getId();
                //作废订单列表
                List<OcBOrder> voidOrderList = new ArrayList<>();
                voidOrderList.add(orderRelation.getOrderInfo());
                if (log.isDebugEnabled()) {
                    log.debug("OmsOrderAutoSplitByStockService.splitOrder 原订单OrderId = {},订单占用库存订单列表occupyList={}",
                            orginId, occupyList);
                }
                ValueHolderV14<List> result =
                        addAndVoidStockListService.AddAndVoidStock(voidOrderList, occupyList, user, true);
                if (CollectionUtils.isNotEmpty(voidOrderList)) {
                    for (OcBOrder ocBOrder : voidOrderList) {
                        //订单操作日志
                        try {
                            omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                                    ocBOrder.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(),
                                    "缺货拆单,作废原单", "", "", user
                            );
                        } catch (Exception e) {
                            log.error("作废原单调用日志服务异常", e);
                        }
                    }
                }
                if (log.isDebugEnabled()) {
                    log.debug("OrderAutoSplitByStockService.splitOrder,原订单OrderId={},订单自动拆单保存调用批量新增逻辑发货单服务出参={}",
                            orginId, JSON.toJSONString(result));
                }
                if (result.isOK()) {
//                    List resultList = result.getData();
//                    for (SgSendSaveWithPriorityResult sgSendSaveWithPriorityResult : resultList) {
//                        if (sgSendSaveWithPriorityResult.getPreoutResult() == 0) {
//                            OcBOrder ocBOrder = ocBOrderMapper.selectByID(sgSendSaveWithPriorityResult.getSourceBillId());
//                            insertOrderLog(sgSendSaveWithPriorityResult.getSourceBillId(),
//                                    sgSendSaveWithPriorityResult.getSourceBillNo(),
//                                    OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(),
//                                    "自动拆单，订单id为【" + sgSendSaveWithPriorityResult.getSourceBillId() + "】占用库存成功!", "",
//                                    "", user);
//                            // 重新分物流
//                            omsOrderAutoSplitUtil.distributeLogistics(ocBOrder);
//                            //占用库存成功后，将待审核订单加入审核Task中，2020/03/30新增逻辑
//                            log.info("进行订单拆单");
//                            // 当开关开启走仓库拆单 （插入拆单task表），不开启 则不走仓库拆单*（插入传订单审核）
//                            if (splitOrderUtils.isOpenWareHouseSplitOrder(ocBOrder)) {
//                                // 插入仓库拆单任务表
//                                wmsTaskService.saveOrUpdateOcBWarehouseSplitTask(ocBOrder.getId(), user);
//                            } else {
//                                // 插入传wms表
//                                omsAuditTaskService.createOcBAuditTask(ocBOrder, OmsAuditTimeCalculateReason.SPLIT);
//                            }
//                            // @20201209 订单合单加密信息
//                            MD5Util.encryptOrderInfo4Merge(ocBOrder);
//                            ocBOrderMapper.updateById(ocBOrder);
//                            omsOrderManualSplitService.pushEs(sgSendSaveWithPriorityResult.getSourceBillId(),
//                                    OmsOrderStatus.UNCONFIRMED.toInteger());
//                            omsOrderAutoSplitByGoodsService.splitOrderBySkuAndSpu(sgSendSaveWithPriorityResult.getSourceBillId());
//                        } else {
//                            //然后更新明细缺货数量
//                            List<SgStoreWithPrioritySearchItemResult> outStockItemList = sgSendSaveWithPriorityResult.getOutStockItemList();
//                            if (log.isDebugEnabled()) {
//                                log.debug("OmsOrderAutoSplitByStockService.splitOrder订单ID={}, 缺货明细：{}", sgSendSaveWithPriorityResult.getSourceBillId(), JSON.toJSONString(outStockItemList));
//                            }
//                            Map<Long, BigDecimal> orderItemOutStockMap = new HashMap<>();
//                            if (CollectionUtils.isNotEmpty(outStockItemList)) {
//                                orderItemOutStockMap = outStockItemList.stream().collect(Collectors.toMap(SgStoreWithPrioritySearchItemResult::getSourceItemId, SgStoreWithPrioritySearchItemResult::getQtyOutOfStock));
//                            }
//                            if (log.isDebugEnabled()) {
//                                log.debug("OmsOrderAutoSplitByStockService.splitOrder订单ID={}, 缺货明细Map：{}", sgSendSaveWithPriorityResult.getSourceBillId(), JSON.toJSONString(orderItemOutStockMap));
//                            }
//                            List<OcBOrderItem> orderItemListTmp = omsOrderItemService.selectUnSuccessRefund(sgSendSaveWithPriorityResult.getSourceBillId());
//                            for (OcBOrderItem orderItem : orderItemListTmp) {
//                                if (orderItemOutStockMap.containsKey(orderItem.getId())) {
//                                    if (log.isDebugEnabled()) {
//                                        log.debug("OmsOrderAutoSplitByStockService.splitOrder订单ID={},skuId={},缺货数量={}",
//                                                sgSendSaveWithPriorityResult.getSourceBillId(),
//                                                orderItem.getPsCSkuId(), orderItemOutStockMap.get(orderItem.getId()));
//                                    }
//                                    orderItem.setQtyLost(orderItemOutStockMap.get(orderItem.getId()));
//                                    //更新明细表
//                                    omsOrderItemService.updateOcBOrderItem(orderItem,
//                                            sgSendSaveWithPriorityResult.getSourceBillId());
//                                }
//                            }
//                            omsOrderLogService.addUserOrderLog(sgSendSaveWithPriorityResult.getSourceBillId(),
//                                    sgSendSaveWithPriorityResult.getSourceBillNo(),
//                                    OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(),
//                                    "缺货拆单，订单id为【" + sgSendSaveWithPriorityResult.getSourceBillId() + "】占用库存缺货!",
//                                    "", "", user);
//                            omsOrderManualSplitService.pushEs(sgSendSaveWithPriorityResult.getSourceBillId(),
//                                    OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
//                            // 缺货订单ID
//                            outStockOrderIdList.add(sgSendSaveWithPriorityResult.getSourceBillId());
//                        }
//                    }
                } else {
                    throw new NDSException(Resources.getMessage("自动拆单调用库存中心，批量作废和占用库存接口失败!异常信息：{}", result.getMessage()));
                }
            }

            /**
             * 处理新拆出来的缺货订单，插入oc_b_order_split_task,oc_b_order_sku_split_task
             */
            //this.processOutStock(outStockOrderIdList);

            /**
             * 对拆分出来的新订单，批量执行hold单
             */
            this.batchExecHoldOrder(occupyList);

        } catch (Exception ex) {
            log.error(LogUtil.format("拆单异常,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            throw ex;
        }
    }

    /**
     * 批量执行hold单
     * @param waitHoldOrderList
     */
    private void batchExecHoldOrder(List<OcBOrderRelation> waitHoldOrderList) {
        if (CollectionUtils.isNotEmpty(waitHoldOrderList)) {
            for (OcBOrderRelation orderRelation : waitHoldOrderList) {
                // 自动执行HOLD单策略
                ocBOrderHoldService.autoHoldOrder(orderRelation, SystemUserResource.getRootUser());
            }
        }
    }


    private void processOutStock(List<Long> outStockOrderIdList) {
        if (CollectionUtils.isNotEmpty(outStockOrderIdList)) {
            for (Long orderId : outStockOrderIdList) {
                OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(orderId);
                if (orderRelation != null && orderRelation.getOrderInfo() != null && OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderRelation.getOrderInfo().getOrderStatus())) {
                    // 实缺标记:0否 1是
                    int lackStock = 1;
                    String remark = "拆分出来的新的缺货订单";
                    int status = 0;
                    this.insertOrderSplitTask(orderRelation.getOrderInfo(), lackStock, remark, orderRelation.getOrderItemList(), status);
                }
            }
        }
    }

    private void insertOrderSplitTask(OcBOrder order, int lackStock, String remark, List<OcBOrderItem> orderItemList, int status) {
        splitOutStockOrderService.insertOrderSplitTask(order, lackStock, remark, status, SystemUserResource.getRootUser());
        // 获取有效是的订单明细数据,描述：剔除组合（福袋）商品（protype = 4），剔除掉已取消（RefundStatus=6）的, 剔除中台赠品（GiftType = 1）
        List<OcBOrderItem> newOrderItemList = splitOutStockOrderService.getEffectiveOrderItemList(orderItemList);
        if (CollectionUtils.isNotEmpty(newOrderItemList)) {
            // 获取有效是的skuId列表
            List<Long> skuIdList = newOrderItemList.stream().map(OcBOrderItem::getPsCSkuId).collect(Collectors.toList());
            splitOutStockOrderService.insertOrderSkuSplitTaskMapper(order.getId(), status, skuIdList, SystemUserResource.getRootUser());
        }
    }

    /**
     * 根据订单信息判断订单是否可以拆单
     *
     * @param orderRelation
     */
    private void checkOrder(OcBOrderRelation orderRelation) {
        OcBOrder order = orderRelation.getOrderInfo();
        if (order == null) {
            throw new NDSException("订单信息为空！");
        }
        if (Objects.equals(order.getPayType(), OmsPayType.CASH_ON_DELIVERY.toInteger())) {
            throw new NDSException("货到付款的订单不支持缺货拆单");
        }
        // HOLD单订单也可以拆单，但是退款原因HOLD单的订单不能拆单
        if (InreturningStatus.INRETURNING.equals(order.getIsInreturning())
                && OmsOrderIsInterceptEnum.YES.getVal().equals(order.getIsInterecept())) {
            throw new NDSException("订单退款Hold单,不能进行缺货拆单！");
        }
        if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus())) {
            throw new NDSException("订单的状态为" + order.getOrderStatus() + "，非缺货状态不能进行缺货拆单！");
        }
        List<OcBOrderItem> items = orderRelation.getOrderItemList();
        if (CollectionUtils.isEmpty(items) || items.size() < 2) {
            throw new NDSException("订单未退款明细为空或未退款明细为1，不能进行缺货拆单！");
        }

        List<OmsOrderAutoSplitUtil.OrderItemGroup> combinedOrGiftBagtList = omsOrderAutoSplitUtil.getCombinedOrGiftBag(items);
        List<OmsOrderAutoSplitUtil.OrderItemGroup> goodsAndGiftList = omsOrderAutoSplitUtil.getGoodsList(items);
        int size = combinedOrGiftBagtList == null ? 0 : combinedOrGiftBagtList.size();
        size = size + (goodsAndGiftList == null ? 0 : goodsAndGiftList.size());
        if (size < 2) {
            throw new NDSException("订单未退款明细根据分组后（分组条件：挂靠赠品、组合商品、福袋）不符合拆单条件，不能进行缺货拆单！");
        }
    }

    /**
     * 根据店铺策略判断订单是否可以拆单
     *
     * @param shopStrategy
     */
    private void checkShopStrategy(StCShopStrategyDO shopStrategy, OcBOrder ocBOrder) {
        if (shopStrategy == null) {
            throw new NDSException("店铺策略没有配置！");
        }
        if ("Y".equalsIgnoreCase(shopStrategy.getIsFullyGoods())) {
            throw new NDSException("店铺策略配置，《是否整单有货占库存》= " + shopStrategy.getIsFullyGoods());
        }
        if (!"Y".equalsIgnoreCase(shopStrategy.getIsJudgeStock())) {
            throw new NDSException("店铺策略配置，《是否校验仓库》= " + shopStrategy.getIsJudgeStock());
        }
        if (PlatFormEnum.JINGDONG.getCode().equals(Optional.ofNullable(ocBOrder.getPlatform()).orElse(-1))) {
            if (!"Y".equalsIgnoreCase(shopStrategy.getIsPlatformSplit())) {
                throw new NDSException("店铺策略配置，《是否京东平台拆单》= " + shopStrategy.getIsPlatformSplit());
            }
        }

    }

    /**
     * 查询所有店铺策略缓存到Map中
     *
     * @return
     */
    public Map<Long, StCShopStrategyDO> loadShopStrategyToMap() {
        List<StCShopStrategyDO> stCShopStrategyDOList = shopStrategyService.selectAllShopStrategy();
        Map<Long, StCShopStrategyDO> shopStrategyMap =
                stCShopStrategyDOList.stream().collect(Collectors.toMap(StCShopStrategyDO::getCpCShopId, t -> t, (v,
                                                                                                                  v1) -> v1));
        return shopStrategyMap;
    }

    /**
     * 插入订单日志，增加异常处理，为了不影响主流程
     *
     * @param orderId
     * @param billNo
     * @param logType
     * @param logMessage
     * @param param
     * @param errorMessage
     * @param user
     */
    private void insertOrderLog(long orderId, String billNo, String logType, String logMessage,
                                String param, String errorMessage, User user) {
        try {
            omsOrderLogService.addUserOrderLog(orderId, billNo, logType, logMessage, param, errorMessage, user);
        } catch (Exception e) {
            log.error(LogUtil.format("OmsOrderAutoSplitByStockService记录日志报错,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 去重订单明细中的sku
     *
     * @param orderUnRefundItemList
     * @return skuCodeList
     */
    public List<String> distinctSku(List<OcBOrderItem> orderUnRefundItemList) {
        if (CollectionUtils.isEmpty(orderUnRefundItemList)) {
            return null;
        }
        return orderUnRefundItemList.stream().map(OcBOrderItem::getPsCSkuEcode).distinct().collect(Collectors.toList());
    }

    /**
     * 以skuCode为维度，聚合计算总可用库存数量
     *
     * @param distinctList
     * @param orderRelation
     * @param sgSumStorageQueryResultList
     * @param sgSendBillQueryResult
     * @return Map<String, List < gBPhyInStorageItemExt>>
     */
//    public Map<String, List<SgBPhyInStorageItemExt>> calcTotalAvailableNum(List<String> distinctList,
//                                                                           OcBOrderRelation orderRelation,
//                                                                           List<SgSumStorageQueryResult> sgSumStorageQueryResultList, SgSendBillQueryResult sgSendBillQueryResult) {
//        log.debug(this.getClass().getName() + "进入calcTotalAvailableNum方法======" + "订单id为：" + orderRelation.getOrderInfo().getId());
//        //构造对象，key值为skuCode，value为skuCode对应的实体仓Id和实体仓下对应的该skuCode的库存数量
//        Map<String, List<SgBPhyInStorageItemExt>> skuAndSgBPhyInStorageMap = new HashMap<>();
//        //解析舒威批量查询逻辑发货单接口，得出正在被占单skuCode和skuCode对应的被占用数量
//        String billId = String.valueOf(orderRelation.getOrderInfo().getId());
//        String billType = String.valueOf(SgConstantsIF.BILL_TYPE_RETAIL);
//        String mapKey = billId + "," + billType;
//        List<SgBSendItem> sgBSendItemOccupyList = new ArrayList<>();
//        Map<String, HashMap<SgBSend, List<SgBSendItem>>> resultsTmp;
//        Map<SgBSend, List<SgBSendItem>> sgSendMapTmp;
//        if (sgSendBillQueryResult != null) {
//            resultsTmp = sgSendBillQueryResult.getResults();
//            if (resultsTmp != null) {
//                sgSendMapTmp = resultsTmp.get(mapKey);
//                if (sgSendMapTmp != null) {
//                    for (SgBSend sgBSend : sgSendMapTmp.keySet()) {
//                        sgBSendItemOccupyList = sgSendMapTmp.get(sgBSend);
//                    }
//                }
//            }
//        }
//        log.debug(this.getClass().getName() + "sgBSendItemOccupyList的值为======" + sgBSendItemOccupyList.toString() +
//                "订单id为：" + orderRelation.getOrderInfo().getId());
//        //遍历去重后的商品明细的sku列表
//        for (String skuCode : distinctList) {
//            List<SgBPhyInStorageItemExt> sgBPhyInStorageItemExtListTmp = new ArrayList<>();
//            boolean oldOrderPhyWareIdContainsFlag = false;
//            //根据skuCode查询斌哥接口返回数据，即先查询逻辑仓下面实体仓的库存情况，并以skuCode为key值将skuCode下的所有实体仓的库存数量聚合到一起。
//            Map<Long, Boolean> phyWareHouseAddOccupyFlagMap = new HashMap<>();
//            for (SgSumStorageQueryResult sgSumStorageQueryResult : sgSumStorageQueryResultList) {
//                phyWareHouseAddOccupyFlagMap.put(sgSumStorageQueryResult.getCpCPhyWarehouseId(), false);
//            }
//            for (SgSumStorageQueryResult sgSumStorageQueryResult : sgSumStorageQueryResultList) {
//                boolean phyWareIdContainsFlag = false;
//                if (sgBPhyInStorageItemExtListTmp.size() > 0) {
//                    if (sgSumStorageQueryResult.getPsCSkuEcode().equals(skuCode)) {
//                        for (SgBPhyInStorageItemExt tmpSgBPhyInStorageItemExt : sgBPhyInStorageItemExtListTmp) {
//                            if (tmpSgBPhyInStorageItemExt.getAdvise_phy_warehouse_id().equals(sgSumStorageQueryResult.getCpCPhyWarehouseId())
//                                    && tmpSgBPhyInStorageItemExt.getPs_c_sku_ecode().equals(sgSumStorageQueryResult.getPsCSkuEcode())) {
//                                phyWareIdContainsFlag = true;
//                                tmpSgBPhyInStorageItemExt.setTotal_qty_available(tmpSgBPhyInStorageItemExt.getTotal_qty_available().add(sgSumStorageQueryResult.getQtyAvailable()));
//                                if (sgSumStorageQueryResult.getCpCPhyWarehouseId().equals(orderRelation.getOrderInfo().getCpCPhyWarehouseId())) {
//                                    //如果是原发货实体仓，则是否包含原发货实体仓，oldOrderPhyWareIdContainsFlag的值设置为true
//                                    oldOrderPhyWareIdContainsFlag = true;
//                                    if (!phyWareHouseAddOccupyFlagMap.get(sgSumStorageQueryResult.getCpCPhyWarehouseId())) {
//                                        //遍历舒威逻辑发货单返回的正在被占用skuCode对象
//                                        for (SgBSendItem sgBSendItemTmp : sgBSendItemOccupyList) {
//                                            if (sgSumStorageQueryResult.getPsCSkuEcode().equals(sgBSendItemTmp.getPsCSkuEcode())) {
//                                                //聚合正在被占用的库存数量到，当前发货实体仓的可用总库存数中；原发货实体仓可用库存数量 = 实体仓可用库存数量 + 正在被占用的库存数量
//                                                tmpSgBPhyInStorageItemExt.setTotal_qty_available(tmpSgBPhyInStorageItemExt.getTotal_qty_available().add(sgBSendItemTmp.getQtyPreout()));
//                                            }
//                                        }
//                                        phyWareHouseAddOccupyFlagMap.put(sgSumStorageQueryResult.getCpCPhyWarehouseId(), true);
//                                    }
//                                }
//                            }
//                        }
//                        if (!phyWareIdContainsFlag) {
//                            SgBPhyInStorageItemExt tmpEntity = new SgBPhyInStorageItemExt();
//                            tmpEntity.setPs_c_sku_ecode(sgSumStorageQueryResult.getPsCSkuEcode());
//                            tmpEntity.setAdvise_phy_warehouse_id(sgSumStorageQueryResult.getCpCPhyWarehouseId());
//                            tmpEntity.setAdvise_phy_warehouse_ename(sgSumStorageQueryResult.getCpCPhyWarehouseEname());
//                            tmpEntity.setAdvise_phy_warehouse_ecode(sgSumStorageQueryResult.getCpCPhyWarehouseEcode());
//                            tmpEntity.setTotal_qty_available(sgSumStorageQueryResult.getQtyAvailable());
//                            if (sgSumStorageQueryResult.getCpCPhyWarehouseId().equals(orderRelation.getOrderInfo().getCpCPhyWarehouseId())) {
//                                //如果是原发货实体仓，则是否包含原发货实体仓，oldOrderPhyWareIdContainsFlag的值设置为true
//                                oldOrderPhyWareIdContainsFlag = true;
//                                //遍历舒威逻辑发货单返回的正在被占用skuCode对象
//                                for (SgBSendItem sgBSendItemTmp : sgBSendItemOccupyList) {
//                                    if (sgSumStorageQueryResult.getPsCSkuEcode().equals(sgBSendItemTmp.getPsCSkuEcode())) {
//                                        //聚合正在被占用的库存数量到，当前发货实体仓的可用总库存数中；原发货实体仓可用库存数量 = 实体仓可用库存数量 + 正在被占用的库存数量
//                                        tmpEntity.setTotal_qty_available(tmpEntity.getTotal_qty_available().add(sgBSendItemTmp.getQtyPreout()));
//                                    }
//                                }
//                                phyWareHouseAddOccupyFlagMap.put(sgSumStorageQueryResult.getCpCPhyWarehouseId(), true);
//                            }
//                            sgBPhyInStorageItemExtListTmp.add(tmpEntity);
//                        }
//                    }
//                } else {
//                    SgBPhyInStorageItemExt sgBPhyInStorageItemExt = new SgBPhyInStorageItemExt();
//                    if (sgSumStorageQueryResult.getPsCSkuEcode().equals(skuCode)) {
//                        sgBPhyInStorageItemExt.setPs_c_sku_ecode(sgSumStorageQueryResult.getPsCSkuEcode());
//                        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_id(sgSumStorageQueryResult.getCpCPhyWarehouseId());
//                        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_ename(sgSumStorageQueryResult.getCpCPhyWarehouseEname());
//                        sgBPhyInStorageItemExt.setAdvise_phy_warehouse_ecode(sgSumStorageQueryResult.getCpCPhyWarehouseEcode());
//                        sgBPhyInStorageItemExt.setTotal_qty_available(sgSumStorageQueryResult.getQtyAvailable());
//                        //判断当前遍历的实体仓对象，是否是原发货实体仓
//                        if (sgSumStorageQueryResult.getCpCPhyWarehouseId().equals(orderRelation.getOrderInfo().getCpCPhyWarehouseId())) {
//                            //如果是原发货实体仓，则是否包含原发货实体仓，oldOrderPhyWareIdContainsFlag的值设置为true
//                            oldOrderPhyWareIdContainsFlag = true;
//                            //遍历舒威逻辑发货单返回的正在被占用skuCode对象
//                            for (SgBSendItem sgBSendItemTmp : sgBSendItemOccupyList) {
//                                if (sgSumStorageQueryResult.getPsCSkuEcode().equals(sgBSendItemTmp.getPsCSkuEcode())) {
//                                    //聚合正在被占用的库存数量到，当前发货实体仓的可用总库存数中；原发货实体仓可用库存数量 = 实体仓可用库存数量 + 正在被占用的库存数量
//                                    sgBPhyInStorageItemExt.setTotal_qty_available(sgBPhyInStorageItemExt.getTotal_qty_available().add(sgBSendItemTmp.getQtyPreout()));
//                                }
//                            }
//                            phyWareHouseAddOccupyFlagMap.put(sgSumStorageQueryResult.getCpCPhyWarehouseId(), true);
//                        }
//                        //将当前实体仓对象，放入到当前skuCode所对应的实体仓对象列表中
//                        sgBPhyInStorageItemExtListTmp.add(sgBPhyInStorageItemExt);
//                    }
//                    skuAndSgBPhyInStorageMap.put(skuCode, sgBPhyInStorageItemExtListTmp);
//                }
//
//            }
//            //判断斌哥返回的逻辑仓下面实体仓的库存情况列表中，是否包含原发货实体仓对象；
//            // 如果不包含原发货实体仓对象，则构造原发货实体仓对象，且原发货实体仓对象skuCode的可用库存数量等于该订单skuCode正在被占用的库存数量
//            if (!oldOrderPhyWareIdContainsFlag) {
//                SgBPhyInStorageItemExt SgBPhyInStorageItemExtThree = new SgBPhyInStorageItemExt();
//                //遍历舒威逻辑发货单返回的正在被占用skuCode对象
//                for (SgBSendItem sgBSendItemTmp : sgBSendItemOccupyList) {
//                    if (skuCode.equals(sgBSendItemTmp.getPsCSkuEcode())) {
//                        SgBPhyInStorageItemExtThree.setTotal_qty_available(sgBSendItemTmp.getQtyPreout());
//                        SgBPhyInStorageItemExtThree.setAdvise_phy_warehouse_ecode(orderRelation.getOrderInfo().getCpCPhyWarehouseEcode());
//                        SgBPhyInStorageItemExtThree.setAdvise_phy_warehouse_ename(orderRelation.getOrderInfo().getCpCPhyWarehouseEname());
//                        SgBPhyInStorageItemExtThree.setAdvise_phy_warehouse_id(orderRelation.getOrderInfo().getCpCPhyWarehouseId());
//                        SgBPhyInStorageItemExtThree.setPs_c_sku_ecode(skuCode);
//                        sgBPhyInStorageItemExtListTmp.add(SgBPhyInStorageItemExtThree);
//                    }
//                }
//            }
//            log.debug(this.getClass().getName() + "最终skuCode的值为：" + skuCode + "skuCode" +
//                    "对应的最终sgBPhyInStorageItemExtListTmp为：" + sgBPhyInStorageItemExtListTmp.toString());
//            skuAndSgBPhyInStorageMap.put(skuCode, sgBPhyInStorageItemExtListTmp);
//        }
//        return skuAndSgBPhyInStorageMap;
//    }

    /**
     * 返回存在库存的实体仓
     *
     * @param orderRelation 订单对象
     * @param
     * @return List<Long>
     */
    public List<SgSumStorageQueryResult> sgSumStorageQuery(OcBOrderRelation orderRelation) {

        //筛选实体仓
        ValueHolderV14<List<SgSumStorageQueryResult>> holderV14 = sgRpcService.querySendWareHouseStock(orderRelation);
        //库存中心实体仓的返回结果
        if (holderV14.isOK()) {
            return holderV14.getData();
        } else {
            return null;
        }
    }

}
