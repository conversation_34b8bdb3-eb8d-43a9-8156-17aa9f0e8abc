package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeProMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBInvoiceNoticeRelation;
import com.jackrain.nea.oc.oms.model.result.OcBInvoiceNoticeItemResult;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNotice;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticeItem;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticePro;
import com.jackrain.nea.oc.oms.nums.OcInvoiceStatusEnum;
import com.jackrain.nea.oc.oms.util.InvoiceNoticeDealUtil;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCAutoInvoiceDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AmountCalcUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: huang.zaizai
 * @since: 2019/8/28
 * create at : 2019/8/28 09:00
 */
@Slf4j
@Component
public class InvoiceNoticeMergeService {
    @Autowired
    private InvoiceNoticeDealUtil invoiceNoticeDealUtil;
    @Autowired
    private OcBInvoiceNoticeProMapper proMapper;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private InvoiceTableQueryService invoiceTableQueryService;
    @Autowired
    private InvoiceNoticeSaveService invoiceNoticeSaveService;
    @Autowired
    private InvoiceNoticeVoidService invoiceNoticeVoidService;

    /**
     * 合并开票通知
     *
     * @param user
     * @return ValueHolderV14
     */
    public ValueHolderV14 mergeInvoiceNotice(User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        vh.setCode(ResultCode.SUCCESS);
        try {
            //获取策略
            List<StCAutoInvoiceDO> stCAutoInvoiceList = stRpcService.queryAllAutoInvoice();
            if (CollectionUtils.isEmpty(stCAutoInvoiceList)) {
                return vh;
            }
            for (StCAutoInvoiceDO stCAutoInvoice : stCAutoInvoiceList) {
                if (!"Y".equals(stCAutoInvoice.getIsAutoMergeInvoice())) {
                    continue;
                }
                String[] cpCShopIds = stCAutoInvoice.getCpCShopId().split(",");
                for (String cpCShopId : cpCShopIds) {
                    //查询属于该店铺的所有开票通知
                    Map<String, List<OcBInvoiceNotice>> invoiceNoticeMap = getAllInvoiceNoticeMap(Long.valueOf(cpCShopId), stCAutoInvoice);
                    //获取合并后的订单
                    List<OcBInvoiceNoticeRelation> relationMergeList = buildInvoiceNotice(stCAutoInvoice, invoiceNoticeMap);

                    for (OcBInvoiceNoticeRelation relation : relationMergeList) {
                        if (CollectionUtils.isEmpty(relation.getInvoiceNoticeItems())) {
                            continue;
                        }
                        for (Long beforeId : relation.getMergeIdList()) {
                            invoiceNoticeVoidService.voidInvoicNoticeSingle(beforeId, user);
                        }
                        ValueHolder valueHolder = invoiceNoticeSaveService.insertInvoiceNoitce(user, relation, null);
                        if (!valueHolder.isOK()) {
                            String errMsg = "开票通知自动合并生成新单据失败："
                                    + valueHolder.getData().getOrDefault("message", "保存开票通知失败!").toString();
                            log.error(LogUtil.format(errMsg));
                        }
                    }
                }
            }
        } catch (Exception e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
            return vh;
        }
        return vh;
    }

    private Map<String, List<OcBInvoiceNotice>> getAllInvoiceNoticeMap(Long cpCShopId, StCAutoInvoiceDO stCAutoInvoice) {
        //获取单一店铺下所有未审核开票信息
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        whereKeys.put("ESTATUS", OcInvoiceStatusEnum.NOT_AUDITED.getVal());
        whereKeys.put("CP_C_SHOP_ID", cpCShopId);

        List<OcBInvoiceNotice> invoiceNoticeList = invoiceNoticeDealUtil.selectAllInvoiceNoticeByEs(whereKeys, filterKeys);

        //准备待合并的开票map
        List<String> mergeKeyList = Lists.newArrayList();

        String mergeKeyStr = stCAutoInvoice.getSameInvoiceInfoWord();
        if (StringUtils.isNotBlank(mergeKeyStr)) {
            String[] mergeKeys = mergeKeyStr.split(",");
            mergeKeyList = Lists.newArrayList(mergeKeys);
            if ("Y".equals(stCAutoInvoice.getIsSameSourceCode())) {
                mergeKeyList.add(stCAutoInvoice.getSameSourceCodeWord());
            }
        }
        List<OcBInvoiceNotice> keyMapList;
        Map<String, List<OcBInvoiceNotice>> invoiceNoticeMap = new HashMap<>();
        for (OcBInvoiceNotice invoiceNotice : invoiceNoticeList) {
            if (invoiceNotice.getCpCShopId() == null) {
                continue;
            }
            String key = invoiceNotice.getCpCShopId().toString();
            for (String mergeKey : mergeKeyList) {
                try {
                    Method method = OcBInvoiceNotice.class.getMethod("get" + mergeKey);
                    if (mergeKey.equals(stCAutoInvoice.getSameSourceCodeWord())) {
                        String arr = (String) method.invoke(invoiceNotice);
                        Set arrSet = new HashSet();
                        for (String a : arr.split(",")) {
                            arrSet.add(a);
                        }
                        if (arrSet.size() == 1) {
                            key = key + "," + mergeKey + ":" + Lists.newArrayList(arrSet).get(0);
                        } else {
                            key = key + "," + mergeKey + ":" + method.invoke(invoiceNotice);
                        }
                    } else {
                        key = key + "," + mergeKey + ":" + method.invoke(invoiceNotice);
                    }
                } catch (Exception ex) {
                    log.error(LogUtil.format("开票通知自动合并获取key: {}"), Throwables.getStackTraceAsString(ex));
                }
            }
            keyMapList = invoiceNoticeMap.containsKey(key) ? invoiceNoticeMap.get(key) : Lists.newArrayList();
            keyMapList.add(invoiceNotice);
            invoiceNoticeMap.put(key, keyMapList);
        }
        return invoiceNoticeMap;
    }

    private List<OcBInvoiceNoticeRelation> buildInvoiceNotice(StCAutoInvoiceDO stCAutoInvoice, Map<String, List<OcBInvoiceNotice>> invoiceNoticeMap) {
        BigDecimal amt;
        List<String> sourceCodeList;
        List<String> ocBOrderIdList;
        List<Integer> proIdList;
        OcBInvoiceNoticeRelation relationMerge;
        List<OcBInvoiceNoticeRelation> relationMergeList = Lists.newArrayList();
        Map<Long, PsCPro> proMap = new HashMap<>();
        //准备合并后的开票信息model
        for (String key : invoiceNoticeMap.keySet()) {
            if (invoiceNoticeMap.get(key).size() > 1) {
                relationMerge = new OcBInvoiceNoticeRelation();
                amt = BigDecimal.ZERO;
                sourceCodeList = Lists.newArrayList();
                ocBOrderIdList = Lists.newArrayList();
                proIdList = Lists.newArrayList();
                for (OcBInvoiceNotice invoiceNoticeSingle : invoiceNoticeMap.get(key)) {
                    amt = AmountCalcUtils.addBigDecimal(amt, invoiceNoticeSingle.getAmt());
                    if (StringUtils.isNotBlank(invoiceNoticeSingle.getSourceCode())) {
                        sourceCodeList.addAll(Lists.newArrayList(invoiceNoticeSingle.getSourceCode().split(",")));
                    }
                    if (StringUtils.isNotBlank(invoiceNoticeSingle.getOcBOrderId())) {
                        ocBOrderIdList.addAll(Lists.newArrayList(invoiceNoticeSingle.getOcBOrderId().split(",")));
                    }

                    if (("Y".equals(stCAutoInvoice.getIsLimitMaximumAmount()) && amt.compareTo(stCAutoInvoice.getMaximumAmount()) > 0)
                            || sourceCodeList.size() > 50) {
                        buildInvoiceNoticeItem(relationMerge, proIdList, proMap);
                        relationMergeList.add(relationMerge);
                        relationMerge = new OcBInvoiceNoticeRelation();
                        amt = invoiceNoticeSingle.getAmt();
                        sourceCodeList = Lists.newArrayList();
                        ocBOrderIdList = Lists.newArrayList();
                        if (StringUtils.isNotBlank(invoiceNoticeSingle.getSourceCode())) {
                            sourceCodeList.addAll(Lists.newArrayList(invoiceNoticeSingle.getSourceCode().split(",")));
                        }
                        if (StringUtils.isNotBlank(invoiceNoticeSingle.getOcBOrderId())) {
                            ocBOrderIdList.addAll(Lists.newArrayList(invoiceNoticeSingle.getOcBOrderId().split(",")));
                        }
                        proIdList = Lists.newArrayList();
                    }
                    if (CollectionUtils.isEmpty(relationMerge.getMergeIdList())) {
                        relationMerge.setMergeIdList(Lists.newArrayList());
                    }
                    relationMerge.getMergeIdList().add(invoiceNoticeSingle.getId());
                    relationMerge.setInvoiceNotice(invoiceNoticeSingle);
                    relationMerge.getInvoiceNotice().setAmt(amt);
                    relationMerge.getInvoiceNotice().setSourceCode(StringUtils.join(sourceCodeList, ","));
                    relationMerge.getInvoiceNotice().setOcBOrderId(StringUtils.join(ocBOrderIdList, ","));

                    //构造子表
                    if (CollectionUtils.isEmpty(relationMerge.getInvoiceNoticePros())) {
                        relationMerge.setInvoiceNoticePros(Lists.newArrayList());
                    }
                    relationMerge.getInvoiceNoticePros().addAll(proMapper.listByMainid(invoiceNoticeSingle.getId()));
                    for (OcBInvoiceNoticePro invoiceNoticePro : relationMerge.getInvoiceNoticePros()) {
                        invoiceNoticePro.setId(-1L);
                        if (!proIdList.contains(invoiceNoticePro.getPsCProId().intValue())
                                && !proMap.containsKey(invoiceNoticePro.getPsCProId())) {
                            proIdList.add(invoiceNoticePro.getPsCProId().intValue());
                        }
                    }
                    relationMerge.getInvoiceNotice().setId(-1L);
                }
                buildInvoiceNoticeItem(relationMerge, proIdList, proMap);
                relationMergeList.add(relationMerge);
            }
        }
        return relationMergeList;
    }

    private void buildInvoiceNoticeItem(OcBInvoiceNoticeRelation relationMerge, List<Integer> proIdList, Map<Long, PsCPro> proMap) {
        if (CollectionUtils.isNotEmpty(proIdList)) {
            List<PsCPro> psCProList = psRpcService.queryProByIds(proIdList);
            if (CollectionUtils.isNotEmpty(psCProList)) {
                proMap.putAll(psCProList.stream().collect(Collectors.toMap(PsCPro::getId, Function.identity())));
            }
        }
        //汇总明细信息
        if (CollectionUtils.isNotEmpty(relationMerge.getInvoiceNoticePros())) {
            Map<String, OcBInvoiceNoticeItemResult> itemMap = invoiceTableQueryService
                    .getInvoiceNoticeItemMap(relationMerge.getInvoiceNoticePros(), proMap);

            List<OcBInvoiceNoticeItem> itemList = Lists.newArrayList();
            for (OcBInvoiceNoticeItemResult itemResult : itemMap.values()) {
                OcBInvoiceNoticeItem item = new OcBInvoiceNoticeItem();
                BeanUtils.copyProperties(itemResult, item);
                itemList.add(item);
            }
            relationMerge.setInvoiceNoticeItems(itemList);
        }
    }
}
