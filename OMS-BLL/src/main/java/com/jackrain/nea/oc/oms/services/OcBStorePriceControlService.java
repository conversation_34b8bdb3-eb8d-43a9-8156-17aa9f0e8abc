package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBStorePriceControlMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBStorePriceControl;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 店铺价格管控新增保存/修改
 * @date 2022/1/17 18:50
 */
@Slf4j
@Service
public class OcBStorePriceControlService {

    @Autowired
    OcBStorePriceControlMapper ocBStorePriceControlMapper;

    @Autowired
    CpRpcService cpRpcService;

    @Autowired
    PsRpcService psRpcService;

    public ValueHolder save(QuerySession session) {

        User user  = session.getUser();
        ValueHolder valueHolder = CommandAdapterUtil.checkSaveSession(session, OcCommonConstant.OC_B_STORE_PRICE_CONTROL);

        if (!valueHolder.isOK()) {
            return valueHolder;
        }

        if (log.isDebugEnabled()) {
            log.debug("Start Sava OcBStorePriceControl valueHolder#{}", valueHolder.getData());
        }
        JSONObject fixColumn = (JSONObject)valueHolder.getData().get(OcCommonConstant.FIX_COLUMN);


        Long id = (Long)((HashMap)valueHolder.getData().get("data")).get(OcCommonConstant.OBJ_ID);

        if (fixColumn != null && id != null) {


            OcBStorePriceControl ocBStorePriceControl = ((JSONObject)fixColumn.get(OcCommonConstant.OC_B_STORE_PRICE_CONTROL)).toJavaObject(OcBStorePriceControl.class);
            // 判断请求参数
            AssertUtil.assertException(Objects.isNull(ocBStorePriceControl), "请求参数异常");

            if (log.isDebugEnabled()) {
            log.debug("Start Sava  ocBStorePriceControl#{}", ocBStorePriceControl);
            }
            // 单件折扣
            String disCount = null;
            // 前N单折扣
            String frontDiscount = null;
            // 判断新增还是修改
            if (id < 0){
                // 新增方法
                // 入参校验
                checkParam(ocBStorePriceControl);
                // 校验店铺是否存在
                Integer cpCShopId = ocBStorePriceControl.getCpCShopId();
                CpShop cpShop = cpRpcService.selectCpCShopById(Long.valueOf(cpCShopId));
                if(cpShop == null){
                    throw new NDSException("输入的店仓在店仓档案表中不存在");
                }

                // 校验sku是否存在
                String sku = ocBStorePriceControl.getSku();
                List<PsCPro> psCPros = psRpcService.queryProByIds(Collections.singletonList(Integer.parseInt(sku)));
                if (psCPros.isEmpty()){
                    throw new NDSException("输入的条码在商品档案表中不存在！");
                }


                /*String drpProBySku = psCPros.get(0).getDrpStyle();
                if (StringUtils.isEmpty(drpProBySku)){
                    throw new NDSException("输入的条码在商品款号表中不存在对应型号!");
                }
                ocBStorePriceControl.setPro(drpProBySku);*/
                // 每个sku的吊牌价是固定的，直接赋值
                if (Objects.isNull(psCPros.get(0).getPricelist())){
                    ocBStorePriceControl.setPriceList(null);
                }
                ocBStorePriceControl.setPriceList(psCPros.get(0).getPricelist());


                // 校验开始时间是否小于结束时间
                Date startTime = ocBStorePriceControl.getStartTime();
                Date endTime = ocBStorePriceControl.getEndTime();
                if (!startTime.before(endTime)){
                    throw new NDSException("开始时间不能大于截至时间！");
                }
                // 吊牌价
                BigDecimal priceList = ocBStorePriceControl.getPriceList();

                // 单件到手价 如果有值就计算单件折扣
                if (!Objects.isNull(ocBStorePriceControl.getActiveOrderPrice())){
                    // 设置折扣，单件到手价折扣=单件到手价/吊牌价
                     disCount = calculateDiscount(ocBStorePriceControl.getActiveOrderPrice(),priceList);
                }

                // 前N单到手价
                if (!Objects.isNull(ocBStorePriceControl.getFrontPrice())){
                    // 设置折扣，单件到手价折扣=单件到手价/吊牌价
                     frontDiscount = calculateDiscount(ocBStorePriceControl.getFrontPrice(),priceList);
                }
                ocBStorePriceControl.setDiscount(disCount);
                ocBStorePriceControl.setFrontDiscount(frontDiscount);

                // 取id值
                id = ModelUtil.getSequence(OcCommonConstant.OC_B_STORE_PRICE_CONTROL);
                ocBStorePriceControl.setId(id);
                // 系统参数
                ocBStorePriceControl.setOwnername(user.getName());
                ocBStorePriceControl.setAdOrgId((long) user.getOrgId());
                ocBStorePriceControl.setAdClientId((long) user.getClientId());
                ocBStorePriceControl.setOwnerid(Long.valueOf(user.getId()));
                ocBStorePriceControl.setOwnereName(user.getEname());
                ocBStorePriceControl.setCreationdate(new Date());
                ocBStorePriceControl.setModifierid(Long.valueOf(user.getId()));
                ocBStorePriceControl.setModifiername(user.getName());
                ocBStorePriceControl.setEname(user.getName());
                ocBStorePriceControl.setUploadTime(new Date());
                ocBStorePriceControl.setModifieddate(new Date());
                // 是否启用
                ocBStorePriceControl.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
                int insert = ocBStorePriceControlMapper.insert(ocBStorePriceControl);

                if (insert <= 0) {
                    throw new NDSException("新增店铺价格管控失败！");
                  }

            }else {
                // 修改
                OcBStorePriceControl storePriceControl = ocBStorePriceControlMapper.selectById(id);
                AssertUtil.assertException(Objects.isNull(storePriceControl), "当前记录已不存在");

                // 判断是否有修改开始日期  若有，则与截止日期比较
                if (ocBStorePriceControl.getStartTime() != null){
                    // 拿到当前截止日期
                    Date endTime = storePriceControl.getEndTime();
                    if (!ocBStorePriceControl.getStartTime().before(endTime)){
                    throw new NDSException("开始时间不能大于截至时间！");
                    }
                }

                // 吊牌价
                BigDecimal priceList = storePriceControl.getPriceList();

                // 单件到手价 如果有值就计算单件折扣
                if (!Objects.isNull(ocBStorePriceControl.getActiveOrderPrice())){
                    // 设置折扣，单件到手价折扣=单件到手价/吊牌价
                     disCount = calculateDiscount(ocBStorePriceControl.getActiveOrderPrice(),priceList);
                }

                // 前N单到手价
                if (!Objects.isNull(ocBStorePriceControl.getFrontPrice())){
                    // 设置折扣，单件到手价折扣=单件到手价/吊牌价
                     frontDiscount = calculateDiscount(ocBStorePriceControl.getFrontPrice(),priceList);
                }
                ocBStorePriceControl.setDiscount(disCount);
                ocBStorePriceControl.setFrontDiscount(frontDiscount);

                ocBStorePriceControl.setId(id);
                ocBStorePriceControl.setModifieddate(new Date());
                ocBStorePriceControl.setModifierid(Long.valueOf(user.getId()));
                ocBStorePriceControl.setModifiername(user.getName());
                int update = ocBStorePriceControlMapper.updateById(ocBStorePriceControl);
                if (update <= 0){
                    throw new NDSException("更新店铺价格管控失败！");
                }
            }

        valueHolder = ValueHolderUtils.getSuccessValueHolder(id,OcCommonConstant.OC_B_STORE_PRICE_CONTROL,"保存成功");
        }
        return valueHolder;
    }

    private void checkParam(OcBStorePriceControl ocBStorePriceControl) {

        if (ocBStorePriceControl.getStartTime() == null){
            throw new NDSException("开始日期不允许为空！！");
        }
        if (ocBStorePriceControl.getEndTime() == null){
            throw new NDSException("结束日期不允许为空！！");
        }
        if (ocBStorePriceControl.getSku() == null){
            throw new NDSException("条码不允许为空！！");
        }
        if (ocBStorePriceControl.getCpCShopId() == null){
            throw new NDSException("店铺不允许为空！！");
        }
    }

    /**
     * 计算折扣
     */
    private String calculateDiscount(BigDecimal divide,BigDecimal beDiveded){
        // 保留两位小数
        return String.valueOf(divide.divide(beDiveded,2, BigDecimal.ROUND_HALF_UP));
    }
}
