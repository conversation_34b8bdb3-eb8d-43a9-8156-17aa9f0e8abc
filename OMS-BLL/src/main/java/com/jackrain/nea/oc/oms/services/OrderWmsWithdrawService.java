package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillVoidResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.es.ES4TaoBaoRefund;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderWmsCancelNumber;
import com.jackrain.nea.oc.oms.model.enums.OcOrderWmsStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2019/6/12 11:40 PM
 * @Version 1.0
 */
@Component
@Slf4j
public class OrderWmsWithdrawService {

    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private IpBTaobaoRefundMapper ipBTaobaoRefundMapper;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private BeforeShipmentReturnService beforeShipmentReturnService;
    @Autowired
    private MarkRefundService markRefundService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    SgRpcService sgRpcService;

    public void OrderWmsWithdraw(List<OcBOrderParam> ocBOrderParams, User operateUser) {

        ocBOrderParams.forEach(p -> {
            OcBOrder ocBOrder = p.getOcBOrder();
            //调用反审核服务
            boolean lock = this.lockUpBackExamine(ocBOrder, operateUser);
            if (!lock) {
                return;
            }
        });
    }


    private void handleOrde(OcBOrderItem ocBOrderItem, OcBOrder ocBOrder, User user) {
        Integer refundStatus = ocBOrderItem.getRefundStatus();//明细的退款状态
        if (OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal() == refundStatus) {
            //获取ooid
            String refund_id = ES4TaoBaoRefund.findRefundIdByOid(ocBOrderItem.getOoid());

            IpBTaobaoRefund ipBTaobaoRefund = null;
            if (StringUtils.isNotBlank(refund_id)) {
                ipBTaobaoRefund = ipBTaobaoRefundMapper.selectTaobaoByRefundId(refund_id);
            }

            //获取中间表的退款状态
            if (ipBTaobaoRefund != null) {
                String status = ipBTaobaoRefund.getStatus();
                if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status)) {
                    OcBOrderItem item = new OcBOrderItem();
                    item.setId(ocBOrderItem.getId());
                    item.setOcBOrderId(ocBOrderItem.getOcBOrderId());
                    item.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());
                    omsOrderItemService.updateOcBOrderItem(item, item.getOcBOrderId());
                }

                if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status)) {
                    //调用标记退款服务
                    BigDecimal refundFee = ipBTaobaoRefund.getRefundFee();
                    ocBOrderItem.setRefundId(refund_id);
                    ocBOrderItem.setAmtRefund(refundFee);
                    beforeShipmentReturnService.markRefundIsFail(ocBOrder, ipBTaobaoRefund, ocBOrderItem, user);
                }
                if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
                    //调用标记退款服务
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("IDS", ocBOrderItem.getId());
                    //标记退款完成
                    try {
                        ValueHolderV14 holderV14 = markRefundService.markRefund(jsonObject, user);
                        int code1 = Tools.getInt(holderV14.getCode(), -1);
                        if (code1 == 0) {
                            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单退款明细标记退款完成成功", null, null, user);
                        } else {
                            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单退款明细标记退款完成失败", null, null, user);
                        }
                    } catch (Exception e) {
                        log.error(LogUtil.format("wms撤回自动任务,中间表状态为SUCCESS时失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.MARK_REFUND_COMPLETION.getKey(), "订单退款明细标记退款完成失败," + e.getMessage(), null, null, user);
                    }
                }
            }
        }
    }

    /**
     * 配货中加锁调用反审核
     *
     * @param ocBOrder    订单信息
     * @param operateUser 用户信息
     */
    private boolean lockUpBackExamine(OcBOrder ocBOrder, User operateUser) {
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                ValueHolderV14 holderV14 = new ValueHolderV14();
                boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(operateUser, holderV14, ocBOrder.getId(), LogTypeEnum.NOT_CAPTURED_SCENE.getType());
                if (isSuccess) {
                    //反审核成功  将订单状态改为 待审核 反审核方法有更新
                    return true;
                } else {
                    int number = Objects.isNull(ocBOrder.getWmsCancelNumber()) ? 0 : ocBOrder.getWmsCancelNumber();
                    if (ocBOrder.getWmsCancelStatus() == OcOrderWmsStatus.WMS_NONE.getVal() ||
                            number > OcOrderWmsCancelNumber.UNOVER.toInteger()) {
                        return false;
                    }
                    List<OcBOrder> orderList = new ArrayList<>();
                    orderList.add(ocBOrder);
                    ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> valueHolderV14 = sgRpcService.invoildOutgoingNoticeAndWms(orderList, operateUser);
                    // 作废出库通知单并传wms返回结果：{"code":0,"data":{"dataArr":[{"code":-1,"objid":7049770,"message":"订单已完成，无法取消！"}]},"message":"成功","oK":true}
                    List<SgBStoOutNoticesBillVoidResult> data = valueHolderV14.getData();
                    String message = "";
                    if (Objects.nonNull(data)) {
                        SgBStoOutNoticesBillVoidResult voidResult = data.get(0);
                        message = voidResult.getMessage();
                    }
                    this.updateWmsCancelNum(orderList.get(0), message.trim());
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("配货中调用反审核出错,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            this.updateWmsCancelNum(ocBOrder, ExceptionUtil.getMessage(e).trim());
        } finally {
            redisLock.unlock();
        }
        return false;
    }

    /**
     * 更新WMS撤回失败次数
     *
     * @param order   订单信息
     * @param message 错误消息
     */
    private void updateWmsCancelNum(OcBOrder order, String message) {
        int number = Objects.isNull(order.getWmsCancelNumber()) ? 0 : order.getWmsCancelNumber();
        if (order.getWmsCancelStatus() == OcOrderWmsStatus.WMS_NONE.getVal() || number > 5) {
            return;
        }

        CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        String allReason = strRedisTemplate.opsForValue().get("business_system:wms_recall_failure_reason");
        if (StringUtils.isBlank(allReason) && StringUtils.isBlank(message)) {
            orderMapper.updateWmsCancelNumberById(order.getId());
            return;
        }

        List<String> reasons = Arrays.stream(allReason.split("\\|")).map(String::trim).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reasons)) {
            boolean flag = false;
            for (String reason : reasons) {
                if (message.contains(reason)) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                orderMapper.updateWmsCancelNumber(order.getId(), OcOrderWmsStatus.WMS_NONE.getVal(),
                        OcOrderWmsCancelNumber.OVER.toInteger());
            } else {
                // 不包含wms撤回原因的，WmsCancelNumber次数+1
                orderMapper.updateWmsCancelNumberById(order.getId());
            }
        } else {
            // 不包含wms撤回原因的，WmsCancelNumber次数+1
            orderMapper.updateWmsCancelNumberById(order.getId());
        }
    }


}
