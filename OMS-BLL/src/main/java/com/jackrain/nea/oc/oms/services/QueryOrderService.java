package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.service.ESHandleService;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.request.OrderQueryRequest;
import com.jackrain.nea.oc.oms.model.result.EsQueryByPageResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderListResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderPlatResult;
import com.jackrain.nea.oc.oms.model.result.OcBOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.request.OcBOrderRequest;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ListUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.GZIPOutputStream;

/**
 * <AUTHOR>
 * @create 2020-07-09
 * @desc 订单查询
 **/
@Slf4j
@Component
public class QueryOrderService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private ESHandleService esHandleService;

    @Autowired
    private OmsOrderService orderService;

    @Autowired
    private CpRpcService cpRpcService;

    /**
     * gzip压缩
     *
     * @param gzipString
     * @return
     * @throws IOException
     */
    public static String gzipCompress(String gzipString) throws IOException {
        if (StringUtils.isBlank(gzipString)) {
            return gzipString;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzip = new GZIPOutputStream(out);
        gzip.write(gzipString.getBytes("UTF-8"));
        gzip.close();
        String zipString = out.toString("ISO-8859-1");
        out.close();
        return zipString;
    }

    public List<OcBOrder> queryOrderList(String sourceCode) {
        return ocBOrderMapper.getOrdersUnionGsiBySourceCode(sourceCode);
    }

    /**
     * 订单列表查询
     *
     * @param ocBOrderRequest 请求参数
     * @return
     */
    public ValueHolderV14 queryOrderList(OcBOrderRequest ocBOrderRequest) {
        ValueHolderV14<String> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, null);
        OcBOrderListResult ocBOrderListResult = getOrderList(ocBOrderRequest);
        String result = JSONObject.toJSONString(ocBOrderListResult, SerializerFeature.WriteMapNullValue);
        try {
            result = gzipCompress(result);
        } catch (IOException e) {
            log.error(LogUtil.format("订单查询压缩失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
        valueHolderV14.setData(result);
        return valueHolderV14;
    }

    /**
     * 获取订单列表
     *
     * @param ocBOrderRequest
     * @return
     */
    public OcBOrderListResult getOrderList(OcBOrderRequest ocBOrderRequest) {
        OcBOrderListResult ocBOrderListResult = new OcBOrderListResult();
        ocBOrderListResult.setPageNum(ocBOrderRequest.getPageNum());
        ocBOrderListResult.setPageSize(ocBOrderRequest.getPageSize());
        //ES查询参数
        OrderQueryRequest orderQueryRequest = new OrderQueryRequest();
        //ES查询结果
        EsQueryByPageResult esQueryByPageResult;
        conditionalConversion(ocBOrderRequest, orderQueryRequest);
        try {
            esQueryByPageResult = esHandleService.selectPrimaryKeyByES(
                    OcBOrderConst.TABLE_NAME, OcBOrderConst.TABLE_NAME,
                    (int) orderQueryRequest.getIndex(), orderQueryRequest.getSize(),
                    orderQueryRequest.getWhere(), orderQueryRequest.getFilter(),
                    orderQueryRequest.getField(), OcBOrder.class);
        } catch (Exception e) {
            log.error(LogUtil.format("订单列表查询，ES错误,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            return ocBOrderListResult;
        }

        ocBOrderListResult.setTotalNum(esQueryByPageResult.getTotalPage());
        ocBOrderListResult.setTotalSize(esQueryByPageResult.getTotal());

        if (null != esQueryByPageResult && CollectionUtils.isEmpty(esQueryByPageResult.getBillNoPrimaryList())) {
            return ocBOrderListResult;
        }
        List<Long> idList = esQueryByPageResult.getBillNoPrimaryList().stream().map(
                a -> Long.parseLong((String) a)
        ).distinct().collect(Collectors.toList());
        //订单查询
        List<OcBOrderResult> resultList = ocBOrderMapper.listOrder(idList);
        if (CollectionUtils.isEmpty(resultList)) {
            return ocBOrderListResult;
        }
        if (ocBOrderRequest.isNeedItem()) {
            //明细查询
            getOrderItem(esQueryByPageResult, resultList);
        }
        ocBOrderListResult.setResultList(resultList);
        return ocBOrderListResult;
    }

    /**
     * 订单列表查询
     *
     * @param ocBOrderRequest 请求参数
     * @return
     */
    public ValueHolderV14<List<OcBOrder>> queryOrderListByRequest(OcBOrderRequest ocBOrderRequest) {
        OcBOrderResult ocBOrderResult = new OcBOrderResult();
        ValueHolderV14<List<OcBOrder>> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, null);
        //ES查询参数
        OrderQueryRequest orderQueryRequest = new OrderQueryRequest();
        //ES查询结果
        EsQueryByPageResult esQueryByPageResult;
        conditionalConversion(ocBOrderRequest, orderQueryRequest);
        try {
            esQueryByPageResult = esHandleService.selectPrimaryKeyByES(
                    OcBOrderConst.TABLE_NAME, OcBOrderConst.TABLE_NAME,
                    (int) orderQueryRequest.getIndex(), orderQueryRequest.getSize(),
                    orderQueryRequest.getWhere(), orderQueryRequest.getFilter(),
                    orderQueryRequest.getField(), OcBOrder.class);
        } catch (Exception e) {
            log.error(LogUtil.format("订单查询，ES错误,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
            return valueHolderV14;
        }

        if (null == esQueryByPageResult || CollectionUtils.isEmpty(esQueryByPageResult.getBillNoPrimaryList())) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("订单查询，ES为空");
            return valueHolderV14;
        }

        List<Long> idList = esQueryByPageResult.getBillNoPrimaryList().stream().map(a -> Long.parseLong((String) a)).collect(Collectors.toList());

        //订单查询
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectBatchIds(idList);
        if (CollectionUtils.isEmpty(ocBOrders)) {
            return valueHolderV14;
        }
        valueHolderV14.setData(ocBOrders);
        return valueHolderV14;
    }

    /**
     * 获取对应零售订单的下单店铺、对应平台店铺档案的平台
     *
     * @param ocBOrderRequest 请求参数
     * @return
     */
    public ValueHolderV14 queryPlatform(OcBOrderRequest ocBOrderRequest) {
        ValueHolderV14<String> vh = new ValueHolderV14<>(ResultCode.SUCCESS, null);
        if (StringUtils.isEmpty(ocBOrderRequest.getBillNo())) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("订单号不能为空!"));
            return vh;
        }
        List<Long> orderList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("BILL_NO", ocBOrderRequest.getBillNo());
            //查询Es数据
            orderList = ES4Order.queryEsOrderList(whereKeys, 0, OcBOrderConst.PAGE_SIZE);
        } catch (Exception e) {
            log.error(LogUtil.format("ueryOrderService.queryPlatform获取店铺平台数据，ES查询错误,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
            return vh;
        }
        if (CollectionUtils.isNotEmpty(orderList)) {
            //订单查询
            List<OcBOrderPlatResult> list = ocBOrderMapper.listPlantOrder(orderList);
            if (CollectionUtils.isEmpty(list)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("查询订单信息为空", orderList));
                return vh;
            }
            if (list.size() > 0) {
                OcBOrderPlatResult ocBOrderResult = list.get(0);
                //RPC.查询平台
                if (null != ocBOrderResult && null != ocBOrderResult.getPlatform()) {
                    String platformName = cpRpcService.rpcQueryPlatformNameByCode(ocBOrderResult.getPlatform().toString());
                    if (StringUtils.isNotEmpty(platformName)) {
                        ocBOrderResult.setPlatformName(platformName);
                    }
                } else {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("查询平台信息为空", orderList));
                    return vh;
                }
                String result = JSONObject.toJSONString(ocBOrderResult, SerializerFeature.WriteMapNullValue);
                vh.setData(result);
            }
        }
        return vh;
    }

    /**
     * 订单明细查询
     *
     * @param esQueryByPageResult
     * @param resultList
     */
    private void getOrderItem(EsQueryByPageResult esQueryByPageResult, List<OcBOrderResult> resultList) {
        //订单明细查询
        QueryWrapper<OcBOrderItem> itemQueryWrapper = new QueryWrapper<>();
        itemQueryWrapper.in("OC_B_ORDER_ID", esQueryByPageResult.getBillNoPrimaryList());
        List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectList(itemQueryWrapper);
        Map<Long, List<OcBOrderItem>> OcBOrderItemMap = ocBOrderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
        resultList.forEach(ocBOrderResult -> {
            List<OcBOrderItem> ocBOrderItems = OcBOrderItemMap.get(ocBOrderResult.getId());
            if (CollectionUtils.isEmpty(ocBOrderItems)) {
                return;
            }
            ocBOrderResult.setItemList(ocBOrderItems);
        });
    }

    /**
     * 条件转换
     *
     * @param ocBOrderRequest   请求参数
     * @param orderQueryRequest ES查询参数
     */
    private void conditionalConversion(OcBOrderRequest ocBOrderRequest, OrderQueryRequest orderQueryRequest) {
        orderQueryRequest.setIndex(ocBOrderRequest.getPageNum());
        orderQueryRequest.setSize(ocBOrderRequest.getPageSize());
        orderQueryRequest.setField(new String[]{OcBOrderConst.DB_PARTITION_FIELD_NAME});
        JSONObject filterKeys = new JSONObject();
        JSONObject whereKeys = new JSONObject();
        //订单ID
        if (StringUtils.isNotEmpty(ocBOrderRequest.getOrderId())) {
            JSONArray orderIdArray = fetchWhereKey(ocBOrderRequest.getOrderId());
            whereKeys.put("ID", orderIdArray);
        }
        //单据编号
        if (StringUtils.isNotEmpty(ocBOrderRequest.getBillNo())) {
            JSONArray BillNoArray = fetchWhereKey(ocBOrderRequest.getBillNo());
            whereKeys.put("BILL_NO", BillNoArray);
        }
        //平台单号
        if (StringUtils.isNotEmpty(ocBOrderRequest.getSourceCode())) {
            JSONArray SourceCodeNoArray = ListUtil.permutationAll(ocBOrderRequest.getSourceCode());
            if (SourceCodeNoArray != null && SourceCodeNoArray.size() == 1) {
                whereKeys.put("SOURCE_CODE", "*" + ocBOrderRequest.getSourceCode() + "*");
            } else {
                whereKeys.put("SOURCE_CODE", SourceCodeNoArray);
            }
        }
        //合单-平台单号
        if (StringUtils.isNotEmpty(ocBOrderRequest.getMergeSourceCode())) {
            JSONArray mergeSourceCodeArray = ListUtil.permutationAll(ocBOrderRequest.getMergeSourceCode());
            if (mergeSourceCodeArray != null && mergeSourceCodeArray.size() == 1) {
                whereKeys.put("MERGE_SOURCE_CODE", "*" + ocBOrderRequest.getMergeSourceCode() + "*");
            } else {
                whereKeys.put("MERGE_SOURCE_CODE", mergeSourceCodeArray);
            }
        }
        //快递单号
        if (StringUtils.isNotEmpty(ocBOrderRequest.getExpresscode())) {
            JSONArray expresscodeArray = fetchWhereKey(ocBOrderRequest.getExpresscode());
            whereKeys.put("EXPRESSCODE", expresscodeArray);
        }
        //用户昵称
        if (StringUtils.isNotEmpty(ocBOrderRequest.getUserNick())) {
            JSONArray userNickArray = fetchWhereKey(ocBOrderRequest.getUserNick());
            whereKeys.put("USER_NICK", userNickArray);
        }
        //下单店铺
        if (StringUtils.isNotEmpty(ocBOrderRequest.getStoreName())) {
            JSONArray storeNameArray = fetchWhereKey(ocBOrderRequest.getStoreName());
            whereKeys.put("CP_C_SHOP_TITLE", storeNameArray);
        }
        //发货实体仓名称
        if (StringUtils.isNotEmpty(ocBOrderRequest.getDepotName())) {
            JSONArray depotJsonArray = fetchWhereKey(ocBOrderRequest.getDepotName());
            whereKeys.put("CP_C_PHY_WAREHOUSE_ENAME", depotJsonArray);
        }
        //物流公司名称
        if (StringUtils.isNotEmpty(ocBOrderRequest.getShipName())) {
            JSONArray shipNameJsonArray = fetchWhereKey(ocBOrderRequest.getShipName());
            whereKeys.put("CP_C_LOGISTICS_ENAME", shipNameJsonArray);
        }
        //订单状态
        if (ocBOrderRequest.getOrderStatus() != null) {
            whereKeys.put("ORDER_STATUS", ocBOrderRequest.getOrderStatus());
        } else {
            //排除作废单据  订单“系统作废”状态8
            whereKeys.put("ORDER_STATUS", "!=" + OmsOrderStatus.SYS_VOID.toInteger());
        }
        //订单类型
        if (ocBOrderRequest.getOrderType() != null) {
            whereKeys.put("ORDER_TYPE", ocBOrderRequest.getOrderType());
        }
        //是否复制单
        if (ocBOrderRequest.getIsCopyOrder() != null) {
            whereKeys.put("IS_COPY_ORDER", ocBOrderRequest.getIsCopyOrder());
        }
        //是否拆分订单
        if (ocBOrderRequest.getIsSplit() != null) {
            whereKeys.put("IS_SPLIT", ocBOrderRequest.getIsSplit());
        }
        //是否换货未入库
        if (ocBOrderRequest.getReserveBigint03() != null) {
            whereKeys.put("RESERVE_BIGINT03", ocBOrderRequest.getReserveBigint03());
        }
        //@20200915  排除历史单据,是否为历史单据/ 防止空取不到数据
        whereKeys.put("IS_HISTORY", "!=" + 'Y');
        orderQueryRequest.setFilter(filterKeys);
        orderQueryRequest.setWhere(whereKeys);
    }

    /**
     * 字符串查询条件
     *
     * @param condition ,分割字符串条件
     * @return
     */
    private JSONArray fetchWhereKey(String condition) {
        String[] conditions = condition.split(",");
        JSONArray jsonArray = new JSONArray();
        for (String value : conditions) {
            if (value == null) {
                continue;
            }
            String trim = value.trim();
            if (trim.length() < 1) {
                continue;
            }
            jsonArray.add(trim);
        }
        return jsonArray;
    }
}
