package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.OmsStorageUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/7 20:03
 * @desc
 */
@Service
@Slf4j
public class OmsReplaceProductService {

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsConstituteSplitService constituteSplitService;

    @Autowired
    private BatchOperationGoodsService batchOperationGoodsService;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @NacosValue(value = "${r3.order.zp.sale:'10800,10801,10802'}", autoRefreshed = true)
    private String sale;

    //组合商品标识前缀
    private static final String GROUP_GOODS_MARK = "CG";

    /**
     * 正常商品替换正常商品
     *
     * @param ocBOrder
     */
    @Transactional(rollbackFor = Exception.class)
    public void replacePro(OcBOrder ocBOrder, OcBOrderItem item, ProductSku productSku, User user,BigDecimal changeQty) {
        // 复制替换之前的商品,重新占单的时候使用,释放相关占用
        OcBOrderItem ocBOrderItem = new OcBOrderItem();
        BeanUtils.copyProperties(item, ocBOrderItem);
        item.setPsCProId(productSku.getProdId());
        // ProECode
        item.setPsCProEcode(productSku.getProdCode());
        item.setPsCSkuId(productSku.getId());
        item.setSex(productSku.getSex());
        //2019-08-30吊牌价改为取商品表数据
        item.setPriceTag(productSku.getPricelist());
        item.setPriceList(productSku.getPricelist());
        item.setPsCClrEcode(productSku.getColorCode());
        item.setPsCClrEname(productSku.getColorName());
        item.setPsCClrId(productSku.getColorId());
        item.setPsCSizeEcode(productSku.getSizeCode());
        item.setPsCSizeEname(productSku.getSizeName());
        item.setPsCSizeId(productSku.getSizeId());
        item.setPsCProMaterieltype(productSku.getMaterialType());
        item.setStandardWeight(productSku.getWeight());
        item.setSkuSpec(productSku.getSkuSpec());
        //item.setPsCSkuPtEcode(productSku.getProdCode());
        item.setPsCSkuEcode(productSku.getSkuEcode());
        item.setBarcode(productSku.getBarcode69());
        item.setExpiryDateRange("");
        item.setOrderLabel("");
        if (item.getOriginSkuQty() == null) {
            item.setOriginSkuQty(item.getQty());
        }

        if (!Objects.isNull(item.getPriceActual())) {
            item.setPriceActual(item.getPriceActual().multiply(item.getQty()).divide(changeQty, 4, BigDecimal.ROUND_HALF_UP));
        }
        item.setPrice(item.getPrice().multiply(item.getQty()).divide(changeQty, 4, BigDecimal.ROUND_HALF_UP));

        item.setQty(changeQty);

        //替换商品后的商品无法指定效期问题修改
        if ("Y".equals(productSku.getIsEnableExpiry())) {
            item.setIsEnableExpiry(1);
        } else {
            item.setIsEnableExpiry(0);
        }

        item.setMDim4Id(productSku.getMDim4Id());
        item.setMDim6Id(productSku.getMDim6Id());

        if (item.getExpiryDateType() != null) {
            item.setExpiryDateType(0);
        }
        if (StringUtils.isEmpty(item.getBarcode())) {
            ocBOrderItemMapper.updateBarcodeById(item.getId());
        }
        //商品名称
        item.setPsCProEname(productSku.getName());
        // 替换前条码编码 只记录第一次替换
        if (StringUtils.isBlank(ocBOrderItem.getOriginSkuEcode()) &&
                !ocBOrderItem.getPsCSkuEcode().equals(productSku.getSkuEcode())) {
            item.setOriginSkuEcode(ocBOrderItem.getPsCSkuEcode());
        }
        omsOrderItemService.updateOcBOrderItem(item, ocBOrder.getId());
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.GOODS_REPLACE.getKey(), "将商品条码:" + ocBOrderItem.getPsCSkuEcode()
                        + "替换为" + productSku.getSkuEcode() + "成功:",
                null, null, user);
        batchOperationGoodsService.handleOccupy(ocBOrder, Lists.newArrayList(ocBOrderItem), user);
    }


    /**
     * 组合品替换为正常品
     *
     * @param ocBOrder   原单
     * @param item       原明细
     * @param productSku 替换之后的正常商品
     * @param user       操作人
     * @return
     */
    public ValueHolderV14 replaceGroup2Normal(OcBOrder ocBOrder, OcBOrderItem item, ProductSku productSku, User user, BigDecimal changeQty) {
        // 获取传入的参数
        ValueHolderV14 holderV14 = new ValueHolderV14();
        if (StringUtils.isBlank(item.getGroupGoodsMark())) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("替换组合商品失败!groupGoodsMark字段为空");
            return holderV14;
        }

        // 查出当前订单下已拆分完的数据 后面备用
        List<OcBOrderItem> willDeletes = ocBOrderItemMapper.selectOrderIteGroupMark(ocBOrder.getId()
                , item.getGroupGoodsMark());
        //  处理明细
        this.handleGroup2NormalItems(ocBOrder, item, willDeletes, productSku, user, changeQty);
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setMessage("替换商品成功!");
        return holderV14;
    }


    /**
     * 组合品替换为组合品
     *
     * @param ocBOrder   原单
     * @param item       原明细
     * @param productSku 替换之后的组合品
     * @param user       操作人
     * @return
     */
    public ValueHolderV14 replaceGroup2Group(OcBOrder ocBOrder, OcBOrderItem item, ProductSku productSku, User user, BigDecimal changeQty) {
        // 获取传入的参数
        ValueHolderV14 holderV14 = new ValueHolderV14();
        if (StringUtils.isBlank(item.getGroupGoodsMark())) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("替换组合商品失败!groupGoodsMark字段为空");
            return holderV14;
        }
        // 查出当前订单下已拆分完的数据 后面备用
        List<OcBOrderItem> willDeletes = ocBOrderItemMapper.selectOrderIteGroupMark(ocBOrder.getId(), item.getGroupGoodsMark());
        //  处理明细
        this.handleGroup2GroupOcBOrderItems(ocBOrder, item, willDeletes, productSku, user,changeQty);
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setMessage("替换组合商品成功!");
        return holderV14;
    }

    /**
     * 正常品替换为组合品
     *
     * @param ocBOrder   原单
     * @param item       原明细
     * @param productSku 替换之后的正常商品
     * @param user       操作人
     * @return
     */
    public ValueHolderV14 replaceNormal2Group(OcBOrder ocBOrder, OcBOrderItem item, ProductSku productSku, User user,BigDecimal changeQty) {
        // 获取传入的参数
        ValueHolderV14 holderV14 = new ValueHolderV14();
        //  处理明细
        this.handleNormal2GroupItems(ocBOrder, item, productSku, user,changeQty);
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setMessage("替换组合商品成功!");
        return holderV14;
    }


    /**
     * 组合品替换为正常品
     *
     * @param ocBOrder    原单
     * @param item        原明细
     * @param willDeletes 要删除的组合品明细
     * @param productSku  替换之后的正常商品
     * @param user        操作人
     * @return
     */
    public void handleGroup2NormalItems(OcBOrder ocBOrder, OcBOrderItem item,
                                        List<OcBOrderItem> willDeletes, ProductSku productSku, User user, BigDecimal changeQty) {

        if (productSku == null) {
            throw new NDSException("所选替换商品不存在!请重新选择!");
        }
        // 没更改的数据
        OcBOrderItem bOrderItem = new OcBOrderItem();
        BeanUtils.copyProperties(item, bOrderItem);
        // 组合商品换为正常商品
        this.merge(item, productSku);
        item.setProType((long) SkuType.NORMAL_PRODUCT);
        item.setGiftbagSku("");
        item.setGroupGoodsMark("");
        if (item.getOriginSkuQty() == null) {
            item.setOriginSkuQty(item.getQty());
        }
        item.setQty(changeQty);
        item.setPriceActual(item.getRealAmt().divide(item.getQty(), 4, BigDecimal.ROUND_HALF_UP));

        //平台售价重新计算
        BigDecimal priceAmt = Optional.ofNullable(item.getRealAmt()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(item.getAdjustAmt()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO))
                .divide(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ONE), 4, BigDecimal.ROUND_HALF_UP);
        item.setPrice(priceAmt);

        /*// 删除之前的组合商品(释放)
        willDeletes.add(bOrderItem);*/
        item.setExpiryDateRange("");
        item.setOrderLabel("");
        if (item.getExpiryDateType() != null) {
            item.setExpiryDateType(0);
        }
        this.handleGroup2NormalData(ocBOrder, item, willDeletes, bOrderItem, user);
    }


    /**
     * 正常品替换为组合品
     *
     * @param ocBOrder   原单
     * @param item       原明细
     * @param productSku 替换之后的组合商品
     * @param user       操作人
     * @return
     */
    public void handleNormal2GroupItems(OcBOrder ocBOrder, OcBOrderItem item, ProductSku productSku, User user,BigDecimal changeQty) {
        if (productSku == null) {
            throw new NDSException("所选替换商品不存在!请重新选择!");
        }
        // 没更改的数据
        OcBOrderItem bOrderItem = new OcBOrderItem();
        BeanUtils.copyProperties(item, bOrderItem);
        this.merge(item, productSku);
        // 正常品替换为组合品
        if (item.getOriginSkuQty() == null) {
            item.setOriginSkuQty(item.getQty());
        }
        item.setQty(changeQty);
        item.setPriceActual(item.getRealAmt().divide(item.getQty(), 4, BigDecimal.ROUND_HALF_UP));
        item.setProType((long) SkuType.NO_SPLIT_COMBINE);
        item.setGroupGoodsMark(GROUP_GOODS_MARK + item.getId());
        item.setGiftbagSku(productSku.getSkuEcode());
        List<OcBOrderItem> items = constituteSplitService.encapsulationParameter(Lists.newArrayList(item), ocBOrder, user, 0);
        this.handle2GroupData(ocBOrder, item, items, null, bOrderItem, user);
    }


    /**
     * 组合品替换为正常品
     *
     * @param ocBOrder    原单
     * @param item        原明细
     * @param willDeletes 要删除的
     * @param productSku  替换之后的正常商品
     * @param user        操作人
     * @return
     */
    public void handleGroup2GroupOcBOrderItems(OcBOrder ocBOrder, OcBOrderItem item,
                                               List<OcBOrderItem> willDeletes, ProductSku productSku, User user,BigDecimal changeQty) {

        if (productSku == null) {
            throw new NDSException("所选替换商品不存在!请重新选择!");
        }
        // 没更改的数据
        OcBOrderItem bOrderItem = new OcBOrderItem();
        BeanUtils.copyProperties(item, bOrderItem);

        // 替换为组合商品
        this.merge(item, productSku);
        if (item.getOriginSkuQty() == null) {
            item.setOriginSkuQty(item.getQty());
        }
        item.setQty(changeQty);
        item.setPriceActual(item.getRealAmt().divide(item.getQty(), 4, BigDecimal.ROUND_HALF_UP));

        // 组合替换成组合
        item.setQtyLost(BigDecimal.ZERO);
        item.setProType((long) SkuType.NO_SPLIT_COMBINE);
        item.setGroupGoodsMark(GROUP_GOODS_MARK + item.getId());
        item.setGiftbagSku(productSku.getSkuEcode());
        List<OcBOrderItem> items = constituteSplitService.encapsulationParameter(Lists.newArrayList(item), ocBOrder, user, 0);
        this.handle2GroupData(ocBOrder, item, items, willDeletes, bOrderItem, user);
    }


    /**
     * 主要是处理:组合替换为正常
     *
     * @param ocBOrder    订单主表
     * @param item        需要修改的明细
     * @param willDeletes 需要删除的数据
     * @param originItem  原明细
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleGroup2NormalData(OcBOrder ocBOrder, OcBOrderItem item, List<OcBOrderItem> willDeletes,
                                       OcBOrderItem originItem, User user) {
        Long id = ocBOrder.getId();
        if (CollectionUtils.isNotEmpty(willDeletes)) {
            List<Long> itemIds = willDeletes.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            if (!itemIds.isEmpty()) {
                String itemStr = StringUtils.join(itemIds, ",");
                ocBOrderItemMapper.deleteByItemIds(itemStr, id);
            }
        }
        //item.setPsCSkuPtEcode(null);
        // 替换前条码编码 只记录第一次替换
        if (StringUtils.isBlank(originItem.getOriginSkuEcode()) &&
                !originItem.getPsCSkuEcode().equals(item.getPsCSkuEcode())) {
            item.setOriginSkuEcode(originItem.getPsCSkuEcode());
        }
        omsOrderItemService.updateOcBOrderItem(item, ocBOrder.getId());
        // 添加log
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.GOODS_REPLACE.getKey(), "将商品条码:" + originItem.getPsCSkuEcode()
                        + "替换为" + item.getPsCSkuEcode() + "成功:", null, null, user);
        BatchOperationGoodsService bean = ApplicationContextHandle.getBean(BatchOperationGoodsService.class);
        bean.handleOccupy(ocBOrder, willDeletes, user);
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        List<OcBOrderItem> ocBOrderItems1 = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        List<OcBOrderItem> collect = ocBOrderItems1.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            order.setIsCombination(1);
        } else {
            order.setIsCombination(0);
        }
        omsOrderService.updateOrderInfo(order);
    }

    /**
     * 组合替换组合
     *
     * @param ocBOrder    订单主表
     * @param item        替换之后的未拆分的组合商品信息
     * @param items       需要修改的明细
     * @param willDeletes 需要删除的数据
     * @param originItem  原明细
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    public void handle2GroupData(OcBOrder ocBOrder, OcBOrderItem item, List<OcBOrderItem> items, List<OcBOrderItem> willDeletes,
                                 OcBOrderItem originItem, User user) {
        items.add(item);
        Long id = ocBOrder.getId();
        if (CollectionUtils.isNotEmpty(willDeletes)) {
            List<Long> itemIds = willDeletes.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            if (!itemIds.isEmpty()) {
                String itemStr = StringUtils.join(itemIds, ",");
                ocBOrderItemMapper.deleteByItemIds(itemStr, id);
            }
        } else {
            willDeletes = Lists.newArrayListWithExpectedSize(1);
            // 正常替换为组合,要删除的是空的,所以把原明细加进来,做下面的释放操作
            willDeletes.add(originItem);
        }
        // 替换前条码编码 只记录第一次替换
        if (StringUtils.isBlank(originItem.getOriginSkuEcode()) &&
                !originItem.getPsCSkuEcode().equals(item.getPsCSkuEcode())) {
            item.setOriginSkuEcode(originItem.getPsCSkuEcode());
        }
        for (OcBOrderItem current : items) {
            /*需要修改的明细： 未拆的组合  或  正常品*/
            if (current.getProType() == SkuType.NO_SPLIT_COMBINE || current.getProType() == SkuType.NORMAL_PRODUCT) {
                omsOrderItemService.updateOcBOrderItem(current, ocBOrder.getId());
            } else {
                ocBOrderItemMapper.insert(current);
            }
        }
        // 添加log
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.GOODS_REPLACE.getKey(), "将商品条码:" + originItem.getPsCSkuEcode()
                        + "替换为" + item.getPsCSkuEcode() + "成功:", null, null, user);
        BatchOperationGoodsService bean = ApplicationContextHandle.getBean(BatchOperationGoodsService.class);
        bean.handleOccupy(ocBOrder, willDeletes, user);
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        List<OcBOrderItem> ocBOrderItems1 = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        List<OcBOrderItem> collect = ocBOrderItems1.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            order.setIsCombination(1);
        } else {
            order.setIsCombination(0);
        }
        omsOrderService.updateOrderInfo(order);
    }


    /**
     * 修改原明细
     *
     * @param item
     * @param productSku
     */
    private void merge(OcBOrderItem item, ProductSku productSku) {
        //正常商品换组合商品
        item.setId(item.getId());
        item.setPsCSkuEcode(productSku.getSkuEcode());
        item.setQty(item.getQty());
        item.setBarcode(productSku.getBarcode69());
        item.setPsCProId(productSku.getProdId());
        // ProECode
        item.setPsCProEname(productSku.getName());
        item.setPsCSkuEname(productSku.getSkuName());
        item.setPsCProEcode(productSku.getProdCode());
        item.setPsCSkuId(productSku.getId());
        item.setSex(productSku.getSex());
        item.setPriceTag(productSku.getPricelist());
        item.setPsCClrEcode(productSku.getColorCode());
        item.setPsCClrEname(productSku.getColorName());
        item.setPsCClrId(productSku.getColorId());
        item.setPsCSizeEcode(productSku.getSizeCode());
        item.setPsCSizeEname(productSku.getSizeName());
        item.setPsCSizeId(productSku.getSizeId());
        item.setPsCProMaterieltype(productSku.getMaterialType());
        item.setStandardWeight(productSku.getWeight());
        item.setSkuSpec(productSku.getSkuSpec());
        item.setOcBOrderId(item.getOcBOrderId());
        item.setPriceTag(productSku.getPricelist());
        item.setIsManualAdd("1");
        item.setBarcode(productSku.getBarcode69());
        item.setMDim4Id(productSku.getMDim4Id());
        item.setMDim6Id(productSku.getMDim6Id());
        //替换商品后的商品无法指定效期问题修改
        if ("Y".equals(productSku.getIsEnableExpiry())) {
            item.setIsEnableExpiry(1);
        } else {
            item.setIsEnableExpiry(0);
        }
        if (StringUtils.isEmpty(item.getBarcode())) {
            ocBOrderItemMapper.updateBarcodeById(item.getId());
        }
    }

    /**
     * 未拆分的组合替换未拆分的组合
     * 参照 {@link OmsReplaceProductService#replaceGroup2Group(OcBOrder, OcBOrderItem, ProductSku, User, BigDecimal)}
     *
     * @param ocBOrder   订单信息
     * @param item       原订单明细
     * @param productSku 替换后的商品
     * @param user       当前用户
     * @param changeQty  替换后的数量
     */
    public ValueHolderV14 replaceNoSplitGroup2NoSplitGroup(OcBOrder ocBOrder, OcBOrderItem item,
                                                           ProductSku productSku, User user, BigDecimal changeQty) {
        // 获取传入的参数
        ValueHolderV14 holderV14 = new ValueHolderV14();
        if (StringUtils.isBlank(item.getGroupGoodsMark())) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("替换组合商品失败!groupGoodsMark字段为空");
            return holderV14;
        }
        // 查出当前订单下已拆分完的数据 后面备用（原来的组合品明细数据，拆分前+后）
        List<OcBOrderItem> willDeletes = ocBOrderItemMapper.selectOrderIteGroupMark(ocBOrder.getId(), item.getGroupGoodsMark());
        log.info(LogUtil.format("未拆分的组合替换未拆分的组合,订单ID:{},原品明细:{},原品信息列表(替换成功会被删除):{}",
                        "OmsReplaceProductService.replaceNormal2ManyNormal"),
                ocBOrder.getId(), JSON.toJSONString(item), JSON.toJSONString(willDeletes));

        //  处理明细
        if (productSku == null) {
            throw new NDSException("所选替换商品不存在!请重新选择!");
        }
        // 没更改的数据
        OcBOrderItem bOrderItem = new OcBOrderItem();
        BeanUtils.copyProperties(item, bOrderItem);

        // 替换为组合商品
        this.merge(item, productSku);
        if (item.getOriginSkuQty() == null) {
            item.setOriginSkuQty(item.getQty());
        }
        item.setQty(changeQty);
        /*金额均继承自原明细，不做处理*/

        // 组合替换成组合
        item.setQtyLost(BigDecimal.ZERO);
        item.setProType((long) SkuType.NO_SPLIT_COMBINE);
        item.setGroupGoodsMark(GROUP_GOODS_MARK + item.getId());
        item.setGiftbagSku(productSku.getSkuEcode());
        List<OcBOrderItem> items = constituteSplitService.encapsulationParameter(Lists.newArrayList(item), ocBOrder, user, 1);
        this.handle2GroupData(ocBOrder, item, items, willDeletes, bOrderItem, user);

        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setMessage("替换组合商品成功!");
        return holderV14;
    }

    /**
     * 拆分后组合品的单品替换正常品
     * 参照 {@link OmsReplaceProductService#replacePro(OcBOrder, OcBOrderItem, ProductSku, User, BigDecimal)}
     *
     * @param ocBOrder   订单信息
     * @param item       原订单明细
     * @param productSku 替换后的商品
     * @param user       当前用户
     * @param changeQty  替换后的数量
     */
    public void replaceSplitGroup2Normal(OcBOrder ocBOrder, OcBOrderItem item, ProductSku productSku, User user, BigDecimal changeQty) {
        // 复制替换之前的商品,重新占单的时候使用,释放相关占用
        OcBOrderItem ocBOrderItem = new OcBOrderItem();
        BeanUtils.copyProperties(item, ocBOrderItem);

        item.setPsCProId(productSku.getProdId());
        // ProECode
        item.setPsCProEcode(productSku.getProdCode());
        item.setPsCSkuId(productSku.getId());
        item.setSex(productSku.getSex());
        //2019-08-30吊牌价改为取商品表数据
        item.setPriceTag(productSku.getPricelist());
        item.setPriceList(productSku.getPricelist());
        item.setPsCClrEcode(productSku.getColorCode());
        item.setPsCClrEname(productSku.getColorName());
        item.setPsCClrId(productSku.getColorId());
        item.setPsCSizeEcode(productSku.getSizeCode());
        item.setPsCSizeEname(productSku.getSizeName());
        item.setPsCSizeId(productSku.getSizeId());
        item.setPsCProMaterieltype(productSku.getMaterialType());
        item.setStandardWeight(productSku.getWeight());
        item.setSkuSpec(productSku.getSkuSpec());
        //item.setPsCSkuPtEcode(productSku.getProdCode());
        item.setPsCSkuEcode(productSku.getSkuEcode());
        item.setBarcode(productSku.getBarcode69());
        item.setExpiryDateRange("");
        item.setOrderLabel("");
        if (item.getOriginSkuQty() == null) {
            item.setOriginSkuQty(item.getQty());
        }
        
        /*成交单价：成交金额/Z ：这里直接用原来的价格，重新分摊到新的明细*/
        if (!Objects.isNull(item.getPriceActual())) {
            item.setPriceActual(item.getPriceActual().multiply(item.getQty()).divide(changeQty, 4, RoundingMode.HALF_UP));
        }
        /*平台售价：（成交金额+优惠金额+平摊金额）/Z ：这里直接用原来的价格，重新分摊到新的明细*/
        item.setPrice(item.getPrice().multiply(item.getQty()).divide(changeQty, 4, RoundingMode.HALF_UP));
        /*优惠金额、平摊金额、成交金额：继承自原来的明细，所以不变动*/

        /*替换数量*/
        item.setQty(changeQty);

        //替换商品后的商品无法指定效期问题修改
        if ("Y".equals(productSku.getIsEnableExpiry())) {
            item.setIsEnableExpiry(1);
        } else {
            item.setIsEnableExpiry(0);
        }

        item.setMDim4Id(productSku.getMDim4Id());
        item.setMDim6Id(productSku.getMDim6Id());

        if (item.getExpiryDateType() != null) {
            item.setExpiryDateType(0);
        }
        if (StringUtils.isEmpty(item.getBarcode())) {
            ocBOrderItemMapper.updateBarcodeById(item.getId());
        }
        //商品名称
        item.setPsCProEname(productSku.getName());
        // 替换前条码编码 只记录第一次替换
        if (StringUtils.isBlank(ocBOrderItem.getOriginSkuEcode()) &&
                !ocBOrderItem.getPsCSkuEcode().equals(productSku.getSkuEcode())) {
            item.setOriginSkuEcode(ocBOrderItem.getPsCSkuEcode());
        }

        omsOrderItemService.updateOcBOrderItem(item, ocBOrder.getId());
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.GOODS_REPLACE.getKey(), "将商品条码:" + ocBOrderItem.getPsCSkuEcode()
                        + "替换为" + productSku.getSkuEcode() + "成功:",
                null, null, user);
        batchOperationGoodsService.handleOccupy(ocBOrder, Lists.newArrayList(ocBOrderItem), user);
    }


    /**
     * 正常商品替换多个正常商品
     *
     * @param ocBOrder
     */
    @Transactional(rollbackFor = Exception.class)
    public void replaceNormal2ManyNormal(OcBOrder ocBOrder, OcBOrderItem item, Map<String, ProductSku> skuProductMap,
                                         User user, Map<String, BigDecimal> skuQtyMap, boolean equalProportion) {
        log.info(LogUtil.format("正常商品替换多个正常商品,订单ID:{},原品信息(替换成功会被删除):{}",
                "OmsReplaceProductService.replaceNormal2ManyNormal"), ocBOrder.getId(), JSON.toJSONString(item));

        // 复制替换之前的商品,重新占单的时候使用,释放相关占用
        OcBOrderItem ocBOrderItem = new OcBOrderItem();
        BeanUtils.copyProperties(item, ocBOrderItem);

        /*查询处理-标准销售价*/
        List<PsCPro> proList = psRpcService.queryProByEcode(new ArrayList<>(skuQtyMap.keySet()));
        Map<String, BigDecimal> salePriceMap = ListUtils.emptyIfNull(proList).stream()
                .peek(o -> o.setPriceSaleList(Optional.ofNullable(o.getPriceSaleList()).orElse(BigDecimal.ZERO)))
                .collect(Collectors.toMap(PsCPro::getEcode, PsCPro::getPriceSaleList, (a, b) -> a));
        BigDecimal totalSalePrice = MapUtil.emptyIfNull(salePriceMap).values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        /*替换后的总的商品标准销售价之和是否为0*/
        boolean productPriceZero = totalSalePrice.compareTo(BigDecimal.ZERO) == 0;
        /*剩余的：成交单价、平台售价、优惠金额、平摊金额*/
        /*成交单价 = 原商品成交单价 * 当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和*/
        /*平台售价 = 原商品平台售价 * 当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和*/
        /*优惠金额 = 原商品优惠金额*当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和*/
        /*平摊金额 = 原商品平摊金额*当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和*/
        BigDecimal priceActual = item.getPriceActual();
        BigDecimal price = item.getPrice();
        BigDecimal amtDiscount = item.getAmtDiscount();
        BigDecimal orderSplitAmt = item.getOrderSplitAmt();

        int idx = 1;
        int size = skuQtyMap.size();
        List<OcBOrderItem> newItemList = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> skuQtyEntry : skuQtyMap.entrySet()) {
            BigDecimal changeQty = skuQtyEntry.getValue();
            /*如果是等比替换，等比只有1:1，所以直接继承原来的数量即可*/
            if (equalProportion) {
                changeQty = item.getQty();
            }

            OcBOrderItem newItem = new OcBOrderItem();
            BeanUtils.copyProperties(item, newItem);

            ProductSku productSku = skuProductMap.get(skuQtyEntry.getKey());
            newItem.setPsCProId(productSku.getProdId());
            newItem.setPsCProEname(productSku.getName());
            newItem.setPsCProEcode(productSku.getProdCode());
            newItem.setPsCSkuId(productSku.getId());
            newItem.setSex(productSku.getSex());
            newItem.setPsCClrEcode(productSku.getColorCode());
            newItem.setPsCClrEname(productSku.getColorName());
            newItem.setPsCClrId(productSku.getColorId());
            newItem.setPsCSizeEcode(productSku.getSizeCode());
            newItem.setPsCSizeEname(productSku.getSizeName());
            newItem.setPsCSizeId(productSku.getSizeId());
            newItem.setPsCProMaterieltype(productSku.getMaterialType());
            newItem.setStandardWeight(productSku.getWeight());
            newItem.setSkuSpec(productSku.getSkuSpec());
            newItem.setPsCSkuEcode(productSku.getSkuEcode());
            newItem.setBarcode(productSku.getBarcode69());
            newItem.setExpiryDateRange("");
            newItem.setOrderLabel("");
            if (newItem.getOriginSkuQty() == null) {
                newItem.setOriginSkuQty(newItem.getQty());
            }

            /*数量*/
            newItem.setQty(changeQty);

            //2019-08-30吊牌价改为取商品表数据
            /*吊牌价*/
            newItem.setPriceTag(productSku.getPricelist());
            /*标准价*/
            newItem.setPriceList(productSku.getPricelist());
            // newItem.setPsCSkuPtEcode(productSku.getProdCode());
            /*单件实际成交价*/
//            if (!Objects.isNull(item.getPriceActual())) {
//                newItem.setPriceActual(item.getPriceActual().multiply(item.getQty())
//                        .divide(changeQty, 2, BigDecimal.ROUND_HALF_UP));
//            }
//            newItem.setPrice(item.getPrice().multiply(item.getQty()).divide(changeQty, 2, BigDecimal.ROUND_HALF_UP));

            /*处理替换金额问题*/
            if (productPriceZero) {
                /*如果总和为0，其他都是0，所有金额给到同一个品*/
                if (idx == size) {
                    newItem.setPriceActual(priceActual);
                    newItem.setPrice(price);
                    newItem.setAmtDiscount(amtDiscount);
                    newItem.setOrderSplitAmt(orderSplitAmt);
                } else {
                    newItem.setPriceActual(BigDecimal.ZERO);
                    newItem.setPrice(BigDecimal.ZERO);
                    newItem.setAmtDiscount(BigDecimal.ZERO);
                    newItem.setOrderSplitAmt(BigDecimal.ZERO);
                }
            } else {
                /*按比计算，差额给到同一个品（这里给到最后一个品）*/
                if (idx == size) {
                    /*最后一个，剩余所有都给它*/

                    /*成交单价 = 原商品成交单价 * 当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和*/
                    newItem.setPriceActual(priceActual);
                    /*平台售价 = 原商品平台售价 * 当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和*/
                    newItem.setPrice(price);
                    /*优惠金额 = 原商品优惠金额*当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和*/
                    newItem.setAmtDiscount(amtDiscount);
                    /*平摊金额 = 原商品平摊金额*当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和*/
                    newItem.setOrderSplitAmt(orderSplitAmt);

                } else {
                    /*否则按比计算*/

                    /*成交单价 = 原商品成交单价 * 当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和，如果全部为0则金额直接摊到第一个商品上*/
                    newItem.setPriceActual(Optional.ofNullable(item.getPriceActual()).orElse(BigDecimal.ZERO)
                            .multiply(salePriceMap.getOrDefault(skuQtyEntry.getKey(), BigDecimal.ZERO))
                            .divide(totalSalePrice, 4, RoundingMode.HALF_DOWN));
                    /*平台售价 = 原商品平台售价 * 当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和*/
                    newItem.setPrice(Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO)
                            .multiply(salePriceMap.getOrDefault(skuQtyEntry.getKey(), BigDecimal.ZERO))
                            .divide(totalSalePrice, 4, RoundingMode.HALF_DOWN));
                    /*优惠金额 = 原商品优惠金额*当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和*/
                    newItem.setAmtDiscount(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO)
                            .multiply(salePriceMap.getOrDefault(skuQtyEntry.getKey(), BigDecimal.ZERO))
                            .divide(totalSalePrice, 4, RoundingMode.HALF_DOWN));
                    /*平摊金额 = 原商品平摊金额*当前被替换的商品标准销售价/所有被替换的商品的标准销售价之和*/
                    newItem.setOrderSplitAmt(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO)
                            .multiply(salePriceMap.getOrDefault(skuQtyEntry.getKey(), BigDecimal.ZERO))
                            .divide(totalSalePrice, 4, RoundingMode.HALF_DOWN));

                    priceActual = priceActual.subtract(newItem.getPriceActual());
                    price = price.subtract(newItem.getPrice());
                    amtDiscount = amtDiscount.subtract(newItem.getAmtDiscount());
                    orderSplitAmt = orderSplitAmt.subtract(newItem.getOrderSplitAmt());
                }
            }
            /*成交金额 = 成交单价*数量*/
            newItem.setRealAmt(newItem.getPriceActual().multiply(newItem.getQty()));

            //替换商品后的商品无法指定效期问题修改
            if ("Y".equals(productSku.getIsEnableExpiry())) {
                newItem.setIsEnableExpiry(1);
            } else {
                newItem.setIsEnableExpiry(0);
            }
            newItem.setMDim4Id(productSku.getMDim4Id());
            newItem.setMDim6Id(productSku.getMDim6Id());
            /*效期类型？*/
            if (newItem.getExpiryDateType() != null) {
                newItem.setExpiryDateType(0);
            }
            // 替换前条码编码 只记录第一次替换
            if (StringUtils.isBlank(item.getOriginSkuEcode()) &&
                    !item.getPsCSkuEcode().equals(productSku.getSkuEcode())) {
                newItem.setOriginSkuEcode(item.getPsCSkuEcode());
            }

            // 处理其他信息
            newItem.setId(sequenceUtil.buildOrderItemSequenceId());
            OmsStorageUtils.setBModelDefalutDataByUpdate(newItem, user);

            newItemList.add(newItem);
            idx++;
        }

        /*删除老明细*/
        ocBOrderItemMapper.deleteByItemId(item.getId(), ocBOrder.getId());
        /*新增新明细列表*/
        ocBOrderItemMapper.batchInsert(newItemList);

        /*记录日志*/
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.GOODS_REPLACE.getKey(), "将商品条码:" + ocBOrderItem.getPsCSkuEcode()
                        + "替换为" + skuQtyMap.keySet() + "成功:",
                null, null, user);

        /*释放占单库存*/
        batchOperationGoodsService.handleOccupy(ocBOrder, Lists.newArrayList(ocBOrderItem), user);
    }

}
