package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.dto.EqualExchangeItem;
import com.jackrain.nea.oc.oms.dto.EqualExchangeStInfo;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCEquityBarterStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.EquityBarterTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategy;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategyItem;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @Author: 黄世新
 * @Date: 2022/7/11 下午3:53
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsManualEqualExchangeService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private StCEquityBarterStrategyItemMapper stCEquityBarterStrategyItemMapper;
    @Autowired
    private StCEquityBarterStrategyMapper stCEquityBarterStrategyMapper;
    @Autowired
    private OmsEqualExchangeStService omsEqualExchangeStService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;
    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;
    @Autowired
    private ThreadPoolTaskExecutor orderEqualExchangeThreadPoolExecutor;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;
    @Autowired
    private OcBOrderItemExtService ocBOrderItemExtService;


    public ValueHolderV14 selectEqualExchangeOrder(List<Long> ids) {
        ValueHolderV14 holder = new ValueHolderV14();
        List<List<Long>> listList = BllCommonUtil.getBasePageList(ids, 100);
        List<EqualExchangeItem> exchangeItems = new ArrayList<>();
        List<Future<List<EqualExchangeItem>>> submitList = new ArrayList<>();
        for (List<Long> longs : listList) {
            log.info(LogUtil.format("对等换货多线程入参:{}", "对等换货多线程入参"), JSONObject.toJSONString(longs));
            Future<List<EqualExchangeItem>> submit = orderEqualExchangeThreadPoolExecutor.submit(new EqualExchangeSelectTask(longs));
            submitList.add(submit);
        }

        for (Future<List<EqualExchangeItem>> listFuture : submitList) {
            try {
                List<EqualExchangeItem> exchangeItems1 = listFuture.get();
                if (CollectionUtils.isNotEmpty(exchangeItems1)) {
                    exchangeItems.addAll(exchangeItems1);
                }
            } catch (Exception e){
            }
        }
        if (CollectionUtils.isNotEmpty(exchangeItems)) {
            log.info(LogUtil.format("对等换货多线程返回:{}", "对等换货多线程返回"), JSONObject.toJSONString(exchangeItems));
            Map<String, List<EqualExchangeItem>> skuMap = exchangeItems.stream().collect(Collectors.groupingBy(EqualExchangeItem::getSkuCode));
            List<EqualExchangeItem> exchanges = new ArrayList<>();
            for (String s : skuMap.keySet()) {
                EqualExchangeItem exchangeItem = new EqualExchangeItem();
                List<EqualExchangeItem> exchangeItems1 = skuMap.get(s);
                Set<Long> idSet = new HashSet<>();
                for (EqualExchangeItem equalExchangeItem : exchangeItems1) {
                    idSet.addAll(equalExchangeItem.getRelationIds());
                }
                BigDecimal totalQty = exchangeItems1.stream().map(EqualExchangeItem::getQty).
                        reduce(BigDecimal.ZERO, BigDecimal::add);
                exchangeItem.setSkuCode(s);
                exchangeItem.setSkuTitle(exchangeItems1.get(0).getSkuTitle());
                exchangeItem.setRelationIds(idSet);
                exchangeItem.setQty(totalQty);
                exchanges.add(exchangeItem);
            }

            holder.setCode(0);
            holder.setMessage("查询成功!");
            holder.setData(exchanges);
            return holder;
        }
        holder.setCode(-1);
        holder.setMessage("未查询到符合条件的数据!");
        return holder;
    }


    private void executeEqualExchange(OcBOrder ocBOrder, EqualExchangeStInfo equalExchangeStInfo, User user) {
        boolean toBOrderPt = OmsBusinessTypeUtil.isToBOrder(ocBOrder);
        boolean toCOrderPt = OmsBusinessTypeUtil.isSapToCOrder(ocBOrder);
        String gwSourceGroup = ocBOrder.getGwSourceGroup();
        if (toBOrderPt || toCOrderPt || "38".equals(gwSourceGroup) || "39".equals(gwSourceGroup)) {
            throw new NDSException("该业务类型订单不支持对等换货");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsCycle())) {
            throw new NDSException("天猫周期购订单不支持对等换货");
        }
        Integer orderStatus = ocBOrder.getOrderStatus();
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)) {
            //释放库存
            ValueHolderV14 sgValueHolder = sgRpcService.voidSgStockOccupy(ocBOrder, orderItems, user);
            if (sgValueHolder.getCode() != 0) {
                throw new NDSException("释放库存失败");
            }
        }
        String exchangeSkuCode = equalExchangeStInfo.getExchangeSkuCode();
        OrderItemResult result = handleEqualExchangeItem(ocBOrder, orderItems, exchangeSkuCode);
        List<OcBOrderItem> itemList = null;
        List<OcBOrderItem> delItemList = null;
        if (result != null) {
            itemList = result.getItemList();
            delItemList = result.getDelList();
        } else {
            itemList = orderItems.stream().filter(p -> p.getPsCSkuEcode().equals(exchangeSkuCode)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemList)) {
                throw new NDSException("未查询到有效的订单明细");
            }
        }
        boolean b = omsEqualExchangeStService.manualEqualExchangeService(itemList, ocBOrder, equalExchangeStInfo, delItemList, user);
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        ocBOrderMapper.updateById(order);

        //卡单不释放
        if (!YesNoEnum.Y.getVal().equals(ocBOrder.getIsDetention())) {
            if (omsBusinessTypeStService.isAutoOccupy(ocBOrder)) {
                omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
            }
        }
        ocBOrderItemExtService.deleteByOrderId(ocBOrder.getId());
        if (!b) {
            throw new NDSException("未匹配到有效的策略");
        }
    }

    public ValueHolderV14<List<EqualExchangeStInfo>> selectEqualExchange(String skuCode) {
        ValueHolderV14<List<EqualExchangeStInfo>> holder = new ValueHolderV14<>();
        List<StCEquityBarterStrategyItem> barterStrategyItems = stCEquityBarterStrategyItemMapper.selectEquityBarterStrategyItemBySkuCode(skuCode);
        if (CollectionUtils.isEmpty(barterStrategyItems)) {
            holder.setCode(-1);
            holder.setMessage("未查询到该条码的对等换货策略");
            return holder;
        }
        Map<Long, List<StCEquityBarterStrategyItem>> stMap = barterStrategyItems.stream()
                .collect(Collectors.groupingBy(StCEquityBarterStrategyItem::getStCEquityBarterStrategyId));
        List<StCEquityBarterStrategy> stCEquityBarterStrategies =
                stCEquityBarterStrategyMapper.selectStCEquityBarterStrategyByIds(new ArrayList<>(stMap.keySet()));
        if (CollectionUtils.isEmpty(stCEquityBarterStrategies)) {
            holder.setCode(-1);
            holder.setMessage("未查询到该条码有效的对等换货策略");
            return holder;
        }
        //查询店铺信息
        List<Long> shopIds = stCEquityBarterStrategies.stream()
                .map(StCEquityBarterStrategy::getCpCShopId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, String> shopMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(shopIds)) {
            List<CpShop> cpShops = cpRpcService.queryShopWithoutActiveByShopIds(shopIds);
            if (CollectionUtils.isNotEmpty(cpShops)) {
                shopMap = cpShops.stream().collect(Collectors.toMap(CpShop::getId, CpShop::getCpCShopTitle));
            }
        }
        List<EqualExchangeStInfo> list = new ArrayList<>();
        for (StCEquityBarterStrategy stCEquityBarterStrategy : stCEquityBarterStrategies) {
            List<StCEquityBarterStrategyItem> barterStrategyItems1 = stMap.get(stCEquityBarterStrategy.getId());
            for (StCEquityBarterStrategyItem stCEquityBarterStrategyItem : barterStrategyItems1) {
                EqualExchangeStInfo stInfo = new EqualExchangeStInfo();
                stInfo.setExchangeSkuCode(stCEquityBarterStrategyItem.getPsCSkuCode());
                stInfo.setExchangeSkuTitle(stCEquityBarterStrategyItem.getPsCSkuName());
                stInfo.setExchangeQty(stCEquityBarterStrategyItem.getQty());
                stInfo.setEqualSkuCode(stCEquityBarterStrategyItem.getEquitySkuCode());
                stInfo.setEqualSkuTitle(stCEquityBarterStrategyItem.getEquitySkuName());
                stInfo.setEqualQty(stCEquityBarterStrategyItem.getEquityQty());
                stInfo.setType(EquityBarterTypeEnum.getByKey(stCEquityBarterStrategy.getType()).getDesc());
                stInfo.setShopName(shopMap.get(stCEquityBarterStrategy.getCpCShopId()));
                stInfo.setOutStockNoRestore(stCEquityBarterStrategyItem.getOutStockNoRestore());
                list.add(stInfo);
            }
        }
        holder.setCode(0);
        holder.setMessage("查询成功");
        holder.setData(list);
        return holder;
    }

    /**
     * 执行手动对等换货
     *
     * @param exchangeStInfos
     * @param user
     * @return
     */
    public ValueHolderV14 confirmEqualExchange(EqualExchangeStInfo exchangeStInfos, User user) {
        ValueHolderV14 holder = new ValueHolderV14();
        holder.setCode(0);
        Set<Long> relationIds = exchangeStInfos.getRelationIds();
        if (relationIds.size() == 1) {
            int i = this.handleOrder(exchangeStInfos, user);
            holder.setMessage("执行对等换货成功!");
            if (i > 0) {
                holder.setCode(-1);
                holder.setMessage("执行对等换货失败!");
            }
            return holder;
        }
        //异步执行
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("手动执行对等换货");
        asyncTaskBody.setTaskType("手动执行对等换货");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        commonTaskExecutor.submit(() -> {
            int count = this.handleOrder(exchangeStInfos, user);
            ValueHolderV14 holderV14 = new ValueHolderV14();
            holderV14.setMessage("手动执行对等换货失败:" + count);
            asyncTaskManager.afterExecute(user, asyncTaskBody, holderV14.toJSONObject());

        });
        holder.setData(asyncTaskBody.getId());
        holder.setMessage("手动执行对等换货任务开始！详情请在我的任务查看");
        return holder;
    }


    private int handleOrder(EqualExchangeStInfo equalExchangeStInfo, User user) {
        Set<Long> relationIds = equalExchangeStInfo.getRelationIds();
        //订单数量大于100时 采用多线程处理
        int size = relationIds.size();
        if (size > 100) {
            int i = new BigDecimal(size).divide(new BigDecimal(10), 0, BigDecimal.ROUND_UP).intValue();
            List<List<Long>> basePageList = BllCommonUtil.getBasePageList(new ArrayList<>(relationIds), i);
            List<Future<Integer>> futures = new ArrayList<>();
            for (List<Long> longs : basePageList) {
                Set<Long> longs1 = new HashSet<>(longs);
                Future<Integer> submit = orderEqualExchangeThreadPoolExecutor.submit(new EqualExchangeTask(equalExchangeStInfo, user, longs1));
                futures.add(submit);
            }
            Integer count = 0;
            for (Future<Integer> future : futures) {
                try {
                    Integer integer = future.get();
                    count = count + integer;
                } catch (Exception e) {
                    log.error(LogUtil.format("对等换货多线程运行失败{}", "对等换货多线程运行失败", Throwables.getStackTraceAsString(e)));
                }
            }
            return count;
        } else {
            return handleOrderEqualExchange(relationIds, equalExchangeStInfo, user);
        }
    }

    private int handleOrderEqualExchange(Set<Long> relationIds, EqualExchangeStInfo equalExchangeStInfo, User user) {
        //加锁
        int failCount = 0;
        for (Long orderId : relationIds) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                    OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                    Integer orderStatus = ocBOrder.getOrderStatus();
                    if ((OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                            || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus))) {
                        this.executeEqualExchange(ocBOrder, equalExchangeStInfo, user);
                    } else {
                        failCount++;
                    }
                } else {
                    log.error(LogUtil.format("对等换货加锁失败", orderId));
                    failCount++;
                }
            } catch (Exception e) {
                log.error(LogUtil.format("手动执行对等换货失败:{}", "手动执行对等换货失败", orderId), Throwables.getStackTraceAsString(e));
                failCount++;
            } finally {
                redisLock.unlock();
            }
        }
        return failCount;
    }

    class EqualExchangeSelectTask implements Callable<List<EqualExchangeItem>> {

        private final List<Long> ids;

        public EqualExchangeSelectTask(List<Long> ids) {
            this.ids = ids;
        }

        @Override
        public List<EqualExchangeItem> call() throws Exception {
            return this.selectEqualExchangeOrderThread(ids);
        }

        private List<EqualExchangeItem> selectEqualExchangeOrderThread(List<Long> ids) {
            List<OcBOrder> idList = new ArrayList<>();
            //查询订单
            List<OcBOrder> orderList = ocBOrderMapper.selectByIdsListByOrders(ids);
            if (CollectionUtils.isNotEmpty(orderList)) {
                for (OcBOrder order : orderList) {
                    String gwSourceGroup = order.getGwSourceGroup();
                    if ((StringUtils.isEmpty(gwSourceGroup) || (!"38".equals(gwSourceGroup) && !"39".equals(gwSourceGroup))) && !OmsOrderUtil.wdtPlatformSend(order)) {
                        idList.add(order);
                    }
                }
            }

            if (CollectionUtils.isEmpty(idList)) {
                return null;
            }
            List<Long> idLists = new ArrayList<>();
            for (OcBOrder ocBOrder : idList) {
                Long shopId = ocBOrder.getCpCShopId();
                CpShop cpShop = cpRpcService.selectCpCShopById(shopId);
                if (cpShop != null && "Y".equals(cpShop.getIsEqualExchange())) {
                    idLists.add(ocBOrder.getId());
                }
            }
            if (CollectionUtils.isEmpty(idLists)) {
                return null;
            }
            List<OcBOrderItem> orderItemList = new ArrayList<>();
            List<List<Long>> basePageList = BllCommonUtil.getBasePageList(idLists, 1000);
            for (List<Long> orderIds : basePageList) {
                List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItemsByOrderIds(orderIds);
                if (CollectionUtils.isNotEmpty(ocBOrderItemList)) {
                    List<OcBOrderItem> orderItems = ocBOrderItemList.stream().filter(p -> p.getExpiryDateType() == null || p.getExpiryDateType() != 3).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(orderItems)) {
                        orderItemList.addAll(orderItems);
                    }
                }
            }
            if (CollectionUtils.isEmpty(orderItemList)) {
                return null;
            }
            this.reductionItem(orderItemList);
            Map<String, List<OcBOrderItem>> itemMap = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuEcode));
            List<EqualExchangeItem> exchangeItems = new ArrayList<>();
            for (String skuCode : itemMap.keySet()) {
                EqualExchangeItem item = new EqualExchangeItem();
                List<OcBOrderItem> ocBOrderItemList = itemMap.get(skuCode);
                Set<Long> idSet = ocBOrderItemList.stream().map(OcBOrderItem::getOcBOrderId).collect(Collectors.toSet());
                item.setSkuCode(skuCode);
                item.setSkuTitle(ocBOrderItemList.get(0).getPsCProEname());
                item.setRelationIds(idSet);
                BigDecimal totalQty = BigDecimal.ZERO;
                for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                    BigDecimal qty = ocBOrderItem.getQty();
                    totalQty = totalQty.add(qty);
                }

                item.setQty(totalQty);
                exchangeItems.add(item);
            }
            return exchangeItems;
        }

        /**
         * 还原对等换货原明细
         *
         * @param orderItemList
         */
        private void reductionItem(List<OcBOrderItem> orderItemList) {
            List<Long> ids = orderItemList.stream().map(OcBOrderItem::getOcBOrderId).collect(Collectors.toList());
            //查询对等换货明细
            List<OcBOrderEqualExchangeItem> ocBOrderEqualExchangeItems = ocBOrderEqualExchangeItemMapper.selectOcBOrderEqualExchangeItemList(ids);
            if (CollectionUtils.isEmpty(ocBOrderEqualExchangeItems)) {
                return;
            }
            List<OcBOrderItem> exchangeMarkItem = orderItemList.stream().filter(p -> StringUtils.isNotEmpty(p.getEqualExchangeMark())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(exchangeMarkItem)) {
                return;
            }
            orderItemList.removeAll(exchangeMarkItem);

            Map<String, List<OcBOrderItem>> collect = exchangeMarkItem.stream().collect(Collectors.groupingBy(OcBOrderItem::getEqualExchangeMark));

            List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
            for (OcBOrderEqualExchangeItem ocBOrderEqualExchangeItem : ocBOrderEqualExchangeItems) {
                List<OcBOrderItem> itemList = collect.get(ocBOrderEqualExchangeItem.getEqualExchangeMark());
                if (CollectionUtils.isNotEmpty(itemList)) {
                    OcBOrderItem item = new OcBOrderItem();
                    BeanUtils.copyProperties(ocBOrderEqualExchangeItem, item);
                    ocBOrderItems.add(item);
                }
            }
            orderItemList.addAll(ocBOrderItems);
        }
    }


    /**
     * 对等换货
     *
     * @param ocBOrder
     * @param orderItems
     * @param exchangeSkuCode
     * @return
     */
    private OrderItemResult handleEqualExchangeItem(OcBOrder ocBOrder, List<OcBOrderItem> orderItems, String exchangeSkuCode) {
        List<OcBOrderEqualExchangeItem> ocBOrderEqualExchangeItems = ocBOrderEqualExchangeItemMapper.
                selectOcBOrderEqualExchangeItemList(Collections.singletonList(ocBOrder.getId()));
        if (CollectionUtils.isNotEmpty(ocBOrderEqualExchangeItems)) {
            List<OcBOrderEqualExchangeItem> exchangeItems = ocBOrderEqualExchangeItems.stream().
                    filter(p -> p.getPsCSkuEcode().equals(exchangeSkuCode)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(exchangeItems)) {
                List<OcBOrderItem> orderItemList = new ArrayList<>();
                List<OcBOrderItem> delItemList = new ArrayList<>();
                Map<String, List<OcBOrderItem>> orderItemMap = orderItems.stream().filter(p -> StringUtils.isNotEmpty(p.getEqualExchangeMark())).collect(Collectors.groupingBy(OcBOrderItem::getEqualExchangeMark));
                for (OcBOrderEqualExchangeItem exchangeItem : exchangeItems) {
                    List<OcBOrderItem> itemList = orderItemMap.get(exchangeItem.getEqualExchangeMark());
                    if (CollectionUtils.isEmpty(itemList)) {
                        continue;
                    }
                    //通过比例计算出原始有多少个数量
                    BigDecimal newQty = BigDecimal.ZERO;
                    for (OcBOrderItem item : itemList) {
                        String equalExchangeRatio = item.getEqualExchangeRatio();
                        BigDecimal qty = item.getQty();
                        //基数
                        int i2 = 0;
                        if (StringUtils.isNotEmpty(equalExchangeRatio)) {
                            String[] split = equalExchangeRatio.split(":");
                            int i = Integer.parseInt(split[0]);
                            int i1 = Integer.parseInt(split[1]);
                            if (i % i1 == 0) {
                                i2 = i / i1;
                            } else {
                                i2 = i;
                            }
                            newQty = newQty.add(qty.multiply(BigDecimal.valueOf(i2)));
                        } else {
                            newQty = newQty.add(qty);
                        }
                    }
                    OcBOrderItem item = new OcBOrderItem();
                    BeanUtils.copyProperties(exchangeItem, item);
                    item.setId(itemList.get(0).getId());
                    item.setProType(itemList.get(0).getProType());
                    item.setQty(newQty);
                    BigDecimal exchangeQty = exchangeItem.getQty();
                    if (newQty.compareTo(exchangeQty) != 0) {
                        BigDecimal realAmtSingle = exchangeItem.getRealAmt().divide(exchangeQty, 10, BigDecimal.ROUND_HALF_UP);
                        BigDecimal adjustAmtSingle = exchangeItem.getAdjustAmt().divide(exchangeQty, 10, BigDecimal.ROUND_HALF_UP);
                        BigDecimal amtDiscountSingle = exchangeItem.getAmtDiscount().divide(exchangeQty, 10, BigDecimal.ROUND_HALF_UP);
                        BigDecimal orderSplitAmtSingle = exchangeItem.getOrderSplitAmt().divide(exchangeQty, 10, BigDecimal.ROUND_HALF_UP);
                        //计算出金额
                        item.setRealAmt(realAmtSingle.multiply(newQty).setScale(4, BigDecimal.ROUND_HALF_UP));
                        item.setAdjustAmt(adjustAmtSingle.multiply(newQty).setScale(4, BigDecimal.ROUND_HALF_UP));
                        item.setAmtDiscount(amtDiscountSingle.multiply(newQty).setScale(4, BigDecimal.ROUND_HALF_UP));
                        item.setOrderSplitAmt(orderSplitAmtSingle.multiply(newQty).setScale(4, BigDecimal.ROUND_HALF_UP));

                    }
                    delItemList.addAll(itemList);
                    orderItemList.add(item);
                }
                OrderItemResult result = new OrderItemResult();
                result.setDelList(delItemList);
                result.setItemList(orderItemList);
                return result;
            }
        }
        return null;
    }

    @Data
    private static class OrderItemResult {

        private List<OcBOrderItem> itemList;

        private List<OcBOrderItem> delList;

    }

    class EqualExchangeTask implements Callable<Integer> {

        private final EqualExchangeStInfo equalExchangeStInfo;

        private final User user;

        private final Set<Long> relationIds;

        public EqualExchangeTask(EqualExchangeStInfo equalExchangeStInfo, User user, Set<Long> relationIds) {
            this.equalExchangeStInfo = equalExchangeStInfo;
            this.user = user;
            this.relationIds = relationIds;
        }

        @Override
        public Integer call() throws Exception {
            return handleOrderEqualExchange(relationIds, equalExchangeStInfo, user);
        }
    }
}
