package com.jackrain.nea.oc.oms.spiltorder;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.stocksync.common.OmsConstantsIF;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderSourceRelationMapper;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBOrderSourceRelationTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsSpiltRuleEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.SpiltOrderParam;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderSourceRelation;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordAddTaskService;
import com.jackrain.nea.oc.oms.services.CycleBuyInfoService;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.services.OmsOccupyTaskService;
import com.jackrain.nea.oc.oms.services.OmsOrderDistributeWarehouseService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.SplitBeforeSourcingStService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryProcessor;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/8/1 下午3:08
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsOrderSpiltRuleService {

    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OrderAmountUtil orderAmountUtil;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOrderSpiltRuleService omsOrderSpiltRuleService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OrderDeliveryProcessor orderDeliveryProcessor;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private OmsOrderDistributeWarehouseService omsWarehousRuleService;
    @Autowired
    private OcBSapSalesDataRecordAddTaskService sapSalesDataRecordAddTaskService;
    @Autowired
    private SplitBeforeSourcingStService splitBeforeSourcingStService;
    @Autowired
    private OcBOrderSourceRelationMapper sourceRelationMapper;
    @Autowired
    private CycleBuyInfoService cycleBuyInfoService;


    public void orderSpiltRuleService(OcBOrderRelation orderInfo, User user) {
        OcBOrder ocBOrder = ocBOrderMapper.selectById(orderInfo.getOrderId());
        List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
        Map<Integer, Map<Set<Long>, SpiltOrderParam>> spiltRuleMap = orderInfo.getSpiltRule();
        if (spiltRuleMap == null || spiltRuleMap.isEmpty()) {
            //this.addOccupy(ocBOrder);
            //判断 该订单的平台状态
            String platformStatus = ocBOrder.getPlatformStatus();
            Integer orderType = ocBOrder.getOrderType();
            if (StringUtils.isNotEmpty(platformStatus)) {
                boolean isHistoryOrder = (TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS.equals(platformStatus)
                        || TaoBaoOrderStatus.TRADE_FINISHED.equals(platformStatus));
                String orderSource = ocBOrder.getOrderSource();
                if (isHistoryOrder && (orderType != null && orderType !=2) && !"手工新增".equals(orderSource)) {
                    OcBOrder order = new OcBOrder();
                    order.setId(ocBOrder.getId());
                    order.setSysremark("");
                    order.setPlatformDeliveryTime(ocBOrder.getPayTime());
                    order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                    //历史单直接标记平台发货时，则同步更新订单“出库时间”为系统当前时间，财务。增加虚拟订单条件
                    if (OmsConstantsIF.YES == ocBOrder.getIsInvented()) {
                        order.setScanTime(new Date());
                    }
                    if (OrderBusinessTypeCodeEnum.VIRTUAL_MILK_CARD.getCode().equals(ocBOrder.getBusinessTypeCode()) ||
                            OrderBusinessTypeCodeEnum.CYCLE_ORDER.getCode().equals(ocBOrder.getBusinessTypeCode()) ||
                            OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER.getCode().equals(ocBOrder.getBusinessTypeCode())) {
                        fillNaiKaWareHouse(ocBOrder, order);
                        sapSalesDataRecordAddTaskService.addTask(0, ocBOrder.getId(), user);
                    }
                    ocBOrderMapper.updateById(order);
                    if (!OmsBusinessTypeUtil.isToBOrder(order)) {
                        ocBOrderItemMapper.updateRealNumSourceQtyByOrderIds(Lists.newArrayList(order.getId()));
                    }
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), "历史订单,直接平台发货", null, null, user);
                    return;
                }
            }
            if (OrderTypeEnum.DIFFPRICE.getVal().equals(orderInfo.getOrderInfo().getOrderType())) {
                OcBOrderRelation relation1 = new OcBOrderRelation();
                relation1.setOrderInfo(ocBOrder);
                relation1.setOrderItemList(orderItemList);
                boolean isCyclePurchaseOrder = checkIsCyclePurchaseOrder(ocBOrder);
                Integer platform = orderInfo.getOrderInfo().getPlatform();
                this.handleFictitiousOrder(relation1, user, !isCyclePurchaseOrder);
                // 抖音、快手、小红书平台的 先不生成销售数据
                if (!((PlatFormEnum.DOU_YIN.getCode().equals(platform) || PlatFormEnum.KUAISHOU.getCode().equals(platform) || PlatFormEnum.HONGSHU_OPEN.getCode().equals(platform))
                        && isCyclePurchaseOrder)) {
                    sapSalesDataRecordAddTaskService.addTask(0, ocBOrder.getId(), user);
                }
            }
            OcBOrder bOrder = new OcBOrder();
            bOrder.setId(ocBOrder.getId());
            bOrder.setOccupyStatus(OrderOccupyStatus.STATUS_170);
            ocBOrderMapper.updateById(bOrder);
            return;
        }
        log.info(LogUtil.format("拆单逻辑数据:{}", "拆单逻辑数据", ocBOrder.getId()), JSONObject.toJSONString(spiltRuleMap));
        Map<Set<Long>, SpiltOrderParam> preSaleMap = spiltRuleMap.get(OmsSpiltRuleEnum.PRE_SALE.getCode());
        Map<Set<Long>, SpiltOrderParam> giftAfterMap = spiltRuleMap.get(OmsSpiltRuleEnum.GIFT_AFTER.getCode());
        Map<Set<Long>, SpiltOrderParam> businessTypeMap = spiltRuleMap.get(OmsSpiltRuleEnum.BUSINESS_TYPE.getCode());
        Map<Set<Long>, SpiltOrderParam> strategyMap = spiltRuleMap.get(OmsSpiltRuleEnum.FICTITIOUS.getCode());
        Map<Set<Long>, SpiltOrderParam> cardMap = spiltRuleMap.get(OmsSpiltRuleEnum.CARD.getCode());
        preSaleMap = preSaleMap == null ? new HashMap<>(16) : preSaleMap;
        giftAfterMap = giftAfterMap == null ? new HashMap<>(16) : giftAfterMap;
        businessTypeMap = businessTypeMap == null ? new HashMap<>(16) : businessTypeMap;
        strategyMap = strategyMap == null ? new HashMap<>(16) : strategyMap;
        cardMap = cardMap == null ? new HashMap<>(16) : cardMap;
        Set<Set<Long>> sets = matchItem(preSaleMap.keySet(), giftAfterMap.keySet());
        if (!checkIsOne(sets)) {
            sets = matchItem(sets, businessTypeMap.keySet());
            if (!checkIsOne(sets)) {
                sets = matchItem(sets, strategyMap.keySet());
            }
            if (!checkIsOne(sets)) {
                sets = matchItem(sets, cardMap.keySet());
            }
        }
        //进行拆单处理
        Map<Long, List<SpiltOrderParam>> longListMap = handleParam(spiltRuleMap);
        List<OcBOrderParam> ocBOrderParams = handleSpiltOrder(sets, orderItemList, ocBOrder, longListMap, orderInfo);
        omsOrderSpiltRuleService.handleOrder(ocBOrderParams, ocBOrder, user);
        //todo 订单保存后的处理
        omsOrderSpiltRuleService.handleAfterOrder(ocBOrderParams, user, ocBOrder);
    }


    private void fillNaiKaWareHouse(OcBOrder ocBOrder, OcBOrder order){
        order.setScanTime(new Date());
        if(ocBOrder.getCpCPhyWarehouseId() != null && ocBOrder.getCpCPhyWarehouseId() != 0){
            return;
        }
        // 赋值默认发货仓库
        StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
        if (shopStrategy != null && shopStrategy.getDefaultStoreId() != null) {
            CpCPhyWarehouse cpCPhyWarehouse = omsWarehousRuleService.queryByWarehouseId(shopStrategy.getDefaultStoreId());
            if (cpCPhyWarehouse != null) {
                order.setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
                order.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                order.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
            }
        }
    }
    /**
     * 处理携带过来的参数
     *
     * @param spiltRuleMap
     * @return
     */
    private Map<Long, List<SpiltOrderParam>> handleParam(Map<Integer, Map<Set<Long>, SpiltOrderParam>> spiltRuleMap) {
        Map<Long, List<SpiltOrderParam>> itemParamMap = new HashMap<>();
        for (Integer key : spiltRuleMap.keySet()) {
            Map<Set<Long>, SpiltOrderParam> setSpiltOrderParamMap = spiltRuleMap.get(key);
            if (setSpiltOrderParamMap == null || setSpiltOrderParamMap.isEmpty()) {
                continue;
            }
            for (Set<Long> longSet : setSpiltOrderParamMap.keySet()) {
                SpiltOrderParam spiltOrderParam = setSpiltOrderParamMap.get(longSet);
                for (Long aLong : longSet) {
                    if (!itemParamMap.containsKey(aLong)) {
                        List<SpiltOrderParam> params = new ArrayList<>();
                        params.add(spiltOrderParam);
                        itemParamMap.put(aLong, params);
                    } else {
                        List<SpiltOrderParam> params = itemParamMap.get(aLong);
                        params.add(spiltOrderParam);
                        itemParamMap.put(aLong, params);
                    }
                }
            }
        }
        return itemParamMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleOrder(List<OcBOrderParam> ocBOrderParams, OcBOrder ocBOrder, User user) {
        if (CollectionUtils.isNotEmpty(ocBOrderParams)) {
            OcBOrder order = new OcBOrder();
            order.setId(ocBOrder.getId());
            order.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
            order.setSysremark("订单拆单,作废原单");
            ocBOrderMapper.updateById(order);
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(), "订单拆单,作废订单", null, null, user);
            List<OcBOrder> ocBOrderList = new ArrayList<>();
            List<OcBOrderItem> itemList = new ArrayList<>();
            List<OcBOrderEqualExchangeItem> exchangeItems = ocBOrderEqualExchangeItemMapper.selectOcBOrderEqualExchangeItemList(Collections.singletonList(ocBOrder.getId()));
            List<OcBOrderNaiKa> ocBOrderNaiKaList = new ArrayList<>();
            List<OcBOrderEqualExchangeItem> exchangeItems1 = new ArrayList<>();
            for (OcBOrderParam ocBOrderParam : ocBOrderParams) {
                OcBOrder bOrder = ocBOrderParam.getOcBOrder();
                bOrder.setOccupyStatus(OrderOccupyStatus.STATUS_170);
                bOrder.setSysremark("");
                ocBOrderList.add(bOrder);
                List<OcBOrderItem> orderItemList = ocBOrderParam.getOrderItemList();
                List<OcBOrderItem> collect = orderItemList.stream().filter(p -> p.getIsGift() != null && p.getIsGift() == 1).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    bOrder.setIsHasgift(1);
                } else {
                    bOrder.setIsHasgift(0);
                }
                List<OcBOrderItem> equalExchangeItems = orderItemList.stream().filter(p -> p.getIsEqualExchange() != null && p.getIsEqualExchange() == 1).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(equalExchangeItems)) {
                    bOrder.setIsEqualExchange(1);
                } else {
                    bOrder.setIsEqualExchange(0);
                }
                List<OcBOrderItem> giftList = orderItemList.stream().filter(p -> p.getIsGiftSplit() != null && p.getIsGiftSplit() == 3).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(giftList)) {
                    //hold 单
                    bOrder.setIsInterecept(1);
                    ocBOrderHoldService.holdOrUnHoldOrder(bOrder, OrderHoldReasonEnum.GIFT_AFTER);
                }
                itemList.addAll(orderItemList);
                omsOrderLogService.addUserOrderLog(bOrder.getId(), bOrder.getBillNo(), OrderLogTypeEnum.ORDER_SPLIT.getKey(), "由订单id:" + ocBOrder.getId() + "拆分", null, null, user);

                //拆单维护补发关系
                if (YesNoEnum.Y.getVal().equals(bOrder.getIsResetShip())) {
                    //补发单，维护来源关系
                    List<Long> sourceIds = sourceRelationMapper.querySourceOrderId(ocBOrder.getId(), OcBOrderSourceRelationTypeEnum.REISSUE.getKey());
                    if (CollectionUtils.isNotEmpty(sourceIds)) {
                        OcBOrderSourceRelation sourceRelation = new OcBOrderSourceRelation();
                        sourceRelation.setId(sequenceUtil.buildOrderSourceRelationSequenceId());
                        sourceRelation.setOrderId(bOrder.getId());
                        sourceRelation.setSourceOrderId(sourceIds.get(0));
                        sourceRelation.setType(OcBOrderSourceRelationTypeEnum.REISSUE.getKey());
                        sourceRelation.setCreationdate(new Date());
                        sourceRelation.setModifieddate(new Date());
                        sourceRelation.setOwnerid(user != null ? user.getId() : 0L);
                        sourceRelation.setOwnername(user != null ? user.getEname() : "");
                        sourceRelation.setIsactive("Y");
                        sourceRelationMapper.insert(sourceRelation);
                    }
                }

                if (CollectionUtils.isNotEmpty(exchangeItems)) {
                    for (OcBOrderEqualExchangeItem exchangeItem : exchangeItems) {
                        OcBOrderEqualExchangeItem item = new OcBOrderEqualExchangeItem();
                        BeanUtils.copyProperties(exchangeItem, item);
                        long l = sequenceUtil.buildEqualExchangeItemSequenceId();
                        item.setId(l);
                        item.setOcBOrderId(bOrder.getId());
                        exchangeItems1.add(item);
                    }
                }
                List<OcBOrderNaiKa> ocBOrderNaiKas = ocBOrderParam.getOcBOrderNaiKas();
                if (CollectionUtils.isNotEmpty(ocBOrderNaiKas)){
                    ocBOrderNaiKaList.addAll(ocBOrderNaiKas);
                }
            }
            if (CollectionUtils.isNotEmpty(exchangeItems1)) {
                ocBOrderEqualExchangeItemMapper.batchInsert(exchangeItems1);
            }
            if (CollectionUtils.isNotEmpty(ocBOrderNaiKaList)) {
                ocBOrderNaiKaMapper.batchInsert(ocBOrderNaiKaList);
            }
            ocBOrderMapper.batchInsert(ocBOrderList);
            ocBOrderItemMapper.batchInsert(itemList);
        }
    }


    private List<OcBOrderParam> handleSpiltOrder(Set<Set<Long>> itemIds, List<OcBOrderItem> orderItemList,
                                                 OcBOrder order, Map<Long, List<SpiltOrderParam>> paramMap, OcBOrderRelation orderRelation) {
        Map<Long, OcBOrderItem> itemMap = orderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
        List<Long> cardByGiftNotSplit = orderRelation.getCardByGiftNotSplit();
        List<Long> cardByGiftRelation = orderRelation.getCardByGiftRelation();
        List<OcBOrderParam> orderParams = new ArrayList<>();
        List<OcBOrderNaiKa> ocBOrderNaiKas = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(order.getId());
        if (CollectionUtils.isNotEmpty(itemIds)) {
            boolean spitAmtFlag = true;
            for (Set<Long> itemId : itemIds) {
                OcBOrderParam param = new OcBOrderParam();
                OcBOrder ocBOrder = new OcBOrder();
                BeanUtils.copyProperties(order, ocBOrder);
                long mainId = sequenceUtil.buildOrderSequenceId();
                ocBOrder.setId(mainId);
                ocBOrder.setIsSplit(1);
                ocBOrder.setBillNo(sequenceUtil.buildBillNo());
                //这个地方改为待分配  因为后面还有策略拆单  数据有策略拆单处理
                ocBOrder.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
                String platformStatus = ocBOrder.getPlatformStatus();
                Integer orderType = ocBOrder.getOrderType();
                String orderSource = ocBOrder.getOrderSource();
                if (StringUtils.isNotEmpty(platformStatus)) {
                    boolean isHistoryOrder = (TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS.equals(platformStatus)
                            || TaoBaoOrderStatus.TRADE_FINISHED.equals(platformStatus));
                    if (isHistoryOrder && (orderType != null && orderType !=2) && !"手工新增".equals(orderSource)) {
                        ocBOrder.setPlatformDeliveryTime(order.getPayTime());
                        ocBOrder.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                    }
                }
                ocBOrder.setSuffixInfo("SP-" + mainId);

                Map<Long, List<OcBOrderNaiKa>> naikaMap = new HashMap<>();
                List<OcBOrderNaiKa> orderNaiKas = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(ocBOrderNaiKas)) {
                    List<OcBOrderNaiKa> naiKas = ocBOrderNaiKas.stream().filter(p -> p.getOcBOrderItemId() == null).collect(Collectors.toList());
                    List<OcBOrderNaiKa> naiKaList = ocBOrderNaiKas.stream().filter(p -> p.getOcBOrderItemId() != null).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(naiKaList)) {
                        naikaMap = naiKaList.stream().collect(Collectors.groupingBy(OcBOrderNaiKa::getOcBOrderItemId));
                    }
                    if (CollectionUtils.isNotEmpty(naiKas)) {
                        // 此处需要改造
                        for (OcBOrderNaiKa naiKa : naiKas) {
                            OcBOrderNaiKa orderNaiKa = new OcBOrderNaiKa();
                            BeanUtils.copyProperties(naiKa, orderNaiKa);
                            orderNaiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
                            orderNaiKa.setOcBOrderId(mainId);
                            orderNaiKa.setCreationdate(new Date());
                            orderNaiKa.setModifieddate(new Date());
                            orderNaiKas.add(orderNaiKa);
                        }
                    }
                }

                List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
                for (Long id : itemId) {
                    OcBOrderItem item = itemMap.get(id);
                    OcBOrderItem orderItem = new OcBOrderItem();
                    BeanUtils.copyProperties(item, orderItem);
                    this.handleItemParam(paramMap, ocBOrder, id, cardByGiftNotSplit);
                    ocBOrderItems.add(orderItem);
                }
                List<OcBOrderItem> itemList1 = ocBOrderItems.stream().filter(p -> p.getProType() != null && p.getProType() == 4).collect(Collectors.toList());
                if (itemList1.size() == ocBOrderItems.size()) {
                    //订单全是组合未拆分的订单,直接丢弃
                    continue;
                }
                List<OcBOrderItem> itemList = ocBOrderItems.stream().filter(p -> p.getProType() != null && p.getProType() != 4).collect(Collectors.toList());

                List<OcBOrderItem> combineSplit = splitBeforeSourcingStService.isCombineSplit(orderItemList, itemList);
                if (CollectionUtils.isNotEmpty(combineSplit)) {
                    ocBOrder.setIsCombination(1);
                    splitBeforeSourcingStService.groupGoodsType(itemList, combineSplit);
                    for (OcBOrderItem orderItem : combineSplit) {
                        OcBOrderItem ocBOrderItem = new OcBOrderItem();
                        BeanUtils.copyProperties(orderItem, ocBOrderItem);
                        itemList.add(ocBOrderItem);
                    }
                } else {
                    ocBOrder.setIsCombination(0);
                    for (OcBOrderItem ocOrderItem : itemList) {
                        ocOrderItem.setProType((long) SkuType.NORMAL_PRODUCT);
                    }
                }
                for (OcBOrderItem item : itemList) {
                    long itemSequenceId = sequenceUtil.buildOrderItemSequenceId();
                    List<OcBOrderNaiKa> orderNaiKas1 = handleNaika(naikaMap, item, itemSequenceId, ocBOrder);
                    if (CollectionUtils.isNotEmpty(orderNaiKas1)) {
                        orderNaiKas.addAll(orderNaiKas1);
                    }
                    if (CollectionUtils.isNotEmpty(cardByGiftNotSplit) && cardByGiftNotSplit.contains(item.getId())) {
                        cardByGiftNotSplit.remove(item.getId());
                        cardByGiftNotSplit.add(itemSequenceId);
                    }
                    if (CollectionUtils.isNotEmpty(cardByGiftRelation) && cardByGiftRelation.contains(item.getId())) {
                        cardByGiftRelation.remove(item.getId());
                        cardByGiftRelation.add(itemSequenceId);
                    }
                    item.setId(itemSequenceId);
                    item.setOcBOrderId(mainId);
                }
                if (spitAmtFlag && ocBOrder.getShipAmt().compareTo(BigDecimal.ZERO) >0){
                    spitAmtFlag = false;
                } else {
                    ocBOrder.setShipAmt(BigDecimal.ZERO);
                }
                param.setOcBOrder(ocBOrder);
                param.setOrderItemList(itemList);
                param.setOcBOrderNaiKas(orderNaiKas);
                //计算金额
                orderAmountUtil.recountOrderAmount(param);
                orderParams.add(param);
            }
        }
        orderRelation.setCardByGiftRelation(cardByGiftRelation);
        orderRelation.setCardByGiftNotSplit(cardByGiftNotSplit);
        return orderParams;
    }


    private List<OcBOrderNaiKa> handleNaika(Map<Long, List<OcBOrderNaiKa>> naikaMap, OcBOrderItem ocBOrderItem, Long newItemId, OcBOrder ocBOrder) {
        if (naikaMap.isEmpty()) {
            return null;
        }
        List<OcBOrderNaiKa> orderNaiKaList = new ArrayList<>();
        List<OcBOrderNaiKa> orderNaiKas = naikaMap.get(ocBOrderItem.getId());
        if (CollectionUtils.isNotEmpty(orderNaiKas)) {
            for (OcBOrderNaiKa orderNaiKa : orderNaiKas) {
                OcBOrderNaiKa naiKa = new OcBOrderNaiKa();
                BeanUtils.copyProperties(orderNaiKa, naiKa);
                naiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
                naiKa.setOcBOrderItemId(newItemId);
                naiKa.setOcBOrderId(ocBOrder.getId());
                naiKa.setCreationdate(new Date());
                naiKa.setModifieddate(new Date());
                naiKa.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
                naiKa.setBusinessTypeId(ocBOrder.getBusinessTypeId());
                naiKa.setBusinessTypeName(ocBOrder.getBusinessTypeName());
                OrderBusinessTypeCodeEnum businessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(naiKa.getBusinessTypeCode());
                naiKa.setBusinessType(businessTypeCodeEnum.getNaiKaType());
                orderNaiKaList.add(naiKa);
            }
        }

        return orderNaiKaList;
    }

    /**
     * 处理带过来的参数
     *
     * @param paramMap
     * @param ocBOrder
     * @param id
     */
    private void handleItemParam(Map<Long, List<SpiltOrderParam>> paramMap, OcBOrder ocBOrder, Long id, List<Long> cardByGiftNotSplit) {
        List<SpiltOrderParam> params = paramMap.get(id);
        if (CollectionUtils.isNotEmpty(params)) {
            for (SpiltOrderParam spiltOrderParam : params) {
                if (spiltOrderParam.getBusinessTypeId() != null) {
                    ocBOrder.setBusinessTypeId(spiltOrderParam.getBusinessTypeId());
                    ocBOrder.setBusinessTypeCode(spiltOrderParam.getBusinessTypeCode());
                    ocBOrder.setBusinessTypeName(spiltOrderParam.getBusinessTypeName());
                }
                if (spiltOrderParam.isCardOrder() && !cardByGiftNotSplit.contains(id)) {
                    ocBOrder.setIsDetention(1);
                    ocBOrder.setDetentionReleaseDate(spiltOrderParam.getCardReleaseTime());
                }
                if (spiltOrderParam.isFictitiousOrder()) {
                    this.handleDiffprice(ocBOrder);
                } else if (ObjectUtil.isNotNull(ocBOrder.getOrderType()) && ObjectUtil.equal(OrderTypeEnum.DIFFPRICE.getVal(), ocBOrder.getOrderType())) {
                    // 啥也不做，就是为了防止进入到else 把前面策略变成虚拟商品的 后面又改成正常的品
                    this.handleDiffprice(ocBOrder);
                } else {
                    ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
                    // 这里需要增加虚拟标签清理
                    ocBOrder.setPriceLabel(IsActiveEnum.N.getKey());
                    ocBOrder.setIsInvented(0);
                }
                // 以后业务扩展需要增加携带过来的参数时  在这里加
            }
        }
    }

    /**
     * 处理虚拟商品
     *
     * @param ocBOrder
     */
    private void handleDiffprice(OcBOrder ocBOrder) {
        ocBOrder.setOrderType(OrderTypeEnum.DIFFPRICE.getVal());
        ocBOrder.setPriceLabel(IsActiveEnum.Y.getKey());
        ocBOrder.setIsInvented(1);
        if (!checkIsCyclePurchaseOrder(ocBOrder)) {
            ocBOrder.setOrderStatus(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
        }
        StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
        if (shopStrategy.getDefaultStoreId() != null) {
            CpCPhyWarehouse cpCPhyWarehouse = omsWarehousRuleService.queryByWarehouseId(shopStrategy.getDefaultStoreId());
            if (cpCPhyWarehouse != null) {
                ocBOrder.setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
                ocBOrder.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                ocBOrder.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                //如果实体仓是o2o仓库，对订单进行打标
                if (StringUtils.equals(cpCPhyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
                    ocBOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
                } else {
                    ocBOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
                }
            }
        }
    }

    /**
     * 订单保存后的处理逻辑
     *
     * @param ocBOrderParams
     */
    /**
     * 订单保存后的处理逻辑
     *
     * @param ocBOrderParams
     */
    public void handleAfterOrder(List<OcBOrderParam> ocBOrderParams, User user,OcBOrder bOrder) {
        if (CollectionUtils.isEmpty(ocBOrderParams)) {
            return;
        }

        //中台周期购信息维护
        if (OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(bOrder.getBusinessTypeCode())) {
            try {
                List<OcBOrder> newOrders = ocBOrderParams.stream().map(OcBOrderParam::getOcBOrder).collect(Collectors.toList());
                cycleBuyInfoService.fullCycleBuyInfo(bOrder, newOrders);
            } catch (Exception e) {
                log.warn("中台周期购信息维护失败 insertNewOrders billNo:{}", bOrder.getBillNo(), e);
                DingTalkUtil.notice("中台周期购信息维护失败:" + bOrder.getBillNo());
            }
        }

        for (OcBOrderParam ocBOrderParam : ocBOrderParams) {
            OcBOrder ocBOrder = ocBOrderParam.getOcBOrder();
            // 中台周期购订单虚拟平台发货延后
            if (OrderTypeEnum.DIFFPRICE.getVal().equals(ocBOrder.getOrderType())) {
                // 新拆出的虚拟单赋值出库时间
                if (!(PlatFormEnum.DOU_YIN.getCode().equals(ocBOrder.getPlatform())
                        || PlatFormEnum.KUAISHOU.getCode().equals(ocBOrder.getPlatform())
                        || PlatFormEnum.HONGSHU_OPEN.getCode().equals(ocBOrder.getPlatform()))) {
                    OcBOrder order = new OcBOrder();
                    order.setId(ocBOrder.getId());
                    order.setScanTime(new Date());
                    ocBOrderMapper.updateById(order);
                    sapSalesDataRecordAddTaskService.addTask(0, ocBOrder.getId(), user);
                }
                if (!checkIsCyclePurchaseOrder(ocBOrder)) {
                    //虚拟平台发货
                    OcBOrderRelation relation = new OcBOrderRelation();
                    relation.setOrderInfo(ocBOrder);
                    relation.setOrderItemList(ocBOrderParam.getOrderItemList());
                    orderDeliveryProcessor.platformSend(relation);
                    if (!OmsBusinessTypeUtil.isToBOrder(ocBOrder)) {
                        ocBOrderItemMapper.updateRealNumSourceQtyByOrderIds(Lists.newArrayList(ocBOrder.getId()));
                    }
                }
            }
            Integer orderStatus = ocBOrder.getOrderStatus();
            if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), "历史订单,直接平台发货", null, null, user);
                if (!OmsBusinessTypeUtil.isToBOrder(ocBOrder)) {
                    ocBOrderItemMapper.updateRealNumSourceQtyByOrderIds(Lists.newArrayList(ocBOrder.getId()));
                }
            }
        }

    }

    /**
     * 匹配交集
     *
     * @param sourceSet
     * @param targetSet
     * @return
     */
    private Set<Set<Long>> matchItem(Set<Set<Long>> sourceSet, Set<Set<Long>> targetSet) {
        if (CollectionUtils.isEmpty(sourceSet)) {
            return targetSet;
        }
        if (CollectionUtils.isEmpty(targetSet)) {
            return sourceSet;
        }
        Set<Set<Long>> afterMap = new HashSet<>();
        for (Set<Long> longSet : sourceSet) {
            for (Set<Long> longs : targetSet) {
                Set<Long> list = longSet.stream().filter(longs::contains).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(list)) {
                    afterMap.add(list);
                }
            }
        }
        return afterMap;
    }


    /**
     * 判断订单是否周期购订单
     *
     * @param orderInfo 订单信息
     * @return true=周期购
     */
    private boolean checkIsCyclePurchaseOrder(OcBOrder orderInfo) {
        return OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(orderInfo.getBusinessTypeCode())
                || OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER.getCode().equals(orderInfo.getBusinessTypeCode());
    }

    /**
     * 检验是否已经全部是一个
     *
     * @param set
     * @return
     */
    private boolean checkIsOne(Set<Set<Long>> set) {
        if (CollectionUtils.isNotEmpty(set)) {
            for (Set<Long> longSet : set) {
                if (longSet.size() > 1) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    private void handleFictitiousOrder(OcBOrderRelation orderInfo, User user, boolean isSyncPlatform) {
        //默认发货仓库
        StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(orderInfo.getOrderInfo().getCpCShopId());
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // new Date()为获取当前系统时间
        OcBOrder ocBOrderDto = new OcBOrder();
        ocBOrderDto.setId(orderInfo.getOrderId());
        //订单类型设置为虚拟，标签为价  start 0926
        ocBOrderDto.setOrderType(OrderTypeEnum.DIFFPRICE.getVal());
        ocBOrderDto.setPriceLabel(IsActiveEnum.Y.getKey());
        ocBOrderDto.setIsInvented(1);

        // 周期购订单更新订单状态放到后面操作
        if(isSyncPlatform){
            ocBOrderDto.setOrderStatus(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
        }
        ocBOrderDto.setScanTime(new Date());
        if (shopStrategy != null && shopStrategy.getDefaultStoreId() != null) {
            CpCPhyWarehouse cpCPhyWarehouse = omsWarehousRuleService.queryByWarehouseId(shopStrategy.getDefaultStoreId());
            if (cpCPhyWarehouse != null) {
                ocBOrderDto.setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
                ocBOrderDto.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                ocBOrderDto.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                //如果实体仓是o2o仓库，对订单进行打标
                if (StringUtils.equals(cpCPhyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
                    ocBOrderDto.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
                } else {
                    ocBOrderDto.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
                }

            }
        }
        OrderUtil.handleOrderSku(orderInfo.getOrderItemList(), ocBOrderDto);
        ocBOrderMapper.updateById(ocBOrderDto);
        // 周期购订单未更新状态不记录日志
        if(isSyncPlatform){
            omsOrderLogService.addUserOrderLog(orderInfo.getOrderId(),
                    orderInfo.getOrderInfo().getBillNo(), OrderLogTypeEnum.STOCK_SEND.getKey(),
                    "虚拟订单自动发货成功", null,
                    null, user);
            if (!OmsBusinessTypeUtil.isToBOrder(orderInfo.getOrderInfo())) {
                ocBOrderItemMapper.updateRealNumSourceQtyByOrderIds(Lists.newArrayList(orderInfo.getOrderInfo().getId()));
            }
        }
        // 周期购订单此处不调用平台发货
        if(isSyncPlatform){
            orderInfo.setAutomaticOperation(true);
            OcBOrderRelation orderRelation = new OcBOrderRelation();
            OcBOrder ocBOrder = ocBOrderMapper.selectById(orderInfo.getOrderId());
            orderRelation.setOrderInfo(ocBOrder);
            orderRelation.setOrderItemList(orderInfo.getOrderItemList());
            orderDeliveryProcessor.platformSend(orderRelation);
        }
    }

}
