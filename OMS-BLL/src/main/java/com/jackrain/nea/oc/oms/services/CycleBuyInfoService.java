package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.burgeon.r3.sg.core.model.table.store.sap.SgBSapInformationMapping;
import com.burgeon.r3.sg.store.api.sap.SgBSapInformationMappingR3Cmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.ac.model.enums.confirm.CycleSalesReportAmtChangeTypeEnum;
import com.jackrain.nea.ac.model.enums.confirm.CycleSalesReportCycleStatusEnum;
import com.jackrain.nea.ac.utils.DingDingUtil;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.hub.enums.OrderStatusEnum;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.AcCycleBuyAmtMapper;
import com.jackrain.nea.oc.oms.mapper.MilkCardAmountOffsetItemMapper;
import com.jackrain.nea.oc.oms.mapper.MilkCardAmountOffsetOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderCycleBuyInfoMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.model.CycleOffsetItemAmtInfo;
import com.jackrain.nea.oc.oms.model.enums.MilkCardCollectStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.MilkCardItemTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.request.CycleConfirmRequest;
import com.jackrain.nea.oc.oms.model.request.CycleOffsetAmountRequest;
import com.jackrain.nea.oc.oms.model.table.AcCycleBuyAmt;
import com.jackrain.nea.oc.oms.model.table.MilkCardAmountOffsetItem;
import com.jackrain.nea.oc.oms.model.table.MilkCardAmountOffsetOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderCycleBuyInfo;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.nums.OmsMiddleGroundBillTypeEnum;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.rpc.AcRpcService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Sets;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 中台周期购额外信息service
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CycleBuyInfoService {

    /**
     * 已提状态(周期购提货订单状态=仓库发货&平台发货&配货中&待传WMS&待审核&已审核)
     */
    private static final List<Integer> ALREADY_PICK_ORDER_STATUS = Lists.newArrayList(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger(),
            OmsOrderStatus.PLATFORM_DELIVERY.toInteger(), OmsOrderStatus.IN_DISTRIBUTION.toInteger(), OmsOrderStatus.PENDING_WMS.toInteger(),
            OmsOrderStatus.UNCONFIRMED.toInteger(), OmsOrderStatus.CHECKED.toInteger());

    @DubboReference(group = "sg", version = "1.0")
    private SgBSapInformationMappingR3Cmd sgBSapInformationMappingR3Cmd;

    @Resource
    private OcBOrderMapper orderMapper;
    @Resource
    private OcBOrderItemMapper orderItemMapper;
    @Resource
    private OcBOrderCycleBuyInfoMapper orderCycleBuyInfoMapper;
    @Resource
    private AcRpcService acRpcService;
    @Resource
    private OmsAdbDataSourceService omsAdbDataSourceService;
    @Resource
    private MilkCardAmountOffsetOrderMapper milkCardAmountOffsetOrderMapper;
    @Resource
    private MilkCardAmountOffsetItemMapper milkCardAmountOffsetItemMapper;
    @Resource
    private StCBusinessTypeMapper stCBusinessTypeMapper;
    @Resource
    private CpRpcService cpRpcExtService;
    @Resource
    private PsRpcService psRpcService;
    @Resource
    private AcCycleBuyAmtMapper acCycleBuyAmtMapper;


//    /**
//     * 周期购额外信息记录
//     * <p>
//     * 周期购提数：周期购订单下所有周期购提货订单（过滤补发和复制单，且商品不全部是赠品）的订单数量
//     * 周期购剩余提数：中台周期购订单下的所有状态=待寻源，（过滤补发和复制单，且商品不全部是赠品）的订单数量。
//     *
//     * @param sourceCode
//     */
//    public void generateCycleBuyInfo(String sourceCode) {
//        log.info("Start generateCycleBuyInfo sourceCode:{}", sourceCode);
//
//        List<Long> esIdList = GSI4Order.getIdListBySourceCode(sourceCode);
//        List<OcBOrder> orderList = orderMapper.selectOrderListByIds(esIdList);
//        if (CollectionUtils.isEmpty(orderList)) {
//            log.info("generateCycleBuyInfo orderList is empty sourceCode:{}", sourceCode);
//            return;
//        }
//
//        List<OcBOrderItem> bOrderItems = orderItemMapper.selectAllStatusOrderItemsByOrderIds(esIdList);
////        bOrderItems = bOrderItems.stream().filter(p -> !YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
//        Map<Long, List<OcBOrderItem>> orderIdMapMap = bOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
//
//        List<OcBOrder> calOrders = needCalOrders(orderList, orderIdMapMap);
//
//        if (CollectionUtils.isEmpty(calOrders)) {
//            log.info("generateCycleBuyInfo calOrders is empty sourceCode:{}", sourceCode);
//            return;
//        }
//
//        //周期购订单
//        List<OcBOrder> cycleOrders = calOrders.stream().filter(p -> OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(p.getBusinessTypeCode())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(cycleOrders)) {
//            log.info("generateCycleBuyInfo cycleOrders is empty sourceCode:{}", sourceCode);
//            return;
//        }
//        OcBOrder cycleOrder = cycleOrders.get(0);
//
//        //周期购提货
//        List<OcBOrder> pickOrders = calOrders.stream().filter(p -> OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(p.getBusinessTypeCode())).collect(Collectors.toList());
//        Map<Integer, List<OcBOrder>> cycleNumberMap = pickOrders.stream().collect(Collectors.groupingBy(OcBOrder::getCurrentCycleNumber));
//
//        //新增周期购额外数据
//        List<OcBOrderCycleBuyInfo> newInfos = Lists.newArrayList();
//
//        //计算周期购订单
//        String tid = cycleOrder.getTid();
//        String cycleBillNo = cycleOrder.getBillNo();
//        List<OcBOrderCycleBuyInfo> existCycleInfo = orderCycleBuyInfoMapper.getByTidAndBillNo(tid, cycleBillNo);
//        if (CollectionUtils.isNotEmpty(existCycleInfo)) {
//            log.info("generateCycleBuyInfo existCycleInfo is exist tid:{},billNo:{}", tid, cycleBillNo);
//            return;
//        }
//
//        //查询周期购订单明细
//        List<OcBOrderItem> cycleItems = orderIdMapMap.get(cycleOrder.getId());
//        if (CollectionUtils.isEmpty(cycleItems)) {
//            log.info("generateCycleBuyInfo cycleNoGiftItems is empty cycleOrderId:{},tid:{},billNo:{}", cycleOrder.getId(), tid, cycleBillNo);
//            return;
//        }
//
//        //周期购订单组装
//        newInfos.addAll(this.generateCycleOrder(cycleOrder, cycleNumberMap.size(), cycleItems));
//
//        //周期购赠品提货额外信息组装
//        newInfos.addAll(this.generateGiftPickOrder(pickOrders, orderIdMapMap));
//
//        //提货金额计算(billNo+skuId,pickAmt)
//        Map<String, BigDecimal> pickAmtMap = pickAmt(cycleOrder, cycleNumberMap, orderIdMapMap);
//
//        //周期购提货额外信息组装
//        newInfos.addAll(this.generatePickOrder(orderIdMapMap, cycleNumberMap, pickAmtMap));
//
//        log.info("generateCycleBuyInfo sourceCode:{}, newInfos:{}", sourceCode, JSON.toJSONString(newInfos));
//
//        if (CollectionUtils.isNotEmpty(newInfos)) {
//            orderCycleBuyInfoMapper.batchInsert(newInfos);
//        }
//    }

    /**
     * 周期购赠品提货额外信息组装
     *
     * @param pickOrders
     * @param orderIdItemMap
     * @return
     */
    private List<OcBOrderCycleBuyInfo> generateGiftPickOrder(List<OcBOrder> pickOrders, Map<Long, List<OcBOrderItem>> orderIdItemMap) {
        List<OcBOrderCycleBuyInfo> newInfos = Lists.newArrayList();
        for (OcBOrder ocBOrder : pickOrders) {
            List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(ocBOrder.getId());
            for (OcBOrderItem orderItem : ocBOrderItems) {
                Integer isGift = orderItem.getIsGift();
                //如果不是赠品，跳过
                if (!YesNoEnum.Y.getVal().equals(isGift)) {
                    continue;
                }

                OcBOrderCycleBuyInfo pickNewOrder = new OcBOrderCycleBuyInfo();
                pickNewOrder.setId(ModelUtil.getSequence("oc_b_order_cycle_buy_info"));
                pickNewOrder.setOcBOrderId(ocBOrder.getId());
                pickNewOrder.setBillNo(ocBOrder.getBillNo());
                pickNewOrder.setTid(ocBOrder.getTid());
                pickNewOrder.setBusinessTypeId(ocBOrder.getBusinessTypeId());
                pickNewOrder.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
                //提货金额
                pickNewOrder.setPickGoodsAmt(orderItem.getRealAmt());
                pickNewOrder.setOrderItemId(orderItem.getId());
                pickNewOrder.setSkuId(orderItem.getPsCSkuId());
                pickNewOrder.setIsGift(orderItem.getIsGift());
                pickNewOrder.setSkuCode(orderItem.getPsCSkuEcode());

                //defaultValue
                BaseModelUtil.initialBaseModelSystemField(pickNewOrder);
                newInfos.add(pickNewOrder);
            }
        }

        return newInfos;
    }

    private List<OcBOrderCycleBuyInfo> generateInitPickOrder(List<OcBOrder> pickOrders, Map<Long, List<OcBOrderItem>> orderIdItemMap) {
        List<OcBOrderCycleBuyInfo> newInfos = Lists.newArrayList();
        for (OcBOrder ocBOrder : pickOrders) {
            List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(ocBOrder.getId());
            for (OcBOrderItem orderItem : ocBOrderItems) {
//                Integer isGift = orderItem.getIsGift();
//                //如果不是赠品，跳过
//                if (!YesNoEnum.Y.getVal().equals(isGift)) {
//                    continue;
//                }

                OcBOrderCycleBuyInfo pickNewOrder = new OcBOrderCycleBuyInfo();
                pickNewOrder.setId(ModelUtil.getSequence("oc_b_order_cycle_buy_info"));
                pickNewOrder.setOcBOrderId(ocBOrder.getId());
                pickNewOrder.setBillNo(ocBOrder.getBillNo());
                pickNewOrder.setTid(ocBOrder.getTid());
                pickNewOrder.setBusinessTypeId(ocBOrder.getBusinessTypeId());
                pickNewOrder.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
                //提货金额
                pickNewOrder.setPickGoodsAmt(orderItem.getRealAmt());
                pickNewOrder.setOrderItemId(orderItem.getId());
                pickNewOrder.setSkuId(orderItem.getPsCSkuId());
                pickNewOrder.setIsGift(orderItem.getIsGift());
                pickNewOrder.setSkuCode(orderItem.getPsCSkuEcode());

                //defaultValue
                BaseModelUtil.initialBaseModelSystemField(pickNewOrder);
                newInfos.add(pickNewOrder);
            }
        }

        return newInfos;
    }

    /**
     * 周期购提货组装
     *
     * @param orderItemMap
     * @param cycleNumberMap
     * @param pickAmtMap
     * @return
     */
    private List<OcBOrderCycleBuyInfo> generatePickOrder(Map<Long, List<OcBOrderItem>> orderItemMap, Map<Integer,
            List<OcBOrder>> cycleNumberMap, Map<String, BigDecimal> pickAmtMap) {
        List<OcBOrderCycleBuyInfo> newInfos = Lists.newArrayList();
        for (Map.Entry<Integer, List<OcBOrder>> entry : cycleNumberMap.entrySet()) {
            //某一期下的所有提货订单
            List<OcBOrder> pickOrderList = entry.getValue();

            for (OcBOrder ocBOrder : pickOrderList) {
                List<OcBOrderItem> items = orderItemMap.get(ocBOrder.getId());
                List<OcBOrderItem> orderItems = items.stream().filter(p -> !YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(orderItems)) {
                    continue;
                }

                for (OcBOrderItem orderItem : orderItems) {
                    OcBOrderCycleBuyInfo pickNewOrder = new OcBOrderCycleBuyInfo();
                    pickNewOrder.setId(ModelUtil.getSequence("oc_b_order_cycle_buy_info"));
                    pickNewOrder.setOcBOrderId(ocBOrder.getId());
                    pickNewOrder.setBillNo(ocBOrder.getBillNo());
                    pickNewOrder.setTid(ocBOrder.getTid());
                    pickNewOrder.setBusinessTypeId(ocBOrder.getBusinessTypeId());
                    pickNewOrder.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
                    //周期购提数 不赋值
//                    pickNewOrder.setBuyNum();
                    //周期购剩余提数 不赋值
//                    pickNewOrder.setRemainingNum();

                    //提货金额
                    BigDecimal skuPickAmt = pickAmtMap.get(getItemPickKey(ocBOrder.getBillNo(), orderItem.getPsCSkuId(), orderItem.getIsGift()));
                    if (!Objects.isNull(skuPickAmt)) {
                        pickNewOrder.setPickGoodsAmt(skuPickAmt);
                    }

                    pickNewOrder.setOrderItemId(orderItem.getId());
                    pickNewOrder.setSkuId(orderItem.getPsCSkuId());
                    pickNewOrder.setIsGift(orderItem.getIsGift());
                    pickNewOrder.setSkuCode(orderItem.getPsCSkuEcode());

                    //defaultValue
                    BaseModelUtil.initialBaseModelSystemField(pickNewOrder);
                    newInfos.add(pickNewOrder);
                }
            }
        }
        return newInfos;
    }

    /**
     * 提货金额计算明细维度提货金额
     *
     * @param billNo
     * @param skuId
     * @param isGift
     * @return
     */
    private String getItemPickKey(String billNo, Long skuId, Integer isGift) {
        return billNo + "," + skuId + "," + isGift;
    }

    private List<OcBOrderCycleBuyInfo> generatePickOrder(OcBOrder ocBOrder, BigDecimal pickGoodsAmt, Map<Long, List<OcBOrderItem>> orderIdItemMap) {
        List<OcBOrderItem> items = orderIdItemMap.get(ocBOrder.getId());
        if (CollectionUtils.isEmpty(items)) {
            return Lists.newArrayList();
        }
        List<OcBOrderItem> orderItems = items.stream().filter(p -> !YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItems)) {
            return Lists.newArrayList();
        }

        //商品明细分摊提货金额
        Map<Integer, BigDecimal> sizeAmtMap = Maps.newHashMap();
        sizeAmtMap.put(1, pickGoodsAmt);
        Map<String, BigDecimal> map = orderPickAmtCalGift(orderIdItemMap, Lists.newArrayList(ocBOrder), sizeAmtMap);
        log.info("generatePickOrder orderId:{},pickGoodsAmt:{}, map:{}", ocBOrder.getId(), pickGoodsAmt, JSON.toJSONString(map));

        List<OcBOrderCycleBuyInfo> newInfos = Lists.newArrayList();
        for (OcBOrderItem orderItem : orderItems) {
            OcBOrderCycleBuyInfo pickNewOrder = new OcBOrderCycleBuyInfo();
            pickNewOrder.setId(ModelUtil.getSequence("oc_b_order_cycle_buy_info"));
            pickNewOrder.setOcBOrderId(ocBOrder.getId());
            pickNewOrder.setBillNo(ocBOrder.getBillNo());
            pickNewOrder.setTid(ocBOrder.getTid());
            pickNewOrder.setBusinessTypeId(ocBOrder.getBusinessTypeId());
            pickNewOrder.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
            pickNewOrder.setOrderItemId(orderItem.getId());
            pickNewOrder.setSkuId(orderItem.getPsCSkuId());
            pickNewOrder.setIsGift(orderItem.getIsGift());
            pickNewOrder.setSkuCode(orderItem.getPsCSkuEcode());
            BigDecimal itemPickAmt = map.get(getItemPickKey(ocBOrder.getBillNo(), orderItem.getPsCSkuId(), orderItem.getIsGift()));
            pickNewOrder.setPickGoodsAmt(itemPickAmt == null ? BigDecimal.ZERO : itemPickAmt);
            //defaultValue
            BaseModelUtil.initialBaseModelSystemField(pickNewOrder);
            newInfos.add(pickNewOrder);
        }
        return newInfos;
    }

    /**
     * 周期购订单组装
     *
     * @param cycleOrder
     * @param cycleNumber
     * @param cycleNoGiftItems
     * @return
     */
    private List<OcBOrderCycleBuyInfo> generateCycleOrder(OcBOrder cycleOrder, Integer cycleNumber, List<OcBOrderItem> cycleNoGiftItems) {
        if (CollectionUtils.isEmpty(cycleNoGiftItems)) {
            return Lists.newArrayList();
        }
        List<OcBOrderCycleBuyInfo> newInfos = Lists.newArrayList();
        for (OcBOrderItem cycleNoGiftItem : cycleNoGiftItems) {
            OcBOrderCycleBuyInfo cycleNewInfo = new OcBOrderCycleBuyInfo();
            cycleNewInfo.setId(ModelUtil.getSequence("oc_b_order_cycle_buy_info"));
            cycleNewInfo.setOcBOrderId(cycleOrder.getId());
            cycleNewInfo.setBillNo(cycleOrder.getBillNo());
            cycleNewInfo.setTid(cycleOrder.getTid());
            cycleNewInfo.setBusinessTypeId(cycleOrder.getBusinessTypeId());
            cycleNewInfo.setBusinessTypeCode(cycleOrder.getBusinessTypeCode());
            //周期购提数
            cycleNewInfo.setBuyNum(cycleNumber);
            //周期购剩余提数（中台周期购订单下的所有状态=待寻源。创建订单，默认是周期购提数）
            cycleNewInfo.setRemainingNum(cycleNumber);
            //提货金额 不赋值
//            cycleNewInfo.setPickGoodsAmt();
            cycleNewInfo.setOrderItemId(cycleNoGiftItem.getId());
            cycleNewInfo.setSkuId(cycleNoGiftItem.getPsCSkuId());
            cycleNewInfo.setIsGift(cycleNoGiftItem.getIsGift());
            cycleNewInfo.setSkuCode(cycleNoGiftItem.getPsCSkuEcode());

            //defaultValue
            BaseModelUtil.initialBaseModelSystemField(cycleNewInfo);
            newInfos.add(cycleNewInfo);
        }
        return newInfos;
    }

    /**
     * 周期购提货金额计算
     *
     * @param cycleOrder
     * @param cycleNumberMap
     * @param noGiftOrderItemMap
     * @return billNo+skuId,pickAmt
     */
    private Map<String, BigDecimal> pickAmt(OcBOrder cycleOrder, Map<Integer, List<OcBOrder>> cycleNumberMap, Map<Long, List<OcBOrderItem>> noGiftOrderItemMap) {
        //billNo+skuId,pickAmt
        Map<String, BigDecimal> map = Maps.newHashMap();

        //每一期的提货金额
        Map<Integer, BigDecimal> cycleNumAmtMap = averageAmount(cycleOrder.getOrderAmt(), cycleNumberMap);

        for (Map.Entry<Integer, List<OcBOrder>> entry : cycleNumberMap.entrySet()) {
            Integer cycleNumber = entry.getKey();
            List<OcBOrder> orders = entry.getValue();

            //每期的金额
            BigDecimal cycleAmt = cycleNumAmtMap.get(cycleNumber);

            //每期的提货金额平均分（key只是数量不是期数）
            Map<Integer, BigDecimal> sizeAmtMap = averageAmount(cycleAmt, orders.size());

            //每个订单的提货金额计算
            map.putAll(orderPickAmtCal(noGiftOrderItemMap, orders, sizeAmtMap));
        }
        return map;
    }

    /**
     * 每个订单每个sku的提货金额计算
     *
     * @param orderIdItemMap
     * @param orders
     * @param sizeAmtMap
     */
    private Map<String, BigDecimal> orderPickAmtCal(Map<Long, List<OcBOrderItem>> orderIdItemMap, List<OcBOrder> orders, Map<Integer, BigDecimal> sizeAmtMap) {
        Map<String, BigDecimal> map = Maps.newHashMap();
        //排个小序
        orders = orders.stream().sorted(Comparator.comparing(OcBOrder::getId).reversed()).collect(Collectors.toList());
        for (int i = 1; i < orders.size() + 1; i++) {
            OcBOrder ocBOrder = orders.get(i - 1);
            String billNo = ocBOrder.getBillNo();
            //订单明细
            List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(ocBOrder.getId());
            if (CollectionUtils.isEmpty(ocBOrderItems)) {
                log.info("orderPickAmtCal 订单明细为空，订单号：{}", billNo);
                continue;
            }
            //获取订单明细里所有赠品的总成交金额
            BigDecimal orderGiftAmt = ocBOrderItems.stream().filter(p -> YesNoEnum.Y.getVal().equals(p.getIsGift())).map(OcBOrderItem::getRealAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            //订单总金额(去掉赠品成交金额)
            BigDecimal orderAmt = ocBOrder.getOrderAmt().subtract(orderGiftAmt);

            BigDecimal pickAmt = sizeAmtMap.get(i);
            if (pickAmt == null) {
                pickAmt = BigDecimal.ZERO;
            }
            //提货金额（去掉赠品成交金额）
            pickAmt = pickAmt.subtract(orderGiftAmt);
            //尾差
            BigDecimal remainAmt = pickAmt;
            //ocBOrderItems去掉赠品,按照成交金额从高到低排序
            ocBOrderItems = ocBOrderItems.stream().filter(p -> !YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ocBOrderItems)) {
                continue;
            }
            ocBOrderItems = ocBOrderItems.stream().sorted(Comparator.comparing(OcBOrderItem::getRealAmt).reversed()).collect(Collectors.toList());
            //明细按照成交金额占比计算
            for (int j = 0; j < ocBOrderItems.size(); j++) {
                OcBOrderItem ocBOrderItem = ocBOrderItems.get(j);
                Long psCSkuId = ocBOrderItem.getPsCSkuId();
                Integer isGift = ocBOrderItem.getIsGift();

                if (orderAmt.compareTo(BigDecimal.ZERO) == 0) {
                    map.put(getItemPickKey(billNo, psCSkuId, isGift), BigDecimal.ZERO);
                    continue;
                }

                BigDecimal skuPickAmt = pickAmt.multiply(ocBOrderItem.getRealAmt()).divide(orderAmt, 2, BigDecimal.ROUND_DOWN);

                if (i == ocBOrderItems.size() - 1) {
                    map.put(getItemPickKey(billNo, psCSkuId, isGift), remainAmt);
                } else {
                    map.put(getItemPickKey(billNo, psCSkuId, isGift), skuPickAmt);
                }

                remainAmt = remainAmt.subtract(skuPickAmt);
            }
        }
        return map;
    }

    /**
     * 提货金额计算不去掉赠品金额
     *
     * @param orderIdItemMap
     * @param orders
     * @param sizeAmtMap
     * @return
     */
    private Map<String, BigDecimal> orderPickAmtCalGift(Map<Long, List<OcBOrderItem>> orderIdItemMap, List<OcBOrder> orders, Map<Integer, BigDecimal> sizeAmtMap) {
        Map<String, BigDecimal> map = Maps.newHashMap();
        //排个小序
        orders = orders.stream().sorted(Comparator.comparing(OcBOrder::getId).reversed()).collect(Collectors.toList());
        for (int i = 1; i < orders.size() + 1; i++) {
            OcBOrder ocBOrder = orders.get(i - 1);
            String billNo = ocBOrder.getBillNo();
            //订单明细
            List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(ocBOrder.getId());
            if (CollectionUtils.isEmpty(ocBOrderItems)) {
                log.info("orderPickAmtCal 订单明细为空，订单号：{}", billNo);
                continue;
            }
            //获取订单明细里所有赠品的总成交金额
            BigDecimal orderGiftAmt = ocBOrderItems.stream().filter(p -> YesNoEnum.Y.getVal().equals(p.getIsGift())).map(OcBOrderItem::getRealAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            //订单总金额(去掉赠品成交金额)
            BigDecimal orderAmt = ocBOrder.getOrderAmt().subtract(orderGiftAmt);

            BigDecimal pickAmt = sizeAmtMap.get(i);
            if (pickAmt == null) {
                pickAmt = BigDecimal.ZERO;
            }

            //尾差
            BigDecimal remainAmt = pickAmt;
            //ocBOrderItems去掉赠品,按照成交金额从高到低排序
            ocBOrderItems = ocBOrderItems.stream().filter(p -> !YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ocBOrderItems)) {
                continue;
            }
            ocBOrderItems = ocBOrderItems.stream().sorted(Comparator.comparing(OcBOrderItem::getRealAmt).reversed()).collect(Collectors.toList());
            //明细按照成交金额占比计算
            for (int j = 0; j < ocBOrderItems.size(); j++) {
                OcBOrderItem ocBOrderItem = ocBOrderItems.get(j);
                Long psCSkuId = ocBOrderItem.getPsCSkuId();
                Integer isGift = ocBOrderItem.getIsGift();

                if (orderAmt.compareTo(BigDecimal.ZERO) == 0) {
                    map.put(getItemPickKey(billNo, psCSkuId, isGift), BigDecimal.ZERO);
                    continue;
                }

                BigDecimal skuPickAmt = pickAmt.multiply(ocBOrderItem.getRealAmt()).divide(orderAmt, 2, BigDecimal.ROUND_DOWN);

                if (i == ocBOrderItems.size() - 1) {
                    map.put(getItemPickKey(billNo, psCSkuId, isGift), remainAmt);
                } else {
                    map.put(getItemPickKey(billNo, psCSkuId, isGift), skuPickAmt);
                }

                remainAmt = remainAmt.subtract(skuPickAmt);
            }
        }
        return map;
    }

    /**
     * 金额根据数量平均分-尾差
     * 期数对应的金额
     *
     * @param amount
     * @param noPickedMap
     * @return
     */
    private Map<Integer, BigDecimal> averageAmount(BigDecimal amount, Map<Integer, List<OcBOrder>> noPickedMap) {
        Map<Integer, BigDecimal> map = Maps.newHashMap();
        if (MapUtils.isEmpty(noPickedMap)) {
            return map;
        }

        // 计算平均金额（不包括尾差）
        BigDecimal averageAmount = amount.divide(new BigDecimal(noPickedMap.size()), 2, RoundingMode.HALF_UP);

        // 计算尾差
        BigDecimal remainder = amount.subtract(averageAmount.multiply(new BigDecimal(noPickedMap.size())));

        // 分配平均金额和尾差
        int i = 1;
        for (Map.Entry<Integer, List<OcBOrder>> entry : noPickedMap.entrySet()) {
            Integer cycleNum = entry.getKey();
            if (i == noPickedMap.size()) {
                // 最后一个加上尾差
                map.put(cycleNum, averageAmount.add(remainder));
            } else {
                map.put(cycleNum, averageAmount);
            }
            i++;
        }

        return map;
    }

    /**
     * 平均分金额
     *
     * @param amount
     * @param num
     * @return
     */
    private Map<Integer, BigDecimal> averageAmount(BigDecimal amount, int num) {
        Map<Integer, BigDecimal> map = Maps.newHashMap();

        // 计算平均金额（不包括尾差）
        BigDecimal averageAmount = amount.divide(new BigDecimal(num), 2, RoundingMode.HALF_UP);

        // 计算尾差
        BigDecimal remainder = amount.subtract(averageAmount.multiply(new BigDecimal(num)));

        // 分配平均金额和尾差
        for (int i = 1; i < num + 1; i++) {
            if (i == num) {
                // 最后一个加上尾差
                map.put(i, averageAmount.add(remainder));
            } else {
                map.put(i, averageAmount);
            }
        }

        return map;
    }

    /**
     * 周期购提货计算
     *
     * @param cycleConfirmList
     */
    public void changeByConfirm(List<CycleConfirmRequest> cycleConfirmList, boolean isReport) {
        if (CollectionUtils.isEmpty(cycleConfirmList)) {
            log.warn("changeByConfirm cycleConfirmList 周期购提货计算参数为空");
            return;
        }

        long timeMillis = System.currentTimeMillis();
        log.info(LogUtil.format("start cycleConfirmList cycleConfirmList:{}", "changeByConfirm"), JSON.toJSONString(cycleConfirmList));

        //对账信息按照billNo分组（周期购订单对账信息）。 key:billNo
        Map<String, List<CycleConfirmRequest>> billNoConfirmMap = cycleConfirmList.stream().collect(Collectors.groupingBy(CycleConfirmRequest::getBillNo));

        List<String> tids = cycleConfirmList.stream().map(CycleConfirmRequest::getTid).collect(Collectors.toList());
        List<Long> esIdList = GSI4Order.getIdListBySourceCodes(tids);
        //订单（包括周期购订单&周期购提货）
        List<OcBOrder> orderList = orderMapper.selectByIdsList(esIdList);
        if (CollectionUtils.isEmpty(orderList)) {
            log.warn(LogUtil.format("changeByConfirm orderList is empty tids:{}", "changeByConfirm"), tids);
            return;
        }

        //计算平台单号维度的周期购状态，用于冲抵金额里的周期购状态判断
        Map<String, CycleSalesReportCycleStatusEnum> tidCycleStatusMap = getTidCycleStatusMap(orderList);

        //过滤取消和作废的单据
        orderList = orderList.stream().filter(p -> !OrderStatusEnum.CANCELLED.getVal().equals(p.getOrderStatus()) && !OrderStatusEnum.SYS_VOID.getVal().equals(p.getOrderStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            log.warn(LogUtil.format("changeByConfirm orderList CANCELLED SYS_VOID is empty tids:{}", "changeByConfirm"), tids);
            return;
        }

        //订单明细
        List<OcBOrderItem> bOrderItems = orderItemMapper.selectAllStatusOrderItemsByOrderIds(orderList.stream().map(OcBOrder::getId).collect(Collectors.toList()));
//        bOrderItems = bOrderItems.stream().filter(p -> !YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
        Map<Long, List<OcBOrderItem>> orderIdItemMap = bOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));

        //需要计算的订单（包括周期购订单&周期购提货，过滤复制和补发）
        List<OcBOrder> orders = needCalOrders(orderList, orderIdItemMap);

        //周期购订单
        List<OcBOrder> cycleOrders = orders.stream().filter(p -> OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(p.getBusinessTypeCode())).collect(Collectors.toList());
        Map<String, OcBOrder> cycleBillNoMap = cycleOrders.stream().collect(Collectors.toMap(OcBOrder::getBillNo, x -> x, (a, b) -> a));

        //周期购提货
        List<OcBOrder> cyclePickOrders = orders.stream().filter(p -> OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(p.getBusinessTypeCode())).collect(Collectors.toList());
        for (OcBOrder ocBOrder : cyclePickOrders) {
            Integer currentCycleNumber = ocBOrder.getCurrentCycleNumber();
            if (currentCycleNumber == null) {
                ocBOrder.setCurrentCycleNumber(0);
            }
        }
        //周期购提货-按照平台单号分组
        Map<String, List<OcBOrder>> cyclePickMap = cyclePickOrders.stream().collect(Collectors.groupingBy(OcBOrder::getTid));

        //周期购额外信息
        List<OcBOrderCycleBuyInfo> existCycleInfos = orderCycleBuyInfoMapper.getByTids(Lists.newArrayList(orders.stream().map(OcBOrder::getTid).collect(Collectors.toSet())));
        //周期购额外信息订单补全（拆单、取消等导致订单数量变化）
//        cycleBuyInfoImprove(orderIdItemMap, cyclePickOrders, existCycleInfos);
        //周期购额外信息（周期购额外信息订单补全处理完后重新查一遍最新的）
        existCycleInfos = orderCycleBuyInfoMapper.getByTids(Lists.newArrayList(orders.stream().map(OcBOrder::getTid).collect(Collectors.toSet())));
        Map<String, OcBOrderCycleBuyInfo> billNoSkuCycleInfoMap = existCycleInfos.stream().collect(Collectors.toMap(x -> x.getBillNo() + "," + x.getSkuId() + "," + x.getIsGift(), x -> x, (a, b) -> a));
        Map<String, List<OcBOrderCycleBuyInfo>> tidCycleInfoMap = existCycleInfos.stream().collect(Collectors.groupingBy(OcBOrderCycleBuyInfo::getTid));

        List<CycleOffsetAmountRequest> cycleOffsetAmountRequests = Lists.newArrayList();
        //按照对账信息处理每条周期购订单
        for (Map.Entry<String, List<CycleConfirmRequest>> entry : billNoConfirmMap.entrySet()) {
            String billNo = entry.getKey();
            List<CycleConfirmRequest> cycleConfirms = entry.getValue();
            //默认一条 OM单号维度
            CycleConfirmRequest confirmInfo = cycleConfirms.get(0);
            String tid = confirmInfo.getTid();

            //周期购订单
            OcBOrder cycleOrder = cycleBillNoMap.get(billNo);
            if (cycleOrder == null) {
                log.info(LogUtil.format("changeByConfirm cycleOrder is empty billNo:{},tid:{}", "changeByConfirm"), billNo, tid);
                continue;
            }

            BigDecimal offsetAmt = null;

            //-------------------周期购订单计算begin---------------------
            //中台周期购订单额外信息
            List<OcBOrderCycleBuyInfo> cycleBuyInfos = tidCycleInfoMap.get(tid);
            if (CollectionUtils.isEmpty(cycleBuyInfos)) {
                log.warn(LogUtil.format("changeByConfirm cycleBuyInfos is empty billNo:{}", "changeByConfirm"), billNo);
                continue;
            }
            List<OcBOrderCycleBuyInfo> cycleInfos = cycleBuyInfos.stream().filter(p ->
                    //周期购订单&OM单号匹配
                    OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(p.getBusinessTypeCode()) && p.getBillNo().equals(billNo))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(cycleInfos)) {
                log.warn(LogUtil.format("changeByConfirm cycleBuyInfo empty billNo:{}", "changeByConfirm"), billNo);
                continue;
            }
            OcBOrderCycleBuyInfo cycleBuyInfo = cycleInfos.get(0);

            //剩余提数计算
            int pickNum = 0;
            List<OcBOrder> pickOrders = cyclePickMap.get(tid);
            if (CollectionUtils.isEmpty(pickOrders)) {
                log.warn(LogUtil.format("changeByConfirm pickOrders is empty billNo:{}", "changeByConfirm"), billNo);
                continue;
            }

            //按期数分组
            Map<Integer, List<OcBOrder>> cyclePickNumberMap = pickOrders.stream().collect(Collectors.groupingBy(OcBOrder::getCurrentCycleNumber));
            for (Map.Entry<Integer, List<OcBOrder>> listEntry : cyclePickNumberMap.entrySet()) {
                List<OcBOrder> ocBOrders = listEntry.getValue();
                List<Integer> orderStatus = ocBOrders.stream().map(OcBOrder::getOrderStatus).collect(Collectors.toList());
                //如果包含待寻源，算剩余
                if (orderStatus.contains(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())) {
                    pickNum++;
                }
            }

            OcBOrderCycleBuyInfo updateCycle = new OcBOrderCycleBuyInfo();
            updateCycle.setId(cycleBuyInfo.getId());
            updateCycle.setModifieddate(new Date());
            updateCycle.setRemainingNum(pickNum);

            //-------------------周期购订单计算end---------------------


            //-------------------周期购提货计算begin---------------------
            //周期购状态
            CycleSalesReportCycleStatusEnum cycleStatus = getCycleStatus(pickOrders);

            Map<Integer, List<OcBOrder>> allPickMap = pickOrders.stream().collect(Collectors.groupingBy(OcBOrder::getCurrentCycleNumber));
            Pair<Map<Integer, List<OcBOrder>>, Map<Integer, List<OcBOrder>>> pair = getPickedCurrentNumMap(allPickMap);
            if (pair == null) {
                pair = new ImmutablePair<>(Maps.newHashMap(), Maps.newHashMap());
            }
            //已提订单（key:期数，value:对应订单）
            Map<Integer, List<OcBOrder>> pickedMap = pair.getLeft();
            //提货总金额
            BigDecimal pickedTotalAmt = pickedTotalAmt(existCycleInfos, pickedMap);

            //未提订单（key:期数，value:对应订单）
            Map<Integer, List<OcBOrder>> noPickedMap = pair.getRight();

            //金额计算（剩余每提提货金额、冲销金额、金额记录变动等）
            Map<String, BigDecimal> billNoSkuGiftPickAmtMap = Maps.newHashMap();
            //已对账
            if (Boolean.TRUE.equals(confirmInfo.getIsConfirm())) {
                //未提货 剩余每提提货金额 = 对账金额/期数
                if (cycleStatus.getCode() == CycleSalesReportCycleStatusEnum.PICK_NO.getCode()) {
                    //对账金额
                    BigDecimal confirmAmt = confirmInfo.getAmt();

                    //赠品提货金额
//                    BigDecimal giftAmt = getGiftAmt(orderIdItemMap, noPickedMap);

                    //获取全是赠品订单的赠品金额
                    Map<Integer, BigDecimal> allGiftOrderMap = getAllGiftOrder(orderIdItemMap, noPickedMap);

                    //获取整期都是赠品的订单暑期和金额
                    BigDecimal allOrderGiftAmt = getCurrentNumberAllGiftOrder(orderIdItemMap, noPickedMap);

                    //去掉全是赠品的订单
                    noPickedMap = removeAllGiftOrder(orderIdItemMap, noPickedMap);

                    //每一期的提货金额
                    Map<Integer, BigDecimal> cycleNumAmtMap = averageAmount(confirmAmt.subtract(allOrderGiftAmt), noPickedMap);

                    for (Map.Entry<Integer, List<OcBOrder>> integerListEntry : noPickedMap.entrySet()) {
                        Integer cycleNumber = integerListEntry.getKey();
                        List<OcBOrder> ocBOrders = integerListEntry.getValue();

                        //每期的金额
                        BigDecimal cycleAmt = cycleNumAmtMap.get(cycleNumber);
                        BigDecimal giftAmt = allGiftOrderMap.get(cycleNumber);

                        //期数对应的所有订单的提货金额(每期的提货金额平均分（key只是数量不是期数）)
                        Map<Integer, BigDecimal> sizeAmtMap = averageAmount(cycleAmt.subtract(giftAmt), ocBOrders.size());

                        //每个订单的提货金额计算(key:billNo+skuId+isgift)
                        billNoSkuGiftPickAmtMap.putAll(orderPickAmtCal(orderIdItemMap, ocBOrders, sizeAmtMap));
                    }
                }

                //提货完毕&已作废
                if (cycleStatus.getCode() == CycleSalesReportCycleStatusEnum.PICK_COMPLETE.getCode() ||
                        cycleStatus.getCode() == CycleSalesReportCycleStatusEnum.PICK_CANCEL.getCode()) {
                    //剩余每提提货金额=0
                    for (Map.Entry<Integer, List<OcBOrder>> listEntry : noPickedMap.entrySet()) {
                        List<OcBOrder> ocBOrders = listEntry.getValue();
                        for (OcBOrder ocBOrder : ocBOrders) {
                            List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(ocBOrder.getId());
                            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                                billNoSkuGiftPickAmtMap.put(getItemPickKey(ocBOrder.getBillNo(), ocBOrderItem.getPsCSkuId(), ocBOrderItem.getIsGift()), BigDecimal.ZERO);
                            }
                        }
                    }

                    if (!isReport) {
                        //冲抵金额:对账金额-成交金额
                        offsetAmt = confirmInfo.getAmt().subtract(cycleOrder.getOrderAmt());
                        //如果生成冲抵时实时判断周期购状态为已作废，就不生成奶卡冲抵，也就不会传SAP
                        CycleSalesReportCycleStatusEnum anEnum = tidCycleStatusMap.get(tid);
                        anEnum = anEnum == null ? CycleSalesReportCycleStatusEnum.NULL : anEnum;
                        if (offsetAmt.compareTo(BigDecimal.ZERO) != 0 && CycleSalesReportCycleStatusEnum.PICK_CANCEL.getCode() != anEnum.getCode()) {
                            //奶卡提货金额冲抵
                            calCycleOffsetAmt(orderIdItemMap, cycleOffsetAmountRequests, cycleOrder, pickedMap, offsetAmt);
                        }
                    }
                }

                //提货中（部分提货）
                if (cycleStatus.getCode() == CycleSalesReportCycleStatusEnum.PICK_ING.getCode()) {
                    //提货总金额＜对账金额-0.5------->提货金额：（对账金额-提货金额）/剩余期数
                    BigDecimal amt = confirmInfo.getAmt();
                    if (pickedTotalAmt.compareTo(amt.subtract(new BigDecimal("0.5"))) < 0) {
                        //赠品提货金额
//                        BigDecimal giftAmt = getGiftAmt(orderIdItemMap, noPickedMap);

                        //获取全是赠品订单的赠品金额
                        Map<Integer, BigDecimal> allGiftOrderMap = getAllGiftOrder(orderIdItemMap, noPickedMap);

                        //获取整期都是赠品的订单暑期和金额
                        BigDecimal allOrderGiftAmt = getCurrentNumberAllGiftOrder(orderIdItemMap, noPickedMap);

                        //去掉全是赠品的订单
                        noPickedMap = removeAllGiftOrder(orderIdItemMap, noPickedMap);

                        //剩余每提提货金额=对账金额-已提货总金额/剩余期数
                        Map<Integer, BigDecimal> cycleNumAmtMap = averageAmount(amt.subtract(allOrderGiftAmt).subtract(pickedTotalAmt), noPickedMap);
                        for (Map.Entry<Integer, List<OcBOrder>> integerListEntry : noPickedMap.entrySet()) {
                            Integer cycleNumber = integerListEntry.getKey();
                            List<OcBOrder> ocBOrders = integerListEntry.getValue();

                            //每期的金额
                            BigDecimal cycleAmt = cycleNumAmtMap.get(cycleNumber);
                            BigDecimal giftAmt = allGiftOrderMap.get(cycleNumber);

                            //期数对应的所有订单的提货金额(每期的提货金额平均分（key只是数量不是期数）)
                            Map<Integer, BigDecimal> sizeAmtMap = averageAmount(cycleAmt.subtract(giftAmt), ocBOrders.size());

                            //每个订单的提货金额计算(key:billNo+skuId)
                            billNoSkuGiftPickAmtMap.putAll(orderPickAmtCal(orderIdItemMap, ocBOrders, sizeAmtMap));
                        }
                    }

                    //对账金额-0.5≤提货总金额≤对账金额------->提货金额：0.01*该期所有商品数量总和
                    if (pickedTotalAmt.compareTo(amt.subtract(new BigDecimal("0.5"))) >= 0 && pickedTotalAmt.compareTo(amt) <= 0) {
//                        BigDecimal pickTotalAmt = BigDecimal.ZERO;
                        //剩余每提提货金额=0.01*该期所有商品数量总和
                        for (Map.Entry<Integer, List<OcBOrder>> listEntry : noPickedMap.entrySet()) {
                            List<OcBOrder> ocBOrders = listEntry.getValue();
                            for (OcBOrder ocBOrder : ocBOrders) {
                                List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(ocBOrder.getId());
                                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                                    //0.01*明细数量
                                    BigDecimal itemAmt = new BigDecimal("0.01").multiply(ocBOrderItem.getQty());
                                    if (offsetAmt == null) {
                                        offsetAmt = itemAmt;
                                    } else {
                                        offsetAmt = offsetAmt.add(itemAmt);
                                    }
                                    billNoSkuGiftPickAmtMap.put(getItemPickKey(ocBOrder.getBillNo(), ocBOrderItem.getPsCSkuId(), ocBOrderItem.getIsGift()), itemAmt);
                                }
                            }
                        }

                        if (!isReport) {
                            //冲抵金额:剩余每期提货金额之和
//                            offsetAmt = new BigDecimal(noPickedMap.size()).multiply(new BigDecimal("0.01"));
                            if (offsetAmt.compareTo(BigDecimal.ZERO) != 0) {
                                //奶卡提货金额冲抵
                                calCycleOffsetAmt(orderIdItemMap, cycleOffsetAmountRequests, cycleOrder, pickedMap, offsetAmt);
                            }
                        }
                    }

                    //提货总金额＞对账金额------->提货金额：0.01*该期所有商品数量总和
                    if (pickedTotalAmt.compareTo(amt) > 0) {
//                        BigDecimal pickTotalAmt = BigDecimal.ZERO;
                        //剩余每提提货金额=0.01
                        for (Map.Entry<Integer, List<OcBOrder>> listEntry : noPickedMap.entrySet()) {
                            List<OcBOrder> ocBOrders = listEntry.getValue();
                            for (OcBOrder ocBOrder : ocBOrders) {
                                List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(ocBOrder.getId());
                                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                                    //0.01*明细数量
                                    BigDecimal itemAmt = new BigDecimal("0.01").multiply(ocBOrderItem.getQty());
                                    if (offsetAmt == null) {
                                        offsetAmt = itemAmt;
                                    } else {
                                        offsetAmt = offsetAmt.add(itemAmt);
                                    }
                                    billNoSkuGiftPickAmtMap.put(getItemPickKey(ocBOrder.getBillNo(), ocBOrderItem.getPsCSkuId(), ocBOrderItem.getIsGift()), itemAmt);
                                }
                            }
                        }

                        if (!isReport) {
                            //冲抵金额:对账金额-已提货总金额-剩余每期提货金额之和
                            if (offsetAmt == null) {
                                offsetAmt = BigDecimal.ZERO;
                            }
                            offsetAmt = amt.subtract(pickedTotalAmt).subtract(offsetAmt);
                            if (offsetAmt.compareTo(BigDecimal.ZERO) != 0) {
                                //奶卡提货金额冲抵
                                calCycleOffsetAmt(orderIdItemMap, cycleOffsetAmountRequests, cycleOrder, pickedMap, offsetAmt);
                            }
                        }
                    }
                }
            } else {
                //赠品提货金额
//                BigDecimal giftAmt = getGiftAmt(orderIdItemMap, noPickedMap);

                //获取全是赠品订单的赠品金额
                Map<Integer, BigDecimal> allGiftOrderMap = getAllGiftOrder(orderIdItemMap, noPickedMap);

                //获取整期都是赠品的订单暑期和金额
                BigDecimal allOrderGiftAmt = getCurrentNumberAllGiftOrder(orderIdItemMap, noPickedMap);

                //去掉全是赠品的订单
                noPickedMap = removeAllGiftOrder(orderIdItemMap, noPickedMap);

                //成交金额-已提金额
                BigDecimal noPickedAmt = cycleOrder.getOrderAmt().subtract(pickedTotalAmt);

                //每一期的提货金额
                Map<Integer, BigDecimal> cycleNumAmtMap = averageAmount(noPickedAmt.subtract(allOrderGiftAmt), noPickedMap);

                for (Map.Entry<Integer, List<OcBOrder>> integerListEntry : noPickedMap.entrySet()) {
                    Integer cycleNumber = integerListEntry.getKey();
                    List<OcBOrder> ocBOrders = integerListEntry.getValue();

                    //每期的金额
                    BigDecimal cycleAmt = cycleNumAmtMap.get(cycleNumber);
                    BigDecimal giftAmt = allGiftOrderMap.get(cycleNumber);

                    //期数对应的所有订单的提货金额(每期的提货金额平均分（key只是数量不是期数）)
                    Map<Integer, BigDecimal> sizeAmtMap = averageAmount(cycleAmt.subtract(giftAmt), ocBOrders.size());

                    //每个订单的提货金额计算(key:billNo+skuId)
                    billNoSkuGiftPickAmtMap.putAll(orderPickAmtCal(orderIdItemMap, ocBOrders, sizeAmtMap));
                }
            }

            //变化数据持久化
            if (!isReport && offsetAmt == null) {
                offsetAmt = BigDecimal.ZERO;
            }
            dataUpdate(billNoSkuCycleInfoMap, confirmInfo, cycleOrder, offsetAmt, updateCycle, billNoSkuGiftPickAmtMap, isReport);
        }

        //-------------------周期购提货计算end---------------------

        //提货冲抵
        if (CollectionUtils.isNotEmpty(cycleOffsetAmountRequests)) {
            log.info(LogUtil.format("提货冲抵 cycleOffsetAmountRequests：{}", "changeByConfirm"), JSON.toJSONString(cycleOffsetAmountRequests));
            //提奶金额冲抵数据
            Map<String, SgBSapInformationMapping> offSetSapTypeMap = getOffSetSapInfo(Lists.newArrayList(OmsMiddleGroundBillTypeEnum.RYCD03.getCode(), OmsMiddleGroundBillTypeEnum.RYCD04.getCode()));
            //获取所有商品
            Map<String, PsCProSkuResult> psCProSkuResultMap = querySku(cycleOffsetAmountRequests);
            //冲抵
            Map<String, String> errorOffSetMap = cycleOffset(cycleOffsetAmountRequests, offSetSapTypeMap, psCProSkuResultMap);
            log.info(LogUtil.format("周期购提货冲抵数据处理完成 errorOffSetMap:{}", "changeByConfirm"), JSON.toJSONString(errorOffSetMap));
        }

        log.info(LogUtil.format("end cycleConfirmList cycleConfirmList:{},time-ms:{}", "changeByConfirm"), JSON.toJSONString(cycleConfirmList), System.currentTimeMillis() - timeMillis);
    }

    private Map<String, CycleSalesReportCycleStatusEnum> getTidCycleStatusMap(List<OcBOrder> orderList) {
        Map<String, CycleSalesReportCycleStatusEnum> tidCycleStatusMap = Maps.newHashMap();
        Map<String, List<OcBOrder>> tidMap = orderList.stream().collect(Collectors.groupingBy(OcBOrder::getTid));
        for (Map.Entry<String, List<OcBOrder>> entry : tidMap.entrySet()) {
            List<OcBOrder> cyclePickOrders =
                    entry.getValue().stream().filter(p -> OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(p.getBusinessTypeCode())).collect(Collectors.toList());
            CycleSalesReportCycleStatusEnum cycleStatus = getCycleStatus(cyclePickOrders);
            tidCycleStatusMap.put(entry.getKey(), cycleStatus);
        }
        return tidCycleStatusMap;
    }

    private BigDecimal getGiftAmt(Map<Long, List<OcBOrderItem>> orderIdItemMap, Map<Integer, List<OcBOrder>> noPickedMap) {
        BigDecimal giftAmt = BigDecimal.ZERO;
        for (Map.Entry<Integer, List<OcBOrder>> listEntry : noPickedMap.entrySet()) {
            List<OcBOrder> ocBOrders = listEntry.getValue();
            for (OcBOrder ocBOrder : ocBOrders) {
                List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(ocBOrder.getId());
                //赠品
                List<OcBOrderItem> giftItems = ocBOrderItems.stream().filter(p -> YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(giftItems)) {
                    giftAmt = giftAmt.add(giftItems.stream().map(OcBOrderItem::getRealAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }
        }
        return giftAmt;
    }

    private Map<Integer, List<OcBOrder>> removeAllGiftOrder(Map<Long, List<OcBOrderItem>> orderIdItemMap, Map<Integer, List<OcBOrder>> noPickedMap) {
        Map<Integer, List<OcBOrder>> noPickedNoGiftMap = Maps.newHashMap();
        for (Map.Entry<Integer, List<OcBOrder>> listEntry : noPickedMap.entrySet()) {
            List<OcBOrder> ocBOrders = listEntry.getValue();
            for (OcBOrder ocBOrder : ocBOrders) {
                List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(ocBOrder.getId());
                //查询赠品订单
                List<OcBOrderItem> giftItems = ocBOrderItems.stream().filter(p -> YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
                if (giftItems.size() == ocBOrderItems.size()) {
                    //全是赠品的订单不处理
                    log.info(LogUtil.format("changeByConfirm all gift order billNo:{}", "changeByConfirm"), ocBOrder.getBillNo());
                } else {
                    List<OcBOrder> ocBOrderList = noPickedNoGiftMap.get(listEntry.getKey());
                    if (CollectionUtils.isEmpty(ocBOrderList)) {
                        noPickedNoGiftMap.put(listEntry.getKey(), Lists.newArrayList(ocBOrder));
                    } else {
                        ocBOrderList.add(ocBOrder);
                    }
                }
            }
        }

        return noPickedNoGiftMap;
    }

    /**
     * 获取全是赠品订单的赠品金额
     * @param orderIdItemMap
     * @param noPickedMap
     * @return
     */
    private Map<Integer,BigDecimal> getAllGiftOrder(Map<Long, List<OcBOrderItem>> orderIdItemMap, Map<Integer, List<OcBOrder>> noPickedMap) {
        Map<Integer,BigDecimal> map = Maps.newHashMap();
        for (Map.Entry<Integer, List<OcBOrder>> listEntry : noPickedMap.entrySet()) {
            BigDecimal giftAmt = BigDecimal.ZERO;
            List<OcBOrder> ocBOrders = listEntry.getValue();
            for (OcBOrder ocBOrder : ocBOrders) {
                List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(ocBOrder.getId());
                //查询赠品订单
                List<OcBOrderItem> giftItems = ocBOrderItems.stream().filter(p -> YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
                if (giftItems.size() == ocBOrderItems.size()) {
                    //全是赠品的订单不处理
                    giftAmt = giftAmt.add(giftItems.stream().map(OcBOrderItem::getRealAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                    log.info(LogUtil.format("changeByConfirm all gift order billNo:{}", "changeByConfirm"), ocBOrder.getBillNo());
                }
            }
            map.put(listEntry.getKey(),giftAmt);
        }
        return map;
    }

    /**
     * 获取全是赠品订单的赠品金额
     * @param orderIdItemMap
     * @param noPickedMap
     * @return
     */
    private BigDecimal getCurrentNumberAllGiftOrder(Map<Long, List<OcBOrderItem>> orderIdItemMap, Map<Integer, List<OcBOrder>> noPickedMap) {
        BigDecimal allAmt = BigDecimal.ZERO;
        for (Map.Entry<Integer, List<OcBOrder>> listEntry : noPickedMap.entrySet()) {
            BigDecimal giftAmt = BigDecimal.ZERO;
            List<OcBOrder> ocBOrders = listEntry.getValue();

            boolean isAll = true;

            for (OcBOrder ocBOrder : ocBOrders) {
                List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(ocBOrder.getId());
                //查询赠品订单
                List<OcBOrderItem> giftItems = ocBOrderItems.stream().filter(p -> YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
                if (giftItems.size() == ocBOrderItems.size()) {
                    //全是赠品的订单
                    giftAmt = giftAmt.add(giftItems.stream().map(OcBOrderItem::getRealAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
                } else {
                    isAll = false;
                    break;
                }
            }
            if (isAll) {
                allAmt = allAmt.add(giftAmt);
            }
        }
        return allAmt;
    }

    /**
     * 周期购额外信息资金等变动后记录数据
     *
     * @param billNoCycleInfoMap
     * @param confirmInfo
     * @param cycleOrder
     * @param offsetAmt
     * @param updateCycle
     * @param itemPickAmtMap
     */
    @Transactional(rollbackFor = Exception.class)
    public void dataUpdate(Map<String, OcBOrderCycleBuyInfo> billNoCycleInfoMap, CycleConfirmRequest confirmInfo, OcBOrder cycleOrder,
                           BigDecimal offsetAmt, OcBOrderCycleBuyInfo updateCycle, Map<String, BigDecimal> itemPickAmtMap, boolean isReport) {
        if (MapUtils.isNotEmpty(itemPickAmtMap)) {
            //按金额分组
            Map<BigDecimal, List<Long>> map = Maps.newHashMap();
            for (Map.Entry<String, BigDecimal> decimalEntry : itemPickAmtMap.entrySet()) {
                String key = decimalEntry.getKey();
                BigDecimal pickAmt = decimalEntry.getValue();
                String[] split = key.split(",");
                String billNon = split[0];
                Long skuId = Long.valueOf(split[1]);
                Integer isGift = Integer.valueOf(split[2]);

                OcBOrderCycleBuyInfo ocBOrderCycleBuyInfo = billNoCycleInfoMap.get(getItemPickKey(billNon, skuId, isGift));
                if (ocBOrderCycleBuyInfo != null) {
                    Long id = ocBOrderCycleBuyInfo.getId();
                    List<Long> list = map.get(pickAmt);
                    if (CollectionUtils.isEmpty(list)) {
                        list = Lists.newArrayList();
                    }
                    list.add(id);
                    map.put(pickAmt, list);
                }
            }
            //分组更新金额
            for (Map.Entry<BigDecimal, List<Long>> listEntry : map.entrySet()) {
                OcBOrderCycleBuyInfo update = new OcBOrderCycleBuyInfo();
                update.setPickGoodsAmt(listEntry.getKey());
                update.setModifieddate(new Date());
                Wrapper<OcBOrderCycleBuyInfo> updateWrapper = new UpdateWrapper<OcBOrderCycleBuyInfo>().lambda()
                        .in(OcBOrderCycleBuyInfo::getId, listEntry.getValue());
                orderCycleBuyInfoMapper.update(update, updateWrapper);
            }
        }

        if (updateCycle != null) {
            orderCycleBuyInfoMapper.updateById(updateCycle);
        }

        //资金变动记录
        AcCycleBuyAmt acCycleBuyAmt = new AcCycleBuyAmt();
        acCycleBuyAmt.setId(ModelUtil.getSequence("ac_cycle_buy_amt"));
        acCycleBuyAmt.setOrderTid(confirmInfo.getTid());
        acCycleBuyAmt.setOrderId(cycleOrder.getId());
        acCycleBuyAmt.setOrderBillNo(cycleOrder.getBillNo());
        acCycleBuyAmt.setCpCShopId(cycleOrder.getCpCShopId());
//            acCycleBuyAmt.setChangeAmt();

        if (isReport) {
            acCycleBuyAmt.setChangeType(CycleSalesReportAmtChangeTypeEnum.BEFORE_REPORT_CAL.getCode());
        } else {
            if (confirmInfo.getIsConfirm()) {
                acCycleBuyAmt.setChangeType(CycleSalesReportAmtChangeTypeEnum.CONFIRM_TO_SAP.getCode());
            } else {
                acCycleBuyAmt.setChangeType(CycleSalesReportAmtChangeTypeEnum.CANCEL_CONFIRM_TO_SAP.getCode());
            }
        }

//            acCycleBuyAmt.setChangeAmt(0);
        acCycleBuyAmt.setAfterConfirmAmt(confirmInfo.getAmt());
        if (offsetAmt != null) {
            acCycleBuyAmt.setWriteOffAmt(offsetAmt);
//                acCycleBuyAmt.setMilkCardAmountOffsetOrderId(0L);
        }
        BaseModelUtil.initialBaseModelSystemField(acCycleBuyAmt);
        acCycleBuyAmtMapper.insert(acCycleBuyAmt);
    }

    /**
     * 拆单等导致的订单变化，调用时完善周期购额外信息
     *
     * @param orderIdItemMap
     * @param cyclePickOrders
     * @param existCycleInfos
     */
    private void cycleBuyInfoImprove(Map<Long, List<OcBOrderItem>> orderIdItemMap, List<OcBOrder> cyclePickOrders, List<OcBOrderCycleBuyInfo> existCycleInfos) {
        //作废ids
        List<Long> cancelIds = Lists.newArrayList();
        //新增额外信息数据
        List<OcBOrderCycleBuyInfo> addCycleInfos = Lists.newArrayList();
        Map<String, List<OcBOrderCycleBuyInfo>> cycleInfoBillNoMap = existCycleInfos.stream().collect(Collectors.groupingBy(OcBOrderCycleBuyInfo::getBillNo));

        for (OcBOrder bOrder : cyclePickOrders) {
            String billNo = bOrder.getBillNo();
            Integer orderStatus = bOrder.getOrderStatus();
            List<OcBOrderCycleBuyInfo> ocBOrderCycleBuyInfos = cycleInfoBillNoMap.get(billNo);
            if (CollectionUtils.isEmpty(ocBOrderCycleBuyInfos)) {
                if (!OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus) && !OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) {
                    //新增
                    List<OcBOrderCycleBuyInfo> buyInfos = generatePickOrder(bOrder, null, orderIdItemMap);
                    if (CollectionUtils.isNotEmpty(buyInfos)) {
                        addCycleInfos.addAll(buyInfos);
                    }
                }
            } else {
                if (OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus) || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) {
                    //作废
                    for (OcBOrderCycleBuyInfo ocBOrderCycleBuyInfo : ocBOrderCycleBuyInfos) {
                        cancelIds.add(ocBOrderCycleBuyInfo.getId());
                    }
                }
            }
        }

        dealBuyInfoData(cancelIds, addCycleInfos);
    }

    /**
     * 提货金额冲抵金额计算
     *
     * @param noGiftOrderItemMap
     * @param cycleOffsetAmountRequests
     * @param cycleOrder
     * @param pickedMap
     * @param offsetAmt
     */
    private void calCycleOffsetAmt(Map<Long, List<OcBOrderItem>> noGiftOrderItemMap, List<CycleOffsetAmountRequest> cycleOffsetAmountRequests,
                                   OcBOrder cycleOrder, Map<Integer, List<OcBOrder>> pickedMap, BigDecimal offsetAmt) {
        log.info("calCycleOffsetAmt billNO:{},offsetAmt:{}", cycleOrder.getBillNo(), offsetAmt);
        CycleOffsetAmountRequest request = new CycleOffsetAmountRequest();
        request.setCycleOcBOrder(cycleOrder);
        //已提订单sku
        List<CycleOffsetAmountRequest.SkuModel> list = pickedItemAmt(offsetAmt, pickedMap, noGiftOrderItemMap);
        request.setSkuModels(list);
        cycleOffsetAmountRequests.add(request);
    }

    private Map<String, PsCProSkuResult> querySku(List<CycleOffsetAmountRequest> cycleOffsetAmountRequests) {
        Set<String> skuCodes = Sets.newHashSet();
        for (CycleOffsetAmountRequest cycleOffsetAmountRequest : cycleOffsetAmountRequests) {
            List<CycleOffsetAmountRequest.SkuModel> skuModels = cycleOffsetAmountRequest.getSkuModels();
            Set<String> collect = skuModels.stream().map(CycleOffsetAmountRequest.SkuModel::getSkuECode).collect(Collectors.toSet());
            skuCodes.addAll(collect);
        }

        List<PsCProSkuResult> psCProSkuResults = psRpcService.selectProSkuByEcodesWithOutActive(Lists.newArrayList(skuCodes));
        return psCProSkuResults.stream().collect(Collectors.toMap(PsCProSkuResult::getSkuEcode, x -> x, (a, b) -> a));
    }

    /**
     * 奶卡冲抵表的相关前置计算数据
     *
     * @param middleGroundBillTypeList
     * @return
     */
    private Map<String, SgBSapInformationMapping> getOffSetSapInfo(List<String> middleGroundBillTypeList) {
        Map<String, SgBSapInformationMapping> codeSapTypeMap = Maps.newHashMap();
        for (String code : middleGroundBillTypeList) {
            List<StCBusinessType> stCBusinessTypeList = stCBusinessTypeMapper.selectStCBusinessTypeByCode(code);
            if (CollectionUtils.isEmpty(stCBusinessTypeList)) {
                continue;
            }
            StCBusinessType stCBusinessType = stCBusinessTypeList.get(0);
            ValueHolderV14<SgBSapInformationMapping> valueHolderV14 = sgBSapInformationMappingR3Cmd.queryByMiddlegroundBillType(String.valueOf(stCBusinessType.getId()));
            if (!valueHolderV14.isOK() || ObjectUtil.isNull(valueHolderV14.getData())) {
                continue;
            }
            SgBSapInformationMapping sapInformationMapping = valueHolderV14.getData();
            codeSapTypeMap.put(code, sapInformationMapping);
        }
        return codeSapTypeMap;
    }

    /**
     * 已提货总金额计算
     *
     * @param existCycleInfos
     * @param pickedMap
     * @return
     */
    private BigDecimal pickedTotalAmt(List<OcBOrderCycleBuyInfo> existCycleInfos, Map<Integer, List<OcBOrder>> pickedMap) {
        if (MapUtils.isEmpty(pickedMap)) {
            return BigDecimal.ZERO;
        }

        Map<String, List<OcBOrderCycleBuyInfo>> cycleInfoMap = existCycleInfos.stream().collect(Collectors.groupingBy(OcBOrderCycleBuyInfo::getBillNo));
        BigDecimal pickedTotalAmt = BigDecimal.ZERO;
        for (Map.Entry<Integer, List<OcBOrder>> integerListEntry : pickedMap.entrySet()) {
            List<OcBOrder> pickedList = integerListEntry.getValue();
            for (OcBOrder ocBOrder : pickedList) {
                List<OcBOrderCycleBuyInfo> ocBOrderCycleBuyInfos = cycleInfoMap.get(ocBOrder.getBillNo());
                if (CollectionUtils.isEmpty(ocBOrderCycleBuyInfos)) {
                    continue;
                }
                for (OcBOrderCycleBuyInfo ocBOrderCycleBuyInfo : ocBOrderCycleBuyInfos) {
                    BigDecimal pickGoodsAmt = ocBOrderCycleBuyInfo.getPickGoodsAmt();
                    if (pickGoodsAmt != null) {
                        pickedTotalAmt = pickedTotalAmt.add(pickGoodsAmt);
                    }
                }
            }
        }
        return pickedTotalAmt;
    }

    /**
     * 已提/未提订单
     * 已提：周期购提货订单状态=仓库发货&平台发货&配货中&待传WMS&待审核&已审核
     * 未提：非已提
     *
     * @param currentNumMap
     * @return
     */
    private Pair<Map<Integer, List<OcBOrder>>, Map<Integer, List<OcBOrder>>> getPickedCurrentNumMap(Map<Integer, List<OcBOrder>> currentNumMap) {
        if (MapUtils.isEmpty(currentNumMap)) {
            return null;
        }

        Map<Integer, List<OcBOrder>> pickedMap = Maps.newHashMap();
        Map<Integer, List<OcBOrder>> noPickedMap = Maps.newHashMap();
        for (Map.Entry<Integer, List<OcBOrder>> entry : currentNumMap.entrySet()) {
            Integer key = entry.getKey();
            List<OcBOrder> ocBOrders = entry.getValue();

            List<OcBOrder> pickedList = Lists.newArrayList();
            List<OcBOrder> noPickedList = Lists.newArrayList();
            for (OcBOrder ocBOrder : ocBOrders) {
                if (ALREADY_PICK_ORDER_STATUS.contains(ocBOrder.getOrderStatus())) {
                    pickedList.add(ocBOrder);
                } else {
                    noPickedList.add(ocBOrder);
                }
            }
            if (CollectionUtils.isNotEmpty(pickedList)) {
                pickedMap.put(key, pickedList);
            }
            if (CollectionUtils.isNotEmpty(noPickedList)) {
                noPickedMap.put(key, noPickedList);
            }
        }
        return new ImmutablePair<>(pickedMap, noPickedMap);
    }

    /**
     * 过滤复制和补发，找到需要计算的订单
     *
     * @param orderList
     * @param orderIdItemMap
     * @return
     */
    private List<OcBOrder> needCalOrders(List<OcBOrder> orderList, Map<Long, List<OcBOrderItem>> orderIdItemMap) {
        List<OcBOrder> calOrders = Lists.newArrayList();
        //过滤复制和补发单
        for (OcBOrder ocBOrder : orderList) {
            Integer isCopyOrder = ocBOrder.getIsCopyOrder();
            if (YesNoEnum.Y.getVal().equals(isCopyOrder)) {
                continue;
            }
            if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsResetShip())) {
                continue;
            }

//            List<OcBOrderItem> orderItems = orderIdItemMap.get(ocBOrder.getId());
//            if (CollectionUtils.isEmpty(orderItems)) {
//                continue;
//            }
            calOrders.add(ocBOrder);
        }
        return calOrders;
    }

    /**
     * 中台周期购进销存报表任务前执行一遍提货信息计算
     */
//    public void beforeChangeByConfirm(Date date) {
//        //获取0点时间
//        Date zeroTime = AcCycleSalesReportUtils.getFirstDateTime(date);
//        if (zeroTime == null) {
//            throw new NDSException("获取0点时间失败");
//        }
//
//        //查询中台周期购订单数据
//        Page<OcBOrder> page = new Page<>(1, 1);
//        IPage<OcBOrder> orderIPage = omsAdbDataSourceService.pageQueryNeedCalCycleOrders(zeroTime, OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode(), page);
//        long total = orderIPage.getTotal();
//        if (total <= 0) {
//            log.info("中台周期购进销存报表任务前执行一遍提货信息计算无符合条件的数据");
//            return;
//        }
//
//        int pageCount = (int) Math.ceil((double) total / 2000);
//        for (int i = 1; i <= pageCount; i++) {
//            List<String> tids = Lists.newArrayList();
//            try {
//                page.setCurrent(i);
//                page.setSize(2000);
//                IPage<OcBOrder> iPage = omsAdbDataSourceService.pageQueryNeedCalCycleOrders(zeroTime, OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode(), page);
//                List<OcBOrder> records = iPage.getRecords();
//                if (CollectionUtils.isEmpty(records)) {
//                    log.info("beforeChangeByConfirm page no records i:{}", i);
//                    continue;
//                }
//                log.info("beforeChangeByConfirm page tid i:{},size:{},records:{}", i, records.size(), JSON.toJSONString(records));
//
//                tids = records.stream().map(OcBOrder::getTid).collect(Collectors.toList());
//
//                beforeReport(tids);
//            } catch (Exception e) {
//                log.warn("AcCycleSalesReportService.beforeChangeByConfirm. batchcal error tids:{}", tids, e);
//                DingDingUtil.reportDing("中台周期购进销存报表任务前执行一遍提货信息计算异常，批次:" + i);
//            }
//        }
//    }

    private void beforeReport(List<String> tids) {
        //获取对账信息
        ValueHolderV14 valueHolderV14 = acRpcService.queryConfirmInfo(tids);
        if (!valueHolderV14.isOK()) {
            log.warn("beforeChangeByConfirm queryConfirmInfo error tids:{},error:{}", tids, valueHolderV14.getMessage());
            throw new NDSException("中台周期购进销存报表任务前执行一遍提货信息计算获取对账信息失败");
        }
        List<CycleConfirmRequest> list = (List<CycleConfirmRequest>) valueHolderV14.getData();
        Map<String, List<CycleConfirmRequest>> cycleConfirmMap = list.stream().collect(Collectors.groupingBy(CycleConfirmRequest::getTid));

        List<CycleConfirmRequest> confirmRequests = Lists.newArrayList();
        for (Map.Entry<String, List<CycleConfirmRequest>> entry : cycleConfirmMap.entrySet()) {
            String tid = entry.getKey();
            List<CycleConfirmRequest> cycleConfirms = entry.getValue();
            for (CycleConfirmRequest cycleConfirm : cycleConfirms) {
                CycleConfirmRequest cycleConfirmRequest = new CycleConfirmRequest();
                cycleConfirmRequest.setTid(tid);
                cycleConfirmRequest.setBillNo(cycleConfirm.getBillNo());
//                    cycleConfirmRequest.setSkuCode();
                cycleConfirmRequest.setAmt(cycleConfirm.getAmt());
                cycleConfirmRequest.setIsConfirm(cycleConfirm.getIsConfirm());
                confirmRequests.add(cycleConfirmRequest);
            }
        }

        //周期购提货计算
        changeByConfirm(confirmRequests, true);
    }

    /**
     * 周期购状态
     * <p>
     * 已作废（优先级最高）：存在已取消状态
     * 未发货：无平台发货且无仓库发货
     * 提货中：有平台发货或仓库发货，且有待寻源
     * 提货完毕：有平台发货货仓库发货，无待寻源
     *
     * @param pickOrders
     * @return
     */
    private CycleSalesReportCycleStatusEnum getCycleStatus(List<OcBOrder> pickOrders) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(pickOrders)) {
            return CycleSalesReportCycleStatusEnum.NULL;
        }

        //平台发货
        List<Integer> platformDeliveryStatusList = Lists.newArrayList();
        //仓库发货
        List<Integer> warehouseDeliveryStatusList = Lists.newArrayList();
        //待寻源
        List<Integer> waitToBeConfirmStatusList = Lists.newArrayList();
        //取消
        List<Integer> cancelStatusList = Lists.newArrayList();

        for (Integer status : pickOrders.stream().map(OcBOrder::getOrderStatus).collect(Collectors.toList())) {
            if (OmsOrderStatus.CANCELLED.toInteger().equals(status)) {
                cancelStatusList.add(status);
            }
            if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(status)) {
                warehouseDeliveryStatusList.add(status);
            }
            if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(status)) {
                platformDeliveryStatusList.add(status);
            }
            if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(status)) {
                waitToBeConfirmStatusList.add(status);
            }
        }

        //存在已取消，状态为已作废
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cancelStatusList)) {
            return CycleSalesReportCycleStatusEnum.PICK_CANCEL;
        }

        //无平台发货和仓库发货，状态为未发货
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(platformDeliveryStatusList) && org.apache.commons.collections4.CollectionUtils.isEmpty(warehouseDeliveryStatusList)) {
            return CycleSalesReportCycleStatusEnum.PICK_NO;
        }

        //有平台发货或仓库发货，无待寻源
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(warehouseDeliveryStatusList) || org.apache.commons.collections4.CollectionUtils.isNotEmpty(platformDeliveryStatusList)) {
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(waitToBeConfirmStatusList)) {
                //无待寻源
                return CycleSalesReportCycleStatusEnum.PICK_COMPLETE;
            } else {
                //有待寻源
                return CycleSalesReportCycleStatusEnum.PICK_ING;
            }
        }

        return CycleSalesReportCycleStatusEnum.NULL;
    }

    /**
     * 中台周期购冲抵
     *
     * @param requestList
     * @param offSetSapTypeMap
     * @param psCProSkuResultMap
     * @return
     */
    private Map<String, String> cycleOffset(List<CycleOffsetAmountRequest> requestList, Map<String, SgBSapInformationMapping> offSetSapTypeMap,
                                            Map<String, PsCProSkuResult> psCProSkuResultMap) {
        Map<String, String> errorMap = Maps.newHashMap();
        for (CycleOffsetAmountRequest request : requestList) {
            //订单
            OcBOrder cycleOcBOrder = request.getCycleOcBOrder();
            CpShop cpShop = cpRpcExtService.selectShopById(cycleOcBOrder.getCpCShopId());

            MilkCardAmountOffsetOrder milkCardAmountOffsetOrder = new MilkCardAmountOffsetOrder();
            Long id = ModelUtil.getSequence("milk_card_amount_offset_order");
            milkCardAmountOffsetOrder.setId(id);
            milkCardAmountOffsetOrder.setCardCode(cycleOcBOrder.getTid());
            milkCardAmountOffsetOrder.setOcBOrderId(cycleOcBOrder.getId());
            milkCardAmountOffsetOrder.setBillNo(cycleOcBOrder.getBillNo());
            milkCardAmountOffsetOrder.setCpCShopId(cycleOcBOrder.getCpCShopId());
            milkCardAmountOffsetOrder.setCollectStatus(MilkCardCollectStatusEnum.UN_COLLECT.getStatus());
            milkCardAmountOffsetOrder.setErrorMsg("");
            milkCardAmountOffsetOrder.setOcBSapSalesDataGatherId(0L);
            // 店铺信息 业务类型名称 业务类型编码 汇总类型
            milkCardAmountOffsetOrder.setCpCShopEcode(cycleOcBOrder.getCpCShopEcode());
            milkCardAmountOffsetOrder.setCpCShopEname(cycleOcBOrder.getCpCShopTitle());
            //产品说用当前时间，奶卡用的上账时间
            milkCardAmountOffsetOrder.setInTime(new Date());
            BaseModelUtil.initialBaseModelSystemField(milkCardAmountOffsetOrder);

            List<CycleOffsetAmountRequest.SkuModel> skuModels = request.getSkuModels();
            if (CollectionUtils.isEmpty(skuModels)) {
                log.info("订单：{}，没有明细，跳过", cycleOcBOrder.getBillNo());
                continue;
            }

            //冲抵金额相加
            BigDecimal skuTotalAmt = skuModels.stream().map(CycleOffsetAmountRequest.SkuModel::getOffsetAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (skuTotalAmt.compareTo(new BigDecimal(0)) == 0) {
                log.info("订单：{}，没有冲抵金额，跳过", cycleOcBOrder.getBillNo());
                continue;
            }

            if (skuTotalAmt.compareTo(new BigDecimal(0)) > 0) {
                milkCardAmountOffsetOrder.setMiddlegroundBillTypeCode(OmsMiddleGroundBillTypeEnum.RYCD03.getCode());
                milkCardAmountOffsetOrder.setMiddlegroundBillTypeName(OmsMiddleGroundBillTypeEnum.RYCD03.getName());
                milkCardAmountOffsetOrder.setSumType(OmsMiddleGroundBillTypeEnum.RYCD03.getCode());
            } else {
                milkCardAmountOffsetOrder.setMiddlegroundBillTypeCode(OmsMiddleGroundBillTypeEnum.RYCD04.getCode());
                milkCardAmountOffsetOrder.setMiddlegroundBillTypeName(OmsMiddleGroundBillTypeEnum.RYCD04.getName());
                milkCardAmountOffsetOrder.setSumType(OmsMiddleGroundBillTypeEnum.RYCD04.getCode());
            }

            SgBSapInformationMapping sgBSapInformationMapping = offSetSapTypeMap.get(milkCardAmountOffsetOrder.getMiddlegroundBillTypeCode());
            //sap单据类型
            milkCardAmountOffsetOrder.setSapOrderType(sgBSapInformationMapping.getSapBillType());
            if (ObjectUtil.isNotNull(cpShop) && ObjectUtil.isNotNull(sgBSapInformationMapping)) {
                if ("1".equals(sgBSapInformationMapping.getSalesOrganization())) {
                    milkCardAmountOffsetOrder.setSalesOrganization(cpShop.getFreeOrganizationCode());
                } else if ("2".equals(sgBSapInformationMapping.getSalesOrganization())) {
                    milkCardAmountOffsetOrder.setSalesOrganization(cpShop.getGeneralOrganizationCode());
                }
            }

            //卡号（周期购-平台单号）
            String cardCode = cycleOcBOrder.getTid();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String inTimeStr = sdf.format(milkCardAmountOffsetOrder.getInTime());
            //合并码
            milkCardAmountOffsetOrder.setCollectCode(milkCardAmountOffsetOrder.getMiddlegroundBillTypeCode() + milkCardAmountOffsetOrder.getCpCShopEcode() + inTimeStr);
            List<MilkCardAmountOffsetItem> addMilkCardAmountOffsetItem = new ArrayList<>();
            for (CycleOffsetAmountRequest.SkuModel skuModel : skuModels) {
                // 奶卡提奶逻辑（因为业务上 组合商品编码与下面的款号编码配置的都是一致的 所以直接拿组合商品编码来查询明细--雷宏宇），中台周期购不用先
//            ValueHolderV14<List<PsCSkugroup>> psCSkuGroupListValue = cskuGroupQureyCmd.selectBySkuGrpEcode(skuModel.getSkuECode());
//            if (psCSkuGroupListValue.isOK() && CollectionUtil.isNotEmpty(psCSkuGroupListValue.getData())) {
//                List<PsCSkugroup> psCSkugroupList = psCSkuGroupListValue.getData();
//                for (PsCSkugroup psCSkugroup : psCSkugroupList) {
//                    CycleOffsetAmountRequest.SkuModel newSkuModel = new CycleOffsetAmountRequest.SkuModel();
//                    newSkuModel.setOffsetAmount(psCSkugroup.getRatio().multiply(skuModel.getOffsetAmount()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
//                    newSkuModel.setQty(psCSkugroup.getNum().multiply(new BigDecimal(skuModel.getQty())).intValue());
//                    newSkuModel.setSkuECode(psCSkugroup.getPsCSkuEcode());
//                    MilkCardAmountOffsetItem milkCardAmountOffsetItem = getCycleAmountOffsetItem(cardCode, milkCardAmountOffsetOrder, newSkuModel);
//                    addMilkCardAmountOffsetItem.add(milkCardAmountOffsetItem);
//                }
//                continue;
//            }
                MilkCardAmountOffsetItem milkCardAmountOffsetItem = getCycleAmountOffsetItem(cardCode, milkCardAmountOffsetOrder, skuModel, psCProSkuResultMap);
                addMilkCardAmountOffsetItem.add(milkCardAmountOffsetItem);
            }
            try {
                extracted(milkCardAmountOffsetOrder, addMilkCardAmountOffsetItem);
            } catch (Exception e) {
                log.error(LogUtil.format("error to add amount offset cardCode:{}, errorMsg:{}", "周期购提货冲抵异常"), cardCode, e.getMessage());
                errorMap.put(cardCode, e.getMessage());
            }
        }

        return errorMap;
    }

    /**
     * 周期购冲抵明细
     *
     * @param cardCode
     * @param milkCardAmountOffsetOrder
     * @param skuModel
     * @param psCProSkuResultMap
     * @return
     * @throws RuntimeException
     */
    private MilkCardAmountOffsetItem getCycleAmountOffsetItem(String cardCode, MilkCardAmountOffsetOrder milkCardAmountOffsetOrder,
                                                              CycleOffsetAmountRequest.SkuModel skuModel, Map<String, PsCProSkuResult> psCProSkuResultMap) {
        MilkCardAmountOffsetItem milkCardAmountOffsetItem = new MilkCardAmountOffsetItem();
        milkCardAmountOffsetItem.setFactoryCode("6010");
        milkCardAmountOffsetItem.setCardCode(cardCode);
        milkCardAmountOffsetItem.setOffsetOrderId(milkCardAmountOffsetOrder.getId());
        milkCardAmountOffsetItem.setId(ModelUtil.getSequence("milk_card_amount_offset_item"));
        BigDecimal offsetAmount = skuModel.getOffsetAmount();
        milkCardAmountOffsetItem.setOffsetPrice(offsetAmount.abs());
        if (offsetAmount.doubleValue() == 0.0) {
            //预留逻辑，周期购正常0不会走进来生成冲抵，以后如果0也要的话，就不用改这里了
            milkCardAmountOffsetItem.setRowItemType(MilkCardItemTypeEnum.ZTD2.getType());
        } else {
            milkCardAmountOffsetItem.setRowItemType(MilkCardItemTypeEnum.ZTD1.getType());
        }

        //商品
        PsCProSkuResult productSku = psCProSkuResultMap.get(skuModel.getSkuECode());
        milkCardAmountOffsetItem.setPsCProEcode(productSku.getPsCProEcode());
        milkCardAmountOffsetItem.setPsCProId(productSku.getPsCProId());
        milkCardAmountOffsetItem.setPsCProEname(productSku.getPsCProEname());
        milkCardAmountOffsetItem.setPsCSkuEcode(productSku.getSkuEcode());
        milkCardAmountOffsetItem.setPsCSkuId(productSku.getId());
        milkCardAmountOffsetItem.setQty(skuModel.getQty());
        if (productSku.getProAttributeMap() != null && productSku.getProAttributeMap().get("M_DIM3_ID") != null) {
            milkCardAmountOffsetItem.setUnit(productSku.getProAttributeMap().get("M_DIM3_ID").getEcode());
        }
        milkCardAmountOffsetItem.setItemType(1);
        BaseModelUtil.initialBaseModelSystemField(milkCardAmountOffsetItem);
        return milkCardAmountOffsetItem;
    }

    @Transactional(rollbackFor = Exception.class)
    public void extracted(MilkCardAmountOffsetOrder milkCardAmountOffsetOrder, List<MilkCardAmountOffsetItem> addMilkCardAmountOffsetItem) {
        milkCardAmountOffsetOrderMapper.insert(milkCardAmountOffsetOrder);
        milkCardAmountOffsetItemMapper.batchInsert(addMilkCardAmountOffsetItem);
    }

    /**
     * 冲销金额明细分摊
     *
     * @param offsetAmt
     * @param pickedMap
     * @param noGiftOrderItemMap
     * @return
     */
    private List<CycleOffsetAmountRequest.SkuModel> pickedItemAmt(BigDecimal offsetAmt, Map<Integer, List<OcBOrder>> pickedMap,
                                                                  Map<Long, List<OcBOrderItem>> noGiftOrderItemMap) {
        if (MapUtils.isEmpty(pickedMap) || MapUtils.isEmpty(noGiftOrderItemMap)) {
            return Lists.newArrayList();
        }

        List<CycleOffsetAmountRequest.SkuModel> list = Lists.newArrayList();
        List<CycleOffsetItemAmtInfo> cycleOffsetItemAmtInfos = Lists.newArrayList();
        Map<String, Integer> itemSkuNumMap = Maps.newHashMap();

        for (Map.Entry<Integer, List<OcBOrder>> listEntry : pickedMap.entrySet()) {
            List<OcBOrder> ocBOrders = listEntry.getValue();
            for (OcBOrder ocBOrder : ocBOrders) {
                List<OcBOrderItem> ocBOrderItems = noGiftOrderItemMap.get(ocBOrder.getId());
                if (CollectionUtils.isEmpty(ocBOrderItems)) {
                    continue;
                }
                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                    //如果是赠品，跳过
                    if (YesNoEnum.Y.getVal().equals(ocBOrderItem.getIsGift())) {
                        continue;
                    }
                    itemSkuNumMap.merge(ocBOrderItem.getPsCSkuEcode(), ocBOrderItem.getQty().intValue(), Integer::sum);
                }
            }
        }

        for (Map.Entry<String, Integer> stringIntegerEntry : itemSkuNumMap.entrySet()) {
            CycleOffsetItemAmtInfo cycleOffsetItemAmtInfo = new CycleOffsetItemAmtInfo();
            cycleOffsetItemAmtInfo.setSkuCode(stringIntegerEntry.getKey());
            cycleOffsetItemAmtInfo.setQty(new BigDecimal(stringIntegerEntry.getValue()));
            cycleOffsetItemAmtInfos.add(cycleOffsetItemAmtInfo);
        }

        //所有明细总数量
        BigDecimal totalNum = cycleOffsetItemAmtInfos.stream().map(CycleOffsetItemAmtInfo::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal remainAmt = offsetAmt;
        for (int i = 0; i < cycleOffsetItemAmtInfos.size(); i++) {
            CycleOffsetItemAmtInfo cycleOffsetItemAmtInfo = cycleOffsetItemAmtInfos.get(i);
            String skuCode = cycleOffsetItemAmtInfo.getSkuCode();
            BigDecimal qty = cycleOffsetItemAmtInfo.getQty();

            CycleOffsetAmountRequest.SkuModel skuModel = new CycleOffsetAmountRequest.SkuModel();
            skuModel.setSkuECode(skuCode);
            skuModel.setQty(qty.intValue());

            BigDecimal skuOffsetAmt = offsetAmt.multiply(qty).divide(totalNum, 2, BigDecimal.ROUND_DOWN);
            if (i == cycleOffsetItemAmtInfos.size() - 1) {
                skuModel.setOffsetAmount(remainAmt);
            } else {
                skuModel.setOffsetAmount(skuOffsetAmt);
            }
            remainAmt = remainAmt.subtract(skuOffsetAmt);

            list.add(skuModel);
        }

        return list;
    }

    /**
     * 拆单后刷新周期购额外信息
     *
     * @param bOrder
     * @param newOrders
     */
    public void fullCycleBuyInfo(OcBOrder bOrder, List<OcBOrder> newOrders) {
        List<OcBOrderCycleBuyInfo> buyInfos = orderCycleBuyInfoMapper.getByTid(bOrder.getTid());
        if (CollectionUtils.isEmpty(buyInfos)) {
            return;
        }
        //获取周期购提货单
        buyInfos = buyInfos.stream().filter(p -> OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(p.getBusinessTypeCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(buyInfos)) {
            return;
        }

        Map<String, List<OcBOrderCycleBuyInfo>> billNoMap = buyInfos.stream().collect(Collectors.groupingBy(OcBOrderCycleBuyInfo::getBillNo));

        //原单提货总金额
        BigDecimal oldPickAmt = BigDecimal.ZERO;
        //作废原单
        List<Long> cancelIds = Lists.newArrayList();
        List<OcBOrderCycleBuyInfo> oldBuyInfos = billNoMap.get(bOrder.getBillNo());
        if (CollectionUtils.isNotEmpty(oldBuyInfos)) {
            cancelIds.addAll(oldBuyInfos.stream().map(OcBOrderCycleBuyInfo::getId).collect(Collectors.toSet()));

            //提货总金额
            oldPickAmt = oldBuyInfos.stream().map(OcBOrderCycleBuyInfo::getPickGoodsAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        List<OcBOrderItem> bOrderItems = orderItemMapper.selectAllStatusOrderItemsByOrderIds(newOrders.stream().map(OcBOrder::getId).collect(Collectors.toList()));

//        bOrderItems = bOrderItems.stream().filter(p -> !YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
        Map<Long, List<OcBOrderItem>> orderIdItemMap = bOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));

        //赠品提货金额处理
        List<OcBOrderCycleBuyInfo> giftBuyInfos = generateGiftPickOrder(newOrders, orderIdItemMap);
        //获取已计算的赠品总提货金额
        BigDecimal giftPickAmt = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(giftBuyInfos)) {
            giftPickAmt = giftBuyInfos.stream().map(OcBOrderCycleBuyInfo::getPickGoodsAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
//            oldPickAmt = oldPickAmt.subtract(giftPickAmt);
        }

        //新订单提货金额平均分,排除全是赠品的订单
        List<OcBOrder> dealOrders = Lists.newArrayList();
        List<OcBOrder> giftOrders = Lists.newArrayList();
        for (OcBOrder newOrder : newOrders) {
            List<OcBOrderItem> ocBOrderItems = orderIdItemMap.get(newOrder.getId());
            List<OcBOrderItem> noGiftItems = ocBOrderItems.stream().filter(p -> !YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noGiftItems)) {
                dealOrders.add(newOrder);
            } else {
                //没有不是赠品的，就是都是赠品，不考虑
                giftOrders.add(newOrder);
            }
        }
        log.info("拆单 新订单平均分提货金额 dealOrders:{}", JSON.toJSONString(dealOrders));

        //oldPickAmt减去giftOrders的订单金额
        oldPickAmt = oldPickAmt.subtract(giftPickAmt);
        Map<Integer, BigDecimal> pickAmtMap = averageAmount(oldPickAmt, dealOrders.size());
        log.info("拆单 新订单平均分提货金额 pickAmtMap:{}", JSON.toJSONString(pickAmtMap));

        //新增新周期购信息
        List<OcBOrderCycleBuyInfo> newBuyInfos = Lists.newArrayList();
        for (int i = 0; i < dealOrders.size(); i++) {
            List<OcBOrderCycleBuyInfo> cycleBuyInfos = billNoMap.get(dealOrders.get(i).getBillNo());
            if (CollectionUtils.isNotEmpty(cycleBuyInfos)) {
                //理论上走不进来
                continue;
            }

            BigDecimal pickAmt = pickAmtMap.get(i + 1);
            List<OcBOrderCycleBuyInfo> infos = generatePickOrder(dealOrders.get(i), pickAmt, orderIdItemMap);
            if (CollectionUtils.isEmpty(infos)) {
                continue;
            }
            newBuyInfos.addAll(infos);
        }

        newBuyInfos.addAll(giftBuyInfos);

        dealBuyInfoData(cancelIds, newBuyInfos);
    }

    @Transactional(rollbackFor = Exception.class)
    public void dealBuyInfoData(List<Long> cancelIds, List<OcBOrderCycleBuyInfo> newBuyInfos) {
        if (CollectionUtils.isNotEmpty(cancelIds)) {
            orderCycleBuyInfoMapper.updateIsactiveN(cancelIds);
        }

        if (CollectionUtils.isNotEmpty(newBuyInfos)) {
            orderCycleBuyInfoMapper.batchInsert(newBuyInfos);
        }
    }

    /**
     * 周期购额外信息初始化
     */
    public void cycleExtInfoInit(Integer batchNum) {
        Date date = new Date();
        //查询中台周期购订单数据
        Page<OcBOrder> page = new Page<>(1, 1);
        IPage<OcBOrder> orderIPage = omsAdbDataSourceService.pageQueryNeedCalCycleOrders(date, OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode(), page);
        long total = orderIPage.getTotal();
        if (total <= 0) {
            log.info("周期购额外信息初始化无符合条件的数据");
            return;
        }

        int pageCount = (int) Math.ceil((double) total / batchNum);
        for (int i = 1; i <= pageCount; i++) {
            long timeMillis = System.currentTimeMillis();
            page.setCurrent(i);
            page.setSize(batchNum);
            IPage<OcBOrder> iPage = omsAdbDataSourceService.pageQueryNeedCalCycleOrders(date, OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode(), page);
            List<OcBOrder> records = iPage.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                log.info("周期购额外信息初始化 page no records i:{}", i);
                continue;
            }
            Set<String> tids = records.stream().map(OcBOrder::getTid).collect(Collectors.toSet());
            generateCycleBuyInfo(Lists.newArrayList(tids));
            log.info("周期购额外信息初始化 page i:{} time:{}", i, System.currentTimeMillis() - timeMillis);
        }
    }

    public void cycleExtInfoInitSingle(List<String> tids) {
        generateCycleBuyInfo(tids);
    }

    public void cycleReportCalData(List<String> list, Date date) {
        //计算报表前置-计算提货金额...
        beforeReport(list);

        //计算报表
        acRpcService.reportCalculateTid(list, date);
    }

    /**
     * 周期购额外信息记录
     * <p>
     * 周期购提数：周期购订单下所有周期购提货订单（过滤补发和复制单，且商品不全部是赠品）的订单数量
     * 周期购剩余提数：中台周期购订单下的所有状态=待寻源，（过滤补发和复制单，且商品不全部是赠品）的订单数量。
     *
     * @param sourceCodes
     */
    public void generateCycleBuyInfo(List<String> sourceCodes) {
        log.info("Start generateCycleBuyInfo sourceCodes:{}", sourceCodes);

        List<Long> esIdList = GSI4Order.getIdListBySourceCodes(sourceCodes);
        List<OcBOrder> orderList = orderMapper.selectOrderListByIds(esIdList);
        if (CollectionUtils.isEmpty(orderList)) {
            log.info("generateCycleBuyInfo orderList is empty sourceCodes:{}", sourceCodes);
            return;
        }

        List<OcBOrderItem> bOrderItems = orderItemMapper.selectAllStatusOrderItemsByOrderIds(esIdList);
//        bOrderItems = bOrderItems.stream().filter(p -> !YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
        Map<Long, List<OcBOrderItem>> orderIdMapMap = bOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));

        List<OcBOrder> calOrders = needCalOrders(orderList, orderIdMapMap);

        if (CollectionUtils.isEmpty(calOrders)) {
            log.info("generateCycleBuyInfo calOrders is empty sourceCodes:{}", sourceCodes);
            return;
        }

        //周期购订单
        List<OcBOrder> cycleOrders = calOrders.stream().filter(p -> OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(p.getBusinessTypeCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cycleOrders)) {
            log.info("generateCycleBuyInfo cycleOrders is empty sourceCodes:{}", sourceCodes);
            return;
        }

        //周期购提货
        List<OcBOrder> pickOrders = calOrders.stream().filter(p -> OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(p.getBusinessTypeCode())).collect(Collectors.toList());
        Map<String, List<OcBOrder>> pickOrderMap = pickOrders.stream().collect(Collectors.groupingBy(OcBOrder::getTid));

        //额外数据表
        List<String> existCycInfoBillNos = cycleBuyInfosBillNos(sourceCodes);

        //新增周期购额外数据
        List<OcBOrderCycleBuyInfo> newInfos = Lists.newArrayList();
        for (OcBOrder cycleOrder : cycleOrders) {
            String billNo = cycleOrder.getBillNo();
            if (existCycInfoBillNos.contains(billNo)) {
                log.info("generateCycleBuyInfo existCycleInfo is exist billNo:{}", billNo);
                continue;
            }
            String tid = cycleOrder.getTid();
            try {
                List<OcBOrder> pickOrderList = pickOrderMap.get(tid);
                if (CollectionUtils.isEmpty(pickOrderList)) {
                    log.info("generateCycleBuyInfo pickOrderList is empty tid:{}", tid);
                    continue;
                }

                for (OcBOrder ocBOrder : pickOrderList) {
                    if (ocBOrder.getCurrentCycleNumber() == null) {
                        ocBOrder.setCurrentCycleNumber(0);
                    }
                }

                Map<Integer, List<OcBOrder>> cycleNumberMap = pickOrderList.stream().collect(Collectors.groupingBy(OcBOrder::getCurrentCycleNumber));

                //查询周期购订单明细
                List<OcBOrderItem> cycleItems = orderIdMapMap.get(cycleOrder.getId());
                if (CollectionUtils.isEmpty(cycleItems)) {
                    log.info("generateCycleBuyInfo cycleNoGiftItems is empty cycleOrderId:{},tid:{}", cycleOrder.getId(), tid);
                    continue;
                }

                //周期购订单组装
                newInfos.addAll(this.generateCycleOrder(cycleOrder, cycleNumberMap.size(), cycleItems));

                //周期购赠品提货额外信息组装
                newInfos.addAll(this.generateInitPickOrder(pickOrderList, orderIdMapMap));

                //提货金额计算(billNo+skuId,pickAmt)
//                Map<String, BigDecimal> pickAmtMap = pickAmt(cycleOrder, cycleNumberMap, orderIdMapMap);

                //周期购提货额外信息组装
//                newInfos.addAll(this.generatePickOrder(orderIdMapMap, cycleNumberMap, pickAmtMap));

                log.info("generateCycleBuyInfo tid:{}, billNo:{}", cycleOrder.getTid(), cycleOrder.getBillNo());
            } catch (Exception e) {
                log.warn("周期购额外信息初始化异常 tid:{}", tid, e);
                DingDingUtil.reportDing("周期购额外信息初始化异常 tid:" + tid);
            }
        }

        if (CollectionUtils.isNotEmpty(newInfos)) {
            orderCycleBuyInfoMapper.batchInsert(newInfos);
        }
    }

    private List<String> cycleBuyInfosBillNos(List<String> sourceCodes) {
        if (CollectionUtils.isEmpty(sourceCodes)) {
            return Lists.newArrayList();
        }

        List<OcBOrderCycleBuyInfo> cycleBuyInfos = orderCycleBuyInfoMapper.getByTids(sourceCodes);
        if (CollectionUtils.isEmpty(cycleBuyInfos)) {
            return Lists.newArrayList();
        }

        List<OcBOrderCycleBuyInfo> cycInfos = cycleBuyInfos.stream().filter(p -> OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(p.getBusinessTypeCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cycInfos)) {
            return Lists.newArrayList();
        }
        return cycInfos.stream().map(OcBOrderCycleBuyInfo::getBillNo).collect(Collectors.toList());
    }
}
