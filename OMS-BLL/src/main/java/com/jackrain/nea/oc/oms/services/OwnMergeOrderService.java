package com.jackrain.nea.oc.oms.services;

import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.api.oms.SgOmsStoTranslationCmd;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoTranslationBillRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoTranslationRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPaymentMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPromotionMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-04-25 13:07
 * 合并订单服务
 */
@Slf4j
@Component
public class OwnMergeOrderService {

    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    OmsOrderLogService omsOrderLogService;

    @Autowired
    OcBOrderPaymentMapper ocBOrderPaymentMapper;

    @Autowired
    OcBOrderPromotionMapper ocBOrderPromotionMapper;

    @Autowired
    private OmsAuditTaskService omsAuditTaskService;

    @Autowired
    private OcBOrderItemExtService ocBOrderItemExtService;

    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;

    @Reference(version = "1.0", group = "sg")
    private SgOmsStoTranslationCmd sgOmsStoTranslationCmd;

    @Transactional(rollbackFor = Exception.class)
    public void insertOrderAndInvokeSgSrv(List<OcBOrder> orders, OcBOrderRelation relation, String logType, User user, boolean is2CDetention) {
        String step = "";
        try {
            // 1. 单据处理
            step = "合单新增";
            if (ocBOrderMapper.insert(relation.getOrderInfo()) != 1) {
                throw new NDSException("订单主表新增失败");
            }
            step = "合单明细";
            if (CollectionUtils.isEmpty(relation.getOrderItemList())) {
                throw new NDSException("订单明细为空，不允许合并");
            }

            if (ocBOrderItemMapper.batchInsert(relation.getOrderItemList()) < 1) {
                throw new NDSException("订单明细新增失败");
            }
            step = "付款信息";
            if (CollectionUtils.isNotEmpty(relation.getOrderPaymentList())) {
                if (ocBOrderPaymentMapper.batchInsert(relation.getOrderPaymentList()) < 1) {
                    throw new NDSException("订单付款信息新增失败");
                }
            }
            step = "优惠信息";
            if (CollectionUtils.isNotEmpty(relation.getOrderPromotionList())) {
                if (ocBOrderPromotionMapper.batchInsert(relation.getOrderPromotionList()) < 1) {
                    throw new NDSException("订单优惠信息新增失败");
                }
            }
            step = "销售、分货部门信息";
            if (CollectionUtils.isNotEmpty(relation.getOcBOrderItemExtList())) {
                if (ocBOrderItemExtService.insertList(relation.getOcBOrderItemExtList()) < 1) {
                    throw new NDSException("订单销售、分货部门信息新增失败");
                }
            }
            step = "合单日志";
            //订单新增日志
            omsOrderLogService.addUserOrderLog(relation.getOrderInfo().getId(),
                    relation.getOrderInfo().getBillNo(), OrderLogTypeEnum.ORDER_MERGE.getKey(),
                    logType + "合并成功,原单id为" + relation.getOrderInfo().getSuffixInfo(),
                    "", "", user);

            if (!is2CDetention) {
                step = "插入审核";
                if (null != relation.getOrderId()) {
                    omsAuditTaskService.createOcBAuditTask(relation.getOrderInfo(), OmsAuditTimeCalculateReason.MERGE);
                }
            }

            mergeVoidOrder(orders, logType, user);

            step = "开始库存平移";
            if (!is2CDetention) {
                SgOmsStoTranslationRequest sgOmsStoTranslationRequest = buildSgOmsStoTranslationRequest(orders, relation, user);
                ValueHolderV14 valueHolderV14 = sgOmsStoTranslationCmd.stoTranslation(sgOmsStoTranslationRequest);
                if (!valueHolderV14.isOK()) {
                    throw new NDSException("库存平移接口时异常 ");
                }
            }

//            if (is2CDetention) {
//                step = "插入卡单";
//                omsOccupyTaskService.addOcBOccupyTask(relation.getOrderInfo(), null, 0);
//            }
            step = "";
        } catch (Exception e) {
            log.error(LogUtil.format("合单, 更新数据,调用库存服务时发生异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("更新数据[" + step + "], 占用库存时异常: " + e.getMessage());
        }
    }

    /**
     * <AUTHOR>
     * @Date 21:52 2021/7/20
     * @Description 批量作废主单
     */
    private void mergeVoidOrder(List<OcBOrder> ocBOrderList,String lgType, User user) {
        List<Long> ids = ocBOrderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
        if (ocBOrderMapper.updateList(ids) != ids.size()) {
            throw new NDSException("合并订单作废原单失败");
        }
        try {
            for (OcBOrder order : ocBOrderList) {
                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(),
                        lgType + "订单合并作废", "", "", user);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("合并订单作废日志异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }

    }

    /**
     * <AUTHOR>
     * @Date 15:58 2021/7/16
     * @Description 构建库存平移参数
     */
    private SgOmsStoTranslationRequest buildSgOmsStoTranslationRequest(List<OcBOrder> orders, OcBOrderRelation ocBOrderRelation, User user) {

        //新订单
        OcBOrder newOrder = ocBOrderRelation.getOrderInfo();
        //新明细
        List<OcBOrderItem> newOrderItemList = ocBOrderRelation.getOrderItemList();

        SgOmsStoTranslationRequest sgOmsStoTranslationRequest = new SgOmsStoTranslationRequest();
        List<SgOmsStoTranslationBillRequest> originalOrders = new ArrayList<>();
        //key :tid + oid + sku + gift + oldidoldid  val:id
        Map<String,Long> map = new HashMap<>();
        for (OcBOrder order : orders) {
            SgOmsStoTranslationBillRequest oldSgOmsStoTranslationBillRequest = new SgOmsStoTranslationBillRequest();
            List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectOrderItemList(order.getId());
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                String key = getItemKey(ocBOrderItem);
                map.put(key,ocBOrderItem.getId());
            }
            oldSgOmsStoTranslationBillRequest.setBillDate(order.getOrderDate());
            oldSgOmsStoTranslationBillRequest.setBillNo(order.getBillNo());
            oldSgOmsStoTranslationBillRequest.setBillType(SgConstantsIF.BILL_TYPE_RETAIL);
            oldSgOmsStoTranslationBillRequest.setId(order.getId());
            originalOrders.add(oldSgOmsStoTranslationBillRequest);
        }
        sgOmsStoTranslationRequest.setOriginalOrders(originalOrders);
        SgOmsStoTranslationBillRequest  newSgOmsStoTranslationBillRequest =new SgOmsStoTranslationBillRequest();
        //新单信息
        List<SgOmsStoTranslationBillRequest.Item> newItems = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : newOrderItemList) {
            SgOmsStoTranslationBillRequest.Item  item = new SgOmsStoTranslationBillRequest.Item();
            item.setOriginalId(map.get(getItemKey(ocBOrderItem)));
            item.setTid(ocBOrderItem.getTid());
            item.setNewId(ocBOrderItem.getId());
            newItems.add(item);
        }
        newSgOmsStoTranslationBillRequest.setBillDate(newOrder.getOrderDate());
        newSgOmsStoTranslationBillRequest.setBillNo(newOrder.getBillNo());
        newSgOmsStoTranslationBillRequest.setBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        newSgOmsStoTranslationBillRequest.setId(newOrder.getId());
        newSgOmsStoTranslationBillRequest.setItems(newItems);

        sgOmsStoTranslationRequest.setNewOrder(newSgOmsStoTranslationBillRequest);
        SgOmsStoTranslationRequest.UserInfo  userInfo = new SgOmsStoTranslationRequest.UserInfo();
        userInfo.setId(user.getId());
        userInfo.setName(user.getName());
        userInfo.setAdClientId((long) user.getClientId());
        userInfo.setEname(user.getEname());
        userInfo.setAdOrgId((long)user.getOrgId());
        sgOmsStoTranslationRequest.setLoginUser(userInfo);
        return sgOmsStoTranslationRequest;
        }
    /**
     * <AUTHOR>
     * @Date 13:44 2021/7/21
     * @Description 查询key
     */
    private String getItemKey(OcBOrderItem ocBOrderItem) {
        StringBuilder sb = new StringBuilder();
        if (ocBOrderItem.getTid() !=null){
            sb.append(ocBOrderItem.getTid()+"_");
        }
        if (ocBOrderItem.getOoid() !=null){
            sb.append(ocBOrderItem.getOoid()+"_");
        }
        if (ocBOrderItem.getIsGift() !=null){
            sb.append(ocBOrderItem.getIsGift()+"_");
        }
        if (ocBOrderItem.getPsCSkuId() !=null){
            sb.append(ocBOrderItem.getPsCSkuId());
        }
        if (ocBOrderItem.getOldSourceItemId() !=null){
            sb.append(ocBOrderItem.getOldSourceItemId());
        }
        return sb.toString();
    }

}
