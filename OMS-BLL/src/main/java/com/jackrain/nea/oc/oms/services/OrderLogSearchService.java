package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderWmsStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.DataPermission;
import com.jackrain.nea.oc.oms.model.result.OrderLogSearchResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.FormatDateUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: 郑立轩
 * @since: 2019/4/16
 * create at : 2019/4/16 14:48
 */
@Slf4j
@Component
public class OrderLogSearchService {
    private static final String logIndex = "oc_b_order";
    private static final String logType = "oc_b_order_log";
    private static final String mainType = "oc_b_order";
    private static final String shopKey = "CP_C_SHOP_PERMISSION_ID";
    private static final String baseKey = "filterPermission";
    private static final String tableName = "OC_B_ORDER_LOG2";
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private CpRpcService cpRpcService;

    public ValueHolder OrderLogSearch(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        User user = querySession.getUser();
        JSONObject resultData = new JSONObject();
        JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
        JSONObject fixedcolumns = param.getJSONObject("fixedcolumns");
        Integer range = param.getInteger("range") == null ? 10 : param.getInteger("range");
        Integer startIndex = param.getInteger("startindex") == null ? 0 : param.getInteger("startindex");
        //ES中获取数据
        JSONObject search = EsHandle(fixedcolumns, range, startIndex, user);
        if (CollectionUtils.isEmpty(search)) {
            resultData.put("start", startIndex);
            resultData.put("row", "");
            resultData.put("totalRowCount", 0);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
            return vh;
        }
        JSONArray data = search.getJSONArray("data");
        if (CollectionUtils.isEmpty(data)) {
            resultData.put("start", startIndex);
            resultData.put("row", "");
            resultData.put("totalRowCount", 0);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
            return vh;
        }
        JSONArray jsonArray = new JSONArray();
        JSONArray jsonArray2 = new JSONArray();
        for (int i = 0; i < data.size(); i++) {
            String id = "'" + data.getJSONObject(i).getString("ID") + "'";
            String orderId = "'" + data.getJSONObject(i).getString("OC_B_ORDER_ID") + "'";
            jsonArray.add(id);
            jsonArray2.add(orderId);
        }
        Integer totalCount = search.getInteger("total");
        String join = StringUtils.join(jsonArray, ",");
        String join2 = StringUtils.join(jsonArray2, ",");
        List<OrderLogSearchResult> logs = new ArrayList<>();
        //组装返回的字段
        List<JSONObject> jsonlogs = new ArrayList<>();
        for (OrderLogSearchResult result : logs) {
            exChangeOutPutField(result);
            String str = JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue);
            JSONObject object = JSONObject.parseObject(str);
            jsonlogs.add(object);
        }
        JSONArray array = ReturnLog(jsonlogs);
        resultData.put("start", startIndex);
        resultData.put("rowCount", range);
        resultData.put("row", array);
        resultData.put("totalRowCount", totalCount);
        vh.put("data", resultData);
        vh.put("code", 0);
        vh.put("message", "success");
        return vh;
    }

    private JSONArray ReturnLog(List<JSONObject> logs) {
        JSONArray array = new JSONArray();
        if (logs != null && logs.size() > 0) {
            for (JSONObject log : logs) {
                log.put("MODIFIEDDATE", FormatDateUtil.formatDate(log.getDate("MODIFIEDDATE"), "yyyy-MM-dd HH:mm:ss"));
                log.put("CREATIONDATE", FormatDateUtil.formatDate(log.getDate("CREATIONDATE"), "yyyy-MM-dd HH:mm:ss"));
                Long orderId = Long.valueOf(log.get("OC_B_ORDER_ID").toString());
                OcBOrder ocBOrder = orderMapper.selectByID(orderId);
                log.put("BILL_NO", log.get("OC_B_ORDER_ID"));
                log.remove("OC_B_ORDER_ID");
                Set<String> keySet = log.keySet();
                JSONObject json = new JSONObject();
                for (String key : keySet) {
                    JSONObject val = new JSONObject();
                    val.put("val", log.get(key));
                    json.put(key.toUpperCase(), val);
                }
                if (ocBOrder == null) {
                    JSONObject val2 = new JSONObject();
                    val2.put("val", "暂无平台单号");
                    json.put("SOURCE_CODE", val2);
                    val2 = new JSONObject();
                    val2.put("val", FormatDateUtil.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
                    json.put("ORDER_DATE", val2);
                    val2 = new JSONObject();
                    val2.put("val", "未查询到WMS撤回状态");
                    json.put("WMS_CANCEL_STATUS", val2);
                    val2 = new JSONObject();
                    val2.put("val", "暂无下单店铺信息");
                    json.put("CP_C_SHOP_ID", val2);
                    val2 = new JSONObject();
                    val2.put("val", "暂无状态，请确认");
                    json.put("ORDER_STATUS", val2);
                    array.add(json);
                    continue;

                }
                //扩展平台单号
                String sourceCode = ocBOrder.getSourceCode();
                JSONObject val2 = new JSONObject();
                if (StringUtils.isEmpty(sourceCode)) {
                    val2.put("val", "暂无平台单号");
                    json.put("SOURCE_CODE", val2);
                } else {
                    val2.put("val", sourceCode);
                    json.put("SOURCE_CODE", val2);
                }
                //扩展下单时间
                val2 = new JSONObject();
                Date orderDate = ocBOrder.getOrderDate();
                //格式化
                String date = FormatDateUtil.formatDate(orderDate, "yyyy-MM-dd HH:mm:ss");
                val2.put("val", date);
                json.put("ORDER_DATE", val2);
                //扩展WMS撤回状态
                val2 = new JSONObject();
                Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
                if (wmsCancelStatus.equals(OcOrderWmsStatus.WMS_FALSE.getVal())) {
                    val2.put("val", OcOrderWmsStatus.WMS_FALSE.getKey());
                    json.put("WMS_CANCEL_STATUS", val2);
                } else if (wmsCancelStatus.equals(OcOrderWmsStatus.WMS_NOTHING.getVal())) {
                    val2.put("val", OcOrderWmsStatus.WMS_NOTHING.getKey());
                    json.put("WMS_CANCEL_STATUS", val2);
                } else if (wmsCancelStatus.equals(OcOrderWmsStatus.WMS_SUCCESS.getVal())) {
                    val2.put("val", OcOrderWmsStatus.WMS_SUCCESS.getKey());
                    json.put("WMS_CANCEL_STATUS", val2);
                } else {
                    val2.put("val", "未查询到WMS撤回状态");
                    json.put("WMS_CANCEL_STATUS", val2);
                }
                //扩展下单店铺
                val2 = new JSONObject();
                String cpCShopTitle = ocBOrder.getCpCShopTitle();
                if (StringUtils.isEmpty(cpCShopTitle)) {
                    val2.put("val", "暂无下单店铺信息");
                    json.put("CP_C_SHOP_ID", val2);
                } else {
                    val2.put("val", cpCShopTitle);
                    json.put("CP_C_SHOP_ID", val2);
                }
                //扩展订单状态
                val2 = new JSONObject();
                Integer orderStatus = ocBOrder.getOrderStatus();
                if (orderStatus.equals(OmsOrderStatus.ORDER_DEFAULT.toInteger())) {
                    val2.put("val", "默认状态");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.CANCELLED.toInteger())) {
                    val2.put("val", "已取消");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.CHECKED.toInteger())) {
                    val2.put("val", "已审核");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.DEAL_DONE.toInteger())) {
                    val2.put("val", "交易完成");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.DELIVERED.toInteger())) {
                    val2.put("val", "物流已送达");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger())) {
                    val2.put("val", "配货中");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.PENDING_WMS.toInteger())) {
                    val2.put("val", "待传WMS");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.PLATFORM_DELIVERY.toInteger())) {
                    val2.put("val", "平台发货");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.PRE_SALE.toInteger())) {
                    val2.put("val", "预售");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.SUBSTITUTE.toInteger())) {
                    val2.put("val", "代发");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())) {
                    val2.put("val", "缺货");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.SYS_VOID.toInteger())) {
                    val2.put("val", "系统作废");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.TO_BE_ASSIGNED.toInteger())) {
                    val2.put("val", "待分配");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger())) {
                    val2.put("val", "待审核");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.UNPAID.toInteger())) {
                    val2.put("val", "未付款");
                    json.put("ORDER_STATUS", val2);
                } else if (orderStatus.equals(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())) {
                    val2.put("val", "仓库发货");
                    json.put("ORDER_STATUS", val2);
                } else {
                    val2.put("val", "暂无状态，请确认");
                    json.put("ORDER_STATUS", val2);
                }
                array.add(json);
            }
        }
        return array;

    }


    private JSONObject EsHandle(JSONObject fixedcolumns, Integer range, Integer startIndex, User user) {
        JSONObject whereKey = new JSONObject();
        JSONObject filterKey = new JSONObject();
        JSONObject childKey = new JSONObject();
        JSONArray orderKeys = new JSONArray();
        JSONObject order = new JSONObject();
        order.put("asc", false);
        order.put("name", "CREATIONDATE");
        orderKeys.add(order);
        String[] returnFiled = {"ID", "OC_B_ORDER_ID"};
//        if (CollectionUtils.isEmpty(fixedcolumns)) {
//            whereKey.put("AD_CLIENT_ID", 37);
//            whereKey.put("AD_ORG_ID", 27);
//            return ElasticSearchUtil.search(logIndex, logType, mainType, whereKey, filterKey, orderKeys, childKey, range, startIndex, returnFiled);
//
//        }
        //用户名称
        String user_name = fixedcolumns.getString("USER_NAME");
        //日志类型
        //String log_type = fixedcolumns.getString("LOG_TYPE");
        //平台单号
        String source_code = fixedcolumns.getString("SOURCE_CODE");
        //订单编号
        String bill_no = fixedcolumns.getString("BILL_NO");
        //下单店铺
        JSONArray cp_c_shop_id = fixedcolumns.getJSONArray("CP_C_SHOP_ID");
        //订单状态
        JSONArray order_status = fixedcolumns.getJSONArray("ORDER_STATUS");

        //增加日志类型枚举
        JSONArray log_type = fixedcolumns.getJSONArray("LOG_TYPE");
        //支持根据ID查询
        Integer id = fixedcolumns.getInteger("ID");
        if (id != null) {
            childKey.put("ID", id);
        }
        if (StringUtils.isNotEmpty(user_name)) {
            childKey.put("USER_NAME", user_name);
        }
        if (!CollectionUtils.isEmpty(log_type)) {
            JSONArray jsonArray = new JSONArray();
            for (Object logType : log_type) {
                jsonArray.add(logType.toString().replaceAll("=", ""));
            }
            childKey.put("LOG_TYPE", jsonArray);
        }
        if (StringUtils.isNotEmpty(source_code)) {
            whereKey.put("SOURCE_CODE", source_code);
        }
        if (StringUtils.isNotEmpty(bill_no)) {
            childKey.put("OC_B_ORDER_ID", bill_no);
        }
        if (!CollectionUtils.isEmpty(cp_c_shop_id)) {
            whereKey.put("CP_C_SHOP_ID", cp_c_shop_id);
        } else {
            if (!user.isAdmin()) {
                JSONArray jsonArray = this.getPrim(user, tableName);
                if (jsonArray.size() > 0) {
                    whereKey.put("CP_C_SHOP_ID", this.getPrim(user, tableName));//查询店铺权限
                } else {
                    whereKey.put("CP_C_SHOP_ID", -1);//查询店铺权限
                }
            }
        }
        if (!CollectionUtils.isEmpty(order_status)) {
            List<String> status = order_status.stream().map(s -> s.toString().replace('=', ' ').trim()).collect(Collectors.toList());
            whereKey.put("ORDER_STATUS", status);
        }
        if (fixedcolumns.containsKey("CREATIONDATE")) {
            String orderDate = fixedcolumns.getString("CREATIONDATE");
            String[] orderSplitDate = orderDate.split("~");
            String esDate = checkDate(orderSplitDate[0], orderSplitDate[1]);
            filterKey.put("CREATIONDATE", esDate);
        }
        //JSONObject search = ElasticSearchUtil.search(logIndex, logType, mainType, childKey, filterKey, null, whereKey, range, startIndex, returnFiled);

        JSONObject search = ElasticSearchUtil.searchChild(logIndex, logType, mainType, childKey, filterKey, orderKeys, whereKey, range, startIndex, returnFiled);
        return search;
    }


    /**
     * 日期校验
     *
     * @return
     */
    public String checkDate(String begindate, String endDate) {
        //type = 1 开始日期和es的下单日期比较
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        if (StringUtils.isEmpty(begindate) || StringUtils.isEmpty(endDate)) {
            return "";
        }
        try {
            String result = sdf.parse(begindate).getTime() + "~" + sdf.parse(endDate).getTime();
            return result;

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";

    }

    /**
     * 订单值:  转换可视化
     *
     * @param orderLogSearchResult 订单结果
     */
    private void exChangeOutPutField(OrderLogSearchResult orderLogSearchResult) {
        if (orderLogSearchResult == null) {
            return;
        }
        String logTypeName = OrderLogTypeEnum.enumToStringBykey(orderLogSearchResult.getLogType());
        orderLogSearchResult.setLogType(logTypeName);

    }

    public JSONArray getPrim(User usr, String table) {
        JSONObject jsnObj;
        try {
            ValueHolderV14 perVh = cpRpcService.getAllPermissionService(table, usr);
            if (perVh == null) {
                throw new NDSException("获取用户权限失败");
            } else if (ResultCode.FAIL == perVh.getCode()) {
                throw new NDSException("获取用户权限失败");
            }
            jsnObj = perVh.toJSONObject();
            if (jsnObj != null) {
                String dtaKey = "data";
                JSONObject data = jsnObj.getJSONObject(dtaKey);
                if (data == null) {
                    return null;
                }
                JSONObject basePem = data.getJSONObject(baseKey);
                Set<String> priKeys = basePem.keySet();
                if (priKeys.contains(shopKey)) {
                    JSONArray shop = basePem.getJSONArray(shopKey);
                    JSONArray shopArray = new JSONArray();
                    List<DataPermission> shopList = JSON.parseArray(shop.toJSONString(), DataPermission.class);
                    for (DataPermission dataPermission : shopList) {
                        shopArray.add(dataPermission.getId());
                    }
                    return shopArray;
                }

            }
        } catch (Exception e) {
            log.error(LogUtil.format("获取用户权限异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("获取用户权限异常");
        }
        return new JSONArray();
    }
}
