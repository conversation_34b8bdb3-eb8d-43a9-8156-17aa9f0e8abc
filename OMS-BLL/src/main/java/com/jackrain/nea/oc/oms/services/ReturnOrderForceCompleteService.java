package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.IsReservedEnum;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderSource;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpOrderReturnRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Desc : 退换货单强制入库完成
 * <AUTHOR> xiWen
 * @Date : 2020/12/21
 */
@Slf4j
@Component
public class ReturnOrderForceCompleteService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBReturnBuildService ocBReturnBuildService;

    @Autowired
    private ReturnOrderAuditService returnOrderAuditService;

    @Autowired
    private OcCancelChangingOrRefundService cancelChangingOrRefundService;


    /**
     * 强制完成 按钮
     *
     * @param param
     * @param user
     * @return
     */
    public ValueHolderV14 forcedCompletion(JSONObject param, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (log.isDebugEnabled()) {
            log.debug("强制完成入参#{}", JSONObject.toJSONString(param));
        }
        JSONArray ids = param.getJSONArray("ids");
        if (ids == null || ids.size() == 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请选择一个退换单进行完成");
            return vh;
        }
        List<Long> longs = JSON.parseArray(ids.toJSONString(), Long.class);

        //分批进行修改
        int success = 0;
        int fail = 0;
        ReturnOrderForceCompleteService bean = ApplicationContextHandle.getBean(ReturnOrderForceCompleteService.class);
        for (Long id : longs) {
            String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(id);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    fail++;
                    continue;
                }
                OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectById(id);
                boolean result = bean.forceComplete(returnOrder, user);
                if (result) {
                    success++;
                    continue;
                }
                fail++;
            } catch (Exception ex) {
                fail++;
                log.error(LogUtil.format("forcedCompletion,异常信息为:{}",id), Throwables.getStackTraceAsString(ex));
            } finally {
                redisLock.unlock();
            }
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("强制完成,成功" + success + "条，失败" + fail + "条数据");
        return vh;
    }


    /**
     * 强制完成
     *
     * @param returnOrder 退换货单
     * @param user        操作用户
     * @return
     */
    @Transactional
    public boolean forceComplete(OcBReturnOrder returnOrder, User user) {

        if (!ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal().equals(returnOrder.getReturnStatus())) {
            return false;
        }

        // 1. 退货
        if (OcReturnBillTypeEnum.RETURN.getVal().equals(returnOrder.getBillType())) {
            boolean updateResult = returnOrderAuditService.updateReturnOrderStatus(returnOrder.getId(), user);
            if (updateResult) {
                cancelChangingOrRefundService.insertReturnOrederLog("强制完成", "强制完成成功",
                        null, user, returnOrder.getId());
                return true;
            }
            return false;
        }
        // 2. 换货
        if (OcReturnBillTypeEnum.EXCHANGE.getVal().equals(returnOrder.getBillType())) {
            OcBOrder exchangeOrder = null;
            Long exchangeOrderId = returnOrderAuditService.selectOrder(returnOrder.getId());
            if (exchangeOrderId != null) {
                exchangeOrder = ocBOrderMapper.selectById(exchangeOrderId);
            }
            // 2.1  勾选
            if (IsReservedEnum.NO.getValue().equals(returnOrder.getIsReserved())) {

                // 2.1.1 不存在发货单: 生成
                if (exchangeOrder == null) {
                    IpOrderReturnRelation relation = returnOrderAuditService.buildExchangeOrderParam(returnOrder);
                    relation.getOcBOrder().setOrderSource(OmsOrderSource.MANUAL_ADD.getEcode());
                    ValueHolderV14 result = ocBReturnBuildService.buildExchange(relation, user);
                    AssertUtil.assertException(result == null || !result.isOK(), "生成换货订单失败");
                }
            }

            if (exchangeOrder != null) {
                // 2.1.2 存在释放
                String s = returnOrderAuditService.releaseHoldOrder(returnOrder, true, user);
                cancelChangingOrRefundService.insertReturnOrederLog("强制完成", "释放Hold单成功" + s,
                        null, user, returnOrder.getId());
            }
            returnOrderAuditService.updateReturnOrderStatus(returnOrder.getId(), user);
            cancelChangingOrRefundService.insertReturnOrederLog("强制完成", "强制完成成功", null,
                    user, returnOrder.getId());
            return true;
        }

        // 3. 其它
        return false;
    }

}
