package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.util.StringUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.QianNiuInterceptOrderCallbackModel;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.IpBOrderLockLogMapper;
import com.jackrain.nea.oc.oms.mapper.IpBOrderLockMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.IpOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.model.resources.LockOrderConstant;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLock;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLockLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLockLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.ReturnOrderTransferUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.GetCustomerIpUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Descroption 淘宝锁单
 * <AUTHOR>
 * @Date 2019/10/9 20:47
 */
@Slf4j
@Component
public class IpOrderLockService {

    @Autowired
    private IpBOrderLockMapper ipBOrderLockMapper;

    @Autowired
    private IpBOrderLockLogMapper ipBOrderLockLogMapper;

    @Autowired
    private ReturnOrderTransferUtil returnOrderTransferUtil;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private GetCustomerIpUtil getCustomerIpUtil;

    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private CpRpcService cpRpcService;
    /**
     * 订单日志服务
     **/
    @Autowired
    private OmsOrderLogService logService;
    /**
     * 订单拦截服务
     **/
    @Autowired
    private OrderInterceptionService orderInterceptionService;
    /**
     * 订单反审核服务
     **/
    @Autowired
    private OcBOrderTheAuditService auditService;

    public IpOrderLockRelation getLockRelation(Long lockId) {
        IpOrderLockRelation ipOrderLockRelation = null;
        IpBOrderLock orderLock = ipBOrderLockMapper.selectById(lockId);
        if (orderLock != null) {
            ipOrderLockRelation = new IpOrderLockRelation();
            //1.设置淘宝锁单中间表对象
            ipOrderLockRelation.setOrderLock(orderLock);
            //2.设置全渠道订单表
            //获取分库键
            String sourceCode = orderLock.getSourceCode();

            Set<Long> ids = ES4Order.findIdByTidAndOrderType(sourceCode, OrderTypeEnum.DIFFPRICE.getVal());

            List<Long> orderIds = new ArrayList<>(ids);

            //生成所有订单List
            if (CollectionUtils.isNotEmpty(orderIds)) {
                List<OcBOrder> orders = ocBOrderMapper.selectOrderListByIds(orderIds);
                ipOrderLockRelation.setOcBOrders(orders);
            }
        }
        return ipOrderLockRelation;
    }

    /**
     * @param orderLock
     * @return boolean
     * @Description 异常修改锁单中间表备注及推送ES并且记录日志
     * <AUTHOR>
     * @date 2019/10/9 17:01
     */
    public boolean updateLockErrorLog(IpBOrderLock orderLock) {
        String sysRemark = SysNotesConstant.SYS_REMARK55;
        return this.updateLockOrder(IpOrderLockStatusEnum.LOCK_FAIL.getKey(),
                sysRemark, null, orderLock);
    }

    /**
     * @param billStatus
     * @param remark
     * @param orderLock
     * @param orderLockStatus
     * @return boolean
     * @Description 手动修改锁单中间表相关信息
     * <AUTHOR>
     * @date 2019/10/9 18:14
     */
    public boolean updateLockOrder(String billStatus,
                                   String remark, String orderLockStatus, IpBOrderLock orderLock) {
        try {
            User rootUser = SystemUserResource.getRootUser();
            orderLock.setBillStatus(billStatus);
            orderLock.setSysRemark(remark);
            orderLock.setOrderLockStatus(orderLockStatus);
            orderLock.setModifieddate(new Date());
            orderLock.setModifierename(rootUser.getEname());
            orderLock.setModifiername(rootUser.getName());
            int i = ipBOrderLockMapper.updateById(orderLock);
            //推送es
            if (i > 0) {
//                SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
//                        OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
//                        orderLock, orderLock.getId());
                return true;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新淘宝锁单中间表失败,Id:{}", orderLock.getId(), e);
            throw new NDSException(e);
        }
        return false;
    }

    /**
     * @param logType
     * @param logMessage
     * @param orderId
     * @param user
     * @return void
     * @Description 插入锁单日志
     * <AUTHOR>
     * @date 2019/10/9 18:36
     */
    public void insetIpOrderLockLog(String logType, String logMessage, Long lockOrderId, Long orderId, User user) {
        try {
            QueryWrapper<IpBOrderLockLog> wrapper = new QueryWrapper<>();
            IpBOrderLockLog preOrderLockLog = ipBOrderLockLogMapper.selectOrderLockByTopOne(lockOrderId);
            if (preOrderLockLog != null && preOrderLockLog.getLogMessage() != null && preOrderLockLog.getLogMessage().equals(logMessage)
                    && ((orderId == null && preOrderLockLog.getOcBOrderId() == null) || (orderId != null && orderId.equals(preOrderLockLog.getOcBOrderId()))
            )) {
                //判断是否跟最近的日志一样
                IpBOrderLockLog log = new IpBOrderLockLog();
                log.setModifierid(Long.valueOf(user.getId()));
                log.setModifieddate(new Date());
                log.setModifiername(user.getName());
                QueryWrapper<IpBOrderLockLog> upwrapper = new QueryWrapper<>();
                upwrapper.eq("ip_b_order_lock_id", preOrderLockLog.getIpBOrderLockId());
                upwrapper.eq("id", preOrderLockLog.getId());
                int updateNum = ipBOrderLockLogMapper.update(log, upwrapper);
                if (updateNum > 0) {
//                  IpBOrderLockLog esLog = ipBOrderLockLogMapper.selectById(preOrderLockLog.getId());
//                    SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
//                            OcElasticSearchIndexResources.IP_B_ORDER_LOCK_LOG_TYPE_NAME, esLog,
//                            esLog.getId());
                }
            } else {
                IpBOrderLockLog log = new IpBOrderLockLog();
                log.setIpBOrderLockId(lockOrderId);
                log.setOcBOrderId(orderId);
                log.setLogType(logType);
                log.setLogMessage(logMessage);
                String ip = user.getLastloginip();
                if (StringUtil.isEmpty(ip)) {
                    ip = getCustomerIpUtil.getIp(null);
                }
                log.setIpAddress(ip);
                this.saveSysLog(log, user);
                int insertNum = ipBOrderLockLogMapper.insert(log);
//                if (insertNum > 0) {
////                    SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
////                            OcElasticSearchIndexResources.IP_B_ORDER_LOCK_LOG_TYPE_NAME, log,
////                            log.getId());
//                }
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + "插入锁单日志异常!", e);
            throw new NDSException(e);
        }
    }

    /**
     * @param ipBOrderLockLog
     * @param operateUser
     * @return void
     * @Description 封装日志参数
     * <AUTHOR>
     * @date 2019/10/9 18:31
     */
    public void saveSysLog(IpBOrderLockLog ipBOrderLockLog, User operateUser) {
        Long logId = ModelUtil.getSequence(OcElasticSearchIndexResources.IP_B_ORDER_LOCK_LOG_TYPE_NAME);
        ipBOrderLockLog.setId(logId);
        ipBOrderLockLog.setOwnerename(operateUser.getName());
        ipBOrderLockLog.setIsactive("Y");
        returnOrderTransferUtil.setOperateUserInfo(ipBOrderLockLog, operateUser);
    }

    /**
     * @param order
     * @return void
     * @Description 处理锁单根据不同的订单状态
     * <AUTHOR>
     * @date 2019/10/10 10:47
     */
    public boolean LockByOrderStatus(OcBOrder order, IpBOrderLock orderLock, User operateUser) {
        try {
            int orderStatus = order.getOrderStatus();
            if (OcOrderCheckBoxEnum.CHECKBOX_PENDING_ALLOCATED.getVal() == orderStatus || OcOrderCheckBoxEnum.CHECKBOX_PENDING_WMS.getVal() == orderStatus) {
                //待分配、待传WMS
                LockByOrderStatusSence01(order, orderLock, operateUser);
            } else if (OcOrderCheckBoxEnum.CHECKBOX_WAREHOUSE_DELIVERY.getVal() == orderStatus || OcOrderCheckBoxEnum.CHECKBOX_PLATFORM_DELIVERY.getVal() == orderStatus) {
                //仓库发货、平台发货
                LockByOrderStatusSence02(order, orderLock, operateUser);
            } else if (OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal() == orderStatus
                    || OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal() == orderStatus) {
                //待审核、缺货
                LockByOrderStatusSence03(order, orderLock, operateUser);
            } else if (OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal() == orderStatus || OcOrderCheckBoxEnum.CHECKBOX_IN_DISTRIBUTION.getVal() == orderStatus) {
                //已审核、配货中
                LockByOrderStatusSence04(order, operateUser);
            } else if (OcOrderCheckBoxEnum.CHECKBOX_SYSTEM_INVALIDATION.getVal() == orderStatus || OcOrderCheckBoxEnum.CHECKBOX_CANCELLED.getVal() == orderStatus) {
                //已作废、已取消 无需打拦截标
            }
            return true;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 根据订单状态处理锁单异常!", e);
            //插入锁单中间表日志
            insetIpOrderLockLog(OrderLockLogTypeEnum.LOCK.getKey(), e.getMessage(), orderLock.getId(), order.getId(),
                    operateUser);
            return false;
        }
    }

    /**
     * @param order
     * @param orderLock
     * @param operateUser
     * @return void
     * @Description 处理锁单-待分配、待传WMS
     * <AUTHOR>
     * @date 2019/10/10 14:24
     */
    private void LockByOrderStatusSence01(OcBOrder order, IpBOrderLock orderLock, User operateUser) {
        //插入锁单中间表日志
        String errMsg = "订单状态为" + OcOrderCheckBoxEnum.enumToStringByValue(order.getOrderStatus()) + "，待下次锁单;";
        this.insetIpOrderLockLog(OrderLockLogTypeEnum.LOCK.getKey(), errMsg, orderLock.getId(), order.getId(),
                operateUser);
        throw new NDSException(errMsg);
    }

    /**
     * @param order
     * @param orderLock
     * @param operateUser
     * @return void
     * @Description 处理锁单-仓库发货、平台发货
     * <AUTHOR>
     * @date 2019/10/10 14:29
     */
    private void LockByOrderStatusSence02(OcBOrder order, IpBOrderLock orderLock, User operateUser) {
        //插入锁单中间表日志

        String errMsg = "订单状态为" + OcOrderCheckBoxEnum.enumToStringByValue(order.getOrderStatus()) + "，锁定失败;";
        this.insetIpOrderLockLog(OrderLockLogTypeEnum.LOCK.getKey(), errMsg, orderLock.getId(), order.getId(),
                operateUser);
        //修改渠道订单锁单状态:锁单失败并插入日志
        updateOrderLockStatus(orderLock, order, operateUser, OcOrderLockStatusEnum.LOCK_FAIL.getKey());
        throw new NDSException(errMsg);
    }

    /**
     * @param order
     * @param orderLock
     * @param operateUser
     * @return void
     * @Description 处理锁单-已作废、待审核、缺货
     * <AUTHOR>
     * @date 2019/10/10 14:29
     */
    private void LockByOrderStatusSence03(OcBOrder order, IpBOrderLock orderLock, User operateUser) {
        //订单拦截
        JSONArray ids = new JSONArray();
        ids.add(order.getId());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ids", ids);
        ValueHolder vh = orderInterceptionService.orderInterception(jsonObject, operateUser);
        if (vh != null) {
            //拦截成功
            if (ResultCode.SUCCESS == (int) vh.get("code") ||
                    (vh.get("message") != null && vh.get("message").toString().contains("已挂起"))) {
                //插入锁单中间表日志
                String errMsg = "调用订单挂起服务成功：" + vh.get("message").toString();
                this.insetIpOrderLockLog(OrderLockLogTypeEnum.LOCK.getKey(), errMsg, orderLock.getId(), order.getId()
                        , operateUser);
            } else {
                throw new NDSException("订单标记挂起失败:" + vh.get("message").toString() + "锁单失败；");
            }
        } else {
            throw new NDSException("订单标记挂起异常,锁单失败；");
        }
    }

    /**
     * @param order
     * @param operateUser
     * @return void
     * @Description 处理锁单-已审核、配货中
     * <AUTHOR>
     * @date 2019/10/10 14:29
     */
    private void LockByOrderStatusSence04(OcBOrder order, User operateUser) {
        //订单拦截
        JSONArray ids = new JSONArray();
        ids.add(order.getId());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ids", ids);
        ValueHolder vh = orderInterceptionService.orderInterception(jsonObject, operateUser);
        if (vh != null) {
            //拦截成功执行反审核操作
            if (ResultCode.SUCCESS == (int) vh.get("code") ||
                    (vh.get("message") != null && vh.get("message").toString().contains("已挂起"))) {
                boolean isSuccess = auditService.updateOrderInfo(operateUser, new ValueHolderV14(), order.getId(), LogTypeEnum.NOT_CAPTURED_SCENE.getType());
                //反审核成功
                if (!isSuccess) {
                    throw new NDSException("订单反审核失败，锁单失败!");
                }
            } else {
                throw new NDSException("订单反审核失败,原因：" + vh.get("message").toString() + "锁单失败!");
            }
        } else {
            throw new NDSException("订单反审核异常，锁单失败！");
        }
    }


    /**
     * @param orderLock
     * @param order
     * @param operateUser
     * @return void
     * @Description 修改全渠道订单信息
     * <AUTHOR>
     * @date 2019/10/9 19:20
     */
    public Boolean updateOrderLockStatus(IpBOrderLock orderLock, OcBOrder order, User operateUser, Long lockStatus) {
        try {
            //1.修改订单锁单状态
            OcBOrder upOrder = new OcBOrder();
            upOrder.setLockStatus(lockStatus);
            upOrder.setId(order.getId());
            int updateNum = ocBOrderMapper.updateById(upOrder);
//            if (updateNum > 0) {
//                OcBOrder esOrder = ocBOrderMapper.selectByID(order.getId());
//                SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
//                        OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
//                        esOrder, order.getId());
//            }
            //2.插入订单日志表
            logService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.ORDER_LOCK.getKey(),
                    "更新主表锁单状态:" + OcOrderLockStatusEnum.toName(lockStatus), null, null, operateUser);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 锁单修改订单锁单状态异常!", e);
            //插入锁单中间表日志
            insetIpOrderLockLog(OrderLockLogTypeEnum.LOCK.getKey(), e.getMessage(), orderLock.getId(), order.getId(),
                    operateUser);
            return false;
        }
        return true;
    }

    /**
     * @param orderIdList
     * @return void
     * @Description 处理锁单中间表锁单状态
     * <AUTHOR>
     * @date 2019/10/11 9:59
     */
    public String dealLockOrder(List<Long> orderIdList, IpBOrderLock orderLock, User operateUser) {
        String lockStatus = IpOrderLockStatusEnum.WAIT_LOCK.getKey();
        List<OcBOrder> newOrderList = ocBOrderMapper.selectByIdsList(orderIdList);
        int orderNum = newOrderList.size();
        int waitLockNum = 0;
        int lockedNum = 0;
        int lockFailNum = 0;
        StringBuilder remarkBuf = new StringBuilder();
        for (OcBOrder order : newOrderList) {
            if (order.getLockStatus() == null || OcOrderLockStatusEnum.WAIT_LOCK.getKey() == order.getLockStatus()) {
                waitLockNum++;
                remarkBuf.append(String.format("[%d]：待锁单;", order.getId()));
            } else {
                if (OcOrderLockStatusEnum.LOCKED.getKey() == order.getLockStatus()) {
                    lockedNum++;
                    remarkBuf.append(String.format("[%d]：已锁单;", order.getId()));
                } else if (OcOrderLockStatusEnum.LOCK_FAIL.getKey() == order.getLockStatus()) {
                    lockFailNum++;
                    remarkBuf.append(String.format("[%d]：锁单失败;", order.getId()));
                }
            }
        }
        if (waitLockNum != 0) {
            //部分待锁单
            if (waitLockNum != orderNum) {
                lockStatus = IpOrderLockStatusEnum.PART_LOCKED.getKey();
                updateLockOrder(lockStatus, "", remarkBuf.toString(), orderLock);
            }
        } else {
            if (lockFailNum == orderNum) {
                //全锁单失败
                lockStatus = IpOrderLockStatusEnum.LOCK_FAIL.getKey();
                updateLockOrder(lockStatus, "", remarkBuf.toString(), orderLock);
                insetIpOrderLockLog(OrderLockLogTypeEnum.LOCK.getKey(), SysNotesConstant.SYS_REMARK55,
                        orderLock.getId(), null, operateUser);
            } else if (lockedNum == orderNum) {
                //全锁单成功
                lockStatus = IpOrderLockStatusEnum.LOCKED.getKey();
                //更新锁单时间
                orderLock.setLockTime(new Date());
                updateLockOrder(lockStatus, "", remarkBuf.toString(), orderLock);
                insetIpOrderLockLog(OrderLockLogTypeEnum.LOCK.getKey(), SysNotesConstant.SYS_REMARK56,
                        orderLock.getId(), null, operateUser);
            } else {
                //存在已锁定和锁单失败
                lockStatus = IpOrderLockStatusEnum.PART_LOCK_FAIL.getKey();
                updateLockOrder(lockStatus, "", remarkBuf.toString(), orderLock);
                insetIpOrderLockLog(OrderLockLogTypeEnum.LOCK.getKey(), SysNotesConstant.SYS_REMARK54,
                        orderLock.getId(), null, operateUser);
            }
        }
        return lockStatus;
    }

    /**
     * @param orderLock
     * @param errCode
     * @param operateUser
     * @return void
     * @Description 调用千牛锁单接口
     * <AUTHOR>
     * @date 2019/10/11 14:02
     */
    public void dealQianNiuInterceptOrder(IpBOrderLock orderLock, String errCode, User operateUser) {
        try {
            ValueHolder vh = new ValueHolder();
            String logMsg = "";
            try {
                vh = ipRpcService.intercaptOrderCallback(getQianNiuModel(orderLock, errCode));
                if (ResultCode.SUCCESS == (int) vh.get("code")) {
                    logMsg = "千牛锁单反馈回执成功！";
                    updateLockOrderCallBackInfo(orderLock.getId(), LockOrderConstant.RETURN_QN_SUCCESS, logMsg,
                            operateUser);
                } else {
                    logMsg = "千牛锁单反馈回执失败，原因：" + vh.get("message").toString();
                    updateLockOrderCallBackInfo(orderLock.getId(), LockOrderConstant.RETURN_QN_FAIL, logMsg,
                            operateUser);
                }

            } catch (Exception ex) {
                //异常
                log.error(this.getClass().getName() + " 千牛锁单反馈回执异常：" + ex.toString());
                logMsg = "千牛锁单反馈回执失败，异常：" + ex.toString();
                updateLockOrderCallBackInfo(orderLock.getId(), LockOrderConstant.RETURN_QN_FAIL, logMsg, operateUser);
            }
            insetIpOrderLockLog(OrderLockLogTypeEnum.CALLBACK.getKey(), logMsg, orderLock.getId(), null, operateUser);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新锁单中间表失败,Id:{}", orderLock.getId(), e);
            throw new NDSException(e);
        }
    }

    /**
     * @param id
     * @return
     * @Description 更新锁单表回执接口信息
     * <AUTHOR>
     * @date 2019-10-11 2019-10-11
     */
    private void updateLockOrderCallBackInfo(Long id, int ReturnQn, String failReason, User operateUser) {
        IpBOrderLock orderLock = new IpBOrderLock();
        orderLock.setId(id);
        orderLock.setReturnQn(ReturnQn);
        orderLock.setFailReason(failReason);
        orderLock.setModifieddate(new Date());
        orderLock.setModifierename(operateUser.getEname());
        orderLock.setModifiername(operateUser.getName());
        int i = ipBOrderLockMapper.updateById(orderLock);
//        try {
//            //推送es
//            if (i > 0) {
//                IpBOrderLock esorderLock = ipBOrderLockMapper.selectById(id);
//                SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
//                        OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
//                        esorderLock, orderLock.getId());
//            }
//        } catch (Exception ex) {
//            throw new NDSException(ex);
//        }
    }

    /**
     * @param orderLock
     * @param errCode
     * @return com.jackrain.nea.ip.model.QianNiuInterceptOrderCallbackModel
     * @Description 生成千牛锁单模型
     * <AUTHOR>
     * @date 2019/10/11 14:01
     */
    public QianNiuInterceptOrderCallbackModel getQianNiuModel(IpBOrderLock orderLock, String errCode) {
        QianNiuInterceptOrderCallbackModel qianniuModel = new QianNiuInterceptOrderCallbackModel();
        qianniuModel.setSessionKey(cpRpcService.getSessionKey(orderLock.getCpCShopId()));
        qianniuModel.setErrorCode(errCode);
        qianniuModel.setTid(orderLock.getSourceCode());
        qianniuModel.setSubOrderIds(orderLock.getItemOrderNo().split(","));
        if (IpOrderLockStatusEnum.LOCK_FAIL.getKey().equals(orderLock.getBillStatus())) {
            qianniuModel.setSuccess(false);
        } else if (IpOrderLockStatusEnum.LOCKED.getKey().equals(orderLock.getBillStatus())) {
            qianniuModel.setSuccess(true);
        }
        return qianniuModel;
    }

}
