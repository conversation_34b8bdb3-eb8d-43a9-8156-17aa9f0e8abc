package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCStickerItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCStickerMapper;
import com.jackrain.nea.oc.oms.model.enums.GiftTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.StickerAppointDimensionEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.StCStickerRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCSticker;
import com.jackrain.nea.oc.oms.model.table.StCStickerItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.StRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgNewRpcService;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.util.OrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2023/2/24 10:29
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsStickerService {

    @Autowired
    private StCStickerMapper stCStickerMapper;
    @Autowired
    private StCStickerItemMapper stCStickerItemMapper;
    @Autowired
    private SgNewRpcService sgNewRpcService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;


    /**
     * 贴纸策略缓存过期时间（分钟）
     */
    private static final int STICKER_CACHE_EXPIRE_MINUTES = 30;

    /**
     * 处理贴纸策略
     *
     * @param orderParam 订单参数
     * @param user       用户
     */
    public void handelStickerService(OcBOrderParam orderParam, User user) {
        OcBOrder ocBOrder = orderParam.getOcBOrder();
        log.info(LogUtil.format("OmsStickerService.handelStickerService start orderId:{}",
                "OmsStickerService.handelStickerService"), ocBOrder.getId());
        try {
            // 判断TOC或TOB残次订单
            String saleProductAttr = ocBOrder.getSaleProductAttr();
            if (OmsOrderUtil.isToCCcOrder(ocBOrder) || OrderSaleProductAttrEnum.isToBCC(saleProductAttr)) {
                log.info(LogUtil.format("OmsStickerService.handelStickerService 残次订单不走贴纸策略 orderId:{}",
                        "OmsStickerService.handelStickerService"), ocBOrder.getId());
                return;
            }

            // b. 仅电商销售业务类型执行（中台周期购订单、中台周期购提货等非电商销售业务类型不走贴纸策略）
            String businessTypeCode = ocBOrder.getBusinessTypeCode();
            if (StringUtils.isEmpty(businessTypeCode) ||
                    !OrderBusinessTypeCodeEnum.E_COMMERCE_SALE_ORDER.getCode().equals(businessTypeCode)) {
                log.info(LogUtil.format("OmsStickerService.handelStickerService 非电商销售业务类型不走贴纸策略 orderId:{}",
                        "OmsStickerService.handelStickerService"), ocBOrder.getId());
                return;
            }

            // c. 补发、复制：不走贴纸策略
            if (ocBOrder.getOrderType() != null &&
                    (OrderTypeEnum.REISSUE.getVal().equals(ocBOrder.getOrderType()) ||
                            YesNoEnum.Y.getVal().equals(ocBOrder.getIsCopyOrder()))) {
                log.info(LogUtil.format("OmsStickerService.handelStickerService 补发、复制订单不走贴纸策略 orderId:{}",
                        "OmsStickerService.handelStickerService"), ocBOrder.getId());
                return;
            }
            // e. 根据店铺信息查询贴纸策略
            List<StCStickerRelation> stickerRelations = getStickerRelationsByShopId(ocBOrder.getCpCShopId());
            if (CollectionUtils.isEmpty(stickerRelations)) {
                log.info(LogUtil.format("OmsStickerService.handelStickerService 不存在贴纸策略 orderId:{}",
                        "OmsStickerService.handelStickerService"), ocBOrder.getId());
                return;
            }
            // g. 支付时间为空直接return
            Date payTime = ocBOrder.getPayTime();
            if (payTime == null) {
                log.info(LogUtil.format("OmsStickerService.handelStickerService 支付时间为空不执行贴纸策略 orderId:{}",
                        "OmsStickerService.handelStickerService"), ocBOrder.getId());
                return;
            }
            // h. 按照支付时间过滤贴纸策略
            List<StCStickerRelation> filteredRelations = stickerRelations.stream()
                    .filter(relation -> {
                        StCSticker sticker = relation.getStCSticker();
                        return payTime.compareTo(sticker.getStartTime()) >= 0 &&
                                payTime.compareTo(sticker.getEndTime()) <= 0;
                    })
                    .collect(Collectors.toList());
            // i. 过滤之后不存在直接return
            if (CollectionUtils.isEmpty(filteredRelations)) {
                log.info(LogUtil.format("OmsStickerService.handelStickerService 没有符合支付时间的贴纸策略 orderId:{} payTime:{}",
                        "OmsStickerService.handelStickerService"), ocBOrder.getId(), payTime);
                return;
            }
            // j. 过滤之后理论上不会存在多条满足条件的策略，如果存在多条选创建时间早的那条
            if (filteredRelations.size() > 1) {
                filteredRelations.sort(Comparator.comparing(relation -> relation.getStCSticker().getCreationdate()));
            }
            StCStickerRelation selectedRelation = filteredRelations.get(0);
            StCSticker sticker = selectedRelation.getStCSticker();
            // 查找匹配的贴纸策略明细
            StCStickerItem matchedStickerItem = findMatchedStickerItem(ocBOrder, selectedRelation);
            if (matchedStickerItem == null) {
                log.info(LogUtil.format("OmsStickerService.handelStickerService 没有匹配到贴纸策略明细 tid:{},stickerName:{}",
                        "OmsStickerService.handelStickerService"), ocBOrder.getTid(), sticker.getName());
                return;
            }
            // 构建贴纸商品
            OcBOrderItem stickerItem = buildOcBOrderItem(matchedStickerItem, ocBOrder);
            List<OcBOrderItem> stickerItems = new ArrayList<>();
            stickerItems.add(stickerItem);
            // 调用sg占用贴纸的库存
            boolean result = sgNewRpcService.findSourceSticker(ocBOrder, stickerItems, user);
            if (result) {
                // 保存订单明细
                ocBOrderItemMapper.batchInsert(stickerItems);
                OcBOrder updateOcBOrder = new OcBOrder();
                updateOcBOrder.setId(ocBOrder.getId());
                // 更新参数中的订单明细列表
                List<OcBOrderItem> orderItemList = orderParam.getOrderItemList();
                if (orderItemList != null) {
                    orderItemList.addAll(stickerItems);
                    // 在更新参数中的订单明细列表后调用handleOrderSku
                    OrderUtil.handleOrderSku(orderItemList, updateOcBOrder);
                    //过滤掉未拆分组合商品
                    orderItemList = orderItemList.stream().filter(s ->
                            !String.valueOf(s.getProType()).equals(String.valueOf(SkuType.NO_SPLIT_COMBINE))).collect(Collectors.toList());
                    updateOcBOrder.setSkuKindQty(BigDecimal.valueOf(orderItemList.size()));
                    BigDecimal weight = BigDecimal.ZERO;
                    for (OcBOrderItem item : orderItemList) {
                        BigDecimal standardWeight = Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO);
                        BigDecimal qty = Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO);
                        weight = weight.add(standardWeight.multiply(qty));
                    }
                    updateOcBOrder.setWeight(weight);
                }
                // 更新订单
                ocBOrderMapper.updateById(updateOcBOrder);
                // 保存操作日志
                String logContent = "添加贴纸商品成功，策略名称[" + sticker.getName() + "]";
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.GIFT_ADD.getKey(), logContent, "", "", user);
                log.info(LogUtil.format("贴纸策略执行成功,orderId:{}", "贴纸策略执行成功", ocBOrder.getId()));
            } else {
                log.warn(LogUtil.format("贴纸库存占用失败,orderId:{}", "贴纸库存占用失败", ocBOrder.getId()));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("贴纸方案异常,orderId:{}", "贴纸方案异常", ocBOrder.getId()), e);
        }
    }

    /**
     * 根据店铺获取贴纸策略（使用缓存）
     *
     * @param shopId 店铺ID
     * @return 贴纸策略列表
     */
    private List<StCStickerRelation> getStickerRelationsByShopId(Long shopId) {
        if (shopId == null) {
            return Collections.emptyList();
        }

        String cacheKey = StRedisKeyResources.buildStickerCacheKey(shopId);
        String cacheValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(cacheKey);

        List<StCStickerRelation> result;
        if (StringUtils.isNotEmpty(cacheValue)) {
            // 从缓存中获取
            result = JSON.parseArray(cacheValue, StCStickerRelation.class);
        } else {
            // 从数据库中获取
            result = new ArrayList<>();
            // 查询已审核且结束时间在当前时间之后的贴纸策略
            List<StCSticker> stickers = stCStickerMapper.selectStCStickerByStatus(new Date(), shopId);
            if (CollectionUtils.isNotEmpty(stickers)) {
                for (StCSticker sticker : stickers) {
                    // 查询贴纸策略明细
                    List<StCStickerItem> items = stCStickerItemMapper.selectList(
                            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<StCStickerItem>()
                                    .eq(StCStickerItem::getStCStickerId, sticker.getId())
                    );
                    if (CollectionUtils.isNotEmpty(items)) {
                        result.add(new StCStickerRelation(sticker, items));
                    }
                }
            }
            // 写入缓存，即使没有数据也写入缓存，避免空空查询
            RedisOpsUtil.getStrRedisTemplate().opsForValue().set(
                    cacheKey,
                    JSON.toJSONString(result),
                    STICKER_CACHE_EXPIRE_MINUTES,
                    TimeUnit.MINUTES
            );
        }
        return result;
    }

    /**
     * 查找匹配的贴纸策略明细
     *
     * @param ocBOrder         订单
     * @param selectedRelation 选中的贴纸策略关系
     * @return 匹配的贴纸策略明细，如果没有匹配到则返回null
     */
    private StCStickerItem findMatchedStickerItem(OcBOrder ocBOrder, StCStickerRelation selectedRelation) {
        // 查询所有具有相同tid的订单明细
        List<OcBOrderItem> allOrderItems = ocBOrderItemMapper.selectList(
                new LambdaQueryWrapper<OcBOrderItem>()
                        .eq(OcBOrderItem::getTid, ocBOrder.getTid())
                        .eq(OcBOrderItem::getIsactive, "Y")
                        .ne(OcBOrderItem::getRefundStatus, 6)
        );
        if (CollectionUtils.isEmpty(allOrderItems)) {
            log.info(LogUtil.format("OmsStickerService.handelStickerService 订单明细为空 tid:{}",
                    "OmsStickerService.handelStickerService"), ocBOrder.getId());
            return null;
        }
        // k. 判断是否存在主播ID
        boolean hasAnchorId = false;
        String anchorId = null;
        // 收集当前订单的明细
        List<OcBOrderItem> orderItems = new ArrayList<>();
        // 收集当前订单已存在的SKU ID
        Set<Long> skuIds = new HashSet<>();
        // 遍历所有订单明细，查找主播ID并收集当前订单的明细
        for (OcBOrderItem item : allOrderItems) {
            // 收集当前订单的明细
            if (ocBOrder.getId().equals(item.getOcBOrderId())) {
                orderItems.add(item);
                // 收集已存在的SKU ID
                if (item.getPsCSkuId() != null) {
                    skuIds.add(item.getPsCSkuId());
                }
            }
            if (StringUtils.isNotEmpty(item.getAnchorId())) {
                if (hasAnchorId && !item.getAnchorId().equals(anchorId)) {
                    // l. 存在大于一个主播id，打印系统日志return
                    log.warn(LogUtil.format("OmsStickerService.handelStickerService 订单存在多个不同的主播ID，不执行贴纸策略 tid:{}",
                            "OmsStickerService.handelStickerService"), ocBOrder.getId());
                    return null;
                }
                hasAnchorId = true;
                anchorId = item.getAnchorId();
            }
        }
        // 检查当前订单明细是否为空
        if (CollectionUtils.isEmpty(orderItems)) {
            log.info(LogUtil.format("OmsStickerService.handelStickerService 当前订单明细为空 orderId:{}",
                    "OmsStickerService.handelStickerService"), ocBOrder.getId());
            return null;
        }

        // 过滤贴纸策略明细，过滤掉当前订单已经存在的SKU
        List<StCStickerItem> filteredStickerItems = selectedRelation.getStCStickerItems();
        if (CollectionUtils.isNotEmpty(filteredStickerItems)) {
            // 过滤掉当前订单已经存在的SKU
            filteredStickerItems = filteredStickerItems.stream()
                    .filter(item -> !skuIds.contains(item.getPsCSkuId()))
                    .collect(Collectors.toList());
            // 更新贴纸策略关系中的明细
            selectedRelation.setStCStickerItems(filteredStickerItems);
            // 如果过滤后没有可用的贴纸策略明细，返回null
            if (CollectionUtils.isEmpty(filteredStickerItems)) {
                log.info(LogUtil.format("OmsStickerService.handelStickerService 根据原单sku过滤后没有可用的贴纸策略明细 orderId:{}",
                        "OmsStickerService.handelStickerService"), ocBOrder.getId());
                return null;
            }
        }

        // m. 判断是否存在主播id，进行不同的匹配逻辑
        if (hasAnchorId) {
            return findMatchedStickerItemWithAnchor(selectedRelation, orderItems);
        } else {
            return findMatchedStickerItemWithoutAnchor(selectedRelation, orderItems);
        }
    }

    /**
     * 存在主播ID时查找匹配的贴纸策略明细
     *
     * @param relation   贴纸策略关系
     * @param orderItems 订单明细
     * @return 匹配的贴纸策略明细，如果没有匹配到则返回null
     */
    private StCStickerItem findMatchedStickerItemWithAnchor(StCStickerRelation relation, List<OcBOrderItem> orderItems) {
        List<StCStickerItem> stickerItems = relation.getStCStickerItems();
        if (CollectionUtils.isEmpty(stickerItems)) {
            return null;
        }
        // 过滤出当前订单存在主播ID的行
        List<OcBOrderItem> anchorOrderItems = orderItems.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getAnchorId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(anchorOrderItems)) {
            return null;
        }

        // 1. 先匹配指定主播ID+SKU的策略明细
        List<StCStickerItem> anchorSkuItems = stickerItems.stream()
                .filter(item -> StickerAppointDimensionEnum.ANCHOR_SKU.getCode().equals(item.getAppointDimension()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(anchorSkuItems)) {
            // 将订单明细按照主播ID+SKU分组
            Map<String, List<OcBOrderItem>> anchorSkuGroupMap = anchorOrderItems.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getPsCSkuEcode()))
                    .collect(Collectors.groupingBy(item -> item.getAnchorId() + "+" + item.getPsCSkuEcode()));

            // 将贴纸策略明细转成Map<匹配内容,StCStickerItem>
            Map<String, StCStickerItem> anchorSkuItemMap = anchorSkuItems.stream()
                    .collect(Collectors.toMap(StCStickerItem::getMatchContent, item -> item, (v1, v2) -> v1));

            // 根据分组好的订单明细按组匹配
            for (Map.Entry<String, List<OcBOrderItem>> entry : anchorSkuGroupMap.entrySet()) {
                String key = entry.getKey();
                if (anchorSkuItemMap.containsKey(key)) {
                    return anchorSkuItemMap.get(key);
                }
            }
        }

        // 2. 如果没有匹配到，则匹配指定主播ID+四级类目的策略明细
        List<StCStickerItem> anchorCategoryItems = stickerItems.stream()
                .filter(item -> StickerAppointDimensionEnum.ANCHOR_CATEGORY.getCode().equals(item.getAppointDimension()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(anchorCategoryItems)) {
            // 将订单明细按照主播ID+四级类目分组
            Map<String, List<OcBOrderItem>> anchorCategoryGroupMap = anchorOrderItems.stream()
                    .filter(item -> item.getMDim6Id() != null)
                    .collect(Collectors.groupingBy(item -> item.getAnchorId() + "+" + item.getMDim6Id().toString()));

            // 将贴纸策略明细转成Map<匹配内容,StCStickerItem>
            Map<String, StCStickerItem> anchorCategoryItemMap = anchorCategoryItems.stream()
                    .collect(Collectors.toMap(StCStickerItem::getMatchContent, item -> item, (v1, v2) -> v1));

            // 根据分组好的订单明细按组匹配
            for (Map.Entry<String, List<OcBOrderItem>> entry : anchorCategoryGroupMap.entrySet()) {
                String key = entry.getKey();
                if (anchorCategoryItemMap.containsKey(key)) {
                    return anchorCategoryItemMap.get(key);
                }
            }
        }
        return null;
    }

    /**
     * 不存在主播ID时查找匹配的贴纸策略明细
     *
     * @param relation   贴纸策略关系
     * @param orderItems 订单明细
     * @return 匹配的贴纸策略明细，如果没有匹配到则返回null
     */
    private StCStickerItem findMatchedStickerItemWithoutAnchor(StCStickerRelation relation, List<OcBOrderItem> orderItems) {
        List<StCStickerItem> stickerItems = relation.getStCStickerItems();
        if (CollectionUtils.isEmpty(stickerItems)) {
            return null;
        }

        // 1. 先匹配无主播ID+SKU的策略明细
        List<StCStickerItem> noAnchorSkuItems = stickerItems.stream()
                .filter(item -> StickerAppointDimensionEnum.NO_ANCHOR_SKU.getCode().equals(item.getAppointDimension()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noAnchorSkuItems)) {
            // 将订单明细按照SKU分组
            Map<String, List<OcBOrderItem>> skuGroupMap = orderItems.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getPsCSkuEcode()))
                    .collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuEcode));
            // 将贴纸策略明细转成Map<匹配内容,StCStickerItem>
            Map<String, StCStickerItem> noAnchorSkuItemMap = noAnchorSkuItems.stream()
                    .collect(Collectors.toMap(StCStickerItem::getMatchContent,
                            item -> item, (v1, v2) -> v1));
            // 根据分组好的订单明细按组匹配
            for (Map.Entry<String, List<OcBOrderItem>> entry : skuGroupMap.entrySet()) {
                String key = entry.getKey();
                if (noAnchorSkuItemMap.containsKey(key)) {
                    return noAnchorSkuItemMap.get(key);
                }
            }
        }

        // 2. 如果没有匹配到，则匹配无主播ID+四级类目的策略明细
        List<StCStickerItem> noAnchorCategoryItems = stickerItems.stream()
                .filter(item -> StickerAppointDimensionEnum.NO_ANCHOR_CATEGORY.getCode().equals(item.getAppointDimension()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noAnchorCategoryItems)) {
            // 将订单明细按照四级类目分组
            Map<String, List<OcBOrderItem>> categoryGroupMap = orderItems.stream()
                    .filter(item -> item.getMDim6Id() != null)
                    .collect(Collectors.groupingBy(item -> item.getMDim6Id().toString()));
            // 将贴纸策略明细转成Map<匹配内容,StCStickerItem>
            Map<String, StCStickerItem> noAnchorCategoryItemMap = noAnchorCategoryItems.stream()
                    .collect(Collectors.toMap(StCStickerItem::getMatchContent,
                            item -> item, (v1, v2) -> v1));
            // 根据分组好的订单明细按组匹配
            for (Map.Entry<String, List<OcBOrderItem>> entry : categoryGroupMap.entrySet()) {
                String key = entry.getKey();
                if (noAnchorCategoryItemMap.containsKey(key)) {
                    return noAnchorCategoryItemMap.get(key);
                }
            }
        }
        return null;
    }

    private OcBOrderItem buildOcBOrderItem(StCStickerItem stCStickerItem, OcBOrder ocBOrder) {
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);
        //活动编号. 默认赋值为null
        item.setActiveId(null);
        BaseModelUtil.initialBaseModelSystemField(item);
        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //tid
        item.setTid(ocBOrder.getTid());
        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        item.setIsGift(1);
        // 设置赠品类型为系统赠品
        item.setGiftType(GiftTypeEnum.SYSTEM.getVal());
        // 设置贴纸赠品标识
        item.setStickerGift(YesNoEnum.Y.getVal());
        //实缺标记
        item.setIsLackstock(0);
        //商品数字编号
        //订单编号
        item.setOcBOrderId(ocBOrder.getId());
        //商品路径
        item.setQtyRefund(BigDecimal.ZERO);
        //退款状态
        // 如果是退款完成，或者是交易关闭 状态=6
        item.setRefundStatus(0);
        item.setQty(stCStickerItem.getQty());
        ProductSku productSku = psRpcService.selectProductSku(stCStickerItem.getPsCSkuEcode());
        this.initialTaobaoOrderItem(productSku, item);
        item.setRealAmt(BigDecimal.ZERO);
        item.setOrderSplitAmt(BigDecimal.ZERO);
        item.setAmtDiscount(BigDecimal.ZERO);
        item.setPriceActual(BigDecimal.ZERO);
        item.setAdjustAmt(BigDecimal.ZERO);
        item.setPrice(BigDecimal.ZERO);
        //类型
        item.setProType(0L);
        // 虚拟条码
        item.setCanSplit(productSku.getCanSplit());
        item.setRealOutNum(BigDecimal.ZERO);
        item.setIsPresalesku(0);
        item.setIsSendout(0);
        item.setOuterrcount(0);
        item.setQtyReturnApply(BigDecimal.ZERO);
        item.setPresellType(0);
        // 小米有品
        return item;
    }


    private void initialTaobaoOrderItem(ProductSku productSku, OcBOrderItem item) {
        if (productSku != null) {
            item.setPsCProId(productSku.getProdId());
            // ProECode
            item.setSex(productSku.getSex());
            //2019-08-30吊牌价改为取商品表数据 //吊牌价
            item.setPriceTag(productSku.getPricelist());
            item.setPsCProEcode(productSku.getProdCode());
            item.setPsCProEname(productSku.getName());
            item.setPsCSkuId(productSku.getId());
            // 2019-06-16 易邵峰修改：增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
            String psSkuEcode = productSku.getSkuEcode();
            item.setPsCSkuEcode(psSkuEcode);
            item.setPsCClrEcode(productSku.getColorCode());
            item.setPsCClrEname(productSku.getColorName());
            item.setPsCClrId(productSku.getColorId());
            item.setStandardWeight(productSku.getWeight());
            item.setSkuSpec(productSku.getSkuSpec());
            item.setPsCSizeEcode(productSku.getSizeCode());
            item.setPsCSizeEname(productSku.getSizeName());
            item.setPsCSizeId(productSku.getSizeId());
            item.setPsCProMaterieltype(productSku.getMaterialType());
            item.setBarcode(productSku.getBarcode69());
            //标准价。
            item.setPriceList(productSku.getPricelist());
            item.setPriceTag(productSku.getPricelist());
            item.setMDim4Id(productSku.getMDim4Id());
            item.setMDim6Id(productSku.getMDim6Id());
            String isEnableExpiry = productSku.getIsEnableExpiry();
            if ("Y".equals(isEnableExpiry)) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }

}
