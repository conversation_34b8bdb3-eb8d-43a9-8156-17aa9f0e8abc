package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderHoldItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderHoldConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.request.StCHoldOrderRequest;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.util.DateConversionUtil;
import com.jackrain.nea.oc.oms.util.FormatDateUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitOrderUtils;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.service.StCHoldOrderReasonQueryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: 江家雷
 * @since: 2020-07-03
 * create at:  2020-07-03 19:12
 */
@Slf4j
@Component
public class OcBOrderHoldItemService {

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;

    private final String TAB_OC_B_ORDER_HOLD_ITEM = "oc_b_order_hold_item";

    @Autowired
    private OcBOrderHoldItemMapper ocBOrderHoldItemMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private OmsOrderLogService orderLogService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsAuditTaskService omsAuditTaskService;

    @Autowired
    private PsRpcService psRpcService;


    @Autowired
    private SplitOrderUtils splitOrderUtils;

    @Autowired
    private OmsWmsTaskService wmsTaskService;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    @Autowired
    private StCHoldOrderReasonQueryService holdOrderReasonQueryService;
    /**
     * 按条件查询HOLD单明细
     *
     * @param orderId
     * @param orderReasons
     * @return
     */
    public List<OcBOrderHoldItem> selectOrderHoldItemsByHoldReasons(@Param("orderId") Long orderId,
                                                                    @Param("orderReasons") List<Integer> orderReasons) {
        return ocBOrderHoldItemMapper.selectOrderHoldItemsByHoldReasons(orderId, orderReasons);
    }

    /**
     * 保存HOLD单明细
     *
     * @param itemList
     * @return
     */
    public void saveOrderHoldItemList(List<OcBOrderHoldItem> itemList) {
        for (OcBOrderHoldItem item : itemList) {
            makeModifierField(item, SystemUserResource.getRootUser());
            item.setId(ModelUtil.getSequence(TAB_OC_B_ORDER_HOLD_ITEM));
            ocBOrderHoldItemMapper.insert(item);
        }
    }

    /***
     * 通过TASK自动释放HOLD单
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoCancelHoldOrder(Long orderId, List<OcBOrderHoldItem> itemList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderHoldItemService_autoCancelHoldOrder 自动取消HOLD单 start orderId=", orderId));
        }
        //给订单加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = this.ocBOrderMapper.selectByID(orderId);
                // 取消、作废状态的订单，直接取消HOLD单。不再进行释放时间计算
                if (voidOrderHoldMark(orderId, ocBOrder)) {
                    return;
                }
                if (CollectionUtils.isEmpty(itemList)) {
                    // 更新主表信息
                    ocBOrderMapper.updateOcBOrderReleaseTimeEmpty(OcBOrderHoldConst.HOLD_ORDER_NO, new Date(), orderId,"");
                    return;
                }
                Date now = new Date();
                Date releaseTime = new Date();
                int count = 0;
                Iterator<OcBOrderHoldItem> iterator = itemList.iterator();
                while (iterator.hasNext()) {
                    OcBOrderHoldItem item = iterator.next();
                    if (log.isDebugEnabled()) {
                        log.debug("autoCancelHoldOrder需要更新时间的ITEM={}", JSON.toJSONString(item));
                    }
                    // 释放时间为null  代表不自动释放
                    if (item.getReleaseTime() == null) {
                        releaseTime = null;
                        count++;
                        continue;
                    }
                    // 释放时间小于等于当前时间的HOLD明细状态置为失效
                    if (now.after(item.getReleaseTime())) {
                        ocBOrderHoldItemMapper.delByOcBOrderIdAndId(item.getOcBOrderId(), item.getId());
                        //iterator.remove();
                    } else {
                        if (releaseTime == null) {
                            count++;
                        } else {
                            // 重新计算释放时间
                            releaseTime = getReleaseTime(releaseTime, item);
                            count++;
                        }
                    }
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("autoCancelHoldOrder是否有hold单明细={},orderId =", orderId), count);
                }
                // 无有效的hold单明细，将订单改为非hold单状态，订单释放时间改为当前时间
                // 若有hold单明细，则更新主表释放时间
                if (count == 0) {
                    OcBOrder ocBOrderDto = new OcBOrder();
                    ocBOrderDto.setHoldReleaseTime(new Date());
                    ocBOrderDto.setIsInterecept(OcBOrderHoldConst.HOLD_ORDER_NO);
                    ocBOrderDto.setHoldReason("");
                    ocBOrderMapper.update(ocBOrderDto, Wrappers.<OcBOrder>lambdaUpdate()
                            .set(OcBOrder::getHoldReasonId, null)
                            .eq(OcBOrder::getId, orderId));

                    //调用添加订单日志
                    String message = "";
                    if (CollectionUtils.isNotEmpty(itemList)) {
                        message =
                                itemList.stream().map(OcBOrderHoldItem::getHoldOrderReasonMsg).collect(Collectors.joining(","));
                    }
                    insertOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.ORDER_HOLD_CANCEL.getKey(), "自动释放HOLD单,HOLD单原因：" + message, null, null,
                            SystemUserResource.getRootUser());
                } else {
                    if (CollectionUtils.isNotEmpty(itemList)) {
                        this.ocBOrderMapper.updateHoldReleaseTime(orderId, releaseTime);
                    }
                }
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!"));
            }
        } catch (Exception e) {
            throw new NDSException(e);
        } finally {
            redisLock.unlock();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelHoldJitxOrder(Long orderId,String message) {
        if (log.isDebugEnabled()) {
            log.debug("OcBOrderHoldItemService_cancelHoldOrder 取消HOLD单 start orderId={}", orderId);
        }
        List<OcBOrderHoldItem> items = this.ocBOrderHoldItemMapper.selectOrderHoldItemListByOrderId(orderId);

        if (orderId == null || CollectionUtils.isEmpty(items)) {
            return;
        }
        Wrapper wrapper = new QueryWrapper<OcBOrderItem>()
                .eq("oc_b_order_id", orderId)
                .eq("hold_order_reason", items.get(0).getHoldOrderReason());
        OcBOrderHoldItem updateItem = new OcBOrderHoldItem();
        updateItem.setHoldStatus(OcBOrderHoldConst.NO);
        makeModifierField(updateItem, SystemUserResource.getRootUser());
        ocBOrderHoldItemMapper.update(updateItem, wrapper);

        insertOrderLog(orderId, "",
                OrderLogTypeEnum.ORDER_JITX_HOLD.getKey(), "自动释放HOLD单,HOLD单原因:" + message, null, null,
                SystemUserResource.getRootUser());

    }
    /***
     * 作废、取消状态的订单直接 取消HOLD单
     * @param orderId
     * @param ocBOrder
     * @return
     */
    private boolean voidOrderHoldMark(Long orderId, OcBOrder ocBOrder) {
        if (Objects.isNull(orderId)) {
            return false;
        }
        try {
            if (ocBOrder == null) { // 测试环境，订单主表数据被删除，导致HOLD单表中有很多无效的明细
                ocBOrderHoldItemMapper.delByOcBOrderId(orderId);
                return true;
            }
            if (OmsOrderStatus.SYS_VOID.toInteger().equals(ocBOrder.getOrderStatus())
                    || OmsOrderStatus.CANCELLED.toInteger().equals(ocBOrder.getOrderStatus())) {
                if (Objects.nonNull(orderId)) {
                    ocBOrderHoldItemMapper.delByOcBOrderId(orderId);
                }
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setId(orderId);
                updateOrder.setIsInterecept(OcBOrderHoldConst.HOLD_ORDER_NO);
                updateOrder.setHoldReason(StringUtils.EMPTY);
                ocBOrderMapper.update(updateOrder, Wrappers.<OcBOrder>lambdaUpdate()
                        .set(OcBOrder::getHoldReasonId, null)
                        .eq(OcBOrder::getId, orderId));

                insertOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_HOLD_CANCEL.getKey(),
                        "自动释放HOLD单,订单为取消或作废状态", null, null, SystemUserResource.getRootUser());
                return true;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("作废、取消状态的订单直接 取消HOLD单失败,error：{}"), Throwables.getStackTraceAsString(e));
            return true;
        }

        return false;
    }

    /**
     * 根据HOLD单策略 在占单过程中自动HOLD单
     *
     * @param orderId
     * @param operateUser
     */
    /***
     * hold单操作
     * @param orderInfo
     * @param operateUser
     */
    public void autoHoldOrder(OcBOrderRelation orderInfo, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderHoldItemService_autoHoldOrder 自动HOLD单 start orderInfo={}"),
                    JSON.toJSONString(orderInfo));
        }
        // 校验订单状态能否hold单
        checkHoldOrder(orderInfo.getOrderInfo());
        List<StCHoldOrderRequest> filterList = getStCHoldOrderStrategyList(orderInfo);
        if (CollectionUtils.isEmpty(filterList)) {
            log.info(LogUtil.format("在当前时间没有符合的HOLD单策略,店铺/orderId=",
                    orderInfo.getOrderInfo().getCpCShopTitle(), orderInfo.getOrderInfo().getId()));
            return;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("当前符合的HOLD单策略 data={}"),
                    JSON.toJSONString(filterList));
        }
        ValueHolder vh = saveOrUpdateItems(orderInfo.getOrderInfo(), filterList, operateUser, true);
        if (vh != null && vh.isOK()) {
            updateOcBOrder(orderInfo.getOrderInfo().getId());
            log.info(LogUtil.format("满足hold修改打上hold标,orderId=", orderInfo.getOrderInfo().getId()));
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderInfo.getOrderInfo().getOrderStatus())) {
                    // 插入订单审核
                    omsAuditTaskService.createOcBAuditTask(orderInfo.getOrderInfo(), OmsAuditTimeCalculateReason.HOLD);
                }
            }
    }

    private void updateOcBOrder(Long ocBOrderId) {
        List<Integer> params = new ArrayList<>();
        params.add(OrderHoldReasonEnum.LIVECAST_HOLD.getKey());
        params.add(OrderHoldReasonEnum.BUYER_HOLD.getKey());
        List<OcBOrderHoldItem> items =
                ocBOrderHoldItemMapper.selectOrderHoldItemsByHoldReasons(ocBOrderId, params);
        Date releaseTime = null;
        for (OcBOrderHoldItem item : items) {
            if (item.getReleaseTime() == null) {
                releaseTime = null;
                break;
            }
            if (releaseTime == null) {
                releaseTime = item.getReleaseTime();
            } else {
                if (releaseTime.before(item.getReleaseTime())) {
                    releaseTime = item.getReleaseTime();
                }
            }
        }
        // 更新主表信息
        ocBOrderMapper.updateOcBOrderReleaseTime(OcBOrderHoldConst.HOLD_ORDER_YES, releaseTime, ocBOrderId);
    }

    /***
     * 过滤出有效的HOLD单策略
     * @param orderInfo
     * @return
     */
    private List<StCHoldOrderRequest> getStCHoldOrderStrategyList(OcBOrderRelation orderInfo) {
        // 1.查询hold单策略
        List<StCHoldOrderRequest> stCHoldOrderRequestList =
                stRpcService.queryStCHoldOrderByShopId(orderInfo.getOrderInfo().getCpCShopId());
        if (CollectionUtils.isEmpty(stCHoldOrderRequestList)) {
            log.info(LogUtil.format("没有配置HOLD单策略,店铺/orderId=",
                    orderInfo.getOrderInfo().getCpCShopTitle(), orderInfo.getOrderInfo().getId()));
            return Lists.newArrayList();
        }
        Date createTime = orderInfo.getOrderInfo().getOrderDate();
        Date payTime = orderInfo.getOrderInfo().getPayTime();
        // 2.根据hold单策略 生成hold单明细，订单主表增加释放时间，修改is_interecept字段标识
        // 筛选出有效的策略（时间类型 下单时间 判断下单是否在方案生效时间内， 时间类型：支付时间 判断支付时间是否在方案生效时间内）
        List<StCHoldOrderRequest> filterList = stCHoldOrderRequestList.stream()
                .filter(o -> (OcBOrderHoldConst.ORDER_DATE.equals(o.getDayType()) && createTime.before(o.getEndTime()) && createTime.after(o.getBeginTime()))
                        || (OcBOrderHoldConst.PAY_TIME.equals(o.getDayType()) && payTime.before(o.getEndTime()) && payTime.after(o.getBeginTime())))
                .collect(Collectors.toList());
        return filterList;
    }

    /***
     *  手工HOLD单
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder manualHoldOrder(Long orderId, List<StCHoldOrderRequest> stCHoldOrderList, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderHoldItemService_manualHoldOrder 手工HOLD单 start orderId=", orderId));
        }
        ValueHolder vh = new ValueHolder();
        // 给订单加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
                // 校验订单状态能否hold单
                checkHoldOrder(ocBOrder);
                // 调用反审核
                ValueHolderV14 vh14 = new ValueHolderV14();
                if (ocBOrder.getOrderStatus().equals(OmsOrderStatus.CHECKED.toInteger())
                        || ocBOrder.getOrderStatus().equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger())) {
                    ocBOrderTheAuditService.updateOrderInfo(operateUser, vh14, ocBOrder.getId(), LogTypeEnum.MANUAL_HOLD_ORDER_REVERSE_AUDIT.getType());
                    if (!vh14.isOK()) {
                        throw new NDSException("手动HOLD单失败，调用反审核失败");
                    }
                    ocBOrder.setHoldReason("手动HOLD单");
                    if (CollectionUtils.isNotEmpty(stCHoldOrderList)) {
                        StCHoldOrderRequest stCHoldOrderRequest = stCHoldOrderList.get(0);
                        ocBOrder.setHoldReasonId(stCHoldOrderRequest.getHoldDetentionOrderReason());
                    }
                    ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                }
                ValueHolder vhResult = saveOrUpdateItems(ocBOrder, stCHoldOrderList, operateUser, false);
                if (vhResult.isOK()) {
                    updateOcBOrder(orderId);
                    if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {

                        log.info(LogUtil.format("进行订单拆单"));
                        // 插入订单审核
                        omsAuditTaskService.createOcBAuditTask(ocBOrder, OmsAuditTimeCalculateReason.HOLD);
                    }
                } else {
                    throw new NDSException("手动HOLD单失败");
                }

            } else {
                throw new NDSException("手动HOLD单失败，当前订单其他人在操作，请稍后再试!");
            }
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("订单HOLD单成功", operateUser.getLocale()));
        } catch (Exception e) {
            log.error(LogUtil.format("手动HOLD单失败,异常信息={},OrderId=", orderId), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        } finally {
            redisLock.unlock();
        }
        return vh;
    }


    /***
     *  手工 释放订单HOLD单
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder cancelHoldOrder(Long orderId, User operateUser, String holdReleaseReason) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderHoldItemService_cancelHoldOrder 手工取消HOLD单 start orderId=", orderId));
        }
        ValueHolder vh = new ValueHolder();
        // 给订单加锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = this.ocBOrderMapper.selectByID(orderId);
                if (ocBOrder == null) {
                    throw new NDSException("订单不存在");
                }
                if (!OmsOrderIsInterceptEnum.YES.getVal().equals(ocBOrder.getIsInterecept())) {
                    throw new NDSException("订单非HOLD单状态,不允许释放HOLD单");
                }

                List<OcBOrderHoldItem> list = this.ocBOrderHoldItemMapper.selectOrderHoldItemListByOrderId(orderId);
                // 查出不能手工释放的明细 （换货HOLD  退款HOLD）
                List<OcBOrderHoldItem> unHoldList = list.stream()
                        .filter(item -> OrderHoldReasonEnum.REFUND_HOLD.getKey().equals(item.getHoldOrderReason())
                                || OrderHoldReasonEnum.ADD_EXCHANGE_ORDER.getKey().equals(item.getHoldOrderReason()))
                        .collect(Collectors.toList());
                // 能够手工释放HOLD单的明细
                List<Long> ids = list.stream()
                        .filter(item -> !OrderHoldReasonEnum.REFUND_HOLD.getKey().equals(item.getHoldOrderReason())
                                && !OrderHoldReasonEnum.ADD_EXCHANGE_ORDER.getKey().equals(item.getHoldOrderReason()))
                        .map(OcBOrderHoldItem ::getId)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(list)) {
                    // 更新主表信息
                    OcBOrder ocBOrderDto = new OcBOrder();
                    ocBOrderDto.setId(orderId);
                    ocBOrderDto.setHoldReleaseTime(new Date());
                    ocBOrderDto.setIsInterecept(OcBOrderHoldConst.HOLD_ORDER_NO);
                    ocBOrderDto.setModifierename(operateUser.getName());
                    //hold单释放最后一次埋点
                    ocBOrderDto.setHoldReleaseDate(new Date());
                    ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.HOLD_RELEASE_DATE,new Date(),ocBOrderDto.getId(),operateUser);
                    omsOrderService.updateOrderInfo(ocBOrderDto);
                    vh.put("code", ResultCode.SUCCESS);
                    vh.put("message", "取消HOLD单成功");
                    insertOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_HOLD_CANCEL.getKey(),
                            "手工取消HOLD单成功！", null, null, operateUser);
                    if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {

                        log.info(LogUtil.format("进行订单拆单"));
                        // 当开关开启走仓库拆单 （插入拆单task表），不开启 则不走仓库拆单*（插入传订单审核）
                        if (splitOrderUtils.isOpenWareHouseSplitOrder(ocBOrder)) {
                            // 插入仓库拆单任务表
                            wmsTaskService.saveOrUpdateOcBWarehouseSplitTask(ocBOrder.getId(), operateUser);
                        } else {
                            // 插入传wms表
                            omsAuditTaskService.createOcBAuditTask(ocBOrder,OmsAuditTimeCalculateReason.HOLD);
                        }
                    }
                    return vh;
                }
                if (CollectionUtils.isNotEmpty(ids)) {
                    ocBOrderHoldItemMapper.updateOrderHoldItemByOrderId(orderId, ids);
                    if (CollectionUtils.isEmpty(unHoldList)) {
                        // 更新主表信息
                        OcBOrder ocBOrderDto = new OcBOrder();
                        ocBOrderDto.setId(orderId);
                        ocBOrderDto.setHoldReleaseTime(new Date());
                        ocBOrderDto.setIsInterecept(OcBOrderHoldConst.HOLD_ORDER_NO);
                        ocBOrderDto.setModifierename(operateUser.getName());
                        ocBOrderDto.setHoldReason("");
                        //hold单释放最后一次埋点
                        ocBOrderDto.setHoldReleaseDate(new Date());
                        ocBOrderDto.setHoldReleaseReason(holdReleaseReason);
                        ocBOrderDto.setHoldReleaseName(operateUser.getName());

                        ocBOrderMapper.update(ocBOrderDto, Wrappers.<OcBOrder>lambdaUpdate()
                                .set(OcBOrder::getHoldReasonId, null)
                                .eq(OcBOrder::getId, orderId));
                        ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.HOLD_RELEASE_DATE,new Date(),orderId,operateUser);
                        vh.put("code", ResultCode.SUCCESS);
                        vh.put("message", "取消HOLD单成功");
                        String msg = list.stream()
                                .filter(item -> item.getHoldOrderReason() != null && (item.getHoldOrderReason().intValue() == 1 || item.getHoldOrderReason().intValue() == 2))
                                .map(item -> item.getHoldOrderReasonMsg())
                                .collect(Collectors.joining(","));
                        insertOrderLog(orderId, ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_HOLD_CANCEL.getKey(),
                                "取消HOLD单成功！取消HOLD单原因：" + holdReleaseReason, null, null, operateUser);
                    } else {
                        String msg =
                                unHoldList.stream().map(item -> item.getHoldOrderReasonMsg()).collect(Collectors.joining(","));
                        ocBOrderMapper.updateHoldReleaseTime(orderId, null);
                        throw new NDSException("取消HOLD单失败，取消失败原因：" + holdReleaseReason);
                    }
                    if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {

                        log.info("进行订单拆单");
                        // 当开关开启走仓库拆单 （插入拆单task表），不开启 则不走仓库拆单*（插入传订单审核）
                        if (splitOrderUtils.isOpenWareHouseSplitOrder(ocBOrder)) {
                            // 插入仓库拆单任务表
                            wmsTaskService.saveOrUpdateOcBWarehouseSplitTask(ocBOrder.getId(), operateUser);
                        } else {
                            // 插入传wms表
                            omsAuditTaskService.createOcBAuditTask(ocBOrder,OmsAuditTimeCalculateReason.HOLD);
                        }
                    }
                } else {
                    throw new NDSException("取消HOLD单失败，当前HOLD单不允许手工取消");
                }
            } else {
                throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", operateUser.getLocale()));
            }
        } catch (Exception e) {
            log.info(LogUtil.format("取消HOLD单失,异常信息={},OrderId=", orderId), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        } finally {
            redisLock.unlock();
        }
        return vh;
    }
    /***
     * 根据业务场景HOLD单
     * @param orderId
     */
    public void businessHold(Long orderId, OrderHoldReasonEnum holdReason) {
        this.businessHold(orderId, holdReason, null, null);
    }

    public void businessHold(Long orderId, OrderHoldReasonEnum holdReason, Date releaseTime, String logMessage) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderHoldItemService.businessHold start orderId={},hold单场景=", orderId),
                    holdReason.getMessage());
        }
        List<OcBOrderHoldItem> items = ocBOrderHoldItemMapper.selectOrderHoldItemByOrderIdAndHoldReasonList(orderId,
                holdReason.getKey());
        if (CollectionUtils.isNotEmpty(items)) {
            OcBOrderHoldItem holdItem = items.get(0);
            Wrapper wrapper = new QueryWrapper<OcBOrderItem>()
                    .eq("oc_b_order_id", holdItem.getOcBOrderId())
                    .eq("hold_order_reason", holdItem.getHoldOrderReason());
            OcBOrderHoldItem updateItem = new OcBOrderHoldItem();
            updateItem.setHoldStatus(OcBOrderHoldConst.YES);
            updateItem.setReleaseTime(releaseTime);
            makeModifierField(updateItem, SystemUserResource.getRootUser());
            ocBOrderHoldItemMapper.update(updateItem, wrapper);
        } else {
            OcBOrderHoldItem item = new OcBOrderHoldItem();
            item.setId(ModelUtil.getSequence(TAB_OC_B_ORDER_HOLD_ITEM));
            item.setOcBOrderId(orderId);
            item.setHoldStatus(OcBOrderHoldConst.YES);
            item.setHoldOrderReason(holdReason.getKey());
            item.setHoldOrderReasonMsg(holdReason.getMessage());
            item.setReleaseTime(releaseTime);
            makeCreateField(item, SystemUserResource.getRootUser());
            ocBOrderHoldItemMapper.insert(item);
        }
        // 更新主表信息
        OcBOrder ocBOrderDto = new OcBOrder();
        ocBOrderDto.setId(orderId);
        ocBOrderDto.setIsInterecept(OcBOrderHoldConst.HOLD_ORDER_YES);
        ocBOrderDto.setHoldReason(holdReason.getMessage());
        ocBOrderDto.setHoldReasonId(Integer.valueOf(holdReason.getKey()));
        ocBOrderDto.setModifierename(SystemUserResource.getRootUser().getEname());
        ocBOrderDto.setModifiername(SystemUserResource.getRootUser().getName());
        ocBOrderDto.setModifierid(Long.valueOf(SystemUserResource.getRootUser().getId()));
        ocBOrderDto.setModifieddate(new Date());
        omsOrderService.updateOrderInfo(ocBOrderDto);

        OcBOrderLog orderLog = orderLogService.selectOcBOrderLogByOrderId(orderId);
        if (orderLog != null
                && OrderLogTypeEnum.ORDER_REFUND_HOLD.getKey().equalsIgnoreCase(orderLog.getLogType())) {
            orderLogService.updateOcBOrderLogModifieddateById(orderId, orderLog.getId());
            return;
        }
        try {
            StringBuilder sb = new StringBuilder();
            String message = "";
            //插入日志
            if (StringUtils.isNotEmpty(logMessage)) {
                message = logMessage;
            } else {
                message = sb.append("系统自动HOLD单，HOLD单原因：").append(holdReason.getMessage()).toString();
            }

            OrderLogTypeEnum orderLogType;
            if (OrderHoldReasonEnum.REFUND_HOLD.equals(holdReason)) {
                orderLogType = OrderLogTypeEnum.ORDER_REFUND_HOLD;
            } else if (OrderHoldReasonEnum.TB_AUDIT_VOLUNTARILY_HOLD.equals(holdReason)) {
                orderLogType = OrderLogTypeEnum.UPDATE_ADDRDERSS_HOLD;
            } else if (OrderHoldReasonEnum.ADD_EXCHANGE_ORDER.equals(holdReason)) {
                orderLogType = OrderLogTypeEnum.EXCHANGE_GOODS_HOLD;
            } else if (OrderHoldReasonEnum.JITX_HOLD.equals(holdReason) || OrderHoldReasonEnum.JITX_FORBIDDEN_DELIVERY.equals(holdReason)) {
                orderLogType = OrderLogTypeEnum.ORDER_JITX_HOLD;
            } else {
                orderLogType = OrderLogTypeEnum.ORDER_HOLD;
            }
            orderLogService.addUserOrderLog(orderId, null, orderLogType.getKey(), message, null, null,
                    SystemUserResource.getRootUser());
        } catch (Exception e) {
            log.error(LogUtil.format("新增订单HOLD单日志失败,Exception = {}，orderId=", orderId),
                    Throwables.getStackTraceAsString(e));
        }

    }

    /***
     * 根据业务场景释放Hold单
     * @param orderId
     */
    public void businessUnHold(Long orderId, OrderHoldReasonEnum holdReason) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderHoldItemService.businessUnHold start ,释放hold单场景={},orderId=", orderId),
                    holdReason.getMessage());
        }
        OrderHoldReasonEnum newHoldReason = holdReason;
        if(OrderHoldReasonEnum.RETURN_AUDIT_COMPLETE.equals(holdReason)
                || OrderHoldReasonEnum.RETURN_VIRTUAL_IN.equals(holdReason)
                || OrderHoldReasonEnum.RETURN_FORCE_COMPLETE.equals(holdReason)){
            newHoldReason = OrderHoldReasonEnum.ADD_EXCHANGE_ORDER;
        }
        final Integer holdReasonKey = newHoldReason.getKey();
        List<OcBOrderHoldItem> items = ocBOrderHoldItemMapper.selectOrderHoldItemListByOrderId(orderId);
        List<OcBOrderHoldItem> item = ocBOrderHoldItemMapper.selectOrderHoldItemByOrderIdAndHoldReasonList(orderId,
                holdReasonKey);
        if (CollectionUtils.isNotEmpty(item)) {
            OcBOrderHoldItem holdItem = item.get(0);
            Wrapper wrapper = new QueryWrapper<OcBOrderItem>()
                    .eq("oc_b_order_id", holdItem.getOcBOrderId())
                    .eq("hold_order_reason", holdItem.getHoldOrderReason());
            OcBOrderHoldItem updateItem = new OcBOrderHoldItem();
            updateItem.setHoldStatus(OcBOrderHoldConst.NO);
            makeModifierField(updateItem, SystemUserResource.getRootUser());
            ocBOrderHoldItemMapper.update(updateItem, wrapper);
        }

        List<OcBOrderHoldItem> results = items.stream()
                .filter(it -> !holdReasonKey.equals(it.getHoldOrderReason())
                        && "Y".equals(it.getHoldStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(results)) {
            // 取消HOLD单
            // 更新主表信息
            OcBOrder ocBOrderDto = new OcBOrder();
            ocBOrderDto.setId(orderId);
            ocBOrderDto.setIsInterecept(OcBOrderHoldConst.HOLD_ORDER_NO);
            ocBOrderDto.setModifierename(SystemUserResource.getRootUser().getEname());
            ocBOrderDto.setModifiername(SystemUserResource.getRootUser().getName());
            ocBOrderDto.setModifierid(Long.valueOf(SystemUserResource.getRootUser().getId()));
            ocBOrderDto.setModifieddate(new Date());
            omsOrderService.updateOrderInfo(ocBOrderDto);
        }

        try {
            //插入日志
            String message = holdReason.getMessage() + ",系统自动释放HOLD单!";
            OrderLogTypeEnum orderLogType;
            if (OrderHoldReasonEnum.REFUND_HOLD.equals(newHoldReason)) {
                orderLogType = OrderLogTypeEnum.ORDER_REFUND_RELEASE;
            } else if (OrderHoldReasonEnum.TB_AUDIT_VOLUNTARILY_HOLD.equals(newHoldReason)) {
                orderLogType = OrderLogTypeEnum.UPDATE_ADDRDERSS_HOLD_RELEASE;
            } else if (OrderHoldReasonEnum.ADD_EXCHANGE_ORDER.equals(newHoldReason)) {
                orderLogType = OrderLogTypeEnum.EXCHANGE_GOODS_HOLD_RELEASE;
            } else if(OrderHoldReasonEnum.JITX_HOLD.equals(newHoldReason)) {
                orderLogType = OrderLogTypeEnum.ORDER_JITX_HOLD;
            } else {
                orderLogType = OrderLogTypeEnum.ORDER_HOLD_CANCEL;
            }
            orderLogService.addUserOrderLog(orderId, null, orderLogType.getKey(), message, null, null,
                    SystemUserResource.getRootUser());

        } catch (Exception e) {
            log.error(LogUtil.format("新增订单HOLD单日志失败,Exception = {}，orderId=", orderId)
                    , Throwables.getStackTraceAsString(e));
        }

    }

    // 保存Hold单明细
    public ValueHolder saveOrUpdateItems(OcBOrder orderInfo, List<StCHoldOrderRequest> stCHoldOrderList, User operateUser,
                                         Boolean isAuto) {
        ValueHolder vh = new ValueHolder();
        if (CollectionUtils.isEmpty(stCHoldOrderList)) {
            vh.put("code", ResultCode.FAIL);
            return vh;
        }
        int result = 0;
        // 构建HOLD单明细， 同一种原因的策略，取HOLD单释放时间最大的，不释放认为时间最大
        Map<String, OcBOrderHoldItem> itemMap = new HashMap<>();
        for (StCHoldOrderRequest stCHoldOrder : stCHoldOrderList) {
            if (!checkOrderFlag(stCHoldOrder, orderInfo) && isAuto) {
                continue;
            }
            OcBOrderHoldItem item = buildHoldOrderItem(orderInfo, stCHoldOrder);
            if (itemMap.get(String.valueOf(stCHoldOrder.getHoldDetentionOrderReason())) == null) {
                itemMap.put(String.valueOf(stCHoldOrder.getHoldDetentionOrderReason()), item);
            } else {
                if (itemMap.get(String.valueOf(stCHoldOrder.getHoldDetentionOrderReason())).getReleaseTime() == null) {
                    continue;
                }
                if (item.getReleaseTime() == null) {
                    itemMap.put(String.valueOf(stCHoldOrder.getHoldDetentionOrderReason()), item);
                } else {
                    if (itemMap.get(String.valueOf(stCHoldOrder.getHoldDetentionOrderReason())).getReleaseTime().before(item.getReleaseTime())) {
                        itemMap.put(String.valueOf(stCHoldOrder.getHoldDetentionOrderReason()), item);
                    }
                }
            }
        }
        if (itemMap == null || itemMap.isEmpty()) {
            vh.put("code", ResultCode.FAIL);
            return vh;
        }
        String holdName = "";
        for (String key : itemMap.keySet()) {
            OcBOrderHoldItem temp = itemMap.get(key);
            OcBOrderHoldItem item =
                    ocBOrderHoldItemMapper.selectOrderHoldItemByOrderIdAndHoldReason(orderInfo.getId(),
                            temp.getHoldOrderReason());
            if (item == null) {
                temp.setId(ModelUtil.getSequence(TAB_OC_B_ORDER_HOLD_ITEM));
                makeCreateField(temp, operateUser);
                item = temp; // 日志需要
                ocBOrderHoldItemMapper.insert(temp);
            } else {
                item.setReleaseTime(temp.getReleaseTime());
                item.setStCHoldOrderEname(temp.getStCHoldOrderEname());
                item.setHoldStatus(OcBOrderHoldConst.YES);
                makeModifierField(item, operateUser);
                ocBOrderHoldItemMapper.updateOcBOrderHoldItem(item);
            }
            result++;
            StringBuilder sb = new StringBuilder();
            if (isAuto) {
                holdName+=item.getStCHoldOrderEname()+",";
                sb.append("系统自动HOLD单，HOLD单原因:");
            } else {
                holdName="手动HOLD单";
                sb.append("手动HOLD单，HOLD单原因:");
            }
            //插入日志
            StCHoldOrderReason stCHoldOrderReason = holdOrderReasonQueryService.selectHoldOrderById(Long.valueOf(item.getHoldOrderReason()));
            String message = sb.append(Objects.nonNull(stCHoldOrderReason) ? stCHoldOrderReason.getReason() : StringUtils.EMPTY)
                    .append("释放时间：")
                    .append(item.getReleaseTime() == null ? "未设置释放时间" :
                            FormatDateUtil.formatDate(item.getReleaseTime(), "yyyy-MM-dd " +
                                    "HH:mm:ss"))
                    .toString();
            insertOrderLog(orderInfo.getId(), orderInfo.getBillNo(), OrderLogTypeEnum.ORDER_HOLD.getKey(), message,
                    null, null, operateUser);
        }
        if (result != 0) {
            vh.put("code", ResultCode.SUCCESS);
            OcBOrder  order=new OcBOrder();
            order.setId(orderInfo.getId());
            //第一次Hold单埋点
            if (Objects.isNull(orderInfo.getHoldDate())){
                order.setHoldDate(new Date());
                ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.HOLD_DATE,new Date(),order.getId(),operateUser);
            }

            order.setHoldReason(holdName.substring(0,holdName.length()-1));
            StCHoldOrderRequest stCHoldOrderRequest = stCHoldOrderList.get(0);
            order.setHoldReasonId(stCHoldOrderRequest.getHoldDetentionOrderReason());
            ocBOrderMapper.updateById(order);
        } else {
            vh.put("code", ResultCode.FAIL);
        }
        return vh;
    }

    // 校验订单标识
    private boolean checkOrderFlag(StCHoldOrderRequest stCHoldOrder, OcBOrder orderInfo) {
        //直播hold
        if (OcOrderTagEum.TAG_LIVE.getKey().equalsIgnoreCase(stCHoldOrder.getOrderFlag())
                && Optional.ofNullable(orderInfo.getLiveFlag()).orElse(0).intValue() == 1) {
            return checkLiveHoldFlag(stCHoldOrder,orderInfo);
        }
//        if (OcOrderTagEum.TAG_LIGHT_SUPPLY.getKey().equalsIgnoreCase(stCHoldOrder.getOrderFlag())
//                && Optional.ofNullable(orderInfo.getHasLightSupplyProd()).orElse(0).intValue() == 1) {
//            return  true;
//        }
        if (OcOrderTagEum.TAG_O2O.getKey().equalsIgnoreCase(stCHoldOrder.getOrderFlag())
                && Optional.ofNullable(orderInfo.getIsO2oOrder()).orElse(0).intValue() == 1) {
            return true;
        }
        //预售hold
        if (OcOrderTagEum.TAG_ELE_PRE.getKey().equalsIgnoreCase(stCHoldOrder.getOrderFlag())
                && Optional.ofNullable(orderInfo.getDouble11PresaleStatus()).orElse(0).intValue() == 1) {
            return true;
        }
        //普通商品hold 根据订单标识进行判断
        if (OcBOrderHoldConst.NORMAL.equalsIgnoreCase(stCHoldOrder.getOrderFlag())) {
            return checkGoodsHoldFlag(stCHoldOrder,orderInfo);
        }
        //省市区HOld单
        if (OcBOrderHoldConst.PROVINCE.equalsIgnoreCase(stCHoldOrder.getOrderFlag())) {
            return checkProvinceHoldFlag(stCHoldOrder,orderInfo);
        }
        return false;
    }
    /**
     * <AUTHOR>
     * @Date 15:47 2021/9/3
     * @Description 省市区Hold单
     */
    private boolean checkProvinceHoldFlag(StCHoldOrderRequest stCHoldOrder, OcBOrder orderInfo) {
        log.info(LogUtil.format("checkProvinceHoldFlag.订单明细数据为{}"), orderInfo);
        List<StCHoldProvinceItemDO> stCHoldProvinceItemDOList = stCHoldOrder.getStCHoldProvinceItemDOList();
        if (CollectionUtils.isNotEmpty(stCHoldProvinceItemDOList)){
            for (StCHoldProvinceItemDO stCHoldProvinceItemDO : stCHoldProvinceItemDOList) {
                StringBuilder sb = new StringBuilder();
                if (stCHoldProvinceItemDO.getSellerProvinceId() != null){
                    sb.append(stCHoldProvinceItemDO.getSellerProvinceId()+"");
                }
                if (stCHoldProvinceItemDO.getSellerCityId() != null){
                    sb.append(stCHoldProvinceItemDO.getSellerCityId()+"");
                }
                if (stCHoldProvinceItemDO.getSellerAreaId() != null){
                    sb.append(stCHoldProvinceItemDO.getSellerAreaId()+"");
                }
                log.info(LogUtil.format("策略拼接省市区的字段为sb:{}"),sb);
                StringBuilder sb1 = new StringBuilder();
                if (orderInfo.getCpCRegionProvinceId() != null && stCHoldProvinceItemDO.getSellerProvinceId() != null){
                    sb1.append(orderInfo.getCpCRegionProvinceId()+"");
                }
                if (orderInfo.getCpCRegionCityId() != null && stCHoldProvinceItemDO.getSellerCityId() != null){
                    sb1.append(orderInfo.getCpCRegionCityId()+"");
                }
                if (orderInfo.getCpCRegionAreaId() != null && stCHoldProvinceItemDO.getSellerAreaId() != null){
                    sb1.append(orderInfo.getCpCRegionAreaId()+"");
                }
                log.info(LogUtil.format("主表拼接的省市区字段为sb1:{}"), sb1);
                if (sb1.toString().equals(sb.toString())){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * <AUTHOR>
     * @Date 13:59 2021/4/28
     * @Description  普通商品Hold验证
     */
    private boolean checkGoodsHoldFlag(StCHoldOrderRequest stCHoldOrder, OcBOrder orderInfo) {
        QueryWrapper<OcBOrderItem> itemWrapper = new QueryWrapper<OcBOrderItem>();
        itemWrapper.eq("OC_B_ORDER_ID", orderInfo.getId());
        List<OcBOrderItem> itemList = ocBOrderItemMapper.selectList(itemWrapper);
        log.info(LogUtil.format("checkGoodsHoldFlag.订单明细数据为{}"), itemList);
        List<StCHoldOrderItemDO> stCHoldOrderItemDOList = stCHoldOrder.getStCHoldOrderItemDOList();
        if (CollectionUtils.isNotEmpty(stCHoldOrderItemDOList)){
            //取出所有的商品id
            List<Integer> integerList = itemList.stream().map(i->i.getPsCProId().intValue()).collect(Collectors.toList());
            //查询所有的商品
            Map<Long,PsCPro> proMap = this.queryProByList(integerList);
            for (OcBOrderItem ocBOrderItem : itemList) {
                Long prId = ocBOrderItem.getPsCProId();
                Long skuId = ocBOrderItem.getPsCSkuId();
                PsCPro psCPro = proMap.get(ocBOrderItem.getPsCProId());
                for (StCHoldOrderItemDO stCHoldOrderItemDO : stCHoldOrderItemDOList) {
                    String rulesRecognition =stCHoldOrderItemDO.getRulesRecognition();
                    //识别内容
                    String content = stCHoldOrderItemDO.getContent();
                    if ( prId ==null ||psCPro ==null ){
                        log.info(LogUtil.format("没有查到商品档案,明细表prid：",prId));
                        return false;
                    }
                    //大类验证 LARGESERIES
                    /*if (OrderHoldEnum.CATEGORIES.getKey().equals(rulesRecognition)){
                        if (psCPro.getLargeseries() !=null &&
                                psCPro.getLargeseries().equals(stCHoldOrderItemDO.getCategoriesId()) &&
                                checkHoldRulesAll(ocBOrderItem.getRealAmt(),stCHoldOrderItemDO.getMinimumAmount())){
                            return true;
                        }
                    }
                    //中类验证
                    if (OrderHoldEnum.IN_THE_CLASS.getKey().equals(rulesRecognition)){
                        if (psCPro.getSmallseries() !=null&&
                                psCPro.getSmallseries().equals(stCHoldOrderItemDO.getInTheClassId()) &&
                                checkHoldRulesAll(ocBOrderItem.getRealAmt(),stCHoldOrderItemDO.getMinimumAmount())){
                            return true;
                        }
                    }
                    //小类验证
                    if (OrderHoldEnum.SMALL_CLASS.getKey().equals(rulesRecognition)){
                        if (psCPro.getSubseries() !=null && psCPro.getLargeseries().equals(stCHoldOrderItemDO.getCategoriesId()) &&
                                checkHoldRulesAll(ocBOrderItem.getRealAmt(),stCHoldOrderItemDO.getMinimumAmount())){
                            return true;
                        }
                    }*/
                    //商品编码
                    if (OrderHoldEnum.GOODS_CODE.getKey().equals(rulesRecognition)){
                        if (prId.equals(stCHoldOrderItemDO.getGoodsCodeId()) && checkHoldRulesAll(ocBOrderItem.getRealAmt(),stCHoldOrderItemDO.getMinimumAmount())){
                            return true;
                        }
                    }
                    //条码验证
                    if (OrderHoldEnum.GOODS_SKU.getKey().equals(rulesRecognition)){
                        if (skuId!=null){
                            if (skuId.equals(stCHoldOrderItemDO.getGoodsSkuId())&&
                                    checkHoldRulesAll(ocBOrderItem.getRealAmt(),stCHoldOrderItemDO.getMinimumAmount())){
                                    return true;
                                }
                        }

                    }
                    //平台商品ID
                    if (OrderHoldEnum.PT_SPU_ID.getKey().equals(rulesRecognition)){

                        if (StringUtils.isNotEmpty(ocBOrderItem.getNumIid()) && ocBOrderItem.getNumIid().equals(stCHoldOrderItemDO.getPtSpuId())&&
                                checkHoldRulesAll(ocBOrderItem.getRealAmt(),stCHoldOrderItemDO.getMinimumAmount())){
                            return true;
                        }

                    }
                    //平台条码
                    if (OrderHoldEnum.PT_SKU_ID.getKey().equals(rulesRecognition)){
                        if (StringUtils.isNotEmpty(ocBOrderItem.getSkuNumiid()) && ocBOrderItem.getSkuNumiid().equals(stCHoldOrderItemDO.getPtSkuId())&&
                                checkHoldRulesAll(ocBOrderItem.getRealAmt(),stCHoldOrderItemDO.getMinimumAmount())){
                            return true;
                        }
                    }
                    //商品标题关键字
                    if(OrderHoldEnum.PT_PRO_TITLE.getKey().equals(rulesRecognition)){
                        //平台商品名称
                        String ptProName = ocBOrderItem.getPtProName();
                        if(StringUtils.isNotEmpty(content) && ptProName.contains(content)){
                            return true;
                        }
                    }
                    //全部
                    if (OrderHoldEnum.ALL.getKey().equals(rulesRecognition)){
                        if (checkHoldRulesAll(ocBOrderItem.getRealAmt(),stCHoldOrderItemDO.getMinimumAmount())){
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
    /**
     * <AUTHOR>
     * @Date 16:18 2021/4/28
     * @Description hold单明细是规则是全部的
     * @param price
     * @param minimumAmount
     */
    private boolean checkHoldRulesAll(BigDecimal price, BigDecimal minimumAmount) {
        if (price ==null || minimumAmount==null){
            return false;
        }
        return minimumAmount.compareTo(price) > 0;
    }

    /**
     * <AUTHOR>
     * @Date 14:02 2021/4/27
     * @Description 直播hold单校验打标
     * 明细中直播场次不为空，具体直播场次都包含 √打标
     * 部分直播场次为空，不为空的直播场次都包含 √打标
     * 明细直播场次都为空 或者不为空的有不满足的 ×不达标
     */
    private boolean checkLiveHoldFlag(StCHoldOrderRequest stCHoldOrder, OcBOrder orderInfo) {
        QueryWrapper<OcBOrderItem> itemWrapper = new QueryWrapper<OcBOrderItem>();
        itemWrapper.eq("OC_B_ORDER_ID", orderInfo.getId());
        List<OcBOrderItem> itemList = ocBOrderItemMapper.selectList(itemWrapper);
        log.info(LogUtil.format("未过滤订单明细数据为[{}]"), itemList);
        List<OcBOrderItem> newItemList = itemList.stream().filter(o-> o.getLiveEvents() != null).collect(Collectors.toList());
        log.info(LogUtil.format("过滤订单明细数据为[{}]"), newItemList);
        if (CollectionUtils.isEmpty(newItemList)){
            return false;
        }else {
            String holdLiveEvents =stCHoldOrder.getHoldLiveEvents();
            if (holdLiveEvents ==null){
                return false;
            }
            for (OcBOrderItem ocBOrderItem : newItemList) {
                if (!holdLiveEvents.contains(ocBOrderItem.getLiveEvents().toString())){
                    return false;
                }
            }
        }
        return true;
    }

    private OcBOrderHoldItem buildHoldOrderItem(OcBOrder orderInfo, StCHoldOrderRequest stCHoldOrder) {
        OcBOrderHoldItem item = new OcBOrderHoldItem();
        item.setOcBOrderId(orderInfo.getId());
        item.setStCHoldOrderId(stCHoldOrder.getId());
        item.setHoldOrderReason(stCHoldOrder.getHoldDetentionOrderReason());
        StCHoldOrderReason stCHoldOrderReason =
                holdOrderReasonQueryService.selectHoldOrderById(Long.valueOf(stCHoldOrder.getHoldDetentionOrderReason()));
        if (Objects.nonNull(stCHoldOrderReason)) {
            item.setHoldOrderReasonMsg(stCHoldOrderReason.getReason());
        }
        item.setHoldStatus(OcBOrderHoldConst.YES);
        //带出策略名称
        item.setStCHoldOrderEname(stCHoldOrder.getEname());
        item.setReleaseTime(bulidReleaseTime(orderInfo, stCHoldOrder));
        return item;
    }

    // 根据策略计算释放时间
    private Date bulidReleaseTime(OcBOrder orderInfo, StCHoldOrderRequest stCHoldOrder) {
        if (!OcBOrderHoldConst.YES.equalsIgnoreCase(stCHoldOrder.getIsAutoRelease())) {
            return null;
        }
        // 指定时点释放
        if (OcBOrderHoldConst.RELEASE_TIME_TYPE_1.equals(stCHoldOrder.getReleaseTimeType())) {
            return stCHoldOrder.getReleaseTime();
        }
        // 固定时长释放 需要根据配置计算释放时间
        if(OcBOrderHoldConst.RELEASE_TIME_TYPE_2.equals(stCHoldOrder.getReleaseTimeType())){
            Date time = OcBOrderHoldConst.ORDER_DATE.equals(stCHoldOrder.getDayType()) ? orderInfo.getOrderDate() :
                    orderInfo.getPayTime();
            if (OcBOrderHoldConst.TIME_UNIT_MINUTE.equals(stCHoldOrder.getTimeUnit())) {
                return DateConversionUtil.plusMinutes(time, stCHoldOrder.getFixedDuration());
            } else if (OcBOrderHoldConst.TIME_UNIT_HOUR.equals(stCHoldOrder.getTimeUnit())) {
                return DateConversionUtil.plusHours(time, stCHoldOrder.getFixedDuration());
            } else if (OcBOrderHoldConst.TIME_UNIT_DAY.equals(stCHoldOrder.getTimeUnit())) {
                return DateConversionUtil.plusDays(time, stCHoldOrder.getFixedDuration());
            }
        }
        // 临近预计发货日释放 需要根据配置计算释放时间
        if(OcBOrderHoldConst.RELEASE_TIME_TYPE_3.equals(stCHoldOrder.getReleaseTimeType())){
            QueryWrapper<OcBOrderItem> itemWrapper = new QueryWrapper<OcBOrderItem>();
            itemWrapper.eq("OC_B_ORDER_ID", orderInfo.getId());
            List<OcBOrderItem> itemList = ocBOrderItemMapper.selectList(itemWrapper);
            Date newDate =null;
            for (OcBOrderItem ocBOrderItem : itemList) {

                if (ocBOrderItem.getEstimateConTime() !=null){
                    Date expectedDeliveryDate =parsDate(ocBOrderItem.getEstimateConTime(),stCHoldOrder.getTimeUnit(),stCHoldOrder.getAdvanceTime());
                        newDate = newDate ==null ? expectedDeliveryDate :newDate ;
                        if (expectedDeliveryDate.after(newDate)){
                            newDate = expectedDeliveryDate;
                        }
                }
            }
            return newDate;
        }

        return null;
    }
    /**
     * <AUTHOR>
     * @Date 18:08 2021/4/27
     * @Description 解析时间
     */
    private Date parsDate(Date time, Integer timeUnit, Integer number) {
        if (OcBOrderHoldConst.TIME_UNIT_MINUTE.equals(timeUnit)) {
            return DateConversionUtil.plusMinutes(time, -number);
        } else if (OcBOrderHoldConst.TIME_UNIT_HOUR.equals(timeUnit)) {
            return DateConversionUtil.plusHours(time, -number);
        } else if (OcBOrderHoldConst.TIME_UNIT_DAY.equals(timeUnit)) {
            return DateConversionUtil.plusDays(time, -number);
        }
        return time;
    }

    // 取最大的hold单释放时间
    private Date getReleaseTime(Date releaseTime, OcBOrderHoldItem item) {
        if (releaseTime == null || item.getReleaseTime() == null) {
            return null;
        } else {
            return releaseTime.before(item.getReleaseTime()) ? item.getReleaseTime() : releaseTime;
        }
    }

    // 校验订单是否可以HOLD单
    private void checkHoldOrder(OcBOrder ocBOrder) {
        // 订单状态为 作废、取消、平台发货、仓库发货 不允许HOLD单
        if (ocBOrder.getOrderStatus().equals(OmsOrderStatus.SYS_VOID.toInteger())) {
            throw new NDSException("不允许对“作废“的订单进行Hold单！");
        }
        if (ocBOrder.getOrderStatus().equals(OmsOrderStatus.CANCELLED.toInteger())) {
            throw new NDSException("不允许对“取消”的订单进行HOLD单");
        }
        if (ocBOrder.getOrderStatus().equals(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())) {
            throw new NDSException("不允许对“仓库发货”的订单进行HOLD单");
        }
        if (ocBOrder.getOrderStatus().equals(OmsOrderStatus.PLATFORM_DELIVERY.toInteger())) {
            throw new NDSException("不允许对“平台发货”的订单进行HOLD单");
        }
        if (ocBOrder.getOrderStatus().equals(OmsOrderStatus.PENDING_WMS.toInteger())) {
            throw new NDSException("不允许对“待传WMS”的订单进行HOLD单");
        }
    }

    private void makeCreateField(OcBOrderHoldItem item, User user) {
        Date date = new Date();
        item.setAdClientId((long) user.getClientId());//所属公司
        item.setAdOrgId((long) user.getOrgId());//所属组织
        item.setOwnerid(Long.valueOf(user.getId()));//创建人id
        item.setCreationdate(date);//创建时间
        item.setOwnername(user.getName());//创建人用户名
        item.setOwnerename(user.getEname());
        item.setModifierid(Long.valueOf(user.getId()));//修改人id
        item.setModifiername(user.getName());//修改人用户名
        item.setModifierename(user.getEname());
        item.setModifieddate(date);//修改时间
        item.setIsactive("Y");//是否启用
    }

    private void makeModifierField(OcBOrderHoldItem item, User user) {
        item.setModifierid(Long.valueOf(user.getId()));//修改人id
        item.setModifiername(user.getName());//修改人用户名
        item.setModifieddate(new Date());//修改时间
        item.setModifierename(user.getEname());
    }


    /**
     * 查找是否有hold单
     *
     * @param orderId
     * @param holdReason
     * @return
     */
    public boolean isHolding(Long orderId, OrderHoldReasonEnum holdReason, String status) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderHoldItemService.isHolding ,hold单场景:{},status:{},orderId=", orderId),
                    holdReason.getMessage(), status);
        }
        // 查找hold单明细
        List<OcBOrderHoldItem> holdItems = ocBOrderHoldItemMapper.selectList(
                new LambdaQueryWrapper<OcBOrderHoldItem>()
                        .eq(OcBOrderHoldItem::getOcBOrderId, orderId)
                        .eq(OcBOrderHoldItem::getHoldOrderReason, holdReason.getKey())
                        .eq(OcBOrderHoldItem::getHoldStatus, status));
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("=============== isHolding :{}"), !holdItems.isEmpty());
        }
        return !holdItems.isEmpty();
    }

    // 插入订单日志
    private void insertOrderLog(long orderId, String billNo, String logType, String logMessage, String param,
                                String errorMessage, User operateUser) {
        //调用添加订单日志
        try {
            orderLogService.addUserOrderLog(orderId, billNo, logType, logMessage, null, null, operateUser);
        } catch (Exception e) {
            log.error(LogUtil.format("新增订单日志失败，失败原因:{}"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 计算hold释放时间
     * N个hold原因，其中有hold释放时间null则返回 null，反之返回最长的hold释放时间
     *
     * @return
     */
    public Date calculateHoldReleaseTime(Long orderId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("calculateHoldReleaseTime start,orderId=", orderId));
        }
        if (Objects.isNull(orderId)) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("calculateHoldReleaseTime orderId is null return HoldReleaseTime is null"));
            }
            return null;
        }

        /* @20210104 黄志优注：查询 HoldItem是否存在来判断是否hold过单。
        * 不存在明细，则从来没有hold单，返回null；反之执行后续逻辑*/
        QueryWrapper<OcBOrderHoldItem> wrapper = new QueryWrapper<>();
        wrapper.in("oc_b_order_id", orderId);
        List<OcBOrderHoldItem> allHoldItemList = ocBOrderHoldItemMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(allHoldItemList)){
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("calculateHoldReleaseTime allHoldItemList is null return HoldReleaseTime is "));
            }

            return  null;
        }

        /* @20210104 黄志优注：判断是否还在hold单。
         * 不存在，则返回当前时间作为hold释放时间，反之取释放时间最久的*/
        List<OcBOrderHoldItem> ocBOrderHoldItems = allHoldItemList.stream()
            .filter(e -> OcBOrderHoldConst.YES.equals(e.getHoldStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ocBOrderHoldItems)) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("calculateHoldReleaseTime ocBOrderHoldItems is null return HoldReleaseTime " +
                        "is currentTimeMillis"));
            }
            //手动释放hold取当前时间
            return new Date();
        }

        Map<Long, Date> calculateHoldReleaseTimeMap = new HashMap<>(ocBOrderHoldItems.size());

        for (OcBOrderHoldItem ocBOrderHoldItem : ocBOrderHoldItems) {

            Date releaseTime = ocBOrderHoldItem.getReleaseTime();
            if (releaseTime == null) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("calculateHoldReleaseTime ocBOrderHoldItems isExist null return " +
                            "HoldReleaseTime is null"));
                }

                return null;
            }

            calculateHoldReleaseTimeMap.put(releaseTime.getTime(), releaseTime);
        }

        Set<Long> releaseSet = calculateHoldReleaseTimeMap.keySet();

        Long maxReleaseTime = Collections.max(releaseSet);

        Date maxReleaseDate = calculateHoldReleaseTimeMap.get(maxReleaseTime);

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("calculateHoldReleaseTime return HoldReleaseTime=", maxReleaseDate));
        }

        return maxReleaseDate;
    }

    /**
     * <AUTHOR>
     * @Date 13:44 2021/4/29
     * @Description 根据商品编码查询商品档案
     */
    public PsCPro queryProByIds(Long proId) {
        List<Integer> list = new ArrayList<Integer>();
        list.add(proId.intValue());
        List<PsCPro> proList = psRpcService.queryProByListIds(list);
        if (proList != null) {
            return proList.get(0);
        }
        return null;
    }

    /**
     *  查询明细所属的所有商品
     * @param list
     * @return
     */
    private Map<Long,PsCPro> queryProByList(List<Integer> list){
        List<PsCPro> proList = psRpcService.queryProByIds(list);
        if (!CollectionUtils.isEmpty(proList) && proList.size() > 0) {
            return proList.stream().collect(Collectors.toMap(PsCPro ::getId, p->p));
        }
        return null;
    }
}

