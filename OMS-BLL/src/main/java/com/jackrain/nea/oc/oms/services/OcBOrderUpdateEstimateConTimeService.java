package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBOccupyTask;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> lin yu
 * @date : 2022/7/20 下午4:44
 * @describe :
 */

@Component
@Slf4j
public class OcBOrderUpdateEstimateConTimeService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOccupyTaskMapper ocBOccupyTaskMapper;
    @Autowired
    private GenerateCyclePurchaseSubOrderService generateCyclePurchaseSubOrderService;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    public ValueHolderV14 updateEstimateConTime(JSONObject jsonObject, User user) {
        ValueHolderV14 valueHolderV14;

        valueHolderV14 = checkParam(jsonObject, user);
        if (!valueHolderV14.isOK()) {
            return valueHolderV14;
        }

        Long orderId = jsonObject.getLong("orderId");
        Date estimateConTime = jsonObject.getDate("estimateConTime");

        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {

                doUpdateEstimateConTime(orderId, estimateConTime, user);

            } else {
                log.error(LogUtil.format("修改预计发货时间加锁失败", orderId));
                throw new NDSException("当前订单正在操作中");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("修改预计发货时间失败", orderId), Throwables.getStackTraceAsString(e));

            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
            return valueHolderV14;

        } finally {
            redisLock.unlock();
        }

        return valueHolderV14;
    }

    private void doUpdateEstimateConTime(Long orderId, Date estimateConTime, User user) {

        OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);

        if (ocBOrder == null) {
            throw new NDSException("当前订单已经不存在");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsCycle())){
            throw new NDSException("天猫周期购订单不允许修改预计发货时间");
        }
        Integer orderStatus = ocBOrder.getOrderStatus();

        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {

            OcBOrder updateModel = new OcBOrder();
            updateModel.setEstimateConTime(estimateConTime);
            updateModel.setModifierid(user.getId().longValue());
            updateModel.setModifiername(user.getName());
            updateModel.setModifierename(user.getEname());
            updateModel.setModifieddate(new Date());

            ocBOrderMapper.update(updateModel, new UpdateWrapper<OcBOrder>().lambda()
                    .in(OcBOrder::getId, orderId));

            OcBOccupyTask ocBOccupyTask = new OcBOccupyTask();
            ocBOccupyTask.setNextTime(estimateConTime);
            ocBOccupyTask.setModifieddate(new Date());
            ocBOccupyTaskMapper.update(ocBOccupyTask,new UpdateWrapper<OcBOccupyTask>().lambda()
                    .in(OcBOccupyTask::getOrderId, orderId));

            // 判断订单业务类型，如果是中台周期购提货，则需要自动顺延后续期数
            handleCyclePurchaseAutoDelay(ocBOrder, user, estimateConTime);
        } else {
            throw new NDSException("当前订单状态不允许修改预计发货时间");
        }
    }


    private ValueHolderV14 checkParam(JSONObject jsonObject, User user) {

        ValueHolderV14 valueHolderV14 = new ValueHolderV14(ResultCode.SUCCESS, "SUCCESS");

        if (jsonObject.getLong("orderId") == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请传入订单主表ID");
            return valueHolderV14;
        }

        Date estimateConTime = jsonObject.getDate("estimateConTime");
        if (estimateConTime == null) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("请选择预计发货时间");
            return valueHolderV14;
        } else if (estimateConTime.before(new Date())) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("预计发货时间不能小于当前时间");
            return valueHolderV14;
        }

        return valueHolderV14;

    }

    /**
     * 处理中台周期购订单的自动顺延逻辑
     *
     * @param ocBOrder 订单信息
     * @param user 操作用户
     * @param estimateConTime 新的预计发货时间
     */
    private void handleCyclePurchaseAutoDelay(OcBOrder ocBOrder, User user, Date estimateConTime) {
        try {
            // 判断订单业务类型是否为中台周期购提货
            if (!isCyclePurchasePickupOrder(ocBOrder)) {
                return;
            }

            log.info("检测到中台周期购提货订单，开始处理自动顺延逻辑，订单ID={}，业务类型={}",
                    ocBOrder.getId(), ocBOrder.getBusinessType());

            // 调用周期购服务的自动顺延逻辑
            generateCyclePurchaseSubOrderService.autoDelaySubsequentOrders(ocBOrder, user, estimateConTime);

        } catch (Exception e) {
            log.error("处理中台周期购自动顺延失败，订单ID={}，异常信息={}", ocBOrder.getId(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 判断是否为中台周期购提货订单
     *
     * @param ocBOrder 订单信息
     * @return true-是中台周期购提货订单，false-不是
     */
    private boolean isCyclePurchasePickupOrder(OcBOrder ocBOrder) {
        // 使用工具类的方法判断
        return OmsBusinessTypeUtil.isCyclePurchasePickupOrder(ocBOrder);
    }
}
