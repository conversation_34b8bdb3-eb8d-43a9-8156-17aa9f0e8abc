package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.model.TradeMemoUpdateModel;
import com.jackrain.nea.oc.oms.gsi.GSI4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: xiWen.z
 * create at: 2019/9/17 0017
 */
@Slf4j
@Component
public class OcBImportReturnRemarkService {

    /**
     * 淘宝
     */
    private final int tb = 2;
    /**
     * 淘宝分销
     */
    private final int tbd1 = 3;
    /**
     * 淘宝经销
     */
    private final int tbD2 = 77;
    /**
     * 京东
     */
    private final int jd = 4;
    /**
     * 平台异常码
     */
    private final int exCode = -2;
    /**
     * 异常长度
     */
    private final int exLen = 100;
    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private CpRpcService cpRpcService;

    public ValueHolderV14 importReturnRemark(Map<String, OcBReturnOrder> sourceMap, JSONArray osc, boolean cv, User usr) {
        ValueHolderV14 vh = new ValueHolderV14();

        try {
            return batchImportSellerRemark(sourceMap, osc, cv, usr);
        } catch (Exception e) {
            log.error(LogUtil.format("导入错误") + Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
            return vh;
        }

    }


    /**
     * @param sourceMap
     * @param osc
     * @param cv
     * @param usr
     * @return
     */
    private ValueHolderV14 batchImportSellerRemark(Map<String, OcBReturnOrder> sourceMap, JSONArray osc,
                                                   boolean cv, User usr) {
        ValueHolderV14 vh = new ValueHolderV14();
        //    JSONObject search = ES4ReturnOrder.getReturnOrderByOrigSourcecode(osc);
        List<String> sourceCodes = JSONArray.parseArray(osc.toJSONString(), String.class);
        osc = null;
        //    List<Long> doIds = splitOrderIds(search);
        List<Long> doIds = GSI4ReturnOrder.getReturnIdListByOrigSourceCodes(sourceCodes);
        if (CollectionUtils.isEmpty(doIds)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage("未查询到退单", usr.getLocale()));
        }
        Set<String> codeRecord = new HashSet<>();
        Map<String, String> pfMap = new HashMap<>();
        Map<String, String> lcMap = new HashMap<>();
        lcMap.put(String.valueOf(-1), String.valueOf(0));
        int pfCnt = 0;
        int scCnt = 0;
        int spfCnt = 0;
        int tmpCnt = 0;
        List<Long> sbl;
        int rtnSize = doIds.size();
        while (rtnSize > 0) {
            if (rtnSize > 50) {
                sbl = doIds.subList(0, 50);
            } else {
                sbl = doIds;
            }
            String idsSearch = joinIds(sbl);
            List<JSONObject> rtnList = ocBReturnOrderMapper.querySourceCodeRemarkByIds(idsSearch);
            scCnt += updateLocalSellerRemark(rtnList, sourceMap, cv, lcMap, codeRecord);
            rtnSize -= 50;
            doIds.removeAll(sbl);
            sbl = null;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("batchImportSellerRemark.rtnSize/scCnt =", doIds.size(), scCnt));
        }
        Set<String> keys = sourceMap.keySet();
        for (String k : keys) {
            if (!codeRecord.contains(k)) {
                pfMap.put(String.valueOf(++pfCnt), "平台单号:" + k + " 导入失败, 该平台单号关联退单更新失败或不存在关联退单");
                continue;
            }
            ValueHolderV14 v0 = synchronizedPlatformOrder(sourceMap.get(k), usr);
            if (v0.getCode() == ResultCode.FAIL) {
                recordMsg(pfMap, ++pfCnt, sourceMap.get(k), " 同步平台失败; " + v0.getMessage());
                continue;
            }
            if (v0.getCode() == exCode) {
                recordMsg(pfMap, ++pfCnt, sourceMap.get(k), " 同步平台异常; " + v0.getMessage());
                continue;
            }
            spfCnt++;
        }
        JSONObject vhJsn = new JSONObject();
        vhJsn.put("PLATFORM", pfMap);
        lcMap.remove(String.valueOf(-1));
        vhJsn.put("LOCAL", lcMap);
        int errorCount = keys.size() - scCnt;
        String msg = "导入完成; 成功: " + scCnt + " 条, 失败: " + errorCount + " 条;"
                + StringUtils.join(lcMap.values(), "</br>")
                + "</br>" + StringUtils.join(pfMap.values(), "</br>");
        vh.setMessage(msg);
        vh.setData(vhJsn);
        return vh;


    }

    /**
     * @param rtnList
     * @param sourceMap
     * @param g
     * @param lcMap
     * @param codeRecord
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateLocalSellerRemark(List<JSONObject> rtnList, Map<String, OcBReturnOrder> sourceMap, boolean g,
                                       Map<String, String> lcMap, Set<String> codeRecord) {
        int count = 0;
        int error = Integer.parseInt(lcMap.get(String.valueOf(-1)));
        Map<Long, String> errorMap = null;
        if (!g) {
            errorMap = chooseMaxSellerRemark(rtnList, sourceMap);
        }
        for (JSONObject jsn : rtnList) {
            Long id = jsn.getLong("ID");
            String code = jsn.getString("ORIG_SOURCE_CODE");
            if (errorMap != null && errorMap.keySet().contains(id)) {
                lcMap.put(String.valueOf(error++), "退单编号: " + id + ", 平台单号: " + code + ", 修改失败; " + errorMap.get(id));
                continue;
            }
            OcBReturnOrder r = sourceMap.get(code);
            /*if (r.getOrderflag() == null) {
                r.setOrderflag(jsn.getString("ORDERFLAG"));
            }*/
            r.setOrderflag("1");
            r.setModifieddate(new Date());
            r.setSellerNick(jsn.getString("SELLER_NICK"));
            r.setPlatform(jsn.getInteger("PLATFORM"));
            String mark = sourceMap.get(code).getBackMessage();
            r.setBackMessage(mark);
            r.setId(id);
            int i = 0;
            try {
                i = ocBReturnOrderMapper.updateSellerRemark(r);
            } catch (Exception e) {
                lcMap.put(String.valueOf(error++), "退单编号: " + r.getId() + ", 平台单号: " + r.getOrigSourceCode() + ", 修改异常; ");
                continue;
            }
            if (i > ResultCode.SUCCESS) {
                count++;
                codeRecord.add(code);
            } else {
                lcMap.put(String.valueOf(error++), "退单编号: " + r.getId() + ", 平台单号: " + r.getOrigSourceCode() + ", 修改失败; ");
            }
        }
        lcMap.put(String.valueOf(-1), String.valueOf(error));
        return count;
    }

    /**
     * 同步平台
     *
     * @param rdto
     * @param usr
     * @return
     */
    private ValueHolderV14 synchronizedPlatformOrder(OcBReturnOrder rdto, User usr) {
        ValueHolderV14 vh;
        Integer platform = rdto.getPlatform();
        if (platform == null || platform == ResultCode.SUCCESS) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("同步平台失败; 平台: " + platform + ". 信息: 平台不存在");
            return vh;
        }
        if (platform == tb || platform == tbd1 || platform == tbD2) {
            TradeMemoUpdateModel request = new TradeMemoUpdateModel();
            try {
                String shopSecretKey = cpRpcService.selectShopById(rdto.getCpCShopId()).getShopSecretKey();
                String[] split = shopSecretKey.split("\\|");
                String[] split1 = split[2].split(":");
                String sessionKey = split1[1];
                request.setOperateUser(usr);
                request.setTid(Long.valueOf(rdto.getOrigSourceCode()));
                //  request.setFlag(Long.valueOf(rdto.getOrderflag()));
                request.setFlag(Long.valueOf(1));
                request.setMemo(rdto.getBackMessage());
                request.setSessionKey(sessionKey);
                request.setPlatform(platform);
                vh = ipRpcService.tradeMemoUpdate(request);
                if (vh == null) {
                    vh = new ValueHolderV14();
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("同步淘宝平台失败; 平台:" + platform + ". 平台接口返回结果为Null");
                    return vh;
                }
                if (vh.getCode() == ResultCode.FAIL) {
                    vh.setMessage("同步淘宝平台失败; 平台:" + platform + ". 状态码:" + vh.getCode() + " .平台接口返回信息: "
                            + vh.getMessage());
                    return vh;
                }
            } catch (Exception e) {
                vh = new ValueHolderV14();
                vh.setCode(exCode);
                vh.setMessage("同步淘宝平台异常; 平台:" + platform + ", 异常信息:" + e.getMessage());
                return vh;
            }
        } else if (platform == jd) {
            TradeMemoUpdateModel request = new TradeMemoUpdateModel();
            try {
                request.setOperateUser(usr);
                request.setTid(Long.valueOf(rdto.getOrigSourceCode()));
                request.setFlag(Long.valueOf(1));
                // request.setFlag(Long.valueOf(rdto.getOrderflag()));
                request.setMemo(rdto.getBackMessage());
                request.setSellerNick(rdto.getSellerNick());
                request.setPlatform(platform);
                vh = ipRpcService.tradeMemoUpdate(request);
                if (vh == null) {
                    vh = new ValueHolderV14();
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("同步京东平台失败; 平台:" + platform + ". 平台接口返回结果为Null");
                    return vh;
                }
                if (vh.getCode() == ResultCode.FAIL) {
                    vh.setMessage("同步京东平台失败; 平台:" + platform + ". 状态码:" + vh.getCode() + " .平台接口返回信息: "
                            + vh.getMessage());
                    return vh;
                }
            } catch (Exception e) {
                vh = new ValueHolderV14();
                vh.setCode(exCode);
                vh.setMessage("同步京东平台异常; 平台:" + platform + ", 异常信息:" + e.getMessage());
                return vh;
            }
        } else {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("未同步平台; 平台: " + platform + ". 信息: 此平台单号不同步平台");
        }
        vh = new ValueHolderV14();
        vh.setCode(ResultCode.SUCCESS);
        return vh;
    }


    /**
     * @param esJo
     * @return
     */
    private List<Long> splitOrderIds(JSONObject esJo) {
        List<Long> list = new ArrayList<>();
        if (esJo == null) {
            return list;
        }
        JSONArray ary = esJo.getJSONArray("data");
        if (ary == null) {
            return list;
        }
        for (int i = 0, l = ary.size(); i < l; i++) {
            JSONObject o = ary.getJSONObject(i);
            if (o == null) {
                continue;
            }
            Long id = o.getLong("ID");
            if (id == null) {
                continue;
            }
            list.add(id);
        }
        return list;
    }

    /**
     * 记录信息
     *
     * @param map
     * @param n
     * @param r
     * @param msg
     */
    private void recordMsg(Map<String, String> map, int n, OcBReturnOrder r, String msg) {
        map.put(String.valueOf(n), "平台单号:" + r.getOrigSourceCode() + msg);
    }

    /**
     * @param sbl
     * @return
     */
    private String joinIds(List<Long> sbl) {
        StringBuilder sb = new StringBuilder();
        for (Long e : sbl) {
            sb.append(",");
            sb.append(e);
        }
        return sb.substring(OcBOrderConst.IS_STATUS_IY);
    }

    /**
     * @param list
     * @param sourceMap
     * @return
     */
    private Map<Long, String> chooseMaxSellerRemark(List<JSONObject> list, Map<String, OcBReturnOrder> sourceMap) {

        Map<Long, String> errorMsg = new HashMap<>();
        Map<String, String> temp = new HashMap<>();
        for (JSONObject o : list) {
            if (o == null) {
                continue;
            }
            String v2 = o.getString("ORIG_SOURCE_CODE");
            if (StringUtils.isBlank(v2)) {
                continue;
            }
            OcBReturnOrder r = sourceMap.get(v2);
            String v5 = r.getBackMessage();
            String v3 = o.getString("BACK_MESSAGE") == null ? "" : o.getString("BACK_MESSAGE");
            if ((v3 + v5).length() > 1600) {
                errorMsg.put(o.getLong("ID"), "备注过长");
                continue;
            }
            String v6 = temp.get(v2) == null ? "" : temp.get(v2);
            if (v3.length() >= v6.length()) {
                temp.put(v2, v3);
            }
        }
        // 提取最长原始备注
        Set<String> v7 = temp.keySet();
        for (String v8 : v7) {
            OcBReturnOrder v9 = sourceMap.get(v8);
            String v10 = temp.get(v8);
            if (StringUtils.isNotBlank(v10)) {
                v9.setBackMessage(v10 + ";" + v9.getBackMessage());
            }
        }
        return errorMsg;
    }
}
