package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.ToDRPStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-07-22
 * create at : 2019-07-22 9:40 AM
 */
@Slf4j
@Component
public class RetransmissionWmsService {

    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;

    public ValueHolderV14 retransmissionWms(String param, User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        JSONObject jsonObject = JSONObject.parseObject(param);
        String returnOrderIds = jsonObject.getString("returnOrderIds");
        if (StringUtils.isBlank(returnOrderIds)) {
            holderV14.setCode(-1);
            holderV14.setMessage("returnOrderIds入参为空");
            return holderV14;
        }
        String[] ids = returnOrderIds.split(",");
        ValueHolderV14 valueHolderV14;
        List<ValueHolderV14> data = new ArrayList<>();
        int success = 0;
        int fail = 0;
        for (int i = 0; i < ids.length; i++) {
            valueHolderV14 = onceTransferWms(ids[i]);
            if (valueHolderV14.isOK()) {
                success += 1;
            } else {
                fail += 1;
            }
            data.add(valueHolderV14);
        }
        if (ids.length == success) {
            holderV14.setCode(0);
        } else {
            holderV14.setCode(-1);
        }
        holderV14.setMessage("重传WMS服务操作数据" + ids.length + "条," + "成功" + success + "条," + "失败" + fail + "条");
        holderV14.setData(data);
        return holderV14;
    }

    private ValueHolderV14 onceTransferWms(String id) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        OcBReturnOrder returnOrder = returnOrderMapper.selectByid(Long.valueOf(id));
        if (returnOrder == null) {
            holderV14.setCode(-1);
            holderV14.setMessage("当前记录已不存在!");
            return holderV14;
        }
        Integer isToWms = returnOrder.getIsTowms();
        if (WmsWithdrawalState.FAIL.toInteger().equals(isToWms)) {
            returnOrder.setIsTowms(WmsWithdrawalState.NO.toInteger());
            returnOrder.setQtyWmsFail(0L);
            returnOrder.setWmsFailreason("");
            returnOrder.setToDrpCount(0);
            returnOrder.setToDrpStatus(ToDRPStatusEnum.NOT.getCode());
            returnOrder.setToDrpFailedReason("");

            // @20200716 抽更新方法
            holderV14 = updateReturnOrder4OnceTransferWms(returnOrder);
            return holderV14;
        } else if (WmsWithdrawalState.YES.toInteger().equals(isToWms)
                && (Objects.nonNull(returnOrder.getWmsCancelStatus()) && OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger() == returnOrder.getWmsCancelStatus())) {
            // @20200716 wms撤回新需求
            // 3) 若退换货订单的传WMS状态是‘传WMS成功’，且WMS撤回状态是‘撤回成功’，则更新传WMS状态为‘未传WMS’，WMS撤回状态为‘未撤回’；
            returnOrder.setIsTowms(WmsWithdrawalState.NO.toInteger());
            // returnOrder.setReserveBigint01(0L);
            returnOrder.setWmsCancelStatus(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger());
            returnOrder.setToDrpCount(0);
            returnOrder.setToDrpStatus(ToDRPStatusEnum.NOT.getCode());
            returnOrder.setToDrpFailedReason("");
            holderV14 = updateReturnOrder4OnceTransferWms(returnOrder);
            return holderV14;
        } else {
            holderV14.setCode(-1);
            // @20200716 由于加了else if逻辑，需要修改提示
            holderV14.setMessage(returnOrder.getId() + "退换货订单传WMS状态为非传WMS失败或者传WMS成功非WMS撤回成功，不能重传！");
            return holderV14;
        }
    }

    /**
     * 更新退货单重传wms状态
     *
     * @param returnOrder
     * @return
     */
    private ValueHolderV14 updateReturnOrder4OnceTransferWms(OcBReturnOrder returnOrder) {
        ValueHolderV14 holderV14 = new ValueHolderV14();

        try {
            if (returnOrderMapper.updateById(returnOrder) > 0) {
                // SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_ORDER_TYPE_NAME, returnOrder, returnOrder.getId());
                holderV14.setCode(0);
                holderV14.setMessage(returnOrder.getId() + "退换货订单重传wms成功！");
            } else {
                holderV14.setCode(-1);
                holderV14.setMessage(returnOrder.getId() + "退换货订单重传wms失败！，更新失败");
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("重传WMS失败,异常信息为:{}"), Throwables.getStackTraceAsString(ex));
            holderV14.setCode(-1);
            holderV14.setMessage(returnOrder.getId() + "退换货订单重传wms失败！，更新异常");
        }
        return holderV14;
    }
}
