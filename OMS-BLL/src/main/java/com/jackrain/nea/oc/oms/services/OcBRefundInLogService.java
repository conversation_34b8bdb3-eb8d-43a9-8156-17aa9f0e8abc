package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInLogMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @author: 李杰
 * @since: 2019/6/21
 * create at : 2019/6/21 11:18
 */
@Slf4j
@Component
public class OcBRefundInLogService {
    private static final String INDEX = "oc_b_refund_in_log";
    private static final String TYPE = "oc_b_refund_in_log";

    @Autowired
    private OcBRefundInLogMapper ocBRefundInLogMapper;

    /**
     * @param refundInId 退货入库单编号
     * @param logType    日志类型
     * @param logMessage 日志内容
     * @param user       操作用户
     */
    public void addLog(Long refundInId, String logType, String logMessage, User user) {
        OcBRefundInLog ocBRefundInLog = new OcBRefundInLog();
        Long logId = ModelUtil.getSequence("oc_b_refund_in_log");
        ocBRefundInLog.setId(logId);
        ocBRefundInLog.setOmsonlineorderid(refundInId);
        ocBRefundInLog.setLogtype(logType);
        ocBRefundInLog.setLogmessage(logMessage);
        ocBRefundInLog.setIpaddress(user.getLastloginip());
        ocBRefundInLog.setOwnerid(user.getId() + 0L);
        ocBRefundInLog.setOwnername(user.getName());
        ocBRefundInLog.setOwnerename(user.getEname());
        ocBRefundInLog.setCreationdate(new Date());
        ocBRefundInLog.setAdClientId(user.getClientId() + 0L);
        ocBRefundInLog.setAdOrgId(user.getOrgId() + 0L);
        int insert = ocBRefundInLogMapper.insert(ocBRefundInLog);
//        if (insert > 0) {
//            try {
//                if (!SpecialElasticSearchUtil.indexExists(INDEX)) {
//                    SpecialElasticSearchUtil.indexCreate(OcBRefundInLog.class);
//                }
//                Boolean aBoolean = SpecialElasticSearchUtil.indexDocument(INDEX, TYPE, ocBRefundInLog, logId);
//                log.debug("退货入库单推送es:" + aBoolean);
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.debug("退货入库单推送es异常");
//            }
//        }
    }

    public OcBRefundInLog buildLogData(Long refundInId, String logType, String logMessage, User user) {
        try {
            OcBRefundInLog ocBRefundInLog = new OcBRefundInLog();
            Long logId = ModelUtil.getSequence("oc_b_refund_in_log");
            ocBRefundInLog.setId(logId);
            ocBRefundInLog.setOmsonlineorderid(refundInId);
            ocBRefundInLog.setLogtype(logType);
            ocBRefundInLog.setLogmessage(logMessage);
            ocBRefundInLog.setIpaddress(user.getLastloginip());
            ocBRefundInLog.setOwnerid(user.getId() + 0L);
            ocBRefundInLog.setOwnername(user.getName());
            ocBRefundInLog.setOwnerename(user.getEname());
            ocBRefundInLog.setCreationdate(new Date());
            ocBRefundInLog.setAdClientId(user.getClientId() + 0L);
            ocBRefundInLog.setAdOrgId(user.getOrgId() + 0L);
            return ocBRefundInLog;
        } catch (Exception e) {
            log.error(LogUtil.format("构建退货入库单日志数据异常：{}"), Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    public void batchInsertLog(List<OcBRefundInLog> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return;
        }
        try {
            ocBRefundInLogMapper.batchInsert(logList);
        } catch (Exception e) {
            log.error(LogUtil.format("批量插入退货入库单日志数据异常：{}"), Throwables.getStackTraceAsString(e));
        }
    }
}
