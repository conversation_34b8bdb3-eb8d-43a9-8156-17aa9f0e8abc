
package com.jackrain.nea.oc.oms.validate;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.StCInvoiceStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.StCInvoiceStrategy;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.tableService.MainTableRecord;
import com.jackrain.nea.tableService.TableServiceContext;
import com.jackrain.nea.tableService.constants.Constants;
import com.jackrain.nea.tableService.validate.BaseValidator;
import com.jackrain.nea.util.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/23 16:13
 */

@Slf4j
@Component
public class StCInvoiceStrategySaveValidator extends BaseValidator {
    @Autowired
    private StCInvoiceStrategyMapper stInvoiceStrategyMapper;
    @Autowired
    private CpRpcService cpRpcService;

    @Override
    public void validate(TableServiceContext context, MainTableRecord currentRow) {
        JSONObject commitData = currentRow.getCommitData();
        JSONObject orignalData = currentRow.getOrignalData();
        AssertUtils.notNull(orignalData, "当前记录已不存在!", context.getLocale());
        if (commitData.containsKey("ENAME")) {
            String strategyName = commitData.getString("ENAME");
            Integer integer = stInvoiceStrategyMapper.selectCount(new LambdaQueryWrapper<StCInvoiceStrategy>()
                    .eq(StCInvoiceStrategy::getEname, strategyName));
            if (integer > 0) {
                AssertUtils.logAndThrow("策略名称已存在！", context.getLocale());
            }
        }
        if (Constants.ACTION_ADD.equals(context.getActionName()) && !commitData.containsKey("CP_C_SHOP_ID")) {
            Integer integer = stInvoiceStrategyMapper.selectCount(new LambdaQueryWrapper<StCInvoiceStrategy>()
                    .isNull(StCInvoiceStrategy::getCpCShopId));
            if (integer > 0) {
                AssertUtils.logAndThrow("已存在公用开票策略,请勿重复添加！", context.getLocale());
            }
        }
        //校验店铺
        if (commitData.containsKey("CP_C_SHOP_ID")) {
            String shopIdString = commitData.getString("CP_C_SHOP_ID");
            if (StringUtils.isBlank(shopIdString)) {
                Integer integer = stInvoiceStrategyMapper.selectCount(new LambdaQueryWrapper<StCInvoiceStrategy>()
                        .isNull(StCInvoiceStrategy::getCpCShopId));
                if (integer > 0) {
                    AssertUtils.logAndThrow("已存在公用开票策略,请勿重复添加！", context.getLocale());
                }
            } else {
                String[] split = shopIdString.split(",");
                List<String> shopIdList = Arrays.asList(split);
                List<StCInvoiceStrategy> invoiceStrategies =
                        stInvoiceStrategyMapper.selectList(new LambdaQueryWrapper<StCInvoiceStrategy>()
                        .select(StCInvoiceStrategy::getCpCShopId,StCInvoiceStrategy::getId)
                        .eq(BaseModel::getIsactive, SgConstants.IS_ACTIVE_Y));
                List<String> existShopIdList =
                        invoiceStrategies.stream().filter(i->StringUtils.isNotBlank(i.getCpCShopId())).map(StCInvoiceStrategy::getCpCShopId).collect(Collectors.toList());
                String shopJoin = String.join(",",existShopIdList);
                StringBuilder shopBuilder = new StringBuilder();
                for (String shopId : shopIdList) {
                    if (shopJoin.contains(shopId)) {
                        CpShop shop = cpRpcService.selectCpCShopById(Long.valueOf(shopId));
                        if (shop != null) {
                            shopBuilder.append(shop.getCpCShopTitle()).append(",");
                        }else{
                            AssertUtils.logAndThrow("店铺已不存在或不可用,ID:"+shopId);
                        }
                    }
                }
                if (shopBuilder.length() > 0) {
                    AssertUtils.logAndThrow(shopBuilder.deleteCharAt(shopBuilder.lastIndexOf(",")).append(
                            "店铺开票策略已存在").toString(), context.getLocale());
                }
            }
        }
    }
}

