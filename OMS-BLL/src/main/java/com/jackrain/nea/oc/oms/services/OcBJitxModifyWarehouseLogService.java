package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.IpBJitxOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBJitxModifyWarehouseLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBJitxModifyWarehouseLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.VipJitxCreateChangeWareReasonEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowCreatedStateEnum;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemTableNames;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2020-12-01
 * @desc JITX订单改仓日志表
 **/
@Slf4j
@Service
public class OcBJitxModifyWarehouseLogService {
    @Autowired
    private OcBJitxModifyWarehouseLogMapper ocBJitxModifyWarehouseLogMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBJitxDealerOrderTaskService ocBJitxDealerOrderTaskService;
    @Autowired
    private IpBJitxOrderMapper ipBJitxOrderMapper;

    public List<OcBJitxModifyWarehouseLog> select(String tableName,
                                                  String where, String order, int limit) {
        return ocBJitxModifyWarehouseLogMapper.select(tableName, where, order, limit);
    }

    public int updateById(OcBJitxModifyWarehouseLog ocBJitxModifyWarehouseLog) {
        return ocBJitxModifyWarehouseLogMapper.updateById(ocBJitxModifyWarehouseLog);
    }

    public int update(OcBJitxModifyWarehouseLog ocBJitxModifyWarehouseLog, QueryWrapper queryWrapper) {
        return ocBJitxModifyWarehouseLogMapper.update(ocBJitxModifyWarehouseLog, queryWrapper);
    }

    public OcBJitxModifyWarehouseLog selectById(Long id) {
        return ocBJitxModifyWarehouseLogMapper.selectById(id);
    }

    public List<OcBJitxModifyWarehouseLog> selectBatchIds(List<Long> idList) {
        return ocBJitxModifyWarehouseLogMapper.selectBatchIds(idList);
    }

    public int updateUnCreate(OcBJitxModifyWarehouseLog log) {
        return ocBJitxModifyWarehouseLogMapper.updateUnCreate(log);
    }

    @Autowired
    private UpdateOrderInfoService updateOrderInfoService;

    /**
     * 更新失败原因,失败次数
     *
     * @param idList      ID列表
     * @param failReason  失败原因
     * @param operateUser 操作人
     * @return
     */
    public int updateFailReasonById(List<Long> idList, String failReason, User operateUser) {
        OcBJitxModifyWarehouseLog ocBJitxModifyWarehouseLog = new OcBJitxModifyWarehouseLog();
        if (StringUtils.isNotEmpty(failReason)) {
            ocBJitxModifyWarehouseLog.setFailReason(failReason.length() > 2000 ? failReason.substring(0, 1999) : failReason);
        }
        BaseModelUtil.makeBaseModifyField(ocBJitxModifyWarehouseLog, operateUser);
        return ocBJitxModifyWarehouseLogMapper.updateFailByIdList(idList, ocBJitxModifyWarehouseLog);
    }

    /**
     * 查询改仓日志
     *
     * @param orderId
     * @return
     */
    public List<OcBJitxModifyWarehouseLog> existOrderLog(Long orderId) {
        QueryWrapper<OcBJitxModifyWarehouseLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ORDER_ID", orderId);
        queryWrapper.eq("ISACTIVE", "Y");
//        queryWrapper.and(o -> {
//            o.isNull("WORKFLOW_STATE").or().in("WORKFLOW_STATE",
//                    Lists.newArrayList(VipJitxWorkflowStateEnum.CREATE.getKey(), VipJitxWorkflowStateEnum.DOING.getKey()));
//            return o;
//        });
        return ocBJitxModifyWarehouseLogMapper.selectList(queryWrapper);
    }

    /**
     * 根据订单信息生成改仓日志表
     *
     * @param ocBOrder             订单信息
     * @param warehouseId          原发货实体仓ID
     * @param warehouseCode        原发货实体仓编码
     * @param warehouseEname       原发货实体仓
     * @param newDeliveryWarehouse 新唯品会仓库编码
     * @param operateUser          操作人
     * @return
     */
    public ValueHolderV14 createByOrder(OcBOrder ocBOrder, boolean isByYY,
                                        Long warehouseId, String warehouseCode, String warehouseEname,
                                        String newDeliveryWarehouse, User operateUser) {
        // 唯品会JITX仓库对照表
        String oldDeliveryWarehouse = this.getVopWarehouse(ocBOrder, warehouseId);
        boolean jitxDealerYYWarehouse = ocBJitxDealerOrderTaskService.isJitxDealerYYWarehouse(ocBOrder.getCpCPhyWarehouseId());
        if (jitxDealerYYWarehouse) {
            newDeliveryWarehouse = null;
        }

        //如果修改前仓为空，就去JITX接口 IP_B_JITX_ORDER 查
        if (Objects.isNull(warehouseId)) {

            log.info(LogUtil.format("原实体仓为空，去JITX接口上读取原仓库,订单:{},平台单号:{}", "createByOrder"),
                    ocBOrder.getBillNo(), ocBOrder.getTid());

            IpBJitxOrder ipJitxOrder = ipBJitxOrderMapper.selectJitxOrderByOrderSn(ocBOrder.getTid());
            if (Objects.nonNull(ipJitxOrder)) {
                //仓库编码（原唯品会仓库编码）
                String deliveryWarehouse = ipJitxOrder.getDeliveryWarehouse();
                log.info(LogUtil.format("JITX接口原唯品会仓库编码:{}", "deliveryWarehouse"), deliveryWarehouse);

                if (StringUtils.isNotEmpty(deliveryWarehouse)) {

                    StCVipcomJitxWarehouse stVipcomJitxWarehouse = jitxWarehouseService.queryJitxCapacity(
                            ocBOrder.getCpCShopId(), warehouseId, deliveryWarehouse);

                    log.info(LogUtil.format("唯品会仓库映射表:{}", "queryJitxCapacity"),
                            Objects.nonNull(stVipcomJitxWarehouse) ? JSONObject.toJSONString(stVipcomJitxWarehouse) : null);

                    if (Objects.nonNull(stVipcomJitxWarehouse)) {
                        warehouseId = stVipcomJitxWarehouse.getCpCPhyWarehouseId();
                        warehouseCode = stVipcomJitxWarehouse.getCpCPhyWarehouseEcode();
                        warehouseEname = stVipcomJitxWarehouse.getCpCPhyWarehouseEname();
                        oldDeliveryWarehouse = deliveryWarehouse;

                        log.info(LogUtil.format("JITX接口原唯品会仓库编码:{}.warehouseId:{},warehouseCode:{},warehouseEname:{}",
                                "StCVipcomJitxWarehouse"), deliveryWarehouse, warehouseId, warehouseCode, warehouseEname);
                    }

                }
            }
        }

        // 平台店铺档案
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        String sellerNick = cpShop == null ? ocBOrder.getCpCShopSellerNick() : cpShop.getSellerNick();
        String vendorId = cpShop == null ? null : cpShop.getPlatformSupplierId();
        OcBJitxModifyWarehouseLog ocBJitxModifyWarehouseLog = OcBJitxModifyWarehouseLog.builder()
                .id(ModelUtil.getSequence(SystemTableNames.OC_B_JITX_MODIFY_WAREHOUSE_LOG))
                .orderId(ocBOrder.getId())
                .billNo(ocBOrder.getBillNo())
                .sourceCode(ocBOrder.getSourceCode())
                .createdStatus(VipJitxWorkflowCreatedStateEnum.UNCREATE.getCode())
                .sellerNick(sellerNick)
                .vendorId(vendorId)
                .cpCShopId(ocBOrder.getCpCShopId())
                .cpCShopEcode(cpShop.getEcode())
                .cpCShopTitle(cpShop.getCpCShopTitle())
                .beforeChangeWarehouseId(warehouseId)
                .beforeChangeWarehouseCode(warehouseCode)
                .beforeChangeWarehouseEname(warehouseEname)
                .afterChangeWarehouseId(ocBOrder.getCpCPhyWarehouseId())
                .afterChangeWarehouseCode(ocBOrder.getCpCPhyWarehouseEcode())
                .afterChangeWarehouseEname(ocBOrder.getCpCPhyWarehouseEname())
                .oldDeliveryWarehouse(oldDeliveryWarehouse)
                .newDeliveryWarehouse(newDeliveryWarehouse)
                .reasonCode(VipJitxCreateChangeWareReasonEnum.OUT_STOCK.getCode())
                .reasonRemark(VipJitxCreateChangeWareReasonEnum.OUT_STOCK.getName())
                .failNumber(0)
                .ipAddress(operateUser.getLastloginip())
                .build();
        BaseModelUtil.makeBaseCreateField(ocBJitxModifyWarehouseLog, operateUser);
        ocBJitxModifyWarehouseLog.setIsactive(IsActiveEnum.Y.getKey());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("创建JITX订单改仓日志表:{}"), ocBJitxModifyWarehouseLog);
        }
        try {
            List<Long> idList = null;
            List<OcBJitxModifyWarehouseLog> existList = existOrderLog(ocBOrder.getId());
            if (CollectionUtils.isNotEmpty(existList)) {
                idList = existList.stream().map(OcBJitxModifyWarehouseLog::getId).collect(Collectors.toList());
            }
            ValueHolderV14 v14 = ApplicationContextHandle.getBean(OcBJitxModifyWarehouseLogService.class)
                    .voidOriginalAndAdd(ocBJitxModifyWarehouseLog, isByYY, idList, ocBOrder, operateUser);

            //新增成功放入redis
            if (v14.isOK()) {
                updateOrderInfoService.setJITXRedisChangeWarehouseFlag(ocBOrder, operateUser);
            }

            return v14;
        } catch (Exception e) {
            log.info(LogUtil.format("根据订单信息生成改仓日志表:{}, 原因:{}"), ocBJitxModifyWarehouseLog,
                    Throwables.getStackTraceAsString(e));
            return new ValueHolderV14(ResultCode.FAIL, String.format("创建JITX订单改仓日志失败, 错误问题:%s", e.getMessage()));
        }

    }

    private String getVopWarehouse(OcBOrder ocBOrder, Long warehouseId) {
        StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseService.queryJitxCapacity(
                ocBOrder.getCpCShopId(), warehouseId, null);
        String oldDeliveryWarehouse = null;
        if (jitxWarehouse != null) {
            oldDeliveryWarehouse = jitxWarehouse.getVipcomWarehouseEcode();
            if (!YesNoEnum.Y.getVal().equals(ocBOrder.getIsStoreDelivery())) {
                oldDeliveryWarehouse = jitxWarehouse.getVipcomUnshopWarehouseEcode();
            }
        }
        return oldDeliveryWarehouse;
    }

    public void writeVopWarehouse(String tid, String storeCode, String jitxWarehouse) {
        log.info("{},YY寻仓反馈更新改仓中间表数据开始：tid:{}", this.getClass().getSimpleName(), tid);

        if (ObjectUtils.isEmpty(storeCode) || ObjectUtils.isEmpty(jitxWarehouse)) {
            return;
        }

        QueryWrapper<OcBJitxModifyWarehouseLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("source_code", tid);
        queryWrapper.eq("ISACTIVE", "Y");
//        queryWrapper.isNull("NEW_DELIVERY_WAREHOUSE");
//        queryWrapper.and(o -> {
//            o.isNull("WORKFLOW_STATE").or().in("WORKFLOW_STATE",
//                    Lists.newArrayList(VipJitxWorkflowStateEnum.CREATE.getKey(), VipJitxWorkflowStateEnum.DOING.getKey()));
//            return o;
//        });
        List<OcBJitxModifyWarehouseLog> ocBJitxModifyWarehouseLogs = ocBJitxModifyWarehouseLogMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(ocBJitxModifyWarehouseLogs)) {
            ocBJitxModifyWarehouseLogs = ocBJitxModifyWarehouseLogs.stream()
                    .filter(log -> ObjectUtils.isEmpty(log.getNewDeliveryWarehouse())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ocBJitxModifyWarehouseLogs)) {
                return;
            }

            OcBJitxModifyWarehouseLog warehouseLog = ocBJitxModifyWarehouseLogs.get(0);
            OcBOrder ocBOrder = ocBOrderMapper.selectByID(warehouseLog.getOrderId());
            boolean jitxDealerYYWarehouse = ocBJitxDealerOrderTaskService.isJitxDealerYYWarehouse(ocBOrder.getCpCPhyWarehouseId());
            if (!jitxDealerYYWarehouse) {
                return;
            }

            if (jitxWarehouse.equals(warehouseLog.getOldDeliveryWarehouse())) {
                //仓库一致一致，作废改仓工单,并去掉零售发货单改仓标
                String redisKey = BllRedisKeyResources.getJitxChangeWarehouseFlagKey(ocBOrder.getId());
                CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
                objRedisTemplate.delete(redisKey);
                OcBJitxModifyWarehouseLog update = OcBJitxModifyWarehouseLog.builder()
                        .id(warehouseLog.getId()).build();
                update.setIsactive(R3CommonResultConstants.VALUE_N);
                update.setModifieddate(new Date());
                User user = SystemUserResource.getRootUser();
                update.setModifierid(SystemUserResource.ROOT_USER_ID);
                ocBJitxModifyWarehouseLogMapper.updateById(update);

                OcBOrder orderUpdate = new OcBOrder();
                orderUpdate.setId(ocBOrder.getId());
                orderUpdate.setModifiername(user.getName());
                orderUpdate.setModifierename(user.getEname());
                orderUpdate.setIsVipUpdateWarehouse(YesNoEnum.N.getVal());
                orderUpdate.setModifierid(Long.valueOf(user.getId()));
                orderUpdate.setModifieddate(new Date());
                ocBOrderMapper.updateById(orderUpdate);

            } else {
                //更新改仓中间表仓库
                OcBJitxModifyWarehouseLog update = OcBJitxModifyWarehouseLog.builder()
                        .id(warehouseLog.getId())
                        .afterChangeWarehouseId(ocBOrder.getCpCPhyWarehouseId())
                        .afterChangeWarehouseCode(ocBOrder.getCpCPhyWarehouseEcode())
                        .afterChangeWarehouseEname(ocBOrder.getCpCPhyWarehouseEname())
                        .newDeliveryWarehouse(jitxWarehouse)
                        .build();
                List<CpCStore> stores = cpRpcService.selectStoresByCodes(Collections.singletonList(storeCode));
                if (CollectionUtils.isNotEmpty(stores)) {
                    update.setAfterChangeWarehouseId(stores.get(0).getCpCPhyWarehouseId());
                    update.setAfterChangeWarehouseCode(stores.get(0).getCpCPhyWarehouseEcode());
                    update.setAfterChangeWarehouseEname(stores.get(0).getCpCPhyWarehouseEname());
                }

                ocBJitxModifyWarehouseLogMapper.updateById(update);
            }

        }
    }

    /**
     * 作废历史JITX订单改仓日志，同时新增新的日志
     *
     * @param ocBJitxModifyWarehouseLog 新的日志
     * @param idList                    历史日志ID
     * @param operateUser               操作人
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 voidOriginalAndAdd(OcBJitxModifyWarehouseLog ocBJitxModifyWarehouseLog, boolean isByYY,
                                             List<Long> idList, OcBOrder order, User operateUser) {
        if (CollectionUtils.isNotEmpty(idList)) {
            // 作废历史改仓日志
            QueryWrapper<OcBJitxModifyWarehouseLog> updateWrapper = new QueryWrapper<>();
            updateWrapper.in("ID", idList);
            OcBJitxModifyWarehouseLog updateModel = new OcBJitxModifyWarehouseLog();
            BaseModelUtil.makeBaseModifyField(updateModel, operateUser);
            updateModel.setIsactive("N");
            if (ocBJitxModifyWarehouseLogMapper.update(updateModel, updateWrapper) <= 0) {
                log.info(LogUtil.multiFormat("作废历史JITX订单改仓中间表失败, idList:{}", idList));
                return new ValueHolderV14(ResultCode.FAIL, "作废历史JITX订单改仓中间表失败");
            }
        }
        int row = ocBJitxModifyWarehouseLogMapper.insert(ocBJitxModifyWarehouseLog);
        if (row <= 0) {
            log.info(LogUtil.format("创建JITX订单改仓中间表失败, ocBJitxModifyWarehouseLog:{}"), ocBJitxModifyWarehouseLog);
            throw new NDSException("新增失败");
        }
        if (!isByYY) {
            //如果换仓不是YY申请的，通知YY仓库处理释放原仓，重新占用
            ocBJitxDealerOrderTaskService.handelChangeWarehouse(ocBJitxModifyWarehouseLog, order, operateUser);
        }
        return new ValueHolderV14(ResultCode.SUCCESS, "创建成功");
    }
}
