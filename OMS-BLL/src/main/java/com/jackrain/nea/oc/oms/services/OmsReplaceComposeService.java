package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.SelectOrderUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/2/25 10:25 上午
 * @Version 1.0
 * 替换组合商品的处理类
 */
@Slf4j
@Component
public class OmsReplaceComposeService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;
    @Autowired
    private OmsOrderRecountAmountService omsOrderRecountAmountService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsReplaceComposeService omsReplaceComposeService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private SelectOrderUtil selectOrderUtil;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private BatchOperationGoodsService batchOperationGoodsService;
    //组合商品标识前缀
    private static final String GROUP_GOODS_MARK = "CG";


    public ValueHolderV14 replaceComposePro(JSONObject object, User user) {
        //获取传入的参数
        ValueHolderV14 holderV14 = new ValueHolderV14();
        //订单id
        Long id = object.getLong("id");
        //所选择的明细id
        Long itemId = object.getLong("itemId");
        //所替换商品的条码
        String skuEcode = object.getString("skuEcode");
        //校验参数是否合法
        //根据选择的明细查询数据
        OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
        OcBOrderItem ocBOrderItem = ocBOrderItemMapper.queryOrderItemByIdPro(itemId, id);
        this.checkParameter(ocBOrderItem, ocBOrder);
        //只需取一条数据 如果商品类型为2 则找到对应的商品类型为4的原始数据
        List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
        //todo true 为组合替换组合  false 为组合替换成正常
        boolean flag = true;
        if (ocBOrderItem != null) {
            //根据ooid 查询明细是否对应多个订单
            /*if (selectOrderUtil.selectOrder(ocBOrderItem.getOoid())) {
                throw new NDSException("被替换商品已拆分,不允许更换");
            }*/
            if (ocBOrderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                //查出当前订单下已拆分完的数据 后面备用
                ocBOrderItems = ocBOrderItemMapper.selectOrderItemByBagSku(id, ocBOrderItem.getPsCSkuEcode());
            } else {
                flag = false;
            }
        } else {
            throw new NDSException("未查到商品信息");
        }
        this.getOcBOrderItems(skuEcode, ocBOrder, ocBOrderItem, ocBOrderItems, user, flag);
        holderV14.setCode(0);
        holderV14.setMessage("替换组合商品成功!");
        return holderV14;
    }


    public void getOcBOrderItems(String skuEcode, OcBOrder ocBOrder, OcBOrderItem item,
                                 List<OcBOrderItem> deleteItems, User user, boolean flag) {

        ProductSku productSku = psRpcService.selectProductSku(skuEcode);
        if (productSku == null) {
            throw new NDSException("所选替换商品不存在!请重新选择!");
        }
        // 没更改的数据
        OcBOrderItem bOrderItem = new OcBOrderItem();
        BeanUtils.copyProperties(item, bOrderItem);
        //正常商品换组合商品
        item.setId(item.getId());
        item.setPsCSkuEcode(skuEcode);
        item.setQty(item.getQty());
        item.setBarcode(productSku.getBarcode69());
        item.setPsCProId(productSku.getProdId());
        // ProECode
        item.setPsCProEname(productSku.getName());
        item.setPsCSkuEname(productSku.getSkuName());
        item.setPsCProEcode(productSku.getProdCode());
        item.setPsCSkuId(productSku.getId());
        item.setSex(productSku.getSex());
        item.setPriceTag(productSku.getPricelist());
        item.setPsCClrEcode(productSku.getColorCode());
        item.setPsCClrEname(productSku.getColorName());
        item.setPsCClrId(productSku.getColorId());
        item.setPsCSizeEcode(productSku.getSizeCode());
        item.setPsCSizeEname(productSku.getSizeName());
        item.setPsCSizeId(productSku.getSizeId());
        item.setPsCProMaterieltype(productSku.getMaterialType());
        item.setStandardWeight(productSku.getWeight());
        item.setSkuSpec(productSku.getSkuSpec());
        item.setOcBOrderId(item.getOcBOrderId());
        item.setPriceTag(productSku.getPricelist());
        item.setIsManualAdd("1");
        // omsOrderItemService.updateOcBOrderItem(orderItem, item.getOcBOrderId());
        // 判断商品是否为组合商品
        List<OcBOrderItem> orderItems = new ArrayList<>();
        List<OcBOrderItem> orderItem = new ArrayList<>();
        if (productSku.getSkuType() == SkuType.COMBINE_PRODUCT || productSku.getSkuType() == SkuType.GIFT_PRODUCT) {
            // 组合替换成组合
            item.setQtyLost(BigDecimal.ZERO);
            item.setProType((long) SkuType.NO_SPLIT_COMBINE);
            item.setGroupGoodsMark(GROUP_GOODS_MARK + item.getId());
            item.setGiftbagSku(skuEcode);
            orderItems.add(item);
            orderItem = omsConstituteSplitService.encapsulationParameter(orderItems, ocBOrder, user, 0);
            orderItem.add(item);

        } else if (productSku.getSkuType() == SkuType.NORMAL_PRODUCT) {
            // 组合商品换正常商品
            item.setProType((long) SkuType.NORMAL_PRODUCT);
            item.setGiftbagSku("");
            item.setGroupGoodsMark("");
            orderItem.add(item);
        }
        if (CollectionUtils.isEmpty(orderItem)) {
            throw new NDSException("数据异常!替换组合商品失败!");
        }
        omsReplaceComposeService.handleData(ocBOrder, orderItem, deleteItems, user, bOrderItem);
    }

    /**
     * 检验传入参数是否合法
     */
    private void checkParameter(OcBOrderItem orderItem, OcBOrder ocBOrder) {
        Integer orderStatus = ocBOrder.getOrderStatus();
        if (!(OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus))) {
            throw new NDSException("状态不是待审核或者缺货,不允许替换商品!");
        }
    }

    /**
     * @param ocBOrder    订单主表
     * @param orderItems  需要增加的数据
     * @param deleteItems 需要删除的数据
     * @param user
     * @param item        选择的替换的数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleData(OcBOrder ocBOrder, List<OcBOrderItem> orderItems, List<OcBOrderItem> deleteItems,
                           User user, OcBOrderItem item) {
        Long id = ocBOrder.getId();
        if (CollectionUtils.isNotEmpty(deleteItems)) {
            for (OcBOrderItem orderItem : deleteItems) {
                ocBOrderItemMapper.deleteByItemId(orderItem.getId(), id);
            }
        }
        String skuCode = "";
        for (OcBOrderItem orderItem : orderItems) {
            if (StringUtils.isBlank(skuCode)) {
                skuCode = orderItem.getPsCSkuEcode();
            }
            orderItem.setPsCSkuPtEcode(null);
            if (orderItem.getProType() == SkuType.NO_SPLIT_COMBINE || orderItem.getProType() == SkuType.NORMAL_PRODUCT) {
                omsOrderItemService.updateOcBOrderItem(orderItem, ocBOrder.getId());
            } else {
                ocBOrderItemMapper.insert(orderItem);
            }
        }
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.GOODS_REPLACE.getKey(), "将商品条码:" + item.getPsCSkuEcode()
                        + "替换为" + skuCode + "成功:", null, null, user);
        OcBOrderItem release = new OcBOrderItem();
        BeanUtils.copyProperties(orderItems.get(0), release);
        // 把要删除的传过去
        if (CollectionUtils.isEmpty(deleteItems)) {
            deleteItems.add(item);
        }
        batchOperationGoodsService.handleOccupy(ocBOrder, deleteItems, user);
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        List<OcBOrderItem> ocBOrderItems1 = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());

        List<OcBOrderItem> collect = ocBOrderItems1.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            order.setIsCombination(1);
        } else {
            order.setIsCombination(0);
        }
        omsOrderService.updateOrderInfo(order);
    }

    /**
     * 占用库存并清空库存
     */
    private boolean occupiedInventory(OcBOrderRelation relation, User loginUser) {
        ValueHolderV14 sgValueHolder = sgRpcService.querySearchStockAndModifyGoodsInfo(relation, loginUser);
        if (!sgValueHolder.isOK()) {
            this.addLog(relation.getOrderInfo(), loginUser, "占用库存失败");
            return false;
        } else {
            //更新明细的缺货数量
            List<OcBOrderItem> orderItemList = relation.getOrderItemList();
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                OcBOrderItem item = new OcBOrderItem();
                item.setId(ocBOrderItem.getId());
                item.setOcBOrderId(ocBOrderItem.getOcBOrderId());
                item.setQtyLost(ocBOrderItem.getQty());
                omsOrderItemService.updateOcBOrderItem(item, ocBOrderItem.getOcBOrderId());
                this.addLog(relation.getOrderInfo(), loginUser, "条码:" + ocBOrderItem.getPsCSkuEcode() + "占用库存成功");

            }
            return true;
        }

    }

    private void addLog(OcBOrder ocBOrder, User user, String content) {
        try {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_OCCUPY_SUCCESS.getKey(), content, "", "", user);
        } catch (Exception ex) {
        }
    }


    private void emptyLogic(OcBOrder ocBOrder, User user) {
        ValueHolderV14 sgValueHolder = sgRpcService.cleanSgLogicalShipment(ocBOrder, user);
        if (sgValueHolder.getCode() == -1) {
            throw new NDSException("清空逻辑发货单失败");
        } else {
            //将订单还原
            ocBOrderMapper.updateOrderEmptyWarehouse(ocBOrder.getId());
        }
    }

}
