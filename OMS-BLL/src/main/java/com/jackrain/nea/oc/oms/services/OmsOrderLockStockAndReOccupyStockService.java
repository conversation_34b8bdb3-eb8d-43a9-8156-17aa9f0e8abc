package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNoticesItem;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutNoticesPosSaveRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderSplitRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.*;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * description：实缺订单冻结并重新寻源以及拆分服务
 *
 * <AUTHOR>
 * @date 2021/11/24
 */
@Component
@Slf4j
public class OmsOrderLockStockAndReOccupyStockService {

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private IpJitxOrderService jitxOrderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OrderAmountUtil orderAmountUtil;

    @Autowired
    private AgainOccupyStockService againOccupyStockService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;



    public ValueHolderV14 lockStockAndReOccupyStock(String param, User user) {
        log.info("{}.lockStockAndReOccupyStock start param:{}", this.getClass().getSimpleName(), param);
        JSONObject jsonObject = JSONObject.parseObject(param);
        JSONArray jsonArray = jsonObject.getJSONArray("ids");
        if (jsonArray == null || jsonArray.isEmpty()) {
            return ValueHolderV14Utils.getFailValueHolder("请选择数据");
        }
        List<Long> idList = new ArrayList<>(jsonArray.size());
        for (int i = 0; i < jsonArray.size(); i++) {
            if (jsonArray.getLong(i) != null) {
                idList.add(jsonArray.getLong(i));
            }
        }
        if (CollectionUtils.isEmpty(idList)) {
            return ValueHolderV14Utils.getFailValueHolder("参数有误");
        }
        int fail = 0;
        OmsOrderLockStockAndReOccupyStockService service = ApplicationContextHandle
                .getBean(OmsOrderLockStockAndReOccupyStockService.class);
        if (idList.size() == 1) {
            ValueHolderV14 v14 = service.doFunction(idList.get(0), user);
            return v14;
        } else {
            for (int i = 0; i < idList.size(); i++) {
                try {

                    ValueHolderV14 v14 = service.doFunction(idList.get(i), user);
                    if (!v14.isOK()) {
                        fail++;
                    }
                } catch (Exception e) {
                    fail++;
                }
            }
            return ValueHolderV14Utils.getSuccessValueHolder("冻结并重新寻源选择" + idList.size() + "条," + "成功" + (idList.size() - fail) + "条," + "失败" + fail + "条");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 doFunction(Long id, User user) {
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(id);
                List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectUnSuccessRefund(id);
                if (ocBOrder == null) {
                    return ValueHolderV14Utils.getFailValueHolder("订单不存在");
                }
                if (CollectionUtils.isEmpty(orderItemList)) {
                    return ValueHolderV14Utils.getFailValueHolder("订单不存在有效明细");
                }
                if (!omsBusinessTypeStService.isAutoOccupy(ocBOrder)) {
                    throw new NDSException("该业务类型订单未开启自动寻源");
                }
                SgBStoOutNoticesPosSaveRequest request = this.setParamData(user, ocBOrder, orderItemList);
                ValueHolderV14 v14 = this.splitOrder(id, user);
                if (v14.isOK()) {
                    ValueHolderV14 valueHolderV14 = sgRpcService.freezeAndSubmit(request);
                    AssertUtil.assertException(!valueHolderV14.isOK(),valueHolderV14.getMessage());
                    return valueHolderV14;
                } else {
                    throw new NDSException(v14.getMessage());
                }
            } else {
                log.error("订单id:{}冻结并重新寻源失败,当前订单其他人在操作，请稍后再试", id);
                return ValueHolderV14Utils.getFailValueHolder("当前订单其他人在操作，请稍后再试");
            }
        } catch (Exception ex) {
            log.error("{},订单id:{},异常{}", this.getClass().getSimpleName(), id, Throwables.getStackTraceAsString(ex));
            throw new NDSException(ex.getMessage());
        } finally {
            redisLock.unlock();
        }
    }

    private SgBStoOutNoticesPosSaveRequest setParamData(User user, OcBOrder ocBOrder, List<OcBOrderItem> orderItemList) {
        SgBStoOutNoticesPosSaveRequest billSaveRequest = new SgBStoOutNoticesPosSaveRequest();
        SgBStoOutNotices noticesSaveRequest = new SgBStoOutNotices();
        noticesSaveRequest.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId()); //实体仓id
        noticesSaveRequest.setCpCPhyWarehouseEcode(ocBOrder.getCpCPhyWarehouseEcode()); //实体仓编码
        noticesSaveRequest.setCpCPhyWarehouseEname(ocBOrder.getCpCPhyWarehouseEname()); //实体仓名称
        noticesSaveRequest.setPayTime(ocBOrder.getPayTime());
        noticesSaveRequest.setBillDate(ocBOrder.getOrderDate());
        noticesSaveRequest.setOutType(SgStoreConstantsIF.OUT_TYPE_ELECTRICITY); //出库类型
        noticesSaveRequest.setCpCCustomerId(ocBOrder.getCpCCustomerId());
        noticesSaveRequest.setSourceBillId(ocBOrder.getId());
        noticesSaveRequest.setSourceBillNo(ocBOrder.getBillNo());
        noticesSaveRequest.setSourcecode(ocBOrder.getSourceCode());
        //收货人信息加密
        noticesSaveRequest.setOaid(ocBOrder.getOaid());
        noticesSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL); //单据类型

        //斯凯奇 1. 生成出库通知单时，若该订单为JITX订单且为合单，则取零售发货单中
        //的"JITX发货平台单号"更新至出库通知单中的"平台单号"中
        if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
            if (YesNoEnum.Y.getVal().equals(ocBOrder.getIsMerge())) {
                noticesSaveRequest.setSourcecode(ocBOrder.getJitxMergedDeliverySn());
            }
        }
        noticesSaveRequest.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        noticesSaveRequest.setCpCShopId(ocBOrder.getCpCShopId()); //店铺id
        noticesSaveRequest.setReceiverMobile(StringUtils.isNotBlank(ocBOrder.getReceiverMobile()) ? ocBOrder.getReceiverMobile() : ocBOrder.getReceiverPhone());
        noticesSaveRequest.setCpCRegionProvinceEname(ocBOrder.getCpCRegionProvinceEname());
        noticesSaveRequest.setCpCRegionProvinceId(ocBOrder.getCpCRegionProvinceId());
        noticesSaveRequest.setCpCRegionProvinceEcode(ocBOrder.getCpCRegionProvinceEcode());
        noticesSaveRequest.setCpCRegionCityEname(ocBOrder.getCpCRegionCityEname());
        noticesSaveRequest.setCpCRegionCityId(ocBOrder.getCpCRegionCityId());
        noticesSaveRequest.setCpCRegionCityEcode(ocBOrder.getCpCRegionCityEcode());
        noticesSaveRequest.setCpCRegionAreaEname(ocBOrder.getCpCRegionAreaEname());
        noticesSaveRequest.setCpCRegionAreaEcode(ocBOrder.getCpCRegionAreaEcode());
        noticesSaveRequest.setCpCRegionAreaId(ocBOrder.getCpCRegionAreaId());
        noticesSaveRequest.setReceiver(ocBOrder.getReceiverName());
        noticesSaveRequest.setReceiverAddress(ocBOrder.getReceiverAddress());
        noticesSaveRequest.setReceiverName(ocBOrder.getReceiverName());
        noticesSaveRequest.setReceiverPhone(StringUtils.isNotBlank(ocBOrder.getReceiverPhone()) ? ocBOrder.getReceiverPhone() : ocBOrder.getReceiverMobile());
        noticesSaveRequest.setReceiverZip(ocBOrder.getReceiverZip());
        noticesSaveRequest.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
        noticesSaveRequest.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
        noticesSaveRequest.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
        noticesSaveRequest.setLogisticNumber(ocBOrder.getExpresscode());
        noticesSaveRequest.setBuyerRemark(ocBOrder.getBuyerMessage());
        noticesSaveRequest.setSellerRemark(ocBOrder.getSellerMemo());
        noticesSaveRequest.setRemark(ocBOrder.getSysremark());
        noticesSaveRequest.setCpCPlatformId(Long.valueOf(ocBOrder.getPlatform())); //平台
        noticesSaveRequest.setSplitType(ocBOrder.getSplitReason());
        List<SgBStoOutNoticesItem> outNoticesItemRequests = new ArrayList<>();
        orderItemList.forEach(p -> {
            SgBStoOutNoticesItem noticesItem = new SgBStoOutNoticesItem();
            noticesItem.setSourceBillItemId(p.getId());
            noticesItem.setQty(p.getQty());
            noticesItem.setPsCProId(p.getPsCProId()); //"商品ID"
            noticesItem.setPsCProEcode(p.getPsCProEcode()); //"商品编码"
            noticesItem.setPsCProEname(p.getPsCProEname()); //"商品名称"
            noticesItem.setPsCSkuId(p.getPsCSkuId()); //"skuID"
            noticesItem.setPsCSkuEcode(p.getPsCSkuEcode()); //"sku编码"
            noticesItem.setPsCSpec1Id(p.getPsCClrId()); //"规格1为颜色"
            noticesItem.setPsCSpec1Ecode(p.getPsCClrEcode());
            noticesItem.setPsCSpec1Ename(p.getPsCClrEname());
            noticesItem.setPsCSpec2Id(p.getPsCSizeId());   //规格2为尺寸"
            noticesItem.setPsCSpec2Ecode(p.getPsCSizeEcode());
            noticesItem.setPsCSpec2Ename(p.getPsCSizeEname());
            noticesItem.setGbcode(p.getBarcode()); //国标码
            noticesItem.setPriceList(p.getPriceList() == null ? p.getPriceTag() : p.getPriceList()); //吊牌价

            outNoticesItemRequests.add(noticesItem);
        });
        billSaveRequest.setStoOutNotices(noticesSaveRequest);
        billSaveRequest.setNoticesItems(outNoticesItemRequests);
        billSaveRequest.setLoginUser(user);
        return billSaveRequest;
    }

    /**
     * description：拆分订单 一单一件
     *
     * <AUTHOR>
     * @date 2021/11/26
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 splitOrder(Long orderId, User user) {
        log.info("{},实缺订单执行拆分开始,orderId:{}", this.getClass().getSimpleName(), orderId);
        Long startTime = System.currentTimeMillis();
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
        List<OcBOrderItem> itemList = ocBOrderItemMapper.selectUnSuccessRefund(orderId);

        if (ocBOrder == null) {
            log.error("{} 订单ID：{} 订单不存在", this.getClass().getSimpleName(), orderId);
            return ValueHolderV14Utils.getFailValueHolder("订单不存在！");
        }

        if (CollectionUtils.isEmpty(itemList)) {
            return ValueHolderV14Utils.getFailValueHolder("订单不存在有效明细！");
        }

        if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
            return ValueHolderV14Utils.getFailValueHolder("订单状态非待审核！");
        }
        ocBOrderMapper.updateWarehouse(orderId);
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.LOCK_STOCK_THEN_REOCCUPY.getKey(), OrderLogTypeEnum.LOCK_STOCK_THEN_REOCCUPY.getName(), null, null, user);
        if (!PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
            //加入占单表
            omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null, 0);
            return ValueHolderV14Utils.getSuccessValueHolder("不是JITX订单,无需拆单！");
        }

        if (StringUtils.isEmpty(ocBOrder.getMergedCode())) {
            //加入占单表
            omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null, 0);
            return ValueHolderV14Utils.getSuccessValueHolder("当前订单没有合包码,无需拆单！");
        }
        if (ocBOrder.getQtyAll().compareTo(BigDecimal.ONE) <= 0) {
            //加入占单表
            omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null, 0);
            return ValueHolderV14Utils.getSuccessValueHolder("当前订单数量小于1,无需拆单！");
        }

        if (itemList.size() < 2) {
            //加入占单表
            omsOccupyTaskService.addOcBOccupyTask(ocBOrder,null, 0);
            return ValueHolderV14Utils.getSuccessValueHolder("当前订单明细数量小于2,无需拆单！");
        }
        OcBOrderSplitRelation splitRelation = new OcBOrderSplitRelation();
        splitRelation.setOrderInfo(ocBOrder);
        splitRelation.setOrderItemList(itemList);
        splitRelation.setUser(user);
        splitRelation.setOriginOrderId(orderId);
        splitRelation.setLogType(OrderLogTypeEnum.JITX_OUT_STOCK_SPLIT.getKey());
        splitRelation.setSplitReason(SplitReason.SPLIT_WAREHOUSE_STOCK);

        return this.splitOrigOrder(splitRelation, 0);
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 splitOrigOrder(OcBOrderSplitRelation splitRelation, Integer isAutoOccupy) {
        Long orderId = splitRelation.getOriginOrderId();
        log.info("{}.splitOrigOrder start original order id:{}", this.getClass().getSimpleName(), orderId);
        User user = splitRelation.getUser();
        OcBOrder ocBOrder = splitRelation.getOrderInfo();
        List<OcBOrderItem> itemList = splitRelation.getOrderItemList();
        String logType = splitRelation.getLogType();

        List<String> tidList = itemList.stream().filter(x -> StringUtils.isNotEmpty(x.getTid())).map(OcBOrderItem::getTid).distinct().collect(Collectors.toList());
        List<IpBJitxOrder> ipBJitxOrders = jitxOrderService.queryAuditOrder(tidList);
        Map<String, List<IpBJitxOrder>> jitxOrderMap = ipBJitxOrders.stream().collect(Collectors.groupingBy(IpBJitxOrder::getOrderSn));
        List<OcBOrderRelation> orderRelationList = new ArrayList<>(itemList.size());
        List<Long> newOrderIds = new ArrayList<>(itemList.size());
        for (OcBOrderItem item : itemList) {
            List<IpBJitxOrder> jitxOrders = jitxOrderMap.get(item.getTid());
            if (CollectionUtils.isNotEmpty(jitxOrders)) {
                OcBOrderRelation relation = new OcBOrderRelation();
                OcBOrder newOrder = this.assemblyOrder(ocBOrder, user, splitRelation.getSplitReason());
                IpBJitxOrder ipBJitxOrder = jitxOrders.get(0);
                //重置主表字段
                newOrder.setTid(ipBJitxOrder.getOrderSn());
                newOrder.setSourceCode(ipBJitxOrder.getOrderSn());
                newOrder.setJitxMergedDeliverySn(ipBJitxOrder.getOrderSn());
                newOrder.setExpresscode(ipBJitxOrder.getTransportNo());
                //重置明细字段
                item.setId(sequenceUtil.buildOrderItemSequenceId());
                item.setOcBOrderId(newOrder.getId());
                item.setQtyReturnApply(BigDecimal.ZERO);
                BaseModelUtil.makeBaseCreateField(item, user);

                relation.setOrderInfo(newOrder);
                relation.setOrderItemList(Lists.newArrayList(item));
                OcBOrder recountOrderAmountOrder = orderAmountUtil.recountOrderAmount(relation);
                relation.setOrderInfo(recountOrderAmountOrder);
                //打标签
                OrderTagUtil.orderTags(relation);
                orderRelationList.add(relation);
                newOrderIds.add(newOrder.getId());
            } else {
                log.error("{},未查询到已审核的JITX订单,orderSn:{}", this.getClass().getSimpleName(), item.getTid());
                if (SplitReason.SPLIT_REDELIVERY.equals(splitRelation.getSplitReason())) {
                    throw new NDSException("JITX订单平台已取消,不允许补发");
                }
            }
        }
        List<OcBOrderLog> logs = new ArrayList<>(orderRelationList.size() + 1);
        for (OcBOrderRelation relation : orderRelationList) {
            OcBOrder orderInfo = relation.getOrderInfo();
            //置为待分配
            orderInfo.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            orderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_0);
            // 落库订单信息
            omsOrderService.saveOrderInfo(orderInfo);
            //加入占单表
            omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null, isAutoOccupy);
            omsOrderItemService.saveOcBOrderItem(relation.getOrderItemList().get(0), relation.getOrderId());
            try {
                String formatMsg = String.format(OrderLogTypeEnum.enumToStringBykey(logType) + "自动拆单,原订单ID：%d", orderId);
                if (SplitReason.SPLIT_REDELIVERY.equals(splitRelation.getSplitReason())) {
                    formatMsg = String.format("操作门店发货异常补发功能生成该笔订单,原订单ID：%d", orderId);
                }
                OcBOrderLog ocBOrderLog = omsOrderLogService.getOcBOrderLog(orderInfo.getId(), orderInfo.getBillNo(), logType, formatMsg, null, null, user);
                logs.add(ocBOrderLog);
            } catch (Exception e) {
                log.error("记录日志信息异常：{}", Throwables.getStackTraceAsString(e));
            }
        }

        if (CollectionUtils.isNotEmpty(orderRelationList)) {
            if (!SplitReason.SPLIT_REDELIVERY.equals(splitRelation.getSplitReason())) {
                ocBOrderMapper.updateList(Lists.newArrayList(orderId));
                OcBOrderLog ocBOrderLog = omsOrderLogService.getOcBOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), logType, String.format("实缺拆单作废原单,拆分后的订单ID：%s", StringUtils.join(newOrderIds, ",")), null, null, user);
                logs.add(ocBOrderLog);
            } else {
                OcBOrderLog ocBOrderLog = omsOrderLogService.getOcBOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), logType, String.format("jitx发货异常补发进行复制拆分订单,拆分后的订单ID：%s", StringUtils.join(newOrderIds, ",")), null, null, user);
                logs.add(ocBOrderLog);
            }
        }
        if (CollectionUtils.isNotEmpty(logs)) {
            omsOrderLogService.save(logs);
        }
        return ValueHolderV14Utils.getSuccessValueHolder("拆单成功");
    }

    /**
     * 组装订单
     */
    public OcBOrder assemblyOrder(OcBOrder origOrder, User user, Integer splitReason) {
        log.info("{},assemblyOrder start", this.getClass().getSimpleName());
        OcBOrder newOrder = new OcBOrder();
        BeanUtils.copyProperties(origOrder, newOrder);
        Date now = new Date();
        BigDecimal zero = BigDecimal.ZERO;
        // 重新生成Id
        newOrder.setId(sequenceUtil.buildOrderSequenceId());
        //如果实体仓是o2o仓库，对订单进行打标
        CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(origOrder.getCpCPhyWarehouseId());
        if (!ObjectUtils.isEmpty(cpCPhyWarehouse) && StringUtils.equals(cpCPhyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
            newOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
        } else {
            newOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
        }
        // 订单补充信息 原订单ID-SP-splitIndex
        newOrder.setSuffixInfo(origOrder.getId() + "-SP" + 1);
        // 订单编号
        newOrder.setBillNo(sequenceUtil.buildBillNo());
        // 是否拆分原单
        newOrder.setIsSplit(1);
        newOrder.setQtyAll(BigDecimal.ONE);
        // 拆单原因
        newOrder.setSplitReason(splitReason);
        // 拆分原订单号
        newOrder.setSplitOrderId(origOrder.getId());
        // 平台单号
        newOrder.setSourceCode(origOrder.getSourceCode());
        //oaid
        newOrder.setOaid(origOrder.getOaid());
        // 配送费用复制
        newOrder.setShipAmt(zero);
        // 服务费复制
        newOrder.setServiceAmt(zero);
        // 商品金额
        newOrder.setProductAmt(zero);
        // 调整金额
        newOrder.setAdjustAmt(zero);
        // 订单优惠金额
        newOrder.setOrderDiscountAmt(zero);
        // 商品优惠金额
        newOrder.setProductDiscountAmt(zero);
        // 订单总额
        newOrder.setOrderAmt(zero);
        // 应收金额
        newOrder.setAmtReceive(zero);
        // 已收金额
        newOrder.setReceivedAmt(zero);
        // 系统备注
        newOrder.setSysremark(String.format("拆单生成新订单,原单：%s", origOrder.getBillNo()));

        BaseModelUtil.makeBaseCreateField(newOrder, user);
        return newOrder;
    }
}
