package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.hub.enums.OrderStatusEnum;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: 黄世新
 * @Date: 2022/7/13 下午8:04
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsBackExamineOrderService {

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;


    public ValueHolderV14 omsBackExamineOrder(Long orderId, User user){
        ValueHolderV14 holder = new ValueHolderV14();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(0, TimeUnit.MICROSECONDS)) {
                OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                if (ocBOrder == null) {
                    holder.setCode(-1);
                    holder.setMessage("不存在零售发货单");
                    return holder;
                }
                holder = handleOrder(ocBOrder, user);
                return holder;
            } else {
                holder.setCode(-1);
                holder.setMessage("当前订单正在被操作,请稍后再试");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("反审核重新寻源异常:{}","反审核重新寻源异常", orderId), Throwables.getStackTraceAsString(e));
            holder.setCode(-1);
            holder.setMessage("程序异常:"+ e.getMessage());
        } finally {
            redisLock.unlock();
        }
        return holder;
    }

    private ValueHolderV14 handleOrder(OcBOrder ocBOrder, User user){
        ValueHolderV14 holder = new ValueHolderV14();
        Integer orderStatus = ocBOrder.getOrderStatus();
        if (!(OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)
                || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)
                || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus))) {
            holder.setCode(-1);
            holder.setMessage("订单状态不满足");
            return holder;
        }
        if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus) || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)) {
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holder, ocBOrder.getId(), LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            if (!isSuccess) {
                holder.setCode(-1);
                holder.setMessage("订单反审核失败");
                return holder;
            }
            ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
        }
        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
            //释放库存
            List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
            ValueHolderV14 sgValueHolder = sgRpcService.voidSgStockOccupy(ocBOrder, orderItems, user);
            if (sgValueHolder.getCode() != 0) {
                holder.setCode(-1);
                holder.setMessage("订单作废逻辑占用失败!");
                return holder;
            }

        }
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        //order.setOccupyStatus(3);
        ocBOrderMapper.updateById(order);
        omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                OrderLogTypeEnum.MATCH_EXPIRY_DATE.getKey(), "订单库存冻结反审核成功", "", "", user);
        holder.setCode(0);
        holder.setMessage("订单库存冻结反审核成功");
        return holder;
    }
}
