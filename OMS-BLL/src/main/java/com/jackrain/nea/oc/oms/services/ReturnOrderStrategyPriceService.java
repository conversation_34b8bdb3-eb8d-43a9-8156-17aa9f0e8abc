package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.CpCShopChannelType;
import com.jackrain.nea.oc.oms.model.enums.OrderWorehouseIsRetry;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.rpc.AcScRpcService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 退单【新增代销退货核算单】服务
 *
 * @date 2019/8/23
 * @author: ming.fz
 */
@Component
@Slf4j
public class ReturnOrderStrategyPriceService {


    @Autowired
    SgRpcService sgRpcService;


    @Autowired
    OmsOrderLogService omsOrderLogService;


    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OrderStrategyPriceComputeService orderStrategyPriceComputeService;

    @Autowired
    AcScRpcService acScRpcService;

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    private ReturnOrderAuditService returnOrderAuditService;

    @Autowired
    private CpRpcService cpRpcService;

//    public ValueHolderV14 onlineFundOccupy(OcBReturnOrder ocBReturnOrder, Map<Long, SgBReceiveItem> storkMap, User user, Map<Long, BigDecimal> priceSettleMap) {
//        if (log.isDebugEnabled()) {
//            log.debug("代销结算零售退单开始");
//        }
//        Long orderId = ocBReturnOrder.getId();
//        List<OcBReturnOrderRefund> ocBReturnOrderRefunds = ocBReturnOrderRefundMapper.selectByOcOrderId(orderId);
//        if (null == ocBReturnOrderRefunds) {
//            log.error("退单明细为空");
//        }
//        ValueHolderV14 resultvh = new ValueHolderV14();
//        CpShop cpShop = cpRpcService.selectShopById(ocBReturnOrder.getCpCShopId());
//        if (cpShop != null && cpShop.getChannelType().equals(CpCShopChannelType.DISTRIBUTION.getVal())) {
//            if (log.isDebugEnabled()) {
//                log.debug("代销结算零售退单1");
//            }
//            //原始订单
//            OcBOrder order = ocBOrderMapper.selectByID(ocBReturnOrder.getOrigOrderId());
//            //调用 新增代销退货核算单 服务
//            //resultvh = acScRpcService.save(ocBReturnOrder, ocBReturnOrderRefunds, storkMap, user);
//            if (resultvh.getCode() == ResultCode.SUCCESS) {
//                updateOrder(user, ocBReturnOrder, OrderWorehouseIsRetry.YES);
//                if (log.isDebugEnabled()) {
//                    log.debug("代销结算零售退单5");
//                }
//                this.saveReturnOrderLog(user, ocBReturnOrder.getId(), "调用新增代销退货核算单服务成功", "调用新增代销退货核算单服务");
//                //todo 日志记录
//            } else {
//                if (log.isDebugEnabled()) {
//                    log.debug("代销结算零售退单4");
//                }
//                updateOrder(user, ocBReturnOrder, OrderWorehouseIsRetry.NO);
//                if (!StringUtils.isEmpty(resultvh.getMessage())) {
//                    this.saveReturnOrderLog(user, ocBReturnOrder.getId(), "调用新增代销退货核算单服务失败：" + resultvh.getMessage(), "调用新增代销退货核算单服务");
//                } else {
//                    this.saveReturnOrderLog(user, ocBReturnOrder.getId(), "调用新增代销退货核算单服务失败", "调用新增代销退货核算单服务");
//                }
//            }
//        } else {
//            if (log.isDebugEnabled()) {
//                log.debug("代销结算零售退单2");
//            }
//            //非分销店铺直接结束
//            resultvh.setCode(ResultCode.SUCCESS);
//        }
//        if (log.isDebugEnabled()) {
//            log.debug("代销结算零售退单结束");
//        }
//        return resultvh;
//    }

    /**
     * @param user
     */
    private void saveReturnOrderLog(User user, Long ocBReturnOrderId, String logMessage, String logType) {

        returnOrderAuditService.insertReturnOrderLog(ocBReturnOrderId, logType, logMessage, user);
    }


    /**
     * 更新代销资金处理重试标识
     *
     * @param user
     * @param order
     * @return
     */
    private boolean updateOrder(User user, OcBReturnOrder order, OrderWorehouseIsRetry reserveBigint05Status) {
        OcBReturnOrder update = new OcBReturnOrder();
        long status = reserveBigint05Status.getVal();
        if (status != OrderWorehouseIsRetry.YES.getVal()) {
            BigDecimal init = NumUtil.init(order.getQtyFundsFail());
            update.setQtyFundsFail(init.add(BigDecimal.ONE));
        }
        update.setStatusFunds(status);
        update.setId(order.getId());
        update.setModifierename(user.getEname());
        update.setModifiername(user.getName());
        update.setModifieddate(new Date(System.currentTimeMillis()));
        int record = ocBReturnOrderMapper.updateById(update);
        if (record > 0) {
            return true;
        }
        return false;
    }

}