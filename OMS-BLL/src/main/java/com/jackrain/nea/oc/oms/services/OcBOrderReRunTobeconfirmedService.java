package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBToBeConfirmedTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName OcBOrderReRunTobeconfirmedService
 * @Description 待分配
 * <AUTHOR>
 * @Date 2025/5/23 14:28
 * @Version 1.0
 */
@Component
@Slf4j
public class OcBOrderReRunTobeconfirmedService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBToBeConfirmedTaskMapper toBeConfirmedTaskMapper;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    /**
     * 重新执行待分配任务
     * 根据订单ID查询订单，只处理状态为待分配的订单
     * 查询oc_b_tobeconfirmed_task表，如果存在记录则将状态设置为0
     * 如果不存在记录则创建新记录
     *
     * @param ids 订单ID列表
     * @param user 用户信息
     * @return 处理结果
     * @throws NDSException 异常信息
     */
    public ValueHolderV14 reRunTobeconfirmed(List<Long> ids, User user) throws NDSException {
        ValueHolderV14 holder = new ValueHolderV14();

        // 参数校验
        if (CollectionUtils.isEmpty(ids)) {
            holder.setCode(-1);
            holder.setMessage("订单ID列表不能为空");
            return holder;
        }

        try {
            // 查询订单列表
            List<OcBOrder> orderList = ocBOrderMapper.selectOrderListByIdsList(ids);
            if (CollectionUtils.isEmpty(orderList)) {
                holder.setCode(-1);
                holder.setMessage("未找到对应的订单");
                return holder;
            }

            // 筛选待分配状态的订单
            List<Long> validOrderIds = new ArrayList<>();
            for (OcBOrder order : orderList) {
                // 只处理待分配状态的订单（状态码50）
                if (order != null && OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(order.getOrderStatus())) {
                    validOrderIds.add(order.getId());
                }
            }

            if (CollectionUtils.isEmpty(validOrderIds)) {
                holder.setCode(-1);
                holder.setMessage("没有找到待分配状态的订单");
                return holder;
            }

            // 处理每个有效的订单ID
            for (Long orderId : validOrderIds) {
                // 查询是否存在任务记录
                int count = toBeConfirmedTaskMapper.selectCount(
                        new QueryWrapper<OcBToBeConfirmedTask>()
                                .lambda()
                                .eq(OcBToBeConfirmedTask::getOrderId, orderId)
                );

                if (count > 0) {
                    // 存在记录，更新状态为0
                    List<Long> orderIdList = new ArrayList<>();
                    orderIdList.add(orderId);
                    toBeConfirmedTaskService.updateToBeConfirmedTask0(orderIdList);

                    log.info(LogUtil.format("更新订单待分配任务状态为0，订单ID={}", "更新订单待分配任务", orderId));
                } else {
                    // 不存在记录，创建新记录
                    OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                    toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                    toBeConfirmedTask.setOrderId(orderId);
                    toBeConfirmedTask.setCreationdate(new Date());
                    toBeConfirmedTask.setStatus(0);
                    toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
                    log.info(LogUtil.format("创建订单待分配任务，订单ID={}", "创建订单待分配任务", orderId));
                }
            }

            holder.setCode(0);
            holder.setMessage("重新执行待分配任务成功");

        } catch (Exception e) {
            log.error(LogUtil.format("重新执行待分配任务异常，异常信息={}", "重新执行待分配任务异常", Throwables.getStackTraceAsString(e)), e);
            holder.setCode(-1);
            holder.setMessage("重新执行待分配任务异常：" + e.getMessage());
        }

        return holder;
    }
}
