package com.jackrain.nea.oc.oms.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.cp.enums.DrpStoreTypeEnum;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefundType;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWrongReceive;
import com.jackrain.nea.oc.oms.model.enums.OcBReturnAfSendListEnums;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.RefundOrderSourceTypeEnum;
import com.jackrain.nea.oc.oms.services.OcSaveChangingOrRefundingService;
import com.jackrain.nea.oc.oms.services.OmsBusinessTypeDistinguishService;
import com.jackrain.nea.oc.oms.services.OmsBusinessTypeStService;
import com.jackrain.nea.oc.oms.services.OmsRefundOrderService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderConfirmService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.oc.oms.services.calculate.qty.OmsOrderQtyCalculateService;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnAfterUtil;
import com.jackrain.nea.oc.oms.services.returnin.OcReturnInSupport;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/6/24 11:19 上午
 * @Version 1.0
 * <p>
 * 通用退单的转换类
 */
@Slf4j
@Component
public class StandplatRefundOrderTransferUtil {
    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;

    @Autowired
    private OmsRefundOrderService refundOrderService;

    @Autowired
    private BasicCpQueryService basicCpQueryService;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private OcBReturnOrderMapper orderMapper;

    @Autowired
    private OcBReturnOrderRefundMapper orderRefundMapper;

    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;

    @Autowired
    private OmsBusinessTypeDistinguishService omsBusinessTypeDistinguishService;

    @Autowired
    private OmsRefundOrderService omsRefundOrderService;

    @Autowired
    private OmsReturnOrderConfirmService omsReturnOrderConfirmService;

    @Autowired
    private TaobaoRefundOrderTransferUtil taobaoRefundOrderTransferUtil;

    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;

    @Autowired
    private OmsReturnOrderService omsReturnOrderService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBOrderNaiKaMapper orderNaiKaMapper;


    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private IpBStandplatRefundItemMapper refundItemMapper;
    @Autowired
    private OcReturnInSupport returnInService;

    /**
     * 退单主表数据创建
     *
     * @return
     */
    private OcBReturnOrder buildOcBReturnOrderFromStandplatRefund(OmsOrderRelation orderRelation,
                                                                  IpBStandplatRefund ipBStandplatRefund,
                                                                  OcBOrderDelivery ocBOrderDelivery,
                                                                  Integer proType, User user) {
        OcBOrder ocBOrder = orderRelation.getOcBOrder();
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        // 判断订单业务类型 如果是奶卡类 不需要生成退换货单
        String naikaType = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(ocBOrder.getBusinessTypeCode()).getNaiKaType();
        if (StringUtils.isNotBlank(naikaType) && ("entity".equals(naikaType) || "virtual".equals(naikaType))) {
            return null;
        }
        //虚拟订单不生成退换货单
        if (OrderTypeEnum.DIFFPRICE.getVal().equals(ocBOrder.getOrderType())) {
            return null;
        }
        if (ipBStandplatRefund != null) {
            //平台退款单号
            returnOrder.setReturnId(ipBStandplatRefund.getReturnNo());
            //卖家昵称
            returnOrder.setBuyerNick(ipBStandplatRefund.getBuyerNick());
            //申请退款时间
            returnOrder.setReturnCreateTime(ipBStandplatRefund.getCreated());
            //最后修改时间
            returnOrder.setLastUpdateTime(ipBStandplatRefund.getModified());
            //买家备注
            returnOrder.setBuyerRemark(ipBStandplatRefund.getBuyerRemark());
            //货物退回时间
            //returnOrder.setReturnTime(ipBStandplatRefund.getGoodReturnTime());
            //退款说明
            returnOrder.setReturnDesc(ipBStandplatRefund.getReturnReason());
            //商品应退金额(
            returnOrder.setReturnAmtList(ipBStandplatRefund.getRefundAmount());
            //售后/售中
            // returnOrder.setReturnPhase(ipBStandplatRefund.getRefundPhase());
            //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
            returnOrder.setReturnAmtActual(ipBStandplatRefund.getRefundAmount());

            String companyName = ipBStandplatRefund.getCompanyName();
            //退回物流单号
            returnOrder.setLogisticsCode(ipBStandplatRefund.getLogisticsNo());

            returnOrder.setCpCLogisticsEname(companyName);
            //退回说明
            //returnOrder.setReserveVarchar02(ipBStandplatRefund.getRefunddesc());
            this.setLogisticInfo(returnOrder, companyName);

        }
        //下载时间
        //卖家呢城
        // returnOrder.setSellerNick(ocBOrder.getS);
        //物流公司名称
        returnOrder.setCreationdate(new Date());
        returnOrder.setBillNo(sequenceUtil.buildReturnBillNo());
        //等待退货入库(PRD数据对象)
        returnOrder.setReturnStatus(TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode());
        //原始订单编号
        returnOrder.setOrigOrderId(ocBOrder.getId());
        //退还运费，默认0
        returnOrder.setReturnAmtShip(BigDecimal.ZERO);
        //退还其他费用，默认0
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);
        //换货人姓名
        returnOrder.setReceiveName(ocBOrder.getReceiverName());
        //换货人手机
        returnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getOrderSource());
        //邮编
        returnOrder.setReceiveZip(ocBOrder.getReceiverZip());
        //发货仓库
        returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        //平台类型
        returnOrder.setPlatform(ocBOrder.getPlatform());
        // returnOrder.setThirdWarehouseType(ocBOrder.getThirdWarehouseType());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());

        //换货金额
        returnOrder.setExchangeAmt(BigDecimal.ZERO);
        returnOrder.setTid(ocBOrder.getTid());
        //是否传AG默认否
        returnOrder.setIsToag(AGStatusEnum.INIT.getVal());
        //是否生成调拨单，默认0
        returnOrder.setIsTransfer(0);
        //是否生成零售，默认0
        returnOrder.setIsTodrp(0);
        //退单状态，默认20
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //是否手工新增，默认0
        returnOrder.setIsAdd(0);
        //虚拟入库状态，默认0
        returnOrder.setInventedStatus(0);
        //是否原退，默认0
        returnOrder.setIsRefund(0);
        //是否确认收货，默认0
        returnOrder.setIsReceiveConfirm(0);
        //WMS撤回状态，默认0
        returnOrder.setWmsCancelStatus(0);
        //强制入库，默认0
        returnOrder.setIsForce(0);
        //是否手工审核，默认0
        returnOrder.setIsManualAudit(0);
        //是否传WMS
        returnOrder.setIsTowms(0);
        //是否入仓成功
        returnOrder.setIsInstorage(0);
        returnOrder.setOrigSourceCode(ocBOrder.getSourceCode());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());
        //退款原因
        //returnOrder.setRemark(ipBTaobaoRefund.getReason());
        //returnOrder.setReturnReason(ipBTaobaoRefund.getReason());
        returnOrder.setReceiveAddress(ocBOrder.getReceiverAddress());
        //店铺id
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        returnOrder.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        returnOrder.setCpCShopEcode(ocBOrder.getCpCShopEcode());

        returnOrder.setReceiverProvinceName(ocBOrder.getCpCRegionProvinceEname());
        returnOrder.setReceiverCityName(ocBOrder.getCpCRegionCityEname());
        returnOrder.setReceiverAreaName(ocBOrder.getCpCRegionAreaEname());
        returnOrder.setReceiverProvinceId(ocBOrder.getCpCRegionProvinceId());
        returnOrder.setReceiverCityId(ocBOrder.getCpCRegionCityId());
        returnOrder.setReceiverAreaId(ocBOrder.getCpCRegionAreaId());
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        //原单卖家备注
        returnOrder.setOrigSellerRemark(ocBOrder.getSellerMemo());
        //原单买家留言
        returnOrder.setOrigBuyerMessage(ocBOrder.getBuyerMessage());
        //取值为发货实体仓档案中关联的退货待检实体仓仓库
        //this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder,false);
        returnOrder.setReturnProType(proType);
        // 退款类型等于 退货退款时,从中间表取物流信息
        if (Objects.nonNull(ipBStandplatRefund)
                && Objects.equals(ipBStandplatRefund.getRefundType(), IpBStandplatRefundType.RETURN_GOODS_RERUEN)) {
            // 假如退货物流单号不为空！ 并且和之前的物流单号不同
            if (StringUtils.isNotBlank(ipBStandplatRefund.getLogisticsNo())) {
                try {
                    LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(ipBStandplatRefund.getCompanyName());
                    if (Objects.nonNull(logisticsInfo)) {
                        returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                        returnOrder.setCpCLogisticsId(logisticsInfo.getId());
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("错误信息:{}"), Throwables.getStackTraceAsString(e));
                }
                returnOrder.setLogisticsCode(ipBStandplatRefund.getLogisticsNo());
                returnOrder.setCpCLogisticsEname(ipBStandplatRefund.getCompanyName());
            }
        } else if (ocBOrderDelivery != null) {
            returnOrder.setLogisticsCode(ocBOrderDelivery.getLogisticNumber());
            returnOrder.setCpCLogisticsId(ocBOrderDelivery.getCpCLogisticsId());
            returnOrder.setCpCLogisticsEcode(ocBOrderDelivery.getCpCLogisticsEcode());
            returnOrder.setCpCLogisticsEname(ocBOrderDelivery.getCpCLogisticsEname());
        }
        returnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());
        returnOrder.setInventedStatus(orderRelation.getInterceptMark()); //未发起拦截
        if (proType.equals(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode())
                || TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_REJECTION.getCode().equals(proType)) {
            returnOrder.setCpCPhyWarehouseInId(ocBOrder.getCpCPhyWarehouseId());
            this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder, true);
            //拦截或者拒收，走原退
            returnOrder.setIsBack(1);
        } else {
            StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
            //todo
            Integer isMultiReturnWarehouse = shopStrategy.getIsMultiReturnWarehouse();
            if (isMultiReturnWarehouse == null || isMultiReturnWarehouse == 0) {
                returnOrder.setCpCPhyWarehouseInId(shopStrategy.getCpCWarehouseDefId());
                this.selectReturnCPhyWarehouse(shopStrategy.getCpCWarehouseDefId(), returnOrder, false);
            }
        }
        ocSaveChangingOrRefundingService.checkBillType(returnOrder);
//        //加入“空运单号延迟推单有效时间”字段  在保存的方法里统一处理 2021-11-10
//        returnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder));
        OperateUserUtils.saveOperator(returnOrder, user);
        //获取退货业务类型
        String platformEcode = ipBStandplatRefund.getCpCPlatformEcode();
        Integer platmform = getStandplatRefundPlatmform(ipBStandplatRefund);
        if (PlatFormEnum.SAP.getCode().equals(platmform) || PlatFormEnum.DMS.getCode().equals(platmform)) {
            //匹配业务类型策略
            OmsBusinessTypeDistinguishService.BusinessTypeResult businessTypeResult = omsBusinessTypeDistinguishService.querySapBusinessType(ipBStandplatRefund.getBusinessTypeCode(), Integer.valueOf(platformEcode), ipBStandplatRefund.getReserveVarchar05(), orderRelation.getOcBOrderItems(), user);
            if (businessTypeResult == null) {
                throw new NDSException("SAP业务类型匹配失败！");
            }
            returnOrder.setBusinessTypeId(businessTypeResult.getId());
            returnOrder.setBusinessTypeCode(businessTypeResult.getCode());
            returnOrder.setBusinessTypeName(businessTypeResult.getName());
        } else {
            StCBusinessType stCBusinessType = omsRefundOrderService.queryReturnOrderType(ocBOrder);
            returnOrder.setBusinessTypeId(stCBusinessType.getId());
            returnOrder.setBusinessTypeCode(stCBusinessType.getEcode());
            returnOrder.setBusinessTypeName(stCBusinessType.getEname());
        }

        returnOrder.setOriginalBillNo(ipBStandplatRefund.getOriginalBillNo());
        returnOrder.setCostCenter(ipBStandplatRefund.getCostCenter());
        returnOrder.setSalesOrganize(ipBStandplatRefund.getSalesOrganize());
        //来源系统
        returnOrder.setReserveVarchar05(ocBOrder.getGwSourceGroup());
        setReturnPhyWarehouseInId(returnOrder, ipBStandplatRefund);
        returnOrder.setIsWrongReceive(IsWrongReceive.NO.val());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("构建退货单主表数据:{}", "构建退货单主表数据"), JSONObject.toJSONString(returnOrder));
        }

        // 维护传奶卡系统值
        if (PlatFormEnum.CARD_CODE.getCode().equals(returnOrder.getPlatform())) {
            // 判断订单业务类型
            if (OcCommonConstant.list.contains(returnOrder.getBusinessTypeCode())) {
                returnOrder.setToNaikaStatus(ReturnNaiKaStatusEnum.UN_PUSH.getStatus());
            }
        }
        returnOrder.setGwSourceCode(ocBOrder.getGwSourceCode());
        returnOrder.setPlatformRefundStatus(OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_INIT);
        if (returnOrder.getIsBack() != null
                && returnOrder.getIsBack() == 1
                && StringUtils.isNotEmpty(returnOrder.getCpCLogisticsEcode())
                && returnOrder.getCpCLogisticsEcode().equals(OcCommonConstant.DNKD)
                && StringUtils.isNotEmpty(returnOrder.getLogisticsCode())
                && !returnOrder.getLogisticsCode().contains("T")) {
            returnOrder.setLogisticsCode("T" + returnOrder.getLogisticsCode());
        }
        return returnOrder;
    }

    /**
     * 如果退单业务类型=线下销售退货/TOB拒收退货，如果通用退单中间表退货入库仓库有值，
     * 直接取值中间表转单时赋值的退货仓库
     *
     * @param returnOrder
     * @param ipBStandplatRefund
     */
    public void setReturnPhyWarehouseInId(OcBReturnOrder returnOrder, IpBStandplatRefund ipBStandplatRefund) {
        List<String> codeList = new ArrayList<>();
        codeList.add("RYTH16");
        codeList.add("RYTH12");
        String businessTypeCode = returnOrder.getBusinessTypeCode();
        if (codeList.contains(businessTypeCode)) {
            if (ipBStandplatRefund != null && ipBStandplatRefund.getSendBackStoreCode() != null) {
                CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.selectPhyWarehouseByEcode(ipBStandplatRefund.getSendBackStoreCode());
                if (cpCPhyWarehouse != null) {
                    returnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouse.getId());
                }
            }
        }
    }

    /**
     * 构建退换货单明细
     *
     * @param orderItem
     * @param ipBStandplatRefund
     * @param qtyRefund
     * @param user
     * @return
     */
    private OcBReturnOrderRefund buildReturnOrderItem(OcBOrderItem orderItem,
                                                      IpBStandplatRefund ipBStandplatRefund,
                                                      BigDecimal qtyRefund,
                                                      User user) {
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        BigDecimal refundAmt = orderItem.getRealAmt().divide(orderItem.getQty(),
                4, BigDecimal.ROUND_HALF_DOWN).multiply(qtyRefund);
        returnOrderRefund.setAmtRefund(refundAmt);
        //申请数量
        returnOrderRefund.setQtyRefund(qtyRefund);
        //商品名称
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());

        returnOrderRefund.setPrice(orderItem.getPrice());
        //1 qty_can_refund 购买数量 合计所有明细qty
        returnOrderRefund.setQtyCanRefund(orderItem.getQty());
        //商品单价
        returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
        returnOrderRefund.setPriceList(orderItem.getPriceList());
        //国标码
        returnOrderRefund.setBarcode(orderItem.getBarcode());
        //修改人用户名
        returnOrderRefund.setModifierename(orderItem.getModifierename());
        //商品规格
        returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setOid(orderItem.getOoid());
        returnOrderRefund.setTid(orderItem.getTid());
        //条码id
        returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCProId(orderItem.getPsCProId());
        //颜色尺寸
        returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
        returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

        returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
        returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
        returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
        returnOrderRefund.setSex(orderItem.getSex());
        returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
        returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
        returnOrderRefund.setGiftType(orderItem.getGiftType());
        returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
        returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                4, BigDecimal.ROUND_HALF_UP));
        returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");
        if (ipBStandplatRefund != null && orderItem.getOoid() != null &&
                orderItem.getOoid().equals(ipBStandplatRefund.getSubOrderId())) {
            Integer returnStatus = ipBStandplatRefund.getReturnStatus();
            String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
            returnOrderRefund.setRefundStatus(status);
            returnOrderRefund.setRefundBillNo(ipBStandplatRefund.getReturnNo());
            returnOrderRefund.setAmtPtRefund(ipBStandplatRefund.getRefundAmount());
        }
        returnOrderRefund.setIsEqualExchange(orderItem.getIsEqualExchange());
        OperateUserUtils.saveOperator(returnOrderRefund, user);
        return returnOrderRefund;
    }

    /**
     * 还原对等换货明细
     *
     * @return
     */
    private List<OcBReturnOrderRefund> reductionOrderItems(OmsOrderRelation omsRelation,
                                                           IpBStandplatRefund ipBStandplatRefund,
                                                           User user) {
        List<OcBReturnOrderRefund> orderRefunds = new ArrayList<>();
        OcBOrder ocBOrder = omsRelation.getOcBOrder();
        List<OcBOrderItem> ocBOrderItems = omsRelation.getOcBOrderItems();

        //对等换货明细
        List<OcBOrderItem> collect = ocBOrderItems.stream().filter(x -> x.getIsEqualExchange() != null && x.getIsEqualExchange().equals(1)).collect(Collectors.toList());
        OcBOrderItem orderItem = collect.get(0);
        List<OcBOrderEqualExchangeItem> equalExchangeItemList = taobaoRefundOrderTransferUtil.queryEqualExchangeItems(collect);
        Map<String, List<OcBOrderEqualExchangeItem>> exchangeItemMap = equalExchangeItemList.stream().
                collect(Collectors.groupingBy(x -> x.getOcBOrderId() + "-" +
                        x.getOoid()));
        //原明细成交单价
        BigDecimal priceActual = equalExchangeItemList.get(0).getPriceActual();
        //根据申请退款金额除以原明细成交单价计算退货数量
        BigDecimal refundAmount = ipBStandplatRefund.getRefundAmount();
        BigDecimal returnShipamount = ipBStandplatRefund.getReturnShipamount();
        if (returnShipamount != null && returnShipamount.compareTo(BigDecimal.ZERO) > 0) {
            refundAmount = refundAmount.subtract(returnShipamount);
        }
        //计算对等还原后的退货商品数量
        BigDecimal returnQty = refundAmount.divide(priceActual, 0, BigDecimal.ROUND_DOWN);

        //还原对等换货,例如：对等换货比例 2:1
        String equalExchangeRatio = orderItem.getEqualExchangeRatio();
        String[] equalExchangeRatioAr = equalExchangeRatio.split(":");
        BigDecimal beforeQty = new BigDecimal(equalExchangeRatioAr[0]);
        BigDecimal afterQty = new BigDecimal(equalExchangeRatioAr[1]);

        //如果有明细直接满足数量，则不用还原对等换货明细
        List<OcBOrderItem> filterList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem1 : ocBOrderItems) {
            if (ocBOrderItem1.getIsGift() != null && ocBOrderItem1.getIsGift() == 1) {
                continue;
            }
            //当前明细数量
            BigDecimal qty = ocBOrderItem1.getQty();
            //对应原明细数量
            BigDecimal bQty = beforeQty.multiply(qty).divide(afterQty, 4, BigDecimal.ROUND_HALF_UP);
            if (returnQty.compareTo(bQty) == 0) {
                //取当前这条明细生成退换货单，不用还原对等换货明细
                filterList.add(ocBOrderItem1);
                break;
            }
        }
        if (CollectionUtils.isNotEmpty(filterList)) {
            OcBOrderItem ocBOrderItem = filterList.get(0);
            OcBReturnOrderRefund returnOrderRefund = buildReturnOrderItem(ocBOrderItem, ipBStandplatRefund, returnQty, user);
            orderRefunds.add(returnOrderRefund);
            returnQty = BigDecimal.ZERO;
        }

        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            BigDecimal qty = BigDecimal.ZERO;
            OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
            if (ocBOrderItem.getIsGift() != null && ocBOrderItem.getIsGift() == 0) {
                if (returnQty.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                List<OcBOrderEqualExchangeItem> exchangeItems = exchangeItemMap.get(ocBOrderItem.getOcBOrderId() + "-" + ocBOrderItem.getOoid());
                if (CollectionUtils.isEmpty(exchangeItems)) {
                    continue;
                }
                OcBOrderEqualExchangeItem exchangeItem = exchangeItems.get(0);
                //非赠品，计算当前明细可退数量
                /**换货后商品数量*/
                BigDecimal aQty = ocBOrderItem.getQty();
                /**换货前商品数量*/
                BigDecimal bQty = aQty.multiply(beforeQty).divide(afterQty, 4, BigDecimal.ROUND_HALF_UP);
                /**可退数量*/
                BigDecimal qtyApply = bQty.subtract(exchangeItem.getQtyRefund() == null ? BigDecimal.ZERO : exchangeItem.getQtyRefund());
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("计算当前明细还原对等换货明细数量。RETURN_NO={},换货后明细数量={}，换货前明细数量={}，可退数量={};",
                            "StandplatRefundOrderTransferUtil"), ipBStandplatRefund.getReturnNo(), aQty, bQty, qtyApply);
                }
                OcBOrderItem newOrderItem = new OcBOrderItem();
                BeanUtils.copyProperties(exchangeItem, newOrderItem);
                newOrderItem.setId(ocBOrderItem.getId());

                if (qtyApply.compareTo(returnQty) >= 0) {
                    qty = returnQty;
                    returnQty = BigDecimal.ZERO;
                } else {
                    qty = qtyApply;
                    returnQty = returnQty.subtract(qtyApply);
                }
                returnOrderRefund = buildReturnOrderItem(ocBOrderItem, ipBStandplatRefund, qty, user);
            } else {
                //赠品
                qty = ocBOrderItem.getQty();
                returnOrderRefund = buildReturnOrderItem(ocBOrderItem, ipBStandplatRefund, qty, user);
            }
            orderRefunds.add(returnOrderRefund);
        }
        if (returnQty.compareTo(BigDecimal.ZERO) > 0) {
            throw new NDSException("还原对等换货，可退数量小于申请数量！");
        }
        return orderRefunds;
    }

    /**
     * 退单明细表数量
     *
     * @param ipBStandplatRefund
     * @return
     */
    private List<OcBReturnOrderRefund> buildReturnOrderItemFromRefund(OmsOrderRelation orderRelation,
                                                                      IpBStandplatRefund ipBStandplatRefund,
                                                                      Map<String, BigDecimal> skuCount,
                                                                      User user) {
        List<OcBReturnOrderRefund> result = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        for (OcBOrderItem orderItem : ocBOrderItems) {
            BigDecimal bigDecimal = skuCount.get(orderItem.getPsCSkuEcode());
            if (bigDecimal == null) {
                continue;
            }
            if (bigDecimal.compareTo(orderItem.getQty()) > 0) {
                bigDecimal = orderItem.getQty();
            }
            OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
            BigDecimal refundAmt = orderItem.getRealAmt().divide(orderItem.getQty(),
                    4, BigDecimal.ROUND_HALF_DOWN).multiply(bigDecimal);
            returnOrderRefund.setAmtRefund(refundAmt);
            //申请数量
            returnOrderRefund.setQtyRefund(bigDecimal);
            //商品名称
            returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());

            returnOrderRefund.setPrice(orderItem.getPrice());
            //1 qty_can_refund 购买数量 合计所有明细qty
            returnOrderRefund.setQtyCanRefund(orderItem.getQty());
            //商品单价
            returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
            returnOrderRefund.setPriceList(orderItem.getPriceList());
            //国标码
            returnOrderRefund.setBarcode(orderItem.getBarcode());
            //修改人用户名
            returnOrderRefund.setModifierename(orderItem.getModifierename());
            //商品规格
            returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
            returnOrderRefund.setOid(orderItem.getOoid());
            returnOrderRefund.setTid(orderItem.getTid());
            //条码id
            returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
            returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
            returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
            returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
            returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
            returnOrderRefund.setPsCProId(orderItem.getPsCProId());
            //颜色尺寸
            returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
            returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
            returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

            returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
            returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
            returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
            returnOrderRefund.setSex(orderItem.getSex());
            returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
            returnOrderRefund.setOcBOrderItemId(orderItem.getId());
            returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
            //returnOrderRefund.setAmtPtRefund(ipBTaobaoRefund.getRefundFee());
            returnOrderRefund.setGiftType(orderItem.getGiftType());
            returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
            //returnOrderRefund.setRefundStatus(ipBTaobaoRefund.getStatus());
            //returnOrderRefund.setRefundBillNo(ocBOrder.getBillNo());
            returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                    4, BigDecimal.ROUND_HALF_UP));
            returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");


            if (ipBStandplatRefund != null && orderItem.getOoid() != null &&
                    orderItem.getOoid().equals(ipBStandplatRefund.getSubOrderId())) {
                Integer returnStatus = ipBStandplatRefund.getReturnStatus();
                String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
                returnOrderRefund.setRefundStatus(status);
                returnOrderRefund.setRefundBillNo(ipBStandplatRefund.getReturnNo());
                returnOrderRefund.setAmtPtRefund(ipBStandplatRefund.getRefundAmount());
            }
            OperateUserUtils.saveOperator(returnOrderRefund, user);

            result.add(returnOrderRefund);
        }
        return result;
    }

    /**
     * 按包裹生成退换货订单的关系处理
     *
     * @param orderRelation
     * @param ipBStandplatRefund
     * @param skuCount
     * @param ocBOrderDelivery
     * @param billsStatus        单据类型(拒收 , 拦截)
     * @return
     */
    public OcBReturnOrderRelation standplatRefundOrderToReturnOrder(OmsOrderRelation orderRelation,
                                                                    IpBStandplatRefund ipBStandplatRefund,
                                                                    Map<String, BigDecimal> skuCount,
                                                                    OcBOrderDelivery ocBOrderDelivery,
                                                                    TaobaoReturnOrderExt.ReturnBillsStatus billsStatus,
                                                                    User user) {
        OcBReturnOrderRelation returnOrderRelation = new OcBReturnOrderRelation();
        OcBReturnOrder ocBReturnOrder = this.buildOcBReturnOrderFromStandplatRefund(orderRelation,
                ipBStandplatRefund, ocBOrderDelivery, billsStatus.getCode(), user);
        if (ocBReturnOrder == null) {
            return null;
        }
        List<OcBReturnOrderRefund> orderRefunds = new ArrayList<>();
        //判断是否需要还原对等换货
        boolean isBeforAndAfter = false;
        String reserveVarchar10 = ipBStandplatRefund.getReserveVarchar10();
        if (StringUtils.isNotBlank(reserveVarchar10) && "T".equals(reserveVarchar10)) {
            isBeforAndAfter = true;
        }
        boolean isPeerExchange = taobaoRefundOrderTransferUtil.checkIsPeerExchange(orderRelation, ipBStandplatRefund.getRefundAmount(), ipBStandplatRefund.getReturnShipamount(), isBeforAndAfter);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查询当前退单是否需要还原对等换货明细！RETURN_NO={}，isPeerExchange={};",
                    "StandplatRefundOrderTransferUtil"), ipBStandplatRefund.getReturnNo(), isPeerExchange);
        }
        if (isPeerExchange) {
            //还原对等换货
            orderRefunds = reductionOrderItems(orderRelation,
                    ipBStandplatRefund, user);
        } else {
            orderRefunds = buildReturnOrderItemFromRefund(orderRelation,
                    ipBStandplatRefund, skuCount, user);

            // @20200818 bug#21336 通用平台退单可退数量校验--拦截场景
            boolean qtyFlag = OmsOrderQtyCalculateService.getInstance().checkQtyCanReturn(orderRefunds, orderRelation.getOcBOrderItems());

            if (!qtyFlag) {
                throw new NDSException("申请数量大于可退数量");
            }
        }
        if (OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN.getCode().equals(ocBReturnOrder.getBusinessTypeCode()) ||
                OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP_RETURN.getCode().equals(ocBReturnOrder.getBusinessTypeCode())) {
            for (OcBReturnOrderRefund returnOrderRefund : orderRefunds) {
                returnOrderRefund.setAmtRefundSingle(new BigDecimal("0.01"));
                returnOrderRefund.setAmtRefund(returnOrderRefund.getAmtRefundSingle().multiply(returnOrderRefund.getQtyRefund()));
                returnOrderRefund.setPriceSettle(new BigDecimal("0.01"));
                returnOrderRefund.setAmtSettleTot(returnOrderRefund.getPriceSettle().multiply(returnOrderRefund.getQtyRefund()));
            }
        }
        String jointTid = OmsReturnAfterUtil.getJointTid(orderRefunds);
        ocBReturnOrder.setTid(jointTid);
        this.getAllSku(orderRefunds, ocBReturnOrder);
        returnOrderRelation.setReturnOrderInfo(ocBReturnOrder);
        returnOrderRelation.setOrderRefundList(orderRefunds);
        return returnOrderRelation;
    }

    /**
     * 封装主表的all_sku以及 商品数量
     *
     * @param returnOrderItems
     * @return
     */
    private void getAllSku(List<OcBReturnOrderRefund> returnOrderItems, OcBReturnOrder returnOrder) {
        //拼接退货sku加数量
        String skuQyt = "";
        BigDecimal qtyInstore = BigDecimal.ZERO;
        int i = 0;
        for (OcBReturnOrderRefund returnOrderItem : returnOrderItems) {
            qtyInstore = qtyInstore.add(returnOrderItem.getQtyRefund());
            if (i == 5) {
                continue;
            }
            String str = returnOrderItem.getPsCSkuEcode() + "(" + returnOrderItem.getQtyRefund().intValue() + "),";
            skuQyt = skuQyt + str;
            i++;

        }
        if (StringUtils.isNotEmpty(skuQyt)) {
            //去掉最后一个,号
            skuQyt = skuQyt.substring(0, skuQyt.length() - 1);
        }
        returnOrder.setAllSku(skuQyt);
        returnOrder.setQtyInstore(qtyInstore);
    }


    /**
     * 按子订单生成退单明细数据
     *
     * @param orderRelation
     * @return
     */
    private List<OcBReturnOrderRefund> buildReturnOrderItemFromOid(boolean isFullRefund, OmsOrderRelation orderRelation,
                                                                   IpBStandplatRefund ipBStandplatRefund,
                                                                   List<IpBStandplatRefundItem> ipBStandplatRefundItems,
                                                                   User user, Boolean flag) {
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        if (isFullRefund) {
            return buildReturnOrderItemFromOidBak(orderRelation, ipBStandplatRefund, ipBStandplatRefundItems, user, flag);
        }
        return this.getReturnRefunds(ipBStandplatRefund, ipBStandplatRefundItems, ocBOrderItems, user);
    }

    private List<OcBReturnOrderRefund> buildReturnOrderItemFromOidBak(OmsOrderRelation orderRelation,
                                                                      IpBStandplatRefund ipBStandplatRefund,
                                                                      List<IpBStandplatRefundItem> ipBStandplatRefundItems,
                                                                      User user, Boolean flag) {
        if (log.isDebugEnabled()) {
            log.debug("buildReturnOrderItemFromOidBak.orderRelation={};ipBStandplatRefund={}",
                    JSON.toJSONString(orderRelation), JSON.toJSONString(ipBStandplatRefund));
        }
        List<OcBReturnOrderRefund> orderRefunds = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        OcBOrder ocBOrder = orderRelation.getOcBOrder();
        //退货金额
        for (OcBOrderItem orderItem : ocBOrderItems) {
            //申请退货数量
            BigDecimal qtyReturnApply = orderItem.getQtyReturnApply();
            if (qtyReturnApply == null) {
                qtyReturnApply = BigDecimal.ZERO;
            }
            //可以退的数量
            // BigDecimal qty = orderItem.getQty().subtract(qtyReturnApply);
            orderRefunds.add(this.geFullOcBReturnOrderRefund(ipBStandplatRefund, user, ocBOrder, orderItem, orderItem.getQty(), flag));
        }
        setReturnOrderItemsByRatio(ocBOrderItems, orderRefunds, ipBStandplatRefund);
        return orderRefunds;
    }

    /**
     * 组合商品根据退款金额算数量的处理
     *
     * @param ocBOrderItems
     */
    private List<OcBReturnOrderRefund> combinationGoodsHandle(List<OcBOrderItem> ocBOrderItems, BigDecimal realAmtCount,
                                                              IpBStandplatRefund ipBStandplatRefund,
                                                              List<IpBStandplatRefundItem> ipBStandplatRefundItems, User user, OcBOrder order) {
        List<OcBReturnOrderRefund> refundList = new ArrayList<>();
        //组合商品的数量
        BigDecimal qtyGroup = ocBOrderItems.get(0).getQtyGroup();
        BigDecimal refundFee = ipBStandplatRefund.getRefundAmount();
        //单价
        BigDecimal price = realAmtCount.divide(qtyGroup, 4, BigDecimal.ROUND_HALF_UP);
        //根据退款金额计算的数量
        BigDecimal qtyApply = refundFee.divide(price, 0, BigDecimal.ROUND_UP);
        for (OcBOrderItem orderItem : ocBOrderItems) {
            //明细的购买数量
            BigDecimal qty = orderItem.getQty();
            //当前组合商品的对应真实条码的数量
            BigDecimal groupNum = qty.divide(qtyGroup, 0, BigDecimal.ROUND_HALF_UP);
            qtyApply = groupNum.multiply(qtyApply);

            //申请退货数量
            BigDecimal qtyReturnApply = orderItem.getQtyReturnApply();

            if (qtyReturnApply == null) {
                qtyReturnApply = BigDecimal.ZERO;
            }
            if (qtyReturnApply.compareTo(orderItem.getQty()) == 0) {
                continue;
            }
            OcBReturnOrderRefund ocBReturnOrderRefund =
                    this.getOcBReturnOrderRefund(ipBStandplatRefund, user, order, orderItem, qtyApply);
            refundList.add(ocBReturnOrderRefund);
        }
        return refundList;
    }


    /**
     * 封装客退的明细数据
     *
     * @param ipBStandplatRefund
     * @param user
     * @param ocBOrder
     * @param orderItem
     * @param qty
     * @return
     */
    private OcBReturnOrderRefund getOcBReturnOrderRefund(IpBStandplatRefund ipBStandplatRefund, User user,
                                                         OcBOrder ocBOrder, OcBOrderItem orderItem, BigDecimal qty) {
        //发货信息
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        BigDecimal refundAmt = orderItem.getRealAmt().divide(orderItem.getQty(),
                4, BigDecimal.ROUND_HALF_UP).multiply(qty);
        returnOrderRefund.setAmtRefund(refundAmt);
        returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        //申请数量
        returnOrderRefund.setQtyRefund(qty);
        Integer returnStatus = ipBStandplatRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        returnOrderRefund.setRefundStatus(status);
        returnOrderRefund.setAmtPtRefund(ipBStandplatRefund.getRefundAmount());
        //商品名称
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPrice(orderItem.getPriceList());
        //1 qty_can_refund 购买数量 合计所有明细qty
        returnOrderRefund.setQtyCanRefund(orderItem.getQty());
        //商品单价
        returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
        //国标码
        returnOrderRefund.setBarcode(orderItem.getBarcode());
        //修改人用户名
        returnOrderRefund.setModifierename(orderItem.getModifierename());
        //商品规格
        returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setOid(orderItem.getOoid());
        returnOrderRefund.setTid(orderItem.getTid());
        //条码id
        returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
        returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
        returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
        returnOrderRefund.setGiftType(orderItem.getGiftType());
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCProId(orderItem.getPsCProId());
        //颜色尺寸
        returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
        returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

        returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
        returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
        returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
        returnOrderRefund.setSex(orderItem.getSex());
        returnOrderRefund.setRefundBillNo(ipBStandplatRefund.getReturnNo());
        returnOrderRefund.setOcBOrderId(ocBOrder.getId());
        returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                4, BigDecimal.ROUND_HALF_UP));
        //returnOrderRefund.setReserveDecimal01(orderItem.getReserveDecimal02());
        returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");
        OperateUserUtils.saveOperator(returnOrderRefund, user);
        return returnOrderRefund;
    }


    /**
     * @param ipBStandplatRefund 通用退单
     * @param user
     * @param ocBOrder
     * @param orderItem
     * @param qty
     * @param flag               是否为组合商品
     * @return
     */
    private OcBReturnOrderRefund geFullOcBReturnOrderRefund(IpBStandplatRefund ipBStandplatRefund, User user,
                                                            OcBOrder ocBOrder, OcBOrderItem orderItem, BigDecimal qty, Boolean flag) {

        //发货信息
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        if (orderItem.getQtyReturnApply() != null && BigDecimal.ZERO.compareTo(orderItem.getQtyReturnApply()) != 0 && flag) {
            //申请数量
            returnOrderRefund.setQtyRefund(qty.subtract(orderItem.getQtyReturnApply()));
        } else {
            //申请数量
            returnOrderRefund.setQtyRefund(qty);
        }
        BigDecimal refundAmt = orderItem.getRealAmt().multiply(returnOrderRefund.getQtyRefund()).divide(orderItem.getQty(),
                OcBOrderConst.DECIMAL_QTY_FOUR, BigDecimal.ROUND_HALF_UP);
        returnOrderRefund.setAmtRefund(refundAmt);
        Integer returnStatus = ipBStandplatRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        returnOrderRefund.setRefundStatus(status);
        returnOrderRefund.setAmtPtRefund(ipBStandplatRefund.getRefundAmount());
        //商品名称
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPrice(orderItem.getPriceList());
        //1 qty_can_refund 购买数量 合计所有明细qty
        returnOrderRefund.setQtyCanRefund(orderItem.getQty());
        //商品单价
        returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
        //国标码
        returnOrderRefund.setBarcode(orderItem.getBarcode());
        //修改人用户名
        returnOrderRefund.setModifierename(orderItem.getModifierename());
        //商品规格
        returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setOid(orderItem.getOoid());
        returnOrderRefund.setTid(orderItem.getTid());
        //条码id
        returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
        returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
        returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
        returnOrderRefund.setGiftType(orderItem.getGiftType());
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCProId(orderItem.getPsCProId());
        //颜色尺寸
        returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
        returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

        returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
        returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
        returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
        returnOrderRefund.setSex(orderItem.getSex());
        returnOrderRefund.setRefundBillNo(ipBStandplatRefund.getReturnNo());
        returnOrderRefund.setOcBOrderId(ocBOrder.getId());
        returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                OcBOrderConst.DECIMAL_QTY_FOUR, BigDecimal.ROUND_HALF_UP));
        returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");
        OperateUserUtils.saveOperator(returnOrderRefund, user);
        return returnOrderRefund;
    }

    /**
     * 赠品订单生成退换货单
     *
     * @param omsOrderRelation
     * @param ipBStandplatRefund
     * @param ipBStandplatRefundItems
     * @param user
     */
    public void bulidGiftReturnOrder(OmsOrderRelation omsOrderRelation,
                                     IpBStandplatRefund ipBStandplatRefund,
                                     List<IpBStandplatRefundItem> ipBStandplatRefundItems,
                                     Integer ReturnBillsStatus,
                                     User user) {
        OcBOrderDelivery ocBOrderDelivery = null;
        if (CollectionUtils.isNotEmpty(omsOrderRelation.getOrderDeliveries())) {
            ocBOrderDelivery = omsOrderRelation.getOrderDeliveries().get(0);
        }
        OcBReturnOrder ocBReturnOrder = this.buildOcBReturnOrderFromStandplatRefund(omsOrderRelation, ipBStandplatRefund,
                ocBOrderDelivery, ReturnBillsStatus, user);
        if (ocBReturnOrder == null) {
            return;
        }

        //sku满足“无名件入库结果单数异、品异剔除商品”配置，生成退换货单时过滤该sku
        List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
        if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
            List<String> skuExcludes = returnInService.getBusinessSystemForOcReturnInExclude();
            List<OcBOrderItem> ocBOrderItemss = Lists.newArrayList();
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                if (!skuExcludes.contains(ocBOrderItem.getPsCSkuEcode())) {
                    ocBOrderItemss.add(ocBOrderItem);
                }
            }

            if (CollectionUtils.isEmpty(ocBOrderItemss)) {
                return;
            }
            omsOrderRelation.setOcBOrderItems(ocBOrderItemss);
        }

        List<OcBReturnOrderRefund> orderRefunds = this.buildReturnOrderItemFromOid(true, omsOrderRelation, ipBStandplatRefund, ipBStandplatRefundItems, user, false);
        if (OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN.getCode().equals(ocBReturnOrder.getBusinessTypeCode()) ||
                OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP_RETURN.getCode().equals(ocBReturnOrder.getBusinessTypeCode())) {
            for (OcBReturnOrderRefund returnOrderRefund : orderRefunds) {
                returnOrderRefund.setAmtRefundSingle(new BigDecimal("0.01"));
                returnOrderRefund.setAmtRefund(returnOrderRefund.getAmtRefundSingle().multiply(returnOrderRefund.getQtyRefund()));
                returnOrderRefund.setPriceSettle(new BigDecimal("0.01"));
                returnOrderRefund.setAmtSettleTot(returnOrderRefund.getPriceSettle().multiply(returnOrderRefund.getQtyRefund()));
            }
        }
        String jointTid = OmsReturnAfterUtil.getJointTid(orderRefunds);
        ocBReturnOrder.setTid(jointTid);
        this.getAllSku(orderRefunds, ocBReturnOrder);
        OcBReturnOrderRelation relation = new OcBReturnOrderRelation();
        ocBReturnOrder.setReturnAmtActual(orderRefunds.stream().map(OcBReturnOrderRefund::getAmtRefund)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        ocBReturnOrder.setReturnAmtList(ocBReturnOrder.getReturnAmtActual());
        relation.setReturnOrderInfo(ocBReturnOrder);
        relation.setOrderRefundList(orderRefunds);
        List<OcBReturnOrderRelation> orderRelations = new ArrayList<>();
        orderRelations.add(relation);
        List<Long> refundIds = omsReturnOrderService.insertOmsReturnOrderInfo(orderRelations, user);
    }


    /**
     * 按子订单生成退换货订单的关系处理
     *
     * @param orderRelation
     * @param ipBStandplatRefund
     * @return
     */
    public List<OcBReturnOrderRelation> standplatRefundOrderToReturnOid(OmsStandPlatRefundRelation orderInfo,
                                                                        List<OmsOrderRelation> orderRelation,
                                                                        IpBStandplatRefund ipBStandplatRefund,
                                                                        List<IpBStandplatRefundItem> ipBStandplatRefundItems,
                                                                        TaobaoReturnOrderExt.ReturnBillsStatus returnBillsStatus,
                                                                        User user, List<OcBOrderItem> refundNoNeedOrderItemList) {
        //是否为组合商品
        boolean flag = false;

        //生成主表数据
        List<OcBReturnOrderRelation> orderRelations = new ArrayList<>();

        //组合商品整单退逻辑： 判断中间表子订单号对应原单是否为组合商品，若为组合商品整单退
        List<IpBStandplatRefundItem> items = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ipBStandplatRefundItems)) {
            for (IpBStandplatRefundItem item : ipBStandplatRefundItems) {
                boolean proType_4 = false;
                boolean proType_2 = false;
                if (StringUtils.isNotBlank(item.getSubOrderId())) {
                    List<OcBOrderItem> bOrderItems = ocBOrderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().lambda()
                            .eq(OcBOrderItem::getOoid, item.getSubOrderId())
                            .eq(OcBOrderItem::getTid, ipBStandplatRefund.getOrderNo()));
                    for (OcBOrderItem ocBOrderItem : bOrderItems) {
                        Long proType = ocBOrderItem.getProType();
                        if (4 == proType) {
                            proType_4 = true;
                        } else if (2 == proType) {
                            proType_2 = true;
                        }
                    }
                }
                if (proType_2 || proType_4) {
                    item.setSubOrderId(null);
                    items.add(item);
                    flag = true;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(items)) {
            orderInfo.setIpBStandplatRefundItem(items);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("按子订单生成退换货订单的关系后续处理：{}"), JSONObject.toJSONString(orderInfo));
        }

        //生成主表数据
        for (int i = 0; i < orderRelation.size(); i++) {
            OmsOrderRelation omsOrderRelation = orderRelation.get(i);
            if (CollectionUtils.isEmpty(omsOrderRelation.getOrderDeliveries())) {
                refundNoNeedOrderItemList.addAll(omsOrderRelation.getOcBOrderItems());
                continue;
            }
            OcBReturnOrder ocBReturnOrder = this.buildOcBReturnOrderFromStandplatRefund(omsOrderRelation, ipBStandplatRefund,
                    omsOrderRelation.getOrderDeliveries().get(0), returnBillsStatus.getCode(), user);
            if (ocBReturnOrder == null) {
                refundNoNeedOrderItemList.addAll(omsOrderRelation.getOcBOrderItems());
                continue;
            }

            //sku满足“无名件入库结果单数异、品异剔除商品”配置，生成退换货单时过滤该sku
            List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                List<String> skuExcludes = returnInService.getBusinessSystemForOcReturnInExclude();
                List<OcBOrderItem> ocBOrderItemss = Lists.newArrayList();
                for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                    if (!skuExcludes.contains(ocBOrderItem.getPsCSkuEcode())) {
                        ocBOrderItemss.add(ocBOrderItem);
                    }
                }

                if (CollectionUtils.isEmpty(ocBOrderItemss)) {
                    refundNoNeedOrderItemList.addAll(omsOrderRelation.getOcBOrderItems());
                    continue;
                }
                omsOrderRelation.setOcBOrderItems(ocBOrderItemss);
            }

            List<OcBOrderItem> orderItemList = new ArrayList<>();
            orderItemList.addAll(omsOrderRelation.getOcBOrderItems());
            if (omsSystemConfig.isReturnOrderAddBringGift()) {
                //判断赠品(是否有挂靠赠品)
                List<OmsOrderRelation.OcOrderGifts> ocOrderGifts = omsOrderRelation.getOcOrderGifts();
                if (CollectionUtils.isNotEmpty(ocOrderGifts)) {
                    for (OmsOrderRelation.OcOrderGifts ocOrderGift : ocOrderGifts) {
                        if (ocOrderGift.getGiftMark() == 2) {
                            List<OcBOrderItem> ocBOrderGifts = ocOrderGift.getOcBOrderGifts();
                            //将挂靠赠品加入明细
                            orderItemList.addAll(ocBOrderGifts);
                        } else {
                            if (Objects.nonNull(ipBStandplatRefund)) {
                                String orderNo = ipBStandplatRefund.getOrderNo();
                                if (!isGift(orderNo)) {
                                    List<OcBOrderItem> ocBOrderGifts = ocOrderGift.getOcBOrderGifts();
                                    //将挂靠赠品加入明细
                                    orderItemList.addAll(ocBOrderGifts);
                                }
                            }
                        }
                    }
                }
            }
            log.info("order {} [standplatRefundOrderToReturnOid] action mapping goods item end, mapping goods list:{}", orderInfo.getOrderId(), JSON.toJSONString(orderItemList));

            OmsOrderRelation omsRelation = new OmsOrderRelation();
            omsRelation.setOcBOrder(omsOrderRelation.getOcBOrder());
            omsRelation.setOcBOrderItems(orderItemList);
            //判断是否需要还原对等换货
            boolean isBeforAndAfter = false;
            String reserveVarchar10 = ipBStandplatRefund.getReserveVarchar10();
            if (StringUtils.isNotBlank(reserveVarchar10) && "T".equals(reserveVarchar10)) {
                isBeforAndAfter = true;
            }
            boolean isPeerExchange = taobaoRefundOrderTransferUtil.checkIsPeerExchange(omsRelation, ipBStandplatRefund.getRefundAmount(), ipBStandplatRefund.getReturnShipamount(), isBeforAndAfter);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询当前退单是否需要还原对等换货明细！RETURN_NO={}，isPeerExchange={};",
                        "StandplatRefundOrderTransferUtil"), ipBStandplatRefund.getReturnNo(), isPeerExchange);
            }
            List<OcBReturnOrderRefund> orderRefunds = new ArrayList<>();
            if (!orderInfo.isFullRefund() && isPeerExchange) {
                orderRefunds = reductionOrderItems(omsRelation, ipBStandplatRefund, user);
                if (CollectionUtils.isEmpty(orderRefunds)) {
                    continue;
                }
            } else {
                orderRefunds = this.buildReturnOrderItemFromOid(orderInfo.isFullRefund(), omsRelation, ipBStandplatRefund, ipBStandplatRefundItems, user, flag);
                if (CollectionUtils.isEmpty(orderRefunds)) {
                    if (i != orderRelation.size() - 1) {
                        continue;
                    } else {
                        return null;
                    }
                }

                // @20200813 bug#21336 通用平台退单可退数量校验
                boolean qtyFlag = OmsOrderQtyCalculateService.getInstance().checkQtyCanReturn(orderRefunds, orderItemList);

                if (!qtyFlag) {
                    if (i != orderRelation.size() - 1) {
                        continue;
                    } else {
                        return null;
                    }
                }
            }

            // 对orderRefunds中的ocborderitemid进行去重处理
            Map<Long, OcBReturnOrderRefund> ocBReturnOrderRefundMap = new HashMap<>();
            for (OcBReturnOrderRefund ocBReturnOrderRefund : orderRefunds) {
                ocBReturnOrderRefundMap.put(ocBReturnOrderRefund.getOcBOrderItemId(), ocBReturnOrderRefund);
            }
            orderRefunds = new ArrayList<>(ocBReturnOrderRefundMap.values());
            // 增加逻辑。 如果ocbreturnorder的businesstypecode为RYTH01或者RYTH13 则将orderRefunds中的amtRefundSingle 金额设置为0.01
            if (OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN.getCode().equals(ocBReturnOrder.getBusinessTypeCode()) ||
                    OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP_RETURN.getCode().equals(ocBReturnOrder.getBusinessTypeCode())) {
                for (OcBReturnOrderRefund returnOrderRefund : orderRefunds) {
                    returnOrderRefund.setAmtRefundSingle(new BigDecimal("0.01"));
                    returnOrderRefund.setAmtRefund(returnOrderRefund.getAmtRefundSingle().multiply(returnOrderRefund.getQtyRefund()));
                    returnOrderRefund.setPriceSettle(new BigDecimal("0.01"));
                    returnOrderRefund.setAmtSettleTot(returnOrderRefund.getPriceSettle().multiply(returnOrderRefund.getQtyRefund()));
                }
            }

            String jointTid = OmsReturnAfterUtil.getJointTid(orderRefunds);
            ocBReturnOrder.setTid(jointTid);
            this.getAllSku(orderRefunds, ocBReturnOrder);
            OcBReturnOrderRelation relation = new OcBReturnOrderRelation();
            ocBReturnOrder.setReturnAmtActual(orderRefunds.stream().map(OcBReturnOrderRefund::getAmtRefund)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            ocBReturnOrder.setReturnAmtList(ocBReturnOrder.getReturnAmtActual());
            relation.setReturnOrderInfo(ocBReturnOrder);
            relation.setOrderRefundList(orderRefunds);
            orderRelations.add(relation);
        }

        // @20200825 重算金额
        OmsReturnOrderService.getInstance().reCalculateReturnAmt(orderRelations);

        return orderRelations;
    }


    /**
     * 通过平台单号查询是否生成过退换货单
     *
     * @param tid
     * @return
     */
    private boolean isGift(String tid) {
        //通过平台单号查询ES
        Set<Long> ids = ES4ReturnOrder.findIdByTid(tid);
        //是否存在退换货订单
        return CollectionUtils.isNotEmpty(ids);
    }

    /**
     * 生成发货后退款单
     *
     * @return
     */
    private OcBReturnAfSend buildOcBReturnAfSend(OcBOrder ocBOrder,
                                                 IpBStandplatRefund ipBStandplatRefund,
                                                 Integer billType, User user) {
        OcBReturnAfSend ocBReturnAfSend = new OcBReturnAfSend();
        ocBReturnAfSend.setRefundOrderSourceType(2);
        ocBReturnAfSend.setCpCShopId(ocBOrder.getCpCShopId());
        ocBReturnAfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        ocBReturnAfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        ocBReturnAfSend.setTid(ocBOrder.getTid());
        ocBReturnAfSend.setBillNo(sequenceUtil.aFbuildBillNo());
        //退款状态 0 待审核
        ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDING.getVal());
        ocBReturnAfSend.setTReturnId(ipBStandplatRefund.getReturnNo());
        //单据类型 0 退货退款 1仅退款',
        ocBReturnAfSend.setBillType(billType);
        ocBReturnAfSend.setVipNick(ipBStandplatRefund.getBuyerNick());
        ocBReturnAfSend.setReason(ipBStandplatRefund.getReturnReason());
        //支付方式
        ocBReturnAfSend.setPayMode(ocBOrder.getPayType() + "");
        //支付宝账号
        ocBReturnAfSend.setPayAccount(ocBOrder.getBuyerAlipayNo());
        //申请退款金额
        ocBReturnAfSend.setAmtReturnApply(ipBStandplatRefund.getRefundAmount());

        // @******** 赋值单据来源为自动
        ocBReturnAfSend.setRefundOrderSourceType(RefundOrderSourceTypeEnum.AUTO.getValue());

        ocBReturnAfSend.setSourceBillNo(ocBOrder.getId() + "");
        ocBReturnAfSend.setCpCPlatformId(Long.valueOf(ocBOrder.getPlatform()));
        ocBReturnAfSend.setPayMode(OcBReturnAfSendListEnums.PayTypeEnum.Alipay.getVal());
        //todo 实际退款金额
        ocBReturnAfSend.setAmtReturnActual(ipBStandplatRefund.getRefundAmount());
        //申请退款时间
        ocBReturnAfSend.setReturnApplyTime(new Date());
        ocBReturnAfSend.setAgStatus(AGStatusEnum.INIT.getVal() + "");
        ocBReturnAfSend.setReturnExplain(ipBStandplatRefund.getReturnReason());
        //收货人姓名
        ocBReturnAfSend.setReceiverName(ocBOrder.getReceiverName());
        //收货人手机
        ocBReturnAfSend.setReceiverMobile(ocBOrder.getReceiverMobile());
        Integer returnStatus = ipBStandplatRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        ocBReturnAfSend.setTReturnStatus(status);
        if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
            ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
            ocBReturnAfSend.setReturnPaymentTime(new Date());
        }
        //查询业务类型：原单业务类型对应的退款业务类型
        StCBusinessType stCBusinessType = omsRefundOrderService.queryRefundOrderType(ocBOrder);
        ocBReturnAfSend.setBusinessTypeId(stCBusinessType.getId());
        ocBReturnAfSend.setBusinessTypeCode(stCBusinessType.getEcode());
        ocBReturnAfSend.setBusinessTypeName(stCBusinessType.getEname());
        OperateUserUtils.saveOperator(ocBReturnAfSend, user);

        return ocBReturnAfSend;
    }


    /**
     * 生成发货后退款单明细(关联退换货单)
     *
     * @param ocBReturnOrderRefunds
     * @return
     */
    private List<OcBReturnAfSendItem> buildOcBReturnAfSendItemRelation(List<OcBReturnOrderRefund> ocBReturnOrderRefunds,
                                                                       IpBStandplatRefund ipBStandplatRefund,
                                                                       User user, List<OcBOrderItem> orderItems,
                                                                       List<OcBReturnOrder> ocBReturnOrders,
                                                                       List<OcBOrderItem> refundNoNeedOrderItemList, OcBReturnAfSend ocBReturnAfSend) {
        Map<Long, OcBOrderItem> map = new HashMap<>(10);
        BigDecimal totalAmtActual = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OcBOrderItem item : orderItems) {
                map.put(item.getId(), item);
                totalAmtActual = totalAmtActual.add(item.getRealAmt() == null ? BigDecimal.ZERO : item.getRealAmt());
            }
        }
        if (CollectionUtils.isNotEmpty(refundNoNeedOrderItemList)) {
            for (OcBOrderItem item : refundNoNeedOrderItemList) {
                map.put(item.getId(), item);
                totalAmtActual = totalAmtActual.add(item.getRealAmt() == null ? BigDecimal.ZERO : item.getRealAmt());
            }
        }
        Map<String, IpBStandplatRefundItem> itemMap = Maps.newHashMap();
        BigDecimal refundFeeTotal = BigDecimal.ZERO;
        // 根据通用退单来查询通用退单明细
        List<IpBStandplatRefundItem> refundItems = refundItemMapper.selectRefundItemByRefundId(ipBStandplatRefund.getId());
        for (IpBStandplatRefundItem p : refundItems) {
            itemMap.put(p.getSubOrderId(), p);
            BigDecimal bigDecimal = Optional.ofNullable(p.getRefundFee()).orElse(BigDecimal.ZERO);
            refundFeeTotal = refundFeeTotal.add(bigDecimal);
        }

        List<OcBReturnAfSendItem> ocBReturnAfSendItems = new ArrayList<>();
        for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefunds) {
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(0L);
            ocBReturnAfSendItem.setRelationBillId(ocBReturnOrderRefund.getOcBReturnOrderId());
            List<OcBReturnOrder> list = ocBReturnOrders.stream().filter(p -> p.getId().equals(ocBReturnOrderRefund.getOcBReturnOrderId())).collect(Collectors.toList());
            ocBReturnAfSendItem.setRelationBillNo(list.get(0).getBillNo());
            //'单据类型  客退 0，拦截 1，拒收 2 ',
            ocBReturnAfSendItem.setBillType(1);
            //todo 拦截状态
            //ocBReturnAfSendItem.setInterceptStatus();
            //赠品
            ocBReturnAfSendItem.setGift(ocBReturnOrderRefund.getGiftType());
            ocBReturnAfSendItem.setPsCSkuId(ocBReturnOrderRefund.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(ocBReturnOrderRefund.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(ocBReturnOrderRefund.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(ocBReturnOrderRefund.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(ocBReturnOrderRefund.getPsCProId());
            // 邮费
            ocBReturnAfSendItem.setFreight(ipBStandplatRefund.getReturnShipamount());
            //申请退货数量
            ocBReturnAfSendItem.setQtyReturnApply(ocBReturnOrderRefund.getQtyRefund());
            ocBReturnAfSendItem.setAmtReturn(ocBReturnOrderRefund.getAmtRefund());
            ocBReturnAfSendItem.setPurchaseQty(ocBReturnOrderRefund.getQtyRefund());
            ocBReturnAfSendItem.setRelationBillItemId(ocBReturnOrderRefund.getOcBOrderItemId());
            OcBOrderItem orderItem = map.get(ocBReturnOrderRefund.getOcBOrderItemId());
            if (orderItem != null) {
                if (refundOrderService.isNullOrZero(ocBReturnAfSendItem.getQtyReturnApply())) {
                    ocBReturnAfSendItem.setQtyReturnApply(orderItem.getQty());
                }
                if (refundOrderService.isNullOrZero(ocBReturnAfSendItem.getAmtReturn())) {
                    ocBReturnAfSendItem.setAmtReturn(orderItem.getRealAmt());
                }
                ocBReturnAfSendItem.setPurchaseQty(orderItem.getQty());
                ocBReturnAfSendItem.setPtProName(orderItem.getPtProName());
                ocBReturnAfSendItem.setPsCSkuEname(orderItem.getPsCSkuEname());
                ocBReturnAfSendItem.setPsCSkuPtEcode(orderItem.getPsCSkuPtEcode());
                // BigDecimal realAmt = orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP).multiply(ocBReturnOrderRefund.getQtyRefund());
                ocBReturnAfSendItem.setAmtActual(orderItem.getRealAmt());

                ocBReturnAfSendItem.setOcBOrderItemId(orderItem.getId());
                if (ObjectUtil.isNotNull(orderItem.getOcBOrderId())) {
                    OcBOrder order = ocBOrderMapper.get4AfReturn(orderItem.getOcBOrderId());
                    if (ObjectUtil.isNotNull(order)) {
                        ocBReturnAfSendItem.setOcBOrderId(order.getId());
                        ocBReturnAfSendItem.setBusinessTypeCode(order.getBusinessTypeCode());
                        ocBReturnAfSendItem.setBusinessTypeId(order.getBusinessTypeId());
                        ocBReturnAfSendItem.setBusinessTypeName(order.getBusinessTypeName());
                    }
                }

            }
            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItems.add(ocBReturnAfSendItem);
        }
        if (CollectionUtils.isNotEmpty(refundNoNeedOrderItemList)) {
            // 根据订单明细来生成已发货退款单明细数据
            for (OcBOrderItem ocBOrderItem : refundNoNeedOrderItemList) {
                OcBOrder ocBOrder = ocBOrderMapper.get4NaiKaOrder(ocBOrderItem.getOcBOrderId());
                OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
                //关联类型
                ocBReturnAfSendItem.setRelationBillType(1L);
                ocBReturnAfSendItem.setRelationBillId(ocBOrderItem.getOcBOrderId());
                ocBReturnAfSendItem.setRelationBillNo(ocBOrder.getBillNo());
                //赠品
                ocBReturnAfSendItem.setGift(ocBOrderItem.getIsGift() + "");
                ocBReturnAfSendItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
                ocBReturnAfSendItem.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
                ocBReturnAfSendItem.setPsCProEcode(ocBOrderItem.getPsCProEcode());
                ocBReturnAfSendItem.setPsCProEname(ocBOrderItem.getPsCProEname());
                ocBReturnAfSendItem.setPsCProId(ocBOrderItem.getPsCProId());
                ocBReturnAfSendItem.setPtProName(ocBOrderItem.getPtProName());

                ocBReturnAfSendItem.setPurchaseQty(ocBOrderItem.getQty());
                ocBReturnAfSendItem.setAmtActual(ocBOrderItem.getRealAmt());
//                totalAmtActual = totalAmtActual.add(ocBOrderItem.getRealAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getRealAmt());
                IpBStandplatRefundItem refundItem =
                        Optional.ofNullable(itemMap.get(ocBOrderItem.getOoid())).orElse(new IpBStandplatRefundItem());
                BigDecimal refundFee = refundItem.getRefundFee() == null ? ocBOrderItem.getRealAmt() :
                        refundItem.getRefundFee();
                ocBReturnAfSendItem.setPsCSkuEname(ocBOrderItem.getPsCSkuEname());
                ocBReturnAfSendItem.setFreight(BigDecimal.ZERO);
                ocBReturnAfSendItem.setRelationBillItemId(ocBOrderItem.getId());
                ocBReturnAfSendItem.setPsCSkuPtEcode(ocBOrderItem.getPsCSkuPtEcode());
                ocBReturnAfSendItem.setGift(ocBOrderItem.getGiftType());
                BigDecimal priceActual = ocBOrderItem.getPriceActual();
                if (priceActual == null) {
                    priceActual = ocBOrderItem.getRealAmt().divide(ocBOrderItem.getQty(), 4,
                            BigDecimal.ROUND_HALF_UP);
                }
                if (Objects.isNull(refundItem.getReturnQuantity()) && !isNullOrZero(priceActual)) {
                    // 申请退货数量 余数进1
                    ocBReturnAfSendItem.setQtyReturnApply(refundFee.divide(priceActual, 0,
                            BigDecimal.ROUND_UP));
                } else {
                    // 申请退货数量
                    ocBReturnAfSendItem.setQtyReturnApply(refundItem.getReturnQuantity());
                }
                ocBReturnAfSendItem.setOcBOrderItemId(ocBOrderItem.getId());
                if (ObjectUtil.isNotNull(ocBOrderItem.getOcBOrderId())) {
                    if (ObjectUtil.isNotNull(ocBOrder)) {
                        ocBReturnAfSendItem.setOcBOrderId(ocBOrder.getId());
                        ocBReturnAfSendItem.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
                        ocBReturnAfSendItem.setBusinessTypeId(ocBOrder.getBusinessTypeId());
                        ocBReturnAfSendItem.setBusinessTypeName(ocBOrder.getBusinessTypeName());
                    }
                }
                OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
                ocBReturnAfSendItems.add(ocBReturnAfSendItem);
            }

            // 遍历
            log.info(LogUtil.format("buildOcBReturnAfSendItem.ocBReturnAfSendItems {}",
                    "buildOcBReturnAfSendItem.ocBReturnAfSendItems1"), JSON.toJSONString(ocBReturnAfSendItems));

            // 计算金额均摊
            List<OcBReturnAfSendItem> zeroMoreOcBReturnAfSendItems = ocBReturnAfSendItems.stream().filter(x -> x.getAmtActual().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            List<OcBReturnAfSendItem> zeroOcBReturnAfSendItems = ocBReturnAfSendItems.stream().filter(x -> x.getAmtActual().compareTo(BigDecimal.ZERO) <= 0).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(zeroOcBReturnAfSendItems)) {
                for (OcBReturnAfSendItem ocBReturnAfSendItem : zeroOcBReturnAfSendItems) {
                    ocBReturnAfSendItem.setAmtHasReturn(BigDecimal.ZERO);
                    ocBReturnAfSendItem.setAmtReturn(BigDecimal.ZERO);
                }
            }

            if (CollectionUtils.isNotEmpty(zeroMoreOcBReturnAfSendItems)) {
                // 判断是不是只有一个明细 如果只有一个明细则直接把已发货退款单主表的金额给到明细
                if (zeroMoreOcBReturnAfSendItems.size() == 1) {
                    zeroMoreOcBReturnAfSendItems.get(0).setAmtHasReturn(refundFeeTotal);
                    zeroMoreOcBReturnAfSendItems.get(0).setAmtReturn(refundFeeTotal);
                }
                BigDecimal subtractAmt = BigDecimal.ZERO;
                for (int i = 0; i < zeroMoreOcBReturnAfSendItems.size(); i++) {
                    OcBReturnAfSendItem returnAfSendItem = zeroMoreOcBReturnAfSendItems.get(i);
                    if (i == zeroMoreOcBReturnAfSendItems.size() - 1) {
                        BigDecimal refundFee = refundFeeTotal.subtract(subtractAmt);
                        if (refundFee.compareTo(BigDecimal.ZERO) < 0) {
                            returnAfSendItem.setAmtHasReturn(BigDecimal.ZERO);
                            returnAfSendItem.setAmtReturn(BigDecimal.ZERO);
                        } else {
                            returnAfSendItem.setAmtHasReturn(refundFee);
                            returnAfSendItem.setAmtReturn(refundFee);
                        }
                    } else {
                        BigDecimal ratio = returnAfSendItem.getAmtActual().divide(totalAmtActual, 4, BigDecimal.ROUND_HALF_UP);
                        BigDecimal refundFee = refundFeeTotal.multiply(ratio);
                        returnAfSendItem.setAmtHasReturn(refundFee);
                        returnAfSendItem.setAmtReturn(refundFee);
                        subtractAmt = subtractAmt.add(refundFee);
                    }
                }
            }
            ocBReturnAfSendItems = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(zeroMoreOcBReturnAfSendItems)) {
                ocBReturnAfSendItems.addAll(zeroMoreOcBReturnAfSendItems);
            }
            if (CollectionUtils.isNotEmpty(zeroOcBReturnAfSendItems)) {
                ocBReturnAfSendItems.addAll(zeroOcBReturnAfSendItems);
            }

            if (itemMap.size() == 1 && ocBReturnAfSendItems.size() == 1) {
                //通用退单中间表明细只有一条 并且 是普通商品
                for (OcBReturnAfSendItem sendItem : ocBReturnAfSendItems) {
                    if ("1".equals(sendItem.getGift())) {
                        sendItem.setAmtReturn(BigDecimal.ZERO);
                        sendItem.setAmtHasReturn(BigDecimal.ZERO);
                        continue;
                    }
                    sendItem.setAmtHasReturn(ocBReturnAfSend.getAmtReturnActual());
                    sendItem.setAmtReturn(ocBReturnAfSend.getAmtReturnActual());
                }
            }
            // 遍历
            log.info(LogUtil.format("buildOcBReturnAfSendItem.ocBReturnAfSendItems {}",
                    "buildOcBReturnAfSendItem.ocBReturnAfSendItems2"), JSON.toJSONString(ocBReturnAfSendItems));
        }
        return ocBReturnAfSendItems;
    }

    /**
     * 是空的或者是0
     *
     * @param arg
     * @return
     */
    public boolean isNullOrZero(BigDecimal arg) {
        return arg == null || BigDecimal.ZERO.compareTo(arg) == 0;
    }


    private List<OcBReturnAfSendItem> buildOcBReturnAfSendItem(List<OcBOrderItem> ocBOrderItems,
                                                               IpBStandplatRefund ipBStandplatRefund,
                                                               User user, OcBOrder ocBOrder) {
        //退款金额
        BigDecimal refundFee = ipBStandplatRefund.getRefundAmount();
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = new ArrayList<>();
        //存在拆数量的情况   将金额放在第一个明细上
        int i = 0;
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(1L);
            ocBReturnAfSendItem.setRelationBillId(ocBOrderItem.getOcBOrderId());
            //'单据类型  客退 0，拦截 1，拒收 2 ',
            //ocBReturnAfSendItem.setBillType(1L);
            //todo 拦截状态
            //ocBReturnAfSendItem.setInterceptStatus();
            //赠品
            ocBReturnAfSendItem.setRelationBillNo(ocBOrder.getBillNo());
            ocBReturnAfSendItem.setGift(ocBOrderItem.getIsGift() + "");
            ocBReturnAfSendItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(ocBOrderItem.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(ocBOrderItem.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(ocBOrderItem.getPsCProId());
            ocBReturnAfSendItem.setPtProName(ocBOrderItem.getPtProName());
            ocBReturnAfSendItem.setPurchaseQty(ocBOrderItem.getQty());
            ocBReturnAfSendItem.setAmtActual(ocBOrderItem.getRealAmt());
            if (i == 0) {
                ocBReturnAfSendItem.setAmtHasReturn(refundFee);
                ocBReturnAfSendItem.setAmtReturn(refundFee);
                i++;
            } else {
                ocBReturnAfSendItem.setAmtHasReturn(BigDecimal.ZERO);
                ocBReturnAfSendItem.setAmtReturn(BigDecimal.ZERO);
            }
            ocBReturnAfSendItem.setPsCSkuEname(ocBOrderItem.getPsCSkuEname());
            ocBReturnAfSendItem.setFreight(BigDecimal.ZERO);
            //todo 规格id
            //ocBReturnAfSendItem.setPsCSpecId(ocBReturnOrderRefund.);
            //todo 规格名称
            //ocBReturnAfSendItem.setPsCSpecEname();
            //申请退货数量
            ocBReturnAfSendItem.setRelationBillItemId(ocBOrderItem.getId());
            ocBReturnAfSendItem.setPsCSkuPtEcode(ocBOrderItem.getPsCSkuPtEcode());
            ocBReturnAfSendItem.setGift(ocBOrderItem.getGiftType());
            ocBReturnAfSendItem.setQtyReturnApply(ocBOrderItem.getQty());

            ocBReturnAfSendItem.setOcBOrderItemId(ocBOrderItem.getId());
            if (ObjectUtil.isNotNull(ocBOrderItem.getOcBOrderId())) {
                OcBOrder order = ocBOrderMapper.get4AfReturn(ocBOrderItem.getOcBOrderId());
                if (ObjectUtil.isNotNull(order)) {
                    ocBReturnAfSendItem.setOcBOrderId(order.getId());
                    ocBReturnAfSendItem.setBusinessTypeCode(order.getBusinessTypeCode());
                    ocBReturnAfSendItem.setBusinessTypeId(order.getBusinessTypeId());
                    ocBReturnAfSendItem.setBusinessTypeName(order.getBusinessTypeName());
                }
            }
            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItems.add(ocBReturnAfSendItem);
        }
        return ocBReturnAfSendItems;
    }

    /**
     * 处理发货后退款单的数据(退货退款)
     *
     * @param ocBReturnOrderRefunds
     * @param ocBOrder
     * @param ipBStandplatRefund
     * @return
     */
    public OcBReturnAfSendRelation standplatRefundAfSendToReturn(List<OcBReturnOrderRefund> ocBReturnOrderRefunds,
                                                                 OcBOrder ocBOrder,
                                                                 IpBStandplatRefund ipBStandplatRefund,
                                                                 User user, List<OcBOrderItem> orderItems,
                                                                 List<OcBReturnOrder> ocBReturnOrders,
                                                                 List<OcBOrderItem> refundNoNeedOrderItemList) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, ipBStandplatRefund,
                getRefundType(ipBStandplatRefund.getRefundType()), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItemRelation(ocBReturnOrderRefunds,
                ipBStandplatRefund, user, orderItems, ocBReturnOrders, refundNoNeedOrderItemList, ocBReturnAfSend);
        // 处理组合商品分摊
        setReturnAfSendItemsByRatio(orderItems, ocBReturnAfSendItems, ipBStandplatRefund);
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }

    /**
     * 通用平台退单状态转换为中台类型
     *
     * @param refundType
     * @return
     */
    int getRefundType(Integer refundType) {
        if (refundType == null) {
            return 0;
        }
        if (refundType == 2) {
            return TaobaoReturnOrderExt.SendBillType.RETURN_REFUND.getCode();
        }
        return refundType;
    }

    /**
     * 处理发货后退款单的数据(仅退款)
     *
     * @param ocBOrderItems
     * @param ocBOrder
     * @param ipBStandplatRefund
     * @return
     */
    public OcBReturnAfSendRelation standplatRefundAfSendToRefundOnly(List<OcBOrderItem> ocBOrderItems,
                                                                     OcBOrder ocBOrder,
                                                                     IpBStandplatRefund ipBStandplatRefund,
                                                                     User user) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, ipBStandplatRefund,
                TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode(), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItem(ocBOrderItems, ipBStandplatRefund, user, ocBOrder);
        // 处理组合商品分摊
        setReturnAfSendItemsByRatio(ocBOrderItems, ocBReturnAfSendItems, ipBStandplatRefund);
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }


    /**
     * 通过实体仓id查询该实体仓的退货仓id
     */
    public void selectReturnCPhyWarehouse(Long cPhyWarehouseId, OcBReturnOrder returnOrder, boolean isNeedOverSetWarehouse) {
        if (cPhyWarehouseId != null) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cPhyWarehouseId);
            if (cpCPhyWarehouse != null) {
                // 是否需要重设退仓：拿店仓档案中的"原退入库实体仓"
                if (isNeedOverSetWarehouse && Objects.nonNull(cpCPhyWarehouse.getOriginalReturnPhyWarehouseId())) {
                    returnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouse.getOriginalReturnPhyWarehouseId());
                }
                //查询退货逻辑仓
                Long cpCPhyWarehouseId = returnOrder.getCpCPhyWarehouseInId();
                StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
                storeInfoQueryRequest.setPhyId(cpCPhyWarehouseId);
                HashMap<Long, List<CpCStore>> storeInfoByPhyId = basicCpQueryService.getStoreInfoByPhyId(storeInfoQueryRequest);
                if (MapUtils.isNotEmpty(storeInfoByPhyId)) {
                    List<CpCStore> cpCStores = storeInfoByPhyId.get(cpCPhyWarehouseId);
                    CpCStore cpCStore = null;
                    if (CollectionUtils.isNotEmpty(cpCStores)) {
                        Optional<CpCStore> returnStore = cpCStores.stream().filter(x -> DrpStoreTypeEnum.TYPE_27.getValue().equals(x.getStoretype())).findFirst();
                        if (returnStore.isPresent()) {
                            cpCStore = returnStore.get();
                            returnOrder.setCpCStoreId(cpCStore.getId());
                            returnOrder.setCpCStoreEcode(cpCStore.getEcode());
                            returnOrder.setCpCStoreEname(cpCStore.getEname());
                        } else {
                            if (log.isDebugEnabled()) {
                                log.debug("{}, 未查询到退货类型的逻辑仓信息,wareId:{}", this.getClass().getName(), cpCPhyWarehouseId);
                            }
                        }
                    } else {
                        if (log.isDebugEnabled()) {
                            log.debug("{}, 通过实体仓ID未查询到逻辑仓信息,wareId:{}", this.getClass().getName(), cpCPhyWarehouseId);
                        }
                    }
                }
                Integer wmsControlWarehouse = cpCPhyWarehouse.getWmsControlWarehouse();
                if (wmsControlWarehouse != null && wmsControlWarehouse == 1) {
                    returnOrder.setIsNeedToWms(1L);
                }
            }
        }
    }

    public void setLogisticInfo(OcBReturnOrder returnOrder, String buyerLogisticName) {
        if (StringUtils.isNotEmpty(buyerLogisticName)) {
            LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(buyerLogisticName);
            if (logisticsInfo != null) {
                returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                returnOrder.setCpCLogisticsId(logisticsInfo.getId());
            }
        }
    }


    /**
     * 获取退单明细
     *
     * @param ipBStandplatRefund      中间表主表
     * @param ipBStandplatRefundItems 中间表明细
     * @param ocBOrderItems           订单明细
     * @param user                    操作员
     * @return
     */
    private List<OcBReturnOrderRefund> getReturnRefunds(IpBStandplatRefund ipBStandplatRefund,
                                                        List<IpBStandplatRefundItem> ipBStandplatRefundItems,
                                                        List<OcBOrderItem> ocBOrderItems,
                                                        User user) {
        ArrayList<OcBReturnOrderRefund> refunds = Lists.newArrayList();
        Map<String, List<OcBOrderItem>> itemMap = new HashMap<>();

        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            if (StringUtils.isNotBlank(ocBOrderItem.getOoid())) {
                List<OcBOrderItem> bOrderItems = itemMap.get(ocBOrderItem.getOoid());
                if (CollectionUtils.isNotEmpty(bOrderItems)) {
                    bOrderItems.add(ocBOrderItem);
                    itemMap.put(ocBOrderItem.getOoid(), bOrderItems);
                } else {
                    List<OcBOrderItem> bOrderItemList = new ArrayList<>();
                    bOrderItemList.add(ocBOrderItem);
                    itemMap.put(ocBOrderItem.getOoid(), bOrderItemList);
                }
            }
        }
        String orderNo = ipBStandplatRefund.getOrderNo();
        String returnNo = ipBStandplatRefund.getReturnNo();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("getReturnRefunds itemMap:{};ipBStandplatRefundItems:{};ipBStandplatRefund:{};ocBOrderItems{}",
                    "getReturnRefunds", orderNo, returnNo), JSONObject.toJSONString(itemMap),
                    JSONObject.toJSONString(ipBStandplatRefundItems),
                    JSONObject.toJSONString(ipBStandplatRefund), JSONObject.toJSONString(ocBOrderItems));
        }
        for (IpBStandplatRefundItem refundItem : ipBStandplatRefundItems) {
            List<OcBOrderItem> bOrderItems = itemMap.get(refundItem.getSubOrderId());
            if (CollectionUtils.isNotEmpty(bOrderItems)) {
                for (OcBOrderItem ocBOrderItem : bOrderItems) {
                if (Objects.isNull(ocBOrderItem)) {
                    continue;
                }
                BigDecimal price = this.getOcBorderItemSinglePrice(refundItem, ocBOrderItem);
                if (BigDecimal.ZERO.compareTo(price) == 0) {
                    continue;
                }
                // 退货单申请数量
                BigDecimal qty = BigDecimal.ZERO;
                // 退货单申请金额
                BigDecimal applyPrice = BigDecimal.ZERO;
                // 退货中间表明细,refundFee不为空
                if (refundItem.getRefundFee() != null && BigDecimal.ZERO.compareTo(refundItem.getRefundFee()) != 0) {
                    qty = refundItem.getRefundFee().divide(price, 0, BigDecimal.ROUND_UP);
                    applyPrice = ocBOrderItem.getRealAmt();
                }
                // 退货中间表明细 数量 不为空
                else if (refundItem.getReturnQuantity() != null && BigDecimal.ZERO.compareTo(refundItem.getReturnQuantity()) != 0) {
                    // 退货单申请金额
                    applyPrice = refundItem.getReturnQuantity().multiply(price);
                    qty = refundItem.getReturnQuantity();
                } else {
                    // 退货单申请金额
                    applyPrice = ocBOrderItem.getRealAmt();
                    qty = ocBOrderItem.getQty();
                }
                refunds.add(this.getRefundItem(ocBOrderItem, qty, applyPrice, ipBStandplatRefund, refundItem, user));
            }
        }

        }
        setReturnOrderItemsByRatio(ocBOrderItems, refunds, ipBStandplatRefund);
        if (refunds.size() != ocBOrderItems.size()) {
            List<OcBOrderItem> gift = this.getGift(ocBOrderItems);
            if (gift.size() == (ocBOrderItems.size() - refunds.size())) {
                for (OcBOrderItem ocBOrderItem : gift) {
                    refunds.add(this.getRefundItem(ocBOrderItem, ocBOrderItem.getQty(), BigDecimal.ZERO, ipBStandplatRefund, null, user));
                }
            }
        }
        return refunds;
    }


    /**
     * 获取单价
     *
     * @param refundItem
     * @return
     */
    private BigDecimal getOcBorderItemSinglePrice(IpBStandplatRefundItem refundItem, OcBOrderItem ocBOrderItem) {
        if (Objects.equals(refundItem.getSubOrderId(), ocBOrderItem.getOoid())) {
            return ocBOrderItem.getRealAmt().divide(ocBOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP);
        }
        return BigDecimal.ZERO;
    }


    /**
     * @param orderItem 订单详情
     * @param qty       申请数量
     * @param price     退单金额
     * @param user      操作人
     * @return
     */
    private OcBReturnOrderRefund getRefundItem(OcBOrderItem orderItem, BigDecimal qty, BigDecimal price,
                                               IpBStandplatRefund ipBStandplatRefund, IpBStandplatRefundItem refundItem,
                                               User user) {
        boolean isQtyChange = false;
        BigDecimal refundQty = qty;
        if (qty.compareTo(orderItem.getQty()) > 0) {
            qty = orderItem.getQty();
            isQtyChange = true;
        }
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        // 退单金额
        returnOrderRefund.setAmtRefund(price);
        // 申请数量
        returnOrderRefund.setQtyRefund(qty);
        //商品名称
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());

        returnOrderRefund.setPrice(orderItem.getPrice());
        returnOrderRefund.setPriceList(orderItem.getPriceList());
        //1 qty_can_refund 购买数量 合计所有明细qty
        returnOrderRefund.setQtyCanRefund(orderItem.getQty());
        //商品单价
        returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
        //国标码
        returnOrderRefund.setBarcode(orderItem.getBarcode());
        //修改人用户名
        returnOrderRefund.setModifierename(orderItem.getModifierename());
        //商品规格
        returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setOid(orderItem.getOoid());
        //条码id
        returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCProId(orderItem.getPsCProId());
        //颜色尺寸
        returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
        returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());
        returnOrderRefund.setTid(orderItem.getTid());
        returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
        returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
        returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
        returnOrderRefund.setSex(orderItem.getSex());
        returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
        returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
        returnOrderRefund.setGiftType(orderItem.getGiftType());
        returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
        returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                4, BigDecimal.ROUND_HALF_UP));
        returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");

        if (Objects.nonNull(refundItem) && Objects.nonNull(refundItem.getSubOrderId()) && Objects.equals(orderItem.getOoid(), refundItem.getSubOrderId())) {
            Integer returnStatus = ipBStandplatRefund.getReturnStatus();
            String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
            returnOrderRefund.setRefundStatus(status);
            returnOrderRefund.setRefundBillNo(ipBStandplatRefund.getReturnNo());
            returnOrderRefund.setAmtPtRefund(ipBStandplatRefund.getRefundAmount());
        }
        OperateUserUtils.saveOperator(returnOrderRefund, user);
        return returnOrderRefund;
    }

    /**
     * 处理组合商品 (按照比例进行分摊)
     *
     * @param orderItemList         订单子表明细
     * @param returnOrderRefundList 退单明细
     * @param ipBStandplatRefund    通用订单中间表
     */
    private void setReturnOrderItemsByRatio(List<OcBOrderItem> orderItemList,
                                            List<OcBReturnOrderRefund> returnOrderRefundList,
                                            IpBStandplatRefund ipBStandplatRefund) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("setReturnOrderItemsByRatio.before.orderItemList={};ipBStandplatRefund={}",
                    "setReturnOrderItemsByRatio.before"), JSON.toJSONString(orderItemList), JSON.toJSONString(returnOrderRefundList));
        }
        List<OcBOrderItem> combineOrderItemList =
                orderItemList.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(combineOrderItemList)) {
            Map<Long, OcBOrderItem> combineOrderItemListMap = combineOrderItemList.stream()
                    .collect(Collectors.toMap(OcBOrderItem::getId, t -> t, (v, v1) -> v1));

            BigDecimal refundAmount = ipBStandplatRefund.getRefundAmount();
            BigDecimal remainRefundAmount = ipBStandplatRefund.getRefundAmount();
            for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefundList) {
                if (StringUtils.isNotBlank(returnOrderRefund.getGiftType()) && !"0".equals(returnOrderRefund.getGiftType())) {
                    returnOrderRefund.setAmtPtRefund(BigDecimal.ZERO);
                    continue;
                }
                OcBOrderItem ocBOrderItem = combineOrderItemListMap.get(returnOrderRefund.getOcBOrderItemId());
                if (Objects.isNull(ocBOrderItem)) {
                    return;
                }
                BigDecimal amtPtRefund = refundAmount.multiply(Optional.ofNullable(ocBOrderItem.getGroupRadio())
                        .orElse(new BigDecimal("0"))).setScale(OcBOrderConst.DECIMAL_QTY_FOUR, BigDecimal.ROUND_HALF_UP);
                returnOrderRefund.setAmtPtRefund(amtPtRefund);
                remainRefundAmount = remainRefundAmount.subtract(amtPtRefund);
            }

            if (remainRefundAmount.compareTo(new BigDecimal("0")) != 0) {
                Collections.sort(returnOrderRefundList, (o1, o2) -> o2.getAmtPtRefund().compareTo(o1.getAmtPtRefund()));
                OcBReturnOrderRefund returnOrderRefund = returnOrderRefundList.get(0);
                returnOrderRefund.setAmtPtRefund(returnOrderRefund.getAmtPtRefund().add(remainRefundAmount));
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("setReturnOrderItemsByRatio.after={}",
                        "setReturnOrderItemsByRatio.after"), JSON.toJSONString(returnOrderRefundList));
            }
        }
    }

    /**
     * 处理组合商品 (按照比例进行分摊)
     *
     * @param orderItemList           订单子表明细
     * @param ocBReturnAfSendItemList 已发货退款单明细
     * @param ipBStandplatRefund      通用订单中间表
     */
    public void setReturnAfSendItemsByRatio(List<OcBOrderItem> orderItemList,
                                            List<OcBReturnAfSendItem> ocBReturnAfSendItemList,
                                            IpBStandplatRefund ipBStandplatRefund) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("setReturnAfSendItemsByRatio.before.orderItemList={};ipBStandplatRefund={}",
                    "setReturnAfSendItemsByRatio.before"), JSON.toJSONString(orderItemList), JSON.toJSONString(ocBReturnAfSendItemList));
        }
        List<OcBOrderItem> combineOrderItemList =
                orderItemList.stream().filter(p -> p.getProType() == SkuType.COMBINE_PRODUCT).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(combineOrderItemList)) {
            Map<Long, OcBOrderItem> combineOrderItemListMap = combineOrderItemList.stream()
                    .collect(Collectors.toMap(OcBOrderItem::getId, t -> t, (v, v1) -> v1));

            BigDecimal refundAmount = ipBStandplatRefund.getRefundAmount();
            BigDecimal remainRefundAmount = ipBStandplatRefund.getRefundAmount();
            for (OcBReturnAfSendItem ocBReturnAfSendItem : ocBReturnAfSendItemList) {
                if (StringUtils.isNotBlank(ocBReturnAfSendItem.getGift()) && !"0".equals(ocBReturnAfSendItem.getGift())) {
                    ocBReturnAfSendItem.setAmtReturn(BigDecimal.ZERO);
                    ocBReturnAfSendItem.setAmtHasReturn(BigDecimal.ZERO);
                    continue;
                }
                OcBOrderItem ocBOrderItem = combineOrderItemListMap.get(ocBReturnAfSendItem.getRelationBillItemId());
                if (Objects.isNull(ocBOrderItem)) {
                    return;
                }
                BigDecimal amtPtRefund = refundAmount.multiply(Optional.ofNullable(ocBOrderItem.getGroupRadio())
                        .orElse(new BigDecimal("0"))).setScale(OcBOrderConst.DECIMAL_QTY_FOUR, BigDecimal.ROUND_HALF_UP);
                ocBReturnAfSendItem.setAmtReturn(amtPtRefund);
                ocBReturnAfSendItem.setAmtHasReturn(amtPtRefund);
                remainRefundAmount = remainRefundAmount.subtract(amtPtRefund);
            }

            if (remainRefundAmount.compareTo(new BigDecimal("0")) != 0) {
                Collections.sort(ocBReturnAfSendItemList, (o1, o2) -> o2.getAmtReturn().compareTo(o1.getAmtReturn()));
                OcBReturnAfSendItem ocBReturnAfSendItem = ocBReturnAfSendItemList.get(0);
                ocBReturnAfSendItem.setAmtReturn(ocBReturnAfSendItem.getAmtReturn().add(remainRefundAmount));
                ocBReturnAfSendItem.setAmtHasReturn(ocBReturnAfSendItem.getAmtReturn());
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("setReturnAfSendItemsByRatio.after={}",
                        "setReturnAfSendItemsByRatio.after"), JSON.toJSONString(ocBReturnAfSendItemList));
            }
        }
    }


    /**
     * 过滤出赠品
     *
     * @return
     */
    private List<OcBOrderItem> getGift(List<OcBOrderItem> items) {
        List<OcBOrderItem> gifts = Lists.newArrayList();
        if (CollectionUtils.isEmpty(items)) {
            return gifts;
        }
        for (OcBOrderItem item : items) {
            if (Objects.equals(item.getIsGift(), 1) && item.getProType() != SkuType.NO_SPLIT_COMBINE) {
                gifts.add(item);
            }
        }
        return gifts;
    }


    /**
     * 构建退换货单信息
     *
     * @param orderRelation
     * @param ipBStandplatRefund
     * @param ipBStandplatRefundItems
     * @param user
     * @return
     */
    public OcBReturnOrderRelation buildRefundOrderInfo(List<OmsOrderRelation> orderRelation,
                                                       IpBStandplatRefund ipBStandplatRefund,
                                                       List<IpBStandplatRefundItem> ipBStandplatRefundItems,
                                                       User user) {
        OmsOrderRelation omsOrderRelation = null;
        List<OcBReturnOrderRelation> orderRelations = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderRelation)) {
            omsOrderRelation = orderRelation.get(0);
        }
        //生成主表数据
        OcBReturnOrder ocBReturnOrder = buildReturnOrder(ipBStandplatRefund, TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode(), user);

        List<OcBReturnOrderRefund> orderRefunds = new ArrayList<>();
        for (IpBStandplatRefundItem standplatRefundItem : ipBStandplatRefundItems) {
            OcBReturnOrderRefund ocBReturnOrderRefund = buildReturnOrderRefund(ipBStandplatRefund, standplatRefundItem, user);
            orderRefunds.add(ocBReturnOrderRefund);
        }
        if (OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN.getCode().equals(ocBReturnOrder.getBusinessTypeCode()) ||
                OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP_RETURN.getCode().equals(ocBReturnOrder.getBusinessTypeCode())) {
            for (OcBReturnOrderRefund returnOrderRefund : orderRefunds) {
                returnOrderRefund.setAmtRefundSingle(new BigDecimal("0.01"));
                returnOrderRefund.setAmtRefund(returnOrderRefund.getAmtRefundSingle().multiply(returnOrderRefund.getQtyRefund()));
                returnOrderRefund.setPriceSettle(new BigDecimal("0.01"));
                returnOrderRefund.setAmtSettleTot(returnOrderRefund.getPriceSettle().multiply(returnOrderRefund.getQtyRefund()));
            }
        }
        this.getAllSku(orderRefunds, ocBReturnOrder);
        OcBReturnOrderRelation relation = new OcBReturnOrderRelation();
        relation.setReturnOrderInfo(ocBReturnOrder);
        relation.setOrderRefundList(orderRefunds);
        return relation;
    }

    /**
     * 构建退换货单主表信息
     *
     * @param ipBStandplatRefund
     * @param proType
     * @param user
     * @return
     */
    public OcBReturnOrder buildReturnOrder(IpBStandplatRefund ipBStandplatRefund,
                                           Integer proType, User user) {
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        returnOrder.setTid(ipBStandplatRefund.getOrderNo());
        //平台退款单号
        returnOrder.setReturnId(ipBStandplatRefund.getReturnNo());
        //卖家昵称
        returnOrder.setBuyerNick(ipBStandplatRefund.getBuyerNick());
        //申请退款时间
        returnOrder.setReturnCreateTime(ipBStandplatRefund.getCreated());
        //最后修改时间
        returnOrder.setLastUpdateTime(ipBStandplatRefund.getModified());
        //退款说明
        returnOrder.setReturnDesc(ipBStandplatRefund.getReturnReason());
        //商品应退金额(
        returnOrder.setReturnAmtList(ipBStandplatRefund.getRefundAmount());
        returnOrder.setBuyerRemark(ipBStandplatRefund.getBuyerRemark());
        //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
        returnOrder.setReturnAmtActual(ipBStandplatRefund.getRefundAmount());
        String companyName = ipBStandplatRefund.getCompanyName();
        //退回物流单号
        returnOrder.setLogisticsCode(ipBStandplatRefund.getLogisticsNo());
        returnOrder.setCpCLogisticsEname(companyName);
        this.setLogisticInfo(returnOrder, companyName);
        returnOrder.setCreationdate(new Date());
        returnOrder.setBillNo(sequenceUtil.buildReturnBillNo());
        //等待退货入库(PRD数据对象)
        returnOrder.setReturnStatus(TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode());
        //退还运费，默认0
        returnOrder.setReturnAmtShip(BigDecimal.ZERO);
        //退还其他费用，默认0
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);
        //换货金额
        returnOrder.setExchangeAmt(BigDecimal.ZERO);
        //是否传AG默认否
        returnOrder.setIsToag(AGStatusEnum.INIT.getVal());
        //是否生成调拨单，默认0
        returnOrder.setIsTransfer(0);
        //是否生成零售，默认0
        returnOrder.setIsTodrp(0);
        //退单状态，默认20
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //是否手工新增，默认0
        returnOrder.setIsAdd(0);
        //虚拟入库状态，默认0
        returnOrder.setInventedStatus(0);
        //是否原退，默认0
        returnOrder.setIsRefund(0);
        //是否确认收货，默认0
        returnOrder.setIsReceiveConfirm(0);
        //WMS撤回状态，默认0
        returnOrder.setWmsCancelStatus(0);
        //强制入库，默认0
        returnOrder.setIsForce(0);
        //是否手工审核，默认0
        returnOrder.setIsManualAudit(0);
        //是否传WMS
        returnOrder.setIsTowms(0);
        //是否入仓成功
        returnOrder.setIsInstorage(0);
        //换货人姓名
        returnOrder.setReceiveName(ipBStandplatRefund.getExchangeReceiverName());
        //换货人手机
        returnOrder.setReceiveMobile(ipBStandplatRefund.getExchangeReceiverMobile());
        //邮编
        returnOrder.setReceiveZip(ipBStandplatRefund.getExchangeReceiverZip());
        //发货仓库,根据编码查询实体仓
        Long cpCPhyWarehouseId = null;
        CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.selectPhyWarehouseByEcode(ipBStandplatRefund.getSendBackStoreCode());
        if (cpCPhyWarehouse != null) {
            cpCPhyWarehouseId = cpCPhyWarehouse.getId();
        } else {
            StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ipBStandplatRefund.getCpCShopId());
            Integer isMultiReturnWarehouse = shopStrategy.getIsMultiReturnWarehouse();
            if (isMultiReturnWarehouse == null || isMultiReturnWarehouse == 0) {
                cpCPhyWarehouseId = shopStrategy.getCpCWarehouseDefId();
            }
        }
        if (cpCPhyWarehouseId == null) {
            throw new NDSException("查询实体仓信息为空！PhyWarehouseCode=" + ipBStandplatRefund.getSendBackStoreCode());
        }
        returnOrder.setCpCPhyWarehouseId(cpCPhyWarehouseId);
        returnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouseId);
        this.selectReturnCPhyWarehouse(cpCPhyWarehouseId, returnOrder, false);

        //平台类型
        returnOrder.setPlatform(ipBStandplatRefund.getCpCPlatformId().intValue());
        returnOrder.setReceiveAddress(ipBStandplatRefund.getExchangeReceiverAddress());
        //店铺id
        returnOrder.setCpCShopId(ipBStandplatRefund.getCpCShopId());
        returnOrder.setCpCShopTitle(ipBStandplatRefund.getCpCShopTitle());
        returnOrder.setCpCShopEcode(ipBStandplatRefund.getCpCShopEcode());
        returnOrder.setReceiverProvinceName(ipBStandplatRefund.getExchangeReceiverProvince());
        returnOrder.setReceiverCityName(ipBStandplatRefund.getExchangeReceiverCity());
        returnOrder.setReceiverAreaName(ipBStandplatRefund.getExchangeReceiverDistrict());
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        returnOrder.setReturnProType(proType);
        returnOrder.setOriginalBillNo(ipBStandplatRefund.getOriginalBillNo());
        returnOrder.setCostCenter(ipBStandplatRefund.getCostCenter());
        returnOrder.setSalesOrganize(ipBStandplatRefund.getSalesOrganize());
        //来源系统
        returnOrder.setReserveVarchar05(ipBStandplatRefund.getReserveVarchar05());
        returnOrder.setDeliveryOrderNo(ipBStandplatRefund.getDeliveryOrderNo());
        // 退款类型等于 退货退款时,从中间表取物流信息
        if (Objects.nonNull(ipBStandplatRefund)
                && Objects.equals(ipBStandplatRefund.getRefundType(), IpBStandplatRefundType.RETURN_GOODS_RERUEN)) {
            // 假如退货物流单号不为空！ 并且和之前的物流单号不同
            if (StringUtils.isNotBlank(ipBStandplatRefund.getLogisticsNo())) {
                try {
                    LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(ipBStandplatRefund.getCompanyName());
                    if (Objects.nonNull(logisticsInfo)) {
                        returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                        returnOrder.setCpCLogisticsId(logisticsInfo.getId());
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("错误信息:{}"), Throwables.getStackTraceAsString(e));
                }
                returnOrder.setLogisticsCode(ipBStandplatRefund.getLogisticsNo());
                returnOrder.setCpCLogisticsEname(ipBStandplatRefund.getCompanyName());
            }
        }
        returnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());
        returnOrder.setInventedStatus(0); //未发起拦截
        returnOrder.setIsWrongReceive(IsWrongReceive.NO.val());
        returnOrder.setPlatformRefundStatus(OcOmsReturnOrderConstant.PLATFORM_REFUND_STATUS_INIT);
        ocSaveChangingOrRefundingService.checkBillType(returnOrder);
        OperateUserUtils.saveOperator(returnOrder, user);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("构建退货单主表数据:{}", "buildReturnOrderInfo"), JSONObject.toJSONString(returnOrder));
        }
        return returnOrder;
    }


    /**
     * 构建退换货单明细数据
     *
     * @param ipBStandplatRefund
     * @param standplatRefundItem
     * @param user
     * @return
     */
    public OcBReturnOrderRefund buildReturnOrderRefund(IpBStandplatRefund ipBStandplatRefund,
                                                       IpBStandplatRefundItem standplatRefundItem,
                                                       User user) {
        //发货信息
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        //总调整金额
        returnOrderRefund.setAmtRefund(standplatRefundItem.getTcAdjustFee());
        //returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        //申请数量
        returnOrderRefund.setQtyRefund(standplatRefundItem.getReturnQuantity());
        Integer returnStatus = ipBStandplatRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        returnOrderRefund.setRefundStatus(status);
        returnOrderRefund.setAmtPtRefund(ipBStandplatRefund.getRefundAmount());
        //商品名称
        returnOrderRefund.setPsCProEname(standplatRefundItem.getTitle());
        returnOrderRefund.setPrice(standplatRefundItem.getPrice());
        //1 qty_can_refund 购买数量 合计所有明细qty
        returnOrderRefund.setQtyCanRefund(standplatRefundItem.getQuantity());
        //商品单价
        returnOrderRefund.setAmtAdjust(standplatRefundItem.getPrice());
        //修改人用户名
        returnOrderRefund.setModifierename(user.getEname());
        //商品规格
        //returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setOid(standplatRefundItem.getSubOrderId());
        returnOrderRefund.setTid(ipBStandplatRefund.getOrderNo());
        //条码id
        ProductSku productSku = psRpcService.selectProductSku(standplatRefundItem.getSku());
        if (productSku == null) {
            throw new NDSException("查询商品条码信息失败");
        }
        returnOrderRefund.setPsCSkuId(productSku.getId());
        returnOrderRefund.setPsCSkuEcode(productSku.getSkuEcode());
        returnOrderRefund.setPsCSkuEname(productSku.getSkuName());
        //国标码
        returnOrderRefund.setBarcode(productSku.getBarcode69());
        //returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
        //returnOrderRefund.setGiftType(orderItem.getGiftType());
        returnOrderRefund.setPsCProEname(standplatRefundItem.getTitle());
        returnOrderRefund.setPsCProEcode(productSku.getProdCode());
        returnOrderRefund.setPsCProId(productSku.getProdId());
        //颜色尺寸
        returnOrderRefund.setPsCSizeEcode(productSku.getSizeCode());
        returnOrderRefund.setPsCSizeEname(productSku.getSizeName());
        returnOrderRefund.setPsCSizeId(productSku.getSizeId());

        returnOrderRefund.setPsCClrEcode(productSku.getColorCode());
        returnOrderRefund.setPsCClrEname(productSku.getColorName());
        returnOrderRefund.setPsCClrId(productSku.getColorId());
        returnOrderRefund.setSex(productSku.getSex());
        returnOrderRefund.setRefundBillNo(ipBStandplatRefund.getReturnNo());
        //设置默认值
        returnOrderRefund.setQtyIn(BigDecimal.ZERO);
        returnOrderRefund.setQtyMatch(0L);
        //returnOrderRefund.setOcBOrderId(ocBOrder.getId());
        //returnOrderRefund.setAmtRefundSingle(standplatRefundItem.getRefundFee().divide(standplatRefundItem.getReturnQuantity()==null?BigDecimal.ONE:standplatRefundItem.getReturnQuantity(),
        //        4, BigDecimal.ROUND_HALF_UP));
        returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");
        returnOrderRefund.setDeliveryOrderNumber(standplatRefundItem.getDeliveryOrderNumber());
        OperateUserUtils.saveOperator(returnOrderRefund, user);
        return returnOrderRefund;
    }

    /**
     * 保存退换货单
     *
     * @param orderRelation
     * @param operateUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long insertOmsReturnOrderInfo(OcBReturnOrderRelation orderRelation, User operateUser) {
        if (orderRelation == null) {
            throw new NDSException("数据异常!");
        }
        OcBReturnOrder returnOrderInfo = orderRelation.getReturnOrderInfo();
        List<OcBReturnOrderRefund> orderRefundList = orderRelation.getOrderRefundList();
        if (returnOrderInfo == null) {
            throw new NDSException("主表数据为空!");
        }
        if (CollectionUtils.isEmpty(orderRefundList)) {
            throw new NDSException("明细数据为空!");
        }
        long orderId = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER);
        returnOrderInfo.setBillNo(sequenceUtil.buildReturnBillNo()); // 退单编号
        returnOrderInfo.setId(orderId);
        int result = orderMapper.insert(returnOrderInfo);

        for (OcBReturnOrderRefund returnOrderRefund : orderRefundList) {
            long refundId = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND);
            returnOrderRefund.setId(refundId);
            returnOrderRefund.setOcBReturnOrderId(orderId);
        }
        orderRefundMapper.batchInsert(orderRefundList);
        //跳过所有验证 直接生成入库通知单
        //omsReturnOrderConfirmService.createInNotices(returnOrderInfo,orderRefundList,operateUser);
        return orderId;
    }

    /**
     * 匹配SAP对应的退单业务类型
     */
    public List<OcBOrderItem> buildOrderItems(List<IpBStandplatRefundItem> ipBStandplatRefundItem) {
        List<OcBOrderItem> orderItemList = new ArrayList<>();
        for (IpBStandplatRefundItem refundItem : ipBStandplatRefundItem) {
            OcBOrderItem ocBOrderItem = new OcBOrderItem();
            ocBOrderItem.setId(refundItem.getId());
            ocBOrderItem.setPsCSkuEcode(refundItem.getSku());
            ocBOrderItem.setRealAmt(refundItem.getRefundFee());
            orderItemList.add(ocBOrderItem);
        }
        return orderItemList;
    }

    /**
     * 获取退单中间表所属平台
     *
     * @param ipBStandplatRefund
     * @return
     */
    public Integer getStandplatRefundPlatmform(IpBStandplatRefund ipBStandplatRefund) {
        Integer platmform;
        String reserveVarchar05 = ipBStandplatRefund.getReserveVarchar05();
        if (StringUtils.isNotEmpty(reserveVarchar05)) {
            platmform = Integer.valueOf(reserveVarchar05);
        } else {
            platmform = Integer.valueOf(ipBStandplatRefund.getCpCPlatformEcode());
        }
        return platmform;
    }


}

