package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.ip.model.taobao.LogisticsConsignResendModel;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: ganquan
 * @Date Create In 2020/7/6 16:31
 * @Description: 修改平台物流
 */
@Component
@Slf4j
public class OmsOrderPlatformLogisticsResendService {

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private OmsOrderItemService omsOrderItemServie;

    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;

    /**
     * @param ocBOrder
     * @return void
     * <AUTHOR>
     * @Description 修改平台物流(目前只适用于淘宝)
     * @Date 16:35 2020/7/6
     **/
    public boolean callOrderPlatformLogisticsResend(OcBOrder ocBOrder) {
        //修改物流是否成功
        Boolean flag = false;
        if (ocBOrder.getIsMultiPack() != null && ocBOrder.getIsMultiPack().equals(1L)
                && PlatFormEnum.TAOBAO.getCode().equals(ocBOrder.getPlatform())) {
            List<String> expressCodeList = new ArrayList<>();
            List<OcBOrderItem> ocBOrderItemList = omsOrderItemServie.selectUnSuccessRefund(ocBOrder.getId());
            List<OcBOrderDelivery> ocBOrderDeliveryList = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
            for (OcBOrderDelivery ocBOrderDelivery : ocBOrderDeliveryList) {
                String expressCode = ocBOrderDelivery.getLogisticNumber();
                if (expressCodeList.size() > 0) {
                    if (!expressCodeList.contains(expressCode)) {
                        expressCodeList.add(expressCode);
                    }
                } else {
                    expressCodeList.add(expressCode);
                }
            }
            //调用多包裹修改物流方法
            flag = callMultiPackageInterface(ocBOrder, ocBOrderItemList, expressCodeList);
        } else {
            //调用非多包裹修改物流方法
            flag = logisticsResend(ocBOrder);
        }
        return flag;
    }

    /**
     * @param ocBOrder
     * @return void
     * <AUTHOR>
     * @Description 二次物流更新，平台发货状态的订单需要更新物流信息(淘宝)
     * @Date 14:48 2020/7/6
     **/
    private boolean logisticsResend(OcBOrder ocBOrder) {
        LogisticsConsignResendModel model = new LogisticsConsignResendModel();
        if (StringUtils.isNotEmpty(ocBOrder.getTid())) {
            model.setTid(Long.valueOf(ocBOrder.getTid()));
        }
        if (StringUtils.isNotEmpty(ocBOrder.getCpCLogisticsEcode())) {
            model.setCompanyCode(ocBOrder.getCpCLogisticsEcode());
        }
        if (StringUtils.isNotEmpty(ocBOrder.getExpresscode())) {
            model.setOutSid(ocBOrder.getExpresscode());
        }
        ValueHolderV14 valueHolderV14 = ipRpcService.logisticsConsignResendForTaoBao(model);
        if (valueHolderV14 != null && valueHolderV14.getCode() == 0) {
            return true;
        }
        return false;
    }

    /**
     * @param ocBOrder
     * @param ocBOrderItemList
     * @param expressCodeList
     * @return void
     * <AUTHOR>
     * @Description 二次物流多包裹更新，平台发货状态的订单需要更新物流信息(淘宝)
     * @Date 16:53 2020/7/6
     **/
    private boolean callMultiPackageInterface(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList, List<String> expressCodeList) {
        long tid = Long.valueOf(ocBOrder.getTid());
        boolean flag = true;
        try {
            // 包裹数、订单明细数 最小值进行依次上传。
            if (ocBOrderItemList.size() >= expressCodeList.size()) {
                // 按照包裹数进行处理；每次处理一个订单明细；余下的订单明细，则统一生成新的Oid进行处理，有可能是多单合并成新单的数据；
                for (int i = 0; i < expressCodeList.size(); i++) {
                    try {
                        Thread.sleep(2000);
                    } catch (Exception e) {
                    }
                    String expressCode = expressCodeList.get(i);
                    if (i + 1 == expressCodeList.size()) {
                        String newOid = "";
                        if (ocBOrderItemList.size() > 0) {
                            newOid = buildDistinctOrderItemOid(ocBOrderItemList, newOid);
                            flag = logisticsResendMultiPackage(newOid, tid, expressCode, ocBOrder);
                        }
                    } else {
                        OcBOrderItem ocBOrderItem = ocBOrderItemList.get(0);
                        String oid = "";
                        oid = ocBOrderItem.getOoid();
                        flag = logisticsResendMultiPackage(oid, tid, expressCode, ocBOrder);
                        ocBOrderItemList.remove(ocBOrderItem);
                    }
                    if (!flag) {
                        return false;
                    }
                }
            } else {
                for (int j = 0; j < ocBOrderItemList.size(); j++) {
                    try {
                        Thread.sleep(2000);
                    } catch (Exception e) {
                    }
                    String oid = ocBOrderItemList.get(j).getOoid();
                    String expressCode = expressCodeList.get(j);
                    flag = logisticsResendMultiPackage(oid, tid, expressCode, ocBOrder);
                    if (!flag) {
                        return false;
                    }
                }
            }
        } catch (Exception e) {
            flag = false;
            log.error(LogUtil.format("多包裹修改物流接口，请求云枢纽报错,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }
        return flag;
    }

    /**
     * @param oid
     * @param tid
     * @param expressCode
     * @param ocBOrder
     * @return boolean
     * <AUTHOR>
     * @Description 淘宝物流修改(分包裹)
     * @Date 17:08 2020/7/6
     **/
    private boolean logisticsResendMultiPackage(String oid, Long tid, String expressCode, OcBOrder ocBOrder) {
        Long isSplitPackage = 1L;
        LogisticsConsignResendModel model = new LogisticsConsignResendModel();
        model.setTid(tid);
        model.setIsSplit(isSplitPackage);
        if (StringUtils.isNotEmpty(oid)) {
            model.setSubTid(oid);
        }
        if (StringUtils.isNotEmpty(ocBOrder.getCpCLogisticsEcode())) {
            model.setCompanyCode(ocBOrder.getCpCLogisticsEcode());
        }
        if (StringUtils.isNotEmpty(expressCode)) {
            model.setOutSid(expressCode);
        }
        ValueHolderV14 valueHolderV14 = ipRpcService.logisticsConsignResendForTaoBao(model);
        if (valueHolderV14 != null && valueHolderV14.getCode() == 0) {
            return true;
        }
        return false;
    }

    /**
     * 按照订单明细，拼接生成新的Oid
     *
     * @param ocBOrderItemList 订单明细
     * @param newOid
     * @return
     */
    private String buildDistinctOrderItemOid(List<OcBOrderItem> ocBOrderItemList, String newOid) {
        List<String> tmpList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(ocBOrderItem.getIsGift())) {
                continue;
            }
            if (!tmpList.contains(ocBOrderItem.getOoid())) {
                tmpList.add(ocBOrderItem.getOoid());
            }
        }
        if (CollectionUtils.isNotEmpty(tmpList)) {
            newOid = StringUtils.join(tmpList, ",");
        }
        return newOid;
    }
}
