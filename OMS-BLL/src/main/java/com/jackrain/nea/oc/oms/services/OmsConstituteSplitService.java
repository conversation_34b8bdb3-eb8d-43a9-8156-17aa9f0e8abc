package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.psext.model.table.ExtractLuckyBag;
import com.jackrain.nea.psext.model.table.SingleProInfo;
import com.jackrain.nea.psext.model.table.SplitVirtualPro;
import com.jackrain.nea.psext.request.VirtualProSplitRequest;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2019-07-16 19:12
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsConstituteSplitService {
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private PropertiesConf propertiesConf;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    //组合商品标识前缀
    private static final String GROUP_GOODS_MARK = "CG";
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;


    /**
     * 存在组合商品或者福袋商品的处理方法
     * 将组合商品和福袋商品转换成标准的商品
     */
    public boolean startExchangeCombineGiftProduct(OcBOrderRelation orderInfo, User operateUser) {

        OmsConstituteSplitService bean = ApplicationContextHandle.getBean(OmsConstituteSplitService.class);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start.checkOrderItemIsCombination.Request={},OrderId=",
                    orderInfo.getOrderId()), JSONObject.toJSONString(orderInfo));
        }
        //获取订单主表信息
        OcBOrder order = orderInfo.getOrderInfo();
        try {
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
            //存放不是组合商品或者福袋商品的的明细数据
            List<OcBOrderItem> normalProdItemList = new ArrayList<>();
            //存放组合或者福袋商品的明细
            List<OcBOrderItem> combineGiftProdItemList = new ArrayList<>();
            for (OcBOrderItem orderItemInfo : orderItemList) {
                int prodType = orderItemInfo.getProType() == null ? 0 : orderItemInfo.getProType().intValue();
                if (prodType == SkuType.NO_SPLIT_COMBINE || prodType == SkuType.GIFT_PRODUCT
                        || prodType == SkuType.COMBINE_PRODUCT) {
                    combineGiftProdItemList.add(orderItemInfo);
                } else {
                    normalProdItemList.add(orderItemInfo);
                }
            }

            List<OcBOrderItem> afterExchangeCombineGiftProdList = bean.encapsulationParameter(combineGiftProdItemList,
                    order, operateUser, 1);
            if (CollectionUtils.isEmpty(afterExchangeCombineGiftProdList)) {
                return false;
            }
            //更新订单明细数据
            normalProdItemList.addAll(afterExchangeCombineGiftProdList);
            omsConstituteSplitService.updateOrder(combineGiftProdItemList, afterExchangeCombineGiftProdList,
                    order, normalProdItemList);
            orderInfo.setOrderItemList(normalProdItemList);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("组合商品解析之后的商品明细,入参 {},OrderId=", order.getId())
                        , JSONObject.toJSONString(normalProdItemList));
            }
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.COMBINATION_SPLIT.getKey(), "组合商品解析成功", null,
                    null, operateUser);
            return true;
        } catch (Exception e) {
            log.error(LogUtil.format("组合商品或者福袋商品拆解异常,error:{}"), Throwables.getStackTraceAsString(e));
            return false;
        }
    }


    /**
     * 封装参数
     *
     * @param combineGiftProdItemList 新明细
     * @param order                   订单信息
     * @param operateUser             当前用户
     * @param type                    0:通用计算售价；1:按照解析后数量计算售价
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<OcBOrderItem> encapsulationParameter(List<OcBOrderItem> combineGiftProdItemList, OcBOrder order,
                                                     User operateUser, Integer type) {
        long begin = System.currentTimeMillis();
        //福袋或者组合商品的的处理
        VirtualProSplitRequest proRequest = new VirtualProSplitRequest();
        List<SplitVirtualPro> splitGroupPros = new ArrayList<>();
        for (OcBOrderItem orderItemInfo : combineGiftProdItemList) {
            //获取虚拟条码
            String skuEcode = orderItemInfo.getPsCSkuEcode();
            BigDecimal qty = orderItemInfo.getQty();
            SplitVirtualPro groupPro = new SplitVirtualPro();
            groupPro.setVirtualSku(skuEcode);
            groupPro.setNum(qty.intValue());
            groupPro.setItemId(orderItemInfo.getId());
            splitGroupPros.add(groupPro);
        }
        proRequest.setSplitVirtualProList(splitGroupPros);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("selectGroupPro.Request={},OrderId=", order.getId()),
                    JSON.toJSONString(proRequest));
        }
        // 调用商品中心通过虚拟条码拿取真实的sku信息
        ValueHolderV14<Map<String, List<ExtractLuckyBag>>> holder = psRpcService.selectGroupPro(proRequest);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("selectGroupPro.Response={},OrderId=", order.getId()), JSON.toJSONString(holder));
        }
        if (!holder.isOK()) {
            if (order.getId() != null) {
                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                        OrderLogTypeEnum.COMBINATION_SPLIT.getKey(), "组合商品解析失败,原因:" + holder.getMessage(),
                        null, null, operateUser);
            }
            return null;
        }

        Map<String, List<ExtractLuckyBag>> dataMap = holder.getData();
        if (dataMap == null) {
            log.error(LogUtil.format("调用商品中心通过虚拟条码拿取真实的sku信息出错{},OrderId=",
                    order.getId()), JSONObject.toJSONString(holder));
            return null;
        }
        List<OcBOrderItem> list = new ArrayList<>();
        // 封装组合商品或者福袋商品的明细数据
        for (OcBOrderItem combineGiftProdItem : combineGiftProdItemList) {
            // 获取虚拟条码
            String psCSkuEcode = combineGiftProdItem.getPsCSkuEcode();
            // 当前商品类型
            // Long skuType = ocBOrderItem.getReserveBigint01();
            List<ExtractLuckyBag> extractGoodYBags = dataMap.get(psCSkuEcode + combineGiftProdItem.getId());
            // 封装当前订单明细数据
            List<OcBOrderItem> ocBOrderItems =
                    this.buildOrderItemFromCombineProdList(combineGiftProdItem, extractGoodYBags, order, type);
            list.addAll(ocBOrderItems);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("bean.addGoodsMainStep.addMerchandise.selectProductSku.use time{},orderId=",
                    order.getId()), System.currentTimeMillis() - begin);
        }
        return list;
    }


    /**
     * 根据原始组合商品明细或者福袋商品明细信息转换成标准的商品明细
     *
     * @param ocBOrderItem     原始订单明细信息
     * @param extractGoodYBags 对应的组合商品解析后的原始商品信息
     * @return 标准商品明细列表
     */
    private List<OcBOrderItem> buildOrderItemFromCombineProdList(OcBOrderItem ocBOrderItem,
                                                                 List<ExtractLuckyBag> extractGoodYBags,
                                                                 OcBOrder ocBOrder, Integer type) {
        List<SingleProInfo> groupList = new ArrayList<>();
        // 20200711易邵峰修改：增加为空判断
        if (CollectionUtils.isNotEmpty(extractGoodYBags)) {
            for (ExtractLuckyBag luckyBagInfo : extractGoodYBags) {
                List<SingleProInfo> list = luckyBagInfo.getExtractLuckyBag();
                groupList.addAll(list);
            }
        }

        //循环sku问题
        List<ProductSku> skuList = new ArrayList<>();

        //计算出真实商品下的所有数量之和
        for (SingleProInfo skuGroupInfo : groupList) {
            //真实条码
            String skuEcode = skuGroupInfo.getPsCSkuEcode();
            //因为返回的信息不全  所以去查一次 (走的redis应该不怎么影响性能)
            ProductSku productSku = psRpcService.selectProductSku(skuEcode);
            productSku.setNum(skuGroupInfo.getNum());
            productSku.setRatio(skuGroupInfo.getRatio());
            // R3 商品中心维护的平台商品信息
            productSku.setNumiid(skuGroupInfo.getNumiid());
            productSku.setSkuId(skuGroupInfo.getSkuId());
            // 是否可拆单 Y/N
            productSku.setCanSplit(skuGroupInfo.getCanSplit());
            skuList.add(productSku);
        }

        // 20200711易邵峰修改：由于原来在转单的过程中将商品更改成了pro_type=4，在进行拆分的时候，无法区分出是福袋商品还是组合商品；
        // 为了区分，则重新查询下原始组合或者福袋商品，确认一下是福袋商品还是组合商品
        String originalSkuEcode = ocBOrderItem.getPsCSkuEcode();
        ProductSku originalProdSku = psRpcService.selectProductSku(originalSkuEcode);
        if (originalProdSku.getSkuType() == SkuType.GIFT_PRODUCT) {
            return getOcBOrderItems(ocBOrderItem, ocBOrder, originalProdSku, skuList);
        }
        return getOcBOrderItemsByRatio(ocBOrderItem, ocBOrder, originalProdSku, skuList, type);
    }


    /**
     * 处理组合商品 (按照比例进行分摊)
     *
     * @param originalCombineGiftItemInfo 交易明细
     * @param ocBOrder                    交易订单信息
     * @param originalProdSku             原始商品SKU信息
     * @param skuList                     组合商品下的SKU列表
     * @return 组合商品或福袋商品转换成功的标准商品明细
     */
    private List<OcBOrderItem> getOcBOrderItemsByRatio(OcBOrderItem originalCombineGiftItemInfo, OcBOrder ocBOrder,
                                                       ProductSku originalProdSku,
                                                       List<ProductSku> skuList, Integer type) {
        //将返回的真实sku信息按照数量从大到小排序
        Collections.sort(skuList, new Comparator<ProductSku>() {
            @Override
            public int compare(ProductSku o1, ProductSku o2) {
                return o2.getNum().compareTo(o1.getNum());
            }
        });
        List<OcBOrderItem> normalProductItemList = new ArrayList<>();
        //单行交易明细组合商品实际成交金额
        BigDecimal realAmtCount = originalCombineGiftItemInfo.getRealAmt() == null ? BigDecimal.ZERO : originalCombineGiftItemInfo.getRealAmt();
        //单行交易明细组合商品总的优惠金额
        BigDecimal amtDiscountTotal = originalCombineGiftItemInfo.getAmtDiscount() == null ? BigDecimal.ZERO : originalCombineGiftItemInfo.getAmtDiscount();
        //单行交易明细组合商品总调整金额
        BigDecimal adjustAmtCount = originalCombineGiftItemInfo.getAdjustAmt() == null ? BigDecimal.ZERO : originalCombineGiftItemInfo.getAdjustAmt();
        //单行交易明细组合商品总平摊金额
        BigDecimal orderSplitAmtCount = originalCombineGiftItemInfo.getOrderSplitAmt() == null ? BigDecimal.ZERO : originalCombineGiftItemInfo.getOrderSplitAmt();
        //原价
        BigDecimal price = originalCombineGiftItemInfo.getPrice() == null ? BigDecimal.ZERO : originalCombineGiftItemInfo.getPrice();
        //判断组合商品的分摊比例是否为0
        BigDecimal totalRatio = skuList.stream().map(ProductSku::getRatio).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        boolean flag = true;
        for (int i = 0; i < skuList.size(); i++) {
            ProductSku productSku = skuList.get(i);
            OcBOrderItem orderItem = this.handleOrderItem(originalProdSku, productSku, originalCombineGiftItemInfo,
                    ocBOrder);
            if (totalRatio.compareTo(BigDecimal.ZERO) == 0 && flag) {
                orderItem.setRealAmt(originalCombineGiftItemInfo.getRealAmt());
                orderItem.setOrderSplitAmt(originalCombineGiftItemInfo.getOrderSplitAmt());
                orderItem.setAmtDiscount(originalCombineGiftItemInfo.getAmtDiscount());
                orderItem.setPriceActual(originalCombineGiftItemInfo.getPriceActual().multiply(originalCombineGiftItemInfo.getQty()).divide(productSku.getNum(), 4, BigDecimal.ROUND_HALF_UP));
                orderItem.setAdjustAmt(originalCombineGiftItemInfo.getAdjustAmt());
                orderItem.setPrice(originalCombineGiftItemInfo.getPrice().multiply(originalCombineGiftItemInfo.getQty()).divide(productSku.getNum(), 4, BigDecimal.ROUND_HALF_UP));
                flag = false;
            } else if (totalRatio.compareTo(BigDecimal.ZERO) == 0 && !flag) {
                orderItem.setRealAmt(BigDecimal.ZERO);
                orderItem.setOrderSplitAmt(BigDecimal.ZERO);
                orderItem.setAmtDiscount(BigDecimal.ZERO);
                orderItem.setPriceActual(BigDecimal.ZERO);
                orderItem.setAdjustAmt(BigDecimal.ZERO);
                orderItem.setPrice(BigDecimal.ZERO);
            }
            if (totalRatio.compareTo(BigDecimal.ZERO) != 0) {
                //根据比例分摊
                BigDecimal ratio = productSku.getRatio().divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                if (BigDecimal.ZERO.compareTo(ratio) == 0) {
                    orderItem.setIsGift(1);
                    orderItem.setGiftType("1");
                    ocBOrder.setIsHasgift(1);
                }
                orderItem.setRealAmt(realAmtCount.multiply(ratio).setScale(4, BigDecimal.ROUND_HALF_UP));
                orderItem.setOrderSplitAmt(orderSplitAmtCount.multiply(ratio).setScale(4, BigDecimal.ROUND_HALF_UP));
                orderItem.setAmtDiscount(amtDiscountTotal.multiply(ratio).setScale(4, BigDecimal.ROUND_HALF_UP));
                orderItem.setPriceActual(orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
                orderItem.setAdjustAmt(adjustAmtCount.multiply(ratio).setScale(4, BigDecimal.ROUND_HALF_UP));

                //售价计算按比例分摊
                BigDecimal priceAmt = price.multiply(ratio).setScale(4, BigDecimal.ROUND_HALF_UP);
                if (type.equals(1)) {
                    //按数量计算
                    BigDecimal qty = orderItem.getQty();
                    if (Objects.isNull(qty) || qty.compareTo(BigDecimal.ZERO) == 0) {
                        qty = BigDecimal.ONE;
                    }
                    priceAmt = price.multiply(ratio).multiply(orderItem.getQtyGroup()).divide(qty, 4, BigDecimal.ROUND_HALF_UP);
                }
                orderItem.setPrice(priceAmt);

                orderItem.setGroupRadio(Optional.ofNullable(ratio).orElse(new BigDecimal("0")));
            }
            // 直播标识不能丢掉
            orderItem.setLiveFlag(originalCombineGiftItemInfo.getLiveFlag());
            orderItem.setLivePlatform(originalCombineGiftItemInfo.getLivePlatform());
            orderItem.setAnchorId(originalCombineGiftItemInfo.getAnchorId());
            orderItem.setAnchorName(originalCombineGiftItemInfo.getAnchorName());
            if (orderItem.getIsGift() != null && orderItem.getIsGift() == 1) {
                if ("Y".equals(orderItem.getCanSplit())) {
                    orderItem.setIsGiftSplit(2);
                } else if ("N".equals(orderItem.getCanSplit())) {
                    orderItem.setIsGiftSplit(1);
                }
            }
            normalProductItemList.add(orderItem);
        }
        BigDecimal realAmt = normalProductItemList.stream().map(OcBOrderItem::getRealAmt).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        if (realAmtCount.compareTo(realAmt) != 0) {
            Collections.sort(normalProductItemList, (o1, o2) -> o2.getRealAmt().compareTo(o1.getRealAmt()));
            OcBOrderItem orderItem = normalProductItemList.get(0);
            //尾差
            BigDecimal orderSplitAmt = normalProductItemList.stream().map(OcBOrderItem::getOrderSplitAmt).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal amtDiscount = normalProductItemList.stream().map(OcBOrderItem::getAmtDiscount).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal adjustAmt = normalProductItemList.stream().map(OcBOrderItem::getAdjustAmt).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal priceQ = normalProductItemList.stream().map(OcBOrderItem::getPrice).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            orderItem.setRealAmt(orderItem.getRealAmt().add(realAmtCount.subtract(realAmt)));
            orderItem.setOrderSplitAmt(orderItem.getOrderSplitAmt().add(orderSplitAmtCount.subtract(orderSplitAmt)));
            orderItem.setAmtDiscount(orderItem.getAmtDiscount().add(amtDiscountTotal.subtract(amtDiscount)));
            orderItem.setPriceActual(orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
            orderItem.setAdjustAmt(orderItem.getAdjustAmt().add(adjustAmtCount.subtract(adjustAmt)));
            orderItem.setPrice(orderItem.getPrice().add(price.subtract(priceQ)));
        }
        return normalProductItemList;
    }


    /**
     * 处理组合商品 (按照吊牌价进行分摊)
     *
     * @param originalCombineGiftItemInfo 交易明细
     * @param ocBOrder                    交易订单信息
     * @param originalProdSku             原始商品SKU信息
     * @param skuList                     组合商品下的SKU列表
     * @return 组合商品或福袋商品转换成功的标准商品明细
     */
    private List<OcBOrderItem> getOcBOrderItems(OcBOrderItem originalCombineGiftItemInfo, OcBOrder ocBOrder,
                                                ProductSku originalProdSku,
                                                List<ProductSku> skuList) {
        //将返回的真实sku信息按照数量从大到小排序
        Collections.sort(skuList, new Comparator<ProductSku>() {
            @Override
            public int compare(ProductSku o1, ProductSku o2) {
                return o2.getNum().compareTo(o1.getNum());
            }
        });
        List<OcBOrderItem> normalProductItemList = new ArrayList<>();
        //单行交易明细组合商品实际成交金额
        BigDecimal realAmtCount = originalCombineGiftItemInfo.getRealAmt() == null ? BigDecimal.ZERO : originalCombineGiftItemInfo.getRealAmt();
        //单行交易明细组合商品总的优惠金额
        BigDecimal amtDiscountTotal = originalCombineGiftItemInfo.getAmtDiscount() == null ? BigDecimal.ZERO : originalCombineGiftItemInfo.getAmtDiscount();
        //单行交易明细组合商品总调整金额
        BigDecimal adjustAmtCount = originalCombineGiftItemInfo.getAdjustAmt() == null ? BigDecimal.ZERO : originalCombineGiftItemInfo.getAdjustAmt();
        //单行交易明细组合商品总平摊金额
        BigDecimal orderSplitAmtCount = originalCombineGiftItemInfo.getOrderSplitAmt() == null ? BigDecimal.ZERO : originalCombineGiftItemInfo.getOrderSplitAmt();
        //原价
        BigDecimal price = originalCombineGiftItemInfo.getPrice() == null ? BigDecimal.ZERO : originalCombineGiftItemInfo.getPrice();

        BigDecimal allProdTotalAmt = BigDecimal.ZERO;
        List<BigDecimal> prodTotalAmtList = new ArrayList<>();
        for (ProductSku productSku : skuList) {
            BigDecimal prodPriceValue = productSku.getPricelist() == null ? BigDecimal.ZERO : productSku.getPricelist();
            BigDecimal prodTotalAmt = prodPriceValue.multiply(productSku.getNum())
                    .setScale(4, BigDecimal.ROUND_HALF_UP);
            allProdTotalAmt = allProdTotalAmt.add(prodTotalAmt)
                    .setScale(4, BigDecimal.ROUND_HALF_UP);
            prodTotalAmtList.add(prodTotalAmt);
        }
        Long orderId = ocBOrder.getId();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("skuList:{},groupCountAmt:{} groupMoneyList:{},OrderId=", orderId)
                    , JSON.toJSONString(skuList), JSON.toJSONString(allProdTotalAmt), JSON.toJSONString(prodTotalAmtList));
        }

//        int i1 = skuList.size() - 1;
        boolean flag = true;
        for (int i = 0; i < skuList.size(); i++) {
            ProductSku productSku = skuList.get(i);
            OcBOrderItem orderItem = this.handleOrderItem(originalProdSku, productSku, originalCombineGiftItemInfo,
                    ocBOrder);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("handleOrderItem处理后返回值:{},OrderId=",
                        orderId), JSON.toJSONString(orderItem));
            }
            if (allProdTotalAmt.compareTo(BigDecimal.ZERO) == 0 && flag) {
                orderItem.setRealAmt(originalCombineGiftItemInfo.getRealAmt());
                orderItem.setOrderSplitAmt(originalCombineGiftItemInfo.getOrderSplitAmt());
                orderItem.setAmtDiscount(originalCombineGiftItemInfo.getAmtDiscount());
                orderItem.setPriceActual(originalCombineGiftItemInfo.getPriceActual().multiply(originalCombineGiftItemInfo.getQty()).divide(productSku.getNum(), 4, BigDecimal.ROUND_HALF_UP));
                orderItem.setAdjustAmt(originalCombineGiftItemInfo.getAdjustAmt());
                orderItem.setPrice(originalCombineGiftItemInfo.getPrice().multiply(originalCombineGiftItemInfo.getQty()).divide(productSku.getNum(), 4, BigDecimal.ROUND_HALF_UP));
                flag = false;
            } else if (allProdTotalAmt.compareTo(BigDecimal.ZERO) == 0 && !flag) {
                orderItem.setRealAmt(BigDecimal.ZERO);
                orderItem.setOrderSplitAmt(BigDecimal.ZERO);
                orderItem.setAmtDiscount(BigDecimal.ZERO);
                orderItem.setPriceActual(BigDecimal.ZERO);
                orderItem.setAdjustAmt(BigDecimal.ZERO);
                orderItem.setPrice(BigDecimal.ZERO);
            }
            if (allProdTotalAmt.compareTo(BigDecimal.ZERO) != 0) {
                List<BigDecimal> valueList = this.calcGroupProdRealAmt(prodTotalAmtList, realAmtCount, allProdTotalAmt);
                orderItem.setRealAmt(valueList.get(i));
                List<BigDecimal> splitAmtList = this.calcGroupProdRealAmt(prodTotalAmtList, orderSplitAmtCount, allProdTotalAmt);
                orderItem.setOrderSplitAmt(splitAmtList.get(i));
                List<BigDecimal> discountList = this.calcGroupProdRealAmt(prodTotalAmtList, amtDiscountTotal, allProdTotalAmt);
                orderItem.setAmtDiscount(discountList.get(i));
                orderItem.setPriceActual(valueList.get(i).divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
                List<BigDecimal> adjustAmt = this.calcGroupProdRealAmt(prodTotalAmtList, adjustAmtCount, allProdTotalAmt);
                orderItem.setAdjustAmt(adjustAmt.get(i));
                List<BigDecimal> prices = this.calcGroupProdRealAmt(prodTotalAmtList, price.multiply(originalCombineGiftItemInfo.getQty()), allProdTotalAmt);
                orderItem.setPrice(prices.get(i).divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
            }

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("handleOrderItem处理后返回值:{},OrderId=", orderId), JSON.toJSONString(orderItem));
            }
//
            // 直播标识不能丢掉
            orderItem.setLiveFlag(originalCombineGiftItemInfo.getLiveFlag());
            orderItem.setLivePlatform(originalCombineGiftItemInfo.getLivePlatform());
            orderItem.setAnchorId(originalCombineGiftItemInfo.getAnchorId());
            orderItem.setAnchorName(originalCombineGiftItemInfo.getAnchorName());
            normalProductItemList.add(orderItem);
        }

        return normalProductItemList;
    }

    private List<BigDecimal> calcGroupProdRealAmt(List<BigDecimal> skuList, BigDecimal realAmtCount,
                                                  BigDecimal totalGroupCountAmt) {

        log.debug(LogUtil.multiFormat("calcGroupProdRealAmt 入参-> realAmtCount:{} totalGroupCountAmt:{} skuList=", skuList),
                realAmtCount, totalGroupCountAmt);
        BigDecimal totalAmt = BigDecimal.ZERO;
        List<BigDecimal> valueList = new ArrayList<>();
        List<BigDecimal> skuGroupMoney = new ArrayList<>();
        for (int i = 0; i < skuList.size(); i++) {
            BigDecimal groupCountAmt = skuList.get(i);
            BigDecimal real = BigDecimal.ZERO;
            if (totalGroupCountAmt.compareTo(BigDecimal.ZERO) != 0) {
                real = realAmtCount.divide(totalGroupCountAmt, 10, BigDecimal.ROUND_HALF_UP)
                        .multiply(groupCountAmt).setScale(4, RoundingMode.HALF_EVEN);
            }
            valueList.add(real);
            skuGroupMoney.add(groupCountAmt);
            totalAmt = totalAmt.add(real);
        }

        log.debug(LogUtil.format("calcGroupProdRealAmt totalAmt:{} skuGroupMoney:{} valueList:{}"), totalAmt,
                JSON.toJSONString(skuGroupMoney), JSON.toJSONString(valueList));

        if (totalAmt.compareTo(realAmtCount) != 0) {
            BigDecimal gap = realAmtCount.subtract(totalAmt);
            log.debug(LogUtil.format("calcGroupProdRealAmt gap{} = realAmtCount{} totalAmt{}"), gap, realAmtCount,
                    totalAmt);

            for (int index = 0; index <= valueList.size() - 1; index++) {
                BigDecimal value = valueList.get(index);
                BigDecimal groupCountAmt = skuGroupMoney.get(index);
                if (value.add(gap).compareTo(BigDecimal.ZERO) < 0) {
                    gap = gap.add(value.subtract(BigDecimal.ZERO)).setScale(4, BigDecimal.ROUND_HALF_UP);
                    value = BigDecimal.ZERO;
                } else if (value.add(gap).compareTo(groupCountAmt) > 0) {
                    gap = gap.subtract(groupCountAmt.subtract(value)).setScale(4, BigDecimal.ROUND_HALF_UP);
                    value = groupCountAmt;
                } else {
                    value = value.add(gap).setScale(4, BigDecimal.ROUND_HALF_UP);
                    gap = BigDecimal.ZERO;
                }
                valueList.set(index, value);
                if (gap.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
            }

        }

        log.debug(LogUtil.format("calcGroupProdRealAmt 结果-> valueList:{}"), valueList);
        return valueList;
    }


    /**
     * 封装订单明细信息
     *
     * @param productSku
     */
    private OcBOrderItem handleOrderItem(ProductSku originalProdSku, ProductSku productSku,
                                         OcBOrderItem originalCombineGiftOrderItem,
                                         OcBOrder originalOrderInfo) {
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);
        //活动编号. 默认赋值为null
        item.setActiveId(null);
        BaseModelUtil.initialBaseModelSystemField(item);
        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //福袋条码
        item.setGiftbagSku(originalCombineGiftOrderItem.getGiftbagSku());
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        //
        item.setIsGift(0);
        //实缺标记
        item.setIsLackstock(0);
        item.setIsPresalesku(originalCombineGiftOrderItem.getIsPresalesku());
        //商品数字编号
        //订单编号
        item.setOcBOrderId(originalOrderInfo.getId());
        //子订单编号(明细编号)
        item.setOoid(originalCombineGiftOrderItem.getOoid());
        //平摊金额
        //item.setOrderSplitAmt(ocBOrderItem.getOrderSplitAmt());
        //商品路径
        item.setPicPath(originalCombineGiftOrderItem.getPicPath());
        //成交价格.若为组合商品的值，则【则【淘宝订单中间表】明细表的（“成本价”*“数量”-“优惠费用”+“调整费用”）/“数量”*【组合商品】的“价格比例”，
        // 若不为组合商品，则【淘宝订单中间表】明细表的（“成本价”*“数量”-“优惠费用”+“调整费用”）/“数量”
        //item.setPrice();


        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        //单行实际成交金额. s.price * s.num - s.discount_fee + s.adjust_fee- part_mjz_discount
        //item.setRealAmt();
        // 平台退款编号
        item.setRefundId(originalCombineGiftOrderItem.getRefundId());
        //退款状态
        // 如果是退款完成，或者是交易关闭 状态=6
        item.setRefundStatus(originalCombineGiftOrderItem.getRefundStatus());

        this.initialTaobaoOrderItem(productSku, item);
        //库位。不用赋值
//        item.setStoreSite(null);
        //标题
        item.setTitle(originalCombineGiftOrderItem.getTitle());
        item.setTid(originalOrderInfo.getTid());
        long proType = SkuType.COMBINE_PRODUCT;
        if (originalProdSku != null && originalProdSku.getSkuType() == SkuType.GIFT_PRODUCT) {
            proType = SkuType.GIFT_PRODUCT;
        }
        //类型
        item.setProType(proType);
        // 虚拟条码
        item.setGiftbagSku(originalCombineGiftOrderItem.getPsCSkuEcode());

        item.setOoid(originalCombineGiftOrderItem.getOoid());
//        String numIid = originalCombineGiftOrderItem.getNumIid();
//        String skuNumiid = originalCombineGiftOrderItem.getSkuNumiid();
//        if (numIid != null) {
//            item.setNumIid(originalCombineGiftOrderItem.getNumIid());
//        }
//        if (StringUtils.isNotEmpty(skuNumiid)) {
//            item.setSkuNumiid(skuNumiid);
//        }
//        log.debug(LogUtil.format("Start 抖音组合商品信息赋值:productSku:{},item:{}"), JSON.toJSONString(productSku), JSON.toJSONString(item));
//        // 抖音做特殊处理
//        if (PlatFormEnum.DOU_YIN.getCode().equals(originalOrderInfo.getPlatform()) && proType == SkuType.COMBINE_PRODUCT) {
//            if (productSku.getSkuId() != null) {
//                item.setSkuNumiid(productSku.getSkuId());
//            }
//            if (productSku.getNumiid() != null) {
//                item.setNumIid(productSku.getNumiid());
//            }
//        }
        item.setCanSplit(productSku.getCanSplit());
        log.debug(LogUtil.format("Finish 抖音组合商品信息赋值item:{}"), JSON.toJSONString(item));


        //组合商品的购买数量
        item.setQtyGroup(originalCombineGiftOrderItem.getQty());

        //平台条码
        //item.setPsCSkuPtEcode(originalCombineGiftOrderItem.getPsCSkuEcode());
        item.setIsManualAdd(originalCombineGiftOrderItem.getIsManualAdd());
        item.setPriceTag(productSku.getPricelist());
        item.setPriceList(productSku.getPricelist());
        item.setPsCProEname(productSku.getName());
        item.setPsCSkuEname(productSku.getSkuName());
        item.setIsGift(originalCombineGiftOrderItem.getIsGift());
        item.setEstimateConTime(originalCombineGiftOrderItem.getEstimateConTime());
        // 把组合商品的挂靠关系赋值给他的下挂明细
        if (StringUtils.isNotEmpty(originalCombineGiftOrderItem.getGiftRelation())) {
            item.setGiftRelation(originalCombineGiftOrderItem.getGiftRelation());
        }
        // 把组合商品的赠品类型赋值给他的下挂明细
        if (StringUtils.isNotEmpty(originalCombineGiftOrderItem.getGiftType())) {
            item.setGiftType(originalCombineGiftOrderItem.getGiftType());
        }
        //已当前id作为组合商品标识 保证唯一性
        item.setGroupGoodsMark(GROUP_GOODS_MARK + originalCombineGiftOrderItem.getId());
        item.setMDim4Id(productSku.getMDim4Id());
        item.setMDim6Id(productSku.getMDim6Id());
        item.setPsCSkuPtEcode(originalCombineGiftOrderItem.getPsCSkuPtEcode());
        item.setNumIid(originalCombineGiftOrderItem.getNumIid());
        item.setSkuNumiid(originalCombineGiftOrderItem.getSkuNumiid());
        item.setReserveVarchar03(originalCombineGiftOrderItem.getReserveVarchar03());
        // 小米有品
        item.setReserveVarchar04(originalCombineGiftOrderItem.getReserveVarchar04());
        item.setReturnOrderId(originalCombineGiftOrderItem.getReturnOrderId());
        return item;
    }


    private void initialTaobaoOrderItem(ProductSku productSku, OcBOrderItem item) {
        if (productSku != null) {
            item.setPsCProId(productSku.getProdId());
            // ProECode
            item.setSex(productSku.getSex());
            //2019-08-30吊牌价改为取商品表数据 //吊牌价
            item.setPriceTag(productSku.getPricelist());
            item.setPsCProEcode(productSku.getProdCode());
            item.setPsCProEname(productSku.getName());
            item.setPsCSkuId(productSku.getId());
            // 2019-06-16 易邵峰修改：增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
            String psSkuEcode = productSku.getSkuEcode();
            if (checkIsNeedTransferSkuUpperCase()) {
                psSkuEcode = StringUtils.upperCase(psSkuEcode);
            }
            item.setPsCSkuEcode(psSkuEcode);
            item.setPsCClrEcode(productSku.getColorCode());
            item.setPsCClrEname(productSku.getColorName());
            item.setPsCClrId(productSku.getColorId());
            item.setStandardWeight(productSku.getWeight());
            item.setSkuSpec(productSku.getSkuSpec());
            item.setPsCSizeEcode(productSku.getSizeCode());
            item.setPsCSizeEname(productSku.getSizeName());
            item.setPsCSizeId(productSku.getSizeId());
            item.setPsCProMaterieltype(productSku.getMaterialType());
            item.setBarcode(productSku.getBarcode69());
            item.setQty(productSku.getNum());
            //标准价。
            item.setPriceList(productSku.getPricelist());
            item.setPriceTag(productSku.getPricelist());
            item.setMDim4Id(productSku.getMDim4Id());
            item.setMDim6Id(productSku.getMDim6Id());
            String isEnableExpiry = productSku.getIsEnableExpiry();
            if ("Y".equals(isEnableExpiry)) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }


    /**
     * 是否需要转换成大写
     * 乔丹项目中：SAP系统存储的SKU部分有小写。为了统一，库里存储的全部为大写。因此在转单的时候强制转换成大写。
     *
     * @return true
     */
    private boolean checkIsNeedTransferSkuUpperCase() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.transfer.sku.toupper", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            log.error(LogUtil.format("checkIsNeedTransferSkuUpperCase,error:{}"), Throwables.getStackTraceAsString(ex));
            return true;
        }
    }


    /**
     * 删除当前订单下的虚拟商品的sku信息 插入当前虚拟条码下所有真实的sku信息
     *
     * @param combineGiftProdItemList          需要删除的明细数据
     * @param afterExchangeCombineGiftProdList 需要新增的明细数据
     * @param listOrder                        所有的明细信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrder(List<OcBOrderItem> combineGiftProdItemList, List<OcBOrderItem> afterExchangeCombineGiftProdList,
                            OcBOrder ocBOrder, List<OcBOrderItem> listOrder) {
        Long id = ocBOrder.getId();
        for (OcBOrderItem orderItem : combineGiftProdItemList) {
            //ocBOrderItemMapper.deleteByItemId(orderItem.getId(), id);
            //将类型更换为 4
            OcBOrderItem ocBOrderItem = new OcBOrderItem();
            ocBOrderItem.setId(orderItem.getId());
            ocBOrderItem.setOcBOrderId(orderItem.getOcBOrderId());
            ocBOrderItem.setProType((long) SkuType.NO_SPLIT_COMBINE);
            ocBOrderItem.setGiftbagSku(orderItem.getPsCSkuEcode());
            ocBOrderItem.setGroupGoodsMark(GROUP_GOODS_MARK + orderItem.getId());
            omsOrderItemService.updateOcBOrderItem(ocBOrderItem, id);
        }
        ocBOrderItemMapper.batchInsert(afterExchangeCombineGiftProdList);
        //更新主表的商品数量
        BigDecimal totalItemQty = afterExchangeCombineGiftProdList.stream().map(OcBOrderItem::getQty).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        List<OcBOrderItem> giftList = afterExchangeCombineGiftProdList.stream().filter(p -> p.getIsGift() != null && p.getIsGift() == 1).collect(Collectors.toList());
//        OcBOrderRelation relation = new OcBOrderRelation();
//        relation.setOrderInfo(ocBOrder);
//        List<OcBOrderItem> list = new ArrayList<>();
        // omsOrderRecountAmountService.doRecountAmount(relation, list, listOrder);
        OcBOrder order = new OcBOrder();
        order.setId(id);
        order.setQtyAll(totalItemQty);
        if (CollectionUtils.isNotEmpty(giftList)){
            order.setIsHasgift(1);
        }
        int count = ocBOrderItemMapper.selectCountForOrder(id);
        order.setSkuKindQty(new BigDecimal(count));
        omsOrderService.updateOrderInfo(order);
        //todo  奶卡信息处理 不干扰正常逻辑
        try {
            List<OcBOrderNaiKa> orderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(id);
            if (CollectionUtils.isEmpty(orderNaiKaList)) {
                return;
            }
            orderNaiKaList = orderNaiKaList.stream().filter(p -> p.getOcBOrderItemId() != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderNaiKaList)) {
                return;
            }
            Map<Long, List<OcBOrderNaiKa>> naikaMap = orderNaiKaList.stream().collect(Collectors.groupingBy(OcBOrderNaiKa::getOcBOrderItemId));
            Map<String, List<OcBOrderItem>> groupMap = afterExchangeCombineGiftProdList.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
            List<OcBOrderNaiKa> list = new ArrayList<>();
            for (OcBOrderItem orderItem : combineGiftProdItemList) {
                List<OcBOrderNaiKa> orderNaiKas = naikaMap.get(orderItem.getId());
                if (CollectionUtils.isEmpty(orderNaiKas)) {
                    continue;
                }
                String s = GROUP_GOODS_MARK + orderItem.getId();
                List<OcBOrderItem> bOrderItems = groupMap.get(s);
                if (CollectionUtils.isEmpty(bOrderItems)) {
                    continue;
                }
                for (OcBOrderNaiKa naiKa : orderNaiKas) {
                    for (OcBOrderItem bOrderItem : bOrderItems) {
                        OcBOrderNaiKa orderNaiKa = new OcBOrderNaiKa();
                        BeanUtils.copyProperties(naiKa, orderNaiKa);
                        orderNaiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
                        orderNaiKa.setOcBOrderItemId(bOrderItem.getId());
                        orderNaiKa.setPsCProEcode(bOrderItem.getPsCProEcode());
                        orderNaiKa.setPsCProEname(bOrderItem.getPsCProEname());
                        orderNaiKa.setPsCProId(bOrderItem.getPsCProId());
                        orderNaiKa.setPsCSkuEcode(bOrderItem.getPsCSkuEcode());
                        orderNaiKa.setPsCSkuEname(bOrderItem.getPsCSkuEname());
                        orderNaiKa.setPsCSkuId(bOrderItem.getPsCSkuId());
                        orderNaiKa.setSkuSpec(bOrderItem.getSkuSpec());
                        list.add(orderNaiKa);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(list)){
                ocBOrderNaiKaMapper.batchInsert(list);
            }
        } catch (Exception e){
            log.error(LogUtil.format("组合商品处理奶卡信息异常,error:{}","组合商品处理奶卡信息异常"), Throwables.getStackTraceAsString(e));
        }




        //try {
        //    SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
        //            OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, afterExchangeCombineGiftProdList,
        //            "OC_B_ORDER_ID");
        //
        //} catch (Exception e) {
        //    log.error(LogUtil.format("OrderId={},{} 删除es明细异常!", this.getClass().getName(), order.getId(), e);
        //}
    }


//    public List<OcBOrderItem> getOrderItemList(Long orderId) {
//        return ocBOrderItemMapper.selectOrderItemListOccupy(orderId);
//    }
}
