package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderWarningMapper;
import com.jackrain.nea.oc.oms.model.constant.R3ParamConstants;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderWarning;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/12/16 10:38
 * 异常信息 处理按钮
 */
@Slf4j
@Component
public class OcBReturnOrderWarningManageService {

    @Autowired
    private OcBReturnOrderWarningMapper returnOrderWarningMapper;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        DefaultWebEvent event = querySession.getEvent();
        User user = querySession.getUser();
        JSONObject Parame = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue(R3ParamConstants.PARAM),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        if (log.isDebugEnabled()) {
            log.debug(" OcBReturnOrderWarningManageService Parame {} user {}", JSONObject.toJSONString(Parame), JSONObject.toJSONString(user));
        }
        List<Long> ids = new ArrayList<>();
        Long objId = Parame.getLong(R3ParamConstants.OBJID);
        if (objId != null) {
            ids.add(objId);
        }
        JSONArray array = Parame.getJSONArray(R3ParamConstants.IDS);
        if (log.isDebugEnabled()) {
            log.debug(" getJSONArray array {} ", JSONObject.toJSONString(array));
        }
        if (CollectionUtils.isNotEmpty(array)) {
            for (int i = 0; i < array.size(); i++) {
                ids.add(array.getLong(i));
            }
        }
        if (CollectionUtils.isEmpty(ids)) {
            throw new NDSException("请选择需要处理的异常数据！");
        }
        if (ids.size() == 1) {
            Long id = ids.get(0);
            OcBReturnOrderWarning warning = returnOrderWarningMapper.selectById(id);
            if (OcBOrderConst.IS_ACTIVE_YES.equals(warning.getManageResult())) {
                throw new NDSException("单据非未处理，不可操作！");
            }
        }
        OcBReturnOrderWarningManageService service = ApplicationContextHandle.getBean(OcBReturnOrderWarningManageService.class);
        return service.manage(ids, user);

    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder manage(List<Long> ids, User user) {
        ValueHolder v14 = new ValueHolder();
        v14.put("code", ResultCode.SUCCESS);

        int size = ids.size();
        Integer count = returnOrderWarningMapper.batchUpdate(ids, user.getId(), user.getEname(), OcBOrderConst.IS_ACTIVE_YES);
        if (count == 0) {
            v14.put("code", ResultCode.FAIL);
        }
        v14.put("message", "异常信息 处理成功" + count + "条，失败" + (size - count) + "条！");
        return v14;
    }

}
