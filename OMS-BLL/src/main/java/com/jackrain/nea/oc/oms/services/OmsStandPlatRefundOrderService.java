package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.service.AcFInvoiceReturnRedOffsetService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.dto.invoice.ReturnRedOffsetDTO;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnBfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.ReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.StandplatRefundOrderTransferUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.lop.open.api.sdk.internal.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/6/23 9:38 下午
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsStandPlatRefundOrderService {

    @Autowired
    private OcBReturnBfSendMapper ocBReturnBfSendMapper;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
    @Autowired
    private StandplatRefundOrderTransferUtil standplatRefundOrderTransferUtil;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private IpStandplatRefundService ipStandplatRefundService;

    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;

    @Autowired
    private AcFInvoiceReturnRedOffsetService acFInvoiceReturnRedOffsetService;

    @Autowired
    private IpBStandplatRefundItemMapper standplatRefundItemMapper;

    /**
     * 生成发货前退款单(仅退款)
     */
    public void foundRefundFrontRefundOnly(OcBOrder ocBOrder, IpBStandplatRefund ipBStandplatRefund, User user) {

        OcBReturnBfSend oldReturnBfSend = isRefundSlipBfExist(ipBStandplatRefund.getReturnNo());
        if (oldReturnBfSend != null) {
            return;
        }
        OcBReturnBfSend ocBReturnBfSend = new OcBReturnBfSend();
        ocBReturnBfSend.setId(ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCBRETURNBFSEND));
        ocBReturnBfSend.setCpCShopId(ocBOrder.getCpCShopId());
        ocBReturnBfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        ocBReturnBfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        ocBReturnBfSend.setTid(ipBStandplatRefund.getOrderNo());
        ocBReturnBfSend.setSubBillNo(ipBStandplatRefund.getSubOrderId());
        ocBReturnBfSend.setTReturnId(ipBStandplatRefund.getReturnNo());
        ocBReturnBfSend.setBuyerNick(ipBStandplatRefund.getBuyerNick());
        ocBReturnBfSend.setAmtReturn(ipBStandplatRefund.getRefundAmount());
        ocBReturnBfSend.setReason(ipBStandplatRefund.getReturnReason());
        ocBReturnBfSend.setReturnApplyTime(ipBStandplatRefund.getCreated());
        Integer returnStatus = ipBStandplatRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        ocBReturnBfSend.setTReturnStatus(status);
        ocBReturnBfSend.setReturnApplyTime(ipBStandplatRefund.getCreated());
        //退款说明
        //ocBReturnBfSend.setReturnExplain(ipBStandplatRefund.get);
        //OcBReturnBfSend oldReturnBfSend = isRefundSlipBfExist(ipBStandplatRefund.getReturnNo());
        OperateUserUtils.saveOperator(ocBReturnBfSend, user);
        ocBReturnBfSendMapper.insert(ocBReturnBfSend);
    }


    /**
     * 查询发货前退款单是否存在
     *
     * @param refundId
     * @return
     */
    private OcBReturnBfSend isRefundSlipBfExist(String refundId) {
        List<OcBReturnBfSend> ocBReturnBfSend = ocBReturnBfSendMapper.selectOcBReturnBfSendsByRefundId(refundId);
        if (CollectionUtils.isNotEmpty(ocBReturnBfSend)) {
            return ocBReturnBfSend.get(0);
        }
        return null;
    }

    /**
     * 生成发货后退款单(仅退款)
     *
     * @param ocBOrderItems
     * @param ocBOrder
     * @param ipBStandplatRefund
     */
    public void foundRefundSlipAfterRefundOnly(List<OcBOrderItem> ocBOrderItems, OcBOrder ocBOrder,
                                               IpBStandplatRefund ipBStandplatRefund, User user, OmsStandPlatRefundRelation refundRelation) {
        //获取所有的退单明细数据
        try {
            if (!isRefundSlipAfExist(ipBStandplatRefund.getReturnNo())) {
                OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(ipBStandplatRefund.getReturnNo());
                // 退款金额
                BigDecimal refundFee = refundRelation.getIpBStandplatRefund().getRefundAmount();

                BigDecimal totalItemReturnFee = new BigDecimal(0);
                if (CollectionUtils.isEmpty(refundRelation.getIpBStandplatRefundItem())) {
                    totalItemReturnFee = refundFee;
                } else {
                    for (IpBStandplatRefundItem ipBStandplatRefundItem : refundRelation.getIpBStandplatRefundItem()) {
                        if (Objects.nonNull(ipBStandplatRefundItem.getRefundFee())) {
                            totalItemReturnFee = totalItemReturnFee.add(ipBStandplatRefundItem.getRefundFee());
                        }
                    }
                }

                OcBReturnAfSend update = new OcBReturnAfSend();
                // 申请退款金额
                update.setAmtReturnApply(totalItemReturnFee);
                update.setAmtReturnActual(refundFee);
                update.setRemark("金额发生变化");
                update.setModifieddate(new Date());
                update.setId(ocBReturnAfSend.getId());
                ocBReturnAfSendMapper.updateById(update);
                return;
            }
            OcBReturnAfSendRelation ocBReturnAfSendRelation =
                    standplatRefundOrderTransferUtil.standplatRefundAfSendToRefundOnly(ocBOrderItems, ocBOrder, ipBStandplatRefund, user);
            /*if(PlatFormEnum.SAP.getCode().equals(ipBStandplatRefund.getCpCPlatformId().intValue())){
                //默认值：电商销售仅退款
                ocBReturnAfSendRelation.getOcBReturnAfSend().setBusinessTypeId(16L);
                ocBReturnAfSendRelation.getOcBReturnAfSend().setBusinessTypeCode("RYTK03");
                ocBReturnAfSendRelation.getOcBReturnAfSend().setBusinessTypeName("电商销售仅退款");
            }else {
                //查询业务类型：原单业务类型对应的退款业务类型
                StCBusinessType stCBusinessType = omsRefundOrderService.queryRefundOrderType(ocBOrder);
                ocBReturnAfSendRelation.getOcBReturnAfSend().setBusinessTypeId(stCBusinessType.getId());
                ocBReturnAfSendRelation.getOcBReturnAfSend().setBusinessTypeCode(stCBusinessType.getEcode());
                ocBReturnAfSendRelation.getOcBReturnAfSend().setBusinessTypeName(stCBusinessType.getEname());
            }*/
            //获取发货单主表数据
            omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation,user);
            //如果是退款完成，则保存退单待红冲发票信息
            if(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal() == ocBReturnAfSendRelation.getOcBReturnAfSend().getReturnStatus().intValue()){
                ReturnRedOffsetDTO dto = omsRefundOrderService.bulidReturnRedOffsetDTO(ocBReturnAfSendRelation.getOcBReturnAfSend(),ocBReturnAfSendRelation.getOcBReturnAfSendItems());
                acFInvoiceReturnRedOffsetService.createRedOffserRecord(dto);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("生成退款单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException("生成退款单异常");
        }


    }


    /**
     * 拦截生成发货后退款单(退货退款)
     */
    public void foundRefundSlipAfter(List<Long> returnId, OcBOrder ocBOrder, IpBStandplatRefund ipBStandplatRefund,
                                     User user, List<OcBOrderItem> refundNoNeedOrderItemList) {
        //获取所有的退单明细数据
        try {
            List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectReturnOrderListByOrderIds(returnId);
//            @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y' and refund_status != 6  and pro_type != 4")

            List<OcBOrderItem> orderItems = new ArrayList<>();
            List<OcBReturnOrderRefund> orderRefunds = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(returnId);
            if (CollectionUtils.isNotEmpty(orderRefunds)) {
                List<Long> ocBOrderItemIds = orderRefunds.stream().map(OcBReturnOrderRefund::getOcBOrderItemId).collect(Collectors.toList());
                orderItems = ocBOrderItemMapper.selectByIds(ocBOrderItemIds);
            } else {
                orderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
            }

            this.updateOcBReturnOrder(ipBStandplatRefund, ocBReturnOrders, orderRefunds);
            //获取发货单主表数据
            if (!isRefundSlipAfExist(ipBStandplatRefund.getReturnNo())) {
                return;
            }
            OcBReturnAfSendRelation ocBReturnAfSendRelation =
                    standplatRefundOrderTransferUtil.standplatRefundAfSendToReturn(orderRefunds, ocBOrder,
                            ipBStandplatRefund, user, orderItems, ocBReturnOrders, refundNoNeedOrderItemList);
            omsRefundOrderService.insertOcBReturnAfSend(ocBReturnAfSendRelation,user);

            //如果是退款完成，则保存退单待红冲发票信息
            log.info("foundRefundSlipAfterExecuteRedPush");
            OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendRelation.getOcBReturnAfSend();
            log.info("foundRefundSlipAfter.ocBReturnAfSend: {}", JSON.toJSONString(ocBReturnAfSend));
            if (ocBReturnAfSend.getBillType().equals(0) || ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal() == ocBReturnAfSend.getReturnStatus().intValue()) {
                ReturnRedOffsetDTO dto = bulidReturnRedOffsetDTO(ocBReturnAfSend, ocBReturnAfSendRelation.getOcBReturnAfSendItems());
                acFInvoiceReturnRedOffsetService.createRedOffserRecord(dto);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("生成退款单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
    }

    /**
     * 生成已发货退款单且退款完成 - 退单待红冲发票信息
     *
     * @param ocBReturnAfSend
     * @return
     */
    public ReturnRedOffsetDTO bulidReturnRedOffsetDTO(OcBReturnAfSend ocBReturnAfSend, List<OcBReturnAfSendItem> ocBReturnAfSendItems) {

        ReturnRedOffsetDTO dto = new ReturnRedOffsetDTO();
        dto.setTid(ocBReturnAfSend.getTid());
        dto.setBillNo(ocBReturnAfSend.getBillNo());
        if (ocBReturnAfSend.getBillType().equals(0)) {
            dto.setBillType("1");
        } else {
            dto.setBillType("3");
        }
        dto.setCpCShopId(ocBReturnAfSend.getCpCShopId());
        dto.setCpCShopEcode(ocBReturnAfSend.getCpCShopEcode());
        dto.setCpCShopTitle(ocBReturnAfSend.getCpCShopTitle());
        dto.setCpCPlatformId(ocBReturnAfSend.getCpCPlatformId());
        //dto.setCpCPlatformEcode();
        //dto.setCpCPlatformEname();
        dto.setReturnType("1");
        dto.setChangeStatus("0");
        dto.setReturnDate(ocBReturnAfSend.getReturnApplyTime());
        //dto.setSystemRemark(ipBTaobaoRefund.getSysremark());

        if (CollectionUtils.isNotEmpty(ocBReturnAfSendItems)) {
            BigDecimal qtyReturnApply = ocBReturnAfSendItems.stream().filter(x -> x.getQtyReturnApply() != null).map(OcBReturnAfSendItem::getQtyReturnApply).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal amtReturn = ocBReturnAfSendItems.stream().filter(x -> x.getAmtReturn() != null).map(OcBReturnAfSendItem::getAmtReturn).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            dto.setReturnCount(String.valueOf(qtyReturnApply));
            dto.setRefundMoney(amtReturn);

            List<ReturnRedOffsetDTO.ReturnOffsetItemDTO> returnOffsetItemDTOList = new ArrayList<>();
            for (OcBReturnAfSendItem sendItem : ocBReturnAfSendItems) {
                ReturnRedOffsetDTO.ReturnOffsetItemDTO itemDTO = new ReturnRedOffsetDTO.ReturnOffsetItemDTO();
                itemDTO.setProductCode(sendItem.getPsCProEcode());
                itemDTO.setProductName(sendItem.getPsCProEname());
                itemDTO.setSkuCode(sendItem.getPsCSkuEcode());
                itemDTO.setSkuName(sendItem.getPsCSkuEname());
                if (sendItem.getQtyReturnApply() != null) {
                    itemDTO.setReturnCount(sendItem.getQtyReturnApply().intValue());
                }
                itemDTO.setRefundAmount(sendItem.getAmtReturn());
                itemDTO.setGiftFlag(sendItem.getGift());
                returnOffsetItemDTOList.add(itemDTO);
            }
            dto.setItemList(returnOffsetItemDTOList);
        }
        return dto;
    }

    public void updateOcBReturnOrder(IpBStandplatRefund ipBStandplatRefund, List<OcBReturnOrder> ocBReturnOrders,
                                     List<OcBReturnOrderRefund> orderRefunds) {
        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
            if (StringUtils.isNotEmpty(ocBReturnOrder.getReturnId())) {
                continue;
            }
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            returnOrder.setId(ocBReturnOrder.getId());
            //平台退款单号
            returnOrder.setReturnId(ipBStandplatRefund.getReturnNo());
            //卖家昵称
            returnOrder.setBuyerNick(ipBStandplatRefund.getBuyerNick());
            //申请退款时间
            returnOrder.setReturnCreateTime(ipBStandplatRefund.getCreated());
            //最后修改时间
            returnOrder.setLastUpdateTime(ipBStandplatRefund.getModified());
            //货物退回时间
            //returnOrder.setReturnTime(ipBStandplatRefund.getGoodReturnTime());
            //退款说明
            returnOrder.setReturnDesc(ipBStandplatRefund.getReturnReason());
            //商品应退金额(
            returnOrder.setReturnAmtList(ipBStandplatRefund.getRefundAmount());
            //售后/售中
            //returnOrder.setReturnPhase(ipBStandplatRefund.getRefundPhase());
            //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
            returnOrder.setReturnAmtActual(ipBStandplatRefund.getRefundAmount());
            //卖家昵称
            //returnOrder.setSellerNick(ipBStandplatRefund.getSellerNick());
            //物流公司名称
            String companyName = ipBStandplatRefund.getCompanyName();
            //退回物流单号
            returnOrder.setLogisticsCode(ipBStandplatRefund.getLogisticsNo());
            returnOrder.setCpCLogisticsEname(companyName);
            // 运费 by 秦俊龙
            returnOrder.setReturnAmtShip(ipBStandplatRefund.getReturnShipamount());
            //加入“空运单号延迟推单有效时间”字段
            returnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder));
            ocBReturnOrderMapper.updateById(returnOrder);
        }

        List<IpBStandplatRefundItem> ipBStandplatRefundItems = standplatRefundItemMapper.selectRefundItemByRefundId(ipBStandplatRefund.getId());
        List<String> subOrderIdList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(ipBStandplatRefundItems)){
            subOrderIdList = ipBStandplatRefundItems.stream().filter(x -> x.getSubOrderId() != null).map(IpBStandplatRefundItem::getSubOrderId).collect(Collectors.toList());
        }
        for (OcBReturnOrderRefund returnOrderRefund : orderRefunds) {
            if (returnOrderRefund.getOid() != null
                    && (returnOrderRefund.getOid().equals(ipBStandplatRefund.getReturnNo())
                    || returnOrderRefund.getOid().equals(ipBStandplatRefund.getSubOrderId())
                    || subOrderIdList.contains(returnOrderRefund.getOid()))) {
                OcBReturnOrderRefund orderRefund = new OcBReturnOrderRefund();
                orderRefund.setId(returnOrderRefund.getId());
                Integer returnStatus = ipBStandplatRefund.getReturnStatus();
                String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
                orderRefund.setRefundStatus(status);
                orderRefund.setRefundBillNo(ipBStandplatRefund.getReturnNo());
                orderRefund.setAmtPtRefund(ipBStandplatRefund.getRefundAmount());
                returnOrderRefund.setRefundStatus(status);
                QueryWrapper<OcBReturnOrderRefund> wrapper = new QueryWrapper<>();
                wrapper.eq("id", returnOrderRefund.getId());
                wrapper.eq("oc_b_return_order_id", returnOrderRefund.getOcBReturnOrderId());
                //更新之前分库建必须设置为空
                ocBReturnOrderRefundMapper.update(orderRefund, wrapper);
            }
        }

    }

    /**
     * 针对贝贝中间表无子单号处理
     *
     * @param ipBStandplatRefund
     * @param ocBReturnOrders
     * @param orderRefunds
     */
    public void fullUpdateOcBReturnOrder(BigDecimal price, IpBStandplatRefund ipBStandplatRefund, List<OcBReturnOrder> ocBReturnOrders,
                                         List<OcBReturnOrderRefund> orderRefunds) {
        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
            if (StringUtils.isNotEmpty(ocBReturnOrder.getReturnId())) {
                continue;
            }
            OcBReturnOrder returnOrder = new OcBReturnOrder();
            returnOrder.setId(ocBReturnOrder.getId());
            //平台退款单号
            returnOrder.setReturnId(ipBStandplatRefund.getReturnNo());
            //卖家昵称
            returnOrder.setBuyerNick(ipBStandplatRefund.getBuyerNick());
            //申请退款时间
            returnOrder.setReturnCreateTime(ipBStandplatRefund.getCreated());
            //最后修改时间
            returnOrder.setLastUpdateTime(ipBStandplatRefund.getModified());
            //退款说明
            returnOrder.setReturnDesc(ipBStandplatRefund.getReturnReason());
            //商品应退金额(
            returnOrder.setReturnAmtList(price);
            //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
            returnOrder.setReturnAmtActual(price);
            //物流公司名称
            String companyName = ipBStandplatRefund.getCompanyName();
            //退回物流单号
            returnOrder.setLogisticsCode(ipBStandplatRefund.getLogisticsNo());
            returnOrder.setCpCLogisticsEname(companyName);
            // 运费 by 秦俊龙
            returnOrder.setReturnAmtShip(ipBStandplatRefund.getReturnShipamount());
            ocBReturnOrderMapper.updateById(returnOrder);
        }
        for (OcBReturnOrderRefund returnOrderRefund : orderRefunds) {
            if (returnOrderRefund.getOid() != null && returnOrderRefund.getOid().equals(ipBStandplatRefund.getReturnNo())) {
                OcBReturnOrderRefund orderRefund = new OcBReturnOrderRefund();
                orderRefund.setId(returnOrderRefund.getId());
                Integer returnStatus = ipBStandplatRefund.getReturnStatus();
                String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
                orderRefund.setRefundStatus(status);
                orderRefund.setRefundBillNo(ipBStandplatRefund.getReturnNo());
                orderRefund.setAmtPtRefund(ipBStandplatRefund.getRefundAmount());
                returnOrderRefund.setRefundStatus(status);
                QueryWrapper<OcBReturnOrderRefund> wrapper = new QueryWrapper<>();
                wrapper.eq("id", returnOrderRefund.getId());
                wrapper.eq("oc_b_return_order_id", returnOrderRefund.getOcBReturnOrderId());
                //更新之前分库建必须设置为空
                ocBReturnOrderRefundMapper.update(orderRefund, wrapper);
            }
        }

    }

    /**
     * 查询发货后退款单是否存在
     *
     * @param refundId
     * @return
     */
    public boolean isRefundSlipAfExist(String refundId) {
        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendMapper.selectOcBReturnAfSendByRefundId(refundId);
        return ocBReturnAfSend == null;
    }


    @Transactional(rollbackFor = Exception.class)
    public void refundOrderClose(List<Long> refundOrderIds, List<OmsOrderRelation> omsOrderRelation,
                                 IpBStandplatRefund ipBStandplatRefund, User user) {
        List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectOcBReturnOrderByOrderIds(refundOrderIds);
        //关闭发货后退款单
        omsRefundOrderService.closedRefundSlip(ipBStandplatRefund.getReturnNo());
        if (CollectionUtils.isNotEmpty(ocBReturnOrders)) {
            JSONObject jsonObject = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            List<OcBReturnOrderRefund> ocBReturnOrderRefunds = new ArrayList<>();
            CusRedisTemplate<String, Long> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
            for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
                //
                List<OcBReturnOrderRefund> refundList = ocBReturnOrderRefundMapper.selectByOcOrderId(ocBReturnOrder.getId());
                ocBReturnOrderRefunds.addAll(refundList);
                Integer returnStatus = ocBReturnOrder.getReturnStatus();
                if (ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnStatus)) {
                    jsonArray.add(ocBReturnOrder.getId());
                    String logisticsCode = ocBReturnOrder.getLogisticsCode();
                    String returnId = ocBReturnOrder.getReturnId();
                    if (StringUtils.isNotEmpty(logisticsCode)) {
                        String redisKey = BllRedisKeyResources.getOmsReturnOrderLogisticsKey(logisticsCode);
                        objRedisTemplate.delete(redisKey);
                    }
                    if (StringUtils.isNotEmpty(returnId)) {
                        String redisKey = BllRedisKeyResources.getOmsReturnOrderReturnIdKey(returnId);
                        objRedisTemplate.delete(redisKey);
                    }

                }
            }
            if (CollectionUtils.isNotEmpty(jsonArray)) {
                jsonObject.put("ids", jsonArray);
                ValueHolderV14 holderV14 = ocCancelChangingOrRefundService.orRefundService(jsonObject, user, Boolean.FALSE);
                int code = Tools.getInt(holderV14.getCode(), -1);
                if (code == 0) {
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            "", ipBStandplatRefund);
                    if (CollectionUtils.isNotEmpty(ocBReturnOrderRefunds)) {
                        for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefunds) {
                            OcBOrderItem item = new OcBOrderItem();
                            item.setId(ocBReturnOrderRefund.getOcBOrderItemId());
                            item.setOcBOrderId(ocBReturnOrderRefund.getOcBOrderId());
                            item.setQtyReturnApply(BigDecimal.ZERO);
                            omsOrderItemService.updateOcBOrderItem(item, ocBReturnOrderRefund.getOcBOrderId());
                        }
                    }
                    //更新中间表转换转状态
                    this.updateOrder(omsOrderRelation);
                    //插入日志
                    //this.insertLogs(jsonArray);
                } else {
                    String remark = holderV14.getMessage();
                    if (checkReturnOrderData(ipBStandplatRefund)) {
                        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                remark, ipBStandplatRefund);
                    } else {
                        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                                remark, ipBStandplatRefund);
                    }
                }
            } else {
                String remark = SysNotesConstant.SYS_REMARK33;
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBStandplatRefund);
            }
        }
    }


    private void updateOrder(List<OmsOrderRelation> omsOrderRelation) {
        for (OmsOrderRelation orderRelation : omsOrderRelation) {
            OcBOrder ocBOrder1 = orderRelation.getOcBOrder();
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(ocBOrder1.getId());
            //无退货
            ocBOrder.setReturnStatus(0);
            omsOrderService.updateOrderInfo(ocBOrder);
        }
    }

    /**
     * 检验原单不存在的时间
     *
     * @return
     */
    private boolean checkReturnOrderData(IpBStandplatRefund ipBStandplatRefund) {
        Date date = new Date();
        //判断退单时间是否超过三天
        Date created = ipBStandplatRefund.getCreated();
        Long threeDays = 3 * 24 * 60 * 60 * 1000L + created.getTime();
        return threeDays < date.getTime();
    }

    /**
     * 拦截关闭时查询退款单是否存在的处理
     *
     * @param ipBStandplatRefund
     * @return
     */
    public List<Long> interceptOrderIsExist(IpBStandplatRefund ipBStandplatRefund, List<IpBStandplatRefundItem> ipBStandplatRefundItem) {
        List<Long> existReturnOrder = this.isExistReturnOrderRefundByReturnIdNew(ipBStandplatRefund.getReturnNo());
        // 假如退款单为空
        if (CollectionUtils.isEmpty(existReturnOrder)) {
            // 中间表的平台单号 String tid = ipBStandplatRefund.getOrderNo();
            List<String> ooids = ipBStandplatRefundItem.stream().filter(item -> item.getSubOrderId() != null).map(IpBStandplatRefundItem::getSubOrderId).collect(Collectors.toList());
            if (ooids.isEmpty()) {
                return null;
            }
            // 根据
            List<Long> refundByoid = this.isExistReturnOrderRefundByOid(StringUtils.join(ooids, ","));
            //将必要的退款数据更新到对应的退货单
            if (CollectionUtils.isNotEmpty(refundByoid)) {
                List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectReturnOrderListByOrderIds(refundByoid);
                List<OcBReturnOrderRefund> orderRefunds = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(refundByoid);
                this.updateOcBReturnOrder(ipBStandplatRefund, ocBReturnOrders, orderRefunds);
                List<OcBReturnOrderRefund> orderRefundList = orderRefunds.stream().filter(p -> p.getRefundStatus() != null
                        && (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(p.getRefundStatus())
                        || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(p.getRefundStatus()))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(orderRefundList) && orderRefunds.size() == orderRefundList.size()) {
                    return refundByoid;
                } else {
                    return null;
                }
            }
        } else {
            // 根据 oc_b_return_order_id 找到退货单明细
            List<OcBReturnOrderRefund> orderRefunds = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(existReturnOrder);
            //  过滤出 退款状态不是空 && (订单状态是关闭 || 卖家拒绝退款 )
            List<OcBReturnOrderRefund> orderRefundList = orderRefunds.stream().filter(p -> p.getRefundStatus() != null
                    && (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(p.getRefundStatus())
                    || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(p.getRefundStatus()))).collect(Collectors.toList());
            // 返回 OC_B_RETURN_ORDER_ID 退款单id
            if (CollectionUtils.isNotEmpty(orderRefundList) && orderRefunds.size() == orderRefundList.size()) {
                return existReturnOrder;
            } else {
                return null;
            }
        }
        return existReturnOrder;
    }

    /**
     * 根据明细表的退款单号查询退单是否存在
     *
     * @param refundNo
     * @return
     */
    public List<Long> isExistReturnOrderRefundByReturnId(String refundNo) {
        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByRefundIdAndStatus(refundNo);
        if (CollectionUtils.isNotEmpty(ids)) {
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(new ArrayList<>(ids));
            if (CollectionUtils.isNotEmpty(list)) {
                return list.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            }
        }
        return null;
    }


    public List<Long> isExistReturnOrderRefundByReturnIdNew(String refundNo) {
        Set<Long> ids = ES4ReturnOrder.findIdByReturnId(refundNo);
        if (CollectionUtils.isNotEmpty(ids)) {
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(new ArrayList<>(ids));
            if (CollectionUtils.isNotEmpty(list)) {
                return list.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            }
        }
        return null;
    }


    /**
     * 根据明细表的oid查询退货单是否存在
     *
     * @param oid
     * @return
     */
    public List<Long> isExistReturnOrderRefundByOid(String oid) {
        if (oid == null) {
            return null;
        }
        Set<Long> ids = ES4ReturnOrder.findReturnOrderIdByOid(oid);
        if (CollectionUtils.isNotEmpty(ids)) {
            //排除已经取消的退换货订单
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(new ArrayList<>(ids));
            if (CollectionUtils.isNotEmpty(list)) {
                return list.stream().map(OcBReturnOrder::getId).collect(Collectors.toList());
            }
        }
        return null;
    }


    /**
     * 客退的退单是否存在的处理
     *
     * @param ipBStandplatRefund
     * @return
     */
    public ReturnOrderRelation goodsAfterOrderIsExist(IpBStandplatRefund ipBStandplatRefund, List<IpBStandplatRefundItem> ipBStandplatRefundItem) {
        ReturnOrderRelation relation = new ReturnOrderRelation();
        List<Long> existReturnOrder = this.isExistReturnOrderRefundByReturnId(ipBStandplatRefund.getReturnNo());
        if (CollectionUtils.isEmpty(existReturnOrder)) {
            List<String> ooids = ipBStandplatRefundItem.stream().filter(item -> item.getSubOrderId() != null).map(IpBStandplatRefundItem::getSubOrderId).collect(Collectors.toList());
            if (ooids.isEmpty()) {
                relation.setIds(existReturnOrder);
                relation.setFlag(false);
                return relation;
            }
            List<Long> refundByoid = this.isExistReturnOrderRefundByOid(StringUtils.join(ooids, ","));
            //将必要的退款数据更新到对应的退货单
            if (CollectionUtils.isNotEmpty(refundByoid)) {
                List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectReturnOrderListByOrderIds(refundByoid);
                List<OcBReturnOrderRefund> orderRefunds = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(refundByoid);
                this.updateOcBReturnOrder(ipBStandplatRefund, ocBReturnOrders, orderRefunds);
                relation.setIds(refundByoid);
                relation.setFlag(true);
            }
        } else {
            relation.setIds(existReturnOrder);
            relation.setFlag(false);
        }
        return relation;
    }

    /**
     * 通过退单明细计算金额
     *
     * @param orderInfo
     * @param ipBStandplatRefund
     */
    public void setAfOrderAmount(OmsStandPlatRefundRelation orderInfo, IpBStandplatRefund ipBStandplatRefund) {
        // 退款金额
        BigDecimal refundFee = BigDecimal.ZERO;
        // 假如不是整单退
        if (!orderInfo.isFullRefund()) {
            for (IpBStandplatRefundItem current : orderInfo.getIpBStandplatRefundItem()) {
                BigDecimal bigDecimal = Optional.ofNullable(current.getRefundFee()).orElse(BigDecimal.ZERO);
                refundFee = refundFee.add(bigDecimal);
            }
            if (BigDecimal.ZERO.compareTo(refundFee) != 0) {
                ipBStandplatRefund.setRefundAmount(refundFee);
            }
        } else {
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            for (OmsOrderRelation orderRelation : omsOrderRelation) {
                if (orderRelation.getOcBOrder() != null) {
                    if (!omsRefundOrderService.isNullOrZero(orderRelation.getOcBOrder().getOrderAmt())) {
                        refundFee = refundFee.add(orderRelation.getOcBOrder().getOrderAmt());
                    }
                }
            }
            ipBStandplatRefund.setRefundAmount(refundFee);
        }
    }
}
