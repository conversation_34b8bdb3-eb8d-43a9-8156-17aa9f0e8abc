package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 重单服务
 *
 * @author: heliu
 * @since: 2019/4/18
 * create at : 2019/4/18 13:58
 */
@Component
@Slf4j
public class OrderHeavySingleService {

    @Autowired
    private OmsOrderService omsOrderService;

    /**
     * 判断是否重单
     *
     * @param ocBorderDto 订单对象
     * @param user        用户对象
     * @throws NDSException 异常
     */
    public void heavySingleSveice(OcBOrder ocBorderDto, User user) throws NDSException {

        JSONObject workey = new JSONObject();

        //平台单号不为空但是订单补充信息为空的情况
        if (StringUtils.isNotBlank(ocBorderDto.getSourceCode()) && StringUtils.isBlank(ocBorderDto.getSuffixInfo())) {
            //    JSONObject data = ES4Order.getIdBySourceCodeOrderById(ocBorderDto.getSourceCode());
            List<Long> ids = GSI4Order.getIdListBySourceCode(ocBorderDto.getSourceCode());
            checkEsOrder(ids, ocBorderDto);
        } else if (StringUtils.isNotBlank(ocBorderDto.getSourceCode()) && StringUtils.isNotBlank(ocBorderDto.getSuffixInfo())) {

          /*  JSONObject data = ES4Order.getIdBySourceCodeAndSuffixInfoOrderById(ocBorderDto.getSourceCode(),
                    ocBorderDto.getSuffixInfo());*/
            List<OcBOrder> orders = GSI4Order.getOrderListBySourceCode(ocBorderDto.getSourceCode());
            if (CollectionUtils.isEmpty(orders)) {
                throw new NDSException("当前订单不存在");
            }
            List<Long> idsList =
                    orders.stream().filter(x -> StringUtils.equalsIgnoreCase(ocBorderDto.getSuffixInfo(), x.getSuffixInfo()))
                            .map(x -> x.getId()).collect(Collectors.toList());
            checkEsOrder(idsList, ocBorderDto);
        }
    }


    public void checkEsOrder(List<Long> orderIds, OcBOrder ocBorderDto) {

        int totalOrderId = 0;
        StringBuffer sb = new StringBuffer();
       /* if (search.getLong("code") < 0 || search.get("data") == null) {
            throw new NDSException(Resources.getMessage("订单OrderId " + ocBorderDto.getId() + "当前订单在ES中不存在！"));
        }*/
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new NDSException(Resources.getMessage("订单OrderId " + ocBorderDto.getId() + "当前订单在ES中不存在！"));
        }
        //   JSONArray jsonArray = search.getJSONArray("data");
        /*if (jsonArray != null && jsonArray.size() > 1) {
            for (int i = 0; i < orders.size(); i++) {
                //在数据库排除已取消的订单
                if (omsOrderService.selectOrderCancelAndCancelInfo(jsonArray.getJSONObject(i).getLong("ID")) != null) {
                    if (jsonArray.getJSONObject(i).getLong("ID").compareTo(ocBorderDto.getId()) != 0) {
                        log.debug("ES重单的订单OrderId" + jsonArray.getJSONObject(i).getLong("ID"));
                        totalOrderId++;
                        sb.append(jsonArray.getJSONObject(i).getLong("ID") + " ");
                    }
                }
            }
        }*/
        for (Long orderId : orderIds) {
            if (omsOrderService.searchDuplicateOrder(orderId) != null) {
                if (orderId.compareTo(ocBorderDto.getId()) != 0) {
                    totalOrderId++;
                    sb.append(orderId + " ");
                }
            }
        }
        //当有重单时抛异常
        if (totalOrderId > 0) {
            throw new NDSException(Resources.getMessage("订单OrderId " + ocBorderDto.getId() + "与" + sb.toString() + "订单重单，请人工进行处理！"));
        }
    }
}