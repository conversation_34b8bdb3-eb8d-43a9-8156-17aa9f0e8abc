package com.jackrain.nea.oc.oms.services;

import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.OcBOrderInvoiceInformMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBInvoiceNoticeRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.result.OcBInvoiceNoticeItemResult;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OcHeaderTypeEnum;
import com.jackrain.nea.oc.oms.nums.OcInvoiceStatusEnum;
import com.jackrain.nea.oc.oms.nums.OcInvoiceTypeEnum;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AmountCalcUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: huang.zaizai
 * @since: 2019/8/28
 * create at : 2019/8/28 09:00
 */
@Slf4j
@Component
public class InvoiceNoticeAutomaticService {
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderInvoiceInformMapper orderInvoiceInformMapper;
    @Autowired
    private InvoiceNoticeSaveService invoiceNoticeSaveService;
    @Autowired
    private InvoiceTableQueryService invoiceTableQueryService;

    /**
     * 生成待审批的开票通知开票
     *
     * @param ocBOrderId
     * @return ValueHolderV14
     */
    public ValueHolderV14 createInvoiceNotice(Long ocBOrderId, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        vh.setCode(ResultCode.SUCCESS);
        if (null == ocBOrderId) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数为空!");
            return vh;
        }
        try {
            OcBOrder ocBOrder = orderMapper.selectByID(ocBOrderId);
            if (ocBOrder == null) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("当前订单已不存在！");
                return vh;
            }
            //构建主表
            OcBInvoiceNoticeRelation relation = buildInvoiceNotice(ocBOrder);
            //构建子表
            buildProAndItem(ocBOrder, relation, relation.getInvoiceNotice());
            //保存
            if (CollectionUtils.isEmpty(relation.getInvoiceNoticeItems())
                    || CollectionUtils.isEmpty(relation.getInvoiceNoticePros())) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("当前订单不存在明细数据，开票失败！");
                return vh;
            }
            ValueHolder valueHolder = invoiceNoticeSaveService.insertInvoiceNoitce(user, relation, null);
            if (!valueHolder.isOK()) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(valueHolder.getData().getOrDefault("message", "保存开票通知失败!").toString());
                return vh;
            }
        } catch (Exception e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
            return vh;
        }
        return vh;
    }

    /**
     * 子表构建
     *
     * @param ocBOrder
     * @param relation
     * @param invoiceNotice
     */
    private void buildProAndItem(OcBOrder ocBOrder, OcBInvoiceNoticeRelation relation, OcBInvoiceNotice invoiceNotice) {
        //子表
        List<OcBOrderItem> orderItemList = orderItemMapper.selectOrderItemListByOrderId(ocBOrder.getId());
        List<OcBInvoiceNoticePro> proList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(orderItemList)) {
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                OcBInvoiceNoticePro invoiceNoticePro = new OcBInvoiceNoticePro();

                invoiceNoticePro.setId(-1L);
                invoiceNoticePro.setCpCShopId(invoiceNotice.getCpCShopId());
                invoiceNoticePro.setCpCShopTitle(invoiceNotice.getCpCShopTitle());
                invoiceNoticePro.setBillNo(ocBOrder.getBillNo());
                invoiceNoticePro.setSourceCode(invoiceNotice.getSourceCode());
                invoiceNoticePro.setPsCProId(ocBOrderItem.getPsCProId());
                invoiceNoticePro.setPsCProEcode(ocBOrderItem.getPsCProEcode());
                invoiceNoticePro.setPsCProEname(ocBOrderItem.getPsCProEname());
                invoiceNoticePro.setPsCSizeId(ocBOrderItem.getPsCSizeId());
                invoiceNoticePro.setPsCSizeEcode(ocBOrderItem.getPsCSizeEcode());
                invoiceNoticePro.setPsCSizeEname(ocBOrderItem.getPsCSizeEname());
                invoiceNoticePro.setQty(ocBOrderItem.getQty());
                invoiceNoticePro.setAmt(ocBOrderItem.getRealAmt());
                invoiceNoticePro.setQtyRefund(ocBOrderItem.getQtyRefund());
                invoiceNoticePro.setAmtRefund(ocBOrderItem.getAmtRefund());

                proList.add(invoiceNoticePro);
            }
        }
        relation.setInvoiceNoticePros(proList);

        List<Integer> proIdList = new ArrayList<>();
        for (OcBInvoiceNoticePro pro : proList) {
            proIdList.add(pro.getPsCProId().intValue());
        }
        List<PsCPro> psCProList = psRpcService.queryProByIds(proIdList);
        Map<Long, PsCPro> proMap = psCProList.stream().collect(Collectors.toMap(PsCPro::getId, Function.identity()));
        //汇总明细信息
        Map<String, OcBInvoiceNoticeItemResult> itemMap = invoiceTableQueryService.getInvoiceNoticeItemMap(proList, proMap);

        List<OcBInvoiceNoticeItem> itemList = Lists.newArrayList();
        BigDecimal amt = BigDecimal.ZERO;
        for (OcBInvoiceNoticeItemResult itemResult : itemMap.values()) {
            OcBInvoiceNoticeItem item = new OcBInvoiceNoticeItem();
            BeanUtils.copyProperties(itemResult, item);
            itemList.add(item);
            amt = AmountCalcUtils.addBigDecimal(amt, itemResult.getAmtTaxable());
        }
        relation.getInvoiceNotice().setAmt(amt);
        relation.setInvoiceNoticeItems(itemList);
    }

    /**
     * 主表构建
     *
     * @param ocBOrder
     * @return OcBInvoiceNoticeRelation
     */
    private OcBInvoiceNoticeRelation buildInvoiceNotice(OcBOrder ocBOrder) {
        OcBInvoiceNoticeRelation relation = new OcBInvoiceNoticeRelation();
        //主表
        OcBInvoiceNotice invoiceNotice = new OcBInvoiceNotice();

        invoiceNotice.setId(-1L);
        invoiceNotice.setEstatus(OcInvoiceStatusEnum.NOT_AUDITED.getVal());
        invoiceNotice.setSourceCode(ocBOrder.getSourceCode());
        invoiceNotice.setOcBOrderId(ocBOrder.getId().toString());
        invoiceNotice.setCpCShopId(ocBOrder.getCpCShopId());
        invoiceNotice.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        if (invoiceNotice.getCpCShopId() != null) {
            CpShop cpShop = cpRpcService.selectShopById(invoiceNotice.getCpCShopId());
            if (cpShop != null) {
                invoiceNotice.setInvoiceCompany(cpShop.getInvoiceCompany());
            }
        }
        invoiceNotice.setBuyerRamark(ocBOrder.getBuyerMessage());
        invoiceNotice.setSellerRemark(ocBOrder.getSellerMemo());
        if (StringUtils.isNotBlank(invoiceNotice.getBuyerRamark()) && invoiceNotice.getBuyerRamark().length() > 100) {
            invoiceNotice.setBuyerRamark(StringUtils.substring(invoiceNotice.getBuyerRamark(), 0, 100));
        }
        if (StringUtils.isNotBlank(invoiceNotice.getSellerRemark()) && invoiceNotice.getSellerRemark().length() > 500) {
            invoiceNotice.setSellerRemark(StringUtils.substring(invoiceNotice.getSellerRemark(), 0, 500));
        }
        //查询订单记录开票信息表
        OcBOrderInvoiceInform inform = orderInvoiceInformMapper.selectInvoiceInformByRefId(ocBOrder.getId(), OcBOrderConst.IS_ACTIVE_YES);
        if (inform != null) {
            invoiceNotice.setInvoiceType(inform.getInvoiceType());
            invoiceNotice.setHeaderType(inform.getHeaderType());
            invoiceNotice.setHeaderName(inform.getHeaderName());
            invoiceNotice.setTaxpayerNo(inform.getTaxpayerNo());
            invoiceNotice.setEmail(inform.getEmail());
            invoiceNotice.setCompanyAddress(inform.getCompany());
            invoiceNotice.setPhoneNo(inform.getPhoneNo());
            invoiceNotice.setOpeningBank(inform.getOpeningBank());
            invoiceNotice.setOpeningBankAccount(inform.getOpeningBankAccount());
            invoiceNotice.setInvoiceRemark(inform.getInvoiceRemark());
            invoiceNotice.setReceiveName(StringUtils.isBlank(inform.getReceiveName()) ?
                    ocBOrder.getReceiverName() : inform.getReceiveName());
            invoiceNotice.setReceiverMobile(StringUtils.isBlank(inform.getReceiverMobile()) ?
                    ocBOrder.getReceiverMobile() : inform.getReceiverMobile());
            invoiceNotice.setReceiverAddress(StringUtils.isBlank(inform.getReceiverAddress()) ?
                    ocBOrder.getReceiverAddress() : inform.getReceiverAddress());
        } else {
            invoiceNotice.setInvoiceType(OcInvoiceTypeEnum.ELE_INVOICE.getKey());
            invoiceNotice.setHeaderType(OcHeaderTypeEnum.PERSONAL.getKey());
            invoiceNotice.setHeaderName(ocBOrder.getInvoiceHeader());
            invoiceNotice.setReceiveName(ocBOrder.getReceiverName());
            invoiceNotice.setReceiverMobile(ocBOrder.getReceiverMobile());
            invoiceNotice.setReceiverAddress(ocBOrder.getReceiverAddress());
        }
        relation.setInvoiceNotice(invoiceNotice);

        return relation;
    }
}
