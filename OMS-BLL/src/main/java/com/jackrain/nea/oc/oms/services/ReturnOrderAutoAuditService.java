package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 退单自动审核
 *
 * @author: 周琳胜
 * @since: 2019/4/2
 * create at : 2019/4/2 14:45
 */
@Slf4j
@Component
public class ReturnOrderAutoAuditService {

    @Autowired
    OcBOrderMapper ocBOrderMapper;

    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    @Autowired
    OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    ReturnOrderAuditService returnOrderAuditService;

    /**
     * 退单自动审核
     *
     * @param obj  入参
     * @param user 用户
     * @return 结果
     */
    // @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 returnOrderAutoAudit(JSONObject obj, User user) {

        // 1. 参数校验
        AssertUtil.notNull(obj, Resources.getMessage("请求参数不能为空!", user.getLocale()));
        Long ocBReturnOrderId = obj.getLong("ID");

        // 2.退换货订单对象
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(ocBReturnOrderId);
        recordReturnLog(ocBReturnOrder == null, ocBReturnOrderId, "数据异常：未查到退换货单!", user);

        // 3. 获得退货商品明细
        QueryWrapper<OcBReturnOrderRefund> Wrapper = new QueryWrapper<>();
        Wrapper.eq("oc_b_return_order_id", ocBReturnOrderId);
        List<OcBReturnOrderRefund> refundItems = ocBReturnOrderRefundMapper.selectList(Wrapper);
        recordReturnLog(CollectionUtils.isEmpty(refundItems), ocBReturnOrderId, "未查询到退货商品数据!", user);

        // 4. 判断退货单时退货金额不能大于默认金额上限
        boolean checkAmtLimit = checkReturnAmtActualWithDefault(ocBReturnOrder);
        recordReturnLog(!checkAmtLimit, ocBReturnOrderId, "退款金额大于设置金额上限", user);

        // 5.查询对应原单明细
        Long origOrderId = ocBReturnOrder.getOrigOrderId();
        QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("oc_b_order_id", origOrderId);
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectList(queryWrapper);
        recordReturnLog(!checkAmtLimit, ocBReturnOrderId, "未查询到原单商品数据，请确认数据!", user);

        // 6. 校验金额,数量
        String msg = checkQtyAndAmt(refundItems, orderItems);

        // 7. 非退单, 校验不通过, 记录退单日志
        boolean isReturnOrder = OcReturnBillTypeEnum.RETURN.getVal().equals(ocBReturnOrder.getBillType());
        boolean isReissue = OcReturnBillTypeEnum.REISSUE.getVal().equals(ocBReturnOrder.getBillType());
        if (!isReturnOrder && !isReissue) {
            recordReturnLog(msg != null, ocBReturnOrderId, msg, user);
        }
        log.debug(LogUtil.format("IpRpcService.sapAg.obj:{}",obj));
        // 8. 审核
        return invokeAuditReturnOrder(user, ocBReturnOrderId);
    }

    /**
     * 记录错误日志
     *
     * @param isRecord 是否记录
     * @param id       退换货单编号
     * @param msg      异常信息
     * @param user     操作用户
     */
    private void recordReturnLog(boolean isRecord, Long id, String msg, User user) {
        if (isRecord) {
            returnOrderAuditService.recordReturnOrderLog(id, "退换货单审核", msg, true, user);
            throw new NDSException(msg);
        }
    }

    /**
     * @param user          user
     * @param returnOrderId 退单编号
     * @return 审核结果
     */
    private ValueHolderV14 invokeAuditReturnOrder( User user, Long returnOrderId) {
        log.debug(LogUtil.format("invokeAuditReturnOrder.sapAg.returnOrderId:{}", returnOrderId));
        ValueHolderV14 vh;
        try {
            vh = returnOrderAuditService.returnOrderAudit(returnOrderId, true, user);
            if (!vh.isOK()) {
                returnOrderAuditService.recordReturnOrderLog(returnOrderId, "退换货单审核", vh.getMessage(),
                        true, user);
            }
        } catch (Exception e) {
            log.error("invokeAuditReturnOrder.Exception ", e);
            String msg = "退单审核异常";
            if (e != null && e.getMessage() != null) {
                msg = e.getMessage().length() > 200 ? e.getMessage().substring(0, 200) : e.getMessage();
            }
            returnOrderAuditService.recordReturnOrderLog(returnOrderId, "退换货单审核", msg, true, user);
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(msg);
        }
        return vh;
    }

    /**
     * 校验金额.数量
     *
     * @param refundItems 退换货订单.退货明细
     * @param orderItems  订单明细
     * @return 校验通过
     */
    private String checkQtyAndAmt(List<OcBReturnOrderRefund> refundItems, List<OcBOrderItem> orderItems) {

        Map<Long, OcBOrderItem> origItemMap = orderItems.stream()
                .filter(e -> e != null).collect(Collectors.toMap(OcBOrderItem::getId, e -> e, (e1, e2) -> e2));
        for (OcBReturnOrderRefund e : refundItems) {
            if (NumUtil.nullAndNe(e.getQtyRefund(), e.getQtyIn())) {
                return "条码: " + e.getPsCSkuEcode() + ", 明细编号: " + e.getId() + ", 申请数量与入库数量不一致";
            }

            OcBOrderItem origItem = origItemMap.get(e.getOcBOrderItemId());
            if (origItem == null) {
                continue;
            }
            BigDecimal amtRefund = NumUtil.init(e.getAmtRefund());
            BigDecimal realAmt = NumUtil.init(origItem.getRealAmt());
            if (NumUtil.ne(amtRefund, realAmt)) {
                return "条码: " + e.getPsCSkuEcode() + ", 明细编号: " + e.getId() + ", 退货金额与原单明细单行实际成交价不一致";
            }
        }
        return null;
    }


    /**
     * 金额上限校验
     *
     * @param ocBReturnOrder 待审核退单
     * @return 是否继续审核
     */
    private boolean checkReturnAmtActualWithDefault(OcBReturnOrder ocBReturnOrder) {
        // 退货总金额
        BigDecimal returnAmtActual = ocBReturnOrder.getReturnAmtActual();
        AssertUtil.assertException(returnAmtActual == null, "退款金额为null");
        // 获取单据类型（退单类型）
        Integer billType = ocBReturnOrder.getBillType();
        //自动审核比较金额默认值 1000
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer amt = config.getProperty("lts.AutoChargeBackTask.amtLimit", 1000);
        BigDecimal amtLimit = new BigDecimal(amt.toString());
        if (OcReturnBillTypeEnum.RETURN.getVal().equals(billType)) {
            return returnAmtActual.compareTo(amtLimit) < 0;
        } else if (OcReturnBillTypeEnum.EXCHANGE.getVal().equals(billType)) {
            return true;
        }
        return false;
    }
}
