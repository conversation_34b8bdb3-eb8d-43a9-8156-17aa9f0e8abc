package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPaymentMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPromotionMapper;
import com.jackrain.nea.oc.oms.matcher.live.LiveFlagEnum;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.MergeTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsMergeSplitOrderType;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderDetentionEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.MergeOrderModel;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.result.MergeEsHavingcountResult;
import com.jackrain.nea.oc.oms.model.result.MergeReturnResult;
import com.jackrain.nea.oc.oms.model.result.OrderDistinctResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromotion;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR> 孙勇生
 * create at:  2019/4/3  15:28
 * @description: 订单合并service
 */
@Slf4j
@Component
public class OrderMergeService {
    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    OwnMergeOrderService ownMergeOrderService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    OcBOrderPaymentMapper ocBOrderPaymentMapper;
    @Autowired
    OcBOrderPromotionMapper ocBOrderPromotionMapper;
    @Autowired
    PsRpcService psRpcService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;
    @Autowired
    CpRpcService cpRpcService;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private OmsAuditTaskService auditTaskService;

    @Autowired
    private OcMergeOrderService ocMergeOrderService;
    @Autowired
    private OcBOrderItemExtService ocBOrderItemExtService;

    public static final int MAX_NUM = 998;

    private final static int MERGE_LIMIT_QUANTITY = 50;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;
    @Autowired
    private OmsBusinessTypeStService omsBusinessTypeStService;
    @Autowired
    private OrderAmountUtil orderAmountUtil;
    @Autowired
    private SequenceGenUtil sequenceGenUtil;

    @Transactional(rollbackFor = Exception.class)
    public boolean merge(List<Long> ids, String lgType, User user) {
        if (CollectionUtils.isEmpty(ids) || ids.size() == 1) {

            return false;
        }
        //查询订单信息
        List<OcBOrder> dbOcBOrderList = ocBOrderMapper.selectOrderListByIds(ids);

        if (CollectionUtils.isEmpty(dbOcBOrderList) || dbOcBOrderList.size() == 1) {
            return false;
        }

        //反审核操作
        //Iterator<OcBOrder> iterator = dbOcBOrderList.iterator();
        //while (iterator.hasNext()) {
        //    OcBOrder order = iterator.next();
        //    if (order.getOrderStatus().intValue() == OmsOrderStatus.CHECKED.toInteger()) {
        //        if (!toExamineOrder(order, user)) {
        //            if (log.isDebugEnabled()) {
        //                log.debug("OrderId={},合单反审核失败", order.getId());
        //            }
        //            iterator.remove();
        //            ids.remove(order.getId());
        //        }
        //    }
        //}

//        if (CollectionUtils.isEmpty(ids) || ids.size() == 1) {
//            return false;
//        }

        //开始组装操作数据库的数据
        OcBOrderRelation ocBOrderRelation = buildMergeOrderInfo(dbOcBOrderList, ids, user, false);
        try {
            ownMergeOrderService.insertOrderAndInvokeSgSrv(dbOcBOrderList, ocBOrderRelation, lgType, user, false);
            OcBOrder order = dbOcBOrderList.get(0);
            distributeLogistics(ocBOrderRelation, user, order);

        } catch (Exception e) {
            try {
                omsOrderLogService.addUserOrderLog(ocBOrderRelation.getOrderId(),
                        ocBOrderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.ORDER_MERGE_FAILED.getKey(),
                        e.getMessage(), "", "", user
                );
            } catch (Exception ex) {
                log.error("合并订单订单新增日志异常", ex);
            }

            throw new NDSException(e.getMessage());
        }

        return true;
    }

    /**
     * 分批合并
     *
     * @param dataList 校验过可以合并的单据ID
     */
    public MergeReturnResult mergeBatch(List<Long> dataList, String lgType, User user) {

        if (CollectionUtils.isEmpty(dataList)) {
            return MergeReturnResult.MERGE_IS_NULL;
        }

        String mergeLimitQuantityStr = redisOpsUtil.strRedisTemplate.opsForValue().get(
                "business_system:merge_limit_quantity");

        Integer mergeLimitQuantity = StringUtils.isBlank(mergeLimitQuantityStr) ? MERGE_LIMIT_QUANTITY
                : Integer.valueOf(mergeLimitQuantityStr);

        List<List<Long>> basePageList = BllCommonUtil.getBasePageList(dataList, mergeLimitQuantity);


        int size = 0;

        for (List<Long> ids : basePageList) {
            try {
                OrderMergeService bean = ApplicationContextHandle.getBean(OrderMergeService.class);
                if (bean.merge(ids, lgType, user)) {
                    size++;
                }
            } catch (Exception e) {
                //合并异常的订单开启自动审核
                auditTaskService.updateAuditTaskByOrderId(ids);
                log.error(LogUtil.format("merge order exception, merge ids{},异常信息为:{}"), JSON.toJSONString(ids), Throwables.getStackTraceAsString(e));
            }
        }

        if (basePageList.size() == 0) {
            return MergeReturnResult.MERGE_IS_NULL;
        } else if (size == 0) {
            return MergeReturnResult.MERGE_FAILED;
        } else {
            return MergeReturnResult.MERGE_SUCCESS;
        }
    }

    /**
     * 合并订单按钮调用
     *
     * @return
     */
    public ValueHolder mergeOrderMenu(List<Long> ids, User user) {
        //1.先去数据库查询出这两单的数据 并锁定
        if (null == user) {
            user = SystemUserResource.getRootUser();
        }
        ValueHolder result = new ValueHolder();
        HashMap map = new HashMap();
        List<OcBOrder> dbList = ocBOrderMapper.selectBatchIds(ids);
        if (dbList.size() != ids.size()) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "单据已被更新,请刷新后重新合并");
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }

        //反审核订单失败的 剔除相同买家的分组
        Map<String, List<OcBOrder>> menuGroupMap = dbList.stream().collect(Collectors.groupingBy(
                goods -> goods.getCpCShopId() + "_" +
                        goods.getUserNick() + "_" +
                        goods.getCpCPhyWarehouseId() + "_" +
                        goods.getPlatform() + "_" +
                        goods.getOrderType() + "_" +
                        goods.getPayType() + "_" +
                        goods.getReceiverName() + "_" +
                        goods.getReceiverMobile() + "_" +
                        goods.getReceiverPhone() + "_" +
                        goods.getCpCRegionProvinceId() + "_" +
                        goods.getCpCRegionAreaId() + "_" +
                        goods.getCpCRegionCityId() + "_" +
                        goods.getReceiverAddress()
        ));

        int success = 0;
        int fail = 0;
        StringBuffer msg = new StringBuffer();
        for (Map.Entry<String, List<OcBOrder>> stringListEntry : menuGroupMap.entrySet()) {

            List<OcBOrder> value = stringListEntry.getValue();
            //合并订单 单个
            Boolean aBoolean = mergeOrder(value, user, msg);
            if (aBoolean) {
                success++;
            } else {
                fail++;
            }
        }
        map.put("code", fail == ResultCode.FAIL ? ResultCode.SUCCESS : ResultCode.FAIL);
        map.put("message", "合并成功条数：" + success + ",失败条数：" + fail);
        map.put("data", null);
        result.setData(map);
        return result;
    }

    /**
     * 合并订单 单个
     *
     * @param value
     * @param user
     * @return
     */
    private Boolean mergeOrder(List<OcBOrder> value, User user, StringBuffer msg) {
        if (CollectionUtils.isEmpty(value) || value.size() == 1) {
            msg.append("订单数量小于2不能合并!");
            return false;
        }
        //开始锁单
        Map<String, List> lockList = this.getLock(this.lockList(value));
        //锁集合用来解锁
        List<RedisReentrantLock> lockListKey = (List<RedisReentrantLock>) lockList.get("lockList");
        try {
            //如果有被其他人锁的单据 不能进行合并
            if (CollectionUtils.isNotEmpty(lockList.get("failLockIds"))) {
                msg.append("手动合单有被其他人锁的单据!");
                return false;
            }
            //重新查询数据 保证最新
            List<Long> ids = value.stream().map(OcBOrder::getId).collect(Collectors.toList());
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(ids);

            //校验单据是否符合合单的条件
            String checkResult = this.checkParam(ocBOrders);
            if (StringUtils.isNotEmpty(checkResult)) {
                msg.append(checkResult);
                return false;
            }

            //开始组装操作数据库的数据
            List<Long> idList = ocBOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());

            MergeReturnResult mergeReturnResult = mergeBatch(idList, MergeTypeEnum.MANUAL.code(), user);
            if (MergeReturnResult.MERGE_SUCCESS.equals(mergeReturnResult)) {
                msg.append("合并成功!");
                return true;
            } else if (MergeReturnResult.MERGE_FAILED.equals(mergeReturnResult)) {
                msg.append("合并失败!");
                return false;
            } else {
                msg.append("部分单据合并成功!");
                return true;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("手工合单异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            msg.append("订单手工合单异常" + e.getMessage());
            return false;
        } finally {
            for (RedisReentrantLock lockItem : lockListKey) {
                lockItem.unlock();
            }
        }
    }


    /**
     * 合单查询入口
     *
     * @param mergeOrderModel
     * @return
     */
    public List<MergeOrderModel> queryMergeOrderList(MergeOrderModel mergeOrderModel) {
        //先查询所有的分组
        List<MergeEsHavingcountResult> result = Lists.newArrayList();
        String[] groups = this.getGroups();//分组条件
        JSONObject filterKeyJo = this.checkFilter(mergeOrderModel);
        JSONObject whereKeys = this.getWhereKey(mergeOrderModel);//查询条件
        JSONObject jsonObject = ElasticSearchUtil.havingCount(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, whereKeys, filterKeyJo, "1~", groups);
        //去除平台=19 唯品会jintX的订单
        if (null != result) {
            Object data = jsonObject.get("data");
            if (null != data) {
                result = JSONArray.parseArray(data.toString(), MergeEsHavingcountResult.class);
                result = result.stream().filter(x -> null != x.getMergeOderGroups()).collect(Collectors.toList());
            }
        }
        List<MergeOrderModel> list = this.getMergeOrderList(result);
        return list;
    }

    /**
     * 组装合单的数据
     * 订单主表,明细,优惠信息,付款信息
     *
     * @param dbList
     * @param is2CDetention
     * @return
     */
    public OcBOrderRelation buildMergeOrderInfo(List<OcBOrder> dbList, List ids, User user, boolean is2CDetention) {
        //开始组装操作数据库的数据
        OcBOrderRelation relation = new OcBOrderRelation();
        Map<Long, Long> itemIdMap = new HashMap<>();

        //明细组装
        QueryWrapper<OcBOrderItem> wrapper = new QueryWrapper<>();
        wrapper.in("oc_b_order_id", ids);
        List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectList(wrapper);
        List<OcBOrderItemExt> oldOcBOrderItemExtList = ocBOrderItemExtService.queryByOrderIdList(ids);
        Long orderId = ModelUtil.getSequence("oc_b_order");
        List<OcBOrderItem> copyOrderItemList = this.getOrderItemList(ocBOrderItemList, orderId, true, user, itemIdMap);

        //订单主表组装
        OcBOrder ocBOrder = this.getOcBOrder(dbList, orderId, copyOrderItemList, user, is2CDetention);

        List<OcBOrderItemExt> ocBOrderItemExtList = this.getOcBOrderItemExtList(ocBOrderItemList, orderId, oldOcBOrderItemExtList, itemIdMap, ocBOrder.getTid());

        //付款信息组装
        QueryWrapper<OcBOrderPayment> paymentWrapper = new QueryWrapper<>();
        paymentWrapper.in("oc_b_order_id", ids);
        List<OcBOrderPayment> ocBOrderPaymentList = ocBOrderPaymentMapper.selectList(paymentWrapper);
        List<OcBOrderPayment> copyOcBOrderPaymentList = this.getOcBOrderPaymentList(ocBOrderPaymentList, orderId);

        //优惠信息组装
        QueryWrapper<OcBOrderPromotion> promotionWrapper = new QueryWrapper<>();
        promotionWrapper.in("oc_b_order_id", ids);
        List<OcBOrderPromotion> ocBOrderPromotionList = ocBOrderPromotionMapper.selectList(promotionWrapper);
        List<OcBOrderPromotion> copyOcBOrderPromotionList = this.getocBOrderPromotionList(ocBOrderPromotionList, orderId);
        //
        OcBOrderParam param = new OcBOrderParam();
        param.setOcBOrder(ocBOrder);
        param.setOrderItemList(copyOrderItemList);
        orderAmountUtil.recountOrderAmount(param);
        relation.setOrderInfo(ocBOrder);
        relation.setOrderItemList(copyOrderItemList);
        relation.setOrderPaymentList(copyOcBOrderPaymentList);
        relation.setOrderPromotionList(copyOcBOrderPromotionList);
        relation.setOcBOrderItemExtList(ocBOrderItemExtList);
        if (log.isDebugEnabled()) {
            log.debug("MergeOrderParams.构建参数.原:{}, 合:{}", JSON.toJSONString(dbList), JSON.toJSONString(relation));
        }
        return relation;
    }

    /**
     * 将同一买家昵称、发货仓库、平台、店铺、支付方式、收货人、收货人手机、收货人电话、
     * 收货人所在省、收货人所在市、收货人所在区、收货人详细地址等订单进行合并
     *
     * @param ocBOrderList
     * @return
     */
    public String checkParam(List<OcBOrder> ocBOrderList) {
        if (CollectionUtils.isEmpty(ocBOrderList) || ocBOrderList.size() < 2) {
            return "订单数小于两单,不允许合并";
        }
        //优先判断一致性，避免废循环
        List<OrderDistinctResult> orderDistinctResult = exchangexClassList(ocBOrderList, OrderDistinctResult.class);
        List<OrderDistinctResult> distinctResultList = orderDistinctResult.stream().distinct().collect(Collectors.toList());
        if (distinctResultList.size() > 1) {
            List<Long> ids = ocBOrderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
            return "单据编号" + ids + "买家昵称/发货仓库/平台/店铺/支付方式/订单类型/收货人/收货人手机/收货人电话/收货人所在省/收货人所在市/" +
                    "收货人所在区/收货人详细地址,不一致,不允许合并";
        }
        //校验合并后的订单商品总数量
        /*long qtyAll = ocBOrderList.stream().map(OcBOrder::getQtyAll).count();
        CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryWarehouseById(ocBOrderList.get(0).getCpCPhyWarehouseId());
        if (cpCPhyWarehouse == null) {
            return "发货仓库未找到，请确认后再进行合并";
        }*/
        /*log.info("cp查询实体仓库返回结果" + cpCPhyWarehouse.toString());
        if (cpCPhyWarehouse.getQtyPkgMax() == null || cpCPhyWarehouse.getLinePkgMax() == null) {
            return "该实体仓库未配置单包最大商品数或最大订单商品总行数，请配置后再进行合并";
        } else if (cpCPhyWarehouse.getQtyPkgMax().compareTo(new BigDecimal(qtyAll)) < 0) {
            return "合并后的订单商品总数量超过仓库(仓库编码：" + cpCPhyWarehouse.getEcode() + ")限制的" + cpCPhyWarehouse.getQtyPkgMax() + "件，不允许再进行合并";
        }
        if (ocBOrderItemMapper.selectOrderItemsByOrderIds(orderIds).size() > cpCPhyWarehouse.getLinePkgMax()) {
            return "合并后的订单商品总行数超过仓库(仓库编码：" + cpCPhyWarehouse.getEcode() + ")限制的" + cpCPhyWarehouse.getQtyPkgMax();
        }*/

        for (OcBOrder ocBOrder : ocBOrderList) {
            //平台一般不能改 去除平台的校验
            //校验订单状态
            if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
                return "订单编号:" + ocBOrder.getId() + "状态除待审核外,不允许合并";
            }
            //校验是否退款中
            if (ocBOrder.getIsInreturning() != 0) {
                return "订单编号:" + ocBOrder.getId() + "该订单已退款不允许合并";
            }
            //校验付款方式
            if (ocBOrder.getPayType() == OmsPayType.CASH_ON_DELIVERY.toInteger()) {
                return "订单编号:" + ocBOrder.getId() + "为货到付款,不允许合并";
            }
            //校验是否包含预售商品
            if (null != ocBOrder.getDouble11PresaleStatus() && ocBOrder.getDouble11PresaleStatus() == 1) {
                return "订单编号:" + ocBOrder.getId() + "包含预售商品,不允许合并";
            }
            //校验订单类型
            /*if (ocBOrder.getOrderType() == OrderTypeEnum.EXCHANGE.getVal() && ocBOrder.getOrderSource().contains("TAOBAO")) {
                return "订单编号:" + ocBOrder.getId() + "淘宝换货订单,不允许合并";
            }*/
            //校验发货仓库
            if (ocBOrder.getCpCPhyWarehouseId() == null) {
                return "订单编号:" + ocBOrder.getId() + "发货仓库为空,不允许合并";
            }
            //校验是否挂起
            if (ocBOrder.getIsInterecept().intValue() == 1) {
                return "订单编号:" + ocBOrder.getId() + "已被挂起,不允许合并";
            }
        }
        return null;
    }

    /**
     * list对象转换
     *
     * @param sourceModelList 入参对象
     * @param clazz           出参类型
     * @return
     */
    public static List exchangexClassList(List sourceModelList, Class clazz) {
        String jsonObject = JSON.toJSONString(sourceModelList);
        return JSONArray.parseArray(jsonObject, clazz);
    }


    /**
     * 组装锁单数据类型
     *
     * @param dbList
     * @return
     */
    public JSONArray lockList(List<OcBOrder> dbList) {
        JSONArray jsonArray = new JSONArray();
        for (OcBOrder ocBOrder : dbList) {
            Map<String, Long> map = new HashMap<>();
            map.put("ID", ocBOrder.getId());
            jsonArray.add(map);
        }
        return jsonArray;
    }


    /**
     * 分配物流,订单主子表,付款信息,优惠信息推Es,调用作废原单占用库存订单新增日志服务
     *
     * @param ocBOrderRelation
     * @param user
     */
    public void distributeLogistics(OcBOrderRelation ocBOrderRelation, User user, OcBOrder order) {
        //先分配物流再更新订单主表
        OcBOrder ocBOrderWL = ocBOrderRelation.getOrderInfo();
        try {
            if (order != null) {
                if (!omsBusinessTypeStService.isAutoOccupy(order)) {
                    //to b不寻源占单
                    OcBOrder ocBOrder = new OcBOrder();
                    ocBOrder.setId(ocBOrderWL.getId());
                    ocBOrder.setCpCLogisticsId(order.getCpCLogisticsId());
                    ocBOrder.setCpCLogisticsEcode(order.getCpCLogisticsEcode());
                    ocBOrder.setCpCLogisticsEname(order.getCpCLogisticsEname());
                    ocBOrderMapper.updateById(ocBOrder);
                    omsOrderLogService.addUserOrderLog(ocBOrderRelation.getOrderInfo().getId(),
                            ocBOrderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(),
                            "TOB订单合单后取原单物流公司 ",
                            null, null, user);
                    return;
                }
            }
            CpCLogistics cpCLogistics = omsOrderDistributeLogisticsService.distributeLogistics(ocBOrderRelation);
            if (cpCLogistics != null) {
                OcBOrder ocBOrder = new OcBOrder();
                ocBOrder.setId(ocBOrderWL.getId());
                ocBOrder.setCpCLogisticsId(cpCLogistics.getId());
                ocBOrder.setCpCLogisticsEcode(cpCLogistics.getEcode());
                ocBOrder.setCpCLogisticsEname(cpCLogistics.getEname());
                ocBOrderMapper.updateById(ocBOrder);
                // 插入日志
                omsOrderLogService.addUserOrderLog(ocBOrderRelation.getOrderInfo().getId(),
                        ocBOrderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(),
                        "订单合单后重新分物流,订单分配到物流公司.物流公司Id[" + cpCLogistics.getId() + "][" + cpCLogistics.getEname() + "]",
                        null, null, user);
            } else {
                ocBOrderWL.setCpCLogisticsId(0L);
                ocBOrderWL.setCpCLogisticsEcode("");
                ocBOrderWL.setCpCLogisticsEname("");
                ocBOrderMapper.updateById(ocBOrderWL);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("合并订单调用分配物流异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
        }

    }


    /**
     * 订单优惠信息复制
     *
     * @param ocBOrderPromotionList
     * @param orderId
     * @return
     */
    public List<OcBOrderPromotion> getocBOrderPromotionList(List<OcBOrderPromotion> ocBOrderPromotionList, Long orderId) {
        List<OcBOrderPromotion> orderPromotionList = new ArrayList<>();
        for (OcBOrderPromotion ocBOrderPromotion : ocBOrderPromotionList) {
            OcBOrderPromotion orderPromotionItem = new OcBOrderPromotion();
            BeanUtils.copyProperties(ocBOrderPromotion, orderPromotionItem);
            orderPromotionItem.setId(ModelUtil.getSequence("oc_b_order_promotion"));
            orderPromotionItem.setOcBOrderId(orderId);
            orderPromotionList.add(orderPromotionItem);
        }
        return orderPromotionList;
    }


    /**
     * 订单付款信息复制
     *
     * @param ocBOrderPaymentList
     * @param orderId
     * @return
     */
    public List<OcBOrderPayment> getOcBOrderPaymentList(List<OcBOrderPayment> ocBOrderPaymentList, Long orderId) {
        List<OcBOrderPayment> orderPaymentList = new ArrayList<>();
        for (OcBOrderPayment ocBOrderPayment : ocBOrderPaymentList) {
            OcBOrderPayment ocBOrderPaymentItem = new OcBOrderPayment();
            BeanUtils.copyProperties(ocBOrderPayment, ocBOrderPaymentItem);
            //重新生成Id
            ocBOrderPaymentItem.setId(ModelUtil.getSequence("oc_b_order_payment"));
            ocBOrderPaymentItem.setOcBOrderId(orderId);
            orderPaymentList.add(ocBOrderPaymentItem);
        }
        return orderPaymentList;
    }

    public List<OcBOrderItem> getOrderItemList(List<OcBOrderItem> ocBOrderItemList, Long orderId, boolean isHandle, User user, Map<Long, Long> itemIdMap) {
        //复制明细开始
        List<OcBOrderItem> copyOrderItemList = Lists.newArrayList();
        if (!isHandle) {
            //明细不处理宣传单的逻辑 目前默认处理宣传单
            for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                // 排除退款完成
                if (ocBOrderItem.getRefundStatus() == OcOrderRefundStatusEnum.SUCCESS.getVal()) {
                    continue;
                }
                OcBOrderItem orderItem = new OcBOrderItem();
                BeanUtils.copyProperties(ocBOrderItem, orderItem);
                //新加来源明细
                orderItem.setOldSourceItemId(orderItem.getId());
                // 重新生成Id
                orderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
                // 设置订单主表Id
                orderItem.setOcBOrderId(orderId);
                orderItem.setOwnerid((long) user.getId());
                orderItem.setOwnername(user.getName());
                orderItem.setOwnerename(user.getEname());
                orderItem.setCreationdate(new Date());
                orderItem.setModifieddate(new Date());
                orderItem.setModifierid((long) user.getId());
                orderItem.setModifiername(user.getName());
                orderItem.setModifierename(user.getEname());
                orderItem.setAdOrgId((long) user.getOrgId());
                orderItem.setAdClientId((long) user.getClientId());
                // 商品金额 = 原价 * 数量
                BigDecimal produceAmt = orderItem.getRealAmt().divide(orderItem.getQty()).setScale(4, BigDecimal.ROUND_HALF_UP);
                // 调整金额 = 成交金额 - 商品金额
                BigDecimal adjustAmt = orderItem.getPrice().subtract(produceAmt);
                orderItem.setAdjustAmt(adjustAmt);
                copyOrderItemList.add(orderItem);
                itemIdMap.put(ocBOrderItem.getId(), orderItem.getId());
            }
            return copyOrderItemList;
        }
        List<String> skus = ocBOrderItemList.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
        List<String> skuList = psRpcService.querySkuList(skus);//所有的条码集合  是赠品 并且是宣传单

        if (CollectionUtils.isNotEmpty(skuList)) {
            for (String ecode : skuList) {
                //如果商品是赠品又是宣传单 只保留一条相同的明细
                List<OcBOrderItem> list = ocBOrderItemList.stream()
                        .filter(x -> x.getPsCSkuEcode().equals(ecode)).collect(Collectors.toList());
                for (int i = 0; i < list.size(); i++) {
                    if (i == 0) {
                        continue;
                    } else {
                        ocBOrderItemList.remove(list.get(i));
                    }
                }
            }
        }

        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            // 排除退款完成
            if (ocBOrderItem.getRefundStatus() == OcOrderRefundStatusEnum.SUCCESS.getVal()) {
                continue;
            }

            //如果商品是赠品又是宣传单  将数量更新为1
            if (skuList.contains(ocBOrderItem.getPsCSkuEcode())) {
                ocBOrderItem.setQty(BigDecimal.ONE);
            }
            OcBOrderItem orderItem = new OcBOrderItem();
            BeanUtils.copyProperties(ocBOrderItem, orderItem);
            //重新生成Id
            orderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
            //设置订单主表Id
            orderItem.setOcBOrderId(orderId);
            orderItem.setOwnerid((long) user.getId());
            orderItem.setOwnername(user.getName());
            orderItem.setOwnerename(user.getEname());
            orderItem.setCreationdate(new Date());
            orderItem.setModifieddate(new Date());
            orderItem.setModifierid((long) user.getId());
            orderItem.setModifiername(user.getName());
            orderItem.setModifierename(user.getEname());
            orderItem.setAdOrgId((long) user.getOrgId());
            orderItem.setAdClientId((long) user.getClientId());
            copyOrderItemList.add(orderItem);
            itemIdMap.put(ocBOrderItem.getId(), orderItem.getId());
        }
        return copyOrderItemList;
    }

    public List<OcBOrderItemExt> getOcBOrderItemExtList(List<OcBOrderItem> ocBOrderItemList, Long orderId, List<OcBOrderItemExt> oldOcBOrderItemExtList, Map<Long, Long> itemIdMap, String tid) {
        if (CollectionUtils.isEmpty(ocBOrderItemList)) {
            return null;
        }
        if (CollectionUtils.isEmpty(oldOcBOrderItemExtList)) {
            return null;
        }
        List<OcBOrderItemExt> ocBOrderItemExtList = Lists.newArrayList();
        for (OcBOrderItemExt ocBOrderItemExt : oldOcBOrderItemExtList) {
            Long newItemId = itemIdMap.get(ocBOrderItemExt.getOrderItemId());
            OcBOrderItemExt newOcBOrderItemExt = new OcBOrderItemExt();
            BeanUtils.copyProperties(ocBOrderItemExt, newOcBOrderItemExt);
            Long id = sequenceUtil.buildOrderItemExtSequenceId();
            newOcBOrderItemExt.setId(id);
            newOcBOrderItemExt.setOrderItemId(newItemId);
            newOcBOrderItemExt.setOcBOrderId(orderId);
            newOcBOrderItemExt.setTid(tid);
            BaseModelUtil.initialBaseModelSystemField(newOcBOrderItemExt);
            ocBOrderItemExtList.add(newOcBOrderItemExt);
        }
        return ocBOrderItemExtList;
    }


    /**
     * 组装主表对象
     *
     * @param ocBOrderList
     * @param orderId
     * @param copyorderItemList
     * @param user
     * @param is2CDetention
     * @return
     */
    public OcBOrder getOcBOrder(List<OcBOrder> ocBOrderList, Long orderId, List<OcBOrderItem> copyorderItemList, User user, boolean is2CDetention) {
        OcBOrder ocBOrder = this.copyOrder(ocBOrderList.get(0));
        ocBOrder.setId(orderId);
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        /** 物流公司信息等待重新分配*/
        ocBOrder.setCpCLogisticsId(null);
        ocBOrder.setCpCLogisticsEcode(null);
        ocBOrder.setCpCLogisticsEname(null);
        ocBOrder.setExpresscode(null);
        /*当前只有2C「待寻源的卡单」或「待审核」状态的数据可合并*/
        if (is2CDetention) {
            ocBOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            ocBOrder.setDetentionReasonId(OrderDetentionEnum.COMMON_SKU_CARD.getKey());
            ocBOrder.setDetentionReason(OrderDetentionEnum.COMMON_SKU_CARD.getVal());
        } else {
            ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
        }

        ocBOrder.setIsMerge(1);
        ocBOrder.setIsSplit(0);
        ocBOrder.setOrigOrderId(null);
        ocBOrder.setOrigReturnOrderId(null);
        ocBOrder.setMergeSourceCode(null);
        ocBOrder.setMergeOrderId(null);
        ocBOrder.setSplitOrderId(null);
        ocBOrder.setWmsCancelStatus(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_NO.toInteger());
        ocBOrder.setReturnStatus(OcBorderListEnums.ReturnStatusEnum.NO_RETURN.getVal());
        ocBOrder.setTbStorecode(null);
        ocBOrder.setRefundConfirmStatus(null);
        ocBOrder.setDouble11PresaleStatus(0);
        ocBOrder.setIsHasTicket(null);
        ocBOrder.setBuyerAlipayNo(null);
        // 去标
        clearTag(ocBOrder);
        this.getCopyOrder(ocBOrder, ocBOrderList);
        ocBOrder.setCreationdate(new Date());
        ocBOrder.setModifieddate(new Date());
        ocBOrder.setOwnerid((long) user.getId());
        ocBOrder.setOwnername(user.getName());
        ocBOrder.setOwnerename(user.getEname());
        ocBOrder.setModifierid((long) user.getId());
        ocBOrder.setModifierename(user.getEname());
        ocBOrder.setModifiername(user.getName());
        ocBOrder.setAdOrgId((long) user.getOrgId());
        ocBOrder.setAdClientId((long) user.getClientId());
        MD5Util.encryptOrderInfo4Merge(ocBOrder);
        //i.在原合单逻辑基础上，针对JITX订单的合单增加下记逻辑：
        //在新产生的合单上更新“合包码”：随机取某一个订单的合包码赋值即可；
        //“物流单号”：取第一个订单明细的平台单号对应的物流单号；
        //“JITX合包发货平台单号”：取第一个订单明细的平台单号；
        if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
            ocBOrder.setMergedCode(ocBOrderList.get(0).getMergedCode());
            ocBOrder.setJitxMergedDeliverySn(ocBOrderList.get(0).getTid());
            ocBOrder.setCpCLogisticsId(ocBOrderList.get(0).getCpCLogisticsId());
            ocBOrder.setCpCLogisticsEcode(ocBOrderList.get(0).getCpCLogisticsEcode());
            ocBOrder.setCpCLogisticsEname(ocBOrderList.get(0).getCpCLogisticsEname());
            ocBOrder.setExpresscode(ocBOrderList.get(0).getExpresscode());
        }
        //oaid取第一发货单的
        ocBOrder.setOaid(ocBOrderList.get(0).getOaid());
        //加上sku
        String allSku = "";
        boolean flag = true;
        Date estimateConTimeMax = null;
        for (OcBOrderItem ocBOrderItem : copyorderItemList) {
            Long proType = Optional.ofNullable(ocBOrderItem.getProType()).orElse(0L);
            if (proType.intValue() == SkuType.GIFT_PRODUCT || proType.intValue() == SkuType.COMBINE_PRODUCT) {
                continue;
            }
            if (flag) {
                if (allSku.length() >= 100) {
                    allSku += "...";
                    flag = false;
                } else {
                    allSku = allSku + ocBOrderItem.getPsCSkuEcode() + "(" + ocBOrderItem.getQty().intValue() + "),";
                }
            }
            if (ocBOrderItem.getEstimateConTime() != null) {
                if (estimateConTimeMax == null) {
                    estimateConTimeMax = ocBOrderItem.getEstimateConTime();
                } else {
                    if (ocBOrderItem.getEstimateConTime().before(estimateConTimeMax)) {
                        estimateConTimeMax = ocBOrderItem.getEstimateConTime();
                    }
                }
            }
        }
        ocBOrder.setEstimateConTime(estimateConTimeMax);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(allSku)) {
            ocBOrder.setAllSku(allSku);
        }
        return ocBOrder;
    }


    /**
     * 主表需要需要拼接的列
     *
     * @param ocBOrder
     * @param ocBOrderList
     */
    public void getCopyOrder(OcBOrder ocBOrder, List<OcBOrder> ocBOrderList) {
        StringBuffer buyerMessageList = new StringBuffer();
        StringBuilder suffixInfoList = new StringBuilder();
        // 内部备注
        StringBuilder insideRemarkList = new StringBuilder();
        // 卖家备注
        StringBuilder sellerRemarkList = new StringBuilder();
        // 订单打标信息
        List<String> labelIdStr = new ArrayList<>();
        // 订单打标信息
        List<String> labelNameStr = new ArrayList<>();
        //调整金额
        BigDecimal adjustAmt = BigDecimal.ZERO;
        //配送费用
        BigDecimal shipAmt = BigDecimal.ZERO;
        //服务费
        BigDecimal serviceAmt = BigDecimal.ZERO;
        //代销运费
        BigDecimal consignShipAmt = BigDecimal.ZERO;
        //到付代收金额
        BigDecimal codAmt = BigDecimal.ZERO;
        //操作费
        BigDecimal operateAmt = BigDecimal.ZERO;
        //应收平台金额（京东）
        BigDecimal jdReciveAmt = BigDecimal.ZERO;
        //京东结算金额
        BigDecimal jdSellerAmt = BigDecimal.ZERO;
        //物流成本
        BigDecimal logisyticsCost = BigDecimal.ZERO;


        for (OcBOrder order : ocBOrderList) {
            String stCCustomLabelId = order.getStCCustomLabelId();
            String stCCustomLabelEname = order.getStCCustomLabelEname();
            if (StringUtils.isNotEmpty(stCCustomLabelId) && StringUtils.isNotEmpty(stCCustomLabelEname)) {
                labelIdStr.add(stCCustomLabelId);
                labelNameStr.add(stCCustomLabelEname);
            }
            // 标
            markTag(ocBOrder, order);
            suffixInfoList.append(order.getId()).append(",");
            //sourceList.append(order.getSourceCode() + ",");
            if (StringUtils.isNotEmpty(order.getBuyerMessage())) {
                buyerMessageList.append(order.getBuyerMessage()).append(",");
            }
            if (StringUtils.isNotEmpty(order.getInsideRemark())) {
                insideRemarkList.append(order.getInsideRemark()).append(",");
            }
            if (StringUtils.isNotEmpty(order.getSellerMemo())) {
                sellerRemarkList.append(order.getSellerMemo()).append(",");
            }
            adjustAmt = adjustAmt.add(order.getAdjustAmt() == null ? BigDecimal.ZERO : order.getAdjustAmt());
            shipAmt = shipAmt.add(order.getShipAmt() == null ? BigDecimal.ZERO : order.getShipAmt());
            serviceAmt = serviceAmt.add(order.getServiceAmt() == null ? BigDecimal.ZERO : order.getServiceAmt());
            consignShipAmt = consignShipAmt.add(order.getConsignShipAmt() == null ? BigDecimal.ZERO : order.getConsignShipAmt());
            codAmt = codAmt.add(order.getCodAmt() == null ? BigDecimal.ZERO : order.getCodAmt());
            // operateAmt = operateAmt.add(order.getOperateAmt() == null ? BigDecimal.ZERO : order.getOperateAmt());
            jdReciveAmt = jdReciveAmt.add(order.getJdReceiveAmt() == null ? BigDecimal.ZERO : order.getJdReceiveAmt());
            jdSellerAmt = jdSellerAmt.add(order.getJdSettleAmt() == null ? BigDecimal.ZERO : order.getJdSettleAmt());
            logisyticsCost = logisyticsCost.add(order.getLogisticsCost() == null ? BigDecimal.ZERO : order.getLogisticsCost());

            // 下单时间, 付款时间
            if (prevLtNext(order.getOrderDate(), ocBOrder.getOrderDate())) {
                ocBOrder.setOrderDate(order.getOrderDate());
            }
            if (prevLtNext(order.getPayTime(), ocBOrder.getPayTime())) {
                ocBOrder.setPayTime(order.getPayTime());
            }
        }
        if (CollectionUtils.isNotEmpty(labelIdStr) && CollectionUtils.isNotEmpty(labelNameStr)) {
            ocBOrder.setStCCustomLabelId(org.apache.commons.lang3.StringUtils.join(labelIdStr, ","));
            ocBOrder.setStCCustomLabelEname(org.apache.commons.lang3.StringUtils.join(labelNameStr, ","));
        }
        ocBOrder.setAdjustAmt(adjustAmt);
        ocBOrder.setShipAmt(shipAmt);
        ocBOrder.setServiceAmt(serviceAmt);
        ocBOrder.setConsignShipAmt(consignShipAmt);
        ocBOrder.setCodAmt(codAmt);
        ocBOrder.setJdReceiveAmt(jdReciveAmt);
        ocBOrder.setJdSettleAmt(jdSellerAmt);
        ocBOrder.setLogisticsCost(logisyticsCost);
        String suffixInfo = suffixInfoList.substring(0, suffixInfoList.length() - 1);
        // 订单补充信息
        ocBOrder.setSuffixInfo("(" + suffixInfo + ") -MG");
        // 来源单号
        ocBOrder.setSourceCode(ocBOrder.getTid());
        ocBOrder.setTid(ocBOrder.getTid());
        // 构建合单单号
        ocBOrder.setMergeSourceCode(buildSourceCode(ocBOrderList));
        if (StringUtils.isNotEmpty(buyerMessageList.toString()) && buyerMessageList.length() > 1) {
            if (buyerMessageList.length() > 500) {
                ocBOrder.setBuyerMessage(buyerMessageList.substring(0, 500));
            } else {
                ocBOrder.setBuyerMessage(buyerMessageList.substring(0, buyerMessageList.length() - 1));
            }
        }
        ocBOrder.setSysremark(null);
        if (StringUtils.isNotEmpty(insideRemarkList.toString()) && insideRemarkList.length() > 1) {
            ocBOrder.setInsideRemark(insideRemarkList.substring(0, insideRemarkList.length() - 1));
        }
        if (StringUtils.isNotEmpty(sellerRemarkList.toString()) && sellerRemarkList.length() > 1) {
            String substring = sellerRemarkList.substring(0, sellerRemarkList.length() - 1);
            if (substring.length() > MAX_NUM) {
                substring = substring.substring(0, MAX_NUM);
            }
            ocBOrder.setSellerMemo(substring);
        }
    }

    /**
     * @param destDate   目标比对时间
     * @param originDate 当前时间
     * @return 目标小于原时间: true
     */
    private boolean prevLtNext(Date destDate, Date originDate) {
        if (destDate == null) {
            return false;
        }
        if (originDate == null) {
            return true;
        }
        return destDate.compareTo(originDate) < 0;
    }

    /**
     * 打标. 继承
     *
     * @param dest 合并后的订单
     * @param orig 原单
     */
    private void markTag(OcBOrder dest, OcBOrder orig) {

        // 1. 赠
        if (OcBOrderConst.IS_STATUS_IY.equals(orig.getIsHasgift())) {
            dest.setIsHasgift(OcBOrderConst.IS_STATUS_IY);
        }
        // 2. 催
        if (OcBOrderConst.IS_STATUS_IY.equals(orig.getIsOutUrgency())) {
            dest.setIsOutUrgency(OcBOrderConst.IS_STATUS_IY);
        }
        // 3. 票
        if (OcBOrderConst.IS_STATUS_IY.equals(orig.getIsInvoice())) {
            dest.setIsInvoice(OcBOrderConst.IS_STATUS_IY);
            dest.setInvoiceHeader(orig.getInvoiceHeader());
            dest.setInsideRemark(orig.getInsideRemark());
            dest.setIsGeninvoiceNotice(orig.getIsGeninvoiceNotice());
        }
        // 4. 促销 合单时打促销标(只要有一个订单时促销，合单后就全部标记为促销)
        if (OcBOrderConst.IS_STATUS_IY.equals(orig.getIsPromOrder())) {
            dest.setIsPromOrder(OcBOrderConst.IS_STATUS_IY);
        }
        // 5. 拆 @20200825 合单时打拆标(只要有一个订单是拆单，合单后就全部标记为拆单)
        if (OcBOrderConst.IS_STATUS_IY.equals(orig.getIsSplit())) {
            dest.setIsSplit(OcBOrderConst.IS_STATUS_IY);
            dest.setSplitReason(OmsMergeSplitOrderType.DEFAULT_00.getVal());
        }
        // 6. 预
        if (orig.getDouble11PresaleStatus() != null
                && !(OcBOrderConst.ORDER_STATUS_ALL.equals(orig.getDouble11PresaleStatus()))) {
            dest.setDouble11PresaleStatus(OcBOrderConst.IS_STATUS_IY);
        }
        // 7. 组
        if (OcBOrderConst.IS_STATUS_IY.equals(orig.getIsCombination())) {
            dest.setIsCombination(OcBOrderConst.IS_STATUS_IY);
        }
        // 8. 换
        if (OrderTypeEnum.EXCHANGE.getVal().equals(orig.getOrderType())) {
            dest.setOrderType(OrderTypeEnum.EXCHANGE.getVal());
        }
        // 9. 急
        if (OcBOrderConst.IS_DELIVERY_URGENT.equals(orig.getIsDeliveryUrgent())) {
            dest.setIsDeliveryUrgent(OcBOrderConst.IS_DELIVERY_URGENT);
        }
        // 10. o2o
        if (OcBOrderConst.IS_STATUS_IY.equals(orig.getIsO2oOrder())) {
            dest.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
        }
        // 11. 播
        if (LiveFlagEnum.Y.getValue().equals(orig.getLiveFlag())) {
            dest.setLiveFlag(LiveFlagEnum.Y.getValue());
        }
        // 12. 轻
        /*if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(orig.getHasLightSupplyProd())) {
            dest.setHasLightSupplyProd(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
        }*/


    }

    /**
     * 清除标签
     *
     * @param order 合并后的订单
     */
    private void clearTag(OcBOrder order) {
        // 手
        order.setOrderSource("");
        // 缺
//        order.setIsLackstock(OcBOrderConst.IS_STATUS_IN);
        // order.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
        // 实缺
        // order.setIsRealLackstock(OcBOrderConst.IS_STATUS_IN);
        // 到
        // order.setPayType();
        // 虚
        order.setIsInvented(0);
        order.setPriceLabel(OcBOrderConst.IS_ACTIVE_NO);
        // 锁
        order.setLockStatus(OcOrderLockStatusEnum.WAIT_LOCK.getKey());
        // 标
        order.setCpCLabelId(null);
        // 复
        order.setIsCopyOrder(OcBOrderConst.IS_STATUS_IN);
        // 促
        //    order.setIsPromOrder(OcBOrderConst.IS_STATUS_IN);
        // 改
        order.setIsModifiedOrder(OcBOrderConst.IS_STATUS_IN);
        // 额
        order.setIsExtra(OcBOrderConst.IS_STATUS_IN);
        // 截
//        order.setIsWosCut(OcBOrderConst.IS_STATUS_IN);
    }

    /**
     * 构建MergerSourceCode
     *
     * @param ocBOrderList
     * @return
     */
    private static String buildSourceCode(List<OcBOrder> ocBOrderList) {
        Set<String> sourceCodes = Sets.newHashSet();
        for (OcBOrder order : ocBOrderList) {
            if (order.getIsMerge() != null && order.getIsMerge() == 1 && order.getMergeSourceCode() != null) {
                sourceCodes.add(order.getMergeSourceCode());
            } else if (order.getTid() != null) {
                sourceCodes.add(order.getTid());
            }
        }
        String join = StringUtils.join(sourceCodes, ",");
        String[] split = join.split(",");
        return StringUtils.join(new HashSet<>(Arrays.asList(split)), ",");
    }


    /**
     * 订单主表字段需要根据子表计算的列
     *
     * @param ocBOrder
     * @return
     */
    public void getOrderAmtAndWeight(OcBOrder ocBOrder, List<OcBOrderItem> copyorderItemList) {

        BigDecimal weight = BigDecimal.ZERO;
        //重新计算商品数量
        BigDecimal normalTotal = BigDecimal.ZERO;
        //重新计算商品总额
        BigDecimal priceListTotal = BigDecimal.ZERO;
        //指定商品优惠金额
        BigDecimal amtDiscountTotal = BigDecimal.ZERO;
        //平摊金额之和
        BigDecimal orderSplitAmtTotal = BigDecimal.ZERO;
        //代销结算金额
        BigDecimal consignmentTotal = BigDecimal.ZERO;
        //应收金额求和
        BigDecimal receivableTotal = BigDecimal.ZERO;
        //调整金额之和
        BigDecimal adjustTotal = BigDecimal.ZERO;
        for (OcBOrderItem ocBOrderItem : copyorderItemList) {
            //福袋商品/组合 不参与计算
            if (Objects.nonNull(ocBOrderItem.getProType())
                    && (ocBOrderItem.getProType() == SkuType.GIFT_PRODUCT
                    || ocBOrderItem.getProType() == SkuType.COMBINE_PRODUCT)) {
                continue;
            }
            if (Objects.nonNull(ocBOrderItem.getRefundStatus())
                    && ocBOrderItem.getRefundStatus() == OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal()) {
                throw new NDSException("明细含有退款中不允许合并订单");
            }
            //重量
            weight = weight.add((ocBOrderItem.getStandardWeight() == null ? BigDecimal.ZERO : ocBOrderItem.getStandardWeight()).multiply(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty()));
            //商品总额
            BigDecimal productAmt = ocBOrderItem.getPrice().multiply(ocBOrderItem.getQty());
            // BigDecimal sumPrice = (ocBOrderItem.getPrice() == null ? BigDecimal.ZERO : ocBOrderItem.getPrice())
            //        .multiply(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty());
            priceListTotal = priceListTotal.add(productAmt);
            //商品数量
            normalTotal = normalTotal.add(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty());
            //优惠金额计算
            amtDiscountTotal = amtDiscountTotal.add(ocBOrderItem.getAmtDiscount() == null ? BigDecimal.ZERO : ocBOrderItem.getAmtDiscount());
            //平摊金额求和
            orderSplitAmtTotal = orderSplitAmtTotal.add(ocBOrderItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getOrderSplitAmt());
            //代销结算金额求和
            consignmentTotal = consignmentTotal.add((ocBOrderItem.getDistributionPrice() == null ? BigDecimal.ZERO : ocBOrderItem.getDistributionPrice()).multiply(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty()));
            //应收金额求和
            receivableTotal = receivableTotal.add((ocBOrderItem.getPriceList() == null ? BigDecimal.ZERO : ocBOrderItem.getPriceList()).multiply(ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty()));
            //调整金额之和
            adjustTotal = adjustTotal.add(ocBOrderItem.getAdjustAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getAdjustAmt());
        }
        //订单总额计算
        BigDecimal orderAmtTotal = priceListTotal.add(ocBOrder.getShipAmt() == null ? BigDecimal.ZERO : ocBOrder.getShipAmt()).add(adjustTotal).add(ocBOrder.getServiceAmt() == null
                ? BigDecimal.ZERO : ocBOrder.getServiceAmt()).subtract(amtDiscountTotal).subtract(orderSplitAmtTotal);
        //更新正常订单表数据
        //	【商品数量】：取明细中所有数量的合计；
        ocBOrder.setQtyAll(normalTotal);
        ocBOrder.setSkuKindQty(new BigDecimal(copyorderItemList.size()));
        //	【商品总额】：取明细中所有吊牌金额的合计；
        ocBOrder.setProductAmt(priceListTotal);
        //	【商品优惠金额】：取明细中指定商品优惠金额
        ocBOrder.setProductDiscountAmt(amtDiscountTotal);
        //	【订单优惠金额】：整单优惠金额
        ocBOrder.setOrderDiscountAmt(orderSplitAmtTotal);
        //	【订单总额】：商品总额+物流费用+调整金额+服务费-商品优惠金额-订单优惠金额
        ocBOrder.setConsignAmt(consignmentTotal);
        //	【代销结算金额】：代销结算价*数量
        ocBOrder.setAmtReceive(receivableTotal);
        //	【应收金额】：商品标准价*数量
        ocBOrder.setOrderAmt(orderAmtTotal);
        //	【已支付金额】（已收金额）：等于计算后的订单总额
        ocBOrder.setReceivedAmt(orderAmtTotal);
    }


    /**
     * 订单copy
     *
     * @param ocBOrder
     * @return
     */
    public OcBOrder copyOrder(OcBOrder ocBOrder) {
        OcBOrder copyOrder = new OcBOrder();
        BeanUtils.copyProperties(ocBOrder, copyOrder);
        return copyOrder;
    }


    /**
     * 合并订单ES查询where条件
     * 1.订单状态待审核
     * 2.退款为否
     * 3.订单拦截否
     * 4.付款方式不等于货到付款
     * 5.平台不等于(7,19) 7-当当  19 唯品会jitX
     * 6.非云仓订单  先不管
     * 7.订单类型不是换货订单
     * 8.不是预售订单(1.不包含预售商品 )
     *
     * @return
     */
    public JSONObject getGroupWhereKeys() {
        JSONObject whereKeys = new JSONObject();//查询条件

        JSONArray statusArray = new JSONArray();
        //订单状态待审核
        whereKeys.put("ORDER_STATUS", OmsOrderStatus.UNCONFIRMED.toInteger());
        whereKeys.put("IS_INRETURNING", "0");//是否退款为否
        whereKeys.put("IS_INTERECEPT", "0");//是否拦截为否
        whereKeys.put("PAY_TYPE", "!=" + OmsPayType.CASH_ON_DELIVERY.toInteger());//付款方式不等于货到付款
        // jsonArray.add("!=" + PlatFormEnum.WPjintX.getVal());//新增平台枚举唯品会jintX
        whereKeys.put("IS_SAME_CITY_PURCHASE", "!=" + "1");//
        JSONArray typeArray = new JSONArray();
        typeArray.add(OrderTypeEnum.NORMAL.getVal());
        typeArray.add(OrderTypeEnum.EXCHANGE.getVal());
        whereKeys.put("ORDER_TYPE", typeArray);
        // whereKeys.put("IS_HASPRESALESKU", "0");
        return whereKeys;
    }

    /**
     * 分组后需要拼接的查询参数
     *
     * @return
     */
    public JSONObject getGroupQueryWhereKey() {
        JSONObject whereKeys = new JSONObject();//查询条件
        JSONArray statusArray = new JSONArray();
        //订单状态待审核
        statusArray.add(OmsOrderStatus.UNCONFIRMED.toInteger());
        //订单状态已审核
        statusArray.add(OmsOrderStatus.CHECKED.toInteger());
        whereKeys.put("ORDER_STATUS", statusArray);
        whereKeys.put("IS_INRETURNING", "0");//是否退款为否
        whereKeys.put("IS_INTERECEPT", "0");//是否拦截为否
        // whereKeys.put("IS_HASPRESALESKU", "0"); //是否包含预售商品为否
        return whereKeys;
    }

    /**
     * 分组条件:
     * 1.订单的买家昵称 USER_NICK
     * 2.发货仓库 CP_C_PHY_WAREHOUSE_ID
     * 3.平台 PLATFORM
     * 4.订单类型
     * 5.店铺 CP_C_SHOP_ID
     * 6.付款方式 PAY_TYPE
     * 7.收货人 RECEIVER_NAME
     * 8.收货人手机 RECEIVER_MOBILE
     * 9.电话 RECEIVER_PHONE
     * 10.省 CP_C_REGION_PROVINCE_ID
     * 11.市 CP_C_REGION_CITY_ID
     * 12.区 CP_C_REGION_AREA_ID
     * 13.地址 RECEIVER_ADDRESS
     *
     * @return
     */
    public String[] getGroups() {
        String[] groups =
                new String[]{"USER_NICK", "CP_C_PHY_WAREHOUSE_ID", "PLATFORM",
                        "CP_C_SHOP_ID", "PAY_TYPE", "RECEIVER_NAME",
                        "RECEIVER_MOBILE", "RECEIVER_PHONE",
                        "CP_C_REGION_PROVINCE_ID", "CP_C_REGION_CITY_ID",
                        "CP_C_REGION_AREA_ID", "RECEIVER_ADDRESS"};
        return groups;
    }


    /**
     * 锁单
     *
     * @param jsonArray
     * @return
     */
    public Map<String, List> getLock(JSONArray jsonArray) {
        List<RedisReentrantLock> lockList = new ArrayList<>();
        List<Integer> ids = Lists.newArrayList();
        List<Integer> failIds = Lists.newArrayList();
        Map<String, List> map = new HashMap<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            Map<String, Long> idMap = (Map<String, Long>) jsonArray.get(i);
            try {
                Long a = idMap.get("ID");
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(a);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    lockList.add(redisLock);
                    ids.add(idMap.get("ID").intValue());
                } else {
                    failIds.add(idMap.get("ID").intValue());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("订单锁定异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                failIds.add(idMap.get("ID").intValue());
            }
        }
        map.put("lockList", lockList);
        map.put("ids", ids);
        map.put("failLockIds", failIds);
        return map;
    }

    /**
     * 日期校验
     *
     * @return
     */
    public String checkDate(String begindate, String endDate) {
        //type = 1 开始日期和es的下单日期比较
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        if (StringUtils.isEmpty(begindate) || StringUtils.isEmpty(endDate)) {
            return "";
        }
        try {
            String result = sdf.parse(begindate).getTime() + "~" + sdf.parse(endDate).getTime();
            return result;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";

    }

    /**
     * 过滤条件
     *
     * @param mergeOrderModel
     * @return
     */
    public JSONObject checkFilter(MergeOrderModel mergeOrderModel) {
        JSONObject filterJsonObject = new JSONObject();
        String createDate = this.checkDate(mergeOrderModel.getBeginOrderDate(), mergeOrderModel.getEndOrderDate());
        if (StringUtils.isNotEmpty(createDate)) {
            filterJsonObject.put("ORDER_DATE", createDate);
            return filterJsonObject;
        }
        return null;
    }

    /**
     * where 条件
     *
     * @param mergeOrderModel
     * @return
     */
    public JSONObject getWhereKey(MergeOrderModel mergeOrderModel) {
        JSONObject whereKeys = this.getGroupWhereKeys();//查询条件
        if (null != mergeOrderModel.getShopId()) {
            whereKeys.put("CP_C_SHOP_ID", mergeOrderModel.getShopId());
        }
        if (null != mergeOrderModel.getShopArray() && mergeOrderModel.getShopArray().size() > 0) {
            whereKeys.put("CP_C_SHOP_ID", mergeOrderModel.getShopArray());
        }
        if (null != mergeOrderModel.getWarehouseId()) {
            whereKeys.put("CP_C_PHY_WAREHOUSE_ID", mergeOrderModel.getWarehouseId());
        }
        if (StringUtils.isNotEmpty(mergeOrderModel.getUserNickName())) {
            whereKeys.put("USER_NICK", mergeOrderModel.getUserNickName());
        }
        return whereKeys;
    }


    /**
     * 循环分组取出所有的id 根据id到数据库查询到分组的数据
     *
     * @param groupList
     * @return
     */
    public List<MergeOrderModel> getMergeOrderList(List<MergeEsHavingcountResult> groupList) {
        if (CollectionUtils.isEmpty(groupList)) {
            return null;
        }
        List<Integer> ids = ES4Order.findJSONObjectByGroupList(groupList);
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        //取前900多条数据
        if (ids.size() > MAX_NUM) {
            ids = ids.subList(0, MAX_NUM);
        }

        List<MergeOrderModel> mergeOrderModelList = ocBOrderMapper.selectMergeOrderByIds(ids);

        //找到换货未审核的订单
        Map<Integer, Map<Integer, List<MergeOrderModel>>> groupBy = mergeOrderModelList.stream().collect(Collectors.groupingBy(
                MergeOrderModel::getOrderType, Collectors.groupingBy(MergeOrderModel::getOrderStatus)));

        List<MergeOrderModel> list = Lists.newArrayList();
        for (Map.Entry<Integer, Map<Integer, List<MergeOrderModel>>> typeMapEntry : groupBy.entrySet()) {
            Integer typeKey = typeMapEntry.getKey();
            for (Map.Entry<Integer, List<MergeOrderModel>> statusListEntry : typeMapEntry.getValue().entrySet()) {
                Integer key = statusListEntry.getKey();
                List<MergeOrderModel> value = statusListEntry.getValue();
                //拿到换货未确认的
//                if (typeKey.intValue() == OrderTypeEnum.EXCHANGE.getVal() && key.intValue() == OmsOrderStatus.UNCONFIRMED.toInteger()) {
//                    for (MergeOrderModel mergeOrderModel : value) {
//                        //找到退换货原单
//                        OcBOrder order = new OcBOrder();
//                        BeanUtils.copyProperties(mergeOrderModel, order);
//                        if (omsOrderManualAuditService.checkOrderIsChangeProd(order)) {
//                            list.add(mergeOrderModel);
//                        }
//                    }
//                } else {
//                    list.addAll(value);
//                }
                list.addAll(value);
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            for (MergeOrderModel mergeOrderModel : mergeOrderModelList) {
                exChangeOutPutField(mergeOrderModel);
            }
        }
        return list;
    }


    /**
     * 订单值:  转换可视化
     *
     * @param mergeOrderModel 订单结果
     */
    private void exChangeOutPutField(MergeOrderModel mergeOrderModel) {
        if (mergeOrderModel == null) {
            return;
        }
        String orderStatusName = OcOrderCheckBoxEnum.enumToStringByValue(mergeOrderModel.getOrderStatus());
        mergeOrderModel.setOrderStatusName(orderStatusName);
        String orderTypeName = OrderTypeEnum.getTextByVal(mergeOrderModel.getOrderType());
        mergeOrderModel.setOrderTypeName(orderTypeName);
        String platFormName = PlatFormEnum.getName(mergeOrderModel.getPlatform());
        mergeOrderModel.setPlatFormName(platFormName);
        String payTypeName = OcBorderListEnums.PayTypeEnum.getTextByVal(mergeOrderModel.getPayType());
        mergeOrderModel.setPayTypeName(payTypeName);
        String occupyStatusName = OcBorderListEnums.OccupyStatusEnum.getTextByVal(mergeOrderModel.getOccupyStatus());
        mergeOrderModel.setOccupyStatusName(occupyStatusName);
        String wmsCancelStatusName = OcBorderListEnums.WmsCanceStatusEnum.getTextByVal(mergeOrderModel.getWmsCancelStatus());
        mergeOrderModel.setWmsCancelStatusName(wmsCancelStatusName);
        String isGeninvoiceNoticeNm = OcBorderListEnums.IsGeninvoiceNoticeEnum.getTextByVal(mergeOrderModel.getIsGeninvoiceNotice());
        mergeOrderModel.setIsGeninvoiceNoticeName(isGeninvoiceNoticeNm);
        String returnName = OcBorderListEnums.ReturnStatusEnum.getTextByVal(mergeOrderModel.getReturnStatus());
        mergeOrderModel.setReturnStatusName(returnName);
        /*String isToDrpName = OcBorderListEnums.IsToDrpOrderEnum.getTextByVal(mergeOrderModel.getIsTodrp());
        mergeOrderModel.setIsToDrpName(isToDrpName);*/

    }

    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    public boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            if (isSuccess) {
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                //订单志
                try {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                            ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_MERGE.getKey(),
                            "合并订单反审核id为" + ocBOrder.getId(), "", "", user
                    );
                } catch (Exception e) {
                    log.error(LogUtil.format("合并订单订单新增日志异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                }
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(LogUtil.format("调用反审核失败,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            return false;
        }
    }

    public ValueHolder mergeOrderMenuOne(List<Long> ids, User user) {
        if (null == user) {
            user = SystemUserResource.getRootUser();
        }
        ValueHolder result = new ValueHolder();
        HashMap map = new HashMap();
        List<OcBOrder> dbList = ocBOrderMapper.selectBatchIds(ids);
        if (dbList.size() != ids.size()) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "单据已被更新,请刷新后重新合并");
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }
        List<OcBOrder> orders = dbList.stream().filter(p -> StringUtils.isNotEmpty(p.getGwSourceCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orders) && orders.size() != dbList.size()) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "原销售店铺不一致!不允许合并");
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }

        //toc残次订单不允许合并
        List<OcBOrder> saleProductAttrrOrders = dbList.stream().filter(p -> StringUtils.isNotBlank(p.getSaleProductAttr())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(saleProductAttrrOrders)) {
            for (OcBOrder ocBOrder : saleProductAttrrOrders) {
                if (OmsOrderUtil.isToCCcOrder(ocBOrder)) {
                    map.put("code", ResultCode.FAIL);
                    map.put("message", "toc残次订单不允许合并");
                    map.put("data", new HashMap<>());
                    result.setData(map);
                    return result;
                }
            }
        }
        // 查询所有订单明细是否存在贴纸赠品，存在就报【该订单有贴纸策略商品，不支持合单】
        Integer count = ocBOrderItemMapper.selectCount(new LambdaQueryWrapper<OcBOrderItem>()
                .in(OcBOrderItem::getOcBOrderId, ids)
                .eq(OcBOrderItem::getStickerGift, YesNoEnum.Y.getVal()));
        if (count > 0) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "该订单有贴纸策略商品，不支持合单");
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }

        ValueHolderV14 vh = ocMergeOrderService.manualMergeHandle(dbList, ids, user);
        result.put("code", vh.getCode());
        result.put("message", vh.getMessage());
        return result;
    }
}
