package com.jackrain.nea.oc.oms.services.returnorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.constant.OcOmsFrontCommonConstant;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.R3ParamUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * description: 零售退货单-批量从wms撤回/
 *
 * <AUTHOR>
 * create: 2022-01-07
 */
@Slf4j
@Component
public class OcBReturnOrderDetailActionR3Service {

    @Autowired
    private OcBReturnFromWMSWithDrawService withDrawService;

    /**
     * 零售退货单--批量从WMS撤回/
     *
     * @param querySession 框架页面参数
     * @return 标准出参
     */
    public ValueHolder batchAction(QuerySession querySession, int operate) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(querySession, SgR3BaseRequest.class);
        log.info(LogUtil.format("零售退货单操作，入参：{}"), JSON.toJSONString(request));
        String tip;
        switch (operate) {
            case OcOmsReturnOrderConstant.OC_B_RETURN_ORDER_RETAIL_FROM_WMS_WITHDRAW:
                tip = OcOmsReturnOrderConstant.OC_B_RETURN_ORDER_RETAIL_FROM_WMS_WITHDRAW_DESC;
                break;
            default:
                tip = OcOmsFrontCommonConstant.OPERATE_UNDEFIND_TIP;
                break;
        }
        Long objId = request.getObjId();

        List<Long> ids = request.getIds();
        if (CollectionUtils.isEmpty(ids) && ObjectUtils.isEmpty(objId)) {
            return R3ParamUtils.convertV14WithResult(new ValueHolderV14<>(ResultCode.FAIL, "请选择需要" + tip + "的数据"));
        }
        User user = request.getLoginUser();

        //详情页操作
        if (!ObjectUtils.isEmpty(objId)) {
            ValueHolderV14<OcBReturnOrder> valueHolderV14 = doAction(objId, operate, user);
            return R3ParamUtils.convertV14WithResult(new ValueHolderV14<>(valueHolderV14.getCode(), valueHolderV14.getMessage()));
        }

        //批量操作
        JSONArray errJa = new JSONArray();
        for (Long id : ids) {
            JSONObject errjo = new JSONObject();
            try {
                ValueHolderV14<OcBReturnOrder> valueHolderV14 = doAction(id, operate, user);

                if (!valueHolderV14.isOK()) {
                    errjo.put(OcOmsFrontCommonConstant.OBJID, id);
                    errjo.put("message", valueHolderV14.getMessage());
                    errJa.add(errjo);
                }
            } catch (Exception e) {
                log.error(LogUtil.format("零售退货单-{},异常error:{}, exception_has_occured=", id, tip),
                        Throwables.getStackTraceAsString(e));
                errjo.put(OcOmsFrontCommonConstant.OBJID, id);
                errjo.put("message", e.getMessage());
                errJa.add(errjo);
            }
        }

        ValueHolder valueHolder;

        if (errJa.isEmpty()) {
            valueHolder = R3ParamUtils.convertV14WithResult(new ValueHolderV14<>(ResultCode.SUCCESS,
                    tip + "成功：" + ids.size() + "条"));
        } else {
            valueHolder = R3ParamUtils.convertV14WithResult(new ValueHolderV14<>(ResultCode.FAIL,
                    tip + "成功：" + (ids.size() - errJa.size()) + "条，" + "失败" + errJa.size() + "条"));
            valueHolder.put("data", errJa);
        }

        return valueHolder;
    }

    /**
     * 零售退货单列表按钮操作
     *
     * @param id      单据id
     * @param operate 操作类型
     * @param user    用户
     * @return 操作结果
     */
    private ValueHolderV14<OcBReturnOrder> doAction(Long id, int operate, User user) {
        ValueHolderV14<OcBReturnOrder> valueHolderV14;
        switch (operate) {
            case OcOmsReturnOrderConstant.OC_B_RETURN_ORDER_RETAIL_FROM_WMS_WITHDRAW:
                valueHolderV14 = withDrawService.withDraw(id, user, null);
                break;
            default:
                valueHolderV14 = new ValueHolderV14<>(ResultCode.FAIL, OcOmsFrontCommonConstant.OPERATE_UNDEFIND_TIP);
                break;
        }
        return valueHolderV14;
    }
}
