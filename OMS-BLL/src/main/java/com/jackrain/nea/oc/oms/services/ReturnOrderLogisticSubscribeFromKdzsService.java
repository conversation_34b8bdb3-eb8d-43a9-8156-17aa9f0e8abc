package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.request.kdzs.KdzsCallBackRequest;
import com.jackrain.nea.oc.oms.model.request.kdzs.LogisticsFullTrace;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: 退单物流轨迹订阅
 * @DateTime 2022/4/1 18:13
 */
@Component
@Slf4j
public class ReturnOrderLogisticSubscribeFromKdzsService {

    @Autowired
    private ReturnOrderLogService returnOrderLogService;

    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;


    public static final int SPLIT_SIZE = 200;

    @Value("${return.order.logistic.subscribe.kdzs.appKey:B98B6445A33F4B50B8EB6B3761EE474C}")
    private String appKey;

    @Value("${return.order.logistic.subscribe.kdzs.appSecret:9A78378F353A45678F0B7628C9D236DB}")
    private String appSecret;

    @Value("${return.order.logistic.subscribe.kdzs.callBackUrl:http://*************:3000/}")
    private String callBackUrl;
    /**
     * 物流轨迹订阅按钮开关
     */
    @Value("${return.order.logistic.subscribe.kdzs.buttonSwitch:true}")
    private boolean buttonSwitchOpen;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private KdzsCallBackService kdzsCallBackService;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    private static final String LOG_TYPE = "退单订阅物流轨迹";

    private static final String SUCCESS_MSG = "快递助手物流轨迹订阅成功";

    private static final String ERR_MSG = "快递助手物流轨迹订阅失败:%s";

    public static final String CALL_BACK_PATH = "/api/ip/kdzs/v1/pushLogisticsTrace";

    /**
     * @description: 快递助手物流轨迹订阅
     * <AUTHOR>
     * @DateTime 2022/4/1 18:18
     */
    @Transactional(rollbackFor = Exception.class)
    public void doSubscribe(List<OcBReturnOrder> returnOrderList, User user) {
        log.info("{}.doSubscribe execute start,物流轨迹订阅开始 return order size = {}", this.getClass().getSimpleName(), returnOrderList.size());
        List<Long> successIdList = new ArrayList<>(returnOrderList.size());

        List<OcBReturnOrderLog> returnOrderLogList = new ArrayList<>(returnOrderList.size());

        String url = callBackUrl + CALL_BACK_PATH;

        for (OcBReturnOrder returnOrder : returnOrderList) {
            try {
//                KdzsClient kdzsClient = new KdzsClient(appKey, appSecret);
//                KdzsLogisticsTraceSubscribeRequest request = new KdzsLogisticsTraceSubscribeRequest();
//                //物流单号
//                request.setMailNo(returnOrder.getLogisticsCode());
//                //优先通过智能识别接口获取物流公司编码
//                String cpCode = this.getMailDiscern(returnOrder);
//                if (StringUtils.isNotEmpty(cpCode)) {
//                    request.setCpCode(cpCode);
//                } else {
//                    //快递公司编码
//                    request.setCpCode(returnOrder.getCpCLogisticsEcode());
//                }
//                //手机号后四位
//                String subPhone = this.getSubPhone(returnOrder);
//                request.setPhone(subPhone);
//                //TODO 回调地址
//                request.setCallBackUrl(url);

//                if (log.isDebugEnabled()) {
//                    log.debug("{},快递助手物流轨迹订阅参数：{}", this.getClass().getSimpleName(), JSON.toJSONString(request));
//                }

//                KdzsLogisticsTraceSubscribeResponse response = kdzsClient.execute(request);
//                if (log.isDebugEnabled()) {
//                    log.debug(" 退单物流轨迹订阅,订阅结果：{}", JSON.toJSONString(response));
//                }
//                if (response.isSuccess()) {
//                    successIdList.add(returnOrder.getId());
//                    OcBReturnOrderLog orderLog = returnOrderLogService.buildReturnOrderLog(returnOrder.getId(), LOG_TYPE, SUCCESS_MSG, user);
//                    if (orderLog != null) {
//                        returnOrderLogList.add(orderLog);
//                    }
//                } else {
//                    OcBReturnOrder update = new OcBReturnOrder();
//                    update.setId(returnOrder.getId());
//                    Integer logisticsTraceFailNum = returnOrder.getLogisticsTraceFailNum() == null ? 0 : returnOrder.getLogisticsTraceFailNum();
//                    update.setLogisticsTraceFailNum(logisticsTraceFailNum + 1);
//                    returnOrderMapper.updateById(update);
//                    OcBReturnOrderLog orderLog = returnOrderLogService.buildReturnOrderLog(returnOrder.getId(), LOG_TYPE, String.format(ERR_MSG, response.getMsg()), user);
//                    if (orderLog != null) {
//                        returnOrderLogList.add(orderLog);
//                    }
//                }
            } catch (Exception e) {
                log.error("{},退单订阅物流轨迹信息调用快递助手异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
                continue;
            }

        }

        //更新退换货单为已订阅物流轨迹追踪
        if (CollectionUtils.isNotEmpty(successIdList)) {
            returnOrderMapper.batchUpdateIsSendTmsLogistic(YesNoEnum.ONE.getKey(), successIdList);
        }

        try {
            commonTaskExecutor.submit(() -> {
                returnOrderLogService.batchInsertLog(returnOrderLogList);
            });
        } catch (Exception e) {
            log.error("{},退单物流轨迹订阅记录日志异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
        }
        log.info("{}.doSubscribe execute end,物流轨迹订阅结束 return order success size = {}", this.getClass().getSimpleName(), successIdList.size());
    }


    public void pushData(List<Long> idList) {

        User user = SystemUserResource.getRootUser();

        if (idList.size() > SPLIT_SIZE) {
            List<List<Long>> partitionList = Lists.partition(idList, SPLIT_SIZE);
            for (List<Long> partIdList : partitionList) {
                List<OcBReturnOrder> ocBReturnOrders = returnOrderMapper.selectOcBReturnOrderByOrder(partIdList);
                List<OcBReturnOrder> filterDataList = this.filterData(ocBReturnOrders);
                if (CollectionUtils.isNotEmpty(filterDataList)) {
                    if (log.isDebugEnabled()) {
                        log.debug("{},退单过滤后数据 size ：{}", this.getClass().getSimpleName(), filterDataList.size());
                    }
                    this.doSubscribe(filterDataList, user);
                }
            }
        } else {
            List<OcBReturnOrder> ocBReturnOrders = returnOrderMapper.selectOcBReturnOrderByOrder(idList);
            List<OcBReturnOrder> filterDataList = this.filterData(ocBReturnOrders);
            if (CollectionUtils.isNotEmpty(filterDataList)) {
                if (log.isDebugEnabled()) {
                    log.debug("{},退单过滤后数据 size ：{}", this.getClass().getSimpleName(), filterDataList.size());
                }
                this.doSubscribe(filterDataList, user);
            }
        }

    }

    /**
     * @description: 过滤筛选数据
     * <AUTHOR>
     * @DateTime 2022/4/1 18:17
     */
    public List<OcBReturnOrder> filterData(List<OcBReturnOrder> data) {
        ValueHolderV14 vh = new ValueHolderV14<>();
        if (log.isDebugEnabled()) {
            log.debug(" {}.filterData 开始数据筛选:{}", this.getClass().getSimpleName(), JSON.toJSONString(data));
        }
        List<OcBReturnOrder> matchList = new ArrayList<>(data.size());
        for (OcBReturnOrder bean : data) {
            //定时任务添加锁单操作
            String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(bean.getId());
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    continue;
                }
                if (YesNoEnum.ONE.getKey().equals(bean.getIsSendTmsLogistic())) {
                    continue;
                }
                //存在取消退单 ES同步延迟 查库后需再次进行状态校验
                if (!ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(bean.getReturnStatus()) && !ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal().equals(bean.getReturnStatus())) {
                    continue;
                }
                if (bean.getLogisticsTraceFailNum() != null && bean.getLogisticsTraceFailNum() > 5) {
                    continue;
                }
                matchList.add(bean);

            } catch (Exception e) {
                log.error("{},过滤退单数据出现异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
            } finally {
                redisLock.unlock();
            }
        }
        return matchList;
    }

    /**
     * <AUTHOR>
     * @description: 查询物流轨迹
     * @DateTime 2022/4/2 11:39
     */
    public ValueHolderV14 queryTrace(JSONObject param, User user) {
        if (!buttonSwitchOpen) {
            return ValueHolderV14Utils.getFailValueHolder("按钮功能暂未开放,请见谅！");
        }
        Long id = param.getLong("ID") == null ? param.getLong("id") : param.getLong("ID");
        if (id == null || id < 1) {
            return ValueHolderV14Utils.getFailValueHolder("参数异常");
        }
        OcBReturnOrder returnOrder = returnOrderMapper.selectByid(id);
        if (returnOrder == null) {
            return ValueHolderV14Utils.getFailValueHolder("未查询到有效退单，请检查！");
        }
        if (StringUtils.isEmpty(returnOrder.getLogisticsCode())) {
            return ValueHolderV14Utils.getFailValueHolder("当前退单没有物流单号，请稍后再试！");
        }
        if (ReturnStatusEnum.CANCLE.getVal().equals(returnOrder.getReturnStatus()) || ReturnStatusEnum.COMPLETION.getVal().equals(returnOrder.getReturnStatus())) {
            return ValueHolderV14Utils.getFailValueHolder("当前退单已取消或者已完成，无需获取物流轨迹！");
        }
        //优先通过智能识别获取物流公司编码
        String cpCode = this.getMailDiscern(returnOrder);
        if (StringUtils.isNotEmpty(cpCode)) {
            returnOrder.setCpCLogisticsEcode(cpCode);
        }

        if (YesNoEnum.ONE.getKey().equals(returnOrder.getIsSendTmsLogistic())) {
            return this.queryAfterSubscribe(returnOrder);
        } else {
            return this.queryInRealTime(returnOrder);
        }
    }

    /**
     * @description: 订阅后查询
     * <AUTHOR>
     * @DateTime 2022/4/2 10:20
     */
    public ValueHolderV14 queryAfterSubscribe(OcBReturnOrder returnOrder) {
//
//        KdzsClient kdzsClient = new KdzsClient(appKey, appSecret);
//        KdzsLogisticsTraceGetRequest kdzsLogisticsTraceGetRequest = new KdzsLogisticsTraceGetRequest();
//        kdzsLogisticsTraceGetRequest.setMailNo(returnOrder.getLogisticsCode());
//        kdzsLogisticsTraceGetRequest.setCpCode(returnOrder.getCpCLogisticsEcode());
//
//        try {
//            KdzsLogisticsTraceGetResponse response = kdzsClient.execute(kdzsLogisticsTraceGetRequest);
//            if (log.isDebugEnabled()) {
//                log.debug("{}.queryAfterSubscribe result:{}", this.getClass().getSimpleName(), JSON.toJSONString(response));
//            }
//            if (response.isSuccess()) {
//                if (response.getLogisticsTrace() != null) {
//                    KdzsCallBackRequest callBackRequest = this.transferField(response.getLogisticsTrace());
//                    return kdzsCallBackService.saveAndUpdateLogisticsTrace(callBackRequest);
//                } else {
//                    return ValueHolderV14Utils.getFailValueHolder("快递助手返回的物流轨迹结果为空,请检查物流单号并稍后再试！");
//                }
//            } else {
//                return ValueHolderV14Utils.getFailValueHolder(String.format("获取失败,快递助手返回信息[%s]", response.getMsg()));
//            }
//        } catch (Exception e) {
//            log.error("{}.queryAfterSubscribe 出现异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
//        }
        return ValueHolderV14Utils.getSuccessValueHolder("获取成功");
    }

    /**
     * @description: 实时查询
     * <AUTHOR>
     * @DateTime 2022/4/2 10:20
     */
    public ValueHolderV14 queryInRealTime(OcBReturnOrder returnOrder) {

//        KdzsClient kdzsClient = new KdzsClient(appKey, appSecret);
//        KdzsLogisticsTraceSearchRequest kdzsLogisticsTraceSearchRequest = new KdzsLogisticsTraceSearchRequest();
//        kdzsLogisticsTraceSearchRequest.setMailNo(returnOrder.getLogisticsCode());
//        kdzsLogisticsTraceSearchRequest.setCpCode(returnOrder.getCpCLogisticsEcode());
//        kdzsLogisticsTraceSearchRequest.setPhone(this.getSubPhone(returnOrder));
//        try {
//            KdzsLogisticsTraceSearchResponse response = kdzsClient.execute(kdzsLogisticsTraceSearchRequest);
//
//            if (log.isDebugEnabled()) {
//                log.debug("{}.queryInRealTime result:{}", this.getClass().getSimpleName(), JSON.toJSONString(response));
//            }
//            if (response.isSuccess()) {
//                if (response.getLogisticsTrace() != null) {
//                    KdzsCallBackRequest callBackRequest = this.transferField(response.getLogisticsTrace());
//                    return kdzsCallBackService.saveAndUpdateLogisticsTrace(callBackRequest);
//                } else {
//                    return ValueHolderV14Utils.getFailValueHolder("快递助手返回的物流轨迹结果为空,请检查物流单号并稍后再试！");
//                }
//            } else {
//                return ValueHolderV14Utils.getFailValueHolder(String.format("获取失败,快递助手返回信息[%s]", response.getMsg()));
//            }
//        } catch (Exception e) {
//            log.error("{}.queryInRealTime 出现异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
//        }
        return ValueHolderV14Utils.getSuccessValueHolder("获取成功");
    }

    /**
     * <AUTHOR>
     * @description: 智能识别物流单号 获取物流公司
     * @DateTime 2022/4/6 13:48
     */
    public String getMailDiscern(OcBReturnOrder returnOrder) {
//        KdzsClient kdzsClient = new KdzsClient(appKey, appSecret);
//        KdzsLogisticsMailDiscernRequest kdzsLogisticsMailDiscernRequest = new KdzsLogisticsMailDiscernRequest();
//        kdzsLogisticsMailDiscernRequest.setMailNo(returnOrder.getLogisticsCode());
//        try {
//            KdzsLogisticsMailDiscernResponse response = kdzsClient.execute(kdzsLogisticsMailDiscernRequest);
//            if (log.isDebugEnabled()) {
//                log.debug("{}.getMailDiscern result:{}", this.getClass().getSimpleName(), JSON.toJSONString(response));
//            }
//            if (response.isSuccess()) {
//                if (CollectionUtils.isNotEmpty(response.getExpressCompanyList())) {
//                    String cpCode = response.getExpressCompanyList().get(0).getCpCode();
//                    return cpCode;
//                }
//            }
//        } catch (Exception e) {
//            log.error("{}.getMailDiscern 出现异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
//        }
        return "";
    }

//    public KdzsCallBackRequest transferField(KdzsLogisticsTraceGetResponse.LogisticsTraceBean logisticsTrace) {
//        KdzsCallBackRequest callBackRequest = new KdzsCallBackRequest();
//        callBackRequest.setCpCode(logisticsTrace.getCpCode());
//        callBackRequest.setMailNo(logisticsTrace.getMailNo());
//        callBackRequest.setLogisticsStatus(logisticsTrace.getLogisticsStatus());
//        callBackRequest.setLogisticsStatusDesc(logisticsTrace.getLogisticsStatusDesc());
//        List<KdzsLogisticsTraceGetResponse.LogisticsTraceBean.LogisticsTraceDetailListBean> logisticsTraceDetailList = logisticsTrace.getLogisticsTraceDetailList();
//        List<LogisticsFullTrace> logisticsFullTraceList = JSON.parseArray(JSON.toJSONString(logisticsTraceDetailList), LogisticsFullTrace.class);
//        callBackRequest.setLogisticsFullTraceList(logisticsFullTraceList);
//        return callBackRequest;
//    }
//
//    public KdzsCallBackRequest transferField(KdzsLogisticsTraceSearchResponse.LogisticsTraceBean logisticsTrace) {
//        KdzsCallBackRequest callBackRequest = new KdzsCallBackRequest();
//        callBackRequest.setCpCode(logisticsTrace.getCpCode());
//        callBackRequest.setMailNo(logisticsTrace.getMailNo());
//        callBackRequest.setLogisticsStatus(logisticsTrace.getLogisticsStatus());
//        callBackRequest.setLogisticsStatusDesc(logisticsTrace.getLogisticsStatusDesc());
//        List<KdzsLogisticsTraceSearchResponse.LogisticsTraceBean.LogisticsTraceDetailListBean> logisticsTraceDetailList = logisticsTrace.getLogisticsTraceDetailList();
//        List<LogisticsFullTrace> logisticsFullTraceList = JSON.parseArray(JSON.toJSONString(logisticsTraceDetailList), LogisticsFullTrace.class);
//        callBackRequest.setLogisticsFullTraceList(logisticsFullTraceList);
//        return callBackRequest;
//    }
//
//    private String getSubPhone(OcBReturnOrder returnOrder) {
//        String phone = StringUtils.isNotEmpty(returnOrder.getReceiveMobile()) ? returnOrder.getReceiveMobile() : returnOrder.getReceivePhone();
//        String subPhone = StringUtils.substring(phone, phone.length() - 4, phone.length());
//        return subPhone;
//    }
}
