package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.extmodel.OcBOrderExtToHya;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by 王强 on 2019/4/11 13:22
 * <p>
 * 现在入参新增一个BILL_TYPE，格式：{"SOURCE_CODE":10010,"BILL_TYPE":1},
 * 如果这个值是1的时候查询退换货订单表oc_b_return_order(平台单号匹配，只返回1条)，
 * 明细oc_b_return_order_refund，
 * 其中原系统订单编号取主表的orig_order_id字段，
 * 值不为1的时候按原来的查询，其中明细的退款金额给空值
 */
@Slf4j
@Component
public class QueryOrderItemService {
    @Autowired
    OcBOrderItemMapper ocBorderItemMapper;
    @Autowired
    OcBorderDetailService ocBorderDetailService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    /**
     * 获取主表+明细 的 信息
     *
     * @param obj  参数
     * @param user 用户信息
     * @return
     */
    public ValueHolder queryOrderItem(JSONObject obj, User user) {
        ValueHolder vh = new ValueHolder();
        JSONObject json = new JSONObject();
        List itemList = new ArrayList();
        String sourceCode = obj.getString("SOURCE_CODE");
        //如果为1 查询退换货订单表
        Integer billType = obj.getInteger("BILL_TYPE");
        if (billType != null && StringUtils.isNotEmpty(sourceCode)) {
            JSONObject whereKeys = new JSONObject();
            JSONObject search = null;
            if (billType == 1) {

                whereKeys.put("ORIG_SOURCE_CODE", sourceCode);
                whereKeys.put("RETURN_STATUS", "!=" + TaobaoReturnOrderExt.ReturnOrderStatus.CANCEL.getCode());
                try {
                    search = ES4ReturnOrder.getReturnOrderSearchByUserinfo(whereKeys, null, null, 200, 0, new String[]{"ID"});
                    Integer totalCount = search.getInteger("total");
                    if (totalCount > 200) {
                        search = ES4ReturnOrder.getReturnOrderSearchByUserinfo(whereKeys, null, null, totalCount, 0, new String[]{"ID"});
                    }
                    JSONArray orderReturnDate = search.getJSONArray("data");
                    OcBOrderExtToHya ocBReturnOrder = new OcBOrderExtToHya();
                    List<OcBOrderExtToHya> orderReturnIds = new ArrayList<>();
                    List<OcBOrderExtToHya> ocBReturnOrderList = new ArrayList<>();
                    log.error("查询退单的入参" + JSONObject.toJSONString(orderReturnDate));
                    if (null != orderReturnDate && !orderReturnDate.isEmpty()) {
                        orderReturnIds = JSONObject.parseArray(
                                orderReturnDate.toJSONString(),
                                OcBOrderExtToHya.class);
                        orderReturnIds.stream().forEach(var0 -> {
                            //退换货ID
                            Long orderReturnId = var0.getId();
                            OcBOrderExtToHya ocBReturnOrder1 = ocBReturnOrderMapper.SelectExtById(orderReturnId);
                            if (ocBReturnOrder1 != null) {
                                //获取订单id TODO
                                Long orderId = ocBReturnOrder1.getOrigOrderId();
                                OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
                                ocBReturnOrder1.setOrderType(ocBOrder.getOrderType());
                                ocBReturnOrder1.setOrderAmt(ocBOrder.getOrderAmt());
                             /*   if(ocBReturnOrder1.getReceiveName() == null) {
                                    ocBReturnOrder1.setReceiveName(ocBOrder.getReceiverName());//设置收款人姓名
                                }
                                if(ocBReturnOrder1.getReceivePhone() == null) {
                                    ocBReturnOrder1.setReceivePhone(ocBOrder.getReceiverPhone()); //设置顾客电话
                                }*/
                                ocBReturnOrderList.add(ocBReturnOrder1);
                            }

                        });
                        //过滤 退换货状态为60 的
//                        List<OcBOrderExtToHya> ocBReturnOrders = ocBReturnOrderList.stream().filter(var1 ->
//                                var1.getReturnStatus() !=  TaobaoReturnOrderExt.ReturnOrderStatus.CANCEL.getCode()
//                        ).collect(Collectors.toList());
                        for (OcBOrderExtToHya bReturnOrder : ocBReturnOrderList) {
                            if (bReturnOrder.getOrigOrderId() != null) {
                                OcBOrder ocBOrder = ocBOrderMapper.selectById(bReturnOrder.getOrigOrderId());
                                bReturnOrder.setReceiver_name(ocBOrder.getReceiverName());//设置 收款人姓名
                                bReturnOrder.setReceiver_mobile(ocBOrder.getReceiverMobile());// 设置收款人电话
                                bReturnOrder.setBuyer_alipay_no(ocBOrder.getBuyerAlipayNo());//设置 收款人账号
                                bReturnOrder.setPay_time(ocBOrder.getPayTime()); // 设置付款时间
                                bReturnOrder.setUser_nick(ocBOrder.getUserNick());// 设置用户昵称
                            }

                        }
                        if (CollectionUtils.isNotEmpty(ocBReturnOrderList)) {
                            ocBReturnOrder = ocBReturnOrderList.get(0);
                            //TODO 调俊服务
                            JSONObject var = new JSONObject();
                            var.put("ORDER", ocBReturnOrderList);
                            //0/1(0是全渠道 1 是退换货)
                            var.put("FLAG", 1);
                            try {
                                itemList = ocBorderDetailService.getOrderItemListByIdAndBillNo(
                                        var, user);
                                json.put("OC_B_ORDERITEM", itemList);
                                json.put("OC_B_ORDER", ocBReturnOrder);
                                json.put("IS_REFUND_FLAG", "Y");
                                vh.put("code", ResultCode.SUCCESS);
                                vh.put("message", Resources.getMessage("获取信息成功", user.getLocale()));
                                vh.put("data", json);
                                return vh;
                            } catch (Exception e) {
                                vh.put("code", ResultCode.FAIL);
                                vh.put("message", Resources.getMessage("调用俊磊服务获取明细失败", user.getLocale()));
                                log.debug("调用俊磊服务获取明细->参数为：" + ocBReturnOrderList + "->返回结果为:" + itemList);
                                return vh;
                            }

                        } else {
                            vh.put("code", ResultCode.FAIL);
                            vh.put("message", "未查询到数据");
                            return vh;
                        }
                    } else {
                        //查不到退换货订单的时 ,再次查询全渠道订单
                        vh = queryOrderAndOrderItem(user, sourceCode, billType);
                        vh.put("IS_REFUND_FLAG", "N");
                        JSONObject json1 = (JSONObject) vh.get("data");
                        if (json1 == null) {
                            vh.put("code", ResultCode.FAIL);
                            vh.put("message", Resources.getMessage(
                                    "不存在退换货订单，不能新增退货退款！",
                                    user.getLocale()));
                            return vh;
                        }
                        return vh;
                    }

                } catch (Exception e) {
                    vh.put("code", ResultCode.FAIL);
                    vh.put("message", Resources.getMessage("ES中获取订单信息异常", user.getLocale()));
                    log.error(LogUtil.format("ES中获取订单信息异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));

                    return vh;
                }
            } else {
                //billType不为1时,查询全渠道订单及订单明细
                try {
                    vh = queryOrderAndOrderItem(user, sourceCode, billType);
                    vh.put("IS_REFUND_FLAG", "N");
                } catch (NDSException e) {
                    vh.put("code", ResultCode.FAIL);
                    vh.put("message", e.getMessage());
                    return vh;
                }
                JSONObject json1 = (JSONObject) vh.get("data");
                if (json1 == null) {
                    vh.put("code", ResultCode.FAIL);
                    vh.put("message", Resources.getMessage(
                            "不存在该平台单号下的订单数据！",
                            user.getLocale()));
                    return vh;
                }
                return vh;
            }

        } else {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("参数不能为空", user.getLocale()));
        }
        return vh;
    }

    /**
     * 查询全渠道订单及订单明细
     *
     * @param user       用户
     * @param sourceCode 平台单号
     * @param billType   为1 时 查询退换订单 ,否 查询 全渠道订单
     * @return 全渠道信息及明细
     * FLAG:0/1(0是全渠道 1 是退换货)
     */
    private ValueHolder queryOrderAndOrderItem(User user, String sourceCode, Integer billType) throws NDSException {
        ValueHolder vh = new ValueHolder();
        //  JSONObject search;
        List<Long> search;
        JSONObject json = new JSONObject();
        try {
        /*    search = ES4Order.findJSONObjectBySourceCode(sourceCode,200);
            Integer totalCount = search.getInteger("total");
            if (totalCount > 200) {
                search =ES4Order.findJSONObjectBySourceCode(sourceCode,totalCount);
            }*/
            search = GSI4Order.getIdListBySourceCode(sourceCode);
        } catch (Exception e) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("ES中获取订单信息异常", user.getLocale()));
            log.error(LogUtil.format("ES中获取订单信息异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            return vh;
        }
        //    JSONArray orderDate = search.getJSONArray("data");
        OcBOrder ocBOrder = new OcBOrder();
        //    List<OcBOrder> orderIds = new ArrayList<>();
        List<OcBOrder> ocBOrders = new ArrayList<>();
        List<OcBOrder> orders = new ArrayList<>();
        List itemList = new ArrayList();
        if (CollectionUtils.isNotEmpty(search)) {
            search.stream().forEach(k -> {
                //     Long orderId = k.getId();
                OcBOrder ocBorder = ocBOrderMapper.selectById(k);
                if (ocBorder != null) {
                    ocBOrders.add(ocBorder);
                }

            });
            if (billType == 1) {
                //只有为1且查不到退换货订单的时
                orders = filterOrderByOrderReturn(ocBOrders);
            } else {
                orders = filterOrderByOrder(ocBOrders);
            }

            if (CollectionUtils.isNotEmpty(orders)) {
                ocBOrder = orders.get(0);
                try {
                    //俊磊 服务 获取明细
                    JSONObject var0 = new JSONObject();
                    var0.put("ORDER", orders);
                    //0/1(0是全渠道 1 是退换货)
                    var0.put("FLAG", 0);
                    itemList = ocBorderDetailService.getOrderItemListByIdAndBillNo(
                            var0, user);
                    json.put("OC_B_ORDERITEM", itemList);
                    json.put("OC_B_ORDER", ocBOrder);
                    vh.put("code", ResultCode.SUCCESS);
                    vh.put("message", Resources.getMessage("获取信息成功", user.getLocale()));
                    vh.put("data", json);
                } catch (Exception e) {
                    log.error(LogUtil.format("查询全渠道订单及订单明细,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    throw new NDSException(Resources.getMessage("获取明细失败:" + e.getMessage(), user.getLocale()));


                }
            }

        }
        return vh;
    }

    /**
     * 只有为1且查不到退换货订单
     * 订单来源=经销销订单
     *
     * @param ocBOrders 订单信息
     * @return
     */
    private List<OcBOrder> filterOrderByOrderReturn(List<OcBOrder> ocBOrders) {
        List<OcBOrder> bOrders = ocBOrders.stream().filter(key -> key.getIsactive().equals("Y")
                /*&&
                key.getOrderStatus() != OmsOrderStatus.CANCELLED.toInteger()
                &&
                key.getOrderStatus() != OmsOrderStatus.SYS_VOID.toInteger()*/
                &&
                !OmsOrderStatus.UNPAID.toInteger().equals(key.getOrderStatus())
                &&
                key.getPlatform().equals(77)).collect(Collectors.toList());
        return bOrders;
    }

    /**
     * type 不为1 时
     *
     * @param ocBOrders 订单信息
     * @return
     */
    private List<OcBOrder> filterOrderByOrder(List<OcBOrder> ocBOrders) {
        List<OcBOrder> collect = ocBOrders.stream()
                .filter(p -> OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus())
                        || OmsOrderStatus.SYS_VOID.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
        if (collect.size() == ocBOrders.size()) {
            List<OcBOrder> bOrders = ocBOrders.stream().filter(key -> key.getIsactive().equals("Y")
               /* &&
                key.getOrderStatus() != OmsOrderStatus.CANCELLED.toInteger()
                &&
                key.getOrderStatus() != OmsOrderStatus.SYS_VOID.toInteger()*/
                    && !OmsOrderStatus.UNPAID.toInteger().equals(key.getOrderStatus())).collect(Collectors.toList());
            return bOrders;
        } else {
            List<OcBOrder> bOrders = ocBOrders.stream().filter(key -> key.getIsactive().equals("Y")
                    && !OmsOrderStatus.CANCELLED.toInteger().equals(key.getOrderStatus())
                    && !OmsOrderStatus.SYS_VOID.toInteger().equals(key.getOrderStatus())
                    && !OmsOrderStatus.UNPAID.toInteger().equals(key.getOrderStatus())).collect(Collectors.toList());
            return bOrders;
        }
    }
}
