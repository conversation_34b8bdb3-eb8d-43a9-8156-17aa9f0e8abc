package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.model.table.TmallExchangeReturngoodsAgreeModel;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 李杰
 * @since: 2019/4/2
 * create at : 2019/4/2 9:21
 */
@Slf4j
@Component
public class ExchangeInService {

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    OcBReturnOrderLogMapper ocBReturnOrderLogMapper;
    @Autowired
    IpBTaobaoExchangeMapper ipBTaobaoExchangeMapper;
    @Autowired
    private IpRpcService ipRpcService;


    public ValueHolderV14 exchangeIn(Long id, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("天猫在线换货确认服务入参: {}", id),id);
        }
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(id);
        if (ocBReturnOrder == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("根据退单id未查询到退单信息：" + id);
            return vh;
        }

        QueryWrapper<IpBTaobaoExchange> wrapper = new QueryWrapper<>();
        wrapper.eq("dispute_id", ocBReturnOrder.getTbDisputeId());
        IpBTaobaoExchange ipBTaobaoExchange = ipBTaobaoExchangeMapper.selectOne(wrapper);
        if (ipBTaobaoExchange == null) {
            log.debug(LogUtil.format("根据平台单号查找淘宝换货中间表未查到记录，平台单号: {}", ocBReturnOrder.getTbDisputeId()),ocBReturnOrder.getTbDisputeId());
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("中间表不存在此换货单：" + ocBReturnOrder.getTbDisputeId());
            confirmFail(ocBReturnOrder, vh, user);
            return vh;
        } else {
            String status = ipBTaobaoExchange.getStatus();
            if ("换货成功".equals(status) || "待发出换货商品".equals(status) || "待买家收货".equals(status)) {
                confirmSuccess(ocBReturnOrder, user);
                vh.setCode(ResultCode.SUCCESS);
                return vh;
            } else {
                //调用换货确认接口
                TmallExchangeReturngoodsAgreeModel model = new TmallExchangeReturngoodsAgreeModel();
                model.setOperateUser(user);
                model.setDisputeId(ocBReturnOrder.getTbDisputeId());
                model.setSellerNick(ocBReturnOrder.getSellerNick());
                try {
                    vh = ipRpcService.tmallExchangeReturngoodsAgree(model);
                } catch (Exception e) {
                    log.error(LogUtil.format("调用天猫在线换货接口异常: {}"), Throwables.getStackTraceAsString(e));
                }
                if (vh.getCode() == 0) {
                    confirmSuccess(ocBReturnOrder, user);
                } else {
                    vh.setMessage(vh.getMessage() + status);
                    confirmFail(ocBReturnOrder, vh, user);
                }
                return vh;
            }
        }
    }

    /**
     * 更新确认失败
     *
     * @param ocBReturnOrder
     * @param vh
     * @param user
     */
    private void confirmFail(OcBReturnOrder ocBReturnOrder, ValueHolderV14 vh, User user) {
        OcBReturnOrder ocBReturnOrder1 = new OcBReturnOrder();
        ocBReturnOrder1.setId(ocBReturnOrder.getId());
        ocBReturnOrder1.setIsReceiveConfirm(2);
        int i = ocBReturnOrderMapper.updateById(ocBReturnOrder1);
        //添加退换货订单操作日志
        Long logId = ModelUtil.getSequence("oc_b_return_order_log");
        OcBReturnOrderLog log1 = getLog(logId, "退换货单确认入库",
                "同步天猫退换货单确认入仓失败" + vh.getMessage(), user.getName(), ocBReturnOrder.getId());
        log1.setAdClientId(user.getClientId() + 0L);
        log1.setAdOrgId(user.getOrgId() + 0L);
        log1.setIpAddress(user.getLastloginip());
        ocBReturnOrderLogMapper.insert(log1);
    }

    /**
     * 更新确认成功
     *
     * @param ocBReturnOrder
     * @param user
     */
    private void confirmSuccess(OcBReturnOrder ocBReturnOrder, User user) {
        OcBReturnOrder ocBReturnOrder1 = new OcBReturnOrder();
        ocBReturnOrder1.setId(ocBReturnOrder.getId());
        ocBReturnOrder1.setIsReceiveConfirm(1);
        int i = ocBReturnOrderMapper.updateById(ocBReturnOrder1);
        //添加退换货订单操作日志
        Long logId = ModelUtil.getSequence("oc_b_return_order_log");
        OcBReturnOrderLog log1 = getLog(logId, "退换货单确认入库",
                "同步天猫退换货单确认入仓成功", user.getName(), ocBReturnOrder.getId());
        log1.setAdClientId(user.getClientId() + 0L);
        log1.setAdOrgId(user.getOrgId() + 0L);
        log1.setIpAddress(user.getLastloginip());
        ocBReturnOrderLogMapper.insert(log1);
    }

    private OcBReturnOrderLog getLog(Long id, String logType, String message, String userName, Long reOrderid) {
        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
        ocBReturnOrderLog.setId(id);
        ocBReturnOrderLog.setLogType(logType);
        ocBReturnOrderLog.setLogMessage(message);
        ocBReturnOrderLog.setUserName(userName);
        ocBReturnOrderLog.setOcBReturnOrderId(reOrderid);
        return ocBReturnOrderLog;
    }
}
