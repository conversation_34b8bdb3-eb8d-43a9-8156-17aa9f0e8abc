package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.util.OrderPrevDealUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: 夏继超
 * @since: 2019/3/13
 * create at : 2019/3/13 15:17
 */
@Slf4j
@Component
//@Transactional
public class CheckAddGiftService {
    @Autowired
    OcBOrderMapper ocBOrderMapper;

    /**
     * 检查参数
     *
     * @param obj
     * @return
     */
    public ValueHolderV14 checkAddGift(JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14();
        JSONArray ids = obj.getJSONArray("ids");
        //订单状态非“待审核”、“缺货”、“已审核”等，则提示：“当前状态异常，不允许添加赠品！”
        List list = new ArrayList<>();
        list.add(OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal());
        list.add(OcOrderCheckBoxEnum.CHECKBOX_OUT_OF_STOCK.getVal());
        list.add(OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal());
        if (ids != null) {
            Long orderId;
            for (Object id : ids) {
                orderId = Long.valueOf(id.toString());
                OcBOrder ocOrder = ocBOrderMapper.selectById(orderId);
                if (ocOrder == null) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("当前数据不存在！");
                    return vh;
                }

                // JITX平台.拦截
                ValueHolderV14 pfVh = OrderPrevDealUtil.platformInterceptVh(ocOrder);
                if (ResultCode.FAIL == pfVh.getCode()) {
                    return pfVh;
                }

                //订单状态为“已审核”，则提示：“订单已审核，不允许添加赠品记录，建议反审核再进行修改！
                Integer orderStatus = ocOrder.getOrderStatus();
                if (orderStatus.equals(OcOrderCheckBoxEnum.CHECKBOX_AUDITED.getVal())) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("订单已审核，不允许添加赠品记录，建议反审核再进行修改！");
                    return vh;
                } else if (!list.contains(orderStatus)) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("当前状态异常，不允许添加赠品！");
                    return vh;
                }
            }
        } else {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请选择需要添加赠品记录！");
            return vh;
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("校验成功");
        return vh;
    }
}
