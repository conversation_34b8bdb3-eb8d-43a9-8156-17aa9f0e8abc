package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.burgeon.r3.sg.sourcing.common.StrategyConstants;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.burgeon.r3.sg.sourcing.model.request.SkuItemC2S;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCRegionRelation;
import com.jackrain.nea.cpext.model.table.CpCVipcomWahouse;
import com.jackrain.nea.cpext.model.table.TOmsvipfulladdress;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.vips.VipJitxFeedBackDeliveryResultModel;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipOccupyItemMapper;
import com.jackrain.nea.oc.oms.model.constant.VipConstant;
import com.jackrain.nea.oc.oms.model.enums.FeedBackStatus;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderLog;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.result.FeedbackDeliveryResponse;
import com.jackrain.nea.oc.oms.model.result.WareHouseResult;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.*;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.model.vo.StCSyncStockStrategyVo;
import com.jackrain.nea.st.service.OmsQueryWareHouseService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: chenxiulou
 * @description: 中间表待寻仓订单处理服务
 * @since: 2019-06-26
 * create at : 2019-06-26 11:24
 */
@Component
@Slf4j
public class IpJitxDeliveryService {

    @Value("${r3.oc.oms.delivery.virtual.occupy.isopen:true}")
    private boolean virtualOccupyIsopen;

    @Autowired
    private IpBJitxDeliveryMapper ipBJitxDeliveryMapper;
    @Autowired
    private IpBJitxDeliveryItemMapper ipBJitxDeliveryItemMapper;
    @Autowired
    private OmsSyncStockStrategyService omsSyncStockStrategyService;
    @Autowired
    private OmsQueryWareHouseService omsQueryWareHouseService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private BasicCpQueryService basicCpQueryService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private IpVipTimeOrderService timeOrderService;
    @Autowired
    private VipComRpcService vipComRpcService;

    @Autowired
    private PropertiesConf propertiesConf;

//    @Autowired
//    private R3MqSendHelper r3MqSendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    private IpBTimeOrderVipOccupyItemMapper ipBTimeOrderVipOccupyItemMapper;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;

    @Autowired
    private IpBTimeOrderVipMapper ipBTimeOrderVipMapper;




    /**
     * 依据OrderNo进行查询中间表信息数据
     *
     * @param orderNo 订单号
     * @return 中间表信息数据
     */
    public IpJitxDeliveryRelation selectJitxDelivery(String orderNo) {
        IpBJitxDelivery orderInfo = this.ipBJitxDeliveryMapper.selectJitxOrderByOrderSn(orderNo);

        if (orderInfo == null) {
            return null;
        }
        IpJitxDeliveryRelation jitxDeliveryRelation = new IpJitxDeliveryRelation();
        List<IpBJitxDeliveryItemEx> orderItemList = this.ipBJitxDeliveryItemMapper.selectDeliveryItemList(orderInfo.getId());
        jitxDeliveryRelation.setJitxDeliveryItemList(orderItemList);
        jitxDeliveryRelation.setJitxDelivery(orderInfo);

        //查询二级索引 获取时效订单
        List<IpBTimeOrderVip> timeOrderVipList = ipBTimeOrderVipMapper.selectTimeOrderOccupiedOrderSnListByOrderSn(orderNo);
        if(CollectionUtils.isEmpty(timeOrderVipList)){
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("寻仓单orderSn:{}寻源占单查询时效单为空",
                        "IpJitxDeliveryService.selectJitxDelivery",orderNo),orderNo);
            }
            return jitxDeliveryRelation;
        }
        jitxDeliveryRelation.setRootOrderSn(timeOrderVipList.get(0).getRootOrderSn());
        //查询时效订单占用明细
        List<Long> timeOrderIdList =  timeOrderVipList.stream().map(IpBTimeOrderVip::getId).collect(Collectors.toList());
        Map<Long, List<IpBTimeOrderVipOccupyItem>> ipBTimeOrderVipItemMap = new HashMap<>();
        List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipItemList =  ipBTimeOrderVipOccupyItemMapper.selectList(Wrappers.<IpBTimeOrderVipOccupyItem>lambdaQuery()
                .eq(IpBTimeOrderVipOccupyItem::getIsactive, "Y")
                .in(IpBTimeOrderVipOccupyItem::getIpBTimeOrderVipId,timeOrderIdList));
        ipBTimeOrderVipItemMap = ipBTimeOrderVipItemList.stream()
                .collect(Collectors.groupingBy(IpBTimeOrderVipOccupyItem::getIpBTimeOrderVipId));
        List<IpVipTimeOrderRelation> vipTimeOrderRelations = new ArrayList<>();
        for(IpBTimeOrderVip vip:timeOrderVipList){
            IpVipTimeOrderRelation ipVipTimeOrderRelation = new IpVipTimeOrderRelation();
            ipVipTimeOrderRelation.setIpBTimeOrderVip(vip);
            ipVipTimeOrderRelation.setIpBTimeOrderVipOccupyItemList(ipBTimeOrderVipItemMap.get(vip.getId()));
            vipTimeOrderRelations.add(ipVipTimeOrderRelation);
        }
        jitxDeliveryRelation.setIpVipTimeOrderRelationList(vipTimeOrderRelations);

        return jitxDeliveryRelation;
    }


    /**
     * 依据OrderNo进行查询中间表信息数据
     *
     * @param orderNos 订单号
     * @return 中间表信息数据
     */
    public List<IpBJitxDelivery> selectJitxDeliveryByOrderNos(List<String> orderNos) {
        return this.ipBJitxDeliveryMapper.selectJitxDeliveryByOrderNos(orderNos);
    }

    public int batchUpdateList(List<String> orderSns, User user, Date currentDate, String remark) {
        return this.ipBJitxDeliveryMapper.batchuUpdateList(orderSns
                , user.getId().longValue()
                , user.getName()
                , user.getEname()
                , currentDate
                , remark);
    }

    /**
     * 更新寻仓订单中间表订单状态值
     *
     * @param orderNo        订单编号
     * @param feedBackStatus 反馈状态
     * @param feedBackMsgKey 反馈错误信息
     * @param wareHouse      反馈实体仓
     * @return 更新是否成功。true-成功；false-失败
     */
    public boolean updateJitxDeliveryFeedBackStatus(String orderNo, String feedBackStatus,
                                                    String feedBackMsgKey, String wareHouse) {

        if (feedBackMsgKey != null && feedBackMsgKey.length() > IpBJitxDeliveryMapper.MAX_FEEDBACKMSG_LENGTH) {
            feedBackMsgKey = feedBackMsgKey.substring(0, IpBJitxDeliveryMapper.MAX_FEEDBACKMSG_LENGTH - 1);
        }
        IpBJitxDelivery ipBJitxDelivery = new IpBJitxDelivery();
        ipBJitxDelivery.setFeedbackState(feedBackStatus);
        ipBJitxDelivery.setFeedbackmsgkey(feedBackMsgKey);
        ipBJitxDelivery.setWarehouse(wareHouse);
        QueryWrapper<IpBJitxDelivery> wrapper = new QueryWrapper<>();
        wrapper.eq("order_sn", orderNo);
        int result = this.ipBJitxDeliveryMapper.update(ipBJitxDelivery, wrapper);
        if (result > 0) {
            String indexName = OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_INDEX_NAME;
            String typeName = OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_TYPE_NAME;
            String itemTypeName = OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_ITEM_TYPE_NAME;

            IpJitxDeliveryRelation newDeliveryRelation = this.selectJitxDelivery(orderNo);
            try {
                if (!SpecialElasticSearchUtil.indexExists(indexName)) {
                    SpecialElasticSearchUtil.indexCreate(IpBJitxDeliveryItem.class, IpBJitxDelivery.class);
                }
                SpecialElasticSearchUtil.indexDocument(indexName, typeName, newDeliveryRelation.getJitxDelivery(),
                        newDeliveryRelation.getOrderId());
                if (newDeliveryRelation.getJitxDeliveryItemList() != null) {
                    SpecialElasticSearchUtil.indexDocuments(indexName, itemTypeName,
                            newDeliveryRelation.getJitxDeliveryItemList(), "IP_B_JITX_DELIVERY_ID");
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result > 0;
    }

    /**
     * 更新寻仓订单中间表订单状态值
     *
     * @param orderNo    订单编号
     * @param syncStatus 转换订单状态
     * @param remarks    备注信息
     * @return 更新是否成功。true-成功；false-失败
     */
    public boolean updateJitxSyncStatus(String orderNo, SyncStatus syncStatus,
                                        String remarks) {
        boolean isUpdateTransNum = syncStatus == SyncStatus.SYNCSUCCESS;
        if (remarks != null && remarks.length() > IpBJitxDeliveryMapper.MAX_REMARK_LENGTH) {
            remarks = remarks.substring(0, IpBJitxDeliveryMapper.MAX_REMARK_LENGTH - 1);
        }
        IpBJitxDelivery ipBJitxDelivery = new IpBJitxDelivery();
        ipBJitxDelivery.setSynstatus(Long.valueOf(syncStatus.toInteger()));
        ipBJitxDelivery.setSysremark(remarks);
        ipBJitxDelivery.setModifieddate(new Date());
        QueryWrapper<IpBJitxDelivery> wrapper = new QueryWrapper<>();
        wrapper.eq("order_sn", orderNo);
        int result = this.ipBJitxDeliveryMapper.update(ipBJitxDelivery, wrapper);
        /*if (result > 0) {
            String indexName = OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_INDEX_NAME;
            String typeName = OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_TYPE_NAME;
            String itemTypeName = OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_ITEM_TYPE_NAME;

            IpJitxDeliveryRelation newDeliveryRelation = this.selectJitxDelivery(orderNo);
            try {
                if (!SpecialElasticSearchUtil.indexExists(indexName)) {
                    SpecialElasticSearchUtil.indexCreate(IpBJitxDeliveryItem.class, IpBJitxDelivery.class);
                }
                SpecialElasticSearchUtil.indexDocument(indexName, typeName, newDeliveryRelation.getJitxDelivery(),
                        newDeliveryRelation.getOrderId());
                if (newDeliveryRelation.getJitxDeliveryItemList() != null) {
                    SpecialElasticSearchUtil.indexDocuments(indexName, itemTypeName,
                            newDeliveryRelation.getJitxDeliveryItemList(), "IP_B_JITX_DELIVERY_ID");
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }*/
        return result > 0;
    }

    /**
     * @param deliveryRelation 订单对象
     * @return
     * @Description 分仓逻辑
     * <AUTHOR>
     * @date 2019-06-27 2019-06-27
     */
    @Transactional(rollbackFor = Exception.class)
    public Long exeCallDistributeWarehouse(IpJitxDeliveryRelation deliveryRelation) {
        try {
            IpBJitxDelivery ipBJitxDelivery = deliveryRelation.getJitxDelivery();
            //查询订单中店铺在【店铺库存同步策略】中存在的逻辑仓（启用状态）
            //syncStockStrategyQueryServiceCmd.querySyncStockStrategyItemByID(shopId);
            List<Long> storeList = omsSyncStockStrategyService.queryShopStoreNextList(ipBJitxDelivery.getCpCShopId());
            if (log.isDebugEnabled()) {
                log.debug("订单OrderId" + deliveryRelation.getOrderId() + "查找逻辑仓集合" + storeList);
            }
            if (CollectionUtils.isEmpty(storeList)) {
                return null;
            } else {
                //根据逻辑仓找到对应的实体仓
                List<Long> warePhyHouseIds = omsQueryWareHouseService.queryWareHouseIds(storeList);
                log.debug("订单OrderId" + deliveryRelation.getOrderId() + "根据逻辑仓找到对应的实体仓集合" + warePhyHouseIds);
                //根据逻辑仓聚合实体仓
                if (CollectionUtils.isEmpty(warePhyHouseIds)) {
                    return null;
                } else {
                    //若有多个实体仓,店铺供货仓集合[即实体仓+逻辑仓名称+逻辑仓ID]和订单的SKU集合[即商品条码+商品ID]的入参，调用【寻仓库存服务】
                    Long wareId = checkWareHouseList(deliveryRelation, warePhyHouseIds);
                    return wareId;

                }
            }
        } catch (NDSException ex) {
            ex.printStackTrace();
            log.error("订单OrderId" + deliveryRelation.getOrderId() + "调用分配发货仓库服务异常: ", ex);
            return null;
        }

    }


    /**
     * @param deliveryRelation 订单对象
     * @return
     * @Description 寻仓反馈到线上
     * <AUTHOR>
     * @date 2019-06-27 2019-06-27
     */
    public ValueHolderV14 feedbackDeliveryResult(String jitWarehouseCode, IpJitxDeliveryRelation deliveryRelation, User operateUser) {
        ValueHolderV14<String> v14 = new ValueHolderV14<String>();
        Integer venderId = Integer.valueOf(deliveryRelation.getJitxDelivery().getVendorId());
        String sellerNick = deliveryRelation.getJitxDelivery().getSellernick();
        String order_sn = deliveryRelation.getOrderNo();
        //创建请求实体
        VipJitxFeedBackDeliveryResultModel deliveryModel = buildFeedbackDeliveryResultList(jitWarehouseCode, operateUser, venderId, sellerNick, order_sn);
        // jitx订单日志对象
        JitxOrderLog jitxOrderLog = new JitxOrderLog();
        jitxOrderLog.setBillId(deliveryRelation.getOrderId());
        jitxOrderLog.setBillNo(order_sn);
        // 固定  5
        jitxOrderLog.setBillType(5);
        // 固定 220
        jitxOrderLog.setLogType(220);
        jitxOrderLog.setParam(" 寻仓反馈，order_sn: " + order_sn);
        // 调用寻仓反馈结果RPC
        try {
            ValueHolderV14<JSONArray> vh = ipRpcService.feedBackDelivery(deliveryModel);
            if (vh != null) {
                if (vh.getCode() == ResultCode.SUCCESS) {
                    JSONArray feedBackResult = vh.getData();
                    List<? extends FeedbackDeliveryResponse> feedBackResultList = JSON.parseArray(JSON.toJSONString(feedBackResult), FeedbackDeliveryResponse.class);
                    if (CollectionUtils.isNotEmpty(feedBackResultList)) {
                        FeedbackDeliveryResponse result = feedBackResultList.get(0);
                        updateJitxDeliveryFeedBackStatus(order_sn, result.getCode(), result.getMsg(), jitWarehouseCode);
                        v14.setCode(ResultCode.SUCCESS);
                        v14.setMessage("寻仓反馈成功！");
                    }
                } else {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("调用云枢纽寻仓反馈接口异常:" + vh.getMessage());
                }
                jitxOrderLog.setMessage(vh.getMessage());
            } else {
                log.error(this.getClass().getName() + " 调用云枢纽寻仓反馈结果vh为空！");
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("调用云枢纽寻仓反馈结果vh为空！");

                jitxOrderLog.setMessage(v14.getMessage());
            }
        } catch (Exception e) {
            jitxOrderLog.setMessage(e.toString());
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("寻仓返回异常！" + e.getMessage());
        } finally {
            // 添加jit日志
            try {
                vipComRpcService.addLog(jitxOrderLog);
            } catch (Exception e) {
                log.error(" call vip add log error.{}", e.toString());
            }
        }
        return v14;
    }


    /**
     * @param deliveryRelation 订单对象
     * @return
     * @Description 寻仓反馈到线上
     * <AUTHOR>
     * @date 2019-06-27 2019-06-27
     */
    public ValueHolderV14 feedbackDeliveryFailResult(IpJitxDeliveryRelation deliveryRelation, User operateUser) {
        ValueHolderV14<String> v14 = new ValueHolderV14<String>();
        Integer venderId = Integer.valueOf(deliveryRelation.getJitxDelivery().getVendorId());
        String sellerNick = deliveryRelation.getJitxDelivery().getSellernick();
        String order_sn = deliveryRelation.getOrderNo();
        //创建请求实体
        VipJitxFeedBackDeliveryResultModel deliveryModel = buildFeedbackDeliveryFailResultList(operateUser, venderId, sellerNick, order_sn);
        // jitx订单日志对象
        JitxOrderLog jitxOrderLog = new JitxOrderLog();
        jitxOrderLog.setBillId(deliveryRelation.getOrderId());
        jitxOrderLog.setBillNo(order_sn);
        // 固定  5
        jitxOrderLog.setBillType(5);
        // 固定 220
        jitxOrderLog.setLogType(220);
        jitxOrderLog.setParam(" 寻仓反馈，order_sn: " + order_sn);
        // 调用寻仓反馈结果RPC
        try {
            ValueHolderV14<JSONArray> vh = ipRpcService.feedBackDelivery(deliveryModel);
            if (vh != null) {
                if (vh.getCode() == ResultCode.SUCCESS) {
                    JSONArray feedBackResult = vh.getData();
                    FeedbackDeliveryResponse feedbackDeliveryResult1 = new FeedbackDeliveryResponse();
                    List<? extends FeedbackDeliveryResponse> feedBackResultList = JSONArray.parseArray(JSON.toJSONString(feedBackResult), feedbackDeliveryResult1.getClass());
                    if (CollectionUtils.isNotEmpty(feedBackResultList)) {
                        FeedbackDeliveryResponse result = feedBackResultList.get(0);
                        updateJitxDeliveryFeedBackStatus(order_sn, result.getCode(), result.getMsg(), null);
                        v14.setCode(ResultCode.SUCCESS);
                        v14.setMessage("寻仓反馈成功！");
                    }
                } else {
                    v14.setCode(ResultCode.FAIL);
                    v14.setMessage("调用云枢纽寻仓反馈接口异常:" + vh.getMessage());

                }
                jitxOrderLog.setMessage(vh.getMessage());
            } else {
                log.error(this.getClass().getName() + " 调用云枢纽寻仓反馈结果vh为空！");
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("调用云枢纽寻仓反馈结果vh为空！");

                jitxOrderLog.setMessage(v14.getMessage());
            }
        } catch (Exception e) {
            jitxOrderLog.setMessage(e.toString());
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("调用云枢纽寻仓反馈失败结果异常！" + e.getMessage());
        } finally {
            // 添加jit日志
            try {
                vipComRpcService.addLog(jitxOrderLog);
            } catch (Exception e) {
                log.error(" call vip add log error.{}", e.toString());
            }
        }
        return v14;
    }


    /**
     * @param jitWarehouseCode 平台仓库编码
     * @param operateUser      操作用户
     * @param venderId         供应商编码
     * @param sellerNick       卖家昵称
     * @param order_sn         订单号
     * @return
     * @Description 创建寻仓反馈请求实体
     * <AUTHOR>
     * @date 2019-09-03 2019-09-03
     */
    private VipJitxFeedBackDeliveryResultModel buildFeedbackDeliveryResultList(String jitWarehouseCode, User operateUser, Integer venderId, String sellerNick, String order_sn) {

        List<VipJitxFeedBackDeliveryResultModel.FeedbackDeliveryResult> results = new ArrayList<>();
        VipJitxFeedBackDeliveryResultModel.FeedbackDeliveryResult feedbackDeliveryResult = new VipJitxFeedBackDeliveryResultModel.FeedbackDeliveryResult();
        feedbackDeliveryResult.setFeedback_status(FeedBackStatus.SUCCESS.toString());
        feedbackDeliveryResult.setOrder_sn(order_sn);
        feedbackDeliveryResult.setWarehouse(jitWarehouseCode);
        results.add(feedbackDeliveryResult);
        VipJitxFeedBackDeliveryResultModel deliveryModel = new VipJitxFeedBackDeliveryResultModel();
        deliveryModel = new VipJitxFeedBackDeliveryResultModel();
        deliveryModel.setSellerNick(sellerNick);
        deliveryModel.setVendor_id(venderId);
        deliveryModel.setResults(results);
        deliveryModel.setOperateUser(operateUser);

        return deliveryModel;

    }

    /**
     * @param operateUser 操作用户
     * @param venderId    供应商编码
     * @param sellerNick  卖家昵称
     * @param order_sn    订单号
     * @return
     * @Description 创建寻仓失败反馈请求实体
     * <AUTHOR>
     * @date 2020-06-11 14:10
     */
    private VipJitxFeedBackDeliveryResultModel buildFeedbackDeliveryFailResultList(User operateUser, Integer venderId, String sellerNick, String order_sn) {

        List<VipJitxFeedBackDeliveryResultModel.FeedbackDeliveryResult> results = new ArrayList<>();
        VipJitxFeedBackDeliveryResultModel.FeedbackDeliveryResult feedbackDeliveryResult = new VipJitxFeedBackDeliveryResultModel.FeedbackDeliveryResult();
        feedbackDeliveryResult.setFeedback_status(FeedBackStatus.FAIL.toString());
        feedbackDeliveryResult.setOrder_sn(order_sn);
        results.add(feedbackDeliveryResult);
        VipJitxFeedBackDeliveryResultModel deliveryModel = new VipJitxFeedBackDeliveryResultModel();
        deliveryModel = new VipJitxFeedBackDeliveryResultModel();
        deliveryModel.setSellerNick(sellerNick);
        deliveryModel.setVendor_id(venderId);
        deliveryModel.setResults(results);
        deliveryModel.setOperateUser(operateUser);

        return deliveryModel;

    }

//    private ValueHolderV14<String> generateJitWareHouseCode(Long phyWareHouseId, String[] availableWares) {
//        ValueHolderV14<String> v14 = new ValueHolderV14<String>();
//
//        if (phyWareHouseId != null) {
//            //查询实体仓档案
//            com.jackrain.nea.cpext.model.table.CpCPhyWarehouse phyWarehouse = cpRpcService.checkWarehouse(phyWareHouseId);
//            if (phyWarehouse == null) {
//                log.error(this.getClass().getName() + " 根据实体仓Id查询实体仓档案RPC-CpRpcService.checkWarehouse，实体仓为空！");
//                v14.setCode(ResultCode.FAIL);
//                v14.setMessage("根据实体仓Id查询实体仓档案RPC-CpRpcService.checkWarehouse，实体仓为空！");
//            }
//            if (phyWarehouse.getJitWarehouseEcode() == null) {
//                log.error(this.getClass().getName() + " 实体仓" + phyWareHouseId + "对应线上JITX仓库编码为空！");
//                v14.setCode(ResultCode.FAIL);
//                v14.setMessage("实体仓" + phyWareHouseId + "对应线上JITX仓库编码为空！");
//            } else {
//                VipJitxFeedBackDeliveryResultModel.FeedbackDeliveryResult feedbackDeliveryResult = new VipJitxFeedBackDeliveryResultModel.FeedbackDeliveryResult();
//                boolean isExists = isAvailableWareHouseExists(availableWares, phyWarehouse.getJitWarehouseEcode());
//                if (isExists) {
//                    v14.setCode(ResultCode.SUCCESS);
//                    v14.setData(phyWarehouse.getJitWarehouseEcode());
//                } else {
//                    v14.setCode(ResultCode.FAIL);
//                    v14.setMessage("实体仓不在可用仓库列表");
//                }
//            }
//        } else {
//            v14.setCode(ResultCode.FAIL);
//            v14.setMessage("实体仓为空");
//        }
//        return v14;
//    }

    /**
     * @param availableWares
     * @param wareHouseCode
     * @return
     * @Description 判断匹配仓是否在可用仓列表
     * <AUTHOR>
     * @date 2019-08-21 2019-08-21
     */
    public ValueHolderV14 isAvailableWareHouseExists(List<String> availableWares, String wareHouseCode) {
        ValueHolderV14 v14 = new ValueHolderV14();
        boolean isExists = false;
        if (CollectionUtils.isEmpty(availableWares)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("可用仓库列表为空！");
            return v14;
        }
        for (String ware : availableWares) {
            if (ware.equals(wareHouseCode)) {
                isExists = true;
                break;
            }
        }
        if (isExists) {
            v14.setCode(ResultCode.SUCCESS);
        } else {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("实体仓不在可用仓库列表");
        }
        return v14;
    }
    //----------------------------------------------------占仓业务逻辑-----------------------------------------------------

    /**
     * 返回存在库存的实体仓
     *
     * @param deliveryRelation 订单对象
     * @param warePhyHouseIds  实体仓集合
     * @return List<Long>
     */
    public Long checkWareHouseList(IpJitxDeliveryRelation deliveryRelation, List<Long> warePhyHouseIds) {
        //取出订单明细条码Code
        List<String> skuCodeList = new ArrayList<>();
        List<IpBJitxDeliveryItemEx> deliveryItemList = deliveryRelation.getJitxDeliveryItemList();
        for (IpBJitxDeliveryItemEx deliveryItem : deliveryItemList) {
            if (deliveryItem.getProdSku() != null) {
                skuCodeList.add(deliveryItem.getProdSku().getSkuEcode());
            }
        }
        //去重明细skuCode
        List<String> distinctList = skuCodeList.stream().distinct().collect(Collectors.toList());
        log.debug("寻仓订单OrderId" + deliveryRelation.getOrderId() + "打印明细集合skuCodeList" + distinctList);
        //筛选实体仓
        ValueHolderV14 holderV14 = sgRpcService.queryJitxSendWareHouseStock(deliveryRelation);
        //库存中心实体仓的返回结果
        if (log.isDebugEnabled()) {
            log.debug("OrderId[" + deliveryRelation.getOrderId() + "],占单分仓服务查询库存服务出参:" + holderV14.toJSONObject().toJSONString() + ";");
        }
        //解析数据
        List<SgSumStorageQueryResult> storageQueryResults = (List<SgSumStorageQueryResult>) holderV14.getData();
        if (log.isDebugEnabled()) {
            log.debug("OrderId[" + deliveryRelation.getOrderId() + "],占单分仓服务获取data:" + JSONObject.toJSONString(storageQueryResults) + ";");
        }
        //库存返回没有数据的情况
        if (CollectionUtils.isEmpty(storageQueryResults)) {
            return null;
        }
        return compareStock(deliveryRelation, distinctList, storageQueryResults, deliveryItemList, warePhyHouseIds);
    }

    /**
     * 比较sku数量比较得出符合库存数的逻辑仓
     *
     * @param deliveryRelation       店铺Id
     * @param distinctList           去重的明细sku集合
     * @param storageQueryResultList 实体仓库存集合
     * @param deliveryItemList       明细集合
     * @param phyWareHouseIds        全部实体仓
     * @return List<Long>
     */
    private Long compareStock(IpJitxDeliveryRelation deliveryRelation, List<String> distinctList, List<SgSumStorageQueryResult> storageQueryResultList, List<IpBJitxDeliveryItemEx> deliveryItemList, List<Long> phyWareHouseIds) {

        //将skuCode 和商品购买数量存在Map集合里面
        Map<String, BigDecimal> stringListMap = new HashMap<>();
        for (IpBJitxDeliveryItemEx jitxDeliveryItem : deliveryItemList) {
            //取出明细购买数量 相同skuCode数量累加
            BigDecimal qty = (jitxDeliveryItem.getQuantity() == null ? BigDecimal.ZERO : BigDecimal.valueOf(jitxDeliveryItem.getQuantity()));
            //避免出现不同明细相同sku,若出现,计算累加和
            if (stringListMap.containsKey(jitxDeliveryItem.getProdSku().getSkuEcode())) {
                stringListMap.put(jitxDeliveryItem.getProdSku().getSkuEcode(), qty
                        .add(stringListMap.get(jitxDeliveryItem.getProdSku().getSkuEcode())));
            } else {
                stringListMap.put(jitxDeliveryItem.getProdSku().getSkuEcode(), qty);
            }
        }
        log.debug("寻仓订单OrderId" + deliveryRelation.getOrderId() + "打印明细stringListMap" + stringListMap);
        //满足库存的临时对象
        List<WareHouseResult> wareHouseResultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(storageQueryResultList)) {
            //逻辑仓对应一个skuCode和可用数量
            for (SgSumStorageQueryResult sgBStorage : storageQueryResultList) {
                BigDecimal qtyAvailable = (sgBStorage.getQtyAvailable() == null ? BigDecimal.ZERO : sgBStorage.getQtyAvailable());
                //将符合条件的逻辑仓都存到集合中  [sku相同  购买数量<库存数量]
                if ((stringListMap.containsKey(sgBStorage.getPsCSkuEcode())) && (stringListMap.get(sgBStorage.getPsCSkuEcode()).compareTo(qtyAvailable) <= 0)) {
                    wareHouseResultList.add(new WareHouseResult(sgBStorage.getPsCSkuEcode(), sgBStorage.getCpCPhyWarehouseId()));
                }
            }
        }
        log.debug("寻仓订单OrderId" + deliveryRelation.getOrderId() + "聚合的list" + wareHouseResultList);
        //转换计算将相同实体仓的满足sku库存的聚合起来
        Map<Long, StringBuffer> wareHouseListMap = new HashMap<>();
        for (WareHouseResult wareHouseResult : wareHouseResultList) {
            StringBuffer stringBuffer = new StringBuffer();
            if (wareHouseListMap.containsKey(wareHouseResult.getWareHosueId())) {
                wareHouseListMap.put(wareHouseResult.getWareHosueId(), wareHouseListMap.get(wareHouseResult.getWareHosueId()).append(",").append(wareHouseResult.getSkuCode()));
            } else {
                wareHouseListMap.put(wareHouseResult.getWareHosueId(), stringBuffer.append(wareHouseResult.getSkuCode()));
            }
        }
        log.debug("寻仓订单OrderId" + deliveryRelation.getOrderId() + "聚合计算的wareHouseListMap" + wareHouseListMap);
        List<Long> wareHouseIds = new ArrayList<>();
        //得到key集合
        for (Long key : wareHouseListMap.keySet()) {
            StringBuffer skuCodeString = wareHouseListMap.get(key);
            log.debug(key + " _ " + skuCodeString);
            //判断当前实体发货仓库的明细sku是否都满足库存
            boolean reulst = checkContain(skuCodeString, distinctList);
            log.debug("寻仓订单OrderId" + deliveryRelation.getOrderId() + "实体仓" + key + "_是否都满足库存" + reulst);
            if (reulst) {
                wareHouseIds.add(key);
            }
        }
        //聚合过得实体仓去重
        List<Long> secondWareHouseIds = wareHouseIds.stream().distinct().collect(Collectors.toList());
        log.debug("寻仓订单OrderId" + deliveryRelation.getOrderId() + "_最终得到的实体仓" + secondWareHouseIds);
        if (CollectionUtils.isEmpty(secondWareHouseIds)) {
            //都不满足库存
            return null;
        } else {
            if (secondWareHouseIds.size() == 1) {
                return secondWareHouseIds.get(0);
            } else {
                //多条满足库存按实体仓优先级取优先级最高
                List<Long> priorityPhyWareHouseIds = sortPhyWarehouse(deliveryRelation.getJitxDelivery().getCpCShopId());
                //返回优先级最高的整包库存符合实体仓
                return selectMaxPriorityPhyWareHouse(priorityPhyWareHouseIds, secondWareHouseIds);
            }
        }
    }

    /**
     * 判断是否全部包含明细sku
     *
     * @param skuCodeString 符合的sku
     * @param distinctList  本身明细
     * @return boolean
     */
    public boolean checkContain(StringBuffer skuCodeString, List<String> distinctList) {

        List<String> skuCodeTempList = new ArrayList<>();
        for (String retval : skuCodeString.toString().split(",")) {
            skuCodeTempList.add(retval);
        }
        List<String> secondSkuCode = skuCodeTempList.stream().distinct().collect(Collectors.toList());
        log.debug("skuCodeTempList" + skuCodeTempList);
        if ((secondSkuCode.size() == distinctList.size()) && (secondSkuCode.containsAll(distinctList))) {
            return true;
        }
        return false;
    }

    /**
     * 按逻辑仓优先级排序实体仓
     *
     * @param cpCShop
     * @return 返回按优先级实体仓排序列表
     */
    private List<Long> sortPhyWarehouse(Long cpCShop) {
        HashMap<Long, CpCStore> hashMap = new HashMap<>();
        List<Long> phyWarehouseVoList = new ArrayList<>();
        //调用店铺同步库存策略接口
        List<StCSyncStockStrategyVo> stCSyncStockStrategyItemDOList = omsSyncStockStrategyService.selectSyncStockStrategyByPriority(cpCShop);
        ;
        //逻辑仓不存在
        if (CollectionUtils.isEmpty(stCSyncStockStrategyItemDOList)) {
            log.debug("调用店铺同步库存策略获取优先级逻辑仓列表出错:selectSyncStockStrategyByPriority;");
            return null;
        }
        //逻辑仓存在
        if (CollectionUtils.isNotEmpty(stCSyncStockStrategyItemDOList)) {
            List<Long> storeIdList = stCSyncStockStrategyItemDOList.stream().filter(x -> x.getCpCStoreId() != null).map(x -> x.getCpCStoreId()).collect(Collectors.toList());
            //逻辑仓为空，店铺同步库存策略保存导致逻辑仓为空
            if (CollectionUtils.isEmpty(storeIdList)) {
                return null;
            }
            StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
            storeInfoQueryRequest.setIds(storeIdList);
            try {
                hashMap = basicCpQueryService.getStoreInfo(storeInfoQueryRequest);
            } catch (Exception e) {
                log.error(this.getClass().getName() + " 逻辑仓查询实体仓basicCpQueryService.getStoreInfo出错{}", e);
                return null;
            }
            //判断逻辑仓的实体仓数量，若就一个实体仓就用这个实体仓作为发货仓
            for (Long cpCStoreId : storeIdList) {
                if (hashMap.get(cpCStoreId) != null) {
                    CpCStore cpCStore = hashMap.get(cpCStoreId);
                    phyWarehouseVoList.add(cpCStore.getCpCPhyWarehouseId());//实体仓
                }
            }
        }
        return phyWarehouseVoList;
    }

    /**
     * 返回优先级最高的整包库存符合实体仓
     *
     * @param priorityPhyWareHouseIds 优先级排序的实体仓列表
     * @param secondWareHouseIds      整包符合实体仓列表
     * @return 返回优先级最高的整包库存符合实体仓
     */
    private Long selectMaxPriorityPhyWareHouse(List<Long> priorityPhyWareHouseIds, List<Long> secondWareHouseIds) {
        for (Long phyWareHouseId : priorityPhyWareHouseIds) {
            List<Long> filterPhyWareHouse = secondWareHouseIds.stream().filter(x -> x.longValue() == phyWareHouseId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterPhyWareHouse)) {
                return filterPhyWareHouse.get(0);
            }
        }
        return null;
    }

    /**
     * 寻仓单寻源占单-发送mq方法
     * @param sgFindSourceStrategyC2SRequest
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 deliveryOccupyStockSendMq(SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest){
        ValueHolderV14 vh = new ValueHolderV14();
        log.info(LogUtil.format("寻仓单寻源占单调用库存中心request:{}",
                "IpJitxDeliveryService.deliveryOccupyStockSendMq",sgFindSourceStrategyC2SRequest.getTid()),sgFindSourceStrategyC2SRequest);

        //更新寻仓单状态为:寻仓中
        String orderNo = sgFindSourceStrategyC2SRequest.getSourceBillNo();
        updateJitxSyncStatus(orderNo,
                SyncStatus.SYNCIN, "寻仓中");
        //更新时效订单状态为：寻仓中
        ipVipTimeOrderService.updateIpBTimeOrderVipByOrderSn(orderNo, TimeOrderVipStatusEnum.IN_SEEKING_STORE.getValue(), "寻仓中！");
        try {
//            String topic = propertiesConf.getProperty("r3.oms.to.sg.tobeConfirm.topic", "BJ_UAT_R3_SG_TOBECONFIRM");
            String topic = "R3_SG_TOBECONFIRM";
//            String tag = propertiesConf.getProperty("r3.oms.to.sg.tobeConfirm.tag", "tobeConfirm");
            String tag = "OperateTobeConfirm";
            String jsonValue = JSONObject.toJSONString(sgFindSourceStrategyC2SRequest);
            MqSendResult result = defaultProducerSend.sendTopic(topic, tag, jsonValue, null);
            log.info("寻仓单寻源占单，发送mq结果{}",result);
        } catch (Exception e) {
            log.error("寻仓单寻源占单，发送Mq异常", e);
            throw new NDSException("寻仓单寻源占单，发送Mq异常");
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(sgFindSourceStrategyC2SRequest.getSourceBillNo() + "寻仓单寻源占单mq发送成功!");
        return vh;
    }

    /**
     * 寻仓单虚拟寻源占单-发送mq方法
     * @param sgFindSourceStrategyC2SRequest
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 deliveryVirtualOccupyStockSendMq(SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest,List<String> orderSnList){
        ValueHolderV14 vh = new ValueHolderV14();
        log.info(LogUtil.format("寻仓单虚拟寻源调用库存中心request:{}",
                "IpJitxDeliveryService.deliveryVirtualOccupyStockSendMq",sgFindSourceStrategyC2SRequest.getTid()),sgFindSourceStrategyC2SRequest);
        //更新寻仓单 虚拟占单中
        ipBJitxDeliveryMapper.batchuUpdateDeliveryList(orderSnList,SyncStatus.IN_VIRTUAL_OCCUPY.toInteger(),new Date(),"虚拟寻仓中");
        try {
//            String topic = propertiesConf.getProperty("r3.oms.to.sg.tobeConfirm.topic", "BJ_UAT_R3_SG_TOBECONFIRM");
            String topic = Mq5Constants.TOPIC_R3_SG_TOBECONFIRM;;
//            String tag = propertiesConf.getProperty("r3.oms.to.sg.tobeConfirm.tag", "tobeConfirm");
            String tag = Mq5Constants.TAG_R3_SG_TOBECONFIRM;
            String jsonValue = JSONObject.toJSONString(sgFindSourceStrategyC2SRequest);
            MqSendResult result = defaultProducerSend.sendTopic(topic, tag, jsonValue, null);
            log.info(LogUtil.format("寻仓单虚拟寻源调用库存中心发送MQ结果result:{}",
                    "IpJitxDeliveryService.deliveryVirtualOccupyStockSendMq",sgFindSourceStrategyC2SRequest.getTid()),result);
        } catch (Exception e) {
            log.error(LogUtil.format("寻仓单虚拟寻源调用库存中心发送MQ异常:{}","IpJitxDeliveryService.deliveryVirtualOccupyStockSendMq",sgFindSourceStrategyC2SRequest.getTid()),
                    Throwables.getStackTraceAsString(e));
            throw new NDSException("寻仓单虚拟寻源占单，发送Mq异常");
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage(sgFindSourceStrategyC2SRequest.getSourceBillNo() + "寻仓单虚拟寻源占单mq发送成功!");
        return vh;
    }

    /**
     * 寻仓单占单请求对象
     * @param deliveryInfo
     * @param operateUser
     * @return
     */
    public SgFindSourceStrategyC2SRequest buildDeliveryOccupyStockRequest(IpJitxDeliveryRelation deliveryInfo,User operateUser){
        IpBJitxDelivery ipBJitxDelivery = deliveryInfo.getJitxDelivery();
        /**平台单号*/
        String orderSn = ipBJitxDelivery.getOrderSn();
        Long shopId = ipBJitxDelivery.getCpCShopId();
        List<IpVipTimeOrderRelation> ipVipTimeOrderRelationList = deliveryInfo.getIpVipTimeOrderRelationList();
        IpVipTimeOrderRelation ipVipTimeOrderRelation = ipVipTimeOrderRelationList.get(0);
        IpBTimeOrderVip ipBTimeOrderVip = ipVipTimeOrderRelation.getIpBTimeOrderVip();

        SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest = new SgFindSourceStrategyC2SRequest();
        sgFindSourceStrategyC2SRequest.setSourceBillId(ipBJitxDelivery.getId());
        sgFindSourceStrategyC2SRequest.setSourceBillNo(orderSn);
        sgFindSourceStrategyC2SRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_FOR_WAREHOUSE);
        sgFindSourceStrategyC2SRequest.setBillDate(ipBJitxDelivery.getCreationdate());
        sgFindSourceStrategyC2SRequest.setTid(orderSn);
        sgFindSourceStrategyC2SRequest.setShopId(shopId);
        sgFindSourceStrategyC2SRequest.setExcludeWarehouseEcode(deliveryInfo.getExcludeWarehouseCode());
        //占单类型-店铺策略配置
        StCShopStrategyDO stCShopStrategyDO= omsStCShopStrategyService.selectOcStCShopStrategy(shopId);
        if(stCShopStrategyDO != null){
            log.info("{}.寻仓单，占单类型为{}",orderSn,stCShopStrategyDO.getOccupyType());
            sgFindSourceStrategyC2SRequest.setSplitType(stCShopStrategyDO.getOccupyType());
        }else{
            log.error("寻仓单{},店铺{}对应店铺策略配置不存在",orderSn,shopId);
        }
        //赋值省市区
        //根据地址编码获取省市区名称
        String addressCode = ipBTimeOrderVip.getAddressCode();
        //获取唯品会平台仓库编码
        String jitWareHouseCode = ipBTimeOrderVip.getSaleWarehouse();
        boolean isProvince = false;
        if(StringUtils.isNotEmpty(addressCode)){
            //根据地址编码查询唯品会对应的省市区名称
            TOmsvipfulladdress tOmsvipfulladdress = cpRpcService.queryProvinceCityAreaNameByCode(addressCode);
            if(tOmsvipfulladdress != null){
                String provinceName = tOmsvipfulladdress.getProvinceName();
                String cityName = tOmsvipfulladdress.getCityName();
                String areaName = tOmsvipfulladdress.getCountyName();
                CpCRegionRelation cpCRegionRelation = cpRpcService.selectRegionRelationByProvinceCityArea(provinceName,cityName,areaName);
                if(cpCRegionRelation != null){
                    isProvince = true;
                    sgFindSourceStrategyC2SRequest.setProvinceId(cpCRegionRelation.getCpCRegionProvinceId());
                    sgFindSourceStrategyC2SRequest.setCityId(cpCRegionRelation.getCpCRegionCityId());
                    sgFindSourceStrategyC2SRequest.setAreaId(cpCRegionRelation.getCpCRegionAreaId());
                }
            }
        }
        if(!isProvince){
            //通过仓库编码查询JIT仓库信息，并赋值省市区id
            CpCVipcomWahouse cpCVipcomWahouse = cpRpcService.selectByCode(jitWareHouseCode);
            if (null == cpCVipcomWahouse) {
                log.error("寻仓单{},销售区域{}对应JIT仓库信息不存在",orderSn,jitWareHouseCode);
            }else{
                //根据唯品会仓库编码，查询仓库对应的省市区
                sgFindSourceStrategyC2SRequest.setProvinceId(cpCVipcomWahouse.getCpCRegionProvinceId());
                sgFindSourceStrategyC2SRequest.setCityId(cpCVipcomWahouse.getCpCRegionCityId());
                sgFindSourceStrategyC2SRequest.setAreaId(cpCVipcomWahouse.getCpCRegionAreaId());
            }
        }
        return sgFindSourceStrategyC2SRequest;
    }


    /**
     *  寻仓单占单请求明细对象
     * @param deliveryInfo
     * @return
     */
    public List<SkuItemC2S> buildDeliveryOccupyStockRequestItem(IpJitxDeliveryRelation deliveryInfo){
        List<SkuItemC2S> skuItemC2SList = new ArrayList<>();
        String orderNo = deliveryInfo.getOrderNo();
        List<IpBJitxDeliveryItemEx> jitxDeliveryItemList = deliveryInfo.getJitxDeliveryItemList();
        List<IpVipTimeOrderRelation> vipTimeOrderRelationList = deliveryInfo.getIpVipTimeOrderRelationList();
        List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipOccupyItemList = new ArrayList<>();
        List<IpBTimeOrderVip> ipBTimeOrderVips = new ArrayList<>();
        for(IpVipTimeOrderRelation ipVipTimeOrderRelation:vipTimeOrderRelationList){
            ipBTimeOrderVipOccupyItemList.addAll(ipVipTimeOrderRelation.getIpBTimeOrderVipOccupyItemList());
            ipBTimeOrderVips.add(ipVipTimeOrderRelation.getIpBTimeOrderVip());
        }
        //根据商品条码分组
        Map<String, List<IpBTimeOrderVipOccupyItem>> ipBTimeOrderVipOccupyItemMap = ipBTimeOrderVipOccupyItemList.stream()
                .collect(Collectors.groupingBy(IpBTimeOrderVipOccupyItem::getBarcode));

        for(IpBJitxDeliveryItemEx ipBJitxDeliveryItemEx:jitxDeliveryItemList){
            SkuItemC2S skuItemC2S = new SkuItemC2S();
            //时效订单占用明细
            List<IpBTimeOrderVipOccupyItem> occupyItemList = ipBTimeOrderVipOccupyItemMap.get(ipBJitxDeliveryItemEx.getBarcode());
            if(CollectionUtils.isEmpty(occupyItemList)){
                log.error("寻仓单 单号:"+orderNo+"对应的时效订单占用明细不存在 商品条码:"+ipBJitxDeliveryItemEx.getBarcode());
                continue;
            }
            IpBTimeOrderVipOccupyItem occupyItem = occupyItemList.get(0);
            //对应时效订单
            Map<Long, List<IpBTimeOrderVip>> ipBTimeOrderVipMap = ipBTimeOrderVips.stream()
                    .collect(Collectors.groupingBy(IpBTimeOrderVip::getId));
            IpBTimeOrderVip timeOrderVip = ipBTimeOrderVipMap.get(occupyItem.getIpBTimeOrderVipId()).get(0);

            skuItemC2S.setSourceItemId(ipBJitxDeliveryItemEx.getId());
            skuItemC2S.setTimeOrderId(occupyItem.getIpBTimeOrderVipId());
            skuItemC2S.setTimeOrderNo(timeOrderVip.getOccupiedOrderSn());
            skuItemC2S.setTimeOrderItemId(occupyItem.getId());
            skuItemC2S.setPsCSkuId(occupyItem.getPsCSkuId());
            skuItemC2S.setProType(StrategyConstants.PRO_TYPE_NORMAL);
            skuItemC2S.setQty(new BigDecimal(ipBJitxDeliveryItemEx.getQuantity()));

            skuItemC2SList.add(skuItemC2S);
        }
        return skuItemC2SList;
    }

    /**
     *  寻仓单虚拟占单请求明细对象
     * @param deliveryInfo
     * @return
     */
    public List<SkuItemC2S> buildDeliveryVirtualOccupyStockRequestItem(IpJitxDeliveryRelation deliveryInfo){
        List<SkuItemC2S> skuItemC2SList = new ArrayList<>();
        List<IpJitxDeliveryRelation> deliveryRelationList = deliveryInfo.getIpJitxDeliveryRelation();
        List<IpVipTimeOrderRelation> vipTimeOrderRelationList = deliveryInfo.getIpVipTimeOrderRelationList();

        List<IpBJitxDeliveryItemEx> jitxDeliveryItemList = new ArrayList<>();
        for(IpJitxDeliveryRelation deliveryRelation:deliveryRelationList){
            jitxDeliveryItemList.addAll(deliveryRelation.getJitxDeliveryItemList());
        }

        Map<String,List<IpVipTimeOrderRelation>> timeOrderRelationMap = vipTimeOrderRelationList.stream()
                .collect(Collectors.groupingBy(IpVipTimeOrderRelation::getOrderSn));

        for(IpJitxDeliveryRelation relation:deliveryRelationList){
            SkuItemC2S skuItemC2S = new SkuItemC2S();
            String orderSn = relation.getOrderNo();
            List<IpBJitxDeliveryItemEx> ItemList = relation.getJitxDeliveryItemList();
            //对应时效单数据
            List<IpVipTimeOrderRelation> timeOrderRelations = timeOrderRelationMap.get(relation.getOrderNo());
            if(CollectionUtils.isEmpty(timeOrderRelations)){
                log.error(LogUtil.format("寻仓单orderSn:{}虚拟占单对应的时效订单不存在:{}","buildDeliveryVirtualOccupyStockRequestItem"),
                        orderSn);
                throw new NDSException("寻仓单orderSn:"+orderSn+"虚拟占单对应的时效订单不存在");
            }
            //时效单主表
            IpBTimeOrderVip ipBTimeOrderVip = timeOrderRelations.get(0).getIpBTimeOrderVip();
            if(ipBTimeOrderVip == null){
                log.error(LogUtil.format("寻仓单orderSn:{}虚拟占单对应的时效订单主表信息为空:{}","buildDeliveryVirtualOccupyStockRequestItem"),
                        orderSn);
                throw new NDSException("寻仓单orderSn:"+orderSn+"虚拟占单对应的时效订单主表信息为空");
            }
            //如果时效单状态为取消或者已完成，则不进行虚拟寻源
            Integer status = ipBTimeOrderVip.getStatus();
            if(TimeOrderVipStatusEnum.CANCELLED.getValue().equals(status) || TimeOrderVipStatusEnum.MATCHED.getValue().equals(status)){
                continue;
            }

            //时效单占用明细
            List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipOccupyItemList = timeOrderRelations.get(0).getIpBTimeOrderVipOccupyItemList();
            if(CollectionUtils.isEmpty(ipBTimeOrderVipOccupyItemList)){
                log.error(LogUtil.format("寻仓单orderSn:{}虚拟占单对应的时效订单占用明细为空:{}","buildDeliveryVirtualOccupyStockRequestItem"),
                        orderSn);
                throw new NDSException("寻仓单orderSn:"+orderSn+"虚拟占单对应的时效订单占用明细为空");
            }
            IpBTimeOrderVipOccupyItem occupyItem = ipBTimeOrderVipOccupyItemList.get(0);

            skuItemC2S.setSourceItemId(ItemList.get(0).getId());
            skuItemC2S.setTimeOrderId(occupyItem.getIpBTimeOrderVipId());
            skuItemC2S.setTimeOrderNo(ipBTimeOrderVip.getOccupiedOrderSn());
            skuItemC2S.setTimeOrderItemId(occupyItem.getId());
            skuItemC2S.setPsCSkuId(occupyItem.getPsCSkuId());
            skuItemC2S.setProType(StrategyConstants.PRO_TYPE_NORMAL);
            skuItemC2S.setQty(new BigDecimal(ItemList.get(0).getQuantity()));
            skuItemC2SList.add(skuItemC2S);
        }
        return skuItemC2SList;
    }


    /**
     * 更新寻仓单 是否可用为：否
     * @param orderNo
     * @param remarks
     */
    public void updateJitxDeliveryIsactiveN(String orderNo,String remarks) {
        if (remarks != null && remarks.length() > IpBJitxDeliveryMapper.MAX_REMARK_LENGTH) {
            remarks = remarks.substring(0, IpBJitxDeliveryMapper.MAX_REMARK_LENGTH - 1);
        }
        IpBJitxDelivery ipBJitxDelivery = new IpBJitxDelivery();
        ipBJitxDelivery.setIsactive("N");
        ipBJitxDelivery.setSysremark(remarks);
        ipBJitxDelivery.setModifieddate(new Date());
        QueryWrapper<IpBJitxDelivery> wrapper = new QueryWrapper<>();
        wrapper.eq("order_sn", orderNo);
        int result = this.ipBJitxDeliveryMapper.update(ipBJitxDelivery, wrapper);
    }

    /**
     * 寻仓反馈补偿任务
     * @param deliveryRelation
     * @param operateUser
     * @return
     */
    public ValueHolderV14 deliveryFeedbackResultToVip(IpJitxDeliveryRelation deliveryRelation,User operateUser){
        ValueHolderV14 v14 = ValueHolderV14Utils.getSuccessValueHolder("反馈成功！");
        String orderSn = deliveryRelation.getOrderNo();
        //根据店铺ID查询店铺策略，获取默认逻辑仓
        Long shopId = deliveryRelation.getJitxDelivery().getCpCShopId();
        StCShopStrategyDO stCShopStrategyDO = stRpcService.selectOcStCShopStrategyByCpCshopId(shopId);
        Long defaultStoreId = stCShopStrategyDO.getDefaultStoreId();

        /**平台仓库编码*/
        StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseService.queryJitxCapacity(shopId, defaultStoreId, null);
        if (jitxWarehouse == null) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("实体仓没有在JITX仓库对照表中维护JITX仓库编码,寻仓单号:{},店铺id:{},实体仓:{}",
                        "IpJitxDeliveryService.deliveryFeedbackResultToVip",orderSn,shopId,defaultStoreId),orderSn,shopId,defaultStoreId);
            }
            return ValueHolderV14Utils.getFailValueHolder("唯品会仓库对照表查询为空");
        } else {
            //门店编码
            String vipcomWarehouseEcode = jitxWarehouse.getVipcomWarehouseEcode();
            ValueHolderV14 v1 = feedbackDeliveryResult(vipcomWarehouseEcode, deliveryRelation, operateUser);
            log.info(LogUtil.format("寻仓单反馈平台orderSn:{},response:{}",
                    "IpJitxDeliveryService.deliveryFeedbackResultToVip",orderSn),orderSn,JSON.toJSONString(v1));
            if (v1.getCode() == ResultCode.FAIL) {
                updateJitxSyncStatus(orderSn, SyncStatus.EXCEPTION, v1.getMessage());
                return v1;
            } else {
                updateJitxSyncStatus(orderSn, SyncStatus.SYNCSUCCESS, "寻仓反馈完成，结束流程");
            }
        }
        return v14;
    }

    /**
     * 所有时效单、寻仓单明细数据
     * @param jitxDeliveryRelation
     * @return
     */
    public IpJitxDeliveryRelation mergeJitxDelivery(IpJitxDeliveryRelation jitxDeliveryRelation) {
        jitxDeliveryRelation.setStoreJitx(false);
        IpBJitxDelivery orderInfo = jitxDeliveryRelation.getJitxDelivery();
        String orderNo = jitxDeliveryRelation.getOrderNo();
        //增加根单号寻源开关
        if(!virtualOccupyIsopen){
            return jitxDeliveryRelation;
        }
        //非店发则不进行虚拟寻源
        Integer isStoreDelivery = orderInfo.getIsStoreDelivery();
        if(VipConstant.JITX_DELIVERY_IS_STORE_DELIVERY_N.equals(isStoreDelivery)){
            return jitxDeliveryRelation;
        }
        //如果虚拟寻源过，则不再进行虚拟寻源
        IpVipTimeOrderRelation timeOrderRelation = jitxDeliveryRelation.getIpVipTimeOrderRelationList().get(0);
        Integer reserveBigint01 = timeOrderRelation.getIpBTimeOrderVip().getReserveBigint01();
        if(VipConstant.JITX_DELIVERY_IS_VIRTUAL_OCCUPY_02.equals(reserveBigint01)){
            jitxDeliveryRelation.setIsVirtualOccupy(VipConstant.JITX_DELIVERY_IS_VIRTUAL_OCCUPY_02);
            return jitxDeliveryRelation;
        }

        String rootOrderSn = timeOrderRelation.getRootOrderSn();
        //时效单没有根单号，则不进行虚拟寻源
        if(StringUtils.isBlank(rootOrderSn)){
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("寻仓单orderSn:{}寻源占单查询时效单根单号为空",
                        "IpJitxDeliveryService.mergeJitxDelivery",orderNo),orderNo);
            }
            return jitxDeliveryRelation;
        }

        //查询同批时效单
        List<IpBTimeOrderVip>  allTimeOrders  = ipBTimeOrderVipMapper.selectTimeOrderOccupiedOrderSnListByRootOrderSn(rootOrderSn);
        //排除状态为已取消的时效单
        List<IpBTimeOrderVip> allTimeOrderList = allTimeOrders.stream()
                .filter(e -> !TimeOrderVipStatusEnum.CANCELLED.getValue().equals(e.getStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(allTimeOrderList)){
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("寻仓单orderSn:{},rootOrderSn:{}寻源占单根据根单号查询非取消状态的时效单为空",
                        "IpJitxDeliveryService.mergeJitxDelivery",orderNo,rootOrderSn),orderNo,rootOrderSn);
            }
            return jitxDeliveryRelation;
        }

        //根据时效单查询同批寻仓单总数
        List<String> allOrderSnList =  allTimeOrderList.stream().map(IpBTimeOrderVip::getOrderSn).collect(Collectors.toList());
        List<IpBJitxDelivery> allDeliverys = ipBJitxDeliveryMapper.selectIsStoreJitxDeliveryByOrderNos(allOrderSnList);
        if(CollectionUtils.isEmpty(allDeliverys)){
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("寻仓单orderSn:{},rootOrderSn{}寻源占单根据时效单反查同批店发寻仓单为空allOrderSnList:{}",
                        "IpJitxDeliveryService.mergeJitxDelivery",orderNo,rootOrderSn),orderNo,rootOrderSn,allOrderSnList);
            }
            return jitxDeliveryRelation;
        }
        List<String> tatalorderSns =  allDeliverys.stream().map(IpBJitxDelivery::getOrderSn).collect(Collectors.toList());
        //设置寻仓单总数
        jitxDeliveryRelation.setDeliveryOrderCount(allDeliverys.size());
        //设置时效单总数
        List<IpBTimeOrderVip> isStoreTimeOrderList = allTimeOrderList.stream()
                .filter(e -> tatalorderSns.contains(e.getOrderSn())).collect(Collectors.toList());
        jitxDeliveryRelation.setTimeOrderCount(isStoreTimeOrderList.size());

        //获取状态为未处理的寻仓单
        List<IpBJitxDelivery> allDeliveryList = allDeliverys.stream()
                .filter(e -> SyncStatus.UNSYNC.toInteger() == e.getSynstatus().intValue()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(allDeliveryList)){
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("寻仓单orderSn:{},rootOrderSn:{}寻源占单根据时效单反查同批未处理状态寻仓单为空",
                        "IpJitxDeliveryService.mergeJitxDelivery",orderNo,rootOrderSn),orderNo,rootOrderSn);
            }
            return jitxDeliveryRelation;
        }
        jitxDeliveryRelation.setUnsyncdeliveryOrderCount(allDeliveryList.size());

        //设置时效单、寻仓单数据 开始进行虚拟寻仓
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("寻仓单orderSn:{},rootOrderSn:{}为门店JITX订单，并且存在多个未处理的寻仓单，开始进行虚拟寻仓,DeliveryOrderCount:{},TimeOrderCount:{},UnsyncdeliveryOrderCount:{}",
                    "IpJitxDeliveryService.mergeJitxDelivery",orderNo,rootOrderSn),orderNo,rootOrderSn,jitxDeliveryRelation.getDeliveryOrderCount(),jitxDeliveryRelation.getTimeOrderCount(),jitxDeliveryRelation.getUnsyncdeliveryOrderCount());
        }
        //查询未处理状态的寻仓单对应的时效单数据
        List<String> orderSns =  allDeliveryList.stream().map(IpBJitxDelivery::getOrderSn).collect(Collectors.toList());
        List<IpBTimeOrderVip> timeOrderVips = allTimeOrderList.stream()
                .filter(e -> orderSns.contains(e.getOrderSn())).collect(Collectors.toList());

        //确认为门店JITX订单 走虚拟寻源
        jitxDeliveryRelation.setStoreJitx(true);
        jitxDeliveryRelation.setUnsyncOrderSnList(orderSns);

        //查询时效单、寻仓单明细数据
        Map<Long, List<IpBJitxDeliveryItemEx>> deliveryItemExMap = new HashMap<>();
        //查询寻仓单明细数据
        List<Long> deliveryIdList =  allDeliveryList.stream().map(IpBJitxDelivery::getId).collect(Collectors.toList());
        List<IpBJitxDeliveryItemEx> deliveryItemExList =  ipBJitxDeliveryItemMapper.selectDeliveryItemListByDeliveryIdList(deliveryIdList);
        deliveryItemExMap = deliveryItemExList.stream()
                .collect(Collectors.groupingBy(IpBJitxDeliveryItemEx::getIpBJitxDeliveryId));

        List<IpJitxDeliveryRelation> allDeliveryRelationList = new ArrayList<>();
        for(IpBJitxDelivery delivery:allDeliveryList){
            IpJitxDeliveryRelation relation = new IpJitxDeliveryRelation();
            relation.setJitxDelivery(delivery);
            relation.setJitxDeliveryItemList(deliveryItemExMap.get(delivery.getId()));
            allDeliveryRelationList.add(relation);
        }
        jitxDeliveryRelation.setIpJitxDeliveryRelation(allDeliveryRelationList);

        List<Long> timeOrderIdList =  timeOrderVips.stream().map(IpBTimeOrderVip::getId).collect(Collectors.toList());
        Map<Long, List<IpBTimeOrderVipOccupyItem>> ipBTimeOrderVipItemMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(timeOrderIdList)){
            //查询时效订单占用明细
            List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipItemList =  ipBTimeOrderVipOccupyItemMapper.selectList(Wrappers.<IpBTimeOrderVipOccupyItem>lambdaQuery()
                    .eq(IpBTimeOrderVipOccupyItem::getIsactive, "Y")
                    .in(IpBTimeOrderVipOccupyItem::getIpBTimeOrderVipId,timeOrderIdList));
            ipBTimeOrderVipItemMap = ipBTimeOrderVipItemList.stream()
                    .collect(Collectors.groupingBy(IpBTimeOrderVipOccupyItem::getIpBTimeOrderVipId));
        }
        List<IpVipTimeOrderRelation> vipTimeOrderRelations = new ArrayList<>();
        for(IpBTimeOrderVip vip:timeOrderVips){
            IpVipTimeOrderRelation ipVipTimeOrderRelation = new IpVipTimeOrderRelation();
            ipVipTimeOrderRelation.setIpBTimeOrderVip(vip);
            ipVipTimeOrderRelation.setIpBTimeOrderVipOccupyItemList(ipBTimeOrderVipItemMap.get(vip.getId()));
            vipTimeOrderRelations.add(ipVipTimeOrderRelation);
        }
        jitxDeliveryRelation.setIpVipTimeOrderRelationList(vipTimeOrderRelations);
        return jitxDeliveryRelation;
    }


}
