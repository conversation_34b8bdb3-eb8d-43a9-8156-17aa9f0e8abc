package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderSendLogMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OmsSysParam;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderWareHouseKeyword;
import com.jackrain.nea.oc.oms.model.result.QuerySendRuleWarehouseRateResult;
import com.jackrain.nea.oc.oms.model.result.WareHouseResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.util.OmsOrderDistributeWarehouseUtils;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.st.model.result.WarehouseRankResult;
import com.jackrain.nea.st.model.table.StCSendRuleWarehouseRateDO;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsQueryWareHouseService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 分仓服务
 *
 * @author: heliu
 * @since: 2019/3/12
 * create at : 2019/3/12 9:12
 */
@Component
@Slf4j
public class OmsOrderDistributeWarehouseService {

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    @Autowired
    private OmsQueryWareHouseService omsQueryWareHouseService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OcBOrderSendLogMapper ocBOrderSendLogMapper;

    @Autowired
    private OmsOrderDistributeWarehouseService omsWarehouseRuleService;

    @Autowired
    private OmsOrderDistributeWarehouseUtils distributeWarehouseUtils;

    @Autowired
    private OmsSyncStockStrategyService syncStockStrategyService;

    /**
     * 分仓逻辑
     *
     * @param orderRelation 订单对象
     * @return boolean
     */
    public Long doCallDistributeWarehouse(OcBOrderRelation orderRelation, User user) {
        Long warehouseId;
        try {
            warehouseId = distributeWarehouse(orderRelation, user);
        } catch (NDSException e){
            log.error(LogUtil.format("分仓服务异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            return null;
        }
        // 分仓分到合适实体仓则直接返回实体仓ID
        if (warehouseId != null) {
            return warehouseId;
        }
        // 分仓未分到合适实体仓，且原订单未分配过实体仓则返回店铺默认实体仓
        if (Optional.ofNullable(orderRelation.getOrderInfo().getCpCPhyWarehouseId()).orElse(0L) == 0L) {
            warehouseId = omsWarehouseRuleService.queryDefaultWarehouse(orderRelation.getOrderInfo().getCpCShopId());
            return warehouseId;
        } else {
            // 分仓未分到合适实体仓，且原订单曾经分配过实体仓则返回原单对应的实体仓ID
            return orderRelation.getOrderInfo().getCpCPhyWarehouseId();
        }
    }

    /**
     * 分仓 返回实体仓信息
     * @param orderRelation
     * @param user
     * @return
     */
    public CpCPhyWarehouse doCallDistributeWarehouseInfo(OcBOrderRelation orderRelation, User user) {
        Long warehouseId = doCallDistributeWarehouse(orderRelation, user);
        if(warehouseId == null){
            return null;
        } else {
            return cpRpcService.queryByWarehouseId(warehouseId);
        }
    }


    /**
     * 分配仓库
     *
     * @param orderRelation
     * @param user
     * @return
     */
    private Long distributeWarehouse(OcBOrderRelation orderRelation, User user) {
        try {

            OcBOrder ocBOrder = orderRelation.getOrderInfo();
            //查询订单【店铺库存同步策略】中存在的逻辑仓（启用状态）
            List<Long> storeList = syncStockStrategyService.queryShopStoreNextList(ocBOrder.getCpCShopId());

            if (CollectionUtils.isEmpty(storeList)) {
                return null;
            }
            //根据逻辑仓找到对应的实体仓
            List<Long> wareHouseIds = omsQueryWareHouseService.queryWareHouseIds(storeList);
            // 天猫换货订单处理,根据店铺策略排除or保留O2O仓
            wareHouseIds = distributeWarehouseUtils.getUnO2oWareHouseIdList(orderRelation, storeList, wareHouseIds);
            // 根据逻辑仓聚合实体仓
            if (CollectionUtils.isEmpty(wareHouseIds)) {
                return null;
            }
            // 若根据逻辑供货仓只查找出一个实体仓，则返回当前实体仓
            if (wareHouseIds.size() == 1) {
                return wareHouseIds.get(0);
            }
            // 若店铺对应多个实体仓，则通过库存和派单方案筛选合适的仓库
            return getWareHouseIdByStockOrSendPlanScheme(orderRelation, wareHouseIds);
        } catch (Exception ex) {
            log.error(LogUtil.format("调用分仓服务异常,异常信息为:{}"), orderRelation.getOrderId(), Throwables.getStackTraceAsString(ex));
            throw new NDSException(ex);
        }
    }
    //----------------------------------------------------新增订单分仓业务逻辑-----------------------------------------------------

    /**
     * 通过派单日志过滤实体仓
     *
     * @param wareHouseIds 实体仓ID列表
     * @param orderId      订单信息ID
     */
    private void filterWareHouseIds(List<Long> wareHouseIds, long orderId) {
        //已经派过单的实体仓ID列表
        List<Long> hasSendWarehouseIdList = ocBOrderSendLogMapper.selectOcBOrderSendLogList(orderId);
        if (CollectionUtils.isEmpty(hasSendWarehouseIdList)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(wareHouseIds)) {
            wareHouseIds.removeAll(hasSendWarehouseIdList);
        }
    }

    /**
     * 获取实体仓ID.若店铺配置了校验库存，则
     *
     * @param orderRelation 订单信息
     * @return 仓库ID
     */
    private Long getWareHouseIdByStockOrSendPlanScheme(OcBOrderRelation orderRelation, List<Long> wareHouseIds) {
        StCShopStrategyDO shopStrategy =
                shopStrategyService.selectOcStCShopStrategy(orderRelation.getOrderInfo().getCpCShopId());
        if (shopStrategy != null && "Y".equals(shopStrategy.getIsJudgeStock())) {
            // 查询可以整单满足的实体仓列表
            try {
                ValueHolderV14<List<SgSumStorageQueryResult>> holderV14 =
                        sgRpcService.querySendWareHouseStock(orderRelation);
                // sku,逻辑仓，实体仓，可用库存，列表
                List<SgSumStorageQueryResult> storageQueryResults = holderV14.getData();
                if (CollectionUtils.isNotEmpty(storageQueryResults)) {
                    return compareStock(orderRelation, storageQueryResults, orderRelation.getOrderItemList());
                }
                // 沒有可以整单满足的仓库返回null
                return null;
            } catch (Exception e) {
                return null;
            }
        }
        // 若不校验库存直接走派单方案
        List<SgSumStorageQueryResult> storageQueryResults = new ArrayList<>();
        return selectSendPlanScheme(orderRelation.getOrderId(), orderRelation.getOrderInfo().getCpCShopId(),
                wareHouseIds, orderRelation.getOrderInfo().getCpCRegionProvinceId(), storageQueryResults);
    }

    /**
     * 比较sku数量比较得出符合库存数的逻辑仓[寻仓库存服务]2020/2/13
     *
     * @param orderRelation          订单orderRelation对象
     * @param storageQueryResultList 实体仓库存集合[库存中心]
     * @param unRefundOrderItemList  非退款明细集合
     * @return List<Long>
     */
    public Long compareStock(OcBOrderRelation orderRelation, List<SgSumStorageQueryResult> storageQueryResultList,
                             List<OcBOrderItem> unRefundOrderItemList) {

        //取出订单明细条码Code
        List<String> skuCodeList = new ArrayList<>();
        for (OcBOrderItem orderItem : unRefundOrderItemList) {
            if (Objects.isNull(orderItem.getProType()) || orderItem.getProType().intValue() != SkuType.NO_SPLIT_COMBINE) {
                skuCodeList.add(orderItem.getPsCSkuEcode());
            }
        }
        //去重明细skuCode
        List<String> distinctSkuCodeList = skuCodeList.stream().distinct().collect(Collectors.toList());

        //将skuCode 和商品购买数量存在Map集合里面
        Map<String, BigDecimal> skuBuyQtyMap = new HashMap<>();
        for (OcBOrderItem orderItemInfo : unRefundOrderItemList) {
            //取出明细购买数量 相同skuCode数量累加
            BigDecimal qty = (orderItemInfo.getQty() == null ? BigDecimal.ZERO : orderItemInfo.getQty());
            //避免出现不同明细相同sku,若出现,计算累加和
            if (skuBuyQtyMap.containsKey(orderItemInfo.getPsCSkuEcode())) {
                skuBuyQtyMap.put(orderItemInfo.getPsCSkuEcode(),
                        qty.add(skuBuyQtyMap.get(orderItemInfo.getPsCSkuEcode())));
            } else {
                skuBuyQtyMap.put(orderItemInfo.getPsCSkuEcode(), qty);
            }
        }
        //满足库存的临时对象
        List<WareHouseResult> wareHouseResultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(storageQueryResultList)) {
            //逻辑仓对应一个skuCode和可用数量
            for (SgSumStorageQueryResult sgBStorage : storageQueryResultList) {
                BigDecimal qtyAvailable = (sgBStorage.getQtyAvailable() == null ? BigDecimal.ZERO :
                        sgBStorage.getQtyAvailable());
                //将符合条件的逻辑仓都存到集合中  [sku相同  购买数量<库存数量]
                if ((skuBuyQtyMap.containsKey(sgBStorage.getPsCSkuEcode()))
                        && (skuBuyQtyMap.get(sgBStorage.getPsCSkuEcode()).compareTo(qtyAvailable) <= 0)) {
                    wareHouseResultList.add(new WareHouseResult(sgBStorage.getPsCSkuEcode(),
                            sgBStorage.getCpCPhyWarehouseId()));
                }
            }
        }
        //转换计算将相同实体仓的满足sku库存的聚合起来
        Map<Long, StringBuffer> wareHouseListMap = new HashMap<>();
        for (WareHouseResult wareHouseResult : wareHouseResultList) {
            StringBuffer stringBuffer = new StringBuffer();
            if (wareHouseListMap.containsKey(wareHouseResult.getWareHosueId())) {
                wareHouseListMap.put(wareHouseResult.getWareHosueId(),
                        wareHouseListMap.get(wareHouseResult.getWareHosueId()).append(",").append(wareHouseResult.getSkuCode()));
            } else {
                wareHouseListMap.put(wareHouseResult.getWareHosueId(),
                        stringBuffer.append(wareHouseResult.getSkuCode()));
            }
        }

        List<Long> wareHouseIds = new ArrayList<>();
        //得到key集合
        for (Map.Entry<Long, StringBuffer> entry : wareHouseListMap.entrySet()) {
            //判断当前实体发货仓库的明细sku是否都满足库存
            boolean reulst = checkContain(entry.getValue(), distinctSkuCodeList);
            if (reulst) {
                wareHouseIds.add(entry.getKey());
            }
        }
        //聚合过得实体仓去重
        List<Long> secondWareHouseIdTempList = wareHouseIds.stream().distinct().collect(Collectors.toList());
        this.filterWareHouseIds(secondWareHouseIdTempList, orderRelation.getOrderId());
        //过滤京东货到付款不发的仓库
        List<Long> secondWareHouseIds;
        //京东货到付款的订单不发仓库[不支持的仓库包含：菜鸟华南仓、菜鸟华北仓、菜鸟华东仓、菜鸟西南仓、菜鸟华中仓]
        if (PlatFormEnum.JINGDONG.getCode().equals(orderRelation.getOrderInfo().getPlatform())
                && OmsPayType.CASH_ON_DELIVERY.toInteger() == orderRelation.getOrderInfo().getPayType()) {
            secondWareHouseIds = getJdNoSendWareHouseList(secondWareHouseIdTempList, orderRelation);
        } else {
            secondWareHouseIds = secondWareHouseIdTempList;
        }
        // 根据库存中心返回的数据没有聚合到实体仓
        if (CollectionUtils.isEmpty(secondWareHouseIds)) {
            return null;
        } else {
            // 天猫换货订单处理
            List<Long> storeList =
                    syncStockStrategyService.queryShopStoreNextList(orderRelation.getOrderInfo().getCpCShopId());
            secondWareHouseIds = distributeWarehouseUtils.getUnO2oWareHouseIdList(orderRelation, storeList,
                    secondWareHouseIds);
            if (secondWareHouseIds.size() == 1) {
                return secondWareHouseIds.get(0);
            } else {
                // 多个实体仓满足库存走派单规则
                return selectSendPlanScheme(orderRelation.getOrderId(), orderRelation.getOrderInfo().getCpCShopId(),
                        secondWareHouseIds, orderRelation.getOrderInfo().getCpCRegionProvinceId(),
                        storageQueryResultList);
            }
        }
    }

    /**
     * 取出京东快递货到付款的不去分配的仓库
     *
     * @param secondWareHouseIdTempList 传进来满足库存的实体仓库
     * @param orderRelation             订单对象
     * @return List<Long>
     */
    public List<Long> getJdNoSendWareHouseList(List<Long> secondWareHouseIdTempList, OcBOrderRelation orderRelation) {

        String jdNoSendWareHouses = OmsSysParam.JD_NO_SEND_WAREHOUSE.parseValue();
        String jdNoSendWareHouseStr = AdParamUtil.getParam(jdNoSendWareHouses);
        List<Long> jdNoSendWareHouseList = Lists.newArrayList();
        //当存在京东货到付款不分配的实体仓库时
        if (StringUtils.isNotEmpty(jdNoSendWareHouseStr) && CollectionUtils.isNotEmpty(secondWareHouseIdTempList)) {
            String[] jdWareList = jdNoSendWareHouseStr.split(",");
            for (int i = 0; i < jdWareList.length; ++i) {
                jdNoSendWareHouseList.add(Long.valueOf(jdWareList[i]));
            }
            secondWareHouseIdTempList.removeAll(jdNoSendWareHouseList);
            return secondWareHouseIdTempList;
        }
        //当不存在京东货到付款不分配的实体仓库时直接返回
        return secondWareHouseIdTempList;
    }

    /**
     * 判断是否全部包含明细sku
     *
     * @param skuCodeString 符合的sku
     * @param distinctList  本身明细
     * @return boolean
     */
    public boolean checkContain(StringBuffer skuCodeString, List<String> distinctList) {
        List<String> skuCodeTempList = Arrays.asList(skuCodeString.toString().split(","));
        List<String> secondSkuCode = skuCodeTempList.stream().distinct().collect(Collectors.toList());
        return (secondSkuCode.size() == distinctList.size()) && (secondSkuCode.containsAll(distinctList));
    }

    /**
     * 判断是否全部包含明细sku
     *
     * @param skuCodeString 符合的sku
     * @param distinctList  本身明细
     * @return boolean
     */
    public boolean checkContainSkuId(StringBuffer skuCodeString, List<Long> distinctList) {

        List<Long> skuIdTempList = new ArrayList<>();
        for (String retval : skuCodeString.toString().split(",")) {
            skuIdTempList.add(Long.valueOf(retval));
        }
        List<Long> secondSkuCode = skuIdTempList.stream().distinct().collect(Collectors.toList());
        return (secondSkuCode.size() == distinctList.size()) && (secondSkuCode.containsAll(distinctList));
    }

    /**
     * 查找派单方案
     *
     * @param orderId          订单Id
     * @param shopId           店铺Id
     * @param warehouseList    实体仓集合
     * @param regionProvinceId 收货人所在省份ID
     * @return Long
     */
    public Long selectSendPlanScheme(Long orderId, Long shopId, List<Long> warehouseList,
                                     Long regionProvinceId,
                                     List<SgSumStorageQueryResult> storageQueryResults) {
        //先根据店铺找派单方案,如果找不到,则随机取一个发货仓库
        Date currentDate = new Date();
        List<Long> sendPlanList = omsQueryWareHouseService.selectSendPlanList(orderId, shopId, currentDate);
        if (CollectionUtils.isEmpty(sendPlanList)) {
            //没有派单方案.随机取一个发货仓库
            return randomWareHouseId(warehouseList);
        } else if (sendPlanList.size() == 1) {
            //如果只有一种派单方案,则直接取方案中的派单规则进行执行
            return doSelectOnlyRule(sendPlanList.get(0), warehouseList, regionProvinceId, orderId, storageQueryResults);
        } else {
            //如果方案有多种,则根据则判断大促、活动、日常（大促>活动>日常）优先取大促，若再有多个再根据创建时间最近的方案为准
            Long lastOnlyPlanId = selectActivePlanId(sendPlanList, orderId);
            //全部方案都找不到的情况
            if (lastOnlyPlanId == null) {
                return randomWareHouseId(warehouseList);
            }
            return doSelectOnlyRule(lastOnlyPlanId, warehouseList, regionProvinceId, orderId, storageQueryResults);
        }
    }

    /**
     * 获取一个方案然后对应下面的规则去找对应符合条件的逻辑仓[公共方法,一个方案和多个方案都调]
     *
     * @param planId           方案Id
     * @param warehouseList    实体仓集合
     * @param regionProvinceId 省份Id
     * @param orderId          订单Id
     * @return 实体仓Id
     */
    public Long doSelectOnlyRule(Long planId, List<Long> warehouseList, Long regionProvinceId, Long orderId,
                                 List<SgSumStorageQueryResult> storageQueryResults) {

        List<Long> sendRuleList = omsQueryWareHouseService.selectSendPlanItemList(orderId, planId);
        if (CollectionUtils.isEmpty(sendRuleList)) {
            //方案下面没有维护派单方案,随机取一个发货仓库
            return randomWareHouseId(warehouseList);
        } else {
            //如果方案下对应多个派单规则 根据优先级排序,取最前面一个
            return querySendWareHouseRule(planId, warehouseList, regionProvinceId, orderId, storageQueryResults);
        }
    }

    /**
     * 查找派单规则[取出规则类型,然后根据相应规则类型进行筛选实体仓,依次执行]
     *
     * @param planId                    派单方案ID
     * @param hasStorageWarehouseIdList 有库存的实体仓集合
     * @param regionProvinceId          收货人所在省ID
     * @param orderId                   订单Id
     * @param storageQueryResults       库存有效数据
     * @return Long
     */
    private Long querySendWareHouseRule(Long planId, List<Long> hasStorageWarehouseIdList, Long regionProvinceId,
                                        Long orderId,
                                        List<SgSumStorageQueryResult> storageQueryResults) {

        Map<String, Long> ruleItemMap = omsQueryWareHouseService.querySendRuleIdByType(orderId, planId);
        //先查 1.按收货地址
        Long addressRankRuleId = ruleItemMap.get("address");
        //再查 2.按分仓比例
        Long wareHouseRateRuleId = ruleItemMap.get("rate");
        //根据地址去查
        List<Long> physicalWarehouseIdList = new ArrayList<>();
        List<Long> warehouseRankList = new ArrayList<>();
        List<WarehouseRankResult> warehouseRankResults = selectAddressRentRule(hasStorageWarehouseIdList,
                regionProvinceId,
                addressRankRuleId, orderId);
        for (WarehouseRankResult warehouseRankResult : warehouseRankResults) {
            physicalWarehouseIdList.add(warehouseRankResult.getWarehouseId());
            warehouseRankList.add(Long.valueOf(warehouseRankResult.getRank()));
        }
        this.filterWareHouseIds(physicalWarehouseIdList, orderId);
        //地址就近为空和分仓比例不为空的情况
        if (CollectionUtils.isEmpty(physicalWarehouseIdList) && wareHouseRateRuleId != null) {
            Long selectedWarehouseId = selectWarehouseRateRule(hasStorageWarehouseIdList, wareHouseRateRuleId, orderId);
            omsQueryWareHouseService.updateRuleWarehouseRate(selectedWarehouseId);
            return selectedWarehouseId;
            //地址就近为空和分仓比例为空的情况
        } else if (CollectionUtils.isEmpty(physicalWarehouseIdList) && wareHouseRateRuleId == null) {
            return randomWareHouseId(hasStorageWarehouseIdList);
            //地址就近不为空和分仓比例不为空的情况
        } else if (CollectionUtils.isNotEmpty(physicalWarehouseIdList) && wareHouseRateRuleId != null) {
            if (physicalWarehouseIdList.size() == 1) {
                return physicalWarehouseIdList.get(0);
            } else {
                //将优先级不一样的时候
                if (hasStorageWarehouseIdList.size() == physicalWarehouseIdList.size()) {
                    return physicalWarehouseIdList.get(0);
                } else {
                    //更新发货数量
                    Long rateId = selectWarehouseRateRule(hasStorageWarehouseIdList, wareHouseRateRuleId, orderId);
                    omsQueryWareHouseService.updateRuleWarehouseRate(rateId);
                    return rateId;
                }
            }
            //地址就近不为空和分仓比例为空的情况
        } else if (CollectionUtils.isNotEmpty(physicalWarehouseIdList) && wareHouseRateRuleId == null) {
            Long minRank = Collections.min(warehouseRankList);
            List<WarehouseRankResult> collect =
                    warehouseRankResults.stream().filter(result -> result.getRank().equals(minRank.toString())).collect(Collectors.toList());
            //如果只有一个，直接返回
            if (collect.size() == 1) {
                return collect.get(0).getWarehouseId();
            } else {
                //如果有多个，证明优先级重复，需判断库存返回的条码可用库存数。
                physicalWarehouseIdList.clear();
                for (WarehouseRankResult warehouseRankResult : collect) {
                    physicalWarehouseIdList.add(warehouseRankResult.getWarehouseId());
                }
                //如果sg实体仓库存结果不为空，则取可用库存最大的实体仓
                if (CollectionUtils.isNotEmpty(storageQueryResults)) {
                    //如果sg实体仓库存结果不为空，则取可用库存最大的实体仓
                    Map<Long, BigDecimal> map = new HashMap<>();
                    for (Long wareId : physicalWarehouseIdList) {
                        BigDecimal bigDecimal = new BigDecimal(0);
                        for (SgSumStorageQueryResult storageQueryResult : storageQueryResults) {
                            if (wareId.equals(storageQueryResult.getCpCPhyWarehouseId())
                                    && storageQueryResult.getQtyAvailable() != null) {
                                bigDecimal = bigDecimal.add(storageQueryResult.getQtyAvailable());
                            }
                        }
                        map.put(wareId, bigDecimal);
                    }
                    List<Map.Entry<Long, BigDecimal>> list = new ArrayList<>(map.entrySet());
                    list.sort((o1, o2) -> (o2.getValue().compareTo(o1.getValue())));
                    return list.get(0).getKey();
                } else {
                    return randomWareHouseId(physicalWarehouseIdList);
                }
            }
            //都不存在的情况
        } else {
            return randomWareHouseId(hasStorageWarehouseIdList);
        }
    }


    /**
     * 地址就近规则 按仓库优先级发货
     *
     * @param warehouseList    实体仓list
     * @param regionProvinceId 收货人地址
     * @param sendRuleId       规则Id
     * @param orderId          订单Id
     * @return Long 实体仓ID
     */
    private List<WarehouseRankResult> selectAddressRentRule(List<Long> warehouseList, Long regionProvinceId,
                                                            Long sendRuleId, Long orderId) {
        return omsQueryWareHouseService.selectAddressWarehouseList(orderId, warehouseList, regionProvinceId,
                sendRuleId);
    }


    /**
     * 按分仓发货比例
     *
     * @param warehouseList 实体仓list
     * @param sendRuleId    规则Id
     * @param orderId       订单Id
     * @return Long 实体仓ID
     */
    public Long selectWarehouseRateRule(List<Long> warehouseList, Long sendRuleId, Long orderId) {
        //根据传过来的实体仓Id去发货比例明细表里面查询数据
        List<StCSendRuleWarehouseRateDO> warehouseRateList = omsQueryWareHouseService.selectWarehouseRateMapper(
                warehouseList, sendRuleId);

        if (CollectionUtils.isEmpty(warehouseRateList)) {
            return randomWareHouseId(warehouseList);
        }
        if (warehouseRateList.size() == 1) {
            //发货比例只有一种情况时
            return warehouseRateList.get(0).getCpCPhyWarehouseId();
        } else {
            BigDecimal qtyTotal = omsQueryWareHouseService.selectQtySendTotal(warehouseList, sendRuleId);
            //排除空的情况
            BigDecimal totalTemp = (qtyTotal == null ? BigDecimal.ZERO : qtyTotal);
            //总发货数量都为0的情况
            if (totalTemp.compareTo(BigDecimal.ZERO) == 0) {
                //首先按发货比例升序
                List<StCSendRuleWarehouseRateDO> firstResultList = warehouseRateList.stream().sorted(Comparator
                        .comparing(StCSendRuleWarehouseRateDO::getSendRate).reversed()).collect(Collectors.toList());
                //临时存储发货比例的第一条数据
                List<StCSendRuleWarehouseRateDO> secondResultList = new ArrayList<>();
                for (StCSendRuleWarehouseRateDO sendRuleWarehouseRate : firstResultList) {
                    //根据发货比例排好序的数据与未排序的数据做比例匹配
                    if (firstResultList.get(0).getSendRate().compareTo(sendRuleWarehouseRate.getSendRate()) == 0) {
                        secondResultList.add(sendRuleWarehouseRate);
                    }
                }
                //发货比例不一样的时候取最大的
                if (secondResultList.size() == 1) {
                    return secondResultList.get(0).getCpCPhyWarehouseId();
                }
                //发货比例一样则按优先级继续排序
                List<StCSendRuleWarehouseRateDO> threeResultList = secondResultList.stream().sorted(Comparator
                        .comparing(StCSendRuleWarehouseRateDO::getRank)).collect(Collectors.toList());
                List<StCSendRuleWarehouseRateDO> fourResultList = new ArrayList<>();
                for (StCSendRuleWarehouseRateDO sendRuleWarehouseRate : threeResultList) {
                    //根据优先级排好序的数据与未排序的数据做匹配
                    if (threeResultList.get(0).getRank().compareTo(sendRuleWarehouseRate.getRank()) == 0) {
                        fourResultList.add(sendRuleWarehouseRate);
                    }
                }
                //发货比例和优先级完全一致的情况直接取第一个,因为发货比例和优先级都已经排好序
                return fourResultList.get(0).getCpCPhyWarehouseId();
            } else {
                List<QuerySendRuleWarehouseRateResult> sendRuleWarehouseRateResultList = new ArrayList<>();
                //发货比例最终计算值list
                for (StCSendRuleWarehouseRateDO ruleWarehouseRate : warehouseRateList) {
                    BigDecimal qtySend = (ruleWarehouseRate.getQtySend() == null ? BigDecimal.ZERO :
                            ruleWarehouseRate.getQtySend());
                    //算出实际发货比例
                    BigDecimal sendScale = qtySend.divide(qtyTotal, 2, BigDecimal.ROUND_HALF_UP);
                    //发货比例
                    BigDecimal scale = (ruleWarehouseRate.getSendRate() == null ? BigDecimal.ZERO :
                            ruleWarehouseRate.getSendRate()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
                    //result = 发货比例-实际发货比例
                    BigDecimal resultScale = scale.subtract(sendScale);
                    QuerySendRuleWarehouseRateResult sendRuleWarehouseRateResult
                            = new QuerySendRuleWarehouseRateResult();
                    //算出来的仓库发货比例
                    sendRuleWarehouseRateResult.setActualProportion(resultScale);
                    BeanUtils.copyProperties(ruleWarehouseRate, sendRuleWarehouseRateResult);
                    sendRuleWarehouseRateResultList.add(sendRuleWarehouseRateResult);
                }
                //首先按仓库计算出来的实际发货比例排序
                List<QuerySendRuleWarehouseRateResult> firstNextResultList =
                        sendRuleWarehouseRateResultList.stream().sorted(Comparator
                                .comparing(QuerySendRuleWarehouseRateResult::getActualProportion).reversed()).collect(Collectors.toList());
                //临时存储仓库实际发货比例的第一条数据
                List<QuerySendRuleWarehouseRateResult> secondNextResultList = new ArrayList<>();
                for (QuerySendRuleWarehouseRateResult sendRuleWarehouseRateResult : firstNextResultList) {
                    //根据发货比例排好序的数据与未排序的数据做比例匹配
                    if (firstNextResultList.get(0).getActualProportion().compareTo(sendRuleWarehouseRateResult.getActualProportion()) == 0) {
                        secondNextResultList.add(sendRuleWarehouseRateResult);
                    }
                }
                //仓库实际发货比例不一样的时候取最大的
                if (secondNextResultList.size() == 1) {
                    return secondNextResultList.get(0).getCpCPhyWarehouseId();
                }
                //仓库实际发货比例一样的时候安装优先级排序
                List<QuerySendRuleWarehouseRateResult> threeNextResultList =
                        secondNextResultList.stream().sorted(Comparator
                                .comparing(QuerySendRuleWarehouseRateResult::getRank)).collect(Collectors.toList());
                List<QuerySendRuleWarehouseRateResult> fourNextResultList = new ArrayList<>();
                for (QuerySendRuleWarehouseRateResult sendRuleWarehouseRateResult : threeNextResultList) {
                    fourNextResultList.add(sendRuleWarehouseRateResult);
                }
                //排完序后取第一个
                return fourNextResultList.get(0).getCpCPhyWarehouseId();
            }
        }
    }

    /**
     * 若有多个方案，则判断大促、活动、日常（大促>活动>日常），优先取大促，若再有多个再根据创建时间最近的方案为准
     *
     * @param sendPlanList 方案list集合
     * @param orderId      订单Id
     * @return Long
     */
    private Long selectActivePlanId(List<Long> sendPlanList, Long orderId) {

        //根据活动类型和创建时间排好序,然后去取第一个值
        //返回第一个元素,即活动方案Id
        return sendPlanList.get(0);
    }

    /**
     * 随机取一个发货仓库,默认实体仓Id最大的元素
     *
     * @param cpPhyWarehouseList 实体仓集合
     * @return Long
     */
    public Long randomWareHouseId(List<Long> cpPhyWarehouseList) {
        return Collections.max(cpPhyWarehouseList);
    }

    //-----------------------------------------基本方法查询----------------------------------------------

    /**
     * 查询店铺的默认发货仓库
     *
     * @param shopId 店铺Id
     * @return Long
     */
    public Long queryDefaultWarehouse(Long shopId) {
        StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(shopId);
        if (shopStrategy == null) {
            return null;
        }
        return shopStrategy.getDefaultStoreId();
    }

    /**
     * 判断是否存在实体仓为JCstore编码的信息
     *
     * @return boolean
     */
    public boolean existOmsWarehouseJStore() {

        CpCPhyWarehouse chyWarehouse = cpRpcService.queryOmsWarehouseJStore(OrderWareHouseKeyword.JD_WAREHOUSE);
        return Objects.nonNull(chyWarehouse);
    }


    /**
     * 获取根据仓库ID获取对象
     *
     * @param cpCphyWarehouseId ecode参数
     * @return OcCpCPhyWarehouse
     */
    public CpCPhyWarehouse queryByWarehouseId(Long cpCphyWarehouseId) {
        return cpRpcService.queryByWarehouseId(cpCphyWarehouseId);
    }

    public int selectOrderSendLog(Long orderId) {
        return ocBOrderSendLogMapper.countByOrderId(orderId);
    }
}