package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jackrain.nea.oc.oms.model.table.StCBoxStrategyEntity;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

import java.util.List;
import java.util.Locale;

public interface StCBoxStrategyService extends IService<StCBoxStrategyEntity> {

    ValueHolder updateBoxStrategy(User user, StCBoxStrategyEntity strategyEntity);

    ValueHolder addBoxStrategy(User user, StCBoxStrategyEntity strategyEntity);

    String orderGoodsSort(String orderGoods);

    Boolean checkSplitOrderRules(String orderGoods, String splitOrderRules);

    boolean checkOrderGoods(String orderGoods);

    String getStrategyNo(StCBoxStrategyEntity strategyEntity, Locale locale);

    Boolean updateActive(User user, List<Long> objIds, String isActive);
}

