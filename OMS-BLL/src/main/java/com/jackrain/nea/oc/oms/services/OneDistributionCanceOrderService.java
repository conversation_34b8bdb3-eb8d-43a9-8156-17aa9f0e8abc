package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.request.CpShopQueryRequest;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemFiMapper;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName : OneDistributionCanceOrderService  
 * @Description : 一件代发退单接口
 * <AUTHOR>  YCH
 * @Date: 2021-11-04 20:28  
 */
@Slf4j
@Component
public class OneDistributionCanceOrderService {

    @Reference(group = "cp-ext", version = "1.0")
    private CpShopQueryCmd cpShopQueryCmd;

    @Autowired
    private IpBTaobaoRefundMapper ipBTaobaoRefundMapper;

    @Autowired
    private IpBStandplatRefundMapper ipBStandplatRefundMapper;

    @Autowired
    private IpBStandplatRefundItemMapper ipBStandplatRefundItemMapper;

    @Autowired
    private OcBOrderItemFiMapper ocBOrderItemFiMapper;

    @Autowired
    private OmsStandplatRefundService omsStandplatRefundService;

    @Autowired
    private CpRpcService cpRpcService;

    public ValueHolderV14 oneDistributionCanceOrder(JSONObject request) {
        if (log.isDebugEnabled()) {
            log.debug(" OneDistributionCanceOrderService.oneDistributionCanceOrder.request {}", JSON.toJSONString(request));
        }
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "success");
        //校验入参
        String errMessage = checkParam(request);
        if (StringUtils.isNotEmpty(errMessage)){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(errMessage);
            return v14;
        }
        return saveParam(request);
    }

    private String checkParam(JSONObject request) {
        StringBuilder errMessage = new StringBuilder();
        if (request.isEmpty()){
            errMessage.append("传入数据不能为空！");
            return errMessage.toString();
        }
        if (StringUtils.isEmpty(request.getString("tid"))){
            errMessage.append("平台单号不能为空！");
        }
        if (StringUtils.isEmpty(request.getString("cpCShopEcode"))){
            errMessage.append("店铺编码不能为空！");
        }
        if (StringUtils.isEmpty(request.getString("refundId"))){
            errMessage.append("平台退单号不能为空！");
        }
        if (StringUtils.isEmpty(request.getString("type"))){
            errMessage.append("业务类型不能为空！");
        }
        if (request.getDate("created") == null){
            errMessage.append("退款申请时间不能为空！");
        }


        String cpCShopEcode = request.getString("cpCShopEcode");
        CpShopQueryRequest shopQueryRequest = new CpShopQueryRequest();
        List<String> shopList = new ArrayList<>();
        shopList.add(cpCShopEcode);
        shopQueryRequest.setShopCodes(shopList);
        List<CpShop> cpShops = cpShopQueryCmd.queryShop(shopQueryRequest);
        if (CollectionUtils.isEmpty(cpShops)){
            errMessage.append("根据店铺编码查询无可用记录，请检查！");
        }else {
            //平台
            request.put("platform",Math.toIntExact(cpShops.get(0).getCpCPlatformId()));
            request.put("platform_ecode",cpShops.get(0).getCpCPlatformEcode());
            request.put("platform_ename",cpShops.get(0).getCpCPlatformEname());
            request.put("cp_c_shop_id",cpShops.get(0).getId() != null ? cpShops.get(0).getId() : cpShops.get(0).getCpCShopId());
            request.put("cp_c_shop_title",cpShops.get(0).getCpCShopTitle());
        }
        return errMessage.toString();
    }
    @Transactional(rollbackFor = {Exception.class})
    public ValueHolderV14 saveParam(JSONObject request) {
        Integer platform = request.getInteger("platform");
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        //淘宝平台
        if (PlatFormEnum.TAOBAO.getCode().equals(platform)){
            IpBTaobaoRefund ipBTaobaoRefund = new IpBTaobaoRefund();
            String errorMsg = buildTaobaoParam(request, ipBTaobaoRefund);
            if (StringUtils.isNotEmpty(errorMsg)){
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(errorMsg);
            }
            OperateUserUtils.saveOperator(ipBTaobaoRefund, SystemUserResource.getRootUser());
            ipBTaobaoRefundMapper.insert(ipBTaobaoRefund);
            return v14;
        }
        //京东
        else if (PlatFormEnum.JINGDONG.getCode().equals(platform)){
            return v14;
        }
        //唯品会
        else if (PlatFormEnum.VIP_JITX.getCode().equals(platform)){
            return v14;
        }else {
            return savedStandplatParam(request);
        }
    }

    private String buildTaobaoParam(JSONObject request,IpBTaobaoRefund ipBTaobaoRefund) {

        StringBuilder errMessage = new StringBuilder();
        ipBTaobaoRefund.setId(ModelUtil.getSequence("ip_b_taobao_refund"));
        //淘宝交易单号-平台单号
        ipBTaobaoRefund.setTid(request.getLong("tid"));
        //平台退款单号-平台退单号
        ipBTaobaoRefund.setRefundId(request.getString("refundId"));
        //店铺
        ipBTaobaoRefund.setCpCShopId(request.getLong("cp_c_shop_id"));
        //物流公司
        ipBTaobaoRefund.setCompanyName(request.getString("companyName"));
        //物流单号
        ipBTaobaoRefund.setSid(request.getString("logisticsNo"));
        //退款原因
        ipBTaobaoRefund.setReason(request.getString("refuseReason"));
        //退款金额
        ipBTaobaoRefund.setRefundFee(request.getBigDecimal("refundAmount"));
        //子订单
        if (StringUtils.isNotEmpty(request.getString("oid"))){
            ipBTaobaoRefund.setOid(request.getLong("oid"));
        }else {
            errMessage.append("该订单为淘宝退单，子订单不能为空！");
        }
        //支付宝交易单号
        if (StringUtils.isNotEmpty(request.getString("alipayNo"))){
            ipBTaobaoRefund.setAlipayNo(request.getString("alipayNo"));
        }else {
            errMessage.append("该订单为淘宝退单，支付宝交易单号不能为空！");
        }
        //退还金额
        if (request.getBigDecimal("refundAmount") != null){
            ipBTaobaoRefund.setRefundFee(request.getBigDecimal("refundAmount"));
        }
        //条码id和条码编码不能均为空
        if (request.getLong("psCSkuId") == null && request.getString("psCSkuEcode") == null){
            errMessage.append("条码id和条码编码不能均为空！");
        }
        //退款状态 1订单取消 2退单生成 3退单取消
        // 退款成功=>1订单取消    买家已经退货，等待卖家确认收货=>2退单生成  退款关闭=>3退单取消
        if ("1".equals(request.getString("type"))){
            ipBTaobaoRefund.setStatus(TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode());
            ipBTaobaoRefund.setOrderStatus(TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode());
        }else if ("2".equals(request.getString("type"))){
            ipBTaobaoRefund.setStatus(TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode());
            ipBTaobaoRefund.setOrderStatus(TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode());
        }else if ("3".equals(request.getString("type"))){
            ipBTaobaoRefund.setStatus(TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode());
            ipBTaobaoRefund.setOrderStatus(TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode());
        }else {
            errMessage.append("退款状态不正确，请检查！");
        }
        //退款原因
        ipBTaobaoRefund.setReason(request.getString("refuseReason"));
        //物流公司
        ipBTaobaoRefund.setCompanyName(request.getString("companyName"));
        //物流单号
        ipBTaobaoRefund.setSid(request.getString("logisticsNo"));
        //退款申请时间
        ipBTaobaoRefund.setCreated(request.getDate("created"));
        //商品条码
        ipBTaobaoRefund.setSku(request.getString("psCSkuEcode"));
        //数量
        ipBTaobaoRefund.setNum(request.getLong("qty"));
        //转换状态
        ipBTaobaoRefund.setIstrans("0");
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemFiMapper.selectList(new QueryWrapper<OcBOrderItem>().lambda()
                .eq(OcBOrderItem::getTid, ipBTaobaoRefund.getTid())
                .eq(OcBOrderItem::getOoid, ipBTaobaoRefund.getOid()));
        BigDecimal qty = BigDecimal.ZERO;
        BigDecimal priceActual = BigDecimal.ZERO;
        BigDecimal realAmt = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(ocBOrderItems)){
            for (OcBOrderItem ocBOrderItem : ocBOrderItems){
                qty = qty.add(ocBOrderItem.getQty());
                priceActual = ocBOrderItem.getPriceActual();
                realAmt = realAmt.add(ocBOrderItem.getRealAmt());
            }
        }
        if (ipBTaobaoRefund.getRefundFee() == null && ipBTaobaoRefund.getNum() == null){
            ipBTaobaoRefund.setNum(qty.longValue());
            ipBTaobaoRefund.setRefundFee(realAmt);
        }else if (ipBTaobaoRefund.getRefundFee() == null && ipBTaobaoRefund.getNum() != null){
            ipBTaobaoRefund.setRefundFee(priceActual.multiply(new BigDecimal(ipBTaobaoRefund.getNum())));
        }else if (ipBTaobaoRefund.getRefundFee() != null && ipBTaobaoRefund.getNum() == null){
            BigDecimal divide = ipBTaobaoRefund.getRefundFee().divide(priceActual);
            ipBTaobaoRefund.setNum(divide.longValue());
        }
        return errMessage.toString();
    }

    /**
     * 通用退单保存
     * @param request
     * @return
     */
    private ValueHolderV14 savedStandplatParam(JSONObject request) {
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS,"数据保存成功！");

        try{
            String refundNo = request.getString("refundId");
            String tid = request.getString("tid");
            IpBStandplatRefund refundRelation = ipBStandplatRefundMapper.selectStandplatRefundByRefundId(refundNo);
//            List<IpBStandplatRefund> ipBStandplatRefunds = ipBStandplatRefundMapper.selectList(new QueryWrapper<IpBStandplatRefund>().lambda()
//                    .eq(IpBStandplatRefund::getOrderNo, tid));
//            if (CollectionUtils.isNotEmpty(ipBStandplatRefunds)){
//                v14.setCode(ResultCode.SUCCESS);
//                v14.setMessage("该平台单号已存在单据！");
//                return v14;
//            }
            if (refundRelation == null){
              return saveStandplatRefund(refundNo,request);
            }else {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("单据已存在，状态为不是为未转化，不允许重复保存！");
                return v14;
            }
        }catch (Exception e){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
            return v14;
        }
    }

    public ValueHolderV14 saveStandplatRefund(String refundNo,JSONObject request){
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS,"数据保存成功！");

        IpBStandplatRefund ipBStandplatRefund = new IpBStandplatRefund();
        ipBStandplatRefund.setId(ModelUtil.getSequence("ip_b_standplat_refund"));
        //退单号
        ipBStandplatRefund.setReturnNo(refundNo);
        //原单单号
        ipBStandplatRefund.setOrderNo(request.getString("tid"));
        //子订单单号
        ipBStandplatRefund.setSubOrderId(request.getString("oid"));
        //店铺
        ipBStandplatRefund.setCpCShopId(request.getLong("cp_c_shop_id"));
        ipBStandplatRefund.setCpCShopEcode(request.getString("cpCShopEcode"));
        ipBStandplatRefund.setCpCShopTitle(request.getString("cp_c_shop_title"));
        //平台
        ipBStandplatRefund.setCpCPlatformId(request.getLong("platform"));
//                ipBStandplatRefund.setCpCPlatformEname(request.getString("platform_ename"));
        //退还金额
        ipBStandplatRefund.setRefundAmount(request.getBigDecimal("refundAmount"));
        //退回物流单号
        ipBStandplatRefund.setLogisticsNo(request.getString("logisticsNo"));
        //退款原因
        ipBStandplatRefund.setReturnReason(request.getString("refuseReason"));
        //退货状态
        ipBStandplatRefund.setRefundType(2);
        //转换状态 默认未转换
        ipBStandplatRefund.setIstrans(0);
        //退回物流公司
        ipBStandplatRefund.setCompanyName(request.getString("companyName"));
        LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(ipBStandplatRefund.getCompanyName());
        log.info(" 退回物流公司logisticsInfo {}",JSON.toJSONString(logisticsInfo));
        if (logisticsInfo != null) {
            ipBStandplatRefund.setCompanyName(logisticsInfo.getName());
        }
        //退款状态 1订单取消 2退单生成 3退单取消
        // 退款成功=>1订单取消    买家已经退货，等待卖家确认收货=>2退单生成  退款关闭=>3退单取消
        if ("1".equals(request.getString("type"))){
            ipBStandplatRefund.setReturnStatus(4);
        }else if ("2".equals(request.getString("type"))){
            ipBStandplatRefund.setReturnStatus(3);
        }else if ("3".equals(request.getString("type"))){
            ipBStandplatRefund.setReturnStatus(6);
        }else {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("退款状态不正确，请检查！");
            return v14;
        }
        OperateUserUtils.saveOperator(ipBStandplatRefund, SystemUserResource.getRootUser());
        saveStandplatRefundItem(ipBStandplatRefund,request);
        log.info(" ipBStandplatRefund.insert {}",JSON.toJSONString(ipBStandplatRefund));
        ipBStandplatRefundMapper.insert(ipBStandplatRefund);
        return v14;
    }
    public void saveStandplatRefundItem(IpBStandplatRefund ipBStandplatRefund,JSONObject request){
        JSONArray itemList = request.getJSONArray("items");
        List<IpBStandplatRefundItem> list = new ArrayList<>();
        BigDecimal amtFee = BigDecimal.ZERO;
        String mainSubId = null;
        for (int i = 0; i < itemList.size(); i++) {
            JSONObject item = itemList.getJSONObject(i);
            //条码id和条码编码不能均为空
            if (item.getLong("psCSkuId") == null && item.getString("psCSkuEcode") == null){
                throw new NDSException("条码id和条码编码不能均为空！");
            }
            //子明细
            IpBStandplatRefundItem ipBStandplatRefundItem = new IpBStandplatRefundItem();
            ipBStandplatRefundItem.setId(ModelUtil.getSequence("ip_b_standplat_refund_item"));
            ipBStandplatRefundItem.setIpBStandplatRefundId(ipBStandplatRefund.getId());
            //退单号
            ipBStandplatRefundItem.setReturnNo(ipBStandplatRefund.getReturnNo());
            //子订单
            if (StringUtils.isNotEmpty(item.getString("oid"))){
                ipBStandplatRefundItem.setSubOrderId(item.getString("oid"));
            }else {
                //通用若没有子订单，根据sku加上tid拼接
                ipBStandplatRefundItem.setSubOrderId(ipBStandplatRefund.getOrderNo()+item.getString("psCSkuEcode"));
            }
            //退款金额
            ipBStandplatRefundItem.setRefundFee(item.getBigDecimal("refundAmount"));
            //退货数量
            if (item.getLong("qty") != null){
                ipBStandplatRefundItem.setReturnQuantity(new BigDecimal(item.getLong("qty").toString()));
            }
            //sku
            ipBStandplatRefundItem.setSkuId(item.getString("psCSkuId"));
            ipBStandplatRefundItem.setSku(item.getString("psCSkuEcode"));

            List<OcBOrderItem> ocBOrderItems = ocBOrderItemFiMapper.selectList(new QueryWrapper<OcBOrderItem>().lambda()
                    .eq(OcBOrderItem::getTid, ipBStandplatRefund.getOrderNo())
                    .eq(OcBOrderItem::getOoid, ipBStandplatRefundItem.getSubOrderId()));
            BigDecimal qty = BigDecimal.ZERO;
            BigDecimal priceActual = BigDecimal.ZERO;
            BigDecimal realAmt = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(ocBOrderItems)){
                for (OcBOrderItem ocBOrderItem : ocBOrderItems){
                    qty = qty.add(ocBOrderItem.getQty());
                    priceActual = ocBOrderItem.getPriceActual();
                    realAmt = realAmt.add(ocBOrderItem.getRealAmt());
                }
            }
            if ( item.getLong("qty")== null && item.get("refundAmount") == null){
                ipBStandplatRefundItem.setReturnQuantity(qty);
                ipBStandplatRefundItem.setQuantity(qty);
                ipBStandplatRefundItem.setRefundFee(realAmt);
            }else if (item.get("refundAmount") == null && item.getLong("qty")!= null){
                ipBStandplatRefundItem.setQuantity(qty);
                ipBStandplatRefundItem.setRefundFee(priceActual.multiply(new BigDecimal(String.valueOf(ipBStandplatRefundItem.getQuantity()))));
            }else if (item.get("refundAmount") != null &&  item.getLong("qty")== null){
                BigDecimal divide = ipBStandplatRefundItem.getRefundFee().divide(priceActual);
                ipBStandplatRefundItem.setQuantity(qty);
                ipBStandplatRefundItem.setReturnQuantity(divide);
            }
            OperateUserUtils.saveOperator(ipBStandplatRefundItem, SystemUserResource.getRootUser());
            list.add(ipBStandplatRefundItem);
            amtFee = amtFee.add(ipBStandplatRefundItem.getRefundFee());
            if (StringUtils.isEmpty(mainSubId)){
                mainSubId = ipBStandplatRefundItem.getSubOrderId();
            }else {
                mainSubId = mainSubId + "," + ipBStandplatRefundItem.getSubOrderId();
            }
        }
        ipBStandplatRefundItemMapper.batchInsert(list);
        ipBStandplatRefund.setRefundAmount(amtFee);
        ipBStandplatRefund.setSubOrderId(mainSubId);
    }
}
