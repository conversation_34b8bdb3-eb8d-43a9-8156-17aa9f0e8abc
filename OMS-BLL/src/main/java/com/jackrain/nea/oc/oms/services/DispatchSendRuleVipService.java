package com.jackrain.nea.oc.oms.services;

import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.model.SendPlanExecution;
import com.jackrain.nea.psext.api.utils.JsonUtils;
import com.jackrain.nea.st.model.enums.SendRuleEtype;
import com.jackrain.nea.st.model.result.WarehouseRankResult;
import com.jackrain.nea.st.model.table.StCSendRuleAddressVipDo;
import com.jackrain.nea.st.model.table.StCSendRuleDO;
import com.jackrain.nea.st.service.SendPlanRuleQueryService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2020-06-17
 * @desc 派单规则-按唯品会调度
 **/
@Component
@Slf4j
public class DispatchSendRuleVipService extends AbstractDispatchSendRule {
    @Autowired
    private SendPlanRuleQueryService sendPlanRuleQueryService;

    /**
     * 派单规则处理
     *
     * @param sendPlanExecution   派单方案执行参数
     * @param storageQueryResults 实体仓库存
     * @param stCSendRuleDO       派单规则
     * @return
     * @warning 找不到一定要返回null，允许一个派单方案和派单规则执行，否则会中断派单方案和派单规则
     */
    @Override
    public Long execute(SendPlanExecution sendPlanExecution, List storageQueryResults, StCSendRuleDO stCSendRuleDO) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("派单规则-唯品会调度开始执行参数{}，实体仓库存{}，规则{}", "派单规则处理"),
                    sendPlanExecution, storageQueryResults, stCSendRuleDO);
        }
        List<StCSendRuleAddressVipDo> stCSendRuleAddressVipDoList = sendPlanRuleQueryService.selectSendRuleVip(stCSendRuleDO.getId(), sendPlanExecution.getCpCVipcomWahouseId());
        //按优先级排序的仓库
        List<WarehouseRankResult> warehouseRankResultList = getWarehouseRankResult(sendPlanExecution.getWarehouseIdList(), stCSendRuleAddressVipDoList);
        if (CollectionUtils.isEmpty(warehouseRankResultList)) {
            //一定返回空
            return null;
        }
        if (warehouseRankResultList.size() == 1) {
            return warehouseRankResultList.get(0).getWarehouseId();
        }
        //优先级
        List<Long> rankList = new ArrayList<>();
        Set<String> uniqueValues = new HashSet<>();
        for (WarehouseRankResult warehouseRankResult : warehouseRankResultList) {
            String rank = warehouseRankResult.getRank();
            if (uniqueValues.add(rank)) {
                rankList.add(Long.valueOf(rank));
            }
        }
        //第一优先级
        Long minRank = Collections.min(rankList);
        List<WarehouseRankResult> resultList = warehouseRankResultList.stream()
                .filter(f -> f.getRank().equals(minRank.toString())).collect(Collectors.toList());
        //sg实体仓库存结果为空
        if (resultList.size() == 1 || CollectionUtils.isEmpty(storageQueryResults)) {
            //理论上来说，一个唯品会仓库只会找出一条派单规则明细，一个明细中优先级不能重复，所有只会有一个第一优先级仓库
            return resultList.get(0).getWarehouseId();
        } else {
            //如果有多个，证明优先级重复，需判断库存返回的条码可用库存数。
            List<Long> warehouseIdList = resultList.stream().map(WarehouseRankResult::getWarehouseId).collect(Collectors.toList());
            //仓库可用库存
            Map<Long, BigDecimal> warehouseAvailableStock = new HashMap<>();
//            storageQueryResults.stream().filter(f -> warehouseIdList.contains(f.getCpCPhyWarehouseId()))
//                    .forEach(storageQueryResult -> {
//                        BigDecimal total = warehouseAvailableStock.get(storageQueryResult.getCpCPhyWarehouseId());
//                        total = null == total ? BigDecimal.ZERO : total;
//                        BigDecimal qtyAvailable = storageQueryResult.getQtyAvailable();
//                        qtyAvailable = null == qtyAvailable ? BigDecimal.ZERO : qtyAvailable;
//                        warehouseAvailableStock.put(storageQueryResult.getCpCPhyWarehouseId(), total.add(qtyAvailable));
//                    });
            List<Map.Entry<Long, BigDecimal>> list = new ArrayList(warehouseAvailableStock.entrySet());
            list.sort((o1, o2) -> (o2.getValue().compareTo(o1.getValue())));
            return list.get(0).getKey();
        }
    }

    /**
     * @param warehouseIdList             符合分仓的实体仓ID
     * @param stCSendRuleAddressVipDoList 派单规则-唯品会
     * @return
     */
    private List<WarehouseRankResult> getWarehouseRankResult(List<Long> warehouseIdList, List<StCSendRuleAddressVipDo> stCSendRuleAddressVipDoList) {
        List<WarehouseRankResult> warehouseRankResultList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(stCSendRuleAddressVipDoList)) {
            return warehouseRankResultList;
        }
        stCSendRuleAddressVipDoList.stream().filter(
                f -> StringUtils.isNotBlank(f.getWarehouseRank())
        ).forEach(stCSendRuleAddressVipDo -> {
            List<WarehouseRankResult> warehouseRankList = JsonUtils.jsonToList(WarehouseRankResult.class, stCSendRuleAddressVipDo.getWarehouseRank());
            if (CollectionUtils.isEmpty(warehouseRankList)) {
                return;
            }
            warehouseRankList = warehouseRankList.stream()
                    .filter(fe -> warehouseIdList.contains(fe.getWarehouseId()))
                    .sorted(Comparator.comparing(WarehouseRankResult::getRank)).collect(Collectors.toList());

            warehouseRankResultList.addAll(warehouseRankList);
        });
        return warehouseRankResultList;
    }

    /**
     * 进行预处理
     */
    @Override
    public void prepare() {

    }

    /**
     * 判断是否支持的派单规则类型
     *
     * @param stCSendRuleDO 派单规则
     */
    @Override
    public boolean support(StCSendRuleDO stCSendRuleDO) {
        return SendRuleEtype.VIP.getEtype().equals(stCSendRuleDO.getEtype());
    }
}
