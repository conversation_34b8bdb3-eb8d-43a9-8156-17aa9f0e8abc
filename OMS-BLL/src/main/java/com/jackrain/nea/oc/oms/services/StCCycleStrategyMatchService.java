package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCCycleItemStrategyMapper;
import com.jackrain.nea.oc.oms.mapper.StCCycleRuleStrategyMapper;
import com.jackrain.nea.oc.oms.mapper.StCCycleStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCCycleItemStrategy;
import com.jackrain.nea.oc.oms.model.table.StCCycleRuleStrategy;
import com.jackrain.nea.oc.oms.model.table.StCCycleStrategy;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.BaseModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName StCCycleStrategyMatchService
 * @Description 周期购促销匹配
 * <AUTHOR>
 * @Date 2024/8/20 10:17
 * @Version 1.0
 */
@Slf4j
@Component
public class StCCycleStrategyMatchService {

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private StCCycleStrategyMapper strategyMapper;
    @Autowired
    private StCCycleRuleStrategyMapper ruleStrategyMapper;
    @Autowired
    private StCCycleItemStrategyMapper itemStrategyMapper;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    public Map<Long, List<StCCycleItemStrategy>> match(OcBOrder order, List<OcBOrderItem> orderItemList, Integer type) {
        Map<Long, List<StCCycleItemStrategy>> cycleItemStrategyMap = new HashMap<>();
        // 先根据店铺ID+支付时间 查一下能否匹配的到策略
        Long shopId = order.getCpCShopId();
        Date payTime = order.getPayTime();
        if (payTime == null) {
            return cycleItemStrategyMap;
        }
        Integer cycleNumber = order.getCurrentCycleNumber();
        List<StCCycleStrategy> strategyList = strategyMapper.selectByShopIdAndPayTimeAndType(shopId, payTime, type);
        if (CollectionUtils.isEmpty(strategyList)) {
            return cycleItemStrategyMap;
        }
        List<Long> strategyIdList = strategyList.stream().map(StCCycleStrategy::getId).collect(Collectors.toList());
        // 去匹配策略 现根据策略ID 把所有的平台SKUID都找出来
        List<StCCycleRuleStrategy> ruleStrategyList = ruleStrategyMapper.selectByStrategyIdList(strategyIdList);
        if (CollectionUtils.isEmpty(ruleStrategyList)) {
            return cycleItemStrategyMap;
        }
        // 获取出来里面 RULE_TYPE为 1的数据
        List<StCCycleRuleStrategy> platformSkuRuleStrategyList = ruleStrategyList.stream().filter(ruleStrategy -> ruleStrategy.getRuleType() == 1).collect(Collectors.toList());
        List<StCCycleRuleStrategy> skuRuleStrategyList = ruleStrategyList.stream().filter(ruleStrategy -> ruleStrategy.getRuleType() == 2).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuRuleStrategyList) && CollectionUtils.isEmpty(platformSkuRuleStrategyList)) {
            return cycleItemStrategyMap;
        }
        List<OcBOrderItem> pickUpOrderItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(platformSkuRuleStrategyList)) {
            List<String> alreadyList = new ArrayList<>();
            // platformSkuRuleStrategyList对里面的
            // platformSkuRuleStrategyList中的ruleContent字段 进行分组 转成map
            Map<String, List<StCCycleRuleStrategy>> platformSkuRuleStrategyMap = platformSkuRuleStrategyList.stream().collect(Collectors.groupingBy(StCCycleRuleStrategy::getRuleContent));
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                // 获取里面的平台商品ID
                String numIid = ocBOrderItem.getNumIid();
                List<StCCycleRuleStrategy> matchRuleStrategyList = platformSkuRuleStrategyMap.get(numIid);
                if (CollectionUtils.isEmpty(matchRuleStrategyList)) {
                    continue;
                }
                pickUpOrderItemList.add(ocBOrderItem);
                if (alreadyList.contains(numIid)) {
                    continue;
                }
                alreadyList.add(numIid);
                // 匹配上了 取出来对应的策略id 找到赠送的商品
                Long strategyId = matchRuleStrategyList.get(0).getStrategyId();
                StCCycleStrategy strategy = strategyMapper.selectById(strategyId);
                // 根据策略找到赠送的商品
                List<StCCycleItemStrategy> giftList = itemStrategyMapper.selectByStrategyIdAndCycleNumber(strategyId, cycleNumber);
                if (CollectionUtils.isEmpty(giftList)) {
                    // 如果匹配上了 但是没配置赠送的商品。不再进行匹配(产品要求)
                    continue;
                }
                cycleItemStrategyMap.put(ocBOrderItem.getId(), giftList);
                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.CYCLE_ORDER.getKey(), "匹配到周期购促销活动:" + strategy.getStrategyCode(), null, null, SystemUserResource.getRootUser());
            }
        }

        if (type == 1) {
            // 天猫周期购的话 只走平台商品ID即可
            return cycleItemStrategyMap;
        }
        if (CollectionUtils.isEmpty(skuRuleStrategyList)) {
            return cycleItemStrategyMap;
        }
        // 匹配SKU
        if (CollectionUtils.isNotEmpty(skuRuleStrategyList)) {
            // 获取匹配到了平台商品ID之后的订单明细
            List<OcBOrderItem> restOrderItemList = new ArrayList<>(orderItemList);
            if (CollectionUtils.isNotEmpty(pickUpOrderItemList)) {
                restOrderItemList.removeAll(pickUpOrderItemList);
            }
            for (OcBOrderItem ocBOrderItem : restOrderItemList) {
                // 获取里面的sku编码
                String skuEcode = ocBOrderItem.getPsCSkuEcode();
                Map<String, List<StCCycleRuleStrategy>> skuRuleStrategyMap = skuRuleStrategyList.stream().collect(Collectors.groupingBy(StCCycleRuleStrategy::getRuleContent));
                List<StCCycleRuleStrategy> matchRuleStrategyList = skuRuleStrategyMap.get(skuEcode);
                if (CollectionUtils.isEmpty(matchRuleStrategyList)) {
                    continue;
                }
                pickUpOrderItemList.add(ocBOrderItem);
                // 匹配上了 取出来对应的策略id 找到赠送的商品
                Long strategyId = matchRuleStrategyList.get(0).getStrategyId();
                StCCycleStrategy strategy = strategyMapper.selectById(strategyId);
                // 根据策略找到赠送的商品
                List<StCCycleItemStrategy> giftList = itemStrategyMapper.selectByStrategyIdAndCycleNumber(strategyId, cycleNumber);
                if (CollectionUtils.isEmpty(giftList)) {
                    // 如果匹配上了 但是没配置赠送的商品。不再进行匹配(产品要求)
                    continue;
                }
                // 如果配置了的话 则记录下匹配到的giftList
                cycleItemStrategyMap.put(ocBOrderItem.getId(), giftList);
                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.CYCLE_ORDER.getKey(), "匹配到周期购促销活动:" + strategy.getStrategyCode(), null, null, SystemUserResource.getRootUser());
            }
        }
        return cycleItemStrategyMap;
    }

    public List<OcBOrderItem> buildGiftOrderItemList(Map<Long, List<StCCycleItemStrategy>> cycleItemStrategyMap, OcBOrder ocBOrder) {
        if (cycleItemStrategyMap.isEmpty()) {
            return null;
        }
        Integer isCycle = ocBOrder.getIsCycle();
        List<OcBOrderItem> giftOrderItemList = new ArrayList<>();
        for (Long itemId : cycleItemStrategyMap.keySet()) {
            List<StCCycleItemStrategy> cCycleItemStrategyList = cycleItemStrategyMap.get(itemId);
            OcBOrderItem ocBOrderItem = ocBOrderItemMapper.selectById(itemId);
            for (StCCycleItemStrategy strategy : cCycleItemStrategyList) {
                OcBOrderItem item = new OcBOrderItem();
                item.setId(sequenceUtil.buildOrderItemSequenceId());
                item.setModifierename(SystemUserResource.ROOT_USER_NAME);
                item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
                item.setVersion(0L);
                //活动编号. 默认赋值为null
                item.setActiveId(null);
                BaseModelUtil.initialBaseModelSystemField(item);
                //退货金额.默认为0
                item.setAmtRefund(BigDecimal.ZERO);
                //使用积分
                item.setBuyerUsedIntegral(0L);
                //分销价格。默认为0
                item.setDistributionPrice(BigDecimal.ZERO);
                //福袋条码
                item.setGiftbagSku(null);
                //组合名称
                item.setGroupName(null);
                //是否已经占用库存
                item.setIsAllocatestock(0);
                //买家是否已评价
                item.setIsBuyerRate(0);
                //是否是赠品
                item.setIsGift(1);
                item.setGiftType("1");
                item.setIsGiftSplit(strategy.getSplitType());
                //实缺标记
                item.setIsLackstock(0);
                item.setIsPresalesku(0);
                //商品数字编号
                item.setNumIid("0");
                //订单编号
                item.setOcBOrderId(ocBOrder.getId());
                item.setTid(ocBOrder.getTid());
                //子订单编号(明细编号)
                item.setOoid(ocBOrderItem.getOoid());
                //平摊金额
                item.setOrderSplitAmt(BigDecimal.ZERO);
                //商品路径
                item.setPicPath(null);
                //已退数量。默认为0
                item.setQtyRefund(BigDecimal.ZERO);
                // 平台退款编号
                item.setRefundId(null);
                //退款状态
                // 如果是退款完成，或者是交易关闭 状态=6
                item.setRefundStatus(0);
                BigDecimal qty = new BigDecimal(strategy.getQty());
                //数量
                item.setQty(qty);
                this.initProductSku(strategy.getSkuCode(), item);
                //标题
                item.setTitle(null);
                item.setTid(ocBOrder.getTid());

                // 平台售价
                item.setPrice(new BigDecimal("0.1"));
                // 成交单价
                item.setPriceActual(new BigDecimal("0.1"));
                // 成交金额
                item.setRealAmt(new BigDecimal("0.1").multiply(qty));
                if (isCycle != null && isCycle == 1) {
                    // 平台售价
                    item.setPrice(BigDecimal.ZERO);
                    // 成交单价
                    item.setPriceActual(BigDecimal.ZERO);
                    // 成交金额
                    item.setRealAmt(BigDecimal.ZERO);
                }
                item.setAmtDiscount(BigDecimal.ZERO);
                item.setAdjustAmt(BigDecimal.ZERO);
                giftOrderItemList.add(item);
            }
        }
        return giftOrderItemList;
    }

    private void initProductSku(String skuCode, OcBOrderItem item) {
        ProductSku productSku = psRpcService.selectProductSku(skuCode);
        if (productSku != null) {
            item.setSex(productSku.getSex());
            // 吊牌价
            item.setPriceTag(productSku.getPricelist());
            item.setPsCProId(productSku.getProdId());
            item.setBarcode(productSku.getBarcode69());
            // ProECode
            item.setPsCProEcode(productSku.getProdCode());
            item.setPsCProEname(productSku.getName());
            item.setPsCSkuPtEcode(productSku.getSkuEcode());
            item.setPsCSkuEcode(productSku.getSkuEcode());
            item.setPsCClrEcode(productSku.getColorCode());
            item.setPsCClrEname(productSku.getColorName());
            item.setPsCClrId(productSku.getColorId());
            item.setStandardWeight(productSku.getWeight());
            item.setSkuSpec(productSku.getSkuSpec());
            item.setPsCSizeEcode(productSku.getSizeCode());
            item.setPsCSizeEname(productSku.getSizeName());
            item.setPsCSizeId(productSku.getSizeId());
            item.setPsCProMaterieltype(productSku.getMaterialType());
            item.setPsCSkuId(productSku.getId());
            // 标准价
            item.setPriceList(BigDecimal.ZERO);
            if ("Y".equals(productSku.getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            item.setMDim4Id(productSku.getMDim4Id());
            item.setMDim6Id(productSku.getMDim6Id());
            if (productSku.getSkuType() == SkuType.COMBINE_PRODUCT
                    || productSku.getSkuType() == SkuType.GIFT_PRODUCT) {
                item.setProType(NumberUtils.toLong(SkuType.NO_SPLIT_COMBINE + ""));
                item.setGiftbagSku(productSku.getSkuEcode());
                item.setQtyGroup(item.getQty());
                item.setGroupGoodsMark("CG" + item.getId());
            } else {
                item.setProType(NumberUtils.toLong(SkuType.NORMAL_PRODUCT + ""));
            }
            item.setPsCSkuEname(productSku.getSkuName());
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }
}
