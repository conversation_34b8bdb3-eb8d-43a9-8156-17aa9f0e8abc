package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @author: 夏继超
 * @since: 2019/3/12
 * create at : 2019/3/12 20:57
 */
@Slf4j
@Component
//@Transactional
public class AddGiftService {
    //订单明细表
    private static final String ORDER_ITEM_TABLE_NAME = "OC_B_ORDER_ITEM";
    @Autowired
    SaveBillService saveBillService;
    @Autowired
    OcBOrderMapper ocBorderMapper;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    OcBorderUpdateService ocBorderUpdateService;
    @Autowired
    private OcBorderUpdateServiceExt ocBorderUpdateServiceExt;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    OcBOrderItemMapper itemMapper;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OmsOrderDifferenPriceService omsOrderDifferenPriceService;
    @Autowired
    OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;


    /**
     * 新增商品
     *
     * @param obj  传入的参数
     * @param user 当前登录用户
     * @return
     */
    public ValueHolder addGift(JSONObject obj, User user) throws NDSException {
        ValueHolder vh = new ValueHolder();
        JSONObject ocBorderDto1 = obj.getJSONObject("OcBorderDto");
        String id = ocBorderDto1.get("id").toString();
        JSONArray array = JSON.parseArray(id);
        //获取主表的id数组
        Object[] ids = array.toArray();
        JSONArray ocBorderItemDto = obj.getJSONArray("OcBorderItemDto");
        //订单明细
        //将json 数组转为对象
        List<OcBOrderItem> orderItemResults = JSON.parseArray(ocBorderItemDto.toJSONString(), OcBOrderItem.class);
        //将Json转为对象
        //判断是否是给主明细批量新增商品
        if (ids.length != 0) {
            //redis锁单
            if (ids.length == 1) {
                String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(Long.valueOf(ids[0].toString()));
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {

                        // 11......
                        AddGiftService bean = ApplicationContextHandle.getBean(AddGiftService.class);
                        return bean.addOneGiftPlus(user, vh, ids[0], orderItemResults);

                    } else {
                        throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
                    }
                } catch (Exception ex) {
                    throw new NDSException(Resources.getMessage("新增商品失败" + ex.getMessage(), user.getLocale()));
                } finally {
                    redisLock.unlock();
                }
            } else {
                int fail = 0;
                for (int i = 0; i < ids.length; i++) {
                    String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(Long.valueOf(ids[i].toString()));
                    RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                    try {
                        if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                            try {
                                //1..222222222
                                AddGiftService bean = ApplicationContextHandle.getBean(AddGiftService.class);
                                fail = bean.getFail(user, vh, ids[i], orderItemResults, fail);
                            } catch (Exception e) {
                                vh.put("code", ResultCode.FAIL);
                                vh.put("message", Resources.getMessage("新增异常", user.getLocale()));
                                fail++;
                            }
                        } else {
                            throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!", user.getLocale()));
                        }
                    } catch (Exception ex) {
                        throw new NDSException(Resources.getMessage("订单更新锁单错误！", user.getLocale()));
                    } finally {
                        redisLock.unlock();
                    }
                }
                vh.put("code", ResultCode.SUCCESS);
                Integer success = ids.length - fail;
                vh.put("message", Resources.getMessage("成功新增商品" + success + "条，新增商品失败了" + fail + "条数据", user.getLocale()));
            }
        } else {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("请勾选明细！", user.getLocale()));
            return vh;
        }
        return vh;
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder addOneGift(User user, ValueHolder vh, Object id, List<OcBOrderItem> orderItemResults) throws NDSException {
        try {

            //保存明细表
            OcBOrder borderDto = ocBorderMapper.selectById(Long.valueOf(id.toString()));
            List<Long> list = this.saveOrderItemInfo(user, borderDto, orderItemResults);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("保存成功!", user.getLocale()));
            //保存日志
            updateOcOrder(user, borderDto, orderItemResults);
            try {
                OcBOrderRelation relation = new OcBOrderRelation();
                //添加头表的信息
                relation.setOrderInfo(borderDto);
                //添加明细的信息
                QueryWrapper wrapper = new QueryWrapper();
                wrapper.in("id", list);
                wrapper.eq("oc_b_order_id", Long.valueOf(id.toString()));
                wrapper.ne("pro_type", 4);
                List list1 = itemMapper.selectList(wrapper);
                relation.setOrderItemList(list1);
                ValueHolderV14 valueHolderV14 = ocBorderUpdateServiceExt.updateOrder(relation, user);
                if (!valueHolderV14.isOK()) {
                    throw new NDSException(Resources.getMessage(" 调用编辑保存服务异常!", user.getLocale()));
                }
            } catch (Exception e) {
                throw new NDSException(Resources.getMessage(" 调用编辑保存服务异常!-》" + ExceptionUtil.getMessage(e), user.getLocale()));
            }
            return vh;
        } catch (Exception e) {
            throw new NDSException(Resources.getMessage("服务调用异常-》" + ExceptionUtil.getMessage(e), user.getLocale()));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder addOneGiftPlus(User user, ValueHolder vh, Object id, List<OcBOrderItem> orderItemResults) throws NDSException {
        ValueHolder v14 = new ValueHolder();
        try {
            // 1 . 先判断明细是不是组合商品 （组合商品走一个逻辑，不是组合商品另一个逻辑）
            if (CollectionUtils.isEmpty(orderItemResults)) {
                v14.put("code", ResultCode.FAIL);
                v14.put("message", "亲选择商品添加");
                return v14;
            }
            for (int i = 0; i < orderItemResults.size(); i++) {
                OcBOrderItem item = orderItemResults.get(i);
                ProductSku productSku = psRpcService.selectProductSku(item.getPsCSkuEcode());
                if (productSku == null) {
                    continue;
                }
//                商品类型(0:正常,1:福袋,2:组合,3:预售)
                OcBOrder borderDto = ocBorderMapper.selectById(Long.valueOf(id.toString()));
                List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
                ocBOrderItems.add(item);
                //组装明细表参数
                List<Long> list = this.saveOrderItemInfo(user, borderDto, orderItemResults);
                // 添加日志
                updateOcOrder(user, borderDto, ocBOrderItems);
                if (2 == productSku.getSkuType()) {
                    //成交金额默认为0，标记为手工增加商品，调用组合商品转换实际商品服务(如果是组合商品)
                    try {
                        OcBOrderRelation relation = new OcBOrderRelation();
                        //添加头表的信息
                        relation.setOrderInfo(borderDto);
                        //添加明细的信息
                        QueryWrapper wrapper = new QueryWrapper();
                        wrapper.in("id", list);
                        wrapper.eq("oc_b_order_id", Long.valueOf(id.toString()));
                        wrapper.ne("pro_type", 4);
                        List list1 = itemMapper.selectList(wrapper);
                        relation.setOrderItemList(list1);
                        boolean isSuccess = omsConstituteSplitService.startExchangeCombineGiftProduct(relation, user);
                        if (!isSuccess) {
                            throw new NDSException("拆分组合商品失败");
                        }

                    } catch (Exception e) {
                        throw new NDSException(Resources.getMessage(e.getMessage(), user.getLocale()));
                    }
                }
                try {
                    OcBOrderRelation relation = new OcBOrderRelation();
                    //添加头表的信息
                    relation.setOrderInfo(borderDto);
                    //添加明细的信息
                    QueryWrapper wrapper = new QueryWrapper();
                    wrapper.in("id", list);
                    wrapper.eq("oc_b_order_id", Long.valueOf(id.toString()));
                    List list1 = itemMapper.selectList(wrapper);
                    relation.setOrderItemList(list1);
                    ValueHolderV14 valueHolderV14 = ocBorderUpdateServiceExt.updateOrder(relation, user);
                    if (!valueHolderV14.isOK()) {
                        throw new NDSException(Resources.getMessage(valueHolderV14.getMessage(), user.getLocale()));
                    }
                } catch (Exception e) {
                    throw new NDSException(Resources.getMessage(e.getMessage(), user.getLocale()));
                }

                return v14;
            }
        } catch (Exception e) {
            throw new NDSException(Resources.getMessage(e.getMessage(), user.getLocale()));
        }
        v14.put("code", ResultCode.SUCCESS);
        v14.put("message", "新增商品成功");
        return v14;
    }


    @Transactional(rollbackFor = Exception.class)
    public int getFail(User user, ValueHolder vh, Object id, List<OcBOrderItem> orderItemResults, int fail) throws NDSException {
        try {
            OcBOrder borderDto = ocBorderMapper.selectById(Long.valueOf(id.toString()));
            //更新从表
            List<Long> list = this.saveOrderItemInfo(user, borderDto, orderItemResults);
            //更新主表
            updateOcOrder(user, borderDto, orderItemResults);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", Resources.getMessage("保存成功!", user.getLocale()));

            OcBOrderRelation relation = new OcBOrderRelation();
            //添加头表的信息
            relation.setOrderInfo(borderDto);
            //添加明细的信息
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.in("id", list);
            wrapper.eq("oc_b_order_id", Long.valueOf(id.toString()));
            wrapper.ne("pro_type", 4);
            List list1 = itemMapper.selectList(wrapper);
            relation.setOrderItemList(list1);
            ValueHolderV14 valueHolderV14 = ocBorderUpdateServiceExt.updateOrder(relation, user);
            if (!valueHolderV14.isOK()) {
                fail++;
                throw new NDSException(Resources.getMessage(" 调用编辑保存服务异常!-》" + valueHolderV14.getMessage(), user.getLocale()));
            }
        } catch (Exception e) {
            fail++;
            throw new NDSException(Resources.getMessage("服务调用异常-》" + e.getMessage(), user.getLocale()));
        }
        return fail;
    }

    /**
     * 更新主表
     *
     * @param loginUser       当前用户
     * @param ocBorderDto     订单对象
     * @param ocBorderItemDto 明细对象集合
     */
    public void updateOcOrder(User loginUser, OcBOrder ocBorderDto, List<OcBOrderItem> ocBorderItemDto) throws NDSException {
        for (OcBOrderItem borderItemDto : ocBorderItemDto) {
            //f)添加成功后，成功后调用订单日志服务
            try {
                omsOrderLogService.addUserOrderLog(ocBorderDto.getId(), ocBorderDto.getBillNo(), OrderLogTypeEnum.GIFT_ADD.getKey(), borderItemDto.getPsCSkuEcode() + " 商品添加成功！", null, null, loginUser);
            } catch (Exception e) {
                log.debug("日志服务异常:" + e.getMessage());
                throw new NDSException(Resources.getMessage("日志服务繁忙", loginUser.getLocale()));
            }
        }
    }

    /**
     * 保存明细
     *
     * @param user             用户
     * @param borderDto        订单
     * @param orderItemResults 明细
     * @throws NDSException 异常
     * @throws IOException  IO异常
     */
    public List<Long> saveOrderItemInfo(User user, OcBOrder borderDto,
                                        List<OcBOrderItem> orderItemResults) throws NDSException {
        List<Long> itemIds = new ArrayList<>();
        for (OcBOrderItem orderItemResult : orderItemResults) {
            //订单状态
            Integer orderStatus = borderDto.getOrderStatus();
            //条码
            String pscSkueCode = orderItemResult.getPsCSkuEcode();
            //数量
            BigDecimal qtyBill = orderItemResult.getQty();
            //商品标准
            String title = orderItemResult.getTitle();
            //商品路径
            String picPath = orderItemResult.getPicPath();

            if (StringUtils.isEmpty(pscSkueCode)) {
                throw new NDSException(Resources.getMessage("条码不能为空", user.getLocale()));
            }
            //检查条码状态是否可用
            Map<String, Integer> queryCount = psRpcService.queryCount(pscSkueCode);
            Integer checkEcodeStaus = queryCount.get("count");
            if (checkEcodeStaus == 0) {
                throw new NDSException(Resources.getMessage(String.format("条码:%s不存在或未启用!",
                                pscSkueCode),
                        user.getLocale()));
            }
            //订单编号
            Long orderId = borderDto.getId();
            //新增

            //数量为空,默认为1
            if (qtyBill == null) {
                qtyBill = new BigDecimal(1);
            }
            OcBOrderItem ocBorderItem = new OcBOrderItem();
            ocBorderItem.setId(ModelUtil.getSequence(ORDER_ITEM_TABLE_NAME));
            itemIds.add(ocBorderItem.getId());
            //数量
            ocBorderItem.setQty(qtyBill);
            List<Integer> ids = new ArrayList<>();

            //调俊鹏服务
            Map map = psRpcService.querySku(pscSkueCode);
            //条码ID
            Long skuId = null;
            if (map != null) {
                String data = JSON.toJSONString(map.get("data"));
                JSONArray jsonArray = JSON.parseArray(data);
                if (jsonArray != null && jsonArray.size() != 0) {
                    for (Object object : jsonArray) {
                        JSONObject psJson = (JSONObject) object;
                        //国标码
                        String gbcode = psJson.getString("GBCODE");
                        ocBorderItem.setBarcode(gbcode);
                        //商品名称
                        String proEname = psJson.getString("PS_C_PRO_ENAME");
                        ocBorderItem.setPsCProEname(proEname);
                        //商品货号
                        String proEcode = psJson.getString("PS_C_PRO_ECODE");
                        //标准价 和性别
                        ocBorderItem.setSex(psJson.getLong("sex"));
                        ocBorderItem.setPriceTag(psJson.getBigDecimal("tagPrice"));
                        ocBorderItem.setPsCProEcode(proEcode);
                        //商品ID
                        Long cProId = psJson.getLong("PS_C_PRO_ID");
                        ocBorderItem.setPsCProId(cProId);
                        //规格=尺寸+颜色
                        String spec = psJson.getString("SPEC");
                        ocBorderItem.setSkuSpec(spec);
                        //标准价
                        BigDecimal pricelist = psJson.getBigDecimal("PRICELIST");
                        ocBorderItem.setPriceList(pricelist);
                        //成交价格
                        ocBorderItem.setPrice(pricelist);
                        //标准重量
                        BigDecimal weight = psJson.getBigDecimal("WEIGHT");
                        ocBorderItem.setStandardWeight(weight);
                        //判断是否商品? 商品为1否则为0
                        Integer isGift = orderItemResult.getIsGift();
                        ocBorderItem.setIsGift(isGift);
                        //如果为商品（或者不是商品）,标准价,成交价重置为0

                        ocBorderItem.setPriceList(BigDecimal.ZERO);
                        ocBorderItem.setPrice(BigDecimal.ZERO);

                        //单行实际成交金额 默认为成交价格*数量
                        if (pricelist != null) {
                            //非商品才计算价格

                            ocBorderItem.setRealAmt(BigDecimal.ZERO);
                        }
                        //skuID
                        skuId = psJson.getLong("skuId");
                    }
                }
                //分销价格
                ocBorderItem.setDistributionPrice(BigDecimal.ZERO);
            }
            if (skuId != null) {
                ProductSku productSku = psRpcService.selectProductById(String.valueOf(skuId));
                if (productSku != null) {
                    String colorEcode = productSku.getColorCode();
                    String colorName = productSku.getColorName();
                    Long colorId = productSku.getColorId();
                    String sizeName = productSku.getSizeName();
                    String sizeEcode = productSku.getSizeCode();
                    Long sizeId = productSku.getSizeId();
                    //颜色ID
                    ocBorderItem.setPsCClrId(colorId);
                    //颜色编码
                    ocBorderItem.setPsCClrEcode(colorEcode);
                    //颜色名字
                    ocBorderItem.setPsCClrEname(colorName);
                    //尺寸id
                    ocBorderItem.setPsCSizeId(sizeId);
                    //尺寸编码
                    ocBorderItem.setPsCSizeEcode(sizeEcode);
                    //尺寸名称
                    ocBorderItem.setPsCSizeEname(sizeName);

                    // 增加品类信息 20220923
                    ocBorderItem.setMDim4Id(productSku.getMDim4Id());
                    ocBorderItem.setMDim6Id(productSku.getMDim6Id());
                    if ("Y".equals(productSku.getIsEnableExpiry())) {
                        ocBorderItem.setIsEnableExpiry(1);
                    } else {
                        ocBorderItem.setIsEnableExpiry(0);
                    }
                }
            }

            //标题
            ocBorderItem.setTitle(title);
            //商品路径
            ocBorderItem.setPicPath(picPath);
            //实物报缺
            ocBorderItem.setIsLackstock(0);
            //时间
            ocBorderItem.setCreationdate(new Date(System.currentTimeMillis()));
            //条码编码
            ocBorderItem.setPsCSkuEcode(pscSkueCode);
            //创建人用户名
            ocBorderItem.setOwnername(user.getName());
            //商品数字编号
            ocBorderItem.setNumIid("0");
            //是否占用库存
            ocBorderItem.setIsAllocatestock(0);
            //整单平摊金额
            ocBorderItem.setOrderSplitAmt(BigDecimal.ZERO);
            //优惠金额
            ocBorderItem.setAmtDiscount(BigDecimal.ZERO);
            //调整金额
            ocBorderItem.setAdjustAmt(BigDecimal.ZERO);
            //Sku id
            ocBorderItem.setPsCSkuId(skuId);
            //退款状态
            ocBorderItem.setRefundStatus(0);
            //退款平台编号
            ocBorderItem.setRefundId("0");
            //已退数量
            ocBorderItem.setQtyRefund(BigDecimal.ZERO);
            //买家是否已评价
            ocBorderItem.setIsBuyerRate(0);
            //发货状态
            ocBorderItem.setIsSendout(0);
            //预售状态
            ocBorderItem.setIsPresalesku(0);
            //退货金额
            ocBorderItem.setAmtRefund(orderItemResult.getAmtRefund());
            //组合名称
            ocBorderItem.setGroupName(orderItemResult.getGroupName());
            //商品数字编号
            ocBorderItem.setNumIid(orderItemResult.getNumIid());
            // 一米有品
            ocBorderItem.setReserveVarchar04(orderItemResult.getReserveVarchar04());
            //子订单编号
            ocBorderItem.setOoid(orderItemResult.getOoid());
            // 是否手工新增商品
            ocBorderItem.setIsManualAdd("1");
            //发货失败次数
            ocBorderItem.setOuterrcount(orderItemResult.getOuterrcount());
            ocBorderItem.setIsactive("Y");
            ocBorderItem.setAdOrgId((long) user.getOrgId());
            ocBorderItem.setAdClientId((long) user.getClientId());

            //订单编号关联订单明细
            ocBorderItem.setOcBOrderId(orderId);
            //查询订单是否存在其他明细,若存在,查询TID是否一致,若一致则该条码的TID为其他的TID,如不一致则为空
            Integer checkOrderItemNum = itemMapper.checkOrderItemNum(orderId);
            //平台单号 (明细中tid 和主表中sourceCode保持一致)
            String sourceCode = borderDto.getSourceCode();
            if (checkOrderItemNum > 0) {
                List<String> tids = itemMapper.getTids(orderId);
                for (String tid : tids) {
                    if (sourceCode.equals(tid)) {
                        ocBorderItem.setTid(borderDto.getSourceCode());
                    } else {
                        ocBorderItem.setTid("0");
                    }
                }
            } else {
                ocBorderItem.setTid(borderDto.getSourceCode());
            }
            //如果明细中条码不存在,则新增
            Integer checkSkuNum = itemMapper.checkSkuNum(pscSkueCode, orderId);
            if (checkSkuNum == 0) {
                //Ture 是 虚拟条码
                boolean differenPriceSku = false;
                try {
                    differenPriceSku = omsOrderDifferenPriceService.isDifferenPriceSku(
                            borderDto.getCpCShopId(), pscSkueCode);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //虚拟条码不允许新增
                if (StringUtils.isNotEmpty(pscSkueCode)) {
                    //不是虚拟条码才可以新增明细
                    if (!differenPriceSku) {
                        itemMapper.insert(ocBorderItem);
                    } else {
                        throw new NDSException(Resources.getMessage("虚拟条码不允许新增", user.getLocale()));
                    }
                }

            } else {
                //如果明细中条码存在,则数量累加
                OcBOrderItem orderItem = itemMapper.queryOrderItem(pscSkueCode, orderId);
                itemIds.add(orderItem.getId());
                BigDecimal price = orderItem.getPrice();
                //单行实际成交金额(后期需要,成交金额可编辑)
                BigDecimal qty = orderItem.getQty();
                BigDecimal qtySum = qty.add(qtyBill);
                orderItem.setQty(qtySum);
                orderItem.setModifiername(user.getName());
                orderItem.setModifieddate(new Date(System.currentTimeMillis()));
                if (price != null) {
                    BigDecimal realAmt = qtySum.multiply(price);
                    orderItem.setRealAmt(realAmt);
                }
                omsOrderItemService.updateOcBOrderItem(orderItem, orderId);

            }
        }
        return itemIds;
    }

}
