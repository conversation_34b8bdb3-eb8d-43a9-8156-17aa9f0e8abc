package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.jdbc.datasource.TargetDataSource;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: heliu
 * @since: 2019/3/15
 * create at : 2019/3/15 9:51
 */
@Component
@Slf4j
public class OmsOrderModifyHeadService {

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderCheckAndUpdateService omsOrderCheckAndUpdateService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    /**
     * 针对没有退款成功的明细做更新操作
     *
     * @param ocBOrderRelation 对象
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean doCallSummary(OcBOrderRelation ocBOrderRelation) {

        try {
            //订单Id
            Long orderId = ocBOrderRelation.getOrderInfo().getId();
            Integer paltform = ocBOrderRelation.getOrderInfo().getPlatform();
            //未退款成功明细
            List<OcBOrderItem> selectUnSuccessRefundList = omsOrderItemService.selectUnSuccessRefund(orderId);
            //平台为2 则更新头表
            if (PlatFormEnum.TAOBAO.getCode().equals(paltform)) {
                changeOrder(selectUnSuccessRefundList, ocBOrderRelation.getOrderInfo());
            } else {
                //则调用订单平摊金额服务
                omsOrderCheckAndUpdateService.doCheckAndUpdateBlanceMoney(ocBOrderRelation);
            }
            return true;
        } catch (Exception ex) {
            log.error(LogUtil.format("更新头表服务异常,异常信息为:{}",ocBOrderRelation.getOrderId()), Throwables.getStackTraceAsString(ex));
            return false;
        }
    }

    /**
     * 更新头表数据
     *
     * @param selectUnSuccessRefundList 查找非退款明细
     * @param ocBOrderDto               订单对象
     * @return boolean
     */
    public boolean changeOrder(List<OcBOrderItem> selectUnSuccessRefundList, OcBOrder ocBOrderDto) {
        List<String> stringArrayList = new ArrayList<>();
        BigDecimal weightCountTotal = BigDecimal.ZERO;
        BigDecimal goodsCountTotal = BigDecimal.ZERO;
        for (OcBOrderItem ocBOrderItem : selectUnSuccessRefundList) {
            Long psCskuId = ocBOrderItem.getPsCSkuId();
            stringArrayList.add(String.valueOf(psCskuId));
            //明细所有重量之和
            weightCountTotal = weightCountTotal.add((ocBOrderItem.getStandardWeight() == null
                    ? BigDecimal.ZERO : ocBOrderItem.getStandardWeight()).multiply(ocBOrderItem.getQty() == null
                    ? BigDecimal.ZERO : ocBOrderItem.getQty()));
            //明细未退款商品数量之和
            goodsCountTotal = goodsCountTotal.add((ocBOrderItem.getQty() == null
                    ? BigDecimal.ZERO : ocBOrderItem.getQty()));
        }
        //设置日期格式
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // new Date()为获取当前系统时间
        OcBOrder ocBOrder = new OcBOrder();
        //830版本：正常流程 系统备注不需要展示
        ocBOrder.setSysremark("");
        //ocBOrder.setSysremark(ocBOrderDto.getId() + "订单自动更新头表完成!操作时间" + df.format(new Date()));
        ocBOrder.setModifierename(SystemUserResource.ROOT_USER_NAME);
        ocBOrder.setModifieddate(new Date());
        ocBOrder.setWeight(weightCountTotal);
        ocBOrder.setQtyAll(goodsCountTotal);
        int count = ocBOrderItemMapper.selectCountForOrder(ocBOrderDto.getId());
        ocBOrder.setSkuKindQty(new BigDecimal(count));
        String join = StringUtils.join(stringArrayList, ",");
        ocBOrder.setAllSku(SplitMessageUtil.splitMsgBySize(join,SplitMessageUtil.SIZE_100));
        ocBOrder.setId(ocBOrderDto.getId());
        return omsOrderService.updateOrderInfo(ocBOrder);
    }
}
