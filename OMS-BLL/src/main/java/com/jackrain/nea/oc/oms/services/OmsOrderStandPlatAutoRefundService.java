package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.ip.api.others.StandPlatRefundAutomaticCmd;
import com.jackrain.nea.ip.api.others.StandPlatRefundIntoWareHouseCmd;
import com.jackrain.nea.ip.model.others.StandPlatRefundAgreeModel;
import com.jackrain.nea.ip.model.others.StandPlatRefundAutomaticModel;
import com.jackrain.nea.ip.model.others.StandPlatRefundIntoWareHouseModel;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnBfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefudStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnBfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.StandplatOrderTransferUtil;
import com.jackrain.nea.oc.oms.util.StandplatRefundOrderTransferUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

//import com.jackrain.nea.st.api.StOrderAutoRefundCmd;

/**
 * @Author: lh
 * @Date: 2021/8/19 00:17
 * @Description: 通用自动退款
 */
@Slf4j
@Component
public class OmsOrderStandPlatAutoRefundService extends AbstractOmsOrderPolicyService {
    @Reference(group = "ip", version = "1.4.0")
    private StandPlatRefundAutomaticCmd standPlatRefundAutomaticCmd;
    @Reference(group = "ip", version = "1.4.0")
    private StandPlatRefundIntoWareHouseCmd standPlatRefundIntoWareHouseCmd;
    //    @Reference(group = "ip", version = "1.4.0")
//    private StOrderAutoRefundCmd stOrderAutoRefundCmd;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private CpRpcService cpRpcService;


    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private OcBReturnBfSendMapper ocBReturnBfSendMapper;
    @Autowired
    private IpBStandplatRefundItemMapper ipBStandplatRefundItemMapper;
    @Autowired
    private OcBReturnOrderMapper orderMapper;

    @Autowired
    private StandplatOrderTransferUtil standplatOrderTransferUtil;

    @Autowired
    private ReturnOrderAuditService returnOrderAuditService;

    @Autowired
    protected StandplatRefundOrderTransferUtil standplatRefundOrderTransferUtil;

    /**
     * 调用退款接口
     *
     * @param refundRelation
     * @param user
     * @param ocBOrder
     * @param ocBOrderItems
     * @param isLogisticsIntercept 是否物流拦截触发
     * @return
     */
    public boolean executeAutoRefund(OmsStandPlatRefundRelation refundRelation, User user,
                                     OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, boolean isLogisticsIntercept) {
        log.info(LogUtil.format("开始进入AG退款方法，refundRelation:{}, isLogisticsIntercept:{}",
                "OmsOrderStandPlatAutoRefundService.executeAutoRefund"), refundRelation, isLogisticsIntercept);

        if (ocBOrder == null) {
            ocBOrder = refundRelation.getOmsOrderRelation().get(0).getOcBOrder();
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(ocBOrderItems)) {
            ocBOrderItems = refundRelation.getOmsOrderRelation().get(0).getOcBOrderItems();
        }
        //退单表
        IpBStandplatRefund ipBStandplatRefund = refundRelation.getIpBStandplatRefund();
        //剔除赠品
        List<OcBOrderItem> targetItemList = ocBOrderItems.stream().filter(x -> Integer.valueOf(0).equals(x.getIsGift())).collect(Collectors.toList());
        Long shopId = ocBOrder.getCpCShopId();
        boolean toAgByShopStrategy = this.isToAgByShopStrategy(shopId);
        if (!toAgByShopStrategy) {
            this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "店铺（AG/SA）状态：未启用", user);
            return false;
        }
        this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "店铺（AG/SA）状态：已开启", user);
        //检验店铺策略
//        if (!this.checkRefundStrategy(shopId, ipBStandplatRefund.getReturnReason())) {
//            //没有符合的策略:没有对应有效退款策略
//            this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "店铺退款策略：无有效退款策略", user);
//            return false;
//        };
        Integer platmform = standplatRefundOrderTransferUtil.getStandplatRefundPlatmform(ipBStandplatRefund);


        ValueHolderV14 v14 = null;
        /*物流拦截，且产品已经调研的平台[拼多多、快手]走这里*/
        if (isLogisticsIntercept) {
            log.debug(LogUtil.format("物流拦截触发-调用AG退款接口，订单ID:{},平台[{}]编码:{}",
                    "OmsOrderStandPlatAutoRefundService.executeAutoRefund"), ocBOrder.getId(), PlatFormEnum.getName(platmform), platmform, isLogisticsIntercept);

            /*【快手】原发货前接口*/
            /*【抖音】20240708，抖音和快手一样的逻辑*/
            if (PlatFormEnum.KUAISHOU.getCode().equals(platmform)
                    || PlatFormEnum.DOU_YIN.getCode().equals(platmform)) {
                Integer returnStatus = ipBStandplatRefund.getReturnStatus();
                if (IpBStandplatRefudStatusEnum.SUCCESS.getVal().equals(returnStatus)) {
                    this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE,
                            "通用取消发货,退单已退款完成，直接标记完成" + ":退单号:" + ipBStandplatRefund.getReturnNo(), user);
                    return true;
                }

                v14 = this.executeAutoRefundBeforeShipment(ocBOrder, targetItemList, ipBStandplatRefund);
                this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE,
                        "通用取消发货" + (v14.isOK() ? "成功" : "失败：" + v14.getMessage()) + ":退单号:" + ipBStandplatRefund.getReturnNo(), user);
                return v14.isOK();
            }

            /*【拼多多】新增接口*/
            if (PlatFormEnum.PINDUODUO.getCode().equals(platmform)) {
                Integer returnStatus = ipBStandplatRefund.getReturnStatus();
                if (IpBStandplatRefudStatusEnum.SUCCESS.getVal().equals(returnStatus)) {
                    this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE,
                            "通用取消发货,退单已退款完成，直接标记完成" + ":退单号:" + ipBStandplatRefund.getReturnNo(), user);
                    return true;
                }

                v14 = this.executeCloudHubRefundAgree(ocBOrder, targetItemList, ipBStandplatRefund);
                this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE,
                        "通用取消发货" + (v14.isOK() ? "成功" : "失败：" + v14.getMessage()) + ":退单号:" + ipBStandplatRefund.getReturnNo(), user);
                return v14.isOK();
            }
        }

        log.debug(LogUtil.format("原有逻辑-调用AG退款接口，订单ID:{},平台[{}]编码:{},是否物流拦截触发:{}",
                "OmsOrderStandPlatAutoRefundService.executeAutoRefund"), ocBOrder.getId(), PlatFormEnum.getName(platmform), platmform, isLogisticsIntercept);

        //判断是否发货前还是发货后
        boolean statusFront = this.checkOrderStatusFront(ocBOrder.getOrderStatus());
        /*开心果，凯儿得乐，视频号走这里*/
        if (PlatFormEnum.KAI_ER_DE_LE.getCode().equals(platmform) || PlatFormEnum.SHIPH.getCode().equals(platmform)) {
            v14 = returnOrderAuditService.invokeBeforeSendAg(ipBStandplatRefund);
        } else {
            Integer returnStatus = ipBStandplatRefund.getReturnStatus();
            if (IpBStandplatRefudStatusEnum.SUCCESS.getVal().equals(returnStatus)) {
                this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, "通用取消发货,退单已退款完成，直接标记完成" + ":退单号:" + ipBStandplatRefund.getReturnNo(), user);
                return true;
            }
            if (statusFront) {
                v14 = this.executeAutoRefundBeforeShipment(ocBOrder, targetItemList, ipBStandplatRefund);
            } else {
                v14 = this.executeAutoRefundAfterShipment(ocBOrder, targetItemList, ipBStandplatRefund);
            }
        }
        this.addReturnOrderAutoRefundLog(ocBOrder.getId(), OrderLogTypeEnum.AG_SEND_CANCLE, (statusFront == true ? "发货前通用取消发货" : "通用取消发货") + (v14.isOK() ? "成功" : "失败：" + v14.getMessage()) + ":退单号:" + ipBStandplatRefund.getReturnNo(), user);
        return v14.isOK();
    }


    /**
     * 生成发货前退款单(仅退款)
     */
    public void foundRefundFrontRefundOnly(OcBOrder ocBOrder, IpBStandplatRefund ipBStandplatRefund, User user) {
        if (omsRefundOrderService.isRefundSlipBfExist(ipBStandplatRefund.getReturnNo())) {
            return;
        }
        OcBReturnBfSend ocBReturnBfSend = new OcBReturnBfSend();
        ocBReturnBfSend.setId(ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCBRETURNBFSEND));
        ocBReturnBfSend.setCpCShopId(ocBOrder.getCpCShopId());
        ocBReturnBfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        ocBReturnBfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        ocBReturnBfSend.setTid(ipBStandplatRefund.getOrderNo() + "");
        ocBReturnBfSend.setSubBillNo(ipBStandplatRefund.getSubOrderId() + "");
        ocBReturnBfSend.setTReturnId(ipBStandplatRefund.getReturnNo());
        ocBReturnBfSend.setBuyerNick(ipBStandplatRefund.getBuyerNick());
        //退款金额
        ocBReturnBfSend.setAmtReturn(ipBStandplatRefund.getRefundAmount());
        ocBReturnBfSend.setReason(ipBStandplatRefund.getReturnReason());
        ocBReturnBfSend.setTReturnStatus(String.valueOf(ipBStandplatRefund.getReturnStatus()));
        ocBReturnBfSend.setReturnApplyTime(ipBStandplatRefund.getCreated());
        //退款说明
        ocBReturnBfSend.setReturnExplain(ipBStandplatRefund.getReturnReason());
        OperateUserUtils.saveOperator(ocBReturnBfSend, user);

        ocBReturnBfSendMapper.insert(ocBReturnBfSend);
    }

    /**
     * 发货后退款
     *
     * @param ocBOrder
     * @param ocBOrderItems
     * @param ipBStandplatRefund
     * @return
     */
    private ValueHolderV14 executeAutoRefundAfterShipment(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, IpBStandplatRefund ipBStandplatRefund) {
        StandPlatRefundIntoWareHouseModel standPlatRefundIntoWareHouseModel = this.buildRefundIntoWareHouseModel(ocBOrder, ocBOrderItems, ipBStandplatRefund);
        log.info("executeAutoRefundAfterShipment input parameter:{},time:{}", JSONObject.toJSONString(standPlatRefundIntoWareHouseModel), DateUtil.getDateTime());
        ValueHolderV14 v14 = standPlatRefundIntoWareHouseCmd.refundIntoWareHouse(standPlatRefundIntoWareHouseModel);
        log.info("executeAutoRefundAfterShipment return results:{},time:{}", v14.toJSONObject(), DateUtil.getDateTime());
        return v14;
    }

    /**
     * 发货后退款入参
     *
     * @param ocBOrder
     * @param ocBOrderItems
     * @param ipBStandplatRefund
     * @return
     */
    private StandPlatRefundIntoWareHouseModel buildRefundIntoWareHouseModel(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, IpBStandplatRefund ipBStandplatRefund) {
        String ooidStr = ocBOrderItems.stream().map(OcBOrderItem::getOoid).collect(Collectors.joining(","));
        List<IpBStandplatRefundItem> ipBStandplatRefundItems = ipBStandplatRefundItemMapper.selectList(
                new QueryWrapper<IpBStandplatRefundItem>().lambda()
                        .eq(IpBStandplatRefundItem::getIpBStandplatRefundId, ipBStandplatRefund.getId())
                        .in(IpBStandplatRefundItem::getSubOrderId, ooidStr)
        );
        //todo 计算退货数，目前只取一个,产品确认不涉及批量退
        BigDecimal returnQuantity = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(ipBStandplatRefundItems)) {
            BigDecimal quantity = ipBStandplatRefundItems.get(0).getReturnQuantity();
            returnQuantity = returnQuantity.add(quantity);
        }
        String tid = ocBOrderItems.get(0).getTid();
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        String shopSellerNick = cpShop != null ? cpShop.getSellerNick() : "";
        StandPlatRefundIntoWareHouseModel standPlatRefundIntoWareHouseModel = new StandPlatRefundIntoWareHouseModel();

        standPlatRefundIntoWareHouseModel.setOid(ooidStr);

        /**
         * 入库状态
         * 0:入库成功
         * 1:入库失败
         */
        standPlatRefundIntoWareHouseModel.setStorageStatus("0");
        /**
         *是否整单退(Y是,N否)
         */
        standPlatRefundIntoWareHouseModel.setIsAllReturn("N");
        /**
         * 平台号code
         */
        standPlatRefundIntoWareHouseModel.setPlatform(ocBOrder.getPlatform());
        /**
         * 店铺名称(卖家昵称)
         */
        standPlatRefundIntoWareHouseModel.setSellerNick(shopSellerNick);
        /**
         * 平台退款单号
         */
        standPlatRefundIntoWareHouseModel.setRefundId(ipBStandplatRefund.getReturnNo());
        if (PlatFormEnum.PINDUODUO.getCode().equals(ocBOrder.getPlatform())) {
            standPlatRefundIntoWareHouseModel.setReturnNo(ipBStandplatRefund.getReturnNo());
            // 买家备注
            standPlatRefundIntoWareHouseModel.setSellerRemark(ocBOrder.getSellerMemo());
        }
        /**
         * 入库时间 取 退换货的审核时间 给接口
         */
        List<OcBReturnOrder> ocReturnOrders = orderMapper.selectList(new LambdaQueryWrapper<OcBReturnOrder>()
                .eq(OcBReturnOrder::getReturnId, ipBStandplatRefund.getReturnNo())
                .eq(OcBReturnOrder::getIsactive, "Y"));
        if (CollectionUtils.isNotEmpty(ocReturnOrders)) {
            OcBReturnOrder ocReturnOrder = ocReturnOrders.get(0);
            if (ocReturnOrder != null && ocReturnOrder.getAuditTime() != null) {
                standPlatRefundIntoWareHouseModel.setWarehousingTime(ocReturnOrder.getAuditTime().toString());
            }
        }


        standPlatRefundIntoWareHouseModel.setTid(tid);
        //退款金额
        standPlatRefundIntoWareHouseModel.setRefundAmount(ipBStandplatRefund.getRefundAmount());

        //入库数量
        standPlatRefundIntoWareHouseModel.setNum(returnQuantity.intValue());

        /**
         *物流公司编码
         */
        standPlatRefundIntoWareHouseModel.setLogisticsCompanyId(0);
        LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(ipBStandplatRefund.getCompanyName());
        String companyCode = "";
        if (logisticsInfo != null) {
            companyCode = cpRpcService.getPlatformLogisticEcode(logisticsInfo.getId(), Long.valueOf(ocBOrder.getPlatform()));
            standPlatRefundIntoWareHouseModel.setLogisticsCompanyId((int) logisticsInfo.getId());
            standPlatRefundIntoWareHouseModel.setCompanyCode(companyCode);
        }
        log.info("buildRefundIntoWareHouseModel,通用退货退款物流入参:{},物流公司编码:{},平台code:{},退单号:{}", ipBStandplatRefund.getCompanyName(), companyCode, ocBOrder.getPlatform(), ipBStandplatRefund.getReturnNo());

        /**
         * 退货物流单号
         */
        standPlatRefundIntoWareHouseModel.setOutSid(ipBStandplatRefund.getLogisticsNo());


//        standPlatRefundIntoWareHouseModel.setTag();


        /**
         *退款拒绝原因
         *1退货与原订单不符（商品不符、退货地址不符）
         * 2退回商品影响二次销售/订单超出售后时效（订单完成超7天）
         * 3已与买家协商补偿，包括差价、赠品、额外补偿
         * 4已与买家协商补发商品/已与买家协商换货
         */
//        standPlatRefundIntoWareHouseModel.setRefuseType();
        /**
         * 拒绝原因
         */
//        standPlatRefundIntoWareHouseModel.setRefuseReason();
        if (PlatFormEnum.YOUZAN.getCode().equals(ocBOrder.getPlatform())) {
            //todo //版本号(有赞必填)
            standPlatRefundIntoWareHouseModel.setVersion("1.0");
        }
        if (PlatFormEnum.DOU_YIN.getCode().equals(ocBOrder.getPlatform())) {
            //todo 抖音必填，退款拒绝凭证（文本）
//            standPlatRefundIntoWareHouseModel.setRefuseEvidence("商家拒绝");
        }
        //退运费金额
        BigDecimal freightAmount = ipBStandplatRefund.getReturnShipamount() == null ? BigDecimal.ZERO : ipBStandplatRefund.getReturnShipamount();
        standPlatRefundIntoWareHouseModel.setFreightAmount(freightAmount);
        return standPlatRefundIntoWareHouseModel;
    }


    private ValueHolderV14 executeCloudHubRefundAgree(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, IpBStandplatRefund ipBStandplatRefund) {
        StandPlatRefundAgreeModel refundAgreeModel = this.buildCloudHubRefundAgreeModel(ocBOrder, ocBOrderItems, ipBStandplatRefund);
        ValueHolderV14 v14 = standPlatRefundAutomaticCmd.refundAgree(refundAgreeModel);
        log.info(LogUtil.format("完成-通用平台售后单无条件同意退款，入参:{}，结果：{}",
                "OmsOrderStandPlatAutoRefundService.executeCloudHubRefundAgree"), JSON.toJSONString(refundAgreeModel), v14);
        return v14;
    }

    private StandPlatRefundAgreeModel buildCloudHubRefundAgreeModel(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, IpBStandplatRefund ipBStandplatRefund) {
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        /*这个参数必填，但是这里不校验*/
        String shopSellerNick = cpShop != null ? cpShop.getSellerNick() : "";

        StandPlatRefundAgreeModel refundAgreeModel = new StandPlatRefundAgreeModel();

        /*卖家昵称，同一个平台下的卖家昵称保持唯一，OMS可根据卖家昵称和平台号来匹配本地的店铺*/
        refundAgreeModel.setSellerNick(shopSellerNick);
        /*平台号，由云枢纽分配，OMS需提前将平台号信息维护到系统中，平台号信息*/
        refundAgreeModel.setPlatform(ocBOrder.getPlatform());
        /*退单号*/
        refundAgreeModel.setReturnNo(ipBStandplatRefund.getReturnNo());
        /*订单号*/
        refundAgreeModel.setTid(ocBOrderItems.get(0).getTid());
        /*卖家退款备注*/
        refundAgreeModel.setSellerRemark(ipBStandplatRefund.getReturnReason());
        /*链路追踪id唯一*/
        refundAgreeModel.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        return refundAgreeModel;
    }

    /**
     * 发货前退款
     *
     * @param ocBOrder           原始订单
     * @param ocBOrderItems      可退明细
     * @param ipBStandplatRefund 退单信息
     * @return
     */
    private ValueHolderV14 executeAutoRefundBeforeShipment(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, IpBStandplatRefund ipBStandplatRefund) {
        StandPlatRefundAutomaticModel refundAutomaticModel = this.buildRefundAutomaticModel(ocBOrder, ocBOrderItems, ipBStandplatRefund);
        log.info("executeAutoRefundBeforeShipment input parameter:{},time:{}", JSONObject.toJSONString(refundAutomaticModel), DateUtil.getDateTime());
        ValueHolderV14 v14 = standPlatRefundAutomaticCmd.refundAutomatic(refundAutomaticModel);
        log.info("executeAutoRefundBeforeShipment return results:{},time:{}", v14.toJSONObject(), DateUtil.getDateTime());
        return v14;
    }

    /**
     * 发货前退款包装入参
     *
     * @param ocBOrder           原始订单
     * @param ocBOrderItems      可退明细
     * @param ipBStandplatRefund 退单信息
     * @return
     */
    private StandPlatRefundAutomaticModel buildRefundAutomaticModel(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems, IpBStandplatRefund ipBStandplatRefund) {
        String ooidStr = ocBOrderItems.stream().map(OcBOrderItem::getOoid).collect(Collectors.joining(","));
        String tid = ocBOrderItems.get(0).getTid();
        CpShop cpShop = cpRpcService.selectShopById(ocBOrder.getCpCShopId());
        String shopSellerNick = cpShop != null ? cpShop.getSellerNick() : "";
        StandPlatRefundAutomaticModel refundAutomaticModel = new StandPlatRefundAutomaticModel();

        //平台订单号
        refundAutomaticModel.setTid(tid);
        ///子订单号(如有多个用逗号隔开)
        refundAutomaticModel.setOid(ooidStr);
        //默认取ip配置
//        refundAutomaticModel.setTag();
        //0:同意
        refundAutomaticModel.setApprovalStatus(0);
        //物流公司id 非必填
//        refundAutomaticModel.setLogisticsCompanyId();
//        是否整单退(Y是,N否)
        refundAutomaticModel.setIsAllReturn("N");
        //退单号
        refundAutomaticModel.setRefundId(ipBStandplatRefund.getReturnNo());
        //物流单号 非必填
        refundAutomaticModel.setOutSid(ipBStandplatRefund.getLogisticsNo());
        //退款原因
        refundAutomaticModel.setRefuseReason(ipBStandplatRefund.getReturnReason());
        if (PlatFormEnum.YOUZAN.getCode().equals(ocBOrder.getPlatform())) {
            refundAutomaticModel.setVersion("1.0");
        }
        //退款金额(快手必填)
        refundAutomaticModel.setRefundAmount(ipBStandplatRefund.getRefundAmount());
        //平台号
        refundAutomaticModel.setPlatform(ocBOrder.getPlatform());
        //店铺名称(卖家昵称)
        refundAutomaticModel.setSellerNick(shopSellerNick);


        return refundAutomaticModel;
    }

}
