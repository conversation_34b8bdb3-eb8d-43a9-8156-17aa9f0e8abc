package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentLogDO;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFCompensationReasonMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFCompensationTypeMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentLogMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.ac.OperatorLogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ac.PayBillTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AcBeanUtils;
import com.jackrain.nea.util.AcPayableAdjustmentPushESUtil;
import com.jackrain.nea.util.AmountCalcUtils;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * description：发货单生成丢件单
 *
 * <AUTHOR>
 * @date 2021/5/20
 */
@Component
@Slf4j
public class OcBOrderPayableAdjustmentCreateService {
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Resource
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;

    @Resource
    private AcFPayableAdjustmentItemMapper acFPayableAdjustmentItemMapper;

    @Resource
    private AcFPayableAdjustmentLogMapper payableAdjustmentLogMapper;

    @Autowired
    AcFCompensationReasonMapper acFCompensationReasonMapper;

    @Autowired
    AcFCompensationTypeMapper acFCompensationTypeMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private IpRpcService ipRpcService;

    @Resource
    private CpRpcService cpRpcService;

    public ValueHolderV14 payableAdjustmentCreate(Long[] ids, User user) throws NDSException {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("生成丢件单入参：{}", Arrays.asList(ids)));
        }
        JSONArray errorArray = new JSONArray();
        for (int i = 0; i < ids.length; i++) {
            Long id = ids[i];
            try {
                ValueHolderV14 holderV14 = createFunction(id, user);
                if(log.isDebugEnabled()){
                    log.debug(LogUtil.format("丢件单生成结果：{}"), holderV14);
                }
                if (!holderV14.isOK()) {
                    JSONObject errJo = new JSONObject();
                    errJo.put("objid", id);
                    errJo.put("message", holderV14.getMessage());
                    errorArray.add(errJo);
                }
            } catch (Exception e) {
                JSONObject errJo = new JSONObject();
                errJo.put("objid", id);
                errJo.put("message", e.getMessage());
                errorArray.add(errJo);
            }
        }
        valueHolder = AcBeanUtils.getProcessValueHolderV14(ids.length, errorArray, "生成丢件单");
        return valueHolder;
    }

    @Transactional(rollbackFor = {Exception.class})
    public ValueHolderV14 createFunction(Long orderId, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("当前生成丢件单的发货单ID是：", orderId));
        }
        OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
        AssertUtil.notNull(ocBOrder, "未查询到有效数据");
        //解密原始订单信息
        ipRpcService.decrypt(ocBOrder);
        //仓库发货或平台发货
        boolean statusFlag = OcOrderCheckBoxEnum.CHECKBOX_WAREHOUSE_DELIVERY.getVal() == ocBOrder.getOrderStatus() || OcOrderCheckBoxEnum.CHECKBOX_PLATFORM_DELIVERY.getVal() == ocBOrder.getOrderStatus();
        AssertUtil.assertException(!statusFlag, "当前记录非仓库发货或平台发货，不允许生成丢件单！");
        List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItemList(orderId);
        AssertUtil.notEmpty(itemList, "当前发货单不存在有效明细数据");
        return this.transferData(ocBOrder, itemList, user);
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 transferData(OcBOrder ocBOrder, List<OcBOrderItem> items, User user) {
        ValueHolderV14 valueHolder = new ValueHolderV14();
        AcFPayableAdjustmentDO payableAdjustmentDO = new AcFPayableAdjustmentDO();
        Long cpShopId = ocBOrder.getCpCShopId();
        CpShop cpShop = cpRpcService.selectCpCShopById(cpShopId);
        if (cpShop != null) {
            payableAdjustmentDO.setCpCShopId(cpShopId);
            //店铺名称
            payableAdjustmentDO.setCpCShopTitle(ocBOrder.getCpCShopTitle());
            //渠道类型
            Long channelType = Long.parseLong(cpShop.getChannelType());
            payableAdjustmentDO.setReserveBigint01(channelType);
        }
        //实体仓
        payableAdjustmentDO.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        payableAdjustmentDO.setCpCPhyWarehouseEname(ocBOrder.getCpCPhyWarehouseEname());
        payableAdjustmentDO.setCpCPhyWarehouseEcode(ocBOrder.getCpCPhyWarehouseEcode());

        //物流公司档案

        payableAdjustmentDO.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
        payableAdjustmentDO.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
        payableAdjustmentDO.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());

        payableAdjustmentDO.setId(ModelUtil.getSequence(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT));
        //新增单据状态默认为: 待审核(未审核)
        payableAdjustmentDO.setBillStatus(AcConstant.CON_BILL_STATUS_01);
        //单据编号生成更新
        JSONObject sequence = new JSONObject();
        try {
            String billNo = SequenceGenUtil.generateSquence("SEQ_AC_F_PAYABLE_ADJUSTMENT",
                    sequence, user.getLocale(), false);
            if (billNo == null) {
                billNo = sequenceUtil.buildPayableAdjustmentBillNo();
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("生成的丢件单编号billNo=", billNo));
            }
            payableAdjustmentDO.setBillNo(StringUtils.isEmpty(billNo) ? "ERROR00001" : billNo);
        } catch (Exception e) {
            log.debug(LogUtil.format("生成丢件单编号失败,error：{}"), Throwables.getStackTraceAsString(e));
            payableAdjustmentDO.setBillNo("ERROR00001");
        }

        //新增 字段 赔付类型
//        Integer compensationTypeId = mainMap.getInteger("AC_F_COMPENSATION_TYPE_ID");
//        acFPayableAdjustmentDO.setAcFCompensationTypeId(compensationTypeId);
//        log.debug("查询赔付类型---------------!");
//        AcFCompensationType compensationType = this.acFCompensationTypeMapper.selectOne(new QueryWrapper<AcFCompensationType>().lambda().eq(AcFCompensationType::getId, compensationTypeId));
//        if (compensationType != null) {
//            acFPayableAdjustmentDO.setCompensationTypeEname(compensationType.getEname());
//        } else {
//            throw new NDSException("该赔付类型不存在,请添加赔付类型!!!");
//        }
//        //赔付原因
//        Integer reasonId = mainMap.getInteger("AC_F_COMPENSATION_REASON_ID");
//        acFPayableAdjustmentDO.setAcFCompensationReasonId(reasonId);
//        AcFCompensationReason reason = this.acFCompensationReasonMapper.selectById(reasonId);
//        if (reason != null) {
//            acFPayableAdjustmentDO.setCompensationReason(reason.getAcFCompensationTypeEname());
//        } else {
//            throw new NDSException("赔付原因不存在,请添加赔付原因!!!");
//        }
        //快递网点
//        acFPayableAdjustmentDO.setExpressOutlets(mainMap.getString("EXPRESS_OUTLETS"));
        //快递单号
        payableAdjustmentDO.setLogisticsNo(ocBOrder.getExpresscode());
        payableAdjustmentDO.setPayType(ocBOrder.getPayType());
        payableAdjustmentDO.setPayTime(ocBOrder.getPayTime());
        payableAdjustmentDO.setBillType(PayBillTypeEnum.PAY_BF.getVal());
        payableAdjustmentDO.setOrderNo(ocBOrder.getBillNo());
        payableAdjustmentDO.setSourceTid(ocBOrder.getId());
        payableAdjustmentDO.setTid(ocBOrder.getTid());
        payableAdjustmentDO.setCustomerNick(ocBOrder.getUserNick());
        payableAdjustmentDO.setCustomerName(ocBOrder.getReceiverName());
        payableAdjustmentDO.setCustomerTel(StringUtils.isEmpty(ocBOrder.getReceiverPhone())?ocBOrder.getReceiverMobile():ocBOrder.getReceiverPhone());
        //原始出库日期
        payableAdjustmentDO.setSourceOutsourceDate(Objects.isNull(ocBOrder.getScanTime()) ? new Date() : ocBOrder.getScanTime());
        //总应付金额
        BigDecimal totalPayablePrice = getTotalPayablePrice(items);
        payableAdjustmentDO.setPayablePrice(totalPayablePrice);
        //设置原单金额为订单总额
        payableAdjustmentDO.setOriginOrderAmt(ocBOrder.getOrderAmt());
        //来源单据类型默认 零售发货单
        payableAdjustmentDO.setReserveVarchar01(AcConstant.PAYABLE_SOURCE_TYPE_1);
        BaseModelUtil.makeBaseCreateField(payableAdjustmentDO, user);
        payableAdjustmentDO.setOwnerename(user.getEname());
        payableAdjustmentDO.setModifierename(user.getEname());
        if (acFPayableAdjustmentMapper.insert(payableAdjustmentDO) < 0) {
            throw new NDSException("保存失败！");
        }
        //新增操作日志-新增
        insertLogFun(user, payableAdjustmentDO.getId(), OperatorLogTypeEnum.ORIGIN_ORDER_ADD.getVal(),
                OperatorLogTypeEnum.ORIGIN_ORDER_ADD.getText(), new Date());
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.CREATE_PAYABLE_ADJUSTMENT.getKey(), "生成丢件单成功,丢件单编号:" + payableAdjustmentDO.getBillNo(), "", "", user);

        /**
         * 子表新增
         */
        if (!items.isEmpty()) {
            List<AcFPayableAdjustmentItemDO> payableAdjustmentItemDOS = new ArrayList<>(items.size());
            for (int i = 0; i < items.size(); i++) {
                OcBOrderItem orderItem = items.get(i);
                AcFPayableAdjustmentItemDO adjustmentItemDO = new AcFPayableAdjustmentItemDO();
                adjustmentItemDO.setId(ModelUtil.getSequence(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT_ITEM));
                adjustmentItemDO.setAcFPayableAdjustmentId(payableAdjustmentDO.getId());
                adjustmentItemDO.setPsCProId(orderItem.getPsCProId());
                adjustmentItemDO.setPsCProEcode(orderItem.getPsCProEcode());
                adjustmentItemDO.setPsCProEname(orderItem.getPsCProEname());
                adjustmentItemDO.setPsCSkuId(orderItem.getPsCSkuId());
                adjustmentItemDO.setPsCClrId(orderItem.getPsCClrId());
                adjustmentItemDO.setPsCClrEcode(orderItem.getPsCClrEcode());
                adjustmentItemDO.setPsCClrEname(orderItem.getPsCClrEname());
                adjustmentItemDO.setPsCSizeId(orderItem.getPsCSizeId());
                adjustmentItemDO.setPsCSizeEcode(orderItem.getPsCSizeEcode());
                adjustmentItemDO.setPsCSizeEname(orderItem.getPsCSizeEname());
                adjustmentItemDO.setQty(orderItem.getQty());
                //标准价
                BigDecimal standardPrice = orderItem.getPriceList();
                //实际成交价
                BigDecimal truePrice = orderItem.getPrice();
                //应付金额与实际成交价不符则取实际成交价
                BigDecimal payablePrice = orderItem.getPrice();
                adjustmentItemDO.setStandardPrice(standardPrice);
                adjustmentItemDO.setTruePrice(truePrice);
                adjustmentItemDO.setPayablePrice(payablePrice);
                adjustmentItemDO.setPsCSkuEcode(orderItem.getPsCSkuEcode());
//                acFPayableAdjustmentItemDO.setFactoryNo(factoryNo);
                adjustmentItemDO.setPsCBrandId(orderItem.getPsCBrandId());
                adjustmentItemDO.setGbcode(orderItem.getBarcode());
                /*adjustmentItemDO.setLogicalStoreId(ocBOrder.getCpCStoreId());
                adjustmentItemDO.setLogicalStoreCode(ocBOrder.getCpCStoreEcode());
                adjustmentItemDO.setLogicalStoreName(ocBOrder.getCpCStoreEname());*/
                //订单明细id
                adjustmentItemDO.setOrderItemId(orderItem.getId());
                //订单数量
                adjustmentItemDO.setOrderQty(orderItem.getQty());
                //成交单价
                adjustmentItemDO.setDealAmt(orderItem.getPriceActual());
                //应付单价=应付金额/数量
                BigDecimal payAmt = AmountCalcUtils.divideBigDecimal(payablePrice, orderItem.getQty(), 4);
                adjustmentItemDO.setPayAmt(payAmt);
                BaseModelUtil.makeBaseCreateField(adjustmentItemDO, user);
                payableAdjustmentItemDOS.add(adjustmentItemDO);
//                if (acFPayableAdjustmentItemMapper.insert(adjustmentItemDO) < 0) {
//                    throw new NDSException("保存明细失败！");
//                }
            }
            acFPayableAdjustmentItemMapper.batchInsert(payableAdjustmentItemDOS);
        }
        valueHolder.setCode(ResultCode.SUCCESS);
        valueHolder.setMessage("丢件单保存成功！");
        Map<String, Object> data = new HashMap();
        data.put("objid", payableAdjustmentDO.getId());
        data.put("tablename", AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT);
        valueHolder.setData(data);
        AcPayableAdjustmentPushESUtil.pushOrderAndItem(payableAdjustmentDO.getId());
        return valueHolder;
    }

    /**
     * 获取总应付金额
     */
    private BigDecimal getTotalPayablePrice(List<OcBOrderItem> itemMap) {

        BigDecimal total = BigDecimal.ZERO;
        if (!itemMap.isEmpty()) {
            for (int i = 0; i < itemMap.size(); i++) {
                OcBOrderItem jsonObject = itemMap.get(i);
                //应付金额
                BigDecimal payablePrice = jsonObject.getPrice();
                BigDecimal truePrice = jsonObject.getPrice();
                //若应付金额与实际成交价不一致则取实际成交价
                if (payablePrice.compareTo(truePrice) != 0 || payablePrice == null) {
                    payablePrice = jsonObject.getPrice();
                }
                total.add(payablePrice);
            }
        }
        return total;
    }

    /**
     * 记录日志
     *
     * @param user
     * @param id
     * @param logType
     * @param logContent
     * @param date
     * @return
     */
    public ValueHolderV14 insertLogFun(User user, Long id, int logType, String logContent, Date date) {
        ValueHolderV14 v14 = new ValueHolderV14();
        //应付款调整单日志
        AcFPayableAdjustmentLogDO payableAdjustmentLogDO = new AcFPayableAdjustmentLogDO();
        //id
        payableAdjustmentLogDO.setId(ModelUtil.getSequence(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT_LOG));
        //操作日志关联单号
        payableAdjustmentLogDO.setAcFPayableAdjustmentId(id);
        //操作类型id
        payableAdjustmentLogDO.setLogType(logType);
        //操作类型名称
        payableAdjustmentLogDO.setLogContent(logContent);

        BaseModelUtil.makeBaseCreateField(payableAdjustmentLogDO, user);
        payableAdjustmentLogDO.setOwnerename(user.getEname());

        if (payableAdjustmentLogMapper.insert(payableAdjustmentLogDO) < 0) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("应付操作日志保存失败！");
            return v14;
        }
        v14.setCode(ResultCode.SUCCESS);
        return v14;
    }
}