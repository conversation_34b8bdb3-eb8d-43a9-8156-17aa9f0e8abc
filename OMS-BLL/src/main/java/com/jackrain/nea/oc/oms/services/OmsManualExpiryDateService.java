package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.map.MapUtil;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.dto.ExpiryDateItem;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ExpiryDateOrderLabelEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/6/21 下午5:27
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsManualExpiryDateService {

    private static final Map<Integer, String> NUMBER_MAP = MapUtil.builder(new HashMap<Integer, String>()).put(1, "一")
            .put(2, "二")
            .put(3, "三")
            .put(4, "四")
            .put(5, "五")
            .put(6, "六")
            .put(7, "七")
            .put(8, "八")
            .put(9, "九")
            .put(10, "十")
            .put(11, "十一")
            .put(12, "十二").build();

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsManualExpiryDateService omsManualExpiryDateService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OmsExpiryDateStService omsExpiryDateStService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Resource
    private ThreadPoolTaskExecutor doAutoExpireDatePollExecutor;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;
    @Resource
    private ThreadPoolTaskExecutor batchManualExpiryDatePollExecutor;


    public ValueHolderV14 selectOmsOrderItem(List<Long> ids) {
        //查寻订单明细 排除已取消的
        ValueHolderV14 holder = new ValueHolderV14();
        List<List<Long>> listList = BllCommonUtil.getBasePageList(ids, 1000);
        List<Long> idList = new ArrayList<>();
        for (List<Long> orderIds : listList) {
            //查询订单
            List<Long> orderIdList = ocBOrderMapper.selectByIdsListByOrderStatus(orderIds);
            if (CollectionUtils.isNotEmpty(orderIdList)) {
                idList.addAll(orderIdList);
            }
        }
        if (CollectionUtils.isEmpty(idList)) {
            holder.setCode(-1);
            holder.setMessage("所选订单状态已不符合");
            return holder;
        }
        List<OcBOrderItem> orderItemList = new ArrayList<>();
        List<List<Long>> basePageList = BllCommonUtil.getBasePageList(idList, 1000);
        for (List<Long> orderIds : basePageList) {
            List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItemsByOrderIdsExpiryDate(orderIds);
            orderItemList.addAll(ocBOrderItemList);
        }
        if (CollectionUtils.isEmpty(orderItemList)) {
            holder.setCode(-1);
            holder.setMessage("未查询到有效的明细数据");
            return holder;
        }

        /*20240927-改造*/
        List<ExpiryDateItem> expiryDateItems = buildExpiryDateItems(orderItemList);

        holder.setCode(0);
        holder.setMessage("查询到有效的明细数据");
        holder.setData(expiryDateItems);
        return holder;
    }

    private static List<ExpiryDateItem> buildExpiryDateItems(List<OcBOrderItem> orderItemList) {
        List<ExpiryDateItem> expiryDateItems = new ArrayList<>();

        //按sku_code聚合明细，加上效期范围
        Map<String, List<OcBOrderItem>> itemMap = orderItemList.stream()
//                .collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuEcode));
                .peek(o -> {
                    Integer appointType = o.getExpiryDateType();
                    String expiryDateRange = o.getExpiryDateRange();
                    String startDateDay = "";
                    String endDateDay = "";
                    if (StringUtils.isNotEmpty(expiryDateRange)
                            && appointType != null
                            && (appointType == 1 || appointType == 2)) {
                        String[] split = expiryDateRange.split("-");
                        startDateDay = split[0];
                        endDateDay = split[1];
                    }

                    if (appointType != null
                            && 2 == appointType
                            && StringUtils.isNotEmpty(startDateDay)
                            && StringUtils.isNotEmpty(endDateDay)) {
                        //生产天数范围类型的天数转成生产日期，跟谷北确定过
                        String startDay =
                                LocalDate.now().plusDays(-new Integer(endDateDay)).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                        String endDay =
                                LocalDate.now().plusDays(-new Integer(startDateDay)).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                        startDateDay = startDay;
                        endDateDay = endDay;
                    }
                    o.setExpiryDateRange(startDateDay + "-" + endDateDay);
                }).collect(Collectors.groupingBy(o -> o.getPsCSkuEcode() + "_" + o.getExpiryDateRange()));
        for (Map.Entry<String, List<OcBOrderItem>> entry : itemMap.entrySet()) {
            String key = entry.getKey();
            String[] split = key.split("_");
            String skuEcode = split[0];
            String[] expiryDateRange = split[1].split("-");

            List<OcBOrderItem> ocBOrderItemList = entry.getValue();

            ExpiryDateItem dateItem = new ExpiryDateItem();
            dateItem.setRelationIds(ocBOrderItemList.stream().map(OcBOrderItem::getOcBOrderId).collect(Collectors.toSet()));
            dateItem.setItemIds(ocBOrderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toSet()));

            dateItem.setSkuCode(skuEcode);
            dateItem.setSkuTitle(ocBOrderItemList.get(0).getPsCProEname());
            /*dateItem.setExpiryDateRange("-".equals(split[1]) ? "" : split[1]);*/
            if (expiryDateRange.length > 0) {
                dateItem.setStartDateDay(expiryDateRange[0]);
            }
            if (expiryDateRange.length > 1) {
                dateItem.setEndDateDay(expiryDateRange[1]);
            }

            dateItem.setQty(ocBOrderItemList.stream().map(OcBOrderItem::getQty).
                    reduce(BigDecimal.ZERO, BigDecimal::add));
            dateItem.setAppointType(1);

            expiryDateItems.add(dateItem);
        }
        return expiryDateItems;
    }

    /**
     * 执行商品效期策略
     *
     * @param expiryDateItems
     * @return
     */
    public ValueHolderV14 executeOmsOrderItem(List<ExpiryDateItem> expiryDateItems, User user) {
        ValueHolderV14 holder = new ValueHolderV14();
        for (ExpiryDateItem expiryDateItem : expiryDateItems) {
            String startDateDay = expiryDateItem.getStartDateDay();
            String endDateDay = expiryDateItem.getEndDateDay();
            Integer appointType = expiryDateItem.getAppointType();
            if (CollectionUtils.isEmpty(expiryDateItem.getItemIds())) {
                return new ValueHolderV14(ResultCode.FAIL, "明细ID集合不能为空");
            }

            if (appointType != null && (appointType == 1 || appointType == 2)) {
                if (StringUtils.isEmpty(startDateDay) || StringUtils.isEmpty(endDateDay)) {
                    holder.setCode(-1);
                    holder.setMessage("开始生产日期/天数或结束生产日期/天数不能为空");
                    return holder;
                }
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            if (appointType != null && appointType == 1) {
                Date startDate, endDate;
                try {
                    startDate = sdf.parse(startDateDay);
                    endDate = sdf.parse(endDateDay);
                } catch (Exception e) {
                    holder.setCode(-1);
                    holder.setMessage("指定类型为生产日期,输入日期类型不正确");
                    return holder;
                }
                if (endDate.getTime() < startDate.getTime()) {
                    holder.setCode(-1);
                    holder.setMessage("【结束生产日期/天数】小于【开始生产日期/天数】，不允许！");
                    return holder;
                }

            }
            if (appointType != null && appointType == 2) {
                int startDay, endDay;
                try {
                    startDay = Integer.parseInt(startDateDay);
                    endDay = Integer.parseInt(endDateDay);
                } catch (Exception e) {
                    holder.setCode(-1);
                    holder.setMessage("指定类型天数,输入类型不正确");
                    return holder;
                }
                if (endDay < 0 || startDay < 0) {
                    holder.setCode(-1);
                    holder.setMessage("【结束生产日期/天数】或【开始生产日期/天数】，不能为负数！");
                    return holder;
                }
                if (endDay < startDay) {
                    holder.setCode(-1);
                    holder.setMessage("【结束生产日期/天数】小于【开始生产日期/天数】，不允许！");
                    return holder;
                }
            }
            if (appointType != null && appointType == 3) {
                List<ExpiryDateItem.DateNum> dateNums = expiryDateItem.getDateNums();
                if (CollectionUtils.isEmpty(dateNums)) {
                    holder.setCode(-1);
                    holder.setMessage("类型为生产日期和数量时,数据不能为空");
                    return holder;
                }
                BigDecimal qty = expiryDateItem.getQty();
                for (ExpiryDateItem.DateNum dateNum : dateNums) {
                    String dateStr = dateNum.getDate();
                    try {
                        Date date = sdf.parse(dateStr);
                    } catch (Exception e) {
                        holder.setCode(-1);
                        holder.setMessage("类型为生产日期和数量时,输入日期类型不正确");
                        return holder;
                    }
                    BigDecimal num = dateNum.getNum();
                    if (num == null || num.compareTo(BigDecimal.ZERO) <= 0) {
                        holder.setCode(-1);
                        holder.setMessage("类型为生产日期和数量时,输入数量不正确");
                        return holder;
                    }
                }
                BigDecimal totalQty = dateNums.stream().map(ExpiryDateItem.DateNum::getNum).
                        reduce(BigDecimal.ZERO, BigDecimal::add);
                if (totalQty.compareTo(qty) != 0) {
                    holder.setCode(-1);
                    holder.setMessage("类型为生产日期和数量时,输入数量不等于购买数量");
                    return holder;
                }

            }
        }

        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("零售发货单");
        asyncTaskBody.setTaskType("手动指定效期");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        commonTaskExecutor.submit(() -> {
            List<ExecuteResult> results = new ArrayList<>();
            int count = 0;
            for (ExpiryDateItem expiryDateItem : expiryDateItems) {
                count = count + expiryDateItem.getRelationIds().size();
                /*执行替换*/
                ExecuteResult result = executeExpiryDate(expiryDateItem, user);
                results.add(result);
            }
            Integer lockFailNum = results.stream().mapToInt(ExecuteResult::getLockFailNum).sum();
            Integer voidSgShareOut = results.stream().mapToInt(ExecuteResult::getVoidSgShareOut).sum();
            Integer statusNotSatisfied = results.stream().mapToInt(ExecuteResult::getStatusNotSatisfied).sum();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            holderV14.setCode(0);
            int failCount = lockFailNum + voidSgShareOut + statusNotSatisfied;
            int success = count - failCount;
            holderV14.setMessage("手动指定批次,加锁失败:" + lockFailNum + "条,作废库存占用失败:" + voidSgShareOut + "条,状态不满足:" + statusNotSatisfied + "条,处理成功:" + success + "条");
            asyncTaskManager.afterExecute(user, asyncTaskBody, holderV14.toJSONObject());

        });
        holder.setCode(0);
        holder.setData(asyncTaskBody.getId());
        holder.setMessage("零售发货单-手动指定效期任务开始！详情请在我的任务查看");
        return holder;
    }

    private ExecuteResult executeExpiryDate(ExpiryDateItem expiryDateItem, User user) {
        //解析数据
        ExecuteResult result = new ExecuteResult();
        Set<Long> relationIds = expiryDateItem.getRelationIds();
        // 此处使用多线程
        List<List<Long>> operateIdList = Lists.partition(Lists.newArrayList(relationIds), 50);
        List<Future<ExecuteResult>> futures = new ArrayList<>();
        for (List<Long> operateIds : operateIdList) {
            futures.add(batchManualExpiryDatePollExecutor.submit(new BatchManualExpiryDateTask(operateIds, user, expiryDateItem)));
        }
        AtomicInteger lockFailNum = new AtomicInteger();
        AtomicInteger voidSgShareOut = new AtomicInteger();
        AtomicInteger statusNotSatisfied = new AtomicInteger();

        for (Future<ExecuteResult> future : futures) {
            try {
                ExecuteResult executeResult = future.get();
                lockFailNum.addAndGet(executeResult.getLockFailNum());
                voidSgShareOut.addAndGet(executeResult.getVoidSgShareOut());
                statusNotSatisfied.addAndGet(executeResult.getStatusNotSatisfied());
            } catch (ExecutionException e) {
                e.printStackTrace();
                log.error(LogUtil.format("手动指定批次异常:{}", "手动指定批次异常"), Throwables.getStackTraceAsString(e));
            } catch (InterruptedException e) {
                e.printStackTrace();
                log.error(LogUtil.format("手动指定批次异常:{}", "手动指定批次异常"), Throwables.getStackTraceAsString(e));
            }
        }
        result.setLockFailNum(lockFailNum.get());
        result.setVoidSgShareOut(voidSgShareOut.get());
        result.setStatusNotSatisfied(statusNotSatisfied.get());
        return result;
    }

    /**
     * @param orderIdList   需要改订单状态的订单
     * @param ocBOrderItems 需要更新的名字数据
     */
    public void handleOrder(List<Long> orderIdList, List<OcBOrderItem> ocBOrderItems, User user) {
        //将需要改变状态的置为待分配, 加入缺货重新占单表, 更新订单明细
        //ocBOrderMapper.updateOrderStatusList(2, orderIdList);
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            omsOrderItemService.updateOcBOrderItem(ocBOrderItem, ocBOrderItem.getOcBOrderId());
        }
        List<OcBOrderLog> logList = new ArrayList<>();
        for (Long id : orderIdList) {
            try {
                OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
                ocBOrderMapper.updateWarehouse(id);
                //卡单不释放
                if (!YesNoEnum.Y.getVal().equals(ocBOrder.getIsDetention())) {
                    omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
                }
                OcBOrderLog ocBOrderLog = omsOrderLogService.getOcBOrderLog(id, null, OrderLogTypeEnum.MATCH_EXPIRY_DATE.getKey(), "订单手动指定效期", "", "", user);
                logList.add(ocBOrderLog);
            } catch (Exception e) {
                OcBOrderLog ocBOrderLog = omsOrderLogService.getOcBOrderLog(id, null, OrderLogTypeEnum.MATCH_EXPIRY_DATE.getKey(), "订单手动指定效期失败:" + e.getMessage(), "", "", user);
                logList.add(ocBOrderLog);
            }
        }
        omsOrderLogService.save(logList);
    }

    class BatchManualExpiryDateTask implements Callable<ExecuteResult> {

        private final List<Long> operateIdList;
        private final User user;
        private final ExpiryDateItem expiryDateItem;

        public BatchManualExpiryDateTask(List<Long> operateIdList, User user, ExpiryDateItem expiryDateItem) {
            this.operateIdList = operateIdList;
            this.user = user;
            this.expiryDateItem = expiryDateItem;
        }

        @Override
        public ExecuteResult call() throws Exception {
            ExecuteResult result = new ExecuteResult();
            List<RedisReentrant> redisReentrants = new ArrayList<>();
            try {
                //批量上锁  并查询订单数据
                for (Long relationId : operateIdList) {
                    String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(relationId);
                    RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                    try {
                        if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                            RedisReentrant redisReentrant = new RedisReentrant();
                            redisReentrant.setLock(redisLock);
                            redisReentrant.setId(relationId);
                            redisReentrants.add(redisReentrant);
                        } else {
                            redisLock.unlock();
                        }
                    } catch (Exception e) {
                        redisLock.unlock();
                    }
                }
                //查询订单
                if (CollectionUtils.isEmpty(redisReentrants)) {
                    //全部加锁失败
                    result.setLockFailNum(operateIdList.size());
                    return result;
                }
                int i = operateIdList.size() - redisReentrants.size();
                result.setLockFailNum(i);
                //查询加锁成功的订单
                List<Long> orderIds = redisReentrants.stream().map(RedisReentrant::getId).collect(Collectors.toList());
                List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsList(orderIds);
                //根据订单状态来做检验(只要缺货,或者待审核) 如果是其他状态不能指定
                List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItemsByOrderIdsExpiryDate(orderIds);
                Map<Long, List<OcBOrderItem>> orderItemMap = ocBOrderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
                List<OcBOrder> unConfirmedOrderList = ocBOrderList.stream().filter(p -> OmsOrderStatus.UNCONFIRMED.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
                List<OcBOrder> outOfStockOrderList = ocBOrderList.stream().filter(p -> OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
                //对待审核 或者缺货的订单单独处理
                if (i == 0) {
                    i = operateIdList.size();
                }
                int i1 = i - (unConfirmedOrderList.size() + outOfStockOrderList.size());
                result.setStatusNotSatisfied(i1);
                List<Long> orderIdList = this.handleUnConfirmedOrderList(unConfirmedOrderList, user);
                result.setVoidSgShareOut(unConfirmedOrderList.size() - orderIdList.size());
                //开始处理明细
                if (CollectionUtils.isNotEmpty(outOfStockOrderList)) {
                    List<Long> outOfStockOrderIds = outOfStockOrderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
                    orderIdList.addAll(outOfStockOrderIds);
                }
                Integer appointType = expiryDateItem.getAppointType();
                List<OcBOrderItem> orderItemArrayList = new ArrayList<>();
                for (Long id : orderIdList) {
                    List<OcBOrderItem> orderItems = orderItemMap.get(id);
                    orderItemArrayList.addAll(orderItems);
                }
//                Map<String, List<OcBOrderItem>> skuMap = orderItemArrayList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuEcode));
//                List<OcBOrderItem> orderItems = skuMap.get(expiryDateItem.getSkuCode());

                List<OcBOrderItem> orderItems = orderItemArrayList.stream()
                        .filter(p -> expiryDateItem.getSkuCode().equals(p.getPsCSkuEcode())
                                && expiryDateItem.getItemIds().contains(p.getId()))
                        .collect(Collectors.toList());
                List<OcBOrderItem> ocBOrderItems = new ArrayList<>();
                if (appointType != 3) {
                    ocBOrderItems.addAll(handleExpiryDate(orderItems, expiryDateItem));
                } else {
                    //生产日期以及数量的处理
                    List<ExpiryDateItem.DateNum> dateNums = expiryDateItem.getDateNums();
                    // ocBOrderItems.addAll(handleExpiryDateAndNum(orderItems, dateNums));
                }
                omsManualExpiryDateService.handleOrder(orderIdList, ocBOrderItems, user);
            } catch (Exception e) {
                log.error(LogUtil.format("手动指定批次异常:{}", "手动指定批次异常"), Throwables.getStackTraceAsString(e));
            } finally {
                if (CollectionUtils.isNotEmpty(redisReentrants)) {
                    for (RedisReentrant redisReentrant : redisReentrants) {
                        redisReentrant.getLock().unlock();
                    }
                }
            }
            return result;
        }

        private List<Long> handleUnConfirmedOrderList(List<OcBOrder> unConfirmedOrderList, User user) {
            //调用sg作废库存占用
            List<Long> orderList = new ArrayList<>();
            if (CollectionUtils.isEmpty(unConfirmedOrderList)) {
                return orderList;
            }
            List<Long> orderIds = unConfirmedOrderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
            Map<Long, List<OcBOrderItem>> orderItemMap =
                    ocBOrderItemMapper.selectOrderItemListOccupyByOrderIds(orderIds)
                            .stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
            for (OcBOrder ocBOrder : unConfirmedOrderList) {
                try {
                    List<OcBOrderItem> orderItems = orderItemMap.get(ocBOrder.getId());
                    ValueHolderV14 sgValueHolder = sgRpcService.voidSgStockOccupy(ocBOrder, orderItems, user);
                    if (sgValueHolder.getCode() == 0) {
                        orderList.add(ocBOrder.getId());
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("指定效期作废库存占用失败{}", ocBOrder.getId()), Throwables.getStackTraceAsString(e));
                }
            }
            return orderList;
        }
    }

    private List<OcBOrderItem> handleExpiryDate(List<OcBOrderItem> orderItems, ExpiryDateItem expiryDateItem) {
        List<OcBOrderItem> orderItemList = new ArrayList<>();
        Map<String, List<OcBOrderItem>> skuMap = orderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuEcode));
        String skuCode = expiryDateItem.getSkuCode();
        List<OcBOrderItem> orderItems1 = skuMap.get(skuCode);
        for (OcBOrderItem item : orderItems1) {
            OcBOrderItem orderItem = new OcBOrderItem();
            orderItem.setId(item.getId());
            orderItem.setOcBOrderId(item.getOcBOrderId());
            orderItem.setExpiryDateType(expiryDateItem.getAppointType());
            orderItem.setExpiryDateRange(expiryDateItem.getStartDateDay() + "-" + expiryDateItem.getEndDateDay());
            orderItem.setOrderLabel(ExpiryDateOrderLabelEnum.特殊订单需单独处理.getDesc());
            OcBOrder ocBOrder = ocBOrderMapper.selectById(item.getOcBOrderId());
            boolean toBOrder = OmsBusinessTypeUtil.isToBOrder(ocBOrder);
            if (toBOrder) {
                Integer appointType = expiryDateItem.getAppointType();
                if (appointType.equals(1)) {
                    String startDate = expiryDateItem.getStartDateDay();
                    String endDate = expiryDateItem.getEndDateDay();
                    LocalDate startLocalDate = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
                    int year = startLocalDate.getYear();
                    int month = startLocalDate.getMonthValue();
                    String monthStr = NUMBER_MAP.get(month);
                    String yearStr = String.valueOf(year).substring(2);
                    String label = yearStr + "年" + monthStr + "月及以后";
                    orderItem.setOrderLabel(label);
                } else if (appointType.equals(2)) {
                    int startDateDay = Integer.parseInt(expiryDateItem.getStartDateDay());
                    int endDateDay = Integer.parseInt(expiryDateItem.getEndDateDay());
                    int days = endDateDay - startDateDay;
                    String label = days + "天以内";
                    orderItem.setOrderLabel(label);
                }
            }
            orderItemList.add(orderItem);
        }
        return orderItemList;
    }


    /**
     * 处理未审核状态的
     *
     * @param unConfirmedOrderList
     * @param user
     */
    private List<Long> handleUnConfirmedOrderList(List<OcBOrder> unConfirmedOrderList, Map<Long, List<OcBOrderItem>> orderItemMap, User user) {
        //调用sg作废库存占用
        List<Long> orderList = new ArrayList<>();
        if (CollectionUtils.isEmpty(unConfirmedOrderList)) {
            return orderList;
        }
        for (OcBOrder ocBOrder : unConfirmedOrderList) {
            try {
                List<OcBOrderItem> orderItems = orderItemMap.get(ocBOrder.getId());
                ValueHolderV14 sgValueHolder = sgRpcService.voidSgStockOccupy(ocBOrder, orderItems, user);
                if (sgValueHolder.getCode() == 0) {
                    orderList.add(ocBOrder.getId());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("指定效期作废库存占用失败{}", ocBOrder.getId()), Throwables.getStackTraceAsString(e));
            }
        }
        return orderList;
    }


    private List<RedisReentrant> batchLock(Set<Long> relationIds) {
        List<RedisReentrant> batchLocks = new ArrayList<>();
        for (Long relationId : relationIds) {
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(relationId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                    RedisReentrant redisReentrant = new RedisReentrant();
                    redisReentrant.setLock(redisLock);
                    redisReentrant.setId(relationId);
                    batchLocks.add(redisReentrant);
                } else {
                    redisLock.unlock();
                }
            } catch (Exception e) {
                redisLock.unlock();
            }
        }
        return batchLocks;
    }

    @Data
    class RedisReentrant implements Serializable {

        private RedisReentrantLock lock;

        private Long id;
    }


    @Data
    class ExecuteResult implements Serializable {
        /**
         * 加锁失败
         */
        private Integer lockFailNum;
        /**
         * 状态不满足
         */
        private Integer statusNotSatisfied;
        /**
         * 作废库存失败
         */
        private Integer voidSgShareOut;
    }

    @Data
    class ExpiryDateAndNum implements Serializable {

        private Long itemId;

        private Long orderId;

        private BigDecimal num;

        /**
         * 已经填的数量
         */
        private BigDecimal qty;
    }

    public ValueHolderV14 autoExpiryDate(List<Long> ids, User user) {
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("零售发货单");
        asyncTaskBody.setTaskType("手动(自动)指定效期");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        commonTaskExecutor.submit(() -> {
            List<List<Long>> autoExpiryDateIdsList = Lists.partition(ids, 50);
            List<Future<ValueHolderV14<Integer>>> faildCountList = new ArrayList<>();
            for (List<Long> autoExpiryDateIds : autoExpiryDateIdsList) {
                faildCountList.add(doAutoExpireDatePollExecutor.submit(new AutoExpireDate(autoExpiryDateIds, user)));
            }
            AtomicInteger failureCount = new AtomicInteger();
            for (Future<ValueHolderV14<Integer>> result : faildCountList) {
                try {
                    failureCount.addAndGet(result.get().getData());
                } catch (InterruptedException e) {
                    log.error(LogUtil.format("订单自动执行效期策略多线程获取InterruptedException异常：{}"),
                            Throwables.getStackTraceAsString(e));
                    Thread.currentThread().interrupt();
                } catch (ExecutionException e) {
                    log.error(LogUtil.format("订单自动执行效期策略多线程获取ExecutionException异常：{}"), Throwables.getStackTraceAsString(e));
                }
            }
            ValueHolderV14 holderV14 = new ValueHolderV14();
            holderV14.setCode(0);
            holderV14.setMessage("手动(自动)指定效期一共:" + ids.size() + "条,失败:" + failureCount + "条");
            asyncTaskManager.afterExecute(user, asyncTaskBody, holderV14.toJSONObject());
        });
        ValueHolderV14 holder = new ValueHolderV14();
        holder.setCode(0);
        holder.setData(asyncTaskBody.getId());
        holder.setMessage("零售发货单-手动(自动)指定效期任务开始！详情请在我的任务查看");
        return holder;

    }

    public void handleAutoExpiryDate(OcBOrder order, User user) {
        if (order == null) {
            throw new NDSException("订单信息不存在!");
        }
        Integer orderStatus = order.getOrderStatus();
        if (!(OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus))) {
            throw new NDSException("订单状态不为待寻源和待审核!");
        }
        List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListOccupy(order.getId());
        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)) {
            //
            ValueHolderV14 sgValueHolder = sgRpcService.voidSgStockOccupy(order, orderItems, user);
            if (sgValueHolder.getCode() != 0) {
                throw new NDSException("释放占用库存失败!");
            }
        }
        OcBOrderParam orderParam = new OcBOrderParam();
        orderParam.setOcBOrder(order);
        orderParam.setOrderItemList(orderItems);
        for (OcBOrderItem orderItem : orderItems) {
            orderItem.setExpiryDateType(null);
            orderItem.setExpiryDateRange(null);
            orderItem.setOrderLabel(null);
        }
        boolean b = omsExpiryDateStService.expiryDateStService(orderParam, user, "手动(自动)");
        if (!b) {
            for (OcBOrderItem orderItem : orderItems) {
                OcBOrderItem item = new OcBOrderItem();
                item.setId(orderItem.getId());
                item.setExpiryDateType(0);
                item.setExpiryDateRange("");
                item.setOrderLabel("");
                item.setOcBOrderId(orderItem.getOcBOrderId());
                omsOrderItemService.updateOcBOrderItem(item, order.getId());
            }
        }
        ocBOrderMapper.updateWarehouse(order.getId());

        //卡单不释放
        if (YesNoEnum.Y.getVal().equals(order.getIsDetention())) {
            return;
        }

        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)) {
            omsOccupyTaskService.addOcBOccupyTask(order, null);
        } else {
            omsOccupyTaskService.updateOcBOccupyOutStockDate(order.getId(), new Date());
        }
    }

    class AutoExpireDate implements Callable<ValueHolderV14<Integer>> {

        List<Long> autoExpireDateIds;
        User user;

        public AutoExpireDate(List<Long> autoExpireDateIds, User user) {
            this.autoExpireDateIds = autoExpireDateIds;
            this.user = user;
        }

        @Override
        public ValueHolderV14<Integer> call() throws Exception {
            int failCount = 0;
            for (Long id : autoExpireDateIds) {
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                        OcBOrder order = ocBOrderMapper.selectById(id);
                        handleAutoExpiryDate(order, user);
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("手动(自动)指定效期失败:{}", "手动(自动)指定效期失败", id), Throwables.getStackTraceAsString(e));
                    failCount++;
                } finally {
                    redisLock.unlock();
                }
            }
            ValueHolderV14<Integer> valueHolderV14 = new ValueHolderV14<>(failCount, ResultCode.SUCCESS, "success");
            return valueHolderV14;
        }
    }
}
