package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipItemMapper;
import com.jackrain.nea.oc.oms.model.request.IpVipTimeOrderItemQueryRequest;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipItem;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class IpVipTimeOrderItemService {

    @Autowired
    private IpBTimeOrderVipItemMapper ipBTimeOrderVipItemMapper;

    /**
     * 更新时效订单明细，以时效订单ID
     *
     * @param ipBTimeOrderVipId   时效订单ID
     * @param ipBTimeOrderVipItem 更新内容
     * @return
     */
    public int update(Long ipBTimeOrderVipId, IpBTimeOrderVipItem ipBTimeOrderVipItem) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("IP_B_TIME_ORDER_VIP_ID", ipBTimeOrderVipId);
        return ipBTimeOrderVipItemMapper.update(ipBTimeOrderVipItem, queryWrapper);
    }

    /**
     * 更新时效订单明细
     *
     * @param queryWrapper        更新条件
     * @param ipBTimeOrderVipItem 更新内容
     * @return
     */
    public int update(QueryWrapper queryWrapper, IpBTimeOrderVipItem ipBTimeOrderVipItem) {
        return ipBTimeOrderVipItemMapper.update(ipBTimeOrderVipItem, queryWrapper);
    }

    /**
     * 查询时效订单明细
     *
     * @param request       查询条件
     * @return              明细集合
     */
    public ValueHolderV14<List<IpBTimeOrderVipItem>> getIpBTimeOrderVipItemByMainId(IpVipTimeOrderItemQueryRequest request) {
        if (Objects.isNull(request.getObjId()) && CollectionUtils.isEmpty(request.getObjIds())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "请求参数不能为空");
        }

        if (log.isDebugEnabled()) {
            log.debug("IpVipTimeOrderItemService.getIpBTimeOrderVipItemByMainId.request:{}", JSONObject.toJSONString(request));
        }

        LambdaQueryWrapper<IpBTimeOrderVipItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(request.getObjId()), IpBTimeOrderVipItem::getIpBTimeOrderVipId, request.getObjId());
        wrapper.in(!CollectionUtils.isEmpty(request.getObjIds()), IpBTimeOrderVipItem::getIpBTimeOrderVipId, request.getObjIds());

        List<IpBTimeOrderVipItem> items = ipBTimeOrderVipItemMapper.selectList(wrapper);

        return new ValueHolderV14<>(items, ResultCode.SUCCESS, "success");
    }
}
