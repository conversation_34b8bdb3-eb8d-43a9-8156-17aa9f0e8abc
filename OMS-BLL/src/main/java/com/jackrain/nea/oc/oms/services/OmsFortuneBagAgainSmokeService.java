package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.error.MqException;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.BllSystemParameterKeyResources;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2019-08-22 14:51
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsFortuneBagAgainSmokeService {


    @Autowired
    private IpBTaobaoOrderMapper ipBTaobaoOrderMapper;
    @Autowired
    private IpBTaobaoOrderItemMapper ipBTaobaoOrderItemMapper;
    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderRecountAmountService omsOrderRecountAmountService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private SgRpcService sgRpcService;
//    @Autowired
//    private R3MqSendHelper sendHelper;
    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private IpBJingdongOrderMapper ipBJingdongOrderMapper;
    @Autowired
    private IpBJingdongOrderItemMapper ipBJingdongOrderItemMapper;
    @Autowired
    private IpBStandplatOrderMapper ipBStandplatOrderMapper;
    @Autowired
    private IpBStandplatOrderItemMapper ipBStandplatOrderItemMapper;
    @Autowired
    private OmsOrderGoBackService omsOrderGoBackService;
    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;


    /**
     * 福袋商品重新抽取
     *
     * @param ocBorder
     * @param orderItemList
     */
    @Deprecated
    @Transactional
    public ValueHolderV14 againFortuneBagSmoke(OcBOrder ocBorder, List<OcBOrderItem> orderItemList, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("福袋商品重新抽取 开始,订单ID=", ocBorder.getId()));
        }
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            //把所有不缺货的明细记录下来
            List<OcBOrderItem> notShortage = new ArrayList<>();
            // 福袋明细Map
            Map<String, List<OcBOrderItem>> fortuneBagMap = new ConcurrentHashMap<>();
            for (OcBOrderItem orderItem : orderItemList) {
                BigDecimal qtyLost = orderItem.getQtyLost();
                if (qtyLost == null || qtyLost.compareTo(BigDecimal.ZERO) == 0) {
                    notShortage.add(orderItem);
                }
                //商品类型
                Long reserveBigint01 = orderItem.getProType();
                //虚拟条码
                if (reserveBigint01 != null && SkuType.GIFT_PRODUCT == reserveBigint01) {
                    String groupGoodsMark = orderItem.getGroupGoodsMark();
                    if (!fortuneBagMap.containsKey(groupGoodsMark)) {
                        List<OcBOrderItem> orderItems = new ArrayList<>();
                        orderItems.add(orderItem);
                        fortuneBagMap.put(groupGoodsMark, orderItems);
                    } else {
                        List<OcBOrderItem> list = fortuneBagMap.get(groupGoodsMark);
                        list.add(orderItem);
                        fortuneBagMap.put(groupGoodsMark, list);
                    }
                }
            }
            if (fortuneBagMap.isEmpty()) {
                throw new NDSException(Resources.getMessage("福袋商品重新抽取: 明细数据为空"));
            }
            //将福袋商品金额复原到拆分之前的对象
            for (String bagSku : fortuneBagMap.keySet()) {
                List<OcBOrderItem> list = fortuneBagMap.get(bagSku);
                //是否福袋商品缺货
                boolean flag = false;
                for (OcBOrderItem orderItem : list) {
                    BigDecimal qtyLost = orderItem.getQtyLost();
                    if (qtyLost != null && qtyLost.compareTo(BigDecimal.ZERO) > 0) {
                        //判断是否有福袋商品缺货
                        flag = true;
                    }
                }
                //将没有缺货的福袋商品移除
                if (!flag) {
                    fortuneBagMap.remove(bagSku);
                }
            }
            List<OcBOrderItem> orderItems = new ArrayList<>();
            for (String bagSku : fortuneBagMap.keySet()) {
                List<OcBOrderItem> list = fortuneBagMap.get(bagSku);
                OcBOrderItem orderItem1 = list.get(0);//福袋商品的ooid是一样的
//                Long num = this.selectSkuQty(ocBorder, orderItem1);
                Long num = this.getFortuneBagQty(ocBorder.getId(), bagSku);
                if (num == null) {
                    throw new NDSException(Resources.getMessage("福袋商品重新抽取: 明细数量为空"));
                }
                OcBOrderItem orderItem = this.handleBagGoods(list, orderItem1.getGiftbagSku(), num);
                orderItems.add(orderItem);
            }
            //重新抽取并封装的新的明细数据
            List<OcBOrderItem> bOrderItemList = omsConstituteSplitService.encapsulationParameter(orderItems, ocBorder, user, 0);
            if (CollectionUtils.isEmpty(bOrderItemList)) {
                throw new NDSException(Resources.getMessage("福袋商品重新抽取: 重新封装明细数据失败"));
            }
            this.handleOcBOrderItem(bOrderItemList, fortuneBagMap, ocBorder, notShortage, user);
            omsOrderLogService.addUserOrderLog(ocBorder.getId(), ocBorder.getBillNo(),
                    OrderLogTypeEnum.COMBINATION_SPLIT.getKey(), "福袋商品缺货重新抽取成功", null, null, user);
            //重新查询订单数据及明细数据
            //先查找订单缺货对象
            OcBOrder ocBorderDto = ocBOrderMapper.selectById(ocBorder.getId());
            if (ocBorderDto == null) {
                throw new NDSException(Resources.getMessage("当前订单对象不存在!"));
            }
            //在查找未退款明细
            List<OcBOrderItem> bOrderItems = omsOrderItemService.selectUnSuccessRefund(ocBorderDto.getId());
            OcBOrderRelation saveOrderInfo = new OcBOrderRelation();
            saveOrderInfo.setOrderInfo(ocBorderDto);
            saveOrderInfo.setOrderItemList(bOrderItems);
            if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
                this.sendMQ(saveOrderInfo);
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 福袋商品缺货重新抽取失败", e);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("福袋商品缺货重新抽取失败");
        }
        return vh;
    }


    /**
     * 福袋商品重新抽取
     * @param orderId
     * @param user
     * @return
     */
    public ValueHolderV14 againFortuneBagDraw(Long orderId, User user){
        ValueHolderV14 vh = new ValueHolderV14();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("福袋商品重新抽取 开始,订单ID=", orderId));
        }
        OcBOrder orderDB = ocBOrderMapper.selectByID(orderId);
        List<OcBOrderItem> orderItemListDB = ocBOrderItemMapper.selectOrderItemListOccupy(orderId);
        ValueHolderV14 checkResultVH = this.checkData(orderId, orderDB, orderItemListDB);
        if (!checkResultVH.isOK()) {
            log.error(LogUtil.format("数据校验结果：{},订单ID=", orderId), JSON.toJSONString(checkResultVH));
            return checkResultVH;
        }

        Map<String, List<OcBOrderItem>> groupGoodsMarkMap = orderItemListDB.stream()
                .filter(obj -> obj.getProType() != null && (obj.getProType() == SkuType.GIFT_PRODUCT || obj.getProType() == SkuType.NO_SPLIT_COMBINE))
                .collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
        // 福袋proType=4
        List<OcBOrderItem> fortuneBagHeaderList = new ArrayList<>();
        // 福袋proType=1
        List<OcBOrderItem> fortuneBagDetailList = new ArrayList<>();
        for (String key : groupGoodsMarkMap.keySet()) {
            List<OcBOrderItem> orderItemList = groupGoodsMarkMap.get(key);
            for (OcBOrderItem orderItem : orderItemList) {
                if (orderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                    OcBOrderItem newOrderItem = new OcBOrderItem();
                    BeanUtils.copyProperties(orderItem, newOrderItem);
                    fortuneBagHeaderList.add(newOrderItem);
                }
                if (orderItem.getProType() == SkuType.GIFT_PRODUCT) {
                    OcBOrderItem newOrderItem = new OcBOrderItem();
                    BeanUtils.copyProperties(orderItem, newOrderItem);
                    fortuneBagDetailList.add(newOrderItem);
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("福袋Header:{} ,福袋明细:{},订单ID=", orderId)
                    , JSON.toJSONString(fortuneBagHeaderList), JSON.toJSONString(fortuneBagDetailList));
        }
        // 福袋明细
        if (CollectionUtils.isEmpty(fortuneBagHeaderList) || CollectionUtils.isEmpty(fortuneBagDetailList)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("福袋商品重新抽取：福袋为空");
            return vh;
        }
        // 重新抽取并封装的新的明细数据
        List<OcBOrderItem> newFortuneBagOrderItemList = omsConstituteSplitService.encapsulationParameter(fortuneBagHeaderList, orderDB, user, 0);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("新福袋明细:{},订单ID=", orderId), JSON.toJSONString(newFortuneBagOrderItemList));
        }
        if (CollectionUtils.isEmpty(newFortuneBagOrderItemList)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("福袋商品重新抽取：重新封装明细数据失败");
            return vh;
        }
        // 处理新抽取得福袋
        OmsFortuneBagAgainSmokeService omsFortuneBagAgainSmokeService = ApplicationContextHandle.getBean(OmsFortuneBagAgainSmokeService.class);
        ValueHolderV14 processResult = omsFortuneBagAgainSmokeService.processFortuneBag(orderDB, orderItemListDB, newFortuneBagOrderItemList, fortuneBagDetailList, user);
        if (!processResult.isOK()) {
            return processResult;
        }
        return vh;
    }


    private ValueHolderV14 checkData(Long orderId, OcBOrder ocBorderDto, List<OcBOrderItem> ocBOrderItemList) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (ocBorderDto == null) {
            log.error(LogUtil.format("不存在,订单ID=", orderId));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单orderId不存在");
            return vh;
        }
        if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBorderDto.getOrderStatus())) {
            log.error(LogUtil.format("订单状态不是缺货不能抽取,订单ID=", orderId));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单状态不是缺货不能抽取");
            return vh;
        }
        if (CollectionUtils.isEmpty(ocBOrderItemList)) {
            log.error(LogUtil.format("订单明细为空,订单ID=", orderId));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单明细为空");
            return vh;
        }
        return vh;
    }


    /**
     * 处理福袋明细
     * @param newFortuneBagOrderItemList 新福袋明细
     * @param oldFortuneBagOrderItemList 旧福袋明细
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 processFortuneBag(OcBOrder orderDB, List<OcBOrderItem> orderItemListDB, List<OcBOrderItem> newFortuneBagOrderItemList, List<OcBOrderItem> oldFortuneBagOrderItemList, User user) {
        Long orderId = orderDB.getId();
        ValueHolderV14 vh = new ValueHolderV14();
        // 有库存的明细
        List<OcBOrderItem> hasStockOrderItemList = orderItemListDB.stream().filter(obj -> (obj.getProType() != 4 && (obj.getQtyLost() == null || obj.getQtyLost().compareTo(BigDecimal.ZERO) == 0))).collect(Collectors.toList());
        if (hasStockOrderItemList.size() > 0) {
            // 清空订单逻辑发货单
            boolean flag = omsOrderGoBackService.emptyLogic(orderDB, user);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("清空订单逻辑发货单:{},订单ID=", orderId), flag);
            }
            if (!flag) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("福袋商品重新抽取：释放库存失败");
                return vh;
            }
        }
        List<Long> oldFortuneBagOrderItemIdList = oldFortuneBagOrderItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
        String itemStr = StringUtils.join(oldFortuneBagOrderItemIdList, ",");
        int delCount = ocBOrderItemMapper.deleteByItemIds(itemStr, orderId);
        int insertCount = ocBOrderItemMapper.batchInsert(newFortuneBagOrderItemList);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("删除旧福袋明细成功条数：{} 新增福袋明细成功条数:{},订单ID=", orderId)
                    , delCount
                    , insertCount);
        }
        // 福袋重新抽取成功
        omsOrderLogService.addUserOrderLog(orderId, orderDB.getBillNo(), OrderLogTypeEnum.GOODS_REPLACE.getKey(), "福袋缺货重新抽取成功",null, null, user);

        List<OcBOrderItem> items = ocBOrderItemMapper.selectOrderItemListAndReturn(orderId);
        ValueHolderV14<Map<Long, Integer>> holder = sgOccupiedInventoryService.shortageOccupyWareHouse(orderDB, items, user);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("占用库存结果：{},订单ID=", orderId), JSON.toJSONString(holder));
        }
        if (holder.getCode() == 0) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("福袋缺货重新占单成功,订单ID=", orderId));
            }
            sgOccupiedInventoryService.handleOccupy(orderDB, items, holder, user);
            // 调用满足库存日志
            omsOrderLogService.addUserOrderLog(orderId, orderDB.getBillNo(), OrderLogTypeEnum.LACK_RE_OCCUPANCY.getKey(), "福袋缺货重新占单成功",null, null, user);
        } else {
            // 调用满足库存日志
            omsOrderLogService.addUserOrderLog(orderId, orderDB.getBillNo(), OrderLogTypeEnum.LACK_RE_OCCUPANCY.getKey(), "订单占用库存失败：" + holder.getMessage(),null, null, user);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("福袋缺货重新抽取成功，但订单占用库存失败");
        }
        return vh;
    }






    /**
     * 处理数据
     *
     * @param orderItems 生成的新福袋明细
     * @param map        原福袋明细数据
     */
    public void handleOcBOrderItem(List<OcBOrderItem> orderItems, Map<String, List<OcBOrderItem>> map,
                                   OcBOrder ocBOrder, List<OcBOrderItem> notShortage, User user) {
        if (CollectionUtils.isNotEmpty(notShortage)) {

            for (OcBOrderItem orderItem : notShortage) {
                //将不缺货的明细数量更新为0,释放库存
                orderItem.setQty(BigDecimal.ZERO);
            }
            //调用释放库存服务
            OcBOrderRelation relation = new OcBOrderRelation();
            relation.setOrderInfo(ocBOrder);
            relation.setOrderItemList(notShortage);
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + " 调用释放库存服务入参:{}", JSONObject.toJSONString(relation));
            }
            ValueHolderV14 sgValueHolder = sgRpcService.querySearchStockAndModifyGoodsInfo(relation, user);
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + " 调用释放库存服务返参:{}", sgValueHolder.toJSONObject());
            }
            if (sgValueHolder.getCode() == -1) {
                log.error(this.getClass().getName() + " 福袋商品缺货重新抽取释放库存失败" + sgValueHolder.getMessage());
                throw new NDSException("福袋商品缺货重新抽取释放库存失败");
            }
        }
        Long id = ocBOrder.getId();
        for (String bagSku : map.keySet()) {
            List<OcBOrderItem> list = map.get(bagSku);
            for (OcBOrderItem orderItem : list) {
                ocBOrderItemMapper.deleteByItemId(orderItem.getId(), id);
            }
        }
        for (OcBOrderItem orderItem : orderItems) {
            ocBOrderItemMapper.insert(orderItem);
        }
        List<OcBOrderItem> orderItems1 = ocBOrderItemMapper.selectOrderItemList(id);
        //更新主表的商品数量
        BigDecimal conutQty = orderItems1.stream().map(OcBOrderItem::getQty).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        OcBOrderRelation relation01 = new OcBOrderRelation();
        relation01.setOrderInfo(ocBOrder);
        List<OcBOrderItem> list = new ArrayList<>();
        omsOrderRecountAmountService.doRecountAmount(relation01, list, orderItems1);
        //将订单状态更改为待分配  并将占单状态置为3  发送MQ从新走占单流程
        OcBOrder order = new OcBOrder();
        order.setId(ocBOrder.getId());
        order.setQtyAll(conutQty);
        order.setSkuKindQty(BigDecimal.valueOf(orderItems1.size(), 4));
        order.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
        omsOrderService.updateOrderInfo(order);
        try {
            for (String bagSku : map.keySet()) {
                List<OcBOrderItem> orderItems2 = map.get(bagSku);
                for (OcBOrderItem orderItem : orderItems2) {
                    //删除ES的数据
                    SpecialElasticSearchUtil.delDocument(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                            OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, orderItem.getId(), id);
                }
            }

            SpecialElasticSearchUtil.indexDocuments(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.OC_B_ORDER_ITEM_TYPE_NAME, orderItems,
                    "OC_B_ORDER_ID");

        } catch (Exception e) {
            log.error(this.getClass().getName() + " 删除es明细异常!", e);
        }
    }


    private Long getFortuneBagQty (Long orderId, String groupGoodsMark) {
        List<OcBOrderItem> ocBOrderItemList =  ocBOrderItemMapper.selectOrderItemListOccupy(orderId);
        for (OcBOrderItem orderItem : ocBOrderItemList) {
            if (orderItem.getProType() == SkuType.NO_SPLIT_COMBINE && orderItem.getGroupGoodsMark().equals(groupGoodsMark)) {
                return orderItem.getQty().longValue();
            }
        }
        return null;
    }


    /**
     * 通过平台单号到中间表查询数量
     *
     * @param ocBOrder
     */
    private Long selectSkuQty(OcBOrder ocBOrder, OcBOrderItem orderItem) {
        Integer platform = ocBOrder.getPlatform();
        String sourceCode = ocBOrder.getSourceCode(); //平台单号
        String ooid = orderItem.getOoid();
        Long num = null;
        if (PlatFormEnum.TAOBAO.getCode().equals(platform)) {
            IpBTaobaoOrder ipBTaobaoOrder = ipBTaobaoOrderMapper.selectTaobaoOrderByTid(sourceCode);
            if (ipBTaobaoOrder != null) {
                Long id = ipBTaobaoOrder.getId();
                IpBTaobaoOrderItem ipBTaobaoOrderItem = ipBTaobaoOrderItemMapper.selectTaobaoOrderItemByOid(id, ooid);
                num = ipBTaobaoOrderItem.getNum();
            }
        } else if (PlatFormEnum.JINGDONG.getCode().equals(platform)) {
            IpBJingdongOrder ipBJingdongOrder = ipBJingdongOrderMapper.selectJingdongOrderByOrderId(NumberUtils.toLong(sourceCode));
            if (ipBJingdongOrder != null) {
                List<IpBJingdongOrderItemExt> ipBJingdongOrderItemExts = ipBJingdongOrderItemMapper.selectJingdongOrderListBySkuId(ipBJingdongOrder.getId(), NumberUtils.toLong(ooid));
                IpBJingdongOrderItemExt ipBJingdongOrderItemExt = ipBJingdongOrderItemExts.get(0);
                num = ipBJingdongOrderItemExt.getItemTotal();
            }
        } else {
            IpBStandplatOrder ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(sourceCode);
            if (ipBStandplatOrder != null) {
                String numIid = orderItem.getNumIid();
                String skuNumiid = orderItem.getSkuNumiid();
                Long id = ipBStandplatOrder.getId();
                List<IpBStandplatOrderItemEx> ipBStandplatOrderItemExes = ipBStandplatOrderItemMapper.selectOrderItemListByTid(id, skuNumiid, numIid);
                IpBStandplatOrderItemEx ipBStandplatOrderItemEx = ipBStandplatOrderItemExes.get(0);
                num = ipBStandplatOrderItemEx.getNum();
            }
        }
        return num;
    }

    /**
     * 处理福袋明细信息
     *
     * @param list
     * @param bagSku
     */
    private OcBOrderItem handleBagGoods(List<OcBOrderItem> list, String bagSku, Long num) {
        OcBOrderItem orderItem = new OcBOrderItem();
        //福袋的成交金额之和
        BigDecimal realAmt = list.stream().map(OcBOrderItem::getRealAmt).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        //总的调整金额
        BigDecimal adjustAmt = list.stream().map(OcBOrderItem::getAdjustAmt).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        //总的平摊金额
        BigDecimal orderSplitAmt = list.stream().map(OcBOrderItem::getOrderSplitAmt).
                reduce(BigDecimal.ZERO, BigDecimal::add);

        orderItem.setPsCSkuEcode(bagSku); //条码
        orderItem.setQty(BigDecimal.valueOf(num)); //数量
        orderItem.setRealAmt(realAmt);
        orderItem.setAdjustAmt(adjustAmt);
        orderItem.setOrderSplitAmt(orderSplitAmt);
        orderItem.setProType(Long.valueOf(SkuType.GIFT_PRODUCT + ""));
        OcBOrderItem item = list.get(0);
        orderItem.setOoid(item.getOoid());
        orderItem.setRefundStatus(item.getRefundStatus());
        orderItem.setSkuNumiid(item.getSkuNumiid());
        orderItem.setNumIid(item.getNumIid());
        // 小米有品
        orderItem.setReserveVarchar04(item.getReserveVarchar04());
        orderItem.setIsPresalesku(item.getIsPresalesku());
        orderItem.setPicPath(item.getPicPath());
        orderItem.setTitle(item.getTitle());
        return orderItem;
    }


    /**
     * 通知占单程序重新占单
     *
     * @param saveOrderInfo
     */
    private void sendMQ(OcBOrderRelation saveOrderInfo) {
        if (saveOrderInfo != null) {
            long saveOrderId = saveOrderInfo.getOrderInfo().getId();
            String billNo = saveOrderInfo.getOrderInfo().getBillNo();
            String msgKey = "TB_TR_" + saveOrderId + "_" + billNo;
            OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
            orderMqInfo.setChannelType(ChannelType.TAOBAO);
            orderMqInfo.setOperateType(OperateType.TOBE_CONFIRMED);
            orderMqInfo.setOrderId(saveOrderId);
            orderMqInfo.setOrderNo(billNo);
            List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
            mqInfoList.add(orderMqInfo);
            String jsonValue = JSONObject.toJSONString(mqInfoList);
            if (log.isDebugEnabled()) {
                String string = (String) jsonValue;
                log.debug(this.getClass().getName() + " message大小:" + string.getBytes().length);
            }
            String messageId = null;
            //获取Topic
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            String topic = config.getProperty("r3.oc.oms.tobeConfirmed.mq.topic", "BJ_DEV_R3_OC_OMS_CALL_TOBECONFIRMED");
            String topic = Mq5Constants.TOPIC_R3_OC_OMS_CALL_TOBECONFIRMED;
//            String tag = config.getProperty("r3.oc.oms.tobeConfirmed.mq.tag", "OperateTobeConfirmed");
            String tag = Mq5Constants.TAG_R3_OC_OMS_CALL_TOBECONFIRMED;
            String uuid = UUID.randomUUID().toString();
            try {
//                messageId = sendHelper.sendMessage(jsonValue, topic, tag, uuid);
                defaultProducerSend.sendTopic(topic, tag, jsonValue, uuid);

            } catch (Exception e) {
                log.error(this.getClass().getName() + " 福袋商品缺货重新抽取发送MQ失败=" + messageId + "MQKey=" + uuid, e);
                e.printStackTrace();
                throw new MqException("mq发送消息异常");
            }
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + "  福袋商品缺货重新抽取发送MQ成功:MQId:{},MQKey:{}", messageId, uuid);
            }
        }
    }
}
