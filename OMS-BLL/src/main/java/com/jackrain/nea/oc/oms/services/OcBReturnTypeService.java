package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.util.TypeUtils;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.enums.YesOrNoEnum;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBReturnTypeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnTypeMapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnType;
import com.jackrain.nea.oc.oms.model.table.OcBReturnTypeItem;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 退款分类service
 *
 * <AUTHOR>
 * @date 2020/12/30 9:59 上午
 */
@Slf4j
@Service
public class OcBReturnTypeService {

    @Autowired
    private OcBReturnTypeMapper returnTypeMapper;
    @Autowired
    private OcBReturnTypeItemMapper returnTypeItemMapper;

    public ValueHolder addReturnType(QuerySession session) {
        ValueHolder holder;
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start Add Return Type Receive Params#{}"), param);
        }

        AssertUtil.assertException(Objects.isNull(param), "请求参数异常");

        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        AssertUtil.assertException(Objects.isNull(fixColumn), "请求参数异常:fixcolumn为空");

        JSONObject obj = fixColumn.getJSONObject("OC_B_RETURN_TYPE");
        JSONArray array = fixColumn.getJSONArray("OC_B_RETURN_TYPE_ITEM");

        List<OcBReturnTypeItem> returnTypeItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(array)) {
            returnTypeItems = JSONObject.parseArray(array.toJSONString(), OcBReturnTypeItem.class);
        }
        Long objId = param.getLong("objid");
        if (objId != null && objId < 0) {
            AssertUtil.assertException(MapUtils.isEmpty(obj), "请求参数异常:退款分类为空");
            // AssertUtil.assertException(CollectionUtils.isEmpty(returnTypeItems), "请求参数异常:退款明细为空");
            holder = this.getInstance().doAddReturnType(obj, returnTypeItems, session.getUser());
        } else {
            holder = this.getInstance().doUpdateReturnType(obj, objId, returnTypeItems, session.getUser());
        }
        return holder;
    }

    /**
     * 新增
     *
     * @param obj   退款分类
     * @param items 退款分类明细
     * @param user  操作用户
     * @return ValueHolder
     */
    public ValueHolder doAddReturnType(JSONObject obj, List<OcBReturnTypeItem> items, User user) {
        String eCode = TypeUtils.castToString(obj.get("ECODE"));
        String eName = TypeUtils.castToString(obj.get("ENAME"));

        AssertUtil.assertException(checkCodeRepeat(eCode), "参数错误：编码重复");
        AssertUtil.assertException(checkNameRepeat(eName), "参数错误：名称重复");
        // 新增主表
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        Long returnTypeId = ModelUtil.getSequence("OC_B_RETURN_TYPE");
        obj.put("ID", returnTypeId);
        obj.put("AD_CLIENT_ID", user.getClientId());
        obj.put("AD_ORG_ID", user.getOrgId());
        obj.put("OWNERID", user.getId());
        obj.put("MODIFIERID", user.getId());
        obj.put("OWNERNAME", user.getName());
        obj.put("OWNERENAME", user.getEname());
        obj.put("MODIFIERNAME", user.getName());
        obj.put("MODIFIERENAME", user.getEname());
        obj.put("CREATIONDATE", timestamp);
        obj.put("MODIFIEDDATE", timestamp);
        int count = returnTypeMapper.insertReturnType(obj);

        if (count > 0) {
            // 新增明细，明细字段必填，已判空
            for (OcBReturnTypeItem returnTypeItem : items) {
                if (returnTypeItem.getId() < 0) {
                    returnTypeItem.setOcBReturnTypeId(obj.getLong("ID"));
                    doAddReturnTypeItem(user, returnTypeItem, returnTypeId);
                }
            }
            return ValueHolderUtils.getSuccessValueHolder(obj.getLong("ID"), "OC_B_RETURN_TYPE");
        }
        return ValueHolderUtils.getFailValueHolder("新增失败");
    }

    /**
     * 更新
     *
     * @param obj   退款分类
     * @param objId 主表id
     * @param items 退款分类明细
     * @param user  操作用户
     * @return ValueHolder
     */
    public ValueHolder doUpdateReturnType(JSONObject obj, Long objId, List<OcBReturnTypeItem> items, User user) {

        OcBReturnType returnType = returnTypeMapper.selectById(objId);
        AssertUtil.assertException(Objects.isNull(returnType), "当前记录已不存在");
        if (Objects.nonNull(obj)) {
            String eCode = "";
            String eName = "";
            if (obj.containsKey("ECODE")) {
                eCode = TypeUtils.castToString(obj.get("ECODE"));
            }
            if (obj.containsKey("ENAME")) {
                eName = TypeUtils.castToString(obj.get("ENAME"));
            }
            String systemDefault = returnType.getSystemDefault();
            String tempECode = returnType.getEcode();
            String tempEName = returnType.getEname();

            AssertUtil.assertException(YesNoEnum.ONE.getKey().equals(systemDefault), "系统默认退款分类不可修改");
            if (StringUtils.isNotEmpty(eCode) && !tempECode.equals(eCode)) {
                AssertUtil.assertException(checkCodeRepeat(eCode), "参数错误：编码重复");
            }
            if (StringUtils.isNotEmpty(eName) && !tempEName.equals(eName)) {
                AssertUtil.assertException(checkNameRepeat(eName), "参数错误：名称重复");
            }

            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            obj.put("ID", objId);
            obj.put("AD_CLIENT_ID", user.getClientId());
            obj.put("AD_ORG_ID", user.getOrgId());
            obj.put("MODIFIERID", user.getId());
            obj.put("MODIFIERNAME", user.getName());
            obj.put("MODIFIERENAME", user.getEname());
            obj.put("CREATIONDATE", timestamp);
            obj.put("MODIFIEDDATE", timestamp);
            returnTypeMapper.updateReturnType(obj);
        }

        if (CollectionUtils.isNotEmpty(items)) {
            // 更新或新增明细
            for (OcBReturnTypeItem item : items) {
                Long itemId = item.getId();
                if (itemId < 0) {
                    item.setOcBReturnTypeId(objId);
                    doAddReturnTypeItem(user, item, objId);
                } else {
                    OcBReturnTypeItem itemTempVal = returnTypeItemMapper.selectById(itemId);
                    AssertUtil.assertException(Objects.isNull(itemTempVal),
                            String.format("参数异常:明细id[%s]不存在", itemId));
                    if (!Objects.equals(itemTempVal.getEname(), item.getEname())) {
                        // 描述修改后去重判断
                        AssertUtil.assertException(checkItemNameRepeat(item.getEname(), objId),
                                String.format("参数异常:退款分类明细描述重复[%s]", item.getEname()));
                    }
                    if (!Objects.equals(itemTempVal.getEname(), item.getEname())
                            || !Objects.equals(itemTempVal.getIsactive(), item.getIsactive())) {
                        // 明细有修改字段
                        returnTypeItemMapper.updateById(item);
                    }
                }
            }
        }
        return ValueHolderUtils.getSuccessValueHolder(objId, "OC_B_RETURN_TYPE");
    }

    /**
     * 新增明细
     *
     * @param user           操作用户
     * @param returnTypeItem 明细
     */
    private void doAddReturnTypeItem(User user, OcBReturnTypeItem returnTypeItem, Long objId) {
        AssertUtil.assertException(checkItemNameRepeat(returnTypeItem.getEname(), objId),
                String.format("参数异常:退款分类明细描述重复[%s]", returnTypeItem.getEname()));
        OperateUserUtils.saveOperator(returnTypeItem, user);
        returnTypeItem.setId(ModelUtil.getSequence("OC_B_RETURN_TYPE_ITEM"));
        returnTypeItemMapper.insert(returnTypeItem);
    }

    private boolean checkItemNameRepeat(String name, Long returnTypeId) {
        int count = returnTypeItemMapper.selectCountByName(name, returnTypeId);
        return count > 0;
    }

    private boolean checkCodeRepeat(String code) {
        int count = returnTypeMapper.selectCountByCode(code);
        return count > 0;
    }

    private boolean checkNameRepeat(String name) {
        int count = returnTypeMapper.selectCountByName(name);
        return count > 0;
    }

    public OcBReturnTypeService getInstance() {
        return ApplicationContextHandle.getBean(this.getClass());
    }

}
