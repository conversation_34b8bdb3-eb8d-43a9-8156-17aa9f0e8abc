package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCRegion;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderBeefOrderExtend;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.vo.OcBOrderImpBeefOrderVO;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @author: 李龙飞
 * @create: 2019-05-13 17:33
 **/
@Component
@Slf4j
public class OcBOrderImportBeefOrderService {
    //订单明细表
    private static final String ORDER_ITEM_TABLE_NAME = "OC_B_ORDER_ITEM";
    //订单表
    private static final String ORDER_TABLE_NAME = "OC_B_ORDER_BEEF";
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    OcBOrderItemMapper ocBorderItemMapper;
    @Autowired
    OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;
    @Autowired
    private ThreadPoolTaskExecutor orderImportThreadPoolExecutor;

    @Transactional(rollbackFor = Exception.class)
    public void batchSaveOrders(List<OcBOrderImpBeefOrderVO> orderList, Map<String, CpShop> cpShopMap,
                                Map<String, ProductSku> productSkuMap, Map<String, StCBusinessType> businessTypeMap,
                                Map<String, CpCPhyWarehouse> warehouseMap, Map<String, CpLogistics> logisticsMap,
                                Map<String, CpCRegion> provinceMap, Map<String, CpCRegion> cityMap,
                                Map<String, List<CpCRegion>> areaMap, User loginUser) throws Exception {
        //按照单据编号分组
        Map<String, List<OcBOrderImpBeefOrderVO>> orderMap =
                orderList.stream().collect(Collectors.groupingBy(OcBOrderImpBeefOrderVO::getBillNo));
        Set<String> keys = orderMap.keySet();
        List<String> keyList = new ArrayList<>(keys);
        int batchSize = (int) Math.ceil((double) keyList.size() / 20);
        List<List<String>> partitionedKeys = Lists.partition(keyList, batchSize);
        List<Map<String, List<OcBOrderImpBeefOrderVO>>> pageList = partitionedKeys.stream()
                .map(partition -> partition.stream()
                        .collect(Collectors.toMap(
                                key -> key,
                                orderMap::get
                        ))
                )
                .collect(Collectors.toList());

        List<OcBOrderBeefOrderExtend> resultOrderList = new ArrayList<>();
        try {
            List<Future<List<OcBOrderBeefOrderExtend>>> results = new ArrayList<>();
            for (Map<String, List<OcBOrderImpBeefOrderVO>> orderVoMap : pageList) {
                results.add(orderImportThreadPoolExecutor.submit(new CallableBuildOcBOrder(orderVoMap, cpShopMap,
                        businessTypeMap, warehouseMap, logisticsMap, productSkuMap, provinceMap, cityMap, areaMap, loginUser)));
            }
            //线程执行结果获取
            for (Future<List<OcBOrderBeefOrderExtend>> futureResult : results) {
                List<OcBOrderBeefOrderExtend> orderExtends = futureResult.get();
                resultOrderList.addAll(orderExtends);
            }
            OcBOrderImportBeefOrderService bean = ApplicationContextHandle.getBean(OcBOrderImportBeefOrderService.class);
            bean.batchSave(resultOrderList);
        } catch (Exception ex) {
            log.error(LogUtil.format("订单导入异常，异常信息,error：{}"), Throwables.getStackTraceAsString(ex));
            throw ex;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<OcBOrderBeefOrderExtend> resultOrderList) {
        //分批调用批量插入
        List<List<OcBOrderBeefOrderExtend>> pageList = Lists.partition(resultOrderList, 500);
        for (List<OcBOrderBeefOrderExtend> ocBOrderExtends : pageList) {
            batchSaveBill(ocBOrderExtends);
        }
    }

    private void batchSaveBill(List<OcBOrderBeefOrderExtend> ocBOrderExtends) {
        List<OcBOrder> ocBOrderList = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
        List<OcBOrderLog> OcBOrderLogList = new ArrayList<>();
        List<OcBToBeConfirmedTask> ocBToBeConfirmedTaskList = new ArrayList<>();
        for (OcBOrderBeefOrderExtend orderExtend : ocBOrderExtends) {
            ocBOrderList.add(orderExtend.getOrder());
            ocBOrderItemList.addAll(orderExtend.getOrderItemList());
            OcBOrderLogList.add(orderExtend.getOcBOrderLog());
            // 构建 占单任务表数据
            if (orderExtend.getOcBToBeConfirmedTask() != null) {
                ocBToBeConfirmedTaskList.add(orderExtend.getOcBToBeConfirmedTask());
            }
        }
        ocBOrderMapper.batchInsert(ocBOrderList);
        ocBorderItemMapper.batchInsert(ocBOrderItemList);
        omsOrderLogService.save(OcBOrderLogList);
        if (CollectionUtils.isNotEmpty(ocBToBeConfirmedTaskList)) {
            toBeConfirmedTaskService.batchInsertToBeConfirmedTask(ocBToBeConfirmedTaskList);
        }

    }

    private List<OcBOrderBeefOrderExtend> buildOrderAndItems(Map<String, List<OcBOrderImpBeefOrderVO>> orderVoMap,
                                                             Map<String, CpShop> cpShopMap,
                                                             Map<String, ProductSku> productSkuMap,
                                                             Map<String, StCBusinessType> businessTypeMap,
                                                             Map<String, CpCPhyWarehouse> warehouseMap,
                                                             Map<String, CpLogistics> logisticsMap,
                                                             Map<String, CpCRegion> provinceMap,
                                                             Map<String, CpCRegion> cityMap,
                                                             Map<String, List<CpCRegion>> areaMap, User user) {
        List<OcBOrderBeefOrderExtend> orderExtends = new ArrayList<>();
        for (Map.Entry<String, List<OcBOrderImpBeefOrderVO>> entry : orderVoMap.entrySet()) {
            String billNo = entry.getKey();
            List<OcBOrderImpBeefOrderVO> orderVOList = entry.getValue();
            OcBOrderBeefOrderExtend orderExtend = new OcBOrderBeefOrderExtend();
            orderExtends.add(orderExtend);
            OcBOrder ocBOrder = new OcBOrder();
            orderExtend.setOrder(ocBOrder);
            initDefault(ocBOrder);
            ocBOrder.setId(ModelUtil.getSequence(ORDER_TABLE_NAME));
            ocBOrder.setBillNo(billNo);
            ocBOrder.setOrderStatus(orderVOList.get(0).getOrderStatus());
            ocBOrder.setSourceCode(orderVOList.get(0).getSourceCode());
            ocBOrder.setTid(orderVOList.get(0).getSourceCode());
            ocBOrder.setMergeSourceCode(orderVOList.get(0).getSourceCode());
            ocBOrder.setOrderType(OrderTypeEnum.getValByText(orderVOList.get(0).getOrderTypeName()));
            ocBOrder.setReceiverName(orderVOList.get(0).getReceiverName());
            ocBOrder.setReceiverMobile(orderVOList.get(0).getReceiverMobile());
            ocBOrder.setReceiverAddress(orderVOList.get(0).getReceiverAddress());
            ocBOrder.setOaid(orderVOList.get(0).getOaid());
            ocBOrder.setExpresscode(orderVOList.get(0).getExpresscode());
            ocBOrder.setBuyerMessage(orderVOList.get(0).getBuyerMessage());
            ocBOrder.setSellerMemo(orderVOList.get(0).getSellerMemo());
            ocBOrder.setOrderDate(orderVOList.get(0).getOrderDate());
            ocBOrder.setPayTime(orderVOList.get(0).getPayTime());
            ocBOrder.setScanTime(orderVOList.get(0).getScanTime());
            if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus())) {
                ocBOrder.setOutStatus(2);
            } else {
                //未发货填入OM单号
                ocBOrder.setSuffixInfo(ocBOrder.getBillNo());
                ocBOrder.setOutStatus(1);
            }
            // 设置店铺信息
            CpShop cpShop = cpShopMap.get(orderVOList.get(0).getShopCode());
            if (cpShop != null) {
                ocBOrder.setCpCShopId(cpShop.getCpCShopId());
                ocBOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
                ocBOrder.setCpCShopSellerNick(cpShop.getSellerNick());
                ocBOrder.setCpCShopEcode(cpShop.getEcode());
                ocBOrder.setPlatform(Optional.ofNullable(cpShop.getCpCPlatformId()).orElse(-1L).intValue());
            }
            // 设置业务类型
            if (StringUtils.isNotBlank(orderVOList.get(0).getBusinessTypeCode())) {
                StCBusinessType businessType = businessTypeMap.get(orderVOList.get(0).getBusinessTypeCode());
                if (businessType != null) {
                    ocBOrder.setBusinessTypeId(businessType.getId());
                    ocBOrder.setBusinessTypeCode(businessType.getEcode());
                    ocBOrder.setBusinessTypeName(businessType.getEname());
                }
            }
            // 设置仓库
            if (StringUtils.isNotBlank(orderVOList.get(0).getCpCPhyWarehouseEname())) {
                CpCPhyWarehouse warehouse = warehouseMap.get(orderVOList.get(0).getCpCPhyWarehouseEname());
                if (warehouse != null) {
                    ocBOrder.setCpCPhyWarehouseId(warehouse.getId());
                    ocBOrder.setCpCPhyWarehouseEcode(warehouse.getEcode());
                    ocBOrder.setCpCPhyWarehouseEname(warehouse.getEname());
                }
            }
            // 设置仓库
            if (StringUtils.isNotBlank(orderVOList.get(0).getCpCLogisticsEcode())) {
                CpLogistics logistics = logisticsMap.get(orderVOList.get(0).getCpCLogisticsEcode());
                if (logistics != null) {
                    ocBOrder.setCpCLogisticsId(logistics.getId());
                    ocBOrder.setCpCLogisticsEcode(logistics.getEcode());
                    ocBOrder.setCpCLogisticsEname(logistics.getEname());
                }
            }
            // 省的赋值
            if (StringUtils.isNotBlank(orderVOList.get(0).getCpCRegionProvinceEname())) {
                CpCRegion province = provinceMap.get(orderVOList.get(0).getCpCRegionProvinceEname());
                if (province != null) {
                    ocBOrder.setCpCRegionProvinceId(province.getId());
                    ocBOrder.setCpCRegionProvinceEcode(province.getEcode());
                    ocBOrder.setCpCRegionProvinceEname(province.getEname());
                }
            }
            // 市的赋值
            if (StringUtils.isNotBlank(orderVOList.get(0).getCpCRegionCityEname())) {
                CpCRegion city = cityMap.get(orderVOList.get(0).getCpCRegionCityEname());
                if (city != null) {
                    ocBOrder.setCpCRegionCityId(city.getId());
                    ocBOrder.setCpCRegionCityEcode(city.getEcode());
                    ocBOrder.setCpCRegionCityEname(city.getEname());
                }
            }

            // 区的赋值
            if (StringUtils.isNotBlank(orderVOList.get(0).getCpCRegionAreaEname())) {
                List<CpCRegion> areaList = areaMap.get(orderVOList.get(0).getCpCRegionAreaEname());
                if (CollectionUtils.isNotEmpty(areaList)) {
                    // 根据市的 C_ID 筛选出属于该市的区
                    CpCRegion area = areaList.stream()
                            .filter(a -> ocBOrder.getCpCRegionCityId() != null
                                    && a.getCUpId().equals(ocBOrder.getCpCRegionCityId()))
                            .findFirst()
                            .orElse(null);
                    if (area != null) {
                        ocBOrder.setCpCRegionAreaId(area.getId());
                        ocBOrder.setCpCRegionAreaEcode(area.getEcode());
                        ocBOrder.setCpCRegionAreaEname(area.getEname());
                    }
                }
            }
            makeCreateField(ocBOrder, user);
            ocBOrder.setOwnerename(user.getEname());
            ocBOrder.setModifierename(user.getEname());
            //构建明细
            List<OcBOrderItem> orderItemList = buildOcBOrderItems(ocBOrder, orderVOList, productSkuMap, user);
            orderExtend.setOrderItemList(orderItemList);
            //详细地址处理
            OrderAddressConvertUtil.convert(ocBOrder);
            // 重新订单头金额
            recountAmount(ocBOrder, orderItemList);

            if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(ocBOrder.getOrderStatus())) {
                OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                toBeConfirmedTask.setOrderId(ocBOrder.getId());
                toBeConfirmedTask.setCreationdate(new Date());
                toBeConfirmedTask.setStatus(1);
                toBeConfirmedTask.setIsactive("Y");
                orderExtend.setOcBToBeConfirmedTask(toBeConfirmedTask);
            }
            OcBOrderLog ocBOrderLog = omsOrderLogService.getOcBOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_IMPORT.getKey(), "订单导入成功", "", "", user);
            orderExtend.setOcBOrderLog(ocBOrderLog);
        }
        return orderExtends;
    }

    /**
     * 设置默认值
     *
     * @param ocBOrder
     */
    public void initDefault(OcBOrder ocBOrder) {
        if (ocBOrder.getIsInvoice() == null) {
            ocBOrder.setIsInvoice(0);
        }
        if (ocBOrder.getIsGeninvoiceNotice() == null) {
            ocBOrder.setIsGeninvoiceNotice(0);
        }
        if (ocBOrder.getIsCalcweight() == null) {
            ocBOrder.setIsCalcweight(0);
        }
        if (ocBOrder.getIsMerge() == null) {
            ocBOrder.setIsMerge(0);
        }
        if (ocBOrder.getIsSplit() == null) {
            ocBOrder.setIsSplit(0);
        }
        if (ocBOrder.getIsInterecept() == null) {
            ocBOrder.setIsInterecept(0);
        }
        if (ocBOrder.getIsInreturning() == null) {
            ocBOrder.setIsInreturning(0);
        }
        if (ocBOrder.getIsHasgift() == null) {
            ocBOrder.setIsHasgift(0);
        }
        if (ocBOrder.getIsJcorder() == null) {
            ocBOrder.setIsJcorder(0);
        }
        if (ocBOrder.getIsCombination() == null) {
            ocBOrder.setIsCombination(0);
        }
        if (ocBOrder.getIsOutUrgency() == null) {
            ocBOrder.setIsOutUrgency(0);
        }
        if (ocBOrder.getIsHasTicket() == null) {
            ocBOrder.setIsHasTicket(0);
        }
        if (ocBOrder.getOrderFlag() == null) {
            ocBOrder.setOrderFlag("0");
        }
        ocBOrder.setIsInterecept(0);
        ocBOrder.setIsInreturning(0);
        ocBOrder.setQtySplit(0L);
        ocBOrder.setIsSplit(0);
        ocBOrder.setIsMerge(0);
        ocBOrder.setIsCancelMerge(0);
        ocBOrder.setOrderSource("牛肉旺店通切换导入");
        ocBOrder.setInvoiceStatus(0);
        ocBOrder.setOccupyStatus(OrderOccupyStatus.STATUS_0);
        ocBOrder.setIsSameCityPurchase(0);
        ocBOrder.setWmsCancelStatus(0);
        ocBOrder.setRefundConfirmStatus(0);
        ocBOrder.setAutoAuditStatus(0);
        ocBOrder.setIsModifiedOrder(0);
        //配货时间
        ocBOrder.setDistributionTime(null);
        // 审核时间
        ocBOrder.setAuditTime(null);
        // 拆单状态
        ocBOrder.setSplitStatus(0);
        // 是否换货未入库
        ocBOrder.setIsExchangeNoIn(0L);
        if (ocBOrder.getServiceAmt() == null) {
            ocBOrder.setServiceAmt(BigDecimal.ZERO);
        }
        if (ocBOrder.getShipAmt() == null) {
            ocBOrder.setShipAmt(BigDecimal.ZERO);
        }
        ocBOrder.setPayType(0);
    }

    private List<OcBOrderItem> buildOcBOrderItems(OcBOrder ocBOrder, List<OcBOrderImpBeefOrderVO> orderVOList,
                                                  Map<String, ProductSku> proSkuMap, User user) {
        List<OcBOrderItem> orderItemList = new ArrayList<>();
        for (OcBOrderImpBeefOrderVO orderVO : orderVOList) {
            OcBOrderItem item = new OcBOrderItem();
            orderItemList.add(item);
            makeCreateField(item, user);
            item.setModifierename(user.getEname());
            item.setOwnerename(user.getEname());
            item.setId(ModelUtil.getSequence(ORDER_ITEM_TABLE_NAME));
            item.setOcBOrderId(ocBOrder.getId());
            item.setOoid(orderVO.getOoid());
            if (StringUtils.isNotEmpty(orderVO.getGiftType())) {
                item.setGiftType(orderVO.getGiftType());
                item.setIsGift(1);
            } else {
                item.setIsGift(0);
            }
            item.setQty(orderVO.getQty());
            item.setPsCSkuEcode(orderVO.getPsCSkuEcode().toUpperCase());
            ProductSku productSku = proSkuMap.get(item.getPsCSkuEcode());
            // 供应类型 0 普通 1.代销轻供 2.寄售轻供
            item.setPsCProSupplyType(productSku.getPsCProSupplyType());
            item.setIsManualAdd("1");
            item.setQtyRefund(BigDecimal.ZERO);
            item.setTid(ocBOrder.getTid());
            item.setProType((long) productSku.getSkuType());
            item.setNumIid("0");
            item.setPsCSkuId(productSku.getId());
            item.setPsCSkuPtEcode(productSku.getSkuEcode());
            item.setPsCSkuEname(productSku.getSkuName());
            item.setSkuSpec(productSku.getSkuSpec());
            item.setBarcode(productSku.getBarcode69());
            item.setPsCProId(productSku.getProdId());
            item.setPsCProEcode(productSku.getProdCode());
            item.setPsCProEname(productSku.getName());
            item.setPsCBrandId(productSku.getPsCBrandId());
            item.setSex(productSku.getSex());
            item.setPsCClrId(productSku.getColorId());
            item.setPsCClrEcode(productSku.getColorCode());
            item.setPsCClrEname(productSku.getColorName());
            item.setPsCSizeId(productSku.getSizeId());
            item.setPsCSizeEcode(productSku.getSizeCode());
            item.setPsCSizeEname(productSku.getSizeName());
            item.setPsCProMaterieltype(productSku.getMaterialType());
            item.setStandardWeight(Optional.ofNullable(productSku.getWeight()).orElse(BigDecimal.ZERO));
            item.setRefundStatus(OcOrderRefundStatusEnum.NOTREFUND.getVal());

            // 一头牛优化内容：补充字段赋值 0917 产线问题修复
            item.setMDim4Id(productSku.getMDim4Id());
            item.setMDim6Id(productSku.getMDim6Id());
            if ("Y".equals(productSku.getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            // 吊牌价
            item.setPriceTag(Optional.ofNullable(productSku.getPricelist()).orElse(BigDecimal.ZERO));
            // 吊牌价
            item.setPriceList(Optional.ofNullable(productSku.getPricelist()).orElse(BigDecimal.ZERO));
            // 金额相关字段的值依靠前端传入，若前端未传默认给0 成交金额，成交单价，平台售价之前已经处理了
            item.setPriceActual(orderVO.getPriceActual());
            item.setPrice(orderVO.getPriceActual());
            item.setRealAmt(orderVO.getRealAmt());
            // 平摊金额
            item.setOrderSplitAmt(Optional.ofNullable(orderVO.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            // 商品优惠金额
            item.setAmtDiscount(Optional.ofNullable(orderVO.getAmtDiscount()).orElse(BigDecimal.ZERO));
            // 调整金额
            BigDecimal adjustAmt = item.getRealAmt()
                    .subtract(item.getPrice().multiply(item.getQty()))
                    .add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            // 调整金额
            item.setAdjustAmt(adjustAmt);
            if (productSku.getSkuType() == SkuType.NORMAL_PRODUCT) {
                item.setProType(Long.valueOf(productSku.getSkuType()));
            } else {
                item.setProType(Long.valueOf(SkuType.NO_SPLIT_COMBINE));
            }
        }
        return orderItemList;
    }

    private void makeCreateField(BaseModel model, User user) {
        Date date = new Date();
        // 所属公司
        model.setAdClientId((long) user.getClientId());
        // 所属组织
        model.setAdOrgId((long) user.getOrgId());
        // 创建人id
        model.setOwnerid(Long.valueOf(user.getId()));
        // 创建时间
        model.setCreationdate(date);
        // 创建人用户名
        model.setOwnername(user.getName());
        // 修改人id
        model.setModifierid(Long.valueOf(user.getId()));
        // 修改人用户名
        model.setModifiername(user.getName());
        // 修改时间
        model.setModifieddate(date);
        // 是否启用
        model.setIsactive("Y");
    }

    private void recountAmount(OcBOrder ocBOrder, List<OcBOrderItem> orderItemList) {
        BigDecimal productAmt = BigDecimal.ZERO;
        BigDecimal orderAmt;
        BigDecimal productDiscountAmt = BigDecimal.ZERO;
        BigDecimal orderDiscountAmt = BigDecimal.ZERO;
        BigDecimal qtyAll = BigDecimal.ZERO;
        BigDecimal adjustAmt = BigDecimal.ZERO;
        BigDecimal weight = BigDecimal.ZERO;
        Boolean flag = false;
        for (OcBOrderItem item : orderItemList) {
            if (item.getIsGift() == 1) {
                flag = true;
            }
            Long proType = Optional.ofNullable(item.getProType()).orElse(0L);
            if (proType.intValue() == SkuType.GIFT_PRODUCT || proType.intValue() == SkuType.COMBINE_PRODUCT) {
                continue;
            }
            // 打组合标
            if (proType.intValue() == SkuType.NO_SPLIT_COMBINE) {
                ocBOrder.setIsCombination(1);
            }
            productAmt = productAmt.add(Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO)
                    .multiply(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO)));
            productDiscountAmt =
                    productDiscountAmt.add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO));
            orderDiscountAmt =
                    orderDiscountAmt.add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            adjustAmt = adjustAmt.add(Optional.ofNullable(item.getAdjustAmt()).orElse(BigDecimal.ZERO));
            qtyAll = qtyAll.add(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO));
            weight = weight.add(Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO));
        }
        if (flag) {
            ocBOrder.setIsHasgift(1);
        }
        ocBOrder.setProductAmt(productAmt);
        ocBOrder.setProductDiscountAmt(productDiscountAmt);
        ocBOrder.setOrderDiscountAmt(orderDiscountAmt);
        ocBOrder.setAdjustAmt(adjustAmt);
        ocBOrder.setQtyAll(qtyAll);
        orderAmt = productAmt.subtract(productDiscountAmt)
                .subtract(orderDiscountAmt)
                .add(adjustAmt).add(ocBOrder.getShipAmt());
        ocBOrder.setOrderAmt(orderAmt);
        ocBOrder.setReceivedAmt(orderAmt);
        ocBOrder.setAmtReceive(orderAmt);
        ocBOrder.setServiceAmt(Optional.ofNullable(ocBOrder.getServiceAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setConsignAmt(Optional.ofNullable(ocBOrder.getConsignAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setConsignShipAmt(Optional.ofNullable(ocBOrder.getConsignShipAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setCodAmt(Optional.ofNullable(ocBOrder.getCodAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setJdReceiveAmt(Optional.ofNullable(ocBOrder.getJdReceiveAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setJdSettleAmt(Optional.ofNullable(ocBOrder.getJdSettleAmt()).orElse(BigDecimal.ZERO));
        ocBOrder.setLogisticsCost(Optional.ofNullable(ocBOrder.getLogisticsCost()).orElse(BigDecimal.ZERO));
        ocBOrder.setWeight(weight);
    }

    /**
     * 开启线程类
     */
    class CallableBuildOcBOrder implements Callable<List<OcBOrderBeefOrderExtend>> {

        Map<String, List<OcBOrderImpBeefOrderVO>> orderVoMap;
        Map<String, CpShop> cpShopMap;
        Map<String, StCBusinessType> businessTypeMap;
        Map<String, CpCPhyWarehouse> warehouseMap;
        Map<String, CpLogistics> logisticsMap;
        Map<String, ProductSku> proSkuMap;
        Map<String, CpCRegion> provinceMap;
        Map<String, CpCRegion> cityMap;
        Map<String, List<CpCRegion>> areaMap;
        User user;


        public CallableBuildOcBOrder(Map<String, List<OcBOrderImpBeefOrderVO>> orderVoMap, Map<String, CpShop> shopMap,
                                     Map<String, StCBusinessType> businessTypeMap, Map<String, CpCPhyWarehouse> warehouseMap,
                                     Map<String, CpLogistics> logisticsMap, Map<String, ProductSku> proSkuMap,
                                     Map<String, CpCRegion> provinceMap, Map<String, CpCRegion> cityMap,
                                     Map<String, List<CpCRegion>> areaMap, User user) {
            this.orderVoMap = orderVoMap;
            this.cpShopMap = shopMap;
            this.proSkuMap = proSkuMap;
            this.businessTypeMap = businessTypeMap;
            this.warehouseMap = warehouseMap;
            this.logisticsMap = logisticsMap;
            this.provinceMap = provinceMap;
            this.cityMap = cityMap;
            this.areaMap = areaMap;
            this.user = user;
        }

        @Override
        public List<OcBOrderBeefOrderExtend> call() {
            long start = System.currentTimeMillis();
            List<OcBOrderBeefOrderExtend> orderExtends = buildOrderAndItems(orderVoMap, cpShopMap, proSkuMap,
                    businessTypeMap, warehouseMap, logisticsMap, provinceMap, cityMap, areaMap, user);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("构建订单耗时:{}ms,一共:{}条,线程=", Thread.currentThread().getName()),
                        System.currentTimeMillis() - start, orderExtends.size());
            }
            return orderExtends;
        }

    }
}
