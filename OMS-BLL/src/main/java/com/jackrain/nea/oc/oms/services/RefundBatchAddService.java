package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cp.result.ReginQueryResult;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.observer.SequenceExec;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBRefundBatchMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRefundBatch;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 退货批次管理新增服务
 *
 * @author: 孙继东
 * @since: 2019-03-25
 * create at : 2019-03-25 15:55
 */
@Slf4j
@Component
public class RefundBatchAddService extends CommandAdapter {
    @Autowired
    private OcBRefundBatchMapper ocBRefundBatchMapper;

    @Autowired
    private CpRpcService cpRpcService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            if (fixColumn != null) {
                JSONObject refundBatch = fixColumn.getJSONObject("OC_B_REFUND_BATCH");
                if (id != null && id < 0) {
                    //新增
                    valueHolder = this.saveRefundBatch(refundBatch, valueHolder, querySession);
                }
                if (id != null && id > 0) {
                    //更新
                    valueHolder = this.updateRefundBatch(refundBatch, valueHolder, querySession, id);
                }
            }
        }
        return valueHolder;
    }

    /**
     * 校验数据
     */
    private void checkData(OcBRefundBatch batch) {
        if (batch.getBatchType() != null && batch.getBatchType() == 1) {
            //仓库   校验必填数据
            if (batch.getTransferStoreId() == null) {
                throw new NDSException("批次类型为仓库时,调拨入库仓库不能为空");
            }
            if (batch.getInStoreId() == null) {
                throw new NDSException("批次类型为仓库时,销退入库待检仓不能为空");
            }
            if (batch.getReturnPhyWarehouseId() == null) {
                throw new NDSException("批次类型为仓库时,销退入库次品仓不能为空");
            }
        }
        if (batch.getBatchType() != null && batch.getBatchType() == 2) {
            //门店 数据不必要
            if (batch.getTransferStoreId() != null) {
                batch.setTransferStoreId(null);
            }
            if (batch.getInStoreId() != null) {
                batch.setInStoreId(null);
            }
            if (batch.getReturnPhyWarehouseId() != null) {
                batch.setReturnPhyWarehouseId(null);
            }
        }


    }


    /**
     * 新增操作
     *
     * @param refundBatch  JSON对象
     * @param holder       存储返回信息
     * @param querySession 用户信息
     * @return 返回是否成功
     */
    public ValueHolder saveRefundBatch(JSONObject refundBatch, ValueHolder holder, QuerySession querySession) {
        OcBRefundBatch batch = JSON.parseObject(refundBatch.toJSONString(), new TypeReference<OcBRefundBatch>() {
        });
//        checkData(batch);
        Long transferStoreId = batch.getTransferStoreId();
        Long inStoreId = batch.getInStoreId();
        Long returnPhyWarehouseId = batch.getReturnPhyWarehouseId();
        try {
            CpStore transferStore = cpRpcService.selectCpCStoreById(transferStoreId);
            CpStore inStore_z = cpRpcService.selectCpCStoreById(inStoreId);
            CpStore cpStore_c = cpRpcService.selectCpCStoreById(returnPhyWarehouseId);
            //默认物流信息
            CpLogistics cpLogistics = cpRpcService.queryCpLogisticByEcode("SHZT");
            if (cpLogistics == null) {
                CpLogistics topOne = cpRpcService.getTopOne();
                if (topOne != null) {
                    batch.setCpCLogisticsId(topOne.getId());
                    batch.setCpCLogisticsEcode(topOne.getEcode());
                    batch.setCpCLogisticsEname(topOne.getEname());
                }
            } else {
                batch.setCpCLogisticsId(cpLogistics.getId());
                batch.setCpCLogisticsEcode(cpLogistics.getEcode());
                batch.setCpCLogisticsEname(cpLogistics.getEname());
            }
            Long batchId = ModelUtil.getSequence("OC_B_REFUND_BATCH");
            batch.setId(batchId);
            //根据入库仓库查询收货人相关信息
            if (transferStoreId != null) {
                List<Long> wareHouseIds = cpRpcService.queryWareHouseIds(Arrays.asList(transferStoreId));
                if (CollectionUtils.isNotEmpty(wareHouseIds)) {
                    batch.setTransferStoreEcode(transferStore.getEcode());
                    batch.setTransferStoreEname(transferStore.getEname());
                    batch.setInStoreEcode(inStore_z.getEcode());
                    batch.setInStoreEname(inStore_z.getEname());
                    batch.setReturnPhyWarehouseEcode(cpStore_c.getEcode());
                    batch.setReturnPhyWarehouseEname(cpStore_c.getEname());
                    CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(wareHouseIds.get(0));
                    if (cpCPhyWarehouse != null) {
                        batch.setReceiverName(cpCPhyWarehouse.getContactName());
                        batch.setReceiverMobile(cpCPhyWarehouse.getMobilephoneNum());
                        batch.setReceiverPhone(cpCPhyWarehouse.getPhoneNum());
                        batch.setReceiverAddress(cpCPhyWarehouse.getSendAddress());
                        Long provinceId = cpCPhyWarehouse.getSellerProvinceId();
                        Long cityId = cpCPhyWarehouse.getSellerCityId();
                        Long areaId = cpCPhyWarehouse.getSellerAreaId();
                        batch.setReceiverProvinceId(provinceId);
                        batch.setReceiverCityId(cityId);
                        batch.setReceiverDistrictId(areaId);
                        ValueHolder valueHolder = cpRpcService.getRegionNameByid(provinceId, cityId, areaId);
                        ReginQueryResult reginQueryResult = (ReginQueryResult) valueHolder.get("data");
                        if (reginQueryResult != null) {
                            batch.setReceiverProvinceEname(reginQueryResult.getProvName());
                            batch.setReceiverCityEname(reginQueryResult.getCityName());
                            batch.setReceiverDistrictEname(reginQueryResult.getRegionName());
                        }
                    }
                }

            }

            userInfo(batch, querySession);
            ocBRefundBatchMapper.insert(batch);
            SequenceExec exec = SequenceGenUtil.preGenerateSequence()
                    .add("REFUND_BATCH", JSON.parseObject(JSON.toJSONString(batch)), batchId, ocBRefundBatchMapper, "updateSequence");
            exec.exec();
            OcBRefundBatch ocBRefundBatch = ocBRefundBatchMapper.selectById(batchId);

            HashMap map = new HashMap();
            map.put("objid", batchId);
            map.put("tablename", "OC_B_REFUND_BATCH");
            holder.put("code", ResultCode.SUCCESS);
            holder.put("data", map);
            holder.put("message", "新增成功");
        } catch (Exception e) {
            log.error(LogUtil.format("调用cp查询实体仓库出错,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            holder.put("code", ResultCode.FAIL);
            holder.put("message", "新增失败");
        }
        return holder;
    }


    /**
     * 更新操作
     *
     * @param refundBatch  退货批次信息
     * @param holder       返回值
     * @param querySession 用户信息
     * @return 结果
     */
    public ValueHolder updateRefundBatch(JSONObject refundBatch, ValueHolder holder, QuerySession querySession, Long id) {
        OcBRefundBatch batch = JSON.parseObject(refundBatch.toJSONString(), new TypeReference<OcBRefundBatch>() {
        });
//        checkData(batch);
        batch.setId(id);
        if (batch.getLogisticNumber() == null && refundBatch.containsKey("LOGISTIC_NUMBER")) {
            batch.setLogisticNumber("");
        }
        if (batch.getReceiverPhone() == null && refundBatch.containsKey("RECEIVER_PHONE")) {
            batch.setReceiverPhone("");
        }
        if (batch.getRemark() == null && refundBatch.containsKey("REMARK")) {
            batch.setRemark("");
        }
        if (batch.getCpCLogisticsId() != null) {
            CpLogistics cpLogistics = cpRpcService.queryLogisticsById(batch.getCpCLogisticsId());
            batch.setCpCLogisticsEname(cpLogistics.getEname());
            batch.setCpCLogisticsEcode(cpLogistics.getEcode());
        }
        if (refundBatch.containsKey("RECEIVER_PROVINCE_ID") || refundBatch.containsKey("RECEIVER_CITY_ID") || refundBatch.containsKey("RECEIVER_DISTRICT_ID")) {
            ValueHolder vh = cpRpcService.getRegionNameByid(batch.getReceiverProvinceId(), batch.getReceiverCityId(), batch.getReceiverDistrictId());
            ReginQueryResult reginQueryResult = (ReginQueryResult) vh.get("data");
            batch.setReceiverProvinceEname(reginQueryResult.getProvName());
            batch.setReceiverCityEname(reginQueryResult.getCityName());
            batch.setReceiverDistrictEname(reginQueryResult.getRegionName());
        }
        batch.setModifierid(querySession.getUser().getId().longValue());
        batch.setModifierename(querySession.getUser().getEname());
        batch.setModifieddate(new Date());
        batch.setModifiername(querySession.getUser().getName());
        ocBRefundBatchMapper.updateById(batch);

        holder.put("code", ResultCode.SUCCESS);
        holder.put("message", "修改成功");
        return holder;
    }

    /**
     * 更新修改人信息
     *
     * @param batch        实体类
     * @param querySession 用户信息
     */
    private void userInfo(OcBRefundBatch batch, QuerySession querySession) {
        batch.setOwnerid(querySession.getUser().getId().longValue());
        batch.setOwnerename(querySession.getUser().getEname());
        batch.setCreationdate(new Date());
        batch.setOwnername(querySession.getUser().getName());
        batch.setModifierid(querySession.getUser().getId().longValue());
        batch.setModifierename(querySession.getUser().getEname());
        batch.setModifieddate(new Date());
        batch.setModifiername(querySession.getUser().getName());
        batch.setAdClientId(querySession.getUser().getClientId() + 0L);
        batch.setAdOrgId(querySession.getUser().getOrgId() + 0L);
    }
}

