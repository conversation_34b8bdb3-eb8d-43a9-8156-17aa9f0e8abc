package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.AcFStoreKpiMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.AcFStoreKpi;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;

/**
 * className: AcFStoreKpiCommonService
 * description:门店KPI设置新增/保存服务
 *
 * <AUTHOR>
 * create: 2021-06-19
 * @since JDK 1.8
 */
@Component
public class AcFStoreKpiSaveService extends CommandAdapter {

    @Autowired
    private AcFStoreKpiMapper kpiMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {

        ValueHolder valueHolder = CommandAdapterUtil.checkSaveSession(querySession, OcCommonConstant.AC_F_STORE_KPI);
        if (!valueHolder.isOK()) {
            return valueHolder;
        }

        User user = querySession.getUser();
        JSONObject fixColumn = (JSONObject)valueHolder.getData().get(OcCommonConstant.FIX_COLUMN);
        Long id = (Long)((HashMap)valueHolder.getData().get("data")).get(OcCommonConstant.OBJ_ID);
        AcFStoreKpi storeKpi = ((JSONObject)fixColumn.get(OcCommonConstant.AC_F_STORE_KPI)).toJavaObject(AcFStoreKpi.class);
        //校验门店编码+年月唯一性
        if(!checkUnique(id,storeKpi)){
            valueHolder = ValueHolderUtils.getFailValueHolder("该门店已存在该年月KPI记录，不允许保存");
            return valueHolder;
        }

        //框架字段赋值
        storeKpi.setId(id);
        CommandAdapterUtil.defaultOperator(storeKpi,user);
        if (id < 0) {
            //新增
            id = ModelUtil.getSequence(OcCommonConstant.AC_F_STORE_KPI);
            storeKpi.setId(id);
            storeKpi.setOwnerename(user.getEname());
            storeKpi.setModifierename(user.getEname());
            kpiMapper.insert(storeKpi);
        }else {
            //更新
            storeKpi.setModifierename(user.getEname());
            kpiMapper.updateById(storeKpi);
        }

        valueHolder = ValueHolderUtils.getSuccessValueHolder(id,OcCommonConstant.AC_F_STORE_KPI,"保存成功");
        return valueHolder;
    }

    private boolean checkUnique(Long id,AcFStoreKpi storeKpi){
        Long storeId = storeKpi.getCpCStoreId();
        String yearMonth = storeKpi.getMonthOfYear();

        //如果门店编码和年月为空，直接校验通过
        if(ObjectUtils.isEmpty(storeId) && ObjectUtils.isEmpty(yearMonth)){
            return true;
        }

        AcFStoreKpi oldKpi = kpiMapper.selectById(id);
        if(!ObjectUtils.isEmpty(storeId) && !ObjectUtils.isEmpty(oldKpi)){
            storeId = oldKpi.getCpCStoreId();
        }
        if(!ObjectUtils.isEmpty(yearMonth) && !ObjectUtils.isEmpty(oldKpi)){
            yearMonth = oldKpi.getMonthOfYear();
        }

        List<AcFStoreKpi> storeKpiList = kpiMapper.selectList(new QueryWrapper<AcFStoreKpi>().lambda()
                .eq(AcFStoreKpi::getCpCStoreId,storeId)
                .eq(AcFStoreKpi::getMonthOfYear,yearMonth)
                .ne(AcFStoreKpi::getId,id));

        return CollectionUtils.isEmpty(storeKpiList);
    }
}
