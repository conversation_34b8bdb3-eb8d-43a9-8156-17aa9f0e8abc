package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.request.OmsReleaseStockRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2023/3/23 14:49
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsReleaseStockOrderService {

    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;


    public ValueHolderV14 releaseStockOrderService(List<OmsReleaseStockRequest> requestList, User user) {
        ValueHolderV14 holder = new ValueHolderV14();
        try {
            for (OmsReleaseStockRequest omsReleaseStockRequest : requestList) {
                this.handleReleaseStock(omsReleaseStockRequest, user);
            }
            holder.setCode(0);
            holder.setMessage("库存异动库存释放成功!");
        } catch (Exception e) {
            holder.setCode(-1);
            holder.setMessage("库存异动库存释放失败!" + e.getMessage());
            log.error(LogUtil.format("库存异动库存释放失败:{}", "库存异动库存释放失败"), e);
        }
        return holder;
    }


    private void handleReleaseStock(OmsReleaseStockRequest omsReleaseStockRequest, User user) {
        log.info(LogUtil.format("库存异动库存释放入参:{}", "OmsReleaseStockOrderService.handleReleaseStock"), JSONObject.toJSONString(omsReleaseStockRequest));
        //查询订单  按支付时间排序
        List<Long> orderIds = omsReleaseStockRequest.getOrderIds();
        List<OcBOrder> orders = orderMapper.selectOrderListByIdsOrderByPayTime(orderIds);
        List<OcBOrder> ocBOrders = new ArrayList<>();
        for (OcBOrder order : orders) {
            String businessTypeCode = order.getBusinessTypeCode();
            if (OrderBusinessTypeCodeEnum.SAP_CONSIGN_SALE.getCode().equals(businessTypeCode)
                    || OrderBusinessTypeCodeEnum.SAP_STANDARD_SALE.getCode().equals(businessTypeCode)
                    || OrderBusinessTypeCodeEnum.SAP_RAW_MATERIAL_SALE.getCode().equals(businessTypeCode)) {
                continue;
            }
            ocBOrders.add(order);
        }
        if (CollectionUtils.isEmpty(ocBOrders)) {
            return;
        }
        orderIds = ocBOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());
        List<OcBOrderItem> orderItems = orderItemMapper.selectAllStatusOrderItemsByOrderIds(orderIds);
        String skuCode = omsReleaseStockRequest.getCpCSkuEcode();
        BigDecimal qty = omsReleaseStockRequest.getQty();
        OmsReleaseStockOrder stockOrder = new OmsReleaseStockOrder(orders, orderItems, skuCode, qty);
        List<OcBOrderParam> ocBOrderParams = stockOrder.handleOrder();
        log.info(LogUtil.format("库存异动库存订单:{}", "OmsReleaseStockOrderService.handleReleaseStock"), JSONObject.toJSONString(ocBOrderParams));
        if (CollectionUtils.isEmpty(ocBOrderParams)) {
            return;
        }
        //调用反审核
        for (OcBOrderParam ocBOrderParam : ocBOrderParams) {
            OcBOrder ocBOrder = ocBOrderParam.getOcBOrder();
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
                continue;
            }
            if (!this.toExamineOrderLock(ocBOrder, user)) {
                throw new NDSException("订单反审核失败");
            }
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(), "库存异动订单反审核", null, null, user);

        }
        //获取时间
        String releaseTime = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:release_stock_card_order_time");
        if (StringUtils.isEmpty(releaseTime)) {
            releaseTime = "30";
        }
        //释放库存
        for (OcBOrderParam ocBOrderParam : ocBOrderParams) {
            OcBOrder ocBOrder = ocBOrderParam.getOcBOrder();
            releaseStock(ocBOrderParam, releaseTime, user);
        }
    }

    public void releaseStock(OcBOrderParam ocBOrderParam, String releaseTime, User user) {
        OcBOrder ocBOrder = ocBOrderParam.getOcBOrder();
        Long id = ocBOrder.getId();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                List<OcBOrderItem> orderItemList = ocBOrderParam.getOrderItemList();
                ValueHolderV14 sgValueHolder = sgRpcService.voidSgStockOccupy(ocBOrder, orderItemList, user);
                if (!sgValueHolder.isOK()) {
                    throw new NDSException("订单释放库存失败");
                }
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(), "库存异动订单释放库存", null, null, user);
                OcBOrder bOrder = new OcBOrder();
                bOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
                bOrder.setId(ocBOrder.getId());
                bOrder.setIsDetention(1);
                bOrder.setDetentionReason("库存异动订单卡单");
                long time = System.currentTimeMillis() + (Long.parseLong(releaseTime) * 60 * 1000);
                Date date = new Date(time);
                bOrder.setDetentionReleaseDate(date);
                log.info(LogUtil.format("库存异动库存订单:{}", "OmsReleaseStockOrderService.releaseStock"), JSONObject.toJSONString(bOrder));
                orderMapper.updateById(bOrder);
                orderMapper.updateWarehouse(bOrder.getId());
                omsOccupyTaskService.addOcBOccupyTask(ocBOrder, new Date(time));
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "库存异动订单卡单,释放时间:" + sdf.format(date), null, null, user);

            } else {
                throw new NDSException("订单释放库存失败");
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 订单释放库存失败", e);
            throw new NDSException("订单释放库存失败" + e.getMessage());
        } finally {
            redisLock.unlock();
        }
    }


    public boolean toExamineOrderLock(OcBOrder ocBOrder, User user) {
        Long id = ocBOrder.getId();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(0, TimeUnit.MILLISECONDS)) {
                ValueHolderV14 holderV14 = new ValueHolderV14();
                boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
                if (isSuccess) {
                    //反审核成功  将订单状态改为 待审核
                    ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                }
                return isSuccess;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用反审核失败", e);
            return false;
        } finally {
            redisLock.unlock();
        }
    }
}