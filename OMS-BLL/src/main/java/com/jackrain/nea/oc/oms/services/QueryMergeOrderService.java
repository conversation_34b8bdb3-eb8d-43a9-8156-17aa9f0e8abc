package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.relation.MergeOrderModel;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-04-09 09:27
 */
@Slf4j
@Component
public class QueryMergeOrderService {
    private static final String tableName = "MERGE_ORDER";

    @Autowired
    private OrderMergeService orderMergeService;

    @Autowired
    private OrderLogSearchService orderLogSearchService;

    /**
     * 框架格式返回
     *
     * @param dataList
     * @return
     */
    private static JSONArray getFrameDataFormat(List<JSONObject> dataList) {
        JSONArray array = new JSONArray();
        if (dataList != null && dataList.size() > 0) {
            for (JSONObject emp : dataList) {
                Set<String> keySet = emp.keySet();
                JSONObject json = new JSONObject();
                for (String key : keySet) {
                    JSONObject val = new JSONObject();
                    val.put("val", emp.get(key));
                    json.put(key.toUpperCase(), val);
                }
                array.add(json);
            }
        }
        return array;
    }

    public ValueHolder queryMergeOrder(QuerySession querySession) {
        ValueHolder result = new ValueHolder();
        JSONObject resultData = new JSONObject();
        try {
            DefaultWebEvent event = querySession.getEvent();
            JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
            JSONObject fixedcolumns = param.getJSONObject("fixedcolumns");
            User user = querySession.getUser();

            //下单日期范围
            /**
             * "fixedcolumns":{"ORDER_DATE":"2019/04/03 00:00:00~2019/04/10 23:59:59","CP_C_SHOP_ID":["19"],
             * "CP_C_PHY_WAREHOUSE_ID":["16"],"USER_NICK":"11"},"multiple":[],"startindex":0,"range":10,
             * "orderby":[{"column":"MERGE_ORDER.ID","asc":true}]}
             */

            MergeOrderModel orderParam = checkParam(fixedcolumns, user);
            //开始条数
            String startindex = param.getString("startindex");
            //每页多少条
            if (StringUtils.isEmpty(startindex)) {
                orderParam.setStartindex(0);
            } else {
                orderParam.setStartindex(Integer.parseInt(startindex));
            }
            //Object range = param.get("range");
            //orderParam.setRang(200);
            //log.debug("range:" + fixedcolumns.get("USER_NICK"));
            List<MergeOrderModel> mergeOrderModelList = orderMergeService.queryMergeOrderList(orderParam);
            if (CollectionUtils.isEmpty(mergeOrderModelList)) {
                resultData.put("row", new JSONArray());
                resultData.put("totalRowCount", 0);
                resultData.put("disablePagination", true);
                result.put("data", resultData);
                result.put("code", 0);
                result.put("message", "success");
                return result;
            }
            JSONArray jsonArray = (JSONArray) JSONArray.toJSON(mergeOrderModelList);
            //JSONArray getFrameDataFormat = getFrameDataFormat(jsonArray);

            List<JSONObject> jsonObjectList = JSONObject.parseArray(
                    JSONObject.toJSONString(jsonArray, SerializerFeature.WriteMapNullValue), JSONObject.class);
            JSONArray getFrameDataFormat = getFrameDataFormat(jsonObjectList);
            resultData.put("start", 0);
            resultData.put("row", getFrameDataFormat);
            resultData.put("totalRowCount", mergeOrderModelList.size());
            resultData.put("disablePagination", true);
            result.put("data", resultData);
            result.put("code", 0);
            result.put("message", "success");
            return result;
        } catch (Exception e) {
            log.error(LogUtil.format("合单列表查询异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            resultData.put("row", new JSONArray());
            resultData.put("totalRowCount", 0);
            resultData.put("disablePagination", true);
            result.put("data", resultData);
            result.put("code", 0);
            result.put("message", "success");
            return result;
        }

    }

    /**
     * 参数校验+拼接
     *
     * @param fixedcolumns
     * @return
     */
    public MergeOrderModel checkParam(JSONObject fixedcolumns, User user) {
        MergeOrderModel mergeOrderModel = new MergeOrderModel();
        if (fixedcolumns.containsKey("ORDER_DATE")) {
            //下单日期
            String orderDate = fixedcolumns.getString("ORDER_DATE");
            String[] orderSplitDate = orderDate.split("~");
            mergeOrderModel.setBeginOrderDate(orderSplitDate[0]);
            mergeOrderModel.setEndOrderDate(orderSplitDate[1]);
        }
        if (fixedcolumns.containsKey("CP_C_SHOP_ID")) {
            //下单店铺
            JSONArray shopId = (JSONArray) fixedcolumns.get("CP_C_SHOP_ID");
            if (shopId.size() > 0) {
                mergeOrderModel.setShopId(Integer.parseInt(shopId.get(0).toString()));
            }
        } else {
            if (!user.isAdmin()) {
                JSONArray jsonArray = orderLogSearchService.getPrim(user, tableName);//查询店铺权限
                if (jsonArray.size() > 0) {
                    mergeOrderModel.setShopArray(jsonArray);
                } else {
                    mergeOrderModel.setShopId(-1);
                }
            }
        }
        if (fixedcolumns.containsKey("CP_C_PHY_WAREHOUSE_ID")) {
            //发货实体仓
            JSONArray warehouseId = (JSONArray) fixedcolumns.get("CP_C_PHY_WAREHOUSE_ID");
            if (warehouseId.size() > 0) {
                mergeOrderModel.setWarehouseId(Integer.parseInt(warehouseId.get(0).toString()));
            }
        }
        if (fixedcolumns.containsKey("USER_NICK")) {
            //用户昵称
            String userNickName = fixedcolumns.getString("USER_NICK");
            mergeOrderModel.setUserNickName(userNickName);
        }
        return mergeOrderModel;

    }

}
