package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatus;
import com.jackrain.nea.oc.oms.model.relation.ScanIncomingRelation;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-08-27
 * create at : 2019-08-27 10:33 AM
 * 扫描入库mq接收后处理类
 */
@Slf4j
@Component
public class ScanIncomingMqDealService {

    @Autowired
    OcBRefundInMapper refundInMapper;

    @Autowired
    OcBRefundInProductItemMapper itemMapper;

    @Autowired
    ScanIncomingLogicService service;


    public void execute(String messageBody) {
        JSONObject jsonObject = JSONObject.parseObject(messageBody);
        Long id = jsonObject.getLong("id");
        Long returnId = jsonObject.getLong("returnId");
        Integer isForce = jsonObject.getInteger("isForce");
        ScanIncomingRelation relation = new ScanIncomingRelation();
        OcBRefundIn ocBRefundIn = refundInMapper.selectById(id);
        QueryWrapper<OcBRefundInProductItem> wrapper = new QueryWrapper<>();
        wrapper.eq("oc_b_refund_in_id", id);
        List<OcBRefundInProductItem> items = itemMapper.selectList(wrapper);
        if (ocBRefundIn == null || items == null || items.size() == 0) {
            throw new NDSException("未查到主表或者子表信息，重新消费mq。入库单id=" + id + ",退单id=" + returnId);
        }
        relation.setReturnId(returnId);
        relation.setIsForce(isForce);
        relation.setItemList(items);
        relation.setOcBRefundIn(ocBRefundIn);
        try {
            service.toDoScanIncoming(relation);
        } catch (Exception ex) {
            OcBRefundIn refundIn = new OcBRefundIn();
            refundIn.setId(relation.getRefundInId());
            Long failCount = relation.getOcBRefundIn().getQtyFail();
            if (failCount == null) {
                failCount = 1L;
            } else {
                failCount += 1;
            }
            refundIn.setQtyFail(failCount);
            refundIn.setRemarkIn(ex.getMessage());
            //入库状态改为异常
            refundIn.setInStatus(ReturnStatus.REFUND_EXCEPTION.toInteger());
            refundInMapper.updateById(refundIn);
          /*  try {
                SpecialElasticSearchUtil.indexDocument(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, refundInMapper.selectById(id), id);
            } catch (IOException e) {
                e.printStackTrace();
            }*/
        }
    }
}
