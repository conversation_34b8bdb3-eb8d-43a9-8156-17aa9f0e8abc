package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.nums.OcOrderActionEnum;
import com.jackrain.nea.st.service.StCBusinessTypeService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/9/19
 * description : 订单前置校验
 */
@Slf4j
@Service
public class OcBOrderBeforeCheckService {

    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private StCBusinessTypeService stCBusinessTypeService;

    public ValueHolderV14 beforeCheck(List<Long> ids, String action, User user){
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "success");
        boolean success = true;
        StringBuilder errMsg = new StringBuilder();
        OcOrderActionEnum actionEnum = OcOrderActionEnum.getOrderActionEnumByCode(action);
        for (Long id : ids) {
            OcBOrder order = orderMapper.selectById(id);
            if(order == null){
                success = false;
                errMsg.append("订单ID[").append(id).append("]不存在！\n");
                continue;
            }
            try {
                ValueHolderV14 result = doBeforeCheck(order, actionEnum, user);
                if(!result.isOK()){
                    success = false;
                    errMsg.append(order.getBillNo()).append(result.getMessage()).append("\n");
                }
            }catch (Exception e){
                success = false;
                errMsg.append(order.getBillNo()).append(e.getMessage()).append("\n");
            }
        }
        if(!success){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(errMsg.toString());
        }
        return v14;
    }
    public ValueHolderV14 doBeforeCheck(OcBOrder order, OcOrderActionEnum actionEnum, User user){
        ValueHolderV14 v14 = null;
        switch (actionEnum){
            case NEW_RETURN:
            case NEW_RETURN_BATCH:
                v14 = checkNewReturn(order);
                break;
            case REFUND_PRICE:
                v14 = checkRefundPrice(order);
                break;
            case UN_KNOW:
                v14 = ValueHolderV14Utils.getSuccessValueHolder("success");
                break;
            default:
                break;
        }
        return v14;
    }
    public ValueHolderV14 checkRefundPrice(OcBOrder order){
        StCBusinessType stCBusinessType = stCBusinessTypeService.selectOneById(order.getBusinessTypeId());
        if (Objects.isNull(stCBusinessType)) {
            return ValueHolderV14Utils.getFailValueHolder("未查询到该业务类型信息");
        }
        if (!YesNoEnum.Y.getVal().equals(stCBusinessType.getIsAllowExtraRefund())) {
            return ValueHolderV14Utils.getFailValueHolder(order.getBusinessTypeName() + "类型的设置为不允许创建额外退款单!");
        }
        return ValueHolderV14Utils.getSuccessValueHolder("success");
    }
    public ValueHolderV14 checkNewReturn(OcBOrder order){
        //取消手工建退单的前置校验，后置到保存的逻辑里去
//        StCBusinessType stCBusinessType = stCBusinessTypeService.selectOneById(order.getBusinessTypeId());
//        if (Objects.isNull(stCBusinessType)) {
//            return ValueHolderV14Utils.getFailValueHolder("未查询到该业务类型信息");
//        }
//        if (!YesNoEnum.Y.getVal().equals(stCBusinessType.getIsAllowHandReturn())) {
//            return ValueHolderV14Utils.getFailValueHolder(order.getBusinessTypeName() + "类型的设置为不允许手工建退单!");
//        }
        return ValueHolderV14Utils.getSuccessValueHolder("success");
    }
}
