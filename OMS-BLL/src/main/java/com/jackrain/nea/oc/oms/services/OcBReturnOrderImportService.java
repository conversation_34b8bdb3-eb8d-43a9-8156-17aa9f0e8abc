package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 15:07
 **/
@Component
@Slf4j
public class OcBReturnOrderImportService {

    public static final String ORIG_ORDER_ID = "ORIG_ORDER_ID";//原始订单id

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    public ValueHolderV14 importList(List<ExtOcBReturnOrder> ocBReturnOrderList, User loginUser) {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "订单管理导入成功！");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBReturnOrderImportService.importList 订单管理导入service入参 loginUser:{}," +
                            "ocBReturnOrderList:{}"),
                    JSONObject.toJSONString(loginUser), JSONObject.toJSONString(ocBReturnOrderList));
        }
        //查出所有原始订单并校验--->>> 1、是否存在未找到原始订单 2、原订单状态是否=5，6，12   3、把原始订单平台单号赋值
        List<Long> orderIdList = ocBReturnOrderList.parallelStream().map(ExtOcBReturnOrder::getOrigOrderId).distinct().collect(Collectors.toList());
        QueryWrapper<OcBOrder> orderWrapper = new QueryWrapper<OcBOrder>();
        orderWrapper.in(ORIG_ORDER_ID, orderIdList);
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectList(orderWrapper);
        //主表物流公司信息、主表退货总金额不能为负

        //校验明细中是否存在组合商品、赋值商品信息

        //注：1、勾选了换货预留库存参数，调用生成换货订单服务

        //新增主表、新增明细、新增日志、推ES


        return holderV14;
    }
}
