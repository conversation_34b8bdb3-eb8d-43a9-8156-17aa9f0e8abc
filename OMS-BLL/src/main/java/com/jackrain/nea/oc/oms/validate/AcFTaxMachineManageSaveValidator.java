package com.jackrain.nea.oc.oms.validate;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFTaxMachineManageMapper;
import com.jackrain.nea.oc.oms.model.table.AcFTaxMachineManage;
import com.jackrain.nea.tableService.MainTableRecord;
import com.jackrain.nea.tableService.TableServiceContext;
import com.jackrain.nea.tableService.validate.BaseValidator;
import com.jackrain.nea.util.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName AcFTaxMachineManageSaveValidator
 * @Description 税盘分类管理保存校验
 * @Date 2022/9/23 上午11:12
 * @Created by wuhang
 */
@Slf4j
@Component
public class AcFTaxMachineManageSaveValidator extends BaseValidator {

    @Autowired
    private AcFTaxMachineManageMapper mapper;

    @Override
    public void validate(TableServiceContext context, MainTableRecord currentRow) {
        JSONObject commitData = currentRow.getCommitData();
        JSONObject orignalData = currentRow.getOrignalData();
        AssertUtils.notNull(orignalData, "当前记录已不存在!", context.getLocale());
        if(commitData.containsKey("USER_ID")){
            Long user_id = commitData.getLong("USER_ID");
            LambdaQueryWrapper<AcFTaxMachineManage> query = new LambdaQueryWrapper<>();
            query.eq(AcFTaxMachineManage::getUserId,user_id);
            Integer count = mapper.selectCount(query);
            if(count > 0){
                AssertUtils.logAndThrow("该用户已配置税盘,请勿重复添加！", context.getLocale());
            }
        }
    }
}
