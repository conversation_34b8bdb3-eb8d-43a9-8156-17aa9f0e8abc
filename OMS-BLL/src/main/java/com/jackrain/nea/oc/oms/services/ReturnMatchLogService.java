package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.mapper.OcBRefundInLogMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @author: 周琳胜
 * @since: 2019/9/4
 * create at : 2019/9/4 14:16
 */
@Component
@Slf4j
public class ReturnMatchLogService {

    @Autowired
    private OcBRefundInLogService ocBRefundInLogService;

    @Autowired
    private OcBRefundInLogMapper ocBRefundInLogMapper;

    /**
     * 入库匹配插入日志
     *
     * @param id
     * @param type
     * @param message
     * @param user
     */
    // @20200903 移除事务REQUIRES_NEW
    // , propagation = Propagation.REQUIRES_NEW
    @Transactional(rollbackFor = Exception.class)
    public void insertLogCheck(Long id, String type, String message, User user) {
        // 根据message查此条日志是否已经插入此条日志
        OcBRefundInLog ocBRefundInLog = ocBRefundInLogMapper.selectLogIsExist(message, id);
        if (ocBRefundInLog == null) {
            // 没查到就调用退货入库日志服务
            ocBRefundInLogService.addLog(id, type, message, user);
        } else {
            ocBRefundInLogMapper.updateRefundlog(new Date(), ocBRefundInLog.getId(), id);
        }
    }

}
