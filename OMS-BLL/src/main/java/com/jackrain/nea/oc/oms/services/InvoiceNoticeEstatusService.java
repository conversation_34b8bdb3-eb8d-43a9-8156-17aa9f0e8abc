package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeMapper;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNotice;
import com.jackrain.nea.oc.oms.nums.OcInvoiceLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.OcInvoiceStatusEnum;
import com.jackrain.nea.oc.oms.util.InvoiceNoticeDealUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: huang.zaizai
 * @description: 开票通知 状态变更
 * @since: 2019-07-23
 * create at : 2019-07-23 15:00
 */
@Slf4j
@Component
public class InvoiceNoticeEstatusService extends CommandAdapter {
    @Autowired
    private OcBInvoiceNoticeMapper mapper;

    @Autowired
    private InvoiceNoticeDealUtil invoiceNoticeDealUtil;

    public ValueHolder changeEstatus(QuerySession querySession,
                                     OcInvoiceStatusEnum beforeStatus,
                                     OcInvoiceStatusEnum afterStatus,
                                     OcInvoiceLogTypeEnum logType) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        // 标准ID
        String objid = param.getString("objid");
        String importFlg = param.getString("importFlag");
        // 批量
        if (null == objid) {
            JSONArray ids = param.getJSONArray("ids");
            // 更新状态
            JSONObject resultJson = update(querySession, ids, beforeStatus, afterStatus, logType, importFlg);
            JSONArray errorInfo = resultJson.getJSONArray("errorInfo");
            JSONArray billNos = resultJson.getJSONArray("billNos");
            // 成功记录数
            int successCount = billNos.size();
            // 失败记录数
            int failCount = ids.size() - successCount;
            if (errorInfo.size() > 0) {
                vh.put("data", errorInfo);
                vh.put("code", -1);
                vh.put("message", "成功" + successCount + "条,失败" + failCount + "条");
            } else {
                vh.put("code", 0);
                vh.put("message", "成功" + successCount + "条");
            }
        } else {
            JSONArray ids = new JSONArray();
            ids.add(objid);
            // 更新状态
            JSONObject resultJson = update(querySession, ids, beforeStatus, afterStatus, logType, importFlg);
            JSONArray errorInfo = resultJson.getJSONArray("errorInfo");
            if (errorInfo.size() > 0) {
                vh.put("code", -1);
                vh.put("message", errorInfo.getJSONObject(0).getString("message"));
            } else {
                String eCode = resultJson.getJSONArray("billNos").getString(0);
                vh.put("code", 0);
                vh.put("message", eCode + logType.getName() + "成功");

                Map<String, Object> data = new HashMap();
                data.put("objid", objid);
                data.put("tablename", "OC_B_INVOICE_NOTICE");
                vh.put("data", data);
            }
        }
        return vh;
    }

    //状态变更
    public JSONObject update(QuerySession querySession,
                             JSONArray ids,
                             OcInvoiceStatusEnum beforeStatus,
                             OcInvoiceStatusEnum afterStatus,
                             OcInvoiceLogTypeEnum logType,
                             String importFlg) {
        JSONObject resultJson = new JSONObject();
        JSONArray errorInfo = new JSONArray();
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        // 根据objid查询
        List<HashMap> infoMap = mapper.getMapByIds(ids, "OC_B_INVOICE_NOTICE");
        if (null == infoMap) {
            throw new NDSException("当前记录已不存在！");
        }
        JSONArray billNos = new JSONArray();
        JSONArray invoiceIds = new JSONArray();
        for (HashMap map : infoMap) {
            if (map.get("ESTATUS").toString().equals(beforeStatus.getVal().toString())) {
                billNos.add(map.get("BILL_NO"));
                invoiceIds.add(map.get("ID"));
            } else {
                JSONObject errorInfoObj = new JSONObject();
                errorInfoObj.put("objid", map.get("ID"));
                errorInfoObj.put("ecode", -1);
                errorInfoObj.put("message", String.format("[%s]只允许%s开票状态为%s的单据!",
                        map.containsKey("BILL_NO") ? map.get("BILL_NO") : "", logType.getName(), beforeStatus.getKey()));
                errorInfo.add(errorInfoObj);
            }
        }
        User user = querySession.getUser();
        if (invoiceIds.size() > 0) {
            //状态变更
            JSONObject setKeys = new JSONObject();
            setKeys.put("ESTATUS", afterStatus.getVal());
            setKeys.put("MODIFIERID", user.getId());
            setKeys.put("MODIFIERNAME", user.getName());
            setKeys.put("MODIFIERENAME", user.getEname());
            setKeys.put("MODIFIEDDATE", timestamp);
            if (OcInvoiceLogTypeEnum.AUDIT.equals(logType)) {
                setKeys.put("CHECK_ID", user.getId());
                setKeys.put("CHECK_NAME", user.getName());
                setKeys.put("CHECK_ENAME", user.getEname());
                setKeys.put("CHECKTIME", timestamp);
            } else if (OcInvoiceLogTypeEnum.CONFIRM_INVOICE.equals(logType)
                    && importFlg == null) {
                setKeys.put("INVOICE_ID", user.getId());
                setKeys.put("INVOICE_NAME", user.getName());
                setKeys.put("INVOICE_ENAME", user.getEname());
                setKeys.put("INVOICE_TIME", timestamp);
            }
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ID", invoiceIds);
            whereKeys.put("is_in", true);
            mapper.updateTable("OC_B_INVOICE_NOTICE", setKeys, whereKeys);
        }
        //新增日志
        for (Object id : invoiceIds) {
            invoiceNoticeDealUtil.addInvoiceNoticeLog((Long) id, logType.getKey(),
                    logType.getName() + "完成", user);
            //推送ES
            OcBInvoiceNotice esInvoiceNotice = mapper.selectById((Long) id);
            invoiceNoticeDealUtil.pushInvoiceNoticeToEs(esInvoiceNotice, null, null, null);
        }
        resultJson.put("billNos", billNos);
        resultJson.put("errorInfo", errorInfo);
        return resultJson;
    }
}
