package com.jackrain.nea.oc.oms.services;


import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 取消自动退款
 *
 * @author: 周琳胜
 * @since: 2019/3/25
 * create at : 2019/3/25 14:45
 */
@Slf4j
@Component
public class CancelAutoRefundService {
    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    OcBReturnOrderLogMapper ocBReturnOrderLogMapper;


    /**
     * 取消自动退款
     *
     * @param obj  入参
     * @param user 用户
     * @return 结果
     */
    public ValueHolderV14 cancelAutoRefund(JSONObject obj, User user) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (null == obj) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        // 获得退单ID
        Long id = obj.getLong("ID");
        // 获得AG状态
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(id);
        if (ocBReturnOrder == null) {
            throw new NDSException(Resources.getMessage("数据异常，未查到对应的退单信息!", user.getLocale()));
        }
        Integer isToag = ocBReturnOrder.getIsToag();
        if (AGStatusEnum.SUCCESS.getVal().equals(isToag)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("退单已经传AG成功，不允许取消自动退款！");
            return vh;
        } else if (AGStatusEnum.INIT.getVal().equals(isToag) || AGStatusEnum.FAIL.getVal().equals(isToag)) {
            isToag = AGStatusEnum.NOT.getVal();
            ocBReturnOrder.setIsToag(isToag);
            ocBReturnOrderMapper.updateById(ocBReturnOrder);

            //  添加退换货订单操作日志
            OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
            ocBReturnOrderLog.setAdClientId(user.getClientId() + 0L);
            ocBReturnOrderLog.setAdOrgId(user.getOrgId() + 0L);
            ocBReturnOrderLog.setLogType("取消自动退款");
            ocBReturnOrderLog.setLogMessage("取消退单自动退款成功");
            ocBReturnOrderLog.setLogParam("");
            ocBReturnOrderLog.setIpAddress("127.0.0.1");
            ocBReturnOrderLog.setUserName(user.getName());
            ocBReturnOrderLog.setOcBReturnOrderId(id);
            ocBReturnOrderLog.setId(ModelUtil.getSequence("oc_b_return_order_log"));
            ocBReturnOrderLogMapper.insert(ocBReturnOrderLog);
        } else if (AGStatusEnum.NOT.getVal().equals(isToag)) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("退单已经取消自动退款成功，不需要重复取消！");
            return vh;
        } else {
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage("退单取消自动退款失败，传AG状态异常！");
            return vh;
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("退单取消自动退款成功！");
        return vh;
    }
}

