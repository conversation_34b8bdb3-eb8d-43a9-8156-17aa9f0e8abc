package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.mapper.OcBOrderTaobaoMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderTaobao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 易邵峰
 * @since: 2019-01-22
 * create at : 2019-01-22 22:42
 */
@Component
public class OmsOrderTaobaoService {

    @Autowired
    private OcBOrderTaobaoMapper taobaoMapper;

    public OcBOrderTaobao selectOrderTaobao(String tid) {
        return taobaoMapper.selectByTid(tid);
    }

    public boolean updateOrderMarkDesc(long id, String markDesc) {
        int result = this.taobaoMapper.updateMarkDesc(id, markDesc);
        return result > 0;
    }

}
