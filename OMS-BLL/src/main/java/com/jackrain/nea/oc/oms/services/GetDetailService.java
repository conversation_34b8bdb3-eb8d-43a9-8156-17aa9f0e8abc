package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.GetDetailMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnTypeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnTypeMapper;
import com.jackrain.nea.oc.oms.model.enums.IsForbiddenDeliveryEnum;
import com.jackrain.nea.oc.oms.model.enums.LogisticsStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderTagEum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.result.GetOrderResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderTagResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnType;
import com.jackrain.nea.oc.oms.model.table.OcBReturnTypeItem;
import com.jackrain.nea.oc.oms.security.OrderPersonalInfoEncrypt;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Author: wangqiang
 * @Date: 2019-03-07 10:46
 * @Version 1.0
 */

/**
 * 订单详情-查询
 */


@Slf4j
@Component
public class GetDetailService {
    @Autowired
    private GetDetailMapper getDetailMapper;

    @Autowired
    private OcBReturnTypeMapper ocBReturnTypeMapper;

    @Autowired
    private OcBReturnTypeItemMapper ocBReturnTypeItemMapper;

    @Autowired
    private OrderPersonalInfoEncrypt orderPersonalInfoEncrypt;

    @Autowired
    private OmsOrderLogService orderLogService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private IpRpcService ipRpcService;

    public ValueHolder getDetail(JSONObject obj, User user) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ValueHolder vh = new ValueHolder();
        try {
            Long id = obj.getLong("ID");
            if (id == null) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", Resources.getMessage("订单id不能为空!", user.getLocale()));
                return vh;
            }
            //检查ID是否存在
            Integer countNum = getDetailMapper.queryCountNum(id);
            if (countNum == 0) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", Resources.getMessage("数据库不存在该订单Id", user.getLocale()));
                return vh;
            }
            //获取订单信息
            GetOrderResult orederInfo = getDetailMapper.queryOrederInfo(id);
            log.info(" GetDetailService.orederInfo {}",JSON.toJSONString(orederInfo));
            String platformProvince = orederInfo.getPlatformProvince();
            String platformCity = orederInfo.getPlatformCity();
            String platformArea = orederInfo.getPlatformArea();
            // @20200805 task#23951 新建退款单时从订单带收款人手机号码，前提：订单的reserve_varchar02字段目前没有使用
            orederInfo.setVipPhone(orederInfo.getReceiverMobile());

            StringBuffer sb = new StringBuffer();
            if (orederInfo != null) {
                //收货人
                sb.append(orederInfo.getReceiverName());
                sb.append("，");
                //如果手机为空
                if (StringUtils.isEmpty(orederInfo.getReceiverMobile())) {
                    sb.append(orederInfo.getReceiverPhone());
                }
                //如果电话为空
                if (StringUtils.isEmpty(orederInfo.getReceiverPhone())) {
                    sb.append(orederInfo.getReceiverMobile());
                }
                //如果都不为空
                if (StringUtils.isNotEmpty(orederInfo.getReceiverMobile())
                        &&
                        StringUtils.isNotEmpty(orederInfo.getReceiverPhone())) {
                    sb.append(orederInfo.getReceiverMobile());
                }
                sb.append("，");
                //省OmsOrderAppointSplitService
                sb.append(orederInfo.getCpCRegionProvinceEname());
                //市
                sb.append(orederInfo.getCpCRegionCityEname());
                //区
                sb.append(orederInfo.getCpCRegionAreaEname());
                // 乡镇/街道
                sb.append(orederInfo.getCpCRegionTownEname());
                //详细地址
                sb.append(orederInfo.getReceiverAddress());
                //组合地址
                orederInfo.setOrderAddress(sb.toString());

                // 菜鸟作业状态
                Map<String, String> caiNiaoMap = LogisticsStatusEnum.getStatusMap();
//                String ccs = caiNiaoMap.get(orederInfo.getCainiaoWhStatus());
//                orederInfo.setCainiaoWhStatus(ccs);
                Date orderDate = orederInfo.getOrderDate();
                if (orderDate != null) {
                    String orderTime = simpleDateFormat.format(orderDate);
                    orederInfo.setOrderTime(orderTime);
                }
                Date payTime = orederInfo.getPayTime();
                if (payTime != null) {
                    String payDate = simpleDateFormat.format(payTime);
                    orederInfo.setPayDate(payDate);
                }
                Date presaleDepositTime = orederInfo.getPresaleDepositTime();
                if (presaleDepositTime != null) {
                    String presaleDepositDate = simpleDateFormat.format(presaleDepositTime);
                    orederInfo.setPresaleDepositDate(presaleDepositDate);
                }
                //获取平台Id 判断 对应 的平台名称
                Integer platformId = orederInfo.getPlatform();
                //订单状态
                Integer orderStatus = orederInfo.getOrderStatus();
                if (orderStatus != null) {
                    String orderName = checkStatus(orderStatus);
                    orederInfo.setOrderStatusName(orderName);
                }

                //支付类型 线上支付是1，货到付款是2
                Integer payType = orederInfo.getPayType();
                if (payType != null) {
                    if (payType == 1) {
                        orederInfo.setPayName("在线支付");
                    } else {
                        orederInfo.setPayName("货到付款");
                    }
                }
                //单据类型
                Integer orderType = orederInfo.getOrderType();
                if (orderType != null){
                    orederInfo.setOrdertypeName(paresOrderType(orderType));
                }
                //平台
                if (platformId != null) {
                    // 调用组织中心服务：
                    List<Long> ids = new ArrayList<>();
                    ids.add(Long.valueOf(platformId));
                    List<CpCPlatform> list = cpRpcService.queryCpCPlatformByIds(ids);
                    if (CollectionUtils.isNotEmpty(list)) {
                        orederInfo.setPlatformName(list.get(0).getEname());
                    }
                }
                //原单店铺ID
                if(StringUtils.isNotBlank(orederInfo.getGwSourceCode())){
                    CpShop shopInfo = cpRpcService.selectShopById(Long.valueOf(orederInfo.getGwSourceCode()));
                    if(shopInfo != null){
                        orederInfo.setOrigOrderShopTitle(shopInfo.getCpCShopTitle());
                    }
                }
                //平台状态
                String platfromStatus = orederInfo.getPlatformStatus();
                if (platfromStatus != null) {
                    orederInfo.setPlatformStatusName(TStatusEnum.getValueByKey(platfromStatus));
                }
                List<QueryOrderTagResult> queryOrderTagResults = this.packageTags(orederInfo);
                orederInfo.setOrderTagList(queryOrderTagResults);

                String isShowSecurity = obj.getString("isShowPii");
                if (StringUtils.equalsIgnoreCase(isShowSecurity, "true")
                        // 抖音平台的地址不解密
                        && !PlatFormEnum.DOU_YIN.getCode().equals(orederInfo.getPlatform())) {
//                    orderPersonalInfoEncrypt.securityOrderPersonalInfo(orederInfo);
                    //解密，默认无需手动加密
                    ipRpcService.decrypt(orederInfo);
                    orederInfo.setOrderAddress(orederInfo.getReceiverName() + "，" +
                            orederInfo.getReceiverMobile() + "，" + orederInfo.getReceiverAddress());
//                    orderLogService.addUserOrderLog(orederInfo.getId(), orederInfo.getBillNo(),
//                            OrderLogTypeEnum.VIEW_PERSONAL_INFO.getKey(), "显示敏感数据成功", "",
//                            "", user);
                }

                //门店接单状态
              /*  Integer storeDeliveryStatus = orederInfo.getStoreDeliveryStatus();
                if (storeDeliveryStatus != null) {
                    // 门店接单状态 2020/09/03 黄志优
                    String storeDeliveryStatusName = OcOrderStoreDeliveryStatusEnum.enumToStringByValue(storeDeliveryStatus);
                    orederInfo.setStoreDeliveryStatusName(storeDeliveryStatusName);
                }*/
                orederInfo.setPlatformProvince(platformProvince);
                orederInfo.setPlatformCity(platformCity);
                orederInfo.setPlatformArea(platformArea);
                vh.put("code", ResultCode.SUCCESS);
                vh.put("message", Resources.getMessage("获取订单信息成功!", user.getLocale()));
                vh.put("data", orederInfo);
                log.info(" 订单详情返回出参 {}",JSON.toJSONString(vh));

                return vh;
            }
        } catch (NDSException e) {
            log.error(LogUtil.format("GetDetailService getDetail: {}"), Throwables.getStackTraceAsString(e));
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage("查询订单信息发生异常", user.getLocale()));
        }
        return vh;
    }
    /**
     * <AUTHOR>
     * @Date 17:35 2021/7/29
     * @Description 订单类型
     */
    private String paresOrderType(Integer orderType){
        switch (orderType){
            case 1 :
                return "正常";
            case 2 :
                return "换货";
            case 3 :
                return "补发";
            case 4 :
                return "赠品";
            case 5 :
                return "积分";
            case 6 :
                return "丢单";
            case 9 :
                return "预售";
            case 10 :
                return "虚拟定金";
            case 7 :
                return "刷单";
            case 8 :
                return "虚拟";
            case 600 :
                return "批发有装箱";
            case 601 :
                return "批发无装箱";
        }
        return "";
    }

    /**
     * * 1,待审核 2,缺货 3,已审核 4,配货中 5,仓库发货 6,平台发货 7,已取消 8,系统作废
     * 9,预售 10,代发 11,物流已送达 12,交易完成 13,未付款 21,待传wms 50,待分配
     *
     * @return
     */

    private String checkStatus(Integer status) {
        switch (status) {
            case 1:
                return "待审核";
            case 2:
                return "待寻源";
            case 3:
                return "已审核";
            case 4:
                return "配货中";
            case 5:
                return "仓库发货";
            case 6:
                return "平台发货";
            case 7:
                return "已取消";
            case 8:
                return "系统作废";
            case 9:
                return "预售";
            case 10:
                return "代发";
            case 11:
                return "物流已送达";
            case 12:
                return "交易完成";
            case 13:
                return "未付款";
            case 21:
                return "待传wms";
            case 50:
                return "待分配";


        }
        return "";
    }

    private List<QueryOrderTagResult> packageTags(GetOrderResult qor) {
        List<String> list = new ArrayList<>();
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsMerge())) {
            list.add("IS_MERGE");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsInterecept())) {
            list.add("IS_INTERECEPT");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsInreturning())) {
            list.add("IS_INRETURNING");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsHasgift())) {
            list.add("IS_HASGIFT");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsSplit())) {
            list.add("IS_SPLIT");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsInvoice())) {
            list.add("IS_INVOICE");
        }
        if (qor.getCpCLabelId() != null) {
            list.add("CP_C_LABEL_ID");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsJcorder())) {
            list.add("IS_JCORDER");
        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsOutUrgency())) {
            list.add("IS_OUT_URGENCY");
        }
       /* if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsLackstock()) &&
                OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(qor.getOrderStatus()) ) {
            list.add("IS_LACKSTOCK");
        }*/
//        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsShopCommission())) {
//            list.add("IS_SHOP_COMMISSION");
//        }
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsHasTicket())) {
            list.add("IS_HAS_TICKET");
        }
        /*if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsInvented())) {
            list.add("IS_INVENTED");
        }*/
        if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsCombination())) {
            list.add("IS_COMBINATION");
        }
        if (qor.getOrderType() != null && OcBOrderConst.ORDER_PAY_TYPE == qor.getOrderType()) {
            list.add("ORDER_TYPE");
        }
        if (qor.getDouble11PresaleStatus() != null
                && !(OcBOrderConst.ORDER_STATUS_ALL.equals(qor.getDouble11PresaleStatus()))) {
            list.add("DOUBLE11_PRESALE_STATUS");
        }
       /* if (!(OcBOrderConst.ORDER_STATUS_ALL.equals(qor.getSysPresaleStatus()))
                && qor.getSysPresaleStatus() != null) {
            list.add("SYS_PRESALE_STATUS");
        }*/
        if (qor.getPayType() != null && OcBOrderConst.ORDER_PAY_TYPE == qor.getPayType()) {
            list.add("PAY_TYPE");
        }
        //价
        if (StringUtils.isNotEmpty(qor.getPriceLabel()) && OcBOrderConst.IS_ACTIVE_YES.equals(qor.getPriceLabel())) {
            list.add("PRICE_LABEL");
        }
        if (qor.getLockStatus() != null && OcOrderLockStatusEnum.LOCKED.getKey() == qor.getLockStatus()) {
            list.add("LOCK_STATUS");
        }
       /* if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsWosUrge())) {
            //WOS催
            list.add("IS_WOS_URGE");
        }*/
        /*if (OcBOrderConst.IS_STATUS_IY.equals(qor.getIsWosCut())) {
            //WOS截
            list.add("IS_WOS_CUT");
        }*/
        if (IsForbiddenDeliveryEnum.FORBIDDEN.getCode().equals(qor.getIsForbiddenDelivery())) {
            //JitX禁发
            list.add("IS_FORBIDDEN_DELIVERY");
        }
        if (YesNoEnum.Y.getVal().equals(qor.getIsVipUpdateWarehouse())) {
            //JitX改仓
            list.add("IS_VIP_UPDATE_WAREHOUSE");
        }
        if (YesNoEnum.ONE.getKey().equals(qor.getReverseAuditType())) {
            //反审核中
            list.add("REVERSE_AUDIT_TYPE");
        }
        // key 值转化
        List<QueryOrderTagResult> tagList = OcOrderTagEum.toListQueryOrderTagResult(list);
        if (OcOrderTagEum.TAG_HAND.getVal().equals(qor.getOrderSource())) {
            QueryOrderTagResult q = OcOrderTagEum.getQueryOrderTagResult(OcBOrderConst.ORDER_TAG_HAND);
            tagList.add(q);
        }

        if (OcBOrderConst.ORDER_TAG_VALLAGE.equals(qor.getOrderSource())) {
            QueryOrderTagResult q = OcOrderTagEum.getQueryOrderTagResult(OcBOrderConst.ORDER_TAG_VALLAGE);
            tagList.add(q);
        }

        return tagList;
    }

    public ValueHolderV14<List<OcBOrderItem>> getOrderItem(Long mainId) {
        ValueHolderV14<List<OcBOrderItem>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS,"success");
        //校验入参
        if (mainId == null){
             v14.setCode(ResultCode.FAIL);
             v14.setMessage("入参不能为空");
             return v14;
        }

        List<OcBOrderItem> ocBOrderItems = getDetailMapper.selectOrderItems(mainId);

        if (CollectionUtils.isEmpty(ocBOrderItems)){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("原始订单编号对应零售发货单明细为空！");
            return v14;
        }
        v14.setData(ocBOrderItems);
        return v14;
    }

    public ValueHolderV14<Map<OcBReturnType, List<OcBReturnTypeItem>>> getTurnTypeList(String ename) {
        ValueHolderV14<Map<OcBReturnType, List<OcBReturnTypeItem>>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS,"success");
        //校验入参
        if (ename == null){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("入参不能为空");
            return v14;
        }

        OcBReturnType ocBReturnType = ocBReturnTypeMapper.selectOne(new QueryWrapper<OcBReturnType>()
                .lambda().eq(OcBReturnType::getEname, ename));

        if (ocBReturnType == null){
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("退款分类查询为空," + ename + "没有维护！");
            return v14;
        }
        List<OcBReturnTypeItem> ocBReturnTypeItems = ocBReturnTypeItemMapper.selectList(new QueryWrapper<OcBReturnTypeItem>().lambda()
                .eq(OcBReturnTypeItem::getOcBReturnTypeId, ocBReturnType.getId()));

        Map<OcBReturnType, List<OcBReturnTypeItem>> map = new HashMap<>();
        map.put(ocBReturnType,ocBReturnTypeItems);
        v14.setData(map);
        return v14;
    }

    /**
     * 根据零售发单ID批量查询
     * @param jsonObjects
     * @param user
     * @return
     */
    public ValueHolderV14<List<GetOrderResult>> getDetailList(List<JSONObject> jsonObjects, User user) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        ValueHolderV14<List<GetOrderResult>> vh = new ValueHolderV14();
        try {
            if (CollectionUtils.isEmpty(jsonObjects)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("订单id不能为空!", user.getLocale()));
                return vh;
            }
            List<Long> idList = new ArrayList<>();
            Map<Long,String> map = new HashMap<>();
            for (JSONObject json : jsonObjects){
                idList.add(json.getLong("ID"));
                map.put(json.getLong("ID"),json.getString("isShowPii"));
            }
            //获取订单信息
            List<GetOrderResult> getOrderResults = getDetailMapper.selectOrderListByIds(idList);

            //校验数据合理性
            if (CollectionUtils.isEmpty(getOrderResults)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("订单id查询数据为空!", user.getLocale()));
                return vh;
            }else if (getOrderResults.size() != idList.size()){
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("部分订单id查询数据为空!" + user.getLocale()));
                return vh;
            }
            List<GetOrderResult> orederInfoList = new ArrayList<>();

            for (GetOrderResult orederInfo : getOrderResults){
                // @20200805 task#23951 新建退款单时从订单带收款人手机号码，前提：订单的reserve_varchar02字段目前没有使用
                orederInfo.setVipPhone(orederInfo.getReceiverMobile());

                StringBuffer sb = new StringBuffer();
                if (orederInfo != null) {
                    //收货人
                    sb.append(orederInfo.getReceiverName());
                    sb.append("，");
                    //如果手机为空
                    if (StringUtils.isEmpty(orederInfo.getReceiverMobile())) {
                        sb.append(orederInfo.getReceiverPhone());
                    }
                    //如果电话为空
                    if (StringUtils.isEmpty(orederInfo.getReceiverPhone())) {
                        sb.append(orederInfo.getReceiverMobile());
                    }
                    //如果都不为空
                    if (StringUtils.isNotEmpty(orederInfo.getReceiverMobile())
                            &&
                            StringUtils.isNotEmpty(orederInfo.getReceiverPhone())) {
                        sb.append(orederInfo.getReceiverMobile());
                    }
                    sb.append("，");
                    //省OmsOrderAppointSplitService
                    sb.append(orederInfo.getCpCRegionProvinceEname());
                    //市
                    sb.append(orederInfo.getCpCRegionCityEname());
                    //区
                    sb.append(orederInfo.getCpCRegionAreaEname());
                    //详细地址
                    sb.append(orederInfo.getReceiverAddress());
                    //组合地址
                    orederInfo.setOrderAddress(sb.toString());

                    // 菜鸟作业状态
                    Map<String, String> caiNiaoMap = LogisticsStatusEnum.getStatusMap();
//                    String ccs = caiNiaoMap.get(orederInfo.getCainiaoWhStatus());
//                    orederInfo.setCainiaoWhStatus(ccs);
                    Date orderDate = orederInfo.getOrderDate();
                    if (orderDate != null) {
                        String orderTime = simpleDateFormat.format(orderDate);
                        orederInfo.setOrderTime(orderTime);
                    }
                    Date payTime = orederInfo.getPayTime();
                    if (payTime != null) {
                        String payDate = simpleDateFormat.format(payTime);
                        orederInfo.setPayDate(payDate);
                    }
                    Date presaleDepositTime = orederInfo.getPresaleDepositTime();
                    if (presaleDepositTime != null) {
                        String presaleDepositDate = simpleDateFormat.format(presaleDepositTime);
                        orederInfo.setPresaleDepositDate(presaleDepositDate);
                    }
                    //获取平台Id 判断 对应 的平台名称
                    Integer platformId = orederInfo.getPlatform();
                    //订单状态
                    Integer orderStatus = orederInfo.getOrderStatus();
                    if (orderStatus != null) {
                        String orderName = checkStatus(orderStatus);
                        orederInfo.setOrderStatusName(orderName);
                    }

                    //支付类型 线上支付是1，货到付款是2
                    Integer payType = orederInfo.getPayType();
                    if (payType != null) {
                        if (payType == 1) {
                            orederInfo.setPayName("在线支付");
                        } else {
                            orederInfo.setPayName("货到付款");
                        }
                    }
                    //单据类型
                    Integer orderType = orederInfo.getOrderType();
                    if (orderType != null){
                        orederInfo.setOrdertypeName(paresOrderType(orderType));
                    }
                    //平台
                    if (platformId != null) {
                        // 调用组织中心服务：
                        List<Long> ids = new ArrayList<>();
                        ids.add(Long.valueOf(platformId));
                        List<CpCPlatform> list = cpRpcService.queryCpCPlatformByIds(ids);
                        if (CollectionUtils.isNotEmpty(list)) {
                            orederInfo.setPlatformName(list.get(0).getEname());
                        }
                    }
                    //平台状态
                    String platfromStatus = orederInfo.getPlatformStatus();
                    if (platfromStatus != null) {
                        orederInfo.setPlatformStatusName(TStatusEnum.getValueByKey(platfromStatus));
                    }
                    List<QueryOrderTagResult> queryOrderTagResults = this.packageTags(orederInfo);
                    orederInfo.setOrderTagList(queryOrderTagResults);

                    String isShowSecurity = map.get(orederInfo.getId());
                    if (StringUtils.equalsIgnoreCase(isShowSecurity, "true")) {
//                    orderPersonalInfoEncrypt.securityOrderPersonalInfo(orederInfo);
                        //解密，默认无需手动加密
                        ipRpcService.decrypt(orederInfo);
                        orederInfo.setOrderAddress(orederInfo.getReceiverName() + "，" +
                                orederInfo.getReceiverMobile() + "，" + orederInfo.getReceiverAddress());
//                        orderLogService.addUserOrderLog(orederInfo.getId(), orederInfo.getBillNo(),
//                                OrderLogTypeEnum.VIEW_PERSONAL_INFO.getKey(), "显示敏感数据成功", "",
//                                "", user);
                    }

                    //门店接单状态
                   /* Integer storeDeliveryStatus = orederInfo.getStoreDeliveryStatus();
                    if (storeDeliveryStatus != null) {
                        // 门店接单状态 2020/09/03 黄志优
                        String storeDeliveryStatusName = OcOrderStoreDeliveryStatusEnum.enumToStringByValue(storeDeliveryStatus);
                        orederInfo.setStoreDeliveryStatusName(storeDeliveryStatusName);
                    }*/
                    orederInfoList.add(orederInfo);
                }
            }
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(Resources.getMessage("获取订单信息成功!", user.getLocale()));
            vh.setData(orederInfoList);
            return vh;
        } catch (NDSException e) {
            log.error(LogUtil.format("GetDetailService getDetailList: {}"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage( Resources.getMessage("查询订单信息发生异常", user.getLocale()));
        }
        return vh;
    }
}
