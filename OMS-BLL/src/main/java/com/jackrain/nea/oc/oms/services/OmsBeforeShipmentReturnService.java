package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.service.IpOrderCancelToAgService;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/2/28 3:32 下午
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsBeforeShipmentReturnService {

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private IpTaobaoRefundService ipTaobaoRefundService;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;
    @Autowired
    private MarkRefundService markRefundService;
    @Autowired
    protected OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private IpOrderCancelToAgService ipOrderCancelToAgService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OmsMarkCancelService omsMarkCancelService;
    @Autowired
    private OmsReturnUtil omsReturnUtil;

    /**
     * 买家申请退款,等待卖家同意
     *
     * @param omsOrderRelation 订单对象
     * @param operateUser      操作人
     * @return
     */
    public boolean taoBaoRefundStatusAgree(OmsOrderRelation omsOrderRelation, User operateUser) {
        try {
            List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
            //判断明细是否全部退款成功(一个事务要么全部成功)  当前的明细
            boolean flag = true;
            for (int i = 0; i < ocBOrderItems.size(); i++) {
                OcBOrderItem item = ocBOrderItems.get(i);
                if (OcOrderRefundStatusEnum.SUCCESS.getVal() != item.getRefundStatus()) {
                    flag = false;
                }
            }
            if (flag) {
                return true;
            }
            OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
            List<OcBOrderItem> ocBOrderItemAll = omsOrderRelation.getOcBOrderItemAll();
            // 未拆分的组合商品信息
            List<Long> updateItemIds = ocBOrderItemAll.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
            // 将订单明细的退款状态更新为 1
            ocBOrderItemMapper.updateOcBOrderItemById(ocBOrder.getId(), updateItemIds, OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal());
            omsReturnUtil.orderHandle(operateUser, ocBOrder);
            if (isJointAg(ocBOrder)) {
                return true;
            } else {
                //未对接ag
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("未对接Ag,店铺id=", ocBOrder.getCpCShopId()));
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format(" 买家申请退款,等待卖家同意处理异常,error:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        }
        return false;
    }

    public void refundGiftItem(OmsOrderRelation omsOrderRelation, User operateUser) {
        try {
            List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
            OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
            Integer status = ocBOrder.getOrderStatus();
            // 判断订单状态。如果平台发货、仓库发货、系统作废、已取消。则不执行
            if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(status) || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(status)
                    || OmsOrderStatus.CANCELLED.toInteger().equals(status) || OmsOrderStatus.SYS_VOID.toInteger().equals(status)) {
                return;
            }
            omsReturnUtil.orderHandle(operateUser, ocBOrder);
            // 此时状态已经变了 需要重新获取
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())
                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrder.getOrderStatus())) {
                List<Long> itemIds = ocBOrderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                if (omsReturnUtil.signRefundNew(ocBOrder.getId(), itemIds, operateUser, OrderHoldReasonEnum.REFUND_HOLD)) {
                    omsReturnUtil.handleRefundComplete(ocBOrder);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format(" 退款成功处理异常,error:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        }
    }


    /**
     * 退款成功
     *
     * @param omsOrderRelation 订单对象
     * @param operateUser      用户
     * @return
     */
    public void refundStatusIsSuccess(OmsOrderRelation omsOrderRelation, User operateUser) {
        try {
            List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItems();
            OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
            // 判断是否已退款完成  防止原单一时没有拉到原单，平台手动同意退款，明细有退款成功的，但是由于退单中间表还是未转化后续会继续转换；
            // 或者由于界面人工将已转换的标记为未转化。
            boolean flag = true;
            for (int i = 0; i < ocBOrderItems.size(); i++) {
                OcBOrderItem item = ocBOrderItems.get(i);
                if (OcOrderRefundStatusEnum.SUCCESS.getVal() == item.getRefundStatus()) {
                    ocBOrderItems.remove(item);
                    i--;
                } else {
                    flag = false;
                }
            }
            if (flag) {
                omsReturnUtil.handleRefundComplete(ocBOrder);
                return;
            }
            //校验订单状态,若需要反审核则需要对订单处理反审核
            omsReturnUtil.orderHandle(operateUser, ocBOrder);
            Integer orderStatus = ocBOrder.getOrderStatus();
            //未拆分的组合商品信息
            //若订单状态是：未确认、缺货、已审核状态时，直接调用【订单传AG取消发货服务】
            if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                    || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)) {
                List<OcBOrderItem> orderItems = ocBOrderItems.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE || p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
                List<Long> itemIds = orderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                if (omsReturnUtil.signRefundNew(ocBOrder.getId(), itemIds, operateUser, OrderHoldReasonEnum.REFUND_HOLD)) {
                    omsReturnUtil.handleRefundComplete(ocBOrder);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format(" 退款成功处理异常,error:{}"), Throwables.getStackTraceAsString(e));
            throw new NDSException(e.getMessage());
        }
    }


    /**
     * @param omsOrderRelation 订单对象
     * @param operateUser
     * @return
     */
    public void orderStatusIsClosed(OmsOrderRelation omsOrderRelation, User operateUser) {
        OcBOrder ocBOrder = omsOrderRelation.getOcBOrder();
        List<OcBOrderItem> ocBOrderItems = omsOrderRelation.getOcBOrderItemAll();
        //将订单明细的对象的退款状态改为未退款
        List<Long> updateItemIds = ocBOrderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
        ocBOrderItemMapper.updateOcBOrderItemById(ocBOrder.getId(), updateItemIds, OcOrderRefundStatusEnum.NOTREFUND.getVal());
        omsReturnUtil.handleRefundComplete(ocBOrder);
//            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
//                    "退款关闭,转换完成!", ipBTaobaoRefund);

        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.INTERCEPTION_CANCLE.getKey(), "订单退款关闭，取消拦截", null, null, operateUser);
    }


    /**
     * 查看店铺是否对接ag
     *
     * @param ocBOrder
     * @return
     */
    public boolean isJointAg(OcBOrder ocBOrder) {
        StCShopStrategyDO stCShopStrategy = omsStCShopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
        if (stCShopStrategy == null) {
            return false;
        }
        return StringUtils.isNotEmpty(stCShopStrategy.getIsAg())
                && "Y".equals(stCShopStrategy.getIsAg());
    }

    /**
     * 调用AG
     *
     * @param ipBTaobaoRefund
     * @param operateUser
     * @return
     */
    public boolean toAg(IpBTaobaoRefund ipBTaobaoRefund, User operateUser) {
        ValueHolderV14 holderV14 = ipOrderCancelToAgService.orderCancelToAgRetry(ipBTaobaoRefund, operateUser);
        if (Objects.isNull(holderV14)) {
            return false;
        }
        int code = Tools.getInt(holderV14.getCode(), -1);
        return code == 0;
    }

    /**
     * 等待卖家同意.赠品订单hold单
     *
     * @param orderRelation
     * @param user
     */
    public void waitSellerAgreeGiftOrderHold(OmsOrderRelation orderRelation, User user) {
        OcBOrder gitOrder = orderRelation.getOcBOrder();
        String billNo = gitOrder.getBillNo();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("赠品订单退款,等待卖家同意,发货前Hold单", billNo));
        }
        boolean statusFront = checkOrderStatusFront(gitOrder.getOrderStatus());
        if (!statusFront) {
            return;
        }
        OcBOrder order = new OcBOrder();
        //是否退款中
        order.setIsInreturning(1);
        order.setId(gitOrder.getId());
        omsOrderService.updateOrderInfo(order);
        //是否已经拦截 Hold单统一调用 HOLD单方法
        order.setIsInterecept(1);
        order.setBillNo(gitOrder.getBillNo());
        ValueHolder holdResult = ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Hold单结果:{}", billNo), JSON.toJSONString(holdResult));
        }
        AssertUtil.isTrue(holdResult.isOK(), String.format("单据[%s]Hold失败", billNo));
        boolean deAuditResult = omsReturnUtil.orderHandle(user, gitOrder);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("单据处理结果:{}", billNo), JSON.toJSONString(deAuditResult));
        }
    }

    /**
     * 发货前的状态
     *
     * @param orderStatus
     * @return
     */
    private boolean checkOrderStatusFront(Integer orderStatus) {
        return OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                || OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)
                || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)
                || OmsOrderStatus.OCCUPY_IN.toInteger().equals(orderStatus)
                || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus);
    }

}


