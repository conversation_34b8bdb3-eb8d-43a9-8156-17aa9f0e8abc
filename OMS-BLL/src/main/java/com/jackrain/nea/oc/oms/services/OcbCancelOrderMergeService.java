package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.IpBJitxOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitOrderUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OrderTagUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: 孙继东
 * @since: 2020-02-15
 * create at : 2020-02-15 9:25
 */
@Slf4j
@Component
public class OcbCancelOrderMergeService {
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private IpBJitxOrderMapper jitxOrderMapper;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OmsOrderCancellationService omsOrderCancellationService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private OmsAuditTaskService omsAuditTaskService;
    @Autowired
    private OrderAmountUtil orderAmountUtil;


    @Autowired
    private SplitOrderUtils splitOrderUtils;

    @Autowired
    private OmsWmsTaskService wmsTaskService;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;


    @Autowired
    SgRpcService sgRpcService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    /**
     * 取消订单合并
     *
     * @param user     用户信息
     * @param orderIds 参数
     * @return
     */
    public ValueHolderV14 cancelMergeOrder(User user, List<Long> orderIds) {
        ValueHolderV14 vh14 = new ValueHolderV14();
        List<JSONObject> errorList = new ArrayList<>();
        int failCount = 0;
        for (Long id : orderIds) {
            String lockRedisKey = BllRedisKeyResources.buildLockReturnInKey(id);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            JSONObject result = new JSONObject();
            long startTime = System.currentTimeMillis();
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("OcbCancelOrderMergeService.cancelMergeOrder锁单耗时={},订单ID=", id),
                                System.currentTimeMillis() - startTime);
                    }
                    OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
                    if (Optional.ofNullable(ocBOrder.getIsMerge()).orElse(0) != 1) {
                        result.put("code", ResultCode.FAIL);
                        result.put("id", id);
                        result.put("message", "订单编号" + id + ";所选单据非合并订单,不允许进行取消取消合并");
                        failCount++;
                        errorList.add(result);
                        continue;
                    }
                    if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
                        result.put("code", ResultCode.FAIL);
                        result.put("id", id);
                        result.put("message", "订单编号" + id + "非待审核状态,不允许进行取消取消合并");
                        failCount++;
                        errorList.add(result);
                        continue;
                    }

                    OcbCancelOrderMergeService bean =
                            ApplicationContextHandle.getBean(OcbCancelOrderMergeService.class);
                    try {
                        bean.splitOrder(user, id, ocBOrder);
                    } catch (Exception e) {
                        log.error("订单id={},取消合并订单失败，异常信息={}", id, e);
                        omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_SPLIT.getKey(),
                                "合包订单中有【已发货】或者【已揽收】的订单，取消合并订单失败", "", "", user);
                        result.put("code", ResultCode.FAIL);
                        result.put("id", id);
                        result.put("message", "订单编号" + id + "取消合并失败," + e.getMessage());
                        failCount++;
                        errorList.add(result);
                    }

                } else {
                    //锁单失败
                    result.put("code", -1);
                    result.put("id", id);
                    result.put("message", Resources.getMessage("订单编号" + id + ";当前订单其他人在操作，请稍后再试!"));
                    failCount++;
                    errorList.add(result);
                }
            } catch (Exception e) {
                result.put("code", -1);
                result.put("id", id);
                result.put("message", "订单编号" + id + ";取消合并异常,异常信息为：" + e.getMessage());
                failCount++;
                errorList.add(result);
                log.error(LogUtil.format("取消合并异常,异常信息为：{}"), Throwables.getStackTraceAsString(e));
            } finally {
                long unlockStartTime = System.currentTimeMillis();
                redisLock.unlock();
                log.debug(LogUtil.format("OcbCancelOrderMergeService.cancelMergeOrder释放锁单耗时={},订单ID=", id),
                        System.currentTimeMillis() - unlockStartTime);
            }
            log.debug(LogUtil.format("OcbCancelOrderMergeService.cancelMergeOrder取消合并耗时={},订单ID=", id),
                    System.currentTimeMillis() - startTime);
        }
        if (failCount > 0) {
            vh14.setCode(ResultCode.FAIL);
            vh14.setData(errorList);
            vh14.setMessage("操作失败或部分失败，失败" + failCount + "条，成功" + (orderIds.size() - failCount) + "条");
        } else {
            vh14.setCode(ResultCode.SUCCESS);
            vh14.setMessage("取消合单成功");
        }
        return vh14;
    }

    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    public boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            if (isSuccess) {
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                //订单志
                try {
                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(),
                            ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_MERGE.getKey(),
                            "取消合并订单反审核id为" + ocBOrder.getId(), "", "", user
                    );
                } catch (Exception e) {
                    log.error(LogUtil.format("取消合并订单订单新增日志异常{}"), Throwables.getStackTraceAsString(e));
                }
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(LogUtil.format("调用反审核失败{}"), Throwables.getStackTraceAsString(e));
            return false;
        }
    }

    /**
     * 合并取消逻辑
     *
     * @param user     用户信息
     * @param id       页面入参id
     * @param ocBOrder 合单的信息
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void splitOrder(User user, Long id, OcBOrder ocBOrder) {

        //作废 被取消合并的单据，记录操作日志；
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("取消合并订单调用作废订单服务入参====>>>>{}"), JSON.toJSONString(ocBOrder));
        }

        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemListAndReturn(id);
        OcBOrderRelation mergeRelation = new OcBOrderRelation();
        mergeRelation.setOrderInfo(ocBOrder);
        mergeRelation.setOrderItemList(ocBOrderItems);

        List<OcBOrderItem> items = ocBOrderItemMapper.selectUnSuccessRefundAndNoSplit(ocBOrder.getId());
        Map<String, List<OcBOrderItem>> tidItemMap =
                items.stream().filter(it -> StringUtils.isNotEmpty(it.getTid())).collect(Collectors.groupingBy(OcBOrderItem::getTid));
        List<OcBOrderItem> tidIsEmptyList =
                items.stream().filter(it -> StringUtils.isEmpty(it.getTid())).collect(Collectors.toList());
        //查询JITX订单，赋值物流单号
        Map<String,String> tidExpressCodeMap = new HashMap<>();
        Map<String,String> tidPlatformStatusMap = new HashMap<>();
        if(PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform()) && !ObjectUtils.isEmpty(tidItemMap)){
            List<IpBJitxOrder> jitxOrderList = jitxOrderMapper.selectList(new LambdaQueryWrapper<IpBJitxOrder>()
                    .eq(IpBJitxOrder::getIsactive, R3CommonResultConstants.VALUE_Y)
                    .in(IpBJitxOrder::getOrderSn,tidItemMap.keySet()));
            if(CollectionUtils.isNotEmpty(jitxOrderList)){
                for(IpBJitxOrder o : jitxOrderList){
                    tidExpressCodeMap.put(o.getOrderSn(),o.getTransportNo());
                    tidPlatformStatusMap.put(o.getOrderSn(),o.getOrderStatus());

                    //存在已发货、已揽收的子订单不允许拆包
                    AssertUtil.assertException(JitxOrderStatus.ORDER_ALREADY_SEND.equals(o.getOrderStatus()) ||
                            JitxOrderStatus.ORDER_ALREADY_COLLECTED.equals(o.getOrderStatus()),
                            "合包订单中有【已发货】或者【已揽收】的订单，请重置发货完成后取消合包");
                }
            }
        }
        if (CollectionUtils.isNotEmpty(tidIsEmptyList)) {
            tidItemMap.put(null, tidIsEmptyList);
        }


        List<OcBOrderRelation> splitRelationList = new ArrayList<>();
        for (String tid : tidItemMap.keySet()) {
            //如果是jitx中间表已取消不拆分出来
            if(JitxOrderStatus.ORDER_SEND_REFUND.equals(tidPlatformStatusMap.get(tid)) ||
                    JitxOrderStatus.ORDER_UNSEND_REFUND.equals(tidPlatformStatusMap.get(tid))){
                continue;
            }
            OcBOrderRelation newRelation = buildOrderRelation(ocBOrder, tidItemMap, tid, user, tidExpressCodeMap);
            orderAmountUtil.recountOrderAmount(newRelation);
            splitRelationList.add(newRelation);
        }
        List<OcBOrder> ocBOrderList =
                splitRelationList.stream().map(OcBOrderRelation::getOrderInfo).collect(Collectors.toList());
        List<List<OcBOrderItem>> ocBOrderItemList =
                splitRelationList.stream().map(OcBOrderRelation::getOrderItemList).collect(Collectors.toList());
        String step= "作废原单";
        try {
            //作废原单
            cancelMergeVoidOrder(mergeRelation.getOrderInfo(),user);
            omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.SYSTEM_ABOLITION.getKey(),
                    "取消合并,作废原合单", "", "", user);
            step = "释放库存";
            //释放库存
            log.info(LogUtil.format("取消合并库存释放接口,order：", mergeRelation.getOrderInfo().getId()));
            //封装数据
            SgOmsShareOutRequest request = buildSgOmsShareOutRequest(mergeRelation.getOrderInfo(),ocBOrderItems,user);
            log.info(LogUtil.format("取消合并，调用sg释放库存封装数据为：{}"), JSON.toJSONString(request));
            ValueHolderV14 sgValueHolder = sgRpcService.voidSgOmsShareOut(request,mergeRelation.getOrderInfo(),ocBOrderItems);
            log.info(LogUtil.format("取消合并，调用sg释放库存返回接口数据为：{}"), JSON.toJSONString(sgValueHolder));
            if (sgValueHolder.isOK()){
                //jitx订单全部取消会存在新拆分的单据为空，直接结束拆分
                if(CollectionUtils.isEmpty(ocBOrderList) || CollectionUtils.isEmpty(ocBOrderItemList)){
                    return;
                }
                step = "生成新订单重新寻源";
                // 保存订单及订单明细
                for (OcBOrder order : ocBOrderList) {
                    order.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
                    ocBOrderMapper.insert(order);
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.ORDER_ADD.getKey(),
                            "取消合并,恢复原单,重新寻源", "", "", user);
                    omsOccupyTaskService.addOcBOccupyTask(order, null);
                    // 当开关开启走仓库拆单 （插入拆单task表），不开启 则不走仓库拆单*（插入传订单审核）
                    //            if (splitOrderUtils.isOpenWareHouseSplitOrder(order)) {
                    //                // 插入仓库拆单任务表
                    //                wmsTaskService.saveOrUpdateOcBWarehouseSplitTask(order.getId(), user);
                    //            } else {
                    //                // 插入传wms表
                    //                omsAuditTaskService.createOcBAuditTask(order, OmsAuditTimeCalculateReason.CANCEL_MERGE);
                    //            }
                }
                for (List<OcBOrderItem> itemList : ocBOrderItemList) {
                    ocBOrderItemMapper.batchInsert(itemList);
                }
            }else {
                log.error(LogUtil.format("取消合并，释放库存异常,order=", ocBOrder.getId()));
                throw new NDSException("取消合并[" + step + "], 时异常: " + sgValueHolder.getMessage());
            }
        }catch (Exception e){
            log.error(LogUtil.format("发生异常{},取消合并时=", step), Throwables.getStackTraceAsString(e));
            throw new NDSException("取消合并[" + step + "], 时异常: " + e.getMessage());
        }
    }
    /**
     * <AUTHOR>
     * @Date 15:00 2021/7/30
     * @Description 加入寻源中间表
     */
    private void creatOcBToBeConfirmedTask(Long oredrId){
        OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
        toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
        toBeConfirmedTask.setOrderId(oredrId);
        toBeConfirmedTask.setCreationdate(new Date());
        toBeConfirmedTask.setStatus(0);
        toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
    }
    /**
     * <AUTHOR>
     * @Date 14:31 2021/7/30
     * @Description 封装sg、所用的数据
     */
    public SgOmsShareOutRequest buildSgOmsShareOutRequest(OcBOrder orderInfo, List<OcBOrderItem> ocBOrderItemList, User user) {
        SgOmsShareOutRequest request = new SgOmsShareOutRequest();
        request.setSourceBillId(orderInfo.getId());
        request.setSourceBillNo(orderInfo.getBillNo());
        request.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        request.setTid(orderInfo.getTid());
        request.setCancelType(SgConstantsIF.OMS_STORAGE_OCCUPY_CANCEL_TYPE_MAIN);
        request.setLoginUser(user);
        List<SgOmsShareOutItemRequest> itemRequestList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            SgOmsShareOutItemRequest sgOmsShareOutItemRequest = new SgOmsShareOutItemRequest();
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            sgOmsShareOutItemRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            sgOmsShareOutItemRequest.setQtyPreout(ocBOrderItem.getQty());
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            itemRequestList.add(sgOmsShareOutItemRequest);
        }
        request.setItemRequestList(itemRequestList);

        return request;
    }
    /**
     * <AUTHOR>
     * @Date 21:52 2021/7/20
     * @Description 批量作废主单MergeTypeEnum.MANUAL
     */
    private void cancelMergeVoidOrder(OcBOrder ocBOrder, User user) {
            OcBOrder ocBOrderTmp = new OcBOrder();
            ocBOrderTmp.setId(ocBOrder.getId());
            //订单状态
            ocBOrderTmp.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
            makeModiferField(ocBOrderTmp, user);
            //再次更新订单信息
            ocBOrderMapper.updateById(ocBOrderTmp);
    }

    private void makeModiferField(OcBOrder order, User user) {
        Date date = new Date();
        order.setModifierid(Long.valueOf(user.getId()));//修改人id
        order.setModifiername(user.getName());//修改人用户名
        order.setModifierename(user.getEname());
        order.setModifieddate(date);//修改时间
        order.setIsactive("Y");//是否启用
    }
    private OcBOrderRelation buildOrderRelation(OcBOrder ocBOrder, Map<String, List<OcBOrderItem>> tidItemMap,
                                                String tid, User user, Map<String,String> tidExpressCodeMap) {
        OcBOrderRelation newRelaion = new OcBOrderRelation();
        OcBOrder newOcBOrder = new OcBOrder();
        BeanUtils.copyProperties(ocBOrder, newOcBOrder);
        newOcBOrder.setId(sequenceUtil.buildOrderSequenceId());
        newOcBOrder.setBillNo(sequenceUtil.buildBillNo());
        newOcBOrder.setSourceCode(tid);
        newOcBOrder.setTid(tid);
        newOcBOrder.setMergeSourceCode(tid);
        newOcBOrder.setIsCancelMerge(1);
        newOcBOrder.setIsMerge(0);
        newOcBOrder.setSuffixInfo(ocBOrder.getId() + "-CMG");
        makeCreateField(newOcBOrder, user);
        newOcBOrder.setOwnerename(user.getEname());
        newOcBOrder.setModifierename(user.getEname());
        if(!ObjectUtils.isEmpty(tidExpressCodeMap) && !ObjectUtils.isEmpty(tidExpressCodeMap.get(tid))){
            newOcBOrder.setExpresscode(tidExpressCodeMap.get(tid));
        }

        List<OcBOrderItem> items = new ArrayList<>();
        for (OcBOrderItem item : tidItemMap.get(tid)) {
            item.setId(sequenceUtil.buildOrderItemSequenceId());
            item.setOcBOrderId(newOcBOrder.getId());
            items.add(item);
        }
        newRelaion.setOrderItemList(items);
        newRelaion.setOrderInfo(newOcBOrder);
        // 直播标  轻供标 组标  赠品标 换货标
        clearTags(newRelaion);
        OrderTagUtil.orderTags(newRelaion);
        return newRelaion;
    }

    /**
     * 清除标签
     *
     * @param newRelaion 合并后的订单
     */
    private void clearTags(OcBOrderRelation newRelaion) {
        // 手
        newRelaion.getOrderInfo().setOrderSource("");
        // 缺
//        newRelaion.getOrderInfo().setIsLackstock(OcBOrderConst.IS_STATUS_IN);
        // 实缺
        //newRelaion.getOrderInfo().setIsRealLackstock(OcBOrderConst.IS_STATUS_IN);
        // 到
        // order.setPayType();
        // 虚
        newRelaion.getOrderInfo().setIsInvented(0);
        newRelaion.getOrderInfo().setPriceLabel(OcBOrderConst.IS_ACTIVE_NO);
        // 锁
        newRelaion.getOrderInfo().setLockStatus(OcOrderLockStatusEnum.WAIT_LOCK.getKey());
        // 复
        newRelaion.getOrderInfo().setIsCopyOrder(OcBOrderConst.IS_STATUS_IN);
        // 改
        newRelaion.getOrderInfo().setIsModifiedOrder(OcBOrderConst.IS_STATUS_IN);
        // 额
        newRelaion.getOrderInfo().setIsExtra(OcBOrderConst.IS_STATUS_IN);
        // 截
//        newRelaion.getOrderInfo().setIsWosCut(OcBOrderConst.IS_STATUS_IN);
    }

    private void makeCreateField(BaseModel model, User user) {
        Date date = new Date();
        model.setAdClientId((long) user.getClientId());//所属公司
        model.setAdOrgId((long) user.getOrgId());//所属组织
        model.setOwnerid(Long.valueOf(user.getId()));//创建人id
        model.setCreationdate(date);//创建时间
        model.setOwnername(user.getName());//创建人用户名
        model.setModifierid(Long.valueOf(user.getId()));//修改人id
        model.setModifiername(user.getName());//修改人用户名
        model.setModifieddate(date);//修改时间
        model.setIsactive("Y");//是否启用
    }
}



