package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.store.api.in.SgBStoInNoticesCmd;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInNoticesBillVoidRequest;
import com.google.common.base.Preconditions;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.constant.OcOmsFrontCommonConstant;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnOrderConfirmStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @author: 郑立轩
 * @since: 2019/5/15
 * create at : 2019/5/15 16:40
 * @author: ming.fz 2019/8/22 重构
 */
@Slf4j
@Component
public class OrderReturnRecallFromWmsService {
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;
    @Autowired
    private OmsReturnOrderService omsReturnOrderService;
    @Autowired
    OcCancelChangingOrRefundService cancelChangingOrRefundService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @DubboReference(group = "sg", version = "1.0")
    private SgBStoInNoticesCmd stoInNoticesCmd;

    public ValueHolderV14 orderReturnRecallFromWms(JSONObject object, User user) {
        int success = 0;
        int fail = 0;
        ValueHolderV14 vh1 = new ValueHolderV14(ResultCode.FAIL, null);
        if (object == null || CollectionUtils.isEmpty(object.getJSONArray("ID"))) {
            vh1.setMessage("请至少选中一项！");
            return vh1;
        }
        JSONArray errJa = new JSONArray();
        JSONArray ids = object.getJSONArray("ID");
        for (Object returnOrderId : ids) {
            JSONObject errjo = new JSONObject();
            ValueHolderV14<String> holderV14 = doOrderCancel(returnOrderId, user);
            if (holderV14.isOK()) {
                success++;
            } else {
                fail++;
                errjo.put(OcOmsFrontCommonConstant.OBJID, returnOrderId);
                errjo.put("message", holderV14.getMessage());
                errJa.add(errjo);
            }
        }
        if (fail == 0) {
            vh1.setCode(ResultCode.SUCCESS);
            vh1.setMessage("本次从WMS撤回退单成功" + success + "条，失败" + fail + "条");
            vh1.setData(ids.getLong(0));
        } else {
            vh1.setCode(ResultCode.FAIL);
            vh1.setMessage("本次从WMS撤回退单成功" + (ids.size() - fail) + "条，失败" + fail + "条");
            vh1.setData(errJa);
        }
        return vh1;
    }

    private void setWmsCancelStatus(OcBReturnOrder bReturnOrder, int status) {
        if (bReturnOrder == null) {
            return;
        }
        OcBOrder order = new OcBOrder();
        order.setId(bReturnOrder.getOrigOrderId());
        order.setWmsCancelStatus(status);
        order.setModifieddate(new Date());
        ocBOrderMapper.updateById(order);
    }

    /**
     * 校验wms撤回前置条件
     *
     * @param ocBReturnOrder 退单对象
     * @param user
     */
    public void checkCancelReturnOrderFromWms(OcBReturnOrder ocBReturnOrder, User user) {
        Preconditions.checkNotNull(ocBReturnOrder, "当前退换货单ID不存在");

        Boolean isWaitIn = ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(ocBReturnOrder.getReturnStatus());
        Preconditions.checkArgument(isWaitIn, "单据状态非待退货入库不能撤回WMS");

        if (ProReturnStatusEnum.PORTION.getVal().equals(ocBReturnOrder.getProReturnStatus())
                || ProReturnStatusEnum.WHOLE.getVal().equals(ocBReturnOrder.getProReturnStatus())) {
            omsReturnOrderService.saveAddOrderReturnLog(ocBReturnOrder.getId()
                    , "退换货单已经入库，不能进行撤回", "WMS撤回失败"
                    , user);
            throw new NDSException("退换货单已经入库，不能进行撤回");
        }

        if (WmsWithdrawalState.NO.toInteger().equals(ocBReturnOrder.getIsTowms())
                && ReturnOrderConfirmStatusEnum.NOT_CONFIRM.getKey().equals(ocBReturnOrder.getConfirmStatus())
                && (ocBReturnOrder.getProReturnStatus() == null || ProReturnStatusEnum.WAIT.getVal().equals(ocBReturnOrder.getProReturnStatus()))) {
            omsReturnOrderService.saveAddOrderReturnLog(ocBReturnOrder.getId()
                    , "未传WMS，无需撤回", "WMS撤回失败"
                    , user);
            throw new NDSException("未传WMS，无需撤回");
        }

        // 20240126佳哥让传WMS状态=未传、已确认、退货状态=待入库OMS支持撤销
        /*if (WmsWithdrawalState.NO.toInteger().equals(ocBReturnOrder.getIsTowms())
                && ReturnOrderConfirmStatusEnum.CONFIRM.getKey().equals(ocBReturnOrder.getConfirmStatus())
                && (ocBReturnOrder.getProReturnStatus() == null || ProReturnStatusEnum.WAIT.getVal().equals(ocBReturnOrder.getProReturnStatus()))) {
            omsReturnOrderService.saveAddOrderReturnLog(ocBReturnOrder.getId()
                    , "退换货单传WMS中，不能进行撤回，请稍后重试", "WMS撤回失败"
                    , user);
            throw new NDSException("退换货单传WMS中，不能进行撤回，请稍后重试");
        }*/
    }

    /**
     * 从wms撤回
     *
     * @param returnOrderId 退单
     * @param user          用户
     */
    public ValueHolderV14<String> doOrderCancel(Object returnOrderId, User user) {
        Long id = Long.valueOf(returnOrderId.toString());
        String lockRedisKey = BllRedisKeyResources.buildLockReturnInKey(id);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                throw new NDSException("当前退单正在操作中");
            }
            OcBReturnOrder returnOrder = returnOrderMapper.selectById(id);
            // 校验前置条件
            this.checkCancelReturnOrderFromWms(returnOrder, user);
            // 从wms撤回
            return qiMenOrderCancel(returnOrder, user);
        } catch (Exception e) {
            log.error("从wms撤回失败：{}", Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            redisLock.unlock();
        }
    }


    /**
     * 从wms撤回
     *
     * @param returnOrder 退单
     * @param user        用户
     * @return ValueHolder
     */
    public ValueHolderV14 qiMenOrderCancel(OcBReturnOrder returnOrder, User user) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OrderReturnRecallFromWmsService qiMenOrderCancel:{}",
                    returnOrder.getId()), JSONObject.toJSONString(returnOrder));
        }

        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS,"SUCCESS");

        try {
            if (ReturnOrderConfirmStatusEnum.CONFIRM.getKey().equals(returnOrder.getConfirmStatus())
                    && (returnOrder.getProReturnStatus() == null || ProReturnStatusEnum.WAIT.getVal().equals(returnOrder.getProReturnStatus()))) {

                SgBStoInNoticesBillVoidRequest stoInNoticesBillVoidRequest = new SgBStoInNoticesBillVoidRequest();
                stoInNoticesBillVoidRequest.setSourceBillId(returnOrder.getId());
                stoInNoticesBillVoidRequest.setSourceBillNo(returnOrder.getBillNo());
                stoInNoticesBillVoidRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);

                ValueHolderV14 valueHolderV14 = stoInNoticesCmd.voidInNotices(stoInNoticesBillVoidRequest, user);

                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("OrderReturnRecallFromWmsService voidInNotices:{}",
                            returnOrder.getId()), JSONObject.toJSONString(valueHolderV14));
                }
                
                OcBReturnOrder updatereturnOrder = new OcBReturnOrder();
                updatereturnOrder.setId(returnOrder.getId());

                if (valueHolderV14.isOK()) {
                    updatereturnOrder.setWmsCancelStatus(OcBorderListEnums.WmsCanceStatusEnum.RECALL_SUCCESS.getVal());
                    updatereturnOrder.setIsTowms(WmsWithdrawalState.NO.toInteger());
                    updatereturnOrder.setConfirmStatus(ReturnOrderConfirmStatusEnum.NOT_CONFIRM.getKey());
                    updatereturnOrder.setStoInNoticesId(null);
                    updatereturnOrder.setStoInNoticesNo(null);
                    BaseModelUtil.makeBaseModifyField(updatereturnOrder, user);

                    returnOrderMapper.updateOcBReturnOrder(updatereturnOrder);
//                    omsReturnOrderService.updateOcBReturnOrder(updatereturnOrder);
                    omsReturnOrderService.saveAddOrderReturnLog(updatereturnOrder.getId()
                            , "WMS撤回成功", "WMS撤回成功"
                            , user);

                    setWmsCancelStatus(updatereturnOrder, OcBorderListEnums.WmsCanceStatusEnum.RECALL_SUCCESS.getVal());
                } else {
                    updatereturnOrder.setWmsCancelStatus(OcBorderListEnums.WmsCanceStatusEnum.RECALL_FAIL.getVal());
                    updatereturnOrder.setRemark(valueHolderV14.getMessage());
                    BaseModelUtil.makeBaseModifyField(updatereturnOrder, user);

                    omsReturnOrderService.updateOcBReturnOrder(updatereturnOrder);
                    omsReturnOrderService.saveAddOrderReturnLog(updatereturnOrder.getId()
                            , valueHolderV14.getMessage(), "WMS撤回失败"
                            , user);

                    setWmsCancelStatus(updatereturnOrder, OcBorderListEnums.WmsCanceStatusEnum.RECALL_FAIL.getVal());

                    vh.setMessage("WMS取消失败，请联系仓库后重试！");
                    vh.setCode(ResultCode.FAIL);
                    return vh;
                }

            }

        } catch (Exception e) {
            log.debug(LogUtil.format("从WMS撤回发生异常>>>", "从WMS撤回发生异常"), e);
            vh.setMessage("从WMS撤回发生异常>>>" + e.getMessage());
            vh.setCode(-1);
            return vh;
        }
        return vh;
    }

}
