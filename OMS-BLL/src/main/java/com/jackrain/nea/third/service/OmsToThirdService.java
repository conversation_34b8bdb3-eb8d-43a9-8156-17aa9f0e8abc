package com.jackrain.nea.third.service;

import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.third.enums.OmsBillTypeEnum;
//import com.jackrain.nea.third.mapper.OcBToAcTaskMapper;
import com.jackrain.nea.third.mapper.OcBToThirdTaskMapper;
import com.jackrain.nea.third.model.OcBToThirdTask;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/1/6 下午1:43
 * @Version 1.0
 */
@Component
public class OmsToThirdService {

    private static final String OC_B_TO_THIRD_TASK = "oc_b_to_third_task";

    @Autowired
    private OcBToThirdTaskMapper ocBToAcTaskMapper;

    /**
     * 新增单据到中间表
     * @param orderId 当前单据的id
     * @param typeEnum 当前单据的类型
     */
    public static void addOmsToThird(Long orderId, OmsBillTypeEnum typeEnum, String[] thirdName, User user){
        OmsToThirdService bean = ApplicationContextHandle.getBean(OmsToThirdService.class);
        bean.insertOmsToThird(orderId+"", typeEnum, thirdName, null,user);
    }


    /**
     *加入平台状态
     *
     * @param tid
     * @param typeEnum
     * @param thirdName
     * @param platformStatus
     * @param user
     */
    public static void addOmsToThirdPlatformStatus(String tid, OmsBillTypeEnum typeEnum, String thirdName,String platformStatus, User user){
        OmsToThirdService bean = ApplicationContextHandle.getBean(OmsToThirdService.class);
        OcBToThirdTaskMapper mapper = ApplicationContextHandle.getBean(OcBToThirdTaskMapper.class);
        List<OcBToThirdTask> ocBToThirdTasks = mapper.selectOcBToAcTaskByOrderId(tid);
        if (CollectionUtils.isEmpty(ocBToThirdTasks)) {
            bean.insertOmsToThird(tid, typeEnum, new String[]{thirdName}, platformStatus, user);
        }
    }



    public void insertOmsToThird(String orderId, OmsBillTypeEnum typeEnum, String[] thirdName, String platformStatus,User user){
        List<OcBToThirdTask> toAcTasks = new ArrayList<>();
        for (int i = 0; i < thirdName.length; i++) {
            OcBToThirdTask acTask = new OcBToThirdTask();
            acTask.setId(ModelUtil.getSequence(OC_B_TO_THIRD_TASK));
            acTask.setBillType(typeEnum.getCode());
            acTask.setOrderId(orderId);
            acTask.setPlatformStatus(platformStatus);
            acTask.setAdOrgId((long) user.getOrgId());
            acTask.setOwnername(user.getName());
            acTask.setAdClientId((long) user.getClientId());
            acTask.setOwnerid(Long.valueOf(user.getId()));
            acTask.setCreationdate(new Date());
            acTask.setModifierid(Long.valueOf(user.getId()));
            acTask.setModifieddate(new Date());
            acTask.setModifiername(user.getName());
            acTask.setStatus(0);
            acTask.setIsactive("Y");
            acTask.setThirdName(thirdName[i]);
            toAcTasks.add(acTask);
        }
        ocBToAcTaskMapper.batchInsert(toAcTasks);
    }
}
