package com.jackrain.nea.third.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * oc_b_to_ac_task
 * <AUTHOR>
@Data
@TableName
public class OcBToThirdTask implements Serializable {
    private Long id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 处理状态:0未处理，1已处理
     */
    private Integer status;

    /**
     * 0,已出库的零售发货单,1,已发货退款单(仅退款),2退货退款(已入库)
     */
    private Integer billType;


    private String thirdName;

    private String platformStatus;

    /**
     * 是否可用
     */
    private String isactive;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 所属组织
     */
    private Long adOrgId;

    /**
     * 所属公司
     */
    private Long adClientId;

    /**
     * 创建人id
     */
    private Long ownerid;

    /**
     * 创建人姓名
     */
    private String ownerename;

    /**
     * 创建人用户名
     */
    private String ownername;

    /**
     * 创建时间
     */
    private Date creationdate;

    /**
     * 修改人id
     */
    private Long modifierid;

    /**
     * 修改人姓名
     */
    private String modifierename;

    /**
     * 修改人用户名
     */
    private String modifiername;

    /**
     * 修改时间
     */
    private Date modifieddate;

    private static final long serialVersionUID = 1L;
}