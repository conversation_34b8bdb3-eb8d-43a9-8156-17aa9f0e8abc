package com.jackrain.nea.third.task;

import com.alibaba.fastjson.JSON;
import com.burgeon.mq.core.DefaultProducerSend;
import com.jackrain.nea.third.mapper.OcBToThirdTaskMapper;
import com.jackrain.nea.third.service.IOmsThird;
import com.jackrain.nea.third.util.BasePageSpiltUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ExecutorService;

/**
 * @Author: 黄世新
 * @Date: 2021/4/14 下午7:27
 * @Version 1.0
 */
@Slf4j
public abstract class AbstractOmsThirdTask<T> implements IOmsThird {

    private static final String TABLE_NAME = "oc_b_to_third_task";

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private OcBToThirdTaskMapper ocBToAcTaskMapper;

//    @Override
//    public OmsThirdResult start(JSONObject params) {
//        return this.handleOrderTask(params);
//    }


//    protected OmsThirdResult handleOrderTask(JSONObject params) {
//        OmsThirdResult result = new OmsThirdResult();
//        long start = System.currentTimeMillis();
//        ExecutorService executor = this.getExecutor();
//        List<Future<Integer>> results = new ArrayList<>();
//        Set<String> nodes = topMap.keySet();
//        for (String nodeName : nodes) {
//            String tableName = topMap.get(nodeName);
//            results.add(executor.submit(new OrderToMqCallable(nodeName, tableName)));
//        }
//        int executeCount = 0;
//        for (Future<Integer> futureResult : results) {
//            try {
//                executeCount += futureResult.get();
//            } catch (InterruptedException e) {
//                log.error(this.getClass().getName() + "Thread InterruptedException：{}", e);
//            } catch (Exception e) {
//                log.error(this.getClass().getName() + "Thread ExecutionException：{}", e);
//            }
//        }
//        long end = System.currentTimeMillis();
//        result.setSuccess(true);
//        result.setMessage("传" + getThirdName() + "执行完毕, 数量：" + executeCount + ", 用时: " + (end - start) + " ms");
//        return result;
//    }


    /**
     * 获取线程池
     *
     * @return
     */
    public abstract ExecutorService getExecutor();

    /**
     * 获取线程池
     *
     * @return
     */
    public abstract List<T> getHandleResult(Map<Integer, List<String>> billMap);

    /**
     * 获取topic
     *
     * @return
     */
    public abstract String getTopic();

    /**
     * 获取tag
     *
     * @return
     */
    public abstract String getTag();

    /**
     * 获取第三方应用名称
     *
     * @return
     */
    public abstract String getThirdName();

    /**
     * mq发送后的处理
     *
     * @param result
     */
    public abstract Map<Integer, List<String>> mqAfterHandle(List<T> result);

    /**
     * mq消息key
     *
     * @return
     */
    public abstract String megKey();

    /***
     * 默认发送mq 不传topic时 可自己适配
     * @param list
     */
    public abstract void adaptThird(List<T> list);

    /**
     * 分割list
     *
     * @param
     * @param
     */

    public void splitDataList(List<T> orderList) {
        //订单自动审核单次最大处理数 默认200单
        int pointsDataLimit = 200;
        //分批次处理
        List<List<T>> basePageList = BasePageSpiltUtil.getBasePageList(orderList, pointsDataLimit);
        String topic = getTopic();
        String tag = getTag();
        String megKey = megKey() + UUID.randomUUID();
        for (List<T> orderIds : basePageList) {
            if (StringUtils.isEmpty(topic) || StringUtils.isEmpty(tag)) {
                adaptThird(orderIds);
                continue;
            }
            if (CollectionUtils.isNotEmpty(orderIds)) {
                String jsonString = JSON.toJSONString(orderIds);
                Map<Integer, List<String>> integerListMap = this.mqAfterHandle(orderIds);
                try {
                    for (Integer billType : integerListMap.keySet()) {
                        List<String> ids = integerListMap.get(billType);
                        ocBToAcTaskMapper.updateOcBToAcTaskByStatus(ids, getThirdName(), 1, billType);
                    }
//                    r3MqSendHelper.sendMessage("default", jsonString, topic, tag, megKey);
                    defaultProducerSend.sendTopic(topic, tag, jsonString, megKey);
                    log.info(LogUtil.format("发送到ACmq,megKey :{}"), megKey);
                } catch (Exception e) {
                    for (Integer billType : integerListMap.keySet()) {
                        List<String> ids = integerListMap.get(billType);
                        ocBToAcTaskMapper.updateOcBToAcTaskByStatus(ids, getThirdName(), 0, billType);
                    }
                    log.error(LogUtil.format("发送mq异常:{},megKey :{}"), e, megKey);
                }
            } else {
                log.debug(LogUtil.format("没有数据此次批量发送Mq不执行~~"));
            }
        }
    }
}
