//package com.jackrain.nea.third.util;
//
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.util.ApplicationContextHandle;
//
//import java.sql.*;
//import java.util.Map;
//
///**
// * @Desc : drds cache
// * <AUTHOR> xiWen
// * @Date : 2020/3/21
// */
//public class OmsThirdCacheInit {
//
//        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//        String driverName = config.getProperty("spring.datasource.driverClassName", null);
//        String connString = config.getProperty("spring.datasource.url", null);
//        String userName = config.getProperty("spring.datasource.username", null);
//        String password = config.getProperty("spring.datasource.password", null);
//
//
//        final String groupName = "GROUP_NAME";
//        final String topTableName = "TABLE_NAME";
//
//        if (driverName == null || connString == null || userName == null || password == null) {
//            throw new NDSException("Mysql DataBase Info Not Found, Check The Apollo Config Or Net Work");
//        }
//
//        Connection conn = null;
//        PreparedStatement stmt = null;
//        ResultSet rs = null;
//        try {
//
//            Class.forName(driverName);
//            conn = DriverManager.getConnection(connString, userName, password);
//
//            stmt = conn.prepareStatement(sql);
//            rs = stmt.executeQuery();
//            while (rs.next()) {
//                String gpName = rs.getString(groupName);
//                String tpName = rs.getString(topTableName);
//            }
//        } catch (ClassNotFoundException e) {
//            e.printStackTrace();
//            throw new NDSException("Connection DataBase Exception");
//        } catch (SQLException e) {
//            e.printStackTrace();
//            throw new NDSException("JDBC SQL SQLException");
//        } finally {
//            closeAll(conn, stmt, rs);
//        }
//
//    }
//
//    /**
//     * close
//     *
//     * @param conn
//     * @param stmt
//     * @param rs
//     */
//    private void closeAll(Connection conn, PreparedStatement stmt, ResultSet rs) {
//        if (rs != null) {
//            try {
//                rs.close();
//            } catch (SQLException e) {
//                e.printStackTrace();
//            }
//        }
//        if (stmt != null) {
//            try {
//                stmt.close();
//            } catch (SQLException e) {
//                e.printStackTrace();
//            }
//        }
//        if (conn != null) {
//            try {
//                conn.close();
//            } catch (SQLException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//}
