package com.jackrain.nea.third.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.third.model.OcBToThirdTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Mapper
@Component
public interface OcBToThirdTaskMapper extends ExtentionMapper<OcBToThirdTask> {


    @SelectProvider(type = OcBToThirdTaskSql.class, method = "selectByNodeSql")
    List<OcBToThirdTask> selectOcBToAcTaskByStatus(@Param("tableName") String tableName,
                                                   @Param("limit") Integer limit,
                                                   @Param("thirdName") String thirdName);

    @Update("<script> "
            + "UPDATE oc_b_to_third_task SET status = #{status},modifieddate = now() where bill_type = #{billType} and third_name = #{thirdName} and order_id in"
            + "<foreach item='item' index='index' collection='orderIds' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int updateOcBToAcTaskByStatus(@Param("orderIds") List<String> orderIds,
                                  @Param("thirdName") String thirdName, @Param("status") Integer status, @Param("billType") Integer billType);

    @Select("SELECT * FROM oc_b_to_third_task WHERE order_id = #{tid}")
    List<OcBToThirdTask> selectOcBToAcTaskByOrderId(@Param("tid") String tid);

    @Select("<script> "
            + "SELECT * FROM oc_b_to_third_task WHERE order_id in"
            + "<foreach item='item' index='index' collection='tids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    List<OcBToThirdTask> selectOcBToAcTaskByOrderIds(@Param("tids") List<String> tids);

    class OcBToThirdTaskSql {
        public String selectByNodeSql(Map<String, Object> para) {
            StringBuilder sql = new StringBuilder();
            StringBuilder limitStr = new StringBuilder(" LIMIT ");
            int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
            limitStr.append(limit);
            String taskTableName = (String) para.get("tableName");
            String thirdName = (String) para.get("thirdName");
            sql.append("select order_id,bill_type from ")
                    .append(taskTableName)
                    .append(" where status = 0 and third_name = '")
                    .append(thirdName).append("'")
                    .append(" ORDER BY modifieddate asc ")
                    .append(limitStr);
            return sql.toString();
        }
    }

}