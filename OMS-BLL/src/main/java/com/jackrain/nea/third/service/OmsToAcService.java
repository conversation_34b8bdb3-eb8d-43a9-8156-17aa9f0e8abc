//package com.jackrain.nea.third.service;
//
//import com.alibaba.nacos.api.config.annotation.NacosValue;
//import com.jackrain.nea.ac.model.request.AcFSourceBillRequest;
//import com.jackrain.nea.ac.model.request.AcFSourcePlatformStatusReq;
//import com.jackrain.nea.ac.model.table.AcFSourceBill;
//import com.jackrain.nea.ac.model.table.AcFSourceBillItem;
//import com.jackrain.nea.oc.oms.mapper.*;
//import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
//import com.jackrain.nea.oc.oms.model.table.*;
//import com.jackrain.nea.third.enums.OmsBillTypeEnum;
//import com.jackrain.nea.third.mapper.OcBToThirdTaskMapper;
//import com.jackrain.nea.third.model.OcBToThirdTask;
//import com.jackrain.nea.third.task.AbstractOmsThirdTask;
//import com.jackrain.nea.third.util.BasePageSpiltUtil;
//import com.jackrain.nea.util.OMSThreadPoolFactory;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.math.NumberUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.ExecutorService;
//import java.util.stream.Collectors;
//
///**
// * @Author: 黄世新
// * @Date: 2022/1/6 下午6:49
// * @Version 1.0
// */
//
//@Component
//
//public class OmsToAcService extends AbstractOmsThirdTask<AcFSourceBillRequest> {
//
//    @Autowired
//    private OcBOrderItemMapper ocBOrderItemMapper;
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//    @Autowired
//    private OcBReturnOrderMapper ocBReturnOrderMapper;
//    @Autowired
//    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
//    @Autowired
//    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
//    @Autowired
//    private OcBReturnAfSendItemMapper ocBReturnAfSendItemMapper;
//    @Autowired
//    private OcBToThirdTaskMapper ocBToThirdTaskMapper;
//
//    @NacosValue(value = "${oms_to_ac_topic:BJ_TEST_OMS_TO_AC}", autoRefreshed = true)
//    private String toAcTopic;
//    @NacosValue(value = "${oms_to_ac_tag:omsToAc}", autoRefreshed = true)
//    private String toAcTag;
//
//
//    @Override
//    public ExecutorService getExecutor() {
//        return OMSThreadPoolFactory.getSyncAcPool();
//    }
//
//    @Override
//    public String getTopic() {
//        return toAcTopic;
//    }
//
//    @Override
//    public String getTag() {
//        return toAcTag;
//    }
//
//    @Override
//    public String getThirdName() {
//        return "AC";
//    }
//
//    @Override
//    public Map<Integer, List<String>> mqAfterHandle(List<AcFSourceBillRequest> result) {
//        Map<Integer, List<String>> orderIdsMap = new HashMap<>(16);
//        if (CollectionUtils.isNotEmpty(result)) {
//            for (AcFSourceBillRequest request : result) {
//                AcFSourcePlatformStatusReq sourcePlatformStatus = request.getSourcePlatformStatus();
//                if (sourcePlatformStatus != null) {
//                    if (!orderIdsMap.containsKey(OmsBillTypeEnum.UPDATE_PLATFORM_STATUS.getCode())) {
//                        List<String> ids = new ArrayList<>();
//                        ids.add(sourcePlatformStatus.getTid());
//                        orderIdsMap.put(OmsBillTypeEnum.UPDATE_PLATFORM_STATUS.getCode(), ids);
//                    } else {
//                        List<String> stringList = orderIdsMap.get(OmsBillTypeEnum.UPDATE_PLATFORM_STATUS.getCode());
//                        stringList.add(sourcePlatformStatus.getTid());
//                        orderIdsMap.put(OmsBillTypeEnum.UPDATE_PLATFORM_STATUS.getCode(), stringList);
//                    }
//                }
//                AcFSourceBill acFSourceBill = request.getAcFSourceBill();
//                if (acFSourceBill != null) {
//                    Integer billType = acFSourceBill.getBillType();
//                    if (!orderIdsMap.containsKey(billType)) {
//                        List<String> ids = new ArrayList<>();
//                        ids.add(acFSourceBill.getSourceId() + "");
//                        orderIdsMap.put(billType, ids);
//                    } else {
//                        List<String> ids = orderIdsMap.get(billType);
//                        ids.add(acFSourceBill.getSourceId() + "");
//                        orderIdsMap.put(billType, ids);
//                    }
//                }
//            }
//        }
//        return orderIdsMap;
//    }
//
//
//    @Override
//    public String megKey() {
//        return "AC_";
//    }
//
//    @Override
//    public void adaptThird(List<AcFSourceBillRequest> list) {
//
//    }
//
//    /**
//     * 封装参数
//     *
//     * @param billMap
//     * @return
//     */
//    @Override
//    public List<AcFSourceBillRequest> getHandleResult(Map<Integer, List<String>> billMap) {
//        List<AcFSourceBillRequest> requests = new ArrayList<>();
//        List<String> orderIds = billMap.get(OmsBillTypeEnum.RETAIL_INVOICE_ORDER.getCode());
//        if (CollectionUtils.isNotEmpty(orderIds)) {
//            List<List<String>> basePageList = BasePageSpiltUtil.getBasePageList(orderIds, 1000);
//            for (List<String> ids : basePageList) {
//                List<Long> idsLong = ids.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
//                List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsList(idsLong);
//                if (CollectionUtils.isEmpty(ocBOrderList)) {
//                    continue;
//                }
//                List<OcBOrderItem> items = ocBOrderItemMapper.selectAllStatusOrderItemsByOrderIds(idsLong);
//                List<AcFSourceBillRequest> billRequests = buildOrderSourceBill(ocBOrderList, items);
//                if (CollectionUtils.isNotEmpty(billRequests)) {
//                    requests.addAll(billRequests);
//                }
//            }
//
//        }
//        List<String> refundIds = billMap.get(OmsBillTypeEnum.RETAIL_REFUND_ORDER.getCode());
//        if (CollectionUtils.isNotEmpty(refundIds)) {
//            List<List<String>> basePageList = BasePageSpiltUtil.getBasePageList(refundIds, 1000);
//            for (List<String> longs : basePageList) {
//                List<Long> idsLong = longs.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
//                List<OcBReturnAfSend> ocBReturnAfSends = ocBReturnAfSendMapper.selectOcBReturnAfSendListById(idsLong);
//                if (CollectionUtils.isEmpty(ocBReturnAfSends)) {
//                    continue;
//                }
//                List<OcBReturnAfSendItem> ocBReturnAfSendItems = ocBReturnAfSendItemMapper.selectByOcBReturnAfSendIdList(idsLong);
//                List<AcFSourceBillRequest> billRequests = buildRefundSourceBill(ocBReturnAfSends, ocBReturnAfSendItems);
//                if (CollectionUtils.isNotEmpty(billRequests)) {
//                    requests.addAll(billRequests);
//                }
//            }
//        }
//
//        List<String> returnIds = billMap.get(OmsBillTypeEnum.RETAIL_RETURN_ORDER.getCode());
//        if (CollectionUtils.isNotEmpty(returnIds)) {
//            List<List<String>> basePageList = BasePageSpiltUtil.getBasePageList(returnIds, 1000);
//            for (List<String> longs : basePageList) {
//                List<Long> idsLong = longs.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
//                List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(idsLong);
//                if (CollectionUtils.isEmpty(ocBReturnOrders)) {
//                    continue;
//                }
//                List<OcBReturnOrderRefund> orderRefundList = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(idsLong);
//                List<AcFSourceBillRequest> billRequests = buildReturnSourceBill(ocBReturnOrders, orderRefundList);
//                if (CollectionUtils.isNotEmpty(billRequests)) {
//                    requests.addAll(billRequests);
//                }
//            }
//        }
//
//        List<String> tids = billMap.get(OmsBillTypeEnum.UPDATE_PLATFORM_STATUS.getCode());
//        if (CollectionUtils.isNotEmpty(tids)) {
//            List<OcBToThirdTask> ocBToThirdTasks = ocBToThirdTaskMapper.selectOcBToAcTaskByOrderIds(tids);
//            if (CollectionUtils.isNotEmpty(ocBToThirdTasks)) {
//                for (OcBToThirdTask ocBToThirdTask : ocBToThirdTasks) {
//                    AcFSourceBillRequest bill = new AcFSourceBillRequest();
//                    AcFSourcePlatformStatusReq sourcePlatformStatus = new AcFSourcePlatformStatusReq();
//                    sourcePlatformStatus.setTid(ocBToThirdTask.getOrderId());
//                    sourcePlatformStatus.setPlatformStatus(ocBToThirdTask.getPlatformStatus());
//                    bill.setSourcePlatformStatus(sourcePlatformStatus);
//                    requests.add(bill);
//                }
//            }
//        }
//        return requests;
//    }
//
//    /**
//     * 构建已发货退款单
//     *
//     * @param ocBReturnAfSends
//     * @param ocBReturnAfSendItems
//     * @return
//     */
//    private List<AcFSourceBillRequest> buildRefundSourceBill(List<OcBReturnAfSend> ocBReturnAfSends, List<OcBReturnAfSendItem> ocBReturnAfSendItems) {
//        List<AcFSourceBillRequest> billRequests = new ArrayList<>();
//        Map<Long, List<OcBReturnAfSendItem>> itemMap = ocBReturnAfSendItems.stream().collect(Collectors.groupingBy(OcBReturnAfSendItem::getOcBReturnAfSendId));
//        for (OcBReturnAfSend returnAfSend : ocBReturnAfSends) {
//            AcFSourceBillRequest request = new AcFSourceBillRequest();
//            AcFSourceBill sourceBill = new AcFSourceBill();
//            sourceBill.setSourceId(returnAfSend.getId());
//            sourceBill.setSourceBillNo(returnAfSend.getBillNo());
//            sourceBill.setRefundSourceCode(returnAfSend.getTReturnId());
//            sourceBill.setPlatform(returnAfSend.getCpCPlatformId());
//            Long cpCPlatformId = returnAfSend.getCpCPlatformId();
//            sourceBill.setPlatformEname(PlatFormEnum.getName(NumberUtils.toInt(cpCPlatformId.toString())));
//            sourceBill.setOrderSourceCode(returnAfSend.getTid());
//            sourceBill.setCpCShopId(returnAfSend.getCpCShopId());
//            sourceBill.setCpCShopEcode(returnAfSend.getCpCShopEcode());
//            sourceBill.setCpCShopTitle(returnAfSend.getCpCShopTitle());
//            sourceBill.setBillType(OmsBillTypeEnum.RETAIL_REFUND_ORDER.getCode());
//            if (StringUtils.isEmpty(returnAfSend.getTReturnId())) {
//                sourceBill.setBillSource(2);
//            } else {
//                sourceBill.setBillSource(1);
//            }
//            sourceBill.setRefundSourceCode(returnAfSend.getTid());
//            sourceBill.setRefundType(returnAfSend.getBillType());
//            sourceBill.setRefundAmt(returnAfSend.getRefundFee());
//            sourceBill.setRefundAmtActual(returnAfSend.getAmtReturnActual());
//            List<AcFSourceBillItem> itemList = new ArrayList<>();
//            List<OcBReturnAfSendItem> returnAfSendItems = itemMap.get(returnAfSend.getId());
//            for (OcBReturnAfSendItem returnAfSendItem : returnAfSendItems) {
//                AcFSourceBillItem billItem = new AcFSourceBillItem();
//                billItem.setPsCProId(returnAfSendItem.getPsCProId());
//                billItem.setPsCProEcode(returnAfSendItem.getPsCProEcode());
//                billItem.setPsCProEname(returnAfSendItem.getPsCProEname());
//                billItem.setPsCSkuId(returnAfSendItem.getPsCSkuId());
//                billItem.setPsCSkuEcode(returnAfSendItem.getPsCSkuEcode());
//                billItem.setPsCSkuEname(returnAfSendItem.getPsCSkuEname());
//                billItem.setTid(returnAfSend.getTid());
//                billItem.setQty(returnAfSendItem.getQtyReturnApply());
//                billItem.setReturnAmt(returnAfSendItem.getAmtReturn());
//                itemList.add(billItem);
//            }
//            request.setAcFSourceBill(sourceBill);
//            request.setItemList(itemList);
//            billRequests.add(request);
//        }
//        return billRequests;
//
//    }
//
//    /**
//     * 构建退货单
//     *
//     * @param ocBReturnOrders
//     * @param orderRefundList
//     * @return
//     */
//    private List<AcFSourceBillRequest> buildReturnSourceBill(List<OcBReturnOrder> ocBReturnOrders, List<OcBReturnOrderRefund> orderRefundList) {
//        List<AcFSourceBillRequest> billRequests = new ArrayList<>();
//        Map<Long, List<OcBReturnOrderRefund>> itemMap = orderRefundList.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getOcBReturnOrderId));
//        for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
//            AcFSourceBillRequest request = new AcFSourceBillRequest();
//            AcFSourceBill sourceBill = new AcFSourceBill();
//            sourceBill.setSourceId(ocBReturnOrder.getId());
//            sourceBill.setSourceBillNo(ocBReturnOrder.getBillNo());
//            Integer platform = ocBReturnOrder.getPlatform();
//            sourceBill.setPlatform(Long.valueOf(platform));
//            sourceBill.setPlatformEname(PlatFormEnum.getName(platform));
//            sourceBill.setOrderSourceCode(ocBReturnOrder.getOrigSourceCode());
//            sourceBill.setExchangeSourceCode(ocBReturnOrder.getTbDisputeId() + "");
//            sourceBill.setRefundSourceCode(ocBReturnOrder.getReturnId());
//            sourceBill.setScanTime(ocBReturnOrder.getInTime());
//            sourceBill.setBillType(OmsBillTypeEnum.RETAIL_RETURN_ORDER.getCode());
//            if (StringUtils.isEmpty(ocBReturnOrder.getReturnId())) {
//                sourceBill.setBillSource(2);
//            } else {
//                sourceBill.setBillSource(1);
//            }
//            sourceBill.setReturnType(ocBReturnOrder.getBillType());
//            sourceBill.setReturnLogisticsNumber(ocBReturnOrder.getLogisticsCode());
//            sourceBill.setExchangeAmt(ocBReturnOrder.getExchangeAmt());
//            sourceBill.setReturnAmtList(ocBReturnOrder.getReturnAmtList());
//            sourceBill.setReturnAmtShip(ocBReturnOrder.getReturnAmtShip());
//            sourceBill.setReturnAmtOther(ocBReturnOrder.getReturnAmtOther());
//            sourceBill.setReturnAmtActual(ocBReturnOrder.getReturnAmtActual());
//            sourceBill.setCpCShopId(ocBReturnOrder.getCpCShopId());
//            sourceBill.setCpCShopEcode(ocBReturnOrder.getCpCShopEcode());
//            sourceBill.setCpCShopTitle(ocBReturnOrder.getCpCShopTitle());
//            sourceBill.setReturnCpCPhyWarehouseId(ocBReturnOrder.getCpCPhyWarehouseInId());
//            sourceBill.setReturnCpCLogisticsId(ocBReturnOrder.getCpCLogisticsId());
//            sourceBill.setReturnCpCLogisticsEname(ocBReturnOrder.getCpCLogisticsEname());
//            List<OcBReturnOrderRefund> refundList = itemMap.get(ocBReturnOrder.getId());
//            List<AcFSourceBillItem> itemList = new ArrayList<>();
//            for (OcBReturnOrderRefund returnOrderRefund : refundList) {
//                AcFSourceBillItem billItem = new AcFSourceBillItem();
//                billItem.setPsCProId(returnOrderRefund.getPsCProId());
//                billItem.setPsCProEcode(returnOrderRefund.getPsCProEcode());
//                billItem.setPsCProEname(returnOrderRefund.getPsCProEname());
//                billItem.setPsCSkuId(returnOrderRefund.getPsCSkuId());
//                billItem.setPsCSkuEcode(returnOrderRefund.getPsCSkuEcode());
//                billItem.setPsCSkuEname(returnOrderRefund.getPsCSkuEname());
//                billItem.setTid(returnOrderRefund.getTid());
//                billItem.setOoid(returnOrderRefund.getOid());
//                billItem.setQty(returnOrderRefund.getQtyRefund());
//                billItem.setPriceList(returnOrderRefund.getPriceList());
//                billItem.setPrice(returnOrderRefund.getPrice());
//                billItem.setReturnAmt(returnOrderRefund.getAmtRefund());
//                itemList.add(billItem);
//            }
//            request.setAcFSourceBill(sourceBill);
//            request.setItemList(itemList);
//            billRequests.add(request);
//        }
//        return billRequests;
//    }
//
//
//    /**
//     * 构建零售发货单的入参
//     *
//     * @param ocBOrderList
//     * @param items
//     * @return
//     */
//    private List<AcFSourceBillRequest> buildOrderSourceBill(List<OcBOrder> ocBOrderList, List<OcBOrderItem> items) {
//        List<AcFSourceBillRequest> billRequests = new ArrayList<>();
//        Map<Long, List<OcBOrderItem>> itemMap = items.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
//        for (OcBOrder ocBOrder : ocBOrderList) {
//            AcFSourceBillRequest request = new AcFSourceBillRequest();
//            AcFSourceBill sourceBill = new AcFSourceBill();
//            sourceBill.setSourceId(ocBOrder.getId());
//            sourceBill.setSourceBillNo(ocBOrder.getBillNo());
//            Long platform = Long.valueOf(ocBOrder.getPlatform());
//            sourceBill.setPlatform(platform);
//            sourceBill.setPlatformEname(PlatFormEnum.getName(ocBOrder.getPlatform()));
//            sourceBill.setOrderSourceCode(ocBOrder.getSourceCode());
//            sourceBill.setOrderDate(ocBOrder.getOrderDate());
//            sourceBill.setBillType(OmsBillTypeEnum.RETAIL_INVOICE_ORDER.getCode());
//            String orderSource = ocBOrder.getOrderSource();
//            if ("手工新增".equals(orderSource)) {
//                sourceBill.setBillSource(2);
//            } else {
//                sourceBill.setBillSource(1);
//            }
//            sourceBill.setOrderType(ocBOrder.getOrderType());
//            sourceBill.setLogisticsNumber(ocBOrder.getExpresscode());
//            sourceBill.setProductAmt(ocBOrder.getProductAmt());
//            sourceBill.setShipAmt(ocBOrder.getShipAmt());
//            sourceBill.setServiceAmt(ocBOrder.getServiceAmt());
//            sourceBill.setAdjustAmt(ocBOrder.getAdjustAmt());
//            sourceBill.setOrderDiscountAmt(ocBOrder.getOrderDiscountAmt());
//            sourceBill.setProductDiscountAmt(ocBOrder.getProductDiscountAmt());
//            sourceBill.setOrderAmt(ocBOrder.getOrderAmt());
//            sourceBill.setIsMergeOrder(ocBOrder.getIsMerge());
//            sourceBill.setIsSplitOrder(ocBOrder.getIsSplit());
//            sourceBill.setCpCShopId(ocBOrder.getCpCShopId());
//            sourceBill.setCpCShopEcode(ocBOrder.getCpCShopEcode());
//            sourceBill.setCpCShopTitle(ocBOrder.getCpCShopTitle());
//            sourceBill.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
//            sourceBill.setCpCPhyWarehouseEcode(ocBOrder.getCpCPhyWarehouseEcode());
//            sourceBill.setCpCPhyWarehouseEname(ocBOrder.getCpCPhyWarehouseEname());
//            sourceBill.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
//            sourceBill.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
//            sourceBill.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
//            sourceBill.setReceivedAmt(ocBOrder.getReceivedAmt());
//            sourceBill.setAmtReceive(ocBOrder.getAmtReceive());
//            List<OcBOrderItem> orderItems = itemMap.get(ocBOrder.getId());
//            List<AcFSourceBillItem> itemList = new ArrayList<>();
//            for (OcBOrderItem orderItem : orderItems) {
//                AcFSourceBillItem billItem = new AcFSourceBillItem();
//                billItem.setPsCProId(orderItem.getPsCProId());
//                billItem.setPsCProEcode(orderItem.getPsCProEcode());
//                billItem.setPsCProEname(orderItem.getPsCProEname());
//                billItem.setPsCSkuId(orderItem.getPsCSkuId());
//                billItem.setPsCSkuEcode(orderItem.getPsCSkuEcode());
//                billItem.setPsCSkuEname(orderItem.getPsCSkuEname());
//                billItem.setTid(orderItem.getTid());
//                billItem.setOoid(orderItem.getOoid());
//                billItem.setQty(orderItem.getQty());
//                billItem.setPriceList(orderItem.getPriceList());
//                billItem.setPrice(orderItem.getPrice());
//                billItem.setPriceActual(orderItem.getPriceActual());
//                billItem.setRealAmt(orderItem.getRealAmt());
//                billItem.setDiscountAmt(orderItem.getAmtDiscount());
//                billItem.setAdjustAmt(orderItem.getAdjustAmt());
//                billItem.setSplitAmt(orderItem.getOrderSplitAmt());
//                billItem.setIsGift(orderItem.getIsGift());
//                itemList.add(billItem);
//            }
//            request.setAcFSourceBill(sourceBill);
//            request.setItemList(itemList);
//            billRequests.add(request);
//        }
//        return billRequests;
//    }
//
//
//    @Override
//    public boolean isClose() {
//        return true;
//    }
//}
