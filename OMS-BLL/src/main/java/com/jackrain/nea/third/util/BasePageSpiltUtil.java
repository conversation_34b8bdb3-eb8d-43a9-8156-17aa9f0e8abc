package com.jackrain.nea.third.util;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/1/10 上午11:14
 * @Version 1.0
 */
public class BasePageSpiltUtil {


    public static <T> List<List<T>> getBasePageList(List<T> sourceList, int pageSize) {

        List<List<T>> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(sourceList)) {
            return resultList;
        }
        int listSize = sourceList.size();
        int page = listSize / pageSize;
        if (listSize % pageSize != 0) {
            page++;
        }
        for (int i = 0; i < page; i++) {
            List<T> pageList = sourceList.subList(i * pageSize,
                    (Math.min((i + 1) * pageSize, listSize)));
            resultList.add(pageList);
        }
        return resultList;
    }
}
