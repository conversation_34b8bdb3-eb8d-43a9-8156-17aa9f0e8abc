package com.jackrain.nea.third.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: 黄世新
 * @Dat2022/1/6 下午2:13
 * @Version 1.0
 */
@AllArgsConstructor
public enum  OmsBillTypeEnum {

    RETAIL_INVOICE_ORDER(1,"零售发货单"),

    RETAIL_REFUND_ORDER(3, "已发货退款单(仅退款,退款成功)"),

    RETAIL_RETURN_ORDER(2, "退货退款(已入库)"),

    UPDATE_PLATFORM_STATUS(4, "平台单号更新");


    @Getter
    private Integer code;
    @Getter
    private String message;
}
