package com.jackrain.nea.rpc;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.api.AcBubbleFeeConfigCmd;
import com.jackrain.nea.ac.api.AcCycleBuyCmd;
import com.jackrain.nea.ac.api.AcLogisticsFeeQueryCmd;
import com.jackrain.nea.ac.model.request.AcBubbleFeeConfigRequest;
import com.jackrain.nea.ac.model.result.AcBubbleFeeConfigResult;
import com.jackrain.nea.ac.model.result.AcLogisticsFeeInfoBySapResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @author: 易邵峰
 * @since: 2019-03-22
 * create at : 2019-03-22 11:54
 */
@Slf4j
@Component
public class AcRpcService {

    @DubboReference(group = "ac", version = "1.0")
    private AcLogisticsFeeQueryCmd acLogisticsFeeQueryCmd;

    @DubboReference(group = "ac", version = "1.0")
    private AcCycleBuyCmd acCycleBuyCmd;

    @DubboReference(group = "ac", version = "1.0")
    private AcBubbleFeeConfigCmd acBubbleFeeConfigCmd;

    /**
     * 应付款调整单
     *
     * @param obj 主表和明细 json
     * @return
     */
    public ValueHolderV14 payableAdjustDropCopy(JSONObject obj, User user) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14(ResultCode.SUCCESS, "success");
        try {
            valueHolderV14 = null;//payableAdjustDropCopyCmd.insertPayableAdjustDropCopy(obj, user);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("应付款调整单info:{}", "应付款调整单info"), JSONObject.toJSONString(valueHolderV14));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("应付款调整单error:{}", "应付款调整单"), Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("应付款调整单error:" + e.getMessage());
        }
        return valueHolderV14;
    }

    public ValueHolderV14 calculateLogisticsFee(OcBOrder orderInfo) {
        ValueHolderV14<AcLogisticsFeeInfoBySapResult> v14 = new ValueHolderV14<>();
        try {
            //查询物流费用
            v14 = acLogisticsFeeQueryCmd.calculateLogisticsFee(orderInfo.getId());
            log.info(LogUtil.format("calculateLogisticsFee orderId:{},v14:{}",
                    "calculateLogisticsFee"), orderInfo.getId(), JSONObject.toJSONString(v14));
            return v14;
        } catch (Exception e) {
            log.error(LogUtil.format("OrderDeliveryOfSapImpl.build.queryFee error:{}",
                    "OrderDeliveryOfSapImpl.build.queryFee"), Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
            return v14;
        }
    }

    public ValueHolderV14 getReceivableAdjustmentData(JSONObject var, User user) {
        ValueHolderV14 holder = null;// getReceivableAdjustmentDataCmd.createReceivableAdjustment(var, user);
        return holder;
    }

    /**
     * 按照日期计算中台周期购报表
     *
     * @param tids
     * @return
     */
    public ValueHolderV14 queryConfirmInfo(List<String> tids) {
        try {
            return acCycleBuyCmd.queryConfirmInfo(tids);
        } catch (Exception e) {
            log.error("queryConfirmInfo tids:{}, error:{}", tids, Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }

    public ValueHolderV14 reportCalculateTid(List<String> list, Date date) {
        try {
            return acCycleBuyCmd.reportCalculateByTids(list, date);
        } catch (Exception e) {
            log.error("reportCalculate date:{}, error:{}", date, Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }

    public ValueHolderV14<List<AcBubbleFeeConfigResult>> queryBubbleFeeConfig(Long logisticsId) {
        try {
            AcBubbleFeeConfigRequest request = new AcBubbleFeeConfigRequest();
            request.setCpCLogisticsId(logisticsId);
            return acBubbleFeeConfigCmd.queryBubbleFeeConfig(request);
        } catch (Exception e) {
            log.error("queryBubbleFeeConfig error:{}", Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }

}
