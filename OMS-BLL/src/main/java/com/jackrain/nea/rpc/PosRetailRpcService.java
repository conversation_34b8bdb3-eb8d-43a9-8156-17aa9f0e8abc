package com.jackrain.nea.rpc;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.retail.api.OmsReverseOrderCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * date ：Created in 14:11 2019/12/3
 * description ：POS零售rpc调用
 * @ Modified By：
 */
@Slf4j
@Component
public class PosRetailRpcService {
    @Reference(group = "pos", version = "1.0")
    private OmsReverseOrderCmd omsReverseOrderCmd;

    public ValueHolder orderCancelInformPos(JSONObject param) {
        log.info(LogUtil.format("订单取消通知线下POS端RPC调用参数：", "订单取消通知线下POS端RPC调用参数") + param);
        QuerySession querySession = new QuerySessionImpl();
        Map paramMap = new HashMap();
        DefaultWebEvent event = new DefaultWebEvent("test", paramMap);
        event.put("param", param);
        querySession.setEvent(event);
        return omsReverseOrderCmd.execute(querySession);
    }
}
