package com.jackrain.nea.rpc;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.api.SgCSaStoreCmd;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.model.table.basic.SgCSaStore;
import com.burgeon.r3.sg.inf.api.oms.SgOmsBigValidityQueryCmd;
import com.burgeon.r3.sg.inf.api.oms.SgOmsShareOutVoidCmd;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsBigValiditySkuIdQueryRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsBigValiditySkuIdQueryResult;
import com.burgeon.r3.sg.share.api.out.SgBShareOutCmd;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutQueryParam;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutQueryResult;
import com.burgeon.r3.sg.sourcing.api.SgDirectOrderCmd;
import com.burgeon.r3.sg.sourcing.api.SgFindSourceStickerCmd;
import com.burgeon.r3.sg.sourcing.model.request.SgDirectOrderStorageOccupyItemRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgDirectOrderStorageOccupyRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStickerRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgDirectOrderStorageOccupyResult;
import com.burgeon.r3.sg.store.api.out.SgBStoFreezeOutResultQueryCmd;
import com.burgeon.r3.sg.store.api.out.SgBStoOutCmd;
import com.burgeon.r3.sg.store.api.out.SgBStoOutResultQueryCmd;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutQueryParam;
import com.burgeon.r3.sg.store.model.result.SgStoFreezeOutResultQueryResult;
import com.burgeon.r3.sg.store.model.result.SgStoOutResultQueryResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutQueryRes;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrder;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.util.DateFormatUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/7/26 下午1:13
 * @Version 1.0
 */
@Component
@Slf4j
public class SgNewRpcService {

    @Reference(group = "sg", version = "1.0")
    private SgOmsBigValidityQueryCmd sgOmsBigValidityQueryCmd;

    @Reference(group = "sg", version = "1.0")
    private SgFindSourceStickerCmd sgFindSourceStickerCmd;

    @Reference(group = "sg", version = "1.0")
    private SgBStoOutResultQueryCmd sgBStoOutResultQueryCmd;

    @Reference(group = "sg", version = "1.0")
    private SgBStoFreezeOutResultQueryCmd sgBStoFreezeOutResultQueryCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgDirectOrderCmd sgDirectOrderCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgOmsShareOutVoidCmd sgOmsShareOutVoidCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgBStoOutCmd sgBStoOutCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgBShareOutCmd sgBShareOutCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgCSaStoreCmd sgCSaStoreCmd;



    public List<SgStoOutResultQueryResult> querySgBStoOutResultItemList(OcBOrder ocBOrder) {
        try {
            ValueHolderV14 holder = sgBStoOutResultQueryCmd.queryStoOutResultByStoNoticeNo(ocBOrder.getSgBOutBillNo());
            if (holder.isOK()) {
                return (List<SgStoOutResultQueryResult>) holder.getData();
            }
            return null;
        } catch (Exception e) {
            log.error(LogUtil.format("查询逻辑出库单数据失败:{}", "根据出库通知单号查询逻辑出库单"), e.getMessage());
            return null;
        }
    }

    public List<SgStoFreezeOutResultQueryResult> querySgBStoFreezeOutResultItemList(OcBOrder ocBOrder) {
        try {
            ValueHolderV14 holder = sgBStoFreezeOutResultQueryCmd.queryStoFreezeOutResultByStoNoticeNo(ocBOrder.getSgBOutBillNo());
            if (holder.isOK()) {
                return (List<SgStoFreezeOutResultQueryResult>) holder.getData();
            }
            return null;
        } catch (Exception e) {
            log.error(LogUtil.format("查询冻结出库单数据失败:{}", "根据出库通知单号查询冻结出库单"), e.getMessage());
            return null;
        }
    }

    public List<SgOmsBigValiditySkuIdQueryResult> querySaStoreBigValidityBySkuIds(OcBOrder ocBOrder, List<OcBOrderItem> items) {
        SgOmsBigValiditySkuIdQueryRequest request = new SgOmsBigValiditySkuIdQueryRequest();
        request.setCpCShopId(ocBOrder.getCpCShopId());
        List<Long> skuIdList = new ArrayList<>();
        for (OcBOrderItem item : items) {
            skuIdList.add(item.getPsCSkuId());
        }
        request.setPsCSkuIds(skuIdList);
        log.info(LogUtil.format("获取是否大效期仓入参:{}", "获取是否大效期仓返回"), JSONObject.toJSONString(request));

        ValueHolderV14<List<SgOmsBigValiditySkuIdQueryResult>> holder = sgOmsBigValidityQueryCmd.querySaStoreBigValidityBySkuIds(request);
        log.info(LogUtil.format("获取是否大效期仓返回:{}", "获取是否大效期仓返回"), JSONObject.toJSONString(holder));
        if (holder.isOK()) {
            return holder.getData();
        }
        return null;
    }

    /**
     * 贴纸方案
     *
     * @param ocBOrder
     * @param items    新的贴纸list
     * @return
     */
    public boolean findSourceSticker(OcBOrder ocBOrder, List<OcBOrderItem> items, User user) {
        SgFindSourceStickerRequest request = new SgFindSourceStickerRequest();
        request.setShopId(ocBOrder.getCpCShopId());
        request.setSourceBillId(ocBOrder.getId());
        request.setSourceBillNo(ocBOrder.getBillNo());
        request.setTid(ocBOrder.getTid());
        request.setBillDate(ocBOrder.getOrderDate());
        request.setSourceBillType(1);
        request.setLoginUser(user);
        List<SgFindSourceStickerRequest.SgFindSourceStickerItemRequest> itemList = new ArrayList<>();
        for (OcBOrderItem item : items) {
            SgFindSourceStickerRequest.SgFindSourceStickerItemRequest itemRequest = new SgFindSourceStickerRequest.SgFindSourceStickerItemRequest();
            itemRequest.setQty(item.getQty());
            itemRequest.setSourceBillItemId(item.getId());
            itemRequest.setPsCSkuId(item.getPsCSkuId());
            itemList.add(itemRequest);
        }
        request.setItemList(itemList);
        log.info(LogUtil.format("贴纸方案入参:{}", "贴纸方案入参", ocBOrder.getId()), JSONObject.toJSONString(request));
        ValueHolderV14 sourceSticker = sgFindSourceStickerCmd.findSourceSticker(request);
        log.info(LogUtil.format("贴纸方案出参:{}", "贴纸方案出参", ocBOrder.getId()), JSONObject.toJSONString(sourceSticker));
        return sourceSticker.isOK();
    }

    /**
     * 直发占用单-审核
     */
    public ValueHolderV14<SgDirectOrderStorageOccupyResult> directReportOrderSubmit(OcBDirectReportOrder main,
                                                                    List<OcBDirectReportOrderItem> items, User user) {
        SgDirectOrderStorageOccupyRequest request = new SgDirectOrderStorageOccupyRequest();
        request.setSourceBillType(SgConstantsIF.BILL_TYPE_DIRECT_ORDER);
        request.setSourceBillId(main.getId());
        request.setSourceBillNo(main.getBillNo());

        request.setSaStoreId(main.getSgCSaStoreId());
        request.setStoreId(main.getCpCStoreId());
        request.setDistCodeLevel2(main.getCpCDisOrgLv2Code());

        request.setBillDate(main.getBillDate());
        request.setShopId(main.getCpCShopId());

        List<SgDirectOrderStorageOccupyItemRequest> requestItems = new ArrayList<>();
        for (OcBDirectReportOrderItem item : items) {
            SgDirectOrderStorageOccupyItemRequest requestItem = new SgDirectOrderStorageOccupyItemRequest();
            requestItem.setSourceItemId(item.getId());
            requestItem.setSkuId(item.getPsCSkuId());
            requestItem.setSkuCode(item.getPsCSkuEcode());
            requestItem.setQty(item.getQty());
            requestItem.setBeginProduceDate(Objects.isNull(item.getStartProduceDate()) ?
                    null : DateFormatUtil.formatDate(item.getStartProduceDate(), DateFormatUtil.YYYYMMDD));
            requestItem.setEndProduceDate(Objects.isNull(item.getEndProduceDate()) ?
                    null : DateFormatUtil.formatDate(item.getEndProduceDate(), DateFormatUtil.YYYYMMDD));
            requestItems.add(requestItem);
        }
        request.setItemRequestList(requestItems);
        request.setUser(user);
        ValueHolderV14<SgDirectOrderStorageOccupyResult> holderV14 = sgDirectOrderCmd.storageOccupy(request);
        log.info(LogUtil.format("SG审核结束，入参:{},结果:{}", "SgNewRpcService.directReportOrderVoid"),
                    JSONObject.toJSONString(request), JSONObject.toJSONString(holderV14));
        return holderV14;
    }

    /**
     * 直发占用单-占用
     */
    public void directReportOrderVoid(OcBDirectReportOrder main, User user) {
        SgOmsShareOutRequest request = new SgOmsShareOutRequest();
        request.setSourceBillId(main.getId());
        request.setSourceBillNo(main.getBillNo());
        request.setSourceBillType(SgConstantsIF.BILL_TYPE_DIRECT_ORDER);
        request.setLoginUser(user);

        ValueHolderV14 holderV14 = sgOmsShareOutVoidCmd.voidSgOmsShareOut(request);
        if (Objects.isNull(holderV14) || !holderV14.isOK()) {
            log.warn(LogUtil.format("SG作废失败，入参:{},结果:{}", "SgNewRpcService.directReportOrderVoid"),
                    JSONObject.toJSONString(request), JSONObject.toJSONString(holderV14));
            throw new NDSException("SG作废失败：" + holderV14.getMessage());
        }
        /*holderV14.getData();*/
    }

    /**
     * 查询-逻辑占用单（明细）
     */
    public SgBStoOutQueryRes queryStoOut(Integer billType, Long sourceBillId, String sourceBillNo) {
        if (Objects.isNull(billType)) {
            throw new NDSException("来源单据类型不能为空");
        }
        if (Objects.isNull(sourceBillId) && StringUtils.isBlank(sourceBillNo)) {
            throw new NDSException("来源单据ID与来源单据编号不能同时为空");
        }

        SgBStoOutQueryParam param = new SgBStoOutQueryParam();
        param.setSourceBillType(billType);
        param.setSourceBillId(sourceBillId);
        param.setSourceBillNo(sourceBillNo);
        /*1-创建，2-更新 3-部分发货 4-发货完成 5-已作废*/
        param.setBillStatusList(Arrays.asList(1, 2, 3, 4));
        ValueHolderV14<SgBStoOutQueryRes> holderV14 = sgBStoOutCmd.querySgBStoOutBySourceBill(param);
        if (Objects.isNull(holderV14) || !holderV14.isOK()) {
            log.warn(LogUtil.format("查询失败，入参:{},结果:{}", "SgNewRpcService.queryShareOut"),
                    JSONObject.toJSONString(param), JSONObject.toJSONString(holderV14));
            throw new NDSException("查询失败：" + holderV14.getMessage());
        }
        return holderV14.getData();
    }

    /**
     * 查询-配销占用单（明细）
     */
    public SgBShareOutQueryResult queryShareOut(Integer billType, Long sourceBillId, String sourceBillNo) {
        if (Objects.isNull(billType)) {
            throw new NDSException("来源单据类型不能为空");
        }
        if (Objects.isNull(sourceBillId) && StringUtils.isBlank(sourceBillNo)) {
            throw new NDSException("来源单据ID与来源单据编号不能同时为空");
        }

        SgBShareOutQueryParam param = new SgBShareOutQueryParam();
        param.setSourceBillType(billType);
        param.setSourceBillId(sourceBillId);
        param.setSourceBillNo(sourceBillNo);
        /*1-创建，2-部分发货 3-发货完成，4-已作废*/
        param.setBillStatusList(Arrays.asList(1, 2, 3));
        ValueHolderV14<SgBShareOutQueryResult> holderV14 = sgBShareOutCmd.queryShareOutBySourceBill(param);
        if (Objects.isNull(holderV14) || !holderV14.isOK()) {
            log.warn(LogUtil.format("查询失败，入参:{},结果:{}", "SgNewRpcService.queryShareOut"),
                    JSONObject.toJSONString(param), JSONObject.toJSONString(holderV14));
            throw new NDSException("查询失败：" + holderV14.getMessage());
        }
        return holderV14.getData();
    }


    public Map<String, SgCSaStore> querySaStoreMapByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return MapUtil.empty();
        }

        try {
            ValueHolderV14<List<SgCSaStore>> holderV14 = sgCSaStoreCmd.queryListByEcode(codes);
            if (Objects.isNull(holderV14) || !holderV14.isOK()) {
                return MapUtil.empty();
            }
            return ListUtils.emptyIfNull(holderV14.getData()).stream()
                    .collect(Collectors.toMap(SgCSaStore::getEcode, x -> x, (a, b) -> a));
        } catch (Exception e) {
            log.error(" 通过编码查询配销仓信息映射异常：{}", Throwables.getStackTraceAsString(e));
        }
        return MapUtil.empty();
    }

}
