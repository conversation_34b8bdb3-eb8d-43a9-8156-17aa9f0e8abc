package com.jackrain.nea.rpc;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.ps.api.CproDimItemQueryCmd;
import com.jackrain.nea.ps.api.CproductReturnQueryCmd;
import com.jackrain.nea.ps.api.ProSkuListCmd;
import com.jackrain.nea.ps.api.PsCProdimItemQueryCmd;
import com.jackrain.nea.ps.api.SkuListCmd;
import com.jackrain.nea.ps.api.request.ProSkuListCmdRequest;
import com.jackrain.nea.ps.api.request.SkuInfoListRequest;
import com.jackrain.nea.ps.api.request.SkuListCmdRequest;
import com.jackrain.nea.ps.api.result.ProAttributeInfo;
import com.jackrain.nea.ps.api.result.ProSkuResult;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.IpCStandplatProItem;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.ps.model.OmsProAttributeInfo;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.psext.api.CskuGroupQureyCmd;
import com.jackrain.nea.psext.api.PsCCollShopProMappingQueryCmd;
import com.jackrain.nea.psext.api.PsCProDimQueryCmd;
import com.jackrain.nea.psext.api.PsCProQueryCmd;
import com.jackrain.nea.psext.api.SkuLikeQueryCmd;
import com.jackrain.nea.psext.api.ValidityDefinitionQueryCmd;
import com.jackrain.nea.psext.api.VirtualProSplitCmd;
import com.jackrain.nea.psext.model.table.ExtractLuckyBag;
import com.jackrain.nea.psext.model.table.PsCCollShopProMapping;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.model.table.PsCSkugroup;
import com.jackrain.nea.psext.model.table.PsCValidityDefinition;
import com.jackrain.nea.psext.request.PsToWmsRequest;
import com.jackrain.nea.psext.request.SkuQueryListRequest;
import com.jackrain.nea.psext.request.SkuQueryRequest;
import com.jackrain.nea.psext.request.VirtualProSplitRequest;
import com.jackrain.nea.psext.result.Dim8Result;
import com.jackrain.nea.psext.result.ProExtResult;
import com.jackrain.nea.psext.result.PsExtCSkuResult;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.PsRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * PS 商品中心相关Service
 *
 * @author: 易邵峰
 * @since: 2019-03-22
 * create at : 2019-03-22 11:54
 */
@Component
@Slf4j
public class PsRpcService {

    @Reference(group = "ps", version = "1.0")
    private ProSkuListCmd proSkuListCmd;

    @Reference(group = "ps", version = "1.0")
    private CskuGroupQureyCmd cskuGroupQureyCmd;

    @Reference(group = "ps-ext", version = "1.0")
    private SkuLikeQueryCmd skuLikeQueryCmd;

    @Reference(group = "ps", version = "1.0")
    private SkuListCmd skuListCmd;

    @Reference(group = "ps-ext", version = "1.0")
    private VirtualProSplitCmd virtualProSplitCmd;

    @Autowired
    private BasicPsQueryService basicPsQueryService;
    @Autowired
    private RedisOpsUtil<String, String> redisUtil;
    @Reference(group = "ps", version = "1.0")
    private CproDimItemQueryCmd cproDimItemQueryCmd;

    @Reference(group = "ps", version = "1.0")
    private PsCProdimItemQueryCmd psCProdimItemQueryCmd;

    @Reference(group = "ps", version = "1.0")
    private CproductReturnQueryCmd cproductReturnQueryCmd;

    @Reference(group = "ps-ext", version = "1.0")
    private ValidityDefinitionQueryCmd validityDefinitionQueryCmd;

    @Reference(group = "ps-ext", version = "1.0")
    private PsCProDimQueryCmd psCProDimQueryCmd;

    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCCollShopProMappingQueryCmd psCCollShopProMappingQueryCmd;

    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCProQueryCmd psCProQueryCmd;

    /**
     * 20200714 添加开关，
     * gbCode查询sku接口  如果是true  则匹配eCode
     */
    @Value("${r3.oms.match.sku.ecode.enable:true}")
    private Boolean findSkuType;

    public List<PsCSku> selectSkuListbyEcode(List<String> codeList) {
        List<PsCSku> psCSkuList = null;
        try {
            psCSkuList = skuLikeQueryCmd.querySkuByEcode(codeList);
        } catch (Exception e) {
            log.error(LogUtil.format("查询sku异常{}"), Throwables.getStackTraceAsString(e));
        }
        return psCSkuList;
    }

    /**
     * 查询所有性别
     *
     * @return vh14
     */
    public ValueHolderV14<Map<Long, PsCProdimItem>> queryAllSexInfo() {
        return psCProdimItemQueryCmd.queryAllSex();
    }

    /**
     * 根据商品编码查询商品信息
     *
     * @param ecodes
     * @return
     */
    public List<PsCPro> queryProByEcode(List<String> ecodes) {
        if (CollectionUtils.isEmpty(ecodes)) {
            return Collections.emptyList();
        }

        List<PsCPro> psCPros = new ArrayList<>();
        try {
            psCPros = skuLikeQueryCmd.queryProByEcode(ecodes);
            if (log.isDebugEnabled()) {
                log.debug(" 查询商品信息返回：{}", JSON.toJSONString(psCPros));
            }
        } catch (Exception e) {
            log.error(" 查询商品信息异常：{}", Throwables.getStackTraceAsString(e));
        }
        return psCPros;
    }

    /**
     * 检查条码是否可用
     *
     * @param skuCode 条码
     * @return Map
     */
    public Map<String, Integer> queryCount(String skuCode) {
        return skuLikeQueryCmd.queryCount(skuCode);
    }

    /**
     * 根据条码ID 查条码信息
     *
     * @param ids id集合
     * @return
     */
    public List<SkuQueryListRequest> querySkuByIds(List<Integer> ids) {
        List<SkuQueryListRequest> skuQueryListRequests = skuLikeQueryCmd.querySkuByIds(ids);
        return skuQueryListRequests;
    }

    /**
     * 根据商品ID 查询条码信息
     *
     * @param ids
     * @return
     */
    public List<PsCPro> queryProByIds(List<Integer> ids) {
        List<PsCPro> psCPros = skuLikeQueryCmd.queryProByIds(ids);
        return psCPros;
    }

    public List<IpCStandplatProItem> selectIpCStandProductItemBySkuId(String skuId) {
        return proSkuListCmd.selectIpCStandProductItemBySkuId(skuId);
    }

    /**
     * 根据skuecode查询条码信息
     *
     * @param sku
     * @return
     */
    public ProductSku selectProductSku(String sku) {
        String redisKey = PsRedisKeyResources.getProductSkuKey(sku);
        try {
            String skuInfo = redisUtil.strRedisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotEmpty(skuInfo)) {
                ProductSku productSku = JSONObject.parseObject(skuInfo, ProductSku.class);
                return productSku;
            }
            List<String> skuList = new ArrayList<>();
            skuList.add(sku);
            ProSkuListCmdRequest listCmdRequest = new ProSkuListCmdRequest();
            listCmdRequest.setEcodes(skuList);
            ValueHolder holder = proSkuListCmd.execute(listCmdRequest);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("SelectProductSku From RPC Sku={};SelectResult={}", sku), sku, JSONUtil.toJsonStr(holder));
            }
            int code = Tools.getInt(holder.get("code"), -1);
            if (code == 0) {
                if (holder.getData().containsKey("data")) {
                    ProSkuResult proSkuResult = (ProSkuResult) holder.get("data");
                    if (CollectionUtils.isNotEmpty(proSkuResult.getProSkuList())) {
                        ProductSku productSku1 =
                                this.buildProductSku(proSkuResult.getProSkuList().get(0));
                        String jsonString = JSONObject.toJSONString(productSku1);
                        redisUtil.strRedisTemplate.opsForValue().set(redisKey, jsonString, 20L, TimeUnit.MINUTES);
                        return productSku1;
                    }
                }
            }

        } catch (Exception ex) {
            log.error(LogUtil.format(" ProductService.selectProductSku") + Throwables.getStackTraceAsString(ex));
        }
        return null;
    }

    /**
     * 根据skuecode查询条码信息
     *
     * @param skuList
     * @return
     */
    public List<ProductSku> selectProductSkuIgnoreActive(List<String> skuList) {
        List<ProductSku> productSkuList = new ArrayList<>();
        try {
            ProSkuListCmdRequest listCmdRequest = new ProSkuListCmdRequest();
            listCmdRequest.setEcodes(skuList);
            ValueHolder holder = proSkuListCmd.querySkuInfoIgnoreActive(listCmdRequest);
            int code = Tools.getInt(holder.get("code"), -1);
            if (code == 0) {
                if (holder.getData().containsKey("data")) {
                    ProSkuResult proSkuResult = (ProSkuResult) holder.get("data");
                    if (CollectionUtils.isNotEmpty(proSkuResult.getProSkuList())) {
                        for (PsCProSkuResult psCProSkuResult : proSkuResult.getProSkuList()) {
                            productSkuList.add(buildProductSku(psCProSkuResult));
                        }
                    }
                }
            }

        } catch (Exception ex) {
            log.error(LogUtil.format(" ProductService.selectProductSku") + Throwables.getStackTraceAsString(ex));
        }
        return productSkuList;
    }


    /**
     * 根据商家外部编码映射
     *
     * @param sku
     * @return
     */
    public List<ProductSku> selectProductSkuByThirdCode(String sku, Long shopId) {
        String redisKey = PsRedisKeyResources.getProductSkuShopKey(sku, shopId);
        List<ProductSku> productSkuList = new ArrayList<>();
        try {
            String skuInfo = redisUtil.strRedisTemplate.opsForValue().get(redisKey);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectProductSkuByThirdCode From Redis Key={}; Sku={};SelectResult={}", redisKey, sku)
                        , redisKey, sku, skuInfo);
            }
            if (StringUtils.isNotEmpty(skuInfo)) {
                ProductSku productSku = JSONObject.parseObject(skuInfo, ProductSku.class);
                productSkuList.add(productSku);
                return productSkuList;
            }
            List<String> skuList = new ArrayList<>();
            skuList.add(sku);
            ProSkuListCmdRequest listCmdRequest = new ProSkuListCmdRequest();
            listCmdRequest.setEcodes(skuList);
            listCmdRequest.setShopId(shopId);
            ValueHolder holder = proSkuListCmd.execute(listCmdRequest);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("selectProductSkuByThirdCode From RPC Sku={};SelectResult={}", sku), sku, holder);
            }
            int code = Tools.getInt(holder.get("code"), -1);
            if (code == 0) {
                if (holder.getData().containsKey("data")) {
                    ProSkuResult proSkuResult = (ProSkuResult) holder.get("data");
                    List<PsCProSkuResult> proSkuList = proSkuResult.getProSkuList();
                    if (CollectionUtils.isNotEmpty(proSkuList)) {
                        if (proSkuList.size() > 1) {
                            //如果返回多条  说明数据不对 放两个空的对象
                            ProductSku productSku1 = new ProductSku();
                            ProductSku productSku2 = new ProductSku();
                            productSkuList.add(productSku1);
                            productSkuList.add(productSku2);
                            return productSkuList;
                        }
                        ProductSku productSku1 =
                                this.buildProductSku(proSkuResult.getProSkuList().get(0));
                        String jsonString = JSONObject.toJSONString(productSku1);
                        redisUtil.strRedisTemplate.opsForValue().set(redisKey, jsonString, 20L, TimeUnit.MINUTES);
                        productSkuList.add(productSku1);
                        return productSkuList;
                    }
                }
            }

        } catch (Exception ex) {
            log.error(LogUtil.format(" selectProductSkuByThirdCode.selectProductSku") + Throwables.getStackTraceAsString(ex));
        }
        return null;
    }


    /**
     * 通过skuid查询商品数据
     *
     * @param skuId 商品SKUID
     * @return 商品数据
     */
    public ProductSku selectProductById(String skuId) {
        String redisKey = PsRedisKeyResources.getProductSkuIdKey(skuId);
        try {
            String skuInfo = redisUtil.strRedisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotEmpty(skuInfo)) {
                ProductSku productSku = JSONObject.parseObject(skuInfo, ProductSku.class);
                return productSku;
            }
            List<Long> skuList = new ArrayList<>();
            skuList.add(NumberUtils.toLong(skuId));
            ProSkuListCmdRequest listCmdRequest = new ProSkuListCmdRequest();
            listCmdRequest.setSkuids(skuList);
            ValueHolder holder = proSkuListCmd.execute(listCmdRequest);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.multiFormat("SelectProductSku From RPC SelectResult={},Sku=", skuList), holder);
            }
            int code = Tools.getInt(holder.get("code"), -1);
            if (code == 0) {
                if (holder.getData().containsKey("data")) {
                    ProSkuResult proSkuResult = (ProSkuResult) holder.get("data");
                    if (CollectionUtils.isNotEmpty(proSkuResult.getProSkuList())) {
                        ProductSku productSku1 =
                                this.buildProductSku(proSkuResult.getProSkuList().get(0));
                        String jsonString = JSONObject.toJSONString(productSku1);
                        redisUtil.strRedisTemplate.opsForValue().set(redisKey, jsonString, 20L, TimeUnit.MINUTES);
                        return productSku1;
                    }
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("ProductService.selectProductById.Error") + Throwables.getStackTraceAsString(ex));
        }
        return null;
    }


    public ValueHolder selectProdSkuInfoBySkuIds(List<Long> skuIds) throws Exception {
        ProSkuListCmdRequest listCmdRequest = new ProSkuListCmdRequest();
        listCmdRequest.setSkuids(skuIds);
        return proSkuListCmd.execute(listCmdRequest);
    }


    /**
     * 通过虚拟条码返回组合商品的信息
     *
     * @param skuList
     * @return
     */
    public PsExtCSkuResult selectGroupProdSkuInfoBySku(List<String> skuList) {
        ValueHolderV14<PsExtCSkuResult> holder = cskuGroupQureyCmd.skuGroupQuery(skuList);
        int code = Tools.getInt(holder.getCode(), -1);
        if (code == 0) {
            PsExtCSkuResult data = holder.getData();
            return data;
        } else {
            return null;
        }
    }

    /**
     * 通过虚拟条码返回组合商品的信息
     *
     * @param ecode
     * @return
     */
    public List<PsCSkugroup> selectGroupProdSkuInfoBySingleSku(String ecode) {
        if (StringUtils.isEmpty(ecode)) {
            return Collections.emptyList();
        }

        ValueHolderV14<List<PsCSkugroup>> holder = cskuGroupQureyCmd.selectBySkuGrpEcode(ecode);
        int code = Tools.getInt(holder.getCode(), -1);
        if (code == 0) {
            return holder.getData();
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 查询明细详情
     *
     * @param pscSkueCode 条码
     * @return
     */
    public Map querySku(String pscSkueCode) {
        SkuQueryRequest skuQueryRequest = new SkuQueryRequest();
        skuQueryRequest.setIsBlur("N");
        PsCSku psCSku = new PsCSku();
        psCSku.setEcode(pscSkueCode);
        skuQueryRequest.setPsCSku(psCSku);
        return skuLikeQueryCmd.querySku(skuQueryRequest);
    }

    /**
     * 查询 sku 明细详情
     *
     * @param skuQueryRequest
     * @return
     */
    public Map querySku(SkuQueryRequest skuQueryRequest) {
        return skuLikeQueryCmd.querySku(skuQueryRequest);
    }

    /**
     * 根据ids批量查询条码信息+颜色规格code name
     * ljp add
     *
     * @param ids
     * @return
     */
    public List<SkuQueryListRequest> querySkuListByIds(List<Integer> ids) {
        try {
            List<SkuQueryListRequest> result = skuLikeQueryCmd.querySkuByIds(ids);
            return result;
        } catch (Exception e) {
            log.error(LogUtil.multiFormat("psRpc异常：{}", ids), Throwables.getStackTraceAsString(e));
            throw new NDSException("PsRpcService.querySkuListByIds:" + e.getMessage());
        }
    }

    /**
     * 根据条码查询出是赠品并且是宣传单的商品
     *
     * @param skuList
     * @return
     */
    public List<String> querySkuList(List<String> skuList) {
        List<String> result;
        try {
            result = skuLikeQueryCmd.querySkuList(skuList);
        } catch (Exception ex) {
            log.error(LogUtil.multiFormat("合单.根据条码查询出是赠品并且是宣传单的商品,异常{}", skuList),
                    Throwables.getStackTraceAsString(ex));
            throw new NDSException("根据条码查询: 是赠品并且是宣传单的商品时出现异常");
        }
        return result;
    }

    /**
     * 查询颜色尺寸
     *
     * @param proId
     * @param ecode
     * @return
     */
    public JSONObject querySkuinfoByProIdAndEcode(Long proId, String ecode) {
        return skuLikeQueryCmd.querySkuinfoByProIdAndEcode(proId, ecode);
    }

    /**
     * 根据国标码查询
     *
     * @param barcodeList
     * @return
     */
    public PsSkuResult selectSkuInfoByBarcode(List<String> barcodeList) {
        if (findSkuType) {
            return selectSkuInfoByeCodes(barcodeList);
        } else {
            try {
                SkuListCmdRequest request = new SkuListCmdRequest();
                request.setGbcodeList(barcodeList);
                ValueHolder valueHolder = skuListCmd.execute(request);
                return (PsSkuResult) valueHolder.get("data");
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error(LogUtil.format("PsRpcService.selectSkuInfoByBarcode") + Throwables.getStackTraceAsString(ex));
                return null;
            }
        }

    }


    public PsSkuResult selectJitxSkuInfo(List<String> barcodeList) {
        if (CollectionUtils.isEmpty(barcodeList)) {
            return null;
        }
        PsSkuResult result = this.selectSkuInfoByforCodes(barcodeList.get(0));
        if (result != null && CollectionUtils.isNotEmpty(result.getProSkus())) {
            return result;
        }
        try {
            SkuListCmdRequest request = new SkuListCmdRequest();
            request.setGbcodeList(barcodeList);
            //通过国际条码查询
            ValueHolder dataByForCode = skuListCmd.findByforCodes(request);
            if (dataByForCode.get("data") != null) {
                if (log.isDebugEnabled()) {
                    log.debug("通过国际条码查询到的数据：{}", JSON.toJSONString(dataByForCode));
                }
                PsSkuResult data = JSON.parseObject(JSON.toJSONString(dataByForCode.get("data")), PsSkuResult.class);
                if (CollectionUtils.isNotEmpty(data.getProSkus())) {
                    return data;
                }
            }
            //通过国标码查询
            ValueHolder dataByGbCode = skuListCmd.execute(request);
            if (dataByGbCode.get("data") != null) {
                if (log.isDebugEnabled()) {
                    log.debug("通过国标码查询到的数据：{}", JSON.toJSONString(dataByGbCode));
                }
                PsSkuResult data = JSON.parseObject(JSON.toJSONString(dataByGbCode.get("data")), PsSkuResult.class);
                if (CollectionUtils.isNotEmpty(data.getProSkus())) {
                    return data;
                }
            }
            //通过条码查询
            ValueHolder dataByEcode = skuListCmd.findByeCodes(request);
            return JSON.parseObject(JSON.toJSONString(dataByEcode.get("data")), PsSkuResult.class);
        } catch (Exception ex) {
            log.error("PsRpcService.selectSkuInfoByBarcode:{}", Throwables.getStackTraceAsString(ex));
            return null;
        }

    }

    /**
     * 根据ecodes查询
     *
     * @param eCodes
     * @return
     */
    public PsSkuResult selectSkuInfoByeCodes(List<String> eCodes) {
        if (CollectionUtils.isEmpty(eCodes)) {
            return null;
        }

        try {
            SkuListCmdRequest request = new SkuListCmdRequest();
            request.setGbcodeList(eCodes);
            ValueHolder valueHolder = skuListCmd.findByeCodes(request);
            return (PsSkuResult) valueHolder.get("data");
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.multiFormat("PsRpcService.selectSkuInfoByeCodes error:{},", eCodes),
                    Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * 优先通过国际条码匹配系统条码，如果没有匹配到，再通过国标码匹配
     *
     * @param eCodes
     * @return
     */
    public PsSkuResult selectSkuInfoByforCodes(String eCodes) {
        String redisKey = PsRedisKeyResources.getProductSkuForCodeKey(eCodes);
        try {
            String skuInfo = redisUtil.strRedisTemplate.opsForValue().get(redisKey);
            if (log.isDebugEnabled()) {
                log.debug("selectSkuInfoByforCodes From Redis Key={}; Sku={};SelectResult={}", redisKey, eCodes, skuInfo);
            }

            if (StringUtils.isNotEmpty(skuInfo)) {
                PsSkuResult redisPsSkuResult = JSONObject.parseObject(skuInfo, PsSkuResult.class);
                return redisPsSkuResult;
            }
            SkuListCmdRequest request = new SkuListCmdRequest();
            List<String> eCodeList = new ArrayList<>();
            eCodeList.add(eCodes);
            request.setGbcodeList(eCodeList);
            ValueHolder valueHolder = skuListCmd.findByforCodes(request);
            PsSkuResult psSkuResult = (PsSkuResult) valueHolder.get("data");
            if (psSkuResult == null || CollectionUtils.isEmpty(psSkuResult.getProSkus())) {
                return null;
            }
            //保存redis
            String jsonString = JSONObject.toJSONString(psSkuResult);
            redisUtil.strRedisTemplate.opsForValue().set(redisKey, jsonString, 20L, TimeUnit.MINUTES);
            return psSkuResult;
        } catch (Exception ex) {
            log.error("PsRpcService.selectSkuInfoByforCodes:{}", Throwables.getStackTraceAsString(ex));
            return null;
        }
    }


    /**
     * 根据虚拟条码查询
     *
     * @param virtualProSplitRequest
     * @return
     */

    public ValueHolderV14<Map<String, List<ExtractLuckyBag>>> selectGroupPro(VirtualProSplitRequest virtualProSplitRequest) {
        ValueHolderV14<Map<String, List<ExtractLuckyBag>>> holderV14 = new ValueHolderV14<>();
        try {
            holderV14 = virtualProSplitCmd.splitVirtualPro(virtualProSplitRequest);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format("通过虚拟条码调用库存中心异常") + Throwables.getStackTraceAsString(e));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage(e.getMessage());
        }
        return holderV14;
    }

    /**
     * 给菜鸟仓库使用
     *
     * @param list 入参每个PsCTowms里边都包含customerId和sku
     * @return PsCTowms.itemId的集合
     */
    public HashMap<String, String> getItemId(List<PsToWmsRequest> list) {
        return basicPsQueryService.getPsToWmsInfo(list);
    }

    public PsCProdimItem selectPsCProdimItem(Long prodimItemId) {
        return cproDimItemQueryCmd.queryPsCProDimItem(prodimItemId);
    }

    public List<PsCProdimItem> selectPsCProdimItemList(List<Long> prodimItemIdList) {
        if (CollectionUtils.isEmpty(prodimItemIdList)) {
            return Collections.emptyList();
        }

        return cproDimItemQueryCmd.queryPsCProDimItem(prodimItemIdList);
    }

    /**
     * 查询商品信息
     *
     * @param proList
     * @return
     */
    public ValueHolder querySkuInfoByProEcodeAndClrsize(List<SkuInfoListRequest> proList) {
        return proSkuListCmd.querySkuInfoByProEcodeAndClrsize(proList);
    }


    /**
     * 根据姓名查询sku
     *
     * @param sex
     * @return
     */
    public ValueHolderV14<PsCProdimItem> queryProdimItem(Long sex) {
        return skuLikeQueryCmd.queryProdimItem(sex);
    }


    /**
     * 退换货信息查询，通过商品编码查询所有条码的颜色尺寸集合，或通过商品编码和尺寸颜色查询条码
     *
     * @param productCode
     * @return
     */
    public JSONObject querySkuInfo(String productCode, User user) {
        JSONObject resultMap = new JSONObject();
        SkuQueryRequest queryRequest = new SkuQueryRequest();
        queryRequest.setIsBlur("N");
        PsCSku psCSku = new PsCSku();
        psCSku.setPsCProEcode(productCode);
        queryRequest.setPsCSku(psCSku);
        queryRequest.setLimit(200);
        Map map = this.querySku(queryRequest);
        if (map != null) {
            String data = JSON.toJSONString(map.get("data"));
            List<JSONObject> jsonArray = JSONArray.parseArray(data, JSONObject.class);
            if (jsonArray != null && jsonArray.size() != 0) {
                //去重
                Set<Long> sizeSet = new HashSet<>();
                Set<Long> colorSet = new HashSet<>();
                List<JSONObject> sizeList = new ArrayList<>();
                List<JSONObject> clrList = new ArrayList<>();
                for (JSONObject psJson : jsonArray) {
                    JSONObject so;
                    JSONObject co;
                    Long sizeId = psJson.getLong("sizeId");
                    if (sizeId != null) {
                        if (sizeSet.add(sizeId)) {
                            so = new JSONObject();
                            so.put("psCSpec2objId", psJson.get("sizeId"));
                            so.put("psCSpec2objName", psJson.get("sizeName"));
                            so.put("psCSpec2objCode", psJson.get("sizeCode"));
                            sizeList.add(so);
                        }
                    }
                    Long colorId = psJson.getLong("colorId");
                    if (colorId != null) {
                        if (colorSet.add(colorId)) {
                            co = new JSONObject();
                            co.put("psCSpec1objId", psJson.get("colorId"));
                            co.put("psCSpec1objName", psJson.get("colorName"));
                            co.put("psCSpec1objCode", psJson.get("colorCode"));
                            clrList.add(co);
                        }
                    }
                }
                resultMap.put("psCSpec1objList", clrList);
                resultMap.put("psCSpec2objList", sizeList);

            }
        }
        return resultMap;
    }


    private ProductSku buildProductSku(PsCProSkuResult skuResult) {
        ProductSku productSku = new ProductSku();
        // PS_C_SKU.ID
        productSku.setId(skuResult.getId());
        // PS_C_PRO.ENAME
        productSku.setName(skuResult.getPsCProEname());
        // PS_C_PRO.ECODE
        productSku.setSku(skuResult.getPsCProEcode());
        // PS_C_SKU.GBCode
        productSku.setBarcode69(skuResult.getGbcode());
        // PS_C_PRO.pricelist
        productSku.setPrice(skuResult.getPricelist());
        // PS_C_SKU.PS_C_SPEC1OBJ_ID
        productSku.setColorId(skuResult.getPsCSpec1objId() == null ? 0L : skuResult.getPsCSpec1objId());
        // PS_C_SPECOBJ.ECODE(spec1objid)
        productSku.setColorCode(skuResult.getClrsEcode());
        // PS_C_SPECOBJ.ENAME(spec1objid)
        productSku.setColorName(skuResult.getClrsEname());
        // PS_C_SPECOBJ.ECODE(spec2objid)
        productSku.setSizeCode(skuResult.getSizesEcode());
        // PS_C_SPECOBJ.ENAME(spec2objid)
        productSku.setSizeName(skuResult.getSizesEname());
        // PS_C_SKU.PS_C_SPEC2OBJ_ID
        productSku.setSizeId(skuResult.getPsCSpec2objId() == null ? 0L : skuResult.getPsCSpec2objId());
        // PS_C_SKU.WareType
        productSku.setSkuType(skuResult.getWareType() == null ? SkuType.NORMAL_PRODUCT : skuResult.getWareType());
        // PS_C_SKU.Weight
        productSku.setWeight(skuResult.getWeight());
        // SkuSpec 描述内容 = 尺寸 + 颜色
        // PS_C_SPECOBJ.ENAME(spec2objid) + PS_C_SPECOBJ.ENAME(spec1objid)
        if (skuResult.getSizesEname() != null && skuResult.getClrsEname() != null) {
            productSku.setSkuSpec(skuResult.getSizesEname() + "," + skuResult.getClrsEname());
        } else {
            productSku.setSkuSpec("");
        }
        // PS_C_PRODIM_ITEM.ECode
        productSku.setMaterialType(skuResult.getMaterieltype());
        // PS_C_SKU.ECode
        productSku.setEcode(skuResult.getSkuEcode());
        // PS_C_PRO.pricelist
        productSku.setPricelist(skuResult.getPricelist());
        // PS_C_PRO.ECODE
        productSku.setProdCode(skuResult.getPsCProEcode());
        //PS_C_PRO.isGroup
        productSku.setIsGroup(skuResult.getIsGroup());
        productSku.setIsGift(skuResult.getIsGift());
        // PS_C_SKU.PS_C_PRO_ID
        productSku.setProdId(skuResult.getPsCProId());
        // PS_C_SKU.ECODE
        productSku.setSkuEcode(skuResult.getSkuEcode());

        // 组合商品数量
        productSku.setNum(skuResult.getNum());
        // PS_C_SKU.ECODE
        productSku.setSkuEcode(skuResult.getSkuEcode());
        //性别
        productSku.setSex(skuResult.getSex());

        productSku.setCreateDate(new Date());
        //是否是虚拟商品
        productSku.setIsVirtual(skuResult.getIsVirtual());
        productSku.setTmallExpandCard(skuResult.getTmallExpandCard());
        productSku.setSkuName(skuResult.getSkuEname());
        productSku.setPsCBrandId(skuResult.getPsCBrandId());
        // 供应类型 0 正常 1.代销轻供 2.寄售轻供
        productSku.setPsCProSupplyType(skuResult.getSupplyType());
        productSku.setForCode(skuResult.getForcode());
        productSku.setBasePriceDown(skuResult.getBasePriceDown());
        productSku.setIsEnableExpiry(skuResult.getIsEnableExpiry());
        productSku.setMDim4Id(skuResult.getMDim4Id());
        productSku.setMDim6Id(skuResult.getMDim6Id());
        productSku.setMDim2Id(skuResult.getMDim2Id());
        productSku.setMDim12Id(skuResult.getMDim12Id());
        Map<String, ProAttributeInfo> proAttributeMap = skuResult.getProAttributeMap();
        if (proAttributeMap != null && !proAttributeMap.isEmpty()) {
            Map<String, OmsProAttributeInfo> proAttributeOmsMap = new HashMap<>(16);
            for (String s : proAttributeMap.keySet()) {
                ProAttributeInfo proAttributeInfo = proAttributeMap.get(s);
                OmsProAttributeInfo info = new OmsProAttributeInfo();
                BeanUtils.copyProperties(proAttributeInfo, info);
                proAttributeOmsMap.put(s, info);
            }
            productSku.setProAttributeMap(proAttributeOmsMap);
        }
        productSku.setWeightUnit(skuResult.getWeightUnit());
        productSku.setIsactive(skuResult.getIsactive());
        return productSku;
    }

    /**
     * <AUTHOR>
     * @Date 14:44 2021/4/29
     * @Description 查询商品档案
     */
    public List<PsCPro> queryProByListIds(List<Integer> list) {
        List<PsCPro> proList = skuLikeQueryCmd.queryProByIds(list);
        if (!CollectionUtils.isEmpty(proList) && proList.size() > 0) {
            return proList;
        }
        return null;
    }

    /**
     * description：批量查询商品信息
     *
     * <AUTHOR>
     * @date 2021/5/13
     */
    public List<PsCPro> queryProByIdList(List<Long> list) {
        List<PsCPro> proList = skuLikeQueryCmd.queryProByIdList(list);
        if (!CollectionUtils.isEmpty(proList) && proList.size() > 0) {
            return proList;
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @Date 14:43 2021/4/29
     * @Description 通过skuid查询对应的sku档案
     */
    public PsCSku getSkuById(Long id) {
        //组装请求bean
        SkuQueryRequest skuQueryRequest = new SkuQueryRequest();
        PsCSku psCSku = new PsCSku();
        psCSku.setId(id);
        skuQueryRequest.setIsBlur("N");
        skuQueryRequest.setPsCSku(psCSku);

        Map<String, Object> map = skuLikeQueryCmd.querySku(skuQueryRequest);
        if (null != map) {
            String data = JSON.toJSONString(map.get("data"));
            JSONArray jsonArray = JSONObject.parseArray(data);
            if (!CollectionUtils.isEmpty(jsonArray)) {
                JSONObject jsonObject = (JSONObject) jsonArray.get(0);
                return JsonUtils.jsonParseClass(jsonObject, PsCSku.class);
            }
        }
        return null;
    }


    public List<PsCValidityDefinition> selectPsCValidityDefinitionByType(Long psCProdimItemId, String type, Long skuId) {
        ValueHolderV14<List<PsCValidityDefinition>> holder = validityDefinitionQueryCmd.selectPsCValidityDefinitionByType(psCProdimItemId, type, skuId);
        if (holder.isOK()) {
            return holder.getData();
        }
        return null;
    }

    public PsCProdimItem selectPsCProDimItemInfo(String ecode) throws Exception {
        return skuLikeQueryCmd.queryDimItemInfo(ecode);
    }

    /**
     * 查询组合商品关联的sku是否允许拆单
     *
     * @param pscSkuIds 条码id
     * @return 允许拆单的条码id
     */

    public ValueHolderV14<Map<Long, Boolean>> querySkuIsSplit(List<Long> pscSkuIds) {
        ValueHolderV14<Map<Long, Boolean>> holderV14 = new ValueHolderV14<>();
        try {
            holderV14 = virtualProSplitCmd.querySkuIsSplit(pscSkuIds);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format("查询组合商品关联的sku是否允许拆单调用商品中心异常") + Throwables.getStackTraceAsString(e));
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage(e.getMessage());
        }
        return holderV14;
    }

    public boolean validate(Long proId, Long dimItemId) {

        String key = OmsRedisKeyResources.buildPsDimIdRedisKey(proId);

        String childKey = String.valueOf(dimItemId);

        String val = (String) RedisOpsUtil.getStrRedisTemplate().opsForHash().get(key, childKey);

        if (val == null) {
            boolean validate = psCProDimQueryCmd.validate(proId, dimItemId);
            RedisOpsUtil.getStrRedisTemplate().opsForHash().put(key, childKey, Boolean.toString(validate));
        }
        return Boolean.TRUE.toString().equals(val);
    }

    /**
     * description:通过店铺sku查询商品映射关系
     *
     * @Author: liuwenjin
     * @Date 2022/9/26 21:08
     */
    public PsCCollShopProMapping queryProMappingByShopIdAndSku(Long cpCShopId, List<Long> skuList) {
        if (log.isDebugEnabled()) {
            log.debug("通过店铺sku查询商品映射关系入参，cpCShopId:{},skuList:{}", cpCShopId, skuList);
        }
        PsCCollShopProMapping shopProMapping = null;
        ValueHolderV14<List<PsCCollShopProMapping>> result = psCCollShopProMappingQueryCmd.queryProMappingByShopIdAndSku(cpCShopId, skuList);
        if (log.isDebugEnabled()) {
            log.debug("通过店铺sku查询商品映射关系出参，queryProMappingByShopIdAndSku:{}", JSON.toJSONString(result));
        }
        if (result.isOK()) {
            List<PsCCollShopProMapping> skuProList = result.getData();
            if (CollectionUtils.isNotEmpty(skuProList)) {
                shopProMapping = skuProList.get(0);
            }
        }
        return shopProMapping;
    }


    /**
     * 根据商品编码查询商品信息以及部分商品属性
     *
     * @param proEcodes
     * @return List<ProExtResult>
     */
    public List<ProExtResult> queryProExtByEcodes(List<String> proEcodes) {
        return skuLikeQueryCmd.queryProExtByEcodes(proEcodes);
    }

    /**
     * 京东映射商品查询skuid
     *
     * @param wareId
     * @return
     */
    public String queryJdProSku(String wareId) {
        if (StringUtils.isBlank(wareId)) {
            return "";
        }
        String redisKey = PsRedisKeyResources.getJdProductSkuShopKey(wareId);
        try {
            String redisSkuId = redisUtil.strRedisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotBlank(redisSkuId)) {
                return redisSkuId;
            }

            ValueHolder holder = proSkuListCmd.queryJdSkuInfoByWareId(wareId);
            int code = Tools.getInt(holder.get("code"), -1);
            if (code != 0) {
                log.warn(LogUtil.format("queryJdProSku failed wareId:{},message:{}", "queryJdProSku"), wareId, holder.get("message"));
                return "";
            }
            String skuId = (String) holder.get("data");
            if (StringUtils.isBlank(skuId)) {
                return "";
            }
            redisUtil.strRedisTemplate.opsForValue().set(redisKey, skuId, 20L, TimeUnit.MINUTES);
            return skuId;
        } catch (Exception e) {
            log.error(LogUtil.format("queryJdProSku error wareId:{}", "queryJdProSku"), wareId, e);
        }
        return "";
    }

    /**
     * 根据商品编码查询商品属性值（提数）
     *
     * @param ecodes
     * @return
     */
    public List<Dim8Result> selectPsCProDim8ItemInfo(List<String> ecodes) {
        if (CollectionUtils.isEmpty(ecodes)) {
            return Lists.newArrayList();
        }

        try {
            List<Dim8Result> dim8Results = skuLikeQueryCmd.queryDim8ItemInfo(ecodes);
            if (CollectionUtils.isEmpty(dim8Results)) {
                return Lists.newArrayList();
            }
            return dim8Results;
        } catch (Exception e) {
            log.error(LogUtil.format("selectPsCProDim8ItemInfo error ecodes:{}", "selectPsCProDim8ItemInfo"), ecodes, e);
        }

        return Lists.newArrayList();
    }

    /**
     * 根据多个商品编码查询商品信息
     *
     * @param skuCodes
     * @return
     */
    public List<PsCProSkuResult> selectProSkuByEcodesWithOutActive(List<String> skuCodes) {
        if (CollectionUtils.isEmpty(skuCodes)) {
            return Lists.newArrayList();
        }
        try {
            ValueHolder valueHolder = proSkuListCmd.selectProSkuByEcodesWithOutActive(skuCodes);
            if (!valueHolder.isOK()) {
                return Lists.newArrayList();
            }
            return (List<PsCProSkuResult>) valueHolder.getData().get("data");
        } catch (Exception e) {
            log.error("selectProSkuByEcodesWithOutActive error skuCodes:{}", skuCodes, e);
            return Lists.newArrayList();
        }
    }

    public List<PsCPro> queryProByEcodes(List<String> skuCodes) {
        if (CollectionUtils.isEmpty(skuCodes)) {
            return Lists.newArrayList();
        }
        try {
            ValueHolderV14<List<PsCPro>> valueHolder = psCProQueryCmd.queryProByEcodes(skuCodes);
            if (!valueHolder.isOK()) {
                return Lists.newArrayList();
            }
            return valueHolder.getData();
        } catch (Exception e) {
            log.error("queryProByEcodes error skuCodes:{}", skuCodes, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 根据物料组编码查询所有商品
     *
     * @param ecode
     * @return
     */
    public List<PsCPro> queryByDim2Ecode(String ecode) {
        if (StringUtils.isBlank(ecode)) {
            return Lists.newArrayList();
        }

        try {
            return skuLikeQueryCmd.queryByDim2Ecode(ecode);
        } catch (Exception e) {
            log.error(LogUtil.format("queryByDim2Ecode.Error.{}", "PsRpcService.queryByDim2Ecode", ecode),
                    Throwables.getStackTraceAsString(e));
        }

        return Lists.newArrayList();
    }

    /**
     * 根据一级分类名称查询
     *
     * @param prodimItemNames
     * @return
     */
    public List<PsCProdimItem> queryProdimItemByNames(List<String> prodimItemNames) {
        if (CollectionUtils.isEmpty(prodimItemNames)) {
            return new ArrayList<>();
        }
        ValueHolderV14<List<PsCProdimItem>> v14 =
                psCProdimItemQueryCmd.queryPsCProdimItemListByNames(prodimItemNames, 4L);
        if (!v14.isOK()) {
            log.error("PsRpcService.queryProdimItemByNames error:{}", v14.getMessage());
            return new ArrayList<>();
        }
        return v14.getData();
    }

    /**
     * 根据四级类目编码查询四级类目信息
     *
     * @param categoryCodes 四级类目编码列表
     * @return 四级类目信息列表
     */
    public List<PsCProdimItem> queryFourthCategoryByCodes(List<String> categoryCodes) {
        if (CollectionUtils.isEmpty(categoryCodes)) {
            return new ArrayList<>();
        }
        ValueHolderV14<List<PsCProdimItem>> v14 =
                psCProdimItemQueryCmd.queryPsCProdimItemListByCodes(categoryCodes, 6L);
        if (!v14.isOK()) {
            log.error("PsRpcService.queryFourthCategoryByCodes error:{}", v14.getMessage());
            return new ArrayList<>();
        }
        return v14.getData();
    }

}
