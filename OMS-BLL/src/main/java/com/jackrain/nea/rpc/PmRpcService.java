package com.jackrain.nea.rpc;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.pm.api.CPromSelectGiftCmd;
import com.jackrain.nea.pm.api.PreExecutionBackCmd;
import com.jackrain.nea.pm.api.PreExecutionCmd;
import com.jackrain.nea.pm.model.request.GiftRequest;
import com.jackrain.nea.pm.model.result.GiftResult;
import com.jackrain.nea.pm.model.result.PreExecutionResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019-07-18 15:51
 * @Version 1.0
 * 促销中心的rpc调用
 */
@Slf4j
@Component
public class PmRpcService {

    @Reference(group = "pm-online", version = "1.0")
    private CPromSelectGiftCmd cPromSelectGiftCmd;

    @Reference(group = "pm-online", version = "1.0")
    private PreExecutionCmd preExecutionCmd;

    @Reference(group = "pm-online", version = "1.0")
    private PreExecutionBackCmd preExecutionBackCmd;


    public ValueHolderV14<List<GiftResult>> selectGift(GiftRequest giftRequest) {
        return cPromSelectGiftCmd.selectGift(giftRequest);
    }

    /**
     * 调用预执行rpc方法
     *
     * @param param
     * @return vh
     */
    public ValueHolder preExecRpc(JSONObject param) {
       return preExecutionCmd.preExec(param);
    }

    /**
     * 调用促销预执行补偿rpc方法
     *
     * @param param
     * @return vh
     */
    public ValueHolder preExecMakeUpRpc(JSONObject param) {
        ValueHolder vh = preExecutionBackCmd.preExec(param);
        return vh;
    }

}
