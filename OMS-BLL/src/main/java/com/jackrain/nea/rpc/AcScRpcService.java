package com.jackrain.nea.rpc;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.data.basic.model.request.SkuInfoQueryRequest;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.data.basic.services.BasicPsQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.ps.api.result.PsCProSkuResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: huang.zaizai
 * @since: 2019-07-27
 * create at : 2019-07-27 11:54
 */
@Component
@Slf4j
public class AcScRpcService {
//
//    @Autowired
//    BasicPsQueryService basicPsQueryService;
//    @Autowired
//    BasicCpQueryService basicCpQueryService;
//
//    @Autowired
//    private CpRpcService cpRpcService;
//
//
//    /**
//     * 分销代销策略
//     * ming.fz add
//     *
//     * @param cpCShopId   店铺id
//     * @param paymentDate 付款时间
//     * @param skuIds      商品id
//     * @return
//     */
//    public ValueHolderV14<List<Long>> selectSkuByDistribution(Long cpCShopId, Date paymentDate, List<Long> skuIds) {
//        ValueHolderV14<List<Long>> result = new ValueHolderV14();
//
//        DistributionQueryRequest request = new DistributionQueryRequest();
//        request.setCpCShopId(cpCShopId);
//        request.setEffectiveDate(paymentDate);
//        request.setSkuIdList(skuIds);
//        try {
//            if (log.isDebugEnabled()) {
//                log.debug("分销代销策略接口入参：" + JSON.toJSONString(request));
//            }
//            result = acDistributionQueryCmd.selectSkuByDistribution(request);
//            if (log.isDebugEnabled()) {
//                log.debug("分销代销策略返回结果：" + JSON.toJSONString(result));
//            }
//        } catch (Exception e) {
//            log.error("调用分销代销策略服务acDistributionQueryCmd.selectSkuByDistribution:异常信息！", e);
//            result.setCode(ResultCode.FAIL);
//            result.setMessage("调用分销代销策略服务acDistributionQueryCmd.selectSkuByDistribution:异常信息！" + e.getMessage());
//        }
//        return result;
//    }
//
//    /**
//     * 线上代销资金占用变动服务
//     * ming.fz add
//     *
//     * @param orderInfos
//     * @param user
//     * @param mapSg               逻辑发货单主表加明细
//     * @param acScConstantsIFType
//     * @return
//     */
//    public ValueHolderV14<OnlineFundOccupyResult> onlineFundOccupy(List<OcBOrderParam> orderInfos, User user,
//                                                                   HashMap<String, HashMap<SgBSend, List<SgBSendItem>>> mapSg,
//                                                                   Integer acScConstantsIFType) {
//        OnlineFundOccupyRequest request = new OnlineFundOccupyRequest();
//
//        //请求单据集合
//        List<OnlineFundOccupyModelRequest> requestList = new ArrayList<>();
//        //请求头表和明细
//        OnlineFundOccupyModelRequest requestOnlineFundOccupyModelRequest = null;
//        //请求明细
//        List<OnlineFundOccupyBaseItemModel> requestItemList = null;
//        for (OcBOrderParam orderInfo : orderInfos) {
//            //入参主表
//            requestOnlineFundOccupyModelRequest = new OnlineFundOccupyModelRequest();
//            requestItemList = new ArrayList<>();
//            OcBOrder ocBOrder = orderInfo.getOcBOrder();
//            List<OcBOrderItem> orderItems = orderInfo.getOrderItemList();
//            String billId = String.valueOf(ocBOrder.getId());
//            String billTypeInt = String.valueOf(SgConstantsIF.BILL_TYPE_RETAIL);
//            String mapKey = billId + "," + billTypeInt;
//            //逻辑发货单主表和明细
//            HashMap<SgBSend, List<SgBSendItem>> sgBSendListHashMap = mapSg.get(mapKey);
//            //一个订单对应一个逻辑发货单
//            for (SgBSend mapKey2 : sgBSendListHashMap.keySet()) {
//                //主表入参
//                setRequestOnlineFundOccupyModelRequest(acScConstantsIFType, ocBOrder, user, requestOnlineFundOccupyModelRequest);
//                List<SgBSendItem> sgBSendItems = sgBSendListHashMap.get(mapKey2);
//                //逻辑发货单明细集合
//                for (SgBSendItem sgBSendItem : sgBSendItems) {
//                    //通过逻辑发货单匹配订单明细
//                    setRequestParam(acScConstantsIFType, ocBOrder, orderItems, sgBSendItem, user, requestItemList);
//                }
//                requestOnlineFundOccupyModelRequest.setItemList(requestItemList);
//            }
//            requestList.add(requestOnlineFundOccupyModelRequest);
//        }
//        request.setLoginUser(user);
//        request.setRequestList(requestList);
//        ValueHolderV14<OnlineFundOccupyResult> result = new ValueHolderV14<>();
//
//        try {
//            if (log.isDebugEnabled()) {
//                log.debug("线上代销资金占用变动服务入参：{}", JSON.toJSONString(request));
//            }
//            result = acFundOnlineSalesOccupyServiceCmd.onlineFundOccupy(request);
//            if (log.isDebugEnabled()) {
//                log.debug("线上代销资金占用变动服务返回结果：" + JSON.toJSONString(result));
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("acFundUpdateServiceCmd.onlineFundOccupy.updateFundFlow:异常信息！", e);
//            result.setCode(ResultCode.FAIL);
//            result.setMessage("acFundUpdateServiceCmd.onlineFundOccupy.updateFundFlow:异常信息！"
//                    + ExceptionUtil.getMessage(e));
//        }
//        return result;
//    }
//
//
//    /**
//     * 接口调用 参数赋值
//     *
//     * @param acScConstantsIFType
//     * @param ocBOrder
//     * @param orderItems
//     * @param sgBSendItem
//     * @param user
//     * @param requestItemList
//     */
//    private void setRequestParam(Integer acScConstantsIFType,
//                                 OcBOrder ocBOrder, List<OcBOrderItem> orderItems, SgBSendItem sgBSendItem, User user,
//                                 List<OnlineFundOccupyBaseItemModel> requestItemList) {
//
//
//        //反审核不需要传明细
//        if (!acScConstantsIFType.equals(AcScConstantsIF.BIll_VARIETY_NODE_NO_AUDIT)) {
//            //订单明细id
//            Long sourceBillItemId = sgBSendItem.getSourceBillItemId();
//            for (OcBOrderItem orderItem : orderItems) {
//                //逻辑发货单匹配对对应的订单明细
//                if (orderItem.getId().equals(sourceBillItemId)) {
//                    OnlineFundOccupyBaseItemModel requestItem = new OnlineFundOccupyBaseItemModel();
//                    //商品编码
//                    requestItem.setPsCProEcode(sgBSendItem.getPsCProEcode());
//
//                    //商品名称
//                    requestItem.setPsCProEname(sgBSendItem.getPsCProEname());
//                    //商品id
//                    requestItem.setPsCProId(sgBSendItem.getPsCProId());
//                    //条码
//                    requestItem.setPsCSkuEcode(sgBSendItem.getPsCSkuEcode());
//                    //条码
//                    requestItem.setPsCSkuId(sgBSendItem.getPsCSkuId());
//                    //快递公司ID
//                    requestItem.setLogisticsId(ocBOrder.getCpCLogisticsId());
//                    //快递公司ecode
//                    requestItem.setLogisticsCode(ocBOrder.getCpCLogisticsEcode());
//                    //物流单号
//                    requestItem.setLogistNumber(ocBOrder.getExpresscode());
//                    //代销运费 可为空
//                    requestItem.setFreight(BigDecimal.ZERO);
//                    //发货逻辑仓编码
//                    requestItem.setStoreEcode(sgBSendItem.getCpCStoreEcode());
//                    //发货逻辑仓id
//                    requestItem.setCpCOrigId(sgBSendItem.getCpCStoreId());
//                    //发货逻辑仓名称
//                    requestItem.setCpCOrigEname(sgBSendItem.getCpCStoreEname());
//                    //全链路 可为空
//                    requestItem.setFullLink(null);
//                    //结算单价 非传
//                    requestItem.setSettlePrice(orderItem.getPriceSettle() == null ? BigDecimal.ZERO : orderItem.getPriceSettle());
//                    //吊牌价
//                    requestItem.setPriceList(
//                            orderItem.getPriceTag() == null ? BigDecimal.ZERO : orderItem.getPriceTag());
//                    //销售数量
//                    requestItem.setSalesQty(sgBSendItem.getQty());
//                    //销售价    //取订单明细结算单价
//                    requestItem.setSalesPrice(orderItem.getPriceSettle() == null ? BigDecimal.ZERO : orderItem.getPriceSettle());
//
//                    //获取商品物料类型
//                    List<Long> skuIds = new ArrayList<>();
//                    Long psCSkuId = orderItem.getPsCSkuId();
//                    skuIds.add(psCSkuId);
//                    SkuInfoQueryRequest reques = new SkuInfoQueryRequest();
//                    reques.setSkuIdList(skuIds);
//                    HashMap<Long, PsCProSkuResult> skuInfo = null;
//                    try {
//                        if (log.isDebugEnabled()) {
//                            log.debug("线上代销资金占用变动服务,物料类型入参：" + JSON.toJSONString(reques));
//                        }
//                        skuInfo = basicPsQueryService.getSkuInfo(reques);
//                        if (log.isDebugEnabled()) {
//                            log.debug("线上代销资金占用变动服务,物料类型出参：" + JSON.toJSONString(skuInfo));
//                        }
//                        PsCProSkuResult productSku = skuInfo.get(psCSkuId);
//                        if (productSku == null) {
//                            throw new NDSException(Resources.getMessage("线上代销资金占用变动服务,物料类型不能为空", user.getLocale()));
//                        }
//                        //物料类型
//                        requestItem.setMaterielType(productSku.getMaterieltype());
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                        log.error("线上代销资金占用变动服务,获取物料类型异常" + e);
//                    }
//                    //订单明细id
//                    requestItem.setId(orderItem.getId());
//                    //订单明细数量
//                    requestItem.setQty(sgBSendItem.getQty().intValue());
//
//                    try {
//                        // sgBSendItem.getCpCStoreId() 去逻辑仓档案（CP_C_STORE）中获取经销商id
//                        long cpCStoreId = sgBSendItem.getCpCStoreId() == null ? -1L : sgBSendItem.getCpCStoreId();
//                        if (log.isDebugEnabled()) {
//                            log.debug("逻辑仓档案入参：" + cpCStoreId);
//                        }
//                        StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
//                        List<Long> ids = new ArrayList<>();
//                        ids.add(cpCStoreId);
//                        storeInfoQueryRequest.setIds(ids);
//                        HashMap<Long, CpCStore> storeInfo = basicCpQueryService.getStoreInfo(storeInfoQueryRequest);
//                        CpCStore cpCStore = storeInfo.get(cpCStoreId);
//                        if (log.isDebugEnabled()) {
//                            log.debug("逻辑仓档案出参：" + JSON.toJSONString(cpCStore));
//                        }
//                        if (cpCStore != null) {
//                            //发货经销商id
//                            requestItem.setCpCCustomerIdSend(cpCStore.getCpCCustomerId());
//                            //发货经销商编码
//                            requestItem.setCustomerEcodeSend(cpCStore.getCpCCustomerEcode());
//                            //发货经销商名称
//                            requestItem.setCpCCustomerEnameSend(cpCStore.getCpCCustomerEname());
//                        }
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                        if (log.isDebugEnabled()) {
//                            log.debug("获取逻辑仓档案异常：" + e);
//                        }
//                    }
//                    requestItemList.add(requestItem);
//                }
//            }
//        }
//
//    }
//
//    /**
//     * 线上资金占用服务主表入参
//     *
//     * @param acScConstantsIFType
//     * @param ocBOrder
//     * @param user
//     */
//    private void setRequestOnlineFundOccupyModelRequest(Integer acScConstantsIFType, OcBOrder ocBOrder, User user,
//                                                        OnlineFundOccupyModelRequest requestOnlineFundOccupyModelRequest) {
//        long shopId = ocBOrder.getCpCShopId() == null ? -1L : ocBOrder.getCpCShopId();
//        if (log.isDebugEnabled()) {
//            log.debug("线上代销资金占用变动服务,下单店铺入参：" + JSON.toJSONString(shopId));
//        }
//        CpShop shop = cpRpcService.selectShopById(shopId);
//        if (log.isDebugEnabled()) {
//            log.debug("线上代销资金占用变动服务,下单店铺出参：" + JSON.toJSONString(shop));
//        }
//        if (shop == null) {
//            throw new NDSException(Resources.getMessage("线上代销资金占用变动服务,下单店铺不能为空", user.getLocale()));
//        }
//        //经销商id 起始节点id
//        requestOnlineFundOccupyModelRequest.setCpCCustomerId(shop.getCpCCustomerId());
//        //经销商编码 起始节点
//        requestOnlineFundOccupyModelRequest.setBeginNodeCode(shop.getCpCCustomerEcode());
//        //经销商名称 起始节点
//        requestOnlineFundOccupyModelRequest.setCpCCustomerEname(shop.getCpCCustomerEname());
//        //queryType3 :经销商 - 发货店仓/收货店仓 类型
//        requestOnlineFundOccupyModelRequest.setQueryEnum(FullLinkQueryEnum.queryType3);
//        //实体仓编码
//        requestOnlineFundOccupyModelRequest.setPhyWarehouseEcode(ocBOrder.getCpCPhyWarehouseEcode());
//        //销售渠道编码 下单店铺ecode
//        requestOnlineFundOccupyModelRequest.setCpCShopEcode(shop.getEcode());
//        //销售渠道id 下单店铺id
//        requestOnlineFundOccupyModelRequest.setCpCShopId(shop.getId());
//        //资金类型
//        requestOnlineFundOccupyModelRequest.setFundType(AcScConstantsIF.FUND_TYPE_02);
//        //单据日期
//        requestOnlineFundOccupyModelRequest.setBillDate(new Date());
//        //来源单据类型
//        requestOnlineFundOccupyModelRequest.setSourceBillType(AcScConstantsIF.FUND_PREOUT_SOURCE_TYPE_RETAIL);
//        //变化节点
//        requestOnlineFundOccupyModelRequest.setVarietyNode(acScConstantsIFType);
//        //订单编号id
//        requestOnlineFundOccupyModelRequest.setSourceBillId(ocBOrder.getId());
//        //来源单据编号
//        requestOnlineFundOccupyModelRequest.setSourceBillBo(ocBOrder.getBillNo());
//        //订单tid 平台单号
//        requestOnlineFundOccupyModelRequest.setCustomize1(ocBOrder.getSourceCode());
//        //下单店仓
//        requestOnlineFundOccupyModelRequest.setCpCDestId(ocBOrder.getCpCStoreId());
//        //下单店仓ecode
//        requestOnlineFundOccupyModelRequest.setCpCDestEcode(ocBOrder.getCpCStoreEcode());
//        //下单店仓Ename
//        requestOnlineFundOccupyModelRequest.setCpCDestEname(ocBOrder.getCpCStoreEname());
//        //出库日期
//        requestOnlineFundOccupyModelRequest.setOutTime(ocBOrder.getScanTime());
//        //渠道类型
//        CpShop cpShop = cpRpcService.selectShopById(shop.getId());
//        requestOnlineFundOccupyModelRequest.setCustomize2(cpShop.getChannelType());
//    }
//
//
//    /**
//     * 【新增代销退货核算单】服务
//     * ming.fz add
//     *
//     * @param ocBReturnOrder
//     * @param ocBReturnOrderItems
//     * @param user
//     * @return
//     */
//    public ValueHolderV14 save(OcBReturnOrder ocBReturnOrder, List<OcBReturnOrderRefund> ocBReturnOrderItems, Map<Long, SgBReceiveItem> storkMap,
//                               User user) {
//        ValueHolderV14 result = new ValueHolderV14();
//        AcScSaleReturnSaveRequest request = new AcScSaleReturnSaveRequest();
//        AcFSaleReturn requestAcFSaleReturn = new AcFSaleReturn();
//        List<AcFSaleReturnItem> requestItems = new ArrayList<>();
//
//        //主表
//        requestAcFSaleReturn.setId(-1L);
//        //单据日期
//        //后置注解上提: 应该获取退单创建时间
//        requestAcFSaleReturn.setBillDate(new Date());
//
//        CpShop shop = cpRpcService.selectShopById(ocBReturnOrder.getCpCShopId());
//        if (log.isDebugEnabled()) {
//            log.debug("线上代销资金占用变动服务,下单店铺出参：{}", JSON.toJSONString(shop));
//        }
//        if (shop == null) {
//            throw new NDSException(Resources.getMessage("线上代销资金占用变动服务,下单店铺不能为空", user.getLocale()));
//        }
//        requestAcFSaleReturn.setCpCOrigId(shop.getCpCStoreId());
//        //发货逻辑仓id
//        requestAcFSaleReturn.setCpCOrigEname(shop.getCpCStoreEname());
//        requestAcFSaleReturn.setCpCOrigEcode(shop.getCpCStoreEcode());
//        //核算类型   1.正常核算 2.线上代销 3.线下代销
//        requestAcFSaleReturn.setAccountType(2);
//        //入库日期
//        requestAcFSaleReturn.setInTime(ocBReturnOrder.getInTime());
//        //来源单号id
//        requestAcFSaleReturn.setSourceBillId(ocBReturnOrder.getId());
//        //来源单号
//        requestAcFSaleReturn.setSourceBillNo(ocBReturnOrder.getOrigSourceCode());
//        //收货经销售
//        requestAcFSaleReturn.setCpCCustomerIdSend(shop.getCpCCustomerId());
//        requestAcFSaleReturn.setCpCCustomerEnameSend(shop.getCpCCustomerEname());
//        requestAcFSaleReturn.setCpCCustomerEcodeSend(shop.getCpCCustomerEcode());
//        requestAcFSaleReturn.setRemark("由零售退换货单" + ocBReturnOrder.getId() + "入库生成");
//        //明细
//        for (OcBReturnOrderRefund ocBReturnOrderItem : ocBReturnOrderItems) {
//            AcFSaleReturnItem requestAcFSaleReturnItem = new AcFSaleReturnItem();
//            requestAcFSaleReturnItem.setId(-1L);
//            requestAcFSaleReturnItem.setAmtCheckPrice(BigDecimal.ZERO);
//            requestAcFSaleReturnItem.setAmtInList(BigDecimal.ZERO);
//            requestAcFSaleReturnItem.setAmtListPrice(BigDecimal.ZERO);
//            requestAcFSaleReturnItem.setAmtOutList(BigDecimal.ZERO);
//            requestAcFSaleReturnItem.setAmtOutPrice(BigDecimal.ZERO);
//            requestAcFSaleReturnItem.setAmtTax(BigDecimal.ZERO);
//            requestAcFSaleReturnItem.setAmtUntaxPrice(BigDecimal.ZERO);
//            requestAcFSaleReturnItem.setAmtInPrice(BigDecimal.ZERO);
//            //条码id
//            requestAcFSaleReturnItem.setPsCSkuId(ocBReturnOrderItem.getPsCSkuId());
//            requestAcFSaleReturnItem.setPsCSkuEcode(ocBReturnOrderItem.getPsCSkuEcode());
//            //审核价 同订单明细中的结算单价
//            requestAcFSaleReturnItem.setAmtCheck(ocBReturnOrderItem.getPriceSettle());
//            //退货价
//            requestAcFSaleReturnItem.setAmt(ocBReturnOrderItem.getAmtRefundSingle());
//            //吊牌价
//            BigDecimal tagPrice = ocBReturnOrderItem.getPriceList() == null ? BigDecimal.ZERO : ocBReturnOrderItem.getPriceList();
//            requestAcFSaleReturnItem.setPriceList(tagPrice);
//            //退货数量
//            BigDecimal qtyRefund = ocBReturnOrderItem.getQtyRefund();
//            requestAcFSaleReturnItem.setQty(qtyRefund);
//            //入库数量
//            requestAcFSaleReturnItem.setQtyIn(ocBReturnOrderItem.getQtyIn());
//            requestItems.add(requestAcFSaleReturnItem);
//            //收货逻辑仓id  调用 获取逻辑收货单明细
//            if (null != storkMap && null != ocBReturnOrderItem.getQtyIn() && ocBReturnOrderItem.getQtyIn().equals(ocBReturnOrderItem.getQtyRefund())) {
//                SgBReceiveItem sgBReceiveItem = storkMap.get(ocBReturnOrderItem.getId());
//                if (sgBReceiveItem != null) {
//                    requestAcFSaleReturn.setCpCDestId(sgBReceiveItem.getCpCStoreId());
//                    requestAcFSaleReturn.setCpCDestEname(sgBReceiveItem.getCpCStoreEname());
//                    requestAcFSaleReturn.setCpCDestEcode(sgBReceiveItem.getCpCStoreEcode());
//                    Long storeId = sgBReceiveItem.getCpCStoreId();
//                    StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
//                    List<Long> ids = new ArrayList<>();
//                    ids.add(storeId);
//                    storeInfoQueryRequest.setIds(ids);
//                    HashMap<Long, CpCStore> storeInfo = null;
//                    try {
//                        storeInfo = basicCpQueryService.getStoreInfo(storeInfoQueryRequest);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                    if (null == storeInfo) {
//                        throw new NDSException(Resources.getMessage("发货经销商获取失败！", user.getLocale()));
//                    }
//                    CpCStore cpCStore = storeInfo.get(storeId);
//                    if (log.isDebugEnabled()) {
//                        log.debug("逻辑仓档案出参：" + JSON.toJSONString(cpCStore));
//                    }
//                    if (cpCStore != null) {
//                        requestAcFSaleReturn.setCpCCustomerId(cpCStore.getCpCCustomerId());
//                        requestAcFSaleReturn.setCpCCustomerEcode(cpCStore.getCpCCustomerEcode());
//                        requestAcFSaleReturn.setCpCCustomerEname(cpCStore.getCpCCustomerEname());
//                    }
//
//                }
//            }
//            BigDecimal amtRefundSingle = ocBReturnOrderItem.getAmtRefundSingle() == null ? BigDecimal.ZERO : ocBReturnOrderItem.getAmtRefundSingle();
//            BigDecimal qty = ocBReturnOrderItem.getQtyIn() == null ? BigDecimal.ZERO : ocBReturnOrderItem.getQtyIn();
//            requestAcFSaleReturnItem.setAmtPrice(amtRefundSingle.multiply(qty));
//        }
//        request.setItems(requestItems);
//        request.setMainData(requestAcFSaleReturn);
//        try {
//            if (log.isDebugEnabled()) {
//                log.debug("新增代销退货核算单接口入参：" + JSON.toJSONString(request));
//            }
//            request.setLoginUser(user);
//            result = acScSaleReturnSaveServiceCmd.save(request);
//            if (log.isDebugEnabled()) {
//                log.debug("新增代销退货核算单返回结果：" + JSON.toJSONString(result));
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("新增代销退货核算单服务acScSaleReturnSaveServiceCmd.save:异常信息！", e);
//            result.setCode(ResultCode.FAIL);
//            result.setMessage("新增代销退货核算单服务acScSaleReturnSaveServiceCmd.save:异常信息！" + e.getMessage());
//        }
//        return result;
//    }
//

}

