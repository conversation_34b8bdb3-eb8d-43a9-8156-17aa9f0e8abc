package com.jackrain.nea.rpc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.api.CcompanyQueryCmd;
import com.jackrain.nea.cp.api.ChruserQueryCmd;
import com.jackrain.nea.cp.api.CstoredimitemQueryCmd;
import com.jackrain.nea.cp.api.GetAllPermissionCmd;
import com.jackrain.nea.cp.api.ReigonQueryCmd;
import com.jackrain.nea.cp.request.RegionQueryCmdRequest;
import com.jackrain.nea.cp.result.CpCSupplier;
import com.jackrain.nea.cpext.api.CpCAnchorArchivesQueryCmd;
import com.jackrain.nea.cpext.api.CpCDistributionOrganizationQueryCmd;
import com.jackrain.nea.cpext.api.CpCPlatformQueryCmd;
import com.jackrain.nea.cpext.api.CpCRegionAliasQueryCmd;
import com.jackrain.nea.cpext.api.CpCRegionAliasSaveCmd;
import com.jackrain.nea.cpext.api.CpCRetailDingtalkMessageMaintenanceMapperQueryCmd;
import com.jackrain.nea.cpext.api.CpCSaleOrganizationQueryCmd;
import com.jackrain.nea.cpext.api.CpCSalesroomQueryByCodesCmd;
import com.jackrain.nea.cpext.api.CpCVipcomWahouseQueryCmd;
import com.jackrain.nea.cpext.api.CpLogisticsSelectServiceCmd;
import com.jackrain.nea.cpext.api.CpPhyWarehouseSelectCmd;
import com.jackrain.nea.cpext.api.CpShopProfileCmd;
import com.jackrain.nea.cpext.api.CpShopQueryCmd;
import com.jackrain.nea.cpext.api.CpStoreQueryCmd;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.api.CshopQueryCmd;
import com.jackrain.nea.cpext.api.GeneralOrganizationCmd;
import com.jackrain.nea.cpext.api.GetPhyWareHouseAndLogisticsInfoCmd;
import com.jackrain.nea.cpext.api.QueryPlatformCmd;
import com.jackrain.nea.cpext.api.QueryStoreListCmd;
import com.jackrain.nea.cpext.api.RegionQueryExtCmd;
import com.jackrain.nea.cpext.api.TOmsvipfulladdressQueryCmd;
import com.jackrain.nea.cpext.api.UserQueryCmd;
import com.jackrain.nea.cpext.model.CpCLogisticsItem;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.RedisKeyConstans;
import com.jackrain.nea.cpext.model.UsersDO;
import com.jackrain.nea.cpext.model.request.CpCRegionAliasCmdRequest;
import com.jackrain.nea.cpext.model.request.CpShopQueryRequest;
import com.jackrain.nea.cpext.model.result.CpCSaleOrganizationQueryResult;
import com.jackrain.nea.cpext.model.result.CpWareHouseQueryResult;
import com.jackrain.nea.cpext.model.table.CpCAnchorArchives;
import com.jackrain.nea.cpext.model.table.CpCDistributionOrganization;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.cpext.model.table.CpCRegion;
import com.jackrain.nea.cpext.model.table.CpCRegionAlias;
import com.jackrain.nea.cpext.model.table.CpCRegionRelation;
import com.jackrain.nea.cpext.model.table.CpCSaleOrganization;
import com.jackrain.nea.cpext.model.table.CpCSalesroom;
import com.jackrain.nea.cpext.model.table.CpCShopItem;
import com.jackrain.nea.cpext.model.table.CpCShopProfile;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpCVipcomWahouse;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpLogisticsItem;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.cpext.model.table.TOmsvipfulladdress;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.IpCTaobaoProductItem;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.RedisKeyConst;
import com.jackrain.nea.resource.StRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.jackrain.nea.cpext.model.RedisKeyConstans.CP_C_SHOP_PROFILE_BY_SHOP_ID;
import static com.jackrain.nea.resource.CpRedisKeyResources.buildArchivesByNameKey;
import static com.jackrain.nea.resource.CpRedisKeyResources.buildCpCStoreKey;
import static com.jackrain.nea.resource.CpRedisKeyResources.buildCpPhyWarehouseRedisKey;
import static com.jackrain.nea.resource.CpRedisKeyResources.buildCpPlatformKey;
import static com.jackrain.nea.resource.CpRedisKeyResources.buildCpRegionById;
import static com.jackrain.nea.resource.CpRedisKeyResources.buildCpStoreByWarehouseId;
import static com.jackrain.nea.resource.CpRedisKeyResources.buildLogicnameListKey;
import static com.jackrain.nea.resource.CpRedisKeyResources.buildLogisticsRedisKey;
import static com.jackrain.nea.resource.CpRedisKeyResources.buildSalesroomKey;
import static com.jackrain.nea.resource.CpRedisKeyResources.buildVipAddressCodeKey;
import static com.jackrain.nea.resource.CpRedisKeyResources.buildWarehouseByEcode;
import static com.jackrain.nea.resource.CpRedisKeyResources.getLogisticsKey;
import static com.jackrain.nea.resource.CpRedisKeyResources.getShopKey;
import static com.jackrain.nea.resource.CpRedisKeyResources.logisticNameKey;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-05-22 10:16
 */
@Component
@Slf4j
public class CpRpcService {

    private static final int CP_REDIS_TIMEOUT = 24 * 60 * 60 * 1000;
    /**
     * EMS 物流公司编码
     */
    public static final String EMS_LOGISTICS_CODE = "EMS";
    @Reference(group = "cp-ext", version = "1.0")
    private GeneralOrganizationCmd generalOrganizationCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private QueryPlatformCmd queryPlatformCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private CpCRegionAliasSaveCmd cpCRegionAliasSaveCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private CpCRegionAliasQueryCmd cpCRegionAliasQueryCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private CpStoreQueryCmd cpStoreQueryCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private CpCPlatformQueryCmd cpCPlatformQueryCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private CpCSalesroomQueryByCodesCmd cpCSalesroomQueryByCodesCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private CpLogisticsSelectServiceCmd cpLogisticsSelectServiceCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private CshopQueryCmd cshopQueryCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private QueryStoreListCmd queryStoreListCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private CpCVipcomWahouseQueryCmd cpCVipcomWahouseQueryCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private RegionQueryExtCmd regionQueryExtCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private CpShopQueryCmd cpShopQueryCmd;
    @Reference(group = "cp-ext", version = "1.0")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;
    @Reference(group = "cp", version = "1.0")
    private ReigonQueryCmd reigonQueryCmd;
    @Reference(group = "cp", version = "1.0")
    private GetAllPermissionCmd getAllPermissionCmd;
    @Reference(group = "cp-ext", version = "1.0")
    CpPhyWarehouseSelectCmd cpPhyWarehouseSelectCmd;
    @Reference(group = "cp-ext", version = "1.0")
    TOmsvipfulladdressQueryCmd tOmsvipfulladdressQueryCmd;
    @DubboReference(group = "cp-ext", version = "1.0")
    private CpCRetailDingtalkMessageMaintenanceMapperQueryCmd cpCRetailDingtalkMessageMaintenanceMapperQueryCmd;

    @Reference(group = "cp-ext", version = "1.0")
    private CpCAnchorArchivesQueryCmd cpCAnchorArchivesQueryCmd;

    @Reference(group = "cp", version = "1.0")
    private CcompanyQueryCmd ccompanyQueryCmd;

    @Reference(group = "cp", version = "1.0")
    private CstoredimitemQueryCmd cstoredimitemQueryCmd;
    @Reference(group = "cp-ext", version = "1.0")
    GetPhyWareHouseAndLogisticsInfoCmd getPhyWareHouseAndLogisticsInfoCmd;

    @Reference(group = "cp", version = "1.0")
    private ChruserQueryCmd chruserQueryCmd;

    @Reference(group = "cp-ext", version = "1.0")
    private UserQueryCmd userQueryCmd;

    @Reference(group = "cp-ext", version = "1.0")
    private CpShopProfileCmd cpShopProfileCmd;

    @Reference(group = "cp-ext", version = "1.0")
    private CpCDistributionOrganizationQueryCmd cpCDistributionOrganizationQueryCmd;

    @Reference(group = "cp-ext", version = "1.0")
    private CpCSaleOrganizationQueryCmd cpCSaleOrganizationQueryCmd;

    @Autowired
    private RedisOpsUtil<String, String> redisCpShopUtil;

    @Autowired
    private RedisOpsUtil<String, CpCPlatform> redisCpPlatformUtil;


    /**
     * 通过逻辑仓编码查询逻辑仓信息
     *
     * @param codes 编码
     * @return 逻辑仓
     */
    public List<CpCStore> selectStoresByCodes(List<String> codes) {

        if (log.isDebugEnabled()) {
            log.debug(" 查询逻辑仓入参：{}", JSON.toJSONString(codes));
        }
        List<CpCStore> result = new ArrayList<>();
        try {
            result = cpStoreQueryCmd.queryStoreByEcodes(codes);
            if (log.isDebugEnabled()) {
                log.debug(" 查询逻辑仓结果：{}", JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error(" 查询逻辑仓异常：{}", Throwables.getStackTraceAsString(e));
        }
        return result;
    }

    public Map<String, CpShop> queryMapByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return MapUtil.empty();
        }

        try {
            ValueHolderV14<List<CpShop>> holderV14 = cpShopQueryCmd.queryByEcodes(codes);
            if (Objects.isNull(holderV14) || !holderV14.isOK()) {
                return MapUtil.empty();
            }
            return ListUtils.emptyIfNull(holderV14.getData()).stream()
                    .collect(Collectors.toMap(CpShop::getEcode, x -> x, (a, b) -> a));
        } catch (Exception e) {
            log.error(" 通过编码查询店铺信息映射异常：{}", Throwables.getStackTraceAsString(e));
        }
        return MapUtil.empty();
    }

    public Map<String, CpCDistributionOrganization> queryOrgMapByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return MapUtil.empty();
        }

        try {
            ValueHolderV14<List<CpCDistributionOrganization>> holderV14 = cpCDistributionOrganizationQueryCmd.queryListByCodes(codes);
            if (Objects.isNull(holderV14) || !holderV14.isOK()) {
                return MapUtil.empty();
            }
            return ListUtils.emptyIfNull(holderV14.getData()).stream()
                    .collect(Collectors.toMap(CpCDistributionOrganization::getEcode, x -> x, (a, b) -> a));
        } catch (Exception e) {
            log.error(" 通过编码查询分货组织信息映射异常：{}", Throwables.getStackTraceAsString(e));
        }
        return MapUtil.empty();
    }

    public Map<String, CpCStore> queryCpCStoreMapByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return MapUtil.empty();
        }

        try {
            ValueHolderV14<List<CpCStore>> holderV14 = cpStoreQueryCmd.queryByEcodes(codes);
            if (Objects.isNull(holderV14) || !holderV14.isOK()) {
                return MapUtil.empty();
            }
            return ListUtils.emptyIfNull(holderV14.getData()).stream()
                    .collect(Collectors.toMap(CpCStore::getEcode, x -> x, (a, b) -> a));
        } catch (Exception e) {
            log.error(" 通过编码查询逻辑仓信息映射异常：{}", Throwables.getStackTraceAsString(e));
        }
        return MapUtil.empty();
    }


    /**
     * 查询 逻辑仓档案
     *
     * @param id 根据逻辑仓的id
     * @return
     */
    public List<CpCStore> selectStoreById(int id) {
        List<CpCStore> cpCStoreList = new ArrayList<>();
        List<Integer> list = new ArrayList<>();
        list.add(id);
        try {
            cpCStoreList = cpStoreQueryCmd.queryStoreInfoByIds(list);
        } catch (Exception e) {
            cpCStoreList = null;
        } finally {
            return cpCStoreList;
        }
    }

    /**
     * 查询实体仓
     *
     * @param phyWarehouseId 实体仓id
     * @return 返回实体仓实体
     */
    public CpCPhyWarehouse selectPhyWarehouseById(Long phyWarehouseId) {
        return cpPhyWarehouseSelectCmd.checkWarehouse(phyWarehouseId);
    }

    /**
     * 查询实体仓
     *
     * @param ecode 实体仓code
     * @return 返回实体仓实体
     */
    public CpCPhyWarehouse selectPhyWarehouseByEcode(String ecode) {
        return cpPhyWarehouseSelectCmd.queryWarehouseByEcode(ecode);
    }


    /**
     * 通过实体仓名称查询实体仓
     */
    public ValueHolderV14<CpCPhyWarehouse> selectPhyWarehouseByEname(String name) {
        return cpPhyWarehouseSelectCmd.queryByName(name);
    }

    /**
     * 查询物流公司档案
     *
     * @param logisticsId 物流公司id
     * @return 返回物流公司档案实体
     */
    public CpLogistics selectLogisticsById(Long logisticsId) {
        return cpLogisticsSelectServiceCmd.checkCpLogistic(logisticsId);
    }

    /**
     * 根据店铺id查询店铺信息
     *
     * @param id
     * @return
     */
    public CpShop selectCpCShopById(Long id) {
        CpShop cpShop;
        String redisKey = String.format("cpext:shop:sessionkey:%s", id);
        try {
            if (log.isDebugEnabled()) {
                //log.debug("CpRpcExtService.selectCpCShopById入参:" + id);
            }
            if (redisCpShopUtil.objRedisTemplate.hasKey(redisKey)) {
                String str = redisCpShopUtil.strRedisTemplate.opsForValue().get(redisKey);
                return JSONObject.parseObject(str, CpShop.class);
            }
            cpShop = generalOrganizationCmd.selectCpCShopById(id);
            if (null == cpShop) {
                return null;
            }
            redisCpShopUtil.strRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(cpShop), 24, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.selectCpCShopById", "selectCpCShopById"), Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.selectCpCShopById:" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.selectCpCShopById返回:{}", "selectCpCShopById"), JSONObject.toJSONString(cpShop));
        }
        return cpShop;
    }

    public List<CpShop> queryShopWithoutActiveByShopIds(List<Long> shopIds) {
        if (CollectionUtils.isEmpty(shopIds)) {
            return new ArrayList<>();
        }
        return cpShopQueryCmd.queryShopParamWithoutActive(shopIds);
    }

    /**
     * 批量查询实体仓信息
     *
     * @return
     */
    public Map<Long, CpCPhyWarehouse> rpcQueryCpCPhyWareHouses(List<Long> warehouseIds) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("批量查询实体仓信息入参:{}", "批量查询实体仓信息"), JSONObject.toJSONString(warehouseIds));
        }
        try {
            if (CollectionUtils.isEmpty(warehouseIds)) {
                return null;
            }
            List<CpCPhyWarehouse> cpCPhyWarehouses = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouses(warehouseIds);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("批量查询实体仓信息出参:{}", "批量查询实体仓信息出参"), JSONObject.toJSONString(cpCPhyWarehouses));
            }
            if (CollectionUtils.isEmpty(cpCPhyWarehouses)) {
                return null;
            }
            Map<Long, CpCPhyWarehouse> phyWareHouseMap = cpCPhyWarehouses.stream()
                    .filter(x -> x != null).collect(Collectors.toMap(CpCPhyWarehouse::getId, o -> o, (x, y) -> y));
            return phyWareHouseMap;
        } catch (Exception ex) {
            log.error(LogUtil.format("查询实体仓信息发生异常{}", "查询实体仓信息发生异常"), Throwables.getStackTraceAsString(ex));
            return null;
        }

    }

    public CpShop selectShopById(Long shopId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ShopService获取店铺信息，店铺id:{}", "ShopService获取店铺信息", shopId), shopId);
        }
        String shopRedisKey = getShopKey(shopId);
        CpShop shopInfo = (CpShop) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(shopRedisKey);
        if (shopInfo == null) {
            shopInfo = generalOrganizationCmd.queryShopById(shopId);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("ShopService获取店铺信息调用RPC获取的数据为:{}", "获取店铺信息调用RPC获取的数据", shopId), JSON.toJSONString(shopInfo));
            }
            if (shopInfo != null) {
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(shopRedisKey, shopInfo);
            }
        }
        return shopInfo;
    }


    /**
     * ljp add
     * 根据省市区id 查询省市区信息
     *
     * @param id
     * @return
     */
    public CpCRegion queryRegionById(Long id) {
        String redisKey = buildCpRegionById(id);
        CusRedisTemplate<String, CpCRegion> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();

        if (objRedisTemplate.hasKey(redisKey)) {
            return objRedisTemplate.opsForValue().get(redisKey);
        }
        CpCRegion cpCRegion = regionQueryExtCmd.queryRegionById(id);
        if (null != cpCRegion) {
            //存放在redis中
            RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, cpCRegion);
        }
        return cpCRegion;
    }


    /**
     * 2019-5-20 19：50 by 孙继东
     * 查询平台店铺对应的实体仓库
     *
     * @param shopId 店铺id
     * @return 实体仓库集合
     */
    public List<CpCPhyWarehouse> queryWarehourseByShopId(Long shopId) {
        return cpcPhyWareHouseQueryCmd.queryWarehourseByShopId(shopId);
    }

    /**
     * 模糊查询平台店铺对应的实体仓库
     *
     * @param shopId 店铺id
     * @param name   实体仓名称
     * @return 实体仓库集合
     */
    public List<CpCPhyWarehouse> queryWarehouseByShopIdAndName(Long shopId, String name) {
        return cpcPhyWareHouseQueryCmd.queryWarehouseByShopIdAndName(shopId, name);
    }

    /**
     * 根据省市区id查询name
     *
     * @param proId    省id
     * @param cityId   市id
     * @param regionId 区id
     * @return 省市区name
     */
    public ValueHolder getRegionNameByid(Long proId, Long cityId, Long regionId) {
        RegionQueryCmdRequest request = new RegionQueryCmdRequest();
        request.setProId(proId);
        request.setCityId(cityId);
        request.setRegionId(regionId);
        return reigonQueryCmd.execute(request);
    }

    public List<CpShop> selectShopByChannalType(String channelType) {
        List<CpShop> shopList = generalOrganizationCmd.searchShopByChannalType(channelType);
        return shopList;
    }

    /**
     * 根据逻辑仓ID查询实体仓信息
     *
     * @return
     */
    public List<Long> rpcQueryO2OCpCPhyWareHouses(List<Long> storeList) {
        // 1.根据逻辑仓ID查询实体仓ID
        try {
            List<CpCPhyWarehouse> cpCPhyWarehouses = getCpCPhyWarehouses(storeList);
            if (cpCPhyWarehouses == null) {
                return null;
            }
            List<Long> list = cpCPhyWarehouses.stream()
                    .filter(x -> Objects.nonNull(x) && StringUtils.equals(x.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE))
                    .map(CpCPhyWarehouse::getId).collect(Collectors.toList());
            return list;
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcService.rpcQueryO2OCpCPhyWareHouses{}", "rpcQueryO2OCpCPhyWareHouses"),
                    Throwables.getStackTraceAsString(e));
            return null;
        }
    }

    private List<CpCPhyWarehouse> getCpCPhyWarehouses(List<Long> storeList) {
        CusRedisTemplate<String, Object> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
        BoundHashOperations<String, String, Long> hashOps = redisTemplate.boundHashOps(RedisKeyConst.CP_LOGICAL_WAREHOUSE_KEY);
        Map<String, Long> entries = hashOps.entries();
        // redis存在的实体仓id
        List<Long> phyWarehouseIds = new ArrayList<>(storeList.size());
        if (entries != null && entries.size() > 0) {
            for (Map.Entry<String, Long> entry : entries.entrySet()) {
                phyWarehouseIds.add(entry.getValue());
                storeList.remove(Long.valueOf(entry.getKey()));
            }
        }
        if (CollectionUtils.isNotEmpty(storeList)) {
            List<CpWareHouseQueryResult> warehouseResult = generalOrganizationCmd.getPhyWarehouseIds(storeList);
            if (CollectionUtils.isNotEmpty(warehouseResult)) {
                for (CpWareHouseQueryResult warehouse : warehouseResult) {
                    phyWarehouseIds.add(warehouse.getCpCPhyWarehouseId());
                    hashOps.put(String.valueOf(warehouse.getId()), warehouse.getCpCPhyWarehouseId());
                    storeList.remove(warehouse.getId());
                }
            }
            for (Long id : storeList) {
                hashOps.put(String.valueOf(id), null);
            }
        }
        if (CollectionUtils.isEmpty(phyWarehouseIds)) {
            return null;
        }
        phyWarehouseIds = phyWarehouseIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        BoundHashOperations<String, String, CpCPhyWarehouse> phyWarehouseHashOps = redisTemplate.boundHashOps(
                RedisKeyConst.CP_PHY_WAREHOUSE_KEY);
        List<CpCPhyWarehouse> cpCPhyWarehouses = new ArrayList<>(phyWarehouseIds.size());
        Map<String, CpCPhyWarehouse> warehouseMap = phyWarehouseHashOps.entries();
        if (warehouseMap != null && warehouseMap.size() > 0) {
            for (Map.Entry<String, CpCPhyWarehouse> entry : warehouseMap.entrySet()) {
                cpCPhyWarehouses.add(entry.getValue());
                phyWarehouseIds.remove(Long.valueOf(entry.getKey()));
            }
        }
        if (CollectionUtils.isNotEmpty(phyWarehouseIds)) {
            List<CpCPhyWarehouse> phys = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouses(phyWarehouseIds);
            if (CollectionUtils.isNotEmpty(phys)) {
                for (CpCPhyWarehouse phy : phys) {
                    cpCPhyWarehouses.add(phy);
                    phyWarehouseHashOps.put(String.valueOf(phy.getId()), phy);
                    phyWarehouseIds.remove(phy.getId());
                }
            }
            for (Long phyWarehouseId : phyWarehouseIds) {
                phyWarehouseHashOps.put(String.valueOf(phyWarehouseId), null);
            }
        }

        if (CollectionUtils.isEmpty(cpCPhyWarehouses)) {
            return null;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("rpcQueryO2OCpCPhyWareHouses,根据逻辑仓查询的实体仓，逻辑仓id={}，实体仓={}", "rpcQueryO2OCpCPhyWareHouses"), JSONObject.toJSONString(storeList), JSONObject.toJSONString(cpCPhyWarehouses));
        }
        return cpCPhyWarehouses;
    }

    /**
     * 通过供应商id查询店铺
     *
     * @param supplierId
     * @date 2020/9/8 下午4:32
     */
    public List<CpShop> queryShopBySupplierId(String supplierId) {
        List<CpShop> shopList = null;
        try {
            shopList = cpShopQueryCmd.queryShopBySupplierId(supplierId);
        } catch (Exception e) {
            log.error(LogUtil.format("通过供应商id查询店铺{}", "通过供应商id查询店铺"), Throwables.getStackTraceAsString(e));
        }
        return shopList;
    }


    /**
     * 获取所有权限
     *
     * @param var1 tableName
     * @param var2 User
     * @return vh
     */
    public ValueHolderV14 getAllPermissionService(String var1, User var2) {
        return getAllPermissionCmd.queryAllPermission(var1, var2);
    }


    /**
     * 根据eocde查询物流公司信息
     *
     * @param ecode
     * @return
     */
    public CpLogistics queryCpLogisticByEcode(String ecode) {
        return cpLogisticsSelectServiceCmd.queryCpLogisticByEcode(ecode);

    }

    /**
     * 获取第一个有效的物流公司
     *
     * @return
     */
    public CpLogistics getTopOne() {
        return cpLogisticsSelectServiceCmd.getTopOne();
    }

    /**
     * 获取物流公司详情
     *
     * @param cpCLogisticsId 物流公司ID
     * @return
     */
    public CpLogistics cpLogisticsInfo(Long cpCLogisticsId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcService.cpLogisticsInfo 查询物流详情入参={}", "cpLogisticsInfo", cpCLogisticsId), cpCLogisticsId);
        }
        CpLogistics cpLogistics = cpLogisticsSelectServiceCmd.checkCpLogistic(cpCLogisticsId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcService.cpLogisticsInfo 物流ID={},查询物流详情出参={}", "cpLogisticsInfo", cpCLogisticsId), cpCLogisticsId, JSON.toJSONString(cpLogistics));
        }
        return cpLogistics;
    }

    /**
     * 查询物流公司档案平台维护的物流编码
     *
     * @param logisticsId 物流公司ID
     * @param platformId  平台ID
     * @return
     */
    public String getPlatformLogisticEcode(Long logisticsId, Long platformId) {
        return cpLogisticsSelectServiceCmd.getPlatformLogisticEcode(logisticsId, platformId);
    }

    /**
     * 查询物流公司档案平台维护的物流编码
     *
     * @param logisticsId 物流公司ID
     * @param platformId  平台ID
     * @return
     */
    public CpLogisticsItem getPlatformLogistic(Long logisticsId, Long platformId) {
        return cpLogisticsSelectServiceCmd.getPlatformLogistic(logisticsId, platformId);
    }

    /**
     * RPC.查询.平台
     *
     * @param sp 值
     * @return 名称
     */
    public String rpcQueryPlatformNameByCode(String sp) {
        try {
            String s = queryPlatformCmd.queryPlatformNameByCode(sp);
            return s == null ? "" : s;
        } catch (Exception e) {
            log.error(LogUtil.format("QueryOrderListService.rpcGetPlatformName{}", "QueryOrderListService" +
                    ".rpcGetPlatformName"), Throwables.getStackTraceAsString(e));
            return "";
        }
    }


    /**
     * 获取实例
     *
     * @return
     */
    public static CpRpcService getInstance() {
        return ApplicationContextHandle.getBean(CpRpcService.class);
    }

    /**
     * 根据实体仓查询逻辑仓
     *
     * @param cphyWarehouseId 逻辑仓集合
     * @return List<Long>
     */
    public List<Long> queryStoreList(Long cphyWarehouseId) {
        List<Long> result;
        String redisKey = buildCpStoreByWarehouseId(cphyWarehouseId);
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("CpRpcExtService.queryStoreList", "queryStoreList入参", cphyWarehouseId));
            }
            CusRedisTemplate<String, List<Long>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            if (objRedisTemplate.hasKey(redisKey)) {
                List<Long> ids = objRedisTemplate.opsForValue().get(redisKey);
                if (CollectionUtils.isNotEmpty(ids)) {
                    return ids;
                }
            }
            result = generalOrganizationCmd.queryStoreList(cphyWarehouseId);
            if (CollectionUtils.isNotEmpty(result)) {
                //存放在redis中
                objRedisTemplate.opsForValue().set(redisKey, result, CP_REDIS_TIMEOUT, TimeUnit.MILLISECONDS);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.queryStoreList{}", "queryStoreList", cphyWarehouseId),
                    Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.queryStoreList:" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.queryStoreList返回:{}", "queryStoreList入参", cphyWarehouseId), JSONObject.toJSONString(result));
        }
        return result;
    }

    /**
     * 获取仓库对象公共方法
     *
     * @param ecode ecode参数
     * @return OcCpCPhyWarehouse
     */

    public CpCPhyWarehouse queryOmsWarehouseJStore(String ecode) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.queryOmsWarehouseJStore", "queryOmsWarehouseJStore", ecode));
        }
        CpCPhyWarehouse cpCPhyWarehouse = null;
        String redisKey = buildWarehouseByEcode(ecode);
        try {
            CusRedisTemplate<String, String> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            Boolean hasRedisKey = objRedisTemplate.hasKey(redisKey);
            if (hasRedisKey != null && hasRedisKey) {
                cpCPhyWarehouse = JSON.parseObject(objRedisTemplate.opsForValue().get(redisKey), CpCPhyWarehouse.class);
                return cpCPhyWarehouse;
            }
            cpCPhyWarehouse = generalOrganizationCmd.queryOmsWarehouseJStore(ecode);
            RedisOpsUtil.getStrRedisTemplate().opsForValue().set(redisKey, JSON.toJSONString(cpCPhyWarehouse), CP_REDIS_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.queryOmsWarehouseJStore.Error{}", "queryOmsWarehouseJStore",
                    ecode), Throwables.getStackTraceAsString(e));
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.queryOmsWarehouseJStore返回:{}", "queryOmsWarehouseJStore返回"), JSON.toJSONString(cpCPhyWarehouse));
        }
        return cpCPhyWarehouse;
    }

    /**
     * 查询所有有效物流公司的简称
     *
     * @return List<String> 所有有效物流公司简称
     */
    public List<String> selectLogisticsKeyList() {
        String redisKey = buildLogicnameListKey();
        List<String> result;
        CusRedisTemplate<String, List<String>> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();

        if (objRedisTemplate.hasKey(redisKey)) {
            return objRedisTemplate.opsForValue().get(redisKey);
        }
        result = generalOrganizationCmd.selectlogitsicsKeyList();
        if (CollectionUtils.isNotEmpty(result)) {
            objRedisTemplate.opsForValue().set(redisKey, result, CP_REDIS_TIMEOUT,
                    TimeUnit.MILLISECONDS);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.selectlogitsicsKeyList返回:{}", "selectlogitsicsKeyList返回"), JSON.toJSONString(result));
        }
        return result;
    }

    /**
     * 查询有效物流公司
     *
     * @param cpClogisticsId 物流公司Id
     * @return OcCpCLogistics
     */
    public CpLogistics queryLogisticsById(Long cpClogisticsId) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.queryLogisticsById入参:{}", "queryLogisticsById入参", cpClogisticsId), cpClogisticsId);
        }
        CpLogistics cpLogistics;
        String redisKey = buildLogisticsRedisKey(cpClogisticsId);

        CusRedisTemplate<String, CpLogistics> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
        if (objRedisTemplate.hasKey(redisKey)) {
            cpLogistics = objRedisTemplate.opsForValue().get(redisKey);
            return cpLogistics;
        }
        cpLogistics = generalOrganizationCmd.queryLogisticsById(cpClogisticsId);
        if (null == cpLogistics) {
            return null;
        }
        //存放在redis中
        objRedisTemplate.opsForValue().set(redisKey, cpLogistics, CP_REDIS_TIMEOUT, TimeUnit.MILLISECONDS);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.queryLogisticsById返回:{}", "queryLogisticsById返回", cpClogisticsId), JSON.toJSONString(cpLogistics));
        }
        return cpLogistics;
    }

    /**
     * @param cpClogisticsId
     * @return
     */
    public CpCLogistics queryLogisticsByIdNew(Long cpClogisticsId) {
        if (log.isDebugEnabled()) {
            log.debug("CpRpcExtService.queryLogisticsById入参:" + cpClogisticsId);
        }
        CpCLogistics cpCLogistics = new CpCLogistics();
        if (null == copyBean(generalOrganizationCmd.queryLogisticsById(cpClogisticsId), cpCLogistics)) {
            return null;
        }
        if (log.isDebugEnabled()) {
            log.debug("CpRpcExtService.queryLogisticsById返回:" + JSON.toJSONString(cpCLogistics));
        }
        return cpCLogistics;
    }

    /**
     * 查询有效物流公司
     *
     * @param ename 物流公司名称
     * @return OcCpCLogistics
     */
    public CpLogistics queryLogisticsByEname(String ename) {
        CpLogistics cpLogistics = generalOrganizationCmd.queryLogisticsByEname(ename);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.queryLogisticsByEname", "CpRpcExtService.queryLogisticsByEname", ename), JSON.toJSONString(cpLogistics));
        }
        return cpLogistics;
    }

    /**
     * 通过简称查询物流公司信息
     *
     * @param shortName 物流公司简称
     * @return OcCpCLogistics
     */
    public CpLogistics queryLogisticsIdByShortName(String shortName) {
        CpLogistics cpLogistics;
        try {
            cpLogistics = generalOrganizationCmd.queryLogisticsIdByShortName(shortName);
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.queryLogisticsIdByShortName{}", "CpRpcExtService" +
                    ".queryLogisticsIdByShortName", shortName), Throwables.getStackTraceAsString(e));
            throw new NDSException(e);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.queryLogisticsIdByShortName", "queryLogisticsIdByShortName", shortName), JSON.toJSONString(cpLogistics));
        }
        return cpLogistics;
    }

    /**
     * 查询是否为京东物流
     *
     * @param ecode 物流ecode
     * @return
     */
    public CpCLogistics queryLogistics(String ecode) {
        CpCLogistics cpCLogistics = new CpCLogistics();
        try {
            if (null == copyBean(generalOrganizationCmd.queryLogistics(ecode), cpCLogistics)) {
                return null;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.queryLogistics异常", "selectStCExpressWarehouseItemInfo", ecode), Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.queryLogistics:" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.queryLogistics返回:{}", "queryLogistics返回", ecode), JSONObject.toJSONString(cpCLogistics));
        }
        return cpCLogistics;
    }

    /**
     * 获取根据仓库ID获取对象
     *
     * @param warehouseId ecode参数
     * @return OcCpCPhyWarehouse
     */
    public CpCPhyWarehouse queryByWarehouseId(Long warehouseId) {
        CpCPhyWarehouse cpCPhyWarehouse = new CpCPhyWarehouse();
        String redisKey = buildCpPhyWarehouseRedisKey(warehouseId);
        try {

            CusRedisTemplate<String, String> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            Boolean hasRedisKey = objRedisTemplate.hasKey(redisKey);
            if (hasRedisKey != null && hasRedisKey) {
                cpCPhyWarehouse = JSON.parseObject(objRedisTemplate.opsForValue().get(redisKey)
                        , CpCPhyWarehouse.class);
                if (cpCPhyWarehouse != null) {
                    return cpCPhyWarehouse;
                }
            }
            if (null == copyBean(generalOrganizationCmd.queryByWarehouseId(warehouseId), cpCPhyWarehouse)) {
                return null;
            }
            //存放在redis中
            objRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(cpCPhyWarehouse),
                    CP_REDIS_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.queryByWarehouseId.Error", "CpRpcExtService.queryByWarehouseId.Error", warehouseId), e);
            throw new NDSException("CpRpcExtService.queryByWarehouseId:" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.queryByWarehouseId.Result={}", "queryByWarehouseId", warehouseId), JSONObject.toJSONString(cpCPhyWarehouse));
        }
        return cpCPhyWarehouse;
    }

    /**
     * 2020/08/28 黄志优 根据门店编码查询门店信息
     *
     * @param code 门店编码
     * @return CpCSalesroom
     */
    public CpCSalesroom selectCpCSalesroomQueryByCode(String code) {
        CpCSalesroom cpCSalesroom = new CpCSalesroom();
        List<String> codes = new ArrayList<>();
        if (StringUtils.isNotBlank(code)) {
            codes.add(code);
        }
        String redisKey = buildSalesroomKey(code);
        try {
            CusRedisTemplate<String, CpCSalesroom> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
            if (objRedisTemplate.hasKey(redisKey)) {
                return objRedisTemplate.opsForValue().get(redisKey);
            }
            List<CpCSalesroom> cpCSalesrooms = cpCSalesroomQueryByCodesCmd.cpCSalesroomQueryByCodes(codes);

            if (CollectionUtils.isEmpty(cpCSalesrooms)
                    || copyBean(cpCSalesrooms.get(0), cpCSalesroom) == null) {
                return null;
            }
            //存放在redis中
            objRedisTemplate.opsForValue().set(redisKey, cpCSalesroom, CP_REDIS_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.selectCpCSalesroomQueryByCode,异常{}", "selectCpCSalesroomQueryByCode", code), Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.selectCpCSalesroomQueryByCode:" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.selectCpCSalesroomQueryByCode返回:{}", "selectCpCSalesroomQueryByCode", code), JSONObject.toJSONString(cpCSalesroom));
        }

        return cpCSalesroom;
    }

    /**
     * 根据逻辑仓ID获取逻辑仓信息
     *
     * @param id
     * @return
     */
    public CpStore selectCpCStoreById(Long id) {
        String redisKey = buildCpCStoreKey(id);
        CpStore cpStore = null;
        try {
            if (RedisOpsUtil.getObjRedisTemplate().hasKey(redisKey)) {
                String cpStoreStr = (String) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(redisKey);
                if (StringUtils.isNotBlank(cpStoreStr)) {
                    cpStore = JSON.parseObject(cpStoreStr, CpStore.class);
                }
            } else {
                cpStore = generalOrganizationCmd.selectCpCStoreById(id);
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, JSON.toJSONString(cpStore),
                        StRedisKeyResources.getCacheTimeConf(), TimeUnit.HOURS);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.selectCpCStoreById,异常{}", "CpRpcExtService.selectCpCStoreById", id), Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.selectCpCStoreById:" + e.getMessage());
        }
        return cpStore;
    }

    /**
     * 根据地址编码查询唯品会对应的省市区
     *
     * @param addressCode
     * @return TOmsvipfulladdress
     */
    public TOmsvipfulladdress queryProvinceCityAreaNameByCode(String addressCode) {
        String redisKey = buildVipAddressCodeKey(addressCode);
        TOmsvipfulladdress tOmsvipfulladdress = null;
        try {
            if (RedisOpsUtil.getObjRedisTemplate().hasKey(redisKey)) {
                String redisValueStr = (String) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(redisKey);
                if (log.isDebugEnabled()) {
                    log.debug("queryProvinceCityAreaNameByCode 地址编码：{},地址编码信息：{}", addressCode, redisValueStr);
                }
                if (StringUtils.isNotBlank(redisValueStr)) {
                    tOmsvipfulladdress = JSON.parseObject(redisValueStr, TOmsvipfulladdress.class);
                }
            } else {
                tOmsvipfulladdress = tOmsvipfulladdressQueryCmd.queryProvinceCityAreaNameByCode(addressCode);
                if (tOmsvipfulladdress != null) {
                    RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, JSON.toJSONString(tOmsvipfulladdress),
                            StRedisKeyResources.getCacheTimeConf(), TimeUnit.HOURS);
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("queryProvinceCityAreaNameByCode,rpc,地址编码信息：{}", "queryProvinceCityAreaNameByCode", addressCode), JSON.toJSONString(tOmsvipfulladdress));
                    }
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.queryProvinceCityAreaNameByCode异常", "queryProvinceCityAreaNameByCode异常", addressCode), Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.queryProvinceCityAreaNameByCode:" + e.getMessage());
        }
        return tOmsvipfulladdress;
    }

    /**
     * 根据省市区name 查询省市区信息
     *
     * @param provinceName
     * @param cityName
     * @param areaName
     * @return
     */
    public CpCRegionRelation selectRegionRelationByProvinceCityArea(String provinceName, String cityName, String areaName) {
        CpCRegionRelation cpCRegionRelation = new CpCRegionRelation();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("CpRpcExtService.selectRegionRelationByProvinceCityArea入参:", "selectRegionRelationByProvinceCityArea入参", provinceName, cityName, areaName), "[provinceName=" + provinceName + "][cityName=" + cityName + "][areaName=" + areaName + "]");
            }
            if (null == copyBean(
                    generalOrganizationCmd.selectRegionRelationByProvinceCityArea(provinceName, cityName, areaName)
                    , cpCRegionRelation)) {
                return null;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.selectRegionRelationByProvinceCityArea异常", "selectRegionRelationByProvinceCityArea异常", provinceName), Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.selectRegionRelationByProvinceCityArea:" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.selectRegionRelationByProvinceCityArea返回:{}", "selectRegionRelationByProvinceCityArea返回", provinceName, cityName, areaName), JSON.toJSONString(cpCRegionRelation));
        }
        return cpCRegionRelation;
    }

    /**
     * regionRelation 表插入
     *
     * @param regionRelation
     * @return
     */
    public int insert(CpCRegionRelation regionRelation) {
        CpCRegionRelation extCpCRegionRelation =
                new CpCRegionRelation();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("CpRpcExtService.insert入参:{}", "CpRpcExtService.insert入参"), JSON.toJSONString(regionRelation));
            }
            if (null == copyBean(regionRelation, extCpCRegionRelation)) {
                return 0;
            }
            return generalOrganizationCmd.omsInsert(extCpCRegionRelation);
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.insert", "CpRpcExtService.insert"), Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.insert:" + e.getMessage());
        }

    }

    /**
     * 根据平台id 和 物流公司id 查询平台物流编码
     *
     * @return CpCLogisticsItem 平台物流对应信息
     */
    public CpCLogisticsItem selectCpCLogisticsEcode(Long platformId, Long logisticsId) {
        CpCLogisticsItem cpCLogisticsItem = new CpCLogisticsItem();
        try {
            if (null == copyBean(generalOrganizationCmd.selectCpCLogisticsEcode(platformId, logisticsId), cpCLogisticsItem)) {
                return null;
            }
        } catch (Exception e) {
            throw new NDSException("CpRpcExtService.selectCpCLogisticsEcode:" + e.getMessage());
        }
        return cpCLogisticsItem;
    }

    public IpCTaobaoProductItem selectIpCTaobaoProductItemBySkuId(String skuId) {
        IpCTaobaoProductItem ipCTaobaoProductItem = new IpCTaobaoProductItem();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("CpRpcExtService.selectIpCTaobaoProductItemBySkuId入参:{}", "selectIpCTaobaoProductItemBySkuId入参", skuId), skuId);
            }
            if (null == copyBean(generalOrganizationCmd.selectIpCTaobaoProductItemBySkuId(skuId), ipCTaobaoProductItem)) {
                return null;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.selectIpCTaobaoProductItemBySkuId", "selectIpCTaobaoProductItemBySkuId"), Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.selectIpCTaobaoProductItemBySkuId:" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.selectIpCTaobaoProductItemBySkuId返回:{}", "selectIpCTaobaoProductItemBySkuId返回", skuId), JSON.toJSONString(ipCTaobaoProductItem));
        }
        return ipCTaobaoProductItem;
    }

    /**
     * 根据逻辑仓聚合实体仓
     *
     * @param storeList 逻辑仓集合
     * @return List<Long>
     */
    public List<Long> queryWareHouseIds(List<Long> storeList) {
        List<Long> list = new ArrayList<>();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("CpRpcExtService.queryWareHouseIds入参:{}", "queryWareHouseIds入参"), JSON.toJSONString(storeList));
            }
            List<CpCPhyWarehouse> cpCPhyWarehouses = getCpCPhyWarehouses(storeList);
            if (CollectionUtils.isEmpty(cpCPhyWarehouses)) {
                return list;
            }
            list = cpCPhyWarehouses.stream().map(CpCPhyWarehouse::getId).collect(Collectors.toList());
        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.queryWareHouseIds", "CpRpcExtService.queryWareHouseIds"), Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.queryWareHouseIds:" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.queryWareHouseIds返回:{}", "queryWareHouseIds返回"), JSON.toJSONString(list));
        }
        return list;
    }

    public <T> T copyBean(T oldBean, T newBean) {
        if (null != oldBean) {
            BeanUtils.copyProperties(oldBean, newBean);
            return newBean;
        }
        return null;
    }

    /***
     * 根据平台的code查询平台的name
     * @param ecode
     * @return
     */


    public String queryPlatformName(String ecode) {
        String platformKey = buildCpPlatformKey(ecode);
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
        String platformName = redisTemplate.opsForValue().get(platformKey);
        if (StringUtils.isNotEmpty(platformName)) {
            return platformName;
        }
        platformName = queryPlatformCmd.queryPlatformNameByCode(ecode);
        return platformName;
    }

    /**
     * 根据省市区的name 查询省市区的id(新)
     *
     * @return
     */
    public ValueHolderV14 selectRegionInfo(String regionName, Integer regiontType, Long parentId) {
        CpCRegionAliasCmdRequest request = new CpCRegionAliasCmdRequest();
        request.setCpCRegionAlias(regionName);
        request.setRegiontype(regiontType);
        request.setCUpId(parentId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("selectRegionInfo.根据省市区的name查询id入参:{}", "根据省市区的name查询id"), JSON.toJSONString(request));
        }
        ValueHolderV14 cpCRegionAlias = cpCRegionAliasQueryCmd.queryRegionAlias(request);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.selectRegion返回:{}", "selectRegion返回"), JSON.toJSONString(cpCRegionAlias));
        }
        return cpCRegionAlias;
    }

    /**
     * 根据省市区的name 查询省市区的id(新)
     * 1 province     2 city
     *
     * @return
     */
    public ValueHolderV14<List<CpCRegionAlias>> selectRegionInfo(String regionName, Integer regiontType) {
        CpCRegionAliasCmdRequest request = new CpCRegionAliasCmdRequest();
        request.setCpCRegionAlias(regionName);
        request.setRegiontype(regiontType);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("selectRegionInfo.根据省市区的name查询id入参:{}", "根据省市区的name查询id"), JSON.toJSONString(request));
        }
        ValueHolderV14<List<CpCRegionAlias>> cpCRegionAlias = cpCRegionAliasQueryCmd.queryRegionAliasWithOutParent(request);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CpRpcExtService.selectRegion返回:{}", "selectRegion返回"), JSON.toJSONString(cpCRegionAlias));
        }
        return cpCRegionAlias;
    }

    /**
     * regionRelation 表插入
     *
     * @return
     */
    public ValueHolderV14 insertCpCRegionAlias(CpCRegionAliasCmdRequest request) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("CpRpcExtService.insert入参d入参:{}", "insertCpCRegionAlias"), JSON.toJSONString(request));
            }
            ValueHolderV14 holderV14 = cpCRegionAliasSaveCmd.saveRegionAlias(request);
            return holderV14;

        } catch (Exception e) {
            log.error(LogUtil.format("CpRpcExtService.insert={}", "CpRpcExtService.insert"),
                    Throwables.getStackTraceAsString(e));
            throw new NDSException("CpRpcExtService.insert:" + e.getMessage());
        }

    }

    /**
     * @param ids
     * @return
     * @Description 查询实体仓id, code和name
     * @date 2019-6-19
     */
    public List<CpCStore> queryStoreInfoByIds(List<Integer> ids) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryStoreInfoByIds.调用门店案店铺rpc接口获取实体仓入参={}", "调用门店案店铺rpc接口获取实体仓"), JSON.toJSONString(ids));
        }
        List<CpCStore> queryStoreInfoByIdsFunc = cpStoreQueryCmd.queryStoreInfoByIds(ids);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryStoreInfoByIds.调用门店案店铺rpc接口获取实体仓出参={}",
                    "调用门店案店铺rpc接口获取实体仓"), JSON.toJSONString(queryStoreInfoByIdsFunc));
        }
        return queryStoreInfoByIdsFunc;
    }

    public List<CpStore> queryListByWarehouseIds(List<Long> ids) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryListByWarehouseIds.根据实体仓{}获取逻辑仓", "调用实体仓获取逻辑仓"), JSON.toJSONString(ids));
        }
        List<CpStore> storeList = queryStoreListCmd.queryListByWarehouseIds(ids);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryListByWarehouseIds.根据实体仓获取逻辑仓={}",
                    "调用实体仓获取逻辑仓"), JSON.toJSONString(storeList));
        }
        return storeList;
    }

    public CpStore queryCpStoreById(Long warehouseId) {
        CpStore cpStore = queryStoreListCmd.quetyCpStoreById(warehouseId);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("quetyCpStoreById.根据实体仓获取逻辑仓={}",
                    "调用实体仓获取逻辑仓"), JSON.toJSONString(cpStore));
        }
        return cpStore;
    }

    /**
     * 按外部系统编码查询逻辑主仓
     *
     * @param code
     * @return
     */
    public CpCStore queryStoreByExtSystemCode(String code) {
        if (Objects.nonNull(code)) {
            return cpStoreQueryCmd.queryStoreByExtSystemCode(code);
        }

        return null;
    }

    public CpCStore queryStoreByEcodes(String code) {
        if (Objects.nonNull(code)) {
            List<CpCStore> cpCStoreList = cpStoreQueryCmd.queryStoreByEcodes(Collections.singletonList(code));
            if (CollectionUtils.isNotEmpty(cpCStoreList)) {
                return cpCStoreList.get(0);
            }
        }

        return null;
    }


    /**
     * 查询平台信息
     *
     * @param ids null ?  查询所有 :  指定id查询
     * @return 平台信息
     */
    public List<CpCPlatform> queryPlatform(List<Long> ids) {

        List<CpCPlatform> resultList = null;
        try {
            if (ids == null) {
                resultList = cpCPlatformQueryCmd.queryCpCPlatforms();
            } else {
                resultList = cpCPlatformQueryCmd.queryCpCPlatforms(ids);
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("查询平台信息异常{}", "queryPlatform"), Throwables.getStackTraceAsString(ex));
        }
        return resultList;
    }

    public CpCPlatform selectCpcPlatformById(Long id) {
        String redisKey = "cp:platform:id:" + id;
        if (redisCpPlatformUtil.objRedisTemplate.hasKey(redisKey)) {
            return redisCpPlatformUtil.objRedisTemplate.opsForValue().get(redisKey);
        }
        List<CpCPlatform> cpCPlatform = cpCPlatformQueryCmd.queryCpCPlatforms(Collections.singletonList(id));
        if (CollectionUtil.isEmpty(cpCPlatform)) {
            return null;
        }
        redisCpPlatformUtil.objRedisTemplate.opsForValue().set(redisKey, cpCPlatform.get(0), CP_REDIS_TIMEOUT, TimeUnit.MILLISECONDS);
        return cpCPlatform.get(0);
    }

    /**
     * 通过天猫仓库编码查找对应逻辑仓
     *
     * @param tmallStoreCode
     * @date 2020/9/8 下午4:30
     */
    public List<CpCStore> queryStoreByTmallStoreCode(String tmallStoreCode) {
        List<CpCStore> storeList = null;
        try {
            storeList = cpStoreQueryCmd.queryStoreByTmallStoreCode(tmallStoreCode);
        } catch (Exception e) {
            log.error(LogUtil.format("通过天猫仓库编码查找对应逻辑仓{}",
                    "通过天猫仓库编码查找对应逻辑仓"), Throwables.getStackTraceAsString(e));
        }
        return storeList;
    }


    /**
     * 根据名称或者code 获取店铺信息
     *
     * @param cpCShopTitleList
     * @return
     */
    public List<CpShop> queryByListByNameOrCode(List<String> cpCShopTitleList) {
        return cshopQueryCmd.queryByListByNameOrCode(cpCShopTitleList).getData();
    }

    /**
     * 根据名称获取店铺信息
     *
     * @param cpCShopTitleList
     * @return
     */
    public List<CpShop> queryByShopTitle(List<String> cpCShopTitleList) {
        return cshopQueryCmd.queryByListTitle(cpCShopTitleList).getData();
    }

    public List<CpShop> queryByShopCodes(List<String> cpCShopCodes) {
        return cshopQueryCmd.queryByListEcode(cpCShopCodes).getData();
    }

    public CpShop queryByShopTitle(String cpCShopTitle) {
        return cshopQueryCmd.queryByTitle(cpCShopTitle).getData();
    }


    /**
     * 获取对应的物流信息
     *
     * @param cLogisticsId
     * @return
     */
    public CpLogistics checkCpLogistic(Long cLogisticsId) {
        return cpLogisticsSelectServiceCmd.checkCpLogistic(cLogisticsId);
    }


    /**
     * 查询平台信息
     *
     * @param ids
     * @return
     */
    public List<CpCPlatform> queryCpCPlatformByIds(List<Long> ids) {
        return cpCPlatformQueryCmd.queryCpCPlatformByIds(ids);
    }

    /**
     * 根据实体仓 - code查询逻辑仓信息
     *
     * @param wmscodes
     * @return
     */
    public ValueHolderV14<List<CpCStore>> queryStoreBySapCodes(List<String> wmscodes) {
        return cpStoreQueryCmd.queryStoreBySapCodes(wmscodes);
    }

    /**
     * 根据实体仓id查询主逻辑仓
     *
     * @param phyId
     * @return
     */
    public CpCStore queryMainStoreByWarehouseId(Long phyId) {
        return cpStoreQueryCmd.queryMainStoreByWarehouseId(phyId);
    }

    /**
     * 通过销售区域（sale_warehouse）查询JIT仓库信息
     *
     * @param saleWarehouse 销售区域
     * @return
     */
    public CpCVipcomWahouse selectByCode(String saleWarehouse) {

        CpCVipcomWahouse warehouse = new CpCVipcomWahouse();

        try {
            if (log.isDebugEnabled()) {
                log.debug(" 根据编码查询JIT仓库入参：{}", saleWarehouse);
            }
            warehouse = cpCVipcomWahouseQueryCmd.selectByCode(saleWarehouse);
            if (log.isDebugEnabled()) {
                log.debug(" 根据编码查询JIT结果：{}", JSON.toJSONString(warehouse));
            }
        } catch (Exception e) {
            log.error(" 根据编码查询JIT仓库异常：{}", Throwables.getStackTraceAsString(e));
        }

        return warehouse;
    }


    /**
     * 查询所有JIT仓库
     *
     * @return
     */
    public List<CpCVipcomWahouse> selectAllVipWarehouse() {

        List<CpCVipcomWahouse> warehouse = new ArrayList<>();

        try {
            warehouse = cpCVipcomWahouseQueryCmd.selectList();
            if (log.isDebugEnabled()) {
                log.debug(" 查询所有JIT仓库结果：{}", JSON.toJSONString(warehouse));
            }
        } catch (Exception e) {
            log.error(" 查询所有JIT仓库异常：{}", Throwables.getStackTraceAsString(e));
        }

        return warehouse;
    }

    /***
     * 根据逻辑仓列表，查询逻辑仓信息
     * @param logicalStoreIdList
     * @return
     */
    public List<CpStore> queryLogicalWarehouse(List<Long> logicalStoreIdList) {
        List<CpStore> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(logicalStoreIdList)) {
            for (Long storeId : logicalStoreIdList) {
                CpStore cpStore = this.selectCpCStoreById(storeId);
                if (cpStore != null) {
                    list.add(cpStore);
                }
            }
        }
        return list;
    }


    /**
     * 获取店铺秘钥中的sessionKey
     *
     * @param cpCShopId 店铺id
     * @return sessionKey
     */
    public String getSessionKey(Long cpCShopId) {
        CpShop cpShop = this.selectShopById(cpCShopId);
        return getSecretKey(cpShop, "sessionkey");
    }

    /**
     * 获取店铺秘钥中的sessionKey
     *
     * @param cpShop
     * @return sessionKey
     */
    public String getUserId(CpShop cpShop) {
        return getSecretKey(cpShop, "userid");
    }

    /**
     * @param cpShop 店铺
     * @param key    店铺密钥key
     * @return 店铺密钥
     */
    private String getSecretKey(CpShop cpShop, String key) {
        String sessionKey;
        if (cpShop == null) {
            return null;
        }
        String shopSecretKey = cpShop.getShopSecretKey();
        if (StringUtils.isBlank(shopSecretKey)) {
            return null;
        }
        String[] split = shopSecretKey.split("\\|");
        String sessionKeyString = null;
        for (String string : split) {
            if (string.contains(key)) {
                sessionKeyString = string;
                break;
            }
        }
        if (sessionKeyString == null) {
            return null;
        }

        int startIndex = sessionKeyString.indexOf(':');
        sessionKey = sessionKeyString.substring(startIndex + 1);
        return sessionKey;
    }

    /**
     * 依据编码查询物流公司
     *
     * @param code 物流编码
     * @return 物流公司信息
     */
    public LogisticsInfo selectLogisticsInfo(String code) {
        String logisticsRedisKey = getLogisticsKey(code.toUpperCase());
        LogisticsInfo logisticsInfo = (LogisticsInfo) RedisOpsUtil.getObjRedisTemplate().opsForValue()
                .get(logisticsRedisKey);
        if (logisticsInfo == null) {
            CpCLogistics dbLogisticsInfo = this.queryLogistics(code);
            if (dbLogisticsInfo != null) {
                logisticsInfo = new LogisticsInfo();
                logisticsInfo.setId(dbLogisticsInfo.getId());
                logisticsInfo.setCode(dbLogisticsInfo.getEcode());
                logisticsInfo.setName(dbLogisticsInfo.getEname());
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(logisticsRedisKey, logisticsInfo);
            }
        }

        return logisticsInfo;
    }

    /**
     * 依据编码查询物流公司
     *
     * @param shortName 物流编码
     * @return 物流公司信息
     */
    public LogisticsInfo selectLogisticsInfoByshortName(String shortName) {
        String logisticsRedisKey = getLogisticsKey("JC" + shortName);
        LogisticsInfo logisticsInfo = (LogisticsInfo) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(logisticsRedisKey);
        if (logisticsInfo == null) {
            CpLogistics cpLogistics = this.queryLogisticsIdByShortName(shortName);
            if (cpLogistics != null) {
                logisticsInfo = new LogisticsInfo();
                logisticsInfo.setId(cpLogistics.getId());
                logisticsInfo.setCode(cpLogistics.getEcode());
                logisticsInfo.setName(cpLogistics.getEname());
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set("JC" + logisticsRedisKey, logisticsInfo);
            }
        }
        return logisticsInfo;
    }

    /**
     * 依据物流名称查询物流公司
     *
     * @param name 物流编码
     * @return 物流公司信息
     */
    public LogisticsInfo selectLogisticsInfoByName(String name) {
        String logisticsRedisKey = getLogisticsKey(logisticNameKey(name));
        LogisticsInfo logisticsInfo = (LogisticsInfo) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(logisticsRedisKey);
        if (logisticsInfo == null) {
            CpLogistics cpLogistics = this.queryLogisticsByEname(name);
            if (cpLogistics != null) {
                logisticsInfo = new LogisticsInfo();
                logisticsInfo.setId(cpLogistics.getId());
                logisticsInfo.setCode(cpLogistics.getEcode());
                logisticsInfo.setName(cpLogistics.getEname());
                RedisOpsUtil.getObjRedisTemplate().opsForValue().set(logisticNameKey(name), logisticsInfo);
            }
        }
        return logisticsInfo;
    }

    /**
     * 依据平台物流编码查询物流公司
     *
     * @param carrierCode 平台物流编码
     * @return 物流公司信息
     */
    public LogisticsInfo selectLogisticsInfoByCarrierCode(String carrierCode) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("CpRpcExtService.queryLogisticsByCarrierCode入参:{}", "queryLogisticsByCarrierCode入参", carrierCode), carrierCode);
            }
            List<CpLogistics> cpLogisticsList = generalOrganizationCmd.queryLogisticsByLogisticsEcode(carrierCode);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("CpRpcExtService.queryLogisticsByCarrierCode出参:{}", "queryLogisticsByCarrierCode出参", carrierCode), JSON.toJSONString(cpLogisticsList));
            }
            if (CollectionUtils.isNotEmpty(cpLogisticsList)) {
                CpLogistics cpLogistics = cpLogisticsList.get(0);
                LogisticsInfo logisticsInfo = new LogisticsInfo();
                logisticsInfo.setId(cpLogistics.getId());
                logisticsInfo.setCode(cpLogistics.getEcode());
                logisticsInfo.setName(cpLogistics.getEname());
                return logisticsInfo;
            }
            return null;
        } catch (Exception ex) {
            log.error(LogUtil.format("LogisticsService.selectLogisticsInfoByCarrierCode error:{}",
                    "selectLogisticsInfoByCarrierCode", carrierCode), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * 查询所有有效物流公司
     *
     * @return 物流公司
     */
    public ValueHolderV14<List<CpLogistics>> queryLogisticsIsY() {

        ValueHolderV14<List<CpLogistics>> valueHolderV14 = new ValueHolderV14<>();
        try {
            valueHolderV14 = cpLogisticsSelectServiceCmd.queryLogisticsIsY();
        } catch (Exception e) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("查询物流公司异常：" + e.getMessage());
        }
        return valueHolderV14;
    }

    /**
     * 根据店铺编码获取店铺信息
     *
     * @param shopCode
     * @return
     */
    public ValueHolderV14<CpShop> queryByShopCode(String shopCode) {
        return cshopQueryCmd.queryByEcode(shopCode);
    }

    public CpCPlatform queryCpCPlatformByCode(String code) {
        CusRedisTemplate<String, Object> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
        BoundHashOperations<String, String, CpCPlatform> hashOperations = redisTemplate.boundHashOps(RedisKeyConst.CP_PLATFORM_KEY);
        List<CpCPlatform> cpCPlatforms = hashOperations.values();
        if (CollectionUtils.isNotEmpty(cpCPlatforms)) {
            Map<String, List<CpCPlatform>> platformMap = cpCPlatforms.stream()
                    .collect(Collectors.groupingBy(CpCPlatform::getEcode));
            if (platformMap.containsKey(code)) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("根据平台编码查询缓存中的平台信息code={}",
                            "queryCpCPlatformByCode", code), code);
                }
                List<CpCPlatform> cPlatformList = platformMap.get(code);
                return cPlatformList.get(0);
            }
        }
        CpCPlatform cpCPlatform = cpCPlatformQueryCmd.queryCpCPlatformByCode(code);
        if (cpCPlatform != null) {
            hashOperations.put(String.valueOf(cpCPlatform.getId()), cpCPlatform);
        }
        return cpCPlatform;
    }

    /**
     * @param sourcePlatformCode
     * @return
     */
    public CpCPlatform queryCpCPlatformBySourcePlatformCode(String sourcePlatformCode) {
        CpCPlatform platform = null;
        try {
            String redisKey = RedisKeyConst.CP_C_SOURCE_PLATFORM_KEY + ":" + sourcePlatformCode;
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
            String redisVal = redisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotBlank(redisVal)) {
                platform = JSON.parseObject(redisVal, CpCPlatform.class);
                return platform;
            }
            ValueHolderV14<CpCPlatform> vh = cpCPlatformQueryCmd.queryCpCPlatformBySystemCode(sourcePlatformCode);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("{}", "queryCpCPlatformBySourcePlatformCode", sourcePlatformCode), JSON.toJSONString(vh));
            }
            if (vh.isOK()) {
                platform = vh.getData();
            }
            if (platform != null) {
                redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(platform), 5, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("queryCpCPlatformBySourcePlatformCode.exp:{}",
                    "queryCpCPlatformBySourcePlatformCode", sourcePlatformCode), Throwables.getStackTraceAsString(e));
        }
        return platform;
    }

    /**
     * @param platId
     * @param logisticsId
     * @return
     */
    public CpCLogisticsItem queryCpPlatformLogisticsInfo(Long platId, Long logisticsId) {
        CpCLogisticsItem logistics = null;
        try {
            String redisKey = RedisKeyConst.CP_C_PLAT_LOGISTICS_KEY + ":" + platId + ":" + logisticsId;
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
            String redisVal = redisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotBlank(redisVal)) {
                logistics = JSON.parseObject(redisVal, CpCLogisticsItem.class);
                return logistics;
            }
            logistics = selectCpCLogisticsEcode(platId, logisticsId);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("{}", "queryCpPlatformLogisticsInfo"), JSON.toJSONString(logistics));
            }
            if (logistics != null) {
                redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(logistics), 5, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("queryCpPlatformLogisticsInfo.exp:{}",
                    "queryCpPlatformLogisticsInfo"), Throwables.getStackTraceAsString(e));
        }
        return logistics;
    }


    public CpCAnchorArchives queryCpCAnchorArchivesByName(String name) {

        if (log.isInfoEnabled()) {
            log.info("queryCpCAnchorArchivesByName 获取主播信息,主播名称={}", name);
        }
        String archivesByNameKey = buildArchivesByNameKey(name);

        CpCAnchorArchives anchorArchives = (CpCAnchorArchives) RedisOpsUtil.getObjRedisTemplate().opsForValue().get(archivesByNameKey);

        if (anchorArchives == null) {
            anchorArchives = cpCAnchorArchivesQueryCmd.queryCpCAnchorArchivesByName(name);
            if (log.isInfoEnabled()) {
                log.info("cpCAnchorArchivesQueryCmd 获取主播信息:{}", JSON.toJSONString(anchorArchives));
            }
            if (anchorArchives == null) {
                anchorArchives = new CpCAnchorArchives();
            }
            RedisOpsUtil.getObjRedisTemplate().opsForValue().set(archivesByNameKey, anchorArchives);
        } else {
            if (log.isInfoEnabled()) {
                log.info("ShopService获取店铺信息从缓存获取的数据为：{}", JSON.toJSONString(anchorArchives));
            }
        }

        return anchorArchives;
    }

    /**
     * 根据编码查询公司档案
     *
     * @param ecode
     * @return
     */
    public CpCSupplier queryCompanyByEcode(String ecode) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("公司档案查询.request={}",
                    "CpRpcService"), ecode);
        }
        try {
            ValueHolderV14<CpCSupplier> v14 = ccompanyQueryCmd.queryCompanyByEcode(ecode);
            if (v14.isOK()) {
                return v14.getData();
            } else {
                throw new NDSException("查询失败！");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("公司档案查询失败={}",
                    "CpRpcService"), Throwables.getStackTraceAsString(e));
            return null;
        }
    }

    /**
     * 根据ID查询公司档案
     *
     * @param id
     * @return
     */
    public CpCSupplier queryCompanyById(Long id) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("公司档案查询.request={}",
                    "CpRpcService"), id);
        }
        try {
            ValueHolderV14<CpCSupplier> v14 = ccompanyQueryCmd.queryCompany(id);
            if (v14.isOK()) {
                return v14.getData();
            } else {
                throw new NDSException("查询失败！");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("公司档案查询失败={}",
                    "CpRpcService"), Throwables.getStackTraceAsString(e));
            return null;
        }
    }

    /**
     * 根据编码查询成本中心、销售部门 id
     *
     * @param columnNameList
     * @return
     */
    public Map<String, JSONObject> findStoredimItemIdByeCodeList(List<String[]> columnNameList) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查询成本中心、销售部门id.request={}",
                    "CpRpcService"), JSON.toJSONString(columnNameList));
        }
        try {
            Map<String, JSONObject> v14 = cstoredimitemQueryCmd.findIdByeCodeList(columnNameList);
            return v14;
        } catch (Exception e) {
            log.error(LogUtil.format("查询成本中心、销售部门id失败={}",
                    "CpRpcService"), Throwables.getStackTraceAsString(e));
            return null;
        }
    }

    /**
     * 根据ID查询店仓属性
     *
     * @param id
     * @return
     */
    public Map<String, Object> findStoredimItemById(Long id) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查询店仓属性.request={}", "CpRpcService.findStoredimItemById"), id);
        }
        try {
            return cstoredimitemQueryCmd.findCstoredImItem(id);
        } catch (Exception e) {
            log.error(LogUtil.format("查询店仓属性失败={}", "CpRpcService.findStoredimItemById"), Throwables.getStackTraceAsString(e));
            return null;
        }
    }

    public List<Map<String, Object>> findItemByAdStorecolumnName(String adStorecolumnName) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查询店仓属性.request={}", "CpRpcService.findItemByAdStorecolumnName"), adStorecolumnName);
        }
        try {
            return cstoredimitemQueryCmd.findItemByAdStorecolumnName(adStorecolumnName);
        } catch (Exception e) {
            log.error(LogUtil.format("查询店仓属性失败={}", "CpRpcService.findItemByAdStorecolumnName"), Throwables.getStackTraceAsString(e));
            return null;
        }
    }

    /**
     * 通过ecode或者ename查询实体仓信息
     *
     * @param session
     * @return
     */
    public ValueHolder queryWarehouseByLike(QuerySession session) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("通过ecode或者ename查询实体仓信息.request={}",
                    "queryWarehouseByLike"), session);
        }
        try {
            ValueHolder v14 = getPhyWareHouseAndLogisticsInfoCmd.queryWarehouseByLike(session);
            return v14;
        } catch (Exception e) {
            log.error(LogUtil.format("通过ecode或者ename查询实体仓信息失败={}",
                    "queryWarehouseByLike"), Throwables.getStackTraceAsString(e));
            return null;
        }
    }

    public CpCShopItem queryShopItem(Long shopId, String organizationCode) {
        CpCShopItem shopItem = null;
        try {
            shopItem = cpShopQueryCmd.queryShopItem(shopId, organizationCode);
        } catch (Exception e) {
            log.error(LogUtil.format("根据店铺ID和销售组织编码查询店铺档案明细成本中心{}", "根据店铺ID和销售组织编码查询店铺档案明细成本中心"), Throwables.getStackTraceAsString(e));
        }
        return shopItem;
    }


    /**
     * 根据唯品会物流公司编码查询物流公司信息
     *
     * @param carrierCode
     * @return
     */
    public LogisticsInfo selectLogisticsInfoByVipCarrierCode(String carrierCode) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("CpRpcExtService.selectLogisticsInfoByVipCarrierCode入参:{}", "CpRpcExtService", carrierCode), carrierCode);
            }
            List<CpLogistics> cpLogisticsList = generalOrganizationCmd.queryLogisticsByVipLogisticsEcode(carrierCode);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("CpRpcExtService.selectLogisticsInfoByVipCarrierCode出参:{}", "CpRpcExtService", carrierCode), JSON.toJSONString(cpLogisticsList));
            }
            if (CollectionUtils.isNotEmpty(cpLogisticsList)) {
                CpLogistics cpLogistics = cpLogisticsList.get(0);
                LogisticsInfo logisticsInfo = new LogisticsInfo();
                logisticsInfo.setId(cpLogistics.getId());
                logisticsInfo.setCode(cpLogistics.getEcode());
                logisticsInfo.setName(cpLogistics.getEname());
                return logisticsInfo;
            }
            return null;
        } catch (Exception ex) {
            log.error(LogUtil.format("CpRpcExtService.selectLogisticsInfoByVipCarrierCode.error:{}",
                    "CpRpcExtService", carrierCode), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * 根据WMS仓库编码查询实体仓
     *
     * @param wmsEcode 实体仓WMS仓库编码
     * @return 返回实体仓实体
     */
    public CpCPhyWarehouse selectPhyWarehouseByWmsEcode(String wmsEcode) {
        return cpPhyWarehouseSelectCmd.queryWarehouseByWmsEcode(wmsEcode);
    }

    /**
     * 根据物流公司id查询（忽略状态）
     *
     * @param ids
     * @return
     */
    public ValueHolderV14<List<CpLogistics>> queryLogisticsByIdsIgnoreStatus(Collection<Long> ids) {
        return cpLogisticsSelectServiceCmd.queryLogisticsByIdsIgnoreStatus(ids);
    }

    /**
     * 分页查询物流公司档案
     *
     * @param page      当前页
     * @param size      每页条数
     * @param selectIds 查询的物流ID
     * @param removeIds 排除的物流ID
     * @return
     */
    public ValueHolderV14<IPage<CpLogistics>> queryLogisticsByPage(Integer page, Integer size, Collection<Long> selectIds, Collection<Long> removeIds) {
        return cpLogisticsSelectServiceCmd.queryLogisticsByPage(page, size, selectIds, removeIds);
    }

    public ValueHolder getByName(String name) {
        return chruserQueryCmd.getByName(name);
    }

    public ValueHolder getByEName(String name) {
        return chruserQueryCmd.getEByName(name);
    }

    public CpShop queryShop(String shopCode) {
        if (StringUtils.isBlank(shopCode)) {
            return null;
        }
        try {
            CpShopQueryRequest shopQueryRequest = new CpShopQueryRequest();
            List<String> shopList = new ArrayList<>();
            shopList.add(shopCode);
            shopQueryRequest.setShopCodes(shopList);
            List<CpShop> cpShops = cpShopQueryCmd.queryShop(shopQueryRequest);
            if (CollectionUtils.isEmpty(cpShops)) {
                return null;
            }
            return cpShops.get(0);
        } catch (Exception e) {
            log.error(LogUtil.format("queryShop error shopCode:{}", "queryShop查询店铺信息异常"), shopCode, Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    public List<CpShop> queryShopByCodeList(List<String> shopCodeList) {
        if (CollectionUtils.isEmpty(shopCodeList)) {
            return new ArrayList<>();
        }
        try {
            CpShopQueryRequest shopQueryRequest = new CpShopQueryRequest();
            shopQueryRequest.setShopCodes(shopCodeList);
            List<CpShop> cpShops = cpShopQueryCmd.queryShop(shopQueryRequest);
            if (CollectionUtils.isEmpty(cpShops)) {
                return new ArrayList<>();
            }
            return cpShops;
        } catch (Exception e) {
            log.error(LogUtil.format("queryShopByCodeList error shopCode:{}", "queryShop查询店铺信息异常"),
                    shopCodeList, Throwables.getStackTraceAsString(e));
        }
        return new ArrayList<>();
    }

    public Long queryDepartmentIdByShopId(Long shopId) {
        if (Objects.isNull(shopId)) {
            return null;
        }
        try {
            ValueHolderV14<Long> holderV14 = cshopQueryCmd.queryDepartmentId(shopId);
            log.debug(LogUtil.format("2B仓辐射，查询销售店铺【组织编码(常规)】对应明细的销售部门，入参：{},返回:{}",
                    "CpRpcService.queryDepartmentIdByShopId"), shopId, holderV14);
            if (Objects.nonNull(holderV14)
                    && holderV14.isOK()
                    && Objects.nonNull(holderV14.getData())) {
                return holderV14.getData();
            }
        } catch (Exception e) {
            log.error(LogUtil.format("2B仓辐射，查询销售店铺【组织编码(常规)】对应明细的销售部门报错，入参：{},异常:{}",
                    "CpRpcService.queryDepartmentIdByShopId"), shopId, Throwables.getStackTraceAsString(e));
        }

        return null;
    }

    /**
     * 根据实体仓ID获取实体仓信息(1分钟过期，防止cp更新缓存不变更，上面的那些hash估计都没用)
     *
     * @param cpcPhyWarehouseId 实体仓ID
     * @return 实体仓对象
     */
    public CpCPhyWarehouse getByCpcPhyWhsId(Long cpcPhyWarehouseId) {
        if (Objects.isNull(cpcPhyWarehouseId)) {
            return null;
        }

        try {
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
            ValueOperations<String, String> opsForValue = redisTemplate.opsForValue();
            String redisValue = opsForValue.get(RedisKeyConst.CP_PHY_WAREHOUSE_ID_KEY + cpcPhyWarehouseId);
            if (StringUtils.isNotBlank(redisValue)) {
                return JSON.parseObject(redisValue, CpCPhyWarehouse.class);
            }

            CpCPhyWarehouse cpCPhyWarehouse = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouseById(cpcPhyWarehouseId);
            if (Objects.isNull(cpCPhyWarehouse)) {
                return null;
            }
            opsForValue.set(RedisKeyConst.CP_PHY_WAREHOUSE_ID_KEY + cpcPhyWarehouseId,
                    JSON.toJSONString(cpCPhyWarehouse), 1, TimeUnit.MINUTES);
            return cpCPhyWarehouse;
        } catch (Exception e) {
            log.error(LogUtil.format("根据实体仓ID获取实体仓信息出错，入参:{},异常:{}",
                    "CpRpcService.getByCpcPhyWhsId"), cpcPhyWarehouseId, Throwables.getStackTraceAsString(e));
        }

        return null;
    }

    public List<CpCShopProfile> queryShopProfileByShopId(Long shopId) {
        String redisKey = CP_C_SHOP_PROFILE_BY_SHOP_ID + shopId;
        String value = redisCpShopUtil.strRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotEmpty(value)) {
            return JSON.parseArray(value, CpCShopProfile.class);
        }

        ValueHolderV14<List<CpCShopProfile>> valueHolderV14 = cpShopProfileCmd.queryCpCShopProfileByShopId(shopId);
        if (valueHolderV14.isOK()) {
            if (CollectionUtils.isEmpty(valueHolderV14.getData())) {
                return null;
            }
            redisCpShopUtil.strRedisTemplate.opsForValue().set(redisKey, JSON.toJSONString(valueHolderV14.getData()), 1, TimeUnit.HOURS);
            return valueHolderV14.getData();
        }
        return null;
    }

    public CpCDistributionOrganization queryDisrtibutionOrgById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        ValueHolderV14<List<CpCDistributionOrganization>> holderV14 = cpCDistributionOrganizationQueryCmd.queryByIds(Collections.singletonList(id));
        if (Objects.isNull(holderV14)
                || !holderV14.isOK() || CollectionUtils.isEmpty(holderV14.getData())) {
            return null;
        }
        return holderV14.getData().get(0);
    }

    /**
     * 根据销售组编码查询【新零售钉钉消息维护】获取用户信息
     *
     * @param groupCode 销售组编码
     * @return 用户信息
     */
    public List<UsersDO> queryUserListByGroupCode(String groupCode) {
        if (StringUtils.isEmpty(groupCode)) {
            return new ArrayList<>();
        }
        String redisKey = RedisKeyConstans.CP_C_RETAIL_DINGTALK_MESSAGE_MAINTENANCE + groupCode;
        try {
            CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getObjRedisTemplate();
            ValueOperations<String, String> opsForValue = redisTemplate.opsForValue();
            String redisValue = opsForValue.get(redisKey);
            if (StringUtils.isNotBlank(redisValue)) {
                return JSONObject.parseArray(redisValue, UsersDO.class);
            }
            ValueHolderV14<List<UsersDO>> v14 = cpCRetailDingtalkMessageMaintenanceMapperQueryCmd.queryByGroupCode(groupCode);
            if (!v14.isOK()) {
                throw new NDSException(v14.getMessage());
            }
            List<UsersDO> userList = v14.getData();
            if (CollectionUtils.isNotEmpty(userList)) {
                opsForValue.set(redisKey,
                        JSON.toJSONString(userList), 30, TimeUnit.MINUTES);
            }
            return userList;
        } catch (Exception e) {
            log.error(LogUtil.format("根据销售组编码查询【新零售钉钉消息维护】获取用户信息出错，入参:{},异常:{}",
                    "CpRpcService.queryUserListByGroupCode"), groupCode, Throwables.getStackTraceAsString(e));
        }

        return null;
    }

    public List<UsersDO> queryUserByNames(List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return new ArrayList<>();
        }
        ValueHolderV14<List<UsersDO>> v14 = userQueryCmd.queryUserByNames(names);
        if (!v14.isOK()) {
            log.error("CpRpcService.queryUserByNames error:{}", v14.getMessage());
            return new ArrayList<>();
        }
        return v14.getData();
    }

    /**
     * 根据销售中心ID查询销售组织架构
     *
     * @param centerSet 销售中心ID集合
     * @return 销售组织架构集合
     */
    public List<CpCSaleOrganization> querySalesOrgBySalesCenter(Collection<Long> centerSet) {
        if (CollectionUtils.isEmpty(centerSet)) {
            return Collections.emptyList();
        }

        try {
            ValueHolderV14<List<CpCSaleOrganization>> holderV14 = cpCSaleOrganizationQueryCmd.queryBySalesCenterIds(centerSet);
            if (Objects.nonNull(holderV14)
                    && holderV14.isOK()
                    && Objects.nonNull(holderV14.getData())) {
                return holderV14.getData();
            }
        } catch (Exception e) {
            log.error(LogUtil.format("根据销售中心ID查询销售组织架构报错，入参：{},异常:{}",
                    "CpRpcService.querySalesOrgBySalesCenter"), centerSet, Throwables.getStackTraceAsString(e));
            throw e;
        }
        return Collections.emptyList();
    }

    /**
     * 根据【商品零级+部门编码】查询销售组织架构
     * 详见：com.burgeon.r3.sg.basic.rpc.RpcCpService#querySaleOrganizationByDepartmentCode
     *
     * @param mDim12Code
     * @param salesDepartmentCode
     * @return
     */
    public List<CpCSaleOrganizationQueryResult> querySaleOrganizationByDepartmentCode(String mDim12Code, String salesDepartmentCode) {
        CusRedisTemplate<String, String> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        String redisKey = RedisKeyConstans.CP_SALES_ORGANIZATION_BY_DEPART_CODE + mDim12Code + ":" + salesDepartmentCode;
        String resultStr = strRedisTemplate.opsForValue().get(redisKey);
        List<CpCSaleOrganizationQueryResult> resultList;
        if (StringUtils.isNotEmpty(resultStr)) {
            log.info(LogUtil.format("CpRpcService.querySaleOrganizationByDepartmentCode redisResult:{}",
                    "CpRpcService.querySaleOrganizationByDepartmentCode"), resultStr);
            resultList = JSONObject.parseArray(resultStr, CpCSaleOrganizationQueryResult.class);
            return resultList;
        }

        ValueHolderV14<List<CpCSaleOrganizationQueryResult>> holderV14 =
                cpCSaleOrganizationQueryCmd.queryByCategoryCodeDepartmentCode(mDim12Code, salesDepartmentCode);
        log.info(LogUtil.format("CpRpcService.querySaleOrganizationByDepartmentCode holderV14:{}",
                "CpRpcService.querySaleOrganizationByDepartmentCode"), JSONObject.toJSONString(holderV14));
        if (!holderV14.isOK()) {
            throw new NDSException(holderV14.getMessage());
        }
        resultList = holderV14.getData();
        strRedisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(resultList), 1, TimeUnit.DAYS);
        return resultList;
    }


    public List<CpCPhyWarehouse> queryWarehouseByNameList(List<String> nameList) {
        if (CollectionUtils.isEmpty(nameList)) {
            return new ArrayList<>();
        }
        return cpcPhyWareHouseQueryCmd.queryWarehouseByEnames(nameList);
    }

    public List<CpCRegion> queryAllRegion() {
        return regionQueryExtCmd.queryAllRegion(Lists.newArrayList("PROV", "CITY", "DIST"));
    }

    public List<CpLogistics> queryLogisticsListByCodeIgnoreActive(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return new ArrayList<>();
        }
        ValueHolderV14<List<CpLogistics>> v14 =
                cpLogisticsSelectServiceCmd.queryLogisticsListByCodeIgnoreActive(codeList);
        if (!v14.isOK()) {
            return new ArrayList<>();
        }
        return v14.getData();
    }
}
