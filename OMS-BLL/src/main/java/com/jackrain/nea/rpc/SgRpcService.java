package com.jackrain.nea.rpc;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.r3.sg.basic.api.SgCTobStrategyQueryCmd;
import com.burgeon.r3.sg.basic.api.SgGroupStorageQueryCmd;
import com.burgeon.r3.sg.basic.api.SgStorageQueryCmd;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.basic.model.request.SgCTobStrategyQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgFreezeStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgGroupStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageQueryRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageRedisQueryLsRequest;
import com.burgeon.r3.sg.basic.model.request.SgStorageRedisQueryProduceRequest;
import com.burgeon.r3.sg.basic.model.result.SgGroupStorageQueryResult;
import com.burgeon.r3.sg.basic.model.result.SgStorageRedisQueryApiResult;
import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.burgeon.r3.sg.basic.model.result.vo.SgFreezeStorageQueryResult;
import com.burgeon.r3.sg.channel.api.sale.SgBChannelAdvanceSaleCmd;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryRequest;
import com.burgeon.r3.sg.channel.model.request.sale.SgBChannelAdvanceSaleBillReleaseRequest;
import com.burgeon.r3.sg.channel.model.result.sale.SgBChannelAdvanceSaleReleaseResult;
import com.burgeon.r3.sg.core.model.ext.SgBStorageInclShare;
import com.burgeon.r3.sg.core.model.table.basic.SgBStorage;
import com.burgeon.r3.sg.core.model.table.basic.SgCTobStrategy;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutNotices;
import com.burgeon.r3.sg.inf.api.oms.SgOmsOffsetAdjustCmd;
import com.burgeon.r3.sg.inf.api.oms.SgOmsReleaseOutCmd;
import com.burgeon.r3.sg.inf.api.oms.SgOmsShareOutVoidCmd;
import com.burgeon.r3.sg.inf.api.oms.SgOmsStandardCmd;
import com.burgeon.r3.sg.inf.api.oms.SgOmsStoAdjustCmd;
import com.burgeon.r3.sg.inf.api.oms.SgOmsStoInCmd;
import com.burgeon.r3.sg.inf.api.oms.SgOmsStoInNoticeAndResultCmd;
import com.burgeon.r3.sg.inf.api.oms.SgOmsStoTranslationCmd;
import com.burgeon.r3.sg.inf.api.oms.freeze.SgOmsFreezeOutCmd;
import com.burgeon.r3.sg.inf.api.oms.product.SgChannelProductQueryCmd;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsPhyStorageOutRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsReleaseOutRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShopShareAndPhyQueryRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShopStorageQueryRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoAdjustSaveRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInNoticeQueryRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoTranslationRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsShopPhyQueryResult;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsShopStorageQueryResult;
import com.burgeon.r3.sg.inf.model.result.oms.SgOmsStoInNoticeQueryResult;
import com.burgeon.r3.sg.share.api.out.SgBShareOutCmd;
import com.burgeon.r3.sg.share.api.translation.SgBStoStockTranslationCmd;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutBillVoidRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutQueryShareStoreRequest;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutSaveRequest;
import com.burgeon.r3.sg.share.model.request.translation.SgBStoStockTranslationRequest;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutQueryShareStoreResult;
import com.burgeon.r3.sg.sourcing.api.SgFindSource2BCmd;
import com.burgeon.r3.sg.sourcing.api.SgOmsSplitOrderCmd;
import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategy2BRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgOmsSplitOrderRequest;
import com.burgeon.r3.sg.store.api.freeze.SgBStoFreezeAndSubmitVipCmd;
import com.burgeon.r3.sg.store.api.in.SgBStoInNoticesCmd;
import com.burgeon.r3.sg.store.api.out.SgBStoOutCmd;
import com.burgeon.r3.sg.store.api.out.SgBStoOutNoticesCmd;
import com.burgeon.r3.sg.store.api.out.SgBStoOutNoticesQueryByNoCmd;
import com.burgeon.r3.sg.store.api.tms.SgLogisticsTrackCmd;
import com.burgeon.r3.sg.store.api.transfer.SgBStoTransferSaveAndSubmitCmd;
import com.burgeon.r3.sg.store.model.request.freeze.out.SgBStoFreezeOutOmsSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.out.SgBStoFreezeOutRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInNoticesBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInNoticesBillVoidRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutNoticesBillVoidRequest;
import com.burgeon.r3.sg.store.model.request.out.SgBStoOutNoticesPosSaveRequest;
import com.burgeon.r3.sg.store.model.request.tms.OrderTrackBackRequest;
import com.burgeon.r3.sg.store.model.request.transfer.SgBStoTransferBillSaveRequest;
import com.burgeon.r3.sg.store.model.result.freeze.out.SgBStoFreezeOutQueryResult;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInNoticesBillSaveResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutNoticesBillVoidResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutQueryResult;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultSendMsgResult;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ipcs.model.LabelRequirementsCancelRequest;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.constant.SgFromSourceBillTypeConstants;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOccupyRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OrderCancleWmsService;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.model.request.StStockPriorityRequest;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsQueryWareHouseService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.util.OrderUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @author: 孙勇生
 * @since: 2019-04-28
 * 库存中心提供的RPC接口
 */
@Component
@Slf4j
public class SgRpcService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OrderCancleWmsService orderCancleWmsService;
    @Reference(group = "sg", version = "1.0")
    private SgStorageQueryCmd sgStorageQueryCmd;
    @Autowired
    private OmsSyncStockStrategyService syncStockStrategyService;
    @Autowired
    private OmsQueryWareHouseService omsQueryWareHouseService;
    @Autowired
    private OmsOrderService omsOrderService;

    @Reference(group = "sg", version = "1.0")
    private SgOmsStoInCmd sgOmsStoInCmd;

    @Reference(group = "sg", version = "1.0")
    private SgOmsStoAdjustCmd sgOmsStoAdjustCmd;


    @Reference(group = "sg", version = "1.0")
    private SgBStoOutNoticesCmd sgBStoOutNoticesCmd;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Reference(group = "sg", version = "1.0")
    private SgGroupStorageQueryCmd sgGroupStorageQueryCmd;

    @Reference(group = "sg", version = "1.0")
    private SgOmsSplitOrderCmd sgOmsSplitOrderCmd;

    @Autowired
    private CpRpcService cpRpcService;

    @Reference(group = "sg", version = "1.0")
    private SgBChannelAdvanceSaleCmd sgBChannelAdvanceSaleCmd;

    @Reference(group = "sg", version = "1.0")
    private SgBStoTransferSaveAndSubmitCmd transferSaveAndSubmitCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgOmsStandardCmd sgOmsStandardCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgOmsReleaseOutCmd sgOmsReleaseOutCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgOmsShareOutVoidCmd sgOmsShareOutVoidCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgOmsStoTranslationCmd SgOmsStoTranslationCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgBShareOutCmd sgBShareOutCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgBStoStockTranslationCmd sgBStoStockTranslationCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgBStoOutCmd sgBStoOutCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgOmsFreezeOutCmd sgOmsFreezeOutCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgBStoFreezeAndSubmitVipCmd sgBStoFreezeAndSubmitVipCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgBStoOutNoticesQueryByNoCmd sgBStoOutNoticesQueryByNoCmd;


    @DubboReference(group = "sg", version = "1.0")
    private SgLogisticsTrackCmd logisticsTrackCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgBStoInNoticesCmd sgBStoInNoticesCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgCTobStrategyQueryCmd sgCTobStrategyQueryCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgOmsOffsetAdjustCmd sgOmsOffsetAdjustCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgFindSource2BCmd findSource2BCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgChannelProductQueryCmd sgChannelProductQueryCmd;

    @DubboReference(group = "sg", version = "1.0")
    private SgOmsStoInNoticeAndResultCmd sgOmsStoInNoticeAndResultCmd;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    /**
     * tob重新占单
     */
    public ValueHolderV14 findSource2B(SgFindSourceStrategy2BRequest request) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("SgRpcService.findSource2B request:{}",
                    "tob重新占单", JSONObject.toJSONString(request)));
        }

        ValueHolderV14 valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");
        try {

            valueHolderV14 = findSource2BCmd.findSource2B(request);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("SgRpcService.findSource2B result:{}",
                        "tob重新占单", JSONObject.toJSONString(valueHolderV14)));
            }

        } catch (Exception e) {

            log.error("tob重新占单:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }

        return valueHolderV14;

    }


    /**
     * 查询逻辑仓produce
     */
    public ValueHolderV14<Map<String, List<String>>> queryLsProduceDate(List<SgStorageRedisQueryProduceRequest> modelList, User user) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("SgRpcService.queryLsProduceDate request:{}",
                    "查询逻辑仓produce入参", JSONObject.toJSONString(modelList)));
        }

        ValueHolderV14<Map<String, List<String>>> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");
        try {

            valueHolderV14 = sgStorageQueryCmd.queryLsProduceDate(modelList, user);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("SgRpcService.queryLsProduceDate result:{}",
                        "查询逻辑仓produce出参", JSONObject.toJSONString(valueHolderV14)));
            }

        } catch (Exception e) {
            log.error("查询逻辑仓produce异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }

        return valueHolderV14;

    }

    /**
     * 查询冻结库存
     */
    public ValueHolderV14<List<SgFreezeStorageQueryResult>> queryFreezeStorage(SgFreezeStorageQueryRequest request, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("SgRpcService.queryFreezeStorage request:{}",
                    "查询冻结库存入参", JSONObject.toJSONString(request)));
        }

        ValueHolderV14<List<SgFreezeStorageQueryResult>> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");
        try {

            valueHolderV14 = sgStorageQueryCmd.queryFreezeStorage(request, user);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("SgRpcService.queryFreezeStorage result:{}",
                        "查询冻结库存出参", JSONObject.toJSONString(valueHolderV14)));
            }

        } catch (Exception e) {
            log.error("查询冻结库存异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }

        return valueHolderV14;
    }

    /**
     * 查询逻辑仓库存
     */
    public ValueHolderV14<HashMap<String, SgStorageRedisQueryApiResult>> queryLsStorageWithRedis(List<SgStorageRedisQueryLsRequest> modelList, User user) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("SgRpcService.queryLsStorageWithRedis request:{}",
                    "查询逻辑仓库存入参", JSONObject.toJSONString(modelList)));
        }

        ValueHolderV14<HashMap<String, SgStorageRedisQueryApiResult>> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");
        try {

            valueHolderV14 = sgStorageQueryCmd.queryLsStorageWithRedis(modelList, user);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("SgRpcService.queryLsProduceDate result:{}",
                        "查询逻辑仓库存出参", JSONObject.toJSONString(valueHolderV14)));
            }

        } catch (Exception e) {
            log.error("查询逻辑仓库存异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }

        return valueHolderV14;
    }

    /**
     * TOB仓库辐射查询
     */
    public ValueHolderV14<List<SgCTobStrategy>> queryTobStrategy(SgCTobStrategyQueryRequest request) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("SgRpcService.queryTobStrategy request:{}",
                    "TOB仓库辐射查询", JSONObject.toJSONString(request)));
        }

        ValueHolderV14<List<SgCTobStrategy>> valueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");
        try {
            valueHolderV14 = sgCTobStrategyQueryCmd.queryTobStrategy(request);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("SgRpcService.queryTobStrategy result:{}",
                        "TOB仓库辐射查询", JSONObject.toJSONString(valueHolderV14)));
            }
        } catch (Exception e) {
            log.error(" TOB仓库辐射查询异常:{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }

        return valueHolderV14;

    }


    /**
     * 获取电子面单
     *
     * @param notices
     * @return
     */
    public ValueHolderV14<String> getLabelByNoticeNo(SgBStoOutNotices notices) {
        if (log.isDebugEnabled()) {
            log.debug(" 获取电子面单,request:{}", JSON.toJSONString(notices));
        }
        ValueHolderV14<String> valueHolderV14 = new ValueHolderV14<>(ResultCode.FAIL, "获取失败");
        try {
            valueHolderV14 = sgBStoOutNoticesCmd.getLabelByNoticeNo(notices);
            if (log.isDebugEnabled()) {
                log.debug(" 获取电子面单,result:{}", JSON.toJSONString(valueHolderV14));
            }
        } catch (Exception e) {
            log.error(" 获取电子面单异常：{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setMessage(e.getMessage());
        }
        return valueHolderV14;
    }

    /**
     * 查询时效单占用配销层是否YY仓库
     *
     * @param requestList 请求参数
     * @return 是否YY仓库
     */
    public ValueHolderV14<Map<Long, Boolean>> queryShareStoreIsYy(List<SgBShareOutSaveRequest> requestList) {
        if (log.isDebugEnabled()) {
            log.debug(" 查询时效单占用配销层是否YY仓库,request:{}", JSON.toJSONString(requestList));
        }
        ValueHolderV14<Map<Long, Boolean>> valueHolderV14 = new ValueHolderV14<>(ResultCode.FAIL, "查询失败");
        try {
            valueHolderV14 = sgBShareOutCmd.queryShareStoreIsYy(requestList);
            if (log.isDebugEnabled()) {
                log.debug(" 查询时效单占用配销层是否YY仓库,result:{}", JSON.toJSONString(valueHolderV14));
            }
        } catch (Exception e) {
            log.error(" 查询时效单占用配销层是否YY仓库异常：{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setMessage(e.getMessage());
        }
        return valueHolderV14;
    }


    /**
     * 发送物流轨迹追踪请求
     *
     * @param request 请求参数
     * @return
     */
    public ValueHolderV14 queryOrderTrackBack(OrderTrackBackRequest request) {

        ValueHolderV14 valueHolderV14 = new ValueHolderV14(ResultCode.FAIL, "发送追踪请求失败");
        try {
            if (log.isDebugEnabled()) {
                log.debug(" 发送物流轨迹追踪请求,入参{}", JSON.toJSONString(request));
            }
            valueHolderV14 = logisticsTrackCmd.queryOrderTrackBack(request);
            if (log.isDebugEnabled()) {
                log.debug(" 发送物流轨迹追踪请求，出参{}", JSON.toJSONString(valueHolderV14));
            }

        } catch (Exception e) {
            log.error(" 发送物流轨迹追踪请求，异常{}", Throwables.getStackTraceAsString(e));
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("发送请求异常：" + e.getMessage());
        }

        return valueHolderV14;
    }


    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 freezeAndSubmit(SgBStoOutNoticesPosSaveRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("{} 缺货冻结,request:{}", this.getClass().getName(), JSON.toJSONString(request));
        }
        ValueHolderV14 v14 = sgBStoFreezeAndSubmitVipCmd.freezeAndSubmit(request);
        log.info("sgBStoFreezeAndSubmitVipCmd.freezeAndSubmit 接口调用返回结果：{}", JSON.toJSONString(v14));
        return v14;
    }

    public ValueHolderV14 stockTranslation(SgBStoStockTranslationRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("{} 库存平移接口调用开始,request:{}", this.getClass().getName(), JSON.toJSONString(request));
        }
        ValueHolderV14 v14 = sgBStoStockTranslationCmd.stockTranslation(request);
        if (log.isDebugEnabled()) {
            log.debug("sgBStoStockTranslationCmd.stockTranslation库存平移接口调用返回结果：{}", JSON.toJSONString(v14));
        }
        return v14;
    }

    /**
     * 实体仓变更调整逻辑发货单
     *
     * @param orderRelation 订单对象
     * @return Long
     * @author: heliu
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 queryChangeWareHouse(OcBOrderRelation orderRelation, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        try {
            //查找实体仓下面对应的逻辑仓
            List<Long> storeList = omsQueryWareHouseService.queryStoreList(orderRelation.getOrderInfo().getCpCPhyWarehouseId());
            if (CollectionUtils.isEmpty(orderRelation.getOrderItemList())) {
                vh.setCode(-1);
                vh.setMessage(orderRelation.getOrderId() + "_订单明细为空!");
                return vh;
            }
            if (CollectionUtils.isEmpty(storeList)) {
                vh.setCode(-1);
                vh.setMessage(orderRelation.getOrderId() + "该实体仓下面逻辑仓为空!");
                return vh;
            }
            //逻辑仓优先级
            List<StStockPriorityRequest> stStockPriorityRequests = syncStockStrategyService.queryStStockPriority(orderRelation.getOrderInfo().getCpCShopId(), storeList);
            if (CollectionUtils.isEmpty(stStockPriorityRequests)) {
                vh.setCode(-1);
                vh.setMessage(orderRelation.getOrderId() + "该店铺逻辑发货仓为空!");
                return vh;
            }

            ValueHolderV14 v14 = null;
            if (log.isDebugEnabled()) {
                log.debug("OrderId[" + orderRelation.getOrderId() + "],OMS实体仓变更占用库存服务出参:" + v14.toJSONObject().toJSONString() + ";");
            }
            JSONObject resultJson = v14.toJSONObject();
            if ("0".equalsIgnoreCase(resultJson.getString("code"))) {
                log.debug("_解析data数据" + resultJson.getJSONObject("data"));
                if ("0".equalsIgnoreCase(resultJson.getJSONObject("data").getString("preoutResult"))) {
                    vh.setCode(0);
                    vh.setMessage("订单Id" + orderRelation.getOrderId() + "实体仓变更调整逻辑发货单成功!");
                    //TODO 更新原发货仓
                } else if ("3".equalsIgnoreCase(resultJson.getJSONObject("data").getString("preoutResult"))) {
                    vh.setCode(3);
                    vh.setMessage("订单Id" + orderRelation.getOrderId() + "实体仓变更调整逻辑发货单失败,明细库存缺货!");
                    //专门用于 修改仓库缺货也能修改的判断
                    vh.setData("1");

                    //==============更新订单主表状态为缺货，更新明细缺货数量=================//
                    OcBOrder updateOrder = new OcBOrder();
                    updateOrder.setId(orderRelation.getOrderId());
                    updateOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
//                    updateOrder.setIsLackstock(OcBOrderConst.IS_STATUS_IY);
                    int count = ocBOrderMapper.updateById(updateOrder);
                    if (count > 0) {
                        JSONArray outStockArray = resultJson.getJSONObject("data").getJSONArray("outStockItemList");
                        if (CollectionUtils.isEmpty(outStockArray)) {
                            log.debug("订单orderId：{}，实体仓变更调整逻辑发货单缺货，缺货明细为空！", orderRelation.getOrderId());
                        } else {
                            Map<Long, BigDecimal> map = new HashMap<>(10);
                            for (int i = 0; i < outStockArray.size(); i++) {
                                JSONObject stockOutObj = outStockArray.getJSONObject(i);
                                Long psCSkuId = stockOutObj.getLongValue("psCSkuId");
                                BigDecimal qtyOutOfStock = stockOutObj.getBigDecimal("qtyOutOfStock");
                                map.put(psCSkuId, qtyOutOfStock);
                            }
                            List<OcBOrderItem> orderItemList = orderRelation.getOrderItemList();
                            for (OcBOrderItem ocBOrderItem : orderItemList) {
                                if (map.containsKey(ocBOrderItem.getPsCSkuId())) {
                                    OcBOrderItem updateOrderItem = new OcBOrderItem();
                                    updateOrderItem.setQtyLost(map.get(ocBOrderItem.getPsCSkuId()));
                                    UpdateWrapper<OcBOrderItem> wrapper = new UpdateWrapper<>();
                                    wrapper.eq("oc_b_order_id", orderRelation.getOrderId()).eq("id", ocBOrderItem.getId());
                                    ocBOrderItemMapper.update(updateOrderItem, wrapper);
                                }
                            }
                        }
                    } else {
                        log.debug("订单orderId：{}，实体仓变更调整逻辑发货单更新订单状态为缺货失败！", orderRelation.getOrderId());
                    }
                } else {
                    vh.setCode(-1);
                    vh.setMessage("订单Id" + orderRelation.getOrderId() + "实体仓变更调整逻辑发货单返回状态异常!");
                }
            } else {
                vh.setCode(-1);
                vh.setMessage(resultJson.getString("message"));
            }
            return vh;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("OrderId[" + orderRelation.getOrderId() + "]调用舒威实体仓变更调整逻辑发货单服务 queryChangeWareHouse 异常信息！", e);
            vh.setCode(-1);
            vh.setMessage("订单Id" + orderRelation.getOrderId() + "实体仓变更调整逻辑发货单服务执行失败!" + e.getMessage());
            return vh;
        }
    }

    /**
     * 清空逻辑发货单
     *
     * @return
     */
    public ValueHolderV14 cleanSgLogicalShipment(OcBOrder ocBOrder, User user) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {

        } catch (Exception e) {
            log.error(this.getClass().getName() + " 清空逻辑发货单失败", e);
            holderV14.setCode(-1);
            holderV14.setMessage("清空逻辑发货单失败");
        }
        return holderV14;
    }

    /**
     * JITX寻仓订单 寻仓/查询有效库存
     *
     * @param deliveryRelation 实体对象
     * @return boolean
     * @author: chenxiulou
     */
    public ValueHolderV14 queryJitxSendWareHouseStock(IpJitxDeliveryRelation deliveryRelation) {

        ValueHolderV14 vh = new ValueHolderV14();
        try {
            //SgSumStoragePageQueryRequest request = new SgSumStoragePageQueryRequest();
            //取出店铺同步策略下面的逻辑供货仓
            List<Long> stStockPriorityRequests = syncStockStrategyService.queryShopStoreNextList(deliveryRelation
                    .getJitxDelivery().getCpCShopId());

            if (CollectionUtils.isEmpty(deliveryRelation.getJitxDeliveryItemList())) {
                vh.setCode(-1);
                vh.setMessage(deliveryRelation.getOrderId() + "_订单的明细为空!");
                return vh;
            }
            if (CollectionUtils.isEmpty(stStockPriorityRequests)) {
                vh.setCode(-1);
                vh.setMessage(deliveryRelation.getOrderId() + "该店铺逻辑发货仓为空!");
                return vh;
            }
            // skuID 与 skuCode对应关系
            Map<Long, String> skuCodeMap = deliveryRelation.getJitxDeliveryItemList()
                    .stream().filter(it -> it.getProdSku() != null && StringUtils.isNotEmpty(it.getProdSku().getSkuEcode()))
                    .map(item -> item.getProdSku()).collect(Collectors.toMap(ProductSku::getId, ProductSku::getSkuEcode, (v1, v2) -> v1));
            return this.querySendWareHouseStockByRedis(deliveryRelation.getOrderId(), stStockPriorityRequests, skuCodeMap);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("OrderId[" + deliveryRelation.getOrderId() + "]查询有效库存服务执行失败 queryStockSgBStorage 异常信息！" + e.getMessage());
            vh.setCode(-1);
            vh.setMessage("订单Id" + deliveryRelation.getOrderId() + "查询有效库存服务执行失败!" + e.getMessage());
            return vh;
        }
    }

    /**
     * 寻仓/查询有效库存
     *
     * @param orderRelation 实体对象
     * @return boolean
     * @author: heliu
     */
    public ValueHolderV14<List<SgSumStorageQueryResult>> querySendWareHouseStock(OcBOrderRelation orderRelation) {

        ValueHolderV14<List<SgSumStorageQueryResult>> vh = new ValueHolderV14<>();
        try {
            if (CollectionUtils.isEmpty(orderRelation.getOrderItemList())) {
                vh.setCode(-1);
                vh.setMessage(orderRelation.getOrderId() + "_订单的明细为空!");
                return vh;
            }
            //取出店铺同步策略下面的逻辑供货仓
            List<Long> logicalStoreIdList = syncStockStrategyService.queryShopStoreNextList(orderRelation.getOrderInfo().getCpCShopId());
            if (CollectionUtils.isEmpty(logicalStoreIdList)) {
                vh.setCode(-1);
                vh.setMessage(orderRelation.getOrderId() + "该店铺逻辑发货仓为空!");
                return vh;
            }
            // skuID 与 skuCode对应关系
            Map<Long, String> skuCodeMap = orderRelation.getOrderItemList().stream().collect(Collectors.toMap(OcBOrderItem::getPsCSkuId, OcBOrderItem::getPsCSkuEcode, (v1, v2) -> v1));
            return this.querySendWareHouseStockByRedis(orderRelation.getOrderInfo().getId(), logicalStoreIdList, skuCodeMap);
        } catch (Exception e) {
            log.error(LogUtil.format("查询有效库存服务执行失败 queryStockSgBStorage 异常信息！{},OrderId：", orderRelation.getOrderId())
                    , Throwables.getStackTraceAsString(e));
            vh.setCode(-1);
            vh.setMessage("订单Id" + orderRelation.getOrderId() + "查询有效库存服务执行失败!" + e.getMessage());
            return vh;
        }
    }

    /**
     * 寻仓
     * 库存中心改造后提供的新接口，走REDIS的接口
     *
     * @param orderId            订单ID
     * @param logicalStoreIdList 逻辑仓ID
     * @param skuIdCodeMap       skuID与skucode对应关系
     * @author: 江家雷
     */
    public ValueHolderV14<List<SgSumStorageQueryResult>> querySendWareHouseStockByRedis(Long orderId, List<Long> logicalStoreIdList,
                                                                                        Map<Long, String> skuIdCodeMap) {

        ValueHolderV14<List<SgSumStorageQueryResult>> vh = new ValueHolderV14<>();
        try {
            // 查询出逻辑仓和实体仓关系构建参数
            Map<Long, Long> phyWarehouseMap = new HashMap<>();
            List<CpStore> cpStoreList = cpRpcService.queryLogicalWarehouse(logicalStoreIdList);

            List<Long> wareIdList = cpStoreList.stream().map(CpStore::getCpCPhyWarehouseId).collect(Collectors.toList());

            List<Long> skuIdList = new ArrayList<>(skuIdCodeMap.keySet());
            // 逻辑仓ID
            SgStorageQueryRequest sgRequest = new SgStorageQueryRequest();
            sgRequest.setSkuIds(skuIdList);
            sgRequest.setStoreIds(logicalStoreIdList);
            sgRequest.setPhyWarehouseIds(wareIdList);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OrderId{}调用库存中心分仓接口入参={}", "调用库存中心分仓接口入参", orderId), JSON.toJSONString(sgRequest));
            }
            ValueHolderV14<List<SgBStorageInclShare>> sgResult = sgStorageQueryCmd.queryStorageInclShare(sgRequest, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OrderId{}调用库存中心分仓接口出参={}", "调用库存中心分仓接口出参", orderId),
                        JSON.toJSONString(sgResult));
            }
            if (sgResult.getCode() == 0) {
                List<SgBStorageInclShare> dataList = sgResult.getData();
                List<SgSumStorageQueryResult> resultList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dataList)) {
                    resultList = dataList.stream().map(
                            item -> {
                                SgSumStorageQueryResult sgSumStorageQueryResult = new SgSumStorageQueryResult();
                                BeanUtils.copyProperties(item, sgSumStorageQueryResult);
                                CpCPhyWarehouse cpCPhyWarehouse = cpRpcService
                                        .queryByWarehouseId(item.getCpCPhyWarehouseId());
                                sgSumStorageQueryResult.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
                                sgSumStorageQueryResult.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
                                sgSumStorageQueryResult.setPsCSkuEcode(skuIdCodeMap.get(item.getPsCSkuId()));
                                return sgSumStorageQueryResult;
                            }
                    ).collect(Collectors.toList());
                }
                vh.setCode(0);
                vh.setData(resultList);
                vh.setMessage("订单Id" + orderId + "查询有效库存成功!");
            } else {
                vh.setCode(-1);
                vh.setMessage("订单Id" + orderId + "查询有效库存失败!");
            }
            return vh;
        } catch (Exception e) {
            log.error(LogUtil.format("查询有效库存服务执行失败,异常信息={}，订单OrderId={}", orderId, "查询有效库存服务"),
                    Throwables.getStackTraceAsString(e));
            vh.setCode(-1);
            vh.setMessage("订单Id" + orderId + "查询有效库存服务执行失败!" + e.getMessage());
            return vh;
        }
    }


    private List<Long> getStoreAndPhyWarehouse(List<Long> storeIdList) {
        List<CpStore> cpStoreList = cpRpcService.queryLogicalWarehouse(storeIdList);

        List<Long> wareIdList = cpStoreList.stream().map(CpStore::getCpCPhyWarehouseId).collect(Collectors.toList());
        return wareIdList;
    }

    /**
     * 查询有效逻辑仓库存
     */
    public List<SgBStorageInclShare> queryAllStorageStockByRedis(Long orderId, List<Long> storeIdList, List<Long> skuIdList) throws NDSException {
        long startTime = System.currentTimeMillis();
        List<SgBStorageInclShare> resultList = new ArrayList<>();
        try {
            // 获取逻辑仓和实体仓的映射关系
            List<Long> warehouseIdList = this.getStoreAndPhyWarehouse(storeIdList);

            SgStorageQueryRequest sgRequest = new SgStorageQueryRequest();
            sgRequest.setSkuIds(skuIdList);
            sgRequest.setStoreIds(storeIdList);
            sgRequest.setPhyWarehouseIds(warehouseIdList);


            log.info(LogUtil.format("调用库存中心-查询有效逻辑仓库存入参：{},订单ID:{}", "查询有效逻辑仓库存入参", orderId)
                    , JSONObject.toJSONString(sgRequest), orderId);
            ValueHolderV14<List<SgBStorageInclShare>> sgResult = sgStorageQueryCmd.queryStorageInclShare(sgRequest, SystemUserResource.getRootUser());
            log.info(LogUtil.format("调用库存中心-查询有效逻辑仓库存出参：{},订单ID:{}", "查询有效逻辑仓库存出参", orderId)
                    , JSONObject.toJSONString(sgResult), orderId);
            if (sgResult.isOK()) {
                resultList = sgResult.getData();
            }
        } catch (Exception e) {
            log.error(LogUtil.format("调用库存中心-查询有效逻辑仓库存异常：{},订单ID=", orderId, "查询有效逻辑仓库存异常"),
                    Throwables.getStackTraceAsString(e));
        }

        log.info(LogUtil.format("调用库存中心-查询有效逻辑仓库存返回值：{} 耗时:{}ms,订单ID:{}", "查询有效逻辑仓库存返回值", orderId)
                , JSONObject.toJSONString(resultList), System.currentTimeMillis() - startTime, orderId);
        return resultList;
    }

    /**
     * 组装寻源逻辑仓发送参数 [占用库存/释放库存 订单Id]
     *
     * @param orderRelation           订单对象
     * @param stStockPriorityRequests 店铺供货逻辑仓优先级
     * @param orderItemList           订单明细
     * @param isOccpy                 是否全部占用
     * @return SgLogicSearchRequest
     */

    /**
     * <AUTHOR>
     * @Date 20:40 2021/7/19
     * @Description 封装数据
     */
    private SgOmsShareOutRequest buildSgOmsShareOutRequest(OcBOrder orderInfo, List<OcBOrderItem> ocBOrderItemList, User user) {
        SgOmsShareOutRequest request = new SgOmsShareOutRequest();
        request.setSourceBillId(orderInfo.getId());
        request.setSourceBillNo(orderInfo.getBillNo());
        request.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        request.setLoginUser(user);
        List<SgOmsShareOutItemRequest> itemRequestList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            SgOmsShareOutItemRequest sgOmsShareOutItemRequest = new SgOmsShareOutItemRequest();
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            sgOmsShareOutItemRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            sgOmsShareOutItemRequest.setQtyPreout(ocBOrderItem.getQty());
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            itemRequestList.add(sgOmsShareOutItemRequest);
        }
        request.setItemRequestList(itemRequestList);

        return request;
    }

    /**
     * 作废逻辑发货单
     * <p>
     * 单据类型  orderType
     * 1零售发货，2.销售发货，3采购退货发货，4 调拨发货， 5 销售退货发货
     * 具体枚举参考 TaobaoReturnOrderExt.sgOrderType
     * ljp add
     *
     * @param ocBOrder
     * @param user
     * @return
     */
    public ValueHolderV14 invoildOutOrder(boolean isCc, OcBOrder ocBOrder, User user) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        try {
            if (isCc || OmsOrderUtil.isToCCcOrder(ocBOrder)) {
                valueHolderV14 = this.voidFreezeOutOrder(ocBOrder, user);
            } else {
                SgOmsShareOutRequest sgOmsShareOutRequest = new SgOmsShareOutRequest();
                sgOmsShareOutRequest.setSourceBillId(ocBOrder.getId());
                sgOmsShareOutRequest.setSourceBillNo(ocBOrder.getBillNo());
                sgOmsShareOutRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
                sgOmsShareOutRequest.setTid(ocBOrder.getTid());
                sgOmsShareOutRequest.setCancelType(SgConstantsIF.OMS_STORAGE_OCCUPY_CANCEL_TYPE_MAIN);
                sgOmsShareOutRequest.setLoginUser(user);
                log.debug("OrderId" + ocBOrder.getId() + "作废逻辑发货单调用库存中心接口入参：" + JSONObject.toJSONString(sgOmsShareOutRequest));
                valueHolderV14 = sgOmsShareOutVoidCmd.voidSgOmsShareOut(sgOmsShareOutRequest);
                log.debug("OrderId" + ocBOrder.getId() + "作废逻辑发货单调用库存中心返回结果"
                        + valueHolderV14.toJSONObject() + ";");
            }
            return valueHolderV14;
        } catch (Exception e) {
            e.printStackTrace();
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
            log.error("OrderId" + ocBOrder.getId() + "调用作废逻辑发货单服务SgSendCmd.voidSgBSend:异常信息！", e);
            return valueHolderV14;
        }
    }

    /**
     * 寻源生成冻结占用单
     *
     * @param request request
     * @return result
     */
    public ValueHolderV14 findSourceSaveFreezeOut(SgBStoFreezeOutOmsSaveRequest request) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        try {
            log.debug("OrderId" + request.getSourceBillId() + "寻源生成冻结占用单调用库存中心接口入参：" + JSONObject.toJSONString(request));
            valueHolderV14 = sgOmsFreezeOutCmd.findSourceSaveFreezeOut(request);
            log.debug("OrderId" + request.getSourceBillId() + "寻源生成冻结占用单调用库存中心返回结果"
                    + valueHolderV14.toJSONObject() + ";");

            return valueHolderV14;
        } catch (Exception e) {
            e.printStackTrace();
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
            log.error("OrderId" + request.getSourceBillId() + "调用寻源生成冻结占用单服务SgOmsFreezeOutCmd.findSourceSaveFreezeOut:异常信息！", e);
            return valueHolderV14;
        }
    }

    /**
     * 作废冻结占用单
     *
     * @param order 订单
     * @param user  user
     * @return result
     */
    public ValueHolderV14 voidFreezeOutOrder(OcBOrder order, User user) {
        SgBStoFreezeOutRequest request = new SgBStoFreezeOutRequest();
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        try {
            request.setSourceBillId(order.getId());
            request.setSourceBillNo(order.getBillNo());
            request.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
            request.setLoginUser(user);
            log.debug("OrderId" + order.getId() + "作废冻结占用单调用库存中心接口入参：" + JSONObject.toJSONString(request));
            valueHolderV14 = sgOmsFreezeOutCmd.freezeOutVoid(request);
            log.debug("OrderId" + order.getId() + "作废冻结占用单调用库存中心返回结果"
                    + valueHolderV14.toJSONObject() + ";");

            return valueHolderV14;
        } catch (Exception e) {
            e.printStackTrace();
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
            log.error("OrderId" + order.getId() + "调用作废冻结占用单服务SgOmsFreezeOutCmd.voidFreezeOutOrder:异常信息！", e);
            return valueHolderV14;
        }
    }

    /**
     * 作废出库通知单并传wms 需要改wms状态记录日志调用
     * ljp add
     *
     * @param orderInfoList 订单列表
     * @param user          操作用户
     * @return 作废结果
     */
    public ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> invoildOutgoingNotice(List<OcBOrder> orderInfoList,
                                                                                      User user, Boolean isRelyOnWing) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("作废出库通知单服务入参:{}", "出库通知单"), JSONObject.toJSONString(orderInfoList));
        }
        ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> result = new ValueHolderV14<>();
        List<SgBStoOutNoticesBillVoidRequest> noticesSaveRequestList = Lists.newArrayList();
        for (OcBOrder orderInfo : orderInfoList) {
            SgBStoOutNoticesBillVoidRequest noticesRequest = new SgBStoOutNoticesBillVoidRequest();
            noticesRequest.setSourceBillId(orderInfo.getId());
            noticesRequest.setSourceBillNo(orderInfo.getBillNo());
            noticesRequest.setIsRecallFromThird(isRelyOnWing);
            noticesRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
            noticesSaveRequestList.add(noticesRequest);
        }
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("作废出库通知单并传wms接口入参:{}", "出库通知单"), JSONObject.toJSONString(noticesSaveRequestList));
            }
            result = sgBStoOutNoticesCmd.voidSgStoOutNotices(noticesSaveRequestList, user);
            try {
                cancelLabelRequirements(noticesSaveRequestList, result);
            } catch (Exception e) {
                log.error("调用作废出库通知单并传wms服务SgBStoOutNoticesCmd.voidSgStoOutNotices:异常信息！", e);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("作废出库通知单并传wms返回结果:{}", "出库通知单"), JSONObject.toJSONString(result));
            }
            List<SgBStoOutNoticesBillVoidResult> dataList = result.getData();
            StringBuilder message = new StringBuilder();
            for (SgBStoOutNoticesBillVoidResult voidResult : dataList) {
                Long id = voidResult.getSourceBillId();
                OcBOrder findOrderInfo = ocBOrderMapper.selectById(id);
                OcBOrder flagOrder = new OcBOrder();
                flagOrder.setId(findOrderInfo.getId());
                flagOrder.setBillNo(findOrderInfo.getBillNo());
                flagOrder.setModifierid((long) user.getId());
                flagOrder.setModifiername(user.getName());
                flagOrder.setModifierename(user.getEname());
                flagOrder.setModifieddate(new Date());
                if (voidResult.getCode() == (ResultCode.SUCCESS)) {
                    //作废成功 修改WMS撤销状态
                    message.append("订单编号(" + id + "):" + "作废成功");
                    flagOrder.setWmsCancelStatus(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger());
                    orderCancleWmsService.saveOrderWmsStatusAndLog(flagOrder, user,
                            OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger(), "");
                } else {
                    if (voidResult.getIsExist()) {
                        message.append("订单编号(" + id + "):" + "作废成功");
                        flagOrder.setWmsCancelStatus(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger());
                        voidResult.setCode(ResultCode.SUCCESS);
                        orderCancleWmsService.saveOrderWmsStatusAndLog(flagOrder, user,
                                OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger(), "");
                    } else {
                        message.append("订单编号(" + id + "):" + "作废失败");
                        voidResult.setCode(ResultCode.FAIL);
                        //作废失败 修改WMS撤销状态
                        flagOrder.setWmsCancelStatus(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger());
                        orderCancleWmsService.saveOrderWmsStatusAndLog(flagOrder, user,
                                OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger(),
                                voidResult.getMessage());
                    }
                    result.setMessage(message.toString());
                }
                if (orderInfoList.size() == 1) {
                    return ValueHolderV14Utils.custom(voidResult.getCode(), voidResult.getMessage(), dataList);
                }
            }
            result.setData(dataList);
        } catch (Exception e) {
            log.error(LogUtil.format("调用作废出库通知单服务sgPhyOutNoticesVoidCmd.execute:异常信息！{}", "出库通知单"),
                    Throwables.getStackTraceAsString(e));
            result.setCode(ResultCode.FAIL);
            result.setMessage("调用作废出库通知单服务sgPhyOutNoticesVoidCmd.execute:异常信息！" + e.getMessage());
        }
        return result;
    }

    /**
     * 新增入库通知单和逻辑在途单
     *
     * @param request
     * @return
     */
    public ValueHolderV14 addOrderNotice(SgOmsStoInRequest request) {
        ValueHolderV14 result = new ValueHolderV14();
        try {
            result = sgOmsStoInCmd.saveSgOmsStoIn(request);
        } catch (Exception e) {
            log.error(LogUtil.format("调用新增入库通知单addOrderNoyiceNew.execute:异常信息！{}", "入库通知单"),
                    Throwables.getStackTraceAsString(e));
            result.setCode(ResultCode.FAIL);
            result.setMessage("调用新增入库通知单addOrderNoyiceNew.execute:异常信息！" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("新的新增入库通知单和逻辑发货单返回结果：{}", "入库通知单"), JSONObject.toJSONString(result));
        }
        return result;
    }

    /**
     * 查询建议聚合仓、实体仓库存
     *
     * @param shopId       店铺id
     * @param skuIds       skuid
     * @param shareStoreId 聚合仓id
     * @param loginUser    用户
     * @return 库存
     */
    public ValueHolderV14<SgOmsShopStorageQueryResult> queryOmsShopStorage(Long shopId, List<Long> skuIds,
                                                                           Long shareStoreId, User loginUser) {
        SgOmsShopStorageQueryRequest queryRequest = new SgOmsShopStorageQueryRequest();
        queryRequest.setCpCShopId(shopId);
        queryRequest.setPsCSkuIdList(skuIds);
        queryRequest.setIncludePhyStorage(false);
        if (!ObjectUtils.isEmpty(shareStoreId)) {
            queryRequest.setSgCShareStoreId(shareStoreId);
            queryRequest.setIncludePhyStorage(true);
        }
        ValueHolderV14<SgOmsShopStorageQueryResult> result = new ValueHolderV14<>();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询建议聚合仓、实体仓库存 sgOmsStandardCmd.queryOmsShopStorage 入参：", "聚合仓") + JSON.toJSONString(queryRequest));
            }
            result = sgOmsStandardCmd.queryOmsShopStorage(queryRequest, loginUser);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询建议聚合仓、实体仓库存 sgOmsStandardCmd.queryOmsShopStorage返回结果：", "聚合仓") + JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询建议聚合仓、实体仓库存 sgOmsStandardCmd.queryOmsShopStorage 异常信息！{}", "聚合仓"),
                    Throwables.getStackTraceAsString(e));
            if (result == null) {
                result = new ValueHolderV14<>();
            }
            result.setCode(ResultCode.FAIL);
            result.setMessage("查询建议聚合仓、实体仓库存 sgOmsStandardCmd.queryOmsShopStorage 异常信息！" + e.getMessage());
        }
        return result;
    }

    /**
     * 库存调整单(实体仓)新增并审核(RPC)
     *
     * @param saveRequest
     * @return true 新增成功 false 失败
     */
    public ValueHolderV14 addStockAdjustment(SgOmsStoAdjustSaveRequest saveRequest) {

        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("SgRpcService.库存调整单入参-{}", "库存调整单"), JSONObject.toJSONString(saveRequest));
            }
            ValueHolderV14 execute = sgOmsStoAdjustCmd.saveAndSubmit(saveRequest);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("SgRpcService.库存调整单出参-{}", "库存调整单"), JSONObject.toJSONString(execute));
            }
            int code = Tools.getInt(execute.getCode(), -1);
            return execute;
        } catch (Exception e) {
            log.error(LogUtil.format("库存调整单异常{}"), Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }

    }

    /**
     * 作废出库通知单并传Wms
     * ljp add
     *
     * @param ocBOrderList
     * @param user
     * @return
     */
    public ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> invoildOutgoingNoticeAndWms(List<OcBOrder> ocBOrderList, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("作废出库通知单入参", "出库通知单") + JSONObject.toJSONString(ocBOrderList));
        }
        ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> result = new ValueHolderV14<>();
        List<SgBStoOutNoticesBillVoidRequest> noticesSaveRequestList = Lists.newArrayList();
        for (OcBOrder orderInfo : ocBOrderList) {
            SgBStoOutNoticesBillVoidRequest noticesRequest = new SgBStoOutNoticesBillVoidRequest();
            noticesRequest.setSourceBillId(orderInfo.getId());
            noticesRequest.setSourceBillNo(orderInfo.getBillNo());
//            noticesRequest.setSourcecode(orderInfo.getSourceCode());
            noticesRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
//            noticesRequest.setOutType(SgOutConstantsIF.OUT_TYPE_ELECTRICITY);
            noticesSaveRequestList.add(noticesRequest);
        }
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("作废出库通知单并传wms接口入参:{}", "作废出库通知单并传wms"), JSONObject.toJSONString(noticesSaveRequestList));
            }
            result = sgBStoOutNoticesCmd.voidSgStoOutNotices(noticesSaveRequestList, user);
            cancelLabelRequirements(noticesSaveRequestList, result);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("作废出库通知单并传wms返回结果:{}", "作废出库通知单并传wms"), JSONObject.toJSONString(result));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("调用作废出库通知单服务sgPhyOutNoticesVoidCmd.execute:异常信息！{}", "作废出库通知单并传wms"),
                    Throwables.getStackTraceAsString(e));
            result.setCode(ResultCode.FAIL);
            result.setMessage("调用作废出库通知单服务sgPhyOutNoticesVoidCmd.execute:异常信息！" + e.getMessage());
        }
        return result;
    }

    /**
     * 零售退货单-作废出库通知单并传Wms
     *
     * @param returnOrders
     * @param user
     * @return
     */
    public ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> callReturnOrderVoidOutNoticeAndToWms(
            List<OcBReturnOrder> returnOrders, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("作废出库通知单入参", "出库通知单") + JSONObject.toJSONString(returnOrders));
        }
        ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> result = new ValueHolderV14<>();
        List<SgBStoOutNoticesBillVoidRequest> noticesSaveRequestList = Lists.newArrayList();
        for (OcBReturnOrder returnOrder : returnOrders) {
            SgBStoOutNoticesBillVoidRequest noticesRequest = new SgBStoOutNoticesBillVoidRequest();
            noticesRequest.setSourceBillId(returnOrder.getId());
            noticesRequest.setSourceBillNo(returnOrder.getBillNo());
            noticesRequest.setSourceBillType(SgFromSourceBillTypeConstants.BILL_TYPE_RETAIL_REF);
            noticesSaveRequestList.add(noticesRequest);
        }
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("作废出库通知单并传wms接口入参:{}", "作废出库通知单并传wms"), JSONObject.toJSONString(noticesSaveRequestList));
            }
            result = sgBStoOutNoticesCmd.voidSgStoOutNotices(noticesSaveRequestList, user);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("作废出库通知单并传wms返回结果:{}", "作废出库通知单并传wms"), JSONObject.toJSONString(result));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("调用作废出库通知单服务sgPhyOutNoticesVoidCmd.execute:异常信息！{}", "作废出库通知单并传wms"),
                    Throwables.getStackTraceAsString(e));
            result.setCode(ResultCode.FAIL);
            result.setMessage("调用作废出库通知单服务sgPhyOutNoticesVoidCmd.execute:异常信息！" + e.getMessage());
        }
        return result;
    }

    public void cancelLabelRequirements(List<SgBStoOutNoticesBillVoidRequest> noticesSaveRequestList, ValueHolderV14<List<SgBStoOutNoticesBillVoidResult>> result) {

        if (!result.isOK()) {
            return;
        }
        List<SgBStoOutNoticesBillVoidResult> dataList = result.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            dataList = new ArrayList<>();
            // 构建dataList
            for (SgBStoOutNoticesBillVoidRequest sgBStoOutNoticesBillVoidRequest : noticesSaveRequestList) {
                SgBStoOutNoticesBillVoidResult sgBStoOutNoticesBillVoidResult = new SgBStoOutNoticesBillVoidResult();
                sgBStoOutNoticesBillVoidResult.setCode(ResultCode.SUCCESS);
                sgBStoOutNoticesBillVoidResult.setSourceBillId(sgBStoOutNoticesBillVoidRequest.getSourceBillId());
                dataList.add(sgBStoOutNoticesBillVoidResult);
            }

        }
        // dataList对sourceBillId进行分组
        Map<Long, List<SgBStoOutNoticesBillVoidResult>> map = dataList.stream().collect(Collectors.groupingBy(SgBStoOutNoticesBillVoidResult::getSourceBillId));
        for (SgBStoOutNoticesBillVoidRequest sgBStoOutNoticesBillVoidRequest : noticesSaveRequestList) {
            Integer sourceBillType = sgBStoOutNoticesBillVoidRequest.getSourceBillType();
            if (Objects.equals(sourceBillType, null) || Objects.equals(sourceBillType, SgFromSourceBillTypeConstants.BILL_TYPE_RETAIL_REF)) {
                continue;
            }
            Long sourceBillId = sgBStoOutNoticesBillVoidRequest.getSourceBillId();
            List<SgBStoOutNoticesBillVoidResult> voidResults = map.get(sourceBillId);
            if (CollectionUtils.isEmpty(voidResults)) {
                return;
            }
            SgBStoOutNoticesBillVoidResult voidResult = voidResults.get(0);
            if (voidResult.getCode().equals(ResultCode.FAIL)) {
                return;
            }
            // 查询订单 判断是否是tob订单
            OcBOrder ocBOrder = ocBOrderMapper.selectById(sourceBillId);
            boolean toBOrder = OmsBusinessTypeUtil.isToBOrder(ocBOrder);
            if (!toBOrder) {
                continue;
            }
            // 如果是tob订单 判断是否有增值服务
            List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectUnSuccessRefund(ocBOrder.getId());
            if (CollectionUtils.isEmpty(ocBOrderItemList)) {
                return;
            }

            // 如果ocBOrderItemList中 labelingRequirements都没有值
            List<OcBOrderItem> containsLabelingRequirements = ocBOrderItemList.stream().filter(item -> item.getLabelingRequirements() != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(containsLabelingRequirements)) {
                return;
            }

            // 构建信息 调用wms
            LabelRequirementsCancelRequest cancelRequest = new LabelRequirementsCancelRequest();
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(ocBOrder.getCpCPhyWarehouseId());
            cancelRequest.setOrderCode(ocBOrder.getSgBOutBillNo());
            cancelRequest.setWarehouseCode(cpCPhyWarehouse.getWmsWarehouseCode());
            cancelRequest.setOwnerCode(cpCPhyWarehouse.getOwnerCode());

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("method", "taobao.qimen.label.requirements.cancel");
            jsonObject.put("data", JSONUtil.toJsonStr(cancelRequest));
            String msgKey = UUID.randomUUID().toString();
            defaultProducerSend.sendTopic("R3_OMS_LABEL_REQUIREMENTS_IPCS", "label_requirements_cancel_to_wms", jsonObject.toJSONString(), msgKey);
        }
    }


    /**
     * 修改收货地址
     *
     * @param orderRelation 实体对象
     * @return boolean
     * @author: heliu
     */
    public ValueHolderV14 querySearchStockAndModifyAddress(OcBOrderRelation orderRelation, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        try {
            //查找实体仓下面对应的逻辑仓
            List<Long> storeList = omsQueryWareHouseService
                    .queryStoreList(orderRelation.getOrderInfo().getCpCPhyWarehouseId());
            if (CollectionUtils.isEmpty(orderRelation.getOrderItemList())) {
                vh.setCode(-1);
                vh.setMessage(orderRelation.getOrderId() + "传入订单明细为空!");
                return vh;
            }
            if (CollectionUtils.isEmpty(storeList)) {
                vh.setCode(-1);
                vh.setMessage(orderRelation.getOrderId() + "传入实体仓下面逻辑仓为空!");
                return vh;
            }
            List<StStockPriorityRequest> stStockPriorityRequests = syncStockStrategyService
                    .queryStStockPriority(orderRelation.getOrderInfo().getCpCShopId(), storeList);
            if (CollectionUtils.isEmpty(stStockPriorityRequests)) {
                vh.setCode(-1);
                vh.setMessage(orderRelation.getOrderId() + "传入店铺逻辑发货仓为空!");
                return vh;
            }
            ValueHolderV14 v14 = null;
            if (log.isDebugEnabled()) {
                log.debug("OrderId[" + orderRelation.getOrderId() + "],OMS修改收货地址占用库存服务出参:" + v14.toJSONObject().toJSONString() + ";");
            }
            JSONObject resultJson = v14.toJSONObject();
            if ("0".equalsIgnoreCase(resultJson.getString("code"))) {
                log.debug("_解析data数据" + resultJson.getJSONObject("data"));
                if ("0".equalsIgnoreCase(resultJson.getJSONObject("data").getString("preoutResult"))) {
                    //当满足库存时为待审核
                    orderRelation.getOrderInfo().setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                    orderRelation.getOrderInfo().setModifierename(SystemUserResource.ROOT_USER_NAME);
                    orderRelation.getOrderInfo().setModifieddate(new Date());
                    omsOrderService.updateOrderInfo(orderRelation.getOrderInfo());
                    vh.setCode(0);
                    vh.setMessage(orderRelation.getOrderId() + "修改收货地址重新占用库存成功!");
                } else if ("3".equalsIgnoreCase(resultJson.getJSONObject("data").getString("preoutResult"))) {
                    vh.setCode(3);
                    vh.setMessage(orderRelation.getOrderId() + "修改收货地址重新占用库存失败,明细库存缺货!");
                } else {
                    vh.setCode(-1);
                    vh.setMessage(orderRelation.getOrderId() + "修改收货地址重新占用库存返回状态异常!");
                }
            } else {
                vh.setCode(-1);
                vh.setMessage(resultJson.getString("message"));
            }
            return vh;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用舒威寻源逻辑仓库存服务 querySearchStockAndModifyAddress 异常信息！" + e.getMessage());
            vh.setCode(-1);
            vh.setMessage(orderRelation.getOrderId() + "修改收货地址重新占用库存失败!" + e.getMessage());
            return vh;
        }
    }

    /**
     * 修改商品信息
     *
     * @param orderRelation 实体对象
     * @return boolean
     * @author: heliu
     */
    public ValueHolderV14 querySearchStockAndModifyGoodsInfo(OcBOrderRelation orderRelation, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        try {
            //查找实体仓下面对应的逻辑仓
            List<Long> storeList = omsQueryWareHouseService
                    .queryStoreList(orderRelation.getOrderInfo().getCpCPhyWarehouseId());
            if (CollectionUtils.isEmpty(orderRelation.getOrderItemList())) {
                vh.setCode(-1);
                vh.setMessage(orderRelation.getOrderId() + "传入订单明细为空!");
                return vh;
            }
            if (CollectionUtils.isEmpty(storeList)) {
                vh.setCode(-1);
                vh.setMessage(orderRelation.getOrderId() + "传入实体仓下面逻辑仓为空!");
                return vh;
            }
            List<StStockPriorityRequest> stStockPriorityRequests = syncStockStrategyService
                    .queryStStockPriority(orderRelation.getOrderInfo().getCpCShopId(), storeList);
            if (CollectionUtils.isEmpty(stStockPriorityRequests)) {
                vh.setCode(-1);
                vh.setMessage(orderRelation.getOrderId() + "传入店铺逻辑发货仓为空!");
                return vh;
            }
            ValueHolderV14 v14 = null;
            if (log.isDebugEnabled()) {
                log.debug("OrderId[" + orderRelation.getOrderId() + "],OMS修改商品信息占用库存服务出参:" + v14.toJSONObject().toJSONString() + ";");
            }
            JSONObject resultJson = v14.toJSONObject();
            if ("0".equalsIgnoreCase(resultJson.getString("code"))) {
                log.debug("_解析data数据" + resultJson.getJSONObject("data"));
                if ("0".equalsIgnoreCase(resultJson.getJSONObject("data").getString("preoutResult"))) {
                    vh.setCode(0);
                    vh.setMessage(orderRelation.getOrderId() + "修改商品信息重新占用库存成功!");
                } else {
                    vh.setCode(-1);
                    vh.setMessage(orderRelation.getOrderId() + "修改商品信息重新占用库存失败,异常原因: " + resultJson.getString("message"));
                }
            } else {
                vh.setCode(-1);
                vh.setMessage(resultJson.getString("message"));
            }
            return vh;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用舒威寻源逻辑仓库存服务 querySearchStockAndModifyGoodsInfo 异常信息！" + e.getMessage());
            vh.setCode(-1);
            vh.setMessage(orderRelation.getOrderId() + "修改商品信息重新占用库存失败!" + e.getMessage());
            return vh;
        }
    }

    /**
     * 库存平移服务
     * 作废原共享占用单、逻辑占用单，生成新共享占用单、逻辑占用单
     *
     * @param request 原单信息、新单信息、新单明细
     * @return 平移结果
     */
    public ValueHolderV14<String> stoTranslation(SgOmsStoTranslationRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("{}库存平移服务入参{}", this.getClass().getName(), JSON.toJSONString(request));
        }

        ValueHolderV14<String> result = new ValueHolderV14<>();
        try {
            ValueHolderV14 sgResult = SgOmsStoTranslationCmd.stoTranslation(request);
            result.setCode(sgResult.getCode());
            result.setMessage(sgResult.getMessage());
        } catch (Exception e) {
            log.error("库存平移服务失败{}", e.getMessage(), e);
            result.setCode(ResultCode.FAIL);
            result.setMessage(e.getMessage());
        }
        return result;
    }

    /**
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     * <AUTHOR>
     * @Description JITX寻仓订单 寻仓/查询有效库存
     * @Date 2019-9-4
     * @Param [timeOrderRelation]
     **/
    public ValueHolderV14 queryTimeSendWareHouseStock(IpVipTimeOrderRelation timeOrderRelation) {

        ValueHolderV14 vh = new ValueHolderV14();
        try {
            // SgSumStoragePageQueryRequest request = new SgSumStoragePageQueryRequest();
            //取出店铺同步策略下面的逻辑供货仓
            List<Long> stStockPriorityRequests = syncStockStrategyService.queryShopStoreNextList(timeOrderRelation
                    .getIpBTimeOrderVip().getCpCShopId());

            if (CollectionUtils.isEmpty(timeOrderRelation.getIpBTimeOrderVipItemExList())) {
                vh.setCode(-1);
                vh.setMessage(timeOrderRelation.getOrderId() + "_订单的明细为空!");
                return vh;
            }
            if (CollectionUtils.isEmpty(stStockPriorityRequests)) {
                vh.setCode(-1);
                vh.setMessage(timeOrderRelation.getOrderId() + "该店铺逻辑发货仓为空!");
                return vh;
            }

            Map<Long, String> skuCodeMap = timeOrderRelation.getIpBTimeOrderVipItemExList()
                    .stream().filter(it -> it.getProdSku() != null && StringUtils.isNotEmpty(it.getProdSku().getSkuEcode()))
                    .map(item -> item.getProdSku()).collect(Collectors.toMap(ProductSku::getId, ProductSku::getSkuEcode, (v1, v2) -> v1));
            return this.querySendWareHouseStockByRedis(timeOrderRelation.getOrderId(), stStockPriorityRequests, skuCodeMap);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format("查询有效库存服务执行失败 " +
                            "queryStockSgBStorage 异常信息！{},OrderId=", timeOrderRelation.getOrderId()),
                    Throwables.getStackTraceAsString(e));
            vh.setCode(-1);
            vh.setMessage("订单Id" + timeOrderRelation.getOrderId() + "查询有效库存服务执行失败!" + e.getMessage());
            return vh;
        }
    }

    /**
     * 查询出库通知单
     *
     * @param no 出库通知单号
     * @return vh
     */
    public ValueHolderV14<List> querySgPhyOutNoticeByOutNoticeNo(String no) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("新增出库通知单查询库存中心入参{}", no));
        }
        ValueHolderV14<List> vh = new ValueHolderV14<>();
        try {
            //SgPhyOutQueryRequest request = new SgPhyOutQueryRequest();
            List<String> list = new ArrayList<>();
            list.add(no);
            //request.setBillNoList(list);

            //vh = sgPhyOutNoticesQueryCmd.queryOutNotice(request);
            return testResult(vh);

        } catch (Exception ex) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("查询出库通知单发生异常");
            log.error(LogUtil.format("查询出库通知单发生异常{}"), Throwables.getStackTraceAsString(ex));
            return vh;
        }
    }

    private ValueHolderV14<List> testResult(ValueHolderV14<List> vh) {
        if (vh == null) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("查询出库通知单结果返回null");
            return vh;
        }
        return vh;
    }

    /**
     * @param ids
     * @param user
     * @return
     */
    public ValueHolderV14<List<SgOutResultSendMsgResult>> querySgPhyOutResultJITXMQBody(List<Long> ids, User user) {
        return null;//sgPhyOutQueryCmd.querySgPhyOutResultJITXMQBody(ids, user);
    }

    /**
     * 查询逻辑仓库存
     *
     * @param request   request
     * @param loginUser 操作人
     * @return
     */
    public ValueHolderV14<List<SgBStorage>> queryStorage(SgStorageQueryRequest request, User loginUser) {
        return sgStorageQueryCmd.queryStorage(request, loginUser);
    }

    /**
     * 查询实体仓库存
     *
     * @param skuIds        skuIds
     * @param shopStoreList 查找店铺下面的逻辑供货仓
     * @param warehouseId   实体仓id
     * @param loginUser     操作人
     * @return
     */
    public ValueHolderV14<List<SgBStorageInclShare>> querySumStorageGrpPhyByRedis(List<Long> skuIds,
                                                                                  List<Long> shopStoreList,
                                                                                  Long warehouseId,
                                                                                  User loginUser) {
        //组装明细参数
        SgStorageQueryRequest sumStorageQueryRequest = new SgStorageQueryRequest();
        sumStorageQueryRequest.setSkuIds(skuIds);
        sumStorageQueryRequest.setStoreIds(shopStoreList);
        sumStorageQueryRequest.setPhyWarehouseIds(Lists.newArrayList(warehouseId));
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("缺货查询库存服务入参:{};"), JSONObject.toJSONString(sumStorageQueryRequest));
        }
        return sgStorageQueryCmd.queryStorageInclShare(sumStorageQueryRequest, loginUser);
    }

    /**
     * @param billNo
     * @return com.jackrain.nea.sys.domain.ValueHolderV14<java.lang.Long>
     * <AUTHOR>
     * @Description 根据出库通知单编码获取来源单据id
     * @Date 13:04 2020/7/7
     **/
    public ValueHolderV14<Long> querySourceIdByBillNo(String billNo) {
        ValueHolderV14<Long> hv = null;
        if (hv == null) {
            hv = new ValueHolderV14<>();
            hv.setCode(ResultCode.FAIL);
            hv.setMessage("RPC根据出库通知单编码获取来源单据id失败");
        }
        return hv;
    }

    @Deprecated
    public ValueHolderV14 releaseInventory(List<OcBOrderParam> orderParam, User user) {
        return null;
    }

    /**
     * 寻仓库存接口
     *
     * @param storeIdAndPhyWarehouseIdMap 逻辑仓ID和实体仓ID对应关系map
     * @param skuIdList                   sku集合
     * @return 库存查询结果
     */
    public ValueHolderV14<List<SgBStorageInclShare>> selectStoreWarehouseSkuNum(List<Long> storeIdAndPhyWarehouseIdMap, List<Long> skuIdList) {
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + "寻仓库存入参：{}/{}", JSON.toJSONString(storeIdAndPhyWarehouseIdMap), JSON.toJSONString(skuIdList));
        }
        SgStorageQueryRequest sgSumStorageQueryRequest = new SgStorageQueryRequest();
        sgSumStorageQueryRequest.setPhyWarehouseIds(storeIdAndPhyWarehouseIdMap);
        sgSumStorageQueryRequest.setSkuIds(skuIdList);
        ValueHolderV14<List<SgBStorageInclShare>> result = sgStorageQueryCmd.queryStorageInclShare(sgSumStorageQueryRequest, R3SystemUserResource.getSystemRootUser());
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + "寻仓库存出参：{}", JSON.toJSONString(result));
        }
        return result;
    }

    /**
     * 查询sku可用库存
     *
     * @param skuIdList
     * @param loginUser
     * @return
     */
    public List<SgBStorageInclShare> getQtyAvailableList(List<Long> skuIdList, User loginUser) {
        List<SgBStorageInclShare> resultList = null;
        SgStorageQueryRequest request = new SgStorageQueryRequest();
        request.setSkuIds(skuIdList);
        try {
            ValueHolderV14<List<SgBStorageInclShare>> vh14 = sgStorageQueryCmd.queryStorageInclShare(request, loginUser);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("查询sku可用库存结果：{}"), JSON.toJSONString(vh14));
            }
            if (vh14.isOK()) {
                resultList = vh14.getData();
            } else {
                log.error(LogUtil.multiFormat("查询sku可用库存，失败：{}", skuIdList), vh14.getMessage());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询sku可用库存，异常：") + Throwables.getStackTraceAsString(e));
        }
        return resultList;
    }

    /**
     * 构建占单relation
     *
     * @param order
     * @param items
     * @return
     */
    public static OmsOccupyRelation getOmsOccupyRelation(OcBOrder order, List<OcBOrderItem> items) {
        //调用占用库存服务。占用成功后调用订单日志服务
        OmsOccupyRelation omsOccupyRelation = new OmsOccupyRelation();
        omsOccupyRelation.setOcBOrder(order);
        omsOccupyRelation.setOcBOrderItems(items);
        omsOccupyRelation.setSpecChange(isFullyGoods(order.getCpCShopId()));
        return omsOccupyRelation;
    }

    /**
     * 是否整单有货占单 true:整单占 false:部分占
     *
     * @param cpCShopId
     * @return
     */
    public static boolean isFullyGoods(Long cpCShopId) {
        if (cpCShopId == null) {
            return false;
        }
        OmsStCShopStrategyService shopStrategyService = ApplicationContextHandle.getBean(OmsStCShopStrategyService.class);
        //缺货时，判断店铺占用库存策略
        StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(cpCShopId);
        if (shopStrategy != null) {
            return "Y".equals(shopStrategy.getIsFullyGoods());
        } else {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("店铺策略不存在，cpCShopId:", cpCShopId));
            }
        }
        return false;
    }

    /**
     * @param ids      虚拟条码ids
     * @param storeIds 逻辑仓ids
     * @return
     */
    public ValueHolderV14<List<SgGroupStorageQueryResult>> queryGroupStorage(List<Long> ids, List<Long> storeIds) {
        List<SgGroupStorageQueryRequest> requests = Lists.newArrayList();
        for (Long id : ids) {
            SgGroupStorageQueryRequest request = new SgGroupStorageQueryRequest();
            request.setId(id);
            request.setStoreIds(storeIds);
            requests.add(request);
        }
        return sgGroupStorageQueryCmd.queryGroupStorage(requests);
    }

    public List<SgBChannelAdvanceSaleReleaseResult> querySgBChannelAdvanceSale(SgBChannelAdvanceSaleBillReleaseRequest request) {
        log.info(LogUtil.format("查询渠道预售活动计划入参是,param:{}", "渠道预售活动计划"), request);
        ValueHolderV14<List<SgBChannelAdvanceSaleReleaseResult>> vh = sgBChannelAdvanceSaleCmd.releaseChannelAdvanceSale(request);
        log.info(LogUtil.format("querySgBChannelAdvanceSale.出参是{}"), vh);
        if (vh.isOK()) {
            return vh.getData();
        }
        return null;
    }

    /**
     * 新增并审核调拨单服务
     *
     * @param sgTransferBillSaveRequest 参数
     * @return 结果
     */
    public ValueHolderV14 saveAndSubmit(SgBStoTransferBillSaveRequest sgTransferBillSaveRequest) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("新增并审核调拨单成功！");
        try {
            valueHolderV14 = transferSaveAndSubmitCmd.saveAndSubmit(sgTransferBillSaveRequest);
        } catch (Exception e) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage("调用新增并审核调拨单服务异常！");
        }
        return valueHolderV14;
    }

    /**
     * 多商品释放库存
     *
     * @param request 订单、商品明细、用户信息
     * @return 是否成功
     */
    public ValueHolderV14<String> releaseOutStock(SgOmsReleaseOutRequest request) {
        ValueHolderV14<String> result = new ValueHolderV14<>(ResultCode.SUCCESS, "释放成功");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("多商品释放库存入参：{}"), JSON.toJSONString(request));
        }
        try {
            ValueHolderV14 sgResult = sgOmsReleaseOutCmd.release(request);
            result.setCode(sgResult.getCode());
            result.setMessage(sgResult.getMessage());
        } catch (Exception e) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("释放库存异常：" + e.getMessage());
        }
        return result;
    }

    /**
     * 多商品释放库存
     *
     * @param requests 订单、商品明细、用户信息
     * @return 是否成功
     */
    public ValueHolderV14<String> batchReleaseOutStock(List<SgOmsReleaseOutRequest> requests) {
        ValueHolderV14<String> result = new ValueHolderV14<>(ResultCode.SUCCESS, "释放成功");
        if (log.isDebugEnabled()) {
            log.debug(" 批量多商品释放库存入参：{}", JSON.toJSONString(requests));
        }
        try {
            ValueHolderV14 sgResult = sgOmsReleaseOutCmd.batchRelease(requests);
            result.setCode(sgResult.getCode());
            result.setMessage(sgResult.getMessage());
        } catch (Exception e) {
            result.setCode(ResultCode.FAIL);
            result.setMessage(" 释放库存异常：" + e.getMessage());
        }
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 16:13 2021/7/21
     * @Description 查询平台店铺实体仓信息/ 聚合仓信息
     */
    public ValueHolderV14<PageInfo<SgOmsShopPhyQueryResult>> queryOmsShopShareAndPhyWarehouse(SgOmsShopShareAndPhyQueryRequest request, User loginUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用查询平台店铺实体仓信息， 聚合仓信息服务SgOmsStandardCmd.release入参：{}"), JSON.toJSONString(request));
        }
        ValueHolderV14<PageInfo<SgOmsShopPhyQueryResult>> sgResult = new ValueHolderV14<>();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用查询平台店铺实体仓信息， 聚合仓信息服务SgOmsStandardCmd.release返回结果：{}"),
                    JSONObject.toJSONString(sgResult));
        }
        return sgResult;
    }

    /**
     * <AUTHOR>
     * @Date 18:08 2021/7/22
     * @Description 指定实体仓占用
     */
    public ValueHolderV14 phyStorageOut(SgOmsPhyStorageOutRequest request) {
        return null;
    }

    /**
     * 共享占用单作废
     *
     * @param request 共享占用单作废
     * @return 是否成功
     */
    public ValueHolderV14 voidSgOmsShareOut(SgOmsShareOutRequest request, OcBOrder order, List<OcBOrderItem> ocBOrderItems) {
        ValueHolderV14 result = new ValueHolderV14<>(ResultCode.SUCCESS, "作废成功");
        ValueHolderV14 sgResult;
        try {
            boolean isCc = OmsBusinessTypeUtil.isToBOrder(order) && OrderSaleProductAttrEnum.isToBCC(order.getSaleProductAttr());
            if (isCc || OmsOrderUtil.isToCCcOrder(order)) {
                // 作废冻结占用单
                sgResult = this.voidFreezeOutOrder(order, request.getLoginUser());
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("调用共享占用单作废库存服务sgOmsShareOutVoidCmd.voidSgOmsShareOut入参：{}", "调用共享占用单作废库存服务入参"),
                            JSON.toJSONString(request));
                }
                sgResult = sgOmsShareOutVoidCmd.voidSgOmsShareOut(request);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("调用共享占用单作废库存服务sgOmsShareOutVoidCmd.voidSgOmsShareOut返回结果：{}", "调用共享占用单作废库存服务出参"),
                            JSONObject.toJSONString(sgResult));
                }
            }
            try {
                if (sgResult.isOK() && order != null) {
                    //处理贴纸赠品
                    List<OcBOrderItem> stickerGiftItems = ocBOrderItems.stream()
                            .filter(p -> p.getStickerGift() != null && p.getStickerGift().equals(YesNoEnum.Y.getVal()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(stickerGiftItems)) {
                        // 获取贴纸赠品的ID列表
                        List<Long> itemIds = stickerGiftItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                        // 删除贴纸赠品明细
                        ocBOrderItemMapper.deleteOcBOrderItemById(order.getId(), itemIds);
                        // 更新订单明细列表，移除已删除的贴纸赠品
                        ocBOrderItems.removeAll(stickerGiftItems);
                        // 重新计算主表的总数量等字段
                        OcBOrder ocBOrder = new OcBOrder();
                        ocBOrder.setId(order.getId());
                        OrderUtil.handleOrderSku(ocBOrderItems, ocBOrder);
                        ocBOrder.setSkuKindQty(BigDecimal.valueOf(ocBOrderItems.size()));
                        BigDecimal weight = BigDecimal.ZERO;
                        for (OcBOrderItem item : ocBOrderItems) {
                            BigDecimal standardWeight = Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO);
                            BigDecimal qty = Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO);
                            weight = weight.add(standardWeight.multiply(qty));
                        }
                        ocBOrder.setWeight(weight);
                        ocBOrderMapper.updateById(ocBOrder);
                        // 记录日志
                        List<String> pscode = stickerGiftItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
                        String join = StringUtils.join(pscode, ",");
                        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                                OrderLogTypeEnum.GIFT_DEL.getKey(), "贴纸商品条码:" + join + "删除成功!", "", "", request.getLoginUser());
                    }
                }
            } catch (Exception e) {
                log.error("删除贴纸商品失败", e);
            }
            result.setCode(sgResult.getCode());
            result.setMessage(sgResult.getMessage());
        } catch (Exception e) {
            result.setCode(ResultCode.FAIL);
            result.setMessage("共享占用单作废异常：" + e.getMessage());
        }
        return result;
    }

    /**
     * <AUTHOR>
     * @Date 12:40 2021/8/6
     * @Description 寻源后回传给库存接口拆单
     */
    public ValueHolderV14 sgCallBackSplitOrder(SgOmsSplitOrderRequest request) {
        ValueHolderV14 result = new ValueHolderV14<>(ResultCode.SUCCESS, "回传给库存接口拆单成功");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用寻源后回传给库存接口拆单sgCallBackSplitOrder.splitOrder入参：{}"), JSON.toJSONString(request));
        }
        ValueHolderV14 sgResult = sgOmsSplitOrderCmd.execute(request);
        result.setCode(sgResult.getCode());
        result.setMessage(sgResult.getMessage());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用寻源后回传给库存接口拆单sgCallBackSplitOrder.splitOrder：{}"), JSON.toJSONString(sgResult));
        }
        return result;
    }

    /**
     * 取消共享层占用
     *
     * @param request
     * @return
     */
    public ValueHolderV14 voidShareOut(SgBShareOutBillVoidRequest request) {
        ValueHolderV14 result = new ValueHolderV14<>(ResultCode.SUCCESS, "取消共享层占用成功");
        if (log.isDebugEnabled()) {
            log.debug("调用取消共享层占用voidShareOut.request入参：{}", JSON.toJSONString(request));
        }
        ValueHolderV14 sgResult = sgBShareOutCmd.voidShareOut(request);
        if (log.isDebugEnabled()) {
            log.debug("调用取消共享层占用voidShareOut.result：{}", JSON.toJSONString(sgResult));
        }
        result.setCode(sgResult.getCode());
        result.setMessage(sgResult.getMessage());
        return result;
    }


    public List<SgBStoOutQueryResult> sgQuerySgBStoOutByIds(List ids) {
        List<SgBStoOutQueryResult> list = new ArrayList<>();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用sg查询占用逻辑接口sgQuerySgBStoOutByIds入参：{}"), JSON.toJSONString(ids));
        }
        ValueHolderV14<List<SgBStoOutQueryResult>> sgResult = sgBStoOutCmd.querySgBStoOutByOMS(ids);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.multiFormat("调用sg查询占用逻辑接口{},sgQuerySgBStoOutByIds：", ids), JSON.toJSONString(sgResult));
        }
        if (sgResult.isOK()) {
            list = sgResult.getData();
        }
        return list;
    }

    public SgBStoOutQueryResult querySgBStoOutByOrderId(Long ocBOrderId) {
        ValueHolderV14<SgBStoOutQueryResult> sgResult = sgBStoOutCmd.querySgBStoOutByOrderId(ocBOrderId);
        SgBStoOutQueryResult result = new SgBStoOutQueryResult();
        if (sgResult.isOK()) {
            result = sgResult.getData();
        }
        return result;
    }

    public List<SgBStoFreezeOutQueryResult> sgQuerySgBStoFreezeOutByIds(List<Long> ids) {
        List<SgBStoFreezeOutQueryResult> list = new ArrayList<>();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用sg查询占用逻辑接口sgQuerySgBStoOutByIds入参：{}"), JSON.toJSONString(ids));
        }
        ValueHolderV14<List<SgBStoFreezeOutQueryResult>> sgResult = sgOmsFreezeOutCmd.querySgStoFreezeOutByOms(ids);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.multiFormat("调用sg查询占用逻辑接口{},sgQuerySgBStoOutByIds：", ids), JSON.toJSONString(sgResult));
        }
        if (sgResult.isOK()) {
            list = sgResult.getData();
        }
        return list;
    }

    //根据skuid查询聚合仓
    public Map<Long, Map<Long, String>> sgQuerySgBShareOut(SgBShareOutQueryShareStoreRequest shareOutQueryShareStoreRequest) {
        if (log.isDebugEnabled()) {
            log.debug(" 调用sg查询聚合仓接口sgQuerySgBShareOut入参：{}", JSON.toJSONString(shareOutQueryShareStoreRequest));
        }
        ValueHolderV14<List<SgBShareOutQueryShareStoreResult>> listValueHolderV14 = sgBShareOutCmd.queryShareStoreBySource(shareOutQueryShareStoreRequest);
        if (log.isDebugEnabled()) {
            log.debug(" 调用sg查询聚合仓接口sgQuerySgBShareOut返参：{}", JSON.toJSONString(listValueHolderV14));
        }
        List<SgBShareOutQueryShareStoreResult> data = listValueHolderV14.getData();
        Map<Long, Map<Long, String>> shapMap = new HashMap<>();
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        for (SgBShareOutQueryShareStoreResult shareStoreResult : data) {
            Long psCSkuId = shareStoreResult.getPsCSkuId();
            Map<Long, String> longStringMap = shapMap.get(psCSkuId);
            if (MapUtils.isNotEmpty(longStringMap)) {
                longStringMap.put(shareStoreResult.getSgCShareStoreId(), shareStoreResult.getSgCShareStoreName());
                shapMap.put(psCSkuId, longStringMap);
            } else {
                Map<Long, String> hashMap = new HashMap<>();
                hashMap.put(shareStoreResult.getSgCShareStoreId(), shareStoreResult.getSgCShareStoreName());
                shapMap.put(psCSkuId, hashMap);
            }
        }
        return shapMap;
    }

    public List<SgBStoOutNotices> queryOutNotices(List<String> outBillNoList) {
        return sgBStoOutNoticesQueryByNoCmd.queryOutNotices(outBillNoList);
    }


    public ValueHolderV14 voidSgStockOccupy(OcBOrder order, List<OcBOrderItem> ocBOrderItems, User user) {
        ValueHolderV14 sgValueHolder;
        boolean isCc = OmsBusinessTypeUtil.isToBOrder(order) && OrderSaleProductAttrEnum.isToBCC(order.getSaleProductAttr());
        if (isCc || OmsOrderUtil.isToCCcOrder(order)) {
            // 作废冻结占用单
            sgValueHolder = this.voidFreezeOutOrder(order, user);
        } else {
            SgOmsShareOutRequest request = buildSgOmsShareOutRequest(order, ocBOrderItems, user);
            sgValueHolder = this.voidSgOmsShareOut(request, order, ocBOrderItems);
        }
        try {
            if (sgValueHolder.isOK()) {
                //处理贴纸赠品
                List<OcBOrderItem> stickerGiftItems = ocBOrderItems.stream()
                        .filter(p -> p.getStickerGift() != null && p.getStickerGift().equals(YesNoEnum.Y.getVal()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(stickerGiftItems)) {
                    // 获取贴纸赠品的ID列表
                    List<Long> itemIds = stickerGiftItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                    // 删除贴纸赠品明细
                    ocBOrderItemMapper.deleteOcBOrderItemById(order.getId(), itemIds);
                    // 更新订单明细列表，移除已删除的贴纸赠品
                    ocBOrderItems.removeAll(stickerGiftItems);
                    // 重新计算主表的总数量等字段
                    OcBOrder ocBOrder = new OcBOrder();
                    ocBOrder.setId(order.getId());
                    OrderUtil.handleOrderSku(ocBOrderItems, ocBOrder);
                    ocBOrder.setSkuKindQty(BigDecimal.valueOf(ocBOrderItems.size()));
                    BigDecimal weight = BigDecimal.ZERO;
                    for (OcBOrderItem item : ocBOrderItems) {
                        BigDecimal standardWeight = Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO);
                        BigDecimal qty = Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO);
                        weight = weight.add(standardWeight.multiply(qty));
                    }
                    ocBOrder.setWeight(weight);
                    ocBOrderMapper.updateById(ocBOrder);
                    // 记录日志
                    List<String> pscode = stickerGiftItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toList());
                    String join = StringUtils.join(pscode, ",");
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                            OrderLogTypeEnum.GIFT_DEL.getKey(), "贴纸商品条码:" + join + "删除成功!", "", "", user);
                }
            }
        } catch (Exception e) {
            log.error("删除贴纸商品失败", e);
        }
        return sgValueHolder;

    }

    /**
     * 创建入库通知单
     *
     * @param request
     * @param user
     * @return
     */
    public ValueHolderV14<SgBStoInNoticesBillSaveResult> createSgBStoInNotice(SgBStoInNoticesBillSaveRequest request, User user) {
        ValueHolderV14 result = new ValueHolderV14<>(ResultCode.SUCCESS, "创建入库通知单成功！");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("创建入库通知单,request={}",
                    "SgRpcService.createSgBStoInNotice"), JSON.toJSONString(request));
        }
        result = sgBStoInNoticesCmd.addSgStoInNotices(request, user);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("创建入库通知单,result={}",
                    "SgRpcService.createSgBStoInNotice"), JSON.toJSONString(result));
        }
        return result;
    }

    /**
     * @param returnOrder
     * @param user
     * @return
     */
    public ValueHolderV14 cancelNoticeInBil(OcBReturnOrder returnOrder, User user) {
        ValueHolderV14 valueHolderV14;
        try {
            SgBStoInNoticesBillVoidRequest stoInNoticesBillVoidRequest = new SgBStoInNoticesBillVoidRequest();
            stoInNoticesBillVoidRequest.setSourceBillId(returnOrder.getId());
            stoInNoticesBillVoidRequest.setSourceBillNo(returnOrder.getBillNo());
            stoInNoticesBillVoidRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);
            valueHolderV14 = sgBStoInNoticesCmd.voidInNotices(stoInNoticesBillVoidRequest, user);
            if (valueHolderV14 == null) {
                valueHolderV14 = new ValueHolderV14(ResultCode.FAIL, "撤回失败,返回null");
            }
        } catch (Exception e) {
            valueHolderV14 = new ValueHolderV14(ResultCode.FAIL, "撤回失败, 异常");
            log.error(LogUtil.format("撤回异常: {}"), Throwables.getStackTraceAsString(e));
        }
        return valueHolderV14;
    }


    /**
     * 生成冲无头件调整单
     *
     * @param user
     * @return
     */
    public boolean generateMinusAdjust(JSONObject jsn, User user) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("无名件负向库存调整单.start:{}", "ReturnInMatch"), JSON.toJSONString(jsn));
            }
            ValueHolderV14 vh = sgOmsOffsetAdjustCmd.offsetAdjust(jsn, user);
            AssertUtil.notNull(vh, "sgOmsOffsetAdjustCmd return is null");
            AssertUtil.isTrue(vh.isOK(), vh.getMessage());
            return vh.isOK();
        } catch (Exception e) {
            log.error(LogUtil.format("无名件负向库存调整单.exp: {}", "ReturnInMatch"), Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    /**
     * 生成入库通知单和逻辑入库单，不生成反向库存调整单
     *
     * @param user
     * @return
     */
    public boolean generateMinusAdjustForFl(JSONObject jsn, User user) {
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("无名件负向库存调整单.start:{}", "ReturnInMatch"), JSON.toJSONString(jsn));
            }
            ValueHolderV14 vh = sgOmsOffsetAdjustCmd.offsetAdjustForFl(jsn, user);
            AssertUtil.notNull(vh, "sgOmsOffsetAdjustCmd return is null");
            AssertUtil.isTrue(vh.isOK(), vh.getMessage());
            return vh.isOK();
        } catch (Exception e) {
            log.error(LogUtil.format("无名件负向库存调整单.exp: {}", "ReturnInMatch"), Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    /**
     * 查询平台店铺商品表
     *
     * @param sgChannelProductQueryRequest 请求体
     * @return ValueHolderV14<List < SgBChannelProduct>>
     */
    public ValueHolderV14<List<SgBChannelProduct>> queryChannelProduct(SgChannelProductQueryRequest sgChannelProductQueryRequest) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("queryChannelProduct.request{}", "queryChannelProduct.request"), JSON.toJSONString(sgChannelProductQueryRequest));
        }
        try {
            ValueHolderV14<List<SgBChannelProduct>> v14 = sgChannelProductQueryCmd.queryChannelProduct(sgChannelProductQueryRequest);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("queryChannelProduct.result{}", "queryChannelProduct.result"), JSON.toJSONString(v14));
            }
            return v14;
        } catch (Exception e) {
            log.error(LogUtil.format("queryChannelProduct.error{}", "queryChannelProduct.error"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
    }

    public ValueHolderV14<List<SgOmsStoInNoticeQueryResult>> queryInNotice(SgOmsStoInNoticeQueryRequest request) {
        try {
            ValueHolderV14<List<SgOmsStoInNoticeQueryResult>> result = sgOmsStoInNoticeAndResultCmd.queryInNotice(request);
            log.info(LogUtil.format("queryInNotice.result{}", "queryInNotice.result"), JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error(LogUtil.format("queryInNotice.error{}", "queryInNotice.error"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
    }
}
