package com.jackrain.nea.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
//import com.jackrain.nea.std.api.cmd.StdPlatformOrderCmd;
//import com.jackrain.nea.std.api.cmd.StdPlatformRefundCmd;
//import com.jackrain.nea.std.api.cmd.dto.StdResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description: 调用私域服务
 * @date 2021/12/6 14:19
 */
@Component
@Slf4j
public class StdRpcService {

//    @Reference(group = "std", version = "1.0")
//    private StdPlatformOrderCmd stdPlatformOrderCmd;
//
//    @Reference(group = "std",version = "1.0")
//    private StdPlatformRefundCmd stdPlatformRefundCmd;


    public ValueHolderV14 cancel(JSONObject param, int platform) {
        log.info("{}.调用私域取消入参：{}", this.getClass().getSimpleName(), param);
        /*StdResult stdResult;
        try {
            stdResult = stdPlatformOrderCmd.cancel(param, platform);
        }catch (Exception e){
            stdResult = StdResult.fail("调用stdPlatformOrderCmd.cancel异常，异常详情："+e.getMessage());
        }
        log.info("{}.调用私域取消结果：{}", this.getClass().getSimpleName(), JSON.toJSONString(stdResult));
        return ValueHolderV14Utils.custom(stdResult.getIsSuccess() ? ResultCode.SUCCESS : ResultCode.FAIL, stdResult.getErrorMsg(), stdResult.getData());*/
        return null;
    }

    public ValueHolderV14 delivery(JSONObject param, int platform) {
        log.info("{}.调用私域发货入参：{}", this.getClass().getSimpleName(), param);
        return null;
        /*StdResult stdResult;
        try {
            stdResult = stdPlatformOrderCmd.delivery(param, platform);
        }catch (Exception e){
            stdResult = StdResult.fail("调用stdPlatformOrderCmd.delivery异常，异常详情："+e.getMessage());
        }
        log.info("{}.调用私域发货结果：{}", this.getClass().getSimpleName(), JSON.toJSONString(stdResult));
        return ValueHolderV14Utils.custom(stdResult.getIsSuccess() ? ResultCode.SUCCESS : ResultCode.FAIL, stdResult.getErrorMsg(), stdResult.getData());*/
    }

    public ValueHolderV14 refundApprove(JSONObject param, int platform){
        log.info("{}.调用私域退单审核入参：{}", this.getClass().getSimpleName(), param);
        return null;
        /*StdResult stdResult;
        try {
            stdResult = stdPlatformRefundCmd.refundApprove(param, platform);
        }catch (Exception e){
            stdResult = StdResult.fail("调用stdPlatformRefundCmd.refundApprove异常，异常详情："+e.getMessage());
        }
        log.info("{}.调用私域退单审核结果：{}", this.getClass().getSimpleName(), JSON.toJSONString(stdResult));
        return ValueHolderV14Utils.custom(stdResult.getIsSuccess() ? ResultCode.SUCCESS : ResultCode.FAIL, stdResult.getErrorMsg(), stdResult.getData());*/
    }

    public ValueHolderV14 refundInNotice(JSONObject param, int platform){
        log.info("{}.调用私域退单入库通知入参：{}", this.getClass().getSimpleName(), param);
        return null;
        /*StdResult stdResult;
        try {
            stdResult = stdPlatformRefundCmd.refundInNotice(param, platform);
        }catch (Exception e){
            stdResult = StdResult.fail("调用stdPlatformRefundCmd.refundInNotice异常，异常详情："+e.getMessage());
        }
        log.info("{}.调用私域退单入库通知结果：{}", this.getClass().getSimpleName(), JSON.toJSONString(stdResult));
        return ValueHolderV14Utils.custom(stdResult.getIsSuccess() ? ResultCode.SUCCESS : ResultCode.FAIL, stdResult.getErrorMsg(), stdResult.getData());*/
    }
}
