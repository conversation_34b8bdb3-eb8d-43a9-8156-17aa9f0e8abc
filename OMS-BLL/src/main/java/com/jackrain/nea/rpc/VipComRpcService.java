package com.jackrain.nea.rpc;

import com.jackrain.nea.oc.oms.model.jitx.JitxOrderLog;
import com.jackrain.nea.oc.oms.vip.api.VipcomLogCmd;
import com.jackrain.nea.request.OcBVipcomReturnPoRequest;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.vip.api.VipcomReturnPoQueryCmd;
import com.jackrain.nea.vip.api.VipcomReturnPoSaveCmd;
import com.jackrain.nea.vip.model.OcBVipcomReturnPoDO;
import com.jackrain.nea.vip.model.OcBVipcomReturnPoItemDO;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since: 2020/11/18
 * create at : 2020/11/18 下午5:30
 */
@Slf4j
@Component
public class VipComRpcService {

    @Reference(group = "vip", version = "1.0")
    private VipcomLogCmd vipcomLogCmd;

    @Reference(group = "vip", version = "1.0")
    private VipcomReturnPoSaveCmd vipcomReturnPoSaveCmd;

    @Reference(group = "vip", version = "1.0")
    private VipcomReturnPoQueryCmd vipcomReturnPoQueryCmd;

    public void addLog(JitxOrderLog jitxOrderLog) {
        vipcomLogCmd.add(jitxOrderLog.getBillId(), jitxOrderLog.getBillType(), jitxOrderLog.getBillNo(),
                jitxOrderLog.getLogType(), jitxOrderLog.getParam(), jitxOrderLog.getMessage(), jitxOrderLog.getOperateUser());
    }

    /**
     * 新增/保存 唯品会退供PO单
     * @param vipcomReturnPoRequest
     * @return
     */
    public ValueHolder saveVipcomReturnPo(OcBVipcomReturnPoRequest vipcomReturnPoRequest) {
        return vipcomReturnPoSaveCmd.saveVipcomReturnPo(vipcomReturnPoRequest);
    }

    /**
     * 根据唯品会退供PO单主表id查询条码明细数据
     */
    public List<OcBVipcomReturnPoItemDO> selectVipcomReturnPoItems(Long id) {
        return vipcomReturnPoQueryCmd.selectVipcomReturnPoItems(id);
    }

    /**
     * 新增成功 更新唯品会退供PO单状态及调拨单号
     * @param id
     * @param status
     * @param transferBillId
     * @param transferBillNo
     * @param user
     * @return
     */
    public void updateStatusAndtransfer(Long id, String status,
                                            Long transferBillId,
                                            String transferBillNo, User user) throws Exception {
        vipcomReturnPoSaveCmd.updateStatusAndtransfer(id, status, transferBillId, transferBillNo, user);
    }

    /**
     * 新增失败 更新唯品会退供PO单失败次数和原因
     * @param id
     * @param failReason
     * @param user
     * @return
     */
    public void updatefailNumAndReason(Long id, String failReason, User user) throws Exception {
        vipcomReturnPoSaveCmd.updatefailNumAndReason(id, failReason, user);
    }

    /**
     * 根据退供单号和po号查询唯品会退供PO单
     */
    public Boolean selectVipcomReturnPo(String returnNo, String po) {
        OcBVipcomReturnPoDO vipcomReturnPoDO = vipcomReturnPoQueryCmd.selectVipcomReturnPo(returnNo, po);
        if (vipcomReturnPoDO != null) {
            return true;
        }
        return false;
    }
}
