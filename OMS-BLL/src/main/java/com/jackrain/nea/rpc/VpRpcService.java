package com.jackrain.nea.rpc;


import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: heliu
 * @since: 2019/7/1
 * create at : 2019/7/1 14:17
 */
@Component
@Slf4j
public class VpRpcService {
//
//    @Reference(group = "vp", version = "1.5")
//    private VpBBlacklistQueryCmd vpBBlacklistQueryCmd;
//    @Reference(group = "vp", version = "1.5")
//    private VpBBlacklistCmd vpBBlacklistCmd;
//    @Reference(group = "vp", version = "1.5")
//    private VpVipMemberQueryCmd vipMemberQueryCmd;
//
//    /**
//     * 判断是否为黑名单
//     *
//     * @param orderId       订单Id
//     * @param receiverPhone 用户手机
//     * @param platform      平台类型
//     * @return boolean
//     */
//    public ValueHolderV14 queryBlackList(Long orderId, String receiverPhone, Integer platform) {
//
//        ValueHolderV14 vh = new ValueHolderV14();
//        try {
//            VpBBlacklistRequest vpBBlacklistRequest = new VpBBlacklistRequest();
//            VpBBlacklistDO vpBBlacklistDO = new VpBBlacklistDO();
//            vpBBlacklistDO.setBuyerMobile(receiverPhone);
//            vpBBlacklistDO.setCpCPlatformId(Long.valueOf(platform));
//            vpBBlacklistRequest.setVpBBlacklistDO(vpBBlacklistDO);
//            if (log.isDebugEnabled()) {
//                log.debug("OrderId[" + orderId + "],获取黑名单服务入参:" + JSONObject.toJSONString(vpBBlacklistRequest) + ";");
//            }
//            ValueHolder valueHolder = vpBBlacklistQueryCmd.vpBBlackQueryByPhoneAndPlatForm(vpBBlacklistRequest);
//            if (log.isDebugEnabled()) {
//                log.debug("OrderId[" + orderId + "],获取黑名单服务出参:" + valueHolder.toJSONObject().toJSONString() + ";");
//            }
//            JSONObject result = valueHolder.toJSONObject();
//            log.debug("OrderId[" + orderId + "],返回result" + result);
//            if (valueHolder.isOK()) {
//                boolean blackStatus = result.getBoolean("blackStatus");
//                log.debug("OrderId[" + orderId + "],blackStatus:" + blackStatus);
//                //true为黑名单 false不是黑名单
//                if (blackStatus) {
//                    vh.setCode(0);
//                    vh.setMessage(orderId + "该用户为黑名单!");
//                } else {
//                    vh.setCode(1);
//                    vh.setMessage(orderId + "该用户不是黑名单!");
//                }
//            } else {
//                vh.setCode(-1);
//                vh.setMessage(orderId + "调用查询黑名单服务异常!-->" + result.getString("message"));
//            }
//            return vh;
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            log.error("OrderId[" + orderId + "]调用查询黑名单服务 queryBlackList 异常信息-->", ex);
//            vh.setCode(-1);
//            vh.setMessage(orderId + "调用查询黑名单服务异常!" + ex.getMessage());
//            return vh;
//        }
//    }
//
//    public ValueHolder insertVpBBlacklist(User user, VpBBlacklistRequest request) {
//        return vpBBlacklistCmd.insertVpBBlacklist(user, request);
//    }
//
//    public VpBVipMemberDO selectVipMember(String buyerNick, long shopId) {
//        String redisKey = OmsRedisKeyResources.buildVipMemberByNickAndShopId(buyerNick, shopId);
//        CusRedisTemplate<String, VpBVipMemberDO> objRedisTemplate = RedisOpsUtil.getObjRedisTemplate();
//
//        Boolean hasKey = objRedisTemplate.hasKey(redisKey);
//        if (hasKey != null && hasKey) {
//            return objRedisTemplate.opsForValue().get(redisKey);
//        }
//        List<VpBVipMemberDO> memberList = vipMemberQueryCmd.selectVipMember(buyerNick, shopId);
//        if (CollectionUtils.isNotEmpty(memberList)) {
//            //存放在redis中
//            RedisOpsUtil.getObjRedisTemplate().opsForValue().set(redisKey, memberList.get(0));
//            return memberList.get(0);
//        } else {
//            return null;
//        }
//    }
}

