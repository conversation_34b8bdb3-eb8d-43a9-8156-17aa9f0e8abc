package com.jackrain.nea.intercept;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.log.service.LogService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.CompletableFuture;

/**
 * @Descroption 策略中心通用操作日志
 * <AUTHOR>
 * @Date 2020/2/7 15:50
 */
@Aspect
@Component
@Slf4j
public class OperationLogIntercept<T extends BaseModel> implements Ordered {
    @Autowired
    private LogService logService;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    /**
     * 日志切入点
     */
    @Pointcut("@annotation(com.jackrain.nea.annotation.OmsOperationLog)")
    public void logPointCut() {
    }

    @AfterReturning(value = "logPointCut()", returning = "returnValue")
    public void doAfter(JoinPoint joinPoint, Object returnValue) {
        /**
         * 解析Log注解
         */
        commonTaskExecutor.submit(() -> {
            try {
                ValueHolder valueHolder = getValueHolder(returnValue);
                ValueHolderV14 valueHolderV14 = getValueHolderV14(returnValue);
                if (valueHolder == null && valueHolderV14 == null) {
                    return;
                }
                if (valueHolder != null && ResultCode.SUCCESS != (int) valueHolder.getData().get("code")) {
                    return;
                }
                if (valueHolderV14 != null && ResultCode.SUCCESS != valueHolderV14.getCode()) {
                    return;
                }
                String methodName = joinPoint.getSignature().getName();
                Method method = currentMethod(joinPoint, methodName);
                if (method == null) {
                    log.error(LogUtil.format("method is null"));
                    return;
                }
                OmsOperationLog logObj = method.getAnnotation(OmsOperationLog.class);
                logService.saveLog(joinPoint, logObj);
            } catch (Exception e) {
                log.error(LogUtil.format("LogService.saveLog,error:{}")
                        , Throwables.getStackTraceAsString(e));
            }
        });
    }

    private ValueHolderV14 getValueHolderV14(Object param) {
        ValueHolderV14 vh = null;
        if (param instanceof ValueHolderV14) {
            vh = (ValueHolderV14) param;
        }
        return vh;
    }

    private ValueHolder getValueHolder(Object param) {
        ValueHolder vh = null;
        if (param instanceof ValueHolder) {
            vh = (ValueHolder) param;
        }
        return vh;
    }

    private Method currentMethod(JoinPoint joinPoint, String methodName) {
        /**
         * 获取目标类的所有方法，找到当前要执行的方法
         */
        Method[] methods = joinPoint.getTarget().getClass().getMethods();
        Method resultMethod = null;
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                resultMethod = method;
                break;
            }
        }
        return resultMethod;
    }

    @Override
    public int getOrder() {
        return 1002;
    }
}
