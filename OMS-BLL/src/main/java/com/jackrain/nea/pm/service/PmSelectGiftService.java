package com.jackrain.nea.pm.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.pm.model.request.GiftRequest;
import com.jackrain.nea.pm.model.request.GitProductRequest;
import com.jackrain.nea.pm.model.result.GiftResult;
import com.jackrain.nea.rpc.PmRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019-07-18 17:44
 * @Version 1.0
 */
@Slf4j
@Component
public class PmSelectGiftService {


    @Autowired
    private PmRpcService pmRpcService;

    /**
     * 返回所有赠品的sku code
     *
     * @param ocBOrderItems
     * @return
     */
    public ValueHolderV14<List<GiftResult>> selectGift(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems) {
        GiftRequest request = new GiftRequest();
        ValueHolderV14<List<GiftResult>> holder = new ValueHolderV14<>();
        try {
            request.setOrder(ocBOrder.getBillNo());   //单号
            request.setOrderTime(ocBOrder.getOrderDate());  //下单时间
            request.setOrderType(ocBOrder.getOrderType() + "");    //单据类型
            request.setPayTime(ocBOrder.getPayTime());  //付款时间
            request.setProvinceId(ocBOrder.getCpCRegionProvinceId()); //省id
            request.setShopId(ocBOrder.getCpCShopId()); //店铺id
            request.setWareHouseId(ocBOrder.getCpCPhyWarehouseId()); //实体仓id
            request.setWareHouseEcode(ocBOrder.getCpCPhyWarehouseEcode()); //实体仓id
            List<GitProductRequest> productList = new ArrayList<>();
            ocBOrderItems.forEach(p -> {
                GitProductRequest productRequest = new GitProductRequest();
                productRequest.setAmt_list(p.getPriceList()); //吊牌价
                productRequest.setAmt_retail(p.getPriceList()); //零售价
                BigDecimal qty = p.getQty();  //数量
                BigDecimal realAmt = p.getRealAmt(); //成交金额
                BigDecimal divide = realAmt.divide(qty, 2, BigDecimal.ROUND_HALF_UP);
                productRequest.setAmt_receivable(divide); //成交单价
                productRequest.setQtty(p.getQty().intValue());  //数量
                productRequest.setSku(p.getPsCSkuEcode()); //条码
                productRequest.setEcode(p.getPsCProEcode());  //商品的code
                productRequest.setPlatformEcode(p.getNumIid() + ""); //平台编码
                productRequest.setPlatformSku(p.getSkuNumiid()); //平台条码
                productList.add(productRequest);
            });
            request.setProductList(productList);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用促销获取赠品信息入参:{}", "selectGift"), JSONObject.toJSONString(request));
            }
            holder = pmRpcService.selectGift(request);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用促销获取赠品信息返回参数:{}", "selectGift"), JSONObject.toJSONString(holder));
            }
        } catch (Exception e) {
            holder.setCode(-1);
            holder.setMessage(e.getMessage());
            log.error(LogUtil.format("调用促销获取赠品失败！:{}", "selectGift"), Throwables.getStackTraceAsString(e));
        }
        return holder;
    }
}
