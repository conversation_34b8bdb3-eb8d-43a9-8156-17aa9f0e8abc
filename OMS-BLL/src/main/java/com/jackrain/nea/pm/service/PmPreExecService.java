package com.jackrain.nea.pm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.rpc.PmRpcService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @Author: 胡林洋
 * @Date: 2020-03-28 17:19
 * @Version 1.0
 */
@Slf4j
@Component
public class PmPreExecService {


    @Autowired
    private PmRpcService pmRpcService;

    /**
     * 调用促销预处理接口
     *
     * @param requestParam
     * @return
     */
    public ValueHolder callPreExecRpc(JSONObject requestParam) {
        ValueHolder holder = new ValueHolder();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用促销预执行接口信息入参:{}", "callPreExecRpc"), requestParam);
            }
            holder = pmRpcService.preExecRpc(requestParam);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用促销预执行接口返回参数:{}", "callPreExecRpc"), JSON.toJSONString(holder));
            }
        } catch (Exception e) {
            log.error(LogUtil.format("调用促销预执行接口失败:{}", "callPreExecRpc"), Throwables.getStackTraceAsString(e));
            holder.put("code", -1);
            holder.put("message", "调用促销预执行接口异常" + e.getMessage());
        }
        return holder;
    }

    /**
     * 调用促销预处理补偿接口
     *
     * @param requestParam
     * @return
     */
    public ValueHolder callpreExecMakeUpRpc(JSONObject requestParam) {

        ValueHolder holder = new ValueHolder();
        HashMap<String, String> map = new HashMap<String, String>();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用促销预执行补偿接口信息入参:{}", "callpreExecMakeUpRpc"), requestParam);
            }
            holder = pmRpcService.preExecRpc(requestParam);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用促销预执行补偿接口返回参数:{}", "callpreExecMakeUpRpc"), holder);
            }
        } catch (Exception e) {
            map.put("code", "-1");
            map.put("message", "调用促销预执行补偿接口异常！");
            holder.setData(map);
            log.error(LogUtil.format("调用促销预执行补偿接口异常！:{}", "callpreExecMakeUpRpc"), Throwables.getStackTraceAsString(e));
        }
        return holder;
    }

}
