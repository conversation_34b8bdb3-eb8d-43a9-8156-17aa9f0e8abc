package com.jackrain.nea.config;


import com.google.common.base.Throwables;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 业务系统
 *
 * @author: lahand
 * @since: 2021/3/18
 * create at : 2021/3/18 19:52
 */
@Slf4j
@Component
public class BusinessSystem {

    /**
     * 退货单定时任务传WMS数量
     */
    public static final String RETURN_ORDER_TRANSFER_WMS_NUM = "business_system:return_order_transfer_wms_num";

    /**
     * 退单wms回传中间表拉取数量
     */
    public static final String REFUND_IN_TASK_NUM = "business_system:refund_in_task_num";

    /**
     * 退换货单自动取消关键字
     */
    public static final String RETURN_ORDER_AUTO_CANCEL_REMARK = "business_system:return_order_auto_cancel_remark";

    /**
     * 订单系统内部备注
     */
    private static final String ORDER_INTERNAL_REMARK_ENUM = "business_system:order_internal_remark_enum";

    /**
     * HOLD单卡单超过结束间N天自动结案
     */
    private static final String ST_C_HOLD_ORDER_FINISH_DAYS = "business_system:st_c_hold_order_finish_days";
    /**
     * 商品效期策略超过结束间N天自动结案
     */
    private static final String ST_C_EXPIRY_DATE_FINISH_DAYS = "business_system:st_c_expiry_date_finish_days";

    /**
     * 商品效期策略访问控制配置
     */
    private static final String ST_C_EXPIRY_DATE_ACCESS_CONTROL = "business_system:st_c_expiry_date_access_control";


    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    /**
     * 退货单定时任务传WMS数量
     *
     * @return
     */
    public int getReturnOrderTransferWmsNum() {
        int num = 200;
        try {
            String numStr = redisUtil.strRedisTemplate.opsForValue().get(RETURN_ORDER_TRANSFER_WMS_NUM);
            if (StringUtils.isNotBlank(numStr)) {
                num = Integer.parseInt(numStr);
            }
        } catch (Exception e) {
            log.error("获取退货单定时任务每次传输WMS数量异常", e);
        }
        return num;
    }

    /**
     * 退单wms回传中间表拉取数量
     *
     * @return
     */
    public int getRefundInTaskNum() {
        int num = 200;
        try {
            String numStr = redisUtil.strRedisTemplate.opsForValue().get(REFUND_IN_TASK_NUM);
            if (StringUtils.isNotBlank(numStr)) {
                num = Integer.parseInt(numStr);
            }
        } catch (Exception e) {
            log.error("退单wms回传中间表拉取数量", e);
        }
        return num;
    }

    public String getReturnOrderAutoCancelRemark() {
        String remark = redisUtil.strRedisTemplate.opsForValue().get(RETURN_ORDER_AUTO_CANCEL_REMARK);
        return remark;
    }

    public String queryOrderInnerRemarkEnum() {
        String remark = redisUtil.strRedisTemplate.opsForValue().get(ORDER_INTERNAL_REMARK_ENUM);
        return remark;
    }

    public int getStCHoldOrderFinishDays() {
        int num = 7;
        try {
            String numStr = redisUtil.strRedisTemplate.opsForValue().get(ST_C_HOLD_ORDER_FINISH_DAYS);
            if (StringUtils.isNotBlank(numStr)) {
                num = Integer.parseInt(numStr);
                if (num < 0) {
                    num = 7;
                }
            }
        } catch (Exception e) {
            log.warn("HOLD单卡单超过结束间N天自动结案（默认7天）:{}", Throwables.getStackTraceAsString(e));
        }
        return num;
    }

    public int getStCExpiryDateFinishDays() {
        int num = 90;
        try {
            String numStr = redisUtil.strRedisTemplate.opsForValue().get(ST_C_EXPIRY_DATE_FINISH_DAYS);
            if (StringUtils.isNotBlank(numStr)) {
                num = Integer.parseInt(numStr);
                if (num < 0) {
                    num = 90;
                }
            }
        } catch (Exception e) {
            log.warn("商品效期策略超过结束间N天自动结案（默认7天）:{}", Throwables.getStackTraceAsString(e));
        }
        return num;
    }

    /**
     * 获取商品效期策略访问控制配置
     *
     * @return 访问控制配置列表
     */
    public List<String> getStCExpiryDateAccessControl() {
        try {
            String configStr = redisUtil.strRedisTemplate.opsForValue().get(ST_C_EXPIRY_DATE_ACCESS_CONTROL);
            if (StringUtils.isNotBlank(configStr)) {
                // 按逗号分割字符串，并去除空白字符
                String[] configArray = configStr.split(",");
                List<String> configList = Arrays.asList(configArray);
                // 去除每个元素的前后空白字符
                configList.replaceAll(String::trim);
                return configList;
            }
        } catch (Exception e) {
            log.warn("获取商品效期策略访问控制配置失败:{}", Throwables.getStackTraceAsString(e));
        }
        // 返回空列表作为默认值
        return Collections.emptyList();
    }

}