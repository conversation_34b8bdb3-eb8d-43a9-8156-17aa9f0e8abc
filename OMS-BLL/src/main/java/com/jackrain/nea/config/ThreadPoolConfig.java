package com.jackrain.nea.config;

import com.yomahub.tlog.core.thread.TLogInheritableCallWrapper;
import com.yomahub.tlog.core.thread.TLogInheritableRunWrapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Callable;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @ClassName ThreadPoolConfig
 * @Description 线程池配置类
 * <AUTHOR>
 * @Date 2022/12/27 20:21
 * @Version 1.0
 */
@Configuration
public class ThreadPoolConfig implements AsyncConfigurer {

    /**
     * 线程等待销毁时间（秒）
     */
    private static final int KEEP_ALIVE_TIME = 30;

    @Override
    public Executor getAsyncExecutor() {
        return commonTaskExecutor();
    }

    @Bean
    public ThreadPoolTaskExecutor commonTaskExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(16, 64, 100, "Oms-CommonPool-");
    }

    /**
     * 订单审核
     *
     * @return
     */
    @Bean
    public ThreadPoolTaskExecutor auditThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(30, 50, 64, "R3_OMS_ORDER_THE_AUDIT_POOL_");
    }

    /**
     * 订单hold单
     *
     * @return
     */
    @Bean
    public ThreadPoolTaskExecutor orderHoldThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(32, 40, 16, "R3_OMS_CREATE_MANUAL_UNHOLD_ORDER_THREAD_POOL_");
    }

    /**
     * 订单导入
     *
     * @return
     */
    @Bean
    public ThreadPoolTaskExecutor orderImportThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(16, 20, 16, "R3_OMS_IMPORT_ORDER_THREAD_POOL_");
    }

    /**
     * 预导入订单修改
     *
     * @return
     */
    @Bean
    public ThreadPoolTaskExecutor preOrderUpdateThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(16, 20, 16, "R3_OMS_SERIAL_PRE_ORDER_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor createReturnAfSendImportThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(32, 40, 16, "R3_OMS_CREATE_RETURNAFSENDIMPORTSERVICE_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor createReturnVirtualLibraryThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(32, 40, 16, "R3_OMS_CREATE_VIRTUALLIBRARYSERVICE_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor createDistributionLogisticsOrderThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(32, 40, 16, "R3_OMS_CREATE_DISTRIBUTION_LOGISTICS_ORDER_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor refundInMatchThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(10, 32, 16, "R3_OMS_REFUND_IN_MATCH_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor preOrderImportThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(16, 20, 16, "R3_OMS_IMPORT_PRE_ORDER_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor acFOrderInvoiceGetThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 40, "R3_OMS_AC_F_ORDER_INVOICE_GET_INVOICE_");
    }

    @Bean
    public ThreadPoolTaskExecutor orderSplitThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_ORDER_SPLIT_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor confirmedOrderThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_TO_CONFIRMED_ORDER_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor jdRefundToMqTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_JD_REFUND_ORDER_TO_MQ_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor toWmsPushDelayTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_ORDER_TO_WMS_PUSH_DELAY_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor preToWmsTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_PRE_TO_WMS_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor naikaUnfreezeThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_NAIKA_UNFREEZE_POOL_NAME_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor naikaAccountThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_NAIKA_ACCOUNT_POOL_NAME_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor naikaReturnInThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_NAIKA_RETURN_IN_POOL_NAME_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor occupyOrderTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_OCCUPY_ORDER_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor tbOrderTransferThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_TAO_BAO_ORDER_TRANSFER_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor nextTaoReturnTaskrThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_NEXT_TAO_RETURN_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor autoReleaseHangTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_AUTO_RELEASE_HANG_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor auditTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_AUDIT_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor auditManualThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 30, "R3_OMS_AUDIT_MANUAL_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor tbExchangeOrderToMqThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_TOBAO_EXCHANGE_ORDER_TO_MQ_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor vipTimeOrderTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_VIP_TIME_ORDER_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor dealerTaskMakeUpThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_DEALER_TASK_MAKE_UP_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor createPhynoticsTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_CREATE_PHYNOTICS_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor sendMsgTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_SEND_MSG_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor refundOrderToAgTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_REFUND_ORDER_TO_AG_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor settlementOrderThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_SETTLEMENT_ORDER_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor calculateSkuStockThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_CALCULATE_SKU_STOCK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor orderHoldItemTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_ORDER_HOLD_ITEM_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor acFInvoiceApplyThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_AC_F_INVOICE_APPLY_");
    }

    @Bean
    public ThreadPoolTaskExecutor acFOrderInvoiceThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_AC_F_ORDER_INVOICE_");
    }

    @Bean
    public ThreadPoolTaskExecutor sapSalesDataRecordAddThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_SAP_SALES_DATA_RECORD_ADD_");
    }

    @Bean
    public ThreadPoolTaskExecutor tbRefundOrderToMqTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(32, 32, 32, "R3_OMS_TOBAO_REFUND_ORDER_TO_MQ_TASK_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor standRefundOrderToMqTaskThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(32, 32, 32, "R3_OMS_STAND_REFUND_ORDER_TO_MQ_TASK_THREAD_POOL_");
    }


    @Bean
    public ThreadPoolTaskExecutor omsOrderSplitThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(32, 32, 32, "R3_OMS_ORDER_SPLIT_THREAD_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor orderEqualExchangeThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(10, 10, 32, "R3_OMS_ORDER_EQUAL_EXCHANGE_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor orderTheAuditStopThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(20, 40, 70, "R3_OMS_ORDER_THE_AUDIT_STOP_POOL_");
    }

    @Bean
    public ThreadPoolTaskExecutor commonThreadPoolExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 24, 24, "R3_OMS_COMMON_");
    }

    @Bean
    public ThreadPoolTaskExecutor orderDetentionPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(5, 20, 36, "R3_OMS_ORDER_DETENTION_");
    }

    @Bean
    public ThreadPoolTaskExecutor cardAutoVoidPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(5, 10, 24, "R3_OMS_CARD_AUTO_VOID_");
    }

    @Bean
    public ThreadPoolTaskExecutor doCardVoidPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(5, 10, 24, "R3_OMS_DO_CARD_VOID_");
    }

    @Bean
    public ThreadPoolTaskExecutor cancelOrderPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(5, 10, 24, "R3_OMS_CANCEL_ORDER_");
    }

    @Bean
    public ThreadPoolTaskExecutor doDeliveryFailRetryPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(5, 10, 24, "R3_OMS_DO_DELIVERY_RETRY_");
    }

    @Bean
    public ThreadPoolTaskExecutor doAutoExpireDatePollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(5, 10, 30, "R3_OMS_DO_AUTO_EXPIRE_DATE_");
    }

    @Bean
    public ThreadPoolTaskExecutor doBatchCancelReturnPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(10, 20, 30, "R3_OMS_BATCH_CANCEL_RETURN_");
    }

    @Bean
    public ThreadPoolTaskExecutor againOccupyPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(40, 40, 30, "R3_OMS_AGAIN_OCCUPY_");
    }

    @Bean
    public ThreadPoolTaskExecutor batchDetentionReleasePollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(40, 40, 500, "R3_OMS_BATCH_DETENTION_RELEASE_");
    }

    @Bean
    public ThreadPoolTaskExecutor batchOperationGoodsPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(40, 40, 30, "R3_OMS_BATCH_OPERATION_GOODS_");
    }

    @Bean
    public ThreadPoolTaskExecutor batchReplaceGoodsPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(40, 40, 30, "R3_OMS_BATCH_REPLACE_GOODS_");
    }

    @Bean
    public ThreadPoolTaskExecutor batchManualExpiryDatePollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(40, 40, 30, "R3_OMS_BATCH_MANUAL_EXPIRY_DATE_");
    }

    @Bean
    public ThreadPoolTaskExecutor batchCancelAppointLogisticsPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(40, 40, 30, "R3_OMS_BATCH_CANCEL_APPOINT_LOGISTICS_");
    }

    @Bean
    public ThreadPoolTaskExecutor lowTemperatureOccupyPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(24, 40, 30, "R3_OMS_LOW_TEMPERATURE_OCCUPY_");
    }

    @Bean
    public ThreadPoolTaskExecutor splitOnePollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(40, 40, 30, "R3_SPLIT_ONE_");
    }

    @Bean
    public ThreadPoolTaskExecutor splitBoxPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(40, 40, 30, "R3_OMS_SPLIT_BOX_");
    }

    @Bean
    public ThreadPoolTaskExecutor preOccupyPollExecutor() {
        return new ThreadPoolTaskExecutorMdcWrapper(10, 20, 10, "R3_OMS_PRE_OCCUPY_");
    }

    private static class ThreadPoolTaskExecutorMdcWrapper extends ThreadPoolTaskExecutor {

        private static final long serialVersionUID = 4013022162700168010L;

        public ThreadPoolTaskExecutorMdcWrapper(Integer corePoolSize, Integer maxPoolSize,
                                                Integer queueCapacity, String poolName) {
            // Spring 默认配置是核心线程数大小为1，最大线程容量大小不受限制，队列容量也不受限制。
            super();
            // 核心线程数
            super.setCorePoolSize(corePoolSize);
            // 最大线程数
            super.setMaxPoolSize(maxPoolSize);
            // 队列大小
            super.setQueueCapacity(queueCapacity);
            //核心线程等待销毁时间
            super.setKeepAliveSeconds(KEEP_ALIVE_TIME);
            //销毁之前执行shutdown方法
            super.setWaitForTasksToCompleteOnShutdown(true);
            // shutdown/shutdownNow后等待5s
            super.setAwaitTerminationSeconds(20);
            super.setAllowCoreThreadTimeOut(true);
            // 当最大池已满时，此策略保证不会丢失任务请求，但是可能会影响应用程序整体性能。
            super.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
            super.setThreadNamePrefix(poolName);
            super.initialize();
        }

        @Override
        public void execute(Runnable task) {
            super.execute(new TLogInheritableRunWrapper(task));
            // 这里可以做线程池监控
        }

        @Override
        public <T> Future<T> submit(Callable<T> task) {
            return super.submit(new TLogInheritableCallWrapper<>(task));
        }

        @Override
        public Future<?> submit(Runnable task) {
            return super.submit(new TLogInheritableRunWrapper(task));
        }
    }
}
