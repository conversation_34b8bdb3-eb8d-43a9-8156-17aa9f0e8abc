package com.jackrain.nea.config;

import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> liang
 * @description RPCMDC
 * @date 2021/6/19
 */
    @Activate(group = {CommonConstants.PROVIDER},order = 1)
    @Component
    public class ProviderRpcTraceFilter implements Filter {



        @Value("${app.id}")
        private String serverName;
        /**
         *
         * @param invoker
         * @param invocation
         * @return
         * @throws RpcException
         */
        @Override
        public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
            //设置日志traceId变量
            MDC.put("SERVER_NAME",serverName);
            try{
                return invoker.invoke(invocation);
            }finally {
                MDC.remove("SERVER_NAME");
            }
        }
    }
