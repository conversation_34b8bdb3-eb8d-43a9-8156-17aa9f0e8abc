//package com.jackrain.nea.config;
//
//import com.ctrip.framework.apollo.model.ConfigChange;
//import com.ctrip.framework.apollo.model.ConfigChangeEvent;
//import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
//import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Component;
//
///**
// * OMS 动态配置信息
// *
// * @author: 易邵峰
// * @since: 2019-03-29
// * create at : 2019-03-29 23:31
// */
//@Data
//@Component
////@EnableApolloConfig
//@Slf4j
//public class OmsDynamicApolloConfig {
//
//    /**
//     * 转单过程中是否检查Prod是否存在 配置关键字信息
//     */
//    private static final String IS_CHECK_TRANSFER_PROD_EXIST_KEY_NAME = "r3.oc.oms.transfer.check.prod.exist";
//
//    /**
//     * 转单过程中是否检查Prod是否存在。默认=true
//     */
//    private boolean isCheckTransferProdExist = true;
//
//    /**
//     * 监听Apollo 配置变化事件
//     *
//     * @param changeEvent 配置变化事件信息
//     */
//    @ApolloConfigChangeListener()
//    @SuppressWarnings("unused")
//    public void onApolloConfigChangeListener(ConfigChangeEvent changeEvent) {
//        changeEvent.changedKeys().forEach(key -> {
//            ConfigChange change = changeEvent.getChange(key);
//
//            String changePropertyName = change.getPropertyName();
//            String propertyNewValue = change.getNewValue();
//            log.info(LogUtil.format("ReceiveChange Key={};OldValue={};NewValue={};ChangeType={};Namespace={}",
//                    "changePropertyName"),  changePropertyName, change.getOldValue(),
//                    propertyNewValue, change.getChangeType(), change.getNamespace());
//
//            if (StringUtils.equalsIgnoreCase(changePropertyName, IS_CHECK_TRANSFER_PROD_EXIST_KEY_NAME)) {
//                isCheckTransferProdExist = !StringUtils.equalsIgnoreCase(propertyNewValue, "false");
//            }
//
//        });
//
//    }
//}
