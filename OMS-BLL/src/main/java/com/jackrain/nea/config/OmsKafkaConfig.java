package com.jackrain.nea.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName OmsKafkaConfig
 * @Description kafka配置类
 * <AUTHOR>
 * @Date 2024/5/28 16:11
 * @Version 1.0
 */
@Slf4j
@Configuration
@Data
public class OmsKafkaConfig {

    @Value("${r3.kafka.producer.oms.orderlog.topic:R3_OMS_ORDER_LOG_SYNC}")
    private String orderLogTopic;

    @Value("${r3.kafak.partition.totalnumber:6}")
    private int totalnumber;
}
