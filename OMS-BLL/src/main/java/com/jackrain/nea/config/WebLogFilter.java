package com.jackrain.nea.config;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR> liang
 * @description HTTP请求日志
 * @date 2021/6/19
 */
@Order(1)
@WebFilter(filterName = "logFilter", urlPatterns = "/*")
public class WebLogFilter implements Filter {

    @Value("${app.id}")
    private String serverName;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest)servletRequest;
        MDC.put("ip",request.getLocalAddr());
        MDC.put("SERVER_NAME", serverName);
        filterChain.doFilter(servletRequest,servletResponse);
    }

    @Override
    public void destroy() {

    }

}
