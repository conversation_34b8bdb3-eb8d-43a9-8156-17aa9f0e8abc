package com.jackrain.nea.retail.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.PosRetailRpcService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * date ：Created in 16:53 2019/12/3
 * description ：通知线下零售业务类
 * @ Modified By：
 */
@Slf4j
@Service
public class RetailNotifyService {
    @Autowired
    private PropertiesConf pconf;
//    @Autowired
//    private R3MqSendHelper r3MqSendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;
    @Autowired
    private PosRetailRpcService posRetailRpcService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;

    /**
     * 缺货、待发货、仓库发货通知线下pos
     *
     * @param orderRelation
     * @param status        订单状态：0缺货，1待发货，2已发货
     */
    public ValueHolder notifyRetailByMq(OcBOrderRelation orderRelation, int status) {
        ValueHolder valueHolder = new ValueHolder();
        String redisKey = "";
        if (status == 0) {
            redisKey = BllRedisKeyResources.buildOutOfStockNotifyRetailKey(orderRelation.getOrderId());
            CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
            Boolean hasKey = objRedisTemplate.hasKey(redisKey);
            if (hasKey != null && hasKey) {
                valueHolder.put("code", 0);
                valueHolder.put("message", "该订单号已经通知过，消息ID：" + objRedisTemplate.opsForValue().get(redisKey));
                return valueHolder;
            }
        }
//        String topic = pconf.getProperty("r3.oc.oms.notify.retail.mq.topic");
        String topic = Mq5Constants.TOPIC_R3_RETAIL_CALLBACK;
//        String tag = pconf.getProperty("r3.oc.oms.notify.retail.mq.tag");
        String tag = Mq5Constants.TAG_R3_RETAIL_CALLBACK;
        JSONObject sendObj = new JSONObject();
        sendObj.put("tid", orderRelation.getOrderInfo().getPosBillId());
        sendObj.put("oms_status", status);
        JSONArray ooidArr = new JSONArray();
        List<OcBOrderItem> orderItemList = orderRelation.getOrderItemList();
        if (CollectionUtils.isNotEmpty(orderItemList)) {
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                JSONObject object = new JSONObject();
                object.put("id", ocBOrderItem.getOoid());
                ooidArr.add(object);
            }
        } else {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单orderItemList为空", "notifyRetailByMq", orderRelation.getOrderId()));
            }
        }
        sendObj.put("ooid", ooidArr);
        if (status == 2) {
            List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(orderRelation.getOrderId());
            if (CollectionUtils.isEmpty(ocBOrderDeliveries)) {
                sendObj.put("invoice_qty", 0);
            } else {
                sendObj.put("invoice_qty", ocBOrderDeliveries.size());
            }
        }
        try {
//            String messageId = r3MqSendHelper.sendMessage("retail", sendObj, topic, tag);
            MqSendResult result = defaultProducerSend.sendTopic(topic, tag, sendObj.toJSONString(), null);
            String messageId = result.getMessageId();

            if (status == 0) {
                CusRedisTemplate<String, String> redisTemplate = RedisMasterUtils.getObjRedisTemplate();
                redisTemplate.opsForValue().set(redisKey, messageId, 3, TimeUnit.DAYS);
            }
            valueHolder.put("code", 0);
            valueHolder.put("message", messageId);
        } catch (Exception e) {
            e.printStackTrace();
            valueHolder.put("code", -1);
            valueHolder.put("message", e.getMessage());
        }
        return valueHolder;
    }

    /**
     * 订单取消调用线下pos服务
     *
     * @param id       订单id
     * @param ocBOrder 订单信息
     * @param user     操作人
     */
    public ValueHolder orderCancelNotifyRetailByRpc(Long id, OcBOrder ocBOrder, User user) {
        log.info(LogUtil.format("开始调用pos RPC服务", id, ocBOrder.getBillNo(),ocBOrder.getSourceCode()));
        JSONObject paramObj = new JSONObject();
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemList(id);
        JSONArray ooidArr = new JSONArray();
        if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                JSONObject object = new JSONObject();
                object.put("id", ocBOrderItem.getOoid());
                ooidArr.add(object);
            }
        } else {
            if (log.isDebugEnabled()) {
                log.info(LogUtil.format("订单orderItemList为空", id, ocBOrder.getBillNo(),ocBOrder.getSourceCode()));
            }
        }
        paramObj.put("tid", ocBOrder.getPosBillId());
        paramObj.put("userName", user.getName());
        paramObj.put("ooid", ooidArr);
        return posRetailRpcService.orderCancelInformPos(paramObj);
    }

}
