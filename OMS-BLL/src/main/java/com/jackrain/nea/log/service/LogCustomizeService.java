package com.jackrain.nea.log.service;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.mapper.OcBOperationLogMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 定制操作日志处理
 * <AUTHOR>
 * @Date 2020/2/7 15:56
 */
@Component
@Slf4j
public class LogCustomizeService extends LogCommonService {
    @Autowired
    private OcBOperationLogMapper operationLogMapper;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Override
    public void generateStLog(JoinPoint joinPoint, OmsOperationLog operationLog) {
        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("进入定制操作日志生成...."));
        }
        String customizeLogAopKey = operationLog.customizeLogAopKey();
        switch (customizeLogAopKey){
            case "ST_C_EXPIRY_DATE_RESERVE_AUDIT":
                stCExpiryDateReserveAudit(joinPoint);
                break;
            default:
                log.error(LogUtil.format("策略平台定制操作日志未获取到对应定制日志唯一识别键!"));
                break;
        }
    }

    private void stCExpiryDateReserveAudit(JoinPoint joinPoint){
        Object[] params = joinPoint.getArgs();
        User user = null;
        JSONObject json = null;
        for(Object param : params){
            if (param instanceof User) {
                user = (User) param;
            } else if (param instanceof JSONObject) {
                json = (JSONObject) param;
            }
        }
        if(user != null && json != null){
            OcBOperationLog operationLogDo = getOperationLog("ST_C_EXPIRY_DATE",
                    OperationTypeEnum.RESERVE_AUDIT.getOperationValue(),Long.parseLong(json.getString("objid")),
                    "商品效期策略","","", "", user);
            operationLogMapper.insert(operationLogDo);
        }
    }
}
