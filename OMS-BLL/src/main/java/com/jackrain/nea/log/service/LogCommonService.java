package com.jackrain.nea.log.service;

import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOperationLogMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.web.face.User;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description 日志操作基类
 * @author:洪艺安
 * @since: 2020/2/18
 * @create at : 2020/2/18 14:58
 */
@Component
public class LogCommonService {

    @Resource
    private OcBOperationLogMapper operationLogMapper;


    /**
     * @param joinPoint
     * @param operationLog
     * @return void
     * @Description 生成日志
     * <AUTHOR>
     * @date 2020/2/19 16:15
     */
    void generateStLog(JoinPoint joinPoint, OmsOperationLog operationLog) {

    }

    /**
     * @Descroption 生成日志对象
     * @param tableName
     * @param operationType
     * @param updateId
     * @param tableDescription
     * @param columnName
     * @param columnBeforeValue
     * @param columnAfterValue
     * @param user
     * @return
     */
    public OcBOperationLog getOperationLog(String tableName, String operationType, Long updateId, String tableDescription, String columnName, String columnBeforeValue, String columnAfterValue, User user) {
        OcBOperationLog operationLog = new OcBOperationLog();
        operationLog.setId(ModelUtil.getSequence(StConstant.TAB_OC_B_OPERATION_LOG));
        operationLog.setTableName(tableName);
        operationLog.setOperationType(OperationTypeEnum.getNameByValue(operationType));
        operationLog.setUpdateId(updateId);
        operationLog.setUpdateModelName(tableDescription);
        operationLog.setModContent(columnName);
        operationLog.setBeforeData(columnBeforeValue);
        operationLog.setAfterData(columnAfterValue);
        StBeanUtils.makeCreateField(operationLog, user);
        return operationLog;
    }

    /**
     * 保存日志
     *
     * @param operationLogDO
     */
    public void insertLog(OcBOperationLog operationLogDO) {
        operationLogMapper.insert(operationLogDO);
    }

    /**
     * 批量保存日志
     *
     * @param operationLogList
     */
    public void batchInsertLog(List<OcBOperationLog> operationLogList) {
        if (CollectionUtils.isNotEmpty(operationLogList)) {
            operationLogMapper.batchInsert(operationLogList);
        }
    }
}
