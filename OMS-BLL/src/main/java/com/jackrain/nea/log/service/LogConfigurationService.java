package com.jackrain.nea.log.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.core.schema.Column;
import com.jackrain.nea.core.schema.Limitvalue;
import com.jackrain.nea.core.schema.LimitvalueGroup;
import com.jackrain.nea.core.schema.Table;
import com.jackrain.nea.cpext.api.CpLogisticsQueryCmd;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.api.RegionQueryExtCmd;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCRegion;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOperationLogMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.ps.api.CproDimItemQueryCmd;
import com.jackrain.nea.ps.api.table.PsCProdimItem;
import com.jackrain.nea.psext.api.PsCProQueryCmd;
import com.jackrain.nea.psext.api.PsCSkuQueryCmd;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.st.model.vo.StCLogChangeVo;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.aspectj.lang.JoinPoint;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Descroption 元数据操作日志处理
 * <AUTHOR>
 * @Date 2020/2/7 15:56
 */
@Component
@Slf4j
public class LogConfigurationService extends LogCommonService {
    @Resource
    private OcBOperationLogMapper operationLogMapper;
    @DubboReference(group = "cp-ext", version = "1.0")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;
    @DubboReference(version = "1.0", group = "cp-ext")
    private RegionQueryExtCmd regionQueryExtCmd;
    @DubboReference(group = "ps", version = "1.0")
    private CproDimItemQueryCmd cproDimItemQueryCmd;
    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCProQueryCmd psCProQueryCmd;
    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCSkuQueryCmd psCSkuQueryCmd;
    @DubboReference(version = "1.0", group = "cp-ext")
    private CpLogisticsQueryCmd cpLogisticsQueryCmd;

    @Override
    public void generateStLog(JoinPoint joinPoint, OmsOperationLog operationLog) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("进入元数据操作日志生成...."));
        }
        String mainTableName = operationLog.mainTableName();
        String itemsTableName = operationLog.itemsTableName();
        List<String> itemsTableNameList = null;

        String operationType = operationLog.operationType();
        Map<String,List<String>> focusColumnMap = new HashMap<>();
        Map<String,String> itemOperationKeyMap = new HashMap<>();
        getFocusColumn(mainTableName,focusColumnMap);
        if(StringUtils.isNotEmpty(itemsTableName)){
            String[] itemsTableNameArr = itemsTableName.split(",");
            for(String itemName : itemsTableNameArr){
                getFocusColumn(itemName,focusColumnMap);
                getItemOperationKey(itemName,itemOperationKeyMap);
            }
            itemsTableNameList = Lists.newArrayList(itemsTableNameArr);
        }
        StCLogChangeVo logChangeVo = new StCLogChangeVo(mainTableName, operationType, itemsTableNameList, itemOperationKeyMap, focusColumnMap);
        resolvingStLog(joinPoint,logChangeVo);
    }

    /**
     * @param tableName
     * @param focusColumnMap
     * @return Map
     * @Description 需要记录变化的字段
     * <AUTHOR>
     * @date 2020/2/18 15:35
     */
    private void getFocusColumn(String tableName, Map<String, List<String>> focusColumnMap) {
        String[] focusColumn = new String[]{};
        if (StConstant.TAB_ST_C_EXPIRY_DATE.equals(tableName)) {
            focusColumn = new String[]{"EXPIRY_TYPE","CUSTOMER_GROUPING","SHOP_ID","START_TIME","END_TIME","REMARKS","SUBMIT_STATUS","ISACTIVE"};
        } else if (StConstant.TAB_ST_C_EXPIRY_DATE_ITEM.equals(tableName)) {
            focusColumn = new String[]{"APPOINT_DIMENSION","APPOINT_CONTENT","APPOINT_TYPE","START_DATE_DAY","END_DATE_DAY","ORDER_LABEL","ISACTIVE","CHEAPEST_EXPRESS"};
        } else if (StConstant.ST_C_EQUITY_BARTER_STRATEGY.equals(tableName)) {
            focusColumn = new String[]{"TYPE","CP_C_SHOP_ID","REMARK"};
        } else if (StConstant.ST_C_EQUITY_BARTER_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"PS_C_SKU_ID","PS_C_SKU_NAME","QTY","EQUITY_SKU_ID","EQUITY_SKU_NAME","EQUITY_QTY","EXCHANGE_TYPE"};
        } else if (StConstant.ST_C_BUSINESS_TYPE.equals(tableName)) {
            focusColumn = new String[]{"BILL_TYPE","PARENT_CODE","ENAME","SAP_TYPE","ECODE","REFUND_TYPE_ID","RESEND_TYPE_ID"
                    ,"RETURN_TYPE_ID","PICK_GOODS_TYPE_ID","OUT_TYPE_CODE","IN_TYPE_CODE","IS_SOURCE_OCCUPY","IS_ALLOW_COPY"
                    ,"IS_ALLOW_RESEND","IS_ALLOW_HAND_RETURN","IS_ALLOW_EXTRA_REFUND","IS_CHECK_SOURCE","IS_CANCEL_ORDER","IS_MERGE_ORDER"};
        } else if (StConstant.ST_C_BUSINESS_TYPE_MATCH_STRATEGY.equals(tableName)) {
            focusColumn = new String[]{"BILL_TYPE","ST_C_BUSINESS_TYPE_ID","REMARK"};
        } else if (StConstant.ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"DISCERN_TYPE","DISCERN_SYMBOL","DISCERN_CONTENT","ISACTIVE"};
        } else if (StConstant.ST_C_SPLIT_BEFORE_SOURCE_STRATEGY.equals(tableName)) {
            focusColumn = new String[]{"ENAME","REMARK"};
        } else if (StConstant.ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_CATEGORY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"PS_C_PRODIM_ITEM_ID","ISACTIVE"};
        } else if (StConstant.ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_WEIGHT_ITEM.equals(tableName)) {
            focusColumn = new String[]{"PS_C_PRODIM_ITEM_ID","MAX_WEIGHT","ISACTIVE"};
        } else if (StConstant.ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_SKU_ITEM.equals(tableName)) {
            focusColumn = new String[]{"PS_C_SKU_ID","PS_C_PRO_NAME","MAX_QTY","ISACTIVE"};
        } else if (StConstant.ST_C_BOX_STRATEGY.equals(tableName)){
            focusColumn = new String[]{"SPLIT_ORDER_RULES","ISACTIVE"};
        } else if (StConstant.ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY.equals(tableName)) {
            focusColumn = new String[]{"ISACTIVE"};
        } else if (StConstant.ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY_DETAIL.equals(tableName)) {
            focusColumn = new String[]{"MATCH_RULE", "MATCH_CONTENT", "AGEING", "ISACTIVE"};
        } else if (StConstant.ST_C_APPOINT_EXPRESS_STRATEGY.equals(tableName)) {
            focusColumn = new String[]{"ISACTIVE"};
        } else if (StConstant.ST_C_APPOINT_EXPRESS_STRATEGY_DETAIL.equals(tableName)) {
            focusColumn = new String[]{"MATCH_RULE", "MATCH_CONTENT", "CP_C_LOGISTICS_ID", "AGEING", "ISACTIVE"};
        } else if (StConstant.ST_C_CYCLE_STRATEGY.equals(tableName)) {
            focusColumn = new String[]{"STRATEGY_NAME", "REMARK"};
        } else if (StConstant.ST_C_CYCLE_RULE_STRATEGY.equals(tableName)) {
            focusColumn = new String[]{"RULE_TYPE", "RULE_CONTENT"};
        } else if (StConstant.ST_C_CYCLE_ITEM_STRATEGY.equals(tableName)) {
            focusColumn = new String[]{"SKU_ID", "CYCLE_NUM", "SPLIT_TYPE", "QTY"};
        } else if (StConstant.OC_B_DIRECT_REPORT_ORDER.equals(tableName)) {
            focusColumn = new String[]{"CP_C_SHOP_ID", "CP_C_DIS_ORG_LV2_ID", "SG_C_SA_STORE_ID", "CP_C_STORE_ID", "AUTO_RELEASE_TIME", "ESTIMATE_CON_TIME"};
        } else if (StConstant.OC_B_DIRECT_REPORT_ORDER_ITEM.equals(tableName)) {
            focusColumn = new String[]{"PS_C_SKU_ID", "QTY", "START_PRODUCE_DATE", "END_PRODUCE_DATE"};
        }
        List<String> focusColumnList = Lists.newArrayList(focusColumn);
        focusColumnMap.put(tableName,focusColumnList);
    }

    /**
     * @param tableName
     * @param itemOperationKeyMap
     * @return java.util.Map
     * @Descroption 获取明细新增/删除操作展示键
     * @Author: 洪艺安
     * @Date 2020/2/18
     */
    private void getItemOperationKey(String tableName, Map<String, String> itemOperationKeyMap) {
        String itemOperationKey = "ID";
//        if (StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_ITEM.equals(tableName)) {
//            itemOperationKey = "CP_C_STORE_ID";
//        } else if (StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_CHANNEL.equals(tableName)) {
//            itemOperationKey = "CP_C_ORG_CHANNEL_ID";
//        }
        itemOperationKeyMap.put(tableName, itemOperationKey);
    }

    /**
     * @Description 解析入参
     * <AUTHOR>
     * @date 2020/2/19 10:35
     * @param joinPoint
     * @param logChangeVo
     * @return void
     */
    private void resolvingStLog(JoinPoint joinPoint,StCLogChangeVo logChangeVo){
        String operationType = logChangeVo.getOperationType();
        if (OperationTypeEnum.MOD.getOperationValue().equals(operationType)) {
            resolvingStLogByMod(joinPoint, logChangeVo);
        } else if (OperationTypeEnum.DEL.getOperationValue().equals(operationType)) {
            resolvingStLogByDel(joinPoint, logChangeVo);
        } else if (OperationTypeEnum.AUDIT.getOperationValue().equals(operationType)
                || OperationTypeEnum.RESERVE_AUDIT.getOperationValue().equals(operationType)
                || OperationTypeEnum.VOID.getOperationValue().equals(operationType)
                || OperationTypeEnum.FINISH.getOperationValue().equals(operationType)
                || OperationTypeEnum.OPEN.getOperationValue().equals(operationType)) {
            resolvingStCommonLog(joinPoint, logChangeVo);
        } else {
            throw new NDSException("日志操作类型注解不满足!");
        }

    }

    /**
     * 通用日志
     *
     * @param joinPoint
     * @param logChangeVo
     */
    private void resolvingStCommonLog(JoinPoint joinPoint, StCLogChangeVo logChangeVo) {
        Object[] params = joinPoint.getArgs();
        QuerySession session = getQuerySession(params);
        if (session != null) {
            DefaultWebEvent event = session.getEvent();
            JSONObject jsonParam = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                    event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                    Feature.OrderedField);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("打印JsonObj：") + jsonParam);
            }
            if (jsonParam != null) {
                List<Long> ids = new ArrayList<>();
                Long updateId = jsonParam.getLong("objid");
                if (updateId != null) {
                    ids.add(updateId);
                }
                JSONArray jsonArray = jsonParam.getJSONArray("ids");
                if (jsonArray != null && jsonArray.size() > 0) {
                    for (Object id : jsonArray) {
                        ids.add(Long.valueOf((String) id));
                    }
                }
                if (!CollectionUtils.isEmpty(ids)) {
                    insertLog(session, logChangeVo, ids);
                }
            }
        }
    }

    /**
     * 通用数据插入
     *
     * @param session
     * @param logChangeVo
     * @param ids
     */
    private void insertLog(QuerySession session, StCLogChangeVo logChangeVo, List<Long> ids) {
        String tableName = logChangeVo.getTableName();
        String operationType = logChangeVo.getOperationType();
        Table table = session.getTableManager().getTable(tableName);
        List<OcBOperationLog> operationLogList = new ArrayList<>();
        for (Long id : ids) {
            operationLogList.add(getOperationLog(tableName, operationType, id, table.getDescription(), "", "", "", session.getUser()));
        }
        operationLogMapper.batchInsert(operationLogList);
    }

    /**
     * @param joinPoint
     * @param logChangeVo
     * @return void
     * @Description 修改日志
     * <AUTHOR>
     * @date 2020/2/19 10:43
     */
    private void resolvingStLogByMod(JoinPoint joinPoint, StCLogChangeVo logChangeVo) {
        Object[] params = joinPoint.getArgs();
        QuerySession session = getQuerySession(params);
        if (session != null) {
            DefaultWebEvent event = session.getEvent();
            JSONObject jsonParam = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                    event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                    Feature.OrderedField);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("打印JsonObj：") + jsonParam);
            }
            if (jsonParam != null) {
                JSONObject fixColumn = jsonParam.getJSONObject("fixcolumn");
                JSONObject beforeValue = jsonParam.getJSONObject("beforevalue");
                JSONObject afterValue = jsonParam.getJSONObject("aftervalue");
                Long updateId = jsonParam.getLong("objid");
                if (fixColumn != null) {
                    //获取主表信息
                    JSONObject mainFixColumnData = fixColumn.getJSONObject(logChangeVo.getTableName());
                    //主表插入
                    if (mainFixColumnData != null) {
                        if (beforeValue != null && afterValue != null) {
                            JSONObject beforeData = beforeValue.getJSONObject(logChangeVo.getTableName());
                            JSONObject afterData = afterValue.getJSONObject(logChangeVo.getTableName());
                            insertMainStLog(session, logChangeVo, updateId, mainFixColumnData, beforeData, afterData);
                        }
                    }
                    //明细逐个表处理
                    List<String> itemTableList = logChangeVo.getItemsTableName();
                    if (!CollectionUtils.isEmpty(itemTableList)) {
                        for (String itemTableName : itemTableList) {
                            JSONArray itemFixColumnDataArr = fixColumn.getJSONArray(itemTableName);
                            JSONArray beforeDataArr = null;
                            JSONArray afterDataArr = null;
                            if (beforeValue != null && afterValue != null) {
                                beforeDataArr = beforeValue.getJSONArray(itemTableName);
                                afterDataArr = afterValue.getJSONArray(itemTableName);
                            }
                            if (null != itemFixColumnDataArr && itemFixColumnDataArr.size() > 0) {
                                insertItemStLog(session, logChangeVo, updateId, itemTableName, itemFixColumnDataArr, beforeDataArr, afterDataArr);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * @param joinPoint
     * @param logChangeVo
     * @return void
     * @Description 删除日志
     * <AUTHOR>
     * @date 2020/2/19 10:44
     */
    private void resolvingStLogByDel(JoinPoint joinPoint, StCLogChangeVo logChangeVo) {
        Object[] params = joinPoint.getArgs();
        QuerySession session = getQuerySession(params);
        List<String> itemsTableNameList = logChangeVo.getItemsTableName();
        if (CollectionUtils.isEmpty(itemsTableNameList)) {
            return;
        }
        for (String itemTableName : itemsTableNameList) {
            if (session != null) {
                DefaultWebEvent event = session.getEvent();
                JSONObject jsonParam = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                        Feature.OrderedField);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("打印删除JsonObj：") + jsonParam);
                }
                if (jsonParam != null) {
                    String isDel = jsonParam.getString("isdelmtable");
                    if (StConstant.FALSE_STR.equals(isDel)) {
                        Long updateId = jsonParam.getLong("objid");
                        JSONObject tabitem = jsonParam.getJSONObject("tabitem");
                        JSONArray itemArray = tabitem.getJSONArray(itemTableName);
                        insertItemDelStLog(session, logChangeVo, updateId, itemTableName, itemArray);
                    }
                }
            }
        }
    }

    /**
     * @param params
     * @return com.jackrain.nea.web.query.QuerySession
     * @Description 获取QuerySession
     * <AUTHOR>
     * @date 2020/2/19 13:11
     */
    private QuerySession getQuerySession(Object[] params) {
        QuerySession session = null;
        for (Object param : params) {
            if (param instanceof QuerySession) {
                session = (QuerySession) param;
                break;
            }
        }
        return session;
    }

    /**
     * @param session
     * @param logChangeVo
     * @param updateId
     * @param fixColumnData
     * @param beforeData
     * @param afterData
     * @return void
     * @Description 主表数据插入
     * <AUTHOR>
     * @date 2020/2/18 16:25
     */
    private void insertMainStLog(QuerySession session, StCLogChangeVo logChangeVo, Long updateId, JSONObject
            fixColumnData, JSONObject beforeData, JSONObject afterData) {
        String tableName = logChangeVo.getTableName();
        String operationType = logChangeVo.getOperationType();
        List focusColomnList = logChangeVo.getFocusColomnMap().get(tableName);
        Table table = session.getTableManager().getTable(tableName);
        List<OcBOperationLog> operationLogList = Lists.newArrayList();
        for (String key : fixColumnData.keySet()) {
            if (focusColomnList.contains(key)) {
                Column column = table.getColumn(key);
                if (column == null || !focusColomnList.contains(column.getName())) {
                    continue;
                }
                String columnName = column.getDescription(session.getLocale());
                if (StringUtils.isEmpty(columnName)) {
                    continue;
                }
                String columnBeforeValue = StringUtils.isNotEmpty(beforeData.getString(key)) ? beforeData.getString(key) : "";
                String columnAfterValue = StringUtils.isNotEmpty(afterData.getString(key)) ? afterData.getString(key) : "";
                if (columnBeforeValue != null && columnAfterValue != null && columnBeforeValue.compareTo(columnAfterValue) != 0) {
                    OcBOperationLog operationLog = getOperationLog(tableName, operationType, updateId, table.getDescription(), columnName, columnBeforeValue, columnAfterValue, session.getUser());
                    operationLogList.add(operationLog);
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("打印主表operationLogList的size：") + operationLogList.size());
        }
        if (operationLogList.size() > 0) {
            operationLogMapper.batchInsert(operationLogList);
        }
    }

    /**
     * @param session
     * @param logChangeVo
     * @param updateId
     * @param itemTableName
     * @param itemFixColumnDataArr
     * @param beforeDataArr
     * @param afterDataArr
     * @return void
     * @Descroption 明细数据插入
     * @Author: 洪艺安
     * @Date 2020/2/18
     */
    private void insertItemStLog(QuerySession session, StCLogChangeVo logChangeVo, Long updateId, String
            itemTableName, JSONArray itemFixColumnDataArr, JSONArray beforeDataArr, JSONArray afterDataArr) {
        String operationType = logChangeVo.getOperationType();
        List focusColomnList = logChangeVo.getFocusColomnMap().get(itemTableName);
        String itemOperationKey = logChangeVo.getItemOperationKey().get(itemTableName);
        Table table = session.getTableManager().getTable(itemTableName);
        List<OcBOperationLog> operationLogList = Lists.newArrayList();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("进入修改itemFixColumnDataArr:") + itemFixColumnDataArr);
        }
        for (Object item : itemFixColumnDataArr) {
            JSONObject itemFixColumnData = (JSONObject) item;
            JSONObject itemBeforeDataData = null;
            JSONObject itemAfterDataData = null;
            Long itemId = itemFixColumnData.getLong("ID");
            //判断明细新增还是修改
            if (itemFixColumnData.getLong("ID") == -1) {
                for (String key : itemFixColumnData.keySet()) {
                    if (focusColomnList.contains(key)) {
                        Column column = table.getColumn(key);
                        if (column == null) {
                            continue;
                        }
                        String columnName = column.getDescription(session.getLocale());
                        if (StringUtils.isEmpty(columnName)) {
                            continue;
                        }
                        String colomnContent = getColomnContent(table, itemTableName, key, itemFixColumnData.getString(key));
                        operationType = OperationTypeEnum.ADD.getOperationValue();
                        OcBOperationLog operationLog = getOperationLog(itemTableName, operationType, updateId, table.getDescription(), columnName, "", colomnContent, session.getUser());
                        operationLogList.add(operationLog);
                    }
                }
            } else {
                Column itemOperationKeyColumn = table.getColumn(itemOperationKey);
                //根据ItemId获取对应的JSONObj修改前后对象
                for (Object value : beforeDataArr) {
                    JSONObject beforeDataDataTmp = (JSONObject) value;
                    Long beforeItemId = beforeDataDataTmp.getLong("ID");
                    if (itemId.equals(beforeItemId)) {
                        itemBeforeDataData = beforeDataDataTmp;
                    }
                }
                for (Object o : afterDataArr) {
                    JSONObject afterDataDataTmp = (JSONObject) o;
                    Long afterItemId = afterDataDataTmp.getLong("ID");
                    if (itemId.equals(afterItemId)) {
                        itemAfterDataData = afterDataDataTmp;
                    }
                }
                //遍历修改字段
                for (String key : itemFixColumnData.keySet()) {
                    if (focusColomnList.contains(key)) {
                        Column column = table.getColumn(key);
                        if (column == null || !focusColomnList.contains(column.getName())) {
                            continue;
                        }
                        String columnName = column.getDescription(session.getLocale());
                        if (StringUtils.isEmpty(columnName)) {
                            continue;
                        }
                        String modContent = String.format("[%s-%s]:%s", itemOperationKeyColumn.getDescription(session.getLocale()), itemId, columnName);
                        if (itemBeforeDataData != null && itemAfterDataData != null) {
                            String columnBeforeValue = itemBeforeDataData.getString(key);
                            String columnAfterValue = itemAfterDataData.getString(key);
                            if (columnBeforeValue != null && columnAfterValue != null && columnBeforeValue.compareTo(columnAfterValue) != 0) {
                                OcBOperationLog operationLog = getOperationLog(itemTableName, operationType, updateId, table.getDescription(), modContent, columnBeforeValue, columnAfterValue, session.getUser());
                                operationLogList.add(operationLog);
                            }
                        }
                    }
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("打印明细operationLogList的size：") + operationLogList.size());
        }
        if (operationLogList.size() > 0) {
            operationLogMapper.batchInsert(operationLogList);
        }
    }


    /**
     * 删除明细记录日志
     *
     * @param session
     * @param logChangeVo
     * @param updateId
     * @param itemTableName
     * @param itemArray
     */
    public void insertItemDelStLog(QuerySession session, StCLogChangeVo logChangeVo, Long updateId, String
            itemTableName, JSONArray itemArray) {
        if (itemArray == null || itemArray.size() == 0) {
            return;
        }
        Table table = session.getTableManager().getTable(itemTableName);
        List<String> itemFixColomnList = logChangeVo.getFocusColomnMap().get(itemTableName);
        List<OcBOperationLog> operationLogList = Lists.newArrayList();
        //获取原对象
        Map beforeDelObjMap = (Map) session.getAttribute("beforeDelObjMap");
        if (MapUtils.isEmpty(beforeDelObjMap)) {
            return;
        }
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemId = itemArray.getLong(i);
            String itemJSONStr = (String) beforeDelObjMap.get(itemId);
            if (StringUtils.isEmpty(itemJSONStr)) {
                continue;
            }
            JSONObject preItemJSONObj = JSON.parseObject(itemJSONStr);
            String columnBeforeValue = "";
            StringBuilder sb = new StringBuilder();
            if (!CollectionUtils.isEmpty(itemFixColomnList)) {
                for (int j = 0; j < itemFixColomnList.size(); j++) {
                    String itemColomn = itemFixColomnList.get(j);
                    String colomnKey = preItemJSONObj.getString(itemColomn);
                    if (StringUtils.isNotEmpty(colomnKey)) {
                        String content = getColomnContent(table, itemTableName, itemColomn, colomnKey);
                        if (sb.length() == 0) {
                            sb.append("[" + content + "]");
                        } else {
                            sb.append(",[" + content + "]");
                        }
                    }
                }
                if (sb.length() > 0) {
                    columnBeforeValue = sb.toString();
                }
            }
            OcBOperationLog operationLog = getOperationLog(itemTableName, logChangeVo.getOperationType(), updateId, table.getDescription(), "删除" + table.getDescription(), columnBeforeValue, "", session.getUser());
            operationLogList.add(operationLog);
        }
        if (operationLogList.size() > 0) {
            operationLogMapper.batchInsert(operationLogList);
        }
    }

    /**
     * 根据字段类型获得显示值
     *
     * @param table
     * @param itemTableName
     * @param itemColomn
     * @param colomnKey
     * @return
     */
    private String getColomnContent(Table table, String itemTableName,
                                    String itemColomn,
                                    String colomnKey) {
        List<String> warehouseField = getWarehouseField(itemTableName);
        List<String> regionField = getRegionField(itemTableName);
        List<String> limitvalueGroupField = getLimitvalueGroupField(itemTableName);
        List<String> proDimField = getProDimField(itemTableName);
        List<String> proField = getProField(itemTableName);
        List<String> skuField = getSkuField(itemTableName);
        List<String> logisticsField = getLogisticsField(itemTableName);
        String content = colomnKey;
        if (warehouseField.contains(itemColomn)) {
            content = queryWarehouseName(Long.valueOf(colomnKey));
        } else if (regionField.contains(itemColomn)) {
            content = queryRegionName(Long.valueOf(colomnKey));
        } else if (limitvalueGroupField.contains(itemColomn)) {
            content = queryLimitvalueGroupName(table, itemColomn, colomnKey);
        } else if (proDimField.contains(itemColomn)) {
            content = queryProDimName(Long.valueOf(colomnKey));
        } else if (proField.contains(itemColomn)) {
            content = queryProCode(Long.valueOf(colomnKey));
        }else if (skuField.contains(itemColomn)) {
            content = querySkuCode(Long.valueOf(colomnKey));
        } else if (logisticsField.contains(itemColomn)) {
            content = queryLogisticsName(Long.valueOf(colomnKey));
        }
        return content;
    }

    private String querySkuCode(Long id) {
        String code = "";
        if (id == null) {
            return code;
        }
        ValueHolderV14<PsCSku> skuV14 = psCSkuQueryCmd.querySkuById(id);
        if (skuV14 != null && skuV14.getData() != null) {
            code = skuV14.getData().getEcode();
        }
        return code;
    }

    private List<String> getSkuField(String itemTableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.ST_C_EQUITY_BARTER_STRATEGY_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"PS_C_SKU_ID","EQUITY_SKU_ID"};
        } else if (StConstant.ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_SKU_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"PS_C_SKU_ID"};
        } else if (StConstant.OC_B_DIRECT_REPORT_ORDER_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"PS_C_SKU_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    private String queryLogisticsName(Long id) {
        String name = "";
        if (id == null) {
            return name;
        }
        ValueHolderV14<Map<Long, CpLogistics>> v14 = cpLogisticsQueryCmd.queryLogisticsByIds(Lists.newArrayList(id));
        if (v14 != null && MapUtils.isNotEmpty(v14.getData()) && v14.getData().get(id) != null) {
            name = v14.getData().get(id).getEname();
        }
        return name;
    }

    private List<String> getLogisticsField(String itemTableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"CP_C_LOGISTICS_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    private String queryProCode(Long id) {
        String name = "";
        if (id == null) {
            return name;
        }
        ValueHolderV14<PsCPro> proV14 = psCProQueryCmd.queryProById(id);
        if (proV14 != null && proV14.getData() != null) {
            name = proV14.getData().getEcode();
        }
        return name;
    }

    private List<String> getProField(String itemTableName) {
        String[] focusColumn = new String[]{};
//        if (StConstant.ST_C_EQUITY_BARTER_STRATEGY_ITEM.equals(itemTableName)) {
//            focusColumn = new String[]{"PS_C_SKU_ID","EQUITY_SKU_ID"};
//        }
        return Lists.newArrayList(focusColumn);
    }

    private String queryProDimName(Long id) {
        String name = "";
        if (id == null) {
            return name;
        }
        PsCProdimItem psCProdimItem = cproDimItemQueryCmd.queryPsCProDimItem(id);
        if (psCProdimItem != null) {
            name = psCProdimItem.getEname();
        }
        return name;
    }

    private List<String> getProDimField(String itemTableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_CATEGORY_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"PS_C_PRODIM_ITEM_ID"};
        } else if (StConstant.ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_WEIGHT_ITEM.equals(itemTableName)) {
            focusColumn = new String[]{"PS_C_PRODIM_ITEM_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    /**
     * 地区字段
     *
     * @param tableName
     * @return
     */
    public List<String> getRegionField(String tableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"CP_C_PROVINCE_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    public String queryRegionName(Long id) {
        String name = "";
        if (id == null) {
            return name;
        }
        CpCRegion cpCRegion = regionQueryExtCmd.queryRegionById(id);
        if (cpCRegion != null) {
            name = cpCRegion.getEname();
        }
        return name;
    }

    /**
     * 仓库字段
     *
     * @param tableName
     * @return
     */
    public List<String> getWarehouseField(String tableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.ST_C_SHOP_LOGISTIC_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"CP_C_PHY_WAREHOUSE_ID"};
        }
        return Lists.newArrayList(focusColumn);
    }

    /**
     * 查询实体仓名称
     *
     * @param id
     * @return
     */
    public String queryWarehouseName(Long id) {
        String name = "";
        if (id == null) {
            return name;
        }
        CpCPhyWarehouse warehouse = cpcPhyWareHouseQueryCmd.queryCpCPhyWarehouseById(id);
        if (warehouse != null) {
            name = warehouse.getEname();
        }
        return name;
    }

    /**
     * 元数据字段选项组字段
     *
     * @param tableName
     * @return
     */
    public List<String> getLimitvalueGroupField(String tableName) {
        String[] focusColumn = new String[]{};
        if (StConstant.TAB_ST_C_EXPIRY_DATE_ITEM.equals(tableName)) {
            focusColumn = new String[]{"APPOINT_DIMENSION","APPOINT_TYPE","ISACTIVE"};
        } else if (StConstant.ST_C_EQUITY_BARTER_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"EXCHANGE_TYPE"};
        } else if (StConstant.ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"DISCERN_TYPE","DISCERN_SYMBOL","ISACTIVE"};
        } else if (StConstant.ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_CATEGORY_ITEM.equals(tableName)) {
            focusColumn = new String[]{"ISACTIVE"};
        } else if (StConstant.ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_WEIGHT_ITEM.equals(tableName)) {
            focusColumn = new String[]{"ISACTIVE"};
        } else if (StConstant.ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_SKU_ITEM.equals(tableName)) {
            focusColumn = new String[]{"ISACTIVE"};
        }
        return Lists.newArrayList(focusColumn);
    }

    /**
     * 查询字段选项组对应字段值
     *
     * @param table
     * @param itemColomn
     * @param key
     * @return
     */
    public String queryLimitvalueGroupName(Table table, String itemColomn, String key) {
        if (StringUtils.isEmpty(key)) {
            return "";
        }
        String name = key;
        if (table != null) {
            Column column = table.getColumn(itemColomn);
            if (column != null) {
                LimitvalueGroup limitvalueGroup = column.getLimitvalueGroup();
                if (limitvalueGroup != null) {
                    ArrayList<Limitvalue> limitvalues = limitvalueGroup.getAdLimitvalues();
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(limitvalues)) {
                        for (Limitvalue limitvalue : limitvalues) {
                            if (key.equals(limitvalue.getValue())) {
                                name = limitvalue.getDescription();
                                break;
                            }
                        }
                    }
                }
            }
        }
        return name;
    }
}
