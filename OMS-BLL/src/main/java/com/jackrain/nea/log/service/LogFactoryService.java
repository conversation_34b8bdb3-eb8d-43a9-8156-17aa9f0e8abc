package com.jackrain.nea.log.service;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Descroption 操作日志处理工厂
 * <AUTHOR>
 * @Date 2020/2/7 15:56
 */
@Component
@Slf4j
public class LogFactoryService {
    public LogCommonService getLogService(boolean configurationFlag) {
        if(configurationFlag){
            return ApplicationContextHandle.getBean(LogConfigurationService.class);
        } else {
            return ApplicationContextHandle.getBean(LogCustomizeService.class);
        }
    }

    public BaseMapper getMapper(String tableName){
//        if(StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_ITEM.equals(tableName)){
//            return ApplicationContextHandle.getBean(StCSyncStockStrategyItemMapper.class);
//        }else if(StConstant.TAB_ST_C_SYNC_STOCK_STRATEGY_CHANNEL.equals(tableName)){
//            return ApplicationContextHandle.getBean(StCSyncStockStrategyChannelMapper.class);
//        }else if (StConstant.TAB_ST_C_PRICE_EXCLUDE_ITEM.equals(tableName)) {
//            return ApplicationContextHandle.getBean(StCPriceExcludeItemMapper.class);
//        } else{
//            return null;
//        }
        return null;
    }
}
