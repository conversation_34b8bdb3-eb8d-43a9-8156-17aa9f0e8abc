package com.jackrain.nea.log.service;

import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.springframework.stereotype.Component;

/**
 * @Descroption 操作日志处理
 * <AUTHOR>
 * @Date 2020/2/7 15:56
 */
@Component
@Slf4j
public class LogService<T extends BaseModel>{

    public void saveLog(JoinPoint joinPoint, OmsOperationLog logObj) {
        if(log.isDebugEnabled()){
            log.debug(LogUtil.format("进入操作日志生成...."));
        }
        
        //执行对应的日志服务
        boolean configurationFlag = logObj.configurationFlag();
        LogFactoryService factory = new LogFactoryService();
        LogCommonService logServer = factory.getLogService(configurationFlag);
        logServer.generateStLog(joinPoint,logObj);
    }
}
