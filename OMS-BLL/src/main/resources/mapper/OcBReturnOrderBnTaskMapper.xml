<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jackrain.nea.oc.oms.mapper.OcBReturnOrderBnTaskMapper">

    <!-- 批量插入关联记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO oc_b_return_order_bn_task (
            id, oc_b_return_order_id, bill_no, bn_task_id, task_param, task_status,
            creatorid, creatorname, creationdate, modifierid, modifiername, modifieddate, isactive
        ) VALUES
        <foreach collection="taskList" item="item" separator=",">
            (
                #{item.id}, #{item.ocBReturnOrderId}, #{item.billNo}, #{item.bnTaskId}, #{item.taskParam}, #{item.taskStatus},
                #{item.creatorid}, #{item.creatorname}, #{item.creationdate}, #{item.modifierid}, #{item.modifiername}, #{item.modifieddate}, #{item.isactive}
            )
        </foreach>
    </insert>

</mapper>
