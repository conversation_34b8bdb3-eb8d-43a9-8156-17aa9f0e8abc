# apollo.bootstrap.enabled=true\u8868\u793AApollo\u5728\u7A0B\u5E8F\u521D\u59CB\u5316\u7684\u65F6\u5019\u5C31\u5F00\u59CB\u6CE8\u5165\uFF0C\u5E76\u4E0B\u8F7D\u5BF9\u5E94\u7684\u914D\u7F6E\u4FE1\u606F\u3002
# \u5982\u679C\u8BBE\u7F6E\u4E3Afalse\u6216\u8005\u53D6\u6D88\u4F1A\u5BFC\u81F4\u67D0\u4E9B\u914D\u7F6E\u9519\u8BEF
#apollo.bootstrap.enabled=true
# apollo.bootstrap.namespace \u8868\u793A\u52A0\u8F7DApollo\u7684namespace\u7684\u5185\u5BB9\u3002\u91C7\u7528\u82F1\u6587,\u8FDE\u63A5\u8868\u793A\u591A\u4E2Anamespace
#apollo.bootstrap.namespaces=application,drds,dubbo,redis,elasticsearch,slaverrds,mq,lts,oss
# \u591A\u8BED\u8A00\u9ED8\u8BA4\u8BBE\u7F6E\u3002\u5728Apollo\u4E2D\u914D\u7F6E\u7684\u65E0\u6CD5\u751F\u6548\uFF0C\u53EA\u80FD\u5728application.properties\u914D\u7F6E
spring.locale.default=zh_CN
server.port=8087
spring.profiles.active=dev
spring.application.name=R3-OC-OMS-BLL