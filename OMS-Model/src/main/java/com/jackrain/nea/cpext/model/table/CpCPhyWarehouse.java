package com.jackrain.nea.cpext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@TableName(value = "cp_c_phy_warehouse")
@Data
public class CpCPhyWarehouse extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ECODE")
    private String ecode;

    @JSONField(name = "ENAME")
    private String ename;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "WMS_WAREHOUSE_CODE")
    private String wmsWarehouseCode;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "OWNER_CODE")
    private String ownerCode;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "WMS_ACCOUNT")
    private String wmsAccount;

    @JSONField(name = "IS_SAP")
    private Integer isSap;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "WMS_URL")
    private String wmsUrl;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "REMARK")
    private String remark;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "CONTACT_NAME")
    private String contactName;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "MOBILEPHONE_NUM")
    private String mobilephoneNum;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "PHONE_NUM")
    private String phoneNum;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "SELLER_PROVINCE_ID")
    private Long sellerProvinceId;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "SELLER_CITY_ID")
    private Long sellerCityId;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "SELLER_AREA_ID")
    private Long sellerAreaId;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "SELLER_ZIP")
    private String sellerZip;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "SEND_ADDRESS")
    private String sendAddress;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "CP_C_CUSTOMER_ID")
    private Long cpCCustomerId;

    @JSONField(name = "CP_C_CUSTOMER_ECODE")
    private String cpCCustomerEcode;

    @JSONField(name = "CP_C_CUSTOMER_ENAME")
    private String cpCCustomerEname;

    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "QTY_PKG_MAX")
    private BigDecimal qtyPkgMax;

    @JSONField(name = "WMS_CONTROL_WAREHOUSE")
    private Integer wmsControlWarehouse;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "CUT_TIME")
    private Date cutTime;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "JD_CUT_TIME")
    private Date jdCutTime;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "JIT_WAREHOUSE_ECODE")
    private String jitWarehouseEcode;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "RETURN_PHY_WAREHOUSE_ID")
    private Long returnPhyWarehouseId;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "RETURN_PHY_WAREHOUSE_ECODE")
    private String returnPhyWarehouseEcode;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "RETURN_PHY_WAREHOUSE_ENAME")
    private String returnPhyWarehouseEname;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "JIT_WAREHOUSE_ID")
    private Long jitWarehouseId;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "CP_C_STORE_ID_LOST")
    private Long cpCStoreIdLost;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "IS_PASS_PRO")
    private Integer isPassPro;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "CP_C_STORE_CC_ID")
    private Long cpCStoreCcId;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "ORIGINAL_RETURN_PHY_WAREHOUSE_ID")
    private Long originalReturnPhyWarehouseId;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "ORIGINAL_RETURN_PHY_WAREHOUSE_ECODE")
    private String originalReturnPhyWarehouseEcode;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "ORIGINAL_RETURN_PHY_WAREHOUSE_ENAME")
    private String originalReturnPhyWarehouseEname;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "IS_SYN")
    private Integer isSyn;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "IS_SYN_MA")
    private Integer isSynMa;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "WH_TYPE")
    private String whType;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "AREATYPE")
    private Long areatype;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "CUSTOMER_LEVEL")
    private Long customerLevel;

    @JSONField(name = "SELF_MEMTION_POINT")
    private String selfMemtionPoint;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "STORE_SHIP_GRADE")
    private Long storeShipGrade;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "SHIP_FLUCTUATION_NUM")
    private Integer shipFluctuationNum;

    // merge ref ys

    @JSONField(name = "LINE_PKG_MAX")
    private Integer linePkgMax;

    @JSONField(name = "CP_C_STORE_ID_DEFECTIVE")
    private Long cpCStoreIdDefective;


    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "SAP_STORE_ECODE")
    private String sapStoreEcode;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "SAP_STORE_ECODE_DEFECTIVE")
    private String sapStoreEcodeDefective;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "THIRD_WAREHOUSE_TYPE")
    private Integer thirdWarehouseType;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "JIT_THRESHOLD")
    private Long jitThreshold;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "ISNEGATIVE")
    private Long isnegative;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "CP_C_STORE_NATURE_ID")
    private Long cpCStoreNatureId;

    @TableField(fill = FieldFill.UPDATE)
    @JSONField(name = "CP_C_CUSTOMER_SUPPLIER_ID")
    private Long cpCCustomerSupplierId;

    /**
     * 是否为POS管控仓
     */
    @JSONField(name = "IS_POS")
    private String isPos;

    /**
     * JITX面单格式
     */
    @JSONField(name = "JITX_FACE_SHEET_FORMAT")
    @TableField(fill = FieldFill.UPDATE)
    private String jitxFaceSheetFormat;

    @JSONField(name = "IS_TRANSFER_TMS")
    @TableField(fill = FieldFill.UPDATE)
    private String isTransferTms;

    @JSONField(name = "WMS_TYPE")
    @TableField(fill = FieldFill.UPDATE)
    private String wmsType;

    @JSONField(name = "CP_C_WAREHOUSE_ID")
    private Long cpCWarehouseId;

    @JSONField(name = "CP_C_WAREHOUSE_ECODE")
    private String cpCWarehouseEcode;

    @JSONField(name = "CP_C_WAREHOUSE_ENAME")
    private String cpCWarehouseEname;

    @JSONField(name = "OMS_MANAGEMENT")
    private Long omsManagement;

    /**
     * N  不允许
     * Y  允许  默认
     */
    @JSONField(name = "IS_MERGE_ORDER")
    private String isMergeOrder;

    /**
     * 预计到货时间-天
     */
    @JSONField(name = "ARRIVAL_DAYS")
    private Integer arrivalDays;
}