
package com.jackrain.nea.cpext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "cp_c_shop")
@Data
public class CpShop extends BaseModel {

    private static final long serialVersionUID = -3640864022759556319L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_PLATFORM_ID")
    private Long cpCPlatformId;

    @JSONField(name = "CP_C_PLATFORM_ECODE")
    private String cpCPlatformEcode;

    @JSONField(name = "CP_C_PLATFORM_ENAME")
    private String cpCPlatformEname;

    @JSONField(name = "CP_C_CUSTOMER_ID")
    private Long cpCCustomerId;

    @JSONField(name = "CP_C_CUSTOMER_ECODE")
    private String cpCCustomerEcode;

    @JSONField(name = "CP_C_CUSTOMER_ENAME")
    private String cpCCustomerEname;

    @JSONField(name = "COMPANY")
    private String company;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "SHOP_TYPE")
    private String shopType;

    @JSONField(name = "SELLER_NICK")
    private String sellerNick;

    @JSONField(name = "SELLER_NAME")
    private String sellerName;

    @JSONField(name = "SELLER_PHONE")
    private String sellerPhone;

    @JSONField(name = "SELLER_PROVINCE_ID")
    private Long sellerProvinceId;

    @JSONField(name = "SELLER_CITY_ID")
    private Long sellerCityId;

    @JSONField(name = "SELLER_AREA_ID")
    private Long sellerAreaId;

    @JSONField(name = "SELLER_PROVINCE")
    private String sellerProvince;

    @JSONField(name = "SELLER_CITY")
    private String sellerCity;

    @JSONField(name = "SELLER_AREA")
    private String sellerArea;

    @JSONField(name = "SELLER_ADDRESS")
    private String sellerAddress;

    @JSONField(name = "SELLER_ZIP")
    private String sellerZip;

    @JSONField(name = "SHOP_SECRET_KEY")
    private String shopSecretKey;

    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @JSONField(name = "CHANNEL_TYPE")
    private String channelType;

    @JSONField(name = "PLATFORM_SUPPLIER_ID")
    private String platformSupplierId;

    @JSONField(name = "RETURN_ADDRESS_ID")
    private String returnAddressId;

    @JSONField(name = "RETURN_ADDRESS")
    private String returnAddress;

    @JSONField(name = "QINGLONG_CODE")
    private String qinglongCode;

    @JSONField(name = "CAINIAO_CODE")
    private String cainiaoCode;

    @JSONField(name = "APPAUTHTOKEN")
    private String appauthtoken;

    @JSONField(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "ECODE")
    private String ecode;

    @JSONField(name = "INVOICE_COMPANY")
    private String invoiceCompany;

    @JSONField(name = "CP_C_DIRECT_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCDirectShopId;

    @JSONField(name = "REGIONAL_STORE")
    private String regionalStore;

    @JSONField(name = "CAINIAO_URL")
    private String cainiaoUrl;

    @JSONField(name = "COOPERATION_NO")
    private String cooperationNo;

    @JSONField(name = "VIP_RETURNS")
    private Long vipReturns;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "STORE_CHANNEL")
    private String storeChannel;

    @JSONField(name = "PS_C_BRAND_ID")
    private Long psCBrandId;

    @JSONField(name = "CP_C_CUSTOMER_SETTLE_ECODE")
    private String cpCCustomerSettleEcode;

    @JSONField(name = "CP_C_SUPPLIER_ECODE")
    private String cpCSupplierEcode;

    @JSONField(name = "COMPANY_ECODE")
    private String companyEcode;

    @JSONField(name = "COMPANY_ENAME")
    private String companyEname;

    @JSONField(name = "CP_C_COMPANY_BODY_ID")
    private Long cpCCompanyBodyId;

    @JSONField(name = "STORE_CATEGARY")
    private String storeCategary;

    @JSONField(name = "CP_C_SUPPLIER_ID")
    private Long cpCSupplierId;

    @JSONField(name = "BRAND_ECODE")
    private String brandEcode;

    @JSONField(name = "VENDOR_RETURN_SHOP")
    private String vendorReturnShop;

    @JSONField(name = "CP_C_SHOP_NATURE_ID")
    private Long cpCShopNatureId;

    @JSONField(name = "IS_CONTROL")
    private String isControl;

    @JSONField(name = "BILL_DATE")
    private Integer billDate;

    @JSONField(name = "CURRENT_ACCOUNT_DATE")
    private Date currentAccountDate;

    @JSONField(name = "CRM_SHOP_ID")
    private Long crmShopId;

    @JSONField(name = "PLATFORM_SUPPLIER_NAME")
    private String platformSupplierName;

    /**
     * 所属聚合仓
     */
    @JSONField(name = "SG_C_SHARE_STORE_ID")
    private Long sgCShareStoreId;

    @JSONField(name = "IS_TRANSFER_TMS")
    private Integer isTransferTms;

    @JSONField(name = "JD_WMS_SHOP_ECODE")
    private String jdWmsShopEcode;

    /**
     * 所属渠道
     */
    @JSONField(name = "channel")
    private Integer channel;

    @JSONField(name = "is_equal_exchange")
    private String isEqualExchange;



    @JSONField(name = "WAREHOUSE_CODE")
    private String warehouseCode;

    @JSONField(name = "OWNER_CODE")
    private String ownerCode;

    //免费组织编码
    @JSONField(name = "FREE_ORGANIZATION_CODE")
    private String freeOrganizationCode;

    //常规组织编码
    @JSONField(name = "GENERAL_ORGANIZATION_CODE")
    private String generalOrganizationCode;

    @JSONField(name = "CUSTOMER_GROUPING")
    private Integer customerGrouping;

    @JSONField(name = "customer_id")
    private String customerId;

    @JSONField(name = "jc_shop_name")
    private String jcShopName;

    @JSONField(name = "TIMEOUT_PLATE")
    private Integer timeoutPlate;
}