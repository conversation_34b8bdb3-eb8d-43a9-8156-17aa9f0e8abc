package com.jackrain.nea.cpext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 13:40
 */
@TableName(value = "CP_C_LOGISTICS")
@Data
public class CpCLogistics extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ECODE")
    private String ecode;

    @JSONField(name = "ENAME")
    private String ename;

    @JSONField(name = "SHORT_NAME")
    private String shortName;

    @JSONField(name = "LOGISTICS_TYPE")
    private String logisticsType;

    @JSONField(name = "MAILNO_REGULAR")
    private String mailnoRegular;

    @JSONField(name = "ACCOUNT_NO")
    private String accountNo;

    @J<PERSON><PERSON><PERSON>(name = "PASSWORD")
    private String password;

    @J<PERSON><PERSON>ield(name = "PAY_TYPE")
    private String payType;

    @JSONField(name = "EXPRESS_TYPE")
    private Integer expressType;

    @JSONField(name = "MONTHLY_CARD_NO")
    private String monthlyCardNo;

    @JSONField(name = "WEIGHT_TYPE")
    private Integer weightType;

    @JSONField(name = "SEND_CITY_CODE")
    private String sendCityCode;

    @JSONField(name = "URL")
    private String url;

    @JSONField(name = "CAINIAO_URL")
    private String cainiaoUrl;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "SERVICE_CODE")
    private String serviceCode;

    @JSONField(name = "COMPANY_CODE")
    private String companyCode;

    @JSONField(name = "TMS_LOGISTIC")
    private String tmsLogistic;

    @JSONField(name = "TYPE")
    private Integer type;

    @JSONField(name = "VIP_LOGISTICS_CODE")
    private String vipLogisticsCode;
}