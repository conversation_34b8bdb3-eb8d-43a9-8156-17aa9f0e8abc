package com.jackrain.nea.cpext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "cp_c_region")
@Data
public class CpCRegion extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "REGIONTYPE")
    private String regiontype;

    @JSONField(name = "ECODE")
    private String ecode;

    @J<PERSON><PERSON>ield(name = "ENAME")
    private String ename;

    @J<PERSON><PERSON>ield(name = "C_UP_ID")
    private Long cUpId;

    @JSO<PERSON>ield(name = "MIXNAME")
    private String mixname;

    @J<PERSON><PERSON><PERSON>(name = "REMARK")
    private String remark;

    @J<PERSON><PERSON>ield(name = "MODIFIERE<PERSON><PERSON>")
    private String modifierename;

    @J<PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @J<PERSON>NField(name = "CITY_LEVEL")
    private String cityLevel;
}