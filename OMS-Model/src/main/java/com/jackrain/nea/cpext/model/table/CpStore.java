package com.jackrain.nea.cpext.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-05-09
 * create at : 2019-05-09 2:33 PM
 */
@TableName(value = "cp_c_store")
@Data
public class CpStore extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_DISTRIB_ID")
    private Long cpCDistribId;

    @JSONField(name = "CP_C_STOREORG_ID")
    private Long cpCStoreorgId;

    @JSONField(name = "CP_C_HRORG_ID")
    private Long cpCHrorgId;

    @JSONField(name = "CP_C_CUSTOMER_ID")
    private Long cpCCustomerId;

    @JSONField(name = "CP_C_CUSTOMER_ECODE")
    private String cpCCustomerEcode;

    @JSONField(name = "CP_C_CUSTOMER_ENAME")
    private String cpCCustomerEname;

    @JSONField(name = "ECODE")
    private String ecode;

    @JSONField(name = "ENAME")
    private String ename;

    @JSONField(name = "ALLNAME")
    private String allname;

    @JSONField(name = "MIXNAME")
    private String mixname;

    @JSONField(name = "CP_C_STORERULE_ID")
    private Long cpCStoreruleId;

    @JSONField(name = "STORENATURE")
    private String storenature;

    @JSONField(name = "ISWMS")
    private Long iswms;

    @JSONField(name = "CP_C_FATWH_ID")
    private Long cpCFatwhId;

    @JSONField(name = "CP_C_PRO_ID")
    private Long cpCProId;

    @JSONField(name = "CP_C_CITY_ID")
    private Long cpCCityId;

    @JSONField(name = "CP_C_DIST_ID")
    private Long cpCDistId;

    @JSONField(name = "ADDRESS")
    private String address;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "LOCATION")
    private Long location;

    @JSONField(name = "SALECATE")
    private Long salecate;

    @JSONField(name = "SALETYPE")
    private Long saletype;

    @JSONField(name = "AREATYPE")
    private Long areatype;

    @JSONField(name = "BUSSTATE")
    private String busstate;

    @JSONField(name = "STORETYPE")
    private String storetype;

    @JSONField(name = "IMAGELEVEL")
    private Long imagelevel;

    @JSONField(name = "PREFER")
    private Long prefer;

    @JSONField(name = "SEABATCH")
    private Long seabatch;

    @JSONField(name = "BAGTYPE")
    private Long bagtype;

    @JSONField(name = "AREAS")
    private BigDecimal areas;

    @JSONField(name = "SALEAREAS")
    private BigDecimal saleareas;

    @JSONField(name = "MRENT")
    private BigDecimal mrent;

    @JSONField(name = "RESEVER")
    private BigDecimal resever;

    @JSONField(name = "PRODUCTER")
    private Long producter;

    @JSONField(name = "BIGMANAGER")
    private Long bigmanager;

    @JSONField(name = "CP_C_EXPCOMPANY_ID")
    private Long cpCExpcompanyId;

    @JSONField(name = "SALERQTY")
    private Long salerqty;

    @JSONField(name = "CP_C_STOREORG_ID1")
    private Long cpCStoreorgId1;

    @JSONField(name = "CP_C_STOREORG_ID2")
    private Long cpCStoreorgId2;

    @JSONField(name = "CP_C_STOREORG_ID3")
    private Long cpCStoreorgId3;

    @JSONField(name = "CP_C_STOREORG_ID4")
    private Long cpCStoreorgId4;

    @JSONField(name = "CP_C_STOREORG_ID5")
    private Long cpCStoreorgId5;

    @JSONField(name = "CP_C_STOREORG_ID6")
    private Long cpCStoreorgId6;

    @JSONField(name = "CP_C_STOREORG_ID7")
    private Long cpCStoreorgId7;

    @JSONField(name = "CP_C_STOREORG_ID8")
    private Long cpCStoreorgId8;

    @JSONField(name = "CP_C_STOREORG_ID9")
    private Long cpCStoreorgId9;

    @JSONField(name = "CP_C_STOREORG_ID10")
    private Long cpCStoreorgId10;

    @JSONField(name = "CONTACTER")
    private String contacter;

    @JSONField(name = "MOBIL")
    private String mobil;

    @JSONField(name = "PHONE")
    private String phone;

    @JSONField(name = "FAX")
    private String fax;

    @JSONField(name = "POST")
    private String post;

    @JSONField(name = "EMAIL")
    private String email;

    @JSONField(name = "ORUNIFY")
    private Long orunify;

    @JSONField(name = "SETTLERATE")
    private BigDecimal settlerate;

    @JSONField(name = "ISOUT")
    private Long isout;

    @JSONField(name = "ISRECEPT")
    private Long isrecept;

    @JSONField(name = "ISDISCENTER")
    private Long isdiscenter;

    @JSONField(name = "ISNEGATIVE")
    private Long isnegative;

    @JSONField(name = "RECYCLE_WH_ID")
    private Long recycleWhId;

    @JSONField(name = "RET_WH_ID")
    private Long retWhId;

    @JSONField(name = "ISWITHPUR")
    private Long iswithpur;

    @JSONField(name = "WEIGHT")
    private BigDecimal weight;

    @JSONField(name = "RECYCLE")
    private Long recycle;

    @JSONField(name = "RETCYCLE")
    private Long retcycle;

    @JSONField(name = "PREINDAYS")
    private Long preindays;

    @JSONField(name = "ISWEEKADD")
    private Long isweekadd;

    @JSONField(name = "LOGISTICS")
    private String logistics;

    @JSONField(name = "PRORATION")
    private BigDecimal proration;

    @JSONField(name = "DATEBEGIN")
    private Long datebegin;

    @JSONField(name = "DATEEND")
    private Long dateend;

    @JSONField(name = "DATEOPEN")
    private Long dateopen;

    @JSONField(name = "DATEINV")
    private Long dateinv;

    @JSONField(name = "WEPAY_ID")
    private Long wepayId;

    @JSONField(name = "ALIPAY_ID")
    private Long alipayId;

    @JSONField(name = "ISUSEPOS")
    private Long isusepos;

    @JSONField(name = "POSVERIFY")
    private String posverify;

    @JSONField(name = "CP_C_SUPPLIER_ID")
    private Long cpCSupplierId;

    @JSONField(name = "DATETIMEDIM5")
    private Date datetimedim5;

    @JSONField(name = "DATETIMEDIM4")
    private Date datetimedim4;

    @JSONField(name = "DATETIMEDIM3")
    private Date datetimedim3;

    @JSONField(name = "DATETIMEDIM2")
    private Date datetimedim2;

    @JSONField(name = "DATETIMEDIM1")
    private Date datetimedim1;

    @JSONField(name = "DATEDIM10")
    private Long datedim10;

    @JSONField(name = "DATEDIM9")
    private Long datedim9;

    @JSONField(name = "DATEDIM8")
    private Long datedim8;

    @JSONField(name = "DATEDIM7")
    private Long datedim7;

    @JSONField(name = "DATEDIM6")
    private Long datedim6;

    @JSONField(name = "DATEDIM5")
    private Long datedim5;

    @JSONField(name = "DATEDIM4")
    private Long datedim4;

    @JSONField(name = "DATEDIM3")
    private Long datedim3;

    @JSONField(name = "DATEDIM1")
    private Long datedim1;

    @JSONField(name = "DATEDIM2")
    private Long datedim2;

    @JSONField(name = "ARRIVETIME")
    private Date arrivetime;

    @JSONField(name = "STORECATE")
    private Long storecate;

    @JSONField(name = "CLOSEDATE")
    private Long closedate;

    @JSONField(name = "MARKETRATE")
    private BigDecimal marketrate;

    @JSONField(name = "RETPREDAYS")
    private Long retpredays;

    @JSONField(name = "GROUPID")
    private Long groupid;

    @JSONField(name = "PROCOE")
    private Long procoe;

    @JSONField(name = "ECSTORE")
    private Long ecstore;

    @JSONField(name = "DECDIM1")
    private BigDecimal decdim1;

    @JSONField(name = "DECDIM2")
    private BigDecimal decdim2;

    @JSONField(name = "DECDIM3")
    private BigDecimal decdim3;

    @JSONField(name = "DECDIM4")
    private BigDecimal decdim4;

    @JSONField(name = "DECDIM5")
    private BigDecimal decdim5;

    @JSONField(name = "DECDIM6")
    private BigDecimal decdim6;

    @JSONField(name = "DECDIM7")
    private BigDecimal decdim7;

    @JSONField(name = "DECDIM8")
    private BigDecimal decdim8;

    @JSONField(name = "DECDIM9")
    private BigDecimal decdim9;

    @JSONField(name = "DECDIM10")
    private BigDecimal decdim10;

    @JSONField(name = "NUMDIM1")
    private Long numdim1;

    @JSONField(name = "NUMDIM2")
    private Long numdim2;

    @JSONField(name = "NUMDIM3")
    private Long numdim3;

    @JSONField(name = "NUMDIM4")
    private Long numdim4;

    @JSONField(name = "NUMDIM5")
    private Long numdim5;

    @JSONField(name = "NUMDIM6")
    private Long numdim6;

    @JSONField(name = "NUMDIM7")
    private Long numdim7;

    @JSONField(name = "NUMDIM8")
    private Long numdim8;

    @JSONField(name = "NUMDIM9")
    private Long numdim9;

    @JSONField(name = "NUMDIM10")
    private Long numdim10;

    @JSONField(name = "TXTDIM1")
    private String txtdim1;

    @JSONField(name = "TXTDIM2")
    private String txtdim2;

    @JSONField(name = "TXTDIM3")
    private String txtdim3;

    @JSONField(name = "TXTDIM4")
    private String txtdim4;

    @JSONField(name = "TXTDIM5")
    private String txtdim5;

    @JSONField(name = "TXTDIM6")
    private String txtdim6;

    @JSONField(name = "TXTDIM7")
    private String txtdim7;

    @JSONField(name = "TXTDIM8")
    private String txtdim8;

    @JSONField(name = "TXTDIM9")
    private String txtdim9;

    @JSONField(name = "TXTDIM10")
    private String txtdim10;

    @JSONField(name = "VP_C_VIPTYPEGROUP_ID")
    private Long vpCViptypegroupId;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "HR002_ID")
    private String hr002Id;

    @JSONField(name = "PLAN_CHECK_DATE")
    private Long planCheckDate;

    @JSONField(name = "CLOSETIME")
    private Date closetime;

    @JSONField(name = "OPENTIME")
    private Date opentime;

    @JSONField(name = "CLOSERID")
    private Long closerid;

    @JSONField(name = "CLOSERENAME")
    private String closerename;

    @JSONField(name = "OPENERENAME")
    private String openerename;

    @JSONField(name = "OPENERID")
    private Long openerid;

    @JSONField(name = "WMS_STORE_ECODE")
    private String wmsStoreEcode;

    @JSONField(name = "ACCESS_NO_BILL")
    private String accessNoBill;

    @JSONField(name = "WMS_STORE_ENAME")
    private String wmsStoreEname;

    @JSONField(name = "EXPAND_REGION")
    private Long expandRegion;

    @JSONField(name = "EXPAND_PROVINCE")
    private Long expandProvince;

    @JSONField(name = "EXPAND_EMP")
    private Long expandEmp;

    @JSONField(name = "CLOSE_REASON")
    private Long closeReason;

    @JSONField(name = "CLOSE_CONTRACT_DAYS")
    private Integer closeContractDays;

    @JSONField(name = "CHANNEL_LEVEL1")
    private Long channelLevel1;

    @JSONField(name = "CHANNEL_LEVEL2")
    private Long channelLevel2;

    @JSONField(name = "CHANNEL_LEVEL3")
    private Long channelLevel3;

    @JSONField(name = "CARD_NO")
    private String cardNo;

    @JSONField(name = "IS_OPEN_BANK")
    private String isOpenBank;

    @JSONField(name = "IS_SHARE")
    private String isShare;

    @JSONField(name = "CLOSERNAME")
    private String closername;

    @JSONField(name = "OPENERNAME")
    private String openername;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @JSONField(name = "PS_C_BRAND_IDS")
    private String psCBrandIds;

    @JSONField(name = "WEAADDRESS")
    private String weaaddress;

    @JSONField(name = "IMAGES")
    private String images;

    @JSONField(name = "IS_AUTO_IN")
    private String isAutoIn;

    @JSONField(name = "IS_AUTO_OUT")
    private String isAutoOut;

    @JSONField(name = "STREET")
    private String street;

    @JSONField(name = "ROAD_NUMBER")
    private String roadNumber;

    @JSONField(name = "IS_JOINT")
    private String isJoint;

    @JSONField(name = "FUTURES_STATUS")
    private Long futuresStatus;

    @JSONField(name = "MANAGE_BRAND")
    private Long manageBrand;

    @JSONField(name = "CHANNEL_TYPE")
    private Long channelType;

    @JSONField(name = "COOPERATION_SYSTEM")
    private Long cooperationSystem;

    @JSONField(name = "MARKET_NAME")
    private String marketName;

    @JSONField(name = "SHOP_TYPE")
    private Long shopType;

    @JSONField(name = "RACK_TYPE")
    private Long rackType;

    @JSONField(name = "CITY_LEVEL")
    private Long cityLevel;

    @JSONField(name = "BUSINESS_CODE")
    private String businessCode;

    @JSONField(name = "BUSINESS_TYPE")
    private Long businessType;

    @JSONField(name = "SPECIFIC_ADDRESS")
    private String specificAddress;

    @JSONField(name = "PLACE")
    private String place;

    @JSONField(name = "COOPERATE_SYSTEM")
    private Long cooperateSystem;

    @JSONField(name = "BUSINESS_ABB")
    private String businessAbb;

    @JSONField(name = "BUSINESS_MATURITY")
    private Long businessMaturity;

    @JSONField(name = "SHOP_QUANTITY")
    private Integer shopQuantity;

    @JSONField(name = "MANAGE_ASCRIPTION")
    private String manageAscription;

    @JSONField(name = "SHOP_GOODS_LEVEL")
    private Long shopGoodsLevel;

    @JSONField(name = "IS_INDEPENDENT_CASH")
    private String isIndependentCash;

    @JSONField(name = "FIRST_OPENTIME")
    private Date firstOpentime;

    @JSONField(name = "EARLIEST_SALES_DATE")
    private Date earliestSalesDate;

    @JSONField(name = "LATEST_SALES_DATE")
    private Date latestSalesDate;

    @JSONField(name = "BUSINESS_LEVEL")
    private Long businessLevel;

    @JSONField(name = "IS_MAIN_WAREHOUSE")
    private Integer isMainWarehouse;

    @JSONField(name = "MEMBER_TRANS_STORE")
    private String memberTransStore;

    @JSONField(name = "CUSTOMER_LEVEL")
    private Long customerLevel;

    @JSONField(name = "BRANCH_CUSTOMER_NAME")
    private String branchCustomerName;

    @JSONField(name = "BRANCH_CUSTOMER_CODE")
    private String branchCustomerCode;

    @JSONField(name = "IS_MODIFIED")
    private String isModified;

    @JSONField(name = "IS_SEND_DEMOGIC")
    private String isSendDemogic;

    @JSONField(name = "GOODS_ASCRIBE")
    private Long goodsAscribe;

    @JSONField(name = "CP_C_STORE_NATURE_ID")
    private String cpCStoreNatureId;

    @JSONField(name = "VIP_CUSTOMER_ECODE")
    private String vipCustomerEcode;

    @JSONField(name = "TMALL_STORE_ECODE")
    private String tmallStoreEcode;
    /**
     * 所属聚合仓
     */
    @JSONField(name = "SG_C_SHARE_STORE_ID")
    private Long sgCShareStoreId;

    /**
     * 优先级
     */
    @JSONField(name = "PRIORITY")
    private Integer priority;
    /**
     * 保底库存量
     */
    @JSONField(name = "MIN_QTY")
    private BigDecimal minQty;

    /**
     * 是否允许零售
     */
    @JSONField(name = "IS_RETAIL")
    private String isRetail;

    /**
     * '唯品店仓性质：1 仓库 2 门店'
     */
    @JSONField(name = "VIP_STORE_NATURE")
    private String vipStoreNature;

    /**
     * 上级经销商
     */
    @JSONField(name = "C_CUSTOMERUP_ID")
    private Integer cCustomerupId;

    @JSONField(name = "WERKS")
    private String werks;
}
