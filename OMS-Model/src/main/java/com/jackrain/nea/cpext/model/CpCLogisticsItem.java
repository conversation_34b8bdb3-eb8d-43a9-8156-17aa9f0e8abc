package com.jackrain.nea.cpext.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "cp_c_logistics_item")
@Data
public class CpCLogisticsItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_PLATFORM_ID")
    private Long cpCPlatformId;

    @<PERSON><PERSON><PERSON><PERSON>(name = "CP_C_PLATFORM_ECODE")
    private String cpCPlatformEcode;

    @J<PERSON><PERSON>ield(name = "CP_C_PLATFORM_ENAME")
    private String cpCPlatformEname;

    @J<PERSON>NField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}