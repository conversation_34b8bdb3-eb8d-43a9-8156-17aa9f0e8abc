package com.jackrain.nea.ps.model;

import com.jackrain.nea.web.face.User;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/3/13 14:38
 * describe 款号删除
 */
public class OmsProDelCmdRequest implements Serializable {

    private Long objId;

    private String tableName;

    private List proECodes;

    private String proECode;

    public String getProECode() {
        return proECode;
    }

    public void setProECode(String proECode) {
        this.proECode = proECode;
    }

    private String key;

    private Long destId;
    private String beginDate;
    private String endDate;
    public OmsProDelCmdRequest(Long objId, String tableName, String proECode, String key) {
        this.objId = objId;
        this.tableName = tableName;
        this.proECode = proECode;
        this.key = key;
    }
    public OmsProDelCmdRequest(Long objId, String tableName, String proECode, String key, Long destId) {
        this.objId = objId;
        this.tableName = tableName;
        this.proECode = proECode;
        this.key = key;
        this.destId = destId;
    }
    public OmsProDelCmdRequest(Long objId, String tableName, String proECode, String key, Long destId, String  beginDate, String endDate) {
        this.objId = objId;
        this.tableName = tableName;
        this.proECode = proECode;
        this.key = key;
        this.destId = destId;
        this.beginDate=beginDate;
        this.endDate=endDate;
    }
    private User user;

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Long getObjId() {
        return objId;
    }

    public void setObjId(Long objId) {
        this.objId = objId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List getProECodes() {
        return proECodes;
    }

    public void setProECodes(List proECodes) {
        this.proECodes = proECodes;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }



    public OmsProDelCmdRequest(Long objId, String tableName, List proECodes, String key) {
        this.objId = objId;
        this.tableName = tableName;
        this.proECodes = proECodes;
        this.key = key;
    }

    public Long getDestId() {
        return destId;
    }

    public void setDestId(Long destId) {
        this.destId = destId;
    }
}
