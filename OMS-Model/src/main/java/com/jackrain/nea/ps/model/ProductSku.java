package com.jackrain.nea.ps.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * @author: 易邵峰
 * @since: 2019-01-28
 * create at : 2019-01-28 09:08
 */
@Data
public class ProductSku implements Serializable {

    private static final long serialVersionUID = 8555880016098028196L;
    /**
     * SKU对应的数据ID
     */
    private long id;

    /**
     * 条码SKU内容
     */
    private String sku;

    /**
     * 编码
     */
    private String ecode;

    /**
     * 款号ID
     */
    private long prodId;

    /**
     * 款号Code
     */
    private String prodCode;

    /**
     * 商品名称
     */
    private String name;


    /**
     * SKU类型
     * 1=实际SKU
     * 2=组合SKU
     * 3=福袋SKU
     * 4=预售活动SKU
     */
    private int skuType;

    /**
     * 标准重量
     */
    private BigDecimal weight;

    /**
     * 69码
     */
    private String barcode69;

    /**
     * 标准单价
     */
    private BigDecimal price;

    /**
     * 吊牌价
     */
    private BigDecimal pricelist;

    /**
     * 规格
     */
    private String skuSpec;

    /**
     * 价格比例--组合商品
     */
    private BigDecimal pricePercent;

    /**
     * 物料类型
     */
    private String materialType;

    /**
     * 颜色ID
     */
    private long colorId;

    /**
     * 颜色编码
     */
    private String colorCode;

    /**
     * 颜色名称
     */
    private String colorName;

    /**
     * 尺寸ID
     */
    private long sizeId;

    /**
     * 尺寸编码
     */
    private String sizeCode;

    /**
     * 尺寸名称
     */
    private String sizeName;

    /**
     * 组合商品对应的数量
     */
    private BigDecimal num;
    /**
     * sku条码
     */
    private String skuEcode;


    /**
     * 性别
     */
    private Long sex;


    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 是否为虚拟订单
     */
    private String isVirtual;

    /**
     * sku名称
     */
    private String skuName;
    /**
     * 是否为组合商品
     */
    private String isGroup;
    /**
     * 商品品牌
     */
    private Long psCBrandId;

    /***
     * 供应类型 0 正常 1.代销轻供 2.寄售轻供
     */
    private Long psCProSupplyType;

    /**
     * 是否是天猫购物金 Y N
     */
    private String tmallExpandCard;

    /**
     * 国际条码
     */
    private String forCode;

    /**
     * 组合商品分摊比例(组合商品拆分用)
     */
    private BigDecimal ratio;

    /**
     * 组合商品拆单信息 平台相关信息
     */
    private String numiid;
    private String skuId;
    private String canSplit;

    /**
     * temp
     */
    private String isGift;
    private String spec;

    /**
     * 基价下限
     */
    private BigDecimal basePriceDown;
    /**
     * 效期
     */
    private String isEnableExpiry;

    private Long mDim4Id;

    private Long mDim2Id;

    /**
     * 品项
     */
    private Long mDim6Id;

    /**
     * 零级
     */
    private Long mDim12Id;


    /**
     * 商品属性的map  key为属性ps_c_pro里属性字段的大写 如果没值  需要到ps里
     */
    private Map<String, OmsProAttributeInfo> proAttributeMap;


    private String weightUnit;

    private String isactive;

}
