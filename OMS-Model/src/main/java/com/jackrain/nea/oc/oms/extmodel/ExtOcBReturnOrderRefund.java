package com.jackrain.nea.oc.oms.extmodel;

import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 14:33
 **/
@Data
public class ExtOcBReturnOrderRefund extends OcBReturnOrderRefund {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    //行号
    private int rowNum;
    //头子表关联列
    private String BK;
    //退货金额
    private BigDecimal allAmtRefund;

    /**
     * 导入生成模型
     *
     * @return
     */
    public static ExtOcBReturnOrderRefund importCreate(int index, ExtOcBReturnOrderRefund refund, Map<String, String> columnMap) {
        try {
            refund.setPsCSkuEcode(columnMap.get(rowStr + index + cellStr + 0));
        } catch (Exception e) {

        }
        try {
            refund.setQtyRefund(new BigDecimal(columnMap.get(rowStr + index + cellStr + 1)));
        } catch (Exception e) {

        }
        try {
            refund.setBK(columnMap.get(rowStr + index + cellStr + 2));
        } catch (Exception e) {

        }
        refund.setRowNum(index + 1);
        return refund;
    }
}
