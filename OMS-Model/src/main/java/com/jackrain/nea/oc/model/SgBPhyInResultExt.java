package com.jackrain.nea.oc.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 入库结果单
 *
 * @author: xiWen.z
 * create at: 2019/5/9 0009
 */
@Data
public class SgBPhyInResultExt implements Serializable {

    /**
     * 来源单据ID
     */
    private Long sourceBillId;

    /**
     * 实体仓ID
     */
    private Long cpCPhyWarehouseId;

    /**
     * 逻辑仓ID
     */
    private Long cpCStoreId;

    /**
     * 逻辑仓CODE
     */
    private String cpCStoreEcode;

    /**
     * 逻辑仓名称
     */
    private String cpCstoreEname;

}
