package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/4/4 5:32 下午
 * @Version 1.0
 */
@Data
public class OmsOccupyRelation {

    /**
     * 订单主编
     */
    private OcBOrder ocBOrder;

    /**
     * 订单明细
     */
    private List<OcBOrderItem> ocBOrderItems;


    /**
     * 是否整单占用(默认false)
     */
    private boolean specChange;

    /**
     * 报警方式 1.报警（允许负库存）2.报错  3.报缺货
     */
    private Integer warningType;

    /**
     * 修改方式  1.全量 2.增量
     * 默认2
     */
    private Integer updateMethod;


    /**
     * 单据类型  1.零售发货，2.销售发货，3采购退货发货，4 调拨发货， 5 销售退货发货
     * 默认 1  SgConstantsIF.BILL_TYPE_RETAIL
     */
    private Integer sourceBillType;


}
