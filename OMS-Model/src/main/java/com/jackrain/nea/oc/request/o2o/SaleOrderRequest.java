package com.jackrain.nea.oc.request.o2o;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Desc : 销售订单
 * <AUTHOR> xiWen
 * @Date : 2020/8/22
 */
@Data
public class SaleOrderRequest implements Serializable {


    private static final long serialVersionUID = 7601943876704552846L;


    /**
     * String	true	A123	单据编号	出库通知单单号
     */
    @JSONField(name = "orderBillCode")
    private String orderBillCode;

    /**
     * String	true	A123	原单据号	零售发货单单据编号
     */
    @JSONField(name = "orderWebCod")
    private String orderWebCod;
    /**
     * String	true	HZ01	实体仓
     */
    @JSONField(name = "shopCode")
    private String shopCode;
    /**
     * String	true	20171010	营业日期	订单下单时间
     */
    @JSONField(name = "billTime")
    private String billTime;
    /**
     * Number	true	100	数量	商品总数
     */
    @JSONField(name = "quantity")
    private String quantity;

    /**
     * String	true	100	标准价格	sum(平台售价)
     */
    @JSONField(name = "money")
    private String money;
    /**
     * String	true	100	成交金额	sum(成交金额)
     */
    @JSONField(name = "realMoney")
    private String realMoney;
    /**
     * todo String	true	9	折扣金额	sum(成交金额)- sum(平台售价)
     */
    @JSONField(name = "discount")
    private String discount;
    /**
     * String	true	支付方式：0-线上支付1-线下支付,	支付方式	固定‘线上支付’
     */
    @JSONField(name = "paymethod")
    private String paymethod;
    /**
     * String	true	张三	姓名	收货人姓名
     */
    @JSONField(name = "name")
    private String name;
    /**
     * String	true	13000000000	手机号	收货人手机号
     */
    @JSONField(name = "phone")
    private String phone;
    /**
     * String	true	浙江省	省	省
     */
    @JSONField(name = "province")
    private String province;
    /**
     * String	true	杭州市	市	市
     */
    @JSONField(name = "city")
    private String city;
    /**
     * String	false	余杭区	区	区
     */
    @JSONField(name = "district")
    private String district;

    /**
     * String	false	文一西路	街道	固定空
     */
    @JSONField(name = "addres")
    private String addres;

    /**
     * String	true	969号	快递地址	收货人地址
     */
    @JSONField(name = "shippingAddress")
    private String shippingAddress;

    /**
     * String	false	张三	制单人	固定空
     */
    @JSONField(name = "creater")
    private String creater;

    /**
     * String	false	2017-10-1012:00:00	制单日期	固定空
     */
    @JSONField(name = "creationDate")
    private String creationDate;

    /**
     * String	false	备注信息	备注	固定空
     */
    @JSONField(name = "note")
    private String note;

    /**
     * String	false	SF	快递代码	固定空
     */
    @JSONField(name = "shippingCode")
    private String shippingCode;

    /**
     * String	false	201711110011	快递单号	固定空
     */
    @JSONField(name = "shippingSn")
    private String shippingSn;

    /**
     * String	true	1	店员代码	固定空
     */
    @JSONField(name = "salerEmployeeNo")
    private String salerEmployeeNo;

    /**
     * String	true	退单	退单	固定false
     */
    @JSONField(name = "th_act")
    private String thAct;

    /**
     * String	true	来源系统0-POS、1-中台、2-Retail、3-OMS、4-其他	来源系统	固定R
     */
    @JSONField(name = "system")
    private String system;

    /**
     * String	true	来源平台：0-后台1-淘宝2-拍拍3-OS主站4-分销商5-京东11-亚马逊13-一号店等
     * 来源平台（中台的平台代码为准）	根据店铺关联查找来源平台，取来源平台的平台编码
     */
    @JSONField(name = "lypt")
    private String lypt;

    /**
     * String	true	来源店铺代码	来源店铺代码	下单店铺代码
     */
    @JSONField(name = "lyzd_dm")
    private String lyzdDm;

    /**
     * String	true	来源店铺名称	来源店铺名称	下单店铺名称
     */
    @JSONField(name = "lyzd_mc")
    private String lyzdMc;

    /**
     * String	true	来源渠道代码	来源渠道代码	根据店铺关联查找来源平台，取来源平台的平台编码
     */
    @JSONField(name = "lyorg_dm")
    private String lyorgDm;

    /**
     * String	true	来源渠道名称	来源渠道名称	根据店铺关联查找来源平台，取来源平台的平台名称
     */
    @JSONField(name = "lyorg_mc")
    private String lyorgMc;

    /**
     * String	true	下单门店代码	下单门店代码	同城购给平台分配的，非同城购的给默认值default
     */
    @JSONField(name = "xdzd_dm")
    private String xdzdDm;

    /**
     * String	true	顾客留言	顾客留言	买家备注
     */
    @JSONField(name = "gkly")
    private String gkly;

    /**
     * String	true	客服备注	客服备注	卖家备注
     */
    @JSONField(name = "kfbz")
    private String kfbz;

    /**
     * String	true	结算代码	结算代码	固定空
     */
    @JSONField(name = "pos_outer_code")
    private String posOuterCode;

    /**
     * String	true		扩展属性
     */
 /*   @JSONField(name = "extend_props")
    private String extendProps;*/

    /**
     * String	true	12233	路由参数
     */
    @JSONField(name = "customerid")
    private String customerid;

    /**
     * Struct[]	true	　	商品明细
     */
    @JSONField(name = "item")
    private List<SaleOrderItem> item;

    /**
     * 扩展属性
     */
    @JSONField(name = "extend_props")
    private JSONObject extend_props;


    @Data
    public static class SaleOrderItem implements Serializable {

        private static final long serialVersionUID = 2677604081863277349L;

        /**
         * String	true	0-正常1-促销2-换购3-赠品4-让利5-补差优惠6-VIP叠加优惠
         * 7-生日优惠8-整单叠加优惠9-积分换购11-换货，12-已换货,13-打折	状态	固定0
         */
        @JSONField(name = "status")
        private String status;

        /**
         * 1. String	true	1000	成交金额	成交金额
         */
        @JSONField(name = "amount")
        private String amount;

        /**
         * 2. String	true	1000	参考金额	平台售价*数量
         */
        @JSONField(name = "referenceAmount")
        private String referenceAmount;

        /**
         * 3. String	true	1000	数量	数量
         */
        @JSONField(name = "quantity")
        private String quantity;

        /**
         * 4.  String	true	8	折扣金额	成交金额-平台售价*数量
         */
        @JSONField(name = "discount")
        private String discount;

        /**
         * 5. String	true	80	单价	成交单价
         */
        @JSONField(name = "price")
        private String price;

        /**
         * 6. String	true	100	参考价	平台售价
         */
        @JSONField(name = "referencePrice")
        private String referencePrice;


        /**
         * String	true	L	尺码代码	根据商品SKU从商品中心获取，商品中心提供获取服务
         */
        @JSONField(name = "sizeCode")
        private String sizeCode;

        /**
         * String	true	A001	颜色代码	根据商品SKU从商品中心获取，商品中心提供获取服务
         */
        @JSONField(name = "colorCode")
        private String colorCode;

        /**
         * String	true	A001	商品代码	商品款号
         */
        @JSONField(name = "goodsCode")
        private String goodsCode;

        /**
         * String	false	A001	商品条码	商品SKU
         */
     /*   @JSONField(name = "skuCode")
        private String skuCode;
*/

        /**
         * String	true	20200725001	伯俊OMS中台订单商品行ID	商品明细ID
         */
        @JSONField(name = "subOrderID")
        private String subOrderID;


        /**
         * false	2020072600121	平台单号	平台单号
         */
      /*  @JSONField(name = "tid")
        private String tid;
*/

    }

    @Data
    public static class ExtendProps implements Serializable {

        /**
         * String	false	zhangsan	主播ID	第一主播ID不为空商品的主播ID
         */
        @JSONField(name = "anchor_id")
        private String anchorId;


        /**
         * String	false	张三	主播昵称	第一主播ID不为空商品的主播昵称
         */
        @JSONField(name = "anchor_name")
        private String anchorName;

        /**
         * Number	true	0	是否完美履约（0非完美履约，1完美履约）	同城购订单传1，非同城购订单转0
         */
        @JSONField(name = "is_perfect_p")
        private int isPerfectP;

        /**
         * 平台单号	平台单号
         */
        @JSONField(name = "tid")
        private String tid;

    }
}
