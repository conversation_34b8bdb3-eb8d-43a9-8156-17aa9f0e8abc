package com.jackrain.nea.oc.oms.model.constant;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @ClassName InvoiceConst
 * @Description 发票相关值
 * @Date 2022/9/1 上午9:49
 * @Created by wuhang
 */
public interface InvoiceConst {

    // 店铺开票策略 是否允许开票
    public static String ALLOW = "1";
    public static String NOT_ALLOW = "0";

    // 是否赠品
    public static final Integer GIFT = 1;
    public static final String NOT_GIFT = "0";

    // 发票billno前缀
    public static final String INVOICE_PREFIX = "FP";

    public static final String AC_F_INVOICE_APPLY = "ac_f_invoice_apply";
    public static final String AC_F_INVOICE_APPLY_ITEM = "ac_f_invoice_apply_item";
    public static final String AC_F_ORDER_INVOICE = "ac_f_order_invoice";
    public static final String AC_F_ORDER_INVOICE_ITEM = "ac_f_order_invoice_item";
    public static final String AC_F_ORDER_INVOICE_SYSTEM_ITEM = "ac_f_order_invoice_system_item";
    public static final String AC_F_INVOICE_RETURN_RED_OFFSET = "ac_f_invoice_return_red_offset";
    public static final String AC_F_INVOICE_RETURN_RED_OFFSET_ITEM = "ac_f_invoice_return_red_offset_item";

    public static final String REDIS_LOCK_KEY_PREFIX = "oms:invoice:change:lock:";

    public static final List<Integer> cancelOrderStatusList = Lists.newArrayList(7,8);

    // -------发票申请表---------
    interface TransStatus{
        public static final String NOT_TRANS = "0";
        public static final String TRANS_SUCCESS = "1";
        public static final String TRANS_FAIL = "2";
    }

    // -------订单开票表----------

    // 订单发票审核状态
    interface AuditStatus {
        public static final String NOT_AUDIT = "0"; // 未审核
        public static final String AUDITED = "1"; // 已审核
    }


    // 开票状态
    interface InvoiceStatus {
        public static final String NOT_INVOICE = "0";// 未开票
        public static final String IN_INVOICE = "1";// 开票中
        public static final String INVOICE_SUCCESS = "2";// 开票成功
        public static final String INVOICE_FAIL = "3";// 开票失败
        public static final String INVOICE_REVOKE = "4";// 已撤销
    }

    // 换票状态
    interface ChangeInvoiceStatus {
        public static final String NOT_CHANGE_INVOICE = "0";// 未换票
        public static final String IN_CHANGE_INVOICE = "1";// 换票中
        public static final String CHANGE_INVOICED = "2";// 已换票
    }

    // 冻结状态
    interface FreezeStatus{
        public static final String NOT_FREEZE = "0";// 未冻结
        public static final String FREEZED = "1";// 已冻结
    }

    // 红冲状态
    interface RedRushStatus{
        public static final String NOT_RED_RUSH = "0";// 未红冲
        public static final String RED_RUSHED = "1";// 已红冲
        public static final String IN_RED_RUSH = "2";// 红冲中
    }

    // 取消状态
    interface CancelStatus{
        public static final String NOT_CANCEL = "0";// 未取消
        public static final String CANCELED = "1";// 已取消
    }

    // 红蓝票类型
    interface TicketType{
        public static final String BLUE = "0";// 蓝票
        public static final String RED = "1"; // 红票
    }

    // 同步平台状态
    interface SyncPlatformStatus{
        public static final Integer NOT_SYNC = 0;// 未同步
        public static final Integer SYNCED = 1;// 已同步
    }

    // 发票种类
    interface InvoiceKind{
        public static final String ELECTRONIC = "1";// 电子发票
        public static final String PAPER = "0";// 纸质发票
    }

    // 发票类型
    interface InvoiceType{
        public static final String NORMAL = "0";// 普票
        public static final String SPECIAL = "1";// 专票
    }

    // 发票抬头类型
    interface HeaderType{
        public static final String INDIVIDUAL = "0";// 个人
        public static final String ENTERPRISE = "1";// 公司
    }

    // -------开票策略-----------
    // 商品开票名称
    interface InvoiceProNameType{
        public static final String PLATFORM_PRO_NAME = "0";// 平台商品名称
        public static final String ORDER_PRO_NAME = "1";// 订单商品名称
    }

    // sku开票名称
    interface InvoiceSkuNameType{
        public static final String PLATFORM_SKU_NAME = "0";// 平台商品名称
        public static final String ORDER_SKU_NAME = "1";// 订单商品名称
    }

    // 开票备注策略
    interface InvoiceRemark{
        public static final String PLATFORM_ID = "0";// 平台单号
        public static final String INVOICE_REMARK = "1";// 开票备注
    }

    // 是否允许修改金额
    interface ModifyAMt{
        public static final String ALLOW_MODIFY = "1";// 允许
        public static final String NOT_MODIFY = "0";// 不允许
    }

    // 赠品是否开票
    interface InvoiceGIft{
        public static final String NOT_INVOICE = "0";// 不开票
        public static final String INVOICE = "1";// 按照数量分摊
        public static final String INVOICE_ZERO = "2";// 0金额开票
        public static final String COST_ALLOCATION = "3";// 成本价分摊
        public static final String PRICE_ALLOCATION = "4";// 吊牌价分摊

    }

    // 抬头为空
    interface EmptyHeaderPersonInvoice{
        public static final String NOT_INVOICE = "0";// 不开票
        public static final String INVOICE = "1";// 按照数量分摊
    }

    interface InvoiceNode{
        public static final String TRADE_SUCCESS_ALL = "0"; // 交易成功-全部订单
        public static final String TRADE_SUCCESS_PART = "1"; // 交易成功-部分订单
        public static final String AFTER_SHIPMENT_ALL = "2"; // 发货后-全部订单
        public static final String AFTER_SHIPMENT_PART = "3"; // 发货后-部分订单
        public static final String AFTER_VERIFY= "4"; // 审核后
    }

    // -------换票种类--------
    interface InvoiceChangeType{
        public static final String CHANGE_INVOICE_KIND = "1";// 换纸质
        public static final String CHANGE_INVOICE_TYPE = "2";// 换专票
        public static final String CHANGE_INVOICE_HEADER = "3";// 换抬头
    }

    interface MergeInvoiceFlag{
        public static final String MERGE = "1";
        public static final String NOT_MERGE = "0";
    }



    interface ReturnGoodsFlag{
        public static final String YES = "1";
        public static final String NO = "0";
    }

    // ---------退单待红冲发票表---------
    // 退货待红冲转换状态
    interface ReturnRedOffsetChangeStatus {
        public static final String NOT_CHANGE = "0";// 未转换
        public static final String CHANGE_SUCCESS = "1";// 已转换
        public static final String CHANGE_FAIL = "2";// 转换失败
    }

    // 是否整单退货 1是,0否
    interface RedOffsetAllReturnFlag{
        public static final String ALL_RETURN = "1";
        public static final String PART_RETURN = "0";
    }

    interface ProType {
        // 普通商品
        public static final Long MORMAL = 0L;
        // 福袋
        public static final Long FD = 1L;
        // 组合商品
        public static final Long GROUP = 2L;
        // 预售商品
        public static final Long PRE_SELL = 3L;
        // 未拆分的组合商品
        public static final Long GROUP_NOT_SPLIT = 4L;
        // 轻供商品
        public static final Long QG = 5L;
    }

    // 金税获取开票结果接口状态码枚举
    interface getInvoiceResultApiCode{
        public static final String SUCCESS = "1";
        public static final String FAIL = "-1";
    }

    // 开票控制 0-手动审核，1-自动审核
    interface InvoiceControl{
        public static final String MUANUAL = "0";// 手动审核
        public static final String AUTO = "1";// 自动审核
    }
}
