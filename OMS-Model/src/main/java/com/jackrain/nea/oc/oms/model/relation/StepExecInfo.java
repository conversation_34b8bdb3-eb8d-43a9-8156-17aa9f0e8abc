package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.enums.StepExeState;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/29
 */
@Data
public class StepExecInfo implements Serializable {

    private static final long serialVersionUID = 3687870049494580329L;

    private int tag;

    private Long id;

    private String shardKey;

    private String relationShardKey;

    private String transMessage;

    private Integer transStatusVal;

    private TransferOrderStatus transStatus;

    private StepExeState stepExeState;

    /**
     * reset
     */
    private int isNeedReTrans;

    private String globalMessage;

    private Integer userId;

    private String userName;

    private String userEName;

    /**
     * oc
     */
    private Map<Long, List<Long>> ocOrderKeys;

    public void markStepInfo(StepExeState state, TransferOrderStatus transStatus, String message) {
        this.stepExeState = state;
        this.transMessage = message;
        this.transStatus = transStatus;
        this.transStatusVal = transStatus.toInteger();
    }

    public static StepExecInfo build(Long id, String shardKey) {
        StepExecInfo stepExecInfo = new StepExecInfo();
        stepExecInfo.setId(id);
        stepExecInfo.setShardKey(shardKey);
        return stepExecInfo;
    }

    public StepExecInfo reSetId(Long id) {
        this.id = id;
        return this;
    }

    public StepExecInfo assignUserInfo(User user) {
        this.setUserId(user.getId());
        this.setUserName(user.getName());
        this.setUserEName(user.getEname());
        return this;
    }

    public StepExecInfo pointRelation(OmsJDDirectCancelRelation omsJDDirectCancelRelation) {
        omsJDDirectCancelRelation.setStepExecInfo(this);
        return this;
    }

    public void reviseTransStatus(TransferOrderStatus status) {
        this.transStatus = status;
        this.transStatusVal = this.transStatus.toInteger();
        if (StringUtils.isNotBlank(this.globalMessage)) {
            this.transMessage = this.globalMessage;
        }
    }

    public void markGlobalFailStateMessage(String message) {
        this.globalMessage = message;
        this.isNeedReTrans = OcBOrderConst.IS_STATUS_IY;
    }

    public void collectOcKeys(Long id, List<Long> subIds) {
        if (this.ocOrderKeys == null) {
            this.ocOrderKeys = new HashMap<>();
        }
        this.ocOrderKeys.put(id, subIds);
    }

}
