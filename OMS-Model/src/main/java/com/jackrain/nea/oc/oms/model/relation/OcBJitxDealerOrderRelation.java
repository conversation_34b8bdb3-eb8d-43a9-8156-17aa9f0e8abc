package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBCancelTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.web.face.User;
import lombok.Data;

/**
 * description：jitx经销商任务
 *
 * <AUTHOR>
 * @date 2021/12/30
 */
@Data
public class OcBJitxDealerOrderRelation {

    private OcBOrder ocBOrder;

    private IpBTimeOrderVip timeOrderVip;

    private IpBCancelTimeOrderVip cancelTimeOrderVip;

    private Long warehouseId;

    private User user;

}
