package com.jackrain.nea.oc.oms;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;

/**
 * 孙勇生
 * create at:  2019/8/28  15:23
 *
 * @description: String转换Long 定义反序列化 暂时没用
 */
public class ToLongDeserializer implements ObjectDeserializer {


    @Override
    public Long deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        String val = parser.getLexer().stringVal();
        System.out.printf("val:" + val);
        if (null != val) {
            return Long.parseLong(val);
        }
        return null;
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }
}