package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: 黄世新
 * @Date: 2019/5/7 1:06 PM
 * @Version 1.0
 */

@Data
public class OcBOrderParam {
    /**
     * 订单信息
     */
    private OcBOrder ocBOrder;

    private List<OcBOrderItem> orderItemList;

    private List<OcBOrderNaiKa> ocBOrderNaiKas;


}
