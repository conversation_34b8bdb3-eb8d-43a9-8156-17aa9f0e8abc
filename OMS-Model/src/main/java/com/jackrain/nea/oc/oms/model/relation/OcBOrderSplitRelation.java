package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.util.List;

/**
 * description：订单拆分
 *
 * <AUTHOR>
 * @date 2022/1/10
 */
@Data
public class OcBOrderSplitRelation {
    /**
     * 订单信息
     */
    private OcBOrder orderInfo;

    private List<OcBOrderItem> orderItemList;

    private User user;

    private String logType;

    private Long originOrderId;

    private Integer splitReason;
}
