package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-08-22
 * create at : 2019-08-22 1:54 PM
 */
@Data
public class ScanIncomingRelation {
    /**
     * 退货入库单主表
     */
    OcBRefundIn ocBRefundIn;

    /**
     * 退货入库单明细
     */
    List<OcBRefundInProductItem> itemList;
    /**
     * 退换货单id
     */
    Long returnId;
    /**
     * 是否强制入库
     */
    Integer isForce;

    public Long getRefundInId() {
        if (ocBRefundIn.getId() == null) {
            return -1L;
        }
        return ocBRefundIn.getId();
    }
}
