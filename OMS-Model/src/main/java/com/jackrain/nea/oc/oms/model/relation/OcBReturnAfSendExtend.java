package com.jackrain.nea.oc.oms.model.relation;

import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @author: 夏继超
 * @create: 2019-05-13 10:30
 **/
@Data
public class OcBReturnAfSendExtend extends OcBReturnAfSend implements Serializable {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";


    //错误信息
    private String desc;
    //头子表关联列
    private String BK;
    //行号
    private int rowNum;

    private List<OcBReturnAfSendItemExtend> afSendItemExtendList;

    public OcBReturnAfSendExtend() {

    }


    /**
     * 校验字段非空
     */
    public static Boolean checkForImport(List<OcBReturnAfSendExtend> afSendExtendList) {
        Boolean checkFlag = true;
        for (OcBReturnAfSendExtend afSendExtend : afSendExtendList) {
            // 数据缺失校验
            StringBuffer checkMessage = new StringBuffer();
            //获取明细数据
            List<OcBReturnAfSendItemExtend> afSendItemExtendList = afSendExtend.getAfSendItemExtendList();

            if (StringUtils.isEmpty(afSendExtend.getSourceBillNo())) {
                checkMessage.append("[主表原始订单编号不允许为空]");
            }
            if (StringUtils.isEmpty(afSendExtend.getCpCShopTitle())) {
                checkMessage.append("[主表主表下单店铺不允许为空]");
            }
            if (StringUtils.isEmpty(afSendExtend.getTid())) {
                checkMessage.append("[主表原始平台退款单号不允许为空]");
            }
            if (afSendExtend.getBillType() == null) {
                checkMessage.append("[主表退款类型不允许为空]");
            }
            if (StringUtils.isEmpty(afSendExtend.getVipNick())) {
                checkMessage.append("[主表买家昵称不允许为空]");
            }
            if (StringUtils.isEmpty(afSendExtend.getPayMode())) {
                checkMessage.append("[主表支付方式不允许为空]");
            }
            if (StringUtils.isEmpty(afSendExtend.getPayAccount())) {
                checkMessage.append("[主表支付账号不允许为空]");
            }
            if (afSendExtend.getAmtReturnApply() == null) {
                checkMessage.append("[主表退款金额不允许为空]");
            }
            if (StringUtils.isEmpty(afSendExtend.getBK())) {
                checkMessage.append("[主表头子表关联列不允许为空]");
            }
            //校验明细表数据
            for (OcBReturnAfSendItemExtend itemExtend : afSendItemExtendList) {
                if (StringUtils.isEmpty(itemExtend.getPsCSkuEcode())) {
                    checkMessage.append("[明细表第" + itemExtend.getRowNum() + "行,SKU条码不允许为空]");
                }
                if (itemExtend.getAmtReturn() == null) {
                    checkMessage.append("[明细表第" + itemExtend.getRowNum() + "行,退款金额不允许为空]");
                }

                if (StringUtils.isEmpty(itemExtend.getBK())) {
                    checkMessage.append("[明细表第" + itemExtend.getRowNum() + "行,头子表关联列不允许为空]");
                }
            }
            if (StringUtils.isNotEmpty(checkMessage.toString())) {
                afSendExtend.setDesc(checkMessage.toString());
                checkFlag = false;
            }
        }

        return checkFlag;
    }

    /**
     * 关联主表-》明细表
     */
    public static Boolean dealBK(List<OcBReturnAfSendExtend> afSendExtendList, List<OcBReturnAfSendItemExtend> afSendItemExtendList) {
        Boolean checkFlag = true;
        for (OcBReturnAfSendExtend afSendExtend : afSendExtendList) {
            List<OcBReturnAfSendItemExtend> afSendItemExtends = Lists.newArrayList();
            for (OcBReturnAfSendItemExtend itemExtend : afSendItemExtendList) {
                if (afSendExtend.getBK().equals(itemExtend.getBK())) {
                    afSendItemExtends.add(itemExtend);
                }
            }
            if (CollectionUtils.isEmpty(afSendItemExtends)) {
                afSendExtend.setDesc("没有明细!");
                checkFlag = false;
            }
            afSendExtend.setAfSendItemExtendList(afSendItemExtends);
        }
        return checkFlag;
    }

    /**
     * 导入生成模型
     *
     * @return
     */
    public static OcBReturnAfSendExtend importCreate(int index, OcBReturnAfSendExtend afSendExtend, Map<String, String> columnMap) {
        try {
            //原始订单编号
            afSendExtend.setSourceBillNo(columnMap.get(rowStr + index + cellStr + 0));
        } catch (Exception e) {

        }
        try {
            //店铺名称
            afSendExtend.setCpCShopTitle(columnMap.get(rowStr + index + cellStr + 1));
        } catch (Exception e) {

        }
        try {
            //原始平台单号
            afSendExtend.setTid((columnMap.get(rowStr + index + cellStr + 2)));
        } catch (Exception e) {

        }
        try {
            //平台退款单号
            afSendExtend.setTReturnId(columnMap.get(rowStr + index + cellStr + 3));
        } catch (Exception e) {

        }

        try {
            //退款类型
            afSendExtend.setBillType(Integer.valueOf(columnMap.get(rowStr + index + cellStr + 4)));
        } catch (Exception e) {

        }
        try {
            //买家昵称
            afSendExtend.setVipNick(columnMap.get(rowStr + index + cellStr + 5));
        } catch (Exception e) {

        }

        try {
            //退款原因
            afSendExtend.setReason(columnMap.get(rowStr + index + cellStr + 6));
        } catch (Exception e) {

        }
        try {
            //支付方式
            afSendExtend.setPayMode(columnMap.get(rowStr + index + cellStr + 7));
        } catch (Exception e) {

        }


        try {
            //支付账号
            afSendExtend.setPayAccount(columnMap.get(rowStr + index + cellStr + 8));
        } catch (Exception e) {

        }
        try {
            //退款金额
            afSendExtend.setAmtReturnApply(new BigDecimal(columnMap.get(rowStr + index + cellStr + 9)));
        } catch (Exception e) {

        }
        try {
            //判责方
            afSendExtend.setResponsibleParty(columnMap.get(rowStr + index + cellStr + 10));
        } catch (Exception e) {

        }
        try {
            //判责方备注
            afSendExtend.setResponsiblePartyRemark(columnMap.get(rowStr + index + cellStr + 11));
        } catch (Exception e) {

        }
        try {
            //备注
            afSendExtend.setRemark(columnMap.get(rowStr + index + cellStr + 12));
        } catch (Exception e) {

        }

        try {
            //头子表关联列
            afSendExtend.setBK(columnMap.get(rowStr + index + cellStr + 13));
        } catch (Exception e) {

        }
        afSendExtend.setRowNum(index + 1);

        return afSendExtend;
    }
}
