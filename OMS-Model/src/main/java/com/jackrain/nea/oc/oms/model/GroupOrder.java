package com.jackrain.nea.oc.oms.model;

import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * GroupOrder实体类
 *
 * @author: 胡林洋
 * @since: 2020-04-09
 * create at : 2020-04-09 16:27
 */
@Data
@ToString
public class GroupOrder {

    private Long orderId;
    private List<Long> skuIdList;
    private BigDecimal skuNum;
    private OcBOrderItem groupOrderItem;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public List<Long> getSkuIdList() {
        return skuIdList;
    }

    public void setSkuIdList(List<Long> skuIdList) {
        this.skuIdList = skuIdList;
    }

    public BigDecimal getSkuNum() {
        return skuNum;
    }

    public void setSkuNum(BigDecimal skuNum) {
        this.skuNum = skuNum;
    }

    public OcBOrderItem getGroupOrderItem() {
        return groupOrderItem;
    }

    public void setGroupOrderItem(OcBOrderItem groupOrderItem) {
        this.groupOrderItem = groupOrderItem;
    }

}
