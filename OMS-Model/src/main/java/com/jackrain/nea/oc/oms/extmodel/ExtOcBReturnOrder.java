package com.jackrain.nea.oc.oms.extmodel;

import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.model.enums.ChangeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 14:32
 **/
@Data
public class ExtOcBReturnOrder extends OcBReturnOrder {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    //行号
    private int rowNum;
    //头子表关联列
    private String BK;
    //错误信息
    private String desc;
    //单据类型名称
    private String billTypeName;
    //换货预留库存名称
    private String isReservedName;
    //是否原退
    private String isBackName;
    //退款原因
    private String returnReasonName;
    //入库实体仓
    private String cpCPhyWarehouseInEname;
    //单据状态
    private String returnStatusName;


    private List<OcBReturnOrderItem> ocBReturnOrderItemList;
    private List<ExtOcBReturnOrderExchange> extOcBReturnOrderExchangeList;
    private List<ExtOcBReturnOrderRefund> extOcBReturnOrderRefundList;

    /**
     * 处理明细是换货还是退货
     */
    public static void dealItem(List<ExtOcBReturnOrder> extOcBReturnOrderList) {
        for (ExtOcBReturnOrder extOcBReturnOrder : extOcBReturnOrderList) {
            List<ExtOcBReturnOrderRefund> extOcBReturnOrderRefundList = Lists.newArrayList(); //退货明细
            List<ExtOcBReturnOrderExchange> extOcBReturnOrderExchangeList = Lists.newArrayList(); //换货明细
            List<OcBReturnOrderItem> itemList = extOcBReturnOrder.getOcBReturnOrderItemList();
            if (extOcBReturnOrder.getBillType().equals(OcReturnBillTypeEnum.RETURN.getVal())) {
                itemList.forEach(x -> {
                    ExtOcBReturnOrderRefund extOcBReturnOrderRefund = new ExtOcBReturnOrderRefund();
                    extOcBReturnOrderRefund.setBK(x.getBK());
                    extOcBReturnOrderRefund.setPsCSkuEcode(x.getPsCSkuEcode());
                    extOcBReturnOrderRefund.setQtyRefund(x.getQty());
                    extOcBReturnOrderRefund.setRowNum(x.getRowNum());
                    extOcBReturnOrderRefundList.add(extOcBReturnOrderRefund);
                });
            } else if (extOcBReturnOrder.getBillType().equals(OcReturnBillTypeEnum.EXCHANGE.getVal())) {
                itemList.forEach(x -> {
                    ExtOcBReturnOrderExchange extOcBReturnOrderExchange = new ExtOcBReturnOrderExchange();
                    extOcBReturnOrderExchange.setBK(x.getBK());
                    extOcBReturnOrderExchange.setPsCSkuEcode(x.getPsCSkuEcode());
                    extOcBReturnOrderExchange.setQtyExchange(x.getQty());
                    extOcBReturnOrderExchange.setRowNum(x.getRowNum());
                    extOcBReturnOrderExchangeList.add(extOcBReturnOrderExchange);
                });
            }
            extOcBReturnOrder.setExtOcBReturnOrderRefundList(extOcBReturnOrderRefundList);
            extOcBReturnOrder.setExtOcBReturnOrderExchangeList(extOcBReturnOrderExchangeList);
        }
    }

    /**
     * 校验字段非空
     */
    public static Boolean checkForImport(List<ExtOcBReturnOrder> extOcBReturnOrderList) {
        Boolean checkFlag = true;
        for (ExtOcBReturnOrder extOcBReturnOrder : extOcBReturnOrderList) {
            // 数据缺失校验
            StringBuffer checkMessage = new StringBuffer();
            //获取明细数据
            List<ExtOcBReturnOrderRefund> refundList = extOcBReturnOrder.getExtOcBReturnOrderRefundList();
            List<ExtOcBReturnOrderExchange> exchangeList = extOcBReturnOrder.getExtOcBReturnOrderExchangeList();
            if (extOcBReturnOrder.getOrigOrderId() == null) {
                checkMessage.append("[主表原始订单编号不允许为空]");
            }
            if (StringUtils.isEmpty(extOcBReturnOrder.getBillTypeName()) || extOcBReturnOrder.getBillType() == null) {
                checkMessage.append("[主表单据类型不允许为空/不存在]");
            }
            if (StringUtils.isEmpty(extOcBReturnOrder.getReceiveName())) {
                checkMessage.append("[主表收货人姓名不允许为空]");
            }
            if (StringUtils.isEmpty(extOcBReturnOrder.getReceiveMobile())) {
                checkMessage.append("[主表收货人的手机号码不允许为空]");
            }
            if (StringUtils.isEmpty(extOcBReturnOrder.getReceiverProvinceName())) {
                checkMessage.append("[主表买家省名称不允许为空]");
            }
            if (StringUtils.isEmpty(extOcBReturnOrder.getReceiverCityName())) {
                checkMessage.append("[主表买家市名称不允许为空]");
            }
            if (StringUtils.isEmpty(extOcBReturnOrder.getReceiveAddress())) {
                checkMessage.append("[主表买家收货地址不允许为空]");
            }
            if (StringUtils.isEmpty(extOcBReturnOrder.getBK())) {
                checkMessage.append("[主表头子表关联列不允许为空]");
            }
            //校验退货明细表数据
            if (CollectionUtils.isNotEmpty(refundList)) {
                for (ExtOcBReturnOrderRefund refund : refundList) {
                    if (StringUtils.isEmpty(refund.getPsCSkuEcode())) {
                        checkMessage.append("[退货明细表第" + refund.getRowNum() + "行,条码不允许为空]");
                    }
                    if (refund.getQtyRefund() == null) {
                        checkMessage.append("[退货明细表第" + refund.getRowNum() + "行,数量不允许为空]");
                    }
                    if (StringUtils.isEmpty(refund.getBK())) {
                        checkMessage.append("[退货明细表第" + refund.getRowNum() + "行,头子表关联列不允许为空]");
                    }
                }
            }
            //校验换货明细表数据
            if (CollectionUtils.isNotEmpty(exchangeList)) {
                for (ExtOcBReturnOrderExchange exchange : exchangeList) {
                    if (StringUtils.isEmpty(exchange.getPsCSkuEcode())) {
                        checkMessage.append("[换货明细表第" + exchange.getRowNum() + "行,条码不允许为空]");
                    }
                    if (exchange.getQtyExchange() == null) {
                        checkMessage.append("[换货明细表第" + exchange.getRowNum() + "行,数量不允许为空]");
                    }
                    if (StringUtils.isEmpty(exchange.getBK())) {
                        checkMessage.append("[换货明细表第" + exchange.getRowNum() + "行,头子表关联列不允许为空]");
                    }
                }
            }
            if (StringUtils.isNotEmpty(checkMessage.toString())) {
                extOcBReturnOrder.setDesc(checkMessage.toString());
                checkFlag = false;
            }
        }
        return checkFlag;
    }

    /**
     * 关联主表-》明细表
     */
    public static Boolean dealBK(List<ExtOcBReturnOrder> extOcBReturnOrderList,
                                 List<ExtOcBReturnOrderRefund> extOcBReturnOrderRefundList,
                                 List<ExtOcBReturnOrderExchange> extOcBReturnOrderExchangeList) {
        Boolean checkFlag = true;
        for (ExtOcBReturnOrder orderExtend : extOcBReturnOrderList) {
            try {
                //退货明细
                List<ExtOcBReturnOrderRefund> refundList = Lists.newArrayList();
                for (ExtOcBReturnOrderRefund refund : extOcBReturnOrderRefundList) {
                    if (orderExtend.getBK().equals(refund.getBK())) {
                        refundList.add(refund);
                    }
                }
                orderExtend.setExtOcBReturnOrderRefundList(refundList);
                //换货明细
                List<ExtOcBReturnOrderExchange> exchangeList = Lists.newArrayList();
                for (ExtOcBReturnOrderExchange exchange : extOcBReturnOrderExchangeList) {
                    if (orderExtend.getBK().equals(exchange.getBK())) {
                        exchangeList.add(exchange);
                    }
                }
                orderExtend.setExtOcBReturnOrderExchangeList(exchangeList);
            } catch (Exception e) {

            }
            if (CollectionUtils.isEmpty(orderExtend.getExtOcBReturnOrderRefundList()) && CollectionUtils.isEmpty(orderExtend.getExtOcBReturnOrderExchangeList())) {
                orderExtend.setDesc("[没有明细!检查头子表关联字段!]");
                checkFlag = false;
            }
        }
        return checkFlag;
    }

    /**
     * 导入生成模型
     *
     * @return
     */
    public static ExtOcBReturnOrder importCreate(int index, ExtOcBReturnOrder extOcBReturnOrder, Map<String, String> columnMap) {
        try {
            //原始订单编号
            extOcBReturnOrder.setOrigOrderId(Long.valueOf(columnMap.get(rowStr + index + cellStr + 0)));
        } catch (Exception e) {

        }
        try {
            //单据类型
            extOcBReturnOrder.setBillTypeName(columnMap.get(rowStr + index + cellStr + 1));
            extOcBReturnOrder.setBillType(ChangeEnum.billTypeNameDatas.get(extOcBReturnOrder.getBillTypeName()));
        } catch (Exception e) {

        }
        try {
            //买家昵称
            extOcBReturnOrder.setBuyerNick(columnMap.get(rowStr + index + cellStr + 2));
        } catch (Exception e) {

        }
        try {
            //原始平台单号
            extOcBReturnOrder.setOrigSourceCode(columnMap.get(rowStr + index + cellStr + 3));
        } catch (Exception e) {

        }
        try {
            //店铺名称
            extOcBReturnOrder.setCpCShopTitle(columnMap.get(rowStr + index + cellStr + 4));
        } catch (Exception e) {

        }
        try {
            //平台退款单号
            extOcBReturnOrder.setReturnId(columnMap.get(rowStr + index + cellStr + 5));
        } catch (Exception e) {

        }
        try {
            //退回物流公司
            extOcBReturnOrder.setCpCLogisticsEname(columnMap.get(rowStr + index + cellStr + 6));
        } catch (Exception e) {

        }
        try {
            //退款原因
            extOcBReturnOrder.setReturnReasonName(columnMap.get(rowStr + index + cellStr + 7));
            extOcBReturnOrder.setReturnReason(ChangeEnum.refundReasonDatas.get(extOcBReturnOrder.getReturnReasonName()));
        } catch (Exception e) {

        }
        try {
            //退回物流单号
            extOcBReturnOrder.setLogisticsCode(columnMap.get(rowStr + index + cellStr + 8));
        } catch (Exception e) {

        }
        try {
            //换货预留库存
            extOcBReturnOrder.setIsReservedName(columnMap.get(rowStr + index + cellStr + 9));
            extOcBReturnOrder.setIsReserved(ChangeEnum.yesOrNoNameDatas.get(extOcBReturnOrder.getIsReservedName()));
        } catch (Exception e) {

        }
        try {
            //是否原退
            extOcBReturnOrder.setIsBackName(columnMap.get(rowStr + index + cellStr + 10));
            extOcBReturnOrder.setIsBack(ChangeEnum.yesOrNoNameDatas.get(extOcBReturnOrder.getIsBackName()));
        } catch (Exception e) {

        }
        try {
            //备注
            extOcBReturnOrder.setRemark(columnMap.get(rowStr + index + cellStr + 11));
        } catch (Exception e) {

        }
        try {
            //收货人
            extOcBReturnOrder.setReceiveName(columnMap.get(rowStr + index + cellStr + 12));
        } catch (Exception e) {

        }
        try {
            //收货人手机
            extOcBReturnOrder.setReceiveMobile(columnMap.get(rowStr + index + cellStr + 13));
        } catch (Exception e) {

        }
        try {
            //收货人电话
            extOcBReturnOrder.setReceivePhone(columnMap.get(rowStr + index + cellStr + 14));
        } catch (Exception e) {

        }
        try {
            //收货人邮编
            extOcBReturnOrder.setReceiveZip(columnMap.get(rowStr + index + cellStr + 15));
        } catch (Exception e) {

        }
        try {
            //收货人省份
            extOcBReturnOrder.setReceiverProvinceName(columnMap.get(rowStr + index + cellStr + 16));
        } catch (Exception e) {

        }
        try {
            //收货人市
            extOcBReturnOrder.setReceiverCityName(columnMap.get(rowStr + index + cellStr + 17));
        } catch (Exception e) {

        }
        try {
            //收货人区
            extOcBReturnOrder.setReceiverAreaName(columnMap.get(rowStr + index + cellStr + 18));
        } catch (Exception e) {

        }
        try {
            //收货人地址
            extOcBReturnOrder.setReceiveAddress(columnMap.get(rowStr + index + cellStr + 19));
        } catch (Exception e) {

        }
        try {
            //换货邮费
            extOcBReturnOrder.setShipAmt(new BigDecimal(columnMap.get(rowStr + index + cellStr + 20)));
        } catch (Exception e) {

        }
        try {
            //应退邮费
            extOcBReturnOrder.setReturnAmtShip(new BigDecimal(columnMap.get(rowStr + index + cellStr + 21)));
        } catch (Exception e) {

        }
        try {
            //其他金额
            extOcBReturnOrder.setReturnAmtOther(new BigDecimal(columnMap.get(rowStr + index + cellStr + 22)));
        } catch (Exception e) {

        }
        try {
            //代销结算金额
            extOcBReturnOrder.setConsignAmtSettle(new BigDecimal(columnMap.get(rowStr + index + cellStr + 23)));
        } catch (Exception e) {

        }
        try {
            //头子表关联列
            extOcBReturnOrder.setBK(columnMap.get(rowStr + index + cellStr + 24));
        } catch (Exception e) {

        }
        extOcBReturnOrder.setRowNum(index + 1);
        extOcBReturnOrder.checkDefault(extOcBReturnOrder);
        return extOcBReturnOrder;
    }

    //设置默认值
    public void checkDefault(ExtOcBReturnOrder extOcBReturnOrder) {

        if (extOcBReturnOrder.getIsTodrp() == null) {
            extOcBReturnOrder.setIsTodrp(0);
        }
        if (extOcBReturnOrder.getIsWriteoff() == null) {
            extOcBReturnOrder.setIsWriteoff(0);
        }
    }
}
