package com.jackrain.nea.oc.oms.model.constant;

/**
 * vip相关状态
 *
 * <AUTHOR>
 * @date 2020/12/7
 */
public class VipConstant {

    /**
     * 作废状态
     */
    // 启用
    public static final String ISACTIVE_Y = "Y";
    // 作废
    public static final String ISACTIVE_N = "N";

    /**
     * 表名
     */
    public static final String TB_NAME_IP_B_TIME_ORDER_VIP_ITEM = "ip_b_time_order_vip_item";
    public static final String TB_NAME_IP_B_TIME_ORDER_VIP_OCCUPY_ITEM = "ip_b_time_order_vip_occupy_item";

    /**
     * 唯品会退供PO单
     */
    public static final String OC_B_VIPCOM_RETURN_PO = "OC_B_VIPCOM_RETURN_PO";

    /**
     * 唯品会退供PO单-条码明细
     */
    public static final String OC_B_VIPCOM_RETURN_PO_ITEM = "OC_B_VIPCOM_RETURN_PO_ITEM";

    /**
     * 唯品会退供PO单-系统匹配条码明细表
     */
    public static final String OC_B_VIPCOM_RETURN_PO_SYSMATCH_ITEM = "OC_B_VIPCOM_RETURN_PO_SYSMATCH_ITEM";

    /**
     * 唯品会退供PO单-错发实际入库条码明细表
     */
    public static final String OC_B_VIPCOM_RETURN_PO_ERROR_ITEM = "OC_B_VIPCOM_RETURN_PO_ERROR_ITEM";

    /**
     * 唯品会退供PO单-单据状态
     * 1：待生成调拨 2：出库完成等待入库 3：出库完成部分入库 4：出库完成入库完成
     */
    public static final String OC_B_VIPCOM_RETURN_PO_STATUS1 = "1";
    public static final String OC_B_VIPCOM_RETURN_PO_STATUS2 = "2";
    public static final String OC_B_VIPCOM_RETURN_PO_STATUS3 = "3";
    public static final String OC_B_VIPCOM_RETURN_PO_STATUS4 = "4";

    /**
     * 时效订单 主表表名
     */
    public static final String IP_B_TIME_ORDER_VIP = "ip_b_time_order_vip";

    /**
     * 时效订单 主表分库键
     */
    public static final String IP_B_TIME_ORDER_VIP_KEY = "OCCUPIED_ORDER_SN";

    /**
     * 寻仓单 主表表名
     */
    public static final String IP_B_JITX_DELIVERY = "ip_b_jitx_delivery";

    /**
     * 寻仓单 主表分库键
     */
    public static final String IP_B_JITX_DELIVERY_KEY = "ORDER_SN";
    /**
     * JITX寻仓结果表 换仓类型 1-YY占单反馈；2-YY换仓申请
     */
    public static final Integer JITX_DELIVERY_RECORD_TYPE_OCCUPY = 1;
    public static final Integer JITX_DELIVERY_RECORD_TYPE_EXCHANGE = 2;
    /**
     * JITX寻仓结果表 换仓状态 1-申请换仓；2-换仓成功；3-换仓失败
     */
    public static final Integer JITX_DELIVERY_RECORD_EXCHANGING = 1;
    public static final Integer JITX_DELIVERY_RECORD_EXCHANGED = 2;
    public static final Integer JITX_DELIVERY_RECORD_EXCHANGE_FAIL = 3;
    /**
     * JITX寻仓结果表 寻仓单状态 ;0-占单失败 1-YY待占单；2-YY已占单；3-YY取消成功；4-YY取消失败
     */
    public static final Integer JITX_DELIVERY_RECORD_STATUS_OCCUPY_FAIL = 0;
    public static final Integer JITX_DELIVERY_RECORD_STATUS_ASIGNING = 1;
    public static final Integer JITX_DELIVERY_RECORD_STATUS_OCCUPIED = 2;
    public static final Integer JITX_DELIVERY_RECORD_STATUS_CANCELED = 3;
    public static final Integer JITX_DELIVERY_RECORD_STATUS_CANCEL_FAIL = 4;

    /**
     * 是否店发 1:是店发；0：非店发
     */
    public static final Integer JITX_DELIVERY_IS_STORE_DELIVERY_Y = 1;
    public static final Integer JITX_DELIVERY_IS_STORE_DELIVERY_N = 0;

    public static final String JITX_DELIVERY_STEP_RESULT_SUCCESS = "SUCCESS";
    public static final String JITX_DELIVERY_STEP_RESULT_FAILED = "FAILED";

    /**
     * 虚拟寻仓标记 0:未进行虚拟寻仓 1:虚拟寻仓中；2：虚拟寻仓完成
     */
    public static final Integer JITX_DELIVERY_IS_VIRTUAL_OCCUPY_00 = 0;
    public static final Integer JITX_DELIVERY_IS_VIRTUAL_OCCUPY_01 = 1;
    public static final Integer JITX_DELIVERY_IS_VIRTUAL_OCCUPY_02 = 2;


}
