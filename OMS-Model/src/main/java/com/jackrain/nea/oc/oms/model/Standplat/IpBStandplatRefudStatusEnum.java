package com.jackrain.nea.oc.oms.model.Standplat;

/**
 * 通用平台退单状态
 *
 * @author: 夏继超
 * @since: 2019/7/23
 * create at : 2019/7/23 10:06
 */
public enum IpBStandplatRefudStatusEnum {
    WAIT_SELLER_AGREE("买家发起退货，等待卖家同意", 1),

    WAIT_BUYER_RETURN_GOODS("卖家已经同意，等待买家发货", 2),

    WAIT_SELLER_CONFIRM_GOODS("买家已经退货，等待卖家确认收货", 3),

    SELLER_REFUSE_BUYER("已拒绝", 5),

    CLOSED("退款关闭", 6),

    PLEASE_REFUND("请退款", 7),

    WAIT_BUYER_RECEIVER_GOODS("卖家已发货，待买家收货", 8),

    BUYER_RECEIVERED("买家已收货", 9),

    SUCCESS("退款完成", 4),

    DEFAULT("DEFAULT", 0);


    String key;
    Integer val;

    IpBStandplatRefudStatusEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    public static IpBStandplatRefudStatusEnum val2Enum(Integer val) {
        for (IpBStandplatRefudStatusEnum value : IpBStandplatRefudStatusEnum.values()) {
            if (value.getVal().equals(val)) {
                return value;
            }
        }
        return DEFAULT;
    }

}
