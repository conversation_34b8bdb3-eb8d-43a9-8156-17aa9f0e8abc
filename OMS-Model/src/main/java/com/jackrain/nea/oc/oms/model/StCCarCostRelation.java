package com.jackrain.nea.oc.oms.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName StCCarCostRelation
 * @Description 组装的 零担报价、整车报价
 * <AUTHOR>
 * @Date 2024/4/9 08:59
 * @Version 1.0
 */
@Data
public class StCCarCostRelation implements Serializable {
    private static final long serialVersionUID = 5894416715570512921L;

    private List<StCCarCostInfo> stCCarCostInfos;

    private List<StCCarCostItemInfo> stCCarCostItemInfos;

}
