package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: 李龙飞
 * @create: 2019-05-13 10:30
 **/
@Data
@Slf4j
public class OcBOrderExtend extends OcBOrder implements Serializable {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final String SOURCE_CODE = "SOURCE_CODE";//平台单号
    public static final String BILL_NO = "BILL_NO";//单据编号
    public static final String ID = "ID";//订单编号
    public static final String USER_NICK = "USER_NICK";//买家昵称
    public static final String RECEIVER_NAME = "RECEIVER_NAME";//收货人姓名
    public static final String RECEIVER_MOBILE = "RECEIVER_MOBILE";//收货人手机号
    public static final String EXPRESSCODE = "EXPRESSCODE";//物流单号
    public static final String WMS_CANCEL_STATUS = "WMS_CANCEL_STATUS";//撤回状态
    public static final String ORDER_AMT = "ORDER_AMT";//订单金额
    public static final String PS_C_SKU_ECODE = "PS_C_SKU_ECODE";//商品条码
    public static final String QTY_ALL = "QTY_ALL";//商品数量
    public static final String INSIDE_REMARK = "INSIDE_REMARK";//买家备注
    public static final String BUYER_MESSAGE = "BUYER_MESSAGE";//卖家备注
    public static final String CP_C_SHOP_ID = "CP_C_SHOP_ID";//店铺
    public static final String CP_C_PHY_WAREHOUSE_ID = "CP_C_PHY_WAREHOUSE_ID";//发货仓库
    public static final String CP_C_LOGISTICS_ID = "CP_C_LOGISTICS_ID";//物流公司
    public static final String CP_C_CUSTOMER_ID = "CP_C_CUSTOMER_ID";//分销商
    public static final String ORDER_STATUS = "ORDER_STATUS";//状态
    public static final String PLATFORM = "PLATFORM";//交易平台
    public static final String ORDER_DATE = "ORDER_DATE";//下单时间
    public static final String PAY_TIME = "PAY_TIME";//付款时间
    public static final String DISTRIBUTION_TIME = "DISTRIBUTION_TIME";//配货时间
    public static final String SCAN_TIME = "SCAN_TIME";//出库时间
    public static final String ORDER_TYPE = "ORDER_TYPE";//订单类型
    public static final String PAY_TYPE = "PAY_TYPE";//支付类型
    public static final String CP_C_PHY_WAREHOUSE_ENAME = "CP_C_PHY_WAREHOUSE_ENAME";//实体仓名称
    public static final String CP_C_LOGISTICS_ENAME = "CP_C_LOGISTICS_ENAME";//物流公司名称
    public static final String CP_C_SHOP_TITLE = "CP_C_SHOP_TITLE";//下单店铺标题
    public static final String ALL_SKU = "ALL_SKU";//商品条码
    // 冗余字段,转换成对应文本
    private String orderTypeName; // 订单类型
    private String orderStatusName; // 订单状态
    private String platformName; // 平台
    private String payTypeName; // 支付方式
    private String occupyStatusName; // 订单占单状态
    private String wmsCancelStatusName; // wms撤回状态
    private String autoAuditStatusName; // 自动审核状态
    private String isGeninvoiceNoticeName; // 是否生成开票通知
    private String returnStatusName; // 退货状态
    private String isTodrpName; // 是否生成零售调拨单
    //其余字段
    private String wmsStatusName; // 仓储状态（拣货中，已打印，已装箱）
    private String outStatusName; // 出库状态
    private String refundConfirmStatusName; // 退款审核状态（AG使用）
    private String double11PresaleStatusName; // 双11预售状态
    private String sysPresaleStatusName; // 系统预售状态，非双11
    //是否
    private String isInvoiceName; // 是否开票
    private String isMergeName; // 是否合并订单
    private String isSplitName; // 是否拆分订单
    private String isIntereceptName; // 是否已经拦截
    private String isInreturningName; // 是否退款中
    private String isHasgiftName; // 是否有赠品
    private String isGiveLogisticName; // 是否已给物流
    private String isInventedName; // 是否虚拟订单
    private String isCombinationName; // 是否组合订单
    private String isOutUrgencyName; // 是否催发货
    private String isShopCommissionName; // 下单店铺是否代销
    private String isHasTicketName; // 是否有工单
    private String isWriteoffName; // 是否插入核销流水
    private String isJcorderName; // 京仓订单
    private String isLackstockName; // 实缺标记
    private String cptAddress;//组合地址
    private BigDecimal totQtyLost;//缺货数量
    // todo 平台售价 , 判断是否为特殊字符,然后转 decimal
    private String platformPrice;
    // 成交单价
    private BigDecimal priceActual;
    //错误信息
    private String desc;
    //头子表关联列
    private String BK;
    //行号
    private int rowNum;

    private BigDecimal minQtyAll;
    private BigDecimal maxQtyAll;
    private BigDecimal minOrderAmt;
    private BigDecimal maxOrderAmt;

    private List<Long> idList; //订单id集合
    private List<Integer> orderStatusList; //订单状态
    private List<Date> distributionTimeList; //配货时间区间
    private List<Date> orderDateList; //下单时间区间
    private List<Date> payTimeList; //付款时间区间
    private List<Date> scanTimeList; //出库时间区间
    private List<Integer> orderTypeList; //订单类型
    private List<Integer> payTypeList; //支付类型
    private List<Integer> platformList; //交易平台

    private List<OcBOrderItemExtend> orderItemList;

    private OcBToBeConfirmedTask ocBToBeConfirmedTask;

    private OcBOrderLog ocBOrderLog;

    private OcBOrderPayment payment;

    public OcBOrderExtend() {

    }

    public OcBOrderExtend(JSONObject jsonObject) {
        this.setIdList((List<Long>) (jsonObject.get("idList")));
        this.setSourceCode(jsonObject.getString(SOURCE_CODE));
        this.setBillNo(jsonObject.getString(BILL_NO));
        this.setAllSku(jsonObject.getString(ALL_SKU));
        this.setCpCPhyWarehouseEname(jsonObject.getString(CP_C_PHY_WAREHOUSE_ENAME));
        this.setCpCLogisticsEname(jsonObject.getString(CP_C_LOGISTICS_ENAME));
        this.setCpCShopTitle(jsonObject.getString(CP_C_SHOP_TITLE));
        this.setId(jsonObject.getLong(ID));
        this.setUserNick(jsonObject.getString(USER_NICK));
        this.setReceiverName(jsonObject.getString(RECEIVER_NAME));
        this.setReceiverMobile(jsonObject.getString(RECEIVER_MOBILE));
        this.setExpresscode(jsonObject.getString(EXPRESSCODE));
        this.setWmsCancelStatus(jsonObject.getInteger(WMS_CANCEL_STATUS));

        /**特殊字段接收*/
        JSONObject orderAmtParam = jsonObject.getJSONObject(ORDER_AMT);
        if (orderAmtParam != null) {
            this.setMinOrderAmt(orderAmtParam.getBigDecimal("value1"));
            this.setMaxOrderAmt(orderAmtParam.getBigDecimal("value2"));
        }
        /**特殊字段接收*/
        JSONObject qtyAllParam = jsonObject.getJSONObject(QTY_ALL);
        if (qtyAllParam != null) {
            this.setMinQtyAll(qtyAllParam.getBigDecimal("value1"));
            this.setMaxQtyAll(qtyAllParam.getBigDecimal("value2"));
        }
        this.setInsideRemark(jsonObject.getString(INSIDE_REMARK));
        this.setBuyerMessage(jsonObject.getString(BUYER_MESSAGE));
        this.setCpCShopId(jsonObject.getLong(CP_C_SHOP_ID));
        this.setCpCPhyWarehouseId(jsonObject.getLong(CP_C_PHY_WAREHOUSE_ID));
        this.setCpCLogisticsId(jsonObject.getLong(CP_C_LOGISTICS_ID));
        this.setCpCCustomerId(jsonObject.getLong(CP_C_CUSTOMER_ID));
        this.setOrderStatusList((List<Integer>) (jsonObject.get(ORDER_STATUS)));
        this.setPlatformList((List<Integer>) (jsonObject.get(PLATFORM)));
        this.setOrderDateList((List<Date>) (jsonObject.get(ORDER_DATE)));
        this.setPayTimeList((List<Date>) (jsonObject.get(PAY_TIME)));
        this.setDistributionTimeList((List<Date>) (jsonObject.get(DISTRIBUTION_TIME)));
        this.setScanTimeList((List<Date>) (jsonObject.get(SCAN_TIME)));
        this.setOrderTypeList((List<Integer>) (jsonObject.get(ORDER_TYPE)));
        this.setPayTypeList((List<Integer>) (jsonObject.get(PAY_TYPE)));
    }

    /**
     * 关联主表-》明细表
     */
    public static boolean dealBK(List<OcBOrderExtend> ocBOrderExtendList,
                                 List<OcBOrderItemExtend> ocBOrderItemExtendList) {
        boolean checkFlag = true;
        List<String> tids = Lists.newArrayList();
        Iterator<OcBOrderExtend> iterator = ocBOrderExtendList.iterator();
        while (iterator.hasNext()) {
            OcBOrderExtend next = iterator.next();
            if (tids.contains(next.getSourceCode())) {
                iterator.remove();
            }
            tids.add(next.getSourceCode());
        }
        for (OcBOrderExtend orderExtend : ocBOrderExtendList) {
            List<OcBOrderItemExtend> orderItemList = Lists.newArrayList();
            for (OcBOrderItemExtend itemExtend : ocBOrderItemExtendList) {
                if (orderExtend.getBK().equals(itemExtend.getBK())) {
                    orderItemList.add(itemExtend);
                }
            }
            if (CollectionUtils.isEmpty(orderItemList)) {
                orderExtend.setDesc("没有明细!");
                checkFlag = false;
            }
            orderExtend.setOrderItemList(orderItemList);
        }
        return checkFlag;
    }

    /**
     * 导入生成模型
     *
     * @return
     */
    public static OcBOrderExtend importCreate(int index, OcBOrderExtend ocBOrderExtend, Map<String, String> columnMap) {
        try {
            //下单店铺
            ocBOrderExtend.setCpCShopTitle(columnMap.get(rowStr + index + cellStr + 0));
            //配送物流
            ocBOrderExtend.setCpCLogisticsEname(columnMap.get(rowStr + index + cellStr + 1));
            //配送费用
            ocBOrderExtend.setShipAmt(new BigDecimal(columnMap.get(rowStr + index + cellStr + 2)));
            //买家昵称
            ocBOrderExtend.setUserNick(columnMap.get(rowStr + index + cellStr + 3));
            //平台单号
            ocBOrderExtend.setSourceCode(columnMap.get(rowStr + index + cellStr + 4));
            //付款方式
            ocBOrderExtend.setPayTypeName(columnMap.get(rowStr + index + cellStr + 5));
            //发货仓库
            ocBOrderExtend.setCpCPhyWarehouseEname(columnMap.get(rowStr + index + cellStr + 6));
            //是否开票
            ocBOrderExtend.setIsInvoiceName(columnMap.get(rowStr + index + cellStr + 7));
            //收货人
            ocBOrderExtend.setReceiverName(columnMap.get(rowStr + index + cellStr + 8));
            //收货人手机
            ocBOrderExtend.setReceiverMobile(columnMap.get(rowStr + index + cellStr + 9));
            //收货人电话
            ocBOrderExtend.setReceiverPhone(columnMap.get(rowStr + index + cellStr + 10));
            //收货人邮编
            ocBOrderExtend.setReceiverZip(columnMap.get(rowStr + index + cellStr + 11));
            //收货人省份
            ocBOrderExtend.setCpCRegionProvinceEname(columnMap.get(rowStr + index + cellStr + 12));
            //收货人市
            ocBOrderExtend.setCpCRegionCityEname(columnMap.get(rowStr + index + cellStr + 13));
            //收货人区
            ocBOrderExtend.setCpCRegionAreaEname(columnMap.get(rowStr + index + cellStr + 14));
            //收货人地址
            ocBOrderExtend.setReceiverAddress(columnMap.get(rowStr + index + cellStr + 15));
            //买家备注
            ocBOrderExtend.setBuyerMessage(columnMap.get(rowStr + index + cellStr + 16));
            //卖家备注
            ocBOrderExtend.setSellerMemo(columnMap.get(rowStr + index + cellStr + 17));
            //订单类型
            ocBOrderExtend.setOrderTypeName(columnMap.get(rowStr + index + cellStr + 18));
            //头子表关联列
            ocBOrderExtend.setBK(columnMap.get(rowStr + index + cellStr + 19));
        } catch (Exception e) {
            log.error("OcBOrderExtend.importCreate 构建数据出错", e);
        }
        ocBOrderExtend.setRowNum(index + 1);
        ocBOrderExtend.checkDefault(ocBOrderExtend);
        return ocBOrderExtend;
    }


    //设置默认值
    public void checkDefault(OcBOrderExtend ocBOrderExtend) {

        ocBOrderExtend.setOrderStatus(Optional.ofNullable(ocBOrderExtend.getOrderStatus()).orElse(1));

        ocBOrderExtend.setIsInvoice(Optional.ofNullable(ocBOrderExtend.getIsInvoice()).orElse(0));

        ocBOrderExtend.setIsGeninvoiceNotice(Optional.ofNullable(ocBOrderExtend.getIsGeninvoiceNotice()).orElse(0));

        ocBOrderExtend.setIsCalcweight(Optional.ofNullable(ocBOrderExtend.getIsCalcweight()).orElse(0));

        ocBOrderExtend.setIsMerge(Optional.ofNullable(ocBOrderExtend.getIsMerge()).orElse(0));

        ocBOrderExtend.setIsSplit(Optional.ofNullable(ocBOrderExtend.getIsSplit()).orElse(0));

        ocBOrderExtend.setIsInterecept(Optional.ofNullable(ocBOrderExtend.getIsInterecept()).orElse(0));

        ocBOrderExtend.setIsInreturning(Optional.ofNullable(ocBOrderExtend.getIsInreturning()).orElse(0));

        ocBOrderExtend.setIsHasgift(Optional.ofNullable(ocBOrderExtend.getIsHasgift()).orElse(0));

        // ocBOrderExtend.setIsTodrp(Optional.ofNullable(ocBOrderExtend.getIsTodrp()).orElse(0));

        // ocBOrderExtend.setIsGiveLogistic(Optional.ofNullable(ocBOrderExtend.getIsGiveLogistic()).orElse(0));

        // ocBOrderExtend.setIsHaspresalesku(Optional.ofNullable(ocBOrderExtend.getIsHaspresalesku()).orElse(0));

        ocBOrderExtend.setIsJcorder(Optional.ofNullable(ocBOrderExtend.getIsJcorder()).orElse(0));

//        ocBOrderExtend.setIsLackstock(Optional.ofNullable(ocBOrderExtend.getIsLackstock()).orElse(0));

        ocBOrderExtend.setIsCombination(Optional.ofNullable(ocBOrderExtend.getIsCombination()).orElse(0));

        ocBOrderExtend.setIsOutUrgency(Optional.ofNullable(ocBOrderExtend.getIsOutUrgency()).orElse(0));

        //ocBOrderExtend.setIsShopCommission(Optional.ofNullable(ocBOrderExtend.getIsShopCommission()).orElse(0));

        ocBOrderExtend.setIsHasTicket(Optional.ofNullable(ocBOrderExtend.getIsHasTicket()).orElse(0));

        // ocBOrderExtend.setIsWriteoff(Optional.ofNullable(ocBOrderExtend.getIsWriteoff()).orElse(0));
    }

    /**
     * 查询类生成工具
     *
     * @return
     */
    public QueryWrapper<OcBOrder> createQueryWrapper() {
        QueryWrapper<OcBOrder> queryWrapper = new QueryWrapper<OcBOrder>();
        if (CollectionUtils.isNotEmpty(this.getIdList())) {
            //订单编号List
            queryWrapper.in(ID, this.getIdList());
        }
        if (this.getSourceCode() != null) {
            // 平台单号
            queryWrapper.eq(SOURCE_CODE, this.getSourceCode());
        }
        if (this.getBillNo() != null) {
            // 单据编号
            queryWrapper.eq(BILL_NO, this.getBillNo());
        }
        if (this.getAllSku() != null) {
            // 单据编号
            queryWrapper.like(BILL_NO, this.getAllSku());
        }
        if (this.getCpCLogisticsEname() != null) {
            // 物流公司名称
            queryWrapper.eq(BILL_NO, this.getCpCLogisticsEname());
        }
        if (this.getCpCPhyWarehouseEname() != null) {
            // 仓库名称
            queryWrapper.eq(BILL_NO, this.getCpCPhyWarehouseEname());
        }
        if (this.getCpCShopTitle() != null) {
            // 下单店铺
            queryWrapper.eq(BILL_NO, this.getCpCShopTitle());
        }
        if (this.getId() != null) {
            // 订单编号
            queryWrapper.eq(ID, this.getId());
        }
        if (this.getUserNick() != null) {
            // 买家昵称
            queryWrapper.eq(USER_NICK, this.getUserNick());
        }
        if (this.getReceiverName() != null) {
            // 收货人姓名
            queryWrapper.eq(RECEIVER_NAME, this.getReceiverName());
        }
        if (this.getReceiverMobile() != null) {
            // 收货人手机号
            queryWrapper.eq(RECEIVER_MOBILE, this.getReceiverMobile());
        }
        if (this.getExpresscode() != null) {
            // 物流单号
            queryWrapper.eq(EXPRESSCODE, this.getExpresscode());
        }
        if (this.getWmsCancelStatus() != null) {
            // 撤回状态
            queryWrapper.eq(WMS_CANCEL_STATUS, this.getWmsCancelStatus());
        }
        /**
         *  订单金额
         * */
        if (this.getMinOrderAmt() != null) {
            // 大于等于这个订单金额
            queryWrapper.ge(ORDER_AMT, this.getMinOrderAmt());
        }
        if (this.getMinOrderAmt() != null) {
            // 小于等于这个订单金额
            queryWrapper.le(ORDER_AMT, this.getMinOrderAmt());
        }
        /**
         * 商品数量
         * */
        if (this.getMinQtyAll() != null) {
            // 大于等于这个商品数量
            queryWrapper.ge(QTY_ALL, this.getMinQtyAll());
        }
        if (this.getMaxQtyAll() != null) {
            // 小于等于这个商品数量
            queryWrapper.le(QTY_ALL, this.getMinQtyAll());
        }
        if (this.getInsideRemark() != null) {
            // 买家备注
            queryWrapper.eq(INSIDE_REMARK, this.getInsideRemark());
        }
        if (this.getBuyerMessage() != null) {
            // 卖家备注
            queryWrapper.eq(BUYER_MESSAGE, this.getBuyerMessage());
        }
        if (this.getCpCShopId() != null) {
            // 店铺
            queryWrapper.eq(CP_C_SHOP_ID, this.getCpCShopId());
        }
        if (this.getCpCPhyWarehouseId() != null) {
            // 发货仓库
            queryWrapper.eq(CP_C_PHY_WAREHOUSE_ID, this.getCpCPhyWarehouseId());
        }
        if (this.getCpCLogisticsId() != null) {
            // 物流公司
            queryWrapper.eq(CP_C_LOGISTICS_ID, this.getCpCLogisticsId());
        }
        if (this.getCpCCustomerId() != null) {
            // 分销商
            queryWrapper.eq(CP_C_CUSTOMER_ID, this.getCpCCustomerId());
        }
        if (CollectionUtils.isNotEmpty(this.getOrderStatusList())) {
            // 状态
            queryWrapper.in(ORDER_STATUS, this.getOrderStatusList());
        }
        if (CollectionUtils.isNotEmpty(this.getPlatformList())) {
            // 交易平台
            queryWrapper.in(PLATFORM, this.getPlatformList());
        }
        if (CollectionUtils.isNotEmpty(this.getOrderDateList())) {
            // 下单时间
            queryWrapper.between(ORDER_DATE, this.getOrderDateList().get(0), this.getOrderDateList().get(1));
        }
        if (CollectionUtils.isNotEmpty(this.getDistributionTimeList())) {
            // 配货时间
            queryWrapper.between(DISTRIBUTION_TIME, this.getDistributionTimeList().get(0),
                    this.getDistributionTimeList().get(1));
        }
        if (CollectionUtils.isNotEmpty(this.getPayTimeList())) {
            // 付款时间
            queryWrapper.between(DISTRIBUTION_TIME, this.getPayTimeList().get(0), this.getPayTimeList().get(1));
        }
        if (CollectionUtils.isNotEmpty(this.getScanTimeList())) {
            // 出库时间
            queryWrapper.between(SCAN_TIME, this.getScanTimeList().get(0), this.getScanTimeList().get(1));
        }
        if (CollectionUtils.isNotEmpty(this.getOrderTypeList())) {
            // 订单类型
            queryWrapper.in(ORDER_TYPE, this.getOrderTypeList());
        }
        if (CollectionUtils.isNotEmpty(this.getPayTypeList())) {
            // 支付类型
            queryWrapper.in(PAY_TYPE, this.getPayTypeList());
        }
        return queryWrapper;
    }
}
