package com.jackrain.nea.oc.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 入库结果单明细
 *
 * @author: xiWen.z
 * create at: 2019/5/9 0009
 */
@Data
public class SgBPhyInResultItemExt implements Serializable {

    /**
     * 条码ID
     */
    private Long psCSkuId;

    /**
     * 条码编码
     */
    private String psCSkuEcode;

    /**
     * 入库数量
     */
    private BigDecimal qtyIn;

    /**
     * 匹配状态
     */
    private int matchTag;

    /**
     * 逻辑仓id(退货单用)
     */
    private Long cpCStoreId;

    /**
     * 逻辑仓ecode(退货单用)
     */
    private String cpCStoreEcode;

    /**
     * 逻辑仓ename(退货单用)
     */
    private String cpCStoreEname;

}
