package com.jackrain.nea.oc.oms;

/**
 * @Description: 拆单原因
 * @author: 江家雷
 * @since: 2020/11/4
 * create at : 2020/11/4 10:24
 */
public class SplitReason {
    // 0 按照SKU或SPU拆出的可以合并的订单
    public static final Integer SPLIT_OTHER = 0;
    // 1.部分发货拆单
    public static final Integer SPLIT_PART_DELIVERY = 1;
    // 2.虚拟拆单
    public static final Integer SPLIT_VIRTUAL = 2;
    // 3.缺货拆单
    public static final Integer SPLIT_OUTOFSTOCK = 3;
    // 4.按SKU拆单
    public static final Integer SPLIT_BY_SKU = 4;
    // 5.按SPU拆单
    public static final Integer SPLIT_BY_SPU = 5;
    // 6.按品牌组拆
    public static final Integer SPLIT_BY_BRAND_GROUP = 6;
    // 7.按性别拆
    public static final Integer SPLIT_BY_SEX = 7;
    // 8.手工拆单
    public static final Integer SPLIT_MANUAL = 8;
    // 9.O2O拆单
    public static final Integer SPLIT_O2O = 9;

    // 10 按sku拆单剩下的单据
    public static final Integer SPLIT_10 = 10;

    // 11.按商品品类拆单
    public static final Integer SPLIT_BY_GOODS_CLASS = 11;

    //12.按商品品类拆单剩下的单据
    public static final Integer SPLIT_BY_GOODS_CLASS_1 = 12;

    //预售拆单
    public static final Integer SPLIT_BY_ADVANCE = 15;

    //卡单拆单
    public static final Integer SPLIT_BY_DETENTION = 16;

    //占单拆单
    public static final Integer SPLIT_BY_INVENTORY = 18;

    //实缺拆单
    public static final Integer SPLIT_WAREHOUSE_STOCK = 25;

    //发货异常补发拆单
    public static final Integer SPLIT_REDELIVERY = 30;


    private SplitReason() {
    }
}