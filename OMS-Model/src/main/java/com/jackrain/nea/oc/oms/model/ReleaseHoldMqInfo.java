package com.jackrain.nea.oc.oms.model;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 释放Hold单MQ消息实体类
 *
 * @author: 胡林洋
 * @since: 2019-03-30
 * create at : 2019-05-07 18:14
 */
@Data
@ToString
public class ReleaseHoldMqInfo {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单编号ֵ/平台单号
     */
    private String orderNo;

    /**
     * pmID促销活动id
     **/
    private Long pmID;

    /**
     * 促销活动分组
     **/
    private String groupNo;

    /**
     * 返回促销执行的类型【1有赠品,2无赠品】
     */
    private String type;

    /**
     * pmName促销活动名称
     **/
    private String pmName;

    /**
     * pmCode促销活动编码
     */
    private String pmCode;

    /**
     * giftList
     */
    private List<GiftInfo> giftList;

}
