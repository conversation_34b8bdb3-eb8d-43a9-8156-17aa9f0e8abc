package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * @author: ming.fz
 * @since: 2019-07-4
 */
@Data
public class IpStandplatOrderRelation {

    private IpBStandplatOrder standplatOrder;

    private List<IpBStandplatOrderItemEx> standPlatOrderItemList;


    public Long getOrderId() {
        if (standplatOrder != null) {
            return standplatOrder.getId();
        }
        return -1L;
    }

    public String getOrderNo() {
        if (standplatOrder != null) {
            return standplatOrder.getTid();
        }
        return "";
    }

    /**
     * 获取当前的平台
     *
     * @return
     */
    public Long getPlatformId() {
        if (Objects.nonNull(standplatOrder)) {
            return standplatOrder.getCpCPlatformId();
        }

        return null;
    }

    /**
     * 平台是否是：速卖通
     *
     * @return
     */
    public boolean isAliExpress() {
        // 101 - 速卖通101 因为是hardcode，所以尽量统一方法提供
        return checkPlatform(PlatFormEnum.ALIEXPRESS);
    }

    public boolean isPos(){
        return checkPlatform(PlatFormEnum.POS);
    }

    private boolean checkPlatform(PlatFormEnum platFormEnum){
        Long platformId = getPlatformId();
        platformId = Objects.isNull(platformId) ? -1L : platformId;

        return platformId.intValue() == platFormEnum.getCode();
    }

}
