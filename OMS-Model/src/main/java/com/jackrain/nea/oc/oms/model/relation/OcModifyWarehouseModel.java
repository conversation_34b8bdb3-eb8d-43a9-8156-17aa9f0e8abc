package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/10/5
 */
@Data
@Accessors(chain = true)
public class OcModifyWarehouseModel implements Serializable {
    private static final long serialVersionUID = -7859393425326617555L;

    private UserImpl user;

    private String tag;

    private String topic;

    private Long orderId;

    private Long warehouseId;

    private String warehouseCode;

    private String warehouseName;

    private String modifyReason;

    private String warehouseWmsType;


}
