package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderActual;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Desc : 退货入库: 匹配,入库参数
 * <AUTHOR> xiWen
 * @Date : 2023/2/10
 */
@Data
public class OmsStockInMatchParam {

    /**
     * 入库结果单.更新
     */
    private OcBRefundIn modRefundIn;

    /**
     * 入库结果明细.更新
     */
    private List<OcBRefundInProductItem> modRefundItems;

    /**
     * 退单.更新
     */
    private OcBReturnOrder modReturn;

    /**
     * 退单明细.更新
     */
    private List<OcBReturnOrderRefund> modReturnItems;

    /**
     * 退单实际入库明细.新增
     */
    private List<OcBReturnOrderActual> insActualItems;

    /**
     * 入库结果单匹配,入库日志.新增
     */
    private List<OcBRefundInLog> insRefundLogs;

    /**
     * 退单入库日志.新增
     */
    private List<OcBReturnOrderLog> insReturnLogs;

    /**
     * 退单.原始退单,当前匹配单据/ 多次入库=新单据
     */
    private OcBReturnOrder matchedReturn;

    /**
     * 退单明细.当前匹配明细 / 多次入库为新单据明细
     */
    private List<OcBReturnOrderRefund> matchedReturnItems;

    /**
     * 操作人
     */
    private User user;

    /**
     * 初始容器
     */ {
        this.modRefundItems = new ArrayList<>();
        this.modReturnItems = new ArrayList<>();
        this.insRefundLogs = new ArrayList<>();
        this.insReturnLogs = new ArrayList<>();
    }

    public static OmsStockInMatchParam build(User user) {
        OmsStockInMatchParam param = new OmsStockInMatchParam();
        param.setUser(user);
        return param;
    }

    public OmsStockInMatchParam modifyParam(OcBRefundIn uRefund, List<OcBRefundInProductItem> uRefundItems,
                                            OcBReturnOrder uReturn, List<OcBReturnOrderRefund> uReturnItems) {
        this.setModRefundIn(uRefund);
        this.setModRefundItems(uRefundItems);
        this.setModReturn(uReturn);
        this.setModReturnItems(uReturnItems);
        return this;
    }

    public OmsStockInMatchParam addParam(List<OcBReturnOrderActual> adActualItems,
                                         List<OcBRefundInLog> adRefundInLogs, List<OcBReturnOrderLog> adReturnLog) {
        this.setInsActualItems(adActualItems);
        this.setInsRefundLogs(adRefundInLogs);
        this.setInsReturnLogs(adReturnLog);
        return this;
    }

    public OmsStockInMatchParam stockInParam(OcBReturnOrder matchedReturn, List<OcBReturnOrderRefund> matchedItems) {
        this.setMatchedReturn(matchedReturn);
        this.setMatchedReturnItems(matchedItems);
        return this;
    }

    /**
     * modify
     *
     * @param refundInProductItem OcBRefundInProductItem
     */
    public void addModRefundItem(OcBRefundInProductItem refundInProductItem) {
        this.modRefundItems.add(refundInProductItem);
    }

    /**
     * modify
     *
     * @param returnOrderRefund OcBReturnOrderRefund
     */
    public void addModReturnItem(OcBReturnOrderRefund returnOrderRefund) {
        this.modReturnItems.add(returnOrderRefund);
    }

    /**
     * insert refund log
     *
     * @param refundInLog OcBRefundInLog
     */
    public OmsStockInMatchParam addInsRefundLog(OcBRefundInLog refundInLog) {
        this.insRefundLogs.add(refundInLog);
        return this;
    }

    /**
     * insert return log
     *
     * @param returnOrderLog OcBReturnOrderLog
     */
    public OmsStockInMatchParam addInsReturnLog(OcBReturnOrderLog returnOrderLog) {
        this.insReturnLogs.add(returnOrderLog);
        return this;
    }

}
