package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @author: 夏继超
 * @create: 2019-05-13 10:30
 **/
@Data
public class OcBReturnAfSendItemExtend extends OcBReturnAfSendItem implements Serializable {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";


    //错误信息
    private String desc;
    //头子表关联列
    private String BK;
    //行号
    private int rowNum;


    private List<OcBOrderItemExtend> orderItemList;

    public OcBReturnAfSendItemExtend() {

    }


    /**
     * 导入生成模型
     *
     * @return
     */
    public static OcBReturnAfSendItemExtend importCreate(int index, OcBReturnAfSendItemExtend afSendItemExtend, Map<String, String> columnMap) {
        try {
            //SKU编码
            afSendItemExtend.setPsCSkuEcode(columnMap.get(rowStr + index + cellStr + 0));
        } catch (Exception e) {

        }
        try {
            //退款金额
            afSendItemExtend.setAmtReturn(new BigDecimal(columnMap.get(rowStr + index + cellStr + 1)));
        } catch (Exception e) {

        }
        try {
            //运费
            afSendItemExtend.setFreight(new BigDecimal(columnMap.get(rowStr + index + cellStr + 2)));
        } catch (Exception e) {

        }


        try {
            //头子表关联列
            afSendItemExtend.setBK(columnMap.get(rowStr + index + cellStr + 3));
        } catch (Exception e) {

        }
        afSendItemExtend.setRowNum(index + 1);

        return afSendItemExtend;
    }
}
