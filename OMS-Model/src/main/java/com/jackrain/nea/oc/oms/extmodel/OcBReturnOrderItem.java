package com.jackrain.nea.oc.oms.extmodel;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 14:38
 **/
@Data
public class OcBReturnOrderItem implements Serializable {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    //条码
    private String psCSkuEcode;
    //数量
    private BigDecimal qty;
    //行号
    private int rowNum;
    //头子表关联列
    private String BK;

    //退单编号
    private Long ocBReturnOrderId;
    //国标码
    private String barcode;
    //商品编码
    private String psCProEcode;
    //商品名称
    private String psCProEname;
    //可退数量
    private BigDecimal qtyCan;
    //申请数量
    private BigDecimal qtyRefund;
    //吊牌价
    private BigDecimal price;
    //单件退货金额
    private BigDecimal amtRefundSingle;
    //退货金额
    private BigDecimal allAmtRefund;
    //入库数量
    private BigDecimal qtyIn;
    //商品标记
    private String productMark;


    /**
     * 导入生成模型
     *
     * @return
     */
    public static OcBReturnOrderItem importCreate(int index, OcBReturnOrderItem ocBReturnOrderItem, Map<String, String> columnMap) {
        try {
            ocBReturnOrderItem.setPsCSkuEcode(columnMap.get(rowStr + index + cellStr + 0));
        } catch (Exception e) {

        }
        try {
            ocBReturnOrderItem.setQty(new BigDecimal(columnMap.get(rowStr + index + cellStr + 1)));
        } catch (Exception e) {

        }
        try {
            ocBReturnOrderItem.setBK(columnMap.get(rowStr + index + cellStr + 2));
        } catch (Exception e) {

        }
        ocBReturnOrderItem.setRowNum(index + 1);
        return ocBReturnOrderItem;
    }
}
