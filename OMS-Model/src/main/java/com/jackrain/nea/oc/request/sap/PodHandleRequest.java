package com.jackrain.nea.oc.request.sap;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class PodHandleRequest {

    @ApiModelProperty(name = "单据编号")
    @JSONField(name = "LIFEX")
    private String billNo;

    @ApiModelProperty(name = "签收日期")
    @JSONField(name = "ZQS_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    @ApiModelProperty(name = "物料编码")
    @JSONField(name = "MATNR")
    private String sku;

    @ApiModelProperty(name = "签收数量")
    @JSONField(name = "ZQS_MENGE")
    private BigDecimal qty;

    @ApiModelProperty(name = "签收状态")
    @JSONField(name = "PDSTK")
    private String status;
}
