package com.jackrain.nea.oc.oms.model.jingdong;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-25
 * create at : 2019-04-25 1:28 PM
 * 京东订单状态
 */
public interface JingdongOrderStatus {
    /*
     * 1）WAIT_SELLER_STOCK_OUT 等待出库
     * 2）WAIT_GOODS_RECEIVE_CONFIRM 等待确认收货
     * 3）WAIT_SELLER_DELIVERY等待发货（只适用于海外购商家，含义为“等待境内发货”标签下的订单,非海外购商家无需使用）
     * 4) POP_ORDER_PAUSE POP暂停
     * 5）FINISHED_L 完成
     * 6）TRADE_CANCELED 取消
     * 7）LOCKED 已锁定
     * 8）WAIT_SEND_CODE 等待发码（LOC订单特有状态）
     */

    String WAIT_SELLER_STOCK_OUT = "WAIT_SELLER_STOCK_OUT";

    String WAIT_GOODS_RECEIVE_CONFIRM = "WAIT_GOODS_RECEIVE_CONFIRM";

    String WAIT_SELLER_DELIVERY = "WAIT_SELLER_DELIVERY";

    String POP_ORDER_PAUSE = "POP_ORDER_PAUSE";

    String FINISHED_L = "FINISHED_L";

    String TRADE_CANCELED = "TRADE_CANCELED";

    String WAIT_SEND_CODE = "WAIT_SEND_CODE";

    String LOCKED = "LOCKED";


}
