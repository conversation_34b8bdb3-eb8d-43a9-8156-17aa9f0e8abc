package com.jackrain.nea.oc.request.o2o;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * o2o 拆单
 *
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/13
 */
@Data
public class SplitOrderRequest implements Serializable {

    private static final long serialVersionUID = 7892829563753149634L;

    /**
     * 出库通知单单号
     */
    @JSONField(name = "OrderId")
    private String orderId;

    /**
     * 路由参数
     */
    @JSONField(name = "customerid")
    private String customerId;

    /**
     * 明细
     */
    @JSONField(name = "SplitOrders")
    private List<SplitOrderItem> splitOrders;


    @Data
    public static class SplitOrderItem implements Serializable {

        private static final long serialVersionUID = -4339598432781437478L;

        /**
         * 拆分组号
         */
        @JSONField(name = "SplitGroup")
        private String splitGroup;

        /**
         * 订单商品明细ID
         */
        @JSONField(name = "subOrderID")
        private String subOrderId;

        /**
         * 商品款号
         */
        @JSONField(name = "goodsCode")
        private String goodsCode;

        /**
         * 颜色代码
         */
        @JSONField(name = "colorCode")
        private String colorCode;

        /**
         * 尺码代码
         */
        @JSONField(name = "sizeCode")
        private String sizeCode;

        /**
         * 商品数量
         */
        @JSONField(name = "Num")
        private BigDecimal num;

    }
}
