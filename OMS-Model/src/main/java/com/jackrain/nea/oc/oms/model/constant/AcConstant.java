package com.jackrain.nea.oc.oms.model.constant;

public class AcConstant {
    /**
     * 表名
     */
    /**
     * 否
     */
    public static final String IS_NO = "0";
    /**
     * 是
     */
    public static final String IS_YES = "1";
    /**
     * 唯品会客退结算编码
     */
    public static final String TAB_AC_F_VIP_CUS_SETTLECODE = "AC_F_VIP_CUS_SETTLECODE";
    /**
     * 小平台原始账单
     */
    public static final String TAB_AC_F_PLATFORM_ORIG_BILL = "AC_F_PLATFORM_ORIG_BILL";
    /**
     * 唯品会进度聚合表
     */
    public static final String TAB_AC_F_VIP_BILL_PROGRESS_AGGREGATE = "AC_F_VIP_BILL_PROGRESS_AGGREGATE";
    /**
     * 唯品会月度聚合表
     */
    public static final String TAB_AC_F_VIP_BILL_MONTH_AGGREGATE = "AC_F_VIP_BILL_MONTH_AGGREGATE";

    /**
     * redis key
     */
    public static final String VIP_BILL_MONTH_KEY_PREFIX = "ac:vip_bill_month:";

    public static final String VIP_BILL_PROGRESS_KEY_PREFIX = "ac:vip_bill_progress:";

    //应付款调整单
    //主表
    public static final String TAB_AC_F_PAYABLE_ADJUSTMENT = "AC_F_PAYABLE_ADJUSTMENT";

    //明细
    public static final String TAB_AC_F_PAYABLE_ADJUSTMENT_ITEM = "AC_F_PAYABLE_ADJUSTMENT_ITEM";

    //日志
    public static final String TAB_AC_F_PAYABLE_ADJUSTMENT_LOG = "AC_F_PAYABLE_ADJUSTMENT_LOG";


    public static final String INDEX_AC_F_PAYABLE_ADJUSTMENT = "ac_f_payable_adjustment";
    public static final String TYPE_AC_F_PAYABLE_ADJUSTMENT = "ac_f_payable_adjustment";
    public static final String TYPE_AC_F_PAYABLE_ADJUSTMENT_ITEM = "ac_f_payable_adjustment_item";
    public static final String TYPE_AC_F_PAYABLE_ADJUSTMENT_LOG = "ac_f_payable_adjustment_log";
    //应收款调整单
    //主表
    public static final String TAB_AC_F_RECEIVABLES_ADJUSTMENT = "AC_F_RECEIVABLES_ADJUSTMENT";
    //明细
    public static final String TAB_AC_F_RECEIVABLES_ADJUSTMENT_ITEM = "AC_F_RECEIVABLES_ADJUSTMENT_ITEM";
    //日志
    public static final String TAB_AC_F_RECEIVABLES_ADJUSTMENT_LOG = "AC_F_RECEIVABLES_ADJUSTMENT_LOG";

    //平台
    //淘宝
    public static final int ENUMDATA_PLATFORM_TB = 2;
    //京东
    public static final int ENUMDATA_PLATFORM_JD = 4;
    //淘宝经销
    public static final int ENUMDATA_PLATFORM_TBJX = 77;
    //淘宝分销
    public static final int ENUMDATA_PLATFORM_TBFX = 3;

    //单据状态
    //未审核
    public static final int CON_BILL_STATUS_01 = 1;
    //已客审
    public static final int CON_BILL_STATUS_02 = 2;
    //已业审
    public static final int CON_BILL_STATUS_03 = 3;
    //已财审
    public static final int CON_BILL_STATUS_04 = 4;
    //已作废
    public static final int CON_BILL_STATUS_05 = 5;

    //核销状态
    //未核销
    public static final int WRITE_OFF_STATUS_01 = 1;
    //核销中
    public static final int WRITE_OFF_STATUS_02 = 2;
    //已核销
    public static final int WRITE_OFF_STATUS_03 = 3;
    //部分核销
    public static final int WRITE_OFF_STATUS_04 = 4;


    //单据类型
    //丢单赔付-补发
    public static final int CON_PAY_BILL_TYPE_01 = 1;
    //仓储赔付
    public static final int CON_PAY_BILL_TYPE_02 = 2;
    //其他
    public static final int CON_PAY_BILL_TYPE_03 = 3;
    //丢单赔付-仅退款
    public static final int CON_PAY_BILL_TYPE_04 = 4;
    //wos截件/拒收
    public static final int CON_PAY_BILL_TYPE_05 = 5;

    //快递赔付策略，赔付类型 COMPENSATE_TYPE
    //价格赔付
    public static final int CON_COMPENSATE_TYPE_01 = 1;
    //邮费赔付
    public static final int CON_COMPENSATE_TYPE_02 = 2;
    //固定结算
    public static final int CON_COMPENSATE_TYPE_03 = 3;

    //支付方式
    //支付宝
    public static final int CON_PAY_TYPE_ZFB = 1;
    //微信
    public static final int CON_PAY_TYPE_WX = 2;
    //现金
    public static final int CON_PAY_TYPE_CASH = 3;
    //备用金
    public static final int CON_PAY_TYPE_PETTY_CASH = 4;
    //财付通
    public static final int CON_PAY_TYPE_CHOI_TENPAY = 5;
    //银行
    public static final int CON_PAY_TYPE_BANK = 6;

    //调整类型
    //线上
    public static final int CON_ADJUST_TYPE_ONLINE = 1;
    //线下
    public static final int CON_ADJUST_TYPE_OFFLINE = 2;

    //单据类型
    //退款
    public static final int CON_REFUND_BILL_TYPE_GOODSORDRAWBACK = 1;
    //其他
    public static final int CON_REFUND_BILL_TYPE_OTHER = 2;

    //是否退款
    //否
    public static final int CON_IS_REFUND_NO = 0;
    //是
    public static final int CON_IS_REFUND_YES = 1;

    //条件表达式
    //包含
    public static final int CON_CONDITION_EXPRESS_CONTAIN = 1;
    //不包含
    public static final int CON_CONDITION_EXPRESS_NO_CONTAIN = 2;
    //开头是
    public static final int CON_CONDITION_EXPRESS_BEGINS_WITH = 3;
    //结尾是
    public static final int CON_CONDITION_EXPRESS_END_WITH = 4;
    //等于
    public static final int CON_CONDITION_EXPRESS_EQUAL = 5;
    //不等于
    public static final int CON_CONDITION_EXPRESS_NO_EQUAL = 6;
    //大于
    public static final int CON_CONDITION_EXPRESS_GREATER = 7;
    //小于
    public static final int CON_CONDITION_EXPRESS_LESS = 8;
    //大于等于
    public static final int CON_CONDITION_EXPRESS_GREATER_AND_EQUAL = 9;
    //小于等于
    public static final int CON_CONDITION_EXPRESS_LESS_AND_EQUAL = 10;

    //条件字段
    //店铺名称
    public static final int CON_FILTERCOL_SHOPTITLE = 1;
    //支付宝订单号
    public static final int CON_FILTERCOL_ALIPAY_ORDER_NUMBER = 2;
    //商户订单号
    public static final int CON_FILTERCOL_MERCHANT_ORDER_NUMBER = 3;
    //账务类型
    public static final int CON_FILTERCOL_ACCOUNTING_TYPE = 4;
    //子业务类型
    public static final int CON_FILTERCOL_SUBBUSINESS_TYPE = 5;
    //账号备注
    public static final int CON_FILTERCOL_ACCOUNT_NOTES = 6;
    //解析备注
    public static final int CON_FILTERCOL_ANALYTICAL_NOTES = 7;
    //解析商户订单号
    public static final int CON_FILTERCOL_ANALYSIS_OF_MERCHANT_ORDER_NUMBER = 8;
    //解析账务类型
    public static final int CON_FILTERCOL_SANALYSIS_OF_ACCOUNTING_TYPES = 9;
    //解析子业务类型
    public static final int CON_FILTERCOL_RESOLVING_SUB_BUSINESS_TYPES = 10;
    //订单编号
    public static final int CON_FILTERCOL_ORDERID = 11;
    //单据编号
    public static final int CON_FILTERCOL_ADOCUMENT_NUMBER = 12;
    //单据类型
    public static final int CON_FILTERCOL_DOCUMENT_TYPE = 13;
    //费用项
    public static final int CON_FILTERCOL_FEE_ITEM = 14;
    //钱包结算备注
    public static final int CON_FILTERCOL_NOTES_ON_WALLET_SETTLEMENT = 15;
    //店铺号
    public static final int CON_FILTERCOL_SHOP_NUMBER = 16;
    //备注
    public static final int CON_FILTERCOL_REMARK = 17;
    //店铺ID
    public static final int CON_FILTERCOL_SHOPID = 18;
    //商品编号
    public static final int CON_FILTERCOL_SKU = 19;

    //是否静态
    //静态
    public static final int CON_ISSTATIC = 1;
    //非静态
    public static final int CON_NO_STATIC = 0;

    //平台
    //淘宝
    public static final int CON_PLATFORM_TB = 2;

    //核销流水-订单来源
    //支付宝流水
    public static final int AC_BILL_SOURCE_1 = 1;
    //全渠道订单
    public static final int AC_BILL_SOURCE_2 = 2;
    //应收款调整单
    public static final int AC_BILL_SOURCE_3 = 3;
    //退换货订单
    public static final int AC_BILL_SOURCE_4 = 4;

    //核销流水-核销状态
    //未核销
    public static final int AC_TBXPAYSTREAM_TYPE_0 = 0;
    //已核销
    public static final int AC_TBXPAYSTREAM_TYPE_1 = 1;
    //核销失败
    public static final int AC_TBXPAYSTREAM_TYPE_2 = 2;

    //核销流水-核销类型
    //字段选项组：实收、实付、 应收、应付、线上调整、线下调整
    //实收
    public static final int AC_WRITEOFF_TYPE_1 = 1;
    //实付
    public static final int AC_WRITEOFF_TYPE_2 = 2;
    //应收
    public static final int AC_WRITEOFF_TYPE_3 = 3;
    //应付
    public static final int AC_WRITEOFF_TYPE_4 = 4;
    //线上调整
    public static final int AC_WRITEOFF_TYPE_5 = 5;
    //线下调整
    public static final int AC_WRITEOFF_TYPE_6 = 6;

    //核销流水 单据类型
    //退货退款
    public static final String AC_BILL_TYPE_1 = "退货退款";
    //其他
    public static final String AC_BILL_TYPE_2 = "其他";

    //财务中心日志表 日志类型
    //手动核销日志
    public static final int AC_LOG_TYPE_1 = 1;
    //手动批量核销日志
    public static final int AC_LOG_TYPE_2 = 2;
    //财务核销流水日志
    public static final int AC_LOG_TYPE_3 = 3;
    //数据解析日志
    public static final int AC_LOG_TYPE_4 = 4;
    //订单核销日志
    public static final int AC_LOG_TYPE_5 = 5;
    //支付宝API数据下载
    public static final int AC_LOG_TYPE_6 = 6;
    //自动核销日志
    public static final int AC_LOG_TYPE_7 = 7;

    //是否插入核销流水
    //否
    public static final int AC_IS_WRITEOFF_0 = 0;
    //是
    public static final int AC_IS_WRITEOFF_1 = 1;

    //全渠道订单-订单类型
    //正常
    public static final long AC_OC_B_ORDER_TYPE_1 = 1L;
    //换货
    public static final long AC_OC_B_ORDER_TYPE_2 = 2L;
    //补发
    public static final long AC_OC_B_ORDER_TYPE_3 = 3L;
    //赠品
    public static final long AC_OC_B_ORDER_TYPE_4 = 4L;
    //积分
    public static final long AC_OC_B_ORDER_TYPE_5 = 5L;
    //丢单
    public static final long AC_OC_B_ORDER_TYPE_6 = 6L;
    //批发有装箱
    public static final long AC_OC_B_ORDER_TYPE_600 = 600L;
    //批发无装箱
    public static final long AC_OC_B_ORDER_TYPE_601 = 601L;

    //全渠道订单-订单状态
    //未确认
    public static final long AC_OC_B_ORDER_STATUS_1 = 1L;
    //缺货
    public static final long AC_OC_B_ORDER_STATUS_2 = 2L;
    //已审核
    public static final long AC_OC_B_ORDER_STATUS_3 = 3L;
    //配货中
    public static final long AC_OC_B_ORDER_STATUS_4 = 4L;
    //仓库发货
    public static final long AC_OC_B_ORDER_STATUS_5 = 5L;
    //平台发货
    public static final long AC_OC_B_ORDER_STATUS_6 = 6L;
    //已取消
    public static final long AC_OC_B_ORDER_STATUS_7 = 7L;
    //系统作废
    public static final long AC_OC_B_ORDER_STATUS_8 = 8L;
    //预售
    public static final long AC_OC_B_ORDER_STATUS_9 = 9L;
    //代发
    public static final long AC_OC_B_ORDER_STATUS_10 = 10L;
    //物流已送达
    public static final long AC_OC_B_ORDER_STATUS_11 = 11L;
    //交易完成
    public static final long AC_OC_B_ORDER_STATUS_12 = 12L;
    //未付款
    public static final long AC_OC_B_ORDER_STATUS_13 = 13L;
    //待传wms
    public static final long AC_OC_B_ORDER_STATUS_21 = 21L;
    //待分配
    public static final long AC_OC_B_ORDER_STATUS_50 = 50L;

    //退换货单-单据类型
    //退货
    public static final long AC_OC_B_BILL_TYPE_1 = 1L;
    //退换货
    public static final long AC_OC_B_BILL_TYPE_2 = 2L;

    //退换货单-订单状态
    //等待退货入库
    public static final long AC_OC_B_REFUNDORDER_STATUS_20 = 20L;
    //等待售后确认
    public static final long AC_OC_B_REFUNDORDER_STATUS_30 = 30L;
    //完成
    public static final long AC_OC_B_REFUNDORDER_STATUS_50 = 50L;
    //取消
    public static final long AC_OC_B_REFUNDORDER_STATUS_60 = 60L;

    //单元格前缀
    public static final String PREFIX_CELL = "cell_";

    //行前缀
    public static final String PREFIX_ROW = "row_";

    //单元格数字类型
    public static final int FORMAT_NUMERIC = 0;

    //单元格字符串类型
    public static final int FORMAT_STRING = 1;

    //单元格公式类型
    public static final int FORMAT_FORMULA = 2;

    //虚拟入库状态
    public static final int INVENTED_STATUS_1 = 1;

    //系统参数：丢件结算店仓
    public static final String AC_DROP_SETTLE_STORE = "AC.DropSettleStore";

    //应付款调整单来源单据类型
    //零售发货单
    public static final String PAYABLE_SOURCE_TYPE_1 = "1";
    //零售退货单
    public static final String PAYABLE_SOURCE_TYPE_2 = "2";

    //取消丢件单-打日志标识
    public static final String RETURN_VOID_TYPE = "RETURNVOID";

    /**
     * 可用
     */
    public static final String IS_ACTIVE_Y = "Y";
    public static final String IS_ACTIVE_N = "N";
    /**
     * 发货
     */
    public static final String BILL_TYPE_SHIP = "0";
    /**
     * 退货
     */
    public static final String BILL_TYPE_RETURN = "1";
    /**
     * 仅退款
     */
    public static final String BILL_TYPE_ONLY_RETURN = "2";
    /**
     * 退货退款
     */
    public static final String BILL_TYPE_REFUNDS = "3";
    /**
     * 运费
     */
    public static final String BILL_TYPE_FREIGHT = "4";
    /**
     * 账单
     */
    public static final String BILL_TYPE_BILL = "5";
    /**
     * SAP通知失败最大次数
     */
    public static final Integer MAX_SAP_FAILURE_TIMES = 5;

    /**
     * SAP通知失败状态
     */
    public static final String SAP_FAILURE_STATUS = "3";

    /**
     * SAP通知成功状态
     */
    public static final String SAP_SUCCESS_STATUS = "2";

    /**
     * SAP未通知
     */
    public static final String SAP_NOT_NOTIFY_STATUS = "0";

    /**
     * redis锁以对账单平台交易号创建
     */
    public static final String BILL_SOURCE_CODE_LOCK_KEY = "ac:core:bill:sourceCode:";

    /**
     * 合并订单
     */
    public static final Integer IS_MERGE = 1;

    /**
     * 是否运费
     */
    public static final Integer IS_FREIGHT = 1;

    /**
     * 账单结构：横向
     */
    public static final Integer BILL_STRUCTURE_TRANSVERSE = 1;

    /**
     * 业务字段
     */
    public static final Integer BUSINESS_COLUMN = 1;

    /**
     * 对账来源订单表
     */
    public final static String AC_F_RECONCILIATION_ORDER = "ac_f_reconciliation_order";

    /**
     * 对账来源订单明细表
     */
    public final static String AC_F_RECONCILIATION_ORDER_ITEM = "ac_f_reconciliation_order_item";

    /**
     * 对账来源退单表
     */
    public final static String AC_F_RECONCILIATION_REFUND = "ac_f_reconciliation_refund";

    /**
     * 对账来源退单明细表
     */
    public final static String AC_F_RECONCILIATION_REFUND_ITEM = "ac_f_reconciliation_refund_item";

    /**
     * 退货回传中间表
     */
    public final static String OC_B_REFUND_IN_TASK = "oc_b_refund_in_task";

}
