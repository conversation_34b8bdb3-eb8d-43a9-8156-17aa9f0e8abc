package com.jackrain.nea.oc.oms.model.constant;

/**
 * <AUTHOR>
 * <p>
 * r3框架的 参数常量
 * <p>
 * param={"objid":-1,"fixcolumn":{"AAA":{},"AAA_ITEM":[]},"table":"AAA"}
 * @create 2021-06-22 21:15
 */
public class R3ParamConstants {

    public final static String OBJID = "objid";

    public final static String TABLENAME = "tablename";

    public final static String TABLE = "table";

    public final static String FIXCOLUMN = "fixcolumn";

    public final static String PARAM = "param";

    public final static String IDS = "ids";

    public final static String CODE = "code";

    public final static String MESSAGE = "message";

    public final static String DATA = "data";

    /**
     * 删除明细时不传fixcolumn 传 tabitem，踩过的坑
     */
    public final static String TABITEM = "tabitem";

    public final static String OBJIDS = "objids";

    public final static String SAVE = "save";
}
