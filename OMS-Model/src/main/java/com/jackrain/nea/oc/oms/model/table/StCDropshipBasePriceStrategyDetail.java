package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 一件代发客户基价策略明细
 *
 * <AUTHOR>
 */
@Data
@TableName("st_c_dropship_base_price_strategy_detail")
public class StCDropshipBasePriceStrategyDetail extends BaseModel {

    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 策略id（关联主表）
     */
    @JSONField(name = "STRATEGY_ID")
    private Long strategyId;

    /**
     * SKU编码
     */
    @JSONField(name = "SKU_CODE")
    private String skuCode;

    /**
     * 录单基价
     */
    @JSONField(name = "BASE_PRICE")
    private BigDecimal basePrice;

    /**
     * 导入内容
     */
    @JSONField(name = "IMPORT_CONTENT")
    private String importContent;

}
