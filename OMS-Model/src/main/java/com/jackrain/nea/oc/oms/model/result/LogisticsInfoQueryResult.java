package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/6/16
 * description :
 */
@Data
public class LogisticsInfoQueryResult implements Serializable {
    private String sourceCode;
    private String paytime;
    private String receiverName;
    private String receiverMobile;
    private String receiverAddress;
    private Long logisticsId;
    private String logisticsName;
    private String logisticsCode;
    private String expresscode;
    private LogisticsInfoResult logisticsResult;
}
