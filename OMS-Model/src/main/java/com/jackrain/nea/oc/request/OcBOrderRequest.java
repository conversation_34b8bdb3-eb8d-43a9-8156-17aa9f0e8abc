package com.jackrain.nea.oc.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @create 2020-07-09
 * @desc 订单查询
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class OcBOrderRequest extends BasePageRequest {
    private static final long serialVersionUID = -461644854987286546L;
    /**
     * 订单编号-id
     */
    private String orderId;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 平台单号-source_code
     */
    private String sourceCode;
    /**
     * 合单平台单号-merge_source_code
     */
    private String mergeSourceCode;
    /**
     * 快递单号
     */
    private String expresscode;
    /**
     * 用户昵称-user_nick
     */
    private String userNick;
    /**
     * 下单店铺-cp_c_shop_title
     */
    private String storeName;
    /**
     * 发货实体仓名称-cp_c_phy_warehouse_ename
     */
    private String depotName;
    /**
     * 物流公司名称-cp_c_logistics_ename
     */
    private String shipName;
    /**
     * 是否查询订单明细
     */
    private boolean needItem;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 是否拆分订单 1 是 0 否
     */
    private Integer isSplit;

    /**
     * 是否换货未入库 1:是,0:否
     */
    private Long reserveBigint03;

    /**
     * 是否为复制订单：0否，1是
     */
    private Integer isCopyOrder;

    /**
     * 订单类型
     */
    private Integer orderType;

}
