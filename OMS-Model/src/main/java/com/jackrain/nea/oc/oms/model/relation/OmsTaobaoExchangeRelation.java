package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.enums.OccupancyStatusEnum;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.ps.model.ProductSku;
import lombok.Data;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/11/30 3:46 下午
 * @Version 1.0
 *  淘宝换货构建对象
 */

@Data
public class OmsTaobaoExchangeRelation {

    /**
     * 换货中间表数据
     */
    private IpBTaobaoExchange ipBTaobaoExchange;

    /**
     * 对应的换货的原始订单(多个个)
     */
    private List<OmsOrderExchangeRelation> originalSingleOrder;


    /**
     * 对应的换货生成的订单(多个)
     */
    private List<OmsOrderExchangeRelation> exchangeOrder;

    /**
     * 换货对应的sku信息
     */
    private ProductSku productSku;

    /**
     * 对应退换单的主表信息
     */
    private OcBReturnOrder ocBReturnOrder;
    /**
     * 对应的退换货单的退货明细
     */
    private List<OcBReturnOrderRefund> ocBReturnOrderRefunds;

    /**
     * 对应的退换货单的换货明细
     */
    private List<OcBReturnOrderExchange> ocBReturnOrderExchanges;






    public boolean isExecuteOccupancy(){
        if (ipBTaobaoExchange != null) {
            Integer occupancyStatus = ipBTaobaoExchange.getOccupancyStatus();
            return OccupancyStatusEnum.SUCCESS.getVal().equals(occupancyStatus);
        }
        return false;
    }

    /**
     * @return
     */
    public long getOrderId() {
        if (ipBTaobaoExchange != null) {
            return ipBTaobaoExchange.getId();
        }
        return -1;
    }

    /**
     * @return
     */
    public String getOrderNo() {
        if (ipBTaobaoExchange != null) {
            return ipBTaobaoExchange.getDisputeId() + "";
        }
        return "";
    }


}
