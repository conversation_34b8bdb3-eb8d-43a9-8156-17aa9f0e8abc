package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 李龙飞
 * @create: 2019-05-13 10:30
 **/
@Data
@Slf4j
public class OcBOrderBeefOrderExtend implements Serializable {

    private OcBOrder order;
    private List<OcBOrderItem> orderItemList;

    private OcBToBeConfirmedTask ocBToBeConfirmedTask;

    private OcBOrderLog ocBOrderLog;

    public OcBOrderBeefOrderExtend() {

    }
}
