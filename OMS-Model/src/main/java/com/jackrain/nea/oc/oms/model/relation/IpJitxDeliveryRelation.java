package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBJitxDelivery;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryItemEx;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chnexiulou
 * @since : 2019-06-25
 * create at : 2019-06-25 6:00 PM
 */
@Data
public class IpJitxDeliveryRelation {
    private String remarks;

    /**
     * 寻源排除实体仓编码
     */
    private String excludeWarehouseCode;
    /**
     * 寻源回执实体仓Id
     */
    private Long cpCWarehouseId;

    private IpBJitxDelivery jitxDelivery;

    private List<IpBJitxDeliveryItemEx> jitxDeliveryItemList;

    /**
     * 时效订单数据
     */
    private List<IpVipTimeOrderRelation> ipVipTimeOrderRelationList;

    /**
     *  寻仓单同批次数据
     */
    private List<IpJitxDeliveryRelation> IpJitxDeliveryRelation;

    /**
     * 根单号锁
     */
    private RedisReentrantLock redisLock;

    /**
     * 根单号
     */
    private String rootOrderSn;

    /**
     * 是否门店JITX订单
     */
    private Boolean storeJitx;


    /**
     * 时效单总数
     */
    private int timeOrderCount;

    /**
     * 寻仓单总数
     */
    private int deliveryOrderCount;

    /**
     * 未处理状态寻仓单总数
     */
    private int unsyncdeliveryOrderCount;

    /**
     * 未处理状态 寻仓单单号
     */
    private List<String> unsyncOrderSnList;

    /**
     * 寻仓单虚拟寻仓完成表示
     */
    private Integer isVirtualOccupy;

    /**
     * @return 获取当前处理单据的订单ID号
     */
    public Long getOrderId() {
        if (jitxDelivery != null) {
            return jitxDelivery.getId();
        }
        return -1L;
    }

    /**
     * @return 获取当前处理单据的订单ID号
     */
    public String getOrderNo() {
        return jitxDelivery.getOrderSn();
    }

}
