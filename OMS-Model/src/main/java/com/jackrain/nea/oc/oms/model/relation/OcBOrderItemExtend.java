package com.jackrain.nea.oc.oms.model.relation;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @author: 李龙飞
 * @create: 2019-05-13 10:31
 **/
@Data
public class OcBOrderItemExtend extends OcBOrderItem implements Serializable {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    public static final String PS_C_SKU_ECODE = "PS_C_SKU_ECODE";//商品条码
    public static final String OC_B_ORDER_ID = "OC_B_ORDER_ID";//订单编号
    //错误信息
    private String desc;
    //头子表关联列
    private String BK;
    //行号
    private int rowNum;

    private String billNo; // 订单单据号
    private String isGiftName; // 是否赠品
    private String isLackstockName; // 是否实缺标记
    private String refundStatusName; // 退款状态


    private List<Long> ocBOrderList;


    /**
     * 查询类生成工具
     *
     * @return
     */
    public QueryWrapper<OcBOrderItem> createQueryWrapper() {
        QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper<OcBOrderItem>();
        if (this.getPsCSkuEcode() != null) {
            // 商品条码
            queryWrapper.eq(PS_C_SKU_ECODE, this.getPsCSkuEcode());
        }
        if (this.getOcBOrderId() != null) {
            // 订单编号
            queryWrapper.eq(OC_B_ORDER_ID, this.getOcBOrderId());
        }
        if (this.getOcBOrderList() != null) {
            // 订单编号List
            queryWrapper.in(OC_B_ORDER_ID, this.getOcBOrderList());
        }
        return queryWrapper;
    }

    /**
     * 导入生成模型
     *
     * @return
     */
    public static OcBOrderItemExtend importCreate(int index, OcBOrderItemExtend ocBOrderItemExtend,
                                                  Map<String, String> columnMap) {
        try {
            ocBOrderItemExtend.setPsCSkuEcode(columnMap.get(rowStr + index + cellStr + 0));
        } catch (Exception e) {

        }
        try {
            ocBOrderItemExtend.setQty(new BigDecimal(columnMap.get(rowStr + index + cellStr + 1)));
        } catch (Exception e) {

        }
        try {
            ocBOrderItemExtend.setPsCSkuEcode(columnMap.get(rowStr + index + cellStr + 0));
            ocBOrderItemExtend.setQty(new BigDecimal(columnMap.get(rowStr + index + cellStr + 1)));
            ocBOrderItemExtend.setPrice(new BigDecimal(columnMap.get(rowStr + index + cellStr + 2)));
            ocBOrderItemExtend.setPriceActual(new BigDecimal(columnMap.get(rowStr + index + cellStr + 2)));
            ocBOrderItemExtend.setRealAmt(ocBOrderItemExtend.getPriceActual().multiply(ocBOrderItemExtend.getQty()));
            ocBOrderItemExtend.setPriceList(new BigDecimal(columnMap.get(rowStr + index + cellStr + 2)));
        } catch (Exception e) {

        }
        try {
            ocBOrderItemExtend.setBK(columnMap.get(rowStr + index + cellStr + 3));
        } catch (Exception e) {

        }

        ocBOrderItemExtend.setRowNum(index + 1);
        return ocBOrderItemExtend;
    }


}
