package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * @ClassName OcBOrderItemOaExtend
 * @Description OA订单商品信息
 * <AUTHOR>
 * @Date 2024/1/25 09:11
 * @Version 1.0
 */
@Data
@Slf4j
public class OcBOrderItemOaExtend extends OcBOrderItem implements Serializable {
    private static final long serialVersionUID = -4419645108947795975L;
}
