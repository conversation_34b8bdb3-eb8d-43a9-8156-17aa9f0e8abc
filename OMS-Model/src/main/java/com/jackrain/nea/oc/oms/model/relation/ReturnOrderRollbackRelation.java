package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 手动匹配异常，数据回滚
 *
 * <AUTHOR>
 * @date 2020/12/1 6:56 下午
 */
@Data
public class ReturnOrderRollbackRelation {

    private OcBReturnOrder returnOrder;

    private Set<OcBReturnOrderRefund> returnOrderRefunds;

    private List<OcBRefundInProductItem> refundInProductItems;
}
