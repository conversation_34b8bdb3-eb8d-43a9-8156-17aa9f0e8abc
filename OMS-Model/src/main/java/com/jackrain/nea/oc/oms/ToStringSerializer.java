package com.jackrain.nea.oc.oms;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializeWriter;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * 孙勇生
 * create at:  2019/8/28  15:23
 *
 * @description: Long转换String 定义序列化
 */
public class ToStringSerializer implements ObjectSerializer {


    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {

        SerializeWriter out = serializer.out;

        if (object == null) {
            out.writeNull();
            return;
        }

        //原来的值code
        String strVal = object.toString();
        out.writeString(strVal);
    }


}