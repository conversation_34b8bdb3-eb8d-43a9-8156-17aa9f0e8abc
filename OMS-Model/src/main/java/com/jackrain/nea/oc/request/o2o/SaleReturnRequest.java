package com.jackrain.nea.oc.request.o2o;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Desc : 门店发货销退单
 * <AUTHOR> xiWen
 * @Date : 2020/8/14
 */
@Data
public class SaleReturnRequest implements Serializable {


    private static final long serialVersionUID = 861817138795901085L;

    @JSONField(name = "line_id")
    private String lineId;

    /**
     * 伯俊中台退单编号	退换货单的单据编号
     */
    @JSONField(name = "refundId")
    private String refundId;

    /**
     * 中台订单编号 - 退换货单原始订单编号对应的出库通知单
     */
    @JSONField(name = "origOrderId")
    private String origOrderId;

    /**
     * 平台单号，多个逗号分隔	退换货单的原始平台单号
     */
    @JSONField(name = "sourceCode")
    private String sourcecode;

    /**
     * 入库仓库	退换货单的入库实体仓编码
     */
    @JSONField(name = "refundWarehouse")
    private String refundWarehouse;

    /**
     * 创建时间	退换货单的创建时间
     */
    @JSONField(name = "creationDate")
    private String creationDate;

    /**
     * 入库时间	退换货单的入库时间
     */
    @JSONField(name = "inTime")
    private String inTime;

    /**
     * 来源店铺代码	来源店铺代码	退换货单对应零售发货单下单店铺的店铺代码
     */
    @JSONField(name = "lyzdDm")
    private String lyzdDm;

    /**
     * 来源店铺名称	退换货单对应零售发货单的下单店铺的店铺名称
     */
    @JSONField(name = "lyzdMc")
    private String lyzdMc;

    /**
     * 来源渠道代码	退换货单对应零售发货单的下单店铺的来源平台编码
     */
    @JSONField(name = "lyorgDm")
    private String lyorgDm;

    /**
     * 来源渠道名称	退换货单对应零售发货单的下单店铺的来源平台名称
     */
    @JSONField(name = "lyorgMc")
    private String lyorgMc;

    /**
     * 下单门店代码	退换货单的原发货门店编码
     */
    @JSONField(name = "xdzdDm")
    private String xdzdDm;

    /**
     *
     */
    @JSONField(name = "customerid")
    private String customerId;

    /**
     * 退货商品明细
     */
    @JSONField(name = "item")
    private List<SaleReturnItem> item;


    @Data
    public static class SaleReturnItem implements Serializable {

        private static final long serialVersionUID = -2867832702055197317L;

        /**
         * 商品代码	根据退货商品SKU从商品中心获取，商品中心提供获取服务
         */
        @JSONField(name = "goodsCode")
        private String goodsCode;

        /**
         * 颜色代码	根据退货商品SKU从商品中心获取，商品中心提供获取服务
         */
        @JSONField(name = "colorCode")
        private String colorCode;

        /**
         * 尺码代码	根据退货商品SKU从商品中心获取，商品中心提供获取服务
         */
        @JSONField(name = "sizeCode")
        private String sizeCode;

        /**
         * 数量	退换货单退货明细相应商品的订单数量
         */
        @JSONField(name = "qty")
        private BigDecimal qty;

        /**
         * 单件退货金额	退换货单退货明细相应商品的单件退货金额
         */
        @JSONField(name = "amtRefundSingle")
        private BigDecimal amtRefundSingle;

        /**
         * 退货金额	退换货单退货明细相应商品的退货金额
         */
        @JSONField(name = "amtRefund")
        private BigDecimal amtRefund;

        /**
         * 赠品标识	退换货单退货明细相应商品的赠品状态
         * 0、非赠品，1、赠品
         */
        @JSONField(name = "giftSign")
        private String giftSign;


    }


}
