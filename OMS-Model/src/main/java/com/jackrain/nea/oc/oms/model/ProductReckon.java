package com.jackrain.nea.oc.oms.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: 黄世新
 * @Date: 2020/4/8 11:07 上午
 * @Version 1.0
 */
@Data
public class ProductReckon implements Serializable {
    /**
     * 条码
     */
    private String skuCode;

    /**
     * 数量
     */
    private BigDecimal qty;

    /**
     * 成交金额
     */
    private BigDecimal realAmt;
    /**
     * 成交单价
     */
    private BigDecimal price;

    /**
     * 优惠金额
     */
    private BigDecimal amtDiscount;

    /**
     * 调整金额
     */
    private BigDecimal adjustAmt;


}
