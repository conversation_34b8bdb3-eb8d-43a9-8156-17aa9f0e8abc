package com.jackrain.nea.oc.oms.model;

import com.jackrain.nea.oc.oms.model.enums.OrderType;
import lombok.Data;
import lombok.ToString;

/**
 * 操作订单“更新配货中”服务MQ消息实体类
 *
 * @author: 胡林洋
 * @since: 2019-05-09
 * create at : 2019-05-09 13:14
 */
@Data
@ToString
public class UpdateInDistributionMqInfo {

    /**
     * 订单ID，根据对应的单据类型到对应的单据表中查询对应的ID
     */
    private Long orderId;

    /**
     * 订单编号ֵ
     */
    private String orderNo;

    /**
     * 状态编码【0:成功 -1:失败】
     */
    private int code;

    /**
     * 订单类型
     */
    private OrderType orderType;

    /**
     * wms失败原因
     */
    private String wmsFailReason;

    /**
     * 出库通知单
     */
    private String noticesBillNo;

    /**
     * 失败原因
     */
    private String flag;
}
