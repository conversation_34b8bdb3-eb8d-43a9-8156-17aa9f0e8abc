package com.jackrain.nea.oc.oms.model;

import com.jackrain.nea.oc.oms.model.relation.MergeOderGroups;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR> ruan.gz
 * @Description : 操作合单MQ消息实体类
 * @Date : 2020/6/19
 **/
@Data
@ToString
public class MergeOrderInfo implements Serializable {

    /**
     * 合单分组信息
     */
    private MergeOderGroups mergeOderGroups;

    /**
     * 合单店铺信息
     */
    private StCMergeOrderInfo stCMergeOrderDO;

    /**
     * 合单ID集合
     */
    private List<Long> orderIds;

    /**
     * 合单订单集合
     */
    private List<OcBOrder> orderList;

    /**
     * 当前操作的订单
     */
    private OcBOrder ocBOrder;
}
