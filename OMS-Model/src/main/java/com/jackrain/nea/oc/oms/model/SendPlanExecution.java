package com.jackrain.nea.oc.oms.model;

import com.jackrain.nea.oc.oms.model.enums.OrderType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-06-17
 * @desc 派单方案执行参数
 **/
@Data
public class SendPlanExecution {
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 符合分仓的实体仓ID
     */
    private List<Long> warehouseIdList;
    /**
     * 省份ID
     */
    private Long provinceId;
    /**
     * 唯品会仓库ID
     */
    private Long cpCVipcomWahouseId;
    /**
     * 订单类型
     */
    private OrderType orderType;

    public SendPlanExecution() {
    }

    public SendPlanExecution(Long orderId, Long shopId, List<Long> warehouseIdList, OrderType orderType) {
        this.orderId = orderId;
        this.shopId = shopId;
        this.warehouseIdList = warehouseIdList;
        this.orderType = orderType;
    }
}
