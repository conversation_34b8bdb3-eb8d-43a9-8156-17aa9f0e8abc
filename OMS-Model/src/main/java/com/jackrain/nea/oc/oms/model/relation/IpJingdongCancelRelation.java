package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongSaRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @description: 京东取消订单中间表关系
 * @author: 郑小龙
 * @date: 2020-05-29 14:23
 **/
@Data
public class IpJingdongCancelRelation {
    /**
     * 京东取消订单中间表
     */
    private IpBJingdongSaRefund jingdongSaRefund;

    /**
     * 京东中间表主表
     */
    private IpBJingdongOrder jingdongOrder;

    /**
     * 对应有效原单
     */
    private List<OcBOrder> ocBOrder;


    /**
     * 平台单号
     *
     * @return
     */
    public String getSourceCode() {
        String tid = "";
        if (null != jingdongSaRefund && StringUtils.isNotEmpty(jingdongSaRefund.getOrderid())) {
            tid = jingdongSaRefund.getOrderid();
        }
        if (StringUtils.isEmpty(tid) && null != ocBOrder && ocBOrder.size() > 0) {
            for (OcBOrder bOrder : ocBOrder) {
                if (StringUtils.isNotEmpty(bOrder.getSourceCode())) {
                    tid = bOrder.getSourceCode();
                    break;
                }
            }
        }
        return tid;
    }

    /**
     * 中间表订单ID
     */
    public long getOrderId() {
        if (jingdongSaRefund != null) {
            return jingdongSaRefund.getId();
        }
        return -1;
    }

    /**
     * 中间表退款单ID
     */
    public long getPopafsrefundapplyid() {
        if (jingdongSaRefund != null) {
            return jingdongSaRefund.getPopafsrefundapplyid();
        }
        return -1;
    }
}
