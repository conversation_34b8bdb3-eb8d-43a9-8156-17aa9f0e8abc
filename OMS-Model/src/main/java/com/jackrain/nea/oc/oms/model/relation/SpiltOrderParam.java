package com.jackrain.nea.oc.oms.model.relation;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: 黄世新
 * @Date: 2022/8/1 下午3:00
 * @Version 1.0
 */
@Data
public class SpiltOrderParam implements Serializable {

    /**
     * 业务类型
     */
    private Long businessTypeId;

    private String businessTypeCode;

    private String businessTypeName;
    /**
     * 是否需要卡单
     */
    private boolean cardOrder;

    /**
     * 是否需要hold单
     */
    private boolean holdOrder;

    /**
     * hold单释放时间
     */
    private Date holdReleaseTime;

    /**
     * 卡单释放时间
     */
    private Date cardReleaseTime;

    /**
     * 是否虚拟订单
     */
    private boolean fictitiousOrder;


}
