package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author:孙勇生
 * @description: 退款中间表关系
 * @Date: 19/3/9 22:14
 */
@Data
public class IpTaobaoRefundRelation {
    /**
     * 淘宝退单中间表
     */
    private IpBTaobaoRefund taobaoRefund;
    /**
     * 对应原始定单商品明细(可有多条原单明细)
     */
    private List<OcBOrderItem> ocBOrderItems;
    /**
     * 对应原始定单赠品明细
     */
    private List<OcBOrderItem> ocBOrderGifts;
    /**
     * 对应有效原单
     */
    private List<OcBOrder> ocBOrder;

    /**
     * 是否存在有效的原单
     */
    private boolean isEffectiveOrder;
    /**
     * 是否为换货后有退单
     */
    private boolean isExchangeGoods;


    private Map<Long, LockObject> lockObjectMap;


    /**
     * @return
     */
    public long getOrderId() {
        if (taobaoRefund != null) {
            return taobaoRefund.getId();
        }
        return -1;
    }

    /**
     * @return
     */
    public String getOrderNo() {
        if (taobaoRefund != null) {
            return taobaoRefund.getRefundId() + "";
        }
        return "";
    }

}

