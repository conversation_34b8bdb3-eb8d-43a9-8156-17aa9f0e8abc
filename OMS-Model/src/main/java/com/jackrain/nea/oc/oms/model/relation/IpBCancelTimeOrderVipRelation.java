package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBCancelTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBCancelTimeOrderVipItem;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chenxiulou
 * @since : 2019-06-25
 * create at : 2019-06-25 6:00 PM
 */
@Data
public class IpBCancelTimeOrderVipRelation {

    private IpBCancelTimeOrderVip cancelTimeOrderVip;

    private List<IpBCancelTimeOrderVipItem> cancelTimeOrderItemList;

    private String remarks;

    /**
     * @return 获取当前处理单据的订单ID号
     */
    public Long getOrderId() {
        if (cancelTimeOrderVip != null) {
            return cancelTimeOrderVip.getId();
        }
        return -1L;
    }

    /**
     * @return 获取当前处理单据的订单ID号
     */
    public String getOrderNo() {
        if (cancelTimeOrderVip != null) {
            return cancelTimeOrderVip.getOccupiedOrderSn();
        }
        return "";
    }

}
