package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 周琳胜
 * @since : 2019-07-09
 * create at : 2019-07-09 14:00
 */
@Data
public class IpTaobaoFxOrderRelation {

    /**
     * 淘宝分销中间表主表
     */
    private IpBTaobaoFxOrder ipBTaobaoFxOrder;

    /**
     * 主表对应明细，分库键是order_id
     */
    private List<IpBTaobaoFxOrderItemExt> taobaoFxOrderItems;


    /**
     * 全渠道订单主表
     */
    private OcBOrder ocBOrder;

    /**
     * 全渠道订单明细
     */
    private List<OcBOrderItem> ocBOrderItems;

    /**
     * 全渠道订单付款信息表
     */
    private OcBOrderPayment ocBOrderPayment;

    /**
     * 全渠道订单优惠信息表
     */
    private List<OcBOrderPromotion> ocBOrderPromotionList;

    /*
     *订单日志表
     */
    private OcBOrderLog ocBOrderLog;

    private OcBOrderLink orderLink;

    /**
     * @return 获取当前处理单据的订单ID号
     */
    public Long getOrderId() {
        if (ipBTaobaoFxOrder != null) {
            return ipBTaobaoFxOrder.getId();
        }
        return -1L;
    }

    public Long getOrderNo() {
        if (ipBTaobaoFxOrder != null) {
            return ipBTaobaoFxOrder.getFenxiaoId();
        }
        return -1L;
    }


    public String getOrderBillCode() {
        if (ocBOrder != null) {
            return ocBOrder.getSgBOutBillNo();
        }
        return "";
    }
}
