package com.jackrain.nea.oc.oms.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @ClassName StCCarCostInfo
 * @Description 零担+整车报价信息
 * <AUTHOR>
 * @Date 2024/4/8 18:26
 * @Version 1.0
 */
@Data
public class StCCarCostInfo implements Serializable {
    private static final long serialVersionUID = -4955815989901757685L;

    private Long id;

    private Long cpCPhyWarehouseId;

    private Long cpCLogisticsId;

    private Date startDate;

    private Date endDate;

    private Long submitUserId;

    private LocalDateTime submitTime;

    private Integer status;

    private Long closeUserId;

    private LocalDateTime closeTime;

    private Integer closeStatus;

    private BigDecimal oilPriceLinkage;

    private String remark;

    private Long version;

    private String ownerename;

    private String modifierename;

    /**
     * 0是零担 1是整车
     */
    private Integer type;

}
