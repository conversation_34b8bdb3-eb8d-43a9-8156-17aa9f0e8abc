package com.jackrain.nea.oc.oms.model.constant;

/**
 * Description: 销售数据记录表常量
 *
 * @Author: guo.kw
 * @Since: 2022/8/23
 * create at: 2022/8/23 15:01
 */
public class OcBSapSalesDataRecordConstant {

    public static final String MAIN_TABLE = "OC_B_SAP_SALES_DATA_RECORD";
    public static final String ITEM_TABLE = "OC_B_SAP_SALES_DATA_RECORD_ITEM";

    /**
     * 汇总状态 0 未汇总  1 已汇总
     */
    public static final String SUM_STATUS_ZERO = "0";
    public static final String SUM_STATUS_ONE = "1";

    /**
     * 汇总状态 1 订单  2 退单 3 库存调整单
     */
    public static final String SAP_BILL_CATEGORY_01 = "1";
    public static final String SAP_BILL_CATEGORY_02 = "2";
    public static final String SAP_BILL_CATEGORY_03 = "3";

    /**
     * 汇总类型 -1 忽略 0 销售汇总  1 奶卡汇总 2 库存调整单汇总(已删除) Z74 无名件  Z73 冲无名件 Z23 包菜调整
     */
    public static final String SUM_TYPE_IGNORE = "-1";
    public static final String SUM_TYPE_XS = "0";
    public static final String SUM_TYPE_NK = "1";
    public static final String SUM_TYPE_KC = "2";
    public static final String SUM_TYPE_WMJ = "Z74";
    public static final String SUM_TYPE_CWMJ = "Z73";
    public static final String SUM_TYPE_BCTZ = "Z23";
    public static final String SUM_TYPE_RYCD01 = "RYCD01";
    public static final String SUM_TYPE_RYCD02 = "RYCD02";
    public static final String SUM_TYPE_RYCD03 = "RYCD03";
    public static final String SUM_TYPE_RYCD04 = "RYCD04";

    public static final Integer OMS_COMMON_INSERT_PAGE_SIZE = 300;


    public static final String ISACTIVE_YES = "Y";
    public static final String ISACTIVE_NO = "N";

}
