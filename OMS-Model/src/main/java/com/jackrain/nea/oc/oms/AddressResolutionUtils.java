package com.jackrain.nea.oc.oms;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 根据详细地址解析 省市区
 *
 * @author: gale.qin
 * @since: 2020/7/1
 * create at : 2020/7/1 19:20
 */
public class AddressResolutionUtils {

    public static String[] ZX_CITYS = new String[]{"北京", "天津", "上海", "重庆"};

    /**
     * 解析地址
     *
     * @param address
     * @return
     * <AUTHOR>
     */
    public static Map<String, String> addressResolution(String address) {

        // "((?<province>[^省]+省|.+自治区|上海|北京|天津|重庆))"
        String regex = "(?<province>[^省]+自治区|.*?省|.*?行政区|上海|北京|天津|重庆)" +
                "(?<city>[^市]+自治州|.*?地区|.*?行政单位|市辖区|.*?市|.+盟|.*?县)" +
                "(?<area>[^县]+县|.*?区|.*?市|.*?旗|.*?海域|.*?岛)?" +
                "(?<town>[^区]+区|.+镇)?(?<village>.*)";
        Matcher m = Pattern.compile(regex).matcher(address);
        String province = null, city = null, area = null, town = null, village = null;
        Map<String, String> row = new LinkedHashMap<String, String>();
        while (m.find()) {
            province = m.group("province");
            row.put("province", province == null ? "" : province.trim());
            city = m.group("city");
            // 处理市辖区
            for (String specialName : ZX_CITYS) {
                if (StringUtils.isNotEmpty(province) && StringUtils.isNotEmpty(city)) {
                    if (province.startsWith(specialName) && !city.startsWith(specialName)) {
                        city = province + "市";
                    }
                }
            }
            row.put("city", city == null ? "" : city.trim());
            area = m.group("area");
            if (StringUtils.isNotBlank(area) && area.startsWith("市辖区")) {
                area = area.replace("市辖区", "");
            }
            row.put("area", area == null ? "" : area.trim());
            town = m.group("town");
            row.put("town", town == null ? "" : town.trim());
            village = m.group("village");
            row.put("village", village == null ? "" : village.trim());
        }
        return row;
    }

    public static void main(String[] args) {
        System.out.println("地址1=" + addressResolution("新疆维吾尔自治区昌吉回族自治州玛纳斯县大虹桥"));
        System.out.println("地址2=" + addressResolution("浙江省杭州市余杭区红旗路1890号"));
        System.out.println("地址3=" + addressResolution("上海市辖区闵行区红旗路1003"));
        System.out.println("地址4=" + addressResolution("上海市市辖区闵行区红旗路1003"));
        System.out.println("地址5=" + addressResolution("云南省昆明市盘龙区联盟街道金尚俊园一期2栋"));
        System.out.println("地址6=" + addressResolution("湖北省黄石市西塞山区湖北省黄石市西塞山区湖北省黄石市西塞山区联合村月桂花苑A栋"));
        String[] adrList = new String[]{"陕西省 安康市 镇坪县陕西省安康市镇坪县城关镇上新街32号镇坪县农村信用合作联社(城关信用社)",
                "北京 北京市 海淀区北京市北京市海淀区学院路街道北京林业大学东区近邻宝",
                "广东省 深圳市 龙岗区广东省深圳市龙岗区南湾街道左庭右院南区4栋一单元7C",
                "广东省 深圳市 南山区广东省深圳市南山区南头街道友邻公寓",
                "江苏省 常州市 天宁区江苏省常州市天宁区和平国际小区4-乙单元-2001",
                "四川省 泸州市 叙永县四川省泸州市叙永县杨武坊汇锦峰二幢一单元",
                "安徽省 合肥市 包河区安徽省合肥市包河区武汉路天慧紫辰阁12栋驿站",
                "湖北省 黄石市 西塞山区湖北省黄石市西塞山区湖北省 黄石市 西塞山区联合村月桂花苑A栋",
                "山东省 烟台市 芝罘区山东省烟台市芝罘区南大街供销大厦5楼烟台日日升智能财税"};

        for (int i = 0; i < adrList.length; i++) {
            System.out.println("循环匹配地址-" + i + ":" + addressResolution(adrList[i]));
        }
    }
}
