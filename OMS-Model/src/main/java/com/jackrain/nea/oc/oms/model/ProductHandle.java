package com.jackrain.nea.oc.oms.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/4/8 11:30 上午
 * @Version 1.0
 */
@Data
public class ProductHandle implements Serializable {

    private ProductReckon productReckon;

    /**
     * 打散的suk
     */
    private List<ProductReckon> productReckons;


    public ProductHandle(ProductReckon productReckon) {
        this.productReckon = productReckon;
    }


    private List<ProductReckon> dataPlan() {
        BigDecimal qty = productReckon.getQty();
        if (BigDecimal.ONE.compareTo(qty) == 0) {
            productReckons.add(productReckon);
            return productReckons;
        }
        //成交金额
        BigDecimal realAmt = productReckon.getRealAmt();
        //优惠金额
        BigDecimal amtDiscount = productReckon.getAmtDiscount();
        //调整金额
        BigDecimal adjustAmt = productReckon.getAdjustAmt();
        //条码
        String skuCode = productReckon.getSkuCode();
        for (int i = 0; i < qty.intValue(); i++) {
            ProductReckon productReckon = new ProductReckon();
            productReckon.setQty(BigDecimal.ONE);
            productReckon.setSkuCode(skuCode);
        }

        return productReckons;
    }
}
