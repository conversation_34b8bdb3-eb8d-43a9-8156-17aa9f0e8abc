package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import lombok.Data;

import java.util.List;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-04-03 11:38
 */
@Data
public class IpOrderReturnRelation {

    /**
     * 全渠道订单主表
     */
    private OcBOrder ocBOrder;

    /**
     * 全渠道订单明细
     */
    private List<OcBOrderItem> ocBOrderItems;

    /**
     * 退换货主表
     */
    private OcBReturnOrder ocBReturnOrder;

    /**
     * 是否禁止hold单.退换货订单
     */
    private boolean forbidHoldExchangeOrder;

    /**
     * 单据类型
     */
    private OrderTypeEnum orderType;

    /**
     * 订单奶卡信息
     */
    private OcBOrderNaiKa orderNaiKa;

}
