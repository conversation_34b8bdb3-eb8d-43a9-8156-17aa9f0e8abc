package com.jackrain.nea.oc.oms.model;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * GiftInfo实体类
 *
 * @author: 胡林洋
 * @since: 2020-03-30
 * create at : 2020-03-30 11:27
 */
@Data
@ToString
public class GiftInfo {

    /**
     * 数量
     */
    private BigDecimal qtty;

    /**
     * 系统条码
     */
    private String sku;

    /**
     * 系统编码
     */
    private String ecode;

    /**
     * 平台编码
     */
    private String platformEcode;

    /**
     * 平台条码
     */
    private String platformSku;

    /**
     * 来源商品系统编码
     */
    private String sourceEcode;

    /**
     * 来源商品系统SKU
     */
    private String sourceEsku;

    /**
     * 赠品拆单类型 1不拆单  2 可拆弹  3赠品后发
     */
    private Integer isGiftSplit;

    /**
     *  间隔时长
     */
    private Integer intervalTime;

    /**
     *     发货时点 1=主品平台发货后 2=主品签收后发 3=主品发货后多久发
     */
    private Integer deliverNode;
}
