package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirectRefund;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirectRefundItem;
import lombok.Data;

import java.util.List;

/**
 * @Desc : DDO
 * <AUTHOR> xiWen
 * @Date : 2022/3/26
 */
@Data
public class OmsJDDirectCancelRelation {

    private StepExecInfo stepExecInfo;

    private IpBJingdongDirectRefund ipRefund;

    private List<IpBJingdongDirectRefundItem> ipRefundItems;

    private List<OmsOrderRelation> ocOrderRelations;


    public Long getIpOrderId() {
        return ipRefund == null ? -1L : ipRefund.getId();
    }

    public String getIpRefundNo() {
        return ipRefund == null ? "" : ipRefund.getRefundId();
    }

    public static OmsJDDirectCancelRelation buildRelation(Long id, String no) {
        IpBJingdongDirectRefund ipBJingdongDirectRefund = new IpBJingdongDirectRefund();
        ipBJingdongDirectRefund.setId(id);
        ipBJingdongDirectRefund.setRefundId(no);
        OmsJDDirectCancelRelation omsJDDirectCancelRelation = new OmsJDDirectCancelRelation();
        omsJDDirectCancelRelation.setIpRefund(ipBJingdongDirectRefund);
        return omsJDDirectCancelRelation;
    }
}
