package com.jackrain.nea.oc.oms.model.resources;

/**
 * 订单占用状态
 *
 * @author: 易邵峰
 * @since: 2019-01-25
 * create at : 2019-01-25 22:15
 */
public class OrderOccupyStatus {

    /**
     * 执行正常占单逻辑状态，并且通过redis锁定行
     *
     */
    public static final int STATUS_0 = 0;

    public static final int STATUS_10 = 10;

    public static final int STATUS_5 = 5;

    /**
     * 组合商品拆分失败
     */
    public static final int STATUS_20 = 20;

    /**
     * 虚拟商品拆分失败
     */
    public static final int STATUS_30 = 30;

    /**
     * 更新平摊金额失败
     */
    public static final int STATUS_40 = 40;

    /**
     * 执行天猫周期购促销
     */
    public static final int STATUS_45 = 45;

    /**
     * 促销预执行
     */
    public static final int STATUS_50 = 50;

    /**
     * 业务类型区分
     */
    public static final int STATUS_60 = 60;

    /**
     * 指定效期
     */
    public static final int STATUS_70 = 70;

    /**
     * 残次策略
     */
    public static final int STATUS_75 = 75;

    /**
     * 对等换货
     */
    public static final int STATUS_80 = 80;


    /**
     * 卡单
     */
    public static final int STATUS_90 = 90;


    /**
     * 卡单
     */
    public static final int STATUS_100 = 100;

    /**
     * 卡单
     */
    public static final int STATUS_110 = 110;


    /**
     * 卡单
     */
    public static final int STATUS_120 = 130;

    /**
     * 卡单(执行周期购失败)
     */
    public static final int STATUS_160 = 160;


    public static final int STATUS_170 = 170;

    /**
     * 卡单状态
     */
    public static final int STATUS_11 = 11;

    /**
     * 缺货
     */
    public static final int STATUS_13 = 13;
}
