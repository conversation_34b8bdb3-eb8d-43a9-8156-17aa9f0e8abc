package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "oc_b_direct_report_order_item")
@Data
public class OcBDirectReportOrderItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "OC_B_DIRECT_REPORT_ORDER_ID")
    private Long ocBDirectReportOrderId;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "QTY")
    private BigDecimal qty;

    @JSONField(name = "START_PRODUCE_DATE", format = "yyyy-MM-dd HH:mm:ss")
    private Date startProduceDate;

    @JSONField(name = "END_PRODUCE_DATE", format = "yyyy-MM-dd HH:mm:ss")
    private Date endProduceDate;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "WEIGHT")
    private BigDecimal weight;

    @JSONField(name = "TOT_WEIGHT")
    private BigDecimal totWeight;
}