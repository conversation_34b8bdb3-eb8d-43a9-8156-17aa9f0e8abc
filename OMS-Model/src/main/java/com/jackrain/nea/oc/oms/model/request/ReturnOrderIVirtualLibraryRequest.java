package com.jackrain.nea.oc.oms.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: ming.fz
 * @Date: 2019-03-26
 * @Version 1.0
 */

@Data
public class ReturnOrderIVirtualLibraryRequest implements Serializable {
    @JSONField(name = "ID")
    private Long id;
    //
    @JSONField(name = "RETURN_STATUS")
    private Long return_status;
    //数量
    @JSONField(name = "ISCHECK")
    private Long isCheck;
}
