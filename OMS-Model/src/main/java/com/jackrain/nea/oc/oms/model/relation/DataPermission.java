package com.jackrain.nea.oc.oms.model.relation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 权限数据
 *
 * @author: xiWen.z
 * create at: 2019/8/26 0026
 */
@Accessors(chain = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataPermission implements Serializable {

    private static final long serialVersionUID = -1914143859227116102L;

    /**
     * ID
     */
    private Long id;
    /**
     * eName
     */
    private String ename;

    /**
     * eCode
     */
    private String ecode;

    /**
     * isRead
     */
    private String isRead;

    /**
     * isWrite
     */
    private String isWrite;

}
