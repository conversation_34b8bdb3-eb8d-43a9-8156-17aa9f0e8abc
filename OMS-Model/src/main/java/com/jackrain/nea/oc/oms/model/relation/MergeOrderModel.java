package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 孙勇生
 * create at:  2019/4/4  15:40
 * @description: 合并订单查询model
 */
@Data
public class MergeOrderModel extends OcBOrder implements Serializable {
    @JSONField(name = "ORDERID")
    private long orderId;//订单ID别名 用于元数据显示
    private String beginOrderDate;//开始日期
    private String endOrderDate;//结束日期
    private Integer shopId;//店铺ID
    private Integer warehouseId;//实体仓ID
    private String userNickName;//用户昵称
    private Integer startindex;//开始条数
    private Integer rang;//每页条数
    private JSONArray shopArray;//店铺权限使用


    /**
     * 订单类型
     */
    @JSONField(name = "ORDER_TYPE_NAME")
    private String orderTypeName;
    /**
     * 订单状态
     */
    @JSONField(name = "ORDER_STATUS_NAME")
    private String orderStatusName;
    /**
     * 平台
     */
    @JSONField(name = "PLATFORM_NAME")
    private String platFormName;
    /**
     * 付款方式
     */
    @JSONField(name = "PAY_TYPE_NAME")
    private String payTypeName;
    /**
     * 订单占单状态
     */
    @JSONField(name = "OCCUPY_STATUS_NAME")
    private String occupyStatusName;
    /**
     * wms撤回状态
     */
    @JSONField(name = "WMSCANCEL_STATUSNAME")
    private String wmsCancelStatusName;
//    /**
//     * 自动审核状态
//     */
//    @JSONField(name = "AUTOAUDITSTATUSNAME")
//    private String autoAuditStatusName;
    /**
     * 是否生成开标通知
     */
    @JSONField(name = "IS_GENINVOICENOTIC_ENAME")
    private String isGeninvoiceNoticeName;
    /**
     * 退货状态
     */
    @JSONField(name = "RETURNSTATUSNAME")
    private String returnStatusName;
    /**
     * 是否生成零售调拨单
     */
    @JSONField(name = "IS_TODRPNAME")
    private String isToDrpName;

    /**
     * 出库状态名称
     */
    @JSONField(name = "OUT_STATUS_NAME")
    private String outStatusName;

    /**
     * 发货标识名称
     */
    @JSONField(name = "FORCE_SEND_NAME")
    private String forceSendName;

    /**
     * 下单时间
     */
    @JSONField(name = "ORDER_DATE_STR")
    private String orderDateStr;

    /**
     * 出库时间
     */
    @JSONField(name = "SCAN_TIME_STR")
    private String scanTimeStr;

    /**
     * 修改时间
     */
    @JSONField(name = "MODIFIEDDATE_STR")
    private String modifieddateStr;
}
