package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.table.*;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 孙勇生
 * create at:
 * @description: 退换货关系
 */
@Data
public class OcBReturnOrderRelation {

    private OcBReturnOrder returnOrderInfo;
    /**
     * 换货明细
     */
    private List<OcBReturnOrderExchange> orderExchangeList;
    /**
     * 退货明细
     */
    private List<OcBReturnOrderRefund> orderRefundList;
    /**
     * 换货订单主表信息
     */
    private OcBOrder ocBOrder;
    /**
     * 换货订单的明细数据
     */
    private List<OcBOrderItem> ocBOrderItems;


    /**
     * wms返回接口对象
     */
    JSONObject request;

    /**
     * 获取returnOrder
     *
     * @return
     */
    public JSONObject getRequestReturnOrder() {
        // key
        String returnOrderKey = "returnOrder";

        if (Objects.nonNull(request) && Objects.nonNull(request.getJSONObject(returnOrderKey))) {
            return request.getJSONObject(returnOrderKey);
        }

        return null;
    }

}
