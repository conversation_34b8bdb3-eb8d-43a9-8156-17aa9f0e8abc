package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 退换货订单_入库_退货明细关系表
 *
 * @author: xiwen.z
 * create at: 2019/4/3 0003
 */
@Data
public class OcBOrderReturnRefundProductRelation implements Serializable {

    // 退货单
    private OcBReturnOrder ocBReturnOrder;
    // 入库商品
    private List<OcBRefundInProductItem> refundInProductItemList = new ArrayList<>();
    // 退货商品
    private List<OcBReturnOrderRefund> returnOrderRefundList = new ArrayList<>();


    public OcBOrderReturnRefundProductRelation() {
    }

}
