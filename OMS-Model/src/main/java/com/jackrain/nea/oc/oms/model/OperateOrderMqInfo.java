package com.jackrain.nea.oc.oms.model;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import lombok.Data;
import lombok.ToString;

/**
 * 操作订单MQ消息实体类
 *
 * @author: 易邵峰
 * @since: 2019-03-05
 * create at : 2019-03-05 18:14
 */
@Data
@ToString
public class OperateOrderMqInfo {

    /**
     * 订单ID，根据对应的单据类型到对应的单据表中查询对应的ID
     */
    private long orderId;

    /**
     * 订单编号ֵ
     */
    private String orderNo;

    /**
     * 订单渠道类型（淘宝、天猫、京东===）
     */
    private ChannelType channelType;

    /**
     * 订单类型
     */
    private OrderType orderType;

    /**
     * 操作订单类型（转单、待确认订单==）
     */
    private OperateType operateType;

    /**
     * 组装订单ids集合
     */
    private String orderIds;
}
