package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 1:55 下午
 * @Version 1.0
 */
@Data
public class OmsOrderRelation {

    /**
     * 订单主表信息
     */
    private OcBOrder ocBOrder;

    /**
     * 对应的订单明细信息
     */
    private List<OcBOrderItem> ocBOrderItems;

    /**
     * 对应的赠品信息
     */
    private List<OcOrderGifts> ocOrderGifts;
    /**
     * 对应的发货信息
     */
    private List<OcBOrderDelivery> orderDeliveries;

    /**
     * 订单标识(1:发货前 2:发货后 3:拦截)
     */
    private Integer orderMark;

    /**
     * 包裹拦截状态 未发起拦截 0、发起拦截成功 1、发起拦截失败 2、配送拦截成功 3、配送拦截失败
     */
    private Integer interceptMark = 0;

    /**
     * 重写get方法排序 防止循环修改死锁  ruan.gz 20200914
     */
    public List<OcBOrderItem> getOcBOrderItems() {
        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            return new ArrayList<>();
        }
        return sortItem(ocBOrderItems);
    }

    private List<OcBOrderItem> sortItem(List<OcBOrderItem> ocBOrderItems) {
        ocBOrderItems.sort(Comparator.comparing(OcBOrderItem::getId));
        return ocBOrderItems;
    }

    /**
     * 获取需要退款的明细以及挂靠赠品信息
     * @return
     */
    public List<OcBOrderItem> getOcBOrderItemAll(){
        List<OcBOrderItem> orderItems = new ArrayList<>(getOcBOrderItems());
        List<OcOrderGifts> ocOrderGifts = getOcOrderGifts();
        if (CollectionUtils.isNotEmpty(ocOrderGifts)) {
            for (OmsOrderRelation.OcOrderGifts ocOrderGift : ocOrderGifts) {
                if (ocOrderGift.getGiftMark() == 2) {
                    orderItems.addAll(ocOrderGift.getOcBOrderGifts());
                }
            }
        }
        return orderItems;
    }

    @Data
    public static class OcOrderGifts {
        /**
         * 对应的赠品信息
         */
        private List<OcBOrderItem> ocBOrderGifts;

        /**
         * 赠品标识(1 正常赠品,2 明细下挂赠品)
         */
        private Integer giftMark;

    }

}
