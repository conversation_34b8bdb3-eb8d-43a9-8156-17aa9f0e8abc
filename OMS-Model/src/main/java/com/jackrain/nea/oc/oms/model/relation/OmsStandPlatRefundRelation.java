package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.ps.model.ProductSku;
import lombok.Data;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/6/22 10:39 上午
 * @Version 1.0
 * <p>
 * 通用退单
 */
@Data
public class OmsStandPlatRefundRelation {

    /**
     * 通用商品退单中间表主表
     */
    private IpBStandplatRefund ipBStandplatRefund;
    /**
     * 通用商品退单中间表明细表
     */
    private List<IpBStandplatRefundItem> ipBStandplatRefundItem;

    /**
     * 通用订单中间表 可能会是空 比如tob售后单 就是空的
     */
    private IpBStandplatOrder ipBStandplatOrder;

    /**
     * 对应的订单信息
     */
    private List<OmsOrderRelation> omsOrderRelation;

    /**
     * 赠品订单信息
     */
    private List<OmsOrderRelation> isGiftOrderRelation;

    /**
     *
     */
    private IntermediateTableRelation intermediateTableRelation;

    /**
     * 换货对应的sku信息
     */
    private ProductSku productSku;

    /**
     * 对应退换单的主表信息
     */
    private OcBReturnOrder ocBReturnOrder;
    /**
     * 对应的退换货单的退货明细
     */
    private List<OcBReturnOrderRefund> ocBReturnOrderRefunds;

    /**
     * 对应的退换货单的换货明细
     */
    private List<OcBReturnOrderExchange> ocBReturnOrderExchanges;

    private List<OmsOrderExchangeRelation> exchangeRelation;
    /**
     * 对应的换货的原始订单(多个个)
     */
    private List<OmsOrderExchangeRelation> originalSingleOrder;

    /**
     * @return
     */
    public long getOrderId() {
        if (ipBStandplatRefund != null) {
            return ipBStandplatRefund.getId();
        }
        return -1;
    }

    /**
     * @return
     */
    public String getOrderNo() {
        if (ipBStandplatRefund != null) {
            return ipBStandplatRefund.getReturnNo();
        }
        return "";
    }

    /**
     * 获取源平台单号
     *
     * @return
     */
    public String getSourceTid() {
        if (ipBStandplatRefund != null) {
            return ipBStandplatRefund.getOrderNo();
        }
        return "";
    }

    /**
     * 是否是整单退,根据字表上的subOrderId去区分
     *
     * @return
     */
    public boolean isFullRefund() {
        if (ipBStandplatRefundItem != null) {
            return ipBStandplatRefundItem.stream().filter(item -> item.getSubOrderId() != null)
                    .map(IpBStandplatRefundItem::getSubOrderId).collect(Collectors.toList()).isEmpty();
        }
        return true;
    }

    /**
     * 获取平台单号
     *
     * @return
     */
    public String getTid() {
        if (Objects.isNull(ipBStandplatRefund)) {
            return "";
        }
        return ipBStandplatRefund.getOrderNo();
    }
}
