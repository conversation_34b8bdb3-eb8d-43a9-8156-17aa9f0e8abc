package com.jackrain.nea.oc.oms.model;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import lombok.Data;

import java.util.List;

/**
 * @Descroption 短信策略处理参数类
 * <AUTHOR>
 * @Date 2020/8/29
 */
@Data
public class SmsSendStrategyInfo {

    private SmsSendMsgTextInfo smsSendMsgTextInfo;

    /**
     * 区分不同短信处理服务
     */
    private String serviceType;

    private OcBOrder ocBOrder;

    private OcBReturnOrder ocBReturnOrder;

    private OcBRefundIn ocBRefundIn;

    /**
     * 通知类型
     * 1:物流发货提醒
     * 2:退换货提醒
     */
    private String adviceType;

    /**
     * 任务节点
     * 1:非平台拆分订单完成仓库发货
     * 2:非天猫换货订单完成仓库发货
     * 3:退换货完成入库
     * 4:无名件完成入库
     */
    private String taskNode;

    /**
     * 短信策略Id
     */
    private Long msgId;

    /**
     * 手机号
     */
    private String receiverMobile;

    /**
     * 短信内容
     */
    private String smsContent;

    /**
     * 订单明细表
     */
    private List<OcBOrderItem> orderItemList;

}
