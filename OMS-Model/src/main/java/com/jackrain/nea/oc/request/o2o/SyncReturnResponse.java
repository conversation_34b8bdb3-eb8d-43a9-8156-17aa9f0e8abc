package com.jackrain.nea.oc.request.o2o;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Desc : 门店发货销退单同步回传
 * <AUTHOR> xiWen
 * @Date : 2020/8/22
 */
@Data
public class SyncReturnResponse implements Serializable {

    private static final long serialVersionUID = -8722031098689407056L;

    @JSONField(name = "method")
    private String method;

    @JSONField(name = "data")
    private List<ItemResult> list;

    private List<ItemResult> successItems;

    private List<ItemResult> failedItems;


    /**
     * 明细
     */
    @Data
    public static class ItemResult implements Serializable {

        private static final long serialVersionUID = -5826027951969916789L;

        @JSONField(name = "code")
        private int code;

        @JSONField(name = "message")
        private String message;

        @JSONField(name = "line_id")
        private Long lineId;

        @JSONField(name = "refundId")
        private String refundId;

        @J<PERSON>NField(name = "customerId")
        private String customerId;


    }
}
