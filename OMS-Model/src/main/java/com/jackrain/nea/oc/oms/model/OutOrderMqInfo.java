package com.jackrain.nea.oc.oms.model;

import com.jackrain.nea.oc.oms.model.enums.OrderType;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 操作订单出库MQ消息实体类
 *
 * @author: 胡林洋
 * @since: 2019-05-07
 * create at : 2019-05-07 18:14
 */
@Data
@ToString
public class OutOrderMqInfo {

    /**
     * 订单ID，根据对应的单据类型到对应的单据表中查询对应的ID
     */
    private Long orderId;

    /**
     * 订单编号ֵ
     */
    private String orderNo;

    /**
     * 状态编码【0:成功 -1:失败】
     */
    private int code;

    /**
     * 订单类型
     */
    private OrderType orderType;

    /**
     * 物流单号
     */
    private String logisticNumber;

    /**
     * 物流公司id
     */
    private Long cpCLogisticsId;

    /**
     * 物流公司编码
     */
    private String cpCLogisticsCode;

    /**
     * 物流公司名称
     */
    private String cpCLogisticsName;

    /**
     * 包裹商品
     */
    private String skuCodes;

    /**
     * 重量
     */
    private BigDecimal theoreticalWeight;

}
