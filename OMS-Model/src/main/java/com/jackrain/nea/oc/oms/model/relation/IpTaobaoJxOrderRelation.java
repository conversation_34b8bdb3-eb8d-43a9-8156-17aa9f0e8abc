package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 淘宝经销订单转单
 **/
@Data
public class IpTaobaoJxOrderRelation {
    /**
     * 淘宝经销订单主表
     **/
    private IpBTaobaoJxOrder taobaoJxOrder;
    /**
     * 淘宝经销订单明细带商品信息子表
     **/
    private List<IpBTaobaoJxOrderItemEx> ipBTaobaoJxOrderItemExList;
    /**
     * 全渠道订单主表
     **/
    private OcBOrder ocBOrder;
    /**
     * 全渠道订单子表
     **/
    private List<OcBOrderItem> ocBOrderItemList;
    /**
     * 全链路日志表
     **/
    private OcBOrderLink ocBOrderLink;
    /**
     * 订单支付表
     **/
    private OcBOrderPayment ocBOrderPayment;

    /**
     * 获取淘宝经销订单ID
     **/
    public Long getOrderId() {
        if (taobaoJxOrder != null) {
            return taobaoJxOrder.getId();
        }
        return -1L;
    }

    /**
     * 获取淘宝经销订单单据编号
     **/
    public String getOrderNo() {
        if (taobaoJxOrder != null) {
            return taobaoJxOrder.getDealerOrderId().toString();
        }
        return "";
    }
}
