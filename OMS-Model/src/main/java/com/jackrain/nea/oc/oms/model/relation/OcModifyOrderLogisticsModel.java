package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * description:
 * @Author:  liuwenjin
 * @Date 2022/10/6 17:33
 */
@Data
@Accessors(chain = true)
public class OcModifyOrderLogisticsModel implements Serializable {
    private static final long serialVersionUID = -7859393425326617555L;

    private UserImpl user;

    private String tag;

    private String topic;

    private Long orderId;

    private Long logisticsId;

    private String logisticsCode;

    private String logisticsName;


}
