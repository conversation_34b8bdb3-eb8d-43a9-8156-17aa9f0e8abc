package com.jackrain.nea.oc.oms.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR> ruan.gz
 * @Description : 操作合单MQ消息实体类
 * @Date : 2020/6/19
 **/
@Data
@ToString
public class StCMergeOrderInfo implements Serializable {

    private Long id;

    private Long cpCShopId;

    private String cpCShopTitle;

    private Integer isAutomerge;

    private Integer isAutochange;

    private String remark;

    private String ownerename;

    private String modifierename;

    private Long delid;

    private String delename;

    private String delname;

    private Date delTime;

    private Integer isMergerPresell11;

    private Integer isMergerPresell;

    private Integer isMergerLive;

    @ApiModelProperty(value = "可合并拆单分类(1.部分发货拆单 2.虚拟拆单 3.缺货拆单 4.按SKU拆单" +
            " 5.按SPU拆单 6.按品牌组拆 7.按性别拆 8.手工拆单 9.O2O拆单)")
    @JSONField(name = "MERGE_SPLIT_ORDER_TYPE")
    @Field(type = FieldType.Keyword)
    private String mergeSplitOrderType;

    @ApiModelProperty(value = "已审核订单是否可合并")
    @JSONField(name = "IS_REVIEWED_MERGE")
    @Field(type = FieldType.Keyword)
    private String isReviewedMerge;

    @ApiModelProperty(value = "品类限制:Y 是 N 否")
    @JSONField(name = "CATEGORY_LIMIT")
    @Field(type = FieldType.Keyword)
    private String categoryLimit;

    @ApiModelProperty(value = "排除订单业务类型")
    @JSONField(name = "ST_C_BUSINESS_TYPE_IDS")
    private String stCBusinessTypeIds;
}
