package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName NaiKaOrderQueryModel
 * @Description 奶卡管理
 * <AUTHOR>
 * @Date 2022/7/21 15:10
 * @Version 1.0
 */
@Data
public class NaiKaOrderQueryModel implements Serializable {

    @JSONField(name = "ID")
    private Long id;

    /**
     * 平台单号
     */
    @JSONField(name = "SOURCE_CODE")
    private String sourceCode;

    /**
     * 单据单号
     */
    @JSONField(name = "BILL_NO")
    private String billNo;

    /**
     * 订单状态
     */
    @JSONField(name = "ORDER_STATUS")
    private Integer orderStatus;

    /**
     * 订单状态
     */
    @JSONField(name = "ORDER_STATUS_NAME")
    private String orderStatusName;


    /**
     * 复制原因
     */
    @JSONField(name = "COPY_REASON")
    private String copyReason;

    /**
     * 订单业务类型
     */
    @JSONField(name = "BUSINESS_TYPE_ID")
    private Long businessTypeId;

    /**
     * 订单业务类型
     */
    @JSONField(name = "BUSINESS_TYPE_NAME")
    private String businessTypeName;

    /**
     * 同步奶卡状态
     */
    @JSONField(name = "TO_NAIKA_STATUS")
    private Integer toNaikaStatus;

    /**
     * 同步奶卡状态
     */
    @JSONField(name = "TO_NAIKA_STATUS_NAME")
    private String toNaikaStatusName;

    /**
     * 平台id
     */
    @JSONField(name = "PLATFORM")
    private Long platform;

    /**
     * 平台名称
     */
    @JSONField(name = "PLATFORM_NAME")
    private String platformName;

    /**
     * 下单店铺id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    /**
     * 店铺编码
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    /**
     * 下单店铺标题
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    /**
     * 商品数量
     */
    @JSONField(name = "QTY_ALL")
    private BigDecimal qtyAll;

    /**
     * 订单总额
     */
    @JSONField(name = "ORDER_AMT")
    private BigDecimal orderAmt;

    private Integer isResetShip;


}
