package com.jackrain.nea.oc.oms.model;

import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategy;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategyItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 残次策略信息
 *
 * <AUTHOR>
 */
@Data
public class StCImperfectStrategyInfo implements Serializable {

    private static final long serialVersionUID = 5711431906218348527L;

    private StCImperfectStrategy imperfectStrategy;
    private List<StCImperfectStrategyItem> imperfectStrategyItems;

}
