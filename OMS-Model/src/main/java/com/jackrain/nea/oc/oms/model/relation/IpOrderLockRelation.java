package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBOrderLock;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;

import java.util.List;

/**
 * @Descroption 淘宝锁单中间表关系
 * <AUTHOR>
 * @Date 2019/10/9 20:39
 */
@Data
public class IpOrderLockRelation {
    /**
     * 淘宝锁单中间表
     */
    private IpBOrderLock orderLock;

    /**
     * 全渠道订单表
     */
    private List<OcBOrder> ocBOrders;

    private String remarks;

    /**
     * 中间表订单ID
     */
    public long getOrderId() {
        if (orderLock != null) {
            return orderLock.getId();
        }
        return -1;
    }
}
