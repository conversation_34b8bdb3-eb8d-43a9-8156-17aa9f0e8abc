package com.jackrain.nea.oc.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName BnQueryForLogisticsProblemResult
 * @Description 物流查询
 * <AUTHOR>
 * @Date 2024/11/15 15:49
 * @Version 1.0
 */
@Data
public class BnQueryForLogisticsProblemResult implements Serializable {
    private static final long serialVersionUID = -5632858056371649154L;

    /**
     * 班牛仓库物流信息
     */
    private String bnWarehouseLogistics;

    /**
     * 班牛侧组件的id
     */
    private Long bnWarehouseLogisticsId;

    /**
     * 是否有班牛工单
     */
    private Integer hasBnTask;
}
