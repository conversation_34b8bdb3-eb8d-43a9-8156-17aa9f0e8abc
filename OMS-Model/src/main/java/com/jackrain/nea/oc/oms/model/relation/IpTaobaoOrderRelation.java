package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderCycleBuy;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderPromotion;
import lombok.Data;

import java.util.List;

/**
 * @author: 易邵峰
 * @since: 2019-01-25
 * create at : 2019-01-25 13:25
 */
@Data
public class IpTaobaoOrderRelation {

    private IpBTaobaoOrder taobaoOrder;

    private List<IpBTaobaoOrderItemEx> taobaoOrderItemList;

    private List<IpBTaobaoOrderPromotion> taobaoOrderPromotionList;

    /**
     * 周期购
     */
    private List<IpBTaobaoOrderCycleBuy> taobaoOrderCycleBuyList;

    public Long getOrderId() {
        if (taobaoOrder != null) {
            return taobaoOrder.getId();
        }
        return -1L;
    }

    public String getOrderNo() {
        if (taobaoOrder != null) {
            return taobaoOrder.getTid();
        }
        return "";
    }

}
