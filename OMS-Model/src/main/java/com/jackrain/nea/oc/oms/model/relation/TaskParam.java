package com.jackrain.nea.oc.oms.model.relation;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/15
 */
@Data
@Accessors(chain = true)
public class TaskParam implements Serializable {


    private static final long serialVersionUID = 5130325706218697108L;

    /**
     * node
     */
    private String node;

    /**
     * task table name
     */
    private String taskTableName;

    /**
     * task appoint condition column
     */
    private String taskStatusCol;

    /**
     * task query or update
     * value
     */
    private int taskStatusVal;

    /**
     * task appoint type
     */
    private String taskTypeCol;

    /**
     * task type val
     */
    private int taskTypeVal;

    /**
     * query size
     */
    private int limit;

    /**
     * origin order send the third system: send status column
     */
    private String origStatusCol;


    /**
     * origin order send the third system: send status column value
     */
    private String origStatusVal;

    /**
     * origin order send the third system: send times column
     */
    private String origSendTimesCol;

    /**
     * origin compensate Failed value
     */
    private int origCompensateFailedVal;

    /**
     * origin table
     */
    private String origTableName;

    /**
     * origin es
     */
    private String origEsIndex;

    private String origEsType;


    /**
     * shard keys
     */
    private List<Long> keys;

    /**
     * active shard keys
     */
    private List<Long> validKeys;

    /**
     * join shard key to string
     */
    private String keyStrings;

    /**
     * invalid shard keys
     *
     * @return list.long
     */
    public List<Long> getInvalidKeys() {
        this.keys.removeAll(this.validKeys);
        return this.keys;
    }


}
