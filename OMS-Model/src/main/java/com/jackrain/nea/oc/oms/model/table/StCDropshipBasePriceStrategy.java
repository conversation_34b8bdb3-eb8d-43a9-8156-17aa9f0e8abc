package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * 一件代发客户基价策略
 *
 * <AUTHOR>
 */
@Data
@TableName("st_c_dropship_base_price_strategy")
public class StCDropshipBasePriceStrategy extends BaseModel {

    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 策略编码
     */
    @JSONField(name = "STRATEGY_CODE")
    private String strategyCode;

    /**
     * 店铺id
     */
    @JSONField(name = "SHOP_ID")
    private Long shopId;

    /**
     * 配置的SKU数量
     */
    @JSONField(name = "SKU_COUNT")
    private Integer skuCount;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;

    /**
     * 审核状态（0:待审核，1:已审核，2:已反审核）
     */
    @JSONField(name = "AUDIT_STATUS")
    private Integer auditStatus;

    // 注意：需要在数据库表中添加以下字段
    // ALTER TABLE st_c_dropship_base_price_strategy ADD COLUMN audit_status int(11) DEFAULT 0 COMMENT '审核状态（0:待审核，1:已审核，2:已反审核）';

}
