package com.jackrain.nea.oc.oms.model;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 订单金额
 *
 * @author: 王帅
 * @since: 2020/4/15
 * create at : 2020/4/15 10:28
 */
@Data
@ToString
public class OrderMoney {
    // 商品优惠金额
    private BigDecimal productDiscountAmt = BigDecimal.ZERO;
    // 订单优惠金额
    private BigDecimal orderDiscountAmt = BigDecimal.ZERO;
    // 调整金额
    private BigDecimal adjustAmt = BigDecimal.ZERO;
    // 商品金额
    private BigDecimal productAmt = BigDecimal.ZERO;
    // 订单金额
    private BigDecimal orderAmt = BigDecimal.ZERO;
}
