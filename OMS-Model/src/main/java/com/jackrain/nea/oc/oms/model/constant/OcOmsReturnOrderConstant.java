package com.jackrain.nea.oc.oms.model.constant;

/**
 * Description： 常量类-退换货单
 * Author: RESET
 * Date: Created in 2020/7/6 16:12
 * Modified By:
 */
public class OcOmsReturnOrderConstant {

    /**
     * 零售退货单按钮操作
     * 1-从WMS撤回
     */
    public static final int OC_B_RETURN_ORDER_RETAIL_FROM_WMS_WITHDRAW = 1;
    public static final String OC_B_RETURN_ORDER_RETAIL_FROM_WMS_WITHDRAW_DESC = "从WMS撤回";

    /**
     * wms状态
     */
    /*** 未传 */
    public static final long WMS_STATUS_WAIT_PASS = 0;
    /*** 传中 */
    public static final long WMS_STATUS_PASSING = 1;
    /*** 传成功 */
    public static final long WMS_STATUS_PASS_SUCCESS = 2;
    /*** 传失败 */
    public static final long WMS_STATUS_PASS_FAILED = 3;


    /**
     * 退换货单 确认状态：0-未确认，1-已确认
     */
    public static final String CONFIRM_STATUS_Y = "1";
    public static final String CONFIRM_STATUS_N = "0";

    /**
     * SAP 平台编码
     */
    public static final Integer PLATFORM_SAP = 38;
    /**
     * DMS 平台编码
     */
    public static final Integer PLATFORM_DMS = 39;


    /**
     * 业务类型编码 SAP销售退货、SAP拒收退货
     */
    public static final String BUSINESS_TYPE_CODE_RYTH11 = "RYTH11";
    public static final String BUSINESS_TYPE_CODE_RYTH12 = "RYTH12";


    /**
     * 退换货单同步平台退款状态
     */
    public static final Integer PLATFORM_REFUND_STATUS_INIT = 0;
    public static final Integer PLATFORM_REFUND_STATUS_SUCCESS = 1;
    public static final Integer PLATFORM_REFUND_STATUS_FAIL = 2;



}