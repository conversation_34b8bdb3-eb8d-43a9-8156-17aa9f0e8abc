package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNotice;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticeItem;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticePro;
import lombok.Data;

import java.util.List;

/**
 * @author: chenxiulou
 * @description: 开票通知单 实体
 * @since: 2019-07-20
 * create at : 2019-07-20 11:48
 */
@Data
public class OcBInvoiceNoticeRelation {
    @JSONField(name = "OC_B_INVOICE_NOTICE")
    private OcBInvoiceNotice invoiceNotice;
    @JSONField(name = "OC_B_INVOICE_NOTICE_ITEM")
    private List<OcBInvoiceNoticeItem> invoiceNoticeItems;
    @JSONField(name = "OC_B_INVOICE_NOTICE_PRO")
    private List<OcBInvoiceNoticePro> invoiceNoticePros;
    private Long id;
    private List<Long> mergeIdList;

    /**
     * @return 获取当前处理单据的订单ID号
     */
    public String getBillNo() {
        return invoiceNotice.getBillNo();
    }

}
