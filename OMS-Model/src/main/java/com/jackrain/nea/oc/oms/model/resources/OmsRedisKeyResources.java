package com.jackrain.nea.oc.oms.model.resources;

/**
 * @author: heliu
 * @since: 2019/9/11
 * create at : 2019/9/11 17:12
 */
public class OmsRedisKeyResources {

    public static final String STORE = "store";

    public static final String WAREHOUSE = "warehouse";

    /**
     * 构造订单派单方案key
     *
     * @param shopId
     * @return
     */
    public static String buildLockShopSendPlanRedisKey(Long shopId) {
        return "st:send:plan:shopid:" + shopId;
    }

    /**
     * 构造订单派单方案IDkey-店铺下所有派单方案ID
     *
     * @param shopId
     * @return
     */
    public static String bulidLockStSendPlanIdKey(Long shopId) {
        return "st:StCSendPlanId:byShopId:" + shopId;
    }

    /**
     * 构造订单派单规则key
     *
     * @param sendPlanId 方案id
     * @return
     */
    public static String buildLockSendPlanRuleRedisKey(Long sendPlanId) {
        return "st:send:rule:planid:" + sendPlanId;
    }

    /**
     * 构造订单派单规则IDkey-派单方案下所有派单规则ID
     *
     * @param sendPlanId 方案id
     * @return
     */
    public static String bulidLockStSendPlanRuleIdKey(Long sendPlanId) {
        return "st:StCSendRuleId:byStCSendPlanId:" + sendPlanId;
    }

    /**
     * 构造订单派单规则ID-派单规则
     *
     * @param sendRuleId 派单规则ID
     * @return
     */
    public static String bulidLockStSendRuleKey(Long sendRuleId) {
        return "st:StCSendRule:byStCSendRuleId:" + sendRuleId;
    }

    /**
     * 构造店铺策略key
     *
     * @param shopId 店铺id
     * @return
     */
    public static String buildLockShopStrategyRedisKey(Long shopId) {
        return "st:shop:strategy:shopid:" + shopId;
    }

    /**
     * 构造查询所有店铺策略key
     *
     * @param
     * @return
     */
    public static String bulidLockAllStCShopStrategyKey() {
        return "st:StCShopStrategyAll";
    }


    /**
     * JITX仓库对照表
     *
     * @param phyWarehouseId 实体仓ID
     * @return 返回值
     */
    public static String buildStJitxWarehouseRedisKey(Long shopId, Long phyWarehouseId) {
        return "st:jitx:warehouse:" + phyWarehouseId + ":" + shopId;
    }
    public static String buildStJitxWarehouseRedisKeyByShopIdAndCode(Long shopId, String code,String type) {
        return "st:jitx:warehouse:" + type + ":" + code + ":" + shopId;
    }

    /**
     * JITX仓库对照表
     *
     * @param warehouseCode 仓库编码
     * @return 返回值
     */
    public static String buildStJitxWarehouseRedisKey(Long shopId, String warehouseCode) {
        return "st:jitx:warehouse:" + warehouseCode + ":" + shopId;
    }


    /**
     * 构造派单规则key-地址就近规则Key
     *
     * @param sendRuleId 派单规则id
     * @return
     */
    public static String buildLockStCSendRuleAddressRankKey(Long sendRuleId, Long regionProvinceId) {
        return "st:send:rule:address:rent:type1:ruleid:" + sendRuleId + "region:" + regionProvinceId;
    }

    /**
     * 构造派单规则中分类为唯品会的key
     *
     * @param sendRuleId 派单规则id
     * @return
     */
    public static String bulidLockStCSendRuleAddressVipKey(Long sendRuleId, Long cpCVipcomWahouseId) {
        return "st:StCSendRuleAddressRent:type3:byStCSendrule:" + sendRuleId + "wahouse:" + cpCVipcomWahouseId;
    }

    /**
     * 店铺策略明细表key
     *
     * @param shopId 店铺Id
     * @return
     */
    public static String bulidLockStCShopStrategyItemKey(Long shopId) {
        return "st:StCShopStrategyItem:byShopId:" + shopId;
    }

    /**
     * 店铺同步库存策略关系明细表key
     *
     * @param shopId 店铺Id
     * @return
     */
    public static String buildLockSyncStockStrategyItemRedisKey(Long shopId) {
        return "st:sync:stock:strategyitem:shopid:" + shopId;
    }

    /**
     * 店铺所有同步库存策略关系表key
     *
     * @param
     * @return
     */
    public static String bulidLockAllStCSyncStockStrategyKey() {
        return "st:stCSyncStockStrategyAll";
    }

    /**
     * 刷单策略redisKey
     *
     * @param shopId
     * @param keyWord
     * @return
     */
    public static String buildScalpingRedisKey(Long shopId, String keyWord) {
        if (null == keyWord) {
            keyWord = "";
        }
        return "st:queryScalpingList:byShopId:keyword:" + shopId + ":" + keyWord.hashCode();
    }

    /**
     * 获取所有店铺审核策略redisKey
     *
     * @return
     */
    public static String buildAutoCheckAllListRedisKey() {

        return "st:autoCheck:all";
    }

    /**
     * 自动审核策略redisKey
     *
     * @param shopId
     * @return
     */
    public static String bulidLockStCAutoCheckKey(Long shopId) {
        return "st:autocheck:shopid:" + shopId;
    }


    /**
     * 根据买家昵称、店铺ID创建Key
     *
     * @param buyerNick
     * @return
     */
    public static String buildVipMemberByNickAndShopId(String buyerNick, long shopId) {
        return "vp:member:" + shopId + ":" + buyerNick.hashCode();
    }

    /**
     * 根据店铺ID、会员等级创建Key
     *
     * @param shopId
     * @return
     */
    public static String buildOrderUrgentStrategy(long shopId, int vipLevel) {
        return "st:order:urgency:" + shopId + ":" + vipLevel;
    }


    /**
     * 省市区对应物流公司列表redisKey
     *
     * @return
     */
    public static String bulidLockStExpressAreaRequestKey() {
        return "st:expressAreaRequest";
    }

    /**
     * 实体仓对应的仓库物流公司明细redisKey
     *
     * @return
     */
    public static String bulidLockStExpressAllocationItemKey(Long phyWarehouseId) {
        return "st:stCExpressAllocationItem:byPhyWarehouseId:" + phyWarehouseId;
    }

    /**
     * 所有有效方案【已审核】redisKey
     *
     * @return
     */
    public static String bulidLockStActiveExpressKey() {
        return "st:stCExpressList";
    }

    /**
     * 查询物流方案对应的仓库明细数据redisKey
     *
     * @return
     */
    public static String bulidLockStExpressWarehouseItemKey(Long expressId) {
        return "st:stCExpressWarehouseItem:byExpressId:" + expressId;
    }

    /**
     * 查询物流方案对应的商品明细数据redisKey
     *
     * @return
     */
    public static String bulidLockStExpressProItemKey(Long expressId) {
        return "st:stCExpressProItem:byExpressId:" + expressId;
    }

    /**
     * 查询物流方案对应的区域明细数据redisKey
     *
     * @return
     */
    public static String bulidLockStExpressPlanAreaItemKey(Long expressId) {
        return "st:stCExpressPlanAreaItem:byExpressId:" + expressId;
    }

    /**
     * 查询物流方案对应的包裹明细数据redisKey
     *
     * @return
     */
    public static String bulidLockStExpressPackageKey(Long expressId) {
        return "st:stCExpressPackage:byExpressId:" + expressId;
    }

    /**
     * 查询实体仓对应的物流公司省市区及其优先级包裹明细数据redisKey
     *
     * @return
     */
    public static String bulidLockStWarehouseLogisticsRankKey() {
        return "st:stCWarehouseLogisticsRank";
    }

    /**
     * 查询发货仓库 + 物流公司在【物流分配比例设置】中的限制数量
     *
     * @return
     */
    public static String bulidLockStWarehouseQueryPriceListKey() {
        return "st:stCWarehouseQueryPriceList";
    }

    /**
     * 判断“物流公司”是否在订单中“发货仓库”对应的【仓库物流规则】中存在且启用
     *
     * @return
     */
    public static String bulidLockStQueryLogisticsRuleKey() {
        return "st:stCWarehouseQueryLogisticsRule";
    }

    /**
     * 推单延迟策略key
     *
     * @param shopId 店铺id
     * @return
     */
    public static String buildLockStrderPushDelayRedisKey(Long shopId) {
        return "st:stCOrderPushDelayStrategy:byShopId:" + shopId;
    }

    /**
     * 获取所有店铺合单策略redisKey
     *
     * @return
     */
    public static String buildAutoMergeAllListRedisKey() {
        return "st:automerge:all:";
    }

    /**
     * 获取所有店铺合单策略redisKey
     *
     * @return
     */
    public static String buildNewAutoMergeAllListRedisKey() {
        return "st:automerge:all-new:";
    }

    /**
     * 自动合单策略redisKey
     *
     * @return
     */
    public static String buildLockStCAutoMergeKey(Long shopId) {
        return "st:automerge:shopid:" + shopId;
    }


    public static String buildExchangeAllListRedisKey() {
        return "st:exchange:strategy:all:";
    }


    public static String buildExpressByBansKey() {
        return "st:bans:express";
    }

    public static String buildExpressByWarehouseKey(long warehouseId) {
        return "st:warehouse:express:warehouseId:" + warehouseId;
    }

    public static String buildExpressByShopKey(long shopId) {
        return "st:shop:express:shopId:" + shopId;
    }

    /**
     * 分物流规则明细
     *
     * @param parentId 分物流规则Id
     * @return
     */
    public static String buildPsDimIdRedisKey(long parentId) {
        return "ps:pro:dim:item:parentId:" + parentId;
    }

    public static String buildExpressPrice(long warehouseId) {
        return "st:express:price:" + warehouseId;
    }

    /**
     * 根据商品编码查已审核周期购策略redisKey
     * @param proCode 商品编码
     * @return key
     */
    public static String buildCyclePurchaseStrategyProCodeRedisKey(String proCode) {
        return "st:cyclePurchaseStrategy:proCode:" + proCode;
    }
    /**
     * 根据策略ID查周期购策略redisKey
     * @param id 策略ID
     * @return key
     */
    public static String buildCyclePurchaseStrategyIdRedisKey(Long id) {
        return "st:cyclePurchaseStrategy:id:" + id;
    }

    /**
     * 指定店铺启用的残次策略缓存
     *
     * @param shopId
     * @return
     */
    public static String buildStCImperfectStrategyRedisKey(Long shopId) {
        return "st:ccImperfectStrategy:shopId:" + shopId;
    }

    /**
     * 指定店铺启用的卖家备注送赠品策略缓存
     *
     * @param shopId
     * @return
     */
    public static String buildStCRemarkGiftStrategyRedisKey(Long shopId) {
        return "st:remarkGiftStrategy:shopId:" + shopId;
    }

}