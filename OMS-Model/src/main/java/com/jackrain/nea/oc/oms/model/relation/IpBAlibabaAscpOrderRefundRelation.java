package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefund;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefundItem;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Descroption 猫超直发退款中间表关系
 * <AUTHOR>
 * @Date 2020/09/05 20:39
 */
@Data
public class IpBAlibabaAscpOrderRefundRelation {
    /**
     * 猫超直发退单中间表
     */
    private IpBAlibabaAscpOrderRefund orderRefund;
    /**
     * 主表对应明细，分库键是ipBAlibabaAscpOrderId
     */
    private List<IpBAlibabaAscpOrderRefundItem> ipBAlibabaAscpOrderRefundItems;

    private List<OmsOrderRelation> omsOrderRelation;

    /**
     * 中间表订单ID
     */
    public long getOrderId() {
        if (orderRefund != null) {
            return orderRefund.getId();
        }
        return -1;
    }

    /**
     * 中间表平台单号
     */
    public String getOrderNo() {
        if (orderRefund != null) {
            return orderRefund.getBizOrderCode();
        }
        return "";
    }

    /**
     * 是否是整单退,根据字表上的subOrderCode去区分
     *
     * @return
     */
    public boolean isFullRefund() {
        if (CollectionUtils.isNotEmpty(ipBAlibabaAscpOrderRefundItems)) {
            return ipBAlibabaAscpOrderRefundItems.stream().filter(item -> item.getSubOrderCode() != null)
                    .map(IpBAlibabaAscpOrderRefundItem::getSubOrderCode).collect(Collectors.toList()).isEmpty();
        }
        return true;
    }
}
