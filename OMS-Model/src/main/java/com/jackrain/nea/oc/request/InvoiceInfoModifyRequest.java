package com.jackrain.nea.oc.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 修改开票信息
 *
 * <AUTHOR>
 */
@Data
public class InvoiceInfoModifyRequest implements Serializable {

    private static final long serialVersionUID = -4228753842385633967L;

    /**
     * 发票单id
     */
    @NotNull(message = "发票单id不能为空")
    @JSONField(name = "id")
    private Long id;

    /**
     * 发票抬头
     */
    @NotNull(message = "发票抬头不能为空")
    @JSONField(name = "INVOICE_HEADER")
    private String invoiceHeader;

    /**
     * 纳税人识别号
     */
    @NotNull(message = "纳税人识别号不能为空")
    @JSONField(name = "TAXPAYER_NO")
    private String taxpayerNo;

}
