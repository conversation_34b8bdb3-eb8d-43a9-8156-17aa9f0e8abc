package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR> 孙勇生
 * create at:  2019/4/3  12:43
 * @description: 合并订单分组信息
 */
@Data
public class MergeOderGroups {
    /**
     * 仓库id
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;
    /**
     * 平台
     */
    @JSONField(name = "PLATFORM")
    private Integer platform;
    /**
     * 平台店铺
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;
    /**
     * 支付方式
     */
    @JSONField(name = "PAY_TYPE")
    private Integer payType;
    /**
     * 收货人
     */
    @JSONField(name = "RECEIVER_NAME")
    private String receiverName;
    /**
     * 收货人手机
     */
    @JSONField(name = "RECEIVER_MOBILE")
    private String receiverMobile;
    /**
     * 收货人电话
     */
    @JSONField(name = "RECEIVER_PHONE")
    private String receiverPhone;
    /**
     * 收货人所在省
     */
    @JSONField(name = "CP_C_REGION_PROVINCE_ID")
    private Long cpCregionProvinceId;
    /**
     * 收货人所在市
     */
    @JSONField(name = "CP_C_REGION_CITY_ID")
    private Long cpCregionCityId;
    /**
     * 收货人所在区
     */
    @JSONField(name = "CP_C_REGION_AREA_ID")
    private Long cpCregionAreaId;
    /**
     * 收货人详细地址的订单
     */
    @JSONField(name = "RECEIVER_ADDRESS")
    private String receiverAddress;


    @JSONField(name = "ORDER_TYPE")
    private Integer orderType;

    @JSONField(name = "USER_NICK")
    private String userNick;

    /**
     * 订单合单加密信息
     */
    @JSONField(name = "ORDER_ENCRYPTION_CODE")
    private String orderEncryptionCode;
}
