package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.request.OmsRefundInSaveRequest;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInActualItem;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/7/18
 */
@Data
public class OcBRefundInRelation {

    private String wmsBilNo;

    private Long refundInId;

    private OcBRefundIn ocBRefundIn;

    private List<OcBRefundInProductItem> productItems;

    private List<OcBRefundInActualItem> actualItems;

    private OmsRefundInSaveRequest request;

    public void addProductItem(OcBRefundInProductItem item) {
        this.productItems.add(item);
    }

    public void addActualItem(OcBRefundInProductItem item) {
        if (this.actualItems == null) {
            this.actualItems = new ArrayList<>();
        }
        OcBRefundInActualItem actualItem = new OcBRefundInActualItem();
        BeanUtils.copyProperties(item, actualItem);
        this.actualItems.add(actualItem);
    }

    public OcBRefundInRelation() {
        this.productItems = new ArrayList<>();
    }


}


