package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.*;
import lombok.Data;

import java.util.List;

/**
 * 通用商品通用 退单 中间关系
 *
 * @author: 夏继超
 * @since: 2019/7/13
 * create at : 2019/7/13 15:27
 */
@Data
public class IpStandplatRefundRelation {
    /**
     * 通用商品退单中间表主表
     */
    private IpBStandplatRefund ipBStandplatRefund;
    /**
     * 通用商品退单中间表明细表
     */
    private List<IpBStandplatRefundItem> ipBStandplatRefundItem;
    /**
     * 退换货订单表
     */
    private OcBReturnOrder ocReturnOrder;
    /**
     * 通用订单中间表
     */
    private IpBStandplatOrder ipBStandplatOrder;

    /**
     * 全渠道订单明细
     */
    private List<OcBOrderItem> ocBOrderItems;

    /**
     * 全渠道订单表
     */
    private OcBOrder ocBOrder;

    /**
     * 满足条件得订单集合
     */
    private List<OcBOrder> ocBOrders;

    /**
     * 退款金额是否大于实际成交价
     */
    private Boolean isPrice;
    /**
     * 赠品集合
     */
    private List<OcBOrderItem> hasGiftList;
    /**
     * 组合商品明细集合
     */
    private List<OcBOrderItem> combinationList;
    /**
     * 不是组合商品的明细集合
     */
    private List<OcBOrderItem> noCombinationList;
    /**
     * 是否退货
     */
    private Boolean isReturn;
    /**
     * 退换货中是否存在已经生成过得赠品
     */
    private Boolean isHaveGift;

    /**
     * 中间表订单ID
     */
    public long getOrderId() {
        if (ipBStandplatRefund != null) {
            return ipBStandplatRefund.getId();
        }
        return -1;
    }

    /**
     * 中间表平台单号
     */
    public String getOrderNo() {
        if (ipBStandplatOrder != null) {
            return ipBStandplatRefund.getReturnNo();
        }
        return "";
    }
}
