//package com.jackrain.nea.oc.oms.resource;
//
///**
// * @author: 易邵峰
// * @since: 2019-03-14
// * create at : 2019-03-14 18:30
// */
//public class OMSBllElasticIndexResources {
//
//    public static final String OC_B_ORDER_INDEX_NAME = "oc_b_order";
//
//    public static final String OC_B_ORDER_TYPE_NAME = "oc_b_order";
//
//    public static final String OC_B_ORDER_ITEM_TYPE_NAME = "oc_b_order_item";
//
//    public static final String OC_B_ORDER_PARENT_FIELD_NAME = "ocBOrderId";
//
//    public static final String OC_B_ORDER_PAYMENT_TYPE_NAME = "oc_b_order_payment";
//
//    public static final String OC_B_ORDER_PROMOTION_TYPE_NAME = "oc_b_order_promotion";
//
//    public static final String OC_B_ORDER_TAOBAO_TYPE_NAME = "oc_b_order_taobao";
//
//
//    public static String buildElasticIndexName(String indexName) {
//        return indexName;
//    }
//
//}
