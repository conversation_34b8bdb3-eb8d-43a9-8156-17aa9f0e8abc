package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.GiftInfo;
import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedReason;
import com.jackrain.nea.oc.oms.model.enums.OmsMethod;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLink;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromotion;
import com.jackrain.nea.oc.oms.model.table.OcBOrderTaobao;
import com.jackrain.nea.st.model.StCAutoCheck;
import com.jackrain.nea.st.model.StCAutoCheckAutoTime;
import com.jackrain.nea.st.model.StCAutoCheckExcludeProduct;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 全渠道订单关联关系类
 *
 * @author: 易邵峰
 * @since: 2019-01-25
 * create at : 2019-01-25 13:20
 */
@Data
public class OcBOrderRelation {
    /**
     * 订单信息
     */
    private OcBOrder orderInfo;

    private List<OcBOrderItem> orderItemList;

    private List<OcBOrderNaiKa> orderNaiKaList;

    private List<OcBOrderPromotion> orderPromotionList;

    private List<OcBOrderPayment> orderPaymentList;

    private OcBOrderTaobao orderTaobao;

    private OcBOrderLink orderLink;

    private Long shareStoreId;

    private String shareStoreEcode;

    /**
     * 虚拟条码
     */
    private List<String> virtualSkuCodeList;

    /**
     * 保存拆单规则key 类型 key 明细id, val携带给主表的参数
     */
    private Map<Integer, Map<Set<Long>, SpiltOrderParam>> spiltRule = new HashMap<>();

    private Map<Long, Long> itemIdMap = new HashMap<>();

    /**
     * 审核时查询策略使用
     */
    private StCAutoCheck stCAutoCheck;

    private List<StCAutoCheckAutoTime> autoCheckAutoTimes;

    private List<StCAutoCheckExcludeProduct> autoCheckExcludeProducts;

    private List<OcBOrderItem> noRefundOrderItems;

    private List<GiftInfo> giftInfoList;

    private OmsMethod omsMethod;

    /**
     * 因为赠品不可拆单 导致原本不卡单的 被设置成卡单集合(用于后续再拆单逻辑中 如果新生成的订单明细是卡单 但是都是因为赠品不可拆单被打标 而不可拆单的赠品被拆出去。则订单不需要被设置成卡单的标识)
     */
    private List<Long> cardByGiftNotSplit;

    /**
     * 因为赠品卡单 导致有挂靠关系的主品也被卡住了
     */
    private List<Long> cardByGiftRelation;

    /**
     * 自动=true,手动=false
     */
    private boolean automaticOperation = true;

    // 审核失败原因
    private OmsAuditFailedReason omsAuditFailedReason;

    private List<OcBOrderEqualExchangeItem> exchangeItems;

    private List<OcBOrderItemExt> ocBOrderItemExtList;

    /**
     * 订单发货单id
     */
    private Long deliveryOrderId;


    /**
     * 订单Id
     *
     * @return
     */
    public Long getOrderId() {
        if (orderInfo != null) {
            return orderInfo.getId();
        }
        return -1L;
    }

    /**
     * 订单tid
     *
     * @return
     */
    public String getOrderTid() {
        if (orderInfo != null) {
            return orderInfo.getTid();
        }
        return "";
    }

    public boolean isPos(){
        return checkPlatform(PlatFormEnum.POS);
    }

    private boolean checkPlatform(PlatFormEnum platFormEnum){
        if(null == orderInfo){
            return false;
        }
        Integer platformId = orderInfo.getPlatform();
        platformId = Objects.isNull(platformId) ? -1 : platformId;

        return platformId.equals(platFormEnum.getCode());
    }

    /**
     * 是否强制审核
     */
    private Boolean mandatoryAudit = false;

    /**
     * 缺货不还原明细ids
     */
    private List<Long> restoreIds;

}
