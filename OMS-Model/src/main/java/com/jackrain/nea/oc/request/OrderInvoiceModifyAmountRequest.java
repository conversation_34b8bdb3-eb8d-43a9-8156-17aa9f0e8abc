package com.jackrain.nea.oc.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName OrderInvoiceModifyAmountRequest
 * @Description
 * @Date 2022/9/21 下午5:07
 * @Created by wuhang
 */
@Data
public class OrderInvoiceModifyAmountRequest implements Serializable {

    /** 订单发票id */
    @NotNull(message = "发票id不能为空")
    private Long id;

    @NotNull(message = "发票明细不能为空")
    private List<OrderInvoiceModifyAmountItem> items;

    private String amountMoreThanBeforeFlag;

    @NoArgsConstructor
    @Data
    public static class OrderInvoiceModifyAmountItem{
        /** 发票明细的id */
        private Long itemId;

        /** 修改后的金额 */
        private BigDecimal amount;
    }
}
