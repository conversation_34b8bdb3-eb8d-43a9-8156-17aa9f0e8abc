package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 1:54 下午
 * @Version 1.0
 */
@Data
public class OmsTaobaoRefundRelation {

    /**
     * 淘宝退单中间表
     */
    private IpBTaobaoRefund ipBTaobaoRefund;

    /**
     * 订单中间表表数据
     */
    private IpBTaobaoOrder ipBTaobaoOrder;

    /**
     * 对应的订单信息
     */
    private List<OmsOrderRelation> omsOrderRelation;

    /**
     * 对应的赠品订单信息
     */
    private List<OmsOrderRelation> isGiftOrderRelation;

    /**
     * 没拆单的订单 平台只取消赠品
     */
    private List<OmsOrderRelation> giftItemRelation;

    /**
     *
     */
    private IntermediateTableRelation intermediateTableRelation;

    /**
     * 天猫周期购需要售后的期数
     */
    private List<Integer> cycleBuyCurrPhases;

    /**
     * 是否天猫周期购售后单
     */
    private Boolean cycleBuy;

    /**
     * @return
     */
    public long getOrderId() {
        if (ipBTaobaoRefund != null) {
            return ipBTaobaoRefund.getId();
        }
        return -1;
    }

    /**
     * @return
     */
    public String getOrderNo() {
        if (ipBTaobaoRefund != null) {
            return ipBTaobaoRefund.getRefundId() + "";
        }
        return "";
    }

    /**
     * 检查是否存在发货单
     *
     * @return
     */
    public boolean isExistsDelivers() {
        if (CollectionUtils.isNotEmpty(omsOrderRelation)) {
            for (OmsOrderRelation relation : omsOrderRelation) {
                if (Objects.nonNull(relation) && CollectionUtils.isNotEmpty(relation.getOrderDeliveries())) {
                    return true;
                }
            }
        }


        return false;
    }


    /**
     * 判断订单中间表数据的订单状态是否为交易关闭 和 不存在原单
     *
     * @return
     */
    public boolean isTransactionClosure() {
        if (ipBTaobaoOrder == null) {
            return false;
        }
        String status = ipBTaobaoOrder.getStatus();
        Integer istrans = ipBTaobaoOrder.getIstrans();
        if (TaoBaoOrderStatus.TRADE_FINISHED.equals(status)
                && TransferOrderStatus.TRANSFERRED.toInteger() == istrans
                && CollectionUtils.isEmpty(omsOrderRelation)) {
            return true;
        }

        return (TaoBaoOrderStatus.TRADE_CLOSED.equals(status)
                || TaoBaoOrderStatus.TRADE_CLOSED_BY_TAOBAO.equals(status))
                && CollectionUtils.isEmpty(omsOrderRelation) && CollectionUtils.isEmpty(isGiftOrderRelation);
    }


    /**
     * 平台单号
     * @return
     */
    public String getTid() {
        if (ipBTaobaoRefund != null) {
            return ipBTaobaoRefund.getTid() + "";
        }
        return "";
    }
}
