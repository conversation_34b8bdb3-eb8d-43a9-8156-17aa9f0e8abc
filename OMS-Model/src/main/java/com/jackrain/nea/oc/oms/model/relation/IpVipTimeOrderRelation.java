package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBCancelTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 唯品会时效订单转单关系类
 * @Date 2019-8-19
 **/
@Data
public class IpVipTimeOrderRelation {

    /**
     * 时效订单表
     **/
    private IpBTimeOrderVip ipBTimeOrderVip;
    /**
     * 时效订单子表拓展
     **/
    private List<IpBTimeOrderVipItemEx> ipBTimeOrderVipItemExList;
    /**
     * 时效订单库存占用明细表
     **/
    private List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipOccupyItemList;
    /**
     * 取消时效订单
     **/
    private IpBCancelTimeOrderVip cancelTimeOrderVip;
    /**
     * 发货实体仓ID
     **/
    private Long cphyWarehouseId;

    /**
     * 获取当前处理单据的订单号
     */
    public String getOrderSn() {
        if (ipBTimeOrderVip != null) {
            return ipBTimeOrderVip.getOrderSn();
        }
        return "";
    }

    /**
     * 获取当前处理单据的订单ID
     */
    public Long getOrderId() {
        if (ipBTimeOrderVip != null) {
            return ipBTimeOrderVip.getId();
        }
        return -1L;
    }

    /**
     * 获取库存占用订单号
     **/
    public String getOccupiedOrderSn() {
        if (ipBTimeOrderVip != null) {
            return ipBTimeOrderVip.getOccupiedOrderSn();
        }
        return "";
    }

    /**
     * 获取单据编号
     **/
    public String getBillNo() {
        if (ipBTimeOrderVip != null) {
            return ipBTimeOrderVip.getBillNo();
        }
        return "";
    }

    /**
     * 获取订单根单号
     **/
    public String getRootOrderSn() {
        if (ipBTimeOrderVip != null) {
            return ipBTimeOrderVip.getRootOrderSn();
        }
        return "";
    }


}
