package com.jackrain.nea.oc.oms.model.jingdong;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-05-22
 * create at : 2019-05-22 3:37 PM
 * 京东优惠信息表-优惠类型
 */
public interface JingdongCouponType {

    String TWENTY_COUPON = "20-套装优惠";
    String TWENTY_EIGHT_COUPON = "28-闪团优惠";
    String TWENTY_NINE_COUPON = "29-团购优惠";
    String THIRTY_COUPON = "30-单品促销优惠";
    String THIRTY_FOUR_COUPON = "34-手机红包";
    String THIRTY_FIVE_COUPON = "35-满返满送(返现)";
    String THIRTY_NINE_COUPON = "39-京豆优惠";
    String FORTY_ONE_COUPON = "41-京东券优惠";
    String FIFTY_TWO_COUPON = "52-礼品卡优惠";
    String ONE_HUNDRED_COUPON = "100-店铺优惠";
    String ONE_SEVEN_ONE_COUPON = "171-店铺首购礼金";
    String THREE_SIX_COUPON = "36-plus会员95折优惠";
    String ONE_SIX_EIGHT_COUPON = "168-定期购满期赠";
    String ONE_SIX_TWO_COUPON = "162-大促跨店满减";

}
