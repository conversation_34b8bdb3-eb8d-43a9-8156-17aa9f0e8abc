package com.jackrain.nea.oc.oms.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MeatRefundOrderImportDTO {

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 平台订单号
     */
    private String platformOrderNo;

    /**
     * 退款单号
     */
    private String refundNo;

    /**
     * 入库时间
     */
    private Date inboundTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 物流编码
     */
    private String logisticsCode;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款类型
     */
    private String refundType;

    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款创建时间
     */
    private Date refundCreateTime;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 入库数量
     */
    private BigDecimal inboundQty;

    /**
     * 申请数量
     */
    private BigDecimal applyQty;

    /**
     * 可退数量
     */
    private BigDecimal refundableQty;

    /**
     * 仓库
     */
    private String warehouse;
}