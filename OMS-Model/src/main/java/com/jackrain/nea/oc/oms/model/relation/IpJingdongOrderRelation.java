package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-24
 * create at : 2019-04-24 4:00 PM
 */
@Data
public class IpJingdongOrderRelation {

    /**
     * 京东中间表主表
     */
    private IpBJingdongOrder jingdongOrder;

    /**
     * 主表对应明细，分库键是order_id
     */
    private List<IpBJingdongOrderItemExt> jingdongOrderItems;

    /**
     * 京东订单优惠子表
     */
    private List<IpBJingdongCoupondtai> jingdongCoupondtaiList;

    /**
     * 京东用户信息
     */
    private IpBJingdongUser jingdongUser;

    /**
     * 全渠道订单主表
     */
    private OcBOrder ocBOrder;

    /**
     * 全渠道订单明细
     */
    private List<OcBOrderItem> ocBOrderItems;

    /**
     * 全渠道订单付款信息表
     */
    private OcBOrderPayment ocBOrderPayment;

    /**
     * 全渠道订单优惠信息表
     */
    private List<OcBOrderPromotion> ocBOrderPromotionList;

    /*
     *订单日志表
     */
    private OcBOrderLog ocBOrderLog;

    /**
     * 拆单后会有多个订单
     */
    private List<OcBOrder> ocBOrderList;

    /**
     * 是否为换货单
     */

    private Boolean isExchange=false;

    /**
     * @return 获取当前处理单据的订单ID号
     */
    public Long getOrderId() {
        if (jingdongOrder != null) {
            return jingdongOrder.getId();
        }
        return -1L;
    }

    /**
     * @return 获取当前处理单据的订单ID号
     */
    public Long getOrderNo() {
        if (jingdongOrder != null) {
            return jingdongOrder.getOrderId();
        }
        return -1L;
    }

}
