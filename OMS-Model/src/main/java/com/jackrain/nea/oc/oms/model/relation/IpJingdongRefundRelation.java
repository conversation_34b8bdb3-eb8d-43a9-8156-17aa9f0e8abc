package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.*;
import lombok.Data;

import java.util.List;

/**
 * @Descroption 京东退款中间表关系
 * <AUTHOR>
 * @Date 2019/4/25 20:39
 */
@Data
public class IpJingdongRefundRelation {
    /**
     * 京东退单中间表
     */
    private IpBJingdongRefund jingdongRefund;
    /**
     * 退换货订单表
     */
    private OcBReturnOrder ocReturnOrder;
    /**
     * 京东订单中间表
     */
    private IpBJingdongOrder ipJingdongOrder;

    /**
     * 全渠道订单明细
     */
    private List<OcBOrderItem> ocBOrderItems;

    /**
     * 京东订单中间明细扩展表
     */
    private IpBJingdongOrderItemExt ipBJingdongOrderItemExt;

    /**
     * 全渠道订单表
     */
    private OcBOrder ocBOrder;

    /**
     * 全渠道订单赠品明细
     */
    private List<OcBOrderItem> giftItemList;

    /**
     * 是否奶卡订单
     */
    private Boolean milkCardOrder = false;

    /**
     * 中间表订单ID
     */
    public long getOrderId() {
        if (jingdongRefund != null) {
            return jingdongRefund.getId();
        }
        return -1;
    }

    /**
     * 中间表平台单号
     */
    public String getOrderNo() {
        if (jingdongRefund != null) {
            return jingdongRefund.getOrderid();
        }
        return "";
    }
}
