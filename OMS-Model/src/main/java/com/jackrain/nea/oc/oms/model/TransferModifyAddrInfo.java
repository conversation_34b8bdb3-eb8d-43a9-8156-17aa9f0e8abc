package com.jackrain.nea.oc.oms.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: 黄世新
 * @Date: 2022/10/13 上午10:22
 * @Version 1.0
 */
@Data
public class TransferModifyAddrInfo implements Serializable {

    /**
     * 收货人的邮编
     */
    private String receiverZip;

    /**
     * 详细地址
     */
    private String receiverAddress;

    /**
     * 区
     */
    private String receiverDistrict;

    /**
     * 市
     */
    private String receiverCity;

    /**
     * 省
     */
    private String receiverState;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人的电话号码
     */
    private String receiverPhone;

    /**
     * 收货人的手机号码
     */
    private String receiverMobile;

    /**
     * 买家邮箱
     */
    private String buyerEmail;


    private String oaid;
}
