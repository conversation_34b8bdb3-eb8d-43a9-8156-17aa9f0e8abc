package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/1 下午3:29
 * @describe :
 */

@Data
public class OcBOrderToBManualSourcingBatchRequest implements Serializable {

    private List<Long> idList;

    private Long cpCPhyWarehouseId;

    private Long cpCLogisticsId;

    private User user;

}
