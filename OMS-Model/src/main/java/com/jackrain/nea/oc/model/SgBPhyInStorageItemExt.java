package com.jackrain.nea.oc.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存扩展类
 *
 * @author: heliu
 * @since: 2019/7/5
 * create at : 2019/7/5 10:28
 */
@Data
public class SgBPhyInStorageItemExt implements Serializable {

    /**
     * 商品skucode
     */
    private String ps_c_sku_ecode;

    /**
     * 商品skuName
     */
    private String ps_c_sku_name;

    /**
     * 建议发货实体仓id
     */
    private Long advise_phy_warehouse_id;

    /**
     * 建议发货实体仓ecode
     */
    private String advise_phy_warehouse_ecode;

    /**
     * 建议发货实体仓ename
     */
    private String advise_phy_warehouse_ename;

    /**
     * 可占用库存总数
     */
    private BigDecimal total_qty_available;
}