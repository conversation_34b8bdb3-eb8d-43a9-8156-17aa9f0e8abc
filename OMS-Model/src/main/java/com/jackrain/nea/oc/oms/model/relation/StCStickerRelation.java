package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.StCSticker;
import com.jackrain.nea.oc.oms.model.table.StCStickerItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 贴纸策略关系类，包含贴纸策略主表和明细
 *
 * <AUTHOR>
 * @date 2023/11/30
 */
@Data
public class StCStickerRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 贴纸策略主表
     */
    private StCSticker stCSticker;

    /**
     * 贴纸策略明细列表
     */
    private List<StCStickerItem> stCStickerItems;

    public StCStickerRelation(StCSticker stCSticker, List<StCStickerItem> stCStickerItems) {
        this.stCSticker = stCSticker;
        this.stCStickerItems = stCStickerItems;
    }
}
