package com.jackrain.nea.oc.oms.model.relation;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 基础权限
 *
 * @author: xiWen.z
 * create at: 2019/8/26 0026
 */
@Data
public class BasePermission implements Serializable {

    private static final long serialVersionUID = -8972485661125492048L;

    /**
     * PS_C_BRAND_ID 品牌权限
     */
    private List<DataPermission> brandPermissions;

    /**
     * CP_C_MAIN_STORE_ID 店仓权限
     */
    private List<DataPermission> storePermissions;

    /**
     * CP_C_WAREHOUSE_ID 实体仓权限
     */
    private List<DataPermission> warehousePermissions;

    /**
     * CP_C_SHOP_PERMISSION_ID 销售渠道权限
     */
    private List<DataPermission> shopPermissions;

    /**
     * CP_C_LOGISTICS_ID 物流公司权限
     */
    private List<DataPermission> logisticsKeyPermissions;
}
