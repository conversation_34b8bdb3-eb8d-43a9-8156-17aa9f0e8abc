package com.jackrain.nea.oc.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/12/15 10:22
 */
@Data
public class OcBReturnOrderWarningRequest implements Serializable {

    private static final long serialVersionUID = 7762235470035008685L;

    @JSONField(name = "IDS")
    private List<Long> orderIds;

    /**
     * 1退款又发货 2退款未入库 3退款后换货发货
     */
    @JSONField(name = "WARNINGTYPE")
    private Integer warningType;

}
