package com.jackrain.nea.oc.request.sap;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * sap order/refund 取消
 *
 * <AUTHOR>
 */
@Data
public class SapCancelRequest {

    @JSONField(name = "VBELN")
    @ApiModelProperty(name = "SAP订单编号")
    private String verln;

    @JSONField(name = "ZSQDH_411T")
    @ApiModelProperty(name = "SAP退单编号")
    private String zsQdh;

    @ApiModelProperty(name = "item")
    private List<Item> item;

    @Data
    public static class Item {

        @JSONField(name = "POSNR")
        @ApiModelProperty(name = "行项目号")
        private Integer line;

        @JSONField(name = "MATNR")
        @ApiModelProperty(name = "物料")
        private String sku;

        @JSONField(name = "OCDQTY_BU")
        @ApiModelProperty(name = "关闭数量")
        private BigDecimal qty;

    }
}

