package com.jackrain.nea.oc.oms.model.Standplat;

/**
 * 通用平台原订单状态/原单状态
 *
 * @author: ming.fz
 * @since: 2019-07-02
 * create at : 2019-07-02 17:35
 */
public enum IpBStandplatOldOrderStatusEnum {


    WAIT_SELLER_SEND_GOODS("买家已付款，等待买家发货", "WAIT_SELLER_SEND_GOODS"),


    WAIT_BUYER_CONFIRM_GOODS("卖家已发货等待买家收货", "WAIT_BUYER_CONFIRM_GOODS"),

    TRADE_FINISHED("订单完成", "TRADE_FINISHED"),


    TEADE_CANCELED("订单取消", "TRADE_FINISHED");


    String key;
    String val;

    IpBStandplatOldOrderStatusEnum(String k, String v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }

}
