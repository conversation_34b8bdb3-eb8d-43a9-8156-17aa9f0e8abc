package com.jackrain.nea.oc.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2020-08-14
 * @desc 零售发货单操作日志推送参数
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OcBOrderLogPushRequest implements Serializable {
    private static final long serialVersionUID = -3556959778417321503L;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 日志类型
     */
    private String logType;
    /**
     * 日志内存
     */
    private String logMessage;
    /**
     * wos工单类型
     */
    private String wosWorkOrderType;
}
