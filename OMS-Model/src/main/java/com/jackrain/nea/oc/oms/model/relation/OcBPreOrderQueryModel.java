package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBPreOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName OcBPreOrderQueryModel
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/11/26 14:50
 * @Version 1.0
 */
@Data
public class OcBPreOrderQueryModel extends OcBPreOrder implements Serializable {

    @ApiModelProperty(value = "转换状态")
    @JSONField(name = "TRANSFER_STATUS_NAME")
    private String transferStatusName;

    @ApiModelProperty(value = "下单时间")
    @JSONField(name = "ORDER_DATE_STR")
    private String orderDateStr;

    @ApiModelProperty(value = "付款时间")
    @JSONField(name = "PAY_TIME_STR")
    private String payTimeStr;
}
