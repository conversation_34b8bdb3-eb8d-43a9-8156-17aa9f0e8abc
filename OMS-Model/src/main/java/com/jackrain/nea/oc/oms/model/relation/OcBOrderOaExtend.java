package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName OcBOrderOaExtend
 * @Description OA订单信息
 * <AUTHOR>
 * @Date 2024/1/25 09:04
 * @Version 1.0
 */
@Data
@Slf4j
public class OcBOrderOaExtend extends OcBOrder implements Serializable {
    private static final long serialVersionUID = -1395007118908814171L;

    private String desc;

    private List<OcBOrderItem> orderItemOaExtendList;

    private OcBToBeConfirmedTask ocBToBeConfirmedTask;

    private OcBOrderPayment ocBOrderPayment;


}
