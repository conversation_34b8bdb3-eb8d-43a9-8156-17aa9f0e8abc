package com.jackrain.nea.oc.oms.model.Standplat;

/**
 * 通用订单交易状态
 *
 * @author: ming.fz
 * @since: 2019-07-02
 * create at : 2019-07-02 17:35
 */
public enum IpBStandplatOrderStatusEnum {

    /**
     * 1、等待付款 2、预付款 3、待发货  4、已发货等待确认收货  5、交易完成  6、交易关闭  7、退款中
     */

    /**
     * 等待付款
     */
    STANDPLAT_ANPAID("等待付款", "1"),

    /**
     * 预付款
     */
    STANDPLAT_FRONT_PAID_FINAL_PAID("预付款", "2"),
    /**
     * 待发货
     */
    STANDPLAT_SUBSTITUTION("待发货", "3"),

    /**
     * 已发货等待确认收货
     */
    STANDPLAT_AWAIT_GOODS("已发货等待确认收货", "4"),

    /**
     * 交易完成
     */
    STANDPLAT_ACCOMPLISH("交易完成", "5"),

    /**
     * 交易关闭
     */
    STANDPLAT_CLOSE("交易关闭", "6"),

    /**
     * 退款中
     */
    STANDPLAT_IN_RETURN("退款中", "7");


    String key;
    String val;

    IpBStandplatOrderStatusEnum(String k, String v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }

}
