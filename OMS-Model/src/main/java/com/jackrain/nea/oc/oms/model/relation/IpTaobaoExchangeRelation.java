package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.ps.model.ProductSku;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR> 孙勇生
 * create at:  19/3/7  14:34
 * @description: 淘宝换货逻辑
 * 换货中间表通过
 */
@Data
public class IpTaobaoExchangeRelation {
    /**
     * 淘宝退单中间表
     */
    private IpBTaobaoExchange taobaoExchange;

    /**
     * 对应有效原单（多条取第一条）
     */
    private OcBOrder originalValidOrderInfo;

    /**
     * 对应的换货订单
     */
    private List<OcBOrder> afterExchangeOrder;

    /**
     * 对应原单明细
     */
    private List<OcBOrderItem> originalOrderItemList;

    /**
     * 对应装换后的主表信息
     */
    private OcBReturnOrder returnOrderInfo;

    /**
     * 是否为组合商品
     */
    private Boolean isGroupGoods = false;

    /**
     *   换货商品的sku(组合商品时多条)
     */
    private List<ProductSku> exchangeProductDetailList;

    /**
     * 换货商品的sku(组合商品时对应的是虚拟条码)
     */
    private String exchangeSku;

    /**
     * 是否为福袋商品
     */
    private Boolean isFortuneBag = false;

    /**
     * 重写get方法排序 防止循环修改死锁  ruan.gz 20200914
     */
    public List<OcBOrderItem> getOriginalOrderItemList() {
        if (CollectionUtils.isEmpty(originalOrderItemList)) {
            return new ArrayList<>();
        }
        return sortItem(originalOrderItemList);
    }

    private List<OcBOrderItem> sortItem(List<OcBOrderItem> originalOrderItemList) {
        originalOrderItemList.sort(Comparator.comparing(OcBOrderItem::getId));
        return originalOrderItemList;
    }

    /**
     * @return
     */
    public long getOrderId() {
        if (taobaoExchange != null) {
            return taobaoExchange.getId();
        }
        return -1;
    }

    /**
     * @return
     */
    public String getOrderNo() {
        if (taobaoExchange != null) {
            return taobaoExchange.getDisputeId() + "";
        }
        return "";
    }


}
