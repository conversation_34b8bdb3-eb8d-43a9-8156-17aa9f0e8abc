package com.jackrain.nea.oc.oms.extmodel;

import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 14:32
 **/
@Data
public class ExtOcBReturnOrderExchange extends OcBReturnOrderExchange {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    //行号
    private int rowNum;
    //头子表关联列
    private String BK;

    /**
     * 导入生成模型
     *
     * @return
     */
    public static ExtOcBReturnOrderExchange importCreate(int index, ExtOcBReturnOrderExchange exchange, Map<String, String> columnMap) {
        try {
            exchange.setPsCSkuEcode(columnMap.get(rowStr + index + cellStr + 0));
        } catch (Exception e) {

        }
        try {
            exchange.setQtyExchange(new BigDecimal(columnMap.get(rowStr + index + cellStr + 1)));
        } catch (Exception e) {

        }
        try {
            exchange.setBK(columnMap.get(rowStr + index + cellStr + 2));
        } catch (Exception e) {

        }
        exchange.setRowNum(index + 1);
        return exchange;
    }

}
