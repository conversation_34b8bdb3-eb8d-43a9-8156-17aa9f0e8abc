package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName OmsNaiKaReturnDetailModel
 * @Description 奶卡管理详情
 * <AUTHOR>
 * @Date 2022/7/25 20:40
 * @Version 1.0
 */
@Data
public class OmsNaiKaReturnDetailModel implements Serializable {

    @JSONField(name = "ID")
    private Long id;

    /**
     * 平台单号
     */
    @JSONField(name = "TID")
    private String tid;

    /**
     * 单据单号
     */
    @JSONField(name = "BILL_NO")
    private String billNo;

    /**
     * 奶卡列表
     */
    @JSONField(name = "NAIKA_MODELS")
    private List<NaiKaModel> naiKaModels;


    @Data
    public static class NaiKaModel implements Serializable {

        @JSONField(name = "ID")
        private Long id;

        /**
         * 商品名称
         */
        @JSONField(name = "PS_C_PRO_ENAME")
        private String psCProEname;

        /**
         * 规格
         */
        @JSONField(name = "SKU_SPEC")
        private String skuSpec;

        /**
         * 奶卡订单类型
         */
        @JSONField(name = "BUSINESS_TYPE_NAME")
        private String businessTypeName;

        /**
         * 卡号
         */
        @JSONField(name = "CARD_CODE")
        private String cardCode;

        /**
         * 操作时间
         */
        @JSONField(name = "OPERATE_TIME")
        private Date operateTime;

        /**
         * 状态
         */
        @JSONField(name = "STATUS_NAME")
        private String statusName;

        /**
         * sku编码
         */
        @JSONField(name = "SKU_ECODE")
        private String skuEcode;
    }

}
