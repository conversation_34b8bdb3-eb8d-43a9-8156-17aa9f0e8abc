package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromotion;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 李清波
 * create at:  2019/4/4  15:40
 * @description: 订单优惠测试-qb
 */
@Data
public class MergeOrderPromotionQbModel extends OcBOrderPromotion implements Serializable {
    @JSONField(name = "ORDERID")
    private long orderId;//订单ID别名 用于元数据显示
    private String beginOrderDate;//开始日期
    private String endOrderDate;//结束日期
    private Integer shopId;//店铺ID
    private Integer startindex;//开始条数
    private Integer rang;//每页条数

    /**
     * 赠送商品编码
     */
    @JSONField(name = "GIFT_ITEM_CODE")
    private String giftItemCode;

    /**
     * 优惠名称
     */
    @JSONField(name = "PROMOTION_NAME")
    private String promotionName;

}


