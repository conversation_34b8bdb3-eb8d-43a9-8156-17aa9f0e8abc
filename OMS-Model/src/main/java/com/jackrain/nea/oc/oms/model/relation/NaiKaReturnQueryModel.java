package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName NaiKaReturnQueryModel
 * @Description 奶卡退单管理
 * <AUTHOR>
 * @Date 2022/7/25 15:10
 * @Version 1.0
 */
@Data
public class NaiKaReturnQueryModel implements Serializable {

    @JSONField(name = "ID")
    private Long id;

    /**
     * 平台单号
     */
    @JSONField(name = "SOURCE_CODE")
    private String sourceCode;

    /**
     * 单据单号
     */
    @JSONField(name = "BILL_NO")
    private String billNo;

    /**
     * 单据类型
     */
    @JSONField(name = "BILL_TYPE")
    private Integer billType;

    /**
     * 单据类型 名称
     */
    @JSONField(name = "BILL_TYPE_NAME")
    private String billTypeName;

    /**
     * 店铺id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    /**
     * 店铺名称
     */
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    /**
     * 平台id
     */
    @JSONField(name = "CP_C_PLATFORM_ID")
    private Long cpCPlatformId;

    /**
     * 平台名称
     */
    @JSONField(name = "CP_C_PLATFORM_NAME")
    private String cpCPlatformName;

    /**
     * 退款状态
     */
    @JSONField(name = "RETURN_STATUS")
    private Integer returnStatus;

    /**
     * 退款状态 名称
     */
    @JSONField(name = "RETURN_STATUS_NAME")
    private String returnStatusName;

    /**
     * 平台退款单号
     */
    @JSONField(name = "T_RETURN_ID")
    private String tReturnId;

    /**
     * 退款自动作废
     */
    @JSONField(name = "CARD_AUTO_VOID")
    private Integer cardAutoVoid;

    /**
     * 退款自动作废
     */
    @JSONField(name = "CARD_AUTO_VOID_NAME")
    private String cardAutoVoidName;

    /**
     * 已发货退款单单据来源
     */
    @JSONField(name = "REFUND_SOURCE")
    private Integer refundSource;

    /**
     * 已发货退款单单据来源
     */
    @JSONField(name = "REFUND_SOURCE_NAME")
    private String refundSourceName;

    /**
     * 系统备注
     */
    @JSONField(name = "SYSREMARK")
    private String sysremark;

    @JSONField(name = "MODIFIEDDATE")
    private String modifiedDate;

    @JSONField(name = "MODIFIERNAME")
    private String modifierName;
}
