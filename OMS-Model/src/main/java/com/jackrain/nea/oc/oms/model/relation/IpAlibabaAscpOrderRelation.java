package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrder;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderItemExt;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/4 下午3:15
 * @description 猫超直发转单中间表关系
 **/
@Setter
@Getter
public class IpAlibabaAscpOrderRelation {
    private IpBAlibabaAscpOrder alibabaAscpOrder;

    private List<IpBAlibabaAscpOrderItemExt> alibabaAscpOrderItemExList;

    public Long getOrderId() {
        if (alibabaAscpOrder != null) {
            return alibabaAscpOrder.getId();
        }
        return -1L;
    }

    public String getOrderNo() {
        if (alibabaAscpOrder != null) {
            return alibabaAscpOrder.getBizOrderCode();
        }
        return "";
    }
}
