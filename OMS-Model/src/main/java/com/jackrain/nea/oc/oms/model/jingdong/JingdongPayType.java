package com.jackrain.nea.oc.oms.model.jingdong;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-05-22
 * create at : 2019-05-22 10:06 AM
 * 京东支付类型
 */
public interface JingdongPayType {
    /**
     * 1-货到付款, 2-邮局汇款, 3-自提, 4-在线支付, 5-公司转账, 6-银行卡转账
     */

    String ONE_PAY_ARRIVAL = "1-货到付款";
    String TW0_REMIT_BY_POST = "2-邮局汇款";
    String THREE_SELF = "3-自提";
    String FOUR_ONLINE = "4-在线支付";
    String FIVE_COMPANY_TRANSFER = "5-公司转账";
    String SIX_BANK_TRANSFER = "6-银行卡转账";

    String PAY_AFTER_USE = "20-先用后付";
}
