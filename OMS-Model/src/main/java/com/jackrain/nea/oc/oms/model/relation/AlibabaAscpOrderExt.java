package com.jackrain.nea.oc.oms.model.relation;

/**
 * @Descroption 猫超直发扩展基础类
 * <AUTHOR>
 * @Date 2020/09/05 15:49
 */
public class AlibabaAscpOrderExt {
    /**
     * 猫超订单主表
     **/
    public static final String TABLE_NAME_IP_B_ALIBABA_ASCP_ORDER = "ip_b_alibaba_ascp_order";
    /**
     * 猫超订单明细表
     */
    public static final String TABLE_NAME_IP_B_ALIBABA_ASCP_ORDER_ITEM = "ip_b_alibaba_ascp_order_item";
    /**
     * 猫超订单取消表
     */
    public static final String TABLE_NAME_IP_B_ALIBABA_ASCP_ORDER_CANCEL = "ip_b_alibaba_ascp_order_cancel";
    /**
     * 猫超退单主表
     */
    public static final String TABLE_NAME_IP_B_ALIBABA_ASCP_ORDER_REFUND = "ip_b_alibaba_ascp_order_refund";
    /**
     * 猫超退单明细表
     */
    public static final String TABLE_NAME_IP_B_ALIBABA_ASCP_ORDER_REFUND_ITEM = "ip_b_alibaba_ascp_order_refund_item";


}
