package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc : 退货单入库类
 * <AUTHOR> xiWen
 * @Date : 2021/5/26
 */
@Data
public class OcReturnInRelation {

    private boolean isWholeStockIn;

    /**
     * 退货单
     */
    private OcBReturnOrder item;

    private List<OcBReturnOrderRefund> subItems;

    private boolean isCurrentMatchFinished;

    /**
     * 退货商品.匹配
     */
    private List<OcBReturnOrderRefund> subMatchItems;

    /**
     * 入库商品.匹配
     */
    private List<OcBRefundInProductItem> subInMatchItems;

    /**
     * 入库匹配数量映射
     */
    private Map<Long, BigDecimal> subItemQtyMapping;

    /**
     * 入库匹配.日期,正次品映射
     */
    private Map<Long, Map<String, BigDecimal>> subItemMatchedMapping;

    /**
     * 是否锁单.匹配
     */
    private boolean isLockReturn;

    public void mappingQty(OcBReturnOrderRefund var, BigDecimal qty) {
        if (this.subItemQtyMapping == null) {
            this.subItemQtyMapping = new HashMap<>();
        }
        Long key = var.getId();
        BigDecimal bigDecimal = this.subItemQtyMapping.get(key);
        if (bigDecimal == null) {
            bigDecimal = BigDecimal.ZERO;
        }
        this.subItemQtyMapping.put(key, bigDecimal.add(qty));
    }

    public void matchedMapping(String groupKey, BigDecimal qty, Long id) {
        if (this.subItemMatchedMapping == null) {
            this.subItemMatchedMapping = new HashMap<>();
        }
        Map<String, BigDecimal> group = this.subItemMatchedMapping.get(id);
        if (group == null) {
            group = new HashMap<>();
            group.put(groupKey, qty);
            this.subItemMatchedMapping.put(id, group);
            return;
        }
        BigDecimal bigDecimal = group.get(groupKey);
        if (bigDecimal == null) {
            bigDecimal = BigDecimal.ZERO;
        }
        group.put(groupKey, bigDecimal.add(qty));
    }

}
