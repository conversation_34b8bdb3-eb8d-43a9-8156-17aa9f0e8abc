package com.jackrain.nea.oc.oms.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/8/27 1:41 下午
 * @Version 1.0
 */
@Data
public class PromotionInfo {

    private Long orderId;

    private String orderNo;

    private Date orderTime;

    private Integer orderType;

    private Date payTime;

    private Date holdTime;

    private String orderMark;

    private Date earnestMoneyTime;

    private Long provinceIdD;

    private Long shopId;

    private String shopEcode;

    private String shopEname;

    private String sellerRemark;

    private String buyerRemark;

    private String enumdataFlatfopm;

    private String memberName;

    private List<PromotionDetailInfo> productList;
}
