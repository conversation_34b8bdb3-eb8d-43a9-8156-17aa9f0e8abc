package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.enums.IsGenAdjustEnum;
import com.jackrain.nea.oc.oms.model.enums.IsGenInEnum;
import com.jackrain.nea.oc.oms.model.enums.IsGenMinusAdjustEnum;
import com.jackrain.nea.oc.oms.model.enums.IsMatchEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWithoutOrigEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnInType;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInActualItem;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 入库单关系
 *
 * @Desc : 入库商品级
 * <AUTHOR> xiWen
 * @Date : 2020/7/24
 */
@Data
public class RefundInRelation {

    private int subItemCount;

    private int subUnMatchCount;

    private int subMatchedCount;

    private boolean hasAdjustedEle;

    private ReturnInType returnInType;

    private Long currentMatchReturnId;

    private boolean forbidMinus;

    private boolean fluxWms = false;

    /**
     * 入库单
     */
    private OcBRefundIn refundIn;

    /**
     * 入库单明细
     */
    private List<OcBRefundInProductItem> items;

    /**
     * 实物明细
     */
    private List<OcBRefundInActualItem> actualItems;

    /**
     * 已匹配记录
     */
    private List<OcBRefundInProductItem> matchedItems;

    /**
     * 未匹配明细
     */
    private List<OcBRefundInProductItem> unMatchItems;

    /**
     * 无头件入库
     */
    private List<OcBRefundInProductItem> unAdjustments;

    /**
     * 需要冲无头件
     */
    private List<OcBRefundInProductItem> unMinusAdjustments;

    /**
     * 是否匹配成功
     *
     * @return 至少匹配一条
     */
    public boolean isMatchedSuccess() {
        return matchedItems != null && matchedItems.size() > 0;
    }

    /**
     * 是否存在未匹配
     *
     * @return
     */
    public boolean hasUnMatch() {
        return unMatchItems != null && unMatchItems.size() > 0;
    }


    /**
     * 记录需匹配明细
     *
     * @param var 未匹配明细
     */
    public void collectUnMatch(OcBRefundInProductItem var) {
        if (unMatchItems == null) {
            unMatchItems = new ArrayList<>();
        }
        unMatchItems.add(var);
    }


    /**
     * 剔除,转移已匹配
     *
     * @param vars 已匹配明细编号集合
     */
    public void popMatchedItem(List<OcBRefundInProductItem> vars) {
        if (CollectionUtils.isNotEmpty(vars)) {
           /* for (OcBRefundInProductItem var : vars) {
                if (isUnMatch(var)) {
                    var.setIsMatch(IsMatchEnum.MATCHED.getVal());
                    var.setIsGenInOrder(IsGenInEnum.YES.integer());
                }
                collectMatched(var);
                unMatchItems.remove(var);
            }*/
            int size = vars.size();
            for (int i = (size - 1); i >= 0; i--) {
                OcBRefundInProductItem var = vars.get(i);
                if (isUnMatch(var)) {
                    var.setIsMatch(IsMatchEnum.MATCHED.getVal());
                    var.setIsGenInOrder(IsGenInEnum.YES.integer());
                }
                collectMatched(var);
                unMatchItems.remove(var);
            }

        }

    }

    /**
     * 记录当前匹配商品
     *
     * @param var
     */
    public void collectMatched(OcBRefundInProductItem var) {
        if (matchedItems == null) {
            matchedItems = new ArrayList<>();
        }
        this.matchedItems.add(var);
    }

    /**
     * 入库单ID
     *
     * @return long
     */
    public Long getRefundId() {
        if (refundIn.getId() == null) {
            return 0L;
        }
        return refundIn.getId();
    }

    /**
     * @return sku e code for match and generate
     */
    public String getSkuCode(OcBRefundInProductItem var) {
        return var.getRealSkuEcode() == null ? var.getPsCSkuEcode() : var.getRealSkuEcode();
    }

    /**
     * sku转换
     *
     * @param var 待处理退货入库单明细
     */
    public void convertSku(OcBRefundInProductItem var) {
        if (StringUtils.isNotBlank(var.getRealSkuEcode())) {
            var.setPsCSkuEcode(var.getRealSkuEcode());
            var.setPsCSkuId(var.getRealSkuId());
        }
    }

    /**
     * @return 当前商品: 未匹配 ? 是
     */
    public boolean isUnMatch(OcBRefundInProductItem var) {
        return var == null || IsMatchEnum.UN_MATCH.getVal().equals(var.getIsMatch());
    }

    /**
     * @return 当前商品: 未匹配 ? 是
     */
    public boolean isMatched(OcBRefundInProductItem var) {
        return IsMatchEnum.MATCHED.getVal().equals(var.getIsMatch());
    }

    public boolean isAdjust(OcBRefundInProductItem var) {
        return IsGenAdjustEnum.YES.integer().equals(var.getIsGenAdjust());
    }

    public boolean isNeedAdjust(OcBRefundInProductItem var) {
        return !(isMatched(var) || IsGenAdjustEnum.YES.integer().equals(var.getIsGenAdjust()));
    }

    public boolean branchNorm2CMatch() {
        String sgBNoticeInBillNo = getRefundIn().getSgBNoticeInBillNo();
        returnInType = ReturnInType.convert2Enum(refundIn.getInType());
        if (ReturnInType.NORM2C == returnInType) {
            if (StringUtils.isNotBlank(sgBNoticeInBillNo)) {
                return this.matchedItems == null || this.matchedItems.size() < 1;
            }
        }
        return false;
    }

    public boolean branchNorm2BMatch() {
        return ReturnInType.NORM2B == returnInType;
    }

    public boolean branchNameless() {
        if (!(ReturnInType.NAMELESS == returnInType)) {
            return false;
        }
        return this.unMatchItems != null && this.unMatchItems.size() > 0;
    }

    public boolean branchMinusAdjust() {
        return this.unMinusAdjustments != null && this.unMinusAdjustments.size() > 0;
    }

    /**
     * @return 当前商品: 是否无原单 ? : 是
     */
    public boolean isWithOutOrig(OcBRefundInProductItem var) {
        return IsWithoutOrigEnum.IS_WITHOUT_ORIG.getVal().equals(var.getIsWithoutOrig());
    }

    /**
     * @return 当前商品: 存在错误条码信息 ? : 是
     */
    public boolean isSkuError(OcBRefundInProductItem var) {
        return var.getPsCSkuId() == null || var.getPsCSkuEcode() == null;
    }

    /**
     * @return 当前起始参数是否有效
     */
    public boolean isCurrentParamValid() {

        return !(refundIn == null || items == null || items.size() < 1);
    }

    /**
     * 是否需要冲无头件调整
     *
     * @param var 入库明细
     * @return true : 是
     */
    public boolean isNeedMinusAdjust(OcBRefundInProductItem var) {
        return IsGenAdjustEnum.YES.integer().equals(var.getIsGenAdjust())
                && IsGenInEnum.YES.integer().equals(var.getIsGenInOrder())
                && IsGenMinusAdjustEnum.NO.integer().equals(var.getIsGenMinusAdjust());
    }

    /**
     * 记录需要冲无头件明细
     *
     * @param var 需要要冲无头件入库单明细
     */
    public void collectAdjust(OcBRefundInProductItem var) {
        if (unAdjustments == null) {
            unAdjustments = new ArrayList<>();
        }
        unAdjustments.add(var);
    }

    /**
     * 记录需要冲无头件明细
     *
     * @param var 需要要冲无头件入库单明细
     */
    public void collectMinusAdjust(OcBRefundInProductItem var) {
        if (unMinusAdjustments == null) {
            unMinusAdjustments = new ArrayList<>();
        }
        boolean contains = unMinusAdjustments.contains(var);
        if (contains) {
            return;
        }
        unMinusAdjustments.add(var);
    }

}
