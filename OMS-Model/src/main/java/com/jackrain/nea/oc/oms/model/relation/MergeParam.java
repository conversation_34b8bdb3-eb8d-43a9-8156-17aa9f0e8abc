package com.jackrain.nea.oc.oms.model.relation;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Desc : 合单dto
 * <AUTHOR> xiWen
 * @Date : 2021/1/8
 */
@Data
@Accessors(chain = true)
public class MergeParam implements Serializable {

    private static final long serialVersionUID = 3118957188222448432L;

    /**
     * node
     */
    private String node;

    /**
     * start
     */
    private int start;

    /**
     * query size
     */
    private int size;

    /**
     * send  group qty each mq item
     */
    private int qtyEachMq;

    /**
     * shop id string
     */
    private String shopKeys;

    /**
     * mq send topic
     */
    private String topic;

    /**
     * mq send tag
     */
    private String tag;

    /**
     * order_encrypt_code string
     */
    private String encryptCodes;

    /**
     * query encryptCode qty
     */
    private int searchQty;

    /**
     * actual send encryptCode qty
     */
    private int validQty;

    /**
     * MQ  send qty
     */
    private int sendQty;

    /**
     * Send MQ  msgId
     */
    private String msgId;

    /**
     * send message
     */
    private String message;

    /**
     * consume time
     */
    private long csmTime;


    /**
     * validQty increase
     *
     * @param num step
     * @return MergeParam
     */
    public MergeParam validQtyRiseOf(int num) {
        this.validQty += num;
        return this;
    }

    /**
     * sendQty increase
     *
     * @param num step
     * @return MergeParam
     */
    public MergeParam sendQtyRiseOf(int num) {
        this.sendQty += num;
        return this;
    }

}
