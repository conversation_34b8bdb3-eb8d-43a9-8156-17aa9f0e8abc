package com.jackrain.nea.oc.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName OrderInvoiceChangeRequest
 * @Description
 * @Date 2022/9/8 下午4:43
 * @Created by wuhang
 */
@Data
public class OrderInvoiceChangeRequest implements Serializable {

    private static final long serialVersionUID = 5101537544477363316L;

    /** 订单发票id */
    @NotNull(message = "订单发票id不能为空")
    @JSONField(name = "ID")
    private Long id;

    /** 换票类型 1换纸质,2换专票,3换抬头 */
    @NotNull(message = "换票类型不能为空")
    @JSONField(name = "CHANGE_TYPE")
    private String changeType;

    /** 发票种类 0纸质,1电子 */
    @NotNull(message = "发票种类不能为空")
    @JSONField(name = "INVOICE_KIND")
    private String invoiceKind;

    /** 发票类型:0普票、1专票 */
    @NotNull(message = "发票类型不能为空")
    @JSONField(name = "INVOICE_TYPE")
    private String invoiceType;

    /** 抬头类型:0个人、1企业 */
    @NotNull(message = "抬头类型不能为空")
    @JSONField(name = "HEADER_TYPE")
    private String headerType;

    /** 发票抬头 */
    @NotNull(message = "发票抬头不能为空")
    @JSONField(name = "INVOICE_HEADER")
    private String invoiceHeader;

    /** 地址 */
    @JSONField(name = "UNIT_ADDRESS")
    private String unitAddress;

    /** 纳税人识别号 */
    @NotNull(message = "纳税人识别号不能为空")
    @JSONField(name = "TAXPAYER_NO")
    private String taxpayerNo;

    /** 手机号 */
    @JSONField(name = "UNIT_MOBILE")
    private String unitMobile;

    /** 开户行 */
    @JSONField(name = "OPENING_BANK")
    private String openingBank;

    /** 银行账号 */
    @JSONField(name = "BANK_ACCOUNT")
    private String bankAccount;

    /** 发票备注 */
    @JSONField(name = "INVOICE_REMARK")
    private String invoiceRemark;

    /** 收票人 */
    @JSONField(name = "RECEIVER")
    private String receiver;

    /** 收票人地址 */
    @JSONField(name = "RECEIVER_ADDRESS")
    private String receiverAddress;

    /** 邮箱 */
    @JSONField(name = "EMAIL")
    private String email;

    /** 手机号 */
    @JSONField(name = "PHONE")
    private String phone;
}
