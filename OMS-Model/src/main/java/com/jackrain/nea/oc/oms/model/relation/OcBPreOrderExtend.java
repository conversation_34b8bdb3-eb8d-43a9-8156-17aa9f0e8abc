package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName OcBPreOrderExtend
 * @Description 订单预导入
 * <AUTHOR>
 * @Date 2022/12/1 12:26
 * @Version 1.0
 */
@Data
@Slf4j
public class OcBPreOrderExtend implements Serializable {

    /**
     * 零售发货单
     */
    private OcBOrder ocBOrder;

    /**
     * 零售发货单明细
     */
    private List<OcBOrderItem> ocBOrderItemList;

    /**
     * 订单日志
     */
    private OcBOrderLog ocBOrderLog;

    /**
     * 支付类
     */
    private OcBOrderPayment payment;

    /**
     * 占单类
     */
    private OcBToBeConfirmedTask toBeConfirmedTask;
}
