package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @ClassName Pod2BOrderQueryRequest
 * @Description
 * <AUTHOR>
 * @Date 2024/8/29 16:51
 * @Version 1.0
 */
@Data
public class Pod2BOrderQueryRequest implements Serializable {
    private static final long serialVersionUID = -2196270228418118117L;

    /**
     * 第几页
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 总数量
     */
    private Integer total;

    /**
     * 平台单号
     */
    private String tid;

    /**
     * 订单状态（逗号分隔的字符串）
     */
    private String orderStatus;

    /**
     * 订单状态列表（从orderStatus解析得到）
     */
    private List<String> orderStatusList;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 出库通知单号
     */
    private String onNo;

    /**
     * 发货仓库
     */

    private String warehouseCode;

    /**
     * 物流公司
     */
    private String logisticsCode;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 省
     */
    private String buyerProvince;

    /**
     * 市
     */
    private String buyerCity;

    /**
     * 区
     */
    private String buyerArea;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 销售部门
     */
    private String salesDepartment;

    /**
     * 审核员
     */
    private String auditor;

    /**
     * 开始创建时间
     */
    private String startCreateTime;

    /**
     * 开始创建时间
     */
    private String endCreateTime;

    /**
     * 开始审核时间
     */
    private String startAuditTime;

    /**
     * 结束审核时间
     */
    private String endAuditTime;

    /**
     * 开始配货时间
     */
    private String startDistributeTime;

    /**
     * 结束配货时间
     */
    private String endDistributeTime;

    /**
     * 创建时间
     */
    private Date createDate;


    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 获取订单状态列表
     * 如果orderStatusList为空，则从orderStatus解析
     */
    public List<String> getOrderStatusList() {
        if (orderStatusList == null && StringUtils.isNotBlank(orderStatus)) {
            // 从逗号分隔的字符串解析状态列表
            orderStatusList = Arrays.asList(orderStatus.split(","));
            // 去除空白字符
            orderStatusList.replaceAll(String::trim);
        }
        return orderStatusList;
    }

    /**
     * 设置订单状态列表
     * 同时更新orderStatus字符串
     */
    public void setOrderStatusList(List<String> orderStatusList) {
        this.orderStatusList = orderStatusList;
        if (orderStatusList != null && !orderStatusList.isEmpty()) {
            this.orderStatus = String.join(",", orderStatusList);
        }
    }
}
