package com.jackrain.nea.oc.oms.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 已发货退款单导入DTO
 */
@Data
public class AfterDeliveryRefundOrderImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 退款状态
     */
    private String returnStatus;

    /**
     * 平台单号
     */
    private String platformOrderNo;

    /**
     * 平台退款单号
     */
    private String platformRefundNo;

    /**
     * 单据类型
     */
    private String billType;

    /**
     * 平台退款状态
     */
    private String platformRefundStatus;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 申请退款金额
     */
    private BigDecimal applyRefundAmount;

    /**
     * 实际退款金额
     */
    private BigDecimal actualRefundAmount;

    /**
     * 申请退款时间
     */
    private Date applyRefundTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 业务类型编码
     */
    private String businessTypeCode;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 退货数量
     */
    private BigDecimal applyRefundQty;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 成交金额
     */
    private BigDecimal transactionAmount;

    /**
     * 可退金额
     */
    private BigDecimal refundableAmount;

    /**
     * 购买数量
     */
    private BigDecimal purchaseQty;

    /**
     * 退款成功时间
     */
    private Date refundSuccessTime;
}