package com.jackrain.nea.oc.oms.model.constant;

/**
 * 来源单据类型
 */
public class SgFromSourceBillTypeConstants {
    // 来源单据类型
    public static final Integer BILL_TYPE_BY_HAND = 0;  //手动新增
    public static final Integer BILL_TYPE_RETAIL = 1;//零售发货
    public static final Integer BILL_TYPE_RETAIL_REF = 2;//零售退货单
    public static final Integer BILL_TYPE_SALE = 3;//销售单
    public static final Integer BILL_TYPE_SALE_REF = 4;//销售退货单
    public static final Integer BILL_TYPE_PUR = 5;//采购单
    public static final Integer BILL_TYPE_PUR_REF = 6;//采购退货单
    public static final Integer BILL_TYPE_TRANSFER = 7;//调拨单
    public static final Integer BILL_TYPE_VIPSHOP = 8;//唯品会单
    public static final Integer BILL_TYPE_ADJUST = 9;//库存调整单
    public static final Integer BILL_TYPE_VIPSHOP_TIME = 10;//唯品会时效订单
    public static final Integer BILL_TYPE_RETAIL_POS = 11;//零售单
    public static final Integer BILL_TYPE_DIRECT = 12;//直发单
    public static final Integer BILL_TYPE_OUT = 13;//出库单
    public static final Integer BILL_TYPE_IN = 14;//入库单
    public static final Integer BILL_TYPE_O2O_SUMMARY_INVENTORY_ADJUST = 15; // O2O汇总仓库存调整
    public static final Integer BILL_TYPE_DIFF = 16;//差异单
    public static final Integer BILL_STO_TRANSFER = 17;//逻辑调拨单
    public static final Integer BILL_SHARE_ALLOCATION = 18;//分货单
    public static final Integer BILL_STO_OUT = 19;//逻辑占用单
    public static final Integer BILL_SHARE_OUT = 20;//共享占用单
    public static final Integer BILL_TYPE_PHY_PROFIT = 21;//盈亏单
    public static final Integer BILL_TYPE_JITX = 25;//JITX订单
    public static final Integer BILL_TYPE_FREEZE = 26;//逻辑冻结单
    public static final Integer BILL_TYPE_UNFREEZE = 27;//逻辑解冻单
    public static final Integer BILL_TYPE_TRANSFER_DIFF = 28;//调拨差异回传
    public static final Integer BILL_TYPE_SHARE_AJUST = 29;//共享调整单
    public static final Integer BILL_TYPE_FOR_WAREHOUSE = 30;//寻仓单
    public static final Integer BILL_TYPE_SHARE_SA_TRANSFER = 31;//配销仓调拨单
    public static final Integer BILL_B2B_STO_TRANSFER = 32;//B2B调拨单
    public static final Integer BILL_REP_STO_TRANSFER = 33;//蓝鼎调拨单
    public static final Integer BILL_OLBP_STO_TRANSFER = 34;//欧睿调拨单
    public static final Integer BILL_B2B_TYPE_SALE = 35;//B2B销售单
    public static final Integer BILL_REP_TYPE_SALE = 36;//蓝鼎销售单
    public static final Integer BILL_OLBP_TYPE_SALE = 37;//欧睿销售单
    public static final Integer BILL_STO_BATCH_TRANSFER = 38;//逻辑调拨单
    public static final Integer BILL_SG_B_SHARE_TRANSFER = 39;//聚合仓调拨单
    public static final Integer BILL_TYPE_LY_RETURN = 100;//领用单（归还）
    public static final Integer BILL_TYPE_LY_NO_RETURN = 101;//领用单（不归还）
    public static final Integer BILL_TYPE_SEND_BACK = 110;//归还单
    public static final Integer BILL_TYPE_INVENTORY_LOSS = 120;//盘亏单
    public static final Integer BILL_TYPE_DRP_FREEZE = 130;//drp冻结单
    public static final Integer BILL_TYPE_DRP_UNFREEZE = 131;//drp解冻单
    public static final Integer BILL_SHARE_DISTRIBUTION = 140;//配货单
}
