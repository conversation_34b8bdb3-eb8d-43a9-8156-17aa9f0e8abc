package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * @Desc : 合单合局二级索引表
 * <AUTHOR> xiWen
 * @Date : 2021/1/8
 */
@Data
public class EncryptCodeGSI {

    @J<PERSON><PERSON>ield(name = "ID")
    private Long id;

    @J<PERSON><PERSON>ield(name = "ORDER_STATUS")
    private Integer orderStatus;

    @J<PERSON><PERSON>ield(name = "ORDER_TYPE")
    private Integer orderType;

    @J<PERSON>NField(name = "IS_INTERECEPT")
    private Integer isInterecept;

    @J<PERSON><PERSON><PERSON>(name = "IS_INRETURNING")
    private Integer isInreturning;

    @J<PERSON><PERSON><PERSON>(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSO<PERSON>ield(name = "ORDER_ENCRYPTION_CODE")
    private String orderEncryptionCode;

    @J<PERSON><PERSON>ield(name = "PLATFOR<PERSON>")
    private Integer platform;

    @J<PERSON><PERSON>ield(name = "IS_SAME_CITY_PURCHASE")
    private Integer isSameCityPurchase;

    @J<PERSON><PERSON><PERSON>(name = "ORDER_DATE")
    private Date orderDate;


}
