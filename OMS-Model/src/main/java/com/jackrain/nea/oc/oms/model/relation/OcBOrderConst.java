package com.jackrain.nea.oc.oms.model.relation;

import java.math.BigDecimal;

/**
 * 订单常量
 *
 * @author: xiwen.z
 * create at: 2019/3/16 0016
 */
public class OcBOrderConst {

    /**
     * 通用状态值,0: 否定, 1: 确定  无具体含义
     */
    public static final String IS_STATUS_SY = "1";
    public static final String IS_STATUS_SN = "0";
    public static final Integer IS_STATUS_IY = 1;
    public static final Integer IS_STATUS_IN = 0;
    public static final Integer IS_DELIVERY_URGENT = 1;
    public static final Integer IS_MODIFIED_ORDER = 1;
    public static final BigDecimal CONST_MINUS_ONE = new BigDecimal(-1);

    /**
     * 分页_每页数量
     */
    public static final int PAGE_SIZE = 20;
    /**
     * 分页_当前页
     */
    public static final int PAGE_NUM = 1;
    /**
     * 订单全部状态
     */
    public static final Integer ORDER_STATUS_ALL = 0;
    public static final String STATUS_TAB_ALL = "0";
    public static final String ORDER_STATUS_NONE = "-1";

    /**
     * 表名
     */
    public final static String TABLE_NAME = "oc_b_order";
    /**
     * 分库键
     */
    public final static String DB_PARTITION_FIELD_NAME = "ID";

    /**
     * 可选查询.开票记录
     */
    public static final String SEARCH_KEY = "queryflag";
    public static final String SEARCH_INVOICE = "invoice";

    /**
     * 订单状态值分隔符
     */
    public static final String ORDER_COMMA = ",";
    public static final String ORDER_COMMA_CN = "，";
    public static final String ORDER_COMMA_BLANK = " ";
    /**
     * 订单ID
     */
    public static final Long ORDER_ID = 0L;
    /**
     * 启用
     */
    public static final String IS_ACTIVE_YES = "Y";
    /**
     * 作废
     */
    public static final String IS_ACTIVE_NO = "N";
    /**
     * 日期最小长度
     */
    public static final int DATE_MIN_LENGTH = 8;
    /**
     * 订单标签.手工新增
     */
    public static final String ORDER_TAG_HAND = "手";
    /**
     * 订单标签.村淘
     */
    public static final String ORDER_TAG_VALLAGE = "村淘";
    /**
     * 订单.到付
     */
    public static final int ORDER_PAY_TYPE = 2;

    /**
     * 金额小数点后位数2
     */
    public static final int DECIMAL_QTY = 2;

    /**
     * 金额小数点后位数4
     */
    public static final int DECIMAL_QTY_FOUR = 4;

    /**
     * 金额,数量 正则
     */
    public static final String OCB_ORDER_NUMBER_REGES = "^((([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){1,4})?)?[~]?"
            + "((([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){1,4})?)?$";

    /**
     * 匹配正则表达式数字
     */
    public static final String OCB_ORDER_NUMBER = "^[-\\+]?[\\d]*$";
    /**
     * key. 数量
     */
    public static final String OCB_ORDER_QTY_ALL = "QTY_ALL";
    /**
     * key.金额
     */
    public static final String OCB_ORDER_ORDER_AMT = "ORDER_AMT";

    /**
     * 超时范围
     */
    public static final String OCB_ORDER_TIME_OUT_RANGE = "TIME_OUT_RANGE";

    /**
     * 付款时间
     */
    public static final String OCB_ORDER_PAY_TIME = "PAY_TIME";
    /**
     * key.商品条码编码
     */
    public static final String OCB_ORDER_ITEM_PSC_SKUECODE = "PS_C_SKU_ECODE";
    public static final String OCB_ORDER_ITEM_PS_C_PRO_ECODE = "PS_C_PRO_ECODE";
    public static final String OCB_ORDER_ITEM_PT_ECODE = "PS_C_SKU_PT_ECODE";
    public static final String OCB_ORDER_ITEM_PT_NAME = "PT_PRO_NAME";
    public static final String OCB_ORDER_ITEM_ESTIMATE_CON_TIME = "ESTIMATE_CON_TIME";
    public static final String OCB_ORDER_ITEM_RESERVE_VARCHAR01 = "RESERVE_VARCHAR01";
    public static final String SOURCE_CODE = "SOURCE_CODE";
    public static final String BILL_NO = "BILL_NO";
    public static final String EXPRESSCODE = "EXPRESSCODE";
    public static final String RECEIVER_MOBILE = "RECEIVER_MOBILE";


    /**
     * 平台SKUID
     */
    public static final String OCB_ORDER_ITEM_SKU_NUMIID = "SKU_NUMIID";

    /**
     * 平台商品ID
     */
    public static final String OCB_ORDER_ITEM_NUM_IID = "NUM_IID";

    /**
     * key.平台单号
     */
    public static final String ORDER_SOURCE_CODE = "SOURCE_CODE";
    public static final String ORDER_MERGE_SOURCE_CODE = "MERGE_SOURCE_CODE";

    /**
     * 预售查询
     */
    public static final String preSale = "FRONT_PAID_FINAL_NOPAID,FRONT_PAID_FINAL_PAID,NOT_PRESALE,FRONT_PAID_FINAL_PAING";


    public static final String IS_NULL = "isNull";
    /**
     * 订单列表.批量查询.指定字段
     */
    public static final String OCB_ORDER_LIST_BATCH_INCLUDEKEYS = "SOURCE_CODE,EXPRESSCODE,BILL_NO,ID,RECEIVER_MOBILE,MERGE_SOURCE_CODE,SG_B_OUT_BILL_NO,USER_NICK,USER_NICK,ORDER_SOURCE_PLATFORM_ECODE,CP_C_SHOP_ECODE,ANCHOR_ID";

    public static final String OCB_ORDER_LIST_BATCH_INCLUDEKEYS_WILDCARD = "MERGE_SOURCE_CODE";
    /**
     * 商品图片解析.图片路径位置
     */
    public static final int IMG_INDEX = 0;
    /**
     * key. 图片路径键值
     */
    public static final String IMG_KEY_NAME = "URL";
    /**
     * 图片路径是直接以字符串存储
     */
    public static final String IMG_PATH_HEAD_VAL = "http://";
    /**
     * 商品默认图片
     */
    public static final String GOODS_DEFAULT_IMAGE = "http://profcweb.oss-cn-hangzhou.aliyuncs.com/PS_C_PRO/6X1023/3e"
            + "2e30df-2980-4d61-9322-48673edb5579.jpg";

    /**
     * 退货入库单.未匹配
     */
    public static final int REFUND_IN_MATCHSTATUS_UN = 0;
    /**
     * 退货入库单.部分匹配
     */
    public static final int REFUND_IN_MATCHSTATUS_PORTION = 1;
    /**
     * 退货入库单.全部匹配
     */
    public static final int REFUND_IN_MATCHSTATUS_ALL = 2;

    /**
     * 退货入库单.特殊处理类型.正常
     */
    public static final String ORDER_SPECIAL_TYPE_NORMAL = "0";
    /**
     * 退货入库单.特殊处理类型.错发扫描处理
     */
    public static final String ORDER_SPECIAL_TYPE_WRONG_DEAL = "1";
    /**
     * 退货入库单.特殊处理类型.鞋盒条码与实物条码不符处理
     */
    public static final String ORDER_SPECIAL_TYPE_INCONFORMITY = "2";


    /**
     * 退换货单状态.未虚拟入库
     */
    public static final int UN_INVENTED_STATUS = 0;
    /**
     * 退换货单状态.虚拟入库未入库
     */
    public static final Integer INVENTED_STATUS_UN = 1;
    /**
     * 退换货单状态.虚拟入库已入库
     */
    public static final int INVENTED_STATUS_FINISH = 2;

    /**
     * 全渠道订单.退货状态.全部退货
     */
    public static final int ORDER_RETURN_STATUS_ALL = 3;
    /**
     * 全渠道订单.退货状态.部分退货
     */
    public static final int ORDER_RETURN_STATUS_PORTION = 2;

    /**
     * 生成调整单类型.正负向调整.实收条码
     */
    public static final int ADJUST_TYPE_MATCH_MINUS = 1;
    /**
     * 生成调整单类型.正向调整.无实收条码
     */
    public static final int ADJUST_TYPE_UNMATCH_SKU = 2;
    /**
     * 生成调整单类型.正向调整.实收条码
     */
    public static final int ADJUST_TYPE_UNMATCH_REAL = 3;

    /**
     * 退换货字段选项组
     */
    public static final String MUL_GROUP_KEYS = "IS_TOAG,IS_ADD,WMS_CANCEL_STATUS,IS_TOWMS,BILL_TYPE,INVENTED_STATUS,"
            + "RETURN_STATUS,IS_BACK,TO_NAIKA_STATUS,GENERIC_MARK,NUM_LESS,NUM_MORE,PRODUCT_DIFF";
    public static final String MUL_REF_KEYS = "OWNERID,CP_C_SHOP_ID,CP_C_PHY_WAREHOUSE_ID,CP_C_PHY_WAREHOUSE_IN_ID,"
            + "CP_C_LOGISTICS_ID,CP_C_STORE_ID,PLATFORM,RESERVE_BIGINT04";


    /**
     * WMS 库存类型
     * ZP：正品
     * CC：次品
     */
    public static final String WMS_INVENTORY_TYPE_ZP = "ZP";
    public static final String WMS_INVENTORY_TYPE_CC = "CC";

    /**
     * 是否无名件单.否
     */
    public static final Integer REFUND_IN_IS_ANONYMOUSO_N = 0;
    /**
     * 是否无名件单.是
     */
    public static final Integer REFUND_IN_IS_ANONYMOUSO_YES = 1;

    /**
     * 仓库性质
     * 05 o2o
     */
    public static final String CP_C_PHY_WAREHOUSE_TYPE = "05";

    /**
     * 实体仓类型 门店
     */
    public static final String CP_C_PHY_WAREHOUSE_TYPE_STORE_02 = "02";

    /**
     * 逻辑仓类型 门店
     */
    public static final String CP_C_STORE_TYPE_STO = "STO";

    /**
     * 是否同意退款 Y是 N否
     */
    public static final String AGREE_REFUND_Y = "是";
    public static final String AGREE_REFUND_N = "否";

    public static final String AGREE_REFUND_YES = "Y";
    public static final String AGREE_REFUND_NO = "N";

    private OcBOrderConst() {
    }

    /**
     * 异常类型 1 寻源  2 分物流 3 传wms 5 效期异常
     */
    public static final Integer OCCUPY = 1;
    public static final Integer SUBLOGISTICS_SERVICE = 2;
    public static final Integer ORDER_TOWMS = 3;
    public static final Integer ORGANIZATIONAL = 4;
    public static final Integer EXPIRY = 5;

}
