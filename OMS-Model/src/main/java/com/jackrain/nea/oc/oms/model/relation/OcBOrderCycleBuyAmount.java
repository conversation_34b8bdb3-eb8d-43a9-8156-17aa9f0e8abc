package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 天猫周期购订单金额
 *
 * <AUTHOR>
 */
@Data
public class OcBOrderCycleBuyAmount {

    /**
     * 商品总额
     */
    private BigDecimal productAmt;

    /**
     * 商品优惠金额
     */
    private BigDecimal productDiscountAmt;

    /**
     * 订单优惠金额
     */
    private BigDecimal orderDiscountAmt;

    /**
     * 调整金额
     */
    private BigDecimal adjustAmt;

    /**
     * 配送费用
     */
    private BigDecimal shipAmt;

    /**
     * 服务费
     */
    private BigDecimal serviceAmt;

    /**
     * 订单总额
     */
    private BigDecimal orderAmt;

    /**
     * 已收金额
     */
    private BigDecimal receivedAmt;

    /**
     * 代销结算金额
     */
    private BigDecimal consignAmt;

    /**
     * 代销运费
     */
    private BigDecimal consignShipAmt;

    /**
     * 应收金额
     */
    private BigDecimal amtReceive;

    //---------明细，每条相同----------//
    /**
     * 成交价格
     */
    private BigDecimal detailPrice;
    /**
     * 优惠金额
     */
    private BigDecimal detailAmtDiscount;
    /**
     * 调整金额
     */
    private BigDecimal detailAdjustAmt;
    /**
     * 单行实际成交金额
     */
    private BigDecimal detailRealAmt;
    /**
     * 整单平摊金额
     */
    private BigDecimal detailOrderSplitAmt;
    /**
     * 单件实际成交价
     */
    private BigDecimal detailPriceActual;


}
