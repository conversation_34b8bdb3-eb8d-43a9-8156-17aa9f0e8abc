package com.jackrain.nea.oc.oms.extmodel;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: 郑立轩
 * @since: 2019/5/16
 * create at : 2019/5/16 13:45
 */
@Data
public class OcBOrderExtToHya extends OcBReturnOrder {
    Integer orderType;
    BigDecimal orderAmt;
    @JSONField(name = "RECEIVER_NAME")
    @Field(type = FieldType.Keyword)
    String receiver_name;  //顾客名字
    @JSONField(name = "RECEIVER_MOBILE")
    @Field(type = FieldType.Keyword)
    String receiver_mobile;  // 顾客电话
    @JSONField(name = "BUYER_ALIPAY_NO")
    @Field(type = FieldType.Keyword)
    String buyer_alipay_no; // 收款账号
    @JSONField(name = "USER_NICK")
    @Field(type = FieldType.Keyword)
    String user_nick; // 会员昵称
    @JSONField(name = "PAY_TIME")
    @Field(type = FieldType.Long)
    Date pay_time; // 付款时间

}
