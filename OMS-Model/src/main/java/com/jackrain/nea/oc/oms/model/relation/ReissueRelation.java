package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc : 退货补寄关系类
 * <AUTHOR> xiWen
 * @Date : 2023/4/11
 */
@Data
public class ReissueRelation {

    private OcBOrder order;

    private OcBReturnOrder returnOrder;

    private Map<Long, OcBOrderItem> orderItemMap;

    private Map<Long, OcBReturnOrderRefund> returnItemMap;


    public static ReissueRelation build() {
        ReissueRelation instance = new ReissueRelation();
        instance.orderItemMap = new HashMap<>();
        instance.returnItemMap = new HashMap<>();
        return instance;
    }

    public ReissueRelation assignParam(OcBOrder order, OcBReturnOrder returnOrder) {
        this.setOrder(order);
        this.setReturnOrder(returnOrder);
        return this;
    }

    public ReissueRelation assignParam(OcBOrder order, OcBReturnOrder returnOrder, List<OcBOrderItem> orderItems) {
        this.setOrder(order);
        this.setReturnOrder(returnOrder);
        this.addOrderItems(orderItems);
        return this;
    }

    public ReissueRelation assignParam(OcBOrder order, OcBReturnOrder returnOrder,
                                       List<OcBOrderItem> orderItems, List<OcBReturnOrderRefund> returnItems) {
        this.setOrder(order);
        this.setReturnOrder(returnOrder);
        this.addOrderItems(orderItems);
        this.addReturnItems(returnItems);
        return this;
    }

    public void addOrderItems(List<OcBOrderItem> list) {
        for (OcBOrderItem item : list) {
            this.orderItemMap.put(item.getId(), item);
        }
    }

    public void addReturnItems(List<OcBReturnOrderRefund> list) {
        for (OcBReturnOrderRefund item : list) {
            this.returnItemMap.put(item.getId(), item);
        }
    }
}
