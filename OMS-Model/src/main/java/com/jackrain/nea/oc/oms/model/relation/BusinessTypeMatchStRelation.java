package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.StCBusinessTypeMatchStrategy;
import com.jackrain.nea.oc.oms.model.table.StCBusinessTypeMatchStrategyItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/7/14 下午4:41
 * @Version 1.0
 */
@Data
public class BusinessTypeMatchStRelation implements Serializable {

    private StCBusinessTypeMatchStrategy matchStrategy;

    private List<StCBusinessTypeMatchStrategyItem> matchStrategyItems;
}
