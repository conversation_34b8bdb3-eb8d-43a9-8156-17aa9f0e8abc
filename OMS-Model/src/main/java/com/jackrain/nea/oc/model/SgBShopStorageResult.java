package com.jackrain.nea.oc.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * className: SgBShopStorageResult
 * description: 查询平台店铺仓库关系库存结果
 *
 * <AUTHOR>
 * create: 2021-07-15
 * @since JDK 1.8
 */
@Data
public class SgBShopStorageResult implements Serializable {

    private static final long serialVersionUID = 435757262905735617L;

    private Set<Long> shareStorageIds;

    private List<SgBPhyInStorageItemExt> phyInStorageItemExtList;

}
