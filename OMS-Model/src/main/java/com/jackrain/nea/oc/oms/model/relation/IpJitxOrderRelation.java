package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipItem;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 黄超
 * @since : 2019-06-25
 * create at : 2019-06-25 6:00 PM
 */
@Data
public class IpJitxOrderRelation {

    private IpBJitxOrder jitxOrder;

    private List<IpBJitxOrderItemEx> jitxOrderItemList;

    private List<IpBTimeOrderVip> timeOrderVipList;

    private CpCPhyWarehouse cpCPhyWarehouse;

    private LogisticsInfo logisticsInfo;

    private String remarks;
    /**
     * 时效订单仓库与平台仓库是否一致 记录
     */
    private Boolean sameWarehouse;
    /**
     * 是否需要取消占用
     */
    private boolean needCancelStockOccupy;
    /**
     * 是否需要更新时效订单为完成
     */
    private boolean needUpdateTimeOrder;

    private Map<String,List<IpBTimeOrderVipItem>> skuTimeOrderItemMap;

    private Long ocBOrderId;

    /**
     * 时效单占用实体仓id
     */
    private Long timOrderItemWareHouseId;

    /**
     * @return 获取当前处理单据的订单ID号
     */
    public Long getOrderId() {
        if (jitxOrder != null) {
            return jitxOrder.getId();
        }
        return -1L;
    }

    /**
     * @return 获取当前处理单据的订单ID号
     */
    public String getOrderNo() {
        if (jitxOrder != null) {
            return jitxOrder.getOrderSn();
        }
        return "";
    }

}
