package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrder;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrderItemEx;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 15:36 2020/5/9
 * description ：唯品会退供单
 * @ Modified By：
 */
@Data
public class IpVipReturnOrderRelation implements Serializable {

    private static final long serialVersionUID = 7368578841186928764L;
    /**
     * 退供单中间表
     */
    private IpBVipReturnOrder vipReturnOrder;

    /**
     * 退供单中间表明细
     */
    private List<IpBVipReturnOrderItemEx> vipReturnOrderItems;

    /**
     * 平台条码 对应的 系统条码
     */
    private Map<String,Long> skuCodeMap;


    /**
     * @return 退供单中间表ID
     */
    public long getOrderId() {
        if (vipReturnOrder != null) {
            return vipReturnOrder.getId();
        }
        return -1;
    }

    /**
     * @return 退供单号
     */
    public String getOrderNo() {
        if (vipReturnOrder != null) {
            return vipReturnOrder.getReturnSn() + "";
        }
        return "";
    }
}
