package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderCancel;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;

/**
 * @description: 猫超直发取消订单中间表关系
 * @author: xtt
 * @date: 2020-09-04 14:23
 **/
@Data
public class IpBAlibabaAscpOrderCancelRelation {
    /**
     * 猫超直发取消订单中间表
     */
    private IpBAlibabaAscpOrderCancel ipBAlibabaAscpOrderCancel;

//    /**
//     * 猫超直发中间表主表
//     */
//    private IpBAlibabaAscpOrder ipBAlibabaAscpOrder;

    /**
     * 对应有效原单
     */
    private OcBOrder ocBOrder;


    /**
     * 平台单号
     *
     * @return
     */
    public String getSourceCode() {
        if (null != ipBAlibabaAscpOrderCancel) {
            return ipBAlibabaAscpOrderCancel.getBizOrderCode();
        }
        return "";
    }

    /**
     * 中间表订单ID
     */
    public long getOrderId() {
        if (ipBAlibabaAscpOrderCancel != null) {
            return ipBAlibabaAscpOrderCancel.getId();
        }
        return -1;
    }
}
