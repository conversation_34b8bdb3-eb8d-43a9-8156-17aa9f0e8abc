package com.jackrain.nea.oc.request;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-12-14 15:16
 * @Description : 通用订单保存
 **/
@Data
public class StandplatOrderCreateModel implements Serializable {

    /**平台单号*/
    private String tid;

    /**平台号*/
    private String platform;

    /**店铺编码*/
    private String cpCShopEcode;

    /**物流类型*/
    private String shippingType;

    /**收货人*/
    private String receiverName;

    /**收货人所在省*/
    private String receiverProvince;

    /**收货人所在市*/
    private String receiverCity;

    /**收货人所在区*/
    private String receiverDistrict;

    /**收货人详细地址*/
    private String receiverAddress;

    /**收货人邮编*/
    private String receiverZip;

    /**收货人电话*/
    private String receiverPhone;

    /**收货人手机号*/
    private String receiverMobile;

    /**买家邮箱*/
    private String buyerEmail;

    /**卖家备注*/
    private String sellerMemo;

    /**买家留言*/
    private String buyerMessage;

    /**系统备注*/
    private String sysremark;

    /**邮费*/
    private BigDecimal postFee;

    /**交易创建时间*/
    private Date tradeCreateTime;

    /**付款时间*/
    private Date payTime;

    /**交易修改时间*/
    private Date tradeUpdateTime;

    /**交易结束时间*/
    private Date tradeEndTime;

    /**业务类型*/
    private String type;

    /**退款状态*/
    private String orderStatus;

    /**调整金额*/
    private BigDecimal adjustFee;

    /**优惠金额*/
    private BigDecimal discountFee;

    /**实付金额*/
    private BigDecimal payment;

    /**应付金额*/
    private BigDecimal totalFee;

    /**商品价格*/
    private BigDecimal price;

    /**官网会员号码*/
    private String gwVipCode;

    /**官网会员手机号*/
    private String gwVipMobile;

    /**官网渠道代码*/
    private String gwSourceCode;

    /**官网渠道组*/
    private String gwSourceGroup;

    /**订单明细信息*/
    private List<OrderItem> itemList;


    @Data
    public static class OrderItem implements Serializable {
        /**子订单号*/
        private String oid;

        /**平台商品id*/
        private String numiid;

        /**平台条码id*/
        private String skuid;

        /**条码id*/
        private Long psCSkuId;

        /**条码编码*/
        private String psCSkuEcode;

        /**商品id*/
        private Long psCProId;

        /**商品编码*/
        private String psCProEcode;

        /**商品名称*/
        private String psCProEname;

        /**数量*/
        private Long qty;

        /**单价*/
        private BigDecimal price;

        /**实付金额*/
        private BigDecimal payment;

        /**调整金额*/
        private BigDecimal adjustFee;

        /**优惠金额*/
        private BigDecimal discountFee;

        /**应付金额*/
        private BigDecimal totalFee;
    }

}
