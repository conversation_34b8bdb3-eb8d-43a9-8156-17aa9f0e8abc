package com.jackrain.nea.oc.oms.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: 黄世新
 * @Date: 2020/8/27 1:49 下午
 * @Version 1.0
 */
@Data
public class PromotionDetailInfo {

    @JSONField(name = "amt_list")
    private BigDecimal amtList;

    @JSONField(name = "amt_retail")
    private BigDecimal amtRetail;

    @JSONField(name = "amt_receivable")
    private BigDecimal amtReceivable;

    private Integer qtty;

    private String sku;

    private String ecode;

    private String platformEcode;

    private String platformSku;

}
