package com.jackrain.nea.oc.oms.model.resources;


/**
 * 系统备注常量
 */
public class SysNotesConstant {

    public static final String SYS_REMARK0 = "系统异常,转换失败!";

    public static final String SYS_REMARK1 = "不存在原始订单;";

    public static final String SYS_REMARK2 = "找不到有效原始订单超过1天,系统自动标记已转换;";

    public static final String SYS_REMARK3 = "关联不到申请的订单商品SKU,无法转换;";

    public static final String SYS_REMARK4 = "原始订单已作废取消，无需进行转换;";

    public static final String SYS_REMARK5 = "退货单已经存在，修改退单状态为已转换;";

    public static final String SYS_REMARK6 = "退货单已经存在，修改退单状态为已转换;";

    public static final String SYS_REMARK7 = "退换货单状态不满足转换条件;";

    public static final String SYS_REMARK8 = "对应的退换货单状态为:%s,状态异常;";

    public static final String SYS_REMARK9 = "换货商品在本地商品中不存在，商品sku:%s;";

    public static final String SYS_REMARK10 = "换货商品sku:%s,在本地商品中不存在,超过3天,系统自动标记已转换;";

    public static final String SYS_REMARK11 = "未找到对应的换货订单;";

    public static final String SYS_REMARK12 = "换货转退货退款，对应的换货单已经发货;";

    public static final String SYS_REMARK13 = "换货单已经退货成功;";

    public static final String SYS_REMARK14 = "换货转退货退款，配货中对接WMS未撤回;";

    public static final String SYS_REMARK15 = "换货订单取消失败;";

    public static final String SYS_REMARK16 = "原始订单未发货，不能转换，请发货后处理;";

    public static final String SYS_REMARK17 = "买家申请退款，转换完成;";

    public static final String SYS_REMARK18 = "退款关闭，转换成功;";

    public static final String SYS_REMARK19 = "订单已全部退款完成;";

    public static final String SYS_REMARK20 = "卖家同意退款，转换完成;";

    public static final String SYS_REMARK21 = "对应的原单数据在京东订单中间表中不存在，不能转换;";

    public static final String SYS_REMARK21_1 = "对应的原单数据在猫超直发订单中间表中不存在，不能转换;";

    public static final String SYS_REMARK22 = "退单商品在本地不存在,超过3天,系统自动标记已转换;";

    public static final String SYS_REMARK23 = "退单商品在本地不存在;";

    public static final String SYS_REMARK24 = "订单已传WMS且不可撤回，不允许转换;";

    public static final String SYS_REMARK25 = "订单状态不满足当前退单转换状态;";

    public static final String SYS_REMARK26 = "退换货单状态不满足当前换货转换状态;";

    public static final String SYS_REMARK27 = "当前退单状态不满足转换条件,标记为已转换,请等待下次转换;";

    public static final String SYS_REMARK28 = "生成应收调整单成功,转换完成;";

    public static final String SYS_REMARK29 = "生成退单成功,转换完成;";

    public static final String SYS_REMARK30 = "退款成功,转换完成;";

    public static final String SYS_REMARK31 = "已审核订单暂不拦截,等待下一次拦截;";

    public static final String SYS_REMARK32 = "仅退款应收调整单已生成;";

    public static final String SYS_REMARK33 = "退单不存在,标记已转换;";

    public static final String SYS_REMARK35 = "不存在原始订单,超过3天,系统自动标记已转换";

    public static final String SYS_REMARK36 = "原订单已经取消或作废;";

    public static final String SYS_REMARK37 = "原订单未发货，不进行转换";

    public static final String SYS_REMARK38 = "原始订单超过3天未发货,系统自动标记已转换";

    public static final String SYS_REMARK39 = "卖家未同意退款，不进行转换";

    public static final String SYS_REMARK40 = "创建应收调整单";

    public static final String SYS_REMARK43 = "退货单中已存在，修改退单状态为已转换";

    public static final String SYS_REMARK45 = "WMS撤回失败";

    public static final String SYS_REMARK46 = "买家申请退款,反审核失败,转换失败!等待下一次转换";

    public static final String SYS_REMARK47 = "当前条码为福袋商品,不允许换货,标记已转换";

    public static final String SYS_REMARK48 = "退单的类型为非退货，系统自动标记为已转换;";

    public static final String SYS_REMARK49 = "超过3天未找到原单，标记为锁定失败;";

    public static final String SYS_REMARK50 = "未找到原订单;";

    public static final String SYS_REMARK51 = "超过解锁时间未找到原单，标记为锁定失败;";

    public static final String SYS_REMARK54 = "部分锁单成功、部分锁单失败;";

    public static final String SYS_REMARK55 = "锁单失败;";

    public static final String SYS_REMARK56 = "锁单成功;";

    public static final String SYS_REMARK57 = "退款关闭,标记为已转换;";

    public static final String SYS_REMARK53 = "原订单已出库，需生成退货单，订单退款转换失败;";

    public static final String SYS_REMARK60 = "订单状态为已审核、待分配或传WMS中，请等待下次转换;";

    public static final String SYS_REMARK61 = "全渠道订单其他人正在在操作，请等待下次转换;";

    public static final String SYS_REMARK62 = "标记退款完成服务失败，请等待下次转换;";

    public static final String SYS_REMARK63 = "退货单的状态不满足，不能取消退货单;";

    public static final String SYS_REMARK64 = "原订单已经取消或作废，不能撤销；";

    public static final String SYS_REMARK65 = "订单已发货，待退货；";

    public static final String SYS_REMARK66 = "执行退款完成服务失败3次，需人为操作，标记已转换;";

    public static final String SYS_REMARK67 = "转换完成;";

    public static final String SYS_REMARK68 = "调用京东取消订单更新出库状态接口失败，请手动调用;";

    public static final String SYS_REMARK69 = "未找到京东订单数据，请等待下次转换;";

    public static final String SYS_REMARK69_1 = "未找到猫超直发订单数据，请等待下次转换;";

    public static final String SYS_REMARK70 = "未找到京东订单数据,超过3天,系统自动标记已转换";

    public static final String SYS_REMARK71 = "AG转换失败，等待补偿任务执行;";

    public static final String SYS_REMARK90 = "原订单未分配、寻源中或传WMS中，不允许处理";

    public static final String SYS_REMARK70_1 = "未找到猫超直发订单数据,超过配置天数,系统自动标记已转换";

    public static final String SYS_REMARK100 = "奶卡订单生成已发货退款单，转换完成;";

    public static final String SYS_REMARK110 = "奶卡订单生成退换货单失败，转换完成;";




    /**
     * 新增log日志常量
     */
    public static final String ADD_LOG_MESSAGE = "新增退货单";

    public static final String ADD_LOG_TYPE = "新增退货单";

    /**
     * log日志常量
     */
    public static final String CANCEL_LOG_MESSAGE = " 取消退货单";

    public static final String CANCEL_LOG_TYPE = "取消退货单";


    /**
     * log日志常量
     */
    public static final String UPDATE_LOG_MESSAGE = " 更新退货单";

    public static final String UPDATE_LOG_TYPE = "更新退货单";


    /**
     * 淘宝换货转换日志
     */
    public static final String TAOBAO_EXCHANGE_LOG_TYPE = "淘宝换货转换";

    public static final String TAOBAO_EXCHANGE_LOG_MESSAGE = "更新换货地址，原地址:%s";

    public static final String TAOBAO_EXCHANGE_PHONE_LOG_MESSAGE = "更新买家联系方式，原联系方式:%s";

    /**
     * 取消订单的日志
     */
    public static final String CANCEL_ORDER_LOG_TYPE = "淘宝换货转换";

    public static final String CANCEL_ORDER_LOG_MESSAGE = "换货转退货退款，取消换货单成功";

    public static final String VOID_ORDER_LOG_MESSAGE = "换货转退货退款，作废换货单成功";

    public static final String CANCEL_ORDER_INVENTE_LOG_MESSAGE = "订单取消，生成虚拟定金订单";

    /**
     * 新增退换货单
     */
    public static final String ADD_EXCHANGEORDER_LOG_TYPE = "新增退换货单";

    public static final String ADD_EXCHANGEORDER_LOG_MESSAGE = "新增退换货单";

    /**
     * 取消退换货单
     */
    public static final String CANCEL_EXCHANGEORDER_LOG_TYPE = "取消退换货单";

    public static final String CANCEL_EXCHANGEORDER_LOG_MESSAGE = "取消退换货单成功";

    /**
     * 更新退换货单
     */
    public static final String UPDATE_EXCHANGEORDER_LOG_TYPE = "更新退货单";

    public static final String UPDATE_EXCHANGEORDER_LOG_MESSAGE_FOR_LOGISTICS = "更新退换货单物流信息";


}
