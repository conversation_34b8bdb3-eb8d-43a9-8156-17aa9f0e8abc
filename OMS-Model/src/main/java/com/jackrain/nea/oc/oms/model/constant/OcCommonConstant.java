package com.jackrain.nea.oc.oms.model.constant;

import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;

import java.util.List;

/**
 * className: OcCommonConstant
 * description:订单中心通用常量类
 *
 * <AUTHOR>
 * create: 2021-06-19
 * @since JDK 1.8
 */
public class OcCommonConstant {

    /**
     * 门店KPI设置表名
     */
    public static final String OC_B_RETURN_ORDER_NODE_RECORD = "OC_B_RETURN_ORDER_NODE_RECORD";
    /**
     * 门店KPI设置表名
     */
    public static final String AC_F_STORE_KPI = "AC_F_STORE_KPI";
    /**
     * 经销商价格配置
     */
    public static final String AC_F_PRICE_SETTING = "AC_F_PRICE_SETTING";
    /**
    /**
     * 经销商价格配置日志
     */
    public static final String AC_F_PRICE_SETTING_LOG = "AC_F_PRICE_SETTING_LOG";
    /**
     * JITX寻仓结果表
     */
    public static final String IP_B_JITX_DELIVERY_RECORD = "IP_B_JITX_DELIVERY_RECORD";
    /**
     * 经销商打包费用
     */
    public static final String AC_F_CUSTOMER_PACKAGE_FEE = "AC_F_CUSTOMER_PACKAGE_FEE";
    /**
     * 经销商打包费用
     */
    public static final String AC_F_VIP_AMT = "AC_F_VIP_AMT";
    /**
     * 门店退货入库单
     */
    public static final String OC_B_STORE_REFUND_IN = "OC_B_STORE_REFUND_IN";
    /**
     * 退货入库单
     */
    public static final String OC_B_REFUND_IN = "OC_B_REFUND_IN";
    /**
     * 门店退货入库单商品明细
     */
    public static final String OC_B_STORE_REFUND_IN_ITEM = "OC_B_STORE_REFUND_IN_ITEM";
    /**
     * 退货入库单商品明细
     */
    public static final String OC_B_REFUND_IN_PRODUCT_ITEM = "OC_B_REFUND_IN_PRODUCT_ITEM";
    /**
     * 店铺平台管控
     */
    public static final String OC_B_STORE_PRICE_CONTROL = "OC_B_STORE_PRICE_CONTROL";
    /**
     * 商品指定效期
     */
    public static final String ST_ITEM_EXPIRATION_DATE = "ST_ITEM_EXPIRATION_DATE";

    /**
     * 增值服务
     */
    public static final String OC_B_ORDER_ADDSERVICE_REPORT = "OC_B_ORDER_ADDSERVICE_REPORT";
    /**
     * 框架标准参数--修改数据
     */
    public static final String FIX_COLUMN = "fixcolumn";
    /**
     * 框架标准参数--id
     */
    public static final String OBJ_ID = "objid";

    public static final String BILL_NO = "billno";

    public static final String DATE = "date";

    public static final String DATA = "data";

    public static final String MESSAGE = "message";

    public static final String ORDER_PRIFIX = "ON";

    public static final String RETURN_ORDER_PRIFIX = "TH";

    /**
     * 物流轨迹原始单据类型--零售发货单
     */
    public static final Integer BILL_TYPE_RETAIL = 1;
    /**
     * 物流轨迹原始单据类型--退换货单
     */
    public static final Integer BILL_TYPE_RETURN_ORDER = 2;

    /**
     * 物流轨迹  在途-揽收，已签收
     */
    public static final String LOGISTICS_STATUS_ONROAD = "ONROAD";
    public static final String LOGISTICS_STATUS_ARRIVED = "ARRIVED";

    /**
     * 丹鸟快递公司编码
     */
    public static final String DNKD = "DNKD";

    /**
     * 门店KPI设置表名
     */
    public static final String OC_B_ORDER_NODE_RECORD = "OC_B_ORDER_NODE_RECORD";

    public static final List<String> list =
            Lists.newArrayList(OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN.getCode(),
                    OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_RETURN.getCode(),
                    OrderBusinessTypeCodeEnum.CYCLE_PICK_UP_RETURN.getCode(),
                    OrderBusinessTypeCodeEnum.E_COMMERCE_SALE_ORDER_RETURN.getCode());

    public static final Integer AUDIT_SEND_DINGTALK = 1;
    public static final Integer DELIVERY_SEND_DINGTALK = 2;


}
