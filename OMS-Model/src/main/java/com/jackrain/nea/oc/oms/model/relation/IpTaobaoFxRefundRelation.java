package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;

import java.util.List;

/**
 * @Author:周琳胜
 * @description: 淘宝分销退单转单关系表
 * @Date: 19/3/9 22:14
 */
@Data
public class IpTaobaoFxRefundRelation {
    /**
     * 淘宝退单中间表
     */
    private IpBTaobaoFxRefund taobaoFxRefund;
    /**
     * 对应原始定单商品明细(可有多条原单明细)
     */
    private List<OcBOrderItem> ocBOrderItems;
    /**
     * 对应原始定单赠品明细
     */
    private List<OcBOrderItem> ocBOrderGifts;
    /**
     * 对应有效原单（多条取第一条）
     */
    private OcBOrder ocBOrder;

    /**
     * 是否存在有效的原单
     */
    private boolean isEffectiveOrder;


    /**
     * @return
     */
    public long getOrderId() {
        if (taobaoFxRefund != null) {
            return taobaoFxRefund.getId();
        }
        return -1;
    }

    /**
     * @return
     */
    public String getOrderNo() {
        if (taobaoFxRefund != null) {
            return taobaoFxRefund.getXrefundId() + "";
        }
        return "";
    }

    /**
     * @return 转换后全渠道订单Id
     */
    public Long getOcOrderId() {
        if (ocBOrder != null) {
            return ocBOrder.getId();
        }
        return -1L;
    }
}