package com.jackrain.nea.oc.oms.model.relation;

/**
 * 订单HOLD单 常量
 *
 * @author: 江家雷
 * create at: 2020/07/04 16:01
 */
public class OcBOrderHoldConst {

    // 时间类型
    // 下单时间
    public final static Integer ORDER_DATE = 1;
    // 支付时间
    public final static Integer PAY_TIME = 2;

    // 时点类型
    // 指定时点释放
    public final static Integer RELEASE_TIME_TYPE_1 = 1;
    // 固定时长后释放
    public final static Integer RELEASE_TIME_TYPE_2 = 2;
    // 临近预计发货日释放
    public final static Integer RELEASE_TIME_TYPE_3 = 3;

    // 是否
    // Y 是
    public final static String YES = "Y";
    // N 否
    public final static String NO = "N";

    // 时间单位
    // 分钟
    public final static Integer TIME_UNIT_MINUTE = 1;
    // 小时
    public final static Integer TIME_UNIT_HOUR = 2;
    // 天
    public final static Integer TIME_UNIT_DAY = 3;

    // HOLD单状态值
    // hold单状态
    public final static Integer HOLD_ORDER_YES = 1;
    // 非hold单状态
    public final static Integer HOLD_ORDER_NO = 0;

    public  final  static  String NORMAL = "NORMAL";

    public  final  static  String PROVINCE = "PROVINCE";

    private OcBOrderHoldConst() {
    }
}
