package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.result.SaveOrderItemResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;

import java.util.List;

/**
 * @Author: wa<PERSON><PERSON><PERSON>
 * @Date: 2019-03-09 13:50
 * @Version 1.0
 */
@Data
public class OrderItemRelation {
    //订单主表
    private OcBOrder ocBorder;
    //条码、数量
    private List<SaveOrderItemResult> orderItemResult;
    //订单(条码)明细表
    private OcBOrderItem ocBorderItem;
}
