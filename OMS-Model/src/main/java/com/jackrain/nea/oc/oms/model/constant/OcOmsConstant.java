package com.jackrain.nea.oc.oms.model.constant;

/**
 * Description： 常量类
 * Author: RESET
 * Date: Created in 2020/7/6 16:12
 * Modified By:
 */
public interface OcOmsConstant {

    // 系统参数KEY：退单传WMS物流单号是否必填
    String KEY_SYS_PARAM_RETURN_ORDER_2_WMS_LOGISTICS_STRICT = "oc.oms.task.returnOrderToWms.logisticsNoStrict";

    // 系统参数KEY：退单退单无物流单号延迟推单
    String KEY_SYS_PARAM_OC_OMS_TASK_RETURNORDERTOWMS_PUSHDELAYTIME = "oc.oms.task.returnOrderToWms.pushDelayTime";


    // 空传WMS物流单号
    String DEFAULT_TO_WMS_LOGISTICS_NO = "000000";

    // 固定true/false
    String TRUE_STRING = "true";
    String FALSE_STRING = "false";

    // @20200823 add by wu.lb 增加消息消费日志表
    String OC_B_MESSAGE_CONSUME_LOG = "oc_b_message_consume_log";
}
