package com.jackrain.nea.oc.oms.model.Standplat;

/**
 * 通用订单交易类型
 *
 * @author: ming.fz
 * @since: 2019-07-02
 * create at : 2019-07-02 17:35
 */
public class IpBStandplatOrderType {

    /**
     * 交易类型
     */
    public static final String TYPE = "TRADE_FINISHED";

    /**
     * 电子奶卡销售
     */
    public static final String ONLINE_MILK_CARD = "ONLINE_MILK_CARD";

    /**
     * 实体奶卡销售
     */
    public static final String OFFLINE_MILK_CARD = "OFFLINE_MILK_CARD";

    /**
     * 小程序单品订单
     */
    public static final String MINI_PROGRAM = "MINI_PROGRAM";

    /**
     * 周期购订单
     */
    public static final String CYCLE = "CYCLE";

    /**
     * 奶卡提货订单
     */
    public static final String MILK_ORDER = "MILK_ORDER";

    /**
     * 周期购提货订单
     */
    public static final String CYCLE_ORDER = "CYCLE_ORDER";

}
