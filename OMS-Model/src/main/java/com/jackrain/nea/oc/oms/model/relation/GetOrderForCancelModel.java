package com.jackrain.nea.oc.oms.model.relation;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName GetOrderForCancelModel
 * @Description
 * <AUTHOR>
 * @Date 2023/10/9 15:32
 * @Version 1.0
 */
@Data
public class GetOrderForCancelModel implements Serializable {

    /**
     * 订单id
     */
    @JSONField(name = "ID")
    private Long id;

    /**
     * 单据编号
     */
    @JSONField(name = "BILL_NO")
    private String billNo;

    /**
     * 单据来源
     */
    @JSONField(name = "ORDER_SOURCE")
    private String orderSource;

    /**
     * 是否复制单
     */
    @J<PERSON>NField(name = "IS_COPY_ORDER")
    private Integer isCopyOrder;

    /**
     * 是否补发单
     */
    @J<PERSON>NField(name = "IS_RESET_SHIP")
    private Integer isResetShip;

    @JSONField(name = "PLATFORM")
    private Integer platform;
}
