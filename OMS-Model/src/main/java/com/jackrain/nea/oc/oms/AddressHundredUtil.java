package com.jackrain.nea.oc.oms;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName AddressHundredUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/20 11:00
 * @Version 1.0
 */
@Component
public class AddressHundredUtil {

    private static String getAddressContent(String address){
        Map<String, Object> param = new HashMap<>();
        param.put("secret_key", "bwPr7Y33RHZbBsDjs7");
        param.put("secret_code", "0db4f037e2094f15a811261722e8a6e9");
        param.put("secret_sign", SecureUtil.md5().digestHex("bwPr7Y33RHZbBsDjs7876a0bec81cd455e98aa1eab1e1dc079").toUpperCase());
        param.put("content", "北京市北京市西城区西便门西里");
        return HttpUtil.post("http://cloud.kuaidi100.com/api", param, 3000);
    }

    public static void main(String[] args) {
        Map<String, Object> param = new HashMap<>();
        param.put("secret_key", "bwPr7Y33RHZbBsDjs7");
        param.put("secret_code", "0db4f037e2094f15a811261722e8a6e9");
        param.put("secret_sign", SecureUtil.md5().digestHex("bwPr7Y33RHZbBsDjs7876a0bec81cd455e98aa1eab1e1dc079").toUpperCase());
        param.put("content", "北京市北京市西城区西便门西里");
        System.err.println(HttpUtil.post("http://cloud.kuaidi100.com/api", param, 3000));
    }

    public Map<String, String> addressResolution(String address){



        return null;
    }
}
