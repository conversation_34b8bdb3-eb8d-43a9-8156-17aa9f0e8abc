package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirect;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirectItem;
import com.jackrain.nea.ps.model.ProductSku;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: 黄世新
 * @Date: 2022/3/28 下午2:08
 * @Version 1.0
 */
@Data
public class IpJingdongDirectOrderRelation implements Serializable {

    private IpBJingdongDirect ipBJingdongDirect;


    private List<IpBJingdongDirectItem> ipBJingdongDirectItems;

    private Map<String, ProductSku> productSkuMap;



    public Long getOrderId(){
        if (ipBJingdongDirect != null) {
            return ipBJingdongDirect.getId();
        }
        return null;
    }

    public String getOrderNo(){
        if (ipBJingdongDirect != null) {
            return ipBJingdongDirect.getCustomOrderId();
        }
        return null;
    }
}
