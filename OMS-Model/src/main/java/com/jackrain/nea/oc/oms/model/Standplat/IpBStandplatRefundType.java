package com.jackrain.nea.oc.oms.model.Standplat;

/**
 * 通用平台 退单退货类型
 *
 * @author: 夏继超
 * @since: 2019/7/23
 * create at : 2019/7/23 9:10
 */
public class IpBStandplatRefundType {
    /**
     * 仅退款
     */
    public static final int ONLY_REFUND = 1;
    /**
     * 退货退款
     */
    public static final int RETURN_GOODS_RERUEN = 2;
    /**
     * 换货
     */
    public static final int EXCHANGE_GOODS = 3;

    /**
     * 补寄
     */
    public static final int RESEND = 4;
}
