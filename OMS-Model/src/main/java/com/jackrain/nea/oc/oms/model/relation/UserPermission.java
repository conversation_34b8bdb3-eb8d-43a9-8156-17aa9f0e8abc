package com.jackrain.nea.oc.oms.model.relation;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户权限
 *
 * @author: xiWen.z
 * create at: 2019/8/26 0026
 */
@Data
public class UserPermission implements Serializable {
    private static final long serialVersionUID = -1663368898763518942L;

    /**
     * 禁读
     */
    private Set<String> forbiddenColumns;

    /**
     * 敏感列
     */
    private List<DataPermission> sensitiveColumns;

    /**
     * 基础
     */
    private Map<String, BasePermission> basePermission;

    /**
     * version
     */
    private int version;

}
