package com.jackrain.nea.ipcs.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName LabelRequirementsRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/4 11:17
 * @Version 1.0
 */
@Data
public class LabelRequirementsRequest implements Serializable {
    private static final long serialVersionUID = -3953249503630945841L;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * ON单号
     */
    private String deliveryOrderCode;

    /**
     * 单据类型
     */
    private String vasType;

    private String udf01;

    /**
     * OM单号
     */
    private String erpTradeNo;

    /**
     * 平台单号
     */
    private String sourceOrderCode;

    /**
     * 库存地点
     */
    private String storageLocation;

    /**
     * 增值服务详情
     */
    private List<LabelRequirementsItemRequest> details;
}
