package com.jackrain.nea.ipcs.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName LabelRequirementsCancelRequest
 * @Description 增值服务取消
 * <AUTHOR>
 * @Date 2024/11/6 16:30
 * @Version 1.0
 */
@Data
public class LabelRequirementsCancelRequest implements Serializable {
    private static final long serialVersionUID = -1715477093686135122L;

    private String orderCode;

    private String orderType;

    private String ownerCode;

    private String warehouseCode;
}
