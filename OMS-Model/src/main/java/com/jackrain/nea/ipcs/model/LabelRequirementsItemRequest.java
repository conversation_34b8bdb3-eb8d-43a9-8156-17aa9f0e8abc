package com.jackrain.nea.ipcs.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName LabelRequirementsItemRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/4 11:18
 * @Version 1.0
 */
@Data
public class LabelRequirementsItemRequest implements Serializable {
    private static final long serialVersionUID = -3414861464861031770L;

    /**
     * 货主代码
     */
    private String ownerCode;

    /**
     * sku
     */
    private String itemCode;

    /**
     * 数量
     */
    private String planQty;

    /**
     * 服务类型
     */
    private String services;

    /**
     * 生产日期
     */
    private String batchCode;

    /**
     * 质量状态
     */
    private String inventoryType;
}
