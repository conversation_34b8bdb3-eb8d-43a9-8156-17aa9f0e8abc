package com.jackrain.nea.resource;

import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;

/**
 * WMS用户资源定义
 * <p>
 *
 * @author: 胡林楊
 * @since: 2019-06-13
 * create at : 2019-06-13 10:26
 */
public class WmsUserResource {
    private WmsUserResource() {

    }

    public static User getWmsUser() {

        User user = new UserImpl();
        ((UserImpl) user).setEname("WMS");
        ((UserImpl) user).setName("WMS");
        ((UserImpl) user).setId(666);
        ((UserImpl) user).setOrgId(27);
        ((UserImpl) user).setClientId(37);
        return user;
    }
}
