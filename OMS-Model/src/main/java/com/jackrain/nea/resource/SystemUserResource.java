package com.jackrain.nea.resource;

import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;

/**
 * 系统用户资源定义
 * <p>
 * 包括：AD_CLIENT_ID,AD_ORG_ID等
 *
 * @author: 易邵峰
 * @since: 2019-03-07
 * create at : 2019-03-07 17:26
 */
public class SystemUserResource {

    public static final Long AD_CLIENT_ID = 37L;

    public static final Long AD_ORG_ID = 27L;

    public static final String ROOT_USER_NAME = "root";

    public static final String ROOT_ENAME = "系统管理员";


    public static final Long ROOT_USER_ID = 893L;

    public static final String SAP_USER_NAME = "SAP";

    public static final String SAP_ENAME = "SAP";

    public static final Long SAP_USER_ID = 1003L;

    public static final Long MINI_PROGRAM_ID = 1615L;

    public static final String MINI_PROGRAM_NAME = "小程序";

    public static final String DMS_USER_NAME = "DMS";

    public static final String DMS_ENAME = "DMS";

    public static final Long DMS_USER_ID = 1003L;

    private SystemUserResource() {

    }

    /**
     * 获取小程序数据
     *
     * @return
     */
    public static User getMiniProgram() {
        User user = new UserImpl();
        ((UserImpl) user).setId(MINI_PROGRAM_ID.intValue());
        ((UserImpl) user).setEname(MINI_PROGRAM_NAME);
        ((UserImpl) user).setTruename(MINI_PROGRAM_NAME);
        ((UserImpl) user).setClientId(AD_CLIENT_ID.intValue());
        ((UserImpl) user).setOrgId(AD_ORG_ID.intValue());
        ((UserImpl) user).setName(MINI_PROGRAM_NAME);
        return user;
    }

    /**
     * 返回默认的系统Root用户
     *
     * @return 默认的系统Root用户
     */
    public static User getRootUser() {
        User user = new UserImpl();
        ((UserImpl) user).setId(ROOT_USER_ID.intValue());
        ((UserImpl) user).setEname(ROOT_ENAME);
        ((UserImpl) user).setTruename(ROOT_USER_NAME);
        ((UserImpl) user).setClientId(AD_CLIENT_ID.intValue());
        ((UserImpl) user).setOrgId(AD_ORG_ID.intValue());
        ((UserImpl) user).setName(ROOT_USER_NAME);


        return user;
    }


    /**
     * 返回默认的系统Sap用户
     *
     * @return 默认的系统Sap用户
     */
    public static User getSapUser() {
        User user = new UserImpl();
        ((UserImpl) user).setId(SAP_USER_ID.intValue());
        ((UserImpl) user).setEname(SAP_ENAME);
        ((UserImpl) user).setTruename(SAP_USER_NAME);
        ((UserImpl) user).setClientId(AD_CLIENT_ID.intValue());
        ((UserImpl) user).setOrgId(AD_ORG_ID.intValue());
        ((UserImpl) user).setName(SAP_USER_NAME);


        return user;
    }

    /**
     * 返回默认的系统Dms用户
     *
     * @return 默认的系统Dms用户
     */
    public static User getDmsUser() {
        User user = new UserImpl();
        ((UserImpl) user).setId(DMS_USER_ID.intValue());
        ((UserImpl) user).setEname(DMS_ENAME);
        ((UserImpl) user).setTruename(DMS_USER_NAME);
        ((UserImpl) user).setClientId(AD_CLIENT_ID.intValue());
        ((UserImpl) user).setOrgId(AD_ORG_ID.intValue());
        ((UserImpl) user).setName(DMS_USER_NAME);


        return user;
    }
}
