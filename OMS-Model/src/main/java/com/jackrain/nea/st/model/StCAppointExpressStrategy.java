package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName StCAppointExpressStrategy
 * @Description 指定快递策略
 * <AUTHOR>
 * @Date 2024/4/12 10:20
 * @Version 1.0
 */
@Data
@TableName("st_c_appoint_express_strategy")
public class StCAppointExpressStrategy implements Serializable {
    private static final long serialVersionUID = 3273237720655557427L;

    /**
     * id
     */
    private Long id;

    /**
     * 策略编码
     */
    @JSONField(name = "STRATEGY_CODE")
    private String strategyCode;

    /**
     * 1.公共 2.指定
     */
    @JSONField(name = "STRATEGY_TYPE")
    private Integer strategyType;

    /**
     * 店铺id
     */
    @JSONField(name = "SHOP_ID")
    private Long shopId;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;

    /**
     * 所属公司
     */
    private Long adClientId;

    /**
     * 所属组织
     */
    private Long adOrgId;

    /**
     * 是否启用（Y:启用，N：未启用）
     */
    @JSONField(name = "ISACTIVE")
    private String isactive;

    /**
     * 创建人
     */
    @JSONField(name = "OWNERID")
    private Long ownerid;

    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    /**
     * 创建人名称
     */
    @JSONField(name = "OWNERNAME")
    private String ownername;

    /**
     * 创建时间
     */
    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    /**
     * 修改人
     */
    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    /**
     * 修改人名称
     */
    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    /**
     * 修改时间
     */
    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;
}
