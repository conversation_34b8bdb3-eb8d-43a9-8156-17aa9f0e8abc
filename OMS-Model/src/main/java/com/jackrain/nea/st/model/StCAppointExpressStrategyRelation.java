package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * @ClassName StCAppointExpressStrategyRelation
 * @Description 指定快递策略关联
 * <AUTHOR>
 * @Date 2024/4/12 16:11
 * @Version 1.0
 */
@Data
public class StCAppointExpressStrategyRelation {

    @JSONField(name = "ST_C_PREORDER_MODEL_STRATEGY")
    private StCAppointExpressStrategy stCAppointExpressStrategy;

    @JSONField(name = "ST_C_APPOINT_EXPRESS_STRATEGY_DETAIL")
    private List<StCAppointExpressStrategyDetail> stCAppointExpressStrategyDetailList;
}
