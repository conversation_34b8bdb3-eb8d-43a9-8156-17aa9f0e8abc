package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 13:57
 */
@TableName(value = "st_c_send_plan")
@Data
public class StCSendPlan extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "ECODE")
    private String ecode;

    @JSONField(name = "ENAME")
    private String ename;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @J<PERSON>NField(name = "BEGIN_TIME")
    private Date beginTime;

    @JSO<PERSON>ield(name = "END_TIME")
    private Date endTime;

    @JSONField(name = "RANK")
    private Integer rank;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "CHECKID")
    private Long checkid;

    @JSONField(name = "CHECKENAME")
    private String checkename;

    @JSONField(name = "CHECKNAME")
    private String checkname;

    @JSONField(name = "CHECKTIME")
    private Date checktime;

    @JSONField(name = "REVERSE_ID")
    private Long reverseId;

    @JSONField(name = "REVERSE_ENAME")
    private String reverseEname;

    @JSONField(name = "REVERSE_NAME")
    private String reverseName;

    @JSONField(name = "REVERSE_TIME")
    private Date reverseTime;

    @JSONField(name = "ESTATUS")
    private Integer estatus;

    @JSONField(name = "FINISHID")
    private Long finishid;

    @JSONField(name = "FINISHENAME")
    private String finishename;

    @JSONField(name = "FINISHNAME")
    private String finishname;

    @JSONField(name = "FINISHTIME")
    private Date finishtime;
}