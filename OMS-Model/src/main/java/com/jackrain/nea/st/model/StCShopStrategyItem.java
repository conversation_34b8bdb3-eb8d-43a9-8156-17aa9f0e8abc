package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ST_C_SHOP_STRATEGY_ITEM")
@Data
public class StCShopStrategyItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_SHOP_STRATEGY_ID")
    private Long stCShopStrategyId;

    @JSONField(name = "DIFFPRICESKU")
    private Long diffpricesku;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @J<PERSON><PERSON>ield(name = "MODIFIERENAME")
    private String modifierename;
}