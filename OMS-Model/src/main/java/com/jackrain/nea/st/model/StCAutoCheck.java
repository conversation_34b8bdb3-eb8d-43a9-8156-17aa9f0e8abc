package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 13:45
 */
@TableName(value = "st_c_autocheck")
@Data
public class StCAutoCheck extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "IS_AUTOCHECK_ORDER")
    private String isAutocheckOrder;

    @JSONField(name = "IS_AUTOCHECK_PAY")
    private String isAutocheckPay;

    @JSONField(name = "IS_REMARK_AUTOCHECK")
    private String isRemarkAutocheck;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @JSONField(name = "CP_C_REGION_PROVINCE_ECODE")
    private String cpCRegionProvinceEcode;

    @JSONField(name = "CP_C_REGION_PROVINCE_ENAME")
    private String cpCRegionProvinceEname;

    @JSONField(name = "BEGIN_TIME")
    private Date beginTime;

    @JSONField(name = "END_TIME")
    private Date endTime;

    @JSONField(name = "LIMIT_PRICE_UP")
    private BigDecimal limitPriceUp;

    @JSONField(name = "LIMIT_PRICE_DOWN")
    private BigDecimal limitPriceDown;


    @JSONField(name = "WAIT_TIME")
    private Integer waitTime;

    @JSONField(name = "KEYWORD")
    private String keyword;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "IS_FULL_GIFT_ORDER")
    private String isFullGiftOrder;

    @JSONField(name = "IS_MANUAL_ORDER")
    private String isManualOrder;

    @JSONField(name = "IS_AUTOCHECK_EXCHANGE")
    private String isAutocheckExchange;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private String cpCLogisticsId;

    @JSONField(name = "CP_C_REGION_PROVINCE_ID")
    private String cpCRegionProvinceId;

    @JSONField(name = "BUYER_REMARK")
    private String buyerRemark;

    @JSONField(name = "SELLER_REMARK")
    private String sellerRemark;

    @JSONField(name = "EXCLUDE_SKU_TYPE")
    private Integer excludeSkuType;

    @JSONField(name = "SKU_CONTENT")
    private String skuContent;

    @JSONField(name = "ORDER_TYPE")
    private String orderType;

    @JSONField(name = "SPECIAL_ORDER")
    private String specialOrder;

    @JSONField(name = "EFFECTIVE_CONDITION")
    private String effectiveCondition;

    @JSONField(name = "IS_SELLER_REMARK_AUTOCHECK")
    private String isSellerRemarkAutocheck;

    @JSONField(name = "LIMIT_PRICE")
    private BigDecimal limitPrice;

    @JSONField(name = "IS_MERGE_ORDER")
    private String isMergeOrder;

    @JSONField(name = "HOLD_WAIT_TIME")
    private Integer holdWaitTime;

    @JSONField(name = "ANTI_AUDIT_WAIT_TIME")
    private Integer antiAuditWaitTime;

    @JSONField(name = "RECEIVER_ADDRESS")
    private String receiverAddress;

    @JSONField(name = "ORDER_DISCOUNT_UP")
    private BigDecimal orderDiscountUp;

    @JSONField(name = "ORDER_DISCOUNT_DOWN")
    private BigDecimal orderDiscountDown;

    @JSONField(name = "SINGLE_SKU_NUM")
    private Integer singleSkuNum;

    // 自定义标签档案id
    @JSONField(name = "ST_C_CUSTOM_LABEL_ID")
    private String stCCustomLabelId;

    // 自定义标签档案ename
    @JSONField(name = "ST_C_CUSTOM_LABEL_ENAME")
    private String stCCustomLabelEname;

    /**
     * 排除仓库ID
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_IDS")
    private String cpCPhyWarehouseIds;

    /**
     * 排除仓库名称
     */
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAMES")
    private String cpCPhyWarehouseEnames;

    /**
     * 排除业务类型ID
     */
    @JSONField(name = "ST_C_BUSINESS_TYPE_IDS")
    private String stCBusinessTypeIds;

    /**
     * 排除业务类型名称
     */
    @JSONField(name = "ST_C_BUSINESS_TYPE_ENAMES")
    private String stCBusinessTypeEnames;
}