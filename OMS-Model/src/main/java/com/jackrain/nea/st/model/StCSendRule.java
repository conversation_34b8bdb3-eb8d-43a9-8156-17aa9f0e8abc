package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 14:02
 */
@TableName(value = "ST_C_SEND_RULE")
@Data
public class StCSendRule extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ECODE")
    private String ecode;

    @J<PERSON>NField(name = "ENAME")
    private String ename;

    @JSONField(name = "ETYPE")
    private String etype;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "IS_DEL")
    private Integer isDel;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "<PERSON><PERSON><PERSON><PERSON>RE<PERSON><PERSON>")
    private String modifierename;

    @JSONField(name = "<PERSON>L<PERSON>")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;
}