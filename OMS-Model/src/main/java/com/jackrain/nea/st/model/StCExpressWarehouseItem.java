package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ST_C_EXPRESS_WAREHOUSE_ITEM")
@Data
public class StCExpressWarehouseItem extends BaseModel {
    @J<PERSON><PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON><PERSON>ield(name = "ST_C_EXPRESS_ID")
    private Long stCExpressId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @J<PERSON>NField(name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String ownerename;

    @J<PERSON><PERSON>ield(name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String modifierename;
}