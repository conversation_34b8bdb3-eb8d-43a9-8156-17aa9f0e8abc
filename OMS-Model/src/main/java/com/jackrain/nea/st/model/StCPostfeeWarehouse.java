package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ST_C_POSTFEE_WAREHOUSE")
@Data
public class StCPostfeeWarehouse extends BaseModel {
    @J<PERSON><PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_POSTFEE_ID")
    private Long stCPostfeeId;

    @J<PERSON>NField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @J<PERSON><PERSON>ield(name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String ownerename;

    @<PERSON><PERSON><PERSON>ield(name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>")
    private String modifierename;
}