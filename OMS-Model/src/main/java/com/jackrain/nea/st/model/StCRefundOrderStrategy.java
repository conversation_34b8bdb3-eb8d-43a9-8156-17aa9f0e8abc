package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 13:54
 */
@TableName(value = "ST_C_REFUND_ORDER_STRATEGY")
@Data
public class StCRefundOrderStrategy extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "BEGIN_TIME")
    private Date beginTime;

    @JSONField(name = "END_TIME")
    private Date endTime;

    @J<PERSON>NField(name = "REMARK")
    private String remark;

    @J<PERSON><PERSON>ield(name = "DELER_ID")
    private Long delerId;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}