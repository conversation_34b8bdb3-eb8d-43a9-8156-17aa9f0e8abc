package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "st_c_send_plan_item")
@Data
public class StCSendPlanItem extends BaseModel {
    @J<PERSON><PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_SEND_PLAN_ID")
    private Long stCSendPlanId;

    @<PERSON><PERSON><PERSON><PERSON>(name = "ST_C_SEND_RULE_ID")
    private Long stCSendRuleId;

    @J<PERSON><PERSON>ield(name = "ST_C_SEND_RULE_ECODE")
    private String stCSendRuleEcode;

    @<PERSON><PERSON><PERSON><PERSON>(name = "ST_C_SEND_RULE_ENAME")
    private String stCSendRuleEname;

    @<PERSON><PERSON><PERSON>ield(name = "<PERSON><PERSON><PERSON><PERSON>")
    private String remark;

    @<PERSON><PERSON><PERSON>ield(name = "RANK")
    private Long rank;

    @<PERSON><PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @<PERSON><PERSON><PERSON>ield(name = "MODIFIERENAME")
    private String modifierename;
}