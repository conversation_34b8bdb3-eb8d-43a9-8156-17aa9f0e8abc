package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "st_c_express_package")
@Data
public class StCExpressPackage extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_EXPRESS_ID")
    private Long stCExpressId;

    @J<PERSON><PERSON>ield(name = "PKG_ATTRIBUTE")
    private String pkgAttribute;

    @J<PERSON><PERSON>ield(name = "CONDITIONS")
    private String conditions;

    @JSONField(name = "BEGIN_VAL")
    private String beginVal;

    @J<PERSON><PERSON>ield(name = "END_VAL")
    private String endVal;

    @J<PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @J<PERSON>NField(name = "MODIFIERENAME")
    private String modifierename;
}