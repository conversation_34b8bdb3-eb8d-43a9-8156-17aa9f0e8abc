package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 14:03
 */
@TableName(value = "ST_C_SYNC_STOCK_STRATEGY")
@Data
public class StCSyncStockStrategy extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "STOCK_RATE")
    private BigDecimal stockRate;

    @JSONField(name = "LOW_STOCK")
    private Long lowStock;

    @JSONField(name = "IS_SYNC_STOCK")
    private Integer isSyncStock;

    @JSONField(name = "OW<PERSON>RENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}