package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ST_C_EXPRESS_LOGISTICS_ITEM")
@Data
public class StCExpressLogisticsItem extends BaseModel {
    @J<PERSON><PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ST_C_EXPRESS_ID")
    private Long stCExpressId;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @J<PERSON><PERSON>ield(name = "<PERSON>W<PERSON>RENAME")
    private String ownerename;

    @J<PERSON>NField(name = "<PERSON><PERSON>IF<PERSON>RENA<PERSON>")
    private String modifierename;
}