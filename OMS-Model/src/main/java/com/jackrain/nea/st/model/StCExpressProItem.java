package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "st_c_express_pro_item")
@Data
public class StCExpressProItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_EXPRESS_ID")
    private Long stCExpressId;

    @<PERSON><PERSON><PERSON><PERSON>(name = "PS_C_PRO_ID")
    private Long psCProId;

    @J<PERSON><PERSON><PERSON>(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @J<PERSON><PERSON>ield(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @<PERSON><PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @<PERSON><PERSON><PERSON><PERSON>(name = "MODIFIERENAME")
    private String modifierename;
}