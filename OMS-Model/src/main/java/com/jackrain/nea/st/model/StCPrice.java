package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 13:47
 */
@TableName(value = "st_c_price")
@Data
public class StCPrice extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "ECODE")
    private String ecode;

    @JSONField(name = "ENAME")
    private String ename;

    @JSONField(name = "BEGIN_TIME")
    private Date beginTime;

    @JSONField(name = "END_TIME")
    private Date endTime;

    @JSONField(name = "ACTIVE_TYPE")
    private Integer activeType;

    @JSONField(name = "RANK")
    private Long rank;

    @JSONField(name = "REMAR<PERSON>")
    private String remark;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "CHECKID")
    private Long checkid;

    @JSONField(name = "CHECKENAME")
    private String checkename;

    @JSONField(name = "CHECKNAME")
    private String checkname;

    @JSONField(name = "CHECKTIME")
    private Date checktime;

    @JSONField(name = "REVERSE_ID")
    private Long reverseId;

    @JSONField(name = "REVERSE_ENAME")
    private String reverseEname;

    @JSONField(name = "REVERSE_NAME")
    private String reverseName;

    @JSONField(name = "REVERSE_TIME")
    private Date reverseTime;

    @JSONField(name = "CLOSE_ID")
    private Long closeId;

    @JSONField(name = "CLOSE_ENAME")
    private String closeEname;

    @JSONField(name = "CLOSE_NAME")
    private String closeName;

    @JSONField(name = "CLOSE_TIME")
    private Date closeTime;
}