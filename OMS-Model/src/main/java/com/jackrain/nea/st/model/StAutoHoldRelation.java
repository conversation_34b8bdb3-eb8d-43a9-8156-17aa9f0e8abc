package com.jackrain.nea.st.model;

import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderItemDO;
import com.jackrain.nea.oc.oms.model.table.StCHoldProvinceItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/8/7 下午4:54
 * @Version 1.0
 */
@Data
public class StAutoHoldRelation implements Serializable {

    private StCHoldOrderDO stCHoldOrder;

    private List<StCHoldOrderItemDO> stCHoldOrderItems;

    private List<StCHoldProvinceItemDO> stCHoldProvinceItems;
}
