package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "st_c_express_allocation_item")
@Data
public class StCExpressAllocationItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_EXPRESS_ALLOCATION_ID")
    private Long stCExpressAllocationId;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @JSONField(name = "SCALE")
    private BigDecimal scale;

    @JSONField(name = "LIMIT_NUM")
    private BigDecimal limitNum;

    @JSONField(name = "SEND_NUM")
    private BigDecimal sendNum;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}