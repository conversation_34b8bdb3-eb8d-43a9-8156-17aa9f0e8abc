package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ST_C_SHOP_STRATEGY")
@Data
public class StCShopStrategy extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @J<PERSON><PERSON>ield(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @J<PERSON><PERSON>ield(name = "POSTAGE_CODE")
    private String postageCode;

    @JSONField(name = "DEFAULT_STORE_ID")
    private Long defaultStoreId;

    @JSONField(name = "IS_AG")
    private String isAg;

    @J<PERSON>NField(name = "IS_AUTO_SPLIT")
    private String isAutoSplit;

    @JSONField(name = "IS_AUTO_MATCH")
    private String isAutoMatch;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}