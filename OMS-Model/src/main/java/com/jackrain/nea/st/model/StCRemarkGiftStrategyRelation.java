package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.StCRemarkGiftStrategy;
import lombok.Data;

import java.io.Serializable;

/**
 * 备注赠品策略relation
 *
 * <AUTHOR>
 */
@Data
public class StCRemarkGiftStrategyRelation implements Serializable {

    private static final long serialVersionUID = 2304036725790134514L;

    @JSONField(name = "ST_C_REMARK_GIFT_STRATEGY")
    private StCRemarkGiftStrategy stCRemarkGiftStrategy;

}
