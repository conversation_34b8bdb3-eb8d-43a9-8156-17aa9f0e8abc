package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategy;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategyItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 残次策略relation
 *
 * <AUTHOR>
 */
@Data
public class StCImperfectStrategyRelation implements Serializable {

    private static final long serialVersionUID = -8372507695771877258L;

    @JSONField(name = "ST_C_IMPERFECT_STRATEGY")
    private StCImperfectStrategy stCImperfectStrategy;

    @JSONField(name = "ST_C_IMPERFECT_STRATEGY_ITEM")
    private List<StCImperfectStrategyItem> stCImperfectStrategyItems;

}
