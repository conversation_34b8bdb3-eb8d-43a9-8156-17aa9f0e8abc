package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ST_C_OPERATIONCOST")
@Data
public class StCOperationcost extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "ENAME")
    private String ename;

    @J<PERSON>NField(name = "BILL_STATUS")
    private Integer billStatus;

    @JSONField(name = "BEGIN_TIME")
    private Date beginTime;

    @JSONField(name = "END_TIME")
    private Date endTime;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @J<PERSON><PERSON><PERSON>(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @JSONField(name = "ORDER_TYPE")
    private Integer orderType;

    @JSONField(name = "ACCESSORYPRICE")
    private BigDecimal accessoryprice;

    @JSONField(name = "PLAN_DESC")
    private String planDesc;

    @JSONField(name = "UNIT")
    private String unit;

    @JSONField(name = "ACCESSORYUNIT")
    private String accessoryunit;

    @JSONField(name = "CHARGEPRICE")
    private BigDecimal chargeprice;

    @JSONField(name = "CHARGEUNIT")
    private BigDecimal chargeunit;

    @JSONField(name = "CONTINUEPRICE")
    private BigDecimal continueprice;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "FINISHID")
    private Long finishid;

    @JSONField(name = "FINISHENAME")
    private String finishename;

    @JSONField(name = "FINISHNAME")
    private String finishname;

    @JSONField(name = "FINISHTIME")
    private Date finishtime;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "DELID")
    private Long delid;

    @JSONField(name = "DELENAME")
    private String delename;

    @JSONField(name = "DELNAME")
    private String delname;

    @JSONField(name = "DEL_TIME")
    private Date delTime;

    @JSONField(name = "CHECKID")
    private Long checkid;

    @JSONField(name = "CHECKENAME")
    private String checkename;

    @JSONField(name = "CHECKNAME")
    private String checkname;

    @JSONField(name = "CHECKTIME")
    private Date checktime;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private String cpCPhyWarehouseId;
}