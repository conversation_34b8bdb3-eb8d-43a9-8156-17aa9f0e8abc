package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * <AUTHOR> lin yu
 * @date : 2022/7/28 上午11:53
 * @describe :
 */

@TableName(value = "st_c_autocheck_auto_time")
@Data
public class StCAutoCheckAutoTime extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_AUTOCHECK_ID")
    private Long stCAutocheckId;

    @JSONField(name = "START_TIME")
    private String startTime;

    @JSO<PERSON>ield(name = "END_TIME")
    private String endTime;

}
