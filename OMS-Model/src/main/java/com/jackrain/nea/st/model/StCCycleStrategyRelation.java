package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.StCCycleItemStrategy;
import com.jackrain.nea.oc.oms.model.table.StCCycleRuleStrategy;
import com.jackrain.nea.oc.oms.model.table.StCCycleStrategy;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName StCCycleStrategyRelation
 * @Description 周期购促销
 * <AUTHOR>
 * @Date 2024/8/19 15:40
 * @Version 1.0
 */
@Data
public class StCCycleStrategyRelation implements Serializable {

    private static final long serialVersionUID = -467213592517902539L;

    @JSONField(name = "ST_C_CYCLE_STRATEGY")
    private StCCycleStrategy stCCycleStrategy;

    @JSONField(name = "ST_C_CYCLE_RULE_STRATEGY")
    private List<StCCycleRuleStrategy> stCCycleRuleStrategy;

    @JSONField(name = "ST_C_CYCLE_ITEM_STRATEGY")
    private List<StCCycleItemStrategy> stCCycleItemStrategy;
}
