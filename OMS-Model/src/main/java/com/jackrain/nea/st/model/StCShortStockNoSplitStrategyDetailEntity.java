package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (StCShortStockNoSplitStrategyDetail)表实体类
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Data
@TableName("st_c_short_stock_no_split_strategy_detail")
public class StCShortStockNoSplitStrategyDetailEntity implements Serializable {
    private static final long serialVersionUID = -3581438841993176016L;
    /**
     * id
     */
    private Long id;

    /**
     * 策略id
     */
    @JSONField(name = "STRATEGY_ID")
    private Long strategyId;

    /**
     * 识别规则(1.平台商品id 2.SKU 3.四级 4.一级)
     */
    @JSONField(name = "MATCH_RULE")
    private String matchRule;

    /**
     * 识别内容
     */
    @JSONField(name = "MATCH_CONTENT")
    private String matchContent;

    /**
     * 不拆失效/秒
     */
    @JSONField(name = "AGEING")
    private String ageing;

    /**
     * 所属公司
     */
    private Long adClientId;

    /**
     * 所属组织
     */
    private Long adOrgId;

    /**
     * 是否启用（Y:启用，N：未启用）
     */
    @JSONField(name = "ISACTIVE")
    private String isactive;

    /**
     * 创建人
     */
    @JSONField(name = "OWNERID")
    private Long ownerid;

    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    /**
     * 创建人名称
     */
    @JSONField(name = "OWNERNAME")
    private String ownername;

    /**
     * 创建时间
     */
    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    /**
     * 修改人
     */
    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    /**
     * 修改人名称
     */
    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    /**
     * 修改时间
     */
    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;
}

