package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName StCCycleStrategyDelRelation
 * @Description 周期购促销策略删除
 * <AUTHOR>
 * @Date 2024/8/20 11:18
 * @Version 1.0
 */
@Data
public class StCCycleStrategyDelRelation implements Serializable {
    private static final long serialVersionUID = 4671614408999503141L;

    @JSONField(name = "ST_C_CYCLE_STRATEGY")
    private Long id;

    @JSONField(name = "ST_C_CYCLE_RULE_STRATEGY")
    private List<Long> stCCycleRuleStrategy;

    @JSONField(name = "ST_C_CYCLE_ITEM_STRATEGY")
    private List<Long> stCCycleItemStrategy;
}
