package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 14:08
 */
@TableName(value = "st_c_sync_stock_strategy_item")
@Data
public class StCSyncStockStrategyItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_SYNC_STOCK_STRATEGY_ID")
    private Long stCSyncStockStrategyId;

    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @JSONField(name = "PRIORITY")
    private Integer priority;

    @JSONField(name = "RATE")
    private BigDecimal rate;

    @JSONField(name = "LOW_STOCK")
    private Long lowStock;

    @JSONField(name = "IS_SEND")
    private Integer isSend;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}