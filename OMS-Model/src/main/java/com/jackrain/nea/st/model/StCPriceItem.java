package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 13:48
 */

@TableName(value = "st_c_price_item")
@Data
public class StCPriceItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_PRICE_ID")
    private Long stCPriceId;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "QTY_LOWPRICE")
    private BigDecimal qtyLowprice;

    @JSONField(name = "PRICE_SALE")
    private BigDecimal priceSale;

    @JSONField(name = "ACTIVE_PRICE")
    private BigDecimal activePrice;

    @JSONField(name = "PRICE_LIST")
    private BigDecimal priceList;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}