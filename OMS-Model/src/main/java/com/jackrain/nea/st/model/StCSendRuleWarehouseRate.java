package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 14:05
 */
@TableName(value = "st_c_send_rule_warehouse_rate")
@Data
public class StCSendRuleWarehouseRate extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_SEND_RULE_ID")
    private Long stCSendRuleId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @JSONField(name = "RANK")
    private Long rank;

    @JSONField(name = "SEND_RATE")
    private BigDecimal sendRate;

    @JSONField(name = "QTY_SEND")
    private BigDecimal qtySend;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}