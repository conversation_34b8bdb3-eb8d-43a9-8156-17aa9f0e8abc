package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName StCAppointExpressStrategyDetail
 * @Description 指定快递策略明细
 * <AUTHOR>
 * @Date 2024/4/12 10:28
 * @Version 1.0
 */
@Data
@TableName("st_c_appoint_express_strategy_detail")
public class StCAppointExpressStrategyDetail implements Serializable {
    private static final long serialVersionUID = -3747886412563287109L;

    /**
     * id
     */
    private Long id;

    /**
     * 策略id
     */
    @JSONField(name = "STRATEGY_ID")
    private Long strategyId;

    /**
     * 识别规则(1.平台商品id 2.SKU)
     */
    @JSONField(name = "MATCH_RULE")
    private String matchRule;

    /**
     * 识别内容
     */
    @JSONField(name = "MATCH_CONTENT")
    private String matchContent;

    /**
     * 失效/秒
     */
    @JSONField(name = "AGEING")
    private String ageing;

    /**
     * 指定快递公司
     */
    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    /**
     * 所属公司
     */
    private Long adClientId;

    /**
     * 所属组织
     */
    private Long adOrgId;

    /**
     * 是否启用（Y:启用，N：未启用）
     */
    @JSONField(name = "ISACTIVE")
    private String isactive;

    /**
     * 创建人
     */
    @JSONField(name = "OWNERID")
    private Long ownerid;

    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    /**
     * 创建人名称
     */
    @JSONField(name = "OWNERNAME")
    private String ownername;

    /**
     * 创建时间
     */
    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    /**
     * 修改人
     */
    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    /**
     * 修改人名称
     */
    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    /**
     * 修改时间
     */
    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;
}
