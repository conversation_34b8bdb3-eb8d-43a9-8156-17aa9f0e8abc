package com.jackrain.nea.st.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 13:50
 */
@TableName(value = "st_c_price_shop")
@Data
public class StCPriceShop extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ST_C_PRICE_ID")
    private Long stCPriceId;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}