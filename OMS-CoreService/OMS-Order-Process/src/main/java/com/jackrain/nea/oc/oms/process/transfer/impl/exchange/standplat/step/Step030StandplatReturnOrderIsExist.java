package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.standplat.step;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefudStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.ExchangeOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/12/1 10:03 上午
 * @Version 1.0
 */
@Step(order = 30, description = "判断是否生成退换货单和换货订单")
@Slf4j
@Component
public class Step030StandplatReturnOrderIsExist extends BaseStandplatExchangeProcessStep implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        if(log.isDebugEnabled()){
            log.debug("BaseStandplatExchangeProcessStep.Step030StandplatReturnOrderIsExist start >>> orderInfo：{}", JSON.toJSONString(orderInfo));
        }
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        List<IpBStandplatRefundItem> ipBStandplatRefundItem = orderInfo.getIpBStandplatRefundItem();
        try {
            //换货数量
            BigDecimal qty = ipBStandplatRefundItem.get(0).getExchangeQty();
            String disputeId = ipBStandplatRefund.getReturnNo();
            //换货的sku
            String sku = orderInfo.getProductSku().getSkuEcode();
            ExchangeOrderRelation relation = standplatExchangeService.selectReturnOrderList(ipBStandplatRefund);
            if (relation != null) {
                Integer flag = relation.getFlag();
                //换货状态
                Integer status = ipBStandplatRefund.getReturnStatus();
                //判断是否需要
                List<OcBReturnOrder> ocBReturnOrders = relation.getOcBReturnOrders();
                //是否存在换货类型的退单
                ocBReturnOrders = ocBReturnOrders.stream().filter(p -> TaobaoReturnOrderExt.BillType.EXCHANGE.getCode().equals(p.getBillType())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(ocBReturnOrders)) {
                    if (IpBStandplatRefudStatusEnum.CLOSED.getVal().equals(status)) {
                        String message = "退款关闭,不存在退换货类型的退换货单,直接标记为已转换";
                        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), message, ipBStandplatRefund);
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    }
                    String message = "存在退货类型的退换货单,不自动转换,转换失败";
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, ipBStandplatRefund);
                    return new ProcessStepResult<>(StepStatus.FAILED, message);
                }
                //存在退换货类型的退货单 则比对换货明细的sku以及换货数量是否一致
                for (OcBReturnOrder ocBReturnOrder : ocBReturnOrders) {
                    List<OcBReturnOrderExchange> ocBReturnOrderExchanges = standplatExchangeService.selectReturnOrderExchangeList(ocBReturnOrder.getId());
                    if (flag == 1 && ocBReturnOrder.getTbDisputeId().equals(Long.valueOf(disputeId))) {
                        orderInfo.setOcBReturnOrder(ocBReturnOrder);
                        orderInfo.setOcBReturnOrderExchanges(ocBReturnOrderExchanges);
                        break;
                    }
                    if (CollectionUtils.isNotEmpty(ocBReturnOrderExchanges) && ocBReturnOrderExchanges.size() == 1) {
                        OcBReturnOrderExchange ocBReturnOrderExchange = ocBReturnOrderExchanges.get(0);
                        //换货数量
                        BigDecimal qtyExchange = ocBReturnOrderExchange.getQtyExchange();
                        //sku信息
                        String psCSkuEcode = ocBReturnOrderExchange.getPsCSkuEcode();
                        if (qtyExchange.compareTo(qty) == 0 && sku.equals(psCSkuEcode)) {
                            orderInfo.setOcBReturnOrder(ocBReturnOrder);
                            orderInfo.setOcBReturnOrderExchanges(ocBReturnOrderExchanges);
                            break;
                        }
                    }
                }
                if (flag == 2 && orderInfo.getOcBReturnOrder() == null) {
                    //通过oid存在退换货单 但是数量或者sku不一致
                    if (IpBStandplatRefudStatusEnum.CLOSED.getVal().equals(status)) {
                        String message = "换货关闭,存在手动新增退换货类型的退换货单,标记为已转换";
                        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                                ipBStandplatRefund);
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    }
                    String message = "存在退换货单,但数量或者sku与平台申请不一致,转换失败";
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                            ipBStandplatRefund);
                    return new ProcessStepResult<>(StepStatus.FAILED, message);
                }

                //找到对应实际的换货订单
                List<OmsOrderExchangeRelation> exchangeOrderNew = new ArrayList<>();
                List<OmsOrderExchangeRelation> exchangeOrder = orderInfo.getExchangeRelation();
                if (CollectionUtils.isNotEmpty(exchangeOrder)) {
                    Long id = orderInfo.getOcBReturnOrder().getId();
                    for (OmsOrderExchangeRelation exchangeRelation : exchangeOrder) {
                        OmsOrderExchangeRelation orderExchangeRelation = new OmsOrderExchangeRelation();
                        OcBOrder ocBOrder = exchangeRelation.getOcBOrder();
                        List<OcBOrderItem> ocBOrderItems = exchangeRelation.getOcBOrderItems();
                        if (ocBOrder.getOrigReturnOrderId() != null && ocBOrder.getOrigReturnOrderId().equals(id)) {
                            orderExchangeRelation.setOcBOrder(ocBOrder);
                            orderExchangeRelation.setOcBOrderItems(ocBOrderItems);
                            exchangeOrderNew.add(orderExchangeRelation);
                        }else {
                            ocBOrderItems = ocBOrderItems.stream().filter(p -> p.getReturnOrderId() != null
                                    && p.getReturnOrderId().equals(id)).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                                orderExchangeRelation.setOcBOrder(ocBOrder);
                                orderExchangeRelation.setOcBOrderItems(ocBOrderItems);
                                exchangeOrderNew.add(orderExchangeRelation);
                            }
                        }
                    }
                } else {
                    List<Long> ids = ES4Order.getIdsByOrigReturnOrderId(orderInfo.getOcBReturnOrder().getId());
                    if (CollectionUtils.isNotEmpty(ids)) {
                        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(ids);
                        if (CollectionUtils.isNotEmpty(ocBOrders)) {
                            ocBOrders = ocBOrders.stream().filter(p -> !(OmsOrderStatus.SYS_VOID.toInteger().equals(p.getOrderStatus())
                                    || OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus()))).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(ocBOrders)) {
                                for (OcBOrder ocBOrder : ocBOrders) {
                                    List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemListOccupy(ocBOrder.getId());
                                    OmsOrderExchangeRelation orderExchangeRelation = new OmsOrderExchangeRelation();
                                    orderExchangeRelation.setOcBOrder(ocBOrder);
                                    orderExchangeRelation.setOcBOrderItems(ocBOrderItems);
                                    exchangeOrderNew.add(orderExchangeRelation);
                                }
                            }
                        }
                    }
                }
                //如果未匹配到对应的实际换货订单  则将改对象置空
                orderInfo.setExchangeRelation(exchangeOrderNew);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
//            ipStandplatRefundService.updateExchangeIsTransError(ipBStandplatRefund, e.getMessage());
            log.error("{} 查找原单以及退换货单失败{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
