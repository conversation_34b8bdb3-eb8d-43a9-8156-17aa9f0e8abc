package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.ReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/3/2 9:52 上午
 * @Version 1.0
 */
@Step(order = 60, description = "拦截处理")
@Slf4j
@Component
public class Step060OrderGoodsAfterRefundOnly extends BaseTaobaoRefundProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoRefundRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoRefundRelation> startProcess(OmsTaobaoRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        log.info("Step060OrderGoodsAfterRefundOnly.startProcess.orderInfo {}", JSON.toJSONString(orderInfo));
        IpBTaobaoRefund ipBTaobaoRefund = orderInfo.getIpBTaobaoRefund();
        try {
            OcBOrder ocBOrder = null;
            List<Long> refundIds = new ArrayList<>();
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            //中间表退款状态
            String status = ipBTaobaoRefund.getStatus();
            String goodStatus = ipBTaobaoRefund.getGoodStatus();
            boolean isAgree = TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status)
                    || TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status);
            boolean closed = TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status)
                    || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(status);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("taoBaoRefundTrans.{}, status:{},goodStatus:{}"), ipBTaobaoRefund.getRefundId(), status, goodStatus);
            }
            int isSuccess = 0;
            List<OmsOrderRelation> omsOrderRelations = new ArrayList<>();
            if (TaobaoReturnOrderExt.GoodStatus.BUYER_NOT_RECEIVED.getCode().equals(goodStatus)) {
                if (isAgree) {
                    for (OmsOrderRelation orderRelation : omsOrderRelation) {
                        if (TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode().equals(orderRelation.getOrderMark())) {
                            ocBOrder = orderRelation.getOcBOrder();
                            List<OcBOrderDelivery> orderDeliveries = orderRelation.getOrderDeliveries();
                            if (CollectionUtils.isEmpty(orderDeliveries)) {
                                isSuccess++;
                                continue;
                            }
                            omsOrderRelations.add(orderRelation);
                        }
                    }
                    if (isSuccess == omsOrderRelation.size()) {
                        String remark = "发货后仅退款,未查询到发货信息,转换结束";
                        TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.AF_SEND_NOT_FOUND_SEND_INFO);
                        ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                                remark, ipBTaobaoRefund);
                        return new ProcessStepResult<>(StepStatus.FAILED, remark);
                    } else {
                        ocBOrder = omsOrderRelation.get(0).getOcBOrder();
                        //买家未收到货 todo 生成发货后买家未收到货(仅退款)
                        List<OcBOrderItem> orderItems = new ArrayList<>();
                        for (OmsOrderRelation orderRelation : omsOrderRelation) {
                            orderItems.addAll(orderRelation.getOcBOrderItems());
                        }
                        //根据退款单号查询退款
                        omsRefundOrderService.foundRefundSlipAfterRefundOnly(orderItems, ocBOrder, ipBTaobaoRefund, operateUser);
                        //执行赠品后发
                        omsReturnOrderService.giftsThenSend(omsOrderRelations, orderInfo.getIsGiftOrderRelation(), orderInfo.getIntermediateTableRelation(), operateUser);
                        String remark = "生成发货后买家未收到货(仅退款)成功";
                        ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                remark, ipBTaobaoRefund);
//                        //判断是否生成退换货单
                        importRetundOrder(orderInfo, operateUser);
                        return new ProcessStepResult<>(StepStatus.FINISHED, remark);
                    }
                } else if (closed) {
                    //关闭退换货单及退款单(根据退款单号查询)
                    List<Long> existReturnOrder =
                            omsRefundOrderService.interceptOrderIsExist(ipBTaobaoRefund);
                    //取消已发货退款单
                    omsRefundOrderService.closedRefundSlip(ipBTaobaoRefund.getRefundId());
                    if (CollectionUtils.isNotEmpty(existReturnOrder)) {
                        omsRefundOrderService.refundOrderClose(existReturnOrder, omsOrderRelation, ipBTaobaoRefund, operateUser);
                    }
                    String remark = "退款关闭及卖家拒绝退款转换完成";
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, ipBTaobaoRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, remark);
                } else {
                    return new ProcessStepResult<>(StepStatus.SUCCESS);
                }
            } else if (TaobaoReturnOrderExt.GoodStatus.BUYER_RECEIVED.getCode().equals(goodStatus)) {
                String remark = null;
                ocBOrder = omsOrderRelation.get(0).getOcBOrder();
                if (TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode().equals(omsOrderRelation.get(0).getOrderMark())) {
                    if (isAgree) {
                        //判断申请金额是否与成交金额相等 相等则生成拦截类型的
                        //买家已收到货 todo 生成发货后退款单(仅退款)
                        List<OcBOrderItem> orderItems = new ArrayList<>();
                        for (OmsOrderRelation orderRelation : omsOrderRelation) {
                            orderItems.addAll(orderRelation.getOcBOrderItems());
                        }
                        //根据退款单号查询退款
                        omsRefundOrderService.foundRefundSlipAfterRefundOnly(orderItems, ocBOrder, ipBTaobaoRefund, operateUser);
//                        remark = "生成发货后退款单(仅退款)成功";
//                        //判断是否生成退换货单
//                        importRetundOrder(orderInfo,operateUser);
                    } else if (closed) {
                        //todo 关闭退款单
                        boolean flag = omsRefundOrderService.closedRefundSlip(ipBTaobaoRefund.getRefundId());
                        if (!flag) {
                            remark = "退款单取消失败";
                            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                                    remark, ipBTaobaoRefund);
                            return new ProcessStepResult<>(StepStatus.FAILED, remark);
                        }
                        remark = "退款单作废成功,标记已转换";
                    } else {
                        remark = "退单不满足状态,标记已转换";

                    }
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, ipBTaobaoRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, remark);

                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("退单转换异常,异常信息:{}", "订单处理异常", ipBTaobaoRefund.getRefundId()), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipTaobaoRefundService.updateRefundIsTransError(ipBTaobaoRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }

    public void importRetundOrder(OmsTaobaoRefundRelation orderInfo, User operateUser) {
        List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
        IpBTaobaoRefund ipBTaobaoRefund = orderInfo.getIpBTaobaoRefund();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("taoBaoRefundTrans.{},importRetundOrder"), ipBTaobaoRefund.getRefundId());
        }
        BigDecimal refundFee = ipBTaobaoRefund.getRefundFee() != null ? ipBTaobaoRefund.getRefundFee() : BigDecimal.ZERO;
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderJdReplacementOrderItem(String.valueOf(ipBTaobaoRefund.getTid()), ipBTaobaoRefund.getOid());
        log.info(" 零售发货单明细 {}", JSON.toJSONString(ocBOrderItems));
        BigDecimal amt = BigDecimal.ZERO;
        for (OcBOrderItem item : ocBOrderItems) {
            // 判断如果包含了未拆分的组合品 则不计算金额
            if (SkuType.NO_SPLIT_COMBINE == item.getProType()) {
                continue;
            }
            // 根据订单明细上的订单ID 来查询订单 如果订单已作废或者已取消 则过滤掉
            OcBOrder ocBOrder = ocBOrderMapper.selectOrderCancelAndCancel(item.getOcBOrderId());
            if (ocBOrder == null) {
                continue;
            }
            amt = amt.add(item.getRealAmt());
        }
        //退还金额等于交易总额时，生成退换货单
        boolean omsItemRealAmtEqRefundAmt = amt.equals(refundFee);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("taoBaoRefundTrans.{},amtEq:{}"), ipBTaobaoRefund.getRefundId(), omsItemRealAmtEqRefundAmt);
        }
        if (omsItemRealAmtEqRefundAmt) {
            ReturnOrderRelation relation = omsRefundOrderService.goodsAfterOrderIsExist(ipBTaobaoRefund);
            List<Long> existReturnOrder = relation.getIds();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("taoBaoRefundTrans.{}, existReturnOrder:{}"), ipBTaobaoRefund.getRefundId(), JSON.toJSONString(existReturnOrder));
            }

            // 检查退换货单的orig_order_id是否与isGiftOrderRelation中的订单ID完全一致
            boolean shouldGenerateReturnOrder = false;
            if (CollectionUtils.isNotEmpty(existReturnOrder)) {
                shouldGenerateReturnOrder = checkOrigOrderIdConsistency(existReturnOrder, orderInfo.getIsGiftOrderRelation(), ipBTaobaoRefund.getRefundId());
            }

            //不存在 或者 orig_order_id完全一致时，按子订单维度生成退换货单
            if (CollectionUtils.isEmpty(existReturnOrder) || relation.isFlag() || shouldGenerateReturnOrder) {
                List<OcBReturnOrderRelation> orderRelations = taobaoRefundOrderTransferUtil.
                        taobaoRefundOrderToReturnOid(omsOrderRelation, ipBTaobaoRefund, operateUser, relation.getQty(), true);
                if (CollectionUtils.isNotEmpty(orderRelations)) {
                    List<Long> refundIds = omsReturnOrderService.insertOmsReturnOrderInfo(orderRelations, ipBTaobaoRefund, omsOrderRelation.size(), operateUser);
                    String remark = "生成退单成功,转换完成（仅退款）";
                    TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.DEFAULT);
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, ipBTaobaoRefund);
                } else {
                    String remark = "申请数量大于可退数量,转换结束";
                    TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.QTY_NOT_ENOUGH);
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, ipBTaobaoRefund);
                }
            }

        }
    }

    /**
     * 检查退换货单的orig_order_id是否与isGiftOrderRelation中的订单ID完全一致
     *
     * @param existReturnOrderIds 已存在的退换货单ID列表
     * @param isGiftOrderRelation 赠品订单关系列表
     * @param refundId 退款单号（用于日志）
     * @return true-完全一致，需要生成退换货单；false-不一致，不生成退换货单
     */
    private boolean checkOrigOrderIdConsistency(List<Long> existReturnOrderIds, List<OmsOrderRelation> isGiftOrderRelation, String refundId) {
        try {
            if (CollectionUtils.isEmpty(existReturnOrderIds)) {
                log.debug("退换货单ID列表为空，refundId={}", refundId);
                return false;
            }

            if (CollectionUtils.isEmpty(isGiftOrderRelation)) {
                log.debug("赠品订单关系列表为空，refundId={}", refundId);
                return false;
            }

            // 获取isGiftOrderRelation中的所有订单ID
            Set<Long> giftOrderIds = isGiftOrderRelation.stream()
                    .map(relation -> relation.getOcBOrder().getId())
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (giftOrderIds.isEmpty()) {
                log.debug("赠品订单关系中没有有效的订单ID，refundId={}", refundId);
                return false;
            }

            // 查询退换货单获取orig_order_id
            Set<Long> returnOrderOrigIds = new HashSet<>();
            for (Long returnOrderId : existReturnOrderIds) {
                try {
                    OcBReturnOrder returnOrder = ocBReturnOrderMapper.selectByid(returnOrderId);
                    if (returnOrder != null && returnOrder.getOrigOrderId() != null) {
                        returnOrderOrigIds.add(returnOrder.getOrigOrderId());
                    }
                } catch (Exception e) {
                    log.error("查询退换货单异常，returnOrderId={}，refundId={}", returnOrderId, refundId, e);
                }
            }

            if (returnOrderOrigIds.isEmpty()) {
                log.debug("退换货单中没有有效的orig_order_id，refundId={}", refundId);
                return false;
            }

            // 检查两个集合是否完全一致
            boolean isConsistent = giftOrderIds.equals(returnOrderOrigIds);

            if (log.isDebugEnabled()) {
                log.debug("订单ID一致性检查结果，refundId={}，赠品订单IDs={}，退换货单原订单IDs={}，是否一致={}",
                        refundId, JSON.toJSONString(giftOrderIds), JSON.toJSONString(returnOrderOrigIds), isConsistent);
            }

            if (isConsistent) {
                log.info("退换货单orig_order_id与赠品订单关系完全一致，将生成退换货单，refundId={}，订单IDs={}",
                        refundId, JSON.toJSONString(giftOrderIds));
            } else {
                log.info("退换货单orig_order_id与赠品订单关系不一致，不生成退换货单，refundId={}，赠品订单IDs={}，退换货单原订单IDs={}",
                        refundId, JSON.toJSONString(giftOrderIds), JSON.toJSONString(returnOrderOrigIds));
            }

            return isConsistent;

        } catch (Exception e) {
            log.error("检查订单ID一致性异常，refundId={}", refundId, e);
            return false;
        }
    }
}
